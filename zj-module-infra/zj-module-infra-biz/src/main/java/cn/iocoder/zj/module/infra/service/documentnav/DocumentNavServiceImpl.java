package cn.iocoder.zj.module.infra.service.documentnav;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.zj.module.infra.controller.admin.documentnav.vo.*;
import cn.iocoder.zj.module.infra.dal.dataobject.documentnav.DocumentNavDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.infra.convert.documentnav.DocumentNavConvert;
import cn.iocoder.zj.module.infra.dal.mysql.documentnav.DocumentNavMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.infra.enums.ErrorCodeConstants.*;

/**
 * 内部知识库 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DocumentNavServiceImpl implements DocumentNavService {

    @Resource
    private DocumentNavMapper documentNavMapper;


    @Override
    public Integer createDocumentNav(DocumentNavCreateReqVO createReqVO) {
        // 插入
        DocumentNavDO documentNav = DocumentNavConvert.INSTANCE.convert(createReqVO);
        documentNavMapper.insert(documentNav);
        // 返回
        return documentNav.getId();
    }

    @Override
    public void updateDocumentNav(DocumentNavUpdateReqVO updateReqVO) {
        // 校验存在
        validateDocumentNavExists(updateReqVO.getId());
        // 更新
        DocumentNavDO updateObj = DocumentNavConvert.INSTANCE.convert(updateReqVO);
        documentNavMapper.updateById(updateObj);
    }

    @Override
    public void deleteDocumentNav(Integer id) {
        // 校验存在
        validateDocumentNavExists(id);
        // 删除
        documentNavMapper.deleteById(id);
    }

    private void validateDocumentNavExists(Integer id) {
        if (documentNavMapper.selectById(id) == null) {
            throw exception(DOCUMENT_NAV_NOT_EXISTS);
        }
    }

    @Override
    public DocumentNavDO getDocumentNav(Integer id) {
        return documentNavMapper.selectById(id);
    }

    @Override
    public List<DocumentNavDO> getDocumentNavList(Collection<Integer> ids) {
        return documentNavMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<DocumentNavDO> getDocumentNavPage(DocumentNavPageReqVO pageReqVO) {
        return documentNavMapper.selectPage(pageReqVO);
    }

    @Override
    public List<DocumentNavDO> getDocumentNavList(DocumentNavExportReqVO exportReqVO) {
        return documentNavMapper.selectList(exportReqVO);
    }

    @Override
    public List<DocumentNavDO> getDocList() {
        return documentNavMapper.getDocList();
    }

}
