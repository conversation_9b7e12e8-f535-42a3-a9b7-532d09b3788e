package cn.iocoder.zj.module.infra.dal.mysql.knowledge;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.infra.dal.dataobject.knowledge.KnowledgeDO;
import cn.iocoder.zj.module.infra.dal.dataobject.knowledgefile.KnowledgeFileDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.infra.controller.admin.knowledge.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 租户与知识库关系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface KnowledgeMapper extends BaseMapperX<KnowledgeDO> {

    default PageResult<KnowledgeDO> selectPage(KnowledgePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<KnowledgeDO>()
                .likeIfPresent(KnowledgeDO::getKnowledgeName, reqVO.getKnowledgeName())
                .betweenIfPresent(KnowledgeDO::getCreateTime, reqVO.getCreateTime())
                .likeIfPresent(KnowledgeDO::getTenantName, reqVO.getTenantName())
                .eqIfPresent(KnowledgeDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(KnowledgeDO::getPlatformName, reqVO.getPlatformName())
                .orderByDesc(KnowledgeDO::getId));
    }

    default List<KnowledgeDO> selectList(KnowledgeExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<KnowledgeDO>()
                .likeIfPresent(KnowledgeDO::getKnowledgeName, reqVO.getKnowledgeName())
                .betweenIfPresent(KnowledgeDO::getCreateTime, reqVO.getCreateTime())
                .likeIfPresent(KnowledgeDO::getTenantName, reqVO.getTenantName())
                .eqIfPresent(KnowledgeDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(KnowledgeDO::getPlatformName, reqVO.getPlatformName())
                .orderByDesc(KnowledgeDO::getId));
    }




    List<KnowledgePlatformVO> platformKnowledge(@Param("tenantId") Long tenantId);

    List<KnowledgeFileDO> knowledgeFileList(@Param("list") List<Long> list);

    List<KnowledgeRespVO> platformKnowledgeResp(@Param("tenantId")Long tenantId);
}
