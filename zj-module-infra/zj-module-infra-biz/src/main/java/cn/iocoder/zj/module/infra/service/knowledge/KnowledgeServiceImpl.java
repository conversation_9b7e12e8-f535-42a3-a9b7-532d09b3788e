package cn.iocoder.zj.module.infra.service.knowledge;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.util.io.FileUtils;
import cn.iocoder.zj.framework.file.core.client.FileClient;
import cn.iocoder.zj.framework.file.core.utils.FileTypeUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.infra.dal.dataobject.file.FileDO;
import cn.iocoder.zj.module.infra.dal.dataobject.knowledgefile.KnowledgeFileDO;
import cn.iocoder.zj.module.infra.dal.mysql.knowledgefile.KnowledgeFileMapper;
import cn.iocoder.zj.module.infra.service.file.FileConfigService;
import lombok.SneakyThrows;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.zj.module.infra.controller.admin.knowledge.vo.*;
import cn.iocoder.zj.module.infra.dal.dataobject.knowledge.KnowledgeDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.infra.convert.knowledge.KnowledgeConvert;
import cn.iocoder.zj.module.infra.dal.mysql.knowledge.KnowledgeMapper;
import org.springframework.web.multipart.MultipartFile;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;


/**
 * 租户与知识库关系 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class KnowledgeServiceImpl implements KnowledgeService {

    @Resource
    private KnowledgeMapper knowledgeMapper;

    @Resource
    private KnowledgeFileMapper knowledgeFileMapper;

    @Resource
    private FileConfigService fileConfigService;

    @Override
    public Long createKnowledge(KnowledgeCreateReqVO createReqVO) {
        // 插入
        KnowledgeDO knowledge = KnowledgeConvert.INSTANCE.convert(createReqVO);
        knowledgeMapper.insert(knowledge);
        // 返回
        return knowledge.getId();
    }

    @Override
    public void updateKnowledge(KnowledgeUpdateReqVO updateReqVO) {
        // 校验存在
        validateKnowledgeExists(updateReqVO.getId());
        // 更新
        KnowledgeDO updateObj = KnowledgeConvert.INSTANCE.convert(updateReqVO);
        knowledgeMapper.updateById(updateObj);
    }

    @Override
    public void deleteKnowledge(Long id) {
        // 校验存在
        validateKnowledgeExists(id);
        // 删除
        knowledgeMapper.deleteById(id);
    }

    private void validateKnowledgeExists(Long id) {
        if (knowledgeMapper.selectById(id) == null) {

        }
    }

    @Override
    public KnowledgeDO getKnowledge(Long id) {
        return knowledgeMapper.selectById(id);
    }

    @Override
    public List<KnowledgeDO> getKnowledgeList(Collection<Long> ids) {
        return knowledgeMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<KnowledgeDO> getKnowledgePage(KnowledgePageReqVO pageReqVO) {
        return knowledgeMapper.selectPage(pageReqVO);
    }

    @Override
    public List<KnowledgeDO> getKnowledgeList(KnowledgeExportReqVO exportReqVO) {
        return knowledgeMapper.selectList(exportReqVO);
    }



    @Override
    @SneakyThrows
    @TenantIgnore
    public String createFile(KnowledgeUploadReqVO uploadReqVO){
        MultipartFile file = uploadReqVO.getFile();
        String name = file.getOriginalFilename();
        String path = uploadReqVO.getPath();
            byte[] content = IoUtil.readBytes(file.getInputStream());
        // 计算默认的 path 名
        String type = FileTypeUtils.getMineType(content, name);
        if (StrUtil.isEmpty(path)) {
            path = FileUtils.generatePath(content, name);
        }
        // 如果 name 为空，则使用 path 填充
        if (StrUtil.isEmpty(name)) {
            name = path;
        }
        //添加前缀
        path="file/"+path;
        // 上传到文件存储器
        FileClient client = fileConfigService.getMasterFileClient();
        Assert.notNull(client, "客户端(master) 不能为空");
        String url = client.upload(content, path, type);
        // 保存到数据库
        KnowledgeFileDO fileDO = new KnowledgeFileDO();
        fileDO.setConfigId(client.getId());
        fileDO.setName(name);
        fileDO.setPath(path);
        fileDO.setUrl(url);
        fileDO.setType(type);
        fileDO.setSize(content.length);
        fileDO.setKnowledgeId(uploadReqVO.getKnowledgeId());
        fileDO.setKnowledgeName(uploadReqVO.getKnowledgeName());
        fileDO.setOpenTime(new Date());
        knowledgeFileMapper.insert(fileDO);
        return url;
    }

    @Override
    public List<KnowledgePlatformVO> platformKnowledge(Long tenantId) {
        return knowledgeMapper.platformKnowledge(tenantId);
    }

    @Override
    @TenantIgnore
    public List<KnowledgeRespVO> knowledgeFileList(Long tenantId) {

        List<KnowledgeRespVO> platformVos = knowledgeMapper.platformKnowledgeResp(tenantId);
        ArrayList<Long> list = new ArrayList<>();
        List<KnowledgeFileDO> knowledgeFileDOS=new ArrayList<>();
        for (KnowledgeRespVO platformVo : platformVos) {
            list.add(platformVo.getKnowledgeId());
            platformVo.setFiles(knowledgeFileDOS);
        }

        List<KnowledgeFileDO> knowledgeFileDos=knowledgeMapper.knowledgeFileList(list);

        for (KnowledgeRespVO platformVo : platformVos) {
            for (KnowledgeFileDO knowledgeFileDo : knowledgeFileDos) {
                if (platformVo.getKnowledgeId().equals(knowledgeFileDo.getKnowledgeId())){
                    List<KnowledgeFileDO> files = platformVo.getFiles();
                    files.add(knowledgeFileDo);
                    platformVo.setFiles(files);
                }
            }
        }

        return platformVos;
    }


}
