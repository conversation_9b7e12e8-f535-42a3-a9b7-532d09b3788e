package cn.iocoder.zj.module.infra.controller.admin.documentnav.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 内部知识库分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DocumentNavPageReqVO extends PageParam {

    @Schema(description = "文档名称")
    private String title;

    @Schema(description = "url地址")
    private String url;

}
