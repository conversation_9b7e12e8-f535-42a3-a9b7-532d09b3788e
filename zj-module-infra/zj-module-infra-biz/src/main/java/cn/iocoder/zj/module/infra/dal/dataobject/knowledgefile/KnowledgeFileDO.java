package cn.iocoder.zj.module.infra.dal.dataobject.knowledgefile;

import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 知识库文件 DO
 *
 * <AUTHOR>
 */
@TableName("infra_knowledge_file")
@KeySequence("infra_knowledge_file_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KnowledgeFileDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 知识库id
     */
    private Long knowledgeId;
    /**
     * 知识库名称
     */
    private String knowledgeName;
    /**
     * 配置编号
     */
    private Long configId;
    /**
     * 文件名
     */
    private String name;
    /**
     * 文件路径
     */
    private String path;
    /**
     * 文件 URL
     */
    private String url;
    /**
     * 文件类型
     */
    private String type;
    /**
     * 文件大小
     */
    private Integer size;
    /**
     * 打开时间
     */
    private Date openTime;

}
