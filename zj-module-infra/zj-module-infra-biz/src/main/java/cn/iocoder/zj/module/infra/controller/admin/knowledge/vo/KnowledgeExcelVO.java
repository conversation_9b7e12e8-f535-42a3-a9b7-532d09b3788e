package cn.iocoder.zj.module.infra.controller.admin.knowledge.vo;

import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 租户与知识库关系 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class KnowledgeExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("知识库名称")
    private String knowledgeName;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private Date createTime;

    @ExcelProperty("租户名")
    private String tenantName;

    @ExcelProperty("平台id")
    private Long platformId;

    @ExcelProperty("平台名称")
    private String platformName;

}
