package cn.iocoder.zj.module.infra.controller.admin.knowledge.vo;

import groovy.transform.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import lombok.ToString;

@Schema(description = "管理后台 - 获取平台知识库列表 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class KnowledgePlatformVO {
    @Schema(description = "知识库id")
    private Long knowledgeId;

    @Schema(description = "知识库名称")
    private String knowledgeName;
}
