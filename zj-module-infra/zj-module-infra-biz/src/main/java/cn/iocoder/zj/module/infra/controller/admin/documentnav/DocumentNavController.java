package cn.iocoder.zj.module.infra.controller.admin.documentnav;

import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.infra.controller.admin.documentnav.vo.*;
import cn.iocoder.zj.module.infra.dal.dataobject.documentnav.DocumentNavDO;
import cn.iocoder.zj.module.infra.convert.documentnav.DocumentNavConvert;
import cn.iocoder.zj.module.infra.service.documentnav.DocumentNavService;

@Tag(name = "管理后台 - 内部知识库")
@RestController
@RequestMapping("/infra/document-nav")
@Validated
public class DocumentNavController {

    @Resource
    private DocumentNavService documentNavService;

    @PostMapping("/create")
    @Operation(summary = "创建内部知识库")
    @PreAuthorize("@ss.hasPermission('infra:document-nav:create')")
    public CommonResult<Integer> createDocumentNav(@Valid @RequestBody DocumentNavCreateReqVO createReqVO) {
        return success(documentNavService.createDocumentNav(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新内部知识库")
    @PreAuthorize("@ss.hasPermission('infra:document-nav:update')")
    public CommonResult<Boolean> updateDocumentNav(@Valid @RequestBody DocumentNavUpdateReqVO updateReqVO) {
        documentNavService.updateDocumentNav(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除内部知识库")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('infra:document-nav:delete')")
    public CommonResult<Boolean> deleteDocumentNav(@RequestParam("id") Integer id) {
        documentNavService.deleteDocumentNav(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得内部知识库")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('infra:document-nav:query')")
    public CommonResult<DocumentNavRespVO> getDocumentNav(@RequestParam("id") Integer id) {
        DocumentNavDO documentNav = documentNavService.getDocumentNav(id);
        return success(DocumentNavConvert.INSTANCE.convert(documentNav));
    }

    @GetMapping("/list")
    @Operation(summary = "获得内部知识库列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('infra:document-nav:query')")
    public CommonResult<List<DocumentNavRespVO>> getDocumentNavList(@RequestParam("ids") Collection<Integer> ids) {
        List<DocumentNavDO> list = documentNavService.getDocumentNavList(ids);
        return success(DocumentNavConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @PermitAll
    @Operation(summary = "获得内部知识库分页")
    @PreAuthorize("@ss.hasPermission('infra:document-nav:query')")
    public CommonResult<PageResult<DocumentNavRespVO>> getDocumentNavPage(@Valid DocumentNavPageReqVO pageVO) {
        PageResult<DocumentNavDO> pageResult = documentNavService.getDocumentNavPage(pageVO);
        return success(DocumentNavConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出内部知识库 Excel")
    @PreAuthorize("@ss.hasPermission('infra:document-nav:export')")
    @OperateLog(type = EXPORT)
    public void exportDocumentNavExcel(@Valid DocumentNavExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<DocumentNavDO> list = documentNavService.getDocumentNavList(exportReqVO);
        // 导出 Excel
        List<DocumentNavExcelVO> datas = DocumentNavConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "内部知识库.xls", "数据", DocumentNavExcelVO.class, datas);
    }

    @PermitAll
    @GetMapping("/getDocList")
    @Operation(summary = "获得内部知识库集合")
    public CommonResult<List<DocumentNavRespVO>> getDocList() {
        List<DocumentNavDO> list = documentNavService.getDocList();
        return success(DocumentNavConvert.INSTANCE.convertList(list));
    }

}
