package cn.iocoder.zj.module.infra.dal.mysql.documentnav;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.infra.dal.dataobject.documentnav.DocumentNavDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.infra.controller.admin.documentnav.vo.*;

/**
 * 内部知识库 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DocumentNavMapper extends BaseMapperX<DocumentNavDO> {

    default PageResult<DocumentNavDO> selectPage(DocumentNavPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DocumentNavDO>()
                .eqIfPresent(DocumentNavDO::getTitle, reqVO.getTitle())
                .eqIfPresent(DocumentNavDO::getUrl, reqVO.getUrl())
                .orderByDesc(DocumentNavDO::getId));
    }

    default List<DocumentNavDO> selectList(DocumentNavExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DocumentNavDO>()
                .eqIfPresent(DocumentNavDO::getTitle, reqVO.getTitle())
                .eqIfPresent(DocumentNavDO::getUrl, reqVO.getUrl())
                .orderByDesc(DocumentNavDO::getId));
    }

    default List<DocumentNavDO> getDocList() {
        return selectList(new LambdaQueryWrapperX<DocumentNavDO>());
    }
}
