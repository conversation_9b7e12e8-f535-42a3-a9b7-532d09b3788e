package cn.iocoder.zj.module.infra.controller.admin.knowledge;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.infra.controller.admin.knowledge.vo.*;
import cn.iocoder.zj.module.infra.dal.dataobject.knowledge.KnowledgeDO;
import cn.iocoder.zj.module.infra.convert.knowledge.KnowledgeConvert;
import cn.iocoder.zj.module.infra.service.knowledge.KnowledgeService;

@Tag(name = "管理后台 - 租户与知识库关系")
@RestController
@RequestMapping("/infra/knowledge")
@Validated
public class KnowledgeController {



    @Resource
    private KnowledgeService knowledgeService;


    @PostMapping("/upload")
    @Operation(summary = "上传文件")
    @OperateLog(logArgs = false) // 上传文件，没有记录操作日志的必要
    public CommonResult<String> uploadFile(KnowledgeUploadReqVO uploadReqVO){
        return success(knowledgeService.createFile(uploadReqVO));
    }



    @GetMapping("/platformKnowledge")
    @Operation(summary = "获得平台知识库列表")
    public CommonResult<List<KnowledgePlatformVO>> platformKnowledge(Long tenantId) {
        List<KnowledgePlatformVO> list = knowledgeService.platformKnowledge(tenantId);
        return success(list);
    }


    @GetMapping("/knowledgeFileList")
    @Operation(summary = "获得平台知识库列表")
    public CommonResult<List<KnowledgeRespVO>> knowledgeFileList(Long tenantId) {
        List<KnowledgeRespVO> list = knowledgeService.knowledgeFileList(tenantId);
        return success(list);
    }





//
//    @GetMapping("/list")
//    @Operation(summary = "获得租户与知识库关系列表")
//    @Parameter(name = "ids", description = "编号列表", required = true, example = "1,2")
//    @PreAuthorize("@ss.hasPermission('infra:knowledge:query')")
//    public CommonResult<List<KnowledgeRespVO>> getKnowledgeList(@RequestParam("ids") Collection<Long> ids) {
//        List<KnowledgeDO> list = knowledgeService.getKnowledgeList(ids);
//        return success(KnowledgeConvert.INSTANCE.convertList(list));
//    }


    @PostMapping("/create")
    @Operation(summary = "创建租户与知识库关系")
    @PreAuthorize("@ss.hasPermission('infra:knowledge:create')")
    public CommonResult<Long> createKnowledge(@Valid @RequestBody KnowledgeCreateReqVO createReqVO) {
        return success(knowledgeService.createKnowledge(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新租户与知识库关系")
    @PreAuthorize("@ss.hasPermission('infra:knowledge:update')")
    public CommonResult<Boolean> updateKnowledge(@Valid @RequestBody KnowledgeUpdateReqVO updateReqVO) {
        knowledgeService.updateKnowledge(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除租户与知识库关系")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('infra:knowledge:delete')")
    public CommonResult<Boolean> deleteKnowledge(@RequestParam("id") Long id) {
        knowledgeService.deleteKnowledge(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得租户与知识库关系")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('infra:knowledge:query')")
    public CommonResult<KnowledgeRespVO> getKnowledge(@RequestParam("id") Long id) {
        KnowledgeDO knowledge = knowledgeService.getKnowledge(id);
        return success(KnowledgeConvert.INSTANCE.convert(knowledge));
    }



    @GetMapping("/page")
    @Operation(summary = "获得租户与知识库关系分页")
    @PreAuthorize("@ss.hasPermission('infra:knowledge:query')")
    public CommonResult<PageResult<KnowledgeRespVO>> getKnowledgePage(@Valid KnowledgePageReqVO pageVO) {
        PageResult<KnowledgeDO> pageResult = knowledgeService.getKnowledgePage(pageVO);
        return success(KnowledgeConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出租户与知识库关系 Excel")
    @PreAuthorize("@ss.hasPermission('infra:knowledge:export')")
    @OperateLog(type = EXPORT)
    public void exportKnowledgeExcel(@Valid KnowledgeExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<KnowledgeDO> list = knowledgeService.getKnowledgeList(exportReqVO);
        // 导出 Excel
        List<KnowledgeExcelVO> datas = KnowledgeConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "租户与知识库关系.xls", "数据", KnowledgeExcelVO.class, datas);
    }

}
