package cn.iocoder.zj.module.infra.dal.dataobject.documentnav;

import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 内部知识库 DO
 *
 * <AUTHOR>
 */
@TableName("infra_document_nav")
@KeySequence("infra_document_nav_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentNavDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Integer id;
    /**
     * 文档名称
     */
    private String title;
    /**
     * url地址
     */
    private String url;

}
