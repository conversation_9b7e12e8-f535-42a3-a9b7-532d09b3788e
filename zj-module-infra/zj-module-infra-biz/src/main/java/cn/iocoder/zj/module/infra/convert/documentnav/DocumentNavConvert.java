package cn.iocoder.zj.module.infra.convert.documentnav;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.infra.controller.admin.documentnav.vo.*;
import cn.iocoder.zj.module.infra.dal.dataobject.documentnav.DocumentNavDO;

/**
 * 内部知识库 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DocumentNavConvert {

    DocumentNavConvert INSTANCE = Mappers.getMapper(DocumentNavConvert.class);

    DocumentNavDO convert(DocumentNavCreateReqVO bean);

    DocumentNavDO convert(DocumentNavUpdateReqVO bean);

    DocumentNavRespVO convert(DocumentNavDO bean);

    List<DocumentNavRespVO> convertList(List<DocumentNavDO> list);

    PageResult<DocumentNavRespVO> convertPage(PageResult<DocumentNavDO> page);

    List<DocumentNavExcelVO> convertList02(List<DocumentNavDO> list);

}
