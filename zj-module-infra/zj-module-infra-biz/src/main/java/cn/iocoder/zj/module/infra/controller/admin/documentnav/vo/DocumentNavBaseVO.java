package cn.iocoder.zj.module.infra.controller.admin.documentnav.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

/**
* 内部知识库 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class DocumentNavBaseVO {

    @Schema(description = "文档名称")
    private String title;

    @Schema(description = "url地址")
    private String url;

}
