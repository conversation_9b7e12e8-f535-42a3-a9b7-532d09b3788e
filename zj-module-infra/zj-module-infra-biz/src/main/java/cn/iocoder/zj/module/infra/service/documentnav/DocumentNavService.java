package cn.iocoder.zj.module.infra.service.documentnav;

import java.util.*;
import javax.validation.*;
import cn.iocoder.zj.module.infra.controller.admin.documentnav.vo.*;
import cn.iocoder.zj.module.infra.dal.dataobject.documentnav.DocumentNavDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 内部知识库 Service 接口
 *
 * <AUTHOR>
 */
public interface DocumentNavService {

    /**
     * 创建内部知识库
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createDocumentNav(@Valid DocumentNavCreateReqVO createReqVO);

    /**
     * 更新内部知识库
     *
     * @param updateReqVO 更新信息
     */
    void updateDocumentNav(@Valid DocumentNavUpdateReqVO updateReqVO);

    /**
     * 删除内部知识库
     *
     * @param id 编号
     */
    void deleteDocumentNav(Integer id);

    /**
     * 获得内部知识库
     *
     * @param id 编号
     * @return 内部知识库
     */
    DocumentNavDO getDocumentNav(Integer id);

    /**
     * 获得内部知识库列表
     *
     * @param ids 编号
     * @return 内部知识库列表
     */
    List<DocumentNavDO> getDocumentNavList(Collection<Integer> ids);

    /**
     * 获得内部知识库分页
     *
     * @param pageReqVO 分页查询
     * @return 内部知识库分页
     */
    PageResult<DocumentNavDO> getDocumentNavPage(DocumentNavPageReqVO pageReqVO);

    /**
     * 获得内部知识库列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 内部知识库列表
     */
    List<DocumentNavDO> getDocumentNavList(DocumentNavExportReqVO exportReqVO);

    List<DocumentNavDO> getDocList();
}
