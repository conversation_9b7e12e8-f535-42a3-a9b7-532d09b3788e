package cn.iocoder.zj.module.infra.service.file;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.pojo.MinioConfigDTO;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.util.io.FileUtils;
import cn.iocoder.zj.framework.common.util.object.BeanUtils;
import cn.iocoder.zj.framework.file.core.client.FileClient;
import cn.iocoder.zj.framework.file.core.utils.FileTypeUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.infra.controller.admin.file.vo.file.FilePageReqVO;
import cn.iocoder.zj.module.infra.dal.dataobject.file.FileDO;
import cn.iocoder.zj.module.infra.dal.mysql.file.FileMapper;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.GZIPOutputStream;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.infra.enums.ErrorCodeConstants.FILE_NOT_EXISTS;
import static org.apache.kafka.common.requests.DeleteAclsResponse.log;

/**
 * 文件 Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class FileServiceImpl implements FileService {

    @Resource
    private FileConfigService fileConfigService;

    @Resource
    private FileMapper fileMapper;

    @Override
    public PageResult<FileDO> getFilePage(FilePageReqVO pageReqVO) {
        return fileMapper.selectPage(pageReqVO);
    }

    @Override
    @SneakyThrows
    public String createFile(String name, String path, byte[] content) {
        // 计算默认的 path 名
        String type = FileTypeUtils.getMineType(content, name);
        if (StrUtil.isEmpty(path)) {
            path = FileUtils.generatePath(content, name);
        }
        // 如果 name 为空，则使用 path 填充
        if (StrUtil.isEmpty(name)) {
            name = path;
        }

        // 上传到文件存储器
        FileClient client = fileConfigService.getMasterFileClient();
        Assert.notNull(client, "客户端(master) 不能为空");
        String url = client.upload(content, path, type);

        // 保存到数据库
        FileDO file = new FileDO();
        file.setConfigId(client.getId());
        file.setName(name);
        file.setPath(path);
        file.setUrl(url);
        file.setType(type);
        file.setSize(content.length);
        fileMapper.insert(file);
        return url;
    }

    @Override
    public void deleteFile(Long id) throws Exception {
        // 校验存在
        FileDO file = validateFileExists(id);

        // 从文件存储器中删除
        FileClient client = fileConfigService.getFileClient(file.getConfigId());
        Assert.notNull(client, "客户端({}) 不能为空", file.getConfigId());
        client.delete(file.getPath());

        // 删除记录
        fileMapper.deleteById(id);
    }

    private FileDO validateFileExists(Long id) {
        FileDO fileDO = fileMapper.selectById(id);
        if (fileDO == null) {
            throw exception(FILE_NOT_EXISTS);
        }
        return fileDO;
    }

    @Override
    public byte[] getFileContent(Long configId, String path) throws Exception {
        FileClient client = fileConfigService.getFileClient(configId);
        Assert.notNull(client, "客户端({}) 不能为空", configId);
        return client.getContent(path);
    }


    @Override
    @SneakyThrows
    public String createFileUrl(String name, String path, byte[] content){
        // 计算默认的 path 名
        String type = FileTypeUtils.getMineType(content, name);
        if (StrUtil.isEmpty(path)) {
            path = FileUtils.generatePath(content, name);
        }
        // 如果 name 为空，则使用 path 填充
        if (StrUtil.isEmpty(name)) {
            name = path;
        }

        // 上传到文件存储器
        FileClient client = fileConfigService.getMasterFileClient();
        Assert.notNull(client, "客户端(master) 不能为空");
        String url = client.uploadNotUrl(content, path, type);

        // 保存到数据库
        FileDO file = new FileDO();
        file.setConfigId(client.getId());
        file.setName(name);
        file.setPath(path);
        file.setUrl(url);
        file.setType(type);
        file.setSize(content.length);
        fileMapper.insert(file);
        return url;
    }

    @SneakyThrows
    @Override
    public byte[] ReaderObjects(String licenseUrl) {
        FileClient client = fileConfigService.getMasterFileClient();
        Assert.notNull(client, "客户端({}) 不能为空", client);
        byte[] originalContent = client.getContent(licenseUrl);
        // 如果内容较大，进行压缩处理
        if (originalContent != null && originalContent.length > 1024 * 1024) {
            return compressContent(originalContent);
        }
        return originalContent;
    }

    @Override
    public MinioConfigDTO getMinioConfig() {
        FileClient client = fileConfigService.getMasterFileClient();
        Assert.notNull(client, "客户端({}) 不能为空", client);
        return BeanUtils.toBean(client.getConfig(), MinioConfigDTO.class);
    }

    @SneakyThrows
    @TenantIgnore
    public Map<String, String> createContractFile(MultipartFile file, String path) {
        String name = file.getOriginalFilename();
        byte[] content = IoUtil.readBytes(file.getInputStream());
        // 计算默认的 path 名
        String type = FileTypeUtils.getMineType(content, name);
        if (StrUtil.isEmpty(path)) {
            path = FileUtils.generatePath(content, name);
        }
        // 如果 name 为空，则使用 path 填充
        if (StrUtil.isEmpty(name)) {
            name = path;
        }
        //添加前缀
        path="file/"+path;
        // 上传到文件存储器
        FileClient client = fileConfigService.getMasterFileClient();
        Assert.notNull(client, "客户端(master) 不能为空");
        String url = client.upload(content, path, type);
        HashMap<String, String> map = new HashMap<>();
        map.put("url",url);
        map.put("path",path);
        map.put("name",name);
        return map;
    }


    /**
     * 压缩内容
     *
     * @param content 原始内容
     * @return 压缩后的内容
     */
    private byte[] compressContent(byte[] content) {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             GZIPOutputStream gzipOut = new GZIPOutputStream(baos)) {

            gzipOut.write(content);
            gzipOut.finish();

            byte[] compressedData = baos.toByteArray();
            log.info("[compressContent][压缩前大小:{}KB, 压缩后大小:{}KB, 压缩率:{}%]",
                    content.length / 1024,
                    compressedData.length / 1024,
                    (1 - (double) compressedData.length / content.length) * 100);

            return compressedData;
        } catch (IOException e) {
            log.error("[compressContent][压缩内容异常]", e);
            // 压缩失败时返回原始内容
            return content;
        }
    }
}
