package cn.iocoder.zj.module.infra.controller.admin.knowledge.vo;

import cn.iocoder.zj.module.infra.dal.dataobject.knowledge.KnowledgeDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;

@Schema(description =  "知识库管理 - 上传文件 Request VO")
@Data
public class KnowledgeUploadReqVO{

    @Schema(description = "文件附件", required = true)
    @NotNull(message = "文件附件不能为空")
    private MultipartFile file;

    @Schema(description = "文件附件", example = "zjyuanma.png")
    private String path;


    @Schema(description = "知识库id", required = true)
    @NotNull(message = "请选择知识库")
    private Long knowledgeId;

    @Schema(description = "知识库名称", required = true)
    @NotNull(message = "请选择知识库")
    private String knowledgeName;


}
