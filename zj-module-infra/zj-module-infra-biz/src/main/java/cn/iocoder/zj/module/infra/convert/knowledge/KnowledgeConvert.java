package cn.iocoder.zj.module.infra.convert.knowledge;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.infra.controller.admin.knowledge.vo.*;
import cn.iocoder.zj.module.infra.dal.dataobject.knowledge.KnowledgeDO;

/**
 * 租户与知识库关系 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface KnowledgeConvert {

    KnowledgeConvert INSTANCE = Mappers.getMapper(KnowledgeConvert.class);

    KnowledgeDO convert(KnowledgeCreateReqVO bean);

    KnowledgeDO convert(KnowledgeUpdateReqVO bean);

    KnowledgeRespVO convert(KnowledgeDO bean);

    List<KnowledgeRespVO> convertList(List<KnowledgeDO> list);

    PageResult<KnowledgeRespVO> convertPage(PageResult<KnowledgeDO> page);

    List<KnowledgeExcelVO> convertList02(List<KnowledgeDO> list);

}
