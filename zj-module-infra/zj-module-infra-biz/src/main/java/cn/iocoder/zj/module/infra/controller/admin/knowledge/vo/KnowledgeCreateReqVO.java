package cn.iocoder.zj.module.infra.controller.admin.knowledge.vo;

import cn.iocoder.zj.module.infra.dal.dataobject.knowledgefile.KnowledgeFileDO;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 租户与知识库关系创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class KnowledgeCreateReqVO extends KnowledgeBaseVO {


}
