package cn.iocoder.zj.module.infra.controller.admin.knowledge.vo;

import cn.iocoder.zj.module.infra.dal.dataobject.knowledgefile.KnowledgeFileDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;
import java.util.List;

@Schema(description = "管理后台 - 租户与知识库关系 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class KnowledgeRespVO extends KnowledgeBaseVO {

    private Long tenantId;
    private Long knowledgeId;
    private List<KnowledgeFileDO> files;

}
