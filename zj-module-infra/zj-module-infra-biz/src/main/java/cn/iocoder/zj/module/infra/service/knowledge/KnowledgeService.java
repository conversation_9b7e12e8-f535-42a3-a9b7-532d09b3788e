package cn.iocoder.zj.module.infra.service.knowledge;

import java.util.*;
import javax.validation.*;

import cn.iocoder.zj.module.infra.controller.admin.knowledge.vo.*;
import cn.iocoder.zj.module.infra.dal.dataobject.knowledge.KnowledgeDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 租户与知识库关系 Service 接口
 *
 * <AUTHOR>
 */
public interface KnowledgeService {

    /**
     * 创建租户与知识库关系
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createKnowledge(@Valid KnowledgeCreateReqVO createReqVO);

    /**
     * 更新租户与知识库关系
     *
     * @param updateReqVO 更新信息
     */
    void updateKnowledge(@Valid KnowledgeUpdateReqVO updateReqVO);

    /**
     * 删除租户与知识库关系
     *
     * @param id 编号
     */
    void deleteKnowledge(Long id);

    /**
     * 获得租户与知识库关系
     *
     * @param id 编号
     * @return 租户与知识库关系
     */
    KnowledgeDO getKnowledge(Long id);

    /**
     * 获得租户与知识库关系列表
     *
     * @param ids 编号
     * @return 租户与知识库关系列表
     */
    List<KnowledgeDO> getKnowledgeList(Collection<Long> ids);

    /**
     * 获得租户与知识库关系分页
     *
     * @param pageReqVO 分页查询
     * @return 租户与知识库关系分页
     */
    PageResult<KnowledgeDO> getKnowledgePage(KnowledgePageReqVO pageReqVO);

    /**
     * 获得租户与知识库关系列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 租户与知识库关系列表
     */
    List<KnowledgeDO> getKnowledgeList(KnowledgeExportReqVO exportReqVO);


    String createFile(KnowledgeUploadReqVO uploadReqVO);


    List<KnowledgePlatformVO> platformKnowledge(Long tenantId);

    List<KnowledgeRespVO> knowledgeFileList(Long tenantId);
}
