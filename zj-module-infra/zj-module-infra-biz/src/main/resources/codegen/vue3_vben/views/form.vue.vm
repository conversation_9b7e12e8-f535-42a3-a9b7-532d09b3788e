<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="isUpdate ? '编辑' : '新增'" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts" setup name="${table.className}Modal">
  import { ref, unref } from 'vue'
  import { BasicModal, useModalInner } from '@/components/Modal'
  import { BasicForm, useForm } from '@/components/Form'
  import { formSchema } from './${classNameVar}.data'
  import { create${simpleClassName}, get${simpleClassName}, update${simpleClassName} } from '@/api/${table.moduleName}/${classNameVar}'

  const emit = defineEmits(['success', 'register'])
  const isUpdate = ref(true)

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 120,
    baseColProps: { span: 24 },
    schemas: formSchema,
    showActionButtonGroup: false,
    actionColOptions: { span: 23 }
  })

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields()
    setModalProps({ confirmLoading: false })
    isUpdate.value = !!data?.isUpdate
    if (unref(isUpdate)) {
      const res = await get${simpleClassName}(data.record.id)
      setFieldsValue({ ...res })
    }
  })

  async function handleSubmit() {
    try {
      const values = await validate()
      setModalProps({ confirmLoading: true })
      if (unref(isUpdate)) {
        await update${simpleClassName}(values)
      } else {
        await create${simpleClassName}(values)
      }
      closeModal()
      emit('success')
    } finally {
      setModalProps({ confirmLoading: false })
    }
  }
</script>
