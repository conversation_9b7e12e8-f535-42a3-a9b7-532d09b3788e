<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.infra.dal.mysql.knowledge.KnowledgeMapper">


    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id="getPlantIds" resultType="cn.iocoder.zj.module.infra.dal.dataobject.knowledgefile.KnowledgeFileDO">
        SELECT
            *
        FROM
            infra_knowledge_file
        WHERE
                knowledge_id IN ( SELECT id FROM infra_knowledge WHERE platform_id IN ( SELECT platform_id FROM system_platform_tenant WHERE tenant_id = 1 AND deleted = 0 ) AND deleted = 0 )
          AND deleted =0
    </select>
    <select id="getKnowledges" resultType="cn.iocoder.zj.module.infra.controller.admin.knowledge.vo.KnowledgeRespVO">
        SELECT
            *
        FROM
            infra_knowledge
        WHERE
                platform_id IN ( SELECT platform_id FROM system_platform_tenant WHERE tenant_id = 1 AND deleted = 0 )
          AND deleted = 0
    </select>




    <select id="platformKnowledge"  resultType="cn.iocoder.zj.module.infra.controller.admin.knowledge.vo.KnowledgePlatformVO">
        SELECT
            id as knowledgeId,
            knowledge_name as knowledgeName
        FROM
            infra_knowledge
        WHERE
                platform_id IN ( SELECT id FROM system_platform_tenant WHERE tenant_id = #{tenantId} AND deleted = 0 )
          AND deleted =0
    </select>

    <select id="platformKnowledgeResp" resultType="cn.iocoder.zj.module.infra.controller.admin.knowledge.vo.KnowledgeRespVO">
        SELECT
            id as knowledgeId,
            knowledge_name as knowledgeName,
            tenant_id as tenantId,
            tenant_name  as tenantName,
            platform_id as  platformId,
            platform_name as platformName
        FROM
            infra_knowledge
        WHERE
                (platform_id IN ( SELECT id FROM system_platform_tenant WHERE tenant_id = #{tenantId} AND deleted = 0 ) or id=1)
          AND deleted =0
    </select>

    <select id="knowledgeFileList" resultType="cn.iocoder.zj.module.infra.dal.dataobject.knowledgefile.KnowledgeFileDO">
        SELECT
            *
        FROM
            infra_knowledge_file
        WHERE
              1=1
            and knowledge_id IN
            <foreach collection="list" open="(" separator="," close=")" item="list">
                #{list}
            </foreach>
    </select>

</mapper>
