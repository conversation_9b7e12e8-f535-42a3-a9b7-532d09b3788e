package cn.iocoder.zj.module.customer.controller.admin.contractinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 合同信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ContractInfoRespVO extends ContractInfoBaseVO {

    @Schema(description = "合同ID", required = true)
    private String contractId;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
