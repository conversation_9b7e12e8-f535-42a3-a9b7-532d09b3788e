package cn.iocoder.zj.module.customer.service.contractinfo;


import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.constants.FileTypeConstants;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.util.date.DateUtils;
import cn.iocoder.zj.framework.common.util.string.StringUtil;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.customer.controller.admin.contractinfo.vo.ContractInfoCreateReqVO;
import cn.iocoder.zj.module.customer.controller.admin.contractinfo.vo.ContractInfoExportReqVO;
import cn.iocoder.zj.module.customer.controller.admin.contractinfo.vo.ContractInfoPageReqVO;
import cn.iocoder.zj.module.customer.controller.admin.contractinfo.vo.ContractInfoUpdateReqVO;
import cn.iocoder.zj.module.customer.convert.contractinfo.ContractInfoConvert;
import cn.iocoder.zj.module.customer.dal.dataobject.contractinfo.ContractInfoDO;
import cn.iocoder.zj.module.customer.dal.dataobject.custinfo.CustInfoDO;
import cn.iocoder.zj.module.customer.dal.mysql.contractinfo.ContractInfoMapper;
import cn.iocoder.zj.module.customer.dal.mysql.custinfo.CustInfoMapper;
import cn.iocoder.zj.module.infra.api.file.FileApi;
import cn.iocoder.zj.module.om.api.dbfile.DbfileApi;
import cn.iocoder.zj.module.om.api.dbfile.dto.WorkOrderCreateReqDTO;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.text.SimpleDateFormat;
import java.util.*;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.customer.enums.ErrorCodeConstants.CONTRACT_INFO_NOT_EXISTS;

/**
 * 合同信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ContractInfoServiceImpl implements ContractInfoService {

    @Resource
    private ContractInfoMapper contractInfoMapper;
    @Resource
    private CustInfoMapper custInfoMapper;

    @Resource
    private DbfileApi dbfileApi;

    @Resource
    private FileApi fileApi;

    @Override
    public String createContractInfo(ContractInfoCreateReqVO createReqVO) {
        // 插入
        ContractInfoDO contractInfo = ContractInfoConvert.INSTANCE.convert(createReqVO);
        contractInfoMapper.insert(contractInfo);
        // 返回
        return contractInfo.getContractId();
    }

    @Override
    @SneakyThrows
    @TenantIgnore
    @DSTransactional
    public void updateContractInfo(ContractInfoUpdateReqVO updateReqVO) {
        ContractInfoDO updateObj = ContractInfoConvert.INSTANCE.convert(updateReqVO);
        validateContractInfoExists(updateReqVO.getContractId());
        ContractInfoDO contractInfoDO = contractInfoMapper.selectByContractId(updateReqVO.getContractId());
        //新添加实施人则新建工单
        if (contractInfoDO.getStatus() == 1 && StrUtil.isEmpty(contractInfoDO.getEnforcerId()) && StrUtil.isNotEmpty(updateObj.getEnforcerId())) {
            WorkOrderCreateReqDTO workOrderCreateReqDTO = new WorkOrderCreateReqDTO();
            workOrderCreateReqDTO.setName("合同" + contractInfoDO.getProjectName() + "审核通过待交付");
            workOrderCreateReqDTO.setDescription("实施人员线下完成实施工作后点击完成");
            workOrderCreateReqDTO.setEnforcerId(updateObj.getEnforcerId());
            workOrderCreateReqDTO.setEnforcerName(updateObj.getEnforcerName());
            workOrderCreateReqDTO.setRatifyStatus("consent-unresolved");
            workOrderCreateReqDTO.setCreateType("hand");
            workOrderCreateReqDTO.setTypeCode("contract");
            workOrderCreateReqDTO.setTypeName("合同类");
            workOrderCreateReqDTO.setContractId(updateReqVO.getContractId());
            dbfileApi.addWorkOrder(workOrderCreateReqDTO);
        }
        //实施人更改则工单转移
        if (contractInfoDO.getStatus() == 1 && StrUtil.isNotEmpty(contractInfoDO.getEnforcerId())
                && StrUtil.isNotEmpty(updateObj.getEnforcerId()) && !contractInfoDO.getEnforcerId().equals(updateObj.getEnforcerId())) {
            dbfileApi.transferWorkOrder(updateObj.getContractId(), updateObj.getEnforcerId(), updateObj.getEnforcerName());
        }
        contractInfoMapper.updateById(updateObj);
    }

    @Override
    public void deleteContractInfo(String id) {
        // 校验存在
        validateContractInfoExists(id);
        // 删除
        contractInfoMapper.deleteById(id);
    }

    private void validateContractInfoExists(String id) {
        if (contractInfoMapper.selectByContractId(id) == null) {
            throw exception(CONTRACT_INFO_NOT_EXISTS);
        }
    }

    @Override
    public ContractInfoDO getContractInfo(String id) {
        return contractInfoMapper.selectByContractId(id);
    }

    @Override
    public List<ContractInfoDO> getContractInfoList(Collection<String> ids) {
        return contractInfoMapper.selectBatchIds(ids);
    }

    @Override
    @TenantIgnore
    public PageResult<ContractInfoDO> getContractInfoPage(ContractInfoPageReqVO pageReqVO) {
        PageResult<ContractInfoDO> contractInfo = contractInfoMapper.selectPage(pageReqVO);
        List<ContractInfoDO> contractInfoDOList = contractInfo.getList();
        for (ContractInfoDO contractInfoDO : contractInfoDOList) {
            long endDiff = DateUtils.diff(DateUtils.toDate(contractInfoDO.getServiceEnd()), new Date());
            if (endDiff < 0) {
                contractInfoDO.setStatus(3);
            }
        }
        contractInfo.setList(contractInfoDOList);
        return contractInfo;
    }

    @Override
    public List<ContractInfoDO> getContractInfoList(ContractInfoExportReqVO exportReqVO) {
        return contractInfoMapper.selectList(exportReqVO);
    }

    @Override
    public void revokeContractInfoByCrm(String id) {
        // 校验存在
        validateContractInfoExists(id);
        // 删除
        contractInfoMapper.revokeByContractId(id);
    }

    @Override
    public List<String> getAuditContractIds() {
        return contractInfoMapper.getAuditContractIds();
    }

    @Override
    public void updateStatusByContractIds(List<String> contractIds) {
        //修改合同状态
        contractInfoMapper.updateStatusByContractIds(contractIds);
        //若存在实施人员则自动创建工单
        /*List<ContractInfoDO> contractInfoDOList = contractInfoMapper.selectList(ContractInfoDO::getContractId,contractIds);
        for (ContractInfoDO contractInfoDO:contractInfoDOList){
            if (StrUtil.isNotEmpty(contractInfoDO.getEnforcerId())){
                WorkOrderCreateReqDTO workOrderCreateReqDTO=new WorkOrderCreateReqDTO();
                workOrderCreateReqDTO.setName("合同"+contractInfoDO.getProjectName()+"审核通过待交付");
                workOrderCreateReqDTO.setDescription("实施人员线下完成实施工作后点击完成");
                workOrderCreateReqDTO.setEnforcerId(contractInfoDO.getEnforcerId());
                workOrderCreateReqDTO.setEnforcerName(contractInfoDO.getEnforcerName());
                workOrderCreateReqDTO.setRatifyStatus("consent-unresolved");
                workOrderCreateReqDTO.setCreateType("hand");
                workOrderCreateReqDTO.setContractId(contractInfoDO.getContractId());
                dbfileApi.addWorkOrder(workOrderCreateReqDTO);
            }
        }*/
    }

    @Override
    @TenantIgnore
    public void updateStatus(@Valid String contractId) {
        //修改合同状态
        contractInfoMapper.updateStatus(contractId);
    }

    @Override
    @SneakyThrows
    public Map<String, String> createFile(MultipartFile file) {
        // 获取原始文件名和扩展名
        String originalFileName = file.getOriginalFilename();
        String fileExtension = "";
        if (StrUtil.isNotBlank(originalFileName) && originalFileName.contains(".")) {
            fileExtension = originalFileName.substring(originalFileName.lastIndexOf("."));
        }
        
        // 生成UUID文件名，保留扩展名
        String uuidFileName = UUID.randomUUID().toString() + fileExtension;
        
        String d = StringUtil.getSavePath(uuidFileName, FileTypeConstants.FILE_TYPE, "contract");
        String url = fileApi.createFileUrl(uuidFileName, d, IoUtil.readBytes(file.getInputStream()));
        
        log.info("文件上传是否进入==================>");
        Map<String, String> map = new HashMap<String, String>();
        try {
            map.put("filePath", url);
            map.put("fileUrl", url);
            map.put("fileName", uuidFileName);  // 存储UUID文件名
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        log.info("文件上传结束==================>");
        return map;
    }

    @Override
    @SneakyThrows
    public byte[] downloadFile(String path) {
        byte[] byteArray = fileApi.ReaderObjects(path);
        System.out.println("下载成功");
        return byteArray;
    }

    @Override
    public Long getContractInfoNum(String contractId) {
        return contractInfoMapper.selectContractInfoNum(contractId);
    }

    @Override
    @DSTransactional
    public String createContractInfoByCrm(@Valid ContractInfoCreateReqVO createReqVO) {
        //验证是否是原退回草稿的合同
        Long num = contractInfoMapper.selectDeletedContractInfoNum(createReqVO);
        ContractInfoDO contractInfo = ContractInfoConvert.INSTANCE.convert(createReqVO);
        if (num == 0) {
            // 插入
            contractInfoMapper.insert(contractInfo);
        } else {
            // 修改
            contractInfoMapper.updateByContractId(contractInfo);
        }
        //更新客户服务日期
        CustInfoDO updateObj = new CustInfoDO();
        updateObj.setCustId(contractInfo.getCustId());
        updateObj.setServiceStartDate(contractInfoMapper.selectContractInfoBySignDate(updateObj.getCustId()).getSignDate());
        updateObj.setServiceEndDate(contractInfoMapper.selectContractInfoByServiceEnd(updateObj.getCustId()).getServiceEnd());
        custInfoMapper.updateByCustId(updateObj);
        // 返回
        return contractInfo.getContractId();
    }

    @Override
    @TenantIgnore
    public String createTestContractInfo(Long tenantId, String tenantName, String expireTime, String custId) {
        ContractInfoCreateReqVO contractInfoCreateReqVO = new ContractInfoCreateReqVO();
        contractInfoCreateReqVO.setContractId(UuidUtils.generateUuid());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String contractNo = contractInfoMapper.getContractNo();
        int contractNoIndex = 0;
        if (StrUtil.isEmpty(contractNo)) {
            contractNoIndex = 1;
        } else {
            contractNoIndex = Integer.parseInt(contractNo) + 1;
        }
        contractInfoCreateReqVO.setContractNo("CSHT" + sdf.format(new Date()) + String.format("%03d", contractNoIndex));
        contractInfoCreateReqVO.setIsTest(0);
        contractInfoCreateReqVO.setProjectName("租户" + tenantName + "测试合同");
        contractInfoCreateReqVO.setCustId(custId);
        contractInfoCreateReqVO.setCustName("租户" + tenantName + "测试客户");
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
        contractInfoCreateReqVO.setSignDate(sdf1.format(new Date()));
        contractInfoCreateReqVO.setServiceEnd(expireTime);
        contractInfoCreateReqVO.setStatus(1);
        ContractInfoDO contractInfoDO = ContractInfoConvert.INSTANCE.convert(contractInfoCreateReqVO);
        contractInfoDO.setTenantId(tenantId);
        contractInfoMapper.insert(contractInfoDO);
        return contractInfoCreateReqVO.getContractId();
    }

    @Override
    @TenantIgnore
    public void formalContract(Long tenantId, String contractNo) {
        ContractInfoDO contractInfoDO = contractInfoMapper.selectOne(ContractInfoDO::getContractNo, contractNo);
        contractInfoDO.setTenantId(tenantId);
        contractInfoMapper.updateByContractNo(contractInfoDO);
    }

    @Override
    @TenantIgnore
    @DSTransactional
    public String transferContract(Long tenantId, String contractNo) {
        ContractInfoDO contractInfoDO = contractInfoMapper.selectOne(ContractInfoDO::getContractNo, contractNo);
        ContractInfoDO contractInfoTest = contractInfoMapper.selectOne(ContractInfoDO::getTenantId, tenantId, ContractInfoDO::getIsTest, 0);
        contractInfoDO.setTenantId(tenantId);
        contractInfoDO.setAssetNum(contractInfoTest.getAssetNum());
        contractInfoDO.setStatus(contractInfoTest.getStatus());
        contractInfoDO.setServiceTag(contractInfoTest.getServiceTag());
        contractInfoDO.setEnforcerId(contractInfoTest.getEnforcerId());
        contractInfoDO.setEnforcerName(contractInfoTest.getEnforcerName());
        contractInfoDO.setFileName(contractInfoTest.getFileName());
        contractInfoDO.setFilePath(contractInfoTest.getFilePath());
        contractInfoDO.setFileUrl(contractInfoTest.getFileUrl());
        contractInfoMapper.updateById(contractInfoDO);

        dbfileApi.updateWorkOrderByContId(contractInfoTest.getContractId(), contractInfoDO.getContractId(), contractInfoDO.getProjectName());

        contractInfoMapper.deleteById(contractInfoTest);
        String custId = contractInfoTest.getCustId();
        Map<String, Object> map = new HashMap();
        map.put("cust_id", custId);
        custInfoMapper.deleteByMap(map);
        return contractInfoDO.getContractId();
    }

    @Override
    @TenantIgnore
    @DSTransactional
    public String updateContract(Long tenantId, String contractNo) {
        ContractInfoDO contractInfoDO = contractInfoMapper.selectOne(ContractInfoDO::getContractNo, contractNo);
        ContractInfoDO oldContractInfo = contractInfoMapper.selectOne(ContractInfoDO::getTenantId, tenantId, ContractInfoDO::getIsTest, 1);
        contractInfoDO.setTenantId(tenantId);
        contractInfoDO.setAssetNum(oldContractInfo.getAssetNum());
        contractInfoDO.setStatus(oldContractInfo.getStatus());
        contractInfoDO.setServiceTag(oldContractInfo.getServiceTag());
        contractInfoDO.setEnforcerId(oldContractInfo.getEnforcerId());
        contractInfoDO.setEnforcerName(oldContractInfo.getEnforcerName());
        contractInfoDO.setFileName(oldContractInfo.getFileName());
        contractInfoDO.setFilePath(oldContractInfo.getFilePath());
        contractInfoDO.setFileUrl(oldContractInfo.getFileUrl());
        contractInfoMapper.updateById(contractInfoDO);

        dbfileApi.updateWorkOrderByContId(oldContractInfo.getContractId(), contractInfoDO.getContractId(), contractInfoDO.getProjectName());

        oldContractInfo.setTenantId(1L);
        oldContractInfo.setAssetNum("");
        oldContractInfo.setStatus(1);
        oldContractInfo.setServiceTag("");
        oldContractInfo.setEnforcerId("");
        oldContractInfo.setEnforcerName("");
        oldContractInfo.setFileName("");
        oldContractInfo.setFilePath("");
        oldContractInfo.setFileUrl("");
        contractInfoMapper.updateById(oldContractInfo);

        return contractInfoDO.getContractId();
    }

    @Override
    @TenantIgnore
    @DSTransactional
    public void tenantDelete(Long tenantId) {
        //删除对应工单
        List<ContractInfoDO> contractInfos = contractInfoMapper.selectList("tenant_id", tenantId);
        if (contractInfos.size() > 0) {
            for (int i = 0; i < contractInfos.size(); i++) {
                dbfileApi.deleteWorkOrder(contractInfos.get(i).getContractId());
            }
        }
        //删除对应合同、客户
        List<ContractInfoDO> testContractInfos = contractInfoMapper.getTestContracts(tenantId);
        if (testContractInfos.size() > 0) {
            for (int i = 0; i < testContractInfos.size(); i++) {
                custInfoMapper.deleteTestByCustId(testContractInfos.get(i).getCustId());
            }
            contractInfoMapper.deleteTestContract(tenantId);
        }
        contractInfoMapper.tenantDelete(tenantId);
    }

    @Override
    @TenantIgnore
    public List<Map> getContractNos() {
        List<Map> contractNos = contractInfoMapper.getContractNos();
        return contractNos;
    }

    @Override
    @TenantIgnore
    public String updateExpireTime(String expireTime, String contractNo) {
        ContractInfoDO contractInfoDO = contractInfoMapper.selectOne(ContractInfoDO::getContractNo, contractNo);
        contractInfoDO.setServiceEnd(expireTime);
        contractInfoMapper.updateById(contractInfoDO);

        return contractInfoDO.getCustId();
    }

    @Override
    public Long getContractList(Long tenantId) {

        return  contractInfoMapper.getContractList(tenantId);
    }

}
