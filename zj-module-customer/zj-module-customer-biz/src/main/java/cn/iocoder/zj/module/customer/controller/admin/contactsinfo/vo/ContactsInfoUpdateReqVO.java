package cn.iocoder.zj.module.customer.controller.admin.contactsinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 客户联系人信息更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ContactsInfoUpdateReqVO extends ContactsInfoBaseVO {

    @Schema(description = "联系人id", required = true)
    @NotNull(message = "联系人id不能为空")
    private String id;

}
