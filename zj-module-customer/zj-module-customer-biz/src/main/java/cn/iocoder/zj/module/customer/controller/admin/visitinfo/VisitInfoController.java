package cn.iocoder.zj.module.customer.controller.admin.visitinfo;

import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.customer.controller.admin.visitinfo.vo.*;
import cn.iocoder.zj.module.customer.dal.dataobject.visitinfo.VisitInfoDO;
import cn.iocoder.zj.module.customer.convert.visitinfo.VisitInfoConvert;
import cn.iocoder.zj.module.customer.service.visitinfo.VisitInfoService;

@Tag(name = "管理后台 - 客户回访信息")
@RestController
@RequestMapping("/customer/visit-info")
@Validated
public class VisitInfoController {

    @Resource
    private VisitInfoService visitInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建客户回访信息")
    @PreAuthorize("@ss.hasPermission('customer:visit-info:create')")
    @TenantIgnore
    @OperateLog(type = CREATE)
    public CommonResult<Integer> createVisitInfo(@Valid @RequestBody VisitInfoCreateReqVO createReqVO) {
        return success(visitInfoService.createVisitInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新客户回访信息")
    @PreAuthorize("@ss.hasPermission('customer:visit-info:update')")
    @TenantIgnore
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateVisitInfo(@Valid @RequestBody VisitInfoUpdateReqVO updateReqVO) {
        visitInfoService.updateVisitInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除客户回访信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('customer:visit-info:delete')")
    @TenantIgnore
    @OperateLog(type = DELETE)
    public CommonResult<Boolean> deleteVisitInfo(@RequestParam("id") Integer id) {
        visitInfoService.deleteVisitInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得客户回访信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('customer:visit-info:query')")
    @TenantIgnore
    public CommonResult<VisitInfoRespVO> getVisitInfo(@RequestParam("id") Integer id) {
        VisitInfoDO visitInfo = visitInfoService.getVisitInfo(id);
        return success(VisitInfoConvert.INSTANCE.convert(visitInfo));
    }

    @GetMapping("/list")
    @Operation(summary = "获得客户回访信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('customer:visit-info:query')")
    @TenantIgnore
    public CommonResult<List<VisitInfoRespVO>> getVisitInfoList(@RequestParam("ids") Collection<Integer> ids) {
        List<VisitInfoDO> list = visitInfoService.getVisitInfoList(ids);
        return success(VisitInfoConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得客户回访信息分页")
    @PreAuthorize("@ss.hasPermission('customer:visit-info:query')")
    @TenantIgnore
    public CommonResult<PageResult<VisitInfoRespVO>> getVisitInfoPage(@Valid VisitInfoPageReqVO pageVO) {
        PageResult<VisitInfoRespVO> pageResult = visitInfoService.getVisitInfoPage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出客户回访信息 Excel")
    @PreAuthorize("@ss.hasPermission('customer:visit-info:export')")
    @OperateLog(type = EXPORT)
    public void exportVisitInfoExcel(@Valid VisitInfoExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<VisitInfoDO> list = visitInfoService.getVisitInfoList(exportReqVO);
        // 导出 Excel
        List<VisitInfoExcelVO> datas = VisitInfoConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "客户回访信息.xls", "数据", VisitInfoExcelVO.class, datas);
    }

}
