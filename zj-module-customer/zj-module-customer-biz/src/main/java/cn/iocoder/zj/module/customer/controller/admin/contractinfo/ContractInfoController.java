package cn.iocoder.zj.module.customer.controller.admin.contractinfo;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.util.servlet.ServletUtils;
import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.customer.controller.admin.contractinfo.vo.*;
import cn.iocoder.zj.module.customer.convert.contractinfo.ContractInfoConvert;
import cn.iocoder.zj.module.customer.dal.dataobject.contractinfo.ContractInfoDO;
import cn.iocoder.zj.module.customer.service.contractinfo.ContractInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

@Tag(name = "管理后台 - 合同信息")
@RestController
@RequestMapping("/customer/contract-info")
@Validated
public class ContractInfoController {

    @Resource
    private ContractInfoService contractInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建合同信息")
    @OperateLog(type = CREATE)
    public CommonResult<String> createContractInfo(@Valid @RequestBody ContractInfoCreateReqVO createReqVO) {
        return success(contractInfoService.createContractInfo(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新合同信息")
    @PreAuthorize("@ss.hasPermission('customer:contract-info:update')")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateContractInfo(@Valid @RequestBody ContractInfoUpdateReqVO updateReqVO) {
        contractInfoService.updateContractInfo(updateReqVO);
        return success(true);
    }
    @CrossOrigin
    @PostMapping("/upload")
    @Operation(summary = "上传文件")
    @OperateLog(type = OTHER)
    public CommonResult<Map<String,String>> uploadFile(MultipartFile file,HttpServletResponse response) throws Exception {
        return success(contractInfoService.createFile(file));
    }


    @Operation(summary = "上传文件")
    @CrossOrigin(origins = "*", maxAge = 3600)
    @RequestMapping(value = "/upload1", method = RequestMethod.POST)
    public CommonResult<Map<String,String>> uploadFile1(MultipartFile file,HttpServletResponse response) throws Exception {
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "POST, GET, OPTIONS, DELETE");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");
        return success(contractInfoService.createFile(file));
    }


    @GetMapping("/download")
    @Operation(summary = "下载文件")
    @OperateLog(type = OTHER) // 上传文件，没有记录操作日志的必要
    public void downloadFile(HttpServletRequest request,
                                             HttpServletResponse response,
                                             String filePath) throws IOException {
        String path = StrUtil.subAfter(request.getRequestURI(), "/get/", false);
            byte[] content =contractInfoService.downloadFile(filePath);
            ServletUtils.writeAttachment(response, path, content);
    }


    @DeleteMapping("/delete")
    @Operation(summary = "删除合同信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('customer:contract-info:delete')")
    @OperateLog(type = DELETE)
    public CommonResult<Boolean> deleteContractInfo(@RequestParam("id") String id) {
        contractInfoService.deleteContractInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得合同信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @TenantIgnore
    public CommonResult<ContractInfoRespVO> getContractInfo(@RequestParam("id") String id) {
        ContractInfoDO contractInfo = contractInfoService.getContractInfo(id);
        return success(ContractInfoConvert.INSTANCE.convert(contractInfo));
    }

    @GetMapping("/list")
    @Operation(summary = "获得合同信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    public CommonResult<List<ContractInfoRespVO>> getContractInfoList(@RequestParam("ids") Collection<String> ids) {
        List<ContractInfoDO> list = contractInfoService.getContractInfoList(ids);
        return success(ContractInfoConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得合同信息分页")
    public CommonResult<PageResult<ContractInfoRespVO>> getContractInfoPage(@Valid ContractInfoPageReqVO pageVO) {
        PageResult<ContractInfoDO> pageResult = contractInfoService.getContractInfoPage(pageVO);
        return success(ContractInfoConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出合同信息 Excel")
    @OperateLog(type = EXPORT)
    public void exportContractInfoExcel(@Valid ContractInfoExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<ContractInfoDO> list = contractInfoService.getContractInfoList(exportReqVO);
        // 导出 Excel
        List<ContractInfoExcelVO> datas = ContractInfoConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "合同信息.xls", "数据", ContractInfoExcelVO.class, datas);
    }

    @DeleteMapping("/revoke/crm")
    @Operation(summary = "crm合同退回草稿")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('customer:contract-info:delete')")
    @OperateLog(type = DELETE)
    @TenantIgnore
    public CommonResult<Boolean> revokeContractInfoByCrm(@RequestParam("id") String id) {
        contractInfoService.revokeContractInfoByCrm(id);
        return success(true);
    }

    @GetMapping("/getAuditContractIds")
    @Operation(summary = "获得待审核合同id")
    @TenantIgnore
    public CommonResult<List<String> > getAuditContractIds() {
        List<String>  ids = contractInfoService.getAuditContractIds();
        return success(ids);
    }

    @PutMapping("/updateStatusByContractIds")
    @Operation(summary = "更新合同状态")
    @PreAuthorize("@ss.hasPermission('customer:contract-info:update')")
    @TenantIgnore
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateStatusByContractIds(@Valid @RequestBody List<String> contractIds) {
        contractInfoService.updateStatusByContractIds(contractIds);
        return success(true);
    }

    @GetMapping("/getNum")
    @Operation(summary = "获得合同数量")
    @Parameter(name = "contractId", description = "合同id", required = true)
    public CommonResult<Long> getContractInfoNum(@RequestParam("contractId") String contractId) {
        Long contractInfoNum = contractInfoService.getContractInfoNum(contractId);
        return success(contractInfoNum);
    }

    @PostMapping("/create/crm")
    @Operation(summary = "crm创建合同信息")
    @OperateLog(logArgs = false)
    @TenantIgnore
    public CommonResult<String> createContractInfoByCrm(@Valid @RequestBody ContractInfoCreateReqVO createReqVO) {
        return success(contractInfoService.createContractInfoByCrm(createReqVO));
    }

    @GetMapping("/getContractNos")
    @Operation(summary = "获得合同选项")
    public CommonResult<List<Map>> getContractNos() {
        List<Map> contractNos = contractInfoService.getContractNos();
        return success(contractNos);
    }
}
