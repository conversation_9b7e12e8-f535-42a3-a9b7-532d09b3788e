package cn.iocoder.zj.module.customer.controller.admin.visitinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 客户回访信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class VisitInfoExcelVO {

    @ExcelProperty("回访信息id")
    private Integer id;

    @ExcelProperty("回访客户id")
    private String custId;

    @ExcelProperty("回访客户名称")
    private String custName;

    @ExcelProperty("服务评价")
    private Double evaluateLeave;

    @ExcelProperty("回访详情")
    private String evaluateInfo;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
