package cn.iocoder.zj.module.customer.controller.admin.custinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 客户信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class CustInfoExcelVO {

    @ExcelProperty("客户ID")
    private String custId;

    @ExcelProperty("客户名称")
    private String custName;

    @ExcelProperty("客户所属行业：取字典表")
    private String industry;

    @ExcelProperty("客户省市区编码")
    private String provinceCityDistrictCode;

    @ExcelProperty("客户省市区")
    private String provinceCityDistrictName;

    @ExcelProperty("客户状态：0.未开始，1.服务中，2.已结束")
    private Integer status;

    @ExcelProperty("服务开始时间")
    private LocalDate serviceStartDate;

    @ExcelProperty("服务到期时间")
    private LocalDate serviceEndDate;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("地区id")
    private Long regionId;

}
