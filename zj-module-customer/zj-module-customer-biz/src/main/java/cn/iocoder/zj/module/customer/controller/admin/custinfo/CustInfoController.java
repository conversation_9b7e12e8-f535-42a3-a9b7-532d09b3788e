package cn.iocoder.zj.module.customer.controller.admin.custinfo;

import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.customer.controller.admin.custinfo.vo.*;
import cn.iocoder.zj.module.customer.dal.dataobject.custinfo.CustInfoDO;
import cn.iocoder.zj.module.customer.convert.custinfo.CustInfoConvert;
import cn.iocoder.zj.module.customer.service.custinfo.CustInfoService;

@Tag(name = "管理后台 - 客户信息")
@RestController
@RequestMapping("/customer/cust-info")
@Validated
public class CustInfoController {

    @Resource
    private CustInfoService custInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建客户信息")
    @PreAuthorize("@ss.hasPermission('customer:cust-info:create')")
    @OperateLog(type = CREATE)
    public CommonResult<String> createCustInfo(@Valid @RequestBody CustInfoCreateReqVO createReqVO) {
        return success(custInfoService.createCustInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新客户信息")
    @PreAuthorize("@ss.hasPermission('customer:cust-info:update')")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateCustInfo(@Valid @RequestBody CustInfoUpdateReqVO updateReqVO) {
        custInfoService.updateCustInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除客户信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('customer:cust-info:delete')")
    @OperateLog(type = DELETE)
    public CommonResult<Boolean> deleteCustInfo(@RequestParam("id") String id) {
        custInfoService.deleteCustInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得客户信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('customer:cust-info:query')")
    public CommonResult<CustInfoBaseVO> getCustInfo(@RequestParam("id") String id) {
        CustInfoBaseVO custInfo = custInfoService.getCustInfo(id);
        return success(custInfo);
    }

    @GetMapping("/list")
    @Operation(summary = "获得客户信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('customer:cust-info:query')")
    public CommonResult<List<CustInfoRespVO>> getCustInfoList(@RequestParam("ids") Collection<String> ids) {
        List<CustInfoDO> list = custInfoService.getCustInfoList(ids);
        return success(CustInfoConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得客户信息分页")
    @PreAuthorize("@ss.hasPermission('customer:cust-info:query')")
    public CommonResult<PageResult<CustInfoPageReqVO>> getCustInfoPage(@Valid CustInfoPageReqVO pageVO) {
        PageResult<CustInfoPageReqVO> pageResult = custInfoService.getCustInfoPage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出客户信息 Excel")
    @PreAuthorize("@ss.hasPermission('customer:cust-info:export')")
    @OperateLog(type = EXPORT)
    public void exportCustInfoExcel(@Valid CustInfoExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<CustInfoDO> list = custInfoService.getCustInfoList(exportReqVO);
        // 导出 Excel
        List<CustInfoExcelVO> datas = CustInfoConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "客户信息.xls", "数据", CustInfoExcelVO.class, datas);
    }

    @GetMapping("/getNum")
    @Operation(summary = "获得客户数量")
    @Parameter(name = "custId", description = "客户id", required = true)
    @PreAuthorize("@ss.hasPermission('customer:cust-info:query')")
    public CommonResult<Long> getCustInfoNum(@RequestParam("custId") String custId) {
        Long custInfoNum = custInfoService.getCustInfoNum(custId);
        return success(custInfoNum);
    }

    @GetMapping("/select")
    @Operation(summary = "获得客户信息作为选项")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('customer:cust-info:query')")
    @TenantIgnore
    public CommonResult<List<CustInfoRespVO>> getCustInfoeSlectList() {
        List<CustInfoDO> list = custInfoService.getCustInfoeSlectList();
        return success(CustInfoConvert.INSTANCE.convertList(list));
    }

}
