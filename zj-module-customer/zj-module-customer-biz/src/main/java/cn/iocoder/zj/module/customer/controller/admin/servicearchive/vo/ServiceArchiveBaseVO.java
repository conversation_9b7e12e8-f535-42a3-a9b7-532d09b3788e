package cn.iocoder.zj.module.customer.controller.admin.servicearchive.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 服务存档信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ServiceArchiveBaseVO {

    @Schema(description = "服务存档名称")
    private String archiveTitle;

    @Schema(description = "文件路径")
    private String filePath;

    @Schema(description = "文件大小")
    private String fileSize;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "文件url")
    private String fileUrl;

    @Schema(description = "关联合同id")
        private String contractId;

    @Schema(description = "备注")
    private String remarks;

    @Schema(description = "上传人")
    private String creatorName;


}
