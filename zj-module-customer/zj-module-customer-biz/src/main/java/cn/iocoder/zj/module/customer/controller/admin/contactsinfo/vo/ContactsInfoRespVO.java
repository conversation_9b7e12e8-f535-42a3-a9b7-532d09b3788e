package cn.iocoder.zj.module.customer.controller.admin.contactsinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 客户联系人信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ContactsInfoRespVO extends ContactsInfoBaseVO {

    @Schema(description = "联系人id", required = true)
    private String id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
