package cn.iocoder.zj.module.customer.service.contactsinfo;

import cn.hutool.core.util.StrUtil;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import cn.iocoder.zj.module.customer.controller.admin.contactsinfo.vo.*;
import cn.iocoder.zj.module.customer.dal.dataobject.contactsinfo.ContactsInfoDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.customer.convert.contactsinfo.ContactsInfoConvert;
import cn.iocoder.zj.module.customer.dal.mysql.contactsinfo.ContactsInfoMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.customer.enums.ErrorCodeConstants.*;

/**
 * 客户联系人信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ContactsInfoServiceImpl implements ContactsInfoService {

    @Resource
    private ContactsInfoMapper contactsInfoMapper;

    @Override
    public Integer createContactsInfo(ContactsInfoCreateReqVO createReqVO) {
        // 校验是否重复
        validateContactsInfoByNameAndTel(null,createReqVO.getCustId(),createReqVO.getContactsName(),createReqVO.getContactsTelephone());
        // 插入
        ContactsInfoDO contactsInfo = ContactsInfoConvert.INSTANCE.convert(createReqVO);
        contactsInfoMapper.insert(contactsInfo);
        // 返回
        return contactsInfo.getId();
    }

    @Override
    public void updateContactsInfo(ContactsInfoUpdateReqVO updateReqVO) {
        // 校验存在
        validateContactsInfoExists(updateReqVO.getId());
        // 校验是否重复
        validateContactsInfoByNameAndTel(updateReqVO.getId(),updateReqVO.getCustId(),updateReqVO.getContactsName(),updateReqVO.getContactsTelephone());
        // 更新
        ContactsInfoDO updateObj = ContactsInfoConvert.INSTANCE.convert(updateReqVO);
        contactsInfoMapper.updateById(updateObj);
    }

    @Override
    public void deleteContactsInfo(String id) {
        // 校验存在
        validateContactsInfoExists(id);
        // 删除
        contactsInfoMapper.deleteById(id);
    }

    private void validateContactsInfoExists(String id) {
        if (contactsInfoMapper.selectById(id) == null) {
            throw exception(CONTACTS_INFO_NOT_EXISTS);
        }
    }


    private void validateContactsInfoByNameAndTel(String id,String custId,String name,String phone) {
        List<ContactsInfoDO> contactsInfoDOS = contactsInfoMapper.selectByNameAndTel(custId,name,phone);
        if (contactsInfoDOS.size() > 0) {
            if (StrUtil.isEmpty(id)){
                throw exception(CONTACTS_INFO_EXISTS);
            }else {
                for (int i=0;i<contactsInfoDOS.size();i++){
                    if (!id.equals(contactsInfoDOS.get(i).getId().toString())){
                        throw exception(CONTACTS_INFO_EXISTS);
                    }
                }
            }
        }
    }

    @Override
    public ContactsInfoDO getContactsInfo(String id) {
        return contactsInfoMapper.selectById(id);
    }

    @Override
    public List<ContactsInfoDO> getContactsInfoList(Collection<String> ids) {
        return contactsInfoMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ContactsInfoDO> getContactsInfoPage(ContactsInfoPageReqVO pageReqVO) {
        return contactsInfoMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ContactsInfoDO> getContactsInfoList(ContactsInfoExportReqVO exportReqVO) {
        return contactsInfoMapper.selectList(exportReqVO);
    }

    @Override
    public Boolean createContactsInfoList(@Valid List<ContactsInfoCreateReqVO> createReqVOList) {
        // 删除原客户联系人
        contactsInfoMapper.deleteByCustId(createReqVOList.get(0).getCustId());
        // 插入
        List<ContactsInfoDO> contactsInfo = ContactsInfoConvert.INSTANCE.convertList2(createReqVOList);
        contactsInfoMapper.insertBatch(contactsInfo);
        return true;
    }

}
