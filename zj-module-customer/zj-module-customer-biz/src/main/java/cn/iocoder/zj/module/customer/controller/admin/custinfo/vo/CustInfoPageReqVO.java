package cn.iocoder.zj.module.customer.controller.admin.custinfo.vo;

import cn.iocoder.zj.module.customer.dal.dataobject.custinfo.CustInfoDO;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 客户信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CustInfoPageReqVO extends PageParam {

    @Schema(description = "客户ID")
    private String custId;

    @Schema(description = "客户名称")
    private String custName;

    @Schema(description = "客户所属行业：取字典表")
    private String industry;

    @Schema(description = "客户省市区编码")
    private String provinceCityDistrictCode;

    @Schema(description = "客户省市区")
    private String provinceCityDistrictName;

    @Schema(description = "客户状态：0.未开始，1.服务中，2.已结束")
    private Integer status;

    @Schema(description = "服务开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String serviceStartDate;

    @Schema(description = "服务到期时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String serviceEndDate;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "地区id")
    private Long regionId;

    @Schema(description = "剩余服务天数")
    private Integer surplusDayCount;

    @Schema(description = "客户联系人")
    private String contacts;

    @Schema(description = "客户联系人电话")
    private String contactsPhone;

    @Schema(description = "资产授权数量")
    private String assetNum;

}
