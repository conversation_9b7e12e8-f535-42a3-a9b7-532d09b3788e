package cn.iocoder.zj.module.customer.controller.admin.contactsinfo.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 客户联系人信息 Excel 导出 Request VO，参数和 ContactsInfoPageReqVO 是一致的")
@Data
public class ContactsInfoExportReqVO {

    @Schema(description = "联系人名称")
    private String contactsName;

    @Schema(description = "联系人手机号")
    private String contactsTelephone;

    @Schema(description = "所属客户ID")
    private String custId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "地区id")
    private Long regionId;

}
