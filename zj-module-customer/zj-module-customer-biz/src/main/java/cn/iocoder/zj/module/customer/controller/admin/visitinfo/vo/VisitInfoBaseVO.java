package cn.iocoder.zj.module.customer.controller.admin.visitinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 客户回访信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class VisitInfoBaseVO {

    @Schema(description = "回访客户id")
    private String custId;

    @Schema(description = "回访客户名称")
    private String custName;

    @Schema(description = "服务评价")
    private Double evaluateLeave;

    @Schema(description = "回访详情")
    private String evaluateInfo;

    @Schema(description = "回访人姓名")
    private String contractName;


    @Schema(description = "回访时间")
    private String updateTime;
}
