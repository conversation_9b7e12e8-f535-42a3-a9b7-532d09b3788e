package cn.iocoder.zj.module.customer.convert.custinfo;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.customer.api.custinfo.dto.CustinfoDTO;
import cn.iocoder.zj.module.customer.api.custinfo.dto.IndustryDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.customer.controller.admin.custinfo.vo.*;
import cn.iocoder.zj.module.customer.dal.dataobject.custinfo.CustInfoDO;

/**
 * 客户信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface CustInfoConvert {

    CustInfoConvert INSTANCE = Mappers.getMapper(CustInfoConvert.class);

    CustInfoDO convert(CustInfoCreateReqVO bean);

    CustInfoDO convert(CustInfoUpdateReqVO bean);

    CustInfoRespVO convert(CustInfoDO bean);

    List<CustInfoRespVO> convertList(List<CustInfoDO> list);

    PageResult<CustInfoRespVO> convertPage(PageResult<CustInfoDO> page);

    List<CustInfoExcelVO> convertList02(List<CustInfoDO> list);

}
