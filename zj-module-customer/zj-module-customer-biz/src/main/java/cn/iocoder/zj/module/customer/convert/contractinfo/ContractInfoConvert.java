package cn.iocoder.zj.module.customer.convert.contractinfo;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.customer.controller.admin.contractinfo.vo.*;
import cn.iocoder.zj.module.customer.dal.dataobject.contractinfo.ContractInfoDO;

/**
 * 合同信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ContractInfoConvert {

    ContractInfoConvert INSTANCE = Mappers.getMapper(ContractInfoConvert.class);

    ContractInfoDO convert(ContractInfoCreateReqVO bean);

    ContractInfoDO convert(ContractInfoUpdateReqVO bean);

    ContractInfoRespVO convert(ContractInfoDO bean);

    List<ContractInfoRespVO> convertList(List<ContractInfoDO> list);

    PageResult<ContractInfoRespVO> convertPage(PageResult<ContractInfoDO> page);

    List<ContractInfoExcelVO> convertList02(List<ContractInfoDO> list);

}
