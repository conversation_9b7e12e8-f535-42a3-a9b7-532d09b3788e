package cn.iocoder.zj.module.customer.controller.admin.servicearchive.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 服务存档信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ServiceArchiveExcelVO {

    @ExcelProperty("服务存档名称")
    private String archiveTitle;

    @ExcelProperty("文件路径")
    private String filePath;

    @ExcelProperty("文件大小")
    private String fileSize;

    @ExcelProperty("文件名称")
    private String fileName;

    @ExcelProperty("文件url")
    private String fileUrl;

    @ExcelProperty("关联合同id")
    private String contractId;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("服务存档id")
    private Integer id;

}
