package cn.iocoder.zj.module.customer.dal.dataobject.visitinfo;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 客户回访信息 DO
 *
 * <AUTHOR>
 */
@TableName("customer_visit_info")
@KeySequence("customer_visit_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VisitInfoDO extends BaseDO {

    /**
     * 回访信息id
     */
    @TableId
    private Integer id;
    /**
     * 回访客户id
     */
    private String custId;
    /**
     * 回访客户名称
     */
    private String custName;
    /**
     * 服务评价
     */
    private Double evaluateLeave;
    /**
     * 回访详情
     */
    private String evaluateInfo;

}
