package cn.iocoder.zj.module.customer.api.custInfo;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.customer.api.custinfo.CustInfoApi;
import cn.iocoder.zj.module.customer.api.custinfo.dto.CustinfoDTO;
import cn.iocoder.zj.module.customer.api.custinfo.dto.IndustryDTO;
import cn.iocoder.zj.module.customer.convert.custinfo.CustInfoConvert;
import cn.iocoder.zj.module.customer.service.custinfo.CustInfoService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.util.List;
import java.util.Map;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

/**
 * @ClassName : CustInfoApiImpl  //类名
 * @Description : 客户信息相关实现  //描述
 * <AUTHOR> lixiaob<PERSON><EMAIL> //作者
 * @Date: 2024/3/25  10:41
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class CustInfoApiImpl implements CustInfoApi {

    @Resource
    private CustInfoService custInfoService;

    @Override
    public CommonResult<List<Map>> getCustomersList() {
       List<Map> list =  custInfoService.getCustomersList();
        return  CommonResult.success(list);
    }

    @Override
    public CommonResult<List<Map>> customerIndustry() {
        List<Map> list =  custInfoService.customerIndustry();
        return CommonResult.success(list);
    }
}
