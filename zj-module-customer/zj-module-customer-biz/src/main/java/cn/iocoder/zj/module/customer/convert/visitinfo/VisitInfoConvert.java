package cn.iocoder.zj.module.customer.convert.visitinfo;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.customer.controller.admin.visitinfo.vo.*;
import cn.iocoder.zj.module.customer.dal.dataobject.visitinfo.VisitInfoDO;

/**
 * 客户回访信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface VisitInfoConvert {

    VisitInfoConvert INSTANCE = Mappers.getMapper(VisitInfoConvert.class);

    VisitInfoDO convert(VisitInfoCreateReqVO bean);

    VisitInfoDO convert(VisitInfoUpdateReqVO bean);

    VisitInfoRespVO convert(VisitInfoDO bean);

    List<VisitInfoRespVO> convertList(List<VisitInfoDO> list);

    PageResult<VisitInfoRespVO> convertPage(PageResult<VisitInfoDO> page);

    List<VisitInfoExcelVO> convertList02(List<VisitInfoDO> list);

}
