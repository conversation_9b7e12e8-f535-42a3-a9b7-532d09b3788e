package cn.iocoder.zj.module.customer.dal.mysql.contractinfo;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.customer.api.contractInfo.dto.ContractInfoDTO;
import cn.iocoder.zj.module.customer.dal.dataobject.contractinfo.ContractInfoDO;
import cn.iocoder.zj.module.customer.dal.dataobject.custinfo.CustInfoDO;
import jodd.util.StringUtil;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.customer.controller.admin.contractinfo.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 合同信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ContractInfoMapper extends BaseMapperX<ContractInfoDO> {

    default PageResult<ContractInfoDO> selectPage(ContractInfoPageReqVO reqVO) {
        LambdaQueryWrapperX<ContractInfoDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.like(StrUtil.isNotEmpty(reqVO.getProjectName()),ContractInfoDO::getProjectName, reqVO.getProjectName())
                .eq(StrUtil.isNotEmpty(reqVO.getEnforcerId()), ContractInfoDO::getEnforcerId, reqVO.getEnforcerId())
                .eq(StrUtil.isNotEmpty(reqVO.getCustId()),ContractInfoDO::getCustId,reqVO.getCustId());
        //标签
        wrapper.like(StrUtil.isNotEmpty(reqVO.getServiceTag()),ContractInfoDO::getServiceTag,reqVO.getServiceTag());

        if (reqVO.getStatus()!=null ){
            if (reqVO.getStatus()!=3 ) {
                wrapper.eq(ContractInfoDO::getStatus, reqVO.getStatus());
                wrapper.gt(ContractInfoDO::getServiceEnd,new Date());
            }else {
                wrapper.lt(ContractInfoDO::getServiceEnd,new Date());
            }
        }

        // 合同有效期筛选：查找与给定时间段有交集的合同
        if (StrUtil.isNotEmpty(reqVO.getSignDate()) && StrUtil.isNotEmpty(reqVO.getServiceEnd())) {
            wrapper.le(ContractInfoDO::getSignDate, reqVO.getServiceEnd())  // 合同开始日期 <= 给定结束日期
                   .ge(ContractInfoDO::getServiceEnd, reqVO.getSignDate()); // 合同结束日期 >= 给定开始日期
        }

        if (StringUtil.isNotEmpty(reqVO.getSortDirection())){
            if (reqVO.getSortDirection().equals("asc")){
                if (reqVO.getSortBy().equals("signDate")){
                    wrapper.orderByAsc(ContractInfoDO::getSignDate);
                }
                if (reqVO.getSortBy().equals("serviceEnd")){
                    wrapper.orderByAsc(ContractInfoDO::getServiceEnd);
                }
            }else if (reqVO.getSortDirection().equals("desc")){
                if (reqVO.getSortBy().equals("signDate")){
                    wrapper.orderByDesc(ContractInfoDO::getSignDate);
                }
                if (reqVO.getSortBy().equals("serviceEnd")){
                    wrapper.orderByDesc(ContractInfoDO::getServiceEnd);
                }
            }
        }else {
            wrapper.orderByDesc(ContractInfoDO::getSignDate);
        }
        return selectPage(reqVO, wrapper);
    }

    default List<ContractInfoDO> selectList(ContractInfoExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ContractInfoDO>()
                .eqIfPresent(ContractInfoDO::getContractNo, reqVO.getContractNo())
                .eqIfPresent(ContractInfoDO::getCustId, reqVO.getCustId())
                .likeIfPresent(ContractInfoDO::getCustName, reqVO.getCustName())
                .likeIfPresent(ContractInfoDO::getProjectName, reqVO.getProjectName())
                .eqIfPresent(ContractInfoDO::getStatus, reqVO.getStatus())
                .eqIfPresent(ContractInfoDO::getAssetNum, reqVO.getAssetNum())
                .eqIfPresent(ContractInfoDO::getServiceTag, reqVO.getServiceTag())
                .eqIfPresent(ContractInfoDO::getEnforcerId, reqVO.getEnforcerId())
                .likeIfPresent(ContractInfoDO::getEnforcerName, reqVO.getEnforcerName())
                .betweenIfPresent(ContractInfoDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(ContractInfoDO::getRegionId, reqVO.getRegionId())
                .eqIfPresent(ContractInfoDO::getFilePath, reqVO.getFilePath())
                .eqIfPresent(ContractInfoDO::getFileSize, reqVO.getFileSize())
                .likeIfPresent(ContractInfoDO::getFileName, reqVO.getFileName())
                .eqIfPresent(ContractInfoDO::getFileUrl, reqVO.getFileUrl())
                .orderByDesc(ContractInfoDO::getCreateTime));
    }

    List<ContractInfoVo> getInfoList(@Param("custId") String custId);

    default ContractInfoDO selectContractInfoBySignDate(String custId) {
        return selectOne(new LambdaQueryWrapperX<ContractInfoDO>()
                .eq(ContractInfoDO::getCustId, custId)
                .orderByAsc(ContractInfoDO::getSignDate)
                .last("limit 1"));
    }

    default ContractInfoDO selectContractInfoByServiceEnd(String custId) {
        return selectOne(new LambdaQueryWrapperX<ContractInfoDO>()
                .eq(ContractInfoDO::getCustId, custId)
                .orderByDesc(ContractInfoDO::getServiceEnd)
                .last("limit 1"));
    }

    default ContractInfoDO selectByContractId(String contractId) {
        return selectOne(ContractInfoDO::getContractId, contractId);
    }

    void revokeByContractId(@Param("contractId") String contractId);

    List<String> getAuditContractIds();

    void updateStatusByContractIds(@Param("contractIds") List<String> contractIds);

    void updateStatus(@Param("contractId") String contractId);

    default Long selectContractInfoNum(String contractId) {
        return selectCount(ContractInfoDO::getContractId, contractId);
    }

    Long selectDeletedContractInfoNum(@Param("contractInfo") ContractInfoCreateReqVO contractInfo);

    void updateByContractId(@Param("contractInfo") ContractInfoDO contractInfo);

    void updateByContractNo(@Param("contractInfo") ContractInfoDO contractInfo);

    String getContractNo();

    List<Map> getContractNos();

    List<ContractInfoDO> getTestContracts(@Param("tenantId") Long tenantId);

    void deleteTestContract(@Param("tenantId") Long tenantId);

    void tenantDelete(@Param("tenantId") Long tenantId);

    Long getContractList(@Param("tenantId") Long tenantId);

}
