package cn.iocoder.zj.module.customer.api.contractInfo;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.customer.api.contractInfo.dto.ContractInfoDTO;
import cn.iocoder.zj.module.customer.service.contractinfo.ContractInfoService;
import cn.iocoder.zj.module.customer.service.custinfo.CustInfoService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.util.List;
import java.util.Map;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class ContractInfoApiImpl implements ContractInfoApi {

    @Resource
    private ContractInfoService contractInfoService;
    @Resource
    private CustInfoService custInfoService;

    @Override
    public CommonResult<Boolean> updateStatus(String contractId) {
        contractInfoService.updateStatus(contractId);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> createTestContract(Long tenantId,String tenantName,String expireTime) {
        String custId=custInfoService.createTestCustInfo(tenantId,tenantName,expireTime);
        contractInfoService.createTestContractInfo(tenantId,tenantName,expireTime,custId);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> formalContract(Long tenantId,String contractNo) {
        contractInfoService.formalContract(tenantId,contractNo);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> transferContract(Long tenantId, String contractNo) {
        contractInfoService.transferContract(tenantId,contractNo);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> updateContract(Long tenantId, String contractNo) {
        contractInfoService.updateContract(tenantId,contractNo);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> tenantDelete(Long tenantId) {
        contractInfoService.tenantDelete(tenantId);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> updateExpireTime(String expireTime, String contractNo) {
        String custId=contractInfoService.updateExpireTime(expireTime,contractNo);
        custInfoService.updateExpireTime(expireTime,custId);
        return success(true);
    }

    @Override
    public CommonResult<Long> getContractInfoList(Long tenantId) {
        return success(contractInfoService.getContractList(tenantId));
    }
}
