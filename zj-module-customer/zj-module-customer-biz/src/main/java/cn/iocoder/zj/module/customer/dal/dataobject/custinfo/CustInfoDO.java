package cn.iocoder.zj.module.customer.dal.dataobject.custinfo;

import lombok.*;

import java.time.LocalDate;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 客户信息 DO
 *
 * <AUTHOR>
 */
@TableName("customer_cust_info")
@KeySequence("customer_cust_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustInfoDO extends BaseDO {

    /**
     * 客户ID
     */
    private String custId;
    /**
     * 客户名称
     */
    private String custName;
    /**
     * 客户所属行业：取字典表
     */
    private String industry;
    /**
     * 客户省市区编码
     */
    private String provinceCityDistrictCode;
    /**
     * 客户省市区
     */
    private String provinceCityDistrictName;
    /**
     * 客户状态：0.未开始，1.服务中，2.已结束
     */
    private Integer status;
    /**
     * 服务开始时间
     */
    private String serviceStartDate;
    /**
     * 服务到期时间
     */
    private String serviceEndDate;
    /**
     * 地区id
     */
    private Long regionId;
    /**
     * 租户id
     */
    private Long tenantId;

}
