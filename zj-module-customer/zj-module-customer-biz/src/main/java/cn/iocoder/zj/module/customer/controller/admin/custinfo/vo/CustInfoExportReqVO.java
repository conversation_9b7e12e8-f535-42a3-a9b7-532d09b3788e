package cn.iocoder.zj.module.customer.controller.admin.custinfo.vo;

import lombok.*;

import java.time.LocalDate;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 客户信息 Excel 导出 Request VO，参数和 CustInfoPageReqVO 是一致的")
@Data
public class CustInfoExportReqVO {

    @Schema(description = "客户名称")
    private String custName;

    @Schema(description = "客户所属行业：取字典表")
    private String industry;

    @Schema(description = "客户省市区编码")
    private String provinceCityDistrictCode;

    @Schema(description = "客户省市区")
    private String provinceCityDistrictName;

    @Schema(description = "客户状态：0.未开始，1.服务中，2.已结束")
    private Integer status;

    @Schema(description = "服务开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate serviceStartDate;

    @Schema(description = "服务到期时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate serviceEndDate;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "地区id")
    private Long regionId;

}
