package cn.iocoder.zj.module.customer.service.servicearchive;

import cn.hutool.core.io.IoUtil;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;

import cn.iocoder.zj.module.customer.controller.admin.custinfo.vo.CustInfoPageReqVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jodd.util.StringUtil;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.zj.module.customer.controller.admin.servicearchive.vo.*;
import cn.iocoder.zj.module.customer.dal.dataobject.servicearchive.ServiceArchiveDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.customer.convert.servicearchive.ServiceArchiveConvert;
import cn.iocoder.zj.module.customer.dal.mysql.servicearchive.ServiceArchiveMapper;
import org.springframework.web.multipart.MultipartFile;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.customer.enums.ErrorCodeConstants.*;

/**
 * 服务存档信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ServiceArchiveServiceImpl implements ServiceArchiveService {

    @Resource
    private ServiceArchiveMapper serviceArchiveMapper;


    @Override
    @SneakyThrows
    @TenantIgnore
    public Integer createServiceArchive(ServiceArchiveCreateReqVO createReqVO) {
        // 插入
        ServiceArchiveDO serviceArchive = ServiceArchiveConvert.INSTANCE.convert(createReqVO);
        serviceArchiveMapper.insert(serviceArchive);
        // 返回
        return serviceArchive.getId();
    }

    @Override
    @TenantIgnore
    public void updateServiceArchive(ServiceArchiveUpdateReqVO updateReqVO) {
        // 校验存在
        validateServiceArchiveExists(updateReqVO.getId());
        // 更新
        ServiceArchiveDO updateObj = ServiceArchiveConvert.INSTANCE.convert(updateReqVO);
        serviceArchiveMapper.updateById(updateObj);
    }

    @Override
    @TenantIgnore
    public void deleteServiceArchive(Integer id) {
        // 校验存在
        validateServiceArchiveExists(id);
        // 删除
        serviceArchiveMapper.deleteById(id);
    }

    private void validateServiceArchiveExists(Integer id) {
        if (serviceArchiveMapper.selectById(id) == null) {
            throw exception(SERVICE_ARCHIVE_NOT_EXISTS);
        }
    }

    @Override
    @TenantIgnore
    public ServiceArchiveDO getServiceArchive(Integer id) {
        return serviceArchiveMapper.selectById(id);
    }

    @Override
    public List<ServiceArchiveDO> getServiceArchiveList(Collection<Integer> ids) {
        return serviceArchiveMapper.selectBatchIds(ids);
    }

    @Override
    @TenantIgnore
    public PageResult<ServiceArchivePageReqVO> getServiceArchivePage(ServiceArchivePageReqVO pageReqVO) {
        Integer pageNo = pageReqVO.getPageNo();
        Integer pageSize = pageReqVO.getPageSize();
        Page page = new Page();
        page.setCurrent(pageNo);
        page.setSize(pageSize);
        if (StringUtil.isNotEmpty(pageReqVO.getSortBy())){
            String regex = "([a-z])([A-Z]+)";
            String replacement = "$1_$2";
            String output = pageReqVO.getSortBy().replaceAll(regex, replacement).toLowerCase();
            pageReqVO.setSortBy(output);
            if (pageReqVO.getSortBy().equals("upload_time")){
                pageReqVO.setSortBy("update_time");
            }
        }
        List<ServiceArchivePageReqVO> list = serviceArchiveMapper.getPage(page,pageReqVO);
        PageResult<ServiceArchivePageReqVO> result = new PageResult<ServiceArchivePageReqVO>().setList(list).setTotal(page.getTotal());
        return result;
    }

    @Override
    public List<ServiceArchiveDO> getServiceArchiveList(ServiceArchiveExportReqVO exportReqVO) {
        return serviceArchiveMapper.selectList(exportReqVO);
    }

}
