package cn.iocoder.zj.module.customer.dal.mysql.servicearchive;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.customer.dal.dataobject.servicearchive.ServiceArchiveDO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.customer.controller.admin.servicearchive.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 服务存档信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ServiceArchiveMapper extends BaseMapperX<ServiceArchiveDO> {

    default PageResult<ServiceArchiveDO> selectPage(ServiceArchivePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ServiceArchiveDO>()
                .eqIfPresent(ServiceArchiveDO::getContractId, reqVO.getContractId())
                .orderByDesc(ServiceArchiveDO::getId));
    }
    List<ServiceArchivePageReqVO> getPage(@Param("page") Page page, @Param("reqVO") ServiceArchivePageReqVO reqVO);


    default List<ServiceArchiveDO> selectList(ServiceArchiveExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ServiceArchiveDO>()
                .eqIfPresent(ServiceArchiveDO::getArchiveTitle, reqVO.getArchiveTitle())
                .eqIfPresent(ServiceArchiveDO::getFilePath, reqVO.getFilePath())
                .eqIfPresent(ServiceArchiveDO::getFileSize, reqVO.getFileSize())
                .likeIfPresent(ServiceArchiveDO::getFileName, reqVO.getFileName())
                .eqIfPresent(ServiceArchiveDO::getFileUrl, reqVO.getFileUrl())
                .eqIfPresent(ServiceArchiveDO::getContractId, reqVO.getContractId())
                .betweenIfPresent(ServiceArchiveDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ServiceArchiveDO::getId));
    }
    List<ServiceArchiveUpdateReqVO> getByCustId(@Param("custId") String custId);

}
