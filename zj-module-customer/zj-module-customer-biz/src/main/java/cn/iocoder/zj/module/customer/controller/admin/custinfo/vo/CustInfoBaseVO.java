package cn.iocoder.zj.module.customer.controller.admin.custinfo.vo;

import cn.iocoder.zj.module.customer.controller.admin.contactsinfo.vo.ContactsInfoRespVO;
import cn.iocoder.zj.module.customer.controller.admin.contractinfo.vo.ContractInfoBaseVO;
import cn.iocoder.zj.module.customer.controller.admin.contractinfo.vo.ContractInfoRespVO;
import cn.iocoder.zj.module.customer.controller.admin.contractinfo.vo.ContractInfoVo;
import cn.iocoder.zj.module.customer.controller.admin.servicearchive.vo.ServiceArchiveBaseVO;
import cn.iocoder.zj.module.customer.controller.admin.servicearchive.vo.ServiceArchivePageReqVO;
import cn.iocoder.zj.module.customer.controller.admin.servicearchive.vo.ServiceArchiveRespVO;
import cn.iocoder.zj.module.customer.controller.admin.servicearchive.vo.ServiceArchiveUpdateReqVO;
import cn.iocoder.zj.module.customer.controller.admin.visitinfo.vo.VisitInfoBaseVO;
import cn.iocoder.zj.module.customer.controller.admin.visitinfo.vo.VisitInfoRespVO;
import cn.iocoder.zj.module.customer.dal.dataobject.contactsinfo.ContactsInfoDO;
import cn.iocoder.zj.module.customer.dal.dataobject.contractinfo.ContractInfoDO;
import cn.iocoder.zj.module.customer.dal.dataobject.visitinfo.VisitInfoDO;
import cn.iocoder.zj.module.customer.service.contactsinfo.ContactsInfoServiceImpl;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 客户信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class CustInfoBaseVO {
    @Schema(description = "客户Id")
    private String custId;

    @Schema(description = "客户名称")
    private String custName;

    @Schema(description = "客户所属行业：取字典表")
    private String industry;

    @Schema(description = "客户省市区编码")
    private String provinceCityDistrictCode;

    @Schema(description = "客户省市区")
    private String provinceCityDistrictName;

    @Schema(description = "客户状态：0.未开始，1.服务中，2.已结束")
    private Integer status;

    @Schema(description = "服务开始时间")
    private String serviceStartDate;

    @Schema(description = "服务到期时间")
    private String serviceEndDate;

    @Schema(description = "地区id")
    private Long regionId;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "剩余服务天数")
    private Integer surplusDayCount;

    private List<ContractInfoVo> contractInfoList;

    private List<ContactsInfoDO> contactsInfoList;

    private List<ServiceArchiveUpdateReqVO> serviceArchiveList;

    private List<VisitInfoBaseVO> visitInfoList;


}
