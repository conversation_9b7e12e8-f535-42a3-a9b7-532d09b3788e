package cn.iocoder.zj.module.customer.dal.mysql.visitinfo;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.customer.dal.dataobject.visitinfo.VisitInfoDO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.customer.controller.admin.visitinfo.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 客户回访信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface VisitInfoMapper extends BaseMapperX<VisitInfoDO> {

    default PageResult<VisitInfoDO> selectPage(VisitInfoPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<VisitInfoDO>()
                .eqIfPresent(VisitInfoDO::getCustId, reqVO.getCustId())
                .likeIfPresent(VisitInfoDO::getCustName, reqVO.getCustName())
                .eqIfPresent(VisitInfoDO::getEvaluateLeave, reqVO.getEvaluateLeave())
                .eqIfPresent(VisitInfoDO::getEvaluateInfo, reqVO.getEvaluateInfo())
                .betweenIfPresent(VisitInfoDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(VisitInfoDO::getId));
    }

    default List<VisitInfoDO> selectList(VisitInfoExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<VisitInfoDO>()
                .eqIfPresent(VisitInfoDO::getCustId, reqVO.getCustId())
                .likeIfPresent(VisitInfoDO::getCustName, reqVO.getCustName())
                .eqIfPresent(VisitInfoDO::getEvaluateLeave, reqVO.getEvaluateLeave())
                .eqIfPresent(VisitInfoDO::getEvaluateInfo, reqVO.getEvaluateInfo())
                .betweenIfPresent(VisitInfoDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(VisitInfoDO::getId));
    }

    List<VisitInfoRespVO> getVisitInfoPage(@Param("mpPage") IPage<VisitInfoRespVO> mpPage, @Param("pageReqVO") VisitInfoPageReqVO pageReqVO);

    List<VisitInfoBaseVO> getByCustIdList(@Param("custId")String custId);
}
