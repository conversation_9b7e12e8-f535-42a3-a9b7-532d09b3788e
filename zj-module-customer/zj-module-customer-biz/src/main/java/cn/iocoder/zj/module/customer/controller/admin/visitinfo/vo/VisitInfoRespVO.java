package cn.iocoder.zj.module.customer.controller.admin.visitinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 客户回访信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VisitInfoRespVO extends VisitInfoBaseVO {

    @Schema(description = "回访信息id", required = true)
    private Integer id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "回访人名称")
    private String createName;

    @Schema(description = "回访人")
    private String creator;

}
