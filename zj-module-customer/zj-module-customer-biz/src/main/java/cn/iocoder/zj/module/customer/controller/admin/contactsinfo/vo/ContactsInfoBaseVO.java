package cn.iocoder.zj.module.customer.controller.admin.contactsinfo.vo;

import cn.iocoder.zj.framework.common.validation.Mobile;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 客户联系人信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ContactsInfoBaseVO {

    @Schema(description = "联系人名称", required = true)
    @NotNull(message = "联系人名称不能为空")
    private String contactsName;

    @Schema(description = "联系人手机号", required = true)
    @NotNull(message = "联系人手机号不能为空")
    @Mobile
    private String contactsTelephone;

    @Schema(description = "所属客户ID",required = true)
    @NotNull(message = "所属客户ID不能为空")
    private String custId;

    @Schema(description = "地区id")
    private Long regionId;

    @Schema(description = "职务")
    private String contactsPosition;

    @Schema(description = "部门")
    private String contactsDept;

}
