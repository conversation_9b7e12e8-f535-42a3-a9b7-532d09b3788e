package cn.iocoder.zj.module.customer.dal.dataobject.servicearchive;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 服务存档信息 DO
 *
 * <AUTHOR>
 */
@TableName("customer_service_archive")
@KeySequence("customer_service_archive_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceArchiveDO extends BaseDO {

    /**
     * 服务存档名称
     */
    private String archiveTitle;
    /**
     * 文件路径
     */
    private String filePath;
    /**
     * 文件大小
     */
    private String fileSize;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件url
     */
    private String fileUrl;
    /**
     * 关联合同id
     */
    private String contractId;
    /**
     * 服务存档id
     */
    @TableId
    private Integer id;

    /**
     * 备注
     */
    private String remarks;

}
