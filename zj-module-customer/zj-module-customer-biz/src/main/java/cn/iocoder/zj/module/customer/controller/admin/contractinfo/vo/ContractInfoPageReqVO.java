package cn.iocoder.zj.module.customer.controller.admin.contractinfo.vo;


import lombok.*;


import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
@Schema(description = "管理后台 - 合同信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ContractInfoPageReqVO extends PageParam {

    @Schema(description = "合同编号")
    private String contractNo;

    @Schema(description = "客户ID")
    private String custId;

    @Schema(description = "客户名称")
    private String custName;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "合同签订日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String signDate;

    @Schema(description = "服务截止日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String serviceEnd;

    @Schema(description = "合同状态：0.待审核，1.待交付，2.履约中，3.已到期")
    private Integer status;

    @Schema(description = "资产授权数量")
    private String assetNum;

    @Schema(description = "服务标签")
    private String serviceTag;

    @Schema(description = "实施负责人id")
    private String enforcerId;

    @Schema(description = "实施负责人姓名")
    private String enforcerName;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "地区id")
    private Long regionId;

    @Schema(description = "文件路径")
    private String filePath;

    @Schema(description = "文件大小")
    private String fileSize;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "文件url")
    private String fileUrl;

}
