package cn.iocoder.zj.module.customer.dal.mysql.contactsinfo;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.customer.dal.dataobject.contactsinfo.ContactsInfoDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.customer.controller.admin.contactsinfo.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 客户联系人信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ContactsInfoMapper extends BaseMapperX<ContactsInfoDO> {

    default PageResult<ContactsInfoDO> selectPage(ContactsInfoPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ContactsInfoDO>()
                .likeIfPresent(ContactsInfoDO::getContactsName, reqVO.getContactsName())
                .eqIfPresent(ContactsInfoDO::getContactsTelephone, reqVO.getContactsTelephone())
                .eqIfPresent(ContactsInfoDO::getCustId, reqVO.getCustId())
                .betweenIfPresent(ContactsInfoDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(ContactsInfoDO::getRegionId, reqVO.getRegionId())
                .orderByDesc(ContactsInfoDO::getCreateTime));
    }

    default List<ContactsInfoDO> selectList(ContactsInfoExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ContactsInfoDO>()
                .likeIfPresent(ContactsInfoDO::getContactsName, reqVO.getContactsName())
                .eqIfPresent(ContactsInfoDO::getContactsTelephone, reqVO.getContactsTelephone())
                .eqIfPresent(ContactsInfoDO::getCustId, reqVO.getCustId())
                .betweenIfPresent(ContactsInfoDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(ContactsInfoDO::getRegionId, reqVO.getRegionId())
                .orderByDesc(ContactsInfoDO::getCreateTime));
    }

    void deleteByCustId(@Param("custId") String custId);

    default List<ContactsInfoDO> selectByNameAndTel(String custId,String name,String phone){
        return selectList(new LambdaQueryWrapperX<ContactsInfoDO>()
                .eqIfPresent(ContactsInfoDO::getContactsName, name)
                .eqIfPresent(ContactsInfoDO::getContactsTelephone, phone)
                .eqIfPresent(ContactsInfoDO::getCustId, custId));
    }
}
