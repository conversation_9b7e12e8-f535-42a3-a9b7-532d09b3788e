package cn.iocoder.zj.module.customer.service.visitinfo;

import cn.iocoder.zj.framework.common.pojo.PageParam;
import cn.iocoder.zj.framework.mybatis.core.util.MyBatisUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import jodd.util.StringUtil;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.zj.module.customer.controller.admin.visitinfo.vo.*;
import cn.iocoder.zj.module.customer.dal.dataobject.visitinfo.VisitInfoDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.customer.convert.visitinfo.VisitInfoConvert;
import cn.iocoder.zj.module.customer.dal.mysql.visitinfo.VisitInfoMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.customer.enums.ErrorCodeConstants.*;

/**
 * 客户回访信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class VisitInfoServiceImpl implements VisitInfoService {

    @Resource
    private VisitInfoMapper visitInfoMapper;

    @Override
    public Integer createVisitInfo(VisitInfoCreateReqVO createReqVO) {
        // 插入
        VisitInfoDO visitInfo = VisitInfoConvert.INSTANCE.convert(createReqVO);
        visitInfoMapper.insert(visitInfo);
        // 返回
        return visitInfo.getId();
    }

    @Override
    public void updateVisitInfo(VisitInfoUpdateReqVO updateReqVO) {
        // 校验存在
        validateVisitInfoExists(updateReqVO.getId());
        // 更新
        VisitInfoDO updateObj = VisitInfoConvert.INSTANCE.convert(updateReqVO);
        visitInfoMapper.updateById(updateObj);
    }

    @Override
    public void deleteVisitInfo(Integer id) {
        // 校验存在
        validateVisitInfoExists(id);
        // 删除
        visitInfoMapper.deleteById(id);
    }

    private void validateVisitInfoExists(Integer id) {
        if (visitInfoMapper.selectById(id) == null) {
            throw exception(VISIT_INFO_NOT_EXISTS);
        }
    }

    @Override
    public VisitInfoDO getVisitInfo(Integer id) {
        return visitInfoMapper.selectById(id);
    }

    @Override
    public List<VisitInfoDO> getVisitInfoList(Collection<Integer> ids) {
        return visitInfoMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<VisitInfoRespVO> getVisitInfoPage(VisitInfoPageReqVO pageReqVO) {
        PageParam pageParam = new PageParam().setPageNo(pageReqVO.getPageNo()).setPageSize(pageReqVO.getPageSize());
        IPage<VisitInfoRespVO> mpPage = MyBatisUtils.buildPage(pageParam);
        if (StringUtil.isNotEmpty(pageReqVO.getSortBy())){
            String regex = "([a-z])([A-Z]+)";
            String replacement = "$1_$2";
            String output = pageReqVO.getSortBy().replaceAll(regex, replacement).toLowerCase();
            pageReqVO.setSortBy(output);
        }
        return new PageResult<>(visitInfoMapper.getVisitInfoPage(mpPage, pageReqVO), mpPage.getTotal());
    }

    @Override
    public List<VisitInfoDO> getVisitInfoList(VisitInfoExportReqVO exportReqVO) {
        return visitInfoMapper.selectList(exportReqVO);
    }

}
