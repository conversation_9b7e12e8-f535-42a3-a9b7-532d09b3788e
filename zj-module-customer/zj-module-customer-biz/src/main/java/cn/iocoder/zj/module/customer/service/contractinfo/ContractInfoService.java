package cn.iocoder.zj.module.customer.service.contractinfo;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.customer.controller.admin.contractinfo.vo.ContractInfoCreateReqVO;
import cn.iocoder.zj.module.customer.controller.admin.contractinfo.vo.ContractInfoExportReqVO;
import cn.iocoder.zj.module.customer.controller.admin.contractinfo.vo.ContractInfoPageReqVO;
import cn.iocoder.zj.module.customer.controller.admin.contractinfo.vo.ContractInfoUpdateReqVO;
import cn.iocoder.zj.module.customer.dal.dataobject.contractinfo.ContractInfoDO;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 合同信息 Service 接口
 *
 * <AUTHOR>
 */
public interface ContractInfoService {

    /**
     * 创建合同信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createContractInfo(@Valid ContractInfoCreateReqVO createReqVO);

    /**
     * 更新合同信息
     *
     * @param updateReqVO 更新信息
     */
    void updateContractInfo(@Valid ContractInfoUpdateReqVO updateReqVO);

    /**
     * 删除合同信息
     *
     * @param id 编号
     */
    void deleteContractInfo(String id);

    /**
     * 获得合同信息
     *
     * @param id 编号
     * @return 合同信息
     */
    ContractInfoDO getContractInfo(String id);

    /**
     * 获得合同信息列表
     *
     * @param ids 编号
     * @return 合同信息列表
     */
    List<ContractInfoDO> getContractInfoList(Collection<String> ids);

    /**
     * 获得合同信息分页
     *
     * @param pageReqVO 分页查询
     * @return 合同信息分页
     */
    PageResult<ContractInfoDO> getContractInfoPage(ContractInfoPageReqVO pageReqVO);

    /**
     * 获得合同信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 合同信息列表
     */
    List<ContractInfoDO> getContractInfoList(ContractInfoExportReqVO exportReqVO);

    /**
     * crm删除合同信息
     *
     * @param id 编号
     */
    void revokeContractInfoByCrm(String id);

    /**
     * 获得待审核合同id
     *
     * @return 待审核合同id
     */
    List<String> getAuditContractIds();

    /**
     * 更新合同状态
     *
     * @param contractIds 更新合同ID
     */
    void updateStatusByContractIds(@Valid List<String> contractIds);

    /**
     * 更新合同状态
     *
     * @param contractId 更新合同ID
     */
    void updateStatus(@Valid String contractId);

    Map<String,String> createFile(MultipartFile file);


    byte[] downloadFile(String path);

    /**
     * 获得合同数量
     *
     * @param contractId 客户ID
     * @return 客户信息
     */
    Long getContractInfoNum(String contractId);

    /**
     * crm创建合同信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createContractInfoByCrm(@Valid ContractInfoCreateReqVO createReqVO);

    /**
     * 创建测试合同
     *
     * @return 测试合同ID
     */
    String createTestContractInfo(Long tenantId,String tenantName,String expireTime,String custId);

    void formalContract(Long tenantId,String contractNo);

    String transferContract(Long tenantId,String contractNo);

    String updateContract(Long tenantId,String contractNo);

    void tenantDelete(Long tenantId);

    List<Map> getContractNos();

    String updateExpireTime(String expireTime,String contractNo);

    Long getContractList(Long tenantId);
}
