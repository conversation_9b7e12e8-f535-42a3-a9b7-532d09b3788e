package cn.iocoder.zj.module.customer.controller.admin.servicearchive.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 服务存档信息更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ServiceArchiveUpdateReqVO extends ServiceArchiveBaseVO {

    @Schema(description = "服务存档id")
    private Integer id;

    @Schema(description = "上传时间")
    private String updateTime;

    @Schema(description = "关联合同名称")
    private String contractName;


}
