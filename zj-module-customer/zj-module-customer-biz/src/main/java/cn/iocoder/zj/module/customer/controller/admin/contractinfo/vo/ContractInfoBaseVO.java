package cn.iocoder.zj.module.customer.controller.admin.contractinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 合同信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ContractInfoBaseVO {

    @Schema(description = "合同id", required = true)
    private String contractId;

    @Schema(description = "合同编号", required = true)
    private String contractNo;

    @Schema(description = "客户ID", required = true)
    private String custId;

    @Schema(description = "客户名称")
    private String custName;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "合同签订日期")
    private String signDate;

    @Schema(description = "服务截止日期")
    private String serviceEnd;

    @Schema(description = "合同状态：0.待审核，1.待交付，2.履约中，3.已到期")
    private Integer status;

    @Schema(description = "资产授权数量")
    private String assetNum;

    @Schema(description = "服务标签")
    private String serviceTag;

    @Schema(description = "实施负责人id")
    private String enforcerId;

    @Schema(description = "实施负责人姓名")
    private String enforcerName;

    @Schema(description = "地区id")
    private Long regionId;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "是否测试合同")
    private Integer isTest;

    @Schema(description = "文件路径")
    private String filePath;

    @Schema(description = "文件大小")
    private String fileSize;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "文件url")
    private String fileUrl;

}
