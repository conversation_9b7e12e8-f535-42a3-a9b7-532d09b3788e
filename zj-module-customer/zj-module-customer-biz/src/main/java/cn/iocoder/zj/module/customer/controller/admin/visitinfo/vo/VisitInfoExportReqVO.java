package cn.iocoder.zj.module.customer.controller.admin.visitinfo.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 客户回访信息 Excel 导出 Request VO，参数和 VisitInfoPageReqVO 是一致的")
@Data
public class VisitInfoExportReqVO {

    @Schema(description = "回访客户id")
    private String custId;

    @Schema(description = "回访客户名称")
    private String custName;

    @Schema(description = "服务评价")
    private Double evaluateLeave;

    @Schema(description = "回访详情")
    private String evaluateInfo;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
