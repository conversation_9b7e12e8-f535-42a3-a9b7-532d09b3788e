package cn.iocoder.zj.module.customer.convert.contactsinfo;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.customer.controller.admin.contactsinfo.vo.*;
import cn.iocoder.zj.module.customer.dal.dataobject.contactsinfo.ContactsInfoDO;

/**
 * 客户联系人信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ContactsInfoConvert {

    ContactsInfoConvert INSTANCE = Mappers.getMapper(ContactsInfoConvert.class);

    ContactsInfoDO convert(ContactsInfoCreateReqVO bean);

    ContactsInfoDO convert(ContactsInfoUpdateReqVO bean);

    ContactsInfoRespVO convert(ContactsInfoDO bean);

    List<ContactsInfoRespVO> convertList(List<ContactsInfoDO> list);

    List<ContactsInfoDO> convertList2(List<ContactsInfoCreateReqVO> list);

    PageResult<ContactsInfoRespVO> convertPage(PageResult<ContactsInfoDO> page);

    List<ContactsInfoExcelVO> convertList02(List<ContactsInfoDO> list);

}
