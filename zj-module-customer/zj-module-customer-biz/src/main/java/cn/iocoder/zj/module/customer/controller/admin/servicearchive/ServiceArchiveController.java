package cn.iocoder.zj.module.customer.controller.admin.servicearchive;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.customer.controller.admin.servicearchive.vo.*;
import cn.iocoder.zj.module.customer.dal.dataobject.servicearchive.ServiceArchiveDO;
import cn.iocoder.zj.module.customer.convert.servicearchive.ServiceArchiveConvert;
import cn.iocoder.zj.module.customer.service.servicearchive.ServiceArchiveService;

@Tag(name = "管理后台 - 服务存档信息")
@RestController
@RequestMapping("/customer/service-archive")
@Validated
public class ServiceArchiveController {

    @Resource
    private ServiceArchiveService serviceArchiveService;

    @PostMapping("/create")
    @Operation(summary = "创建服务存档信息")
    public CommonResult<Integer> createServiceArchive(@Valid  @RequestBody  ServiceArchiveCreateReqVO createReqVO) {
        return success(serviceArchiveService.createServiceArchive(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新服务存档信息")
    public CommonResult<Boolean> updateServiceArchive(@Valid @RequestBody ServiceArchiveUpdateReqVO updateReqVO) {
        serviceArchiveService.updateServiceArchive(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除服务存档信息")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteServiceArchive(@RequestParam("id") Integer id) {
        serviceArchiveService.deleteServiceArchive(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得服务存档信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<ServiceArchiveRespVO> getServiceArchive(@RequestParam("id") Integer id) {
        ServiceArchiveDO serviceArchive = serviceArchiveService.getServiceArchive(id);
        return success(ServiceArchiveConvert.INSTANCE.convert(serviceArchive));
    }

    @GetMapping("/list")
    @Operation(summary = "获得服务存档信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    public CommonResult<List<ServiceArchiveRespVO>> getServiceArchiveList(@RequestParam("ids") Collection<Integer> ids) {
        List<ServiceArchiveDO> list = serviceArchiveService.getServiceArchiveList(ids);
        return success(ServiceArchiveConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得服务存档信息分页")
    public CommonResult<PageResult<ServiceArchivePageReqVO>> getServiceArchivePage(@Valid ServiceArchivePageReqVO pageVO) {
        PageResult<ServiceArchivePageReqVO> pageResult = serviceArchiveService.getServiceArchivePage(pageVO);
        return success(pageResult );
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出服务存档信息 Excel")
    @OperateLog(type = EXPORT)
    public void exportServiceArchiveExcel(@Valid ServiceArchiveExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<ServiceArchiveDO> list = serviceArchiveService.getServiceArchiveList(exportReqVO);
        // 导出 Excel
        List<ServiceArchiveExcelVO> datas = ServiceArchiveConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "服务存档信息.xls", "数据", ServiceArchiveExcelVO.class, datas);
    }

}
