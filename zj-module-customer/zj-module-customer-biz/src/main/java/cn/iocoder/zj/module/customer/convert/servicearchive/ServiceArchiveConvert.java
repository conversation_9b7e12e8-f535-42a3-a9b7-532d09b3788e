package cn.iocoder.zj.module.customer.convert.servicearchive;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.customer.controller.admin.servicearchive.vo.*;
import cn.iocoder.zj.module.customer.dal.dataobject.servicearchive.ServiceArchiveDO;

/**
 * 服务存档信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ServiceArchiveConvert {

    ServiceArchiveConvert INSTANCE = Mappers.getMapper(ServiceArchiveConvert.class);

    ServiceArchiveDO convert(ServiceArchiveCreateReqVO bean);

    ServiceArchiveDO convert(ServiceArchiveUpdateReqVO bean);

    ServiceArchiveRespVO convert(ServiceArchiveDO bean);

    List<ServiceArchiveRespVO> convertList(List<ServiceArchiveDO> list);

    PageResult<ServiceArchiveRespVO> convertPage(PageResult<ServiceArchiveDO> page);

    List<ServiceArchiveExcelVO> convertList02(List<ServiceArchiveDO> list);

}
