package cn.iocoder.zj.module.customer.framework.rpc.config;


import cn.iocoder.zj.module.om.api.dbfile.DbfileApi;
import cn.iocoder.zj.module.infra.api.file.FileApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName : RpcConfiguration  //类名
 * @Description : RPC 接口  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/23  10:38
 */

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {PlatformconfigApi.class, DbfileApi.class, FileApi.class})
public class RpcConfiguration {
}
