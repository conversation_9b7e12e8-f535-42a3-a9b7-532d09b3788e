package cn.iocoder.zj.module.customer.dal.mysql.custinfo;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;

import cn.iocoder.zj.module.customer.dal.dataobject.custinfo.CustInfoDO;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.customer.controller.admin.custinfo.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 客户信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CustInfoMapper extends BaseMapperX<CustInfoDO> {

    List<CustInfoPageReqVO> selectCustInfoPage(@Param("page") Page page, @Param("reqVO")CustInfoPageReqVO reqVO) ;

//        LambdaQueryWrapperX<CustInfoPageReqVO> wrapper = new LambdaQueryWrapperX<>();
//        if (reqVO.getCustName()!=null){
//            wrapper.likeIfPresent(CustInfoPageReqVO::getCustName, reqVO.getCustName());
//        }
//
//        //客户状态自己定义
//        wrapper.eq(reqVO.getIndustry()!=null, CustInfoPageReqVO::getIndustry, reqVO.getIndustry())
//                .eq(reqVO.getRegionId()!=null, CustInfoPageReqVO::getRegionId, reqVO.getRegionId());
//        //客户状态
//        if(reqVO.getStatus()!=null){
//            if (reqVO.getStatus().equals("1")){
//                wrapper.gt(CustInfoPageReqVO::getServiceStartDate,new Date());
//            }else if (reqVO.getStatus().equals("2")){
//                wrapper.gt(CustInfoPageReqVO::getServiceEndDate,new Date())
//                        .lt(CustInfoPageReqVO::getServiceStartDate,new Date());
//            }else if (reqVO.getStatus().equals("3")){
//                wrapper.lt(CustInfoPageReqVO::getServiceEndDate,new Date());
//            }
//        }
//
//        if (reqVO.getServiceStartDate()!=null && reqVO.getServiceEndDate()!=null){
//            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            Date endDate = new Date();
//            Date startDate = new Date();
//            try {
//                endDate = sdf.parse(reqVO.getServiceEndDate());
//                Calendar calendar = new GregorianCalendar();
//                calendar.setTime(endDate);
//                // 把日期设置为当天最后一秒
//                calendar.set(Calendar.HOUR_OF_DAY, 23);
//                calendar.set(Calendar.MINUTE, 59);
//                calendar.set(Calendar.SECOND, 59);
//                endDate = calendar.getTime();
//
//                startDate = sdf.parse(reqVO.getServiceStartDate());
//                Calendar calendar2 = new GregorianCalendar();
//                calendar2.setTime(endDate);
//                calendar2.set(Calendar.HOUR_OF_DAY, 23);
//                calendar2.set(Calendar.MINUTE, 59);
//                calendar2.set(Calendar.SECOND, 59);
//                startDate=calendar2.getTime();
//            } catch (ParseException e) {
//                e.printStackTrace();
//            }
//            wrapper.gt(CustInfoPageReqVO::getServiceEndDate,sdf.format(endDate))
//                    .lt(CustInfoPageReqVO::getServiceStartDate,sdf.format(startDate));
//        }

//
//
//        return null;
//    }


    default List<CustInfoDO> selectList(CustInfoExportReqVO reqVO) {

        return selectList(new LambdaQueryWrapperX<CustInfoDO>()
                .likeIfPresent(CustInfoDO::getCustName, reqVO.getCustName())
                .eqIfPresent(CustInfoDO::getIndustry, reqVO.getIndustry())
                .eqIfPresent(CustInfoDO::getProvinceCityDistrictCode, reqVO.getProvinceCityDistrictCode())
                .likeIfPresent(CustInfoDO::getProvinceCityDistrictName, reqVO.getProvinceCityDistrictName())
                .eqIfPresent(CustInfoDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(CustInfoDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(CustInfoDO::getRegionId, reqVO.getRegionId())
                .orderByDesc(CustInfoDO::getCreateTime));

    }

    default Long selectCustInfoNum(String custId) {
        return selectCount(CustInfoDO::getCustId, custId);
    }

    default CustInfoDO selectByCustId(String custId) {
        return selectOne(CustInfoDO::getCustId, custId);
    }

    default int updateByCustId(CustInfoDO updateObj) {
        LambdaQueryWrapperX<CustInfoDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(CustInfoDO::getCustId,updateObj.getCustId());
        return update(updateObj,wrapper);
    }

    String getByIndustryId(@Param("industry")String industry);

    void deleteTestByCustId(String custId);

    List<Map> getCustomersList();

    List<Map> customerIndustry();
}
