package cn.iocoder.zj.module.customer.controller.admin.contractinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 合同信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ContractInfoExcelVO {

    @ExcelProperty("合同ID")
    private String contractId;

    @ExcelProperty("合同编号")
    private String contractNo;

    @ExcelProperty("客户ID")
    private String custId;

    @ExcelProperty("客户名称")
    private String custName;

    @ExcelProperty("项目名称")
    private String projectName;

    @ExcelProperty("合同签订日期")
    private LocalDate signDate;

    @ExcelProperty("服务截止日期")
    private LocalDate serviceEnd;

    @ExcelProperty("合同状态：0.待审核，1.待交付，2.履约中，3.已到期")
    private Integer status;

    @ExcelProperty("资产授权数量")
    private String assetNum;

    @ExcelProperty("服务标签")
    private String serviceTag;

    @ExcelProperty("实施负责人id")
    private String enforcerId;

    @ExcelProperty("实施负责人姓名")
    private String enforcerName;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("地区id")
    private Long regionId;

    @ExcelProperty("文件路径")
    private String filePath;

    @ExcelProperty("文件大小")
    private String fileSize;

    @ExcelProperty("文件名称")
    private String fileName;

    @ExcelProperty("文件url")
    private String fileUrl;

}
