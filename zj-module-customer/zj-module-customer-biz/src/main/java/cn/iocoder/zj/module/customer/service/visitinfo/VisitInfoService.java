package cn.iocoder.zj.module.customer.service.visitinfo;

import java.util.*;
import javax.validation.*;
import cn.iocoder.zj.module.customer.controller.admin.visitinfo.vo.*;
import cn.iocoder.zj.module.customer.dal.dataobject.visitinfo.VisitInfoDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 客户回访信息 Service 接口
 *
 * <AUTHOR>
 */
public interface VisitInfoService {

    /**
     * 创建客户回访信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createVisitInfo(@Valid VisitInfoCreateReqVO createReqVO);

    /**
     * 更新客户回访信息
     *
     * @param updateReqVO 更新信息
     */
    void updateVisitInfo(@Valid VisitInfoUpdateReqVO updateReqVO);

    /**
     * 删除客户回访信息
     *
     * @param id 编号
     */
    void deleteVisitInfo(Integer id);

    /**
     * 获得客户回访信息
     *
     * @param id 编号
     * @return 客户回访信息
     */
    VisitInfoDO getVisitInfo(Integer id);

    /**
     * 获得客户回访信息列表
     *
     * @param ids 编号
     * @return 客户回访信息列表
     */
    List<VisitInfoDO> getVisitInfoList(Collection<Integer> ids);

    /**
     * 获得客户回访信息分页
     *
     * @param pageReqVO 分页查询
     * @return 客户回访信息分页
     */
    PageResult<VisitInfoRespVO> getVisitInfoPage(VisitInfoPageReqVO pageReqVO);

    /**
     * 获得客户回访信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 客户回访信息列表
     */
    List<VisitInfoDO> getVisitInfoList(VisitInfoExportReqVO exportReqVO);

}
