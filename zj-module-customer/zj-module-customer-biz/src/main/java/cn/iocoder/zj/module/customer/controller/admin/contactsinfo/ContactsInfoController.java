package cn.iocoder.zj.module.customer.controller.admin.contactsinfo;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.customer.controller.admin.contactsinfo.vo.*;
import cn.iocoder.zj.module.customer.dal.dataobject.contactsinfo.ContactsInfoDO;
import cn.iocoder.zj.module.customer.convert.contactsinfo.ContactsInfoConvert;
import cn.iocoder.zj.module.customer.service.contactsinfo.ContactsInfoService;

@Tag(name = "管理后台 - 客户联系人信息")
@RestController
@RequestMapping("/customer/contacts-info")
@Validated
public class ContactsInfoController {

    @Resource
    private ContactsInfoService contactsInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建客户联系人信息")
    @OperateLog(type = CREATE)
    public CommonResult<Integer> createContactsInfo(@Valid @RequestBody ContactsInfoCreateReqVO createReqVO) {
        return success(contactsInfoService.createContactsInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新客户联系人信息")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateContactsInfo(@Valid @RequestBody ContactsInfoUpdateReqVO updateReqVO) {
        contactsInfoService.updateContactsInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除客户联系人信息")
    @Parameter(name = "id", description = "编号", required = true)
    @OperateLog(type = DELETE)
    public CommonResult<Boolean> deleteContactsInfo(@RequestParam("id") String id) {
        contactsInfoService.deleteContactsInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得客户联系人信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<ContactsInfoRespVO> getContactsInfo(@RequestParam("id") String id) {
        ContactsInfoDO contactsInfo = contactsInfoService.getContactsInfo(id);
        return success(ContactsInfoConvert.INSTANCE.convert(contactsInfo));
    }

    @GetMapping("/list")
    @Operation(summary = "获得客户联系人信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    public CommonResult<List<ContactsInfoRespVO>> getContactsInfoList(@RequestParam("ids") Collection<String> ids) {
        List<ContactsInfoDO> list = contactsInfoService.getContactsInfoList(ids);
        return success(ContactsInfoConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得客户联系人信息分页")
    public CommonResult<PageResult<ContactsInfoRespVO>> getContactsInfoPage(@Valid ContactsInfoPageReqVO pageVO) {
        PageResult<ContactsInfoDO> pageResult = contactsInfoService.getContactsInfoPage(pageVO);
        return success(ContactsInfoConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出客户联系人信息 Excel")
    @OperateLog(type = EXPORT)
    public void exportContactsInfoExcel(@Valid ContactsInfoExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<ContactsInfoDO> list = contactsInfoService.getContactsInfoList(exportReqVO);
        // 导出 Excel
        List<ContactsInfoExcelVO> datas = ContactsInfoConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "客户联系人信息.xls", "数据", ContactsInfoExcelVO.class, datas);
    }

    @PostMapping("/create/list")
    @Operation(summary = "批量创建客户联系人信息")
    @OperateLog(type = CREATE)
    public CommonResult<Boolean> createContactsInfoList(@Valid @RequestBody List<ContactsInfoCreateReqVO> createReqVOList) {
        return success(contactsInfoService.createContactsInfoList(createReqVOList));
    }
}
