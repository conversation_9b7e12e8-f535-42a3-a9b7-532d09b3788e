package cn.iocoder.zj.module.customer.service.custinfo;

import java.util.*;
import javax.validation.*;
import cn.iocoder.zj.module.customer.controller.admin.custinfo.vo.*;
import cn.iocoder.zj.module.customer.dal.dataobject.custinfo.CustInfoDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 客户信息 Service 接口
 *
 * <AUTHOR>
 */
public interface CustInfoService {

    /**
     * 创建客户信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createCustInfo(@Valid CustInfoCreateReqVO createReqVO);

    /**
     * 更新客户信息
     *
     * @param updateReqVO 更新信息
     */
    void updateCustInfo(@Valid CustInfoUpdateReqVO updateReqVO);

    /**
     * 删除客户信息
     *
     * @param id 编号
     */
    void deleteCustInfo(String id);

    /**
     * 获得客户信息
     *
     * @param id 编号
     * @return 客户信息
     */
    CustInfoBaseVO getCustInfo(String id);

    /**
     * 获得客户信息列表
     *
     * @param ids 编号
     * @return 客户信息列表
     */
    List<CustInfoDO> getCustInfoList(Collection<String> ids);

    /**
     * 获得客户信息分页
     *
     * @param pageReqVO 分页查询
     * @return 客户信息分页
     */
    PageResult<CustInfoPageReqVO> getCustInfoPage(CustInfoPageReqVO pageReqVO);

    /**
     * 获得客户信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 客户信息列表
     */
    List<CustInfoDO> getCustInfoList(CustInfoExportReqVO exportReqVO);

    /**
     * 获得客户数量
     *
     * @param custId 客户ID
     * @return 客户信息
     */
    Long getCustInfoNum(String custId);

    /**
     * 获得客户信息列表
     *
     * @return 客户信息列表
     */
    List<CustInfoDO> getCustInfoeSlectList();

    /**
     * 创建测试客户
     *
     * @return 测试客户ID
     */
    String createTestCustInfo(Long tenantId,String tenantName,String expireTime);

    /**
     * 修改测试客户服务日期
     *
     * @return 测试客户ID
     */
    void updateExpireTime(String expireTime, String custId);

    List<Map> getCustomersList();

    List<Map> customerIndustry();
}
