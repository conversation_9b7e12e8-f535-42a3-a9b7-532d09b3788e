package cn.iocoder.zj.module.customer.controller.admin.custinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 客户信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CustInfoRespVO extends CustInfoBaseVO {

    @Schema(description = "客户ID", required = true)
    private String custId;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
