package cn.iocoder.zj.module.customer.dal.dataobject.contractinfo;

import lombok.*;

import java.time.LocalDate;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 合同信息 DO
 *
 * <AUTHOR>
 */
@TableName("customer_contract_info")
@KeySequence("customer_contract_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContractInfoDO extends BaseDO {

    /**
     * 合同ID
     */
    @TableId(type = IdType.INPUT)
    private String contractId;
    /**
     * 合同编号
     */
    private String contractNo;
    /**
     * 客户ID
     */
    private String custId;
    /**
     * 客户名称
     */
    private String custName;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 合同签订日期
     */
    private String signDate;
    /**
     * 服务截止日期
     */
    private String serviceEnd;
    /**
     * 合同状态：0.待审核，1.待交付，2.履约中，3.已到期
     */
    private Integer status;
    /**
     * 资产授权数量
     */
    private String assetNum;
    /**
     * 服务标签
     */
    private String serviceTag;
    /**
     * 实施负责人id
     */
    private String enforcerId;
    /**
     * 实施负责人姓名
     */
    private String enforcerName;
    /**
     * 地区id
     */
    private Long regionId;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 是否测试合同
     */
    private Integer isTest;
    /**
     * 文件路径
     */
    private String filePath;
    /**
     * 文件大小
     */
    private String fileSize;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件url
     */
    private String fileUrl;

}
