package cn.iocoder.zj.module.customer.service.custinfo;


import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.customer.controller.admin.contactsinfo.vo.ContactsInfoExportReqVO;
import cn.iocoder.zj.module.customer.controller.admin.servicearchive.vo.ServiceArchiveBaseVO;
import cn.iocoder.zj.module.customer.controller.admin.servicearchive.vo.ServiceArchiveUpdateReqVO;
import cn.iocoder.zj.module.customer.controller.admin.visitinfo.vo.VisitInfoBaseVO;
import cn.iocoder.zj.module.customer.controller.admin.visitinfo.vo.VisitInfoExportReqVO;
import cn.iocoder.zj.module.customer.dal.dataobject.contactsinfo.ContactsInfoDO;

import cn.iocoder.zj.module.customer.dal.dataobject.visitinfo.VisitInfoDO;
import cn.iocoder.zj.module.customer.dal.mysql.contractinfo.ContractInfoMapper;
import cn.iocoder.zj.framework.common.util.date.DateUtils;
import cn.iocoder.zj.module.customer.controller.admin.contractinfo.vo.ContractInfoVo;
import cn.iocoder.zj.module.customer.dal.mysql.contactsinfo.ContactsInfoMapper;
import cn.iocoder.zj.module.customer.dal.mysql.servicearchive.ServiceArchiveMapper;
import cn.iocoder.zj.module.customer.dal.mysql.visitinfo.VisitInfoMapper;

import cn.iocoder.zj.module.system.api.dict.DictDataApi;
import cn.iocoder.zj.module.system.api.dict.dto.DictDataRespDTO;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import cn.iocoder.zj.module.customer.controller.admin.custinfo.vo.*;
import cn.iocoder.zj.module.customer.dal.dataobject.custinfo.CustInfoDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.customer.convert.custinfo.CustInfoConvert;
import cn.iocoder.zj.module.customer.dal.mysql.custinfo.CustInfoMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.customer.enums.ErrorCodeConstants.*;

/**
 * 客户信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CustInfoServiceImpl implements CustInfoService {

    @Resource
    private CustInfoMapper custInfoMapper;

    @Autowired
    private ContractInfoMapper contractInfoMapper;

    @Autowired
    private ContactsInfoMapper contactsInfoMapper;

    @Autowired
    private ServiceArchiveMapper serviceArchiveMapper;

    @Autowired
    private VisitInfoMapper visitInfoMapper;

    @Autowired
    private DictDataApi dictDataApi;


    @Override
    public String createCustInfo(CustInfoCreateReqVO createReqVO) {
        // 插入
        CustInfoDO custInfo = CustInfoConvert.INSTANCE.convert(createReqVO);
        custInfo.setServiceStartDate(contractInfoMapper.selectContractInfoBySignDate(custInfo.getCustId()).getSignDate());
        custInfo.setServiceEndDate(contractInfoMapper.selectContractInfoByServiceEnd(custInfo.getCustId()).getServiceEnd());
        custInfoMapper.insert(custInfo);
        // 返回
        return custInfo.getCustId();
    }

    @Override
    public void updateCustInfo(CustInfoUpdateReqVO updateReqVO) {
        // 校验存在
        validateCustInfoExists(updateReqVO.getCustId());
        // 更新
        CustInfoDO updateObj = CustInfoConvert.INSTANCE.convert(updateReqVO);
        updateObj.setServiceStartDate(contractInfoMapper.selectContractInfoBySignDate(updateObj.getCustId()).getSignDate());
        updateObj.setServiceEndDate(contractInfoMapper.selectContractInfoByServiceEnd(updateObj.getCustId()).getServiceEnd());
        custInfoMapper.updateByCustId(updateObj);
    }

    @Override
    public void deleteCustInfo(String id) {
        // 校验存在
        validateCustInfoExists(id);
        // 删除
        custInfoMapper.deleteById(id);
    }

    @TenantIgnore
    private void validateCustInfoExists(String custId) {
        if (custInfoMapper.selectByCustId(custId) == null) {
            throw exception(CUST_INFO_NOT_EXISTS);
        }
    }

    @Override
    @TenantIgnore
    public CustInfoBaseVO getCustInfo(String id) {
        CustInfoDO custInfoDO = custInfoMapper.selectByCustId(id);
        if (StringUtils.isNotBlank(custInfoDO.getIndustry())) {
            String industryName = custInfoMapper.getByIndustryId(custInfoDO.getIndustry());
            custInfoDO.setIndustry(industryName);
        }

        CustInfoBaseVO cust = new CustInfoBaseVO();
        try {
            BeanUtils.copyProperties(custInfoDO, cust);

        } catch (Exception e) {
            e.printStackTrace();
        }
        long startDiff = DateUtils.diff(DateUtils.toDate(cust.getServiceStartDate()), new Date());
        long endDiff = DateUtils.diff(DateUtils.toDate(cust.getServiceEndDate()), new Date());
        if (startDiff > 0) {
            cust.setSurplusDayCount(0);
            cust.setStatus(0);
        } else if (startDiff < 0 && endDiff > 0) {
            cust.setSurplusDayCount((int) Math.ceil(endDiff / (24 * 60 * 60 * 1000)));
            cust.setStatus(1);
        } else {
            cust.setSurplusDayCount(0);
            cust.setStatus(2);
        }
        if (StrUtil.isNotEmpty(cust.getProvinceCityDistrictName())) {
            String str = cust.getProvinceCityDistrictName();
            str = str.replace("/", "->");
            cust.setProvinceCityDistrictName(str);
        }
        //合同
        List<ContractInfoVo> contractInfoList = contractInfoMapper.getInfoList(id);
        cust.setContractInfoList(contractInfoList);
        //联系人
        ContactsInfoExportReqVO contactsInfoExportReqVO = new ContactsInfoExportReqVO();
        contactsInfoExportReqVO.setCustId(id);
        List<ContactsInfoDO> contactsInfoDOList = contactsInfoMapper.selectList(contactsInfoExportReqVO);
        cust.setContactsInfoList(contactsInfoDOList);
        //服务存档
        List<ServiceArchiveUpdateReqVO> serviceArchiveBaseVOList = serviceArchiveMapper.getByCustId(id);
        cust.setServiceArchiveList(serviceArchiveBaseVOList);
        //回访记录
        VisitInfoExportReqVO infoExportReqVO = new VisitInfoExportReqVO();
        infoExportReqVO.setCustId(id);
        List<VisitInfoBaseVO> visitInfoDOList = visitInfoMapper.getByCustIdList(id);
        cust.setVisitInfoList(visitInfoDOList);
        return cust;
    }

    @Override
    public List<CustInfoDO> getCustInfoList(Collection<String> ids) {
        return custInfoMapper.selectBatchIds(ids);
    }

    @Override
    @TenantIgnore
    public PageResult<CustInfoPageReqVO> getCustInfoPage(CustInfoPageReqVO pageReqVO) {
        Integer pageNo = pageReqVO.getPageNo();
        Integer pageSize = pageReqVO.getPageSize();
        Page page = new Page();
        page.setCurrent(pageNo);
        page.setSize(pageSize);
        if (StringUtil.isNotEmpty(pageReqVO.getSortBy())){
            String regex = "([a-z])([A-Z]+)";
            String replacement = "$1_$2";
            String output = pageReqVO.getSortBy().replaceAll(regex, replacement).toLowerCase();
            pageReqVO.setSortBy(output);
        }
        List<CustInfoPageReqVO> list = custInfoMapper.selectCustInfoPage(page, pageReqVO);

        for (CustInfoPageReqVO cust : list) {
            if (cust.getServiceStartDate() == null && cust.getServiceEndDate() == null) {
                cust.setSurplusDayCount(0);
            } else {
                long startDiff = DateUtils.diff(DateUtils.toDate(cust.getServiceStartDate()), new Date());
                long endDiff = DateUtils.diff(DateUtils.toDate(cust.getServiceEndDate()), new Date());
                if (startDiff > 0) {
                    cust.setSurplusDayCount(0);
                    cust.setStatus(0);
                } else if (startDiff < 0 && endDiff > 0) {
                    cust.setSurplusDayCount((int) Math.ceil(endDiff / (24 * 60 * 60 * 1000)));
                    cust.setStatus(1);
                } else {
                    cust.setSurplusDayCount(0);
                    cust.setStatus(2);
                }
            }
            if (StrUtil.isNotEmpty(cust.getProvinceCityDistrictName())) {
                String str = cust.getProvinceCityDistrictName();
                str = str.replace("/", "->");
                cust.setProvinceCityDistrictName(str);
            }
        }
        PageResult<CustInfoPageReqVO> result = new PageResult<CustInfoPageReqVO>().setList(list).setTotal(page.getTotal());
        return result;
    }

    @Override
    public List<CustInfoDO> getCustInfoList(CustInfoExportReqVO exportReqVO) {
        return custInfoMapper.selectList(exportReqVO);
    }

    @Override
    public Long getCustInfoNum(String custId) {
        return custInfoMapper.selectCustInfoNum(custId);
    }

    @Override
    public List<CustInfoDO> getCustInfoeSlectList() {
        return custInfoMapper.selectList();
    }

    @Override
    public String createTestCustInfo(Long tenantId, String tenantName, String expireTime) {
        CustInfoCreateReqVO custInfoCreateReqVO = new CustInfoCreateReqVO();
        custInfoCreateReqVO.setCustId(UuidUtils.generateUuid());
        custInfoCreateReqVO.setCustName("租户" + tenantName + "测试客户");
        String industry = dictDataApi.parseDictData("cust_industry", "其他").getData().getValue();
        custInfoCreateReqVO.setIndustry(industry);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        custInfoCreateReqVO.setServiceStartDate(sdf.format(new Date()));
        custInfoCreateReqVO.setServiceEndDate(expireTime);
        CustInfoDO custInfoDO = CustInfoConvert.INSTANCE.convert(custInfoCreateReqVO);
        custInfoDO.setTenantId(tenantId);
        custInfoMapper.insert(custInfoDO);
        return custInfoCreateReqVO.getCustId();
    }

    @Override
    @TenantIgnore
    public void updateExpireTime(String expireTime, String custId) {
        // 校验存在
        validateCustInfoExists(custId);
        // 更新
        CustInfoDO updateObj = custInfoMapper.selectByCustId(custId);
        updateObj.setServiceEndDate(expireTime);
        custInfoMapper.updateByCustId(updateObj);
    }

    @Override
    @TenantIgnore
    public List<Map> getCustomersList() {
        List<Map> list = custInfoMapper.getCustomersList();
        if (!list.isEmpty()) {
            for (Map map : list) {
                long startDiff = DateUtils.diff(DateUtils.toDate(Convert.toStr(map.get("service_start_date"))), new Date());
                long endDiff = DateUtils.diff(DateUtils.toDate(Convert.toStr(map.get("service_end_date"))), new Date());
                if (startDiff > 0) {
                    //0是未开始
                    map.put("status", 0);
                } else if (startDiff < 0 && endDiff > 0) {
                    // 1是服务中
                    map.put("status", 1);
                } else {
                    // 2是已结束
                    map.put("status", 2);
                }
            }
        }
        return list;
    }

    @Override
    @TenantIgnore
    public List<Map> customerIndustry() {
        return custInfoMapper.customerIndustry();
    }

}
