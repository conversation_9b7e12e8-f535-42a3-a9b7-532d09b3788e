package cn.iocoder.zj.module.customer.service.contactsinfo;

import java.util.*;
import javax.validation.*;
import cn.iocoder.zj.module.customer.controller.admin.contactsinfo.vo.*;
import cn.iocoder.zj.module.customer.dal.dataobject.contactsinfo.ContactsInfoDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 客户联系人信息 Service 接口
 *
 * <AUTHOR>
 */
public interface ContactsInfoService {

    /**
     * 创建客户联系人信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createContactsInfo(@Valid ContactsInfoCreateReqVO createReqVO);

    /**
     * 更新客户联系人信息
     *
     * @param updateReqVO 更新信息
     */
    void updateContactsInfo(@Valid ContactsInfoUpdateReqVO updateReqVO);

    /**
     * 删除客户联系人信息
     *
     * @param id 编号
     */
    void deleteContactsInfo(String id);

    /**
     * 获得客户联系人信息
     *
     * @param id 编号
     * @return 客户联系人信息
     */
    ContactsInfoDO getContactsInfo(String id);

    /**
     * 获得客户联系人信息列表
     *
     * @param ids 编号
     * @return 客户联系人信息列表
     */
    List<ContactsInfoDO> getContactsInfoList(Collection<String> ids);

    /**
     * 获得客户联系人信息分页
     *
     * @param pageReqVO 分页查询
     * @return 客户联系人信息分页
     */
    PageResult<ContactsInfoDO> getContactsInfoPage(ContactsInfoPageReqVO pageReqVO);

    /**
     * 获得客户联系人信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 客户联系人信息列表
     */
    List<ContactsInfoDO> getContactsInfoList(ContactsInfoExportReqVO exportReqVO);

    /**
     * 批量创建客户联系人信息
     *
     * @param createReqVOList 创建信息
     * @return 编号
     */
    Boolean createContactsInfoList(@Valid List<ContactsInfoCreateReqVO> createReqVOList);

}
