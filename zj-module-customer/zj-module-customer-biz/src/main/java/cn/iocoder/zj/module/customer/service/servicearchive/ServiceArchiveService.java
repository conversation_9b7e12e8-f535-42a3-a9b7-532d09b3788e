package cn.iocoder.zj.module.customer.service.servicearchive;

import java.util.*;
import javax.validation.*;
import cn.iocoder.zj.module.customer.controller.admin.servicearchive.vo.*;
import cn.iocoder.zj.module.customer.dal.dataobject.servicearchive.ServiceArchiveDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 服务存档信息 Service 接口
 *
 * <AUTHOR>
 */
public interface ServiceArchiveService {

    /**
     * 创建服务存档信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createServiceArchive(@Valid ServiceArchiveCreateReqVO createReqVO);

    /**
     * 更新服务存档信息
     *
     * @param updateReqVO 更新信息
     */
    void updateServiceArchive(@Valid ServiceArchiveUpdateReqVO updateReqVO);

    /**
     * 删除服务存档信息
     *
     * @param id 编号
     */
    void deleteServiceArchive(Integer id);

    /**
     * 获得服务存档信息
     *
     * @param id 编号
     * @return 服务存档信息
     */
    ServiceArchiveDO getServiceArchive(Integer id);

    /**
     * 获得服务存档信息列表
     *
     * @param ids 编号
     * @return 服务存档信息列表
     */
    List<ServiceArchiveDO> getServiceArchiveList(Collection<Integer> ids);

    /**
     * 获得服务存档信息分页
     *
     * @param pageReqVO 分页查询
     * @return 服务存档信息分页
     */
    PageResult<ServiceArchivePageReqVO> getServiceArchivePage(ServiceArchivePageReqVO pageReqVO);

    /**
     * 获得服务存档信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 服务存档信息列表
     */
    List<ServiceArchiveDO> getServiceArchiveList(ServiceArchiveExportReqVO exportReqVO);

}
