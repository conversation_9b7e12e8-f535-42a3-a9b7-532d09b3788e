package cn.iocoder.zj.module.customer.dal.dataobject.contactsinfo;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 客户联系人信息 DO
 *
 * <AUTHOR>
 */
@TableName("customer_contacts_info")
@KeySequence("customer_contacts_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContactsInfoDO extends BaseDO {

    /**
     * 联系人id
     */
    @TableId
    private Integer id;
    /**
     * 联系人名称
     */
    private String contactsName;
    /**
     * 联系人手机号
     */
    private String contactsTelephone;
    /**
     * 所属客户ID
     */
    private String custId;
    /**
     * 地区id
     */
    private Long regionId;
    /**
     * 职务
     */
    private String contactsPosition;
    /**
     * 部门
     */
    private String contactsDept;

}
