package cn.iocoder.zj.module.customer.controller.admin.visitinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 客户回访信息更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VisitInfoUpdateReqVO extends VisitInfoBaseVO {

    @Schema(description = "回访信息id", required = true)
    @NotNull(message = "回访信息id不能为空")
    private Integer id;

}
