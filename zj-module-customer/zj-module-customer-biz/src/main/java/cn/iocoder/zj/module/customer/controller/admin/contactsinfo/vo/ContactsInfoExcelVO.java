package cn.iocoder.zj.module.customer.controller.admin.contactsinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 客户联系人信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ContactsInfoExcelVO {

    @ExcelProperty("联系人id")
    private String id;

    @ExcelProperty("联系人名称")
    private String contactsName;

    @ExcelProperty("联系人手机号")
    private String contactsTelephone;

    @ExcelProperty("所属客户ID")
    private String custId;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("地区id")
    private Long regionId;

}
