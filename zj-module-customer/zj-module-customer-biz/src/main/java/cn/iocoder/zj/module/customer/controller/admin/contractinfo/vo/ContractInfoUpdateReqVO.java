package cn.iocoder.zj.module.customer.controller.admin.contractinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 合同信息更新 Request VO")
@Data
public class ContractInfoUpdateReqVO extends ContractInfoBaseVO {

    @Schema(description = "合同ID", required = true)
    @NotNull(message = "合同ID不能为空")
    private String contractId;

}
