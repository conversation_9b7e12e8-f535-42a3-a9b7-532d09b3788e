package cn.iocoder.zj.module.customer.controller.admin.servicearchive.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 服务存档信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ServiceArchiveRespVO extends ServiceArchiveBaseVO {

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

    @Schema(description = "服务存档id", required = true)
    private Integer id;

}
