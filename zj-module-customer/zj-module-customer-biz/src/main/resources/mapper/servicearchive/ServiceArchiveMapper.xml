<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.customer.dal.mysql.servicearchive.ServiceArchiveMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getPage"
            resultType="cn.iocoder.zj.module.customer.controller.admin.servicearchive.vo.ServiceArchivePageReqVO" parameterType="com.baomidou.mybatisplus.extension.plugins.pagination.Page">
        SELECT
            archive.*,
            users.username AS creatorName,
            DATE_FORMAT(archive.update_time, '%Y-%m-%d %H:%i:%s') AS uploadTime
        FROM
            customer_service_archive archive
                LEFT JOIN system_users users ON archive.creator = users.id
            where  1=1
            AND archive.contract_id = #{reqVO.contractId}
            and archive.deleted = 0
        Order by
        <if test="reqVO.sortBy == null or reqVO.sortBy =='' or reqVO.sortDirection== null or reqVO.sortDirection ==''">
            update_time desc
        </if>
        <if test="reqVO.sortBy != null and reqVO.sortBy !='' and reqVO.sortDirection!= null and reqVO.sortDirection !=''">
            ${reqVO.sortBy} ${reqVO.sortDirection}
        </if>
    </select>
    <select id="getByCustId"
            resultType="cn.iocoder.zj.module.customer.controller.admin.servicearchive.vo.ServiceArchiveUpdateReqVO">
        SELECT
            archive.id,
            archive.archive_title,
            archive.file_url,
            archive.contract_id,
            archive.create_time,
            archive.remarks,
            archive.update_time,
            archive.file_path,
            archive.file_url,
            users.username AS creatorName,
            contract.project_name AS contractName
        FROM
            customer_service_archive archive
                LEFT JOIN system_users users ON archive.creator = users.id
                LEFT JOIN customer_contract_info contract ON archive.contract_id = contract.contract_id
        WHERE
                archive.contract_id IN ( SELECT CONTRACT_ID FROM customer_contract_info WHERE cust_id = #{custId} AND deleted = 0 )
          AND archive.deleted =0
    </select>
</mapper>
