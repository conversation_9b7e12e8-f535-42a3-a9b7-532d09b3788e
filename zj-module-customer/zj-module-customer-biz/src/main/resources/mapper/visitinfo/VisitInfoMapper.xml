<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.customer.dal.mysql.visitinfo.VisitInfoMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getVisitInfoPage" resultType="cn.iocoder.zj.module.customer.controller.admin.visitinfo.vo.VisitInfoRespVO">
        SELECT visit.*,users.nickname createName
        FROM
        customer_visit_info visit
        LEFT JOIN system_users users ON visit.creator = users.id
        where visit.deleted = 0
        <if test="pageReqVO.custName != null and pageReqVO.custName !=''">
            and visit.cust_name like concat("%",#{pageReqVO.custName},"%")
        </if>
        <if test="pageReqVO.createName != null and pageReqVO.createName !=''">
            and users.nickname like concat("%",#{pageReqVO.createName},"%")
        </if>
        <if test="pageReqVO.startTime != '' and pageReqVO.endTime != ''and pageReqVO.startTime != null and pageReqVO.endTime != null">
            and DATE_FORMAT( visit.update_time, "%Y-%m-%d %H:%i:%s" )  between DATE_FORMAT( #{pageReqVO.startTime}, "%Y-%m-%d %H:%i:%s" ) and  DATE_FORMAT( #{pageReqVO.endTime}, "%Y-%m-%d %H:%i:%s" )
        </if>
        ORDER BY
        <if test="pageReqVO.sortBy == null or pageReqVO.sortBy =='' or pageReqVO.sortDirection== null or pageReqVO.sortDirection ==''">
            visit.create_time DESC
        </if>
        <if test="pageReqVO.sortBy != null and pageReqVO.sortBy !='' and pageReqVO.sortDirection!= null and pageReqVO.sortDirection !=''">
            visit.${pageReqVO.sortBy} ${pageReqVO.sortDirection}
        </if>
    </select>

    <select id="getByCustIdList"
            resultType="cn.iocoder.zj.module.customer.controller.admin.visitinfo.vo.VisitInfoBaseVO">
        SELECT
            info.*,
            users.username AS contractName
        FROM
            customer_visit_info info
                LEFT JOIN system_users users ON info.creator = users.id
        WHERE
            info.cust_id = #{custId}
    </select>
</mapper>
