<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.customer.dal.mysql.custinfo.CustInfoMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id="selectCustInfoPage"
            resultType="cn.iocoder.zj.module.customer.controller.admin.custinfo.vo.CustInfoPageReqVO"
            parameterType="com.baomidou.mybatisplus.extension.plugins.pagination.Page">
        SELECT
        cust.CUST_ID AS custId,
        cust.CUST_NAME AS custName,
        cust.PROVINCE_CITY_DISTRICT_NAME AS provinceCityDistrictName,
        cust.SERVICE_END_DATE AS serviceEndDate,
        cust.PROVINCE_CITY_DISTRICT_CODE AS provinceCityDistrictCode,
        cust.SERVICE_START_DATE AS serviceStartDate,
        cust.REGION_ID AS regionId,
        TIMESTAMPDIFF( DAY, now( ), cust.service_end_date ) AS surplus_day_count,
        dict.label AS industry,
        contacts.contacts_name AS contacts,
        contacts.contacts_telephone AS contactsPhone
        FROM
        customer_cust_info cust
        LEFT JOIN ( SELECT label, VALUE FROM system_dict_data WHERE dict_type = 'cust_industry' AND deleted = 0 ) dict ON ifnull(cust.INDUSTRY,'other') = dict.VALUE
        LEFT JOIN ( SELECT * FROM customer_contacts_info GROUP BY cust_id ) contacts ON cust.CUST_ID = contacts.cust_id
        WHERE
        1 = 1
        <if test="reqVO.custName!= null and reqVO.custName!=''">
            AND cust.CUST_NAME like concat("%",#{reqVO.custName},"%")
        </if>
        <if test="reqVO.industry!= null and reqVO.industry!=''">
            AND (cust.INDUSTRY = #{reqVO.industry}
            <if test="reqVO.industry == 'other'">
                or cust.INDUSTRY is null
            </if>
            )
        </if>
        <if test="reqVO.provinceCityDistrictCode!= null and reqVO.provinceCityDistrictCode!=''">
            AND cust.province_city_district_code like concat("%",#{reqVO.provinceCityDistrictCode},"%")
        </if>
        <if test="reqVO.status!= null">
            <if test="reqVO.status == 0">
                AND STR_TO_DATE(cust.SERVICE_START_DATE, '%Y-%m-%d %H:%M:%S') >  now()
            </if>
            <if test="reqVO.status == 1">
                AND  now() >= STR_TO_DATE(cust.SERVICE_START_DATE, '%Y-%m-%d %H:%M:%S')
                AND STR_TO_DATE(cust.SERVICE_END_DATE, '%Y-%m-%d %H:%M:%S') >=  now()
            </if>
            <if test="reqVO.status == 2">
                AND now() > STR_TO_DATE(cust.SERVICE_END_DATE, '%Y-%m-%d %H:%M:%S')
            </if>
        </if>
        <if test="reqVO.serviceStartDate!= null and reqVO.serviceStartDate!=''">
            <if test="reqVO.serviceEndDate!= null and reqVO.serviceEndDate!=''">
                and (STR_TO_DATE(cust.SERVICE_START_DATE, '%Y-%m-%d') BETWEEN STR_TO_DATE(#{reqVO.serviceStartDate}, '%Y-%m-%d')
                AND STR_TO_DATE(#{reqVO.serviceEndDate}, '%Y-%m-%d'))
                AND (STR_TO_DATE(cust.SERVICE_END_DATE, '%Y-%m-%d') BETWEEN STR_TO_DATE(#{reqVO.serviceStartDate}, '%Y-%m-%d')
                AND STR_TO_DATE(#{reqVO.serviceEndDate}, '%Y-%m-%d'))
            </if>
        </if>
        AND cust.deleted = 0
        GROUP BY
        cust.CUST_ID
        order by
        <if test="reqVO.sortBy == null or reqVO.sortBy =='' or reqVO.sortDirection== null or reqVO.sortDirection ==''">
            cust.create_time DESC
        </if>
        <if test="reqVO.sortBy != null and reqVO.sortBy !='' and reqVO.sortDirection!= null and reqVO.sortDirection !=''">
            ${reqVO.sortBy} ${reqVO.sortDirection}
        </if>

    </select>
    <select id="getByIndustryId" resultType="java.lang.String">
        SELECT
            dict.label,
            dict.
                VALUE

        FROM
            system_dict_data dict
        WHERE
            dict.dict_type = 'cust_industry'
          AND dict.STATUS = 0
          AND dict.deleted = 0
          AND dict.VALUE=#{industry}
    </select>

    <delete id="deleteTestByCustId">
        delete from customer_cust_info where cust_id=#{custId}
    </delete>

    <select id="getCustomersList" resultType="java.util.Map">
        SELECT cci.cust_id as custId,cci.cust_name as custName,service_start_date,service_end_date, ifnull(sum(asset_num),0) as assetNum from
            customer_cust_info cci
                LEFT JOIN customer_contract_info ci on cci.cust_id = ci.cust_id
        WHERE cci.deleted = 0
        GROUP BY cci.cust_id
        ORDER BY service_start_date desc
    </select>

    <select id="customerIndustry" resultType="java.util.Map">
        SELECT industry,label,count as value  FROM (SELECT ifnull(industry,"other") industry,count(*) count FROM customer_cust_info  cc WHERE deleted = 0
                                           GROUP BY industry) cc
                                              LEFT JOIN system_dict_data sdd on sdd.value = cc.industry
        GROUP BY industry
        ORDER BY count desc
    </select>
</mapper>
