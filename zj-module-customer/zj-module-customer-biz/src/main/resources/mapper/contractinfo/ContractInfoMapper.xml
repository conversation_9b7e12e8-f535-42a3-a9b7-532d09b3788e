<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.customer.dal.mysql.contractinfo.ContractInfoMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getInfoList"
            resultType="cn.iocoder.zj.module.customer.controller.admin.contractinfo.vo.ContractInfoVo">
        SELECT
            CUST_ID as custId,
            contract_id as  contractId,
            contract_no as contractNo,
            PROJECT_NAME as projectName,
            service_tag as serviceTag
        FROM
            customer_contract_info
        WHERE
            deleted =0 and cust_id=#{custId}
    </select>
    <delete id="revokeByContractId">
        update customer_contract_info set deleted=1 where contract_id=#{contractId}
    </delete>

    <select id="getAuditContractIds" resultType="string">
        select contract_id from customer_contract_info where deleted=0 and status=0
    </select>

    <update id="updateStatusByContractIds">
        update customer_contract_info set status=1 where deleted=0 and status=0
        <if test="contractIds !=null and contractIds.size()>0">
            and contract_id in
            <foreach collection="contractIds" open="(" separator="," close=")" item="list">
                #{list}
            </foreach>
        </if>
    </update>

    <update id="updateStatus">
        update customer_contract_info set status=2 where deleted=0
            and contract_id = #{contractId}
    </update>

    <select id="selectDeletedContractInfoNum" resultType="long">
        select count(*) from customer_contract_info where contract_id=#{contractInfo.contractId} and deleted=1;
    </select>

    <update id="updateByContractId">
        update customer_contract_info
        set deleted=0,cust_id=#{contractInfo.custId},cust_name=#{contractInfo.custName},project_name=#{contractInfo.projectName},sign_date=#{contractInfo.signDate},service_end=#{contractInfo.serviceEnd}
        where contract_id = #{contractInfo.contractId} and deleted=1
    </update>

    <update id="updateByContractNo">
        update customer_contract_info
        set tenant_id=#{contractInfo.tenantId}
        where contract_id = #{contractInfo.contractId} and deleted=0
    </update>

    <select id="getContractNo" resultType="java.lang.String">
        SELECT MAX(RIGHT(CONTRACT_NO,3) )
        FROM customer_contract_info
        WHERE YEAR(CREATE_TIME ) = YEAR(NOW())
        and is_test=0
    </select>

    <select id="getContractNos" resultType="map">
        SELECT contract_no,CONCAT( project_name, '(', cust_name, ')' ) project_name
        FROM customer_contract_info
        WHERE deleted = 0
        and is_test=1
        and status = 1
        AND DATE_FORMAT( service_end, "%Y-%m-%d" ) >= CURDATE()
    </select>

    <select id="getTestContracts" resultType="cn.iocoder.zj.module.customer.dal.dataobject.contractinfo.ContractInfoDO">
        select * from customer_contract_info where tenant_id=#{tenantId} and is_test=0
    </select>

    <delete id="deleteTestContract">
        delete from customer_contract_info where tenant_id=#{tenantId} and is_test=0
    </delete>

    <update id="tenantDelete">
        update customer_contract_info
        set tenant_id=1,asset_num='',service_tag='',enforcer_id='',enforcer_name='',file_path='',file_size='',file_name='',file_url=''
        where tenant_id = #{tenantId}
    </update>

    <select id="getContractList" resultType="java.lang.Long">
        select sum(asset_num) num from customer_contract_info where tenant_id = #{tenantId}
        GROUP BY tenant_id
    </select>
</mapper>
