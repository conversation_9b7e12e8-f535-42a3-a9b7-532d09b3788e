package cn.iocoder.zj.module.customer.api.contractInfo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName : ContractInfoDTO  //类名
 * @Description : 合同信息  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/1/30  14:32
 */
@Schema(description = "RPC 服务 - 合约信息数据 Response DTO")
@Data
public class ContractInfoDTO {
    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Long tenantId;
    /**
     * 资产授权数量
     */
    @Schema(description = "资产授权数量")
    private String assetNum;
}
