package cn.iocoder.zj.module.customer.enums;

import cn.iocoder.zj.framework.common.exception.ErrorCode;

/**
 * System 错误码枚举类
 * <p>
 * system 监控，使用 6-002-000-000 段
 */
public interface ErrorCodeConstants {

    ErrorCode CONTACTS_INFO_NOT_EXISTS = new ErrorCode(9-001-000-000, "客户联系人信息不存在");
    ErrorCode CONTACTS_TELEPHONE_NOT_EXISTS = new ErrorCode(9-001-000-001, "手机号信息错误");
    ErrorCode CONTACTS_INFO_EXISTS = new ErrorCode(9-001-000-002, "客户联系人信息已存在");

    ErrorCode CONTRACT_INFO_NOT_EXISTS = new ErrorCode(9-002-000-000, "合同信息不存在");

    ErrorCode CUST_INFO_NOT_EXISTS = new ErrorCode(9-003-000-000, "客户信息不存在");

    ErrorCode SERVICE_ARCHIVE_NOT_EXISTS = new ErrorCode(9-004-000-000, "服务存档信息不存在");

    ErrorCode VISIT_INFO_NOT_EXISTS = new ErrorCode(9-005-000-000, "客户回访信息不存在");
}
