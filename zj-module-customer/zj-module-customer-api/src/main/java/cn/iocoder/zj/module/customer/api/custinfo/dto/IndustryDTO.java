package cn.iocoder.zj.module.customer.api.custinfo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName : IndustryDTO  //类名
 * @Description : 客户行业分布  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/3/25  14:17
 */
@Schema(description = "RPC 服务 - 客户行业分布 Response DTO")
@Data
public class IndustryDTO {
    @Schema(description = "行业名称")
    private String label;
    @Schema(description = "行业数量")
    private String value;
}

