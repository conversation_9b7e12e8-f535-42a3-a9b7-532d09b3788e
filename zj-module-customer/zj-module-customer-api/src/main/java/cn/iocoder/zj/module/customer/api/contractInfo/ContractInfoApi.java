package cn.iocoder.zj.module.customer.api.contractInfo;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.customer.api.contractInfo.dto.ContractInfoDTO;
import cn.iocoder.zj.module.customer.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

@FeignClient(name = ApiConstants.NAME) // ① @FeignClient 注解
@Tag(name = "RPC 服务 - 客户合同相关") // ② Swagger 接口文档
public interface ContractInfoApi {
    String PREFIX = ApiConstants.PREFIX + "/zstack";

    @PostMapping(PREFIX + "/updateStatus") // ③ Spring MVC 接口注解
    @Operation(summary = "修改合同状态")  // ② Swagger 接口文档
    CommonResult<Boolean> updateStatus(@RequestParam("contractId") String contractId);

    @PostMapping(PREFIX + "/createTestContract") // ③ Spring MVC 接口注解
    @Operation(summary = "创建测试合同")  // ② Swagger 接口文档
    CommonResult<Boolean> createTestContract(@RequestParam("tenantId") Long tenantId,@RequestParam("tenantName") String tenantName,@RequestParam("expireTime") String expireTime);

    @PostMapping(PREFIX + "/formalContract") // ③ Spring MVC 接口注解
    @Operation(summary = "新建租户绑定正式合同")  // ② Swagger 接口文档
    CommonResult<Boolean> formalContract(@RequestParam("tenantId") Long tenantId,@RequestParam("contractNo") String contractNo);

    @PostMapping(PREFIX + "/transferContract") // ③ Spring MVC 接口注解
    @Operation(summary = "测试合同转为正式合同")  // ② Swagger 接口文档
    CommonResult<Boolean> transferContract(@RequestParam("tenantId") Long tenantId,@RequestParam("contractNo") String contractNo);

    @PostMapping(PREFIX + "/updateContract") // ③ Spring MVC 接口注解
    @Operation(summary = "租户正式合同修改")  // ② Swagger 接口文档
    CommonResult<Boolean> updateContract(@RequestParam("tenantId") Long tenantId,@RequestParam("contractNo") String contractNo);

    @PostMapping(PREFIX + "/tenantDelete") // ③ Spring MVC 接口注解
    @Operation(summary = "租户删除同步修改对应合同")  // ② Swagger 接口文档
    CommonResult<Boolean> tenantDelete(@RequestParam("tenantId") Long tenantId);

    @PostMapping(PREFIX + "/updateExpireTime") // ③ Spring MVC 接口注解
    @Operation(summary = "租户正式合同修改")  // ② Swagger 接口文档
    CommonResult<Boolean> updateExpireTime(@RequestParam("expireTime") String expireTime,@RequestParam("contractNo") String contractNo);

    @PostMapping(PREFIX + "/getContractInfoList") // ③ Spring MVC 接口注解
    @Operation(summary = "获取租户合同信息")  // ② Swagger 接口文档
    CommonResult<Long> getContractInfoList(@RequestParam("tenantId") Long tenantId);


}
