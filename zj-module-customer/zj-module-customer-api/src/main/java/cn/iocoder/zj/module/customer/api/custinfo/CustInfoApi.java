package cn.iocoder.zj.module.customer.api.custinfo;


import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.customer.api.custinfo.dto.CustinfoDTO;
import cn.iocoder.zj.module.customer.api.custinfo.dto.IndustryDTO;
import cn.iocoder.zj.module.customer.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(name = ApiConstants.NAME) // ① @FeignClient 注解
@Tag(name = "RPC 服务 - 客户信息相关") // ② Swagger 接口文档
public interface CustInfoApi {
    String PREFIX = ApiConstants.PREFIX + "/custinfo";

    @PostMapping(PREFIX + "/getCustomersList") // ③ Spring MVC 接口注解
    @Operation(summary = "查询客户信息一览")  // ② Swagger 接口文档
    CommonResult<List<Map>> getCustomersList();

    @PostMapping(PREFIX + "/customerIndustry") // ③ Spring MVC 接口注解
    @Operation(summary = "客户行业分布")  // ② Swagger 接口文档
    CommonResult<List<Map>> customerIndustry();
}
