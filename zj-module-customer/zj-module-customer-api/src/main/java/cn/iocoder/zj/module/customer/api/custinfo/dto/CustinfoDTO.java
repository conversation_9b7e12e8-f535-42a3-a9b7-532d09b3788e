package cn.iocoder.zj.module.customer.api.custinfo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName : CustinfoDTO  //类名
 * @Description : 大屏客户基础信息一览  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/3/25  10:30
 */
@Schema(description = "RPC 服务 - 客户信息 Response DTO")
@Data
public class CustinfoDTO {
    @Schema(description = "客户id")
    private String custId;
    @Schema(description = "客户名称")
    private String custName;
    @Schema(description = "合同开始时间")
    private Date serviceStartDate;
    @Schema(description = "合同结束时间")
    private Date serviceEndDate;
    @Schema(description = "资产授权数量")
    private String assetNum;
    @Schema(description = "客户状态")
    private String status;
}
