<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.iocoder.cloud</groupId>
    <artifactId>zj-dependencies</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <name>${project.artifactId}</name>
    <description>基础 bom 文件，管理整个项目的依赖版本</description>
    <url>https://github.com/YunaiV/ruoyi-vue-pro</url>

    <properties>
        <revision>1.7.2-snapshot</revision>
        <!-- 统一依赖管理 -->
        <spring.boot.version>2.7.14</spring.boot.version>
        <spring.cloud.version>2021.0.5</spring.cloud.version>
        <spring.cloud.alibaba.version>2021.0.4.0</spring.cloud.alibaba.version>
        <!-- Web 相关 -->
        <servlet.versoin>2.5</servlet.versoin>
        <swagger.version>2.2.8</swagger.version>
        <springdoc.version>1.6.15</springdoc.version>
        <knife4j.version>4.1.0</knife4j.version>
        <netty.version>4.1.100.Final</netty.version>
        <!-- DB 相关 -->
        <druid.version>1.2.16</druid.version>
        <mybatis-plus.version>*******</mybatis-plus.version>
        <mybatis-plus-generator.version>*******</mybatis-plus-generator.version>
        <dynamic-datasource.version>3.6.1</dynamic-datasource.version>
        <redisson.version>3.18.0</redisson.version>
        <jest.version>3.2.5.RELEASE</jest.version>
        <!-- 消息队列 -->
        <rocketmq-spring.version>2.2.3</rocketmq-spring.version>

        <!-- RPC 相关 -->
        <dubbo.version>2.7.18</dubbo.version>
        <!-- Config 配置中心相关 -->
        <apollo.version>1.9.2</apollo.version>
        <!-- Job 定时任务相关 -->
        <xxl-job.version>2.3.1</xxl-job.version>
        <!-- 服务保障相关 -->
        <lock4j.version>2.2.3</lock4j.version>
        <resilience4j.version>1.7.1</resilience4j.version>
        <!-- 监控相关 -->
        <skywalking.version>8.12.0</skywalking.version>
        <spring-boot-admin.version>2.7.10</spring-boot-admin.version>
        <opentracing.version>0.33.0</opentracing.version>
        <!-- Test 测试相关 -->
        <podam.version>7.2.11.RELEASE</podam.version>
        <jedis-mock.version>1.0.7</jedis-mock.version>
        <mockito-inline.version>4.11.0</mockito-inline.version>
        <!-- Bpm 工作流相关 -->
        <flowable.version>6.8.0</flowable.version>
        <!-- 工具类相关 -->
        <captcha-plus.version>1.0.2</captcha-plus.version>
        <jsoup.version>1.15.4</jsoup.version>
        <lombok.version>1.18.30</lombok.version>
        <mapstruct.version>1.5.3.Final</mapstruct.version>
        <hutool.version>5.8.15</hutool.version>
        <easyexcel.verion>3.2.1</easyexcel.verion>
        <velocity.version>2.3</velocity.version>
        <screw.version>1.0.5</screw.version>
        <fastjson.version>1.2.83</fastjson.version>
        <guava.version>32.1.2-jre</guava.version>
        <guice.version>5.1.0</guice.version>
        <transmittable-thread-local.version>2.14.2</transmittable-thread-local.version>
        <commons-net.version>3.8.0</commons-net.version>
        <jsch.version>0.1.55</jsch.version>
        <tika-core.version>2.7.0</tika-core.version>
        <ip2region.version>2.7.0</ip2region.version>
        <reflections.version>0.10.2</reflections.version>
        <!-- 三方云服务相关 -->
        <okio.version>3.0.0</okio.version>
        <okhttp3.version>4.10.0</okhttp3.version>
        <minio.version>8.5.2</minio.version>
        <aliyun-java-sdk-core.version>4.6.3</aliyun-java-sdk-core.version>
        <aliyun-java-sdk-dysmsapi.version>2.2.1</aliyun-java-sdk-dysmsapi.version>
        <tencentcloud-sdk-java.version>3.1.715</tencentcloud-sdk-java.version>
        <justauth.version>1.4.0</justauth.version>
        <jimureport.version>1.5.6</jimureport.version>
        <xercesImpl.version>2.12.2</xercesImpl.version>

        <!--hz相关pom-->

        <p3c-pmd.version>2.1.1</p3c-pmd.version>
        <!--hertzbeat 相关模块-->

        <nekohtml.version>1.9.22</nekohtml.version>
        <checker-qual.version>3.19.0</checker-qual.version>
        <error_prone_annotations.version>2.14.0</error_prone_annotations.version>
        <json-path.version>2.7.0</json-path.version>

        <aviator.version>5.2.7</aviator.version>
        <gson.version>2.8.9</gson.version>
        <protobuf.version>3.19.4</protobuf.version>
        <caffeine.version>2.9.3</caffeine.version>

        <xml.bind.version>2.3.0</xml.bind.version>
        <snake.yaml.version>1.33</snake.yaml.version>
        <kafka-clients.version>3.4.0</kafka-clients.version>

        <mysql.version>8.0.30</mysql.version>
        <mssql-jdbc.version>10.2.0.jre8</mssql-jdbc.version>
        <ojdbc8.version>21.5.0.0</ojdbc8.version>
        <mongodb-driver.version>4.6.1</mongodb-driver.version>
        <clickhouse.version>1.4.4</clickhouse.version>
        <damengc-driver.version>8.1.2.141</damengc-driver.version>
        <postgresql.version>42.5.1</postgresql.version>
        <h2.version>2.1.214</h2.version>
        <taos-jdbcdriver.version>3.0.0</taos-jdbcdriver.version>
        <iotdb-session.version>0.13.3</iotdb-session.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <flatten-maven-plugin.version>1.4.0</flatten-maven-plugin.version>


        <native.version>0.9.28</native.version>

        <greptimedb.version>0.9.1</greptimedb.version>
        <arrow.version>18.1.0</arrow.version>
        <maven-surefire-plugin.version>2.22.2</maven-surefire-plugin.version>

    </properties>


    <dependencyManagement>
        <dependencies>
            <!-- 统一依赖管理 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 业务组件 -->
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-banner</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-biz-operatelog</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-biz-dict</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-biz-sms</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-biz-pay</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-biz-weixin</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-biz-tenant</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-biz-data-permission</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-biz-social</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-biz-error-code</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-biz-ip</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-captcha</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-desensitize</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- Spring 核心 -->
            <dependency>
                <!-- 用于生成自定义的 Spring @ConfigurationProperties 配置类的说明文件 -->
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-env</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- Web 相关 -->
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-web</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-security</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>io.swagger.core.v3</groupId> <!-- 接口文档：使用最新版本的 Swagger 模型 -->
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId> <!-- 接口文档：使用最新版本的 Swagger 模型 -->
                <artifactId>swagger-models</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId> <!-- 接口文档 UI：解决 knife4j 引入的 Spring Doc 版本太老 -->
                <artifactId>springdoc-openapi-common</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId> <!-- 接口文档 UI：解决 knife4j 引入的 Spring Doc 版本太老 -->
                <artifactId>springdoc-openapi-webmvc-core</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId> <!-- 接口文档 UI：解决 knife4j 引入的 Spring Doc 版本太老 -->
                <artifactId>springdoc-openapi-webflux-core</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId> <!-- 接口文档 UI：默认 -->
                <artifactId>springdoc-openapi-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId> <!-- 接口文档 UI：knife4j -->
                <artifactId>knife4j-openapi3-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId> <!-- 接口文档 UI：knife4j【网关专属】 -->
                <artifactId>knife4j-gateway-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <!-- DB 相关 -->
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-mybatis</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency> <!-- TODO 芋艿：说不清楚 -->
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-webflux-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId> <!-- 代码生成器，使用它解析表结构 -->
                <version>${mybatis-plus-generator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId> <!-- 多数据源 -->
                <version>${dynamic-datasource.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-redis</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.vanroy</groupId>
                <artifactId>spring-boot-starter-data-jest</artifactId>
                <version>${jest.version}</version>
            </dependency>

            <!-- RPC 远程调用相关 -->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-common</artifactId> <!-- 兜底，保证在不引入 spring-cloud-starter-dubbo 时，注解等不报错 -->
                <version>${dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-cluster</artifactId> <!-- 兜底，保证在不引入 spring-cloud-starter-dubbo 时，注解等不报错 -->
                <version>${dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-rpc-api</artifactId> <!-- 兜底，保证在不引入 spring-cloud-starter-dubbo 时，注解等不报错 -->
                <version>${dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-rpc</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- Registry 注册中心相关 -->

            <!-- Config 配置中心相关 -->

            <!-- Job 定时任务相关 -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-job</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-influx</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-es</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 消息队列相关 -->
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-mq</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq-spring.version}</version>
            </dependency>

            <!-- 服务保障相关 -->
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-protection</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
                <version>${lock4j.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>redisson-spring-boot-starter</artifactId>
                        <groupId>org.redisson</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-ratelimiter</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-spring-boot2</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>

            <!-- 监控相关 -->
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-monitor</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-trace</artifactId>
                <version>${skywalking.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-logback-1.x</artifactId>
                <version>${skywalking.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-opentracing</artifactId>
                <version>${skywalking.version}</version>
                <!--                <exclusions>-->
                <!--                    <exclusion>-->
                <!--                        <artifactId>opentracing-api</artifactId>-->
                <!--                        <groupId>io.opentracing</groupId>-->
                <!--                    </exclusion>-->
                <!--                    <exclusion>-->
                <!--                        <artifactId>opentracing-util</artifactId>-->
                <!--                        <groupId>io.opentracing</groupId>-->
                <!--                    </exclusion>-->
                <!--                </exclusions>-->
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-api</artifactId>
                <version>${opentracing.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-util</artifactId>
                <version>${opentracing.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-noop</artifactId>
                <version>${opentracing.version}</version>
            </dependency>

            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-server</artifactId> <!-- 实现 Spring Boot Admin Server 服务端 -->
                <version>${spring-boot-admin.version}</version>
            </dependency>
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId> <!-- 实现 Spring Boot Admin Server 服务端 -->
                <version>${spring-boot-admin.version}</version>
            </dependency>

            <!-- Test 测试相关 -->
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-test</artifactId>
                <version>${revision}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>${mockito-inline.version}</version> <!-- 支持 Mockito 的 final 类与 static 方法的 mock -->
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring.boot.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>asm</artifactId>
                        <groupId>org.ow2.asm</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.mockito</groupId>
                        <artifactId>mockito-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.github.fppt</groupId> <!-- 单元测试，我们采用内嵌的 Redis 数据库 -->
                <artifactId>jedis-mock</artifactId>
                <version>${jedis-mock.version}</version>
            </dependency>

            <dependency>
                <groupId>uk.co.jemos.podam</groupId> <!-- 单元测试，随机生成 POJO 类 -->
                <artifactId>podam</artifactId>
                <version>${podam.version}</version>
            </dependency>

            <!-- 工作流相关 -->
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-flowable</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-process</artifactId>
                <version>${flowable.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-actuator</artifactId>
                <version>${flowable.version}</version>
            </dependency>
            <!-- 工作流相关结束 -->

            <!-- 工具类相关 -->
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-common</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-excel</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId> <!-- use mapstruct-jdk8 for Java 8 or higher -->
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-jdk8</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.verion}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-core</artifactId> <!-- 文件类型的识别 -->
                <version>${tika-core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.smallbun.screw</groupId>
                <artifactId>screw-core</artifactId> <!-- 实现数据库文档 -->
                <version>${screw.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.freemarker</groupId>
                        <artifactId>freemarker</artifactId> <!-- 移除 Freemarker 依赖，采用 Velocity 作为模板引擎 -->
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId> <!-- 最新版screw-core1.0.5依赖fastjson1.2.73存在漏洞，移除。 -->
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.inject</groupId>
                <artifactId>guice</artifactId>
                <version>${guice.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId> <!-- 解决 ThreadLocal 父子线程的传值问题 -->
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId> <!-- 解决 ftp 连接 -->
                <version>${commons-net.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jcraft</groupId>
                <artifactId>jsch</artifactId> <!-- 解决 sftp 连接 -->
                <version>${jsch.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xingyuv</groupId>
                <artifactId>spring-boot-starter-captcha-plus</artifactId>
                <version>${captcha-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>

            <dependency>
                <groupId>org.reflections</groupId>
                <artifactId>reflections</artifactId>
                <version>${reflections.version}</version>
            </dependency>

            <!-- 三方云服务相关 -->
            <dependency>
                <groupId>com.squareup.okio</groupId>
                <artifactId>okio</artifactId>
                <version>${okio.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp3.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-file</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>

            <!-- SMS SDK begin -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>${aliyun-java-sdk-core.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>opentracing-api</artifactId>
                        <groupId>io.opentracing</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>opentracing-util</artifactId>
                        <groupId>io.opentracing</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-dysmsapi</artifactId>
                <version>${aliyun-java-sdk-dysmsapi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java-sms</artifactId>
                <version>${tencentcloud-sdk-java.version}</version>
            </dependency>
            <!-- SMS SDK end -->

            <dependency>
                <groupId>com.xkcoding.justauth</groupId>
                <artifactId>justauth-spring-boot-starter</artifactId> <!-- 社交登陆（例如说，个人微信、企业微信等等） -->
                <version>${justauth.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.hutool</groupId>
                        <artifactId>hutool-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 积木报表-->
            <dependency>
                <groupId>org.jeecgframework.jimureport</groupId>
                <artifactId>jimureport-spring-boot-starter</artifactId>
                <version>${jimureport.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>druid</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>xerces</groupId>
                <artifactId>xercesImpl</artifactId>
                <version>${xercesImpl.version}</version>
            </dependency>


            <dependency>
                <groupId>com.toastcoders</groupId>
                <artifactId>yavijava</artifactId>
                <version>6.0.05</version>
            </dependency>
            <dependency>
                <groupId>com.vmware.vim25</groupId>
                <artifactId>vim25</artifactId>
                <version>6.7.0</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.hertzbeat</groupId>
                <artifactId>zj-common-hz</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-hertzbeat-warehouse</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-warehouse</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-hertzbeat-alerter</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-hertzbeat-remoting</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>hertzbeat-collector-collector</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-hertzbeat-push</artifactId>
                <version>${revision}</version>
            </dependency>
            <!-- spring -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-devtools</artifactId>
                <version>${spring-boot-dependencies}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>${xml.bind.version}</version>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snake.yaml.version}</version>
            </dependency>
            <!-- kafka -->
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka-clients.version}</version>
            </dependency>
            <!-- tool -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>${commons-net.version}</version>
            </dependency>
            <!-- mysql -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <!-- sql server -->
            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>mssql-jdbc</artifactId>
                <version>${mssql-jdbc.version}</version>
            </dependency>
            <!-- oracle -->
            <dependency>
                <groupId>com.oracle.database.jdbc</groupId>
                <artifactId>ojdbc8</artifactId>
                <version>${ojdbc8.version}</version>
            </dependency>
            <dependency>
                <groupId>com.oracle.database.nls</groupId>
                <artifactId>orai18n</artifactId>
                <version>${ojdbc8.version}</version>
            </dependency>
            <!-- mongodb -->
            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongodb-driver-sync</artifactId>
                <version>${mongodb-driver.version}</version>
            </dependency>
            <!-- clickhouse -->
            <dependency>
                <groupId>cc.blynk.clickhouse</groupId>
                <artifactId>clickhouse4j</artifactId>
                <version>${clickhouse.version}</version>
            </dependency>
            <!-- dm -->
            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmJdbcDriver18</artifactId>
                <version>${damengc-driver.version}</version>
            </dependency>
            <!-- postgresql -->
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>
            <!-- taos-jdbc driver -->
            <dependency>
                <groupId>com.taosdata.jdbc</groupId>
                <artifactId>taos-jdbcdriver</artifactId>
                <version>${taos-jdbcdriver.version}</version>
            </dependency>
            <!-- IoTDB -->
            <dependency>
                <groupId>org.apache.iotdb</groupId>
                <artifactId>iotdb-session</artifactId>
                <version>${iotdb-session.version}</version>
            </dependency>
            <!-- h2 database-->
            <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>${h2.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>net.sourceforge.nekohtml</groupId>
                <artifactId>nekohtml</artifactId>
                <version>${nekohtml.version}</version>
            </dependency>
            <dependency>
                <groupId>org.checkerframework</groupId>
                <artifactId>checker-qual</artifactId>
                <version>${checker-qual.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.errorprone</groupId>
                <artifactId>error_prone_annotations</artifactId>
                <version>${error_prone_annotations.version}</version>
            </dependency>
            <!--json path parser-->
            <dependency>
                <groupId>com.jayway.jsonpath</groupId>
                <artifactId>json-path</artifactId>
                <version>${json-path.version}</version>
            </dependency>
            <!-- 表达式计算 -->
            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>${aviator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java-util</artifactId>
                <version>${protobuf.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>


            <!-- sms -->

            <!-- caffeine-->
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <!-- 3.x版本支持Java11及以上, 因此这边还是使用2.x的版本-->
                <version>${caffeine.version}</version>
            </dependency>
            <!--  通用集合操作  -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections4.version}</version>
            </dependency>


            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-ssh</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-proxy-core</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-hertzbeat-plugin</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>zj-spring-boot-starter-hertzbeat-grafana</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>4.1.100.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-buffer</artifactId>
                <version>4.1.100.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec</artifactId>
                <version>4.1.100.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-dns</artifactId>
                <version>4.1.100.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-haproxy</artifactId>
                <version>4.1.100.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-http</artifactId>
                <version>4.1.100.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-http2</artifactId>
                <version>4.1.100.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-memcache</artifactId>
                <version>4.1.100.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-mqtt</artifactId>
                <version>4.1.100.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-redis</artifactId>
                <version>4.1.100.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-smtp</artifactId>
                <version>4.1.100.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-socks</artifactId>
                <version>4.1.100.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-stomp</artifactId>
                <version>4.1.100.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-xml</artifactId>
                <version>4.1.100.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-common</artifactId>
                <version>4.1.100.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-handler</artifactId>
                <version>4.1.100.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-handler-proxy</artifactId>
                <version>4.1.100.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-resolver</artifactId>
                <version>4.1.100.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-resolver-dns</artifactId>
                <version>4.1.100.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport</artifactId>
                <version>4.1.100.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport-native-unix-common</artifactId>
                <version>4.1.100.Final</version>
            </dependency>

            <dependency>
                <groupId>org.apache.arrow</groupId>
                <artifactId>arrow-vector</artifactId>
                <version>${arrow.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.arrow</groupId>
                <artifactId>arrow-memory-netty</artifactId>
                <version>${arrow.version}</version>
            </dependency>
            <dependency>
                <groupId>io.lettuce</groupId>
                <artifactId>lettuce-core</artifactId>
                <version>6.3.1.RELEASE</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <distributionManagement>
        <repository>
            <id>nexus</id>
            <name>release</name>
            <url>http://172.16.200.114:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus</id>
            <name>snapshot</name>
            <url>http://172.16.200.114:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <!-- 统一 revision 版本 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                    <updatePomFile>true</updatePomFile>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                    </execution>
                    <execution>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
