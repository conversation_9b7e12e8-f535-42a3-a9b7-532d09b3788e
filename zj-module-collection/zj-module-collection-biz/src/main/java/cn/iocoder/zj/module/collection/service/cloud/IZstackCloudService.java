package cn.iocoder.zj.module.collection.service.cloud;

public interface IZstackCloudService {


    String cloudInfo(String url, String token);

    String eips(String url, String token);

    //-----------------设置---------------------//

    String queryGlobalConfig(String url, String token);

    String resourceConfigList(String url, String token,String uuid);


    String diskReadOps(String url,String token);

    String diskAllReadOps(String url, String token);

    String diskWriteOps(String url, String token);

    String diskAllWriteOps(String url, String token);

    String clusters(String url, String token,String uuid);

    String vmSnapshot(String url, String token, String uuid);
}

