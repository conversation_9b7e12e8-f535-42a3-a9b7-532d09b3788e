package cn.iocoder.zj.module.collection.dal.redis;

import cn.iocoder.zj.module.collection.dal.dataobject.zstack.*;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;




/**
 * System Redis Key 枚举类
 *
 * <AUTHOR>
 */
public interface RedisKeyConstants {

    String ZSTACK_ACCESS_TOKEN = "zstack_access_token:%s";

    String ALARM_CONFIG = "alarm_config:%s";

    String ALARM_HOST_RELATION = "alarm_host_relation:%s";

    String PLATFORM_LIST = "platfrom_list:%s";
    String VMWARE_ACCESS_TOKEN = "vmware_access_token:%s";
    String SANGFOR_ACCESS_TOKEN = "sangFor_access_token:%s";
    String WINHONG_ACCESS_TOKEN = "winhong_access_token:%s";
    String FUSIONONE_ACCESS_TOKEN = "fusionone_access_token:%s";
}
