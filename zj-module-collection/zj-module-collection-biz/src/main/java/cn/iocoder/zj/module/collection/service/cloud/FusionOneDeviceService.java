package cn.iocoder.zj.module.collection.service.cloud;

import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

public interface FusionOneDeviceService {
    //====================================云主机===============================

    JSONArray getClouds(PlatformconfigDTO p, String token,String siteId);

    JSONObject getCloudInfo(PlatformconfigDTO p, String token, String uuid);

    JSONArray getHosts(PlatformconfigDTO p, String token,String siteId);

    JSONObject getHostIfno(PlatformconfigDTO p, String token, String vmID);

    JSONArray getSnapshots(PlatformconfigDTO p, String token, String vmID);

    JSONObject getHostCpuInfo(PlatformconfigDTO p, String token, String ip);

    JSONObject getInfoByVms(PlatformconfigDTO p, String token, String uri);

    JSONObject getMemoryIfno(PlatformconfigDTO p, String token, String computeResourceStatics);

    JSONArray getRelaTimeData(PlatformconfigDTO p, String token,String siteId, String urn);

    JSONObject getVolumes(PlatformconfigDTO p, String token, String siteId, String volUrns);

    JSONArray getCurvedata(PlatformconfigDTO p, String token, String siteId, String urn);

    JSONArray getStorages(PlatformconfigDTO p, String token, String siteId);

    JSONArray getNetWork(PlatformconfigDTO p, String token, String siteId);

    JSONArray getRelaTimeDataNetWork(PlatformconfigDTO p, String token, String siteId, String urn);

    JSONArray getInternetspeed(PlatformconfigDTO p, String token, String siteId, String urn);

    JSONObject getSnapshotsInfo(PlatformconfigDTO platform, String token, String uriSnap);

    JSONObject getVolumeInfo(PlatformconfigDTO platform, String token, String volumeUri);

    JSONObject getImageInfo(PlatformconfigDTO platform, String token, String uri);

    JSONArray getRealtimeData(PlatformconfigDTO p, String token, String siteId, String urn);

    JSONArray getSystemIntFs(PlatformconfigDTO p, String token, String siteId, String urn);

    JSONArray getDvswitchs(PlatformconfigDTO p, String token, String siteId);

    JSONArray getDvswitchInfo(PlatformconfigDTO p, String token, String siteId, String urn);


//    JSONObject getVmDetails(PlatformconfigDTO p, String cookie);
}
