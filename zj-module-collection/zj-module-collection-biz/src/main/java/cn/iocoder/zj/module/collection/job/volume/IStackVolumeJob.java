package cn.iocoder.zj.module.collection.job.volume;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.service.cloud.IStackDeviceService;
import cn.iocoder.zj.module.monitor.api.valume.VolumeApi;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeDTO;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeSnapshotDTO;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Component
@Slf4j
public class IStackVolumeJob {
    @Resource
    private PlatformRedisDAO platformRedisDAO;

    @Resource
    private IStackDeviceService iStackDeviceService;

    @Resource
    VolumeApi volumeApi;

    @XxlJob("getIStackVolumeJob")
    public void getIStackVolumeJob() {

        log.info("[istackImageJob][开始执行 iStack 云盘同步任务]");

        // 1. 获取平台配置
        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        if (ObjectUtil.isEmpty(platformconfigDTOList)) {
            log.warn("[istackImageJob][未获取到平台配置]");
            return;
        }

        // 2. 过滤出 istack 平台
        List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                .filter(dto -> "istack".equals(dto.getTypeCode()))
                .toList();

        if (filteredList.isEmpty()) {
            log.info("[istackImageJob][未找到 istack 平台配置]");
            return;
        }
        List<VolumeDTO> volumeInfoDOS = new ArrayList<>();
        // 3. 遍历平台获取云盘信息
        for (PlatformconfigDTO p : filteredList) {

            log.info("[istackImageJob][开始获取平台({})的云盘列表]", p.getName());

            // 获取云盘列表
            JSONArray diskList = iStackDeviceService.getVmDetail(p, null);
            if (diskList == null || diskList.isEmpty()) {
                log.info("[istackImageJob][平台({})未获取到云盘信息]", p.getName());

            }
            //处理云盘信息
            if (diskList != null) {
                AtomicInteger size = new AtomicInteger();
                diskList.forEach(disk -> {
                    JSONObject jsonObject = (JSONObject) disk;
                    VolumeDTO volumeDTO = convertToVolumeInfo(jsonObject, p);
                    log.info("[istackImageJob][云盘信息:{}]", volumeDTO);
                    log.info("[istackImageJob][第几条:{}]", size.getAndIncrement());
                    volumeInfoDOS.add(volumeDTO);
                });
            }
        }
        if (!volumeInfoDOS.isEmpty()) {
            Long volumeCount = volumeApi.getVolumeCount().getData();
            List<VolumeDTO> dtos = volumeApi.getAllVolumes(-1L, "istack").getData();
            if (volumeCount == 0) {
                volumeApi.addVolumes(volumeInfoDOS);
            } else {
                //比较uuid不存在删除
                List<VolumeDTO> deleteTarget = dtos.stream()
                        .filter(volumeDTO -> volumeInfoDOS.stream()
                                .noneMatch(item -> item.getUuid().equals(volumeDTO.getUuid())))
                        .toList();

                //新增云盘
                List<VolumeDTO> collect = volumeInfoDOS.stream()
                        .filter(volumeDTO -> dtos.stream()
                                .noneMatch(item -> item.getUuid().equals(volumeDTO.getUuid())))
                        .collect(Collectors.toList());
                //修改云盘
                List<VolumeDTO> updateDTOs = volumeInfoDOS.stream()
                        .filter(volumeDTO -> dtos.stream()
                                .anyMatch(item -> item.getUuid().equals(volumeDTO.getUuid())))
                        .toList();

                if (!collect.isEmpty()) {
                    volumeApi.addVolumes(collect);
                }
                if (!updateDTOs.isEmpty()){
                    volumeApi.updateVolumes(updateDTOs);
                }
                if (!deleteTarget.isEmpty()) {
                    volumeApi.delVolumes(deleteTarget);
                }
            }
        }
    }

    private VolumeDTO convertToVolumeInfo(JSONObject jsonObject, PlatformconfigDTO p) {
        VolumeDTO volumeDTO = new VolumeDTO();
        volumeDTO.setDescription("IStack云盘描述");    //描述
        volumeDTO.setName(jsonObject.getString("name"));
        volumeDTO.setFormat(jsonObject.getString("volume_type"));  //云盘格式
        long total = jsonObject.getLong("size") * 1024 * 1024 * 1024;
        volumeDTO.setSize(total);
        volumeDTO.setActualSize(total);
        String type = jsonObject.getString("type");
        JSONObject vmuse = iStackDeviceService.getVmuse(p, jsonObject.getString("instance_uuid"));
        JSONArray rows = vmuse.getJSONObject("pageResult").getJSONArray("rows");
        if (!rows.isEmpty()) {
            JSONObject object = (JSONObject) vmuse.getJSONObject("pageResult").getJSONArray("rows").get(0);
            if (type.equals("ROOT")) {
                String vmVolumeUtil = object.getString("vm_volume_util");
                if (StrUtil.isEmpty(vmVolumeUtil)) {
                    vmVolumeUtil = "0";
                }
                volumeDTO.setActualRatio(vmVolumeUtil);
                volumeDTO.setType("Root");
                BigDecimal diskUtilRatio = new BigDecimal(vmVolumeUtil).divide(BigDecimal.valueOf(100));
                long diskToal = diskUtilRatio.multiply(BigDecimal.valueOf(total)).setScale(0, BigDecimal.ROUND_HALF_UP).longValue();
                volumeDTO.setActualUse(diskToal);
                volumeDTO.setActualFree(total - diskToal);
            } else {
                String vmDiskUtil = object.getString("vm_disk_util_avg");
                volumeDTO.setType("Data");
                if (StrUtil.isEmpty(vmDiskUtil)) {
                    vmDiskUtil = "0";
                }
                volumeDTO.setActualRatio(vmDiskUtil);
                BigDecimal voUtilRatio = new BigDecimal(vmDiskUtil).divide(BigDecimal.valueOf(100));
                ;
                long diskToal = voUtilRatio.multiply(BigDecimal.valueOf(total)).setScale(0, BigDecimal.ROUND_HALF_UP).longValue();
                volumeDTO.setActualUse(diskToal);
                volumeDTO.setActualFree(total - diskToal);
            }
        }else{
            if (type.equals("ROOT")) {
                volumeDTO.setType("Root");
            }else {
                volumeDTO.setType("Data");
            }
            volumeDTO.setActualUse(0L);
            volumeDTO.setActualFree(total);
            volumeDTO.setActualRatio("0");
        }
        String timeStr = jsonObject.getString("create_time") != null ? jsonObject.getString("create_time") : null;
        Instant instant = null;
        if (timeStr != null) {
            instant = Instant.parse(timeStr);
        }
        if (instant != null) {
            Date date1 = Date.from(instant);
            volumeDTO.setVCreateDate(date1);
            volumeDTO.setVUpdateDate(date1);
        }
        volumeDTO.setState("Enabled");
        volumeDTO.setUuid(jsonObject.getString("id"));
        volumeDTO.setStatus("Ready");
        volumeDTO.setPlatformId(p.getId());
        volumeDTO.setPlatformName(p.getName());
        volumeDTO.setVmInstanceUuid(jsonObject.getString("instance_uuid"));
        volumeDTO.setVmInstanceName(jsonObject.getString("instance_name"));
        volumeDTO.setPrimaryStorageUuid(jsonObject.getString("storageid"));
        volumeDTO.setPrimaryStorageName(jsonObject.getString("storage"));
        volumeDTO.setDeleted(0L);
        String hardDiskStatus = jsonObject.getString("harddisk_status");
        if(hardDiskStatus!=null){
            volumeDTO.setIsMount(hardDiskStatus.equals("ATTACHED"));
        }else {
            volumeDTO.setIsMount(null);
        }
        volumeDTO.setMediaType(MediaType.SSD.getEnName());
        return volumeDTO;
    }


    @XxlJob("getIStackSnapshotJob")
    public void getIStackSnapshotJob() {
        log.info("[getIStackSnapshotJob][开始获取istack云盘快照]");
        // 1. 获取平台配置
        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        if (ObjectUtil.isEmpty(platformconfigDTOList)) {
            log.warn("[istackImageJob][未获取到平台配置]");
            return;
        }

        // 2. 过滤出 istack 平台
        List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                .filter(dto -> "istack".equals(dto.getTypeCode()))
                .toList();

        if (filteredList.isEmpty()) {
            log.info("[istackImageJob][未找到 istack 平台配置]");
            return;
        }
        List<VolumeSnapshotDTO> volumeSnapshotDTOS = new ArrayList<>();
        // 3. 遍历平台获取云盘信息
        for (PlatformconfigDTO p : filteredList) {
            //获取云盘快照列表
            JSONArray volumeSnapshotList = iStackDeviceService.getVolumeSnapshotList(p);
            log.info("[istackImageJob][获取到云盘快照信息]" + volumeSnapshotList);
            //获取主机快照列表
            JSONArray instanceSnapshotList = iStackDeviceService.getInstanceSnapshotList(p);
            log.info("[istackImageJob][获取到主机快照信息]" + instanceSnapshotList);

            if (volumeSnapshotList != null && !volumeSnapshotList.isEmpty()) {
                for (Object o : instanceSnapshotList) {
                    JSONObject jsonObject = (JSONObject) o;
                    VolumeSnapshotDTO volumeSnapshotDTO = convertToSnapshotInfo("云盘快照",jsonObject, p);
                    volumeSnapshotDTOS.add(volumeSnapshotDTO);
                    log.info("[istackImageJob][主机快照信息:{}]", volumeSnapshotDTO);
                }
            }
            if(instanceSnapshotList!= null &&!instanceSnapshotList.isEmpty()) {
                for (Object o : instanceSnapshotList) {
                    JSONObject jsonObject = (JSONObject) o;
                    VolumeSnapshotDTO volumeSnapshotDTO = convertToSnapshotInfo("主机快照",jsonObject, p);
                    volumeSnapshotDTOS.add(volumeSnapshotDTO);
                }
            }
        }
        if (!volumeSnapshotDTOS.isEmpty()) {
            Long volumeSnapshotCount = volumeApi.getVolumeSnapshotCount("istack").getData();
            List<VolumeSnapshotDTO> dtos = volumeApi.getAllVolumeSnapshots("istack").getData();
            if (volumeSnapshotCount == 0) {
                volumeApi.addVolumeSnapshots(volumeSnapshotDTOS);
            } else {
                List<VolumeSnapshotDTO> collect = volumeSnapshotDTOS.stream()
                        .filter(item -> !dtos.stream().map(VolumeSnapshotDTO::getUuid)
                                .toList().contains(item.getUuid()))
                        .collect(Collectors.toList());
                List<VolumeSnapshotDTO> deleteTarget = dtos.stream()
                        .filter(item -> !(volumeSnapshotDTOS.stream().map(VolumeSnapshotDTO::getUuid)
                                .toList().contains(item.getUuid()))
                        ).collect(Collectors.toList());
                //修改列表
                List<VolumeSnapshotDTO> updateDTOs = volumeSnapshotDTOS.stream()
                        .filter(item ->dtos.stream().map(VolumeSnapshotDTO::getUuid)
                                .toList().contains(item.getUuid()))
                        .toList();

                if (!updateDTOs.isEmpty()) {
                    volumeApi.updateVolumeSnapshots(updateDTOs);
                }
                if (!deleteTarget.isEmpty()) {
                    volumeApi.delVolumeSnapshots(deleteTarget);
                }
                if (!collect.isEmpty()) {
                    volumeApi.addVolumeSnapshots(collect);
                }
            }
        }
    }

    private VolumeSnapshotDTO convertToSnapshotInfo(String type,JSONObject jsonObject, PlatformconfigDTO p) {
        VolumeSnapshotDTO volumeSnapshotDTO = new VolumeSnapshotDTO();
        String uuid = jsonObject.getString("uuid");
        String name = jsonObject.getString("name");
        String description = jsonObject.getString("description");
        String instanceUuid = jsonObject.getString("instance_uuid") == null ? "" : jsonObject.getString("instance_uuid");
        String instanceName = jsonObject.getString("instance_name") == null ? "" : jsonObject.getString("instance_name");
        String volumeUuid = jsonObject.getString("source_disk_id") == null ? "" : jsonObject.getString("volume_uuid");
        String volumeName = jsonObject.getString("source_disk_name") == null? "" : jsonObject.getString("volume_name");
//        String primaryStorageUuid = jsonObject.getString("primaryStorageUuid");
//        String installPath = jsonObject.getString("primaryStorageInstallPath");
        String volumeType = jsonObject.getString("source_disk_type") == null ? ""
                : jsonObject.getString("source_disk_type").equals("ROOT") ? "Root" : "Data";
        Long size = jsonObject.getLong("size") != null ? jsonObject.getLong("size") * 1024 * 1024 * 1024 : 0L;
        String status = jsonObject.getString("status");
        if (StrUtil.isNotEmpty(status)) {
            status = status.equals("available") ? "Enabled" : "Disabled";
        }
        String createTimeStr = jsonObject.getString("create_time") != null ? jsonObject.getString("create_time") : null;
        Date vCreateDate = null;
        if (createTimeStr != null) {
            Instant instant = Instant.parse(createTimeStr);
            if (instant != null) {
                vCreateDate = Date.from(instant);
            }
        }
        String updateTimeStr = jsonObject.getString("update_time")!= null? jsonObject.getString("update_time") : null;
        Date vUpdateDate = null;
        if (updateTimeStr != null) {
            Instant instant = Instant.parse(updateTimeStr);
            if (instant != null) {
                vUpdateDate = Date.from(instant);
            }
        }
        String format = jsonObject.getString("format");
        Long platformId = p.getId();
        String platformName = p.getName();
        volumeSnapshotDTO.setCreateTime(DateUtil.toLocalDateTime(new Date()));
        volumeSnapshotDTO.setUuid(uuid);
        volumeSnapshotDTO.setName(name);
        volumeSnapshotDTO.setDescription(description);
        volumeSnapshotDTO.setHostUuid(instanceUuid);
        volumeSnapshotDTO.setHostName(instanceName);
        volumeSnapshotDTO.setVolumeUuid(volumeUuid);
        volumeSnapshotDTO.setVolumeName(volumeName);
//        volumeSnapshotDTO.setPrimaryStorageUuid(primaryStorageUuid);
//        volumeSnapshotDTO.setInstallPath(installPath);
        volumeSnapshotDTO.setType(type);
        volumeSnapshotDTO.setVolumeType(volumeType);
        volumeSnapshotDTO.setLatest("true");
        volumeSnapshotDTO.setPlatformName(platformName);
        volumeSnapshotDTO.setPlatformId(platformId);
        volumeSnapshotDTO.setTypeName("istack");
        volumeSnapshotDTO.setSize(size);
        volumeSnapshotDTO.setStatus(status);
        volumeSnapshotDTO.setVCreateDate(vCreateDate);
        volumeSnapshotDTO.setVUpdateDate(vUpdateDate);
        volumeSnapshotDTO.setFormat("qcow2");
        volumeSnapshotDTO.setIsMemory(false);
        return volumeSnapshotDTO;
    }
}
