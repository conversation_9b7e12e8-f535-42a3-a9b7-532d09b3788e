package cn.iocoder.zj.module.collection.job.image;

import cn.hutool.core.convert.Convert;
import cn.iocoder.zj.module.collection.dal.dataobject.zstack.ZstackLoginInfo;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.zstack.ZstackAccessTokenRedisDAO;
import cn.iocoder.zj.module.monitor.api.imageinfo.ImageInfoApi;
import cn.iocoder.zj.module.monitor.api.imageinfo.dto.ImageInfoCreateReqDTO;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.zstack.sdk.ImageInventory;
import org.zstack.sdk.QueryImageAction;
import org.zstack.sdk.ZSClient;
import org.zstack.sdk.ZSConfig;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static cn.iocoder.zj.module.collection.job.cloud.ZstackCloudJob.extractPort;
import static cn.iocoder.zj.module.collection.job.cloud.ZstackCloudJob.removeProtocolAndPort;

@Component
@Slf4j
public class ZstackImageJob {

    @Resource
    ZstackAccessTokenRedisDAO zstackAccessTokenRedisDAO;

    @Resource
    PlatformRedisDAO platformRedisDAO;

    @Resource
    ImageInfoApi imageInfoApi;

    @XxlJob("zstackImageJob")
    public void execute() {
        try {
            //从redis中获取zstack的登录信息
            List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
            List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                    .filter(dto -> "zstack".equals(dto.getTypeCode()))
                    .toList();
            if (filteredList.isEmpty()) {
                log.info("[execute][没有zstack平台]");
                return;
            }
            List<ImageInfoCreateReqDTO> imageInfoDTOs = new ArrayList<>();
            for (PlatformconfigDTO p : filteredList) {
                ZstackLoginInfo zstackLoginInfo = zstackAccessTokenRedisDAO.get("zstack:" + p.getId());
                // 1. 初始化ZStack客户端
                String processedUrl = removeProtocolAndPort(p.getUrl());
                String port = extractPort(p.getUrl());
                ZSClient.configure(
                        new ZSConfig.Builder()
                                .setHostname(processedUrl)
                                .setPort(Convert.toInt(port))
                                .setContextPath("zstack")
                                .build()
                );
                // 2. 创建查询镜像的API
                QueryImageAction action = new QueryImageAction();

                action.sessionId = zstackLoginInfo.getUuid(); // 登录获取sessionId

                // 3. 设置查询条件(可选)
                action.conditions = new ArrayList<>();
                // action.conditions.add("status=Ready"); // 例如只查询Ready状态的镜像

                // 4. 调用API
                QueryImageAction.Result result = action.call();

                // 5. 处理返回结果
                if (result.error != null) {
                    log.error("[execute][ZStack镜像查询失败: {}]", result.error.description);
                    return;
                }

                // 6. 解析镜像信息
                List<ImageInventory> images = result.value.getInventories();
                for (ImageInventory image : images) {
                    // 构建镜像信息DTO
                    ImageInfoCreateReqDTO imageInfoDTO = new ImageInfoCreateReqDTO();
                    imageInfoDTO.setUuid(image.uuid);
                    imageInfoDTO.setName(image.name);
                    imageInfoDTO.setStatus(image.state);
                    imageInfoDTO.setFormat(image.format);
                    imageInfoDTO.setCpuArch(image.architecture);
                    imageInfoDTO.setOsType(image.guestOsType);
                    imageInfoDTO.setSize(image.size);
                    if ("ISO".equals(image.mediaType))
                        imageInfoDTO.setImageType("RootVolumeTemplate");
                    else
                        imageInfoDTO.setImageType(image.mediaType);
                    imageInfoDTO.setSharingScope("不共享");
                    imageInfoDTO.setVCreateDate(image.createDate.toLocalDateTime());
                    imageInfoDTO.setVUpdateDate(image.lastOpDate.toLocalDateTime());
                    imageInfoDTO.setOsLanguage("");
                    imageInfoDTO.setMinMemory(null);
                    imageInfoDTO.setMinDisk(BigDecimal.valueOf(image.size));
                    imageInfoDTO.setDiskDriver("");
                    imageInfoDTO.setNetworkDriver("");
                    imageInfoDTO.setBootMode("");
                    imageInfoDTO.setRemoteProtocol("");
                    imageInfoDTO.setApplicationPlatform(image.platform);
                    imageInfoDTO.setPlatformId(p.getId());
                    imageInfoDTO.setPlatformName(p.getName());
                    imageInfoDTOs.add(imageInfoDTO);
                    log.info("[execute][保存镜像信息成功: {}]", image.name);
                }
                log.info("[execute][ZStack镜像查询成功,共获取到 {} 个镜像]", images.size());
            }
            if (!imageInfoDTOs.isEmpty()){
                List<ImageInfoCreateReqDTO> imageInfoDTOList = imageInfoApi.getAllImagesByTypeCode("zstack").getData();
                if (imageInfoDTOList == null || imageInfoDTOList.isEmpty()) {
                    imageInfoApi.batchCreateImageInfo(imageInfoDTOs);
                }else {
                    //比较uuid不存在删除
                    List<ImageInfoCreateReqDTO> deleteTarget = imageInfoDTOList.stream()
                            .filter(imageInfoDTO -> imageInfoDTOs.stream()
                                    .noneMatch(item -> item.getUuid().equals(imageInfoDTO.getUuid())))
                            .collect(Collectors.toList());
                    //新增镜像信息
                    List<ImageInfoCreateReqDTO> collect = imageInfoDTOs.stream()
                            .filter(imageInfoDTO -> imageInfoDTOList.stream()
                                   .noneMatch(item -> item.getUuid().equals(imageInfoDTO.getUuid())))
                            .collect(Collectors.toList());
                    //更新镜像信息
                    List<ImageInfoCreateReqDTO> updateDTOs = imageInfoDTOs.stream()
                            .filter(imageInfoDTO -> imageInfoDTOList.stream()
                                    .anyMatch(item -> item.getUuid().equals(imageInfoDTO.getUuid())))
                            .toList();
                    //去掉uuid不存在的collect
                    if (!collect.isEmpty()){
                        imageInfoApi.batchCreateImageInfo(collect);
                    }
                    if (!updateDTOs.isEmpty()){
                        imageInfoApi.batchUpdateImageInfo(updateDTOs);
                    }
                    if (!deleteTarget.isEmpty()) {
                        imageInfoApi.batchDeleteImageInfo(deleteTarget);
                    }
                }
            }
        } catch (Exception e) {
            log.error("[execute][ZStack镜像查询异常]", e);
        }
    }
}
