package cn.iocoder.zj.module.collection.service.vmware.host;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.module.collection.util.SampleUtil;
import com.alibaba.fastjson.JSONObject;
import com.vmware.vim25.VirtualMachineSnapshotInfo;
import com.vmware.vim25.VirtualMachineSnapshotTree;
import com.vmware.vim25.VirtualMachineConfigInfo;
import com.vmware.vim25.*;
import com.vmware.vim25.mo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.rmi.RemoteException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : HostComputerResourceSummary  //类名
 * @Description : 操作vcenter中的宿主机对象  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/7/24  15:07
 */
@Component
@Slf4j
public class HostComputerResourceSummary{


    public static List<HostSystem> getHostList(ServiceInstance serviceInstance) throws RemoteException {
        List<HostSystem> hostSystemList = new ArrayList<HostSystem>();
        HostSystem hostSystem = null;

        ManagedEntity[] managedEntities = new InventoryNavigator(serviceInstance.getRootFolder())
                .searchManagedEntities("HostSystem");
        if (managedEntities != null && managedEntities.length > 0) {
            for (ManagedEntity managedEntity : managedEntities) {
                hostSystem = (HostSystem) managedEntity;
                hostSystemList.add(hostSystem);
            }
        } else {
            return null;
        }
        return hostSystemList;
    }

    public static List<Map<String, Object>> getVMSnapshots(ServiceInstance serviceInstance) throws RemoteException {
        try {
            ManagedEntity[] managedEntities = new InventoryNavigator(serviceInstance.getRootFolder()).searchManagedEntities("VirtualMachine");
            return Arrays.stream(managedEntities)
                    .map(entity -> (VirtualMachine) entity)
                    .map(vm -> {
                        try {
                            List<Map<String, Object>> snapshots = getAllSnapshotInfo(vm);
                            snapshots.forEach(snapshot -> {
                                snapshot.put("hostName", vm.getName());
                                snapshot.put("vms", vm.getMOR().getVal());
                            });
                            return snapshots;
                        } catch (RemoteException e) {
                            log.error("获取主机快照异常: {}", vm.getName(), e);
                            return Collections.<Map<String, Object>>emptyList();
                        }
                    })
                    .flatMap(List::stream)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            throw new RemoteException("获取虚拟机快照列表失败: " + e.getMessage());
        }
    }


    public static List<Map<String, Object>> getAllSnapshotInfo(VirtualMachine vm) throws RemoteException {
        try {
            VirtualMachineSnapshotInfo snapInfo = vm.getSnapshot();
            if (snapInfo == null || snapInfo.getRootSnapshotList() == null) {
                return Collections.emptyList();
            }
            List<Map<String, Object>> allSnapshots = new ArrayList<>();
            Arrays.stream(snapInfo.getRootSnapshotList()).forEach(snapshot -> processSnapshotTree(snapshot, vm, allSnapshots));
            return allSnapshots;
        } catch (Exception e) {
            throw new RemoteException("获取快照信息失败: " + e.getMessage());
        }
    }

    private static void processSnapshotTree(VirtualMachineSnapshotTree snapshot,VirtualMachine vm,List<Map<String, Object>> allSnapshots) {
        try {
            Map<String, Object> snapshotInfo = getSnapshotDetails(snapshot, vm);
            VirtualMachineSnapshotTree[] childSnapshots = snapshot.getChildSnapshotList();
            boolean hasChildren = childSnapshots != null && childSnapshots.length > 0;
            snapshotInfo.put("hasChildren", hasChildren);
            snapshotInfo.put("childCount", hasChildren ? childSnapshots.length : 0);
            allSnapshots.add(snapshotInfo);
            if (hasChildren) {
                Arrays.stream(childSnapshots)
                        .forEach(childSnapshot -> processSnapshotTree(childSnapshot, vm, allSnapshots));
            }
        } catch (Exception e) {
            log.error("Failed to process snapshot: {}", snapshot.getName(), e);
        }
    }

    private static Map<String, Object> getSnapshotDetails(VirtualMachineSnapshotTree snapshot, VirtualMachine vm) {
        Map<String, Object> snapshotInfo = new HashMap<>();
        // 基本信息
        snapshotInfo.put("name", snapshot.getName());
        snapshotInfo.put("description", snapshot.getDescription());
        snapshotInfo.put("createTime", snapshot.getCreateTime().getTime());
        snapshotInfo.put("id", snapshot.getId());
        snapshotInfo.put("state", snapshot.getState().toString());

        // 快照引用信息
        ManagedObjectReference mor = snapshot.getSnapshot();
        if (mor != null) {
            snapshotInfo.put("uuid", mor.getVal());
        }
        VirtualMachineConfigInfo config = vm.getConfig();
        if (config != null && config.getFiles() != null) {
            snapshotInfo.put("snapshotDirectory", config.getFiles().getSnapshotDirectory());
            snapshotInfo.put("vmPathName", config.getFiles().getVmPathName());
            snapshotInfo.put("logDirectory", config.getFiles().getLogDirectory());
        }
        return snapshotInfo;
    }

    private static final String DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";

    public static List<Map<String, Object>> getVMImages(ServiceInstance serviceInstance) {
        List<Map<String, Object>> imageList = new ArrayList<>();
        try {
            ManagedEntity[] vms = new InventoryNavigator(serviceInstance.getRootFolder())
                    .searchManagedEntities("VirtualMachine");
            if (vms == null || vms.length == 0) {
                return imageList;
            }
            for (ManagedEntity entity : vms) {
                VirtualMachine vm = (VirtualMachine) entity;
                try {
                    if (!vm.getConfig().isTemplate()) {
                        continue;
                    }
                    imageList.add(buildImageInfo(vm));
                } catch (Exception e) {
                    log.error("处理虚拟机模板 {} 失败: {}", vm.getName(), e.getMessage());
                }
            }
            return imageList;
        } catch (Exception e) {
            log.error("获取VMware镜像列表失败", e);
            return Collections.emptyList();
        }
    }

    private static Map<String, Object> buildImageInfo(VirtualMachine vm) throws Exception {
        Map<String, Object> imageInfo = new HashMap<>();
        VirtualMachineConfigInfo config = vm.getConfig();
        imageInfo.put("name", vm.getName());
        imageInfo.put("uuid", config.getUuid());
        imageInfo.put("osType", config.getGuestFullName());
        imageInfo.put("hostUuid", vm.getMOR().getVal());

        addCpuArchInfo(vm, imageInfo);

        VirtualHardware hardware = config.getHardware();
        imageInfo.put("minMemory", hardware.getMemoryMB() * 1024L);

        addStorageInfo(hardware, imageInfo);

        addCreateTime(config, imageInfo);
        return imageInfo;
    }

    private static void addCpuArchInfo(VirtualMachine vm, Map<String, Object> imageInfo) {
        GuestInfo guestInfo = vm.getGuest();
        if (guestInfo != null) {
            String guestId = guestInfo.getGuestId();
            imageInfo.put("cpuArch", determineCpuArch(guestId));
        }
    }

    private static void addStorageInfo(VirtualHardware hardware, Map<String, Object> imageInfo) {
        long totalSize = 0;
        for (VirtualDevice device : hardware.getDevice()) {
            if (device instanceof VirtualDisk) {
                VirtualDisk disk = (VirtualDisk) device;
                totalSize += disk.getCapacityInKB();
            }
        }
        imageInfo.put("minDisk", totalSize * 1024L);
    }

    private static void addCreateTime(VirtualMachineConfigInfo config, Map<String, Object> imageInfo) {
        String changeVersion = config.getChangeVersion();
        if (StrUtil.isNotEmpty(changeVersion)) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
                sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
                imageInfo.put("createTime", sdf.parse(changeVersion.substring(0, 23) + "Z").getTime());
            } catch (Exception e) {
                imageInfo.put("createTime", new Date().getTime());
            }
        } else {
            imageInfo.put("createTime", new Date());
        }
    }

    private static String determineCpuArch(String guestId) {
        if (guestId == null) {
            return "x86_64";
        }
        if (guestId.contains("64")) {
            return "x86_64";
        } else if (guestId.contains("arm") || guestId.contains("ARM")) {
            return "aarch64";
        }else if (guestId.contains("32")) {
            return "x86_32";
        }else if (guestId.contains("mips")) {
            return "mips64el";
        }
        return "x86_64";
    }



    public static void main(String[] args) {
        try {
            ServiceInstance si = SampleUtil.createServiceInstance(
                    "https://**************:10002",
                    "<EMAIL>",
                    "Lihulin@123"
            );
            List<Map<String, Object>> snapshots = getVMImages(si);
           log.info(JSONObject.toJSONString(snapshots));
        } catch (Exception e) {
            log.error("操作失败", e);
        }
    }

}
