package cn.iocoder.zj.module.collection.handler;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.iocoder.zj.module.collection.service.zstack.RequestInterceptor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class HttpClientWrapper {
    private List<RequestInterceptor> interceptors = new ArrayList<>();

    public void addInterceptor(RequestInterceptor interceptor) {
        interceptors.add(interceptor);
    }

    public HttpResponse execute(HttpRequest request) {
        // 依次执行所有拦截器
        for (RequestInterceptor interceptor : interceptors) {
            request = interceptor.intercept(request);
        }
        // 执行最终的HTTP请求
        HttpResponse execute = request.execute();
        if(execute.getStatus() != 200){
            System.out.println(request);
        }
        return execute;
    }

}
