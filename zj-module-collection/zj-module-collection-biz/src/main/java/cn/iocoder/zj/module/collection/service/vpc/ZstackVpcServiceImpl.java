package cn.iocoder.zj.module.collection.service.vpc;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.iocoder.zj.module.collection.service.zstack.core.ZstackApiConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @ClassName : ZstackVpcServiceImpl  //类名
 * @Description : VPC 接口实现类  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/8/3  15:09
 */

@Slf4j
@Service
public class ZstackVpcServiceImpl implements ZstackVpcService{

    /**
     * @description:  查询VPC路由器(QueryVpcRouter)
     * <AUTHOR>
     * @date 2023/8/3 15:15
     * @version 1.0
     */
    @Override
    public String virtualRouters(String url, String token) {
        HttpResponse result = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_VPC)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .execute();
        if (result.getStatus() != 200) {
            throw new RuntimeException("getVirtualRouters error");
        }
        return result.body();
    }

    /**
     * @description: 获取VPC路由器实时流量状态。
     * <AUTHOR>
     * @date 2023/8/3 15:15
     * @version 1.0
     */
    @Override
    public String trackedConnections(String url, String token, String uuid) {
        HttpResponse result = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_VPC+"/"+uuid+"/tracked-connections")
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .execute();
        if (result.getStatus() != 200) {
            throw new RuntimeException("getTrackedConnections error");
        }
        return result.body();
    }
}
