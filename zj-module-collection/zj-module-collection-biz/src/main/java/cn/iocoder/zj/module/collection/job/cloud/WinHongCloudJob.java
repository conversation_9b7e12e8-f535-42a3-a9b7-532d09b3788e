package cn.iocoder.zj.module.collection.job.cloud;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import cn.iocoder.zj.framework.common.util.collection.CollectionUtils;
import cn.iocoder.zj.framework.common.util.date.DateUtils;
import cn.iocoder.zj.module.collection.dal.dataobject.zstack.AlarmConfigInfo;
import cn.iocoder.zj.module.collection.dal.dataobject.zstack.AlarmHostRelationInfo;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.winhong.WinHongAccessTokenRedisDAO;

import cn.iocoder.zj.module.collection.dal.redis.zstack.AlarmInfoRedisDAO;
import cn.iocoder.zj.module.collection.framework.influx.config.InfluxDBTemplate;
import cn.iocoder.zj.module.collection.service.cloud.IWinHongDeviceService;
import cn.iocoder.zj.module.collection.service.winHong.WinHongApiConstant;
import cn.iocoder.zj.module.collection.util.PowerStateUtil;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import cn.iocoder.zj.module.monitor.api.hostinfo.HostInfoApi;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.hostnic.HostNicApi;
import cn.iocoder.zj.module.monitor.api.hostnic.dto.HostNicCreateReqDto;
import cn.iocoder.zj.module.monitor.api.valume.VolumeApi;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeDTO;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeSnapshotDTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class WinHongCloudJob {

    @Resource
    PlatformRedisDAO platformRedisDAO;

    @Resource
    PlatformconfigApi platformconfigApi;

    @Resource
    WinHongAccessTokenRedisDAO winHongAccessTokenRedisDAO;

    @Resource
    AlarmInfoRedisDAO alarmInfoRedisDAO;

    @Resource
    HostNicApi hostNicApi;

    @Resource
    IWinHongDeviceService winHongDeviceService;

    @Resource
    VolumeApi volumeApi;

    @Resource
    HostInfoApi hostInfoApi;

    @Autowired
    private AlarmConfigApi alarmConfigApi;

    @Resource
    InfluxDBTemplate influxDBTemplate;

    public void winHongJobList() {
        loginInfo();
        collectWinHongCloud();
        winHongCloudByVmInfos();
        volumeWinHongSnapshotInfo();
    }


    public String getToken(PlatformconfigDTO p) {
        String token = "";
        JSONObject tokenData = winHongAccessTokenRedisDAO.get("winhong:" + p.getId());

        // 判断 token 是否存在并且是否有效
        if (tokenData != null && tokenData.containsKey("sessionId")) {
            token = tokenData.getString("sessionId");
            Boolean isTokenValid = winHongDeviceService.noRefresh(p, token);
            if (isTokenValid) {
                return token; // 如果 token 有效，直接返回
            }
        }

        log.info("Token 失效，重新获取 token...");
        JSONObject parame = new JSONObject();
        parame.put("user", p.getUsername());
        parame.put("pwd", p.getPassword());

        try {
            // 发送登录请求
            HttpRequest res = HttpRequest.post(p.getUrl() + WinHongApiConstant.LOGIN)
                    .body(parame.toJSONString());
            log.info("获取登录数据：" + res.execute().body());

            if (res.execute().getStatus() == 200) {
                JSONObject responseData = JSONObject.parseObject(res.execute().body());
                token = responseData.getString("sessionId");

                // 将 token 存入 Redis
                winHongAccessTokenRedisDAO.set("winhong:" + p.getId(), responseData);
                return token;
            } else {
                log.error("登录请求失败，状态码: " + res.execute().getStatus());
            }
        } catch (Exception e) {
            log.error("获取授权异常: " + e.getMessage(), e);
        }

        return null; // 如果所有步骤都失败，返回 null
    }


    @XxlJob("winHongCloud")
    @Transactional
    public void collectWinHongCloud() {
        List<PlatformconfigDTO> platformconfigDTOList = new ArrayList<>();
        if (platformRedisDAO.get("platform") == null) {
            platformconfigDTOList = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform", platformconfigDTOList);
        }
        List<HostInfoRespCreateReqDTO> hostInfoRespCreateReqDTOList = new ArrayList<>();
        List<AlarmHostRelationInfo> alarmHostRealtionList = JSON.parseArray(alarmInfoRedisDAO.getRelation("alarm_host_relation"), AlarmHostRelationInfo.class);
        List<AlarmConfigInfo> alarmConfigInfoList = JSON.parseArray(alarmInfoRedisDAO.getConfig("alarm_config"), AlarmConfigInfo.class);
        platformconfigDTOList = platformRedisDAO.get("platform");
        if (platformconfigDTOList.size() > 0) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                List<HostNicCreateReqDto> hostNicCreateReqDtoList = new ArrayList<>();

                if (p.getTypeCode().equals("winhong") && winHongAccessTokenRedisDAO.get("winhong:" + p.getId()) != null) {
                    String token = winHongDeviceService.getToken(p);
                    if (token==null){
                        log.info("获取云宏token失败");
                        return;
                    }
                    JSONArray clouds = winHongDeviceService.getClouds(p, token);
                    if (ObjectUtil.isNull(clouds)) return;
                    List<Map> listObjectSec = JSONObject.parseArray(clouds.toJSONString(), Map.class);
//                    HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO = new HostInfoRespCreateReqDTO();
                    ArrayList<HostInfoRespCreateReqDTO> hostInfoList = new ArrayList<>();
                    List<VolumeDTO> volumeDTOList = new ArrayList<>();
                    List<String> deleteList = new ArrayList<>();
                    for (Map map : listObjectSec) {
                        log.info("在这——————————————————————————>" + listObjectSec.indexOf(map));
                        //获取cpu类型
                        JSONObject summary = winHongDeviceService.getSummary(p, token, map.get("uuid").toString());
                        //云主机详情
                        JSONObject domainInfo = winHongDeviceService.getdomainInfo(p, token, map.get("uuid").toString());
                        //云盘
                        JSONArray diskStorageInfos = winHongDeviceService.getDiskStorageInfo(p, token, map.get("uuid").toString());
                        //查询虚拟机cpu和内存利用率
                        JSONObject cpuAndMemoryRate = winHongDeviceService.getCpuAndMemoryRate(p, token, map.get("uuid").toString());

//                        List<Map> diskList = JSONObject.parseArray(diskStorageInfos.toJSONString(), Map.class);
                        //云盘信息
                        JSONArray diskInfos = winHongDeviceService.getDomainDiskInfo(p, token, map.get("uuid").toString());

                        HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO = new HostInfoRespCreateReqDTO();
                        String status = PowerStateUtil.powerStateConvert(stateConvert(map.get("status").toString()));
                        hostInfoRespCreateReqDTO.setPowerState(status);
                        hostInfoRespCreateReqDTO.setImageName(domainInfo.getString("templateName"));
                        hostInfoRespCreateReqDTO.setImageUuid(domainInfo.getString("templateId"));
                        if(domainInfo.getBoolean("isHa")){
                            hostInfoRespCreateReqDTO.setAutoInitType("NeverStop");
                        }else {
                            hostInfoRespCreateReqDTO.setAutoInitType("None");
                        }

                        if(domainInfo.getInteger("bootType") == 0){
                            hostInfoRespCreateReqDTO.setGuideMode("Legacy");
                        }else {
                            hostInfoRespCreateReqDTO.setGuideMode("UEFI");
                        }


                        hostInfoRespCreateReqDTO.setUuid(map.get("uuid").toString());
                        hostInfoRespCreateReqDTO.setHardwareUuid(map.get("hostId").toString());
                        hostInfoRespCreateReqDTO.setHardwareName(map.get("hostName").toString());
                        hostInfoRespCreateReqDTO.setState(stateConvert(map.get("status").toString()));
                        hostInfoRespCreateReqDTO.setName(map.get("name").toString());
                        hostInfoRespCreateReqDTO.setGuestOsType(map.get("osVersion").toString());
//                        hostInfoRespCreateReqDTO.setVms(jsonObject.getString("vmid"));
                        hostInfoRespCreateReqDTO.setVCreateDate(DateUtils.convertDate(map.get("createTime").toString()));
                        hostInfoRespCreateReqDTO.setIp(map.get("ip") != null ? map.get("ip").toString() : "");
                        JSONArray bridgeInterfaces = JSONObject.parseArray(map.get("bridgeInterfaces").toString());

                        JSONArray dombridgeInterfaces = JSONObject.parseArray(domainInfo.get("bridgeInterfaces").toString());
                        if (!dombridgeInterfaces.isEmpty()) {
                            for (int i = 0; i < dombridgeInterfaces.size(); i++) {
                                JSONObject vm = dombridgeInterfaces.getJSONObject(i);
                                HostNicCreateReqDto hostNicCreateReqDto = new HostNicCreateReqDto();
                                hostNicCreateReqDto.setHostUuid(map.get("uuid").toString());
                                hostNicCreateReqDto.setUuid(vm.get("interfaceId").toString());
                                hostNicCreateReqDto.setIp(vm.getString("ip"));
                                hostNicCreateReqDto.setIp6("");
                                hostNicCreateReqDto.setPlatformId(p.getId());
                                hostNicCreateReqDto.setPlatformName(p.getName());
                                hostNicCreateReqDto.setMac(vm.getString("mac"));
                                hostNicCreateReqDto.setDriver("virtio");
                                hostNicCreateReqDto.setInClassicNetwork((byte) 0);
                                hostNicCreateReqDto.setNetworkUuid(vm.getString("portGroupId"));
                                hostNicCreateReqDtoList.add(hostNicCreateReqDto);
                            }
                        }

                        if (bridgeInterfaces.size()>0){
                            JSONObject bridgeInterface = bridgeInterfaces.getJSONObject(0);
                            String mac=bridgeInterface.getString("mac");
                            if (mac==null || mac.equals("")){
                                hostInfoRespCreateReqDTO.setMac("-");
                            }else {
                                if (mac.contains(",")) {
                                    // 如果包含逗号，取第一个MAC地址
                                    hostInfoRespCreateReqDTO.setMac(mac.split(",")[0].trim());
                                } else {
                                    // 如果不包含逗号，直接返回
                                    hostInfoRespCreateReqDTO.setMac(mac);
                                }
                            }
                        }else {
                            hostInfoRespCreateReqDTO.setMac("");
                        }
                        hostInfoRespCreateReqDTO.setClusterName(map.get("clusterName").toString());
                        hostInfoRespCreateReqDTO.setClusterUuid(map.get("clusterId").toString());
                        hostInfoRespCreateReqDTO.setHardwareUuid(map.get("hostId").toString());
                        hostInfoRespCreateReqDTO.setHardwareName(map.get("hostName").toString());
                        hostInfoRespCreateReqDTO.setZoneUuid(map.get("poolId").toString());
                        hostInfoRespCreateReqDTO.setZoneName(map.get("poolName").toString());
                        hostInfoRespCreateReqDTO.setMemoryUsed(cpuAndMemoryRate.getBigDecimal("memRate"));
                        //是总的还是使用的？
                        hostInfoRespCreateReqDTO.setMemorySize(Long.valueOf(map.get("memory").toString()));
                        hostInfoRespCreateReqDTO.setCpuUsed(cpuAndMemoryRate.getBigDecimal("cpuRate"));
                        hostInfoRespCreateReqDTO.setArchitecture(summary.getString("cpuArchitecture"));
//                        BigDecimal diskTotal = cpuAndMemoryRate.getBigDecimal("diskTotal");
//                        BigDecimal diskUsed = cpuAndMemoryRate.get("diskUsed") != null ? cpuAndMemoryRate.getBigDecimal("diskUsed") : new BigDecimal("0");
                        hostInfoRespCreateReqDTO.setDiskUsed(cpuAndMemoryRate.get("diskRate") != null ? cpuAndMemoryRate.getBigDecimal("diskRate") : new BigDecimal("0"));
                        hostInfoRespCreateReqDTO.setCpuNum(domainInfo.getJSONObject("cpu").getInteger("cores"));
                        hostInfoRespCreateReqDTO.setNetworkInPackets(map.get("inAvgBandwidth") != null ? new BigDecimal(map.get("inBurstSize").toString()) : new BigDecimal(0));
                        hostInfoRespCreateReqDTO.setNetworkInBytes(map.get("inAvgBandwidth") != null ? new BigDecimal(Double.valueOf(map.get("inAvgBandwidth").toString())).setScale(2, BigDecimal.ROUND_HALF_UP) : new BigDecimal(0.00));

                        hostInfoRespCreateReqDTO.setNetworkOutPackets(map.get("outBurstSize") != null ? new BigDecimal(map.get("outBurstSize").toString()) : new BigDecimal(0));
                        hostInfoRespCreateReqDTO.setNetworkOutBytes(map.get("outAvgBandwidth") != null ? new BigDecimal(Double.valueOf(map.get("outAvgBandwidth").toString())).setScale(2, BigDecimal.ROUND_HALF_UP) : new BigDecimal(0.00));
                        hostInfoRespCreateReqDTO.setPlatformId(p.getId());
                        hostInfoRespCreateReqDTO.setPlatformName(p.getName());
                        hostInfoRespCreateReqDTO.setTypeName(p.getTypeCode());
                        hostInfoRespCreateReqDTO.setTenantId(p.getTenantId());
                        hostInfoRespCreateReqDTO.setRegionId(p.getRegionId());
                        hostInfoRespCreateReqDTO.setType("winhong");
                        hostInfoRespCreateReqDTO.setDeleted(0);
                        //云盘信息
                        List<Map> diskList = JSONObject.parseArray(diskInfos.toJSONString(), Map.class);
                        Long cloudSize=0l;
                        Long actualSize=0l;
                        //磁盘总容量
                        Long totalDiskCapacity=0l;
                        //磁盘已用
                        Long diskUsedBytes=0l;
                        //剩余
                        for (int i = 0; i < diskList.size(); i++) {
                            Map disk = diskList.get(i);
                            VolumeDTO volumeDTO = new VolumeDTO();
                            volumeDTO.setDescription("云宏云盘描述");    //描述
                            volumeDTO.setName("disk"+i);
                            //未找
                            volumeDTO.setFormat(getFormat(Integer.valueOf(disk.get("busType").toString())));  //云盘格式
                            Long capacity = disk.get("capacity")!=null?Long.valueOf(disk.get("capacity").toString()):0;
                            if (i>0){
                                cloudSize=cloudSize+capacity;
                            }
                            Long allocation = disk.get("allocation")!=null?Long.valueOf(disk.get("allocation").toString()):0;
                            if (i>0){
                                actualSize=actualSize+allocation;
                            }
                            if (i==0){
                                totalDiskCapacity=capacity;
                                diskUsedBytes=allocation;
                            }
                            volumeDTO.setSize(capacity);
                            volumeDTO.setActualFree(capacity - allocation);
                            volumeDTO.setActualUse(allocation);
                            volumeDTO.setActualRatio(new BigDecimal(allocation).multiply(new BigDecimal(100)).divide(new BigDecimal(capacity) ,2, BigDecimal.ROUND_HALF_UP).toString());
                            //磁盘类型：file, block, network …
                            if (i==0){
                                volumeDTO.setType("Root");
                            }else {
                                volumeDTO.setType("Data");
                            }
                            volumeDTO.setActualSize(capacity);
                            volumeDTO.setState("Enabled");
                            volumeDTO.setUuid(disk.get("volId").toString());
                            volumeDTO.setStatus("Ready");
                            volumeDTO.setPlatformId(p.getId());
                            volumeDTO.setPlatformName(p.getName());
                            volumeDTO.setVmInstanceUuid(map.get("uuid").toString());
                            volumeDTO.setVmInstanceName(map.get("name").toString());
                            volumeDTO.setPrimaryStorageUuid(disk.get("storagePoolId").toString());
                            volumeDTO.setPrimaryStorageName(disk.get("storagePoolShowName").toString());
                            volumeDTOList.add(volumeDTO);
                            deleteList.add(map.get("uuid").toString());
                        }
                        hostInfoRespCreateReqDTO.setTotalDiskCapacity(new BigDecimal(totalDiskCapacity));
                        hostInfoRespCreateReqDTO.setDiskUsedBytes(new BigDecimal(totalDiskCapacity-diskUsedBytes));
                        hostInfoRespCreateReqDTO.setDiskFreeBytes(new BigDecimal(diskUsedBytes));

                        hostInfoRespCreateReqDTO.setCloudSize(new BigDecimal(cloudSize));
                        hostInfoRespCreateReqDTO.setActualSize(new BigDecimal(actualSize));
                        hostInfoRespCreateReqDTOList.add(hostInfoRespCreateReqDTO);
                    }
//
                    if (volumeDTOList.size() > 0) {
                        if (deleteList.size() > 0) {
                            volumeApi.delVolumeByInstanceUuid(deleteList);
                        }
                        //删除 根据云主机uuid批量删除磁盘数据
                        volumeApi.addVolumes(volumeDTOList);
                    }

                    if (!hostNicCreateReqDtoList.isEmpty()) {
                        List<HostNicCreateReqDto> nowNic = hostNicApi.getHostNicsByPlatformId(p.getId()).getData();
                        //根据uuid筛选出hostNicCreateReqDtoList里需要新增的数据
                        Set<String> nowNicUuids = nowNic.stream()
                                .map(HostNicCreateReqDto::getUuid)
                                .collect(Collectors.toSet());

                        List<HostNicCreateReqDto> addHostNicList = hostNicCreateReqDtoList.stream()
                                .filter(hostNicCreateReqDto -> !nowNicUuids.contains(hostNicCreateReqDto.getUuid()))
                                .toList();
                        if (!addHostNicList.isEmpty()) {
                            hostNicApi.addHostNics(addHostNicList);
                        }
                        //查询updateList 并将nowNicUuids 的id 赋值给updateHostNicList
                        List<HostNicCreateReqDto> updateHostNicList = hostNicCreateReqDtoList.stream()
                                .filter(hostNicCreateReqDto -> nowNicUuids.contains(hostNicCreateReqDto.getUuid()))
                                .toList().stream().map(hostNicCreateReqDto -> {
                                    return hostNicCreateReqDto.setId(nowNic.stream()
                                            .filter(hostNicRespDTO -> hostNicRespDTO.getUuid().equals(hostNicCreateReqDto.getUuid()))
                                            .findFirst()
                                            .map(HostNicCreateReqDto::getId)
                                            .orElse(null));
                                }).toList();
                        if (!updateHostNicList.isEmpty()) {
                            hostNicApi.updateHostNics(updateHostNicList);
                        }
                        //根据uuid 判断在nowNic不在hostNicCreateReqDtoList里的数据
                        Set<String> hostNicUuids = hostNicCreateReqDtoList.stream()
                                .map(HostNicCreateReqDto::getUuid)
                                .collect(Collectors.toSet());
                        List<HostNicCreateReqDto> delHostNicList = nowNic.stream()
                                .filter(hostNicRespDTO -> !hostNicUuids.contains(hostNicRespDTO.getUuid()))
                                .toList();
                        if (!delHostNicList.isEmpty()) {
                            hostNicApi.delHostNics(delHostNicList);
                        }
                    }

                    if (hostInfoRespCreateReqDTOList.size() > 0) {
                        List<AlarmDorisReqDTO> alarmDorisReqDTO = new ArrayList<>();
                        List<HostInfoRespCreateReqDTO> dtoList = hostInfoApi.getAll("winhong").getData();
                        // 获取当前节点的index 与 总节点数
                        int shardIndex = XxlJobHelper.getShardIndex();
                        int shardTotal = XxlJobHelper.getShardTotal();
                        log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
                        int hostCount = hostInfoApi.count("winhong");
                        List<HostInfoRespCreateReqDTO> shardingData = new ArrayList<>();
                        if (shardIndex < 0) {
                            shardingData = hostInfoRespCreateReqDTOList;
                        } else {
                            shardingData = StringUtil.getShardingData(hostInfoRespCreateReqDTOList, shardTotal, shardIndex);
                        }

                        // 对分片数据进行业务处理
                        for (HostInfoRespCreateReqDTO item : shardingData) {
                            // 模拟业务逻辑处理
                            log.info("Processing item: " + item.getName());
                        }
                        if (hostCount == 0) {
                            hostInfoApi.adds(shardingData);
                        } else {
                            List<HostInfoRespCreateReqDTO> collect3 = dtoList
                                    .stream()
                                    .filter(item -> !hostInfoRespCreateReqDTOList.stream()
                                            .map(HostInfoRespCreateReqDTO::getUuid)
                                            .collect(Collectors.toList())
                                            .contains(item.getUuid()))
                                    .collect(Collectors.toList());
                            if (alarmDorisReqDTO.size() > 0) {
                                Map<String, List> addMap = new HashMap<>();
                                addMap.put("updateList", new ArrayList<>());
                                addMap.put("insertList", alarmDorisReqDTO);
                                alarmConfigApi.createAlarmToDoris(addMap);
                            }
                            // 软删除云主机历史数据
                            if (collect3.size() > 0) {
                                collect3.forEach(item -> item.setDeleted(1).setState("Destroyed"));
                                hostInfoApi.updates(collect3);
                            }
                            if (shardingData.size() > 0) {
                                hostInfoApi.updates(shardingData);
                            }
                            List<HostInfoRespCreateReqDTO> collect = hostInfoRespCreateReqDTOList.stream().filter(item -> !dtoList.stream().map(HostInfoRespCreateReqDTO::getUuid).collect(Collectors.toList()).contains(item.getUuid())).collect(Collectors.toList());
                            if (collect.size() > 0) {
                                collect = collect.stream()
                                        .distinct()
                                        .collect(Collectors.toList());
                                hostInfoApi.adds(collect);
                            }
                            hostInfoApi.removeDuplicateData();


                        }


                    }

                }
            }


        }
    }

    public long convertGBtoBytes(String gb) {
        double gbValue = Double.parseDouble(gb);
        long bytes = (long) (gbValue * 1024 * 1024 * 1024);
        return bytes;
    }

    public String getFormat(Integer busType){
        String format="";
        switch (busType) {
            case 1:
                format = "高速硬盘";
                break;
            case 2:
                format = "IDE硬盘";
                break;
            case 3:
                format = "SCSI硬盘";
                break;
            case 4:
                format = "SATA硬盘";
                break;

                case 5:
                format = "USB硬盘";
                break;
            case 6:
                format = "SD硬盘";
                break;
            default:
                format = "";
        }
        return format;
    }

    @XxlJob("winHongCloudByVmInfos")
    public void winHongCloudByVmInfos() {
        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        if (platformconfigDTOList.size() > 0) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                if (p.getTypeCode().equals("winhong")) {
                    String token = winHongDeviceService.getToken(p);
                    if (token==null){
                        log.info("获取云宏token失败");
                        return;
                    }
                    JSONArray clouds = winHongDeviceService.getClouds(p, token);
                    BatchPoints batchPoints = BatchPoints.builder().build();
                    List<Map> cloudList = JSONObject.parseArray(clouds.toJSONString(), Map.class);
                    List<String> domainIds = cloudList.stream()
                            .filter(map -> map.get("status").toString().equals("1"))
                            .map(map -> map.get("uuid").toString())
                            .collect(Collectors.toList());

                    String result = String.join(",", domainIds);
                    //所有cup使用率
                    JSONArray cpuRateList = winHongDeviceService.getHostCpuRate(p, token, result);
                    if (cpuRateList.size() > 0) {
                        for (Object cpuRate : cpuRateList) {
                            JSONObject usageItem = JSONObject.parseObject(JSONUtil.toJsonStr(cpuRate));
                            String domainId = usageItem.get("domainId").toString();
                            List<Map> data = JSONObject.parseArray(usageItem.getString("data"), Map.class);
                            if (data.size() > 0) {
                                for (Object item : data) {
                                    JSONObject cpu = JSONObject.parseObject(JSONUtil.toJsonStr(item));
                                    Point point = Point.measurement("zj_cloud_host")
                                            .tag("label", "cpu")
                                            .tag("uuid", domainId)
                                            .tag("metricName", "CPUAverageUsedUtilization")
                                            .addField("productsName", usageItem.get("domainName")!=null?usageItem.get("domainName").toString():"")
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("vm_type", "CPUAverageUsedUtilization")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "CPUAverageUsedUtilization")
                                            .addField("type", "CPUAverageUsedUtilization")
                                            .addField("value", Convert.toBigDecimal(cpu.get("value")))
                                            .time(DateUtil.parse(Convert.toStr(cpu.get("time"))).getTime() / 1000, TimeUnit.SECONDS).build();
                                    batchPoints.point(point);
                                }
                            }
                        }

                    }

                    //所有内存使用
                    JSONArray flowLinkList = winHongDeviceService.getHostFlowLink(p, token, result);

                    if (flowLinkList.size() > 0) {
                        for (Object flowLink : flowLinkList) {
                            JSONObject usageItem = JSONObject.parseObject(JSONUtil.toJsonStr(flowLink));
                            String domainId = usageItem.get("domainId").toString();
                            List<Map> data = JSONObject.parseArray(usageItem.getString("data"), Map.class);
                            if (data.size() > 0) {
                                for (Object item : data) {
                                    JSONObject flow = JSONObject.parseObject(JSONUtil.toJsonStr(item));

                                    BigDecimal memoryPercent = flow.get("value") != null ? Convert.toBigDecimal(flow.get("value")) : new BigDecimal("0");
//                                    JSONObject cpuAndMemoryRate = winHongDeviceService.getCpuAndMemoryRate(p, token, domainId.toString());
                                    //总内存
                                    BigDecimal memory = new BigDecimal(0);
                                    for (Map map : cloudList) {
                                        if (map.get("uuid").toString().equals(domainId)) {
                                            memory = new BigDecimal(map.get("memory").toString());
                                            break;
                                        }
                                    }
                                    //内存使用
                                    BigDecimal memoryUsed = memory.multiply(memoryPercent).setScale(2, RoundingMode.HALF_UP);
                                    //内存kxina
                                    BigDecimal memoryFree = memory.subtract(memoryUsed).setScale(2, RoundingMode.HALF_UP);
                                    Point usedPoint = Point.measurement("zj_cloud_host")
                                            .tag("uuid", domainId)
                                            .tag("metricName", "MemoryUsedBytes")
                                            .tag("label", "mem")
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("vm_type", "all")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("productsName", usageItem.get("domainName")!=null?usageItem.get("domainName").toString():"")
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "MemoryUsedBytes")
                                            .addField("type", "all")
                                            .addField("value", memoryUsed)
                                            .time(DateUtil.parse(Convert.toStr(flow.get("time"))).getTime() / 1000, TimeUnit.SECONDS).build();
                                    batchPoints.point(usedPoint);
                                    //内存空闲
                                    Point freePoint = Point.measurement("zj_cloud_host")
                                            .tag("uuid", domainId)
                                            .tag("label", "mem")
                                            .tag("metricName", "MemoryFreeBytes")
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("vm_type", "all")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("productsName", usageItem.get("domainName")!=null?usageItem.get("domainName").toString():"")
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "MemoryFreeBytes")
                                            .addField("type", "all")
                                            .addField("value", memoryFree)
                                            .time(DateUtil.parse(Convert.toStr(flow.get("time"))).getTime() / 1000, TimeUnit.SECONDS).build();
                                    batchPoints.point(freePoint);

                                    Point percentPoints = Point.measurement("zj_cloud_host")
                                            .tag("uuid", domainId)
                                            .tag("metricName", "MemoryUsedInPercent")
                                            .tag("label", "mem")
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("vm_type", "all")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("productsName", usageItem.get("domainName")!=null?usageItem.get("domainName").toString():"")
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "MemoryUsedInPercent")
                                            .addField("type", "all")
                                            .addField("value", memoryPercent)
                                            .time(DateUtil.parse(Convert.toStr(flow.get("time"))).getTime() / 1000, TimeUnit.SECONDS).build();
                                    batchPoints.point(percentPoints);
                                }
                            }
                        }
                    }

                    //磁盘
                    JSONArray ioStats = winHongDeviceService.getHostIoStat(p, token, result);
                    if (ioStats.size() > 0) {
                        for (Object ioStat : ioStats) {
                            JSONObject usageItem = JSONObject.parseObject(JSONUtil.toJsonStr(ioStat));
                            JSONArray diskIORsps = JSONObject.parseArray(usageItem.get("diskIORsps").toString());
                            //diskIORsps是云主机下的磁盘数据列表
                            for (Object diskIORsp : diskIORsps) {
                                JSONObject disk = JSONObject.parseObject(JSONUtil.toJsonStr(diskIORsp));
                                //读
                                String domainId = disk.get("domainId").toString();
                                String devName = disk.get("devName").toString();
                                JSONObject read = JSONObject.parseObject(JSONUtil.toJsonStr(disk.get("read")));
                                List<Map> data = JSONObject.parseArray(read.getString("data"), Map.class);
                                if (data.size() > 0) {
                                    for (Object item : data) {
                                        JSONObject flow = JSONObject.parseObject(JSONUtil.toJsonStr(item));
                                        Point point = Point.measurement("zj_cloud_host")
                                                .tag("uuid", domainId)
                                                .tag("label", "disk")
                                                .tag("metricName", "DiskReadBytes")
                                                .addField("productsName", devName)
                                                .tag("regionId", StringUtil.toString(p.getRegionId()))
                                                .addField("regionName", StringUtil.toString(p.getRegionName()))
                                                .tag("vm_type", "all")
                                                .tag("platformId", StringUtil.toString(p.getId()))
                                                .addField("platformName", StringUtil.toString(p.getName()))
                                                .addField("vm_metricName", "DiskReadBytes")
                                                .addField("type", "all")
                                                .addField("value", Convert.toBigDecimal(flow.get("value")))
                                                .time(DateUtil.parse(Convert.toStr(flow.get("time"))).getTime() / 1000
                                                        , TimeUnit.SECONDS).build();
                                        batchPoints.point(point);
                                    }

                                    //写
                                    JSONObject write = JSONObject.parseObject(JSONUtil.toJsonStr(disk.get("write")));
                                    List<Map> writeData = JSONObject.parseArray(write.getString("data"), Map.class);
                                    if (writeData.size() > 0) {
                                        for (Object item : writeData) {
                                            JSONObject flow = JSONObject.parseObject(JSONUtil.toJsonStr(item));
                                            Point point = Point.measurement("zj_cloud_host")
                                                    .tag("uuid", domainId)
                                                    .tag("label", "disk")
                                                    .tag("metricName", "DiskWriteBytes")
                                                    .addField("productsName", devName)
                                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                                                    .tag("vm_type", "all")
                                                    .tag("platformId", StringUtil.toString(p.getId()))
                                                    .addField("platformName", StringUtil.toString(p.getName()))
                                                    .addField("vm_metricName", "DiskWriteBytes")
                                                    .addField("type", "all")
                                                    .addField("value", Convert.toBigDecimal(flow.get("value")))
                                                    .time(DateUtil.parse(Convert.toStr(flow.get("time"))).getTime() / 1000
                                                            , TimeUnit.SECONDS).build();
                                            batchPoints.point(point);
                                        }
                                    }
                                }

                            }
                        }
                    }

                    //磁盘IO
                    JSONArray ioReqs = winHongDeviceService.getHostIoReq(p, token, result);
                    if (ioReqs.size() > 0) {
                        for (Object ioReq : ioReqs) {
                            JSONObject usageItem = JSONObject.parseObject(JSONUtil.toJsonStr(ioReq));
                            JSONArray diskIORsps = JSONObject.parseArray(usageItem.get("diskIORsps").toString());
                            //diskIORsps是云主机下的磁盘数据列表
                            for (Object diskIORsp : diskIORsps) {
                                JSONObject disk = JSONObject.parseObject(JSONUtil.toJsonStr(diskIORsp));
                                //读
                                String domainId = disk.get("domainId").toString();
                                String devName = disk.get("devName").toString();
                                JSONObject read = JSONObject.parseObject(JSONUtil.toJsonStr(disk.get("read")));
                                List<Map> data = JSONObject.parseArray(read.getString("data"), Map.class);
                                if (data.size() > 0) {
                                    for (Object item : data) {
                                        JSONObject flow = JSONObject.parseObject(JSONUtil.toJsonStr(item));
                                        Point point = Point.measurement("zj_cloud_host")
                                                .tag("uuid", domainId)
                                                .tag("label", "disk")
                                                .tag("metricName", "DiskReadOps")
                                                .addField("productsName", devName)
                                                .tag("regionId", StringUtil.toString(p.getRegionId()))
                                                .addField("regionName", StringUtil.toString(p.getRegionName()))
                                                .tag("vm_type", "all")
                                                .tag("platformId", StringUtil.toString(p.getId()))
                                                .addField("platformName", StringUtil.toString(p.getName()))
                                                .addField("vm_metricName", "DiskReadOps")
                                                .addField("type", "all")
                                                .addField("value", Convert.toBigDecimal(flow.get("value")))
                                                .time(DateUtil.parse(Convert.toStr(flow.get("time"))).getTime() / 1000, TimeUnit.SECONDS).build();
                                        batchPoints.point(point);
                                    }

                                    //写
                                    JSONObject write = JSONObject.parseObject(JSONUtil.toJsonStr(disk.get("write")));
                                    List<Map> writeData = JSONObject.parseArray(write.getString("data"), Map.class);
                                    if (writeData.size() > 0) {
                                        for (Object item : writeData) {
                                            JSONObject flow = JSONObject.parseObject(JSONUtil.toJsonStr(item));
                                            Point point = Point.measurement("zj_cloud_host")
                                                    .tag("uuid", domainId)
                                                    .tag("label", "disk")
                                                    .tag("metricName", "DiskWriteOps")
                                                    .addField("productsName", devName)
                                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                                                    .tag("vm_type", "all")
                                                    .tag("platformId", StringUtil.toString(p.getId()))
                                                    .addField("platformName", StringUtil.toString(p.getName()))
                                                    .addField("vm_metricName", "DiskWriteOps")
                                                    .addField("type", "all")
                                                    .addField("value", Convert.toBigDecimal(flow.get("value")))
                                                    .time(DateUtil.parse(Convert.toStr(flow.get("time"))).getTime() / 1000, TimeUnit.SECONDS).build();
                                            batchPoints.point(point);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    //网络
                    JSONArray networkFlows = winHongDeviceService.getNetworkFlow(p, token, result);
                    if (networkFlows.size() > 0) {
                        for (Object networkFlow : networkFlows) {
                            JSONObject usageItem = JSONObject.parseObject(JSONUtil.toJsonStr(networkFlow));
                            JSONArray networkFlowRspList = JSONObject.parseArray(usageItem.get("networkFlowRspList").toString());
                            for (Object flowResp : networkFlowRspList) {
                                JSONObject flow = JSONObject.parseObject(JSONUtil.toJsonStr(flowResp));
                                String devName = flow.get("devName").toString();
                                String domainId = flow.get("domainId").toString();
                                //网络发送速率
                                JSONObject output = JSONObject.parseObject(JSONUtil.toJsonStr(flow.get("output")));
                                List<Map> data = JSONObject.parseArray(output.getString("data"), Map.class);
                                if (data.size() > 0) {
                                    for (Map item : data) {
                                        JSONObject network = JSONObject.parseObject(JSONUtil.toJsonStr(item));
                                        Point point = Point.measurement("zj_cloud_host")
                                                .tag("uuid", domainId)
                                                .tag("label", "net")
                                                .tag("metricName", "NetworkOutBytes")
                                                .addField("productsName", devName)
                                                .tag("regionId", StringUtil.toString(p.getRegionId()))
                                                .addField("regionName", StringUtil.toString(p.getRegionName()))
                                                .tag("vm_type", "all")
                                                .tag("platformId", StringUtil.toString(p.getId()))
                                                .addField("platformName", StringUtil.toString(p.getName()))
                                                .addField("vm_metricName", "NetworkOutBytes")
                                                .addField("type", "all")
                                                .addField("value", Convert.toBigDecimal(network.get("value")))
                                                .time(DateUtil.parse(Convert.toStr(network.get("time"))).getTime() / 1000, TimeUnit.SECONDS).build();
                                        batchPoints.point(point);
                                    }
                                }

                                //网络接收速率
                                JSONObject input = JSONObject.parseObject(JSONUtil.toJsonStr(flow.get("input")));
                                List<Map> inputData = JSONObject.parseArray(input.getString("data"), Map.class);
                                if (inputData.size() > 0) {
                                    for (Map inputDatum : inputData) {
                                        JSONObject network = JSONObject.parseObject(JSONUtil.toJsonStr(inputDatum));
                                        Point point = Point.measurement("zj_cloud_host")
                                                .tag("uuid", domainId)
                                                .tag("label", "net")
                                                .tag("metricName", "NetworkInBytes")
                                                .addField("productsName", devName)
                                                .tag("regionId", StringUtil.toString(p.getRegionId()))
                                                .addField("regionName", StringUtil.toString(p.getRegionName()))
                                                .tag("vm_type", "all")
                                                .tag("platformId", StringUtil.toString(p.getId()))
                                                .addField("platformName", StringUtil.toString(p.getName()))
                                                .addField("vm_metricName", "NetworkInBytes")
                                                .addField("type", "all")
                                                .addField("value", Convert.toBigDecimal(network.get("value")))
                                                .time(DateUtil.parse(Convert.toStr(network.get("time"))).getTime() / 1000, TimeUnit.SECONDS).build();
                                        batchPoints.point(point);
                                    }

                                }

                            }
                        }
                    }
                    log.info("batchPoints--->" + batchPoints);
                    int shardIndex = XxlJobHelper.getShardIndex();
                    int shardTotal = XxlJobHelper.getShardTotal();
                    log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
                    List<Point> s = StringUtil.getShardingData(batchPoints.getPoints(), shardTotal, shardIndex);
                    influxDBTemplate.writeBatch(BatchPoints.builder().points(s).build());
                }

            }
        }
    }

    private String stateConvert(String state) {
        String target = "";
        switch (state) {
            case "1":
                target = "Running";
                break;
            case "starting":
                target = "Starting";
                break;
            case "Stopping":
                target = "Stopping";
                break;
            case "2":
                target = "Stopped";
                break;
            case "resetting":
                target = "Rebooting";
                break;
            case "deleting":
                target = "Destroying";
                break;
            case "suspend":
                target = "Stopped";
                break;
            case "suspending":
                target = "Stopping";
                break;
            default:
                target = "Unknown";
        }
        return target;
    }

    /**
     * 云宏云主机快照采集
     */
    @XxlJob("volumeWinHongSnapshotInfo")
    public void volumeWinHongSnapshotInfo() {
        List<VolumeSnapshotDTO> volumeSnapshotDTOList = new ArrayList<>();
        List<PlatformconfigDTO> platformconfigDTOList = new ArrayList<>();

        if (platformRedisDAO.get("platform") == null) {
            platformconfigDTOList = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform", platformconfigDTOList);
        }
        platformconfigDTOList = platformRedisDAO.get("platform");
        if (!platformconfigDTOList.isEmpty()) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                if (winHongAccessTokenRedisDAO.get("winhong:" + p.getId()) != null) {
                    String token = winHongDeviceService.getToken(p);
                    if (token==null){
                        log.info("获取云宏token失败");
                        return;
                    }
                    JSONArray volumeSnapshots = winHongDeviceService.getSnapshots(p, token);
                    for (int i = 0; i < volumeSnapshots.size(); i++) {
                        VolumeSnapshotDTO volumeSnapshotDTO = new VolumeSnapshotDTO();
                        JSONObject jsonObject = volumeSnapshots.getJSONObject(i);
                        String domainId = jsonObject.getString("domainId");
                        String name = jsonObject.getString("name");
                        String description = jsonObject.getString("description");
                        String primaryStorageUuid = UuidUtils.generateUuid();
                        String type = "主机快照";
                        String status = jsonObject.getString("status");
                        if (status.equals("1")) {
                            status = "Enabled";
                        } else {
                            status = "Disabled";
                        }
                        Boolean isMemorySnapshot = jsonObject.getBoolean("isMemorySnapshot");
//                        if (jsonObject.getBoolean("isMemorySnapshot")) {
//                            type = "整机";
//                        } else {
//                            type = "仅磁盘";
//                        }
                        String volumeType = "Root";
                        String latest = "true";
                        Long platformId = p.getId();
                        String platformName = p.getName();
                        LocalDateTime dateTime = Instant.ofEpochMilli(jsonObject.getLong("creationTime"))
                                .atZone(ZoneId.systemDefault())
                                .toLocalDateTime();
                        volumeSnapshotDTO.setCreateTime(dateTime);
                        long timestamp = jsonObject.getLong("creationTime") * 1000;
                        volumeSnapshotDTO.setVCreateDate(new Date(timestamp));
                        volumeSnapshotDTO.setVUpdateDate(new Date(timestamp));
                        volumeSnapshotDTO.setUuid(UuidUtils.generateUuid());
                        volumeSnapshotDTO.setName(name);
                        volumeSnapshotDTO.setDescription(description);
                        volumeSnapshotDTO.setHostUuid(domainId);
                        volumeSnapshotDTO.setStatus(status);
                        //查找根云盘uuid
//                        String voUuid=volumeApi.getVolumeByVmUuid(domainId).getData();
//                        volumeSnapshotDTO.setVolumeUuid(voUuid);
                        volumeSnapshotDTO.setPrimaryStorageUuid(primaryStorageUuid);
                        volumeSnapshotDTO.setType(type);
                        volumeSnapshotDTO.setVolumeType(volumeType);
                        volumeSnapshotDTO.setLatest(latest);
                        volumeSnapshotDTO.setPlatformName(platformName);
                        volumeSnapshotDTO.setPlatformId(platformId);
                        volumeSnapshotDTO.setTypeName("winhong");
                        volumeSnapshotDTO.setIsMemory(isMemorySnapshot);
                        volumeSnapshotDTO.setFormat("raw");
                        volumeSnapshotDTOList.add(volumeSnapshotDTO);
                    }

                }

            }

        }
        if (!volumeSnapshotDTOList.isEmpty()) {
            Long volumeSnapshotCount = volumeApi.getVolumeSnapshotCount("winhong").getData();
            List<VolumeSnapshotDTO> dtos = volumeApi.getAllVolumeSnapshots("winhong").getData();
            if (volumeSnapshotCount == 0) {
                volumeApi.addVolumeSnapshots(volumeSnapshotDTOList);
            } else {
                List<VolumeSnapshotDTO> collect = volumeSnapshotDTOList.stream()
                        .filter(item -> !dtos.stream().map(VolumeSnapshotDTO::getUuid)
                                .toList().contains(item.getUuid()))
                        .collect(Collectors.toList());
                List<VolumeSnapshotDTO> deleteTarget = dtos.stream()
                        .filter(item -> !(volumeSnapshotDTOList.stream().map(VolumeSnapshotDTO::getUuid)
                                .toList().contains(item.getUuid()))
                        ).collect(Collectors.toList());
                //修改列表
                List<VolumeSnapshotDTO> updateDTOs = volumeSnapshotDTOList.stream()
                        .filter(item ->dtos.stream().map(VolumeSnapshotDTO::getUuid)
                                .toList().contains(item.getUuid()))
                        .toList();

                if (!updateDTOs.isEmpty()) {
                    volumeApi.updateVolumeSnapshots(updateDTOs);
                }
                if (!deleteTarget.isEmpty()) {
                    volumeApi.delVolumeSnapshots(deleteTarget);
                }
                if (!collect.isEmpty()) {
                    volumeApi.addVolumeSnapshots(collect);
                }
            }
        }
    }





    @XxlJob("winHongLogin")
    public void loginInfo() {

        List<PlatformconfigDTO> platformconfigDTOList = new ArrayList<>();
        if (platformRedisDAO.get("platform") == null) {
            platformconfigDTOList = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform", platformconfigDTOList);
        }

        platformconfigDTOList = platformRedisDAO.get("platform");
        if (platformconfigDTOList.size() > 0) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                if (p.getTypeCode().equals("winhong") && winHongAccessTokenRedisDAO.get("winhong:" + p.getId()) == null) {
                    JSONObject parame = new JSONObject();
                    parame.put("user", p.getUsername());
                    parame.put("pwd", p.getPassword());
                    try {
                        HttpRequest res = HttpRequest.post(p.getUrl() + WinHongApiConstant.LOGIN).body(parame.toJSONString());
                        log.info("获取登录数据：" + res.execute().body());
                        // 请求成功
                        if (200 == res.execute().getStatus()) {
                            winHongAccessTokenRedisDAO.set("winhong:" + p.getId(), JSONObject.parseObject(res.execute().body()));
                        }
                    } catch (Exception e) {
                        log.info("获取授权异常：" + e.getMessage());
                    }
                }
            }
        }
    }


}
