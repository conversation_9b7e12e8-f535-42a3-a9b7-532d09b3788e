package cn.iocoder.zj.module.collection.service.vmware;

import cn.iocoder.zj.module.collection.collect.vmware.ConnectedVimServiceBase;

/**
 * @ClassName : AbstractComputerResourceSummary  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/7/24  16:17
 */
public abstract  class AbstractComputerResourceSummary {
    protected ConnectedVimServiceBase cs = null; // Connection class declaration

    public ConnectedVimServiceBase getCs() {
        return cs;
    }

    public void setCs(ConnectedVimServiceBase cs) {
        this.cs = cs;
    }


}
