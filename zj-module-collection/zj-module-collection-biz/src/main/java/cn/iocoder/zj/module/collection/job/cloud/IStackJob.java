package cn.iocoder.zj.module.collection.job.cloud;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.framework.influx.config.InfluxDBTemplate;
import cn.iocoder.zj.module.collection.job.volume.MediaType;
import cn.iocoder.zj.module.collection.service.cloud.IStackDeviceService;
import cn.iocoder.zj.module.collection.util.PowerStateUtil;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.monitor.api.eip.EipApi;
import cn.iocoder.zj.module.monitor.api.eip.dto.EipCreateReqDto;
import cn.iocoder.zj.module.monitor.api.hostinfo.HostInfoApi;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.hostnic.HostNicApi;
import cn.iocoder.zj.module.monitor.api.hostnic.dto.HostNicCreateReqDto;
import cn.iocoder.zj.module.monitor.api.hostsecgroup.HostSecgroupApi;
import cn.iocoder.zj.module.monitor.api.hostsecgroup.dto.HostSecgroupCreateReqDto;
import cn.iocoder.zj.module.monitor.api.secgroup.SecgroupApi;
import cn.iocoder.zj.module.monitor.api.secgroup.dto.SecgroupCreateReqDto;
import cn.iocoder.zj.module.monitor.api.secgrouprule.SecgroupruleApi;
import cn.iocoder.zj.module.monitor.api.secgrouprule.dto.SecgroupRuleCreateReqDto;
import cn.iocoder.zj.module.monitor.api.valume.VolumeApi;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeDTO;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class IStackJob {
    @Resource
    InfluxDBTemplate influxDBTemplate;
    @Resource
    PlatformRedisDAO platformRedisDAO;
    @Resource
    IStackDeviceService iStackDeviceService;
    @Resource
    HostInfoApi hostInfoApi;
    @Resource
    VolumeApi volumeApi;
    @Resource
    HostNicApi hostNicApi;
    @Resource
    EipApi eipApi;
    @Resource
    SecgroupApi secgroupApi;
    @Autowired
    @Resource
    SecgroupruleApi secgroupruleApi;
    @Resource
    HostSecgroupApi hostSecgroupApi;


    private final ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());

    private static final BigDecimal BYTES_TO_GB = new BigDecimal(1024 * 1024 * 1024);
    private static final BigDecimal HUNDRED = new BigDecimal(100);
    private static final String DATA_DISK_TYPE = "DATADISK";
    public void IStackCloudJobList() {
        iStackCloud();
//        collectToInflux();
    }

    @XxlJob("iStackCloud")
    public void iStackCloud() {

        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                .filter(dto -> "istack".equals(dto.getTypeCode()))
                .toList();
        if (filteredList.isEmpty()) return;
        try {
            for (PlatformconfigDTO platformconfigDTO : filteredList) {
                handlePlatformConfigExecutor(platformconfigDTO);
            }
        } catch (Exception e) {
            StackTraceElement stackTraceElement = e.getStackTrace()[0];
            XxlJobHelper.handleFail("istack中IStackCloud异常" + stackTraceElement);
        }
    }

    private void handlePlatformConfigExecutor(PlatformconfigDTO p) {
        JSONArray clouds = iStackDeviceService.getClouds(p);
        CompletableFuture<List<HostInfoRespCreateReqDTO>> hostInfoFuture = processHostInfo(clouds, p);
        CompletableFuture<List<HostNicCreateReqDto>> hostNicFuture = processHostNic(clouds, p);
        CompletableFuture<List<EipCreateReqDto>> eipFuture = processEip(clouds, p);
        processScegroup(clouds, p);
        List<HostInfoRespCreateReqDTO> hostInfoRespCreateReqDTOList = hostInfoFuture.join();
        List<HostNicCreateReqDto> hostNicCreateReqDto = hostNicFuture.join();
        List<EipCreateReqDto> eipCreateReqDto = eipFuture.join();
        updateHostInfo(hostInfoRespCreateReqDTOList, p.getId());
        updateNicInfo(hostNicCreateReqDto, p.getId());
        updateEip(eipCreateReqDto, p.getId());
        List<VolumeDTO> volumeFuture = volumeInfo(clouds, p);
        updateVolumeInfo(volumeFuture, p.getId());

    }

    private void updateVolumeInfo(List<VolumeDTO> volumeDTOList, Long id) {
        if (!volumeDTOList.isEmpty()) {
            List<VolumeDTO> existingVolume = volumeApi.getVolumesByPlatformId(id);
            if (existingVolume.isEmpty()) {
                volumeApi.addVolumes(volumeDTOList);
            } else {
                //比较uuid不存在删除
                List<VolumeDTO> deleteTarget = existingVolume.stream()
                        .filter(volumeDTO -> volumeDTOList.stream()
                                .noneMatch(item -> item.getUuid().equals(volumeDTO.getUuid())))
                        .toList();

                //新增云盘
                List<VolumeDTO> collect = volumeDTOList.stream()
                        .filter(volumeDTO -> existingVolume.stream()
                                .noneMatch(item -> item.getUuid().equals(volumeDTO.getUuid())))
                        .collect(Collectors.toList());
                //修改云盘
                List<VolumeDTO> updateDTOs = volumeDTOList.stream()
                        .filter(volumeDTO -> existingVolume.stream()
                                .anyMatch(item -> item.getUuid().equals(volumeDTO.getUuid())))
                        .toList();

                if (!collect.isEmpty()) {
                    volumeApi.addVolumes(collect);
                }
                if (!updateDTOs.isEmpty()){
                    volumeApi.updateVolumes(updateDTOs);
                }
                if (!deleteTarget.isEmpty()) {
                    volumeApi.delVolumes(deleteTarget);
                }
            }
        }
    }

    private void updateHostInfo(List<HostInfoRespCreateReqDTO> hostInfoRespCreateReqDTOList, Long id) {
        if (!hostInfoRespCreateReqDTOList.isEmpty()) {
            List<HostInfoRespCreateReqDTO> existingHosts = hostInfoApi.getVmByPlatformId(id);
            if (existingHosts.isEmpty()) {
                hostInfoApi.adds(hostInfoRespCreateReqDTOList);
            } else {
                Map<String, HostInfoRespCreateReqDTO> existingHostMap = existingHosts.stream()
                        .collect(Collectors.toMap(HostInfoRespCreateReqDTO::getUuid, hostInfo -> hostInfo));

                List<HostInfoRespCreateReqDTO> newEntries = new ArrayList<>();
                List<HostInfoRespCreateReqDTO> updateEntries = new ArrayList<>();
                List<HostInfoRespCreateReqDTO> deleteEntries = existingHosts.stream()
                        .filter(item -> !hostInfoRespCreateReqDTOList.stream()
                                .map(HostInfoRespCreateReqDTO::getUuid)
                                .toList().contains(item.getUuid()))
                        .collect(Collectors.toList());

                for (HostInfoRespCreateReqDTO newHostInfo : hostInfoRespCreateReqDTOList) {
                    HostInfoRespCreateReqDTO existingHost = existingHostMap.get(newHostInfo.getUuid());
                    if (existingHost == null) {
                        newEntries.add(newHostInfo);
                    } else if (!existingHost.equals(newHostInfo)) {
                        updateEntries.add(newHostInfo);
                    }
                }
                if (!deleteEntries.isEmpty()) {
                    deleteEntries.forEach(item -> item.setDeleted(1).setState("Destroyed"));
                    hostInfoApi.updates(deleteEntries);
                }

                hostInfoApi.updates(updateEntries);
                hostInfoApi.adds(newEntries);
            }
        }
    }

    private void updateNicInfo(List<HostNicCreateReqDto> hostNicCreateReqDtoList, Long id) {
        if (!hostNicCreateReqDtoList.isEmpty()) {
            List<HostNicCreateReqDto> nowNic = hostNicApi.getHostNicsByPlatformId(id).getData();
            //根据uuid筛选出hostNicCreateReqDtoList里需要新增的数据
            Set<String> nowNicUuids = nowNic.stream()
                    .map(HostNicCreateReqDto::getUuid)
                    .collect(Collectors.toSet());

            List<HostNicCreateReqDto> addHostNicList = hostNicCreateReqDtoList.stream()
                    .filter(hostNicCreateReqDto -> !nowNicUuids.contains(hostNicCreateReqDto.getUuid()))
                    .toList();
            if (!addHostNicList.isEmpty()) {
                hostNicApi.addHostNics(addHostNicList);
            }
            //查询updateList 并将nowNicUuids 的id 赋值给updateHostNicList
            List<HostNicCreateReqDto> updateHostNicList = hostNicCreateReqDtoList.stream()
                    .filter(hostNicCreateReqDto -> nowNicUuids.contains(hostNicCreateReqDto.getUuid()))
                    .toList().stream().map(hostNicCreateReqDto -> {
                        return hostNicCreateReqDto.setId(nowNic.stream()
                                .filter(hostNicRespDTO -> hostNicRespDTO.getUuid().equals(hostNicCreateReqDto.getUuid()))
                                .findFirst()
                                .map(HostNicCreateReqDto::getId)
                                .orElse(null));
                    }).toList();
            if (!updateHostNicList.isEmpty()) {
                hostNicApi.updateHostNics(updateHostNicList);
            }
            //根据uuid 判断在nowNic不在hostNicCreateReqDtoList里的数据
            Set<String> hostNicUuids = hostNicCreateReqDtoList.stream()
                    .map(HostNicCreateReqDto::getUuid)
                    .collect(Collectors.toSet());
            List<HostNicCreateReqDto> delHostNicList = nowNic.stream()
                    .filter(hostNicRespDTO -> !hostNicUuids.contains(hostNicRespDTO.getUuid()))
                    .toList();
            if (!delHostNicList.isEmpty()) {
                hostNicApi.delHostNics(delHostNicList);
            }
        }
    }

    private void updateEip(List<EipCreateReqDto> eipCreateReqDtoList, Long id) {
        if (!eipCreateReqDtoList.isEmpty()) {
            List<EipCreateReqDto> nowEip = eipApi.getEipsByPlatformId(id).getData();
            //根据uuid筛选出eipCreateReqDtoList里需要新增的数据
            Set<String> nowEipUuids = nowEip.stream()
                    .map(EipCreateReqDto::getUuid)
                    .collect(Collectors.toSet());

            List<EipCreateReqDto> addEipList = eipCreateReqDtoList.stream()
                    .filter(eipCreateReqDto -> !nowEipUuids.contains(eipCreateReqDto.getUuid()))
                    .toList();
            if (!addEipList.isEmpty()) {
                eipApi.addEips(addEipList);
            }
            //查询updateList 并将nowEipUuids 的id 赋值给updateEipList
            List<EipCreateReqDto> updateEipList = eipCreateReqDtoList.stream()
                    .filter(hostNicCreateReqDto -> nowEipUuids.contains(hostNicCreateReqDto.getUuid()))
                    .toList().stream().map(hostEipReqDto -> {
                        return hostEipReqDto.setId(nowEip.stream()
                                .filter(eipRespDto -> eipRespDto.getUuid().equals(hostEipReqDto.getUuid()))
                                .findFirst()
                                .map(EipCreateReqDto::getId)
                                .orElse(null));
                    }).toList();
            if (!updateEipList.isEmpty()) {
                eipApi.updateEips(updateEipList);
            }
            //根据uuid 判断在nowEip不在eipCreateReqDtoList里的数据
            Set<String> hostEipUuids = eipCreateReqDtoList.stream()
                    .map(EipCreateReqDto::getUuid)
                    .collect(Collectors.toSet());
            List<EipCreateReqDto> delEipList = nowEip.stream()
                    .filter(hostNicRespDTO -> !hostEipUuids.contains(hostNicRespDTO.getUuid()))
                    .toList();
            if (!delEipList.isEmpty()) {
                eipApi.delEips(delEipList);
            }
        }
    }

    private void updateSecgroup(List<SecgroupCreateReqDto> secgroupCreateReqDtoList, Long id) {
        if (!secgroupCreateReqDtoList.isEmpty()) {
            List<SecgroupCreateReqDto> now = secgroupApi.getSecgroupsByPlatformId(id).getData();
            //根据uuid筛选出eipCreateReqDtoList里需要新增的数据
            Set<String> nowUuids = now.stream()
                    .map(SecgroupCreateReqDto::getUuid)
                    .collect(Collectors.toSet());

            List<SecgroupCreateReqDto> addList = secgroupCreateReqDtoList.stream()
                    .filter(eipCreateReqDto -> !nowUuids.contains(eipCreateReqDto.getUuid()))
                    .toList();
            if (!addList.isEmpty()) {
                secgroupApi.addSecgroups(addList);
            }
            //查询updateList 并将nowEipUuids 的id 赋值给updateEipList
            List<SecgroupCreateReqDto> updateList = secgroupCreateReqDtoList.stream()
                    .filter(hostNicCreateReqDto -> nowUuids.contains(hostNicCreateReqDto.getUuid()))
                    .toList().stream().map(reqDto -> {
                        return reqDto.setId(now.stream()
                                .filter(respDto -> respDto.getUuid().equals(reqDto.getUuid()))
                                .findFirst()
                                .map(SecgroupCreateReqDto::getId)
                                .orElse(null));
                    }).toList();
            if (!updateList.isEmpty()) {
                secgroupApi.updateSecgroups(updateList);
            }
            //根据uuid 判断在nowEip不在eipCreateReqDtoList里的数据
            Set<String> uuids = secgroupCreateReqDtoList.stream()
                    .map(SecgroupCreateReqDto::getUuid)
                    .collect(Collectors.toSet());
            List<SecgroupCreateReqDto> delList = now.stream()
                    .filter(respDTO -> !uuids.contains(respDTO.getUuid()))
                    .toList();
            if (!delList.isEmpty()) {
                secgroupApi.delSecgroups(delList);
            }
        }
    }
    private void updateSecgroupRule(List<SecgroupRuleCreateReqDto> secgroupCreateReqDtoList, Long id) {
        if (!secgroupCreateReqDtoList.isEmpty()) {
            List<SecgroupRuleCreateReqDto> now = secgroupruleApi.getSecgroupRulesByPlatformId(id).getData();
            //根据uuid筛选出eipCreateReqDtoList里需要新增的数据
            Set<String> nowUuids = now.stream()
                    .map(SecgroupRuleCreateReqDto::getUuid)
                    .collect(Collectors.toSet());

            List<SecgroupRuleCreateReqDto> addList = secgroupCreateReqDtoList.stream()
                    .filter(eipCreateReqDto -> !nowUuids.contains(eipCreateReqDto.getUuid()))
                    .toList();
            if (!addList.isEmpty()) {
                secgroupruleApi.addSecgroupRules(addList);
            }
            //查询updateList 并将nowEipUuids 的id 赋值给updateEipList
            List<SecgroupRuleCreateReqDto> updateList = secgroupCreateReqDtoList.stream()
                    .filter(reqDto -> nowUuids.contains(reqDto.getUuid()))
                    .toList().stream().map(reqDto -> {
                        return reqDto.setId(now.stream()
                                .filter(respDto -> respDto.getUuid().equals(reqDto.getUuid()))
                                .findFirst()
                                .map(SecgroupRuleCreateReqDto::getId)
                                .orElse(null));
                    }).toList();
            if (!updateList.isEmpty()) {
                secgroupruleApi.updateSecgroupRules(updateList);
            }
            //根据uuid 判断在nowEip不在eipCreateReqDtoList里的数据
            Set<String> uuids = secgroupCreateReqDtoList.stream()
                    .map(SecgroupRuleCreateReqDto::getUuid)
                    .collect(Collectors.toSet());
            List<SecgroupRuleCreateReqDto> delList = now.stream()
                    .filter(respDTO -> !uuids.contains(respDTO.getUuid()))
                    .toList();
            if (!delList.isEmpty()) {
                secgroupruleApi.delSecgroupRules(delList);
            }
        }
    }
    private void updateHostSecgroup(List<HostSecgroupCreateReqDto> secgroupCreateReqDtoList, Long id) {
        if (!secgroupCreateReqDtoList.isEmpty()) {
            List<HostSecgroupCreateReqDto> now = hostSecgroupApi.getHostSecgroupsByPlatformId(id).getData();
            //根据uuid筛选出eipCreateReqDtoList里需要新增的数据
            Set<String> nowUuids = now.stream()
                    .map(r -> r.getHostUuid() + r.getSecgroupUuid())
                    .collect(Collectors.toSet());

            List<HostSecgroupCreateReqDto> addList = secgroupCreateReqDtoList.stream()
                    .filter(reqDto -> !nowUuids.contains(reqDto.getHostUuid() + reqDto.getSecgroupUuid()))
                    .toList();
            if (!addList.isEmpty()) {
                hostSecgroupApi.addHostSecgroups(addList);
            }
            //根据uuid 判断在nowEip不在eipCreateReqDtoList里的数据
            Set<String> uuids = secgroupCreateReqDtoList.stream()
                    .map(r -> r.getHostUuid() + r.getSecgroupUuid())
                    .collect(Collectors.toSet());
            List<HostSecgroupCreateReqDto> delList = now.stream()
                    .filter(respDTO -> !uuids.contains(respDTO.getHostUuid()+ respDTO.getSecgroupUuid()))
                    .toList();
            if (!delList.isEmpty()) {
                hostSecgroupApi.delHostSecgroups(delList);
            }
        }
    }

    private List<VolumeDTO> volumeInfo(JSONArray clouds, PlatformconfigDTO p) {
        List<VolumeDTO> volumeDTOList = new ArrayList<>();
        for (int l = 0; l < clouds.size(); l++) {
            JSONObject jsonObject = clouds.getJSONObject(l);
            JSONObject vmuse = iStackDeviceService.getVmuse(p, jsonObject.getString("id"));
            JSONArray diskList = iStackDeviceService.getVmDetail(p, jsonObject.getString("id"));
            if (!diskList.isEmpty()) {
                for (int i = 0; i < diskList.size(); i++) {
                    VolumeDTO volumeDTO = new VolumeDTO();
                    JSONObject disk = diskList.getJSONObject(i);
                    volumeDTO.setDescription("IStack云盘描述");    //描述
                    volumeDTO.setName(disk.getString("name"));
                    volumeDTO.setFormat(disk.getString("volume_type"));  //云盘格式
                    long total = disk.getLong("size") * 1024 * 1024 * 1024;
                    volumeDTO.setSize(total);
                    volumeDTO.setActualSize(total);
                    String type = disk.getString("type");
                    JSONObject object = (JSONObject) vmuse.getJSONObject("pageResult").getJSONArray("rows").get(0);
                    if (type.equals("ROOT")) {
                        String vmVolumeUtil = object.getString("vm_volume_util");
                        if (StrUtil.isEmpty(vmVolumeUtil)) {
                            vmVolumeUtil = "0";
                        }
                        volumeDTO.setActualRatio(vmVolumeUtil);
                        volumeDTO.setType("Root");
                        BigDecimal diskUtilRatio = new BigDecimal(vmVolumeUtil).divide(BigDecimal.valueOf(100));
                        ;
                        long diskToal = diskUtilRatio.multiply(BigDecimal.valueOf(total)).setScale(0, BigDecimal.ROUND_HALF_UP).longValue();
                        volumeDTO.setActualUse(diskToal);
                        volumeDTO.setActualFree(total - diskToal);
                    } else {
                        String vmDiskUtil = object.getString("vm_disk_util_avg");
                        volumeDTO.setType("Data");
                        if (StrUtil.isEmpty(vmDiskUtil)) {
                            vmDiskUtil = "0";
                        }
                        volumeDTO.setActualRatio(vmDiskUtil);
                        BigDecimal voUtilRatio = new BigDecimal(vmDiskUtil).divide(BigDecimal.valueOf(100));
                        ;
                        long diskToal = voUtilRatio.multiply(BigDecimal.valueOf(total)).setScale(0, BigDecimal.ROUND_HALF_UP).longValue();
                        volumeDTO.setActualUse(diskToal);
                        volumeDTO.setActualFree(total - diskToal);
                    }
                    String timeStr = disk.getString("create_time") != null ? disk.getString("create_time") : null;
                    Instant instant = null;
                    if (timeStr != null) {
                        instant = Instant.parse(timeStr);
                    }
                    if (instant != null) {
                        Date date1 = Date.from(instant);
                        volumeDTO.setVCreateDate(date1);
                        volumeDTO.setVUpdateDate(date1);
                    }
                    volumeDTO.setState("Enabled");
                    volumeDTO.setUuid(disk.getString("id"));
                    volumeDTO.setStatus("Ready");
                    volumeDTO.setPlatformId(p.getId());
                    volumeDTO.setPlatformName(p.getName());
                    volumeDTO.setVmInstanceUuid(disk.getString("instance_uuid"));
                    volumeDTO.setVmInstanceName(disk.getString("instance_name"));
                    volumeDTO.setPrimaryStorageUuid(disk.getString("storageid"));
                    volumeDTO.setPrimaryStorageName(disk.getString("storage"));
                    String hardDiskStatus = disk.getString("harddisk_status");
                    if(hardDiskStatus!=null){
                        volumeDTO.setIsMount(hardDiskStatus.equals("ATTACHED"));
                    }else {
                        volumeDTO.setIsMount(null);
                    }
                    volumeDTO.setMediaType(MediaType.SSD.getEnName());
                    volumeDTO.setDeleted(0L);
                    volumeDTOList.add(volumeDTO);
                }
            }

        }
        return volumeDTOList;
    }


    private CompletableFuture<List<HostInfoRespCreateReqDTO>> processHostInfo(JSONArray clouds, PlatformconfigDTO p) {
        List<CompletableFuture<HostInfoRespCreateReqDTO>> futures = clouds.stream()
                .map(vm -> CompletableFuture.supplyAsync(() -> {
                    try {
                        JSONObject jsonObject = (JSONObject) vm;
                        JSONArray items = iStackDeviceService.getRelaTimeData(p, jsonObject.getString("id"), false);
                        return extractHostInfos(vm, p, items);
                    } catch (Exception e) {
                        StackTraceElement stackTraceElement = e.getStackTrace()[0];
                        log.error("Error extracting host info for VM: " + stackTraceElement);

                        return null;
                    }
                }, executor))
                .toList();
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .map(CompletableFuture::join)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()));
    }

    private CompletableFuture<List<HostNicCreateReqDto>> processHostNic(JSONArray clouds, PlatformconfigDTO p) {
        List<CompletableFuture<List<HostNicCreateReqDto>>> futures = clouds.stream()
                .map(vm -> CompletableFuture.supplyAsync(() -> {
                    try {
                        JSONObject jsonObject = (JSONObject) vm;
                        JSONArray items = iStackDeviceService.getCloudNics(p, jsonObject.getString("id"));
                        return extractHostNics(vm, p, items);
                    } catch (Exception e) {
                        StackTraceElement stackTraceElement = e.getStackTrace()[0];
                        log.error("Error extracting host info for VM: " + stackTraceElement);

                        return null;
                    }
                }, executor))
                .toList();
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .map(CompletableFuture::join)
                        .flatMap(Collection::stream)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()));
    }

    private void processScegroup(JSONArray clouds, PlatformconfigDTO p) {
        JSONArray items = iStackDeviceService.getSecgroup(p);
        List<SecgroupCreateReqDto> secgroupCreateReqDtoList = new ArrayList<>();
        List<SecgroupRuleCreateReqDto> secgroupRuleCreateReqDtoList = new ArrayList<>();
        List<HostSecgroupCreateReqDto> hostSecgroupCreateReqDtoList = new ArrayList<>();
        for (Object item : items) {
            JSONObject secObj = (JSONObject) item;
            //安全组
            SecgroupCreateReqDto scegroupCreateReqDTO = new SecgroupCreateReqDto();
            scegroupCreateReqDTO.setName(secObj.getString("name"));
            scegroupCreateReqDTO.setPlatformId(p.getId());
            scegroupCreateReqDTO.setPlatformName(p.getName());
            scegroupCreateReqDTO.setUuid(secObj.getString("id"));
            scegroupCreateReqDTO.setDescription(secObj.getString("description"));
            scegroupCreateReqDTO.setStatus("Enabled");
            secgroupCreateReqDtoList.add(scegroupCreateReqDTO);
            //规则
            JSONArray rules = secObj.getJSONArray("security_group_rules");
            for (Object rule : rules) {
                JSONObject ruleObj = (JSONObject) rule;
                SecgroupRuleCreateReqDto secgroupRuleCreateReqDto = new SecgroupRuleCreateReqDto();
                secgroupRuleCreateReqDto.setUuid(ruleObj.getString("uuid"));
                secgroupRuleCreateReqDto.setSecgroupUuid(secObj.getString("id"));
                //allow | deny
                secgroupRuleCreateReqDto.setAction(ruleObj.getString("action").equals("permit") || ruleObj.getInteger("action") == 0 ? "allow" : "deny");
                secgroupRuleCreateReqDto.setProtocol(ruleObj.getString("protocol").toLowerCase());
                secgroupRuleCreateReqDto.setPlatformId(p.getId());
                secgroupRuleCreateReqDto.setPlatformName(p.getName());
                secgroupRuleCreateReqDto.setCidr(ruleObj.getString("remote_ip_prefix") == null ? ruleObj.getString("remote_cidr") : ruleObj.getString("remote_ip_prefix"));
                if (ruleObj.getInteger("port_range_min") != null && ruleObj.getInteger("port_range_min") > 0
                        && ruleObj.getInteger("port_range_max") != null && ruleObj.getInteger("port_range_max") > 0) {
                    secgroupRuleCreateReqDto.setPorts(ruleObj.getInteger("port_range_min") + "-" + ruleObj.getInteger("port_range_max"));
                }
                if (ruleObj.getInteger("port_range_min") != null && ruleObj.getInteger("port_range_min") > 0
                        && ruleObj.getInteger("port_range_max") != null && ruleObj.getInteger("port_range_max") > 0) {
                    secgroupRuleCreateReqDto.setPorts(ruleObj.getInteger("port_range_min") + "-" + ruleObj.getInteger("port_range_max"));
                }
                if (ruleObj.getInteger("port_start") != null && ruleObj.getInteger("port_start") > 0
                        && ruleObj.getInteger("port_end") != null && ruleObj.getInteger("port_end") > 0) {
                    secgroupRuleCreateReqDto.setPorts(ruleObj.getInteger("port_start") + "-" + ruleObj.getInteger("port_end"));
                    secgroupRuleCreateReqDto.setPriority(ruleObj.getInteger("priority"));
                    secgroupRuleCreateReqDto.setDirection(Objects.equals(ruleObj.getString("direction"), "ingress") ? "in" : "out");
                }
                secgroupRuleCreateReqDtoList.add(secgroupRuleCreateReqDto);
            }
            // 关联虚拟机
            JSONArray secinstance = secObj.getJSONArray("instance_info");
            for (Object secVm : secinstance) {
                JSONObject secVmObj = (JSONObject) secVm;
                HostSecgroupCreateReqDto hostSecgroupCreateReqDto = new HostSecgroupCreateReqDto();
                hostSecgroupCreateReqDto.setPlatformId(p.getId());
                hostSecgroupCreateReqDto.setPlatformName(p.getName());
                hostSecgroupCreateReqDto.setSecgroupUuid(secObj.getString("id"));
                hostSecgroupCreateReqDto.setHostUuid(secVmObj.getString("instance_uuid"));
                hostSecgroupCreateReqDtoList.add(hostSecgroupCreateReqDto);
            }
        }
        updateSecgroup(secgroupCreateReqDtoList, p.getId());
        updateSecgroupRule(secgroupRuleCreateReqDtoList, p.getId());
        updateHostSecgroup(hostSecgroupCreateReqDtoList, p.getId());
    }

    private CompletableFuture<List<EipCreateReqDto>> processEip(JSONArray clouds, PlatformconfigDTO p) {
        CompletableFuture<List<EipCreateReqDto>> futures = CompletableFuture.supplyAsync(() -> {
            try {
                JSONArray items = iStackDeviceService.getEips(p, "");
                return extractEips(new Object(), p, items);
            } catch (Exception e) {
                StackTraceElement stackTraceElement = e.getStackTrace()[0];
                log.error("Error extracting host eip for VM: " + stackTraceElement);
                return null;
            }
        }, executor);
        return futures;
    }


    private HostInfoRespCreateReqDTO extractHostInfos(Object vm, PlatformconfigDTO p, JSONArray items) {
        HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO = new HostInfoRespCreateReqDTO();
        JSONObject jsonObject = (JSONObject) vm;
        hostInfoRespCreateReqDTO.setGuestOsType(StringUtil.isNotEmpty(jsonObject.getString("ostypename")) ? jsonObject.getString("ostypename") : "");
        hostInfoRespCreateReqDTO.setIp(StringUtil.isNotEmpty(jsonObject.getString("private_ip")) ? jsonObject.getString("private_ip") : "");
        hostInfoRespCreateReqDTO.setVipIp("");
        String addresses = JSONObject.parseObject(jsonObject.getJSONObject("addresses").getJSONArray("vpc-yw").get(0).toString()).getString("OS-EXT-IPS-MAC:mac_addr");
        hostInfoRespCreateReqDTO.setMac(StringUtil.isNotEmpty(addresses) ? addresses : "");
        hostInfoRespCreateReqDTO.setArchitecture("x86_64");
        hostInfoRespCreateReqDTO.setUuid(jsonObject.getString("id"));
        hostInfoRespCreateReqDTO.setState(stateConvert(jsonObject.getString("status")));
        hostInfoRespCreateReqDTO.setName(jsonObject.getString("name"));
        hostInfoRespCreateReqDTO.setVCreateDate(convertStringToDate(jsonObject.getString("created")));
        hostInfoRespCreateReqDTO.setClusterName(jsonObject.getString("availability_zone"));
        hostInfoRespCreateReqDTO.setZoneName(jsonObject.getString("zone_name"));
        hostInfoRespCreateReqDTO.setZoneUuid(jsonObject.getString("zone_id"));
        hostInfoRespCreateReqDTO.setHardwareUuid(jsonObject.getString("host_id"));
        hostInfoRespCreateReqDTO.setHardwareName(jsonObject.getString("host_name"));
        hostInfoRespCreateReqDTO.setImageUuid(jsonObject.getJSONObject("image").isEmpty() ? "" : jsonObject.getJSONObject("image").getString("id"));
        BigDecimal multiply = jsonObject.getJSONObject("flavor").getBigDecimal("ram").multiply(new BigDecimal(1024 * 1024 * 1024));
        hostInfoRespCreateReqDTO.setMemorySize(multiply.longValue());
        BigDecimal disk = jsonObject.getJSONObject("flavor").getBigDecimal("sys_disk_size").multiply(new BigDecimal(1024 * 1024 * 1024));
        hostInfoRespCreateReqDTO.setTotalDiskCapacity(disk);
        hostInfoRespCreateReqDTO.setCpuNum(jsonObject.getJSONObject("flavor").getInteger("vcpus"));
        processMetrics(items, hostInfoRespCreateReqDTO);

        hostInfoRespCreateReqDTO.setPlatformId(p.getId());
        hostInfoRespCreateReqDTO.setPlatformName(p.getName());
        hostInfoRespCreateReqDTO.setTypeName(p.getTypeCode());
        hostInfoRespCreateReqDTO.setTenantId(p.getTenantId());
        hostInfoRespCreateReqDTO.setRegionId(p.getRegionId());
        hostInfoRespCreateReqDTO.setType("UserVm");
        hostInfoRespCreateReqDTO.setDeleted(0);

        String status = PowerStateUtil.powerStateConvert(stateConvert(jsonObject.getString("status")));
        hostInfoRespCreateReqDTO.setPowerState(status);
        hostInfoRespCreateReqDTO.setImageName(jsonObject.getJSONObject("image").isEmpty() ? "" : jsonObject.getJSONObject("image").getString("name"));
        hostInfoRespCreateReqDTO.setAutoInitType("NeverStop");
        hostInfoRespCreateReqDTO.setGuideMode("Legacy");
        hostInfoRespCreateReqDTO.setIso(jsonObject.getJSONObject("image").isEmpty() ? "" : jsonObject.getJSONObject("image").getString("name"));

        JSONObject vmuse = iStackDeviceService.getVmuse(p, jsonObject.getString("id"));
        JSONArray diskList = iStackDeviceService.getVmDetail(p, jsonObject.getString("id"));
        calculateAndSetDiskSizes(hostInfoRespCreateReqDTO, vmuse, diskList);
        return hostInfoRespCreateReqDTO;
    }

    private List<HostNicCreateReqDto> extractHostNics(Object vm, PlatformconfigDTO p, JSONArray items) {
        List<HostNicCreateReqDto> hostNicCreateReqDtoList = new ArrayList<>();
        JSONObject jsonObject = (JSONObject) vm;
        for (Object item : items) {
            HostNicCreateReqDto hostNicCreateReqDto = new HostNicCreateReqDto();
            JSONObject jsonItem = (JSONObject) item;
            hostNicCreateReqDto.setHostUuid(jsonItem.getString("instance_uuid"));
            hostNicCreateReqDto.setUuid(jsonItem.getString("uuid"));
            hostNicCreateReqDto.setIp("");
            hostNicCreateReqDto.setIp6("");
            hostNicCreateReqDto.setPlatformId(p.getId());
            hostNicCreateReqDto.setPlatformName(p.getName());
            hostNicCreateReqDto.setMac(jsonItem.getString("mac_addr"));
            hostNicCreateReqDto.setDriver("virtio");
            hostNicCreateReqDto.setInClassicNetwork((byte) 0);
            hostNicCreateReqDto.setNetworkUuid(jsonItem.getString("sub_id"));
            hostNicCreateReqDtoList.add(hostNicCreateReqDto);
        }
        return hostNicCreateReqDtoList;
    }

    private List<EipCreateReqDto> extractEips(Object vm, PlatformconfigDTO p, JSONArray items) {
        List<EipCreateReqDto> eipCreateReqDtoList = new ArrayList<>();
        for (Object item : items) {
            EipCreateReqDto eipCreateReqDto = new EipCreateReqDto();
            JSONObject eipObj = (JSONObject) item;
            eipCreateReqDto.setIpAddr(eipObj.getString("floating_ip_address"));
            eipCreateReqDto.setAssociateId(eipObj.getString("instance_uuid"));
            eipCreateReqDto.setAssociateType("server");
            eipCreateReqDto.setName(eipObj.getString("name"));
            eipCreateReqDto.setUuid(eipObj.getString("id"));
            eipCreateReqDto.setStatus("ready");
            eipCreateReqDto.setMode("elastic_ip");
            eipCreateReqDto.setBandwidth(eipObj.getInteger("bandwidth"));
            eipCreateReqDto.setChargeType("traffic");
            eipCreateReqDto.setCloudregionId("");
            eipCreateReqDto.setNetworkId(eipObj.getString("nic"));
            eipCreateReqDto.setPlatformId(p.getId());
            eipCreateReqDto.setPlatformName(p.getName());
            eipCreateReqDtoList.add(eipCreateReqDto);
        }
        return eipCreateReqDtoList;
    }

    public void processMetrics(JSONArray items, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO) {
        if (items == null) {
            return;
        }

        items.forEach(item -> {
            try {
                JSONObject jsonItem = (JSONObject) item;
                JSONObject metric = jsonItem.getJSONObject("metric");
                if (metric == null) {
                    return;
                }

                String itemName = metric.getString("item_name");
                if (StrUtil.isEmpty(itemName)) {
                    return;
                }

                BigDecimal samplingValue = getSamplingValue(jsonItem);
                switch (itemName) {
                    case "cpu_util":
                        if (samplingValue.scale() >= 3) {
                            samplingValue = BigDecimal.valueOf(0.01);
                        }
                        hostInfoRespCreateReqDTO.setCpuUsed(samplingValue);
                        break;
                    case "mem_util":
                        hostInfoRespCreateReqDTO.setMemoryUsed(samplingValue);
                        break;
                    case "disk_util":
                        hostInfoRespCreateReqDTO.setDiskUsed(samplingValue);
                        updateDiskUsage(hostInfoRespCreateReqDTO, samplingValue);
                        break;
                    case "net_in_bytes_rate":
                        hostInfoRespCreateReqDTO.setNetworkInPackets(BigDecimal.ZERO);
                        BigDecimal multiply = samplingValue.multiply(new BigDecimal(1024 * 8));
                        hostInfoRespCreateReqDTO.setNetworkInBytes(multiply);
                        break;
                    case "net_out_bytes_rate":
                        hostInfoRespCreateReqDTO.setNetworkOutPackets(BigDecimal.ZERO);
                        BigDecimal outMultiply = samplingValue.multiply(new BigDecimal(1024 * 8));
                        hostInfoRespCreateReqDTO.setNetworkOutBytes(outMultiply);
                        break;
                    default:
                        break; // 其他项不处理
                }
            } catch (Exception e) {
                log.error("Error processing metric item: {}", e.getMessage());
            }
        });
    }

    private BigDecimal getSamplingValue(JSONObject jsonItem) {
        try {
            JSONArray values = jsonItem.getJSONArray("values");
            if (values == null || values.isEmpty()) {
                return BigDecimal.ZERO;
            }
            Object firstValue = values.get(0);
            if (firstValue == null) {
                return BigDecimal.ZERO;
            }
            JSONObject valueObj = JSONObject.parseObject(firstValue.toString());
            if (valueObj == null || !valueObj.containsKey("sampling_value")) {
                return BigDecimal.ZERO;
            }
            String samplingValue = valueObj.getString("sampling_value");
            return StrUtil.isEmpty(samplingValue) ? BigDecimal.ZERO : new BigDecimal(samplingValue);
        } catch (Exception e) {
            log.warn("Error getting sampling value: {}", e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    private void updateDiskUsage(HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BigDecimal usedPercentage) {
        BigDecimal totalDiskCapacity = hostInfoRespCreateReqDTO.getTotalDiskCapacity();
        BigDecimal usedBytes = totalDiskCapacity.multiply(usedPercentage.divide(new BigDecimal(100)));
        hostInfoRespCreateReqDTO.setDiskUsedBytes(usedBytes);
        hostInfoRespCreateReqDTO.setDiskFreeBytes(totalDiskCapacity.subtract(usedBytes));
    }

    @XxlJob("iStackStorToInflux")
    public void collectToInflux() {

        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                .filter(dto -> "istack".equals(dto.getTypeCode()))
                .collect(Collectors.toList());
        if (filteredList.isEmpty()) return;
        for (PlatformconfigDTO platform : filteredList) {
            try {
                processPlatform(platform);
            } catch (Exception e) {
                log.error("Error processing platform: " + platform.getName(), e);
            }
        }
    }

    private void processPlatform(PlatformconfigDTO platform) {
        ExecutorService executorService = Executors.newFixedThreadPool(9);
        try {
            List<Future<BatchPoints>> futures = new ArrayList<>();
            JSONArray clouds = iStackDeviceService.getClouds(platform);
            // 创建批量写入点的容器
            List<BatchPoints> batchPointsList = Collections.synchronizedList(new ArrayList<>());

            for (int j = 0; j < clouds.size(); j++) {
                String vmid = clouds.getJSONObject(j).getString("id");
                JSONObject jsonObject = clouds.getJSONObject(j);
                JSONArray items = iStackDeviceService.getRelaTimeData(platform, vmid, true);
                if ("ACTIVE".equals(jsonObject.getString("status"))) {
                    Future<BatchPoints> future = executorService.submit(new Callable<BatchPoints>() {
                        @Override
                        public BatchPoints call() throws Exception {
                            try {
                                // 处理虚拟机并返回结果
                                return processVM(platform, items, jsonObject);
                            } catch (Exception e) {
                                log.error("Error processing VM: " + jsonObject.getString("name"), e);
                                return null;  // 出现异常时返回 null
                            }
                        }
                    });
                    futures.add(future);
                }
            }
            for (Future<BatchPoints> future : futures) {
                try {
                    BatchPoints result = future.get();  // 阻塞等待任务完成
                    if (result != null && !result.getPoints().isEmpty()) {
                        batchPointsList.add(result);
                    }
                } catch (InterruptedException | ExecutionException e) {
                    log.error("Error retrieving result from future", e);
                }
            }
            // 批量写入InfluxDB
            for (BatchPoints batchPoints : batchPointsList) {
                try {
                    influxDBTemplate.writeBatch(batchPoints);
                } catch (Exception e) {
                    log.error("Error writing to InfluxDB", e);
                }
            }
        } finally {
            // 关闭线程池`
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
            }
        }
    }

    private BatchPoints processVM(PlatformconfigDTO platform, JSONArray usageInfo, JSONObject vmInfo) {
        String uuid = vmInfo.getString("id");
        String name = vmInfo.getString("name");
        BigDecimal memoryMultiplier = (vmInfo.getJSONObject("flavor").getBigDecimal("ram")
                .multiply(new BigDecimal(1024 * 1024 * 1024))).setScale(5, RoundingMode.HALF_UP);
        BatchPoints.Builder batchPointsBuilder = BatchPoints.builder();

        if (usageInfo != null) {
            usageInfo.forEach(item -> {
                JSONObject jsonItem = (JSONObject) item;
                String itemName = jsonItem.getJSONObject("metric").getString("item_name");
                JSONArray values = jsonItem.getJSONArray("values");

                if (StrUtil.isNotEmpty(itemName)) {
                    processMetricByType(itemName, values, uuid, name, platform, batchPointsBuilder, memoryMultiplier);
                }
            });
        }

        return batchPointsBuilder.build();
    }

    private void processMetricByType(String itemName, JSONArray values, String uuid, String name,
                                     PlatformconfigDTO platform, BatchPoints.Builder batchPointsBuilder, BigDecimal memoryMultiplier) {
        switch (itemName) {
            case "cpu_util":
                addMetricPoint(batchPointsBuilder, values, uuid, name, platform, "cpu",
                        "CPUAverageUsedUtilization", false);
                break;
            case "mem_util":
                // 内存使用
                addMemMetricPoint(batchPointsBuilder, values, uuid, name, platform, "mem",
                        "MemoryUsedBytes", memoryMultiplier);
                // 内存使用率
                addMemoryMetrics(batchPointsBuilder, values, uuid, name, platform);
                break;
            case "disk_read_bytes_rate":
                addMetricPoint(batchPointsBuilder, values, uuid, name, platform, "disk",
                        "DiskReadBytes", true);
                break;
            case "disk_read_requests_rate":
                addMetricPoint(batchPointsBuilder, values, uuid, name, platform, "disk",
                        "DiskReadOps", false);
                break;
            case "disk_write_bytes_rate":
                addMetricPoint(batchPointsBuilder, values, uuid, name, platform, "disk",
                        "DiskWriteBytes", true);
                break;
            case "disk_write_requests_rate":
                addMetricPoint(batchPointsBuilder, values, uuid, name, platform, "disk",
                        "DiskWriteOps", false);
                break;
            case "net_in_bytes_rate":
                addMetricPoint(batchPointsBuilder, values, uuid, name, platform, "net",
                        "NetworkInBytes", true);
                processNetworkErrorMetrics(batchPointsBuilder, values, uuid, name, platform);
                break;
            case "net_out_bytes_rate":
                addMetricPoint(batchPointsBuilder, values, uuid, name, platform, "net",
                        "NetworkOutBytes", true);
                break;
        }
    }

    private void addMetricPoint(BatchPoints.Builder builder, JSONArray metrics, String uuid,
                                String name, PlatformconfigDTO platform, String label, String metricName,
                                boolean needConvertKbToBytes) {
        if (metrics == null) return;

        String vmType = "CPUAverageUsedUtilization".equals(metricName) ? metricName : "all";

        for (Object item : metrics) {
            JSONObject metricItem = JSONObject.parseObject(JSONUtil.toJsonStr(item));
            BigDecimal value;
            if (needConvertKbToBytes) {
                value = BigDecimal.valueOf(convertKbToBytes(metricItem.getString("sampling_value")))
                        .setScale(5, RoundingMode.HALF_UP);
            } else {
                value = new BigDecimal(metricItem.getString("sampling_value"))
                        .setScale(5, RoundingMode.HALF_UP);
            }
            if (value.compareTo(new BigDecimal("0.005")) < 0) {
                value = BigDecimal.ZERO;
            } else if (value.compareTo(new BigDecimal("0.01")) < 0) {
                value = new BigDecimal("0.01");
            }

            Point point = Point.measurement("zj_cloud_host")
                    .tag("uuid", uuid)
                    .tag("label", label)
                    .tag("metricName", metricName)
                    .addField("productsName", name)
                    .tag("regionId", StringUtil.toString(platform.getRegionId()))
                    .addField("regionName", StringUtil.toString(platform.getRegionName()))
                    .tag("vm_type", vmType)
                    .tag("platformId", StringUtil.toString(platform.getId()))
                    .addField("platformName", StringUtil.toString(platform.getName()))
                    .addField("vm_metricName", metricName)
                    .addField("type", vmType)
                    .addField("value", formatScienceValue(value.toPlainString()))
                    .time(convertTimestamp(StringUtil.toLong(metricItem.get("sampling_time"))), TimeUnit.SECONDS)
                    .build();

            builder.point(point);
        }
    }

    private void addMemMetricPoint(BatchPoints.Builder builder, JSONArray metrics, String uuid,
                                   String name, PlatformconfigDTO platform, String label, String metricName,
                                   BigDecimal multiply) {
        if (metrics == null) return;

        String vmType = "all";

        for (Object item : metrics) {
            JSONObject metricItem = JSONObject.parseObject(JSONUtil.toJsonStr(item));
            BigDecimal value = new BigDecimal(metricItem.getString("sampling_value"))
                    .setScale(5, RoundingMode.HALF_UP)
                    .divide(new BigDecimal(100), 4, RoundingMode.HALF_UP)
                    .multiply(multiply)
                    .setScale(5, RoundingMode.HALF_UP);

            Point point = Point.measurement("zj_cloud_host")
                    .tag("uuid", uuid)
                    .tag("label", label)
                    .tag("metricName", metricName)
                    .addField("productsName", name)
                    .tag("regionId", StringUtil.toString(platform.getRegionId()))
                    .addField("regionName", StringUtil.toString(platform.getRegionName()))
                    .tag("vm_type", vmType)
                    .tag("platformId", StringUtil.toString(platform.getId()))
                    .addField("platformName", StringUtil.toString(platform.getName()))
                    .addField("vm_metricName", metricName)
                    .addField("type", vmType)
                    .addField("value", formatSamplingValue(value.toPlainString()))
                    .time(convertTimestamp(StringUtil.toLong(metricItem.get("sampling_time"))), TimeUnit.SECONDS)
                    .build();

            builder.point(point);
        }
    }

    private void addMemoryMetrics(BatchPoints.Builder batchPointsBuilder, JSONArray values,
                                  String uuid, String name, PlatformconfigDTO platform) {
        if (values == null) return;

        values.forEach(item -> {
            JSONObject metricItem = JSONObject.parseObject(JSONUtil.toJsonStr(item));
            BigDecimal sampling_value = Convert.toBigDecimal(metricItem.get("sampling_value"))
                    .setScale(5, RoundingMode.HALF_UP);
            ;
            Point point = Point.measurement("zj_cloud_host")
                    .tag("uuid", uuid)
                    .tag("label", "mem")
                    .tag("metricName", "MemoryUsedInPercent")
                    .addField("productsName", name)
                    .tag("regionId", StringUtil.toString(platform.getRegionId()))
                    .addField("regionName", StringUtil.toString(platform.getRegionName()))
                    .tag("vm_type", "all")
                    .tag("platformId", StringUtil.toString(platform.getId()))
                    .addField("platformName", StringUtil.toString(platform.getName()))
                    .addField("vm_metricName", "MemoryUsedInPercent")
                    .addField("type", "all")
                    .addField("value", formatScienceValue(sampling_value.toPlainString()))
                    .time(convertTimestamp(StringUtil.toLong(metricItem.get("sampling_time"))), TimeUnit.SECONDS)
                    .build();

            batchPointsBuilder.point(point);
        });
    }

    private void processNetworkErrorMetrics(BatchPoints.Builder batchPointsBuilder, JSONArray usageInfo,
                                            String uuid, String name, PlatformconfigDTO platform) {
        if (usageInfo == null) return;
        usageInfo.forEach(item -> {
            JSONObject metricItem = JSONObject.parseObject(JSONUtil.toJsonStr(item));
            Point point = Point.measurement("zj_cloud_host")
                    .tag("uuid", uuid)
                    .tag("label", "net")
                    .tag("metricName", "NetworkInErrors")
                    .addField("productsName", name)
                    .tag("regionId", StringUtil.toString(platform.getRegionId()))
                    .addField("regionName", StringUtil.toString(platform.getRegionName()))
                    .tag("vm_type", "all")
                    .tag("platformId", StringUtil.toString(platform.getId()))
                    .addField("platformName", StringUtil.toString(platform.getName()))
                    .addField("vm_metricName", "NetworkInErrors")
                    .addField("type", "all")
                    .addField("value", Convert.toBigDecimal(0))
                    .time(convertTimestamp(StringUtil.toLong(metricItem.get("sampling_time"))), TimeUnit.SECONDS)
                    .build();

            Point point1 = Point.measurement("zj_cloud_host")
                    .tag("uuid", uuid)
                    .tag("label", "net")
                    .tag("metricName", "NetworkOutErrors")
                    .addField("productsName", name)
                    .tag("regionId", StringUtil.toString(platform.getRegionId()))
                    .addField("regionName", StringUtil.toString(platform.getRegionName()))
                    .tag("vm_type", "all")
                    .tag("platformId", StringUtil.toString(platform.getId()))
                    .addField("platformName", StringUtil.toString(platform.getName()))
                    .addField("vm_metricName", "NetworkOutErrors")
                    .addField("type", "all")
                    .addField("value", Convert.toBigDecimal(0))
                    .time(convertTimestamp(StringUtil.toLong(metricItem.get("sampling_time"))), TimeUnit.SECONDS)
                    .build();

            batchPointsBuilder.point(point);
            batchPointsBuilder.point(point1);
        });
    }

    private String stateConvert(String state) {
        String target = "";
        switch (state) {
            case "ACTIVE":
                target = "Running";
                break;
            case "Starting":
                target = "Starting";
                break;
            case "Stopping":
                target = "Stopping";
                break;
            case "SHUTOFF":
                target = "Stopped";
                break;
            case "resetting":
                target = "Rebooting";
                break;
            case "deleting":
                target = "Destroying";
                break;
            case "suspend":
                target = "Stopped";
                break;
            case "suspending":
                target = "Stopping";
                break;
            default:
                target = "Unknown";
        }
        return target;
    }

    private BigDecimal formatSamplingValue(Object samplingValue) {
        if (samplingValue == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal value = Convert.toBigDecimal(samplingValue);
        if (value.scale() <= 0) {
            return new BigDecimal(value.toPlainString()).setScale(0, RoundingMode.UNNECESSARY);
        }
        return new BigDecimal(value.setScale(2, RoundingMode.HALF_UP).toPlainString());
    }

    private BigDecimal formatScienceValue(Object samplingValue) {
        if (samplingValue == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal value = Convert.toBigDecimal(samplingValue);
        // 如果值大于100，强制设为100
        if (value.compareTo(new BigDecimal("100")) > 0) {
            return new BigDecimal("100");
        }
        if (value.scale() <= 0) {
            return new BigDecimal(value.toPlainString()).setScale(0, RoundingMode.UNNECESSARY);
        }
        return new BigDecimal(value.setScale(2, RoundingMode.HALF_UP).toPlainString());
    }


    public static Long convertTimestamp(long timestamp) {
        Date date = new Date(timestamp * 1000);
        long time = date.getTime() / 1000;
        return time;
    }

    public static double convertKbToBytes(String kbPerSecStr) {
        double kbPerSec = Double.parseDouble(kbPerSecStr);
        return kbPerSec * 1024;
    }

    public static Date convertStringToDate(String dateString) {
        try {
            OffsetDateTime offsetDateTime = OffsetDateTime.parse(dateString);
            return Date.from(offsetDateTime.toInstant());
        } catch (DateTimeParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    private BigDecimal calculateTotalDataDiskSize(JSONArray diskList) {
        try {
            return diskList.stream()
                    .map(disk -> (JSONObject) disk)
                    .filter(disk -> DATA_DISK_TYPE.equals(disk.getString("type")))
                    .map(disk -> {
                        BigDecimal size = disk.getBigDecimal("size");
                        return size != null ? size : BigDecimal.ZERO;
                    })
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        } catch (Exception e) {
            log.warn("Error calculating total data disk size", e);
            return BigDecimal.ZERO;
        }
    }

    private String getVmDiskUtil(JSONObject vmuse) {
        try {
            return Optional.ofNullable(vmuse)
                    .map(vm -> vm.getJSONObject("pageResult"))
                    .map(page -> page.getJSONArray("rows"))
                    .filter(rows -> !rows.isEmpty())
                    .map(rows -> ((JSONObject) rows.get(0)).getString("vm_volume_util"))
                    .orElse("0");
        } catch (Exception e) {
            log.warn("Failed to get vm_volume_util from vmuse", e);
            return "0";
        }
    }

    private void setDiskSizes(HostInfoRespCreateReqDTO hostInfo, BigDecimal cloudSize, String vmDiskUtil) {
        BigDecimal cloudSizeBytes = cloudSize.multiply(BYTES_TO_GB);

        if (StrUtil.isNotEmpty(vmDiskUtil)) {
            try {
                BigDecimal utilization = new BigDecimal(vmDiskUtil);
                BigDecimal actualSize = cloudSize
                        .multiply(utilization)
                        .divide(HUNDRED, 2, RoundingMode.HALF_UP)
                        .multiply(BYTES_TO_GB);
                hostInfo.setActualSize(actualSize);
            } catch (NumberFormatException e) {
                log.warn("Invalid vmDiskUtil value: {}, using cloudSizeBytes", vmDiskUtil);
                hostInfo.setActualSize(cloudSizeBytes);
            }
        } else {
            hostInfo.setActualSize(cloudSizeBytes);
        }

        hostInfo.setCloudSize(cloudSizeBytes);
    }

    private void calculateAndSetDiskSizes(HostInfoRespCreateReqDTO hostInfo, JSONObject vmuse, JSONArray diskList) {
        // 初始化云盘总大小
        BigDecimal cloudSize = BigDecimal.ZERO;

        if (!diskList.isEmpty()) {
            // 计算所有数据盘大小总和
            cloudSize = calculateTotalDataDiskSize(diskList);

            // 获取磁盘使用率
            String vmDiskUtil = getVmDiskUtil(vmuse);

            // 设置实际使用大小和云盘总大小(转换为字节)
            setDiskSizes(hostInfo, cloudSize, vmDiskUtil);
        } else {
            // 如果没有磁盘，设置为0
            hostInfo.setActualSize(cloudSize);
            hostInfo.setCloudSize(cloudSize);
        }
    }
}
