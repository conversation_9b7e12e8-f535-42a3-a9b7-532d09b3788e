package cn.iocoder.zj.module.collection.job.hardware;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.winhong.WinHongAccessTokenRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.zstack.AlarmInfoRedisDAO;
import cn.iocoder.zj.module.collection.framework.influx.config.InfluxDBTemplate;
import cn.iocoder.zj.module.collection.service.cloud.ISangForDeviceService;
import cn.iocoder.zj.module.collection.service.cloud.IWinHongDeviceService;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import cn.iocoder.zj.module.monitor.api.hardware.HardWareInfoApi;
import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.hardwarenic.HardWareNicApi;
import cn.iocoder.zj.module.monitor.api.hardwarenic.HardWareNicRespDTO;
import cn.iocoder.zj.module.monitor.api.network.NetworkApi;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL2DTO;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL3DTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.util.JsonUtil;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class WinHongHardwareJob {
    @Resource
    InfluxDBTemplate influxDBTemplate;
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    HardWareInfoApi hardWareInfoApi;
    @Resource
    ISangForDeviceService iSangForDeviceService;
    @Resource
    PlatformRedisDAO platformRedisDAO;

    @Autowired
    private AlarmConfigApi alarmConfigApi;

    @Resource
    AlarmInfoRedisDAO alarmInfoRedisDAO;

    @Resource
    WinHongAccessTokenRedisDAO winHongAccessTokenRedisDAO;

    @Resource
    IWinHongDeviceService winHongDeviceService;

    @Resource
    HardWareNicApi hardWareNicApi;

    @Resource
    NetworkApi networkApi;

    private final ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());


    public void winHongHardWareJob() {
        getWinHongHardware();
        getWinHongHardwarePre();
    }


    @XxlJob("getWinHongHardware")
    public void getWinHongHardware() {
        try {
            List<PlatformconfigDTO> filteredList = platformRedisDAO.get("platform").stream()
                    .filter(dto -> "winhong".equals(dto.getTypeCode()))
                    .collect(Collectors.toList());
            if (filteredList.isEmpty()) return;
            CompletableFuture<?>[] futures = filteredList.stream()
                    .map(this::processPlatformConfigAsync)
                    .toArray(CompletableFuture[]::new);
            CompletableFuture.allOf(futures).join();
        } catch (Exception e) {
            log.error("Error in winhongInfo", e);
        }
    }

    private CompletableFuture<Void> processPlatformConfigAsync(PlatformconfigDTO platformconfigDTO) {
        return CompletableFuture.runAsync(() -> {
            try {
                processPlatformConfig(platformconfigDTO);
            } catch (Exception e) {
                log.error("Error processing platform config", e);
            }
        }, executor);
    }

    private void processPlatformConfig(PlatformconfigDTO platformconfigDTO) {
        String token = winHongDeviceService.getToken(platformconfigDTO);
                    if (token==null){
                        log.info("获取云宏token失败");
                        return;
                    }
        JSONArray hardWares = winHongDeviceService.getHardware(platformconfigDTO, token);
        if (ObjectUtil.isNull(hardWares)) return;
        List<Map> listObjectSec = JSONObject.parseArray(hardWares.toJSONString(), Map.class);

        processHardwareInfo(listObjectSec, platformconfigDTO, token);

    }

    private void processHardwareInfo(List<Map> listObjectSec, PlatformconfigDTO platformconfigDTO, String token) {
        List<HardWareRespCreateReqDTO> hardWareRespCreateReqDTOS = new ArrayList<>();
        List<HardWareNicRespDTO> nicList = new ArrayList<>();
        List<NetWorkL2DTO> netWorkL2DTOS = new ArrayList<>();
        List<NetWorkL3DTO> netWorkL3DTOS = new ArrayList<>();
        for (Map map : listObjectSec) {
            hardWareRespCreateReqDTOS.add(collectHostInfo(platformconfigDTO, map, token));
            nicList.addAll(collectNicInfo(platformconfigDTO, map, token,netWorkL2DTOS,netWorkL3DTOS));
        }

        if (!hardWareRespCreateReqDTOS.isEmpty()) {
            updateHardwareData(platformconfigDTO, hardWareRespCreateReqDTOS);
        }
        if (!nicList.isEmpty()) {
            updateNicData(nicList,platformconfigDTO);
        }

        if (netWorkL2DTOS.size() > 0) {
            // 获取当前节点的index 与 总节点数
            int shardIndex = XxlJobHelper.getShardIndex();
            int shardTotal = XxlJobHelper.getShardTotal();
            log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
            Long networkL2Count = networkApi.getNetWorkL2Count("winhong");
            List<NetWorkL2DTO> netWorks = networkApi.getNetWorkL2List("winhong").getData();
            List<NetWorkL2DTO> dtos = netWorks.stream().filter(item -> Long.compare(item.getPlatformId(), platformconfigDTO.getId()) == 0).collect(Collectors.toList());
            List<NetWorkL2DTO> shardingData = new ArrayList<>();

            if (shardIndex < 0) {
                shardingData = netWorkL2DTOS;
            } else {
                shardingData = StringUtil.getShardingData(netWorkL2DTOS, shardTotal, shardIndex);
            }
            // 对分片数据进行业务处理
            for (NetWorkL2DTO item : shardingData) {
                // 模拟业务逻辑处理
                log.info("Processing item: " + item.getName());
            }
            if (networkL2Count == 0) {
                networkApi.addNetWorkL2(shardingData);
            } else {
                List<NetWorkL2DTO> collect2 = dtos.stream()
                        .filter(item -> !netWorkL2DTOS.stream().map(NetWorkL2DTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid()))
                        .collect(Collectors.toList());
                if (collect2.size() > 0) {
                    networkApi.deleteNetWorkL2ByNameList(collect2);
                }

                networkApi.updateNetWorkL2(shardingData);
                List<NetWorkL2DTO> collect = shardingData.stream()
                        .filter(item -> !dtos.stream().map(NetWorkL2DTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid()))
                        .collect(Collectors.toList());
                if (collect.size() > 0) {
                    networkApi.addNetWorkL2(collect);
                }
            }
        }
        if (netWorkL3DTOS.size() > 0) {
            // 获取当前节点的index 与 总节点数
            int shardIndex = XxlJobHelper.getShardIndex();
            int shardTotal = XxlJobHelper.getShardTotal();
            log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
            Long networkL3Count = networkApi.getNetWorkL3Count("winHong");
            List<NetWorkL3DTO> netWorks = networkApi.getNetWorkL3List("winHong").getData();
            List<NetWorkL3DTO> dtos = netWorks.stream().filter(item -> Long.compare(item.getPlatformId(), platformconfigDTO.getId()) == 0).collect(Collectors.toList());
            List<NetWorkL3DTO> shardingData = new ArrayList<>();


            if (shardTotal < 0) {
                shardingData = netWorkL3DTOS;
            } else {
                shardingData = StringUtil.getShardingData(netWorkL3DTOS, shardTotal, shardIndex);
            }// 对分片数据进行业务处理
            for (NetWorkL3DTO item : shardingData) {
                // 模拟业务逻辑处理
                log.info("Processing item: " + item.getName());
            }
            if (networkL3Count == 0) {
                networkApi.addNetWorkL3(shardingData);
            } else {
                List<NetWorkL3DTO> collect3 = dtos.stream()
                        .filter(item -> !netWorkL3DTOS.stream().map(NetWorkL3DTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid()))
                        .collect(Collectors.toList());
                if (collect3.size() > 0) {
                    networkApi.deleteNetWorkL3ByNameList(collect3);
                }
                networkApi.updateNetWorkL3(shardingData);
                List<NetWorkL3DTO> collect = shardingData.stream()
                        .filter(item -> !dtos.stream().map(NetWorkL3DTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid()))
                        .collect(Collectors.toList());
                if (collect.size() > 0) {
                    networkApi.addNetWorkL3(collect);
                }
            }
        }
    }

    private void updateNicData(List<HardWareNicRespDTO> nicList, PlatformconfigDTO p) {
        List<HardWareNicRespDTO> oldNicList=hardWareNicApi.getHardwareNicByPlatformId(p.getId()).getCheckedData();
        if (oldNicList.isEmpty()) {
            hardWareNicApi.adds(nicList);
        } else {
            // 修改Map的key为uuid+hardwareUuid的组合
            Map<String, HardWareNicRespDTO> existingHardwareMap = oldNicList.stream()
                    .collect(Collectors.toMap(
                            hardwareNic -> hardwareNic.getUuid() + "_" + hardwareNic.getHardwareUuid(),
                            hardwareNic -> hardwareNic
                    ));

            List<HardWareNicRespDTO> newEntries = new ArrayList<>();
            List<HardWareNicRespDTO> updatedEntries = new ArrayList<>();
            // 修改删除条件，同时比对uuid和hardwareUuid
            List<HardWareNicRespDTO> deleteEntries = oldNicList.stream()
                    .filter(item -> !nicList.stream()
                            .anyMatch(newItem ->
                                    newItem.getUuid().equals(item.getUuid()) &&
                                            newItem.getHardwareUuid().equals(item.getHardwareUuid())
                            ))
                    .collect(Collectors.toList());

            for (HardWareNicRespDTO hardWareNicRespDTO : nicList) {
                // 使用组合key来查找
                String compositeKey = hardWareNicRespDTO.getUuid() + "_" + hardWareNicRespDTO.getHardwareUuid();
                HardWareNicRespDTO nicRespDTO = existingHardwareMap.get(compositeKey);
                if (nicRespDTO == null) {
                    newEntries.add(hardWareNicRespDTO);
                } else if (!nicRespDTO.equals(hardWareNicRespDTO)) {
                    updatedEntries.add(hardWareNicRespDTO);
                }
            }

            hardWareNicApi.updates(updatedEntries);
            hardWareNicApi.adds(newEntries);
            if (!deleteEntries.isEmpty()) {
                hardWareNicApi.deletes(deleteEntries);
            }
        }
    }

    private List<HardWareNicRespDTO> collectNicInfo(PlatformconfigDTO platformconfigDTO, Map map, String token,List<NetWorkL2DTO> netWorkL2DTOS,List<NetWorkL3DTO> netWorkL3DTOS) {
        List<HardWareNicRespDTO> list = new ArrayList<>();
        String uuid = Convert.toStr(map.get("id"));
        //网络
        JSONArray groups = winHongDeviceService.portGroupsVswitchs(platformconfigDTO, uuid, token);
        for (Object group : groups) {
            //二级
            JSONObject groupObj = (JSONObject) group;
            String portGroupsId = groupObj.getString("id");
            String ip = groupObj.getString("ip");
            //三级
            JSONArray portGroups = winHongDeviceService.portGroups(platformconfigDTO, portGroupsId, token);
            if(CollectionUtil.isNotEmpty(portGroups)){
                for (Object item : portGroups) {
                    JSONObject obj = (JSONObject) item;
                    NetWorkL2DTO netWorkL2 = new NetWorkL2DTO();
                    netWorkL2.setName(obj.getString("name"));
                    netWorkL2.setUuid(obj.getString("id"));
                    netWorkL2.setPlatformName(platformconfigDTO.getName());
                    netWorkL2.setPlatformId(platformconfigDTO.getId());
                    netWorkL2.setRegionId(platformconfigDTO.getRegionId());
                    netWorkL2.setRegionName(platformconfigDTO.getRegionName());
                    netWorkL2.setTenantId(platformconfigDTO.getTenantId());
                    netWorkL2.setTypeName("winHong");
                    if(StringUtil.isNotEmpty(obj.getString("vlanId"))){
                        netWorkL2.setVlan(obj.getString("vlanId"));
                        netWorkL2.setType("VLAN");
                    }else {
                        netWorkL2.setType("FLAT");
                    }
                    netWorkL2DTOS.add(netWorkL2);

                    HardWareNicRespDTO nicRespDTO = new HardWareNicRespDTO();
                    nicRespDTO.setUuid(obj.getString("id") + "_3");
                    nicRespDTO.setHardwareUuid(uuid);
                    nicRespDTO.setPlatformName(platformconfigDTO.getName());
                    nicRespDTO.setPlatformId(platformconfigDTO.getId());
                    nicRespDTO.setL2NetworkUuid(obj.getString("id"));
                    nicRespDTO.setL2NetworkName(obj.getString("name"));
                    nicRespDTO.setState(true);
                    list.add(nicRespDTO);
                }
            }

            if(StringUtil.isNotEmpty(ip)){
                NetWorkL2DTO netWorkL2 = new NetWorkL2DTO();
                netWorkL2.setName(groupObj.getString("name"));
                netWorkL2.setUuid(groupObj.getString("id"));
                netWorkL2.setPlatformName(platformconfigDTO.getName());
                netWorkL2.setPlatformId(platformconfigDTO.getId());
                netWorkL2.setRegionId(platformconfigDTO.getRegionId());
                netWorkL2.setRegionName(platformconfigDTO.getRegionName());
                netWorkL2.setTenantId(platformconfigDTO.getTenantId());
                netWorkL2.setTypeName("winHong");
                if(StringUtil.isNotEmpty(groupObj.getString("vlanId"))){
                    netWorkL2.setType("VLAN");
                    netWorkL2.setVlan(groupObj.getString("vlanId"));
                }else {
                    netWorkL2.setType("FLAT");
                }
                netWorkL2DTOS.add(netWorkL2);

                NetWorkL3DTO netWorkL3DTO = new NetWorkL3DTO();
                netWorkL3DTO.setUuid(groupObj.getString("id") + "_3");
                netWorkL3DTO.setType("L3BasicNetwork");
                netWorkL3DTO.setNetworkSegment(groupObj.getString("ip")+"-"+groupObj.getString("netmask"));
                netWorkL3DTO.setNetmask(groupObj.getString("netmask"));
                netWorkL3DTO.setGateway(groupObj.getString("gateWay"));
                netWorkL3DTO.setPlatformId(platformconfigDTO.getId());
                netWorkL3DTO.setPlatformName(platformconfigDTO.getName());
                netWorkL3DTO.setTenantId(platformconfigDTO.getTenantId());
                netWorkL3DTO.setL2NetworkUuid(groupObj.getString("id"));
                netWorkL3DTO.setL2NetworkName(groupObj.getString("name"));
                netWorkL3DTO.setTypeName("winHong");
                netWorkL3DTOS.add(netWorkL3DTO);

                HardWareNicRespDTO nicRespDTO = new HardWareNicRespDTO();
                nicRespDTO.setUuid(groupObj.getString("id") + "_3");
                nicRespDTO.setNetworkType("管理口");
                nicRespDTO.setHardwareUuid(uuid);
                nicRespDTO.setNetworkType("-");
                nicRespDTO.setIpAddresses(groupObj.getString("ip"));
                nicRespDTO.setIpSubnet(groupObj.getString("netmask"));
                nicRespDTO.setL2NetworkUuid(groupObj.getString("id"));
                nicRespDTO.setL2NetworkName(groupObj.getString("name"));
                if (groupObj.getInteger("status")==1){
                    nicRespDTO.setState(true);
                }else {
                    nicRespDTO.setState(false);
                }
                nicRespDTO.setPlatformId(platformconfigDTO.getId());
                nicRespDTO.setPlatformName(platformconfigDTO.getName());
                list.add(nicRespDTO);
            }
        }
        return list;
    }


    private void updateHardwareData(PlatformconfigDTO platformconfigDTO, List<HardWareRespCreateReqDTO> hardWareRespCreateReqDTOS) {
        List<HardWareRespCreateReqDTO> existingHardwareList = hardWareInfoApi.getHardwareByPlatformId(platformconfigDTO.getId()).getCheckedData();

        if (existingHardwareList.isEmpty()) {
            hardWareInfoApi.adds(hardWareRespCreateReqDTOS);
        } else {
            Map<String, HardWareRespCreateReqDTO> existingHardwareMap = existingHardwareList.stream()
                    .collect(Collectors.toMap(HardWareRespCreateReqDTO::getUuid, hardware -> hardware));

            List<HardWareRespCreateReqDTO> newEntries = new ArrayList<>();
            List<HardWareRespCreateReqDTO> updatedEntries = new ArrayList<>();
            List<HardWareRespCreateReqDTO> deleteEntries = existingHardwareList.stream()
                    .filter(item -> !hardWareRespCreateReqDTOS.stream()
                            .map(HardWareRespCreateReqDTO::getUuid)
                            .collect(Collectors.toList()).contains(item.getUuid())).collect(Collectors.toList());


            for (HardWareRespCreateReqDTO newHardware : hardWareRespCreateReqDTOS) {
                HardWareRespCreateReqDTO existingHardware = existingHardwareMap.get(newHardware.getUuid());
                if (existingHardware == null) {
                    newEntries.add(newHardware);
                } else if (!existingHardware.equals(newHardware)) {
                    updatedEntries.add(newHardware);
                }
            }
            hardWareInfoApi.updates(updatedEntries);
            hardWareInfoApi.adds(newEntries);
            if (!deleteEntries.isEmpty()) {
                deleteEntries.forEach(item -> item.setDeleted(1));
                hardWareInfoApi.deleteHardWare(deleteEntries);

                Long alertIds = alarmConfigApi.getMaxAlertId().getData();
                // 资源删除触发告警
                List<AlarmDorisReqDTO> toInsert = new ArrayList<>();
                int i = 1;
                for (HardWareRespCreateReqDTO hardWareRespCreateReqDTO : deleteEntries){
                    AlarmDorisReqDTO collectorAlert = new AlarmDorisReqDTO();
                    JSONObject tags = new JSONObject();
                    tags.put("app", "hardware");
                    tags.put("monitorId", hardWareRespCreateReqDTO.getUuid());
                    tags.put("monitorName", hardWareRespCreateReqDTO.getName());
                    collectorAlert.setPriority(0);
                    collectorAlert.setStatus(0);
                    collectorAlert.setIsSolved(0);
                    collectorAlert.setFirstAlarmTime(new Date().getTime());
                    collectorAlert.setGmtCreate(new Date());
                    collectorAlert.setGmtUpdate(DateUtil.date());
                    collectorAlert.setLastAlarmTime(new Date().getTime());
                    collectorAlert.setMonitorName(hardWareRespCreateReqDTO.getName());
                    collectorAlert.setMonitorId(Convert.toStr( hardWareRespCreateReqDTO.getUuid()));
                    collectorAlert.setPlatformName(hardWareRespCreateReqDTO.getPlatformName());
                    collectorAlert.setPlatformId(hardWareRespCreateReqDTO.getPlatformId());
                    collectorAlert.setContent(String.format("该宿主机资源(%s)已被删除！",hardWareRespCreateReqDTO.getName()));
                    collectorAlert.setAlarmName(hardWareRespCreateReqDTO.getName());
                    collectorAlert.setTimes(1);
                    collectorAlert.setResourceType(1);
                    collectorAlert.setTags(JsonUtil.toJson(tags));
                    collectorAlert.setTarget("delete");
                    collectorAlert.setApp("hardware");
                    collectorAlert.setApp("hardware");
                    collectorAlert.setAlarmRule("-");
                    collectorAlert.setAlarmId(0L);
                    collectorAlert.setId(alertIds + i);
                    toInsert.add(collectorAlert);
                    i++;
                }
                Map<String, List> alertMap = new HashMap<>();
                alertMap.put("insertList", toInsert);
                //创建方法中需要有updateList，防止空指针异常
                alertMap.put("updateList", new ArrayList<>());
                alarmConfigApi.createAlarmToDoris(alertMap);
            }
        }
    }

    private HardWareRespCreateReqDTO collectHostInfo(PlatformconfigDTO platformconfigDTO, Map map, String token) {
        // 查询cpu可用数， vmCpuTotal，
        String uuid = Convert.toStr(map.get("id"));
        String name = Convert.toStr(map.get("hostname"));
        String ip = Convert.toStr(map.get("ip"));
        String clusterId = Convert.toStr(map.get("clusterId"));
        String clusterName = Convert.toStr(map.get("clusterName"));
        String poolName = Convert.toStr(map.get("poolName"));
        String architecture = Convert.toStr(map.get("cpuArchitecture"));
        Integer cpuSockets = Convert.toInt(map.get("cpuSockets"));
        Integer cpuNum = Convert.toInt(map.get("cpuCores"));
        BigDecimal cpuRate = Convert.toBigDecimal(map.get("cpuRate"));
        BigDecimal availableCpuCapacity = Convert.toBigDecimal(map.get("cpuCores"));
        String ratio = winHongDeviceService.ratio(platformconfigDTO.getUrl(), uuid, token);
        //查询物理主机预留内存
        Long reserveMemorySize=winHongDeviceService.reserveMemory(platformconfigDTO, uuid, token);


        BigDecimal totalCpuCapacity = Convert.toBigDecimal(JSONObject.parseObject(ratio).getInteger("vmCpuTotal"));

        //当前cpu超分比率
        BigDecimal cpuCommitRate = totalCpuCapacity.subtract(availableCpuCapacity).divide(new BigDecimal(cpuNum), 3, BigDecimal.ROUND_HALF_UP);

        Long totalMemoryCapacity = Convert.toLong(map.get("memory"));
        Long memoryUsage = Convert.toLong(map.get("memoryUsage"));
        //内存可用容量
        BigDecimal availableMemoryCapacity = NumberUtil.sub(totalMemoryCapacity, memoryUsage);

        //当前内存超售比率
        BigDecimal memCommitRate = new BigDecimal(totalMemoryCapacity).subtract(availableMemoryCapacity).divide(new BigDecimal(totalMemoryCapacity), 3, BigDecimal.ROUND_HALF_UP);

        BigDecimal memoryUsed = Convert.toBigDecimal(map.get("memoryRate"));
        String state = "Disabled";
        if (!Convert.toBool("isConnected")) {
            state = "Enabled";
        } else {
            state = "Disabled";
        }
        if ((Boolean) map.get("isMaintain")){
            state = "Maintenance";
        }
        String status = "Connected";
        if (!Convert.toBool("isConnected")) {
            status = "Connected";
        } else {
            status = "Disconnected";
        }

        Integer isMaintain = 0;
        if (Convert.toBool("isMaintain")) {
            isMaintain = 1;
        }
        BigDecimal totalDiskCapacity = new BigDecimal(0);
        BigDecimal diskUsedBytes = new BigDecimal(0);
        BigDecimal diskFreeBytes = new BigDecimal(0);
        BigDecimal diskUsed = Convert.toBigDecimal(map.get("storageRate"));

        JSONArray diskInfo = winHongDeviceService.disksInfo(platformconfigDTO.getUrl(), uuid, token);
        List<Map> diskInfoList = JSONObject.parseArray(diskInfo.toJSONString(), Map.class);

        for (Map map1 : diskInfoList) {
            totalDiskCapacity = totalDiskCapacity.add(Convert.toBigDecimal(map1.get("total")));
            diskUsedBytes = diskUsedBytes.add(Convert.toBigDecimal(map1.get("used")));
        }
        diskFreeBytes = NumberUtil.sub(totalDiskCapacity.setScale(0, RoundingMode.DOWN), diskFreeBytes.setScale(0, RoundingMode.DOWN));
        totalDiskCapacity = NumberUtil.mul(totalDiskCapacity.setScale(0, RoundingMode.DOWN), 1024, 1024, 1024);
        diskUsedBytes = NumberUtil.mul(diskUsedBytes.setScale(0, RoundingMode.DOWN), 1024, 1024, 1024);
        diskFreeBytes = NumberUtil.mul(diskFreeBytes.setScale(0, RoundingMode.DOWN), 1024, 1024, 1024);

        BigDecimal bandwidthUpstream = new BigDecimal(0);
        BigDecimal bandwidthDownstream = new BigDecimal(0);
        List<Map> phynicList = winHongDeviceService.phynic(platformconfigDTO.getUrl(), uuid, token);
        for (Map map1 : phynicList) {
            String flowInfo = winHongDeviceService.flow(platformconfigDTO.getUrl(), uuid, token, Convert.toStr(map1.get("mac")));
            JSONArray outputJson = JSONObject.parseObject(flowInfo).getJSONObject("output").getJSONArray("data");
            JSONArray inputJson = JSONObject.parseObject(flowInfo).getJSONObject("input").getJSONArray("data");
            if (outputJson.size() > 0) {
                bandwidthUpstream = bandwidthUpstream.add(Convert.toBigDecimal(JSONObject.parseObject(flowInfo).getJSONObject("output").getJSONArray("data").getJSONObject(0).get("value")));
            }
            if (inputJson.size() > 0) {
                bandwidthDownstream = bandwidthDownstream.add(Convert.toBigDecimal(JSONObject.parseObject(flowInfo).getJSONObject("input").getJSONArray("data").getJSONObject(0).get("value")));
            }
        }



        HardWareRespCreateReqDTO hardWareRespCreateReqDTO = new HardWareRespCreateReqDTO();
        hardWareRespCreateReqDTO.setUuid(uuid);
        // =========================型号相关========================================
        JSONObject summaryHost=winHongDeviceService.getSummaryHost(platformconfigDTO, uuid, token);
        hardWareRespCreateReqDTO.setBrandName("WinHong");
        hardWareRespCreateReqDTO.setModel(summaryHost.getString("hostModel"));
        hardWareRespCreateReqDTO.setSerialNumber(map.get("serialNumber").toString());
        hardWareRespCreateReqDTO.setIpmi("-");
        hardWareRespCreateReqDTO.setManufacturer("-");

        //cpu类型（描述）
        hardWareRespCreateReqDTO.setCpuType(map.get("cpuModelName").toString());

        hardWareRespCreateReqDTO.setName(name);
        hardWareRespCreateReqDTO.setState(state);
        hardWareRespCreateReqDTO.setIp(ip);
        hardWareRespCreateReqDTO.setStatus(status);
        hardWareRespCreateReqDTO.setClusterUuid(clusterId);
        hardWareRespCreateReqDTO.setClusterName(clusterName);
        hardWareRespCreateReqDTO.setManager(platformconfigDTO.getName());
        hardWareRespCreateReqDTO.setAvailableManager(poolName);
        hardWareRespCreateReqDTO.setTotalCpuCapacity(Convert.toLong(totalCpuCapacity));
        hardWareRespCreateReqDTO.setAvailableCpuCapacity(Convert.toLong(availableCpuCapacity));
        hardWareRespCreateReqDTO.setCpuSockets(cpuSockets);
        hardWareRespCreateReqDTO.setArchitecture(architecture);
        hardWareRespCreateReqDTO.setCpuNum(cpuNum);
        hardWareRespCreateReqDTO.setTotalMemoryCapacity(totalMemoryCapacity);
        hardWareRespCreateReqDTO.setAvailableMemoryCapacity(Convert.toLong(availableMemoryCapacity));
        hardWareRespCreateReqDTO.setDeleted(0);
        hardWareRespCreateReqDTO.setBandwidthUpstream(bandwidthUpstream);
        hardWareRespCreateReqDTO.setBandwidthDownstream(bandwidthDownstream);
        hardWareRespCreateReqDTO.setMemoryUsed(memoryUsed);
        hardWareRespCreateReqDTO.setCpuUsed(cpuRate);
        // 云鸿平台没有该数据所以这里只给0
        hardWareRespCreateReqDTO.setPacketRate(BigDecimal.valueOf(0));
        hardWareRespCreateReqDTO.setDiskUsed(diskUsed);
        hardWareRespCreateReqDTO.setDiskUsedBytes(diskUsedBytes);
        hardWareRespCreateReqDTO.setDiskFreeBytes(diskFreeBytes);
        hardWareRespCreateReqDTO.setTotalDiskCapacity(totalDiskCapacity);
        hardWareRespCreateReqDTO.setPlatformId(platformconfigDTO.getId());
        hardWareRespCreateReqDTO.setPlatformName(platformconfigDTO.getName());
        hardWareRespCreateReqDTO.setVms("");
        hardWareRespCreateReqDTO.setTypeName("winhong");
        hardWareRespCreateReqDTO.setIsMaintain(isMaintain);
        hardWareRespCreateReqDTO.setRegionId(platformconfigDTO.getRegionId());
        hardWareRespCreateReqDTO.setCpuOverPercent(new BigDecimal(8));
        hardWareRespCreateReqDTO.setMemoryOverPercent(new BigDecimal(1));
        if (cpuCommitRate.compareTo(BigDecimal.ZERO) < 0){
            hardWareRespCreateReqDTO.setCpuCommitRate(new BigDecimal(1));
        }else {
            hardWareRespCreateReqDTO.setCpuCommitRate(cpuCommitRate);
        }
        hardWareRespCreateReqDTO.setMemoryCommitRate(memCommitRate);
        hardWareRespCreateReqDTO.setReservedMemory(new BigDecimal(reserveMemorySize));
        return hardWareRespCreateReqDTO;
    }


    @XxlJob("getWinHongHardwarePre")
    public void getWinHongHardwarePre() {
        try {
            // 获取redis中数据
            List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
            // 取出只有vmware的数据
            List<PlatformconfigDTO> filteredList = new ArrayList<>();
            for (PlatformconfigDTO dto : platformconfigDTOList) {
                if ("winhong".equals(dto.getTypeCode())) {
                    filteredList.add(dto);
                }
            }

            if (!filteredList.isEmpty()) {
                for (PlatformconfigDTO platformconfigDTO : filteredList) {
                    String token = winHongDeviceService.getToken(platformconfigDTO);
                    if (token==null){
                        log.info("获取云宏token失败");
                        return;
                    }
                    List<HardWareRespCreateReqDTO> list = hardWareInfoApi.getHardwareByPlatformId(platformconfigDTO.getId()).getCheckedData();

                    BatchPoints batchPoints = BatchPoints.builder().build();
                    ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
                    List<Future<Void>> futures = new ArrayList<>();

                    for (HardWareRespCreateReqDTO hardWareRespCreateReqDTO : list) {
                        futures.add(executor.submit(() -> {
                            try {
                                collectMetricsForHost(platformconfigDTO, hardWareRespCreateReqDTO, batchPoints, token);
                            } catch (Exception e) {
                                log.error("Error collecting metrics for VM", e);
                            }
                            return null;
                        }));
                    }

                    for (Future<Void> future : futures) {
                        future.get();
                    }
                    executor.shutdown();

                    influxDBTemplate.writeBatch(BatchPoints.builder().points(batchPoints.getPoints()).build());

                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        

    }

    private void collectMetricsForHost(PlatformconfigDTO platformconfigDTO, HardWareRespCreateReqDTO hardWareRespCreateReqDTO, BatchPoints batchPoints, String token) {
        String uuid = hardWareRespCreateReqDTO.getUuid();
        String cpuRate = winHongDeviceService.cpuRate(platformconfigDTO.getUrl(), hardWareRespCreateReqDTO.getUuid(), token);

        BigDecimal totalMemoryCapacity = Convert.toBigDecimal(hardWareRespCreateReqDTO.getTotalMemoryCapacity());
        BigDecimal freeMemoryCapacity = Convert.toBigDecimal(hardWareRespCreateReqDTO.getAvailableMemoryCapacity());
        BigDecimal usedMemoryCapacity = NumberUtil.sub(totalMemoryCapacity, freeMemoryCapacity);
        //内存使用率
        BigDecimal memoryPercent = usedMemoryCapacity.multiply(new BigDecimal(100)).divide(totalMemoryCapacity,2, BigDecimal.ROUND_HALF_UP);

        // 存储
        BigDecimal storage_usage = hardWareRespCreateReqDTO.getDiskUsedBytes();
        BigDecimal storage_rate = hardWareRespCreateReqDTO.getDiskUsed();


        List<Map> cpuRateList = JSONObject.parseArray(JSONObject.parseObject(cpuRate).getJSONArray("data").toJSONString(), Map.class);
        if (cpuRateList.size() > 0) {
            BigDecimal cpuall = new BigDecimal(0);
            for (Map map : cpuRateList) {
                cpuall= cpuall.add(Convert.toBigDecimal(map.get("value")));
                Point point = Point.measurement("zj_cloud_hardware")
                        .tag("label", "cpu")
                        .tag("uuid", hardWareRespCreateReqDTO.getUuid())
                        .tag("metricName", "CPUUsedUtilization")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("host_type", "0")
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hardWareRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("host_metricName", "CPUUsedUtilization")
                        .addField("type", "CPUUsedUtilization")
                        .addField("value", Convert.toBigDecimal(map.get("value")))
                        .time(DateUtil.parse(Convert.toStr(map.get("time"))).getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                Point used = Point.measurement("zj_cloud_hardware")
                        .tag("label", "mem")
                        .tag("uuid", hardWareRespCreateReqDTO.getUuid())
                        .tag("metricName", "MemoryUsedBytes")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("host_type", "MemoryUsedBytes")
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hardWareRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("host_metricName", "MemoryUsedBytes")
                        .addField("type", "MemoryUsedBytes")
                        .addField("value", usedMemoryCapacity)
                        .time(DateUtil.parse(Convert.toStr(map.get("time"))).getTime() / 1000, TimeUnit.SECONDS)
                        .build();

                Point free = Point.measurement("zj_cloud_hardware")
                        .tag("label", "mem")
                        .tag("uuid", hardWareRespCreateReqDTO.getUuid())
                            .tag("metricName", "MemoryFreeBytes")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("host_type", "MemoryFreeBytes")
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hardWareRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("host_metricName", "MemoryFreeBytes")
                        .addField("type", "MemoryFreeBytes")
                        .addField("value", freeMemoryCapacity)
                        .time(DateUtil.parse(Convert.toStr(map.get("time"))).getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                Point freePercent = Point.measurement("zj_cloud_hardware")
                        .tag("label", "mem")
                        .tag("uuid", hardWareRespCreateReqDTO.getUuid())
                        .tag("metricName", "MemoryUsedInPercent")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("host_type", "MemoryUsedInPercent")
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hardWareRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("host_metricName", "MemoryUsedInPercent")
                        .addField("type", "MemoryUsedInPercent")
                        .addField("value", memoryPercent)
                        .time(DateUtil.parse(Convert.toStr(map.get("time"))).getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(freePercent);
                batchPoints.point(point);
                batchPoints.point(used);
                batchPoints.point(free);
            }

            for (Map map : cpuRateList){
                Point point = Point.measurement("zj_cloud_hardware")
                        .tag("label", "cpu")
                        .tag("uuid", hardWareRespCreateReqDTO.getUuid())
                        .tag("metricName", "CPUAllUsedUtilization")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("host_type", "0")
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hardWareRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("host_metricName", "CPUAllUsedUtilization")
                        .addField("type", "CPUAllUsedUtilization")
                        .addField("value", cpuall)
                        .time(DateUtil.parse(Convert.toStr(map.get("time"))).getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);

                BigDecimal cpuallAverage=cpuall.divide(new BigDecimal(cpuRateList.size()), 2, BigDecimal.ROUND_HALF_UP);
                Point pointAverage = Point.measurement("zj_cloud_hardware")
                        .tag("label", "cpu")
                        .tag("uuid", hardWareRespCreateReqDTO.getUuid())
                        .tag("metricName", "CPUAverageUsedUtilization")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("host_type", "0")
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hardWareRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("host_metricName", "CPUAverageUsedUtilization")
                        .addField("type", "CPUAverageUsedUtilization")
                        .addField("value", cpuallAverage)
                        .time(DateUtil.parse(Convert.toStr(map.get("time"))).getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(pointAverage);

            }


            JSONArray diskInfo = winHongDeviceService.disksInfo(platformconfigDTO.getUrl(), hardWareRespCreateReqDTO.getUuid(), token);
            List<Map> diskInfoList = JSONObject.parseArray(diskInfo.toJSONString(), Map.class);
            for (Map map1 : diskInfoList) {
                String type = Convert.toStr(map1.get("devName"));
                Map<String, Object> param = new HashMap<>();
                param.put("hostId", hardWareRespCreateReqDTO.getUuid());
                param.put("devName", Convert.toStr(map1.get("devName")));
                String ioreq = winHongDeviceService.ioReq(platformconfigDTO.getUrl(), token, param);
                // iops 读写
                List<Map> ioreadList = JSONObject.parseArray(JSONObject.parseObject(ioreq).getJSONObject("read").getJSONArray("data").toJSONString(), Map.class);
                if (ioreadList.size() > 0) {
                    for (Map map : ioreadList) {
                        Point point = Point.measurement("zj_cloud_hardware")
                                .tag("label", "disk")
                                .tag("uuid", uuid)
                                .tag("metricName", "DiskReadOps")
                                .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                                .tag("host_type", type)
                                .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                                .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                                .addField("productsName", hardWareRespCreateReqDTO.getName())
                                .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                                .addField("host_metricName", "DiskReadOps")
                                .addField("type", type)
                                .addField("value", Convert.toBigDecimal(map.get("value")))
                                .time(DateUtil.parse(Convert.toStr(map.get("time"))).getTime() / 1000, TimeUnit.SECONDS)
                                .build();
                        batchPoints.point(point);

                        Point statDiskUsage = Point.measurement("zj_cloud_hardware")
                                .tag("label", "disk")
                                .tag("uuid", uuid)
                                .tag("metricName", "DiskAllUsedCapacityInBytes")
                                .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                                .tag("host_type", "DiskAllUsedCapacityInBytes")
                                .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                                .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                                .addField("productsName", hardWareRespCreateReqDTO.getName())
                                .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                                .addField("host_metricName", "DiskAllUsedCapacityInBytes")
                                .addField("type", "DiskAllUsedCapacityInBytes")
                                .addField("value", Convert.toBigDecimal(storage_usage))
                                .time(DateUtil.parse(Convert.toStr(map.get("time"))).getTime() / 1000, TimeUnit.SECONDS)
                                .build();
                        batchPoints.point(statDiskUsage);

                        Point inPercent = Point.measurement("zj_cloud_hardware")
                                .tag("label", "disk")
                                .tag("uuid", uuid)
                                .tag("metricName", "DiskAllUsedCapacityInPercent")
                                .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                                .tag("host_type", "DiskAllUsedCapacityInPercent")
                                .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                                .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                                .addField("productsName", hardWareRespCreateReqDTO.getName())
                                .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                                .addField("host_metricName", "DiskAllUsedCapacityInPercent")
                                .addField("type", "DiskAllUsedCapacityInPercent")
                                .addField("value", storage_rate)
                                .time(DateUtil.parse(Convert.toStr(map.get("time"))).getTime() / 1000, TimeUnit.SECONDS)
                                .build();
                        batchPoints.point(inPercent);

                    }

                }
                List<Map> iowriteList = JSONObject.parseArray(JSONObject.parseObject(ioreq).getJSONObject("write").getJSONArray("data").toJSONString(), Map.class);
                if (!iowriteList.isEmpty()) {


                    for (Map map : iowriteList) {
                        Point point = Point.measurement("zj_cloud_hardware")
                                .tag("label", "disk")
                                .tag("uuid", uuid)
                                .tag("metricName", "DiskWriteOps")
                                .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                                .tag("host_type", type)
                                .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                                .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                                .addField("productsName", hardWareRespCreateReqDTO.getName())
                                .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                                .addField("host_metricName", "DiskWriteOps")
                                .addField("type", type)
                                .addField("value", Convert.toBigDecimal(map.get("value")))
                                .time(DateUtil.parse(Convert.toStr(map.get("time"))).getTime() / 1000, TimeUnit.SECONDS)
                                .build();
                        batchPoints.point(point);
                    }
                }

                String iostat = winHongDeviceService.ioStat(platformconfigDTO.getUrl(), token, param);
                List<Map> iostatreadList = JSONObject.parseArray(JSONObject.parseObject(iostat).getJSONObject("read").getJSONArray("data").toJSONString(), Map.class);
                if (!iostatreadList.isEmpty()) {
                    BigDecimal disk_read_all = new BigDecimal(0);
                    for (Map map : iostatreadList) {
                        disk_read_all = disk_read_all.add(Convert.toBigDecimal(map.get("value")));
                        Point point = Point.measurement("zj_cloud_hardware")
                                .tag("label", "disk")
                                .tag("uuid", uuid)
                                .tag("metricName", "DiskReadBytes")
                                .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                                .tag("host_type", type)
                                .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                                .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                                .addField("productsName", hardWareRespCreateReqDTO.getName())
                                .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                                .addField("host_metricName", "DiskReadBytes")
                                .addField("type", type)
                                .addField("value", Convert.toBigDecimal(map.get("value")))
                                .time(DateUtil.parse(Convert.toStr(map.get("time"))).getTime() / 1000, TimeUnit.SECONDS)
                                .build();
                        batchPoints.point(point);
                    }


                    Point point = Point.measurement("zj_cloud_hardware")
                            .tag("label", "disk")
                            .tag("uuid", uuid)
                            .tag("metricName", "DiskAllReadBytes")
                            .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                            .tag("host_type", "DiskAllReadBytes")
                            .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                            .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                            .addField("productsName", hardWareRespCreateReqDTO.getName())
                            .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                            .addField("host_metricName", "DiskAllReadBytes")
                            .addField("type", "DiskAllReadBytes")
                            .addField("value", disk_read_all)
                            .time(DateUtil.parse(Convert.toStr(iostatreadList.get(0).get("time"))).getTime() / 1000, TimeUnit.SECONDS)
                            .build();
                    batchPoints.point(point);
                }
                List<Map> iostatwriteList = JSONObject.parseArray(JSONObject.parseObject(iostat).getJSONObject("write").getJSONArray("data").toJSONString(), Map.class);
                if (!iostatwriteList.isEmpty()) {
                    BigDecimal disk_write_all = new BigDecimal(0);
                    for (Map map : iostatwriteList) {
                        disk_write_all = disk_write_all.add(Convert.toBigDecimal(map.get("value")));
                        Point point = Point.measurement("zj_cloud_hardware")
                                .tag("label", "disk")
                                .tag("uuid", uuid)
                                .tag("metricName", "DiskWriteBytes")
                                .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                                .tag("host_type", type)
                                .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                                .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                                .addField("productsName", hardWareRespCreateReqDTO.getName())
                                .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                                .addField("host_metricName", "DiskWriteBytes")
                                .addField("type", type)
                                .addField("value", Convert.toBigDecimal(map.get("value")))
                                .time(DateUtil.parse(Convert.toStr(map.get("time"))).getTime() / 1000, TimeUnit.SECONDS)
                                .build();
                        batchPoints.point(point);
                    }

                    Point point = Point.measurement("zj_cloud_hardware")
                            .tag("label", "disk")
                            .tag("uuid", uuid)
                            .tag("metricName", "DiskAllWriteBytes")
                            .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                            .tag("host_type", "DiskAllWriteBytes")
                            .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                            .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                            .addField("productsName", hardWareRespCreateReqDTO.getName())
                            .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                            .addField("host_metricName", "DiskAllWriteBytes")
                            .addField("type", "DiskAllWriteBytes")
                            .addField("value", disk_write_all)
                            .time(DateUtil.parse(Convert.toStr(iostatwriteList.get(0).get("time"))).getTime() / 1000, TimeUnit.SECONDS)
                            .build();
                    batchPoints.point(point);
                }


                BigDecimal bandwidthUpstream = new BigDecimal(0);
                BigDecimal bandwidthDownstream = new BigDecimal(0);
                List<Map> phynicList = winHongDeviceService.phynic(platformconfigDTO.getUrl(), uuid, token);
                if (!phynicList.isEmpty()) {
                    for (Map macs : phynicList) {
                        String flowInfo = winHongDeviceService.flow(platformconfigDTO.getUrl(), uuid, token, Convert.toStr(macs.get("mac")));
                        List<Map> outputJson = JSONObject.parseArray(JSONObject.parseObject(flowInfo).getJSONObject("output").getJSONArray("data").toJSONString(),Map.class);
                        List<Map> inputJson = JSONObject.parseArray(JSONObject.parseObject(flowInfo).getJSONObject("input").getJSONArray("data").toJSONString(),Map.class);

                        if (outputJson.size() > 0) {
                            String metricName = "NetworkOutBytes";
                            BigDecimal net_out_all = new BigDecimal(0);
                            for (Map map : outputJson){
                                net_out_all = net_out_all.add(Convert.toBigDecimal(map.get("value")));
                                Point point = Point.measurement("zj_cloud_hardware")
                                        .tag("label", "net")
                                        .tag("uuid", uuid)
                                        .tag("metricName", metricName)
                                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                                        .tag("host_type", macs.get("name")!=null?Convert.toStr(macs.get("name")):"")
                                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                                        .addField("productsName", hardWareRespCreateReqDTO.getName())
                                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                                        .addField("host_metricName", metricName)
                                        .addField("type",macs.get("name")!=null?Convert.toStr(macs.get("name")):"")
                                        .addField("value", Convert.toBigDecimal(map.get("value")))
                                        .time(DateUtil.parse(Convert.toStr(map.get("time"))).getTime() / 1000, TimeUnit.SECONDS)
                                        .build();
                                batchPoints.point(point);
                            }

                            Point point = Point.measurement("zj_cloud_hardware")
                                    .tag("label", "net")
                                    .tag("uuid", uuid)
                                    .tag("metricName", "NetworkAllOutBytes")
                                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                                    .tag("host_type", "NetworkAllOutBytes")
                                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                                    .addField("productsName", hardWareRespCreateReqDTO.getName())
                                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                                    .addField("host_metricName", "NetworkAllOutBytes")
                                    .addField("type", "NetworkAllOutBytes")
                                    .addField("value", net_out_all)
                                    .time(DateUtil.parse(Convert.toStr(outputJson.get(0).get("time"))).getTime() / 1000, TimeUnit.SECONDS)
                                    .build();
                            batchPoints.point(point);


                        }
                        if (inputJson.size() > 0) {
                            String metricName = "NetworkInBytes";
                            BigDecimal net_in_all = new BigDecimal(0);
                            for (Map map : inputJson){
                                net_in_all = net_in_all.add(Convert.toBigDecimal(map.get("value")));
                                Point point = Point.measurement("zj_cloud_hardware")
                                        .tag("label", "net")
                                        .tag("uuid", uuid)
                                        .tag("metricName", metricName)
                                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                                        .tag("host_type", Convert.toStr(macs.get("name")!=null?Convert.toStr(macs.get("name")):""))
                                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                                        .addField("productsName", hardWareRespCreateReqDTO.getName())
                                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                                        .addField("host_metricName", metricName)
                                        .addField("type",Convert.toStr(macs.get("name")!=null?Convert.toStr(macs.get("name")):""))
                                        .addField("value", Convert.toBigDecimal(map.get("value")))
                                        .time(DateUtil.parse(Convert.toStr(map.get("time"))).getTime() / 1000, TimeUnit.SECONDS)
                                        .build();
                                batchPoints.point(point);
                            }


                            Point point = Point.measurement("zj_cloud_hardware")
                                    .tag("label", "net")
                                    .tag("uuid", uuid)
                                    .tag("metricName", "NetworkAllInBytes")
                                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                                    .tag("host_type", "NetworkAllInBytes")
                                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                                    .addField("productsName", hardWareRespCreateReqDTO.getName())
                                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                                    .addField("host_metricName", "NetworkAllInBytes")
                                    .addField("type", "NetworkAllInBytes")
                                    .addField("value", NumberUtil.mul(NumberUtil.div(net_in_all, 8), 1024))
                                    .time(DateUtil.parse(Convert.toStr(inputJson.get(0).get("time"))).getTime() / 1000, TimeUnit.SECONDS)
                                    .build();
                            batchPoints.point(point);
                        }
                    }
                }

            }
        }

    }

}
