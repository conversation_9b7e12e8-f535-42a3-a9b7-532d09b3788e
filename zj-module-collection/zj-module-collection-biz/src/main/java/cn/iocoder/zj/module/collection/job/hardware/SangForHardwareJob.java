package cn.iocoder.zj.module.collection.job.hardware;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.zj.framework.common.util.collection.CollectionUtils;
import cn.iocoder.zj.module.collection.dal.dataobject.zstack.AlarmConfigInfo;
import cn.iocoder.zj.module.collection.dal.dataobject.zstack.AlarmHostRelationInfo;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.zstack.AlarmInfoRedisDAO;
import cn.iocoder.zj.module.collection.framework.influx.config.InfluxDBTemplate;
import cn.iocoder.zj.module.collection.service.cloud.ISangForDeviceService;
import cn.iocoder.zj.module.collection.util.SangForPwEncryptor;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import cn.iocoder.zj.module.monitor.api.hardware.HardWareInfoApi;
import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.hardwarenic.HardWareNicApi;
import cn.iocoder.zj.module.monitor.api.hardwarenic.HardWareNicRespDTO;
import cn.iocoder.zj.module.monitor.api.network.NetworkApi;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL2DTO;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL3DTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SangForHardwareJob {
    @Resource
    InfluxDBTemplate influxDBTemplate;
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    HardWareInfoApi hardWareInfoApi;
    @Resource
    ISangForDeviceService iSangForDeviceService;
    @Resource
    PlatformRedisDAO platformRedisDAO;
    @Resource
    HardWareNicApi hardWareNicApi;

    @Autowired
    private AlarmConfigApi alarmConfigApi;

    @Resource
    AlarmInfoRedisDAO alarmInfoRedisDAO;

    @Resource
    NetworkApi networkApi;

    public final Map<Long, List<HardWareRespCreateReqDTO>> hardwareData = new ConcurrentHashMap<>();

//    // 创建线程池
//    int processors = Runtime.getRuntime().availableProcessors();
//    ThreadPoolExecutor executor = new ThreadPoolExecutor(
//            processors * 2, // 核心线程数
//            processors * 4, // 最大线程数
//            60L, // 空闲线程存活时间
//            TimeUnit.SECONDS,
//            new LinkedBlockingQueue<>(1000), // 工作队列
//            new ThreadFactory() {
//                private final AtomicInteger counter = new AtomicInteger(1);
//
//                @Override
//                public Thread newThread(Runnable r) {
//                    Thread thread = new Thread(r);
//                    thread.setName("vm-metrics-collector-" + counter.getAndIncrement());
//                    return thread;
//                }
//            },
//            new ThreadPoolExecutor.CallerRunsPolicy() // 满载策略
//    );


    public void sangForHardwareJob() {
        getSangForHardware();
        collectToInflux();
    }

    @XxlJob("getSangForHardware")
    public void getSangForHardware() {
        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                .filter(dto -> "sangFor".equals(dto.getTypeCode()))
                .collect(Collectors.toList());
        if (filteredList.isEmpty()) return;


        List<AlarmHostRelationInfo> alarmHostRealtionList = JSON.parseArray(alarmInfoRedisDAO.getRelation("alarm_host_relation"), AlarmHostRelationInfo.class);
        List<AlarmConfigInfo> alarmConfigInfoList = JSON.parseArray(alarmInfoRedisDAO.getConfig("alarm_config"), AlarmConfigInfo.class);
        Map<Long, AlarmConfigInfo> alarmConfigMap = new HashMap<>();
        Map<String, AlarmHostRelationInfo> alarmConfigRelationMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(alarmConfigInfoList)) {
            alarmConfigInfoList.removeIf(item -> item.getDeleted() == 1);
            alarmConfigMap = CollectionUtils.convertMap(alarmConfigInfoList, AlarmConfigInfo::getId);
        }
        if (ObjectUtil.isNotEmpty(alarmHostRealtionList)) {
            alarmHostRealtionList.removeIf(item -> item.getStatus() == 1);
            alarmConfigRelationMap = CollectionUtils.convertMap(alarmHostRealtionList, AlarmHostRelationInfo::getHostUuid);
        }

        try {
            for (PlatformconfigDTO platformconfigDTO : filteredList) {
                handlePlatformConfig(platformconfigDTO, alarmConfigRelationMap, alarmConfigMap);
            }
        } catch (Exception e) {
            XxlJobHelper.handleFail("sanfor中getSangForHardware异常" + e.getMessage());
        }
    }

    private void handlePlatformConfig(PlatformconfigDTO p, Map<String, AlarmHostRelationInfo> alarmConfigRelationMap, Map<Long, AlarmConfigInfo> alarmConfigMap) {

        List<HardWareRespCreateReqDTO> hardWareRespCreateReqDTOS = new ArrayList<>();
        ArrayList<HardWareNicRespDTO> nicList = new ArrayList<>();
        List<NetWorkL2DTO> netWorkL2DTOS = new ArrayList<>();
        List<NetWorkL3DTO> netWorkL3DTOS = new ArrayList<>();
        if (p.getTypeCode().equals("sangFor")) {
            JSONObject tokenInfo = SangForPwEncryptor.sangForlogin(p);
            JSONArray hardwares = iSangForDeviceService.getHardware(p, tokenInfo);
            JSONArray cpuOveruseConfig = iSangForDeviceService.getCpuOveruseConfig(p, tokenInfo);
            JSONArray memoryOveruseConfig = iSangForDeviceService.getMemoryOveruseConfig(p, tokenInfo);
            Map<String,JSONArray> networkInfoArray = iSangForDeviceService.getNetworksInfo(p, tokenInfo);
            JSONArray nicInfoArray = iSangForDeviceService.getNicInfo(p, tokenInfo);

            //查询集群信息
            JSONObject clusterInfo =iSangForDeviceService.getClusterIp(p, tokenInfo);
            for (int i = 0; i < hardwares.size(); i++) {
                 HardWareRespCreateReqDTO hardWareRespCreateReqDTO = new HardWareRespCreateReqDTO();
                JSONObject jsonObject = hardwares.getJSONObject(i);
                JSONObject hostInfo = iSangForDeviceService.getHardwareDetail(p, tokenInfo, jsonObject.getString("id"));
                JSONObject cpuInfo = jsonObject.getJSONObject("conf_cpu");
                BigDecimal bigDecimal = jsonObject.getBigDecimal("mem_ratio");

                JSONObject cpuObject = new JSONObject();
                JSONObject memoryObject = new JSONObject();
                JSONArray nicArray = new JSONArray();
                BigDecimal cpuOverPercent = new BigDecimal(0);
                if (cpuOveruseConfig != null && !cpuOveruseConfig.isEmpty()) {
                    for (Object object : cpuOveruseConfig) {
                        JSONObject cpuOveruse = JSONObject.parseObject(object.toString());
                        if (cpuOveruse.getString("node_id").equals(jsonObject.getString("id"))) {
                            cpuObject = cpuOveruse;
                            break;
                        }
                    }
                    //cpu超售比例
                    cpuOverPercent = cpuObject.getBigDecimal("cpu_over_percent").divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                } else {
                    cpuOverPercent = new BigDecimal(1);
                }
                for (Object object : memoryOveruseConfig) {
                    JSONObject memoryOveruse = JSONObject.parseObject(object.toString());
                    if (memoryOveruse.getString("node_id").equals(jsonObject.getString("id"))){
                        memoryObject=memoryOveruse;
                        break;
                    }
                }
                BigDecimal memoryOverPercent = memoryObject.getBigDecimal("mem_over_percent") != null
                        ? memoryObject.getBigDecimal("mem_over_percent").divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)
                        : new BigDecimal(0);

                //物理网络
                for (Object object : nicInfoArray) {
                    JSONObject nicInfo = JSONObject.parseObject(object.toString());
                    if (nicInfo.getString("node_id").equals(jsonObject.getString("id"))){
                        nicArray = nicInfo.getJSONArray("data");
                        break;
                    }
                }

                for (Object object : nicArray) {
                    JSONObject nic = JSONObject.parseObject(object.toString());
                    if(StringUtil.isNotEmpty(nic.getString("ip"))){
                        NetWorkL2DTO netWorkL2 = new NetWorkL2DTO();
                        netWorkL2.setName(nic.getString("name"));
                        netWorkL2.setUuid(nic.getString("id"));
                        netWorkL2.setVlan(nic.getString("vlan_id"));
                        netWorkL2.setPlatformName(p.getName());
                        netWorkL2.setPlatformId(p.getId());
                        netWorkL2.setRegionId(p.getRegionId());
                        netWorkL2.setRegionName(p.getRegionName());
                        netWorkL2.setPhysicalInterface(nic.getString("type"));
                        if(StringUtil.isNotEmpty(nic.getString("vlan_id"))){
                            netWorkL2.setType("VLAN");
                        }else {
                            netWorkL2.setType("FLAT");
                        }
                        netWorkL2.setTenantId(p.getTenantId());
                        netWorkL2.setTypeName("sangFor");
                        netWorkL2DTOS.add(netWorkL2);

                        NetWorkL3DTO netWorkL3DTO = new NetWorkL3DTO();
                        netWorkL3DTO.setUuid(nic.getString("id")+nic.getString("ip")+nic.getString("mac"));
                        netWorkL3DTO.setType("L3BasicNetwork");
                        netWorkL3DTO.setNetworkSegment(nic.getString("ip")+"-"+nic.getString("netmask"));
                        netWorkL3DTO.setNetmask(nic.getString("netmask"));
                        netWorkL3DTO.setGateway(nic.getString("gateway_ip"));
                        netWorkL3DTO.setPlatformId(p.getId());
                        netWorkL3DTO.setPlatformName(p.getName());
                        netWorkL3DTO.setTenantId(p.getTenantId());
                        netWorkL3DTO.setL2NetworkUuid(nic.getString("id"));
                        netWorkL3DTO.setL2NetworkName(nic.getString("name"));
                        netWorkL3DTO.setTypeName("sangFor");
                        netWorkL3DTOS.add(netWorkL3DTO);


                        HardWareNicRespDTO nicRespDTO = new HardWareNicRespDTO();
                        nicRespDTO.setUuid(nic.getString("id")+nic.getString("ip")+nic.getString("mac"));
                        nicRespDTO.setHardwareUuid(jsonObject.getString("id"));
                        nicRespDTO.setMac(nic.getString("mac"));
                        //data中primary取出
                        nicRespDTO.setNetworkType("-");
                        nicRespDTO.setIpAddresses(nic.getString("ip"));
                        nicRespDTO.setIpSubnet(nic.getString("netMask"));
                        nicRespDTO.setL2NetworkUuid(nic.getString("id"));
                        nicRespDTO.setL2NetworkName(nic.getString("name"));
                        if (nic.getInteger("status")==1){
                            nicRespDTO.setState(true);
                        }else {
                            nicRespDTO.setState(false);
                        }
                        nicRespDTO.setPlatformId(p.getId());
                        nicRespDTO.setPlatformName(p.getName());
                        nicList.add(nicRespDTO);
                    }
                }

                //网络信息
                int inBps = 0;
                int outBps = 0;
                int inPps = 0;
                if (networkInfoArray != null && !networkInfoArray.isEmpty()) {
                    JSONArray array = networkInfoArray.get("bridgeList");
                    JSONArray vlanArray = networkInfoArray.get("vlanGroup");
                    if (array.size() > 0) {
                        for (int j = 0; j < array.size(); j++) {
                            JSONObject netInfo = JSONObject.parseObject(JSONUtil.toJsonStr(array.get(j)));
                            if (netInfo.getString("hostID").equals(jsonObject.getString("id"))) {
                                inBps += netInfo.getInteger("inBps");
                                outBps += netInfo.getInteger("outBps");
                                inPps += netInfo.getInteger("inPps");
                            }
                        }
                        hardWareRespCreateReqDTO.setBandwidthDownstream(new BigDecimal(inBps));
                        hardWareRespCreateReqDTO.setBandwidthUpstream(new BigDecimal(outBps));
                        hardWareRespCreateReqDTO.setPacketRate(new BigDecimal(inPps));
                    } else {
                        Map map = iSangForDeviceService.getNodeSheetByHardId(p, tokenInfo, jsonObject.getString("id"));
                        hardWareRespCreateReqDTO.setBandwidthDownstream(Convert.toBigDecimal(map.get("down")));
                        hardWareRespCreateReqDTO.setBandwidthUpstream(Convert.toBigDecimal(map.get("up")));
                        hardWareRespCreateReqDTO.setPacketRate(Convert.toBigDecimal(map.get("down")));
                    }

                    if(vlanArray.size() > 0){
                        for (int j = 0; j < vlanArray.size(); j++) {
                            JSONObject netInfo = JSONObject.parseObject(JSONUtil.toJsonStr(vlanArray.get(j)));
                            NetWorkL2DTO netWorkL2 = new NetWorkL2DTO();
                            netWorkL2.setName(netInfo.getString("name"));
                            netWorkL2.setUuid(netInfo.getString("id"));
                            netWorkL2.setPlatformName(p.getName());
                            netWorkL2.setPlatformId(p.getId());
                            netWorkL2.setRegionId(p.getRegionId());
                            netWorkL2.setRegionName(p.getRegionName());
                            netWorkL2.setPhysicalInterface(netInfo.getString("type"));
                            if(StringUtil.isNotEmpty(netInfo.getString("vlan_id"))){
                                netWorkL2.setType("VLAN");
                            }else {
                                netWorkL2.setType("FLAT");
                            }
                            netWorkL2.setTenantId(p.getTenantId());
                            netWorkL2.setTypeName("sangFor");
                            netWorkL2DTOS.add(netWorkL2);

                            HardWareNicRespDTO nicRespDTO = new HardWareNicRespDTO();
                            nicRespDTO.setHardwareUuid(jsonObject.getString("id"));
                            nicRespDTO.setUuid(netInfo.getString("id") + "_3");
                            //data中primary取出
                            nicRespDTO.setNetworkType("-");
                            nicRespDTO.setL2NetworkUuid(netInfo.getString("id"));
                            nicRespDTO.setL2NetworkName(netInfo.getString("id"));
                            nicRespDTO.setState(true);
                            nicRespDTO.setPlatformId(p.getId());
                            nicRespDTO.setPlatformName(p.getName());
                            nicList.add(nicRespDTO);
                        }
                    }
                }

                //基本信息
                hardWareRespCreateReqDTO.setName(jsonObject.getString("name"));
                hardWareRespCreateReqDTO.setUuid(jsonObject.getString("id"));
                hardWareRespCreateReqDTO.setIp(jsonObject.getString("ip"));

                hardWareRespCreateReqDTO.setStatus(jsonObject.getInteger("status") == 1 ? "Connected" : "Disconnected");
                hardWareRespCreateReqDTO.setState("Enabled");
                //无集群信息，设置默认集群信息
                hardWareRespCreateReqDTO.setClusterName("default cluster");
                hardWareRespCreateReqDTO.setClusterUuid("default_cluster");
                //cpu信息
                BigDecimal cpuCommitRate=new BigDecimal(0);
                if (cpuInfo != null) {
                    long confTotalVcore = cpuInfo.getLong("conf_total_vcore") != null ? cpuInfo.getLong("conf_total_vcore") : 0;
                    long cpuOverPercent1 = cpuObject.getLong("cpu_over_percent") != null ? cpuObject.getLong("cpu_over_percent") : 0;
                    long totalCpuCapacity = confTotalVcore * cpuOverPercent1 / 100;
                    hardWareRespCreateReqDTO.setTotalCpuCapacity(totalCpuCapacity);
                    int cpuNum = cpuInfo.getInteger("conf_total_vcore") != null ? cpuInfo.getInteger("conf_total_vcore") : 0;
                    hardWareRespCreateReqDTO.setCpuNum(cpuNum);
                    long confUsedVcore = cpuInfo.getLong("conf_used_vcore") != null ? cpuInfo.getLong("conf_used_vcore") : 0;
                    long availableCpuCapacity = confTotalVcore - confUsedVcore;
                    hardWareRespCreateReqDTO.setAvailableCpuCapacity(availableCpuCapacity);
                    //当前cpu超分比率
                    cpuCommitRate= new BigDecimal(totalCpuCapacity-availableCpuCapacity).divide(new BigDecimal(cpuNum), 3, BigDecimal.ROUND_HALF_UP);

                } else {
                    JSONObject hardwareCloudDetail = iSangForDeviceService.getHardwareCloudDetail(p, tokenInfo, jsonObject.getString("id"));
                    Long cores = hardwareCloudDetail.getJSONObject("data").getJSONObject("cpu_status").getLong("cores") != null
                            ? hardwareCloudDetail.getJSONObject("data").getJSONObject("cpu_status").getLong("cores") : 0;
                    Long cupThreads = hardwareCloudDetail.getJSONObject("data").getJSONObject("cpu_status").getLong("cpu_threads") != null
                            ? hardwareCloudDetail.getJSONObject("data").getJSONObject("cpu_status").getLong("cpu_threads") : 0;
                    Long sockets = hostInfo.getLong("cpu_sockets") != null ? hostInfo.getLong("cpu_sockets") : 0;
                    BigDecimal cpuRatio = jsonObject.getBigDecimal("cpu_ratio") != null ? jsonObject.getBigDecimal("cpu_ratio") : new BigDecimal(0);
                    Long totalCpuCapacity = Convert.toLong(NumberUtil.mul(cores, sockets));
                    Long useCpuCapacity = Convert.toLong(NumberUtil.mul(totalCpuCapacity, cpuRatio));
                    Long availableCpuCapacity = Convert.toLong(NumberUtil.sub(totalCpuCapacity, useCpuCapacity));
                    hardWareRespCreateReqDTO.setTotalCpuCapacity(totalCpuCapacity);
                    hardWareRespCreateReqDTO.setAvailableCpuCapacity(availableCpuCapacity);
//                    hardWareRespCreateReqDTO.setCpuNum(Convert.toInt(useCpuCapacity));
                    hardWareRespCreateReqDTO.setCpuNum(Convert.toInt(NumberUtil.mul(cupThreads, sockets)));
                    //当前cpu超分比率
                    cpuCommitRate= new BigDecimal(totalCpuCapacity-availableCpuCapacity).divide(new BigDecimal(useCpuCapacity), 3, BigDecimal.ROUND_HALF_UP);

                }

                hardWareRespCreateReqDTO.setCpuSockets(hostInfo.getInteger("cpu_sockets"));
                hardWareRespCreateReqDTO.setCpuUsed(jsonObject.getBigDecimal("cpu_ratio").multiply(new BigDecimal(100)));
                hardWareRespCreateReqDTO.setArchitecture(hostInfo.getString("architecture"));
                //磁盘信息
                hardWareRespCreateReqDTO.setDiskUsedBytes(hostInfo.getBigDecimal("disk_used_bytes"));
                hardWareRespCreateReqDTO.setDiskFreeBytes(hostInfo.getBigDecimal("disk_free_bytes"));
                hardWareRespCreateReqDTO.setDiskUsed(hostInfo.getBigDecimal("disk_used").multiply(new BigDecimal(100)));
                hardWareRespCreateReqDTO.setTotalDiskCapacity(hostInfo.getBigDecimal("total_disk_capacity"));
                //内存信息
                Long totalMemoryCapacity = hostInfo.getLong("total_memory_capacity") != null
                        ? hostInfo.getLong("total_memory_capacity") : 0;
                hardWareRespCreateReqDTO.setTotalMemoryCapacity(totalMemoryCapacity);
                Long availableMemoryCapacity = hostInfo.getLong("available_memory_capacity") != null
                        ? hostInfo.getLong("available_memory_capacity") : 0;
                hardWareRespCreateReqDTO.setAvailableMemoryCapacity(availableMemoryCapacity);
                BigDecimal memRatio = jsonObject.getBigDecimal("mem_ratio") != null ? jsonObject.getBigDecimal("mem_ratio") : new BigDecimal(0);
                hardWareRespCreateReqDTO.setMemoryUsed(memRatio.multiply(new BigDecimal(100)));
                //当前内存超售比率
                BigDecimal memCommitRate = new BigDecimal(totalMemoryCapacity-availableMemoryCapacity).divide(new BigDecimal(totalMemoryCapacity), 3, BigDecimal.ROUND_HALF_UP);
                // 如果是负数则转成去除负号
                if (bigDecimal.signum() < 0) {
                    hardWareRespCreateReqDTO.setMemoryUsed(bigDecimal.abs().multiply(new BigDecimal(100)));
                    hardWareRespCreateReqDTO.setStatus("Connected");
                }
                //平台信息
                hardWareRespCreateReqDTO.setPlatformId(p.getId());
                hardWareRespCreateReqDTO.setPlatformName(p.getName());
                hardWareRespCreateReqDTO.setTenantId(p.getTenantId());
                hardWareRespCreateReqDTO.setRegionId(p.getRegionId());
                hardWareRespCreateReqDTO.setTypeName(p.getTypeCode());
                hardWareRespCreateReqDTO.setDeleted(0);
                hardWareRespCreateReqDTO.setCpuOverPercent(cpuOverPercent);
                hardWareRespCreateReqDTO.setMemoryOverPercent(memoryOverPercent);
                hardWareRespCreateReqDTO.setCpuCommitRate(cpuCommitRate);
                hardWareRespCreateReqDTO.setMemoryCommitRate(memCommitRate);
                hardWareRespCreateReqDTOS.add(hardWareRespCreateReqDTO);
            }
        }
        syncHardwareData(p,hardWareRespCreateReqDTOS);
        syncNicData(p,nicList);
        syncNetworkL2Data(p,netWorkL2DTOS);
        syncNetworkL3Data(p,netWorkL3DTOS);
    }
    

    private void syncHardwareData(PlatformconfigDTO platform, List<HardWareRespCreateReqDTO> hardwareList) {
        if (CollectionUtil.isEmpty(hardwareList)) {
            return;
        }

        try {
            List<HardWareRespCreateReqDTO> existingList = hardWareInfoApi.getHardwareByPlatformId(platform.getId())
                    .getCheckedData();

            if (existingList.isEmpty()) {
                hardWareInfoApi.adds(hardwareList);
                hardwareData.put(platform.getId(), hardwareList);
                return;
            }

            // 分类处理
            Map<String, List<HardWareRespCreateReqDTO>> changes = classifyHardwareChanges(existingList, hardwareList);

            // 执行数据同步
            executeHardwareChanges(changes, platform);

            // 清理重复数据
            hardWareInfoApi.removeDuplicateData();
        } catch (Exception e) {
            log.error("[syncHardwareData][platformId({}) 同步硬件数据异常]", platform.getId(), e);
        }
    }

    private Map<String, List<HardWareRespCreateReqDTO>> classifyHardwareChanges(
            List<HardWareRespCreateReqDTO> existingList,
            List<HardWareRespCreateReqDTO> newList) {

        Map<String, HardWareRespCreateReqDTO> existingMap = existingList.stream()
                .collect(Collectors.toMap(
                        HardWareRespCreateReqDTO::getUuid,
                        Function.identity()
                ));

        List<HardWareRespCreateReqDTO> toAdd = new ArrayList<>();
        List<HardWareRespCreateReqDTO> toUpdate = new ArrayList<>();
        List<HardWareRespCreateReqDTO> toDelete = new ArrayList<>(existingList);

        for (HardWareRespCreateReqDTO newHardware : newList) {
            HardWareRespCreateReqDTO existingHardware = existingMap.get(newHardware.getUuid());
                    if (existingHardware == null) {
                toAdd.add(newHardware);
                    } else if (!existingHardware.equals(newHardware)) {
                toUpdate.add(newHardware);
            }
            toDelete.removeIf(item -> item.getUuid().equals(newHardware.getUuid()));
        }

        Map<String, List<HardWareRespCreateReqDTO>> changes = new HashMap<>();
        changes.put("add", toAdd);
        changes.put("update", toUpdate);
        changes.put("delete", toDelete);
        return changes;
    }

    private void executeHardwareChanges(Map<String, List<HardWareRespCreateReqDTO>> changes, PlatformconfigDTO platform) {
        List<HardWareRespCreateReqDTO> toDelete = changes.get("delete");
        List<HardWareRespCreateReqDTO> toUpdate = changes.get("update");
        List<HardWareRespCreateReqDTO> toAdd = changes.get("add");

        // 处理删除的硬件
        if (!CollectionUtil.isEmpty(toDelete)) {
            handleDeletedHardware(toDelete);
        }

        // 处理更新和新增
        if (!CollectionUtil.isEmpty(toUpdate)) {
            hardWareInfoApi.updates(toUpdate);
        }
        if (!CollectionUtil.isEmpty(toAdd)) {
            hardWareInfoApi.adds(toAdd);
        }
    }

    private void handleDeletedHardware(List<HardWareRespCreateReqDTO> deleteList) {
        try {
            // 标记删除
            deleteList.forEach(item -> item.setDeleted(1));
            hardWareInfoApi.updates(deleteList);

            // 创建告警
            createDeleteAlarms(deleteList);
        } catch (Exception e) {
            log.error("[handleDeletedHardware] 处理删除硬件异常", e);
        }
    }

    private void createDeleteAlarms(List<HardWareRespCreateReqDTO> deleteList) {
        Long alertId = alarmConfigApi.getMaxAlertId().getData();
        List<AlarmDorisReqDTO> alarms = new ArrayList<>();

        for (int i = 0; i < deleteList.size(); i++) {
            HardWareRespCreateReqDTO hardware = deleteList.get(i);
            AlarmDorisReqDTO alarm = buildDeleteAlarm(hardware, alertId + i + 1);
            alarms.add(alarm);
        }

        // 发送告警
                    Map<String, List> alertMap = new HashMap<>();
        alertMap.put("insertList", alarms);
                    alertMap.put("updateList", new ArrayList<>());
                    alarmConfigApi.createAlarmToDoris(alertMap);
                }

    private AlarmDorisReqDTO buildDeleteAlarm(HardWareRespCreateReqDTO hardware, Long alarmId) {
        return new AlarmDorisReqDTO()
                .setPriority(0)
                .setStatus(0)
                .setIsSolved(0)
                .setFirstAlarmTime(new Date().getTime())
                .setGmtCreate(new Date())
                .setGmtUpdate(new Date())
                .setLastAlarmTime(new Date().getTime())
                .setMonitorName(hardware.getName())
                .setMonitorId(Convert.toStr(hardware.getUuid()))
                .setPlatformName(hardware.getPlatformName())
                .setPlatformId(hardware.getPlatformId())
                .setContent(String.format("该宿主机资源(%s)已被删除！", hardware.getName()))
                .setAlarmName(hardware.getName())
                .setTimes(1)
                .setResourceType(1)
                .setApp("hardware")
                .setAlarmId(0L)
                .setId(alarmId);
    }

    private void syncNicData(PlatformconfigDTO platform, List<HardWareNicRespDTO> nicList) {
        if (CollectionUtil.isEmpty(nicList)) {
            return;
        }

        try {
            List<HardWareNicRespDTO> existingNics = hardWareNicApi.getHardwareNicByPlatformId(platform.getId())
                    .getCheckedData();

            if (existingNics.isEmpty()) {
                hardWareNicApi.adds(nicList);
                return;
            }

            // 分类处理
            Map<String, List<HardWareNicRespDTO>> changes = classifyNicChanges(existingNics, nicList);

            // 执行数据同步
            executeNicChanges(changes);
        } catch (Exception e) {
            log.error("[syncNicData][platformId({}) 同步网卡数据异常]", platform.getId(), e);
        }
    }

    private Map<String, List<HardWareNicRespDTO>> classifyNicChanges(
            List<HardWareNicRespDTO> existingList,
            List<HardWareNicRespDTO> newList) {

        // 使用复合键（uuid + hardwareUuid）作为Map的key
        Map<String, HardWareNicRespDTO> existingMap = existingList.stream()
                        .collect(Collectors.toMap(
                        nic -> buildNicKey(nic),
                        Function.identity()
                ));

        List<HardWareNicRespDTO> toAdd = new ArrayList<>();
        List<HardWareNicRespDTO> toUpdate = new ArrayList<>();
        List<HardWareNicRespDTO> toDelete = new ArrayList<>(existingList);

        for (HardWareNicRespDTO newNic : newList) {
            String key = buildNicKey(newNic);
            HardWareNicRespDTO existingNic = existingMap.get(key);

            if (existingNic == null) {
                toAdd.add(newNic);
            } else if (!existingNic.equals(newNic)) {
                toUpdate.add(newNic);
            }
            toDelete.removeIf(item -> buildNicKey(item).equals(key));
        }

        Map<String, List<HardWareNicRespDTO>> changes = new HashMap<>();
        changes.put("add", toAdd);
        changes.put("update", toUpdate);
        changes.put("delete", toDelete);
        return changes;
    }

    private String buildNicKey(HardWareNicRespDTO nic) {
        return nic.getUuid() + "_" + nic.getHardwareUuid();
    }

    private void executeNicChanges(Map<String, List<HardWareNicRespDTO>> changes) {
        List<HardWareNicRespDTO> toDelete = changes.get("delete");
        List<HardWareNicRespDTO> toUpdate = changes.get("update");
        List<HardWareNicRespDTO> toAdd = changes.get("add");

        if (!CollectionUtil.isEmpty(toDelete)) {
            hardWareNicApi.deletes(toDelete);
        }
        if (!CollectionUtil.isEmpty(toUpdate)) {
            hardWareNicApi.updates(toUpdate);
        }
        if (!CollectionUtil.isEmpty(toAdd)) {
            hardWareNicApi.adds(toAdd);
        }
    }

    private void syncNetworkL2Data(PlatformconfigDTO platform, List<NetWorkL2DTO> networkL2List) {
        if (CollectionUtil.isEmpty(networkL2List)) {
            return;
        }

        try {
            List<NetWorkL2DTO> existingNetworks = networkApi.getNetworkL2ByPlatformId(platform.getId()).getData();
            if (existingNetworks.isEmpty()) {
                networkApi.addNetWorkL2(networkL2List);
                return;
            }

            // 分类处理
            Map<String, List<NetWorkL2DTO>> changes = classifyNetworkChanges(
                    existingNetworks,
                    networkL2List,
                    NetWorkL2DTO::getUuid);

            // 执行数据同步
            executeNetworkL2Changes(changes);
        } catch (Exception e) {
            log.error("[syncNetworkL2Data][platformId({}) 同步L2网络数据异常]", platform.getId(), e);
        }
    }

    private void syncNetworkL3Data(PlatformconfigDTO platform, List<NetWorkL3DTO> networkL3List) {
        if (CollectionUtil.isEmpty(networkL3List)) {
            return;
        }

        try {
            // 获取分片数据
            List<NetWorkL3DTO> shardingData = getShardingData(networkL3List);
            logProcessingItems(shardingData);

            Long networkL3Count = networkApi.getNetWorkL3Count("istack");
            if (networkL3Count == 0) {
                networkApi.addNetWorkL3(shardingData);
                return;
            }

            List<NetWorkL3DTO> existingList = networkApi.getNetWorkL3List("istack").getData();

            // 分类处理
            Map<String, List<NetWorkL3DTO>> changes = classifyNetworkChanges(
                    existingList,
                    shardingData,
                    NetWorkL3DTO::getUuid);

            // 执行数据同步
            executeNetworkL3Changes(changes);
        } catch (Exception e) {
            log.error("[syncNetworkL3Data][platformId({}) 同步L3网络数据异常]", platform.getId(), e);
        }
    }

    private <T> Map<String, List<T>> classifyNetworkChanges(List<T> existingList, List<T> newList,
                                                            Function<T, String> uuidGetter) {
        Map<String, T> existingMap = existingList.stream()
                .collect(Collectors.toMap(uuidGetter, Function.identity()));

        List<T> toAdd = new ArrayList<>();
        List<T> toUpdate = new ArrayList<>();
        List<T> toDelete = new ArrayList<>(existingList);

        for (T newItem : newList) {
            String uuid = uuidGetter.apply(newItem);
            T existingItem = existingMap.get(uuid);

            if (existingItem == null) {
                toAdd.add(newItem);
            } else if (!existingItem.equals(newItem)) {
                toUpdate.add(newItem);
            }
            toDelete.removeIf(item -> uuidGetter.apply(item).equals(uuid));
        }

        Map<String, List<T>> changes = new HashMap<>();
        changes.put("add", toAdd);
        changes.put("update", toUpdate);
        changes.put("delete", toDelete);
        return changes;
    }

    private void executeNetworkL2Changes(Map<String, List<NetWorkL2DTO>> changes) {
        List<NetWorkL2DTO> toDelete = changes.get("delete");
        List<NetWorkL2DTO> toUpdate = changes.get("update");
        List<NetWorkL2DTO> toAdd = changes.get("add");

        if (!CollectionUtil.isEmpty(toDelete)) {
            networkApi.deleteNetWorkL2ByNameList(toDelete);
        }
        if (!CollectionUtil.isEmpty(toUpdate)) {
            networkApi.updateNetWorkL2(toUpdate);
        }
        if (!CollectionUtil.isEmpty(toAdd)) {
            networkApi.addNetWorkL2(toAdd);
        }
    }

    private void executeNetworkL3Changes(Map<String, List<NetWorkL3DTO>> changes) {
        List<NetWorkL3DTO> toDelete = changes.get("delete");
        List<NetWorkL3DTO> toUpdate = changes.get("update");
        List<NetWorkL3DTO> toAdd = changes.get("add");

        if (!CollectionUtil.isEmpty(toDelete)) {
            networkApi.deleteNetWorkL3ByNameList(toDelete);
        }
        if (!CollectionUtil.isEmpty(toUpdate)) {
            networkApi.updateNetWorkL3(toUpdate);
        }
        if (!CollectionUtil.isEmpty(toAdd)) {
            networkApi.addNetWorkL3(toAdd);
        }
    }

    private List<NetWorkL3DTO> getShardingData(List<NetWorkL3DTO> fullList) {
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        log.info("[getShardingData] 当前节点index={}, 总节点数={}", shardIndex, shardTotal);

        return shardTotal < 0 ? fullList :
                StringUtil.getShardingData(fullList, shardTotal, shardIndex);
    }

    private void logProcessingItems(List<NetWorkL3DTO> items) {
        if (!CollectionUtil.isEmpty(items)) {
            items.forEach(item -> log.info("[logProcessingItems] Processing item: {}", item.getName()));
        }
    }

    @XxlJob("collectHostToInflux")
    public void collectToInflux() {
        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                .filter(dto -> "sangFor".equals(dto.getTypeCode()))
                .collect(Collectors.toList());
        if (filteredList.isEmpty()) return;
        // 并行处理每个平台
        for (PlatformconfigDTO platform : filteredList) {
            try {
                processPlatform(platform);
            } catch (Exception e) {
                log.error("Error processing platform: " + platform.getName(), e);
            }
        }
    }

    private void processPlatform(PlatformconfigDTO platform) {
        ExecutorService executorService = Executors.newFixedThreadPool(9);
        try {
            List<Future<BatchPoints>> futures = new ArrayList<>();
            // 创建一个线程池，线程数可以根据 CPU 核心数调整。这里使用 10 个并发线程。
            JSONObject tokenInfo = SangForPwEncryptor.sangForlogin(platform);
            JSONArray hardwares = iSangForDeviceService.getHardware(platform, tokenInfo);

            // 创建批量写入点的容器, 使用线程安全的集合，因为它会被多线程访问
            List<BatchPoints> batchPointsList = Collections.synchronizedList(new ArrayList<>());

            // 并行处理每个 VM
            for (int i = 0; i < hardwares.size(); i++) {
                JSONObject jsonObject = hardwares.getJSONObject(i);
                String vmId = jsonObject.getString("id");
                JSONObject usageInfo = iSangForDeviceService.getHostDetail(platform, tokenInfo, vmId);
                String disk = iSangForDeviceService.getStorageList(platform, tokenInfo, vmId).getJSONArray("data").toJSONString();
                // 提交任务到线程池并行执行
                Future<BatchPoints> future = executorService.submit(new Callable<BatchPoints>() {
                    @Override
                    public BatchPoints call() throws Exception {
                        try {
                            // 处理虚拟机并返回结果
                            return processVM(platform, usageInfo, jsonObject, disk);
                        } catch (Exception e) {
                            log.error("Error processing VM: " + jsonObject.getString("name"), e);
                            return null;  // 出现异常时返回 null
                        }
                    }
                });

                // 将 Future 对象添加到列表中
                futures.add(future);
            }
            // 等待所有任务完成，并将结果添加到 batchPointsList 中

            for (Future<BatchPoints> future : futures) {
                try {
                    BatchPoints result = future.get();  // 阻塞等待任务完成
                    if (result != null && !result.getPoints().isEmpty()) {
                        batchPointsList.add(result);
                    }
                } catch (InterruptedException | ExecutionException e) {
                    log.error("Error retrieving result from future", e);
                }
            }

            // 批量写入InfluxDB
            for (BatchPoints batchPoints : batchPointsList) {
                try {
                    influxDBTemplate.writeBatch(batchPoints);
                } catch (Exception e) {
                    log.error("Error writing to InfluxDB", e);
                }
            }
        } finally {
            // 关闭线程池`
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
            }
        }
    }

    private BatchPoints processVM(PlatformconfigDTO p, JSONObject usageInfo, JSONObject jsonObject, String disk) {
        List<Map> listObjectSec = JSONObject.parseArray(disk, Map.class);
        BigDecimal diskAllUsed = new BigDecimal("0.0");
        BigDecimal total = new BigDecimal("0.0");
        BigDecimal diskAllRate = new BigDecimal("0.0");
        if (listObjectSec.size() > 0) {
            for (Map map : listObjectSec) {
                total = total.add(Convert.toBigDecimal(map.get("total")));
                diskAllUsed = diskAllUsed.add(Convert.toBigDecimal(map.get("used")));
            }
            diskAllRate = NumberUtil.div(diskAllUsed, total, 2);
        }
        BatchPoints.Builder batchPoints = BatchPoints.builder();
        String uuid = jsonObject.getString("id");
        String name = jsonObject.getString("name");
        //cpu使用率
        for (Object item : usageInfo.getJSONArray("cpuInfo")) {
            JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
            Point point = Point.measurement("zj_cloud_hardware")
                    .tag("uuid", uuid)
                    .tag("metricName", "CPUUsedUtilization")
                    .addField("productsName", name)
                    .tag("label", "cpu")
                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                    .tag("host_type", "all")
                    .tag("platformId", StringUtil.toString(p.getId()))
                    .addField("platformName", StringUtil.toString(p.getName()))
                    .addField("host_metricName", "CPUUsedUtilization")
                    .addField("type", "CPUUsedUtilization")
                    .addField("value", Convert.toBigDecimal(obj.get("val")))
                    .time(new Date().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
            Point pointAll = Point.measurement("zj_cloud_hardware")
                    .tag("uuid", uuid)
                    .tag("metricName", "CPUAllUsedUtilization")
                    .addField("productsName", name)
                    .tag("label", "cpu")
                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                    .tag("host_type", "all")
                    .tag("platformId", StringUtil.toString(p.getId()))
                    .addField("platformName", StringUtil.toString(p.getName()))
                    .addField("host_metricName", "CPUAllUsedUtilization")
                    .addField("type", "CPUAllUsedUtilization")
                    .addField("value", Convert.toBigDecimal(obj.get("val")))
                    .time(new Date().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(pointAll);

            Point pointAverage = Point.measurement("zj_cloud_hardware")
                    .tag("uuid", uuid)
                    .tag("metricName", "CPUAverageUsedUtilization")
                    .addField("productsName", name)
                    .tag("label", "cpu")
                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                    .tag("host_type", "CPUAverageUsedUtilization")
                    .tag("platformId", StringUtil.toString(p.getId()))
                    .addField("platformName", StringUtil.toString(p.getName()))
                    .addField("host_metricName", "CPUAverageUsedUtilization")
                    .addField("type", "CPUAverageUsedUtilization")
                    .addField("value", Convert.toBigDecimal(obj.get("val")))
                    .time(new Date().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(pointAverage);
            // cpu空闲使用率
            Point idle = Point.measurement("zj_cloud_hardware")
                    .tag("uuid", uuid)
                    .tag("metricName", "CPUIdleUtilization")
                    .addField("productsName", name)
                    .tag("label", "cpu")
                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                    .tag("host_type", "all")
                    .tag("platformId", StringUtil.toString(p.getId()))
                    .addField("platformName", StringUtil.toString(p.getName()))
                    .addField("host_metricName", "CPUIdleUtilization")
                    .addField("type", "all")
                    .addField("value", NumberUtil.sub(100, Convert.toBigDecimal(obj.get("val"))))
                    .time(StringUtil.toLong(obj.get("time")), TimeUnit.SECONDS)
                    .build();
            batchPoints.point(idle);

            Point diskAll = Point.measurement("zj_cloud_hardware")
                    .tag("uuid", uuid)
                    .tag("metricName", "DiskAllUsedCapacityInPercent")
                    .addField("productsName", name)
                    .tag("label", "disk")
                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                    .tag("host_type", "all")
                    .tag("platformId", StringUtil.toString(p.getId()))
                    .addField("platformName", StringUtil.toString(p.getName()))

                    .addField("host_metricName", "DiskAllUsedCapacityInPercent")
                    .addField("type", "all")
                    .addField("value", NumberUtil.mul(diskAllRate, 100))
                    .time(StringUtil.toLong(obj.get("time")), TimeUnit.SECONDS)
                    .build();
            batchPoints.point(diskAll);


            Point diskAllUsedPoint = Point.measurement("zj_cloud_hardware")
                    .tag("uuid", uuid)
                    .tag("metricName", "DiskAllUsedCapacityInBytes")
                    .addField("productsName", name)
                    .tag("label", "disk")
                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                    .tag("host_type", "all")
                    .tag("platformId", StringUtil.toString(p.getId()))
                    .addField("platformName", StringUtil.toString(p.getName()))

                    .addField("host_metricName", "DiskAllUsedCapacityInBytes")
                    .addField("type", "all")
                    .addField("value", diskAllUsed)
                    .time(StringUtil.toLong(obj.get("time")), TimeUnit.SECONDS)
                    .build();
            batchPoints.point(diskAllUsedPoint);

        }
        //内存使用情况
        for (Object item : usageInfo.getJSONArray("usedMemInfo")) {
            JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
            Point point = Point.measurement("zj_cloud_hardware")
                    .tag("uuid", uuid)
                    .tag("metricName", "MemoryUsedBytes")
                    .addField("productsName", name)
                    .tag("label", "mem")
                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                    .tag("host_type", "all")
                    .tag("platformId", StringUtil.toString(p.getId()))
                    .addField("platformName", StringUtil.toString(p.getName()))
                    .addField("host_metricName", "MemoryUsedBytes")
                    .addField("type", "all")
                    .addField("value", Convert.toBigDecimal(obj.get("val")))
                    .time(StringUtil.toLong(obj.get("time")), TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }
        //内存空闲情况
        for (Object item : usageInfo.getJSONArray("freeMemInfo")) {
            JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
            Point point = Point.measurement("zj_cloud_hardware")
                    .tag("uuid", uuid)
                    .tag("metricName", "MemoryFreeBytes")
                    .addField("productsName", name)
                    .tag("label", "mem")
                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                    .tag("host_type", "all")
                    .tag("platformId", StringUtil.toString(p.getId()))
                    .addField("platformName", StringUtil.toString(p.getName()))
                    .addField("host_metricName", "MemoryFreeBytes")
                    .addField("type", "all")
                    .addField("value", Convert.toBigDecimal(obj.get("val")))
                    .time(StringUtil.toLong(obj.get("time")), TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
            for (Object itme2 : usageInfo.getJSONArray("usedMemInfo")) {
                JSONObject usageItem2 = JSONObject.parseObject(JSONUtil.toJsonStr(itme2));
                if (StringUtil.toString(obj.get("time")).equals(StringUtil.toString(usageItem2.get("time")))) {
                    BigDecimal value = NumberUtil.add(Convert.toBigDecimal(obj.get("val")), Convert.toBigDecimal(usageItem2.get("val")));
                    BigDecimal usedInPercent = usageItem2.getLong("val") > 0 ? NumberUtil.div(Convert.toBigDecimal(usageItem2.get("val")), value).multiply(BigDecimal.valueOf(100)) : new BigDecimal(0);
                    Point points = Point.measurement("zj_cloud_hardware")
                            .tag("uuid", uuid)
                            .tag("metricName", "MemoryUsedInPercent")
                            .tag("label", "mem")
                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                            .tag("vm_type", "all")
                            .tag("platformId", StringUtil.toString(p.getId()))
                            .addField("productsName", name)
                            .addField("platformName", StringUtil.toString(p.getName()))
                            .addField("vm_metricName", "MemoryUsedInPercent")
                            .addField("type", "MemoryUsedInPercent")
                            .addField("value", NumberUtil.round(usedInPercent, 2))
                            .time(StringUtil.toLong(obj.get("time")), TimeUnit.SECONDS).build();
                    batchPoints.point(points);
                }
            }
        }
        //IO读速率
        for (Object item : usageInfo.getJSONArray("ioReadSpeed")) {
            JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
            Point point = Point.measurement("zj_cloud_hardware")
                    .tag("uuid", uuid)
                    .tag("metricName", "DiskReadBytes")
                    .addField("productsName", name)
                    .tag("label", "disk")
                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                    .tag("host_type", "all")
                    .tag("platformId", StringUtil.toString(p.getId()))
                    .addField("platformName", StringUtil.toString(p.getName()))
                    .addField("host_metricName", "DiskReadBytes")
                    .addField("type", "all")
                    .addField("value", Convert.toBigDecimal(obj.get("val")))
                    .time(StringUtil.toLong(obj.get("time")), TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }
        //IO写速率
        for (Object item : usageInfo.getJSONArray("ioWriteSpeed")) {
            JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
            Point point = Point.measurement("zj_cloud_hardware")
                    .tag("uuid", uuid)
                    .tag("metricName", "DiskWriteBytes")
                    .addField("productsName", name)
                    .tag("label", "disk")
                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                    .tag("host_type", "all")
                    .tag("platformId", StringUtil.toString(p.getId()))
                    .addField("platformName", StringUtil.toString(p.getName()))
                    .addField("host_metricName", "DiskWriteBytes")
                    .addField("type", "all")
                    .addField("value", Convert.toBigDecimal(obj.get("val")))
                    .time(StringUtil.toLong(obj.get("time")), TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }
        //IO读IOPS
        for (Object item : usageInfo.getJSONArray("ioReadOPER")) {
            JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
            Point point = Point.measurement("zj_cloud_hardware")
                    .tag("uuid", uuid)
                    .tag("metricName", "DiskReadOps")
                    .addField("productsName", name)
                    .tag("label", "disk")
                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                    .tag("host_type", "all")
                    .tag("platformId", StringUtil.toString(p.getId()))
                    .addField("platformName", StringUtil.toString(p.getName()))
                    .addField("host_metricName", "DiskReadOps")
                    .addField("type", "all")
                    .addField("value", Convert.toBigDecimal(obj.get("val")))
                    .time(StringUtil.toLong(obj.get("time")), TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }
        //IO写IOPS
        for (Object item : usageInfo.getJSONArray("ioWriteOPER")) {
            JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
            Point point = Point.measurement("zj_cloud_hardware")
                    .tag("uuid", uuid)
                    .tag("metricName", "DiskWriteOps")
                    .addField("productsName", name)
                    .tag("label", "disk")
                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                    .tag("host_type", "all")
                    .tag("platformId", StringUtil.toString(p.getId()))
                    .addField("platformName", StringUtil.toString(p.getName()))
                    .addField("host_metricName", "DiskWriteOps")
                    .addField("type", "all")
                    .addField("value", Convert.toBigDecimal(obj.get("val")))
                    .time(StringUtil.toLong(obj.get("time")), TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }
        //网络接收
        for (Object item : usageInfo.getJSONArray("netIn")) {
            JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
            Point point = Point.measurement("zj_cloud_hardware")
                    .tag("uuid", uuid)
                    .tag("metricName", "NetworkInBytes")
                    .addField("productsName", name)
                    .tag("label", "net")
                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                    .tag("host_type", "all")
                    .tag("platformId", StringUtil.toString(p.getId()))
                    .addField("platformName", StringUtil.toString(p.getName()))
                    .addField("host_metricName", "NetworkInBytes")
                    .addField("type", "all")
                    .addField("value", Convert.toBigDecimal(obj.get("val")))
                    .time(StringUtil.toLong(obj.get("time")), TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }
        //网络发送
        for (Object item : usageInfo.getJSONArray("netOut")) {
            JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
            Point point = Point.measurement("zj_cloud_hardware")
                    .tag("uuid", uuid)
                    .tag("metricName", "NetworkOutBytes")
                    .addField("productsName", name)
                    .tag("label", "net")
                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                    .tag("host_type", "all")
                    .tag("platformId", StringUtil.toString(p.getId()))
                    .addField("platformName", StringUtil.toString(p.getName()))
                    .addField("host_metricName", "NetworkOutBytes")
                    .addField("type", "all")
                    .addField("value", Convert.toBigDecimal(obj.get("val")))
                    .time(StringUtil.toLong(obj.get("time")), TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }
        return batchPoints.build();

    }


    // if (platformconfigDTOList.size() > 0) {
    //            for (PlatformconfigDTO p : platformconfigDTOList) {
    //                if (p.getTypeCode().equals("sangFor")) {
    //                    JSONObject tokenInfo = SangForPwEncryptor.sangForlogin(p);
    //                    JSONArray hardwares = iSangForDeviceService.getHardware(p, tokenInfo);
    //                    BatchPoints batchPoints = BatchPoints.builder().build();
    //                    for (int j = 0; j < hardwares.size(); j++) {
    //                        JSONObject jsonObject = hardwares.getJSONObject(j);
    //                        JSONObject usageInfo = iSangForDeviceService.getHostDetail(p, tokenInfo, jsonObject.getString("id"));
    //                        String disk = iSangForDeviceService.getStorageList(p, tokenInfo, jsonObject.getString("id")).getJSONArray("data").toJSONString();
    //                        List<Map> listObjectSec = JSONObject.parseArray(disk, Map.class);
    //
    //                        BigDecimal diskAllUsed = new BigDecimal("0.0");
    //                        BigDecimal total = new BigDecimal("0.0");
    //                        BigDecimal diskAllRate = new BigDecimal("0.0");
    //                        if (listObjectSec.size()>0){
    //                            for (Map map : listObjectSec) {
    //                                total= total.add(Convert.toBigDecimal(map.get("total")));
    //                                diskAllUsed= diskAllUsed.add(Convert.toBigDecimal(map.get("used")));
    //                            }
    //                            diskAllRate = NumberUtil.div(diskAllUsed,total,2);
    //                        }
    //
    //
    //
    //                        String uuid = jsonObject.getString("id");
    //                        String name = jsonObject.getString("name");
    //                        //cpu使用率
    //                        for (Object item : usageInfo.getJSONArray("cpuInfo")) {
    //                            JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
    //                            Point point = Point.measurement("zj_cloud_hardware")
    //                                    .tag("uuid", uuid)
    //                                    .tag("metricName", "CPUAverageUsedUtilization")
    //                                    .addField("productsName", name)
    //                                    .tag("label", "cpu")
    //                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
    //                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
    //                                    .tag("host_type", "CPUAverageUsedUtilization")
    //                                    .tag("platformId", StringUtil.toString(p.getId()))
    //                                    .addField("platformName", StringUtil.toString(p.getName()))
    //                                    .addField("host_metricName", "CPUAverageUsedUtilization")
    //                                    .addField("type", "CPUAverageUsedUtilization")
    //                                    .addField("value", Convert.toBigDecimal(obj.get("val")))
    //                                    .time(StringUtil.toLong(obj.get("time")), TimeUnit.SECONDS)
    //                                    .build();
    //                            batchPoints.point(point);
    //
    //                            Point diskAll = Point.measurement("zj_cloud_hardware")
    //                                    .tag("uuid", uuid)
    //                                    .tag("metricName", "DiskAllUsedCapacityInPercent")
    //                                    .addField("productsName", name)
    //                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
    //                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
    //                                    .tag("host_type", "DiskAllUsedCapacityInPercent")
    //                                    .tag("platformId", StringUtil.toString(p.getId()))
    //                                    .addField("platformName", StringUtil.toString(p.getName()))
    //
    //                                    .addField("host_metricName", "DiskAllUsedCapacityInPercent")
    //                                    .addField("type", "DiskAllUsedCapacityInPercent")
    //                                    .addField("value",NumberUtil.mul(diskAllRate,100)  )
    //                                    .time(StringUtil.toLong(obj.get("time")), TimeUnit.SECONDS)
    //                                    .build();
    //                            batchPoints.point(diskAll);
    //
    //
    //                            Point diskAllUsedPoint = Point.measurement("zj_cloud_hardware")
    //                                    .tag("uuid", uuid)
    //                                    .tag("metricName", "DiskAllUsedCapacityInBytes")
    //                                    .addField("productsName", name)
    //                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
    //                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
    //                                    .tag("host_type", "DiskAllUsedCapacityInBytes")
    //                                    .tag("platformId", StringUtil.toString(p.getId()))
    //                                    .addField("platformName", StringUtil.toString(p.getName()))
    //
    //                                    .addField("host_metricName", "DiskAllUsedCapacityInBytes")
    //                                    .addField("type", "DiskAllUsedCapacityInBytes")
    //                                    .addField("value", diskAllUsed)
    //                                    .time(StringUtil.toLong(obj.get("time")), TimeUnit.SECONDS)
    //                                    .build();
    //                            batchPoints.point(diskAllUsedPoint);
    //
    //                        }
    //                        //内存使用情况
    //                        for (Object item : usageInfo.getJSONArray("usedMemInfo")) {
    //                            JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
    //                            Point point = Point.measurement("zj_cloud_hardware")
    //                                    .tag("uuid", uuid)
    //                                    .tag("metricName", "MemoryUsedBytes")
    //                                    .addField("productsName", name)
    //                                    .tag("label", "mem")
    //                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
    //                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
    //                                    .tag("host_type", "all")
    //                                    .tag("platformId", StringUtil.toString(p.getId()))
    //                                    .addField("platformName", StringUtil.toString(p.getName()))
    //                                    .addField("host_metricName", "MemoryUsedBytes")
    //                                    .addField("type", "all")
    //                                    .addField("value", Convert.toBigDecimal(obj.get("val")))
    //                                    .time(StringUtil.toLong(obj.get("time")), TimeUnit.SECONDS)
    //                                    .build();
    //                            batchPoints.point(point);
    //                        }
    //                        //内存空闲情况
    //                        for (Object item : usageInfo.getJSONArray("freeMemInfo")) {
    //                            JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
    //                            Point point = Point.measurement("zj_cloud_hardware")
    //                                    .tag("uuid", uuid)
    //                                    .tag("metricName", "MemoryFreeBytes")
    //                                    .addField("productsName", name)
    //                                    .tag("label", "mem")
    //                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
    //                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
    //                                    .tag("host_type", "all")
    //                                    .tag("platformId", StringUtil.toString(p.getId()))
    //                                    .addField("platformName", StringUtil.toString(p.getName()))
    //                                    .addField("host_metricName", "MemoryFreeBytes")
    //                                    .addField("type", "all")
    //                                    .addField("value", Convert.toBigDecimal(obj.get("val")))
    //                                    .time(StringUtil.toLong(obj.get("time")), TimeUnit.SECONDS)
    //                                    .build();
    //                            batchPoints.point(point);
    //                            for (Object itme2 : usageInfo.getJSONArray("usedMemInfo")) {
    //                                JSONObject usageItem2 = JSONObject.parseObject(JSONUtil.toJsonStr(itme2));
    //                                if (StringUtil.toString(obj.get("time")).equals(StringUtil.toString(usageItem2.get("time")))) {
    //                                    BigDecimal value = NumberUtil.add(Convert.toBigDecimal(obj.get("val")), Convert.toBigDecimal(usageItem2.get("val")));
    //                                    BigDecimal usedInPercent = usageItem2.getLong("val") > 0 ? NumberUtil.div(Convert.toBigDecimal(usageItem2.get("val")), value).multiply(BigDecimal.valueOf(100)) : new BigDecimal(0);
    //                                    Point points = Point.measurement("zj_cloud_hardware")
    //                                            .tag("uuid", uuid)
    //                                            .tag("metricName", "MemoryUsedInPercent")
    //                                            .tag("label", "mem")
    //                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
    //                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
    //                                            .tag("vm_type", "all")
    //                                            .tag("platformId", StringUtil.toString(p.getId()))
    //                                            .addField("productsName", name)
    //                                            .addField("platformName", StringUtil.toString(p.getName()))
    //                                            .addField("vm_metricName", "MemoryUsedInPercent")
    //                                            .addField("type", "all")
    //                                            .addField("value", NumberUtil.round(usedInPercent, 2))
    //                                            .time(StringUtil.toLong(obj.get("time")), TimeUnit.SECONDS).build();
    //                                    batchPoints.point(points);
    //                                }
    //                            }
    //                        }
    //                        //IO读速率
    //                        for (Object item : usageInfo.getJSONArray("ioReadSpeed")) {
    //                            JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
    //                            Point point = Point.measurement("zj_cloud_hardware")
    //                                    .tag("uuid", uuid)
    //                                    .tag("metricName", "DiskReadBytes")
    //                                    .addField("productsName", name)
    //                                    .tag("label", "disk")
    //                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
    //                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
    //                                    .tag("host_type", "all")
    //                                    .tag("platformId", StringUtil.toString(p.getId()))
    //                                    .addField("platformName", StringUtil.toString(p.getName()))
    //                                    .addField("host_metricName", "DiskReadBytes")
    //                                    .addField("type", "all")
    //                                    .addField("value", Convert.toBigDecimal(obj.get("val")))
    //                                    .time(StringUtil.toLong(obj.get("time")), TimeUnit.SECONDS)
    //                                    .build();
    //                            batchPoints.point(point);
    //                        }
    //                        //IO写速率
    //                        for (Object item : usageInfo.getJSONArray("ioWriteSpeed")) {
    //                            JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
    //                            Point point = Point.measurement("zj_cloud_hardware")
    //                                    .tag("uuid", uuid)
    //                                    .tag("metricName", "DiskWriteBytes")
    //                                    .addField("productsName", name)
    //                                    .tag("label", "disk")
    //                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
    //                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
    //                                    .tag("host_type", "all")
    //                                    .tag("platformId", StringUtil.toString(p.getId()))
    //                                    .addField("platformName", StringUtil.toString(p.getName()))
    //                                    .addField("host_metricName", "DiskWriteBytes")
    //                                    .addField("type", "all")
    //                                    .addField("value", Convert.toBigDecimal(obj.get("val")))
    //                                    .time(StringUtil.toLong(obj.get("time")), TimeUnit.SECONDS)
    //                                    .build();
    //                            batchPoints.point(point);
    //                        }
    //                        //IO读IOPS
    //                        for (Object item : usageInfo.getJSONArray("ioReadOPER")) {
    //                            JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
    //                            Point point = Point.measurement("zj_cloud_hardware")
    //                                    .tag("uuid", uuid)
    //                                    .tag("metricName", "DiskReadOps")
    //                                    .addField("productsName", name)
    //                                    .tag("label", "disk")
    //                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
    //                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
    //                                    .tag("host_type", "all")
    //                                    .tag("platformId", StringUtil.toString(p.getId()))
    //                                    .addField("platformName", StringUtil.toString(p.getName()))
    //                                    .addField("host_metricName", "DiskReadOps")
    //                                    .addField("type", "all")
    //                                    .addField("value", Convert.toBigDecimal(obj.get("val")))
    //                                    .time(StringUtil.toLong(obj.get("time")), TimeUnit.SECONDS)
    //                                    .build();
    //                            batchPoints.point(point);
    //                        }
    //                        //IO写IOPS
    //                        for (Object item : usageInfo.getJSONArray("ioWriteOPER")) {
    //                            JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
    //                            Point point = Point.measurement("zj_cloud_hardware")
    //                                    .tag("uuid", uuid)
    //                                    .tag("metricName", "DiskWriteOps")
    //                                    .addField("productsName", name)
    //                                    .tag("label", "disk")
    //                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
    //                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
    //                                    .tag("host_type", "all")
    //                                    .tag("platformId", StringUtil.toString(p.getId()))
    //                                    .addField("platformName", StringUtil.toString(p.getName()))
    //                                    .addField("host_metricName", "DiskWriteOps")
    //                                    .addField("type", "all")
    //                                    .addField("value", Convert.toBigDecimal(obj.get("val")))
    //                                    .time(StringUtil.toLong(obj.get("time")), TimeUnit.SECONDS)
    //                                    .build();
    //                            batchPoints.point(point);
    //                        }
    //                        //网络接收
    //                        for (Object item : usageInfo.getJSONArray("netIn")) {
    //                            JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
    //                            Point point = Point.measurement("zj_cloud_hardware")
    //                                    .tag("uuid", uuid)
    //                                    .tag("metricName", "NetworkInBytes")
    //                                    .addField("productsName", name)
    //                                    .tag("label", "net")
    //                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
    //                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
    //                                    .tag("host_type", "all")
    //                                    .tag("platformId", StringUtil.toString(p.getId()))
    //                                    .addField("platformName", StringUtil.toString(p.getName()))
    //                                    .addField("host_metricName", "NetworkInBytes")
    //                                    .addField("type", "all")
    //                                    .addField("value", Convert.toBigDecimal(obj.get("val")))
    //                                    .time(StringUtil.toLong(obj.get("time")), TimeUnit.SECONDS)
    //                                    .build();
    //                            batchPoints.point(point);
    //                        }
    //                        //网络发送
    //                        for (Object item : usageInfo.getJSONArray("netOut")) {
    //                            JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
    //                            Point point = Point.measurement("zj_cloud_hardware")
    //                                    .tag("uuid", uuid)
    //                                    .tag("metricName", "NetworkOutBytes")
    //                                    .addField("productsName", name)
    //                                    .tag("label", "net")
    //                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
    //                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
    //                                    .tag("host_type", "all")
    //                                    .tag("platformId", StringUtil.toString(p.getId()))
    //                                    .addField("platformName", StringUtil.toString(p.getName()))
    //                                    .addField("host_metricName", "NetworkOutBytes")
    //                                    .addField("type", "all")
    //                                    .addField("value", Convert.toBigDecimal(obj.get("val")))
    //                                    .time(StringUtil.toLong(obj.get("time")), TimeUnit.SECONDS)
    //                                    .build();
    //                            batchPoints.point(point);
    //                        }
    //                    }
    //                    int shardIndex = XxlJobHelper.getShardIndex();
    //                    int shardTotal = XxlJobHelper.getShardTotal();
    //                    log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
    //                    List<Point> s = StringUtil.getShardingData(batchPoints.getPoints(), shardTotal, shardIndex);
    //                    influxDBTemplate.writeBatch(BatchPoints.builder().points(s).build());
    //                }
    //            }
    //        }
}
