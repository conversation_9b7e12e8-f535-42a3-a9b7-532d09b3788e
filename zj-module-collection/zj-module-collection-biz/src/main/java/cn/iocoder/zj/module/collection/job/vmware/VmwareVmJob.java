package cn.iocoder.zj.module.collection.job.vmware;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.module.collection.dal.dataobject.vmware.PerMonitorDO;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.framework.influx.config.InfluxDBTemplate;
import cn.iocoder.zj.module.collection.job.volume.MediaType;
import cn.iocoder.zj.module.collection.service.vmware.host.HostComputerResourceSummary;
import cn.iocoder.zj.module.collection.service.vmware.perf.RealtimePerfMonitor;
import cn.iocoder.zj.module.collection.service.vmware.vm.VmComputerResourceSummary;
import cn.iocoder.zj.module.collection.util.PowerStateUtil;
import cn.iocoder.zj.module.collection.util.SampleUtil;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.monitor.api.hostinfo.HostInfoApi;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.hostnic.HostNicApi;
import cn.iocoder.zj.module.monitor.api.hostnic.dto.HostNicCreateReqDto;
import cn.iocoder.zj.module.monitor.api.valume.VolumeApi;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeDTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.vmware.vim25.*;
import com.vmware.vim25.mo.*;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.rmi.RemoteException;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * @ClassName : VmwareVmJob  //类名
 * @Description :  虚拟机数据采集//描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/7/31  9:12
 */
@Component
@Slf4j
public class VmwareVmJob {

    @Resource
    InfluxDBTemplate influxDBTemplate;
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    PlatformRedisDAO platformRedisDAO;
    @Resource
    HostInfoApi hostInfoApi;
    @Resource
    VolumeApi volumeApi;
    @Resource
    HostNicApi hostNicApi;

    public final Map<Long, List<HostInfoRespCreateReqDTO>> vmData = new ConcurrentHashMap<>();

    public final Map<Long, List<HostInfoRespCreateReqDTO>> vmInfo = new ConcurrentHashMap<>();

    public void vmstorageJobList() throws Exception {
        vmwarehostInfo();
        vmwarevmPre();
    }


    private final ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());

    @XxlJob("vmwarevmInfo")
    public void vmwarehostInfo() throws Exception {

        List<PlatformconfigDTO> filteredList = platformRedisDAO.get("platform").stream()
                .filter(dto -> "vmware".equals(dto.getTypeCode()))
                .collect(Collectors.toList());
        if (filteredList.isEmpty()) return;
        for (PlatformconfigDTO platformconfigDTO : filteredList) {
            if (platformconfigDTO.getId() == 18) {
                continue;
            }
            if (platformconfigDTO.getId()==15){
                processPlatformConfig(platformconfigDTO);
            }

        }

    }


    private CompletableFuture<Void> processPlatformConfigAsync(PlatformconfigDTO platformconfigDTO) {
        return CompletableFuture.runAsync(() -> {
            try {
                processPlatformConfig(platformconfigDTO);
            } catch (Exception e) {
                log.error("Error processing platform config", e);
            }
        }, executor);
    }


    // 初始化配置
    private void processPlatformConfig(PlatformconfigDTO platformconfigDTO) throws Exception {
        System.out.println("平台已进入！" + platformconfigDTO);
        Map<String, Date> createDateInfo = new HashMap<>();
        List<HostInfoRespCreateReqDTO> hostInfoApiVmByPlatformId1 = hostInfoApi.getVmByPlatformId(platformconfigDTO.getId());
        if (!hostInfoApiVmByPlatformId1.isEmpty()) {
            hostInfoApiVmByPlatformId1.forEach(hostInfo -> createDateInfo.put(hostInfo.getUuid(), hostInfo.getVCreateDate()));
        }

        // 创建服务实例并获取主机列表
        ServiceInstance serviceInstance = null;
        try {
            serviceInstance = SampleUtil.createServiceInstance(
                    platformconfigDTO.getUrl(),
                    platformconfigDTO.getUsername(),
                    platformconfigDTO.getPassword()
            );
            List<VirtualMachine> virtualMachines = VmComputerResourceSummary.getVmList(serviceInstance);
            List<HostInfoRespCreateReqDTO> hostInfoRespCreateReqDTOList = processHostInfo(serviceInstance, virtualMachines, createDateInfo, platformconfigDTO);
            List<VolumeDTO> volumeDTOList = processVolumeInfo(serviceInstance, virtualMachines, platformconfigDTO);
            List<HostNicCreateReqDto> nicDTOList = processHostNic(serviceInstance, virtualMachines, createDateInfo, platformconfigDTO);
            updateHostInfo(hostInfoRespCreateReqDTOList, platformconfigDTO.getId());
            updateVolumeInfo(volumeDTOList, platformconfigDTO.getId());
            updateNicInfo(nicDTOList, platformconfigDTO.getId());
        } finally {
            // 确保服务实例正确登出
            if (serviceInstance != null) {
                try {
                    serviceInstance.getServerConnection().logout();
                } catch (Exception e) {
                    log.warn("服务实例登出失败: {}", e.getMessage());
                }
            }
        }
    }

    private void updateVolumeInfo(List<VolumeDTO> volumeDTOList, Long id) {
        if (!volumeDTOList.isEmpty()) {
            List<VolumeDTO> existingVolume = volumeApi.getVolumeByPlatformId(id);
            if (existingVolume.isEmpty()) {
                volumeApi.addVolumes(volumeDTOList);
            } else {
                Map<String, VolumeDTO> existingVolumeMap = existingVolume.stream()
                        .collect(Collectors.toMap(VolumeDTO::getUuid, volumeInfo -> volumeInfo));

                List<VolumeDTO> newVolume = new ArrayList<>();
                List<VolumeDTO> updateVolume = new ArrayList<>();
                List<VolumeDTO> deleteEntries = existingVolume.stream()
                        .filter(item -> !volumeDTOList.stream()
                                .map(VolumeDTO::getUuid).collect(Collectors.toList())
                                .contains(item.getUuid())).collect(Collectors.toList());

                for (VolumeDTO volumeDTO : volumeDTOList) {
                    VolumeDTO existingvolume = existingVolumeMap.get(volumeDTO.getUuid());
                    if (existingvolume == null) {
                        newVolume.add(volumeDTO);
                    } else if (!existingvolume.equals(volumeDTO)) {
                        updateVolume.add(volumeDTO);
                    }
                }
                if (!deleteEntries.isEmpty()) {
                    deleteEntries.forEach(item -> item.setDeleted(1L));
                    volumeApi.delVolumes(deleteEntries);
                }
                if (updateVolume.size() > 0) {
                    volumeApi.updateVolumes(updateVolume);
                }
                if (newVolume.size() > 0) {
                    volumeApi.addVolumes(newVolume);
                }
            }

        }
    }

    private void updateNicInfo(List<HostNicCreateReqDto> hostNicCreateReqDtoList, Long id) {
        if (!hostNicCreateReqDtoList.isEmpty()) {
            List<HostNicCreateReqDto> nowNic = hostNicApi.getHostNicsByPlatformId(id).getData();
            //根据uuid筛选出hostNicCreateReqDtoList里需要新增的数据
            Set<String> nowNicUuids = nowNic.stream()
                    .map(HostNicCreateReqDto::getUuid)
                    .collect(Collectors.toSet());

            List<HostNicCreateReqDto> addHostNicList = hostNicCreateReqDtoList.stream()
                    .filter(hostNicCreateReqDto -> !nowNicUuids.contains(hostNicCreateReqDto.getUuid()))
                    .toList();
            if (!addHostNicList.isEmpty()) {
                hostNicApi.addHostNics(addHostNicList);
            }
            //查询updateList 并将nowNicUuids 的id 赋值给updateHostNicList
            List<HostNicCreateReqDto> updateHostNicList = hostNicCreateReqDtoList.stream()
                    .filter(hostNicCreateReqDto -> nowNicUuids.contains(hostNicCreateReqDto.getUuid()))
                    .toList().stream().map(hostNicCreateReqDto -> {
                        return hostNicCreateReqDto.setId(nowNic.stream()
                                .filter(hostNicRespDTO -> hostNicRespDTO.getUuid().equals(hostNicCreateReqDto.getUuid()))
                                .findFirst()
                                .map(HostNicCreateReqDto::getId)
                                .orElse(null));
                    }).toList();
            if (!updateHostNicList.isEmpty()) {
                hostNicApi.updateHostNics(updateHostNicList);
            }
            //根据uuid 判断在nowNic不在hostNicCreateReqDtoList里的数据
            Set<String> hostNicUuids = hostNicCreateReqDtoList.stream()
                    .map(HostNicCreateReqDto::getUuid)
                    .collect(Collectors.toSet());
            List<HostNicCreateReqDto> delHostNicList = nowNic.stream()
                    .filter(hostNicRespDTO -> !hostNicUuids.contains(hostNicRespDTO.getUuid()))
                    .toList();
            if (!delHostNicList.isEmpty()) {
                hostNicApi.delHostNics(delHostNicList);
            }
        }
    }

    private void updateHostInfo(List<HostInfoRespCreateReqDTO> hostInfoRespCreateReqDTOList, Long id) {

        if (!hostInfoRespCreateReqDTOList.isEmpty()) {
            List<HostInfoRespCreateReqDTO> existingHosts = hostInfoApi.getVmByPlatformId(id);
            if (existingHosts.isEmpty()) {
                hostInfoApi.adds(hostInfoRespCreateReqDTOList);
            } else {
                Map<String, HostInfoRespCreateReqDTO> existingHostMap = existingHosts.stream()
                        .collect(Collectors.toMap(HostInfoRespCreateReqDTO::getUuid, hostInfo -> hostInfo));

                List<HostInfoRespCreateReqDTO> newEntries = new ArrayList<>();
                List<HostInfoRespCreateReqDTO> updateEntries = new ArrayList<>();
                List<HostInfoRespCreateReqDTO> deleteEntries = existingHosts.stream()
                        .filter(item -> !hostInfoRespCreateReqDTOList.stream()
                                .map(HostInfoRespCreateReqDTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid()))
                        .collect(Collectors.toList());

                for (HostInfoRespCreateReqDTO newHostInfo : hostInfoRespCreateReqDTOList) {
                    HostInfoRespCreateReqDTO existingHost = existingHostMap.get(newHostInfo.getUuid());
                    if (existingHost == null) {
                        newEntries.add(newHostInfo);
                    } else if (!existingHost.equals(newHostInfo)) {
                        updateEntries.add(newHostInfo);
                    }
                }
                if (!deleteEntries.isEmpty()) {
                    deleteEntries.forEach(item -> item.setDeleted(1).setState("Destroyed"));
                    hostInfoApi.updates(deleteEntries);
                }

                hostInfoApi.updates(updateEntries);
                hostInfoApi.adds(newEntries);
            }
        }

    }


    private List<VolumeDTO> processVolumeInfo(ServiceInstance serviceInstance, List<VirtualMachine> virtualMachines, PlatformconfigDTO platformconfigDTO) {
        List<VolumeDTO> allVolumes = new ArrayList<>();
        for (VirtualMachine vm : virtualMachines) {
            try {
                String uuid = vm.getConfig().getUuid();
                String vms = vm.getMOR().getVal();
                VirtualDevice[] devices = vm.getConfig().getHardware().getDevice();
                String uid = vms + "-" + uuid;
                List<VmDiskFileInfo> diskFileInfos = VmComputerResourceSummary.getVmDiskInfoWithActualSizeVm(vm);
                List<VolumeDTO> vmVolumes = new ArrayList<>();
                volumeVmwareInfo(uid, platformconfigDTO, vm, devices, diskFileInfos, vmVolumes);
                allVolumes.addAll(vmVolumes);
            } catch (Exception e) {
                log.error("Error processing volume info for VM: " + vm.getName(), e);
            }
        }
        return allVolumes;
    }


    private List<HostInfoRespCreateReqDTO> processHostInfo(ServiceInstance serviceInstance, List<VirtualMachine> virtualMachines, Map<String, Date> createDateInfo, PlatformconfigDTO platformconfigDTO) throws Exception {
        List<HostInfoRespCreateReqDTO> list = new ArrayList<>();
        for (VirtualMachine vm : virtualMachines) {
            System.out.println("虚拟机采集中：" + vm.getName());
            HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO = extractHostInfos(serviceInstance, vm, createDateInfo, platformconfigDTO);
            list.add(hostInfoRespCreateReqDTO);
        }
        return list;
    }

    private List<HostNicCreateReqDto> processHostNic(ServiceInstance serviceInstance, List<VirtualMachine> virtualMachines, Map<String, Date> createDateInfo, PlatformconfigDTO platformconfigDTO) throws Exception {
        List<HostNicCreateReqDto> list = new ArrayList<>();
        for (VirtualMachine vm : virtualMachines) {
            System.out.println("虚拟机采集中：" + vm.getName());
            List<HostNicCreateReqDto> nics = extractHostNics(serviceInstance, vm, createDateInfo, platformconfigDTO);
            list.addAll(nics);
        }
        return list;
    }


    private HostInfoRespCreateReqDTO extractHostInfos(ServiceInstance serviceInstance, VirtualMachine virtualMachine, Map<String, Date> createDateInfo, PlatformconfigDTO platformconfigDTO) throws Exception {
        VirtualMachineSummary virtualMachineSummary = virtualMachine.getSummary();
        String uuid = virtualMachineSummary.getConfig().getUuid();
        if (uuid.equals("42312dbd-2657-4ad6-7a91-7d3aac28317b")){
            log.info("虚拟机uuid："+uuid);
        }
        String name = virtualMachine.getName();
        String state = getState(virtualMachineSummary.getRuntime().getPowerState().name());
        String ip = virtualMachine.getGuest().getIpAddress();
        String clusterId = virtualMachine.getParent().getMOR().getVal();
        String clusterName = virtualMachine.getParent().getName();
        String guestOsType = virtualMachineSummary.getConfig().getGuestFullName();
        BigDecimal memtotal = NumberUtil.mul(Convert.toBigDecimal(virtualMachineSummary.getConfig().getMemorySizeMB()), 1024, 1024);
        BigDecimal memusage = NumberUtil.mul(Convert.toBigDecimal(virtualMachineSummary.getQuickStats().getGuestMemoryUsage()), 1024, 1024);
        BigDecimal memoryUsed = NumberUtil.mul(NumberUtil.div(memusage, memtotal, 2), 100);
        long storageUsage = virtualMachineSummary.getStorage().getCommitted();

        List<HostSystem> systems = HostComputerResourceSummary.getHostList(serviceInstance);
        String hardwareUuid = "";
        String hardwareName = "";
        if (systems != null) {
            for (HostSystem system : systems) {
                if (system.getMOR().getVal().equals(virtualMachine.getRuntime().getHost().getVal())) {
                    hardwareUuid = system.getParent().getMOR().getVal() + system.getSummary().getHardware().getUuid() + system.getMOR().getVal();
                    hardwareName = system.getName();
                    break;
                }
            }
        }

        ManagedEntity managedEntities = new InventoryNavigator(serviceInstance.getRootFolder())
                .searchManagedEntity("VirtualMachine", virtualMachine.getName());
        List<PerMonitorDO> cpuMetrics = RealtimePerfMonitor.getPerEntityMericBasesByname(name, serviceInstance, managedEntities, "cpu.usage.average", platformconfigDTO);

        double cpuUsage = cpuMetrics.stream().mapToLong(PerMonitorDO::getValue).average().orElse(0) / 100;
        cpuUsage = cpuUsage < 0 ? 0.00 : cpuUsage;
        VirtualDevice[] devices = virtualMachine.getConfig().getHardware().getDevice();
        String firmware = virtualMachine.getConfig().getFirmware();

        long storageTotal = 0;
        long virtualTotal = 0;
        boolean firstDiskFound = false;
        List<VmDiskFileInfo> diskFileInfos = VmComputerResourceSummary.getVmDiskInfoWithActualSize(virtualMachine);

        if (devices != null && devices.length > 0) {
            for (VirtualDevice virtualDevice : devices) {
                if (virtualDevice instanceof VirtualDisk) {
                    VirtualDisk disk = (VirtualDisk) virtualDevice;
                    if (!firstDiskFound) {
                        firstDiskFound = true; // 第一个 VirtualDisk 找到了，设置标志为 true
                        storageTotal = disk.getCapacityInBytes();
                        continue; // 跳过第一个 VirtualDisk
                    }
                    virtualTotal += disk.getCapacityInBytes();
                }
            }
        }

        BigDecimal storageAll = NumberUtil.add(Convert.toBigDecimal(storageTotal), Convert.toBigDecimal(virtualTotal));
        BigDecimal diskFreeBytes = new BigDecimal(0);
        BigDecimal actualSize = new BigDecimal(0);

        if (!diskFileInfos.isEmpty()) {
            diskFreeBytes = Convert.toBigDecimal(diskFileInfos.get(0).getFileSize());
            for (int i = 1; i < diskFileInfos.size(); i++) { // 从索引 1 开始，跳过第一个元素
                VmDiskFileInfo diskFileInfo = diskFileInfos.get(i);
                // 如果
                if (firmware.equals("efi") && diskFileInfo.getFileSize() > 1048576) {
                    BigDecimal dsk = Convert.toBigDecimal(diskFileInfo.getFileSize());
                    actualSize = actualSize.add(dsk);
                } else if (!firmware.equals("efi")) {
                    BigDecimal dsk = Convert.toBigDecimal(diskFileInfo.getFileSize());
                    actualSize = actualSize.add(dsk);
                }
            }
        }

        List<PerMonitorDO> diskMetrics = RealtimePerfMonitor.getPerEntityMericBasesByname(name, serviceInstance, managedEntities, "disk.usage.average", platformconfigDTO);
        double diskUsage = diskMetrics.stream().mapToLong(PerMonitorDO::getValue).average().orElse(0) / 100;
        String mac = getMacAddresses(virtualMachine);
        if (mac == null || mac.equals("")) {
            mac = "-";
        } else {
            if (mac.contains(",")) {
                // 如果包含逗号，取第一个MAC地址
                mac = mac.split(",")[0].trim();
            }
        }
        String typeName = "vmware";
        String vms = virtualMachine.getMOR().getVal();

        long networkRx = getNetworkUsage(serviceInstance, managedEntities, name, "net.bytesRx.average", platformconfigDTO);
        long networkTx = getNetworkUsage(serviceInstance, managedEntities, name, "net.bytesTx.average", platformconfigDTO);
        long netpacRx = getNetworkUsage(serviceInstance, managedEntities, name, "net.packetsRx.summation", platformconfigDTO);
        long netpacTx = getNetworkUsage(serviceInstance, managedEntities, name, "net.packetsTx.summation", platformconfigDTO);

        Date createDate = createDateInfo.getOrDefault(uuid, new Date());
        if (!state.equals("Stopped")) {
            if (!createDateInfo.containsKey(uuid)) {
                Calendar startTime = DateUtil.calendar(DateUtil.parse("1990-01-01 00:00:00"));
                Calendar endTime = DateUtil.calendar(new Date());
                PerMonitorDO perMonitorDO = new PerMonitorDO();
                try {
                    perMonitorDO = RealtimePerfMonitor.getPerEntityMericBasesCreaateByname(virtualMachine.getName(), serviceInstance, managedEntities, "cpu.usage.average", startTime, endTime);
                } catch (Exception e) {
                    log.info("获取性能数据中日期异常RealtimePerfMonitor：" + e.getMessage());
                }
                if (perMonitorDO != null) {
                    createDate = perMonitorDO.getDateTime();
                }
            }
            if (createDate == null) {
                Calendar startTime = DateUtil.calendar(DateUtil.parse("1990-01-01 00:00:00"));
                Calendar endTime = DateUtil.calendar(new Date());
                PerMonitorDO perMonitorDO = new PerMonitorDO();
                try {
                    perMonitorDO = RealtimePerfMonitor.getPerEntityMericBasesCreaateByname(virtualMachine.getName(), serviceInstance, managedEntities, "cpu.usage.average", startTime, endTime);
                } catch (Exception e) {
                    log.info("获取性能数据中日期异常RealtimePerfMonitor：" + e.getMessage());
                }
                if (perMonitorDO.getDateTime() == null) {
                    startTime = DateUtil.calendar(DateUtil.offsetDay(new Date(), -7));
                    endTime = DateUtil.calendar(new Date());
                    perMonitorDO = new PerMonitorDO();
                    try {
                        perMonitorDO = RealtimePerfMonitor.getPerEntityMericBasesCreaateByname(virtualMachine.getName(), serviceInstance, managedEntities, "cpu.usage.average", startTime, endTime);
                    } catch (Exception e) {
                        log.info("获取性能数据中日期异常RealtimePerfMonitor：" + e.getMessage());
                    }
                }

                if (perMonitorDO.getDateTime() != null) {
                    createDate = perMonitorDO.getDateTime();
                }
            }
        }
        String status = PowerStateUtil.powerStateConvert(state);
        String guideMode = "";
        String autoInitType = "";
        String imageUuid = "";
        String imageName = "";

        ManagedObjectReference hostMor = virtualMachine.getRuntime().getHost();
        HostSystem host = new HostSystem(serviceInstance.getServerConnection(), hostMor);
        ManagedEntity parent = host.getParent();
        if (parent instanceof ClusterComputeResource) {
            ClusterComputeResource cluster = (ClusterComputeResource) parent;
            ClusterConfigInfoEx configEx = (ClusterConfigInfoEx) cluster.getConfigurationEx();
            ClusterDasConfigInfo dasConfig = configEx.getDasConfig();
            if (dasConfig.getEnabled()) {
                autoInitType = "None";
            } else {
                autoInitType = "NeverStop";
            }
        }

        if (StrUtil.isNotEmpty(firmware) && firmware.equals("bios")) {
            guideMode = "Legacy";
        } else if (StrUtil.isNotEmpty(firmware) && firmware.equals("efi")) {
            guideMode = "UEFI";
        } else {
            guideMode = "Other";
        }


        if (virtualMachine.getConfig().isTemplate()) {
            imageUuid = virtualMachine.getConfig().getUuid();
            imageName = virtualMachine.getName();
        }

        log.info("虚拟机采集中：" + name);
        return new HostInfoRespCreateReqDTO()
                .setUuid(vms + "-" + uuid)
                .setName(name)
                .setState(state)
                .setIp(ip)
                .setVipIp("")
                .setClusterUuid(clusterId)
                .setClusterName(clusterName)
                .setHardwareUuid(hardwareUuid)
                .setHardwareName(hardwareName)
                .setArchitecture("x86_64")
                .setGuestOsType(guestOsType)
                .setType("UserVm")
                .setMemoryUsed(memoryUsed)
                .setMemorySize(Convert.toLong(memtotal))
                .setCpuUsed(Convert.toBigDecimal(cpuUsage))
                .setDiskUsed(Convert.toBigDecimal(diskUsage))
                .setCpuNum(virtualMachineSummary.getConfig().getNumCpu())
                .setMac(mac)
                .setDiskFreeBytes(diskFreeBytes)
                .setDiskUsedBytes(Convert.toBigDecimal(storageUsage))
                .setTotalDiskCapacity(Convert.toBigDecimal(storageTotal))
                .setCloudSize(Convert.toBigDecimal(virtualTotal))
                .setTypeName(typeName)
                .setVms(vms)
                .setPlatformId(platformconfigDTO.getId())
                .setPlatformName(platformconfigDTO.getName())
                .setRegionId(platformconfigDTO.getRegionId())
                .setNetworkInBytes(Convert.toBigDecimal(networkRx))
                .setNetworkOutBytes(Convert.toBigDecimal(networkTx))
                .setNetworkInPackets(Convert.toBigDecimal(netpacRx))
                .setNetworkOutPackets(Convert.toBigDecimal(netpacTx))
                .setVCreateDate(createDate)
                .setActualSize(actualSize)
                .setDeleted(0)
                .setPowerState(status)
                .setZoneName(host.getParent().getParent().getParent().getName())
                .setImageName(imageName)
                .setImageUuid(imageUuid)
                .setAutoInitType(autoInitType)
                .setGuideMode(guideMode);
    }

    private List<HostNicCreateReqDto> extractHostNics(ServiceInstance serviceInstance, VirtualMachine virtualMachine, Map<String, Date> createDateInfo, PlatformconfigDTO platformconfigDTO) throws Exception {
        VirtualMachineSummary virtualMachineSummary = virtualMachine.getSummary();
        String hostUuid = virtualMachine.getMOR().getVal() + "-" + virtualMachineSummary.getConfig().getUuid();
        VirtualDevice[] devices = virtualMachine.getConfig().getHardware().getDevice();

        List<HostNicCreateReqDto> hostNicCreateReqDtoList = new ArrayList<>();

        if (devices != null) {
            for (VirtualDevice virtualDevice : devices) {
                if (virtualDevice instanceof VirtualEthernetCard ethernetCard) {
                    VirtualDeviceBackingInfo backing = ethernetCard.getBacking();
                    HostNicCreateReqDto hostNicCreateReqDto = new HostNicCreateReqDto();
                    hostNicCreateReqDto.setHostUuid(hostUuid);

                    // 根据不同的网卡类型处理
                    if (backing instanceof VirtualEthernetCardDistributedVirtualPortBackingInfo dvsBacking) {
                        // 分布式虚拟交换机
                        hostNicCreateReqDto.setUuid(dvsBacking.getPort().getPortgroupKey() + "_" + dvsBacking.getPort().getPortKey());
                        hostNicCreateReqDto.setNetworkUuid(dvsBacking.getPort().getPortgroupKey());
                    } else if (backing instanceof VirtualEthernetCardNetworkBackingInfo standardBacking) {
                        // 标准虚拟交换机
                        hostNicCreateReqDto.setUuid(standardBacking.getDeviceName() + "_" + ethernetCard.getKey());
                        hostNicCreateReqDto.setNetworkUuid(standardBacking.getDeviceName());
                    } else {
                        // 跳过其他类型的网卡
                        log.warn("未知的网卡类型: {}", backing.getClass().getName());
                        continue;
                    }

                    hostNicCreateReqDto.setIp("");
                    hostNicCreateReqDto.setIp6("");
                    hostNicCreateReqDto.setPlatformId(platformconfigDTO.getId());
                    hostNicCreateReqDto.setPlatformName(platformconfigDTO.getName());
                    hostNicCreateReqDto.setMac(ethernetCard.getMacAddress());
                    hostNicCreateReqDto.setDriver(virtualDevice.getClass().getSimpleName().toLowerCase().replace("virtual", "").replace("virtualmachine", ""));
                    hostNicCreateReqDto.setInClassicNetwork((byte) 0);
                    hostNicCreateReqDtoList.add(hostNicCreateReqDto);
                }
            }
        }
        return hostNicCreateReqDtoList;
    }


    private void extractHostInfo(ServiceInstance serviceInstance, VirtualMachine virtualMachine, Map<String, Date> createDateInfo, PlatformconfigDTO platformconfigDTO, List<HostInfoRespCreateReqDTO> hostInfoRespCreateReqDTOList, List<VolumeDTO> volumeDTOList) throws Exception {
        VirtualMachineSummary virtualMachineSummary = virtualMachine.getSummary();
        String uuid = virtualMachineSummary.getConfig().getUuid();
        String name = virtualMachine.getName();
        String state = getState(virtualMachineSummary.getRuntime().getPowerState().name());
        String ip = virtualMachine.getGuest().getIpAddress();
        String clusterId = virtualMachine.getParent().getMOR().getVal();
        String clusterName = virtualMachine.getParent().getName();
        String guestOsType = virtualMachineSummary.getConfig().getGuestFullName();
        BigDecimal memtotal = NumberUtil.mul(Convert.toBigDecimal(virtualMachineSummary.getConfig().getMemorySizeMB()), 1024, 1024);
        BigDecimal memusage = NumberUtil.mul(Convert.toBigDecimal(virtualMachineSummary.getQuickStats().getGuestMemoryUsage()), 1024, 1024);
        BigDecimal memoryUsed = NumberUtil.mul(NumberUtil.div(memusage, memtotal, 2), 100);
        long storageUsage = virtualMachineSummary.getStorage().getCommitted();

        ManagedEntity managedEntities = new InventoryNavigator(serviceInstance.getRootFolder())
                .searchManagedEntity("VirtualMachine", virtualMachine.getName());
        List<PerMonitorDO> cpuMetrics = RealtimePerfMonitor.getPerEntityMericBasesByname(name, serviceInstance, managedEntities, "cpu.usage.average", platformconfigDTO);

        double cpuUsage = cpuMetrics.stream().mapToLong(PerMonitorDO::getValue).average().orElse(0) / 100;
        VirtualDevice[] devices = virtualMachine.getConfig().getHardware().getDevice();
        String firmware = virtualMachine.getConfig().getFirmware();

        long storageTotal = 0;
        long virtualTotal = 0;
        boolean firstDiskFound = false;
        List<VmDiskFileInfo> diskFileInfos = VmComputerResourceSummary.getVmDiskInfoWithActualSize(virtualMachine);

        if (devices != null && devices.length > 0) {
            for (VirtualDevice virtualDevice : devices) {
                if (virtualDevice instanceof VirtualDisk) {
                    VirtualDisk disk = (VirtualDisk) virtualDevice;
                    if (!firstDiskFound) {
                        firstDiskFound = true; // 第一个 VirtualDisk 找到了，设置标志为 true
                        storageTotal = disk.getCapacityInBytes();
                        continue; // 跳过第一个 VirtualDisk
                    }
                    virtualTotal += disk.getCapacityInBytes();
                }
            }
        }

        BigDecimal storageAll = NumberUtil.add(Convert.toBigDecimal(storageTotal), Convert.toBigDecimal(virtualTotal));
        BigDecimal diskFreeBytes = new BigDecimal(0);
        BigDecimal actualSize = new BigDecimal(0);

        if (!diskFileInfos.isEmpty()) {
            diskFreeBytes = Convert.toBigDecimal(diskFileInfos.get(0).getFileSize());
            for (int i = 1; i < diskFileInfos.size(); i++) { // 从索引 1 开始，跳过第一个元素
                VmDiskFileInfo diskFileInfo = diskFileInfos.get(i);
                // 如果
                if (firmware.equals("efi") && diskFileInfo.getFileSize() > 1048576) {
                    BigDecimal dsk = Convert.toBigDecimal(diskFileInfo.getFileSize());
                    actualSize = actualSize.add(dsk);
                } else if (!firmware.equals("efi")) {
                    BigDecimal dsk = Convert.toBigDecimal(diskFileInfo.getFileSize());
                    actualSize = actualSize.add(dsk);
                }
            }
        }

        BigDecimal diskUsed = NumberUtil.mul(NumberUtil.div(Convert.toBigDecimal(storageUsage), Convert.toBigDecimal(storageAll), 2), 100);
        String mac = getMacAddresses(virtualMachine);
        String typeName = "vmware";
        String vms = virtualMachine.getMOR().getVal();

        long networkRx = getNetworkUsage(serviceInstance, managedEntities, name, "net.bytesRx.average", platformconfigDTO);
        long networkTx = getNetworkUsage(serviceInstance, managedEntities, name, "net.bytesTx.average", platformconfigDTO);
        long netpacRx = getNetworkUsage(serviceInstance, managedEntities, name, "net.packetsRx.summation", platformconfigDTO);
        long netpacTx = getNetworkUsage(serviceInstance, managedEntities, name, "net.packetsTx.summation", platformconfigDTO);

        Date createDate = createDateInfo.getOrDefault(uuid, new Date());
        if (!createDateInfo.containsKey(uuid)) {
            Calendar startTime = DateUtil.calendar(DateUtil.parse("1990-01-01 00:00:00"));
            Calendar endTime = DateUtil.calendar(new Date());
            PerMonitorDO perMonitorDO = new PerMonitorDO();
            try {
                perMonitorDO = RealtimePerfMonitor.getPerEntityMericBasesCreaateByname(virtualMachine.getName(), serviceInstance, managedEntities, "cpu.usage.average", startTime, endTime);
            } catch (Exception e) {
                log.info("获取性能数据中日期异常RealtimePerfMonitor：" + e.getMessage());
            }
            if (perMonitorDO != null) {
                createDate = perMonitorDO.getDateTime();
            }
        }
        // 采集云盘数据
        volumeVmwareInfo(uuid, platformconfigDTO, virtualMachine, devices, diskFileInfos, volumeDTOList);

        hostInfoRespCreateReqDTOList.add(new HostInfoRespCreateReqDTO()
                .setUuid(uuid)
                .setName(name)
                .setState(state)
                .setIp(ip)
                .setVipIp("")
                .setClusterUuid(clusterId)
                .setClusterName(clusterName)
                .setArchitecture("x86_64")
                .setGuestOsType(guestOsType)
                .setType("UserVm")
                .setMemoryUsed(memoryUsed)
                .setMemorySize(Convert.toLong(memtotal))
                .setCpuUsed(Convert.toBigDecimal(cpuUsage))
                .setDiskUsed(diskUsed)
                .setCpuNum(virtualMachineSummary.getConfig().getNumCpu())
                .setMac(mac)
                .setDiskFreeBytes(diskFreeBytes)
                .setDiskUsedBytes(Convert.toBigDecimal(storageUsage))
                .setTotalDiskCapacity(Convert.toBigDecimal(storageTotal))
                .setCloudSize(Convert.toBigDecimal(virtualTotal))
                .setTypeName(typeName)
                .setVms(vms)
                .setPlatformId(platformconfigDTO.getId())
                .setPlatformName(platformconfigDTO.getName())
                .setRegionId(platformconfigDTO.getRegionId())
                .setNetworkInBytes(Convert.toBigDecimal(networkRx))
                .setNetworkOutBytes(Convert.toBigDecimal(networkTx))
                .setNetworkInPackets(Convert.toBigDecimal(netpacRx))
                .setNetworkOutPackets(Convert.toBigDecimal(netpacTx))
                .setVCreateDate(createDate)
                .setActualSize(actualSize)
                .setDeleted(0));
    }


    private String getState(String powerState) {
        switch (powerState) {
            case "poweredOff":
                return "Stopped";
            case "poweredOn":
                return "Running";
            case "suspended":
                return "Suspended";
            default:
                return "Unknown";
        }
    }

    private String getMacAddresses(VirtualMachine virtualMachine) {
        GuestNicInfo[] guestNicInfos = virtualMachine.getGuest().getNet();
        if (guestNicInfos == null) return "";

        return Arrays.stream(guestNicInfos)
                .map(GuestNicInfo::getMacAddress)
                .collect(Collectors.joining(","));
    }

    private long getNetworkUsage(ServiceInstance serviceInstance, ManagedEntity managedEntities, String vmName, String metric, PlatformconfigDTO platformconfigDTO) throws Exception {
        List<PerMonitorDO> metrics = RealtimePerfMonitor.getPerEntityMericBasesByname(vmName, serviceInstance, managedEntities, metric, platformconfigDTO);
        return metrics.stream().mapToLong(PerMonitorDO::getValue).sum();
    }


    public void vmwarevmPre() throws Exception {

        // 获取redis中数据
        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                .filter(dto -> "vmware".equals(dto.getTypeCode()))
                .collect(Collectors.toList());

        if (filteredList.isEmpty()) return;

        ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
        List<Future<Void>> futures = new ArrayList<>();

        for (PlatformconfigDTO platformconfigDTO : filteredList) {
            if (platformconfigDTO.getId() == 18) {
                continue;
            }
            processpreConfig(platformconfigDTO);
        }


    }

    private void processpreConfig(PlatformconfigDTO platformconfigDTO) throws Exception {

        ServiceInstance serviceInstance = SampleUtil.createServiceInstance(platformconfigDTO.getUrl(), platformconfigDTO.getUsername(), platformconfigDTO.getPassword());
        List<HostInfoRespCreateReqDTO> hostInfoApiVmByPlatformId1 = hostInfoApi.getVmByPlatformId(platformconfigDTO.getId());
        BatchPoints batchPoints = BatchPoints.builder().build();
        // 获取ServiceContent
        ServiceContent serviceContent = serviceInstance.getServiceContent();
        // 获取关于vSphere服务器的版本信息
        String apiVersion = serviceContent.getAbout().getApiVersion();

        ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
        List<Future<Void>> futures = new ArrayList<>();
        // 如果小于7.0 走单个性能数据查询
        if (isVersionLessThan(apiVersion, "7.0.0")) {
            Map<String, ManagedEntity> vmEntities = getStringManagedEntityMap(serviceInstance, hostInfoApiVmByPlatformId1);
            processMetricsBydata(serviceInstance, platformconfigDTO, hostInfoApiVmByPlatformId1, batchPoints, executor, futures, vmEntities, true);
        } else {
            processMetricsBydata(serviceInstance, platformconfigDTO, hostInfoApiVmByPlatformId1, batchPoints, executor, futures, null, false);
        }

        for (Future<Void> future : futures) {
            future.get();
        }
        executor.shutdown();
        influxDBTemplate.writeBatch(batchPoints);
        serviceInstance.getServerConnection().logout();
    }

    private void processMetricsBydata(ServiceInstance serviceInstance, PlatformconfigDTO platformconfigDTO, List<HostInfoRespCreateReqDTO> hostInfoApiVmByPlatformId1, BatchPoints batchPoints, ExecutorService executor, List<Future<Void>> futures, Map<String, ManagedEntity> vmEntities, boolean isSingleQuery) {
        for (HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO : hostInfoApiVmByPlatformId1) {
            futures.add(executor.submit(() -> {
                try {
                    if (isSingleQuery) {
                        collectMetricsForByoneVm(serviceInstance, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, vmEntities);
                    } else {
                        collectMetricsForVm(serviceInstance, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints);
                    }
                } catch (Exception e) {
                    log.error("Error collecting metrics for VM", e);
                }
                return null;
            }));
        }
    }

    private void collectMetricsForByoneVm(ServiceInstance serviceInstance, PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BatchPoints batchPoints, Map<String, ManagedEntity> vmEntities) throws Exception {

        ManagedEntity managedEntities = vmEntities.get(hostInfoRespCreateReqDTO.getName());

        long startTime = System.currentTimeMillis(); // 开始时间

//            ManagedEntity managedEntities = new InventoryNavigator(serviceInstance.getRootFolder())
//                    .searchManagedEntity("VirtualMachine", hostInfoRespCreateReqDTO.getName());

        VirtualMachine virtualMachine = (VirtualMachine) managedEntities;
        Datastore[] datastores = virtualMachine.getDatastores();
        Long storage_total = 0L;
        Long storage_total_free = 0L;
        for (Datastore datastore : datastores) {
            storage_total += datastore.getSummary().getCapacity();
            storage_total_free += datastore.getSummary().getFreeSpace();
        }
        BigDecimal memSize = Convert.toBigDecimal(virtualMachine.getSummary().getConfig().getMemorySizeMB());
        BigDecimal storage_usage = NumberUtil.sub(storage_total, storage_total_free);
        BigDecimal storage_rate = NumberUtil.div(storage_total_free, storage_total);
        String uuid = hostInfoRespCreateReqDTO.getUuid();
        // ==========================================CPU==================================================


        // cpu.usage.none CPUUsedUtilization
        List<PerMonitorDO> CPUUsedUtilization = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.usage.none", platformconfigDTO);
        if (!CPUUsedUtilization.isEmpty()) {
            Long allUsage = 0L;
            for (PerMonitorDO perMonitorDO : CPUUsedUtilization) {
                allUsage += perMonitorDO.getValue();
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "cpu")
                        .tag("uuid", uuid)
                        .tag("metricName", "CPUUsedUtilization")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", "CPUUsedUtilization")
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 100))
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }

            BigDecimal v = NumberUtil.div(Convert.toBigDecimal(allUsage), CPUUsedUtilization.size());
            Point point = Point.measurement("zj_cloud_host")
                    .tag("label", "cpu")
                    .tag("uuid", uuid)
                    .tag("metricName", "CPUAllUsedUtilization")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("vm_type", "CPUAllUsedUtilization")
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("vm_metricName", "CPUAllUsedUtilization")
                    .addField("type", "CPUAllUsedUtilization")
                    .addField("value", NumberUtil.div(v, 100))
                    .time(CPUUsedUtilization.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);

        }


        // cpu.usage.average CPUAverageUsedUtilization
        List<PerMonitorDO> CPUAverageUsedUtilization = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.usage.average", platformconfigDTO);
        if (!CPUAverageUsedUtilization.isEmpty()) {
            for (PerMonitorDO perMonitorDO : CPUAverageUsedUtilization) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "cpu")
                        .tag("uuid", uuid)
                        .tag("metricName", "CPUAverageUsedUtilization")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", "CPUAverageUsedUtilization")
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 100, 2))
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }


        // cpu.idle.summation CPUIdleUtilization
        List<PerMonitorDO> CPUIdleUtilization = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.idle.summation", platformconfigDTO);
        if (!CPUIdleUtilization.isEmpty()) {
            for (PerMonitorDO perMonitorDO : CPUIdleUtilization) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "cpu")
                        .tag("uuid", uuid)
                        .tag("metricName", "CPUIdleUtilization")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", "CPUIdleUtilization")
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", Convert.toBigDecimal(perMonitorDO.getValue()))
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }

//        // cpu.usage.none CPUAllUsedUtilization
//        List<PerMonitorDO> CPUAllUsedUtilization = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.usage.none");
//        if (CPUAllUsedUtilization.size() > 0) {
//            for (PerMonitorDO perMonitorDO : CPUAllUsedUtilization) {
//                if (perMonitorDO.getInstance().isEmpty()) {
//                    perMonitorDO.setInstance("0");
//                }
//                Point point = Point.measurement("zj_cloud_host")
//                        .tag("label", "cpu")
//                        .tag("uuid", uuid)
//                        .tag("metricName", "CPUAllUsedUtilization")
//                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                        .tag("vm_type", perMonitorDO.getInstance())
//                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                        .addField("vm_metricName", "CPUAllUsedUtilization")
//                        .addField("type", perMonitorDO.getInstance())
//                        .addField("value", Convert.toBigDecimal(perMonitorDO.getValue()))
//                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                        .build();
//                batchPoints.point(point);
//            }


        // cpu.utilization.average CPUSystemUtilization
        List<PerMonitorDO> CPUSystemUtilization = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.utilization.average", platformconfigDTO);
        if (CPUSystemUtilization.size() > 0) {
            for (PerMonitorDO perMonitorDO : CPUSystemUtilization) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "cpu")
                        .tag("uuid", uuid)
                        .tag("metricName", "CPUSystemUtilization")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", "CPUSystemUtilization")
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", Convert.toBigDecimal(perMonitorDO.getValue()))
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }
        // cpu.used.summation
        List<PerMonitorDO> cpuUsedList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.used.summation", platformconfigDTO);
        Long valueAll = 0L;
        if (cpuUsedList.size() > 0) {
            for (PerMonitorDO perMonitorDO : cpuUsedList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    valueAll += valueAll + perMonitorDO.getValue();
                }
            }
        }

        // cpu.wait.summation
        List<PerMonitorDO> cpuWaitList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.wait.summation", platformconfigDTO);
        if (!cpuWaitList.isEmpty()) {
            for (PerMonitorDO perMonitorDO : cpuWaitList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                BigDecimal value = NumberUtil.div(perMonitorDO.getValue(), valueAll, 2);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "cpu")
                        .tag("uuid", uuid)
                        .tag("metricName", "CPUWaitUtilization")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", "CPUWaitUtilization")
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", value)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }

        //==========================================内存==================================================
        List<PerMonitorDO> memUsageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "mem.usage.average", platformconfigDTO);
        for (PerMonitorDO perMonitorDO : memUsageList) {
            BigDecimal value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 100);
            String label = perMonitorDO.getInstance();
            String type = label + "MemoryUsedInPercent";
            Point point = Point.measurement("zj_cloud_host")
                    .tag("label", "mem")
                    .tag("uuid", uuid)
                    .tag("metricName", "MemoryUsedInPercent")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("vm_type", type)
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("vm_metricName", "MemoryUsedInPercent")
                    .addField("type", "MemoryUsedInPercent")
                    .addField("value", value)
                    .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }


        List<PerMonitorDO> memActiveList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "mem.active.average", platformconfigDTO);
        for (PerMonitorDO perMonitorDO : memActiveList) {
            String label = perMonitorDO.getInstance();
            String type = label + "MemoryUsedBytes";
            BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 1024, 2);
            Point point = Point.measurement("zj_cloud_host")
                    .tag("label", "mem")
                    .tag("uuid", uuid)
                    .tag("metricName", "MemoryUsedBytes")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("vm_type", type)
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("vm_metricName", "MemoryUsedBytes")
                    .addField("type", "MemoryUsedBytes")
                    .addField("value", value)
                    .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }

        for (PerMonitorDO ac : memActiveList) {
            BigDecimal sd = NumberUtil.sub(NumberUtil.mul(memSize, 1024), Convert.toBigDecimal(ac.getValue()));
            String label = ac.getInstance();
            String type = label + "MemoryFreeBytes";
            Point point = Point.measurement("zj_cloud_host")
                    .tag("label", "mem")
                    .tag("uuid", uuid)
                    .tag("metricName", "MemoryFreeBytes")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("vm_type", type)
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("vm_metricName", "MemoryFreeBytes")
                    .addField("type", "MemoryFreeBytes")
                    .addField("value", Convert.toBigDecimal(NumberUtil.mul(sd, 1024)))
                    .time(ac.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }

        //==========================================硬盘==================================================
        List<PerMonitorDO> diskUsageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "disk.usage.average", platformconfigDTO);
        for (PerMonitorDO perMonitorDO : diskUsageList) {
            if (perMonitorDO.getInstance().isEmpty()) {
                perMonitorDO.setInstance("0");
            }


            BigDecimal st = NumberUtil.div(Convert.toBigDecimal(storage_usage), 8);
            BigDecimal value = NumberUtil.mul(st, 1024);
            Point statDiskUsage = Point.measurement("zj_cloud_host")
                    .tag("label", "disk")
                    .tag("uuid", uuid)
                    .tag("metricName", "DiskAllUsedCapacityInBytes")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("vm_type", "DiskAllUsedCapacityInBytes")
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("vm_metricName", "DiskAllUsedCapacityInBytes")
                    .addField("type", "DiskAllUsedCapacityInBytes")
                    .addField("value", value)
                    .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(statDiskUsage);

            Point inPercent = Point.measurement("zj_cloud_host")
                    .tag("label", "disk")
                    .tag("uuid", uuid)
                    .tag("metricName", "DiskAllUsedCapacityInPercent")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("vm_type", "DiskAllUsedCapacityInBytes")
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("vm_metricName", "DiskAllUsedCapacityInPercent")
                    .addField("type", "DiskAllUsedCapacityInPercent")
                    .addField("value", NumberUtil.mul(storage_rate, 100))
                    .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(inPercent);
        }


        List<PerMonitorDO> diskWriteList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "virtualDisk.write.average", platformconfigDTO);
        for (PerMonitorDO perMonitorDO : diskWriteList) {
            if (perMonitorDO.getInstance().isEmpty()) {
                perMonitorDO.setInstance("0");
            }
            String metricName = "DiskReadBytes";
            BigDecimal value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
            BigDecimal lastValue = NumberUtil.mul(value, 1024);
            Point point = Point.measurement("zj_cloud_host")
                    .tag("label", "disk")
                    .tag("uuid", uuid)
                    .tag("metricName", metricName)
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("vm_type", perMonitorDO.getInstance())
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("vm_metricName", metricName)
                    .addField("type", perMonitorDO.getInstance())
                    .addField("value", lastValue)
                    .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }

        List<PerMonitorDO> diskReadList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "virtualDisk.read.average", platformconfigDTO);
        if (!diskReadList.isEmpty()) {
            BigDecimal disk_write_all = new BigDecimal(0);
            for (PerMonitorDO perMonitorDO : diskReadList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                String metricName = "DiskWriteBytes";
                BigDecimal value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.mul(value, 1024);
                disk_write_all = disk_write_all.add(lastValue);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "disk")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
            Point point = Point.measurement("zj_cloud_host")
                    .tag("label", "disk")
                    .tag("uuid", uuid)
                    .tag("metricName", "DiskAllWriteBytes")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("vm_type", "DiskAllWriteBytes")
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("vm_metricName", "DiskAllWriteBytes")
                    .addField("type", "DiskAllWriteBytes")
                    .addField("value", disk_write_all)
                    .time(diskReadList.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }


        List<PerMonitorDO> iopsReadList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "virtualDisk.numberReadAveraged.average", platformconfigDTO);
        for (PerMonitorDO perMonitorDO : iopsReadList) {
            if (perMonitorDO.getInstance().isEmpty()) {
                perMonitorDO.setInstance("0");
            }
            String metricName = "DiskReadOps";
            Point point = Point.measurement("zj_cloud_host")
                    .tag("label", "disk")
                    .tag("uuid", uuid)
                    .tag("metricName", metricName)
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("vm_type", perMonitorDO.getInstance())
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("vm_metricName", metricName)
                    .addField("type", perMonitorDO.getInstance())
                    .addField("value", Convert.toBigDecimal(perMonitorDO.getValue()))
                    .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }

        List<PerMonitorDO> iopsWriteList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "virtualDisk.numberWriteAveraged.average", platformconfigDTO);
        if (!iopsWriteList.isEmpty()) {
            for (PerMonitorDO perMonitorDO : iopsWriteList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                String metricName = "DiskWriteOps";
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "disk")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", Convert.toBigDecimal(perMonitorDO.getValue()))
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }

        // ==========================================网络==================================================
        List<PerMonitorDO> netBytesRxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.bytesRx.average", platformconfigDTO);
        if (!netBytesRxAverageList.isEmpty()) {
            BigDecimal net_rx_all = new BigDecimal(0);
            for (PerMonitorDO perMonitorDO : netBytesRxAverageList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                String metricName = "NetworkInBytes";
                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.mul(value, 1024);
                net_rx_all = net_rx_all.add(lastValue);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "net")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }


            Point point = Point.measurement("zj_cloud_host")
                    .tag("label", "net")
                    .tag("uuid", uuid)
                    .tag("metricName", "NetworkAllInBytes")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("vm_type", "NetworkAllInBytes")
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("vm_metricName", "NetworkAllInBytes")
                    .addField("type", "NetworkAllInBytes")
                    .addField("value", NumberUtil.mul(NumberUtil.div(net_rx_all, 8), 1024))
                    .time(netBytesRxAverageList.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);

        }


        List<PerMonitorDO> netBytesTxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.bytesTx.average", platformconfigDTO);
        if (!netBytesTxAverageList.isEmpty()) {
            BigDecimal net_tx_all = new BigDecimal(0);
            for (PerMonitorDO perMonitorDO : netBytesTxAverageList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                String metricName = "NetworkOutBytes";
                BigDecimal value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.mul(value, 1024);
                net_tx_all = net_tx_all.add(lastValue);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "net")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }

            Point point = Point.measurement("zj_cloud_host")
                    .tag("label", "net")
                    .tag("uuid", uuid)
                    .tag("metricName", "NetworkAllOutBytes")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("vm_type", "NetworkAllOutBytes")
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("vm_metricName", "NetworkAllOutBytes")
                    .addField("type", "NetworkAllOutBytes")
                    .addField("value", NumberUtil.mul(NumberUtil.mul(net_tx_all, 8), 1024))
                    .time(netBytesTxAverageList.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);

        }

        List<PerMonitorDO> netPacketsRxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.packetsRx.summation", platformconfigDTO);
        if (!netPacketsRxAverageList.isEmpty()) {
            for (PerMonitorDO perMonitorDO : netPacketsRxAverageList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                String metricName = "NetworkInPackets";
                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.div(value, 1024);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "net")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }

        List<PerMonitorDO> netPacketsTxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.packetsTx.summation", platformconfigDTO);
        if (!netPacketsTxAverageList.isEmpty()) {
            for (PerMonitorDO perMonitorDO : netPacketsTxAverageList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                String metricName = "NetworkOutPackets";
                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.div(value, 1024);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "net")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }

        List<PerMonitorDO> netDroppedRxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.droppedRx.summation", platformconfigDTO);
        if (!netDroppedRxAverageList.isEmpty()) {
            for (PerMonitorDO perMonitorDO : netDroppedRxAverageList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                String metricName = "NetworkInErrors";
                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.div(value, 1024);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "net")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }

        List<PerMonitorDO> netDroppedTxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.droppedTx.summation", platformconfigDTO);
        if (!netDroppedTxAverageList.isEmpty()) {
            for (PerMonitorDO perMonitorDO : netDroppedTxAverageList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                String metricName = "NetworkOutErrors";
                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.div(value, 1024);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "net")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }

        long eTime = System.currentTimeMillis(); // 结束时间
        long executionTime = eTime - startTime;
        double executionTimeInSeconds = executionTime / 1000.0;


    }

    @NotNull
    private static Map<String, ManagedEntity> getStringManagedEntityMap(ServiceInstance serviceInstance, List<HostInfoRespCreateReqDTO> hostInfoApiVmByPlatformId1) throws RemoteException {
        InventoryNavigator navigator = new InventoryNavigator(serviceInstance.getRootFolder());
        Map<String, ManagedEntity> vmEntities = new HashMap<>();

        // 批量获取所有虚拟机的实体
        for (HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO : hostInfoApiVmByPlatformId1) {
            ManagedEntity managedEntity = navigator.searchManagedEntity("VirtualMachine", hostInfoRespCreateReqDTO.getName());
            if (managedEntity != null) {
                vmEntities.put(hostInfoRespCreateReqDTO.getName(), managedEntity);
            }
        }
        return vmEntities;
    }

    private void collectMetricsForVm(ServiceInstance serviceInstance, PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BatchPoints batchPoints) throws RemoteException {
        long startTime = System.currentTimeMillis(); // 开始时间

        ManagedEntity managedEntities = new InventoryNavigator(serviceInstance.getRootFolder())
                .searchManagedEntity("VirtualMachine", hostInfoRespCreateReqDTO.getName());
        VirtualMachine virtualMachine = (VirtualMachine) managedEntities;


        if (virtualMachine == null) {
            System.out.println("Virtual Machine " + hostInfoRespCreateReqDTO.getName() + " cannot be found.");
            return;
        }

        Datastore[] datastores = virtualMachine.getDatastores();
        Long storage_total = 0L;
        Long storage_total_free = 0L;
        for (Datastore datastore : datastores) {
            storage_total += datastore.getSummary().getCapacity();
            storage_total_free += datastore.getSummary().getFreeSpace();
        }

        BigDecimal memSize = Convert.toBigDecimal(virtualMachine.getSummary().getConfig().getMemorySizeMB());
        BigDecimal storage_usage = NumberUtil.sub(storage_total, storage_total_free);
        BigDecimal storage_rate = NumberUtil.div(storage_total_free, storage_total);
        String uuid = hostInfoRespCreateReqDTO.getUuid();

        // Collect all required metrics in a single call
        List<String> counterNames = Arrays.asList(
                "cpu.usage.none", "cpu.usage.average", "cpu.idle.summation",
                "cpu.utilization.average", "cpu.wait.summation",
                "mem.usage.average", "mem.active.average",
                "disk.usage.average", "virtualDisk.write.average", "virtualDisk.read.average",
                "virtualDisk.numberReadAveraged.average", "virtualDisk.numberWriteAveraged.average",
                "net.bytesRx.average", "net.bytesTx.average", "net.packetsRx.summation",
                "net.packetsTx.summation", "net.droppedRx.summation", "net.droppedTx.summation"
        );
        Map<String, List<PerMonitorDO>> metrics = RealtimePerfMonitor.getPerEntityMetricsByNames(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, counterNames);
        // Process each metric and add to batch points
        processMetrics(metrics, "CPUUsedUtilization", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "cpu.usage.none");
        processMetrics(metrics, "CPUAverageUsedUtilization", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "cpu.usage.average");
        processMetrics(metrics, "CPUIdleUtilization", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "cpu.idle.summation");
        processMetrics(metrics, "CPUSystemUtilization", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "cpu.utilization.average");
        processCPUWaitUtilization(metrics, uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints);

        // ================================================内存====================================
        processMemoryMetrics(metrics, "MemoryUsedInPercent", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "mem.usage.average", memSize);
        processMemoryUsedBytes(metrics, "MemoryUsedBytes", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "mem.active.average", memSize);
        processMemoryFreeBytes(metrics, uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "mem.active.average", memSize);
        // ==============================================磁盘========================================
        processDiskAllMetrics(metrics, "DiskAllUsedCapacityInBytes", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "disk.usage.average", storage_usage, storage_rate);
        processDiskWriteMetrics(metrics, "DiskWriteBytes", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "virtualDisk.write.average");
        processDiskReadMetrics(metrics, "DiskReadBytes", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "virtualDisk.read.average");
        processDiskIopsMetrics(metrics, "DiskReadOps", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "virtualDisk.numberReadAveraged.average");
        processDiskIopsMetrics(metrics, "DiskWriteOps", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "virtualDisk.numberWriteAveraged.average");
        // ==============================================网络===========================================
        processNetBytesRxAverageMetrics(metrics, "NetworkOutBytes", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "net.bytesTx.average");
        processNetBytesRxAverageMetrics(metrics, "NetworkInBytes", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "net.bytesRx.average");
        processNetPacketsAverageMetrics(metrics, "NetworkInPackets", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "net.packetsRx.summation");
        processNetPacketsAverageMetrics(metrics, "NetworkOutPackets", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "net.packetsTx.summation");
        processNetPacketsAverageMetrics(metrics, "NetworkInErrors", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "net.droppedRx.summation");
        processNetPacketsAverageMetrics(metrics, "NetworkOutErrors", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "net.droppedTx.summation");


        long eTime = System.currentTimeMillis(); // 结束时间
        long executionTime = eTime - startTime;
        double executionTimeInSeconds = executionTime / 1000.0;
//        System.out.println("一个虚拟机采集执行时间: " + executionTimeInSeconds + " seconds");
    }


    private void processMetrics(Map<String, List<PerMonitorDO>> metrics, String metricName, String uuid, PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BatchPoints batchPoints, String counterName) {
        List<PerMonitorDO> metricData = metrics.get(counterName);
        if (metricData != null && !metricData.isEmpty()) {
            Long allcpu = 0L;
            for (PerMonitorDO perMonitorDO : metricData) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                BigDecimal bigDecimal = new BigDecimal(0);
                if (metricName.equals("CPUUsedUtilization") || metricName.equals("CPUAverageUsedUtilization")) {
                    allcpu += perMonitorDO.getValue();
                    bigDecimal = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 100);
                } else {
                    bigDecimal = Convert.toBigDecimal(perMonitorDO.getValue());
                }
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "cpu")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", bigDecimal)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }

            if (metricName.equals("CPUUsedUtilization")) {
                BigDecimal v = NumberUtil.div(Convert.toBigDecimal(allcpu), metricData.size());
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "cpu")
                        .tag("uuid", uuid)
                        .tag("metricName", "CPUAllUsedUtilization")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", "CPUAllUsedUtilization")
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", "CPUAllUsedUtilization")
                        .addField("type", "CPUAllUsedUtilization")
                        .addField("value", Convert.toBigDecimal(NumberUtil.div(v, 100, 2)))
                        .time(metricData.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }
    }

    private void processCPUWaitUtilization(Map<String, List<PerMonitorDO>> metrics, String uuid, PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BatchPoints batchPoints) {
        List<PerMonitorDO> cpuUsedList = metrics.get("cpu.used.summation");
        List<PerMonitorDO> cpuWaitList = metrics.get("cpu.wait.summation");
        if (cpuUsedList != null && cpuWaitList != null && !cpuUsedList.isEmpty() && !cpuWaitList.isEmpty()) {
            for (int i = 0; i < cpuUsedList.size(); i++) {
                PerMonitorDO usedDO = cpuUsedList.get(i);
                PerMonitorDO waitDO = cpuWaitList.get(i);
                if (usedDO.getInstance().isEmpty()) {
                    usedDO.setInstance("0");
                }
                BigDecimal waitUsage = NumberUtil.div(waitDO.getValue(), NumberUtil.add(usedDO.getValue(), waitDO.getValue()), 5);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "cpu")
                        .tag("uuid", uuid)
                        .tag("metricName", "CPUWaitUtilization")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", waitDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", "CPUWaitUtilization")
                        .addField("type", waitDO.getInstance())
                        .addField("value", waitUsage)
                        .time(waitDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }
    }

    // ===========================================内存================================================
    private void processMemoryFreeBytes(Map<String, List<PerMonitorDO>> metrics, String uuid, PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BatchPoints batchPoints, String counterName, BigDecimal memSize) {
        List<PerMonitorDO> metricData = metrics.get(counterName);
        if (metricData != null && !metricData.isEmpty()) {
            for (PerMonitorDO ac : metricData) {
                BigDecimal sd = NumberUtil.sub(NumberUtil.mul(memSize, 1024), Convert.toBigDecimal(ac.getValue()));
                String label = ac.getInstance();
                String type = label + "MemoryFreeBytes";
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "mem")
                        .tag("uuid", uuid)
                        .tag("metricName", "MemoryFreeBytes")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", type)
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", "MemoryFreeBytes")
                        .addField("type", "MemoryFreeBytes")
                        .addField("value", Convert.toBigDecimal(NumberUtil.mul(sd, 1024)))
                        .time(ac.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }
    }

    private void processMemoryUsedBytes(Map<String, List<PerMonitorDO>> metrics, String metricName, String uuid, PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BatchPoints batchPoints, String counterName, BigDecimal memSize) {
        List<PerMonitorDO> metricData = metrics.get(counterName);
        if (metricData != null && !metricData.isEmpty()) {
            for (PerMonitorDO perMonitorDO : metricData) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }

                String label = perMonitorDO.getInstance();
                String type = label + "MemoryUsedBytes";

                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "mem")
                        .tag("uuid", uuid)
                        .tag("metricName", "MemoryUsedBytes")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", type)
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", "MemoryUsedBytes")
                        .addField("type", "MemoryUsedBytes")
                        .addField("value", Convert.toBigDecimal(NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 1024)))
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }
    }


    private void processMemoryMetrics(Map<String, List<PerMonitorDO>> metrics, String metricName, String uuid, PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BatchPoints batchPoints, String counterName, BigDecimal memSize) {
        List<PerMonitorDO> metricData = metrics.get(counterName);
        if (metricData != null && !metricData.isEmpty()) {
            for (PerMonitorDO perMonitorDO : metricData) {
                BigDecimal value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 100);
                String label = perMonitorDO.getInstance();
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "mem")
                        .tag("uuid", uuid)
                        .tag("metricName", "MemoryUsedInPercent")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", "MemoryUsedInPercent")
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", "MemoryUsedInPercent")
                        .addField("type", "MemoryUsedInPercent")
                        .addField("value", value)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }
    }

    // ===========================================磁盘================================================
    private void processDiskIopsMetrics(Map<String, List<PerMonitorDO>> metrics, String metricName, String uuid, PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BatchPoints batchPoints, String counterName) {
        List<PerMonitorDO> metricData = metrics.get(counterName);
        if (metricData != null && !metricData.isEmpty()) {
            for (PerMonitorDO perMonitorDO : metricData) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "disk")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", Convert.toBigDecimal(perMonitorDO.getValue()))
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }
    }

    private void processDiskReadMetrics(Map<String, List<PerMonitorDO>> metrics, String metricName, String uuid, PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BatchPoints batchPoints, String counterName) {
        List<PerMonitorDO> metricData = metrics.get(counterName);
        BigDecimal disk_read_all = new BigDecimal(0);
        if (metricData != null && !metricData.isEmpty()) {
            for (PerMonitorDO perMonitorDO : metricData) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                BigDecimal value = new BigDecimal(0);
                if (perMonitorDO.getValue() > 0) {
                    value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                }
                BigDecimal lastValue = NumberUtil.mul(value, 1024);
                disk_read_all = disk_read_all.add(lastValue);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "disk")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", Convert.toBigDecimal(perMonitorDO.getValue()))
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);

            }

            Point point = Point.measurement("zj_cloud_host")
                    .tag("label", "disk")
                    .tag("uuid", uuid)
                    .tag("metricName", "DiskAllReadBytes")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("vm_type", "DiskAllReadBytes")
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("vm_metricName", "DiskAllReadBytes")
                    .addField("type", "DiskAllReadBytes")
                    .addField("value", disk_read_all)
                    .time(metricData.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }
    }

    private void processDiskWriteMetrics(Map<String, List<PerMonitorDO>> metrics, String metricName, String uuid, PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BatchPoints batchPoints, String counterName) {
        List<PerMonitorDO> metricData = metrics.get(counterName);
        BigDecimal disk_write_all = new BigDecimal(0);
        if (metricData != null && !metricData.isEmpty()) {
            for (PerMonitorDO perMonitorDO : metricData) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                BigDecimal value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.mul(value, 1024);

                disk_write_all = disk_write_all.add(lastValue);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "disk")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
            Point point = Point.measurement("zj_cloud_host")
                    .tag("label", "disk")
                    .tag("uuid", uuid)
                    .tag("metricName", "DiskAllWriteBytes")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("vm_type", "DiskAllWriteBytes")
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("vm_metricName", "DiskAllWriteBytes")
                    .addField("type", "DiskAllWriteBytes")
                    .addField("value", disk_write_all)
                    .time(metricData.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);

        }
    }

    private void processDiskAllMetrics(Map<String, List<PerMonitorDO>> metrics, String metricName, String uuid, PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BatchPoints batchPoints, String counterName, BigDecimal storage_usage, BigDecimal storage_rate) {
        List<PerMonitorDO> metricData = metrics.get(counterName);
        if (metricData != null && !metricData.isEmpty()) {
            for (PerMonitorDO perMonitorDO : metricData) {
                String label = perMonitorDO.getInstance();
                BigDecimal st = NumberUtil.div(Convert.toBigDecimal(storage_usage), 8);
                BigDecimal value = NumberUtil.mul(st, 1024);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "disk")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", metricName)
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", metricName)
                        .addField("value", value)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);

                Point inPercent = Point.measurement("zj_cloud_host")
                        .tag("label", "disk")
                        .tag("uuid", uuid)
                        .tag("metricName", "DiskAllUsedCapacityInPercent")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", "DiskAllUsedCapacityInBytes")
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", "DiskAllUsedCapacityInPercent")
                        .addField("type", "DiskAllUsedCapacityInPercent")
                        .addField("value", NumberUtil.mul(storage_rate, 100))
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(inPercent);
            }
        }
    }


    // ===========================================网络================================================


    private void processNetPacketsAverageMetrics(Map<String, List<PerMonitorDO>> metrics, String metricName, String uuid, PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BatchPoints batchPoints, String counterName) {
        List<PerMonitorDO> metricData = metrics.get(counterName);
        if (metricData != null && !metricData.isEmpty()) {
            BigDecimal net_tx_all = new BigDecimal(0);
            for (PerMonitorDO perMonitorDO : metricData) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.div(value, 1024);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "net")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }
    }


    private void processNetBytesRxAverageMetrics(Map<String, List<PerMonitorDO>> metrics, String metricName, String uuid, PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BatchPoints batchPoints, String counterName) {
        List<PerMonitorDO> metricData = metrics.get(counterName);
        if (metricData != null && !metricData.isEmpty()) {
            BigDecimal net_tx_all = new BigDecimal(0);
            for (PerMonitorDO perMonitorDO : metricData) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }

                BigDecimal value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.mul(value, 1024);
                net_tx_all = net_tx_all.add(lastValue);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "net")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
            if (metricName.equals("NetworkOutBytes")) {
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "net")
                        .tag("uuid", uuid)
                        .tag("metricName", "NetworkAllOutBytes")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", "NetworkAllOutBytes")
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", "NetworkAllOutBytes")
                        .addField("type", "NetworkAllOutBytes")
                        .addField("value", NumberUtil.mul(NumberUtil.mul(net_tx_all, 8), 1024))
                        .time(metricData.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            } else {
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "net")
                        .tag("uuid", uuid)
                        .tag("metricName", "NetworkAllInBytes")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", "NetworkAllOutBytes")
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", "NetworkAllInBytes")
                        .addField("type", "NetworkAllInBytes")
                        .addField("value", NumberUtil.mul(NumberUtil.mul(net_tx_all, 8), 1024))
                        .time(metricData.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }

        }
    }


    //    private long test(){
    //        if (!filteredList.isEmpty()) {
//            for (PlatformconfigDTO platformconfigDTO : filteredList) {
//                List<HostInfoRespCreateReqDTO> hostInfoRespCreateReqDTOList = new ArrayList<>();
//                // 存储平台日期数据
//                Map<String, Date> createDateInfo = new HashMap<>();
//                List<HostInfoRespCreateReqDTO> hostInfoApiVmByPlatformId1 = hostInfoApi.getVmByPlatformId(platformconfigDTO.getId());
//                // 如果查询到有数据说明数据库已存在
//                if (!hostInfoApiVmByPlatformId1.isEmpty()) {
//                    for (HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO : hostInfoApiVmByPlatformId1) {
//                        createDateInfo.put(hostInfoRespCreateReqDTO.getUuid(), hostInfoRespCreateReqDTO.getVCreateDate());
//                    }
//                }
//
//                ServiceInstance serviceInstance = SampleUtil.createServiceInstance(platformconfigDTO.getUrl(), platformconfigDTO.getUsername(), platformconfigDTO.getPassword());
//                VmComputerResourceSummary vmComputerResourceSummary = new VmComputerResourceSummary();
//                List<VirtualMachine> virtualMachines = VmComputerResourceSummary.getVmList(serviceInstance);
//                // 获取虚拟机信息
//                for (VirtualMachine virtualMachine : virtualMachines) {
//                    long stime = System.currentTimeMillis(); // 开始时间
//                    VirtualMachineSummary virtualMachineSummary = virtualMachine.getSummary();
//                    Datastore[] d = virtualMachine.getDatastores();
//
//                    String uuid = virtualMachineSummary.getConfig().getUuid();
//                    String name = virtualMachine.getName();
//                    String powerState = virtualMachineSummary.getRuntime().getPowerState().name();
//                    VirtualMachineConfigInfoDatastoreUrlPair[] virtualMachineConfigInfoDatastoreUrlPair = virtualMachine.getConfig().getDatastoreUrl();
//                    String virtualName = virtualMachineConfigInfoDatastoreUrlPair[0].getName();
//
//                    String state = "";
//                    switch (powerState) {
//                        case "poweredOff":
//                            state = "Stopped";
//                            break;
//                        case "poweredOn":
//                            state = "Running";
//                            break;
//                        case "suspended":
//                            state = "Suspended";
//                            break;
//                        default:
//                            state = "Unknown"; // 可选：处理其他未知的电源状态
//                            break;
//                    }
//                    String ip = virtualMachine.getGuest().getIpAddress();
//                    String vip_ip = "";
//                    String clusterId = virtualMachine.getParent().getMOR().getVal();
//                    String clusterName = virtualMachine.getParent().getName();
//                    String architecture = "x86_64";
//                    String guestOsType = virtualMachineSummary.getConfig().getGuestFullName();
//                    String type = "UserVm";
//                    BigDecimal memtotal = NumberUtil.mul(Convert.toBigDecimal(virtualMachineSummary.getConfig().getMemorySizeMB()), 1024, 1024);
//                    BigDecimal memusage = NumberUtil.mul(Convert.toBigDecimal(virtualMachineSummary.getQuickStats().getGuestMemoryUsage()), 1024, 1024);
//                    BigDecimal memoryUsed = NumberUtil.mul(NumberUtil.div(memusage, memtotal, 2), 100);
//                    long storageUsage = virtualMachineSummary.getStorage().getCommitted();
//                    ManagedEntity managedEntities = new InventoryNavigator(serviceInstance.getRootFolder())
//                            .searchManagedEntity("VirtualMachine", virtualMachine.getName());
//                    List<PerMonitorDO> doList = RealtimePerfMonitor.getPerEntityMericBasesByname(virtualMachine.getName(), serviceInstance, managedEntities, "cpu.usage.average");
//                    // cpu
//                    long totalValue = 0;
//                    if (!doList.isEmpty()) {
//                        for (PerMonitorDO perMonitorDO : doList) {
//                            totalValue += perMonitorDO.getValue();
//                        }
//                    }
//                    double cpuUsage = 0;
//                    if (totalValue != 0) {
//                        cpuUsage = NumberUtil.div(totalValue / doList.size() * 3, 100);
//                    }
//                    VirtualDevice[] devices = virtualMachine.getConfig().getHardware().getDevice();
//
//                    boolean firstDiskFound = false; // 标志变量，跟踪第一个 VirtualDisk 是否已找到
//                    // storageTotal取devices中的第一条数据
//                    Long storageTotal = 0L;
//                    // 除了第一条中的数据
//                    Long virtualTotal = 0l;
//                    if (devices != null && devices.length > 0) {
//                        for (VirtualDevice virtualDevice : devices) {
//                            if (virtualDevice instanceof VirtualDisk) {
//                                VirtualDisk disk = (VirtualDisk) virtualDevice;
//                                if (!firstDiskFound) {
//                                    firstDiskFound = true; // 第一个 VirtualDisk 找到了，设置标志为 true
//                                    storageTotal = disk.getCapacityInBytes();
//                                    continue; // 跳过第一个 VirtualDisk
//                                }
//                                virtualTotal += disk.getCapacityInBytes();
//                            }
//                        }
//                    }
//
//                    BigDecimal storageAll = NumberUtil.add(storageTotal, virtualTotal);
//
//                    // diskFreeBytes 取VmComputerResourceSummary.getVmDiskFileList 中的第一块盘中的数据
//                    BigDecimal diskFreeBytes = new BigDecimal(0);
//                    // 取出除了第一条中的数据
//                    BigDecimal actualSize = new BigDecimal(0);
//                    // 获取磁盘中vmdk的文件数据
//                    List<VmDiskFileInfo> list = VmComputerResourceSummary.getVmDiskFileList(serviceInstance, name, virtualName, d);
//                    if (list.size() > 0) {
//                        diskFreeBytes = Convert.toBigDecimal(list.get(0).getFileSize());
//                        for (int i = 1; i < list.size(); i++) { // 从索引 1 开始，跳过第一个元素
//                            VmDiskFileInfo diskFileInfo = list.get(i);
//                            BigDecimal dsk = Convert.toBigDecimal(diskFileInfo.getFileSize());
//                            actualSize = actualSize.add(dsk);
//
//                        }
//                    }
//
//                    BigDecimal diskUsed = NumberUtil.mul(NumberUtil.div(Convert.toBigDecimal(storageUsage), Convert.toBigDecimal(storageAll), 2), 100);
//                    Integer numCpu = virtualMachineSummary.getConfig().getNumCpu();
//                    GuestNicInfo[] guestNicInfos = virtualMachine.getGuest().getNet();
//                    String mac = "";
//                    if (guestNicInfos != null) {
//                        for (GuestNicInfo guestNicInfo : guestNicInfos) {
//                            String[] ips = guestNicInfo.getIpAddress();
//                            List<String> colList = CollUtil.newArrayList(ips);
//                            mac = CollUtil.join(colList, ",");
//                        }
//                    }
//
//
//                    String typeName = "vmware";
//                    String vms = virtualMachine.getMOR().getVal();
//
//                    // 带宽上行
//                    List<PerMonitorDO> networkRxList = RealtimePerfMonitor.getPerEntityMericBasesByname(virtualMachine.getName(), serviceInstance, managedEntities, "net.bytesRx.average");
//                    long networkRx = 0;
//                    if (!networkRxList.isEmpty()) {
//                        for (PerMonitorDO perMonitorDO : networkRxList) {
//                            networkRx += perMonitorDO.getValue();
//                        }
//                    }
//                    // 带宽下行
//                    List<PerMonitorDO> networkTxList = RealtimePerfMonitor.getPerEntityMericBasesByname(virtualMachine.getName(), serviceInstance, managedEntities, "net.bytesTx.average");
//                    long networkTx = 0;
//                    if (!networkTxList.isEmpty()) {
//                        for (PerMonitorDO perMonitorDO : networkTxList) {
//                            networkTx += perMonitorDO.getValue();
//                        }
//                    }
//
//                    List<PerMonitorDO> netpacRxList = RealtimePerfMonitor.getPerEntityMericBasesByname(virtualMachine.getName(), serviceInstance, managedEntities, "net.packetsRx.summation");
//                    long netpacRx = 0;
//                    if (!netpacRxList.isEmpty()) {
//                        for (PerMonitorDO perMonitorDO : netpacRxList) {
//                            netpacRx += perMonitorDO.getValue();
//                        }
//                    }
//
//                    List<PerMonitorDO> netpacTxList = RealtimePerfMonitor.getPerEntityMericBasesByname(virtualMachine.getName(), serviceInstance, managedEntities, "net.packetsTx.summation");
//                    long netpacTx = 0;
//                    if (!netpacTxList.isEmpty()) {
//                        for (PerMonitorDO perMonitorDO : netpacTxList) {
//                            netpacTx += perMonitorDO.getValue();
//                        }
//                    }
//
//                    Date createDate = new Date();
//                    if (!createDateInfo.containsKey(uuid)) {
//                        // 如果时间已经采集存在过了则不需要再次采集
//                        Calendar startTime = DateUtil.calendar(DateUtil.parse("1990-01-01 00:00:00"));
//                        Calendar endTime = DateUtil.calendar(new Date());
//                        PerMonitorDO perMonitorDO = new PerMonitorDO();
//                        try {
//                            perMonitorDO = RealtimePerfMonitor.getPerEntityMericBasesCreaateByname(virtualMachine.getName(), serviceInstance, managedEntities, "cpu.usage.average", startTime, endTime);
//                        } catch (Exception e) {
//                            log.info("获取性能数据中日期异常RealtimePerfMonitor：" + e.getMessage());
//                        }
//                        if (perMonitorDO != null) {
//                            createDate = perMonitorDO.getDateTime();
//                        }
//
//                    } else {
//                        createDate = createDateInfo.get(uuid);
//                    }
//
//                    HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO = new HostInfoRespCreateReqDTO();
//                    hostInfoRespCreateReqDTO.setUuid(uuid);
//                    hostInfoRespCreateReqDTO.setName(name);
//                    hostInfoRespCreateReqDTO.setState(state);
//                    hostInfoRespCreateReqDTO.setIp(ip);
//                    hostInfoRespCreateReqDTO.setVipIp(vip_ip);
//                    hostInfoRespCreateReqDTO.setClusterUuid(clusterId);
//                    hostInfoRespCreateReqDTO.setClusterName(clusterName);
//                    hostInfoRespCreateReqDTO.setArchitecture(architecture);
//                    hostInfoRespCreateReqDTO.setGuestOsType(guestOsType);
//                    hostInfoRespCreateReqDTO.setType(type);
//                    hostInfoRespCreateReqDTO.setMemoryUsed(memoryUsed);
//                    hostInfoRespCreateReqDTO.setMemorySize(Convert.toLong(memtotal));
//                    hostInfoRespCreateReqDTO.setCpuUsed(Convert.toBigDecimal(cpuUsage));
//                    hostInfoRespCreateReqDTO.setDiskUsed(diskUsed);
//                    hostInfoRespCreateReqDTO.setCpuNum(numCpu);
//                    hostInfoRespCreateReqDTO.setMac(mac);
//                    hostInfoRespCreateReqDTO.setDiskFreeBytes(diskFreeBytes);
//                    hostInfoRespCreateReqDTO.setDiskUsedBytes(Convert.toBigDecimal(storageUsage));
//                    hostInfoRespCreateReqDTO.setTotalDiskCapacity(Convert.toBigDecimal(storageTotal));
//                    hostInfoRespCreateReqDTO.setCloudSize(Convert.toBigDecimal(virtualTotal));
//                    hostInfoRespCreateReqDTO.setTypeName(typeName);
//                    hostInfoRespCreateReqDTO.setVms(vms);
//                    hostInfoRespCreateReqDTO.setPlatformId(platformconfigDTO.getId());
//                    hostInfoRespCreateReqDTO.setPlatformName(platformconfigDTO.getName());
//                    hostInfoRespCreateReqDTO.setRegionId(platformconfigDTO.getRegionId());
//                    hostInfoRespCreateReqDTO.setNetworkInBytes(Convert.toBigDecimal(networkRx));
//                    hostInfoRespCreateReqDTO.setNetworkOutBytes(Convert.toBigDecimal(networkTx));
//                    hostInfoRespCreateReqDTO.setNetworkInPackets(Convert.toBigDecimal(netpacRx));
//                    hostInfoRespCreateReqDTO.setNetworkOutPackets(Convert.toBigDecimal(netpacTx));
//                    hostInfoRespCreateReqDTO.setVCreateDate(createDate);
//                    hostInfoRespCreateReqDTO.setActualSize(actualSize);
//                    hostInfoRespCreateReqDTO.setDeleted(0);
//                    hostInfoRespCreateReqDTOList.add(hostInfoRespCreateReqDTO);
//                    long eTime = System.currentTimeMillis(); // 结束时间
//                    long executionTime = eTime - stime;
//                    double executionTimeInSeconds = executionTime / 1000.0;
//                    System.out.println("virtualMachine代码执行时间: " + executionTimeInSeconds + " seconds");
//
//                }
//
//                //关闭连接
//                serviceInstance.getServerConnection().logout();
//                if (!hostInfoRespCreateReqDTOList.isEmpty()) {
//                    List<HostInfoRespCreateReqDTO> list = hostInfoApi.getVmByPlatformId(platformconfigDTO.getId());
//                    if (list.isEmpty()) {
//                        hostInfoApi.adds(hostInfoRespCreateReqDTOList);
//                        vmInfo.put(platformconfigDTO.getId(), hostInfoRespCreateReqDTOList);
//                    } else {
//                        List<HostInfoRespCreateReqDTO> newEntries = new ArrayList<>();
//                        List<HostInfoRespCreateReqDTO> updateEntries = new ArrayList<>();
//
//                        Map<String, HostInfoRespCreateReqDTO> existingHostInfo = list.stream().collect(Collectors.toMap(HostInfoRespCreateReqDTO::getUuid, hostInfo -> hostInfo));
//                        // 遍历云主机数据
//                        for (HostInfoRespCreateReqDTO newHostInfo : hostInfoRespCreateReqDTOList) {
//                            HostInfoRespCreateReqDTO existingHost = existingHostInfo.get(newHostInfo.getUuid());
//                            if (existingHost == null) {
//                                newEntries.add(newHostInfo);
//                            } else {
//                                if (!existingHostInfo.equals(newHostInfo)) {
//                                    updateEntries.add(newHostInfo);
//                                }
//                            }
//                        }
//                        hostInfoApi.updates(updateEntries);
//                        hostInfoApi.adds(newEntries);
//                        vmInfo.remove(platformconfigDTO.getId());
//                        List<HostInfoRespCreateReqDTO> cache = hostInfoApi.getVmByPlatformId(platformconfigDTO.getId());
//                        vmInfo.put(platformconfigDTO.getId(), cache);
//                    }
//                }
//            }
//        }
//    }

//    private void test(){
    //        InventoryNavigator navigator = new InventoryNavigator(serviceInstance.getRootFolder());
//        Map<String, ManagedEntity> vmEntities = new HashMap<>();
//
//        // 批量获取所有虚拟机的实体
//        for (HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO : hostInfoApiVmByPlatformId1) {
//            ManagedEntity managedEntity = navigator.searchManagedEntity("VirtualMachine", hostInfoRespCreateReqDTO.getName());
//            if (managedEntity != null) {
//                vmEntities.put(hostInfoRespCreateReqDTO.getName(), managedEntity);
//            }
//        }
//
//
//        for (HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO : hostInfoApiVmByPlatformId1) {
//            ManagedEntity managedEntity = vmEntities.get(hostInfoRespCreateReqDTO.getName());
//
//            long startTime = System.currentTimeMillis(); // 开始时间
//
////            ManagedEntity managedEntities = new InventoryNavigator(serviceInstance.getRootFolder())
////                    .searchManagedEntity("VirtualMachine", hostInfoRespCreateReqDTO.getName());
//
//            VirtualMachine virtualMachine = (VirtualMachine) managedEntity;
//            Datastore[] datastores = virtualMachine.getDatastores();
//            Long storage_total = 0L;
//            Long storage_total_free = 0L;
//            for (Datastore datastore : datastores) {
//                storage_total += datastore.getSummary().getCapacity();
//                storage_total_free += datastore.getSummary().getFreeSpace();
//            }
//            BigDecimal memSize = Convert.toBigDecimal(virtualMachine.getSummary().getConfig().getMemorySizeMB());
//            BigDecimal storage_usage = NumberUtil.sub(storage_total, storage_total_free);
//            BigDecimal storage_rate = NumberUtil.div(storage_total_free, storage_total);
//            String uuid = hostInfoRespCreateReqDTO.getUuid();
//            // ==========================================CPU==================================================
//            collectCpuMetrics(serviceInstance, hostInfoRespCreateReqDTO, virtualMachine, uuid, batchPoints, platformconfigDTO);


//            // cpu.usage.none CPUUsedUtilization
//            List<PerMonitorDO> CPUUsedUtilization = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.usage.none");
//            if (!CPUUsedUtilization.isEmpty()) {
//                for (PerMonitorDO perMonitorDO : CPUUsedUtilization) {
//
//                    if (perMonitorDO.getInstance().isEmpty()) {
//                        perMonitorDO.setInstance("0");
//                    }
//                    Point point = Point.measurement("zj_cloud_host")
//                            .tag("label", "cpu")
//                            .tag("uuid", uuid)
//                            .tag("metricName", "CPUUsedUtilization")
//                            .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                            .tag("vm_type", perMonitorDO.getInstance())
//                            .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                            .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                            .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                            .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                            .addField("vm_metricName", "CPUUsedUtilization")
//                            .addField("type", perMonitorDO.getInstance())
//                            .addField("value", NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 100))
//                            .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                            .build();
//                    batchPoints.point(point);
//                }
//
//            }
//
//
//            // cpu.usage.average CPUAverageUsedUtilization
//            List<PerMonitorDO> CPUAverageUsedUtilization = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.usage.average");
//            if (!CPUAverageUsedUtilization.isEmpty()) {
//                for (PerMonitorDO perMonitorDO : CPUAverageUsedUtilization) {
//                    if (perMonitorDO.getInstance().isEmpty()) {
//                        perMonitorDO.setInstance("0");
//                    }
//                    Point point = Point.measurement("zj_cloud_host")
//                            .tag("label", "cpu")
//                            .tag("uuid", uuid)
//                            .tag("metricName", "CPUAverageUsedUtilization")
//                            .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                            .tag("vm_type", perMonitorDO.getInstance())
//                            .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                            .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                            .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                            .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                            .addField("vm_metricName", "CPUAverageUsedUtilization")
//                            .addField("type", perMonitorDO.getInstance())
//                            .addField("value", Convert.toBigDecimal(perMonitorDO.getValue()))
//                            .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                            .build();
//                    batchPoints.point(point);
//                }
//            }
//
//
//            // cpu.idle.summation CPUIdleUtilization
//            List<PerMonitorDO> CPUIdleUtilization = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.idle.summation");
//            if (!CPUIdleUtilization.isEmpty()) {
//                for (PerMonitorDO perMonitorDO : CPUIdleUtilization) {
//                    if (perMonitorDO.getInstance().isEmpty()) {
//                        perMonitorDO.setInstance("0");
//                    }
//                    Point point = Point.measurement("zj_cloud_host")
//                            .tag("label", "cpu")
//                            .tag("uuid", uuid)
//                            .tag("metricName", "CPUIdleUtilization")
//                            .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                            .tag("vm_type", perMonitorDO.getInstance())
//                            .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                            .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                            .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                            .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                            .addField("vm_metricName", "CPUIdleUtilization")
//                            .addField("type", perMonitorDO.getInstance())
//                            .addField("value", Convert.toBigDecimal(perMonitorDO.getValue()))
//                            .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                            .build();
//                    batchPoints.point(point);
//                }
//            }
//
//            // cpu.usage.none CPUAllUsedUtilization
//            List<PerMonitorDO> CPUAllUsedUtilization = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.usage.none");
//            if (CPUAllUsedUtilization.size() > 0) {
//                for (PerMonitorDO perMonitorDO : CPUAllUsedUtilization) {
//                    if (perMonitorDO.getInstance().isEmpty()) {
//                        perMonitorDO.setInstance("0");
//                    }
//                    Point point = Point.measurement("zj_cloud_host")
//                            .tag("label", "cpu")
//                            .tag("uuid", uuid)
//                            .tag("metricName", "CPUAllUsedUtilization")
//                            .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                            .tag("vm_type", perMonitorDO.getInstance())
//                            .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                            .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                            .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                            .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                            .addField("vm_metricName", "CPUAllUsedUtilization")
//                            .addField("type", perMonitorDO.getInstance())
//                            .addField("value", Convert.toBigDecimal(perMonitorDO.getValue()))
//                            .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                            .build();
//                    batchPoints.point(point);
//                }
//            }
//
//
//            // cpu.utilization.average CPUSystemUtilization
//            List<PerMonitorDO> CPUSystemUtilization = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.utilization.average");
//            if (CPUSystemUtilization.size() > 0) {
//                for (PerMonitorDO perMonitorDO : CPUSystemUtilization) {
//                    if (perMonitorDO.getInstance().isEmpty()) {
//                        perMonitorDO.setInstance("0");
//                    }
//                    Point point = Point.measurement("zj_cloud_host")
//                            .tag("label", "cpu")
//                            .tag("uuid", uuid)
//                            .tag("metricName", "CPUSystemUtilization")
//                            .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                            .tag("vm_type", perMonitorDO.getInstance())
//                            .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                            .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                            .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                            .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                            .addField("vm_metricName", "CPUSystemUtilization")
//                            .addField("type", perMonitorDO.getInstance())
//                            .addField("value", Convert.toBigDecimal(perMonitorDO.getValue()))
//                            .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                            .build();
//                    batchPoints.point(point);
//                }
//            }
//            // cpu.used.summation
//            List<PerMonitorDO> cpuUsedList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.used.summation");
//            Long valueAll = 0L;
//            if (cpuUsedList.size() > 0) {
//                for (PerMonitorDO perMonitorDO : cpuUsedList) {
//                    if (perMonitorDO.getInstance().isEmpty()) {
//                        valueAll += valueAll + perMonitorDO.getValue();
//                    }
//                }
//            }
//
//            // cpu.wait.summation
//            List<PerMonitorDO> cpuWaitList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.wait.summation");
//            if (!cpuWaitList.isEmpty()) {
//                for (PerMonitorDO perMonitorDO : cpuWaitList) {
//                    if (perMonitorDO.getInstance().isEmpty()) {
//                        perMonitorDO.setInstance("0");
//                    }
//                    BigDecimal value = NumberUtil.div(perMonitorDO.getValue(), valueAll, 2);
//                    Point point = Point.measurement("zj_cloud_host")
//                            .tag("label", "cpu")
//                            .tag("uuid", uuid)
//                            .tag("metricName", "CPUWaitUtilization")
//                            .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                            .tag("vm_type", perMonitorDO.getInstance())
//                            .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                            .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                            .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                            .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                            .addField("vm_metricName", "CPUWaitUtilization")
//                            .addField("type", perMonitorDO.getInstance())
//                            .addField("value", value)
//                            .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                            .build();
//                    batchPoints.point(point);
//                }
//            }

    // ==========================================内存==================================================
//            List<PerMonitorDO> memUsageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "mem.usage.average");
//            for (PerMonitorDO perMonitorDO : memUsageList) {
//                BigDecimal value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 100);
//                String label = perMonitorDO.getInstance();
//                String type = label + "MemoryUsedBytes";
//                Point point = Point.measurement("zj_cloud_host")
//                        .tag("label", "mem")
//                        .tag("uuid", uuid)
//                        .tag("metricName", "MemoryUsedInPercent")
//                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                        .tag("vm_type", type)
//                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                        .addField("vm_metricName", "MemoryUsedInPercent")
//                        .addField("type", "MemoryUsedInPercent")
//                        .addField("value", value)
//                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                        .build();
//                batchPoints.point(point);
//            }
//
//
//            List<PerMonitorDO> memActiveList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "mem.active.average");
//            for (PerMonitorDO perMonitorDO : memActiveList) {
//                String label = perMonitorDO.getInstance();
//                String type = label + "MemoryUsedBytes";
//                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 1024, 2);
//                Point point = Point.measurement("zj_cloud_host")
//                        .tag("label", "mem")
//                        .tag("uuid", uuid)
//                        .tag("metricName", "MemoryUsedBytes")
//                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                        .tag("vm_type", type)
//                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                        .addField("vm_metricName", "MemoryUsedBytes")
//                        .addField("type", "MemoryUsedBytes")
//                        .addField("value", value)
//                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                        .build();
//                batchPoints.point(point);
//            }
//
//            for (PerMonitorDO ac : memActiveList) {
//                BigDecimal acvalue = NumberUtil.div(Convert.toBigDecimal(ac.getValue()), 1024);
//                BigDecimal sd = NumberUtil.sub(memSize, acvalue);
//
//                String label = ac.getInstance();
//                String type = label + "MemoryUsedBytes";
//                Point point = Point.measurement("zj_cloud_host")
//                        .tag("label", "mem")
//                        .tag("uuid", uuid)
//                        .tag("metricName", "MemoryFreeBytes")
//                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                        .tag("vm_type", type)
//                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                        .addField("vm_metricName", "MemoryFreeBytes")
//                        .addField("type", "MemoryFreeBytes")
//                        .addField("value", NumberUtil.mul(sd,1024) )
//                        .time(ac.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                        .build();
//                batchPoints.point(point);
//            }
//
//             ==========================================硬盘==================================================
//            List<PerMonitorDO> diskUsageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "disk.usage.average");
//            for (PerMonitorDO perMonitorDO : diskUsageList) {
//                if (perMonitorDO.getInstance().isEmpty()) {
//                    perMonitorDO.setInstance("0");
//                }
//
//
//                BigDecimal st = NumberUtil.div(Convert.toBigDecimal(storage_usage), 8);
//                BigDecimal value = NumberUtil.mul(st, 1024);
//                Point statDiskUsage = Point.measurement("zj_cloud_host")
//                        .tag("label", "disk")
//                        .tag("uuid", uuid)
//                        .tag("metricName", "DiskAllUsedCapacityInBytes")
//                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                        .tag("vm_type", "DiskAllUsedCapacityInBytes")
//                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                        .addField("vm_metricName", "DiskAllUsedCapacityInBytes")
//                        .addField("type", "DiskAllUsedCapacityInBytes")
//                        .addField("value", value)
//                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                        .build();
//                batchPoints.point(statDiskUsage);
//
//                Point inPercent = Point.measurement("zj_cloud_host")
//                        .tag("label", "disk")
//                        .tag("uuid", uuid)
//                        .tag("metricName", "DiskAllUsedCapacityInPercent")
//                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                        .tag("vm_type", "DiskAllUsedCapacityInBytes")
//                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                        .addField("vm_metricName", "DiskAllUsedCapacityInPercent")
//                        .addField("type", "DiskAllUsedCapacityInPercent")
//                        .addField("value", NumberUtil.mul(storage_rate, 100))
//                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                        .build();
//                batchPoints.point(inPercent);
//            }


//            List<PerMonitorDO> diskWriteList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "disk.write.average");
//            for (PerMonitorDO perMonitorDO : diskWriteList) {
//                if (perMonitorDO.getInstance().isEmpty()) {
//                    perMonitorDO.setInstance("0");
//                }
//                String metricName = "DiskReadBytes";
//                BigDecimal value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
//                BigDecimal lastValue = NumberUtil.mul(value, 1024);
//                Point point = Point.measurement("zj_cloud_host")
//                        .tag("label", "disk")
//                        .tag("uuid", uuid)
//                        .tag("metricName", metricName)
//                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                        .tag("vm_type", perMonitorDO.getInstance())
//                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                        .addField("vm_metricName", metricName)
//                        .addField("type", perMonitorDO.getInstance())
//                        .addField("value", lastValue)
//                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                        .build();
//                batchPoints.point(point);
//            }
//
//        List<PerMonitorDO> diskReadList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "disk.read.average");
//        if (!diskReadList.isEmpty()) {
//            BigDecimal disk_write_all = new BigDecimal(0);
//            for (PerMonitorDO perMonitorDO : diskReadList) {
//                if (perMonitorDO.getInstance().isEmpty()) {
//                    perMonitorDO.setInstance("0");
//                }
//                String metricName = "DiskWriteBytes";
//                BigDecimal value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
//                BigDecimal lastValue = NumberUtil.mul(value, 1024);
//                disk_write_all = disk_write_all.add(lastValue);
//                Point point = Point.measurement("zj_cloud_host")
//                        .tag("label", "disk")
//                        .tag("uuid", uuid)
//                        .tag("metricName", metricName)
//                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                        .tag("vm_type", perMonitorDO.getInstance())
//                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                        .addField("vm_metricName", metricName)
//                        .addField("type", perMonitorDO.getInstance())
//                        .addField("value", lastValue)
//                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                        .build();
//                batchPoints.point(point);
//            }
//            Point point = Point.measurement("zj_cloud_host")
//                    .tag("label", "disk")
//                    .tag("uuid", uuid)
//                    .tag("metricName", "DiskAllWriteBytes")
//                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                    .tag("vm_type", "DiskAllWriteBytes")
//                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                    .addField("vm_metricName", "DiskAllWriteBytes")
//                    .addField("type", "DiskAllWriteBytes")
//                    .addField("value", disk_write_all)
//                    .time(diskReadList.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                    .build();
//            batchPoints.point(point);
//        }

//
//        List<PerMonitorDO> iopsReadList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "disk.numberRead.summation");
//        for (PerMonitorDO perMonitorDO : iopsReadList) {
//            if (perMonitorDO.getInstance().isEmpty()) {
//                perMonitorDO.setInstance("0");
//            }
//            String metricName = "DiskReadOps";
//            Point point = Point.measurement("zj_cloud_host")
//                    .tag("label", "disk")
//                    .tag("uuid", uuid)
//                    .tag("metricName", metricName)
//                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                    .tag("vm_type", perMonitorDO.getInstance())
//                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                    .addField("vm_metricName", metricName)
//                    .addField("type", perMonitorDO.getInstance())
//                    .addField("value", Convert.toBigDecimal(perMonitorDO.getValue()))
//                    .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                    .build();
//            batchPoints.point(point);
//        }
//
//        List<PerMonitorDO> iopsWriteList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "disk.numberWrite.summation");
//        if (!iopsWriteList.isEmpty()) {
//            for (PerMonitorDO perMonitorDO : iopsWriteList) {
//                if (perMonitorDO.getInstance().isEmpty()) {
//                    perMonitorDO.setInstance("0");
//                }
//                String metricName = "DiskWriteOps";
//                Point point = Point.measurement("zj_cloud_host")
//                        .tag("label", "disk")
//                        .tag("uuid", uuid)
//                        .tag("metricName", metricName)
//                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                        .tag("vm_type", perMonitorDO.getInstance())
//                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                        .addField("vm_metricName", metricName)
//                        .addField("type", perMonitorDO.getInstance())
//                        .addField("value", Convert.toBigDecimal(perMonitorDO.getValue()))
//                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                        .build();
//                batchPoints.point(point);
//            }
//        }
//
//            // ==========================================网络==================================================
//        List<PerMonitorDO> netBytesRxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.bytesRx.average");
//        if (!netBytesRxAverageList.isEmpty()) {
//            BigDecimal net_rx_all = new BigDecimal(0);
//            for (PerMonitorDO perMonitorDO : netBytesRxAverageList) {
//                if (perMonitorDO.getInstance().isEmpty()) {
//                    perMonitorDO.setInstance("0");
//                }
//                String metricName = "NetworkInBytes";
//                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
//                BigDecimal lastValue = NumberUtil.mul(value, 1024);
//                net_rx_all = net_rx_all.add(lastValue);
//                Point point = Point.measurement("zj_cloud_host")
//                        .tag("label", "net")
//                        .tag("uuid", uuid)
//                        .tag("metricName", metricName)
//                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                        .tag("vm_type", perMonitorDO.getInstance())
//                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                        .addField("vm_metricName", metricName)
//                        .addField("type", perMonitorDO.getInstance())
//                        .addField("value", lastValue)
//                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                        .build();
//                batchPoints.point(point);
//            }
//
//
//            Point point = Point.measurement("zj_cloud_host")
//                    .tag("label", "net")
//                    .tag("uuid", uuid)
//                    .tag("metricName", "NetworkAllInBytes")
//                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                    .tag("vm_type", "NetworkAllInBytes")
//                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                    .addField("vm_metricName", "NetworkAllInBytes")
//                    .addField("type", "NetworkAllInBytes")
//                    .addField("value", NumberUtil.mul(NumberUtil.div(net_rx_all, 8), 1024))
//                    .time(netBytesRxAverageList.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                    .build();
//            batchPoints.point(point);
//
//        }
//
//
//        List<PerMonitorDO> netBytesTxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.bytesTx.average");
//        if (!netBytesTxAverageList.isEmpty()) {
//            BigDecimal net_tx_all = new BigDecimal(0);
//            for (PerMonitorDO perMonitorDO : netBytesTxAverageList) {
//                if (perMonitorDO.getInstance().isEmpty()) {
//                    perMonitorDO.setInstance("0");
//                }
//                String metricName = "NetworkOutBytes";
//                BigDecimal value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
//                BigDecimal lastValue = NumberUtil.mul(value, 1024);
//                net_tx_all = net_tx_all.add(lastValue);
//                Point point = Point.measurement("zj_cloud_host")
//                        .tag("label", "net")
//                        .tag("uuid", uuid)
//                        .tag("metricName", metricName)
//                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                        .tag("vm_type", perMonitorDO.getInstance())
//                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                        .addField("vm_metricName", metricName)
//                        .addField("type", perMonitorDO.getInstance())
//                        .addField("value", lastValue)
//                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                        .build();
//                batchPoints.point(point);
//            }
//
//            Point point = Point.measurement("zj_cloud_host")
//                    .tag("label", "net")
//                    .tag("uuid", uuid)
//                    .tag("metricName", "NetworkAllOutBytes")
//                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                    .tag("vm_type", "NetworkAllOutBytes")
//                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                    .addField("vm_metricName", "NetworkAllOutBytes")
//                    .addField("type", "NetworkAllOutBytes")
//                    .addField("value", NumberUtil.mul(NumberUtil.mul(net_tx_all, 8), 1024))
//                    .time(netBytesTxAverageList.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                    .build();
//            batchPoints.point(point);
//
//        }
//
//        List<PerMonitorDO> netPacketsRxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.packetsRx.summation");
//        if (!netPacketsRxAverageList.isEmpty()) {
//            for (PerMonitorDO perMonitorDO : netPacketsRxAverageList) {
//                if (perMonitorDO.getInstance().isEmpty()) {
//                    perMonitorDO.setInstance("0");
//                }
//                String metricName = "NetworkInPackets";
//                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
//                BigDecimal lastValue = NumberUtil.div(value, 1024);
//                Point point = Point.measurement("zj_cloud_host")
//                        .tag("label", "net")
//                        .tag("uuid", uuid)
//                        .tag("metricName", metricName)
//                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                        .tag("vm_type", perMonitorDO.getInstance())
//                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                        .addField("vm_metricName", metricName)
//                        .addField("type", perMonitorDO.getInstance())
//                        .addField("value", lastValue)
//                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                        .build();
//                batchPoints.point(point);
//            }
//        }
//
//        List<PerMonitorDO> netPacketsTxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.packetsTx.summation");
//        if (!netPacketsTxAverageList.isEmpty()) {
//            for (PerMonitorDO perMonitorDO : netPacketsTxAverageList) {
//                if (perMonitorDO.getInstance().isEmpty()) {
//                    perMonitorDO.setInstance("0");
//                }
//                String metricName = "NetworkOutPackets";
//                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
//                BigDecimal lastValue = NumberUtil.div(value, 1024);
//                Point point = Point.measurement("zj_cloud_host")
//                        .tag("label", "net")
//                        .tag("uuid", uuid)
//                        .tag("metricName", metricName)
//                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                        .tag("vm_type", perMonitorDO.getInstance())
//                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                        .addField("vm_metricName", metricName)
//                        .addField("type", perMonitorDO.getInstance())
//                        .addField("value", lastValue)
//                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                        .build();
//                batchPoints.point(point);
//            }
//        }
//
//        List<PerMonitorDO> netDroppedRxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.droppedRx.summation");
//        if (!netDroppedRxAverageList.isEmpty()) {
//            for (PerMonitorDO perMonitorDO : netDroppedRxAverageList) {
//                if (perMonitorDO.getInstance().isEmpty()) {
//                    perMonitorDO.setInstance("0");
//                }
//                String metricName = "NetworkInErrors";
//                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
//                BigDecimal lastValue = NumberUtil.div(value, 1024);
//                Point point = Point.measurement("zj_cloud_host")
//                        .tag("label", "net")
//                        .tag("uuid", uuid)
//                        .tag("metricName", metricName)
//                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                        .tag("vm_type", perMonitorDO.getInstance())
//                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                        .addField("vm_metricName", metricName)
//                        .addField("type", perMonitorDO.getInstance())
//                        .addField("value", lastValue)
//                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                        .build();
//                batchPoints.point(point);
//            }
//        }
//
//        List<PerMonitorDO> netDroppedTxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.droppedTx.summation");
//        if (!netDroppedTxAverageList.isEmpty()) {
//            for (PerMonitorDO perMonitorDO : netDroppedTxAverageList) {
//                if (perMonitorDO.getInstance().isEmpty()) {
//                    perMonitorDO.setInstance("0");
//                }
//                String metricName = "NetworkOutErrors";
//                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
//                BigDecimal lastValue = NumberUtil.div(value, 1024);
//                Point point = Point.measurement("zj_cloud_host")
//                        .tag("label", "net")
//                        .tag("uuid", uuid)
//                        .tag("metricName", metricName)
//                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                        .tag("vm_type", perMonitorDO.getInstance())
//                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                        .addField("vm_metricName", metricName)
//                        .addField("type", perMonitorDO.getInstance())
//                        .addField("value", lastValue)
//                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                        .build();
//                batchPoints.point(point);
//            }
//        }
//
//            long eTime = System.currentTimeMillis(); // 结束时间
//            long executionTime = eTime - startTime;
//            double executionTimeInSeconds = executionTime / 1000.0;
//            System.out.println("一个虚拟机采集执行时间: " + executionTimeInSeconds + " seconds");
//        }
//        influxDBTemplate.writeBatch(BatchPoints.builder().points(batchPoints.getPoints()).build());
//        serviceInstance.getServerConnection().logout();
//    }


    private static boolean isVersionLessThan(String version1, String version2) {
        String[] v1Parts = version1.split("\\.");
        String[] v2Parts = version2.split("\\.");

        int length = Math.max(v1Parts.length, v2Parts.length);

        for (int i = 0; i < length; i++) {
            int v1 = i < v1Parts.length ? Integer.parseInt(v1Parts[i]) : 0;
            int v2 = i < v2Parts.length ? Integer.parseInt(v2Parts[i]) : 0;

            if (v1 < v2) {
                return true;
            } else if (v1 > v2) {
                return false;
            }
        }

        return false;
    }


    private void volumeVmwareInfo(String uuid, PlatformconfigDTO platformconfigDTO, VirtualMachine virtualMachine,
                                  VirtualDevice[] devices, List<VmDiskFileInfo> diskFileInfos, List<VolumeDTO> volumeDTOList) {
        List<Map> virtualDisks = new ArrayList<>();
        List<Map> vmDiskFileInfoList = new ArrayList<>();
        boolean isMount = false;

        if (devices != null && devices.length > 0) {
            for (VirtualDevice virtualDevice : devices) {
                if (virtualDevice instanceof VirtualDisk) {
                    VirtualDisk disk = (VirtualDisk) virtualDevice;
                    // 获取磁盘backing信息
                    String volumeUuid = "";
                    String fileName = "";
                    String datastoreName = "";
                    if (disk.getBacking() instanceof VirtualDiskFlatVer2BackingInfo) {
                        VirtualDiskFlatVer2BackingInfo backing = (VirtualDiskFlatVer2BackingInfo) disk.getBacking();
                        volumeUuid = backing.getUuid();
                        fileName = backing.getFileName();
                        datastoreName = backing.getDatastore().getVal();
                    } else if (disk.getBacking() instanceof VirtualDiskSparseVer2BackingInfo) {
                        VirtualDiskSparseVer2BackingInfo backing = (VirtualDiskSparseVer2BackingInfo) disk.getBacking();
                        volumeUuid = backing.getUuid();
                        fileName = backing.getFileName();
                        datastoreName = backing.getDatastore().getVal();
                    } else {
                        log.warn("未知的磁盘backing类型: {}", disk.getBacking().getClass().getName());
                        continue;
                    }

                    Map<String, Object> map = new HashMap();
                    map.put("capacityInKB", disk.getCapacityInKB());
                    map.put("uuid", volumeUuid);
                    map.put("name", fileName);
                    map.put("datastore", datastoreName);
                    virtualDisks.add(map);
                }
            }
        }
        Datastore[] datastores;
        try {
            datastores = virtualMachine.getDatastores();
        } catch (RemoteException e) {
            datastores = new Datastore[]{};
        }
        if (diskFileInfos.size() > 0) {
            for (int i = 0; i < diskFileInfos.size(); i++) {
                VmDiskFileInfo diskFileInfo = diskFileInfos.get(i);
                String name = Convert.toStr(diskFileInfo.getPath());
                Long actualSize = Convert.toLong(diskFileInfo.getFileSize());
                String volumeUuid = name + "-vm";
                BigDecimal d = new BigDecimal(0);
                BigDecimal actual_ratio = new BigDecimal(0);
                BigDecimal actualFree = new BigDecimal(0);

                if (StrUtil.isNotEmpty(virtualMachine.getName())) {
                    isMount = true;
                }
                VolumeDTO volumeDTO = new VolumeDTO();

                volumeDTO.setName(name);
                volumeDTO.setPlatformId(platformconfigDTO.getId());
                volumeDTO.setPlatformName(platformconfigDTO.getName());
                volumeDTO.setDescription("vm云盘");
                volumeDTO.setVmInstanceUuid(uuid);
                volumeDTO.setVmInstanceName(virtualMachine.getName());
                volumeDTO.setType("Data");
                volumeDTO.setFormat("vmdk");
                volumeDTO.setState("Enabled");
                volumeDTO.setStatus("Ready");
                volumeDTO.setDeleted(0L);
                volumeDTO.setMediaType(MediaType.ROTATE.getEnName());
                volumeDTO.setIsMount(isMount);

                volumeDTO.setUuid(volumeUuid);
                volumeDTO.setSize(Convert.toLong(actualSize));
                volumeDTO.setActualSize(actualSize);
                volumeDTO.setActualRatio(Convert.toStr(actual_ratio));
                volumeDTO.setActualFree(Convert.toLong(actualFree));
                volumeDTO.setActualUse(actualSize);

                if (virtualDisks.size() > 0) {
                    for (Map map1 : virtualDisks) {
                        if (name.equals(StrUtil.toString(map1.get("name")))) {
                            Long size = Convert.toLong(map1.get("capacityInKB"));
                            volumeUuid = Convert.toStr(map1.get("uuid"));
                            BigDecimal b = NumberUtil.div(actualSize, NumberUtil.mul(Convert.toBigDecimal(size), 1024), 2);
                            actualFree = NumberUtil.sub(NumberUtil.mul(Convert.toBigDecimal(size), 1024), actualSize);
                            actual_ratio = b;
                            d = NumberUtil.mul(Convert.toBigDecimal(size), 1024);
                            volumeDTO.setType("Root");
                            volumeDTO.setUuid(volumeUuid);
                            volumeDTO.setSize(Convert.toLong(d));
                            volumeDTO.setActualSize(actualSize);
                            volumeDTO.setActualRatio(Convert.toStr(actual_ratio));
                            volumeDTO.setActualFree(Convert.toLong(actualFree));
                            volumeDTO.setActualUse(actualSize);
                            for (Datastore dm : datastores) {
                                if (dm.getMOR().getVal().equals(map1.get("datastore"))) {
                                    volumeDTO.setPrimaryStorageUuid(dm.getSummary().getName() + dm.getSummary().getUrl() + dm.getParent().getMOR().getVal());
                                    volumeDTO.setPrimaryStorageName(dm.getSummary().getName());
                                    volumeDTO.setPrimaryStorageType(dm.getSummary().getType());
                                }
                            }
                        }
                    }
                }

                volumeDTOList.add(volumeDTO);
            }
        }
    }
}

