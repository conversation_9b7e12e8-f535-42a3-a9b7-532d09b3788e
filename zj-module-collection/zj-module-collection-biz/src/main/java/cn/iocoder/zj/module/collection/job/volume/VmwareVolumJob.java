package cn.iocoder.zj.module.collection.job.volume;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.util.SampleUtil;
import cn.iocoder.zj.module.monitor.api.hostinfo.HostInfoApi;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.valume.VolumeApi;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeSnapshotDTO;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.vmware.vim25.mo.ServiceInstance;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.iocoder.zj.module.collection.service.vmware.host.HostComputerResourceSummary.getVMSnapshots;

/**
 * @ClassName : VmwareHostJob  //类名
 * @Description :   采集vmware宿主机数据//描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/7/25  11:03
 */

@Component
@Slf4j
public class VmwareVolumJob {

    @Resource
    private PlatformRedisDAO platformRedisDAO;
    @Resource
    private VolumeApi volumeApi;
    @Resource
    HostInfoApi hostInfoApi;

    private static final String PLATFORM_TYPE = "vmware";

    @XxlJob("vmwareSnapShortInfo")
    public void vmwareSnapShortInfo() throws Exception {

        List<PlatformconfigDTO> vmwarePlatforms = platformRedisDAO.get("platform").stream()
                .filter(dto -> PLATFORM_TYPE.equals(dto.getTypeCode()))
                .collect(Collectors.toList());
        if (vmwarePlatforms.isEmpty()) {
            return;
        }

        for (PlatformconfigDTO platform : vmwarePlatforms) {
            if (platform.getId() == 18) {
                continue;
            }
            handlePlatform(platform);
        }

    }

    private void handlePlatform(PlatformconfigDTO platform) throws Exception {
        ServiceInstance serviceInstance = SampleUtil.createServiceInstance(
                platform.getUrl(),
                platform.getUsername(),
                platform.getPassword()
        );

        List<Map<String, Object>> vmSnapshots = getVMSnapshots(serviceInstance);
        if (vmSnapshots == null || vmSnapshots.isEmpty()) {
            return;
        }
        List<HostInfoRespCreateReqDTO> hostInfoApiVmByPlatformId = hostInfoApi.getVmByPlatformId(platform.getId());
        Map<String, HostInfoRespCreateReqDTO> hostVmsMap = Optional.ofNullable(hostInfoApiVmByPlatformId)
                .orElse(Collections.emptyList())
                .stream()
                .filter(host -> StrUtil.isNotEmpty(host.getVms()))
                .collect(Collectors.toMap(
                        HostInfoRespCreateReqDTO::getVms,
                        Function.identity(),
                        (existing, replacement) -> {
                            return existing;
                        },
                        HashMap::new
                ));

        List<VolumeSnapshotDTO> currentSnapshots = convertToSnapshotDTOs(vmSnapshots, platform, hostVmsMap);
        syncSnapshots(currentSnapshots, platform);
    }

    private List<VolumeSnapshotDTO> convertToSnapshotDTOs(List<Map<String, Object>> vmSnapshots, PlatformconfigDTO platform, Map<String, HostInfoRespCreateReqDTO> hostVmsMap) {
        return vmSnapshots.stream()
                .map(snapshot -> {
                    VolumeSnapshotDTO dto = new VolumeSnapshotDTO();
                    dto.setCreateTime(DateUtil.toLocalDateTime(new Date()));
                    dto.setUuid(Objects.toString(snapshot.get("uuid"), ""));
                    dto.setName(Objects.toString(snapshot.get("name"), ""));
                    dto.setDescription(Objects.toString(snapshot.get("description"), ""));
                    dto.setVolumeUuid(Objects.toString(snapshot.get("vms"), ""));
                    dto.setInstallPath(Objects.toString(snapshot.get("snapshotDirectory"), ""));
                    dto.setType("主机快照");
                    dto.setLatest("true");
                    dto.setPlatformName(platform.getName());
                    dto.setPlatformId(platform.getId());
                    dto.setTypeName(PLATFORM_TYPE);
                    dto.setStatus("Enabled");
                    Date createTime = DateUtil.parse(snapshot.get("createTime").toString(), "EEE MMM dd HH:mm:ss z yyyy", Locale.ENGLISH);
                    dto.setVCreateDate(createTime);;
                    dto.setVUpdateDate(new Date());
                    dto.setFormat("vmsn");
                    dto.setIsMemory(false);
                    dto.setDeleted(0L);
                    HostInfoRespCreateReqDTO hostUuid = hostVmsMap.get(snapshot.get("vms").toString());
                    if (BeanUtil.isNotEmpty(hostUuid)) {
                        dto.setHostUuid(hostUuid.getUuid());
                        dto.setHostName(hostUuid.getName());
                    }
                    return dto;
                })
                .collect(Collectors.toList());
    }

    private void syncSnapshots(List<VolumeSnapshotDTO> currentSnapshots, PlatformconfigDTO platform) {
        try {
            Long existingCount = volumeApi.getVolumeSnapshotCount(PLATFORM_TYPE).getData();
            if (existingCount == 0) {
                volumeApi.addVolumeSnapshots(currentSnapshots);
                return;
            }
            List<VolumeSnapshotDTO> existingSnapshots = volumeApi.getVolumeSnapshotByPlatformId(platform.getId());

            // 计算需要新增、更新和删除的快照
            Map<String, List<VolumeSnapshotDTO>> diffResult = calculateSnapshotDiff(currentSnapshots, existingSnapshots);

            // 执行同步操作
            executeSync(diffResult);
            log.info("平台 [{}] 快照同步完成 - 新增: {}, 更新: {}, 删除: {}",
                    platform.getName(),
                    diffResult.get("add").size(),
                    diffResult.get("update").size(),
                    diffResult.get("delete").size());
        } catch (Exception e) {
            log.error("同步快照失败: {}", e.getMessage(), e);
        }
    }

    private Map<String, List<VolumeSnapshotDTO>> calculateSnapshotDiff(List<VolumeSnapshotDTO> currentSnapshots, List<VolumeSnapshotDTO> existingSnapshots) {
        Set<String> existingUuids = existingSnapshots.stream()
                .map(VolumeSnapshotDTO::getUuid)
                .collect(Collectors.toSet());
        Map<String, List<VolumeSnapshotDTO>> result = new HashMap<>();
        result.put("add", currentSnapshots.stream()
                .filter(s -> !existingUuids.contains(s.getUuid()))
                .collect(Collectors.toList()));

        result.put("update", currentSnapshots.stream()
                .filter(s -> existingUuids.contains(s.getUuid()))
                .collect(Collectors.toList()));
        result.put("delete", existingSnapshots.stream()
                .filter(s -> !currentSnapshots.stream()
                        .map(VolumeSnapshotDTO::getUuid)
                        .collect(Collectors.toSet())
                        .contains(s.getUuid()))
                .collect(Collectors.toList()));
        return result;
    }

    private void executeSync(Map<String, List<VolumeSnapshotDTO>> diffResult) {
        if (!diffResult.get("update").isEmpty()) {
            volumeApi.updateVolumeSnapshots(diffResult.get("update"));
        }
        if (!diffResult.get("delete").isEmpty()) {
            volumeApi.delVolumeSnapshots(diffResult.get("delete"));
        }
        if (!diffResult.get("add").isEmpty()) {
            volumeApi.addVolumeSnapshots(diffResult.get("add"));
        }
    }
}
