package cn.iocoder.zj.module.collection.service.volumes;

import cn.iocoder.zj.framework.common.pojo.CommonResult;

import java.util.Map;

public interface ZstackVolumesService {
    String volumeBaseInfo(String url, String token);
    String volumeSnapshotInfo(String url, String token);

    CommonResult<Map<String,String>> mountVolumeToHost(String volumeUuid, String hostUuid, Long platformId);
    void mountVolumeToHardware(String volumeUuid, String hardwareUuid,Long platformId,String mountPath);
    CommonResult<Map<String,String>> uninstallVolumeFromHost(String volumeUuid, String hostUuid, Long platformId);

    void uninstallVolumeFromHardware(String volumeUuid, String hardwareUuid,Long platformId);

    String getVmAttachableDataVolume(String url, String token,String volumeUuid);
}
