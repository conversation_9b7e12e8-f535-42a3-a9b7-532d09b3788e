package cn.iocoder.zj.module.collection.service.cloud;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import cn.iocoder.zj.module.collection.service.sangFor.core.SangForApiConstant;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.StringUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Log4j2
@Service
public class SangForDeviceServiceImpl implements ISangForDeviceService {
    @Override
    public JSONArray getClouds(PlatformconfigDTO platform, JSONObject tokenInfo) {
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        String url = platform.getUrl() + SangForApiConstant.GET_CLOUDS;
        HttpResponse res = HttpRequest.get(url)
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != res.getStatus()) {
            throw new RuntimeException("获取虚拟机失败！");
        }
        return JSONObject.parseObject(res.body()).getJSONArray("data");
    }

    @Override
    public JSONArray getCloudsVms(PlatformconfigDTO platform, JSONObject tokenInfo) {
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        String url = platform.getUrl() + SangForApiConstant.GET_CLOUDS_VMS;
        HttpResponse res = HttpRequest.get(url)
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != res.getStatus()) {
            throw new RuntimeException("获取虚拟机失败！");
        }
        return JSONObject.parseObject(res.body()).getJSONArray("data").getJSONObject(0).getJSONArray("data");
    }

    @Override
    public JSONArray getCloudsByHostId(PlatformconfigDTO platform, JSONObject tokenInfo, String hostId) {
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        String url = platform.getUrl() + SangForApiConstant.GET_CLOUDS_BY_HOST_ID.replace("{host_id}",hostId);
        HttpResponse res = HttpRequest.get(url)
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != res.getStatus()) {
            throw new RuntimeException("获取虚拟机失败！");
        }
        return JSONObject.parseObject(res.body()).getJSONArray("data");
    }

    @Override
    public JSONArray getNicInfo(PlatformconfigDTO platform, JSONObject tokenInfo) {
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        String url = platform.getUrl() + SangForApiConstant.GET_IFACES;
        HttpResponse res = HttpRequest.get(url)
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != res.getStatus()) {
            throw new RuntimeException("获取物理网络失败！");
        }
        return JSONObject.parseObject(res.body()).getJSONArray("data");
    }

    @Override
    public JSONArray getVmNics(PlatformconfigDTO p, JSONObject tokenInfo, String vmid) {
        String url = p.getUrl() + (SangForApiConstant.GET_VM_CONFIG.replace("{vmid}", vmid));
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        HttpResponse res = HttpRequest.get(url)
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != res.getStatus()) {
            throw new RuntimeException("网络信息获取失败！");
        }
        JSONArray result = new  JSONArray();
        JSONObject data = JSONObject.parseObject(res.body()).getJSONObject("data");
        for (int i = 0; i < 100; i++) {
            String netTmp = data.getString("net" + i);
            if(netTmp == null || netTmp.isEmpty()){
                break;
            }
            JSONObject obj = new JSONObject();
            obj.put("mac_addr", netTmp.split(",")[0].split("=")[1]);
            obj.put("net_name", netTmp.split(",")[0].split("=")[0]);
            obj.put("net_name", netTmp.split(",")[0].split("=")[0]);
            for (String s : netTmp.split(",")) {
                obj.put(s.split("=")[0], s.split("=")[1]);
            }
            result.add(obj);
        }

        return result;
    }

    @Override
    public JSONArray getVtpstorageDetail(PlatformconfigDTO platform, JSONObject tokenInfo, String id) {
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        String url = platform.getUrl() + SangForApiConstant.GET_VTPSTORAGE_DETAIL.replace("{id}",id);
        HttpResponse res = HttpRequest.get(url)
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != res.getStatus()) {
            throw new RuntimeException("主存储关联物理机列表失败！");
        }
        return JSONObject.parseObject(res.body()).getJSONArray("data");
    }

    @Override
    public JSONObject getVmSnapshots(PlatformconfigDTO p, JSONObject tokenInfo, String vmid) {
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        String url = p.getUrl() + SangForApiConstant.GET_VM_SNAPSHOT_LIST.replace("{vmid}",vmid);
        HttpResponse res = HttpRequest.get(url)
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != res.getStatus()) {
            log.info("虚拟机关联的快照列表获取失败 = {}, 返回信息 = {}", url, res.body());
            return new JSONObject();
        }
        return JSONObject.parseObject(res.body()).getJSONObject("data").getJSONObject("snapshot");
    }

    @Override
    public JSONObject getVmBios(PlatformconfigDTO p, JSONObject tokenInfo, String vmid) {
        String url = p.getUrl() + (SangForApiConstant.GET_VM_CONFIG.replace("{vmid}", vmid));
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        HttpResponse res = HttpRequest.get(url)
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != res.getStatus()) {
            throw new RuntimeException("网络信息获取失败！");
        }
        return JSONObject.parseObject(res.body()).getJSONObject("data");
    }

    @Override
    public JSONObject getNetInfos(PlatformconfigDTO platform, JSONObject tokenInfo, String vmid) {
        String url = platform.getUrl() + (SangForApiConstant.GET_VM_CONFIG.replace("{vmid}", vmid));
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        HttpResponse res = HttpRequest.get(url)
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != res.getStatus()) {
            throw new RuntimeException("网络信息获取失败！");
        }
        JSONObject supInfo = JSONObject.parseObject(res.body()).getJSONObject("data").getJSONObject("ctrl").getJSONObject("hotplug_sup_info");
        String osName = "";
        JSONObject result = new JSONObject();
        if(BeanUtil.isNotEmpty(supInfo)){
            osName = supInfo.getString("osname")+"("+supInfo.getString("sysbit")+"-bit)";
            if(supInfo.getInteger("net")>0) {
                String  ip = "";
                String mac = "";
                for (int i = 0; i < supInfo.getInteger("net"); i++) {
                    String[] netInfo = JSONObject.parseObject(res.body()).getJSONObject("data").getString("net" + i).split(",");
                    for (String net : netInfo) {
                        if (net.contains("virtio=")) {
                            mac = mac + net.replace("virtio=", "") + ",";
                        }
                        if (net.contains("ip=")) {
                            ip = ip + net.replace("ip=", "") + ",";
                        }
                    }
                }
                result.put("ip", StringUtils.isNotEmpty(ip) ? ip.substring(0, ip.length() - 1) : "");
                result.put("mac", StringUtils.isNotEmpty(mac) ? mac.substring(0, mac.length() - 1) : "");
            }
        }else {
            osName = JSONObject.parseObject(res.body()).getJSONObject("data").getString("osname");
            String[] netInfo = JSONObject.parseObject(res.body()).getJSONObject("data").getString("net0").split(",");
            String ip = JSONObject.parseObject(res.body()).getJSONObject("data").getJSONObject("location").getString("ip");
            String mac = netInfo[0].split("=")[1];
            result.put("ip", ip);
            result.put("mac",mac);
        }

        result.put("osName",osName);
        return result;
    }

    @Override
    public JSONArray getHardware(PlatformconfigDTO platform, JSONObject tokenInfo) {
        String url = platform.getUrl() + SangForApiConstant.GET_HARDWARE;
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        HttpResponse res = HttpRequest.get(url)
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != res.getStatus()) {
            throw new RuntimeException("获取宿主机列表失败！");
        }
        return JSONObject.parseObject(res.body()).getJSONArray("data");
    }

    @Override
    public JSONObject getHardwareDetail(PlatformconfigDTO platform, JSONObject tokenInfo, String id) {
        String url = platform.getUrl() + (SangForApiConstant.GET_HARDWARE_INFO.replace("{id}", id));
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        HttpResponse res = HttpRequest.get(url)
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != res.getStatus()) {
            throw new RuntimeException("获取宿主机详情失败！");
        }
        JSONObject jsonObj = JSONObject.parseObject(res.body()).getJSONObject("data");
        JSONArray storages = jsonObj.getJSONArray("storage_status");
        JSONObject cpuStatus = jsonObj.getJSONObject("cpu_status");
        JSONObject memStatus = jsonObj.getJSONObject("mem_status");
        JSONObject hostInfo = new JSONObject();
        hostInfo.put("power", jsonObj.getString("status"));

        Long disk_used_bytes = 0L;
        Long disk_free_bytes = 0L;
        Long total_disk_capacity = 0L;
        for (int i = 0; i < storages.size(); i++) {
            JSONObject item = storages.getJSONObject(i);
            total_disk_capacity += item.getLong("total");
            disk_free_bytes += item.getLong("free");
            disk_used_bytes += item.getLong("used");
        }
        BigDecimal disk_used = new BigDecimal((double) disk_used_bytes / total_disk_capacity).setScale(2, BigDecimal.ROUND_HALF_UP);
        //磁盘
        hostInfo.put("disk_used_bytes", new BigDecimal(disk_used_bytes));
        hostInfo.put("disk_free_bytes", new BigDecimal(disk_free_bytes));
        hostInfo.put("total_disk_capacity", new BigDecimal(total_disk_capacity));
        hostInfo.put("disk_used", disk_used);
        //cpu
        hostInfo.put("cpu_sockets", cpuStatus.getIntValue("sockets"));
        hostInfo.put("architecture", cpuStatus.getString("type"));
        hostInfo.put("ratio", cpuStatus.getString("ratio"));
        //内存
        hostInfo.put("total_memory_capacity", memStatus.getLong("total"));
        hostInfo.put("available_memory_capacity", memStatus.getLong("free"));
        hostInfo.put("memory_used", memStatus.getBigDecimal("ratio").setScale(2, BigDecimal.ROUND_HALF_UP));
        return hostInfo;
    }

    @Override
    public JSONArray getStorages(PlatformconfigDTO platform, JSONObject tokenInfo) {
        String url = platform.getUrl() + SangForApiConstant.GET_STORAGE_LIST;
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        HttpResponse res = HttpRequest.get(url)
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != res.getStatus()) {
            throw new RuntimeException("获取存储列表失败！");
        }
        return JSONObject.parseObject(res.body()).getJSONObject("data").getJSONArray("local");
    }

    @Override
    public JSONObject getStorageDetail(PlatformconfigDTO p, JSONObject tokenInfo, String id) {
        String url = p.getUrl() + SangForApiConstant.GET_STORAGE_DETAIL.replace("{storageUuid}", id);
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        HttpResponse res = HttpRequest.get(url)
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != res.getStatus()) {
            throw new RuntimeException("获取存储列表失败！");
        }
        return JSONObject.parseObject(res.body()).getJSONObject("data");
    }

    @Override
    public JSONArray getNetworks(PlatformconfigDTO platform, JSONObject tokenInfo) {
        String url = platform.getUrl() + SangForApiConstant.GET_V_NETWORK_LIST;
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        HttpResponse res = HttpRequest.get(url)
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != res.getStatus()) {
            throw new RuntimeException("获取网络列表失败！");
        }
        JSONArray target = new JSONArray();
        JSONArray respArrays = JSONObject.parseObject(res.body()).getJSONArray("data");
        for (int i = 0; i < respArrays.size(); i++) {
            JSONArray netArray = JSONObject.parseObject(JSONUtil.toJsonPrettyStr(respArrays.get(i))).getJSONArray("data");
            for (Object item : netArray) {
                JSONObject net = JSONObject.parseObject(JSONUtil.toJsonPrettyStr(item));
                target.add(net);
            }
        }
        return target;
    }

    @Override
    public Map<String,JSONArray> getNetworksInfo(PlatformconfigDTO platform, JSONObject tokenInfo) {
        JSONArray networkArr = getNetworks(platform, tokenInfo);
        JSONArray vlanGroup = new JSONArray();
        JSONArray target = new JSONArray();
        Map<String,JSONArray> map = new HashMap<>();
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        for (int i = 0; i < networkArr.size(); i++) {
            JSONObject net = JSONObject.parseObject(JSONUtil.toJsonPrettyStr(networkArr.get(i)));
            HttpResponse res = HttpRequest.get(platform.getUrl() + SangForApiConstant.GET_NETWORK_INFO + net.getString("id"))
                    .header("CSRFPreventionToken", CSRFPreventionToken)
                    .execute();
            if (res.getStatus() == 200) {
                JSONObject netInfo = JSONObject.parseObject(res.body()).getJSONObject("data");
                target.addAll(netInfo.getJSONArray("bridgeList"));
                vlanGroup.addAll(netInfo.getJSONArray("vlanGroup"));
            }
        }
        map.put("bridgeList",target);
        map.put("vlanGroup",vlanGroup);
        return map;
    }

    @Override
    public JSONArray getStoragesUsageInPercent(PlatformconfigDTO platform, JSONObject tokenInfo, String storageId) {
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        HttpResponse res = HttpRequest.get(platform.getUrl() + SangForApiConstant.GET_STORAGE_USAGE.replace("{id}", storageId))
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != res.getStatus()) {
            throw new RuntimeException("获取存储空间使用率趋势失败！");
        }
        return dealInfo(JSONObject.parseObject(res.body()).getJSONArray("data"), "空间利用率");
    }

    @Override
    public JSONArray getCpuOveruseConfig(PlatformconfigDTO platform, JSONObject tokenInfo) {
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        HttpResponse res = HttpRequest.get(platform.getUrl() + SangForApiConstant.GET_CPU_OVERUSE_CONFIG)
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != res.getStatus()) {
//            throw new RuntimeException("获取CPU超频设置失败！");
            return null;
        }
        JSONObject jsonObject = JSONObject.parseObject(res.body());
        return jsonObject.getJSONObject("data").getJSONArray("nodes");
    }

    @Override
    public JSONArray getMemoryOveruseConfig(PlatformconfigDTO platform, JSONObject tokenInfo) {
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        HttpResponse res = HttpRequest.get(platform.getUrl() + SangForApiConstant.GET_MEMORY_OVERUSE_CONFIG)
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != res.getStatus()) {
            throw new RuntimeException("获取内存超频设置失败！");
        }
        JSONObject jsonObject = JSONObject.parseObject(res.body());
        return jsonObject.getJSONObject("data").getJSONArray("nodes");
    }

    @Override
    public JSONObject getVmDetail(PlatformconfigDTO platform, JSONObject tokenInfo, String vmid) {
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        HttpResponse res = HttpRequest.get(platform.getUrl() + SangForApiConstant.GET_VM_DETAIL.replace("{vmid}", vmid))
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != res.getStatus()) {
            throw new RuntimeException("获取虚拟机详情失败！");
        }
        JSONObject usageInfo = new JSONObject();
        JSONObject obj = JSONObject.parseObject(res.body()).getJSONObject("data");

        JSONArray diskStatus = obj.getJSONArray("disk_status");
        usageInfo.put("diskStatus", diskStatus);

        JSONObject hardwareStatus = obj.getJSONObject("hardware_status");
        usageInfo.put("hardwareStatus", hardwareStatus);


        //CPU使用率
        JSONArray cpuSheet = dealInfo(obj.getJSONObject("cpu_sheet").getJSONArray("hour"), "CPU使用率");
        usageInfo.put("cpuSheet", cpuSheet);
        //已使用内存
        JSONArray usedMemSheet = dealInfo(obj.getJSONObject("mem_sheet").getJSONArray("hour"), "已使用内存");
        usageInfo.put("usedMemSheet", usedMemSheet);
        //总内存,计算空闲内存
        JSONArray totalMemSheet = dealInfo(obj.getJSONObject("mem_sheet").getJSONArray("hour"), "总内存");
        JSONArray freeMemSheet = new JSONArray();
        int length = Math.min(usedMemSheet.size(), totalMemSheet.size());
        for (int i = 0; i < length; i++) {
            JSONObject jsonObject = new JSONObject();
            Long free = totalMemSheet.getJSONObject(i).getLong("val") - usedMemSheet.getJSONObject(i).getLong("val");
            jsonObject.put("val", free);
            jsonObject.put("time", totalMemSheet.getJSONObject(i).getLong("time"));
            freeMemSheet.add(jsonObject);
        }
        usageInfo.put("freeMemSheet", freeMemSheet);
        //IO读速率
        JSONArray diskReadSheet = new JSONArray();
        if (obj.getJSONObject("disk_sheet").getJSONObject("total") != null) {
            diskReadSheet = dealInfo(obj.getJSONObject("disk_sheet").getJSONObject("total").getJSONObject("speed").getJSONArray("hour"), "IO读速率");
        } else {
            diskReadSheet = dealInfo(obj.getJSONObject("disk_sheet").getJSONObject("speed").getJSONArray("hour"), "IO读速率");
        }
        usageInfo.put("diskReadSheet", diskReadSheet);
        //IO写速率
        JSONArray diskWriteSheet = new JSONArray();
        if (obj.getJSONObject("disk_sheet").getJSONObject("total") != null) {
            diskWriteSheet = dealInfo(obj.getJSONObject("disk_sheet").getJSONObject("total").getJSONObject("speed").getJSONArray("hour"), "IO写速率");
        } else {
            diskWriteSheet = dealInfo(obj.getJSONObject("disk_sheet").getJSONObject("speed").getJSONArray("hour"), "IO写速率");
        }
        usageInfo.put("diskWriteSheet", diskWriteSheet);
        JSONArray diskReadIOSheet = new JSONArray();
        if (obj.getJSONObject("disk_sheet").getJSONObject("total") != null) {
            diskReadIOSheet = dealInfo(obj.getJSONObject("disk_sheet").getJSONObject("total").getJSONObject("oper").getJSONArray("hour"), "IO读次数");
        } else {
            diskReadIOSheet = dealInfo(obj.getJSONObject("disk_sheet").getJSONObject("oper").getJSONArray("hour"), "IO读次数");
        }
        //IO读次数
        usageInfo.put("diskReadIOSheet", diskReadIOSheet);
        JSONArray diskWriteIOSheet = new JSONArray();
        if (obj.getJSONObject("disk_sheet").getJSONObject("total") != null) {
            diskWriteIOSheet = dealInfo(obj.getJSONObject("disk_sheet").getJSONObject("total").getJSONObject("oper").getJSONArray("hour"), "IO写次数");
        } else {
            diskWriteIOSheet = dealInfo(obj.getJSONObject("disk_sheet").getJSONObject("oper").getJSONArray("hour"), "IO写次数");
        }
        //IO写次数
        usageInfo.put("diskWriteIOSheet", diskWriteIOSheet);
        JSONArray netOutSheet = new JSONArray();
        if (obj.getJSONObject("disk_sheet").getJSONObject("total") != null) {
            netOutSheet = dealInfo(obj.getJSONObject("net_sheet").getJSONObject("total").getJSONObject("bps").getJSONArray("hour"), "发送");
        } else {
            netOutSheet = dealInfo(obj.getJSONObject("net_sheet").getJSONObject("bps").getJSONArray("hour"), "发送");
        }
        //网卡发送速率
        usageInfo.put("netOutSheet", netOutSheet);
        JSONArray netInSheet = new JSONArray();
        if (obj.getJSONObject("disk_sheet").getJSONObject("total") != null) {
            netInSheet = dealInfo(obj.getJSONObject("net_sheet").getJSONObject("total").getJSONObject("bps").getJSONArray("hour"), "接收");
        } else {
            netInSheet = dealInfo(obj.getJSONObject("net_sheet").getJSONObject("bps").getJSONArray("hour"), "接收");
        }
        //网卡接收速率
        usageInfo.put("netInSheet", netInSheet);
        JSONArray netOutPacketSheet = new JSONArray();
        if (obj.getJSONObject("disk_sheet").getJSONObject("total") != null) {
            netOutPacketSheet = dealInfo(obj.getJSONObject("net_sheet").getJSONObject("total").getJSONObject("pps").getJSONArray("hour"), "发送");
        } else {
            netOutPacketSheet = dealInfo(obj.getJSONObject("net_sheet").getJSONObject("pps").getJSONArray("hour"), "发送");
        }
        //网卡发包速率
        usageInfo.put("netOutPacketSheet", netOutPacketSheet);
        JSONArray netInPacketSheet = new JSONArray();
        if (obj.getJSONObject("disk_sheet").getJSONObject("total") != null) {

            netInPacketSheet = dealInfo(obj.getJSONObject("net_sheet").getJSONObject("total").getJSONObject("pps").getJSONArray("hour"), "接收");
        } else {
            netInPacketSheet = dealInfo(obj.getJSONObject("net_sheet").getJSONObject("pps").getJSONArray("hour"), "接收");
        }
        //网卡收包速率
        usageInfo.put("netInPacketSheet", netInPacketSheet);
        return usageInfo;

    }

    @Override
    public JSONObject getHostDetail(PlatformconfigDTO platform, JSONObject tokenInfo, String nodeid) {
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        JSONObject usageInfo = new JSONObject();
        //cpu趋势
        HttpResponse cpuResp = HttpRequest.get(platform.getUrl() + SangForApiConstant.GET_HOST_DETAIL.replace("{nodeId}", nodeid) + "cpu")
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != cpuResp.getStatus()) {
            throw new RuntimeException("获取宿主机CPU趋势失败！");
        }
        JSONArray cpuInfo = JSONObject.parseObject(cpuResp.body()).getJSONArray("data");
        usageInfo.put("cpuInfo", dealInfo(cpuInfo, "CPU使用率"));
        //内存趋势
        HttpResponse memResp = HttpRequest.get(platform.getUrl() + SangForApiConstant.GET_HOST_DETAIL.replace("{nodeId}", nodeid) + "mem")
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != memResp.getStatus()) {
            throw new RuntimeException("获取宿主机内存趋势失败！");
        }
        JSONArray totalMemInfo = dealInfo(JSONObject.parseObject(memResp.body()).getJSONArray("data"), "总内存");
        JSONArray usedMemInfo = dealInfo(JSONObject.parseObject(memResp.body()).getJSONArray("data"), "已使用内存");
        JSONArray freeMemInfo = new JSONArray();
        int length = Math.min(usedMemInfo.size(), totalMemInfo.size());
        for (int i = 0; i < length; i++) {
            JSONObject jsonObject = new JSONObject();
            Long free = totalMemInfo.getJSONObject(i).getLong("val") - usedMemInfo.getJSONObject(i).getLong("val");
            jsonObject.put("val", free);
            jsonObject.put("time", totalMemInfo.getJSONObject(i).getLong("time"));
            freeMemInfo.add(jsonObject);
        }
        usageInfo.put("freeMemInfo", freeMemInfo);
        usageInfo.put("usedMemInfo", usedMemInfo);
        //网络速率
        HttpResponse netResp = HttpRequest.get(platform.getUrl() + SangForApiConstant.GET_HOST_DETAIL.replace("{nodeId}", nodeid) + "net")
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != netResp.getStatus()) {
            throw new RuntimeException("获取宿主机网络趋势失败！");
        }
        JSONArray netInfo = JSONObject.parseObject(netResp.body()).getJSONArray("data");
        usageInfo.put("netIn", dealInfo(netInfo, "接收"));
        usageInfo.put("netOut", dealInfo(netInfo, "发送"));
        //磁盘io速率
        HttpResponse speedResp = HttpRequest.get(platform.getUrl() + SangForApiConstant.GET_HOST_DETAIL.replace("{nodeId}", nodeid) + "io_speed")
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != speedResp.getStatus()) {
            throw new RuntimeException("获取宿主机磁盘io速率失败！");
        }
        JSONArray speedInfo = JSONObject.parseObject(speedResp.body()).getJSONArray("data");
        usageInfo.put("ioReadSpeed", dealInfo(speedInfo, "IO读速率"));
        usageInfo.put("ioWriteSpeed", dealInfo(speedInfo, "IO写速率"));
        //磁盘io次数
        HttpResponse operResp = HttpRequest.get(platform.getUrl() + SangForApiConstant.GET_HOST_DETAIL.replace("{nodeId}", nodeid) + "io_oper")
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != operResp.getStatus()) {
            throw new RuntimeException("获取宿主机磁盘io次数失败！");
        }
        JSONArray operInfo = JSONObject.parseObject(operResp.body()).getJSONArray("data");
        usageInfo.put("ioReadOPER", dealInfo(operInfo, "IO读次数"));
        usageInfo.put("ioWriteOPER", dealInfo(operInfo, "IO写次数"));
        return usageInfo;
    }

    @Override
    public JSONObject getHardwareCloudDetail(PlatformconfigDTO p, JSONObject tokenInfo, String id) {

        String url = p.getUrl() + (SangForApiConstant.GET_HARDWARE_CLOUD_INFO.replace("{id}", id));
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        HttpResponse res = HttpRequest.get(url)
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != res.getStatus()) {
            throw new RuntimeException("获取宿主机详情失败！");
        }
        return JSONObject.parseObject(res.body());
    }

    @Override
    public JSONObject getNetCloudInfos(PlatformconfigDTO p, JSONObject tokenInfo, String vmid) {
        String url = p.getUrl() + (SangForApiConstant.GET_VM_CLOUD_INFO.replace("{vmid}", vmid));
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        HttpResponse res = HttpRequest.get(url)
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != res.getStatus()) {
            throw new RuntimeException("网络信息获取失败！");
        }
//        JSONObject supInfo = JSONObject.parseObject(res.body()).getJSONObject("data").getJSONObject("ctrl").getJSONObject("hotplug_sup_info");
//        JSONObject result = new JSONObject();
//        if (supInfo != null) {
//            String osName = supInfo.getString("osname") + "(" + supInfo.getString("sysbit") + "-bit)";
//            if (supInfo.getInteger("net") > 0) {
//                String ip = "";
//                String mac = "";
//                for (int i = 0; i < supInfo.getInteger("net"); i++) {
//                    String[] netInfo = JSONObject.parseObject(res.body()).getJSONObject("data").getString("net" + i).split(",");
//                    for (String net : netInfo) {
//                        if (net.contains("virtio=")) {
//                            mac = mac + net.replace("virtio=", "") + ",";
//                        }
//                        if (net.contains("ip=")) {
//                            ip = ip + net.replace("ip=", "") + ",";
//                        }
//                    }
//                }
//                result.put("ip", StringUtils.isNotEmpty(ip) ? ip.substring(0, ip.length() - 1) : "");
//                result.put("mac", StringUtils.isNotEmpty(mac) ? mac.substring(0, mac.length() - 1) : "");
//            }
//            result.put("osName", osName);
//        }

        return JSONObject.parseObject(res.body()).getJSONObject("data");
    }

    @Override
    public JSONArray getStorageClouds(PlatformconfigDTO p, JSONObject tokenInfo) {
        String url = p.getUrl() + SangForApiConstant.GET_STORAGE_CLOUD_LIST;
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        HttpResponse res = HttpRequest.get(url)
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != res.getStatus()) {
            throw new RuntimeException("获取存储列表失败！");
        }
        return JSONObject.parseObject(res.body()).getJSONObject("data").getJSONArray("volumes");
    }

    @Override
    public Map getNodeSheetByHardId(PlatformconfigDTO platform, JSONObject tokenInfo, String id) {
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        HttpResponse res = HttpRequest.get(platform.getUrl() + SangForApiConstant.GET_NODE_SHEET + "?node=" + id + "&time_frame=" + "hour" + "&data_type=" + "net")
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
//            if (res.getStatus() != 200) {
//                throw new RuntimeException("获取主机网络信息失败！");
//            }
        JSONObject js = JSONObject.parseObject(dealInfo(JSONObject.parseObject(res.body()).getJSONArray("data"), "接收").get(0).toString());
        JSONObject fs = JSONObject.parseObject(dealInfo(JSONObject.parseObject(res.body()).getJSONArray("data"), "发送").get(0).toString());
        Map map = new HashMap();
        map.put("up", Convert.toInt(fs.get("val")));
        map.put("down", Convert.toInt(js.get("val")));
        return map;
    }

    @Override
    public JSONObject getStorageList(PlatformconfigDTO platform, JSONObject tokenInfo, String id) {
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        JSONObject usageInfo = new JSONObject();
        //cpu趋势
        HttpResponse disk = HttpRequest.get(platform.getUrl() + SangForApiConstant.GET_STORAGE_LIST_NODEID.replace("{nodeId}", id) )
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != disk.getStatus()) {
            throw new RuntimeException("获取宿主机CPU趋势失败！");
        }


        return JSONObject.parseObject(disk.body());
    }

    @Override
    public JSONArray getStoragesRealTime(PlatformconfigDTO p, JSONObject tokenInfo) {
        String url = p.getUrl() + SangForApiConstant.GET_STORAGE_CLOUD_LIST_REALTIME;
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        HttpResponse res = HttpRequest.get(url)
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != res.getStatus()) {
            throw new RuntimeException("获取存储REALTIME列表失败！");
        }
        return JSONObject.parseObject(res.body()).getJSONObject("data").getJSONArray("volumes");

    }

    @Override
    public JSONObject getClusterIp(PlatformconfigDTO p, JSONObject tokenInfo) {
        String url = p.getUrl() + SangForApiConstant.GET_CLUSTER_IP;
        String CSRFPreventionToken = tokenInfo.getString("CSRFPreventionToken");
        HttpResponse res = HttpRequest.get(url)
                .header("CSRFPreventionToken", CSRFPreventionToken)
                .execute();
        if (200 != res.getStatus()) {
            throw new RuntimeException("获取集群配置信息失败！");
        }
        return JSONObject.parseObject(res.body()).getJSONObject("data");
    }

    private JSONArray dealInfo(JSONArray sheet, String labe) {
        JSONArray valArr = new JSONArray();
        Long time = 0L;
        Long interval = 0L;
        for (int i = 0; i < sheet.size(); i++) {
            JSONObject item = JSONObject.parseObject(JSONUtil.toJsonStr(sheet.get(i)));
            if (item.getString("name").contains(labe)) {
                valArr = item.getJSONArray("data");
            }
            if (item.getString("name").contains("time")) {
                time = item.getJSONObject("data").getLong("start");
                interval = item.getJSONObject("data").getLong("interval");
            }
        }

        JSONArray valOfTime = new JSONArray();
        for (int i = 0; i < valArr.size(); i++) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("val", valArr.get(i));
            jsonObject.put("time", time);
            valOfTime.add(jsonObject);
            time += interval;
        }
        return valOfTime;
    }
}
