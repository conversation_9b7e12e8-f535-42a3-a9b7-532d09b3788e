package cn.iocoder.zj.module.collection.service.hardware;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import cn.iocoder.zj.module.collection.collect.http.MetricsFetcher;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.zstack.ZstackAccessTokenRedisDAO;
import cn.iocoder.zj.module.collection.handler.HttpClientWrapper;
import cn.iocoder.zj.module.collection.service.zstack.AbstractZstackApi;
import cn.iocoder.zj.module.collection.service.zstack.core.ZstackApiConstant;
import cn.iocoder.zj.module.collection.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.IdentityHashMap;
import java.util.Map;

/**
 * @ClassName : ZstackHardWare  //类名
 * @Description : 硬件设施实现类  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/26  9:13
 */
@Slf4j
@Service
public class ZstackHardWareServiceImpl extends AbstractZstackApi implements IZstackHardWareService {


    @Autowired
    ZstackAccessTokenRedisDAO zstackAccessTokenRedisDAO;
    @Resource
    PlatformRedisDAO platformRedisDAO;
    MetricsFetcher fetcher = new MetricsFetcher();
    private static final int METRIC_PERIOD = 20;
    private static final String CONTENT_TYPE = "application/json;charset=UTF-8";
    @Autowired
    private HttpClientWrapper httpClientWrapper;

    // 通用方法
    private String fetchMetricUtilization(String url, String token, String uuid, String metricName) {
        Map<String, String> header = new HashMap<>();
        header.put("Authorization", "OAuth " + token);
        header.put("Content-Type", CONTENT_TYPE);

        Map<String, Object> params = new HashMap<>();
        params.put("metricName", metricName);
        params.put("namespace", "ZStack/Host");

        if (StringUtil.isNotEmpty(uuid)) {
            params.put("labels", "HostUuid=" + uuid);
        }

        params.put("period", METRIC_PERIOD);

        String response = fetcher.sendRequest(url + ZstackApiConstant.GET_ZSTACK_METRICS, Method.GET, header, params, token,
                httpResponse -> {
                    if (httpResponse.getStatus() == 200) {
                        return httpResponse.body();
                    } else {
                        throw new RuntimeException("请求失败，状态码：" + httpResponse.getStatus());
                    }
                });

        return response;
    }

    // 使用通用方法的具体实现


    @Override
    public String fetchMetricData(String url, String token, String uuid, String metricName) {
        Map<String, String> header = new HashMap<>();
        header.put("Authorization", "OAuth " + token);
        header.put("Content-Type", CONTENT_TYPE);

        Map<String, Object> params = new HashMap<>();
        params.put("metricName", metricName);
        params.put("namespace", "ZStack/Host");

        if (StringUtil.isNotEmpty(uuid)) {
            params.put("labels", "HostUuid=" + uuid);
        }
        params.put("period", METRIC_PERIOD);

        String response = fetcher.sendRequest(url + ZstackApiConstant.GET_ZSTACK_METRICS, Method.GET, header, params, token,
                httpResponse -> {
                    if (httpResponse.getStatus() == 200) {
                        return httpResponse.body();
                    } else {
                        throw new RuntimeException("请求失败，状态码：" + httpResponse.getStatus());
                    }
                });

        return response;
    }

    /**
     * @description: 硬件设施cpu使用率
     * @param:
     * @return: java.lang.String
     * <AUTHOR>
     * @date: 2023/5/29 15:59
     */
    @Override
    public String cpuUsedUtilization(String url, String token, String uuid) {
//        Map<String, String> header = new HashMap<>();
//        IdentityHashMap<String, Object> p = new IdentityHashMap<>();
//        p.put("metricName", "CPUUsedUtilization");
//        p.put("namespace", "ZStack/Host");
//        if (StringUtil.isNotEmpty(uuid)){
//            p.put("labels","HostUuid="+uuid);
//        }
//        p.put("period",20);
//        header.put("Authorization", "OAuth " + token);
//        header.put("Content-Type", "application/json;charset=UTF-8");
//
//        HttpResponse result = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_METRICS)
//                .header(Header.AUTHORIZATION, "OAuth " + token)
//                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
//                .form(p)
//                .execute();
//        if (result.getStatus() != 200) {
//            throw new RuntimeException("getcpuUsedUtilization error");
//        }
//        return result.body();
        return fetchMetricUtilization(url, token, uuid, "CPUUsedUtilization");
    }

    @Override
    public String cpuAllUsedUtilization(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "CPUAllUsedUtilization");
    }

    @Override
    public String cpuUserUtilization(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "CPUUserUtilization");
    }

    @Override
    public String cpuWaitUtilization(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "CPUWaitUtilization");
    }

    @Override
    public String cpuSystemUtilization(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "CPUSystemUtilization");
    }

    @Override
    public String cpuIdleUtilization(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "CPUIdleUtilization");

    }


    /**
     * @description: 硬件设施 CPU平均使用率
     * <AUTHOR>
     * @date 2023/6/1 13:47
     * @version 1.0
     */
    @Override
    public String cpuAverageUsedUtilization(String url, String token, String uuid) {
//        Map<String, String> header = new HashMap<>();
//        Map<String, Object> p = new HashMap<>();
//        p.put("metricName", "CPUAverageUsedUtilization");
//        p.put("namespace", "ZStack/Host");
//        Map<String,Object> s= new HashMap<>();
//
//        if (StringUtil.isNotEmpty(uuid)){
//            p.put("labels","HostUuid="+uuid);
//        }
//        p.put("period",20);
//        MetricsFetcher fetcher = new MetricsFetcher();
//        String response = fetcher.sendRequest(url + ZstackApiConstant.GET_ZSTACK_METRICS, Method.GET, header, p, token,
//                httpResponse -> {
//                    // 假设响应是JSON格式，我们想要得到某个具体字段
//                    // 这里简化处理，实际使用时需要根据响应结构进行解析
//                    if (httpResponse.getStatus() == 200) {
//                        return httpResponse.body();
//                    } else {
//                        throw new RuntimeException("请求失败，状态码：" + httpResponse.getStatus());
//                    }
//                });
//
//        System.out.println(response);
//        return response;

        return fetchMetricUtilization(url, token, uuid, "CPUAverageUsedUtilization");
    }


    /**
     * @description: 硬件设施内存已用百分比
     * <AUTHOR>
     * @date 2023/6/1 13:46
     * @version 1.0
     */
    @Override
    public String memoryUsedInPercent(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "MemoryUsedInPercent");
    }

    /**
     * @description: 硬件设施 内存已用容量
     * <AUTHOR>
     * @date 2023/6/1 13:46
     * @version 1.0
     */
    @Override
    public String memoryUsedBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "MemoryUsedBytes");
    }

    /**
     * @description: 硬件设施 内存空闲容量
     * <AUTHOR>
     * @date 2023/6/1 13:51
     * @version 1.0
     */
    @Override
    public String memoryFreeBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "MemoryFreeBytes");
    }

    @Override
    public String diskReadBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "DiskReadBytes");
    }

    @Override
    public String diskWriteBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "DiskWriteBytes");
    }

    @Override
    public String diskReadOps(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "DiskReadOps");
    }

    @Override
    public String diskWriteOps(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "DiskWriteOps");
    }

    /**
     * @description: 硬件设施 磁盘已使用容量百分比
     * <AUTHOR>
     * @date 2023/6/2 11:11
     * @version 1.0
     */
    @Override
    public String diskUsedCapacityInPercent(String url, String token) {
        return fetchMetricUtilization(url, token, "", "DiskUsedCapacityInPercent");
    }

    /**
     * @description: 硬件设施 磁盘剩余容量百分比
     * <AUTHOR>
     * @date 2023/6/2 11:12
     * @version 1.0
     */
    @Override
    public String diskFreeCapacityInPercent(String url, String token) {
        return fetchMetricUtilization(url, token, "", "DiskFreeCapacityInPercent");
    }

    @Override
    public String diskFreeCapacityInBytes(String url, String token, String uuid) {
        return null;
    }

    /**
     * @description: 硬件设施 磁盘总容量
     * <AUTHOR>
     * @date 2023/6/2 11:12
     * @version 1.0
     */
    @Override
    public String diskTotalCapacityInBytes(String url, String token) {
        return fetchMetricUtilization(url, token, "", "DiskTotalCapacityInBytes");
    }

    /**
     * @description: 硬件设施 磁盘已使用容量
     * <AUTHOR>
     * @date 2023/6/2 11:13
     * @version 1.0
     */
    @Override
    public String diskUsedCapacityInBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "DiskUsedCapacityInBytes");
    }

    /**
     * @description: 硬件设施 全部磁盘剩余容量百分比
     * <AUTHOR>
     * @date 2023/6/2 15:22
     * @version 1.0
     */

    @Override
    public String diskAllFreeCapacityInPercent(String url, String token) {
//        Map<String, String> header = new HashMap<>();
//        IdentityHashMap<String, Object> p = new IdentityHashMap<>();
//        p.put("metricName", "DiskAllFreeCapacityInPercent");
//        p.put("namespace", "ZStack/Host");
//        p.put("period",20);
//        header.put("Authorization", "OAuth " + token);
//        header.put("Content-Type", "application/json;charset=UTF-8");
//
//        HttpResponse result = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_METRICS)
//                .header(Header.AUTHORIZATION, "OAuth " + token)
//                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
//                .form(p)
//                .execute();
//        if (result.getStatus() != 200) {
//            throw new RuntimeException("getdiskAllFreeCapacityInPercent error");
//        }
//        return result.body();

        return fetchMetricUtilization(url, token, "", "DiskAllFreeCapacityInPercent");
    }

    /**
     * @description: 硬件设施 全部磁盘已使用容量百分比
     * <AUTHOR>
     * @date 2023/6/2 15:22
     * @version 1.0
     */
    @Override
    public String diskAllUsedCapacityInPercent(String url, String token, String uuid) {
//        Map<String, String> header = new HashMap<>();
//        IdentityHashMap<String, Object> p = new IdentityHashMap<>();
//        p.put("metricName", "DiskAllUsedCapacityInPercent");
//        p.put("namespace", "ZStack/Host");
//
//        if (StringUtil.isNotEmpty(uuid)){
//            p.put("labels","HostUuid="+uuid);
//        }
//        p.put("period",20);
//        header.put("Authorization", "OAuth " + token);
//        header.put("Content-Type", "application/json;charset=UTF-8");
//
//        HttpResponse result = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_METRICS)
//                .header(Header.AUTHORIZATION, "OAuth " + token)
//                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
//                .form(p)
//                .execute();
//        if (result.getStatus() != 200) {
//            throw new RuntimeException("getdiskAllUsedCapacityInPercent error");
//        }
//        return result.body();

        return fetchMetricUtilization(url, token, uuid, "DiskAllUsedCapacityInPercent");
    }

    /**
     * @description: 硬件设施 全部磁盘已使用容量
     * <AUTHOR>
     * @date 2023/6/2 15:23
     * @version 1.0
     */
    @Override
    public String diskAllUsedCapacityInBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "DiskAllUsedCapacityInBytes");
    }

    /**
     * @description: 硬件设施 全部磁盘剩余容量
     * <AUTHOR>
     * @date 2023/6/2 15:23
     * @version 1.0
     */
    @Override
    public String diskAllFreeCapacityInBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "DiskAllFreeCapacityInBytes");
    }

    @Override
    public String diskZStackUsedCapacityInPercent(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "DiskZStackUsedCapacityInPercent");
    }

    @Override
    public String diskZStackUsedCapacityInBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "DiskZStackUsedCapacityInBytes");
    }

    @Override
    public String diskAllReadBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "DiskAllReadBytes");
    }

    @Override
    public String diskAllWriteBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "DiskAllWriteBytes");
    }

    /**
     * @description: 硬件设施 网卡入速度
     * <AUTHOR>
     * @date 2023/6/5 9:52
     * @version 1.0
     */
    @Override
    public String networkInBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "NetworkInBytes");
    }

    /**
     * @description: 硬件设施 全部网卡入速度
     * <AUTHOR>
     * @date 2023/6/5 9:53
     * @version 1.0
     */
    @Override
    public String networkAllInBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "NetworkAllInBytes");
    }

    /**
     * @description: 硬件设施 网卡出速度
     * <AUTHOR>
     * @date 2023/6/5 9:53
     * @version 1.0
     */
    @Override
    public String networkOutBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "NetworkOutBytes");
    }

    /**
     * @description: 硬件设施 全部网卡出速度
     * <AUTHOR>
     * @date 2023/6/5 9:54
     * @version 1.0
     */
    @Override
    public String networkAllOutBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "NetworkAllOutBytes");
    }

    /**
     * @description: 硬件设施 网卡入包数
     * <AUTHOR>
     * @date 2023/6/5 9:54
     * @version 1.0
     */
    @Override
    public String networkInPackets(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "NetworkInPackets");
    }

    /**
     * @description: 硬件设施 全部网卡入包数
     * <AUTHOR>
     * @date 2023/6/5 9:55
     * @version 1.0
     */
    @Override
    public String networkAllInPackets(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "NetworkAllInPackets");
    }

    /**
     * @description: 硬件设施 网卡出包数
     * <AUTHOR>
     * @date 2023/6/5 9:55
     * @version 1.0
     */
    @Override
    public String networkOutPackets(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "NetworkOutPackets");
    }

    /**
     * @description: 硬件设施 全部网卡出包数
     * <AUTHOR>
     * @date 2023/6/5 9:59
     * @version 1.0
     */
    @Override
    public String networkAllOutPackets(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "NetworkAllOutPackets");
    }

    @Override
    public String networkOutErrors(String url, String token, String uuid) {

        return fetchMetricUtilization(url, token, uuid, "NetworkOutErrors");
    }

    @Override
    public String networkInErrors(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "NetworkInErrors");
    }


    @Override
    public String hardWareInfo(String url, String token, String uuid) {
        String body = "";
        if (StringUtil.isNotEmpty(uuid)) {
            HttpRequest request = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_HOSTS + "/" + uuid)
                    .header(Header.AUTHORIZATION, "OAuth " + token)
                    .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8");
            // 执行请求（通过包装类）
            HttpResponse result = httpClientWrapper.execute(request);
            if (result.getStatus() != 200) {
                throw new RuntimeException("getcloudInfo error");
            }
            body = result.body();
        } else {
            HttpRequest request = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_HOSTS)
                    .header(Header.AUTHORIZATION, "OAuth " + token)
                    .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8");
            // 执行请求（通过包装类）
            HttpResponse result = httpClientWrapper.execute(request);
            if (result.getStatus() != 200) {
                throw new RuntimeException("getcloudInfo error");
            }
            body = result.body();
        }

        return body;
    }

    @Override
    public String clusters(String url, String token) {
        HttpRequest request = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_CLUSTERS)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8");
        HttpResponse result = httpClientWrapper.execute(request);
        if (result.getStatus() != 200) {
            throw new RuntimeException("getclusters error");
        }
        return result.body();
    }

    @Override
    public String diskAllReadOps(String url, String token) {
        Map<String, String> header = new HashMap<>();
        IdentityHashMap<String, Object> p = new IdentityHashMap<>();
        p.put("metricName", "DiskAllReadOps");
        p.put("namespace", "ZStack/Host");
        header.put("Authorization", "OAuth " + token);
        header.put("Content-Type", "application/json;charset=UTF-8");

        HttpResponse result = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_METRICS)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .form(p)
                .execute();
        if (result.getStatus() != 200) {
            throw new RuntimeException("getNetworkAllOutPackets error");
        }
        return result.body();
    }

    @Override
    public String diskAllWriteOps(String url, String token) {
        Map<String, String> header = new HashMap<>();
        IdentityHashMap<String, Object> p = new IdentityHashMap<>();
        p.put("metricName", "DiskAllWriteOps");
        p.put("namespace", "ZStack/Host");
        header.put("Authorization", "OAuth " + token);
        header.put("Content-Type", "application/json;charset=UTF-8");

        HttpResponse result = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_METRICS)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .form(p)
                .execute();
        if (result.getStatus() != 200) {
            throw new RuntimeException("getNetworkAllOutPackets error");
        }
        return result.body();
    }


    @Override
    public int getStatus(String url, String token, String uuid) {
        int body = 0;
        if (StringUtil.isNotEmpty(uuid)) {
            HttpRequest request = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_HOSTS + "/" + uuid)
                    .header(Header.AUTHORIZATION, "OAuth " + token)
                    .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8");

            HttpResponse result = httpClientWrapper.execute(request);
            if (result.getStatus() != 200) {
                throw new RuntimeException("getcloudInfo error");
            }
            body = result.getStatus();
        } else {
            HttpRequest request = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_HOSTS)
                    .header(Header.AUTHORIZATION, "OAuth " + token)
                    .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8");
            HttpResponse result = httpClientWrapper.execute(request);
            if (result.getStatus() != 200) {
                throw new RuntimeException("getcloudInfo error");
            }
            body = result.getStatus();
        }

        return body;
    }


}
