package cn.iocoder.zj.module.collection.collect.vmware;


import com.vmware.vim25.mo.Folder;
import com.vmware.vim25.mo.ServerConnection;
import com.vmware.vim25.mo.ServiceInstance;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.net.MalformedURLException;
import java.net.URL;
import java.rmi.RemoteException;
import java.security.cert.X509Certificate;

/**
 * @ClassName : ConnectedVimServiceBase  //类名
 * @Description :   操作vcenter的连接和断开，以及定义公共应用类
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/7/24  10:07
 */
public class ConnectedVimServiceBase {

    public ServiceInstance si = null;

    public void connect(String url, String userName, String passWord) {
        String vCenterURL = "https://" + url + "/sdk";
        String username = userName;
        String password = passWord;
        // Disable SSL certificate verification
        try {
            disableSSLVerification();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        try {
            si = new ServiceInstance(new URL(vCenterURL), username, password, true);
            si.getSessionManager().setLocale("zh-CN"); // set locale for the content of all API result.
            System.out.println("Connected to vCenter Server");
        } catch (RemoteException e) {
            throw new RuntimeException(e);
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }

    }


    /**
     * @description 断开vcenter链接
     * @date 2017年2月8日14:23:37
     * @version 1.1
     * <AUTHOR>
     */
    public void disconnect() {
        si.getServerConnection().logout();
    }

    /**
     * @description 获取链接URL
     * @date 2017年2月8日14:23:37
     * @version 1.1
     * <AUTHOR>
     */
    public URL getUrl() {
        ServerConnection serverConnection = si.getServerConnection();
        URL url = null;
        if (serverConnection != null) {
            url = serverConnection.getUrl();
        } else {
            return null;
        }
        return url;
    }

    // Method to disable SSL certificate verification
    private static void disableSSLVerification() throws Exception {
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }

                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                    }

                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                    }
                }
        };

        SSLContext sc = SSLContext.getInstance("SSL");
        sc.init(null, trustAllCerts, new java.security.SecureRandom());
        HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
        HttpsURLConnection.setDefaultHostnameVerifier((hostname, session) -> true);
    }
}
