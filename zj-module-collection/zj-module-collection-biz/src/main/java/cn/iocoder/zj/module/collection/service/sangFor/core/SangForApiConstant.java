package cn.iocoder.zj.module.collection.service.sangFor.core;

public class SangForApiConstant {
    public static final String SANG_FOR_API_PREFIX = "/vapi/json";
    public static final String SANG_FOR_CLOUD_API_PREFIX = "/vapi/extjs";
    public static final String GET_PUBLIC_KEY = SANG_FOR_API_PREFIX + "/public_key";
    public static final String LOGIN = SANG_FOR_API_PREFIX + "/access/ticket";
    public static final String GET_CLOUDS = SANG_FOR_API_PREFIX + "/cluster/vms";
    public static final String GET_CLOUDS_VMS = SANG_FOR_API_PREFIX + "/cluster/vms?group_type=group&sort_type=&desc=1&scene=resources_used";
    public static final String GET_VM_CONFIG = SANG_FOR_API_PREFIX + "/cluster/vm/{vmid}/config";
    public static final String GET_VM_CLOUD_INFO = SANG_FOR_CLOUD_API_PREFIX + "/cluster/vm/{vmid}/info";
    public static final String GET_HARDWARE = SANG_FOR_API_PREFIX + "/index/host_list";
    public static final String GET_HARDWARE_INFO = SANG_FOR_API_PREFIX + "/nodes/{id}/info";
    public static final String GET_HARDWARE_CLOUD_INFO = SANG_FOR_CLOUD_API_PREFIX + "/nodes/{id}/info";
    public static final String GET_V_NETWORK_LIST = SANG_FOR_API_PREFIX + "/network/allnet?group_type=group";
    public static final String GET_NETWORK_INFO = SANG_FOR_API_PREFIX + "/hci/sdn/ui/network-portal/evswitches/";
    public static final String GET_CPU_OVERUSE_CONFIG = SANG_FOR_API_PREFIX + "/vapimain/host/cpu/compute_overuse_config";
    public static final String GET_MEMORY_OVERUSE_CONFIG = SANG_FOR_API_PREFIX + "/vapimain/host/memory/compute_overuse_config";

    public static final String GET_STORAGE_USAGE = SANG_FOR_API_PREFIX + "/vtpstorage/lvinfo/sheet?storage={id}&time_frame=hour&type=capacity";
    public static final String GET_STORAGE_LIST = SANG_FOR_API_PREFIX + "/vtpstorage/storelist";
    public static final String GET_STORAGE_DETAIL = SANG_FOR_API_PREFIX + "/vtpstorage/lvinfo/{storageUuid}";
    public static final String GET_STORAGE_CLOUD_LIST = SANG_FOR_API_PREFIX + "/vs/vs_status/vs_get_volume_list";
    public static final String GET_STORAGE_CLOUD_LIST_REALTIME = SANG_FOR_API_PREFIX + "/vs/vs_status/vs_get_volume_list_realtime";
    public static final String GET_VM_DETAIL = SANG_FOR_API_PREFIX + "/cluster/vm/{vmid}/info";
    public static final String GET_HOST_DETAIL = "/vapi/extjs/cluster/node_sheet?time_frame=hour&node={nodeId}&data_type=";

    public static final String GET_STORAGE_LIST_NODEID = "/vapi/extjs/nodes/{nodeId}/storage_list";
    public static final String GET_NODE_SHEET = SANG_FOR_CLOUD_API_PREFIX + "/cluster/node_sheet";
    public static final String GET_CLUSTER_IP = SANG_FOR_API_PREFIX + "/cluster/cluster_ip";
    public static final String GET_CLOUDS_BY_HOST_ID = SANG_FOR_API_PREFIX + "/cluster/vms?snode={host_id}&nostatus=1";
    public static final String GET_IFACES = SANG_FOR_API_PREFIX + "/cluster/network/ifaces";

    //获取主存储绑定的物理机列表
    public static final String GET_VTPSTORAGE_DETAIL = SANG_FOR_API_PREFIX + "/vtpstorage/detail?storageid={id}&sort=host_name&direct=ASC&start=0&limit=100";

    //获取主机快照列表
//    public static final String GET_VM_SNAPSHOT_LIST = SANG_FOR_API_PREFIX + "/cluster/vm/{vmid}/snapshots";
    public static final String GET_VM_SNAPSHOT_LIST = SANG_FOR_API_PREFIX + "/vapimain/vm/{vmid}/snapshots";

}
