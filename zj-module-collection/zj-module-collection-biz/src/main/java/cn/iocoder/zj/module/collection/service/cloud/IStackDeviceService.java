package cn.iocoder.zj.module.collection.service.cloud;

import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonArray;

import java.util.Map;

public interface IStackDeviceService {

    JSONArray getClouds(PlatformconfigDTO p);

    JSONArray getCloudsByHost(PlatformconfigDTO p,String hostname);

    JSONArray getHardware(PlatformconfigDTO p);

    JSONArray getStorages(PlatformconfigDTO p);

    JSONArray getNetworks(PlatformconfigDTO p);

    JSONArray getVmDetail(PlatformconfigDTO p, String vmid);

    JSONArray getRelaTimeData(PlatformconfigDTO p, String uuid,Boolean flag);

    JSONObject getVmuse(PlatformconfigDTO p, String uuid);

    JSONArray getPerformanceData(PlatformconfigDTO p, String uuid, String itemName,Boolean flag);

    JSONArray getNetworks3(PlatformconfigDTO p);

    JSONArray getNetDhcp(PlatformconfigDTO p, String id);

    JSONArray getCloudNics(PlatformconfigDTO p, String instance_uuid);

    JSONArray getEips(PlatformconfigDTO p, String instance_uuid);

    JSONArray getSecgroup(PlatformconfigDTO p);

    JSONArray getImageList(PlatformconfigDTO p);

    JSONArray getVolumeSnapshotList(PlatformconfigDTO p);

    JSONArray getInstanceSnapshotList(PlatformconfigDTO p);

    JSONArray getAllZone(PlatformconfigDTO p);
}
