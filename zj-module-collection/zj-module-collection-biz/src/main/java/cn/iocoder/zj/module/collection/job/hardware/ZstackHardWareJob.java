package cn.iocoder.zj.module.collection.job.hardware;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.iocoder.zj.framework.common.util.collection.CollectionUtils;
import cn.iocoder.zj.module.collection.dal.dataobject.zstack.AlarmConfigInfo;
import cn.iocoder.zj.module.collection.dal.dataobject.zstack.AlarmHostRelationInfo;
import cn.iocoder.zj.module.collection.dal.dataobject.zstack.ZstackLoginInfo;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.zstack.AlarmInfoRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.zstack.ZstackAccessTokenRedisDAO;
import cn.iocoder.zj.module.collection.framework.influx.config.InfluxDBTemplate;
import cn.iocoder.zj.module.collection.service.cloud.IZstackCloudService;
import cn.iocoder.zj.module.collection.service.hardware.IZstackHardWareService;
import cn.iocoder.zj.module.collection.service.storage.ZstackStorageService;
import cn.iocoder.zj.module.collection.service.zstack.AbstractZstackApi;
import cn.iocoder.zj.module.collection.service.zstack.core.ZstackApiConstant;
import cn.iocoder.zj.module.collection.util.Sha256;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import cn.iocoder.zj.module.monitor.api.hardware.HardWareInfoApi;
import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.hardwarenic.HardWareNicApi;
import cn.iocoder.zj.module.monitor.api.hardwarenic.HardWareNicRespDTO;
import cn.iocoder.zj.module.monitor.api.hardwarestorage.HardWareStorageRespDTO;
import cn.iocoder.zj.module.monitor.api.topology.TopologyApi;
import cn.iocoder.zj.module.monitor.api.topology.dto.TopologyDTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.util.JsonUtil;
import org.frameworkset.elasticsearch.boot.BBossESStarter;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.zstack.sdk.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @ClassName : ZstackHardWareJob  //类名
 * @Description : 硬件设施定时任务  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/6/5  11:15
 */
@Component
@Slf4j
public class ZstackHardWareJob {

    @Resource
    InfluxDBTemplate influxDBTemplate;

    @Autowired
    private BBossESStarter bbossESStarter;

    @Autowired
    private AlarmConfigApi alarmConfigApi;
    @Resource
    ZstackAccessTokenRedisDAO zstackAccessTokenRedisDAO;
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource(name = "zstackHardWareServiceImpl")
    AbstractZstackApi zstackApi;

    @Resource
    IZstackCloudService iZstackCloudService;
    @Resource
    IZstackHardWareService iZstackHardWareService;
    @Resource
    HardWareInfoApi hardWareInfoApi;
    @Resource
    HardWareNicApi hardWareNicApi;
    @Resource
    PlatformRedisDAO platformRedisDAO;

    @Resource
    AlarmInfoRedisDAO alarmInfoRedisDAO;

    @Resource
    ZstackStorageService zstackStorageService;

    @Resource
    TopologyApi topologyApi;
    private DateTimeFormatter dtf2 = DateTimeFormatter.ofPattern("MMM dd, yyyy h:mm:ss a", Locale.US);


    public void zstackHardWareJob() {
        execute();
        hostByInfos();
    }

    // 从缓存中获取并更新平台配置
    private List<PlatformconfigDTO> fetchAndCachePlatformConfig(PlatformconfigApi platformconfigApi, PlatformRedisDAO platformRedisDAO) {
        List<PlatformconfigDTO> config = platformRedisDAO.get("platform");
        if (config == null) {
            config = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform", config);
        }
        return config;
    }


    // 通用方法用于获取硬件指标并构建批处理数据点
    private void fetchMetricAndBuildPoints(String url, String token, PlatformconfigDTO platform, String uuid, String metricName, String measurementName, BatchPoints batchPoints, String name, JSONObject jsonObject, String[] type) {
        String responseData = zstackApi.fetchMetricData(url, token, uuid, metricName);
        JSONArray data = JSONObject.parseObject(responseData).getJSONArray("data");
        List<Map<String, Object>> metricMaps = parseJSONArray(data, "value", "value");
        // 检查当前指标是否在 `type` 数组中
        boolean types = false;
        if (Arrays.asList(type).contains(metricName)) {
            types = true;
        }

        for (Map<String, Object> map : metricMaps) {
            String hostType = metricName;
            if (types) {
                hostType = Convert.toStr(map.get("type"));
            }
            Point point = Point.measurement(measurementName)
                    .tag("uuid", uuid)
                    .tag("metricName", metricName)
                    .addField("productsName", name) // 假定硬件名称和平台名称一致
                    .tag("regionId", platform.getRegionId().toString())
                    .addField("regionName", platform.getRegionName())
                    .tag("host_type", hostType)
                    .tag("platformId", platform.getId().toString())
                    .addField("platformName", platform.getName())
                    .addField("host_metricName", metricName)
                    .addField("type", hostType)
                    .addField("value", Convert.toBigDecimal(map.get("value")))
                    .time(Convert.toLong(map.get("time")) + 20, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }
    }


    @XxlJob("hardWareInfo")
    public void execute() {
        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                .filter(dto -> "zstack".equals(dto.getTypeCode()))
                .collect(Collectors.toList());
        if (filteredList.isEmpty()) return;

        List<AlarmHostRelationInfo> alarmHostRealtionList = JSON.parseArray(alarmInfoRedisDAO.getRelation("alarm_host_relation"), AlarmHostRelationInfo.class);
        List<AlarmConfigInfo> alarmConfigInfoList = JSON.parseArray(alarmInfoRedisDAO.getConfig("alarm_config"), AlarmConfigInfo.class);
        Map<Long, AlarmConfigInfo> alarmConfigMap = new HashMap<>();
        Map<String, AlarmHostRelationInfo> alarmConfigRelationMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(alarmConfigInfoList)) {
            alarmConfigInfoList.removeIf(item -> item.getDeleted() == 1);
            alarmConfigMap = CollectionUtils.convertMap(alarmConfigInfoList, AlarmConfigInfo::getId);
        }
        if (ObjectUtil.isNotEmpty(alarmHostRealtionList)) {
            alarmHostRealtionList.removeIf(item -> item.getStatus() == 1);
            alarmConfigRelationMap = CollectionUtils.convertMap(alarmHostRealtionList, AlarmHostRelationInfo::getHostUuid);
        }
        for (PlatformconfigDTO platformconfigDTO : filteredList) {
            try {
                handlePlatformConfig(platformconfigDTO, alarmConfigRelationMap, alarmConfigMap);
            } catch (Exception e) {
                // 记录错误日志，包含平台信息
                log.error("处理平台 [{}] 失败: {}",
                        platformconfigDTO.getName(), e.getMessage(), e);
                // 继续处理下一个平台
                continue;
            }
        }
    }

    private void handlePlatformConfig(PlatformconfigDTO p, Map<String, AlarmHostRelationInfo> alarmConfigRelationMap, Map<Long, AlarmConfigInfo> alarmConfigMap) {
        List<HardWareRespCreateReqDTO> hardWareRespCreateReqDTOS = new ArrayList<>();
        List<HardWareNicRespDTO> nicList = new ArrayList<>();

        if (zstackAccessTokenRedisDAO.get("zstack:" + p.getId()) != null) {

            String token = zstackAccessTokenRedisDAO.get("zstack:" + p.getId()).getUuid();
            if (StrUtil.isEmpty(token)) {
                XxlJobHelper.handleFail("zstackHardWare中token失效");
            }
            // 拿token去换接口状态 监测接口是否正常
            int isstatus = iZstackHardWareService.getStatus(p.getUrl(), token, "");
            if (isstatus != 200) {
                loginInfo();
                XxlJobHelper.handleFail("token获取失败");
                return;
            }

            // 初始化 ZStack SDK 配置
            try {
                String processedUrl = removeProtocolAndPort(p.getUrl());
                String port = extractPort(p.getUrl());
                ZSClient.configure(
                        new ZSConfig.Builder()
                                .setHostname(processedUrl)
                                .setPort(Convert.toInt(port))
                                .setContextPath("zstack")
                                .build()
                );
            } catch (Exception e) {
                log.error("ZStack SDK 调用失败: {}", e.getMessage(), e);
                return;
            }


                //查询区域列表
                QueryZoneAction action = new QueryZoneAction();
//                action.conditions = Arrays.asList("state=Enabled");
                action.sessionId = token;
                QueryZoneAction.Result res = action.call();
            List regionList = res.value.inventories;
            log.info("regionList------------------------>{}",regionList);


//                //cpu超分率（全局）
//                QueryGlobalConfigAction configAction = new QueryGlobalConfigAction();
//                configAction.conditions = Arrays.asList("name=cpu.overProvisioning.ratio");
//                configAction.sessionId = token;
//                QueryGlobalConfigAction.Result configRes = configAction.call();
//                List<Object> cpuInventories = res.value.inventories;
//                log.info("value------------------------>{}",cpuInventories);





            // 1: 处理内存
            List<Map<String, Object>> memory = new ArrayList<>();
            //  处理内存使用率 ，内存已用百分比
            String memoryUsed = zstackApi.memoryUsedInPercent(p.getUrl(), token, "");
            JSONArray memorys = JSONObject.parseObject(memoryUsed).getJSONArray("data");
            List<Map<String, Object>> maps = parseJSONArray(memorys, "memoryUsed", "value");
            maps.stream().collect(Collectors.groupingBy(map -> map.get("HostUuid"))).forEach((k, v) -> {
                Map<String, Object> map = new HashMap();
                map.put("uuid", k);
                map.put("memoryUsed", v.get(v.size() - 1).get("memoryUsed"));
                memory.add(map);
            });


            // 2： 处理集群名称
            List<Map<String, Object>> clusters = new ArrayList<>();
            String clusters1 = iZstackHardWareService.clusters(p.getUrl(), token);
            JSONArray clus = JSONObject.parseObject(clusters1).getJSONArray("inventories");
            for (int i = 0; i < clus.size(); i++) {
                JSONObject jsonObject = clus.getJSONObject(i);
                if ("KVM".equals(jsonObject.getString("hypervisorType"))) {
                    Map<String, Object> map = new HashMap();
                    map.put("uuid", jsonObject.getString("uuid"));
                    map.put("clustersName", jsonObject.getString("name"));
                    clusters.add(map);
                }
            }

            //三级网络列表
            QueryL3NetworkAction l3action = new QueryL3NetworkAction();
            l3action.sessionId = token;
            QueryL3NetworkAction.Result l3res = l3action.call();
            List<L3NetworkInventory> l3Inventories = l3res.value.inventories;




            // 根据不同租户查询不同用户下主机信息
            String d = iZstackHardWareService.hardWareInfo(p.getUrl(), token, "");
            JSONArray jsonArray = JSONObject.parseObject(d).getJSONArray("inventories");
            for (int i = 0; i < jsonArray.size(); i++) {
                HardWareRespCreateReqDTO hardWareRespCreateReqDTO = new HardWareRespCreateReqDTO();
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                if ("KVM".equals(jsonObject.getString("hypervisorType"))) {
                    String clusterName = "";
                    BigDecimal cpu_useds = new BigDecimal(0);
                    BigDecimal bandwidth_upstream = new BigDecimal(0);
                    BigDecimal bandwidth_downstream = new BigDecimal(0);
                    BigDecimal memory_used = new BigDecimal(0);
                    BigDecimal packet_rate = new BigDecimal(0);
                    BigDecimal diskUsed = new BigDecimal(0);
                    BigDecimal diskUsedBytes = new BigDecimal(0);
                    BigDecimal diskFreeBytes = new BigDecimal(0);
                    BigDecimal totalDiskCapacity = new BigDecimal(0);
                    String uuid = jsonObject.getString("uuid");
                    String name = jsonObject.getString("name");
                    String state = jsonObject.getString("state");
                    String ip = jsonObject.getString("managementIp");
                    String status = jsonObject.getString("status");
                    //区域
                    String zoneUuid = jsonObject.getString("zoneUuid");
                    hardWareRespCreateReqDTO.setManager(p.getName());
                    hardWareRespCreateReqDTO.setAvailableManager("-");
                    for (Object item : regionList) {
                        if (item instanceof org.zstack.sdk.ZoneInventory) {
                            org.zstack.sdk.ZoneInventory zone = (org.zstack.sdk.ZoneInventory) item;
                            if (zone.getUuid().equals(zoneUuid)) {
                                hardWareRespCreateReqDTO.setAvailableManager(zone.getName());
                                break;
                            }
                        }
                    }

                    //查询集群下所有二级网络
                    QueryL2NetworkAction networkAction = new QueryL2NetworkAction();
                    String clusterUuid = jsonObject.getString("clusterUuid");
                    networkAction.conditions = Arrays.asList("cluster.uuid="+clusterUuid);
                    networkAction.sessionId = token;
                    QueryL2NetworkAction.Result networkRes = networkAction.call();

                    List<L2NetworkInventory> l2Inventories = networkRes.value.inventories;


                    //添加网络信息
                    for (L2NetworkInventory l2Inventory : l2Inventories) {
                        for (L3NetworkInventory l3Inventory : l3Inventories) {
                            if (l3Inventory.getL2NetworkUuid().equals(l2Inventory.getUuid())){
                                HardWareNicRespDTO nicRespDTO = new HardWareNicRespDTO();
                                nicRespDTO.setUuid(l3Inventory.getUuid());
                                nicRespDTO.setHardwareUuid(uuid);
                                nicRespDTO.setNetworkType("管理口");
                                nicRespDTO.setIpAddresses("-");
                                nicRespDTO.setIpSubnet(l3Inventory.getName());
                                nicRespDTO.setL2NetworkUuid(l2Inventory.getUuid());
                                nicRespDTO.setL2NetworkName(l2Inventory.getName());
                                nicRespDTO.setState(true);
                                nicRespDTO.setPlatformId(p.getId());
                                nicRespDTO.setPlatformName(p.getName());
                                nicList.add(nicRespDTO);

                            }
                        }
                    }
                    log.info("nicList:{}",nicList);


                    // =========================型号相关========================================
                    hardWareRespCreateReqDTO.setModel("-");
                    hardWareRespCreateReqDTO.setBrandName("Zstack");
                    hardWareRespCreateReqDTO.setSerialNumber("-");
                    //cpu类型（描述）
                    hardWareRespCreateReqDTO.setCpuType("-");
                    hardWareRespCreateReqDTO.setIpmi("-");
                    hardWareRespCreateReqDTO.setManufacturer("-");



                    //cpu总量
                    Long totalCpuCapacity = jsonObject.getLong("totalCpuCapacity");
                    //cpu可用量
                    Long availableCpuCapacity = jsonObject.getLong("availableCpuCapacity");
                    Integer cpuSockets = jsonObject.getInteger("cpuSockets");
                    String architecture = jsonObject.getString("architecture");
                    Integer cpuNum = jsonObject.getInteger("cpuNum");
                    //当前cpu超分比率
                    BigDecimal cpuCommitRate = new BigDecimal(totalCpuCapacity-availableCpuCapacity).divide(new BigDecimal(cpuNum), 3, BigDecimal.ROUND_HALF_UP);

                    //内存总容量
                    Long totalMemoryCapacity = jsonObject.getLong("totalMemoryCapacity");
                    //内存可用容量
                    Long availableMemoryCapacity = jsonObject.getLong("availableMemoryCapacity");

                    //当前内存超售比率
                    BigDecimal memCommitRate = new BigDecimal(totalMemoryCapacity-availableMemoryCapacity).divide(new BigDecimal(totalMemoryCapacity), 3, BigDecimal.ROUND_HALF_UP);
                    LocalDateTime createDate = LocalDateTime.parse(jsonObject.getString("createDate"), dtf2);
                    for (Map map : clusters) {
                        if (StringUtil.toString(map.get("uuid")).equals(clusterUuid)) {
                            clusterName = StringUtil.toString(map.get("clustersName"));
                        }
                    }

                    // =========================CPU相关========================================
                    String avercpus = zstackApi.cpuAverageUsedUtilization(p.getUrl(), token, uuid);
                    JSONArray cau = JSONObject.parseObject(avercpus).getJSONArray("data");
                    if (cau.size() > 0) {
                        int lastIndex = cau.size() - 1;
                        JSONObject cauObj = cau.getJSONObject(lastIndex);
                        cpu_useds = cauObj.getBigDecimal("value");
                    }
                    //查询cpu超分(集群，如果没有，取总的)
                    GetResourceConfigAction cpuAction = new GetResourceConfigAction();
                    cpuAction.category = "host";
                    cpuAction.name = "cpu.overProvisioning.ratio";
                    cpuAction.resourceUuid = clusterUuid;
                    cpuAction.sessionId = token;
                    GetResourceConfigAction.Result cpuRes = cpuAction.call();
                    BigDecimal cpuOversold =  new BigDecimal(cpuRes.value.value);
                    hardWareRespCreateReqDTO.setCpuOverPercent(cpuOversold);
                    log.info("res------------------------>{}",res);

                    // =============================磁盘相关====================================
                    //  磁盘已使用容量百分比
                    String usedCapacityInPercent = zstackApi.diskAllUsedCapacityInPercent(p.getUrl(), token, uuid);
                    if (usedCapacityInPercent != null && !usedCapacityInPercent.isEmpty()) {
                        JSONObject jsonObject2 = JSONObject.parseObject(usedCapacityInPercent);
                        if (jsonObject2 != null && jsonObject2.containsKey("data")) {
                            JSONArray usedCapacityArray = jsonObject2.getJSONArray("data");
                            if (usedCapacityArray != null && usedCapacityArray.size() > 0) {
                                // 获取最后一条JSON数据
                                int lastIndex = usedCapacityArray.size() - 1;
                                JSONObject usedCapacity = usedCapacityArray.getJSONObject(lastIndex);
                                if (usedCapacity != null && usedCapacity.containsKey("value")) {
                                    diskUsed = usedCapacity.getBigDecimal("value");
                                }
                            }
                        }
                    }

                    // 磁盘剩余容量
                    String freeBytes = zstackApi.diskAllFreeCapacityInBytes(p.getUrl(), token, uuid);
                    JSONArray freeBytesArray = JSONObject.parseObject(freeBytes).getJSONArray("data");
                    if (freeBytesArray.size() > 0) {
                        // 获取最后一条JSON数据
                        int freeBytesIndex = freeBytesArray.size() - 1;
                        JSONObject freeBytesCapacity = freeBytesArray.getJSONObject(freeBytesIndex);
                        diskFreeBytes = freeBytesCapacity.getBigDecimal("value");
                    }


                    // 磁盘已使用容量
                    String usedBytes = zstackApi.diskAllUsedCapacityInBytes(p.getUrl(), token, uuid);
                    JSONArray usedBytesArray = JSONObject.parseObject(usedBytes).getJSONArray("data");
                    if (usedBytesArray.size() > 0) {
                        // 获取最后一条JSON数据
                        int usedBytesIndex = usedBytesArray.size() - 1;
                        JSONObject usedBytesCapacity = usedBytesArray.getJSONObject(usedBytesIndex);
                        diskUsedBytes = usedBytesCapacity.getBigDecimal("value");

                    }

                    // =============================网络相关====================================
                    // 网络下行
                    String networkOut = zstackApi.networkAllOutBytes(p.getUrl(), token, uuid);
                    JSONArray networkOutArray = JSONObject.parseObject(networkOut).getJSONArray("data");
                    if (networkOutArray.size() > 0) {
                        JSONObject maxValObj = null;
                        double maxVal = Double.MIN_VALUE;

                        // 遍历数组
                        for (int id = 0; id < networkOutArray.size(); id++) {
                            JSONObject currentObj = networkOutArray.getJSONObject(id);
                            double currentValue = currentObj.getDouble("value");

                            // 如果当前值大于追踪的最大值，更新最大值和对应的JSONObject
                            if (currentValue > maxVal) {
                                maxVal = currentValue;
                                maxValObj = currentObj;
                            }
                        }
                        if (maxValObj != null && !maxValObj.isEmpty()) {
                            bandwidth_downstream = maxValObj.getBigDecimal("value");
                        }
                    }
                    // 网络上行
                    String networkIn = zstackApi.networkAllInBytes(p.getUrl(), token, uuid);
                    JSONArray networkInArray = JSONObject.parseObject(networkIn).getJSONArray("data");
                    if (networkInArray.size() > 0) {


                        JSONObject maxValObj = null;
                        double maxVal = Double.MIN_VALUE;

                        // 遍历数组
                        for (int id = 0; id < networkInArray.size(); id++) {
                            JSONObject currentObj = networkInArray.getJSONObject(id);
                            double currentValue = currentObj.getDouble("value");

                            // 如果当前值大于追踪的最大值，更新最大值和对应的JSONObject
                            if (currentValue > maxVal) {
                                maxVal = currentValue;
                                maxValObj = currentObj;
                            }
                        }
                        if (maxValObj != null && !maxValObj.isEmpty()) {
                            bandwidth_upstream = maxValObj.getBigDecimal("value");
                        }
                    }

                    // 收包数
                    String networkPacKets = zstackApi.networkAllInPackets(p.getUrl(), token, uuid);
                    JSONArray networkPacKetsArray = JSONObject.parseObject(networkPacKets).getJSONArray("data");
                    if (networkPacKetsArray.size() > 0) {

                        // 初始化追踪最大值的变量
                        JSONObject maxValObj = null;
                        double maxVal = Double.MIN_VALUE;

                        // 遍历数组
                        for (int id = 0; id < networkPacKetsArray.size(); id++) {
                            JSONObject currentObj = networkPacKetsArray.getJSONObject(id);
                            double currentValue = currentObj.getDouble("value");

                            // 如果当前值大于追踪的最大值，更新最大值和对应的JSONObject
                            if (currentValue > maxVal) {
                                maxVal = currentValue;
                                maxValObj = currentObj;
                            }
                        }
                        if (maxValObj != null && !maxValObj.isEmpty()) {
                            packet_rate = maxValObj.getBigDecimal("value");
                        }
                    }

                    // 内存
                    for (Map map : memory) {
                        if (StringUtil.toString(map.get("uuid")).equals(uuid)) {
                            memory_used = (BigDecimal) map.get("memoryUsed");
                        }
                    }

                    GetResourceConfigAction memoryAction = new GetResourceConfigAction();
                    memoryAction.category = "mevoco";
                    memoryAction.name = "overProvisioning.memory";
                    memoryAction.resourceUuid = clusterUuid;
                    memoryAction.sessionId = token;
                    GetResourceConfigAction.Result memoryRes = memoryAction.call();
                    //内存超售比
                    BigDecimal memoryOversold =  new BigDecimal(memoryRes.value.value);
                    hardWareRespCreateReqDTO.setMemoryOverPercent(memoryOversold);
                    log.info("res------------------------>{}",memoryRes);

                    //reservedMemory 系统预留
                    GetResourceConfigAction reservedAction = new GetResourceConfigAction();
                    reservedAction.category = "kvm";
                    reservedAction.name = "reservedMemory";
                    reservedAction.resourceUuid = clusterUuid;
                    reservedAction.sessionId = token;
                    GetResourceConfigAction.Result reservedActionRes = reservedAction.call();
                    String value = reservedActionRes.value.value;
                    BigDecimal reserved = new BigDecimal(value.replace("G", "")).multiply(new BigDecimal(1024*1024*1024)); // 移除单位，只保留数字部分
                    hardWareRespCreateReqDTO.setReservedMemory(reserved);
                    log.info("res------------------------>{}",reservedActionRes);

//                    查询主机网络
//                    QueryHostAction networkAction = new QueryHostAction();
//                    networkAction.conditions = Arrays.asList(String.format("uuid=%s", uuid));
//                    QueryHostAction.Result result = networkAction.call();
//                    HostInventory networkHost = (HostInventory) result.value.getInventories().get(0);
//                    log.info("regionList------------------------>{}",networkHost);

                    QueryHostNetworkInterfaceAction macAction = new QueryHostNetworkInterfaceAction();
                    macAction.conditions = Arrays.asList("hostUuid="+uuid);
                    macAction.sessionId = token;
                    QueryHostNetworkInterfaceAction.Result macRes = macAction.call();
                    log.info("macRes------------------------>{}",macRes);

                    String resourceConfig = iZstackCloudService.resourceConfigList(p.getUrl(), token, clusterUuid);
                    Long retainMem = 0L;
                    if (StrUtil.isNotEmpty(resourceConfig)) {
                        try {
                            String mem = JSONObject.parseObject(resourceConfig).getString("value");
                            if (StrUtil.isNotEmpty(mem)) {
                                retainMem = StringUtil.convertToKB(mem);
                            }
                        } catch (Exception e) {
                            log.info("获取资源物理机保留内存失败！");
                        }
                    } else {
                        String globalConfig = iZstackCloudService.queryGlobalConfig(p.getUrl(), token);

                        JSONArray reservedMemory = JSONObject.parseObject(globalConfig).getJSONArray("inventories");
                        List<Map> listObjectSec = JSONObject.parseArray(reservedMemory.toJSONString(), Map.class);

                        for (Map map : listObjectSec) {
                            if (map.get("name").equals("reservedMemory")) {
                                if (map.get("value") != null) {
                                    retainMem = StringUtil.convertToKB(Convert.toStr(map.get("value")));
                                    break;
                                }
                            }
                        }
                    }
                    hardWareRespCreateReqDTO.setDeleted(0);
                    hardWareRespCreateReqDTO.setUuid(uuid);
                    hardWareRespCreateReqDTO.setName(name);
                    hardWareRespCreateReqDTO.setState(state);
                    hardWareRespCreateReqDTO.setIp(ip);
                    hardWareRespCreateReqDTO.setStatus(status);
                    hardWareRespCreateReqDTO.setClusterUuid(clusterUuid);
                    hardWareRespCreateReqDTO.setClusterName(clusterName);
                    hardWareRespCreateReqDTO.setTotalCpuCapacity(totalCpuCapacity);
                    hardWareRespCreateReqDTO.setAvailableCpuCapacity(availableCpuCapacity);
                    hardWareRespCreateReqDTO.setCpuSockets(cpuSockets);
                    hardWareRespCreateReqDTO.setArchitecture(architecture);
                    hardWareRespCreateReqDTO.setCpuNum(cpuNum);
                    hardWareRespCreateReqDTO.setCpuCommitRate(cpuCommitRate);
                    hardWareRespCreateReqDTO.setMemoryCommitRate(memCommitRate);
                    hardWareRespCreateReqDTO.setTotalMemoryCapacity(totalMemoryCapacity);
                    //TODO 暂无法获得物理内存的保留值
                    BigDecimal availableMem = NumberUtil.sub(Convert.toBigDecimal(availableMemoryCapacity), retainMem);
                    if (availableMem.signum() < 0) {
                        availableMem = new BigDecimal(0);
                    }
                    hardWareRespCreateReqDTO.setAvailableMemoryCapacity(Convert.toLong(availableMem));
                    hardWareRespCreateReqDTO.setBandwidthUpstream(bandwidth_upstream);
                    hardWareRespCreateReqDTO.setBandwidthDownstream(bandwidth_downstream);
                    hardWareRespCreateReqDTO.setMemoryUsed(memory_used);
                    hardWareRespCreateReqDTO.setPacketRate(packet_rate);
                    hardWareRespCreateReqDTO.setCpuUsed(cpu_useds);
                    hardWareRespCreateReqDTO.setDiskUsed(diskUsed);
                    hardWareRespCreateReqDTO.setDiskUsedBytes(diskUsedBytes);
                    hardWareRespCreateReqDTO.setDiskFreeBytes(diskFreeBytes);
                    hardWareRespCreateReqDTO.setTotalDiskCapacity(diskUsedBytes.add(diskFreeBytes));
                    hardWareRespCreateReqDTO.setTenantId(p.getTenantId());
                    hardWareRespCreateReqDTO.setRegionId(p.getRegionId());
                    hardWareRespCreateReqDTO.setPlatformId(p.getId());
                    hardWareRespCreateReqDTO.setPlatformName(p.getName());
                    hardWareRespCreateReqDTO.setDeleted(0);
                    hardWareRespCreateReqDTO.setTypeName("zstack");
                    hardWareRespCreateReqDTO.setCreateTime(createDate);
                    hardWareRespCreateReqDTOS.add(hardWareRespCreateReqDTO);
                }
            }
        }

        if (!nicList.isEmpty()){
            List<HardWareNicRespDTO> oldNicList=hardWareNicApi.getHardwareNicByPlatformId(p.getId()).getCheckedData();

            if (oldNicList.isEmpty()) {
                hardWareNicApi.adds(nicList);
            } else {
                // 修改Map的key为uuid+hardwareUuid的组合
                Map<String, HardWareNicRespDTO> existingHardwareMap = oldNicList.stream()
                        .collect(Collectors.toMap(
                                hardwareNic -> hardwareNic.getUuid() + "_" + hardwareNic.getHardwareUuid(),
                                hardwareNic -> hardwareNic
                        ));

                List<HardWareNicRespDTO> newEntries = new ArrayList<>();
                List<HardWareNicRespDTO> updatedEntries = new ArrayList<>();
                // 修改删除条件，同时比对uuid和hardwareUuid
                List<HardWareNicRespDTO> deleteEntries = oldNicList.stream()
                        .filter(item -> !nicList.stream()
                                .anyMatch(newItem ->
                                        newItem.getUuid().equals(item.getUuid()) &&
                                                newItem.getHardwareUuid().equals(item.getHardwareUuid())
                                ))
                        .collect(Collectors.toList());

                for (HardWareNicRespDTO hardWareNicRespDTO : nicList) {
                    // 使用组合key来查找
                    String compositeKey = hardWareNicRespDTO.getUuid() + "_" + hardWareNicRespDTO.getHardwareUuid();
                    HardWareNicRespDTO nicRespDTO = existingHardwareMap.get(compositeKey);
                    if (nicRespDTO == null) {
                        newEntries.add(hardWareNicRespDTO);
                    } else if (!nicRespDTO.equals(hardWareNicRespDTO)) {
                        hardWareNicRespDTO.setId(nicRespDTO.getId());
                        updatedEntries.add(hardWareNicRespDTO);
                    }
                }

                hardWareNicApi.updates(updatedEntries);
                hardWareNicApi.adds(newEntries);
                if (!deleteEntries.isEmpty()) {
                    hardWareNicApi.deletes(deleteEntries);
                }
            }
        }


        if (!hardWareRespCreateReqDTOS.isEmpty()) {
            List<HardWareRespCreateReqDTO> existingHardwareList = hardWareInfoApi.getHardwareByPlatformId(p.getId()).getCheckedData();

            if (existingHardwareList.isEmpty()) {
                hardWareInfoApi.adds(hardWareRespCreateReqDTOS);
            } else {
                Map<String, HardWareRespCreateReqDTO> existingHardwareMap = existingHardwareList.stream()
                        .collect(Collectors.toMap(HardWareRespCreateReqDTO::getUuid, hardware -> hardware));

                List<HardWareRespCreateReqDTO> newEntries = new ArrayList<>();
                List<HardWareRespCreateReqDTO> updatedEntries = new ArrayList<>();
                List<HardWareRespCreateReqDTO> deleteEntries = existingHardwareList.stream()
                        .filter(item -> !hardWareRespCreateReqDTOS.stream()
                                .map(HardWareRespCreateReqDTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid())).collect(Collectors.toList());


                for (HardWareRespCreateReqDTO newHardware : hardWareRespCreateReqDTOS) {
                    HardWareRespCreateReqDTO existingHardware = existingHardwareMap.get(newHardware.getUuid());
                    if (existingHardware == null) {
                        newEntries.add(newHardware);
                    } else if (!existingHardware.equals(newHardware)) {
                        updatedEntries.add(newHardware);
                    }
                }

                hardWareInfoApi.updates(updatedEntries);
                hardWareInfoApi.adds(newEntries);

                if (!deleteEntries.isEmpty()) {
                    hardWareInfoApi.deleteHardWare(deleteEntries);

                    Long alertIds = alarmConfigApi.getMaxAlertId().getData();
                    // 资源删除触发告警
                    List<AlarmDorisReqDTO> toInsert = new ArrayList<>();
                    int i = 1;
                    for (HardWareRespCreateReqDTO hardWareRespCreateReqDTO : deleteEntries) {
                        AlarmDorisReqDTO collectorAlert = new AlarmDorisReqDTO();
                        JSONObject tags = new JSONObject();
                        tags.put("app", "hardware");
                        tags.put("monitorId", hardWareRespCreateReqDTO.getUuid());
                        tags.put("monitorName", hardWareRespCreateReqDTO.getName());
                        collectorAlert.setPriority(0);
                        collectorAlert.setStatus(0);
                        collectorAlert.setIsSolved(0);
                        collectorAlert.setFirstAlarmTime(new Date().getTime());
                        collectorAlert.setGmtCreate(new Date());
                        collectorAlert.setGmtUpdate(new Date());
                        collectorAlert.setLastAlarmTime(new Date().getTime());
                        collectorAlert.setMonitorName(hardWareRespCreateReqDTO.getName());
                        collectorAlert.setMonitorId(Convert.toStr(hardWareRespCreateReqDTO.getUuid()));
                        collectorAlert.setPlatformName(hardWareRespCreateReqDTO.getPlatformName());
                        collectorAlert.setPlatformId(hardWareRespCreateReqDTO.getPlatformId());
                        collectorAlert.setContent(String.format("该宿主机资源(%s)已被删除！", hardWareRespCreateReqDTO.getName()));
                        collectorAlert.setAlarmName(hardWareRespCreateReqDTO.getName());
                        collectorAlert.setTimes(1);
                        collectorAlert.setResourceType(1);
                        collectorAlert.setTags(JsonUtil.toJson(tags));
                        collectorAlert.setTarget("delete");
                        collectorAlert.setApp("hardware");
                        collectorAlert.setAlarmRule("-");
                        collectorAlert.setAlarmId(0L);
                        collectorAlert.setId(alertIds + i);
                        toInsert.add(collectorAlert);
                        i++;
                    }
                    for (HardWareRespCreateReqDTO hardWareRespCreateReqDTO : deleteEntries) {
                        Long monitor = Convert.toLong(hardWareRespCreateReqDTO.getUuid());
                        List<TopologyDTO> list = topologyApi.get().getCheckedData();
                        if (!list.isEmpty()) {
                            List<TopologyDTO> topologyDTOS = new ArrayList<>();
                            list.forEach(topologyDTO -> {
                                try {
                                    JSONObject topologyJson = JSONObject.parseObject(topologyDTO.getTopologyJson());
                                    JSONArray edgesJson = topologyJson.getJSONArray("edges");
                                    JSONArray interfacesJson = JSONObject.parseArray(topologyDTO.getInterfacesJson());

                                    List<Map> edges = JSONObject.parseArray(edgesJson.toJSONString(), Map.class);
                                    edges.forEach(edge -> {
                                        String relationList = jodd.util.StringUtil.toString(edge.get("relationList"));
                                        JSONArray relationJson = JSONArray.parseArray(relationList);
                                        if (relationJson != null) {
                                            List<Map> relations = JSONObject.parseArray(relationJson.toJSONString(), Map.class);
                                            if (relations.size() > 0) {
                                                Iterator<Map> iterator = relations.iterator();
                                                while (iterator.hasNext()) {
                                                    Map map = iterator.next();
                                                    if (monitor.equals(Convert.toLong(map.get("sourceMonitorId"))) || monitor.equals(Convert.toLong(map.get("targetMonitorId")))) {
                                                        iterator.remove(); // 使用迭代器来移除元素
                                                    }
                                                }
                                            }
                                            edge.put("relationList", JsonUtil.toJson(relations));
                                        }
                                    });

                                    List<Map> interfaces = JSONObject.parseArray(interfacesJson.toJSONString(), Map.class);
                                    interfaces.removeIf(iface -> monitor.equals(Convert.toLong(iface.get("monitorId"))));

                                    JSONObject resourceJson = JSONObject.parseObject(topologyDTO.getResourceJson());
                                    JSONArray physicalList = resourceJson.getJSONArray("physicalList");
                                    if (!physicalList.isEmpty()) {
                                        JSONArray assetsList = resourceJson.getJSONArray("assetsList");
                                        JSONObject jsonObject = new JSONObject();
                                        List<Map> resource = JSONObject.parseArray(physicalList.toJSONString(), Map.class);
                                        resource.removeIf(r -> monitor.equals(Convert.toLong(r.get("uuid"))));
                                        jsonObject.put("assetsList", assetsList);
                                        jsonObject.put("physicalList", JSONObject.parseArray(JsonUtil.toJson(resource)));
                                        topologyDTO.setResourceJson(JsonUtil.toJson(jsonObject));
                                    }


                                    JSONObject updatedTopologyJson = new JSONObject();
                                    updatedTopologyJson.put("nodes", topologyJson.getJSONArray("nodes"));
                                    updatedTopologyJson.put("combos", topologyJson.getJSONArray("combos"));
                                    updatedTopologyJson.put("edges", edges);

                                    topologyDTO.setTopologyJson(updatedTopologyJson.toJSONString());
                                    topologyDTO.setInterfacesJson(JsonUtil.toJson(interfaces));

                                    topologyDTOS.add(topologyDTO);
                                } catch (Exception e) {
                                    log.error("Error processing topology", e);
                                }
                            });
                            topologyApi.updatebatch(topologyDTOS);
                        }
                    }


                    Map<String, List> alertMap = new HashMap<>();
                    alertMap.put("insertList", toInsert);
                    //创建方法中需要有updateList，防止空指针异常
                    alertMap.put("updateList", new ArrayList<>());
                    alarmConfigApi.createAlarmToDoris(alertMap);

                }


            }
        }

    }


    @XxlJob("hostByInfo")
    public void hostByInfo() {
        // 获取配置租户平台信息
        List<PlatformconfigDTO> platformconfigDTOList = new ArrayList<>();
        if (platformRedisDAO.get("platform") == null) {
            platformconfigDTOList = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform", platformconfigDTOList);
        }

        platformconfigDTOList = platformRedisDAO.get("platform");
        if (platformconfigDTOList.size() > 0) {

            for (PlatformconfigDTO p : platformconfigDTOList) {
                ZstackLoginInfo zstackLoginInfo = new ZstackLoginInfo();
                if (zstackAccessTokenRedisDAO.get("zstack:" + p.getId()) != null) {
                    String token = zstackAccessTokenRedisDAO.get("zstack:" + p.getId()).getUuid();
                    // 根据不同租户查询不同用户下主机信息
                    String hostInfo = iZstackHardWareService.hardWareInfo(p.getUrl(), token, "");
                    //1.选择一个库
                    BatchPoints batchPoints = BatchPoints.builder().build();
                    JSONArray jsonArray = JSONObject.parseObject(hostInfo).getJSONArray("inventories");
                    for (int i = 0; i < jsonArray.size(); i++) {
                        HardWareRespCreateReqDTO hardWareRespCreateReqDTO = new HardWareRespCreateReqDTO();
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        if ("KVM".equals(jsonObject.getString("hypervisorType"))) {
                            String uuid = jsonObject.getString("uuid");
                            String name = jsonObject.getString("name");
                            // 全部CPU使用率
                            String cpuAllUsed = zstackApi.cpuAllUsedUtilization(p.getUrl(), token, uuid);
                            if (!cpuAllUsed.isEmpty()) {
                                JSONArray cpuallUsed = JSONObject.parseObject(cpuAllUsed).getJSONArray("data");
                                List<Map<String, Object>> cpuallUsedMaps = parseJSONArray(cpuallUsed, "value", "value");
                                cpuallUsedMaps.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_hardware")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "CPUAllUsedUtilization")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("host_type", "CPUAllUsedUtilization")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("host_metricName", "CPUAllUsedUtilization")
                                            .addField("type", "CPUAllUsedUtilization")
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                            .build();
                                    batchPoints.point(point);
                                });
                            } else {
                                System.out.println("进入");
                            }

                            String avercpus = zstackApi.cpuAverageUsedUtilization(p.getUrl(), token, uuid);
                            if (avercpus != null && !avercpus.isEmpty()) {
                                JSONArray avercpusUsed = JSONObject.parseObject(avercpus).getJSONArray("data");
                                List<Map<String, Object>> avercpusUsedMaps = parseJSONArray(avercpusUsed, "value", "value");
                                avercpusUsedMaps.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_hardware")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "CPUAverageUsedUtilization")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("host_type", "CPUAverageUsedUtilization")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("host_metricName", "CPUAverageUsedUtilization")
                                            .addField("type", "CPUAverageUsedUtilization")
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                            .build();
                                    batchPoints.point(point);
                                });
                            }

                            // CPU使用率
                            String cpuUsed = zstackApi.cpuUsedUtilization(p.getUrl(), token, uuid);
                            if (cpuUsed != null && !cpuUsed.isEmpty()) {
                                JSONArray cUsed = JSONObject.parseObject(cpuUsed).getJSONArray("data");
                                List<Map<String, Object>> cUsedMaps = parseJSONArray(cUsed, "value", "value");
                                cUsedMaps.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_hardware")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "CPUUsedUtilization")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("host_type", StringUtil.toString(map.get("type")))
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("host_metricName", "CPUUsedUtilization")
                                            .addField("type", StringUtil.toString(map.get("type")))
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                            .build();
                                    batchPoints.point(point);
                                });
                            }


                            // CPU用户进程使用率
                            String cpuUserUsed = zstackApi.cpuUserUtilization(p.getUrl(), token, uuid);
                            if (cpuUserUsed != null && !cpuUserUsed.isEmpty()) {
                                JSONArray cpuUser = JSONObject.parseObject(cpuUserUsed).getJSONArray("data");
                                List<Map<String, Object>> cpuUserMaps = parseJSONArray(cpuUser, "value", "value");
                                cpuUserMaps.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_hardware")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "CPUUserUtilization")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("host_type", StringUtil.toString(map.get("type")))
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("host_metricName", "CPUUserUtilization")
                                            .addField("type", StringUtil.toString(map.get("type")))
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                            .build();
                                    batchPoints.point(point);
                                });

                            }


                            // CPU等待IO完成使用率
                            String cpuWait = zstackApi.cpuWaitUtilization(p.getUrl(), token, uuid);
                            if (cpuWait != null && !cpuWait.isEmpty()) {
                                JSONArray cpuWaitUtilization = JSONObject.parseObject(cpuWait).getJSONArray("data");
                                List<Map<String, Object>> cpuWaitUtilizationMaps = parseJSONArray(cpuWaitUtilization, "value", "value");
                                cpuWaitUtilizationMaps.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_hardware")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "CPUWaitUtilization")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("host_type", StringUtil.toString(map.get("type")))
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))

                                            .addField("host_metricName", "CPUWaitUtilization")
                                            .addField("type", StringUtil.toString(map.get("type")))
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                            .build();
                                    batchPoints.point(point);
                                });
                            }


                            // CPU系统进程使用率
                            String cpuSystem = zstackApi.cpuSystemUtilization(p.getUrl(), token, uuid);
                            if (cpuSystem != null && !cpuSystem.isEmpty()) {
                                JSONArray cpuSystemUtilization = JSONObject.parseObject(cpuSystem).getJSONArray("data");
                                List<Map<String, Object>> cpuSystemUtilizationMaps = parseJSONArray(cpuSystemUtilization, "value", "value");
                                cpuSystemUtilizationMaps.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_hardware")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "CPUSystemUtilization")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("host_type", StringUtil.toString(map.get("type")))
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("host_metricName", "CPUSystemUtilization")
                                            .addField("type", StringUtil.toString(map.get("type")))
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                            .build();
                                    batchPoints.point(point);
                                });

                            }

                            // CPU空闲率
                            String cpuIdle = zstackApi.cpuIdleUtilization(p.getUrl(), token, uuid);
                            if (cpuIdle != null && !cpuIdle.isEmpty()) {
                                JSONArray cpuIdleUtilization = JSONObject.parseObject(cpuIdle).getJSONArray("data");
                                List<Map<String, Object>> cpuIdleUtilizationMaps = parseJSONArray(cpuIdleUtilization, "value", "value");
                                cpuIdleUtilizationMaps.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_hardware")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "CPUIdleUtilization")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("host_type", StringUtil.toString(map.get("type")))
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))

                                            .addField("host_metricName", "CPUIdleUtilization")
                                            .addField("type", StringUtil.toString(map.get("type")))
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                            .build();
                                    batchPoints.point(point);
                                });
                            }


                            // =====================================内存==========================================

                            String memoryUsedInPercent = zstackApi.memoryUsedInPercent(p.getUrl(), token, uuid);
                            if (memoryUsedInPercent != null && !memoryUsedInPercent.isEmpty()) {
                                JSONArray memoryUsedInPercentArray = JSONObject.parseObject(memoryUsedInPercent).getJSONArray("data");
                                List<Map<String, Object>> memoryUsedInPercentArrayMaps = parseJSONArray(memoryUsedInPercentArray, "value", "value");
                                memoryUsedInPercentArrayMaps.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_hardware")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "MemoryUsedInPercent")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("host_type", "MemoryUsedInPercent")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))

                                            .addField("host_metricName", "MemoryUsedInPercent")
                                            .addField("type", "MemoryUsedInPercent")
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                            .build();
                                    batchPoints.point(point);
                                });

                            }


                            // 内存使用容量
                            String memoryUsed = zstackApi.memoryUsedBytes(p.getUrl(), token, uuid);
                            if (memoryUsed != null && !memoryUsed.isEmpty()) {
                                JSONArray memoryUsedBytes = JSONObject.parseObject(memoryUsed).getJSONArray("data");
                                List<Map<String, Object>> memoryUsedBytesMaps = parseJSONArray(memoryUsedBytes, "value", "value");
                                memoryUsedBytesMaps.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_hardware")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "MemoryUsedBytes")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("host_type", "MemoryUsedBytes")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))

                                            .addField("host_metricName", "MemoryUsedBytes")
                                            .addField("type", "MemoryUsedBytes")
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                            .build();
                                    batchPoints.point(point);
                                });
                            }


                            //内存未使用容量
                            String memoryFree = zstackApi.memoryFreeBytes(p.getUrl(), token, uuid);
                            if (memoryFree != null && !memoryFree.isEmpty()) {
                                JSONArray memoryFreeBytes = JSONObject.parseObject(memoryFree).getJSONArray("data");
                                List<Map<String, Object>> memoryFreeBytesMaps = parseJSONArray(memoryFreeBytes, "value", "value");
                                memoryFreeBytesMaps.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_hardware")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "MemoryFreeBytes")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("host_type", "MemoryFreeBytes")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))

                                            .addField("host_metricName", "MemoryFreeBytes")
                                            .addField("type", "MemoryFreeBytes")
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                            .build();
                                    batchPoints.point(point);
                                });

                            }

                            String diskRead = zstackApi.diskReadBytes(p.getUrl(), token, uuid);
                            if (diskRead != null && !diskRead.isEmpty()) {
                                JSONArray diskReadBytes = JSONObject.parseObject(diskRead).getJSONArray("data");
                                List<Map<String, Object>> diskReadBytesMaps = parseJSONArray(diskReadBytes, "value", "value");
                                diskReadBytesMaps.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_hardware")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "DiskReadBytes")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("host_type", StringUtil.toString(map.get("type")))
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))

                                            .addField("host_metricName", "DiskReadBytes")
                                            .addField("type", StringUtil.toString(map.get("type")))
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                            .build();
                                    batchPoints.point(point);
                                });
                            }


                            String diskWrite = zstackApi.diskWriteBytes(p.getUrl(), token, uuid);
                            if (diskWrite != null && !diskWrite.isEmpty()) {
                                JSONArray diskWriteBytes = JSONObject.parseObject(diskWrite).getJSONArray("data");
                                List<Map<String, Object>> diskWriteBytesMaps = parseJSONArray(diskWriteBytes, "value", "value");
                                diskWriteBytesMaps.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_hardware")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "DiskWriteBytes")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("host_type", StringUtil.toString(map.get("type")))
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))

                                            .addField("host_metricName", "DiskWriteBytes")
                                            .addField("type", StringUtil.toString(map.get("type")))
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                            .build();
                                    batchPoints.point(point);
                                });
                            }


                            String diskReadOps = zstackApi.diskReadOps(p.getUrl(), token, uuid);
                            if (diskReadOps != null && !diskReadOps.isEmpty()) {
                                JSONArray diskReadOpsJson = JSONObject.parseObject(diskReadOps).getJSONArray("data");
                                List<Map<String, Object>> diskReadOpsMaps = parseJSONArray(diskReadOpsJson, "value", "value");
                                diskReadOpsMaps.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_hardware")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "DiskReadOps")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("host_type", StringUtil.toString(map.get("type")))
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))

                                            .addField("host_metricName", "DiskReadOps")
                                            .addField("type", StringUtil.toString(map.get("type")))
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                            .build();
                                    batchPoints.point(point);
                                });
                            }


                            String diskWriteOps = zstackApi.diskWriteOps(p.getUrl(), token, uuid);
                            if (diskWriteOps != null && !diskWriteOps.isEmpty()) {
                                JSONArray diskWriteOpsJson = JSONObject.parseObject(diskWriteOps).getJSONArray("data");
                                List<Map<String, Object>> diskWriteOpsJsonMaps = parseJSONArray(diskWriteOpsJson, "value", "value");
                                diskWriteOpsJsonMaps.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_hardware")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "DiskWriteOps")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("host_type", StringUtil.toString(map.get("type")))
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))

                                            .addField("host_metricName", "DiskWriteOps")
                                            .addField("type", StringUtil.toString(map.get("type")))
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                            .build();
                                    batchPoints.point(point);
                                });
                            }


                            String diskAllUsedInPercent = zstackApi.diskAllUsedCapacityInPercent(p.getUrl(), token, uuid);
                            if (diskAllUsedInPercent != null && !diskAllUsedInPercent.isEmpty()) {
                                JSONArray diskAllUsedInPercentJson = JSONObject.parseObject(diskAllUsedInPercent).getJSONArray("data");
                                List<Map<String, Object>> diskAllUsedInPercentJsonMaps = parseJSONArray(diskAllUsedInPercentJson, "value", "value");
                                diskAllUsedInPercentJsonMaps.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_hardware")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "DiskAllUsedCapacityInPercent")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("host_type", "DiskAllUsedCapacityInPercent")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))

                                            .addField("host_metricName", "DiskAllUsedCapacityInPercent")
                                            .addField("type", "DiskAllUsedCapacityInPercent")
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                            .build();
                                    batchPoints.point(point);
                                });
                            }


                            String diskAllUsedInBytes = zstackApi.diskAllUsedCapacityInBytes(p.getUrl(), token, uuid);
                            if (diskAllUsedInBytes != null && !diskAllUsedInBytes.isEmpty()) {
                                JSONArray diskAllUsedInBytesJson = JSONObject.parseObject(diskAllUsedInBytes).getJSONArray("data");
                                List<Map<String, Object>> diskAllUsedInBytesJsonMaps = parseJSONArray(diskAllUsedInBytesJson, "value", "value");
                                diskAllUsedInBytesJsonMaps.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_hardware")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "DiskAllUsedCapacityInBytes")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("host_type", "DiskAllUsedCapacityInBytes")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))

                                            .addField("host_metricName", "DiskAllUsedCapacityInBytes")
                                            .addField("type", "DiskAllUsedCapacityInBytes")
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                            .build();
                                    batchPoints.point(point);
                                });
                            }


                            String diskZStackAllUsedInBytes = zstackApi.diskZStackUsedCapacityInPercent(p.getUrl(), token, uuid);
                            if (diskZStackAllUsedInBytes != null && !diskZStackAllUsedInBytes.isEmpty()) {
                                JSONArray diskZStackAllUsedInBytesJson = JSONObject.parseObject(diskZStackAllUsedInBytes).getJSONArray("data");
                                List<Map<String, Object>> diskZStackAllUsedInBytesJsonMaps = parseJSONArray(diskZStackAllUsedInBytesJson, "value", "value");
                                diskZStackAllUsedInBytesJsonMaps.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_hardware")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "DiskZStackUsedCapacityInPercent")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("host_type", "DiskZStackUsedCapacityInPercent")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))

                                            .addField("host_metricName", "DiskZStackUsedCapacityInPercent")
                                            .addField("type", "DiskZStackUsedCapacityInPercent")
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                            .build();
                                    batchPoints.point(point);
                                });

                            }

                            String diskZStackUsedCapacityInBytes = zstackApi.diskZStackUsedCapacityInBytes(p.getUrl(), token, uuid);
                            if (diskZStackUsedCapacityInBytes != null && !diskZStackUsedCapacityInBytes.isEmpty()) {
                                JSONArray diskZStackUsedCapacityInBytesJson = JSONObject.parseObject(diskZStackUsedCapacityInBytes).getJSONArray("data");
                                List<Map<String, Object>> diskZStackUsedCapacityInBytesJsonMaps = parseJSONArray(diskZStackUsedCapacityInBytesJson, "value", "value");
                                diskZStackUsedCapacityInBytesJsonMaps.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_hardware")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "DiskZStackUsedCapacityInBytes")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("host_type", "DiskZStackUsedCapacityInBytes")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))

                                            .addField("host_metricName", "DiskZStackUsedCapacityInBytes")
                                            .addField("type", "DiskZStackUsedCapacityInBytes")
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                            .build();
                                    batchPoints.point(point);
                                });
                            }


                            // 全部磁盘读速度
                            String diskAllReadBytes = zstackApi.diskAllReadBytes(p.getUrl(), token, uuid);
                            if (diskAllReadBytes != null && !diskAllReadBytes.isEmpty()) {
                                JSONArray diskAllReadBytesJson = JSONObject.parseObject(diskAllReadBytes).getJSONArray("data");
                                List<Map<String, Object>> diskAllReadBytesJsonMaps = parseJSONArray(diskAllReadBytesJson, "value", "value");
                                diskAllReadBytesJsonMaps.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_hardware")
                                            .tag("uuid", uuid)
                                            .addField("productsName", name)
                                            .tag("metricName", "DiskAllReadBytes")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("host_type", "DiskAllReadBytes")
                                            .addField("host_metricName", "DiskAllReadBytes")
                                            .addField("type", "DiskAllReadBytes")
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                            .build();
                                    batchPoints.point(point);
                                });
                            }


                            // 全部磁盘读速度
                            String diskAllWriteBytes = zstackApi.diskAllWriteBytes(p.getUrl(), token, uuid);
                            if (diskAllWriteBytes != null && !diskAllWriteBytes.isEmpty()) {
                                JSONArray diskAllWriteBytesJson = JSONObject.parseObject(diskAllWriteBytes).getJSONArray("data");
                                List<Map<String, Object>> diskAllWriteBytesJsonMaps = parseJSONArray(diskAllWriteBytesJson, "value", "value");
                                diskAllWriteBytesJsonMaps.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_hardware")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "DiskAllWriteBytes")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("host_type", "DiskAllWriteBytes")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))

                                            .addField("host_metricName", "DiskAllWriteBytes")
                                            .addField("type", "DiskAllWriteBytes")
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                            .build();
                                    batchPoints.point(point);
                                });
                            }


                            // ==============================================网络===============================================
                            String netWorkOut = zstackApi.networkOutBytes(p.getUrl(), token, uuid);
                            if (netWorkOut != null && !netWorkOut.isEmpty()) {
                                JSONArray netWorkOutJson = JSONObject.parseObject(netWorkOut).getJSONArray("data");
                                List<Map<String, Object>> netWorkOutJsonMaps = parseJSONArray(netWorkOutJson, "value", "value");
                                netWorkOutJsonMaps.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_hardware")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "NetworkOutBytes")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("host_type", StringUtil.toString(map.get("type")))
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))

                                            .addField("host_metricName", "NetworkOutBytes")
                                            .addField("type", StringUtil.toString(map.get("type")))
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                            .build();
                                    batchPoints.point(point);
                                });
                            }


                            String netWorkIn = zstackApi.networkInBytes(p.getUrl(), token, uuid);
                            if (netWorkIn != null && !netWorkIn.isEmpty()) {
                                JSONArray netWorkInJson = JSONObject.parseObject(netWorkIn).getJSONArray("data");
                                List<Map<String, Object>> netWorkInJsonMaps = parseJSONArray(netWorkInJson, "value", "value");
                                netWorkInJsonMaps.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_hardware")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "NetworkInBytes")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("host_type", StringUtil.toString(map.get("type")))
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))

                                            .addField("host_metricName", "NetworkInBytes")
                                            .addField("type", StringUtil.toString(map.get("type")))
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                            .build();
                                    batchPoints.point(point);
                                });
                            }


                            String netOutPackets = zstackApi.networkOutPackets(p.getUrl(), token, uuid);
                            JSONArray netOutPacketsJson = JSONObject.parseObject(netOutPackets).getJSONArray("data");
                            List<Map<String, Object>> netOutPacketsJsonMaps = parseJSONArray(netOutPacketsJson, "value", "value");
                            netOutPacketsJsonMaps.forEach(map -> {
                                Point point = Point.measurement("zj_cloud_hardware")
                                        .tag("uuid", uuid)
                                        .tag("metricName", "NetworkOutPackets")
                                        .addField("productsName", name)
                                        .tag("regionId", StringUtil.toString(p.getRegionId()))
                                        .addField("regionName", StringUtil.toString(p.getRegionName()))
                                        .tag("host_type", StringUtil.toString(map.get("type")))
                                        .tag("platformId", StringUtil.toString(p.getId()))
                                        .addField("platformName", StringUtil.toString(p.getName()))

                                        .addField("host_metricName", "NetworkOutPackets")
                                        .addField("type", StringUtil.toString(map.get("type")))
                                        .addField("value", Convert.toBigDecimal(map.get("value")))
                                        .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                        .build();
                                batchPoints.point(point);
                            });

                            String netInPackets = zstackApi.networkInPackets(p.getUrl(), token, uuid);
                            JSONArray netInPacketsJson = JSONObject.parseObject(netInPackets).getJSONArray("data");
                            List<Map<String, Object>> netInPacketsJsonMaps = parseJSONArray(netInPacketsJson, "value", "value");
                            netInPacketsJsonMaps.forEach(map -> {
                                Point point = Point.measurement("zj_cloud_hardware")
                                        .tag("uuid", uuid)
                                        .tag("metricName", "NetworkInPackets")
                                        .addField("productsName", name)
                                        .tag("regionId", StringUtil.toString(p.getRegionId()))
                                        .addField("regionName", StringUtil.toString(p.getRegionName()))
                                        .tag("host_type", StringUtil.toString(map.get("type")))
                                        .tag("platformId", StringUtil.toString(p.getId()))
                                        .addField("platformName", StringUtil.toString(p.getName()))

                                        .addField("host_metricName", "NetworkInPackets")
                                        .addField("type", StringUtil.toString(map.get("type")))
                                        .addField("value", Convert.toBigDecimal(map.get("value")))
                                        .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                        .build();
                                batchPoints.point(point);
                            });
                            //
                            String netWorkOutErrors = zstackApi.networkOutErrors(p.getUrl(), token, uuid);
                            JSONArray netWorkOutErrorsJson = JSONObject.parseObject(netWorkOutErrors).getJSONArray("data");
                            List<Map<String, Object>> netWorkOutErrorsJsonMaps = parseJSONArray(netWorkOutErrorsJson, "value", "value");
                            netWorkOutErrorsJsonMaps.forEach(map -> {
                                Point point = Point.measurement("zj_cloud_hardware")
                                        .tag("uuid", uuid)
                                        .tag("metricName", "NetworkOutErrors")
                                        .addField("productsName", name)
                                        .tag("regionId", StringUtil.toString(p.getRegionId()))
                                        .addField("regionName", StringUtil.toString(p.getRegionName()))
                                        .tag("host_type", StringUtil.toString(map.get("type")))
                                        .tag("platformId", StringUtil.toString(p.getId()))
                                        .addField("platformName", StringUtil.toString(p.getName()))

                                        .addField("host_metricName", "NetworkOutErrors")
                                        .addField("type", StringUtil.toString(map.get("type")))
                                        .addField("value", Convert.toBigDecimal(map.get("value")))
                                        .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                        .build();
                                batchPoints.point(point);
                            });

                            String netWorkInErrors = zstackApi.networkInErrors(p.getUrl(), token, uuid);
                            JSONArray netWorkInErrorsJson = JSONObject.parseObject(netWorkInErrors).getJSONArray("data");
                            List<Map<String, Object>> netWorkInErrorsJsonMaps = parseJSONArray(netWorkInErrorsJson, "value", "value");
                            netWorkInErrorsJsonMaps.forEach(map -> {
                                Point point = Point.measurement("zj_cloud_hardware")
                                        .tag("uuid", uuid)
                                        .tag("metricName", "NetworkInErrors")
                                        .addField("productsName", name)
                                        .tag("regionId", StringUtil.toString(p.getRegionId()))
                                        .addField("regionName", StringUtil.toString(p.getRegionName()))
                                        .tag("host_type", StringUtil.toString(map.get("type")))
                                        .tag("platformId", StringUtil.toString(p.getId()))
                                        .addField("platformName", StringUtil.toString(p.getName()))

                                        .addField("host_metricName", "NetworkInErrors")
                                        .addField("type", StringUtil.toString(map.get("type")))
                                        .addField("value", Convert.toBigDecimal(map.get("value")))
                                        .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                        .build();
                                batchPoints.point(point);
                            });

                            String networkAllOutBytes = zstackApi.networkAllOutBytes(p.getUrl(), token, uuid);
                            JSONArray networkAllOutBytesJson = JSONObject.parseObject(networkAllOutBytes).getJSONArray("data");
                            List<Map<String, Object>> networkAllOutBytesJsonMaps = parseJSONArray(networkAllOutBytesJson, "value", "value");
                            networkAllOutBytesJsonMaps.forEach(map -> {
                                Point point = Point.measurement("zj_cloud_hardware")
                                        .tag("uuid", uuid)
                                        .tag("metricName", "NetworkAllOutBytes")
                                        .addField("productsName", name)
                                        .tag("regionId", StringUtil.toString(p.getRegionId()))
                                        .addField("regionName", StringUtil.toString(p.getRegionName()))
                                        .tag("host_type", "NetworkAllOutBytes")
                                        .tag("platformId", StringUtil.toString(p.getId()))
                                        .addField("platformName", StringUtil.toString(p.getName()))

                                        .addField("host_metricName", "NetworkAllOutBytes")
                                        .addField("type", "NetworkAllOutBytes")
                                        .addField("value", Convert.toBigDecimal(map.get("value")))
                                        .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                        .build();
                                batchPoints.point(point);
                            });

                            String networkAllInBytes = zstackApi.networkAllInBytes(p.getUrl(), token, uuid);
                            JSONArray networkAllInBytesJson = JSONObject.parseObject(networkAllInBytes).getJSONArray("data");
                            List<Map<String, Object>> networkAllInBytesJsonMaps = parseJSONArray(networkAllInBytesJson, "value", "value");
                            networkAllInBytesJsonMaps.forEach(map -> {
                                Point point = Point.measurement("zj_cloud_hardware")
                                        .tag("uuid", uuid)
                                        .tag("metricName", "NetworkAllInBytes")
                                        .addField("productsName", name)
                                        .tag("regionId", StringUtil.toString(p.getRegionId()))
                                        .addField("regionName", StringUtil.toString(p.getRegionName()))
                                        .tag("host_type", "NetworkAllInBytes")
                                        .tag("platformId", StringUtil.toString(p.getId()))
                                        .addField("platformName", StringUtil.toString(p.getName()))

                                        .addField("host_metricName", "NetworkAllInBytes")
                                        .addField("type", "NetworkAllInBytes")
                                        .addField("value", Convert.toBigDecimal(map.get("value")))
                                        .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                        .build();
                                batchPoints.point(point);
                            });
                        }
                    }
                    int shardIndex = XxlJobHelper.getShardIndex();
                    int shardTotal = XxlJobHelper.getShardTotal();
                    log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
                    List<Point> s = StringUtil.getShardingData(batchPoints.getPoints(), shardTotal, shardIndex);

                    influxDBTemplate.writeBatch(BatchPoints.builder().points(s).build());
                }
            }
        }

    }


    @XxlJob("hostByInfos")
    public void hostByInfos() {
        List<Callable<Void>> tasks = new ArrayList<>();
        // 获取配置租户平台信息
        List<PlatformconfigDTO> platformconfigDTOList = fetchAndCachePlatformConfig(platformconfigApi, platformRedisDAO);
        if (!platformconfigDTOList.isEmpty()) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                String token = Optional.ofNullable(zstackAccessTokenRedisDAO.get("zstack:" + p.getId()))
                        .map(ZstackLoginInfo::getUuid)
                        .orElse(null);
                if (token != null) {
                    try {
                        // 获取主机信息
                        String hostInfo = iZstackHardWareService.hardWareInfo(p.getUrl(), token, "");
                        JSONArray hostInventories = JSONObject.parseObject(hostInfo).getJSONArray("inventories");
                        // 指标名称数组
                        String[] metrics = {
                                "CPUAllUsedUtilization", "CPUAverageUsedUtilization", "CPUUsedUtilization", "CPUUserUtilization",
                                "CPUWaitUtilization", "CPUSystemUtilization", "MemoryUsedInPercent", "MemoryUsedBytes",
                                "MemoryFreeBytes", "DiskReadBytes", "DiskWriteBytes", "DiskReadOps", "DiskWriteOps",
                                "DiskAllUsedCapacityInPercent", "DiskAllUsedCapacityInBytes", "DiskZStackUsedCapacityInPercent",
                                "DiskZStackUsedCapacityInBytes", "DiskAllReadBytes", "DiskAllWriteBytes", "NetworkOutBytes",
                                "NetworkInBytes", "NetworkOutPackets", "NetworkInPackets", "NetworkOutErrors", "NetworkInErrors",
                                "NetworkAllOutBytes", "NetworkAllInBytes"
                        };

                        String[] type = {"CPUUsedUtilization", "CPUUserUtilization", "CPUWaitUtilization", "CPUSystemUtilization",
                                "CPUIdleUtilization", "DiskReadBytes", "DiskWriteBytes", "DiskReadOps",
                                "DiskWriteOps", "NetworkOutBytes", "NetworkInBytes", "NetworkOutPackets", "NetworkInPackets",
                                "NetworkOutErrors", "NetworkInErrors"};
                        BatchPoints batchPoints = BatchPoints.builder().build();
                        for (int i = 0; i < hostInventories.size(); i++) {
                            JSONObject jsonObject = hostInventories.getJSONObject(i);
                            if ("KVM".equals(jsonObject.getString("hypervisorType"))) {
                                String uuid = jsonObject.getString("uuid");
                                String name = jsonObject.getString("name");
                                for (String metric : metrics) {
                                    fetchMetricAndBuildPoints(p.getUrl(), token, p, uuid, metric, "zj_cloud_hardware", batchPoints, name, jsonObject, type);
                                }

                            }
                        }
                        // 写入InfluxDB
                        writeBatchPoints(batchPoints);
                    } catch (Exception e) {
                        System.err.println("Error processing platform " + p.getName() + ": " + e.getMessage());
                        // 日志记录
                        log.error("Error processing platform: " + p.getName(), e);
                    }
                }
            }
        }
    }

    // 将数据点写入InfluxDB的方法
    private void writeBatchPoints(BatchPoints batchPoints) {
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
        List<Point> s = StringUtil.getShardingData(batchPoints.getPoints(), shardTotal, shardIndex);

        influxDBTemplate.writeBatch(BatchPoints.builder().points(s).build());
    }

    private List<Map<String, Object>> parseJSONArray(JSONArray jsonArray, String labelKey, String valueKey) {
        List<Map<String, Object>> maps = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            Map<String, Object> map = new HashMap<>();
            if (jsonObject.getJSONObject("labels").getString("CPUNum") != null) {
                map.put("type", jsonObject.getJSONObject("labels").getString("CPUNum"));
            }
            if (jsonObject.getJSONObject("labels").getString("DiskDeviceLetter") != null) {
                map.put("type", jsonObject.getJSONObject("labels").getString("DiskDeviceLetter"));
            }
            if (jsonObject.getJSONObject("labels").getString("NetworkDeviceLetter") != null) {
                map.put("type", jsonObject.getJSONObject("labels").getString("NetworkDeviceLetter"));
            }
            if (jsonObject.get("time") != null) {
                map.put("time", jsonObject.getLong("time"));
            }
            map.put("HostUuid", jsonObject.getJSONObject("labels").getString("HostUuid"));
            map.put(labelKey, jsonObject.getBigDecimal(valueKey));
            maps.add(map);
        }
        return maps;
    }


    public void loginInfo() {

        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                .filter(dto -> "zstack".equals(dto.getTypeCode()))
                .collect(Collectors.toList());

        if (!filteredList.isEmpty()) {
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (PlatformconfigDTO p : filteredList) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    ZstackLoginInfo zstackLoginInfo = new ZstackLoginInfo();
                    if (zstackAccessTokenRedisDAO.get("zstack:" + p.getId()) == null) {
                        JSONObject parameters = new JSONObject();
                        JSONObject parame = new JSONObject();
                        parame.put("accountName", p.getUsername());
                        parame.put("password", Sha256.SHA512(p.getPassword()));
                        parameters.put("logInByAccount", parame);
                        try {
                            HttpRequest res = HttpRequest.put(p.getUrl() + ZstackApiConstant.GET_ZSTACK_LOGIN).body(parameters.toJSONString());
                            log.info("获取登录数据：" + res.execute().body());
                            if (200 == res.execute().getStatus()) {
                                String d = res.execute().body();
                                JSONObject jsonObject = JSONObject.parseObject(d).getJSONObject("inventory");
                                String uuid = jsonObject.getString("uuid");
                                String userUuid = jsonObject.getString("userUuid");
                                String accountUuid = jsonObject.getString("accountUuid");
                                Date expiredDate = new Date(jsonObject.getString("expiredDate"));
                                Date createDate = new Date(jsonObject.getString("createDate"));
                                zstackLoginInfo.setUuid(uuid);
                                zstackLoginInfo.setUserUuid(userUuid);
                                zstackLoginInfo.setAccountUuid(accountUuid);
                                zstackLoginInfo.setUrl(p.getUrl());
                                zstackLoginInfo.setRegionId(p.getRegionId());
                                zstackLoginInfo.setTenanName(p.getName());
                                zstackLoginInfo.setRegionName(p.getRegionName());
                                zstackLoginInfo.setCreateDate(DateUtil.toLocalDateTime(createDate));
                                zstackLoginInfo.setExpiredDate(DateUtil.toLocalDateTime(expiredDate));
                                zstackAccessTokenRedisDAO.set("zstack:" + p.getId(), zstackLoginInfo);
                            }
                        } catch (Exception e) {
                            log.info("获取授权异常：" + e.getMessage());
                        }
                    } else {
                        zstackLoginInfo = zstackAccessTokenRedisDAO.get("zstack" + p.getId());
                    }
                });
                futures.add(future);
            }
            // 等待所有异步任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        }
    }

    public static String removeProtocolAndPort(String url) {
        // 去除协议部分
        String noProtocol = url.replaceFirst("^(http://|https://)", "");
        // 去除端口号部分
        String noPort = noProtocol.replaceFirst(":\\d+$", "");
        return noPort;
    }

    public static String extractPort(String url) {
        // 定义正则表达式模式来匹配端口号
        Pattern pattern = Pattern.compile(":\\d+$");
        Matcher matcher = pattern.matcher(url);

        // 查找匹配的端口号
        if (matcher.find()) {
            // 去掉冒号并返回端口号
            return matcher.group().substring(1);
        } else {
            // 如果没有找到端口号，根据协议返回默认端口号
            if (url.startsWith("https://")) {
                return "443";
            } else if (url.startsWith("http://")) {
                return "80";
            }
        }

        // 如果没有找到协议，返回空字符串
        return "";
    }
}
