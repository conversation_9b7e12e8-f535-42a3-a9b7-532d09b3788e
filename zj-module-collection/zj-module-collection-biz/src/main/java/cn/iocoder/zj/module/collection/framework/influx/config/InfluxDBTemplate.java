package cn.iocoder.zj.module.collection.framework.influx.config;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.InfluxDB;
import org.influxdb.InfluxDBFactory;
import org.influxdb.annotation.Column;
import org.influxdb.annotation.Measurement;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName : InfluxDBTemplate  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/6/12  13:49
 */
@Slf4j
@Configuration
public class InfluxDBTemplate {

    //1.influxDBTemplate ---> influxdb---> 数据库 获取 influxdb 连接



    @Resource
    private final InfluxdbProperties influxdbProperties;

    private InfluxDB influxDB;

    @Autowired
    public InfluxDBTemplate(InfluxdbProperties influxdbProperties) {
        this.influxdbProperties = influxdbProperties;
//        getInfluxDB();
    }

    /**
     * 获取 influxdb 连接
     */
    public void getInfluxDB() {
        this.influxDB = influxDbBuild();
    }

    private InfluxDB influxDbBuild() {
        String dbName = influxdbProperties.getDatabase();
        influxDB = InfluxDBFactory.connect(influxdbProperties.getUrl(), influxdbProperties.getUsername(), influxdbProperties.getPassword());
        List<String> dbNames = influxDB.describeDatabases();
        if (dbNames.contains(dbName)) {
            try {
                influxDB.setDatabase(influxdbProperties.getDatabase());
            } catch (Exception e) {
                log.error("create influx db failed, error: {}", e.getMessage());
            } finally {
                influxDB.setRetentionPolicy(influxdbProperties.getRetention());
            }
        } else {
            try {
                createDataBase(influxdbProperties.getDatabase());
                influxDB.setDatabase(influxdbProperties.getDatabase());
            } catch (Exception e) {
                log.error("create influx db failed, error: {}", e.getMessage());
            } finally {
                influxDB.setRetentionPolicy(influxdbProperties.getRetention());
            }
        }
        influxDB.setLogLevel(InfluxDB.LogLevel.BASIC);
        return influxDB;
    }

    /**
     * 关闭连接
     */
    public void close() {
        if (influxDB != null) {
            influxDB.close();
        }
    }


    /**
     * 指定时间插入
     *
     * @param measurement 表
     * @param tags        标签
     * @param fields      字段
     * @param time        时间
     * @param unit        单位
     */
    public void write(String measurement, Map<String, String> tags, Map<String, Object> fields, long time, TimeUnit unit) {
        Point point = Point.measurement(measurement).tag(tags).fields(fields).time(time, unit).build();
        influxDB.write(point);
        close();
    }

    /**
     * 插入数据-自动生成时间
     *
     * @param measurement 表
     * @param tags        标签
     * @param fields      字段
     */
    public void write(String measurement, Map<String, String> tags, Map<String, Object> fields) {
        write(measurement, tags, fields, System.currentTimeMillis(), TimeUnit.MILLISECONDS);
    }

    /**
     * 批量插入
     *
     * @param points 批量记录  推荐 1000 条作为一个批
     */
    public void writeBatch(BatchPoints points) {
        influxDB.write(points);
        close();
    }


    /**
     * 用来执行相关操作
     *
     * @param command 执行命令
     * @return 返回结果
     */
    public QueryResult query(String command) {
        return influxDB.query(new Query(command));
    }


    /**
     * 创建数据库
     *
     * @param name 库名
     */
    public void createDataBase(String name) {
        query("create database " + name);
    }

    /**
     * 删除数据库
     *
     * @param name 库名
     */
    public void dropDataBase(String name) {
        query("drop database " + name);
    }

    /**
     * select 查询封装
     *
     * @param queryResult 查询返回结果
     * @param clazz       封装对象类型
     * @param <T>         泛型
     * @return 返回处理回收结果
     */
    public <T> List<T> handleQueryResult(QueryResult queryResult, Class<T> clazz) {
        //0.定义保存结果集合
        List<T> lists = new ArrayList<>();
        //1.获取结果
        List<QueryResult.Result> results = queryResult.getResults();
        //2.遍历结果
        results.forEach(result -> {
            //3.获取 series
            List<QueryResult.Series> seriesList = result.getSeries();
            //4.遍历 series
            seriesList.forEach(series -> {
                //5.获取的所有列
                List<String> columns = series.getColumns();
                //6.获取所有值
                List<List<Object>> values = series.getValues();
                //7.遍历数据 获取结果
                for (int i = 0; i < values.size(); i++) {
                    try {
                        //8.根据 clazz 进行封装
                        T instance = clazz.newInstance();
                        //9.通过 spring 框架提供反射类进行处理
                        BeanWrapperImpl beanWrapper = new BeanWrapperImpl(instance);
                        HashMap<String, Object> fields = new HashMap<>();
                        for (int j = 0; j < columns.size(); j++) {
                            String column = columns.get(j);
                            Object val = values.get(i).get(j);
                            if ("time".equals(column)) {
                                beanWrapper.setPropertyValue("time", Timestamp.from(ZonedDateTime.parse(String.valueOf(val)).toInstant()).getTime());
                            } else {
                                //保存当前列和值到 field map 中 //注意: 返回结果无须在知道是 tags 还是 fields  认为就是字段和值 可以将所有字段作为 field 进行返回
                                fields.put(column, val);
                            }
                        }
                        //10.通过反射完成 fields 赋值操作
                        beanWrapper.setPropertyValue("fields", fields);
                        lists.add(instance);
                    } catch (InstantiationException | IllegalAccessException e) {
                        throw new RuntimeException(e);
                    }
                }
            });
        });
        return lists;
    }


    /**
     * 查询返回指定对象
     *
     * @param selectCommand select 语句
     * @param clazz         类型
     * @param <T>           泛型
     * @return 结果
     */
    public <T> List<T> query(String selectCommand, Class<T> clazz) {
        return handleQueryResult(query(selectCommand), clazz);
    }


    /**
     * 新增单条记录,利用java的反射机制进行新增操作
     */
    public void insertOne(Object obj, long timeStamp) {
        //获取度量
        Class<?> clasz = obj.getClass();
        Measurement measurement = clasz.getAnnotation(Measurement.class);
        //构建
        Point.Builder builder = Point.measurement(measurement.name())
                .time(timeStamp, TimeUnit.MILLISECONDS);
        // 获取对象属性
        Field[] fieldArray = clasz.getDeclaredFields();
        Column column = null;
        for (Field field : fieldArray) {
            try {
                column = field.getAnnotation(Column.class);
                //设置属性可操作
                field.setAccessible(true);
                if (column.tag()) {
                    //tag属性只能存储String类型
                    builder.tag(column.name(), field.get(obj).toString());
                } else {
                    //设置field
                    if (field.get(obj) != null) {
                        builder.addField(column.name(), field.get(obj).toString());
                    }
                }
            } catch (IllegalArgumentException | IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        influxDB.write(builder.build());
    }

    /**
     * 批量新增,方法一
     */
    public void insertBatchByRecords(List<?> records) {
        List<String> lines = new ArrayList<String>();
        records.forEach(record -> {
            Class<?> clasz = record.getClass();
            //获取度量
            Measurement measurement = clasz.getAnnotation(Measurement.class);
            //构建
            Point.Builder builder = Point.measurement(measurement.name());
            Field[] fieldArray = clasz.getDeclaredFields();
            Column column = null;
            for (Field field : fieldArray) {
                try {
                    column = field.getAnnotation(Column.class);
                    //设置属性可操作
                    field.setAccessible(true);
                    if (column.tag()) {
                        //tag属性只能存储String类型
                        builder.tag(column.name(), field.get(record).toString());
                    } else {
                        //设置field
                        if (field.get(record) != null) {
                            builder.addField(column.name(), field.get(record).toString());
                        }
                    }
                } catch (IllegalArgumentException | IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
            lines.add(builder.build().lineProtocol());
        });
        influxDB.write(lines);
    }

    /**
     * 批量新增，方法二
     */
    public void insertBatchByPoints(List<?> records) {
        BatchPoints batchPoints = BatchPoints.database(influxdbProperties.getDatabase())
                .consistency(InfluxDB.ConsistencyLevel.ALL)
                .build();
        records.forEach(record -> {
            Class<?> clasz = record.getClass();
            //获取度量
            Measurement measurement = clasz.getAnnotation(Measurement.class);
            //构建
            Point.Builder builder = Point.measurement(measurement.name());
            Field[] fieldArray = clasz.getDeclaredFields();
            Column column = null;
            for (Field field : fieldArray) {
                try {
                    column = field.getAnnotation(Column.class);
                    //设置属性可操作
                    field.setAccessible(true);
                    if (column.tag()) {
                        //tag属性只能存储String类型
                        builder.tag(column.name(), field.get(record).toString());
                    } else {
                        //设置field
                        if (field.get(record) != null) {
                            builder.addField(column.name(), field.get(record).toString());
                        }
                    }
                } catch (IllegalArgumentException | IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
            batchPoints.point(builder.build());
        });
        influxDB.write(batchPoints);
    }

    /**
     * 查询，返回Map集合
     *
     * @param query 完整的查询语句
     * @return
     */
    public List<Object> fetchRecords(String query) {
        List<Object> results = new ArrayList<Object>();
        query = query + " TZ('Asia/Shanghai')";
        QueryResult queryResult = influxDB.query(new Query(query, influxdbProperties.getDatabase()));
        queryResult.getResults().forEach(result -> {
            if (result.getSeries()!=null){
                result.getSeries().forEach(serial -> {
                    List<String> columns = serial.getColumns();
                    int fieldSize = columns.size();
                    serial.getValues().forEach(value -> {
                        Map<String, Object> obj = new HashMap<String, Object>();
                        for (int i = 0; i < fieldSize; i++) {
                            obj.put(columns.get(i), value.get(i));
                        }
                        results.add(obj);
                    });
                });
            }
        });
        return results;
    }

    /**
     * 查询，返回map集合
     *
     * @param fieldKeys   查询的字段，不可为空；不可为单独的tag
     * @param measurement 度量，不可为空；
     * @return
     */
    public List<Object> fetchRecords(String fieldKeys, String measurement) {
        StringBuilder query = new StringBuilder();
        query.append("select ").append(fieldKeys).append(" from ").append(measurement);
        return this.fetchRecords(query.toString());
    }

    /**
     * 查询，返回map集合
     *
     * @param fieldKeys   查询的字段，不可为空；不可为单独的tag
     * @param measurement 度量，不可为空；
     * @param order
     * @return
     */
    public List<Object> fetchRecords(String fieldKeys, String measurement, String order) {
        StringBuilder query = new StringBuilder();
        query.append("select ").append(fieldKeys).append(" from ").append(measurement);
        query.append(" order by ").append(order);
        return this.fetchRecords(query.toString());
    }

    /**
     * 查询，返回map集合
     *
     * @param fieldKeys   查询的字段，不可为空；不可为单独的tag
     * @param measurement 度量，不可为空；
     * @param order
     * @param limit
     * @return
     */
    public List<Object> fetchRecords(String fieldKeys, String measurement, String order, String limit) {
        StringBuilder query = new StringBuilder();
        query.append("select ").append(fieldKeys).append(" from ").append(measurement);
        query.append(" order by ").append(order);
        query.append(limit);
        return this.fetchRecords(query.toString());
    }

    /**
     * 查询，返回对象的list集合
     *
     * @param query
     * @return
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    public <T> List<T> fetchResults(String query, Class<?> clasz) {
        List results = new ArrayList<>();
        //query = query + " TZ('Asia/Shanghai')";
        QueryResult queryResult = influxDB.query(new Query(query, influxdbProperties.getDatabase()));
        List<QueryResult.Series> series = queryResult.getResults().get(0).getSeries();
        if (series == null || series.size() < 1) {
            return results;
        }
        queryResult.getResults().forEach(result -> {
            result.getSeries().forEach(serial -> {
                List<String> columns = serial.getColumns();
                int fieldSize = columns.size();
                serial.getValues().forEach(value -> {
                    Object obj = null;
                    try {
                        obj = clasz.newInstance();
                        for (int i = 0; i < fieldSize; i++) {
                            String fieldName = columns.get(i);
                            Field field = clasz.getDeclaredField(fieldName);
                            field.setAccessible(true);
                            Class<?> type = field.getType();
                            if (type == float.class) {
                                field.set(obj, Float.valueOf(value.get(i).toString()));
                            } else {
                                Object o = value.get(i);
                                if ("time".equals(fieldName)) {
                                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
                                    LocalDateTime localDateTime = LocalDateTime.parse(o.toString(), formatter);
                                    ZonedDateTime utcZonedDateTime = ZonedDateTime.of(localDateTime, ZoneId.of("UTC"));
                                    ZonedDateTime cstZonedDateTime = utcZonedDateTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
                                    String cstTime = cstZonedDateTime.format(formatter);

                                    String s = DateUtil.format(DateUtil.parse(cstTime), "yyyy-MM-dd HH:mm:ss");
                                    field.set(obj, s);
                                } else {
                                    field.set(obj, o);
                                }
                            }
                        }
                    } catch (NoSuchFieldException | SecurityException | InstantiationException |
                             IllegalAccessException e) {
                        e.printStackTrace();
                    }
                    results.add(obj);
                });
            });
        });
        return results;
    }

    /**
     * 查询，返回对象的list集合
     *
     * @param fieldKeys
     * @param measurement
     * @param clasz
     * @return
     */
    public <T> List<T> fetchResults(String fieldKeys, String measurement, Class<?> clasz) {
        StringBuilder query = new StringBuilder();
        query.append("select ").append(fieldKeys).append(" from ").append(measurement);
        return this.fetchResults(query.toString(), clasz);
    }

    /**
     * 查询，返回对象的list集合
     *
     * @param fieldKeys
     * @param measurement
     * @param order
     * @param clasz
     * @return
     */
    public <T> List<T> fetchResults(String fieldKeys, String measurement, String order, Class<?> clasz) {
        StringBuilder query = new StringBuilder();
        query.append("select ").append(fieldKeys).append(" from ").append(measurement);
        query.append(" order by ").append(order);
        return this.fetchResults(query.toString(), clasz);
    }

    /**
     * 查询，返回对象的list集合
     *
     * @param fieldKeys
     * @param measurement
     * @param order
     * @param limit
     * @param clasz
     * @return
     */
    public <T> List<T> fetchResults(String fieldKeys, String measurement, String order, String limit, Class<?> clasz) {
        StringBuilder query = new StringBuilder();
        query.append("select ").append(fieldKeys).append(" from ").append(measurement);
        query.append(" order by ").append(order);
        query.append(limit);
        return this.fetchResults(query.toString(), clasz);
    }

}
