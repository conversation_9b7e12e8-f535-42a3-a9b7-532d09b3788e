package cn.iocoder.zj.module.collection.util;

import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketAddress;

/**
 * @ClassName : NetworkUtils  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/5/23  11:44
 */
public class NetworkUtils {
    public static boolean tcping(String ip, int port) {
        Socket socket = new Socket();
        try {
            SocketAddress socketAddress = new InetSocketAddress(ip, port);
            // 15秒的连接超时
            socket.connect(socketAddress, 15000);
            // 连接成功
            return true;
        } catch (Exception e) {
            // 连接失败
            return false;
        } finally {
            // 确保在最后关闭socket连接
            try {
                socket.close();
            } catch (Exception ignored) {
                // 忽略关闭时产生的异常
            }
        }
    }
}
