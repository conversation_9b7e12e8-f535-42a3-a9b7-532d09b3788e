package cn.iocoder.zj.module.collection.service.zstack.core;

/**
 * @ClassName : ZstackApiConstant  //类名
 * @Description : API相关接口  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/29  15:53
 */
public class ZstackApiConstant {

    public static final String DEFAULE_DOMAIN = "http://**************:8080";


    public static final String ZSTACK_API_PREFIX = "/zstack/v1";

    /**
     * @description: ZSTAKC 登录URL
     * <AUTHOR>
     * @date 2023/5/27 14:46
     * @version 1.0
     */
    public static final String GET_ZSTACK_LOGIN = ZSTACK_API_PREFIX + "/accounts/login";
    public static final String GET_LOGIN_CAPTCHA = ZSTACK_API_PREFIX + "/login/control/captcha?resourceName=admin&loginType=account";
    public static final String GET_ZSTACK_VM_INSTANCES = ZSTACK_API_PREFIX + "/vm-instances";
    public static final String GET_ZSTACK_HOSTS = ZSTACK_API_PREFIX + "/hosts";
    public static final String GET_ZSTACK_STORAGE = ZSTACK_API_PREFIX + "/primary-storage";
    public static final String GET_ZSTACK_METRICS = ZSTACK_API_PREFIX + "/zwatch/metrics";
    public static final String GET_ZSTACK_NICS = ZSTACK_API_PREFIX + "/eips";
    public static final String GET_ZSTACK_CLUSTERS = ZSTACK_API_PREFIX + "/clusters";

    public static final String GET_ZSTACK_VPC = ZSTACK_API_PREFIX + "/vpc/virtual-routers";

    public static final String GET_L2_NETWORKS = ZSTACK_API_PREFIX + "/l2-networks";

    public static final String GET_VLAN = GET_L2_NETWORKS + "/vlan";

    public static final String GET_L3_NETWORKS = ZSTACK_API_PREFIX + "/l3-networks";

    public static final String GET_IP_FREE = GET_L3_NETWORKS + "/ip/free";

    public static final String GET_ZSTACK_VOLUMES = ZSTACK_API_PREFIX + "/volumes";
    public static final String GET_VM_ATTACHABLE_DATA_VOLUME = ZSTACK_API_PREFIX + "/vm-instances";
    public static final String GET_ZSTACK_VOLUMES_SNAPSHOTS = ZSTACK_API_PREFIX + "/volume-snapshots";

    public static final String GET_ZSTACK_GLOBAL_CONFIG = ZSTACK_API_PREFIX + "/global-configurations";

    public static final String GET_ZSTACK_RESOURCE_CONFIG = ZSTACK_API_PREFIX + "/resource-configurations";

    public static final String GET_ZSTACK_VM_SNAPSHOTS = ZSTACK_API_PREFIX + "/vm-snapshots";

    public static final String GET_ZSTACK_IMAGES = ZSTACK_API_PREFIX + "/image-packages";
}
