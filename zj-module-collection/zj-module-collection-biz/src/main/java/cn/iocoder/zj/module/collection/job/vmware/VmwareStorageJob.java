package cn.iocoder.zj.module.collection.job.vmware;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.framework.influx.config.InfluxDBTemplate;
import cn.iocoder.zj.module.collection.service.vmware.storage.StorageResourceSummary;
import cn.iocoder.zj.module.collection.util.SampleUtil;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.monitor.api.hardwarestorage.HardWareStorageApi;
import cn.iocoder.zj.module.monitor.api.hardwarestorage.HardWareStorageRespDTO;
import cn.iocoder.zj.module.monitor.api.storage.StorageInfoApi;
import cn.iocoder.zj.module.monitor.api.storage.dto.StorageRespCreateReqDTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.vmware.vim25.DatastoreHostMount;
import com.vmware.vim25.DatastoreSummary;
import com.vmware.vim25.ManagedObjectReference;
import com.vmware.vim25.mo.Datastore;
import com.vmware.vim25.mo.HostSystem;
import com.vmware.vim25.mo.ServiceInstance;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.MalformedURLException;
import java.rmi.RemoteException;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * @ClassName : VmwareStorageJob  //类名
 * @Description : Vmware 存储采集  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/8/5  9:59
 */
@Component
@Slf4j
public class VmwareStorageJob {


    @Resource
    InfluxDBTemplate influxDBTemplate;
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    PlatformRedisDAO platformRedisDAO;
    @Resource
    StorageInfoApi storageInfoApi;

    @Autowired
    StorageResourceSummary storageResourceSummary;
    @Resource
    HardWareStorageApi hardWareStorageApi;

    public final Map<Long, List<StorageRespCreateReqDTO>> storageData = new ConcurrentHashMap<>();


    public void vmstorageJobList() throws Exception {
        vmwareStorageInfo();
        vmwarestoragePre();
    }


    @XxlJob("vmwareStorageInfo")
    public void vmwareStorageInfo() throws Exception {
        // 获取redis中数据
        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                .filter(dto -> "vmware".equals(dto.getTypeCode()))
                .collect(Collectors.toList());

        if (filteredList.isEmpty()) return;
        ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
        List<Future<Void>> futures = new ArrayList<>();
        Map<Long, Date> dateMap = platformconfigApi.getPlatCreateTimeList().getData();
        for (PlatformconfigDTO platformconfigDTO : filteredList) {
            // todo 待修改
            if (platformconfigDTO.getId() == 18) {
                continue;
            }
            Date createTime = dateMap.get(platformconfigDTO.getId());
            storagePlatformConfig(platformconfigDTO, createTime);
        }


    }

    @XxlJob("vmwarestoragePre")
    public void vmwarestoragePre() throws Exception {
        // 获取redis中数据
        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                .filter(dto -> "vmware".equals(dto.getTypeCode()))
                .collect(Collectors.toList());

        if (filteredList.isEmpty()) return;
        ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
        List<Future<Void>> futures = new ArrayList<>();


        for (PlatformconfigDTO platformconfigDTO : filteredList) {
            // todo 待修改
            if (platformconfigDTO.getId() == 18) {
                continue;
            }
            vmwarestoragePreConfig(platformconfigDTO);
        }


    }

    private void vmwarestoragePreConfig(PlatformconfigDTO platformconfigDTO) {
        List<StorageRespCreateReqDTO> dtos = storageInfoApi.getStorageByPlatformId(platformconfigDTO.getId());
        //
        BatchPoints batchPoints = BatchPoints.builder().build();
        for (StorageRespCreateReqDTO storageRespCreateReqDTO : dtos) {
            Point point = Point.measurement("zj_cloud_storage")
                    .tag("label", "disk")
                    .tag("uuid", storageRespCreateReqDTO.getUuid())
                    .tag("metricName", "UsedCapacityInPercent")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("storage_type", "UsedCapacityInPercent")
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", "")
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("storage_metricName", "UsedCapacityInPercent")
                    .addField("type", "UsedCapacityInPercent")
                    .addField("value", storageRespCreateReqDTO.getCapacityUtilization())
                    .time(new Date().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }
        influxDBTemplate.writeBatch(BatchPoints.builder().points(batchPoints.getPoints()).build());
    }

    private void storagePlatformConfig(PlatformconfigDTO platformconfigDTO, Date createTime) throws Exception {
        List<StorageRespCreateReqDTO> storageRespCreateReqDTOS = new ArrayList<>();
        List<HardWareStorageRespDTO> storageList = new ArrayList<>();
        List<StorageRespCreateReqDTO> dtos = storageInfoApi.getStorageByPlatformId(platformconfigDTO.getId());
        Map<String, Date> createDateInfo = new HashMap<>();

        if (!dtos.isEmpty()) {
            dtos.forEach(storageRespCreateReqDTO -> createDateInfo.put(storageRespCreateReqDTO.getUuid(), storageRespCreateReqDTO.getSCreateTime()));
        }

        ServiceInstance serviceInstance = SampleUtil.createServiceInstance(
                platformconfigDTO.getUrl(),
                platformconfigDTO.getUsername(),
                platformconfigDTO.getPassword()
        );

        List<Datastore> datastores = storageResourceSummary.getDatastores(serviceInstance);
        for (Datastore datastore : datastores) {
            // 获取存储关联的主机挂载信息
            DatastoreHostMount[] hostMounts = datastore.getHost();
            if (hostMounts != null && hostMounts.length > 0) {
                for (DatastoreHostMount hostMount : hostMounts) {
                    // 获取主机引用
                    ManagedObjectReference hostMor = hostMount.getKey();
                    // 转换为HostSystem对象
                    HostSystem hostSystem = new HostSystem(
                            datastore.getServerConnection(),
                            hostMor
                    );
                    // 获取主机所属集群/数据中心ID
                    String parentId = hostSystem.getParent().getMOR().getVal();
                    // 获取主机UUID
                    String hostUuid = hostSystem.getSummary().getHardware().getUuid();
                    // 获取主机MOR值
                    String hostId = parentId + hostUuid + hostSystem.getMOR().getVal();

                    String name = datastore.getName();
                    String uuid = datastore.getSummary().getUrl();
                    String clusterId = datastore.getParent().getMOR().getVal();
                    HardWareStorageRespDTO storageRespDTO = new HardWareStorageRespDTO();
                    storageRespDTO.setHardwareUuid(hostId);
                    storageRespDTO.setStorageUuid(name + uuid + clusterId);
                    storageRespDTO.setPlatformId(platformconfigDTO.getId());
                    storageRespDTO.setPlatformName(platformconfigDTO.getName());
                    storageList.add(storageRespDTO);
                }
            }
            storageRespCreateReqDTOS.add(collectStorageInfo(platformconfigDTO, datastore, serviceInstance, createDateInfo, createTime));
        }
        serviceInstance.getServerConnection().logout();
        if (!storageRespCreateReqDTOS.isEmpty()) {
            updateStorage(platformconfigDTO, storageRespCreateReqDTOS);
        }
        if (!storageList.isEmpty()) {
            updateStorageHost(platformconfigDTO, storageList);
        }
    }

    private void updateStorageHost(PlatformconfigDTO platformconfigDTO, List<HardWareStorageRespDTO> storageList) {
        List<HardWareStorageRespDTO> oldList = hardWareStorageApi.getHardWareStorageByPlatformId(platformconfigDTO.getId()).getCheckedData();
        if (oldList.isEmpty()) {
            hardWareStorageApi.adds(storageList);
        } else {
            // 修改Map的key为uuid+hardwareUuid的组合
            Map<String, HardWareStorageRespDTO> existingHardwareMap = oldList.stream()
                    .collect(Collectors.toMap(
                            hardwareStorage -> hardwareStorage.getHardwareUuid() + "_" + hardwareStorage.getStorageUuid(),
                            hardwareStorage -> hardwareStorage
                    ));

            List<HardWareStorageRespDTO> newEntries = new ArrayList<>();
            List<HardWareStorageRespDTO> updatedEntries = new ArrayList<>();
            // 修改删除条件，同时比对uuid和hardwareUuid
            List<HardWareStorageRespDTO> deleteEntries = oldList.stream()
                    .filter(item -> !storageList.stream()
                            .anyMatch(newItem ->
                                    newItem.getHardwareUuid().equals(item.getHardwareUuid()) &&
                                            newItem.getStorageUuid().equals(item.getStorageUuid())
                            ))
                    .collect(Collectors.toList());

            for (HardWareStorageRespDTO hardWareStorageRespDTO : storageList) {
                // 使用组合key来查找
                String compositeKey = hardWareStorageRespDTO.getHardwareUuid() + "_" + hardWareStorageRespDTO.getStorageUuid();
                HardWareStorageRespDTO nicRespDTO = existingHardwareMap.get(compositeKey);
                if (nicRespDTO == null) {
                    newEntries.add(hardWareStorageRespDTO);
                } else if (!nicRespDTO.equals(hardWareStorageRespDTO)) {
                    updatedEntries.add(hardWareStorageRespDTO);
                }
            }
            hardWareStorageApi.updates(updatedEntries);
            hardWareStorageApi.adds(newEntries);
            if (!deleteEntries.isEmpty()) {
                hardWareStorageApi.deletes(deleteEntries);
            }
        }
    }

    private void updateStorage(PlatformconfigDTO platformconfigDTO, List<StorageRespCreateReqDTO> storageRespCreateReqDTOS) {
        List<StorageRespCreateReqDTO> dtos = storageInfoApi.getStorageByPlatformId(platformconfigDTO.getId());
        if (dtos.isEmpty()) {
            storageInfoApi.adds(storageRespCreateReqDTOS);
        } else {
            Map<String, StorageRespCreateReqDTO> existingStorageMap = dtos.stream()
                    .collect(Collectors.toMap(StorageRespCreateReqDTO::getUuid, storaget -> storaget));

            List<StorageRespCreateReqDTO> newEntries = new ArrayList<>();
            List<StorageRespCreateReqDTO> updateEntries = new ArrayList<>();
            List<StorageRespCreateReqDTO> deleteEntries = dtos.stream()
                    .filter(item -> !storageRespCreateReqDTOS.stream()
                            .map(StorageRespCreateReqDTO::getUuid)
                            .collect(Collectors.toList()).contains(item.getUuid()))
                    .collect(Collectors.toList());

            for (StorageRespCreateReqDTO storageRespCreateReqDTO : storageRespCreateReqDTOS) {
                StorageRespCreateReqDTO existingStroage = existingStorageMap.get(storageRespCreateReqDTO.getUuid());
                if (existingStroage == null) {
                    newEntries.add(storageRespCreateReqDTO);
                } else if (!existingStroage.equals(storageRespCreateReqDTO)) {
                    updateEntries.add(storageRespCreateReqDTO);
                }
            }
            if (!deleteEntries.isEmpty()) {
                deleteEntries.forEach(item -> item.setDeleted(1));
                storageInfoApi.deleteStorageList(deleteEntries);
            }
            storageInfoApi.updates(updateEntries);
            storageInfoApi.adds(newEntries);
        }

    }

    private StorageRespCreateReqDTO collectStorageInfo(PlatformconfigDTO platformconfigDTO, Datastore datastore, ServiceInstance serviceInstance, Map<String, Date> createDateInfo, Date createTime) {
        DatastoreSummary datastoreSummary = datastore.getSummary();
        String clusterId = datastore.getParent().getMOR().getVal();
        String clustername = datastore.getParent().getName();
        String uuid = datastoreSummary.getUrl();
        String state = mapState(datastore.getOverallStatus().toString());
        String status = mapStatus(datastore.getOverallStatus().toString());
        String type = datastoreSummary.getType();
        Long capacity = datastoreSummary.getCapacity();
        Long freespace = datastoreSummary.getFreeSpace();
        BigDecimal usedCapacity = new BigDecimal(0);
        Long uncommitted = datastoreSummary.getUncommitted();
        if (datastoreSummary.getUncommitted() != null) {
            usedCapacity = NumberUtil.add(NumberUtil.sub(capacity, freespace), uncommitted);
        } else {
            usedCapacity = NumberUtil.sub(capacity, freespace);
        }
        String name = datastore.getName();
        BigDecimal total = NumberUtil.sub(capacity, freespace);
        BigDecimal capacityUtilization = NumberUtil.div(total, capacity);

        Date createDate = createDateInfo.getOrDefault(uuid, createTime);
        if (!createDateInfo.containsKey(uuid)) {
            try {
                createDate = storageResourceSummary.getDatastoreCreationTime(serviceInstance, datastore);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        if (createDate == null) {
            try {
                createDate = storageResourceSummary.getDatastoreCreationTime(serviceInstance, datastore);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        if (createDate == null) {
            createDate = new Date();
        }

        // 计算虚拟容量
        BigDecimal availableDecimal = Convert.toBigDecimal(freespace).compareTo(new BigDecimal(0)) > 0 ? Convert.toBigDecimal(freespace) : new BigDecimal(0);
        BigDecimal allocation = Convert.toBigDecimal(capacity).subtract(availableDecimal);
        BigDecimal commitRate = allocation.divide(availableDecimal, 2, RoundingMode.HALF_UP);
        BigDecimal virtualCapacity = Convert.toBigDecimal(capacity);

        return new StorageRespCreateReqDTO()
                .setUuid(name + uuid + clusterId)
                .setName(name)
                .setUrl(uuid)
                .setState(state)
                .setType(type)
                .setStatus(status)
                .setCapacityUtilization(NumberUtil.mul(capacityUtilization, 100))
                .setUsedCapacity(Convert.toLong(usedCapacity))
                .setTotalCapacity(capacity)
                .setAvailablePhysicalCapacity(Convert.toBigDecimal(freespace))
                .setTotalPhysicalCapacity(Convert.toBigDecimal(capacity))
                .setAvailableCapacity(Convert.toBigDecimal(freespace))
                .setClusterUuid(clusterId)
                .setClusterName(clustername)
                .setTypeName("vmware")
                .setPlatformId(platformconfigDTO.getId())
                .setPlatformName(platformconfigDTO.getName())
                .setDeleted(0)
                .setCreateTime(createDate)
                .setSCreateTime(createDate)
                .setRegionId(platformconfigDTO.getRegionId())
                .setMediaType("机械盘")
                .setManager(platformconfigDTO.getName())
                .setAvailableManager(datastore.getParent().getParent().getName())
                .setAllocation(allocation)
                .setVirtualCapacity(virtualCapacity)
                .setCommitRate(commitRate)
                .setStoragePercent(new BigDecimal(1));
    }


    private String mapState(String state) {
        switch (state) {
            case "green":
                return "Enabled";
            case "red":
                return "Enabled";
            case "yellow":
                return "Enabled";
            case "gray":
                return "Gray";
            default:
                return "Unknown";
        }
    }

    private String mapStatus(String state) {
        switch (state) {
            case "green":
                return "Connected";
            case "red":
                return "Enabled";
            case "yellow":
                return "Connected";
            default:
                return "Unknown";
        }
    }
}
