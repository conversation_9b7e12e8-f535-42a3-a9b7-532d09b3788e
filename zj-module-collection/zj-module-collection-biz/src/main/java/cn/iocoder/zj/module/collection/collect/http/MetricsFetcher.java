package cn.iocoder.zj.module.collection.collect.http;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import cn.iocoder.zj.module.collection.handler.HttpClientWrapper;

import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;


/**
 * @ClassName : MetricsFetcher  //类名
 * @Description :  发送统一的接口请求 //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/5/17  17:28
 */
public class MetricsFetcher {

    HttpClientWrapper httpClientWrapper = new HttpClientWrapper();

    private static final Map<String, String> DEFAULT_HEADERS = createDefaultHeaders();
    private ExecutorService executor = Executors.newFixedThreadPool(10); // 根据实际需求调整线程池大小

    private static Map<String, String> createDefaultHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json;charset=UTF-8");
        return headers;
    }

    /**
     * 发送统一的接口请求
     *
     * @param url            请求的URL
     * @param method         请求方法，比如GET、POST
     * @param headers        请求头
     * @param params         请求的表单参数
     * @param token          身份验证Token
     * @param responseParser 响应解析器，可以根据不同API的响应格式进行定制解析
     * @return 解析后的响应内容作为字符串返回
     */
    public String sendRequest(String url, Method method, Map<String, String> headers, Map<String, Object> params,
                              String token, Function<HttpResponse, String> responseParser) {
        HttpRequest request = new HttpRequest(url);
        String type = headers.get("Authorization").toString();
        if (type.contains("accessKey")) {
            Map<String, Long> time = getCurrentAndOneMinuteLaterTimestamp();
            params.put("startTime", time.get("startTime"));
            params.put("endTime", time.get("endTime"));
            // 添加通用请求头
            headers.put("Authorization", "OAuth " + token);
            headers.put("Content-Type", "application/json;charset=UTF-8");
            headers.forEach(request::header);

            // 设置请求参数，分GET和POST处理
            if (method == Method.GET) {
                params.forEach((key, value) -> request.form(key, value.toString()));
            } else if (method == Method.POST) {
                request.body(JSONUtil.toJsonStr(params)); // 这里简化了，实际中可能需要将params转为JSON字符串
            }
            // 发送请求
            HttpResponse response = httpClientWrapper.execute(request);
            // 使用传入的响应解析器处理响应
            return responseParser.apply(response);
        }else {
            // 设置请求方法
            request.setMethod(method);
            Map<String, Long> time = getCurrentAndOneMinuteLaterTimestamp();
            params.put("startTime", time.get("startTime"));
            params.put("endTime", time.get("endTime"));
            // 添加通用请求头
            headers.put("Authorization", "OAuth " + token);
            headers.put("Content-Type", "application/json;charset=UTF-8");
            headers.forEach(request::header);

            // 设置请求参数，分GET和POST处理
            if (method == Method.GET) {
                params.forEach((key, value) -> request.form(key, value.toString()));
            } else if (method == Method.POST) {
                request.body(JSONUtil.toJsonStr(params)); // 这里简化了，实际中可能需要将params转为JSON字符串
            }

            // 发送请求
            HttpResponse response = request.execute();

            // 使用传入的响应解析器处理响应
            return responseParser.apply(response);
        }

    }


    public static Map<String, Long> getCurrentAndOneMinuteLaterTimestamp() {
        Map<String, Long> timestamps = new HashMap<>();

        // 获取当前时间的Calendar实例
        Calendar calendar = Calendar.getInstance();
        // 输出当前时间的时间戳（秒为单位）
        long startTime = calendar.getTimeInMillis() / 1000;

        // 将日历向前移动一分钟
        calendar.add(Calendar.MINUTE, 1);
        // 输出一分钟后的时间戳（秒为单位）
        long endTime = calendar.getTimeInMillis() / 1000;

        timestamps.put("startTime", startTime);
        timestamps.put("endTime", endTime);

        return timestamps;
    }


    /**
     * 云鸿统一的接口请求
     *
     * @param url            请求的URL
     * @param method         请求方法，比如GET、POST
     * @param headers        请求头
     * @param params         请求的表单参数
     * @param token          身份验证Token
     * @param responseParser 响应解析器，可以根据不同API的响应格式进行定制解析
     * @return 解析后的响应内容作为字符串返回
     */
    public String sendWinhongRequest(String url, Method method, Map<String, String> headers, Map<String, Object> params,
                                     String token, Function<HttpResponse, String> responseParser) {
        HttpRequest request = new HttpRequest(url);

        // 设置请求方法
        request.setMethod(method);
        if (!headers.isEmpty()) {
            headers.forEach(request::header);
        }
        // 设置请求参数，分GET和POST处理
        if (method == Method.GET) {
            params.forEach((key, value) -> request.form(key, value.toString()));
        } else if (method == Method.POST) {
            request.body(JSONUtil.toJsonStr(params)); // 这里简化了，实际中可能需要将params转为JSON字符串
        }
        request.cookie("SESSION=" + token);
        request.contentType("application/json;charset=UTF-8");

        // 发送请求
        HttpResponse response = request.execute();

        // 使用传入的响应解析器处理响应
        return responseParser.apply(response);
    }


    /**
     * FusionOne统一的接口请求
     *
     * @param url            请求的URL
     * @param method         请求方法，比如GET、POST
     * @param headers        请求头
     * @param params         请求的表单参数
     * @param token          身份验证Token
     * @param responseParser 响应解析器，可以根据不同API的响应格式进行定制解析
     * @return 解析后的响应内容作为字符串返回
     */
    public String sendFusionOneRequest(String url, Method method, Map<String, String> headers, Map<String, Object> params,
                                     String token, Function<HttpResponse, String> responseParser) {
        HttpRequest request = new HttpRequest(url);

        // 设置请求方法
        request.setMethod(method);
        if (!headers.isEmpty()) {
            headers.forEach(request::header);
        }
        // 设置请求参数，分GET和POST处理
        if (method == Method.GET) {
            params.forEach((key, value) -> request.form(key, value.toString()));
        } else if (method == Method.POST) {
            request.body(JSONUtil.toJsonStr(params)); // 这里简化了，实际中可能需要将params转为JSON字符串
        }
        request.cookie(token);

        // 发送请求
        HttpResponse response = request.execute();

        // 使用传入的响应解析器处理响应
        return responseParser.apply(response);
    }

    public String sendIstackRequest(String url, Method method, Map<String, Object> params, Function<HttpResponse, String> responseParser) {
        HttpRequest request = new HttpRequest(url);
        // 设置请求方法
        request.setMethod(method);
        // 设置请求参数，分GET和POST处理
        if (method == Method.GET) {
            params.forEach((key, value) -> request.form(key, value.toString()));
        } else if (method == Method.POST) {
            request.body(JSONUtil.toJsonStr(params));
            request.contentType("application/json;charset=UTF-8");
        }

        // 发送请求
        HttpResponse response = request.execute();

        // 使用传入的响应解析器处理响应
        return responseParser.apply(response);
    }
}
