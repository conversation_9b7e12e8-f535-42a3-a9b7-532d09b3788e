package cn.iocoder.zj.module.collection.service.vmware.cluster;

import cn.iocoder.zj.module.collection.collect.vmware.ConnectedVimServiceBase;
import cn.iocoder.zj.module.collection.service.vmware.AbstractComputerResourceSummary;
import com.vmware.vim25.ClusterComputeResourceSummary;
import com.vmware.vim25.mo.ClusterComputeResource;
import com.vmware.vim25.mo.Datastore;
import com.vmware.vim25.mo.InventoryNavigator;
import com.vmware.vim25.mo.ManagedEntity;

import java.rmi.RemoteException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @ClassName : ClusterComputerResourceSummary  //类名
 * @Description : 操作vcenter中的集群对象  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/7/24  11:36
 */
public class ClusterComputerResourceSummary extends AbstractComputerResourceSummary {

    /**
     * @return clusterList 集群对象集合
     * @description 获取vcenter中所有的集群对象
     */
    public List<ClusterComputeResource> getClusterList() throws RemoteException {
        List<ClusterComputeResource> clusterList = new ArrayList<ClusterComputeResource>();
        ClusterComputeResource clusterComputeResource = null;

        ManagedEntity[] managedEntities = new InventoryNavigator(cs.si.getRootFolder())
                .searchManagedEntities("ClusterComputeResource");
        if (managedEntities != null && managedEntities.length > 0) {
            for (ManagedEntity managedEntity : managedEntities) {
                clusterComputeResource = (ClusterComputeResource) managedEntity;
                clusterList.add(clusterComputeResource);
            }
        } else {
            return null;
        }
        return clusterList;
    }

    /**
     * @return clusterList 集群对象集合
     * @description 根据集群名称获取对应的集群对象
     */
    public List<ClusterComputeResource> getClusterListByName(List<String> ClustersName) throws RemoteException {
        List<ClusterComputeResource> clusterList = new ArrayList<ClusterComputeResource>();
        ClusterComputeResource clusterComputeResource = null;

        if (ClustersName == null || ClustersName.size() < 0) {
            return null;
        }
        List<ClusterComputeResource> clusterList2 = getClusterList();
        if (clusterList2 == null || clusterList2.size() < 0) {
            return null;
        }
        for (String string : ClustersName) {
            for (ClusterComputeResource clusterComputeResource2 : clusterList2) {
                if (clusterComputeResource2.getName().equals(string)) {
                    clusterList.add(clusterComputeResource);
                }
            }
        }

        return clusterList;
    }

    /**
     * @return clusterSumList 集群对象summary集合
     * @description 根据集群名称获取对应的集群summary
     */
    public List<ClusterComputeResourceSummary> getClusterComputeResourceSummary(List<String> ClustersName) throws RemoteException {
        List<ClusterComputeResourceSummary> clusterSumList = new ArrayList<ClusterComputeResourceSummary>();
        List<ClusterComputeResource> clusterListByName = null;

        clusterListByName = getClusterListByName(ClustersName);
        if (clusterListByName != null && clusterListByName.size() > 0) {
            for (ClusterComputeResource cluster : clusterListByName) {
                ClusterComputeResourceSummary summary = (ClusterComputeResourceSummary) cluster.getSummary();
                clusterSumList.add(summary);
            }
        } else {
            return null;
        }

        return clusterSumList;
    }

    /**
     * @return clusterDataStore 集群所关联的数据存储的集合
     * @description 根据集群名称获取集群关联的数据存储
     */
    public List<Datastore> getDataStoreByClusterNm(List<String> ClustersName) throws RemoteException {
        List<Datastore> clusterDataStore = new ArrayList<Datastore>();
        List<ClusterComputeResource> clusterListByName = null;

        clusterListByName = getClusterListByName(ClustersName);
        if (clusterListByName != null && clusterListByName.size() > 0) {
            for (ClusterComputeResource cluster : clusterListByName) {
                Datastore[] datastores = cluster.getDatastores();
                clusterDataStore.addAll(Arrays.asList(datastores));
            }
        } else {
            return null;
        }
        return clusterDataStore;
    }


    //集群测试方法
    public static void main(String[] args) throws RemoteException {
        ConnectedVimServiceBase cs = new ConnectedVimServiceBase();
        cs.connect("*************", "<EMAIL>", "Lihulin@123");
        ClusterComputerResourceSummary cluster = new ClusterComputerResourceSummary();
        cluster.setCs(cs);
        List<ClusterComputeResource> clusterList = cluster.getClusterList();

        ManagedEntity[] searchManagedEntities = new InventoryNavigator(cs.si.getRootFolder()).searchManagedEntities("HostSystem");

        ManagedEntity[] vm = new InventoryNavigator(cs.si.getRootFolder()).searchManagedEntities("VirtualMachine");

        if (searchManagedEntities != null && searchManagedEntities.length > 0) {
            for (ManagedEntity managedEntity : searchManagedEntities) {
                System.out.println(managedEntity.getName());
            }
        }

        if (vm != null && vm.length > 0) {
            for (ManagedEntity managedEntity : vm) {
                System.out.println(managedEntity.getName());
            }
        }

        List<String> clusterName = new ArrayList<>();
        if (clusterList != null && clusterList.size() > 0) {
            for (ClusterComputeResource clusterComputeResource : clusterList) {

                System.out.println(clusterComputeResource.getName());
            }
        }
        cs.disconnect();
    }


}
