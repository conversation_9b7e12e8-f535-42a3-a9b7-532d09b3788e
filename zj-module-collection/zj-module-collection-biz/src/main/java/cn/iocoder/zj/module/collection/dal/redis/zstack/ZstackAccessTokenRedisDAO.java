package cn.iocoder.zj.module.collection.dal.redis.zstack;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.module.collection.dal.dataobject.zstack.AlarmConfigInfo;
import cn.iocoder.zj.module.collection.dal.dataobject.zstack.ZstackLoginInfo;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.zj.module.collection.dal.redis.RedisKeyConstants.ZSTACK_ACCESS_TOKEN;

/**
 * @ClassName : ZstackAccessTokenRedisDAO  //类名
 * @Description : zstack 缓存  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/24  15:11
 */


@Repository
public class ZstackAccessTokenRedisDAO {

    @Resource
    private StringRedisTemplate stringRedisTemplate;


    public ZstackLoginInfo get(String accessToken) {
        String redisKey = formatKey(accessToken);
        return JsonUtils.parseObject(stringRedisTemplate.opsForValue().get(redisKey), ZstackLoginInfo.class);
    }

    public AlarmConfigInfo getAlarmInfo(String accessToken) {
        String redisKey = formatKey(accessToken);
        return JsonUtils.parseObject(stringRedisTemplate.opsForValue().get(redisKey), AlarmConfigInfo.class);
    }
    public void set(String accessToken, ZstackLoginInfo zstackLoginInfo) {
        String redisKey = formatKey(accessToken);
        if(zstackLoginInfo.getCreateDate() != null && zstackLoginInfo.getExpiredDate() != null){
            long time = LocalDateTimeUtil.between(zstackLoginInfo.getCreateDate(), zstackLoginInfo.getExpiredDate(), ChronoUnit.SECONDS);
            stringRedisTemplate.opsForValue().set(redisKey, JsonUtils.toJsonString(zstackLoginInfo), time, TimeUnit.SECONDS);
        }else {
            stringRedisTemplate.opsForValue().set(redisKey, JsonUtils.toJsonString(zstackLoginInfo), 1000, TimeUnit.SECONDS);
        }
    }

    public void delete(String accessToken) {
        String redisKey = formatKey(accessToken);
        stringRedisTemplate.delete(redisKey);
    }

    private static String formatKey(String accessToken) {
        return String.format(ZSTACK_ACCESS_TOKEN, accessToken);
    }
}
