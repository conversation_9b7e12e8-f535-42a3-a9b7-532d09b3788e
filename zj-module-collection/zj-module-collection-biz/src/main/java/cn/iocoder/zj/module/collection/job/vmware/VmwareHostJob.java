package cn.iocoder.zj.module.collection.job.vmware;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.module.collection.dal.dataobject.vmware.PerMonitorDO;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.framework.influx.config.InfluxDBTemplate;
import cn.iocoder.zj.module.collection.service.vmware.host.HostComputerResourceSummary;
import cn.iocoder.zj.module.collection.service.vmware.perf.RealtimePerfMonitor;
import cn.iocoder.zj.module.collection.util.SampleUtil;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import cn.iocoder.zj.module.monitor.api.hardware.HardWareInfoApi;
import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.hardwarenic.HardWareNicApi;
import cn.iocoder.zj.module.monitor.api.hardwarenic.HardWareNicRespDTO;
import cn.iocoder.zj.module.monitor.api.network.NetworkApi;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL2DTO;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL3DTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.vmware.vim25.*;
import com.vmware.vim25.mo.*;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.rmi.RemoteException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName : VmwareHostJob  //类名
 * @Description :   采集vmware宿主机数据//描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/7/25  11:03
 */

@Component
@Slf4j
public class VmwareHostJob {

    @Resource
    InfluxDBTemplate influxDBTemplate;
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    PlatformRedisDAO platformRedisDAO;
    @Resource
    HardWareInfoApi hardWareInfoApi;
    @Autowired
    private AlarmConfigApi alarmConfigApi;
    @Autowired
    private HardWareNicApi hardWareNicApi;
    @Resource
    NetworkApi networkApi;
    public final Map<Long, List<HardWareRespCreateReqDTO>> hardwareData = new ConcurrentHashMap<>();

    private final ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());


    public void vmHostJobList() throws Exception {
        vmwarehostInfo();
        vmwarehostPre();
    }

    @XxlJob("vmwarehostInfo")
    public void vmwarehostInfo() throws Exception {
        // 获取redis中数据并直接过滤
        List<PlatformconfigDTO> vmwarePlatforms = platformRedisDAO.get("platform").stream()
                .filter(dto -> "vmware".equals(dto.getTypeCode()))
                .collect(Collectors.toList());

        if (vmwarePlatforms.isEmpty()) {
            log.info("未找到vmware类型的平台配置");
            return;
        }

        for (PlatformconfigDTO platformconfigDTO : vmwarePlatforms) {
            if (platformconfigDTO.getId() == 18) {
                continue;
            }
            handlePlatformConfig(platformconfigDTO);
        }
    }

    private void handlePlatformConfig(PlatformconfigDTO platformconfigDTO) throws Exception {
        // 创建服务实例并获取主机列表
        ServiceInstance serviceInstance = null;
        try {
            serviceInstance = SampleUtil.createServiceInstance(
                    platformconfigDTO.getUrl(),
                    platformconfigDTO.getUsername(),
                    platformconfigDTO.getPassword()
            );

            List<HostSystem> systems = new HostComputerResourceSummary().getHostList(serviceInstance);
            if (systems.isEmpty()) {
                log.warn("平台 [{}] 未找到主机系统", platformconfigDTO.getName());
                return;
            }

            List<NetWorkL3DTO> netWorkL3DTOS = new ArrayList<>();
            List<NetWorkL2DTO> netWorkL2DTOs = new ArrayList<>();
            // 并行处理硬件信息和网卡信息
            List<HardWareRespCreateReqDTO> hardwareList = processHardwareInfo(serviceInstance, systems, platformconfigDTO);
            List<HardWareNicRespDTO> nicList = processHardwareNicInfo(systems, platformconfigDTO, netWorkL3DTOS, netWorkL2DTOs);

            if (!hardwareList.isEmpty()) {
                updateHardwareData(platformconfigDTO, hardwareList);
            }

            if (!nicList.isEmpty()) {
                updateHardwareNicData(platformconfigDTO, nicList);
            }

            if (!netWorkL3DTOS.isEmpty()) {
                // 处理L3网络数据
                processL3NetworkData(platformconfigDTO, netWorkL3DTOS);
            }

            if (!netWorkL2DTOs.isEmpty()) {
                updateNetWorkData(platformconfigDTO, netWorkL2DTOs);
            }
        } finally {
            // 确保服务实例正确登出
            if (serviceInstance != null) {
                try {
                    serviceInstance.getServerConnection().logout();
                } catch (Exception e) {
                    log.warn("服务实例登出失败: {}", e.getMessage());
                }
            }
        }
    }

    private void updateHardwareNicData(PlatformconfigDTO p, List<HardWareNicRespDTO> nicList) {
        List<HardWareNicRespDTO> oldNicList = hardWareNicApi.getHardwareNicByPlatformId(p.getId()).getCheckedData();

        if (oldNicList.isEmpty()) {
            hardWareNicApi.adds(nicList);
        } else {
            // 修改Map的key为uuid+hardwareUuid的组合
            Map<String, HardWareNicRespDTO> existingHardwareMap = oldNicList.stream()
                    .collect(Collectors.toMap(
                            hardwareNic -> hardwareNic.getL2NetworkUuid() + "_" + hardwareNic.getHardwareUuid(),
                            hardwareNic -> hardwareNic
                    ));

            List<HardWareNicRespDTO> newEntries = new ArrayList<>();
            List<HardWareNicRespDTO> updatedEntries = new ArrayList<>();
            // 修改删除条件，同时比对uuid和hardwareUuid
            List<HardWareNicRespDTO> deleteEntries = oldNicList.stream()
                    .filter(item -> !nicList.stream()
                            .anyMatch(newItem ->
                                    newItem.getL2NetworkUuid().equals(item.getL2NetworkUuid()) &&
                                            newItem.getHardwareUuid().equals(item.getHardwareUuid())
                            ))
                    .collect(Collectors.toList());

            for (HardWareNicRespDTO hardWareNicRespDTO : nicList) {
                // 使用组合key来查找
                String compositeKey = hardWareNicRespDTO.getL2NetworkUuid() + "_" + hardWareNicRespDTO.getHardwareUuid();
                HardWareNicRespDTO nicRespDTO = existingHardwareMap.get(compositeKey);
                if (nicRespDTO == null) {
                    newEntries.add(hardWareNicRespDTO);
                } else {
                    updatedEntries.add(hardWareNicRespDTO);
                }
            }

            hardWareNicApi.updates(updatedEntries);
            hardWareNicApi.adds(newEntries);
            if (!deleteEntries.isEmpty()) {
                hardWareNicApi.deletes(deleteEntries);
            }
        }
    }

    private List<HardWareNicRespDTO> collectHostNicInfo(PlatformconfigDTO platformconfigDTO, HostSystem hostSystem, List<NetWorkL3DTO> netWorkL3DTOS, List<NetWorkL2DTO> netWorkL2DTOs) {
        ArrayList<HardWareNicRespDTO> nicList = new ArrayList<>();
        try {
            // 获取基础信息
            String clusterUuid = hostSystem.getParent().getMOR().getVal();
            HostHardwareSummary hostHardwareSummary = hostSystem.getSummary().getHardware();
            String vms = hostSystem.getMOR().getVal();
            String hardwareUuid = clusterUuid + hostHardwareSummary.getUuid() + vms;

            // 获取网络相关信息
            PhysicalNic[] pnic = hostSystem.getConfig().getNetwork().getPnic();
            Network[] networks = hostSystem.getNetworks();
            HostVirtualNic[] vnic = hostSystem.getHostNetworkSystem().getNetworkInfo().getVnic();
            String mac = pnic.length > 0 ? pnic[0].getMac() : null;
            // 获取主机端口组信息
            List<Map<String, Object>> portGroupList = getHostPortGroups(hostSystem);
            // 处理分布式交换机
            Arrays.stream(networks).forEach(network -> {
                HardWareNicRespDTO nicRespDTO = new HardWareNicRespDTO();
                nicRespDTO.setHardwareUuid(hardwareUuid);
                String c_uuid = network.getParent().getMOR().getVal();
                String l2Uuid = c_uuid + network.getMOR().getVal() + network.getName();
                nicRespDTO.setL2NetworkUuid(l2Uuid);
                nicRespDTO.setUuid(l2Uuid + "_3");
                nicRespDTO.setL2NetworkName(network.getName());
                nicRespDTO.setState(true);
                nicRespDTO.setPlatformId(platformconfigDTO.getId());
                nicRespDTO.setPlatformName(platformconfigDTO.getName());
                if (mac != null) {
                    nicRespDTO.setMac(mac);
                }
                NetWorkL2DTO baseNetworkDTO = buildBaseNetworkDTO(network, platformconfigDTO, portGroupList);
                netWorkL2DTOs.add(baseNetworkDTO);
                nicList.add(nicRespDTO);
            });

            // 处理虚拟网卡和L3网络
            for (HostVirtualNic hostVirtualNic : vnic) {
                // 创建网卡信息
                HardWareNicRespDTO hardWareNicRespDTO = createNicFromVirtualNic(platformconfigDTO, hardwareUuid, mac, hostVirtualNic);
                nicList.add(hardWareNicRespDTO);

                NetWorkL2DTO dto = new NetWorkL2DTO();
                dto.setPhysicalInterface(hostVirtualNic.getDevice());
                dto.setVirtualNetworkId(0);
                // 设置平台信息
                setPlatformInfo(dto, platformconfigDTO);
                NetWorkL2DTO vnicDTO = buildVnicNetworkDTO(dto, hostVirtualNic, portGroupList);
                netWorkL2DTOs.add(vnicDTO);
                // 创建L3网络信息
                NetWorkL3DTO netWorkL3DTO = createL3NetworkFromVirtualNic(platformconfigDTO, hardWareNicRespDTO, hostVirtualNic);
                netWorkL3DTOS.add(netWorkL3DTO);
            }
        } catch (Exception e) {
            log.error("收集主机网卡信息失败: {}", e.getMessage(), e);
        }
        return nicList;
    }

    private NetWorkL2DTO buildVnicNetworkDTO(NetWorkL2DTO vnicDTO, HostVirtualNic vnic, List<Map<String, Object>> portGroupList) {
        vnicDTO.setUuid(vnic.getKey() + "_" + vnic.getSpec().ip.ipAddress);
        vnicDTO.setName(vnic.getDevice());

        // 设置VLAN信息
        setVlanInfo(vnicDTO, vnic.getDevice(), portGroupList);

        return vnicDTO;
    }

    private List<Map<String, Object>> getHostPortGroups(HostSystem hostSystem) throws RemoteException {
        HostPortGroup[] portGroups = hostSystem.getConfig().getNetwork().getPortgroup();
        List<Map<String, Object>> portGroupList = new ArrayList<>();

        for (HostPortGroup portGroup : portGroups) {
            String name = portGroup.getSpec().getName();
            // 检查是否已存在相同名称的端口组
            boolean exists = portGroupList.stream()
                    .anyMatch(item -> item.get("name").equals(name));
            if (!exists) {
                Map<String, Object> data = new HashMap<>();
                data.put("name", name);
                data.put("vlanid", portGroup.getSpec().getVlanId());
                portGroupList.add(data);
            }
        }
        return portGroupList;
    }

    private NetWorkL2DTO buildBaseNetworkDTO(Network network, PlatformconfigDTO platformConfig,
                                             List<Map<String, Object>> portGroupList) {
        String networkName = network.getName();
        String c_uuid = network.getParent().getMOR().getVal();
        String uuid = c_uuid + network.getMOR().getVal() + networkName;

        NetWorkL2DTO dto = new NetWorkL2DTO();
        dto.setUuid(uuid);
        dto.setName(networkName);
        dto.setPhysicalInterface(networkName);
        dto.setVirtualNetworkId(0);

        // 设置VLAN信息
        setVlanInfo(dto, networkName, portGroupList);

        // 设置平台信息
        setPlatformInfo(dto, platformConfig);

        return dto;
    }

    private void setVlanInfo(NetWorkL2DTO dto, String name, List<Map<String, Object>> portGroupList) {
        portGroupList.stream()
                .filter(map -> name.equals(Convert.toStr(map.get("name"))))
                .findFirst()
                .ifPresent(map -> dto.setVlan(Convert.toStr(map.get("vlanid"))));

        dto.setType(StrUtil.isNotEmpty(dto.getVlan()) ? "VLAN" : "FLAT");
    }

    private void setPlatformInfo(NetWorkL2DTO dto, PlatformconfigDTO platformConfig) {
        dto.setTypeName("vmware");
        dto.setPlatformName(platformConfig.getName());
        dto.setPlatformId(platformConfig.getId());
        dto.setRegionId(platformConfig.getRegionId());
        dto.setRegionName(platformConfig.getRegionName());
        dto.setTenantId(1L);
        dto.setCreateTime(new Date());
    }

    private List<HardWareRespCreateReqDTO> processHardwareInfo(ServiceInstance serviceInstance, List<HostSystem> systems, PlatformconfigDTO platformconfigDTO) throws Exception {
        List<HardWareRespCreateReqDTO> list = new ArrayList<>();
        for (HostSystem host : systems) {
            HardWareRespCreateReqDTO hardWareRespCreateReqDTO = collectHostInfo(platformconfigDTO, host, serviceInstance);
            list.add(hardWareRespCreateReqDTO);
        }
        return list;
    }

    private List<HardWareNicRespDTO> processHardwareNicInfo(
            List<HostSystem> systems, PlatformconfigDTO platformconfigDTO, List<NetWorkL3DTO> netWorkL3DTOS, List<NetWorkL2DTO> netWorkL2DTOs) {
        List<HardWareNicRespDTO> list = new ArrayList<>();
        for (HostSystem host : systems) {
            List<HardWareNicRespDTO> respDTOS = collectHostNicInfo(platformconfigDTO, host, netWorkL3DTOS, netWorkL2DTOs);
            list.addAll(respDTOS);
        }
        return list;
    }

    // 从虚拟网卡创建网卡信息
    private HardWareNicRespDTO createNicFromVirtualNic(PlatformconfigDTO platformconfigDTO, String hardwareUuid, String mac, HostVirtualNic hostVirtualNic) {
        HardWareNicRespDTO hardWareNicRespDTO = new HardWareNicRespDTO();
        hardWareNicRespDTO.setHardwareUuid(hardwareUuid);
        hardWareNicRespDTO.setPlatformId(platformconfigDTO.getId());
        hardWareNicRespDTO.setPlatformName(platformconfigDTO.getName());
        if (mac != null) {
            hardWareNicRespDTO.setMac(mac);
        }

        // 设置网络信息
        hardWareNicRespDTO.setIpSubnet(hostVirtualNic.getDevice());
        String subnetMask = hostVirtualNic.getSpec().ip.subnetMask;
        int prefixLength = getPrefixLength(subnetMask);
        hardWareNicRespDTO.setIpAddresses(hostVirtualNic.getSpec().ip.ipAddress + "/" + prefixLength);
        hardWareNicRespDTO.setState(hostVirtualNic.getSpec().tsoEnabled);
        hardWareNicRespDTO.setNetworkType("管理口");

        // 设置UUID和名称
        String l2NetworkUuid = hostVirtualNic.getKey() + "_" + hostVirtualNic.getSpec().ip.ipAddress;
        hardWareNicRespDTO.setL2NetworkUuid(l2NetworkUuid);
        hardWareNicRespDTO.setUuid(l2NetworkUuid + "_3");
        hardWareNicRespDTO.setL2NetworkName(hostVirtualNic.getDevice());

        return hardWareNicRespDTO;
    }

    // 从虚拟网卡创建L3网络信息
    private NetWorkL3DTO createL3NetworkFromVirtualNic(PlatformconfigDTO platformconfigDTO, HardWareNicRespDTO hardWareNicRespDTO, HostVirtualNic hostVirtualNic) {
        String subnetMask = hostVirtualNic.getSpec().ip.subnetMask;
        int prefixLength = getPrefixLength(subnetMask);

        NetWorkL3DTO netWorkL3DTO = new NetWorkL3DTO();
        netWorkL3DTO.setUuid(hardWareNicRespDTO.getL2NetworkUuid() + "_3");
        netWorkL3DTO.setL2NetworkUuid(hardWareNicRespDTO.getL2NetworkUuid());
        netWorkL3DTO.setName(hostVirtualNic.getDevice());
        netWorkL3DTO.setL2NetworkName(hardWareNicRespDTO.getL2NetworkName());
        netWorkL3DTO.setType("L3BasicNetwork");
        netWorkL3DTO.setNetworkCidr(hostVirtualNic.getSpec().ip.ipAddress + "/" + prefixLength);
        netWorkL3DTO.setNextHopIp(hostVirtualNic.getSpec().ip.ipAddress);
        netWorkL3DTO.setPlatformId(platformconfigDTO.getId());
        netWorkL3DTO.setPlatformName(platformconfigDTO.getName());
        netWorkL3DTO.setTenantId(platformconfigDTO.getTenantId());
        netWorkL3DTO.setTypeName("vmware");

        return netWorkL3DTO;
    }

    // 处理L3网络数据
    private void processL3NetworkData(PlatformconfigDTO platformconfigDTO, List<NetWorkL3DTO> netWorkL3DTOS) {
        if (netWorkL3DTOS.isEmpty()) {
            return;
        }

        // 获取分片信息
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);

        // 获取现有数据
        Long networkL3Count = networkApi.getNetWorkL3Count("vmware");
        List<NetWorkL3DTO> existingDtos = networkL3Count > 0 ?
                networkApi.getNetWorkL3List("vmware").getData() : Collections.emptyList();

        // 准备分片数据
        List<NetWorkL3DTO> shardingData = shardTotal < 0 ?
                netWorkL3DTOS : StringUtil.getShardingData(netWorkL3DTOS, shardTotal, shardIndex);

        // 记录处理信息
        shardingData.forEach(item -> log.info("Processing item: {}", item.getName()));

        if (networkL3Count == 0) {
            // 无现有数据，直接添加
            networkApi.addNetWorkL3(shardingData);
        } else {
            // 有现有数据，需要更新

            // 找出需要删除的数据
            List<String> newUuids = netWorkL3DTOS.stream()
                    .map(NetWorkL3DTO::getUuid)
                    .collect(Collectors.toList());

            List<NetWorkL3DTO> toDelete = existingDtos.stream()
                    .filter(item -> !newUuids.contains(item.getUuid()))
                    .collect(Collectors.toList());

            if (!toDelete.isEmpty()) {
                networkApi.deleteNetWorkL3ByNameList(toDelete);
            }

            // 更新现有数据
            networkApi.updateNetWorkL3(shardingData);

            // 找出需要新增的数据
            List<String> existingUuids = existingDtos.stream()
                    .map(NetWorkL3DTO::getUuid)
                    .collect(Collectors.toList());

            List<NetWorkL3DTO> toAdd = shardingData.stream()
                    .filter(item -> !existingUuids.contains(item.getUuid()))
                    .collect(Collectors.toList());

            if (!toAdd.isEmpty()) {
                networkApi.addNetWorkL3(toAdd);
            }
        }
    }

    /**
     * @description: 性能指标数据
     * <AUTHOR>
     * @date 2024/7/25 15:52
     * @version 1.0
     */
    @XxlJob("vmwarehostPre")
    public void vmwarehostPre() throws Exception {
        // 获取redis中数据
        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        // 取出只有vmware的数据
        List<PlatformconfigDTO> filteredList = new ArrayList<>();
        for (PlatformconfigDTO dto : platformconfigDTOList) {
            if ("vmware".equals(dto.getTypeCode())) {
                filteredList.add(dto);
            }
        }
        if (filteredList.isEmpty()) return;

        for (PlatformconfigDTO platformconfigDTO : filteredList) {
            if (platformconfigDTO.getId() == 18) {
                continue;
            }
            log.info("开始处理平台: {}", platformconfigDTO.getName());
            List<HardWareRespCreateReqDTO> list = hardWareInfoApi.getHardwareByPlatformId(platformconfigDTO.getId()).getCheckedData();
            BatchPoints batchPoints = processHardwarePre(list, platformconfigDTO);
            influxDBTemplate.writeBatch(batchPoints);
            log.info("平台 {} 处理完成", platformconfigDTO.getName());
        }
    }


    private BatchPoints processHardwarePre(List<HardWareRespCreateReqDTO> list, PlatformconfigDTO platformconfigDTO) throws Exception {
        BatchPoints batchPoints = BatchPoints.builder().build();
        for (HardWareRespCreateReqDTO hardWareRespCreateReqDTO : list) {
            // 创建服务实例并获取主机列表
            ServiceInstance serviceInstance = null;
            try {
                serviceInstance = SampleUtil.createServiceInstance(
                        platformconfigDTO.getUrl(),
                        platformconfigDTO.getUsername(),
                        platformconfigDTO.getPassword()
                );

                collectMetricsForHost(platformconfigDTO, hardWareRespCreateReqDTO, batchPoints, serviceInstance);
            } finally {
                // 确保服务实例正确登出
                if (serviceInstance != null) {
                    try {
                        serviceInstance.getServerConnection().logout();
                    } catch (Exception e) {
                        log.warn("服务实例登出失败: {}", e.getMessage());
                    }
                }
            }
        }
        return batchPoints;
    }

    private void collectMetricsForHost(PlatformconfigDTO platformconfigDTO, HardWareRespCreateReqDTO hardWareRespCreateReqDTO, BatchPoints batchPoints, ServiceInstance serviceInstance) throws Exception {
        BigDecimal memSize = Convert.toBigDecimal(hardWareRespCreateReqDTO.getTotalMemoryCapacity());

        ManagedEntity managedEntities = new InventoryNavigator(serviceInstance.getRootFolder())
                .searchManagedEntity("HostSystem", hardWareRespCreateReqDTO.getName());
        HostSystem hostSystem = (HostSystem) managedEntities;
        Datastore[] datastores = hostSystem.getDatastores();
        Long storage_total = 0L;
        Long storage_total_free = 0L;
        for (Datastore datastore : datastores) {
            storage_total += datastore.getSummary().getCapacity();
            storage_total_free += datastore.getSummary().getFreeSpace();
        }
        BigDecimal storage_usage = NumberUtil.sub(storage_total, storage_total_free);
        BigDecimal storage_rate = NumberUtil.div(storage_total_free, storage_total);


        String clusterUuid = hostSystem.getParent().getMOR().getVal();
        String uuid = hardWareRespCreateReqDTO.getUuid();

        // ==========================================CPU==================================================
        // cpu.usage.none CPUUsedUtilization
        List<PerMonitorDO> CPUUsedUtilization = RealtimePerfMonitor.getPerEntityMericBasesByname(hardWareRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.usage.none", platformconfigDTO);
        if (!CPUUsedUtilization.isEmpty()) {
            for (PerMonitorDO perMonitorDO : CPUUsedUtilization) {

                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                BigDecimal bigDecimal = new BigDecimal(0);
                if (perMonitorDO.getValue() != 0) {
                    bigDecimal = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 100);
                }
                if (bigDecimal.signum() == -1) {
                    bigDecimal = new BigDecimal(0);
                }

                Point point = Point.measurement("zj_cloud_hardware")
                        .tag("label", "cpu")
                        .tag("uuid", uuid)
                        .tag("metricName", "CPUUsedUtilization")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("host_type", "0")
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hardWareRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("host_metricName", "CPUUsedUtilization")
                        .addField("type", "CPUUsedUtilization")
                        .addField("value", bigDecimal)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }

        }


        // cpu.usage.average CPUAverageUsedUtilization
        List<PerMonitorDO> CPUAverageUsedUtilization = RealtimePerfMonitor.getPerEntityMericBasesByname(hardWareRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.usage.average", platformconfigDTO);
        if (!CPUAverageUsedUtilization.isEmpty()) {
            for (PerMonitorDO perMonitorDO : CPUAverageUsedUtilization) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                Point point = Point.measurement("zj_cloud_hardware")
                        .tag("label", "cpu")
                        .tag("uuid", uuid)
                        .tag("metricName", "CPUAverageUsedUtilization")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("host_type", "CPUAverageUsedUtilization")
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hardWareRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("host_metricName", "CPUAverageUsedUtilization")
                        .addField("type", "CPUAverageUsedUtilization")
                        .addField("value", NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 100, 2))
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }


        // cpu.idle.summation CPUIdleUtilization
        List<PerMonitorDO> CPUIdleUtilization = RealtimePerfMonitor.getPerEntityMericBasesByname(hardWareRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.idle.summation", platformconfigDTO);
        if (!CPUIdleUtilization.isEmpty()) {
            for (PerMonitorDO perMonitorDO : CPUIdleUtilization) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                Point point = Point.measurement("zj_cloud_hardware")
                        .tag("label", "cpu")
                        .tag("uuid", uuid)
                        .tag("metricName", "CPUIdleUtilization")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("host_type", "CPUIdleUtilization")
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hardWareRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("host_metricName", "CPUIdleUtilization")
                        .addField("type", "CPUIdleUtilization")
                        .addField("value", Convert.toBigDecimal(perMonitorDO.getValue()))
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }

        // cpu.usage.none CPUAllUsedUtilization
        List<PerMonitorDO> CPUAllUsedUtilization = RealtimePerfMonitor.getPerEntityMericBasesByname(hardWareRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.usage.none", platformconfigDTO);
        if (CPUAllUsedUtilization.size() > 0) {
            for (PerMonitorDO perMonitorDO : CPUAllUsedUtilization) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                Point point = Point.measurement("zj_cloud_hardware")
                        .tag("label", "cpu")
                        .tag("uuid", uuid)
                        .tag("metricName", "CPUAllUsedUtilization")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("host_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hardWareRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("host_metricName", "CPUAllUsedUtilization")
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 100))
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }


        // cpu.utilization.average CPUSystemUtilization
        List<PerMonitorDO> CPUSystemUtilization = RealtimePerfMonitor.getPerEntityMericBasesByname(hardWareRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.utilization.average", platformconfigDTO);
        if (CPUSystemUtilization.size() > 0) {
            for (PerMonitorDO perMonitorDO : CPUSystemUtilization) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                Point point = Point.measurement("zj_cloud_hardware")
                        .tag("label", "cpu")
                        .tag("uuid", uuid)
                        .tag("metricName", "CPUSystemUtilization")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("host_type", "CPUSystemUtilization")
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hardWareRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("host_metricName", "CPUSystemUtilization")
                        .addField("type", "CPUSystemUtilization")
                        .addField("value", Convert.toBigDecimal(perMonitorDO.getValue()))
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }
        // cpu.used.summation
        List<PerMonitorDO> cpuUsedList = RealtimePerfMonitor.getPerEntityMericBasesByname(hardWareRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.used.summation", platformconfigDTO);
        Long valueAll = 0L;
        if (cpuUsedList.size() > 0) {
            for (PerMonitorDO perMonitorDO : cpuUsedList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    valueAll += valueAll + perMonitorDO.getValue();
                }
            }
        }

        // cpu.wait.summation
        List<PerMonitorDO> cpuWaitList = RealtimePerfMonitor.getPerEntityMericBasesByname(hardWareRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.wait.summation", platformconfigDTO);
        if (!cpuWaitList.isEmpty()) {
            for (PerMonitorDO perMonitorDO : cpuWaitList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                BigDecimal value = NumberUtil.div(perMonitorDO.getValue(), valueAll, 2);
                Point point = Point.measurement("zj_cloud_hardware")
                        .tag("label", "cpu")
                        .tag("uuid", uuid)
                        .tag("metricName", "CPUWaitUtilization")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("host_type", "CPUWaitUtilization")
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hardWareRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("host_metricName", "CPUWaitUtilization")
                        .addField("type", "CPUWaitUtilization")
                        .addField("value", value)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }


        // ==========================================内存==================================================
        List<PerMonitorDO> memUsageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hardWareRespCreateReqDTO.getName(), serviceInstance, managedEntities, "mem.usage.average", platformconfigDTO);
        for (PerMonitorDO perMonitorDO : memUsageList) {
            BigDecimal value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 100);
            String label = perMonitorDO.getInstance();
            String type = label + "MemoryUsedInPercent";
            Point point = Point.measurement("zj_cloud_hardware")
                    .tag("label", "mem")
                    .tag("uuid", uuid)
                    .tag("metricName", "MemoryUsedInPercent")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("host_type", type)
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hardWareRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("host_metricName", "MemoryUsedInPercent")
                    .addField("type", "MemoryUsedInPercent")
                    .addField("value", value)
                    .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);

        }


        List<PerMonitorDO> memActiveList = RealtimePerfMonitor.getPerEntityMericBasesByname(hardWareRespCreateReqDTO.getName(), serviceInstance, managedEntities, "mem.consumed.average", platformconfigDTO);
        for (PerMonitorDO perMonitorDO : memActiveList) {
            String label = perMonitorDO.getInstance();

            String type = label + "MemoryUsedBytes";
            BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 1024);
            Point point = Point.measurement("zj_cloud_hardware")
                    .tag("label", "mem")
                    .tag("uuid", uuid)
                    .tag("metricName", "MemoryUsedBytes")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("host_type", type)
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hardWareRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("host_metricName", "MemoryUsedBytes")
                    .addField("type", "MemoryUsedBytes")
                    .addField("value", value)
                    .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();

            BigDecimal subtract = memSize.subtract(value);
            String type1 = label + "MemoryFreeBytes";
            Point point1 = Point.measurement("zj_cloud_hardware")
                    .tag("label", "mem")
                    .tag("uuid", uuid)
                    .tag("metricName", "MemoryFreeBytes")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("host_type", type1)
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hardWareRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("host_metricName", "MemoryFreeBytes")
                    .addField("type", "MemoryFreeBytes")
                    .addField("value", subtract)
                    .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
            batchPoints.point(point1);
        }


        // ==========================================硬盘==================================================
        List<PerMonitorDO> diskUsageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hardWareRespCreateReqDTO.getName(), serviceInstance, managedEntities, "disk.usage.average", platformconfigDTO);
        for (PerMonitorDO perMonitorDO : diskUsageList) {
            if (perMonitorDO.getInstance().isEmpty()) {
                perMonitorDO.setInstance("0");
            }

            Point statDiskUsage = Point.measurement("zj_cloud_hardware")
                    .tag("label", "disk")
                    .tag("uuid", uuid)
                    .tag("metricName", "DiskAllUsedCapacityInBytes")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("host_type", "DiskAllUsedCapacityInBytes")
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hardWareRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("host_metricName", "DiskAllUsedCapacityInBytes")
                    .addField("type", "DiskAllUsedCapacityInBytes")
                    .addField("value", Convert.toBigDecimal(storage_usage))
                    .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(statDiskUsage);

            Point inPercent = Point.measurement("zj_cloud_hardware")
                    .tag("label", "disk")
                    .tag("uuid", uuid)
                    .tag("metricName", "DiskAllUsedCapacityInPercent")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("host_type", "DiskAllUsedCapacityInPercent")
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hardWareRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("host_metricName", "DiskAllUsedCapacityInPercent")
                    .addField("type", "DiskAllUsedCapacityInPercent")
                    .addField("value", NumberUtil.mul(storage_rate, 100))
                    .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(inPercent);
        }


        List<PerMonitorDO> diskWriteList = RealtimePerfMonitor.getPerEntityMericBasesByname(hardWareRespCreateReqDTO.getName(), serviceInstance, managedEntities, "disk.write.average", platformconfigDTO);
        if (!diskWriteList.isEmpty()) {
            BigDecimal disk_write_all = new BigDecimal(0);
            for (PerMonitorDO perMonitorDO : diskWriteList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                String metricName = "DiskReadBytes";
                BigDecimal value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.mul(value, 1024);
                if (lastValue.signum() == -1) {
                    lastValue = new BigDecimal(0);
                }
                disk_write_all = disk_write_all.add(lastValue);
                Point point = Point.measurement("zj_cloud_hardware")
                        .tag("label", "disk")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("host_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hardWareRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("host_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
            Point point = Point.measurement("zj_cloud_hardware")
                    .tag("label", "disk")
                    .tag("uuid", uuid)
                    .tag("metricName", "DiskAllReadBytes")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("host_type", "DiskAllReadBytes")
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hardWareRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("host_metricName", "DiskAllReadBytes")
                    .addField("type", "DiskAllReadBytes")
                    .addField("value", disk_write_all)
                    .time(diskWriteList.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }

        List<PerMonitorDO> diskReadList = RealtimePerfMonitor.getPerEntityMericBasesByname(hardWareRespCreateReqDTO.getName(), serviceInstance, managedEntities, "disk.read.average", platformconfigDTO);
        if (!diskReadList.isEmpty()) {
            BigDecimal disk_read_all = new BigDecimal(0);
            for (PerMonitorDO perMonitorDO : diskReadList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                String metricName = "DiskWriteBytes";
                BigDecimal value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.mul(value, 1024);
                disk_read_all = disk_read_all.add(lastValue);
                Point point = Point.measurement("zj_cloud_hardware")
                        .tag("label", "disk")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("host_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hardWareRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("host_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
            Point point = Point.measurement("zj_cloud_hardware")
                    .tag("label", "disk")
                    .tag("uuid", uuid)
                    .tag("metricName", "DiskAllWriteBytes")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("host_type", "DiskAllWriteBytes")
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hardWareRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("host_metricName", "DiskAllWriteBytes")
                    .addField("type", "DiskAllWriteBytes")
                    .addField("value", disk_read_all)
                    .time(diskReadList.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }


        List<PerMonitorDO> iopsReadList = RealtimePerfMonitor.getPerEntityMericBasesByname(hardWareRespCreateReqDTO.getName(), serviceInstance, managedEntities, "disk.numberRead.summation", platformconfigDTO);
        for (PerMonitorDO perMonitorDO : iopsReadList) {
            if (perMonitorDO.getInstance().isEmpty()) {
                perMonitorDO.setInstance("0");
            }
            String metricName = "DiskReadOps";
            Point point = Point.measurement("zj_cloud_hardware")
                    .tag("label", "disk")
                    .tag("uuid", uuid)
                    .tag("metricName", metricName)
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("host_type", perMonitorDO.getInstance())
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hardWareRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("host_metricName", metricName)
                    .addField("type", perMonitorDO.getInstance())
                    .addField("value", Convert.toBigDecimal(perMonitorDO.getValue()))
                    .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }

        List<PerMonitorDO> iopsWriteList = RealtimePerfMonitor.getPerEntityMericBasesByname(hardWareRespCreateReqDTO.getName(), serviceInstance, managedEntities, "disk.numberWrite.summation", platformconfigDTO);
        if (!iopsWriteList.isEmpty()) {
            for (PerMonitorDO perMonitorDO : iopsWriteList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                String metricName = "DiskWriteOps";
                Point point = Point.measurement("zj_cloud_hardware")
                        .tag("label", "disk")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("host_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hardWareRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("host_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", Convert.toBigDecimal(perMonitorDO.getValue()))
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }

        // ==========================================网络==================================================
        List<PerMonitorDO> netBytesRxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hardWareRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.bytesRx.average", platformconfigDTO);
        if (!netBytesRxAverageList.isEmpty()) {
            BigDecimal net_rx_all = new BigDecimal(0);
            for (PerMonitorDO perMonitorDO : netBytesRxAverageList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                String metricName = "NetworkInBytes";
                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.mul(value, 1024);
                net_rx_all = net_rx_all.add(lastValue);
                Point point = Point.measurement("zj_cloud_hardware")
                        .tag("label", "net")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("host_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hardWareRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("host_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }


            Point point = Point.measurement("zj_cloud_hardware")
                    .tag("label", "net")
                    .tag("uuid", uuid)
                    .tag("metricName", "NetworkAllInBytes")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("host_type", "NetworkAllInBytes")
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hardWareRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("host_metricName", "NetworkAllInBytes")
                    .addField("type", "NetworkAllInBytes")
                    .addField("value", NumberUtil.mul(NumberUtil.div(net_rx_all, 8), 1024))
                    .time(netBytesRxAverageList.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);

        }


        List<PerMonitorDO> netBytesTxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hardWareRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.bytesTx.average", platformconfigDTO);
        if (!netBytesTxAverageList.isEmpty()) {
            BigDecimal net_tx_all = new BigDecimal(0);
            for (PerMonitorDO perMonitorDO : netBytesTxAverageList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                String metricName = "NetworkOutBytes";
                BigDecimal value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.mul(value, 1024);
                net_tx_all = net_tx_all.add(lastValue);
                Point point = Point.measurement("zj_cloud_hardware")
                        .tag("label", "net")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("host_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hardWareRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("host_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }

            Point point = Point.measurement("zj_cloud_hardware")
                    .tag("label", "net")
                    .tag("uuid", uuid)
                    .tag("metricName", "NetworkAllOutBytes")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("host_type", "NetworkAllOutBytes")
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hardWareRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("host_metricName", "NetworkAllOutBytes")
                    .addField("type", "NetworkAllOutBytes")
                    .addField("value", NumberUtil.mul(NumberUtil.mul(net_tx_all, 8), 1024))
                    .time(netBytesTxAverageList.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);

        }

        List<PerMonitorDO> netPacketsRxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hardWareRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.packetsRx.summation", platformconfigDTO);
        if (!netPacketsRxAverageList.isEmpty()) {
            for (PerMonitorDO perMonitorDO : netPacketsRxAverageList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                String metricName = "NetworkInPackets";
                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.div(value, 1024);
                Point point = Point.measurement("zj_cloud_hardware")
                        .tag("label", "net")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("host_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hardWareRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("host_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }

        List<PerMonitorDO> netPacketsTxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hardWareRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.packetsTx.summation", platformconfigDTO);
        if (!netPacketsTxAverageList.isEmpty()) {
            for (PerMonitorDO perMonitorDO : netPacketsTxAverageList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                String metricName = "NetworkOutPackets";
                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.div(value, 1024);
                Point point = Point.measurement("zj_cloud_hardware")
                        .tag("label", "net")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("host_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hardWareRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("host_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }

        List<PerMonitorDO> netDroppedRxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hardWareRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.droppedRx.summation", platformconfigDTO);
        if (!netDroppedRxAverageList.isEmpty()) {
            for (PerMonitorDO perMonitorDO : netDroppedRxAverageList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                String metricName = "NetworkInErrors";
                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.div(value, 1024);
                Point point = Point.measurement("zj_cloud_hardware")
                        .tag("label", "net")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("host_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hardWareRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("host_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }

        List<PerMonitorDO> netDroppedTxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hardWareRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.droppedTx.summation", platformconfigDTO);
        if (!netDroppedTxAverageList.isEmpty()) {
            for (PerMonitorDO perMonitorDO : netDroppedTxAverageList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                String metricName = "NetworkOutErrors";
                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.div(value, 1024);
                Point point = Point.measurement("zj_cloud_hardware")
                        .tag("label", "net")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("host_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hardWareRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("host_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }

    }

    private HardWareRespCreateReqDTO collectHostInfo(PlatformconfigDTO platformconfigDTO, HostSystem hostSystem, ServiceInstance serviceInstance) throws Exception {
        HostHardwareSummary hostHardwareSummary = hostSystem.getSummary().getHardware();
        BigDecimal memorySize = Convert.toBigDecimal(hostHardwareSummary.getMemorySize());
        BigDecimal cpusize = NumberUtil.div(
                NumberUtil.mul(Convert.toBigDecimal(hostHardwareSummary.getCpuMhz()), Convert.toBigDecimal(hostHardwareSummary.getNumCpuCores())),
                1000,
                2
        );
        BigDecimal availableCpuCapacity = NumberUtil.div(Convert.toBigDecimal(hostSystem.getSummary().getQuickStats().getOverallCpuUsage()), 1000, 2);
        BigDecimal hostMemoryUsage = NumberUtil.mul(Convert.toBigDecimal(hostSystem.getSummary().getQuickStats().getOverallMemoryUsage()), 1024 * 1024);
        BigDecimal availableMemoryCapacity = NumberUtil.sub(memorySize, hostMemoryUsage);
        String vms = hostSystem.getMOR().getVal();
        String state = mapPowerState(hostSystem.getRuntime().getPowerState().name());
        String ip = hostSystem.getName();
        String status = StrUtil.upperFirst(hostSystem.getRuntime().getConnectionState().name());
        String clusterUuid = hostSystem.getParent().getMOR().getVal();
        String clustername = hostSystem.getParent().getName();
        String name = hostSystem.getParent().getParent().getParent().getName();
        String architecture = "x86_64";
        BigDecimal memoryUsed = NumberUtil.mul(NumberUtil.div(hostMemoryUsage, memorySize), 100);
        ManagedEntity managedEntities = new InventoryNavigator(serviceInstance.getRootFolder())
                .searchManagedEntity("HostSystem", hostSystem.getName());

        double cpuUsage = calculateCpuUsage(managedEntities, hostSystem.getName(), serviceInstance, platformconfigDTO);
        long networkRx = calculateNetworkUsage(managedEntities, hostSystem.getName(), serviceInstance, "net.bytesRx.average", platformconfigDTO);
        long networkTx = calculateNetworkUsage(managedEntities, hostSystem.getName(), serviceInstance, "net.bytesTx.average", platformconfigDTO);
        long packetsRx = calculateNetworkUsage(managedEntities, hostSystem.getName(), serviceInstance, "net.packetsRx.summation", platformconfigDTO);
        double totalCapacity = 0;
        double freeCapacity = 0;
        Datastore[] datastores = hostSystem.getDatastores();
        for (Datastore datastore : datastores) {
            totalCapacity += datastore.getSummary().getCapacity();
            freeCapacity += datastore.getSummary().getFreeSpace();
        }
        double storageUsage = NumberUtil.sub(totalCapacity, freeCapacity);
        double diskUsed = NumberUtil.mul(NumberUtil.div(storageUsage, totalCapacity), 100);

        Integer cpuNum = Convert.toInt(hostHardwareSummary.getNumCpuThreads());


        //系统预留内存
        ResourceConfigSpec config = hostSystem.getSystemResources().config;
        Long reservation = config.memoryAllocation.reservation;

        HardWareRespCreateReqDTO dto = new HardWareRespCreateReqDTO();

        BigDecimal cpuCommitRate = cpusize.subtract(availableCpuCapacity).divide(new BigDecimal(cpuNum), 3, BigDecimal.ROUND_HALF_UP);
        dto.setCpuCommitRate(cpuCommitRate);

        BigDecimal memCommitRate = memorySize.subtract(availableMemoryCapacity).divide(memorySize, 3, BigDecimal.ROUND_HALF_UP);
        dto.setMemoryCommitRate(memCommitRate);

        dto.setCpuOverPercent(Convert.toBigDecimal(1));
        dto.setMemoryOverPercent(Convert.toBigDecimal(1));

        //品牌名称
        HostSystemInfo systemInfo = hostSystem.getHardware().getSystemInfo();
        String vendor = systemInfo.getVendor();
        dto.setBrandName(vendor);
        String model = systemInfo.getModel();
        dto.setModel(model);
        //序列号
        HostSystemIdentificationInfo[] identifyingInfo = systemInfo.getOtherIdentifyingInfo();
        String serialNumber = "-";
        if (identifyingInfo != null) {
            for (HostSystemIdentificationInfo info : identifyingInfo) {
                if ("ServiceTag".equals(info.getIdentifierType().getKey())) {
                    serialNumber = info.getIdentifierValue();
                    break;
                }
            }
        }
        dto.setSerialNumber(serialNumber);
        dto.setCpuType(hostSystem.getHardware().getCpuPkg()[0].getDescription());

        dto.setReservedMemory(Convert.toBigDecimal(reservation).multiply(new BigDecimal(1024 * 1024)));
        dto.setUuid(clusterUuid + hostHardwareSummary.getUuid() + vms);
        dto.setName(hostSystem.getName());
        dto.setState(state);
        dto.setIp(ip);
        dto.setStatus(status);
        dto.setClusterUuid(clusterUuid);
        dto.setClusterName(clustername);
        dto.setTotalCpuCapacity(Convert.toLong(cpusize));
        dto.setAvailableCpuCapacity(Convert.toLong(availableCpuCapacity));
        dto.setMemoryUsed(memoryUsed);
        dto.setCpuUsed(Convert.toBigDecimal(cpuUsage));
        dto.setDiskUsed(Convert.toBigDecimal(diskUsed));
        dto.setDiskUsedBytes(Convert.toBigDecimal(storageUsage));
        dto.setDiskFreeBytes(Convert.toBigDecimal(freeCapacity));
        dto.setTotalDiskCapacity(Convert.toBigDecimal(totalCapacity));
        dto.setCpuSockets(Convert.toInt(hostHardwareSummary.getNumCpuPkgs()));
        dto.setArchitecture(architecture);
        dto.setCpuNum(cpuNum);
        dto.setTotalMemoryCapacity(Convert.toLong(memorySize));
        dto.setAvailableMemoryCapacity(Convert.toLong(availableMemoryCapacity));
        dto.setDeleted(0);
        dto.setVms(vms);
        dto.setTypeName("vmware");
        dto.setBandwidthUpstream(Convert.toBigDecimal(networkRx));
        dto.setBandwidthDownstream(Convert.toBigDecimal(networkTx));
        dto.setPacketRate(Convert.toBigDecimal(packetsRx));
        dto.setRegionId(platformconfigDTO.getRegionId());
        dto.setManager(platformconfigDTO.getName());
        dto.setAvailableManager(name);
        dto.setPlatformId(platformconfigDTO.getId());
        dto.setPlatformName(platformconfigDTO.getName());

        return dto;
    }

    public String generateMacAddress() {
        Random random = new Random();
        byte[] macAddr = new byte[6];
        random.nextBytes(macAddr);

        // Set the first byte to a random value and ensure the second least significant bit is 0
        macAddr[0] = (byte) (macAddr[0] & (byte) 254);

        StringBuilder sb = new StringBuilder(18);
        for (byte b : macAddr) {
            sb.append(String.format("%02X:", b));
        }
        sb.deleteCharAt(sb.length() - 1); // Remove the last colon
        return sb.toString();
    }

    private int getPrefixLength(String subnetMask) {
        String[] octets = subnetMask.split("\\.");
        int prefixLength = 0;

        for (String octet : octets) {
            int value = Integer.parseInt(octet);
            // Count the number of 1 bits in the octet
            prefixLength += Integer.bitCount(value);
        }

        return prefixLength;
    }

    private void updateHardwareData(PlatformconfigDTO platformconfigDTO, List<HardWareRespCreateReqDTO> hardWareRespCreateReqDTOS) {
        List<HardWareRespCreateReqDTO> existingHardwareList = hardWareInfoApi.getHardwareByPlatformId(platformconfigDTO.getId()).getCheckedData();

        if (existingHardwareList.isEmpty()) {
            hardWareInfoApi.adds(hardWareRespCreateReqDTOS);
        } else {
            Map<String, HardWareRespCreateReqDTO> existingHardwareMap = existingHardwareList.stream()
                    .collect(Collectors.toMap(HardWareRespCreateReqDTO::getUuid, hardware -> hardware));

            List<HardWareRespCreateReqDTO> newEntries = new ArrayList<>();
            List<HardWareRespCreateReqDTO> updatedEntries = new ArrayList<>();
            List<HardWareRespCreateReqDTO> deleteEntries = existingHardwareList.stream()
                    .filter(item -> !hardWareRespCreateReqDTOS.stream()
                            .map(HardWareRespCreateReqDTO::getUuid)
                            .collect(Collectors.toList()).contains(item.getUuid())).collect(Collectors.toList());


            for (HardWareRespCreateReqDTO newHardware : hardWareRespCreateReqDTOS) {
                HardWareRespCreateReqDTO existingHardware = existingHardwareMap.get(newHardware.getUuid());
                if (existingHardware == null) {
                    newEntries.add(newHardware);
                } else if (!existingHardware.equals(newHardware)) {
                    updatedEntries.add(newHardware);
                }
            }
            if (!deleteEntries.isEmpty()) {

                deleteEntries.forEach(item -> item.setDeleted(1));
                hardWareInfoApi.updates(deleteEntries);
                final Long alertId = alarmConfigApi.getMaxAlertId().getData();
                // 资源删除触发告警
                List<AlarmDorisReqDTO> toInsert = new ArrayList<>();
                int i = 1;
                for (HardWareRespCreateReqDTO hardWareRespCreateReqDTO : deleteEntries) {
                    AlarmDorisReqDTO collectorAlert = new AlarmDorisReqDTO();
                    collectorAlert.setPriority(0);
                    collectorAlert.setStatus(0);
                    collectorAlert.setIsSolved(0);
                    collectorAlert.setFirstAlarmTime(new Date().getTime());
                    collectorAlert.setGmtCreate(new Date());
                    collectorAlert.setGmtUpdate(new Date());
                    collectorAlert.setLastAlarmTime(new Date().getTime());
                    collectorAlert.setMonitorName(hardWareRespCreateReqDTO.getName());
                    collectorAlert.setMonitorId(Convert.toStr(hardWareRespCreateReqDTO.getUuid()));
                    collectorAlert.setPlatformName(hardWareRespCreateReqDTO.getPlatformName());
                    collectorAlert.setPlatformId(hardWareRespCreateReqDTO.getPlatformId());
                    collectorAlert.setContent(String.format("该宿主机资源(%s)已被删除！", hardWareRespCreateReqDTO.getName()));
                    collectorAlert.setAlarmName(hardWareRespCreateReqDTO.getName());
                    collectorAlert.setTimes(1);
                    collectorAlert.setResourceType(1);
                    collectorAlert.setApp("hardware");
                    collectorAlert.setAlarmId(0L);
                    collectorAlert.setId(alertId + i);
                    toInsert.add(collectorAlert);
                    i++;
                }
                Map<String, List> alertMap = new HashMap<>();
                alertMap.put("insertList", toInsert);
                //创建方法中需要有updateList，防止空指针异常
                alertMap.put("updateList", new ArrayList<>());
                alarmConfigApi.createAlarmToDoris(alertMap);
            }

            hardWareInfoApi.updates(updatedEntries);
            hardWareInfoApi.adds(newEntries);
        }
    }

    private String mapPowerState(String powerstate) {
        switch (powerstate) {
            case "poweredOn":
                return "Enabled";
            case "poweredOff":
                return "Disabled";
            case "stopped":
                return "Suspended";
            default:
                return "Unknown";
        }
    }

    private double calculateCpuUsage(ManagedEntity managedEntity, String name, ServiceInstance serviceInstance, PlatformconfigDTO platformconfigDTO) throws Exception {
        List<PerMonitorDO> doList = RealtimePerfMonitor.getPerEntityMericBasesByname(name, serviceInstance, managedEntity, "cpu.usage.average", platformconfigDTO);
        return doList.isEmpty() ? 0 : NumberUtil.div(doList.stream().mapToLong(PerMonitorDO::getValue).sum() / doList.size(), 100);
    }

    private long calculateNetworkUsage(ManagedEntity managedEntity, String name, ServiceInstance serviceInstance, String metric, PlatformconfigDTO platformconfigDTO) throws Exception {
        List<PerMonitorDO> networkList = RealtimePerfMonitor.getPerEntityMericBasesByname(name, serviceInstance, managedEntity, metric, platformconfigDTO);
        return networkList.stream().mapToLong(PerMonitorDO::getValue).sum();
    }


    private void collectCPUMetrics(ServiceInstance serviceInstance, ManagedEntity managedEntities, HardWareRespCreateReqDTO hardWareRespCreateReqDTO, PlatformconfigDTO platformconfigDTO, String uuid, BatchPoints batchPoints) throws Exception {
        collectMetric("cpu.usage.none", "CPUUsedUtilization", "cpu", serviceInstance, managedEntities, hardWareRespCreateReqDTO, platformconfigDTO, uuid, batchPoints,
                value -> NumberUtil.div(Convert.toBigDecimal(value), 100));
        collectMetric("cpu.usage.average", "CPUAverageUsedUtilization", "cpu", serviceInstance, managedEntities, hardWareRespCreateReqDTO, platformconfigDTO, uuid, batchPoints,
                value -> NumberUtil.div(Convert.toBigDecimal(value), 100, 2));
        collectMetric("cpu.idle.summation", "CPUIdleUtilization", "cpu", serviceInstance, managedEntities, hardWareRespCreateReqDTO, platformconfigDTO, uuid, batchPoints,
                value -> Convert.toBigDecimal(value));
        collectMetric("cpu.usage.none", "CPUAllUsedUtilization", "cpu", serviceInstance, managedEntities, hardWareRespCreateReqDTO, platformconfigDTO, uuid, batchPoints,
                value -> NumberUtil.div(Convert.toBigDecimal(value), 100));
    }

    private void collectMetric(String metricName, String measurementName, String label, ServiceInstance serviceInstance, ManagedEntity managedEntities,
                               HardWareRespCreateReqDTO hardWareRespCreateReqDTO, PlatformconfigDTO platformconfigDTO, String uuid, BatchPoints batchPoints,
                               Function<Long, BigDecimal> valueConverter) throws Exception {
        List<PerMonitorDO> metricList = RealtimePerfMonitor.getPerEntityMericBasesByname(hardWareRespCreateReqDTO.getName(), serviceInstance, managedEntities, metricName, platformconfigDTO);
        metricList.forEach(perMonitorDO -> {
            String instance = perMonitorDO.getInstance().isEmpty() ? "0" : perMonitorDO.getInstance();
            BigDecimal value = valueConverter.apply(perMonitorDO.getValue());
            Point point = createPoint(label, uuid, measurementName, platformconfigDTO, hardWareRespCreateReqDTO, instance, value, perMonitorDO.getDateTime());
            batchPoints.point(point);
        });
    }

    private Point createPoint(String label, String uuid, String metricName, PlatformconfigDTO platformconfigDTO,
                              HardWareRespCreateReqDTO hardWareRespCreateReqDTO, String instance, BigDecimal value, Date timestamp) {
        return Point.measurement("zj_cloud_hardware")
                .tag("label", label)
                .tag("uuid", uuid)
                .tag("metricName", metricName)
                .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                .tag("host_type", instance)
                .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
                .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                .addField("productsName", hardWareRespCreateReqDTO.getName())
                .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                .addField("host_metricName", metricName)
                .addField("type", instance)
                .addField("value", value)
                .time(timestamp.getTime() / 1000, TimeUnit.SECONDS)
                .build();
    }

    private void updateNetWorkData(PlatformconfigDTO p, List<NetWorkL2DTO> netWorkL2DTOS) {
        List<NetWorkL2DTO> netWorks = networkApi.getNetworkL2ByPlatformId(p.getId()).getData();
        if (netWorks.isEmpty()) {
            networkApi.addNetWorkL2(netWorkL2DTOS);
        } else {
            Map<String, NetWorkL2DTO> existingHardwareMap = netWorks.stream()
                    .collect(Collectors.toMap(
                            NetWorkL2DTO::getUuid,
                            netWorkL2DTO -> netWorkL2DTO,
                            (existing, newValue) -> {
                                return newValue;
                            }
                    ));
            List<NetWorkL2DTO> newEntries = new ArrayList<>();
            List<NetWorkL2DTO> updatedEntries = new ArrayList<>();
            List<NetWorkL2DTO> deleteEntries = netWorks.stream()
                    .filter(item -> !netWorkL2DTOS.stream()
                            .map(NetWorkL2DTO::getUuid)
                            .collect(Collectors.toList()).contains(item.getUuid())).collect(Collectors.toList());

            for (NetWorkL2DTO newNet : netWorkL2DTOS) {
                NetWorkL2DTO netWorkL2DTO = existingHardwareMap.get(newNet.getUuid());
                if (netWorkL2DTO == null) {
                    newEntries.add(newNet);
                } else if (!netWorkL2DTO.equals(newNet)) {
                    updatedEntries.add(newNet);
                }
            }
            networkApi.updateNetWorkL2(updatedEntries);
            networkApi.addNetWorkL2(newEntries);
            if (!deleteEntries.isEmpty()) {
                networkApi.deleteNetWorkL2ByNameList(deleteEntries);
            }

        }
    }

}
