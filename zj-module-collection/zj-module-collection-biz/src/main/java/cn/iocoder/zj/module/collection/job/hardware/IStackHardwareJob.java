package cn.iocoder.zj.module.collection.job.hardware;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.zstack.AlarmInfoRedisDAO;
import cn.iocoder.zj.module.collection.framework.influx.config.InfluxDBTemplate;
import cn.iocoder.zj.module.collection.service.cloud.IStackDeviceService;
import cn.iocoder.zj.module.collection.util.SangForPwEncryptor;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import cn.iocoder.zj.module.monitor.api.hardware.HardWareInfoApi;
import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.hardwarenic.HardWareNicApi;
import cn.iocoder.zj.module.monitor.api.hardwarenic.HardWareNicRespDTO;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL2DTO;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL3DTO;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonArray;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class IStackHardwareJob {
    @Resource
    InfluxDBTemplate influxDBTemplate;
    @Resource
    HardWareInfoApi hardWareInfoApi;
    @Resource
    IStackDeviceService iStackDeviceService;
    @Resource
    PlatformRedisDAO platformRedisDAO;
    @Autowired
    private AlarmConfigApi alarmConfigApi;

    @Autowired
    private HardWareNicApi hardWareNicApi;

    public final Map<Long, List<HardWareRespCreateReqDTO>> hardwareData = new ConcurrentHashMap<>();

    public void IStackHardwareJob() {
        ItackHardware();
//        collectToInflux();
    }

    @XxlJob("IStackHardware")
    public void ItackHardware() {
        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                .filter(dto -> "istack".equals(dto.getTypeCode()))
                .collect(Collectors.toList());
        if (filteredList.isEmpty()) return;

        try {
            for (PlatformconfigDTO platformconfigDTO : filteredList) {
                handlePlatformConfig(platformconfigDTO);
            }
        } catch (Exception e) {
            XxlJobHelper.handleFail("sanfor中getSangForHardware异常" + e.getMessage());
        }
    }

    private void handlePlatformConfig(PlatformconfigDTO p) {
        List<HardWareRespCreateReqDTO> hardWareRespCreateReqDTOS = new ArrayList<>();
        if (!"istack".equals(p.getTypeCode())) {
            return;
        }
        List<HardWareNicRespDTO> nicList = new ArrayList<>();
        iStackDeviceService.getHardware(p).stream().map(hardware -> {
            JSONObject jsonObject = (JSONObject) hardware;
            String uuid = jsonObject.getString("uuid");
            HardWareRespCreateReqDTO dto = new HardWareRespCreateReqDTO();
            JSONObject ratio = jsonObject.getJSONObject("ratio");
            dto.setCpuOverPercent(ratio.getBigDecimal("cpu"));
            dto.setMemoryOverPercent(ratio.getBigDecimal("ram"));
//            BigDecimal disk = ratio.getBigDecimal("disk");
            //可用区
            dto.setAvailableManager(jsonObject.getString("dc_uuid"));
            dto.setManager(p.getName());
            // 设置基础信息
            setBasicInfo(dto, jsonObject, p);
            // 设置性能数据
            setPerformanceData(dto, p, uuid, jsonObject);
            // 设置CPU信息
            setCpuInfo(dto, jsonObject);
            // 设置内存信息
            setMemoryInfo(dto, jsonObject);
            // 设置磁盘信息
            setDiskInfo(dto, p, uuid, jsonObject);
            //设置物理机网络信息
            List<HardWareNicRespDTO> hardWareNicRespDTOS = setNicInfo(p, jsonObject);
            if (hardWareNicRespDTOS!=null){
                nicList.addAll(hardWareNicRespDTOS);
            }
            return dto;
        }).forEach(hardWareRespCreateReqDTOS::add);
        if (hardWareRespCreateReqDTOS.size() > 0) {
            List<HardWareRespCreateReqDTO> existingHardwareList = hardWareInfoApi.getHardwareByPlatformId(p.getId()).getCheckedData();

            if (existingHardwareList.isEmpty()) {
                hardWareInfoApi.adds(hardWareRespCreateReqDTOS);
                hardwareData.put(p.getId(), hardWareRespCreateReqDTOS);
            } else {
                Map<String, HardWareRespCreateReqDTO> existingHardwareMap = existingHardwareList.stream()
                        .collect(Collectors.toMap(HardWareRespCreateReqDTO::getUuid, hardware -> hardware));

                List<HardWareRespCreateReqDTO> newEntries = new ArrayList<>();
                List<HardWareRespCreateReqDTO> updatedEntries = new ArrayList<>();
                List<HardWareRespCreateReqDTO> deleteEntries = existingHardwareList.stream()
                        .filter(item -> !hardWareRespCreateReqDTOS.stream()
                                .map(HardWareRespCreateReqDTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid())).collect(Collectors.toList());
                for (HardWareRespCreateReqDTO newHardware : hardWareRespCreateReqDTOS) {
                    HardWareRespCreateReqDTO existingHardware = existingHardwareMap.get(newHardware.getUuid());
                    if (existingHardware == null) {
                        newEntries.add(newHardware);
                    } else if (!existingHardware.equals(newHardware)) {
                        updatedEntries.add(newHardware);
                    }
                }
                if (!deleteEntries.isEmpty()) {
                    deleteEntries.forEach(item -> item.setDeleted(1));
                    hardWareInfoApi.updates(deleteEntries);
                    final Long alertId = alarmConfigApi.getMaxAlertId().getData();
                    // 资源删除触发告警
                    List<AlarmDorisReqDTO> toInsert = new ArrayList<>();
                    int i = 1;
                    for (HardWareRespCreateReqDTO hardWareRespCreateReqDTO : deleteEntries) {
                        AlarmDorisReqDTO collectorAlert = new AlarmDorisReqDTO();
                        collectorAlert.setPriority(0);
                        collectorAlert.setStatus(0);
                        collectorAlert.setIsSolved(0);
                        collectorAlert.setFirstAlarmTime(new Date().getTime());
                        collectorAlert.setGmtCreate(new Date());
                        collectorAlert.setGmtUpdate(new Date());
                        collectorAlert.setLastAlarmTime(new Date().getTime());
                        collectorAlert.setMonitorName(hardWareRespCreateReqDTO.getName());
                        collectorAlert.setMonitorId(Convert.toStr(hardWareRespCreateReqDTO.getUuid()));
                        collectorAlert.setPlatformName(hardWareRespCreateReqDTO.getPlatformName());
                        collectorAlert.setPlatformId(hardWareRespCreateReqDTO.getPlatformId());
                        collectorAlert.setContent(String.format("该宿主机资源(%s)已被删除！", hardWareRespCreateReqDTO.getName()));
                        collectorAlert.setAlarmName(hardWareRespCreateReqDTO.getName());
                        collectorAlert.setTimes(1);
                        collectorAlert.setResourceType(1);
                        collectorAlert.setApp("hardware");
                        collectorAlert.setAlarmId(0L);
                        collectorAlert.setId(alertId + i);
                        toInsert.add(collectorAlert);
                        i++;
                    }
                    Map<String, List> alertMap = new HashMap<>();
                    alertMap.put("insertList", toInsert);
                    //创建方法中需要有updateList，防止空指针异常
                    alertMap.put("updateList", new ArrayList<>());
                    alarmConfigApi.createAlarmToDoris(alertMap);
                }
                hardWareInfoApi.updates(updatedEntries);
                hardWareInfoApi.adds(newEntries);
            }
            hardWareInfoApi.removeDuplicateData();
        }
        if (!nicList.isEmpty()){
            List<HardWareNicRespDTO> oldNicList=hardWareNicApi.getHardwareNicByPlatformId(p.getId()).getCheckedData();

            if (oldNicList.isEmpty()) {
                hardWareNicApi.adds(nicList);
            } else {
                // 修改Map的key为uuid+hardwareUuid的组合
                Map<String, HardWareNicRespDTO> existingHardwareMap = oldNicList.stream()
                        .collect(Collectors.toMap(
                                hardwareNic -> hardwareNic.getUuid() + "_" + hardwareNic.getHardwareUuid(),
                                hardwareNic -> hardwareNic
                        ));

                List<HardWareNicRespDTO> newEntries = new ArrayList<>();
                List<HardWareNicRespDTO> updatedEntries = new ArrayList<>();
                // 修改删除条件，同时比对uuid和hardwareUuid
                List<HardWareNicRespDTO> deleteEntries = oldNicList.stream()
                        .filter(item -> !nicList.stream()
                                .anyMatch(newItem ->
                                        newItem.getUuid().equals(item.getUuid()) &&
                                                newItem.getHardwareUuid().equals(item.getHardwareUuid())
                                ))
                        .collect(Collectors.toList());

                for (HardWareNicRespDTO hardWareNicRespDTO : nicList) {
                    // 使用组合key来查找
                    String compositeKey = hardWareNicRespDTO.getUuid() + "_" + hardWareNicRespDTO.getHardwareUuid();
                    HardWareNicRespDTO nicRespDTO = existingHardwareMap.get(compositeKey);
                    if (nicRespDTO == null) {
                        newEntries.add(hardWareNicRespDTO);
                    } else if (!nicRespDTO.equals(hardWareNicRespDTO)) {
                        updatedEntries.add(hardWareNicRespDTO);
                    }
                }

                hardWareNicApi.updates(updatedEntries);
                hardWareNicApi.adds(newEntries);
                if (!deleteEntries.isEmpty()) {
                    hardWareNicApi.deletes(deleteEntries);
                }
            }
        }
    }

    private String getSamplingValue(JSONArray array) {
        if (array == null || array.isEmpty()) {
            return "0";
        }
        return JSONObject.parseObject(array.get(0).toString()).getString("sampling_value");
    }

    private void setBasicInfo(HardWareRespCreateReqDTO dto, JSONObject jsonObject, PlatformconfigDTO p) {
        dto.setName(jsonObject.getString("name"));
        dto.setUuid(jsonObject.getString("uuid"));
        dto.setIp(jsonObject.getString("host_ip"));
        dto.setStatus("enabled".equals(jsonObject.getString("status")) ? "Connected" : "Disconnected");
        dto.setState("Enabled");
        dto.setClusterName(jsonObject.getString("availability_zone"));
        dto.setClusterUuid("default_cluster");

        // 设置平台信息
        dto.setPlatformId(p.getId());
        dto.setPlatformName(p.getName());
        dto.setTenantId(p.getTenantId());
        dto.setRegionId(p.getRegionId());
        dto.setTypeName(p.getTypeCode());
        dto.setDeleted(0);
    }

    private void setPerformanceData(HardWareRespCreateReqDTO dto, PlatformconfigDTO p, String uuid, JSONObject jsonObject) {
        // 带宽信息 - 输入值为 kb/s，需转换为 b/s
        JSONArray inInfo = iStackDeviceService.getPerformanceData(p, uuid, "net_in_bytes_rate",false);
        JSONArray outInfo = iStackDeviceService.getPerformanceData(p, uuid, "net_out_bytes_rate",false);

        // 将 kb/s 转换为 b/s (乘以 1024)
        dto.setBandwidthDownstream(new BigDecimal(getSamplingValue(outInfo)).multiply(new BigDecimal(1024)));
        dto.setBandwidthUpstream(new BigDecimal(getSamplingValue(inInfo)).multiply(new BigDecimal(1024)));
    }

    private void setCpuInfo(HardWareRespCreateReqDTO dto, JSONObject jsonObject) {
        Long totalCpuCapacity = jsonObject.getLong("cpu_allocate_size");
        dto.setTotalCpuCapacity(totalCpuCapacity);
        //cpu可用量
        Long availableCpuCapacity = totalCpuCapacity - jsonObject.getLong("cpu_allocate_used");
        Integer cpuNum = jsonObject.getInteger("cpu_allocate_used");
        dto.setCpuNum(cpuNum);
        dto.setAvailableCpuCapacity(availableCpuCapacity);

        //当前cpu超分比率
        BigDecimal cpuCommitRate = new BigDecimal(totalCpuCapacity-availableCpuCapacity).divide(new BigDecimal(cpuNum), 3, BigDecimal.ROUND_HALF_UP);
        dto.setCpuCommitRate(cpuCommitRate);

        JSONObject cpuInfo = jsonObject.getJSONObject("cpu_info");
        JSONObject topology = JSONObject.parseObject(cpuInfo.getString("topology"));
        dto.setCpuSockets(topology.getInteger("sockets"));
        dto.setArchitecture(cpuInfo.getString("arch"));
        BigDecimal cpuUsedRate = jsonObject.getBigDecimal("cpu_allocate_used_rate")
                .multiply(new BigDecimal("100"))
                .stripTrailingZeros()
                .setScale(2, BigDecimal.ROUND_HALF_UP);

// 如果使用率大于100，则设置为100
        if (cpuUsedRate.compareTo(new BigDecimal("100")) > 0) {
            cpuUsedRate = new BigDecimal("100").setScale(2, BigDecimal.ROUND_HALF_UP);
        }

// 确保数值在存入前是普通十进制格式
        cpuUsedRate = new BigDecimal(cpuUsedRate.toPlainString()).setScale(2, BigDecimal.ROUND_HALF_UP);

        dto.setCpuUsed(cpuUsedRate);
    }

    private void setMemoryInfo(HardWareRespCreateReqDTO dto, JSONObject jsonObject) {
        Long totalMemoryCapacity = jsonObject.getLong("memory_size") * 1024 * 1024 * 1024;
        dto.setTotalMemoryCapacity(totalMemoryCapacity);
        Long availableMemoryCapacity = jsonObject.getLong("memory_free") * 1024 * 1024 * 1024;
        dto.setAvailableMemoryCapacity(availableMemoryCapacity);
        dto.setMemoryUsed(jsonObject.getBigDecimal("memory_used_rate").multiply(new BigDecimal(100)));
        BigDecimal memCommitRate = new BigDecimal(totalMemoryCapacity-availableMemoryCapacity).divide(new BigDecimal(totalMemoryCapacity), 3, BigDecimal.ROUND_HALF_UP);
        dto.setMemoryCommitRate(memCommitRate);
        Long memoryReserve = jsonObject.getLong("memory_reserve") * 1024 * 1024;
        dto.setReservedMemory(new BigDecimal(memoryReserve));

    }

    private void setDiskInfo(HardWareRespCreateReqDTO dto, PlatformconfigDTO p, String uuid, JSONObject jsonObject) {
        JSONArray useInfo = iStackDeviceService.getPerformanceData(p, uuid, "disk_used",false);
        dto.setDiskUsedBytes(new BigDecimal(getSamplingValue(useInfo)).multiply(new BigDecimal(1024*1024*1024)));

        // 磁盘使用率
        JSONArray diskInfo = iStackDeviceService.getPerformanceData(p, uuid, "disk_util",false);
        dto.setDiskUsed(new BigDecimal(getSamplingValue(diskInfo)));

        // 磁盘空闲空间
        JSONArray freeInfo = iStackDeviceService.getPerformanceData(p, uuid, "disk_free",false);
        dto.setDiskFreeBytes(new BigDecimal(getSamplingValue(freeInfo)).multiply(new BigDecimal(1024*1024*1024)));

        // 磁盘总容量
        JSONArray totalInfo = iStackDeviceService.getPerformanceData(p, uuid, "disk_total",false);
        dto.setTotalDiskCapacity(new BigDecimal(getSamplingValue(totalInfo)).multiply(new BigDecimal(1024*1024*1024)));
    }

    private List<HardWareNicRespDTO> setNicInfo( PlatformconfigDTO p, JSONObject jsonObject) {
        List<HardWareNicRespDTO> list = new ArrayList<>();
        JSONArray result = iStackDeviceService.getNetworks(p);
        List<Map> networks = JSONObject.parseArray(result.toJSONString(), Map.class);
        if (result.size() > 0) {
            for (int j = 0; j < result.size(); j++) {
                JSONObject network = result.getJSONObject(j);
                HardWareNicRespDTO nicDto = new HardWareNicRespDTO();
                nicDto.setL2NetworkUuid(network.getString("id"));
                nicDto.setL2NetworkName(network.getString("name"));
                nicDto.setHardwareUuid(jsonObject.getString("uuid"));
                nicDto.setMac("-");
                nicDto.setNetworkType("业务网");
                nicDto.setIpAddresses(network.getString("cidr"));
                nicDto.setIpSubnet(network.getString("netmask"));
                nicDto.setPlatformId(p.getId());
                nicDto.setPlatformName(p.getName());
                if (network.getString("status").equals("ACTIVE")){
                    nicDto.setState(true);
                }else {
                    nicDto.setState(false);
                }
                JSONArray subnetList = network.getJSONArray("subnet_list");
                if(subnetList != null && subnetList.size()>0){
                    for (Object item : subnetList) {
                        HardWareNicRespDTO hardWareNicRespDTO = BeanUtil.copyProperties(nicDto, HardWareNicRespDTO.class);
                        JSONObject network3 = JSONObject.parseObject(item.toString());
                        hardWareNicRespDTO.setUuid(network3.getString("id"));
                        list.add(hardWareNicRespDTO);
                    }
                }
            }
        }
        return list;
    }

    @XxlJob("IStackWareToInflux")
    public void collectToInflux() {
        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                .filter(dto -> "istack".equals(dto.getTypeCode()))
                .collect(Collectors.toList());
        if (filteredList.isEmpty()) return;
        // 并行处理每个平台
        for (PlatformconfigDTO platform : filteredList) {
            try {
                processPlatform(platform);
            } catch (Exception e) {
                log.error("Error processing platform: " + platform.getName(), e);
            }
        }
    }

    private void processPlatform(PlatformconfigDTO platform) {
        ExecutorService executorService = Executors.newFixedThreadPool(9);
        try {
            List<Future<BatchPoints>> futures = new ArrayList<>();
            // 创建一个线程池，线程数可以根据 CPU 核心数调整。这里使用 10 个并发线程。
            JSONArray hardwares = iStackDeviceService.getHardware(platform);
            // 创建批量写入点的容器, 使用线程安全的集合，因为它会被多线程访问
            List<BatchPoints> batchPointsList = Collections.synchronizedList(new ArrayList<>());

            // 并行处理每个 VM
            for (int i = 0; i < hardwares.size(); i++) {
                JSONObject jsonObject = hardwares.getJSONObject(i);
                // 提交任务到线程池并行执行
                Future<BatchPoints> future = executorService.submit(new Callable<BatchPoints>() {
                    @Override
                    public BatchPoints call() throws Exception {
                        try {
                            // 处理虚拟机并返回结果
                            return processVM(platform, jsonObject);
                        } catch (Exception e) {
                            log.error("Error processing VM: " + jsonObject.getString("name"), e);
                            return null;  // 出现异常时返回 null
                        }
                    }
                });

                // 将 Future 对象添加到列表中
                futures.add(future);
            }
            // 等待所有任务完成，并将结果添加到 batchPointsList 中

            for (Future<BatchPoints> future : futures) {
                try {
                    BatchPoints result = future.get();  // 阻塞等待任务完成
                    if (result != null && !result.getPoints().isEmpty()) {
                        batchPointsList.add(result);
                    }
                } catch (InterruptedException | ExecutionException e) {
                    log.error("Error retrieving result from future", e);
                }
            }

            // 批量写入InfluxDB
            for (BatchPoints batchPoints : batchPointsList) {
                try {
                    influxDBTemplate.writeBatch(batchPoints);
                } catch (Exception e) {
                    log.error("Error writing to InfluxDB", e);
                }
            }
        } finally {
            // 关闭线程池`
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
            }
        }
    }

    private BigDecimal formatSamplingValue(Object samplingValue) {
        if (samplingValue == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal value = Convert.toBigDecimal(samplingValue);
        if (value.scale() <= 0) {
            return new BigDecimal(value.toPlainString()).setScale(0, RoundingMode.UNNECESSARY);
        }
        return new BigDecimal(value.setScale(2, RoundingMode.HALF_UP).toPlainString());
    }

    private BigDecimal formatScienceValue(Object samplingValue) {
        if (samplingValue == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal value = Convert.toBigDecimal(samplingValue);
        // 如果值大于100，强制设为100
        if (value.compareTo(new BigDecimal("100")) > 0) {
            return new BigDecimal("100");
        }
        if (value.scale() <= 0) {
            return new BigDecimal(value.toPlainString()).setScale(0, RoundingMode.UNNECESSARY);
        }
        return new BigDecimal(value.setScale(2, RoundingMode.HALF_UP).toPlainString());
    }

    private BatchPoints processVM(PlatformconfigDTO p, JSONObject jsonObject) {
        BatchPoints.Builder batchPoints = BatchPoints.builder();
        String uuid = jsonObject.getString("uuid");
        String name = jsonObject.getString("name");
        
        //cpu使用率
        JSONArray cpuInfo = iStackDeviceService.getPerformanceData(p, uuid, "cpu_util",true);
        for (Object item : cpuInfo) {
            JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
            BigDecimal value = formatScienceValue(obj.get("sampling_value"));
            Point point = Point.measurement("zj_cloud_hardware")
                    .tag("uuid", uuid)
                    .tag("metricName", "CPUAverageUsedUtilization")
                    .addField("productsName", name)
                    .tag("label", "cpu")
                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                    .tag("host_type", "CPUAverageUsedUtilization")
                    .tag("platformId", StringUtil.toString(p.getId()))
                    .addField("platformName", StringUtil.toString(p.getName()))
                    .addField("host_metricName", "CPUAverageUsedUtilization")
                    .addField("type", "CPUAverageUsedUtilization")
                    .addField("value", value)
                    .time(convertTimestamp(StringUtil.toLong(obj.get("sampling_time"))), TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }

        //内存使用情况
        JSONArray memInfo = iStackDeviceService.getPerformanceData(p, uuid, "mem_util",true);
        long memorySize = jsonObject.getLong("memory_size") * 1024;
        for (Object item : memInfo) {
            JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
            BigDecimal samplingRate = formatScienceValue(obj.get("sampling_value"));
            BigDecimal samplingValue = formatSamplingValue(obj.get("sampling_value"));

            // 内存使用字节数
            String memValue = samplingValue.multiply(new BigDecimal(memorySize))
                    .toPlainString();
            Point point = Point.measurement("zj_cloud_hardware")
                    .tag("uuid", uuid)
                    .tag("metricName", "MemoryUsedBytes")
                    .addField("productsName", name)
                    .tag("label", "mem")
                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                    .tag("host_type", "all")
                    .tag("platformId", StringUtil.toString(p.getId()))
                    .addField("platformName", StringUtil.toString(p.getName()))
                    .addField("host_metricName", "MemoryUsedBytes")
                    .addField("type", "all")
                    .addField("value", formatSamplingValue(memValue))
                    .time(convertTimestamp(StringUtil.toLong(obj.get("sampling_time"))), TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);

            //内存空闲情况
            BigDecimal freePercentage = new BigDecimal(100).subtract(samplingValue);
            String freeValue = freePercentage.multiply(new BigDecimal(memorySize))
                    .toPlainString();
            Point freePoint = Point.measurement("zj_cloud_hardware")
                    .tag("uuid", uuid)
                    .tag("metricName", "MemoryFreeBytes")
                    .addField("productsName", name)
                    .tag("label", "mem")
                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                    .tag("host_type", "all")
                    .tag("platformId", StringUtil.toString(p.getId()))
                    .addField("platformName", StringUtil.toString(p.getName()))
                    .addField("host_metricName", "MemoryFreeBytes")
                    .addField("type", "all")
                    .addField("value", formatSamplingValue(freeValue))
                    .time(convertTimestamp(StringUtil.toLong(obj.get("sampling_time"))), TimeUnit.SECONDS)
                    .build();
            batchPoints.point(freePoint);

            //内存使用率
            Point usePoint = Point.measurement("zj_cloud_hardware")
                    .tag("uuid", uuid)
                    .tag("label", "mem")
                    .tag("metricName", "MemoryUsedInPercent")
                    .addField("productsName", name)
                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                    .tag("vm_type", "all")
                    .tag("platformId", StringUtil.toString(p.getId()))
                    .addField("platformName", StringUtil.toString(p.getName()))
                    .addField("vm_metricName", "MemoryUsedInPercent")
                    .addField("type", "all")
                    .addField("value", samplingRate)
                    .time(convertTimestamp(StringUtil.toLong(obj.get("sampling_time"))), TimeUnit.SECONDS)
                    .build();
            batchPoints.point(usePoint);
        }

        //磁盘使用
        JSONArray diskInfo = iStackDeviceService.getPerformanceData(p, uuid, "disk_util",true);
        JSONArray totalInfo = iStackDeviceService.getPerformanceData(p, uuid, "disk_total",false);
        BigDecimal multiply = formatSamplingValue(getSamplingValue(totalInfo))
                .multiply(new BigDecimal(1024 * 1024 * 1024));
        
        for (Object metricValue : diskInfo) {
            JSONObject metric = JSONObject.parseObject(metricValue.toString());
            BigDecimal samplingValue = formatSamplingValue(metric.get("sampling_value"));
            
            Point usedPoint = Point.measurement("zj_cloud_hardware")
                    .tag("uuid", uuid)
                    .tag("metricName", "DiskAllUsedCapacityInPercent")
                    .tag("label", "disk")
                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                    .tag("host_type", "all")
                    .tag("platformId", StringUtil.toString(p.getId()))
                    .addField("productsName", name)
                    .addField("platformName", StringUtil.toString(p.getName()))
                    .addField("host_metricName", "DiskAllUsedCapacityInPercent")
                    .addField("type", "all")
                    .addField("value", formatSamplingValue(samplingValue.toPlainString()))
                    .time(convertTimestamp(StringUtil.toLong(metric.get("sampling_time"))), TimeUnit.SECONDS)
                    .build();
            batchPoints.point(usedPoint);

            String diskUsedBytes = multiply.multiply(samplingValue.divide(new BigDecimal(100)))
                    .toPlainString();
            Point pointFree = Point.measurement("zj_cloud_hardware")
                    .tag("uuid", uuid)
                    .tag("label","disk")
                    .tag("metricName", "DiskAllUsedCapacityInBytes")
                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                    .tag("host_type", "all")
                    .tag("platformId", StringUtil.toString(p.getId()))
                    .addField("productsName", name)
                    .addField("platformName", StringUtil.toString(p.getName()))
                    .addField("host_metricName", "DiskAllUsedCapacityInBytes")
                    .addField("type", "all")
                    .addField("value", formatSamplingValue(diskUsedBytes))
                    .time(convertTimestamp(StringUtil.toLong(metric.get("sampling_time"))), TimeUnit.SECONDS)
                    .build();
            batchPoints.point(pointFree);
        }

        //IO读速率
        JSONArray readInfo = iStackDeviceService.getPerformanceData(p, uuid, "disk_read_bytes_rate",true);
        for (Object item : readInfo) {
            JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
            String value = formatSamplingValue(obj.get("sampling_value"))
                    .multiply(new BigDecimal(1024))
                    .toPlainString();
            Point point = Point.measurement("zj_cloud_hardware")
                    .tag("uuid", uuid)
                    .tag("metricName", "DiskReadBytes")
                    .addField("productsName", name)
                    .tag("label", "disk")
                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                    .tag("host_type", "all")
                    .tag("platformId", StringUtil.toString(p.getId()))
                    .addField("platformName", StringUtil.toString(p.getName()))
                    .addField("host_metricName", "DiskReadBytes")
                    .addField("type", "all")
                    .addField("value", formatSamplingValue(value))
                    .time(convertTimestamp(StringUtil.toLong(obj.get("sampling_time"))), TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }

        //IO写速率
        JSONArray writeInfo = iStackDeviceService.getPerformanceData(p, uuid, "disk_write_bytes_rate",true);
        for (Object item : writeInfo) {
            JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
            String value = formatSamplingValue(obj.get("sampling_value"))
                    .multiply(new BigDecimal(1024))
                    .toPlainString();
            Point point = Point.measurement("zj_cloud_hardware")
                    .tag("uuid", uuid)
                    .tag("metricName", "DiskWriteBytes")
                    .addField("productsName", name)
                    .tag("label", "disk")
                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                    .tag("host_type", "all")
                    .tag("platformId", StringUtil.toString(p.getId()))
                    .addField("platformName", StringUtil.toString(p.getName()))
                    .addField("host_metricName", "DiskWriteBytes")
                    .addField("type", "all")
                    .addField("value", formatSamplingValue(value))
                    .time(convertTimestamp(StringUtil.toLong(obj.get("sampling_time"))), TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }

        //IO读IOPS
        JSONArray readRateInfo = iStackDeviceService.getPerformanceData(p, uuid, "disk_read_requests_rate",true);
        for (Object item : readRateInfo) {
            JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
            BigDecimal value = formatSamplingValue(obj.get("sampling_value"));
            Point point = Point.measurement("zj_cloud_hardware")
                    .tag("uuid", uuid)
                    .tag("metricName", "DiskReadOps")
                    .addField("productsName", name)
                    .tag("label", "disk")
                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                    .tag("host_type", "all")
                    .tag("platformId", StringUtil.toString(p.getId()))
                    .addField("platformName", StringUtil.toString(p.getName()))
                    .addField("host_metricName", "DiskReadOps")
                    .addField("type", "all")
                    .addField("value", value)
                    .time(convertTimestamp(StringUtil.toLong(obj.get("sampling_time"))), TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }

        //IO写IOPS
        JSONArray writeRateInfo = iStackDeviceService.getPerformanceData(p, uuid, "disk_write_requests_rate",true);
        for (Object item : writeRateInfo) {
            JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
            BigDecimal value = formatSamplingValue(obj.get("sampling_value"));
            Point point = Point.measurement("zj_cloud_hardware")
                    .tag("uuid", uuid)
                    .tag("metricName", "DiskWriteOps")
                    .addField("productsName", name)
                    .tag("label", "disk")
                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                    .tag("host_type", "all")
                    .tag("platformId", StringUtil.toString(p.getId()))
                    .addField("platformName", StringUtil.toString(p.getName()))
                    .addField("host_metricName", "DiskWriteOps")
                    .addField("type", "all")
                    .addField("value", value)
                    .time(convertTimestamp(StringUtil.toLong(obj.get("sampling_time"))), TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }

        //网络接收
        JSONArray inInfo = iStackDeviceService.getPerformanceData(p, uuid, "net_in_bytes_rate",true);
        for (Object item : inInfo) {
            JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
            String value = formatSamplingValue(obj.get("sampling_value"))
                    .multiply(new BigDecimal(1024))
                    .toPlainString();
            Point point = Point.measurement("zj_cloud_hardware")
                    .tag("uuid", uuid)
                    .tag("metricName", "NetworkInBytes")
                    .addField("productsName", name)
                    .tag("label", "net")
                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                    .tag("host_type", "all")
                    .tag("platformId", StringUtil.toString(p.getId()))
                    .addField("platformName", StringUtil.toString(p.getName()))
                    .addField("host_metricName", "NetworkInBytes")
                    .addField("type", "all")
                    .addField("value", formatSamplingValue(value))
                    .time(convertTimestamp(StringUtil.toLong(obj.get("sampling_time"))), TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }

        //网络发送
        JSONArray outInfo = iStackDeviceService.getPerformanceData(p, uuid, "net_out_bytes_rate",true);
        for (Object item : outInfo) {
            JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
            String value = formatSamplingValue(obj.get("sampling_value"))
                    .multiply(new BigDecimal(1024))
                    .toPlainString();
            Point point = Point.measurement("zj_cloud_hardware")
                    .tag("uuid", uuid)
                    .tag("metricName", "NetworkOutBytes")
                    .addField("productsName", name)
                    .tag("label", "net")
                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                    .tag("host_type", "all")
                    .tag("platformId", StringUtil.toString(p.getId()))
                    .addField("platformName", StringUtil.toString(p.getName()))
                    .addField("host_metricName", "NetworkOutBytes")
                    .addField("type", "all")
                    .addField("value", formatSamplingValue(value))
                    .time(convertTimestamp(StringUtil.toLong(obj.get("sampling_time"))), TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }
        
        return batchPoints.build();
    }

    public static Long convertTimestamp(long timestamp) {
        Date date = new Date(timestamp * 1000);
        long time = date.getTime() / 1000;
        return time;
    }
}
