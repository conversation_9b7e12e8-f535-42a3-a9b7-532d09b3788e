package cn.iocoder.zj.module.collection.job.cloud;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.zj.framework.common.util.collection.CollectionUtils;
import cn.iocoder.zj.module.collection.dal.dataobject.zstack.AlarmConfigInfo;
import cn.iocoder.zj.module.collection.dal.dataobject.zstack.AlarmHostRelationInfo;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.zstack.AlarmInfoRedisDAO;
import cn.iocoder.zj.module.collection.framework.influx.config.InfluxDBTemplate;
import cn.iocoder.zj.module.collection.job.volume.MediaType;
import cn.iocoder.zj.module.collection.service.cloud.ISangForDeviceService;
import cn.iocoder.zj.module.collection.util.PowerStateUtil;
import cn.iocoder.zj.module.collection.util.SangForPwEncryptor;
import cn.iocoder.zj.module.collection.util.StateConverter;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import cn.iocoder.zj.module.monitor.api.hardware.HardWareInfoApi;
import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.hostinfo.HostInfoApi;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.hostnic.HostNicApi;
import cn.iocoder.zj.module.monitor.api.hostnic.dto.HostNicCreateReqDto;
import cn.iocoder.zj.module.monitor.api.imageinfo.ImageInfoApi;
import cn.iocoder.zj.module.monitor.api.imageinfo.dto.ImageInfoCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.valume.VolumeApi;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeDTO;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeSnapshotDTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SangForCloudJob {
    @Resource
    InfluxDBTemplate influxDBTemplate;
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    PlatformRedisDAO platformRedisDAO;
    @Resource
    ISangForDeviceService iSangForDeviceService;
    @Resource
    HostInfoApi hostInfoApi;
    @Autowired
    private AlarmConfigApi alarmConfigApi;

    @Resource
    VolumeApi volumeApi;
    @Resource
    HostNicApi hostNicApi;

    @Resource
    AlarmInfoRedisDAO alarmInfoRedisDAO;

    @Resource
    ImageInfoApi imageInfoApi;

    @Resource
    HardWareInfoApi hardWareInfoApi;

    private final ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());

    public void sangForCloudJobList() {
        collectSangForCloud();
        collectToInflux();
    }

    @XxlJob("sangForCloud")
    public void collectSangForCloud() {

        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                .filter(dto -> "sangFor".equals(dto.getTypeCode()))
                .collect(Collectors.toList());
        if (filteredList.isEmpty()) return;

        List<AlarmHostRelationInfo> alarmHostRealtionList = JSON.parseArray(alarmInfoRedisDAO.getRelation("alarm_host_relation"), AlarmHostRelationInfo.class);
        List<AlarmConfigInfo> alarmConfigInfoList = JSON.parseArray(alarmInfoRedisDAO.getConfig("alarm_config"), AlarmConfigInfo.class);
        Map<Long, AlarmConfigInfo> alarmConfigMap = new HashMap<>();
        Map<String, AlarmHostRelationInfo> alarmConfigRelationMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(alarmConfigInfoList)) {
            alarmConfigInfoList.removeIf(item -> item.getDeleted() == 1);
            alarmConfigMap = CollectionUtils.convertMap(alarmConfigInfoList, AlarmConfigInfo::getId);
        }
        if (ObjectUtil.isNotEmpty(alarmHostRealtionList)) {
            alarmHostRealtionList.removeIf(item -> item.getStatus() == 1);
            alarmConfigRelationMap = CollectionUtils.convertMap(alarmHostRealtionList, AlarmHostRelationInfo::getHostUuid);
        }

        try {
            for (PlatformconfigDTO platformconfigDTO : filteredList) {
                handlePlatformConfigExecutor(platformconfigDTO, alarmConfigRelationMap, alarmConfigMap);
            }
        } catch (Exception e) {
            StackTraceElement stackTraceElement = e.getStackTrace()[0];
            XxlJobHelper.handleFail("sanfor中sangForCloud异常" + stackTraceElement);
        }
    }

    private void handlePlatformConfigExecutor(PlatformconfigDTO p, Map<String, AlarmHostRelationInfo> alarmConfigRelationMap, Map<Long, AlarmConfigInfo> alarmConfigMap) {
        JSONObject tokenInfo = SangForPwEncryptor.sangForlogin(p);
        JSONArray clouds = iSangForDeviceService.getClouds(p, tokenInfo);
        CompletableFuture<List<HostInfoRespCreateReqDTO>> hostInfoFuture = processHostInfo(clouds, p, tokenInfo, alarmConfigRelationMap);
        CompletableFuture<List<ImageInfoCreateReqDTO>> imageInfoFuture = processImageInfo(clouds, p, tokenInfo);
        CompletableFuture<List<HostNicCreateReqDto>> hostNicFuture = processHostNic(clouds, p,tokenInfo);
        List<HostInfoRespCreateReqDTO> hostInfoRespCreateReqDTOList = hostInfoFuture.join();
        updateHostInfo(hostInfoRespCreateReqDTOList, p.getId());
        updateImageInfo(imageInfoFuture.join(), p.getId());
        List<VolumeDTO> volumeFuture = volumeInfo(clouds, p);
        updateVolumeInfo(volumeFuture, p.getId());
        List<HostNicCreateReqDto> hostNicCreateReqDto = hostNicFuture.join();
        updateNicInfo(hostNicCreateReqDto, p.getId());
        updateVolumeSnapshotInfo(clouds, p);
    }

    private void updateVolumeInfo(List<VolumeDTO> volumeDTOList, Long id) {
        if (!volumeDTOList.isEmpty()) {
            List<VolumeDTO> existingVolume = volumeApi.getVolumeByPlatformId(id);
            if (existingVolume.isEmpty()) {
                volumeApi.addVolumes(volumeDTOList);
            } else {
                Map<String, VolumeDTO> existingVolumeMap = existingVolume.stream()
                        .collect(Collectors.toMap(VolumeDTO::getUuid, volumeInfo -> volumeInfo));

                List<VolumeDTO> newVolume = new ArrayList<>();
                List<VolumeDTO> updateVolume = new ArrayList<>();
                List<VolumeDTO> deleteEntries = existingVolume.stream()
                        .filter(item -> !volumeDTOList.stream()
                                .map(VolumeDTO::getUuid).collect(Collectors.toList())
                                .contains(item.getUuid())).collect(Collectors.toList());

                for (VolumeDTO volumeDTO : volumeDTOList) {
                    VolumeDTO existingvolume = existingVolumeMap.get(volumeDTO.getUuid());
                    if (existingvolume == null) {
                        newVolume.add(volumeDTO);
                    } else if (!existingvolume.equals(volumeDTO)) {
                        updateVolume.add(volumeDTO);
                    }
                }
                if (!deleteEntries.isEmpty()) {
                    deleteEntries.forEach(item -> item.setDeleted(1L));
                    volumeApi.delVolumes(deleteEntries);
                }
                if (updateVolume.size()>0){
                    volumeApi.updateVolumes(updateVolume);
                }
                if (newVolume.size()>0) {
                    volumeApi.addVolumes(newVolume);
                }
            }
        }
    }

    private void updateHostInfo(List<HostInfoRespCreateReqDTO> hostInfoRespCreateReqDTOList, Long id) {
        if (!hostInfoRespCreateReqDTOList.isEmpty()) {
            List<HostInfoRespCreateReqDTO> existingHosts = hostInfoApi.getVmByPlatformId(id);
            if (existingHosts.isEmpty()) {
                hostInfoApi.adds(hostInfoRespCreateReqDTOList);
            } else {
                Map<String, HostInfoRespCreateReqDTO> existingHostMap = existingHosts.stream()
                        .collect(Collectors.toMap(HostInfoRespCreateReqDTO::getUuid, hostInfo -> hostInfo));

                List<HostInfoRespCreateReqDTO> newEntries = new ArrayList<>();
                List<HostInfoRespCreateReqDTO> updateEntries = new ArrayList<>();
                List<HostInfoRespCreateReqDTO> deleteEntries = existingHosts.stream()
                        .filter(item -> !hostInfoRespCreateReqDTOList.stream()
                                .map(HostInfoRespCreateReqDTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid()))
                        .collect(Collectors.toList());

                for (HostInfoRespCreateReqDTO newHostInfo : hostInfoRespCreateReqDTOList) {
                    HostInfoRespCreateReqDTO existingHost = existingHostMap.get(newHostInfo.getUuid());
                    if (existingHost == null) {
                        newEntries.add(newHostInfo);
                    } else if (!existingHost.equals(newHostInfo)) {
                        updateEntries.add(newHostInfo);
                    }
                }
                if (!deleteEntries.isEmpty()) {
                    deleteEntries.forEach(item -> item.setDeleted(1).setState("Destroyed"));
                    hostInfoApi.updates(deleteEntries);
                }

                hostInfoApi.updates(updateEntries);
                hostInfoApi.adds(newEntries);
            }
        }
    }

    private void updateImageInfo(List<ImageInfoCreateReqDTO> imageInfoDTOs, Long id) {
        if (!imageInfoDTOs.isEmpty()){
            List<ImageInfoCreateReqDTO> imageInfoDTOList = imageInfoApi.getImageInfoByPlatformId(id).getData();
            if (imageInfoDTOList == null || imageInfoDTOList.isEmpty()) {
                imageInfoApi.batchCreateImageInfo(imageInfoDTOs);
            }else {
                //比较uuid不存在删除
                List<ImageInfoCreateReqDTO> deleteTarget = imageInfoDTOList.stream()
                        .filter(imageInfoDTO -> imageInfoDTOs.stream()
                                .noneMatch(item -> item.getUuid().equals(imageInfoDTO.getUuid())))
                        .collect(Collectors.toList());
                //新增镜像信息
                List<ImageInfoCreateReqDTO> collect = imageInfoDTOs.stream()
                        .filter(imageInfoDTO -> imageInfoDTOList.stream()
                                .noneMatch(item -> item.getUuid().equals(imageInfoDTO.getUuid())))
                        .collect(Collectors.toList());
                //更新镜像信息
                List<ImageInfoCreateReqDTO> updateDTOs = imageInfoDTOs.stream()
                        .filter(imageInfoDTO -> imageInfoDTOList.stream()
                                .anyMatch(item -> item.getUuid().equals(imageInfoDTO.getUuid())))
                        .toList();
                //去掉uuid不存在的collect
                if (!collect.isEmpty()){
                    imageInfoApi.batchCreateImageInfo(collect);
                }
                if (!updateDTOs.isEmpty()){
                    imageInfoApi.batchUpdateImageInfo(updateDTOs);
                }
                if (!deleteTarget.isEmpty()) {
                    imageInfoApi.batchDeleteImageInfo(deleteTarget);
                }
            }
        }
    }

    private void updateNicInfo(List<HostNicCreateReqDto> hostNicCreateReqDtoList, Long id) {
        if (!hostNicCreateReqDtoList.isEmpty()) {
            List<HostNicCreateReqDto> nowNic = hostNicApi.getHostNicsByPlatformId(id).getData();
            //根据uuid筛选出hostNicCreateReqDtoList里需要新增的数据
            Set<String> nowNicUuids = nowNic.stream()
                    .map(HostNicCreateReqDto::getUuid)
                    .collect(Collectors.toSet());

            List<HostNicCreateReqDto> addHostNicList = hostNicCreateReqDtoList.stream()
                    .filter(hostNicCreateReqDto -> !nowNicUuids.contains(hostNicCreateReqDto.getUuid()))
                    .toList();
            if (!addHostNicList.isEmpty()) {
                hostNicApi.addHostNics(addHostNicList);
            }
            //查询updateList 并将nowNicUuids 的id 赋值给updateHostNicList
            List<HostNicCreateReqDto> updateHostNicList = hostNicCreateReqDtoList.stream()
                    .filter(hostNicCreateReqDto -> nowNicUuids.contains(hostNicCreateReqDto.getUuid()))
                    .toList().stream().map(hostNicCreateReqDto -> {
                        return hostNicCreateReqDto.setId(nowNic.stream()
                                .filter(hostNicRespDTO -> hostNicRespDTO.getUuid().equals(hostNicCreateReqDto.getUuid()))
                                .findFirst()
                                .map(HostNicCreateReqDto::getId)
                                .orElse(null));
                    }).toList();
            if (!updateHostNicList.isEmpty()) {
                hostNicApi.updateHostNics(updateHostNicList);
            }
            //根据uuid 判断在nowNic不在hostNicCreateReqDtoList里的数据
            Set<String> hostNicUuids = hostNicCreateReqDtoList.stream()
                    .map(HostNicCreateReqDto::getUuid)
                    .collect(Collectors.toSet());
            List<HostNicCreateReqDto> delHostNicList = nowNic.stream()
                    .filter(hostNicRespDTO -> !hostNicUuids.contains(hostNicRespDTO.getUuid()))
                    .toList();
            if (!delHostNicList.isEmpty()) {
                hostNicApi.delHostNics(delHostNicList);
            }
        }
    }


    private List<VolumeDTO> volumeInfo(JSONArray clouds, PlatformconfigDTO p) {
        List<VolumeDTO> volumeDTOList = new ArrayList<>();
        for (int l = 0; l < clouds.size(); l++) {
            JSONObject jsonObject = clouds.getJSONObject(l);
            String vmid = jsonObject.getString("vmid");
            JSONObject tokenInfos = SangForPwEncryptor.sangForlogin(p);
            JSONObject usageInfo = iSangForDeviceService.getVmDetail(p, tokenInfos, jsonObject.getString("vmid"));
            JSONArray diskList = usageInfo.getJSONArray("diskStatus");
            JSONObject hardwareStatus = usageInfo.getJSONObject("hardwareStatus");
            if (!diskList.isEmpty()) {
                for (int i = 0; i < diskList.size(); i++) {
                    String json = "";
                    if (hardwareStatus.get("ide" + i) != null) {
                        json = hardwareStatus.get("ide" + i).toString();
                    }

                    HashMap<String, String> map = new HashMap<>();
                    if (!Objects.equals(json, "")) {
                        // 将字符串分割成键值对
                        String[] pairs = json.split(",");
                        for (String pair : pairs) {
                            // 处理键值对
                            String[] keyValue;
                            if (pair.contains(":")) {
                                keyValue = pair.split(":", 2);
                            } else {
                                keyValue = pair.split("=", 2);
                            }
                            // 将键值对放入 HashMap
                            map.put(keyValue[0].trim(), keyValue[1].trim());
                        }
                    }

                    VolumeDTO volumeDTO = new VolumeDTO();
                    JSONObject diskStatus = usageInfo.getJSONArray("diskStatus").getJSONObject(i);
                    volumeDTO.setDescription("深信服云盘描述");    //描述
                    volumeDTO.setName("vm-disk-" + i);
                    volumeDTO.setFormat("vmtx");  //云盘格式
                    Long total = Long.valueOf(diskStatus.get("total").toString());
                    Long free = Convert.toLong(diskStatus.get("free"));
                    volumeDTO.setSize(total);
                    volumeDTO.setActualFree(free);
                    if (free == null) {
                        volumeDTO.setActualUse(0L);
                    } else {
                        volumeDTO.setActualUse(Convert.toLong(NumberUtil.sub(total, free)));
                    }
                    volumeDTO.setActualRatio(diskStatus.get("ratio").toString());
                    volumeDTO.setType("Data");
                    volumeDTO.setActualSize(0L);
                    volumeDTO.setState(map.get("forecast"));
                    volumeDTO.setUuid(UuidUtils.generateUuid());
                    volumeDTO.setStatus("Ready");
                    volumeDTO.setState("Enabled");
                    volumeDTO.setPlatformId(p.getId());
                    volumeDTO.setPlatformName(p.getName());
                    String uuid = jsonObject.getString("uuid") != null ? jsonObject.getString("uuid")
                            : jsonObject.getString("vmid");
                    volumeDTO.setVmInstanceUuid(uuid);
                    volumeDTO.setVmInstanceName(jsonObject.getString("name"));
                    String volumeId = jsonObject.getString("volume_id");
                    if (volumeId != null){
                        if (volumeId.equals("local")){
                            String host = jsonObject.getString("host");
                            if (host != null){
                                volumeDTO.setPrimaryStorageUuid(host + "_local");
                            }
                            volumeDTO.setPrimaryStorageType("local");
                        }else {
                            volumeDTO.setPrimaryStorageUuid(volumeId);
                            JSONObject storageInfo = iSangForDeviceService.getStorageDetail(p, tokenInfos, volumeId);
                            volumeDTO.setPrimaryStorageType(storageInfo.getString("lvtype"));
                        }
                    }
                    String primaryStorageName = jsonObject.getString("storagename");
                    if (jsonObject.getLong("create_time") != null) {
                        long timestamp = jsonObject.getLong("create_time") * 1000;
                        volumeDTO.setVCreateDate(new Date(timestamp));
                        volumeDTO.setVUpdateDate(new Date(timestamp));
                    }
                    volumeDTO.setPrimaryStorageName(primaryStorageName);
                    volumeDTO.setMediaType(MediaType.ROTATE.getEnName());
                    volumeDTO.setIsMount(true);
                    volumeDTOList.add(volumeDTO);
                }
            }

        }
        return volumeDTOList;
    }

    public void updateVolumeSnapshotInfo(JSONArray clouds, PlatformconfigDTO p) {
        List<VolumeSnapshotDTO> volumeSnapshotDTOList = new ArrayList<>();
        for (int l = 0; l < clouds.size(); l++) {
            JSONObject jsonObject = clouds.getJSONObject(l);
            String vmid = jsonObject.getString("vmid");
            JSONObject tokenInfos = SangForPwEncryptor.sangForlogin(p);
            JSONObject snapshotList = iSangForDeviceService.getVmSnapshots(p, tokenInfos, vmid);
            if (snapshotList.isEmpty()) {
                continue;
            }
            Long latest_snap_time = snapshotList.getLong("latest_snap_time");
            Date updateTime = null;
            if (latest_snap_time != null && latest_snap_time != 0) {
                updateTime = new Date(latest_snap_time);
            }
            JSONArray snapshots = snapshotList.getJSONArray("snaps");
            for (int i = 0; i < snapshots.size(); i++) {
                JSONObject snapshot = snapshots.getJSONObject(i);
                VolumeSnapshotDTO volumeSnapshotDTO = new VolumeSnapshotDTO();
                volumeSnapshotDTO.setUuid(snapshot.getString("snap_id"));
                volumeSnapshotDTO.setName(snapshot.getString("name"));
                volumeSnapshotDTO.setPlatformId(p.getId());
                volumeSnapshotDTO.setPlatformName(p.getName());
                String uuid = jsonObject.getString("uuid") != null ? jsonObject.getString("uuid")
                        : jsonObject.getString("vmid");
                volumeSnapshotDTO.setHostUuid(uuid);
                volumeSnapshotDTO.setHostName(jsonObject.getString("name"));
                String volumeId = jsonObject.getString("volume_id");
                if (volumeId != null){
                    if (volumeId.equals("local")){
                        String host = jsonObject.getString("host");
                        if (host != null){
                            volumeSnapshotDTO.setPrimaryStorageUuid(host + "_local");
                        }
                    }else {
                        volumeSnapshotDTO.setPrimaryStorageUuid(volumeId);
                    }
                }
                volumeSnapshotDTO.setPrimaryStorageName(jsonObject.getString("storagename"));
                String status = snapshot.getString("status");
                if (status != null){
                    if (status.equals("normal")){
                        status = "Enabled";
                    }else {
                        status = "Disabled";
                    }
                }
                volumeSnapshotDTO.setStatus(status);
                Long createTime = jsonObject.getLong("create_time");
                if (createTime != null){
                    volumeSnapshotDTO.setVCreateDate(new Date(createTime));
                }
                volumeSnapshotDTO.setType("主机快照");
                volumeSnapshotDTO.setTypeName("sangFor");
                volumeSnapshotDTO.setStatus(status);
                volumeSnapshotDTO.setVUpdateDate(updateTime);
                volumeSnapshotDTO.setCreateTime(LocalDateTime.now());
                volumeSnapshotDTO.setFormat("qcow2");
                volumeSnapshotDTO.setLatest("true");
                volumeSnapshotDTO.setIsMemory(false);
                volumeSnapshotDTOList.add(volumeSnapshotDTO);
            }
        }
        if (!volumeSnapshotDTOList.isEmpty()) {
            List<VolumeSnapshotDTO> dtos = volumeApi.getVolumeSnapshotByPlatformId(p.getId());
            if (dtos.isEmpty()) {
                volumeApi.addVolumeSnapshots(volumeSnapshotDTOList);
            } else {
                List<VolumeSnapshotDTO> collect = volumeSnapshotDTOList.stream()
                        .filter(item -> !dtos.stream().map(VolumeSnapshotDTO::getUuid)
                                .toList().contains(item.getUuid()))
                        .collect(Collectors.toList());
                List<VolumeSnapshotDTO> deleteTarget = dtos.stream().filter(item ->!volumeSnapshotDTOList.stream()
                               .map(VolumeSnapshotDTO::getUuid).toList()
                               .contains(item.getUuid())).collect(Collectors.toList());
                List<VolumeSnapshotDTO> updateDTOs = volumeSnapshotDTOList.stream()
                        .filter(item -> dtos.stream().map(VolumeSnapshotDTO::getUuid)
                                .toList().contains(item.getUuid()))
                        .toList();
                if (!updateDTOs.isEmpty()) {
                    volumeApi.updateVolumeSnapshots(updateDTOs);
                }
                if (!deleteTarget.isEmpty()) {
                    volumeApi.delVolumeSnapshots(deleteTarget);
                }
                if (!collect.isEmpty()) {
                    volumeApi.addVolumeSnapshots(collect);
                }
            }
        }
    }


    private CompletableFuture<List<HostInfoRespCreateReqDTO>> processHostInfo(JSONArray clouds, PlatformconfigDTO p, JSONObject tokenInfo, Map<String, AlarmHostRelationInfo> alarmConfigRelationMap) {
        List<CompletableFuture<HostInfoRespCreateReqDTO>> futures = clouds.stream()
                .map(vm -> CompletableFuture.supplyAsync(() -> {
                    try {
                        JSONObject jsonObject = (JSONObject) vm;
                        String vmid = jsonObject.getString("vmid");

                        // 如果 vmid 为空，则获取 netInfo
                        JSONObject netInfo = null;
                        JSONObject netCloudInfo = null;
                        JSONObject usageInfo = null;
                        JSONObject tokenInfos = SangForPwEncryptor.sangForlogin(p);
                        if (StrUtil.isEmpty(vmid)) {
                            netInfo = iSangForDeviceService.getNetInfos(p, tokenInfos, vmid);
                        } else {
                            netCloudInfo = iSangForDeviceService.getNetCloudInfos(p, tokenInfos, vmid);
                            usageInfo = iSangForDeviceService.getVmDetail(p, tokenInfos, vmid);
                        }
                        JSONObject bios =  iSangForDeviceService.getVmBios(p, tokenInfos, vmid);
                        return extractHostInfos(vm, p, tokenInfo, alarmConfigRelationMap, netInfo, netCloudInfo, usageInfo,bios);
                    } catch (Exception e) {
                        StackTraceElement stackTraceElement = e.getStackTrace()[0];
                        log.error("Error extracting host info for VM: " + stackTraceElement);

                        return null;
                    }
                }, executor))
                .collect(Collectors.toList());
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .map(CompletableFuture::join)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()));
    }

    private CompletableFuture<List<ImageInfoCreateReqDTO>> processImageInfo(JSONArray clouds, PlatformconfigDTO p, JSONObject tokenInfo) {
        List<CompletableFuture<ImageInfoCreateReqDTO>> futures = clouds.stream()
                .map(vm -> CompletableFuture.supplyAsync(() -> {
                    try {
                        JSONObject jsonObject = (JSONObject) vm;
                        String vmType = jsonObject.getString("vmtype") != null ? jsonObject.getString("vmtype") : "";
                        if(StrUtil.isNotEmpty(vmType)){
                            if (!vmType.equals("tpl")){
                                return null;
                            }
                        }else{
                            return null;
                        }
                        // 构建镜像信息DTO
                        ImageInfoCreateReqDTO imageInfoDTO = new ImageInfoCreateReqDTO();
                        if (jsonObject.getString("uuid") != null) {
                            imageInfoDTO.setUuid(jsonObject.getString("uuid"));
                        } else {
                            imageInfoDTO.setUuid(jsonObject.getString("vmid"));
                        }
                        imageInfoDTO.setName(jsonObject.getString("name"));
                        imageInfoDTO.setStatus("Enabled");
                        imageInfoDTO.setFormat("qcow2");
                        imageInfoDTO.setCpuArch("x86_64");
                        String osName = jsonObject.getString("osname") != null ? jsonObject.getString("osname") : "";
                        imageInfoDTO.setOsType(osName);
                        Long size = jsonObject.getLong("disk_total") != null ? jsonObject.getLong("disk_total") : null;
                        imageInfoDTO.setSize(size);
                        imageInfoDTO.setImageType("RootVolumeTemplate");
                        boolean shared = jsonObject.getBoolean("cfgstorageshared") != null ? jsonObject.getBoolean("cfgstorageshared") : false;
                        if (shared)
                            imageInfoDTO.setSharingScope("共享");
                        else
                            imageInfoDTO.setSharingScope("不共享");

                        if (jsonObject.getLong("create_time") != null) {
                            long timestamp = jsonObject.getLong("create_time") * 1000;
                            LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
                            imageInfoDTO.setVCreateDate(localDateTime);
                            imageInfoDTO.setVUpdateDate(localDateTime);
                        }
                        imageInfoDTO.setOsLanguage("");
                        BigDecimal minMemory = jsonObject.getBigDecimal("mem_total") != null ? jsonObject.getBigDecimal("mem_total") : null;
                        imageInfoDTO.setMinMemory(minMemory);
                        imageInfoDTO.setMinDisk(BigDecimal.valueOf(size));
                        imageInfoDTO.setDiskDriver("");
                        imageInfoDTO.setNetworkDriver("");
                        imageInfoDTO.setBootMode("");
                        String graphicType = jsonObject.getString("graphic_type")!= null? jsonObject.getString("graphic_type") : "";
                        imageInfoDTO.setRemoteProtocol(graphicType);
                        if(osName.contains("linux"))
                            imageInfoDTO.setApplicationPlatform("Linux");
                        else if(osName.contains("windows"))
                            imageInfoDTO.setApplicationPlatform("Windows");
                        else
                            imageInfoDTO.setApplicationPlatform("");
                        imageInfoDTO.setPlatformId(p.getId());
                        imageInfoDTO.setPlatformName(p.getName());
                        return imageInfoDTO;
                    } catch (Exception e) {
                        StackTraceElement stackTraceElement = e.getStackTrace()[0];
                        log.error("Error extracting host info for VM: " + stackTraceElement);
                        return null;
                    }
                }, executor))
                .toList();
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .map(CompletableFuture::join)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()));
    }

    private CompletableFuture<List<HostNicCreateReqDto>> processHostNic(JSONArray clouds, PlatformconfigDTO p, JSONObject tokenInfo) {
        List<CompletableFuture<List<HostNicCreateReqDto>>> futures = clouds.stream()
                .map(vm -> CompletableFuture.supplyAsync(() -> {
                    try {
                        JSONObject jsonObject = (JSONObject) vm;
                        String vmid = jsonObject.getString("vmid");

                        // 如果 vmid 为空，则获取 netInfo
                        JSONArray netInfo = null;
                        JSONObject tokenInfos = SangForPwEncryptor.sangForlogin(p);
                        netInfo = iSangForDeviceService.getVmNics(p, tokenInfos, vmid);
                        return extractHostNics(vm, p, netInfo);
                    } catch (Exception e) {
                        StackTraceElement stackTraceElement = e.getStackTrace()[0];
                        log.error("Error extracting host info for VM: " + stackTraceElement);

                        return null;
                    }
                }, executor))
                .toList();
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .map(CompletableFuture::join)
                        .flatMap(Collection::stream)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()));
    }

    private HostInfoRespCreateReqDTO extractHostInfos(Object vm, PlatformconfigDTO p, JSONObject tokenInfo, Map<String, AlarmHostRelationInfo> alarmConfigRelationMap, JSONObject netInfo, JSONObject netCloudInfo, JSONObject usageInfo,JSONObject bios) {
        HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO = new HostInfoRespCreateReqDTO();
        List<String> deleteList = new ArrayList<>();
        JSONObject jsonObject = (JSONObject) vm;
        String vmid = jsonObject.getString("vmid");
        if (StrUtil.isEmpty(vmid)) {
            if (netInfo != null) {
                if (netInfo.getString("osName") != null) {
                    hostInfoRespCreateReqDTO.setGuestOsType(netInfo.getString("osName"));
                } else {
                    hostInfoRespCreateReqDTO.setGuestOsType("");
                }
                hostInfoRespCreateReqDTO.setIp(StringUtil.isNotEmpty(netInfo.getString("ip")) ? netInfo.getString("ip") : "");
                String mac = netInfo.getString("mac");
                if (mac == null || mac.equals("")) {
                    hostInfoRespCreateReqDTO.setMac("-");
                } else {
                    if (mac.contains(",")) {
                        // 如果包含逗号，取第一个MAC地址
                        hostInfoRespCreateReqDTO.setMac(mac.split(",")[0].trim());
                    } else {
                        // 如果不包含逗号，直接返回
                        hostInfoRespCreateReqDTO.setMac(mac);
                    }
                }
            }
        } else {
            if (netCloudInfo != null) {
                hostInfoRespCreateReqDTO.setGuestOsType(StringUtil.isNotEmpty(netCloudInfo.getString("osname")) ? netCloudInfo.getString("osname") : "");
                hostInfoRespCreateReqDTO.setIp(StringUtil.isNotEmpty(jsonObject.getString("ip")) ? jsonObject.getString("ip") : "-");
                if (netCloudInfo.getJSONObject("hardware_status").get("net0") != null) {
                    hostInfoRespCreateReqDTO.setMac(netCloudInfo.getJSONObject("hardware_status").get("net0").toString().split(",")[0].split("=")[1]);
                } else {
                    hostInfoRespCreateReqDTO.setMac("-");
                }

//                String hardWareId = netCloudInfo.getJSONObject("location").getString("id");
//                if (StringUtil.isNotEmpty(hardWareId) && !hardWareId.equals("cluster")) {
//                    JSONObject tokenInfos = SangForPwEncryptor.sangForlogin(p);
//                    String architecture = iSangForDeviceService.getHardwareDetail(p, tokenInfos, hardWareId).getString("architecture");
//                    if (architecture.toLowerCase().contains("intel") || architecture.toLowerCase().contains("amd")) {
//                        hostInfoRespCreateReqDTO.setArchitecture("x86_64");
//                    } else {
//                        hostInfoRespCreateReqDTO.setArchitecture("-");
//                    }
//                }
            }
        }


//        log.info(usageInfo.toString());
        if (jsonObject.getString("uuid") != null) {
            hostInfoRespCreateReqDTO.setUuid(jsonObject.getString("uuid"));
        } else {
            hostInfoRespCreateReqDTO.setUuid(jsonObject.getString("vmid"));
        }

        hostInfoRespCreateReqDTO.setState(stateConvert(jsonObject.getString("status")));
        hostInfoRespCreateReqDTO.setName(jsonObject.getString("name"));

        hostInfoRespCreateReqDTO.setVms(jsonObject.getString("vmid"));
        if (jsonObject.getLong("create_time") != null) {
            hostInfoRespCreateReqDTO.setVCreateDate(DateUtil.date(jsonObject.getLong("create_time") * 1000));
        }
        //无集群信息，设置默认集群信息
        hostInfoRespCreateReqDTO.setClusterName("default cluster");
        hostInfoRespCreateReqDTO.setClusterUuid("default_cluster");
        hostInfoRespCreateReqDTO.setHardwareUuid(jsonObject.getString("host"));
        if (jsonObject.getString("host") != null && !jsonObject.getString("host").isEmpty()) {
            //查询 云主机库中是否存在该硬件
            HardWareRespCreateReqDTO hardWareRespCreateReqDTO = hardWareInfoApi.getByUuid(jsonObject.getString("host")).getData();
            if (hardWareRespCreateReqDTO != null) {
                String architecture = hardWareRespCreateReqDTO.getArchitecture();
                if (architecture.toLowerCase().contains("intel") || architecture.toLowerCase().contains("amd") || architecture.toLowerCase().contains("core2duo")) {
                    hostInfoRespCreateReqDTO.setArchitecture("x86_64");
                } else {
                    hostInfoRespCreateReqDTO.setArchitecture("-");
                }
            }
        } else {
            hostInfoRespCreateReqDTO.setArchitecture("-");
        }
        hostInfoRespCreateReqDTO.setHardwareName(jsonObject.getString("hostname"));
        hostInfoRespCreateReqDTO.setMemoryUsed(jsonObject.getBigDecimal("mem_ratio").setScale(2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)));
        hostInfoRespCreateReqDTO.setMemorySize(jsonObject.getLong("mem_total"));
        hostInfoRespCreateReqDTO.setCpuUsed(jsonObject.getBigDecimal("cpu_ratio").setScale(2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)));

        hostInfoRespCreateReqDTO.setTotalDiskCapacity(jsonObject.getBigDecimal("disk_total"));
        hostInfoRespCreateReqDTO.setDiskUsedBytes(jsonObject.getBigDecimal("disk_used"));
        hostInfoRespCreateReqDTO.setDiskFreeBytes(jsonObject.getBigDecimal("disk_total").subtract(jsonObject.getBigDecimal("disk_used")));
        hostInfoRespCreateReqDTO.setDiskUsed(jsonObject.getBigDecimal("disk_used").divide(jsonObject.getBigDecimal("disk_total"), 2, BigDecimal.ROUND_HALF_UP));
        hostInfoRespCreateReqDTO.setCpuNum(jsonObject.getInteger("cpus"));
        hostInfoRespCreateReqDTO.setNetworkInPackets(jsonObject.getBigDecimal("netin_packet"));
        hostInfoRespCreateReqDTO.setNetworkInBytes(jsonObject.getBigDecimal("netin"));
        hostInfoRespCreateReqDTO.setNetworkOutPackets(jsonObject.getBigDecimal("netout_packet"));
        hostInfoRespCreateReqDTO.setNetworkOutBytes(jsonObject.getBigDecimal("netout"));
        hostInfoRespCreateReqDTO.setPlatformId(p.getId());
        hostInfoRespCreateReqDTO.setPlatformName(p.getName());
        hostInfoRespCreateReqDTO.setTypeName(p.getTypeCode());
        hostInfoRespCreateReqDTO.setTenantId(p.getTenantId());
        hostInfoRespCreateReqDTO.setRegionId(p.getRegionId());
        hostInfoRespCreateReqDTO.setType("UserVm");
        hostInfoRespCreateReqDTO.setDeleted(0);

        String status = PowerStateUtil.powerStateConvert(stateConvert(jsonObject.getString("status")));
        hostInfoRespCreateReqDTO.setPowerState(status);
        String vmType = jsonObject.getString("vmtype") != null ? jsonObject.getString("vmtype") : "";
        if(StrUtil.isNotEmpty(vmType) && vmType.equals("tpl")){
            if (jsonObject.getString("uuid") != null) {
                hostInfoRespCreateReqDTO.setImageUuid(jsonObject.getString("uuid"));
            } else {
                hostInfoRespCreateReqDTO.setImageUuid(jsonObject.getString("vmid"));
            }
            hostInfoRespCreateReqDTO.setImageName(jsonObject.getString("name"));
        }

        if(jsonObject.getInteger("ha") == 0){
            hostInfoRespCreateReqDTO.setAutoInitType("None");
        }else if(jsonObject.getInteger("ha") == 1){
            hostInfoRespCreateReqDTO.setAutoInitType("NeverStop");
        }

        if(StrUtil.isNotEmpty(bios.getString("uefi_bios")) &&  bios.getString("uefi_bios").equals("0")){
            hostInfoRespCreateReqDTO.setGuideMode("Legacy");
        }else if(StrUtil.isNotEmpty(bios.getString("uefi_bios")) &&  bios.getString("uefi_bios").equals("1")){
            hostInfoRespCreateReqDTO.setGuideMode("UEFI");
        }else {
            hostInfoRespCreateReqDTO.setGuideMode("Other");
        }

        return hostInfoRespCreateReqDTO;
    }

    private List<HostNicCreateReqDto> extractHostNics(Object vm, PlatformconfigDTO p, JSONArray items) {
        List<HostNicCreateReqDto> hostNicCreateReqDtoList = new ArrayList<>();
        JSONObject jsonObject = (JSONObject) vm;
        for (Object item : items) {
            HostNicCreateReqDto hostNicCreateReqDto = new HostNicCreateReqDto();
            JSONObject jsonItem = (JSONObject) item;
            hostNicCreateReqDto.setHostUuid((jsonObject.getString("uuid") == null || jsonObject.getString("uuid").isEmpty()) ? jsonObject.getString("vmid") : jsonObject.getString("uuid"));
            hostNicCreateReqDto.setUuid(jsonItem.getString("iface_id"));
            hostNicCreateReqDto.setIp(jsonItem.getString("ip") == null ? "" : jsonItem.getString("ip"));
            hostNicCreateReqDto.setIp6("");
            hostNicCreateReqDto.setPlatformId( p.getId());
            hostNicCreateReqDto.setPlatformName(p.getName());
            hostNicCreateReqDto.setMac(jsonItem.getString("mac_addr"));
            hostNicCreateReqDto.setDriver("virtio");
            hostNicCreateReqDto.setInClassicNetwork((byte) 0);
            hostNicCreateReqDto.setNetworkUuid(jsonItem.getString("peer_vlan_group_id"));
            hostNicCreateReqDtoList.add(hostNicCreateReqDto);
        }
        return hostNicCreateReqDtoList;
    }

    // todo 可删除
    private void handlePlatformConfig(PlatformconfigDTO p, Map<String, AlarmHostRelationInfo> alarmConfigRelationMap, Map<Long, AlarmConfigInfo> alarmConfigMap) {
        List<HostInfoRespCreateReqDTO> hostInfoRespCreateReqDTOList = new ArrayList<>();
        List<VolumeDTO> volumeDTOList = new ArrayList<>();
        List<String> deleteList = new ArrayList<>();
        try {
            if (p.getTypeCode().equals("sangFor")) {
                JSONObject tokenInfo = SangForPwEncryptor.sangForlogin(p);
                JSONArray clouds = iSangForDeviceService.getClouds(p, tokenInfo);
                for (int j = 0; j < clouds.size(); j++) {
                    HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO = new HostInfoRespCreateReqDTO();
                    JSONObject jsonObject = clouds.getJSONObject(j);
                    String vmid = jsonObject.getString("vmid");
                    if (StrUtil.isEmpty(vmid)) {
                        JSONObject netInfo = iSangForDeviceService.getNetInfos(p, tokenInfo, jsonObject.getString("vmid"));
                        if (netInfo != null) {
                            if (netInfo.getString("osName") != null) {
                                hostInfoRespCreateReqDTO.setGuestOsType(netInfo.getString("osName"));
                            } else {
                                hostInfoRespCreateReqDTO.setGuestOsType("");
                            }
                            hostInfoRespCreateReqDTO.setIp(StringUtil.isNotEmpty(netInfo.getString("ip")) ? netInfo.getString("ip") : "");
                            String mac = netInfo.getString("mac");
                            if (mac == null || mac.equals("")) {
                                hostInfoRespCreateReqDTO.setMac("-");
                            } else {
                                if (mac.contains(",")) {
                                    // 如果包含逗号，取第一个MAC地址
                                    hostInfoRespCreateReqDTO.setMac(mac.split(",")[0].trim());
                                } else {
                                    // 如果不包含逗号，直接返回
                                    hostInfoRespCreateReqDTO.setMac(mac);
                                }
                            }
                        }
                    } else {
                        JSONObject netCloudInfo = iSangForDeviceService.getNetCloudInfos(p, tokenInfo, jsonObject.getString("vmid"));
                        if (netCloudInfo != null) {
                            hostInfoRespCreateReqDTO.setGuestOsType(StringUtil.isNotEmpty(netCloudInfo.getString("osname")) ? netCloudInfo.getString("osname") : "");
                            hostInfoRespCreateReqDTO.setIp(StringUtil.isNotEmpty(jsonObject.getString("ip")) ? jsonObject.getString("ip") : "-");
                            hostInfoRespCreateReqDTO.setMac(netCloudInfo.getJSONObject("hardware_status").get("net0").toString().split(",")[0].split("=")[1]);
                            String hardWareId = netCloudInfo.getJSONObject("location").getString("id");
                            if (StringUtil.isNotEmpty(hardWareId) && !hardWareId.equals("cluster")) {
                                String architecture = iSangForDeviceService.getHardwareDetail(p, tokenInfo, hardWareId).getString("architecture");
                                if (architecture.toLowerCase().contains("intel") || architecture.toLowerCase().contains("amd")) {
                                    hostInfoRespCreateReqDTO.setArchitecture("x86_64");
                                } else {
                                    hostInfoRespCreateReqDTO.setArchitecture("-");
                                }
                            }
                        }
                    }

                    JSONObject usageInfo = iSangForDeviceService.getVmDetail(p, tokenInfo, jsonObject.getString("vmid"));
                    log.info(usageInfo.toString());
                    if (jsonObject.getString("uuid") != null) {
                        hostInfoRespCreateReqDTO.setUuid(jsonObject.getString("uuid"));
                    } else {
                        hostInfoRespCreateReqDTO.setUuid(jsonObject.getString("vmid"));
                    }

                    hostInfoRespCreateReqDTO.setState(stateConvert(jsonObject.getString("status")));
                    hostInfoRespCreateReqDTO.setName(jsonObject.getString("name"));

                    hostInfoRespCreateReqDTO.setVms(jsonObject.getString("vmid"));
                    if (jsonObject.getLong("create_time") != null) {
                        hostInfoRespCreateReqDTO.setVCreateDate(DateUtil.date(jsonObject.getLong("create_time") * 1000));
                    }

                    hostInfoRespCreateReqDTO.setClusterName(jsonObject.getString("hostname"));
                    hostInfoRespCreateReqDTO.setClusterUuid(jsonObject.getString("node"));
                    hostInfoRespCreateReqDTO.setHardwareUuid(jsonObject.getString("host"));
                    hostInfoRespCreateReqDTO.setHardwareName(jsonObject.getString("hostname"));
                    hostInfoRespCreateReqDTO.setMemoryUsed(jsonObject.getBigDecimal("mem_ratio").setScale(2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)));
                    hostInfoRespCreateReqDTO.setMemorySize(jsonObject.getLong("mem_total"));
                    hostInfoRespCreateReqDTO.setCpuUsed(jsonObject.getBigDecimal("cpu_ratio").setScale(2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)));
                    hostInfoRespCreateReqDTO.setArchitecture(jsonObject.getString("cpu"));
                    hostInfoRespCreateReqDTO.setTotalDiskCapacity(jsonObject.getBigDecimal("disk_total"));
                    hostInfoRespCreateReqDTO.setDiskUsedBytes(jsonObject.getBigDecimal("disk_used"));
                    hostInfoRespCreateReqDTO.setDiskFreeBytes(jsonObject.getBigDecimal("disk_total").subtract(jsonObject.getBigDecimal("disk_used")));
                    hostInfoRespCreateReqDTO.setDiskUsed(jsonObject.getBigDecimal("disk_used").divide(jsonObject.getBigDecimal("disk_total"), 2, BigDecimal.ROUND_HALF_UP));
                    hostInfoRespCreateReqDTO.setCpuNum(jsonObject.getInteger("cpus"));
                    hostInfoRespCreateReqDTO.setNetworkInPackets(jsonObject.getBigDecimal("netin_packet"));
                    hostInfoRespCreateReqDTO.setNetworkInBytes(jsonObject.getBigDecimal("netin"));
                    hostInfoRespCreateReqDTO.setNetworkOutPackets(jsonObject.getBigDecimal("netout_packet"));
                    hostInfoRespCreateReqDTO.setNetworkOutBytes(jsonObject.getBigDecimal("netout"));
                    hostInfoRespCreateReqDTO.setPlatformId(p.getId());
                    hostInfoRespCreateReqDTO.setPlatformName(p.getName());
                    hostInfoRespCreateReqDTO.setTypeName(p.getTypeCode());
                    hostInfoRespCreateReqDTO.setTenantId(p.getTenantId());
                    hostInfoRespCreateReqDTO.setRegionId(p.getRegionId());
                    hostInfoRespCreateReqDTO.setType("UserVm");
                    hostInfoRespCreateReqDTO.setDeleted(0);
                    hostInfoRespCreateReqDTOList.add(hostInfoRespCreateReqDTO);

                    //云盘信息

                    JSONArray diskList = usageInfo.getJSONArray("diskStatus");
                    JSONObject hardwareStatus = usageInfo.getJSONObject("hardwareStatus");
                    for (int i = 0; i < diskList.size(); i++) {
                        String json = hardwareStatus.get("ide" + i).toString();
                        HashMap<String, String> map = new HashMap<>();

                        // 将字符串分割成键值对
                        String[] pairs = json.split(",");
                        for (String pair : pairs) {
                            // 处理键值对
                            String[] keyValue;
                            if (pair.contains(":")) {
                                keyValue = pair.split(":", 2);
                            } else {
                                keyValue = pair.split("=", 2);
                            }

                            // 将键值对放入 HashMap
                            map.put(keyValue[0].trim(), keyValue[1].trim());
                        }

                        VolumeDTO volumeDTO = new VolumeDTO();
                        JSONObject diskStatus = usageInfo.getJSONArray("diskStatus").getJSONObject(i);
                        volumeDTO.setDescription("深信服云盘描述");    //描述
                        volumeDTO.setName("disk" + i);
                        volumeDTO.setFormat("vmtx");  //云盘格式
                        Long total = Long.valueOf(diskStatus.get("total").toString());
                        Long free = Convert.toLong(diskStatus.get("free"));
                        volumeDTO.setSize(total);
                        volumeDTO.setActualFree(free);
                        if (free == null) {
                            volumeDTO.setActualUse(0L);
                        } else {
                            volumeDTO.setActualUse(Convert.toLong(NumberUtil.sub(total, free)));
                        }
                        volumeDTO.setActualRatio(diskStatus.get("ratio").toString());
                        volumeDTO.setType("Data");
                        volumeDTO.setActualSize(0L);
                        volumeDTO.setState(map.get("forecast"));
                        volumeDTO.setUuid(UuidUtils.generateUuid());
                        volumeDTO.setStatus("Ready");
                        volumeDTO.setPlatformId(p.getId());
                        volumeDTO.setPlatformName(p.getName());
                        volumeDTO.setVmInstanceUuid(jsonObject.getString("uuid"));
                        volumeDTO.setPrimaryStorageUuid(UuidUtils.generateUuid());
                        volumeDTOList.add(volumeDTO);
                        deleteList.add(jsonObject.getString("uuid"));
                    }
                }
            }
        } catch (Exception e) {
            StackTraceElement stackTraceElement = e.getStackTrace()[0];
        }


        if (!volumeDTOList.isEmpty()) {
            if (!deleteList.isEmpty()) {
                volumeApi.delVolumeByInstanceUuid(deleteList);
            }
            //删除 根据云主机uuid批量删除磁盘数据
            volumeApi.addVolumes(volumeDTOList);
        }

        if (!hostInfoRespCreateReqDTOList.isEmpty()) {
            List<AlarmDorisReqDTO> alarmDorisReqDTO = new ArrayList<>();
            List<HostInfoRespCreateReqDTO> existingHosts = hostInfoApi.getVmByPlatformId(p.getId());


            if (existingHosts.isEmpty()) {
                hostInfoApi.adds(hostInfoRespCreateReqDTOList);
            } else {
                Map<String, HostInfoRespCreateReqDTO> existingHostwareMap = existingHosts.stream()
                        .collect(Collectors.toMap(HostInfoRespCreateReqDTO::getUuid, hardware -> hardware));


                List<HostInfoRespCreateReqDTO> newEntries = new ArrayList<>();
                List<HostInfoRespCreateReqDTO> updatedEntries = new ArrayList<>();
                List<HostInfoRespCreateReqDTO> deleteEntries = existingHosts.stream()
                        .filter(item -> !hostInfoRespCreateReqDTOList.stream()
                                .map(HostInfoRespCreateReqDTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid())).collect(Collectors.toList());


                final Long alertId = alarmConfigApi.getMaxAlertId().getData();
                Map<String, AlarmHostRelationInfo> finalAlarmConfigRelationMap = alarmConfigRelationMap;
                Map<Long, AlarmConfigInfo> finalAlarmConfigMap = alarmConfigMap;
                hostInfoRespCreateReqDTOList.forEach(item -> {
                    AlarmHostRelationInfo relationInfo = finalAlarmConfigRelationMap.get(item.getUuid());
                    if (ObjectUtil.isNotEmpty(relationInfo)) {
                        AlarmConfigInfo alarmConfigInfo = finalAlarmConfigMap.get(relationInfo.getAlarmId());
                        int i = 1;
                        for (HostInfoRespCreateReqDTO dto : existingHosts) {
                            //启用状态变更后发送告警
                            if (ObjectUtil.isNotEmpty(alarmConfigInfo) && alarmConfigInfo.getDictLabelValue().equals("state_change") && dto.getUuid().equals(item.getUuid()) && !dto.getState().equals(item.getState())) {
                                int alarmLevel = alarmConfigInfo.getAlarmLevel() == 1 ? 2 : (alarmConfigInfo.getAlarmLevel() == 2 ? 1 : 0);
                                AlarmDorisReqDTO alarm = new AlarmDorisReqDTO();
                                JSONObject tags = new JSONObject();
                                alarm.setId(alertId + i);
                                tags.put("app", "host");
                                tags.put("monitorId", item.getUuid());
                                tags.put("monitorName", item.getName());
                                alarm.setPlatformId(item.getPlatformId());
                                alarm.setResourceType(0);
                                alarm.setStatus(0);
                                alarm.setIsSolved(0);
                                alarm.setGmtCreate(new Date());
                                alarm.setFirstAlarmTime(DateUtil.current());
                                alarm.setGmtUpdate(new Date());
                                alarm.setLastAlarmTime(DateUtil.current());
                                alarm.setPlatformName(item.getPlatformName());
                                alarm.setTimes(1);
                                alarm.setMonitorId(item.getUuid());
                                alarm.setContent("云主机:" + item.getName() + " 的启用状态由\"" + StateConverter.stateToCh(dto.getState()) + "\"转换为\"" + StateConverter.stateToCh(item.getState()) + "\"");
                                alarm.setTarget("host.state.changed");
                                alarm.setApp("host");
                                alarm.setMonitorName(item.getName());
                                alarm.setPriority(alarmLevel);
                                alarm.setAlarmId(0L);
                                alarmDorisReqDTO.add(alarm);
                            }
                        }
                        i++;
                    }
                });
                if (!alarmDorisReqDTO.isEmpty()) {
                    Map<String, List> addMap = new HashMap<>();
                    addMap.put("updateList", new ArrayList<>());
                    addMap.put("insertList", alarmDorisReqDTO);
                    alarmConfigApi.createAlarmToDoris(addMap);
                }
                hostInfoApi.updates(updatedEntries);
                hostInfoApi.adds(newEntries);
                if (!deleteEntries.isEmpty()) {
                    hostInfoApi.deleteHostList(deleteEntries);
                }
            }
            hostInfoApi.removeDuplicateData();
        }
    }

    @XxlJob("collectVMToInflux")
    public void collectToInflux() {

        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                .filter(dto -> "sangFor".equals(dto.getTypeCode()))
                .collect(Collectors.toList());
        if (filteredList.isEmpty()) return;
        for (PlatformconfigDTO platform : filteredList) {
            try {
                processPlatform(platform);
            } catch (Exception e) {
                log.error("Error processing platform: " + platform.getName(), e);
            }
        }
    }

    private void processPlatform(PlatformconfigDTO platform) {
        ExecutorService executorService = Executors.newFixedThreadPool(9);
        try {
            List<Future<BatchPoints>> futures = new ArrayList<>();
            JSONObject tokenInfo = SangForPwEncryptor.sangForlogin(platform);
            JSONArray clouds = iSangForDeviceService.getClouds(platform, tokenInfo);
            // 创建批量写入点的容器
            List<BatchPoints> batchPointsList = Collections.synchronizedList(new ArrayList<>());

            for (int j = 0; j < clouds.size(); j++) {
                String vmid = clouds.getJSONObject(j).getString("vmid");
//            String vmid = vmInfo.getString("vmid");
                JSONObject jsonObject = clouds.getJSONObject(j);
                JSONObject usageInfo = iSangForDeviceService.getVmDetail(platform, tokenInfo, vmid);
                String uuid = jsonObject.getString("uuid");
                if(StrUtil.isEmpty(uuid)){
                    uuid = jsonObject.getString("vmid");
                }

                if ("running".equals(jsonObject.getString("status"))) {
                    Future<BatchPoints> future = executorService.submit(new Callable<BatchPoints>() {
                        @Override
                        public BatchPoints call() throws Exception {
                            try {
                                // 处理虚拟机并返回结果
                                return processVM(platform, usageInfo, jsonObject);
                            } catch (Exception e) {
                                log.error("Error processing VM: " + jsonObject.getString("name"), e);
                                return null;  // 出现异常时返回 null
                            }
                        }
                    });
                    futures.add(future);
                }
            }
            for (Future<BatchPoints> future : futures) {
                try {
                    BatchPoints result = future.get();  // 阻塞等待任务完成
                    if (result != null && !result.getPoints().isEmpty()) {
                        batchPointsList.add(result);
                    }
                } catch (InterruptedException | ExecutionException e) {
                    log.error("Error retrieving result from future", e);
                }
            }
            // 批量写入InfluxDB
            for (BatchPoints batchPoints : batchPointsList) {
                try {
                    influxDBTemplate.writeBatch(batchPoints);
                } catch (Exception e) {
                    log.error("Error writing to InfluxDB", e);
                }
            }
        } finally {
            // 关闭线程池`
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
            }
        }
    }

    private BatchPoints processVM(PlatformconfigDTO platform, JSONObject usageInfo, JSONObject vmInfo) {
        String vmid = vmInfo.getString("vmid");
        String uuid = vmInfo.getString("uuid");
        if (uuid == null) {
            uuid = vmid;
        }
        String name = vmInfo.getString("name");
        BatchPoints.Builder batchPointsBuilder = BatchPoints.builder();
        // CPU使用率
        processMetrics(batchPointsBuilder, usageInfo.getJSONArray("cpuSheet"), uuid, name, platform,
                "cpu", "CPUAverageUsedUtilization");
        // 内存使用

        processMetrics(batchPointsBuilder, usageInfo.getJSONArray("usedMemSheet"), uuid, name, platform,
                "mem", "MemoryUsedBytes");

        processMetrics(batchPointsBuilder, usageInfo.getJSONArray("freeMemSheet"), uuid, name, platform,
                "mem", "MemoryFreeBytes");

        // 内存空闲与使用率计算
        processMemoryMetrics(batchPointsBuilder, usageInfo, uuid, name, platform);
        // 磁盘相关指标
        processMetrics(batchPointsBuilder, usageInfo.getJSONArray("diskReadSheet"), uuid, name, platform,
                "disk", "DiskReadBytes");
        processMetrics(batchPointsBuilder, usageInfo.getJSONArray("diskWriteSheet"), uuid, name, platform,
                "disk", "DiskWriteBytes");
        processMetrics(batchPointsBuilder, usageInfo.getJSONArray("diskReadIOSheet"), uuid, name, platform,
                "disk", "DiskReadOps");
        processMetrics(batchPointsBuilder, usageInfo.getJSONArray("diskWriteIOSheet"), uuid, name, platform,
                "disk", "DiskWriteOps");
        // 网络相关指标
        processMetrics(batchPointsBuilder, usageInfo.getJSONArray("netOutSheet"), uuid, name, platform,
                "net", "NetworkOutBytes");
        processMetrics(batchPointsBuilder, usageInfo.getJSONArray("netInSheet"), uuid, name, platform,
                "net", "NetworkInBytes");
        processMetrics(batchPointsBuilder, usageInfo.getJSONArray("netOutPacketSheet"), uuid, name, platform,
                "net", "NetworkOutPackets");
        processMetrics(batchPointsBuilder, usageInfo.getJSONArray("netInPacketSheet"), uuid, name, platform,
                "net", "NetworkInPackets");
        // 处理网络错误率指标
        processNetworkErrorMetrics(batchPointsBuilder, usageInfo, uuid, name, platform);

        return batchPointsBuilder.build();
    }

    private void processMetrics(BatchPoints.Builder batchPointsBuilder, JSONArray metricsArray,
                                String uuid, String name, PlatformconfigDTO platform,
                                String label, String metricName) {
        if (metricsArray == null) return;
        String vmType = "all";
        if (metricName.equals("CPUAverageUsedUtilization")) {
            vmType = "CPUAverageUsedUtilization";
        }
        for (Object item : metricsArray) {
            JSONObject metricItem = JSONObject.parseObject(JSONUtil.toJsonStr(item));
            Point point = Point.measurement("zj_cloud_host")
                    .tag("uuid", uuid)
                    .tag("label", label)
                    .tag("metricName", metricName)
                    .addField("productsName", name)
                    .tag("regionId", StringUtil.toString(platform.getRegionId()))
                    .addField("regionName", StringUtil.toString(platform.getRegionName()))
                    .tag("vm_type", vmType)
                    .tag("platformId", StringUtil.toString(platform.getId()))
                    .addField("platformName", StringUtil.toString(platform.getName()))
                    .addField("vm_metricName", metricName)
                    .addField("type", vmType)
                    .addField("value", Convert.toBigDecimal(metricItem.get("val")))
                    .time(StringUtil.toLong(metricItem.get("time")), TimeUnit.SECONDS)
                    .build();

            batchPointsBuilder.point(point);
        }
    }

    private void processMemoryMetrics(BatchPoints.Builder batchPointsBuilder, JSONObject usageInfo,
                                      String uuid, String name, PlatformconfigDTO platform) {
        JSONArray freeMemSheet = usageInfo.getJSONArray("freeMemSheet");
        JSONArray usedMemSheet = usageInfo.getJSONArray("usedMemSheet");

        if (freeMemSheet == null || usedMemSheet == null) return;

        // 处理内存使用率计算逻辑
        for (Object freeItem : freeMemSheet) {
            JSONObject freeMetric = JSONObject.parseObject(JSONUtil.toJsonStr(freeItem));
            for (Object usedItem : usedMemSheet) {
                JSONObject usedMetric = JSONObject.parseObject(JSONUtil.toJsonStr(usedItem));

                if (StringUtil.toString(freeMetric.get("time"))
                        .equals(StringUtil.toString(usedMetric.get("time")))) {

                    BigDecimal totalMem = NumberUtil.add(
                            Convert.toBigDecimal(freeMetric.get("val")),
                            Convert.toBigDecimal(usedMetric.get("val"))
                    );

                    BigDecimal usedPercent = NumberUtil.div(
                            Convert.toBigDecimal(usedMetric.get("val")),
                            totalMem
                    ).multiply(BigDecimal.valueOf(100));

                    Point point = Point.measurement("zj_cloud_host")
                            .tag("uuid", uuid)
                            .tag("label", "mem")
                            .tag("metricName", "MemoryUsedInPercent")
                            .addField("productsName", name)
                            .tag("regionId", StringUtil.toString(platform.getRegionId()))
                            .addField("regionName", StringUtil.toString(platform.getRegionName()))
                            .tag("vm_type", "all")
                            .tag("platformId", StringUtil.toString(platform.getId()))
                            .addField("platformName", StringUtil.toString(platform.getName()))
                            .addField("vm_metricName", "MemoryUsedInPercent")
                            .addField("type", "all")
                            .addField("value", NumberUtil.round(usedPercent, 2))
                            .time(StringUtil.toLong(freeMetric.get("time")), TimeUnit.SECONDS)
                            .build();

                    batchPointsBuilder.point(point);
                    break;
                }
            }
        }
    }

    private void processNetworkErrorMetrics(BatchPoints.Builder batchPointsBuilder, JSONObject usageInfo,
                                            String uuid, String name, PlatformconfigDTO platform) {
        JSONArray netInPacketSheet = usageInfo.getJSONArray("netInPacketSheet");
        JSONArray netOutPacketSheet = usageInfo.getJSONArray("netOutPacketSheet");

        if (netInPacketSheet != null) {
            for (Object item : netInPacketSheet) {
                JSONObject metricItem = JSONObject.parseObject(JSONUtil.toJsonStr(item));
                Point point = Point.measurement("zj_cloud_host")
                        .tag("uuid", uuid)
                        .tag("label", "net")
                        .tag("metricName", "NetworkInErrors")
                        .addField("productsName", name)
                        .tag("regionId", StringUtil.toString(platform.getRegionId()))
                        .addField("regionName", StringUtil.toString(platform.getRegionName()))
                        .tag("vm_type", "all")
                        .tag("platformId", StringUtil.toString(platform.getId()))
                        .addField("platformName", StringUtil.toString(platform.getName()))
                        .addField("vm_metricName", "NetworkInErrors")
                        .addField("type", "all")
                        .addField("value", Convert.toBigDecimal(0))
                        .time(StringUtil.toLong(metricItem.get("time")), TimeUnit.SECONDS)
                        .build();

                batchPointsBuilder.point(point);
            }
        }
        if (netOutPacketSheet != null) {
            for (Object item : netOutPacketSheet) {
                JSONObject metricItem = JSONObject.parseObject(JSONUtil.toJsonStr(item));
                Point point = Point.measurement("zj_cloud_host")
                        .tag("uuid", uuid)
                        .tag("label", "net")
                        .tag("metricName", "NetworkOutErrors")
                        .addField("productsName", name)
                        .tag("regionId", StringUtil.toString(platform.getRegionId()))
                        .addField("regionName", StringUtil.toString(platform.getRegionName()))
                        .tag("vm_type", "all")
                        .tag("platformId", StringUtil.toString(platform.getId()))
                        .addField("platformName", StringUtil.toString(platform.getName()))
                        .addField("vm_metricName", "NetworkOutErrors")
                        .addField("type", "all")
                        .addField("value", Convert.toBigDecimal(0))
                        .time(StringUtil.toLong(metricItem.get("time")), TimeUnit.SECONDS)
                        .build();

                batchPointsBuilder.point(point);
            }
        }
    }

//        if (platformconfigDTOList.size() > 0) {
//            for (PlatformconfigDTO p : filteredList) {
//                JSONObject tokenInfo = SangForPwEncryptor.sangForlogin(p);
//                JSONArray clouds = iSangForDeviceService.getClouds(p, tokenInfo);
//                BatchPoints batchPoints = BatchPoints.builder().build();
//                for (int j = 0; j < clouds.size(); j++) {
//                    JSONObject jsonObject = clouds.getJSONObject(j);
//                    if (jsonObject.getString("status").equals("running")) {
//                        JSONObject usageInfo = iSangForDeviceService.getVmDetail(p, tokenInfo, jsonObject.getString("vmid"));
//                        String uuid = jsonObject.getString("uuid");
//                        if (uuid == null) {
//                            uuid = jsonObject.getString("vmid");
//                        }
//                        String name = jsonObject.getString("name");
//                        //cup使用率
//                        JSONArray cpuSheet = usageInfo.getJSONArray("cpuSheet");
//                        for (Object item : cpuSheet) {
//                            JSONObject usageItem = JSONObject.parseObject(JSONUtil.toJsonStr(item));
//                            Point point = Point.measurement("zj_cloud_host")
//                                    .tag("label", "cpu")
//                                    .tag("uuid", uuid)
//                                    .tag("metricName", "CPUAverageUsedUtilization")
//                                    .addField("productsName", name)
//                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
//                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
//                                    .tag("vm_type", "CPUAverageUsedUtilization")
//                                    .tag("platformId", StringUtil.toString(p.getId()))
//                                    .addField("platformName", StringUtil.toString(p.getName()))
//                                    .addField("vm_metricName", "CPUAverageUsedUtilization")
//                                    .addField("type", "CPUAverageUsedUtilization")
//                                    .addField("value", Convert.toBigDecimal(usageItem.get("val")))
//                                    .time(StringUtil.toLong(usageItem.get("time")), TimeUnit.SECONDS).build();
//                            batchPoints.point(point);
//                        }
//                        //内存使用
//                        JSONArray usedMemSheet = usageInfo.getJSONArray("usedMemSheet");
//                        for (Object item : usedMemSheet) {
//                            JSONObject usageItem = JSONObject.parseObject(JSONUtil.toJsonStr(item));
//                            Point point = Point.measurement("zj_cloud_host")
//                                    .tag("uuid", uuid)
//                                    .tag("metricName", "MemoryUsedBytes")
//                                    .tag("label", "mem")
//                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
//                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
//                                    .tag("vm_type", "all")
//                                    .tag("platformId", StringUtil.toString(p.getId()))
//                                    .addField("productsName", name)
//                                    .addField("platformName", StringUtil.toString(p.getName()))
//                                    .addField("vm_metricName", "MemoryUsedBytes")
//                                    .addField("type", "all")
//                                    .addField("value", Convert.toBigDecimal(usageItem.get("val")))
//                                    .time(StringUtil.toLong(usageItem.get("time")), TimeUnit.SECONDS).build();
//                            batchPoints.point(point);
//                        }
//
//                        //内存空闲
//                        JSONArray freeMemSheet = usageInfo.getJSONArray("freeMemSheet");
//                        for (Object item : freeMemSheet) {
//                            JSONObject usageItem = JSONObject.parseObject(JSONUtil.toJsonStr(item));
//                            Point point = Point.measurement("zj_cloud_host")
//                                    .tag("uuid", uuid)
//                                    .tag("label", "mem")
//                                    .tag("metricName", "MemoryFreeBytes")
//                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
//                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
//                                    .tag("vm_type", "all")
//                                    .tag("platformId", StringUtil.toString(p.getId()))
//                                    .addField("productsName", name)
//                                    .addField("platformName", StringUtil.toString(p.getName()))
//                                    .addField("vm_metricName", "MemoryFreeBytes")
//                                    .addField("type", "all")
//                                    .addField("value", Convert.toBigDecimal(usageItem.get("val")))
//                                    .time(StringUtil.toLong(usageItem.get("time")), TimeUnit.SECONDS).build();
//                            batchPoints.point(point);
//                            for (Object itme2 : usedMemSheet) {
//                                JSONObject usageItem2 = JSONObject.parseObject(JSONUtil.toJsonStr(itme2));
//                                if (StringUtil.toString(usageItem.get("time")).equals(StringUtil.toString(usageItem2.get("time")))) {
//                                    BigDecimal value = NumberUtil.add(Convert.toBigDecimal(usageItem.get("val")), Convert.toBigDecimal(usageItem2.get("val")));
//                                    BigDecimal usedInPercent = NumberUtil.div(Convert.toBigDecimal(usageItem2.get("val")), value).multiply(BigDecimal.valueOf(100));
//                                    Point points = Point.measurement("zj_cloud_host")
//                                            .tag("uuid", uuid)
//                                            .tag("metricName", "MemoryUsedInPercent")
//                                            .tag("label", "mem")
//                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
//                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
//                                            .tag("vm_type", "all")
//                                            .tag("platformId", StringUtil.toString(p.getId()))
//                                            .addField("productsName", name)
//                                            .addField("platformName", StringUtil.toString(p.getName()))
//                                            .addField("vm_metricName", "MemoryUsedInPercent")
//                                            .addField("type", "all")
//                                            .addField("value", NumberUtil.round(usedInPercent, 2))
//                                            .time(StringUtil.toLong(usageItem.get("time")), TimeUnit.SECONDS).build();
//                                    batchPoints.point(points);
//                                }
//                            }
//                        }
//
//
//                        //磁盘读虚度
//                        JSONArray diskReadSheet = usageInfo.getJSONArray("diskReadSheet");
//                        for (Object item : diskReadSheet) {
//                            JSONObject usageItem = JSONObject.parseObject(JSONUtil.toJsonStr(item));
//                            Point point = Point.measurement("zj_cloud_host")
//                                    .tag("uuid", uuid)
//                                    .tag("label", "disk")
//                                    .tag("metricName", "DiskReadBytes")
//                                    .addField("productsName", name)
//                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
//                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
//                                    .tag("vm_type", "all")
//                                    .tag("platformId", StringUtil.toString(p.getId()))
//                                    .addField("platformName", StringUtil.toString(p.getName()))
//                                    .addField("vm_metricName", "DiskReadBytes")
//                                    .addField("type", "all")
//                                    .addField("value", Convert.toBigDecimal(usageItem.get("val")))
//                                    .time(StringUtil.toLong(usageItem.get("time")), TimeUnit.SECONDS).build();
//                            batchPoints.point(point);
//                        }
//                        //磁盘写速度
//                        JSONArray diskWriteSheet = usageInfo.getJSONArray("diskWriteSheet");
//                        for (Object item : diskWriteSheet) {
//                            JSONObject usageItem = JSONObject.parseObject(JSONUtil.toJsonStr(item));
//                            Point point = Point.measurement("zj_cloud_host")
//                                    .tag("uuid", uuid)
//                                    .tag("label", "disk")
//                                    .tag("metricName", "DiskWriteBytes")
//                                    .addField("productsName", name)
//                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
//                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
//                                    .tag("vm_type", "all")
//                                    .tag("platformId", StringUtil.toString(p.getId()))
//                                    .addField("platformName", StringUtil.toString(p.getName()))
//                                    .addField("vm_metricName", "DiskWriteBytes")
//                                    .addField("type", "all")
//                                    .addField("value", Convert.toBigDecimal(usageItem.get("val")))
//                                    .time(StringUtil.toLong(usageItem.get("time")), TimeUnit.SECONDS).build();
//                            batchPoints.point(point);
//                        }
//                        //磁盘写IO
//                        JSONArray diskWriteIOSheet = usageInfo.getJSONArray("diskWriteIOSheet");
//                        for (Object item : diskWriteIOSheet) {
//                            JSONObject usageItem = JSONObject.parseObject(JSONUtil.toJsonStr(item));
//                            Point point = Point.measurement("zj_cloud_host")
//                                    .tag("uuid", uuid)
//                                    .tag("label", "disk")
//                                    .tag("metricName", "DiskWriteOps")
//                                    .addField("productsName", name)
//                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
//                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
//                                    .tag("vm_type", "all")
//                                    .tag("platformId", StringUtil.toString(p.getId()))
//                                    .addField("platformName", StringUtil.toString(p.getName()))
//                                    .addField("vm_metricName", "DiskWriteOps")
//                                    .addField("type", "all")
//                                    .addField("value", Convert.toBigDecimal(usageItem.get("val")))
//                                    .time(StringUtil.toLong(usageItem.get("time")), TimeUnit.SECONDS).build();
//                            batchPoints.point(point);
//                        }
//                        //磁盘读IO
//                        JSONArray diskReadIOSheet = usageInfo.getJSONArray("diskReadIOSheet");
//                        for (Object item : diskReadIOSheet) {
//                            JSONObject usageItem = JSONObject.parseObject(JSONUtil.toJsonStr(item));
//                            Point point = Point.measurement("zj_cloud_host")
//                                    .tag("uuid", uuid)
//                                    .tag("label", "disk")
//                                    .tag("metricName", "DiskReadOps")
//                                    .addField("productsName", name)
//                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
//                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
//                                    .tag("vm_type", "all")
//                                    .tag("platformId", StringUtil.toString(p.getId()))
//                                    .addField("platformName", StringUtil.toString(p.getName()))
//                                    .addField("vm_metricName", "DiskReadOps")
//                                    .addField("type", "all")
//                                    .addField("value", Convert.toBigDecimal(usageItem.get("val")))
//                                    .time(StringUtil.toLong(usageItem.get("time")), TimeUnit.SECONDS).build();
//                            batchPoints.point(point);
//                        }
//                        //网络发送速率
//                        JSONArray netOutSheet = usageInfo.getJSONArray("netOutSheet");
//                        for (Object item : netOutSheet) {
//                            JSONObject usageItem = JSONObject.parseObject(JSONUtil.toJsonStr(item));
//                            Point point = Point.measurement("zj_cloud_host")
//                                    .tag("uuid", uuid)
//                                    .tag("label", "net")
//                                    .tag("metricName", "NetworkOutBytes")
//                                    .addField("productsName", name)
//                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
//                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
//                                    .tag("vm_type", "all")
//                                    .tag("platformId", StringUtil.toString(p.getId()))
//                                    .addField("platformName", StringUtil.toString(p.getName()))
//                                    .addField("vm_metricName", "NetworkOutBytes")
//                                    .addField("type", "all")
//                                    .addField("value", Convert.toBigDecimal(usageItem.get("val")))
//                                    .time(StringUtil.toLong(usageItem.get("time")), TimeUnit.SECONDS).build();
//                            batchPoints.point(point);
//                        }
//                        //网络发送速率
//                        JSONArray netInSheet = usageInfo.getJSONArray("netInSheet");
//                        for (Object item : netInSheet) {
//                            JSONObject usageItem = JSONObject.parseObject(JSONUtil.toJsonStr(item));
//                            Point point = Point.measurement("zj_cloud_host")
//                                    .tag("uuid", uuid)
//                                    .tag("label", "net")
//                                    .tag("metricName", "NetworkInBytes")
//                                    .addField("productsName", name)
//                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
//                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
//                                    .tag("vm_type", "all")
//                                    .tag("platformId", StringUtil.toString(p.getId()))
//                                    .addField("platformName", StringUtil.toString(p.getName()))
//                                    .addField("vm_metricName", "NetworkInBytes")
//                                    .addField("type", "all")
//                                    .addField("value", Convert.toBigDecimal(usageItem.get("val")))
//                                    .time(StringUtil.toLong(usageItem.get("time")), TimeUnit.SECONDS).build();
//                            batchPoints.point(point);
//                        }
//                        //网络发包速率
//                        JSONArray netOutPacketSheet = usageInfo.getJSONArray("netOutPacketSheet");
//                        for (Object item : netOutPacketSheet) {
//                            JSONObject usageItem = JSONObject.parseObject(JSONUtil.toJsonStr(item));
//                            Point point = Point.measurement("zj_cloud_host")
//                                    .tag("uuid", uuid)
//                                    .tag("label", "net")
//                                    .tag("metricName", "NetworkOutPackets")
//                                    .addField("productsName", name)
//                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
//                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
//                                    .tag("vm_type", "all")
//                                    .tag("platformId", StringUtil.toString(p.getId()))
//                                    .addField("platformName", StringUtil.toString(p.getName()))
//                                    .addField("vm_metricName", "NetworkOutPackets")
//                                    .addField("type", "all")
//                                    .addField("value", Convert.toBigDecimal(usageItem.get("val")))
//                                    .time(StringUtil.toLong(usageItem.get("time")), TimeUnit.SECONDS).build();
//                            batchPoints.point(point);
//                        }
//                        //网络发包速率
//                        JSONArray netInPacketSheet = usageInfo.getJSONArray("netInPacketSheet");
//                        for (Object item : netInPacketSheet) {
//                            JSONObject usageItem = JSONObject.parseObject(JSONUtil.toJsonStr(item));
//                            Point point = Point.measurement("zj_cloud_host")
//                                    .tag("uuid", uuid)
//                                    .tag("label", "net")
//                                    .tag("metricName", "NetworkInPackets")
//                                    .addField("productsName", name)
//                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
//                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
//                                    .tag("vm_type", "all")
//                                    .tag("platformId", StringUtil.toString(p.getId()))
//                                    .addField("platformName", StringUtil.toString(p.getName()))
//                                    .addField("vm_metricName", "NetworkInPackets")
//                                    .addField("type", "all")
//                                    .addField("value", Convert.toBigDecimal(usageItem.get("val")))
//                                    .time(StringUtil.toLong(usageItem.get("time")), TimeUnit.SECONDS).build();
//                            batchPoints.point(point);
//                        }
//                        //网络入包丢包速率
//                        for (Object item : netInPacketSheet) {
//                            JSONObject usageItem = JSONObject.parseObject(JSONUtil.toJsonStr(item));
//                            Point point = Point.measurement("zj_cloud_host")
//                                    .tag("uuid", uuid)
//                                    .tag("label", "net")
//                                    .tag("metricName", "NetworkInErrors")
//                                    .addField("productsName", name)
//                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
//                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
//                                    .tag("vm_type", "all")
//                                    .tag("platformId", StringUtil.toString(p.getId()))
//                                    .addField("platformName", StringUtil.toString(p.getName()))
//                                    .addField("vm_metricName", "NetworkInErrors")
//                                    .addField("type", "all")
//                                    .addField("value", Convert.toBigDecimal(0))
//                                    .time(StringUtil.toLong(usageItem.get("time")), TimeUnit.SECONDS).build();
//                            batchPoints.point(point);
//                        }
//                        //网络出包丢包速率
//                        for (Object item : netOutPacketSheet) {
//                            JSONObject usageItem = JSONObject.parseObject(JSONUtil.toJsonStr(item));
//                            Point point = Point.measurement("zj_cloud_host")
//                                    .tag("uuid", uuid)
//                                    .tag("label", "net")
//                                    .tag("metricName", "NetworkOutErrors")
//                                    .addField("productsName", name)
//                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
//                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
//                                    .tag("vm_type", "all")
//                                    .tag("platformId", StringUtil.toString(p.getId()))
//                                    .addField("platformName", StringUtil.toString(p.getName()))
//                                    .addField("vm_metricName", "NetworkOutErrors")
//                                    .addField("type", "all")
//                                    .addField("value", Convert.toBigDecimal(0))
//                                    .time(StringUtil.toLong(usageItem.get("time")), TimeUnit.SECONDS).build();
//                            batchPoints.point(point);
//                        }
//                    }
//                }
//                influxDBTemplate.writeBatch(BatchPoints.builder().points(batchPoints.getPoints()).build());
//
//            }
//        }


    private String stateConvert(String state) {
        String target = "";
        switch (state) {
            case "running":
                target = "Running";
                break;
            case "starting":
                target = "Starting";
                break;
            case "stoping":
                target = "Stopping";
                break;
            case "stopped":
                target = "Stopped";
                break;
            case "resetting":
                target = "Rebooting";
                break;
            case "deleting":
                target = "Destroying";
                break;
            case "suspend":
                target = "Stopped";
                break;
            case "suspending":
                target = "Stopping";
                break;
            default:
                target = "Unknown";
        }
        return target;
    }

}
