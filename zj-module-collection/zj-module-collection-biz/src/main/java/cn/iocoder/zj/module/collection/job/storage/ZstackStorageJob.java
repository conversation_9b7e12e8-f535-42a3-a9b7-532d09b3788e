package cn.iocoder.zj.module.collection.job.storage;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.iocoder.zj.module.collection.dal.dataobject.zstack.ZstackLoginInfo;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.zstack.ZstackAccessTokenRedisDAO;
import cn.iocoder.zj.module.collection.framework.influx.config.InfluxDBTemplate;
import cn.iocoder.zj.module.collection.service.cloud.IZstackCloudService;
import cn.iocoder.zj.module.collection.service.storage.ZstackStorageService;
import cn.iocoder.zj.module.collection.util.StateConverter;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.hardwarenic.HardWareNicRespDTO;
import cn.iocoder.zj.module.monitor.api.hardwarestorage.HardWareStorageApi;
import cn.iocoder.zj.module.monitor.api.hardwarestorage.HardWareStorageRespDTO;
import cn.iocoder.zj.module.monitor.api.storage.StorageInfoApi;
import cn.iocoder.zj.module.monitor.api.storage.dto.StorageRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.storagepool.StoragePoolApi;
import cn.iocoder.zj.module.monitor.api.storagepool.dto.StoragePoolCreateRespDTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.frameworkset.elasticsearch.boot.BBossESStarter;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.zstack.sdk.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @ClassName : ZstackStorageJob  //类名
 * @Description : 存储系统定时任务    //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/6/6  13:18
 */
@Component
@Slf4j
public class ZstackStorageJob {
    @Resource
    InfluxDBTemplate influxDBTemplate;

    @Autowired
    private BBossESStarter bbossESStarter;
    @Resource
    ZstackAccessTokenRedisDAO zstackAccessTokenRedisDAO;
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    ZstackStorageService zstackStorageService;
    @Resource
    StorageInfoApi storageInfoApi;
    @Resource
    PlatformRedisDAO platformRedisDAO;
    @Resource
    IZstackCloudService zstackCloudService;
    @Autowired
    private AlarmConfigApi alarmConfigApi;
    @Resource
    HardWareStorageApi hardWareStorageApi;
    @Resource
    StoragePoolApi storagePoolApi;
    public void zstackStorageJob() {
        execute();
        storageByInfo();
    }

    @XxlJob("storageInfo")
    public void execute() {

        List<StorageRespCreateReqDTO> storageRespCreateReqDTOS = new ArrayList<>();
        // 获取redis中数据
        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                .filter(dto -> "zstack".equals(dto.getTypeCode()))
                .collect(Collectors.toList());

        if (filteredList.isEmpty()) return;

        List<PlatformconfigDTO> vmwarePlats = new ArrayList<>();
        for (PlatformconfigDTO p : filteredList) {
            // 初始化 ZStack SDK 配置
            try {
                String processedUrl = removeProtocolAndPort(p.getUrl());
                String port = extractPort(p.getUrl());
                ZSClient.configure(
                        new ZSConfig.Builder()
                                .setHostname(processedUrl)
                                .setPort(Convert.toInt(port))
                                .setContextPath("zstack")
                                .build()
                );
            } catch (Exception e) {
                log.error("ZStack SDK 调用失败: {}", e.getMessage(), e);
                return;
            }
            ZstackLoginInfo zstackLoginInfo = new ZstackLoginInfo();
            if (zstackAccessTokenRedisDAO.get("zstack:" + p.getId()) != null) {
                String token = zstackAccessTokenRedisDAO.get("zstack:" + p.getId()).getUuid();
                vmwarePlats.add(p);
                // 3：主存储总容量
                List<Map<String, Object>> totalCapa = new ArrayList<>();
                String totalCapacitys = zstackStorageService.totalPhysicalCapacityInBytes(p.getUrl(), token);
                JSONArray total = JSONObject.parseObject(totalCapacitys).getJSONArray("data");
                List<Map<String, Object>> totalMaps = parseJSONArray(total, "totalUsed", "value");
                totalMaps.stream().collect(Collectors.groupingBy(map -> map.get("PrimaryStorageUuid"))).forEach((k, v) -> {
                    Map<String, Object> map = new HashMap();
                    map.put("uuid", k);
                    map.put("totalUsed", v.get(v.size() - 1).get("totalUsed"));
                    totalCapa.add(map);
                });

                String d = zstackStorageService.storageInfo(p.getUrl(), token);
                JSONArray jsonArray = JSONObject.parseObject(d).getJSONArray("inventories");
                for (int j = 0; j < jsonArray.size(); j++) {
                    StorageRespCreateReqDTO storageRespCreateReqDTO = new StorageRespCreateReqDTO();
                    JSONObject jsonObject = jsonArray.getJSONObject(j);
//                    if ("Ceph".equals(jsonObject.getString("type"))) {

                        String name = jsonObject.getString("name");
                        String clusterUuids = jsonObject.getJSONArray("attachedClusterUuids").get(0).toString();
                        String clusters1 = zstackCloudService.clusters(p.getUrl(), token, clusterUuids);
                        JSONArray clus = JSONObject.parseObject(clusters1).getJSONArray("inventories");
                        String clusterName = clus.getJSONObject(0).getString("name");
                        String uuid = jsonObject.getString("uuid");
                        String url = jsonObject.getString("url");
                        String state = jsonObject.getString("state");
                        String tyep = jsonObject.getString("type");
                        String status = jsonObject.getString("status");
                        BigDecimal availablePhysicalCapacity = jsonObject.getBigDecimal("availablePhysicalCapacity");
                        BigDecimal totalPhysicalCapacity = jsonObject.getBigDecimal("totalPhysicalCapacity");
                        BigDecimal availableCapacity = jsonObject.getBigDecimal("availableCapacity");
                        BigDecimal capacityUtilization = new BigDecimal(0);
                        Date date = DateUtil.date(new Date(jsonObject.getString("createDate")));
                        Long totalCapacity = 0L;
                        Long usedCapacity = 0L;
                        //查询区域列表
                        QueryZoneAction action = new QueryZoneAction();
                        action.sessionId = token;
                        QueryZoneAction.Result res = action.call();
                        List regionList = res.value.inventories;

                        //区域
                        String zoneUuid = jsonObject.getString("zoneUuid");
                        storageRespCreateReqDTO.setManager("zstack");
                        storageRespCreateReqDTO.setAvailableManager("-");
                        for (Object item : regionList) {
                            if (item instanceof org.zstack.sdk.ZoneInventory) {
                                org.zstack.sdk.ZoneInventory zone = (org.zstack.sdk.ZoneInventory) item;
                                if (zone.getUuid().equals(zoneUuid)) {
                                    storageRespCreateReqDTO.setAvailableManager(zone.getName());
                                    break;
                                }
                            }
                        }

                    storageRespCreateReqDTO.setMediaType("机械盘");
                    storageRespCreateReqDTO.setStoragePercent(new BigDecimal(1));
                    //虚拟可用容量
                    BigDecimal availableDecimal = availableCapacity.compareTo(new BigDecimal(0)) > 0 ? availableCapacity : new BigDecimal(0);
                    //虚拟容量
                    storageRespCreateReqDTO.setVirtualCapacity(jsonObject.getBigDecimal("totalPhysicalCapacity").multiply(storageRespCreateReqDTO.getStoragePercent()));
                    storageRespCreateReqDTO.setAllocation(jsonObject.getBigDecimal("totalPhysicalCapacity").subtract(availableDecimal));
                    storageRespCreateReqDTO.setCommitRate(storageRespCreateReqDTO.getAllocation().divide(jsonObject.getBigDecimal("totalPhysicalCapacity"), 2, RoundingMode.HALF_UP));
                    storageRespCreateReqDTO.setRemark("-");
                    storageRespCreateReqDTO.setVUpdateTime(date);
                        //存储容量使用率
                        String usedPercent = zstackStorageService.usedCapacityInPercent(p.getUrl(), token, uuid);
                        JSONArray usedPercentJson = JSONObject.parseObject(usedPercent).getJSONArray("data");
                        if (usedPercentJson.size() > 0) {
                            int outIndex = usedPercentJson.size() - 1;
                            JSONObject networkOutBytesObj = usedPercentJson.getJSONObject(outIndex);
                            capacityUtilization = networkOutBytesObj.getBigDecimal("value");
                        }

                        // 已用容量
                        String used = zstackStorageService.usedCapacityInBytes(p.getUrl(), token, uuid);
                        JSONArray usedJson = JSONObject.parseObject(used).getJSONArray("data");
                        if (usedJson.size() > 0) {
                            int outIndex = usedJson.size() - 1;
                            JSONObject networkOutBytesObj = usedJson.getJSONObject(outIndex);
                            usedCapacity = networkOutBytesObj.getLong("value");
                        }

                        for (Map map : totalCapa) {
                            if (StringUtil.toString(map.get("uuid")).equals(uuid)) {
                                totalCapacity = ((BigDecimal) map.get("totalUsed")).longValue();
                            }
                        }
                        storageRespCreateReqDTO.setDeleted(0);
                        storageRespCreateReqDTO.setUuid(uuid);
                        storageRespCreateReqDTO.setName(name);
                        storageRespCreateReqDTO.setUrl(url);
                        storageRespCreateReqDTO.setState(state);
                        storageRespCreateReqDTO.setType(tyep);
                        storageRespCreateReqDTO.setStatus(status);
                        storageRespCreateReqDTO.setCapacityUtilization(capacityUtilization.compareTo(new BigDecimal(0)) > 0 ? (capacityUtilization.compareTo(new BigDecimal(0)) > 100 ? new BigDecimal(100) : capacityUtilization) : new BigDecimal(0));
                        storageRespCreateReqDTO.setUsedCapacity(usedCapacity);
                        storageRespCreateReqDTO.setTotalCapacity(totalCapacity);
                        storageRespCreateReqDTO.setAvailablePhysicalCapacity(availablePhysicalCapacity.compareTo(new BigDecimal(0)) > 0 ? availablePhysicalCapacity : new BigDecimal(0));
                        storageRespCreateReqDTO.setAvailableCapacity(availableDecimal);
                        storageRespCreateReqDTO.setTotalPhysicalCapacity(totalPhysicalCapacity.compareTo(new BigDecimal(0)) > 0 ? totalPhysicalCapacity : new BigDecimal(0));
                        storageRespCreateReqDTO.setRegionId(p.getRegionId());
                        storageRespCreateReqDTO.setPlatformId(p.getId());
                        storageRespCreateReqDTO.setPlatformName(p.getName());
                        storageRespCreateReqDTO.setTypeName("zstack");
                        storageRespCreateReqDTO.setClusterUuid(clusterUuids);
                        storageRespCreateReqDTO.setClusterName(clusterName);
                        storageRespCreateReqDTO.setCreateTime(date);
                        storageRespCreateReqDTO.setSCreateTime(date);
                        storageRespCreateReqDTOS.add(storageRespCreateReqDTO);
//                    }
                }
            }

        }
        for (PlatformconfigDTO p : filteredList) {
            List<HardWareStorageRespDTO> storageList = new ArrayList<>();
            String token = zstackAccessTokenRedisDAO.get("zstack:" + p.getId()).getUuid();
            String d = zstackStorageService.storageInfo(p.getUrl(), token);


            QueryHostAction action = new QueryHostAction();
            action.sessionId = token;
            QueryHostAction.Result res = action.call();
            List<KVMHostInventory> hostActionList = res.value.inventories;
            JSONObject storageObject = JSONObject.parseObject(d);
            JSONArray inventories = storageObject.getJSONArray("inventories");
            JSONObject cephStorage = inventories.getJSONObject(1);  // 获取第二个元素
            JSONArray mons = cephStorage.getJSONArray("mons");
            for (Object mon : mons) {
                HardWareStorageRespDTO storageRespDTO = new HardWareStorageRespDTO();
                JSONObject jsonObject = JSONObject.parseObject(mon.toString());
                String hostname = jsonObject.getString("hostname");
                for (KVMHostInventory item : hostActionList) {
                    if (item.name.equals(hostname)){
                        storageRespDTO.setHardwareUuid(item.uuid);
                    }
                }
                storageRespDTO.setStorageUuid(jsonObject.getString("primaryStorageUuid"));
                storageRespDTO.setPlatformId(p.getId());
                storageRespDTO.setPlatformName(p.getName());
                storageList.add(storageRespDTO);
            }
            if (storageList.size()>0){
                List<HardWareStorageRespDTO> oldList = hardWareStorageApi.getHardWareStorageByPlatformId(p.getId()).getCheckedData();
                if (oldList.isEmpty()){
                    hardWareStorageApi.adds(storageList);
                }else {
                    // 修改Map的key为uuid+hardwareUuid的组合
                    Map<String, HardWareStorageRespDTO> existingHardwareMap = oldList.stream()
                            .collect(Collectors.toMap(
                                    hardwareStorage -> hardwareStorage.getHardwareUuid() + "_" + hardwareStorage.getStorageUuid(),
                                    hardwareStorage -> hardwareStorage
                            ));

                    List<HardWareStorageRespDTO> newEntries = new ArrayList<>();
                    List<HardWareStorageRespDTO> updatedEntries = new ArrayList<>();
                    // 修改删除条件，同时比对uuid和hardwareUuid
                    List<HardWareStorageRespDTO> deleteEntries = oldList.stream()
                            .filter(item -> !storageList.stream()
                                    .anyMatch(newItem ->
                                            newItem.getHardwareUuid().equals(item.getHardwareUuid()) &&
                                                    newItem.getStorageUuid().equals(item.getStorageUuid())
                                    ))
                            .collect(Collectors.toList());

                    for (HardWareStorageRespDTO hardWareStorageRespDTO : storageList) {
                        // 使用组合key来查找
                        String compositeKey = hardWareStorageRespDTO.getHardwareUuid() + "_" + hardWareStorageRespDTO.getStorageUuid();
                        HardWareStorageRespDTO nicRespDTO = existingHardwareMap.get(compositeKey);
                        if (nicRespDTO == null) {
                            newEntries.add(hardWareStorageRespDTO);
                        } else if (!nicRespDTO.equals(hardWareStorageRespDTO)) {
                            updatedEntries.add(hardWareStorageRespDTO);
                        }
                    }
                    hardWareStorageApi.updates(updatedEntries);
                    hardWareStorageApi.adds(newEntries);
                    if (!deleteEntries.isEmpty()) {
                        hardWareStorageApi.deletes(deleteEntries);
                    }
                }
            }
        }


        if (storageRespCreateReqDTOS.size() > 0) {
            List<AlarmDorisReqDTO> alarmDorisReqDTO = new ArrayList<>();
            // 获取当前节点的index 与 总节点数
            int shardIndex = XxlJobHelper.getShardIndex();
            int shardTotal = XxlJobHelper.getShardTotal();
            log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
            int storageCount = storageInfoApi.count("zstack");
            List<StorageRespCreateReqDTO> dtos = storageInfoApi.getAll("zstack").getData();
            List<StorageRespCreateReqDTO> shardingData = new ArrayList<>();

            if (shardIndex < 0) {
                shardingData = storageRespCreateReqDTOS;
            } else {
                shardingData = StringUtil.getShardingData(storageRespCreateReqDTOS, shardTotal, shardIndex);
            }
            // 对分片数据进行业务处理
            for (StorageRespCreateReqDTO item : shardingData) {
                // 模拟业务逻辑处理
                log.info("Processing item: " + item.getName());
            }
            if (storageCount == 0) {
                storageInfoApi.adds(storageRespCreateReqDTOS);
            } else {
                List<StorageRespCreateReqDTO> storage = dtos.stream()
                        .filter(item -> !storageRespCreateReqDTOS.stream().map(StorageRespCreateReqDTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid()))
                        .collect(Collectors.toList());
                final Long alertId = alarmConfigApi.getMaxAlertId().getData();
                shardingData.forEach(item -> {
                    int i = 1;
                    for (StorageRespCreateReqDTO dto : dtos) {
                        //启用状态变更后发送告警
                        if (dto.getUuid().equals(item.getUuid()) && !dto.getStatus().equals(item.getStatus())) {
                            AlarmDorisReqDTO alarm = new AlarmDorisReqDTO();
                            JSONObject tags = new JSONObject();
                            alarm.setId(alertId + i);
                            tags.put("app", "storage");
                            tags.put("monitorId", item.getUuid());
                            tags.put("monitorName", item.getName());
                            alarm.setPlatformId(item.getPlatformId());
                            alarm.setResourceType(0);
                            alarm.setStatus(0);
                            alarm.setIsSolved(0);
                            alarm.setGmtCreate(new Date());
                            alarm.setFirstAlarmTime(DateUtil.current());
                            alarm.setGmtUpdate(new Date());
                            alarm.setLastAlarmTime(DateUtil.current());
                            alarm.setPlatformName(item.getPlatformName());
                            alarm.setTimes(1);
                            alarm.setMonitorId(item.getUuid());
                            alarm.setContent("云存储:" + item.getName() + " 的启用状态由\"" + StateConverter.stateToCh(dto.getState()) + "\"转换为\"" + StateConverter.stateToCh(item.getState()) + "\"");
                            alarm.setTarget("storage.state.changed");
                            alarm.setApp("storage");
                            alarm.setMonitorName(item.getName());
                            alarm.setPriority(1);
                            alarm.setAlarmId(0L);
                            alarmDorisReqDTO.add(alarm);
                        }
                    }
                    i++;
                });
                if (alarmDorisReqDTO.size() > 0) {
                    Map<String, List> addMap = new HashMap<>();
                    addMap.put("updateList", new ArrayList<>());
                    addMap.put("insertList", alarmDorisReqDTO);
                    alarmConfigApi.createAlarmToDoris(addMap);
                }
                // 软删除主存储历史数据
                if (storage.size() > 0) {
                    storage.forEach(item -> item.setDeleted(1));
                    storageInfoApi.updates(storage);
                }
                storageInfoApi.updates(storageRespCreateReqDTOS);
                List<StorageRespCreateReqDTO> collect = shardingData.stream()
                        .filter(item -> !dtos.stream().map(StorageRespCreateReqDTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid()))
                        .collect(Collectors.toList());
                if (collect.size() > 0) {
                    collect = collect.stream()
                            .distinct()
                            .collect(Collectors.toList());
                    storageInfoApi.adds(collect);
                }
            }
        }


    }
    public static String removeProtocolAndPort(String url) {
        // 去除协议部分
        String noProtocol = url.replaceFirst("^(http://|https://)", "");
        // 去除端口号部分
        String noPort = noProtocol.replaceFirst(":\\d+$", "");
        return noPort;
    }

    public static String extractPort(String url) {
        // 定义正则表达式模式来匹配端口号
        Pattern pattern = Pattern.compile(":\\d+$");
        Matcher matcher = pattern.matcher(url);

        // 查找匹配的端口号
        if (matcher.find()) {
            // 去掉冒号并返回端口号
            return matcher.group().substring(1);
        } else {
            // 如果没有找到端口号，根据协议返回默认端口号
            if (url.startsWith("https://")) {
                return "443";
            } else if (url.startsWith("http://")) {
                return "80";
            }
        }

        // 如果没有找到协议，返回空字符串
        return "";
    }


    @XxlJob("storage")
    public void storage() {
//
//        List<StorageDO> storageDOS = new ArrayList<>();
//        List<PlatformconfigDTO>  platformconfigDTOList = new ArrayList<>();
//        if (platformRedisDAO.get("platform") == null) {
//            platformconfigDTOList = platformconfigApi.getPlatList().getData();
//            platformRedisDAO.set("platform",platformconfigDTOList);
//        }
//
//        platformconfigDTOList = platformRedisDAO.get("platform");
//        if (platformconfigDTOList.size() > 0) {
//            for (PlatformconfigDTO p : platformconfigDTOList) {
//                String token = zstackAccessTokenRedisDAO.get("zstack:" +  p.getId()).getUuid();
//
//                // 1：存储容量使用率
//                List<Map<String, Object>> capacity = new ArrayList<>();
//                String capacitys = zstackStorageService.availablePhysicalCapacityInPercent(p.getUrl(), zstackAccessTokenRedisDAO.get("zstack:" +  p.getId()).getUuid());
//                JSONArray cap = JSONObject.parseObject(capacitys).getJSONArray("data");
//                List<Map<String, Object>> capMaps = parseJSONArray(cap, "avacapacity", "value");
//                capMaps.stream().collect(Collectors.groupingBy(map -> map.get("PrimaryStorageUuid"))).forEach((k, v) -> {
//                    Map<String, Object> map = new HashMap();
//                    map.put("uuid", k);
//                    map.put("avacapacity", v.get(v.size() - 1).get("avacapacity"));
//                    capacity.add(map);
//                });
//
//                // 3：主存储总容量
//                List<Map<String, Object>> totalCapa = new ArrayList<>();
//                String totalCapacitys = zstackStorageService.totalPhysicalCapacityInBytes(p.getUrl(), zstackAccessTokenRedisDAO.get("zstack:" +  p.getId()).getUuid());
//                JSONArray total = JSONObject.parseObject(totalCapacitys).getJSONArray("data");
//                List<Map<String, Object>> totalMaps = parseJSONArray(total, "totalUsed", "value");
//                totalMaps.stream().collect(Collectors.groupingBy(map -> map.get("PrimaryStorageUuid"))).forEach((k, v) -> {
//                    Map<String, Object> map = new HashMap();
//                    map.put("uuid", k);
//                    map.put("totalUsed", v.get(v.size() - 1).get("totalUsed"));
//                    totalCapa.add(map);
//                });
//
//                String d = zstackStorageService.storageInfo(p.getUrl(), token);
//                JSONArray jsonArray = JSONObject.parseObject(d).getJSONArray("inventories");
//                for (int j = 0; j < jsonArray.size(); j++) {
//                    StorageDO storageDO = new StorageDO();
//                    JSONObject jsonObject = jsonArray.getJSONObject(j);
//                    if ("Ceph".equals(jsonObject.getString("type"))) {
//                        String name = jsonObject.getString("name");
//                        String uuid = jsonObject.getString("uuid");
//                        String url = jsonObject.getString("url");
//                        String state = jsonObject.getString("state");
//                        String tyep = jsonObject.getString("type");
//                        String status = jsonObject.getString("status");
//                        BigDecimal capacityUtilization = new BigDecimal(0);
//                        Long totalCapacity = 0L;
//                        Long usedCapacity = 0L;
//                        //存储容量使用率
//                        for (Map map : capacity) {
//                            if (StringUtil.toString(map.get("uuid")).equals(uuid)) {
//                                capacityUtilization = (BigDecimal) map.get("avacapacity");
//                            }
//                        }
//
//
//
//                        for (Map map : totalCapa) {
//                            if (StringUtil.toString(map.get("uuid")).equals(uuid)) {
//                                totalCapacity = StringUtil.toLong(map.get("totalUsed"));
//                            }
//                        }
//
//                        storageDO.setUuid(uuid);
//                        storageDO.setName(name);
//                        storageDO.setUrl(url);
//                        storageDO.setState(state);
//                        storageDO.setType(tyep);
//                        storageDO.setStatus(status);
//                        storageDO.setCapacityUtilization(capacityUtilization);
//                        storageDO.setUsedCapacity(usedCapacity);
//                        storageDO.setTotalCapacity(totalCapacity);
//                        storageDO.setRegionId(p.getRegionId());
//                        storageDO.setCreateTime(new Date());
//                        storageDO.setCreateDate(DateUtil.formatDate(new Date()));
//                        storageDOS.add(storageDO);
//                    }
//                }
//                ClientInterface clientUtil = bbossESStarter.getRestClient();
//                String result = clientUtil.addDocuments("storage" + "_" +  p.getId(), storageDOS);
//            }
//        }
    }


    @XxlJob("storageByInfo")
    public void storageByInfo() {
        // 获取配置租户平台信息
        // 获取redis中数据
        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                .filter(dto -> "zstack".equals(dto.getTypeCode()))
                .collect(Collectors.toList());

        if (filteredList.isEmpty()) return;

        for (PlatformconfigDTO p : filteredList) {
            if (zstackAccessTokenRedisDAO.get("zstack:" + p.getId()) != null) {
                String token = zstackAccessTokenRedisDAO.get("zstack:" + p.getId()).getUuid();
                String d = zstackStorageService.storageInfo(p.getUrl(), token);
                JSONArray jsonArray = JSONObject.parseObject(d).getJSONArray("inventories");
                BatchPoints batchPoints = BatchPoints.builder().build();
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    if ("Ceph".equals(jsonObject.getString("type"))) {
                        String uuid = jsonObject.getString("uuid");
                        String name = jsonObject.getString("name");
                        String used = zstackStorageService.usedCapacityInPercent(p.getUrl(), token, uuid);
                        JSONArray netWorkInErrorsJson = JSONObject.parseObject(used).getJSONArray("data");
                        List<Map<String, Object>> netWorkInErrorsJsonMaps = parseJSONArray(netWorkInErrorsJson, "value", "value");
                        netWorkInErrorsJsonMaps.forEach(map -> {
                            Point point = Point.measurement("zj_cloud_storage")
                                    .tag("uuid", uuid)
                                    .tag("metricName", "UsedCapacityInPercent")
                                    .addField("productsName", name)
                                    .tag("regionId", StringUtil.toString(p.getRegionId()))
                                    .addField("regionName", StringUtil.toString(p.getRegionName()))
                                    .tag("storage_type", "UsedCapacityInPercent")
                                    .tag("platformId", StringUtil.toString(p.getId()))
                                    .addField("platformName", StringUtil.toString(p.getName()))

                                    .addField("storage_metricName", "UsedCapacityInPercent")
                                    .addField("type", "UsedCapacityInPercent")
                                    .addField("value", Convert.toBigDecimal(map.get("value")))
                                    .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                    .build();
                            batchPoints.point(point);
                        });
                    }
                }
                int shardIndex = XxlJobHelper.getShardIndex();
                int shardTotal = XxlJobHelper.getShardTotal();
                log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
                List<Point> s = StringUtil.getShardingData(batchPoints.getPoints(), shardTotal, shardIndex);

                influxDBTemplate.writeBatch(BatchPoints.builder().points(s).build());
            }

        }


    }

    @XxlJob("storagePoolInfo")
    public void storagePoolInfo() {
        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                .filter(dto -> "zstack".equals(dto.getTypeCode()))
                .collect(Collectors.toList());

        if (filteredList.isEmpty()) return;

        for (PlatformconfigDTO p : filteredList) {
            List<StoragePoolCreateRespDTO> pools = new ArrayList<>();
            // 初始化 ZStack SDK 配置
            try {
                String processedUrl = removeProtocolAndPort(p.getUrl());
                String port = extractPort(p.getUrl());
                ZSClient.configure(
                        new ZSConfig.Builder()
                                .setHostname(processedUrl)
                                .setPort(Convert.toInt(port))
                                .setContextPath("zstack")
                                .build()
                );
            } catch (Exception e) {
                log.error("ZStack SDK 调用失败: {}", e.getMessage(), e);
                return;
            }
            String token = zstackAccessTokenRedisDAO.get("zstack:" + p.getId()).getUuid();
            QueryCephPrimaryStoragePoolAction action = new QueryCephPrimaryStoragePoolAction();
            action.sessionId = token;
            QueryCephPrimaryStoragePoolAction.Result res = action.call();
            List<CephPrimaryStoragePoolInventory> inventories1 = res.value.inventories;
            for (CephPrimaryStoragePoolInventory pool : inventories1) {
                StoragePoolCreateRespDTO createRespDTO = new StoragePoolCreateRespDTO();
                createRespDTO.setUuid(pool.getUuid());
                createRespDTO.setName(pool.getPoolName());
                createRespDTO.setType(pool.getType());
                createRespDTO.setDescription(pool.getDescription());
                createRespDTO.setSecurityPolicy(pool.getSecurityPolicy());
                createRespDTO.setStorageUuid(pool.getPrimaryStorageUuid());
                createRespDTO.setAvailableCapacity(new BigDecimal(pool.getAvailableCapacity()));
                createRespDTO.setUsedCapacity(new BigDecimal(pool.getUsedCapacity()));
                createRespDTO.setTotalCapacity(new BigDecimal(pool.getTotalCapacity()));
                createRespDTO.setVCreateDate(pool.getCreateDate().toLocalDateTime());
                createRespDTO.setLastOpDate(pool.getLastOpDate().toLocalDateTime());
                createRespDTO.setPlatformId(p.getId());
                createRespDTO.setPlatformName(p.getName());
                pools.add(createRespDTO);
            }
            if (!pools.isEmpty()) {
                List<StoragePoolCreateRespDTO> oldStoragePools = storagePoolApi.getStoragePoolByPlatformId(p.getId()).getData();
                if (oldStoragePools.isEmpty()) {
                    storagePoolApi.adds(pools);
                } else {
                    Map<String, StoragePoolCreateRespDTO> existingStoragePoolMap = oldStoragePools.stream()
                            .collect(Collectors.toMap(
                                    storagePool -> storagePool.getUuid(),
                                    storagePool -> storagePool
                            ));
                    List<StoragePoolCreateRespDTO> newEntries = new ArrayList<>();
                    List<StoragePoolCreateRespDTO> updatedEntries = new ArrayList<>();

                    List<StoragePoolCreateRespDTO> deleteEntries = oldStoragePools.stream()
                            .filter(item -> !pools.stream()
                                    .map(StoragePoolCreateRespDTO::getUuid)
                                    .collect(Collectors.toList()).contains(item.getUuid())).collect(Collectors.toList());
                    for (StoragePoolCreateRespDTO newPool : pools) {
                        StoragePoolCreateRespDTO existingPool = existingStoragePoolMap.get(newPool.getUuid());
                        if (existingPool == null) {
                            newEntries.add(newPool);
                        } else if (!existingPool.equals(newPool)) {
                            newPool.setId(existingPool.getId());
                            updatedEntries.add(newPool);
                        }
                    }
                    storagePoolApi.updates(updatedEntries);
                    storagePoolApi.adds(newEntries);
                    if (!deleteEntries.isEmpty()) {
                        storagePoolApi.deletes(deleteEntries);
                    }
                }
            }
        }
    }



    private List<Map<String, Object>> parseJSONArray(JSONArray jsonArray, String labelKey, String valueKey) {
        List<Map<String, Object>> maps = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            Map<String, Object> map = new HashMap<>();
            if ("Ceph".equals(jsonObject.getJSONObject("labels").getString("PrimaryStorageType"))) {
                map.put("time", jsonObject.getLong("time"));
                map.put("type", jsonObject.getJSONObject("labels").getString("PrimaryStorageType"));
                map.put("PrimaryStorageUuid", jsonObject.getJSONObject("labels").getString("PrimaryStorageUuid"));
                map.put(labelKey, jsonObject.getBigDecimal(valueKey));
                maps.add(map);
            }
        }
        return maps;
    }
}
