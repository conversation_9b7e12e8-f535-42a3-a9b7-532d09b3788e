package cn.iocoder.zj.module.collection.util;

import com.alibaba.excel.metadata.data.WriteCellData;
import org.apache.commons.lang3.StringUtils;

public class StateConverter {
//    状态转换中文
    public static String stateToCh(String state){
        if (StringUtils.isEmpty(state)) {
            return "";
        }
        state = StringUtils.lowerCase(state);
        String convertde = "";
        switch (state){
            case "connecting": convertde = "正在连接";break;
            case "connected": convertde = "已连接";break;
            case "disconnected": convertde = "未连接";break;
            case "enabled": convertde = "启用";break;
            case "disabled": convertde = "停用";break;
            case "premaintenance": convertde = "预维护";break;
            case "maintenance": convertde = "维护";break;
            case "created": convertde = "已创建";break;
            case "starting": convertde = "启动中";break;
            case "running": convertde = "运行中";break;
            case "stopping": convertde = "停止中";break;
            case "stopped": convertde = "已停止";break;
            case "rebooting": convertde = "重启中";break;
            case "destroying": convertde = "销毁中";break;
            case "destroyed": convertde = "已销毁";break;
            case "expunging": convertde = "删除中";break;
            case "pausing": convertde = "暂停中";break;
            case "resuming": convertde = "恢复中";break;
            case "volumemigrating": convertde = "卷迁移中";break;
            case "migrating": convertde = "迁移中";break;
            case "paused": convertde = "已暂停";break;
            case "unknown": convertde = "未知";break;
        }
        return convertde;
    }
}
