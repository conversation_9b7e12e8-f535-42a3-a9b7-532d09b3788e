package cn.iocoder.zj.module.collection.dal.redis.winhong;

import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import com.alibaba.fastjson.JSONObject;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

import java.util.concurrent.TimeUnit;

import static cn.iocoder.zj.module.collection.dal.redis.RedisKeyConstants.WINHONG_ACCESS_TOKEN;


@Repository
public class WinHongAccessTokenRedisDAO {

    @Resource
    private StringRedisTemplate stringRedisTemplate;


    public JSONObject get(String accessToken) {
        String redisKey = formatKey(accessToken);
        return JsonUtils.parseObject(stringRedisTemplate.opsForValue().get(redisKey), JSONObject.class);
    }

    public void set(String accessToken, JSONObject loginInfo) {
        String redisKey = formatKey(accessToken);
        stringRedisTemplate.opsForValue().set(redisKey, JsonUtils.toJsonString(loginInfo),20, TimeUnit.MINUTES);
    }
    private static String formatKey(String accessToken) {
        return String.format(WINHONG_ACCESS_TOKEN, accessToken);
    }
}
