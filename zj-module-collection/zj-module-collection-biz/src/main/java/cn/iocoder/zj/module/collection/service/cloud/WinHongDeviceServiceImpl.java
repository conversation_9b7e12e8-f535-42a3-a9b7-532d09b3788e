package cn.iocoder.zj.module.collection.service.cloud;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.Method;
import cn.iocoder.zj.module.collection.collect.http.MetricsFetcher;
import cn.iocoder.zj.module.collection.dal.redis.winhong.WinHongAccessTokenRedisDAO;
import cn.iocoder.zj.module.collection.service.winHong.WinHongApiConstant;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class WinHongDeviceServiceImpl implements IWinHongDeviceService {

    @Autowired
    WinHongAccessTokenRedisDAO winHongAccessTokenRedisDAO;

    MetricsFetcher fetcher = new MetricsFetcher();

    private String fetchMetricUtilization(String url, String token, Map<String, String> header, Map<String, Object> param, Method method) {
        String response = fetcher.sendWinhongRequest(url, method, header, param, token,
                httpResponse -> {
                    if (httpResponse.getStatus() == 200) {
                        return httpResponse.body();
                    } else {
                        throw new RuntimeException("请求失败，状态码：" + httpResponse.getStatus());
                    }
                });

        return response;
    }


    @Override
    public JSONArray getHardware(PlatformconfigDTO p, String token) {
        String url = p.getUrl() + WinHongApiConstant.GET_HARDWARE_LIST;
        Map<String, Object> param = new HashMap<>();
        param.put("needClusterName", true);
        param.put("needHostRate", true);
        param.put("needPoolName", true);
        param.put("needStorageRate", true);
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("data");
    }

    @Override
    public JSONArray disksInfo(String url, String uuid, String token) {
        Map<String, Object> param = new HashMap<>();
        param.put("hostId", uuid);
        String body = fetchMetricUtilization(url + WinHongApiConstant.GET_DISKS_INFO, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("data");
    }


    @Override
    public List<Map> phynic(String url, String uuid, String token) {
        Map<String, Object> param = new HashMap<>();
        param.put("hostId", uuid);
        String body = fetchMetricUtilization(url + WinHongApiConstant.WIN_HONG_API_PREFIX + WinHongApiConstant.COMPUTE + "/hosts/" + uuid + "/phynic", token, new HashMap<>(), param, Method.GET);
        JSONArray phynic = JSONObject.parseObject(body).getJSONArray("data");
        return JSONObject.parseArray(phynic.toJSONString(), Map.class);
    }

    @Override
    public String flow(String url, String uuid, String token, String mac) {
        String endTime = Convert.toStr(DateUtil.currentSeconds());
        String startTime = Convert.toStr(DateUtil.offsetMinute(new Date(), -16).getTime() / 1000);

        Map<String, Object> param = new HashMap<>();
        param.put("hostId", uuid);
        param.put("token", token);
        param.put("mac", mac);
        param.put("endTime", endTime);
        param.put("startTime", startTime);
        return fetchMetricUtilization(url + WinHongApiConstant.GET_FLOW_INFO, token, new HashMap<>(), param, Method.GET);
    }

    @Override
    public String ratio(String url, String uuid, String token) {
        Map<String, Object> param = new HashMap<>();
        param.put("hostId", uuid);
        return fetchMetricUtilization(url + WinHongApiConstant.WIN_HONG_API_PREFIX + WinHongApiConstant.COMPUTE + "/hosts/" + uuid + "/resource/ratio", token, new HashMap<>(), param, Method.GET);
    }

    @Override
    public String cpuRate(String url, String uuid, String token) {
        String endTime = Convert.toStr(DateUtil.currentSeconds());
        String startTime = Convert.toStr(DateUtil.offsetMinute(new Date(), -16).getTime() / 1000);

        Map<String, Object> param = new HashMap<>();
        param.put("hostId", uuid);
        param.put("endTime", endTime);
        param.put("startTime", startTime);
        return fetchMetricUtilization(url + WinHongApiConstant.GET_CPU_RATE, token, new HashMap<>(), param, Method.GET);
    }

    @Override
    public String ioStat(String url, String token,Map<String, Object> param) {
        String endTime = Convert.toStr(DateUtil.currentSeconds());
        String startTime = Convert.toStr(DateUtil.offsetMinute(new Date(), -30).getTime() / 1000);

        param.put("endTime", endTime);
        param.put("startTime", startTime);
        return fetchMetricUtilization(url + WinHongApiConstant.GET_IO_STAT, token, new HashMap<>(), param, Method.GET);
    }

    @Override
    public String ioReq(String url, String token,Map<String, Object> param) {
        String endTime = Convert.toStr(DateUtil.currentSeconds());
        String startTime = Convert.toStr(DateUtil.offsetMinute(new Date(), -30).getTime() / 1000);

        param.put("endTime", endTime);
        param.put("startTime", startTime);
        return fetchMetricUtilization(url + WinHongApiConstant.GET_IO_REQ, token, new HashMap<>(), param, Method.GET);
    }

    @Override
    public JSONArray getClouds(PlatformconfigDTO p, String token) {
        String url = p.getUrl() + WinHongApiConstant.GET_CLOUDS;
        Map<String, Object> param = new HashMap<>();
        param.put("isPaginate", false);
        param.put("needDiskInfo", true);
        param.put("needHostName", true);
        param.put("needInterface", true);
        param.put("needMonitorInfo", true);
        param.put("needPoolName", true);
        param.put("needClusterName", true);
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("data");
    }

    @Override
    public JSONObject getSummary(PlatformconfigDTO p, String token,String domainId) {
        String url = p.getUrl() + WinHongApiConstant.GET_SUMMARY.replace("{domainId}",domainId);
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body);
    }

    @Override
    public JSONObject getSummaryHost(PlatformconfigDTO p,String domainId, String token) {
        String url = p.getUrl() + WinHongApiConstant.GET_SUMMARY_HOST.replace("{domainId}",domainId);
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body);
    }

    @Override
    public JSONArray getImages(PlatformconfigDTO p, String token) {
        String url = p.getUrl() + WinHongApiConstant.GET_IMAGES;
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONArray.parseObject(body).getJSONArray("data");
    }

    @Override
    public JSONArray getImagesOperationLogs(PlatformconfigDTO p, String domainTemplateId, String order, Integer size, Integer start, String token) {
        String url = p.getUrl() + WinHongApiConstant.GET_IMAGE_OPERATION_LOGS;
        Map<String, Object> param = new HashMap<>();
        param.put("domainTemplateId",domainTemplateId);
        param.put("order", order);
        param.put("size", size);
        param.put("start", start);
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONArray.parseObject(body).getJSONArray("data");
    }

    @Override
    public JSONArray getStoragePoolshosts(PlatformconfigDTO p, String token, String storagePoolId) {
        String url = p.getUrl() + WinHongApiConstant.GET_STORAGE_POOLS_HOSTS.replace("{storagePoolId}",storagePoolId);
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONArray.parseObject(body).getJSONArray("data");
    }

    @Override
    public JSONArray portGroupsVswitchs(PlatformconfigDTO p, String uuid, String token) {
        String url = p.getUrl() + WinHongApiConstant.GET_PORT_GROUPS_VSWITCHS.replace("{hostId}",uuid);
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONArray.parseArray(body);
    }

    @Override
    public JSONObject getdomainInfo(PlatformconfigDTO p, String token, String domainId) {
        String url = p.getUrl() + WinHongApiConstant.GET_DOMAIN_INFO.replace("{domainId}",domainId);
        Map<String, Object> param = new HashMap<>();
        param.put("needMonitorInfo", true);
        param.put("domainFlag", 4);
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body);
    }

    @Override
    public JSONArray getDiskStorageInfo(PlatformconfigDTO p, String token, String domainId) {
        String url = p.getUrl() + WinHongApiConstant.GET_DISK_STORAGE_INFOS.replace("{domainId}",domainId);
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONArray.parseArray(body);
    }

    @Override
    public JSONObject getCpuAndMemoryRate(PlatformconfigDTO p, String token, String domainId) {
        String url = p.getUrl() + WinHongApiConstant.GET_CPU_AND_MEMORY_RATE.replace("{domainId}",domainId);
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body);
    }

    @Override
    public JSONArray getHostCpuRate(PlatformconfigDTO p, String token, String domainIds) {
        String url = p.getUrl() + WinHongApiConstant.GET_HOST_CPU_RATE;
        String endTime = Convert.toStr(DateUtil.currentSeconds());
        String startTime = Convert.toStr(DateUtil.offsetMinute(new Date(), -16).getTime() / 1000);
        Map<String, Object> param = new HashMap<>();
        param.put("domainIds", domainIds);
        param.put("startTime", startTime);
        param.put("endTime", endTime);
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONArray.parseArray(body);
    }

    @Override
    public JSONArray getHostFlowLink(PlatformconfigDTO p, String token, String domainIds) {
        String url = p.getUrl() + WinHongApiConstant.GET_HOST_CPU_RATE;
        String endTime = Convert.toStr(DateUtil.currentSeconds());
        String startTime = Convert.toStr(DateUtil.offsetMinute(new Date(), -16).getTime() / 1000);
        Map<String, Object> param = new HashMap<>();
        param.put("domainIds", domainIds);
        param.put("startTime", startTime);
        param.put("endTime", endTime);
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONArray.parseArray(body);
    }

    @Override
    public JSONArray getHostIoStat(PlatformconfigDTO p, String token, String domainIds) {
        String url = p.getUrl() + WinHongApiConstant.GET_HOST_IO_STAT;
        String endTime = Convert.toStr(DateUtil.currentSeconds());
        String startTime = Convert.toStr(DateUtil.offsetMinute(new Date(), -16).getTime() / 1000);
        Map<String, Object> param = new HashMap<>();
        param.put("domainIds", domainIds);
        param.put("startTime", startTime);
        param.put("endTime", endTime);
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONArray.parseArray(body);
    }

    @Override
    public JSONArray getHostIoReq(PlatformconfigDTO p, String token, String domainIds) {
        String url = p.getUrl() + WinHongApiConstant.GET_HOST_IO_REQ;
        String endTime = Convert.toStr(DateUtil.currentSeconds());
        String startTime = Convert.toStr(DateUtil.offsetMinute(new Date(), -16).getTime() / 1000);
        Map<String, Object> param = new HashMap<>();
        param.put("domainIds", domainIds);
        param.put("startTime", startTime);
        param.put("endTime", endTime);
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONArray.parseArray(body);
    }

    @Override
    public JSONArray getNetworkFlow(PlatformconfigDTO p, String token, String domainIds) {
        String url = p.getUrl() + WinHongApiConstant.GET_NETWORK_FLOW;
        String endTime = Convert.toStr(DateUtil.currentSeconds());
        String startTime = Convert.toStr(DateUtil.offsetMinute(new Date(), -16).getTime() / 1000);
        Map<String, Object> param = new HashMap<>();
        param.put("domainIds", domainIds);
        param.put("startTime", startTime);
        param.put("endTime", endTime);
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONArray.parseArray(body);

    }

    @Override
    public JSONArray getStoragePools(PlatformconfigDTO p, String token) {
        String url = p.getUrl() + WinHongApiConstant.GET_STORAGE_POOLS;
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("data");

    }

    @Override
    public JSONArray getVolumes(PlatformconfigDTO p, String token, String storagePoolId) {
        String url = p.getUrl() + WinHongApiConstant.GET_VOLUMES.replace("{storagePoolId}",storagePoolId);
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("data");
    }

    @Override
    public JSONArray getNetworks(PlatformconfigDTO p, String token) {
        String url = p.getUrl() + WinHongApiConstant.GET_NETWORKS;
        Map<String, Object> param = new HashMap<>();
        param.put("showUsedIpCount", true);
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONArray.parseArray(body);
    }

    @Override
    public JSONArray getDomainDiskInfo(PlatformconfigDTO p, String token, String domainId) {
        String url = p.getUrl() + WinHongApiConstant.GET_DOMAIN_DISK_INFO.replace("{domainId}",domainId);
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("diskDevices");
    }

    @Override
    public JSONArray getSnapshots(PlatformconfigDTO p, String token) {
        String url = p.getUrl() + WinHongApiConstant.GET_SNAPSHOTS;
        Map<String, Object> param = new HashMap<>();
        param.put("isPaginate", false);
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("data");
    }

    @Override
    public Boolean noRefresh(PlatformconfigDTO p,String token) {
        String url = p.getUrl() + WinHongApiConstant.REFRESH;
        Map<String, Object> param = new HashMap<>();
        try {
             fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
             return true;
        }catch (RuntimeException e){
            return false;
        }
    }


    @Override
    public Long reserveMemory(PlatformconfigDTO p, String uuid, String token) {
        String url = p.getUrl() + WinHongApiConstant.GET_RESERVE_MEMORY.replace("{hostId}",uuid);
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        JSONObject jsonObject = JSONObject.parseObject(body);
        return jsonObject.getLong("reserveMemorySize");
    }

    @Override
    public JSONArray portGroupsV2(PlatformconfigDTO p, String uuid, String token) {
        String url = p.getUrl() + WinHongApiConstant.GET_PORT_GROUPS_V2.replace("{hostId}",uuid);
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("data");
    }

    @Override
    public JSONArray portGroups(PlatformconfigDTO p, String portGroupsId, String token) {
        String url = p.getUrl() + WinHongApiConstant.GET_PORT_GROUPS.replace("{portGroupsId}",portGroupsId);
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("data");
    }

    public String getToken(PlatformconfigDTO p) {
        String token = "";
        JSONObject tokenData = winHongAccessTokenRedisDAO.get("winhong:" + p.getId());

        // 判断 token 是否存在并且是否有效
        if (tokenData != null && tokenData.containsKey("sessionId")) {
            token = tokenData.getString("sessionId");
            Boolean isTokenValid = noRefresh(p, token);
            if (isTokenValid) {
                return token; // 如果 token 有效，直接返回
            }
        }

        log.info("Token 失效，重新获取 token...");
        JSONObject parame = new JSONObject();
        parame.put("user", p.getUsername());
        parame.put("pwd", p.getPassword());

        try {
            // 发送登录请求
            HttpRequest res = HttpRequest.post(p.getUrl() + WinHongApiConstant.LOGIN)
                    .body(parame.toJSONString());
            log.info("获取登录数据：" + res.execute().body());

            if (res.execute().getStatus() == 200) {
                JSONObject responseData = JSONObject.parseObject(res.execute().body());
                token = responseData.getString("sessionId");

                // 将 token 存入 Redis
                winHongAccessTokenRedisDAO.set("winhong:" + p.getId(), responseData);
                return token;
            } else {
                log.error("登录请求失败，状态码: " + res.execute().getStatus());
            }
        } catch (Exception e) {
            log.error("获取授权异常: " + e.getMessage(), e);
        }

        return null; // 如果所有步骤都失败，返回 null
    }
}
