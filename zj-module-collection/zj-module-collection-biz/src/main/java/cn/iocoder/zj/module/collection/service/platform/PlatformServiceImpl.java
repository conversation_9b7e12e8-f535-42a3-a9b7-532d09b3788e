package cn.iocoder.zj.module.collection.service.platform;

import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.collection.job.cloud.IStackJob;
import cn.iocoder.zj.module.collection.job.cloud.SangForCloudJob;
import cn.iocoder.zj.module.collection.job.cloud.WinHongCloudJob;
import cn.iocoder.zj.module.collection.job.cloud.ZstackCloudJob;
import cn.iocoder.zj.module.collection.job.hardware.IStackHardwareJob;
import cn.iocoder.zj.module.collection.job.hardware.SangForHardwareJob;
import cn.iocoder.zj.module.collection.job.hardware.WinHongHardwareJob;
import cn.iocoder.zj.module.collection.job.hardware.ZstackHardWareJob;
import cn.iocoder.zj.module.collection.job.network.IStackNetWorkJob;
import cn.iocoder.zj.module.collection.job.network.SangForNetWorkJob;
import cn.iocoder.zj.module.collection.job.network.WinHongNetWorkJob;
import cn.iocoder.zj.module.collection.job.network.ZstackNetWorkJob;
import cn.iocoder.zj.module.collection.job.storage.IStackStorageJob;
import cn.iocoder.zj.module.collection.job.storage.SangForStorageJob;
import cn.iocoder.zj.module.collection.job.storage.WinHongStorageJob;
import cn.iocoder.zj.module.collection.job.storage.ZstackStorageJob;
import cn.iocoder.zj.module.collection.job.vmware.VmwareHostJob;
import cn.iocoder.zj.module.collection.job.vmware.VmwareStorageJob;
import cn.iocoder.zj.module.collection.job.vmware.VmwareVmJob;
import cn.iocoder.zj.module.collection.job.volume.ZstackVolumeJob;
import cn.iocoder.zj.module.collection.job.vpc.ZstackVpcJob;
import cn.iocoder.zj.module.collection.mq.message.platform.PlatformSendMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @ClassName : PlatformServiceImpl  //类名
 * @Description :   接收平台删除消息 批量处理垃圾数据//描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/5/10  15:12
 */
@Service
@Slf4j
public class PlatformServiceImpl implements PlatformService {

    @Resource
    private ZstackCloudJob zstackCloudJob;
    @Resource
    private SangForCloudJob sangForCloudJob;
    @Resource
    private ZstackHardWareJob zstackHardWareJob;
    @Resource
    private SangForHardwareJob sangForHardwareJob;
    @Resource
    private ZstackNetWorkJob zstackNetWorkJob;

    @Resource
    private SangForNetWorkJob sangForNetWorkJob;
    @Resource
    private SangForStorageJob sangForStorageJob;
    @Resource
    ZstackStorageJob zstackStorageJob;

    @Resource
    ZstackVolumeJob zstackVolumeJob;
    @Resource
    ZstackVpcJob zstackVpcJob;

    @Resource
    VmwareHostJob vmwareHostJob;
    @Resource
    VmwareStorageJob vmwareStorageJob;
    @Resource
    VmwareVmJob vmwareVmJob;

    @Resource
    WinHongCloudJob winHongCloudJob;
    @Resource
    WinHongHardwareJob winHongHardwareJob;
    @Resource
    WinHongStorageJob winHongStorageJob;
    @Resource
    WinHongNetWorkJob winHongNetWorkJob;

    @Resource
    IStackJob iStackJob;
    @Resource
    IStackHardwareJob iStackHardwareJob;
    @Resource
    IStackStorageJob iStackStorageJob;
    @Resource
    IStackNetWorkJob iStackNetWorkJob;

    /**
     * @description: 根据发送过来的消息解析，并删除所有相关平台的数据
     * @param: platformSendMessage
     * @return: void
     * <AUTHOR>
     * @date: 2024/5/11 13:21
     */
    @Override
    @TenantIgnore
    public void doSendPlatform(PlatformSendMessage platformSendMessage) {
        zstackCloudJob.zstackCloudJobList();
        zstackHardWareJob.zstackHardWareJob();
        zstackNetWorkJob.zstackNetWorkJob();
        zstackStorageJob.zstackStorageJob();
        zstackVolumeJob.zstackVolumeJob();
        zstackVpcJob.zstackVpcJob();

        sangForStorageJob.getSangForStorage();
        sangForNetWorkJob.sangForNetWorkJob();
        sangForCloudJob.sangForCloudJobList();
        sangForHardwareJob.sangForHardwareJob();

        //
        winHongCloudJob.winHongJobList();
        winHongHardwareJob.winHongHardWareJob();
        winHongStorageJob.winHongStorageJob();
        winHongNetWorkJob.winHongNetWorkJob();

        iStackJob.IStackCloudJobList();
        iStackHardwareJob.IStackHardwareJob();
        iStackStorageJob.IStackStorageJob();
        iStackNetWorkJob.IStackNetWorkJob();

        try {
            vmwareHostJob.vmHostJobList();
            vmwareStorageJob.vmstorageJobList();
            vmwareVmJob.vmstorageJobList();
        } catch (Exception e) {
            log.info("Vmware采集数据异常" + e.getMessage());
        }
    }
}
