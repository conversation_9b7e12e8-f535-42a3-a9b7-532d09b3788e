package cn.iocoder.zj.module.collection.service.volumes;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.zstack.ZstackAccessTokenRedisDAO;
import cn.iocoder.zj.module.collection.handler.HttpClientWrapper;
import cn.iocoder.zj.module.collection.service.zstack.core.ZstackApiConstant;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.monitor.api.hostinfo.HostInfoApi;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.valume.VolumeApi;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeAttachableVmDTO;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeDTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ZstackVolumesServiceImpl implements ZstackVolumesService {

    @Autowired
    ZstackAccessTokenRedisDAO zstackAccessTokenRedisDAO;
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    VolumeApi volumeApi;
    @Resource
    HostInfoApi hostInfoApi;
    @Resource
    PlatformRedisDAO platformRedisDAO;
    @Autowired
    private HttpClientWrapper httpClientWrapper;
    @Override
    public String volumeBaseInfo(String url, String token){
        HttpRequest request = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_VOLUMES)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8");
        HttpResponse result = httpClientWrapper.execute(request);
        if (result.getStatus() != 200) {
            throw new RuntimeException("getVolume error");
        }
        return result.body();
    }

    @Override
    public String volumeSnapshotInfo(String url, String token) {
        HttpRequest request = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_VOLUMES_SNAPSHOTS)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8");
        HttpResponse result = httpClientWrapper.execute(request);
        if (result.getStatus() != 200) {
            throw new RuntimeException("getVolumeSnapshot error");
        }
        return result.body();
    }

    /**
     * 挂载云盘
     *
     * @param volumeUuid
     * @param hostUuid
     * @return
     */
    @Override
    public CommonResult<Map<String,String>> mountVolumeToHost(String volumeUuid, String hostUuid, Long platformId) {
        String token = zstackAccessTokenRedisDAO.get("zstack:" +platformId).getUuid();
        PlatformconfigDTO platformconfig = platformconfigApi.getByConfigId(platformId).getData();
        HttpRequest request = HttpRequest.post(platformconfig.getUrl() + ZstackApiConstant.GET_ZSTACK_VOLUMES + "/" + volumeUuid + "/vm-instances/" + hostUuid)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .timeout(600000);
        HttpResponse result = httpClientWrapper.execute(request);
        if (result.getStatus() != 200) {
            throw new RuntimeException("挂载云盘 error");
        }
        String respBody = HttpRequest.get(JSONObject.parseObject(result.body()).get("location").toString()).execute().body();
        while (respBody.equals("{}")){
            respBody = HttpRequest.get(JSONObject.parseObject(result.body()).get("location").toString()).execute().body();
        }
        String errorMsg = "";
        Map<String,String> reuslt = new HashMap<>();
        reuslt.put("success","true");
        reuslt.put("msg",errorMsg);
        if(JSONObject.parseObject(respBody).get("error")!=null) {
            JSONObject obj = JSONObject.parseObject(JSONObject.toJSONString(JSONObject.parseObject(respBody).get("error")));
            errorMsg = StringUtil.isNullOrEmpty(obj.getString("details")) ?
                       obj.getString("description") : obj.getString("details");
            reuslt.put("success", "false");
            reuslt.put("msg",errorMsg);
            return CommonResult.success(reuslt);
        }
        collectVolumeInfo();
        getDataVolumeAttachableVm();
        collectVolumeSnapshotRefresh();
        return CommonResult.success(reuslt);
    }
    @Override
    public void mountVolumeToHardware(String volumeUuid, String hardwareUuid,Long platformId,String mountPath) {
        String token = zstackAccessTokenRedisDAO.get("zstack:" +platformId).getUuid();
        PlatformconfigDTO platformconfig = platformconfigApi.getByConfigId(platformId).getData();
        HttpRequest request = HttpRequest.post(platformconfig.getUrl() + ZstackApiConstant.GET_ZSTACK_VOLUMES + "/" + volumeUuid + "/host/" + hardwareUuid)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .body("{\"params\":{\"mountPath\":\""+mountPath+"\"}}");
        HttpResponse result = httpClientWrapper.execute(request);
        if (result.getStatus() != 200) {
            throw new RuntimeException("mountVolumeToHardware error");
        }
    }

    @Override
    public CommonResult<Map<String,String>> uninstallVolumeFromHost(String volumeUuid, String hostUuid, Long platformId) {
        String token = zstackAccessTokenRedisDAO.get("zstack:" +platformId).getUuid();
        PlatformconfigDTO platformconfig = platformconfigApi.getByConfigId(platformId).getData();

        HttpRequest request = HttpRequest.delete(platformconfig.getUrl() + ZstackApiConstant.GET_ZSTACK_VOLUMES + "/" + volumeUuid + "/vm-instances?")
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .body("{\"params\":{\"vmUuid\":\""+hostUuid+"\"}}")
                .timeout(600000);
        HttpResponse result = httpClientWrapper.execute(request);
            String respBody = HttpRequest.get(JSONObject.parseObject(result.body()).get("location").toString()).execute().body();
            while (respBody.equals("{}")){
                respBody = HttpRequest.get(JSONObject.parseObject(result.body()).get("location").toString()).execute().body();
            }
        String errorMsg = "";
        Map<String,String> reuslt = new HashMap<>();
        reuslt.put("success","true");
        reuslt.put("msg",errorMsg);
        if (JSONObject.parseObject(respBody).get("error")!=null) {
            JSONObject obj = JSONObject.parseObject(JSONObject.toJSONString(JSONObject.parseObject(respBody).get("error")));
            errorMsg = StringUtil.isNullOrEmpty(obj.getString("details"))?
                       obj.getString("description"):obj.getString("details");
            reuslt.put("success", "false");
            if(obj.getString("code").equals("SYS.1006")){
                errorMsg = "当前云主机不支持热卸载云盘，请停止云主机后再尝试";
            }
            reuslt.put("msg",errorMsg);
            return CommonResult.success(reuslt);
        }
        collectVolumeInfo();
        getDataVolumeAttachableVm();
        return CommonResult.success(reuslt);
    }

    @Override
    public void uninstallVolumeFromHardware(String volumeUuid, String hardwareUuid, Long platformId) {
        String token = zstackAccessTokenRedisDAO.get("zstack:" +platformId).getUuid();
        PlatformconfigDTO platformconfig = platformconfigApi.getByConfigId(platformId).getData();
        HttpRequest request = HttpRequest.delete(platformconfig.getUrl() + ZstackApiConstant.GET_ZSTACK_VOLUMES + "/" + volumeUuid + "/hosts/")
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .body("{\"params\":{\"hostUuid \":\""+hardwareUuid+"\"}}");
        HttpResponse result = httpClientWrapper.execute(request);
        if (result.getStatus() != 200) {
            throw new RuntimeException("mountVolumeToHardware error");
        }
    }

    @Override
    public String getVmAttachableDataVolume(String url, String token,String hostUuid) {
        HttpRequest request = HttpRequest.get(url + ZstackApiConstant.GET_VM_ATTACHABLE_DATA_VOLUME + "/" + hostUuid + "/data-volume-candidates")
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8");
        HttpResponse result = httpClientWrapper.execute(request);
        if (result.getStatus() != 200) {
            throw new RuntimeException("getDataVolumeAttachableVm error");
        }
        return  result.body();
    }

    /**
     * 获取云盘数据
     */
    public void collectVolumeInfo() {
        List<VolumeDTO> volumeDTOList = new ArrayList<>();
        List<PlatformconfigDTO> platformconfigDTOList = new ArrayList<>();
        if (platformRedisDAO.get("platform") == null) {
            platformconfigDTOList = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform",platformconfigDTOList);
        }
        platformconfigDTOList = platformRedisDAO.get("platform");
        if (platformconfigDTOList.size() > 0) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                if (zstackAccessTokenRedisDAO.get("zstack:" + p.getId()) != null) {
                    String token = zstackAccessTokenRedisDAO.get("zstack:" + p.getId()).getUuid();
                    String volumeStr = volumeBaseInfo(p.getUrl(), token);
                    JSONArray volumes = JSONObject.parseObject(volumeStr).getJSONArray("inventories");
                    for (int i = 0; i < volumes.size(); i++) {
                        VolumeDTO volumeDTO = new VolumeDTO();
                        JSONObject jsonObject = volumes.getJSONObject(i);
                        String uuid = jsonObject.getString("uuid");
                        Long platformId = p.getId();
                        String platformName = p.getName();
                        String name = jsonObject.getString("name");
                        String description = jsonObject.getString("description");
                        String primaryStorageUuid = jsonObject.getString("primaryStorageUuid");
                        String vminstanceUuid = jsonObject.getString("vmInstanceUuid");
                        String type = jsonObject.getString("type");
                        String format = jsonObject.getString("format");
                        Long size = jsonObject.getLong("size");
                        Long actualSize = jsonObject.getLong("actualSize");
                        String state = jsonObject.getString("state");
                        String status = jsonObject.getString("status");

                        volumeDTO.setDescription(description);
                        volumeDTO.setName(name);
                        volumeDTO.setFormat(format);
                        volumeDTO.setSize(size);
                        volumeDTO.setType(type);
                        volumeDTO.setActualSize(actualSize);
                        volumeDTO.setState(state);
                        volumeDTO.setUuid(uuid);
                        volumeDTO.setStatus(status);
                        volumeDTO.setPlatformId(platformId);
                        volumeDTO.setPlatformName(platformName);
                        volumeDTO.setVmInstanceUuid(vminstanceUuid);
                        volumeDTO.setPrimaryStorageUuid(primaryStorageUuid);
                        volumeDTOList.add(volumeDTO);
                    }
                }
            }
        }

        if (volumeDTOList.size() > 0) {
            // 获取当前节点的index 与 总节点数
            Long volumeCount = volumeApi.getVolumeCount().getData();
            List<VolumeDTO> dtos = volumeApi.getAllVolumes(-1L,"zstack").getData();
            // 对分片数据进行业务处理
            if (volumeCount == 0) {
                volumeApi.addVolumes(volumeDTOList);
            } else {
                volumeApi.updateVolumes(volumeDTOList);
                //待添加数据
                List<VolumeDTO> collect = volumeDTOList.stream()
                        .filter(item -> !dtos.stream().map(VolumeDTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid()))
                        .collect(Collectors.toList());
                //待删除数据
                List<VolumeDTO> deleteTarget = dtos.stream()
                        .filter(item -> !(volumeDTOList.stream().map(VolumeDTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid()))
                        )
                        .collect(Collectors.toList());
                if (deleteTarget.size() > 0) {
                    volumeApi.delVolumes(deleteTarget);
                }
                if (collect.size() > 0) {
                    volumeApi.addVolumes(collect);
                }
            }
        }
    }
    /**
     * 获取云主机可挂载云盘数据
     */
    public void getDataVolumeAttachableVm(){
        List<PlatformconfigDTO> platformconfigDTOList = new ArrayList<>();
        if (platformRedisDAO.get("platform") == null) {
            platformconfigDTOList = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform",platformconfigDTOList);
        }
        platformconfigDTOList = platformRedisDAO.get("platform");
        List<VolumeAttachableVmDTO> dtoList = new ArrayList<>();
        List<HostInfoRespCreateReqDTO> hostDTOs = hostInfoApi.getAll("zstack").getData();
        if (platformconfigDTOList.size() > 0) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                if (p.getTypeCode().equals("zstack") && zstackAccessTokenRedisDAO.get("zstack:" + p.getId()) != null) {
                    List<HostInfoRespCreateReqDTO> targetHosts =  hostDTOs.stream().filter(item  ->(item.getState().equals("Running")||item.getState().equals("Stopped")) && Objects.equals(item.getPlatformId(), p.getId()))
                            .collect(Collectors.toList());
                    for (HostInfoRespCreateReqDTO hostDTO : targetHosts) {String token = zstackAccessTokenRedisDAO.get("zstack:" + p.getId()).getUuid();String volumeStr = getVmAttachableDataVolume(p.getUrl(), token,hostDTO.getUuid());JSONArray volumeAttachableVm = JSONObject.parseObject(volumeStr).getJSONArray("inventories");if (volumeAttachableVm.size() > 0) {
                        for (int i = 0; i < volumeAttachableVm.size(); i++) {
                            JSONObject jsonObject = volumeAttachableVm.getJSONObject(i);
                            VolumeAttachableVmDTO attachableVmDto = new VolumeAttachableVmDTO();
                            attachableVmDto.setVolumeName(jsonObject.getString("name"));
                            attachableVmDto.setVolumeUuid(jsonObject.getString("uuid"));
                            attachableVmDto.setHostName(hostDTO.getName());
                            attachableVmDto.setHostUuid(hostDTO.getUuid());
                            attachableVmDto.setPlatformName(p.getName());
                            attachableVmDto.setPlatformId(p.getId());
                            dtoList.add(attachableVmDto);
                            }
                        }
                    }
                }
            }
        }
        if (dtoList.size() > 0) {
            Long attachableVmCount = volumeApi.getAttachableVmCount().getData();
            List<VolumeAttachableVmDTO> dtos = volumeApi.getAllVolumeAttachableVm().getData();
            // 对分片数据进行业务处理
            if (attachableVmCount == 0) {
                volumeApi.addVolumeAttachableVms(dtoList);
            } else {
                volumeApi.updateVolumeAttachableVms(dtoList);
                List<VolumeAttachableVmDTO> collect = dtoList.stream()
                        .filter(item -> !(dtos.stream().map(VolumeAttachableVmDTO::getVolumeUuid)
                                .collect(Collectors.toList()).contains(item.getVolumeUuid())
                                && dtos.stream().map(VolumeAttachableVmDTO::getHostUuid)
                                .collect(Collectors.toList()).contains(item.getHostUuid()))
                        )
                        .collect(Collectors.toList());
                List<VolumeAttachableVmDTO> deleteTarget = dtos.stream()
                        .filter(item -> !(dtoList.stream().map(VolumeAttachableVmDTO::getVolumeUuid)
                                .collect(Collectors.toList()).contains(item.getVolumeUuid())
                                && dtoList.stream().map(VolumeAttachableVmDTO::getHostUuid)
                                .collect(Collectors.toList()).contains(item.getHostUuid()))
                        )
                        .collect(Collectors.toList());
                if (deleteTarget.size()>0){
                    volumeApi.delVolumeAttachableVms(deleteTarget);
                }
                if (collect.size() > 0) {
                    volumeApi.addVolumeAttachableVms(collect);
                }
            }
        }
    }

    /**
     * 云盘快照刷新
     *
     */
    public void collectVolumeSnapshotRefresh() {
        List<PlatformconfigDTO> platformconfigDTOList = new ArrayList<>();
        if (platformRedisDAO.get("platform") == null) {
            platformconfigDTOList = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform",platformconfigDTOList);
        }
        platformconfigDTOList = platformRedisDAO.get("platform");
        List<VolumeAttachableVmDTO> dtoList = new ArrayList<>();
        List<HostInfoRespCreateReqDTO> hostDTOs = hostInfoApi.getAll("zstack").getData();
        if (platformconfigDTOList.size() > 0) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                List<HostInfoRespCreateReqDTO> targetHosts =  hostDTOs.stream().filter(item  ->!item.getState().equals("Destroyed") && Objects.equals(item.getPlatformId(), p.getId()))
                        .collect(Collectors.toList());
                for (HostInfoRespCreateReqDTO hostDTO : targetHosts) {
                    if (zstackAccessTokenRedisDAO.get("zstack:" + p.getId()) != null) {
                        String token = zstackAccessTokenRedisDAO.get("zstack:" + p.getId()).getUuid();
                        try {
                            String volumeStr=getVmAttachableDataVolume(p.getUrl(), token, hostDTO.getUuid());
                            // 在没有抛出异常的情况下执行的代码
                            JSONArray volumeAttachableVm = JSONObject.parseObject(volumeStr).getJSONArray("inventories");
                            if (volumeAttachableVm.size() > 0) {
                                for (int i = 0; i < volumeAttachableVm.size(); i++) {
                                    JSONObject jsonObject = volumeAttachableVm.getJSONObject(i);
                                    VolumeAttachableVmDTO attachableVmDto = new VolumeAttachableVmDTO();
                                    attachableVmDto.setVolumeName(jsonObject.getString("name"));
                                    attachableVmDto.setVolumeUuid(jsonObject.getString("uuid"));
                                    attachableVmDto.setHostName(hostDTO.getName());
                                    attachableVmDto.setHostUuid(hostDTO.getUuid());
                                    attachableVmDto.setPlatformName(p.getName());
                                    attachableVmDto.setPlatformId(p.getId());
                                    dtoList.add(attachableVmDto);
                                }
                            }
                        } catch (RuntimeException e) {
                        }
                    }
                }
            }
        }
        if (dtoList.size() > 0) {
            Long attachableVmCount = volumeApi.getAttachableVmCount().getData();
            List<VolumeAttachableVmDTO> dtos = volumeApi.getAllVolumeAttachableVm().getData();
            if (attachableVmCount == 0) {
                volumeApi.addVolumeAttachableVms(dtoList);
            } else {
                volumeApi.updateVolumeAttachableVms(dtoList);
                List<VolumeAttachableVmDTO> collect = dtoList.stream()
                        .filter(item -> !(dtos.stream().map(VolumeAttachableVmDTO::getVolumeUuid)
                                .collect(Collectors.toList()).contains(item.getVolumeUuid())
                                && dtos.stream().map(VolumeAttachableVmDTO::getHostUuid)
                                .collect(Collectors.toList()).contains(item.getHostUuid()))
                        )
                        .collect(Collectors.toList());
                List<VolumeAttachableVmDTO> deleteTarget = dtos.stream()
                        .filter(item -> !(dtoList.stream().map(VolumeAttachableVmDTO::getVolumeUuid)
                                .collect(Collectors.toList()).contains(item.getVolumeUuid())
                                && dtoList.stream().map(VolumeAttachableVmDTO::getHostUuid)
                                .collect(Collectors.toList()).contains(item.getHostUuid()))
                        )
                        .collect(Collectors.toList());
                if (deleteTarget.size()>0){
                    volumeApi.delVolumeAttachableVms(deleteTarget);
                }
                if (collect.size() > 0) {
                    volumeApi.addVolumeAttachableVms(collect);
                }
            }
        }
    }
}
