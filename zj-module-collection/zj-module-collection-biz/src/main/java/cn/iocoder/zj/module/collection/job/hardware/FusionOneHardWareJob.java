package cn.iocoder.zj.module.collection.job.hardware;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.util.collection.CollectionUtils;
import cn.iocoder.zj.module.collection.dal.dataobject.zstack.AlarmConfigInfo;
import cn.iocoder.zj.module.collection.dal.dataobject.zstack.AlarmHostRelationInfo;
import cn.iocoder.zj.module.collection.dal.redis.fusionOne.FusionOneAccessTokenRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.zstack.AlarmInfoRedisDAO;
import cn.iocoder.zj.module.collection.framework.influx.config.InfluxDBTemplate;
import cn.iocoder.zj.module.collection.service.cloud.FusionOneDeviceService;
import cn.iocoder.zj.module.collection.util.StateConverter;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import cn.iocoder.zj.module.monitor.api.hardware.HardWareInfoApi;
import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.hardwarenic.HardWareNicApi;
import cn.iocoder.zj.module.monitor.api.hardwarenic.HardWareNicRespDTO;
import cn.iocoder.zj.module.monitor.api.storage.StorageInfoApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class FusionOneHardWareJob {


    @Resource
    PlatformRedisDAO platformRedisDAO;

    @Resource
    PlatformconfigApi platformconfigApi;

    @Resource
    AlarmInfoRedisDAO alarmInfoRedisDAO;

    @Resource
    FusionOneAccessTokenRedisDAO fusionOneAccessTokenRedisDAO;

    @Resource
    FusionOneDeviceService fusionOneDeviceService;

    @Resource
    HardWareInfoApi hardWareInfoApi;

    @Autowired
    private AlarmConfigApi alarmConfigApi;

    @Resource
    InfluxDBTemplate influxDBTemplate;

    @Resource
    StorageInfoApi storageInfoApi;

    @Resource
    HardWareNicApi hardWareNicApi;



    @XxlJob("fusionOneHarWareJob")
    @Transactional
    public void collectFusionOneHarWareJob() {
        List<PlatformconfigDTO> platformconfigDTOList = new ArrayList<>();
        if (platformRedisDAO.get("platform") == null) {
            platformconfigDTOList = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform", platformconfigDTOList);
        }
        List<AlarmHostRelationInfo> alarmHostRealtionList = JSON.parseArray(alarmInfoRedisDAO.getRelation("alarm_host_relation"), AlarmHostRelationInfo.class);
        List<AlarmConfigInfo> alarmConfigInfoList = JSON.parseArray(alarmInfoRedisDAO.getConfig("alarm_config"), AlarmConfigInfo.class);
        Map<Long,AlarmConfigInfo> alarmConfigMap = new HashMap<>();
        Map<String,AlarmHostRelationInfo> alarmConfigRelationMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(alarmConfigInfoList)) {
            alarmConfigInfoList.removeIf(item->item.getDeleted()==1);
            alarmConfigMap = CollectionUtils.convertMap(alarmConfigInfoList,AlarmConfigInfo::getId);
        }
        if (ObjectUtil.isNotEmpty(alarmHostRealtionList)) {
            alarmHostRealtionList.removeIf(item->item.getStatus()==1);
            alarmConfigRelationMap = CollectionUtils.convertMap(alarmHostRealtionList,AlarmHostRelationInfo::getHostUuid);
        }
        List<HardWareRespCreateReqDTO> hardWareRespCreateReqDTOS = new ArrayList<>();
        List<HardWareNicRespDTO> nicList = new ArrayList<>();
        platformconfigDTOList = platformRedisDAO.get("platform");
        if (platformconfigDTOList.size() > 0) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                if (p.getTypeCode().equals("fusionOne") && fusionOneAccessTokenRedisDAO.get("FusionOne:" + p.getId()) != null) {
                    String token = fusionOneAccessTokenRedisDAO.get("FusionOne:" + p.getId()).get("token").toString();
                    if (token==null){
                        log.info("获取FusionOne token失败");
                        return;
                    }
                    String siteId = fusionOneAccessTokenRedisDAO.get("FusionOne:" + p.getId()).get("siteId").toString();
                    String siteName = fusionOneAccessTokenRedisDAO.get("FusionOne:" + p.getId()).get("siteName").toString();
                    JSONArray hosts = fusionOneDeviceService.getHosts(p,token,siteId);
                    if (ObjectUtil.isNull(hosts)) return;

                    JSONArray storages = fusionOneDeviceService.getStorages(p, token, siteId);
                    if (ObjectUtil.isNull(storages)) continue;
                    Long num = 0L;
                    Long used = 0L;
                    for (Object storage : storages) {
                        JSONObject jsonObject = (JSONObject) storage;
                        num  += jsonObject.getLong("actualCapacityGB");
                        used += jsonObject.getLong("actualFreeSizeGB");
                    }
                    for (int i = 0; i < hosts.size(); i++) {
                        JSONObject host = JSONObject.parseObject(hosts.get(i).toString(), JSONObject.class);
                        String uri = host.getString("uri");
                        //物理机详情
                        JSONObject cpuInfo = fusionOneDeviceService.getHostIfno(p,token,uri);
                        HardWareRespCreateReqDTO hardWareRespCreateReqDTO = new HardWareRespCreateReqDTO();
                        //网络信息
                        String urn = host.getString("urn");
                        JSONArray usageInfo = fusionOneDeviceService.getInternetspeed(p, token, siteId, urn);
                        String inBps = "0";
                        String outBps = "0";
                        String inPps = "0";
                        if (usageInfo!=null){
                            for (Object item : usageInfo) {
                                JSONObject obj = (JSONObject) item;
                                JSONArray values = obj.getJSONArray("value");
                                for (Object value : values) {
                                    JSONObject json = (JSONObject) value;
                                    if ("nic_byte_in".equals(json.getString("metricId"))) {
                                        inBps = json.getString("metricValue");
                                    }else {
                                        outBps  = json.getString("metricValue");
                                    }
                                }
                            }
                        }
                        //物理网卡信息
                        JSONArray systemIntFs =fusionOneDeviceService.getSystemIntFs(p, token, siteId,urn.split("hosts:")[1]);
                        //添加网络信息
                        for (Object systemIntF : systemIntFs) {
                            JSONObject jsonObject = (JSONObject) systemIntF;
                            HardWareNicRespDTO nicRespDTO = new HardWareNicRespDTO();
                            nicRespDTO.setUuid(jsonObject.getString("urn"));
                            nicRespDTO.setHardwareUuid(host.getString("uuid"));
                            nicRespDTO.setIpAddresses(jsonObject.getString("netAddr"));
                            nicRespDTO.setIpSubnet(jsonObject.getString("name"));
                            nicRespDTO.setL2NetworkUuid(jsonObject.getString("portUrn"));
                            nicRespDTO.setL2NetworkName(jsonObject.getString("portName"));
                            Integer flag = jsonObject.getInteger("flag");
                            if (flag==0){
                                nicRespDTO.setNetworkType("上行口");
                            }else {
                                nicRespDTO.setNetworkType("上行链路口");
                            }
                            nicRespDTO.setState(true);
                            nicRespDTO.setPlatformId(p.getId());
                            nicRespDTO.setPlatformName(p.getName());
                            nicList.add(nicRespDTO);
                        }

                        hardWareRespCreateReqDTO.setCpuOverPercent(new BigDecimal(1));
                        hardWareRespCreateReqDTO.setMemoryOverPercent(new BigDecimal(1));
                        //可用区
                        hardWareRespCreateReqDTO.setAvailableManager(siteName);
                        hardWareRespCreateReqDTO.setManager(p.getName());
                        //系统预留内存
                        hardWareRespCreateReqDTO.setReservedMemory(cpuInfo.getBigDecimal("memoryReserve").multiply(new BigDecimal(1024*1024)));
                        //查询主机配置信息
                        JSONArray realtimeData = fusionOneDeviceService.getRealtimeData(p, token, siteId, urn);
                        for (Object realtimeDatum : realtimeData) {
                            JSONObject jsonObject = (JSONObject) realtimeDatum;
                            if (jsonObject.getString("metricId").equals("product_mfg")){
                                hardWareRespCreateReqDTO.setBrandName(jsonObject.getString("metricValue"));
                            }
                            if (jsonObject.getString("metricId").equals("product_name")){
                                hardWareRespCreateReqDTO.setModel(jsonObject.getString("metricValue"));
                            }
                            if (jsonObject.getString("metricId").equals("product_serial")){
                                hardWareRespCreateReqDTO.setSerialNumber(jsonObject.getString("metricValue"));
                            }

                            if (jsonObject.getString("metricId").equals("cpu_info")){
                                String metricValue = jsonObject.getString("metricValue");
                                JSONArray jsonArray = JSONArray.parseArray(metricValue);
                                JSONObject firstObject = jsonArray.getJSONObject(0);
                                String version = firstObject.getString("version");
                                hardWareRespCreateReqDTO.setCpuType(version);
                            }
                        }

                        hardWareRespCreateReqDTO.setBandwidthDownstream(new BigDecimal(inBps).compareTo(BigDecimal.ZERO) < 0
                                ? new BigDecimal(inBps)
                                : new BigDecimal(inBps).multiply(new BigDecimal(1024)).setScale(0, BigDecimal.ROUND_DOWN));
                        hardWareRespCreateReqDTO.setBandwidthUpstream(new BigDecimal(outBps).compareTo(BigDecimal.ZERO) < 0
                                ? new BigDecimal(outBps)
                                : new BigDecimal(outBps).multiply(new BigDecimal(1024)).setScale(0, BigDecimal.ROUND_DOWN));
                        hardWareRespCreateReqDTO.setPacketRate(new BigDecimal(inPps));
                        //基本信息
                        hardWareRespCreateReqDTO.setName(host.getString("name"));
                        hardWareRespCreateReqDTO.setUuid(host.getString("uuid"));
                        hardWareRespCreateReqDTO.setIp(host.getString("ip"));
                        hardWareRespCreateReqDTO.setStatus(stateConvert(host.getString("status")));
                        hardWareRespCreateReqDTO.setState("Enabled");
                        //无集群信息，设置默认集群信息
                        hardWareRespCreateReqDTO.setClusterName(Optional.ofNullable(host.getString("clusterName")).orElse("default cluster"));
                        hardWareRespCreateReqDTO.setClusterUuid(Optional.ofNullable(host.getString("clusterUrn")).orElse("default cluster"));
                        //cpu信息
                        long totalSizeMCpu = cpuInfo.getLong("hostTotalCpuQuantity");
                        long availableCpu = cpuInfo.getLong("cpuQuantity");
                        BigDecimal totalcpu = BigDecimal.valueOf(totalSizeMCpu);
                        long use = totalSizeMCpu - availableCpu;
                        BigDecimal usecpu = BigDecimal.valueOf(use);
                        BigDecimal cpu = usecpu.divide(totalcpu, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
                        hardWareRespCreateReqDTO.setCpuNum(cpuInfo.getInteger("hostTotalCpuQuantity"));
                        hardWareRespCreateReqDTO.setTotalCpuCapacity(totalSizeMCpu);
                        //当前cpu超分比率
                        BigDecimal cpuCommitRate = new BigDecimal(totalSizeMCpu-availableCpu).divide(cpuInfo.getBigDecimal("hostTotalCpuQuantity"), 3, BigDecimal.ROUND_HALF_UP);
                        hardWareRespCreateReqDTO.setCpuCommitRate(cpuCommitRate);
                        hardWareRespCreateReqDTO.setAvailableCpuCapacity(availableCpu);
                        hardWareRespCreateReqDTO.setCpuSockets(cpuInfo.getInteger("cpuQuantity"));
                        hardWareRespCreateReqDTO.setCpuUsed(cpu);
                        String arch = cpuInfo.getString("arch") == null ? "" : cpuInfo.getString("arch");
                        if (arch.toLowerCase().contains("x86")) {
                            hardWareRespCreateReqDTO.setArchitecture("x86_64");
                        } else if (arch.toLowerCase().contains("arm")) {
                            hardWareRespCreateReqDTO.setArchitecture("arm64");
                        } else if (arch.isEmpty()) {
                            hardWareRespCreateReqDTO.setArchitecture("-");
                        } else {
                            hardWareRespCreateReqDTO.setArchitecture(arch);
                        }
                        //hardWareRespCreateReqDTO.setArchitecture(cpuInfo.getString("arch"));
                        //磁盘信息
                        hardWareRespCreateReqDTO.setDiskUsedBytes(BigDecimal.valueOf((num - used)*1024*1024*1024));
                        hardWareRespCreateReqDTO.setDiskFreeBytes(BigDecimal.valueOf(used*1024*1024*1024));
                        hardWareRespCreateReqDTO.setDiskUsed(new BigDecimal(num).subtract(new BigDecimal(used)).divide(new BigDecimal(num), 10,
                                        RoundingMode.HALF_UP).multiply(new BigDecimal(100))
                                .setScale(2, RoundingMode.HALF_UP));
                        hardWareRespCreateReqDTO.setTotalDiskCapacity(BigDecimal.valueOf(num*1024*1024*1024));

                        String computeResourceStatics = cpuInfo.getString("computeResourceStatics");
                        JSONObject memoryInfo = fusionOneDeviceService.getMemoryIfno(p,token,computeResourceStatics);
                        //内存信息
                        long allocatedSizeMB = memoryInfo.getLong("allocatedSizeMB");
                        long totalSizeMB = memoryInfo.getLong("totalSizeMB");
                        long realtimeUsedSizeMB = memoryInfo.getLong("realtimeUsedSizeMB");
                        BigDecimal allocated = BigDecimal.valueOf(allocatedSizeMB);
                        BigDecimal total = BigDecimal.valueOf(totalSizeMB);
                        long totalMemoryCapacity = totalSizeMB * 1024 * 1024;
                        long availableMemoryCapacity = realtimeUsedSizeMB * 1024 * 1024;
                        BigDecimal percentage = allocated.divide(total, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
                        hardWareRespCreateReqDTO.setTotalMemoryCapacity(totalMemoryCapacity);
                        hardWareRespCreateReqDTO.setAvailableMemoryCapacity(availableMemoryCapacity);
                        hardWareRespCreateReqDTO.setMemoryUsed(percentage);
                        //当前内存超售比率
                        BigDecimal memCommitRate = new BigDecimal(totalMemoryCapacity-availableMemoryCapacity).divide(new BigDecimal(totalMemoryCapacity), 3, BigDecimal.ROUND_HALF_UP);
                        hardWareRespCreateReqDTO.setMemoryCommitRate(memCommitRate);
                        //平台信息
                        hardWareRespCreateReqDTO.setPlatformId(p.getId());
                        hardWareRespCreateReqDTO.setPlatformName(p.getName());
                        hardWareRespCreateReqDTO.setTenantId(p.getTenantId());
                        hardWareRespCreateReqDTO.setRegionId(p.getRegionId());
                        hardWareRespCreateReqDTO.setTypeName(p.getTypeCode());
                        hardWareRespCreateReqDTO.setDeleted(0);
                        hardWareRespCreateReqDTOS.add(hardWareRespCreateReqDTO);

                    }
                }
                if (!nicList.isEmpty()){
                    List<HardWareNicRespDTO> oldNicList=hardWareNicApi.getHardwareNicByPlatformId(p.getId()).getCheckedData();

                    if (oldNicList.isEmpty()) {
                        hardWareNicApi.adds(nicList);
                    } else {
                        // 修改Map的key为uuid+hardwareUuid的组合
                        Map<String, HardWareNicRespDTO> existingHardwareMap = oldNicList.stream()
                                .collect(Collectors.toMap(
                                        hardwareNic -> hardwareNic.getUuid() + "_" + hardwareNic.getHardwareUuid(),
                                        hardwareNic -> hardwareNic
                                ));

                        List<HardWareNicRespDTO> newEntries = new ArrayList<>();
                        List<HardWareNicRespDTO> updatedEntries = new ArrayList<>();
                        // 修改删除条件，同时比对uuid和hardwareUuid
                        List<HardWareNicRespDTO> deleteEntries = oldNicList.stream()
                                .filter(item -> !nicList.stream()
                                        .anyMatch(newItem ->
                                                newItem.getUuid().equals(item.getUuid()) &&
                                                        newItem.getHardwareUuid().equals(item.getHardwareUuid())
                                        ))
                                .collect(Collectors.toList());

                        for (HardWareNicRespDTO hardWareNicRespDTO : nicList) {
                            // 使用组合key来查找
                            String compositeKey = hardWareNicRespDTO.getUuid() + "_" + hardWareNicRespDTO.getHardwareUuid();
                            HardWareNicRespDTO nicRespDTO = existingHardwareMap.get(compositeKey);
                            if (nicRespDTO == null) {
                                newEntries.add(hardWareNicRespDTO);
                            } else if (!nicRespDTO.equals(hardWareNicRespDTO)) {
                                updatedEntries.add(hardWareNicRespDTO);
                            }
                        }

                        hardWareNicApi.updates(updatedEntries);
                        hardWareNicApi.adds(newEntries);
                        if (!deleteEntries.isEmpty()) {
                            hardWareNicApi.deletes(deleteEntries);
                        }
                    }
                }
            }
        }


        if (hardWareRespCreateReqDTOS.size()>0){
            List<AlarmDorisReqDTO> alarmDorisReqDTO = new ArrayList<>();
            // 获取当前节点的index 与 总节点数
            int shardIndex = XxlJobHelper.getShardIndex();
            int shardTotal = XxlJobHelper.getShardTotal();
            log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
            int hardWare = hardWareInfoApi.count("FusionOne");
            List<HardWareRespCreateReqDTO> dtos = hardWareInfoApi.getAll("FusionOne").getData();
            List<HardWareRespCreateReqDTO> shardingData = new ArrayList<>();

            if (shardIndex<0){
                shardingData = hardWareRespCreateReqDTOS;
            }else {
                shardingData =  StringUtil.getShardingData(hardWareRespCreateReqDTOS, shardTotal, shardIndex);
            }

            // 对分片数据进行业务处理
            for (HardWareRespCreateReqDTO item : shardingData) {
                // 模拟业务逻辑处理
                log.info("Processing item: " + item.getName());
            }
            if (hardWare == 0) {
                hardWareInfoApi.adds(shardingData);
            } else {
                List<HardWareRespCreateReqDTO> collect3 = dtos.stream()
                        .filter(item -> !hardWareRespCreateReqDTOS
                                .stream()
                                .map(HardWareRespCreateReqDTO::getUuid)
                                .collect(Collectors.toList())
                                .contains(item.getUuid()))
                        .collect(Collectors.toList());
                final Long alertId = alarmConfigApi.getMaxAlertId().getData();
                Map<String, AlarmHostRelationInfo> finalAlarmConfigRelationMap = alarmConfigRelationMap;
                Map<Long, AlarmConfigInfo> finalAlarmConfigMap = alarmConfigMap;
                shardingData.forEach(item -> {
                    AlarmHostRelationInfo relationInfo = finalAlarmConfigRelationMap.get(item.getUuid());
                    if (ObjectUtil.isNotEmpty(relationInfo)) {
                        AlarmConfigInfo alarmConfigInfo = finalAlarmConfigMap.get(relationInfo.getAlarmId());
                        int i =1;
                        for (HardWareRespCreateReqDTO dto:dtos) {
                            if(dto.getUuid().equals(item.getUuid())){
                                item.setId(dto.getId());
                            }
                            //启用状态变更后发送告警
                            if (ObjectUtil.isNotEmpty(alarmConfigInfo)&&alarmConfigInfo.getDictLabelValue().equals("state_change")&&dto.getUuid().equals(item.getUuid()) && !dto.getStatus().equals(item.getStatus())) {
                                int alarmLevel = alarmConfigInfo.getAlarmLevel()==1?2:(alarmConfigInfo.getAlarmLevel()==2?1:0);
                                AlarmDorisReqDTO alarm = new AlarmDorisReqDTO();
                                JSONObject tags = new JSONObject();
                                alarm.setId(alertId + i);
                                tags.put("app", "hardware");
                                tags.put("monitorId", item.getUuid());
                                tags.put("monitorName", item.getName());
                                alarm.setPlatformId(item.getPlatformId());
                                alarm.setResourceType(0);
                                alarm.setStatus(0);
                                alarm.setIsSolved(0);
                                alarm.setGmtCreate(new Date());
                                alarm.setFirstAlarmTime(DateUtil.current());
                                alarm.setGmtUpdate(DateUtil.date());
                                alarm.setLastAlarmTime(DateUtil.current());
                                alarm.setPlatformName(item.getPlatformName());
                                alarm.setTimes(1);
                                alarm.setMonitorId(item.getUuid());
                                alarm.setContent("宿主机:" + item.getName() + " 的启用状态由\"" + StateConverter.stateToCh(dto.getState()) + "\"转换为\"" + StateConverter.stateToCh(item.getState()) + "\"");
                                alarm.setTarget("hardware.state.changed");
                                alarm.setApp("hardware");
                                alarm.setMonitorName(item.getName());
                                alarm.setPriority(alarmLevel);
                                alarm.setAlarmId(0L);
                                alarmDorisReqDTO.add(alarm);
                            }
                        }
                        i++;
                    }
                });
                if (alarmDorisReqDTO.size() > 0) {
                    Map<String,List> addMap = new HashMap<>();
                    addMap.put("updateList",new ArrayList<>());
                    addMap.put("insertList",alarmDorisReqDTO);
                    alarmConfigApi.createAlarmToDoris(addMap);
                }
                // 软删除宿主机历史数据
                if (collect3.size()>0){
                    hardWareInfoApi.deleteHardWare(collect3);
                }
                hardWareInfoApi.updates(shardingData);
                List<HardWareRespCreateReqDTO> collect = hardWareRespCreateReqDTOS.stream().filter(item -> !dtos.stream().map(HardWareRespCreateReqDTO::getUuid).collect(Collectors.toList()).contains(item.getUuid())).collect(Collectors.toList());
                if (collect.size() > 0) {
                    collect = collect.stream()
                            .distinct()
                            .collect(Collectors.toList());
                    hardWareInfoApi.adds(collect);
                }
            }
            hardWareInfoApi.removeDuplicateData();
        }
    }

    @XxlJob("fusionOneHostToInflux")
    public void fusionOneHostToInflux() {
        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        if (platformconfigDTOList.isEmpty()) {
            return; // 提前返回，避免不必要的嵌套
        }

        for (PlatformconfigDTO p : platformconfigDTOList) {
            if (isFusionOnePlatform(p)) {
                String token = getFusionOneToken(p);
                if (token == null) {
                    log.info("获取FusionOne token失败");
                    continue; // 继续处理下一个平台
                }

                String siteId = getSiteId(p);
                JSONArray hardwares = fusionOneDeviceService.getHosts(p, token, siteId);
                if (ObjectUtil.isNull(hardwares)) {
                    continue; // 当前硬件为空，跳过
                }

                processHardwares(hardwares, p, token, siteId);
            }
        }
    }


    private boolean isFusionOnePlatform(PlatformconfigDTO p) {
        return "fusionOne".equals(p.getTypeCode()) && fusionOneAccessTokenRedisDAO.get("FusionOne:" + p.getId()) != null;
    }

    private String getFusionOneToken(PlatformconfigDTO p) {
        JSONObject tokenData = fusionOneAccessTokenRedisDAO.get("FusionOne:" + p.getId());
        return tokenData != null ? tokenData.getString("token") : null;
    }

    private String getSiteId(PlatformconfigDTO p) {
        JSONObject tokenData = fusionOneAccessTokenRedisDAO.get("FusionOne:" + p.getId());
        return tokenData != null ? tokenData.getString("siteId") : null;
    }

    private void processHardwares(JSONArray hardwares, PlatformconfigDTO p, String token, String siteId) {
        BatchPoints batchPoints = BatchPoints.builder().build();
        for (int j = 0; j < hardwares.size(); j++) {
            //内存信息
            JSONObject jsonObject = hardwares.getJSONObject(j);
            String computeResourceStatics = jsonObject.getString("computeResourceStatics");
            JSONObject memoryInfo = fusionOneDeviceService.getMemoryIfno(p,token,computeResourceStatics);
            long totalSizeMB = memoryInfo.getLong("totalSizeMB");
            JSONArray storages = fusionOneDeviceService.getStorages(p, token, siteId);
            if (ObjectUtil.isNull(storages)) continue;
            Long num = 0L;
            for (Object storage : storages) {
                JSONObject storage1 = (JSONObject) storage;
                num  += storage1.getLong("actualCapacityGB");
            }
            String urn = jsonObject.getString("urn");
            JSONArray usageInfo = fusionOneDeviceService.getCurvedata(p, token, siteId, urn);
            String uuid = jsonObject.getString("uuid");
            String name = jsonObject.getString("hostRealName");
            if (ObjectUtil.isNull(usageInfo)) {
                continue; // 当前使用信息为空，跳过
            }
            for (Object item : usageInfo) {
                JSONObject obj = (JSONObject) item;
                String metricId = obj.getString("metricId");
                //cup使用率
                if (metricId.equals("cpu_usage")) {
                    JSONArray metricValues = obj.getJSONArray("metricValue");
                    for (Object metricValue : metricValues) {
                        JSONObject metric = JSONObject.parseObject(metricValue.toString());
                        Point point = Point.measurement("zj_cloud_hardware")
                                .tag("label","cpu")
                                .tag("uuid", uuid)
                                .tag("metricName", "CPUAverageUsedUtilization")
                                .addField("productsName", name)
                                .tag("regionId", StringUtil.toString(p.getRegionId()))
                                .addField("regionName", StringUtil.toString(p.getRegionName()))
                                .tag("host_type", "CPUAverageUsedUtilization")
                                .tag("platformId", StringUtil.toString(p.getId()))
                                .addField("platformName", StringUtil.toString(p.getName()))
                                .addField("host_metricName", "CPUAverageUsedUtilization")
                                .addField("type", "CPUAverageUsedUtilization")
                                .addField("value", Convert.toBigDecimal(metric.get("value")))
                                .time(StringUtil.toLong(metric.get("time")), TimeUnit.SECONDS).build();
                        batchPoints.point(point);
                    }
                }
                //内存使用
                if (metricId.equals("mem_usage")){
                    JSONArray metricValues = obj.getJSONArray("metricValue");
                    for (Object metricValue : metricValues) {
                        JSONObject metric = JSONObject.parseObject(metricValue.toString());
                        BigDecimal value =
                                Convert.toBigDecimal(metric.get("value")).multiply(new BigDecimal(totalSizeMB)).divide(new BigDecimal(100)).multiply(new BigDecimal(1024 * 1024));
                        Point usedPoint = Point.measurement("zj_cloud_hardware")
                                .tag("uuid", uuid)
                                .tag("metricName", "MemoryUsedBytes")
                                .tag("label", "mem")
                                .tag("regionId", StringUtil.toString(p.getRegionId()))
                                .addField("regionName", StringUtil.toString(p.getRegionName()))
                                .tag("host_type", "all")
                                .tag("platformId", StringUtil.toString(p.getId()))
                                .addField("productsName", name)
                                .addField("platformName", StringUtil.toString(p.getName()))
                                .addField("host_metricName", "MemoryUsedBytes")
                                .addField("type", "all")
                                .addField("value", value)
                                .time(StringUtil.toLong(metric.get("time")), TimeUnit.SECONDS).build();
                        batchPoints.point(usedPoint);
                        Point pointFree = Point.measurement("zj_cloud_hardware")
                                .tag("uuid", uuid)
                                .tag("label","mem")
                                .tag("metricName", "MemoryFreeBytes")
                                .tag("regionId", StringUtil.toString(p.getRegionId()))
                                .addField("regionName", StringUtil.toString(p.getRegionName()))
                                .tag("host_type", "all")
                                .tag("platformId", StringUtil.toString(p.getId()))
                                .addField("productsName", name)
                                .addField("platformName", StringUtil.toString(p.getName()))
                                .addField("host_metricName", "MemoryFreeBytes")
                                .addField("type", "all")
                                .addField("value", BigDecimal.valueOf(100).subtract(Convert.toBigDecimal(metric.get("value"))).multiply(new BigDecimal(totalSizeMB)).divide(new BigDecimal(100)).multiply(new BigDecimal(1024 * 1024)))
                                .time(StringUtil.toLong(metric.get("time")), TimeUnit.SECONDS).build();
                        batchPoints.point(pointFree);
                        Point percentPoints = Point.measurement("zj_cloud_hardware")
                                .tag("uuid", uuid)
                                .tag("metricName", "MemoryUsedInPercent")
                                .tag("label", "mem")
                                .tag("regionId", StringUtil.toString(p.getRegionId()))
                                .addField("regionName", StringUtil.toString(p.getRegionName()))
                                .tag("host_type", "all")
                                .tag("platformId", StringUtil.toString(p.getId()))
                                .addField("productsName", StringUtil.toString(p.getName()))
                                .addField("platformName", StringUtil.toString(p.getName()))
                                .addField("host_metricName", "MemoryUsedInPercent")
                                .addField("type", "all")
                                .addField("value", Convert.toBigDecimal(metric.get("value")))
                                .time(StringUtil.toLong(metric.get("time")), TimeUnit.SECONDS).build();
                        batchPoints.point(percentPoints);
                    }
                }
                //磁盘使用
                if (metricId.equals("logic_disk_usage")){
                    JSONArray metricValues = obj.getJSONArray("metricValue");
                    for (Object metricValue : metricValues) {
                        JSONObject metric = JSONObject.parseObject(metricValue.toString());
                        BigDecimal value =
                                Convert.toBigDecimal(metric.get("value")).multiply(new BigDecimal(num)).divide(new BigDecimal(100)).multiply(new BigDecimal(1024 * 1024*1024));
                        Point usedPoint = Point.measurement("zj_cloud_hardware")
                                .tag("uuid", uuid)
                                .tag("metricName", "DiskAllUsedCapacityInPercent")
                                .tag("label", "disk")
                                .tag("regionId", StringUtil.toString(p.getRegionId()))
                                .addField("regionName", StringUtil.toString(p.getRegionName()))
                                .tag("host_type", "all")
                                .tag("platformId", StringUtil.toString(p.getId()))
                                .addField("productsName", name)
                                .addField("platformName", StringUtil.toString(p.getName()))
                                .addField("host_metricName", "DiskAllUsedCapacityInPercent")
                                .addField("type", "all")
                                .addField("value", Convert.toBigDecimal(metric.get("value")))
                                .time(StringUtil.toLong(metric.get("time")), TimeUnit.SECONDS).build();
                        batchPoints.point(usedPoint);
                        Point pointFree = Point.measurement("zj_cloud_hardware")
                                .tag("uuid", uuid)
                                .tag("label","disk")
                                .tag("metricName", "DiskAllUsedCapacityInBytes")
                                .tag("regionId", StringUtil.toString(p.getRegionId()))
                                .addField("regionName", StringUtil.toString(p.getRegionName()))
                                .tag("host_type", "all")
                                .tag("platformId", StringUtil.toString(p.getId()))
                                .addField("productsName", name)
                                .addField("platformName", StringUtil.toString(p.getName()))
                                .addField("host_metricName", "DiskAllUsedCapacityInBytes")
                                .addField("type", "all")
                                .addField("value", value)
                                .time(StringUtil.toLong(metric.get("time")), TimeUnit.SECONDS).build();
                        batchPoints.point(pointFree);
                    }
                }
                //磁盘I/O写入
                if (metricId.equals("disk_io_in")) {
                    JSONArray metricValues = obj.getJSONArray("metricValue");
                    for (Object metricValue : metricValues) {
                        JSONObject metric = JSONObject.parseObject(metricValue.toString());
                        Point point = Point.measurement("zj_cloud_hardware")
                                .tag("uuid", uuid)
                                .tag("label", "disk")
                                .tag("metricName", "DiskWriteBytes")
                                .addField("productsName", name)
                                .tag("regionId", StringUtil.toString(p.getRegionId()))
                                .addField("regionName", StringUtil.toString(p.getRegionName()))
                                .tag("host_type", "all")
                                .tag("platformId", StringUtil.toString(p.getId()))
                                .addField("platformName", StringUtil.toString(p.getName()))
                                .addField("host_metricName", "DiskWriteBytes")
                                .addField("type", "all")
                                .addField("value",
                                        Convert.toBigDecimal(metric.get("value")).multiply(new BigDecimal(1024)))
                                .time(StringUtil.toLong(metric.get("time")), TimeUnit.SECONDS).build();
                        batchPoints.point(point);
                    }
                }
                //磁盘I/O读出
                if (metricId.equals("disk_io_out")) {
                    JSONArray metricValues = obj.getJSONArray("metricValue");
                    for (Object metricValue : metricValues) {
                        JSONObject metric = JSONObject.parseObject(metricValue.toString());
                        Point point = Point.measurement("zj_cloud_hardware")
                                .tag("uuid", uuid)
                                .tag("label", "disk")
                                .tag("metricName", "DiskReadBytes")
                                .addField("productsName", name)
                                .tag("regionId", StringUtil.toString(p.getRegionId()))
                                .addField("regionName", StringUtil.toString(p.getRegionName()))
                                .tag("host_type", "all")
                                .tag("platformId", StringUtil.toString(p.getId()))
                                .addField("platformName", StringUtil.toString(p.getName()))
                                .addField("host_metricName", "DiskReadBytes")
                                .addField("type", "all")
                                .addField("value", Convert.toBigDecimal(metric.get("value")).multiply(new BigDecimal(1024)))
                                .time(StringUtil.toLong(metric.get("time")), TimeUnit.SECONDS).build();
                        batchPoints.point(point);
                    }
                }
                //网络流入流速
                if (metricId.equals("nic_byte_in")) {
                    JSONArray metricValues = obj.getJSONArray("metricValue");
                    for (Object metricValue : metricValues) {
                        JSONObject metric = JSONObject.parseObject(metricValue.toString());
                        Point point = Point.measurement("zj_cloud_hardware")
                                .tag("uuid", uuid)
                                .tag("label", "net")
                                .tag("metricName", "NetworkInBytes")
                                .addField("productsName", name)
                                .tag("regionId", StringUtil.toString(p.getRegionId()))
                                .addField("regionName", StringUtil.toString(p.getRegionName()))
                                .tag("host_type", "all")
                                .tag("platformId", StringUtil.toString(p.getId()))
                                .addField("platformName", StringUtil.toString(p.getName()))
                                .addField("host_metricName", "NetworkInBytes")
                                .addField("type", "all")
                                .addField("value", Convert.toBigDecimal(metric.get("value")).multiply(new BigDecimal(1024 )))
                                .time(StringUtil.toLong(metric.get("time")), TimeUnit.SECONDS).build();
                        batchPoints.point(point);
                    }
                }
                //网络流出流速
                if (metricId.equals("nic_byte_out")) {
                    JSONArray metricValues = obj.getJSONArray("metricValue");
                    for (Object metricValue : metricValues) {
                        JSONObject metric = JSONObject.parseObject(metricValue.toString());
                        Point point = Point.measurement("zj_cloud_hardware")
                                .tag("uuid", uuid)
                                .tag("label", "net")
                                .tag("metricName", "NetworkOutBytes")
                                .addField("productsName", name)
                                .tag("regionId", StringUtil.toString(p.getRegionId()))
                                .addField("regionName", StringUtil.toString(p.getRegionName()))
                                .tag("host_type", "all")
                                .tag("platformId", StringUtil.toString(p.getId()))
                                .addField("platformName", StringUtil.toString(p.getName()))
                                .addField("host_metricName", "NetworkOutBytes")
                                .addField("type", "all")
                                .addField("value", Convert.toBigDecimal(metric.get("value")).multiply(new BigDecimal(1024 )))
                                .time(StringUtil.toLong(metric.get("time")), TimeUnit.SECONDS).build();
                        batchPoints.point(point);
                    }
                }
            }
            writeBatchPoints(batchPoints); // 写入数据
        }
    }

//    private void processHardwares(JSONArray hardwares, PlatformconfigDTO p, String token, String siteId) {
//        BatchPoints batchPoints = BatchPoints.builder().build();
//        for (Object obj : hardwares) {
//            JSONObject hardware = (JSONObject) obj;
//            String computeResourceStatics = hardware.getString("computeResourceStatics");
//            JSONObject memoryInfo = fusionOneDeviceService.getMemoryIfno(p, token, computeResourceStatics);
//            long totalSizeMB = memoryInfo.getLong("totalSizeMB");
//            JSONArray storages = fusionOneDeviceService.getStorages(p, token, siteId);
//
//            if (ObjectUtil.isNull(storages)) continue;
//
//            long totalStorageCapacity = calculateTotalStorageCapacity(storages);
//            String uuid = hardware.getString("uuid");
//            String name = hardware.getString("hostRealName");
//            JSONArray usageInfo = fusionOneDeviceService.getCurvedata(p, token, siteId, hardware.getString("urn"));
//
//            if (ObjectUtil.isNull(usageInfo)) continue;
//
//            for (Object usageObj : usageInfo) {
//                JSONObject usage = (JSONObject) usageObj;
//                processUsage(batchPoints, uuid, name, p, usage, totalSizeMB, totalStorageCapacity);
//            }
//        }
//        writeBatchPoints(batchPoints); // 写入数据
//    }
//
//    private long calculateTotalStorageCapacity(JSONArray storages) {
//        long totalCapacity = 0;
//        for (Object storage : storages) {
//            totalCapacity += ((JSONObject) storage).getLong("actualCapacityGB");
//        }
//        return totalCapacity;
//    }
//
//    private void processUsage(BatchPoints batchPoints, String uuid, String name, PlatformconfigDTO p, JSONObject usage, long totalSizeMB, long totalStorageCapacity) {
//        String metricId = usage.getString("metricId");
//        JSONArray metricValues = usage.getJSONArray("metricValue");
//
//        for (Object metricValue : metricValues) {
//            JSONObject metric = JSONObject.parseObject(metricValue.toString());
//            BigDecimal value = Convert.toBigDecimal(metric.get("value"));
//            long time = StringUtil.toLong(metric.get("time"));
//
//            switch (metricId) {
//                case "cpu_usage":
//                    createAndAddPoint(batchPoints, uuid, name, p, "CPUAverageUsedUtilization", "cpu", value, time);
//                    break;
//                case "mem_usage":
//                    addMemoryPoints(batchPoints, uuid, name, p, metric, value, totalSizeMB, time);
//                    break;
//                case "logic_disk_usage":
//                    addDiskPoints(batchPoints, uuid, name, p, metric, value, totalStorageCapacity, time);
//                    break;
//                case "disk_io_in":
//                    BigDecimal inmultiply = value.multiply(new BigDecimal(1024*1024));
//                    createAndAddPoint(batchPoints, uuid, name, p, "DiskWriteBytes", "disk", inmultiply, time);
//                    break;
//                case "disk_io_out":
//                    BigDecimal outmultiply = value.multiply(new BigDecimal(1024*1024));
//                    createAndAddPoint(batchPoints, uuid, name, p, "DiskReadBytes", "disk", outmultiply, time);
//                    break;
//                case "nic_byte_in":
//                    BigDecimal nicin = value.multiply(new BigDecimal(1024*1024));
//                    createAndAddPoint(batchPoints, uuid, name, p, "NetworkInBytes", "net", nicin, time);
//                    break;
//                case "nic_byte_out":
//                    BigDecimal nicout = value.multiply(new BigDecimal(1024*1024));
//                    createAndAddPoint(batchPoints, uuid, name, p, "NetworkOutBytes", "net", nicout, time);
//                    break;
//            }
//        }
//    }
//
//    private void addMemoryPoints(BatchPoints batchPoints, String uuid, String name, PlatformconfigDTO p, JSONObject metric, BigDecimal usagePercent, long totalSizeMB, long time) {
//        BigDecimal usedBytes = usagePercent.multiply(BigDecimal.valueOf(totalSizeMB)).divide(BigDecimal.valueOf(100)).multiply(BigDecimal.valueOf(1024 * 1024));
//        createAndAddPoint(batchPoints, uuid, name, p, "MemoryUsedBytes", "mem", usedBytes, time);
//        createAndAddPoint(batchPoints, uuid, name, p, "MemoryFreeBytes", "mem", BigDecimal.valueOf(100).subtract(usagePercent), time);
//        createAndAddPoint(batchPoints, uuid, name, p, "MemoryUsedInPercent", "mem", usagePercent, time);
//    }
//
//    private void addDiskPoints(BatchPoints batchPoints, String uuid, String name, PlatformconfigDTO p, JSONObject metric, BigDecimal usagePercent, long totalStorageCapacity, long time) {
//        BigDecimal usedBytes = usagePercent.multiply(BigDecimal.valueOf(totalStorageCapacity)).divide(BigDecimal.valueOf(100)).multiply(BigDecimal.valueOf(1024 * 1024));
//        createAndAddPoint(batchPoints, uuid, name, p, "DiskAllUsedCapacityInPercent", "disk", usagePercent, time);
//        createAndAddPoint(batchPoints, uuid, name, p, "DiskAllUsedCapacityInBytes", "disk", usedBytes, time);
//    }
//
//    private void createAndAddPoint(BatchPoints batchPoints, String uuid, String name, PlatformconfigDTO p, String metricName, String label, BigDecimal value, long time) {
//        Point point = Point.measurement("zj_cloud_hardware")
//                .tag("uuid", uuid)
//                .tag("label", label)
//                .tag("metricName", metricName)
//                .addField("productsName", name)
//                .tag("regionId", StringUtil.toString(p.getRegionId()))
//                .addField("regionName", StringUtil.toString(p.getRegionName()))
//                .tag("host_type", metricName)
//                .tag("platformId", StringUtil.toString(p.getId()))
//                .addField("platformName", StringUtil.toString(p.getName()))
//                .addField("host_metricName", metricName)
//                .addField("type", metricName)
//                .addField("value", value)
//                .time(time, TimeUnit.SECONDS)
//                .build();
//        batchPoints.point(point);
//    }

    private void writeBatchPoints(BatchPoints batchPoints) {
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);

        List<Point> shardedPoints = StringUtil.getShardingData(batchPoints.getPoints(), shardTotal, shardIndex);
        influxDBTemplate.writeBatch(BatchPoints.builder().points(shardedPoints).build());
    }

    private String stateConvert(String status) {
        String target = "";
        switch (status) {
            case "normal":
                target = "Connected";
                break;
            default:
                target = "Disconnected";
        }
        return target;
    }
}
