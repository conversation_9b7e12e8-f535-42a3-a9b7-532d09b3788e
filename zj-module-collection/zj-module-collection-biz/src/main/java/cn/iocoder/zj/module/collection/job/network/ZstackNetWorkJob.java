package cn.iocoder.zj.module.collection.job.network;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.module.collection.dal.dataobject.zstack.ZstackLoginInfo;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.zstack.ZstackAccessTokenRedisDAO;
import cn.iocoder.zj.module.collection.framework.influx.config.InfluxDBTemplate;
import cn.iocoder.zj.module.collection.service.network.ZstackNetWorkService;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.monitor.api.network.NetworkApi;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL2DTO;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL3DTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.zstack.sdk.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @ClassName : ZstackNetWorkJob  //类名
 * @Description : 网络相关接口  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/8/3  15:59
 */

@Component
@Slf4j
public class ZstackNetWorkJob {

    @Resource
    InfluxDBTemplate influxDBTemplate;
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    ZstackAccessTokenRedisDAO zstackAccessTokenRedisDAO;
    @Resource
    PlatformRedisDAO platformRedisDAO;
    @Resource
    ZstackNetWorkService zstackNetWorkService;

    @Resource
    NetworkApi networkApi;

    public void zstackNetWorkJob() {
        execute();
        networkL3();
    }

    @XxlJob("networkL2")
    public void execute() {
        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                .filter(dto -> "zstack".equals(dto.getTypeCode()))
                .collect(Collectors.toList());
        if (filteredList.isEmpty()) return;
        try {
            for (PlatformconfigDTO platformconfigDTO : filteredList) {
                handlePlatformConfig(platformconfigDTO);
            }
        } catch (Exception e) {
            XxlJobHelper.handleFail("zstack中NetworkL2异常" + e.getMessage());
        }
        XxlJobHelper.handleSuccess();
    }

    private void handlePlatformConfig(PlatformconfigDTO p) {
        List<NetWorkL2DTO> netWorkL2DTOS = new ArrayList<>();
        if (zstackAccessTokenRedisDAO.get("zstack:" + p.getId())!=null){
            String token = zstackAccessTokenRedisDAO.get("zstack:" + p.getId()).getUuid();
            if (StrUtil.isEmpty(token)) {
                XxlJobHelper.handleFail("zstack中token已过期");
            }

            String d = zstackNetWorkService.l2Networks(p.getUrl(), token);
            JSONArray jsonArray = JSONObject.parseObject(d).getJSONArray("inventories");
            for (int i = 0; i < jsonArray.size(); i++) {
                NetWorkL2DTO netWorkL2DTO = new NetWorkL2DTO();
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String uuid = jsonObject.getString("uuid");
                String vlan = jsonObject.getString("vlan");
                String physicalInterface = jsonObject.getString("physicalInterface");
                String name = jsonObject.getString("name");
                String createDate = jsonObject.getString("createDate");
                Integer virtualNetworkId = jsonObject.getInteger("virtualNetworkId");
                String type = jsonObject.getString("type");
                Date date = DateUtil.date(new Date(jsonObject.getString("createDate")));
                Long platformId = p.getId();
                String platformName = p.getName();
                Long tenantId = p.getTenantId();
                netWorkL2DTO.setName(name);
                netWorkL2DTO.setUuid(uuid);
                netWorkL2DTO.setPhysicalInterface(physicalInterface);
                netWorkL2DTO.setType(type);
                netWorkL2DTO.setVlan(vlan);
                netWorkL2DTO.setVirtualNetworkId(virtualNetworkId);
                netWorkL2DTO.setPlatformId(p.getId());
                netWorkL2DTO.setPlatformName(p.getName());
                netWorkL2DTO.setRegionId(p.getRegionId());
                netWorkL2DTO.setRegionName(p.getRegionName());
                netWorkL2DTO.setTenantId(tenantId);
                netWorkL2DTO.setTypeName("zstack");
                netWorkL2DTO.setCreateTime(date);
                netWorkL2DTOS.add(netWorkL2DTO);
            }
        }
        if (!netWorkL2DTOS.isEmpty()) {
            List<NetWorkL2DTO> netWorks = networkApi.getNetworkL2ByPlatformId(p.getId()).getData();
            if (netWorks.isEmpty()) {
                networkApi.addNetWorkL2(netWorkL2DTOS);
            } else {
                Map<String, NetWorkL2DTO> existingHardwareMap = netWorks.stream()
                        .collect(Collectors.toMap(NetWorkL2DTO::getUuid, netWorkL2DTO -> netWorkL2DTO));

                List<NetWorkL2DTO> newEntries = new ArrayList<>();
                List<NetWorkL2DTO> updatedEntries = new ArrayList<>();
                List<NetWorkL2DTO> deleteEntries = netWorks.stream()
                        .filter(item -> !netWorkL2DTOS.stream()
                                .map(NetWorkL2DTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid())).collect(Collectors.toList());

                for (NetWorkL2DTO newNet : netWorkL2DTOS) {
                    NetWorkL2DTO netWorkL2DTO = existingHardwareMap.get(newNet.getUuid());
                    if (netWorkL2DTO == null) {
                        newEntries.add(newNet);
                    } else if (!netWorkL2DTO.equals(newNet)) {
                        updatedEntries.add(newNet);
                    }
                }
                networkApi.updateNetWorkL2(updatedEntries);
                networkApi.addNetWorkL2(newEntries);
                if (!deleteEntries.isEmpty()) {
                    networkApi.deleteNetWorkL2ByNameList(deleteEntries);
                }

            }
        }
    }


    @XxlJob("networkL3")
    public void networkL3() {

        List<NetWorkL3DTO> netWorkL3DTOS = new ArrayList<>();
        List<PlatformconfigDTO> platformconfigDTOList = new ArrayList<>();
        if (platformRedisDAO.get("platform") == null) {
            platformconfigDTOList = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform", platformconfigDTOList);
        }
        platformconfigDTOList = platformRedisDAO.get("platform");

        // 查询二级网络名称列表
        List<Map> list = networkApi.getNetWorkL2ByNameList();
        if (platformconfigDTOList.size() > 0) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                if (zstackAccessTokenRedisDAO.get("zstack:" + p.getId()) != null) {
                    String token = zstackAccessTokenRedisDAO.get("zstack:" + p.getId()).getUuid();
//                    JSONArray jsonArray = zstackNetWorkService.l3Networks(p.getUrl(), token);
                    // 初始化 ZStack SDK 配置
                    try {
                        String processedUrl = removeProtocolAndPort(p.getUrl());
                        String port = extractPort(p.getUrl());
                        ZSClient.configure(
                                new ZSConfig.Builder()
                                        .setHostname(processedUrl)
                                        .setPort(Convert.toInt(port))
                                        .setContextPath("zstack")
                                        .build()
                        );
                    } catch (Exception e) {
                        log.error("ZStack SDK 调用失败: {}", e.getMessage(), e);
                        return;
                    }
                    //三级网络列表
                    QueryL3NetworkAction l3action = new QueryL3NetworkAction();
                    l3action.sessionId = token;
                    QueryL3NetworkAction.Result l3res = l3action.call();
                    List<L3NetworkInventory> l3Inventories = l3res.value.inventories;
                    for (L3NetworkInventory l3Inventory : l3Inventories) {
                        NetWorkL3DTO netWorkL3DTO = new NetWorkL3DTO();
                        netWorkL3DTO.setUuid(l3Inventory.getUuid());
                        netWorkL3DTO.setL2NetworkUuid(l3Inventory.getL2NetworkUuid());
                        String l2NetworkName="";
                        // 二级网络名称
                        for (Map m : list) {
                            if (m.get("uuid").toString().equals(l3Inventory.getL2NetworkUuid())) {
                                l2NetworkName = StringUtil.toString(m.get("name"));
                            }
                        }
                        netWorkL3DTO.setL2NetworkName(l2NetworkName);
                        String dns="";
                        List<String> jsonArrayDns = l3Inventory.getDns();
                        if (jsonArrayDns != null && jsonArrayDns.size() > 0) {
                            StringBuilder sb = new StringBuilder();
                            for (int j = 0; j < jsonArrayDns.size(); j++) {
                                if (j > 0) {
                                    sb.append(",");
                                }
                                sb.append(jsonArrayDns.get(j));
                            }
                            dns = sb.toString();
                        }
                        netWorkL3DTO.setDns(dns);
                        netWorkL3DTO.setType(l3Inventory.getType());
                        List<NetworkServiceL3NetworkRefInventory> services = l3Inventory.getNetworkServices();
                        String networkServices="";
                        if (services.size() > 0) {
                            StringBuilder sb = new StringBuilder();
                            for (int j = 0; j < services.size(); j++) {
                                NetworkServiceL3NetworkRefInventory service = services.get(j);
                                String networkServiceType = service.getNetworkServiceType();
                                if (j > 0) {
                                    sb.append(",");
                                }
                                sb.append(networkServiceType);
                            }
                            networkServices = sb.toString();
                        }
                        netWorkL3DTO.setNetworkServices(networkServices);
                        // ip详情
                        List<IpRangeInventory> ipRanges = l3Inventory.getIpRanges();
                        if (ipRanges.size()>0){
                            for (IpRangeInventory ipRange : ipRanges) {
                                netWorkL3DTO.setStartIp(ipRange.getStartIp());
                                netWorkL3DTO.setEndIp(ipRange.getEndIp());
                                netWorkL3DTO.setNetmask(ipRange.getNetmask());
                                netWorkL3DTO.setNetworkCidr(ipRange.getNetworkCidr());
                                netWorkL3DTO.setGateway(ipRange.getGateway());
                                netWorkL3DTO.setNetworkSegment(ipRange.getName());
                            }
                        }
                        List<L3NetworkHostRouteInventory> hostRoute = l3Inventory.getHostRoute();
                        if (hostRoute.size() > 0) {
                            for (L3NetworkHostRouteInventory l3NetworkHostRouteInventory : hostRoute) {
                                netWorkL3DTO.setNextHopIp(l3NetworkHostRouteInventory.getNexthop());
                            }
                        }
                        netWorkL3DTO.setPlatformId(p.getId());
                        netWorkL3DTO.setPlatformName(p.getName());
                        netWorkL3DTO.setTenantId(p.getTenantId());
                        netWorkL3DTO.setCreateTime(DateUtil.parse(Convert.toStr(l3Inventory.getCreateDate())));
                        netWorkL3DTO.setTypeName("zstack");
                        netWorkL3DTOS.add(netWorkL3DTO);
                    }
                }
            }
        }
        if (netWorkL3DTOS.size() > 0) {
            // 获取当前节点的index 与 总节点数
            int shardIndex = XxlJobHelper.getShardIndex();
            int shardTotal = XxlJobHelper.getShardTotal();
            log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
            Long networkL3Count = networkApi.getNetWorkL3Count("zstack");
            List<NetWorkL3DTO> dtos = networkApi.getNetWorkL3List("zstack").getData();
            List<NetWorkL3DTO> shardingData = new ArrayList<>();


            if (shardTotal < 0) {
                shardingData = netWorkL3DTOS;
            } else {
                shardingData = StringUtil.getShardingData(netWorkL3DTOS, shardTotal, shardIndex);
            }// 对分片数据进行业务处理
            for (NetWorkL3DTO item : shardingData) {
                // 模拟业务逻辑处理
                log.info("Processing item: " + item.getName());
            }
            if (networkL3Count == 0) {
                networkApi.addNetWorkL3(shardingData);
            } else {
                List<NetWorkL3DTO> collect3 = dtos.stream()
                        .filter(item -> !netWorkL3DTOS.stream().map(NetWorkL3DTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid()))
                        .collect(Collectors.toList());
                if (collect3.size() > 0) {
                    networkApi.deleteNetWorkL3ByNameList(collect3);
                }
                networkApi.updateNetWorkL3(shardingData);
                List<NetWorkL3DTO> collect = shardingData.stream()
                        .filter(item -> !dtos.stream().map(NetWorkL3DTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid()))
                        .collect(Collectors.toList());
                if (collect.size() > 0) {
                    networkApi.addNetWorkL3(collect);
                }
            }
        }
    }
    public static String removeProtocolAndPort(String url) {
        // 去除协议部分
        String noProtocol = url.replaceFirst("^(http://|https://)", "");
        // 去除端口号部分
        String noPort = noProtocol.replaceFirst(":\\d+$", "");
        return noPort;
    }

    public static String extractPort(String url) {
        // 定义正则表达式模式来匹配端口号
        Pattern pattern = Pattern.compile(":\\d+$");
        Matcher matcher = pattern.matcher(url);

        // 查找匹配的端口号
        if (matcher.find()) {
            // 去掉冒号并返回端口号
            return matcher.group().substring(1);
        } else {
            // 如果没有找到端口号，根据协议返回默认端口号
            if (url.startsWith("https://")) {
                return "443";
            } else if (url.startsWith("http://")) {
                return "80";
            }
        }

        // 如果没有找到协议，返回空字符串
        return "";
    }

}
