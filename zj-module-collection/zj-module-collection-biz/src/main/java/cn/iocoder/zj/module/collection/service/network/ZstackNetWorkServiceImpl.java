package cn.iocoder.zj.module.collection.service.network;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.iocoder.zj.module.collection.service.zstack.core.ZstackApiConstant;
import cn.iocoder.zj.module.collection.util.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @ClassName : ZstackNetWorkServiceImpl  //类名
 * @Description : 网络相关实现  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/8/3  16:01
 */
@Slf4j
@Service
public class ZstackNetWorkServiceImpl implements ZstackNetWorkService {
    @Override
    public String l2Networks(String url, String token) {
        HttpResponse result = HttpRequest.get(url + ZstackApiConstant.GET_L2_NETWORKS)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .execute();
        if (result.getStatus() != 200) {
            throw new RuntimeException("getl2Networks error");
        }
        return result.body();
    }

    @Override
    public String vpcNetworks(String url, String token, String uuid) {
        String body = "";
            HttpResponse result = HttpRequest.get(url + ZstackApiConstant.GET_L3_NETWORKS + "/" + uuid)
                    .header(Header.AUTHORIZATION, "OAuth " + token)
                    .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                    .execute();
            if (result.getStatus() != 200) {
                throw new RuntimeException("getl3Networks error");
            }
            body = result.body();

        return body;
    }

    @Override
    public JSONArray l3Networks(String url, String token) {
        String basicNetwork = "?q=l2Network.cluster.type=zstack&q=system=false&q=category=Private&q=type=L3BasicNetwork";
        String publicNetwork = "?q=l2Network.cluster.type=zstack&q=category=Public";
        HttpResponse basicNetworkResult = HttpRequest.get(url + ZstackApiConstant.GET_L3_NETWORKS+basicNetwork)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .execute();
        if (basicNetworkResult.getStatus() != 200) {
            throw new RuntimeException("get basicNetworks error");
        }
        JSONArray basicNetworkArray = JSONObject.parseObject(basicNetworkResult.body()).getJSONArray("inventories");
        HttpResponse publicNetworkResult = HttpRequest.get(url + ZstackApiConstant.GET_L3_NETWORKS+publicNetwork)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .execute();
        if (publicNetworkResult.getStatus() != 200) {
            throw new RuntimeException("get publicNetwork error");
        }
        JSONArray publicNetworkArray = JSONObject.parseObject(publicNetworkResult.body()).getJSONArray("inventories");
        return publicNetworkArray.fluentAddAll(basicNetworkArray);
    }
}
