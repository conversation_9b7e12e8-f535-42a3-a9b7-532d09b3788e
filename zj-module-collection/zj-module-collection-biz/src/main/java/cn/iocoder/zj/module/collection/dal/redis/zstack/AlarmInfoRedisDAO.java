package cn.iocoder.zj.module.collection.dal.redis.zstack;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

import static cn.iocoder.zj.module.collection.dal.redis.RedisKeyConstants.ALARM_CONFIG;
import static cn.iocoder.zj.module.collection.dal.redis.RedisKeyConstants.ALARM_HOST_RELATION;


@Repository
public class AlarmInfoRedisDAO {
    @Resource
    private StringRedisTemplate stringRedisTemplate;


    public String  getConfig(String accessToken) {
        String redisKey = configFormatKey(accessToken);
        return stringRedisTemplate.opsForValue().get(redisKey);
    }
    public String  getRelation(String accessToken) {
        String redisKey = relationFormatKey(accessToken);
        return stringRedisTemplate.opsForValue().get(redisKey);
    }
    private static String configFormatKey(String accessToken) {
        return String.format(ALARM_CONFIG, accessToken);
    }
    private static String relationFormatKey(String accessToken) {
        return String.format(ALARM_HOST_RELATION, accessToken);
    }
}
