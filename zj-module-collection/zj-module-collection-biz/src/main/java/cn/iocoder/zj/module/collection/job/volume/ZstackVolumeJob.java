package cn.iocoder.zj.module.collection.job.volume;

import cn.hutool.core.date.DateUtil;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.zstack.ZstackAccessTokenRedisDAO;
import cn.iocoder.zj.module.collection.service.volumes.ZstackVolumesService;
import cn.iocoder.zj.module.collection.service.zstack.AbstractZstackApi;
import cn.iocoder.zj.module.monitor.api.hostinfo.HostInfoApi;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.storage.StorageInfoApi;
import cn.iocoder.zj.module.monitor.api.storage.dto.StorageRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.valume.VolumeApi;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeAttachableVmDTO;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeDTO;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeSnapshotDTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ZstackVolumeJob {

    @Resource
    ZstackAccessTokenRedisDAO zstackAccessTokenRedisDAO;
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    ZstackVolumesService zstackVolumesService;
    @Resource
    PlatformRedisDAO platformRedisDAO;
    @Resource
    HostInfoApi hostInfoApi;
    @Resource
    StorageInfoApi storageInfoApi;
    @Resource
    VolumeApi volumeApi;

    @Resource(name = "zstackCloudServiceImpl")
    AbstractZstackApi zstackApi;

    public void zstackVolumeJob() {
        collectVolumeInfo();
        collectVolumeSnapshotInfo();
        getDataVolumeAttachableVm();
    }

    /**
     * 采集云盘数据
     */
    @XxlJob("volumeInfo")
    public void collectVolumeInfo() {
        List<VolumeDTO> volumeDTOList = new ArrayList<>();
        List<PlatformconfigDTO> platformconfigDTOList = new ArrayList<>();
        if (platformRedisDAO.get("platform") == null) {
            platformconfigDTOList = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform", platformconfigDTOList);
        }
        platformconfigDTOList = platformRedisDAO.get("platform");
        if (platformconfigDTOList.size() > 0) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                if (zstackAccessTokenRedisDAO.get("zstack:" + p.getId()) != null) {
                    String token = zstackAccessTokenRedisDAO.get("zstack:" + p.getId()).getUuid();
                    String volumeStr = zstackVolumesService.volumeBaseInfo(p.getUrl(), token);
                    JSONArray volumes = JSONObject.parseObject(volumeStr).getJSONArray("inventories");
                    for (int i = 0; i < volumes.size(); i++) {
                        VolumeDTO volumeDTO = new VolumeDTO();
                        JSONObject jsonObject = volumes.getJSONObject(i);
                        String uuid = jsonObject.getString("uuid");
                        Long platformId = p.getId();
                        String platformName = p.getName();
                        String name = jsonObject.getString("name");
                        String description = jsonObject.getString("description");
                        String primaryStorageUuid = jsonObject.getString("primaryStorageUuid");
                        String vminstanceUuid = jsonObject.getString("vmInstanceUuid");
                        String type = jsonObject.getString("type");
                        String format = jsonObject.getString("format");
                        Long size = jsonObject.getLong("size");
                        Long actualSize = jsonObject.getLong("actualSize");
                        String state = jsonObject.getString("state");
                        String status = jsonObject.getString("status");
                        Date vCreateTime = new Date(jsonObject.getString("createDate"));
                        Date vUpdateTime = new Date(jsonObject.getString("lastOpDate"));

                        volumeDTO.setCreateTime(DateUtil.toLocalDateTime(new Date(jsonObject.getString("createDate"))));
                        volumeDTO.setDescription(description);
                        volumeDTO.setName(name);
                        volumeDTO.setFormat(format);
                        volumeDTO.setSize(size);
                        volumeDTO.setType(type);
                        volumeDTO.setActualSize(actualSize);
                        volumeDTO.setState(state);
                        volumeDTO.setUuid(uuid);
                        volumeDTO.setStatus(status);
                        volumeDTO.setPlatformId(platformId);
                        volumeDTO.setPlatformName(platformName);
                        volumeDTO.setVmInstanceUuid(vminstanceUuid);
                        volumeDTO.setPrimaryStorageUuid(primaryStorageUuid);
                        volumeDTO.setActualUse(actualSize);
                        Long actualFree = size - actualSize > 0 ? size - actualSize : 0;
                        volumeDTO.setActualFree(actualFree);
                        volumeDTO.setDeleted(0L);
                        BigDecimal result = BigDecimal.valueOf(actualSize).divide(BigDecimal.valueOf(size), 2, BigDecimal.ROUND_HALF_UP);
                        volumeDTO.setActualRatio(result.compareTo(BigDecimal.valueOf(1)) > 0 ? "1.00" : result.toString());
                        volumeDTO.setVCreateDate(vCreateTime);
                        volumeDTO.setVUpdateDate(vUpdateTime);
                        volumeDTO.setMediaType(MediaType.ROTATE.getEnName());
                        volumeDTO.setIsMount(vminstanceUuid != null);
                        volumeDTOList.add(volumeDTO);
                    }
                }
            }
        }

        if (!volumeDTOList.isEmpty()) {
            Long volumeCount = volumeApi.getVolumeCount().getData();
            List<VolumeDTO> dtos = volumeApi.getAllVolumes(-1L, "zstack").getData();
            //获取所有存储信息
            List<StorageRespCreateReqDTO> storageRespDTOList = storageInfoApi.getAll("zstack").getData();
            //获取所有的云主机信息
            List<HostInfoRespCreateReqDTO> hostInfoRespCreateReqDTOS = hostInfoApi.getAll("zstack").getData();
            for (VolumeDTO volumeDTO : volumeDTOList) {
                //获取主存储名称和主存储类型
                Optional<StorageRespCreateReqDTO> storageRespDTO = storageRespDTOList.stream()
                        .filter(s -> s.getUuid().equals(volumeDTO.getPrimaryStorageUuid())
                                && Objects.equals(s.getPlatformId(), volumeDTO.getPlatformId())).findFirst();
                if (storageRespDTO.isPresent()) {
                    volumeDTO.setPrimaryStorageName(storageRespDTO.get().getName());
                    volumeDTO.setPrimaryStorageType(storageRespDTO.get().getType());
                }
                if (volumeDTO.getVmInstanceUuid() != null) {
                    Optional<HostInfoRespCreateReqDTO> hostInfoRespCreateReqDTO = hostInfoRespCreateReqDTOS.stream()
                            .filter(s -> s.getUuid().equals(volumeDTO.getVmInstanceUuid())
                                    && Objects.equals(s.getPlatformId(), volumeDTO.getPlatformId())).findFirst();
                    hostInfoRespCreateReqDTO.ifPresent(infoRespCreateReqDTO -> volumeDTO.setVmInstanceName(infoRespCreateReqDTO.getName()));
                }
            }

            if (volumeCount == 0) {
                volumeApi.addVolumes(volumeDTOList);
            } else {
                //比较uuid不存在删除
                List<VolumeDTO> deleteTarget = dtos.stream()
                        .filter(volumeDTO -> volumeDTOList.stream()
                                .noneMatch(item -> item.getUuid().equals(volumeDTO.getUuid())))
                        .toList();

                //新增云盘
                List<VolumeDTO> collect = volumeDTOList.stream()
                        .filter(volumeDTO -> dtos.stream()
                                .noneMatch(item -> item.getUuid().equals(volumeDTO.getUuid())))
                        .collect(Collectors.toList());
                //修改云盘
                List<VolumeDTO> updateDTOs = volumeDTOList.stream()
                       .filter(volumeDTO -> dtos.stream()
                               .anyMatch(item -> item.getUuid().equals(volumeDTO.getUuid())))
                       .toList();

                if (!collect.isEmpty()) {
                    volumeApi.addVolumes(collect);
                }
                if (!updateDTOs.isEmpty()){
                    volumeApi.updateVolumes(updateDTOs);
                }
                if (!deleteTarget.isEmpty()) {
                    volumeApi.delVolumes(deleteTarget);
                }
            }
        }
    }

    /**
     * 采集云盘快照数据
     */
        @XxlJob("volumeSnapshotInfo")
    public void collectVolumeSnapshotInfo() {
        List<VolumeSnapshotDTO> volumeSnapshotDTOList = new ArrayList<>();
        List<PlatformconfigDTO> platformconfigDTOList = new ArrayList<>();
        if (platformRedisDAO.get("platform") == null) {
            platformconfigDTOList = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform", platformconfigDTOList);
        }
        platformconfigDTOList = platformRedisDAO.get("platform");
        if (!platformconfigDTOList.isEmpty()) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                if (zstackAccessTokenRedisDAO.get("zstack:" + p.getId()) != null) {
                    String token = zstackAccessTokenRedisDAO.get("zstack:" + p.getId()).getUuid();
                    String volumeSnapshotStr = zstackVolumesService.volumeSnapshotInfo(p.getUrl(), token);
                    JSONArray volumeSnapshots = JSONObject.parseObject(volumeSnapshotStr).getJSONArray("inventories");
                    for (int i = 0; i < volumeSnapshots.size(); i++) {
                        VolumeSnapshotDTO volumeSnapshotDTO = new VolumeSnapshotDTO();
                        JSONObject jsonObject = volumeSnapshots.getJSONObject(i);
                        String uuid = jsonObject.getString("uuid");
                        String name = jsonObject.getString("name");
                        String description = jsonObject.getString("description");
                        String volumeUuid = jsonObject.getString("volumeUuid");
                        String primaryStorageUuid = jsonObject.getString("primaryStorageUuid");
                        String installPath = jsonObject.getString("primaryStorageInstallPath");
                        String type = jsonObject.getString("type");
                        String volumeType = jsonObject.getString("volumeType");
                        String latest = jsonObject.getString("latest");
                        Long size = jsonObject.getLong("size");
                        String status = jsonObject.getString("state");
                        Date vCreateDate = new Date(jsonObject.getString("createDate"));
                        Date vUpdateDate = new Date(jsonObject.getString("lastOpDate"));
                        String format = jsonObject.getString("format");
                        Long platformId = p.getId();
                        String platformName = p.getName();
                        volumeSnapshotDTO.setCreateTime(DateUtil.toLocalDateTime(new Date()));
                        volumeSnapshotDTO.setUuid(uuid);
                        volumeSnapshotDTO.setName(name);
                        volumeSnapshotDTO.setDescription(description);
                        volumeSnapshotDTO.setVolumeUuid(volumeUuid);
                        volumeSnapshotDTO.setPrimaryStorageUuid(primaryStorageUuid);
                        volumeSnapshotDTO.setInstallPath(installPath);
                        if (type.equals("Storage")) {
                            volumeSnapshotDTO.setType("云盘快照");
                        }else {
                            volumeSnapshotDTO.setType("主机快照");
                        }
                        volumeSnapshotDTO.setVolumeType(volumeType);
                        volumeSnapshotDTO.setLatest(latest);
                        volumeSnapshotDTO.setPlatformName(platformName);
                        volumeSnapshotDTO.setPlatformId(platformId);
                        volumeSnapshotDTO.setTypeName("zstack");
                        volumeSnapshotDTO.setSize(size);
                        volumeSnapshotDTO.setStatus(status);
                        volumeSnapshotDTO.setVCreateDate(vCreateDate);
                        volumeSnapshotDTO.setVUpdateDate(vUpdateDate);
                        volumeSnapshotDTO.setFormat(format);
                        volumeSnapshotDTO.setIsMemory(false);
                        volumeSnapshotDTOList.add(volumeSnapshotDTO);
                    }
                }
            }
        }
        if (!volumeSnapshotDTOList.isEmpty()) {
            Long volumeSnapshotCount = volumeApi.getVolumeSnapshotCount("zstack").getData();
            List<VolumeSnapshotDTO> dtos = volumeApi.getAllVolumeSnapshots("zstack").getData();
            if (volumeSnapshotCount == 0) {
                volumeApi.addVolumeSnapshots(volumeSnapshotDTOList);
            } else {
                List<VolumeSnapshotDTO> collect = volumeSnapshotDTOList.stream()
                        .filter(item -> !dtos.stream().map(VolumeSnapshotDTO::getUuid)
                                .toList().contains(item.getUuid()))
                        .collect(Collectors.toList());
                List<VolumeSnapshotDTO> deleteTarget = dtos.stream()
                        .filter(item -> !(volumeSnapshotDTOList.stream().map(VolumeSnapshotDTO::getUuid)
                                .toList().contains(item.getUuid()))
                        ).collect(Collectors.toList());
                //修改列表
                List<VolumeSnapshotDTO> updateDTOs = volumeSnapshotDTOList.stream()
                      .filter(item ->dtos.stream().map(VolumeSnapshotDTO::getUuid)
                              .toList().contains(item.getUuid()))
                      .toList();

                if (!updateDTOs.isEmpty()) {
                    volumeApi.updateVolumeSnapshots(updateDTOs);
                }
                if (!deleteTarget.isEmpty()) {
                    volumeApi.delVolumeSnapshots(deleteTarget);
                }
                if (!collect.isEmpty()) {
                    volumeApi.addVolumeSnapshots(collect);
                }
            }
        }
    }

    @XxlJob("getDataVolumeAttachableVm")
    public void getDataVolumeAttachableVm() {
        List<PlatformconfigDTO> platformconfigDTOList = new ArrayList<>();
        if (platformRedisDAO.get("platform") == null) {
            platformconfigDTOList = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform", platformconfigDTOList);
        }
        platformconfigDTOList = platformRedisDAO.get("platform");
        List<VolumeAttachableVmDTO> dtoList = new ArrayList<>();
        List<HostInfoRespCreateReqDTO> hostDTOs = hostInfoApi.getAll("zstack").getData();
        if (!platformconfigDTOList.isEmpty()) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                List<HostInfoRespCreateReqDTO> targetHosts = hostDTOs.stream().filter(item -> !item.getState().equals("Destroyed") && Objects.equals(item.getPlatformId(), p.getId()))
                        .toList();
                for (HostInfoRespCreateReqDTO hostDTO : targetHosts) {
                    if (zstackAccessTokenRedisDAO.get("zstack:" + p.getId()) != null) {
                        String token = zstackAccessTokenRedisDAO.get("zstack:" + p.getId()).getUuid();
                        try {
                            String volumeStr = zstackVolumesService.getVmAttachableDataVolume(p.getUrl(), token, hostDTO.getUuid());
                            // 在没有抛出异常的情况下执行的代码
                            JSONArray volumeAttachableVm = JSONObject.parseObject(volumeStr).getJSONArray("inventories");
                            if (!volumeAttachableVm.isEmpty()) {
                                for (int i = 0; i < volumeAttachableVm.size(); i++) {
                                    JSONObject jsonObject = volumeAttachableVm.getJSONObject(i);
                                    VolumeAttachableVmDTO attachableVmDto = new VolumeAttachableVmDTO();
                                    attachableVmDto.setVolumeName(jsonObject.getString("name"));
                                    attachableVmDto.setVolumeUuid(jsonObject.getString("uuid"));
                                    attachableVmDto.setHostName(hostDTO.getName());
                                    attachableVmDto.setHostUuid(hostDTO.getUuid());
                                    attachableVmDto.setPlatformName(p.getName());
                                    attachableVmDto.setPlatformId(p.getId());
                                    dtoList.add(attachableVmDto);
                                }
                            }
                        } catch (RuntimeException e) {
                        }
                    }
                }
            }
        }
        if (!dtoList.isEmpty()) {
            Long attachableVmCount = volumeApi.getAttachableVmCount().getData();
            List<VolumeAttachableVmDTO> dtos = volumeApi.getAllVolumeAttachableVm().getData();
            if (attachableVmCount == 0) {
                volumeApi.addVolumeAttachableVms(dtoList);
            } else {
                volumeApi.updateVolumeAttachableVms(dtoList);
                List<VolumeAttachableVmDTO> collect = dtoList.stream()
                        .filter(item -> !(dtos.stream().map(VolumeAttachableVmDTO::getVolumeUuid)
                                .toList().contains(item.getVolumeUuid())
                                && dtos.stream().map(VolumeAttachableVmDTO::getHostUuid)
                                .toList().contains(item.getHostUuid()))
                        )
                        .collect(Collectors.toList());
                List<VolumeAttachableVmDTO> deleteTarget = dtos.stream()
                        .filter(item -> !(dtoList.stream().map(VolumeAttachableVmDTO::getVolumeUuid)
                                .toList().contains(item.getVolumeUuid())
                                && dtoList.stream().map(VolumeAttachableVmDTO::getHostUuid)
                                .toList().contains(item.getHostUuid()))
                        )
                        .collect(Collectors.toList());
                if (!deleteTarget.isEmpty()) {
                    volumeApi.delVolumeAttachableVms(deleteTarget);
                }
                if (!collect.isEmpty()) {
                    volumeApi.addVolumeAttachableVms(collect);
                }
            }
        }
    }
}
