package cn.iocoder.zj.module.collection.dal.dataobject.zstack;

import lombok.Data;

import java.util.Date;

@Data
public class HostInFluxRespInfo {
    private Long id;

    private String alarmName;
    //监控对象名称
    private String metricName;

    private String time;

    private String type;

    private String uuid;
    /**
     * 宿主机或云主机或存储的名称
     */
    private String productsName;
    /**
     * 实际告警信息
     */
    private String actualContext;

    private Double value;
    /**
     * 消息内容
     */
    private String context;
    /**
     * 资源类型，host云主机；hardware宿主机；storage存储；image镜像
     */
    private String sourceType;
    /**
     * 资源类型，云主机；宿主机；存储；镜像
     */
    private String sourceName;
    /**
     * 字典名称
     */
    private String dictLabelName;
    /**
     * 字典类型
     */
    private String dictLabelType;
    /**
     * 字典值
     */
    private String dictLabelValue;
    /**
     * 触发规则
     */
    private String alarmRule;
    /**
     * 告警阈值
     */
    private Integer alarmVal;
    /**
     * 收敛次数
     */
    private Long alarmTime;
    /**
     * 告警级别
     */
    private Integer alarmLevel;
    /**
     * 告警次数
     */
    private int alarmNum;
    /**
     * 地区ID
     */
    private Integer regionId;
    /**
     * 地区名称
     */
    private String regionName;
    /**
     * 省地区ID
     */
    private Integer parentRegionId;
    /**
     * 省地区名称
     */
    private String parentRegionName;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 平台配置ID
     */
    private Long platformConfigId;
    /**
     * 平台配置名称
     */
    private String platformName;
}
