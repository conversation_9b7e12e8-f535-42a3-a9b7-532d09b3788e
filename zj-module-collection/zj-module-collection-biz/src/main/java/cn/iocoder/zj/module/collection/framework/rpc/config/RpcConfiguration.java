package cn.iocoder.zj.module.collection.framework.rpc.config;

import cn.iocoder.zj.module.infra.api.file.FileApi;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.monitor.api.asset.GatherAssetApi;
import cn.iocoder.zj.module.monitor.api.eip.EipApi;
import cn.iocoder.zj.module.monitor.api.hardware.HardWareInfoApi;
import cn.iocoder.zj.module.monitor.api.hardwarenic.HardWareNicApi;
import cn.iocoder.zj.module.monitor.api.hardwarestorage.HardWareStorageApi;
import cn.iocoder.zj.module.monitor.api.hostinfo.HostInfoApi;
import cn.iocoder.zj.module.monitor.api.hostnic.HostNicApi;
import cn.iocoder.zj.module.monitor.api.hostsecgroup.HostSecgroupApi;
import cn.iocoder.zj.module.monitor.api.imageinfo.ImageInfoApi;
import cn.iocoder.zj.module.monitor.api.network.NetworkApi;
import cn.iocoder.zj.module.monitor.api.secgroup.SecgroupApi;
import cn.iocoder.zj.module.monitor.api.secgrouprule.SecgroupruleApi;
import cn.iocoder.zj.module.monitor.api.storage.StorageInfoApi;
import cn.iocoder.zj.module.monitor.api.storagepool.StoragePoolApi;
import cn.iocoder.zj.module.monitor.api.topology.TopologyApi;
import cn.iocoder.zj.module.monitor.api.valume.VolumeApi;
import cn.iocoder.zj.module.om.api.dbfile.DbfileApi;
import cn.iocoder.zj.module.om.api.userbind.UserBindApi;
import cn.iocoder.zj.module.report.api.subscription.dto.ReportSubscriptionApi;
import cn.iocoder.zj.module.system.api.dingtalk.DingTalkSendApi;
import cn.iocoder.zj.module.system.api.mail.MailSendApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.region.RegionApi;
import cn.iocoder.zj.module.system.api.sms.SmsSendApi;
import cn.iocoder.zj.module.system.api.wechat.WeChatSendApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName : RpcConfiguration  //类名
 * @Description : RPC 接口  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/23  10:38
 */

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {HostInfoApi.class,
        RegionApi.class,
        PlatformconfigApi.class,
        HardWareInfoApi.class,
        StorageInfoApi.class,
        NetworkApi.class,
        AlarmConfigApi.class,
        SmsSendApi.class,
        VolumeApi.class,
        MailSendApi.class,
        WeChatSendApi.class,
        GatherAssetApi.class,
        DbfileApi.class,
        ReportSubscriptionApi.class,
        TopologyApi.class,
        HostNicApi.class,
        EipApi.class,
        SecgroupApi.class,
        HostSecgroupApi.class,
        SecgroupruleApi.class,
        ReportSubscriptionApi.class,
        DingTalkSendApi.class,
        UserBindApi.class,
        FileApi.class, ImageInfoApi.class,
        HardWareNicApi.class,
        HardWareStorageApi.class, StoragePoolApi.class})
public class RpcConfiguration {
}
