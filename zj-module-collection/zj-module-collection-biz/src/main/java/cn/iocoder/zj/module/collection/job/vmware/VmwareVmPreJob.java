package cn.iocoder.zj.module.collection.job.vmware;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.iocoder.zj.module.collection.dal.dataobject.vmware.PerMonitorDO;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.framework.influx.config.InfluxDBTemplate;
import cn.iocoder.zj.module.collection.service.vmware.perf.RealtimePerfMonitor;
import cn.iocoder.zj.module.collection.util.SampleUtil;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.monitor.api.hostinfo.HostInfoApi;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.valume.VolumeApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.vmware.vim25.ServiceContent;
import com.vmware.vim25.mo.*;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.rmi.RemoteException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @ClassName : VmwareVmPreJob  //类名
 * @Description : 性能数据  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/9/23  13:11
 */
@Component
@Slf4j
public class VmwareVmPreJob {
    @Resource
    InfluxDBTemplate influxDBTemplate;
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    PlatformRedisDAO platformRedisDAO;
    @Resource
    HostInfoApi hostInfoApi;
    @Resource
    VolumeApi volumeApi;

    private final ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());


    @XxlJob("vmwarevmPre")
    public void vmwarevmPre() throws Exception {

        // 获取redis中数据
        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                .filter(dto -> "vmware".equals(dto.getTypeCode()))
                .collect(Collectors.toList());

        if (filteredList.isEmpty()) return;
        for (PlatformconfigDTO platformconfigDTO : filteredList) {
            // todo 待修改
            if (platformconfigDTO.getId() == 18) {
                continue;
            }
            ServiceInstance serviceInstance = SampleUtil.createServiceInstance(platformconfigDTO.getUrl(), platformconfigDTO.getUsername(), platformconfigDTO.getPassword());
            List<HostInfoRespCreateReqDTO> hostInfoApiVmByPlatformId1 = hostInfoApi.getVmByPlatformId(platformconfigDTO.getId());
            BatchPoints batchPoints = processVmPre(hostInfoApiVmByPlatformId1, platformconfigDTO, serviceInstance);
            influxDBTemplate.writeBatch(batchPoints);
            serviceInstance.getServerConnection().logout();
        }
    }

    private BatchPoints processVmPre(List<HostInfoRespCreateReqDTO> hostInfoApiVmByPlatformId1, PlatformconfigDTO platformconfigDTO, ServiceInstance serviceInstance) {
        BatchPoints batchPoints = BatchPoints.builder().build();

        List<CompletableFuture<Void>> futures = hostInfoApiVmByPlatformId1.stream()
                .map(vm -> CompletableFuture.runAsync(() -> {
                    try {
                        collectMetricsVm(platformconfigDTO, vm, batchPoints, serviceInstance);
                    } catch (Exception e) {
                        log.error("Error collecting metrics for host: " + vm.getName(), e);
                    }
                }, executor))
                .collect(Collectors.toList());

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        return batchPoints;
    }

    private void collectMetricsVm(PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO vm, BatchPoints batchPoints, ServiceInstance serviceInstance) throws Exception {
        // 获取ServiceContent
        ServiceContent serviceContent = serviceInstance.getServiceContent();
        // 获取关于vSphere服务器的版本信息
        String apiVersion = serviceContent.getAbout().getApiVersion();
        // 如果小于7.0 走单个性能数据查询
        if (isVersionLessThan(apiVersion, "7.0.0")) {
            Map<String, ManagedEntity> vmEntities = getStringManagedEntityMap(serviceInstance, vm);
            collectMetricsForByoneVm(serviceInstance, platformconfigDTO, vm, batchPoints, vmEntities);
        } else {
            collectMetricsForVm(serviceInstance, platformconfigDTO, vm, batchPoints);
        }
    }

    private void collectMetricsForByoneVm(ServiceInstance serviceInstance, PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BatchPoints batchPoints, Map<String, ManagedEntity> vmEntities) throws Exception {

        ManagedEntity managedEntities = vmEntities.get(hostInfoRespCreateReqDTO.getName());

        long startTime = System.currentTimeMillis(); // 开始时间

//            ManagedEntity managedEntities = new InventoryNavigator(serviceInstance.getRootFolder())
//                    .searchManagedEntity("VirtualMachine", hostInfoRespCreateReqDTO.getName());

        VirtualMachine virtualMachine = (VirtualMachine) managedEntities;
        Datastore[] datastores = virtualMachine.getDatastores();
        Long storage_total = 0L;
        Long storage_total_free = 0L;
        for (Datastore datastore : datastores) {
            storage_total += datastore.getSummary().getCapacity();
            storage_total_free += datastore.getSummary().getFreeSpace();
        }
        BigDecimal memSize = Convert.toBigDecimal(virtualMachine.getSummary().getConfig().getMemorySizeMB());
        BigDecimal storage_usage = NumberUtil.sub(storage_total, storage_total_free);
        BigDecimal storage_rate = NumberUtil.div(storage_total_free, storage_total);
        String uuid = hostInfoRespCreateReqDTO.getUuid();
        // ==========================================CPU==================================================


        // cpu.usage.none CPUUsedUtilization
        List<PerMonitorDO> CPUUsedUtilization = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.usage.none", platformconfigDTO);
        if (!CPUUsedUtilization.isEmpty()) {
            Long allUsage = 0L;
            for (PerMonitorDO perMonitorDO : CPUUsedUtilization) {
                allUsage += perMonitorDO.getValue();
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "cpu")
                        .tag("uuid", uuid)
                        .tag("metricName", "CPUUsedUtilization")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", "CPUUsedUtilization")
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 100))
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }

            BigDecimal v = NumberUtil.div(Convert.toBigDecimal(allUsage), CPUUsedUtilization.size());
            Point point = Point.measurement("zj_cloud_host")
                    .tag("label", "cpu")
                    .tag("uuid", uuid)
                    .tag("metricName", "CPUAllUsedUtilization")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("vm_type", "CPUAllUsedUtilization")
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("vm_metricName", "CPUAllUsedUtilization")
                    .addField("type", "CPUAllUsedUtilization")
                    .addField("value", NumberUtil.div(v, 100))
                    .time(CPUUsedUtilization.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);

        }


        // cpu.usage.average CPUAverageUsedUtilization
        List<PerMonitorDO> CPUAverageUsedUtilization = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.usage.average", platformconfigDTO);
        if (!CPUAverageUsedUtilization.isEmpty()) {
            for (PerMonitorDO perMonitorDO : CPUAverageUsedUtilization) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "cpu")
                        .tag("uuid", uuid)
                        .tag("metricName", "CPUAverageUsedUtilization")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", "CPUAverageUsedUtilization")
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 100, 2))
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }


        // cpu.idle.summation CPUIdleUtilization
        List<PerMonitorDO> CPUIdleUtilization = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.idle.summation", platformconfigDTO);
        if (!CPUIdleUtilization.isEmpty()) {
            for (PerMonitorDO perMonitorDO : CPUIdleUtilization) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "cpu")
                        .tag("uuid", uuid)
                        .tag("metricName", "CPUIdleUtilization")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", "CPUIdleUtilization")
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", Convert.toBigDecimal(perMonitorDO.getValue()))
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }

//        // cpu.usage.none CPUAllUsedUtilization
//        List<PerMonitorDO> CPUAllUsedUtilization = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.usage.none");
//        if (CPUAllUsedUtilization.size() > 0) {
//            for (PerMonitorDO perMonitorDO : CPUAllUsedUtilization) {
//                if (perMonitorDO.getInstance().isEmpty()) {
//                    perMonitorDO.setInstance("0");
//                }
//                Point point = Point.measurement("zj_cloud_host")
//                        .tag("label", "cpu")
//                        .tag("uuid", uuid)
//                        .tag("metricName", "CPUAllUsedUtilization")
//                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                        .tag("vm_type", perMonitorDO.getInstance())
//                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                        .addField("vm_metricName", "CPUAllUsedUtilization")
//                        .addField("type", perMonitorDO.getInstance())
//                        .addField("value", Convert.toBigDecimal(perMonitorDO.getValue()))
//                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                        .build();
//                batchPoints.point(point);
//            }


        // cpu.utilization.average CPUSystemUtilization
        List<PerMonitorDO> CPUSystemUtilization = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.utilization.average", platformconfigDTO);
        if (CPUSystemUtilization.size() > 0) {
            for (PerMonitorDO perMonitorDO : CPUSystemUtilization) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "cpu")
                        .tag("uuid", uuid)
                        .tag("metricName", "CPUSystemUtilization")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", "CPUSystemUtilization")
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", Convert.toBigDecimal(perMonitorDO.getValue()))
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }
        // cpu.used.summation
        List<PerMonitorDO> cpuUsedList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.used.summation", platformconfigDTO);
        Long valueAll = 0L;
        if (cpuUsedList.size() > 0) {
            for (PerMonitorDO perMonitorDO : cpuUsedList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    valueAll += valueAll + perMonitorDO.getValue();
                }
            }
        }

        // cpu.wait.summation
        List<PerMonitorDO> cpuWaitList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "cpu.wait.summation", platformconfigDTO);
        if (!cpuWaitList.isEmpty()) {
            for (PerMonitorDO perMonitorDO : cpuWaitList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                BigDecimal value = NumberUtil.div(perMonitorDO.getValue(), valueAll, 2);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "cpu")
                        .tag("uuid", uuid)
                        .tag("metricName", "CPUWaitUtilization")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", "CPUWaitUtilization")
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", value)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }

        //==========================================内存==================================================
        List<PerMonitorDO> memUsageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "mem.usage.average", platformconfigDTO);
        for (PerMonitorDO perMonitorDO : memUsageList) {
            BigDecimal value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 100);
            String label = perMonitorDO.getInstance();
            String type = label + "MemoryUsedInPercent";
            Point point = Point.measurement("zj_cloud_host")
                    .tag("label", "mem")
                    .tag("uuid", uuid)
                    .tag("metricName", "MemoryUsedInPercent")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("vm_type", type)
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("vm_metricName", "MemoryUsedInPercent")
                    .addField("type", "MemoryUsedInPercent")
                    .addField("value", value)
                    .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }


        List<PerMonitorDO> memActiveList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "mem.active.average", platformconfigDTO);
        for (PerMonitorDO perMonitorDO : memActiveList) {
            String label = perMonitorDO.getInstance();
            String type = label + "MemoryUsedBytes";
            BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 1024, 2);
            Point point = Point.measurement("zj_cloud_host")
                    .tag("label", "mem")
                    .tag("uuid", uuid)
                    .tag("metricName", "MemoryUsedBytes")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("vm_type", type)
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("vm_metricName", "MemoryUsedBytes")
                    .addField("type", "MemoryUsedBytes")
                    .addField("value", value)
                    .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }

        for (PerMonitorDO ac : memActiveList) {
            BigDecimal sd = NumberUtil.sub(NumberUtil.mul(memSize, 1024), Convert.toBigDecimal(ac.getValue()));
            String label = ac.getInstance();
            String type = label + "MemoryFreeBytes";
            Point point = Point.measurement("zj_cloud_host")
                    .tag("label", "mem")
                    .tag("uuid", uuid)
                    .tag("metricName", "MemoryFreeBytes")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("vm_type", type)
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("vm_metricName", "MemoryFreeBytes")
                    .addField("type", "MemoryFreeBytes")
                    .addField("value", Convert.toBigDecimal(NumberUtil.mul(sd, 1024)))
                    .time(ac.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }

        //==========================================硬盘==================================================
        List<PerMonitorDO> diskUsageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "disk.usage.average", platformconfigDTO);
        for (PerMonitorDO perMonitorDO : diskUsageList) {
            if (perMonitorDO.getInstance().isEmpty()) {
                perMonitorDO.setInstance("0");
            }


            BigDecimal st = NumberUtil.div(Convert.toBigDecimal(storage_usage), 8);
            BigDecimal value = NumberUtil.mul(st, 1024);
            Point statDiskUsage = Point.measurement("zj_cloud_host")
                    .tag("label", "disk")
                    .tag("uuid", uuid)
                    .tag("metricName", "DiskAllUsedCapacityInBytes")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("vm_type", "DiskAllUsedCapacityInBytes")
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("vm_metricName", "DiskAllUsedCapacityInBytes")
                    .addField("type", "DiskAllUsedCapacityInBytes")
                    .addField("value", value)
                    .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(statDiskUsage);

            Point inPercent = Point.measurement("zj_cloud_host")
                    .tag("label", "disk")
                    .tag("uuid", uuid)
                    .tag("metricName", "DiskAllUsedCapacityInPercent")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("vm_type", "DiskAllUsedCapacityInBytes")
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("vm_metricName", "DiskAllUsedCapacityInPercent")
                    .addField("type", "DiskAllUsedCapacityInPercent")
                    .addField("value", NumberUtil.mul(storage_rate, 100))
                    .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(inPercent);
        }


        List<PerMonitorDO> diskWriteList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "virtualDisk.write.average", platformconfigDTO);
        for (PerMonitorDO perMonitorDO : diskWriteList) {
            if (perMonitorDO.getInstance().isEmpty()) {
                perMonitorDO.setInstance("0");
            }
            String metricName = "DiskReadBytes";
            BigDecimal value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
            BigDecimal lastValue = NumberUtil.mul(value, 1024);
            Point point = Point.measurement("zj_cloud_host")
                    .tag("label", "disk")
                    .tag("uuid", uuid)
                    .tag("metricName", metricName)
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("vm_type", perMonitorDO.getInstance())
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("vm_metricName", metricName)
                    .addField("type", perMonitorDO.getInstance())
                    .addField("value", lastValue)
                    .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }

        List<PerMonitorDO> diskReadList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "virtualDisk.read.average", platformconfigDTO);
        if (!diskReadList.isEmpty()) {
            BigDecimal disk_write_all = new BigDecimal(0);
            for (PerMonitorDO perMonitorDO : diskReadList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                String metricName = "DiskWriteBytes";
                BigDecimal value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.mul(value, 1024);
                disk_write_all = disk_write_all.add(lastValue);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "disk")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
            Point point = Point.measurement("zj_cloud_host")
                    .tag("label", "disk")
                    .tag("uuid", uuid)
                    .tag("metricName", "DiskAllWriteBytes")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("vm_type", "DiskAllWriteBytes")
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("vm_metricName", "DiskAllWriteBytes")
                    .addField("type", "DiskAllWriteBytes")
                    .addField("value", disk_write_all)
                    .time(diskReadList.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }


        List<PerMonitorDO> iopsReadList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "virtualDisk.numberReadAveraged.average", platformconfigDTO);
        for (PerMonitorDO perMonitorDO : iopsReadList) {
            if (perMonitorDO.getInstance().isEmpty()) {
                perMonitorDO.setInstance("0");
            }
            String metricName = "DiskReadOps";
            Point point = Point.measurement("zj_cloud_host")
                    .tag("label", "disk")
                    .tag("uuid", uuid)
                    .tag("metricName", metricName)
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("vm_type", perMonitorDO.getInstance())
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("vm_metricName", metricName)
                    .addField("type", perMonitorDO.getInstance())
                    .addField("value", Convert.toBigDecimal(perMonitorDO.getValue()))
                    .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }

        List<PerMonitorDO> iopsWriteList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "virtualDisk.numberWriteAveraged.average", platformconfigDTO);
        if (!iopsWriteList.isEmpty()) {
            for (PerMonitorDO perMonitorDO : iopsWriteList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                String metricName = "DiskWriteOps";
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "disk")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", Convert.toBigDecimal(perMonitorDO.getValue()))
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }

        // ==========================================网络==================================================
        List<PerMonitorDO> netBytesRxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.bytesRx.average", platformconfigDTO);
        if (!netBytesRxAverageList.isEmpty()) {
            BigDecimal net_rx_all = new BigDecimal(0);
            for (PerMonitorDO perMonitorDO : netBytesRxAverageList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                String metricName = "NetworkInBytes";
                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.mul(value, 1024);
                net_rx_all = net_rx_all.add(lastValue);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "net")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }


            Point point = Point.measurement("zj_cloud_host")
                    .tag("label", "net")
                    .tag("uuid", uuid)
                    .tag("metricName", "NetworkAllInBytes")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("vm_type", "NetworkAllInBytes")
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("vm_metricName", "NetworkAllInBytes")
                    .addField("type", "NetworkAllInBytes")
                    .addField("value", NumberUtil.mul(NumberUtil.div(net_rx_all, 8), 1024))
                    .time(netBytesRxAverageList.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);

        }


        List<PerMonitorDO> netBytesTxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.bytesTx.average", platformconfigDTO);
        if (!netBytesTxAverageList.isEmpty()) {
            BigDecimal net_tx_all = new BigDecimal(0);
            for (PerMonitorDO perMonitorDO : netBytesTxAverageList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                String metricName = "NetworkOutBytes";
                BigDecimal value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.mul(value, 1024);
                net_tx_all = net_tx_all.add(lastValue);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "net")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }

            Point point = Point.measurement("zj_cloud_host")
                    .tag("label", "net")
                    .tag("uuid", uuid)
                    .tag("metricName", "NetworkAllOutBytes")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("vm_type", "NetworkAllOutBytes")
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("vm_metricName", "NetworkAllOutBytes")
                    .addField("type", "NetworkAllOutBytes")
                    .addField("value", NumberUtil.mul(NumberUtil.mul(net_tx_all, 8), 1024))
                    .time(netBytesTxAverageList.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);

        }

        List<PerMonitorDO> netPacketsRxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.packetsRx.summation", platformconfigDTO);
        if (!netPacketsRxAverageList.isEmpty()) {
            for (PerMonitorDO perMonitorDO : netPacketsRxAverageList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                String metricName = "NetworkInPackets";
                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.div(value, 1024);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "net")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }

        List<PerMonitorDO> netPacketsTxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.packetsTx.summation", platformconfigDTO);
        if (!netPacketsTxAverageList.isEmpty()) {
            for (PerMonitorDO perMonitorDO : netPacketsTxAverageList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                String metricName = "NetworkOutPackets";
                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.div(value, 1024);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "net")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }

        List<PerMonitorDO> netDroppedRxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.droppedRx.summation", platformconfigDTO);
        if (!netDroppedRxAverageList.isEmpty()) {
            for (PerMonitorDO perMonitorDO : netDroppedRxAverageList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                String metricName = "NetworkInErrors";
                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.div(value, 1024);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "net")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }

        List<PerMonitorDO> netDroppedTxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.droppedTx.summation", platformconfigDTO);
        if (!netDroppedTxAverageList.isEmpty()) {
            for (PerMonitorDO perMonitorDO : netDroppedTxAverageList) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                String metricName = "NetworkOutErrors";
                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.div(value, 1024);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "net")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }

        long eTime = System.currentTimeMillis(); // 结束时间
        long executionTime = eTime - startTime;
        double executionTimeInSeconds = executionTime / 1000.0;


    }

    private void collectMetricsForVm(ServiceInstance serviceInstance, PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BatchPoints batchPoints) throws Exception {
        long startTime = System.currentTimeMillis(); // 开始时间

        ManagedEntity managedEntities = new InventoryNavigator(serviceInstance.getRootFolder())
                .searchManagedEntity("VirtualMachine", hostInfoRespCreateReqDTO.getName());
        VirtualMachine virtualMachine = (VirtualMachine) managedEntities;


        if (virtualMachine == null) {
            System.out.println("Virtual Machine " + hostInfoRespCreateReqDTO.getName() + " cannot be found.");
            return;
        }

        Datastore[] datastores = virtualMachine.getDatastores();
        Long storage_total = 0L;
        Long storage_total_free = 0L;
        for (Datastore datastore : datastores) {
            storage_total += datastore.getSummary().getCapacity();
            storage_total_free += datastore.getSummary().getFreeSpace();
        }

        BigDecimal memSize = Convert.toBigDecimal(virtualMachine.getSummary().getConfig().getMemorySizeMB());
        BigDecimal storage_usage = NumberUtil.sub(storage_total, storage_total_free);
        BigDecimal storage_rate = NumberUtil.div(storage_total_free, storage_total);
        String uuid = hostInfoRespCreateReqDTO.getUuid();

        // Collect all required metrics in a single call
        List<String> counterNames = Arrays.asList(
                "cpu.usage.none", "cpu.usage.average", "cpu.idle.summation", "cpu.utilization.average",
                "mem.usage.average", "mem.active.average", "mem.active.average",
                "disk.usage.average", "virtualDisk.write.average", "virtualDisk.read.average", "virtualDisk.numberReadAveraged.average", "virtualDisk.numberWriteAveraged.average",
                "net.bytesTx.average", "net.bytesRx.average", "net.packetsRx.summation", "net.packetsTx.summation", "net.droppedRx.summation", "net.droppedTx.summation"
        );
        Map<String, List<PerMonitorDO>> metrics = RealtimePerfMonitor.getPerEntityMetricsByNamesTest(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, counterNames);

        // Process each metric and add to batch points
        processMetrics(metrics, "CPUUsedUtilization", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "cpu.usage.none");
        processMetrics(metrics, "CPUAverageUsedUtilization", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "cpu.usage.average");
        processMetrics(metrics, "CPUIdleUtilization", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "cpu.idle.summation");
        processMetrics(metrics, "CPUSystemUtilization", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "cpu.utilization.average");
        processCPUWaitUtilization(metrics, uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints);

        // ================================================内存====================================
        processMemoryMetrics(metrics, "MemoryUsedInPercent", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "mem.usage.average", memSize);
        processMemoryUsedBytes(metrics, "MemoryUsedBytes", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "mem.active.average", memSize);
        processMemoryFreeBytes(metrics, uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "mem.active.average", memSize);
        // ==============================================磁盘========================================
        processDiskAllMetrics(metrics, "DiskAllUsedCapacityInBytes", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "disk.usage.average", storage_usage, storage_rate);
        processDiskWriteMetrics(metrics, "DiskWriteBytes", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "virtualDisk.write.average");
        processDiskReadMetrics(metrics, "DiskReadBytes", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "virtualDisk.read.average");
        processDiskIopsMetrics(metrics, "DiskReadOps", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "virtualDisk.numberReadAveraged.average");
        processDiskIopsMetrics(metrics, "DiskWriteOps", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "virtualDisk.numberWriteAveraged.average");
        // ==============================================网络===========================================
        processNetBytesRxAverageMetrics(metrics, "NetworkOutBytes", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "net.bytesTx.average");
        processNetBytesRxAverageMetrics(metrics, "NetworkInBytes", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "net.bytesRx.average");
        processNetPacketsAverageMetrics(metrics, "NetworkInPackets", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "net.packetsRx.summation");
        processNetPacketsAverageMetrics(metrics, "NetworkOutPackets", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "net.packetsTx.summation");
        processNetPacketsAverageMetrics(metrics, "NetworkInErrors", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "net.droppedRx.summation");
        processNetPacketsAverageMetrics(metrics, "NetworkOutErrors", uuid, platformconfigDTO, hostInfoRespCreateReqDTO, batchPoints, "net.droppedTx.summation");

        //==========================================硬盘==================================================
//        List<PerMonitorDO> diskUsageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "disk.usage.average", platformconfigDTO);
//        for (PerMonitorDO perMonitorDO : diskUsageList) {
//            if (perMonitorDO.getInstance().isEmpty()) {
//                perMonitorDO.setInstance("0");
//            }
//
//
//            BigDecimal st = NumberUtil.div(Convert.toBigDecimal(storage_usage), 8);
//            BigDecimal value = NumberUtil.mul(st, 1024);
//            Point statDiskUsage = Point.measurement("zj_cloud_host")
//                    .tag("label", "disk")
//                    .tag("uuid", uuid)
//                    .tag("metricName", "DiskAllUsedCapacityInBytes")
//                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                    .tag("vm_type", "DiskAllUsedCapacityInBytes")
//                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                    .addField("vm_metricName", "DiskAllUsedCapacityInBytes")
//                    .addField("type", "DiskAllUsedCapacityInBytes")
//                    .addField("value", value)
//                    .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                    .build();
//            batchPoints.point(statDiskUsage);
//
//            Point inPercent = Point.measurement("zj_cloud_host")
//                    .tag("label", "disk")
//                    .tag("uuid", uuid)
//                    .tag("metricName", "DiskAllUsedCapacityInPercent")
//                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                    .tag("vm_type", "DiskAllUsedCapacityInBytes")
//                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                    .addField("vm_metricName", "DiskAllUsedCapacityInPercent")
//                    .addField("type", "DiskAllUsedCapacityInPercent")
//                    .addField("value", NumberUtil.mul(storage_rate, 100))
//                    .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                    .build();
//            batchPoints.point(inPercent);
//        }
//
//
//        List<PerMonitorDO> diskWriteList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "virtualDisk.write.average", platformconfigDTO);
//        for (PerMonitorDO perMonitorDO : diskWriteList) {
//            if (perMonitorDO.getInstance().isEmpty()) {
//                perMonitorDO.setInstance("0");
//            }
//            String metricName = "DiskReadBytes";
//            BigDecimal value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
//            BigDecimal lastValue = NumberUtil.mul(value, 1024);
//            Point point = Point.measurement("zj_cloud_host")
//                    .tag("label", "disk")
//                    .tag("uuid", uuid)
//                    .tag("metricName", metricName)
//                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                    .tag("vm_type", perMonitorDO.getInstance())
//                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                    .addField("vm_metricName", metricName)
//                    .addField("type", perMonitorDO.getInstance())
//                    .addField("value", lastValue)
//                    .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                    .build();
//            batchPoints.point(point);
//        }
//
//        List<PerMonitorDO> diskReadList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "virtualDisk.read.average", platformconfigDTO);
//        if (!diskReadList.isEmpty()) {
//            BigDecimal disk_write_all = new BigDecimal(0);
//            for (PerMonitorDO perMonitorDO : diskReadList) {
//                if (perMonitorDO.getInstance().isEmpty()) {
//                    perMonitorDO.setInstance("0");
//                }
//                String metricName = "DiskWriteBytes";
//                BigDecimal value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
//                BigDecimal lastValue = NumberUtil.mul(value, 1024);
//                disk_write_all = disk_write_all.add(lastValue);
//                Point point = Point.measurement("zj_cloud_host")
//                        .tag("label", "disk")
//                        .tag("uuid", uuid)
//                        .tag("metricName", metricName)
//                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                        .tag("vm_type", perMonitorDO.getInstance())
//                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                        .addField("vm_metricName", metricName)
//                        .addField("type", perMonitorDO.getInstance())
//                        .addField("value", lastValue)
//                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                        .build();
//                batchPoints.point(point);
//            }
//            Point point = Point.measurement("zj_cloud_host")
//                    .tag("label", "disk")
//                    .tag("uuid", uuid)
//                    .tag("metricName", "DiskAllWriteBytes")
//                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                    .tag("vm_type", "DiskAllWriteBytes")
//                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                    .addField("vm_metricName", "DiskAllWriteBytes")
//                    .addField("type", "DiskAllWriteBytes")
//                    .addField("value", disk_write_all)
//                    .time(diskReadList.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                    .build();
//            batchPoints.point(point);
//        }
//
//
//        List<PerMonitorDO> iopsReadList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "virtualDisk.numberReadAveraged.average", platformconfigDTO);
//        for (PerMonitorDO perMonitorDO : iopsReadList) {
//            if (perMonitorDO.getInstance().isEmpty()) {
//                perMonitorDO.setInstance("0");
//            }
//            String metricName = "DiskReadOps";
//            Point point = Point.measurement("zj_cloud_host")
//                    .tag("label", "disk")
//                    .tag("uuid", uuid)
//                    .tag("metricName", metricName)
//                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                    .tag("vm_type", perMonitorDO.getInstance())
//                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                    .addField("vm_metricName", metricName)
//                    .addField("type", perMonitorDO.getInstance())
//                    .addField("value", Convert.toBigDecimal(perMonitorDO.getValue()))
//                    .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                    .build();
//            batchPoints.point(point);
//        }
//
//        List<PerMonitorDO> iopsWriteList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "virtualDisk.numberWriteAveraged.average", platformconfigDTO);
//        if (!iopsWriteList.isEmpty()) {
//            for (PerMonitorDO perMonitorDO : iopsWriteList) {
//                if (perMonitorDO.getInstance().isEmpty()) {
//                    perMonitorDO.setInstance("0");
//                }
//                String metricName = "DiskWriteOps";
//                Point point = Point.measurement("zj_cloud_host")
//                        .tag("label", "disk")
//                        .tag("uuid", uuid)
//                        .tag("metricName", metricName)
//                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                        .tag("vm_type", perMonitorDO.getInstance())
//                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                        .addField("vm_metricName", metricName)
//                        .addField("type", perMonitorDO.getInstance())
//                        .addField("value", Convert.toBigDecimal(perMonitorDO.getValue()))
//                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                        .build();
//                batchPoints.point(point);
//            }
//        }
//
//        // ==========================================网络==================================================
//        List<PerMonitorDO> netBytesRxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.bytesRx.average", platformconfigDTO);
//        if (!netBytesRxAverageList.isEmpty()) {
//            BigDecimal net_rx_all = new BigDecimal(0);
//            for (PerMonitorDO perMonitorDO : netBytesRxAverageList) {
//                if (perMonitorDO.getInstance().isEmpty()) {
//                    perMonitorDO.setInstance("0");
//                }
//                String metricName = "NetworkInBytes";
//                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
//                BigDecimal lastValue = NumberUtil.mul(value, 1024);
//                net_rx_all = net_rx_all.add(lastValue);
//                Point point = Point.measurement("zj_cloud_host")
//                        .tag("label", "net")
//                        .tag("uuid", uuid)
//                        .tag("metricName", metricName)
//                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                        .tag("vm_type", perMonitorDO.getInstance())
//                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                        .addField("vm_metricName", metricName)
//                        .addField("type", perMonitorDO.getInstance())
//                        .addField("value", lastValue)
//                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                        .build();
//                batchPoints.point(point);
//            }
//
//
//            Point point = Point.measurement("zj_cloud_host")
//                    .tag("label", "net")
//                    .tag("uuid", uuid)
//                    .tag("metricName", "NetworkAllInBytes")
//                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                    .tag("vm_type", "NetworkAllInBytes")
//                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                    .addField("vm_metricName", "NetworkAllInBytes")
//                    .addField("type", "NetworkAllInBytes")
//                    .addField("value", NumberUtil.mul(NumberUtil.div(net_rx_all, 8), 1024))
//                    .time(netBytesRxAverageList.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                    .build();
//            batchPoints.point(point);
//
//        }
//
//
//        List<PerMonitorDO> netBytesTxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.bytesTx.average", platformconfigDTO);
//        if (!netBytesTxAverageList.isEmpty()) {
//            BigDecimal net_tx_all = new BigDecimal(0);
//            for (PerMonitorDO perMonitorDO : netBytesTxAverageList) {
//                if (perMonitorDO.getInstance().isEmpty()) {
//                    perMonitorDO.setInstance("0");
//                }
//                String metricName = "NetworkOutBytes";
//                BigDecimal value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
//                BigDecimal lastValue = NumberUtil.mul(value, 1024);
//                net_tx_all = net_tx_all.add(lastValue);
//                Point point = Point.measurement("zj_cloud_host")
//                        .tag("label", "net")
//                        .tag("uuid", uuid)
//                        .tag("metricName", metricName)
//                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                        .tag("vm_type", perMonitorDO.getInstance())
//                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                        .addField("vm_metricName", metricName)
//                        .addField("type", perMonitorDO.getInstance())
//                        .addField("value", lastValue)
//                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                        .build();
//                batchPoints.point(point);
//            }
//
//            Point point = Point.measurement("zj_cloud_host")
//                    .tag("label", "net")
//                    .tag("uuid", uuid)
//                    .tag("metricName", "NetworkAllOutBytes")
//                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                    .tag("vm_type", "NetworkAllOutBytes")
//                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                    .addField("vm_metricName", "NetworkAllOutBytes")
//                    .addField("type", "NetworkAllOutBytes")
//                    .addField("value", NumberUtil.mul(NumberUtil.mul(net_tx_all, 8), 1024))
//                    .time(netBytesTxAverageList.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                    .build();
//            batchPoints.point(point);
//
//        }
//
//        List<PerMonitorDO> netPacketsRxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.packetsRx.summation", platformconfigDTO);
//        if (!netPacketsRxAverageList.isEmpty()) {
//            for (PerMonitorDO perMonitorDO : netPacketsRxAverageList) {
//                if (perMonitorDO.getInstance().isEmpty()) {
//                    perMonitorDO.setInstance("0");
//                }
//                String metricName = "NetworkInPackets";
//                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
//                BigDecimal lastValue = NumberUtil.div(value, 1024);
//                Point point = Point.measurement("zj_cloud_host")
//                        .tag("label", "net")
//                        .tag("uuid", uuid)
//                        .tag("metricName", metricName)
//                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                        .tag("vm_type", perMonitorDO.getInstance())
//                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                        .addField("vm_metricName", metricName)
//                        .addField("type", perMonitorDO.getInstance())
//                        .addField("value", lastValue)
//                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                        .build();
//                batchPoints.point(point);
//            }
//        }
//
//        List<PerMonitorDO> netPacketsTxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.packetsTx.summation", platformconfigDTO);
//        if (!netPacketsTxAverageList.isEmpty()) {
//            for (PerMonitorDO perMonitorDO : netPacketsTxAverageList) {
//                if (perMonitorDO.getInstance().isEmpty()) {
//                    perMonitorDO.setInstance("0");
//                }
//                String metricName = "NetworkOutPackets";
//                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
//                BigDecimal lastValue = NumberUtil.div(value, 1024);
//                Point point = Point.measurement("zj_cloud_host")
//                        .tag("label", "net")
//                        .tag("uuid", uuid)
//                        .tag("metricName", metricName)
//                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                        .tag("vm_type", perMonitorDO.getInstance())
//                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                        .addField("vm_metricName", metricName)
//                        .addField("type", perMonitorDO.getInstance())
//                        .addField("value", lastValue)
//                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                        .build();
//                batchPoints.point(point);
//            }
//        }
//
//        List<PerMonitorDO> netDroppedRxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.droppedRx.summation", platformconfigDTO);
//        if (!netDroppedRxAverageList.isEmpty()) {
//            for (PerMonitorDO perMonitorDO : netDroppedRxAverageList) {
//                if (perMonitorDO.getInstance().isEmpty()) {
//                    perMonitorDO.setInstance("0");
//                }
//                String metricName = "NetworkInErrors";
//                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
//                BigDecimal lastValue = NumberUtil.div(value, 1024);
//                Point point = Point.measurement("zj_cloud_host")
//                        .tag("label", "net")
//                        .tag("uuid", uuid)
//                        .tag("metricName", metricName)
//                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                        .tag("vm_type", perMonitorDO.getInstance())
//                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                        .addField("vm_metricName", metricName)
//                        .addField("type", perMonitorDO.getInstance())
//                        .addField("value", lastValue)
//                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                        .build();
//                batchPoints.point(point);
//            }
//        }
//
//        List<PerMonitorDO> netDroppedTxAverageList = RealtimePerfMonitor.getPerEntityMericBasesByname(hostInfoRespCreateReqDTO.getName(), serviceInstance, managedEntities, "net.droppedTx.summation", platformconfigDTO);
//        if (!netDroppedTxAverageList.isEmpty()) {
//            for (PerMonitorDO perMonitorDO : netDroppedTxAverageList) {
//                if (perMonitorDO.getInstance().isEmpty()) {
//                    perMonitorDO.setInstance("0");
//                }
//                String metricName = "NetworkOutErrors";
//                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
//                BigDecimal lastValue = NumberUtil.div(value, 1024);
//                Point point = Point.measurement("zj_cloud_host")
//                        .tag("label", "net")
//                        .tag("uuid", uuid)
//                        .tag("metricName", metricName)
//                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
//                        .tag("vm_type", perMonitorDO.getInstance())
//                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
//
//                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
//                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
//                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
//                        .addField("vm_metricName", metricName)
//                        .addField("type", perMonitorDO.getInstance())
//                        .addField("value", lastValue)
//                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
//                        .build();
//                batchPoints.point(point);
//            }
//        }

        long eTime = System.currentTimeMillis(); // 结束时间
        long executionTime = eTime - startTime;
        double executionTimeInSeconds = executionTime / 1000.0;
//        System.out.println("一个虚拟机采集执行时间: " + executionTimeInSeconds + " seconds");
    }


    private void processMetrics(Map<String, List<PerMonitorDO>> metrics, String metricName, String uuid, PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BatchPoints batchPoints, String counterName) {
        List<PerMonitorDO> metricData = metrics.get(counterName);
        if (metricData != null && !metricData.isEmpty()) {
            Long allcpu = 0L;
            for (PerMonitorDO perMonitorDO : metricData) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                BigDecimal bigDecimal = new BigDecimal(0);
                if (metricName.equals("CPUUsedUtilization") || metricName.equals("CPUAverageUsedUtilization")) {
                    allcpu += perMonitorDO.getValue();
                    bigDecimal = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 100);
                } else {
                    bigDecimal = Convert.toBigDecimal(perMonitorDO.getValue());
                }
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "cpu")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", bigDecimal)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }

            if (metricName.equals("CPUUsedUtilization")) {
                BigDecimal v = NumberUtil.div(Convert.toBigDecimal(allcpu), metricData.size());
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "cpu")
                        .tag("uuid", uuid)
                        .tag("metricName", "CPUAllUsedUtilization")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", "CPUAllUsedUtilization")
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", "CPUAllUsedUtilization")
                        .addField("type", "CPUAllUsedUtilization")
                        .addField("value", Convert.toBigDecimal(NumberUtil.div(v, 100, 2)))
                        .time(metricData.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }
    }

    private void processCPUWaitUtilization(Map<String, List<PerMonitorDO>> metrics, String uuid, PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BatchPoints batchPoints) {
        List<PerMonitorDO> cpuUsedList = metrics.get("cpu.used.summation");
        List<PerMonitorDO> cpuWaitList = metrics.get("cpu.wait.summation");
        if (cpuUsedList != null && cpuWaitList != null && !cpuUsedList.isEmpty() && !cpuWaitList.isEmpty()) {
            for (int i = 0; i < cpuUsedList.size(); i++) {
                PerMonitorDO usedDO = cpuUsedList.get(i);
                PerMonitorDO waitDO = cpuWaitList.get(i);
                if (usedDO.getInstance().isEmpty()) {
                    usedDO.setInstance("0");
                }
                BigDecimal waitUsage = NumberUtil.div(waitDO.getValue(), NumberUtil.add(usedDO.getValue(), waitDO.getValue()), 5);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "cpu")
                        .tag("uuid", uuid)
                        .tag("metricName", "CPUWaitUtilization")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", waitDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", "CPUWaitUtilization")
                        .addField("type", waitDO.getInstance())
                        .addField("value", waitUsage)
                        .time(waitDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }
    }

    private void processMemoryMetrics(Map<String, List<PerMonitorDO>> metrics, String metricName, String uuid, PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BatchPoints batchPoints, String counterName, BigDecimal memSize) {
        List<PerMonitorDO> metricData = metrics.get(counterName);
        if (metricData != null && !metricData.isEmpty()) {
            for (PerMonitorDO perMonitorDO : metricData) {
                BigDecimal value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 100);
                String label = perMonitorDO.getInstance();
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "mem")
                        .tag("uuid", uuid)
                        .tag("metricName", "MemoryUsedInPercent")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", "MemoryUsedInPercent")
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", "MemoryUsedInPercent")
                        .addField("type", "MemoryUsedInPercent")
                        .addField("value", value)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }
    }

    private void processMemoryUsedBytes(Map<String, List<PerMonitorDO>> metrics, String metricName, String uuid, PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BatchPoints batchPoints, String counterName, BigDecimal memSize) {
        List<PerMonitorDO> metricData = metrics.get(counterName);
        if (metricData != null && !metricData.isEmpty()) {
            for (PerMonitorDO perMonitorDO : metricData) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }

                String label = perMonitorDO.getInstance();
                String type = label + "MemoryUsedBytes";

                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "mem")
                        .tag("uuid", uuid)
                        .tag("metricName", "MemoryUsedBytes")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", type)
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", "MemoryUsedBytes")
                        .addField("type", "MemoryUsedBytes")
                        .addField("value", Convert.toBigDecimal(NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 1024)))
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }
    }

    private void processMemoryFreeBytes(Map<String, List<PerMonitorDO>> metrics, String uuid, PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BatchPoints batchPoints, String counterName, BigDecimal memSize) {
        List<PerMonitorDO> metricData = metrics.get(counterName);
        if (metricData != null && !metricData.isEmpty()) {
            for (PerMonitorDO ac : metricData) {
                BigDecimal sd = NumberUtil.sub(NumberUtil.mul(memSize, 1024), Convert.toBigDecimal(ac.getValue()));
                String label = ac.getInstance();
                String type = label + "MemoryFreeBytes";
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "mem")
                        .tag("uuid", uuid)
                        .tag("metricName", "MemoryFreeBytes")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", type)
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", "MemoryFreeBytes")
                        .addField("type", "MemoryFreeBytes")
                        .addField("value", Convert.toBigDecimal(NumberUtil.mul(sd, 1024)))
                        .time(ac.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }
    }

    private void processDiskAllMetrics(Map<String, List<PerMonitorDO>> metrics, String metricName, String uuid, PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BatchPoints batchPoints, String counterName, BigDecimal storage_usage, BigDecimal storage_rate) {
        List<PerMonitorDO> metricData = metrics.get(counterName);
        if (metricData != null && !metricData.isEmpty()) {
            for (PerMonitorDO perMonitorDO : metricData) {
                String label = perMonitorDO.getInstance();
                BigDecimal st = NumberUtil.div(Convert.toBigDecimal(storage_usage), 8);
                BigDecimal value = NumberUtil.mul(st, 1024);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "disk")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", metricName)
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))
                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", metricName)
                        .addField("value", value)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);

                Point inPercent = Point.measurement("zj_cloud_host")
                        .tag("label", "disk")
                        .tag("uuid", uuid)
                        .tag("metricName", "DiskAllUsedCapacityInPercent")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", "DiskAllUsedCapacityInBytes")
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", "DiskAllUsedCapacityInPercent")
                        .addField("type", "DiskAllUsedCapacityInPercent")
                        .addField("value", NumberUtil.mul(storage_rate, 100))
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(inPercent);
            }
        }
    }

    private void processDiskWriteMetrics(Map<String, List<PerMonitorDO>> metrics, String metricName, String uuid, PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BatchPoints batchPoints, String counterName) {
        List<PerMonitorDO> metricData = metrics.get(counterName);
        BigDecimal disk_write_all = new BigDecimal(0);
        if (metricData != null && !metricData.isEmpty()) {
            for (PerMonitorDO perMonitorDO : metricData) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                BigDecimal value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.mul(value, 1024);

                disk_write_all = disk_write_all.add(lastValue);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "disk")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
            Point point = Point.measurement("zj_cloud_host")
                    .tag("label", "disk")
                    .tag("uuid", uuid)
                    .tag("metricName", "DiskAllWriteBytes")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("vm_type", "DiskAllWriteBytes")
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("vm_metricName", "DiskAllWriteBytes")
                    .addField("type", "DiskAllWriteBytes")
                    .addField("value", disk_write_all)
                    .time(metricData.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);

        }
    }

    private void processDiskReadMetrics(Map<String, List<PerMonitorDO>> metrics, String metricName, String uuid, PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BatchPoints batchPoints, String counterName) {
        List<PerMonitorDO> metricData = metrics.get(counterName);
        BigDecimal disk_read_all = new BigDecimal(0);
        if (metricData != null && !metricData.isEmpty()) {
            for (PerMonitorDO perMonitorDO : metricData) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                BigDecimal value = new BigDecimal(0);
                if (perMonitorDO.getValue() > 0) {
                    value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                }
                BigDecimal lastValue = NumberUtil.mul(value, 1024);
                disk_read_all = disk_read_all.add(lastValue);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "disk")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", Convert.toBigDecimal(perMonitorDO.getValue()))
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);

            }

            Point point = Point.measurement("zj_cloud_host")
                    .tag("label", "disk")
                    .tag("uuid", uuid)
                    .tag("metricName", "DiskAllReadBytes")
                    .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                    .tag("vm_type", "DiskAllReadBytes")
                    .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                    .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                    .addField("productsName", hostInfoRespCreateReqDTO.getName())
                    .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                    .addField("vm_metricName", "DiskAllReadBytes")
                    .addField("type", "DiskAllReadBytes")
                    .addField("value", disk_read_all)
                    .time(metricData.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }
    }

    private void processDiskIopsMetrics(Map<String, List<PerMonitorDO>> metrics, String metricName, String uuid, PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BatchPoints batchPoints, String counterName) {
        List<PerMonitorDO> metricData = metrics.get(counterName);
        if (metricData != null && !metricData.isEmpty()) {
            for (PerMonitorDO perMonitorDO : metricData) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "disk")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", Convert.toBigDecimal(perMonitorDO.getValue()))
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }
    }

    private void processNetBytesRxAverageMetrics(Map<String, List<PerMonitorDO>> metrics, String metricName, String uuid, PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BatchPoints batchPoints, String counterName) {
        List<PerMonitorDO> metricData = metrics.get(counterName);
        if (metricData != null && !metricData.isEmpty()) {
            BigDecimal net_tx_all = new BigDecimal(0);
            for (PerMonitorDO perMonitorDO : metricData) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }

                BigDecimal value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.mul(value, 1024);
                net_tx_all = net_tx_all.add(lastValue);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "net")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
            if (metricName.equals("NetworkOutBytes")) {
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "net")
                        .tag("uuid", uuid)
                        .tag("metricName", "NetworkAllOutBytes")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", "NetworkAllOutBytes")
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", "NetworkAllOutBytes")
                        .addField("type", "NetworkAllOutBytes")
                        .addField("value", NumberUtil.mul(NumberUtil.mul(net_tx_all, 8), 1024))
                        .time(metricData.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            } else {
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "net")
                        .tag("uuid", uuid)
                        .tag("metricName", "NetworkAllInBytes")
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", "NetworkAllOutBytes")
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", "NetworkAllInBytes")
                        .addField("type", "NetworkAllInBytes")
                        .addField("value", NumberUtil.mul(NumberUtil.mul(net_tx_all, 8), 1024))
                        .time(metricData.get(0).getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }

        }
    }

    private void processNetPacketsAverageMetrics(Map<String, List<PerMonitorDO>> metrics, String metricName, String uuid, PlatformconfigDTO platformconfigDTO, HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO, BatchPoints batchPoints, String counterName) {
        List<PerMonitorDO> metricData = metrics.get(counterName);
        if (metricData != null && !metricData.isEmpty()) {
            BigDecimal net_tx_all = new BigDecimal(0);
            for (PerMonitorDO perMonitorDO : metricData) {
                if (perMonitorDO.getInstance().isEmpty()) {
                    perMonitorDO.setInstance("0");
                }
                BigDecimal value = NumberUtil.mul(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                BigDecimal lastValue = NumberUtil.div(value, 1024);
                Point point = Point.measurement("zj_cloud_host")
                        .tag("label", "net")
                        .tag("uuid", uuid)
                        .tag("metricName", metricName)
                        .tag("regionId", StringUtil.toString(platformconfigDTO.getRegionId()))
                        .tag("vm_type", perMonitorDO.getInstance())
                        .tag("platformId", StringUtil.toString(platformconfigDTO.getId()))

                        .addField("regionName", StringUtil.toString(platformconfigDTO.getRegionName()))
                        .addField("productsName", hostInfoRespCreateReqDTO.getName())
                        .addField("platformName", StringUtil.toString(platformconfigDTO.getName()))
                        .addField("vm_metricName", metricName)
                        .addField("type", perMonitorDO.getInstance())
                        .addField("value", lastValue)
                        .time(perMonitorDO.getDateTime().getTime() / 1000, TimeUnit.SECONDS)
                        .build();
                batchPoints.point(point);
            }
        }
    }

    @NotNull
    private static Map<String, ManagedEntity> getStringManagedEntityMap(ServiceInstance serviceInstance, HostInfoRespCreateReqDTO hostInfoApiVmByPlatformId1) throws RemoteException {
        InventoryNavigator navigator = new InventoryNavigator(serviceInstance.getRootFolder());
        Map<String, ManagedEntity> vmEntities = new HashMap<>();

        // 批量获取所有虚拟机的实体
        ManagedEntity managedEntity = navigator.searchManagedEntity("VirtualMachine", hostInfoApiVmByPlatformId1.getName());
        if (managedEntity != null) {
            vmEntities.put(hostInfoApiVmByPlatformId1.getName(), managedEntity);
        }
        return vmEntities;
    }

    private static boolean isVersionLessThan(String version1, String version2) {
        String[] v1Parts = version1.split("\\.");
        String[] v2Parts = version2.split("\\.");

        int length = Math.max(v1Parts.length, v2Parts.length);

        for (int i = 0; i < length; i++) {
            int v1 = i < v1Parts.length ? Integer.parseInt(v1Parts[i]) : 0;
            int v2 = i < v2Parts.length ? Integer.parseInt(v2Parts[i]) : 0;

            if (v1 < v2) {
                return true;
            } else if (v1 > v2) {
                return false;
            }
        }

        return false;
    }
}
