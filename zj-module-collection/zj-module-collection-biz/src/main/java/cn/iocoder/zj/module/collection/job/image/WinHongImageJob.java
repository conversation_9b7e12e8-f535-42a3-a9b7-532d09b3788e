package cn.iocoder.zj.module.collection.job.image;

import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.winhong.WinHongAccessTokenRedisDAO;
import cn.iocoder.zj.module.collection.service.cloud.IWinHongDeviceService;
import cn.iocoder.zj.module.monitor.api.imageinfo.ImageInfoApi;
import cn.iocoder.zj.module.monitor.api.imageinfo.dto.ImageInfoCreateReqDTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class WinHongImageJob {

    @Autowired
    private PlatformRedisDAO platformRedisDAO;
    @Autowired
    private PlatformconfigApi platformconfigApi;
    @Autowired
    private WinHongAccessTokenRedisDAO winHongAccessTokenRedisDAO;
    @Resource
    ImageInfoApi imageInfoApi;

    @Resource
    IWinHongDeviceService winHongDeviceService;
    @XxlJob("getWinHongImage")
    public void getWinHongImage()
    {
        List<ImageInfoCreateReqDTO> imageInfoDTOs = new ArrayList<>();
        List<PlatformconfigDTO> platformconfigDTOList = new ArrayList<>();
        if (platformRedisDAO.get("platform") == null) {
            platformconfigDTOList = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform", platformconfigDTOList);
        }
        platformconfigDTOList = platformRedisDAO.get("platform");
        if (!platformconfigDTOList.isEmpty()) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                if (p.getTypeCode().equals("winhong") && winHongAccessTokenRedisDAO.get("winhong:" + p.getId()) != null) {
                    String token = winHongDeviceService.getToken(p);
                    if (token == null) {
                        log.info("获取云宏token失败");
                        return;
                    }
                    JSONArray imagesJson = winHongDeviceService.getImages(p, token);
                    if (imagesJson != null && !imagesJson.isEmpty()){
                        List<Map> images = JSONObject.parseArray(imagesJson.toJSONString(), Map.class);
                        for (Map image : images) {
                            ImageInfoCreateReqDTO imageInfoDTO = new ImageInfoCreateReqDTO();
                            imageInfoDTO.setUuid(image.get("id").toString());
                            imageInfoDTO.setName(image.get("name").toString());
                            imageInfoDTO.setStatus("Enabled");
                            imageInfoDTO.setFormat("qcow2");
                            //cpu架构 1.32位 2.64位 3.aarch64 4.mips64el
                            switch (image.get("cpuArch").toString()){
                                case "1":
                                    imageInfoDTO.setCpuArch("x86_32");
                                    break;
                                case "2":
                                    imageInfoDTO.setCpuArch("x86_64");
                                    break;
                                case "3":
                                    imageInfoDTO.setCpuArch("aarch64");
                                    break;
                                case "4":
                                    imageInfoDTO.setCpuArch("mips64el");
                                    break;
                               default:
                                   break;
                            }
                            //系统类型 0.其他 1.linux 2.windows
                            switch (image.get("osType").toString()){
                                case "0":
                                    imageInfoDTO.setOsType("Other");
                                    imageInfoDTO.setApplicationPlatform("Other");
                                    break;
                                case "1":
                                    imageInfoDTO.setOsType("Linux");
                                    imageInfoDTO.setApplicationPlatform("Linux");
                                    break;
                                case "2":
                                    imageInfoDTO.setOsType("Windows");
                                    imageInfoDTO.setApplicationPlatform("Windows");
                                    break;
                                default:
                                    break;
                            }
                            //镜像大小GB转byte
                            imageInfoDTO.setSize(Long.parseLong(image.get("deviceDiskTotalCapacity").toString()));
                            //镜像类型
                            imageInfoDTO.setImageType("RootVolumeTemplate");
                            //共享范围
                            switch (image.get("share").toString()){
                                case "1":
                                    imageInfoDTO.setSharingScope("不共享");
                                    break;
                                case "2":
                                    imageInfoDTO.setSharingScope("共享");
                                    break;
                                default:
                                    break;
                            }
                            JSONArray imagesStartOperationLogs = winHongDeviceService.getImagesOperationLogs(p,image.get("id").toString(), "asc", 1, 1, token);
                            if (imagesStartOperationLogs != null && !imagesStartOperationLogs.isEmpty()){
                                List<Map> imagesOperationLogsList = JSONObject.parseArray(imagesStartOperationLogs.toJSONString(), Map.class);
                                Long operateTime = (Long) imagesOperationLogsList.get(0).get("operateTime");
                                LocalDateTime operateTimeL = LocalDateTime.ofInstant(Instant.ofEpochMilli(operateTime), ZoneId.systemDefault());
                                imageInfoDTO.setVCreateDate(operateTimeL);

                            }
                            JSONArray imagesEndOperationLogs = winHongDeviceService.getImagesOperationLogs(p,image.get("id").toString(), "desc", 1, 1, token);
                            if (imagesEndOperationLogs!= null &&!imagesEndOperationLogs.isEmpty()){
                                List<Map> imagesOperationLogsList = JSONObject.parseArray(imagesEndOperationLogs.toJSONString(), Map.class);
                                Long operateTime = (Long) imagesOperationLogsList.get(imagesOperationLogsList.size() - 1).get("operateTime");
                                LocalDateTime operateTimeL = LocalDateTime.ofInstant(Instant.ofEpochMilli(operateTime), ZoneId.systemDefault());
                                imageInfoDTO.setVUpdateDate(operateTimeL);
                            }
                            imageInfoDTO.setOsLanguage("");
                            imageInfoDTO.setMinMemory(BigDecimal.valueOf(Long.parseLong(image.get("memory").toString())));
                            imageInfoDTO.setMinDisk(BigDecimal.valueOf(Long.parseLong(image.get("deviceDiskTotalCapacity").toString())));
                            imageInfoDTO.setDiskDriver("");
                            imageInfoDTO.setNetworkDriver("");
                            imageInfoDTO.setBootMode("");
                            imageInfoDTO.setRemoteProtocol("");
                            imageInfoDTO.setPlatformId(p.getId());
                            imageInfoDTO.setPlatformName(p.getName());
                            imageInfoDTO.setDeleted(0L);
                            imageInfoDTOs.add(imageInfoDTO);
                        }
                    }
                }
            }
        }
        if (!imageInfoDTOs.isEmpty()){
            List<ImageInfoCreateReqDTO> imageInfoDTOList = imageInfoApi.getAllImagesByTypeCode("winhong").getData();
            if (imageInfoDTOList == null || imageInfoDTOList.isEmpty()) {
                imageInfoApi.batchCreateImageInfo(imageInfoDTOs);
            }else {
                //比较uuid不存在删除
                List<ImageInfoCreateReqDTO> deleteTarget = imageInfoDTOList.stream()
                        .filter(imageInfoDTO -> imageInfoDTOs.stream()
                                .noneMatch(item -> item.getUuid().equals(imageInfoDTO.getUuid())))
                        .collect(Collectors.toList());
                //新增镜像信息
                List<ImageInfoCreateReqDTO> collect = imageInfoDTOs.stream()
                        .filter(imageInfoDTO -> imageInfoDTOList.stream()
                                .noneMatch(item -> item.getUuid().equals(imageInfoDTO.getUuid())))
                        .collect(Collectors.toList());
                //更新镜像信息
                List<ImageInfoCreateReqDTO> updateDTOs = imageInfoDTOs.stream()
                        .filter(imageInfoDTO -> imageInfoDTOList.stream()
                                .anyMatch(item -> item.getUuid().equals(imageInfoDTO.getUuid())))
                        .toList();
                //去掉uuid不存在的collect
                if (!collect.isEmpty()){
                    imageInfoApi.batchCreateImageInfo(collect);
                }
                if (!updateDTOs.isEmpty()){
                    imageInfoApi.batchUpdateImageInfo(updateDTOs);
                }
                if (!deleteTarget.isEmpty()) {
                    imageInfoApi.batchDeleteImageInfo(deleteTarget);
                }
            }
        }
    }
}
