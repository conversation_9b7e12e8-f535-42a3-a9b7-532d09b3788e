package cn.iocoder.zj.module.collection.dal.dataobject.zstack;

import lombok.Data;

@Data
public class AlarmConfigInfo {
    private Long id;

    private String alarmName;
    /**
     * 告警简介
     */
    private String description;
    /**
     * 资源类型，host云主机；hardware宿主机；storage存储；image镜像
     */
    private String sourceType;
    /**
     * 字典名称
     */
    private String dictLabelName;
    /**
     * 字典类型
     */
    private String dictLabelType;
    /**
     * 字典值
     */
    private String dictLabelValue;
    /**
     * 触发规则
     */
    private String alarmRule;
    /**
     * 告警阈值
     */
    private Integer alarmVal;
    /**
     * 单位
     */
    private String unit;
    /**
     * 收敛次数
     */
    private Long alarmTime;
    /**
     * 告警级别
     */
    private Integer alarmLevel;
    /**
     * 消息内容
     */
    private String context;
    /**
     * 删除标记
     */
    private Integer deleted;

    private Long tenantId;
}
