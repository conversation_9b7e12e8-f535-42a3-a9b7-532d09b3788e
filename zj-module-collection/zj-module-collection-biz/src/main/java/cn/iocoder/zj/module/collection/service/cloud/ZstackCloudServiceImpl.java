package cn.iocoder.zj.module.collection.service.cloud;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import cn.iocoder.zj.module.collection.collect.http.MetricsFetcher;
import cn.iocoder.zj.module.collection.dal.redis.zstack.ZstackAccessTokenRedisDAO;
import cn.iocoder.zj.module.collection.handler.HttpClientWrapper;
import cn.iocoder.zj.module.collection.service.zstack.AbstractZstackApi;
import cn.iocoder.zj.module.collection.service.zstack.core.ZstackApiConstant;
import cn.iocoder.zj.module.collection.util.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.IdentityHashMap;
import java.util.Map;

/**
 * @ClassName : ZstackCloud  //类名
 * @Description : 云接口实现  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/25  17:23
 */


@Log4j2
@Service
public class ZstackCloudServiceImpl extends AbstractZstackApi implements IZstackCloudService {

    @Autowired
    ZstackAccessTokenRedisDAO zstackAccessTokenRedisDAO;

    @Autowired
    private HttpClientWrapper httpClientWrapper;

    MetricsFetcher fetcher = new MetricsFetcher();
    private static final int METRIC_PERIOD = 20;
    private static final String CONTENT_TYPE = "application/json;charset=UTF-8";

    private String fetchMetricUtilization(String url, String token, String uuid, String metricName) {
        Map<String, String> header = new HashMap<>();
        header.put("Authorization", "OAuth " + token);
        header.put("Content-Type", CONTENT_TYPE);
        Map<String, Object> params = new HashMap<>();
        params.put("metricName", metricName);
        params.put("namespace", "ZStack/VM");

        if (StringUtil.isNotEmpty(uuid)){
            params.put("labels", "VMUuid=" + uuid);
        }
        params.put("period", METRIC_PERIOD);
        String response = fetcher.sendRequest(url + ZstackApiConstant.GET_ZSTACK_METRICS, Method.GET, header, params, token,
                httpResponse -> {
                    if (httpResponse.getStatus() == 200) {
                        return httpResponse.body();
                    } else {
                        throw new RuntimeException("请求失败，状态码：" + httpResponse.getStatus());
                    }
                });

        return response;
    }
    /**
     * @description: 云主机基础信息
     * <AUTHOR>
     * @date 2023/6/1 13:48
     * @version 1.0
     */
    @Override
    public String cloudInfo(String url, String token) {
        HttpRequest request = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_VM_INSTANCES)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8");
        // 执行请求（通过包装类）
        HttpResponse result = httpClientWrapper.execute(request);
        if (result.getStatus() != 200) {
            throw new RuntimeException("getcloudInfo error");
        }
        return result.body();
    }

    /**
     * @description: 弹性ip
     * <AUTHOR>
     * @date 2023/6/1 13:48
     * @version 1.0
     */
    @Override
    public String eips(String url, String token) {
        HttpRequest request = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_NICS)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8");
        // 执行请求（通过包装类）
        HttpResponse result = httpClientWrapper.execute(request);
        if (result.getStatus() != 200) {
            throw new RuntimeException("geteips error");
        }
        return result.body();
    }

    @Override
    public String queryGlobalConfig(String url, String token) {
        HttpRequest request = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_GLOBAL_CONFIG)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8");
        // 执行请求（通过包装类）
        HttpResponse result = httpClientWrapper.execute(request);
        if (result.getStatus() != 200) {
            throw new RuntimeException("getcloudInfo error");
        }
        return result.body();
    }

    @Override
    public String resourceConfigList(String url, String token, String uuid) {
        HttpRequest request =  HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_RESOURCE_CONFIG+"/"+uuid+"/kvm"+"/reservedMemory")
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8");
        // 执行请求（通过包装类）
        HttpResponse result = httpClientWrapper.execute(request);
        if (result.getStatus() != 200) {
            throw new RuntimeException("getcloudInfo error");
        }
        return result.body();
    }

    @Override
    public String diskReadOps(String url, String token) {
        Map<String, String> header = new HashMap<>();
        IdentityHashMap<String, Object> p = new IdentityHashMap<>();
        p.put("metricName", "DiskReadOps");
        p.put("namespace", "ZStack/VM");
        header.put("Authorization", "OAuth " + token);
        header.put("Content-Type", "application/json;charset=UTF-8");

        HttpRequest request =  HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_METRICS)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .form(p);
        // 执行请求（通过包装类）
        HttpResponse result = httpClientWrapper.execute(request);
        if (result.getStatus() != 200) {
            throw new RuntimeException("getcpuUsedUtilization error");
        }
        return result.body();
    }

    @Override
    public String diskAllReadOps(String url, String token) {
        Map<String, String> header = new HashMap<>();
        IdentityHashMap<String, Object> p = new IdentityHashMap<>();
        p.put("metricName", "DiskAllReadOps");
        p.put("namespace", "ZStack/VM");
        header.put("Authorization", "OAuth " + token);
        header.put("Content-Type", "application/json;charset=UTF-8");

        HttpRequest request = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_METRICS)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .form(p);
        HttpResponse result = httpClientWrapper.execute(request);
        if (result.getStatus() != 200) {
            throw new RuntimeException("getcpuUsedUtilization error");
        }
        return result.body();
    }

    /**
     * 磁盘读IOPS
     *
     * @param url
     * @param token
     * @return
     */
    @Override
    public String diskWriteOps(String url, String token) {
        Map<String, String> header = new HashMap<>();
        IdentityHashMap<String, Object> p = new IdentityHashMap<>();
        p.put("metricName", "DiskAllWriteOps");
        p.put("namespace", "ZStack/VM");
        header.put("Authorization", "OAuth " + token);
        header.put("Content-Type", "application/json;charset=UTF-8");

        HttpRequest request = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_METRICS)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .form(p);
        HttpResponse result = httpClientWrapper.execute(request);
        if (result.getStatus() != 200) {
            throw new RuntimeException("getcpuUsedUtilization error");
        }
        return result.body();
    }

    /**
     * 全部磁盘写IOPS
     *
     * @param url
     * @param token
     * @return
     */
    @Override
    public String diskAllWriteOps(String url, String token) {
        Map<String, String> header = new HashMap<>();
        IdentityHashMap<String, Object> p = new IdentityHashMap<>();
        p.put("metricName", "DiskWriteOps");
        p.put("namespace", "ZStack/VM");
        header.put("Authorization", "OAuth " + token);
        header.put("Content-Type", "application/json;charset=UTF-8");

        HttpResponse result = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_METRICS)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .form(p)
                .execute();
        if (result.getStatus() != 200) {
            throw new RuntimeException("getcpuUsedUtilization error");
        }
        return result.body();
    }

    @Override
    public String clusters(String url, String token, String uuid) {
        String body = "";
        if (StringUtil.isNotEmpty(uuid)) {
            HttpRequest request =  HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_CLUSTERS+"/"+uuid)
                    .header(Header.AUTHORIZATION, "OAuth " + token)
                    .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8");
            // 执行请求（通过包装类）
            HttpResponse result = httpClientWrapper.execute(request);
            if (result.getStatus() != 200) {
                throw new RuntimeException("getcloudInfo error");
            }
            body = result.body();
        } else {
            HttpRequest request =  HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_CLUSTERS)
                    .header(Header.AUTHORIZATION, "OAuth " + token)
                    .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8");
            // 执行请求（通过包装类）
            HttpResponse result = httpClientWrapper.execute(request);
            if (result.getStatus() != 200) {
                throw new RuntimeException("getcloudInfo error");
            }
            body = result.body();
        }

        return body;
    }


    @Override
    public String fetchMetricData(String url, String token, String uuid, String metricName) {
        Map<String, String> header = new HashMap<>();
        header.put("Authorization", "OAuth " + token);
        header.put("Content-Type", CONTENT_TYPE);

        Map<String, Object> params = new HashMap<>();
        params.put("metricName", metricName);
        params.put("namespace", "ZStack/VM");

        if (StringUtil.isNotEmpty(uuid)){
            params.put("labels", "VMUuid=" + uuid);
        }
        params.put("period", METRIC_PERIOD);

        String response = fetcher.sendRequest(url + ZstackApiConstant.GET_ZSTACK_METRICS, Method.GET, header, params, token,
                httpResponse -> {
                    if (httpResponse.getStatus() == 200) {
                        return httpResponse.body();
                    } else {
                        throw new RuntimeException("请求失败，状态码：" + httpResponse.getStatus());
                    }
                });
        return response;
    }

    /**
     * @description: cpu使用率
     * @param:
     * @return: java.lang.String
     * <AUTHOR>
     * @date: 2023/5/29 15:59
     */
    @Override
    public String cpuUsedUtilization(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "CPUUsedUtilization");
    }

    @Override
    public String cpuAllUsedUtilization(String url, String token, String uuid) {
//        Map<String, String> header = new HashMap<>();
//        Map<String, Object> map = new HashMap<>();
//
//        IdentityHashMap<String, Object> p = new IdentityHashMap<>();
//        p.put("metricName", "CPUAllUsedUtilization");
//        p.put("namespace", "ZStack/VM");
//        if (StringUtil.isNotEmpty(uuid)) {
//            p.put("labels", "VMUuid=" + uuid);
//        }
//        //p.put("period",20);
//        header.put("Authorization", "OAuth " + token);
//        header.put("Content-Type", "application/json;charset=UTF-8");
//
//        map.put("metricParam", p);
//        HttpResponse result = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_METRICS)
//                .header(Header.AUTHORIZATION, "OAuth " + token)
//                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
//                .form(p)
//                .execute();
//        if (result.getStatus() != 200) {
//            throw new RuntimeException("getcpuCPUAllUsedUtilization error");
//        }
//        return result.body();

        return fetchMetricUtilization(url, token, uuid, "CPUAllUsedUtilization");
    }

    @Override
    public String cpuUserUtilization(String url, String token, String uuid) {
        return null;
    }

    @Override
    public String cpuWaitUtilization(String url, String token, String uuid) {
        return null;
    }

    @Override
    public String cpuSystemUtilization(String url, String token, String uuid) {
        return null;
    }

    @Override
    public String cpuIdleUtilization(String url, String token, String uuid) {
        return null;
    }

    /**
     * CPU平均使用率
     *
     * @description:
     * <AUTHOR>
     * @date 2023/6/1 13:47
     * @version 1.0
     */
    @Override
    public String cpuAverageUsedUtilization(String url, String token, String uuid) {
//        Map<String, String> header = new HashMap<>();
//        IdentityHashMap<String, Object> p = new IdentityHashMap<>();
//        p.put("metricName", "CPUAverageUsedUtilization");
//        p.put("namespace", "ZStack/VM");
//        if (StringUtil.isNotEmpty(uuid)) {
//            p.put("labels", "VMUuid=" + uuid);
//        }
//        //p.put("period",20);
//        header.put("Authorization", "OAuth " + token);
//        header.put("Content-Type", "application/json;charset=UTF-8");
//
//        HttpResponse result = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_METRICS)
//                .header(Header.AUTHORIZATION, "OAuth " + token)
//                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
//                .form(p)
//                .execute();
//        if (result.getStatus() != 200) {
//            throw new RuntimeException("getcpuAverageUsedUtilization error");
//        }
//        return result.body();

        return fetchMetricUtilization(url, token, uuid, "CPUAverageUsedUtilization");
    }

    /**
     * @description: 内存已用百分比
     * <AUTHOR>
     * @date 2023/6/1 13:46
     * @version 1.0
     */
    @Override
    public String memoryUsedInPercent(String url, String token, String uuid) {
//        Map<String, String> header = new HashMap<>();
//        IdentityHashMap<String, Object> p = new IdentityHashMap<>();
//        p.put("metricName", "MemoryUsedInPercent");
//        p.put("namespace", "ZStack/VM");
//        if (StringUtil.isNotEmpty(uuid)) {
//            p.put("labels", "VMUuid=" + uuid);
//        }
//        //p.put("period",20);
//        header.put("Authorization", "OAuth " + token);
//        header.put("Content-Type", "application/json;charset=UTF-8");
//
//        HttpResponse result = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_METRICS)
//                .header(Header.AUTHORIZATION, "OAuth " + token)
//                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
//                .form(p)
//                .execute();
//        if (result.getStatus() != 200) {
//            throw new RuntimeException("getmemoryUsedInPercent error");
//        }
//        return result.body();

        return fetchMetricUtilization(url, token, uuid, "MemoryUsedInPercent");
    }

    /**
     * @description: 内存已用容量
     * <AUTHOR>
     * @date 2023/6/1 13:46
     * @version 1.0
     */
    @Override
    public String memoryUsedBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "MemoryUsedBytes");
    }

    /**
     * @description: 内存空闲容量
     * <AUTHOR>
     * @date 2023/6/1 13:51
     * @version 1.0
     */
    @Override
    public String memoryFreeBytes(String url, String token, String uuid) {
//        Map<String, String> header = new HashMap<>();
//        IdentityHashMap<String, Object> p = new IdentityHashMap<>();
//        p.put("metricName", "MemoryFreeBytes");
//        p.put("namespace", "ZStack/VM");
//        if (StringUtil.isNotEmpty(uuid)) {
//            p.put("labels", "VMUuid=" + uuid);
//        }
//        //p.put("period",20);
//        header.put("Authorization", "OAuth " + token);
//        header.put("Content-Type", "application/json;charset=UTF-8");
//
//        HttpResponse result = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_METRICS)
//                .header(Header.AUTHORIZATION, "OAuth " + token)
//                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
//                .form(p)
//                .execute();
//        if (result.getStatus() != 200) {
//            throw new RuntimeException("getmemoryFreeBytes error");
//        }
//        return result.body();

        return fetchMetricUtilization(url, token, uuid, "MemoryFreeBytes");
    }

    @Override
    public String diskReadBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "DiskReadBytes");
    }

    @Override
    public String diskWriteBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "DiskWriteBytes");
    }

    @Override
    public String diskReadOps(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "DiskReadOps");
    }

    @Override
    public String diskWriteOps(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "DiskWriteOps");
    }

    /**
     * @description: 磁盘已使用容量百分比
     * <AUTHOR>
     * @date 2023/6/2 11:11
     * @version 1.0
     */
    @Override
    public String diskUsedCapacityInPercent(String url, String token) {
        return fetchMetricUtilization(url, token, "", "DiskUsedCapacityInPercent");
    }

    /**
     * @description: 磁盘剩余容量百分比
     * <AUTHOR>
     * @date 2023/6/2 11:12
     * @version 1.0
     */
    @Override
    public String diskFreeCapacityInPercent(String url, String token) {
        return fetchMetricUtilization(url, token, "", "DiskFreeCapacityInPercent");
    }

    /**
     * @description: 磁盘剩余容量
     * <AUTHOR>
     * @date 2023/6/2 11:12
     * @version 1.0
     */
    @Override
    public String diskFreeCapacityInBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "DiskFreeCapacityInBytes");
    }

    /**
     * @description: 磁盘已使用容量
     * <AUTHOR>
     * @date 2023/6/2 11:13
     * @version 1.0
     */
    @Override
    public String diskUsedCapacityInBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "DiskUsedCapacityInBytes");
    }

    /**
     * @description: 全部磁盘剩余容量百分比
     * <AUTHOR>
     * @date 2023/6/2 15:22
     * @version 1.0
     */

    @Override
    public String diskAllFreeCapacityInPercent(String url, String token) {
        return fetchMetricUtilization(url, token, "", "DiskAllFreeCapacityInPercent");
    }

    /**
     * @description: 全部磁盘已使用容量百分比
     * <AUTHOR>
     * @date 2023/6/2 15:22
     * @version 1.0
     */
    @Override
    public String diskAllUsedCapacityInPercent(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "DiskAllUsedCapacityInPercent");
    }

    /**
     * @description: 全部磁盘已使用容量
     * <AUTHOR>
     * @date 2023/6/2 15:23
     * @version 1.0
     */
    @Override
    public String diskAllUsedCapacityInBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "DiskAllUsedCapacityInBytes");
    }

    /**
     * @description: 全部磁盘剩余容量
     * <AUTHOR>
     * @date 2023/6/2 15:23
     * @version 1.0
     */
    @Override
    public String diskAllFreeCapacityInBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "DiskAllFreeCapacityInBytes");
    }

    @Override
    public String diskZStackUsedCapacityInPercent(String url, String token, String uuid) {
        return null;
    }

    @Override
    public String diskZStackUsedCapacityInBytes(String url, String token, String uuid) {
        return null;
    }

    @Override
    public String diskAllReadBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "DiskAllReadBytes");
    }

    @Override
    public String diskAllWriteBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "DiskAllWriteBytes");
    }

    /**
     * @description: 网卡入速度
     * <AUTHOR>
     * @date 2023/6/5 9:52
     * @version 1.0
     */
    @Override
    public String networkInBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "NetworkInBytes");
    }

    /**
     * @description: 全部网卡入速度
     * <AUTHOR>
     * @date 2023/6/5 9:53
     * @version 1.0
     */
    @Override
    public String networkAllInBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "NetworkAllInBytes");
    }

    /**
     * @description: 网卡出速度
     * <AUTHOR>
     * @date 2023/6/5 9:53
     * @version 1.0
     */
    @Override
    public String networkOutBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "NetworkOutBytes");
    }

    /**
     * @description: 全部网卡出速度
     * <AUTHOR>
     * @date 2023/6/5 9:54
     * @version 1.0
     */
    @Override
    public String networkAllOutBytes(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "NetworkAllOutBytes");
    }

    /**
     * @description: 网卡入包数
     * <AUTHOR>
     * @date 2023/6/5 9:54
     * @version 1.0
     */
    @Override
    public String networkInPackets(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "NetworkInPackets");
    }

    /**
     * @description: 全部网卡入包数
     * <AUTHOR>
     * @date 2023/6/5 9:55
     * @version 1.0
     */
    @Override
    public String networkAllInPackets(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "NetworkAllInPackets");
    }

    /**
     * @description: 网卡出包数
     * <AUTHOR>
     * @date 2023/6/5 9:55
     * @version 1.0
     */
    @Override
    public String networkOutPackets(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "NetworkOutPackets");
    }

    /**
     * @description: 全部网卡出包数
     * <AUTHOR>
     * @date 2023/6/5 9:59
     * @version 1.0
     */
    @Override
    public String networkAllOutPackets(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "NetworkAllOutPackets");
    }

    @Override
    public String networkOutErrors(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "NetworkOutErrors");
    }

    @Override
    public String networkInErrors(String url, String token, String uuid) {
        return fetchMetricUtilization(url, token, uuid, "NetworkInErrors");
    }

    @Override
    public String vmSnapshot(String url, String token, String uuid) {
        HttpRequest request = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_VM_SNAPSHOTS)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8");
        // 执行请求（通过包装类）
        HttpResponse result = httpClientWrapper.execute(request);
        if (result.getStatus() != 200) {
            throw new RuntimeException("geteips error");
        }
        return result.body();
    }


//    @Override
//    public String netWorkUpdown() {
//        return null;
//    }
//
//    @Override
//    public String netWorkSendOrHarvest() {
//        return null;
//    }

    private String VMUuidList(String uuid) {
        Map<String, String> header = new HashMap<>();
        header.put("Authorization", "OAuth " + uuid);
        header.put("Content-Type", "application/json;charset=UTF-8");

        Map<String, Object> p = new IdentityHashMap<>();
        p.put("zoneUuid", uuid);
        p.put(new String("q"), "hypervisorType=KVM");
        p.put(new String("q"), "state=Running");
        p.put(new String("q"), "type=UserVm");
        // 获取uuidList
        HttpResponse result = HttpRequest.get("http://**************:8080" + "/zstack/v1/vm-instances")
                .header(Header.AUTHORIZATION, "OAuth " + uuid)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .form(p)
                .execute();
        if (result.getStatus() != 200) {
            throw new RuntimeException("getVMUuidList error");
        }

        StringBuffer sb = new StringBuffer();
        JSONObject jsonObject = JSONObject.parseObject(result.body());

        JSONArray jsonArray = jsonObject.getJSONArray("inventories");
        if (jsonArray.size() > 0) {
            for (int i = 0; i < jsonArray.size(); i++) {
                sb.append(StringUtil.toString(jsonArray.getJSONObject(i).get("uuid"))).append("|");
            }
        }
        return sb.deleteCharAt(sb.length() - 1).toString();
    }
}
