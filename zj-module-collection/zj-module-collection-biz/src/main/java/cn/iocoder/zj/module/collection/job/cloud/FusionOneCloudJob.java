package cn.iocoder.zj.module.collection.job.cloud;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.iocoder.zj.module.collection.dal.redis.fusionOne.FusionOneAccessTokenRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.zstack.AlarmInfoRedisDAO;
import cn.iocoder.zj.module.collection.framework.influx.config.InfluxDBTemplate;
import cn.iocoder.zj.module.collection.service.cloud.FusionOneDeviceService;
import cn.iocoder.zj.module.collection.service.fusionOne.FusionOneApiConstant;
import cn.iocoder.zj.module.collection.util.PowerStateUtil;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.monitor.api.hostinfo.HostInfoApi;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.valume.VolumeApi;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeDTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class FusionOneCloudJob {


    @Resource
    PlatformRedisDAO platformRedisDAO;

    @Resource
    PlatformconfigApi platformconfigApi;

    @Resource
    AlarmInfoRedisDAO alarmInfoRedisDAO;

    @Resource
    FusionOneAccessTokenRedisDAO fusionOneAccessTokenRedisDAO;

    @Resource
    FusionOneDeviceService fusionOneDeviceService;

    @Resource
    VolumeApi volumeApi;

    @Resource
    HostInfoApi hostInfoApi;

    @Autowired
    private AlarmConfigApi alarmConfigApi;

    @Resource
    InfluxDBTemplate influxDBTemplate;

    private static final String PLATFORM_TYPE = "fusionOne";
    private static final String TOKEN_PREFIX = "FusionOne:";


    @XxlJob("fusionOneCloud")
    @Transactional
    public void collectFusionOneCloud() {
        try {
            List<PlatformconfigDTO> platforms = getPlatformConfigs();
            if (CollUtil.isEmpty(platforms)) {
                return;
            }
            List<HostInfoRespCreateReqDTO> allHostInfos = new ArrayList<>();
            for (PlatformconfigDTO platform : platforms) {
                try {
                    processPlatform(platform, allHostInfos);
                } catch (Exception e) {
                    log.error("处理平台 [{}] 失败", platform.getName(), e);
                }
            }

            if (!allHostInfos.isEmpty()) {
                syncHostInfo(allHostInfos);
            }
        } catch (Exception e) {
            log.error("采集FusionOne云平台信息失败", e);
        }
    }

    private List<PlatformconfigDTO> getPlatformConfigs() {
        List<PlatformconfigDTO> platforms = platformRedisDAO.get("platform");
        if (platforms == null) {
            platforms = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform", platforms);
        }
        return platforms;
    }

    private void processPlatform(PlatformconfigDTO platform, List<HostInfoRespCreateReqDTO> allHostInfos) {
        if (!PLATFORM_TYPE.equals(platform.getTypeCode())) {
                        return;
                    }
        Map<String, Object> tokenInfo = fusionOneAccessTokenRedisDAO.get(TOKEN_PREFIX + platform.getId());
        if (tokenInfo == null) {
                        return;
                    }
        String token = Convert.toStr(tokenInfo.get("token"));
        String siteId = Convert.toStr(tokenInfo.get("siteId"));
        if (StrUtil.hasBlank(token, siteId)) {
            log.warn("平台 [{}] token或siteId为空", platform.getName());
            return;
        }
        platform.setUrl(platform.getUrl().replace(":8443", ":7443"));
        JSONArray clouds = fusionOneDeviceService.getClouds(platform, token, siteId);
        if (clouds == null) {
            return;
        }
        List<VolumeDTO> volumeDTOs = new ArrayList<>();
                    for (int i = 0; i < clouds.size(); i++) {
            JSONObject cloud = clouds.getJSONObject(i);
            processCloud(platform, cloud, token, siteId, allHostInfos, volumeDTOs);
        }
        if (!volumeDTOs.isEmpty()) {
            updateVolumeInfo(volumeDTOs, platform.getId());
        }
    }

    private void processCloud(PlatformconfigDTO platform, JSONObject cloud, String token, String siteId,List<HostInfoRespCreateReqDTO> allHostInfos, List<VolumeDTO> volumeDTOs) {
                        String uri = cloud.getString("uri");
        JSONObject cloudInfo = fusionOneDeviceService.getInfoByVms(platform, token, uri);
        if (cloudInfo == null) {
            return;
        }
        HostInfoRespCreateReqDTO hostInfo = buildHostInfo(platform, cloud, cloudInfo);
        // 处理实时数据
        processRealTimeData(platform, token, siteId, cloudInfo, hostInfo);
        // 处理磁盘信息
        processDiskInfo(platform, token, siteId, cloud, cloudInfo, hostInfo, volumeDTOs);
        allHostInfos.add(hostInfo);
    }

    private HostInfoRespCreateReqDTO buildHostInfo(PlatformconfigDTO platform, JSONObject cloud, JSONObject cloudInfo) {
        HostInfoRespCreateReqDTO hostInfo = new HostInfoRespCreateReqDTO();
        // 基本信息
        hostInfo.setUuid(cloud.getString("uuid"));
        hostInfo.setState(stateConvert(cloud.getString("status")));
        hostInfo.setName(cloud.getString("name"));

        // 操作系统信息
                        JSONObject osOptions = JSONObject.parseObject(cloudInfo.getString("osOptions"));
        hostInfo.setGuestOsType(osOptions.getString("guestOSName"));
        hostInfo.setVCreateDate(cloudInfo.getDate("createTime"));

        // 网络信息
                        JSONObject vmConfig = cloudInfo.getJSONObject("vmConfig");
                        JSONArray nics = vmConfig.getJSONArray("nics");
                        if (nics != null && nics.size() > 0) {
            JSONObject nic = nics.getJSONObject(0);
            hostInfo.setIp(nic.getString("ip"));
            hostInfo.setMac(nic.getString("mac"));
                        } else {
            hostInfo.setMac("");
        }
        hostInfo.setVipIp("");

        // 集群信息
        hostInfo.setClusterName(StrUtil.emptyToDefault(cloudInfo.getString("clusterName"), ""));
        hostInfo.setClusterUuid(StrUtil.emptyToDefault(cloudInfo.getString("clusterUrn"), ""));

        // 硬件信息
                        JSONObject cpu = vmConfig.getJSONObject("cpu");
                        JSONObject memory = vmConfig.getJSONObject("memory");
        hostInfo.setMemorySize(memory.getLong("quantityMB") * 1024 * 1024);
        String arch = cloud.getString("arch") == null ? "" : cloud.getString("arch");
        if (arch.toLowerCase().contains("x86")) {
            hostInfo.setArchitecture("x86_64");
        } else if (arch.toLowerCase().contains("arm")) {
            hostInfo.setArchitecture("arm64");
        } else if (arch.isEmpty()) {
            hostInfo.setArchitecture("-");
        } else {
            hostInfo.setArchitecture(arch);
        }
        //hostInfo.setArchitecture(cloud.getString("arch"));
        hostInfo.setCpuNum(cpu.getInteger("quantity"));

        // 平台信息
        hostInfo.setPlatformId(platform.getId());
        hostInfo.setPlatformName(platform.getName());
        hostInfo.setTypeName(platform.getTypeCode());
        hostInfo.setTenantId(platform.getTenantId());
        hostInfo.setRegionId(platform.getRegionId());

        // 虚拟机类型
        Integer vmType = cloudInfo.getInteger("vmType");
        hostInfo.setType(convertVmType(vmType));

        // 删除标记
        hostInfo.setDeleted(cloud.getBoolean("isTemplate") ? 1 : 0);
        String status = PowerStateUtil.powerStateConvert(stateConvert(cloudInfo.get("status").toString()));
        String siteName = fusionOneAccessTokenRedisDAO.get("FusionOne:" + platform.getId()).get("siteName").toString();
        hostInfo.setPowerState(status);
//        hostInfo.setImageName(cloudInfo.getString("templateName"));
//        hostInfo.setImageUuid(cloudInfo.getString("templateId"));
        hostInfo.setZoneName(siteName);
//        hostInfo.setHardwareUuid(siteName);
        hostInfo.setHardwareName(cloudInfo.getString("hostName"));

        JSONObject properties = vmConfig.getJSONObject("properties");
        JSONObject haConfig = cloudInfo.getJSONObject("haConfig");
        if(StrUtil.isNotEmpty(haConfig.getString("hostFaultPolicy")) && !haConfig.getString("hostFaultPolicy").equals("3")){
            hostInfo.setAutoInitType("NeverStop");
        }else {
            hostInfo.setAutoInitType("None");
        }

        if(properties.getString("bootFirmware").equals("BIOS")){
            hostInfo.setGuideMode("Legacy");
        }else {
            hostInfo.setGuideMode("UEFI");
        }

        return hostInfo;
    }

    private void processRealTimeData(PlatformconfigDTO platform, String token, String siteId,
                                     JSONObject cloudInfo, HostInfoRespCreateReqDTO hostInfo) {
                        String urn = cloudInfo.getString("urn");

        // 获取实时数据
        JSONObject items = fusionOneDeviceService.getRelaTimeData(platform, token, siteId, urn).getJSONObject(0);
                        JSONArray valueArray = items.getJSONArray("value");

        BigDecimal cpuUsage = BigDecimal.ZERO;
        BigDecimal memUsage = BigDecimal.ZERO;
        BigDecimal diskUsage = BigDecimal.ZERO;
                        JSONArray nicOriginalInfo = new JSONArray();

        // 处理各项指标
        for (int i = 0; i < valueArray.size(); i++) {
            JSONObject value = valueArray.getJSONObject(i);
            String metricId = value.getString("metricId");
            String metricValue = value.getString("metricValue");

            if (StrUtil.isNotEmpty(metricValue)) {
                switch (metricId) {
                    case "cpu_usage":
                                    cpuUsage = cpuUsage.add(value.getBigDecimal("metricValue"));
                        break;
                    case "mem_usage":
                                    memUsage = memUsage.add(value.getBigDecimal("metricValue"));
                        break;
                    case "disk_usage":
                                    diskUsage = diskUsage.add(value.getBigDecimal("metricValue"));
                        break;
                    case "nic_original_info":
                                    nicOriginalInfo = value.getJSONArray("metricValue");
                        break;
                }
            }
        }

        // 设置使用率
        hostInfo.setMemoryUsed(memUsage);
        hostInfo.setCpuUsed(cpuUsage);
        hostInfo.setDiskUsed(diskUsage);

        // 处理网络信息
        processNetworkInfo(platform, token, siteId, urn, hostInfo);

        // 处理网卡信息
        if (nicOriginalInfo.size() > 0) {
            BigDecimal rx = BigDecimal.ZERO;
            BigDecimal tx = BigDecimal.ZERO;
            for (int i = 0; i < nicOriginalInfo.size(); i++) {
                JSONObject nic = nicOriginalInfo.getJSONObject(i);
                rx = rx.add(nic.getBigDecimal("rx"));
                tx = tx.add(nic.getBigDecimal("tx"));
            }
            hostInfo.setNetworkInPackets(rx);
            hostInfo.setNetworkOutPackets(tx);
        }
    }

    private void processNetworkInfo(PlatformconfigDTO platform, String token, String siteId,
                                    String urn, HostInfoRespCreateReqDTO hostInfo) {
        JSONArray netWorkInfo = fusionOneDeviceService.getRelaTimeDataNetWork(platform, token, siteId, urn);
        if (netWorkInfo != null && netWorkInfo.size() > 0) {
            JSONArray netWorkValues = netWorkInfo.getJSONObject(0).getJSONArray("value");
            for (int i = 0; i < netWorkValues.size(); i++) {
                JSONObject value = netWorkValues.getJSONObject(i);
                String metricId = value.getString("metricId");
                if ("nic_byte_in".equals(metricId)) {
                    hostInfo.setNetworkInBytes(value.getBigDecimal("metricValue"));
                } else if ("nic_byte_out".equals(metricId)) {
                    hostInfo.setNetworkOutBytes(value.getBigDecimal("metricValue"));
                }
            }
        }
    }

    private void processDiskInfo(PlatformconfigDTO platform, String token, String siteId,
                                 JSONObject cloud, JSONObject cloudInfo, HostInfoRespCreateReqDTO hostInfo,
                                 List<VolumeDTO> volumeDTOs) {
        JSONObject vmConfig = cloudInfo.getJSONObject("vmConfig");
        JSONArray disks = vmConfig.getJSONArray("disks");

        Long cloudSize = 0L;
        Long actualSize = 0L;
        Long totalDiskCapacity = 0L;
        Long diskUsedBytes = 0L;

        for (int i = 0; i < disks.size(); i++) {
            JSONObject disk = disks.getJSONObject(i);
            String volumeUrn = disk.getString("volumeUrn");
            JSONObject volume = fusionOneDeviceService.getVolumes(platform, token, siteId, volumeUrn);

            VolumeDTO volumeDTO = buildVolumeDTO(cloud, volume, i);

            // 计算容量
            Long capacity = Optional.ofNullable(volume.get("quantityGB"))
                    .map(qty -> Convert.toLong(qty) * 1024 * 1024 * 1024)
                    .orElse(0L);
            Long allocation = Optional.ofNullable(volume.get("userUsedSize"))
                    .map(size -> Convert.toLong(size) * 1024 * 1024)
                    .orElse(0L);

            if (i > 0) {
                cloudSize += capacity;
                actualSize += allocation;
            } else {
                                totalDiskCapacity = capacity;
                                diskUsedBytes = allocation;
                            }

                            volumeDTO.setSize(capacity);
            volumeDTO.setActualSize(capacity);
                            volumeDTO.setActualFree(capacity - allocation);
                            volumeDTO.setActualUse(allocation);
            volumeDTO.setActualRatio(calculateRatio(allocation, capacity));
            volumeDTO.setPlatformId(platform.getId());
            volumeDTO.setPlatformName(platform.getName());

            volumeDTOs.add(volumeDTO);
        }

        // 设置主机磁盘信息
        hostInfo.setTotalDiskCapacity(new BigDecimal(totalDiskCapacity));
        hostInfo.setDiskUsedBytes(new BigDecimal(totalDiskCapacity - diskUsedBytes));
        hostInfo.setDiskFreeBytes(new BigDecimal(diskUsedBytes));
        hostInfo.setCloudSize(new BigDecimal(cloudSize));
        hostInfo.setActualSize(new BigDecimal(actualSize));
    }

    private void syncHostInfo(List<HostInfoRespCreateReqDTO> newHostInfos) {
        // 获取分片信息
                int shardIndex = XxlJobHelper.getShardIndex();
                int shardTotal = XxlJobHelper.getShardTotal();
        log.info("当前节点index = {}, 总节点数 = {}", shardIndex, shardTotal);

        // 获取分片数据
        List<HostInfoRespCreateReqDTO> shardingData = shardIndex < 0 ?
                newHostInfos :
                StringUtil.getShardingData(newHostInfos, shardTotal, shardIndex);

        // 同步数据
        int existingCount = hostInfoApi.count(PLATFORM_TYPE);
        if (existingCount == 0) {
                    hostInfoApi.adds(shardingData);
            return;
        }

        // 获取现有数据
        List<HostInfoRespCreateReqDTO> existingHosts = hostInfoApi.getAll(PLATFORM_TYPE).getData();

        // 处理需要删除的主机
        List<HostInfoRespCreateReqDTO> toDelete = existingHosts.stream()
                .filter(existing -> newHostInfos.stream()
                        .noneMatch(newHost -> newHost.getUuid().equals(existing.getUuid())))
                .peek(host -> {
                    host.setDeleted(1);
                    host.setState("Destroyed");
                })
                            .collect(Collectors.toList());

        // 处理需要新增的主机
        List<HostInfoRespCreateReqDTO> toAdd = newHostInfos.stream()
                .filter(newHost -> existingHosts.stream()
                        .noneMatch(existing -> existing.getUuid().equals(newHost.getUuid())))
                .distinct()
                .collect(Collectors.toList());

        // 执行更新操作
        if (!toDelete.isEmpty()) {
            hostInfoApi.updates(toDelete);
        }
        if (!shardingData.isEmpty()) {
                        hostInfoApi.updates(shardingData);
                    }
        if (!toAdd.isEmpty()) {
            hostInfoApi.adds(toAdd);
        }

        // 清理重复数据
                    hostInfoApi.removeDuplicateData();
                }

    private VolumeDTO buildVolumeDTO(JSONObject cloud, JSONObject volume, int index) {
        VolumeDTO volumeDTO = new VolumeDTO();
        volumeDTO.setDescription("FusionOne云盘描述");
        volumeDTO.setName(volume.getString("name"));
        volumeDTO.setFormat(volume.getString("volumeFormat"));
        volumeDTO.setType(index == 0 ? "Root" : "Data");
        volumeDTO.setState("Enabled");
        volumeDTO.setStatus("Ready");
        volumeDTO.setUuid(volume.getString("uuid"));
        volumeDTO.setIsMount(true);

        // 设置关联信息
        volumeDTO.setVmInstanceUuid(cloud.getString("uuid"));
        volumeDTO.setVmInstanceName(cloud.getString("name"));
        volumeDTO.setPrimaryStorageUuid(volume.getString("datastoreUrn"));
        volumeDTO.setPrimaryStorageName(volume.getString("datastoreName"));
        volumeDTO.setPrimaryStorageType(volume.getString("storageType"));

        return volumeDTO;
    }

    private String calculateRatio(Long used, Long total) {
        if (total == 0) return "0";
        return new BigDecimal(used)
                .multiply(new BigDecimal(100))
                .divide(new BigDecimal(total), 2, BigDecimal.ROUND_HALF_UP)
                .toString();
    }

    private String convertVmType(Integer vmType) {
        if (vmType == null) return "未知类型";
        switch (vmType) {
            case 0: return "普通虚拟机";
            case 1: return "容灾虚拟机";
            case 2: return "占位虚拟机";
            default: return "未知类型";
        }
    }

    private String stateConvert(String status) {
        if (StrUtil.isEmpty(status)) {
            return "Unknown";
        }
        switch (status.toLowerCase()) {
            case "running": return "Running";
            case "stopped": return "Stopped";
            case "error": return "Error";
            default: return "Unknown";
        }
    }
    private void updateVolumeInfo(List<VolumeDTO> volumeDTOList, Long id) {
        if (!volumeDTOList.isEmpty()) {
            List<VolumeDTO> existingVolume = volumeApi.getVolumesByPlatformId(id);
            if (existingVolume.isEmpty()) {
                volumeApi.addVolumes(volumeDTOList);
            } else {
                Map<String, VolumeDTO> existingVolumeMap = existingVolume.stream()
                        .collect(Collectors.toMap(VolumeDTO::getUuid, volumeInfo -> volumeInfo));

                List<VolumeDTO> newVolume = new ArrayList<>();
                List<VolumeDTO> updateVolume = new ArrayList<>();
                List<VolumeDTO> deleteEntries = existingVolume.stream()
                        .filter(item -> !volumeDTOList.stream()
                                .map(VolumeDTO::getUuid).collect(Collectors.toList())
                                .contains(item.getUuid())).collect(Collectors.toList());

                for (VolumeDTO volumeDTO : volumeDTOList) {
                    VolumeDTO existingvolume = existingVolumeMap.get(volumeDTO.getUuid());
                    if (existingvolume == null) {
                        newVolume.add(volumeDTO);
                    } else if (!existingvolume.equals(volumeDTO)) {
                        volumeDTO.setDeleted(0L);
                        updateVolume.add(volumeDTO);
                    }
                }
                if (!deleteEntries.isEmpty()) {
                    deleteEntries.forEach(item -> item.setDeleted(1L));
                    volumeApi.delVolumes(deleteEntries);
                }
                if (updateVolume.size() > 0) {
                    volumeApi.updateVolumes(updateVolume);
                }
                if (newVolume.size() > 0) {
                    volumeApi.addVolumes(newVolume);
                }
            }
        }
    }


    @XxlJob("fusionOneCloudByVmInfos")
    public void fusionOneCloudByVmInfos() {
        List<PlatformconfigDTO> platformconfigDTOList = new ArrayList<>();
        if (platformRedisDAO.get("platform") == null) {
            platformconfigDTOList = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform", platformconfigDTOList);
        }
        List<HostInfoRespCreateReqDTO> hostInfoRespCreateReqDTOList = new ArrayList<>();
        platformconfigDTOList = platformRedisDAO.get("platform");
        if (platformconfigDTOList.size() > 0) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                String newUrl = p.getUrl().replace(":8443", ":7443");
                p.setUrl(newUrl);
                if (p.getTypeCode().equals("fusionOne") && fusionOneAccessTokenRedisDAO.get("FusionOne:" + p.getId()) != null) {
                    String token = fusionOneAccessTokenRedisDAO.get("FusionOne:" + p.getId()).get("token").toString();
                    String siteId = fusionOneAccessTokenRedisDAO.get("FusionOne:" + p.getId()).get("siteId").toString();
                    if (token == null) {
                        log.info("获取FusionOne token失败");
                        return;
                    }
                    if (siteId == null) {
                        log.info("获取FusionOne siteId失败");
                        return;
                    }
                    JSONArray clouds = fusionOneDeviceService.getClouds(p, token, siteId);
                    BatchPoints batchPoints = BatchPoints.builder().build();

                    for (Object cloud : clouds) {
                        JSONObject jsonObject = JSONObject.parseObject(cloud.toString());
                        String uri = jsonObject.getString("uri");
                        JSONObject cloudInfo = fusionOneDeviceService.getInfoByVms(p, token, uri);
                        JSONObject vmConfig = cloudInfo.getJSONObject("vmConfig");
                        JSONObject memory = vmConfig.getJSONObject("memory");
                        BigDecimal quantity = memory.getBigDecimal("quantityMB").multiply(new BigDecimal(1024));

                        //总内存
                        String urn = jsonObject.getString("urn");
                        JSONArray usageInfo = fusionOneDeviceService.getCurvedata(p, token, siteId, urn);
                        String uuid = jsonObject.getString("uuid");
                        String name = jsonObject.getString("name");
                        JSONObject items = fusionOneDeviceService.getRelaTimeData(p, token, siteId, urn).getJSONObject(0);
                        JSONArray valueArray = items.getJSONArray("value");
                        JSONArray nicOriginalInfo = new JSONArray();
                        JSONArray diskIoInfo = new JSONArray();
                        for (int j = 0; j < valueArray.size(); j++) {
                            JSONObject value = valueArray.getJSONObject(j);
                            if (value.getString("metricId").equals("disk_io_info")) {
                                if (!value.get("metricValue").equals("")) {
                                    diskIoInfo = value.getJSONArray("metricValue");
                                }
                            }
                            if (value.getString("metricId").equals("nic_original_info")) {
                                if (!value.get("metricValue").equals("")) {
                                    nicOriginalInfo = value.getJSONArray("metricValue");
                                }
                            }
                        }
                        //读IPOS
                        BigDecimal readIpos = new BigDecimal(0);
                        BigDecimal outIpos = new BigDecimal(0);
                        for (Object disk : diskIoInfo) {
                            JSONObject diskInfo = JSONObject.parseObject(disk.toString());
                            readIpos.add(diskInfo.getBigDecimal("rd_ios"));
                            outIpos.add(diskInfo.getBigDecimal("wr_ios"));
                        }
                        if (ObjectUtil.isNull(usageInfo)) {
                            continue; // 当前使用信息为空，跳过
                        }
                        for (Object item : usageInfo) {
                            JSONObject obj = JSONObject.parseObject(item.toString());
                            String metricId = obj.getString("metricId");
                            //cup使用率
                            if (metricId.equals("cpu_usage")) {
                                JSONArray metricValues = obj.getJSONArray("metricValue");
                                for (Object metricValue : metricValues) {
                                    JSONObject metric = JSONObject.parseObject(metricValue.toString());
                                    Point point = Point.measurement("zj_cloud_host")
                                            .tag("label","cpu")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "CPUAverageUsedUtilization")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("vm_type", "CPUAverageUsedUtilization")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "CPUAverageUsedUtilization")
                                            .addField("type", "CPUAverageUsedUtilization")
                                            .addField("value", Convert.toBigDecimal(metric.get("value")))
                                            .time(StringUtil.toLong(metric.get("time")), TimeUnit.SECONDS).build();
                                    batchPoints.point(point);
                                }
                            }
                            //内存使用
                            if (metricId.equals("mem_usage")){
                                JSONArray metricValues = obj.getJSONArray("metricValue");
                                for (Object metricValue : metricValues) {
                                    JSONObject metric = JSONObject.parseObject(metricValue.toString());
                                    //内存使用百分比
                                    BigDecimal memoryPercent = Convert.toBigDecimal(metric.get("value"));
                                    BigDecimal memoryUsed=quantity.multiply(memoryPercent);
                                    BigDecimal memoryFree = quantity.subtract(memoryUsed).setScale(2, RoundingMode.HALF_UP);
                                    if(memoryFree.compareTo(BigDecimal.ZERO) < 0){
                                        memoryFree=BigDecimal.ZERO;
                                    }
                                    Point usedPoint = Point.measurement("zj_cloud_host")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "MemoryUsedBytes")
                                            .tag("label", "mem")
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("vm_type", "all")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("productsName", name)
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "MemoryUsedBytes")
                                            .addField("type", "all")
                                            .addField("value", memoryUsed)
                                            .time(StringUtil.toLong(metric.get("time")), TimeUnit.SECONDS).build();
                                    batchPoints.point(usedPoint);

                                    Point pointFree = Point.measurement("zj_cloud_host")
                                            .tag("uuid", uuid)
                                            .tag("label","mem")
                                            .tag("metricName", "MemoryFreeBytes")
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("vm_type", "all")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("productsName", name)
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "MemoryFreeBytes")
                                            .addField("type", "all")
                                            .addField("value", memoryFree)
                                            .time(StringUtil.toLong(metric.get("time")), TimeUnit.SECONDS).build();
                                    batchPoints.point(pointFree);
                                    Point percentPoints = Point.measurement("zj_cloud_host")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "MemoryUsedInPercent")
                                            .tag("label", "mem")
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("vm_type", "all")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("productsName", StringUtil.toString(p.getName()))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "MemoryUsedInPercent")
                                            .addField("type", "all")
                                            .addField("value", memoryPercent)
                                            .time(StringUtil.toLong(metric.get("time")), TimeUnit.SECONDS).build();
                                    batchPoints.point(percentPoints);
                            }
                            }
                            //磁盘I/O写入
                            if (metricId.equals("disk_io_in")) {
                                JSONArray metricValues = obj.getJSONArray("metricValue");
                                for (Object metricValue : metricValues) {
                                    JSONObject metric = JSONObject.parseObject(metricValue.toString());
                                    Point point = Point.measurement("zj_cloud_host")
                                            .tag("uuid", uuid)
                                            .tag("label", "disk")
                                            .tag("metricName", "DiskWriteBytes")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("vm_type", "all")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "DiskWriteBytes")
                                            .addField("type", "all")
                                            .addField("value", Convert.toBigDecimal(metric.get("value")))
                                            .time(StringUtil.toLong(metric.get("time")), TimeUnit.SECONDS).build();
                                    batchPoints.point(point);
                                    Point pointOutIpos = Point.measurement("zj_cloud_host")
                                            .tag("uuid", uuid)
                                            .tag("label", "net")
                                            .tag("metricName", "DiskWriteOps")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("vm_type", "all")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "DiskWriteOps")
                                            .addField("type", "all")
                                            .addField("value", outIpos)
                                            .time(StringUtil.toLong(metric.get("time")), TimeUnit.SECONDS).build();
                                    batchPoints.point(pointOutIpos);
                                }
                            }
                            //磁盘I/O读出
                            if (metricId.equals("disk_io_out")) {
                                JSONArray metricValues = obj.getJSONArray("metricValue");
                                for (Object metricValue : metricValues) {
                                    JSONObject metric = JSONObject.parseObject(metricValue.toString());
                                    Point point = Point.measurement("zj_cloud_host")
                                            .tag("uuid", uuid)
                                            .tag("label", "disk")
                                            .tag("metricName", "DiskReadBytes")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("vm_type", "all")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "DiskReadBytes")
                                            .addField("type", "all")
                                            .addField("value", Convert.toBigDecimal(metric.get("value")))
                                            .time(StringUtil.toLong(metric.get("time")), TimeUnit.SECONDS).build();
                                    batchPoints.point(point);
                                    Point pointReadIpos = Point.measurement("zj_cloud_host")
                                            .tag("uuid", uuid)
                                            .tag("label", "disk")
                                            .tag("metricName", "DiskReadOps")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("vm_type", "all")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "DiskReadOps")
                                            .addField("type", "all")
                                            .addField("value", readIpos)
                                            .time(StringUtil.toLong(metric.get("time")), TimeUnit.SECONDS).build();
                                    batchPoints.point(pointReadIpos);
                                }
                            }
                            //网络流入流速
                            if (metricId.equals("nic_byte_in")) {
                                JSONArray metricValues = obj.getJSONArray("metricValue");
                                for (Object metricValue : metricValues) {
                                    JSONObject metric = JSONObject.parseObject(metricValue.toString());
                                    Point point = Point.measurement("zj_cloud_host")
                                            .tag("uuid", uuid)
                                            .tag("label", "net")
                                            .tag("metricName", "NetworkInBytes")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("vm_type", "all")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "NetworkInBytes")
                                            .addField("type", "all")
                                            .addField("value", Convert.toBigDecimal(metric.get("value")))
                                            .time(StringUtil.toLong(metric.get("time")), TimeUnit.SECONDS).build();
                                    batchPoints.point(point);
                                }
                            }
                                //网络流出流速
                            if (metricId.equals("nic_byte_out")) {
                                JSONArray metricValues = obj.getJSONArray("metricValue");
                                for (Object metricValue : metricValues) {
                                    JSONObject metric = JSONObject.parseObject(metricValue.toString());
                                    Point point = Point.measurement("zj_cloud_host")
                                            .tag("uuid", uuid)
                                            .tag("label", "net")
                                            .tag("metricName", "NetworkOutBytes")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("vm_type", "all")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "NetworkOutBytes")
                                            .addField("type", "all")
                                            .addField("value", Convert.toBigDecimal(metric.get("value")))
                                            .time(StringUtil.toLong(metric.get("time")), TimeUnit.SECONDS).build();
                                    batchPoints.point(point);
                                    BigDecimal tx = new BigDecimal(0);
                                    BigDecimal txDPkts = new BigDecimal(0);
                                    BigDecimal rx = new BigDecimal(0);
                                    BigDecimal rxDPkts = new BigDecimal(0);
                                    if (nicOriginalInfo.size() > 0) {
                                        for (Object nico : nicOriginalInfo) {
                                            JSONObject networkInfo = JSONObject.parseObject(nico.toString());
                                            tx.add(networkInfo.getBigDecimal("tx"));
                                            txDPkts.add(networkInfo.getBigDecimal("txDPkts"));
                                            rx.add(networkInfo.getBigDecimal("rx"));
                                            rxDPkts.add(networkInfo.getBigDecimal("rxDPkts"));
                                        }
                                    }
                                        Point pointTx = Point.measurement("zj_cloud_host")
                                                .tag("uuid",uuid)
                                                .tag("label","net")
                                                .tag("metricName", "NetworkOutPackets")
                                                .addField("productsName", name)
                                                .tag("regionId", StringUtil.toString(p.getRegionId()))
                                                .addField("regionName", StringUtil.toString(p.getRegionName()))
                                                .tag("vm_type", "all")
                                                .tag("platformId", StringUtil.toString(p.getId()))
                                                .addField("platformName", StringUtil.toString(p.getName()))
                                                .addField("vm_metricName", "NetworkOutPackets")
                                                .addField("type", "all")
                                                .addField("value", tx)
                                                .time(StringUtil.toLong(metric.get("time")), TimeUnit.SECONDS)
                                                .build();
                                        batchPoints.point(pointTx);

                                        Point pointTxDPkts = Point.measurement("zj_cloud_host")
                                                .tag("uuid",uuid)
                                                .tag("label","net")
                                                .tag("metricName", "NetworkOutErrors")
                                                .addField("productsName", name)
                                                .tag("regionId", StringUtil.toString(p.getRegionId()))
                                                .addField("regionName", StringUtil.toString(p.getRegionName()))
                                                .tag("vm_type", "all")
                                                .tag("platformId", StringUtil.toString(p.getId()))
                                                .addField("platformName", StringUtil.toString(p.getName()))
                                                .addField("vm_metricName", "NetworkOutErrors")
                                                .addField("type", "all")
                                                .addField("value", txDPkts)
                                                .time(StringUtil.toLong(metric.get("time")), TimeUnit.SECONDS)
                                                .build();
                                        batchPoints.point(pointTxDPkts);


                                        Point pointRx = Point.measurement("zj_cloud_host")
                                                .tag("uuid",uuid)
                                                .tag("label","net")
                                                .tag("metricName", "NetworkInPackets")
                                                .addField("productsName", name)
                                                .tag("regionId", StringUtil.toString(p.getRegionId()))
                                                .addField("regionName", StringUtil.toString(p.getRegionName()))
                                                .tag("vm_type", "all")
                                                .tag("platformId", StringUtil.toString(p.getId()))
                                                .addField("platformName", StringUtil.toString(p.getName()))
                                                .addField("vm_metricName", "NetworkInPackets")
                                                .addField("type", "all")
                                                .addField("value", rx)
                                                .time(StringUtil.toLong(metric.get("time")), TimeUnit.SECONDS)
                                                .build();
                                        batchPoints.point(pointRx);

                                        Point pointRxDPkts = Point.measurement("zj_cloud_host")
                                                .tag("uuid",uuid)
                                                .tag("label","net")
                                                .tag("metricName", "NetworkInErrors")
                                                .addField("productsName", name)
                                                .tag("regionId", StringUtil.toString(p.getRegionId()))
                                                .addField("regionName", StringUtil.toString(p.getRegionName()))
                                                .tag("vm_type", "all")
                                                .tag("platformId", StringUtil.toString(p.getId()))
                                                .addField("platformName", StringUtil.toString(p.getName()))
                                                .addField("vm_metricName", "NetworkInErrors")
                                                .addField("type", "all")
                                                .addField("value", rxDPkts)
                                                .time(StringUtil.toLong(metric.get("time")), TimeUnit.SECONDS)
                                                .build();
                                        batchPoints.point(pointRxDPkts);
                                }
                            }
                        }
                    }

                    log.info("batchPoints--->" + batchPoints);
                    int shardIndex = XxlJobHelper.getShardIndex();
                    int shardTotal = XxlJobHelper.getShardTotal();
                    log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
                    List<Point> s = StringUtil.getShardingData(batchPoints.getPoints(), shardTotal, shardIndex);
                    influxDBTemplate.writeBatch(BatchPoints.builder().points(s).build());
                }
            }
        }
    }





    @XxlJob("FusionOneLogin")
    public void loginInfo() {
        List<PlatformconfigDTO> platformconfigDTOList = new ArrayList<>();
        if (platformRedisDAO.get("platform") == null) {
            platformconfigDTOList = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform", platformconfigDTOList);
        }
        platformconfigDTOList = platformRedisDAO.get("platform");
        if (platformconfigDTOList.size() > 0) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                if (p.getTypeCode().equals("fusionOne")) {
                    HashMap<String, String> headers = new HashMap<>();
                    String host = p.getUrl().replace("http://", "").replace("https://", "");
                    headers.put("X-Auth-User", p.getUsername());
                    headers.put("X-Auth-Key", hash(p.getPassword()));
                    headers.put("X-Auth-UserType", "2");
                    headers.put("X-Auth-AuthType", "0");
                    headers.put("Host", host);
//                    headers.put("X-ENCRYPT-ALGORITHM", "1");
                    headers.put("Accept", "application/json;version=8.0;charset=UTF-8");
                    headers.put("Content-Type", "application/json; charset=UTF-8");
                    headers.put("Accept-Language", "zh_CN");
//                    String newUrl = p.getUrl().replace(":8443", ":7443");
                    try {
                        HttpRequest res = HttpRequest.post(p.getUrl() + FusionOneApiConstant.LOGIN)
                                .addHeaders(headers);
                        // 请求成功
                        if (200 == res.execute().getStatus()) {
                            HttpResponse response = res.execute();
                            String token = response.headers().get("X-Auth-Token").get(0);
                            log.info("token: {}", token);
                            JSONObject jsonObject = JSONObject.parseObject(response.body());
                            jsonObject.put("token", token);
                            fusionOneAccessTokenRedisDAO.set("FusionOne:" + p.getId(), jsonObject);
                            //获取站点id
                            HashMap<String, String> headerMap = new HashMap<>();
                            headerMap.put("Host", host);
                            headerMap.put("Accept", "application/json;version=8.0;charset=UTF-8");
                            headerMap.put("X-Auth-Token",token);
                            HttpRequest siteRes = HttpRequest.get(p.getUrl() + FusionOneApiConstant.SITE)
                                    .addHeaders(headerMap);
                            if (200 == siteRes.execute().getStatus()) {
                                HttpResponse siteResponse = siteRes.execute();
                                JSONObject json = JSONObject.parseObject(siteResponse.body());
                                JSONObject site = JSONObject.parseObject(json.getJSONArray("sites").get(0).toString());
                                String[] split = site.get("urn").toString().split(
                                                "urn:sites:");
                                jsonObject.put("siteId", split[1]);
                                jsonObject.put("siteName", site.get("name").toString());
                                fusionOneAccessTokenRedisDAO.set("FusionOne:" + p.getId(), jsonObject);
                            }
                        }
                    } catch (Exception e) {
                        log.info("获取授权异常：" + e.getMessage());
                    }
                }
            }
        }
    }

    public String hash(String input) {
        try {
            // 创建 SHA-256 的 MessageDigest 实例
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            // 进行哈希计算
            byte[] hashBytes = digest.digest(input.getBytes());
            // 将字节数组转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256 algorithm not found", e);
        }
    }
}
