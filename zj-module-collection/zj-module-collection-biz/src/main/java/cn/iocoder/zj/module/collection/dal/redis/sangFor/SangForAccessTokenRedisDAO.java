package cn.iocoder.zj.module.collection.dal.redis.sangFor;

import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.module.collection.dal.dataobject.zstack.AlarmConfigInfo;
import com.alibaba.fastjson.JSONObject;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.zj.module.collection.dal.redis.RedisKeyConstants.SANGFOR_ACCESS_TOKEN;
@Repository
public class SangForAccessTokenRedisDAO {
    @Resource
    private StringRedisTemplate stringRedisTemplate;


    public JSONObject get(String accessToken) {
        String redisKey = formatKey(accessToken);
        return JsonUtils.parseObject(stringRedisTemplate.opsForValue().get(redisKey), JSONObject.class);
    }

    public AlarmConfigInfo getAlarmInfo(String accessToken) {
        String redisKey = formatKey(accessToken);
        return JsonUtils.parseObject(stringRedisTemplate.opsForValue().get(redisKey), AlarmConfigInfo.class);
    }
    public void set(String accessToken, JSONObject loginInfo) {
        String redisKey = formatKey(accessToken);
        stringRedisTemplate.opsForValue().set(redisKey, JsonUtils.toJsonString(loginInfo),120, TimeUnit.SECONDS);
    }

    public void delete(String accessToken) {
        String redisKey = formatKey(accessToken);
        stringRedisTemplate.delete(redisKey);
    }

    private static String formatKey(String accessToken) {
        return String.format(SANGFOR_ACCESS_TOKEN, accessToken);
    }

}
