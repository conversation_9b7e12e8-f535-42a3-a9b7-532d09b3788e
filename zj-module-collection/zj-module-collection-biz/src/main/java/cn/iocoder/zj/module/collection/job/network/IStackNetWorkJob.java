package cn.iocoder.zj.module.collection.job.network;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.service.cloud.IStackDeviceService;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.monitor.api.network.NetworkApi;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL2DTO;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL3DTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class IStackNetWorkJob {


    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    PlatformRedisDAO platformRedisDAO;
    @Resource
    NetworkApi networkApi;
    @Autowired
    private IStackDeviceService iStackDeviceService;


    public void IStackNetWorkJob() {
        istackNetworkL2();
        istackNetworkL3();
    }


    @XxlJob("istackNetworkL2")
    public void istackNetworkL2() {
        List<NetWorkL2DTO> netWorkL2DTOS = new ArrayList<>();
        List<PlatformconfigDTO> platformconfigDTOList = new ArrayList<>();
        if (platformRedisDAO.get("platform") == null) {
            platformconfigDTOList = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform", platformconfigDTOList);
        }
        platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> vmwarePlats = new ArrayList<>();
        if (platformconfigDTOList.size() > 0) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                if (p.getTypeCode().equals("istack")) {
                    vmwarePlats.add(p);
                    JSONArray result = iStackDeviceService.getNetworks(p);
                    List<Map> networks = JSONObject.parseArray(result.toJSONString(), Map.class);
                    if (networks.size() > 0) {
                        for (Map network : networks) {
                            NetWorkL2DTO netWorkL2 = new NetWorkL2DTO();
                            netWorkL2.setName(network.get("name").toString());
                            netWorkL2.setUuid(network.get("uuid").toString());
                            netWorkL2.setPlatformName(p.getName());
                            netWorkL2.setPlatformId(p.getId());
                            netWorkL2.setRegionId(p.getRegionId());
                            netWorkL2.setRegionName(p.getRegionName());
                            if(network.get("network_type").toString().equals("vlan")){
                                netWorkL2.setType("L2VlanNetwork");
                            }else {
                                netWorkL2.setType("L2NoVlanNetwork");
                            }
                            netWorkL2.setTenantId(p.getTenantId());
                            netWorkL2.setTypeName("istack");
                            netWorkL2.setVlan(network.get("vlan_id").toString());
                            netWorkL2.setVirtualNetworkId(Integer.valueOf(network.get("vlan_id").toString()));
                            netWorkL2DTOS.add(netWorkL2);
                        }
                    }
                }
            }
        }
        if (netWorkL2DTOS.size() > 0) {
            // 获取当前节点的index 与 总节点数
            int shardIndex = XxlJobHelper.getShardIndex();
            int shardTotal = XxlJobHelper.getShardTotal();
            log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
            Long networkL2Count = networkApi.getNetWorkL2Count("istack");
            List<NetWorkL2DTO> netWorks = networkApi.getNetWorkL2List("istack").getData();
            List<NetWorkL2DTO> dtos = netWorks.stream()
                    .filter(item -> vmwarePlats.stream().map(PlatformconfigDTO::getId)
                            .collect(Collectors.toList()).contains(item.getPlatformId()))
                    .collect(Collectors.toList());
            List<NetWorkL2DTO> shardingData = new ArrayList<>();

            if (shardIndex < 0) {
                shardingData = netWorkL2DTOS;
            } else {
                shardingData = StringUtil.getShardingData(netWorkL2DTOS, shardTotal, shardIndex);
            }
            // 对分片数据进行业务处理
            for (NetWorkL2DTO item : shardingData) {
                // 模拟业务逻辑处理
                log.info("Processing item: " + item.getName());
            }
            if (networkL2Count == 0) {
                networkApi.addNetWorkL2(shardingData);
            } else {
                List<NetWorkL2DTO> collect2 = dtos.stream()
                        .filter(item -> !netWorkL2DTOS.stream().map(NetWorkL2DTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid()))
                        .collect(Collectors.toList());
                if (collect2.size() > 0) {
                    networkApi.deleteNetWorkL2ByNameList(collect2);
                }

                networkApi.updateNetWorkL2(shardingData);
                List<NetWorkL2DTO> collect = shardingData.stream()
                        .filter(item -> !dtos.stream().map(NetWorkL2DTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid()))
                        .collect(Collectors.toList());
                if (collect.size() > 0) {
                    networkApi.addNetWorkL2(collect);
                }
            }
        }
    }


    @XxlJob("istackNetworkL3")
    public void istackNetworkL3() {
        List<PlatformconfigDTO> platformconfigDTOList = new ArrayList<>();
        if (platformRedisDAO.get("platform") == null) {
            platformconfigDTOList = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform", platformconfigDTOList);
        }
        List<NetWorkL3DTO> netWorkL3DTOS = new ArrayList<>();
        platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> vmwarePlats = new ArrayList<>();
        if (platformconfigDTOList.size() > 0) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                if (p.getTypeCode().equals("istack")) {
                    vmwarePlats.add(p);
                    JSONArray result = iStackDeviceService.getNetworks3(p);
                    for (int j = 0; j < result.size(); j++) {
                        JSONObject network = result.getJSONObject(j);
                        NetWorkL3DTO netWorkL3DTO = new NetWorkL3DTO();
                        netWorkL3DTO.setUuid(network.getString("uuid"));
                        netWorkL3DTO.setName(network.getString("name"));
                        if(network.getJSONArray("dns_nameservers").size() > 0){
                            netWorkL3DTO.setDns(network.getJSONArray("dns_nameservers").get(0).toString());
                        }
                        netWorkL3DTO.setType("L3BasicNetwork");
                        String cidr = getNetworkFromCIDR(network.getString("cidr"));
                        netWorkL3DTO.setStartIp(cidr + ".1");
                        netWorkL3DTO.setEndIp(cidr + ".254");
                        netWorkL3DTO.setNetworkSegment(netWorkL3DTO.getStartIp()+"-"+netWorkL3DTO.getEndIp());
                        netWorkL3DTO.setGateway(network.getString("gateway_ip"));
                        netWorkL3DTO.setNetworkCidr(network.getString("cidr"));
                        JSONArray dhcp = iStackDeviceService.getNetDhcp(p,network.getString("uuid"));
                        if(CollectionUtil.isNotEmpty(dhcp)){
                            dhcp.forEach(item->{
                                JSONObject jsonItem = (JSONObject) item;
                                if(jsonItem.getString("device_owner").equals("network:dhcp")){
                                    netWorkL3DTO.setNextHopIp(jsonItem.getString("ipv4_address"));
                                }
                            });
                        }
                        netWorkL3DTO.setPlatformId(p.getId());
                        netWorkL3DTO.setPlatformName(p.getName());
                        netWorkL3DTO.setTenantId(p.getTenantId());
                        netWorkL3DTO.setCreateTime(DateUtil.parse(Convert.toStr(network.getString("create_time"))));
                        netWorkL3DTO.setTypeName("istack");
                        netWorkL3DTOS.add(netWorkL3DTO);
                    }
                }
            }
        }
        if (netWorkL3DTOS.size() > 0) {
            // 获取当前节点的index 与 总节点数
            int shardIndex = XxlJobHelper.getShardIndex();
            int shardTotal = XxlJobHelper.getShardTotal();
            log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
            Long networkL3Count = networkApi.getNetWorkL3Count("istack");
            List<NetWorkL3DTO> dtos = networkApi.getNetWorkL3List("istack").getData();
            List<NetWorkL3DTO> shardingData = new ArrayList<>();


            if (shardTotal < 0) {
                shardingData = netWorkL3DTOS;
            } else {
                shardingData = StringUtil.getShardingData(netWorkL3DTOS, shardTotal, shardIndex);
            }// 对分片数据进行业务处理
            for (NetWorkL3DTO item : shardingData) {
                // 模拟业务逻辑处理
                log.info("Processing item: " + item.getName());
            }
            if (networkL3Count == 0) {
                networkApi.addNetWorkL3(shardingData);
            } else {
                List<NetWorkL3DTO> collect3 = dtos.stream()
                        .filter(item -> !netWorkL3DTOS.stream().map(NetWorkL3DTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid()))
                        .collect(Collectors.toList());
                if (collect3.size() > 0) {
                    networkApi.deleteNetWorkL3ByNameList(collect3);
                }
                networkApi.updateNetWorkL3(shardingData);
                List<NetWorkL3DTO> collect = shardingData.stream()
                        .filter(item -> !dtos.stream().map(NetWorkL3DTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid()))
                        .collect(Collectors.toList());
                if (collect.size() > 0) {
                    networkApi.addNetWorkL3(collect);
                }
            }
        }

    }

    private String getNetworkFromCIDR(String cidr) {
        if (cidr == null || cidr.isEmpty()) {
            return "";
        }
        String ipPart = cidr.split("/")[0];
        String[] parts = ipPart.split("\\.");
        if (parts.length >= 3) {
            return parts[0] + "." + parts[1] + "." + parts[2];
        }
        return "";
    }
}
