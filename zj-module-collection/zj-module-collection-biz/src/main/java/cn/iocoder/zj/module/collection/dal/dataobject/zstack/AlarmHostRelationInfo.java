package cn.iocoder.zj.module.collection.dal.dataobject.zstack;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

@Data
public class AlarmHostRelationInfo {
    @TableId
    private Long id;
    /**
     * 告警配合名称
     */
    private String alarmName;
    /**
     * 告警配置ID
     */
    private Long alarmId;
    /**
     * 主机名称
     */
    private String hostName;
    /**
     * 云主机uuid
     */
    private String hostUuid;
    /**
     * 租户名称
     */
    private String tenantName;
    /**
     * 地区ID
     */
    private Long regionId;
    /**
     * 地区名称
     */
    private Long regionName;
    /**
     * 平台配置类型
     */
    private String platformConfigType;
    /**
     * 启用状态
     */
    private Integer status;
}
