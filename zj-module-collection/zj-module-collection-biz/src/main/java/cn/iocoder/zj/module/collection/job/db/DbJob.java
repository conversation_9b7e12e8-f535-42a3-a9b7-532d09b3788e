package cn.iocoder.zj.module.collection.job.db;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.iocoder.zj.framework.common.constants.FileTypeConstants;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.infra.api.file.FileApi;
import cn.iocoder.zj.module.om.api.dbfile.DbfileApi;
import cn.iocoder.zj.module.om.api.dbfile.dto.DbfileCreateReqDTO;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;

import static cn.iocoder.zj.module.collection.util.StringUtil.getSavePath;

/**
 * @ClassName : dbJob  //类名
 * @Description : 数据库备份  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/10/10  15:58
 */
@Component
@Slf4j
public class DbJob {

    /**
     * 用户名
     */
    @Value("${db.username}")
    private String root;

    /**
     * 密码
     */
    @Value("${db.password}")
    private String pwd;


    /**
     * 数据库名字
     */
    @Value("${db.dbname}")
    private String dbname;
    /**
     * ip
     *
     * @param args
     */
    @Value("${db.ip}")
    private String ip;
    @Resource
    FileApi fileApi;

    @Resource
    DbfileApi dbfileApi;

    String relativelyPath = System.getProperty("user.dir");

    /**
     * 备份路径
     */
    private static String WbackPath = "D:\\db\\";
    private static String LbackPath = "/home/";

    /**
     * 恢复路径
     */
    private static String filePath = "D:\\back.sql";
//	private static String filePath = "/home/<USER>";
    @Value("${minio.bucketName}")
    private String bucketName;

    /**
     * 备份数据库db
     */
    //
    @XxlJob("dbBackUp")
    public void dbBackUp() {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("YYYY-MM-dd-HH-mm-sss");
        String backName = dateFormat.format(date) + ".sql";
        String pathSql = "";
        //输出流文件
        File fileSql = new File(pathSql);
        try {
            //判断系统
            String osName = System.getProperties().getProperty("os.name");
            log.info("osName是什么系统：" + osName);
            if (osName.equals("Linux")) {
                pathSql = relativelyPath + "/upfile/" + backName;
            } else {
                pathSql = relativelyPath + "/upfile/" + backName;
            }

            //输出流文件
            fileSql = new File(pathSql);
            //创建备份sql文件
            if (!fileSql.exists()) {
                fileSql.getParentFile().mkdirs();
                try {
                    fileSql.createNewFile();
                } catch (IOException e) {
                    log.info("创建文件异常" + e.toString());
                    e.printStackTrace();
                }
            }

            log.info("-==[ 进入db！ ]==-");
            //mysqldump -hlocalhost -uroot -p123456 db > /home/<USER>
            //mysqldump -hlocalhost -uroot -p123456 数据库名  数据表名 > /home/<USER>
            StringBuffer sb = new StringBuffer();
            sb.append("mysqldump");
            sb.append(" -h" + ip);
            sb.append(" -u" + root);
            sb.append(" -p" + pwd);
            sb.append(" " + dbname + " ");
            sb.append("-r");//java中必须使用"-r"替代">"
            sb.append(pathSql);

            Process process = null;
            //判断系统
            if (osName.equals("Linux")) {
                log.info("-==[ 开始备份！ ]==-");
                BufferedReader bufferedReader = null;
                PrintWriter printWriter = new PrintWriter(new OutputStreamWriter(new FileOutputStream(fileSql), "utf8"));
                process = Runtime.getRuntime().exec(" mysqldump -h" + ip + " -u" + root + " -p" + pwd + " --set-charset=UTF8 " + dbname + " -r " + pathSql);
                log.info("这里面会出现异常数据么" + process);
                InputStreamReader inputStreamReader = new InputStreamReader(process.getInputStream(), "utf8");
                bufferedReader = new BufferedReader(inputStreamReader);
                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    printWriter.println(line);
                }
                printWriter.flush();
                log.info("-==[ 备份结束！ ]==-");
            } else {
                System.out.println("cmd命令为：" + "cmd /c " + sb.toString());
                process = Runtime.getRuntime().exec("cmd /c " + sb.toString());//执行语句
            }
            process.waitFor();//等待上述命令执行完毕后打印下面log
            // 获取文件流
            InputStream iss = new FileInputStream(pathSql);
            DbfileCreateReqDTO dto = new DbfileCreateReqDTO();
            dto.setName(backName);
            log.info("-==[ 获取文件！ ]==-" + iss);
            //输出错误信息
            InputStream is = process.getErrorStream();
            InputStreamReader isr = new InputStreamReader(is, "gbk");//读取
//            String path = minioUtil.upload(backName, iss, bucketName);
            String url = getSavePath(backName, FileTypeConstants.FILE_TYPE, "sql");
            String path = fileApi.createFileUrl(backName, url, IoUtil.readBytes(iss));
            dto.setPath(pathSql);
            dto.setUrl(path);
            dto.setSize(Convert.toInt(FileUtil.size(new File(pathSql))));
            System.out.println(isr.getEncoding());
            BufferedReader bufr = new BufferedReader(isr);//缓冲
            String line = null;
            while ((line = bufr.readLine()) != null) {
                System.out.println("error:" + line);
            }
            isr.close();
            iss.close();
            if (StringUtil.isNotEmpty(path)) {
                FileUtil.del(pathSql);
            }
            // 如果备份结果成功以后存入数据库
            if (process.exitValue() == 0) {
                dbfileApi.adds(dto);
            }
            log.info("数据库备份结束，备份结果：{}", process.exitValue() == 0 ? "success" : "fail");
        } catch (Exception e) {
            log.info("异常信息为:" + e.toString());
        }
    }

    // 文件恢复
//    @SneakyThrows
//    public void dbFile() {
//        InputStream i = minioUtil.getObject("zj-server-cloud", "/2023/10/11/346dcec2-e519-45b2-902c-244e7305e365.sql");
//
//        //输出流文件
//        File fileSqls = new File(relativelyPath + "/dowfile/" + "123.sql");
//        //创建备份sql文件
//        if (!fileSqls.exists()) {
//            fileSqls.getParentFile().mkdirs();
//            try {
//                fileSqls.createNewFile();
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
//        try (OutputStream os = new FileOutputStream(fileSqls)) {
//            byte[] buffer = new byte[4096];
//            int bytesRead;
//            while ((bytesRead = i.read(buffer)) != -1) {
//                os.write(buffer, 0, bytesRead);
//            }
//        }
//
//        i.close();
//    }

    /**
     * 恢复数据库
     * mysql -hlocalhost -uroot -proot dbname < /home/<USER>
     */
    public void dbRestore() {
        StringBuilder sb = new StringBuilder();
        sb.append("mysql");
        sb.append(" -h" + "**************");
        sb.append(" -u" + "zjiecn");
        sb.append(" -p" + "zjiecn@123456");
        sb.append(" " + "zjiecn@2023" + " <");
        sb.append(filePath);
        System.out.println("cmd命令为：" + sb.toString());
        Runtime runtime = Runtime.getRuntime();
        System.out.println("开始还原数据");
        try {
            Process process = runtime.exec("cmd /c" + sb.toString());

            //输出错误信息
            FileInputStream errorStream = (FileInputStream) process.getErrorStream();
            InputStreamReader isr = new InputStreamReader(errorStream, "gbk");//读取
            System.out.println(isr.getEncoding());
            BufferedReader bufr = new BufferedReader(isr);//缓冲
            String line = null;
            while ((line = bufr.readLine()) != null) {
                System.out.println("error:" + line);
            }
            isr.close();
            InputStream is = process.getInputStream();
            BufferedReader bf = new BufferedReader(new InputStreamReader(is, "utf8"));
            line = null;
            while ((line = bf.readLine()) != null) {
                System.out.println(line);
            }
            is.close();
            bf.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        System.out.println("还原成功！");
    }
}
