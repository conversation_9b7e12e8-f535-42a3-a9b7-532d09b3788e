package cn.iocoder.zj.module.collection.service.storage;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.iocoder.zj.module.collection.handler.HttpClientWrapper;
import cn.iocoder.zj.module.collection.service.zstack.core.ZstackApiConstant;
import cn.iocoder.zj.module.collection.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.IdentityHashMap;
import java.util.Map;

/**
 * @ClassName : ZstackStorageServiceImpl  //类名
 * @Description : 存储系统相关实现  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/25  15:05
 */



@Slf4j
@Service
public class ZstackStorageServiceImpl implements ZstackStorageService {
    @Autowired
    private HttpClientWrapper httpClientWrapper;
    @Override
    public String storageInfo(String url, String token) {
        HttpRequest request = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_STORAGE)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8");
        HttpResponse result = httpClientWrapper.execute(request);
        if (result.getStatus() != 200) {
            throw new RuntimeException("getstorageInfo error");
        }
        return result.body();
    }

    @Override
    public String availablePhysicalCapacityInPercent(String url, String token) {
        Map<String, String> header = new HashMap<>();
        IdentityHashMap<String, Object> p = new IdentityHashMap<>();
        p.put("metricName", "AvailablePhysicalCapacityInPercent");
        p.put("namespace", "ZStack/PrimaryStorage");
        header.put("Authorization", "OAuth " + token);
        header.put("Content-Type", "application/json;charset=UTF-8");

        HttpResponse result = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_METRICS)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .form(p)
                .execute();
        if (result.getStatus() != 200) {
            throw new RuntimeException("getavailablePhysicalCapacityInPercent error");
        }
        return result.body();
    }

    @Override
    public String totalUsedCapacityInBytes(String url, String token) {
        Map<String, String> header = new HashMap<>();
        IdentityHashMap<String, Object> p = new IdentityHashMap<>();
        p.put("metricName", "TotalUsedCapacityInBytes");
        p.put("namespace", "ZStack/PrimaryStorage");
        header.put("Authorization", "OAuth " + token);
        header.put("Content-Type", "application/json;charset=UTF-8");

        HttpResponse result = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_METRICS)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .form(p)
                .execute();
        if (result.getStatus() != 200) {
            throw new RuntimeException("getTotalUsedCapacityInBytes error");
        }
        return result.body();
    }

    /**
     * @description:  主存储可用容量
     * <AUTHOR>
     * @date 2023/6/6 17:10
     * @version 1.0
     */
    @Override
    public String availableCapacityInBytes(String url, String token) {
        Map<String, String> header = new HashMap<>();
        IdentityHashMap<String, Object> p = new IdentityHashMap<>();
        p.put("metricName", "AvailableCapacityInBytes");
        p.put("namespace", "ZStack/PrimaryStorage");
        header.put("Authorization", "OAuth " + token);
        header.put("Content-Type", "application/json;charset=UTF-8");

        HttpResponse result = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_METRICS)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .form(p)
                .execute();
        if (result.getStatus() != 200) {
            throw new RuntimeException("getavailableCapacityInBytes error");
        }
        return result.body();
    }

    /**
     * @description:  主存储总容量
     * <AUTHOR>
     * @date 2023/6/6 17:10
     * @version 1.0
     */
    @Override
    public String totalPhysicalCapacityInBytes(String url, String token) {
        Map<String, String> header = new HashMap<>();
        IdentityHashMap<String, Object> p = new IdentityHashMap<>();
        p.put("metricName", "TotalPhysicalCapacityInBytes");
        p.put("namespace", "ZStack/PrimaryStorage");
        header.put("Authorization", "OAuth " + token);
        header.put("Content-Type", "application/json;charset=UTF-8");

        HttpRequest request = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_METRICS)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .form(p);
        HttpResponse result = httpClientWrapper.execute(request);
        if (result.getStatus() != 200) {
            throw new RuntimeException("gettotalPhysicalCapacityInBytes error");
        }
        return result.body();
    }

    /**
     * @description: 主存储可用容量百分比
     * <AUTHOR>
     * @date 2023/6/7 16:19
     * @version 1.0
     */
    @Override
    public String availableCapacityInPercent(String url, String token) {
        Map<String, String> header = new HashMap<>();
        IdentityHashMap<String, Object> p = new IdentityHashMap<>();
        p.put("metricName", "AvailableCapacityInPercent");
        p.put("namespace", "ZStack/PrimaryStorage");
        header.put("Authorization", "OAuth " + token);
        header.put("Content-Type", "application/json;charset=UTF-8");

        HttpResponse result = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_METRICS)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .form(p)
                .execute();
        if (result.getStatus() != 200) {
            throw new RuntimeException("gettotalPhysicalCapacityInBytes error");
        }
        return result.body();
    }

    /**
     * @description: 主存储已用容量
     * <AUTHOR>
     * @date 2023/6/7 16:18
     * @version 1.0
     */
    @Override
    public String usedCapacityInBytes(String url, String token, String uuid) {
        Map<String, String> header = new HashMap<>();
        IdentityHashMap<String, Object> p = new IdentityHashMap<>();
        p.put("metricName", "UsedCapacityInBytes");
        p.put("namespace", "ZStack/PrimaryStorage");
        if (StringUtil.isNotEmpty(uuid)){
            p.put("labels","PrimaryStorageUuid="+uuid);
        }
        header.put("Authorization", "OAuth " + token);
        header.put("Content-Type", "application/json;charset=UTF-8");

        HttpRequest request = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_METRICS)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .form(p);

        HttpResponse result = httpClientWrapper.execute(request);
        if (result.getStatus() != 200) {
            throw new RuntimeException("gettotalPhysicalCapacityInBytes error");
        }
        return result.body();
    }

    /**
     * @description:  已用容量百分比
     * <AUTHOR>
     * @date 2023/6/7 16:18
     * @version 1.0
     */
    @Override
    public String usedCapacityInPercent(String url, String token,String uuid) {
        Map<String, String> header = new HashMap<>();
        IdentityHashMap<String, Object> p = new IdentityHashMap<>();
        p.put("metricName", "UsedCapacityInPercent");
        p.put("namespace", "ZStack/PrimaryStorage");
        if (StringUtil.isNotEmpty(uuid)){
            p.put("labels","PrimaryStorageUuid="+uuid);
        }
        p.put("period",3);
        header.put("Authorization", "OAuth " + token);
        header.put("Content-Type", "application/json;charset=UTF-8");

        HttpRequest request = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_METRICS)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .form(p);
        HttpResponse result = httpClientWrapper.execute(request);
        if (result.getStatus() != 200) {
            throw new RuntimeException("gettotalPhysicalCapacityInBytes error");
        }
        return result.body();
    }

    /**
     * @description: 主存储可用物理容量
     * <AUTHOR>
     * @date 2023/6/7 16:17
     * @version 1.0
     */
    @Override
    public String availablePhysicalCapacityInBytes(String url, String token) {
        Map<String, String> header = new HashMap<>();
        IdentityHashMap<String, Object> p = new IdentityHashMap<>();
        p.put("metricName", "AvailablePhysicalCapacityInBytes");
        p.put("namespace", "ZStack/PrimaryStorage");
        header.put("Authorization", "OAuth " + token);
        header.put("Content-Type", "application/json;charset=UTF-8");

        HttpResponse result = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_METRICS)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .form(p)
                .execute();
        if (result.getStatus() != 200) {
            throw new RuntimeException("gettotalPhysicalCapacityInBytes error");
        }
        return result.body();
    }
}
