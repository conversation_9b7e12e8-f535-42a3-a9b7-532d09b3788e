package cn.iocoder.zj.module.collection.job.image;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.util.SampleUtil;
import cn.iocoder.zj.module.monitor.api.hostinfo.HostInfoApi;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.imageinfo.ImageInfoApi;
import cn.iocoder.zj.module.monitor.api.imageinfo.dto.ImageInfoCreateReqDTO;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.vmware.vim25.mo.ServiceInstance;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.iocoder.zj.module.collection.service.vmware.host.HostComputerResourceSummary.getVMImages;

@Component
@Slf4j
public class VmwareImageJob {
    @Resource
    PlatformRedisDAO platformRedisDAO;

    @Resource
    ImageInfoApi imageInfoApi;

    @Resource
    HostInfoApi hostInfoApi;

    private static final String PLATFORM_TYPE = "vmware";
    private static final String IMAGE_FORMAT = "vmdk";
    private static final String IMAGE_STATUS = "Enabled";
    private static final String IMAGE_TYPE = "RootVolumeTemplate";

    @XxlJob("vmwareImageJob")
    public void vmwareImageInfo() {

        List<PlatformconfigDTO> vmwarePlatforms = platformRedisDAO.get("platform").stream()
                .filter(dto -> PLATFORM_TYPE.equals(dto.getTypeCode()))
                .collect(Collectors.toList());
        if (vmwarePlatforms.isEmpty()) {
            return;
        }

        for (PlatformconfigDTO platform : vmwarePlatforms) {
            if (platform.getId() == 18) {
                continue;
            }
            handlePlatform(platform);

        }
    }

    private void handlePlatform(PlatformconfigDTO platform) {
        try {
            // 获取VMware镜像列表
            ServiceInstance serviceInstance = SampleUtil.createServiceInstance(
                    platform.getUrl(),
                    platform.getUsername(),
                    platform.getPassword()
            );
            List<Map<String, Object>> vmImages = getVMImages(serviceInstance);
            if (vmImages == null || vmImages.isEmpty()) {
                return;
            }

            List<HostInfoRespCreateReqDTO> hostInfoApiVmByPlatformId = hostInfoApi.getVmByPlatformId(platform.getId());
            Map<String, HostInfoRespCreateReqDTO> hostVmsMap = Optional.ofNullable(hostInfoApiVmByPlatformId)
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(host -> StrUtil.isNotEmpty(host.getVms()))
                    .collect(Collectors.toMap(
                            HostInfoRespCreateReqDTO::getVms,
                            Function.identity(),
                            (existing, replacement) -> {
                                return existing;
                            },
                            HashMap::new
                    ));

            List<ImageInfoCreateReqDTO> imageInfoDTOs = convertToImageInfoDTOs(vmImages, platform, hostVmsMap);
            if (imageInfoDTOs.isEmpty()) {
                return;
            }

            syncImageInfo(imageInfoDTOs,platform);

        } catch (Exception e) {
            log.error("处理平台 [{}] 镜像同步失败: {}", platform.getName(), e.getMessage(), e);
        }
    }

    private List<ImageInfoCreateReqDTO> convertToImageInfoDTOs(List<Map<String, Object>> vmImages, PlatformconfigDTO platform, Map<String, HostInfoRespCreateReqDTO> hostVmsMap) {
        return vmImages.stream().map(vmImage -> {
            ImageInfoCreateReqDTO imageInfoDTO = new ImageInfoCreateReqDTO();
            imageInfoDTO.setUuid(vmImage.get("uuid").toString());
            imageInfoDTO.setName(vmImage.get("name").toString());
            imageInfoDTO.setStatus(IMAGE_STATUS);
            imageInfoDTO.setFormat(IMAGE_FORMAT);
            imageInfoDTO.setCpuArch(vmImage.get("cpuArch").toString());

            String osType = Objects.toString(vmImage.get("osType"), "").toLowerCase();
            imageInfoDTO.setOsType(vmImage.get("osType").toString());
            imageInfoDTO.setApplicationPlatform(determineApplicationPlatform(osType));
            imageInfoDTO.setImageType(IMAGE_TYPE);
            imageInfoDTO.setSharingScope("不共享");

            Date createTime = parseCreateTime(vmImage.get("createTime"));
            imageInfoDTO.setVCreateDate(DateUtil.toLocalDateTime(createTime));
            imageInfoDTO.setVUpdateDate(DateUtil.toLocalDateTime(createTime));

            imageInfoDTO.setOsLanguage("");
            imageInfoDTO.setMinMemory(BigDecimal.valueOf(Long.parseLong(vmImage.get("minMemory").toString())));
            imageInfoDTO.setMinDisk(BigDecimal.valueOf(Long.parseLong(vmImage.get("minDisk").toString())));
            imageInfoDTO.setDiskDriver("");
            imageInfoDTO.setNetworkDriver("");
            imageInfoDTO.setBootMode("");
            imageInfoDTO.setRemoteProtocol("");

            imageInfoDTO.setPlatformId(platform.getId());
            imageInfoDTO.setPlatformName(platform.getName());
            HostInfoRespCreateReqDTO hostUuid = hostVmsMap.get(vmImage.get("hostUuid").toString());
            if (BeanUtil.isNotEmpty(hostUuid)) {
                imageInfoDTO.setHostUuid(hostUuid.getUuid());
                imageInfoDTO.setHostName(hostUuid.getName());
            }
            imageInfoDTO.setDeleted(0L);
            return imageInfoDTO;
        }).collect(Collectors.toList());
    }

    private void syncImageInfo(List<ImageInfoCreateReqDTO> newImageInfos, PlatformconfigDTO platform) {
        try {
            List<ImageInfoCreateReqDTO> existingImages = imageInfoApi.getImageInfoByPlatformId(platform.getId()).getData();
            if (existingImages == null || existingImages.isEmpty()) {
                imageInfoApi.batchCreateImageInfo(newImageInfos);
                return;
            }
            // 计算需要新增、更新和删除的镜像
            Map<String, List<ImageInfoCreateReqDTO>> diffResult = calculateImageDiff(newImageInfos, existingImages);
            executeSyncOperations(diffResult);
        } catch (Exception e) {
            log.error("同步镜像信息失败: {}", e.getMessage(), e);
        }
    }

    private Map<String, List<ImageInfoCreateReqDTO>> calculateImageDiff(
            List<ImageInfoCreateReqDTO> newImages,
            List<ImageInfoCreateReqDTO> existingImages) {

        Map<String, List<ImageInfoCreateReqDTO>> result = new HashMap<>();
        // 计算需要新增的镜像
        result.put("create", newImages.stream()
                .filter(newImage -> existingImages.stream()
                        .noneMatch(existing -> existing.getUuid().equals(newImage.getUuid())))
                .collect(Collectors.toList()));
        // 计算需要更新的镜像
        result.put("update", newImages.stream()
                .filter(newImage -> existingImages.stream()
                        .anyMatch(existing -> existing.getUuid().equals(newImage.getUuid())))
                .collect(Collectors.toList()));
        // 计算需要删除的镜像
        result.put("delete", existingImages.stream()
                .filter(existing -> newImages.stream()
                        .noneMatch(newImage -> newImage.getUuid().equals(existing.getUuid())))
                .collect(Collectors.toList()));
        return result;
    }

    private void executeSyncOperations(Map<String, List<ImageInfoCreateReqDTO>> diffResult) {
        List<ImageInfoCreateReqDTO> toCreate = diffResult.get("create");
        List<ImageInfoCreateReqDTO> toUpdate = diffResult.get("update");
        List<ImageInfoCreateReqDTO> toDelete = diffResult.get("delete");
        if (!toCreate.isEmpty()) {
            imageInfoApi.batchCreateImageInfo(toCreate);
        }
        if (!toUpdate.isEmpty()) {
            imageInfoApi.batchUpdateImageInfo(toUpdate);
        }
        if (!toDelete.isEmpty()) {
            imageInfoApi.batchDeleteImageInfo(toDelete);
        }
    }

    private String determineApplicationPlatform(String osType) {
        if (osType.contains("linux") || osType.contains("centos")) {
            return "Linux";
        } else if (osType.contains("windows")) {
            return "Windows";
        }
        return "";
    }

    private Date parseCreateTime(Object createTimeObj) {
        if (createTimeObj instanceof Long) {
            return new Date((Long) createTimeObj);
        } else if (createTimeObj instanceof Date) {
            return (Date) createTimeObj;
        }
        return new Date();
    }
}
