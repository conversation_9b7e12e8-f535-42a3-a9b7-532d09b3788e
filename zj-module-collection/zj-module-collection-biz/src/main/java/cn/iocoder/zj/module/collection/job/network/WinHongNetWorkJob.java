package cn.iocoder.zj.module.collection.job.network;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.winhong.WinHongAccessTokenRedisDAO;
import cn.iocoder.zj.module.collection.service.cloud.IWinHongDeviceService;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.monitor.api.network.NetworkApi;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL2DTO;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL3DTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Component
@Slf4j
public class WinHongNetWorkJob {


    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    PlatformRedisDAO platformRedisDAO;
    @Resource
    NetworkApi networkApi;

    @Resource
    WinHongAccessTokenRedisDAO winHongAccessTokenRedisDAO;

    @Autowired
    private IWinHongDeviceService winHongDeviceService;


    public void winHongNetWorkJob() {
        winHongnNetworkL2();
        winHongNetworkL3();
    }


//    @XxlJob("winHongnNetworkL2")
    public void winHongnNetworkL2() {
        List<NetWorkL2DTO> netWorkL2DTOS = new ArrayList<>();
        List<PlatformconfigDTO> platformconfigDTOList = new ArrayList<>();
        if (platformRedisDAO.get("platform") == null) {
            platformconfigDTOList = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform", platformconfigDTOList);
        }
        platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> vmwarePlats = new ArrayList<>();
        if (platformconfigDTOList.size() > 0) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                if (p.getTypeCode().equals("winhong") && winHongAccessTokenRedisDAO.get("winhong:" + p.getId()) != null) {
                    vmwarePlats.add(p);
                    String token = winHongDeviceService.getToken(p);
                    if (token==null){
                        log.info("获取云宏token失败");
                        return;
                    }
                    JSONArray result = winHongDeviceService.getNetworks(p, token);
                    List<Map> networks = JSONObject.parseArray(result.toJSONString(), Map.class);
                    if (networks.size() > 0) {
                        for (Map network : networks) {
                            NetWorkL2DTO netWorkL2 = new NetWorkL2DTO();
                            netWorkL2.setName(network.get("name").toString());
                            netWorkL2.setUuid(network.get("id").toString());
                            netWorkL2.setPlatformName(p.getName());
                            netWorkL2.setPlatformId(p.getId());
                            netWorkL2.setRegionId(p.getRegionId());
                            netWorkL2.setRegionName(p.getRegionName());
                            netWorkL2.setType(network.get("netType").toString());
                            netWorkL2.setTenantId(p.getTenantId());
                            netWorkL2.setTypeName("winHong");
                            netWorkL2.setVlan(network.get("segmentationId") != null ? network.get("segmentationId").toString() : "");
                            netWorkL2DTOS.add(netWorkL2);
                        }
                    }
                }
            }
        }
        if (netWorkL2DTOS.size() > 0) {
            // 获取当前节点的index 与 总节点数
            int shardIndex = XxlJobHelper.getShardIndex();
            int shardTotal = XxlJobHelper.getShardTotal();
            log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
            Long networkL2Count = networkApi.getNetWorkL2Count("winhong");
            List<NetWorkL2DTO> netWorks = networkApi.getNetWorkL2List("winhong").getData();
            List<NetWorkL2DTO> dtos = netWorks.stream()
                    .filter(item -> vmwarePlats.stream().map(PlatformconfigDTO::getId)
                            .collect(Collectors.toList()).contains(item.getPlatformId()))
                    .collect(Collectors.toList());
            List<NetWorkL2DTO> shardingData = new ArrayList<>();

            if (shardIndex < 0) {
                shardingData = netWorkL2DTOS;
            } else {
                shardingData = StringUtil.getShardingData(netWorkL2DTOS, shardTotal, shardIndex);
            }
            // 对分片数据进行业务处理
            for (NetWorkL2DTO item : shardingData) {
                // 模拟业务逻辑处理
                log.info("Processing item: " + item.getName());
            }
            if (networkL2Count == 0) {
                networkApi.addNetWorkL2(shardingData);
            } else {
                List<NetWorkL2DTO> collect2 = dtos.stream()
                        .filter(item -> !netWorkL2DTOS.stream().map(NetWorkL2DTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid()))
                        .collect(Collectors.toList());
                if (collect2.size() > 0) {
                    networkApi.deleteNetWorkL2ByNameList(collect2);
                }

                networkApi.updateNetWorkL2(shardingData);
                List<NetWorkL2DTO> collect = shardingData.stream()
                        .filter(item -> !dtos.stream().map(NetWorkL2DTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid()))
                        .collect(Collectors.toList());
                if (collect.size() > 0) {
                    networkApi.addNetWorkL2(collect);
                }
            }
        }
    }


//    @XxlJob("winHongNetworkL3")
    public void winHongNetworkL3() {
        List<NetWorkL2DTO> netWorkL2DTOS = new ArrayList<>();
        List<PlatformconfigDTO> platformconfigDTOList = new ArrayList<>();
        if (platformRedisDAO.get("platform") == null) {
            platformconfigDTOList = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform", platformconfigDTOList);
        }
        List<NetWorkL3DTO> netWorkL3DTOS = new ArrayList<>();
        platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> vmwarePlats = new ArrayList<>();
        if (platformconfigDTOList.size() > 0) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                if (p.getTypeCode().equals("winhong") && winHongAccessTokenRedisDAO.get("winhong:" + p.getId()) != null) {
                    vmwarePlats.add(p);
                    String token = winHongDeviceService.getToken(p);
                    if (token==null){
                        log.info("获取云宏token失败");
                        return;
                    }
                    JSONArray result = winHongDeviceService.getNetworks(p, token);
                    List<Map> networks = JSONObject.parseArray(result.toJSONString(), Map.class);
                    for (Map network : networks) {

                        NetWorkL3DTO netWorkL3DTO = new NetWorkL3DTO();
                        netWorkL3DTO.setUuid(UUID.randomUUID().toString());
                        netWorkL3DTO.setL2NetworkUuid(network.get("id").toString());
                        netWorkL3DTO.setName(network.get("name").toString());
                        netWorkL3DTO.setL2NetworkName(network.get("name").toString());
//                        netWorkL3DTO.setDns(dns);
                        netWorkL3DTO.setType("L3BasicNetwork");
//                        netWorkL3DTO.setNetworkServices(networkServices);
                        String[] ipRanges=new String[2];
                        if (network.get("ipRange")!=null && StringUtil.isNotEmpty(network.get("ipRange").toString())){
                            ipRanges = network.get("ipRange").toString().split("-");
                        }else {
                            ipRanges = generateStartEndIP(network.get("gateway").toString());
                        }
                        netWorkL3DTO.setStartIp(ipRanges[0]);
                        netWorkL3DTO.setEndIp(ipRanges[1]);
//                        netWorkL3DTO.setNetmask(netmask);
                        netWorkL3DTO.setGateway(network.get("gateway").toString());
                        netWorkL3DTO.setNetworkSegment(network.get("ipRange")!=null?network.get("ipRange").toString(): String.join("-", ipRanges));
                        if (network.get("ipVersion").toString().equals("4")){
                            netWorkL3DTO.setNetworkCidr(network.get("cidr").toString());
                        }
//                        netWorkL3DTO.setNextHopIp(next_hop_ip);
                        netWorkL3DTO.setPlatformId(p.getId());
                        netWorkL3DTO.setPlatformName(p.getName());
                        netWorkL3DTO.setTenantId(p.getTenantId());
                        netWorkL3DTO.setCreateTime(DateUtil.parse(Convert.toStr(network.get("createTime"))));
                        netWorkL3DTO.setTypeName("winHong");
                        netWorkL3DTOS.add(netWorkL3DTO);
                    }
                }
            }
        }
        if (netWorkL3DTOS.size() > 0) {
            // 获取当前节点的index 与 总节点数
            int shardIndex = XxlJobHelper.getShardIndex();
            int shardTotal = XxlJobHelper.getShardTotal();
            log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
            Long networkL3Count = networkApi.getNetWorkL3Count("winHong");
            List<NetWorkL3DTO> dtos = networkApi.getNetWorkL3List("winHong").getData();
            List<NetWorkL3DTO> shardingData = new ArrayList<>();


            if (shardTotal < 0) {
                shardingData = netWorkL3DTOS;
            } else {
                shardingData = StringUtil.getShardingData(netWorkL3DTOS, shardTotal, shardIndex);
            }// 对分片数据进行业务处理
            for (NetWorkL3DTO item : shardingData) {
                // 模拟业务逻辑处理
                log.info("Processing item: " + item.getName());
            }
            if (networkL3Count == 0) {
                networkApi.addNetWorkL3(shardingData);
            } else {
                List<NetWorkL3DTO> collect3 = dtos.stream()
                        .filter(item -> !netWorkL3DTOS.stream().map(NetWorkL3DTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid()))
                        .collect(Collectors.toList());
                if (collect3.size() > 0) {
                    networkApi.deleteNetWorkL3ByNameList(collect3);
                }
                networkApi.updateNetWorkL3(shardingData);
                List<NetWorkL3DTO> collect = shardingData.stream()
                        .filter(item -> !dtos.stream().map(NetWorkL3DTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid()))
                        .collect(Collectors.toList());
                if (collect.size() > 0) {
                    networkApi.addNetWorkL3(collect);
                }
            }
        }

    }



    public String[]  generateStartEndIP(String gateway) {
        // 分割传入的网关地址
        String[] parts = gateway.split("\\.");

        try {
            // 获取前三段作为IP前缀
            String prefix = parts[0] + "." + parts[1] + "." + parts[2] + ".";

            // 固定规则：起始IP为前缀 + 1，结束IP为前缀 + 253
            String startIP = prefix + "1";
            String endIP = prefix + "253";

            // 输出结果
            System.out.println("网关: " + gateway + " -> 起始IP: " + startIP + ", 结束IP: " + endIP);
            return new String[] { startIP, endIP };
        } catch (NumberFormatException e) {
            System.out.println("无效的网关地址: " + gateway);
        }
        return new String[] { "", "" };
    }
}
