package cn.iocoder.zj.module.collection.service.vmware.perf;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.iocoder.zj.module.collection.dal.dataobject.vmware.PerMonitorDO;
import cn.iocoder.zj.module.collection.util.SampleUtil;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.vmware.vim25.*;
import com.vmware.vim25.mo.InventoryNavigator;
import com.vmware.vim25.mo.ManagedEntity;
import com.vmware.vim25.mo.PerformanceManager;
import com.vmware.vim25.mo.ServiceInstance;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.rmi.RemoteException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @ClassName : RealtimePerfMonitor  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/7/24  17:02
 */

@Slf4j
public class RealtimePerfMonitor {

    private static final Map<String, Integer> counterCache = new ConcurrentHashMap<>();
    private static final Map<String, Map<String, Integer>> perCounterList = new ConcurrentHashMap<>();



    public static void main(String[] args) throws Exception {
        ServiceInstance si = SampleUtil.createServiceInstance("https://*************:10002", "<EMAIL>", "Lihulin@123");

        ManagedEntity[] managedEntities = new InventoryNavigator(si.getRootFolder())
                .searchManagedEntities("VirtualMachine");
        String vmname = "";
        for (ManagedEntity managedEntity : managedEntities) {
            vmname = managedEntity.getName();
            PlatformconfigDTO platformconfigDTO = new PlatformconfigDTO();
            platformconfigDTO.setId(43L);

            List<String> counterNames = Arrays.asList(

                    "cpu.usage.none", "cpu.usage.average", "cpu.idle.summation", "cpu.utilization.average",

                    "mem.usage.average", "mem.active.average","mem.active.average",

                    "disk.usage.average", "virtualDisk.write.average", "virtualDisk.read.average", "virtualDisk.numberReadAveraged.average", "virtualDisk.numberWriteAveraged.average",

                    "net.bytesTx.average", "net.bytesRx.average", "net.packetsRx.summation", "net.packetsTx.summation", "net.droppedRx.summation", "net.droppedTx.summation"
            );
            Map<String, List<PerMonitorDO>> metrics = RealtimePerfMonitor.getPerEntityMetricsByNamesTest(
                    "gpuvdi6", si, managedEntity, counterNames);
//            while (true) {
//                getPerEntityMetricsByNames("Client-MES-08", si, managedEntity, "virtualDisk.write.average",platformconfigDTO);
//                System.out.println("Sleeping 60 seconds...");
//                Thread.sleep(10 * 3 * 1000);
//            }
            List<PerMonitorDO> metricData  =     metrics.get("virtualDisk.write.average");
           try {
               for (PerMonitorDO perMonitorDO : metricData) {
                   if (perMonitorDO.getInstance().isEmpty()) {
                       perMonitorDO.setInstance("0");
                   }
                   BigDecimal value = new BigDecimal(0);
                   if (perMonitorDO.getValue() > 0) {
                       value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 8);
                   }
                   BigDecimal lastValue = NumberUtil.mul(value, 1024);
                   String vmType = perMonitorDO.getInstance();
                   String type = perMonitorDO.getInstance();
                   Long time = perMonitorDO.getDateTime().getTime() / 1000;

               }
           }catch (Exception e){
               e.getMessage();
           }

        }


        ManagedEntity vm = new InventoryNavigator(
                si.getRootFolder()).searchManagedEntity(
                "VirtualMachine", vmname);

        if (vm == null) {
            System.out.println("Virtual Machine " + vmname
                    + " cannot be found.");
            si.getServerConnection().logout();
            return;
        }


        PerformanceManager perfMgr = si.getPerformanceManager();

        String counterName = "disk.read.average";

        Integer counterIds = 0;
        Map<String, Integer> counterKeyToNameMaps = perCounterList.get("43");

        if (counterKeyToNameMaps == null) {
            PerfCounterInfo[] d = perfMgr.getPerfCounter();
            Map<String, Integer> map = new HashMap<>();
            for (PerfCounterInfo counter : d) {
                String fullName = counter.getGroupInfo().getKey() + "." + counter.getNameInfo().getKey() + "." + counter.getRollupType();
                map.put(fullName, counter.getKey());
                if (counterName.equals(fullName)) {
                    counterIds = counter.getKey();
                }
            }
            perCounterList.put("1", map);
        } else if (counterKeyToNameMaps != null) {
            counterIds = counterKeyToNameMaps.get(counterName);
        }


        // find out the refresh interval for the virtual machine
        // use this interval to retrieve real time performance data
        // the interval must be one of 300, 1800, 7200 and 86400
        // see http://pubs.vmware.com/vsphere-60/index.jsp?topic=%2Fcom.vmware.wssdk.apiref.doc%2Fvim.HistoricalInterval.html
        PerfProviderSummary pps = perfMgr.queryPerfProviderSummary(vm);
        int refreshInterval = pps.getRefreshRate().intValue();
//        System.out.println("Current refresh interval is " + refreshInterval);


        // 获取所有性能计数器信息
        PerfCounterInfo[] perfCounters = perfMgr.getPerfCounter();
        // 创建一个映射从counter key到完整名称
        Map<Integer, String> counterKeyToNameMap = new HashMap<>();
        for (PerfCounterInfo counter : perfCounters) {
            String fullName = counter.getGroupInfo().getKey() + "." + counter.getNameInfo().getKey() + "." + counter.getRollupType();
            counterKeyToNameMap.put(counter.getKey(), fullName);
        }


        // retrieve all the available perf metrics for vm
        PerfMetricId[] pmis = perfMgr.queryAvailablePerfMetric(
                vm, null, null, refreshInterval);

        // 查找指定的性能计数器
        String desiredMetricName = "disk.read.average";
        Integer counterId = null;

        // 在虚拟机的可用性能指标中查找指定的性能指标
        for (PerfMetricId pmi : pmis) {
            if (desiredMetricName.equals(counterKeyToNameMap.get(pmi.getCounterId()))) {
                counterId = pmi.getCounterId();
                break;
                // 如果没有找到直接返回异常信息
            }
        }

        if (counterId != null) {
            System.out.println("CounterId for " + desiredMetricName + " is: " + counterId);
        } else {
            System.out.println("Counter not found for: " + desiredMetricName);
        }

        // 筛选出指定的 CounterId
        List<PerfMetricId> selectedMetrics = new ArrayList<>();
        for (PerfMetricId pmi : pmis) {
            System.out.println(pmi.getCounterId() + "instance" + pmi.getInstance());
        }


        PerfMetricId perfMetricId = new PerfMetricId();
        perfMetricId.setCounterId(counterId);
        perfMetricId.setInstance("*");

        Calendar startTime = DateUtil.calendar(DateUtil.parse("2020-01-01 00:00:00"));
        Calendar endTime = DateUtil.calendar(new Date());
        PerfQuerySpec qSpec = createPerfQuerySpecBytime(
                vm, new PerfMetricId[]{perfMetricId}, 3, refreshInterval, startTime, endTime);

        PerMonitorDO perMonitorDO = new PerMonitorDO();

        while (true) {
            PerfEntityMetricBase[] pValues = perfMgr.queryPerf(
                    new PerfQuerySpec[]{qSpec});

            if (pValues != null) {
                displayValues(pValues);
                for (int i = 0; i < pValues.length; ++i) {
                    String entityDesc = pValues[i].getEntity().getType()
                            + ":" + pValues[i].getEntity().get_value();
                    System.out.println("Entity:" + entityDesc);
                    if (pValues[i] instanceof PerfEntityMetric) {
                        PerfEntityMetric pem = (PerfEntityMetric) pValues[i];
                        if (pem.getValue() != null) {
                            perMonitorDO = printPerfMetricByName((PerfEntityMetric) pValues[i]);
                            break;
                        }

                    } else if (pValues[i] instanceof PerfEntityMetricCSV) {
                        printPerfMetricCSV((PerfEntityMetricCSV) pValues[i]);
                    } else {
                        System.out.println("UnExpected sub-type of " +
                                "PerfEntityMetricBase.");
                    }
                }
            }
            System.out.println("Sleeping 60 seconds...");
            Thread.sleep(refreshInterval * 3 * 1000);
        }
    }

    /**
     * @description: 根据指定id获取性能指标数据
     * <AUTHOR>
     * @date 2024/7/25 10:55
     * @version 1.0
     */
    public static List<PerMonitorDO> getPerEntityMericBasesByname(String name, ServiceInstance si, ManagedEntity vm, String counterName, PlatformconfigDTO platformconfigDTO) throws Exception {
        long startTime = System.currentTimeMillis();

        List<PerMonitorDO> list = new ArrayList<>();

        if (vm == null) {
            System.out.println("Virtual Machine " + name + " cannot be found.");
            si.getServerConnection().logout();
            return list;
        }

        PerformanceManager perfMgr = si.getPerformanceManager();
        PerfProviderSummary pps = perfMgr.queryPerfProviderSummary(vm);
        int refreshInterval = pps.getRefreshRate().intValue();

        Integer counterId = 0;

        Map<String, Integer> platformMap = perCounterList.get(StringUtil.toString(platformconfigDTO.getId()) );

        if (platformMap == null) {
            PerfCounterInfo[] d = perfMgr.getPerfCounter();
            Map<String, Integer> map = new HashMap<>();
            for (PerfCounterInfo counter : d) {
                String fullName = counter.getGroupInfo().getKey() + "." + counter.getNameInfo().getKey() + "." + counter.getRollupType();
                map.put(fullName, counter.getKey());
                if (counterName.equals(fullName)) {
                    counterId = counter.getKey();
                }
            }
            perCounterList.put(StringUtil.toString(platformconfigDTO.getId()), map);
        } else {
            counterId = platformMap.get(counterName);
        }

        if (counterId == null) {
            System.out.println("Counter not found for: " + counterName);
            return list;
        }

        PerfMetricId perfMetricId = new PerfMetricId();
        perfMetricId.setCounterId(counterId);
        perfMetricId.setInstance("*");

        PerfQuerySpec qSpec = createPerfQuerySpec(vm, new PerfMetricId[]{perfMetricId}, 3, refreshInterval);
        PerfEntityMetricBase[] pValues = perfMgr.queryPerf(new PerfQuerySpec[]{qSpec});

        if (pValues != null) {
            for (PerfEntityMetricBase pValue : pValues) {
                if (pValue instanceof PerfEntityMetric) {
                    list.addAll(printPerfMetric((PerfEntityMetric) pValue));
                    break;
                } else if (pValue instanceof PerfEntityMetricCSV) {
                    printPerfMetricCSV((PerfEntityMetricCSV) pValue);
                } else {
                    System.out.println("Unexpected sub-type of PerfEntityMetricBase.");
                }
            }
        }

        long eTime = System.currentTimeMillis();
        double executionTimeInSeconds = (eTime - startTime) / 1000.0;
        return list;
    }


    public static PerMonitorDO getPerEntityMericBasesCreaateByname(String name, ServiceInstance si, ManagedEntity vm, String counterName, Calendar beginTime, Calendar endTime) throws Exception {

        long startTime = System.currentTimeMillis(); // 开始时间

        if (vm == null) {
            System.out.println("Virtual Machine " + name
                    + " cannot be found.");
            si.getServerConnection().logout();
        }
        List<PerMonitorDO> list = new ArrayList<>();

        PerformanceManager perfMgr = si.getPerformanceManager();
        // find out the refresh interval for the virtual machine
        // use this interval to retrieve real time performance data
        // the interval must be one of 300, 1800, 7200 and 86400
        // see http://pubs.vmware.com/vsphere-60/index.jsp?topic=%2Fcom.vmware.wssdk.apiref.doc%2Fvim.HistoricalInterval.html
        PerfProviderSummary pps = perfMgr.queryPerfProviderSummary(vm);
        int refreshInterval = pps.getRefreshRate().intValue();
//        System.out.println("Current refresh interval is " + refreshInterval);

        Integer counterId = counterCache.get(counterName);
        if (counterId == null) {
            PerfCounterInfo[] perfCounters = perfMgr.getPerfCounter();
            Map<Integer, String> counterKeyToNameMap = new HashMap<>();

            for (PerfCounterInfo counter : perfCounters) {
                String fullName = counter.getGroupInfo().getKey() + "." + counter.getNameInfo().getKey() + "." + counter.getRollupType();
                counterKeyToNameMap.put(counter.getKey(), fullName);
                if (counterName.equals(fullName)) {
                    counterId = counter.getKey();
                    counterCache.put(counterName, counterId); // 缓存counterId
                }
            }
        }

        if (counterId != null) {
            System.out.println("CounterId for " + counterName + " is: " + counterId);
        } else {
            System.out.println("Counter not found for: " + counterName);
        }

        PerMonitorDO perMonitorDO = new PerMonitorDO();

        PerfMetricId perfMetricId = new PerfMetricId();
        perfMetricId.setCounterId(counterId);
        perfMetricId.setInstance("*");


        PerfQuerySpec qSpec = createPerfQuerySpecBytime(
                vm, new PerfMetricId[]{perfMetricId}, 3, refreshInterval, beginTime, endTime);
        PerfEntityMetricBase[] pValues = perfMgr.queryPerf(
                new PerfQuerySpec[]{qSpec});
        if (pValues != null) {
            for (int i = 0; i < pValues.length; ++i) {
                String entityDesc = pValues[i].getEntity().getType()
                        + ":" + pValues[i].getEntity().get_value();
                System.out.println("Entity:" + entityDesc);
                if (pValues[i] instanceof PerfEntityMetric) {
                    perMonitorDO = printPerfMetricByName((PerfEntityMetric) pValues[i]);
                    break;
                } else if (pValues[i] instanceof PerfEntityMetricCSV) {
                    printPerfMetricCSV((PerfEntityMetricCSV) pValues[i]);
                } else {
                    System.out.println("UnExpected sub-type of " +
                            "PerfEntityMetricBase.");
                }
            }
        }
        long eTime = System.currentTimeMillis(); // 结束时间
        long executionTime = eTime - startTime;
        double executionTimeInSeconds = executionTime / 1000.0;
        System.out.println("getPerEntityMericBasesCreaateByname代码执行时间: " + executionTimeInSeconds + " seconds");
        return perMonitorDO;
    }


    static PerfQuerySpec createPerfQuerySpec(ManagedEntity me, PerfMetricId[] metricIds, int maxSample, int interval) {
        PerfQuerySpec qSpec = new PerfQuerySpec();
        qSpec.setEntity(me.getMOR());
        qSpec.setMaxSample(maxSample);
        qSpec.setMetricId(metricIds);
        qSpec.setFormat("normal");
        qSpec.setIntervalId(interval);
        return qSpec;
    }


    static PerfQuerySpec createPerfQuerySpecBytime(ManagedEntity me,
                                                   PerfMetricId[] metricIds, int maxSample, int interval, Calendar startTime, Calendar endTime) {
        PerfQuerySpec qSpec = new PerfQuerySpec();
        qSpec.setEntity(me.getMOR());
        // set the maximum of metrics to be return
        // only appropriate in real-time performance collecting
//        qSpec.setMaxSample(new Integer(maxSample));
        qSpec.setMetricId(metricIds);
        // optionally you can set format as "normal" or "csv"
//        qSpec.setFormat("normal");
        // set the interval to the refresh rate for the entity
//        qSpec.setIntervalId(new Integer(300));
        qSpec.setStartTime(startTime);
        qSpec.setEndTime(endTime);

        return qSpec;
    }

    static void displayValues(PerfEntityMetricBase[] values) {
        for (int i = 0; i < values.length; ++i) {
            String entityDesc = values[i].getEntity().getType()
                    + ":" + values[i].getEntity().get_value();
            System.out.println("Entity:" + entityDesc);
            if (values[i] instanceof PerfEntityMetric) {
                printPerfMetric((PerfEntityMetric) values[i]);
            } else if (values[i] instanceof PerfEntityMetricCSV) {
                printPerfMetricCSV((PerfEntityMetricCSV) values[i]);
            } else {
                System.out.println("UnExpected sub-type of " +
                        "PerfEntityMetricBase.");
            }
        }
    }

    static List<PerMonitorDO> printPerfMetric(PerfEntityMetric pem) {
        List<PerMonitorDO> list = new ArrayList<>();
        PerfMetricSeries[] vals = pem.getValue();
        PerfSampleInfo[] infos = pem.getSampleInfo();

        for (int j = 0; vals != null && j < vals.length; ++j) {
            if (vals[j] instanceof PerfMetricIntSeries) {
                PerfMetricIntSeries val = (PerfMetricIntSeries) vals[j];
                long[] longs = val.getValue();
                String instance = val.getId().getInstance();
                int conterId = val.getId().getCounterId();

                for (int i = 0; infos != null && i < infos.length; i++) {
                    PerMonitorDO perMonitorDO = new PerMonitorDO();
                    perMonitorDO.setDateTime(infos[i].getTimestamp().getTime());
                    perMonitorDO.setInstance(instance);
                    perMonitorDO.setValue(longs[i]);
                    perMonitorDO.setCounterId(Convert.toLong(conterId));
                    list.add(perMonitorDO);
//                    System.out.println("Sample time: " + infos[i].getTimestamp().getTime() + ", Value: " + longs[i]);
                }
            }
        }

        return list;
    }

    static PerMonitorDO printPerfMetricByName(PerfEntityMetric pem) {
        PerfMetricSeries[] vals = pem.getValue();
        PerfSampleInfo[] infos = pem.getSampleInfo();

        PerfMetricIntSeries val = (PerfMetricIntSeries) vals[0];
        long[] longs = val.getValue();
        Long values = longs[0];

        PerMonitorDO perMonitorDO = new PerMonitorDO();
        try {
            perMonitorDO.setValue(values);
            perMonitorDO.setInstance(val.getId().getInstance());
            perMonitorDO.setDateTime(infos[0].getTimestamp().getTime());
//            System.out.println("Sampling Times and Values:");
        } catch (Exception e) {
            log.info("处理性能数据异常printPerfMetric：" + e.getMessage());
            return null;
        }

        return perMonitorDO;
    }

    static void printPerfMetricCSV(PerfEntityMetricCSV pems) {
        System.out.println("SampleInfoCSV: " + pems.getSampleInfoCSV());
        PerfMetricSeriesCSV[] csvs = pems.getValue();
        for (PerfMetricSeriesCSV csv : csvs) {
            System.out.println("PerfCounterId: " + csv.getId().getCounterId());
            System.out.println("CSV sample values: " + csv.getValue());
        }
    }

    public static Map<String, List<PerMonitorDO>> getPerEntityMetricsByNames(String name, ServiceInstance si, ManagedEntity vm, List<String> counterNames) throws RemoteException {
        Map<String, List<PerMonitorDO>> metricsMap = new HashMap<>();
        PerformanceManager perfMgr = si.getPerformanceManager();
        PerfProviderSummary pps = perfMgr.queryPerfProviderSummary(vm);
        int refreshInterval = pps.getRefreshRate().intValue();
        List<PerfMetricId> perfMetricIds = new ArrayList<>();

        for (String counterName : counterNames) {
            Integer counterId = counterCache.get(counterName);
            if (counterId == null) {
                PerfCounterInfo[] perfCounters = perfMgr.getPerfCounter();
                for (PerfCounterInfo counter : perfCounters) {
                    String fullName = counter.getGroupInfo().getKey() + "." + counter.getNameInfo().getKey() + "." + counter.getRollupType();
                    if (counterName.equals(fullName)) {
                        counterId = counter.getKey();
                        counterCache.put(counterName, counterId); // 缓存counterId
                        break;
                    }
                }
            }

            if (counterId != null) {
                PerfMetricId perfMetricId = new PerfMetricId();
                perfMetricId.setCounterId(counterId);
                perfMetricId.setInstance("*");
                perfMetricIds.add(perfMetricId);
            }
        }

        PerfQuerySpec qSpec = createPerfQuerySpec(vm, perfMetricIds.toArray(new PerfMetricId[0]), 3, refreshInterval);
        PerfEntityMetricBase[] pValues = perfMgr.queryPerf(new PerfQuerySpec[]{qSpec});

        if (pValues != null) {
            for (PerfEntityMetricBase pValue : pValues) {
                if (pValue instanceof PerfEntityMetric) {
                    List<PerMonitorDO> metricList = printPerfMetric((PerfEntityMetric) pValue);
                    for (String counterName : counterNames) {
                        Integer i = counterCache.get(counterName);
                        List<PerMonitorDO> doList = metricList.stream().filter(perMonitorDO -> Convert.toLong(counterCache.get(counterName)).equals(perMonitorDO.getCounterId())).collect(Collectors.toList());
                        metricsMap.put(counterName, doList);
                    }
                }
            }
        }

        return metricsMap;
    }



    public static Map<String, List<PerMonitorDO>> getPerEntityMetricsByNamesTest(String name, ServiceInstance si,
                                                                             ManagedEntity vm, List<String> counterNames) throws RemoteException {
        Map<String, List<PerMonitorDO>> metricsMap = new HashMap<>();
        try {
            // 1. 获取性能管理器和刷新间隔
            PerformanceManager perfMgr = si.getPerformanceManager();
            PerfProviderSummary pps = perfMgr.queryPerfProviderSummary(vm);
            int refreshInterval = pps.getRefreshRate().intValue();

            // 2. 获取所有可用的性能计数器信息
            Map<String, Integer> counterIdMap = getOrInitializeCounters(perfMgr, counterNames);

            // 3. 构建性能查询规范
            List<PerfMetricId> perfMetricIds = buildPerfMetricIds(counterNames, counterIdMap);
            if (perfMetricIds.isEmpty()) {
                log.warn("未找到任何有效的性能计数器: {}", counterNames);
                return metricsMap;
            }

            // 4. 创建查询规范，包含时间范围
            PerfQuerySpec qSpec = createEnhancedPerfQuerySpec(vm, perfMetricIds, refreshInterval);

            // 5. 执行查询并处理结果
            PerfEntityMetricBase[] pValues = perfMgr.queryPerf(new PerfQuerySpec[]{qSpec});
            if (pValues != null) {
                processMetricResults(pValues, counterNames, counterIdMap, metricsMap);
            }

            // 6. 验证结果完整性
            validateAndSupplementResults(metricsMap, counterNames);

        } catch (Exception e) {
            log.error("获取性能指标时发生错误: {} - {}", name, e.getMessage(), e);
            throw new RuntimeException("获取性能指标失败", e);
        }

        return metricsMap;
    }

    private static Map<String, Integer> getOrInitializeCounters(PerformanceManager perfMgr,
                                                                List<String> counterNames) throws RemoteException {
        Map<String, Integer> counterIdMap = new HashMap<>();

        // 获取所有性能计数器信息
        PerfCounterInfo[] perfCounters = perfMgr.getPerfCounter();
        for (PerfCounterInfo counter : perfCounters) {
            String fullName = buildCounterFullName(counter);
            if (counterNames.contains(fullName)) {
                counterIdMap.put(fullName, counter.getKey());
                counterCache.put(fullName, counter.getKey());
            }
        }

        return counterIdMap;
    }

    private static String buildCounterFullName(PerfCounterInfo counter) {
        return counter.getGroupInfo().getKey() + "." +
                counter.getNameInfo().getKey() + "." +
                counter.getRollupType();
    }

    private static List<PerfMetricId> buildPerfMetricIds(List<String> counterNames,
                                                         Map<String, Integer> counterIdMap) {
        List<PerfMetricId> perfMetricIds = new ArrayList<>();

        for (String counterName : counterNames) {
            Integer counterId = counterIdMap.get(counterName);
            if (counterId != null) {
                PerfMetricId perfMetricId = new PerfMetricId();
                perfMetricId.setCounterId(counterId);

                // 根据不同的计数器类型设置不同的实例
                if (counterName.startsWith("virtualDisk")) {
                    perfMetricId.setInstance(""); // 虚拟磁盘指标使用空实例
                } else if (counterName.startsWith("net")) {
                    perfMetricId.setInstance(""); // 网络指标使用空实例
                } else {
                    perfMetricId.setInstance("*");
                }

                perfMetricIds.add(perfMetricId);
            }
        }

        return perfMetricIds;
    }

    private static PerfQuerySpec createEnhancedPerfQuerySpec(ManagedEntity me,
                                                             List<PerfMetricId> metricIds, int interval) {
        PerfQuerySpec qSpec = new PerfQuerySpec();
        qSpec.setEntity(me.getMOR());
        qSpec.setMetricId(metricIds.toArray(new PerfMetricId[0]));
        qSpec.setFormat("normal");
        qSpec.setIntervalId(interval);

        // 设置查询时间范围
        Calendar startTime = Calendar.getInstance();
        startTime.add(Calendar.MINUTE, -5); // 往前查5分钟的数据
        qSpec.setStartTime(startTime);

        // 设置采样数
        qSpec.setMaxSample(5); // 获取多个采样点

        return qSpec;
    }

    private static void processMetricResults(PerfEntityMetricBase[] pValues,
                                             List<String> counterNames, Map<String, Integer> counterIdMap,
                                             Map<String, List<PerMonitorDO>> metricsMap) {

        for (PerfEntityMetricBase pValue : pValues) {
            if (pValue instanceof PerfEntityMetric) {
                PerfEntityMetric pem = (PerfEntityMetric) pValue;
                PerfMetricSeries[] vals = pem.getValue();
                PerfSampleInfo[] infos = pem.getSampleInfo();

                if (vals != null) {
                    for (PerfMetricSeries val : vals) {
                        if (val instanceof PerfMetricIntSeries) {
                            PerfMetricIntSeries series = (PerfMetricIntSeries) val;
                            String counterName = getCounterNameById(counterIdMap, series.getId().getCounterId());
                            if (counterName != null) {
                                List<PerMonitorDO> metrics = convertToMetrics(series, infos);
                                metricsMap.put(counterName, metrics);
                            }
                        }
                    }
                }
            }
        }
    }

    private static List<PerMonitorDO> convertToMetrics(PerfMetricIntSeries series,
                                                       PerfSampleInfo[] infos) {
        List<PerMonitorDO> metrics = new ArrayList<>();
        long[] values = series.getValue();

        for (int i = 0; i < values.length && i < infos.length; i++) {
            PerMonitorDO metric = new PerMonitorDO();
            metric.setDateTime(infos[i].getTimestamp().getTime());
            metric.setInstance(series.getId().getInstance());
            metric.setValue(values[i]);
            metric.setCounterId(Convert.toLong(series.getId().getCounterId()));
            metrics.add(metric);
        }

        return metrics;
    }

    private static String getCounterNameById(Map<String, Integer> counterIdMap, int counterId) {
        return counterIdMap.entrySet().stream()
                .filter(entry -> entry.getValue() == counterId)
                .map(Map.Entry::getKey)
                .findFirst()
                .orElse(null);
    }

    private static void validateAndSupplementResults(Map<String, List<PerMonitorDO>> metricsMap,
                                                     List<String> counterNames) {
        // 检查是否所有计数器都有数据
        for (String counterName : counterNames) {
            if (!metricsMap.containsKey(counterName) || metricsMap.get(counterName).isEmpty()) {
                log.warn("计数器 {} 未获取到数据，将使用默认值", counterName);
                // 添加默认值
                List<PerMonitorDO> defaultMetrics = createDefaultMetrics(counterName);
                metricsMap.put(counterName, defaultMetrics);
            }
        }
    }

    private static List<PerMonitorDO> createDefaultMetrics(String counterName) {
        List<PerMonitorDO> metrics = new ArrayList<>();
        PerMonitorDO metric = new PerMonitorDO();
        metric.setDateTime(new Date());
        metric.setInstance("");
        metric.setValue(0L); // 默认值设为0
        metric.setCounterId(counterCache.get(counterName) != null ?
                Convert.toLong(counterCache.get(counterName)) : 0L);
        metrics.add(metric);
        return metrics;
    }

    private static boolean isVersionLessThan(String version1, String version2) {
        String[] v1Parts = version1.split("\\.");
        String[] v2Parts = version2.split("\\.");

        int length = Math.max(v1Parts.length, v2Parts.length);

        for (int i = 0; i < length; i++) {
            int v1 = i < v1Parts.length ? Integer.parseInt(v1Parts[i]) : 0;
            int v2 = i < v2Parts.length ? Integer.parseInt(v2Parts[i]) : 0;

            if (v1 < v2) {
                return true;
            } else if (v1 > v2) {
                return false;
            }
        }

        return false;
    }
}
