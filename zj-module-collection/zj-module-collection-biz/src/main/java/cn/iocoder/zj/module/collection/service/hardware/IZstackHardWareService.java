package cn.iocoder.zj.module.collection.service.hardware;

public interface IZstackHardWareService {

    int getStatus(String url,String token,String uuid);
    String diskTotalCapacityInBytes(String url, String token);

    String hardWareInfo(String url, String token,String uuid);

    String clusters(String url, String token);

    String diskAllReadOps(String url, String token);

    String diskAllWriteOps(String url, String token);
}
