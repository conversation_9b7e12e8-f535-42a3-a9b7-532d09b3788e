package cn.iocoder.zj.module.collection.job.vmware;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.service.vmware.host.HostComputerResourceSummary;
import cn.iocoder.zj.module.collection.service.vmware.net.NetComputerResourceSummary;
import cn.iocoder.zj.module.collection.util.SampleUtil;
import cn.iocoder.zj.module.monitor.api.network.NetworkApi;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL2DTO;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.vmware.vim25.*;
import com.vmware.vim25.mo.HostSystem;
import com.vmware.vim25.mo.Network;
import com.vmware.vim25.mo.ServiceInstance;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.MalformedURLException;
import java.rmi.RemoteException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * @ClassName : VmwareNetWorkJob  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/10/21  9:18
 */
@Component
@Slf4j
public class VmwareNetWorkJob {
    @Resource
    PlatformRedisDAO platformRedisDAO;

    @Resource
    NetworkApi networkApi;

    private final ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());


    @XxlJob("vmwarenetL2Info")
    public void execute() throws MalformedURLException, RemoteException {
        // 获取redis中数据
        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                .filter(dto -> "vmware".equals(dto.getTypeCode()))
                .collect(Collectors.toList());

        if (filteredList.isEmpty()) return;

        for (PlatformconfigDTO platformconfigDTO : filteredList) {
            try {
                // todo 待修改
                if (platformconfigDTO.getId() == 18) {
                    continue;
                }
                handlePlatformConfig(platformconfigDTO);
            } catch (Exception e) {
                // 记录错误日志，包含平台信息
                log.error("处理平台 [{}] 失败: {}",
                        platformconfigDTO.getName(), e.getMessage(), e);
                // 继续处理下一个平台
                continue;
            }
        }
    }

    private void handlePlatformConfig(PlatformconfigDTO platformconfigDTO) throws Exception {
        ServiceInstance serviceInstance = SampleUtil.createServiceInstance(
                platformconfigDTO.getUrl(),
                platformconfigDTO.getUsername(),
                platformconfigDTO.getPassword()
        );
        List<HostSystem> systems = new HostComputerResourceSummary().getHostList(serviceInstance);
        List<Network> networks = new NetComputerResourceSummary().getNetWorkList(serviceInstance);
        CompletableFuture<List<NetWorkL2DTO>> netWorkL2DTOS = processNetWorkInfo(serviceInstance, systems, platformconfigDTO, networks);

        List<NetWorkL2DTO> netWorkL2DTOS1 = netWorkL2DTOS.join();
        serviceInstance.getServerConnection().logout();
        if (!netWorkL2DTOS1.isEmpty()) {
            updateNetWorkData(platformconfigDTO, netWorkL2DTOS1);
        }


    }

    private void updateNetWorkData(PlatformconfigDTO p, List<NetWorkL2DTO> netWorkL2DTOS) {
        List<NetWorkL2DTO> netWorks = networkApi.getNetworkL2ByPlatformId(p.getId()).getData();
        if (netWorks.isEmpty()) {
            networkApi.addNetWorkL2(netWorkL2DTOS);
        } else {
            Map<String, NetWorkL2DTO> existingHardwareMap = netWorks.stream()
                    .collect(Collectors.toMap(
                            NetWorkL2DTO::getUuid,
                            netWorkL2DTO -> netWorkL2DTO,
                            (existing, newValue) -> {
                                return newValue;
                            }
                    ));
            List<NetWorkL2DTO> newEntries = new ArrayList<>();
            List<NetWorkL2DTO> updatedEntries = new ArrayList<>();
            List<NetWorkL2DTO> deleteEntries = netWorks.stream()
                    .filter(item -> !netWorkL2DTOS.stream()
                            .map(NetWorkL2DTO::getUuid)
                            .collect(Collectors.toList()).contains(item.getUuid())).collect(Collectors.toList());

            for (NetWorkL2DTO newNet : netWorkL2DTOS) {
                NetWorkL2DTO netWorkL2DTO = existingHardwareMap.get(newNet.getUuid());
                if (netWorkL2DTO == null) {
                    newEntries.add(newNet);
                } else if (!netWorkL2DTO.equals(newNet)) {
                    updatedEntries.add(newNet);
                }
            }
            networkApi.updateNetWorkL2(updatedEntries);
            networkApi.addNetWorkL2(newEntries);
            if (!deleteEntries.isEmpty()) {
                networkApi.deleteNetWorkL2ByNameList(deleteEntries);
            }

        }
    }

    private CompletableFuture<List<NetWorkL2DTO>> processNetWorkInfo(ServiceInstance serviceInstance, List<HostSystem> systems, PlatformconfigDTO platformconfigDTO, List<Network> networks) {
        CompletableFuture<List<NetWorkL2DTO>> future2 = CompletableFuture.supplyAsync(() ->
                        systems.parallelStream()
                                .map(host -> {
                                    try {
                                        return collectHostInfo(platformconfigDTO, host, serviceInstance, networks);
                                    } catch (Exception e) {
                                        log.error("Error extracting host info for VM: " + host.getName(), e);
                                        return null;
                                    }
                                })
                                .filter(Objects::nonNull)
                                .flatMap(List::stream)
                                .collect(Collectors.toList())
                , executor);
        return future2;
    }

    private List<NetWorkL2DTO> collectHostInfo(PlatformconfigDTO platformconfigDTO, HostSystem hostSystem, ServiceInstance serviceInstance, List<Network> networks) throws RemoteException {
        // 获取主机端口组信息
        List<Map<String, Object>> portGroupList = getHostPortGroups(hostSystem);
        
        // 获取主机虚拟网卡信息
        HostVirtualNic[] vnics = hostSystem.getHostNetworkSystem().getNetworkInfo().getVnic();
        
        // 处理网络信息
        List<NetWorkL2DTO> netWorkL2DTOs = new ArrayList<>();
        
        // 处理标准网络
        for (Network network : networks) {
            NetWorkL2DTO baseNetworkDTO = buildBaseNetworkDTO(network, platformconfigDTO, portGroupList);
            netWorkL2DTOs.add(baseNetworkDTO);
        }

        // 处理虚拟网卡
        for (HostVirtualNic vnic : vnics) {
            NetWorkL2DTO dto = new NetWorkL2DTO();
            dto.setPhysicalInterface(vnic.getDevice());
            dto.setVirtualNetworkId(0);
            // 设置平台信息
            setPlatformInfo(dto, platformconfigDTO);
            NetWorkL2DTO vnicDTO = buildVnicNetworkDTO(dto, vnic, portGroupList);
            netWorkL2DTOs.add(vnicDTO);
        }
        
        // 去重返回
        return netWorkL2DTOs.stream().distinct().collect(Collectors.toList());
    }

    private List<Map<String, Object>> getHostPortGroups(HostSystem hostSystem) throws RemoteException {
        HostPortGroup[] portGroups = hostSystem.getConfig().getNetwork().getPortgroup();
        List<Map<String, Object>> portGroupList = new ArrayList<>();
        
        for (HostPortGroup portGroup : portGroups) {
            String name = portGroup.getSpec().getName();
            // 检查是否已存在相同名称的端口组
            boolean exists = portGroupList.stream()
                    .anyMatch(item -> item.get("name").equals(name));
            if (!exists) {
                Map<String, Object> data = new HashMap<>();
                data.put("name", name);
                data.put("vlanid", portGroup.getSpec().getVlanId());
                portGroupList.add(data);
            }
        }
        return portGroupList;
    }

    private NetWorkL2DTO buildBaseNetworkDTO(Network network, PlatformconfigDTO platformConfig, 
            List<Map<String, Object>> portGroupList) {
        String networkName = network.getName();
        String c_uuid = network.getParent().getMOR().getVal();
        String uuid = c_uuid + network.getMOR().getVal() + networkName;
        
        NetWorkL2DTO dto = new NetWorkL2DTO();
        dto.setUuid(uuid);
        dto.setName(networkName);
        dto.setPhysicalInterface(networkName);
        dto.setVirtualNetworkId(0);
        
        // 设置VLAN信息
        setVlanInfo(dto, networkName, portGroupList);
        
        // 设置平台信息
        setPlatformInfo(dto, platformConfig);
        
        return dto;
    }

    private NetWorkL2DTO buildVnicNetworkDTO(NetWorkL2DTO vnicDTO, HostVirtualNic vnic,List<Map<String, Object>> portGroupList) {
        vnicDTO.setUuid(vnic.getKey() + "_" + vnic.getSpec().ip.ipAddress);
        vnicDTO.setName(vnic.getDevice());

        // 设置VLAN信息
        setVlanInfo(vnicDTO, vnic.getDevice(), portGroupList);
        
        return vnicDTO;
    }

    private void setVlanInfo(NetWorkL2DTO dto, String name, List<Map<String, Object>> portGroupList) {
        portGroupList.stream()
                .filter(map -> name.equals(Convert.toStr(map.get("name"))))
                .findFirst()
                .ifPresent(map -> dto.setVlan(Convert.toStr(map.get("vlanid"))));
        
        dto.setType(StrUtil.isNotEmpty(dto.getVlan()) ? "VLAN" : "FLAT");
    }

    private void setPlatformInfo(NetWorkL2DTO dto, PlatformconfigDTO platformConfig) {
        dto.setTypeName("vmware");
        dto.setPlatformName(platformConfig.getName());
        dto.setPlatformId(platformConfig.getId());
        dto.setRegionId(platformConfig.getRegionId());
        dto.setRegionName(platformConfig.getRegionName());
        dto.setTenantId(1L);
        dto.setCreateTime(new Date());
    }
}
