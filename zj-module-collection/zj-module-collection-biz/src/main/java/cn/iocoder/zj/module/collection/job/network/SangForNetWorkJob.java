package cn.iocoder.zj.module.collection.job.network;

import cn.hutool.json.JSONUtil;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.service.cloud.ISangForDeviceService;
import cn.iocoder.zj.module.collection.util.SangForPwEncryptor;
import cn.iocoder.zj.module.monitor.api.network.NetworkApi;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL2DTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SangForNetWorkJob {
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    PlatformRedisDAO platformRedisDAO;
    @Resource
    ISangForDeviceService iSangForDeviceService;
    @Resource
    NetworkApi networkApi;

    public void sangForNetWorkJob() {
        getNetworks();
    }
    @XxlJob("getNetworks")
    public void getNetworks() {
        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                .filter(dto -> "sangFor".equals(dto.getTypeCode()))
                .collect(Collectors.toList());
        if (filteredList.isEmpty()) return;

        for (PlatformconfigDTO platformconfigDTO : filteredList) {
            handlePlatformConfig(platformconfigDTO);
        }
        XxlJobHelper.handleSuccess("执行成功");
    }

    private void handlePlatformConfig(PlatformconfigDTO p) {
        List<NetWorkL2DTO> netWorkL2DTOS = new ArrayList<>();
        JSONObject tokenInfo = SangForPwEncryptor.sangForlogin(p);
        JSONArray networks = iSangForDeviceService.getNetworks(p, tokenInfo);
        for (Object item : networks) {
            NetWorkL2DTO netWorkL2 = new NetWorkL2DTO();
            JSONObject jsonObj = JSONObject.parseObject(JSONUtil.toJsonStr(item));
            netWorkL2.setName(jsonObj.getString("name"));
            netWorkL2.setUuid(jsonObj.getString("id"));
            netWorkL2.setPlatformName(p.getName());
            netWorkL2.setPlatformId(p.getId());
            netWorkL2.setRegionId(p.getRegionId());
            netWorkL2.setRegionName(p.getRegionName());
            netWorkL2.setType(jsonObj.getString("type"));
            netWorkL2.setTenantId(p.getTenantId());
            netWorkL2.setTypeName("sangFor");
            netWorkL2DTOS.add(netWorkL2);
        }
        if (!netWorkL2DTOS.isEmpty()) {
            List<NetWorkL2DTO> netWorks = networkApi.getNetworkL2ByPlatformId(p.getId()).getData();
            if (netWorks.isEmpty()) {
                networkApi.addNetWorkL2(netWorkL2DTOS);
            } else {
                Map<String, NetWorkL2DTO> existingHardwareMap = netWorks.stream()
                        .collect(Collectors.toMap(NetWorkL2DTO::getUuid, netWorkL2DTO -> netWorkL2DTO));

                List<NetWorkL2DTO> newEntries = new ArrayList<>();
                List<NetWorkL2DTO> updatedEntries = new ArrayList<>();
                List<NetWorkL2DTO> deleteEntries = netWorks.stream()
                        .filter(item -> !netWorkL2DTOS.stream()
                                .map(NetWorkL2DTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid())).collect(Collectors.toList());

                for (NetWorkL2DTO newNet : netWorkL2DTOS) {
                    NetWorkL2DTO netWorkL2DTO = existingHardwareMap.get(newNet.getUuid());
                    if (netWorkL2DTO == null) {
                        newEntries.add(newNet);
                    } else if (!netWorkL2DTO.equals(newNet)) {
                        updatedEntries.add(newNet);
                    }
                }
                if (!deleteEntries.isEmpty()) {
                    networkApi.deleteNetWorkL2ByNameList(deleteEntries);
                }
                networkApi.updateNetWorkL2(updatedEntries);
                networkApi.addNetWorkL2(newEntries);
            }
        }
    }


}
