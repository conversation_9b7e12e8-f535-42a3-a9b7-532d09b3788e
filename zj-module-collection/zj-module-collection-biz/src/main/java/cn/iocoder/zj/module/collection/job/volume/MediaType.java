package cn.iocoder.zj.module.collection.job.volume;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 存储介质类型枚举
 */
@Getter
@AllArgsConstructor
public enum MediaType {
    
    ROTATE(1, "机械盘", "rotate"),
    SSD(2, "固态硬盘", "ssd"),
    HYBIRD(3, "混合盘", "hybird"),
    ;

    /**
     * 类型编码
     */
    private final Integer type;
    
    /**
     * 类型名称（中文）
     */
    private final String name;
    
    /**
     * 类型名称（英文）
     */
    private final String enName;

    /**
     * 根据类型编码获取枚举
     */
    public static MediaType getByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (MediaType value : MediaType.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
