package cn.iocoder.zj.module.collection.service.cloud;

import cn.hutool.db.PageResult;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.List;
import java.util.Map;


public interface IWinHongDeviceService {
    JSONArray getHardware(PlatformconfigDTO p, String token);

    JSONArray disksInfo(String url, String uuid, String token);

    List<Map> phynic(String url, String uuid, String token);

    String flow(String url, String uuid, String token, String mac);

    String ratio(String url, String uuid, String token);

    Long reserveMemory(PlatformconfigDTO p, String uuid, String token);


    //====================================性能数据===============================
    String cpuRate(String url, String uuid, String token);

    String ioStat(String url,String token,Map<String, Object> param);

    String ioReq(String url, String token,Map<String, Object> param);




    //====================================云主机===============================

    JSONArray getClouds(PlatformconfigDTO p, String token);

    JSONObject getSummary(PlatformconfigDTO p, String token,String domainId);

    JSONObject getdomainInfo(PlatformconfigDTO p, String token, String uuid);

    JSONArray getDiskStorageInfo(PlatformconfigDTO p, String token, String uuid);

    JSONObject getCpuAndMemoryRate(PlatformconfigDTO p, String token, String uuid);



    //====================================性能数据===============================
    JSONArray getHostCpuRate(PlatformconfigDTO p, String token, String domainIds);

    JSONArray getHostFlowLink(PlatformconfigDTO p, String token, String domainIds);

    JSONArray getHostIoStat(PlatformconfigDTO p, String token, String domainIds);

    JSONArray getHostIoReq(PlatformconfigDTO p, String token, String domainIds);

    JSONArray getNetworkFlow(PlatformconfigDTO p, String token, String domainIds);


    //====================================存储===============================

    JSONArray getStoragePools(PlatformconfigDTO p, String token);

    JSONArray getVolumes(PlatformconfigDTO p, String token, String storagePoolId);

    //====================================网络===============================
    JSONArray getNetworks(PlatformconfigDTO p, String token);

    JSONArray getDomainDiskInfo(PlatformconfigDTO p, String token, String uuid);

    JSONArray getSnapshots(PlatformconfigDTO p, String token);

    Boolean noRefresh(PlatformconfigDTO p,String token);

    String getToken(PlatformconfigDTO p);

    JSONArray portGroupsV2(PlatformconfigDTO p, String uuid, String token);

    JSONArray portGroups(PlatformconfigDTO platformconfigDTO, String portGroupsId, String token);

    JSONObject getSummaryHost(PlatformconfigDTO p, String uuid, String token);

    //==================================镜像===============================
    JSONArray getImages(PlatformconfigDTO p, String token);

    JSONArray getImagesOperationLogs(PlatformconfigDTO p, String domainTemplateId, String order, Integer size, Integer start, String token);

    JSONArray getStoragePoolshosts(PlatformconfigDTO p, String token, String storagePoolId);

    JSONArray portGroupsVswitchs(PlatformconfigDTO platformconfigDTO, String uuid, String token);
}
