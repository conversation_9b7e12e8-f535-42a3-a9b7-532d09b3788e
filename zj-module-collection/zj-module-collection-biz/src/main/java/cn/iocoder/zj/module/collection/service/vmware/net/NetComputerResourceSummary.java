package cn.iocoder.zj.module.collection.service.vmware.net;

import com.vmware.vim25.mo.InventoryNavigator;
import com.vmware.vim25.mo.ManagedEntity;
import com.vmware.vim25.mo.Network;
import com.vmware.vim25.mo.ServiceInstance;
import org.springframework.stereotype.Component;

import java.rmi.RemoteException;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName : NetComputerResourceSummary  //类名
 * @Description :   操作vcenter中网络对象 //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/7/24  15:54
 */
@Component
public class NetComputerResourceSummary {
    public static List<Network> getNetWorkList(ServiceInstance serviceInstance) throws RemoteException {
        List<Network> hostSystemList = new ArrayList<Network>();
        Network network = null;

        ManagedEntity[] managedEntities = new InventoryNavigator(serviceInstance.getRootFolder())
                .searchManagedEntities("Network");
        if (managedEntities != null && managedEntities.length > 0) {
            for (ManagedEntity managedEntity : managedEntities) {
                network = (Network) managedEntity;
                hostSystemList.add(network);
            }
        } else {
            return null;
        }
        return hostSystemList;
    }
}

