package cn.iocoder.zj.module.collection.job.alarm;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.zj.framework.common.enums.ResourceAppEnum;
import cn.iocoder.zj.framework.common.pojo.UserBindDTO;
import cn.iocoder.zj.framework.common.util.collection.CollectionUtils;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.framework.ip.core.Area;
import cn.iocoder.zj.framework.ip.core.utils.AreaUtils;
import cn.iocoder.zj.module.collection.dal.dataobject.zstack.AlarmConfigInfo;
import cn.iocoder.zj.module.collection.dal.dataobject.zstack.AlarmHostRelationInfo;
import cn.iocoder.zj.module.collection.dal.redis.zstack.AlarmInfoRedisDAO;
import cn.iocoder.zj.module.collection.framework.influx.config.InfluxDBTemplate;
import cn.iocoder.zj.module.collection.util.PlatformParamConverter;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import cn.iocoder.zj.module.monitor.api.hardware.HardWareInfoApi;
import cn.iocoder.zj.module.om.api.userbind.UserBindApi;
import cn.iocoder.zj.module.report.api.subscription.dto.ReportSubscriptionApi;
import cn.iocoder.zj.module.report.api.subscription.dto.ReportSubscriptionDTO;
import cn.iocoder.zj.module.system.api.dingtalk.DingTalkSendApi;
import cn.iocoder.zj.module.system.api.mail.MailSendApi;
import cn.iocoder.zj.module.system.api.mail.dto.MailSendSingleToUserReqDTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import cn.iocoder.zj.module.system.api.sms.SmsSendApi;
import cn.iocoder.zj.module.system.api.wechat.WeChatSendApi;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.alerter.AlertConverge;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ZstackAlarmJob {
    @Resource
    InfluxDBTemplate influxDBTemplate;

    @Resource
    AlarmInfoRedisDAO alarmInfoRedisDAO;

    @Autowired
    private AlarmConfigApi alarmConfigApi;

    @Autowired
    private SmsSendApi smsSendApi;

    @Autowired
    private MailSendApi mailSendApi;

    @Resource
    private WeChatSendApi weChatSendApi;

    @Resource
    private DingTalkSendApi dingTalkSendApi;

    @Autowired
    private PlatformconfigApi platformconfigApi;

    @Autowired
    private HardWareInfoApi hardWareInfoApi;

    @Autowired
    private ReportSubscriptionApi reportSubscriptionApi;

    @Autowired
    private UserBindApi userBindApi;

    @Resource
    private RedisTemplate redisTemplate;

    @Value("${cloud-domain.url}")
    private String cloudDomain;

    @XxlJob("zstackAlarm")
    public void dealAlarmInfo() {
        List<AlarmHostRelationInfo> alarmHostRealtionList = JSON.parseArray(alarmInfoRedisDAO.getRelation("alarm_host_relation"), AlarmHostRelationInfo.class);
        List<AlarmConfigInfo> alarmConfigInfoList = JSON.parseArray(alarmInfoRedisDAO.getConfig("alarm_config"), AlarmConfigInfo.class);
        List<PlatformconfigDTO> platformList = platformconfigApi.getPlatList().getData();
        Map<Long,PlatformconfigDTO> platformMap= CollectionUtils.convertMap(platformList,PlatformconfigDTO::getId);
        List<AlarmDorisReqDTO> alarmDorisReqDTOs = new ArrayList<>();

        AlertConverge converge = alarmConfigApi.getAvailableAlertConverge().getData();
        Map<Long, List<AlarmDorisReqDTO>> alarmRecordList = alarmConfigApi.getSilentTarget(converge).getData();
//
        if (ObjectUtil.isEmpty(alarmHostRealtionList)||ObjectUtil.isEmpty(alarmConfigInfoList)) {
            return;
        }
        alarmConfigInfoList.removeIf(item->item.getDeleted()==1);
        alarmHostRealtionList.removeIf(item->item.getStatus()==1);
        Map<Long,AlarmConfigInfo> alarmConfigMap = CollectionUtils.convertMap(alarmConfigInfoList,AlarmConfigInfo::getId);
        for (AlarmHostRelationInfo relation : alarmHostRealtionList) {
            AlarmConfigInfo alarmConfig = alarmConfigMap.get(relation.getAlarmId());
            if (ObjectUtil.isNotEmpty(alarmConfig)&&!alarmConfig.getDictLabelValue().equals("state_change")) {
                String query = "SELECT value,metricName,uuid as monitorId,platformName,platformId FROM (SELECT mean(value) AS value FROM {table_name} WHERE metricName = {metricName} and time >= now()-10m {and_uuid} GROUP BY metricName,uuid,platformName,platformId )";
                 //处理云主机告警信息
                if (alarmConfig.getSourceType().equals("monitor_alarm_host")) {
                    query = query.replace("{table_name}", "zj_cloud_host").
                            replace("{metricName}", "'"+alarmConfig.getDictLabelValue()+"'").
                            replace("{and_uuid}", "and uuid= '" + relation.getHostUuid() + "'");
                    alarmDorisReqDTOs.addAll(collector(alarmConfig, "云主机", query, relation, platformMap));

                    //处理宿主机告警信息
                }else if (alarmConfig.getSourceType().equals("monitor_alarm_hardware")) {
                    query = query.replace("{table_name}", "zj_cloud_hardware").
                            replace("{metricName}", "'"+alarmConfig.getDictLabelValue()+"'").
                            replace("{and_uuid}", "and uuid= '" + relation.getHostUuid()+"'");
                    alarmDorisReqDTOs.addAll(collector(alarmConfig, "宿主机", query, relation, platformMap));

                    //处理存储告警信息
                }else if (alarmConfig.getSourceType().equals("monitor_alarm_disk")) {
                    query = query.replace("{table_name}", "zj_cloud_storage").
                            replace("{metricName}", "'"+alarmConfig.getDictLabelValue()+"'").
                            replace("{and_uuid}", "and uuid= '" + relation.getHostUuid()+"'");
                    alarmDorisReqDTOs.addAll(collector(alarmConfig, "云存储", query, relation, platformMap));
                }
            }
        }
        if (alarmDorisReqDTOs.size() > 0) {
            //待移除新增列表的目标集合；
            int shardIndex = XxlJobHelper.getShardIndex();
            int shardTotal = XxlJobHelper.getShardTotal();
            log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
            final Long alertId = alarmConfigApi.getMaxAlertId().getData();
            //接受需要更新的告警
            List<AlarmDorisReqDTO> updateAlarmDoris = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(converge)) {
                //对于同一告警配置下的同一资源，在静默时间内不重复触发告警
                List<AlarmDorisReqDTO> removeList = new ArrayList<>();
                for (AlarmDorisReqDTO alarmDorisReqDTO : alarmDorisReqDTOs) {
                    if (ObjectUtil.isNotEmpty(alarmRecordList.get(alarmDorisReqDTO.getAlarmId()))) {
                        for (AlarmDorisReqDTO item : alarmRecordList.get(alarmDorisReqDTO.getAlarmId())) {
                            //判断是否满足告警条件
                            if (item.getMonitorId().equals(alarmDorisReqDTO.getMonitorId())) {
                                if (item.getIsSolved() != 2) {
                                    //取模，为0则告警次数加1
                                    Integer remainder = Integer.parseInt(String.valueOf(alarmConfigMap.get(alarmDorisReqDTO.getAlarmId()).getAlarmTime()));
                                    //判断收敛周期内触发次数是否达到预设次数，如果已达到则告警次数加1，如果未达到，则收敛周期内触发次数加1
                                    if (alarmDorisReqDTO.getTimes()%remainder==0||alarmDorisReqDTO.getTimes()<remainder) {
                                        Integer times =item.getTimes() + 1;
                                        item.setAlarmConfigName(alarmDorisReqDTO.getAlarmConfigName());
                                        item.setAlarmRule(alarmDorisReqDTO.getAlarmRule());
                                        BeanUtil.copyProperties(item, alarmDorisReqDTO, "id");
                                        //不满足条件的告警从alarmDorisReqDTOs移除，并将数据库中存在的条目的告警次数+1并更新
                                        item.setTimes(times);
                                        item.setGmtUpdate(DateUtil.date());
                                        item.setLastAlarmTime(new Date().getTime());
                                        item.setContent(alarmDorisReqDTO.getContent());
                                        updateAlarmDoris.add(item);
                                    }
                                    removeList.add(alarmDorisReqDTO);
                                }
                            }
                        }
                    }
                }
                removeList = removeList.stream()
                        .distinct()
                        .collect(Collectors.toList());
                //移除不满足条件的告警
                alarmDorisReqDTOs.removeAll(removeList);
            }
            Map<String,List> updateAndInsert = new HashMap<>();
            List<AlarmDorisReqDTO> shardingData = new ArrayList<>();
            updateAndInsert.put("updateList",updateAlarmDoris);

            //从待新增的列表移除更新的目标
            if (alarmDorisReqDTOs.size() > 0) {
                shardingData = StringUtil.getShardingData(alarmDorisReqDTOs, shardTotal, shardIndex);
                // 对分片数据进行业务处理
                for (AlarmDorisReqDTO item : shardingData) {
                    item.setId(shardingData.indexOf(item)+ Optional.ofNullable(alertId).orElse(0L) +1);
                    // 模拟业务逻辑处理
                    log.info("Processing item: " + item.getContent());
                }
                updateAndInsert.put("insertList",shardingData);
            }
            alarmConfigApi.createAlarmToDoris(updateAndInsert);
            System.out.println("创建告警数据");
            if(!CollectionUtil.isEmpty(shardingData)) {
                sendAlarmNotice(shardingData,platformMap);
            }
        }

    }

    private List<AlarmDorisReqDTO> collector(AlarmConfigInfo alarmConfig, String sourceName, String query, AlarmHostRelationInfo relation,Map<Long,PlatformconfigDTO> platformMap) {
        //按照单位进行换算
        Pattern pattern = Pattern.compile("/s", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(alarmConfig.getUnit());
        alarmConfig.setUnit(matcher.replaceAll(""));
        Double value = PlatformParamConverter.convert(alarmConfig.getAlarmVal().doubleValue(), alarmConfig.getUnit(), "down", relation.getPlatformConfigType());
        //从时序库获取数据
        List<AlarmDorisReqDTO> alarmDorisReqDTOs = new ArrayList<>();

        List<Object> objs = influxDBTemplate.fetchRecords(query);
        objs.parallelStream().forEach(obj -> {
            AlarmDorisReqDTO data = JSON.parseObject(JSONObject.toJSONString(obj), AlarmDorisReqDTO.class);
            PlatformconfigDTO platformconfig = platformMap.get(data.getPlatformId());
            JSONObject tags = new JSONObject();
            tags.put("app", alarmConfig.getSourceType().substring(alarmConfig.getSourceType().lastIndexOf("_") + 1));
            tags.put("monitorId",data.getMonitorId());
            tags.put("thresholdId",alarmConfig.getId());
            tags.put("monitorName",data.getMonitorName());
            if( StringUtil.isNotEmpty(platformconfig.getAddress())) {
                tags.put("address",platformconfig.getAddress());
            }
            String target = alarmConfig.getSourceType().substring(alarmConfig.getSourceType().lastIndexOf("_") + 1)+"."+alarmConfig.getDictLabelType().substring(alarmConfig.getDictLabelType().lastIndexOf("_") + 1)+"."+alarmConfig.getDictLabelValue();
            //设置省地区信息
            BeanUtils.copyProperties(alarmConfig, data);
            data.setMonitorName(relation.getHostName());
            data.setPlatformName(platformconfig.getName());
            data.setApp(alarmConfig.getSourceType().contains("host")?"host":(alarmConfig.getSourceType().contains("hardware")?"hardware":"storage"));
            data.setPriority(alarmConfig.getAlarmLevel()==3?0:alarmConfig.getAlarmLevel()==2?1:2);
            data.setAlarmId(alarmConfig.getId());
            data.setTags(JSONUtil.toJsonStr(tags));
            data.setTarget(target);
            data.setResourceType(0);
            data.setAlarmRule(alarmConfig.getContext());
            data.setAlarmConfigName(alarmConfig.getAlarmName());
            data.setStatus(0);
            data.setIsSolved(0);
            data.setTimes(1);
            data.setGmtCreate(new Date());
            data.setFirstAlarmTime(DateUtil.current());
            data.setGmtUpdate(DateUtil.date());
            data.setLastAlarmTime(DateUtil.current());
            data.setAlarmTime(alarmConfig.getAlarmTime());
            if (alarmConfig.getUnit().equals("%")) {
                data.setContent(sourceName + alarmConfig.getDictLabelName() + String.format("%.2f", data.getValue())+"%");
            }else {
                data.setContent(sourceName + alarmConfig.getDictLabelName() + unitConvert(Long.valueOf(String.format("%.0f", data.getValue()))));
            }
            if (alarmConfig.getAlarmRule().equals(">") && value < data.getValue()) {
                alarmDorisReqDTOs.add(data);
            } else if (alarmConfig.getAlarmRule().equals(">=") && value <= data.getValue()) {
                alarmDorisReqDTOs.add(data);
            } else if (alarmConfig.getAlarmRule().equals("<") && value > data.getValue()) {
                alarmDorisReqDTOs.add(data);
            } else if (alarmConfig.getAlarmRule().equals("<=") && value >= data.getValue()) {
                alarmDorisReqDTOs.add(data);
            }
        });
        return alarmDorisReqDTOs;
    }

    public Area getProvinceByRegionId(Integer regionId) {
        return getProvince(getProvince(AreaUtils.getArea(regionId)));
    }

    private Area getProvince(Area area) {
        if (area.getType() == 2) {
            return area;
        }
        return getProvince(area.getParent());
    }

    private void sendAlarmNotice(List<AlarmDorisReqDTO> alarmDorisReqDTOs, Map<Long, PlatformconfigDTO> platformMap) {
        List<ReportSubscriptionDTO> report = reportSubscriptionApi.getAlarmSubscription().getData();
        if (ObjectUtil.isEmpty(report)) {
            return;
        }

        for (ReportSubscriptionDTO subscription : report) {
            UserBindDTO userBindDO = JsonUtils.parseObject(redisTemplate.opsForValue().get("app_key_secret:app").toString(), UserBindDTO.class);
            for (AlarmDorisReqDTO item : alarmDorisReqDTOs) {
                if (!isPlatformMatched(subscription, item)) {
                    continue;
                }

                Map<String, Object> templateParams = prepareTemplateParams(item, platformMap);

                try {
                    if (subscription.getEmailState() == 1 && StringUtil.isNotEmpty(subscription.getEmail()) && userBindDO.getEmailState()==1) {
                        sendEmail(subscription.getEmail(), subscription.getUserId(), templateParams);
                        log.info("邮件发送成功给: {}", subscription.getEmail());
                    }
                } catch (Exception e) {
                    log.error("邮件发送失败", e);
                }

                try {
                    if (subscription.getWechatState() == 1 && StringUtil.isNotEmpty(subscription.getOpenId())) {
                        sendWechatNotification(subscription, item);
                        log.info("微信通知发送成功给: {}", subscription.getOpenId());
                    }
                } catch (Exception e) {
                    log.error("微信通知发送失败", e);
                }

                try {
                    if (subscription.getDingtalkState() == 1 && BeanUtil.isNotEmpty(userBindDO) && StringUtil.isNotEmpty(subscription.getDingtalkPhone()) && userBindDO.getDingtalkState() == 1) {
                        sendDingtalkNotification(subscription, item, userBindDO);
                        log.info("钉钉通知发送成功给: {}", subscription.getDingtalkPhone());
                    }
                } catch (Exception e) {
                    log.error("钉钉通知发送失败", e);
                }

                try {
                    if (subscription.getWecomState() == 1 && BeanUtil.isNotEmpty(userBindDO) && StringUtil.isNotEmpty(subscription.getWecomPhone()) && userBindDO.getWxState() == 1) {
                        sendWecomNotification(subscription, item, userBindDO);
                        log.info("企业微信通知发送成功给: {}", subscription.getWecomPhone());
                    }
                } catch (Exception e) {
                    log.error("企业微信通知发送失败", e);
                }
            }
        }
    }

    private boolean isPlatformMatched(ReportSubscriptionDTO subscription, AlarmDorisReqDTO item) {
        return StringUtil.isNotEmpty(subscription.getPlatformId()) &&
                subscription.getPlatformId().contains(Convert.toStr(item.getPlatformId()));
    }

    private Map<String, Object> prepareTemplateParams(AlarmDorisReqDTO item, Map<Long, PlatformconfigDTO> platformMap) {
        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("alarmName", item.getMonitorName());
        templateParams.put("productsName", item.getMonitorName());
        templateParams.put("value", Math.round(Optional.ofNullable(item.getValue()).orElse(0d) * 100.0) / 100.0);
        templateParams.put("time", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        templateParams.put("app", getResourceLabel(item.getApp()));
        String priority = String.valueOf(item.getPriority());
        templateParams.put("priority", StringUtil.isNullOrEmpty(priority) ? "" : (priority.equals("0") ? "严重" : priority.equals("1") ?
                "警告" : "提示"));
        templateParams.put("times", item.getTimes());
        templateParams.put("content", item.getContent());

        PlatformconfigDTO platformConfig = platformMap.get(item.getPlatformId());
        templateParams.put("platformName", platformConfig != null ? platformConfig.getName() : "-");
        templateParams.put("address", platformConfig != null && StringUtil.isNotEmpty(platformConfig.getAddress()) ?
                platformConfig.getAddress() : "-");
        return templateParams;
    }

    private void sendEmail(String email, Long userId, Map<String, Object> templateParams) {
        MailSendSingleToUserReqDTO reqDTO = new MailSendSingleToUserReqDTO();
        String templateCode = mailSendApi.getTemplateByName("资源告警通知").getData();
        reqDTO.setTemplateCode(templateCode);
        reqDTO.setTemplateParams(templateParams);
        reqDTO.setMail(email);
        reqDTO.setUserId(userId);
        reqDTO.setAlertMail("true");
        Long id = mailSendApi.sendSingleMailToAdmin(reqDTO).getData();
        System.out.println("邮件推送编号：" + id + ", 收件人邮箱:" + email);
    }

    private void sendWechatNotification(ReportSubscriptionDTO subscription, AlarmDorisReqDTO item) {
        Map<String, Object> target = createNotificationTarget(item, subscription.getOpenId());
        String redictUrl = generateRedirectUrl(item.getId());
        target.put("url", redictUrl);
        target.put("ip", getIpAddress(item));
        target.put("platformName", item.getPlatformName());
        String priorityDescription = (item.getPriority() == 0) ? "严重" :((item.getPriority() == 1) ? "警告" :((item.getPriority() == 2) ? "提示" : ""));
        target.put("priority",priorityDescription);
        boolean success = weChatSendApi.sendSingleWeChatToMember(target).getData();
        System.out.println("微信发送成功：" + success);
    }

    private void sendDingtalkNotification(ReportSubscriptionDTO subscription, AlarmDorisReqDTO item, UserBindDTO userBindRespVO) {
        Map<String, Object> target = createNotificationTarget(item, subscription.getOpenId());
        String redictUrl = generateRedirectUrl(item.getId());
        target.put("url", redictUrl);
        target.put("ip", getIpAddress(item));

        if (userBindRespVO == null) {
            log.info("钉钉发送告警信息，应用参数为空");
            return;
        }

        target.put("appKey", userBindRespVO.getDingtalkAppKey());
        target.put("appSecret", userBindRespVO.getDingtalkAppSecret());
        target.put("agentId", userBindRespVO.getDingtalkAgentId());
        target.put("dingtalkPhone", subscription.getDingtalkPhone());

        boolean success = dingTalkSendApi.sendSingleDingTalkToMember(target).getData();
        System.out.println("钉钉发送成功：" + subscription.getDingtalkPhone());
    }

    private void sendWecomNotification(ReportSubscriptionDTO subscription, AlarmDorisReqDTO item, UserBindDTO userBindRespVO) {
        Map<String, Object> target = createNotificationTarget(item, subscription.getOpenId());
        String redictUrl = generateRedirectUrl(item.getId());
        target.put("url", redictUrl);
        target.put("ip", getIpAddress(item));

        if (userBindRespVO == null) {
            log.info("企微发送告警信息，应用参数为空");
            return;
        }

        target.put("appKey", userBindRespVO.getWxCorpid());
        target.put("appSecret", userBindRespVO.getWxCorpsecret());
        target.put("agentId", userBindRespVO.getWxAgentId());
        target.put("wecomPhone", subscription.getWecomPhone());

        boolean success = dingTalkSendApi.sendSingleWeComToMember(target).getData();
        System.out.println("企微发送成功：" + subscription.getWecomPhone());
    }

    private Map<String, Object> createNotificationTarget(AlarmDorisReqDTO item, String openId) {
        Map<String, Object> target = JSON.parseObject(JSON.toJSONString(item), Map.class);
        target.put("openId", openId);
        return target;
    }

    private String generateRedirectUrl(Long itemId) {
        try {
            String encodedUrl = URLEncoder.encode(cloudDomain, "UTF-8");
            String parameter = URLUtil.encode("/pages/alarm/info?id=" + itemId);
            return "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxe3d3d4577c6c41b6&redirect_uri=" + encodedUrl +
                    "&response_type=code&scope=snsapi_userinfo&state=" + parameter + "&connect_redirect=1#wechat_redirect";
        } catch (UnsupportedEncodingException e) {
            System.out.println("Error encoding URL: " + e.getMessage());
            return "";
        }
    }

    private String getIpAddress(AlarmDorisReqDTO item) {
        String uuid = item.getMonitorId();
        String sourceType = item.getApp();
        Map<String, String> map = new HashMap<>();
        map.put("uuid", uuid);
        map.put("sourceType", sourceType);
        return hardWareInfoApi.getIp(map).getData();
    }

    public String unitConvert(Long size) {
        if (size <= 0) return "0";
        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        return new DecimalFormat("#,##0.#").format(size / Math.pow(1024, digitGroups)) + units[digitGroups];
    }

    private String getResourceLabel(String app) {
        ResourceAppEnum[] values = ResourceAppEnum.values();
        String label = "";
        for (ResourceAppEnum item : values) {
            if (item.getValue().equals(app)) {
                label = item.getLabel();
            } else if (app.equals("host")) {
                label =   "云主机";
            } else if (app.equals("hardware")) {
                label =   "宿主机";
            } else if (app.equals("storage")) {
                label =    "主存储";
            }
        }
        return label;
    }
}
