package cn.iocoder.zj.module.collection.service.zstack;

import org.springframework.stereotype.Component;

/**
 * @ClassName : AbstractZstackApi  //类名
 * @Description : zstack接口抽象类  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/6/5  10:34
 */
@Component
public abstract class AbstractZstackApi {

    public abstract String fetchMetricData(String url, String token, String uuid,String metricName);
    public abstract String cpuUsedUtilization(String url, String token, String uuid);

    public abstract String cpuAverageUsedUtilization(String url, String token, String uuid);

    public abstract String cpuAllUsedUtilization(String url, String token, String uuid);

    public abstract String cpuUserUtilization(String url, String token, String uuid);

    public abstract String cpuWaitUtilization(String url, String token, String uuid);

    public abstract String cpuSystemUtilization(String url, String token, String uuid);

    public abstract String cpuIdleUtilization(String url, String token, String uuid);


    //-----------------内存---------------------//
    public abstract String  memoryUsedInPercent(String url, String token, String uuid);

    public abstract String memoryUsedBytes(String url, String token, String uuid);

    public abstract String memoryFreeBytes(String url, String token, String uuid);


    //-----------------磁盘---------------------//
    public abstract String diskReadBytes(String url, String token, String uuid);

    public abstract String diskWriteBytes(String url, String token, String uuid);

    public abstract String diskReadOps(String url, String token, String uuid);

    public abstract String diskWriteOps(String url, String token, String uuid);

    public abstract String diskUsedCapacityInPercent(String url, String token);

    public abstract String diskFreeCapacityInPercent(String url, String token);

    public abstract String diskFreeCapacityInBytes(String url, String token, String uuid);

    public abstract String diskUsedCapacityInBytes(String url, String token, String uuid);

    public abstract String diskAllFreeCapacityInPercent(String url, String token);

    public abstract String diskAllUsedCapacityInPercent(String url, String token, String uuid);

    public abstract String diskAllUsedCapacityInBytes(String url, String token, String uuid);

    public abstract String diskAllFreeCapacityInBytes(String url, String token, String uuid);

    public abstract String diskZStackUsedCapacityInPercent(String url, String token, String uuid);

    public abstract String diskZStackUsedCapacityInBytes(String url, String token, String uuid);

    public abstract String diskAllReadBytes(String url, String token, String uuid);

    public abstract String diskAllWriteBytes(String url, String token, String uuid);

    //-----------------网络---------------------//
    public abstract String networkInBytes(String url, String token, String uuid);

    public abstract String networkAllInBytes(String url, String token, String uuid);

    public abstract String networkOutBytes(String url, String token, String uuid);

    public abstract String networkAllOutBytes(String url, String token, String uuid);

    public abstract String networkInPackets(String url, String token, String uuid);

    public abstract String networkAllInPackets(String url, String token, String uuid);

    public abstract String networkOutPackets(String url, String token, String uuid);

    public abstract String networkAllOutPackets(String url, String token, String uuid);

    public abstract String networkOutErrors(String url, String token, String uuid);

    public abstract String networkInErrors(String url, String token, String uuid);


}
