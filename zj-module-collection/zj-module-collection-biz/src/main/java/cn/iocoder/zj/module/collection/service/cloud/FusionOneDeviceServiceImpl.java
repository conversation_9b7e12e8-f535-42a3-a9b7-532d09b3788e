package cn.iocoder.zj.module.collection.service.cloud;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import cn.iocoder.zj.module.collection.collect.http.MetricsFetcher;
import cn.iocoder.zj.module.collection.service.fusionOne.FusionOneApiConstant;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;

@Service
@Slf4j
public class FusionOneDeviceServiceImpl implements FusionOneDeviceService {

    MetricsFetcher fetcher = new MetricsFetcher();

    private String fetchMetricUtilization(String url, String token, Map<String, String> header, Map<String, Object> param, Method method) {
        header.put("Accept","application/json;version=8.0; charset=UTF-8");
        String host="";
        try {
            URL urlObj = new URL(url);
            host = urlObj.getHost() + ":" + urlObj.getPort();
            // 如果端口是-1(默认端口)，可以根据协议设置默认端口
            if (urlObj.getPort() == -1) {
                int defaultPort = urlObj.getProtocol().equalsIgnoreCase("https") ? 443 : 80;
                host = urlObj.getHost() + ":" + defaultPort;
            }
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
        header.put("Host",host);
        header.put("X-Auth-Token", token);
        String response = fetcher.sendFusionOneRequest(url, method, header, param, token,
                httpResponse -> {
                    if (httpResponse.getStatus() == 200) {
                        return httpResponse.body();
                    } else {
                        throw new RuntimeException("请求失败，状态码：" + httpResponse.getStatus());
                    }
                });

        return response;
    }

    @Override
    public JSONArray getClouds(PlatformconfigDTO p, String token,String siteId) {
        String url = p.getUrl() + FusionOneApiConstant.GET_CLOUD_LIST.replace("{siteId}", siteId);
        Map<String, Object> param = new HashMap<>();
        param.put("detail",1);
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("vms");
    }



    @Override
    public JSONObject getCloudInfo(PlatformconfigDTO p, String token, String uuid) {
        String url = p.getUrl() + FusionOneApiConstant.GET_CLOUD_INFO.replace("{vmId}", uuid);
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body);
    }

    @Override
    public JSONArray getHosts(PlatformconfigDTO p, String token,String siteId) {
        String url = p.getUrl().replace(":8443",":7443") + FusionOneApiConstant.GET_HOST_LIST.replace("{siteId}",
                siteId);
        Map<String, Object> param = new HashMap<>();

        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("hosts");
    }

    @Override
    public JSONObject getHostIfno(PlatformconfigDTO p, String token, String vmID) {
        String url = p.getUrl().replace(":8443",":7443") +  vmID;
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body);
    }

    @Override
    public JSONArray getSnapshots(PlatformconfigDTO p, String token, String vmID) {
        String url = p.getUrl().replace(":8443",":7443") +  vmID + "/snapshots";
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("rootSnapshots");
    }

    @Override
    public JSONArray getRealtimeData(PlatformconfigDTO p, String token ,String siteId,String urn) {
        String url = p.getUrl() +  FusionOneApiConstant.GET_REALTIME_DATA.replace("{siteId}", siteId);
        Map<String, Object> param = new HashMap<>();
        param.put("urn", urn);
        String[] metricId = {
//                "bios_release_date",
                "product_name",
                "product_serial",
                "product_mfg",
//                "board_prod",
//                "board_serial",
//                "board_mfg_date",
//                "board_mfg",
//                "bios_version",
                "cpu_info"
        };
        param.put("metricId", metricId);

        List<Map<String, Object>> list = new ArrayList<>();
        list.add(param);
        Map<String, String> headers = new HashMap<>();
        headers.put("Accept","application/json;version=8.0; charset=UTF-8");
        String host="";
        try {
            URL urlObj = new URL(url);
            host = urlObj.getHost() + ":" + urlObj.getPort();
            // 如果端口是-1(默认端口)，可以根据协议设置默认端口
            if (urlObj.getPort() == -1) {
                int defaultPort = urlObj.getProtocol().equalsIgnoreCase("https") ? 443 : 80;
                host = urlObj.getHost() + ":" + defaultPort;
            }
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
        headers.put("Host",host);
        headers.put("X-Auth-Token", token);
        HttpRequest request = new HttpRequest(url);
        // 设置请求方法
        request.cookie(token);
        request.setMethod(Method.POST);
        request.body(JSONUtil.toJsonStr(list)); // 这里简化了，实际中可能需要将params转为JSON字符串
        if (!headers.isEmpty()) {
            headers.forEach(request::header);
        }
        // 发送请求
        HttpResponse response = request.execute();
        // 使用传入的响应解析器处理响应
        if (response.getStatus() == 200) {
            return JSONObject.parseObject(response.body()).getJSONArray("items").getJSONObject(0).getJSONArray("value");
        } else {
            throw new RuntimeException("请求失败，状态码：" + response.getStatus());
        }
    }

    @Override
    public JSONObject getMemoryIfno(PlatformconfigDTO p, String token, String computeResourceStatics) {
        String url = p.getUrl().replace(":8443",":7443") +  computeResourceStatics;
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body).getJSONObject("memResource");
    }

    @Override
    public JSONObject getHostCpuInfo(PlatformconfigDTO p, String token, String ip) {
        String url = p.getUrl() + FusionOneApiConstant.GET_HOST_INFO.replace("{vmId}", ip);
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body);
    }

    @Override
    public JSONArray getSystemIntFs(PlatformconfigDTO p, String token, String siteId,String urn) {
        String url = p.getUrl() + FusionOneApiConstant.GET_HOST_SYSTEMINTFS.replace("{siteId}", siteId).replace("{urn}", urn);
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("systemIntfs");
    }

    @Override
    public JSONArray getDvswitchs(PlatformconfigDTO p, String token, String siteId) {
        String url = p.getUrl() + FusionOneApiConstant.GET_DVSWITCHS.replace("{siteId}", siteId);
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("dvSwitchs");
    }

    @Override
    public JSONArray getDvswitchInfo(PlatformconfigDTO p, String token, String siteId,String urn) {
        String url = p.getUrl() + FusionOneApiConstant.GET_DVSWITCH_INFO.replace("{siteId}", siteId).replace("{urn}", urn);
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("hostPortSet");
    }

    @Override
    public JSONObject getInfoByVms(PlatformconfigDTO p, String token, String uri) {
        String url = p.getUrl() + uri;
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body);
    }

    @Override
    public JSONArray getRelaTimeData(PlatformconfigDTO p, String token,String siteId, String urn) {
        String host = p.getUrl().replace("https://", "");
        String newUrl = p.getUrl().replace(":8443", ":7443");
        JSONArray requestArray = new JSONArray();
        JSONObject requestObj = new JSONObject();
        String url = newUrl + FusionOneApiConstant.GET_REAL_TIME_DATA.replace("{siteId}", siteId);
        JSONArray metricArray = new JSONArray();
        metricArray.add("cpu_usage");
        metricArray.add("mem_usage");
        metricArray.add("disk_usage");
        metricArray.add("nic_original_info");
        metricArray.add("disk_io_info");

        requestObj.put("metricId", metricArray);
        requestObj.put("urn", urn);
        requestArray.add(requestObj);

        String jsonBody = requestArray.toJSONString();
        HashMap<String, String> headers = new HashMap<>();
        headers.put("Host", host);
        headers.put("Accept", "application/json;version=8.0;charset=UTF-8");
//        headers.put("Content-Type", "application/json; charset=UTF-8");
//        headers.put("Accept-Language", "zh_CN");
        headers.put("X-Auth-Token", token);
        try {
            HttpResponse res = HttpRequest.post(url)
                    .addHeaders(headers)
                    .body(jsonBody)
                    .execute();

            return JSONObject.parseObject(res.body()).getJSONArray("items");
        } catch (Exception e) {
            log.info("获取授权异常：" + e.getMessage());
        }
        return null;
    }

    @Override
    public JSONArray getRelaTimeDataNetWork(PlatformconfigDTO p, String token,String siteId, String urn) {
        String host = p.getUrl().replace("https://", "");
        String newUrl = p.getUrl().replace(":8443", ":7443");
        JSONArray requestArray = new JSONArray();
        JSONObject requestObj = new JSONObject();
        String url = newUrl + FusionOneApiConstant.GET_REAL_TIME_DATA.replace("{siteId}", siteId);
        JSONArray metricArray = new JSONArray();
        metricArray.add("nic_byte_in");
        metricArray.add("nic_byte_out");


        requestObj.put("metricId", metricArray);
        requestObj.put("urn", urn);
        requestArray.add(requestObj);

        String jsonBody = requestArray.toJSONString();
        HashMap<String, String> headers = new HashMap<>();
        headers.put("Host", host);
        headers.put("Accept", "application/json;version=8.1;charset=UTF-8");
//        headers.put("Content-Type", "application/json; charset=UTF-8");
//        headers.put("Accept-Language", "zh_CN");
        headers.put("X-Auth-Token", token);
        try {
            HttpResponse res = HttpRequest.post(url)
                    .addHeaders(headers)
                    .body(jsonBody)
                    .execute();

            return JSONObject.parseObject(res.body()).getJSONArray("items");
        } catch (Exception e) {
            log.info("获取授权异常：" + e.getMessage());
        }
        return null;
    }

    @Override
    public JSONArray getCurvedata(PlatformconfigDTO p, String token,String siteId, String urn) {
        String host = p.getUrl().replace("https://", "");
        String newUrl = p.getUrl().replace(":8443", ":7443");
        String url = newUrl + FusionOneApiConstant.CURVE_DATA.replace("{siteId}", siteId);
        JSONArray metricArray = new JSONArray();
        // 定义每个指标
        String[] metrics = {
                "cpu_usage",
                "mem_usage",
                "logic_disk_usage",
                "disk_io_in",
                "disk_io_out",
                "nic_byte_in",
                "nic_byte_out"
        };
        long nowTime = new Date().getTime() / 1000;
        long startTime = nowTime - 900l;
        // 添加每个指标及其附加信息
        for (String metric : metrics) {
            JSONObject metricObject = new JSONObject();
            metricObject.put("metricId", metric);
            metricObject.put("urn", urn);
            metricObject.put("startTime", String.valueOf(startTime));
            metricObject.put("endTime",String.valueOf(nowTime));
            metricObject.put("interval", "60");
            metricArray.add(metricObject);
        }

        String jsonBody = metricArray.toJSONString();
        HashMap<String, String> headers = new HashMap<>();
        headers.put("Host", host);
        headers.put("Accept", "application/json;version=8.1;charset=UTF-8");
        headers.put("X-Auth-Token", token);
        try {
            HttpResponse res = HttpRequest.post(url)
                    .addHeaders(headers)
                    .body(jsonBody)
                    .execute();

            return JSONObject.parseObject(res.body()).getJSONArray("items");
        } catch (Exception e) {
            log.info("获取性能数据异常：" + e.getMessage());
        }
        return null;
    }

    @Override
    public JSONArray getStorages(PlatformconfigDTO p, String token, String siteId) {
        String url = p.getUrl().replace(":8443",":7443") + FusionOneApiConstant.DATASTORES_LIST.replace("{siteId}",
                siteId);
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("datastores");
    }

    @Override
    public JSONArray getNetWork(PlatformconfigDTO p, String token, String siteId) {
        String url = p.getUrl().replace(":8443",":7443") + FusionOneApiConstant.NETWORK_LIST.replace("{siteId}",
                siteId);
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("portGroups");
    }

    @Override
    public JSONArray getInternetspeed(PlatformconfigDTO p, String token, String siteId, String urn) {
        String host = p.getUrl().replace("https://", "");
        String newUrl = p.getUrl().replace(":8443", ":7443");
        String url = newUrl + FusionOneApiConstant.GET_REAL_TIME_DATA.replace("{siteId}", siteId);
        JSONArray metricArray = new JSONArray();
        // 定义每个指标
        String[] metricId = {"nic_byte_in","nic_byte_out"};
        JSONObject metricObject = new JSONObject();
        metricObject.put("metricId", metricId);
        metricObject.put("urn", urn);
        metricArray.add(metricObject);
        String jsonBody = metricArray.toJSONString();
        HashMap<String, String> headers = new HashMap<>();
        headers.put("Host", host);
        headers.put("Accept", "application/json;version=8.1;charset=UTF-8");
        headers.put("X-Auth-Token", token);
        try {
            HttpResponse res = HttpRequest.post(url)
                    .addHeaders(headers)
                    .body(jsonBody)
                    .execute();

            return JSONObject.parseObject(res.body()).getJSONArray("items");
        } catch (Exception e) {
            log.info("获取性能数据异常：" + e.getMessage());
        }
        return null;
    }

    @Override
    public JSONObject getSnapshotsInfo(PlatformconfigDTO platform, String token, String uriSnap) {
        String url = platform.getUrl() + uriSnap;
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body);
    }

    @Override
    public JSONObject getVolumeInfo(PlatformconfigDTO platform, String token, String volumeUri) {
        String url = platform.getUrl() + volumeUri;
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body);
    }

    @Override
    public JSONObject getImageInfo(PlatformconfigDTO platform, String token, String uri) {
        String url = platform.getUrl() + uri;
        Map<String, Object> param = new HashMap<>();
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body);
    }


    @Override
    public JSONObject getVolumes(PlatformconfigDTO p, String token, String siteId, String volUrns) {
        String url = p.getUrl() + FusionOneApiConstant.GET_VOLUMES.replace("{siteId}", siteId);
        Map<String, Object> param = new HashMap<>();
        param.put("volUrns", volUrns);
        param.put("limit", 100);
        param.put("refreshflag", true);
        param.put("offset", 0);
        String body = fetchMetricUtilization(url, token, new HashMap<>(), param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("volumes").getJSONObject(0);
    }
}
