package cn.iocoder.zj.module.collection.job.image;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.module.collection.dal.redis.fusionOne.FusionOneAccessTokenRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.service.cloud.FusionOneDeviceService;
import cn.iocoder.zj.module.monitor.api.imageinfo.ImageInfoApi;
import cn.iocoder.zj.module.monitor.api.imageinfo.dto.ImageInfoCreateReqDTO;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class FusionOneImageJob {
    @Resource
    PlatformRedisDAO platformRedisDAO;
    @Resource
    ImageInfoApi imageInfoApi;
    @Resource
    FusionOneAccessTokenRedisDAO fusionOneAccessTokenRedisDAO;
    @Resource
    FusionOneDeviceService fusionOneDeviceService;

    private static final String PLATFORM_TYPE = "fusionOne";
    private static final String TOKEN_KEY_PREFIX = "FusionOne:";
    private static final String IMAGE_STATUS = "Enabled";
    private static final String IMAGE_TYPE = "RootVolumeTemplate";

    @XxlJob("fusionOneImageJob")
    public void fusionOneImageJob() {
        try {
            List<PlatformconfigDTO> vmwarePlatforms = platformRedisDAO.get("platform").stream()
                    .filter(dto -> PLATFORM_TYPE.equals(dto.getTypeCode()))
                    .collect(Collectors.toList());
            if (vmwarePlatforms.isEmpty()) {
                return;
            }

            for (PlatformconfigDTO platform : vmwarePlatforms) {
                try {
                    handlePlatform(platform);
                } catch (Exception e) {
                    log.error("处理平台 [{}] 失败: {}", platform.getName(), e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            log.error("fusionOne快照同步任务执行失败", e);
        }
    }

    private void handlePlatform(PlatformconfigDTO platform) {
        try {
            if (!PLATFORM_TYPE.equals(platform.getTypeCode())) {
                return;
            }
            Map<String, String> tokenInfo = getTokenInfo(platform.getId());
            if (tokenInfo == null) {
                return;
            }

            List<Map<String, Object>> imageList = getImageList(platform, tokenInfo);
            if (!imageList.isEmpty()) {
                List<ImageInfoCreateReqDTO> currentSnapshots = convertToImageInfoDTOs(imageList, platform);
                syncImageInfo(currentSnapshots);
            }

        } catch (Exception e) {
            log.error("处理平台[{}]快照信息失败", platform.getName(), e);
        }
    }

    private Map<String, String> getTokenInfo(Long platformId) {
        Map<String, Object> tokenMap = fusionOneAccessTokenRedisDAO.get(TOKEN_KEY_PREFIX + platformId);
        if (tokenMap == null) {
            log.info("Redis中未找到FusionOne token信息");
            return null;
        }

        String token = Convert.toStr(tokenMap.get("token"));
        String siteId = Convert.toStr(tokenMap.get("siteId"));

        if (StrUtil.hasBlank(token, siteId)) {
            log.info("FusionOne token或siteId为空");
            return null;
        }

        Map<String, String> result = new HashMap<>();
        result.put("token", token);
        result.put("siteId", siteId);
        return result;
    }

    private List<Map<String, Object>> getImageList(PlatformconfigDTO platform, Map<String, String> tokenInfo) {
        List<Map<String, Object>> imageList = new ArrayList<>();
        JSONArray hosts = fusionOneDeviceService.getClouds(platform, tokenInfo.get("token"), tokenInfo.get("siteId"));
        if (hosts == null) {
            return imageList;
        }

        for (int i = 0; i < hosts.size(); i++) {
            JSONObject host = hosts.getJSONObject(i);
            processHostImages(platform, tokenInfo.get("token"), host, imageList);
        }

        return imageList;
    }

    private void processHostImages(PlatformconfigDTO platform, String token,JSONObject host, List<Map<String, Object>> imageList) {
        String uri = host.getString("uri");
        if(host.getBoolean("isTemplate")){
            JSONObject cloudInfo = fusionOneDeviceService.getImageInfo(platform, token, uri);
            Map<String, Object> image = new HashMap<>();
            image.put("uuid",cloudInfo.getString("uuid"));
            image.put("name",cloudInfo.getString("name"));
            image.put("status",cloudInfo.getString("status"));
            image.put("cpu_arch",cloudInfo.getString("arch"));
            String osType = cloudInfo.getJSONObject("osOptions").getString("osType");
            JSONObject disk = cloudInfo.getJSONObject("vmConfig").getJSONArray("disks").getJSONObject(0);
            JSONObject memory = cloudInfo.getJSONObject("vmConfig").getJSONObject("memory");
            image.put("os_type",osType);
            image.put("v_create_date",cloudInfo.getString("createTime"));
            image.put("min_memory",memory.getString("quantityMB"));
            image.put("min_disk",disk.getString("quantityGB"));
            image.put("format",disk.getString("volumeFormat"));
            imageList.add(image);
        }
    }

    private List<ImageInfoCreateReqDTO> convertToImageInfoDTOs(List<Map<String, Object>> vmImages,PlatformconfigDTO platform) {
        return vmImages.stream().map(vmImage -> {
            ImageInfoCreateReqDTO imageInfoDTO = new ImageInfoCreateReqDTO();
            imageInfoDTO.setUuid(vmImage.get("uuid").toString());
            imageInfoDTO.setName(vmImage.get("name").toString());
            imageInfoDTO.setStatus(IMAGE_STATUS);
            imageInfoDTO.setFormat(vmImage.get("format").toString());
            imageInfoDTO.setCpuArch(vmImage.get("cpu_arch").toString());

            String osType = Objects.toString(vmImage.get("os_type"), "").toLowerCase();
            imageInfoDTO.setOsType(vmImage.get("os_type").toString());
            imageInfoDTO.setApplicationPlatform(determineApplicationPlatform(osType));
            imageInfoDTO.setImageType(IMAGE_TYPE);
            imageInfoDTO.setSharingScope("不共享");

            Date createTime = parseCreateTime(vmImage.get("createTime"));
            imageInfoDTO.setVCreateDate(DateUtil.toLocalDateTime(createTime));
            imageInfoDTO.setVUpdateDate(DateUtil.toLocalDateTime(createTime));

            imageInfoDTO.setOsLanguage("");
            imageInfoDTO.setMinMemory(BigDecimal.valueOf(Long.parseLong(vmImage.get("min_memory").toString())*1024*1024));
            imageInfoDTO.setMinDisk(BigDecimal.valueOf(Long.parseLong(vmImage.get("min_disk").toString())*1024*1024*1024));
            imageInfoDTO.setDiskDriver("");
            imageInfoDTO.setNetworkDriver("");
            imageInfoDTO.setBootMode("");
            imageInfoDTO.setRemoteProtocol("");

            imageInfoDTO.setPlatformId(platform.getId());
            imageInfoDTO.setPlatformName(platform.getName());
            return imageInfoDTO;
        }).collect(Collectors.toList());
    }

    private void syncImageInfo(List<ImageInfoCreateReqDTO> newImageInfos) {
        try {
            List<ImageInfoCreateReqDTO> existingImages = imageInfoApi.getAllImagesByTypeCode(PLATFORM_TYPE).getData();
            if (existingImages == null || existingImages.isEmpty()) {
                imageInfoApi.batchCreateImageInfo(newImageInfos);
                return;
            }
            // 计算需要新增、更新和删除的镜像
            Map<String, List<ImageInfoCreateReqDTO>> diffResult = calculateImageDiff(newImageInfos, existingImages);
            executeSyncOperations(diffResult);
        } catch (Exception e) {
            log.error("同步镜像信息失败: {}", e.getMessage(), e);
        }
    }

    private Map<String, List<ImageInfoCreateReqDTO>> calculateImageDiff(
            List<ImageInfoCreateReqDTO> newImages,
            List<ImageInfoCreateReqDTO> existingImages) {

        Map<String, List<ImageInfoCreateReqDTO>> result = new HashMap<>();
        // 计算需要新增的镜像
        result.put("create", newImages.stream()
                .filter(newImage -> existingImages.stream()
                        .noneMatch(existing -> existing.getUuid().equals(newImage.getUuid())))
                .collect(Collectors.toList()));
        // 计算需要更新的镜像
        result.put("update", newImages.stream()
                .filter(newImage -> existingImages.stream()
                        .anyMatch(existing -> existing.getUuid().equals(newImage.getUuid())))
                .collect(Collectors.toList()));
        // 计算需要删除的镜像
        result.put("delete", existingImages.stream()
                .filter(existing -> newImages.stream()
                        .noneMatch(newImage -> newImage.getUuid().equals(existing.getUuid())))
                .collect(Collectors.toList()));
        return result;
    }

    private void executeSyncOperations(Map<String, List<ImageInfoCreateReqDTO>> diffResult) {
        List<ImageInfoCreateReqDTO> toCreate = diffResult.get("create");
        List<ImageInfoCreateReqDTO> toUpdate = diffResult.get("update");
        List<ImageInfoCreateReqDTO> toDelete = diffResult.get("delete");
        if (!toCreate.isEmpty()) {
            imageInfoApi.batchCreateImageInfo(toCreate);
        }
        if (!toUpdate.isEmpty()) {
            imageInfoApi.batchUpdateImageInfo(toUpdate);
        }
        if (!toDelete.isEmpty()) {
            imageInfoApi.batchDeleteImageInfo(toDelete);
        }
    }

    private String determineApplicationPlatform(String osType) {
        if (osType.contains("linux") || osType.contains("centos")) {
            return "Linux";
        } else if (osType.contains("windows")) {
            return "Windows";
        }
        return "";
    }

    private Date parseCreateTime(Object createTimeObj) {
        if (createTimeObj instanceof Long) {
            return new Date((Long) createTimeObj);
        } else if (createTimeObj instanceof Date) {
            return (Date) createTimeObj;
        }
        return new Date();
    }
}
