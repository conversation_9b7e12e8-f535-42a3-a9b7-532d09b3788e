package cn.iocoder.zj.module.collection.job.volume;

import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.winhong.WinHongAccessTokenRedisDAO;
import cn.iocoder.zj.module.collection.service.cloud.IWinHongDeviceService;
import cn.iocoder.zj.module.monitor.api.valume.VolumeApi;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeAttachableVmDTO;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeDTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class WinHongVolumeJob {

    @Resource
    private PlatformRedisDAO platformRedisDAO;
    @Resource
    private PlatformconfigApi platformconfigApi;

    @Autowired
    private IWinHongDeviceService winHongDeviceService;
    @Resource
    WinHongAccessTokenRedisDAO winHongAccessTokenRedisDAO;

    @Resource
    VolumeApi volumeApi;

    @XxlJob("getWinHongVolume")
    public void getWinHongVolume(){
        List<PlatformconfigDTO> platformconfigDTOList = new ArrayList<>();
        if (platformRedisDAO.get("platform") == null) {
            platformconfigDTOList = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform", platformconfigDTOList);
        }
        platformconfigDTOList = platformRedisDAO.get("platform");
        List<VolumeDTO> volumeDTOList = new ArrayList<>();
        List<VolumeAttachableVmDTO> volumeAttachableVmDTOS = new ArrayList<>();
        if (!platformconfigDTOList.isEmpty()) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                if (p.getTypeCode().equals("winhong") && winHongAccessTokenRedisDAO.get("winhong:" + p.getId()) != null) {
                    String token = winHongDeviceService.getToken(p);
                    if (token==null){
                        log.info("获取云宏token失败");
                        return;
                    }
                    JSONArray storagePools = winHongDeviceService.getStoragePools(p, token);
                    List<Map> storages = JSONObject.parseArray(storagePools.toJSONString(), Map.class);
                    if (!storages.isEmpty()) {
                        for (Map storage : storages) {
                            String storageUuid = storage.get("id").toString();
                            JSONArray volumes =  winHongDeviceService.getVolumes(p, token, storageUuid);
                            if (!volumes.isEmpty()) {
                                List<Map> volumeMaps = JSONObject.parseArray(volumes.toJSONString(), Map.class);
                                for (Map volumeMap : volumeMaps) {
                                    VolumeDTO volumeDTO = new VolumeDTO();
                                    String uuid = volumeMap.get("id").toString();
                                    Long platformId = p.getId();
                                    String platformName = p.getName();
                                    String name = volumeMap.get("name").toString();
                                    String description = volumeMap.get("remark").toString();
                                    String primaryStorageUuid = volumeMap.get("storagePoolId").toString();
                                    JSONArray userList = volumeMap.get("userList") == null? new JSONArray() : (JSONArray) volumeMap.get("userList");
                                    boolean isMount = false;
                                    String vminstanceUuid = "";
                                    if (!userList.isEmpty()){
                                        isMount = true;
                                        List<Map> userMaps = JSONObject.parseArray(userList.toJSONString(), Map.class);
                                        for (Map userMap : userMaps) {
                                            VolumeAttachableVmDTO  volumeAttachableVmDTO = new VolumeAttachableVmDTO();
                                            String domainName = userMap.get("domainName").toString();
                                            String domainId = userMap.get("domainId").toString();
                                            volumeAttachableVmDTO.setHostUuid(domainId);
                                            volumeAttachableVmDTO.setHostName(domainName);
                                            volumeAttachableVmDTO.setPlatformId(platformId);
                                            volumeAttachableVmDTO.setPlatformName(platformName);
                                            volumeAttachableVmDTO.setVolumeUuid(uuid);
                                            volumeAttachableVmDTO.setVolumeName(name);
                                            volumeAttachableVmDTOS.add(volumeAttachableVmDTO);
                                        }
                                        vminstanceUuid = userList.getJSONObject(0).getString("domainId");
                                    }
                                    String type = "Data";
                                    String format = volumeMap.get("type").toString();
                                    Long size = volumeMap.get("capacity")!=null? Long.parseLong(volumeMap.get("capacity").toString()):0;
                                    Long actualSize = volumeMap.get("allocation")!=null?Long.parseLong(volumeMap.get("allocation").toString()):0;
                                    String status = volumeMap.get("status").toString();
                                    Date vCreateDate = volumeMap.get("time")!=null? new Date(volumeMap.get("time").toString()):new Date();
                                    volumeDTO.setUuid(uuid);
                                    volumeDTO.setName(name);
                                    volumeDTO.setDescription(description);
                                    volumeDTO.setName(name);
                                    volumeDTO.setFormat(format);
                                    volumeDTO.setSize(size);
                                    volumeDTO.setType(type);
                                    volumeDTO.setActualSize(actualSize);
                                    if (status.equals("1")){
                                        volumeDTO.setState("Enabled");
                                    }else {
                                        volumeDTO.setState("Disabled");
                                    }
                                    volumeDTO.setStatus("Ready");
                                    volumeDTO.setPlatformId(platformId);
                                    volumeDTO.setPlatformName(platformName);
                                    volumeDTO.setPrimaryStorageUuid(primaryStorageUuid);
                                    volumeDTO.setVmInstanceUuid(vminstanceUuid);
                                    volumeDTO.setActualUse(actualSize);
                                    Long actualFree = size - actualSize > 0 ? size - actualSize : 0;
                                    volumeDTO.setActualFree(actualFree);
                                    volumeDTO.setDeleted(0L);
                                    BigDecimal result = BigDecimal.valueOf(actualSize).divide(BigDecimal.valueOf(size), 2, BigDecimal.ROUND_HALF_UP);
                                    volumeDTO.setActualRatio(result.compareTo(BigDecimal.valueOf(1)) > 0 ? "1.00" : result.toString());
                                    volumeDTO.setVCreateDate(vCreateDate);
                                    volumeDTO.setVUpdateDate(vCreateDate);
                                    volumeDTO.setMediaType(MediaType.ROTATE.getEnName());
                                    volumeDTO.setIsMount(isMount);
                                    volumeDTOList.add(volumeDTO);
                                }
                            }
                        }
                    }
                }
            }
            if (!volumeDTOList.isEmpty()){
                Long volumeCount = volumeApi.getVolumeCount().getData();
                List<VolumeDTO> dtos = volumeApi.getAllVolumes(-1L, "winhong").getData();
                if (volumeCount == 0) {
                    volumeApi.addVolumes(volumeDTOList);
                } else {
                    volumeApi.updateVolumes(volumeDTOList);
                    List<VolumeDTO> collect = volumeDTOList.stream()
                            .filter(item -> !dtos.stream().map(VolumeDTO::getUuid)
                                    .collect(Collectors.toList()).contains(item.getUuid()))
                            .collect(Collectors.toList());
                    List<VolumeDTO> deleteTarget = dtos.stream()
                            .filter(item -> !(volumeDTOList.stream().map(VolumeDTO::getUuid)
                                    .collect(Collectors.toList()).contains(item.getUuid()))
                            )
                            .collect(Collectors.toList());
                    if (deleteTarget.size() > 0) {
                        volumeApi.delVolumes(deleteTarget);
                    }
                    if (collect.size() > 0) {
                        volumeApi.addVolumes(collect);
                    }
                }
            }
            if (volumeAttachableVmDTOS.size() > 0) {
                Long attachableVmCount = volumeApi.getAttachableVmCount().getData();
                List<VolumeAttachableVmDTO> dtos = volumeApi.getAllVolumeAttachableVm().getData();
                if (attachableVmCount == 0) {
                    volumeApi.addVolumeAttachableVms(volumeAttachableVmDTOS);
                } else {
                    volumeApi.updateVolumeAttachableVms(volumeAttachableVmDTOS);
                    List<VolumeAttachableVmDTO> collect = volumeAttachableVmDTOS.stream()
                            .filter(item -> !(dtos.stream().map(VolumeAttachableVmDTO::getVolumeUuid)
                                    .collect(Collectors.toList()).contains(item.getVolumeUuid())
                                    && dtos.stream().map(VolumeAttachableVmDTO::getHostUuid)
                                    .collect(Collectors.toList()).contains(item.getHostUuid()))
                            )
                            .collect(Collectors.toList());
                    List<VolumeAttachableVmDTO> deleteTarget = dtos.stream()
                            .filter(item -> !(volumeAttachableVmDTOS.stream().map(VolumeAttachableVmDTO::getVolumeUuid)
                                    .collect(Collectors.toList()).contains(item.getVolumeUuid())
                                    && volumeAttachableVmDTOS.stream().map(VolumeAttachableVmDTO::getHostUuid)
                                    .collect(Collectors.toList()).contains(item.getHostUuid()))
                            )
                            .collect(Collectors.toList());
                    if (deleteTarget.size() > 0) {
                        volumeApi.delVolumeAttachableVms(deleteTarget);
                    }
                    if (collect.size() > 0) {
                        volumeApi.addVolumeAttachableVms(collect);
                    }
                }
            }
        }
    }
}
