package cn.iocoder.zj.module.collection.service.cloud;

import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.Map;

public interface ISangForDeviceService {

    JSONArray getClouds(PlatformconfigDTO p, JSONObject tokenInfo);

    JSONArray getCloudsVms(PlatformconfigDTO p, JSONObject tokenInfo);

    JSONObject getNetInfos(PlatformconfigDTO p, JSONObject tokenInfo, String vmid);

    JSONArray getHardware(PlatformconfigDTO p, JSONObject tokenInfo);

    JSONObject getHardwareDetail(PlatformconfigDTO p, JSONObject tokenInfo, String id);

    JSONArray getStorages(PlatformconfigDTO p, JSONObject tokenInfo);

    JSONObject getStorageDetail(PlatformconfigDTO p, JSONObject tokenInfo, String id);

    JSONArray getNetworks(PlatformconfigDTO p, JSONObject tokenInfo);

    Map<String,JSONArray> getNetworksInfo(PlatformconfigDTO p, JSONObject tokenInfo);

    JSONArray getCpuOveruseConfig(PlatformconfigDTO p, JSONObject tokenInfo);

    JSONArray getMemoryOveruseConfig(PlatformconfigDTO p, JSONObject tokenInfo);

    JSONArray getStoragesUsageInPercent(PlatformconfigDTO p, JSONObject tokenInfo,String storageId);

    JSONObject getVmDetail(PlatformconfigDTO p, JSONObject tokenInfo, String vmid);

    JSONObject getHostDetail(PlatformconfigDTO p, JSONObject tokenInfo, String nodeid);

    JSONObject getHardwareCloudDetail(PlatformconfigDTO p, JSONObject tokenInfo, String id);

    JSONObject getNetCloudInfos(PlatformconfigDTO p, JSONObject tokenInfo, String vmid);

    JSONArray getStorageClouds(PlatformconfigDTO p, JSONObject tokenInfo);


    Map getNodeSheetByHardId(PlatformconfigDTO p, JSONObject tokenInfo, String id);

    JSONObject getStorageList(PlatformconfigDTO p, JSONObject tokenInfo, String id);

    JSONArray getStoragesRealTime(PlatformconfigDTO p, JSONObject tokenInfo);

    JSONObject getClusterIp(PlatformconfigDTO p, JSONObject tokenInfo);

    JSONArray getCloudsByHostId(PlatformconfigDTO p, JSONObject tokenInfo, String id);

    JSONArray getNicInfo(PlatformconfigDTO p, JSONObject tokenInfo);

    JSONArray getVmNics(PlatformconfigDTO p, JSONObject tokenInfo, String vmid);

    JSONArray getVtpstorageDetail(PlatformconfigDTO p, JSONObject tokenInfo, String id);

    JSONObject getVmSnapshots(PlatformconfigDTO p, JSONObject tokenInfo, String vmid);

    JSONObject getVmBios(PlatformconfigDTO p, JSONObject tokenInfos, String vmid);
}
