package cn.iocoder.zj.module.collection.job.storage;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.module.collection.dal.redis.fusionOne.FusionOneAccessTokenRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.zstack.AlarmInfoRedisDAO;
import cn.iocoder.zj.module.collection.framework.influx.config.InfluxDBTemplate;
import cn.iocoder.zj.module.collection.service.cloud.FusionOneDeviceService;
import cn.iocoder.zj.module.collection.util.StateConverter;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import cn.iocoder.zj.module.monitor.api.hardware.HardWareInfoApi;
import cn.iocoder.zj.module.monitor.api.hardwarestorage.HardWareStorageApi;
import cn.iocoder.zj.module.monitor.api.hardwarestorage.HardWareStorageRespDTO;
import cn.iocoder.zj.module.monitor.api.storage.StorageInfoApi;
import cn.iocoder.zj.module.monitor.api.storage.dto.StorageRespCreateReqDTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Component
public class FusionOneStorageJob {


    @Resource
    PlatformRedisDAO platformRedisDAO;

    @Resource
    PlatformconfigApi platformconfigApi;

    @Resource
    AlarmInfoRedisDAO alarmInfoRedisDAO;

    @Resource
    FusionOneAccessTokenRedisDAO fusionOneAccessTokenRedisDAO;

    @Resource
    FusionOneDeviceService fusionOneDeviceService;

    @Resource
    HardWareInfoApi hardWareInfoApi;

    @Autowired
    private AlarmConfigApi alarmConfigApi;

    @Resource
    InfluxDBTemplate influxDBTemplate;

    @Resource
    StorageInfoApi storageInfoApi;

    @Resource
    HardWareStorageApi hardWareStorageApi;

    @XxlJob("fusionOneStorage")
    public void fusionOneStorage() {
        List<StorageRespCreateReqDTO> storageRespCreateReqDTOS = new ArrayList<>();
        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");

        if (platformconfigDTOList.isEmpty()) {
            return; // 提前返回，避免不必要的嵌套
        }
        for (PlatformconfigDTO p : platformconfigDTOList) {
            if (!isFusionOnePlatform(p)) continue;

            String token = getFusionOneToken(p);
            if (token == null) {
                log.info("获取FusionOne token失败");
                continue; // 继续处理下一个平台
            }
            List<HardWareStorageRespDTO> storageList = new ArrayList<>();
            String siteId = getSiteId(p);
            JSONArray storages = fusionOneDeviceService.getStorages(p, token, siteId);
            if (ObjectUtil.isNull(storages)) continue;
            JSONArray hosts = fusionOneDeviceService.getHosts(p,token,siteId);
            Map<String, JSONObject> hostMap = IntStream.range(0, hosts.size())
                    .mapToObj(hosts::getJSONObject)
                    .collect(Collectors.toMap(host -> host.getString("urn"), host -> host));
            for (int j = 0; j < storages.size(); j++) {
                storageRespCreateReqDTOS.add(createStorageRespCreateReqDTO(storages.getJSONObject(j), p,hostMap,storageList));
            }

            if (storageList.size()>0){
                List<HardWareStorageRespDTO> oldList = hardWareStorageApi.getHardWareStorageByPlatformId(p.getId()).getCheckedData();
                if (oldList.isEmpty()){
                    hardWareStorageApi.adds(storageList);
                }else {
                    // 修改Map的key为uuid+hardwareUuid的组合
                    Map<String, HardWareStorageRespDTO> existingHardwareMap = oldList.stream()
                            .collect(Collectors.toMap(
                                    hardwareStorage -> hardwareStorage.getHardwareUuid() + "_" + hardwareStorage.getStorageUuid(),
                                    hardwareStorage -> hardwareStorage
                            ));

                    List<HardWareStorageRespDTO> newEntries = new ArrayList<>();
                    List<HardWareStorageRespDTO> updatedEntries = new ArrayList<>();
                    // 修改删除条件，同时比对uuid和hardwareUuid
                    List<HardWareStorageRespDTO> deleteEntries = oldList.stream()
                            .filter(item -> !storageList.stream()
                                    .anyMatch(newItem ->
                                            newItem.getHardwareUuid().equals(item.getHardwareUuid()) &&
                                                    newItem.getStorageUuid().equals(item.getStorageUuid())
                                    ))
                            .collect(Collectors.toList());

                    for (HardWareStorageRespDTO hardWareStorageRespDTO : storageList) {
                        // 使用组合key来查找
                        String compositeKey = hardWareStorageRespDTO.getHardwareUuid() + "_" + hardWareStorageRespDTO.getStorageUuid();
                        HardWareStorageRespDTO nicRespDTO = existingHardwareMap.get(compositeKey);
                        if (nicRespDTO == null) {
                            newEntries.add(hardWareStorageRespDTO);
                        } else if (!nicRespDTO.equals(hardWareStorageRespDTO)) {
                            updatedEntries.add(hardWareStorageRespDTO);
                        }
                    }
                    hardWareStorageApi.updates(updatedEntries);
                    hardWareStorageApi.adds(newEntries);
                    if (!deleteEntries.isEmpty()) {
                        hardWareStorageApi.deletes(deleteEntries);
                    }
                }
            }
        }

        if (!storageRespCreateReqDTOS.isEmpty()) {
            processStorageData(storageRespCreateReqDTOS);
        }
    }

    @XxlJob("fusionOneStorageToInflux")
    public void fusionOneHostToInflux() {
        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        if (platformconfigDTOList.isEmpty()) {
            return; // 提前返回，避免不必要的嵌套
        }
        for (PlatformconfigDTO p : platformconfigDTOList) {
            if (isFusionOnePlatform(p)) {
                String token = getFusionOneToken(p);
                if (token == null) {
                    log.info("获取FusionOne token失败");
                    continue; // 继续处理下一个平台
                }
                String siteId = getSiteId(p);
                JSONArray storages = fusionOneDeviceService.getStorages(p, token, siteId);
                if (ObjectUtil.isNull(storages)) continue;
                BatchPoints batchPoints = BatchPoints.builder().build();
                for (Object storage : storages) {
                    JSONObject jsonObject = (JSONObject) storage;
                    String urn = jsonObject.getString("urn");
                    String name = jsonObject.getString("name");
                    //容量使用率
                    BigDecimal usageRate = BigDecimal.ZERO;
                    BigDecimal totalCapacity = jsonObject.getBigDecimal("actualCapacityGB");
                    Long usedCapacity = jsonObject.getLong("usedSizeGB");
                    if (totalCapacity != null && totalCapacity.compareTo(BigDecimal.ZERO) > 0) {
                        usageRate = BigDecimal.valueOf(usedCapacity).divide(totalCapacity, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
                    }
                    Point point = Point.measurement("zj_cloud_storage")
                            .tag("uuid", urn)
                            .tag("metricName", "UsedCapacityInPercent")
                            .addField("productsName", name)
                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                            .tag("storage_type", "UsedCapacityInPercent")
                            .tag("platformId", StringUtil.toString(p.getId()))
                            .addField("platformName", StringUtil.toString(p.getName()))
                            .addField("storage_metricName", "UsedCapacityInPercent")
                            .addField("type", "UsedCapacityInPercent")
                            .addField("value", usageRate)
                            .time(new Date().getTime() / 1000, TimeUnit.SECONDS)
                            .build();
                    batchPoints.point(point);
                }
                int shardIndex = XxlJobHelper.getShardIndex();
                int shardTotal = XxlJobHelper.getShardTotal();
                log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
                List<Point> s = StringUtil.getShardingData(batchPoints.getPoints(), shardTotal, shardIndex);
                influxDBTemplate.writeBatch(BatchPoints.builder().points(s).build());
            }
        }
    }

    private StorageRespCreateReqDTO createStorageRespCreateReqDTO(JSONObject jsonObject, PlatformconfigDTO p,Map<String, JSONObject> hostMap,List<HardWareStorageRespDTO> storageList) {
        String siteName = fusionOneAccessTokenRedisDAO.get("FusionOne:" + p.getId()).get("siteName").toString();
        StorageRespCreateReqDTO storageRespCreateReqDTO = new StorageRespCreateReqDTO();
        storageRespCreateReqDTO.setName(jsonObject.getString("name"));
        storageRespCreateReqDTO.setUuid(jsonObject.getString("urn"));
        storageRespCreateReqDTO.setUrl("not used");
        storageRespCreateReqDTO.setState(jsonObject.getString("status").equals("NORMAL") ? "Enabled" : "Disabled");
        storageRespCreateReqDTO.setStatus(jsonObject.getString("status").equals("NORMAL") ? "Connected" : "Disconnected");
        storageRespCreateReqDTO.setTotalCapacity(jsonObject.getLong("actualCapacityGB") * 1024 * 1024 * 1024);
        storageRespCreateReqDTO.setTotalPhysicalCapacity(jsonObject.getBigDecimal("actualCapacityGB").multiply(BigDecimal.valueOf(1024)).multiply(BigDecimal.valueOf(1024).multiply(BigDecimal.valueOf(1024))));
        storageRespCreateReqDTO.setUsedCapacity(jsonObject.getLong("usedSizeGB") * 1024 * 1024 * 1024);
        storageRespCreateReqDTO.setAvailableCapacity(jsonObject.getBigDecimal("actualFreeSizeGB").multiply(BigDecimal.valueOf(1024)).multiply(BigDecimal.valueOf(1024).multiply(BigDecimal.valueOf(1024))));
        storageRespCreateReqDTO.setAvailablePhysicalCapacity(storageRespCreateReqDTO.getAvailableCapacity()); // 直接使用已计算的值

        BigDecimal totalCapacity = jsonObject.getBigDecimal("actualCapacityGB");
        Long usedCapacity = jsonObject.getLong("usedSizeGB");
        BigDecimal usageRate = BigDecimal.ZERO;

        if (totalCapacity != null && totalCapacity.compareTo(BigDecimal.ZERO) > 0) {
            usageRate = BigDecimal.valueOf(usedCapacity).divide(totalCapacity, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
        }
        storageRespCreateReqDTO.setCapacityUtilization(usageRate);
        storageRespCreateReqDTO.setType(jsonObject.getString("storageType"));
        storageRespCreateReqDTO.setPlatformId(p.getId());
        storageRespCreateReqDTO.setPlatformName(p.getName());
        storageRespCreateReqDTO.setRegionId(p.getRegionId());
        storageRespCreateReqDTO.setDeleted(0);
        storageRespCreateReqDTO.setTypeName("FusionOne");
        storageRespCreateReqDTO.setCreateTime(new Date());
        storageRespCreateReqDTO.setSCreateTime(new Date());

        storageRespCreateReqDTO.setMediaType("机械盘");
        storageRespCreateReqDTO.setManager(p.getName());
        storageRespCreateReqDTO.setAvailableManager(siteName);
        storageRespCreateReqDTO.setStoragePercent(new BigDecimal(1));
        storageRespCreateReqDTO.setRemark(jsonObject.getString("description"));
        // 计算虚拟容量
        BigDecimal availableDecimal = storageRespCreateReqDTO.getAvailableCapacity().compareTo(new BigDecimal(0)) > 0 ?
                storageRespCreateReqDTO.getAvailableCapacity() : new BigDecimal(0);
        storageRespCreateReqDTO.setVirtualCapacity(storageRespCreateReqDTO.getTotalPhysicalCapacity().multiply(storageRespCreateReqDTO.getStoragePercent()));
        storageRespCreateReqDTO.setAllocation(storageRespCreateReqDTO.getTotalPhysicalCapacity().subtract(availableDecimal));
        storageRespCreateReqDTO.setCommitRate(storageRespCreateReqDTO.getAllocation().divide(availableDecimal, 2, RoundingMode.HALF_UP));
        JSONArray hosts = jsonObject.getJSONArray("hosts");
        HardWareStorageRespDTO storageRespDTO = new HardWareStorageRespDTO();
        if(CollectionUtil.isNotEmpty(hosts)){
            if(BeanUtil.isNotEmpty(hostMap.get(hosts.get(0).toString()))){
                JSONObject urn = hostMap.get(hosts.get(0).toString());
                storageRespDTO.setHardwareUuid(urn.getString("uuid"));
            }
        }
        storageRespDTO.setStorageUuid(jsonObject.getString("urn"));
        storageRespDTO.setPlatformId(p.getId());
        storageRespDTO.setPlatformName(p.getName());
        storageList.add(storageRespDTO);
        return storageRespCreateReqDTO;
    }

    private void processStorageData(List<StorageRespCreateReqDTO> storageRespCreateReqDTOS) {
        List<AlarmDorisReqDTO> alarmDorisReqDTO = new ArrayList<>();
        int storageCount = storageInfoApi.count("FusionOne");
        List<StorageRespCreateReqDTO> dtos = storageInfoApi.getAll("FusionOne").getData();
        if (storageCount == 0) {
            storageInfoApi.adds(storageRespCreateReqDTOS);
        } else {
            handleExistingStorage(dtos, storageRespCreateReqDTOS, alarmDorisReqDTO);
        }

        if (!alarmDorisReqDTO.isEmpty()) {
            Map<String, List> addMap = new HashMap<>();
            addMap.put("updateList", new ArrayList<>());
            addMap.put("insertList", alarmDorisReqDTO);
            alarmConfigApi.createAlarmToDoris(addMap);
        }

        // 软删除主存储历史数据
        List<StorageRespCreateReqDTO> toDelete = dtos.stream()
                .filter(item -> storageRespCreateReqDTOS.stream().noneMatch(dto -> dto.getUuid().equals(item.getUuid())))
                .collect(Collectors.toList());

        if (!toDelete.isEmpty()) {
            toDelete.forEach(item -> item.setDeleted(1));
            storageInfoApi.updates(toDelete);
        }

        storageInfoApi.updates(storageRespCreateReqDTOS);
        List<StorageRespCreateReqDTO> newStorage = storageRespCreateReqDTOS.stream()
                .filter(item -> dtos.stream().noneMatch(dto -> dto.getUuid().equals(item.getUuid())))
                .distinct()
                .collect(Collectors.toList());

        if (!newStorage.isEmpty()) {
            storageInfoApi.adds(newStorage);
        }
    }

    private List<StorageRespCreateReqDTO> getShardingData(List<StorageRespCreateReqDTO> storageRespCreateReqDTOS, int shardIndex, int shardTotal) {
        if (shardIndex < 0) {
            return storageRespCreateReqDTOS;
        }
        return StringUtil.getShardingData(storageRespCreateReqDTOS, shardTotal, shardIndex);
    }

    private void handleExistingStorage(List<StorageRespCreateReqDTO> dtos, List<StorageRespCreateReqDTO> shardingData, List<AlarmDorisReqDTO> alarmDorisReqDTO) {
        final Long alertId = alarmConfigApi.getMaxAlertId().getData();
        int i = 1;

        for (StorageRespCreateReqDTO item : shardingData) {
            for (StorageRespCreateReqDTO dto : dtos) {
                if (dto.getUuid().equals(item.getUuid()) && !dto.getStatus().equals(item.getStatus())) {
                    AlarmDorisReqDTO alarm = createAlarm(item, dto, alertId + i);
                    alarmDorisReqDTO.add(alarm);
                }
            }
            i++;
        }
    }

    private AlarmDorisReqDTO createAlarm(StorageRespCreateReqDTO item, StorageRespCreateReqDTO dto, Long alarmId) {
        AlarmDorisReqDTO alarm = new AlarmDorisReqDTO();
        JSONObject tags = new JSONObject();

        alarm.setId(alarmId);
        tags.put("app", "storage");
        tags.put("monitorId", item.getUuid());
        tags.put("monitorName", item.getName());

        alarm.setPlatformId(item.getPlatformId());
        alarm.setResourceType(0);
        alarm.setStatus(0);
        alarm.setIsSolved(0);
        alarm.setGmtCreate(new Date());
        alarm.setFirstAlarmTime(DateUtil.current());
        alarm.setGmtUpdate(DateUtil.date());
        alarm.setLastAlarmTime(DateUtil.current());
        alarm.setPlatformName(item.getPlatformName());
        alarm.setTimes(1);
        alarm.setMonitorId(item.getUuid());
        alarm.setContent("云存储:" + item.getName() + " 的启用状态由\"" + StateConverter.stateToCh(dto.getState()) + "\"转换为\"" + StateConverter.stateToCh(item.getState()) + "\"");
        alarm.setTarget("storage.state.changed");
        alarm.setApp("storage");
        alarm.setMonitorName(item.getName());
        alarm.setPriority(1);
        alarm.setAlarmId(0L);

        return alarm;
    }

    private boolean isFusionOnePlatform(PlatformconfigDTO p) {
        return "fusionOne".equals(p.getTypeCode()) && fusionOneAccessTokenRedisDAO.get("FusionOne:" + p.getId()) != null;
    }

    private String getFusionOneToken(PlatformconfigDTO p) {
        JSONObject tokenData = fusionOneAccessTokenRedisDAO.get("FusionOne:" + p.getId());
        return tokenData != null ? tokenData.getString("token") : null;
    }

    private String getSiteId(PlatformconfigDTO p) {
        JSONObject tokenData = fusionOneAccessTokenRedisDAO.get("FusionOne:" + p.getId());
        return tokenData != null ? tokenData.getString("siteId") : null;
    }

}
