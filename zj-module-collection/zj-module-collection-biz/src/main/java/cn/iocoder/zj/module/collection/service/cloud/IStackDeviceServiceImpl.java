package cn.iocoder.zj.module.collection.service.cloud;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import cn.iocoder.zj.module.collection.collect.http.MetricsFetcher;
import cn.iocoder.zj.module.collection.service.istack.IStackApiConstant;
import cn.iocoder.zj.module.collection.service.sangFor.core.SangForApiConstant;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.StringUtils;
import com.google.gson.JsonArray;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Log4j2
@Service
public class IStackDeviceServiceImpl implements IStackDeviceService {
    MetricsFetcher fetcher = new MetricsFetcher();
    @Override
    public JSONArray getClouds(PlatformconfigDTO platform) {
        String url = platform.getUrl() + IStackApiConstant.GET_HOST_DETAIL;
        Map<String, Object> param = new HashMap<>();
        param.put("os_id",platform.getPassword());
        param.put("ct_user_id",platform.getUsername());
        param.put("page","1");
        param.put("page_size","2000");
        String body = fetchMetricUtilization(url, param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("results");
    }

    @Override
    public JSONArray getCloudsByHost(PlatformconfigDTO platform,String hostname) {
        String url = platform.getUrl() + IStackApiConstant.GET_HOST_DETAIL;
        Map<String, Object> param = new HashMap<>();
        param.put("os_id",platform.getPassword());
        param.put("ct_user_id",platform.getUsername());
        param.put("page","1");
        param.put("page_size","2000");
        param.put("host_name",hostname);
        String body = fetchMetricUtilization(url, param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("results");
    }

    @Override
    public JSONArray getRelaTimeData(PlatformconfigDTO platform, String uuid,Boolean flag) {
        String url = platform.getUrl() + IStackApiConstant.GET_HOST_RELATIME;
        Map<String, Object> param = new HashMap<>();
        param.put("os_id",platform.getPassword());
        param.put("ct_user_id",platform.getUsername());
        param.put("uuid",uuid);
        param.put("device_type","vm");
        param.put("item_names","cpu_util,mem_util,disk_util,disk_read_bytes_rate,disk_read_requests_rate,disk_write_bytes_rate,disk_write_requests_rate,net_in_bytes_rate,net_out_bytes_rate");
        long nowTime = (new Date().getTime()/ 1000)* 1000;
        if(flag){
            long fifteenMinutesAgo = nowTime - (15 * 60 * 1000);
            param.put("from", fifteenMinutesAgo);
        }else {
            param.put("from", nowTime);
        }
        param.put("to",nowTime);
        String body = fetchMetricUtilization(url, param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("results");
    }

    @Override
    public JSONObject getVmuse(PlatformconfigDTO platform, String uuid) {
        String url = platform.getUrl() + IStackApiConstant.GET_VM_USE;
        Map<String, Object> param = new HashMap<>();
        param.put("os_id",platform.getPassword());
        param.put("ct_user_id",platform.getUsername());
        param.put("pageNum","1");
        param.put("pageSize","10");
        param.put("poolid","1");
        param.put("instanceuuid",uuid);
        param.put("downExcelParameters","vm_disk_util,vm_volume_util");
        param.put("monitorStartTimeStamp",getCurrentDate());
        param.put("monitorEndTimeStamp",getCurrentDate());
        String body = fetchMetricUtilization(url, param, Method.GET);
        return JSONObject.parseObject(body).getJSONObject("results");
    }

    @Override
    public JSONArray getPerformanceData(PlatformconfigDTO platform, String uuid, String itemName,Boolean flag) {
        String url = platform.getUrl() + IStackApiConstant.GET_DISK_INFO;
        Map<String, Object> param = new HashMap<>();
        param.put("os_id",platform.getPassword());
        param.put("ct_user_id",platform.getUsername());
        param.put("uuid",uuid);
        param.put("item_name",itemName);
        long nowTime = (new Date().getTime()/ 1000)* 1000;
        if(flag){
            long fifteenMinutesAgo = nowTime - 15 * 60 * 1000;
            param.put("from", fifteenMinutesAgo);
        }else {
            param.put("from", nowTime);
        }
        param.put("to",nowTime);
        String body = fetchMetricUtilization(url, param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("results");
    }

    @Override
    public JSONArray getNetworks3(PlatformconfigDTO platform) {
        String url = platform.getUrl() + IStackApiConstant.GET_V_NETWORK3_LIST;
        Map<String, Object> param = new HashMap<>();
        param.put("os_id",platform.getPassword());
        param.put("ct_user_id",platform.getUsername());
        param.put("page","1");
        param.put("page_size","2000");
        String body = fetchMetricUtilization(url, param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("results");
    }

    @Override
    public JSONArray getNetDhcp(PlatformconfigDTO platform, String id) {
        String url = platform.getUrl() + IStackApiConstant.GET_V_DHCP_LIST;
        Map<String, Object> param = new HashMap<>();
        param.put("os_id",platform.getPassword());
        param.put("ct_user_id",platform.getUsername());
        param.put("page","1");
        param.put("page_size","2000");
        param.put("subnet_uuid",id);
        String body = fetchMetricUtilization(url, param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("results");
    }

    @Override
    public JSONArray getImageList(PlatformconfigDTO platform) {
        String url = platform.getUrl() + IStackApiConstant.GET_IMAGE_LIST;
        Map<String, Object> param = new HashMap<>();
        param.put("os_id",platform.getPassword());
        param.put("ct_user_id",platform.getUsername());
        String body = fetchMetricUtilization(url, param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("results");
    }

    @Override
    public JSONArray getVolumeSnapshotList(PlatformconfigDTO platform) {
        String url = platform.getUrl() + IStackApiConstant.GET_VOLUME_SNAPSHOT_LIST;
        Map<String, Object> param = new HashMap<>();
        param.put("os_id",platform.getPassword());
        param.put("ct_user_id",platform.getUsername());
        String body = fetchMetricUtilization(url, param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("results");
    }

    @Override
    public JSONArray getInstanceSnapshotList(PlatformconfigDTO platform) {
        String url = platform.getUrl() + IStackApiConstant.GET_INSTANCE_SNAPSHOT_LIST;
        Map<String, Object> param = new HashMap<>();
        param.put("os_id",platform.getPassword());
        param.put("ct_user_id",platform.getUsername());
        String body = fetchMetricUtilization(url, param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("results");
    }

    @Override
    public JSONArray getAllZone(PlatformconfigDTO platform) {
        String url = platform.getUrl() + IStackApiConstant.GET_ALL_ZONE;
        Map<String, Object> param = new HashMap<>();
        param.put("os_id",platform.getPassword());
        param.put("ct_user_id",platform.getUsername());
        param.put("pageNum","1");
        param.put("pageSize","100");
        String body = fetchMetricUtilization(url, param, Method.POST);
        return JSONObject.parseObject(body).getJSONArray("results");
    }

    @Override
    public JSONArray getCloudNics(PlatformconfigDTO p, String instance_uuid) {
        String url = p.getUrl() + IStackApiConstant.GET_VM_NICS;
        Map<String, Object> param = new HashMap<>();
        param.put("os_id",p.getPassword());
        param.put("ct_user_id",p.getUsername());
        param.put("instance_uuid",instance_uuid);
        String body = fetchMetricUtilization(url, param, Method.POST);
        return JSONObject.parseObject(body).getJSONArray("results");
    }

    @Override
    public JSONArray getEips(PlatformconfigDTO p, String instance_uuid) {
        String url = p.getUrl() + IStackApiConstant.GET_EIPS;
        Map<String, Object> param = new HashMap<>();
        param.put("os_id",p.getPassword());
        param.put("ct_user_id",p.getUsername());
        if (instance_uuid != null && !instance_uuid.isEmpty()){
            param.put("instance_uuid",instance_uuid);
        }
        String body = fetchMetricUtilization(url, param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("results");
    }

    @Override
    public JSONArray getSecgroup(PlatformconfigDTO p) {
        String url = p.getUrl() + IStackApiConstant.GET_SECGROUPS;
        Map<String, Object> param = new HashMap<>();
        param.put("os_id",p.getPassword());
        param.put("ct_user_id",p.getUsername());
        String body = fetchMetricUtilization(url, param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("results");
    }


    public static String getCurrentDate() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return LocalDate.now().format(formatter);
    }

    @Override
    public JSONArray getHardware(PlatformconfigDTO platform) {
        String url = platform.getUrl() + IStackApiConstant.GET_HARDWARE;
        Map<String, Object> param = new HashMap<>();
        param.put("os_id",platform.getPassword());
        param.put("ct_user_id",platform.getUsername());
        param.put("pageNo","1");
        param.put("pageSize","2000");
        String body = fetchMetricUtilization(url, param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("results");
    }

    @Override
    public JSONArray getStorages(PlatformconfigDTO platform) {
        String url = platform.getUrl() + IStackApiConstant.GET_STORAGE_LIST;
        Map<String, Object> param = new HashMap<>();
        param.put("os_id",platform.getPassword());
        param.put("ct_user_id",platform.getUsername());
        param.put("page","1");
        param.put("page_size","2000");
        String body = fetchMetricUtilization(url, param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("results");
    }

    @Override
    public JSONArray getNetworks(PlatformconfigDTO platform) {
        String url = platform.getUrl() + IStackApiConstant.GET_V_NETWORK_LIST;
        Map<String, Object> param = new HashMap<>();
        param.put("os_id",platform.getPassword());
        param.put("ct_user_id",platform.getUsername());
        param.put("page","1");
        param.put("page_size","2000");
        String body = fetchMetricUtilization(url, param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("results");
    }

    @Override
    public JSONArray getVmDetail(PlatformconfigDTO platform,String vmid) {
        String url = platform.getUrl() + IStackApiConstant.GET_VM_DETAIL;
        Map<String, Object> param = new HashMap<>();
        param.put("os_id",platform.getPassword());
        param.put("ct_user_id",platform.getUsername());
        if (vmid != null && !vmid.isEmpty()){
            param.put("instance_uuid",vmid);
        }
        String body = fetchMetricUtilization(url, param, Method.GET);
        return JSONObject.parseObject(body).getJSONArray("results");
    }

    private String fetchMetricUtilization(String url,Map<String, Object> param, Method method) {
        String response = fetcher.sendIstackRequest(url, method, param,
                httpResponse -> {
                    if (httpResponse.getStatus() == 200) {
                        return httpResponse.body();
                    } else {
                        throw new RuntimeException("请求失败，状态码：" + httpResponse.getStatus());
                    }
                });
        return response;
    }
}
