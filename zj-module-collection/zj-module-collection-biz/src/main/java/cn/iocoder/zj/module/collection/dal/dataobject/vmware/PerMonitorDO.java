package cn.iocoder.zj.module.collection.dal.dataobject.vmware;

import lombok.Data;

import java.util.Date;

/**
 * @ClassName : PerMonitorDO  //类名
 * @Description : 性能指标数据  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/7/25  10:24
 */
@Data
public class PerMonitorDO {

    /**
     * @description: 实例名称
     * <AUTHOR>
     * @date 2024/7/25 10:26
     * @version 1.0
     */
    private String instance;

    /**
     * @description: 实例值
     * <AUTHOR>
     * @date 2024/7/25 10:26
     * @version 1.0
     */
    private Long value;


    /**
     * @description: 时间
     * <AUTHOR>
     * @date 2024/7/25 10:26
     * @version 1.0
     */
    private Date dateTime;

    private Long counterId;
}
