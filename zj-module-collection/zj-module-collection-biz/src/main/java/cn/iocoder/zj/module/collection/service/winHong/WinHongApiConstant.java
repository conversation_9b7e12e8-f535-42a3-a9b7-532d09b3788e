package cn.iocoder.zj.module.collection.service.winHong;

public class <PERSON>HongApiConstant {

    public static final String WIN_HONG_API_PREFIX = "/api";
    public static final String LOGIN = WIN_HONG_API_PREFIX + "/login";

    public static final String MONITOR = "/monitor";

    public static final String COMPUTE = "/compute";
    public static final String REFRESH = WIN_HONG_API_PREFIX+"/check/session";
    public static final String GET_HARDWARE_LIST = WIN_HONG_API_PREFIX + "/compute/hosts";
    public static final String GET_CLOUD_LIST = WIN_HONG_API_PREFIX + "/compute/domains";

    public static final String GET_DISKS_INFO = WIN_HONG_API_PREFIX + MONITOR + "/host/disks_info";

    public static final String GET_FLOW_INFO = WIN_HONG_API_PREFIX + MONITOR + "/host/network/flow";

    public static final String GET_CPU_RATE= WIN_HONG_API_PREFIX + MONITOR + "/host/cpu_rate";

    public static final String GET_IO_STAT= WIN_HONG_API_PREFIX + MONITOR + "/host/disk/io_stat";

    public static final String GET_IO_REQ= WIN_HONG_API_PREFIX + MONITOR + "/host/disk/io_req";

    public static final String GET_CLOUDS = WIN_HONG_API_PREFIX + "/compute/domains";

    public static final String GET_DOMAIN_INFO = WIN_HONG_API_PREFIX + "/compute/domains/{domainId}";

    public static final String GET_SUMMARY = WIN_HONG_API_PREFIX + "/compute/domains/{domainId}/summary";

    public static final String GET_DISK_STORAGE_INFOS= WIN_HONG_API_PREFIX + "/compute/domains/{domainId}/diskStorageInfo";

    public static final String GET_CPU_AND_MEMORY_RATE = WIN_HONG_API_PREFIX + "/compute/domains/{domainId}/cpuAndMemoryRate";
    public static final String GET_DOMAIN_DISK_INFO = WIN_HONG_API_PREFIX + "/compute/domains/{domainId}/domainDiskInfo";
    public static final String GET_SNAPSHOTS = WIN_HONG_API_PREFIX + "/compute/domains/snapshots";

    public static final String GET_HOST_CPU_RATE= WIN_HONG_API_PREFIX + MONITOR + "/report/domain/cpuRate";
    public static final String GET_HOST_FLOW_LINK= WIN_HONG_API_PREFIX + MONITOR + "/report/domain/flowLink";
    public static final String GET_HOST_IO_STAT= WIN_HONG_API_PREFIX + MONITOR + "/report/domain/disk/ioStat";
    public static final String GET_HOST_IO_REQ= WIN_HONG_API_PREFIX + MONITOR + "/report/domain/disk/ioReq";
    public static final String GET_DOMAIN_DISKS_INFO= WIN_HONG_API_PREFIX + MONITOR + "/domain/disks_info";


    public static final String GET_NETWORK_FLOW= WIN_HONG_API_PREFIX + MONITOR + "/report/domain/network/flow";


    public static final String GET_STORAGE_POOLS = WIN_HONG_API_PREFIX + "/storage/storagePools";
    public static final String GET_NETWORKS = WIN_HONG_API_PREFIX + "/network/networks/external_nets";
    public static final String GET_NETWORKS_INFO = WIN_HONG_API_PREFIX + "/network/networks/external_nets/{networkId}/info";
    //存储池下存储卷列表
    public static final String GET_VOLUMES = WIN_HONG_API_PREFIX + "/storage/storagePools/{storagePoolId}/storageVolumes";

    public static final String GET_RESERVE_MEMORY=WIN_HONG_API_PREFIX+COMPUTE+"/hosts/{hostId}/reserve/memory";
    public static final String GET_PORT_GROUPS_V2=WIN_HONG_API_PREFIX+COMPUTE+"/hosts/{hostId}/portGroups/v2";
    public static final String GET_PORT_GROUPS_VSWITCHS = WIN_HONG_API_PREFIX + "/network/network/host/{hostId}/vswitchs";
    public static final String GET_PORT_GROUPS=WIN_HONG_API_PREFIX+"/network/vswitchs/{portGroupsId}/portGroups";

    public static final String GET_SUMMARY_HOST=WIN_HONG_API_PREFIX+"/compute/stat/summary/host/{domainId}";

    //获取镜像列表
    public static final String GET_IMAGES =WIN_HONG_API_PREFIX+"/compute/domain_templates";

    //获取镜像操作日志
    public static final String GET_IMAGE_OPERATION_LOGS =WIN_HONG_API_PREFIX+"/compute/domain_templates/logs/operate";

    //查询存储池关联的主机列表
    public static final String GET_STORAGE_POOLS_HOSTS =WIN_HONG_API_PREFIX+"/storage/storagePools/{storagePoolId}/rel/hosts";



}
