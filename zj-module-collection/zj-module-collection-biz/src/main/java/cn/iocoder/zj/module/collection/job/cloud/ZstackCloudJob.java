package cn.iocoder.zj.module.collection.job.cloud;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.util.collection.CollectionUtils;
import cn.iocoder.zj.module.collection.dal.dataobject.zstack.AlarmConfigInfo;
import cn.iocoder.zj.module.collection.dal.dataobject.zstack.AlarmHostRelationInfo;
import cn.iocoder.zj.module.collection.dal.dataobject.zstack.ZstackLoginInfo;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.zstack.AlarmInfoRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.zstack.ZstackAccessTokenRedisDAO;
import cn.iocoder.zj.module.collection.framework.influx.config.InfluxDBTemplate;
import cn.iocoder.zj.module.collection.service.cloud.IZstackCloudService;
import cn.iocoder.zj.module.collection.service.hardware.IZstackHardWareService;
import cn.iocoder.zj.module.collection.service.zstack.AbstractZstackApi;
import cn.iocoder.zj.module.collection.util.Sha256;
import cn.iocoder.zj.module.collection.util.StateConverter;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import cn.iocoder.zj.module.monitor.api.eip.EipApi;
import cn.iocoder.zj.module.monitor.api.eip.dto.EipCreateReqDto;
import cn.iocoder.zj.module.monitor.api.hostinfo.HostInfoApi;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.hostnic.HostNicApi;
import cn.iocoder.zj.module.monitor.api.hostnic.dto.HostNicCreateReqDto;
import cn.iocoder.zj.module.monitor.api.hostnic.dto.HostNicRespDTO;
import cn.iocoder.zj.module.monitor.api.hostsecgroup.HostSecgroupApi;
import cn.iocoder.zj.module.monitor.api.hostsecgroup.dto.HostSecgroupCreateReqDto;
import cn.iocoder.zj.module.monitor.api.secgroup.SecgroupApi;
import cn.iocoder.zj.module.monitor.api.secgroup.dto.SecgroupCreateReqDto;
import cn.iocoder.zj.module.monitor.api.secgrouprule.SecgroupruleApi;
import cn.iocoder.zj.module.monitor.api.secgrouprule.dto.SecgroupRuleCreateReqDto;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.frameworkset.elasticsearch.boot.BBossESStarter;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.zstack.sdk.*;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @ClassName : ZstackCloudJob  //类名
 * @Description : 硬件设施相关定时任务  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/29  16:08
 */


@Component
@Slf4j
public class ZstackCloudJob {


    @Resource
    InfluxDBTemplate influxDBTemplate;

    @Autowired
    private BBossESStarter bbossESStarter;

    @Resource
    IZstackCloudService zstackCloudService;
    @Resource
    HostInfoApi hostInfoApi;
    @Resource
    HostNicApi hostNicApi;
    @Resource
    EipApi eipApi;
    @Resource
    ZstackAccessTokenRedisDAO zstackAccessTokenRedisDAO;
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    PlatformRedisDAO platformRedisDAO;
    @Autowired
    private AlarmConfigApi alarmConfigApi;

    @Resource(name = "zstackCloudServiceImpl")
    AbstractZstackApi zstackApi;

    @Resource
    IZstackHardWareService iZstackHardWareService;

    @Resource
    AlarmInfoRedisDAO alarmInfoRedisDAO;
    @Resource
    SecgroupApi secgroupApi;
    @Resource
    SecgroupruleApi secgroupruleApi;
    @Resource
    HostSecgroupApi hostSecgroupApi;

    public void zstackCloudJobList() {
        execute();
        cloudByVmInfos();
    }

    private List<PlatformconfigDTO> fetchAndCachePlatformConfig(PlatformconfigApi platformconfigApi, PlatformRedisDAO platformRedisDAO) {
        List<PlatformconfigDTO> config = platformRedisDAO.get("platform");
        if (config == null) {
            config = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform", config);
        }
        return config;
    }

    // 通用方法用于获取硬件指标并构建批处理数据点
    private void fetchMetricAndBuildPoints(String url, String token, PlatformconfigDTO platform, String uuid, String metricName, String measurementName, BatchPoints batchPoints, String name, String[] type) {
        String responseData = zstackApi.fetchMetricData(url, token, uuid, metricName);
        JSONArray data = JSONObject.parseObject(responseData).getJSONArray("data");
        List<Map<String, Object>> metricMaps = parseJSONArray(data, "value", "value");
        // 检查当前指标是否在 `type` 数组中
        boolean types = false;
        if (Arrays.asList(type).contains(metricName)) {
            types = true;
        }
        for (Map<String, Object> map : metricMaps) {
            String hostType = metricName;
            if (types) {
                hostType = Convert.toStr(map.get("type"));
            }
            if (hostType == null) {
                hostType = metricName;
            }
            Point point = Point.measurement(measurementName)
                    .tag("uuid", uuid)
                    .tag("metricName", metricName)
                    .addField("productsName", name) // 假定硬件名称和平台名称一致
                    .tag("regionId", platform.getRegionId().toString())
                    .addField("regionName", platform.getRegionName())
                    .tag("vm_type", hostType)
                    .tag("platformId", platform.getId().toString())
                    .addField("platformName", platform.getName())
                    .addField("vm_metricName", metricName)
                    .addField("type", hostType)
                    .addField("value", Convert.toBigDecimal(map.get("value")))
                    .time(Convert.toLong(map.get("time")) + 20, TimeUnit.SECONDS)
                    .build();
            batchPoints.point(point);
        }
    }

    @XxlJob("zstackCloud")
    public void execute() {

        // 获取配置租户平台信息
//        CommonResult<List<PlatformconfigDTO>> listCommonResult = platformconfigApi.getPlatList();
        List<HostInfoRespCreateReqDTO> hostInfoRespCreateReqDTOList = new ArrayList<>();
        //1.选择一个库
        BatchPoints batchPoints = BatchPoints.builder().build();//创建批量数据存储batch
        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                .filter(dto -> "zstack".equals(dto.getTypeCode()))
                .collect(Collectors.toList());
        if (filteredList.isEmpty()) return;

        List<AlarmHostRelationInfo> alarmHostRealtionList = JSON.parseArray(alarmInfoRedisDAO.getRelation("alarm_host_relation"), AlarmHostRelationInfo.class);
        List<AlarmConfigInfo> alarmConfigInfoList = JSON.parseArray(alarmInfoRedisDAO.getConfig("alarm_config"), AlarmConfigInfo.class);
        Map<Long, AlarmConfigInfo> alarmConfigMap = new HashMap<>();
        Map<String, AlarmHostRelationInfo> alarmConfigRelationMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(alarmConfigInfoList)) {
            alarmConfigInfoList.removeIf(item -> item.getDeleted() == 1);
            alarmConfigMap = CollectionUtils.convertMap(alarmConfigInfoList, AlarmConfigInfo::getId);
        }
        if (ObjectUtil.isNotEmpty(alarmHostRealtionList)) {
            alarmHostRealtionList.removeIf(item -> item.getStatus() == 1);
            alarmConfigRelationMap = CollectionUtils.convertMap(alarmHostRealtionList, AlarmHostRelationInfo::getHostUuid);
        }

        if (filteredList.size() > 0) {
            for (PlatformconfigDTO p : filteredList) {
                try {
                    List<HostNicCreateReqDto> hostNicCreateReqDtoList = new ArrayList<>();
                    List<EipCreateReqDto> eipCreateReqDtoList = new ArrayList<>();
                    ZstackLoginInfo zstackLoginInfo = zstackAccessTokenRedisDAO.get("zstack:" + p.getId());
                    if (zstackLoginInfo != null) {
                        String token = zstackLoginInfo.getUuid();

                        String processedUrl = removeProtocolAndPort(p.getUrl());
                        String port = extractPort(p.getUrl());
                        ZSClient.configure(
                                new ZSConfig.Builder()
                                        .setHostname(processedUrl)
                                        .setPort(Convert.toInt(port))
                                        .setContextPath("zstack")
                                        .build()
                        );
                        // 拿token去换接口状态 监测接口是否正
                        // 1: 处理内存
                        List<Map<String, Object>> memory = new ArrayList<>();
                        //  处理内存使用率 ，内存已用百分比
                        String memoryUsed = zstackApi.memoryUsedInPercent(p.getUrl(), token, "");
                        JSONArray memorys = JSONObject.parseObject(memoryUsed).getJSONArray("data");
                        List<Map<String, Object>> maps = parseJSONArray(memorys, "memoryUsed", "value");
                        maps.stream().collect(Collectors.groupingBy(map -> map.get("VMUuid"))).forEach((k, v) -> {
                            Map<String, Object> map = new HashMap();
                            map.put("uuid", k);
                            map.put("memoryUsed", v.get(v.size() - 1).get("memoryUsed"));
                            memory.add(map);
                        });
                        //查询三层网络列表
                        QueryL3NetworkAction action = new QueryL3NetworkAction();
                        action.sessionId = token;
                        QueryL3NetworkAction.Result res  = action.call();
                        List<L3NetworkInventory> L3Inventories = res.value.inventories;
                        // 2：处理弹性ip
                        List<Map> vipIp = new ArrayList<>();
                        String vip = zstackCloudService.eips(p.getUrl(), token);
                        JSONArray inventories = JSONObject.parseObject(vip).getJSONArray("inventories");
                        for (int i = 0; i < inventories.size(); i++) {
                            Map map = new HashMap();
                            JSONObject jsonObject = inventories.getJSONObject(i);
                            if (jsonObject.getString("vmNicUuid") != null) {
                                map.put("uuid", jsonObject.getString("vmNicUuid"));
                                map.put("vipIp", StringUtil.toString(jsonObject.getString("vipIp")));
                                vipIp.add(map);
                            }
                        }
                        List<Map> vipList = new ArrayList<>();
                        if (vipIp.size() > 0) {
                            vipIp.stream().collect(Collectors.groupingBy(map -> map.get("uuid"))).forEach((k, v) -> {
                                Map map = new HashMap();
                                map.put("uuid", k);
                                List<String> vipIps = CollUtil.getFieldValues(v, "vipIp", String.class);
                                map.put("vipIp", CollUtil.join(vipIps, ","));
                                vipList.add(map);
                            });
                        }

                        // 3：处理集群名称
                        List<Map<String, Object>> clusters = new ArrayList<>();
                        List<Map<String, Object>> cluster = new ArrayList<>();
                        String clusters1 = zstackCloudService.clusters(p.getUrl(), token, "");
                        JSONArray clus = JSONObject.parseObject(clusters1).getJSONArray("inventories");
                        for (int i = 0; i < clus.size(); i++) {
                            JSONObject jsonObject = clus.getJSONObject(i);
                            if ("KVM".equals(jsonObject.getString("hypervisorType"))) {
                                Map<String, Object> map = new HashMap();
                                map.put("uuid", jsonObject.getString("uuid"));
                                map.put("clustersName", jsonObject.getString("name"));
                                clusters.add(map);
                            }
                        }

                        // 3：宿主机名称
                        List<Map<String, Object>> hardwares = new ArrayList<>();
                        String sHardware = iZstackHardWareService.hardWareInfo(p.getUrl(), token, "");
                        JSONArray hardware = JSONObject.parseObject(sHardware).getJSONArray("inventories");
                        for (int i = 0; i < hardware.size(); i++) {
                            JSONObject jsonObject = hardware.getJSONObject(i);
                            Map<String, Object> map = new HashMap();
                            map.put("uuid", jsonObject.getString("uuid"));
                            map.put("hardwareName", jsonObject.getString("name"));
                            hardwares.add(map);
                        }
                        List<Map<String, Object>> zones = new ArrayList<>();
                        QueryZoneAction queryZoneAction = new QueryZoneAction();
                        queryZoneAction.sessionId = token;
                        QueryZoneAction.Result zoneRes = queryZoneAction.call();
                        List<ZoneInventory> zonesData = zoneRes.value.inventories;
                        for (ZoneInventory zone : zonesData) {
                            Map<String, Object> map = new HashMap();
                            map.put("uuid", zone.getUuid());
                            map.put("zoneName", zone.getName());
                            zones.add(map);
                        }

                        //查询安全组
                        List<SecgroupCreateReqDto> secgroupCreateReqDtoList = new ArrayList<>();
                        List<SecgroupRuleCreateReqDto> secgroupRuleCreateReqDtoList = new ArrayList<>();
                        List<HostSecgroupCreateReqDto> hostSecgroupCreateReqDtoList = new ArrayList<>();
                        //安全组与三级网络对应关系

                        QuerySecurityGroupAction groupAction = new QuerySecurityGroupAction();
                        groupAction.sessionId=token;
                        QuerySecurityGroupAction.Result groupRes = groupAction.call();
                        List<SecurityGroupInventory> groupinventories = groupRes.value.inventories;
                        for (SecurityGroupInventory item : groupinventories) {
                            //安全组
                            SecgroupCreateReqDto createReqDto = new SecgroupCreateReqDto();
                            createReqDto.setUuid(item.getUuid());
                            createReqDto.setName(item.getName());
                            createReqDto.setStatus(item.getState());
                            createReqDto.setDescription(item.getDescription());
                            createReqDto.setPlatformId(p.getId());
                            createReqDto.setPlatformName(p.getName());
                            secgroupCreateReqDtoList.add(createReqDto);
                            //安全组规则
                            List<SecurityGroupRuleInventory> rules = item.getRules();
                            for (SecurityGroupRuleInventory rule : rules) {
                                SecgroupRuleCreateReqDto secgroupRuleCreateReqDto = new SecgroupRuleCreateReqDto();
                                secgroupRuleCreateReqDto.setUuid(rule.getUuid());
                                secgroupRuleCreateReqDto.setSecgroupUuid(rule.getSecurityGroupUuid());
                                secgroupRuleCreateReqDto.setStatus(rule.getState());
                                secgroupRuleCreateReqDto.setDescription(rule.getDescription());
                                secgroupRuleCreateReqDto.setCidr(rule.getAllowedCidr());
                                secgroupRuleCreateReqDto.setProtocol(rule.getProtocol());
                                Integer startPort = rule.getStartPort();
                                Integer endPort = rule.getEndPort();
                                if (startPort != null && endPort != null ) {
                                    if (startPort == -1 || endPort==-1){
                                        secgroupRuleCreateReqDto.setPorts("任意端口");
                                    }else {
                                        secgroupRuleCreateReqDto.setPorts(startPort + "-" + endPort);
                                    }
                                    secgroupRuleCreateReqDto.setPriority(rule.getPriority());
                                    secgroupRuleCreateReqDto.setDirection(Objects.equals(rule.getType(), "Ingress") ? "in" : "out");
                                }
                                secgroupRuleCreateReqDto.setPlatformId(p.getId());
                                secgroupRuleCreateReqDto.setPlatformName(p.getName());
                                secgroupRuleCreateReqDtoList.add(secgroupRuleCreateReqDto);
                            }
                        }

                        QueryImageAction queryImageAction = new QueryImageAction();
                        queryImageAction.sessionId = token;
                        QueryImageAction.Result imageResult = queryImageAction.call();
                        List<ImageInventory> imageInventories = imageResult.value.inventories;
                        Map<String, ImageInventory> imageMap = imageInventories.stream()
                                .collect(Collectors.toMap(ImageInventory::getUuid, image -> image));

                        // 查询所有相关的系统标签
                        QuerySystemTagAction queryTagAction = new QuerySystemTagAction();
                        queryTagAction.sessionId = token;
                        QuerySystemTagAction.Result tagResult = queryTagAction.call();
                        List<SystemTagInventory> allTags = tagResult.value.inventories;
                        Map<String, List<SystemTagInventory>> vmTagsMap = allTags.stream()
                                .collect(Collectors.groupingBy(SystemTagInventory::getResourceUuid));
                        // 根据不同租户查询不同用户下主机信息
                        String d = zstackCloudService.cloudInfo(p.getUrl(), token);
                        JSONArray jsonArray = JSONObject.parseObject(d).getJSONArray("inventories");
                        for (int j = 0; j < jsonArray.size(); j++) {
                            HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO = new HostInfoRespCreateReqDTO();
                            String ip = "";
                            String vip_ip = "";
                            String mac = "";
                            String architecture = "";
                            String guestOsType = "";
                            BigDecimal cpu_useds = new BigDecimal(0);
                            BigDecimal memory_useds = new BigDecimal(0);
                            BigDecimal disk_useds = new BigDecimal(0);
                            JSONObject jsonObject = jsonArray.getJSONObject(j);
                            String zoneName = "";
                            String clusterName = "";
                            String hardwareName = "";
                            BigDecimal actualSize = new BigDecimal(0);
                            BigDecimal cloudSize = new BigDecimal(0);

                            BigDecimal diskAuthenticBytes = new BigDecimal(0);
                            BigDecimal dataSize = new BigDecimal(0);

                            BigDecimal inBytes = new BigDecimal(0);
                            BigDecimal outBytes = new BigDecimal(0);
                            BigDecimal diskUsedBytes = new BigDecimal(0);
                            BigDecimal diskFreeBytes = new BigDecimal(0);
                            BigDecimal totalDiskCapacity = new BigDecimal(0);
                            BigDecimal inPackets = new BigDecimal(0);
                            BigDecimal outPackets = new BigDecimal(0);

                            if ("KVM".equals(jsonObject.getString("hypervisorType"))) {
                                String name = jsonObject.getString("name");
                                String imageUuid = jsonObject.getString("imageUuid");
                                String uuid = jsonObject.getString("uuid");
                                String clusterUuid = jsonObject.getString("clusterUuid");
                                String hardwareUuid = jsonObject.getString("lastHostUuid");
                                String zoneUuid = jsonObject.getString("zoneUuid");
                                String state = jsonObject.getString("state");
                                String u = jsonObject.getString("createDate");
                                Date createDate = new Date(u);
                                String type = jsonObject.getString("type");
                                Long memorySize = jsonObject.getLong("memorySize");
                                String cpuNum = jsonObject.getString("cpuNum");
                                String cpuSpeed = jsonObject.getString("cpuSpeed");
                                architecture = jsonObject.getString("architecture");
                                guestOsType = jsonObject.getString("guestOsType");
                                List<SystemTagInventory> vmTags = vmTagsMap.getOrDefault(uuid, Collections.emptyList());
                                String bootMode = "";
                                String haStatus = "";
                                for (SystemTagInventory tag : vmTags) {
                                    String tagString = tag.getTag();
                                    if (tagString.contains("bootMode::")) {
                                        bootMode = tagString.split("::")[1];
                                    }
                                    if (tagString.contains("ha::")) {
                                        haStatus = tagString.split("::")[1];
                                    }
                                }
                                // 弹性ip
                                JSONArray vmNics = jsonObject.getJSONArray("vmNics");
                                //提取出所关联的三级网络id列表
                                List<String> l3NetworkUuids = new ArrayList<>();
                                if (vmNics.size() > 0) {
                                    List<String> ipList = CollUtil.getFieldValues(vmNics, "ip", String.class);
                                    List<String> macList = CollUtil.getFieldValues(vmNics, "mac", String.class);
                                    // 拼接IP地址
                                    ip = CollUtil.join(ipList, ",");
                                    mac = CollUtil.join(macList, ",");
                                    if (ip.equals("null")) {
                                        ip = "";
                                    }
                                    for (int i = 0; i < vmNics.size(); i++) {
                                        JSONObject vm = vmNics.getJSONObject(i);
                                        HostNicCreateReqDto hostNicCreateReqDto = new HostNicCreateReqDto();
                                        hostNicCreateReqDto.setHostUuid(vm.getString("vmInstanceUuid"));
                                        hostNicCreateReqDto.setName(vm.getString("internalName"));
                                        hostNicCreateReqDto.setUuid(vm.getString("uuid"));
                                        hostNicCreateReqDto.setIp(vm.getString("ip"));
                                        hostNicCreateReqDto.setIp6("");
                                        hostNicCreateReqDto.setPlatformId( p.getId());
                                        hostNicCreateReqDto.setPlatformName(p.getName());
                                        hostNicCreateReqDto.setMac(vm.getString("mac"));
                                        hostNicCreateReqDto.setDriver("virtio");
                                        hostNicCreateReqDto.setInClassicNetwork((byte) 0);
                                        String l3NetworkUuid = vm.getString("l3NetworkUuid");
                                        hostNicCreateReqDto.setNetworkUuid(l3NetworkUuid);
                                        l3NetworkUuids.add(l3NetworkUuid);
                                        //获取三层网络名称
                                        for (L3NetworkInventory l3Inventory : L3Inventories) {
                                            if (l3Inventory.getUuid().equals(l3NetworkUuid)){
                                                hostNicCreateReqDto.setNetworkName(l3Inventory.getName());
                                                break;
                                            }
                                        }
                                        hostNicCreateReqDtoList.add(hostNicCreateReqDto);
                                        for (Object inventory : inventories) {
                                            JSONObject eipObj = (JSONObject) inventory;
                                            if(vm.getString("uuid").equals(eipObj.getString("vmNicUuid"))) {
                                                EipCreateReqDto eipCreateReqDto = new EipCreateReqDto();
                                                eipCreateReqDto.setIpAddr(eipObj.getString("vipIp"));
                                                eipCreateReqDto.setAssociateId(vm.getString("vmInstanceUuid"));
                                                eipCreateReqDto.setAssociateType("server");
                                                eipCreateReqDto.setName(eipObj.getString("name"));
                                                eipCreateReqDto.setUuid(eipObj.getString("uuid"));
                                                eipCreateReqDto.setStatus("ready");
                                                eipCreateReqDto.setMode("elastic_ip");
                                                eipCreateReqDto.setBandwidth(0);
                                                eipCreateReqDto.setChargeType("traffic");
                                                eipCreateReqDto.setCloudregionId("");
                                                eipCreateReqDto.setNetworkId(eipObj.getString("vmNicUuid"));
                                                eipCreateReqDto.setPlatformId(p.getId());
                                                eipCreateReqDto.setPlatformName(p.getName());
                                                eipCreateReqDto.setVCreateTime(new Date(eipObj.getString("createDate")));
                                                eipCreateReqDtoList.add(eipCreateReqDto);
                                            }

                                        }
                                        for (Map map : vipList) {
                                            // 判断uuid 相同取出弹性ip （vmNics集合中的uuid 与弹性ip中的 vmNicUuid对应，我这里重写了uuid 名称，之前名称是vmNicUuid）
                                            if (StringUtil.toString(map.get("uuid")).equals(vm.getString("uuid"))) {
                                                vip_ip = StringUtil.toString(map.get("vipIp"));
                                            }
                                        }
                                    }
                                }
                                for (SecurityGroupInventory groupinventory : groupinventories) {
                                    Set attachedL3NetworkUuids = groupinventory.getAttachedL3NetworkUuids();
                                    for (String l3NetworkUuid : l3NetworkUuids) {
                                        if (attachedL3NetworkUuids.contains(l3NetworkUuid)){
                                            // 关联虚拟机
                                            HostSecgroupCreateReqDto hostSecgroupCreateReqDto = new HostSecgroupCreateReqDto();
                                            hostSecgroupCreateReqDto.setPlatformId(p.getId());
                                            hostSecgroupCreateReqDto.setPlatformName(p.getName());
                                            hostSecgroupCreateReqDto.setHostUuid(uuid);
                                            hostSecgroupCreateReqDto.setSecgroupUuid(groupinventory.getUuid());
                                            hostSecgroupCreateReqDtoList.add(hostSecgroupCreateReqDto);
                                            break;
                                        }
                                    }
                                }

                                // =====================cpu相关================
                                String avercpus = zstackApi.cpuAverageUsedUtilization(p.getUrl(), token, uuid);
                                JSONArray cau = JSONObject.parseObject(avercpus).getJSONArray("data");
                                if (cau.size() > 0) {
                                    int lastIndex = cau.size() - 1;
                                    JSONObject cauObj = cau.getJSONObject(lastIndex);
                                    cpu_useds = cauObj.getBigDecimal("value");
                                }

                                // 采集云盘内存
                                JSONArray jsonArrays = jsonArray.getJSONObject(j).getJSONArray("allVolumes");
                                if (jsonArrays.size() > 0) {
                                    for (int i = 0; i < jsonArrays.size(); i++) {
                                        JSONObject jsonObject1 = jsonArrays.getJSONObject(i);
                                        // 目前值采取了Root 的盘内存
                                        if ("Root".equals(jsonObject1.getString("type"))) {
                                            actualSize = jsonObject1.getBigDecimal("actualSize");
                                            cloudSize = jsonObject1.getBigDecimal("size");
                                        } else {
                                            diskAuthenticBytes = jsonObject1.getBigDecimal("actualSize");
                                            dataSize = jsonObject1.getBigDecimal("size");
                                            // 真实容量
                                            diskFreeBytes = diskFreeBytes.add(diskAuthenticBytes);
                                            totalDiskCapacity = dataSize.add(totalDiskCapacity);
                                        }
                                    }
                                }
                                // 系统盘真实+数据盘真实  /  系统+数据盘总量 = diskUsed
                                // 使用率 // 可用量  // 总量
//                            diskUsed  totalDiskCapacity
                                disk_useds = actualSize.add(diskFreeBytes)
                                        .divide(cloudSize.add(totalDiskCapacity), 2, RoundingMode.UP);
                                // 网卡入速度
                                String networkInBytes = zstackApi.networkInBytes(p.getUrl(), token, uuid);

                                JSONArray networkInBytesArray = JSONObject.parseObject(networkInBytes).getJSONArray("data");
                                if (networkInBytesArray.size() > 0) {
                                    int lastIndex = networkInBytesArray.size() - 1;
                                    JSONObject networkInbytesObj = networkInBytesArray.getJSONObject(lastIndex);
                                    inBytes = networkInbytesObj.getBigDecimal("value");
                                }

                                // 网卡出速度
                                String networkOutBytes = zstackApi.networkOutBytes(p.getUrl(), token, uuid);
                                JSONArray networkOutBytesArray = JSONObject.parseObject(networkOutBytes).getJSONArray("data");
                                if (networkOutBytesArray.size() > 0) {
                                    int outIndex = networkOutBytesArray.size() - 1;
                                    JSONObject networkOutBytesObj = networkOutBytesArray.getJSONObject(outIndex);
                                    outBytes = networkOutBytesObj.getBigDecimal("value");
                                }

                                // 磁盘已用容量百分比
//                            String diskAllUsedCapacityInPercent = zstackApi.diskAllUsedCapacityInPercent(p.getUrl(), token, uuid);
//                            JSONArray diskAllUsedCapacityInPercentArray = JSONObject.parseObject(diskAllUsedCapacityInPercent).getJSONArray("data");
//                            if (diskAllUsedCapacityInPercentArray.size() > 0) {
//                                int diskAllUsedCapacityIn = diskAllUsedCapacityInPercentArray.size() - 1;
//                                JSONObject diskUsedCapacityInIndexObj = diskAllUsedCapacityInPercentArray.getJSONObject(diskAllUsedCapacityIn);
//                                disk_useds = diskUsedCapacityInIndexObj.getBigDecimal("value");
//                            }

                                // 磁盘已用容量
//                            String diskUsedCapacityInBytes = zstackApi.diskAllUsedCapacityInBytes(p.getUrl(), token, uuid);
//                            JSONArray diskUsedCapacityInBytesArray = JSONObject.parseObject(diskUsedCapacityInBytes).getJSONArray("data");
//                            if (diskUsedCapacityInBytesArray.size() > 0) {
//                                int diskUsedCapacityInIndex = diskUsedCapacityInBytesArray.size() - 1;
//                                JSONObject diskUsedCapacityInIndexObj = diskUsedCapacityInBytesArray.getJSONObject(diskUsedCapacityInIndex);
//                                diskUsedBytes = diskUsedCapacityInIndexObj.getBigDecimal("value");
//                            }

                                // 硬盘剩余容量
//                            String diskFreeCapacityInBytes = zstackApi.diskAllFreeCapacityInBytes(p.getUrl(), token, uuid);
//                            JSONArray diskFreeCapacityInBytesArray = JSONObject.parseObject(diskFreeCapacityInBytes).getJSONArray("data");
//                            if (diskFreeCapacityInBytesArray.size() > 0) {
//                                int diskFreeCapacityInBytesIndex = diskFreeCapacityInBytesArray.size() - 1;
//                                JSONObject diskFreeCapacityInBytesIndexObj = diskFreeCapacityInBytesArray.getJSONObject(diskFreeCapacityInBytesIndex);
//                                diskFreeBytes = diskFreeCapacityInBytesIndexObj.getBigDecimal("value");
//                            }

                                //=====================================网络相关============================

                                // 全部网卡入包数
                                String netInpackets = zstackApi.networkAllInPackets(p.getUrl(), token, uuid);
                                JSONArray netInpacketsArray = JSONObject.parseObject(netInpackets).getJSONArray("data");
                                if (netInpacketsArray.size() > 0) {
                                    int netInpacketsArrayIndex = netInpacketsArray.size() - 1;
                                    JSONObject netInpacketsArrayObj = netInpacketsArray.getJSONObject(netInpacketsArrayIndex);
                                    inPackets = netInpacketsArrayObj.getBigDecimal("value");
                                }


                                // 全部网卡出包数
                                String netOutpackets = zstackApi.networkAllOutPackets(p.getUrl(), token, uuid);
                                JSONArray netOutpacketsArray = JSONObject.parseObject(netOutpackets).getJSONArray("data");
                                if (netOutpacketsArray.size() > 0) {
                                    int netOutpacketsArrayIndex = netOutpacketsArray.size() - 1;
                                    JSONObject netOutpacketsObj = netOutpacketsArray.getJSONObject(netOutpacketsArrayIndex);
                                    outPackets = netOutpacketsObj.getBigDecimal("value");
                                }


                                // 内存
                                for (Map map : memory) {
                                    if (StringUtil.toString(map.get("uuid")).equals(uuid)) {
                                        memory_useds = (BigDecimal) map.get("memoryUsed");
                                    }
                                }
                                // 区域名称
                                for (Map map : zones) {
                                    if (StringUtil.toString(map.get("uuid")).equals(zoneUuid)) {
                                        zoneName = StringUtil.toString(map.get("zoneName"));
                                    }
                                }
                                // 集群名称
                                for (Map map : clusters) {
                                    if (StringUtil.toString(map.get("uuid")).equals(clusterUuid)) {
                                        clusterName = StringUtil.toString(map.get("clustersName"));
                                    }
                                }
                                //宿主机名称
                                for (Map map : hardwares) {
                                    if (StringUtil.toString(map.get("uuid")).equals(hardwareUuid)) {
                                        hardwareName = StringUtil.toString(map.get("hardwareName"));
                                    }
                                }
                                //电源状态
                                String powerState = switch (state) {
                                    case "Created" -> "on";
                                    case "Starting" -> "on";
                                    case "Running" -> "on";
                                    case "Stopping" -> "off";
                                    case "Stopped" -> "off";
                                    case "Unknown" -> "unknown";
                                    case "Rebooting" -> "on";
                                    case "Destroyed" -> "off";
                                    case "Destroying" -> "off";
                                    case "Migrating" -> "on";
                                    case "Expunging" -> "off";
                                    case "Paunging" -> "off";
                                    case "Paused" -> "off";
                                    case "Resuming" -> "on";
                                    case "VolumeMigrating" -> "on";
                                    default -> "unknown";
                                };
                                hostInfoRespCreateReqDTO.setPowerState(powerState);
                                hostInfoRespCreateReqDTO.setAutoInitType(StringUtil.isNotEmpty(haStatus)?haStatus:"None");
                                hostInfoRespCreateReqDTO.setGuideMode(bootMode);
                                hostInfoRespCreateReqDTO.setDeleted(0);
                                hostInfoRespCreateReqDTO.setUuid(uuid);
                                hostInfoRespCreateReqDTO.setName(name);
                                hostInfoRespCreateReqDTO.setState(state);
                                hostInfoRespCreateReqDTO.setIp(ip);
                                hostInfoRespCreateReqDTO.setVipIp(vip_ip);
                                hostInfoRespCreateReqDTO.setZoneUuid(zoneUuid);
                                hostInfoRespCreateReqDTO.setClusterUuid(clusterUuid);
                                hostInfoRespCreateReqDTO.setImageUuid(imageUuid);
                                if(BeanUtil.isNotEmpty(imageMap.get(imageUuid))){
                                    hostInfoRespCreateReqDTO.setImageName(imageMap.get(imageUuid).getName());
                                    hostInfoRespCreateReqDTO.setIso(imageMap.get(imageUuid).getName());
                                }
                                hostInfoRespCreateReqDTO.setHardwareUuid(hardwareUuid);
                                hostInfoRespCreateReqDTO.setArchitecture(architecture);
                                hostInfoRespCreateReqDTO.setGuestOsType(guestOsType);
                                hostInfoRespCreateReqDTO.setVCreateDate(DateUtil.date(createDate));
                                hostInfoRespCreateReqDTO.setCpuUsed(cpu_useds);
                                hostInfoRespCreateReqDTO.setMemoryUsed(memory_useds);
                                hostInfoRespCreateReqDTO.setZoneName(zoneName);
                                hostInfoRespCreateReqDTO.setClusterName(clusterName);
                                hostInfoRespCreateReqDTO.setHardwareName(hardwareName);
                                hostInfoRespCreateReqDTO.setType(type);
                                hostInfoRespCreateReqDTO.setMemorySize(StringUtil.toLong(memorySize));
                                hostInfoRespCreateReqDTO.setCpuNum(StringUtil.toInt(cpuNum));
                                String trimmedMac = StringUtil.toString(mac);
                                if (trimmedMac == null || trimmedMac.equals("")) {
                                    hostInfoRespCreateReqDTO.setMac("-");
                                } else {
                                    if (trimmedMac.contains(",")) {
                                        // 如果包含逗号，取第一个MAC地址
                                        hostInfoRespCreateReqDTO.setMac(trimmedMac.split(",")[0].trim());
                                    } else {
                                        // 如果不包含逗号，直接返回
                                        hostInfoRespCreateReqDTO.setMac(trimmedMac);
                                    }
                                }
                                hostInfoRespCreateReqDTO.setRegionId(p.getRegionId());
                                hostInfoRespCreateReqDTO.setPlatformId(p.getId());
                                hostInfoRespCreateReqDTO.setPlatformName(p.getName());
                                // 系统盘
                                hostInfoRespCreateReqDTO.setActualSize(diskFreeBytes);
                                // 系统总
                                hostInfoRespCreateReqDTO.setCloudSize(totalDiskCapacity);
                                hostInfoRespCreateReqDTO.setNetworkInBytes(inBytes);
                                hostInfoRespCreateReqDTO.setNetworkOutBytes(outBytes);
                                hostInfoRespCreateReqDTO.setDiskUsedBytes(diskUsedBytes);
                                hostInfoRespCreateReqDTO.setNetworkInBytes(inPackets);
                                hostInfoRespCreateReqDTO.setNetworkOutPackets(outPackets);
                                hostInfoRespCreateReqDTO.setDiskFreeBytes(actualSize);
                                hostInfoRespCreateReqDTO.setTotalDiskCapacity(cloudSize);
                                hostInfoRespCreateReqDTO.setTypeName("zstack");
                                hostInfoRespCreateReqDTOList.add(hostInfoRespCreateReqDTO);
                            }
                        }

                        if (!hostNicCreateReqDtoList.isEmpty()) {
                            List<HostNicCreateReqDto> nowNic = hostNicApi.getHostNicsByPlatformId(p.getId()).getData();
                            //根据uuid筛选出hostNicCreateReqDtoList里需要新增的数据
                            Set<String> nowNicUuids = nowNic.stream()
                                    .map(HostNicCreateReqDto::getUuid)
                                    .collect(Collectors.toSet());

                            List<HostNicCreateReqDto> addHostNicList = hostNicCreateReqDtoList.stream()
                                    .filter(hostNicCreateReqDto -> !nowNicUuids.contains(hostNicCreateReqDto.getUuid()))
                                    .toList();
                            if (!addHostNicList.isEmpty()) {
                                hostNicApi.addHostNics(addHostNicList);
                            }
                            //查询updateList 并将nowNicUuids 的id 赋值给updateHostNicList
                            List<HostNicCreateReqDto> updateHostNicList = hostNicCreateReqDtoList.stream()
                                    .filter(hostNicCreateReqDto -> nowNicUuids.contains(hostNicCreateReqDto.getUuid()))
                                    .toList().stream().map(hostNicCreateReqDto -> {
                                        return hostNicCreateReqDto.setId(nowNic.stream()
                                                .filter(hostNicRespDTO -> hostNicRespDTO.getUuid().equals(hostNicCreateReqDto.getUuid()))
                                                .findFirst()
                                                .map(HostNicCreateReqDto::getId)
                                                .orElse(null));
                                    }).toList();
                            if (!updateHostNicList.isEmpty()) {
                                hostNicApi.updateHostNics(updateHostNicList);
                            }
                            //根据uuid 判断在nowNic不在hostNicCreateReqDtoList里的数据
                            Set<String> hostNicUuids = hostNicCreateReqDtoList.stream()
                                    .map(HostNicCreateReqDto::getUuid)
                                    .collect(Collectors.toSet());
                            List<HostNicCreateReqDto> delHostNicList = nowNic.stream()
                                    .filter(hostNicRespDTO -> !hostNicUuids.contains(hostNicRespDTO.getUuid()))
                                    .toList();
                            if (!delHostNicList.isEmpty()) {
                                hostNicApi.delHostNics(delHostNicList);
                            }
                        }

                        if (!eipCreateReqDtoList.isEmpty()) {
                            List<EipCreateReqDto> nowEip = eipApi.getEipsByPlatformId(p.getId()).getData();
                            //根据uuid筛选出eipCreateReqDtoList里需要新增的数据
                            Set<String> nowEipUuids = nowEip.stream()
                                    .map(EipCreateReqDto::getUuid)
                                    .collect(Collectors.toSet());

                            List<EipCreateReqDto> addEipList = eipCreateReqDtoList.stream()
                                    .filter(eipCreateReqDto -> !nowEipUuids.contains(eipCreateReqDto.getUuid()))
                                    .toList();
                            if (!addEipList.isEmpty()) {
                                eipApi.addEips(addEipList);
                            }
                            //查询updateList 并将nowEipUuids 的id 赋值给updateEipList
                            List<EipCreateReqDto> updateEipList = eipCreateReqDtoList.stream()
                                    .filter(hostNicCreateReqDto -> nowEipUuids.contains(hostNicCreateReqDto.getUuid()))
                                    .toList().stream().map(hostEipReqDto -> {
                                        return hostEipReqDto.setId(nowEip.stream()
                                                .filter(eipRespDto -> eipRespDto.getUuid().equals(hostEipReqDto.getUuid()))
                                                .findFirst()
                                                .map(EipCreateReqDto::getId)
                                                .orElse(null));
                                    }).toList();
                            if (!updateEipList.isEmpty()) {
                                eipApi.updateEips(updateEipList);
                            }
                            //根据uuid 判断在nowEip不在eipCreateReqDtoList里的数据
                            Set<String> hostEipUuids = eipCreateReqDtoList.stream()
                                    .map(EipCreateReqDto::getUuid)
                                    .collect(Collectors.toSet());
                            List<EipCreateReqDto> delEipList = nowEip.stream()
                                    .filter(hostNicRespDTO -> !hostEipUuids.contains(hostNicRespDTO.getUuid()))
                                    .toList();
                            if (!delEipList.isEmpty()) {
                                eipApi.delEips(delEipList);
                            }
                        }
                        updateSecgroup(secgroupCreateReqDtoList, p.getId());
                        updateSecgroupRule(secgroupRuleCreateReqDtoList, p.getId());
                        updateHostSecgroup(hostSecgroupCreateReqDtoList, p.getId());
                    }
                } catch (Exception e) {
                    // 记录错误日志，包含平台信息
                    log.error("处理平台 [{}] 失败: {}",
                            p.getName(), e.getMessage(), e);
                    // 继续处理下一个平台
                    continue;
                }

            }
        }

        if (!hostInfoRespCreateReqDTOList.isEmpty()) {
            List<AlarmDorisReqDTO> alarmDorisReqDTO = new ArrayList<>();
            List<HostInfoRespCreateReqDTO> dtoList = hostInfoApi.getAll("zstack").getData();
            int hostCount = hostInfoApi.count("zstack");
            if (hostCount == 0) {
                hostInfoApi.adds(hostInfoRespCreateReqDTOList);
            } else {
                List<HostInfoRespCreateReqDTO> collect3 = dtoList
                        .stream()
                        .filter(item -> !hostInfoRespCreateReqDTOList.stream()
                                .map(HostInfoRespCreateReqDTO::getUuid)
                                .collect(Collectors.toList())
                                .contains(item.getUuid()))
                        .collect(Collectors.toList());
                final Long alertId = alarmConfigApi.getMaxAlertId().getData();
                Map<String, AlarmHostRelationInfo> finalAlarmConfigRelationMap = alarmConfigRelationMap;
                Map<Long, AlarmConfigInfo> finalAlarmConfigMap = alarmConfigMap;
                hostInfoRespCreateReqDTOList.forEach(item -> {
                    for (HostInfoRespCreateReqDTO dto : dtoList) {
                        AlarmHostRelationInfo relationInfo = finalAlarmConfigRelationMap.get(item.getUuid());
                        if (ObjectUtil.isNotEmpty(relationInfo)) {
                            AlarmConfigInfo alarmConfigInfo = finalAlarmConfigMap.get(relationInfo.getAlarmId());
                            int i = 1;
                            //启用状态变更后发送告警
                            if (ObjectUtil.isNotEmpty(alarmConfigInfo) && alarmConfigInfo.getDictLabelValue().equals("state_change") && dto.getUuid().equals(item.getUuid()) && !dto.getState().equals(item.getState())) {
                                AlarmDorisReqDTO alarm = new AlarmDorisReqDTO();
                                int alarmLevel = alarmConfigInfo.getAlarmLevel() == 1 ? 2 : (alarmConfigInfo.getAlarmLevel() == 2 ? 1 : 0);
                                JSONObject tags = new JSONObject();
                                alarm.setId(alertId);
                                tags.put("app", "host");
                                tags.put("monitorId", item.getUuid());
                                tags.put("monitorName", item.getName());
                                alarm.setPlatformId(item.getPlatformId());
                                alarm.setResourceType(0);
                                alarm.setStatus(0);
                                alarm.setIsSolved(0);
                                alarm.setGmtCreate(new Date());
                                alarm.setFirstAlarmTime(DateUtil.current());
                                alarm.setGmtUpdate(new Date());
                                alarm.setLastAlarmTime(DateUtil.current());
                                alarm.setPlatformName(item.getPlatformName());
                                alarm.setTimes(1);
                                alarm.setMonitorId(item.getUuid());
                                alarm.setContent("云主机:" + item.getName() + " 的启用状态由\"" + StateConverter.stateToCh(dto.getState()) + "\"转换为\"" + StateConverter.stateToCh(item.getState()) + "\"");
                                alarm.setTarget("host.state.changed");
                                alarm.setApp("host");
                                alarm.setMonitorName(item.getName());
                                alarm.setPriority(alarmLevel);
                                alarm.setAlarmId(0L);
                                alarmDorisReqDTO.add(alarm);
                            }
                            i++;
                        }
                    }
                });
                if (alarmDorisReqDTO.size() > 0) {
                    Map<String, List> addMap = new HashMap<>();
                    addMap.put("updateList", new ArrayList<>());
                    addMap.put("insertList", alarmDorisReqDTO);
                    alarmConfigApi.createAlarmToDoris(addMap);
                }
                // 软删除云主机历史数据
                if (collect3.size() > 0) {
                    collect3.forEach(item -> item.setDeleted(1).setState("Destroyed"));
                    // 增加删除以后触发告警
                    hostInfoApi.updates(collect3);
                }
                hostInfoApi.updates(hostInfoRespCreateReqDTOList);
                List<HostInfoRespCreateReqDTO> collect = hostInfoRespCreateReqDTOList.stream().filter(item -> !dtoList.stream().map(HostInfoRespCreateReqDTO::getUuid).collect(Collectors.toList()).contains(item.getUuid())).collect(Collectors.toList());
                if (collect.size() > 0) {
                    collect = collect.stream()
                            .distinct()
                            .collect(Collectors.toList());
                    hostInfoApi.adds(collect);
                }
            }
            hostInfoApi.removeDuplicateData();
        }

    }

    private void updateSecgroup(List<SecgroupCreateReqDto> secgroupCreateReqDtoList, Long id) {
        if (!secgroupCreateReqDtoList.isEmpty()) {
            List<SecgroupCreateReqDto> now = secgroupApi.getSecgroupsByPlatformId(id).getData();
            //根据uuid筛选出eipCreateReqDtoList里需要新增的数据
            Set<String> nowUuids = now.stream()
                    .map(SecgroupCreateReqDto::getUuid)
                    .collect(Collectors.toSet());

            List<SecgroupCreateReqDto> addList = secgroupCreateReqDtoList.stream()
                    .filter(eipCreateReqDto -> !nowUuids.contains(eipCreateReqDto.getUuid()))
                    .toList();
            if (!addList.isEmpty()) {
                secgroupApi.addSecgroups(addList);
            }
            //查询updateList 并将nowEipUuids 的id 赋值给updateEipList
            List<SecgroupCreateReqDto> updateList = secgroupCreateReqDtoList.stream()
                    .filter(hostNicCreateReqDto -> nowUuids.contains(hostNicCreateReqDto.getUuid()))
                    .toList().stream().map(reqDto -> {
                        return reqDto.setId(now.stream()
                                .filter(respDto -> respDto.getUuid().equals(reqDto.getUuid()))
                                .findFirst()
                                .map(SecgroupCreateReqDto::getId)
                                .orElse(null));
                    }).toList();
            if (!updateList.isEmpty()) {
                secgroupApi.updateSecgroups(updateList);
            }
            //根据uuid 判断在nowEip不在eipCreateReqDtoList里的数据
            Set<String> uuids = secgroupCreateReqDtoList.stream()
                    .map(SecgroupCreateReqDto::getUuid)
                    .collect(Collectors.toSet());
            List<SecgroupCreateReqDto> delList = now.stream()
                    .filter(respDTO -> !uuids.contains(respDTO.getUuid()))
                    .toList();
            if (!delList.isEmpty()) {
                secgroupApi.delSecgroups(delList);
            }
        }
    }

    private void updateSecgroupRule(List<SecgroupRuleCreateReqDto> secgroupCreateReqDtoList, Long id) {
        if (!secgroupCreateReqDtoList.isEmpty()) {
            List<SecgroupRuleCreateReqDto> now = secgroupruleApi.getSecgroupRulesByPlatformId(id).getData();
            //根据uuid筛选出eipCreateReqDtoList里需要新增的数据
            Set<String> nowUuids = now.stream()
                    .map(SecgroupRuleCreateReqDto::getUuid)
                    .collect(Collectors.toSet());

            List<SecgroupRuleCreateReqDto> addList = secgroupCreateReqDtoList.stream()
                    .filter(eipCreateReqDto -> !nowUuids.contains(eipCreateReqDto.getUuid()))
                    .toList();
            if (!addList.isEmpty()) {
                secgroupruleApi.addSecgroupRules(addList);
            }
            //查询updateList 并将nowEipUuids 的id 赋值给updateEipList
            List<SecgroupRuleCreateReqDto> updateList = secgroupCreateReqDtoList.stream()
                    .filter(reqDto -> nowUuids.contains(reqDto.getUuid()))
                    .toList().stream().map(reqDto -> {
                        return reqDto.setId(now.stream()
                                .filter(respDto -> respDto.getUuid().equals(reqDto.getUuid()))
                                .findFirst()
                                .map(SecgroupRuleCreateReqDto::getId)
                                .orElse(null));
                    }).toList();
            if (!updateList.isEmpty()) {
                secgroupruleApi.updateSecgroupRules(updateList);
            }
            //根据uuid 判断在nowEip不在eipCreateReqDtoList里的数据
            Set<String> uuids = secgroupCreateReqDtoList.stream()
                    .map(SecgroupRuleCreateReqDto::getUuid)
                    .collect(Collectors.toSet());
            List<SecgroupRuleCreateReqDto> delList = now.stream()
                    .filter(respDTO -> !uuids.contains(respDTO.getUuid()))
                    .toList();
            if (!delList.isEmpty()) {
                secgroupruleApi.delSecgroupRules(delList);
            }
        }
    }

    private void updateHostSecgroup(List<HostSecgroupCreateReqDto> secgroupCreateReqDtoList, Long id) {
        if (!secgroupCreateReqDtoList.isEmpty()) {
            List<HostSecgroupCreateReqDto> now = hostSecgroupApi.getHostSecgroupsByPlatformId(id).getData();
            //根据uuid筛选出eipCreateReqDtoList里需要新增的数据
            Set<String> nowUuids = now.stream()
                    .map(r -> r.getHostUuid() + r.getSecgroupUuid())
                    .collect(Collectors.toSet());

            List<HostSecgroupCreateReqDto> addList = secgroupCreateReqDtoList.stream()
                    .filter(reqDto -> !nowUuids.contains(reqDto.getHostUuid() + reqDto.getSecgroupUuid()))
                    .toList();
            if (!addList.isEmpty()) {
                hostSecgroupApi.addHostSecgroups(addList);
            }
            //根据uuid 判断在nowEip不在eipCreateReqDtoList里的数据
            Set<String> uuids = secgroupCreateReqDtoList.stream()
                    .map(r -> r.getHostUuid() + r.getSecgroupUuid())
                    .collect(Collectors.toSet());
            List<HostSecgroupCreateReqDto> delList = now.stream()
                    .filter(respDTO -> !uuids.contains(respDTO.getHostUuid()+ respDTO.getSecgroupUuid()))
                    .toList();
            if (!delList.isEmpty()) {
                hostSecgroupApi.delHostSecgroups(delList);
            }
        }
    }

    @XxlJob("zstackLogin")
    public void loginInfo() {

        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                .filter(dto -> "zstack".equals(dto.getTypeCode()))
                .collect(Collectors.toList());

        if (!filteredList.isEmpty()) {
            for (PlatformconfigDTO p : filteredList) {
                ZstackLoginInfo zstackLoginInfo = zstackAccessTokenRedisDAO.get("zstack:" + p.getId());
                if (zstackLoginInfo == null) {
                    if (p.getAkType() == 0) {
                        try {
                            String processedUrl = removeProtocolAndPort(p.getUrl());
                            String port = extractPort(p.getUrl());
                            ZSClient.configure(
                                    new ZSConfig.Builder()
                                            .setHostname(processedUrl)
                                            .setPort(Convert.toInt(port))
                                            .setContextPath("zstack")
                                            .build()
                            );
                            LogInByAccountAction action = new LogInByAccountAction();
                            action.accountName = p.getUsername();
                            action.password = Sha256.SHA512(p.getPassword());
                            LogInByAccountAction.Result res = action.call();
                            ZstackLoginInfo zstackLoginInfo1 = new ZstackLoginInfo();
                            zstackLoginInfo1.setUuid(res.value.getInventory().getUuid());
                            zstackLoginInfo1.setUserUuid(res.value.getInventory().getUserUuid());
                            zstackLoginInfo1.setAccountUuid(res.value.getInventory().getAccountUuid());
                            zstackLoginInfo1.setUrl(p.getUrl());
                            zstackLoginInfo1.setRegionId(p.getRegionId());
                            zstackLoginInfo1.setTenanName(p.getName());
                            zstackLoginInfo1.setRegionName(p.getRegionName());
                            zstackLoginInfo1.setCreateDate(DateUtil.toLocalDateTime(res.value.getInventory().getCreateDate()));
                            zstackLoginInfo1.setExpiredDate(DateUtil.toLocalDateTime(res.value.getInventory().getExpiredDate()));
                            zstackAccessTokenRedisDAO.set("zstack:" + p.getId(), zstackLoginInfo1);
//                                }
                        } catch (Exception e) {
                            log.info("获取授权异常：" + e.getMessage());
                        }
                    } else {
                        // 如果是ak类型，则将平台中的AccessKey ID，和AccessKey Secret,并讲uuid设置成特有的ak做标记
                        LogInByAccountAction action = new LogInByAccountAction();
                        ZstackLoginInfo zstackLoginInfo1 = new ZstackLoginInfo();
                        zstackLoginInfo1.setUuid("accessKey" + "," + p.getId());
                        zstackLoginInfo1.setAccountUuid(p.getUsername());
                        zstackLoginInfo1.setUserUuid(p.getPassword());
                        zstackLoginInfo1.setUrl(p.getUrl());
                        zstackLoginInfo1.setRegionId(p.getRegionId());
                        zstackLoginInfo1.setTenanName(p.getName());
                        zstackLoginInfo1.setRegionName(p.getRegionName());
                        zstackAccessTokenRedisDAO.set("zstack:" + p.getId(), zstackLoginInfo1);
                    }

                } else {
                    zstackLoginInfo = zstackAccessTokenRedisDAO.get("zstack" + p.getId());
                }
            }
        }
    }

    private static String getSessionByLoginAccount(String accountName, String password) {
        LogInByAccountAction action = new LogInByAccountAction();
        action.accountName = accountName;
        action.password = encryptToSHA512(password);

        LogInByAccountAction.Result result = action.call();
        result.throwExceptionIfError();
        System.out.println("登录成功");
        return result.value.getInventory().getUuid();
    }

    private static String encryptToSHA512(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-512");
            md.reset();
            md.update(input.getBytes("utf8"));
            BigInteger bigInteger = new BigInteger(1, md.digest());
            return String.format("%0128x", bigInteger);
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }


    @XxlJob("cloudByVmInfo")
    public void cpubyVmuuid() {
        // 获取配置租户平台信息

        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                .filter(dto -> "zstack".equals(dto.getTypeCode()))
                .collect(Collectors.toList());
        if (filteredList.isEmpty()) return;

        if (filteredList.size() > 0) {
            for (PlatformconfigDTO p : filteredList) {
                ZstackLoginInfo zstackLoginInfo = zstackAccessTokenRedisDAO.get("zstack:" + p.getId());
                if (zstackLoginInfo != null) {
                    String token = zstackLoginInfo.getUuid();
                    // 根据不同租户查询不同用户下主机信息
                    String d = zstackCloudService.cloudInfo(p.getUrl(), token);
                    JSONArray jsonArray = JSONObject.parseObject(d).getJSONArray("inventories");
                    //1.选择一个库
                    BatchPoints batchPoints = BatchPoints.builder().build();
                    for (int j = 0; j < jsonArray.size(); j++) {
                        HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO = new HostInfoRespCreateReqDTO();
                        JSONObject jsonObject = jsonArray.getJSONObject(j);
                        if ("KVM".equals(jsonObject.getString("hypervisorType"))) {
                            String uuid = jsonObject.getString("uuid");
                            String name = jsonObject.getString("name");
                            if (!"Stopped".equals(jsonObject.getString("state"))) {
                                List<Map<String, Object>> cpuUseds = new ArrayList<>();
                                String cpus = zstackApi.cpuUsedUtilization(p.getUrl(), token, uuid);
                                String avercpus = zstackApi.cpuAverageUsedUtilization(p.getUrl(), token, uuid);
                                JSONArray cpu = JSONObject.parseObject(cpus).getJSONArray("data");
                                List<Map<String, Object>> cpuMaps = parseJSONArray(cpu, "cpuUsed", "value");
                                cpuMaps.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_host")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .tag("uuid", uuid)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .tag("metricName", "CPUUsedUtilization")
                                            .addField("productsName", name)
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("vm_type", StringUtil.toString(map.get("type")))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "CPUUsedUtilization")
                                            .addField("type", StringUtil.toString(map.get("type")))
                                            .addField("value", Convert.toBigDecimal(map.get("cpuUsed")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS).build();
                                    batchPoints.point(point);
                                });

                                JSONArray cau = JSONObject.parseObject(avercpus).getJSONArray("data");
                                List<Map<String, Object>> caumap = parseJSONArray(cau, "cpuUsed", "value");
                                caumap.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_host").tag("uuid", uuid)
                                            .tag("metricName", "CPUAverageUsedUtilization")
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .tag("vm_type", "CPUAverageUsedUtilization")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("productsName", name)
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "CPUAverageUsedUtilization")
                                            .addField("type", "CPUAverageUsedUtilization").addField("value", Convert.toBigDecimal(map.get("cpuUsed"))).time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS).build();
                                    batchPoints.point(point);
                                });
                                // ==================================================内存==========================================
                                // 内存
                                String meUsed = zstackApi.memoryUsedBytes(p.getUrl(), token, uuid);
                                String mefree = zstackApi.memoryFreeBytes(p.getUrl(), token, uuid);
                                JSONArray used = JSONObject.parseObject(meUsed).getJSONArray("data");
                                List<Map<String, Object>> memorymap = parseJSONArray(used, "memory", "value");
                                memorymap.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_host").tag("uuid", uuid)
                                            .tag("metricName", "MemoryUsedBytes")
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .tag("vm_type", "MemoryUsedBytes")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .addField("productsName", name)
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "MemoryUsedBytes")
                                            .addField("type", "MemoryUsedBytes")
                                            .addField("value", Convert.toBigDecimal(map.get("memory")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS).build();
                                    batchPoints.point(point);
                                });
                                JSONArray free = JSONObject.parseObject(mefree).getJSONArray("data");
                                List<Map<String, Object>> freemap = parseJSONArray(free, "memory", "value");
                                freemap.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_host")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "MemoryFreeBytes")

                                            .tag("regionId", StringUtil.toString(p.getRegionId()))

                                            .tag("vm_type", "MemoryFreeBytes")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("productsName", name)
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "MemoryFreeBytes")
                                            .addField("type", "MemoryFreeBytes")
                                            .addField("value", Convert.toBigDecimal(map.get("memory")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS).build();
                                    batchPoints.point(point);
                                });


                                String memoryUsedInPercent = zstackApi.memoryUsedInPercent(p.getUrl(), token, uuid);
                                JSONArray memoryUsedInPercentArray = JSONObject.parseObject(memoryUsedInPercent).getJSONArray("data");
                                List<Map<String, Object>> memoryUsedInPercentArrayMaps = parseJSONArray(memoryUsedInPercentArray, "value", "value");
                                memoryUsedInPercentArrayMaps.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_host")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "MemoryUsedInPercent")
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .tag("vm_type", "MemoryUsedInPercent")
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("productsName", name)
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "MemoryUsedInPercent")
                                            .addField("type", "MemoryUsedInPercent")
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS)
                                            .build();
                                    batchPoints.point(point);
                                });


                                // ====================================磁盘===================================================
                                // disk 读磁盘
                                String diskAllUsed = zstackApi.diskAllUsedCapacityInPercent(p.getUrl(), token, uuid);
                                JSONArray diskUsed = JSONObject.parseObject(diskAllUsed).getJSONArray("data");
                                List<Map<String, Object>> diskUsedMap = parseJSONArray(diskUsed, "value", "value");
                                diskUsedMap.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_host").tag("uuid", uuid)
                                            .tag("metricName", "DiskAllUsedCapacityInPercent")

                                            .tag("regionId", StringUtil.toString(p.getRegionId()))

                                            .tag("vm_type", StringUtil.toString(map.get("type")))
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("productsName", name)
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "DiskAllUsedCapacityInPercent")
                                            .addField("type", StringUtil.toString(map.get("type")))
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS).build();
                                    batchPoints.point(point);
                                });


                                // disk 读磁盘
                                String diskRead = zstackApi.diskReadBytes(p.getUrl(), token, uuid);
                                JSONArray dRead = JSONObject.parseObject(diskRead).getJSONArray("data");
                                List<Map<String, Object>> diskReadMap = parseJSONArray(dRead, "value", "value");
                                diskReadMap.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_host").tag("uuid", uuid)
                                            .tag("metricName", "DiskReadBytes")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("vm_type", StringUtil.toString(map.get("type")))
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "DiskReadBytes").addField("type", StringUtil.toString(map.get("type"))).addField("value", Convert.toBigDecimal(map.get("value"))).time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS).build();
                                    batchPoints.point(point);
                                });
                                // disk 写磁盘
                                String diskWrite = zstackApi.diskWriteBytes(p.getUrl(), token, uuid);
                                JSONArray dWrite = JSONObject.parseObject(diskWrite).getJSONArray("data");
                                List<Map<String, Object>> diskWriteMap = parseJSONArray(dWrite, "value", "value");
                                diskWriteMap.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_host").tag("uuid", uuid)
                                            .tag("metricName", "DiskWriteBytes")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("vm_type", StringUtil.toString(map.get("type")))
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "DiskWriteBytes")
                                            .addField("type", StringUtil.toString(map.get("type")))
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS).build();
                                    batchPoints.point(point);
                                });
                                // disk 读磁盘ops
                                String diskReadOps = zstackApi.diskReadOps(p.getUrl(), token, uuid);
                                JSONArray dReadOps = JSONObject.parseObject(diskReadOps).getJSONArray("data");
                                List<Map<String, Object>> dReadOpsMap = parseJSONArray(dReadOps, "value", "value");
                                dReadOpsMap.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_host").tag("uuid", uuid)
                                            .tag("metricName", "DiskReadOps")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("vm_type", StringUtil.toString(map.get("type")))
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "DiskReadOps")
                                            .addField("type", StringUtil.toString(map.get("type")))
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS).build();
                                    batchPoints.point(point);
                                });

                                // disk 写磁盘ops
                                String diskWriteOps = zstackApi.diskWriteOps(p.getUrl(), token, uuid);
                                JSONArray dWriteOps = JSONObject.parseObject(diskWriteOps).getJSONArray("data");
                                List<Map<String, Object>> dWriteOpsMap = parseJSONArray(dWriteOps, "value", "value");
                                dWriteOpsMap.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_host").tag("uuid", uuid)
                                            .tag("metricName", "DiskWriteOps")
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .tag("vm_type", StringUtil.toString(map.get("type")))
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .addField("productsName", name)
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "DiskWriteOps")
                                            .addField("type", StringUtil.toString(map.get("type"))).addField("value", Convert.toBigDecimal(map.get("value"))).time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS).build();
                                    batchPoints.point(point);
                                });

                                // =====================================网卡==============================================
                                // 网卡发送数据速率
                                String netOutByes = zstackApi.networkOutBytes(p.getUrl(), token, uuid);
                                JSONArray nOutByes = JSONObject.parseObject(netOutByes).getJSONArray("data");
                                List<Map<String, Object>> nOutByesMap = parseJSONArray(nOutByes, "value", "value");
                                nOutByesMap.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_host").tag("uuid", uuid)
                                            .tag("metricName", "NetworkOutBytes")
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .tag("vm_type", StringUtil.toString(map.get("type")))
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .addField("productsName", name)
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "NetworkOutBytes")
                                            .addField("type", StringUtil.toString(map.get("type")))
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS).build();
                                    batchPoints.point(point);
                                });

                                // 网卡接收数据速率
                                String netInByes = zstackApi.networkInBytes(p.getUrl(), token, uuid);
                                JSONArray nInByes = JSONObject.parseObject(netInByes).getJSONArray("data");
                                List<Map<String, Object>> nInByesMap = parseJSONArray(nInByes, "value", "value");
                                nInByesMap.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_host").tag("uuid", uuid)
                                            .tag("metricName", "NetworkInBytes")
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .tag("vm_type", StringUtil.toString(map.get("type")))
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .addField("productsName", name)
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "NetworkInBytes")
                                            .addField("type", StringUtil.toString(map.get("type")))
                                            .addField("value", Convert.toBigDecimal(map.get("value")))
                                            .time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS).build();
                                    batchPoints.point(point);
                                });

                                // 网卡发收包速率
                                String netOutPackets = zstackApi.networkOutPackets(p.getUrl(), token, uuid);
                                JSONArray nOutPackets = JSONObject.parseObject(netOutPackets).getJSONArray("data");
                                List<Map<String, Object>> nOutPacketsMap = parseJSONArray(nOutPackets, "value", "value");
                                nOutPacketsMap.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_host").tag("uuid", uuid)
                                            .tag("metricName", "NetworkOutPackets")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("vm_type", StringUtil.toString(map.get("type")))
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "NetworkOutPackets")
                                            .addField("type", StringUtil.toString(map.get("type")))
                                            .addField("value", Convert.toBigDecimal(map.get("value"))).time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS).build();
                                    batchPoints.point(point);
                                });

                                // 网卡发送包速率
                                String netInPackets = zstackApi.networkInPackets(p.getUrl(), token, uuid);
                                JSONArray nInPackets = JSONObject.parseObject(netInPackets).getJSONArray("data");
                                List<Map<String, Object>> nInPacketsMap = parseJSONArray(nInPackets, "value", "value");
                                nInPacketsMap.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_host")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "NetworkInPackets")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("vm_type", StringUtil.toString(map.get("type")))
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "NetworkInPackets")
                                            .addField("type", StringUtil.toString(map.get("type"))).addField("value", Convert.toBigDecimal(map.get("value"))).time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS).build();
                                    batchPoints.point(point);
                                });

                                // 网卡出包丢弃速率
                                String netOutErrors = zstackApi.networkOutErrors(p.getUrl(), token, uuid);
                                JSONArray nOutErrors = JSONObject.parseObject(netOutErrors).getJSONArray("data");
                                List<Map<String, Object>> nOutErrorsMap = parseJSONArray(nOutErrors, "value", "value");
                                nOutErrorsMap.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_host")
                                            .tag("uuid", uuid)
                                            .tag("metricName", "NetworkOutErrors")
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("vm_type", StringUtil.toString(map.get("type")))
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "NetworkOutErrors").addField("type", StringUtil.toString(map.get("type"))).addField("value", Convert.toBigDecimal(map.get("value"))).time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS).build();
                                    batchPoints.point(point);
                                });
                                // 网卡入包丢弃速率
                                String netInErrors = zstackApi.networkInErrors(p.getUrl(), token, uuid);
                                JSONArray nInErrors = JSONObject.parseObject(netInErrors).getJSONArray("data");
                                List<Map<String, Object>> nInErrorsMap = parseJSONArray(nInErrors, "value", "value");
                                nInErrorsMap.forEach(map -> {
                                    Point point = Point.measurement("zj_cloud_host").tag("uuid", uuid)
                                            .addField("productsName", name)
                                            .tag("regionId", StringUtil.toString(p.getRegionId()))
                                            .addField("regionName", StringUtil.toString(p.getRegionName()))
                                            .tag("metricName", "NetworkInErrors")
                                            .tag("vm_type", StringUtil.toString(map.get("type")))
                                            .tag("platformId", StringUtil.toString(p.getId()))
                                            .addField("platformName", StringUtil.toString(p.getName()))
                                            .addField("vm_metricName", "NetworkInErrors")
                                            .addField("type", StringUtil.toString(map.get("type"))).addField("value", Convert.toBigDecimal(map.get("value"))).time(StringUtil.toLong(map.get("time")), TimeUnit.SECONDS).build();
                                    batchPoints.point(point);
                                });
                            }
                        }
                    }

                    int shardIndex = XxlJobHelper.getShardIndex();
                    int shardTotal = XxlJobHelper.getShardTotal();
                    log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
                    List<Point> s = StringUtil.getShardingData(batchPoints.getPoints(), shardTotal, shardIndex);

                    influxDBTemplate.writeBatch(BatchPoints.builder().points(s).build());
                }
            }
        }
    }


    @XxlJob("cloudByVmInfos")
    public void cloudByVmInfos() {
        List<Callable<Void>> tasks = new ArrayList<>();
        // 获取配置租户平台信息
        List<PlatformconfigDTO> platformconfigDTOList = fetchAndCachePlatformConfig(platformconfigApi, platformRedisDAO);
        if (!platformconfigDTOList.isEmpty()) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                String token = Optional.ofNullable(zstackAccessTokenRedisDAO.get("zstack:" + p.getId()))
                        .map(ZstackLoginInfo::getUuid)
                        .orElse(null);
                if (token != null) {
                    try {
                        // 根据不同租户查询不同用户下主机信息
                        String d = zstackCloudService.cloudInfo(p.getUrl(), token);
                        JSONArray jsonArray = JSONObject.parseObject(d).getJSONArray("inventories");
                        //1.选择一个库
                        // 指标名称数组
                        String[] metrics = {
                                "CPUUsedUtilization", "CPUAverageUsedUtilization", "MemoryUsedBytes", "MemoryFreeBytes",
                                "MemoryUsedInPercent", "DiskAllUsedCapacityInPercent", "DiskReadBytes", "DiskWriteBytes",
                                "DiskReadOps", "DiskWriteOps", "NetworkOutBytes", "NetworkInBytes", "NetworkOutPackets",
                                "NetworkInPackets", "NetworkOutErrors", "NetworkInErrors"
                        };

                        String[] type = {"CPUUsedUtilization", "DiskAllUsedCapacityInPercent", "DiskReadBytes", "DiskWriteBytes",
                                "DiskReadOps", "DiskWriteOps", "NetworkOutBytes", "NetworkInBytes",
                                "NetworkOutPackets", "NetworkInPackets", "NetworkOutErrors", "NetworkInErrors"};

                        BatchPoints batchPoints = BatchPoints.builder().build();
                        for (int i = 0; i < jsonArray.size(); i++) {
                            JSONObject jsonObject = jsonArray.getJSONObject(i);
                            if ("KVM".equals(jsonObject.getString("hypervisorType"))) {
                                String uuid = jsonObject.getString("uuid");
                                String name = jsonObject.getString("name");
                                if (!"Stopped".equals(jsonObject.getString("state"))) {
                                    for (String metric : metrics) {
                                        fetchMetricAndBuildPoints(p.getUrl(), token, p, uuid, metric, "zj_cloud_host", batchPoints, name, type);
                                    }

                                }
                            }
                        }
                        // 写入InfluxDB
                        writeBatchPoints(batchPoints);


                    } catch (Exception e) {
                        System.err.println("Error processing platform " + p.getName() + ": " + e.getMessage());
                        // 日志记录
                        log.error("Error processing platform: " + p.getName(), e);
                    }
                }
            }
        }
    }

    // 将数据点写入InfluxDB的方法
    private void writeBatchPoints(BatchPoints batchPoints) {
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
        List<Point> s = StringUtil.getShardingData(batchPoints.getPoints(), shardTotal, shardIndex);

        influxDBTemplate.writeBatch(BatchPoints.builder().points(s).build());
    }

    private List<Map<String, Object>> parseJSONArray(JSONArray jsonArray, String labelKey, String valueKey) {
        List<Map<String, Object>> maps = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            Map<String, Object> map = new HashMap<>();

            if (jsonObject.getJSONObject("labels").getString("CPUNum") != null) {
                map.put("type", jsonObject.getJSONObject("labels").getString("CPUNum"));
            }
            if (jsonObject.getJSONObject("labels").getString("DiskDeviceLetter") != null) {
                map.put("type", jsonObject.getJSONObject("labels").getString("DiskDeviceLetter"));
            }
            if (jsonObject.getJSONObject("labels").getString("NetworkDeviceLetter") != null) {
                map.put("type", jsonObject.getJSONObject("labels").getString("NetworkDeviceLetter"));
            }
            if (jsonObject.get("time") != null) {
                map.put("time", jsonObject.getInteger("time"));
            }
            map.put("VMUuid", jsonObject.getJSONObject("labels").getString("VMUuid"));
            map.put(labelKey, jsonObject.getBigDecimal(valueKey));
            maps.add(map);
        }
        return maps;
    }


    public static String removeProtocolAndPort(String url) {
        // 去除协议部分
        String noProtocol = url.replaceFirst("^(http://|https://)", "");
        // 去除端口号部分
        String noPort = noProtocol.replaceFirst(":\\d+$", "");
        return noPort;
    }

    public static String extractPort(String url) {
        // 定义正则表达式模式来匹配端口号
        Pattern pattern = Pattern.compile(":\\d+$");
        Matcher matcher = pattern.matcher(url);

        // 查找匹配的端口号
        if (matcher.find()) {
            // 去掉冒号并返回端口号
            return matcher.group().substring(1);
        } else {
            // 如果没有找到端口号，根据协议返回默认端口号
            if (url.startsWith("https://")) {
                return "443";
            } else if (url.startsWith("http://")) {
                return "80";
            }
        }

        // 如果没有找到协议，返回空字符串
        return "";
    }
}
