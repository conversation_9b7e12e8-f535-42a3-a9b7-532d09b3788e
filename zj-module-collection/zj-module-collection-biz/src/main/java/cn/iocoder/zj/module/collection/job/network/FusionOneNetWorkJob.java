package cn.iocoder.zj.module.collection.job.network;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.module.collection.dal.redis.fusionOne.FusionOneAccessTokenRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.service.cloud.FusionOneDeviceService;
import cn.iocoder.zj.module.monitor.api.network.NetworkApi;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL2DTO;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL3DTO;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class FusionOneNetWorkJob {
    @Resource
    PlatformRedisDAO platformRedisDAO;

    @Resource
    FusionOneAccessTokenRedisDAO fusionOneAccessTokenRedisDAO;

    @Resource
    FusionOneDeviceService fusionOneDeviceService;


    @Resource
    NetworkApi networkApi;

    @XxlJob("fusionOneNetWork")
    public void fusionOneNetWork() {
        List<NetWorkL2DTO> netWorkL2DTOS = new ArrayList<>();
        List<NetWorkL3DTO> netWorkL3DTOS = new ArrayList<>();
        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");

        if (platformconfigDTOList.isEmpty()) {
            return; // 提前返回，避免不必要的嵌套
        }

        for (PlatformconfigDTO p : platformconfigDTOList) {
            if (!isFusionOnePlatform(p)) continue;

            String token = getFusionOneToken(p);
            if (token == null) {
                log.info("获取FusionOne token失败");
                continue; // 继续处理下一个平台
            }
            String siteId = getSiteId(p);
            JSONArray netWork = fusionOneDeviceService.getNetWork(p, token, siteId);

            if (ObjectUtil.isNotNull(netWork)) {
                netWorkL2DTOS.addAll(processNetWorkJsonArray(netWork, p));
            }
            JSONArray dvswitchs =fusionOneDeviceService.getDvswitchs(p, token, siteId);
            if (ObjectUtil.isNotNull(dvswitchs)) {
                netWorkL2DTOS.addAll(processDvswitchs(dvswitchs,token, p));
            }
            JSONArray hosts = fusionOneDeviceService.getHosts(p,token,siteId);
            if (ObjectUtil.isNotNull(hosts)) {
                netWorkL2DTOS.addAll(processHostsL2(hosts,siteId,token, p));
                netWorkL3DTOS.addAll(processHostsL3(hosts,siteId,token, p));
            }
            if (!netWorkL2DTOS.isEmpty()) {
                updateNetworkL2(p, netWorkL2DTOS);
            }
            if (!netWorkL3DTOS.isEmpty()) {
                updateNetworkL3(p, netWorkL3DTOS);
            }
        }
    }


    private List<NetWorkL3DTO> processHostsL3(JSONArray hosts, String siteId, String token, PlatformconfigDTO p) {
        List<NetWorkL3DTO> netWorkL3DTOS = new ArrayList<>();
        Date date = DateUtil.date(new Date());
        for (Object host : hosts) {
            JSONObject jsonObject = JSONObject.parseObject(host.toString());
            String urn = jsonObject.getString("urn");
            JSONArray systemIntFs = fusionOneDeviceService.getSystemIntFs(p, token, siteId,urn.split("hosts:")[1]);
            for (Object systemIntF : systemIntFs) {
                JSONObject protObject = (JSONObject) systemIntF;
                //三级
                NetWorkL3DTO netWorkL3DTO = new NetWorkL3DTO();
                netWorkL3DTO.setUuid(protObject.getString("urn"));
                netWorkL3DTO.setName(jsonObject.getString("name"));
                netWorkL3DTO.setL2NetworkUuid(protObject.getString("portUrn"));
                netWorkL3DTO.setL2NetworkName(protObject.getString("portName"));
                netWorkL3DTO.setType("L3BasicNetwork");
                netWorkL3DTO.setNetworkServices("-");
                netWorkL3DTO.setNextHopIp(jsonObject.getString("netAddr"));
                netWorkL3DTO.setCreateTime(date);
                netWorkL3DTO.setPlatformId(p.getId());
                netWorkL3DTO.setPlatformName(p.getName());
                netWorkL3DTO.setTenantId(p.getTenantId());
                netWorkL3DTO.setTypeName("FusionOne");
                netWorkL3DTOS.add(netWorkL3DTO);
            }
        }
        return netWorkL3DTOS;

    }

    private List<NetWorkL2DTO> processHostsL2(JSONArray hosts, String siteId, String token, PlatformconfigDTO p) {
        List<NetWorkL2DTO> netWorkL2DTOS = new ArrayList<>();
        Date date = DateUtil.date(new Date());
        for (Object host : hosts) {
            JSONObject jsonObject = JSONObject.parseObject(host.toString());
            String urn = jsonObject.getString("urn");
            JSONArray systemIntFs = fusionOneDeviceService.getSystemIntFs(p, token, siteId,urn.split("hosts:")[1]);
            for (Object systemIntF : systemIntFs) {
                JSONObject protObject = (JSONObject) systemIntF;
                //二级
                NetWorkL2DTO netWorkL2DTO = new NetWorkL2DTO();
                netWorkL2DTO.setName(protObject.getString("portName"));
                netWorkL2DTO.setUuid(protObject.getString("portUrn"));
                netWorkL2DTO.setPhysicalInterface(protObject.getString("portName"));
                netWorkL2DTO.setType("VXLAN");
                netWorkL2DTO.setVlan("-");
                netWorkL2DTO.setPlatformId(p.getId());
                netWorkL2DTO.setPlatformName(p.getName());
                netWorkL2DTO.setRegionId(p.getRegionId());
                netWorkL2DTO.setRegionName(p.getRegionName());
                netWorkL2DTO.setTenantId(p.getTenantId());
                netWorkL2DTO.setTypeName("FusionOne");
                netWorkL2DTO.setCreateTime(date);
                netWorkL2DTOS.add(netWorkL2DTO);
            }
        }
        return netWorkL2DTOS;
    }

    private List<NetWorkL2DTO> processDvswitchs(JSONArray dvswitchs,String token, PlatformconfigDTO p) {
        List<NetWorkL2DTO> netWorkL2DTOS = new ArrayList<>();
        //获取上行链路详情
        Date date = DateUtil.date(new Date());
        for (Object dvswitch : dvswitchs) {
            JSONObject jsonObject = (JSONObject) dvswitch;
            JSONArray hostPortSet =fusionOneDeviceService.getDvswitchInfo(p, token, getSiteId(p),jsonObject.getString("urn").split("dvswitchs:")[1]);
            for (Object protocol : hostPortSet) {
                JSONObject protObject = (JSONObject) protocol;
                NetWorkL2DTO netWorkL2DTO = new NetWorkL2DTO();
                netWorkL2DTO.setName(protObject.getString("portName"));
                netWorkL2DTO.setUuid(protObject.getString("portUrn"));
                netWorkL2DTO.setPhysicalInterface(protObject.getString("portName"));
                netWorkL2DTO.setType("VXLAN");
                netWorkL2DTO.setVlan("-");
                netWorkL2DTO.setPlatformId(p.getId());
                netWorkL2DTO.setPlatformName(p.getName());
                netWorkL2DTO.setRegionId(p.getRegionId());
                netWorkL2DTO.setRegionName(p.getRegionName());
                netWorkL2DTO.setTenantId(p.getTenantId());
                netWorkL2DTO.setTypeName("FusionOne");
                netWorkL2DTO.setCreateTime(date);
                netWorkL2DTOS.add(netWorkL2DTO);

            }
            return netWorkL2DTOS;
        }

        return null;
    }

    private List<NetWorkL2DTO> processNetWorkJsonArray(JSONArray netWork, PlatformconfigDTO p) {
        List<NetWorkL2DTO> netWorkL2DTOS = new ArrayList<>();
        Date date = DateUtil.date(new Date());

        for (int i = 0; i < netWork.size(); i++) {
            JSONObject jsonObject = netWork.getJSONObject(i);
            NetWorkL2DTO netWorkL2DTO = new NetWorkL2DTO();

            netWorkL2DTO.setName(jsonObject.getString("name"));
            netWorkL2DTO.setUuid(jsonObject.getString("urn"));
            netWorkL2DTO.setPhysicalInterface(jsonObject.getString("physicalInterface"));
            netWorkL2DTO.setType(jsonObject.getInteger("portType") == 0 ? "Access" : "Trunk");
            netWorkL2DTO.setVlan(jsonObject.getString("vlanId"));
            netWorkL2DTO.setVirtualNetworkId(0);
            netWorkL2DTO.setPlatformId(p.getId());
            netWorkL2DTO.setPlatformName(p.getName());
            netWorkL2DTO.setRegionId(p.getRegionId());
            netWorkL2DTO.setRegionName(p.getRegionName());
            netWorkL2DTO.setTenantId(p.getTenantId());
            netWorkL2DTO.setTypeName("FusionOne");
            netWorkL2DTO.setCreateTime(date);
            netWorkL2DTO.setPhysicalInterface(jsonObject.getString("name"));

            netWorkL2DTOS.add(netWorkL2DTO);
        }
        return netWorkL2DTOS;
    }

    private void updateNetworkL2(PlatformconfigDTO p, List<NetWorkL2DTO> netWorkL2DTOS) {
        List<NetWorkL2DTO> existingNetworks = networkApi.getNetworkL2ByPlatformId(p.getId()).getData();

        if (existingNetworks.isEmpty()) {
            networkApi.addNetWorkL2(netWorkL2DTOS);
            return;
        }

        Map<String, NetWorkL2DTO> existingHardwareMap = existingNetworks.stream()
                .collect(Collectors.toMap(NetWorkL2DTO::getUuid, netWorkL2DTO -> netWorkL2DTO));

        List<NetWorkL2DTO> newEntries = new ArrayList<>();
        List<NetWorkL2DTO> updatedEntries = new ArrayList<>();

        for (NetWorkL2DTO newNet : netWorkL2DTOS) {
            NetWorkL2DTO existingNet = existingHardwareMap.get(newNet.getUuid());
            if (existingNet == null) {
                newEntries.add(newNet);
            } else if (!existingNet.equals(newNet)) {
                updatedEntries.add(newNet);
            }
        }

        if (!updatedEntries.isEmpty()) {
            networkApi.updateNetWorkL2(updatedEntries);
        }

        if (!newEntries.isEmpty()) {
            networkApi.addNetWorkL2(newEntries);
        }

        List<NetWorkL2DTO> deleteEntries = existingNetworks.stream()
                .filter(item -> netWorkL2DTOS.stream().noneMatch(newNet -> newNet.getUuid().equals(item.getUuid())))
                .collect(Collectors.toList());

        if (!deleteEntries.isEmpty()) {
            networkApi.deleteNetWorkL2ByNameList(deleteEntries);
        }
    }

    private void updateNetworkL3(PlatformconfigDTO p, List<NetWorkL3DTO> netWorkL3DTOS) {
        List<NetWorkL3DTO> existingNetworks = networkApi.getNetworkL3ByPlatformId(p.getId()).getData();
        if (existingNetworks.isEmpty()) {
            networkApi.addNetWorkL3(netWorkL3DTOS);
            return;
        }
        Map<String, NetWorkL3DTO> existingHardwareMap = existingNetworks.stream()
                .collect(Collectors.toMap(NetWorkL3DTO::getUuid, netWorkL3DTO -> netWorkL3DTO));
        List<NetWorkL3DTO> newEntries = new ArrayList<>();
        List<NetWorkL3DTO> updatedEntries = new ArrayList<>();

        for (NetWorkL3DTO newNet : netWorkL3DTOS) {
            NetWorkL3DTO existingNet = existingHardwareMap.get(newNet.getUuid());
            if (existingNet == null) {
                newEntries.add(newNet);
            } else if (!existingNet.equals(newNet)) {
                updatedEntries.add(newNet);
            }
        }
        if (!updatedEntries.isEmpty()) {
            networkApi.updateNetWorkL3(updatedEntries);
        }

        if (!newEntries.isEmpty()) {
            networkApi.addNetWorkL3(newEntries);
        }
        List<NetWorkL3DTO> deleteEntries = existingNetworks.stream()
                .filter(item -> netWorkL3DTOS.stream().noneMatch(newNet -> newNet.getUuid().equals(item.getUuid())))
                .collect(Collectors.toList());
        if (!deleteEntries.isEmpty()) {
            networkApi.deleteNetWorkL3ByNameList(deleteEntries);
        }
    }

    private boolean isFusionOnePlatform(PlatformconfigDTO p) {
        return "fusionOne".equals(p.getTypeCode()) && fusionOneAccessTokenRedisDAO.get("FusionOne:" + p.getId()) != null;
    }

    private String getFusionOneToken(PlatformconfigDTO p) {
        JSONObject tokenData = fusionOneAccessTokenRedisDAO.get("FusionOne:" + p.getId());
        return tokenData != null ? tokenData.getString("token") : null;
    }

    private String getSiteId(PlatformconfigDTO p) {
        JSONObject tokenData = fusionOneAccessTokenRedisDAO.get("FusionOne:" + p.getId());
        return tokenData != null ? tokenData.getString("siteId") : null;
    }

}
