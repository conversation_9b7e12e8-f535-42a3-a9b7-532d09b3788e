package cn.iocoder.zj.module.collection.job.storage;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.winhong.WinHongAccessTokenRedisDAO;
import cn.iocoder.zj.module.collection.framework.influx.config.InfluxDBTemplate;
import cn.iocoder.zj.module.collection.service.cloud.IWinHongDeviceService;
import cn.iocoder.zj.module.collection.util.StateConverter;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import cn.iocoder.zj.module.monitor.api.hardwarestorage.HardWareStorageApi;
import cn.iocoder.zj.module.monitor.api.hardwarestorage.HardWareStorageRespDTO;
import cn.iocoder.zj.module.monitor.api.storage.StorageInfoApi;
import cn.iocoder.zj.module.monitor.api.storage.dto.StorageRespCreateReqDTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class WinHongStorageJob {

    @Resource
    InfluxDBTemplate influxDBTemplate;
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    StorageInfoApi storageInfoApi;
    @Resource
    PlatformRedisDAO platformRedisDAO;
    @Autowired
    private AlarmConfigApi alarmConfigApi;

    @Resource
    WinHongAccessTokenRedisDAO winHongAccessTokenRedisDAO;

    @Autowired
    private IWinHongDeviceService winHongDeviceService;
    @Resource
    HardWareStorageApi hardWareStorageApi;




    public void winHongStorageJob() {
        getWinHongStorage();
        winHongStorageToInflux();
    }


    @XxlJob("getWinHongStorage")
    public void getWinHongStorage() {
        List<StorageRespCreateReqDTO> storageRespCreateReqDTOS = new ArrayList<>();
        List<PlatformconfigDTO> platformconfigDTOList = new ArrayList<>();
        if (platformRedisDAO.get("platform") == null) {
            platformconfigDTOList = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform", platformconfigDTOList);
        }

        platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> vmwarePlats = new ArrayList<>();
        if (platformconfigDTOList.size() > 0) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                if (p.getTypeCode().equals("winhong") && winHongAccessTokenRedisDAO.get("winhong:" + p.getId()) != null) {
                    vmwarePlats.add(p);
                    String token = winHongDeviceService.getToken(p);
                    if (token==null){
                        log.info("获取云宏token失败");
                        return;
                    }
                    List<HardWareStorageRespDTO> storageList = new ArrayList<>();
                    JSONArray storagePools = winHongDeviceService.getStoragePools(p, token);
                    List<Map> storages = JSONObject.parseArray(storagePools.toJSONString(), Map.class);
                    if (storages.size() > 0) {
                        for (Map storage : storages) {
                            BigDecimal availableCapacity = new BigDecimal(storage.get("available").toString());
                            BigDecimal totalPhysicalCapacity = new BigDecimal(storage.get("capacity").toString());
                            String uuid = storage.get("id").toString();
                            StorageRespCreateReqDTO storageRespCreateReqDTO = new StorageRespCreateReqDTO();
                            storageRespCreateReqDTO.setName(storage.get("name").toString());
                            storageRespCreateReqDTO.setUuid(uuid);
                            storageRespCreateReqDTO.setUrl(storage.get("storageResourceName")!=null?storage.get("storageResourceName").toString():"not used");
                            storageRespCreateReqDTO.setState(Integer.valueOf(storage.get("status").toString()) == 2 ? "Enabled" : "Disabled");
                            storageRespCreateReqDTO.setStatus(Integer.valueOf(storage.get("status").toString()) == 2 ? "Connected" : "Disconnected");
                            storageRespCreateReqDTO.setTotalCapacity(Long.valueOf(storage.get("capacity").toString()));
                            storageRespCreateReqDTO.setTotalPhysicalCapacity(totalPhysicalCapacity);
                            storageRespCreateReqDTO.setUsedCapacity(Long.valueOf(storage.get("usedCapacity").toString()));
                            storageRespCreateReqDTO.setAvailableCapacity(availableCapacity);
                            storageRespCreateReqDTO.setAvailablePhysicalCapacity(new BigDecimal(storage.get("available").toString()));
                            storageRespCreateReqDTO.setCapacityUtilization(new BigDecimal(storage.get("usedCapacity").toString()).divide(new BigDecimal(storage.get("capacity").toString()), 2, RoundingMode.HALF_UP));
                            storageRespCreateReqDTO.setType(getType(storage.get("type").toString()));
                            storageRespCreateReqDTO.setPlatformId(p.getId());
                            storageRespCreateReqDTO.setPlatformName(p.getName());
                            storageRespCreateReqDTO.setRegionId(p.getRegionId());
                            storageRespCreateReqDTO.setDeleted(0);
                            storageRespCreateReqDTO.setTypeName("winHong");
                            ZonedDateTime zonedDateTime = ZonedDateTime.parse(storage.get("time").toString(), DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssZ"));
                            Date date = Date.from(zonedDateTime.toInstant());
                            storageRespCreateReqDTO.setCreateTime(date);
                            storageRespCreateReqDTO.setSCreateTime(date);

                            storageRespCreateReqDTO.setMediaType("机械盘");
                            storageRespCreateReqDTO.setManager("winhong");
                            storageRespCreateReqDTO.setStoragePercent(new BigDecimal(1));


                            //虚拟可用容量
                            BigDecimal availableDecimal = availableCapacity.compareTo(new BigDecimal(0)) > 0 ? availableCapacity : new BigDecimal(0);
                            //虚拟容量
                            storageRespCreateReqDTO.setVirtualCapacity(totalPhysicalCapacity.multiply(storageRespCreateReqDTO.getStoragePercent()));
                            storageRespCreateReqDTO.setAllocation(totalPhysicalCapacity.subtract(availableDecimal));
                            storageRespCreateReqDTO.setCommitRate(storageRespCreateReqDTO.getAllocation().divide(availableDecimal, 2, RoundingMode.HALF_UP));
                            storageRespCreateReqDTO.setVUpdateTime(date);
                            storageRespCreateReqDTO.setRemark(storage.get("remark")!=null?storage.get("remark").toString():"-");
                            storageRespCreateReqDTOS.add(storageRespCreateReqDTO);

                            //查询存储池关联的主机列表
                            JSONArray hosts = winHongDeviceService.getStoragePoolshosts(p, token,uuid);
                            if (hosts!=null && !hosts.isEmpty()){
                                for (Object host : hosts) {
                                    JSONObject jsonObject = JSONObject.parseObject(host.toString());
                                    HardWareStorageRespDTO storageRespDTO = new HardWareStorageRespDTO();
                                    storageRespDTO.setHardwareUuid(jsonObject.getString("hostId"));
                                    storageRespDTO.setStorageUuid(uuid);
                                    storageRespDTO.setPlatformId(p.getId());
                                    storageRespDTO.setPlatformName(p.getName());
                                    storageList.add(storageRespDTO);
                                }
                            }
                        }
                    }
                    if (storageList.size()>0){
                        List<HardWareStorageRespDTO> oldList = hardWareStorageApi.getHardWareStorageByPlatformId(p.getId()).getCheckedData();
                        if (oldList.isEmpty()){
                            hardWareStorageApi.adds(storageList);
                        }else {
                            // 修改Map的key为uuid+hardwareUuid的组合
                            Map<String, HardWareStorageRespDTO> existingHardwareMap = oldList.stream()
                                    .collect(Collectors.toMap(
                                            hardwareStorage -> hardwareStorage.getHardwareUuid() + "_" + hardwareStorage.getStorageUuid(),
                                            hardwareStorage -> hardwareStorage
                                    ));

                            List<HardWareStorageRespDTO> newEntries = new ArrayList<>();
                            List<HardWareStorageRespDTO> updatedEntries = new ArrayList<>();
                            // 修改删除条件，同时比对uuid和hardwareUuid
                            List<HardWareStorageRespDTO> deleteEntries = oldList.stream()
                                    .filter(item -> !storageList.stream()
                                            .anyMatch(newItem ->
                                                    newItem.getHardwareUuid().equals(item.getHardwareUuid()) &&
                                                            newItem.getStorageUuid().equals(item.getStorageUuid())
                                            ))
                                    .collect(Collectors.toList());

                            for (HardWareStorageRespDTO hardWareStorageRespDTO : storageList) {
                                // 使用组合key来查找
                                String compositeKey = hardWareStorageRespDTO.getHardwareUuid() + "_" + hardWareStorageRespDTO.getStorageUuid();
                                HardWareStorageRespDTO nicRespDTO = existingHardwareMap.get(compositeKey);
                                if (nicRespDTO == null) {
                                    newEntries.add(hardWareStorageRespDTO);
                                } else if (!nicRespDTO.equals(hardWareStorageRespDTO)) {
                                    updatedEntries.add(hardWareStorageRespDTO);
                                }
                            }
                            hardWareStorageApi.updates(updatedEntries);
                            hardWareStorageApi.adds(newEntries);
                            if (!deleteEntries.isEmpty()) {
                                hardWareStorageApi.deletes(deleteEntries);
                            }
                        }
                    }

                    if (storageRespCreateReqDTOS.size() > 0) {
                        List<AlarmDorisReqDTO> alarmDorisReqDTO = new ArrayList<>();
                        // 获取当前节点的index 与 总节点数
                        int shardIndex = XxlJobHelper.getShardIndex();
                        int shardTotal = XxlJobHelper.getShardTotal();
                        log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
                        int storageCount = storageInfoApi.count("winHong");
                        List<StorageRespCreateReqDTO> dtos = storageInfoApi.getAll("winHong").getData();
                        List<StorageRespCreateReqDTO> shardingData = new ArrayList<>();

                        if (shardIndex < 0) {
                            shardingData = storageRespCreateReqDTOS;
                        } else {
                            shardingData = StringUtil.getShardingData(storageRespCreateReqDTOS, shardTotal, shardIndex);
                        }
                        // 对分片数据进行业务处理
                        for (StorageRespCreateReqDTO item : shardingData) {
                            // 模拟业务逻辑处理
                            log.info("Processing item: " + item.getName());
                        }
                        if (storageCount == 0) {
                            storageInfoApi.adds(shardingData);
                        } else {

                            List<StorageRespCreateReqDTO> storage = dtos.stream()
                                    .filter(item -> !storageRespCreateReqDTOS.stream().map(StorageRespCreateReqDTO::getUuid)
                                            .collect(Collectors.toList()).contains(item.getUuid()))
                                    .collect(Collectors.toList());
                            final Long alertId = alarmConfigApi.getMaxAlertId().getData();
                            shardingData.forEach(item -> {
                                int i = 1;
                                for (StorageRespCreateReqDTO dto : dtos) {
                                    //启用状态变更后发送告警
                                    if (dto.getUuid().equals(item.getUuid()) && !dto.getStatus().equals(item.getStatus())) {
                                        AlarmDorisReqDTO alarm = new AlarmDorisReqDTO();
                                        JSONObject tags = new JSONObject();
                                        alarm.setId(alertId + i);
                                        tags.put("app", "storage");
                                        tags.put("monitorId", item.getUuid());
                                        tags.put("monitorName", item.getName());
                                        alarm.setPlatformId(item.getPlatformId());
                                        alarm.setResourceType(0);
                                        alarm.setStatus(0);
                                        alarm.setIsSolved(0);
                                        alarm.setGmtCreate(new Date());
                                        alarm.setFirstAlarmTime(DateUtil.current());
                                        alarm.setGmtUpdate(DateUtil.date());
                                        alarm.setLastAlarmTime(DateUtil.current());
                                        alarm.setPlatformName(item.getPlatformName());
                                        alarm.setTimes(1);
                                        alarm.setMonitorId(item.getUuid());
                                        alarm.setContent("云存储:" + item.getName() + " 的启用状态由\"" + StateConverter.stateToCh(dto.getState()) + "\"转换为\"" + StateConverter.stateToCh(item.getState()) + "\"");
                                        alarm.setTarget("storage.state.changed");
                                        alarm.setApp("storage");
                                        alarm.setMonitorName(item.getName());
                                        alarm.setPriority(1);
                                        alarm.setAlarmId(0L);
                                        alarmDorisReqDTO.add(alarm);
                                    }
                                }
                                i++;
                            });
                            if (alarmDorisReqDTO.size() > 0) {
                                Map<String, List> addMap = new HashMap<>();
                                addMap.put("updateList", new ArrayList<>());
                                addMap.put("insertList", alarmDorisReqDTO);
                                alarmConfigApi.createAlarmToDoris(addMap);
                            }
                            // 软删除主存储历史数据
                            if (storage.size() > 0) {
                                storage.forEach(item -> item.setDeleted(1));
                                storageInfoApi.updates(storage);
                            }
                            storageInfoApi.updates(storageRespCreateReqDTOS);
                            List<StorageRespCreateReqDTO> collect = shardingData.stream()
                                    .filter(item -> !dtos.stream().map(StorageRespCreateReqDTO::getUuid)
                                            .collect(Collectors.toList()).contains(item.getUuid()))
                                    .collect(Collectors.toList());
                            if (collect.size() > 0) {
                                collect = collect.stream()
                                        .distinct()
                                        .collect(Collectors.toList());
                                storageInfoApi.adds(collect);
                            }
                        }
                    }

                }
            }
        }
    }

    @XxlJob("winHongStorageToInflux")
    public void winHongStorageToInflux() {
        List<PlatformconfigDTO> platformconfigDTOList = new ArrayList<>();
        if (platformRedisDAO.get("platform") == null) {
            platformconfigDTOList = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform", platformconfigDTOList);
        }
        platformconfigDTOList = platformRedisDAO.get("platform");
        if (platformconfigDTOList.size() > 0) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                if (p.getTypeCode().equals("winhong") && winHongAccessTokenRedisDAO.get("winhong:" + p.getId()) != null) {
                    String token = winHongDeviceService.getToken(p);
                    if (token==null){
                        log.info("获取云宏token失败");
                        return;
                    }
                    JSONArray storagePools = winHongDeviceService.getStoragePools(p, token);
                    List<Map> storages = JSONObject.parseArray(storagePools.toJSONString(), Map.class);
                    BatchPoints batchPoints = BatchPoints.builder().build();
                    for (Map storage : storages) {
                        String uuid = storage.get("id").toString();
                        String name = storage.get("name").toString();
                        BigDecimal memoryFree = new BigDecimal(storage.get("usedCapacity").toString()).divide(new BigDecimal(storage.get("capacity").toString()), 2, RoundingMode.HALF_UP);
                        Point point = Point.measurement("zj_cloud_storage")
                                .tag("uuid", uuid)
                                .tag("metricName", "UsedCapacityInPercent")
                                .addField("productsName", name)
                                .tag("regionId", StringUtil.toString(p.getRegionId()))
                                .addField("regionName", StringUtil.toString(p.getRegionName()))
                                .tag("storage_type", "UsedCapacityInPercent")
                                .tag("platformId", StringUtil.toString(p.getId()))
                                .addField("platformName", StringUtil.toString(p.getName()))
                                .addField("storage_metricName", "UsedCapacityInPercent")
                                .addField("type", "UsedCapacityInPercent")
                                .addField("value", NumberUtil.mul(memoryFree, 100))
                                .time(new Date().getTime() / 1000, TimeUnit.SECONDS)
                                .build();
                        batchPoints.point(point);
                    }
                    int shardIndex = XxlJobHelper.getShardIndex();
                    int shardTotal = XxlJobHelper.getShardTotal();
                    log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
                    List<Point> s = StringUtil.getShardingData(batchPoints.getPoints(), shardTotal, shardIndex);
                    influxDBTemplate.writeBatch(BatchPoints.builder().points(s).build());
                }
            }
        }
    }


    public String getType(String type) {
        switch (type) {
            case "1":
                return "fc存储";
            case "2":
                return "iscsi存储";
            case "3":
                return "nfs存储";
            case "4":
                return "分布式存储";
            case "5":
                return "本地存储";
            case "6":
                return "共享存储";
            case "6+1":
                return "fc共享存储";
            case "6+2":
                return "iscsi 共享存储";
            case "6+6":
                return "nvme共享存储";
            default:
                return "本地存储";
        }
    }


}
