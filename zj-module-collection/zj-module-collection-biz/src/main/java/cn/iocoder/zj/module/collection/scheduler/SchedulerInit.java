//package cn.iocoder.zj.module.collection.scheduler;
//
//import cn.iocoder.cloud.framework.ssh.core.Gateway;
//import cn.iocoder.cloud.framework.ssh.core.Manager;
//import cn.iocoder.cloud.framework.ssh.core.Tunnel;
//import cn.iocoder.cloud.framework.ssh.dal.GatewayDal;
//import com.jcraft.jsch.JSchException;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.boot.CommandLineRunner;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.Ordered;
//import org.springframework.core.annotation.Order;
//
///**
// * @ClassName : SchedulerInit  //类名
// * @Description : scheduler init  //描述
// * <AUTHOR> <EMAIL> //作者
// * @Date: 2024/5/23  10:54
// */
//@Configuration
//@Order(value = Ordered.LOWEST_PRECEDENCE - 1)
//@Slf4j
//public class SchedulerInit implements CommandLineRunner {
//    @Override
//    public void run(String... args) throws Exception {
//        // init pre collector status 初始化ssh 隧道
//
//        GatewayDal gatewayDal = new GatewayDal();
//        gatewayDal.setId("tun1");
//        gatewayDal.setGatewayType("SSH");
//        gatewayDal.setIp("**************");
//        gatewayDal.setPort(22);
//        gatewayDal.setUsername("root");
//        gatewayDal.setPassword("JOYfull@2019");
//
//        String tunnelId = "myTunnel";
//        String remoteIp = "127.0.0.1"; // 或其他远程服务的 IP
//        int remotePort = 3306; // 远程服务监听端口
//        try {
//
////            Gateway gateway = manager.add(gatewayDal); // 添加到 Manager 管理
////            Tunnel tunnel = gateway.openSshTunnel(tunnelId, remoteIp, remotePort); // 打开隧道
//
//
////                // 计算本地隧道端口
////                String localUrl = "http://localhost:" + tunnel.getLocalPort() + "/api/getAll";
////
////                String body = "";
////                HttpResponse result = HttpRequest.post(localUrl)
////                        .execute();
////                if (result.getStatus() != 200) {
////                    throw new RuntimeException("getl3Networks error");
////                }
////                body = result.body();
//
//
//        } catch (JSchException e) {
//            throw new RuntimeException(e);
//        }
//
//
//    }
//}
