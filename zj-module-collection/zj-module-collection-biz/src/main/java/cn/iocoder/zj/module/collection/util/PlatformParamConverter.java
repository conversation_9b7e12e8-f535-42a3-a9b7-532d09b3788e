package cn.iocoder.zj.module.collection.util;

import cn.iocoder.zj.module.collection.dal.dataobject.zstack.AlarmConfigInfo;

public class PlatformParamConverter {
    public static Double convert(Double alarmVal, String unit, String convertType, String platformType) {
        if (convertType.equals("down")) {
            switch (unit.toUpperCase()) {
                case "KB":
                    alarmVal = alarmVal * (1 << 10); // KB 转换为 B
                    break;
                case "MB":
                    alarmVal = alarmVal * (1 << 20); // MB 转换为 B
                    break;
                case "GB":
                    alarmVal = alarmVal * (1 << 30); // GB 转换为 B
                    break;
                case "TB":
                    alarmVal = alarmVal * (1 << 40); // TB 转换为 B
                    break;
            }
        } else {
            switch (unit.toUpperCase()) {
                case "KB":
                    alarmVal = alarmVal / (1 << 10); // B 转换为 KB
                    break;
                case "MB":
                    alarmVal = alarmVal / (1 << 20); // B 转换为 MB
                    break;
                case "GB":
                    alarmVal = alarmVal / (1 << 30); // B 转换为 GB
                    break;
                case "TB":
                    alarmVal = alarmVal / (1 << 40); // B 转换为 TB
                    break;
            }
        }
        return alarmVal;
    }

    public static AlarmConfigInfo conventDictLabelValue(AlarmConfigInfo alarmConfig) {
        switch (alarmConfig.getDictLabelType()){
            case "monitor_alarm_host_memory":
                switch (alarmConfig.getDictLabelValue()){
                    case "MemoryUsedBytes":
                        alarmConfig.setDictLabelValue("MemoryActive");break;
                    case "MemoryFreeBytes":
                        alarmConfig.setDictLabelValue("MemoryUsage");break;
                }
                break;
            case "monitor_alarm_host_cpu":
                switch (alarmConfig.getDictLabelValue()){
                    case "CPUUsedUtilization":
                        alarmConfig.setDictLabelValue("CPUUsage");break;
                    case "CPUAverageUsedUtilization":
                        alarmConfig.setDictLabelValue("CPUUsage");break;
                }
                break;
            case "monitor_alarm_host_disk":
                switch (alarmConfig.getDictLabelValue()){
                    case "DiskWriteBytes":
                        alarmConfig.setDictLabelValue("DiskWrite");break;
                    case "DiskReadBytes":
                        alarmConfig.setDictLabelValue("DiskRead");break;
                }
                break;
        }
        return alarmConfig;
    }

}
