package cn.iocoder.zj.module.collection.job.storage;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.framework.influx.config.InfluxDBTemplate;
import cn.iocoder.zj.module.collection.service.cloud.IStackDeviceService;
import cn.iocoder.zj.module.collection.util.SangForPwEncryptor;
import cn.iocoder.zj.module.collection.util.StateConverter;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import cn.iocoder.zj.module.monitor.api.hardware.HardWareInfoApi;
import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.hardwarestorage.HardWareStorageApi;
import cn.iocoder.zj.module.monitor.api.hardwarestorage.HardWareStorageRespDTO;
import cn.iocoder.zj.module.monitor.api.storage.StorageInfoApi;
import cn.iocoder.zj.module.monitor.api.storage.dto.StorageRespCreateReqDTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class IStackStorageJob {
    @Resource
    InfluxDBTemplate influxDBTemplate;
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    StorageInfoApi storageInfoApi;
    @Resource
    PlatformRedisDAO platformRedisDAO;
    @Resource
    IStackDeviceService iStackDeviceService;
    @Autowired
    private AlarmConfigApi alarmConfigApi;
    @Resource
    HardWareInfoApi hardWareInfoApi;
    @Resource
    HardWareStorageApi hardWareStorageApi;

    public void IStackStorageJob(){
        IStackStorage();
        collectToInflux();
    }
    @XxlJob("IStackStorage")
    public void IStackStorage() {
        // 获取平台配置
        List<PlatformconfigDTO> platformconfigDTOList = getPlatformConfigs();
        if (CollUtil.isEmpty(platformconfigDTOList)) {
            return;
        }

        // 收集存储信息
        List<StorageRespCreateReqDTO> storageRespCreateReqDTOS = new ArrayList<>();
        for (PlatformconfigDTO platform : platformconfigDTOList) {
            if ("istack".equals(platform.getTypeCode())) {
                collectStorageInfo(platform, storageRespCreateReqDTOS);
            }
        }

        // 同步存储数据
        if (!storageRespCreateReqDTOS.isEmpty()) {
            syncStorageData(storageRespCreateReqDTOS);
        }
    }

    private List<PlatformconfigDTO> getPlatformConfigs() {
        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        if (platformconfigDTOList == null) {
            platformconfigDTOList = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform", platformconfigDTOList);
        }
        return platformconfigDTOList;
    }

    private void collectStorageInfo(PlatformconfigDTO platform, List<StorageRespCreateReqDTO> storageRespCreateReqDTOS) {
        List<HardWareStorageRespDTO> storageList = new ArrayList<>();
        JSONArray storages = iStackDeviceService.getStorages(platform);
        if (storages.size() > 0) {
            for (int j = 0; j < storages.size(); j++) {
                JSONObject jsonObject = storages.getJSONObject(j);
                StorageRespCreateReqDTO storageRespCreateReqDTO = new StorageRespCreateReqDTO();
                // 设置基本信息
                storageRespCreateReqDTO.setName(jsonObject.getString("name"));
                storageRespCreateReqDTO.setUuid(jsonObject.getString("uuid"));
                storageRespCreateReqDTO.setUrl("not used");
                storageRespCreateReqDTO.setState("Enabled");
                storageRespCreateReqDTO.setStatus("Connected");
                // 设置容量信息
                storageRespCreateReqDTO.setTotalCapacity(jsonObject.getLong("capacity_size"));
                storageRespCreateReqDTO.setUsedCapacity(jsonObject.getLong("capacity_allocate_used"));
                storageRespCreateReqDTO.setAvailableCapacity(jsonObject.getBigDecimal("capacity_size").subtract(jsonObject.getBigDecimal("capacity_allocate_used")));
                storageRespCreateReqDTO.setCapacityUtilization(jsonObject.getBigDecimal("capacity_allocate_used_rate").multiply(new BigDecimal(100)));
                storageRespCreateReqDTO.setTotalPhysicalCapacity(jsonObject.getBigDecimal("capacity_size"));
                storageRespCreateReqDTO.setAvailablePhysicalCapacity(jsonObject.getBigDecimal("capacity_size").subtract(jsonObject.getBigDecimal("capacity_used")));
                // 设置平台信息
                storageRespCreateReqDTO.setType("其他存储");
                storageRespCreateReqDTO.setPlatformId(platform.getId());
                storageRespCreateReqDTO.setPlatformName(platform.getName());
                storageRespCreateReqDTO.setRegionId(platform.getRegionId());
                storageRespCreateReqDTO.setDeleted(0);
                storageRespCreateReqDTO.setTypeName("istack");
                // 设置时间信息
                ZonedDateTime zonedDateTime = ZonedDateTime.parse(jsonObject.getString("create_time"), DateTimeFormatter.ISO_DATE_TIME);
                Date date = Date.from(zonedDateTime.toInstant());
                storageRespCreateReqDTO.setCreateTime(date);
                storageRespCreateReqDTO.setSCreateTime(date);
                storageRespCreateReqDTO.setVUpdateTime(date);
                // 设置其他信息
                storageRespCreateReqDTO.setMediaType("机械盘");
                storageRespCreateReqDTO.setManager(platform.getName());
                storageRespCreateReqDTO.setAvailableManager(jsonObject.getString("os_id"));
                storageRespCreateReqDTO.setStoragePercent(new BigDecimal(1));
                storageRespCreateReqDTO.setRemark(jsonObject.getString("remark"));
                // 计算虚拟容量
                BigDecimal availableDecimal = storageRespCreateReqDTO.getAvailableCapacity().compareTo(new BigDecimal(0)) > 0 ?
                        storageRespCreateReqDTO.getAvailableCapacity() : new BigDecimal(0);
                storageRespCreateReqDTO.setVirtualCapacity(storageRespCreateReqDTO.getTotalPhysicalCapacity().multiply(storageRespCreateReqDTO.getStoragePercent()));
                storageRespCreateReqDTO.setAllocation(storageRespCreateReqDTO.getTotalPhysicalCapacity().subtract(availableDecimal));
                storageRespCreateReqDTO.setCommitRate(storageRespCreateReqDTO.getAllocation().divide(availableDecimal, 2, RoundingMode.HALF_UP));

                // 处理硬件关联
                List<HardWareRespCreateReqDTO> existingHardwareList = hardWareInfoApi.getHardwareByPlatformId(platform.getId()).getCheckedData();
                if (!existingHardwareList.isEmpty()) {
                    List<HardWareRespCreateReqDTO> osIds = existingHardwareList.stream()
                            .filter(x -> x.getAvailableManager().equals(jsonObject.getString("os_id")))
                            .collect(Collectors.toList());
                    for (HardWareRespCreateReqDTO osId : osIds) {
                        HardWareStorageRespDTO storageRespDTO = new HardWareStorageRespDTO();
                        storageRespDTO.setHardwareUuid(osId.getUuid());
                        storageRespDTO.setStorageUuid(jsonObject.getString("uuid"));
                        storageRespDTO.setPlatformId(platform.getId());
                        storageRespDTO.setPlatformName(platform.getName());
                        storageList.add(storageRespDTO);
                    }
                }
                storageRespCreateReqDTOS.add(storageRespCreateReqDTO);
            }
        }

        // 同步硬件存储关联
        if (storageList.size() > 0) {
            syncHardwareStorage(storageList, platform.getId());
        }
    }

    private void syncHardwareStorage(List<HardWareStorageRespDTO> storageList, Long platformId) {
        List<HardWareStorageRespDTO> oldList = hardWareStorageApi.getHardWareStorageByPlatformId(platformId).getCheckedData();
        if (oldList.isEmpty()) {
            hardWareStorageApi.adds(storageList);
            return;
        }

        Map<String, HardWareStorageRespDTO> existingHardwareMap = oldList.stream()
                .collect(Collectors.toMap(
                        hardwareStorage -> hardwareStorage.getHardwareUuid() + "_" + hardwareStorage.getStorageUuid(),
                        hardwareStorage -> hardwareStorage
                ));

        List<HardWareStorageRespDTO> newEntries = new ArrayList<>();
        List<HardWareStorageRespDTO> updatedEntries = new ArrayList<>();
        List<HardWareStorageRespDTO> deleteEntries = oldList.stream()
                .filter(item -> !storageList.stream()
                        .anyMatch(newItem ->
                                newItem.getHardwareUuid().equals(item.getHardwareUuid()) &&
                                        newItem.getStorageUuid().equals(item.getStorageUuid())
                        ))
                .collect(Collectors.toList());

        for (HardWareStorageRespDTO hardWareStorageRespDTO : storageList) {
            String compositeKey = hardWareStorageRespDTO.getHardwareUuid() + "_" + hardWareStorageRespDTO.getStorageUuid();
            HardWareStorageRespDTO existingDTO = existingHardwareMap.get(compositeKey);
            if (existingDTO == null) {
                hardWareStorageRespDTO.setId(existingDTO.getId());
                newEntries.add(hardWareStorageRespDTO);
            } else if (!existingDTO.equals(hardWareStorageRespDTO)) {
                hardWareStorageRespDTO.setId(existingDTO.getId());
                updatedEntries.add(hardWareStorageRespDTO);
            }
        }

        if (!updatedEntries.isEmpty()) {
            hardWareStorageApi.updates(updatedEntries);
        }
        if (!newEntries.isEmpty()) {
            hardWareStorageApi.adds(newEntries);
        }
        if (!deleteEntries.isEmpty()) {
            hardWareStorageApi.deletes(deleteEntries);
        }
    }

    private void syncStorageData(List<StorageRespCreateReqDTO> storageRespCreateReqDTOS) {
        // 获取分片数据
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        log.info("当前节点的index = {}, 总节点数 = {}", shardIndex, shardTotal);

        List<StorageRespCreateReqDTO> shardingData = shardIndex < 0 ?
                storageRespCreateReqDTOS :
                StringUtil.getShardingData(storageRespCreateReqDTOS, shardTotal, shardIndex);

        // 处理存储数据
        int storageCount = storageInfoApi.count("istack");
        if (storageCount == 0) {
            storageInfoApi.adds(shardingData);
            return;
        }

        // 获取现有存储数据
        List<StorageRespCreateReqDTO> existingStorages = storageInfoApi.getAll("istack").getData();

        // 处理需要删除的存储
        List<StorageRespCreateReqDTO> toDelete = existingStorages.stream()
                .filter(item -> !storageRespCreateReqDTOS.stream()
                        .map(StorageRespCreateReqDTO::getUuid)
                        .collect(Collectors.toList())
                        .contains(item.getUuid()))
                .collect(Collectors.toList());

        // 处理告警
        processStorageAlarms(shardingData, existingStorages);

        // 执行存储同步
        if (!toDelete.isEmpty()) {
            toDelete.forEach(item -> item.setDeleted(1));
            storageInfoApi.updates(toDelete);
        }
        storageInfoApi.updates(storageRespCreateReqDTOS);

        // 处理新增的存储
        List<StorageRespCreateReqDTO> toAdd = shardingData.stream()
                .filter(item -> !existingStorages.stream()
                        .map(StorageRespCreateReqDTO::getUuid)
                        .collect(Collectors.toList())
                        .contains(item.getUuid()))
                .distinct()
                .collect(Collectors.toList());

        if (!toAdd.isEmpty()) {
            storageInfoApi.adds(toAdd);
        }
    }

    private void processStorageAlarms(List<StorageRespCreateReqDTO> newStorages,
                                      List<StorageRespCreateReqDTO> existingStorages) {
        List<AlarmDorisReqDTO> alarms = new ArrayList<>();
        final Long alertId = alarmConfigApi.getMaxAlertId().getData();
        int i = 1;

        for (StorageRespCreateReqDTO newStorage : newStorages) {
            for (StorageRespCreateReqDTO oldStorage : existingStorages) {
                if (oldStorage.getUuid().equals(newStorage.getUuid())
                        && !oldStorage.getStatus().equals(newStorage.getStatus())) {
                    AlarmDorisReqDTO alarm = new AlarmDorisReqDTO();
                    alarm.setId(alertId + i);
                    alarm.setPlatformId(newStorage.getPlatformId());
                    alarm.setResourceType(0);
                    alarm.setStatus(0);
                    alarm.setIsSolved(0);
                    alarm.setGmtCreate(new Date());
                    alarm.setFirstAlarmTime(DateUtil.current());
                    alarm.setGmtUpdate(new Date());
                    alarm.setLastAlarmTime(DateUtil.current());
                    alarm.setPlatformName(newStorage.getPlatformName());
                    alarm.setTimes(1);
                    alarm.setMonitorId(newStorage.getUuid());
                    alarm.setMonitorName(newStorage.getName());
                    alarm.setContent("云存储:" + newStorage.getName() + " 的启用状态由\""
                            + StateConverter.stateToCh(oldStorage.getState()) + "\"转换为\""
                            + StateConverter.stateToCh(newStorage.getState()) + "\"");
                    alarm.setTarget("storage.state.changed");
                    alarm.setApp("storage");
                    alarm.setPriority(1);
                    alarm.setAlarmId(0L);
                    alarms.add(alarm);
                    i++;
                }
            }
        }

        if (!alarms.isEmpty()) {
            Map<String, List> alarmMap = new HashMap<>();
            alarmMap.put("updateList", new ArrayList<>());
            alarmMap.put("insertList", alarms);
            alarmConfigApi.createAlarmToDoris(alarmMap);
        }
    }

    @XxlJob("IStackToInflux")
    public void collectToInflux() {
        try {
            List<PlatformconfigDTO> platformList = getPlatformList();
            if (CollectionUtils.isEmpty(platformList)) {
                log.warn("未获取到平台配置信息");
                return;
            }

            int shardIndex = XxlJobHelper.getShardIndex();
            int shardTotal = XxlJobHelper.getShardTotal();
            log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);

            platformList.stream()
                    .filter(p -> "istack".equals(p.getTypeCode()))
                    .forEach(platform -> processIstackPlatform(platform, shardIndex, shardTotal));

        } catch (Exception e) {
            log.error("采集存储数据到influxDB失败", e);
            throw new RuntimeException("采集存储数据到influxDB失败", e);
        }
    }

    private List<PlatformconfigDTO> getPlatformList() {
        List<PlatformconfigDTO> platformList = platformRedisDAO.get("platform");
        if (platformList == null) {
            platformList = platformconfigApi.getPlatList().getData();
            if (CollectionUtils.isNotEmpty(platformList)) {
                platformRedisDAO.set("platform", platformList);
            }
        }
        return platformList;
    }

    private void processIstackPlatform(PlatformconfigDTO platform, int shardIndex, int shardTotal) {
        try {
            JSONArray storages = iStackDeviceService.getStorages(platform);
            if (CollectionUtils.isEmpty(storages)) {
                log.info("平台{}未获取到存储信息", platform.getName());
                return;
            }

            BatchPoints batchPoints = createBatchPoints(platform, storages);
            if (CollectionUtils.isEmpty(batchPoints.getPoints())) {
                return;
            }

            influxDBTemplate.writeBatch(batchPoints);
            
        } catch (Exception e) {
            log.error("处理istack平台数据失败: {}", platform.getName(), e);
        }
    }

    private BatchPoints createBatchPoints(PlatformconfigDTO platform, JSONArray storages) {
        BatchPoints.Builder batchPointsBuilder = BatchPoints.builder();
        List<Long> timestamps = timestamps();

        for (int i = 0; i < storages.size(); i++) {
            JSONObject storage = storages.getJSONObject(i);
            List<Point> points = createStoragePoints(storage, platform, timestamps);
            points.forEach(batchPointsBuilder::point);
        }

        return batchPointsBuilder.build();
    }

    private List<Point> createStoragePoints(JSONObject storage, PlatformconfigDTO platform, List<Long> timestamps) {
        try {
            BigDecimal capacityUsedRate = storage.getBigDecimal("capacity_used_rate")
                    .multiply(new BigDecimal(100))
                    .setScale(2, RoundingMode.HALF_UP);
            String uuid = storage.getString("uuid");
            String name = storage.getString("name");

            Map<String, String> tags = new HashMap<>();
            tags.put("uuid", uuid);
            tags.put("metricName", "UsedCapacityInPercent");
            tags.put("regionId", StringUtil.toString(platform.getRegionId()));
            tags.put("storage_type", "UsedCapacityInPercent");
            tags.put("platformId", StringUtil.toString(platform.getId()));

            Map<String, Object> fields = new HashMap<>();
            fields.put("productsName", name);
            fields.put("regionName", StringUtil.toString(platform.getRegionName()));
            fields.put("platformName", StringUtil.toString(platform.getName()));
            fields.put("storage_metricName", "UsedCapacityInPercent");
            fields.put("type", "UsedCapacityInPercent");
            fields.put("value", capacityUsedRate);

            List<Point> points = new ArrayList<>();
            timestamps.forEach(timestamp -> {
                points.addAll(createTimeSeriesPoints(tags, fields, timestamps));
            });

            return points;
        } catch (Exception e) {
            log.error("创建存储监控点失败, storage={}", storage, e);
            return Collections.emptyList();
        }
    }

    private Point buildPoint(long timestamp, Map<String, String> tags, Map<String, Object> fields) {
        Point.Builder pointBuilder = Point.measurement("zj_cloud_storage")
                .time(timestamp, TimeUnit.SECONDS);
        tags.forEach((key, value) -> {
            if (value != null) {
                pointBuilder.tag(key, value);
            }
        });
        fields.forEach((key, value) -> {
            if (value != null) {
                if (value instanceof Number) {
                    pointBuilder.addField(key, (Number) value);
                } else {
                    pointBuilder.addField(key, String.valueOf(value));
                }
            }
        });
        
        return pointBuilder.build();
    }

    private List<Point> createTimeSeriesPoints(Map<String, String> tags, Map<String, Object> fields, List<Long> timestamps) {
        return timestamps.stream()
                .map(timestamp -> {
                    try {
                        return buildPoint(timestamp, tags, fields);
                    } catch (Exception e) {
                        log.error("创建时间序列点失败: timestamp={}, tags={}, fields={}", 
                                timestamp, tags, fields, e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static List<Long> timestamps() {
        List<Long> timestamps = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        for (int i = 0; i < 15; i++) {
            timestamps.add(calendar.getTimeInMillis() / 1000);
            calendar.add(Calendar.MINUTE, 1);
        }
        return timestamps;
    }
}
