package cn.iocoder.zj.module.collection.service.storage;

/**
 * @ClassName : ZstackStorage  //类名
 * @Description : 存储系统相关  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/25  15:03
 */
public interface ZstackStorageService {

    String storageInfo(String url, String token);

    String availablePhysicalCapacityInPercent(String url, String token);

    String totalUsedCapacityInBytes(String url, String token);

    String availableCapacityInBytes(String url, String token);

    String totalPhysicalCapacityInBytes(String url, String token);

    String  availableCapacityInPercent(String url,String token);

    String usedCapacityInBytes(String url, String token, String uuid);

    String usedCapacityInPercent(String url,String token,String uuid);

    String availablePhysicalCapacityInBytes(String url,String token);


}
