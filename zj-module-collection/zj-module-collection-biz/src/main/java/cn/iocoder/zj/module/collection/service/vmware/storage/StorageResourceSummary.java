package cn.iocoder.zj.module.collection.service.vmware.storage;

import cn.hutool.core.date.DateUtil;
import cn.iocoder.zj.module.collection.collect.vmware.ConnectedVimServiceBase;
import cn.iocoder.zj.module.collection.service.vmware.cluster.ClusterComputerResourceSummary;
import cn.iocoder.zj.module.collection.service.vmware.vm.VmComputerResourceSummary;
import cn.iocoder.zj.module.collection.util.SampleUtil;
import com.vmware.vim25.*;
import com.vmware.vim25.mo.*;
import org.springframework.stereotype.Component;

import java.rmi.RemoteException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @ClassName : StorageResourceSummary  //类名
 * @Description :  存储 //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/8/5  10:23
 */

@Component
public class StorageResourceSummary {

    public static List<Datastore> getDatastores(ServiceInstance serviceInstance) throws RemoteException {
        List<Datastore> datastores = new ArrayList<Datastore>();
        Datastore datastore = null;

        ManagedEntity[] managedEntities = new InventoryNavigator(serviceInstance.getRootFolder())
                .searchManagedEntities("Datastore");
        if (managedEntities != null && managedEntities.length > 0) {
            for (ManagedEntity managedEntity : managedEntities) {
                datastore = (Datastore) managedEntity;
                datastores.add(datastore);
            }
        } else {
            return null;
        }
        return datastores;
    }


    public static Date getDatastoreCreationTime(ServiceInstance si, Datastore datastore) throws Exception {
        PerformanceManager perfManager = si.getPerformanceManager();

        // 获取可用的采样间隔
        PerfInterval[] intervals = perfManager.getHistoricalInterval();
        if (intervals.length == 0) {
            System.out.println("No historical intervals available.");
            return null;
        }

        // 选择最长的采样间隔，这样可以查询最远的历史数据
        PerfInterval longestInterval = intervals[intervals.length - 1];

        // 创建性能查询规范
        PerfQuerySpec qSpec = new PerfQuerySpec();
        qSpec.setEntity(datastore.getMOR());
        qSpec.setMaxSample(1);

        PerfMetricId perfMetricId = new PerfMetricId();
        perfMetricId.setCounterId(267);
        perfMetricId.setInstance("");
        qSpec.setMetricId(new PerfMetricId[]{perfMetricId});


        qSpec.setStartTime(DateUtil.calendar(DateUtil.parse("1990-01-01 00:00:00")));
        qSpec.setEndTime(DateUtil.calendar(new Date()));

        // 执行查询
        PerfEntityMetricBase[] metrics = perfManager.queryPerf(new PerfQuerySpec[]{qSpec});
        if (metrics != null && metrics.length > 0) {
            PerfEntityMetric metric = (PerfEntityMetric) metrics[0];
            PerfSampleInfo[] sampleInfos = metric.getSampleInfo();
            if (sampleInfos != null && sampleInfos.length > 0) {
                // 返回最早的样本时间作为创建时间的估计
                return sampleInfos[0].getTimestamp().getTime();
            }
        }

        System.out.println("No performance data found for the datastore.");
        return null;
    }


    public static void main(String[] args) throws Exception {

        ServiceInstance serviceInstance = SampleUtil.createServiceInstance("https://172.16.100.50", "<EMAIL>", "Lihulin@123");

        getDatastores(serviceInstance);


        serviceInstance.getServerConnection().logout();
    }

}
