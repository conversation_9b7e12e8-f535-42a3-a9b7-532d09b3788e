package cn.iocoder.zj.module.collection.util;


import com.vmware.vim25.mo.ServiceInstance;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.net.MalformedURLException;
import java.net.URL;
import java.rmi.RemoteException;
import java.security.cert.X509Certificate;

/**
 * @ClassName : SampleUtil  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/7/24  16:30
 */
public class SampleUtil {

    public static ServiceInstance createServiceInstance(String url, String username, String password) throws Exception {
        disableSSLVerification();
        ServiceInstance si = new ServiceInstance(new URL(url + "/sdk"), username, password, true, 5000, 5000);
        si.getSessionManager().setLocale("zh-CN");
        return si;
    }


    // Method to disable SSL certificate verification
    private static void disableSSLVerification() throws Exception {
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }

                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                    }

                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                    }
                }
        };

        SSLContext sc = SSLContext.getInstance("SSL");
        sc.init(null, trustAllCerts, new java.security.SecureRandom());
        HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
        HttpsURLConnection.setDefaultHostnameVerifier((hostname, session) -> true);
    }

}
