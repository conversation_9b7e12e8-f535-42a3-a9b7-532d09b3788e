package cn.iocoder.zj.module.collection.mq.platform;

import cn.iocoder.zj.module.collection.job.cloud.SangForCloudJob;
import cn.iocoder.zj.module.collection.job.cloud.ZstackCloudJob;
import cn.iocoder.zj.module.collection.job.hardware.SangForHardwareJob;
import cn.iocoder.zj.module.collection.job.hardware.ZstackHardWareJob;
import cn.iocoder.zj.module.collection.job.network.SangForNetWorkJob;
import cn.iocoder.zj.module.collection.job.network.ZstackNetWorkJob;
import cn.iocoder.zj.module.collection.job.storage.SangForStorageJob;
import cn.iocoder.zj.module.collection.job.storage.ZstackStorageJob;
import cn.iocoder.zj.module.collection.job.volume.ZstackVolumeJob;
import cn.iocoder.zj.module.collection.job.vpc.ZstackVpcJob;
import cn.iocoder.zj.module.collection.mq.message.platform.PlatformSendMessage;
import cn.iocoder.zj.module.collection.service.platform.PlatformService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @ClassName : PlatformConsumer  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/5/20  14:15
 */
@Component
@Slf4j
public class PlatformConsumer {



    @Resource
    PlatformService platformService;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue("platform_collection"),
            exchange = @Exchange("collection")
    ))
    public void accept(@Payload PlatformSendMessage platformSendMessage) {
        try {
            log.info("[accept][消息内容({})]", platformSendMessage);
            platformService.doSendPlatform(platformSendMessage);
        } catch (Exception e) {
            log.error("处理消息时出错: {}", platformSendMessage, e.getMessage());
        }
    }
}
