package cn.iocoder.zj.module.collection.job.volume;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.module.collection.dal.redis.fusionOne.FusionOneAccessTokenRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.service.cloud.FusionOneDeviceService;
import cn.iocoder.zj.module.monitor.api.valume.VolumeApi;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeSnapshotDTO;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : VmwareHostJob  //类名
 * @Description :   采集vmware宿主机数据//描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/7/25  11:03
 */

@Component
@Slf4j
public class FusionOneVolumJob {
    
    @Resource
    private PlatformRedisDAO platformRedisDAO;
    @Resource
    private VolumeApi volumeApi;
    @Resource
    FusionOneAccessTokenRedisDAO fusionOneAccessTokenRedisDAO;
    @Resource
    FusionOneDeviceService fusionOneDeviceService;

    private static final String PLATFORM_TYPE = "fusionOne";
    private static final String TOKEN_KEY_PREFIX = "FusionOne:";
    private static final BigDecimal BYTE_MULTIPLIER = new BigDecimal(1024 * 1024);

    @XxlJob("fusionOneSnapShortInfo")
    public void fusionOneSnapShortInfo() {
        try {
            List<PlatformconfigDTO> vmwarePlatforms = platformRedisDAO.get("platform").stream()
                    .filter(dto -> PLATFORM_TYPE.equals(dto.getTypeCode()))
                    .collect(Collectors.toList());
            if (vmwarePlatforms.isEmpty()) {
                return;
            }

            for (PlatformconfigDTO platform : vmwarePlatforms) {
                try {
                    handlePlatform(platform);
                } catch (Exception e) {
                    log.error("处理平台 [{}] 失败: {}", platform.getName(), e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            log.error("fusionOne快照同步任务执行失败", e);
        }
    }

    private void handlePlatform(PlatformconfigDTO platform) {
        try {
            if (!PLATFORM_TYPE.equals(platform.getTypeCode())) {
                return;
            }
            Map<String, String> tokenInfo = getTokenInfo(platform.getId());
            if (tokenInfo == null) {
                return;
            }

            List<Map<String, Object>> snapshotList = getSnapshotList(platform, tokenInfo);
            if (!snapshotList.isEmpty()) {
                List<VolumeSnapshotDTO> currentSnapshots = convertToSnapshotDTOs(snapshotList, platform);
                syncSnapshots(currentSnapshots, platform);
            }

        } catch (Exception e) {
            log.error("处理平台[{}]快照信息失败", platform.getName(), e);
        }
    }

    private Map<String, String> getTokenInfo(Long platformId) {
        Map<String, Object> tokenMap = fusionOneAccessTokenRedisDAO.get(TOKEN_KEY_PREFIX + platformId);
        if (tokenMap == null) {
            log.info("Redis中未找到FusionOne token信息");
            return null;
        }

        String token = Convert.toStr(tokenMap.get("token"));
        String siteId = Convert.toStr(tokenMap.get("siteId"));

        if (StrUtil.hasBlank(token, siteId)) {
            log.info("FusionOne token或siteId为空");
            return null;
        }

        Map<String, String> result = new HashMap<>();
        result.put("token", token);
        result.put("siteId", siteId);
        return result;
    }

    private List<Map<String, Object>> getSnapshotList(PlatformconfigDTO platform, Map<String, String> tokenInfo) {
        List<Map<String, Object>> snapshotList = new ArrayList<>();
        JSONArray hosts = fusionOneDeviceService.getClouds(platform, tokenInfo.get("token"), tokenInfo.get("siteId"));
        if (hosts == null) {
            return snapshotList;
        }

        for (int i = 0; i < hosts.size(); i++) {
            JSONObject host = hosts.getJSONObject(i);
            processHostSnapshots(platform, tokenInfo.get("token"), host, snapshotList);
        }

        return snapshotList;
    }

    private void processHostSnapshots(PlatformconfigDTO platform, String token,JSONObject host, List<Map<String, Object>> snapshotList) {
        String uri = host.getString("uri");
        JSONArray snapshots = fusionOneDeviceService.getSnapshots(platform, token, uri);
        if (snapshots == null) {
            return;
        }

        for (Object snapshotObj : snapshots) {
            JSONObject snapshot = (JSONObject) snapshotObj;
            Map<String, Object> snapshotInfo = buildBasicSnapshotInfo(host, snapshot);
            String snapUri = snapshot.getString("uri");
            JSONObject snapshotsInfo = fusionOneDeviceService.getSnapshotsInfo(platform, token, snapUri);
            if (snapshotsInfo != null) {
                enrichSnapshotInfo(platform, token, snapshotInfo, snapshotsInfo);
                snapshotList.add(snapshotInfo);
            }
        }
    }

    private Map<String, Object> buildBasicSnapshotInfo(JSONObject host, JSONObject snapshot) {
        Map<String, Object> snapshotInfo = new HashMap<>();
        snapshotInfo.put("name", snapshot.getString("name"));
        snapshotInfo.put("hostUuid", host.getString("uuid"));
        snapshotInfo.put("hostName", host.getString("name"));
        snapshotInfo.put("description", snapshot.getString("description"));
        snapshotInfo.put("createTime", snapshot.getString("createTime"));
        return snapshotInfo;
    }

    private void enrichSnapshotInfo(PlatformconfigDTO platform, String token,Map<String, Object> snapshotInfo, JSONObject snapshotsInfo) {
        JSONArray volSnapshots = snapshotsInfo.getJSONArray("volsnapshots");
        if (volSnapshots != null && !volSnapshots.isEmpty()) {
            JSONObject volSnapshot = volSnapshots.getJSONObject(0);
            BigDecimal provisionSize = snapshotsInfo.getBigDecimal("snapProvisionSize");
            BigDecimal size = (provisionSize != null && provisionSize.compareTo(BigDecimal.ZERO) > 0)
                    ? provisionSize.multiply(BYTE_MULTIPLIER)
                    : new BigDecimal("0.00");
            snapshotInfo.put("size", size);

//            snapshotInfo.put("size", snapshotsInfo.getBigDecimal("snapProvisionSize").multiply(BYTE_MULTIPLIER));
            snapshotInfo.put("isMemory", snapshotsInfo.getBoolean("includingMemorySnapshot"));
            snapshotInfo.put("primaryStorageUuid", volSnapshot.getString("datastoreUrn"));
            snapshotInfo.put("uuid", volSnapshot.getString("snapUuid"));

            String volumeUri = volSnapshot.getString("volumeUri");
            JSONObject volume = fusionOneDeviceService.getVolumeInfo(platform, token, volumeUri);
            if (volume != null) {
                snapshotInfo.put("volumeUuid", volume.getString("uuid"));
                snapshotInfo.put("volumeType", volume.getString("type"));
                snapshotInfo.put("installPath", extractPathWithoutFilename(volume.getString("volumeUrl")));
            }
        }
    }

    private List<VolumeSnapshotDTO> convertToSnapshotDTOs(List<Map<String, Object>> vmSnapshots,PlatformconfigDTO platform) {
        return vmSnapshots.stream()
                .map(snapshot -> {
                    VolumeSnapshotDTO dto = new VolumeSnapshotDTO();
                    dto.setCreateTime(DateUtil.toLocalDateTime(new Date()));
                    dto.setUuid(Objects.toString(snapshot.get("uuid"), ""));
                    dto.setName(Objects.toString(snapshot.get("name"), ""));
                    dto.setDescription(Objects.toString(snapshot.get("description"), ""));
                    dto.setVolumeUuid(Objects.toString(snapshot.get("volumeUuid"), ""));
                    dto.setPrimaryStorageUuid(Objects.toString(snapshot.get("primaryStorageUuid"), ""));
                    dto.setHostUuid(Objects.toString(snapshot.get("hostUuid"), ""));
                    dto.setHostName(Objects.toString(snapshot.get("hostName"), ""));
                    dto.setSize(Convert.toLong(snapshot.get("size")));
                    dto.setVolumeType(Objects.toString(snapshot.get("volumeType"), ""));
                    dto.setInstallPath(Objects.toString(snapshot.get("installPath"), ""));
                    dto.setType("主机快照");
                    dto.setLatest("true");
                    dto.setPlatformName(platform.getName());
                    dto.setPlatformId(platform.getId());
                    dto.setTypeName(PLATFORM_TYPE);
                    dto.setStatus("Enabled");
                    dto.setVCreateDate(convertStringToDate(snapshot.get("createTime").toString()));
                    dto.setVUpdateDate(new Date());
                    dto.setFormat("vmsn");
                    dto.setIsMemory(convertToBoolean(snapshot.get("isMemory")));
                    dto.setDeleted(0L);
                    return dto;
                })
                .collect(Collectors.toList());
    }
    private void syncSnapshots(List<VolumeSnapshotDTO> currentSnapshots, PlatformconfigDTO platform) {
        try {
            Long existingCount = volumeApi.getVolumeSnapshotCount(PLATFORM_TYPE).getData();
            if (existingCount == 0) {
                volumeApi.addVolumeSnapshots(currentSnapshots);
                return;
            }
            List<VolumeSnapshotDTO> existingSnapshots = volumeApi.getAllVolumeSnapshots(PLATFORM_TYPE).getData();

            // 计算需要新增、更新和删除的快照
            Map<String, List<VolumeSnapshotDTO>> diffResult = calculateSnapshotDiff(currentSnapshots, existingSnapshots);

            // 执行同步操作
            executeSync(diffResult);
            log.info("平台 [{}] 快照同步完成 - 新增: {}, 更新: {}, 删除: {}",
                    platform.getName(),
                    diffResult.get("add").size(),
                    diffResult.get("update").size(),
                    diffResult.get("delete").size());
        } catch (Exception e) {
            log.error("同步快照失败: {}", e.getMessage(), e);
        }
    }

    private Map<String, List<VolumeSnapshotDTO>> calculateSnapshotDiff(List<VolumeSnapshotDTO> currentSnapshots,List<VolumeSnapshotDTO> existingSnapshots) {
        Set<String> existingUuids = existingSnapshots.stream()
                .map(VolumeSnapshotDTO::getUuid)
                .collect(Collectors.toSet());
        Map<String, List<VolumeSnapshotDTO>> result = new HashMap<>();
        result.put("add", currentSnapshots.stream()
                .filter(s -> !existingUuids.contains(s.getUuid()))
                .collect(Collectors.toList()));

        result.put("update", currentSnapshots.stream()
                .filter(s -> existingUuids.contains(s.getUuid()))
                .collect(Collectors.toList()));
        result.put("delete", existingSnapshots.stream()
                .filter(s -> !currentSnapshots.stream()
                        .map(VolumeSnapshotDTO::getUuid)
                        .collect(Collectors.toSet())
                        .contains(s.getUuid()))
                .collect(Collectors.toList()));
        return result;
    }

    private void executeSync(Map<String, List<VolumeSnapshotDTO>> diffResult) {
        if (!diffResult.get("update").isEmpty()) {
            volumeApi.updateVolumeSnapshots(diffResult.get("update"));
        }
        if (!diffResult.get("delete").isEmpty()) {
            volumeApi.delVolumeSnapshots(diffResult.get("delete"));
        }
        if (!diffResult.get("add").isEmpty()) {
            volumeApi.addVolumeSnapshots(diffResult.get("add"));
        }
    }

    public static String extractPathWithoutFilename(String input) {
        if (input == null || input.isEmpty()) {
            return "";
        }
        int lastSlashIndex = input.lastIndexOf('/');
        return lastSlashIndex == -1 ? input : input.substring(0, lastSlashIndex);
    }

    public static boolean convertToBoolean(Object value) {
        if (value instanceof Boolean) {
            return (Boolean) value;
        } else if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }
        return false;
    }

    public static Date convertStringToDate(String dateString){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            return sdf.parse(dateString);
        } catch (ParseException e) {
        }
        return new Date();
    }
}
