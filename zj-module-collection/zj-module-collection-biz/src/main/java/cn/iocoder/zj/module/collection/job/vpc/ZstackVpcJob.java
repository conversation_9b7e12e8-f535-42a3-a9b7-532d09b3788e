package cn.iocoder.zj.module.collection.job.vpc;

import cn.hutool.core.date.DateUtil;
import cn.iocoder.zj.module.collection.dal.dataobject.zstack.ZstackLoginInfo;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.zstack.ZstackAccessTokenRedisDAO;
import cn.iocoder.zj.module.collection.framework.influx.config.InfluxDBTemplate;
import cn.iocoder.zj.module.collection.service.cloud.IZstackCloudService;
import cn.iocoder.zj.module.collection.service.hardware.IZstackHardWareService;
import cn.iocoder.zj.module.collection.service.network.ZstackNetWorkService;
import cn.iocoder.zj.module.collection.service.vpc.ZstackVpcService;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.monitor.api.network.NetworkApi;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkVpcDTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName : ZstackVpcJob  //类名
 * @Description : VPC 网络采集  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/8/3  14:24
 */

@Component
@Slf4j
public class ZstackVpcJob {
    @Resource
    InfluxDBTemplate influxDBTemplate;
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    ZstackAccessTokenRedisDAO zstackAccessTokenRedisDAO;
    @Resource
    PlatformRedisDAO platformRedisDAO;

    @Resource
    ZstackVpcService zstackVpcService;
    @Resource
    IZstackCloudService zstackCloudService;
    @Resource
    IZstackHardWareService iZstackHardWareService;
    @Resource
    ZstackNetWorkService zstackNetWorkService;
    @Resource
    NetworkApi networkApi;

    public void zstackVpcJob(){
        execute();
    }

    @XxlJob("virtualRouters")
    public void execute() {

        List<NetWorkVpcDTO> netWorkVpcDTOS = new ArrayList<>();
        List<PlatformconfigDTO> platformconfigDTOList = new ArrayList<>();
        if (platformRedisDAO.get("platform") == null) {
            platformconfigDTOList = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform", platformconfigDTOList);
        }

        platformconfigDTOList = platformRedisDAO.get("platform");

        if (platformconfigDTOList.size() > 0) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                ZstackLoginInfo zstackLoginInfo = new ZstackLoginInfo();
                if (zstackAccessTokenRedisDAO.get("zstack:" + p.getId()) != null) {
                    String token = zstackAccessTokenRedisDAO.get("zstack:" + p.getId()).getUuid();

                    String d = zstackVpcService.virtualRouters(p.getUrl(), token);
                    JSONArray jsonArray = JSONObject.parseObject(d).getJSONArray("inventories");
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        String uuid = jsonObject.getString("uuid");

                        Date date =  DateUtil.date( new Date(jsonObject.getString("createDate")));

                        String name = jsonObject.getString("name");
                        Integer cpu_num = jsonObject.getInteger("cpuNum");
                        Long memorySize = jsonObject.getLong("memorySize");
                        String architecture = jsonObject.getString("architecture");
                        String status = jsonObject.getString("status");
                        String state = jsonObject.getString("state");
                        String managementNetworkUuid = jsonObject.getString("managementNetworkUuid");
                        String l3NetworkUuid = jsonObject.getString("defaultRouteL3NetworkUuid");
                        String clusterUuid = jsonObject.getString("clusterUuid");
                        String hypervisorType = jsonObject.getString("hypervisorType");
                        String hostUuid = jsonObject.getString("hostUuid");
                        Long platformId = p.getId();
                        String platformName = p.getName();
                        Long tenantId = p.getTenantId();

                        String l3NetworkName = "";
                        // 根据hostUuid 去查名称
                        String hostName = "";
                        // 取ipv4的mac地址
                        String mac = "";
                        // 根据clusterUuid 去查
                        String clusterName = "";
                        // IPv4 地址根据l3networkUuid 去查；
                        String ip = "";
                        // 根据dns 循环去取
                        String dns = "";
                        // 根据managementNetwork中的uuid 相同取vmics中uuid 相同的ip地址，ip同理
                        String managementNetworkip = "";

                        // 集群名称
                        String clusters1 = zstackCloudService.clusters(p.getUrl(), token, clusterUuid);
                        JSONArray clus = JSONObject.parseObject(clusters1).getJSONArray("inventories");
                        clusterName = clus.getJSONObject(0).getString("name");


                        // 宿主机名称
                        String hName = iZstackHardWareService.hardWareInfo(p.getUrl(), token, hostUuid);
                        JSONArray hNameArray = JSONObject.parseObject(hName).getJSONArray("inventories");
                        hostName = hNameArray.getJSONObject(0).getString("name");
                        // 三级网络名称
                        String l3 = zstackNetWorkService.vpcNetworks(p.getUrl(), token, l3NetworkUuid);
                        JSONArray l3JsonArray = JSONObject.parseObject(l3).getJSONArray("inventories");
                        l3NetworkName = l3JsonArray.getJSONObject(0).getString("name");
                        // dns
                        JSONArray jsonArrayDns = jsonObject.getJSONArray("dns");

                        if (jsonArrayDns != null && jsonArrayDns.size() > 0) {
                            StringBuilder sb = new StringBuilder();
                            for (int j = 0; j < jsonArrayDns.size(); j++) {
                                JSONObject dnsobj = jsonArrayDns.getJSONObject(j);
                                if (j > 0) {
                                    sb.append(",");
                                }
                                sb.append(dnsobj.getString("dns"));
                            }
                            dns = sb.toString();
                        }

                        JSONArray jsonArrayVmNics = jsonObject.getJSONArray("vmNics");
                        for (int j = 0; j < jsonArrayVmNics.size(); j++) {
                            JSONObject vmNics = jsonArrayVmNics.getJSONObject(j);
                            if (l3NetworkUuid.equals(vmNics.getString("l3NetworkUuid"))) {
                                ip = vmNics.getString("ip");
                                mac = vmNics.getString("mac");
                                managementNetworkip = vmNics.getString("ip");
                            }
                        }

                        NetWorkVpcDTO netWorkVpcDTO = new NetWorkVpcDTO();
                        netWorkVpcDTO.setUuid(uuid);
                        netWorkVpcDTO.setName(name);
                        netWorkVpcDTO.setCpuNum(cpu_num);
                        netWorkVpcDTO.setMemorySize(memorySize);
                        netWorkVpcDTO.setArchitecture(architecture);
                        netWorkVpcDTO.setDns(dns);
                        netWorkVpcDTO.setStatus(status);
                        netWorkVpcDTO.setState(state);
                        netWorkVpcDTO.setL3NetworkUuid(l3NetworkUuid);
                        netWorkVpcDTO.setL3NetworkName(l3NetworkName);
                        netWorkVpcDTO.setIp(ip);
                        netWorkVpcDTO.setManagementNetworkUuid(managementNetworkUuid);
                        netWorkVpcDTO.setManagementNetworkIp(managementNetworkip);
                        netWorkVpcDTO.setClusterUuid(clusterUuid);
                        netWorkVpcDTO.setClusterName(clusterName);
                        netWorkVpcDTO.setHypervisorType(hypervisorType);
                        netWorkVpcDTO.setMac(mac);
                        netWorkVpcDTO.setHostUuid(hostUuid);
                        netWorkVpcDTO.setHostName(hostName);
                        netWorkVpcDTO.setPlatformId(platformId);
                        netWorkVpcDTO.setPlatformName(platformName);
                        netWorkVpcDTO.setTenantId(tenantId);
                        netWorkVpcDTO.setCreateTime(date);
                        netWorkVpcDTOS.add(netWorkVpcDTO);
                    }

                }
            }
        }

        if (netWorkVpcDTOS.size() > 0) {
            // 获取当前节点的index 与 总节点数
            int shardIndex = XxlJobHelper.getShardIndex();
            int shardTotal = XxlJobHelper.getShardTotal();
            log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
            Long hardWare = networkApi.getNetWorkVpcCount();
            List<NetWorkVpcDTO> dtos = networkApi.getNetWorkVpcList().getData();
            List<NetWorkVpcDTO> shardingData =new ArrayList<>();


            if (shardIndex<0){
                shardingData = netWorkVpcDTOS;
            }else {
                shardingData = StringUtil.getShardingData(netWorkVpcDTOS, shardTotal, shardIndex);
            }

            // 对分片数据进行业务处理
            for (NetWorkVpcDTO item : shardingData) {
                // 模拟业务逻辑处理
                log.info("Processing item: " + item.getName());
            }
            if (hardWare == 0) {
                networkApi.addNetWorkVPC(shardingData);
            } else {
                List<NetWorkVpcDTO> vpcDTOS = dtos.stream()
                        .filter(item -> !netWorkVpcDTOS.stream().map(NetWorkVpcDTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid()))
                        .collect(Collectors.toList());
                if (vpcDTOS.size() > 0) {
                    networkApi.deleteNetWorkVPCByNameList(vpcDTOS);
                }
                networkApi.updateNetWorkVpc(shardingData);
                List<NetWorkVpcDTO> collect = shardingData.stream()
                        .filter(item -> !dtos.stream().map(NetWorkVpcDTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid()))
                        .collect(Collectors.toList());
                if (collect.size() > 0) {
                    networkApi.addNetWorkVPC(collect);
                }
            }
        }

    }
}
