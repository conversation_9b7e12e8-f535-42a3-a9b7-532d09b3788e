package cn.iocoder.zj.module.collection.job.storage;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.dal.redis.sangFor.SangForAccessTokenRedisDAO;
import cn.iocoder.zj.module.collection.framework.influx.config.InfluxDBTemplate;
import cn.iocoder.zj.module.collection.service.cloud.ISangForDeviceService;
import cn.iocoder.zj.module.collection.util.SangForPwEncryptor;
import cn.iocoder.zj.module.collection.util.StateConverter;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmRecordDTO;
import cn.iocoder.zj.module.monitor.api.hardwarestorage.HardWareStorageApi;
import cn.iocoder.zj.module.monitor.api.hardwarestorage.HardWareStorageRespDTO;
import cn.iocoder.zj.module.monitor.api.storage.StorageInfoApi;
import cn.iocoder.zj.module.monitor.api.storage.dto.StorageRespCreateReqDTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SangForStorageJob {
    @Resource
    InfluxDBTemplate influxDBTemplate;
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    StorageInfoApi storageInfoApi;
    @Resource
    PlatformRedisDAO platformRedisDAO;
    @Resource
    ISangForDeviceService iSangForDeviceService;
    @Autowired
    private AlarmConfigApi alarmConfigApi;
    @Resource
    HardWareStorageApi hardWareStorageApi;

    private void sangForStorageJob(){
        getSangForStorage();
        collectToInflux();
    }
    @XxlJob("getSangForStorage")
    public void getSangForStorage(){
        List<StorageRespCreateReqDTO> storageRespCreateReqDTOS = new ArrayList<>();
        List<PlatformconfigDTO>  platformconfigDTOList = new ArrayList<>();
        if (platformRedisDAO.get("platform") == null) {
            platformconfigDTOList = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform",platformconfigDTOList);
        }
        Map<Long,Date> dateMap = platformconfigApi.getPlatCreateTimeList().getData();
        platformconfigDTOList = platformRedisDAO.get("platform");
        List<PlatformconfigDTO> vmwarePlats = new ArrayList<>();
        if (platformconfigDTOList.size() > 0) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                if (p.getTypeCode().equals("sangFor")) {
                    vmwarePlats.add(p);
                    JSONObject tokenInfo = SangForPwEncryptor.sangForlogin(p);
                    JSONArray storages = iSangForDeviceService.getStorages(p,tokenInfo);
                    List<HardWareStorageRespDTO> storageList = new ArrayList<>();
                    if (storages.size()>0){
                        for (int j = 0; j < storages.size(); j++) {
                            StorageRespCreateReqDTO storageRespCreateReqDTO = new StorageRespCreateReqDTO();
                            JSONObject jsonObject = storages.getJSONObject(j);
                            storageRespCreateReqDTO.setName(jsonObject.getString("name"));
                            storageRespCreateReqDTO.setUuid(jsonObject.getString("id"));
                            storageRespCreateReqDTO.setUrl("not used");
                            storageRespCreateReqDTO.setState(jsonObject.getInteger("disable")==0?"Enabled":"Disabled");
                            storageRespCreateReqDTO.setStatus(jsonObject.getInteger("active")==1?"Connected":"Disconnected");
                            storageRespCreateReqDTO.setTotalCapacity(jsonObject.getLong("total"));
                            storageRespCreateReqDTO.setTotalPhysicalCapacity(jsonObject.getBigDecimal("total"));
                            storageRespCreateReqDTO.setUsedCapacity(jsonObject.getLong("used"));
                            storageRespCreateReqDTO.setAvailableCapacity(jsonObject.getBigDecimal("avail"));
                            storageRespCreateReqDTO.setAvailablePhysicalCapacity(jsonObject.getBigDecimal("avail"));
                            storageRespCreateReqDTO.setCapacityUtilization(jsonObject.getBigDecimal("used_ratio")==null?new BigDecimal(0):jsonObject.getBigDecimal("used_ratio"));
                            storageRespCreateReqDTO.setType(jsonObject.getString("type"));
                            storageRespCreateReqDTO.setPlatformId(p.getId());
                            storageRespCreateReqDTO.setPlatformName(p.getName());
                            storageRespCreateReqDTO.setRegionId(p.getRegionId());
                            storageRespCreateReqDTO.setDeleted(0);
                            storageRespCreateReqDTO.setTypeName("sangFor");
                            storageRespCreateReqDTO.setCreateTime(dateMap.get(p.getId()));
                            storageRespCreateReqDTO.setSCreateTime(dateMap.get(p.getId()));
                            storageRespCreateReqDTO.setMediaType("机械盘");
                            storageRespCreateReqDTO.setManager(p.getName());
                            storageRespCreateReqDTO.setStoragePercent(new BigDecimal(1));
                            storageRespCreateReqDTO.setRemark(jsonObject.getString("remark"));
                            // 计算虚拟容量
                            BigDecimal availableDecimal = storageRespCreateReqDTO.getAvailableCapacity().compareTo(new BigDecimal(0)) > 0 ?
                                    storageRespCreateReqDTO.getAvailableCapacity() : new BigDecimal(0);
                            storageRespCreateReqDTO.setVirtualCapacity(storageRespCreateReqDTO.getTotalPhysicalCapacity().multiply(storageRespCreateReqDTO.getStoragePercent()));
                            storageRespCreateReqDTO.setAllocation(storageRespCreateReqDTO.getTotalPhysicalCapacity().subtract(availableDecimal));
                            if(availableDecimal.compareTo(new BigDecimal(0)) != 0){
                                storageRespCreateReqDTO.setCommitRate(storageRespCreateReqDTO.getAllocation().divide(availableDecimal, 2, RoundingMode.HALF_UP));
                            }else {
                                storageRespCreateReqDTO.setCommitRate(new BigDecimal(0));
                            }

                            storageRespCreateReqDTOS.add(storageRespCreateReqDTO);

                            //查询主存储对应的物理机列表
                            JSONArray hosts=iSangForDeviceService.getVtpstorageDetail(p, tokenInfo,jsonObject.getString("id"));
                            if (hosts!=null && !hosts.isEmpty()){
                                for (Object host : hosts) {
                                    JSONObject hostJson = JSONObject.parseObject(host.toString());
                                    HardWareStorageRespDTO storageRespDTO = new HardWareStorageRespDTO();
                                    storageRespDTO.setHardwareUuid(hostJson.getString("host_id"));
                                    storageRespDTO.setStorageUuid(jsonObject.getString("id"));
                                    storageRespDTO.setPlatformId(p.getId());
                                    storageRespDTO.setPlatformName(p.getName());
                                    storageList.add(storageRespDTO);
                                }
                            }
                        }
                    }else {
                        JSONArray storageClouds = iSangForDeviceService.getStorageClouds(p, tokenInfo);
                        JSONArray storageCloudsRealTime = iSangForDeviceService.getStoragesRealTime(p, tokenInfo);
                        Map<String, String> realTimeMap = storageCloudsRealTime.stream()
                                .map(obj -> JSONObject.parseObject(obj.toString()))
                                .collect(Collectors.toMap(
                                        json -> json.getString("id"),
                                        json -> json.getString("nfstype"),
                                        (existing, replacement) -> existing // 处理重复key的情况
                                ));
                        for (int i = 0; i < storageClouds.size(); i++) {
                            JSONObject storage = JSONObject.parseObject(storageClouds.getString(i));
                            String id = storage.getString("id");
                            if (realTimeMap.containsKey(id)) {
                                storage.put("nfstype", realTimeMap.get(id));
                                storageClouds.set(i, storage);
                            }
                        }
                        for (int j = 0; j < storageClouds.size(); j++) {
                            StorageRespCreateReqDTO storageRespCreateReqDTO = new StorageRespCreateReqDTO();
                            JSONObject jsonObject = storageClouds.getJSONObject(j);
                            storageRespCreateReqDTO.setName(jsonObject.getString("name"));
                            storageRespCreateReqDTO.setUuid(jsonObject.getString("id"));
                            storageRespCreateReqDTO.setUrl("not used");
                            storageRespCreateReqDTO.setState(jsonObject.getString("health_status").equals("ok")?"Enabled":"Disabled");
                            storageRespCreateReqDTO.setStatus(jsonObject.getString("health_status").equals("ok")?"Connected":"Disconnected");
                            storageRespCreateReqDTO.setTotalCapacity(jsonObject.getLong("total"));
                            storageRespCreateReqDTO.setTotalPhysicalCapacity(jsonObject.getBigDecimal("total"));
                            storageRespCreateReqDTO.setUsedCapacity(jsonObject.getLong("total")-jsonObject.getLong("free"));
                            storageRespCreateReqDTO.setAvailableCapacity(jsonObject.getBigDecimal("free"));
                            storageRespCreateReqDTO.setAvailablePhysicalCapacity(jsonObject.getBigDecimal("free"));
                            storageRespCreateReqDTO.setCapacityUtilization(jsonObject.getBigDecimal("disk_num"));
                            storageRespCreateReqDTO.setType(jsonObject.getString("nfstype").equals("vsnfs")?"虚拟存储":"其他存储");
                            storageRespCreateReqDTO.setPlatformId(p.getId());
                            storageRespCreateReqDTO.setPlatformName(p.getName());
                            storageRespCreateReqDTO.setRegionId(p.getRegionId());
                            storageRespCreateReqDTO.setDeleted(0);
                            storageRespCreateReqDTO.setTypeName("sangFor");
                            storageRespCreateReqDTO.setCreateTime(new Date());
                            storageRespCreateReqDTO.setSCreateTime(new Date());

                            storageRespCreateReqDTO.setMediaType("机械盘");
                            storageRespCreateReqDTO.setManager(p.getName());
                            storageRespCreateReqDTO.setStoragePercent(new BigDecimal(1));
                            storageRespCreateReqDTO.setRemark(jsonObject.getString("remark"));
                            // 计算虚拟容量
                            BigDecimal availableDecimal = storageRespCreateReqDTO.getAvailableCapacity().compareTo(new BigDecimal(0)) > 0 ?
                                    storageRespCreateReqDTO.getAvailableCapacity() : new BigDecimal(0);
                            storageRespCreateReqDTO.setVirtualCapacity(storageRespCreateReqDTO.getTotalPhysicalCapacity().multiply(storageRespCreateReqDTO.getStoragePercent()));
                            storageRespCreateReqDTO.setAllocation(storageRespCreateReqDTO.getTotalPhysicalCapacity().subtract(availableDecimal));
                            storageRespCreateReqDTO.setCommitRate(storageRespCreateReqDTO.getAllocation().divide(availableDecimal, 2, RoundingMode.HALF_UP));

                            storageRespCreateReqDTOS.add(storageRespCreateReqDTO);
                            //查询主存储对应的物理机列表
                            JSONArray hosts=iSangForDeviceService.getVtpstorageDetail(p, tokenInfo,jsonObject.getString("id"));
                            if (hosts!=null && !hosts.isEmpty()){
                                for (Object host : hosts) {
                                    JSONObject hostJson = JSONObject.parseObject(host.toString());
                                    HardWareStorageRespDTO storageRespDTO = new HardWareStorageRespDTO();
                                    storageRespDTO.setHardwareUuid(hostJson.getString("host_id"));
                                    storageRespDTO.setStorageUuid(jsonObject.getString("id"));
                                    storageRespDTO.setPlatformId(p.getId());
                                    storageRespDTO.setPlatformName(p.getName());
                                    storageList.add(storageRespDTO);
                                }
                            }
                        }
                    }
                    if (storageList.size()>0){
                        List<HardWareStorageRespDTO> oldList = hardWareStorageApi.getHardWareStorageByPlatformId(p.getId()).getCheckedData();
                        if (oldList.isEmpty()){
                            hardWareStorageApi.adds(storageList);
                        }else {
                            // 修改Map的key为uuid+hardwareUuid的组合
                            Map<String, HardWareStorageRespDTO> existingHardwareMap = oldList.stream()
                                    .collect(Collectors.toMap(
                                            hardwareStorage -> hardwareStorage.getHardwareUuid() + "_" + hardwareStorage.getStorageUuid(),
                                            hardwareStorage -> hardwareStorage
                                    ));

                            List<HardWareStorageRespDTO> newEntries = new ArrayList<>();
                            List<HardWareStorageRespDTO> updatedEntries = new ArrayList<>();
                            // 修改删除条件，同时比对uuid和hardwareUuid
                            List<HardWareStorageRespDTO> deleteEntries = oldList.stream()
                                    .filter(item -> !storageList.stream()
                                            .anyMatch(newItem ->
                                                    newItem.getHardwareUuid().equals(item.getHardwareUuid()) &&
                                                            newItem.getStorageUuid().equals(item.getStorageUuid())
                                            ))
                                    .collect(Collectors.toList());

                            for (HardWareStorageRespDTO hardWareStorageRespDTO : storageList) {
                                // 使用组合key来查找
                                String compositeKey = hardWareStorageRespDTO.getHardwareUuid() + "_" + hardWareStorageRespDTO.getStorageUuid();
                                HardWareStorageRespDTO nicRespDTO = existingHardwareMap.get(compositeKey);
                                if (nicRespDTO == null) {
                                    newEntries.add(hardWareStorageRespDTO);
                                } else if (!nicRespDTO.equals(hardWareStorageRespDTO)) {
                                    updatedEntries.add(hardWareStorageRespDTO);
                                }
                            }
                            hardWareStorageApi.updates(updatedEntries);
                            hardWareStorageApi.adds(newEntries);
                            if (!deleteEntries.isEmpty()) {
                                hardWareStorageApi.deletes(deleteEntries);
                            }
                        }
                    }
                }
            }
        }
        if (storageRespCreateReqDTOS.size()>0){
            List<AlarmDorisReqDTO> alarmDorisReqDTO = new ArrayList<>();
            // 获取当前节点的index 与 总节点数
            int shardIndex = XxlJobHelper.getShardIndex();
            int shardTotal = XxlJobHelper.getShardTotal();
            log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
            int storageCount = storageInfoApi.count("sangFor");
            List<StorageRespCreateReqDTO> dtos = storageInfoApi.getAll("sangFor").getData();
            List<StorageRespCreateReqDTO> shardingData = new ArrayList<>();

            if (shardIndex<0){
                shardingData = storageRespCreateReqDTOS;
            }else {
                shardingData = StringUtil.getShardingData(storageRespCreateReqDTOS, shardTotal, shardIndex);
            }
            // 对分片数据进行业务处理
            for (StorageRespCreateReqDTO item : shardingData) {
                // 模拟业务逻辑处理
                log.info("Processing item: " + item.getName());
            }
            if (storageCount == 0) {
                storageInfoApi.adds(shardingData);
            } else {

                List<StorageRespCreateReqDTO> storage = dtos.stream()
                        .filter(item -> !storageRespCreateReqDTOS.stream().map(StorageRespCreateReqDTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid()))
                        .collect(Collectors.toList());
                final Long alertId = alarmConfigApi.getMaxAlertId().getData();
                shardingData.forEach(item -> {
                    int i =1;
                    for (StorageRespCreateReqDTO dto:dtos) {
                        //启用状态变更后发送告警
                        if(dto.getUuid().equals(item.getUuid()) && !dto.getStatus().equals(item.getStatus())){
                            AlarmDorisReqDTO alarm = new AlarmDorisReqDTO();
                            JSONObject tags = new JSONObject();
                            alarm.setId(alertId+i);
                            tags.put("app","storage");
                            tags.put("monitorId",item.getUuid());
                            tags.put("monitorName",item.getName());
                            alarm.setPlatformId(item.getPlatformId());
                            alarm.setResourceType(0);
                            alarm.setStatus(0);
                            alarm.setIsSolved(0);
                            alarm.setGmtCreate(new Date());
                            alarm.setFirstAlarmTime(DateUtil.current());
                            alarm.setGmtUpdate(new Date());
                            alarm.setLastAlarmTime(DateUtil.current());
                            alarm.setPlatformName(item.getPlatformName());
                            alarm.setTimes(1);
                            alarm.setMonitorId(item.getUuid());
                            alarm.setContent("云存储:"+item.getName()+" 的启用状态由\""+ StateConverter.stateToCh(dto.getState())+"\"转换为\""+StateConverter.stateToCh(item.getState())+"\"");
                            alarm.setTarget("storage.state.changed");
                            alarm.setApp("storage");
                            alarm.setMonitorName(item.getName());
                            alarm.setPriority(1);
                            alarm.setAlarmId(0L);
                            alarmDorisReqDTO.add(alarm);
                        }
                    }
                    i++;
                });
                if (alarmDorisReqDTO.size() > 0) {
                    Map<String,List> addMap = new HashMap<>();
                    addMap.put("updateList",new ArrayList<>());
                    addMap.put("insertList",alarmDorisReqDTO);
                    alarmConfigApi.createAlarmToDoris(addMap);
                }
                // 软删除主存储历史数据
                if (storage.size()>0){
                    storage.forEach(item-> item.setDeleted(1));
                    storageInfoApi.updates(storage);
                }
                storageInfoApi.updates(storageRespCreateReqDTOS);
                List<StorageRespCreateReqDTO> collect = shardingData.stream()
                        .filter(item -> !dtos.stream().map(StorageRespCreateReqDTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid()))
                        .collect(Collectors.toList());
                if (collect.size() > 0) {
                    collect = collect.stream()
                            .distinct()
                            .collect(Collectors.toList());
                    storageInfoApi.adds(collect);
                }
            }
        }
    }
    @XxlJob("collectStorageToInflux")
    public void collectToInflux(){

        List<PlatformconfigDTO>  platformconfigDTOList = new ArrayList<>();
        if (platformRedisDAO.get("platform") == null) {
            platformconfigDTOList = platformconfigApi.getPlatList().getData();
            platformRedisDAO.set("platform",platformconfigDTOList);
        }
        platformconfigDTOList = platformRedisDAO.get("platform");

//        CommonResult<List<PlatformconfigDTO>> listCommonResult = platformconfigApi.getPlatList();
//        if (listCommonResult.getData().size() > 0) {
//            for (PlatformconfigDTO p : listCommonResult.getData()) {
        if (platformconfigDTOList.size() > 0) {
            for (PlatformconfigDTO p : platformconfigDTOList) {
                if (p.getTypeCode().equals("sangFor")) {
                    JSONObject tokenInfo = SangForPwEncryptor.sangForlogin(p);
                    JSONArray storages = iSangForDeviceService.getStorages(p,tokenInfo);
                    BatchPoints batchPoints = BatchPoints.builder().build();
                    if (storages.size()>0){
                        for (int j = 0; j < storages.size(); j++) {
                            JSONObject jsonObject = storages.getJSONObject(j);
                            JSONArray usageInfo = iSangForDeviceService.getStoragesUsageInPercent(p,tokenInfo,jsonObject.getString("id"));
                            String uuid = jsonObject.getString("id");
                            String name = jsonObject.getString("name");
                            for (Object item:usageInfo) {
                                JSONObject usageItem = JSONObject.parseObject(JSONUtil.toJsonStr(item));
                                Point point = Point.measurement("zj_cloud_storage")
                                        .tag("uuid", uuid)
                                        .tag("metricName", "UsedCapacityInPercent")
                                        .addField("productsName",name)
                                        .tag("regionId", StringUtil.toString(p.getRegionId()))
                                        .addField("regionName",StringUtil.toString(p.getRegionName()))
                                        .tag("storage_type", "UsedCapacityInPercent")
                                        .tag("platformId",StringUtil.toString(p.getId()))
                                        .addField("platformName",StringUtil.toString(p.getName()))
                                        .addField("storage_metricName", "UsedCapacityInPercent")
                                        .addField("type", "UsedCapacityInPercent")
                                        .addField("value", Convert.toBigDecimal(usageItem.get("val")))
                                        .time(StringUtil.toLong(usageItem.get("time")), TimeUnit.SECONDS)
                                        .build();
                                batchPoints.point(point);
                            }
                        }
                    }else {
                        storages = iSangForDeviceService.getStorageClouds(p,tokenInfo);
                        for (int j = 0; j < storages.size(); j++) {
                            JSONObject jsonObject = storages.getJSONObject(j);
                            String uuid = jsonObject.getString("id");
                            String name = jsonObject.getString("name");
                            BigDecimal total = jsonObject.getBigDecimal("total");
                            BigDecimal free = jsonObject.getBigDecimal("free");
                            BigDecimal occupy = total.subtract(free);
                            BigDecimal val = occupy.divide(total, 4, RoundingMode.HALF_UP)
                                    .multiply(new BigDecimal("100"))
                                    .setScale(2, RoundingMode.HALF_UP);
                            Point point = Point.measurement("zj_cloud_storage")
                                        .tag("uuid", uuid)
                                        .tag("metricName", "UsedCapacityInPercent")
                                        .addField("productsName",name)
                                        .tag("regionId", StringUtil.toString(p.getRegionId()))
                                        .addField("regionName",StringUtil.toString(p.getRegionName()))
                                        .tag("storage_type", "UsedCapacityInPercent")
                                        .tag("platformId",StringUtil.toString(p.getId()))
                                        .addField("platformName",StringUtil.toString(p.getName()))
                                        .addField("storage_metricName", "UsedCapacityInPercent")
                                        .addField("type", "UsedCapacityInPercent")
                                        .addField("value", val)
                                        .time(new Date().getTime() / 1000, TimeUnit.SECONDS)
                                        .build();
                                batchPoints.point(point);
                        }
                    }
                    int shardIndex = XxlJobHelper.getShardIndex();
                    int shardTotal = XxlJobHelper.getShardTotal();
                    log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
                    List<Point> s = StringUtil.getShardingData(batchPoints.getPoints(), shardTotal, shardIndex);
                    influxDBTemplate.writeBatch(BatchPoints.builder().points(s).build());
                }
            }
        }
    }
}
