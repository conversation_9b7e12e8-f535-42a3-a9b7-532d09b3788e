package cn.iocoder.zj.module.collection.util;
public class PowerStateUtil {
    public static String powerStateConvert(String state) {
        return  switch (state) {
            case "Created" -> "on";
            case "Starting" -> "on";
            case "Running" -> "on";
            case "Stopping" -> "off";
            case "Stopped" -> "off";
            case "Unknown" -> "unknown";
            case "Rebooting" -> "on";
            case "Destroyed" -> "off";
            case "Destroying" -> "off";
            case "Migrating" -> "on";
            case "Expunging" -> "off";
            case "Paunging" -> "off";
            case "Paused" -> "off";
            case "Resuming" -> "on";
            case "VolumeMigrating" -> "on";
            default -> "unknown";
        };
    }
}
