package cn.iocoder.zj.module.collection.api.volume;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.collection.api.VolumeApi;
import cn.iocoder.zj.module.collection.service.volumes.ZstackVolumesService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.util.List;
import java.util.Map;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class VolumeApiImpl implements VolumeApi {
    @Resource
    ZstackVolumesService zstackVolumesService;
    @Override
    public CommonResult<Map<String,String>> mountVolumeToHost(String volumeUuid, String hostUuid, Long platformId) {
        return zstackVolumesService.mountVolumeToHost(volumeUuid,hostUuid,platformId);
    }

    @Override
    public CommonResult<Boolean> mountVolumeToHardware(String volumeUuid, String hardwareUuid, Long platformId, String mountPath) {
        zstackVolumesService.mountVolumeToHardware(volumeUuid,hardwareUuid,platformId,mountPath);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Map<String,String>> uninstallVolumeFromHost(String volumeUuid, String hostUuid, Long platformId) {
        return  zstackVolumesService.uninstallVolumeFromHost(volumeUuid,hostUuid,platformId);
    }

    @Override
    public CommonResult<Boolean> uninstallVolumeFromHardware(String volumeUuid, String hardwareUuid, Long platformId) {
        zstackVolumesService.uninstallVolumeFromHardware(volumeUuid,hardwareUuid,platformId);
        return CommonResult.success(true);
    }
}
