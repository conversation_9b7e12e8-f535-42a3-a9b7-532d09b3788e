package cn.iocoder.zj.module.collection.job.asset;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import cn.iocoder.zj.module.collection.dal.dataobject.zstack.ZstackLoginInfo;
import cn.iocoder.zj.module.collection.framework.influx.config.InfluxDBTemplate;
import cn.iocoder.zj.module.collection.service.zstack.core.ZstackApiConstant;
import cn.iocoder.zj.module.collection.util.Sha256;
import cn.iocoder.zj.module.collection.util.StringUtil;
import cn.iocoder.zj.module.monitor.api.asset.GatherAssetApi;
import cn.iocoder.zj.module.monitor.api.asset.dto.GatherAssetRespDTO;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName : AssetJob  //类名
 * @Description : 资产定时任务  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/9/4  9:31
 */

@Component
@Slf4j
public class AssetJob {
    @Resource
    GatherAssetApi gatherAssetApi;
    @Resource
    InfluxDBTemplate influxDBTemplate;
    @XxlJob("asset")
    public void asset() {
        //1.选择一个库
        BatchPoints batchPoints = BatchPoints.builder().build();
        List<GatherAssetRespDTO> gatherAssetRespDTOS = gatherAssetApi.getAssetList().getData();
        gatherAssetRespDTOS.forEach(GatherAssetRespDTO -> {
            double d = 0.00;
            if (GatherAssetRespDTO.getDiskUsage()!=null){
                d = GatherAssetRespDTO.getDiskUsage();
            }

            Point point = Point.measurement("zj_asset")
                    .tag("uuid",GatherAssetRespDTO.getUuid())
                    .tag("productsName",GatherAssetRespDTO.getIp())
                    .tag("platformId",GatherAssetRespDTO.getPlatformId())
                    .tag("platformName",GatherAssetRespDTO.getPlatformName())
                    .addField("cpu_value",GatherAssetRespDTO.getCpuUsage())
                    .addField("memory_value",GatherAssetRespDTO.getMemoryUsage())
                    .addField("disk_value",d)
                    .addField("timestmp",DateUtil.currentSeconds())
                    .time(DateUtil.currentSeconds(), TimeUnit.SECONDS).build();
            batchPoints.point(point);
        });
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
        List<Point> s = StringUtil.getShardingData(batchPoints.getPoints(), shardTotal, shardIndex);

        influxDBTemplate.writeBatch(BatchPoints.builder().points(s).build());
    }
}
