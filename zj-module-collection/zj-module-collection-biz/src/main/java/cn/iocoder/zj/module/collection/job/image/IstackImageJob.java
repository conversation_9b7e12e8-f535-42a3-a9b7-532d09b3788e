package cn.iocoder.zj.module.collection.job.image;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.module.collection.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.collection.service.cloud.IStackDeviceService;
import cn.iocoder.zj.module.monitor.api.imageinfo.ImageInfoApi;
import cn.iocoder.zj.module.monitor.api.imageinfo.dto.ImageInfoCreateReqDTO;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * iStack 镜像同步任务
 */
@Component
@Slf4j
public class IstackImageJob {
    
    @Resource
    private PlatformRedisDAO platformRedisDAO;
    
    @Resource
    private IStackDeviceService iStackDeviceService;
    
    @Resource
    private ImageInfoApi imageInfoApi;

    @XxlJob("istackImageJob")
    public void istackImageJob() {
        log.info("[istackImageJob][开始执行 iStack 镜像同步任务]");
        
        // 1. 获取平台配置
        List<PlatformconfigDTO> platformconfigDTOList = platformRedisDAO.get("platform");
        if (ObjectUtil.isEmpty(platformconfigDTOList)) {
            log.warn("[istackImageJob][未获取到平台配置]");
            return;
        }

        // 2. 过滤出 istack 平台
        List<PlatformconfigDTO> filteredList = platformconfigDTOList.stream()
                .filter(dto -> "istack".equals(dto.getTypeCode()))
                .toList();
                
        if (filteredList.isEmpty()) {
            log.info("[istackImageJob][未找到 istack 平台配置]");
            return;
        }

        // 3. 遍历平台获取镜像信息
        List<ImageInfoCreateReqDTO> imageInfoDTOs = new ArrayList<>();
        for (PlatformconfigDTO platform : filteredList) {
            try {
                log.info("[istackImageJob][开始获取平台({})的镜像列表]", platform.getName());
                
                // 获取镜像列表
                JSONArray array = iStackDeviceService.getImageList(platform);
                if (array == null || array.isEmpty()) {
                    log.info("[istackImageJob][平台({})未获取到镜像信息]", platform.getName());
                    continue;
                }

                // 处理镜像信息
                for (Object o : array) {
                    JSONObject jsonObject = (JSONObject) o;
                    ImageInfoCreateReqDTO imageInfo = convertToImageInfo(jsonObject, platform);
                    imageInfoDTOs.add(imageInfo);
                }
                
                log.info("[istackImageJob][平台({})获取到{}个镜像]", platform.getName(), array.size());
                
            } catch (Exception e) {
                log.error("[istackImageJob][处理平台({})镜像异常]", platform.getName(), e);
            }
        }

        // 4. 保存镜像信息
        if (!imageInfoDTOs.isEmpty()){
            List<ImageInfoCreateReqDTO> imageInfoDTOList = imageInfoApi.getAllImagesByTypeCode("istack").getData();
            if (imageInfoDTOList == null || imageInfoDTOList.isEmpty()) {
                imageInfoApi.batchCreateImageInfo(imageInfoDTOs);
            }else {
                //比较uuid不存在删除
                List<ImageInfoCreateReqDTO> deleteTarget = imageInfoDTOList.stream()
                        .filter(imageInfoDTO -> imageInfoDTOs.stream()
                                .noneMatch(item -> item.getUuid().equals(imageInfoDTO.getUuid())))
                        .collect(Collectors.toList());
                //新增镜像信息
                List<ImageInfoCreateReqDTO> collect = imageInfoDTOs.stream()
                        .filter(imageInfoDTO -> imageInfoDTOList.stream()
                                .noneMatch(item -> item.getUuid().equals(imageInfoDTO.getUuid())))
                        .collect(Collectors.toList());
                //更新镜像信息
                List<ImageInfoCreateReqDTO> updateDTOs = imageInfoDTOs.stream()
                        .filter(imageInfoDTO -> imageInfoDTOList.stream()
                                .anyMatch(item -> item.getUuid().equals(imageInfoDTO.getUuid())))
                        .toList();
                //去掉uuid不存在的collect
                if (!collect.isEmpty()){
                    imageInfoApi.batchCreateImageInfo(collect);
                }
                if (!updateDTOs.isEmpty()){
                    imageInfoApi.batchUpdateImageInfo(updateDTOs);
                }
                if (!deleteTarget.isEmpty()) {
                    imageInfoApi.batchDeleteImageInfo(deleteTarget);
                }
            }
        }
        
        log.info("[istackImageJob][iStack 镜像同步任务执行完成]");
    }

    /**
     * 转换镜像信息
     */
    private ImageInfoCreateReqDTO convertToImageInfo(JSONObject jsonObject, PlatformconfigDTO platform) {
        ImageInfoCreateReqDTO imageInfo = new ImageInfoCreateReqDTO();
        imageInfo.setPlatformId(platform.getId());
        imageInfo.setPlatformName(platform.getName());
        imageInfo.setUuid(jsonObject.getString("id"));
        imageInfo.setName(jsonObject.getString("name"));
        imageInfo.setStatus(jsonObject.getString("status").equals("active") ? "Enabled" : "Disabled");
        imageInfo.setFormat("qcow2");
        imageInfo.setCpuArch(jsonObject.getString("arch"));
        imageInfo.setOsType(jsonObject.getString("os_type"));
        // 转换大小为字节（GB to Bytes）
        Long size = jsonObject.getLong("size") != null ?
                jsonObject.getLong("size") * 1024 * 1024 * 1024 : 0L;
        imageInfo.setSize(size);
        imageInfo.setImageType("RootVolumeTemplate");
        String visibility = jsonObject.getString("visibility");
        if ("shared".equals(visibility) || "public".equals(visibility)) {
            imageInfo.setSharingScope("共享");
        }else {
            imageInfo.setSharingScope("不共享");
        }
        LocalDateTime createDate = jsonObject.getDate("created_at") != null ?
                jsonObject.getDate("created_at").toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
        imageInfo.setVCreateDate(createDate);
        imageInfo.setVUpdateDate(createDate);
        imageInfo.setTag("");
        imageInfo.setOsLanguage("");
        imageInfo.setMinMemory(null);
        BigDecimal minDisk = jsonObject.getLong("min_disk") != null ?
                BigDecimal.valueOf(jsonObject.getLong("min_disk") * 1024 * 1024 * 1024) : null;
        imageInfo.setMinDisk(minDisk);
        imageInfo.setDiskDriver("");
        imageInfo.setNetworkDriver("");
        imageInfo.setBootMode("");
        imageInfo.setRemoteProtocol("");
        imageInfo.setApplicationPlatform(jsonObject.getString("os_type"));
        imageInfo.setDeleted(0L);
        return imageInfo;
    }
}
