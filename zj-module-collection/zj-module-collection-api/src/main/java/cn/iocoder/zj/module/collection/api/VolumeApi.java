package cn.iocoder.zj.module.collection.api;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.collection.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;


@FeignClient(name = ApiConstants.NAME) // ① @FeignClient 注解
@Tag(name = "RPC 服务 - 云盘相关") // ② Swagger 接口文档
public interface VolumeApi {

    String PREFIX = ApiConstants.PREFIX + "/zstack";

    //云盘
    @PostMapping(PREFIX + "/mountVolumeToHost") // ③ Spring MVC 接口注解
    @Operation(summary = "挂载云盘到云主机")
    CommonResult<Map<String,String>> mountVolumeToHost(@RequestParam("volumeUuid")String volumeUuid,
                                                       @RequestParam("hostUuid")String hostUuid,
                                                       @RequestParam("platformId")Long platformId);

    @PostMapping(PREFIX + "/mountVolumeToHardware") // ③ Spring MVC 接口注解
    @Operation(summary = "挂载云盘到宿主机")
    CommonResult<Boolean> mountVolumeToHardware(@RequestParam("volumeUuid")String volumeUuid,
                                                @RequestParam("hardwareUuid")String hardwareUuid,
                                                @RequestParam("platformId")Long platformId,
                                                @RequestParam("mountPath")String mountPath);

    @PostMapping(PREFIX + "/uninstallVolumeFromHost") // ③ Spring MVC 接口注解
    @Operation(summary = "从云主机卸载云盘")
    CommonResult<Map<String,String>> uninstallVolumeFromHost(@RequestParam("volumeUuid")String volumeUuid,
                                                  @RequestParam("hostUuid")String hostUuid,
                                                  @RequestParam("platformId")Long platformId);

    @PostMapping(PREFIX + "/uninstallVolumeFromHardware") // ③ Spring MVC 接口注解
    @Operation(summary = "从宿主机卸载云盘")
    CommonResult<Boolean> uninstallVolumeFromHardware(@RequestParam("volumeUuid")String volumeUuid,
                                                      @RequestParam("hardwareUuid")String hardwareUuid,
                                                      @RequestParam("platformId")Long platformId);
}
