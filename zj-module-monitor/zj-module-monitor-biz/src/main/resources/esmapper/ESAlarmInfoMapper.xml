<?xml version="1.0" encoding="UTF-8"?>

<properties>
    <!--获取告警分页信息与条件查询——按名称、资源类型、告警等级模糊查询-->
<!--    <property name="alarmInfo">-->
<!--        <![CDATA[-->
<!--          {-->
<!--  "query": {-->
<!--     "bool": {-->
<!--      "filter": [-->
<!--       {-->
<!--        "match_all": {}-->
<!--        }-->
<!--       #if($sourceType.size() > 0)-->
<!--       ,-->
<!--        {-->
<!--          "terms": {-->
<!--            "sourceType.keyword":#[sourceType]-->
<!--          }-->
<!--        }-->
<!--        #end-->
<!--        #if($platformConfigId)-->
<!--       ,-->
<!--        {-->
<!--          "term": {-->
<!--            "platformConfigId": #[platformConfigId]-->
<!--          }-->
<!--        }-->
<!--        #end-->
<!--        #if($platformConfigId)-->
<!--       ,-->
<!--        {-->
<!--          "term": {-->
<!--            "platformConfigId": #[platformConfigId]-->
<!--          }-->
<!--        }-->
<!--        #end-->
<!--        #if($alarmLevel.size() > 0)-->
<!--       ,-->
<!--        {-->
<!--          "terms": {-->
<!--            "alarmLevel": #[alarmLevel]-->
<!--          }-->
<!--        }-->
<!--        #end-->
<!--       ,-->
<!--         {-->
<!--          "wildcard": {-->
<!--            "context.keyword": {-->
<!--              "wildcard": #[context],-->
<!--              "case_insensitive": true,-->
<!--              "boost": 1-->
<!--            }-->
<!--          }-->
<!--        }-->
<!--      ]-->
<!--    }-->
<!--  },-->
<!--    "size": 0,-->
<!--    "aggs": {-->
<!--    "count": {-->
<!--      "cardinality": {-->
<!--        "field": "id"-->
<!--      }-->
<!--    },-->
<!--    "group_by_id": {-->
<!--      "terms": {-->
<!--        "script": "doc['id'].value+'#split#'+ doc['uuid.keyword'].value",-->
<!--        "size":100000-->
<!--      },-->
<!--      "aggs": {-->
<!--        "my_top_hits": {-->
<!--          "top_hits": {-->
<!--            "sort": [-->
<!--              {-->
<!--                "createTime": {-->
<!--                  "order": "desc"-->
<!--                }-->
<!--              }-->
<!--            ],-->
<!--            "_source": {-->
<!--              "includes": [-->
<!--                "createTime",-->
<!--                "alarmLevel",-->
<!--                "context",-->
<!--                "sourceName",-->
<!--                "sourceType",-->
<!--                "regionId",-->
<!--                "regionName",-->
<!--                "id",-->
<!--                "uuid",-->
<!--                "platformName",-->
<!--                "platformConfigId",-->
<!--                "actualContext",-->
<!--                "productsName"-->
<!--              ]-->
<!--            },-->
<!--            "size": 1-->
<!--          }-->
<!--        },-->
<!--        "r_bucket_sort": {-->
<!--          "bucket_sort": {-->
<!--            "from": #[pageNo],-->
<!--            "size": #[pageSize]-->
<!--          }-->
<!--        }-->
<!--      }-->
<!--    }-->
<!--  }-->

<!--}-->
<!-- ]]></property>-->

<!--按地区获取告警信息-->
    <property name="getAlarmByArea">
        <![CDATA[
          {
  "query": {
    "wildcard": {
      "regionId": {
        "wildcard":  #[regionId],
        "boost": 1
      }
    }
  },
  "size": 0,
  "aggs": {
    "regionId": {
      "terms": {
        "field": "context.keyword"
      }
    }
  }
}
 ]]></property>

    <!--按租户平台获取告警数量最多的告警信息信息-->
<property name="getAlarmByPlatform">
    <![CDATA[
{
    "query": {
        "bool": {
            "filter": [
        {
        "match_all": {}
        },
        #if($platformConfigId)
        {
          "term": {
            "platformConfigId": #[platformConfigId]
          }
        },
        #end
        #if($tenantId)
        {
          "term": {
            "tenantId": #[tenantId]
          }
        },
        #end
        {
          "range": {
            "createTime": {
              "from": "now-7d",
              "to": "now"
            }
          }
        }
      ]
        }
    },
  "size": 0,
  "aggs": {
    "context": {
      "terms":{
	      "script": "doc['context.keyword'].value + '#' + doc['uuid.keyword'].value + '#' +doc['alarmLevel'].value+ '#' +doc['platformConfigId'].value"
	    }
    }
  }
}
 ]]></property>
<!--按平台和租户id获取告警信息数量-->
<property name="getAlarmNum">
<![CDATA[
{
	"query": {
		"bool": {
			"filter": [
        {
        "match_all": {}
        },
        #if($platformConfigId)
        {
          "term": {
            "platformConfigId": #[platformConfigId]
          }
        },
        #end
        #if($tenantId)
        {
          "term": {
            "tenantId": #[tenantId]
          }
        },
        #end
        {
          "term": {
            "alarmLevel": #[alarmLevel]
          }
        },
        {
          "range": {
            "createTime": {
              "from": "now-7d",
              "to": "now"
            }
          }
        }
      ]
		}
	},
  "size": 0,
  "aggs": {
    "alarmLevel": {
      "terms": {
        "field": "alarmLevel"
      }
    }
  }
}
 ]]></property>

    <!--按租户id获取地区告警信息数量-->
    <property name="getAlarmNumByArea">
        <![CDATA[
{
	"query": {
		"bool": {
			"filter": [
        {
        "match_all": {}
        },
        #if($tenantId)
        {
          "term": {
            "tenantId": #[tenantId]
          }
        },
        #end
        {
          "term": {
            "parentRegionId": #[parentRegionId]
          }
        },
        {
          "terms": {
            "alarmLevel": [2,3]
          }
        },
        {
          "range": {
            "createTime": {
              "from": "now-7d",
              "to": "now"
            }
          }
        }
      ]
		}
	},
  "size": 0,
  "aggs": {
    "parentRegionId": {
      "terms": {
        "field": "parentRegionId"
      }
    }
  }
}
 ]]></property>

    <!--按地区id获取下级地区告警信息数量及平台列表-->
    <property name="getPlatformByArea">
        <![CDATA[
{
	"query": {
		"bool": {
			"filter": [
        {
        "match_all": {}
        },
        {
          "terms": {
            "platformConfigId": #[platformConfigIds]
          }
        },
        #if($tenantId)
        {
          "term": {
            "tenantId": #[tenantId]
          }
        },
        #end
        {
          "terms": {
            "alarmLevel": [2,3]
          }
        },
        {
          "range": {
            "createTime": {
              "from": "now-7d",
              "to": "now"
            }
          }
        }
      ]
		}
	},
  "size": 0,
  "aggs": {
    "parentRegionId": {
      "terms": {
        "field": "parentRegionId"
      }
    }
  }
}
 ]]></property>

    <property name="alarmHostPage">
        <![CDATA[
{
    "query": {
     "bool": {
      "filter": [
       {
        "match_all": {}
        },
        {
          "term": {
            "id": #[id]
          }
        },
        {
          "wildcard": {
            "productsName.keyword": #[productsName]
          }
        }
      ]
    }
  },
  "from": 0,
  "size": 0,
  "aggs": {
  "total": {
      "cardinality": {
        "field": "uuid.keyword"
      }
    },
    "alarmHost": {
      "terms": {
        "script": "doc['uuid.keyword'].value",
        "size":100000
      },
      "aggs": {
        "my_top_hits": {
          "top_hits": {
            "sort": [
              {
                "createTime": {
                  "order": "desc"
                }
              }
            ],
            "_source": {
              "includes": [
                "uuid",
                "productsName",
                "alarmLevel",
                "context",
                "sourceName",
                "sourceType",
                "regionId",
                "regionName",
                "createTime",
                "actualContext",
                "id"
              ]
            },
            "size": 1
          }
        },
        "r_bucket_sort": {
          "bucket_sort": {
            "from": #[pageNo],
            "size": #[pageSize]
          }
        }
      }
    }
  }
}
 ]]></property>
    <property name="alarmBaseInfo">
        <![CDATA[
 {
  "query": {
    "bool": {
      "filter": [
        {
          "term": {
            "id": #[id]
          }
        }
      ]
    }
  },
  "size": 0,
  "aggs": {
    "alarmConfig": {
      "terms": {
        "script": "doc['id'].value",
        "size":100000
      },
      "aggs": {
        "my_top_hits": {
          "top_hits": {
            "sort": [
              {
                "createTime": {
                  "order": "desc"
                }
              }
            ],
            "_source": {
              "includes": [
                "alarmLevel",
                "context",
                "sourceName",
                "sourceType",
                "regionId",
                "regionName",
                "id"
              ]
            },
            "size": 1
          }
        }
      }
    }
  }
}
 ]]></property>
    <property name="getAlarmByType">
    <![CDATA[
    {
  "track_total_hits": true,
  "query": {
    "bool": {
      "filter": [
        {
          "range": {
            "createTime": {
              "from": #[reckonTime],
              "to": "now"
            }
          }
        },
        {
          "term": {
            "sourceType.keyword":"monitor_alarm_host"
          }
        }
       #if($platformIds.size() > 0)
       ,
        {
          "terms": {
            "platformConfigId": #[platformIds]
          }
        }
        #end
        #if($uuids.size() > 0)
        ,
        {
          "terms": {
            "uuid.keyword": #[uuids]
          }
        }
        #end
        ,
        {
          "wildcard": {
            "metricName.keyword": {
              "value": #[type]
            }
          }
        }
      ]
    }
  }
}
    ]]>
    </property>
    <property name="alarmInfo">
        <![CDATA[
{
  "query": {
    "bool": {
      "filter": [
        {
          "match_all": {}
        }
      ]
    }
  },
  "size": 0,
  "aggs": {
    "count": {
      "cardinality": {
        "field": "id"
      }
    },
    "group_by_id": {
      "terms": {
        "script": "doc['id'].value+'#split#'+ doc['uuid.keyword'].value",
        "size": 100000
      },
      "aggs": {
        "my_top_hits": {
          "top_hits": {
            "sort": [
              {
                "createTime": {
                  "order": "desc"
                }
              }
            ],
            "_source": {
              "includes": [
                "createTime",
                "alarmLevel",
                "context",
                "sourceName",
                "sourceType",
                "regionId",
                "regionName",
                "id",
                "uuid",
                "platformName",
                "platformConfigId",
                "actualContext",
                "productsName"
              ]
            },
            "size": 1
          }
        }
      }
    }
  }
}
 ]]></property>
</properties>