<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.monitor.dal.mysql.imageinfo.ImageInfoMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="getAllImagesByTypeCode" resultType="cn.iocoder.zj.module.monitor.dal.dataobject.imageinfo.ImageInfoDO">
        select * from monitor_image_info
        where platform_id IN (
            SELECT
                id
            FROM
                system_platform_config
            WHERE deleted = 0
              AND type_code = #{typeCode})
    </select>

    <update id="batchUpdateImageInfo">
        UPDATE monitor_image_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="name = case" suffix="end,">
                <foreach collection="ImageInfoDOs" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR}
                    then #{item.name}
                </foreach>
            </trim>
            <trim prefix="status = case" suffix="end,">
                <foreach collection="ImageInfoDOs" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR}
                    then #{item.status}
                </foreach>
            </trim>
            <trim prefix="format = case" suffix="end,">
                <foreach collection="ImageInfoDOs" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR}
                    then #{item.format}
                </foreach>
            </trim>
            <trim prefix="cpu_arch = case" suffix="end,">
                <foreach collection="ImageInfoDOs" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR}
                    then #{item.cpuArch}
                </foreach>
            </trim>
            <trim prefix="os_type = case" suffix="end,">
                <foreach collection="ImageInfoDOs" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR}
                    then #{item.osType}
                </foreach>
            </trim>
            <trim prefix="size = case" suffix="end,">
                <foreach collection="ImageInfoDOs" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR}
                    then #{item.size}
                </foreach>
            </trim>
            <trim prefix="image_type = case" suffix="end,">
                <foreach collection="ImageInfoDOs" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR}
                    then #{item.imageType}
                </foreach>
            </trim>
            <trim prefix="sharing_scope = case" suffix="end,">
                <foreach collection="ImageInfoDOs" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR}
                    then #{item.sharingScope}
                </foreach>
            </trim>
            <trim prefix="tag = case" suffix="end,">
                <foreach collection="ImageInfoDOs" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR}
                    then #{item.tag}
                </foreach>
            </trim>
            <trim prefix="host_uuid = case" suffix="end,">
                <foreach collection="ImageInfoDOs" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR}
                    then #{item.hostUuid}
                </foreach>
            </trim>
            <trim prefix="host_name = case" suffix="end,">
                <foreach collection="ImageInfoDOs" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR}
                    then #{item.hostName}
                </foreach>
            </trim>
            <trim prefix="v_create_date = case" suffix="end,">
                <foreach collection="ImageInfoDOs" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR}
                    then #{item.vCreateDate}
                </foreach>
            </trim>

            <trim prefix="v_update_date = case" suffix="end,">
                <foreach collection="ImageInfoDOs" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR}
                    then #{item.vUpdateDate}
                </foreach>
            </trim>
            <trim prefix="os_language = case" suffix="end,">
                <foreach collection="ImageInfoDOs" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR}
                    then #{item.osLanguage}
                </foreach>
            </trim>
            <trim prefix="min_memory = case" suffix="end,">
                <foreach collection="ImageInfoDOs" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR}
                    then #{item.minMemory}
                </foreach>
            </trim>
            <trim prefix="min_disk = case" suffix="end,">
                <foreach collection="ImageInfoDOs" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR}
                    then #{item.minDisk}
                </foreach>
            </trim>
            <trim prefix="disk_driver = case" suffix="end,">
                <foreach collection="ImageInfoDOs" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR}
                    then #{item.diskDriver}
                </foreach>
            </trim>
            <trim prefix="network_driver = case" suffix="end,">
                <foreach collection="ImageInfoDOs" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR}
                    then #{item.networkDriver}
                </foreach>
            </trim>
            <trim prefix="boot_mode = case" suffix="end,">
                <foreach collection="ImageInfoDOs" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR}
                    then #{item.bootMode}
                </foreach>
            </trim>
            <trim prefix="remote_protocol = case" suffix="end,">
                <foreach collection="ImageInfoDOs" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR}
                    then #{item.remoteProtocol}
                </foreach>
            </trim>
            <trim prefix="application_platform = case" suffix="end,">
                <foreach collection="ImageInfoDOs" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR}
                    then #{item.applicationPlatform}
                </foreach>
            </trim>
            <trim prefix="platform_id = case" suffix="end,">
                <foreach collection="ImageInfoDOs" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR}
                    then #{item.platformId}
                </foreach>
            </trim>
            <trim prefix="platform_name = case" suffix="end,">
                <foreach collection="ImageInfoDOs" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR}
                    then #{item.platformName}
                </foreach>
            </trim>
            <trim prefix="deleted = case" suffix="end,">
                <foreach collection="ImageInfoDOs" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR}
                    then #{item.deleted}
                </foreach>
            </trim>
        </trim>
        where uuid in
        <foreach close=")" collection="ImageInfoDOs" item="item" open="(" separator=", ">
            #{item.uuid,jdbcType=VARCHAR}
        </foreach>
    </update>
    <select id="getImageStatusCount" resultType="java.util.Map">
        SELECT
        COUNT(1) AS total,
        SUM(CASE WHEN status = 'Enabled' THEN 1 ELSE 0 END) AS available,
        SUM(CASE WHEN status != 'Enabled' THEN 1 ELSE 0 END) AS other
        FROM
        (select b.* from
        (SELECT * FROM system_platform_tenant WHERE deleted = 0 GROUP BY platform_id
        <if test="tenantIds != null and tenantIds.size>0">
            ,tenant_id
        </if>
        ) a
        LEFT JOIN
        (SELECT * FROM monitor_image_info
        WHERE deleted = 0
        GROUP BY UUID) b
        ON a.platform_id=b.platform_id
        where b.deleted = 0
        <if test="tenantIds != null and tenantIds.size>0">
            and a.deleted = 0
            and a.tenant_id in
            <foreach collection="tenantIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="platformId != null and platformId != ''">
            and b.platform_id = #{platformId}
        </if>
        group by b.uuid )c
    </select>

    <select id="getImageByList" resultType="cn.iocoder.zj.module.monitor.dal.dataobject.imageinfo.ImageInfoDO">
        SELECT
        uuid,
        NAME,
        min_memory,
        min_disk,
        platform_id,
        platform_name
        FROM
        monitor_image_info
        WHERE  deleted = 0 AND uuid in
        <foreach item="item" index="index" collection="list"  open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
