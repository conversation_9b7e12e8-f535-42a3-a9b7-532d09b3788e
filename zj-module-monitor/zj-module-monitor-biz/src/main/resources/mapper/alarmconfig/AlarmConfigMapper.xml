<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.monitor.dal.mysql.alarmconfig.AlarmConfigMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->



<select id="selectAlarmConfigPage"
            resultType="cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo.AlarmConfigRespVO">
    SELECT
    <if test="pageReqVO.uuid != null and pageReqVO.uuid != ''">
        if(ahr.alarm_id is null,'false','true') selected,
    </if>
    ac.*,ten.NAME AS tenantName
    FROM
        monitor_alarm_config ac
    LEFT JOIN system_tenant ten ON ten.id = ac.tenant_id
    <if test="pageReqVO.uuid != null and pageReqVO.uuid != ''">
        LEFT JOIN monitor_alarm_host_relation ahr
        on ahr.host_uuid = #{pageReqVO.uuid}
        and ac.id = ahr.alarm_id
    </if>
    WHERE
    ac.deleted = 0
        <if test="pageReqVO.alarmName != null and pageReqVO.alarmName != ''">
            and ac.alarm_name like concat("%",#{pageReqVO.alarmName},"%")
        </if>
        <if test="pageReqVO.context != null and pageReqVO.context != ''">
            and ac.context like concat("%",#{pageReqVO.context},"%")
        </if>
        <if test="pageReqVO.dictLabelName != null and pageReqVO.dictLabelName != ''">
            and ac.dict_label_name like concat("%",#{pageReqVO.dictLabelName},"%")
        </if>

        <if test="pageReqVO.sourceType != null and pageReqVO.sourceType != ''">
            and
            <foreach collection="pageReqVO.sourceTypeList" open="(" separator="or" close=")" item="list">
                ac.source_type = #{list}
            </foreach>
        </if>
    
        <if test="pageReqVO.alarmLevel != null and pageReqVO.alarmLevel != ''">
            and
            <foreach collection="pageReqVO.levelList" open="(" separator="or" close=")" item="list">
                ac.alarm_level = #{list}
            </foreach>
        </if>
    <if test="pageReqVO.startTime != null and pageReqVO.startTime != '' and pageReqVO.endTime != null and pageReqVO.endTime != ''">
        and DATE_FORMAT( ac.create_time,"%Y-%m-%d %H:%i:%s" )  between #{pageReqVO.startTime} and  #{pageReqVO.endTime}
    </if>
    <if test="pageReqVO.tenantList !=null and pageReqVO.tenantList.size()>0">
        and ac.tenant_id in
        <foreach collection="pageReqVO.tenantList" open="(" separator="," close=")" item="list">
            #{list}
        </foreach>
    </if>
    ORDER BY
    <if test="pageReqVO.uuid != null and pageReqVO.uuid != ''">
        selected DESC,
    </if>
    <if test="pageReqVO.sortBy == null or pageReqVO.sortBy =='' or pageReqVO.sortDirection== null or pageReqVO.sortDirection ==''">
        ac.update_time DESC
    </if>
    <if test="pageReqVO.sortBy != null and pageReqVO.sortBy !='' and pageReqVO.sortDirection!= null and pageReqVO.sortDirection !=''">
        ac.${pageReqVO.sortBy} ${pageReqVO.sortDirection}
    </if>
    </select>

<select id="getAlarmAddedList" resultType="cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo.AlarmHostListPageResp">
    SELECT
        DISTINCT (relation.id) relationId,
        sourceInfo.uuid hostUuid,
        sourceInfo.name hostName,
        sourceInfo.platform_id,
        sourceInfo.platform_name,
        relation.tenant_id,
        relation.tenant_name,
        relation.status,
<!--    该语句查询所有已关联告警配置的主机，所以关联状态定为1-->
        1 relationState
    FROM
    <if test="sourceType == 'monitor_alarm_host'">
        monitor_host_info sourceInfo
    </if>
    <if test="sourceType == 'monitor_alarm_hardware'">
        monitor_hardware_info sourceInfo
    </if>
    <if test="sourceType == 'monitor_alarm_disk'">
        monitor_storage_info sourceInfo
    </if>
    LEFT JOIN
        monitor_alarm_host_relation relation
    ON
        relation.host_uuid = sourceInfo.uuid
    WHERE
        sourceInfo.deleted = 0
      AND
        relation.id IS NOT NULL
      AND
        relation.deleted = 0
      AND
        relation.alarm_id = #{reqVO.alarmId}
    <if test="reqVO.hostName != '' and reqVO.hostName != null">
      AND sourceInfo.name LIKE CONCAT("%",#{reqVO.hostName},"%")
    </if>
    AND sourceInfo.platform_id in
    <foreach collection="platformIdList" separator="," open="(" close=")" item="item">
        #{item}
    </foreach>

    ORDER BY
        relation.create_time,relation.id
    </select>

<select id="getAlarmNoticeByConfigId"
            resultType="cn.iocoder.zj.module.monitor.controller.admin.alarmnotice.vo.AlarmNoticeBaseVO">
            select * from monitor_alarm_notice notice
            where alarm_config_id = #{configId}
    </select>

    <insert id="createAlarmRecord">
        insert into monitor_alarm_record
        (region_id,region_name,actual_context,
        context,products_name,
        platform_name,source_type,source_name,
        uuid,alarm_id,alarm_level,alarm_num,
        platform_id,create_time,update_time)
        values
        <if test="alarmInfoRespVoList.size>0">
            <foreach collection="alarmInfoRespVoList" item="alarmInfo" separator=",">
                (#{alarmInfo.regionId},#{alarmInfo.regionName},
                #{alarmInfo.actualContext},#{alarmInfo.context},#{alarmInfo.productsName},
                #{alarmInfo.platformName},#{alarmInfo.sourceType},#{alarmInfo.sourceName},
                #{alarmInfo.uuid},#{alarmInfo.id},#{alarmInfo.alarmLevel},
                #{alarmInfo.alarmNum},#{alarmInfo.platformConfigId},
                STR_TO_DATE(#{alarmInfo.createTime}, '%Y-%m-%d %H:%i:%s.%f'),
                STR_TO_DATE(#{alarmInfo.createTime}, '%Y-%m-%d %H:%i:%s.%f'))
            </foreach>
        </if>
    </insert>

    <select id="getAllAlarmRecord"
            resultType="cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo.AlarmInfoRespVo">
        select alarm_id as id,uuid,alarm_num,is_solved
        from  monitor_alarm_record
    </select>

    <select id="getSilentTarget"
            resultType="cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO">
        SELECT * from hzb_alert WHERE #{timestamp}-first_alarm_time &lt; (#{converge.evalInterval}*1000) and resource_type = 0
    </select>

    <update id="updateAlarmRecord">
        <foreach collection="updateList" item="item" separator=";">
            update monitor_alarm_record
            set alarm_num = #{item.alarmNum},
            actual_context = #{item.actualContext},
            update_time = now(),
            create_time = #{item.createTime},
            is_solved = #{item.isSolved}
            where uuid = #{item.uuid}
            and alarm_id = #{item.id}
        </foreach>
    </update>

    <update id="solvedAlarm">
        update monitor_alarm_record
        set   is_solved = 2
        where id = #{alarmId}
    </update>

    <update id="alarmWorkOrder">
        update monitor_alarm_record
        set   is_solved = 1
        where id = #{alarmId}
    </update>

    <update id="cleanWorkOrder">
        update monitor_alarm_record
        set   is_solved = 0
        where id = #{alarmId}
    </update>
    <update id="updateIsRead">
        UPDATE monitor_alarm_record
        SET is_read = 1
        WHERE id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>
    <update id="updateIsSolved">
        UPDATE monitor_alarm_record
        SET is_solved = 3
        WHERE id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>
    <select id="getAlarmCount" resultType="java.util.Map">
        SELECT
        SUM(CASE WHEN priority = 0 AND is_solved = 0 THEN 1 ELSE 0 END) AS level3,
        SUM(CASE WHEN priority = 1 AND is_solved = 0 THEN 1 ELSE 0 END) AS level2,
        SUM(CASE WHEN priority = 2 AND is_solved = 0 THEN 1 ELSE 0 END) AS level1,
        SUM(CASE WHEN is_solved = 2 THEN 1 ELSE 0 END) AS isSolved
        FROM
        hzb_alert
        where
        platform_id is not null and
        platform_id in
        <foreach collection="platformIdList" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        <if test="platformId != null">
                and platform_id = #{platformId}
        </if>
    </select>

    <select id="getAlarmInfoPage"
            resultType="cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo.AlarmInfoRespVo">
        select red.source_type,red.source_name,red.region_id,red.region_name,
        red.platform_name,red.products_name,red.actual_context,red.context,
        red.uuid,red.id,red.alarm_level,red.alarm_num,
        red.is_solved,red.platform_id as platformConfigId,
        red.create_time,red.is_read,info.id sourceId,
        IF(info.state = 'Destroyed',1,0) isDestroyed
        FROM
        monitor_alarm_record red
        LEFT JOIN(
        SELECT uuid,state,id FROM monitor_hardware_info
        UNION ALL
        SELECT uuid,state,id FROM monitor_host_info
        UNION ALL
        SELECT uuid,state,id FROM monitor_storage_info
        ) info on info.uuid = red.uuid
        WHERE red.platform_id in (
        SELECT platform_id from system_platform_tenant
        WHERE tenant_id in
        <foreach collection="pageReqVO.tenantId" separator="," close=")" open="(" item="item">
            #{item}
        </foreach>
        and deleted = 0
        )
        <if test="pageReqVO.sourceType != '' and pageReqVO.sourceType != null">
            and red.source_type like concat('%',#{pageReqVO.sourceType},'%')
        </if>
        <if test="pageReqVO.platformId != null">
            and red.platform_id = #{pageReqVO.platformId}
        </if>
        <if test="pageReqVO.productsName != '' and pageReqVO.productsName != null">
            and red.products_name  like concat('%',#{pageReqVO.productsName},'%')
        </if>
        <if test="pageReqVO.alarmLevel != null and pageReqVO.alarmLevel != 0">
            and red.alarm_level = #{pageReqVO.alarmLevel}
        </if>
        <if test="pageReqVO.alarmLevel != null and pageReqVO.alarmLevel == 0">
            and red.is_solved = 2
        </if>
        <if test="pageReqVO.recentlyDay != null and pageReqVO.recentlyDay > 0">
        and red.create_time >= DATE_SUB(CURDATE(), INTERVAL #{pageReqVO.recentlyDay} DAY)
        </if>
        <if test="pageReqVO.startTime != '' and pageReqVO.endtime != '' and pageReqVO.startTime != null and pageReqVO.endtime != null">
            and DATE_FORMAT( red.create_time, "%Y-%m-%d" )  between DATE_FORMAT( #{pageReqVO.startTime}, "%Y-%m-%d" ) and  DATE_FORMAT( #{pageReqVO.endTime}, "%Y-%m-%d" )
        </if>
        order by red.create_time desc
    </select>

    <select id="getAlarmInfo" resultType="cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo.AlarmInfoRespVo">
        select source_type,source_name,region_id,region_name,
        platform_name,products_name,actual_context,context,
        uuid,id,alarm_level,alarm_num,
        is_solved,platform_id as platformConfigId,
        platform_name,create_time
        from monitor_alarm_record
        where id=#{id}
    </select>

    <select id="getUnsolvedAlarms"
            resultType="cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo.AlarmInfoRespVo">
        SELECT
            source_type,
            source_name,
            region_id,
            region_name,
            platform_name,
            products_name,
            actual_context,
            context,
            uuid,
            id,
            alarm_level,
            alarm_num,
            is_solved,
            platform_id AS platformConfigId,
            platform_name,
            create_time,
            is_read
        FROM
            monitor_alarm_record
        WHERE
                platform_id IN ( SELECT platform_id FROM system_platform_tenant WHERE tenant_id = #{tenantId} AND deleted = 0 )
          AND is_solved != 2
          AND is_read=0
        ORDER BY
            is_read ASC,
            create_time DESC
            LIMIT #{limit};
    </select>

    <select id="alarmConfigMaxId" resultType="long">
        SELECT MAX(id)+1 FROM monitor_alarm_config
    </select>

    <update id="updateAlarmConfig">
        UPDATE monitor_alarm_config
        SET id = #{id},
            alarm_name = #{updateObj.alarmName},
            description = #{updateObj.description},
            source_type = #{updateObj.sourceType},
            dict_label_name = #{updateObj.dictLabelName},
            dict_label_type = #{updateObj.dictLabelType},
            dict_label_value = #{updateObj.dictLabelValue},
            alarm_rule = #{updateObj.alarmRule},
            alarm_val = #{updateObj.alarmVal},
            unit = #{updateObj.unit},
            unit_type = #{updateObj.unitType},
            alarm_time = #{updateObj.alarmTime},
            alarm_level = #{updateObj.alarmLevel},
            context = #{updateObj.context},
            create_time = #{updateObj.createTime},
            enabled = #{updateObj.enabled}
        WHERE
            id = #{updateObj.id}
    </update>
    <update id="updateAlarmHostRelation">
        UPDATE monitor_alarm_host_relation
        SET alarm_id = #{id},
            alarm_name = #{updateObj.alarmName}
        WHERE
            alarm_id = #{updateObj.id}
    </update>
    <update id="updateAlarmNotice">
        UPDATE monitor_alarm_notice
        SET alarm_config_id = #{id}
        WHERE
            alarm_config_id = #{updateObj.id}
    </update>

    <select id="getAlarmConfigByUuid"
            resultType="cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo.AlarmConfigRespVO">
        SELECT
            conf.*,
            rela.id relationId,
            rela.status status
        FROM
            monitor_alarm_config conf
                LEFT JOIN monitor_alarm_host_relation rela on conf.id = rela.alarm_id
        WHERE rela.host_uuid = #{uuid}
          and conf.deleted = 0
          and rela.deleted = 0
          and rela.tenant_id = #{tenantId}
        order by rela.create_time,rela.id
    </select>

    <select id="getPreparingAddAlarmList"
            resultType="cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo.AlarmHostListPageResp">
        SELECT
        sourceInfo.uuid hostUuid,
        sourceInfo.name hostName,
        sourceInfo.platform_id,
        sourceInfo.platform_name,
        IF(relation.id is null,0,1) relationState
        FROM
        <if test="sourceType == 'monitor_alarm_host'">
            monitor_host_info sourceInfo
        </if>
        <if test="sourceType == 'monitor_alarm_hardware'">
            monitor_hardware_info sourceInfo
        </if>
        <if test="sourceType == 'monitor_alarm_disk'">
            monitor_storage_info sourceInfo
        </if>
        LEFT JOIN monitor_alarm_host_relation relation
        on relation.host_uuid = sourceInfo.uuid
        and relation.deleted = 0
        and relation.alarm_id = #{reqVO.alarmId}
        WHERE
        sourceInfo.deleted = 0 and sourceInfo.state != "Destroyed"
        <if test="reqVO.hostName != null and reqVO.hostName !=''">
            and sourceInfo.hostName like concat("%",#{reqVO.hostName},"%")
        </if>
        <if test="reqVO.tenantId != null">
            AND sourceInfo.platform_id in (SELECT platform_id from system_platform_tenant pt WHERE pt.tenant_id in
            <foreach collection="reqVO.tenantId" separator="," close=")" open="(" item="item">
                #{item}
            </foreach>
            AND pt.deleted = 0 )
        </if>
        ORDER BY
        sourceInfo.create_time
    </select>

    <select id="getAlarmHostPage"
            resultType="cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo.AlarmHostListPageResp">
        SELECT
        DISTINCT (relation.id) relationId,
        sourceInfo.uuid hostUuid,
        sourceInfo.name hostName,
        sourceInfo.platform_id,
        sourceInfo.platform_name,
        relation.tenant_id,
        relation.tenant_name,
        relation.status
        FROM
        <if test="sourceType == 'monitor_alarm_host'">
            monitor_host_info sourceInfo
        </if>
        <if test="sourceType == 'monitor_alarm_hardware'">
            monitor_hardware_info sourceInfo
        </if>
        <if test="sourceType == 'monitor_alarm_disk'">
            monitor_storage_info sourceInfo
        </if>
        LEFT JOIN
        monitor_alarm_host_relation relation
        ON
        relation.host_uuid = sourceInfo.uuid
        WHERE
        sourceInfo.deleted = 0
        AND
        relation.id IS NOT NULL
        AND
        relation.deleted = 0
        AND
        relation.alarm_id = #{reqVO.alarmId}
        <if test="reqVO.hostName != '' and reqVO.hostName != null">
            AND sourceInfo.name LIKE CONCAT("%",#{reqVO.hostName},"%")
        </if>
        ORDER BY
        relation.create_time,relation.id
    </select>

    <select id="getUnsolvedAlarmsList"
            resultType="cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo.AlarmInfoRespVo">
        SELECT
        source_type,
        source_name,
        region_id,
        region_name,
        platform_name,
        products_name,
        actual_context,
        context,
        uuid,
        id,
        alarm_level,
        alarm_num,
        is_solved,
        platform_id AS platformConfigId,
        platform_name,
        create_time,
        is_read
        FROM
        monitor_alarm_record
        WHERE
        platform_id IN ( SELECT platform_id FROM system_platform_tenant WHERE tenant_id = #{tenantId} AND deleted = 0 )
        AND is_solved != 2
        AND is_read=0
        ORDER BY
        is_read ASC,
        create_time DESC
    </select>

    <select id="getAlarmInfoList"
            resultType="cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo.AlarmInfoRespVo">
        select red.source_type,red.source_name,red.region_id,red.region_name,
        red.platform_name,red.products_name,red.actual_context,red.context,
        red.uuid,red.id,red.alarm_level,red.alarm_num,
        red.is_solved,red.platform_id as platformConfigId,
        red.create_time,red.is_read,info.id sourceId,
        IF(info.state = 'Destroyed',1,0) isDestroyed
        FROM
        monitor_alarm_record red
        LEFT JOIN(
        SELECT uuid,state,id FROM monitor_hardware_info
        UNION ALL
        SELECT uuid,state,id FROM monitor_host_info
        UNION ALL
        SELECT uuid,state,id FROM monitor_storage_info
        ) info on info.uuid = red.uuid
        WHERE red.platform_id in (
        SELECT platform_id from system_platform_tenant
        WHERE tenant_id in
        <foreach collection="tenantId" separator="," close=")" open="(" item="item">
            #{item}
        </foreach>
        and deleted = 0
        )
        <if test="alarmInfoReqVo.sourceType != '' and alarmInfoReqVo.sourceType != null">
            and red.source_type like concat('%',#{alarmInfoReqVo.sourceType},'%')
        </if>
        <if test="alarmInfoReqVo.platformId != null">
            and red.platform_id = #{alarmInfoReqVo.platformId}
        </if>
        <if test="alarmInfoReqVo.productsName != '' and alarmInfoReqVo.productsName != null">
            and red.products_name  like concat('%',#{alarmInfoReqVo.productsName},'%')
        </if>
        <if test="alarmInfoReqVo.alarmLevel != null and alarmInfoReqVo.alarmLevel != 0">
            and red.alarm_level = #{alarmInfoReqVo.alarmLevel}
        </if>
        <if test="alarmInfoReqVo.alarmLevel != null and alarmInfoReqVo.alarmLevel == 0">
            and red.is_solved = 2
        </if>
        <if test="alarmInfoReqVo.recentlyDay != null and alarmInfoReqVo.recentlyDay > 0">
            and red.create_time >= DATE_SUB(CURDATE(), INTERVAL #{alarmInfoReqVo.recentlyDay} DAY)
        </if>
        <if test="alarmInfoReqVo.startTime != '' and alarmInfoReqVo.endtime != '' and alarmInfoReqVo.startTime != null and alarmInfoReqVo.endtime != null">
            and DATE_FORMAT( red.create_time, "%Y-%m-%d %H:%i:%s" )  between DATE_FORMAT( #{alarmInfoReqVo.startTime}, "%Y-%m-%d %H:%i:%s" ) and  DATE_FORMAT( #{alarmInfoReqVo.endTime}, "%Y-%m-%d %H:%i:%s" )
        </if>
        order by red.create_time desc
    </select>

    <select id="getCloudAlarms" resultType="java.util.Map">
        SELECT
        source_name,
        actual_context,
        alarm_level,
        platform_name,
        create_time
        FROM
        monitor_alarm_record
        WHERE
        platform_id IN
        <foreach close=")" collection="platform" item="item" open="(" separator=", ">
            #{item.platformId,jdbcType=VARCHAR}
        </foreach>
        AND is_solved != 2
        AND is_read=0
        ORDER BY
        is_read ASC,
        create_time DESC
        limit #{limit}
    </select>

    <select id="getHzAlarmsList" resultType="java.util.Map">
        SELECT platform_name,content,monitor_name as name,
        CASE
        WHEN priority = 0 THEN 0
        WHEN priority = 1 THEN 1
        WHEN priority = 2 THEN 2
        END AS priority, last_alarm_time,tags from hzb_alert  WHERE platform_id IN
        <foreach close=")" collection="platform" item="item" open="(" separator=", ">
            #{item.platformId,jdbcType=VARCHAR}
        </foreach>
        and is_solved = 0
        order by  last_alarm_time desc
        limit 200
    </select>

    <update id="updateAlarmRecordSolvedState">
        update monitor_alarm_record
        set   is_solved = #{isSolved}
        where id  = #{alertId}
    </update>

    <select id="getAvailableAlertConverge" resultType="org.apache.hertzbeat.common.entity.alerter.AlertConverge">
    select * from hzb_alert_converge where enable = #{enable} and match_all = #{machAll} order by gmt_create desc limit 1
    </select>

<update id="updateCollectorAlarm">
    update hzb_alert
    set is_solved = #{result.isSolved},last_alarm_time=#{result.lastAlarmTime},gmt_update = #{result.gmtUpdate},times = #{result.times}
    where id = #{result.id}
    </update>

<insert id="createAlarmToDoris" parameterType="java.util.List">
    insert into hzb_alert
    (id,alert_define_id,content,creator,first_alarm_time,
     gmt_create,gmt_update,last_alarm_time,modifier,priority,
     status,tags,target,times,platform_id,platform_name,
     monitor_id,resource_type,app,is_solved,alarm_id,monitor_name,is_fall_back,alarm_config_name,alarm_rule
    )
    values
    <foreach collection="list" item="item" index= "index" separator =",">
        (#{item.id},#{item.alertDefineId},#{item.content},#{item.creator},#{item.firstAlarmTime},#{item.gmtCreate},#{item.gmtUpdate},
        #{item.lastAlarmTime},#{item.modifier},#{item.priority},#{item.status},#{item.tags},#{item.target},
        #{item.times},#{item.platformId},#{item.platformName},#{item.monitorId},#{item.resourceType},#{item.app},
        #{item.isSolved},#{item.alarmId},#{item.monitorName},#{item.isFallback},#{item.alarmConfigName},#{item.alarmRule})
    </foreach>
    </insert>

<select id="getMaxAlertId" resultType="java.lang.Long">
    SELECT IFNULL(MAX( id ) ,0) as id from hzb_alert limit 1
    </select>

<delete id="deleteAlarmDoris">
    DELETE from hzb_alert WHERE id IN
        <foreach collection="newList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>

<select id="getAlarmDorisById" resultType="cn.iocoder.zj.module.monitor.dal.mysql.alarmdoriseinfo.AlarmDorisDO">
    select * from hzb_alert where  id = #{alertId}
    </select>

<insert id="createCollectorAlert">
    insert into
        hzb_collector_alert
        (collector_name,content,platform_name,platform_id,create_time,update_time,collector_id)
        value
        (#{alertMap.name},#{alertMap.content},#{alertMap.platformName},#{alertMap.platformId},#{alertMap.createTime},#{alertMap.updateTime},#{alertMap.id});
</insert>

<update id="deletedCollectorAlert">
    UPDATE hzb_collector_alert SET deleted = #{alertMap.deleted}, update_time = #{alertMap.updateTime} WHERE collector_id = #{alertMap.id}
    </update>

<select id="getCollectorAlertsByPlatform" resultType="java.util.Map">
    select * from hzb_collector_alert where deleted = 0
    and platform_id in
    <foreach collection="platformIds" separator="," close=")" item="item" open="(">
        #{item}
    </foreach>
    ORDER BY create_time DESC
    limit 5
</select>
<select id="getTowDaysGrowth" resultType="java.util.Map">
    SELECT
    SUM( CASE WHEN priority = 0 AND is_solved = 0 THEN 1 ELSE 0 END ) level3Growth,
	SUM( CASE WHEN priority = 1 AND is_solved = 0 THEN 1 ELSE 0 END ) level2Growth,
	SUM( CASE WHEN priority = 2 AND is_solved = 0 THEN 1 ELSE 0 END ) level1Growth,
	SUM( CASE WHEN is_solved = 2 THEN 1 ELSE 0 END ) isSolvedGrowth,
	'yesterday' dayStr
FROM
	hzb_alert
WHERE
	DATE_FORMAT( gmt_create, '%Y-%m-%d' ) = DATE_FORMAT( DATE_SUB( NOW(), INTERVAL 1 DAY ), '%Y-%m-%d' )
	AND platform_id IS NOT NULL
    AND platform_id in
    <foreach collection="platformIdList" separator="," open="(" close=")" item="item">
        #{item}
    </foreach>
    <if test="platformId != null">
        and platform_id = #{platformId}
    </if>

	UNION ALL
SELECT
	SUM( CASE WHEN priority = 0 AND is_solved = 0 THEN 1 ELSE 0 END ) level3Growth,
	SUM( CASE WHEN priority = 1 AND is_solved = 0 THEN 1 ELSE 0 END ) level2Growth,
	SUM( CASE WHEN priority = 2 AND is_solved = 0 THEN 1 ELSE 0 END ) level1Growth,
	SUM( CASE WHEN is_solved = 2 THEN 1 ELSE 0 END ) isSolvedGrowth,
	'today' dayStr
FROM
	hzb_alert
WHERE
	DATE_FORMAT( gmt_create, '%Y-%m-%d' ) = DATE_FORMAT(NOW(),'%Y-%m-%d') AND platform_id IS NOT NULL
    AND platform_id in
    <foreach collection="platformIdList" separator="," open="(" close=")" item="item">
        #{item}
    </foreach>
    <if test="platformId != null">
        and platform_id = #{platformId}
    </if>
</select>

<select id="getAlarmSummary" resultType="java.util.Map">
    SELECT
        IF(sum(IF(is_solved = 0,1,0)) is null ,0,sum(IF(is_solved = 0,1,0))) toBeSolved,
        IF(sum(IF(is_solved = 1,1,0)) is null ,0,sum(IF(is_solved = 1,1,0))) inSolving,
        IF(sum(IF(is_solved = 2,1,0)) is null ,0,sum(IF(is_solved = 2,1,0))) hasSolved,
        count(*) total
    from hzb_alert
    where 1 = 1
    <if test="platformIds.size>0 and platformIds != null">
        and platform_id in
        <foreach collection="platformIds" close=")" open="(" separator="," item="platformId">
            #{platformId}
        </foreach>
    </if>
    <if test="platformId !=null">
        and platform_id = #{platformId}
    </if>
    <if test="priority !=null">
        and priority = #{priority}
    </if>
    <if test="app != null and app.size() > 0">
        and app not in
        <foreach collection="app" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
    </select>

<!--<update id="updateAlarmToDoris">-->
<!--    &lt;!&ndash;@mbg.generated&ndash;&gt;-->
<!--    <foreach collection="list" item="item" index="index" separator=";">-->
<!--        update hzb_alert-->
<!--        <set>-->
<!--            <if test="item.content!=null and  item.content != ''">-->
<!--                content = #{item.content,jdbcType=VARCHAR},-->
<!--            </if>-->
<!--            <if test="item.gmtUpdate!=null">-->
<!--                gmt_update = #{item.gmtUpdate,jdbcType=BIGINT},-->
<!--            </if>-->
<!--            <if test="item.lastAlarmTime!=null and item.lastAlarmTime!=''">-->
<!--                last_alarm_time = #{item.lastAlarmTime,jdbcType=VARCHAR},-->
<!--            </if>-->
<!--            <if test="item.priority!=null">-->
<!--                priority = #{item.priority,jdbcType=INTEGER},-->
<!--            </if>-->
<!--            <if test="item.status!=null and item.status !=''">-->
<!--                status=#{item.status,jdbcType=VARCHAR},-->
<!--            </if>-->
<!--            <if test="item.tags!=null and item.tags != ''">-->
<!--                tags=#{item.tags,jdbcType=VARCHAR},-->
<!--            </if>-->
<!--            <if test="item.target!=null and item.target != ''">-->
<!--                target=#{item.target,jdbcType=VARCHAR},-->
<!--            </if>-->
<!--            <if test="item.times!=null">-->
<!--                times=#{item.times,jdbcType=INTEGER},-->
<!--            </if>-->
<!--        </set>-->
<!--        WHERE id = #{item.id,jdbcType=BIGINT}-->
<!--    </foreach>-->
<!--    </update>-->

    <update id="updateAlarmToDoris" parameterType="java.util.List">
        update hzb_alert
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="content = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then  #{item.content,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="gmt_update = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.gmtUpdate}
                </foreach>
            </trim>
            <trim prefix="last_alarm_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.lastAlarmTime,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="priority = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.priority,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="tags = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.tags,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="target = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.target,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="times = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.times,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="alarm_config_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.alarmConfigName}
                </foreach>
            </trim>
            <trim prefix="alarm_rule = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.alarmRule}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="getAlertInfoById" resultType="org.apache.hertzbeat.common.entity.alerter.Alert">
    select * from hzb_alert where id = #{id}
    </select>

<select id="getAlarmConfigById" resultType="cn.iocoder.zj.module.monitor.dal.dataobject.alarmconfig.AlarmConfigDO">
    select * from monitor_alarm_config where id = #{id}
</select>

<select id="getAlarmConfigList" resultType="cn.iocoder.zj.module.monitor.dal.dataobject.alarmconfig.AlarmConfigDO">
    SELECT
    <if test="pageReqVO.uuid != null and pageReqVO.uuid != ''">
        if(ahr.alarm_id is null,'false','true') selected,
    </if>
    ac.*,ten.NAME AS tenantName
    FROM
    monitor_alarm_config ac
    LEFT JOIN system_tenant ten ON ten.id = ac.tenant_id
    <if test="pageReqVO.uuid != null and pageReqVO.uuid != ''">
        LEFT JOIN monitor_alarm_host_relation ahr
        on ahr.host_uuid = #{pageReqVO.uuid}
        and ac.id = ahr.alarm_id
    </if>
    WHERE
    ac.deleted = 0
    <if test="pageReqVO.alarmName != null and pageReqVO.alarmName != ''">
        and ac.alarm_name like concat("%",#{pageReqVO.alarmName},"%")
    </if>
    <if test="pageReqVO.tenantId !=null and pageReqVO.tenantId !=''">
        and ac.tenant_id  in
        <foreach collection="pageReqVO.tenantId" separator="," close=")" open="(" item="item">
            #{item}
        </foreach>
    </if>

    <if test="pageReqVO.sourceType != null and pageReqVO.sourceType != ''">
        and
        <foreach collection="pageReqVO.sourceTypeList" open="(" separator="or" close=")" item="list">
            ac.source_type = #{list}
        </foreach>
    </if>

    <if test="pageReqVO.alarmLevel != null and pageReqVO.alarmLevel != ''">
        and
        <foreach collection="pageReqVO.levelList" open="(" separator="or" close=")" item="list">
            ac.alarm_level = #{list}
        </foreach>
    </if>
    <if test="pageReqVO.startTime != null and pageReqVO.startTime != '' and pageReqVO.endTime != null and pageReqVO.endTime != ''">
        and DATE_FORMAT( ac.update_time,"%Y-%m-%d %H:%i:%s" )  between #{pageReqVO.startTime} and  #{pageReqVO.endTime}
    </if>
    <if test="pageReqVO.tenantList !=null and pageReqVO.tenantList.size()>0">
        and ac.tenant_id in
        <foreach collection="pageReqVO.tenantList" open="(" separator="," close=")" item="list">
            #{list}
        </foreach>
    </if>
    ORDER BY
    <if test="pageReqVO.uuid != null and pageReqVO.uuid != ''">
        selected DESC,
    </if>
    ac.create_time DESC
    </select>
</mapper>
