<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.monitor.dal.mysql.hardwareinfo.HardwareInfoMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <update id="updateHardWareList" parameterType="java.util.List">
        <!--@mbg.generated-->
        update monitor_hardware_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.name,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`state` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.state,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ip = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.ip,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.status,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cluster_uuid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.clusterUuid,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cluster_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.clusterName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="total_cpu_capacity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.totalCpuCapacity,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="available_cpu_capacity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.availableCpuCapacity,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="cpu_sockets = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.cpuSockets,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="architecture = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.architecture,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cpu_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.cpuNum,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="total_memory_capacity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.totalMemoryCapacity,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="available_memory_capacity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.availableMemoryCapacity,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="tenant_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.tenantId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="region_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.regionId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="bandwidth_upstream = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.bandwidthUpstream,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="bandwidth_downstream = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.bandwidthDownstream,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="memory_used = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.memoryUsed,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="cpu_used = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.cpuUsed,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="packet_rate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.packetRate,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="disk_used = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.diskUsed,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="disk_used_bytes = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.diskUsedBytes,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="disk_free_bytes = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.diskFreeBytes,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="total_disk_capacity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.totalDiskCapacity,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="platform_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.platformId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="platform_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.platformName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="vms = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.vms}
                </foreach>
            </trim>

            <trim prefix="cpu_over_percent = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.cpuOverPercent}
                </foreach>
            </trim>

            <trim prefix="memory_over_percent = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.memoryOverPercent}
                </foreach>
            </trim>

            <trim prefix="manager = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.manager}
                </foreach>
            </trim>

            <trim prefix="available_manager = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.availableManager}
                </foreach>
            </trim>

            <trim prefix="cpu_commit_rate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.cpuCommitRate}
                </foreach>
            </trim>

            <trim prefix="memory_commit_rate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.memoryCommitRate}
                </foreach>
            </trim>

            <trim prefix="reserved_memory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.reservedMemory}
                </foreach>
            </trim>
            <trim prefix="brand_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.brandName}
                </foreach>
            </trim>
            <trim prefix="model = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.model}
                </foreach>
            </trim>
            <trim prefix="serial_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.serialNumber}
                </foreach>
            </trim>
            <trim prefix="cpu_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.cpuType}
                </foreach>
            </trim>

            <trim prefix="deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.deleted}
                </foreach>
            </trim>
        </trim>
        where uuid in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.uuid,jdbcType=VARCHAR}
        </foreach>
    </update>

<select id="getCount" resultType="int">
    select count(*)
    from monitor_hardware_info
    where type_name = #{typeName}
    </select>

    <select id="getHardwareStatusCount" resultType="map">
        SELECT
            SUM( CASE STATUS WHEN 'Connected' THEN 1 ELSE 0 END ) hardwareConnectedNum,
            SUM( CASE STATUS WHEN 'Disconnected' THEN 1 ELSE 0 END ) hardwareDisconnectedNum,
            SUM( CASE STATUS WHEN 'Connecting' THEN 1 ELSE 0 END ) hardwareConnectingNum,
            count( 1 ) hardwareNum
        FROM
        (select b.* from
        (SELECT * FROM system_platform_tenant WHERE deleted = 0 GROUP BY platform_id
        <if test="tenantIds != null and tenantIds.size > 0">
            ,tenant_id
        </if>
        ) a
        LEFT JOIN
        (SELECT * FROM monitor_hardware_info
        WHERE deleted = 0
        GROUP BY UUID) b
        ON a.platform_id=b.platform_id
        <where>
            b.deleted = 0
            <if test="tenantIds != null and tenantIds.size > 0">
                and a.deleted = 0
                and a.tenant_id in
                <foreach collection="tenantIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="platformId != null and platformId != ''">
                and b.platform_id = #{platformId}
            </if>
        </where>
       group by b.uuid) c

    </select>

    <select id="getCpuTop" resultType="map">
        SELECT NAME,
            cpu_used
        FROM
            (SELECT * FROM system_platform_tenant WHERE deleted = 0 GROUP BY platform_id
        <if test="tenantIds != null and tenantIds.size > 0">
            ,tenant_id
        </if>
        ) a
        LEFT JOIN
        monitor_hardware_info b
        ON a.platform_id=b.platform_id
        where 1=1 AND b.deleted = 0
        <if test="tenantIds != null and tenantIds.size > 0">
            and a.tenant_id in
            <foreach collection="tenantIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="platformId != null and platformId != ''">
            and b.platform_id = #{platformId}
        </if>
        <if test="regionId != null and regionId != ''">
            and b.region_id like concat(#{regionId},"%")
        </if>
        ORDER BY
            cpu_used DESC
            LIMIT 0,#{top}
    </select>

    <select id="getMemoryTop" resultType="map">
        SELECT NAME,
            memory_used
        FROM
            (SELECT * FROM system_platform_tenant WHERE deleted = 0 GROUP BY platform_id
        <if test="tenantIds != null and tenantIds.size > 0">
            ,tenant_id
        </if>
        ) a
        LEFT JOIN
        monitor_hardware_info b
        ON a.platform_id=b.platform_id
        where 1=1 AND b.deleted = 0
        <if test="tenantIds != null and tenantIds.size > 0">
            and a.tenant_id in
            <foreach collection="tenantIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="platformId != null and platformId != ''">
            and b.platform_id = #{platformId}
        </if>
        <if test="regionId != null and regionId != ''">
            and b.region_id like concat(#{regionId},"%")
        </if>
        ORDER BY
            memory_used DESC
            LIMIT 0,#{top}
    </select>

    <select id="getCpuCapacity" resultType="map">
        SELECT
            sum( total_cpu_capacity ) totalCpuCapacity,
            sum( total_cpu_capacity - available_cpu_capacity ) usedCpuCapacity
        FROM
        (select b.* from
        (SELECT * FROM system_platform_tenant WHERE deleted = 0 GROUP BY platform_id
        <if test="tenantIds != null and tenantIds.size > 0">
            ,tenant_id
        </if>
        ) a
        LEFT JOIN
        monitor_hardware_info b
        ON a.platform_id=b.platform_id
        where 1=1 and b.deleted=0
        <if test="tenantIds != null and tenantIds.size > 0">
            and a.tenant_id in
            <foreach collection="tenantIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="platformId != null and platformId != ''">
            and b.platform_id = #{platformId}
        </if>
        <if test="regionId != null and regionId != ''">
            and b.region_id like concat(#{regionId},"%")
        </if>
        GROUP BY b.uuid
        <if test="tenantIds != null and tenantIds.size > 0">
            ,a.tenant_id
        </if>
        ) c
    </select>

    <select id="getMemoryCapacity" resultType="map">
        SELECT
            sum( total_memory_capacity ) totalMemoryCapacity,
            sum( total_memory_capacity - available_memory_capacity ) usedMemoryCapacity
        FROM
        (select b.* from
        (SELECT * FROM system_platform_tenant WHERE deleted = 0 GROUP BY platform_id
        <if test="tenantIds != null and tenantIds.size > 0">
            ,tenant_id
        </if>
        ) a
        LEFT JOIN
        monitor_hardware_info b
        ON a.platform_id=b.platform_id
        where 1=1 AND b.deleted = 0
        <if test="tenantIds != null and tenantIds.size > 0">
            and a.tenant_id in
            <foreach collection="tenantIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="platformId != null and platformId != ''">
            and b.platform_id = #{platformId}
        </if>
        <if test="regionId != null and regionId != ''">
            and b.region_id like concat(#{regionId},"%")
        </if>
        group by b.uuid
        <if test="tenantIds != null and tenantIds.size > 0">
            ,a.tenant_id
        </if>
         ) c
    </select>

    <select id="getUUID" resultType="string">
        SELECT uuid
        FROM
            monitor_hardware_info
        where tenant_id=#{id}
        ORDER BY
            memory_used DESC
            LIMIT 0,5
    </select>

    <select id="selectPlatformList" resultType="long">
        select platform_id from system_platform_tenant where deleted = 0
        <if test="tenantIds != null and tenantIds.size > 0">
            and tenant_id in
            <foreach collection="tenantIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by platform_id
    </select>

    <select id="getHardwareInfoPage"
            resultType="cn.iocoder.zj.module.monitor.dal.dataobject.hardwareinfo.HardwareInfoDO">
        SELECT info.*  FROM
        system_platform_tenant plat
        LEFT JOIN monitor_hardware_info info ON info.platform_id = plat.platform_id AND plat.deleted = 0
        <if test="pageReqVO.tagIds != null and pageReqVO.tagIds !=''">
            inner join monitor_taggables taggables on taggables.taggable_id = info.id and taggables.taggable_type = '${@<EMAIL>()}'
        </if>
        where info.deleted = 0
        <if test="pageReqVO.tagIds != null and pageReqVO.tagIds !=''">
            and taggables.tag_id in (${pageReqVO.tagIds}) and taggables.deleted = 0
        </if>
        <if test="pageReqVO.tenantId != null and pageReqVO.tenantId.size>0">
        and plat.tenant_id in
            <foreach collection="pageReqVO.tenantId" separator="," close=")" open="(" item="item">
                #{item}
            </foreach>
        </if>
        <if test="pageReqVO.queryData != null and pageReqVO.queryData !=''">
            and(info.name like concat("%",#{pageReqVO.queryData},"%")
            or info.ip like concat("%",#{pageReqVO.queryData},"%"))
        </if>
        <if test="pageReqVO.platformName != null and pageReqVO.platformName !=''">
            and info.platform_name like concat("%",#{pageReqVO.platformName},"%")
        </if>
        <if test="pageReqVO.status != null and pageReqVO.status !=''">
            and info.status = #{pageReqVO.status}
        </if>
        <if test="pageReqVO.state != null and pageReqVO.state !=''">
            <if test="pageReqVO.state == 'Maintenance'">
                and ((info.state = #{pageReqVO.state} and info.type_name = 'zstack') or (info.is_maintain = 1 and info.type_name = 'vmware') or (info.state = 'Maintenance' and info.type_name = 'winhong'))
            </if>
        </if>
        <if test="pageReqVO.state != null and pageReqVO.state !=''">
            <if test="pageReqVO.state != 'Maintenance'">
                and info.state = #{pageReqVO.state} and info.is_maintain = 0
            </if>
        </if>
        <if test="pageReqVO.platformId != null">
            and info.platform_id = #{pageReqVO.platformId}
        </if>
        <if test="pageReqVO.name != null and pageReqVO.name !=''">
            and info.name like concat("%",#{pageReqVO.name},"%")
        </if>
        <if test="pageReqVO.ip != null and pageReqVO.ip !=''">
            and info.ip like concat("%",#{pageReqVO.ip},"%")
        </if>
        <if test="pageReqVO.clusterUuid != null and pageReqVO.clusterUuid !=''">
            and info.cluster_uuid = #{pageReqVO.clusterUuid}
        </if>
        <if test="pageReqVO.startTime != '' and pageReqVO.endtime != '' and pageReqVO.startTime != null and pageReqVO.endtime != null">
            and DATE_FORMAT( info.create_time, "%Y-%m-%d %H:%i:%s" )  between #{pageReqVO.startTime} and  #{pageReqVO.endTime}
        </if>
        <if test="ids.size()>0">
            and info.uuid not in
            <foreach item="item" index="index" collection="ids"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="pageReqVO.inPks != null and !pageReqVO.inPks.isEmpty()">
            AND info.id IN
            <foreach collection="pageReqVO.inPks" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY info.uuid
        ORDER BY
        <if test="pageReqVO.sortBy == null or pageReqVO.sortBy =='' or pageReqVO.sortDirection== null or pageReqVO.sortDirection ==''">
                info.create_time DESC
               ,info.name DESC
        </if>
        <if test="pageReqVO.sortBy != null and pageReqVO.sortBy !='' and pageReqVO.sortDirection!= null and pageReqVO.sortDirection !=''">
            info.${pageReqVO.sortBy} ${pageReqVO.sortDirection}
        </if>
    </select>

    <delete id="deleteHardWare">
        DELETE FROM monitor_hardware_info
        WHERE uuid  IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.uuid}
        </foreach>
    </delete>

    <select id="getHardwareInfoSlavePage"
            resultType="cn.iocoder.zj.module.monitor.dal.dataobject.hardwareinfo.HardwareInfoDO">
        SELECT info.*  FROM
        system_platform_tenant plat
        LEFT JOIN monitor_hardware_info info ON info.platform_id = plat.platform_id AND plat.deleted = 0
        where info.deleted = 0
        <if test="pageVO.tenantId != null and pageVO.tenantId.size>0">
            and plat.tenant_id in
            <foreach collection="pageVO.tenantId" separator="," close=")" open="(" item="item">
                #{item}
            </foreach>
        </if>
        <if test="pageVO.queryData != null and pageVO.queryData !=''">
            and(info.name like concat("%",#{pageVO.queryData},"%")
            or info.ip like concat("%",#{pageVO.queryData},"%"))
        </if>
        <if test="pageVO.platformName != null and pageVO.platformName !=''">
            and info.platform_name like concat("%",#{pageVO.platformName},"%")
        </if>
        <if test="pageVO.status != null and pageVO.status !=''">
            and info.status = #{pageVO.status}
        </if>
        <if test="pageVO.platformId != null">
            and info.platform_id = #{pageVO.platformId}
        </if>
        <if test="pageVO.name != null and pageVO.name !=''">
            and info.name like concat("%",#{pageVO.name},"%")
        </if>
        <if test="ids.size()>0">
            and info.uuid not in
            <foreach item="item" index="index" collection="ids"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY info.uuid
        ORDER BY info.name DESC
    </select>

    <select id="getClusterSimpleInfo" resultType="java.util.Map">
        SELECT
            cluster_name clusterName,
            cluster_uuid clusterUuid
        FROM monitor_hardware_info
        WHERE platform_id in
        <foreach item="item" index="index" collection="platformId"  open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY cluster_uuid
    </select>

    <select id="selectListByUuids" resultType="cn.iocoder.zj.module.monitor.dal.dataobject.hardwareinfo.HardwareInfoDO">
        SELECT
        info.*,
        pConfig.region_id,
        pConfig.region_name
        FROM
        monitor_hardware_info info
        LEFT JOIN system_platform_config pConfig ON pConfig.id = info.platform_id and pConfig.deleted = 0
        WHERE
        info.deleted = 0
        AND info.uuid IN
        <foreach collection="uuids" open="(" separator="," close=")" item="uuid">
            #{uuid}
        </foreach>
        GROUP BY info.uuid
    </select>

    <select id="selectHardwareList"
            resultType="cn.iocoder.zj.module.monitor.dal.dataobject.hardwareinfo.HardwareInfoDO">
        SELECT
        info.*
        FROM
        monitor_hardware_info info
        WHERE
        info.uuid NOT IN ( SELECT host_uuid FROM monitor_alarm_host_relation relation WHERE relation.deleted = 0 )
        AND info.deleted = 0
        <if test="pageVO.name != null and pageVO.name !=''">
            and info.name like concat("%",#{pageVO.name},"%")
        </if>
        <if test="pageVO.tenantId != null and pageVO.tenantId.size>0">
            AND info.platform_id in (
            SELECT platform_id from system_platform_tenant pt
            WHERE pt.tenant_id in
            <foreach collection="pageVO.tenantId" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            AND pt.deleted = 0)
        </if>
        ORDER BY
        info.create_time DESC
    </select>

    <select id="getHardwareInfoList"
            resultType="cn.iocoder.zj.module.monitor.dal.dataobject.hardwareinfo.HardwareInfoDO">
        SELECT info.*  FROM
        system_platform_tenant plat
        LEFT JOIN monitor_hardware_info info ON info.platform_id = plat.platform_id AND plat.deleted = 0
        <if test="reqVO.tagIds != null and reqVO.tagIds !=''">
            inner join monitor_taggables taggables on taggables.taggable_id = info.id and taggables.taggable_type = '${@<EMAIL>()}'
        </if>
        where info.deleted = 0
        <if test="reqVO.tagIds != null and reqVO.tagIds !=''">
            and taggables.tag_id in (${reqVO.tagIds}) and taggables.deleted = 0
        </if>
        <if test="reqVO.tenantId != null and reqVO.tenantId.size>0">
            and plat.tenant_id in
            <foreach collection="reqVO.tenantId" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="reqVO.platformName != null and reqVO.platformName !=''">
            and info.platform_name like concat("%",#{reqVO.platformName},"%")
        </if>
        <if test="reqVO.status != null and reqVO.status !=''">
            and info.status = #{reqVO.status}
        </if>
        <if test="reqVO.state != null and reqVO.state !=''">
            and info.state = #{reqVO.state}
        </if>
        <if test="reqVO.platformId != null">
            and info.platform_id = #{reqVO.platformId}
        </if>
        <if test="reqVO.name != null and reqVO.name !=''">
            and info.name like concat("%",#{reqVO.name},"%")
        </if>
        <if test="reqVO.clusterUuid != null and reqVO.clusterUuid !=''">
            and info.cluster_uuid = #{reqVO.clusterUuid}
        </if>
        <if test="reqVO.startTime != '' and reqVO.endtime != '' and reqVO.startTime != null and reqVO.endtime != null">
            and DATE_FORMAT( info.create_time, "%Y-%m-%d %H:%i:%s" )  between #{reqVO.startTime} and  #{reqVO.endTime}
        </if>
        GROUP BY info.uuid
        ORDER BY info.create_time DESC
    </select>

    <select id="getDuplicateDataIds" resultType="java.util.Map">
        SELECT
            max(id) as id,
            uuid
        FROM
            monitor_hardware_info
        WHERE
            1=1
        GROUP BY
            uuid
        HAVING
            count( 1 ) > 1
    </select>
    <delete id="removeDuplicateData">
        delete  from monitor_hardware_info
        where
        <foreach collection="duplicateDatas" item="data" separator="or" open="(" close=")">
            id != #{data.id} and uuid = #{data.uuid}
        </foreach>
    </delete>

    <select id="getCpuSockets" resultType="java.lang.Long">
        select ifnull(sum(cpu_num) ,0) cpuNum from monitor_hardware_info
        <where>
            monitor_hardware_info.platform_id  in
            <foreach  item="item" index="index" collection="platformIds" open="(" separator="," close=" )">
                #{platformIds}
            </foreach>
        </where>
        and deleted = 0
    </select>

    <select id="getScreenStatistics" resultType="java.util.Map">
        select cpu_used,disk_used,memory_used from monitor_hardware_info
        <where>
            platform_id in
            <foreach close=")" collection="platformIds" item="item" open="(" separator=", ">
                #{item.platformId,jdbcType=VARCHAR}
            </foreach>
        </where>
        and deleted = 0
    </select>

    <select id="getHostNameByuuid" resultType="java.util.Map">
        select uuid,name from monitor_hardware_info
        <where>
            uuid in
            <foreach close=")" collection="hostcpus" item="item" open="(" separator=", ">
                #{item.uuid,jdbcType=VARCHAR}
            </foreach>
        </where>
    </select>

    <select id="selectUserCount" resultType="int">
        SELECT count(*) count  from customer_cust_info WHERE deleted = 0
    </select>

    <select id="selectCloudCount" resultType="int">
        SELECT count(*) count FROM system_platform_config WHERE deleted = 0
    </select>

    <select id="selectAssetCount" resultType="int">
        SELECT sum(count) count from
            (SELECT count(*) count from monitor_hardware_info WHERE deleted = 0
             UNION
             SELECT count(*) count  from hzb_monitor
             UNION
             SELECT count(*) count from monitor_host_info WHERE deleted = 0 and state != 'Destroyed'
                union
             SELECT count(*) count from monitor_storage_info WHERE deleted = 0
            )  a
    </select>

    <select id="getHzAppListCount" resultType="java.util.Map">
        SELECT app,count(*) count  from hzb_monitor
        WHERE  platform_id IN
        <foreach collection="mapList" separator="," close=")" open="(" item="platform">
            #{platform.platformId}
        </foreach>
        GROUP BY app
        ORDER BY count desc
    </select>

    <select id="selectCountByPlatfrom" resultType="java.lang.Long">
        SELECT count(*) count  from monitor_hardware_info WHERE platform_id
        IN
        <foreach collection="mapList" separator="," close=")" open="(" item="platform">
            #{platform.platformId}
        </foreach>
        and deleted = 0
    </select>

    <delete id="deleteHardwareInfoByplatform">
        delete from monitor_hardware_info
        where platform_id = #{platformId}
    </delete>

    <select id="getHardwareByPlatformId"
            resultType="cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespCreateReqDTO">
        select * from monitor_hardware_info   where platform_id = #{platformId} and deleted = 0
    </select>

    <select id="findHardwareByplatformIdAndHost"
            resultType="cn.iocoder.zj.module.monitor.dal.dataobject.hardwareinfo.HardwareInfoDO">
        select *
        from monitor_hardware_info
        where ip = #{hostName}
          and platform_id = #{platformId}
          and deleted = 0
    </select>

    <select id="getHardwareInfoByList"
            resultType="cn.iocoder.zj.module.monitor.dal.dataobject.hardwareinfo.HardwareInfoDO">
        SELECT info.*  FROM
        system_platform_tenant plat
        LEFT JOIN monitor_hardware_info info ON info.platform_id = plat.platform_id AND plat.deleted = 0
        where info.deleted = 0
        <if test="pageReqVO.tenantId != null and pageReqVO.tenantId.size>0">
            and plat.tenant_id in
            <foreach collection="pageReqVO.tenantId" separator="," close=")" open="(" item="item">
                #{item}
            </foreach>
        </if>
        <if test="pageReqVO.queryData != null and pageReqVO.queryData !=''">
            and(info.name like concat("%",#{pageReqVO.queryData},"%")
            or info.ip like concat("%",#{pageReqVO.queryData},"%"))
        </if>
        <if test="pageReqVO.platformName != null and pageReqVO.platformName !=''">
            and info.platform_name like concat("%",#{pageReqVO.platformName},"%")
        </if>
        <if test="pageReqVO.status != null and pageReqVO.status !=''">
            and info.status = #{pageReqVO.status}
        </if>
        <if test="pageReqVO.state != null and pageReqVO.state !=''">
            <if test="pageReqVO.state == 'Maintenance'">
                and ((info.state = #{pageReqVO.state} and info.type_name = 'zstack') or (info.is_maintain = 1 and info.type_name = 'vmware'))
            </if>
        </if>
        <if test="pageReqVO.state != null and pageReqVO.state !=''">
            <if test="pageReqVO.state != 'Maintenance'">
                and info.state = #{pageReqVO.state} and info.is_maintain = 0
            </if>
        </if>
        <if test="pageReqVO.platformId != null">
            and info.platform_id = #{pageReqVO.platformId}
        </if>
        <if test="pageReqVO.name != null and pageReqVO.name !=''">
            and info.name like concat("%",#{pageReqVO.name},"%")
        </if>
        <if test="pageReqVO.clusterUuid != null and pageReqVO.clusterUuid !=''">
            and info.cluster_uuid = #{pageReqVO.clusterUuid}
        </if>
        <if test="pageReqVO.startTime != '' and pageReqVO.endtime != '' and pageReqVO.startTime != null and pageReqVO.endtime != null">
            and DATE_FORMAT( info.create_time, "%Y-%m-%d %H:%i:%s" )  between #{pageReqVO.startTime} and  #{pageReqVO.endTime}
        </if>
        <if test="ids.size()>0">
            and info.uuid not in
            <foreach item="item" index="index" collection="ids"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="inIds.size()>0">
            and info.uuid in
            <foreach item="item" index="index" collection="inIds"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY info.uuid
        ORDER BY
        <if test="pageReqVO.sortBy == null or pageReqVO.sortBy =='' or pageReqVO.sortDirection== null or pageReqVO.sortDirection ==''">
            info.create_time DESC
            ,info.name DESC
        </if>
        <if test="pageReqVO.sortBy != null and pageReqVO.sortBy !='' and pageReqVO.sortDirection!= null and pageReqVO.sortDirection !=''">
            info.${pageReqVO.sortBy} ${pageReqVO.sortDirection}
        </if>
    </select>
    <select id="getCloudHostCount" resultType="java.lang.Integer">
        SELECT
            COUNT(*) AS cloudHostCount
        FROM
            monitor_host_info
        WHERE
            hardware_uuid = #{hardwareUuid}
          AND deleted =0
    </select>
    <select id="getHostInfoCpuUsedTopByPlatformId" resultType="java.util.Map">
        SELECT name ,cpu_used as value FROM monitor_hardware_info WHERE deleted = 0

        <if test="platformId.size>0">
            and platform_id in
            <foreach
                    collection="platformId" open="(" separator="," close=")" item="platform">
                #{platform.platformId}
            </foreach>
        </if>
        ORDER BY cpu_used desc
        limit 5
    </select>

    <select id="getHostInfoMemUsedTopByPlatformId" resultType="java.util.Map">
        SELECT name ,memory_used as value FROM monitor_hardware_info WHERE deleted = 0
        <if test="platformId.size>0">
            and platform_id in
            <foreach
                    collection="platformId" open="(" separator="," close=")" item="platform">
                #{platform.platformId}
            </foreach>
        </if>
        ORDER BY memory_used desc
        limit 5
    </select>

    <select id="getHardwareByList" resultType="cn.iocoder.zj.module.monitor.dal.dataobject.hardwareinfo.HardwareInfoDO">
        SELECT
        uuid,
        name,
        cpu_used,
        memory_used,
        cpu_num,
        cpu_sockets,
        total_cpu_capacity,
        cpu_commit_rate,
        total_memory_capacity,
        total_virtual_memory,
        memory_commit_rate,
        platform_id,
        platform_name
        FROM
        monitor_hardware_info
        WHERE  deleted = 0 AND uuid in
        <foreach item="item" index="index" collection="collect"  open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getListByTag" resultType="cn.iocoder.zj.module.monitor.taskTime.cache.TaggableDO">
        SELECT
            id,
            uuid,
            NAME,
            platform_id,
            tag
        FROM
            monitor_hardware_info
        WHERE
            deleted = 0
        GROUP BY
            uuid,platform_id
    </select>
</mapper>
