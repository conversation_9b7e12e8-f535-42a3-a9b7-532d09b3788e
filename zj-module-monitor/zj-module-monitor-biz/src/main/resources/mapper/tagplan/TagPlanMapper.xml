<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.monitor.dal.mysql.tagplan.TagPlanMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectTagPlanPage" resultType="cn.iocoder.zj.module.monitor.dal.dataobject.tagplan.TagPlanDO">
        SELECT
        p.*,
        COUNT(r.id) AS reportCount
        FROM tag_plan p
        LEFT JOIN tag_record r ON p.id = r.plan_id
        <where>
            p.deleted = 0
            <if test="reqVO.name != null and reqVO.name != ''">
                AND p.name LIKE CONCAT('%', #{reqVO.name}, '%')
            </if>
            <if test="reqVO.status != null">
                AND p.status = #{reqVO.status}
            </if>
            <if test="reqVO.tenantId != null">
                AND p.tenant_id = #{reqVO.tenantId}
            </if>
            <if test="reqVO.platformId != null and reqVO.platformId != ''">
                AND p.platform_ids LIKE CONCAT('%', #{reqVO.platformId}, '%')
            </if>
            <if test="reqVO.startTime != null and reqVO.endTime != null">
                AND p.last_execution_time BETWEEN #{reqVO.startTime} AND #{reqVO.endTime}
            </if>
        </where>
        GROUP BY p.id
        <choose>
            <when test="reqVO.sortBy == 'lastExecutionTime'">
                ORDER BY p.last_execution_time
                <if test="reqVO.sortDirection == 'asc'">ASC</if>
                <if test="reqVO.sortDirection == 'desc'">DESC</if>
            </when>
            <when test="reqVO.sortBy == 'nextExecutionTime'">
                ORDER BY p.next_execution_time
                <if test="reqVO.sortDirection == 'asc'">ASC</if>
                <if test="reqVO.sortDirection == 'desc'">DESC</if>
            </when>
            <when test="reqVO.sortBy == 'reportCount'">
                ORDER BY reportCount
                <if test="reqVO.sortDirection == 'asc'">ASC</if>
                <if test="reqVO.sortDirection == 'desc'">DESC</if>
            </when>
            <otherwise>
                ORDER BY p.id DESC
            </otherwise>
        </choose>
    </select>


    <select id="getPlatformSelectList" resultType="java.lang.String">
        SELECT
            GROUP_CONCAT(tenant.platform_id) platformId
        FROM
            system_platform_tenant tenant
                LEFT JOIN system_platform_config config ON tenant.platform_id = config.id
        WHERE
            tenant.tenant_id =  #{tenantId}
          AND tenant.deleted = 0
          AND config.deleted = 0
          AND config.type_code IN ("zstack", "winhong")
    </select>

</mapper>
