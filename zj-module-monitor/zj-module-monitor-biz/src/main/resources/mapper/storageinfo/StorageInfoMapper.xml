<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.monitor.dal.mysql.storageinfo.StorageInfoMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


<select id="getCount" resultType="int">
    select count(*)
    from monitor_storage_info
    where type_name = #{typeName}
    </select>


    <update id="updateHostInfoList" parameterType="java.util.List">
        <!--@mbg.generated-->
        update monitor_storage_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.url,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`state` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.state,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`type` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.type,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.status,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="capacity_utilization = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.capacityUtilization,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="total_capacity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.totalCapacity,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="used_capacity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.usedCapacity,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="tenant_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.tenantId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="region_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.regionId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="available_physical_capacity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.availablePhysicalCapacity,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="total_physical_capacity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.totalPhysicalCapacity,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="available_capacity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.availableCapacity,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="platform_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.platformId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="platform_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.platformName,jdbcType=VARCHAR}
                </foreach>
            </trim>
                <trim prefix="deleted = case" suffix="end,">
                    <foreach collection="list" index="index" item="item">
                        when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.deleted,jdbcType=INTEGER}
                    </foreach>
                </trim>
            <trim prefix="type_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.typeName,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="cluster_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.clusterName,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="cluster_uuid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.clusterUuid,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.createTime}
                </foreach>
            </trim>
            <trim prefix="s_create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.sCreateTime}
                </foreach>
            </trim>

            <trim prefix="media_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.mediaType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="storage_percent = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.storagePercent,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="manager = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.manager,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="available_manager = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.availableManager,jdbcType=VARCHAR}
            </foreach>
            </trim>
            <trim prefix="reserve_capacity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.reserveCapacity,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="waste_capacity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.wasteCapacity,jdbcType=BIGINT}
                </foreach>
            </trim>

            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="virtual_capacity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.virtualCapacity,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="commit_rate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.commitRate,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="allocation = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.allocation,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="v_update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.vUpdateTime}
                </foreach>
            </trim>
        </trim>
        where uuid in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.uuid,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="getStorageCapacity" resultType="map">
        SELECT
        sum( used_capacity ) usedCapacity,
        sum( total_capacity ) totalCapacity
        FROM
        monitor_storage_info
        where deleted=0
        and platform_id in (
        SELECT
        platform_id
        FROM
        system_platform_tenant
        WHERE
        deleted = 0
        <if test="tenantIds != null and tenantIds.size > 0">
        and tenant_id in
            <foreach collection="tenantIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        )
        <if test="platformId != null and platformId != ''">
            and platform_id = #{platformId}
        </if>
        <if test="regionId != null and regionId != ''">
            and region_id like concat(#{regionId},"%")
        </if>
    </select>

    <select id="selectPlatformList" resultType="long">
        select platform_id from system_platform_tenant where  deleted = 0
        <if test="tenantIds != null and tenantIds.size > 0">
        and
        tenant_id in
            <foreach collection="tenantIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

    </select>

<select id="getStorageInfoPage" resultType="cn.iocoder.zj.module.monitor.dal.dataobject.storageinfo.StorageInfoDO">
    SELECT info.*  FROM
    system_platform_tenant plat
    LEFT JOIN ( SELECT
    id,
    NAME,
    uuid,
    url,
    state,
    type,
    STATUS,
    used_capacity,
    total_capacity,
    creator,
    create_time,
    updater,
    update_time,
    deleted,
    tenant_id,
    region_id,
    available_physical_capacity,
    total_physical_capacity,
    available_capacity,
    platform_id,
    platform_name,
    type_name,
    cluster_uuid,
    cluster_name,
    s_create_time, IF ( total_capacity > 0, used_capacity * 100.00 / total_capacity, 0 ) AS virtual_utilization
    ,ROUND(( total_physical_capacity - available_physical_capacity ) * 100.00 / total_physical_capacity, 2 ) AS capacity_utilization
    FROM monitor_storage_info ) info ON info.platform_id = plat.platform_id
    <if test="pageReqVO.tagIds != null and pageReqVO.tagIds !=''">
        inner join monitor_taggables taggables on taggables.taggable_id = info.id and taggables.taggable_type = '${@<EMAIL>()}'
    </if>
    AND plat.deleted = 0
    where info.deleted = 0
    <if test="pageReqVO.tagIds != null and pageReqVO.tagIds != ''">
        and taggables.tag_id in (${pageReqVO.tagIds}) and taggables.deleted = 0
    </if>
    <if test="pageReqVO.tenantId != null and pageReqVO.tenantId.size>0">
        and plat.tenant_id in
        <foreach collection="pageReqVO.tenantId" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </if>
    <if test="pageReqVO.name != null and pageReqVO.name !=''">
        and info.name like concat("%",#{pageReqVO.name},"%")
    </if>
    <if test="pageReqVO.platformName != null and pageReqVO.platformName !=''">
        and info.platform_name like concat("%",#{pageReqVO.platformName},"%")
    </if>
    <if test="pageReqVO.status != null and pageReqVO.status !=''">
        and info.status = #{pageReqVO.status}
    </if>
    <if test="pageReqVO.platformId != null">
        and info.platform_id = #{pageReqVO.platformId}
    </if>
    <if test="pageReqVO.state != null and pageReqVO.state !=''">
        and info.state = #{pageReqVO.state}
    </if>
    <if test="pageReqVO.type != null and pageReqVO.type !=''">
        and info.type like concat("%",#{pageReqVO.type},"%")
    </if>
    <if test="pageReqVO.startTime != '' and pageReqVO.endtime != ''and pageReqVO.startTime != null and pageReqVO.endtime != null">
        and DATE_FORMAT( info.s_create_time, "%Y-%m-%d %H:%i:%s" ) between #{pageReqVO.startTime} and
        #{pageReqVO.endTime}
    </if>
    <if test="ids.size()>0">
        and info.uuid not in
        <foreach item="item" index="index" collection="ids"  open="(" separator="," close=")">
            #{item}
        </foreach>
    </if>
    <if test="pageReqVO.hardWareUuid != null">
        and info.uuid in  (SELECT DISTINCT storage_uuid FROM monitor_hardware_storage WHERE hardware_uuid = #{pageReqVO.hardWareUuid} and deleted = 0)
    </if>
    <if test="pageReqVO.inPks != null and !pageReqVO.inPks.isEmpty()">
        AND info.id IN
        <foreach collection="pageReqVO.inPks" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </if>
    GROUP BY info.uuid
    ORDER BY
    <if test="pageReqVO.sortBy == null or pageReqVO.sortBy =='' or pageReqVO.sortDirection== null or pageReqVO.sortDirection ==''">
        info.create_time,info.name DESC
    </if>
    <if test="pageReqVO.sortBy != null and pageReqVO.sortBy !='' and pageReqVO.sortDirection!= null and pageReqVO.sortDirection !=''">
        info.${pageReqVO.sortBy} ${pageReqVO.sortDirection}
    </if>
    </select>

<delete id="deleteStorageList">
    DELETE FROM monitor_storage_info
    WHERE uuid  IN
    <foreach collection="list" item="item" open="(" separator="," close=")">
        #{item.uuid}
    </foreach>
    </delete>

<select id="getStorageInfoSlavePage"
            resultType="cn.iocoder.zj.module.monitor.dal.dataobject.storageinfo.StorageInfoDO">
    SELECT info.*  FROM
    system_platform_tenant plat
    LEFT JOIN monitor_storage_info info ON info.platform_id = plat.platform_id and plat.deleted = 0
    where info.deleted = 0
    <if test="pageReqVO.tenantId != null and pageReqVO.tenantId.size>0">
        and plat.tenant_id in
        <foreach collection="pageReqVO.tenantId" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </if>
    <if test="pageReqVO.name != null and pageReqVO.name !=''">
        and info.name like concat("%",#{pageReqVO.name},"%")
    </if>
    <if test="pageReqVO.platformName != null and pageReqVO.platformName !=''">
        and info.platform_name like concat("%",#{pageReqVO.platformName},"%")
    </if>
    <if test="pageReqVO.status != null and pageReqVO.status !=''">
        and info.status = #{pageReqVO.status}
    </if>
    <if test="pageReqVO.platformId != null">
        and info.platform_id = #{pageReqVO.platformId}
    </if>
    <if test="ids.size()>0">
        and info.uuid not in
        <foreach item="item" index="index" collection="ids"  open="(" separator="," close=")">
            #{item}
        </foreach>
    </if>
    GROUP BY info.uuid
    ORDER BY info.name DESC
    </select>

    <select id="getStorageListByUuids"
            resultType="cn.iocoder.zj.module.monitor.dal.dataobject.storageinfo.StorageInfoDO">
        SELECT
        info.*,
        pConfig.region_id,
        pConfig.region_name
        FROM
        monitor_storage_info info
        LEFT JOIN system_platform_config pConfig ON pConfig.id = info.platform_id and pConfig.deleted = 0
        WHERE
        info.deleted = 0
        AND info.uuid IN
        <foreach collection="uuidList" open="(" separator="," close=")" item="uuid">
            #{uuid}
        </foreach>
        GROUP BY info.uuid
    </select>

    <select id="selectStorageList" resultType="cn.iocoder.zj.module.monitor.dal.dataobject.storageinfo.StorageInfoDO">
        SELECT
        info.*
        FROM
        monitor_storage_info info
        WHERE
        info.uuid NOT IN ( SELECT host_uuid FROM monitor_alarm_host_relation relation WHERE relation.deleted = 0 )
        AND info.deleted = 0
        <if test="pageVO.name != null and pageVO.name !=''">
            and info.name like concat("%",#{pageVO.name},"%")
        </if>
        <if test="pageVO.tenantId != null">
            AND info.platform_id in (
            SELECT platform_id from system_platform_tenant pt
            WHERE pt.tenant_id in
            <foreach collection="pageVO.tenantId" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            and pt.deleted = 0)
        </if>
        ORDER BY
        info.create_time DESC
    </select>

    <select id="getStorageInfoList" resultType="cn.iocoder.zj.module.monitor.dal.dataobject.storageinfo.StorageInfoDO">
        SELECT info.*  FROM
        system_platform_tenant plat
        LEFT JOIN monitor_storage_info info ON info.platform_id = plat.platform_id and plat.deleted = 0
        <if test="reqVO.tagIds != null and reqVO.tagIds !=''">
            inner join monitor_taggables taggables on taggables.taggable_id = info.id and taggables.taggable_type = '${@<EMAIL>()}'
        </if>
        where info.deleted = 0
        <if test="reqVO.tagIds != null and reqVO.tagIds !=''">
            and taggables.tag_id in (${reqVO.tagIds}) and taggables.deleted = 0
        </if>
        <if test="reqVO.tenantId != null and reqVO.tenantId.size>0">
            and plat.tenant_id in
            <foreach collection="reqVO.tenantId" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="reqVO.name != null and reqVO.name !=''">
            and info.name like concat("%",#{reqVO.name},"%")
        </if>
        <if test="reqVO.platformName != null and reqVO.platformName !=''">
            and info.platform_name like concat("%",#{reqVO.platformName},"%")
        </if>
        <if test="reqVO.status != null and reqVO.status !=''">
            and info.status = #{reqVO.status}
        </if>
        <if test="reqVO.platformId != null">
            and info.platform_id = #{reqVO.platformId}
        </if>
        <if test="reqVO.state != null and reqVO.state !=''">
            and info.state = #{reqVO.state}
        </if>
        GROUP BY info.uuid
        ORDER BY info.create_time DESC
    </select>
    <select id="getByUuid" resultType="cn.iocoder.zj.module.monitor.dal.dataobject.storageinfo.StorageInfoDO">
        SELECT * FROM monitor_storage_info WHERE (uuid = #{uuid}) and deleted=0 group by uuid
    </select>

    <select id="getStorageStatusCount" resultType="java.util.Map">
        SELECT SUM(CASE status WHEN 'Connected' THEN 1 ELSE 0 END)                    online,
               SUM(CASE status WHEN 'Disconnected' THEN 1 ELSE 0 END)                 unonline,
               SUM(CASE status WHEN 'Connected' THEN 0 WHEN 'Disconnected' THEN 0 ELSE 1 END) other,
               count(1)                                                               total
        FROM
        (select b.* from
                (SELECT * FROM system_platform_tenant where deleted = 0 GROUP BY platform_id, tenant_id) a
                    LEFT JOIN
        (SELECT *
         FROM monitor_storage_info
         WHERE state != "Destroyed"
           AND deleted = 0
         GROUP BY UUID) b
            ON a.platform_id = b.platform_id
        where b.deleted = 0
        <if test="tenantIds != null and tenantIds.size > 0">
            and a.deleted = 0
            and a.tenant_id in
            <foreach collection="tenantIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="platformId != null and platformId != ''">
            and b.platform_id = #{platformId}
        </if>
        group by b.uuid )c
    </select>

    <select id="selectCountByPlatfrom" resultType="java.lang.Long">
        SELECT count(*) count  from monitor_storage_info WHERE platform_id
        IN
        <foreach collection="mapList" separator="," close=")" open="(" item="platform">
            #{platform.platformId}
        </foreach>

        and deleted = 0
    </select>

    <delete id="deleteStorageInfoByplatform">
        delete from monitor_storage_info
        where platform_id = #{platformId}
    </delete>

    <select id="getStorageByPlatformId"
            resultType="cn.iocoder.zj.module.monitor.api.storage.dto.StorageRespCreateReqDTO">
        select * from monitor_storage_info where platform_id = #{id} and deleted = 0
    </select>

    <select id="getStorageByList" resultType="cn.iocoder.zj.module.monitor.controller.admin.storageinfo.vo.StorageInfoRespVO">
        SELECT
        uuid,
        name,
        capacity_utilization,
        (used_capacity*100)/total_capacity as virtualUtilization,
        virtual_capacity,
        total_physical_capacity,
        virtual_capacity,
        commit_rate,
        reserve_capacity,
        waste_capacity,
        platform_id,platform_name
        FROM
        monitor_storage_info
        WHERE  deleted = 0 AND uuid in
        <foreach item="item" index="index" collection="list"  open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
