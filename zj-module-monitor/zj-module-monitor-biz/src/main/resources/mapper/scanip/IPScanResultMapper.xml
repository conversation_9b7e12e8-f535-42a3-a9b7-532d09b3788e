<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.monitor.dal.mysql.scanip.IPScanResultMapper">
    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <delete id="deleteIPScanByIpRangeId">
        delete from ip_scan_result
        where ip_range_id = #{ipRangeId}
    </delete>

    <delete id="deleteIPScanByIpRangeIds">
        delete from ip_scan_result
        where
        ip_range_id in
        <if test="ids != null and ids.size>0">
            <foreach collection="ids" separator="," close=")" open="(" item="item">
                #{item}
            </foreach>
        </if>
    </delete>

    <select id="getPage" resultType="cn.iocoder.zj.module.monitor.controller.admin.scanip.vo.IPScanResultVO">
        SELECT
        isr.*,
        i.ip_ranges
        FROM
        ip_scan_result isr
        LEFT JOIN ip_range i ON i.id = isr.ip_range_id
        WHERE
        isr.deleted = 0
        AND (  isr.snmp_status = 1 OR  isr.tcp_status = 1 OR  isr.ping_status = 1 )
        <if test="pageVO.tenantIds != null and pageVO.tenantIds.size>0">
            and isr.tenant_id in
            <foreach collection="pageVO.tenantIds" separator="," close=")" open="(" item="item">
                #{item}
            </foreach>
        </if>
        <if test="pageVO.ips != null and pageVO.ips.size>0">
            and isr.ip_address in
            <foreach collection="pageVO.ips" separator="," close=")" open="(" item="item">
                #{item}
            </foreach>
        </if>

        <if test="pageVO.ipRangeId != null">
            and isr.ip_range_id = #{pageVO.ipRangeId}
        </if>

        <if test="pageVO.ipRanges != null">
            and i.ip_ranges like concat("%",#{pageVO.ipRanges},"%")
        </if>

        <if test="pageVO.remark != null and pageVO.remark !=''">
            and isr.remark = #{pageVO.remark}
        </if>

        <if test="pageVO.ipAddress != null and pageVO.ipAddress !=''">
            and isr.ip_address like concat("%",#{pageVO.ipAddress},"%")
        </if>
        <if test="pageVO.platformId != null">
            AND isr.platform_id = #{pageVO.platformId}
        </if>
        <if test="pageVO.startTime != '' and pageVO.endTime != ''and pageVO.startTime != null and pageVO.endTime != null">
            and DATE_FORMAT( isr.update_time, "%Y-%m-%d %H:%i:%s" )  between DATE_FORMAT( #{pageVO.startTime}, "%Y-%m-%d %H:%i:%s" ) and  DATE_FORMAT( #{pageVO.endTime}, "%Y-%m-%d %H:%i:%s" )
        </if>
        <if test="pageVO.typeName != null and pageVO.typeName !=''">
            <if test="pageVO.pingStatus != null">
                and isr.ping_status = 1
            </if>
            <if test="pageVO.snmpStatus != null">
                and isr.snmp_status = 1
            </if>
            <if test="pageVO.tcpStatus != null">
                and isr.tcp_status = 1
            </if>
        </if>
        ORDER BY
        <if test="pageVO.sortBy == null or pageVO.sortBy =='' or pageVO.sortDirection== null or pageVO.sortDirection ==''">
            isr.create_time DESC
        </if>
        <if test="pageVO.sortBy != null and pageVO.sortBy !='' and pageVO.sortDirection!= null and pageVO.sortDirection !=''">
            ${pageVO.sortBy} ${pageVO.sortDirection}
        </if>
    </select>

    <select id="getCollectByTenantId" resultType="java.lang.String">
        SELECT
           collector_name
        FROM
            hzb_collector_platform
        WHERE
            platform_id = #{tenantId}
            LIMIT 1
    </select>

    <select id="getMonitorByIds" resultType="java.util.Map">
        SELECT
            `host`,
            platform_id
        FROM
            `hzb_monitor`
        WHERE
            platform_id =  #{platformId} and `host` in
        <foreach collection="collect" separator="," close=")" open="(" item="item">
            #{item}
        </foreach>
    </select>

</mapper>
