<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.monitor.dal.mysql.scanip.IPRangeMapper">
    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="getPage" resultType="cn.iocoder.zj.module.monitor.dal.dataobject.scanip.IPRangeDO">
        SELECT
        r.*,
        IFNULL(s.total_count, 0) as succCount
        FROM ip_range r
        LEFT JOIN (
        SELECT
        ip_range_id,
        COUNT(DISTINCT id) as total_count
        FROM ip_scan_result
        WHERE deleted = 0
        AND (snmp_status = 1 OR tcp_status = 1 OR ping_status = 1)
        GROUP BY ip_range_id
        ) s ON r.id = s.ip_range_id
        WHERE r.deleted = 0
        <if test="pageVO.tenantIds != null and pageVO.tenantIds.size>0">
            AND r.tenant_id in
            <foreach collection="pageVO.tenantIds" separator="," close=")" open="(" item="item">
                #{item}
            </foreach>
        </if>
        <if test="pageVO.status != null">
            AND r.status = #{pageVO.status}
        </if>
        <if test="pageVO.name != null and pageVO.name !=''">
            AND r.name like concat("%",#{pageVO.name},"%")
        </if>
        <if test="pageVO.platformId != null">
            AND r.platform_id = #{pageVO.platformId}
        </if>
        <if test="pageVO.ipRanges != null and pageVO.ipRanges !=''">
            AND r.ip_ranges like concat("%",#{pageVO.ipRanges},"%")
        </if>

        <if test="pageVO.startTime != '' and pageVO.endTime != ''and pageVO.startTime != null and pageVO.endTime != null">
            and DATE_FORMAT( r.create_time, "%Y-%m-%d %H:%i:%s" )  between DATE_FORMAT( #{pageVO.startTime}, "%Y-%m-%d %H:%i:%s" ) and  DATE_FORMAT( #{pageVO.endTime}, "%Y-%m-%d %H:%i:%s" )
        </if>
        ORDER BY
        <if test="pageVO.sortBy == null or pageVO.sortBy =='' or pageVO.sortDirection== null or pageVO.sortDirection ==''">
            r.create_time DESC,r.name DESC
        </if>
        <if test="pageVO.sortBy != null and pageVO.sortBy !='' and pageVO.sortDirection!= null and pageVO.sortDirection !=''">
            <choose>
                <when test="pageVO.sortBy == 'succ_count'">
                    succCount ${pageVO.sortDirection}
                </when>
                <otherwise>
                    r.${pageVO.sortBy} ${pageVO.sortDirection}
                </otherwise>
            </choose>
        </if>

    </select>

    <update id="updateBatchStatus">
        UPDATE ip_range
        SET status = #{status}
        WHERE deleted = 0 and id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getTaskConfigs" resultType="java.util.Map">
        SELECT
            task_id AS task_name,
            cron_exp AS cron_exp
        FROM
            ip_range
        WHERE
            STATUS = 1
          AND deleted = 0
    </select>

    <delete id="delById">
        delete from ip_range
        where id = #{id}
    </delete>

    <delete id="deleteBatchByIds">
        delete from ip_range
        where
        id in
        <if test="ids != null and ids.size>0">
            <foreach collection="ids" separator="," close=")" open="(" item="item">
                #{item}
            </foreach>
        </if>
    </delete>
</mapper>
