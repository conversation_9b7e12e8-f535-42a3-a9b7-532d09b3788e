<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.monitor.dal.mysql.home.devopsscreen.DevOpsScreenMapper">

<select id="getResourceDistribution" resultType="java.util.Map">
    SELECT
        info.*
    FROM
        (
            (
                SELECT 'host' AS app, count( `host`.id ) count,
	IFNULL( SUM( CASE WHEN `host`.state = 'Running' THEN 1 ELSE 0 END ), 0 ) enabled,
	IFNULL( SUM( CASE WHEN `host`.state = 'Stopped' THEN 1 ELSE 0 END ), 0 ) disabled,
	IFNULL( SUM( CASE WHEN `host`.state != 'Running' AND `host`.state != 'Stopped' THEN 1 ELSE 0 END ), 0 ) other
                FROM
                    monitor_host_info `host`
                WHERE
                    `host`.deleted = 0
                  AND `host`.state != 'Destroyed'
            ) UNION ALL
            (
                SELECT 'hardware' AS app, count( hardware.id ) count,
	IFNULL( SUM( CASE WHEN hardware.state = 'Enabled' THEN 1 ELSE 0 END ), 0 ) enabled,
	IFNULL( SUM( CASE WHEN hardware.state = 'Disabled' THEN 1 ELSE 0 END ), 0 ) disabled,
	IFNULL( SUM( CASE WHEN hardware.state != 'Enabled' AND hardware.state != 'Disabled' THEN 1 ELSE 0 END ), 0 ) other
                FROM
                    monitor_hardware_info hardware
                WHERE
                    hardware.deleted = 0
            ) UNION ALL
            (
                SELECT 'storage' AS app, count( sto.id ) `count`,
                    IFNULL( SUM( CASE WHEN sto.`status` = 'Connected' THEN 1 ELSE 0 END ), 0 ) enabled,
                    IFNULL( SUM( CASE WHEN sto.`status` = 'Disconnected' THEN 1 ELSE 0 END ), 0 ) disabled,
                    IFNULL( SUM( CASE WHEN sto.`status` != 'Connected' AND sto.`status` != 'Disconnected' THEN 1 ELSE 0 END ), 0 ) other
                FROM
                    monitor_storage_info sto
                WHERE
                    sto.deleted = 0
            ) UNION ALL
            (
                SELECT hzb.app app, count( hzb.id ) `count`,
                    IFNULL( SUM( CASE WHEN hzb.`status` = 1 THEN 1 ELSE 0 END ), 0 ) enabled,
                    IFNULL( SUM( CASE WHEN hzb.`status` = 2 THEN 1 ELSE 0 END ), 0 ) disabled,
                    IFNULL( SUM( CASE WHEN hzb.`status` != 2 AND hzb.`status` != 1 THEN 1 ELSE 0 END ), 0 ) other
                FROM
                    hzb_monitor hzb
                GROUP BY
                    hzb.app
            )
        ) info
</select>

<select id="getLoginRanking" resultType="java.util.Map">
    SELECT
        user_ip ip,
        count(id) `count`
    FROM
        system_login_log
    GROUP BY
        user_ip
    ORDER BY count(id) desc
        limit 5
    </select>

<select id="getRunningAsset" resultType="java.util.Map">
    SELECT
        SUM(info.count) total,
        IFNULL( SUM(info.enabled),0) running
    FROM
        (
            (
                SELECT count( `host`.id ) `count`,
			    SUM( CASE WHEN `host`.state = 'Running' THEN 1 ELSE 0 END ) enabled
                FROM
                    monitor_host_info `host`
                WHERE
                    `host`.deleted = 0
                  AND `host`.state != 'Destroyed'
            ) UNION ALL
            (
                SELECT count( hardware.id ) `count`,
			    SUM( CASE WHEN hardware.state = 'Enabled' THEN 1 ELSE 0 END ) enabled
                FROM
                    monitor_hardware_info hardware
                WHERE
                    hardware.deleted = 0
            ) UNION ALL
            (
                SELECT count( sto.id ) `count`,
			    SUM( CASE WHEN sto.`status` = 'Connected' THEN 1 ELSE 0 END ) enabled
                FROM
                    monitor_storage_info sto
                WHERE
                    sto.deleted = 0
            ) UNION ALL
            (
                SELECT count( hzb.id ) `count`,
			    SUM( CASE WHEN hzb.`status` = 1 THEN 1 ELSE 0 END ) enabled
                FROM
                    hzb_monitor hzb
                GROUP BY
                    hzb.app
            )
        ) info
</select>

<select id="getUserCount" resultType="java.util.Map">
    SELECT count(id) usersCount from system_users WHERE deleted = 0
    </select>

    <select id="abnormalUsage" resultType="java.util.Map">
        SELECT asset_name       assetName,
               cpu,
               mem,
               disk,
               abnormal_item as abnormalItem,
               asset_type,
               platform_name    platformName
        FROM om_inspection_logs il
                 JOIN (SELECT uuid
                       FROM om_inspection_record
                       ORDER BY create_time desc
                       limit 1) ir on il.record_uuid = ir.uuid

        WHERE result = '异常'
          and abnormal_item is not null and cpu is not null
        <if test="platformId!=null and platformId != ''">
                and platform_id = #{platformId}
            </if>
        ORDER BY create_time desc
    </select>

    <select id="storageExceptions" resultType="java.util.Map">
        SELECT name,
               capacity_utilization                                               as capacityUtilization,
               ifnull(CONVERT(used_capacity / total_capacity, DECIMAL(15, 2)), 0) AS virtualUtilization,
               platform_name                                                         platformName
        FROM (SELECT asset_name assetName, asset_uuid, asset_type, platform_name platformName
              FROM om_inspection_logs il
                       JOIN (SELECT uuid
                             FROM om_inspection_record
                             ORDER BY create_time desc
                             limit 1) ir on il.record_uuid = ir.uuid
              WHERE result = '异常'
                and abnormal_item is not null
                and asset_type = 'storage'
        <if test="platformId!=null and platformId != ''">
            and platform_id = #{platformId}
        </if>
              ORDER BY create_time desc) ds
                 LEFT JOIN monitor_storage_info msi on msi.uuid = ds.asset_uuid
        ORDER BY capacity_utilization desc
    </select>
</mapper>