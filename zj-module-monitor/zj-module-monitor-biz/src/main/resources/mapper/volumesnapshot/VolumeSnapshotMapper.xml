<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.monitor.dal.mysql.volumesnapshot.VolumeSnapshotMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


<update id="updateSnapshotBatch">
    UPDATE monitor_volume_snapshot
    <trim prefix="set" suffixOverrides=",">
        <trim prefix="platform_id = case" suffix="end,">
            <foreach collection="updateDos" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.platformId}
            </foreach>
        </trim>
        <trim prefix="platform_name = case" suffix="end,">
            <foreach collection="updateDos" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.platformName}
            </foreach>
        </trim>
        <trim prefix="name = case" suffix="end,">
            <foreach collection="updateDos" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.name}
            </foreach>
        </trim>
        <trim prefix="description = case" suffix="end,">
            <foreach collection="updateDos" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.description}
            </foreach>
        </trim>
        <trim prefix="host_uuid = case" suffix="end,">
            <foreach collection="updateDos" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.hostUuid}
            </foreach>
        </trim>
        <trim prefix="host_name = case" suffix="end,">
            <foreach collection="updateDos" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.hostName}
            </foreach>
        </trim>
        <trim prefix="volume_uuid = case" suffix="end,">
            <foreach collection="updateDos" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.volumeUuid}
            </foreach>
        </trim>
        <trim prefix="volume_name = case" suffix="end,">
            <foreach collection="updateDos" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.volumeName}
            </foreach>
        </trim>
        <trim prefix="type = case" suffix="end,">
            <foreach collection="updateDos" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.type}
            </foreach>
        </trim>
        <trim prefix="volume_type = case" suffix="end,">
            <foreach collection="updateDos" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.volumeType}
            </foreach>
        </trim>
        <trim prefix="primary_storage_uuid = case" suffix="end,">
            <foreach collection="updateDos" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.primaryStorageUuid}
            </foreach>
        </trim>
        <trim prefix="latest = case" suffix="end,">
            <foreach collection="updateDos" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.latest}
            </foreach>
        </trim>
        <trim prefix="create_time = case" suffix="end,">
            <foreach collection="updateDos" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.createTime}
            </foreach>
        </trim>
        <trim prefix="size = case" suffix="end,">
            <foreach collection="updateDos" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.size}
            </foreach>
        </trim>
        <trim prefix="status = case" suffix="end,">
            <foreach collection="updateDos" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.status}
            </foreach>
        </trim>
        <trim prefix="v_create_date = case" suffix="end,">
            <foreach collection="updateDos" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.vCreateDate}
            </foreach>
        </trim>
        <trim prefix="v_update_date = case" suffix="end,">
            <foreach collection="updateDos" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.vUpdateDate}
            </foreach>
        </trim>
        <trim prefix="install_path = case" suffix="end,">
            <foreach collection="updateDos" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.installPath}
            </foreach>
        </trim>
        <trim prefix="format = case" suffix="end,">
            <foreach collection="updateDos" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.format}
            </foreach>
        </trim>
        <trim prefix="deleted = case" suffix="end,">
            <foreach collection="updateDos" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.deleted}
            </foreach>
        </trim>
        <trim prefix="is_memory = case" suffix="end,">
            <foreach collection="updateDos" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.isMemory}
            </foreach>
        </trim>
    </trim>
    where uuid in
    <foreach close=")" collection="updateDos" item="item" open="(" separator=", ">
        #{item.uuid,jdbcType=VARCHAR}
    </foreach>
    </update>

<select id="getVolumeSnapshotPage"
            resultType="cn.iocoder.zj.module.monitor.dal.dataobject.volumesnapshot.VolumeSnapshotDO">
    SELECT * FROM monitor_volume_snapshot
    WHERE
            volume_uuid IN (
            SELECT
                uuid
            FROM
                monitor_volume_info
            WHERE
                vm_instance_uuid = #{pageReqVO.hostUuid}
        )
    <if test="pageReqVO.queryData != null and pageReqVO.queryData!=''">
        and name like concat("%",#{pageReqVO.queryData},"%")
    </if>
    </select>

    <delete id="deleteVolumeSnapshotByplatform">
        delete from monitor_volume_snapshot
        where platform_id = #{platformId}
    </delete>

    <select id="getVolumeSnapshotStatusCount" resultType="java.util.Map">
        SELECT
            COUNT(1) AS total,
            SUM(CASE WHEN status = 'Enabled' THEN 1 ELSE 0 END) AS available,
            SUM(CASE WHEN status != 'Enabled' THEN 1 ELSE 0 END) AS other
        FROM
        (select b.* from
        (SELECT * FROM system_platform_tenant WHERE deleted = 0 GROUP BY platform_id
        <if test="tenantIds != null and tenantIds.size>0">
            ,tenant_id
        </if>
        ) a
        LEFT JOIN
        (SELECT * FROM monitor_volume_snapshot
        WHERE deleted = 0
        GROUP BY UUID) b
        ON a.platform_id=b.platform_id
        where b.deleted = 0
        <if test="tenantIds!= null and tenantIds.size>0">
            and a.deleted = 0
            and a.tenant_id in
            <foreach collection="tenantIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="platformId!= null and platformId!= ''">
            and b.platform_id = #{platformId}
        </if>
        group by b.uuid )c
    </select>
    <select id="getVolumeSnapshotByPlatformId"
            resultType="cn.iocoder.zj.module.monitor.api.valume.dto.VolumeSnapshotDTO">
        SELECT * FROM monitor_volume_snapshot WHERE deleted = 0 and platform_id = #{platformId}
    </select>

    <select id="getsnapByList" resultType="cn.iocoder.zj.module.monitor.api.valume.dto.VolumeSnapshotDTO">
        SELECT
        uuid,
        NAME,
        IFNULL( size, 0 ) AS size,
        platform_id,
        platform_name
        FROM
        monitor_volume_snapshot
        WHERE  deleted = 0 AND uuid in
        <foreach item="item" index="index" collection="list"  open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getVolumeSnapshotByHostUuids"
            resultType="cn.iocoder.zj.module.monitor.dal.dataobject.volumesnapshot.VolumeSnapshotDO">
        SELECT * FROM monitor_volume_snapshot WHERE deleted = 0 and host_uuid in
        <foreach item="item" index="index" collection="hostUuids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
