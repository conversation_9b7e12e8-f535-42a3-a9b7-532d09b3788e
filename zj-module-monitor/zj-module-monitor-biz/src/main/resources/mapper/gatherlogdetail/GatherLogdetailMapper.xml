<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.monitor.dal.mysql.gatherlogdetail.GatherLogdetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


<update id="updateState">
    UPDATE monitor_gather_logdetail
    SET alarm_state = 1
        WHERE id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>


<select id="getGatherLogdetailPage"
            resultType="cn.iocoder.zj.module.monitor.dal.dataobject.gatherlogdetail.GatherLogdetailDO">
    select * from monitor_gather_logdetail
    </select>

    <select id="getPageInfo"
            resultType="cn.iocoder.zj.module.monitor.controller.admin.gatherlogdetail.vo.GatherLogdetailRespVO">
        SELECT
            log.*,
            asset.device_name,
            asset.device_Type,
            asset.id as device_id
        FROM
            monitor_gather_logdetail log
                LEFT JOIN monitor_gather_asset asset ON log.uuid = asset.uuid and log.ip = asset.ip
        where log.deleted = 0
        and
        log.platform_id IN (
        SELECT
        platform_id
        FROM
        system_platform_tenant
        WHERE
        tenant_id = ( SELECT tenant_id FROM system_users WHERE id = #{user.id} )
        )
        AND deleted = 0
        <if test="pageVO.platformId!=null and pageVO.platformId!=''">
            and log.platform_id = #{pageVO.platformId}
        </if>
        <if test="pageVO.deviceType != null and pageVO.deviceType !=''">
            and asset.device_type = #{pageVO.deviceType}
        </if>
        <if test="pageVO.hostName != null and pageVO.hostName !=''">

            and log.host_name like concat("%",#{pageVO.hostName},"%")
        </if>
        order by alarm_date desc
    </select>

    <select id="getLatestLog"
            resultType="cn.iocoder.zj.module.monitor.controller.admin.gatherlogdetail.vo.GatherLogdetailRespVO">
        SELECT
            *
        FROM
            (
                SELECT
                    log.*,
                    asset.device_name,
                    asset.device_Type
                FROM
                    monitor_gather_logdetail log
                        LEFT JOIN monitor_gather_asset asset ON log.uuid = asset.uuid
                WHERE
                    log.deleted = 0
                AND log.platform_id IN ( SELECT platform_id FROM system_platform_tenant WHERE tenant_id = ( SELECT tenant_id FROM system_users WHERE id = #{user.id} ) AND deleted = 0 )
                  AND log.alarm_state = 0
                GROUP BY
                    log.id
            ) c
        ORDER BY
            c.alarm_date DESC
            LIMIT 5
    </select>
    <update id="solvedGather">
        update monitor_gather_logdetail
        set   is_solved = 2
        where id = #{gatherId}
    </update>

    <update id="gatherWorkOrder">
        update monitor_gather_logdetail
        set   is_solved = 1
        where id = #{gatherId}
    </update>

    <update id="cleanWorkOrder">
        update monitor_gather_logdetail
        set   is_solved = 0
        where id = #{gatherId}
    </update>

    <select id="getGatherLogdetailById"
            resultType="cn.iocoder.zj.module.monitor.controller.admin.gatherlogdetail.vo.GatherLogdetailRespVO">
        SELECT
            log.*,
            asset.device_name,
            asset.device_Type
        FROM
            monitor_gather_logdetail log
                LEFT JOIN monitor_gather_asset asset ON log.uuid = asset.uuid and log.ip = asset.ip
        where log.deleted = 0 and log.id = #{id}
    </select>

    <update id="updateIsSolved">
        UPDATE monitor_gather_logdetail
        SET is_solved = 3
        WHERE id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>
</mapper>
