<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.monitor.dal.mysql.gatherasset.GatherAssetMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


<select id="selectByUuId" resultType="java.util.Map">
    select uuid,ip,community,version,snmp_port,host_name,device_type,device_name,alarm_location,platform_id,platform_name,
    sys_type,sys_name,online_type

    from monitor_gather_asset where uuid = #{assetUuid} and deleted = 0
    </select>

<select id="selectByAssetIp" resultType="java.lang.Long">
    select count(*) count
    from monitor_gather_asset where ip = #{ip} and platform_id = #{platformId} and deleted = 0
    </select>

<update id="updateOnlieType">
    <foreach collection="list" item="item" separator=";" >
        update monitor_gather_asset set online_type = #{item.onlineType}
        where  uuid = #{item.uuid} and ip = #{item.ip}
    </foreach>
</update>

    <update id="updateBatchByList" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update monitor_gather_asset
            set cpu_usage    = #{item.cpuUsage,jdbcType=DOUBLE},
                disk_usage   = #{item.diskUsage,javaType=DOUBLE},
                memory_usage = #{item.memoryUsage,javaType=DOUBLE}
            where uuid = #{item.uuid}
              and ip = #{item.ip}
        </foreach>
    </update>

<select id="selectCountByUuid" resultType="int">
    select count(*)count from monitor_gather_asset where uuid = #{uuid}
    </select>

    <select id="getGatherAssetStatusCount" resultType="java.util.Map">
        SELECT SUM(if(online_type = 1,1,0)) assetRunningNum,
        SUM(if(online_type = 0,1,0)) assetStoppedNum,
        SUM(if(online_type is null,1,0)) otherStateNum,
        count(1) assteNum
        FROM
        (SELECT * FROM system_platform_tenant WHERE deleted = 0 GROUP BY platform_id
        <if test="tenantIds != null and tenantIds.size>0">
            ,tenant_id
        </if>
        ) a
        LEFT JOIN
        (SELECT * FROM monitor_gather_asset) b
            ON a.platform_id=b.platform_id
        where b.deleted = 0
        <if test="tenantIds != null and tenantIds.size>0">
            and a.deleted = 0
            and a.tenant_id in
            <foreach collection="tenantIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="platformId != null and platformId != ''">
            and b.platform_id = #{platformId}
        </if>
    </select>


</mapper>
