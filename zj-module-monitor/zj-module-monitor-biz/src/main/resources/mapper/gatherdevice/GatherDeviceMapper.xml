<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.monitor.dal.mysql.gatherdevice.GatherDeviceMapper">
    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <insert id="insertDevice">
        insert into monitor_gather_device(uuid, gather_ip, gather_name)
        values (#{uuid}, #{gatherIp}, #{gatherName})
    </insert>

    <select id="selectCountDeviceByUUid" resultType="int">
        select count(*)count from monitor_gather_device where uuid = #{uuid} and deleted = 0
    </select>

    <select id="selectCountByPlatformId" resultType="java.lang.Long">
        select count(*)count from monitor_gather_device where platform_id = #{platformId} and deleted = 0
    </select>

    <select id="getGatherDeviceByIdList" resultType="java.lang.String">
        select uuid from monitor_gather_device where deleted = 0
    </select>

    <update id="updateOnlineType">
        <foreach collection="uuid" item="item" separator=";" >
            update monitor_gather_device set online_type = #{type}
            where  uuid = #{item}
        </foreach>
    </update>
</mapper>
