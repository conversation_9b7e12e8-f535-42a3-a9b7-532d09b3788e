<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.monitor.dal.mysql.hostinfo.HostInfoMapper">
    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id="getCount" resultType="int">
        select count(*)
        from monitor_host_info
        where type_name = #{typeName}
    </select>

    <select id="getHostStatusCount" resultType="map">
        SELECT
        SUM( CASE state WHEN 'Running' THEN 1 ELSE 0 END ) hostRunningNum,
        SUM( CASE state WHEN 'Stopped' THEN 1 ELSE 0 END ) hostStoppedNum,
        SUM( CASE state WHEN 'Running' THEN 0 WHEN 'Stopped' THEN 0 ELSE 1 END ) hostOtherNum,
        count( 1 ) hostNum
        FROM
        (select b.* from
            (SELECT * FROM system_platform_tenant WHERE deleted = 0 GROUP BY platform_id
        <if test="tenantIds != null and tenantIds.size>0">
            ,tenant_id
        </if>
        ) a
            LEFT JOIN
        (SELECT * FROM monitor_host_info
        WHERE state != "Destroyed"
        AND deleted = 0
        GROUP BY UUID) b
        ON a.platform_id=b.platform_id
        where b.deleted = 0
        <if test="tenantIds != null and tenantIds.size>0">
            and a.deleted = 0
            and a.tenant_id in
            <foreach collection="tenantIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="platformId != null and platformId != ''">
            and b.platform_id = #{platformId}
        </if>
        group by b.uuid )c
    </select>

    <select id="getCpuTop" resultType="map">
        SELECT NAME,
        cpu_used
        FROM
            (SELECT * FROM system_platform_tenant WHERE deleted = 0 GROUP BY platform_id
        <if test="tenantIds != null and tenantIds.size>0">
            ,tenant_id
        </if>
             ) a
            LEFT JOIN
        monitor_host_info b
            ON a.platform_id=b.platform_id
        where 1=1 AND b.deleted = 0
        <if test="tenantIds != null and tenantIds.size>0">
            and a.tenant_id in
            <foreach collection="tenantIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="platformId != null and platformId != ''">
            and b.platform_id = #{platformId}
        </if>
        <if test="regionId != null and regionId != ''">
            and b.region_id like concat(#{regionId},"%")
        </if>
        ORDER BY
        cpu_used DESC
        LIMIT 0,#{top}
    </select>

    <select id="getMemoryTop" resultType="map">
        SELECT NAME,
        memory_used
        FROM
            (SELECT * FROM system_platform_tenant WHERE deleted = 0 GROUP BY platform_id
        <if test="tenantIds != null and tenantIds.size>0">
            ,tenant_id
        </if>
        ) a
            LEFT JOIN
        monitor_host_info b
            ON a.platform_id=b.platform_id
        where 1=1 AND b.deleted = 0
        <if test="tenantIds != null and tenantIds.size>0">
            and a.tenant_id in
            <foreach collection="tenantIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="platformId != null and platformId != ''">
            and b.platform_id = #{platformId}
        </if>
        <if test="regionId != null and regionId != ''">
            and b.region_id like concat(#{regionId},"%")
        </if>
        ORDER BY
        memory_used DESC
        LIMIT 0,#{top}
    </select>

    <select id="getIsDisplay" resultType="INT">
        SELECT is_display
        FROM
        monitor_homepage_module
        where 1=1
        <if test="tenantId != null and tenantId != ''">
            and tenant_id = #{tenantId}
        </if>
        <if test="monitorEntry != null and monitorEntry != ''">
            and monitor_entry = #{monitorEntry}
        </if>
    </select>

    <select id="getAllDisplay" resultType="string">
        SELECT display_monitor_entry
        FROM
        monitor_homepage_display
        where 1=1
        <if test="tenantId != null and tenantId != ''">
            and tenant_id = #{tenantId}
        </if>
    </select>

    <update id="updateDisplay">
        update
        monitor_homepage_display
        set layout_config=#{layoutConfig}
        where 1=1
        <if test="tenantId != null and tenantId != ''">
            and tenant_id = #{tenantId}
        </if>
    </update>

    <select id="getModuleList" resultType="map">
        SELECT
        module,
        module_name,
        CASE
        WHEN sum( CASE WHEN tenant_id IS NULL THEN 1 ELSE 0 END )= 0 THEN
        0 ELSE 1
        END AS selected
        FROM
        monitor_homepage_module a
        LEFT JOIN ( SELECT * FROM monitor_homepage_display
        where 1=1
        <if test="tenantId != null and tenantId != ''">
            and tenant_id = #{tenantId}
        </if>) b ON b.layout_config LIKE concat( '%', a.monitor_entry, '%' )
        GROUP BY
        module,
        module_name;
    </select>

    <select id="getResourceType" resultType="map">
        SELECT
        resource_type,
        resource_type_name,
        CASE
        WHEN sum( CASE WHEN tenant_id IS NULL THEN 1 ELSE 0 END )= 0 THEN
        0 ELSE 1
        END AS selected
        FROM
        monitor_homepage_module a
        LEFT JOIN ( SELECT * FROM monitor_homepage_display
        where 1=1
        <if test="tenantId != null and tenantId != ''">
            and tenant_id = #{tenantId}
        </if>) b ON b.layout_config LIKE concat( '%', a.monitor_entry, '%' )
        WHERE
        module = #{module}
        GROUP BY
        resource_type,
        resource_type_name;
    </select>

    <select id="getMonitorEntry" resultType="map">
        SELECT
        monitor_entry,
        monitor_entry_name,
        CASE
        WHEN tenant_id IS NULL THEN
        1 ELSE 0
        END AS selected
        FROM
        monitor_homepage_module a
        LEFT JOIN ( SELECT * FROM monitor_homepage_display
        where 1=1
        <if test="tenantId != null and tenantId != ''">
            and tenant_id = #{tenantId}
        </if>) b ON b.layout_config LIKE concat( '%', a.monitor_entry, '%' )
        WHERE
        module = #{module}
        AND resource_type = #{resourceType}
    </select>

    <select id="getLayoutConfig" resultType="map">
        select layout_config layoutConfig
        from monitor_homepage_display
        where tenant_id = #{tenantId}
    </select>

    <select id="getDMonitorEntry" resultType="string">
        select monitor_entry monitorEntry
        from monitor_homepage_module
    </select>

    <select id="getEntryCount" resultType="int">
        select count(*)
        from monitor_homepage_module
        where module = #{module}
    </select>

    <update id="updateHostInfoList" parameterType="java.util.List">
        <!--@mbg.generated-->
        update monitor_host_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.name,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`state` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.state,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ip = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.ip,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="vip_ip = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.vipIp,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cluster_uuid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.clusterUuid,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="image_uuid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.imageUuid,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="zone_uuid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.zoneUuid,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="hardware_uuid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.hardwareUuid,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="architecture = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.architecture,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="guest_os_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.guestOsType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="v_create_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.vCreateDate}
                </foreach>
            </trim>
            <trim prefix="`type` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.type,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="memory_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.memorySize,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="memory_used = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.memoryUsed,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="disk_used = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.diskUsed,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="cpu_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.cpuNum,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="mac = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.mac,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="updater = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.updater,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="tenant_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.tenantId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="cluster_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.clusterName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="zone_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.zoneName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="hardware_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.hardwareName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="network_in_bytes = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.networkInBytes,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="network_out_bytes = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.networkOutBytes,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="disk_used_bytes = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.diskUsedBytes,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="disk_free_bytes = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.diskFreeBytes,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="total_disk_capacity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.totalDiskCapacity,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="actual_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.actualSize,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="cloud_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.cloudSize,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="network_in_packets = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.networkInBytes,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="network_out_packets = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.networkOutBytes,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="platform_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.platformId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="platform_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.platformName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="region_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.regionId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.deleted,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="power_state = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.powerState,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="image_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.imageName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="iso = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.iso,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="auto_init_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.autoInitType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="guide_mode = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.guideMode,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where uuid in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.uuid,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="selectTenantHostUUID" resultType="java.lang.String">
        SELECT distinct uuid
        from monitor_host_info
        where deleted = 0
          and tenant_id = #{param1,jdbcType=VARCHAR}
    </select>

    <select id="getHostInfoPage" resultType="cn.iocoder.zj.module.monitor.dal.dataobject.hostinfo.HostInfoDO">
        SELECT info.*,	info.create_time as createTime FROM
        system_platform_tenant plat
        LEFT JOIN monitor_host_info info ON info.platform_id = plat.platform_id AND plat.deleted = 0
        <if test="pageReqVO.tagIds != null and pageReqVO.tagIds !=''">
            inner join monitor_taggables taggables on taggables.taggable_id = info.id and taggables.taggable_type = '${@<EMAIL>()}'
        </if>
        where info.deleted = 0
        and info.state != 'Destroyed'
        <if test="pageReqVO.tagIds != null and pageReqVO.tagIds !=''">
            and taggables.tag_id in (${pageReqVO.tagIds}) and taggables.deleted = 0
        </if>
        <if test="pageReqVO.tenantId != null and pageReqVO.tenantId.size>0">
            and plat.tenant_id in
            <foreach collection="pageReqVO.tenantId" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="pageReqVO.queryData != null and pageReqVO.queryData !=''">
            and(info.name like concat("%",#{pageReqVO.queryData},"%")
            or info.ip like concat("%",#{pageReqVO.queryData},"%")
            or info.mac like concat("%",#{pageReqVO.queryData},"%"))
        </if>
        <if test="pageReqVO.state != null and pageReqVO.state !=''">
            <if test="pageReqVO.state != 'Unknown'">
                and info.state = #{pageReqVO.state}
            </if>
            <if test="pageReqVO.state == 'Unknown'">
                and info.state != 'Expunging'
                and info.state != 'Paused'
                and info.state != 'Running'
                and info.state != 'Stopped'
            </if>
        </if>
        <if test="pageReqVO.hardWareUuid != null and pageReqVO.hardWareUuid !=''">
            and info.hardware_uuid = #{pageReqVO.hardWareUuid}
        </if>
        <if test="pageReqVO.clusterUuid != null and pageReqVO.clusterUuid !=''">
            and info.cluster_uuid = #{pageReqVO.clusterUuid}
        </if>
        <if test="pageReqVO.startTime != '' and pageReqVO.endtime != ''and pageReqVO.startTime != null and pageReqVO.endtime != null">
            and DATE_FORMAT( info.v_create_date, "%Y-%m-%d %H:%i:%s" ) between #{pageReqVO.startTime} and
            #{pageReqVO.endTime}
        </if>
        <if test="pageReqVO.vipIp != null and pageReqVO.vipIp !=''">
            and info.vip_ip like concat("%",#{pageReqVO.vipIp},"%")
        </if>
        <if test="pageReqVO.architecture != null and pageReqVO.architecture !=''">
            and info.architecture like concat("%",#{pageReqVO.architecture},"%")
        </if>
        <if test="pageReqVO.name != null and pageReqVO.name !=''">
            and info.name like concat("%",#{pageReqVO.name},"%")
        </if>
        <if test="pageReqVO.ip != null and pageReqVO.ip !=''">
            and info.ip like concat("%",#{pageReqVO.ip},"%")
        </if>
        <if test="pageReqVO.platformName != null and pageReqVO.platformName !=''">
            and info.platform_name like concat("%",#{pageReqVO.platformName},"%")
        </if>
        <if test="ids.size()>0">
            and info.uuid not in
            <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="pageReqVO.platformId != null">
            and info.platform_id = #{pageReqVO.platformId}
        </if>
        <if test="pageReqVO.inPks != null and !pageReqVO.inPks.isEmpty()">
            AND info.id IN
            <foreach collection="pageReqVO.inPks" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
                GROUP BY info.uuid
        ORDER BY
        <if test="pageReqVO.sortBy == null or pageReqVO.sortBy =='' or pageReqVO.sortDirection== null or pageReqVO.sortDirection ==''">
            info.v_create_date,info.name DESC
        </if>
        <if test="pageReqVO.sortBy != null and pageReqVO.sortBy !='' and pageReqVO.sortDirection!= null and pageReqVO.sortDirection !=''">
             info.${pageReqVO.sortBy} ${pageReqVO.sortDirection}
        </if>
    </select>

    <select id="alarmHostList" resultType="cn.iocoder.zj.module.monitor.dal.dataobject.hostinfo.HostInfoDO">
        SELECT
        hostInfo.*
        FROM
        monitor_host_info hostInfo
        WHERE
        hostInfo.uuid NOT IN ( SELECT host_uuid FROM monitor_alarm_host_relation relation WHERE relation.deleted = 0 )
        AND hostInfo.deleted = 0
        and hostInfo.state != 'Destroyed'
        <if test="pageReqVO.name != null and pageReqVO.name !=''">
            and hostInfo.name like concat("%",#{pageReqVO.name},"%")
        </if>
        <if test="pageReqVO.tenantId != null and pageReqVO.tenantId.size>0">
            AND hostInfo.platform_id in (SELECT platform_id from system_platform_tenant pt WHERE
            pt.tenant_id = in
            <foreach collection="pageReqVO.tenantId" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            AND pt.deleted = 0)
        </if>
        ORDER BY
        hostInfo.create_time DESC
    </select>

    <select id="getRegionId" resultType="long">
        select distinct b.region_id from
        <if test="tenantId != null and tenantId !=''">
            system_platform_tenant a left join
        </if>
        system_platform_config b
        <if test="tenantId != null and tenantId != ''">
            on a.platform_id=b.id
        </if>
        where b.deleted=0
        <if test="tenantId != null and tenantId !=''">
            and a.tenant_id=#{tenantId} and a.deleted = 0
        </if>
        <if test="regionId != null and regionId != ''">
            and b.region_id like concat(#{regionId},"%")
        </if>
    </select>
    <select id="getPlatformInfos" resultType="map">
        select distinct b.id,b.name from
        <if test="tenantId != null and tenantId !=''">
            system_platform_tenant a left join
        </if>
        system_platform_config b
        <if test="tenantId != null and tenantId != ''">
            on a.platform_id=b.id
        </if>
        where b.deleted=0
        <if test="tenantId != null and tenantId !=''">
            and a.tenant_id=#{tenantId} and a.deleted = 0
        </if>
        <if test="regionId != null and regionId != ''">
            and b.region_id like concat(#{regionId},"%")
        </if>
    </select>

    <select id="selectListByUuid" resultType="cn.iocoder.zj.module.monitor.dal.dataobject.hostinfo.HostInfoDO">
        SELECT
        info.*,
        pConfig.region_id,
        pConfig.region_name
        FROM
        monitor_host_info info
        LEFT JOIN system_platform_config pConfig ON pConfig.id = info.platform_id and pConfig.deleted = 0
        WHERE
        info.deleted = 0
        AND info.uuid IN
        <foreach collection="uuidList" open="(" separator="," close=")" item="uuid">
            #{uuid}
        </foreach>
        GROUP BY info.uuid
    </select>

    <select id="selectByTenant" resultType="cn.iocoder.zj.module.monitor.dal.dataobject.hostinfo.HostInfoDO">
        SELECT
        info.*
        FROM
        monitor_host_info info
        LEFT JOIN system_platform_config config ON info.platform_id = config.id
        <if test="tenantId!=null">
            LEFT JOIN system_platform_tenant pt ON config.id = pt.platform_id
            AND pt.tenant_id = #{tenantId} AND pt.id is not null and pt.deleted = 0
        </if>
        WHERE info.state != "Destroyed"
    </select>

    <select id="getplatformName" resultType="string">
        SELECT NAME
        FROM system_platform_config
        WHERE id = #{platformId}
          AND deleted = 0
    </select>

    <update id="updateByUuid">
        update monitor_host_info
        set state = #{hostInfoDO.state}
        where uuid = #{hostInfoDO.uuid}
    </update>

    <delete id="deleteHostList">
        DELETE FROM monitor_host_info
        WHERE uuid IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.uuid}
        </foreach>
    </delete>
    <select id="getRecourseByPlatform" resultType="java.util.Map">
        SELECT uuid,platform_id,platform_Name,
        <if test="reqVO.sourceType == 'host'">
            name,
            "云主机" as sourceType
            from monitor_host_info
            WHERE platform_id = #{reqVO.platformId} and state != "Destroyed" and deleted = 0
            <if test="reqVO.queryData != null and reqVO.queryData!=''">
                and name like concat("%",#{reqVO.queryData},"%")
            </if>
            group by uuid
        </if>
        <if test="reqVO.sourceType == 'hardware'">
            name,
            "宿主机" as sourceType
            from monitor_hardware_info
            WHERE platform_id = #{reqVO.platformId} and state != "Destroyed" and deleted = 0
            <if test="reqVO.queryData != null and reqVO.queryData!=''">
                and name like concat("%",#{reqVO.queryData},"%")
            </if>
            group by uuid
        </if>
        <if test="reqVO.sourceType == 'storage'">
            name,
            "主存储" as sourceType
            from monitor_storage_info
            WHERE platform_id = #{reqVO.platformId} and deleted = 0
            <if test="reqVO.queryData != null and reqVO.queryData!=''">
                and name like concat("%",#{reqVO.queryData},"%")
            </if>
            group by uuid
        </if>
        <if test="reqVO.sourceType == 'gather_asset'">
            host_name as name,
            "基础设施" as sourceType
            from monitor_gather_asset
            WHERE platform_id = #{reqVO.platformId} and deleted = 0
            <if test="reqVO.queryData != null and reqVO.queryData!=''">
                and host_name like concat("%",#{reqVO.queryData},"%")
            </if>
        </if>
        order by create_time
    </select>

    <select id="getHostInfoSlavePagePage" resultType="cn.iocoder.zj.module.monitor.dal.dataobject.hostinfo.HostInfoDO">
        SELECT info.* FROM
        system_platform_tenant plat
        LEFT JOIN monitor_host_info info ON info.platform_id = plat.platform_id and plat.deleted = 0
        where info.deleted = 0
        and info.state != 'Destroyed'
        <if test="pageReqVO.tenantId != null and pageReqVO.tenantId.size>0">
            and plat.tenant_id in
            <foreach collection="pageReqVO.tenantId" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="pageReqVO.queryData != null and pageReqVO.queryData !=''">
            and(info.name like concat("%",#{pageReqVO.queryData},"%")
            or info.ip like concat("%",#{pageReqVO.queryData},"%")
            or info.mac like concat("%",#{pageReqVO.queryData},"%"))
        </if>
        <if test="pageReqVO.platformName != null and pageReqVO.platformName !=''">
            and info.platform_name like concat("%",#{pageReqVO.platformName},"%"))
        </if>
        <if test="pageReqVO.state != null and pageReqVO.state !=''">
            and info.state = #{pageReqVO.state}
        </if>
        <if test="pageReqVO.name != null and pageReqVO.name !=''">
            and info.name like concat("%",#{pageReqVO.name},"%")
        </if>
        <if test="ids.size()>0">
            and info.uuid not in
            <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="pageReqVO.platformId != null">
            and info.platform_id = #{pageReqVO.platformId}
        </if>
        GROUP BY info.uuid
        ORDER BY info.name DESC
    </select>

    <select id="getClusterSimpleInfo" resultType="java.util.Map">
        SELECT
        cluster_name clusterName,
        cluster_uuid clusterUuid
        FROM monitor_host_info
        WHERE (cluster_name is NOT null and cluster_name != "")
        <if test="platformId.size > 0">
            and platform_id in
            <foreach item="item" index="index" collection="platformId" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY cluster_name
    </select>

    <select id="getExportInfoList" resultType="cn.iocoder.zj.module.monitor.dal.dataobject.hostinfo.HostInfoDO">
        SELECT
        info.*
        FROM
        monitor_host_info info
        <if test="reqVO.tagIds != null and reqVO.tagIds !=''">
            inner join monitor_taggables taggables on taggables.taggable_id = info.id and taggables.taggable_type = '${@<EMAIL>()}'
        </if>
        WHERE info.deleted = 0
        <if test="reqVO.tagIds != null and reqVO.tagIds !=''">
            and taggables.tag_id in (${reqVO.tagIds}) and taggables.deleted = 0
        </if>
        <if test="reqVO.tenantId != null and reqVO.tenantId.size>0">
            and info.platform_id in (SELECT platform_id FROM system_platform_tenant WHERE tenant_id IN
            <foreach collection="reqVO.tenantId" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            AND deleted = 0)
        </if>
        AND info.state != 'Destroyed'
        <if test="reqVO.state != null and reqVO.state !=''">
            and info.state = #{reqVO.state}
        </if>
        <if test="reqVO.clusterUuid != null and reqVO.clusterUuid !=''">
            and info.cluster_uuid = #{reqVO.clusterUuid}
        </if>
        <if test="reqVO.startTime != '' and reqVO.endtime != ''and reqVO.startTime != null and reqVO.endtime != null">
            and DATE_FORMAT( info.create_time, "%Y-%m-%d %H:%i:%s" ) between #{reqVO.startTime} and #{reqVO.endTime}
        </if>
        <if test="reqVO.name != null and reqVO.name !=''">
            and info.name like concat("%",#{reqVO.name},"%")
        </if>
        <if test="reqVO.platformId != null">
            and info.platform_id = #{reqVO.platformId}
        </if>
        GROUP BY info.uuid
        ORDER BY info.create_time DESC
    </select>

    <select id="getDuplicateDataIds" resultType="java.util.Map">
        SELECT max(id) as id,
               uuid
        FROM monitor_host_info
        WHERE 1 = 1
        GROUP BY uuid
        HAVING count(1) > 1
    </select>

    <delete id="removeDuplicateData">
        delete from monitor_host_info
        where
        <foreach collection="duplicateDatas" item="data" separator="or" open="(" close=")">
            id != #{data.id} and uuid = #{data.uuid}
        </foreach>
    </delete>

    <select id="getResourceAppList" resultType="java.util.Map">
        SELECT id as uuid,name,app from hzb_monitor  WHERE status = 1 and platform_id = #{platformId}
    </select>

    <select id="getResourcesList" resultType="java.util.Map">
        SELECT id as uuid,name,app,status from hzb_monitor
        <if test="platform != null and platform.size > 0">
            WHERE
            platform_id in
        <foreach close=")" collection="platform" item="item" open="(" separator=", ">
            #{item.platformId,jdbcType=VARCHAR}
        </foreach>
        </if>
    </select>

    <select id="getHostNameByuuid" resultType="java.util.Map">
        SELECT  uuid,name from monitor_host_info  WHERE
        uuid in
        <foreach close=")" collection="hostcpus" item="item" open="(" separator=", ">
            #{item.uuid,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="selectCountByPlatfrom" resultType="java.lang.Long">
        SELECT count(*) count  from monitor_host_info WHERE platform_id
        IN
        <foreach collection="mapList" separator="," close=")" open="(" item="platform">
            #{platform.platformId}
        </foreach>
        and  state != "Destroyed"
        and deleted = 0
    </select>

    <delete id="deleteByPlatformId">
        delete from monitor_host_info
        where platform_id = #{id}
    </delete>

    <select id="getHostAlarmByType" resultType="java.util.Map">
        SELECT
            SUM(IF(target like "%.cpu%",1,0)) CPU,
            SUM(IF(target like "%.memory%",1,0)) Memory,
            SUM(IF(target like "%.disk%",1,0)) `Disk`
        FROM
            hzb_alert
        WHERE
            app = "host"
        <if test="reqVo.reckonTime != null and reqVo.reckonTime != ''">
            and gmt_create > DATE_SUB(NOW(), INTERVAL SUBSTRING(#{reqVo.reckonTime}, 1, LENGTH(#{reqVo.reckonTime}) - 1) DAY)
        </if>
        <if test="reqVo.uuid != null and reqVo.uuid != ''">
            and monitor_id =#{reqVo.uuid}
        </if>
        <if test="reqVo.platformId != null and reqVo.platformId != ''">
            and platform_id =#{reqVo.platformId}
        </if>
        <if test="reqVo.PlatformList.size>0 and reqVo.PlatformList != null">
            and platform_id in
            <foreach collection="reqVo.PlatformList" item="platfoemId" separator="," open="(" close=")" >
                #{platfoemId}
            </foreach>
        </if>
    </select>

    <delete id="deletMonitorByPlatformId">
        delete from hzb_monitor
        where platform_id = #{platformId}
    </delete>

    <select id="getVmByPlatformId" resultType="cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO">
        select  * from monitor_host_info where platform_id = #{platformId} and deleted = 0
    </select>

    <select id="findPlatformName" resultType="java.lang.Long">
        select platform_id from hzb_collector where name = #{platformName}
    </select>

    <select id="findMonitorsByPlatformIdAndHost" resultType="org.apache.hertzbeat.common.entity.manager.Monitor">
        select * from hzb_monitor where platform_id = #{platformId} and host = #{hostName}
    </select>

    <insert id="addAlertList">
        insert into hzb_alert
        (id,alert_define_id,content,creator,first_alarm_time,
        gmt_create,gmt_update,last_alarm_time,modifier,priority,
        status,tags,target,times,platform_id,platform_name,
        monitor_id,resource_type,app,is_solved,alarm_id,alarm_name,monitor_name
        )
        values
        <foreach collection="list" item="item" index= "index" separator =",">
            (#{item.id},#{item.alertDefineId},#{item.content},#{item.creator},#{item.firstAlarmTime},#{item.gmtCreate},#{item.gmtUpdate},
            #{item.lastAlarmTime},#{item.modifier},#{item.priority},#{item.status},#{item.tags},#{item.target},
            #{item.times},#{item.platformId},#{item.platformName},#{item.monitorId},#{item.resourceType},#{item.app},
            #{item.isSolved},#{item.alarmId},#{item.alarmName},#{item.monitorName})
        </foreach>
    </insert>

    <select id="findMaxId" resultType="java.lang.Long">
        select id from hzb_alert order by id desc limit 1
    </select>
    <select id="getCloudByPlatformId"
            resultType="cn.iocoder.zj.module.monitor.api.cloud.dto.CloudRespCreateReqDTO">
        select * from monitor_host_info   where platform_id = #{platformId} and deleted = 0
    </select>

    <select id="getMonitorAsset" resultType="java.util.Map">
        select id,asset_id,authorization_type from om_monitor_asset where
        deleted = 0 and asset_type = 2
        <if test="platformId != null and platformId !=''">
            and platform_id = #{platformId}
        </if>
    </select>

    <select id="getHostCount" resultType="java.lang.Integer">
        SELECT
            SUM( count )
        FROM
            ( SELECT COUNT( 1 ) AS count FROM monitor_host_info WHERE deleted = 0 UNION ALL SELECT COUNT( 1 ) AS
                count FROM hzb_monitor WHERE
                  status != 0  ) AS counts;
    </select>

    <delete id="deletMonitorAssetByPlatformId">
        delete from om_monitor_asset
        where platform_id = #{platformId}
    </delete>

    <select id="getUserListByTenantId" resultType="java.lang.String">
        SELECT ro.userId
        FROM (SELECT urole.user_id userId,
                     role.`code`
              FROM system_user_role urole
                       LEFT JOIN system_role role ON role.id = urole.role_id
                  AND role.tenant_id = #{tenantId}
              WHERE urole.deleted = 0
                AND urole.tenant_id = #{tenantId}
              GROUP BY urole.user_id) ro
        WHERE ro.`code` = 'tenant_admin'
    </select>

    <select id="getTenantInfo" resultType="java.lang.String">
        select contact_user_id from system_tenant where id = #{tenantId}
    </select>

    <select id="getOpenIdByUserId" resultType="java.lang.String">
        select open_id from system_wechat_binding where user_id = #{userId}
    </select>

    <select id="getVmInfoMemoryUsedTopByPlatformId" resultType="java.util.Map">
        select name,memory_used as value from monitor_host_info WHERE deleted = 0
        <if test="platformId.size>0">
            and platform_id in
            <foreach
                    collection="platformId" open="(" separator="," close=")" item="platform">
                #{platform.platformId}
            </foreach>
        </if>
        ORDER BY memory_used desc
        limit 5
    </select>

    <select id="getVmInfoCpuUsedTopByPlatformId" resultType="java.util.Map">
        select name,cpu_used as value from monitor_host_info WHERE deleted = 0
        <if test="platformId.size>0">
            and platform_id in
            <foreach
                    collection="platformId" open="(" separator="," close=")" item="platform">
                #{platform.platformId}
            </foreach>
        </if>
        ORDER BY cpu_used desc
        limit 5
    </select>

    <update id="updateAuthorizationType">
        UPDATE monitor_host_info
        SET authorization_type = #{authorizationType},
            authorization_time = #{authorizationTime}
        WHERE id = #{id}
    </update>


    <select id="getHostByList" resultType="cn.iocoder.zj.module.monitor.dal.dataobject.hostinfo.HostInfoDO">
        SELECT
        h.uuid,
        h.name,
        h.cpu_used,
        h.memory_used,
        h.disk_used,
        h.cpu_num,
        h.memory_size,
        COALESCE(SUM(CASE WHEN v.type = 'root' THEN v.size ELSE 0 END), 0) AS system_disk_size,
        COALESCE(SUM(CASE WHEN v.type = 'data' THEN v.size ELSE 0 END), 0) AS data_disk_size,
        h.platform_id,
        h.platform_name
        FROM
        monitor_host_info h
        LEFT JOIN
        monitor_volume_info v ON v.vm_instance_uuid = h.uuid
        WHERE  h.deleted = 0 AND h.uuid in
        <foreach item="item" index="index" collection="list"  open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY
        h.uuid
    </select>

    <select id="getByPlatformIdAndTags" resultType="cn.iocoder.zj.module.monitor.dal.dataobject.hostinfo.HostInfoDO">
        SELECT name, uuid, tag, cpu_num, memory_size, ip
        FROM monitor_host_info m
        WHERE m.deleted = 0
        AND m.type_name IN ('zstack', 'winhong')
        <if test="platformIds != null and platformIds.size() > 0">
            AND m.platform_id IN
            <foreach collection="platformIds" open="(" separator="," close=")" item="platformId">
                #{platformId}
            </foreach>
        </if>
        <if test="tagNames != null and tagNames.size() > 0">
            AND (
            <foreach collection="tagNames" item="tagName" separator="OR">
                m.tag LIKE CONCAT('%', #{tagName}, '%')
            </foreach>
            OR m.id IN (
            SELECT mt.taggable_id
            FROM monitor_taggables mt
            JOIN monitor_tags t ON t.id = mt.tag_id
            WHERE mt.deleted = 0 AND t.deleted = 0
            <if test="tagNames != null and tagNames.size() > 0">
                AND (
                <foreach collection="tagNames" item="tagName" separator="OR">
                    t.name LIKE CONCAT('%', #{tagName}, '%')
                </foreach>
                )
            </if>
            )
            )
        </if>
    </select>


    <select id="getListByTag" resultType="cn.iocoder.zj.module.monitor.taskTime.cache.TaggableDO">
        SELECT
            id,
            uuid,
            NAME,
            platform_id,
            tag
        FROM
            monitor_host_info
        WHERE
            deleted = 0
        GROUP BY
            uuid,platform_id
    </select>
</mapper>
