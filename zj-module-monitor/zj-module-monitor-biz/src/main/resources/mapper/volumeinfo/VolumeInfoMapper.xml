<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.monitor.dal.mysql.volumeinfo.VolumeInfoMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


<select id="getAttachableVmCount" resultType="java.lang.Long">
    SELECT
        COUNT(*)
    FROM monitor_volume_attachable_vm
    </select>

<select id="getAllVolumeAttachableVm"
            resultType="cn.iocoder.zj.module.monitor.api.valume.dto.VolumeAttachableVmDTO">
    SELECT *
    FROM monitor_volume_attachable_vm
    </select>

<insert id="addVolumeAttachableVmBatch">
    INSERT INTO monitor_volume_attachable_vm
        (volume_uuid,volume_name,host_uuid,host_name,platform_id,platform_name)
        VALUES 
            <foreach collection="shardingData" item="item" index="index" separator=",">
                (#{item.volumeUuid},#{item.volumeName},#{item.hostUuid},#{item.hostName},#{item.platformId},#{item.platformName})
            </foreach>
    </insert>

<update id="updateVolumeAttachableVmBatch">
    UPDATE monitor_volume_attachable_vm
    <trim prefix="set" suffixOverrides=",">
        <trim prefix="volume_name = case" suffix="end,">
            <foreach collection="shardingData" index="index" item="item">
                when volume_uuid = #{item.volumeUuid,jdbcType=VARCHAR} and host_uuid = #{item.hostUuid}
                then #{item.volumeName,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="host_uuid = case" suffix="end,">
            <foreach collection="shardingData" index="index" item="item">
                when volume_uuid = #{item.volumeUuid,jdbcType=VARCHAR} and host_uuid = #{item.hostUuid}
                then #{item.hostUuid,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="host_name = case" suffix="end,">
            <foreach collection="shardingData" index="index" item="item">
                when volume_uuid = #{item.volumeUuid,jdbcType=VARCHAR} and host_uuid = #{item.hostUuid}
                then #{item.hostName,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="platform_id = case" suffix="end,">
            <foreach collection="shardingData" index="index" item="item">
                when volume_uuid = #{item.volumeUuid,jdbcType=VARCHAR} and host_uuid = #{item.hostUuid}
                then #{item.platformId,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="platform_name = case" suffix="end,">
            <foreach collection="shardingData" index="index" item="item">
                when volume_uuid = #{item.volumeUuid,jdbcType=VARCHAR} and host_uuid = #{item.hostUuid}
                then #{item.platformName,jdbcType=VARCHAR}
            </foreach>
        </trim>
    </trim>
    WHERE 1=1
    AND
    <foreach close=")" collection="shardingData" item="item" open="(" separator="OR ">
        volume_uuid =#{item.volumeUuid,jdbcType=VARCHAR} and host_uuid = #{item.hostUuid}
    </foreach>
    </update>

<delete id="delVolumeAttachableVms">
    DELETE FROM monitor_volume_attachable_vm
    WHERE 1=1 AND
        <foreach collection="deleteTarget" open="(" close=")" separator="OR " item="item">
            volume_uuid =#{item.volumeUuid,jdbcType=VARCHAR} and host_uuid = #{item.hostUuid}
        </foreach>
    </delete>

<select id="getVolumeAttachableVmByHostUuid"
            resultType="cn.iocoder.zj.module.monitor.controller.admin.volumeinfo.vo.VolumeInfoRespVO">
    SELECT
        *
    FROM
        monitor_volume_info
    WHERE
            uuid IN (
            SELECT
                volume_uuid
            FROM
                monitor_volume_attachable_vm
            WHERE
                host_uuid = #{hostUuid})
    <if test="queryData != null and queryData != ''">
        and name like concat("%",#{queryData},"%")
    </if>
    </select>

<update id="updateVolumeBatch">
    UPDATE monitor_volume_info
    <trim prefix="set" suffixOverrides=",">
        <trim prefix="platform_id = case" suffix="end,">
            <foreach collection="updateReqVOs" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.platformId}
            </foreach>
        </trim>
        <trim prefix="platform_name = case" suffix="end,">
            <foreach collection="updateReqVOs" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.platformName}
            </foreach>
        </trim>
        <trim prefix="name = case" suffix="end,">
            <foreach collection="updateReqVOs" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.name}
            </foreach>
        </trim>
        <trim prefix="description = case" suffix="end,">
            <foreach collection="updateReqVOs" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.description}
            </foreach>
        </trim>
        <trim prefix="primary_storage_uuid = case" suffix="end,">
            <foreach collection="updateReqVOs" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.primaryStorageUuid}
            </foreach>
        </trim>
        <trim prefix="primary_storage_name = case" suffix="end,">
            <foreach collection="updateReqVOs" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.primaryStorageName}
            </foreach>
        </trim>
        <trim prefix="primary_storage_type = case" suffix="end,">
            <foreach collection="updateReqVOs" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.primaryStorageType}
            </foreach>
        </trim>
        <trim prefix="vm_instance_uuid = case" suffix="end,">
            <foreach collection="updateReqVOs" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.vmInstanceUuid}
            </foreach>
        </trim>
        <trim prefix="vm_instance_name = case" suffix="end,">
            <foreach collection="updateReqVOs" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.vmInstanceName}
            </foreach>
        </trim>
        <trim prefix="type = case" suffix="end,">
            <foreach collection="updateReqVOs" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.type}
            </foreach>
        </trim>
        <trim prefix="format = case" suffix="end,">
            <foreach collection="updateReqVOs" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.format}
            </foreach>
        </trim>
        <trim prefix="size = case" suffix="end,">
            <foreach collection="updateReqVOs" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.size}
            </foreach>
        </trim>
        <trim prefix="actual_size = case" suffix="end,">
            <foreach collection="updateReqVOs" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.actualSize}
            </foreach>
        </trim>
        <trim prefix="state = case" suffix="end,">
            <foreach collection="updateReqVOs" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.state}
            </foreach>
        </trim>
        <trim prefix="status = case" suffix="end,">
            <foreach collection="updateReqVOs" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.status}
            </foreach>
        </trim>
        <trim prefix="actual_ratio = case" suffix="end,">
            <foreach collection="updateReqVOs" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.actualRatio}
            </foreach>
        </trim>
        <trim prefix="actual_free = case" suffix="end,">
            <foreach collection="updateReqVOs" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.actualFree}
            </foreach>
        </trim>
        <trim prefix="actual_use = case" suffix="end,">
            <foreach collection="updateReqVOs" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.actualUse}
            </foreach>
        </trim>

        <trim prefix="deleted = case" suffix="end,">
            <foreach collection="updateReqVOs" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.deleted}
            </foreach>
        </trim>

        <trim prefix="v_create_date = case" suffix="end,">
            <foreach collection="updateReqVOs" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.vCreateDate}
            </foreach>
        </trim>

        <trim prefix="v_update_date = case" suffix="end,">
            <foreach collection="updateReqVOs" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.vUpdateDate}
            </foreach>
        </trim>

        <trim prefix="max_iops = case" suffix="end,">
            <foreach collection="updateReqVOs" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.maxIops}
            </foreach>
        </trim>

        <trim prefix="throughput = case" suffix="end,">
            <foreach collection="updateReqVOs" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.throughput}
            </foreach>
        </trim>

        <trim prefix="media_type = case" suffix="end,">
            <foreach collection="updateReqVOs" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.mediaType}
            </foreach>
        </trim>

        <trim prefix="is_mount = case" suffix="end,">
            <foreach collection="updateReqVOs" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR}
                then #{item.isMount}
            </foreach>
        </trim>

    </trim>
    where uuid in
    <foreach close=")" collection="updateReqVOs" item="item" open="(" separator=", ">
        #{item.uuid,jdbcType=VARCHAR}
    </foreach>
    </update>

<select id="getNotUsedVolum" resultType="cn.iocoder.zj.module.monitor.dal.dataobject.volumeinfo.VolumeInfoDO">
    select * from monitor_volume_info info
    where 1=1
    <if test="id != -1">
        and  info.status = 'Ready' and info.type = 'Data'
        and info.platform_id = #{id}
    </if>
    AND info.platform_id IN (
    SELECT
    config.id
    FROM
    system_platform_config config
    WHERE
    config.deleted = 0
    AND config.type_code = #{typeCode})
    </select>

<delete id="delVolumes">
    DELETE FROM monitor_volume_info
    WHERE 1=1 AND
    <foreach collection="deleteTarget" open="(" close=")" separator="OR " item="item">
        uuid =#{item.uuid,jdbcType=VARCHAR}
    </foreach>
    </delete>

<delete id="delVolumeSnapshots">
    DELETE FROM monitor_volume_snapshot
    WHERE 1=1 AND
    <foreach collection="deleteTarget" open="(" close=")" separator="OR " item="item">
        uuid =#{item.uuid,jdbcType=VARCHAR}
    </foreach>
    </delete>
    <delete id="delVolumeByInstanceUuid">
        DELETE FROM monitor_volume_info
        WHERE 1=1 AND vm_instance_uuid in
        <foreach collection="deleteList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>

    <delete id="delVolumesByplatform">
        delete from monitor_volume_info
        where platform_id = #{platformId}
    </delete>

    <update id="delVolumeAttachableVmByplatform">
        update monitor_volume_attachable_vm
        set deleted = 1
        where platform_id = #{platformId}
    </update>

    <select id="getVolumeByPlatformId" resultType="cn.iocoder.zj.module.monitor.api.valume.dto.VolumeDTO">
        select * from monitor_volume_info where platform_id = #{id} and deleted = 0 and uuid != ''
        GROUP BY uuid
    </select>
    <select id="getVolumeByVmUuid" resultType="java.lang.String">
        SELECT
            uuid
        FROM
            monitor_volume_info
        WHERE
            vm_instance_uuid = #{domainId}
          AND type = 'Root'
          AND deleted = 0
            LIMIT 1
    </select>

    <select id="getVolumesByPlatformId" resultType="cn.iocoder.zj.module.monitor.api.valume.dto.VolumeDTO">
        select * from monitor_volume_info where platform_id = #{id} and deleted = 0
    </select>
    <select id="getVolumeCountByStorageUuid" resultType="java.lang.Integer">
        SELECT DISTINCT
            count(*) AS count
        FROM
            monitor_volume_info
        WHERE
            primary_storage_uuid = #{storageUuid}
          AND deleted = 0
    </select>
    <select id="getVolumeStatusCount" resultType="java.util.Map">
        SELECT
        COUNT(1) AS total,
        SUM(CASE WHEN mvi.state = 'Enabled' THEN 1 ELSE 0 END) AS available,
        SUM(CASE WHEN mvi.state != 'Enabled' THEN 1 ELSE 0 END) AS other
        FROM
        monitor_volume_info mvi
        JOIN system_platform_tenant spt ON mvi.platform_id = spt.platform_id
        WHERE
        mvi.deleted = 0
        AND spt.deleted = 0
        <if test="tenantIds!= null and tenantIds.size>0">
            and spt.tenant_id in
            <foreach collection="tenantIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="platformId!= null and platformId!= ''">
            and spt.platform_id = #{platformId}
        </if>
    </select>

    <select id="getvolumeByList" resultType="cn.iocoder.zj.module.monitor.api.valume.dto.VolumeDTO">
        SELECT
        uuid,
        NAME,
        IFNULL( size, 0 ) AS size,
        IFNULL( actual_size, 0 ) AS actual_size,
        IFNULL( max_iops, 0 ) AS max_iops,
        IFNULL( throughput, 0 ) AS throughput,
        platform_id,
        platform_name
        FROM
        monitor_volume_info
        WHERE  deleted = 0 AND uuid in
        <foreach item="item" index="index" collection="list"  open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getByPlatformIdAndTags" resultType="cn.iocoder.zj.module.monitor.dal.dataobject.volumeinfo.VolumeInfoDO">
        SELECT
        *
        FROM
        monitor_volume_info
        WHERE  deleted = 0
        <if test="platformIds != null and platformIds.size() > 0">
            AND platform_id IN
            <foreach collection="platformIds" open="(" separator="," close=")" item="platformId">
                #{platformId}
            </foreach>
        </if>
        <if test="tagNames != null and tagNames.size() > 0">
            AND (
            <foreach collection="tagNames" item="tagName" separator="OR">
                tag LIKE CONCAT('%', #{tagName}, '%')
            </foreach>
            )
        </if>
    </select>

    <select id="getListByTag" resultType="cn.iocoder.zj.module.monitor.taskTime.cache.TaggableDO">
        SELECT
            id,
            uuid,
            NAME,
            platform_id,
            tag
        FROM
            monitor_volume_info
        WHERE
            deleted = 0
        GROUP BY
            uuid,platform_id
    </select>

</mapper>
