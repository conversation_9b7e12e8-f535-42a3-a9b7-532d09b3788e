<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.monitor.dal.mysql.taggables.TaggablesMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <update id="updateByDetel">
        update monitor_taggables
        set deleted = 1,
        update_time = now()
        where type = 1
        <if test="type != null and type != ''">
            and taggable_type = #{type}
        </if>
        <if test="collect != null and !collect.isEmpty()">
            and tag_id in
            <foreach collection="collect" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>

    <select id="selectListByType" resultType="cn.iocoder.zj.module.monitor.dal.dataobject.taggables.TaggablesDO">
        SELECT
        s.*,
        t.tag_uuid as tagUuid,
        t.tag_name as tagName
        FROM
        monitor_taggables s
        LEFT JOIN monitor_tags t ON s.tag_id = t.id
        WHERE
        s.deleted = 0
        AND s.type = 1
        <if test="comIds != null and !comIds.isEmpty()">
            and s.tag_id in
            <foreach collection="comIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="taggableType != null and taggableType != ''">
            and taggable_type = #{taggableType}
        </if>
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO monitor_taggables (
        tag_id, taggable_id, taggable_type, type,tenant_id
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.tagId}, #{item.taggableId}, #{item.taggableType}, #{item.type}, #{item.tenantId})
        </foreach>
    </insert>

    <update id="updateByIdDetel">
        update monitor_taggables
        set deleted = 1,
        update_time = now()
        where type = 1
        <if test="type != null and type != ''">
            and taggable_type = #{type}
        </if>
        <if test="longs != null and !longs.isEmpty()">
            and id in
            <foreach collection="longs" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="taggableIds != null and !taggableIds.isEmpty()">
            and taggable_id in
            <foreach collection="taggableIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>
</mapper>
