<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.monitor.dal.mysql.hardwarestorage.HardwareStorageMapper">


    <select id="getHardwareStorageByPlatformId"
            resultType="cn.iocoder.zj.module.monitor.api.hardwarestorage.HardWareStorageRespDTO">
        select * from  monitor_hardware_storage where platform_id = #{platformId} and deleted = 0;
    </select>
    <select id="getStorageIds" resultType="java.lang.String">
        SELECT DISTINCT
            storage_uuid
        FROM
            monitor_hardware_storage
        WHERE
            hardware_uuid = #{hardwareUuid} and deleted = 0;
    </select>
    <select id="getHareWareCountByStorageUuid" resultType="java.lang.Integer">
        SELECT DISTINCT
            count(*) AS count
        FROM
            monitor_hardware_storage
        WHERE
            storage_uuid = #{storageUuid} and deleted = 0;
    </select>
</mapper>