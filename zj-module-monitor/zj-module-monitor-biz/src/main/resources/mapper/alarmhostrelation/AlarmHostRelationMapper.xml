<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.monitor.dal.mysql.alarmhostrelation.AlarmHostRelationMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


<select id="getLatestId" resultType="java.lang.Long">
    select id from monitor_alarm_host_relation order by id desc limit 1
    </select>

<update id="updateStatus">
    update monitor_alarm_host_relation set status = #{reqVO.status}
    where id = #{reqVO.id}
    </update>

<delete id="deleteByAlarmConfigId">
    delete from monitor_alarm_host_relation where alarm_id = #{alarmConfigId}
    </delete>

<insert id="createRelationByHost">
    <if test="hostInfoList.size>0 and alarmConfigs.size>0">
    insert into monitor_alarm_host_relation
    (alarm_name,alarm_id,host_name,host_uuid,platform_config_name,platform_config_id,platform_config_type,tenant_name,tenant_id,creator)
    values
        <foreach collection="alarmConfigs" item="alarmConfig" separator=",">
            <foreach collection="hostInfoList" item="targetInfo" separator=",">
            (#{alarmConfig.alarmName},#{alarmConfig.id},#{targetInfo.name},#{targetInfo.uuid},
            #{targetInfo.platformName},#{targetInfo.platformId},#{targetInfo.typeName},#{tenant.name},#{tenant.id},#{tenant.id})
            </foreach>
        </foreach>

    </if>
</insert>

<update id="deleteByUuid">
    update monitor_alarm_host_relation set deleted = 1
    where host_uuid = #{reqVO.uuid}
    </update>

    <select id="getRelationByUuid" resultType="java.lang.Long">
        select alarm_id from monitor_alarm_host_relation where host_uuid = #{uuid} and deleted = 0
    </select>

<select id="getRelationCountByAlarmId" resultType="java.lang.Long">
    SELECT
    count(relation.id) as total
    FROM
    monitor_alarm_host_relation relation
    LEFT JOIN
    <if test="sourceType == 'monitor_alarm_host'">
        monitor_host_info sourceInfo ON relation.host_uuid = sourceInfo.uuid  and sourceInfo.deleted = 0
    </if>
    <if test="sourceType == 'monitor_alarm_hardware'">
        monitor_hardware_info sourceInfo ON relation.host_uuid = sourceInfo.uuid  and sourceInfo.deleted = 0
    </if>
    <if test="sourceType == 'monitor_alarm_disk'">
        monitor_storage_info sourceInfo ON relation.host_uuid = sourceInfo.uuid  and sourceInfo.deleted = 0
    </if>
    WHERE
    relation.alarm_id = #{alarmId}
    and relation.deleted = 0
    and sourceInfo.uuid is not null
    </select>

<select id="getRelationListInUsed"
            resultType="cn.iocoder.zj.module.monitor.dal.dataobject.alarmhostrelation.AlarmHostRelationDO">
    SELECT
        relation.*
    FROM
        monitor_alarm_host_relation relation
            LEFT JOIN monitor_alarm_config mac ON relation.alarm_id = mac.id
            AND mac.deleted = 0
    WHERE
        relation.deleted = 0
      AND mac.enabled = 1
</select>
</mapper>
