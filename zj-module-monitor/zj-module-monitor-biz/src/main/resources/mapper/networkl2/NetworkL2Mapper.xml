<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.monitor.dal.mysql.networkl2.NetworkL2Mapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


<update id="updateNetWorkL2List" parameterType="java.util.List">
    <!--@mbg.generated-->
    update monitor_network_l2
    <trim prefix="set" suffixOverrides=",">
        <trim prefix="`name` = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.name,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="physical_interface = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.physicalInterface,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="`type` = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.type,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="vlan = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.vlan,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="virtual_network_id = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.virtualNetworkId,jdbcType=INTEGER}
            </foreach>
        </trim>
        <trim prefix="tenant_id = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.tenantId,jdbcType=BIGINT}
            </foreach>
        </trim>
        <trim prefix="region_name = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.regionName,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="region_id = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.regionId,jdbcType=BIGINT}
            </foreach>
        </trim>
        <trim prefix="tenant_id = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.tenantId,jdbcType=BIGINT}
            </foreach>
        </trim>
        <trim prefix="platform_id = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.platformId,jdbcType=BIGINT}
            </foreach>
        </trim>
        <trim prefix="platform_name = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.platformName,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="type_name = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.typeName,jdbcType=VARCHAR}
            </foreach>
        </trim>


    </trim>
    where uuid in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
        #{item.uuid,jdbcType=VARCHAR}
    </foreach>
    </update>

<select id="selectListByName" resultType="java.util.Map">
    select name,uuid from monitor_network_l2 where deleted = 0
    </select>

<delete id="deleteNetWorkL2ByNameList">
    DELETE FROM monitor_network_l2
    WHERE uuid IN
    <foreach collection="list" item="item" open="(" separator="," close=")">
        #{item.uuid}
    </foreach>
    </delete>

<delete id="deleteNetWorkL3ByNameList">
    DELETE FROM monitor_network_l3
    WHERE uuid IN
    <foreach collection="list" item="item" open="(" separator="," close=")">
        #{item.uuid}
    </foreach>
    </delete>


    <delete id="deleteNetworkL2Byplatform">
        delete from monitor_network_l2
        where platform_id = #{platformId}
    </delete>

    <select id="getNetworkL2ByPlatformId" resultType="cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL2DTO">
        select * from monitor_network_l2 where platform_id = #{platformId} and deleted = 0
    </select>
</mapper>
