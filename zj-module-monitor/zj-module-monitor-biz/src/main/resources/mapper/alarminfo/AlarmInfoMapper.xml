<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.monitor.dal.mysql.alarminfo.AlarmInfoMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id="getAlertInfoById" resultType="org.apache.hertzbeat.common.entity.alerter.AlertRespVo">
        select * from hzb_alert where id = #{id}
    </select>

    <select id="getAlarmInfoByalarmId" resultType="cn.iocoder.zj.module.monitor.dal.dataobject.alarminfo.AlarmInfoDO">
        select * from monitor_alarm_info where alarm_id = #{alarmId} ORDER BY create_time desc limit 1
    </select>

    <select id="getAlertDefineById" resultType="org.apache.hertzbeat.common.entity.alerter.AlertDefine">
        select * from hzb_alert_define where id = #{alarmId}
    </select>

    <select id="getAlarmConfigDoById"
            resultType="cn.iocoder.zj.module.monitor.dal.dataobject.alarmconfig.AlarmConfigDO">
        select * from monitor_alarm_config where id = #{alarmId}
    </select>

    <update id="updateisSolvedByAlarmId">
        update hzb_alert set is_solved = 2 where id = #{alarmId}
    </update>

    <select id="getAlertInfoByMonitorId" resultType="org.apache.hertzbeat.common.entity.alerter.AlertRespVo">
        select * from hzb_alert where monitor_id in
        <foreach collection="monitorId" separator="," close=")" open="(" item="item">
            #{item}
        </foreach>
        and is_solved != 2
    </select>

</mapper>
