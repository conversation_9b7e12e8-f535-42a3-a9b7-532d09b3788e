<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.monitor.dal.mysql.tags.TagsMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="getAllList" resultType="cn.iocoder.zj.module.monitor.dal.dataobject.tags.TagsDO">
        SELECT
        m.*,t.taggable_type as taggableType
        FROM
        monitor_tags m
        LEFT JOIN monitor_taggables t ON t.tag_id = m.id
        WHERE
            m.deleted = 0
            <if test="tag.tagUuids != null and tag.tagUuids.size>0">
                and m.tag_uuid in
                <foreach collection="tag.tagUuids" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="tag.type != null and tag.type !=''">
                and m.type = #{tag.type}
            </if>
            <if test="tag.isUpdate != null and tag.isUpdate !=''">
                and m.is_update = #{tag.isUpdate}
            </if>
            <if test="tag.taggableType != null and tag.taggableType !=''">
                and t.taggable_type = #{tag.taggableType}
            </if>
            <if test="tag.platformId != null and tag.platformId !=''">
                and m.platform_id = #{tag.platformId}
            </if>
            <if test="tag.tenantId != null and tag.tenantId !=''">
                and m.tenant_id = #{tag.tenantId}
            </if>
    </select>


    <select id="getTenantByPlatform" resultType="cn.iocoder.zj.framework.common.dal.manager.TagData">
        SELECT
            platform_id as platformId,
            tenant_id as tenantId
        FROM
            system_platform_tenant
        WHERE
            deleted = 0
    </select>

    <update id="updateByTagName">
        <foreach collection="changed" item="item" separator=";">
            UPDATE monitor_tags
            SET
            tag_name = #{item.tagName},
            update_time = NOW(),
            name = CASE WHEN is_update = 0 THEN #{item.tagName} ELSE name END
            WHERE tag_uuid = #{item.tagUuid}
            AND platform_id = #{item.platformId}
            AND type = 1
            AND deleted = 0
        </foreach>
    </update>


</mapper>
