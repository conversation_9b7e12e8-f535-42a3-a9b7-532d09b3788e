<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.monitor.dal.mysql.topreport.TopReportMetricMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <delete id="delById">
        delete from top_report_metric
        where report_id = #{id}
    </delete>

    <select id="getreportmetric" resultType="cn.iocoder.zj.module.monitor.controller.admin.topreport.vo.ReportMetrics">
        SELECT
        *
        FROM
        report_metrics
        WHERE
        pid = #{id} and deleted = 0
    </select>

    <select id="getpidmetric" resultType="cn.iocoder.zj.module.monitor.controller.admin.topreport.vo.ReportMetrics">
        SELECT
            *
        FROM
            report_metrics
        WHERE
            id = #{id} and deleted = 0
    </select>


    <select id="getmetrics" resultType="java.lang.String">
        SELECT
            metric_code
        FROM
            report_metrics
        WHERE
            type = 0
    </select>

    <select id="getPids" resultType="java.util.Map">
        SELECT id, metric_code,metric_name FROM report_metrics
        WHERE id IN
        <foreach collection="pids" item="id" separator="," open="(" close=")">#{id}</foreach>
    </select>

</mapper>
