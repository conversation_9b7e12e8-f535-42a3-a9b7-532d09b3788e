<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.monitor.dal.mysql.topology.TopologyMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


<update id="updateByPlatform">
    update monitor_topology
    set topology_json = #{updateObj.topologyJson},
    platform_name = #{updateObj.platformName},
    resource_json=#{updateObj.resourceJson},
    platform_id = #{updateObj.platformId}
    where platform_id = #{updateObj.platformId}
    </update>

    <select id="selectHostState" resultType="java.util.Map">
        select uuid,state from monitor_hardware_info
        where uuid in
        <foreach collection="hostUuids" separator="," close=")" open="(" item="item">
            #{item}
        </foreach>
         and deleted = 0
    </select>

    <select id="selectAlertBystate" resultType="java.util.Map">
        SELECT max(priority) as priority
        from hzb_alert
        where monitor_id = #{uuid} and is_solved != 2
    </select>

    <select id="selectHzState" resultType="java.util.Map">
        select  id,status from hzb_monitor
        where id in
        <foreach collection="hzUuids" separator="," close=")" open="(" item="item">
            #{item}
        </foreach>
    </select>

    <select id="selectHostStates" resultType="java.util.Map">
        SELECT
        uuid,
        status
        FROM
        monitor_hardware_info
        where uuid in
        <foreach collection="hostUuids" separator="," close=")" open="(" item="item">
            #{item}
        </foreach>
        and deleted = 0
    </select>

    <select id="selectHzStates" resultType="java.util.Map">
        select id as uuid,status from hzb_monitor
        where id in
        <foreach collection="hzUuids" separator="," close=")" open="(" item="item">
            #{item}
        </foreach>
    </select>

    <select id="selectAlertStates" resultType="java.util.Map">
        select monitor_id as uuid,status from hzb_alert
        where monitor_id in
        <foreach collection="allUuids" separator="," close=")" open="(" item="item">
            #{item}
        </foreach>
        and is_solved != 2
    </select>

    <select id="selectHzStatesByList" resultType="java.util.Map">
        select id as uuid,status,name,host,platform_name,app,category from hzb_monitor
        where id in
        <foreach collection="hzUuids" separator="," close=")" open="(" item="item">
            #{item}
        </foreach>
        and platform_id =#{platformId}
    </select>

    <select id="selectHostStatesByList" resultType="java.util.Map">
        SELECT
        uuid,
        status,
        name,ip as host,
        "host" as app,
        platform_name
        FROM
        monitor_hardware_info
        where uuid in
        <foreach collection="hostUuids" separator="," close=")" open="(" item="item">
            #{item}
        </foreach>
        and deleted = 0 and platform_id =#{platformId}
    </select>

    <select id="selectAlertStatesByList" resultType="java.util.Map">
        select id, monitor_id as uuid,monitor_name as monitorName,priority,app,target,times,first_alarm_time as firstAlarmTime,last_alarm_time as lastAlarmTime,content,
               COALESCE(category, NULL) as category  from hzb_alert
        where monitor_id in
        <foreach collection="allUuids" separator="," close=")" open="(" item="item">
            #{item}
        </foreach>
         and is_solved != 2 and platform_id =#{platformId}
        ORDER BY last_alarm_time desc
    </select>
</mapper>
