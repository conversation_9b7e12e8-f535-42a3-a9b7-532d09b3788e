<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.monitor.dal.mysql.networkvpc.NetworkVpcMapper">
    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <update id="updateNetWorkVpcList" parameterType="java.util.List">
        <!--@mbg.generated-->
        update monitor_network_vpc
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.name,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cpu_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.cpuNum,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="memory_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.memorySize,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="architecture = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.architecture,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="dns = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.dns,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.status,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`state` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.state,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="l3_network_uuid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.l3NetworkUuid,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ip = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.ip,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="management_network_uuid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.managementNetworkUuid,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="management_network_ip = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.managementNetworkIp,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="l3_network_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.l3NetworkName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cluster_uuid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.clusterUuid,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cluster_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.clusterName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="hypervisor_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.hypervisorType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mac = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.mac,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="host_uuid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.hostUuid,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="host_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.hostName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="platform_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.platformId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="platform_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.platformName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="tenant_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.tenantId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.createTime}
                </foreach>
            </trim>
        </trim>
        where uuid in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.uuid,jdbcType=VARCHAR}
        </foreach>
    </update>

    <delete id="deleteNetWorkVPCByNameList">
        DELETE FROM monitor_network_vpc
        WHERE uuid  IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.uuid}
        </foreach>
    </delete>

    <select id="getClusterSimpleInfo" resultType="java.util.Map">
        SELECT
        cluster_name clusterName,
        cluster_uuid clusterUuid
        FROM monitor_network_vpc
        WHERE platform_id in
        <foreach item="item" index="index" collection="platformId"  open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY cluster_uuid
    </select>

    <delete id="deleteNetworkVpcByplatform">
        delete from monitor_network_vpc
        where platform_id = #{platformId}
    </delete>

    <select id="getnetvpcByList" resultType="cn.iocoder.zj.module.monitor.dal.dataobject.networkvpc.NetworkVpcDO">
        SELECT
        uuid,
        NAME,
        IFNULL( memory_size, 0 ) AS memorySize,
        platform_id,
        platform_name
        FROM
        monitor_network_vpc
        WHERE  deleted = 0 AND uuid in
        <foreach item="item" index="index" collection="list"  open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
