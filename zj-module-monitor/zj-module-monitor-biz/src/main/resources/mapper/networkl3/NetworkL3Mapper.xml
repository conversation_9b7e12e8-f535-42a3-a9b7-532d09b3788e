<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.monitor.dal.mysql.networkl3.NetworkL3Mapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->





    <update id="updateNetWorkL3List" parameterType="java.util.List">
        <!--@mbg.generated-->
        update monitor_network_l3
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="l2_network_uuid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.l2NetworkUuid,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="l2_network_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.l2NetworkName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.name,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="dns = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.dns,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`type` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.type,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="network_services = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.networkServices,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="start_ip = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.startIp,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="end_ip = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.endIp,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="netmask = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.netmask,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="gateway = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.gateway,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="network_segment = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.networkSegment,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="network_cidr = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.networkCidr,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="next_hop_ip = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.nextHopIp,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="tenant_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.tenantId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="platform_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.platformId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="platform_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.platformName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when uuid = #{item.uuid,jdbcType=VARCHAR} then #{item.createTime}
                </foreach>
            </trim>
        </trim>
        where uuid in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.uuid,jdbcType=VARCHAR}
        </foreach>
    </update>

    <delete id="deleteNetworkL3Byplatform">
        delete from monitor_network_l3
        where platform_id = #{platformId}
    </delete>
    <select id="getNetworkL3ByPlatformId"
            resultType="cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL3DTO">
        select * from monitor_network_l3 where platform_id = #{platformId} and deleted = 0
    </select>
</mapper>
