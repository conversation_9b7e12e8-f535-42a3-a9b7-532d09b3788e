package cn.iocoder.zj.module.monitor.controller.admin.networkvpc.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - VPC路由器 Excel 导出 Request VO，参数和 NetworkVpcPageReqVO 是一致的")
@Data
public class NetworkVpcExportReqVO {

    @Schema(description = "vpc 路由器uuid")
    private String uuid;

    @Schema(description = "路由器名称")
    private String name;

    @Schema(description = "cpu")
    private Integer cpuNum;

    @Schema(description = "内存")
    private Long memorySize;

    @Schema(description = "CPU架构")
    private String architecture;

    @Schema(description = "路由dns")
    private String dns;

    @Schema(description = "就绪状态")
    private String status;

    @Schema(description = "启用状态")
    private String state;

    @Schema(description = "L3网络uuid")
    private String l3NetworkUuid;

    @Schema(description = "ipv4 Ip")
    private String ip;

    @Schema(description = "管理网络uuid")
    private String managementNetworkUuid;

    @Schema(description = "管理网络ip")
    private String managementNetworkIp;

    @Schema(description = "三层网络名称")
    private String l3NetworkName;

    @Schema(description = "集群uuid")
    private String clusterUuid;

    @Schema(description = "集群名称")
    private String clusterName;

    @Schema(description = "虚拟化技术")
    private String hypervisorType;

    @Schema(description = "mac地址（IPV4)")
    private String mac;

    @Schema(description = "宿主机uuid")
    private String hostUuid;

    @Schema(description = "宿主机名称")
    private String hostName;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] createTime;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(required = false,description = "主键逗号拼接,'1','2','3'")
    private List<Long> inPks;
}
