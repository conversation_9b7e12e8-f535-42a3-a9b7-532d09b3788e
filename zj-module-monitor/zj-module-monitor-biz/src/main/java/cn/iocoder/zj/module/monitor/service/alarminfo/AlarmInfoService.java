package cn.iocoder.zj.module.monitor.service.alarminfo;

import java.io.IOException;
import java.util.*;
import javax.servlet.http.HttpServletResponse;
import javax.validation.*;
import cn.iocoder.zj.module.monitor.controller.admin.alarminfo.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarminfo.AlarmInfoDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import org.springframework.web.multipart.MultipartFile;

/**
 * 监控告警详情 Service 接口
 *
 * <AUTHOR>
 */
public interface AlarmInfoService {

    /**
     * 创建监控告警详情
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAlarmInfo(@Valid AlarmInfoCreateReqVO createReqVO);

    /**
     * 更新监控告警详情
     *
     * @param updateReqVO 更新信息
     */
    void updateAlarmInfo(@Valid AlarmInfoUpdateReqVO updateReqVO);

    /**
     * 删除监控告警详情
     *
     * @param id 编号
     */
    void deleteAlarmInfo(Long id);

    /**
     * 获得监控告警详情
     *
     * @param id 编号
     * @return 监控告警详情
     */
    AlarmInfoDO getAlarmInfo(Long id);

    /**
     * 获得监控告警详情列表
     *
     * @param ids 编号
     * @return 监控告警详情列表
     */
    List<AlarmInfoDO> getAlarmInfoList(Collection<Long> ids);

    /**
     * 获得监控告警详情分页
     *
     * @param pageReqVO 分页查询
     * @return 监控告警详情分页
     */
    PageResult<AlarmInfoDO> getAlarmInfoPage(AlarmInfoPageReqVO pageReqVO);

    /**
     * 获得监控告警详情列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 监控告警详情列表
     */
    List<AlarmInfoDO> getAlarmInfoList(AlarmInfoExportReqVO exportReqVO);

    Map<String, String> getAlarmInfoUpload(MultipartFile file, HttpServletResponse response) throws IOException;

    AlarmInfoByIdRespVO getAlarmInfoById(Long id);

    void batchUpdateStatus(List<AlarmInfoCreateReqVO> alarmList);
}
