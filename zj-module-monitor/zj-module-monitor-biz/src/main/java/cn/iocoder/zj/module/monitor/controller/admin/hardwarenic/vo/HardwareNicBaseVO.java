package cn.iocoder.zj.module.monitor.controller.admin.hardwarenic.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 物理机网络关系 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class HardwareNicBaseVO {

    @Schema(description = "网卡uuid(v3)")
    private String uuid;

    @Schema(description = "宿主机uuid")
    private String hardwareUuid;

    @Schema(description = "mac")
    private String mac;

    @Schema(description = "网卡类型")
    private String networkType;

    @Schema(description = "ip地址")
    private String ipAddresses;

    @Schema(description = "ip子网")
    private String ipSubnet;

    @Schema(description = "二层网络uuid")
    private String l2NetworkUuid;

    @Schema(description = "二层网络名称")
    private String l2NetworkName;

    @Schema(description = "是否离线")
    private Boolean state;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

}
