package cn.iocoder.zj.module.monitor.service.gatherdevice;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.monitor.dal.mysql.gatherasset.GatherAssetMapper;
import cn.iocoder.zj.module.monitor.dal.redis.device.AssetRedisDAO;
import cn.iocoder.zj.module.monitor.util.StringUtil;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.util.*;

import cn.iocoder.zj.module.monitor.controller.admin.gatherdevice.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.gatherdevice.GatherDeviceDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.convert.gatherdevice.GatherDeviceConvert;
import cn.iocoder.zj.module.monitor.dal.mysql.gatherdevice.GatherDeviceMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.singleton;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.*;

/**
 * 采集设备 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class GatherDeviceServiceImpl implements GatherDeviceService {

    @Resource
    private GatherDeviceMapper gatherDeviceMapper;
    @Resource
    GatherAssetMapper gatherAssetMapper;
    @Resource
    PermissionApi permissionApi;
    @Resource
    RoleApi roleApi;
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    AssetRedisDAO assetRedisDAO;

    @Override
    public Long createGatherDevice(GatherDeviceCreateReqVO createReqVO) {
        // 插入
        GatherDeviceDO gatherDevice = GatherDeviceConvert.INSTANCE.convert(createReqVO);
        gatherDeviceMapper.insert(gatherDevice);
        // 返回
        return gatherDevice.getId();
    }

    @Override
    public Long updateGatherDevice(GatherDeviceUpdateReqVO updateReqVO) {
        Long count = gatherDeviceMapper.selectCountByPlatformId(updateReqVO.getPlatformId());
        if (count > 0) {
            return -1L;
        }
        // 校验存在
        validateGatherDeviceExists(updateReqVO.getId());
        // 更新
        GatherDeviceDO updateObj = GatherDeviceConvert.INSTANCE.convert(updateReqVO);
        gatherDeviceMapper.updateById(updateObj);
        return 1L;
    }

    @Override
    public void deleteGatherDevice(Long id) {
        // 校验存在
        validateGatherDeviceExists(id);
        // 校验该设备下是否已绑定资产，如果绑定了资产需要先删除资产在进行删除设备
        GatherDeviceDO deviceDO = gatherDeviceMapper.selectById(id);
        int count = gatherAssetMapper.selectCountByUuid(deviceDO.getUuid());
        if (count == 0) {
            // 删除
            gatherDeviceMapper.deleteById(id);
        } else {
            throw exception(GATHER_DEVICE_IS_ASSET);
        }
    }

    private void validateGatherDeviceExists(Long id) {
        if (gatherDeviceMapper.selectById(id) == null) {
            throw exception(GATHER_DEVICE_NOT_EXISTS);
        }
    }

    @Override
    public GatherDeviceDO getGatherDevice(Long id) {
        return gatherDeviceMapper.selectById(id);
    }

    @Override
    public List<GatherDeviceDO> getGatherDeviceList(Collection<Long> ids) {
        return gatherDeviceMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<GatherDeviceDO> getGatherDevicePage(GatherDevicePageReqVO pageReqVO) {
        boolean admin = false;
        List<Map> platform = new ArrayList<>();
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        if (roleApi.hasAnySuperAdmin(roleIds)) {
            admin = true;
        } else {
            platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
        }
        return gatherDeviceMapper.selectPage(pageReqVO, admin, platform);
    }

    @Override
    public List<GatherDeviceDO> getGatherDeviceList(GatherDeviceExportReqVO exportReqVO) {
        return gatherDeviceMapper.selectList(exportReqVO);
    }

    @Override
    public Map createDevice(String json, String token_desc) {
        Map map = new HashMap();
        if (!SecureUtil.md5("<EMAIL>").equals(token_desc)) {
            map.put("code", 500);
            map.put("data", new HashMap<>());
            map.put("msg", "token验证失败");
            return map;
        }

        // 查询数据库是否已存在如果已存在返回存在codel;
        if (StrUtil.isNotEmpty(json)) {
            JSONObject obj = JSON.parseObject(json);
            String uuid = obj.getString("uuid");
            String gatherIp = obj.getString("gatherIp");
            String gatherName = obj.getString("gatherName");

            int count = gatherDeviceMapper.selectCountDeviceByUUid(uuid);
            if (count > 0) {
                map.put("code", 1);
                map.put("data", new HashMap<>());
                map.put("msg", "数据已存在不用新增！");
            } else {
                gatherDeviceMapper.insertDevice(uuid, gatherIp, gatherName);
                map.put("code", 0);
                map.put("data", new HashMap<>());
                map.put("msg", "新增成功");
            }
        }
        return map;
    }

    @Override
    public List<GatherDeviceDO> deviceSelect() {
        boolean admin = false;
        List<Map> platform = new ArrayList<>();
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        if (roleApi.hasAnySuperAdmin(roleIds)) {
            admin = true;
        } else {
            platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
        }

        return gatherDeviceMapper.deviceSelect(admin, platform);
    }

    @Override
    public void getOnlineById(String uuid, String tokenDesc) {
        Map map = new HashMap();
        if (!SecureUtil.md5("<EMAIL>").equals(tokenDesc)) {
            map.put("code", 500);
            map.put("msg", "token验证失败");
        }
        String id = assetRedisDAO.get(uuid);
        if (StringUtil.isNullOrEmpty(id)) {
            assetRedisDAO.set(uuid);
        }
    }
    @Override
    public List<String> getGatherDeviceByIdList() {
        return gatherDeviceMapper.getGatherDeviceByIdList();
    }

    @Override
    public void updateOnlineType(List<String> onlineType, int type) {
         gatherDeviceMapper.updateOnlineType(onlineType,type);
    }
}
