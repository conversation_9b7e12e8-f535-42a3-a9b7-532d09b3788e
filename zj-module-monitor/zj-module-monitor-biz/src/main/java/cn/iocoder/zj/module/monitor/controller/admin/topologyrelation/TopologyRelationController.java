package cn.iocoder.zj.module.monitor.controller.admin.topologyrelation;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.monitor.controller.admin.topologyrelation.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.topologyrelation.TopologyRelationDO;
import cn.iocoder.zj.module.monitor.convert.topologyrelation.TopologyRelationConvert;
import cn.iocoder.zj.module.monitor.service.topologyrelation.TopologyRelationService;

@Tag(name = "管理后台 - 拓扑图关系")
@RestController
@RequestMapping("/monitor/topology-relation")
@Validated
public class TopologyRelationController {

    @Resource
    private TopologyRelationService topologyRelationService;

    @PostMapping("/create")
    @Operation(summary = "创建拓扑图关系")
    @PreAuthorize("@ss.hasPermission('monitor:topology-relation:create')")
    public CommonResult<Long> createTopologyRelation(@Valid @RequestBody TopologyRelationCreateReqVO createReqVO) {
        return success(topologyRelationService.createTopologyRelation(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新拓扑图关系")
    @PreAuthorize("@ss.hasPermission('monitor:topology-relation:update')")
    public CommonResult<Boolean> updateTopologyRelation(@Valid @RequestBody TopologyRelationUpdateReqVO updateReqVO) {
        topologyRelationService.updateTopologyRelation(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除拓扑图关系")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('monitor:topology-relation:delete')")
    public CommonResult<Boolean> deleteTopologyRelation(@RequestParam("id") Long id) {
        topologyRelationService.deleteTopologyRelation(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得拓扑图关系")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('monitor:topology-relation:query')")
    public CommonResult<TopologyRelationRespVO> getTopologyRelation(@RequestParam("id") Long id) {
        TopologyRelationDO topologyRelation = topologyRelationService.getTopologyRelation(id);
        return success(TopologyRelationConvert.INSTANCE.convert(topologyRelation));
    }

    @GetMapping("/list")
    @Operation(summary = "获得拓扑图关系列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('monitor:topology-relation:query')")
    public CommonResult<List<TopologyRelationRespVO>> getTopologyRelationList(@RequestParam("ids") Collection<Long> ids) {
        List<TopologyRelationDO> list = topologyRelationService.getTopologyRelationList(ids);
        return success(TopologyRelationConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得拓扑图关系分页")
    @PreAuthorize("@ss.hasPermission('monitor:topology-relation:query')")
    public CommonResult<PageResult<TopologyRelationRespVO>> getTopologyRelationPage(@Valid TopologyRelationPageReqVO pageVO) {
        PageResult<TopologyRelationDO> pageResult = topologyRelationService.getTopologyRelationPage(pageVO);
        return success(TopologyRelationConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出拓扑图关系 Excel")
    @PreAuthorize("@ss.hasPermission('monitor:topology-relation:export')")
    @OperateLog(type = EXPORT)
    public void exportTopologyRelationExcel(@Valid TopologyRelationExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<TopologyRelationDO> list = topologyRelationService.getTopologyRelationList(exportReqVO);
        // 导出 Excel
        List<TopologyRelationExcelVO> datas = TopologyRelationConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "拓扑图关系.xls", "数据", TopologyRelationExcelVO.class, datas);
    }
}
