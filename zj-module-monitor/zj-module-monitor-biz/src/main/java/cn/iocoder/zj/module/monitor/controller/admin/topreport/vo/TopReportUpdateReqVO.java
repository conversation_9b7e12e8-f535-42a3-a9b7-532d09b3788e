package cn.iocoder.zj.module.monitor.controller.admin.topreport.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TopReportUpdateReqVO extends TopReportBaseVO {

    @NotNull(message = "ID不能为空")
    private Long id;
    /**
     * 资产id列表
     */
    private List<TopReportAssetVO> assets;

    /**
     * 指标名称列表
     */
    private List<TopReportMetricVO> metrics;
}
