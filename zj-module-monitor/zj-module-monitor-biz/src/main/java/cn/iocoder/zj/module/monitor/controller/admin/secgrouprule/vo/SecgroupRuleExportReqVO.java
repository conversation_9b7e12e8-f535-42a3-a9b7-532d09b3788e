package cn.iocoder.zj.module.monitor.controller.admin.secgrouprule.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 端口组规则 Excel 导出 Request VO，参数和 SecgroupRulePageReqVO 是一致的")
@Data
public class SecgroupRuleExportReqVO {

    @Schema(description = "uuid")
    private String uuid;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "安全组uuid")
    private String secgroupUuid;

    @Schema(description = "优先级")
    private Integer priority;

    @Schema(description = "协议")
    private String protocol;

    @Schema(description = "端口")
    private String ports;

    @Schema(description = "方向")
    private String direction;

    @Schema(description = "cidr")
    private String cidr;

    @Schema(description = "策略")
    private String action;

    @Schema(description = "备注")
    private String description;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

}
