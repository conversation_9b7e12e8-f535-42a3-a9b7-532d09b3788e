package cn.iocoder.zj.module.monitor.dal.mysql.bulletin;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.dal.dataobject.bulletin.BulletinDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 实时报 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BulletinMapper extends BaseMapperX<BulletinDO> {

    default PageResult<BulletinDO> selectPage(BulletinPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BulletinDO>()
                .likeIfPresent(BulletinDO::getName, reqVO.getName())
                .eqIfPresent(BulletinDO::getDescription, reqVO.getDescription())
                .eqIfPresent(BulletinDO::getCategory, reqVO.getCategory())
                .eqIfPresent(BulletinDO::getApp, reqVO.getApp())
                .eqIfPresent(BulletinDO::getFields, reqVO.getFields())
                .eqIfPresent(BulletinDO::getAssetIds, reqVO.getAssetIds())
                .betweenIfPresent(BulletinDO::getCreateTime, reqVO.getCreateTime())
                .betweenIfPresent(BulletinDO::getUpdateTime, reqVO.getUpdateTime())
                .orderBy(
                        reqVO.getSortBy() != null && reqVO.getSortBy().equals("updateTime"),
                        reqVO.getSortDirection() != null && reqVO.getSortDirection().equalsIgnoreCase("asc"),
                        BulletinDO::getUpdateTime
                        )
                .orderByDesc(BulletinDO::getId));
    }

    default List<BulletinDO> selectList(BulletinExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BulletinDO>()
                .likeIfPresent(BulletinDO::getName, reqVO.getName())
                .eqIfPresent(BulletinDO::getDescription, reqVO.getDescription())
                .eqIfPresent(BulletinDO::getCategory, reqVO.getCategory())
                .eqIfPresent(BulletinDO::getApp, reqVO.getApp())
                .eqIfPresent(BulletinDO::getFields, reqVO.getFields())
                .eqIfPresent(BulletinDO::getAssetIds, reqVO.getAssetIds())
                .betweenIfPresent(BulletinDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BulletinDO::getId));
    }
    @TenantIgnore
    @Select("<script>" +
            "SELECT ${field} FROM ${tableName} WHERE platform_id IN (${platformIds}) " +
            "<if test='tableName == \"hzb_monitor\"'> AND app = #{app} </if>" +
            "<if test='tableName != \"hzb_monitor\"'> AND deleted = 0 </if>" +
            "<if test='name != null and name != \"\"'> AND `name` like '%${name}%' </if>" +
            "<if test='tableName != \"hzb_monitor\"'> group by `uuid` </if>" +
            "</script>")
    List<Map<String, Object>> selectAssetListByApp(@Param("tableName") String tableName, @Param("platformIds") String platformIds, @Param("field") String field, @Param("app") String app, @Param("name") String name);

    @TenantIgnore
    @Select("<script>" +
            "SELECT * FROM hzb_monitor WHERE id = (${id}) " +
            "</script>")
    Map<String, Object> selectHzbById(@Param("id") Long id);
}
