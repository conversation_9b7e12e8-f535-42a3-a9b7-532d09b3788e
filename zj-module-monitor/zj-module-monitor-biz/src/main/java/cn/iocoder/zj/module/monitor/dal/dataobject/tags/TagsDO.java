package cn.iocoder.zj.module.monitor.dal.dataobject.tags;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 标签 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_tags")
@KeySequence("monitor_tags_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TagsDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 名称
     */
    private String name;

    /**
     * 总数
     */
    private Long totalCount;

    private Long tenantId;
    /**
     * 备注
     */
    private String remark;

    private String color;

    private Integer type;

    private String tagName;

    private String tagUuid;

    private Integer isUpdate;

    @TableField(exist = false)
    private List<String> tagUuids;

    @TableField(exist = false)
    private String taggableType;

    private Long platformId;
}
