package cn.iocoder.zj.module.monitor.dal.mysql.eip;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.dal.dataobject.eip.EipDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.monitor.controller.admin.eip.vo.*;

/**
 * 弹性公网 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface EipMapper extends BaseMapperX<EipDO> {

    @TenantIgnore
    default PageResult<EipDO> selectPage(EipPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<EipDO>()
                .likeIfPresent(EipDO::getName, reqVO.getName())
                .eqIfPresent(EipDO::getUuid, reqVO.getUuid())
                .eqIfPresent(EipDO::getIpAddr, reqVO.getIpAddr())
                .eqIfPresent(EipDO::getBandwidth, reqVO.getBandwidth())
                .eqIfPresent(EipDO::getNetworkId, reqVO.getNetworkId())
                .eqIfPresent(EipDO::getAssociateType, reqVO.getAssociateType())
                .eqIfPresent(EipDO::getAssociateId, reqVO.getAssociateId())
                .eqIfPresent(EipDO::getChargeType, reqVO.getChargeType())
                .eqIfPresent(EipDO::getCloudregionId, reqVO.getCloudregionId())
                .eqIfPresent(EipDO::getMode, reqVO.getMode())
                .eqIfPresent(EipDO::getStatus, reqVO.getStatus())
                .eqIfPresent(EipDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(EipDO::getPlatformName, reqVO.getPlatformName())
                .likeIfPresent(EipDO::getIpAddr, reqVO.getIp())
                .orderByDesc(EipDO::getId));
    }

    default List<EipDO> selectList(EipExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<EipDO>()
                .likeIfPresent(EipDO::getName, reqVO.getName())
                .eqIfPresent(EipDO::getUuid, reqVO.getUuid())
                .eqIfPresent(EipDO::getIpAddr, reqVO.getIpAddr())
                .eqIfPresent(EipDO::getBandwidth, reqVO.getBandwidth())
                .eqIfPresent(EipDO::getNetworkId, reqVO.getNetworkId())
                .eqIfPresent(EipDO::getAssociateType, reqVO.getAssociateType())
                .eqIfPresent(EipDO::getAssociateId, reqVO.getAssociateId())
                .eqIfPresent(EipDO::getChargeType, reqVO.getChargeType())
                .eqIfPresent(EipDO::getCloudregionId, reqVO.getCloudregionId())
                .eqIfPresent(EipDO::getMode, reqVO.getMode())
                .eqIfPresent(EipDO::getStatus, reqVO.getStatus())
                .eqIfPresent(EipDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(EipDO::getPlatformName, reqVO.getPlatformName())
                .orderByDesc(EipDO::getId));
    }

}
