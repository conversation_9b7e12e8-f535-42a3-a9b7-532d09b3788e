package cn.iocoder.zj.module.monitor.dal.mysql.alarmconfig;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo.*;
import cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo.AlarmHostListPageResp;
import cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo.AlarmHostRelationPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo.AlarmHostRelationReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.alarmnotice.vo.AlarmNoticeBaseVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmconfig.AlarmConfigDO;
import cn.iocoder.zj.module.monitor.dal.mysql.alarmdoriseinfo.AlarmDorisDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.hertzbeat.common.entity.alerter.Alert;
import org.apache.hertzbeat.common.entity.alerter.AlertConverge;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 告警配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AlarmConfigMapper extends BaseMapperX<AlarmConfigDO> {

    default PageResult<AlarmConfigDO> selectPage(AlarmConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AlarmConfigDO>()
                .likeIfPresent(AlarmConfigDO::getAlarmName, reqVO.getAlarmName())
                .eqIfPresent(AlarmConfigDO::getDescription, reqVO.getDescription())
                .eqIfPresent(AlarmConfigDO::getSourceType, reqVO.getSourceType())
                .likeIfPresent(AlarmConfigDO::getDictLabelName, reqVO.getDictLabelName())
                .eqIfPresent(AlarmConfigDO::getDictLabelType, reqVO.getDictLabelType())
                .eqIfPresent(AlarmConfigDO::getAlarmRule, reqVO.getAlarmRule())
                .eqIfPresent(AlarmConfigDO::getAlarmVal, reqVO.getAlarmVal())
                .eqIfPresent(AlarmConfigDO::getAlarmLevel, reqVO.getAlarmLevel())
                .eqIfPresent(AlarmConfigDO::getTenantId,reqVO.getTenantId())
                .orderByDesc(AlarmConfigDO::getId));
    }

    @TenantIgnore
    default List<AlarmConfigDO> selectList(AlarmConfigExcelReqVO reqVO,Collection<Long> d) {
        return selectList(new LambdaQueryWrapperX<AlarmConfigDO>()
                .eq(AlarmConfigDO::getDeleted,0)
                .likeIfPresent(AlarmConfigDO::getAlarmName, reqVO.getAlarmName())
                .eqIfPresent(AlarmConfigDO::getDescription, reqVO.getDescription())
                .eqIfPresent(AlarmConfigDO::getSourceType, reqVO.getSourceType())
                .likeIfPresent(AlarmConfigDO::getDictLabelName, reqVO.getDictLabelName())
                .inIfPresent(AlarmConfigDO::getId, reqVO.getIds())
                .eqIfPresent(AlarmConfigDO::getDictLabelType, reqVO.getDictLabelType())
                .eqIfPresent(AlarmConfigDO::getAlarmRule, reqVO.getAlarmRule())
                .eqIfPresent(AlarmConfigDO::getAlarmVal, reqVO.getAlarmVal())
                .eqIfPresent(AlarmConfigDO::getAlarmLevel, reqVO.getAlarmLevel())
                .betweenIfPresent(AlarmConfigDO::getUpdateTime, reqVO.getCreateTime())
                .orderByDesc(AlarmConfigDO::getUpdateTime)
                .inIfPresent(AlarmConfigDO::getTenantId,d));
    }
    @TenantIgnore
    List<AlarmConfigRespVO> selectAlarmConfigPage(@Param("mpPage") IPage<AlarmConfigRespVO> mpPage, @Param("pageReqVO")AlarmConfigPageReqVO pageReqVO);

    List<AlarmHostListPageResp> getAlarmAddedList(@Param("reqVO")AlarmHostRelationReqVO reqVO,
                                                  @Param("sourceType")String sourceType, @Param("platformIdList")List<String> platformIdList);

    AlarmNoticeBaseVO getAlarmNoticeByConfigId(@Param("configId")Long configId);

    void createAlarmRecord(@Param("alarmInfoRespVoList")List<AlarmInfoRespVo> alarmInfoRespVoList);

    List<AlarmInfoRespVo> getAllAlarmRecord();

    List<AlarmDorisReqDTO> getSilentTarget(@Param("timestamp")Long timestamp,
                                           @Param("converge")AlertConverge converge);

    void updateAlarmRecord(@Param("updateList")List<AlarmInfoRespVo> updateList);

    void solvedAlarm(@Param("alarmId") String alarmId);

    void alarmWorkOrder(@Param("alarmId") String alarmId);

    void cleanWorkOrder(@Param("alarmId") String alarmId);

    @DS("doris")
    Map<String,Object> getAlarmCount(@Param("platformId")Long platformId,@Param("platformIdList")List<String> platformIdList);

    List<AlarmInfoRespVo> getAlarmInfoPage(IPage<AlarmInfoRespVo> mpPage,@Param("pageReqVO") AlarmInfoReqVo pageReqVO);

    AlarmInfoRespVo getAlarmInfo(@Param("id") Long id);

    List<AlarmInfoRespVo> getUnsolvedAlarms(@Param("tenantId") Long tenantId, @Param("limit") Long limit);

    void updateIsRead(@Param("ids") Collection<Long> ids);

    void updateIsSolved(@Param("ids")Collection<Long> ids);

    Long alarmConfigMaxId();

    void updateAlarmConfig(@Param("id") Long id,@Param("updateObj") AlarmConfigDO updateObj);

    void updateAlarmHostRelation(@Param("id") Long id,@Param("updateObj") AlarmConfigDO updateObj);

    void updateAlarmNotice(@Param("id") Long id,@Param("updateObj") AlarmConfigDO updateObj);

    List<AlarmConfigRespVO> getAlarmConfigByUuid(@Param("uuid") String uuid,@Param("tenantId") Long tenantId);
    List<AlarmHostListPageResp> getPreparingAddAlarmList(@Param("reqVO") AlarmHostRelationReqVO reqVO, @Param("sourceType") String sourceType);

    List<AlarmHostListPageResp> getAlarmHostPage(@Param("mpPage")IPage<AlarmHostListPageResp> mpPage,
                                                 @Param("reqVO")AlarmHostRelationPageReqVO reqVO,
                                                 @Param("sourceType")String sourceType);

    List<AlarmInfoRespVo> getUnsolvedAlarmsList(@Param("tenantId") Long tenantId);

    List<AlarmInfoRespVo> getAlarmInfoList(@Param("tenantId") List<String> tenantId, @Param("alarmInfoReqVo") AlarmInfoReqVo alarmInfoReqVo);

    @TenantIgnore
    List<Map<String, Object>> getCloudAlarms(@Param("platform") List<Map> platform, @Param("limit") long limit);
    @TenantIgnore
    @DS("doris")
    List<Map<String, Object>> getHzAlarmsList(@Param("platform") List<Map> platform, @Param("limit") long limit);

    void updateAlarmRecordSolvedState(@Param("alertId")Long alertId, @Param("isSolved")Integer isSolved);

    AlertConverge getAvailableAlertConverge(@Param("enable")Integer enable,@Param("machAll")Integer machAll);

    @TenantIgnore
    void updateCollectorAlarm(@Param("result")AlarmDorisDO result);

    @TenantIgnore
    void createAlarmToDoris(@Param("list")List<AlarmDorisDO> result);

    @TenantIgnore
    Long getMaxAlertId();

    @TenantIgnore
    void deleteAlarmDoris(@Param("newList")List<Long> newList);

    @TenantIgnore
    AlarmDorisDO getAlarmDorisById(@Param("alertId")Long alertId);

    @TenantIgnore
    void createCollectorAlert(@Param("alertMap")Map<String, Object> alertMap);

    @TenantIgnore
    void deletedCollectorAlert(@Param("alertMap")Map<String, Object> alertMap);

    @TenantIgnore
    List<Map<String, Object>> getCollectorAlertsByPlatform(@Param("platformIds")Collection<Long> platformIds);
    @TenantIgnore
    @DS("doris")
    List<Map<String, Object>> getTowDaysGrowth(@Param("platformId")Long platformId,@Param("platformIdList")List<String> platformIdList);

    @TenantIgnore
    @DS("doris")
    Map<String, Object> getAlarmSummary(@Param("platformIds") List<Long> platformIds, @Param("platformId") Long platformId, @Param("priority") Integer priority, @Param("app") List<String> app);
    @TenantIgnore
    @DS("doris")
    void updateAlarmToDoris(@Param("list")List<AlarmDorisReqDTO> updateAlarmDoris);

    @TenantIgnore
    @DS("doris")
    Alert getAlertInfoById(Long id);
    @TenantIgnore
    AlarmConfigDO getAlarmConfigById(@Param("id")Long id);

    @TenantIgnore
    List<AlarmConfigDO> getAlarmConfigList(@Param("pageReqVO")AlarmConfigPageReqVO pageReqVO);
}
