package cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 告警配置与云主机关联关系分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AlarmHostRelationPageReqVO extends PageParam {

    @Schema(description = "告警配合名称")
    private String alarmName;

    @Schema(description = "告警配置ID,进入详情页必传")
    private Long alarmId;

    @Schema(description = "告警等级")
    private Integer alarmLevel;

    @Schema(description = "告警消息")
    private String context;

    @Schema(description = "云主机名称")
    private String hostName;

    @Schema(description = "云主机uuid")
    private String hostUuid;

    @Schema(description = "租户名称")
    private String tenantName;

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "云主机、宿主机或存储的名称")
    private String productsName;

}
