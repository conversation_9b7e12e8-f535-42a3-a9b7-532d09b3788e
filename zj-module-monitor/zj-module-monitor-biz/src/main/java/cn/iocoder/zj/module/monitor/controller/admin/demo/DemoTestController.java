package cn.iocoder.zj.module.monitor.controller.admin.demo;


import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.dal.redis.zstack.ZstackAccessTokenRedisDAO;
import cn.iocoder.zj.module.monitor.service.cloud.IZstackCloudService;
import cn.iocoder.zj.module.monitor.service.demo.DemoService;
import cn.iocoder.zj.module.system.api.region.RegionApi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - Test")
@RestController
@RequestMapping("/monitor/test")
public class DemoTestController {

    @Resource
    private RegionApi regionApi;
    @Resource
    private DemoService demoService;

    @Resource
    ZstackAccessTokenRedisDAO zstackAccessTokenRedisDAO;

    @Resource
    IZstackCloudService zstackCloudService;

    @GetMapping("/get")
    @Operation(summary = "获取 test 信息")
    @PermitAll
    public CommonResult<String> get() {
        zstackCloudService.cpuUsage();
//        ZstakApiLogin zstakApiLogin = new ZstakApiLogin(zstackAccessTokenRedisDAO);
//        ZstackHardWare zstackHardWare = new ZstackHardWare(zstakApiLogin,zstackAccessTokenRedisDAO);
        return success("true");
    }

    @GetMapping("/wework-callback")
    @Operation(summary = "企业微信授权回调")
    public String weworkCallback(final HttpServletRequest request)  throws Exception{

//        String signature = request.getParameter("signature");
//        String timestamp = request.getParameter("timestamp");
//        String nonce = request.getParameter("nonce");
////        String echostr = request.getParameter ("echostr");
//        //参数排序。 token 就要换成自己实际写的 token
//        String[] params = new String[]{timestamp, nonce, "zjToken"};
//        Arrays.sort(params);
//        //拼接
//        String paramstr = params[0] + params[1] + params[2];
//        //加密
//        //获取 shal 算法封装类
//        MessageDigest Sha1Dtgest = MessageDigest.getInstance("SHA-1");
//        //进行加密
//        byte[] digestResult = Sha1Dtgest.digest(paramstr.getBytes("UTF-8"));
        //拿到加密结果
//        String mysignature = CheckoutUtil.byteToStr(digestResult);
//        mysignature = mysignature.toLowerCase(Locale.ROOT);
//        log.info("微信加密，signature：" + signature);
//        log.info("本地加密，mysignature：" + mysignature);

//        try {
////            String corpid = environment.getProperty("wx.corpid");
//            InputStream inputStream = request.getInputStream();
//            String sPostData = IOUtils.toString(inputStream, "UTF-8");
//            WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(token,"100006", encodingAesKey);
//            //解密
//            String sMsg = wxcpt.DecryptMsg(sMsgSignature, sTimestamp, sNonce, sPostData);
//            //将post数据转换为map
////            Map<String, String> dataMap = MessageUtil.parseXml(sMsg);
////            Map<String, String> requestMap = WxMessageUtil.parseXml(request);
//            //然后去操作你的业务逻辑
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        return "success";
    }
}
