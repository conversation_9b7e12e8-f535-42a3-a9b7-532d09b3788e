package cn.iocoder.zj.module.monitor.controller.admin.networkl3.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 三级网络资源分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NetworkL3PageReqVO extends PageParam {

    @Schema(description = "三级网络uuid")
    private String uuid;

    @Schema(description = "二级网络uuid")
    private String l2NetworkUuid;
    @Schema(description = "二级网络名称")
    private String l2NetworkName;
    @Schema(description = "三级网络名称")
    private String name;

    @Schema(description = "dns 逗号分割")
    private String dns;

    @Schema(description = "网络资源类型（L3BasicNetwork 公有网络， L3VpcNetwork  vpc 网络 ）")
    private String type;

    @Schema(description = "网络服务 逗号分割")
    private String networkServices;

    @Schema(description = "起始ip")
    private String startIp;

    @Schema(description = "结束ip")
    private String endIp;

    @Schema(description = "子网掩码")
    private String netmask;

    @Schema(description = "网关")
    private String gateway;

    @Schema(description = "网段名称")
    private String networkSegment;

    @Schema(description = "		IPv4 CIDR")
    private String networkCidr;

    @Schema(description = "IPV4 DHCP")
    private String nextHopIp;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;
    @Schema(description = "查询条件")
    private String queryData;
    @Schema(description = "租户编号")
    private Long tenantId;

    @Schema(required = false,description = "主机过滤ids逗号拼接,'1','2','3'")
    private String ids;
    @Schema(required = false,description = "主键逗号拼接,'1','2','3'")
    private List<Long> inPks;

    private String startTime;

    private String endTime;
}
