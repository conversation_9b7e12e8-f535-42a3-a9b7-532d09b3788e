package cn.iocoder.zj.module.monitor.dal.dataobject.hardwarestorage;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 宿主机与存储关联 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_hardware_storage")
@KeySequence("monitor_hardware_storage_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HardwareStorageDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 宿主机id
     */
    private Long hardwareId;
    /**
     * 宿主机uuid
     */
    private String hardwareUuid;
    /**
     * 主存储id
     */
    private Long storageId;
    /**
     * 主存储uuid
     */
    private String storageUuid;

    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;


}
