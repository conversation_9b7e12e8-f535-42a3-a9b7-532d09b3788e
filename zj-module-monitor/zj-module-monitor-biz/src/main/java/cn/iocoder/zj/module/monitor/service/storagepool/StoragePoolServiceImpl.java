package cn.iocoder.zj.module.monitor.service.storagepool;

import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.api.storagepool.dto.StoragePoolCreateRespDTO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.zj.module.monitor.controller.admin.storagepool.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.storagepool.StoragePoolDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.convert.storagepool.StoragePoolConvert;
import cn.iocoder.zj.module.monitor.dal.mysql.storagepool.StoragePoolMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 存储池 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class StoragePoolServiceImpl implements StoragePoolService {

    @Resource
    private StoragePoolMapper storagePoolMapper;

    @Override
    public Long createStoragePool(StoragePoolCreateReqVO createReqVO) {
        // 插入
        StoragePoolDO storagePool = StoragePoolConvert.INSTANCE.convert(createReqVO);
        storagePoolMapper.insert(storagePool);
        // 返回
        return storagePool.getId();
    }

    @Override
    public void updateStoragePool(StoragePoolUpdateReqVO updateReqVO) {
        // 校验存在
//        validateStoragePoolExists(updateReqVO.getId());
        // 更新
        StoragePoolDO updateObj = StoragePoolConvert.INSTANCE.convert(updateReqVO);
        storagePoolMapper.updateById(updateObj);
    }

    @Override
    public void deleteStoragePool(Long id) {
        // 校验存在
//        validateStoragePoolExists(id);
        // 删除
        storagePoolMapper.deleteById(id);
    }

//    private void validateStoragePoolExists(Long id) {
//        if (storagePoolMapper.selectById(id) == null) {
//            throw exception(STORAGE_POOL_NOT_EXISTS);
//        }
//    }

    @Override
    @TenantIgnore
    public StoragePoolDO getStoragePool(Long id) {
        return storagePoolMapper.selectById(id);
    }

    @Override
    public List<StoragePoolDO> getStoragePoolList(Collection<Long> ids) {
        return storagePoolMapper.selectBatchIds(ids);
    }

    @Override
    @TenantIgnore
    public PageResult<StoragePoolDO> getStoragePoolPage(StoragePoolPageReqVO pageReqVO) {
        return storagePoolMapper.selectPage(pageReqVO);
    }

    @Override
    public List<StoragePoolDO> getStoragePoolList(StoragePoolExportReqVO exportReqVO) {
        return storagePoolMapper.selectList(exportReqVO);
    }

    @Override
    public void createStoragePoolList(List<StoragePoolDO> list) {
        storagePoolMapper.insertBatch(list);
    }

    @Override
    public void updateStoragePoolList(List<StoragePoolDO> list) {
        storagePoolMapper.updateBatch(list);
    }

    @Override
    public List<StoragePoolCreateRespDTO> getStoragePoolByPlatformId(Long platformId) {
        return storagePoolMapper.getStoragePoolByPlatformId(platformId);

    }

    @Override
    public int deleteStoragePoolList(List<StoragePoolDO> list) {
        return storagePoolMapper.deletes(list);
    }

}
