package cn.iocoder.zj.module.monitor.controller.admin.scanip.vo;

import cn.iocoder.zj.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - IP段更新 Request VO")
@Data
public class IPScanResultVO extends PageParam {

    @Schema(description = "编号", example = "1")
    private Long id;

    @Schema(description = "IP段编号", example = "1")
    private Long ipRangeId;

    @Schema(description = "IP段名称", example = "1")
    private String ipRangeName;

    @Schema(description = "IP地址", example = "1")
    private String ipAddress;

    @Schema(description = "Ping支持", example = "0 - 不支持  1 - 支持")
    private Integer pingSupport;

    @Schema(description = "SNMP支持", example = "0 - 不支持  1 - 支持")
    private Integer snmpSupport;

    @Schema(description = "TCP支持", example = "0 - 不支持  1 - 支持")
    private Integer tcpSupport;

    @Schema(description = "扫描时间", example = "1")
    private LocalDateTime scanTime;

    @Schema(description = "租户编号", example = "1")
    private Long tenantId;

    @Schema(description = "租户名称", example = "1")
    private String tenantName;

    @Schema(description = "TCP端口", example = "")
    private Integer tcpPort;

    @Schema(description = "租户ID")
    private List<String> tenantIds;

    @Schema(description = "IP段范围")
    private String ipRanges;

    @Schema(description = "类型")
    private String typeName;

    @Schema(description = "remark")
    private String remark;

    @Schema(description = "添加时间", example = "60")
    private LocalDateTime createTime;


    @Schema(description = "修改时间", example = "60")
    private LocalDateTime updateTime;

    private List<String> ips;

    private Integer pingStatus;

    private Integer snmpStatus;

    private Integer tcpStatus;

    private Long platformId;

    private String platformName;

    private String startTime;

    private String endTime;
}
