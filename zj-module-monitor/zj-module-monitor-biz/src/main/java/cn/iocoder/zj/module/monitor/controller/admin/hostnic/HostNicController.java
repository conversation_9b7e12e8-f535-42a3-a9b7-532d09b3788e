package cn.iocoder.zj.module.monitor.controller.admin.hostnic;

import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.monitor.controller.admin.hostnic.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostnic.HostNicDO;
import cn.iocoder.zj.module.monitor.convert.hostnic.HostNicConvert;
import cn.iocoder.zj.module.monitor.service.hostnic.HostNicService;

@Tag(name = "管理后台 - 云主机网络")
@RestController
@RequestMapping("/monitor/host-nic")
@Validated
public class HostNicController {

    @Resource
    private HostNicService hostNicService;

//    @PostMapping("/create")
//    @Operation(summary = "创建云主机网络")
//    @PreAuthorize("@ss.hasPermission('monitor:host-nic:create')")
//    public CommonResult<Long> createHostNic(@Valid @RequestBody HostNicCreateReqVO createReqVO) {
//        return success(hostNicService.createHostNic(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新云主机网络")
//    @PreAuthorize("@ss.hasPermission('monitor:host-nic:update')")
//    public CommonResult<Boolean> updateHostNic(@Valid @RequestBody HostNicUpdateReqVO updateReqVO) {
//        hostNicService.updateHostNic(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除云主机网络")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('monitor:host-nic:delete')")
//    public CommonResult<Boolean> deleteHostNic(@RequestParam("id") Long id) {
//        hostNicService.deleteHostNic(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得云主机网络")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('monitor:host-nic:query')")
//    public CommonResult<HostNicRespVO> getHostNic(@RequestParam("id") Long id) {
//        HostNicDO hostNic = hostNicService.getHostNic(id);
//        return success(HostNicConvert.INSTANCE.convert(hostNic));
//    }
//
//    @GetMapping("/list")
//    @Operation(summary = "获得云主机网络列表")
//    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
//    @PreAuthorize("@ss.hasPermission('monitor:host-nic:query')")
//    public CommonResult<List<HostNicRespVO>> getHostNicList(@RequestParam("ids") Collection<Long> ids) {
//        List<HostNicDO> list = hostNicService.getHostNicList(ids);
//        return success(HostNicConvert.INSTANCE.convertList(list));
//    }

    @GetMapping("/page")
    @Operation(summary = "获得云主机网络分页")
    @TenantIgnore
    public CommonResult<PageResult<HostNicRespVO>> getHostNicPage(@Valid HostNicPageReqVO pageVO) {
        PageResult<HostNicDO> pageResult = hostNicService.getHostNicPage(pageVO);
        return success(HostNicConvert.INSTANCE.convertPage(pageResult));
    }

//    @GetMapping("/export-excel")
//    @Operation(summary = "导出云主机网络 Excel")
//    @PreAuthorize("@ss.hasPermission('monitor:host-nic:export')")
//    @OperateLog(type = EXPORT)
//    public void exportHostNicExcel(@Valid HostNicExportReqVO exportReqVO,
//              HttpServletResponse response) throws IOException {
//        List<HostNicDO> list = hostNicService.getHostNicList(exportReqVO);
//        // 导出 Excel
//        List<HostNicExcelVO> datas = HostNicConvert.INSTANCE.convertList02(list);
//        ExcelUtils.write(response, "云主机网络.xls", "数据", HostNicExcelVO.class, datas);
//    }

}
