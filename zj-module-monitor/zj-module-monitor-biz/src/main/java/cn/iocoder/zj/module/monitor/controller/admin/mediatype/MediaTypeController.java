package cn.iocoder.zj.module.monitor.controller.admin.mediatype;

import org.jetbrains.annotations.NotNull;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.monitor.controller.admin.mediatype.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.mediatype.MediaTypeDO;
import cn.iocoder.zj.module.monitor.convert.mediatype.MediaTypeConvert;
import cn.iocoder.zj.module.monitor.service.mediatype.MediaTypeService;

@Tag(name = "管理后台 - 介质类型")
@RestController
@RequestMapping("/monitor/media-type")
@Validated
public class MediaTypeController {

    @Resource
    private MediaTypeService mediaTypeService;

    @PostMapping("/create")
    @Operation(summary = "创建介质类型")
//    @PreAuthorize("@ss.hasPermission('monitor:media-type:create')")
    public CommonResult<Long> createMediaType(@Valid @RequestBody MediaTypeCreateReqVO createReqVO) {
        return success(mediaTypeService.createMediaType(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新介质类型")
//    @PreAuthorize("@ss.hasPermission('monitor:media-type:update')")
    public CommonResult<Boolean> updateMediaType(@Valid @RequestBody MediaTypeUpdateReqVO updateReqVO) {
        mediaTypeService.updateMediaType(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除介质类型")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('monitor:media-type:delete')")
    public CommonResult<Boolean> deleteMediaType(@RequestParam("id") Long id) {
        mediaTypeService.deleteMediaType(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得介质类型")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('monitor:media-type:query')")
    public CommonResult<MediaTypeRespVO> getMediaType(@RequestParam("id") Long id) {
        MediaTypeDO mediaType = mediaTypeService.getMediaType(id);
        return success(MediaTypeConvert.INSTANCE.convert(mediaType));
    }

    @GetMapping("/list")
    @Operation(summary = "获得介质类型列表")
//    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
//    @PreAuthorize("@ss.hasPermission('monitor:media-type:query')")
    public CommonResult<List<MediaTypeRespVO>> getMediaTypeList() {
        List<MediaTypeDO> list = mediaTypeService.getMediaTypeList();
        return success(MediaTypeConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得介质类型分页")
//    @PreAuthorize("@ss.hasPermission('monitor:media-type:query')")
    public CommonResult<PageResult<MediaTypeRespVO>> getMediaTypePage(@Valid MediaTypePageReqVO pageVO) {
        PageResult<MediaTypeDO> pageResult = mediaTypeService.getMediaTypePage(pageVO);
        return success(MediaTypeConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出介质类型 Excel")
//    @PreAuthorize("@ss.hasPermission('monitor:media-type:export')")
    @OperateLog(type = EXPORT)
    public void exportMediaTypeExcel(@Valid MediaTypeExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<MediaTypeDO> list = mediaTypeService.getMediaTypeList(exportReqVO);
        // 导出 Excel
        List<MediaTypeExcelVO> datas = MediaTypeConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "介质类型.xls", "数据", MediaTypeExcelVO.class, datas);
    }

    @PostMapping("/bind-storage")
    @Operation(summary = "绑定存储")
//    @PreAuthorize("@ss.hasPermission('monitor:media-type:update')")
    public CommonResult<Boolean> bindStorage(@Valid @RequestBody MediaTypeBindStorageReqVO bindStorageReqVO) {
        String storageIds = bindStorageReqVO.getStorageIds();
        if (storageIds != null && !storageIds.isEmpty()) {
            String[] storageIdsArr = storageIds.split(",");
            for (String storageId : storageIdsArr) {
                Long s = Long.valueOf(storageId);
                mediaTypeService.bindStorage(bindStorageReqVO.getMediaTypeId(), s);
            }
            return success(true);
        }
        return success(false);
    }
}
