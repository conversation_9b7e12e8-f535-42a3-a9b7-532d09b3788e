package cn.iocoder.zj.module.monitor.service.imageinfo;

import java.util.*;
import javax.validation.*;
import cn.iocoder.zj.module.monitor.controller.admin.imageinfo.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.imageinfo.ImageInfoDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import org.apache.ibatis.annotations.Param;

/**
 * 镜像信息 Service 接口
 *
 * <AUTHOR>
 */
public interface ImageInfoService {

    /**
     * 创建镜像信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createImageInfo(@Valid ImageInfoCreateReqVO createReqVO);

    /**
     * 批量创建镜像信息
     *
     * @param createReqVOList 创建信息
     */
    void batchCreateImageInfo(@Valid List<ImageInfoCreateReqVO> createReqVOList);

    /**
     * 批量更新镜像信息
     *
     * @param updateReqVOList 更新信息
     */
    void batchUpdateImageInfo(@Valid List<ImageInfoCreateReqVO> updateReqVOList);

    /**
     * 批量删除镜像信息
     *
     * @param deleteReqVOList 删除信息**
     */
    void batchDeleteImageInfo(@Valid List<ImageInfoCreateReqVO> deleteReqVOList);

    /**
     * 更新镜像信息
     *
     * @param updateReqVO 更新信息
     */
    void updateImageInfo(@Valid ImageInfoUpdateReqVO updateReqVO);

    /**
     * 删除镜像信息
     *
     * @param id 编号
     */
    void deleteImageInfo(Long id);

    /**
     * 获得镜像信息
     *
     * @param id 编号
     * @return 镜像信息
     */
    ImageInfoDO getImageInfo(Long id);

    /**
     * 获得镜像信息列表
     *
     * @param ids 编号
     * @return 镜像信息列表
     */
    List<ImageInfoDO> getImageInfoList(Collection<Long> ids);

    /**
     * 获得镜像信息分页
     *
     * @param pageReqVO 分页查询
     * @return 镜像信息分页
     */
    PageResult<ImageInfoDO> getImageInfoPage(ImageInfoPageReqVO pageReqVO);

    /**
     * 获得镜像信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 镜像信息列表
     */
    List<ImageInfoDO> getImageInfoList(ImageInfoExportReqVO exportReqVO);

    /**
     * 根据平台id获得镜像信息列表
     *
     * @param platformId 平台id
     * @return 镜像信息列表
     */
    List<ImageInfoDO> getImageInfosByPlatformId(Long platformId);

    /**
     * 根据类型名称获得镜像信息列表
     *
     * @param typeCode 类型
     * @return 镜像信息列表
     */
    List<ImageInfoDO> getAllImagesByTypeCode(String typeCode);

    /**
     * 获得镜像状态数量
     * @param tenantIds 租户ids
     * @param platformId 平台id
     * @return  镜像状态数量
     */
    Map<String, Object> getImageStatusCount(List<String> tenantIds, Long platformId);

}
