package cn.iocoder.zj.module.monitor.controller.admin.topology.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 监控资源拓扑图 Excel 导出 Request VO，参数和 TopologyPageReqVO 是一致的")
@Data
public class TopologyExportReqVO {

    @Schema(description = "拓扑图json")
    private String topologyJson;

    @Schema(description = "租户名称")
    private String tenantName;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] createTime;

}
