package cn.iocoder.zj.module.monitor.controller.admin.networkvpc;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.controller.admin.networkvpc.vo.*;
import cn.iocoder.zj.module.monitor.convert.networkvpc.NetworkVpcConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkvpc.NetworkVpcDO;
import cn.iocoder.zj.module.monitor.service.networkvpc.NetworkVpcService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

@Tag(name = "管理后台 - VPC路由器")
@RestController
@RequestMapping("/monitor/network-vpc")
@Validated
public class NetworkVpcController {

    @Resource
    private NetworkVpcService networkVpcService;

    @PostMapping("/create")
    @Operation(summary = "创建VPC路由器")
    @PermitAll
    @PreAuthorize("@ss.hasPermission('monitor:network-vpc:create')")
    @OperateLog(type = CREATE)
    public CommonResult<Long> createNetworkVpc(@Valid @RequestBody NetworkVpcCreateReqVO createReqVO) {
        return success(networkVpcService.createNetworkVpc(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新VPC路由器")
    @PermitAll
    @PreAuthorize("@ss.hasPermission('monitor:network-vpc:update')")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateNetworkVpc(@Valid @RequestBody NetworkVpcUpdateReqVO updateReqVO) {
        networkVpcService.updateNetworkVpc(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除VPC路由器")
    @Parameter(name = "id", description = "编号", required = true)
    @PermitAll
    @PreAuthorize("@ss.hasPermission('monitor:network-vpc:delete')")
    @OperateLog(type = DELETE)
    public CommonResult<Boolean> deleteNetworkVpc(@RequestParam("id") Long id) {
        networkVpcService.deleteNetworkVpc(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得VPC路由器")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PermitAll
    public CommonResult<NetworkVpcRespVO> getNetworkVpc(@RequestParam("id") Long id) {
        NetworkVpcDO networkVpc = networkVpcService.getNetworkVpc(id);
        return success(NetworkVpcConvert.INSTANCE.convert(networkVpc));
    }

    @GetMapping("/list")
    @Operation(summary = "获得VPC路由器列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PermitAll
    public CommonResult<List<NetworkVpcRespVO>> getNetworkVpcList(@RequestParam("ids") Collection<Long> ids) {
        List<NetworkVpcDO> list = networkVpcService.getNetworkVpcList(ids);
        return success(NetworkVpcConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得VPC路由器分页")
    @PreAuthenticated
    @TenantIgnore
    public CommonResult<PageResult<NetworkVpcRespVO>> getNetworkVpcPage(@Valid NetworkVpcPageReqVO pageVO) {
        PageResult<NetworkVpcDO> pageResult = networkVpcService.getNetworkVpcPage(pageVO);
        return success(NetworkVpcConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出VPC路由器 Excel")
    @PreAuthenticated
    @OperateLog(type = EXPORT)
    public void exportNetworkVpcExcel(@Valid NetworkVpcExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<NetworkVpcDO> list = networkVpcService.getNetworkVpcList(exportReqVO);
        // 导出 Excel
        List<NetworkVpcExcelVO> datas = NetworkVpcConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "VPC路由器.xls", "数据", NetworkVpcExcelVO.class, datas);
    }

    @GetMapping("/clusterSimpleInfo")
    @Operation(summary = "集群下拉选择框信息")
    @Parameter(name = "platformId", description = "编号", required = false, example = "1024,1023")
    public CommonResult<List<Map<String,String>>> getClusterSimpleInfo(@RequestParam(value = "platformId",required = false) Collection<Long> platformId) {
        return success(networkVpcService.getClusterSimpleInfo(platformId));
    }
}
