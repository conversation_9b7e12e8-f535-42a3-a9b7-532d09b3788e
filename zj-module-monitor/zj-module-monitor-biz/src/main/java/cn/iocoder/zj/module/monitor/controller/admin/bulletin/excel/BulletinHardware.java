package cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel;

import cn.iocoder.zj.framework.excel.core.convert.ByteConvent;
import cn.iocoder.zj.framework.excel.core.convert.UnitConvert;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.convert.BulletinBandwidthConvert;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.convert.BulletinPercentConvert;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.convert.BulletinStateConvert;
import cn.iocoder.zj.module.monitor.controller.admin.tags.vo.TagsRespVO;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ExcelIgnoreUnannotated
public class BulletinHardware {
    private String uuid;

    @ExcelProperty("宿主机名称")
    private String name;

    @ExcelProperty("平台名称")
    private String platformName;

    @ExcelProperty("集群名称")
    private String clusterName;

    @ExcelProperty(value = "启用状态", converter = BulletinStateConvert.class)
    private String state;

    @ExcelProperty(value = "就绪状态", converter = BulletinStateConvert.class)
    private String status;

    @ExcelProperty("宿主机IP")
    private String ip;

    @ExcelProperty(value = "cpu使用率", converter = BulletinPercentConvert.class)
    private String cpuUsed;

    @ExcelProperty(value = "内存使用率", converter = BulletinPercentConvert.class)
    private String memoryUsed;

    @ExcelProperty(value = "宽带上行", converter = BulletinBandwidthConvert.class)
    private BigDecimal bandwidthUpstream;

//    @ExcelProperty("宽带上下行")
    private String bandwidthStream;
    /**
     * 带宽下行速率
     */
    @ExcelProperty(value = "宽带下行", converter = BulletinBandwidthConvert.class)
    private BigDecimal bandwidthDownstream;

    private List<TagsRespVO> tags;

    @ExcelProperty("标签")
    private String tagsStr;

    @ExcelProperty("ipmi")
    private String ipmi;

    @ExcelProperty("MAC地址")
    private String mac;

    @ExcelProperty("区域")
    private String manager;

    @ExcelProperty("可用区域")
    private String availableManager;

    @ExcelProperty(value = "物理CPU", converter = UnitConvert.class)
    private Integer cpuNum;

    @ExcelProperty("插槽数")
    @NumberFormat("0 个")
    private Integer cpuSockets;

    @ExcelProperty("CPU超售比")
    private BigDecimal cpuOverPercent;

    @ExcelProperty(value = "虚拟CPU",converter = UnitConvert.class)
    private Integer totalVirtualCpu;

    @ExcelProperty("已分配CPU")
    private BigDecimal cpuCommitRate;

    @ExcelProperty("CPU型号")
    private String cpuType;

    @ExcelProperty("CPU架构")
    private String architecture;

    @ExcelProperty(value = "物理容量", converter = ByteConvent.class)
    private Long totalMemoryCapacity;

    @ExcelProperty(value = "虚拟容量", converter = ByteConvent.class)
    private String totalVirtualMemory;

    @ExcelProperty(value = "已分配容量", converter = ByteConvent.class)
    private BigDecimal memoryCommitRate;

    @ExcelProperty(value = "内存超售比")
    private BigDecimal memoryOverPercent;

    @ExcelProperty(value = "系统预留", converter = ByteConvent.class)
    private BigDecimal reservedMemory;
}
