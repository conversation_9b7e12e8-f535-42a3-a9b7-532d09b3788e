package cn.iocoder.zj.module.monitor.controller.admin.hostsecgroup.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 安全组关联云主机 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class HostSecgroupBaseVO {

    @Schema(description = "云主机uuid")
    private String hostUuid;

    @Schema(description = "安全组uuid")
    private String secgroupUuid;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

}
