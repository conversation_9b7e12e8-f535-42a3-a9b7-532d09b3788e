package cn.iocoder.zj.module.monitor.service.homeinfo;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo.AlarmInfoRespVo;
import cn.iocoder.zj.module.monitor.dal.mysql.hostinfo.HostInfoMapper;
import cn.iocoder.zj.module.system.api.tenant.TenantApi;
import cn.iocoder.zj.module.system.api.tenant.dto.TenantRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.frameworkset.elasticsearch.ElasticSearchHelper;
import org.frameworkset.elasticsearch.client.ClientInterface;
import org.frameworkset.elasticsearch.entity.ESDatas;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Validated
@Slf4j
public class HomeInfoServiceImpl implements HomeInfoService {
    @Resource
    HostInfoMapper hostInfoMapper;
    @Resource
    TenantApi tenantApi;

    @Override
    public int getIsDisplay(Long tenantId,String monitorEntry) {
        return hostInfoMapper.getIsDisplay(tenantId,monitorEntry);
    }

    @Override
    public String getAllDisplay(Long tenantId) {
        return hostInfoMapper.getAllDisplay(tenantId);
    }

    @Override
    public Map<String, Object> getDisplayModule(Long tenantId) {
        Map<String, Object> displayModule=new HashMap<>();
        List<Map<String, Object>> moduleList=hostInfoMapper.getModuleList(tenantId);
        for (int i=0;i<moduleList.size();i++){
            List<Map<String, Object>> ResourceType=hostInfoMapper.getResourceType(tenantId,moduleList.get(i).get("module").toString());
            for (int j=0;j<ResourceType.size();j++){
                String resourceType=ResourceType.get(j).get("resource_type").toString();
                List<Map<String, Object>> monitorEntry=hostInfoMapper.getMonitorEntry(tenantId,moduleList.get(i).get("module").toString(),resourceType);
                ResourceType.get(j).put("monitorEntry",monitorEntry);
            }
            moduleList.get(i).put("ResourceType",ResourceType);
            int entryCount=hostInfoMapper.getEntryCount(tenantId,moduleList.get(i).get("module").toString());
            moduleList.get(i).put("entryCount",entryCount);
        }
        displayModule.put("moduleList",moduleList);
        return displayModule;
    }

    @Override
    public Map<String, Object> getResourceType(Long tenantId, String module) {
        Map<String, Object> moduleList=new HashMap<>();
        List<Map<String, Object>> ResourceType=hostInfoMapper.getResourceType(tenantId,module);
        for (int i=0;i<ResourceType.size();i++){
            String resourceType=ResourceType.get(i).get("resource_type").toString();
            List<Map<String, Object>> monitorEntry=hostInfoMapper.getMonitorEntry(tenantId,module,resourceType);
            ResourceType.get(i).put("monitorEntry",monitorEntry);
        }
        moduleList.put("ResourceType",ResourceType);
        return moduleList;
    }

    @Override
    public int updateDisplay(Long tenantId, String layoutConfig) {
        return hostInfoMapper.updateDisplay(tenantId,layoutConfig);
    }

    @Override
    public Map<String, Object> getLayoutConfig(Long tenantId) {
        return hostInfoMapper.getLayoutConfig(tenantId);
    }

    @Override
    public List<String> getDMonitorEntry() {
        return hostInfoMapper.getDMonitorEntry();
    }

    @Override
    public List<AlarmInfoRespVo> getAlarmNum(Long tenantId, Long platformConfigId) {
        ClientInterface clientUtil = ElasticSearchHelper.getConfigRestClientUtil("esmapper/ESAlarmInfoMapper.xml");
        Map searchFields = new HashMap<>();
        searchFields.put("tenantId",tenantId);
        searchFields.put("platformConfigId",platformConfigId);
        List<AlarmInfoRespVo> alarmInfoRespVoList = new ArrayList<>();
        for (int i=1;i<4;i++){
            searchFields.put("alarmLevel",i);
            ESDatas<Map> data = new ESDatas<>();
            try {
                data = clientUtil.searchList("alarm_info/_search", "getAlarmNum", searchFields, Map.class);
            }catch (Exception e){
                log.info("Es连接超时!"+e);
            }
            List<Map<String,Object>> result =  data.getAggregationBuckets("alarmLevel");
            AlarmInfoRespVo alarmInfoRespVo = new AlarmInfoRespVo();
            if(result != null && result.size() > 0) {
                Map<String, Object> map = result.get(0);
                alarmInfoRespVo.setAlarmLevel(Integer.parseInt(map.get("key").toString()));
                alarmInfoRespVo.setAlarmNum(Long.valueOf(map.get("doc_count").toString()));
                alarmInfoRespVoList.add(alarmInfoRespVo);
            }else {
                alarmInfoRespVo.setAlarmLevel(i);
                alarmInfoRespVo.setAlarmNum(0L);
                alarmInfoRespVoList.add(alarmInfoRespVo);
            }
        }
        return alarmInfoRespVoList;
    }

    @Override
    public List<Map<String, Object>> getAlarmInfo(Long tenantId, Long platformConfigId) {
        ClientInterface clientUtil = ElasticSearchHelper.getConfigRestClientUtil("esmapper/ESAlarmInfoMapper.xml");
        Map searchFields = new HashMap<>();
        searchFields.put("tenantId",tenantId);
        searchFields.put("platformConfigId",platformConfigId);
        ESDatas<Map> data = new ESDatas<>();
        try {
            data = clientUtil.searchList("alarm_info/_search", "getAlarmByPlatform", searchFields, Map.class);
        }catch (Exception e){
            log.info("Es连接超时!"+e);
        }
        List<Map<String,Object>> result =  data.getAggregationBuckets("context");
        List<Map<String,Object>> alarmInfoRespVoList = new ArrayList<Map<String,Object>>();
        if (result != null && result.size() > 0){
            for (int i = 0; i < result.size(); i++) {
                Map<String,Object> regionAlarmInfo = new HashMap<>();
                String alarmInfo=result.get(i).get("key").toString();
                String[] alarmInfos=alarmInfo.split("#");
                regionAlarmInfo.put("context",alarmInfos[0]);
                regionAlarmInfo.put("uuid",alarmInfos[1]);
                regionAlarmInfo.put("alarmLevel",alarmInfos[2]);
                String platformName=hostInfoMapper.getplatformName(Long.parseLong(alarmInfos[3]));
                regionAlarmInfo.put("platformName",platformName);
                regionAlarmInfo.put("alarmNum",result.get(i).get("doc_count").toString());
                if (StrUtil.isNotEmpty(platformName)) {
                    alarmInfoRespVoList.add(regionAlarmInfo);
                    if (alarmInfoRespVoList.size()==5){
                        break;
                    }
                }
            }
        }
        return alarmInfoRespVoList;
    }

    @Override
    public List<Map<String,Object>> getAlarmMap(Long tenantId,Long regionId) {
        ClientInterface clientUtil = ElasticSearchHelper.getConfigRestClientUtil("esmapper/ESAlarmInfoMapper.xml");
        if (regionId != null && regionId.longValue() != 0){
            regionId = Long.parseLong(regionId.toString().substring(0,2));
            List<Long> regionIds=hostInfoMapper.getRegionId(tenantId,regionId);
            List<String> citys=tenantApi.getParentRegionInfo(regionIds,3).getData();
            if(citys.get(0).equals("110100") || citys.get(0).equals("120100") || citys.get(0).equals("310100") || citys.get(0).equals("500100")){
                regionIds=hostInfoMapper.getRegionId(tenantId,Long.parseLong(citys.get(0).substring(0,4)));
                citys=tenantApi.getParentRegionInfo(regionIds,4).getData();
            }
            List<Map<String, Object>> alarmInfoRespVoList = new ArrayList<Map<String, Object>>(citys.size());
            for (int i=0;i<citys.size();i++) {
                List<String> platformConfigIds = new ArrayList<>();
                List<Map<String, Object>> platformInfos = hostInfoMapper.getPlatformInfos(tenantId,citys.get(i).substring(0,4));
                if(citys.get(i).substring(0,4).equals("1101") || citys.get(i).substring(0,4).equals("1201") || citys.get(i).substring(0,4).equals("3101") || citys.get(i).substring(0,4).equals("5001")) {
                    platformInfos = hostInfoMapper.getPlatformInfos(tenantId,citys.get(i));
                }
                for (int j=0;j<platformInfos.size();j++){
                    platformConfigIds.add(platformInfos.get(j).get("id").toString());
                }
                Map<String, Object> regionInfo = new HashMap<>();
                regionInfo.put("regionId", citys.get(i));
                CommonResult<TenantRespDTO> tenant = tenantApi.getRegionInfo(Long.parseLong(citys.get(i)));
                regionInfo.put("regionName", tenant.getData().getRegionName());
                regionInfo.put("platformInfos", platformInfos);
                Map searchFields = new HashMap<>();
                searchFields.put("tenantId", tenantId);
                searchFields.put("platformConfigIds", platformConfigIds);
                ESDatas<Map> data = new ESDatas<>();
                try {
                    data = clientUtil.searchList("alarm_info/_search", "getPlatformByArea", searchFields, Map.class);
                }catch (Exception e){
                    log.info("Es连接超时!"+e);
                }
                List<Map<String, Object>> result = data.getAggregationBuckets("parentRegionId");
                if (result != null && result.size() > 0){
                    Map<String, Object> map = result.get(0);
                    regionInfo.put("alarmNum", map.get("doc_count"));
                }else {
                    regionInfo.put("alarmNum", 0);
                }
                alarmInfoRespVoList.add(regionInfo);
            }
            return alarmInfoRespVoList;
        }else {
            List<Long> regionIds=hostInfoMapper.getRegionId(tenantId,regionId);
            List<String> areas=tenantApi.getParentRegionInfo(regionIds,2).getData();
            List<Map<String, Object>> alarmInfoRespVoList = new ArrayList<>(areas.size());
            for (int i=0;i<areas.size();i++){
                Map<String, Object> regionInfo = new HashMap<>();
                regionInfo.put("regionId", areas.get(i));
                CommonResult<TenantRespDTO> tenant = tenantApi.getRegionInfo(Long.parseLong(areas.get(i)));
                regionInfo.put("regionName", tenant.getData().getRegionName());
                Map searchFields = new HashMap<>();
                searchFields.put("tenantId",tenantId);
                searchFields.put("parentRegionId",areas.get(i));
                ESDatas<Map> data = new ESDatas<>();
                try {
                    data = clientUtil.searchList("alarm_info/_search", "getAlarmNumByArea", searchFields, Map.class);
                }catch (Exception e){
                    log.info("Es连接超时!"+e);
                }
                List<Map<String,Object>> result =  data.getAggregationBuckets("parentRegionId");
                if (result != null && result.size() > 0){
                    Map<String, Object> map = result.get(0);
                    regionInfo.put("alarmNum", map.get("doc_count"));
                }else {
                    regionInfo.put("alarmNum", 0);
                }
                List<Map<String, Object>> platformInfos = new ArrayList<>();
                regionInfo.put("platformInfos", platformInfos);
                alarmInfoRespVoList.add(regionInfo);
            }
            return alarmInfoRespVoList;
        }
    }

}
