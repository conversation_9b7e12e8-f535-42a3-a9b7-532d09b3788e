package cn.iocoder.zj.module.monitor.api.topology;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.topology.dto.TopologyDTO;
import cn.iocoder.zj.module.monitor.convert.topology.TopologyConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.topology.TopologyDO;
import cn.iocoder.zj.module.monitor.service.topology.TopologyService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

/**
 * @ClassName : TopologyApiImpl  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/9/25  14:59
 */

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class TopologyApiImpl implements TopologyApi {

    @Resource
    TopologyService topologyService;

    @Override
    public CommonResult<List<TopologyDTO>> get() {
        List<TopologyDTO> dtos = TopologyConvert.INSTANCE.DOConvertToDTO(topologyService.getList());
        return CommonResult.success(dtos);
    }

    @Override
    public CommonResult<Void> updatebatch(List<TopologyDTO> topologyUpdateDTOS) {
        List<TopologyDO> list = TopologyConvert.INSTANCE.DTOConvertoDo(topologyUpdateDTOS);
        topologyService.updatebatchTopology(list);
        return null;
    }
}
