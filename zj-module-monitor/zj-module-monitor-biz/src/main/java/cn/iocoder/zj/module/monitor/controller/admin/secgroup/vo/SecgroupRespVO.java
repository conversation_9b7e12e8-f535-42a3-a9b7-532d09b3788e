package cn.iocoder.zj.module.monitor.controller.admin.secgroup.vo;

import cn.iocoder.zj.module.monitor.dal.dataobject.secgrouprule.SecgroupRuleDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 安全组 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SecgroupRespVO extends SecgroupBaseVO {

    @Schema(description = "id", required = true)
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

    //云主机id
    @Schema(description = "云主机id")
    private String hostUuid;

    //安全组规则
    @Schema(description = "安全组规则")
    private List<SecgroupRuleDO> rules;

}
