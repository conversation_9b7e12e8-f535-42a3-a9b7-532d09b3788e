package cn.iocoder.zj.module.monitor.controller.admin.imageinfo.vo;

import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 镜像信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ImageInfoPageReqVO extends PageParam {

    @Schema(description = "镜像名称")
    private String name;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "镜像格式")
    private String format;

    @Schema(description = "CPU架构")
    private String cpuArch;

    @Schema(description = "操作系统")
    private String osType;

    @Schema(description = "镜像大小(字节)")
    private Long size;

    @Schema(description = "镜像类型")
    private String imageType;

    @Schema(description = "共享范围")
    private String sharingScope;

    @Schema(description = "镜像id")
    private String uuid;

    @Schema(description = "标签")
    private String tag;

    @Schema(description = "云主机uuid")
    private String hostUuid;

    @Schema(description = "镜像创建时间")
    private LocalDateTime vCreateDate;

    @Schema(description = "镜像更新时间")
    private LocalDateTime vUpdateDate;

    @Schema(description = "系统语言")
    private String osLanguage;

    @Schema(description = "最小内存要求(MB)")
    private BigDecimal minMemory;

    @Schema(description = "最小磁盘要求(GB)")
    private BigDecimal minDisk;

    @Schema(description = "磁盘驱动")
    private String diskDriver;

    @Schema(description = "网卡驱动")
    private String networkDriver;

    @Schema(description = "引导方式")
    private String bootMode;

    @Schema(description = "远程终端协议")
    private String remoteProtocol;

    @Schema(description = "应用平台")
    private String applicationPlatform;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(required = false,description = "开始时间",example = "2023-06-07")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String startTime;

    @Schema(required = false,description = "结束时间",example = "2023-06-07")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String endTime;

    @Schema(required = false,description = "主机tagId逗号拼接,1,2,3")
    private String tagIds;

    @Schema(required = false,description = "主机tagId逗号拼接,1,2,3")
    private List<Long> ids;
}
