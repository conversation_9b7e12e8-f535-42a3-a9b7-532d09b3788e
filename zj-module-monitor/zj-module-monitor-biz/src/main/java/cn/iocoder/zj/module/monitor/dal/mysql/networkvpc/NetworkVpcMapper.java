package cn.iocoder.zj.module.monitor.dal.mysql.networkvpc;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkVpcDTO;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkl2.NetworkL2DO;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkl3.NetworkL3DO;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkvpc.NetworkVpcDO;
import cn.iocoder.zj.module.monitor.util.StringUtil;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.monitor.controller.admin.networkvpc.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * VPC路由器 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface NetworkVpcMapper extends BaseMapperX<NetworkVpcDO> {

    default PageResult<NetworkVpcDO> selectPage(NetworkVpcPageReqVO reqVO, List<Map> platform) {
        List<String> uids = new ArrayList<>();
        if (StringUtil.isNotEmpty(reqVO.getIds())){
            uids=Arrays.asList(reqVO.getIds().split(","));
        }
        List<String> data = new ArrayList<>();
        if (platform.size()>0){
            for (Map map : platform){
                data.add(map.get("platformId").toString());
            }
        }
        LambdaQueryWrapperX<NetworkVpcDO> queryWrapperX = new LambdaQueryWrapperX<NetworkVpcDO>()
                .inIfPresent(NetworkVpcDO::getId, reqVO.getInPks());

        if(StringUtil.isNotEmpty(reqVO.getName())){
            queryWrapperX.likeIfPresent(NetworkVpcDO::getName, reqVO.getName());
        }
        if(StringUtil.isNotEmpty(reqVO.getStatus())) {
            queryWrapperX.eqIfPresent(NetworkVpcDO::getStatus, reqVO.getStatus());
        }
        if(StringUtil.isNotEmpty(reqVO.getState())) {
            queryWrapperX.eqIfPresent(NetworkVpcDO::getState, reqVO.getState());
        }
        if(StringUtil.isNotEmpty(reqVO.getClusterUuid())) {
            queryWrapperX.eqIfPresent(NetworkVpcDO::getClusterUuid, reqVO.getClusterUuid());
        }
        if(StringUtil.isNotEmpty(reqVO.getClusterName())) {
            queryWrapperX.likeIfPresent(NetworkVpcDO::getClusterName, reqVO.getClusterName());
        }
        if(StringUtil.isNotEmpty(reqVO.getManagementNetworkIp())) {
            queryWrapperX.likeIfPresent(NetworkVpcDO::getManagementNetworkIp, reqVO.getManagementNetworkIp());
        }
        if(reqVO.getPlatformId()!=null) {
            queryWrapperX.eqIfPresent(NetworkVpcDO::getPlatformId, reqVO.getPlatformId());
        }
        if(StringUtil.isNotEmpty(reqVO.getPlatformName())) {
            queryWrapperX.likeIfPresent(NetworkVpcDO::getPlatformName, reqVO.getPlatformName());
        }

        if (StringUtil.isNotEmpty(reqVO.getSortDirection()) && reqVO.getSortDirection().equals("asc")){
            if (reqVO.getSortBy().equals("createTime")){
                queryWrapperX.orderByAsc(NetworkVpcDO::getCreateTime);
            }
        }else {
            queryWrapperX.orderByDesc(NetworkVpcDO::getCreateTime);
        }

        if (!uids.isEmpty()) {
            queryWrapperX = (LambdaQueryWrapperX<NetworkVpcDO>) queryWrapperX.notIn(NetworkVpcDO::getId, uids);
        }

        if (data.size()>0){
            queryWrapperX = (LambdaQueryWrapperX<NetworkVpcDO>) queryWrapperX.in(NetworkVpcDO::getPlatformId, data);
        }else{
            queryWrapperX = (LambdaQueryWrapperX<NetworkVpcDO>) queryWrapperX.in(NetworkVpcDO::getPlatformId, "null");
        }

//        if (!reqVO.getTenantId().equals(0L)) {
//            queryWrapperX = (LambdaQueryWrapperX<NetworkVpcDO>) queryWrapperX.eqIfPresent(NetworkVpcDO::getTenantId, reqVO.getTenantId());
//        }

        return selectPage(reqVO, queryWrapperX);

    }

    default List<NetworkVpcDO> selectList(NetworkVpcExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<NetworkVpcDO>()
                .inIfPresent(NetworkVpcDO::getId, reqVO.getInPks())
                .eqIfPresent(NetworkVpcDO::getUuid, reqVO.getUuid())
                .likeIfPresent(NetworkVpcDO::getName, reqVO.getName())
                .eqIfPresent(NetworkVpcDO::getCpuNum, reqVO.getCpuNum())
                .eqIfPresent(NetworkVpcDO::getMemorySize, reqVO.getMemorySize())
                .eqIfPresent(NetworkVpcDO::getArchitecture, reqVO.getArchitecture())
                .eqIfPresent(NetworkVpcDO::getDns, reqVO.getDns())
                .eqIfPresent(NetworkVpcDO::getStatus, reqVO.getStatus())
                .eqIfPresent(NetworkVpcDO::getState, reqVO.getState())
                .eqIfPresent(NetworkVpcDO::getL3NetworkUuid, reqVO.getL3NetworkUuid())
                .eqIfPresent(NetworkVpcDO::getIp, reqVO.getIp())
                .eqIfPresent(NetworkVpcDO::getManagementNetworkUuid, reqVO.getManagementNetworkUuid())
                .eqIfPresent(NetworkVpcDO::getManagementNetworkIp, reqVO.getManagementNetworkIp())
                .likeIfPresent(NetworkVpcDO::getL3NetworkName, reqVO.getL3NetworkName())
                .eqIfPresent(NetworkVpcDO::getClusterUuid, reqVO.getClusterUuid())
                .likeIfPresent(NetworkVpcDO::getClusterName, reqVO.getClusterName())
                .eqIfPresent(NetworkVpcDO::getHypervisorType, reqVO.getHypervisorType())
                .eqIfPresent(NetworkVpcDO::getMac, reqVO.getMac())
                .eqIfPresent(NetworkVpcDO::getHostUuid, reqVO.getHostUuid())
                .likeIfPresent(NetworkVpcDO::getHostName, reqVO.getHostName())
                .betweenIfPresent(NetworkVpcDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(NetworkVpcDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(NetworkVpcDO::getPlatformName, reqVO.getPlatformName())
                .orderByDesc(NetworkVpcDO::getId));
    }

    void updateNetWorkVpcList(@Param("list") List<NetWorkVpcDTO> list);

    int deleteNetWorkVPCByNameList(@Param("list") List<NetworkVpcDO> list);

    List<Map<String, String>> getClusterSimpleInfo(@Param("platformId")Collection<Long> platformId);

    void deleteNetworkVpcByplatform(@Param("platformId") Long platformId);

    @TenantIgnore
    List<NetworkVpcDO> getnetvpcByList(@Param("list")List<String> list);
}
