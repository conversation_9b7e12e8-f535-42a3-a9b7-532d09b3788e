package cn.iocoder.zj.module.monitor.controller.admin.gatherlogdetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

/**
* 告警日志 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class GatherLogdetailBaseVO {

    @Schema(description = "租户绑定的采集设备id")
    private String uuid;

    @Schema(description = "trap告警ip")
    private String ip;

    @Schema(description = "主机名称")
    private String hostName;

    @Schema(description = "告警位置")
    private String alarmLocation;

    @Schema(description = "告警时间")
    private Date alarmDate;

    @Schema(description = "告警详情")
    private String alarmDetail;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "告警状态 0： 未读，1：已读")
    private Integer alarmState;

    @Schema(description = "是否解决0未创建工单，1未解决，2已解决")
    private Integer isSolved;
}
