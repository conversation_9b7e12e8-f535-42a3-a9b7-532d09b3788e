package cn.iocoder.zj.module.monitor.controller.admin.storageinfo;

import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.controller.admin.taggables.vo.TaggablesExportReqVO;
import cn.iocoder.zj.module.monitor.convert.tags.TagsConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.tags.TagsDO;
import cn.iocoder.zj.module.monitor.service.alarmhostrelation.AlarmHostRelationService;
import cn.iocoder.zj.module.monitor.service.hardwarestorage.HardwareStorageService;
import cn.iocoder.zj.module.monitor.service.taggables.TaggablesService;
import cn.iocoder.zj.module.monitor.service.taggables.enums.TagAssetTypeEnum;
import cn.iocoder.zj.module.monitor.service.tags.TagsService;
import cn.iocoder.zj.module.monitor.service.volumeinfo.VolumeInfoService;
import cn.iocoder.zj.module.monitor.util.AreaName.AreaUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import javax.validation.*;
import javax.servlet.http.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;

import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.monitor.controller.admin.storageinfo.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.storageinfo.StorageInfoDO;
import cn.iocoder.zj.module.monitor.convert.storageinfo.StorageInfoConvert;
import cn.iocoder.zj.module.monitor.service.storageinfo.StorageInfoService;

@Tag(name = "管理后台 - 存储设备信息")
@RestController
@RequestMapping("/monitor/storage-info")
@Validated
public class StorageInfoController {

    @Resource
    private StorageInfoService storageInfoService;
    @Resource
    private AlarmHostRelationService alarmHostRelationService;
    @Resource
    private HardwareStorageService hardwareStorageService;
    @Resource
    private VolumeInfoService volumeInfoService;

    @Resource
    private TagsService tagsService;

    @Resource
    private TaggablesService taggablesService;

    @PostMapping("/create")
    @Operation(summary = "创建存储设备信息")
    @PreAuthorize("@ss.hasPermission('monitor:storage-info:create')")
    @OperateLog(type = CREATE)
    public CommonResult<Long> createStorageInfo(@Valid @RequestBody StorageInfoCreateReqVO createReqVO) {
        return success(storageInfoService.createStorageInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新存储设备信息")
    @PreAuthorize("@ss.hasPermission('monitor:storage-info:update')")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateStorageInfo(@Valid @RequestBody StorageInfoUpdateReqVO updateReqVO) {
        storageInfoService.updateStorageInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除存储设备信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('monitor:storage-info:delete')")
    @OperateLog(type = DELETE)
    public CommonResult<Boolean> deleteStorageInfo(@RequestParam("id") Long id) {
        storageInfoService.deleteStorageInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得存储设备信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<StorageInfoRespVO> getStorageInfo(@RequestParam("uuid") String uuid) {
        StorageInfoDO storageInfo = storageInfoService.getStorageInfo(uuid);
        Collection<Long> alarmIds = alarmHostRelationService.getRelationIdsByUuid(storageInfo.getUuid());
        StorageInfoRespVO respVO = StorageInfoConvert.INSTANCE.convert(storageInfo);
        //查询存储对应宿主机数量
        Integer hareWareCount = hardwareStorageService.getHareWareCountByStorageUuid(storageInfo.getUuid());
        Integer volumeCount =volumeInfoService.getVolumeCountByStorageUuid(storageInfo.getUuid());
        respVO.setHardwareCount(hareWareCount);
        respVO.setVolumeCount(volumeCount);
        respVO.setAlarmIds(alarmIds);
        respVO.setVirtualUtilization(
                respVO.getTotalCapacity() > 0 ?
                        new BigDecimal(respVO.getUsedCapacity() * 100.00 / respVO.getTotalCapacity()) : new BigDecimal(0));
        respVO.setPhysicalUsage(respVO.getTotalPhysicalCapacity().subtract(respVO.getAvailablePhysicalCapacity()));
        //标签
        List<TagsDO> tag = tagsService.getTagListByTagGables(new TaggablesExportReqVO()
                .setTaggableId(respVO.getId())
                .setTaggableType(TagAssetTypeEnum.STORAGE.getCode())
        );
        respVO.setTags(TagsConvert.INSTANCE.convertList(tag));
        return success(respVO);
    }

    @GetMapping("/list")
    @Operation(summary = "获得存储设备信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    public CommonResult<List<StorageInfoRespVO>> getStorageInfoList(@RequestParam("ids") Collection<Long> ids) {
        List<StorageInfoDO> list = storageInfoService.getStorageInfoList(ids);
        return success(StorageInfoConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得存储设备信息分页")
    @TenantIgnore
    public CommonResult<PageResult<StorageInfoRespVO>> getStorageInfoPage(@Valid StorageInfoPageReqVO pageVO) {
        PageResult<StorageInfoDO> pageResult = storageInfoService.getStorageInfoPage(pageVO);
        PageResult<StorageInfoRespVO> list = StorageInfoConvert.INSTANCE.convertPage(pageResult);
        list.getList().forEach(item -> {
            List<TagsDO> tag = tagsService.getTagListByTagGables(new TaggablesExportReqVO()
                    .setTaggableId(item.getId())
                    .setTaggableType(TagAssetTypeEnum.STORAGE.getCode())
            );
            item.setTags(TagsConvert.INSTANCE.convertList(tag)).setVirtualUtilization(
                            item.getTotalCapacity() > 0 ?
                                    new BigDecimal(item.getUsedCapacity() * 100.00 / item.getTotalCapacity()) : new BigDecimal(0))
                    .setPhysicalUsage(item.getTotalPhysicalCapacity().subtract(item.getAvailablePhysicalCapacity()))
                    .setCapacityUtilization(item.getCapacityUtilization() != null ? item.getCapacityUtilization() : new BigDecimal(0));
        });
        return success(list);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出存储设备信息 Excel")
    @OperateLog(type = EXPORT)
    @PreAuthorize("@ss.hasPermission('monitor:storage-info:export')")
    public void exportStorageInfoExcel(@Valid StorageInfoExportReqVO exportReqVO,
                                       HttpServletResponse response) throws IOException {
        List<StorageInfoDO> list = storageInfoService.getStorageInfoList(exportReqVO);
        // 导出 Excel
        List<StorageInfoExcelVO> datas = StorageInfoConvert.INSTANCE.convertList02(list);
        datas.forEach(storage -> {
            String areaName = AreaUtil.getFullAreaName(storage.getRegionId());
            storage.setRegionName(areaName);
        });
        ExcelUtils.write(response, "存储设备信息.xls", "数据", StorageInfoExcelVO.class, datas);
    }

    @GetMapping("/getCpuInfo")
    @Operation(summary = "获得存储CPU数据")
    @Parameter(name = "tenantid", description = "租户ID", required = true, example = "1")
    @Parameter(name = "uuid", description = "主机UUID", required = true, example = "6ef87447f2134a99973145f44e27635a")
    @Parameter(name = "timeStr", description = "时间条件,1h(1小时),1d(1天),1M/d(1月),1y/d(1年)", required = true, example = "24h")
    public CommonResult<List<Map>> getStorageCpuInfo(@RequestParam("tenantid") Long tenantid, @RequestParam("uuid") String uuid, @RequestParam("timeStr") String timeStr) {
        List<Map> storageCpuInfo = storageInfoService.getStorageCpuInfo(tenantid, uuid, timeStr);
        return success(storageCpuInfo);
    }


    @GetMapping("/getUsedCapacityInPercent")
    @Operation(summary = "获得存储已用容量百分比")
    @Parameter(name = "uuid", description = "存储UUID", required = true, example = "6ef87447f2134a99973145f44e27635a")
    @Parameter(name = "startTime", description = "开始时间", required = true, example = "1686758400000")
    @Parameter(name = "endTime", description = "结束时间", required = true, example = "1686895554000")

    @PermitAll
    public CommonResult<List<Object>> getUsedCapacityInPercent(@RequestParam("uuid") String uuid,
                                                               @RequestParam("startTime") Long startTime,
                                                               @RequestParam("endTime") Long endTime) {
        List<Object> storageUsedCapacityInfo = storageInfoService.getUsedCapacityInPercent(uuid, startTime, endTime);
        return success(storageUsedCapacityInfo);
    }


    @GetMapping("/slavePage")
    @Operation(summary = "获得存储设备信息分页(从库数据)")
    @TenantIgnore
    public CommonResult<PageResult<StorageInfoRespVO>> getStorageInfoSlavePage(@Valid StorageInfoPageReqVO pageVO) {
        PageResult<StorageInfoDO> pageResult = storageInfoService.getStorageInfoSlavePage(pageVO);
        return success(StorageInfoConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/slaveget")
    @Operation(summary = "获得存储设备信息(从库数据)")
    @Parameter(name = "uuid", description = "编号", required = true, example = "1024")
    public CommonResult<StorageInfoRespVO> getStorageSlaveInfo(@RequestParam("uuid") String uuid) {
        StorageInfoDO storageInfo = storageInfoService.getStorageSlaveInfo(uuid);
        StorageInfoRespVO respVO = StorageInfoConvert.INSTANCE.convert(storageInfo);
        return success(respVO);
    }


    @GetMapping("/masterget")
    @Operation(summary = "获得存储设备信息(主库数据)")
    @Parameter(name = "uuid", description = "编号", required = true, example = "1024")
    public CommonResult<StorageInfoRespVO> masterget(@RequestParam("uuid") String uuid) {
        StorageInfoDO storageInfo = storageInfoService.getStorageMasterInfo(uuid);
        StorageInfoRespVO respVO = StorageInfoConvert.INSTANCE.convert(storageInfo);
        return success(respVO);
    }

    @GetMapping("/selectStorageList")
    @Operation(summary = "用于在告警模板中选择主存储")
    @TenantIgnore
    public CommonResult<PageResult<StorageInfoRespVO>> selectStorageList(@Valid StorageInfoPageReqVO pageVO) {
        PageResult<StorageInfoDO> pageResult = storageInfoService.selectStorageList(pageVO);
        return success(StorageInfoConvert.INSTANCE.convertPage(pageResult));
    }
}
