package cn.iocoder.zj.module.monitor.dal.mysql.networkl3;

import java.util.*;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL3DTO;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkl2.NetworkL2DO;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkl3.NetworkL3DO;
import cn.iocoder.zj.module.monitor.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.monitor.controller.admin.networkl3.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 三级网络资源 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface NetworkL3Mapper extends BaseMapperX<NetworkL3DO> {

    default PageResult<NetworkL3DO> selectPage(NetworkL3PageReqVO reqVO, List<Map> platform) {
        List<String> uids = new ArrayList<>();
        if (StringUtil.isNotEmpty(reqVO.getIds())){
            uids=Arrays.asList(reqVO.getIds().split(","));
        }
        List<String> data = new ArrayList<>();
        if (platform.size()>0){
            for (Map map : platform){
                data.add(map.get("platformId").toString());
            }
        }

        LambdaQueryWrapper<NetworkL3DO> queryWrapperX = new LambdaQueryWrapperX<NetworkL3DO>()
                .inIfPresent(NetworkL3DO::getId, reqVO.getInPks())
                .eqIfPresent(NetworkL3DO::getUuid, reqVO.getUuid())
                .eqIfPresent(NetworkL3DO::getL2NetworkUuid, reqVO.getL2NetworkUuid())
                .likeIfPresent(NetworkL3DO::getName, reqVO.getName())
                .eqIfPresent(NetworkL3DO::getDns, reqVO.getDns())
                .eqIfPresent(NetworkL3DO::getNetworkServices, reqVO.getNetworkServices())
                .eqIfPresent(NetworkL3DO::getStartIp, reqVO.getStartIp())
                .eqIfPresent(NetworkL3DO::getEndIp, reqVO.getEndIp())
                .eqIfPresent(NetworkL3DO::getNetmask, reqVO.getNetmask())
                .eqIfPresent(NetworkL3DO::getGateway, reqVO.getGateway())
                .eqIfPresent(NetworkL3DO::getNetworkSegment, reqVO.getNetworkSegment())
                .eqIfPresent(NetworkL3DO::getNetworkCidr, reqVO.getNetworkCidr())
                .eqIfPresent(NetworkL3DO::getNextHopIp, reqVO.getNextHopIp())
                .eqIfPresent(NetworkL3DO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(NetworkL3DO::getPlatformName, reqVO.getPlatformName())
                .between(ObjectUtil.isNotEmpty(reqVO.getStartTime()) && ObjectUtil.isNotEmpty(reqVO.getEndTime())
                        , NetworkL3DO::getCreateTime, reqVO.getStartTime(), reqVO.getEndTime());
        if (StringUtil.isNotEmpty(reqVO.getSortDirection()) && reqVO.getSortDirection().equals("asc")){
            if (reqVO.getSortBy().equals("createTime")){
                queryWrapperX.orderByAsc(NetworkL3DO::getCreateTime);
            }
        }else {
            queryWrapperX.orderByDesc(NetworkL3DO::getCreateTime);
        }
        if (StringUtil.isNotEmpty(reqVO.getType())){
            queryWrapperX = (LambdaQueryWrapperX<NetworkL3DO>) queryWrapperX.eq(NetworkL3DO::getType, reqVO.getType());
        }
        if (!uids.isEmpty()) {
            queryWrapperX = (LambdaQueryWrapperX<NetworkL3DO>) queryWrapperX.notIn(NetworkL3DO::getId, uids);
        }

        if (data.size()>0){
            queryWrapperX = (LambdaQueryWrapperX<NetworkL3DO>) queryWrapperX.in(NetworkL3DO::getPlatformId, data);
        }else {
            queryWrapperX = (LambdaQueryWrapperX<NetworkL3DO>) queryWrapperX.in(NetworkL3DO::getPlatformId, "null");
        }
//        if (!reqVO.getTenantId().equals(0L)) {
//            queryWrapperX = (LambdaQueryWrapperX<NetworkL3DO>) queryWrapperX.eqIfPresent(NetworkL3DO::getTenantId, reqVO.getTenantId());
//        }

        return selectPage(reqVO, queryWrapperX);

    }

    default List<NetworkL3DO> selectList(NetworkL3ExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<NetworkL3DO>()
                .eqIfPresent(NetworkL3DO::getUuid, reqVO.getUuid())
                .eqIfPresent(NetworkL3DO::getL2NetworkUuid, reqVO.getL2NetworkUuid())
                .likeIfPresent(NetworkL3DO::getName, reqVO.getName())
                .eqIfPresent(NetworkL3DO::getDns, reqVO.getDns())
                .eqIfPresent(NetworkL3DO::getType, reqVO.getType())
                .eqIfPresent(NetworkL3DO::getNetworkServices, reqVO.getNetworkServices())
                .eqIfPresent(NetworkL3DO::getStartIp, reqVO.getStartIp())
                .eqIfPresent(NetworkL3DO::getEndIp, reqVO.getEndIp())
                .eqIfPresent(NetworkL3DO::getNetmask, reqVO.getNetmask())
                .eqIfPresent(NetworkL3DO::getGateway, reqVO.getGateway())
                .eqIfPresent(NetworkL3DO::getNetworkSegment, reqVO.getNetworkSegment())
                .eqIfPresent(NetworkL3DO::getNetworkCidr, reqVO.getNetworkCidr())
                .eqIfPresent(NetworkL3DO::getNextHopIp, reqVO.getNextHopIp())
                .betweenIfPresent(NetworkL3DO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(NetworkL3DO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(NetworkL3DO::getPlatformName, reqVO.getPlatformName())
                .orderByDesc(NetworkL3DO::getId));
    }

    void updateNetWorkL3List(@Param("list") List<NetWorkL3DTO> list);

   default PageResult<NetworkL3DO> selectVpcPage(NetworkL3PageReqVO pageVO){
       List<String> uids = new ArrayList<>();
       if (StringUtil.isNotEmpty(pageVO.getIds())){
           uids=Arrays.asList(pageVO.getIds().split(","));
       }
       LambdaQueryWrapperX<NetworkL3DO> queryWrapperX = new LambdaQueryWrapperX<NetworkL3DO>()

               .eqIfPresent(NetworkL3DO::getUuid, pageVO.getUuid())
               .eqIfPresent(NetworkL3DO::getL2NetworkUuid, pageVO.getL2NetworkUuid())
               .likeIfPresent(NetworkL3DO::getName, pageVO.getName())
               .eqIfPresent(NetworkL3DO::getDns, pageVO.getDns())
               .eqIfPresent(NetworkL3DO::getType, pageVO.getType())
               .eqIfPresent(NetworkL3DO::getNetworkServices, pageVO.getNetworkServices())
               .eqIfPresent(NetworkL3DO::getStartIp, pageVO.getStartIp())
               .eqIfPresent(NetworkL3DO::getEndIp, pageVO.getEndIp())
               .eqIfPresent(NetworkL3DO::getNetmask, pageVO.getNetmask())
               .eqIfPresent(NetworkL3DO::getGateway, pageVO.getGateway())
               .eqIfPresent(NetworkL3DO::getNetworkSegment, pageVO.getNetworkSegment())
               .eqIfPresent(NetworkL3DO::getNetworkCidr, pageVO.getNetworkCidr())
               .eqIfPresent(NetworkL3DO::getNextHopIp, pageVO.getNextHopIp())
               .eqIfPresent(NetworkL3DO::getPlatformId, pageVO.getPlatformId())
               .likeIfPresent(NetworkL3DO::getPlatformName, pageVO.getPlatformName())
               .orderByDesc(NetworkL3DO::getId);
       queryWrapperX =  (LambdaQueryWrapperX<NetworkL3DO>) queryWrapperX.notIn(NetworkL3DO::getType, "L3BasicNetwork");
       if (!uids.isEmpty()) {
           queryWrapperX = (LambdaQueryWrapperX<NetworkL3DO>) queryWrapperX.notIn(NetworkL3DO::getId, uids);
       }
//        if (!reqVO.getTenantId().equals(0L)) {
//            queryWrapperX = (LambdaQueryWrapperX<NetworkL3DO>) queryWrapperX.eqIfPresent(NetworkL3DO::getTenantId, reqVO.getTenantId());
//        }

       return selectPage(pageVO, queryWrapperX);
   }

    void deleteNetworkL3Byplatform(@Param("platformId") Long platformId);

    List<NetWorkL3DTO> getNetworkL3ByPlatformId(@Param("platformId")Long platformId);
}
