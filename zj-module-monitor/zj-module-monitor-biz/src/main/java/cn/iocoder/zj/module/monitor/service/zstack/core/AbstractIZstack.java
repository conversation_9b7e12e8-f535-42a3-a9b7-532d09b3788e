package cn.iocoder.zj.module.monitor.service.zstack.core;


import cn.iocoder.zj.module.monitor.dal.dataobject.alarmconfig.AlarmConfigDO;

/**
 * @ClassName : AbstractIZstackCloud  //类名
 * @Description : ZSTACK 公共抽象类  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/25  17:20
 */

public abstract class AbstractIZstack  {

    /**
     * @description: cpu 使用率
     * @param:
     * @return: void
     * <AUTHOR>
     * @date: 2023/5/25 14:17
     */
    public abstract String cpuUsage();



    /**
     * @description: 内存使用率
     * @param:
     * @return: void
     * <AUTHOR>
     * @date: 2023/5/25 14:19
     */
    public abstract void memoryUsage();

    /**
     * @description: 网络上下行带宽
     * @param:
     * @return: void
     * <AUTHOR>
     * @date: 2023/5/25 14:20
     */
    public abstract  void netWorkUpdown();

    /**
     * @description: 网络收发包信息
     * <AUTHOR>
     * @date 2023/5/25 14:22
     * @version 1.0
     */
    public abstract void netWorkSendOrHarvest();
}
