package cn.iocoder.zj.module.monitor.service.tagplan;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.monitor.controller.admin.tagplan.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.tagplan.TagPlanDO;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

public interface TagPlanService {

    /**
     * 创建巡检计划
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTagPlan(@Valid TagPlanCreateReqVO createReqVO);

    /**
     * 更新巡检计划
     *
     * @param updateReqVO 更新信息
     */
    void updateTagPlan(@Valid TagPlanUpdateReqVO updateReqVO);

    /**
     * 删除巡检计划
     *
     * @param id 编号
     */
    void deleteTagPlan(Long id);

    /**
     * 获得巡检计划
     *
     * @param id 编号
     * @return 巡检计划
     */
    TagPlanRespVO getTagPlan(Long id);

    /**
     * 获得巡检计划列表
     *
     * @param ids 编号
     * @return 巡检计划列表
     */
    List<TagPlanDO> getTagPlanList(Collection<Long> ids);

    /**
     * 获得巡检计划分页
     *
     * @param pageReqVO 分页查询
     * @return 巡检计划分页
     */
    PageResult<TagPlanRespVO> getTagPlanPage(TagPlanPageReqVO pageReqVO);

    /**
     * 获得巡检计划列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 巡检计划列表
     */
    List<TagPlanDO> getTagPlanList(TagPlanExportReqVO exportReqVO);

    /**
     * 根据jobId获取巡检计划
     *
     * @param jobId
     * @return 巡检计划
     */
    TagPlanDO getTagPlanByJobId(Long jobId);

    /**
     * 修改最近执行时间和下次执行时间
     *
     * @param patrolPlanDO
     */
    void updateLastTimeAndNextTime(TagPlanDO patrolPlanDO);

    void executeTagPlan(Long id);

    void startOrStopTagPlan(Long id, int status);

    void runTagTask(TagPlanDO patrolPlanDO);

    Long createTagRecord(TagRecordCreateReqVO createReqVO);

    PageResult<TagRecordRespVO> getTagRecordPage(TagRecordPageReqVO pageVO);

    void uploadWord(Long id, HttpServletResponse response);

    String getPlatformSelectList(Long sysSettingTenant);
}
