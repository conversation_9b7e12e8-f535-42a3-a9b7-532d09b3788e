package cn.iocoder.zj.module.monitor.dal.dataobject.networkl2;

import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 二级网络信息 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_network_l2")
@KeySequence("monitor_network_l2_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NetworkL2DO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 二级网络名称
     */
    private String name;
    /**
     * 二级网络uuid
     */
    private String uuid;
    /**
     * 网卡
     */
    private String physicalInterface;
    /**
     * 二级网络类型
     */
    private String type;
    /**
     * vlan
     */
    private String vlan;
    /**
     * 虚拟网络标识
     */
    private Integer virtualNetworkId;
    /**
     * 地区名称
     */
    private String regionName;
    /**
     * 地区id
     */
    private Long regionId;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;

    /**
     * @description: 租户编号
     * <AUTHOR>
     * @date 2023/8/8 11:37
     * @version 1.0
     */
    private Long tenantId;

    private String typeName;
}
