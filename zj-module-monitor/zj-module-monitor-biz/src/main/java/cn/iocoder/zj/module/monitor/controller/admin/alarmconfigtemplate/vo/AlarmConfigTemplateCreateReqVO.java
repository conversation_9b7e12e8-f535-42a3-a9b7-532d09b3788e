package cn.iocoder.zj.module.monitor.controller.admin.alarmconfigtemplate.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 告警配置模板创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AlarmConfigTemplateCreateReqVO extends AlarmConfigTemplateBaseVO {

}
