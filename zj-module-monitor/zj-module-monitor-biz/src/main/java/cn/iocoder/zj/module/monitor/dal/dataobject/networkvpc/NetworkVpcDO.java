package cn.iocoder.zj.module.monitor.dal.dataobject.networkvpc;

import lombok.*;

import java.util.*;

import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * VPC路由器 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_network_vpc")
@KeySequence("monitor_network_vpc_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NetworkVpcDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * vpc 路由器uuid
     */
    private String uuid;
    /**
     * 路由器名称
     */
    private String name;
    /**
     * cpu
     */
    private Integer cpuNum;
    /**
     * 内存
     */
    private Long memorySize;
    /**
     * CPU架构
     */
    private String architecture;
    /**
     * 路由dns
     */
    private String dns;
    /**
     * 就绪状态
     */
    private String status;
    /**
     * 启用状态
     */
    private String state;
    /**
     * L3网络uuid
     */
    private String l3NetworkUuid;
    /**
     * ipv4 Ip
     */
    private String ip;
    /**
     * 管理网络uuid
     */
    private String managementNetworkUuid;
    /**
     * 管理网络ip
     */
    private String managementNetworkIp;
    /**
     * 三层网络名称
     */
    private String l3NetworkName;
    /**
     * 集群uuid
     */
    private String clusterUuid;
    /**
     * 集群名称
     */
    private String clusterName;
    /**
     * 虚拟化技术
     */
    private String hypervisorType;
    /**
     * mac地址（IPV4)
     */
    private String mac;
    /**
     * 宿主机uuid
     */
    private String hostUuid;
    /**
     * 宿主机名称
     */
    private String hostName;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;

    /**
     * @description: 
     * <AUTHOR>
     * @date 2023/8/9 14:47
     * @version 1.0
     */
    private Long tenantId;
}
