package cn.iocoder.zj.module.monitor.controller.admin.secgroup.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 安全组 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SecgroupBaseVO {

    @Schema(description = "uuid")
    private String uuid;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "是否共享")
    private Boolean isPublic;

    @Schema(description = "默认共享范围", required = true)
    @NotNull(message = "默认共享范围不能为空")
    private String publicScope;

    @Schema(description = "共享设置的来源, local: 本地设置, cloud: 从云上同步过来")
    private String publicSrc;

    @Schema(description = "是否垃圾", required = true)
    @NotNull(message = "是否垃圾不能为空")
    private Boolean isDirty;

    @Schema(description = "区域id", required = true)
    @NotNull(message = "区域id不能为空")
    private String cloudregionId;

    @Schema(description = "vpcId")
    private String vpcId;

    @Schema(description = "全局vpcId")
    private String globalvpcId;

    @Schema(description = "备注")
    private String description;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

}
