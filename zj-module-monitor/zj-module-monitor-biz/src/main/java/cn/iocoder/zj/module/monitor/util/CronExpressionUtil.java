package cn.iocoder.zj.module.monitor.util;

import cn.hutool.core.util.StrUtil;
import org.springframework.scheduling.support.CronExpression;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * Cron表达式工具类
 * 用于根据巡检周期和时间生成Cron表达式
 *
 * <AUTHOR>
 */
public class CronExpressionUtil {

    /**
     * 生成Cron表达式
     *
     * @param periodType 周期类型：day-每天, week-每周, month-每月
     * @param timeStr    时间字符串，格式为 HH:mm 或 HH:mm:ss
     * @param weekDay    周几，当periodType为week时使用，1-7代表周一到周日
     * @param monthDay   每月几号，当periodType为month时使用，1-31
     * @return Cron表达式
     */
    public static String generateCronExpression(String periodType, String timeStr, Integer weekDay, Integer monthDay) {
        if (StrUtil.isBlank(timeStr)) {
            throw new IllegalArgumentException("时间不能为空");
        }

        // 解析时间
        String[] timeParts = timeStr.split(":");
        String hour = timeParts[0];
        String minute = timeParts.length > 1 ? timeParts[1] : "00";
        String second = timeParts.length > 2 ? timeParts[2] : "00";

        // 根据周期类型生成表达式
        StringBuilder cronExpression = new StringBuilder();
        cronExpression.append(second).append(" ").append(minute).append(" ").append(hour);

        switch (periodType.toLowerCase()) {
            case "day":
                // 每天执行，格式：秒 分 时 * * ?
                cronExpression.append(" * * ?");
                break;
            case "week":
                // 每周执行，格式：秒 分 时 ? * 周几
                // 注意：Cron表达式中，1-7代表周日到周六，而我们的输入1-7代表周一到周日
                int cronWeekDay = weekDay == null ? 1 : (weekDay % 7); // 默认周一(2)，转换为Cron的周几
                cronExpression.append(" ? * ").append(cronWeekDay);
                break;
            case "month":
                // 每月执行，格式：秒 分 时 日 * ?
                int day = monthDay == null ? 1 : monthDay;
                cronExpression.append(" ").append(day).append(" * ?");
                break;
            default:
                throw new IllegalArgumentException("不支持的周期类型: " + periodType);
        }

        return cronExpression.toString();
    }

    /**
     * 生成Cron表达式（简化版，适用于每天执行）
     *
     * @param timeStr 时间字符串，格式为 HH:mm 或 HH:mm:ss
     * @return Cron表达式
     */
    public static String generateDailyCronExpression(String timeStr) {
        return generateCronExpression("day", timeStr, null, null);
    }

    /**
     * 生成Cron表达式（简化版，适用于每周执行）
     *
     * @param timeStr 时间字符串，格式为 HH:mm 或 HH:mm:ss
     * @param weekDay 周几，1-7代表周一到周日
     * @return Cron表达式
     */
    public static String generateWeeklyCronExpression(String timeStr, Integer weekDay) {
        return generateCronExpression("week", timeStr, weekDay, null);
    }

    /**
     * 生成Cron表达式（简化版，适用于每月执行）
     *
     * @param timeStr  时间字符串，格式为 HH:mm 或 HH:mm:ss
     * @param monthDay 每月几号，1-31
     * @return Cron表达式
     */
    public static String generateMonthlyCronExpression(String timeStr, Integer monthDay) {
        return generateCronExpression("month", timeStr, null, monthDay);
    }

    /**
     * 获取Cron表达式的描述
     *
     * @param cronExpression Cron表达式
     * @return 描述
     */
    public static String getCronDescription(String cronExpression) {
        if (StrUtil.isBlank(cronExpression)) {
            return "无效的Cron表达式";
        }

        String[] parts = cronExpression.split("\\s+");
        if (parts.length < 6) {
            return "无效的Cron表达式";
        }

        String second = parts[0];
        String minute = parts[1];
        String hour = parts[2];
        String dayOfMonth = parts[3];
        String month = parts[4];
        String dayOfWeek = parts[5];

        StringBuilder description = new StringBuilder();

        // 添加时间部分
        description.append("每");

        // 判断周期类型
        if ("*".equals(dayOfMonth) && "?".equals(dayOfWeek)) {
            description.append("天");
        } else if ("?".equals(dayOfMonth) && !"?".equals(dayOfWeek)) {
            description.append("周");
            // 转换星期
            switch (dayOfWeek) {
                case "1":
                    description.append("日");
                    break;
                case "2":
                    description.append("一");
                    break;
                case "3":
                    description.append("二");
                    break;
                case "4":
                    description.append("三");
                    break;
                case "5":
                    description.append("四");
                    break;
                case "6":
                    description.append("五");
                    break;
                case "7":
                    description.append("六");
                    break;
                default:
                    description.append(dayOfWeek);
                    break;
            }
        } else if (!"*".equals(dayOfMonth) && "?".equals(dayOfWeek)) {
            description.append("月").append(dayOfMonth).append("号");
        }

        // 添加时间
        description.append(" ").append(hour).append(":").append(minute);
        if (!"0".equals(second) && !"00".equals(second)) {
            description.append(":").append(second);
        }

        return description.toString();
    }

    /**
     * 计算下次执行时间
     *
     * @param cronExpression Cron表达式
     * @param currentTime    当前时间
     * @return 下次执行时间
     */
    public static Date getNextExecutionTime(String cronExpression, Date currentTime) {
        try {
            // 使用Spring的CronExpression类
            CronExpression cron = CronExpression.parse(cronExpression);
            LocalDateTime dateTime = LocalDateTime.ofInstant(currentTime.toInstant(), ZoneId.systemDefault());
            LocalDateTime next = cron.next(dateTime);
            if (next == null) {
                return null;
            }
            return Date.from(next.atZone(ZoneId.systemDefault()).toInstant());
        } catch (Exception e) {
            throw new IllegalArgumentException("无效的Cron表达式: " + cronExpression, e);
        }
    }

    /**
     * 计算下次执行时间（使用当前时间）
     *
     * @param cronExpression Cron表达式
     * @return 下次执行时间
     */
    public static Date getNextExecutionTime(String cronExpression) {
        return getNextExecutionTime(cronExpression, new Date());
    }

    /**
     * 计算多次执行时间
     *
     * @param cronExpression Cron表达式
     * @param currentTime    当前时间
     * @param count          需要计算的次数
     * @return 执行时间列表
     */
    public static List<Date> getNextExecutionTimes(String cronExpression, Date currentTime, int count) {
        if (count <= 0) {
            return Collections.emptyList();
        }

        List<Date> executionTimes = new ArrayList<>(count);
        try {
            // 使用Spring的CronExpression类
            CronExpression cron = CronExpression.parse(cronExpression);
            LocalDateTime dateTime = LocalDateTime.ofInstant(currentTime.toInstant(), ZoneId.systemDefault());

            for (int i = 0; i < count; i++) {
                LocalDateTime next = cron.next(dateTime);
                if (next == null) {
                    break;
                }
                Date nextDate = Date.from(next.atZone(ZoneId.systemDefault()).toInstant());
                executionTimes.add(nextDate);
                dateTime = next;
            }

            return executionTimes;
        } catch (Exception e) {
            throw new IllegalArgumentException("无效的Cron表达式: " + cronExpression, e);
        }
    }
}