package cn.iocoder.zj.module.monitor.controller.admin.hardwarenic;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.monitor.controller.admin.hardwarenic.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.hardwarenic.HardwareNicDO;
import cn.iocoder.zj.module.monitor.convert.hardwarenic.HardwareNicConvert;
import cn.iocoder.zj.module.monitor.service.hardwarenic.HardwareNicService;

@Tag(name = "管理后台 - 物理机网络关系")
@RestController
@RequestMapping("/monitor/hardware-nic")
@Validated
public class HardwareNicController {

    @Resource
    private HardwareNicService hardwareNicService;

    @PostMapping("/create")
    @Operation(summary = "创建物理机网络关系")
    //@PreAuthorize("@ss.hasPermission('monitor:hardware-nic:create')")
    public CommonResult<Long> createHardwareNic(@Valid @RequestBody HardwareNicCreateReqVO createReqVO) {
        return success(hardwareNicService.createHardwareNic(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新物理机网络关系")
    //@PreAuthorize("@ss.hasPermission('monitor:hardware-nic:update')")
    public CommonResult<Boolean> updateHardwareNic(@Valid @RequestBody HardwareNicUpdateReqVO updateReqVO) {
        hardwareNicService.updateHardwareNic(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除物理机网络关系")
    @Parameter(name = "id", description = "编号", required = true)
    //@PreAuthorize("@ss.hasPermission('monitor:hardware-nic:delete')")
    public CommonResult<Boolean> deleteHardwareNic(@RequestParam("id") Long id) {
        hardwareNicService.deleteHardwareNic(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得物理机网络关系")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('monitor:hardware-nic:query')")
    public CommonResult<HardwareNicRespVO> getHardwareNic(@RequestParam("id") Long id) {
        HardwareNicDO hardwareNic = hardwareNicService.getHardwareNic(id);
        return success(HardwareNicConvert.INSTANCE.convert(hardwareNic));
    }

    @GetMapping("/list")
    @Operation(summary = "获得物理机网络关系列表")
    @Parameter(name = "hardwareUuid", description = "编号列表", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('monitor:hardware-nic:query')")
    public CommonResult<List<HardwareNicRespVO>> getHardwareNicList(@Valid HardwareNicReqVO reqVO) {
        List<HardwareNicDO> list = hardwareNicService.getHardwareNicList(reqVO);
        return success(HardwareNicConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得物理机网络关系分页")
    //@PreAuthorize("@ss.hasPermission('monitor:hardware-nic:query')")
    public CommonResult<PageResult<HardwareNicRespVO>> getHardwareNicPage(@Valid HardwareNicPageReqVO pageVO) {
        PageResult<HardwareNicDO> pageResult = hardwareNicService.getHardwareNicPage(pageVO);
        return success(HardwareNicConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出物理机网络关系 Excel")
    //@PreAuthorize("@ss.hasPermission('monitor:hardware-nic:export')")
    @OperateLog(type = EXPORT)
    public void exportHardwareNicExcel(@Valid HardwareNicExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<HardwareNicDO> list = hardwareNicService.getHardwareNicList(exportReqVO);
        // 导出 Excel
        List<HardwareNicExcelVO> datas = HardwareNicConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "物理机网络关系.xls", "数据", HardwareNicExcelVO.class, datas);
    }

}
