package cn.iocoder.zj.module.monitor.service.eip;

import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostnic.HostNicDO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.zj.module.monitor.controller.admin.eip.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.eip.EipDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.convert.eip.EipConvert;
import cn.iocoder.zj.module.monitor.dal.mysql.eip.EipMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.*;

/**
 * 弹性公网 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EipServiceImpl implements EipService {

    @Resource
    private EipMapper eipMapper;

    @Override
    public Long createEip(EipCreateReqVO createReqVO) {
        // 插入
        EipDO eip = EipConvert.INSTANCE.convert(createReqVO);
        eipMapper.insert(eip);
        // 返回
        return eip.getId();
    }

    @Override
    public void updateEip(EipUpdateReqVO updateReqVO) {
        // 校验存在
        validateEipExists(updateReqVO.getId());
        // 更新
        EipDO updateObj = EipConvert.INSTANCE.convert(updateReqVO);
        eipMapper.updateById(updateObj);
    }

    @Override
    public void deleteEip(Long id) {
        // 校验存在
        validateEipExists(id);
        // 删除
        eipMapper.deleteById(id);
    }

    private void validateEipExists(Long id) {
        if (eipMapper.selectById(id) == null) {
            throw exception(EIP_NOT_EXISTS);
        }
    }

    @Override
    @TenantIgnore
    public EipDO getEip(Long id) {
        return eipMapper.selectById(id);
    }

    @Override
    public List<EipDO> getEipList(Collection<Long> ids) {
        return eipMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<EipDO> getEipPage(EipPageReqVO pageReqVO) {
        return eipMapper.selectPage(pageReqVO);
    }

    @Override
    public List<EipDO> getEipList(EipExportReqVO exportReqVO) {
        return eipMapper.selectList(exportReqVO);
    }

    @Override
    public void createEipList(List<EipDO> list) {
        eipMapper.insertBatch(list);
    }

    @Override
    public void updateEips(List<EipDO> list) {
        eipMapper.updateBatch(list);
    }

    @Override
    public void deleteEips(List<EipDO> list) {
        List<Long> ids = list.stream().map(EipDO::getId).toList();
        eipMapper.deleteBatchIds(ids);
    }

}
