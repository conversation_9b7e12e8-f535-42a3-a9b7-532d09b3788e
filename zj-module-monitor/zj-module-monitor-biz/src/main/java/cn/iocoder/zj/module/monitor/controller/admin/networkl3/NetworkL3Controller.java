package cn.iocoder.zj.module.monitor.controller.admin.networkl3;

import cn.iocoder.zj.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.monitor.controller.admin.networkl3.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkl3.NetworkL3DO;
import cn.iocoder.zj.module.monitor.convert.networkl3.NetworkL3Convert;
import cn.iocoder.zj.module.monitor.service.networkl3.NetworkL3Service;

@Tag(name = "管理后台 - 三级网络资源")
@RestController
@RequestMapping("/monitor/network-l3")
@Validated
public class NetworkL3Controller {

    @Resource
    private NetworkL3Service networkL3Service;

    @PostMapping("/create")
    @Operation(summary = "创建三级网络资源")
    @PreAuthenticated
    @PreAuthorize("@ss.hasPermission('monitor:network-l3:create')")
    @OperateLog(type = CREATE)
    public CommonResult<Long> createNetworkL3(@Valid @RequestBody NetworkL3CreateReqVO createReqVO) {
        return success(networkL3Service.createNetworkL3(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新三级网络资源")
    @PreAuthenticated
    @PreAuthorize("@ss.hasPermission('monitor:network-l3:update')")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateNetworkL3(@Valid @RequestBody NetworkL3UpdateReqVO updateReqVO) {
        networkL3Service.updateNetworkL3(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除三级网络资源")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthenticated
    @PreAuthorize("@ss.hasPermission('monitor:network-l3:delete')")
    @OperateLog(type = DELETE)
    public CommonResult<Boolean> deleteNetworkL3(@RequestParam("id") Long id) {
        networkL3Service.deleteNetworkL3(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得三级网络资源")
    @Parameter(name = "id", description = "编号", required = false, example = "1024")
    @Parameter(name = "uuid", description = "UUID", required = false, example = "1024")
    @PreAuthenticated
    public CommonResult<NetworkL3RespVO> getNetworkL3(Long id, String uuid) {
        NetworkL3DO networkL3 = networkL3Service.getNetworkL3(id,uuid);
        return success(NetworkL3Convert.INSTANCE.convert(networkL3));
    }

    @GetMapping("/list")
    @Operation(summary = "获得三级网络资源列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthenticated
    public CommonResult<List<NetworkL3RespVO>> getNetworkL3List(@RequestParam("ids") Collection<Long> ids) {
        List<NetworkL3DO> list = networkL3Service.getNetworkL3List(ids);
        return success(NetworkL3Convert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得三级网络资源分页")
    @TenantIgnore
    public CommonResult<PageResult<NetworkL3RespVO>> getNetworkL3Page(@Valid NetworkL3PageReqVO pageVO) {
        PageResult<NetworkL3DO> pageResult = networkL3Service.getNetworkL3Page(pageVO);
        return success(NetworkL3Convert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/vpcPage")
    @Operation(summary = "获得VPC网络资源分页")
    @PreAuthenticated
    @TenantIgnore
    public CommonResult<PageResult<NetworkL3RespVO>> getNetworkL3VpcPage(@Valid NetworkL3PageReqVO pageVO) {
        PageResult<NetworkL3DO> pageResult = networkL3Service.getNetworkL3VpcPage(pageVO);
        return success(NetworkL3Convert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出三级网络资源 Excel")
    @PreAuthenticated
    @OperateLog(type = EXPORT)
    public void exportNetworkL3Excel(@Valid NetworkL3ExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<NetworkL3DO> list = networkL3Service.getNetworkL3List(exportReqVO);
        // 导出 Excel
        List<NetworkL3ExcelVO> datas = NetworkL3Convert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "三级网络资源.xls", "数据", NetworkL3ExcelVO.class, datas);
    }

}
