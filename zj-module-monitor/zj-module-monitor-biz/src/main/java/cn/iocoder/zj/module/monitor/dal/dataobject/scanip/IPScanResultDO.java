package cn.iocoder.zj.module.monitor.dal.dataobject.scanip;

import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * IP扫描结果 DO
 */
@TableName("ip_scan_result")
@KeySequence("ip_scan_result") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class IPScanResultDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;

    /**
     * IP段编号
     */
    private Long ipRangeId;

    /**
     * IP段名称
     */
    private String ipRangeName;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * Ping支持
     *
     * 0 - 不支持
     * 1 - 支持
     */
    private Integer pingSupport;

    /**
     * SNMP支持
     *
     * 0 - 不支持
     * 1 - 支持
     */
    private Integer snmpSupport;

    /**
     * TCP支持
     *
     * 0 - 不支持
     * 1 - 支持
     */
    private Integer tcpSupport;

    /**
     * 扫描时间
     */
    private LocalDateTime scanTime;

    private Integer snmpPort;

    /**
     * SNMP团体名
     */
    private String snmpCommunity;

    private String snmpVersion;

    private Integer tcpPort;

    /**
     * 租户编号
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    private Integer pingStatus;
    
    private Integer snmpStatus;

    private Integer tcpStatus;

    private Long platformId;

    private String platformName;

    private String remark;
}