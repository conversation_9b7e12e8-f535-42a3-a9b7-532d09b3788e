package cn.iocoder.zj.module.monitor.controller.admin.hostnic.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 云主机网络 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class HostNicExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("uuid")
    private String uuid;

    @ExcelProperty("云主机uuid")
    private String hostUuid;

    @ExcelProperty("ipV6")
    private String ip6;

    @ExcelProperty("ip")
    private String ip;

    @ExcelProperty("mac")
    private String mac;

    @ExcelProperty("驱动")
    private String driver;

    @ExcelProperty("在经典网络")
    private Byte inClassicNetwork;

    @ExcelProperty("网络uuid")
    private String networkUuid;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("平台id")
    private Long platformId;

    @ExcelProperty("平台名称")
    private String platformName;

}
