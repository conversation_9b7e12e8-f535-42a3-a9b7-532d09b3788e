package cn.iocoder.zj.module.monitor.service.alarmconfigtemplate;

import java.util.*;
import javax.validation.*;
import cn.iocoder.zj.module.monitor.controller.admin.alarmconfigtemplate.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmconfigtemplate.AlarmConfigTemplateDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 告警配置模板 Service 接口
 *
 * <AUTHOR>
 */
public interface AlarmConfigTemplateService {

    /**
     * 创建告警配置模板
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAlarmConfigTemplate(@Valid AlarmConfigTemplateCreateReqVO createReqVO);

    /**
     * 更新告警配置模板
     *
     * @param updateReqVO 更新信息
     */
    void updateAlarmConfigTemplate(@Valid AlarmConfigTemplateUpdateReqVO updateReqVO);

    /**
     * 删除告警配置模板
     *
     * @param id 编号
     */
    void deleteAlarmConfigTemplate(Long id);

    /**
     * 获得告警配置模板
     *
     * @param id 编号
     * @return 告警配置模板
     */
    AlarmConfigTemplateDO getAlarmConfigTemplate(Long id);

    /**
     * 获得告警配置模板列表
     *
     * @param ids 编号
     * @return 告警配置模板列表
     */
    List<AlarmConfigTemplateDO> getAlarmConfigTemplateList(Collection<Long> ids);

    /**
     * 获得告警配置模板分页
     *
     * @param pageReqVO 分页查询
     * @return 告警配置模板分页
     */
    PageResult<AlarmConfigTemplateDO> getAlarmConfigTemplatePage(AlarmConfigTemplatePageReqVO pageReqVO);

    /**
     * 获得告警配置模板列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 告警配置模板列表
     */
    List<AlarmConfigTemplateDO> getAlarmConfigTemplateList(AlarmConfigTemplateExportReqVO exportReqVO);

}
