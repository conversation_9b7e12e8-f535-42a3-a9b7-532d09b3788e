package cn.iocoder.zj.module.monitor.controller.admin.hardwarestorage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 宿主机与存储关联 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class HardwareStorageExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("宿主机id")
    private Long hardwareId;

    @ExcelProperty("宿主机uuid")
    private String hardwareUuid;

    @ExcelProperty("主存储id")
    private Long storageId;

    @ExcelProperty("主存储uuid")
    private String storageUuid;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("平台id")
    private Long platformId;

    @ExcelProperty("平台名称")
    private String platformName;

}
