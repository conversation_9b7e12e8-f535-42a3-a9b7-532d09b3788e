package cn.iocoder.zj.module.monitor.dal.dataobject.alarmhostrelation;

import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 告警配置与云主机关联关系 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_alarm_host_relation")
@KeySequence("monitor_alarm_host_relation_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmHostRelationDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 告警配合名称
     */
    private String alarmName;
    /**
     * 告警配置ID
     */
    private Long alarmId;
    /**
     * 平台配置ID
     */
    private Long platformConfigId;
    /**
     * 平台配置类型
     */
    private String platformConfigType;
    /**
     * 主机名称
     */
    private String hostName;
    /**
     * 云主机uuid
     */
    private String hostUuid;
    /**
     * 租户名称
     */
    private String tenantName;
    /**
     * 启用状态
     */
    private Integer status;

}
