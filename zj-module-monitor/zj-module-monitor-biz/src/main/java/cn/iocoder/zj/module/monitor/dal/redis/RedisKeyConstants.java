package cn.iocoder.zj.module.monitor.dal.redis;

import cn.iocoder.zj.module.monitor.dal.dataobject.alarmconfig.AlarmConfigDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmhostrelation.AlarmHostRelationDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.zstack.ZstackLoginInfo;


import java.util.Map;



/**
 * System Redis Key 枚举类
 *
 * <AUTHOR>
 */
public interface RedisKeyConstants {

    String ZSTACK_ACCESS_TOKEN = "zstack_access_token:%s";

    String ALARM_CONFIG = "alarm_config:%s";

    String ALARM_HOST_RELATION = "alarm_host_relation:%s";


    String Label_VM = "label_vm:%s";


    String Label_HOST = "label_host:%s";
    String ASSET_ID = "asset_id:%s";

    String SCAN_IP = "SCAN_IP:%s";
}
