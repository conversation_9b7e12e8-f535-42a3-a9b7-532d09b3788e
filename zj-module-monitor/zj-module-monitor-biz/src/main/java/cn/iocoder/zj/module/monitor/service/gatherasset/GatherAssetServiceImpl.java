package cn.iocoder.zj.module.monitor.service.gatherasset;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.monitor.controller.admin.gatherasset.vo.*;
import cn.iocoder.zj.module.monitor.convert.gatherasset.GatherAssetConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.gatherasset.GatherAssetDO;
import cn.iocoder.zj.module.monitor.dal.mysql.gatherasset.GatherAssetMapper;
import cn.iocoder.zj.module.monitor.service.topology.TopologyService;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.singleton;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.GATHER_ASSET_NOT_EXISTS;

/**
 * 租户资产 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class GatherAssetServiceImpl implements GatherAssetService {

    @Resource
    private GatherAssetMapper gatherAssetMapper;
    @Resource
    PermissionApi permissionApi;
    @Resource
    RoleApi roleApi;
    @Resource
    PlatformconfigApi platformconfigApi;

    @Resource
    TopologyService topologyService;


    @Override
    public Long createGatherAsset(GatherAssetCreateReqVO createReqVO) {
        Long count = gatherAssetMapper.selectByAssetIp(createReqVO.getIp(), createReqVO.getPlatformId());
        if (count > 0) {
            return -1L;
        }
        // 插入
        GatherAssetDO gatherAsset = GatherAssetConvert.INSTANCE.convert(createReqVO);
        gatherAssetMapper.insert(gatherAsset);
        // 返回
        return gatherAsset.getId();
    }

    @Override
    public void updateGatherAsset(GatherAssetUpdateReqVO updateReqVO) {
        // 校验存在
        validateGatherAssetExists(updateReqVO.getId());
        // 更新
        GatherAssetDO updateObj = GatherAssetConvert.INSTANCE.convert(updateReqVO);
        gatherAssetMapper.updateById(updateObj);
    }

    @Override
    public void deleteGatherAsset(Long id) {
        // 校验存在
        validateGatherAssetExists(id);
        // 删除
        gatherAssetMapper.deleteById(id);
        // 同步删除拓扑图中的信息
        topologyService.deleteAssetAndHostJson(id.toString(),"asset");
    }

    private void validateGatherAssetExists(Long id) {
        if (gatherAssetMapper.selectById(id) == null) {
            throw exception(GATHER_ASSET_NOT_EXISTS);
        }
    }

    @Override
    public GatherAssetDO getGatherAsset(Long id) {
        return gatherAssetMapper.selectById(id);
    }

    @Override
    public List<GatherAssetDO> getGatherAssetList(Collection<Long> ids) {
        return gatherAssetMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<GatherAssetDO> getGatherAssetPage(GatherAssetPageReqVO pageReqVO) {
        boolean admin = false;
        List<Map> platform = new ArrayList<>();
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        if (roleApi.hasAnySuperAdmin(roleIds)) {
            admin = true;
        } else {
            platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
        }

        return gatherAssetMapper.selectPage(pageReqVO, admin, platform);
    }

    @Override
    public List<GatherAssetDO> getGatherAssetList(GatherAssetExportReqVO exportReqVO) {
        return gatherAssetMapper.selectList(exportReqVO);
    }

    @Override
    public Map getAssetListByUuid(String uuid, String token_desc) {
        Map map = new HashMap();
        if (!SecureUtil.md5("<EMAIL>").equals(token_desc)) {
            map.put("code", 500);
            map.put("msg", "token验证失败");
            return map;
        }
        // 查询数据库是否已存在如果已存在返回存在codel;
        if (StrUtil.isNotEmpty(uuid)) {
            JSONObject obj = JSON.parseObject(uuid);
            String assetUuid = obj.getString("uuid");
            List<Map> maps = gatherAssetMapper.selectByUuId(assetUuid);
            if (maps.size() > 0) {
                map.put("code", 200);
                map.put("data", maps);
                map.put("msg", "请求成功");
            } else {
                map.put("code", 200);
                map.put("data", new ArrayList<>());
                map.put("msg", "请求成功");
            }

        } else {
            map.put("code", 500);
            map.put("data", new ArrayList<>());
            map.put("msg", "uuid不能为空！");
        }
        return map;
    }

    @Override
    public Map updateOnlieType(String json, String tokenDesc) {
        Map map = new HashMap();
        if (!SecureUtil.md5("<EMAIL>").equals(tokenDesc)) {
            map.put("code", 500);
            map.put("msg", "token验证失败");
            return map;
        }
        List<GatherAssetDO> list = new ArrayList<>();
        // 解析json中设备存活状态数据
        if (StrUtil.isNotEmpty(json)) {
            JSONArray jsonArray = JSONObject.parseObject(json).getJSONArray("json");
            for (int i = 0; i < jsonArray.size(); i++) {
                GatherAssetDO gatherAssetDO = new GatherAssetDO();
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String uuid = jsonObject.getString("uuid");
                String ip = jsonObject.getString("ip");
                Integer onlineType = jsonObject.getInteger("onlineType");
                gatherAssetDO.setUuid(uuid);
                gatherAssetDO.setIp(ip);
                gatherAssetDO.setOnlineType(onlineType);
                list.add(gatherAssetDO);
            }
            int count = gatherAssetMapper.updateOnlieType(list);
            map.put("code", 200);
            map.put("data", count);
            map.put("msg", "请求成功");
        } else {
            map.put("code", 500);
            map.put("data", -1);
            map.put("msg", "uuid不能为空！");
        }
        return map;

    }

    @Override
    public Map addAssetList(String json, String tokenDesc) {
        Map map = new HashMap();
        if (!SecureUtil.md5("<EMAIL>").equals(tokenDesc)) {
            map.put("code", 500);
            map.put("msg", "token验证失败");
            return map;
        }
        List<GatherAssetDO> list = new ArrayList<>();
        // 解析json中设备存活状态数据
        if (StrUtil.isNotEmpty(json)) {
            JSONArray jsonArray = JSONObject.parseObject(json).getJSONArray("json");
            for (int i = 0; i < jsonArray.size(); i++) {
                GatherAssetDO gatherAssetDO = new GatherAssetDO();
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String uuid = jsonObject.getString("uuid");
                String ip = jsonObject.getString("ip");
                Double cpuUsage = jsonObject.getDouble("cpuUsage");
                Double memoryUsage = jsonObject.getDouble("memoryUsage");
                Double diskUsage = jsonObject.getDouble("diskUsage");
                gatherAssetDO.setUuid(uuid);
                gatherAssetDO.setCpuUsage(cpuUsage);
                gatherAssetDO.setMemoryUsage(memoryUsage);
                gatherAssetDO.setDiskUsage(diskUsage);
                gatherAssetDO.setIp(ip);
                list.add(gatherAssetDO);
            }
            int s = gatherAssetMapper.updateBatchByList(list);
            map.put("code", 200);
            map.put("data", "");
            map.put("msg", "请求成功");
        } else {
            map.put("code", 500);
            map.put("data", -1);
            map.put("msg", "uuid不能为空！");
        }
        return map;
    }

    @Override
    public List<GatherAssetDO> getAssetList() {
        return gatherAssetMapper.selectList();
    }

    @Override
    public void createAssetInfoList(List<GatherAssetDO> list) {
        gatherAssetMapper.insertBatch(list);
    }

    @Override
    public List<GatherAssetDataRespVO> getAssetData(GatherAssetDataReqVO reqVo) {
        Long period = reqVo.getEndTime() - reqVo.getStartTime();
        List<GatherAssetDataRespVO> gatherAssetDataRespVOS = new ArrayList<>();
//        String query = "SELECT cpuValue,diskValue,memoryValue,productsName,uuid,platformId,platformName from (SELECT MEAN(cpu_value) as cpuValue,MEAN(disk_value) as diskValue,MEAN(memory_value) as memoryValue FROM zj_asset " +
//                "WHERE time >= '" + DateUtil.format(new Date(reqVo.getStartTime()), "yyyy-MM-dd HH:mm:ss") + "'" +
//                " AND time <= '" + DateUtil.format(new Date(reqVo.getEndTime()), "yyyy-MM-dd HH:mm:ss") + "'  " +
//                "and uuid = '" + reqVo.getUuid() + "' " +
//                "and productsName = '" + reqVo.getIp() + "' " +
//                "group by uuid, productsName,platformId,platformName, time(10m) TZ('Asia/Shanghai'))";
//
//        for (Object list : influxDBTemplate.fetchRecords(query)) {
//            gatherAssetDataRespVOS.add(new ObjectMapper().convertValue(list, GatherAssetDataRespVO.class));
//        }
        return gatherAssetDataRespVOS;
    }

    @Override
    public Map<String, Object> getGatherAssetStatusCount(List<String> tenantIds, Long platformId) {
        Map<String, Object> gatherAssetStatusCount = gatherAssetMapper.getGatherAssetStatusCount(tenantIds, platformId);
        if (gatherAssetStatusCount.size() == 1) {
            gatherAssetStatusCount = new HashMap<>();
            gatherAssetStatusCount.put("otherStateNum", 0);
            gatherAssetStatusCount.put("assetRunningNum", 0);
            gatherAssetStatusCount.put("assetStoppedNum", 0);
            gatherAssetStatusCount.put("assteNum", 0);
        }
        return gatherAssetStatusCount;
    }

    @Override
    public Long getGatherAssetCount(String deviceType) {
        return gatherAssetMapper.getGatherAssetCount(deviceType);
    }

    @Override
    public Long getGatherAssetCountBySysType(String sysType) {
        return gatherAssetMapper.getGatherAssetCountBySysType(sysType);
    }

}
