package cn.iocoder.zj.module.monitor.controller.admin.eip.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 弹性公网 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class EipExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("名称")
    private String name;

    @ExcelProperty("uuid")
    private String uuid;

    @ExcelProperty("ip地址")
    private String ipAddr;

    @ExcelProperty("带宽")
    private Integer bandwidth;

    @ExcelProperty("网络uuid")
    private String networkId;

    @ExcelProperty("资源类型")
    private String associateType;

    @ExcelProperty("资源id")
    private String associateId;

    @ExcelProperty("计费类型")
    private String chargeType;

    @ExcelProperty("区域id")
    private String cloudregionId;

    @ExcelProperty("public_ip 公网IP  elastic_ip弹性公网IP")
    private String mode;

    @ExcelProperty("状态")
    private String status;

    @ExcelProperty("平台id")
    private Long platformId;

    @ExcelProperty("平台名称")
    private String platformName;

}
