package cn.iocoder.zj.module.monitor.controller.admin.bulletin;

import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.*;
import cn.iocoder.zj.module.monitor.controller.admin.hardwareinfo.vo.HardwareInfoRespVO;
import cn.iocoder.zj.module.monitor.dal.mysql.bulletin.BulletinMapper;
import cn.iocoder.zj.module.monitor.service.bulletin.dto.BulletinFieldItemDto;
import cn.iocoder.zj.module.monitor.service.bulletin.enums.BulletinCloudMonitorItemEnum;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.net.URLEncoder;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.monitor.controller.admin.bulletin.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.bulletin.BulletinDO;
import cn.iocoder.zj.module.monitor.convert.bulletin.BulletinConvert;
import cn.iocoder.zj.module.monitor.service.bulletin.BulletinService;

@Tag(name = "管理后台 - 实时报")
@RestController
@RequestMapping("/monitor/bulletin")
@Validated
public class BulletinController {

    @Resource
    private BulletinService bulletinService;
    @Resource
    private BulletinMapper  bulletinMapper;
    @Resource
    private PlatformconfigApi platformconfigApi;

    @PostMapping("/create")
    @Operation(summary = "创建实时报")
//    @PreAuthorize("@ss.hasPermission('monitor:bulletin:create')")
    public CommonResult<Long> createBulletin(@Valid @RequestBody BulletinCreateReqVO createReqVO) {
        return success(bulletinService.createBulletin(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新实时报")
//    @PreAuthorize("@ss.hasPermission('monitor:bulletin:update')")
    public CommonResult<Boolean> updateBulletin(@Valid @RequestBody BulletinUpdateReqVO updateReqVO) {
        bulletinService.updateBulletin(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除实时报")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('monitor:bulletin:delete')")
    public CommonResult<Boolean> deleteBulletin(@RequestParam("id") Long id) {
        bulletinService.deleteBulletin(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得实时报")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('monitor:bulletin:query')")
    public CommonResult<BulletinRespVO> getBulletin(@RequestParam("id") Long id) {
        BulletinDO bulletin = bulletinService.getBulletin(id);
        BulletinRespVO res = BulletinConvert.INSTANCE.convert(bulletin);
        return success(res);
    }
    @GetMapping("/getDetail")
    @Operation(summary = "获得实时报表详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('monitor:bulletin:query')")
    public CommonResult<PageResult<Object>> getBulletinDetail(
            @RequestParam("id") Long id,
            @RequestParam(value = "pageNo", required = false, defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", required = false,defaultValue = "10") Integer pageSize
            ) {
        BulletinDO bulletin = bulletinService.getBulletin(id);
        /*List<Object> rList = bulletinService.getDetailList(bulletin);
        //分页截取
        int total = rList.size();
        int fromIndex = Math.min((pageNo - 1) * pageSize, total);
        int toIndex = Math.min(fromIndex + pageSize, total);
        List<Object> pagedList = rList.subList(fromIndex, toIndex);

        PageResult<Object> res = new PageResult<>();
        res.setList(pagedList);
        res.setTotal((long) total);*/

        PageResult<Object> res = bulletinService.getDetailPage(bulletin, pageSize, pageNo);

        return success(res);
    }

    @GetMapping("/getAppMonitorField")
    @Operation(summary = "获得实时报表监控项")
    @Parameter(name = "app", description = "app", required = true, example = "network")
//    @PreAuthorize("@ss.hasPermission('monitor:bulletin:query')")
    public CommonResult<List<BulletinFieldItemDto>> getAppMonitorField(@RequestParam("app") String app) {
        List<BulletinFieldItemDto> res = bulletinService.getAppBulletinFieldList(app);
        return success(res);
    }

    @GetMapping("/list")
    @Operation(summary = "获得实时报列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
//    @PreAuthorize("@ss.hasPermission('monitor:bulletin:query')")
    public CommonResult<List<BulletinRespVO>> getBulletinList(@RequestParam("ids") Collection<Long> ids) {
        List<BulletinDO> list = bulletinService.getBulletinList(ids);
        return success(BulletinConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得实时报分页")
//    @PreAuthorize("@ss.hasPermission('monitor:bulletin:query')")
    public CommonResult<PageResult<BulletinRespVO>> getBulletinPage(@Valid BulletinPageReqVO pageVO) {
        PageResult<BulletinDO> pageResult = bulletinService.getBulletinPage(pageVO);
        PageResult<BulletinRespVO> res = BulletinConvert.INSTANCE.convertPage(pageResult);
        return success(res);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出实时报 Excel")
//    @PreAuthorize("@ss.hasPermission('monitor:bulletin:export')")
    @OperateLog(type = EXPORT)
    public void exportBulletinExcel(@Valid BulletinExportReqVO exportReqVO,
                                    HttpServletResponse response) throws IOException {
        List<BulletinDO> list = bulletinService.getBulletinList(exportReqVO);
        // 导出 Excel
        List<BulletinExcelVO> datas = BulletinConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "实时报.xls", "数据", BulletinExcelVO.class, datas);
    }

    @GetMapping("/export-detail-excel")
    @Operation(summary = "导出实时报表详情 Excel")
//    @PreAuthorize("@ss.hasPermission('monitor:bulletin:export')")
    @OperateLog(type = EXPORT)
    public void exportBulletinDetailExcel(@RequestParam("id") Long id,
              HttpServletResponse response) throws IOException {
        BulletinDO bulletin = bulletinService.getBulletin(id);
        bulletinService.exportDetailExcel(bulletin, response);
    }

    @GetMapping("/app-asset-list")
    @Operation(summary = "app资产列表")
//    @PreAuthorize("@ss.hasPermission('monitor:taggables:query')")
    public CommonResult<Object> appAssetList(
            @RequestParam("app") String app,
            @RequestParam(value = "name", required = false,defaultValue = "") String name
    ) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        List<Map> platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
        StringBuilder inplatform = new StringBuilder("0");
        if (!platform.isEmpty()){
            for (Map map : platform){
                inplatform.append(",").append(map.get("platformId").toString());
            }
        }
        String table = "hzb_monitor";
        String field = "id,`name`,id as uuid,platform_id,platform_name,DATE_FORMAT(gmt_create, '%Y-%m-%d %H:%i:%s') AS create_time";
        for (BulletinCloudMonitorItemEnum value : BulletinCloudMonitorItemEnum.values()) {
            if (value.getApp().equals(app)) {
                table = value.getTable();
                field = "id,`name`,platform_id,platform_name,uuid,DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') AS create_time";
            }
        }
        List<Map<String, Object>> list = bulletinMapper.selectAssetListByApp(table,inplatform.toString(), field,app,name);
        return CommonResult.success(list);
    }
}
