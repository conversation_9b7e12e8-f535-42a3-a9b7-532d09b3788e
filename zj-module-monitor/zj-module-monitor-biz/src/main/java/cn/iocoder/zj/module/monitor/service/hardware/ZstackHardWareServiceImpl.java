package cn.iocoder.zj.module.monitor.service.hardware;

import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.hardwareinfo.HardwareInfoDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostinfo.HostInfoDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.storageinfo.StorageInfoDO;
import cn.iocoder.zj.module.monitor.dal.mysql.hardwareinfo.HardwareInfoMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.hostinfo.HostInfoMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.storageinfo.StorageInfoMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @ClassName : ZstackHardWare  //类名
 * @Description : 硬件设施实现类  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/26  9:13
 */
@Component
public class ZstackHardWareServiceImpl   implements IZstackHardWareService {

    @Resource
    private HardwareInfoMapper hardwareInfoMapper;
    @Resource
    private HostInfoMapper hostInfoMapper;
    @Resource
    private StorageInfoMapper storageInfoMapper;
    @Override
    public String cpuUsage() {
        return null;
    }

    @Override
    public void memoryUsage() {

    }

    @Override
    public void netWorkUpdown() {

    }

    @Override
    public void netWorkSendOrHarvest() {

    }

    @Override
    public void createHostInfoList(List<HardwareInfoDO> list) {
        hardwareInfoMapper.insertBatch(list);
    }

    @Override
    public int getCount(String typeName) {
        return hardwareInfoMapper.getCount(typeName);
    }

    @Override
    public void updateHardwareInfoList(List<HardwareInfoDO> list) {
        hardwareInfoMapper.updateHardWareList(list);
    }

    @Override
    public List<HardwareInfoDO> getAll(String typeName) {
        return hardwareInfoMapper.selectList(HardwareInfoDO::getTypeName,typeName);
    }

    @Override
    public String getIp(String uuid, String sourceType) {
        if (sourceType !=null && sourceType !=""){
            if (sourceType.equals("monitor_alarm_hardware")){
                LambdaQueryWrapper<HardwareInfoDO> lqw = new LambdaQueryWrapper<>();
                lqw.eq(uuid!=null,HardwareInfoDO::getUuid,uuid)
                        .eq(BaseDO::getDeleted,0);
                HardwareInfoDO hardwareInfoDO = hardwareInfoMapper.selectOne(lqw);
                return hardwareInfoDO.getIp();
            }else if (sourceType.equals("monitor_alarm_host")){
                LambdaQueryWrapper<HostInfoDO> lqw = new LambdaQueryWrapper<>();
                lqw.eq(uuid!=null,HostInfoDO::getUuid,uuid)
                        .eq(BaseDO::getDeleted,0);
                HostInfoDO hostInfoDO = hostInfoMapper.selectOne(lqw);
                return hostInfoDO.getIp();
            }else {
                return "";
            }
        }else {
            return "";
        }
    }

    @Override
    public int deleteHardWare(List<HardwareInfoDO> list) {
        return hardwareInfoMapper.deleteHardWare(list);
    }

    @Override
    public List<HardwareInfoDO> getListAll() {
        return hardwareInfoMapper.selectList();
    }

    @Override
    public void removeDuplicateData() {
        List<Map<String,String>> duplicateDatas = hardwareInfoMapper.getDuplicateDataIds();
        if(duplicateDatas.size()>0) {
            hardwareInfoMapper.removeDuplicateData(duplicateDatas);
        }
    }

    @Override
    public Long getCpuSockets(List<String> platformIds) {

        return  hardwareInfoMapper.getCpuSockets(platformIds);
    }

    private List<HardwareInfoDO>  VMUuidList(String uuid) {
        return hardwareInfoMapper.selectList();
    }

}
