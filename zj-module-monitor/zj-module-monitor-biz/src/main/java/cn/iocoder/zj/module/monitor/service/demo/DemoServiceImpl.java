package cn.iocoder.zj.module.monitor.service.demo;

import cn.iocoder.zj.module.monitor.util.Sha256;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service("DemoService")
@Slf4j
public class DemoServiceImpl implements DemoService {



    @Override
    public Long getUser() {

        JSONObject parameters = new JSONObject();
        JSONObject a = new JSONObject();
        a.put("accountName", "admin");
        //密码sha512加密（密码）
        a.put("password", Sha256.SHA512("password"));
        parameters.put("logInByAccount", a);
        return 1L;
    }
}
