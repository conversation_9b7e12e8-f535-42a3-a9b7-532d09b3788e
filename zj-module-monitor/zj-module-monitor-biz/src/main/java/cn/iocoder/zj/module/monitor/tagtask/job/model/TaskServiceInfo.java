package cn.iocoder.zj.module.monitor.tagtask.job.model;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.zj.module.monitor.dal.dataobject.tagplan.TagPlanDO;
import cn.iocoder.zj.module.monitor.service.tagplan.TagPlanService;
import cn.iocoder.zj.module.monitor.tagtask.job.task.TagTaskService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.stream.Collectors;

import static cn.iocoder.zj.module.monitor.util.CronExpressionUtil.getNextExecutionTime;


@Slf4j
@Component
public class TaskServiceInfo {
    
    @Autowired
    @Lazy
    private TagPlanService tagPlanService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private TagTaskService tagTaskService;

    private final ThreadPoolTaskScheduler threadPoolTaskScheduler;

    private final Map<Long, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>(32);

    public TaskServiceInfo(ThreadPoolTaskScheduler threadPoolTaskScheduler) {
        this.threadPoolTaskScheduler = threadPoolTaskScheduler;
    }

    public void updateTask(TagTaskModel updatedTask) {
        removeTask(updatedTask.getId());
        scheduleTask(updatedTask);
    }

    public void removeTask(Long taskId) {
        Optional.ofNullable(scheduledTasks.remove(taskId))
                .ifPresent(scheduledFuture -> scheduledFuture.cancel(false));
    }

    @EventListener(ContextRefreshedEvent.class)
    public void startPolling() {
        String lockKey = "tag_task";
        RLock lock = redissonClient.getLock(lockKey);
        if (!lock.tryLock()) {
            log.info("tag-其他实例已获取锁，本实例将不执行任务初始化");
            return;
        }
        try {
            log.info("tag-任务初始化");
            List<TagTaskModel> tasks = Optional.ofNullable(tagTaskService.listTasks())
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(task -> task.getStatus() == 1)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(tasks)) {
                tasks.forEach(this::scheduleTask);
            }
        } catch (Exception e) {
            lock.unlock();
            log.error("Error scheduling task: {}", lockKey, e);
        }
    }

    public void scheduleTask(TagTaskModel task) {
            scheduledTasks.computeIfAbsent(task.getId(), id -> {
                Runnable runnableTask = () -> {
                    try {
                        toDoTask(id);
                    } catch (Exception e) {
                        log.error("Error executing task: {}", id, e);
                    }
                };
                ScheduledFuture<?> scheduledFuture =
                        threadPoolTaskScheduler.schedule(runnableTask, new CronTrigger(task.getCronExpression()));
                log.info("Scheduled task: {}, expression: {}", task.getJobName(), task.getCronExpression());
                return scheduledFuture;
            });
    }


    /**
     * 需要执行的任务
     *
     * @param jobId
     */
    private void toDoTask(Long jobId) {
        log.info("toDoTask: {}", jobId);
        Date now = new Date();
        // 根据jobId 查询计划任务
        TagPlanDO patrolPlanDO = tagPlanService.getTagPlanByJobId(jobId);
        if (patrolPlanDO != null && patrolPlanDO.getStatus() == 0) {
            TenantContextHolder.setTenantId(patrolPlanDO.getTenantId());
            String cronExpression = patrolPlanDO.getExecutionCron();
            Date nextExecutionTime = getNextExecutionTime(cronExpression, now);
            //修改计划任务表中最近执行时间和下次执行时间
            patrolPlanDO.setLastExecutionTime(now);
            patrolPlanDO.setNextExecutionTime(nextExecutionTime);
            tagPlanService.updateLastTimeAndNextTime(patrolPlanDO);
            log.info("修改任务最近执行时间: {}", patrolPlanDO);
            if(StrUtil.isEmpty(patrolPlanDO.getPlatformIds())){
                String mapList = tagPlanService.getPlatformSelectList(patrolPlanDO.getSysSettingTenant());
                patrolPlanDO.setPlatformIds(mapList);
            }
            tagPlanService.runTagTask(patrolPlanDO);
        }
    }

}
