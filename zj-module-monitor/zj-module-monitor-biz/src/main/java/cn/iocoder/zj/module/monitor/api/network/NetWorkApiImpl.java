package cn.iocoder.zj.module.monitor.api.network;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL2DTO;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL3DTO;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkVpcDTO;
import cn.iocoder.zj.module.monitor.convert.networkl2.NetworkL2Convert;
import cn.iocoder.zj.module.monitor.convert.networkl3.NetworkL3Convert;
import cn.iocoder.zj.module.monitor.convert.networkvpc.NetworkVpcConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkl2.NetworkL2DO;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkl3.NetworkL3DO;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkvpc.NetworkVpcDO;
import cn.iocoder.zj.module.monitor.service.networkl2.NetworkL2Service;
import cn.iocoder.zj.module.monitor.service.networkl3.NetworkL3Service;
import cn.iocoder.zj.module.monitor.service.networkvpc.NetworkVpcService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

/**
 * @ClassName : NetWorkApiImpl  //类名
 * @Description : 网络实现类  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/8/7  11:00
 */

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class NetWorkApiImpl implements NetworkApi {

    @Resource
    NetworkL2Service networkL2Service;

    @Resource
    NetworkL3Service networkL3Service;
    @Resource
    NetworkVpcService networkVpcService;

    @Override
    public CommonResult<Boolean> addNetWorkL2(List<NetWorkL2DTO> reqDTO) {
        List<NetworkL2DO> list = NetworkL2Convert.INSTANCE.convertCreateList(reqDTO);
        networkL2Service.createNetworkL2List(list);
        return CommonResult.success(true);
    }

    @Override
    public Long getNetWorkL2Count(String typeName) {
        return networkL2Service.getNetWorkL2Count(typeName);
    }

    @Override
    public CommonResult<Boolean> updateNetWorkL2(List<NetWorkL2DTO> netWorkL2DTOS) {
        List<NetworkL2DO> list = NetworkL2Convert.INSTANCE.convertCreateList(netWorkL2DTOS);
        networkL2Service.updateNetWorkL2List(netWorkL2DTOS);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<List<NetWorkL2DTO>> getNetWorkL2List(String typeName) {
        List<NetWorkL2DTO> list = NetworkL2Convert.INSTANCE.convertListDoToDto(networkL2Service.getNetWorkL2List(typeName));
        return CommonResult.success(list);
    }

    @Override
    public List<Map> getNetWorkL2ByNameList() {
        return networkL2Service.getNetWorkL2ByNameList();
    }

    @Override
    public int deleteNetWorkL2ByNameList(List<NetWorkL2DTO> netWorkL2DTOS) {
        List<NetworkL2DO> list = NetworkL2Convert.INSTANCE.convertCreateList(netWorkL2DTOS);
        return networkL2Service.deleteNetWorkL2ByNameList(list);
    }

    //================================三级网络==============================
    @Override
    public CommonResult<Boolean> addNetWorkL3(List<NetWorkL3DTO> reqDTO) {
        List<NetworkL3DO> list = NetworkL3Convert.INSTANCE.convertCreateList(reqDTO);
        networkL3Service.createNetworkL3List(list);
        return CommonResult.success(true);
    }

    @Override
    public Long getNetWorkL3Count(String typeName) {
        return networkL3Service.getNetWorkL3Count(typeName);
    }

    @Override
    public CommonResult<Boolean> updateNetWorkL3(List<NetWorkL3DTO> netWorkL3DTOS) {
        List<NetworkL3DO> list = NetworkL3Convert.INSTANCE.convertCreateList(netWorkL3DTOS);
        networkL3Service.updateNetWorkL3List(netWorkL3DTOS);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<List<NetWorkL3DTO>> getNetWorkL3List(String typeName) {
        List<NetWorkL3DTO> list = NetworkL3Convert.INSTANCE.convertListDoToDto(networkL3Service.getNetWorkL3List(typeName));
        return CommonResult.success(list);
    }

    @Override
    public int deleteNetWorkL3ByNameList(List<NetWorkL3DTO> netWorkL3DTOS) {
        List<NetworkL3DO> list = NetworkL3Convert.INSTANCE.convertCreateList(netWorkL3DTOS);
        return networkL2Service.deleteNetWorkL3ByNameList(list);
    }

    //================================三级网络==============================
    @Override
    public CommonResult<Boolean> addNetWorkVPC(List<NetWorkVpcDTO> reqDTO) {
        List<NetworkVpcDO> list = NetworkVpcConvert.INSTANCE.convertCreateList(reqDTO);
        networkVpcService.createNetworkL3List(list);
        return CommonResult.success(true);
    }

    @Override
    public Long getNetWorkVpcCount() {
        return networkVpcService.getNetWorkL3Count();
    }

    @Override
    public CommonResult<Boolean> updateNetWorkVpc(List<NetWorkVpcDTO> netWorkVpcDTOS) {
        List<NetworkVpcDO> list = NetworkVpcConvert.INSTANCE.convertCreateList(netWorkVpcDTOS);
        networkVpcService.updateNetWorkVpcList(netWorkVpcDTOS);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<List<NetWorkVpcDTO>> getNetWorkVpcList() {
        List<NetWorkVpcDTO> list = NetworkVpcConvert.INSTANCE.convertListDoToDto(networkVpcService.getNetWorkVpcList());
        return CommonResult.success(list);
    }

    @Override
    public int deleteNetWorkVPCByNameList(List<NetWorkVpcDTO> netWorkVpcDTOS) {
        List<NetworkVpcDO> list = NetworkVpcConvert.INSTANCE.convertCreateList(netWorkVpcDTOS);
        return networkVpcService.deleteNetWorkVPCByNameList(list);
    }

    @Override
    public CommonResult<List<NetWorkL2DTO>> getNetworkL2ByPlatformId(Long platformId) {
        List<NetWorkL2DTO> netWorkL2DTOS =  networkL2Service.getNetworkL2ByPlatformId(platformId);
        return CommonResult.success(netWorkL2DTOS);
    }

    @Override
    public CommonResult<List<NetWorkL3DTO>> getNetworkL3ByPlatformId(Long platformId) {
        List<NetWorkL3DTO> netWorkL3DTOS = networkL3Service.getNetworkL3ByPlatformId(platformId);
        return CommonResult.success(netWorkL3DTOS);
    }
}
