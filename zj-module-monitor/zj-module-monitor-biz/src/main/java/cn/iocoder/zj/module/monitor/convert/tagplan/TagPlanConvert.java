package cn.iocoder.zj.module.monitor.convert.tagplan;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.monitor.controller.admin.tagplan.vo.TagPlanCreateReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.tagplan.vo.TagPlanExcelVO;
import cn.iocoder.zj.module.monitor.controller.admin.tagplan.vo.TagPlanRespVO;
import cn.iocoder.zj.module.monitor.controller.admin.tagplan.vo.TagPlanUpdateReqVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.tagplan.TagPlanDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 巡检计划 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TagPlanConvert {

    TagPlanConvert INSTANCE = Mappers.getMapper(TagPlanConvert.class);

    TagPlanDO convert(TagPlanCreateReqVO bean);

    TagPlanDO convert(TagPlanUpdateReqVO bean);

    TagPlanRespVO convert(TagPlanDO bean);

    List<TagPlanRespVO> convertList(List<TagPlanDO> list);

    PageResult<TagPlanRespVO> convertPage(PageResult<TagPlanDO> page);

    List<TagPlanExcelVO> convertList02(List<TagPlanDO> list);

}
