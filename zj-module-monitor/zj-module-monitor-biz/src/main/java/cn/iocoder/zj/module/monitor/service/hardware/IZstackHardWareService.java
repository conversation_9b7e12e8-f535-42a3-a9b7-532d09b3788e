package cn.iocoder.zj.module.monitor.service.hardware;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.dal.dataobject.hardwareinfo.HardwareInfoDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostinfo.HostInfoDO;

import java.util.List;
import java.util.Map;

public interface IZstackHardWareService {


     String cpuUsage();


     void memoryUsage();


     void netWorkUpdown();


     void netWorkSendOrHarvest();

    void createHostInfoList(List<HardwareInfoDO> list);

    int getCount(String typeName);

    void updateHardwareInfoList(List<HardwareInfoDO> list);

    List<HardwareInfoDO> getAll(String typeName);


    String getIp(String uuid, String sourceType);

    int deleteHardWare(List<HardwareInfoDO> list);

    List<HardwareInfoDO> getListAll();

    void removeDuplicateData();

    Long getCpuSockets(List<String> platformIds);
}
