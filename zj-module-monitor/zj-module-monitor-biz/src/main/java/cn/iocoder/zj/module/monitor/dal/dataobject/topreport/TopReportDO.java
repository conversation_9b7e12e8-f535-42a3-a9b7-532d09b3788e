package cn.iocoder.zj.module.monitor.dal.dataobject.topreport;

import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("top_report")
public class TopReportDO extends BaseDO {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 报表名称
     */
    private String reportName;

    /**
     * 描述
     */
    private String description;

    /**
     * TOP N数字
     */
    private Integer topNum;

    /**
     * 统计方式（1小值 2大值 3平均值）
     */
    private Integer statisticsType;

    /**
     * 资产类型
     */
    private String assetType;

    private String metricType;

    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;
}