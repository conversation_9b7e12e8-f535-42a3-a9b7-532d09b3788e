package cn.iocoder.zj.module.monitor.convert.secgrouprule;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.api.secgrouprule.dto.SecgroupRuleCreateReqDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.secgrouprule.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.secgrouprule.SecgroupRuleDO;

/**
 * 端口组规则 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface SecgroupRuleConvert {

    SecgroupRuleConvert INSTANCE = Mappers.getMapper(SecgroupRuleConvert.class);

    SecgroupRuleDO convert(SecgroupRuleCreateReqVO bean);

    SecgroupRuleDO convert(SecgroupRuleUpdateReqVO bean);

    SecgroupRuleRespVO convert(SecgroupRuleDO bean);

    List<SecgroupRuleRespVO> convertList(List<SecgroupRuleDO> list);

    PageResult<SecgroupRuleRespVO> convertPage(PageResult<SecgroupRuleDO> page);

    List<SecgroupRuleExcelVO> convertList02(List<SecgroupRuleDO> list);

    List<SecgroupRuleDO> convertCreateList(List<SecgroupRuleCreateReqDto> reqDTO);

    List<SecgroupRuleCreateReqDto> convertDoToCreateDtoList(List<SecgroupRuleDO> secgroupRuleList);
}
