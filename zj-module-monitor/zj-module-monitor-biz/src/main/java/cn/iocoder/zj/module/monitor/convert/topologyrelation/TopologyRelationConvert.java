package cn.iocoder.zj.module.monitor.convert.topologyrelation;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.topologyrelation.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.topologyrelation.TopologyRelationDO;

/**
 * 拓扑图关系 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TopologyRelationConvert {

    TopologyRelationConvert INSTANCE = Mappers.getMapper(TopologyRelationConvert.class);

    TopologyRelationDO convert(TopologyRelationCreateReqVO bean);

    TopologyRelationDO convert(TopologyRelationUpdateReqVO bean);

    TopologyRelationRespVO convert(TopologyRelationDO bean);

    List<TopologyRelationRespVO> convertList(List<TopologyRelationDO> list);

    PageResult<TopologyRelationRespVO> convertPage(PageResult<TopologyRelationDO> page);

    List<TopologyRelationExcelVO> convertList02(List<TopologyRelationDO> list);

}
