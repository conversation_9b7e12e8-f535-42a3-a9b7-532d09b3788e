package cn.iocoder.zj.module.monitor.service.storageinfo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.api.storage.dto.StorageRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.controller.admin.storageinfo.vo.StorageInfoCreateReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.storageinfo.vo.StorageInfoExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.storageinfo.vo.StorageInfoPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.storageinfo.vo.StorageInfoUpdateReqVO;
import cn.iocoder.zj.module.monitor.convert.storageinfo.StorageInfoConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.storageinfo.StorageInfoDO;
import cn.iocoder.zj.module.monitor.dal.mysql.hardwarestorage.HardwareStorageMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.storageinfo.StorageInfoMapper;
import cn.iocoder.zj.module.monitor.pojo.AssetReqVO;
import cn.iocoder.zj.module.monitor.util.StringUtil;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.googlecode.aviator.utils.ArrayHashMap;
import org.frameworkset.elasticsearch.ElasticSearchHelper;
import org.frameworkset.elasticsearch.client.ClientInterface;
import org.frameworkset.elasticsearch.entity.ESDatas;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.singleton;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.STORAGE_INFO_NOT_EXISTS;

/**
 * 存储设备信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class StorageInfoServiceImpl implements StorageInfoService {


    @Resource
    private StorageInfoMapper storageInfoMapper;

    @Resource
    private RoleApi roleApi;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private AdminUserApi adminUserService;

    @Resource
    private HardwareStorageMapper hardwareStorageMapper;
    @Resource
    private PlatformconfigApi platformconfigApi;

    @Override
    public Long createStorageInfo(StorageInfoCreateReqVO createReqVO) {
        // 插入
        StorageInfoDO storageInfo = StorageInfoConvert.INSTANCE.convert(createReqVO);
        storageInfoMapper.insert(storageInfo);
        // 返回
        return storageInfo.getId();
    }

    @Override
    public void updateStorageInfo(StorageInfoUpdateReqVO updateReqVO) {
        // 校验存在
        validateStorageInfoExists(updateReqVO.getId());
        // 更新
        StorageInfoDO updateObj = StorageInfoConvert.INSTANCE.convert(updateReqVO);
        storageInfoMapper.updateById(updateObj);
    }

    @Override
    public void deleteStorageInfo(Long id) {
        // 校验存在
        validateStorageInfoExists(id);
        // 删除
        storageInfoMapper.deleteById(id);
    }

    private void validateStorageInfoExists(Long id) {
        if (storageInfoMapper.selectById(id) == null) {
            throw exception(STORAGE_INFO_NOT_EXISTS);
        }
    }

    @Override
    public StorageInfoDO getStorageInfo(String uuid) {
        return storageInfoMapper.selectOne("uuid", uuid);
    }

    @Override
    public List<StorageInfoDO> getStorageInfoList(Collection<Long> ids) {
        return storageInfoMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<StorageInfoDO> getStorageInfoPage(StorageInfoPageReqVO pageReqVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        AdminUserRespDTO adminUserRespDTO = adminUserService.getUser(loginUser.getId()).getData();
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            if (StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
                pageReqVO.setTenantId(Arrays.asList(String.valueOf(adminUserRespDTO.getTenantId()).split(",")));
            } else {
                pageReqVO.setTenantId(Arrays.asList(adminUserRespDTO.getServiceTenantId().split(",")));
            }
        }
        PageParam pageParam = new PageParam().setPageNo(pageReqVO.getPageNo()).setPageSize(pageReqVO.getPageSize());
        IPage<StorageInfoDO> mpPage = MyBatisUtils.buildPage(pageParam);
        List<String> uids = new ArrayList<>();
        if (pageReqVO.getIds()!=null && StringUtil.isNotEmpty(pageReqVO.getIds())) {
            uids = Arrays.asList(pageReqVO.getIds().split(","));
        }
        if (StringUtil.isNotEmpty(pageReqVO.getSortBy())){
            String regex = "([a-z])([A-Z]+)";
            String replacement = "$1_$2";
            String output = pageReqVO.getSortBy().replaceAll(regex, replacement).toLowerCase();
            pageReqVO.setSortBy(output);
        }
        return new PageResult<>(storageInfoMapper.getStorageInfoPage(mpPage, pageReqVO, uids), mpPage.getTotal());

    }

    @Override
    public List<StorageInfoDO> getStorageInfoList(StorageInfoExportReqVO exportReqVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        AdminUserRespDTO adminUserRespDTO = adminUserService.getUser(loginUser.getId()).getData();
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            if (StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
                exportReqVO.setTenantId(Arrays.asList(String.valueOf(adminUserRespDTO.getTenantId()).split(",")));
            } else {
                exportReqVO.setTenantId(Arrays.asList(adminUserRespDTO.getServiceTenantId().split(",")));
            }
        }
        return storageInfoMapper.getStorageInfoList(exportReqVO);
    }

    @Override
    public void createStorageInfoList(List<StorageInfoDO> list) {
        for (StorageInfoDO storageInfoDO : list) {
            storageInfoDO.setMediaType(null);
        }
        storageInfoMapper.insertBatch(list);
    }

    @Override
    public int getCount(String typeName) {
        return storageInfoMapper.getCount(typeName);
    }

    @Override
    public void updateHostInfoList(List<StorageRespCreateReqDTO> list) {
        for (StorageRespCreateReqDTO storageRespCreateReqDTO : list) {
            storageRespCreateReqDTO.setMediaType(null);
        }
        storageInfoMapper.updateHostInfoList(list);
    }

    @Override
    public List<StorageInfoDO> getAll(String typeName) {
        return storageInfoMapper.selectList(StorageInfoDO::getTypeName, typeName);
    }

    @Override
    public List<Map> getStorageCpuInfo(Long id, String uuid, String timeStr) {
        ClientInterface clientUtil = ElasticSearchHelper.getConfigRestClientUtil("esmapper/ESStorageMapper.xml");
        Map searchFields = new HashMap<>();
        searchFields.put("passTime", "now-" + timeStr);
        searchFields.put("uuid", uuid);
        ESDatas<Map> data = clientUtil.searchList("storage_" + id + "/_search", "storageInfo", searchFields, Map.class);
        return data.getDatas();
    }

    @Override
    public List<Object> getUsedCapacityInPercent(String uuid, Long startTime, Long endTime) {
//        Long period = endTime - startTime;
//        String mTime = DateUtil.format(DateUtil.beginOfDay(new Date(startTime)), "yyyy-MM-dd HH:mm:ss");
//
//        String lastQuery = "SELECT LAST(value) as value from zj_cloud_storage WHERE uuid = '" + uuid +
//                "' and time>= '" + mTime + "' and time < '" + DateUtil.format(new Date(startTime), "yyyy-MM-dd HH:mm:ss") + "'";
//
//        List<Object> lastRecords = influxDBTemplate.fetchRecords(lastQuery);
//
//        String query = " SELECT MAX(value) as value,type,storage_metricName FROM zj_cloud_storage WHERE uuid = '" + uuid
//                + "' AND metricName = 'UsedCapacityInPercent' AND time >= '";
//        query = query + DateUtil.format(new Date(startTime), "yyyy-MM-dd HH:mm:ss") + "' AND time <= '"
//                + DateUtil.format(new Date(endTime), "yyyy-MM-dd HH:mm:ss") + "'";
//        if (period > 3600000 && period <= 21600000) {
//            query = query + "group by time(2m)";
//        } else if (period > 21600000 && period <= 86400000) {
//            query = query + "group by time(8m)";
//        } else if (period > 86400000 && period <= 604800000) {
//            query = query + "group by time(56m)";
//        } else if (period > 604800000 && period <= 2592000000L) {
//            query = query + "group by time(2h)";
//        } else if (period > 2592000000L) {
//            query = query + "group by time(3h)";
//        } else if (period <= 3600000) {
//            query = query + "group by time(60s)";
//        }
//        query = query + " fill(0) order by time asc";
//        List<Object> re = influxDBTemplate.fetchRecords(query).stream()
//                .sorted((data1, data2) -> JSONObject.parseObject(JSONUtil.toJsonStr(data1)).getString("time").compareTo(JSONObject.parseObject(JSONUtil.toJsonStr(data2)).getString("time")))
//                .collect(Collectors.toList());
//
//        List<Object> data = new ArrayList<>();
//        for (Object o : re) {
//            Map map = new HashMap();
//            JSONObject jsonObject = JSONObject.parseObject(JSONUtil.toJsonStr(o));
//            String value = jsonObject.getBigDecimal("value").toString();
//            String type = jsonObject.getString("type");
//            String metricName = jsonObject.getString("storage_metricName");
//            String time = jsonObject.getString("time");
//            if (type.equals("0")) {
//                type = "UsedCapacityInPercent";
//            }
//            if (metricName.equals("0")) {
//                metricName = "UsedCapacityInPercent";
//            }
//            if (value.equals("0")) {
//                List<Map> maps = JSONArray.parseArray(JSON.toJSONString(lastRecords), Map.class);
//                if (maps.size()>0){
//                    Map map1 =maps.get(0);
//                    value = Convert.toStr(map1.get("value"));
//                }
//            }
//            map.put("type", type);
//            map.put("storage_metricName", metricName);
//            map.put("value", Convert.toBigDecimal(value));
//            map.put("time", time);
//            data.add(map);
//        }
//
//        return data;
        return new ArrayList<>();
    }

    @Override
    public Map<String, Object> getStorageCapacity(List<String> tenantIds, Long platformId, Long regionId) {
        if (regionId != null && regionId.longValue() != 0) {
            if (regionId.toString().substring(2, 6).equals("0000")) {
                regionId = Long.parseLong(regionId.toString().substring(0, 2));
            } else {
                regionId = Long.parseLong(regionId.toString().substring(0, 4));
            }
        }
        Map<String, Object> storageCapacity = storageInfoMapper.getStorageCapacity(tenantIds, platformId, regionId);
        if (storageCapacity == null) {
            storageCapacity = new HashMap<>();
            storageCapacity.put("usedCapacity", 0);
            storageCapacity.put("totalCapacity", 0);
        }
        return storageCapacity;
    }

    @Override
    public Map<String, Object> getAllUsedCapacityInPercent(List<String> tenantId, Long platformId, Long regionId, String time) {
//        Map<String, String> timeConfig = getTimeConfig(time);
//        String interval = timeConfig.get("interval");
//        String date = timeConfig.get("date");
//
//        Map<String, Object> storageUsedCapacityInfo = new HashMap<>();
//        StringBuilder platformQuery = new StringBuilder();
//        if (platformId != null && platformId != 0) {
//            platformQuery.append(" AND platformId='").append(platformId).append("'");
//        } else {
//            List<Long> platformList = storageInfoMapper.selectPlatformList(tenantId);
//            if (platformList.size()>0){
//                platformQuery.append("AND (");
//                for (int i = 0, len = platformList.size(); i < len; i++) {
//                    platformQuery.append(" platformId='").append(platformList.get(i)).append("'");
//                    if (i < len - 1) {
//                        platformQuery.append(" or");
//                    }
//                }
//                platformQuery.append(")");
//            }else {
//                platformQuery.append(" AND platformId='").append(platformId).append("'");
//            }
//        }
//        if (regionId != null && regionId != 0) {
//            String regionString = regionId.toString();
//            String simplifiedRegionId = regionString.substring(2, 6).equals("0000") ? regionString.substring(0, 2) : regionString.substring(0, 4);
//            platformQuery.append(" AND regionId =~ /^").append(simplifiedRegionId).append("/");
//        }
//        String query = String.format("SELECT MEAN(value) AS value FROM zj_cloud_storage WHERE metricName = '%s' %s AND time >= %s GROUP BY time(%s) FILL(0) order by time asc", "UsedCapacityInPercent", platformQuery, date, interval);
//        List<Object> storage = influxDBTemplate.fetchRecords(query);
//        List<Map<String, String>> stor = storage.stream()
//                .map(obj -> (Map<String, String>) obj)
//                .map(map -> {
//                    Map<String, String> Map = new HashMap<>();
//                    Map.putAll(map);
//                    return Map;
//                })
//                .sorted((map1, map2) -> map1.get("time").compareTo(map2.get("time")))
//                .collect(Collectors.toList());
//        storageUsedCapacityInfo.put("storageUsedCapacity", stor);
//        query = " SELECT MEAN(value) as value FROM zj_cloud_storage WHERE metricName = 'UsedCapacityInPercent' " + platformQuery + " AND time >= now()-30m GROUP BY time(20s) fill(none) order by time desc limit 1";
//        List currentInfo = new ArrayList();
//        if (influxDBTemplate.fetchRecords(query).size() == 0) {
//            currentInfo.add(0);
//        } else {
//            Map monitorEntryName = (Map) influxDBTemplate.fetchRecords(query).get(0);
//            currentInfo.add(monitorEntryName.get("value"));
//        }
//        storageUsedCapacityInfo.put("currentInfo", currentInfo);
//        return storageUsedCapacityInfo;
        return new ArrayHashMap<>();
    }

    @Override
    public int deleteStorageList(List<StorageInfoDO> list) {
        return storageInfoMapper.deleteStorageList(list);
    }

    @Override
    public Long getCountByPlatformId(Long platformId) {
        return storageInfoMapper.getCountByPlatformId(platformId);
    }

    @Override
    public PageResult<StorageInfoDO> getStorageInfoSlavePage(StorageInfoPageReqVO pageReqVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        AdminUserRespDTO adminUserRespDTO = adminUserService.getUser(loginUser.getId()).getData();
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            if (StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
                pageReqVO.setTenantId(Arrays.asList(String.valueOf(adminUserRespDTO.getTenantId()).split(",")));
            } else {
                pageReqVO.setTenantId(Arrays.asList(adminUserRespDTO.getServiceTenantId().split(",")));
            }
        }
        PageParam pageParam = new PageParam().setPageNo(pageReqVO.getPageNo()).setPageSize(pageReqVO.getPageSize());
        IPage<StorageInfoDO> mpPage = MyBatisUtils.buildPage(pageParam);
        List<String> uids = new ArrayList<>();
        if (StringUtil.isNotEmpty(pageReqVO.getIds())) {
            uids = Arrays.asList(pageReqVO.getIds().split(","));
        }
        return new PageResult<>(storageInfoMapper.getStorageInfoSlavePage(mpPage, pageReqVO, uids), mpPage.getTotal());

    }

    @Slave
    @Override
    public StorageInfoDO getStorageSlaveInfo(String uuid) {
        return storageInfoMapper.getByUuid(uuid);
    }

    @Override
    public StorageInfoDO getStorageMasterInfo(String uuid) {
        return storageInfoMapper.getByUuid(uuid);
    }

    @Override
    public List<StorageInfoDO> getListByPlatformId(Collection<Long> platformId) {
        return storageInfoMapper.getListByPlatformId(platformId);
    }

    @Override
    public List<StorageInfoDO> getStorageListByUuids(List<String> uuids) {
        return storageInfoMapper.getStorageListByUuids(uuids);
    }

    @Override
    public PageResult<StorageInfoDO> selectStorageList(StorageInfoPageReqVO pageVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        AdminUserRespDTO adminUserRespDTO = adminUserService.getUser(loginUser.getId()).getData();
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            if (StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
                pageVO.setTenantId(Arrays.asList(String.valueOf(adminUserRespDTO.getTenantId()).split(",")));
            } else {
                pageVO.setTenantId(Arrays.asList(adminUserRespDTO.getServiceTenantId().split(",")));
            }
        }
        PageParam pageParam = new PageParam().setPageNo(pageVO.getPageNo()).setPageSize(pageVO.getPageSize());
        IPage<StorageInfoDO> mpPage = MyBatisUtils.buildPage(pageParam);
        return new PageResult<>(storageInfoMapper.selectStorageList(mpPage, pageVO), mpPage.getTotal());
    }

    @Override
    public List<StorageInfoDO> getListAll() {
        return storageInfoMapper.selectList();
    }

    @Override
    public Map<String, Object> getStorageStatusCount(List<String> tenantIds, Long platformId) {
        Map<String, Object> hostStatusCount = storageInfoMapper.getStorageStatusCount(tenantIds, platformId);
        if (hostStatusCount.size() == 1) {
            hostStatusCount = new HashMap<>();
            hostStatusCount.put("other", 0);
            hostStatusCount.put("online", 0);
            hostStatusCount.put("unonline", 0);
            hostStatusCount.put("total", 0);
        }
        return hostStatusCount;
    }

    @Override
    public void deleteStorageInfoByplatform(Long platformId) {
        storageInfoMapper.deleteStorageInfoByplatform(platformId);
    }

    @Override
    @TenantIgnore
    public List<StorageRespCreateReqDTO> getStorageByPlatformId(Long id) {
        return storageInfoMapper.getStorageByPlatformId(id);
    }

    @Override
    @TenantIgnore
    public List<StorageInfoDO> getStoragesByTenantOrPlatforms(AssetReqVO assetReqVO) {
        List<Long> platformIds = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(assetReqVO.getPlatformIds())) {
            platformIds = assetReqVO.getPlatformIds();
        } else {
            List<Map> platform = platformconfigApi.getPlatformByTenantId(String.valueOf(assetReqVO.getTenantId())).getData();
            if (ObjectUtil.isNotEmpty(platform)) {
                platformIds = platform.stream().map(map -> Long.parseLong(String.valueOf(map.get("platformId")))).toList();
            }
        }
        return storageInfoMapper.getListByPlatform(platformIds);
    }

    private Map<String, String> getTimeConfig(String time) {
        Map<String, String> config = new HashMap<>();
        switch (time) {
            case "15m":
                config.put("interval", "3m");
                config.put("date", "now()-15m");
                break;
            case "1d":
                config.put("interval", "20m");
                config.put("date", "now()-1d");
                break;
            case "7d":
                config.put("interval", "2h");
                config.put("date", "'" + DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -6), "yyyy-MM-dd") + "'");
                break;
            case "15d":
                config.put("interval", "4h");
                config.put("date", "'" + DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -14), "yyyy-MM-dd") + "'");
                break;
            case "30d":
                config.put("interval", "8h");
                config.put("date", "'" + DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -29), "yyyy-MM-dd") + "'");
                break;
            case "90d":
                config.put("interval", "24h");
                config.put("date", "'" + DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -89), "yyyy-MM-dd") + "'");
                break;
            default:
                config.put("interval", "2h"); // defaults for unmapped values
                config.put("date", "now()");
        }
        return config;
    }

}
