package cn.iocoder.zj.module.monitor.controller.admin.mediatype.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 介质类型 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class MediaTypeBaseVO {

    @Schema(description = "名称", required = true)
    @NotNull(message = "名称不能为空")
    private String name;

    @Schema(description = "备注", required = false)
//    @NotNull(message = "备注不能为空")
    private String remark;

    @Schema(description = "资源总数", required = false)
//    @NotNull(message = "资源总数不能为空")
    private Long totalCount;

}
