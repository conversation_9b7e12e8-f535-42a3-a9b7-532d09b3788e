package cn.iocoder.zj.module.monitor.controller.admin.tagplan.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 巡检记录 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TagRecordRespVO extends TagRecordBaseVO {

    @Schema(description = "主键ID", required = true)
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

    @Schema(description = "租户名称")
    private String tenantName;

    @Schema(description = "平台名称，多的用,分隔")
    private String platformName;

    private String tagsName;
}
