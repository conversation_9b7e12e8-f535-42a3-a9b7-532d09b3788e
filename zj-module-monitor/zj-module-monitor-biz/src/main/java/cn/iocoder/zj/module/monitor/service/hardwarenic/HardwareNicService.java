package cn.iocoder.zj.module.monitor.service.hardwarenic;

import java.util.*;
import javax.validation.*;

import cn.iocoder.zj.module.monitor.api.hardwarenic.HardWareNicRespDTO;
import cn.iocoder.zj.module.monitor.controller.admin.hardwarenic.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.hardwarenic.HardwareNicDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 物理机网络关系 Service 接口
 *
 * <AUTHOR>
 */
public interface HardwareNicService {

    /**
     * 创建物理机网络关系
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createHardwareNic(@Valid HardwareNicCreateReqVO createReqVO);

    /**
     * 更新物理机网络关系
     *
     * @param updateReqVO 更新信息
     */
    void updateHardwareNic(@Valid HardwareNicUpdateReqVO updateReqVO);

    /**
     * 删除物理机网络关系
     *
     * @param id 编号
     */
    void deleteHardwareNic(Long id);

    /**
     * 获得物理机网络关系
     *
     * @param id 编号
     * @return 物理机网络关系
     */
    HardwareNicDO getHardwareNic(Long id);

    /**
     * 获得物理机网络关系列表
     *
     * @param reqVO 编号
     * @return 物理机网络关系列表
     */
    List<HardwareNicDO> getHardwareNicList(HardwareNicReqVO reqVO);

    /**
     * 获得物理机网络关系分页
     *
     * @param pageReqVO 分页查询
     * @return 物理机网络关系分页
     */
    PageResult<HardwareNicDO> getHardwareNicPage(HardwareNicPageReqVO pageReqVO);

    /**
     * 获得物理机网络关系列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 物理机网络关系列表
     */
    List<HardwareNicDO> getHardwareNicList(HardwareNicExportReqVO exportReqVO);

    void updateHardwareNicInfoList(List<HardwareNicDO> list);

    void createHardwareNicList(List<HardwareNicDO> list);

    List<HardWareNicRespDTO> getHardwareNicByPlatformId(Long platformId);

    int deleteHardwareNicList(List<HardwareNicDO> list);
}
