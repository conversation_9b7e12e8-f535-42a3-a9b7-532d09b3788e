package cn.iocoder.zj.module.monitor.controller.admin.hostinfo.vo;

import cn.iocoder.zj.framework.excel.core.convert.ByteConvent;
import cn.iocoder.zj.framework.excel.core.convert.StateConvert;
import cn.iocoder.zj.framework.excel.core.convert.UnitConvert;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 云主机基本信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class HostInfoExcelVO {

    @ExcelProperty("主机名称")
    private String name;

    @ExcelProperty(value = "状态",converter = StateConvert.class)
    private String state;

    @ExcelProperty("IP地址")
    private String ip;

    @ExcelProperty("弹性IP")
    private String vipIp;

    @ExcelProperty("集群名称")
    private String clusterName;

    @ExcelProperty("CPU架构")
    private String architecture;

    @ExcelProperty("操作系统类型")
    private String guestOsType;

    @ExcelProperty("主机创建时间")
    private Date vCreateDate;

    @ExcelProperty("主机类型")
    private String type;

    @ExcelProperty(value = "分配内存",converter = ByteConvent.class)
    private Long memorySize;

    @ExcelProperty(value = "分配CPU",converter = UnitConvert.class)
    private Integer cpuNum;

    @ExcelProperty("MAC地址")
    private String mac;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private LocalDateTime createTime;

}
