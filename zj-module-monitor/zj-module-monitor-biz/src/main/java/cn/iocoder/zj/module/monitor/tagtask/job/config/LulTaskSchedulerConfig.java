package cn.iocoder.zj.module.monitor.tagtask.job.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

@Configuration
public class LulTaskSchedulerConfig {

    @Bean
    @Primary
    public ThreadPoolTaskScheduler lulTaskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(30);
        scheduler.setThreadNamePrefix("tagTask-");
        scheduler.initialize();
        return scheduler;
    }
}
