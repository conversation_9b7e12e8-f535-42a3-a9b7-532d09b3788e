package cn.iocoder.zj.module.monitor.dal.dataobject.hostnic;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 云主机网络 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_host_nic")
@KeySequence("monitor_host_nic_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HostNicDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * uuid
     */
    private String uuid;
    /**
     * 名称
     */
    private String name;
    /**
     * 云主机uuid
     */
    private String hostUuid;
    /**
     * ipV6
     */
    private String ip6;
    /**
     * ip
     */
    private String ip;
    /**
     * mac
     */
    private String mac;
    /**
     * 驱动
     */
    private String driver;
    /**
     * 在经典网络
     */
    private Byte inClassicNetwork;
    /**
     * 网络uuid
     */
    private String networkUuid;
    /**
     * 三级网络名称
     */
    private String networkName;
    /**
     * 队列数量
     */
    private String numQueues;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;

}
