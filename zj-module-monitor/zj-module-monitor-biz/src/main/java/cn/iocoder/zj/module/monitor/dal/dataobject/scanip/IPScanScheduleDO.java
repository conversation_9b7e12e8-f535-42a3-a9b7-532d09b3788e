package cn.iocoder.zj.module.monitor.dal.dataobject.scanip;

import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * IP扫描计划 DO
 */
@TableName("ip_scan_schedule")
@KeySequence("ip_scan_schedule_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class IPScanScheduleDO extends BaseDO {

    /**
     * 计划ID
     */
    @TableId
    private Long id;

    /**
     * IP段ID
     */
    private Long ipRangeId;

    /**
     * IP段名称
     */
    private String ipRangeName;

    /**
     * 计划时间
     */
    private LocalDateTime scheduleTime;

    /**
     * 执行时间
     */
    private LocalDateTime executeTime;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 计划类型：1-一次性，2-定时，3-周期性
     */
    private Integer scheduleType;

    /**
     * 状态：1-待执行，2-执行中，3-已完成，4-失败，5-已取消
     */
    private Integer status;

    /**
     * 是否在系统重启后执行
     */
    private Boolean executeOnRestart;

    /**
     * 进度百分比 (0-100)
     */
    private Integer progress;

    /**
     * 总IP数量
     */
    private Integer totalCount;

    /**
     * 已处理IP数量
     */
    private Integer processedCount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    // 计划类型常量
    public static final Integer TYPE_ONE_TIME = 1;
    public static final Integer TYPE_SCHEDULED = 2;
    public static final Integer TYPE_PERIODIC = 3;

    // 状态常量
    public static final Integer STATUS_PENDING = 1;
    public static final Integer STATUS_PROCESSING = 2;
    public static final Integer STATUS_COMPLETED = 3;
    public static final Integer STATUS_FAILED = 4;
    public static final Integer STATUS_CANCELLED = 5;
}