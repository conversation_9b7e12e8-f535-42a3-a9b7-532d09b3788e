package cn.iocoder.zj.module.monitor.tagtask.job.model;


import cn.iocoder.zj.module.monitor.service.tagplan.TagTaskModelService;
import cn.iocoder.zj.module.monitor.tagtask.job.task.TagTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class TagTaskServiceImpl implements TagTaskService {

    @Lazy
    @Resource
    private TagTaskModelService lulTaskModelService;

    @Override
    public List<TagTaskModel> listTasks() {
        return lulTaskModelService.findAll();
    }

    @Override
    public void removeTask(TagTaskModel taskCacheModel) {
        Assert.notNull(taskCacheModel.getId(), "任务ID不能为空");
        lulTaskModelService.removeTask(taskCacheModel);
    }

    @Override
    public void addTask(TagTaskModel taskCacheModel) {
        taskCacheModel.setVersion(0);
        lulTaskModelService.addTask(taskCacheModel);
    }

    @Override
    public void updateTasByJobId(TagTaskModel taskCacheModel) {
        Assert.notNull(taskCacheModel.getId(), "任务ID不能为空");
        lulTaskModelService.updateTasByJobId(taskCacheModel);
    }

    @Override
    public TagTaskModel getTaskByJobId(Long jobId) {
        return lulTaskModelService.getTaskByJobId(jobId);
    }


}
