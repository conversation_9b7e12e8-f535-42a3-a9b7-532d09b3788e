package cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 分别获取参与或未参与告警的设备列表 Request VO")
@Data
@ToString(callSuper = true)
public class AlarmHostRelationReqVO {
    @Schema(description = "告警配合名称")
    private String alarmName;

    @Schema(description = "告警配置ID,进入详情页必传")
    private Long alarmId;

    @Schema(description = "告警等级")
    private Integer alarmLevel;

    @Schema(description = "告警消息")
    private String context;

    @Schema(description = "云主机名称")
    private String hostName;

    @Schema(description = "云主机uuid")
    private String hostUuid;

    @Schema(description = "租户名称")
    private String tenantName;

    @Schema(description = "租户ID")
    private List<String> tenantId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "云主机、宿主机或存储的名称")
    private String productsName;
}
