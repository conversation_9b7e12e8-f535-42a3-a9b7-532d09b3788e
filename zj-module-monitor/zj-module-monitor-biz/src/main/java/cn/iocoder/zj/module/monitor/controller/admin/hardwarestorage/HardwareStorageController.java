package cn.iocoder.zj.module.monitor.controller.admin.hardwarestorage;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.monitor.controller.admin.hardwarestorage.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.hardwarestorage.HardwareStorageDO;
import cn.iocoder.zj.module.monitor.convert.hardwarestorage.HardwareStorageConvert;
import cn.iocoder.zj.module.monitor.service.hardwarestorage.HardwareStorageService;

@Tag(name = "管理后台 - 宿主机与存储关联")
@RestController
@RequestMapping("/monitor/hardware-storage")
@Validated
public class HardwareStorageController {

    @Resource
    private HardwareStorageService hardwareStorageService;

    @PostMapping("/create")
    @Operation(summary = "创建宿主机与存储关联")
    @PreAuthorize("@ss.hasPermission('monitor:hardware-storage:create')")
    public CommonResult<Long> createHardwareStorage(@Valid @RequestBody HardwareStorageCreateReqVO createReqVO) {
        return success(hardwareStorageService.createHardwareStorage(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新宿主机与存储关联")
    @PreAuthorize("@ss.hasPermission('monitor:hardware-storage:update')")
    public CommonResult<Boolean> updateHardwareStorage(@Valid @RequestBody HardwareStorageUpdateReqVO updateReqVO) {
        hardwareStorageService.updateHardwareStorage(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除宿主机与存储关联")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('monitor:hardware-storage:delete')")
    public CommonResult<Boolean> deleteHardwareStorage(@RequestParam("id") Long id) {
        hardwareStorageService.deleteHardwareStorage(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得宿主机与存储关联")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('monitor:hardware-storage:query')")
    public CommonResult<HardwareStorageRespVO> getHardwareStorage(@RequestParam("id") Long id) {
        HardwareStorageDO hardwareStorage = hardwareStorageService.getHardwareStorage(id);
        return success(HardwareStorageConvert.INSTANCE.convert(hardwareStorage));
    }

    @GetMapping("/list")
    @Operation(summary = "获得宿主机与存储关联列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('monitor:hardware-storage:query')")
    public CommonResult<List<HardwareStorageRespVO>> getHardwareStorageList(@RequestParam("ids") Collection<Long> ids) {
        List<HardwareStorageDO> list = hardwareStorageService.getHardwareStorageList(ids);
        return success(HardwareStorageConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得宿主机与存储关联分页")
    @PreAuthorize("@ss.hasPermission('monitor:hardware-storage:query')")
    public CommonResult<PageResult<HardwareStorageRespVO>> getHardwareStoragePage(@Valid HardwareStoragePageReqVO pageVO) {
        PageResult<HardwareStorageDO> pageResult = hardwareStorageService.getHardwareStoragePage(pageVO);
        return success(HardwareStorageConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出宿主机与存储关联 Excel")
    @PreAuthorize("@ss.hasPermission('monitor:hardware-storage:export')")
    @OperateLog(type = EXPORT)
    public void exportHardwareStorageExcel(@Valid HardwareStorageExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<HardwareStorageDO> list = hardwareStorageService.getHardwareStorageList(exportReqVO);
        // 导出 Excel
        List<HardwareStorageExcelVO> datas = HardwareStorageConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "宿主机与存储关联.xls", "数据", HardwareStorageExcelVO.class, datas);
    }

}
