package cn.iocoder.zj.module.monitor.controller.admin.gatherdevice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 采集设备更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GatherDeviceUpdateReqVO extends GatherDeviceBaseVO {

    @Schema(description = "主键id", required = true)
    @NotNull(message = "主键id不能为空")
    private Long id;

}
