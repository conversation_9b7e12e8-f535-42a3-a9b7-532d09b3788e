package cn.iocoder.zj.module.monitor.controller.admin.hostinfo.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 云主机基本信息 Excel 导出 Request VO，参数和 HostInfoPageReqVO 是一致的")
@Data
public class HostInfoExportReqVO {

    @Schema(description = "主机id")
    private String uuid;

    @Schema(description = "主机名称")
    private String name;

    @Schema(description = "主机状态")
    private String state;

    @Schema(description = "ip")
    private String ip;

    @Schema(description = "弹性ip")
    private String vipIp;

    @Schema(description = "集群id")
    private String clusterUuid;

    @Schema(description = "集群名称")
    private String clusterName;

    @Schema(description = "CPU架构")
    private String architecture;

    @Schema(description = "操作系统类型")
    private String guestOsType;

    @Schema(description = "主机类型")
    private String type;

    @Schema(description = "分配内存")
    private Long memorySize;

    @Schema(description = "分配cpu")
    private Integer cpuNum;

    @Schema(description = "mac 地址")
    private String mac;

    @Schema(description = "平台ID")
    private Long platformId;

    @Schema(description = "租户ID")
    private List<String> tenantId;

    @Schema(required = false,description = "开始时间",example = "2023-06-07")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String startTime;

    @Schema(required = false,description = "结束时间",example = "2023-06-07")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String endTime;

    @Schema(required = false,description = "主机tagId逗号拼接,1,2,3")
    private String tagIds;
}
