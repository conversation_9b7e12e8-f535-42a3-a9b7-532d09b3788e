package cn.iocoder.zj.module.monitor.service.topologyrelation;

import java.util.*;
import javax.validation.*;
import cn.iocoder.zj.module.monitor.controller.admin.topologyrelation.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.topologyrelation.TopologyRelationDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 拓扑图关系 Service 接口
 *
 * <AUTHOR>
 */
public interface TopologyRelationService {

    /**
     * 创建拓扑图关系
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTopologyRelation(@Valid TopologyRelationCreateReqVO createReqVO);

    /**
     * 更新拓扑图关系
     *
     * @param updateReqVO 更新信息
     */
    void updateTopologyRelation(@Valid TopologyRelationUpdateReqVO updateReqVO);

    /**
     * 删除拓扑图关系
     *
     * @param id 编号
     */
    void deleteTopologyRelation(Long id);

    /**
     * 获得拓扑图关系
     *
     * @param id 编号
     * @return 拓扑图关系
     */
    TopologyRelationDO getTopologyRelation(Long id);

    /**
     * 获得拓扑图关系列表
     *
     * @param ids 编号
     * @return 拓扑图关系列表
     */
    List<TopologyRelationDO> getTopologyRelationList(Collection<Long> ids);

    /**
     * 获得拓扑图关系分页
     *
     * @param pageReqVO 分页查询
     * @return 拓扑图关系分页
     */
    PageResult<TopologyRelationDO> getTopologyRelationPage(TopologyRelationPageReqVO pageReqVO);

    /**
     * 获得拓扑图关系列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 拓扑图关系列表
     */
    List<TopologyRelationDO> getTopologyRelationList(TopologyRelationExportReqVO exportReqVO);

}
