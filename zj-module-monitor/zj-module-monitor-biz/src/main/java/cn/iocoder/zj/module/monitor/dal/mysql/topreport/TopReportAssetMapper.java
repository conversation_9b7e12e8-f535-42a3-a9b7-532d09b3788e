package cn.iocoder.zj.module.monitor.dal.mysql.topreport;

import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.module.monitor.dal.dataobject.topreport.TopReportAssetDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface TopReportAssetMapper extends BaseMapperX<TopReportAssetDO> {

    default List<TopReportAssetDO> selectByReportId(Long reportId) {
        return selectList(TopReportAssetDO::getReportId, reportId);
    }

    default List<TopReportAssetDO> selectByReportIds(Collection<Long> reportIds) {
        return selectList(TopReportAssetDO::getReportId, reportIds);
    }

    default void deleteByReportId(Long reportId) {
        delete(new LambdaQueryWrapperX<TopReportAssetDO>()
                .eq(TopReportAssetDO::getReportId, reportId));
    }

    void delById(@Param("id")Long id);

    void getAssetBylist(List<String> assetIds);
}
