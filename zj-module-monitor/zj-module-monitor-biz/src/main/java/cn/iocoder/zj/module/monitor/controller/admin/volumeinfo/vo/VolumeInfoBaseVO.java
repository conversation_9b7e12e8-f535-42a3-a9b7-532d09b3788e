package cn.iocoder.zj.module.monitor.controller.admin.volumeinfo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
* 云盘信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class VolumeInfoBaseVO {

    @Schema(description = "云盘uuid")
    private String uuid;

    @Schema(description = "平台id")
    private String platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "云盘名称")
    private String name;

    @Schema(description = "云盘详细描述")
    private String description;

    @Schema(description = "主存储uuid")
    private String primaryStorageUuid;

    @Schema(description = "主存储名称")
    private String primaryStorageName;

    @Schema(description = "主存储类型")
    private String primaryStorageType;

    @Schema(description = "云主机uuid")
    private String vmInstanceUuid;

    @Schema(description = "云主机名称")
    private String vmInstanceName;

    @Schema(description = "云盘类型，数据云盘/根云盘")
    private String type;

    @Schema(description = "云盘格式")
    private String format;

    @Schema(description = "云盘大小")
    private Long size;

    @Schema(description = "云盘真实大小")
    private Long actualSize;

    @Schema(description = "云盘是否开启")
    private String state;

    @Schema(description = "云盘状态")
    private String status;

    @Schema(description = "是否删除，0否，1是")
    private Integer deleted;

    @Schema(description = "云盘创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date vCreateDate;

    @Schema(description = "云盘更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date vUpdateDate;

    // 最大iops max_iops
    @Schema(description = "最大iops")
    private Long maxIops;

    //吞吐量
    @Schema(description = "吞吐量")
    private BigDecimal throughput;

    //介质类型
    @Schema(description = "介质类型")
    private String mediaType;

    @Schema(description = "是否挂载")
    private Boolean isMount;
}
