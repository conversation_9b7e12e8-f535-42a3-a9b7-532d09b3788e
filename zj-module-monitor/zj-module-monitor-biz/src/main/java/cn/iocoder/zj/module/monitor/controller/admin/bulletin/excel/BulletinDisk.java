package cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel;


import cn.iocoder.zj.framework.excel.core.convert.ByteConvent;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.convert.BulletinDiskTypeConvert;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.convert.BulletinEnableStatusConvert;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.convert.BulletinMediaTypeConvert;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.convert.BulletinMountConvert;
import cn.iocoder.zj.module.monitor.controller.admin.tags.vo.TagsRespVO;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ExcelIgnoreUnannotated
public class BulletinDisk {

    private Long id;
    @ExcelProperty("云盘名称")
    private String name;

    @ExcelProperty("平台名称")
    private String platformName;

    @ExcelProperty(value = "状态", converter = BulletinEnableStatusConvert.class)
    private String state;

    @ExcelProperty(value = "云盘类型", converter = BulletinDiskTypeConvert.class)
    private String type;

    @ExcelProperty("云盘格式")
    private String format;

    @ExcelProperty("存储类型")
    private String primaryStorageType;

    @ExcelProperty(value = "云盘容量", converter = ByteConvent.class)
    private Long size;

    @ExcelProperty("最大iops")
    private Long maxIops;

    @ExcelProperty("云主机")
    private String vmInstanceName;

    @ExcelProperty("云存储")
    private String primaryStorageName;

    @ExcelProperty(value = "是否挂载", converter = BulletinMountConvert.class)
    private Boolean isMount;

    @ExcelProperty("标签")
    private String tagsStr;

    @ExcelProperty(value = "云盘真实容量", converter = ByteConvent.class)
    private Long actualSize;

    @ExcelProperty("吞吐量")
    private BigDecimal throughput;

    @ExcelProperty(value = "介质类型", converter = BulletinMediaTypeConvert.class)
    private String mediaType;

    @Schema(description = "标签信息")
    private List<TagsRespVO> tags;
}
