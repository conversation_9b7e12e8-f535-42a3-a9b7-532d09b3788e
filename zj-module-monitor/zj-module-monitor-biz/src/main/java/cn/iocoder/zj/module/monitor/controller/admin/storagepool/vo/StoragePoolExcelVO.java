package cn.iocoder.zj.module.monitor.controller.admin.storagepool.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 存储池 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class StoragePoolExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("存储池uuid")
    private String uuid;

    @ExcelProperty("存储名称")
    private String name;

    @ExcelProperty("存储池类型")
    private String type;

    @ExcelProperty("存储名称")
    private String description;

    @ExcelProperty("存储池创建时间")
    private LocalDateTime vCreateDate;

    @ExcelProperty("存储池修改时间")
    private LocalDateTime lastOpDate;

    @ExcelProperty("已使用容量")
    private BigDecimal usedCapacity;

    @ExcelProperty("虚拟可用容量")
    private BigDecimal availableCapacity;

    @ExcelProperty("总容量")
    private BigDecimal totalCapacity;

    @ExcelProperty("主存储uuid")
    private String storageUuid;

    @ExcelProperty("数据安全类型")
    private String securityPolicy;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("平台id")
    private Long platformId;

    @ExcelProperty("平台名称")
    private String platformName;

}
