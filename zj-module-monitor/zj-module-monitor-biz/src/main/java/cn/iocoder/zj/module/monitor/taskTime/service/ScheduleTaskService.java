package cn.iocoder.zj.module.monitor.taskTime.service;
import cn.iocoder.zj.module.monitor.taskTime.cache.TaskCacheModel;

import java.util.List;

/**
 * 定时任务服务类
 * <AUTHOR>
 **/
public interface ScheduleTaskService {

    /**
     * 查询任务列表
     * @return
     */
    List<TaskCacheModel> listTasks();

    /**
     * 移除任务
     */
    void removeTask(String jobName);


    /**
     * 新增任务
     */
    void addTask(String jobName,String cronExpression);

    /**
     * 更新任务
     */
    void updateTask(String jobName, String cronExpression);


    void clearAllTasks();
}
