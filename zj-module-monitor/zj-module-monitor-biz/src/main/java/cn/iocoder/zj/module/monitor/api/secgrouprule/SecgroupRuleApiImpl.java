package cn.iocoder.zj.module.monitor.api.secgrouprule;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.secgrouprule.dto.SecgroupRuleCreateReqDto;
import cn.iocoder.zj.module.monitor.controller.admin.secgroup.vo.SecgroupExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.secgrouprule.vo.SecgroupRuleExportReqVO;
import cn.iocoder.zj.module.monitor.convert.secgrouprule.SecgroupRuleConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.secgrouprule.SecgroupRuleDO;
import cn.iocoder.zj.module.monitor.service.secgrouprule.SecgroupRuleService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class SecgroupRuleApiImpl implements SecgroupruleApi {
    @Resource
    private SecgroupRuleService secgroupRuleService;

    @Override
    public CommonResult<Boolean> addSecgroupRules(List<SecgroupRuleCreateReqDto> reqDTO) {
        List<SecgroupRuleDO> list = SecgroupRuleConvert.INSTANCE.convertCreateList(reqDTO);
        secgroupRuleService.createSecgroupRuleList(list);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> updateSecgroupRules(List<SecgroupRuleCreateReqDto> secgroupRuleCreateReqDtoList) {
        if (!secgroupRuleCreateReqDtoList.isEmpty()) {
            List<SecgroupRuleDO> list = SecgroupRuleConvert.INSTANCE.convertCreateList(secgroupRuleCreateReqDtoList);
            secgroupRuleService.updateSecgroupRules(list);
        }
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> delSecgroupRules(List<SecgroupRuleCreateReqDto> secgroupRuleCreateReqDtoList) {
        if (!secgroupRuleCreateReqDtoList.isEmpty()) {
            List<SecgroupRuleDO> list = SecgroupRuleConvert.INSTANCE.convertCreateList(secgroupRuleCreateReqDtoList);
            secgroupRuleService.deleteSecgroupRules(list);
        }
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<List<SecgroupRuleCreateReqDto>> getSecgroupRulesByPlatformId(Long platformId) {
        SecgroupRuleExportReqVO reqVo = new SecgroupRuleExportReqVO();
        reqVo.setPlatformId(platformId);
        List<SecgroupRuleCreateReqDto> list = SecgroupRuleConvert.INSTANCE.convertDoToCreateDtoList(secgroupRuleService.getSecgroupRuleList(reqVo));
        return CommonResult.success(list);
    }
}
