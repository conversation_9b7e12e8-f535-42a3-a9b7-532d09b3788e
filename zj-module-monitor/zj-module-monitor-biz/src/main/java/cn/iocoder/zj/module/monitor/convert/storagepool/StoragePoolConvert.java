package cn.iocoder.zj.module.monitor.convert.storagepool;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.api.storagepool.dto.StoragePoolCreateRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.storagepool.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.storagepool.StoragePoolDO;

/**
 * 存储池 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface StoragePoolConvert {

    StoragePoolConvert INSTANCE = Mappers.getMapper(StoragePoolConvert.class);

    StoragePoolDO convert(StoragePoolCreateReqVO bean);

    StoragePoolDO convert(StoragePoolUpdateReqVO bean);

    StoragePoolRespVO convert(StoragePoolDO bean);

    List<StoragePoolRespVO> convertList(List<StoragePoolDO> list);

    PageResult<StoragePoolRespVO> convertPage(PageResult<StoragePoolDO> page);

    List<StoragePoolExcelVO> convertList02(List<StoragePoolDO> list);

    List<StoragePoolDO> convertCreateList(List<StoragePoolCreateRespDTO> reqDTO);
}
