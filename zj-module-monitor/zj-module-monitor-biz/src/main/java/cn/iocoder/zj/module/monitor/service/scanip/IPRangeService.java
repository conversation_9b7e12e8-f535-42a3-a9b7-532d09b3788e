package cn.iocoder.zj.module.monitor.service.scanip;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.monitor.controller.admin.scanip.vo.IPRangeVO;
import cn.iocoder.zj.module.monitor.controller.admin.scanip.vo.IPScanResultVO;

import javax.validation.Valid;
import java.util.List;

/**
 * IP段管理 Service 接口
 */
public interface IPRangeService {

    /**
     * 创建IP段
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createIPRange(@Valid IPRangeVO createReqVO);

    /**
     * 更新IP段
     *
     * @param updateReqVO 更新信息
     */
    void updateIPRange(@Valid IPRangeVO updateReqVO);

    /**
     * 删除IP段
     *
     * @param id 编号
     */
    void deleteIPRange(Long id);

    /**
     * 获得IP段
     *
     * @param id 编号
     * @return IP段
     */
    IPRangeVO getIPRange(Long id);

    /**
     * 获得IP段列表
     *
     * @param exportReqVO 查询条件
     * @return IP段列表
     */
    List<IPRangeVO> getIPRangeList(IPRangeVO exportReqVO);

    /**
     * 获得IP段分页
     *
     * @param pageReqVO 分页查询
     * @return IP段分页
     */
    PageResult<IPRangeVO> getIPRangePage(IPRangeVO pageReqVO);

    PageResult<IPScanResultVO> getIPScanResultPage(IPScanResultVO pageVO);

    void batchUpdate(IPRangeVO updateReqVO);

    void execute(Long id);
}
