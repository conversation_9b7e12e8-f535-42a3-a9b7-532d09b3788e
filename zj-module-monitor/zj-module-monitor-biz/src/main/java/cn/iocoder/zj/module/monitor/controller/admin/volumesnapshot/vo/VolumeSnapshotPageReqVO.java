package cn.iocoder.zj.module.monitor.controller.admin.volumesnapshot.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 云盘快照信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class    VolumeSnapshotPageReqVO extends PageParam {

    @Schema(description = "云盘快照uuid")
    private String uuid;

    @Schema(description = "云主机uuid")
    private String hostUuid;

    @Schema(description = "云主机名称")
    private String hostName;

    @Schema(description = "平台ID")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "云盘快照名称")
    private String name;

    @Schema(description = "云盘快照描述")
    private String description;

    @Schema(description = "云盘uuid")
    private String volumeUuid;

    @Schema(description = "云盘名称")
    private String volumeName;

    @Schema(description = "主存储uuid")
    private String primaryStorageUuid;

    @Schema(description = "主存储名称")
    private String primaryStorageName;

    @Schema(description = "云盘快照类型")
    private String type;

    @Schema(description = "云盘类型")
    private String volumeType;

    @Schema(description = "是否最新")
    private String latest;

    @Schema(description = "查询条件")
    private String queryData;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    @Schema(description = "云盘快照大小")
    private Long size;

    @Schema(description = "云盘快照状态")
    private String status;

    @Schema(description = "云盘快照创建时间")
    private Date vCreateDate;

    @Schema(description = "云盘快照更新时间")
    private Date vUpdateDate;

    @Schema(description = "云盘快照安装路径")
    private String installPath;

    @Schema(description = "云盘快照格式")
    private String format;

    @Schema(description = "是否包含内存快照")
    private Boolean isMemory;

    @Schema(required = false,description = "开始时间",example = "2023-06-07")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String startTime;

    @Schema(required = false,description = "结束时间",example = "2023-06-07")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String endTime;

    @Schema(required = false,description = "主机tagId逗号拼接,1,2,3")
    private String tagIds;

    @Schema(required = false,description = "主机id逗号拼接,1,2,3")
    private List<Long> ids;

}
