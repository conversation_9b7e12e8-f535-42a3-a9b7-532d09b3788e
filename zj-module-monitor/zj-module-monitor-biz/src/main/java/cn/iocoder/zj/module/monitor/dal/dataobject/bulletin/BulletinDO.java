package cn.iocoder.zj.module.monitor.dal.dataobject.bulletin;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 实时报 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_bulletin")
@KeySequence("monitor_bulletin_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BulletinDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * 描述
     */
    private String description;
    /**
     * 监控资产类型
     */
    private String category;
    /**
     * 监控资产小类型
     */
    private String app;
    /**
     * 监控选项
     */
    private String fields;
    /**
     * 资产ids
     */
    private String assetIds;

}
