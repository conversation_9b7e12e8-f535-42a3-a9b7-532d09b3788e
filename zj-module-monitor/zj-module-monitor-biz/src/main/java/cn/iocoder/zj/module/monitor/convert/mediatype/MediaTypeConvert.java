package cn.iocoder.zj.module.monitor.convert.mediatype;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.mediatype.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.mediatype.MediaTypeDO;

/**
 * 介质类型 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface MediaTypeConvert {

    MediaTypeConvert INSTANCE = Mappers.getMapper(MediaTypeConvert.class);

    MediaTypeDO convert(MediaTypeCreateReqVO bean);

    MediaTypeDO convert(MediaTypeUpdateReqVO bean);

    MediaTypeRespVO convert(MediaTypeDO bean);

    List<MediaTypeRespVO> convertList(List<MediaTypeDO> list);

    PageResult<MediaTypeRespVO> convertPage(PageResult<MediaTypeDO> page);

    List<MediaTypeExcelVO> convertList02(List<MediaTypeDO> list);

}
