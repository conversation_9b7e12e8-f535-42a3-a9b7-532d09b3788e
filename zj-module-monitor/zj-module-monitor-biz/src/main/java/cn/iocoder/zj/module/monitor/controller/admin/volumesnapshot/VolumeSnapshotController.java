package cn.iocoder.zj.module.monitor.controller.admin.volumesnapshot;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeSnapshotDTO;
import cn.iocoder.zj.module.monitor.controller.admin.taggables.vo.TaggablesExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.volumeinfo.vo.VolumeInfoRespVO;
import cn.iocoder.zj.module.monitor.convert.tags.TagsConvert;
import cn.iocoder.zj.module.monitor.convert.volume.VolumeInfoConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.taggables.TaggablesDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.tags.TagsDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.volumeinfo.VolumeInfoDO;
import cn.iocoder.zj.module.monitor.service.taggables.TaggablesService;
import cn.iocoder.zj.module.monitor.service.taggables.enums.TagAssetTypeEnum;
import cn.iocoder.zj.module.monitor.service.tags.TagsService;
import cn.iocoder.zj.module.monitor.util.StringUtil;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;
import static java.util.Collections.singleton;

import cn.iocoder.zj.module.monitor.controller.admin.volumesnapshot.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.volumesnapshot.VolumeSnapshotDO;
import cn.iocoder.zj.module.monitor.convert.volumesnapshot.VolumeSnapshotConvert;
import cn.iocoder.zj.module.monitor.service.volumesnapshot.VolumeSnapshotService;

@Tag(name = "管理后台 - 云盘快照信息")
@RestController
@RequestMapping("/monitor/volume-snapshot")
@Validated
public class VolumeSnapshotController {

    @Resource
    private VolumeSnapshotService volumeSnapshotService;

    @Resource
    private RoleApi roleApi;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private TagsService tagsService;

    @Resource
    private TaggablesService taggablesService;

    @PostMapping("/create")
    @Operation(summary = "创建云盘快照信息")
    @TenantIgnore
    //@PreAuthorize("@ss.hasPermission('monitor:volume-snapshot:create')")
    @OperateLog(type = CREATE)
    public CommonResult<Long> createVolumeSnapshot(@Valid @RequestBody VolumeSnapshotCreateReqVO createReqVO) {
        return success(volumeSnapshotService.createVolumeSnapshot(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新云盘快照信息")
    @TenantIgnore
    //@PreAuthorize("@ss.hasPermission('monitor:volume-snapshot:update')")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateVolumeSnapshot(@Valid @RequestBody VolumeSnapshotUpdateReqVO updateReqVO) {
        volumeSnapshotService.updateVolumeSnapshot(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除云盘快照信息")
    @Parameter(name = "id", description = "编号", required = true)
    @TenantIgnore
    //@PreAuthorize("@ss.hasPermission('monitor:volume-snapshot:delete')")
    @OperateLog(type = DELETE)
    public CommonResult<Boolean> deleteVolumeSnapshot(@RequestParam("id") Long id) {
        volumeSnapshotService.deleteVolumeSnapshot(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得云盘快照信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @TenantIgnore
    public CommonResult<VolumeSnapshotRespVO> getVolumeSnapshot(@RequestParam("id") Long id) {
        VolumeSnapshotDO volumeSnapshot = volumeSnapshotService.getVolumeSnapshot(id);
        VolumeSnapshotRespVO respVO = VolumeSnapshotConvert.INSTANCE.convert(volumeSnapshot);
        //标签
        List<TagsDO> tag = tagsService.getTagListByTagGables(new TaggablesExportReqVO()
                .setTaggableId(respVO.getId())
                .setTaggableType(TagAssetTypeEnum.SNAPSHOT.getCode())
        );
        respVO.setTags(TagsConvert.INSTANCE.convertList(tag));
        return success(respVO);
    }

    @GetMapping("/list")
    @Operation(summary = "获得云盘快照信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @TenantIgnore
    public CommonResult<List<VolumeSnapshotRespVO>> getVolumeSnapshotList(@RequestParam("ids") Collection<Long> ids) {
        List<VolumeSnapshotDO> list = volumeSnapshotService.getVolumeSnapshotList(ids);
        return success(VolumeSnapshotConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得云盘快照信息分页")
    @TenantIgnore
    public CommonResult<PageResult<VolumeSnapshotRespVO>> getVolumeSnapshotPage(@Valid VolumeSnapshotPageReqVO pageVO) {
        if (ObjectUtil.isNotEmpty(pageVO.getTagIds())) {
            List<TaggablesDO> tmp = taggablesService.getTaggablesList(new TaggablesExportReqVO()
                    .setTaggableType(TagAssetTypeEnum.SNAPSHOT.getCode())
                    .setTagId(Long.valueOf(pageVO.getTagIds()))
            );

            if (!tmp.isEmpty()) {
                pageVO.setIds(tmp.stream().map(TaggablesDO::getTaggableId).distinct().toList());
            } else {
                return success(PageResult.empty());
            }
        }
        PageResult<VolumeSnapshotDO> pageResult = volumeSnapshotService.getVolumeSnapshotPage(pageVO);

        List<VolumeSnapshotRespVO> infoRespVOS = new ArrayList<>();
        if (pageResult.getList() != null && !pageResult.getList().isEmpty()) {
            infoRespVOS = BeanUtil.copyToList(pageResult.getList(), VolumeSnapshotRespVO.class);
        }
        for (VolumeSnapshotRespVO respVO : infoRespVOS) {
            List<TagsDO> tag = tagsService.getTagListByTagGables(new TaggablesExportReqVO()
                    .setTaggableId(respVO.getId())
                    .setTaggableType(TagAssetTypeEnum.SNAPSHOT.getCode())
            );
            respVO.setTags(TagsConvert.INSTANCE.convertList(tag));
        }
        PageResult<VolumeSnapshotRespVO> pageResults = new PageResult<>();
        pageResults.setList(infoRespVOS);
        pageResults.setTotal(pageResult.getTotal());
        return success(pageResults);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出云盘快照信息 Excel")
    @OperateLog(type = EXPORT)
    @TenantIgnore
    public void exportVolumeSnapshotExcel(@Valid VolumeSnapshotExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        if (ObjectUtil.isNotEmpty(exportReqVO.getTagIds())) {
            List<TaggablesDO> tmp = taggablesService.getTaggablesList(new TaggablesExportReqVO()
                    .setTaggableType(TagAssetTypeEnum.SNAPSHOT.getCode())
                    .setTagId(Long.valueOf(exportReqVO.getTagIds()))
            );

            if (!tmp.isEmpty()) {
                exportReqVO.setIds(tmp.stream().map(TaggablesDO::getTaggableId).distinct().toList());
            } else {
                return;
            }
        }
        List<VolumeSnapshotDO> list = volumeSnapshotService.getVolumeSnapshotList(exportReqVO);
        // 导出 Excel
        List<VolumeSnapshotExcelVO> datas = VolumeSnapshotConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "云盘快照信息.xls", "数据", VolumeSnapshotExcelVO.class, datas);
    }

    @GetMapping("/snapshot-status-count")
    @Operation(summary = "获取云盘快照状态数量")
    @TenantIgnore
    //@PreAuthorize("@ss.hasPermission('monitor:volume-snapshot:query')")
    public CommonResult<Map<String, Object>> getVolumeSnapshotStatusCount(@RequestParam(required = false, defaultValue = "") Long platformId) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(loginUser.getId()).getData();
        List<String> tenantIds = new ArrayList<>();
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            if(StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
                tenantIds = Arrays.asList(String.valueOf(adminUserRespDTO.getTenantId()).split(","));
            }else {
                tenantIds = Arrays.asList(adminUserRespDTO.getServiceTenantId().split(","));
            }
        }
        return CommonResult.success(volumeSnapshotService.getVolumeSnapshotStatusCount(tenantIds,platformId));
    }
}
