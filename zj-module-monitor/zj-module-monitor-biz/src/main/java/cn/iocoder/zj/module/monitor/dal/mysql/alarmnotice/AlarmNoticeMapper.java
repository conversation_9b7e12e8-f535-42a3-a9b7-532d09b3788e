package cn.iocoder.zj.module.monitor.dal.mysql.alarmnotice;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmnotice.AlarmNoticeDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.monitor.controller.admin.alarmnotice.vo.*;

/**
 * 告警与通知模板关系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AlarmNoticeMapper extends BaseMapperX<AlarmNoticeDO> {

    default PageResult<AlarmNoticeDO> selectPage(AlarmNoticePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AlarmNoticeDO>()
                .eqIfPresent(AlarmNoticeDO::getAlarmConfigId, reqVO.getAlarmConfigId())
                .eqIfPresent(AlarmNoticeDO::getTemplateId, reqVO.getTemplateId())
                .eqIfPresent(AlarmNoticeDO::getTemplateType, reqVO.getTemplateType())
                .eqIfPresent(AlarmNoticeDO::getMail, reqVO.getMail())
                .orderByDesc(AlarmNoticeDO::getId));
    }

    default List<AlarmNoticeDO> selectList(AlarmNoticeExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AlarmNoticeDO>()
                .eqIfPresent(AlarmNoticeDO::getAlarmConfigId, reqVO.getAlarmConfigId())
                .eqIfPresent(AlarmNoticeDO::getTemplateId, reqVO.getTemplateId())
                .eqIfPresent(AlarmNoticeDO::getTemplateType, reqVO.getTemplateType())
                .eqIfPresent(AlarmNoticeDO::getMail, reqVO.getMail())
                .orderByDesc(AlarmNoticeDO::getId));
    }

}
