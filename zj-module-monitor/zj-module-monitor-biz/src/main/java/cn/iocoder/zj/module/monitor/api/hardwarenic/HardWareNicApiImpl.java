package cn.iocoder.zj.module.monitor.api.hardwarenic;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.convert.hardwarenic.HardwareNicConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.hardwarenic.HardwareNicDO;
import cn.iocoder.zj.module.monitor.service.hardwarenic.HardwareNicService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.util.List;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class HardWareNicApiImpl implements HardWareNicApi{

    @Resource
    private HardwareNicService hardwareNicService;

    @Override
    public CommonResult<Boolean> adds(List<HardWareNicRespDTO> reqDTO) {

        List<HardwareNicDO> list = HardwareNicConvert.INSTANCE.convertCreateList(reqDTO);
        hardwareNicService.createHardwareNicList(list);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> updates(List<HardWareNicRespDTO> reqDTO) {
        if (reqDTO.size()>0){
            List<HardwareNicDO> list = HardwareNicConvert.INSTANCE.convertCreateList(reqDTO);
            hardwareNicService.updateHardwareNicInfoList(list);
        }
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<List<HardWareNicRespDTO>> getHardwareNicByPlatformId(Long platformId) {
        return CommonResult.success(hardwareNicService.getHardwareNicByPlatformId(platformId));
    }

    @Override
    public int deletes(List<HardWareNicRespDTO> reqDTO) {
        List<HardwareNicDO> list = HardwareNicConvert.INSTANCE.convertCreateList(reqDTO);
        return hardwareNicService.deleteHardwareNicList(list);
    }
}
