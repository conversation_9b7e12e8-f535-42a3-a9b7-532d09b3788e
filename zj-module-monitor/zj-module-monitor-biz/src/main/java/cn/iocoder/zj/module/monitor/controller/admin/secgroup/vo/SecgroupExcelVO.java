package cn.iocoder.zj.module.monitor.controller.admin.secgroup.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 安全组 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SecgroupExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("uuid")
    private String uuid;

    @ExcelProperty("名称")
    private String name;

    @ExcelProperty("状态")
    private String status;

    @ExcelProperty("是否共享")
    private Boolean isPublic;

    @ExcelProperty("默认共享范围")
    private String publicScope;

    @ExcelProperty("共享设置的来源, local: 本地设置, cloud: 从云上同步过来")
    private String publicSrc;

    @ExcelProperty("是否垃圾")
    private Boolean isDirty;

    @ExcelProperty("区域id")
    private String cloudregionId;

    @ExcelProperty("vpcId")
    private String vpcId;

    @ExcelProperty("全局vpcId")
    private String globalvpcId;

    @ExcelProperty("备注")
    private String description;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("平台id")
    private Long platformId;

    @ExcelProperty("平台名称")
    private String platformName;

}
