package cn.iocoder.zj.module.monitor.service.bulletin;

import java.io.IOException;
import java.util.*;
import javax.servlet.http.HttpServletResponse;
import javax.validation.*;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.bulletin.BulletinDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.monitor.service.bulletin.dto.BulletinFieldItemDto;

/**
 * 实时报 Service 接口
 *
 * <AUTHOR>
 */
public interface BulletinService {

    /**
     * 创建实时报
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createBulletin(@Valid BulletinCreateReqVO createReqVO);

    /**
     * 更新实时报
     *
     * @param updateReqVO 更新信息
     */
    void updateBulletin(@Valid BulletinUpdateReqVO updateReqVO);

    /**
     * 删除实时报
     *
     * @param id 编号
     */
    void deleteBulletin(Long id);

    /**
     * 获得实时报
     *
     * @param id 编号
     * @return 实时报
     */
    BulletinDO getBulletin(Long id);

    /**
     * 获得实时报列表
     *
     * @param ids 编号
     * @return 实时报列表
     */
    List<BulletinDO> getBulletinList(Collection<Long> ids);

    /**
     * 获得实时报分页
     *
     * @param pageReqVO 分页查询
     * @return 实时报分页
     */
    PageResult<BulletinDO> getBulletinPage(BulletinPageReqVO pageReqVO);

    /**
     * 获得实时报列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 实时报列表
     */
    List<BulletinDO> getBulletinList(BulletinExportReqVO exportReqVO);

    List<Map<String, Object>> getAppHierarchy();

    List<Object> getDetailList(BulletinDO info);

    PageResult<Object> getDetailPage(BulletinDO info, Integer pageSize,Integer pageNo);

    List<BulletinFieldItemDto> getAppBulletinFieldList(String app);

    void exportDetailExcel(BulletinDO bulletin, HttpServletResponse response) throws IOException;
}
