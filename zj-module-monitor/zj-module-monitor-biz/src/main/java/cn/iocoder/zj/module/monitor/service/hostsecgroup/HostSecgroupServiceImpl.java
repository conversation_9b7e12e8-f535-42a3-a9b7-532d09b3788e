package cn.iocoder.zj.module.monitor.service.hostsecgroup;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.monitor.controller.admin.hostsecgroup.vo.HostSecgroupCreateReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.hostsecgroup.vo.HostSecgroupExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.hostsecgroup.vo.HostSecgroupPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.hostsecgroup.vo.HostSecgroupUpdateReqVO;
import cn.iocoder.zj.module.monitor.convert.hostsecgroup.HostSecgroupConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostsecgroup.HostSecgroupDO;
import cn.iocoder.zj.module.monitor.dal.mysql.hostsecgroup.HostSecgroupMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * 安全组关联云主机 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class HostSecgroupServiceImpl implements HostSecgroupService {

    @Resource
    private HostSecgroupMapper hostSecgroupMapper;

    @Override
    public Long createHostSecgroup(HostSecgroupCreateReqVO createReqVO) {
        // 插入
        HostSecgroupDO hostSecgroup = HostSecgroupConvert.INSTANCE.convert(createReqVO);
        hostSecgroupMapper.insert(hostSecgroup);
        // 返回
        return hostSecgroup.getId();
    }

    @Override
    public void updateHostSecgroup(HostSecgroupUpdateReqVO updateReqVO) {
        // 校验存在
        validateHostSecgroupExists(updateReqVO.getId());
        // 更新
        HostSecgroupDO updateObj = HostSecgroupConvert.INSTANCE.convert(updateReqVO);
        hostSecgroupMapper.updateById(updateObj);
    }

    @Override
    public void deleteHostSecgroup(Long id) {
        // 校验存在
        validateHostSecgroupExists(id);
        // 删除
        hostSecgroupMapper.deleteById(id);
    }

    private void validateHostSecgroupExists(Long id) {
//        if (hostSecgroupMapper.selectById(id) == null) {
//            throw exception(HOST_SECGROUP_NOT_EXISTS);
//        }
    }

    @Override
    public HostSecgroupDO getHostSecgroup(Long id) {
        return hostSecgroupMapper.selectById(id);
    }

    @Override
    public List<HostSecgroupDO> getHostSecgroupList(Collection<Long> ids) {
        return hostSecgroupMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<HostSecgroupDO> getHostSecgroupPage(HostSecgroupPageReqVO pageReqVO) {
        return hostSecgroupMapper.selectPage(pageReqVO);
    }

    @Override
    public List<HostSecgroupDO> getHostSecgroupList(HostSecgroupExportReqVO exportReqVO) {
        return hostSecgroupMapper.selectList(exportReqVO);
    }

    @Override
    public void createHostSecgroupList(List<HostSecgroupDO> list) {
        hostSecgroupMapper.insertBatch(list);
    }

    @Override
    public void updateHostSecgroups(List<HostSecgroupDO> list) {
        hostSecgroupMapper.updateBatch(list);
    }

    @Override
    public void deleteHostSecgroups(List<HostSecgroupDO> list) {
        List<Long> ids = list.stream().map(HostSecgroupDO::getId).toList();
        hostSecgroupMapper.deleteBatchIds(ids);
    }

}
