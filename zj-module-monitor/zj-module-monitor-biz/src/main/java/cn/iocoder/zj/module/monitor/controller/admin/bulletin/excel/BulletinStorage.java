package cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel;

import cn.iocoder.zj.framework.excel.core.convert.ByteConvent;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.convert.BulletinMediaTypeConvert;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.convert.BulletinPercentConvert;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.convert.BulletinStateConvert;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.convert.BulletinStorageStatusConvert;
import cn.iocoder.zj.module.monitor.controller.admin.tags.vo.TagsRespVO;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ExcelIgnoreUnannotated
public class BulletinStorage {

    @ExcelProperty("云存储名称")
    private String name;

    @ExcelProperty("平台名称")
    private String platformName;

    @ExcelProperty("URL")
    private String url;

    @ExcelProperty(value = "启用状态", converter = BulletinStateConvert.class)
    private String state;

    @ExcelProperty(value = "就绪状态", converter = BulletinStorageStatusConvert.class)
    private String status;

    @ExcelProperty("类型")
    private String type;

    @ExcelProperty(value = "物理使用率",converter = BulletinPercentConvert.class)
    private BigDecimal capacityUtilization;

    @ExcelProperty(value = "置备使用率",converter = BulletinPercentConvert.class)
    private BigDecimal virtualUtilization;

    @ExcelProperty(value = "总容量", converter = ByteConvent.class)
    private BigDecimal totalCapacity;

    @ExcelProperty("宿主机")
    private Integer hardwareCount;

    @ExcelProperty("云盘")
    private Integer volumeCount;

    @Schema(description = "标签信息")
    private List<TagsRespVO> tags;

    @ExcelProperty("标签")
    private String tagsStr;

    @ExcelProperty(value = "介质类型", converter = BulletinMediaTypeConvert.class)
    private String mediaType;

    @ExcelProperty("区域")
    private String manager;

    @ExcelProperty("可用区域")
    private String availableManager;

    @ExcelProperty("超售比")
    private BigDecimal storagePercent;

    @ExcelProperty("分配率")
    private BigDecimal commitRate;

    @ExcelProperty(value = "分配", converter = ByteConvent.class)
    private BigDecimal allocation;

    @ExcelProperty(value = "预留", converter = ByteConvent.class)
    private BigDecimal reserveCapacity;

    @ExcelProperty(value = "浪费", converter = ByteConvent.class)
    private BigDecimal wasteCapacity;
}
