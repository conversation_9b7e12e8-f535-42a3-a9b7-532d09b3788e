package cn.iocoder.zj.module.monitor.controller.admin.storageinfo.vo;

import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 存储设备信息 Excel 导出 Request VO，参数和 StorageInfoPageReqVO 是一致的")
@Data
public class StorageInfoExportReqVO {

    @Schema(description = "存储名称")
    private String name;

    @Schema(description = "存储uuid")
    private String uuid;

    @Schema(description = "url")
    private String url;

    @Schema(description = "状态")
    private String state;

    @Schema(description = "类型：Ceph")
    private String type;

    @Schema(description = "状态，页面展示值（Connected：已连接，DisConnected: 未连接）")
    private String status;

    @Schema(description = "容量使用率")
    private BigDecimal capacityUtilization;

    @Schema(description = "总容量")
    private BigDecimal totalCapacity;

    @Schema(description = "平台ID")
    private Long platformId;

    @Schema(description = "平台名称")
    private Long platformName;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "地区id")
    private Long regionId;

    @Schema(description = "租户ID")
    private List<String> tenantId;

    @Schema(required = false,description = "主机过滤ids逗号拼接,'1','2','3'")
    private String ids;

    @Schema(required = false,description = "主机tagId逗号拼接,1,2,3")
    private String tagIds;
}
