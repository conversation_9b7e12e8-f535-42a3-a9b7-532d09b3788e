package cn.iocoder.zj.module.monitor.controller.admin.alarmconfigtemplate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 告警配置模板 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class AlarmConfigTemplateBaseVO {

    @Schema(description = "消息内容")
    private String context;

    @Schema(description = "告警名称")
    private String alarmName;

    @Schema(description = "告警简介")
    private String description;

    @Schema(description = "资源类型，host云主机；hardware物理机；storage存储；image镜像")
    private String sourceType;

    @Schema(description = "字典名称")
    private String dictLabelName;

    @Schema(description = "字典类型")
    private String dictLabelType;

    @Schema(description = "字典值")
    private String dictLabelValue;

    @Schema(description = "触发规则")
    private String alarmRule;

    @Schema(description = "告警阈值")
    private Integer alarmVal;

    @Schema(description = "告警条目单位")
    private String unit;

    @Schema(description = "触发次数")
    private Long alarmTime;

    @Schema(description = "告警级别：1提示，2警告，3严重")
    private Integer alarmLevel;

    @Schema(description = "告警条目单位的字典项code")
    private String unitType;

}
