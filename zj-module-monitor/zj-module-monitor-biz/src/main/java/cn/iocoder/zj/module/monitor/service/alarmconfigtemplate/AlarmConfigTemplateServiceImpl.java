package cn.iocoder.zj.module.monitor.service.alarmconfigtemplate;

import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.zj.module.monitor.controller.admin.alarmconfigtemplate.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmconfigtemplate.AlarmConfigTemplateDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.convert.alarmconfigtemplate.AlarmConfigTemplateConvert;
import cn.iocoder.zj.module.monitor.dal.mysql.alarmconfigtemplate.AlarmConfigTemplateMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.*;

/**
 * 告警配置模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AlarmConfigTemplateServiceImpl implements AlarmConfigTemplateService {

    @Resource
    private AlarmConfigTemplateMapper alarmConfigTemplateMapper;

    @Override
    @TenantIgnore
    public Long createAlarmConfigTemplate(AlarmConfigTemplateCreateReqVO createReqVO) {
        // 插入
        AlarmConfigTemplateDO alarmConfigTemplate = AlarmConfigTemplateConvert.INSTANCE.convert(createReqVO);
        alarmConfigTemplateMapper.insert(alarmConfigTemplate);
        // 返回
        return alarmConfigTemplate.getId();
    }

    @Override
    @TenantIgnore
    public void updateAlarmConfigTemplate(AlarmConfigTemplateUpdateReqVO updateReqVO) {
        // 校验存在
        validateAlarmConfigTemplateExists(updateReqVO.getId());
        // 更新
        AlarmConfigTemplateDO updateObj = AlarmConfigTemplateConvert.INSTANCE.convert(updateReqVO);
        alarmConfigTemplateMapper.updateById(updateObj);
    }

    @Override
    @TenantIgnore
    public void deleteAlarmConfigTemplate(Long id) {
        // 校验存在
        validateAlarmConfigTemplateExists(id);
        // 删除
        alarmConfigTemplateMapper.deleteById(id);
    }

    private void validateAlarmConfigTemplateExists(Long id) {
        if (alarmConfigTemplateMapper.selectById(id) == null) {
//            throw exception(ALARM_CONFIG_TEMPLATE_NOT_EXISTS);
        }
    }

    @Override
    @TenantIgnore
    public AlarmConfigTemplateDO getAlarmConfigTemplate(Long id) {
        return alarmConfigTemplateMapper.selectById(id);
    }

    @Override
    @TenantIgnore
    public List<AlarmConfigTemplateDO> getAlarmConfigTemplateList(Collection<Long> ids) {
        return alarmConfigTemplateMapper.selectBatchIds(ids);
    }

    @Override
    @TenantIgnore
    public PageResult<AlarmConfigTemplateDO> getAlarmConfigTemplatePage(AlarmConfigTemplatePageReqVO pageReqVO) {
        return alarmConfigTemplateMapper.selectPage(pageReqVO);
    }

    @Override
    @TenantIgnore
    public List<AlarmConfigTemplateDO> getAlarmConfigTemplateList(AlarmConfigTemplateExportReqVO exportReqVO) {
        return alarmConfigTemplateMapper.selectList(exportReqVO);
    }

}
