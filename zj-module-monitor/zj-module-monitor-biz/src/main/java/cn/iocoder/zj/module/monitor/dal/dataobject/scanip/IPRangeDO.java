package cn.iocoder.zj.module.monitor.dal.dataobject.scanip;

import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.util.List;

/**
 * IP段 DO
 */
@TableName("ip_range")
@KeySequence("ip_range") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class IPRangeDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;

    /**
     * 租户编号
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * IP段名称
     */
    private String name;

    /**
     * IP段范围，支持单个IP、IP段、IP范围，多个用逗号分隔
     */
    private String ipRanges;

    /**
     * 状态
     *
     */
    private Integer status;

    /**
     * SNMP端口
     */
    private Integer snmpPort;

    /**
     * SNMP团体名
     */
    private String snmpCommunity;

    private String snmpVersion;

    /**
     * TCP端口
     */
    private String tcpPort;

    /**
     * 备注
     */
    private String remark;

    /**
     * Ping支持
     *
     * 0 - 不支持
     * 1 - 支持
     */
    private Integer pingSupport;

    /**
     * SNMP支持
     *
     * 0 - 不支持
     * 1 - 支持
     */
    private Integer snmpSupport;

    /**
     * TCP支持
     *
     * 0 - 不支持
     * 1 - 支持
     */
    private Integer tcpSupport;

    private Integer state;

    private String taskId;

    private String cronExp;

    private String taskCycle;

    @TableField(exist = false)
    private Integer succCount;

    @TableField(exist = false)
    private List<Integer> tcps;

    private Long platformId;

    private String platformName;
}