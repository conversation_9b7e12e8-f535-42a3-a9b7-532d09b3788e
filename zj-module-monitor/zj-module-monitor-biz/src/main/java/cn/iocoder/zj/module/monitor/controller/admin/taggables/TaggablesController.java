package cn.iocoder.zj.module.monitor.controller.admin.taggables;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.monitor.controller.admin.taggables.vo.*;
import cn.iocoder.zj.module.monitor.convert.taggables.TaggablesConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.taggables.TaggablesDO;
import cn.iocoder.zj.module.monitor.dal.mysql.taggables.TaggablesMapper;
import cn.iocoder.zj.module.monitor.service.taggables.TaggablesService;
import cn.iocoder.zj.module.monitor.service.taggables.enums.TagAssetTypeEnum;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.TAGS_NOT_DEL;

@Tag(name = "管理后台 - 标签绑定关系")
@RestController
@RequestMapping("/monitor/taggables")
@Validated
public class TaggablesController {

    @Resource
    private TaggablesService taggablesService;
    @Resource
    private TaggablesMapper taggablesMapper;

    @Resource
    private PlatformconfigApi platformconfigApi;

    @PostMapping("/create")
    @Operation(summary = "创建标签绑定关系")
//    @PreAuthorize("@ss.hasPermission('monitor:taggables:create')")
    public CommonResult<Long> createTaggables(@Valid @RequestBody TaggablesCreateReqVO createReqVO) {
        return success(taggablesService.createTaggables(createReqVO));
    }

    @PostMapping("/batch-create")
    @Operation(summary = "批量创建标签绑定关系")
//    @PreAuthorize("@ss.hasPermission('monitor:taggables:create')")
    public CommonResult<Boolean> batchCreateTaggables(@RequestBody TaggablesBatchCreateReqVO createReqVO) {
        return success(taggablesService.batchCreateTaggables(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新标签绑定关系")
    public CommonResult<Boolean> updateTaggables(@Valid @RequestBody TaggablesUpdateReqVO updateReqVO) {
        taggablesService.updateTaggables(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除标签绑定关系")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('monitor:taggables:delete')")
    public CommonResult<Boolean> deleteTaggables(@RequestParam("id") Long id) {
        taggablesService.deleteTaggables(id);
        return success(true);
    }

    @DeleteMapping("/unbind")
    @Operation(summary = "解绑标签绑定关系")
//    @PreAuthorize("@ss.hasPermission('monitor:taggables:delete')")
    public CommonResult<Boolean> unbindTaggables(@RequestParam("tagId") Long tagId, @RequestParam("taggableId") Long taggableId, @RequestParam("taggableType") String taggableType) {
        LambdaQueryWrapper<TaggablesDO> query = new LambdaQueryWrapper<TaggablesDO>()
                .eq(TaggablesDO::getTaggableId, taggableId)
                .eq(TaggablesDO::getTagId, tagId)
                .eq(TaggablesDO::getTaggableType, taggableType);
        TaggablesDO find = taggablesMapper.selectOne(query);
        if (find != null) {
            if(find.getType() == 1){
                throw exception(TAGS_NOT_DEL);
            }
            taggablesService.deleteTaggables(find.getId());
        }
        return success(true);
    }


    @DeleteMapping("/batch-delete")
    @Operation(summary = "批量删除标签绑定关系")
    @Parameter(name = "ids", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('monitor:taggables:delete')")
    public CommonResult<Boolean> batchDeleteTaggables(@RequestParam("ids") String ids) {
        String[] idArray = ids.split(",");
        for (String s : idArray) {
            taggablesService.deleteTaggables(Long.parseLong(s));
        }
        return success(true);
    }
    @GetMapping("/get")
    @Operation(summary = "获得标签绑定关系")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<TaggablesRespVO> getTaggables(@RequestParam("id") Long id) {
        TaggablesDO taggables = taggablesService.getTaggables(id);
        return success(TaggablesConvert.INSTANCE.convert(taggables));
    }

    @GetMapping("/list")
    @Operation(summary = "获得标签绑定关系列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
//    @PreAuthorize("@ss.hasPermission('monitor:taggables:query')")
    public CommonResult<List<TaggablesRespVO>> getTaggablesList(@RequestParam("ids") Collection<Long> ids) {
        List<TaggablesDO> list = taggablesService.getTaggablesList(ids);
        return success(TaggablesConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得标签绑定关系分页")
//    @PreAuthorize("@ss.hasPermission('monitor:taggables:query')")
    public CommonResult<PageResult<TaggablesRespVO>> getTaggablesPage(@Valid TaggablesPageReqVO pageVO) {
        if (pageVO.getTaggableType() == null) {
            return success(PageResult.empty());
        }
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        List<Map> platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
        StringBuilder inplatform = new StringBuilder("0");
        if (!platform.isEmpty()){
            for (Map map : platform){
                inplatform.append(",").append(map.get("platformId").toString());
            }
        }
        //循环TagAssetTypeEnum
        for (TagAssetTypeEnum value : TagAssetTypeEnum.values()) {
            if (value.getCode().equals(pageVO.getTaggableType())) {
                if (pageVO.getTaggableName() !=  null && !pageVO.getTaggableName().isEmpty()) {
                    List<Map<String, Object>> selectAssetByName = taggablesMapper.selectAssetByName(value.getTable(), pageVO.getTaggableName(), inplatform.toString(), value.getField(), value.getCode());
                    List<Long> ids = selectAssetByName.stream().map(map -> Long.valueOf(map.get("id").toString())).toList();
                    if (ids.isEmpty()) {
                        return success(PageResult.empty());
                    }
                    pageVO.setTaggableIds(ids);
                }
                PageResult<TaggablesDO> pageResult = taggablesService.getTaggablesPage(pageVO);
                PageResult<TaggablesRespVO> resp = TaggablesConvert.INSTANCE.convertPage(pageResult);
                PageResult<TaggablesRespVO> res = PageResult.empty();
                Long oldTotal = resp.getTotal();
                for (TaggablesRespVO taggablesRespVO : resp.getList()) {
                    Map<String, Object> a = taggablesMapper.selectRelationById(value.getTable(), taggablesRespVO.getTaggableId(), value.getField());
                    if (a != null) {
                        taggablesRespVO.setRelation(a);
                        res.getList().add(taggablesRespVO);
                    } else {
                        oldTotal -= 1;
                        taggablesMapper.deleteById(taggablesRespVO.getId());
                    }
                }
                res.setTotal(oldTotal);
                return success(res);
            }
        }
        return success(PageResult.empty());
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出标签绑定关系 Excel")
    @PreAuthorize("@ss.hasPermission('monitor:taggables:export')")
    @OperateLog(type = EXPORT)
    public void exportTaggablesExcel(@Valid TaggablesExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<TaggablesDO> list = taggablesService.getTaggablesList(exportReqVO);
        // 导出 Excel
        List<TaggablesExcelVO> datas = TaggablesConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "标签绑定关系.xls", "数据", TaggablesExcelVO.class, datas);
    }


    @GetMapping("/bindable-asset-page")
    @Operation(summary = "可绑定标签资产列表")
//    @PreAuthorize("@ss.hasPermission('monitor:taggables:query')")
    public CommonResult<Object> bindableAssetPage(
            @RequestParam("taggableType") String taggableType,
            @RequestParam("tagIds") String tagIds,
            @RequestParam(value = "pageNo", required = false, defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", required = false,defaultValue = "10") Integer pageSize,
            @RequestParam(value = "taggableName", required = false,defaultValue = "") String taggableName
            ) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        List<Map> platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
        StringBuilder inplatform = new StringBuilder("0");
        if (!platform.isEmpty()){
            for (Map map : platform){
                inplatform.append(",").append(map.get("platformId").toString());
            }
        }
        Map<String, Object> res = new HashMap<>();
        res.put("total",0);
        res.put("list",new ArrayList<>());
        for (TagAssetTypeEnum value : TagAssetTypeEnum.values()) {
            if (value.getCode().equals(taggableType)) {
                List<TaggablesDO> tmp = taggablesMapper.selectList(new LambdaQueryWrapper<TaggablesDO>()
                        .eq(TaggablesDO::getTaggableType, taggableType)
                        .in(TaggablesDO::getTagId, Arrays.stream(tagIds.split(",")).map(Long::valueOf).toList())
                );
                String idsStr = "0";
                if (!tmp.isEmpty()) {
                    List<Long> hasids = tmp.stream().map(TaggablesDO::getTaggableId).distinct().toList();
                    idsStr = hasids.stream()
                            .map(String::valueOf)
                            .collect(Collectors.joining(","));
                }
                String limit = (pageNo - 1) * pageSize + "," + pageSize;
                List<Map<String, Object>> list = taggablesMapper.selectAssetPageByNotId(value.getTable(), idsStr,inplatform.toString(), value.getField(),taggableType, limit,taggableName);
                Map<String, Object> count = taggablesMapper.selectAssetListByNotIdCount(value.getTable(), idsStr,inplatform.toString(),taggableType,taggableName);
                res.put("total",count.get("total"));
                res.put("list",list);
            }
        }
        return success(res);
    }
}
