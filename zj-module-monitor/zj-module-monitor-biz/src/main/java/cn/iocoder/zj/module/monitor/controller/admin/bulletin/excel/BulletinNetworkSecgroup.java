package cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel;

import cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.convert.BulletinBoolConvert;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.convert.BulletinEnableStatusConvert;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
@ExcelIgnoreUnannotated
public class BulletinNetworkSecgroup {

    private Long id;
    @ExcelProperty("名称")
    private String name;

    @ExcelProperty("平台名称")
    private String platformName;

    @ExcelProperty(value = "是否共享", converter = BulletinBoolConvert.class)
    private Boolean isPublic;

    @ExcelProperty("默认共享范围")
    private String publicScope;

    @ExcelProperty("共享来源")
    private String publicSrc;

    @ExcelProperty(value = "是否垃圾", converter = BulletinBoolConvert.class)
    private Boolean isDirty;

    @ExcelProperty(value = "状态", converter = BulletinEnableStatusConvert.class)
    private String status;
}
