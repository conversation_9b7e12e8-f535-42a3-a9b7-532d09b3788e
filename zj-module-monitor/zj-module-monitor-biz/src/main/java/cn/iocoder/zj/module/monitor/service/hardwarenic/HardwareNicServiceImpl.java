package cn.iocoder.zj.module.monitor.service.hardwarenic;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.api.hardwarenic.HardWareNicRespDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.zj.module.monitor.controller.admin.hardwarenic.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.hardwarenic.HardwareNicDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.convert.hardwarenic.HardwareNicConvert;
import cn.iocoder.zj.module.monitor.dal.mysql.hardwarenic.HardwareNicMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.*;

/**
 * 物理机网络关系 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class HardwareNicServiceImpl implements HardwareNicService {

    @Resource
    private HardwareNicMapper hardwareNicMapper;

    @Override
    public Long createHardwareNic(HardwareNicCreateReqVO createReqVO) {
        // 插入
        HardwareNicDO hardwareNic = HardwareNicConvert.INSTANCE.convert(createReqVO);
        hardwareNicMapper.insert(hardwareNic);
        // 返回
        return hardwareNic.getId();
    }

    @Override
    public void updateHardwareNic(HardwareNicUpdateReqVO updateReqVO) {
        // 校验存在
        validateHardwareNicExists(updateReqVO.getId());
        // 更新
        HardwareNicDO updateObj = HardwareNicConvert.INSTANCE.convert(updateReqVO);
        hardwareNicMapper.updateById(updateObj);
    }

    @Override
    public void deleteHardwareNic(Long id) {
        // 校验存在
        validateHardwareNicExists(id);
        // 删除
        hardwareNicMapper.deleteById(id);
    }

    private void validateHardwareNicExists(Long id) {
        if (hardwareNicMapper.selectById(id) == null) {
            throw exception(HARDWARE_NIC_NOT_EXISTS);
        }
    }

    @Override
    public HardwareNicDO getHardwareNic(Long id) {
        return hardwareNicMapper.selectById(id);
    }

    @Override
    @TenantIgnore
    public List<HardwareNicDO> getHardwareNicList(HardwareNicReqVO reqVO) {
        LambdaQueryWrapper<HardwareNicDO> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HardwareNicDO::getHardwareUuid,reqVO.getHardwareUuid())
                .like(ObjectUtil.isNotEmpty(reqVO.getMac()),HardwareNicDO::getMac,reqVO.getMac())
                .like(ObjectUtil.isNotEmpty(reqVO.getIpAddresses()),HardwareNicDO::getIpAddresses,reqVO.getIpAddresses())
                .like(ObjectUtil.isNotEmpty(reqVO.getNetworkType()),HardwareNicDO::getNetworkType,reqVO.getNetworkType())
                .like(ObjectUtil.isNotEmpty(reqVO.getIpSubnet()),HardwareNicDO::getIpSubnet,reqVO.getIpSubnet())
                .like(ObjectUtil.isNotEmpty(reqVO.getL2NetworkName()),HardwareNicDO::getL2NetworkName,reqVO.getL2NetworkName())
                .eq(ObjectUtil.isNotEmpty(reqVO.getState()),HardwareNicDO::getState,reqVO.getState())
                .eq(HardwareNicDO::getDeleted,0);
        return hardwareNicMapper.selectList(lqw);
    }

    @Override
    public PageResult<HardwareNicDO> getHardwareNicPage(HardwareNicPageReqVO pageReqVO) {
        return hardwareNicMapper.selectPage(pageReqVO);
    }

    @Override
    public List<HardwareNicDO> getHardwareNicList(HardwareNicExportReqVO exportReqVO) {
        return hardwareNicMapper.selectList(exportReqVO);
    }

    @Override
    public void updateHardwareNicInfoList(List<HardwareNicDO> list) {
        hardwareNicMapper.updateBatch(list);
    }

    @Override
    public void createHardwareNicList(List<HardwareNicDO> list) {
        hardwareNicMapper.insertBatch(list);
    }

    @Override
    public List<HardWareNicRespDTO> getHardwareNicByPlatformId(Long platformId) {
        return hardwareNicMapper.getByPlatformId(platformId);
    }

    @Override
    public int deleteHardwareNicList(List<HardwareNicDO> list) {
        return hardwareNicMapper.deletes(list);
    }

}
