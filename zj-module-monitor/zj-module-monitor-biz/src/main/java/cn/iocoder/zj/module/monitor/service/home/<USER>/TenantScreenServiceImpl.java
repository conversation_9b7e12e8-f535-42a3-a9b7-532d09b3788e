package cn.iocoder.zj.module.monitor.service.home.tenantscreen;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.enums.ResourceCategoryEnum;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.monitor.dal.mysql.alarmconfig.AlarmConfigMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.hardwareinfo.HardwareInfoMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.hostinfo.HostInfoMapper;
import cn.iocoder.zj.module.monitor.service.hardwareinfo.HardwareInfoService;
import cn.iocoder.zj.module.monitor.service.hostinfo.HostInfoService;
import cn.iocoder.zj.module.monitor.service.storageinfo.StorageInfoService;
import cn.iocoder.zj.module.monitor.util.StringUtil;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : TenantScreenServiceImpl  //类名
 * @Description : 租户大屏  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/2/29  9:47
 */
@Slf4j
@Service
public class TenantScreenServiceImpl implements TenantScreenService {
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    PermissionApi permissionApi;
    @Resource
    RoleApi roleApi;
    @Resource
    HostInfoMapper hostInfoMapper;
    @Resource
    HardwareInfoMapper hardwareInfoMapper;
    @Resource
    private HostInfoService hostInfoService;
    @Resource
    private HardwareInfoService hardwareInfoService;
    @Resource
    private StorageInfoService storageInfoService;
    @Resource
    private AlarmConfigMapper alarmConfigMapper;
//    @Resource
//    InfluxDBTemplate influxDBTemplate;

    @Override
    public Map<String, Map<String, Object>> getResourcesList(List<String> tenantIds, Long tenantId, Long platformId) {
//        todo 无平台
        boolean admin = false;
        List<Map> platform = new ArrayList<>();
        List<Map> list = new ArrayList<>();
        // 如果提供了 platformId，则添加当前平台
        if (platformId != null) {
            Map m = new HashMap();
            m.put("platformId", platformId);
            platform.add(m);
            list = hostInfoMapper.getResourcesList(platform);
        } else {
            platform = platformconfigApi.getPlatformSelectList(tenantId.toString()).getData();
            if (CollectionUtil.isNotEmpty(platform)) {
                list = hostInfoMapper.getResourcesList(platform);
            }
        }

        // 初始化结果列表
        List<Map> resultList = new ArrayList<>();
        // 定义一个映射，用于保存每种应用类型的结果
        Map<String, Map<String, Object>> resultMap = new HashMap<>();
        // 定义一个映射，将应用类型映射到其相应的列表
        Map<String, List<String>> appTypeMap = new HashMap<>();
        appTypeMap.put(ResourceCategoryEnum.APP_SERVICE.getCode(), Arrays.asList("website", "port", "ssl_cert", "ping", "websocket", "api", "dns", "fullsite", "api_code", "udp_port"));
        appTypeMap.put(ResourceCategoryEnum.APP_DB.getCode(), Arrays.asList("mysql", "oracle", "sqlserver", "mariadb", "dm", "redis", "redis_cluster", "redis_sentinel", "memcached", "tidb", "nebulaGraph", "opengauss", "mongodb"));
        appTypeMap.put(ResourceCategoryEnum.APP_MID.getCode(), Arrays.asList("nacos", "zookeeper", "kafka", "rabbitmq", "nginx", "pop3", "ftp", "smtp", "ntp", "spring_gateway", "emqx", "activemq", "rocketmq", "shenyu", "rabbitmq", "almalinux", "tomcat"));
        appTypeMap.put(ResourceCategoryEnum.APP_OS.getCode(), Arrays.asList("windows", "linux", "ubuntu", "coreos", "opensuse", "rockylinux", "euleros", "redhat", "centos", "freebsd", "debian", "ipmi", "linux_snmp"));
        appTypeMap.put(ResourceCategoryEnum.APP_CN.getCode(), Arrays.asList("docker", "kubernetes"));
//        appTypeMap.put(ResourceCategoryEnum.APP_PROMETHEUS.getCode(), Collections.singletonList("prometheus"));
        appTypeMap.put(ResourceCategoryEnum.APP_FIREWALL.getCode(), Arrays.asList("qax_firewall", "sswk_firewall", "sswk_defense", "sxf_firewall", "ad_firewall", "ac_firewall", "ah_firewall", "ah_waf", "lm_defense", "lm_waf", "trx_defense", "trx_firewall"));
        appTypeMap.put(ResourceCategoryEnum.APP_NETWORK.getCode(), Arrays.asList("h3c_switch", "cisco_switch", "huawei_switch", "tplink_switch", "hpe_switch"));
        appTypeMap.put(ResourceCategoryEnum.APP_STORED.getCode(), Arrays.asList("storage_huawei", "storage_inspur", "storage_synology"));
        appTypeMap.put(ResourceCategoryEnum.APP_INFRA.getCode(), Arrays.asList("infra_ipmi"));
        // 遍历 appTypeMap
        for (Map.Entry<String, List<String>> entry : appTypeMap.entrySet()) {
            // 初始化计数器
            int other = 0;
            int online = 0;
            int unonline = 0;
            String appType = entry.getKey();
            List<String> apps = entry.getValue();

            // 初始化列表，用于存储当前应用类型的结果
            List<Map<String, Object>> appResultList = new ArrayList<>();

            // 创建一个映射，用于存储当前应用的计数结果
            Map<String, Object> appMap = new HashMap<>();
            // 遍历当前类型的每个应用
            for (String app : apps) {
                // 遍历资源列表
                for (Map map : list) {
                    if (map.get("app").toString().equals(app)) {
                        int status = Integer.parseInt(map.get("status").toString());
                        switch (status) {
                            case 0:
                                other = other + 1;
                                break;
                            case 1:
                                online = online + 1;
                                break;
                            case 2:
                                unonline = unonline + 1;
                                break;
                        }
                    }
                }
                appMap.put("other", other);
                appMap.put("online", online);
                appMap.put("unonline", unonline);
                appMap.put("total", other + online + unonline);

            }
            // 将映射添加到当前应用类型的结果列表中
            appResultList.add(appMap);
            // 将当前应用类型的结果列表添加到 resultMap 中
            resultMap.put(appType, appMap);
        }
        Map<String, Object> hostNum = hostInfoService.getHostStatusCount(tenantIds, platformId);
        Map<String, Object> hardwareNum = hardwareInfoService.getHardwareStatusCount(tenantIds, platformId);
        Map<String, Object> storageNum = storageInfoService.getStorageStatusCount(tenantIds, platformId);
        Map hostMap = new HashMap();
        hostMap.put("other", hostNum.get("hostOtherNum"));
        hostMap.put("online", hostNum.get("hostRunningNum"));
        hostMap.put("unonline", hostNum.get("hostStoppedNum"));
        hostMap.put("total", hostNum.get("hostNum"));

        Map hardMap = new HashMap();
        hardMap.put("other", hardwareNum.get("hardwareConnectingNum"));
        hardMap.put("online", hardwareNum.get("hardwareConnectedNum"));
        hardMap.put("unonline", hardwareNum.get("hardwareDisconnectedNum"));
        hardMap.put("total", hardwareNum.get("hardwareNum"));

        resultMap.put("host", hostMap);
        resultMap.put("hardware", hardMap);
        resultMap.put("storage", storageNum);
        // 将 resultMap 添加到 resultList 中
        resultList.add(resultMap);

        return resultMap;
    }

    @Override
    public List<Map<String, Object>> getScreenAlarm(Long tenantId, Long platformId) {
        //todo 无平台
        boolean admin = false;
        List<Map> platform = new ArrayList<>();
        List<Map> list = new ArrayList<>();
        // 如果提供了 platformId，则添加当前平台
        if (platformId != null) {
            Map m = new HashMap();
            m.put("platformId", platformId);
            platform.add(m);
            list = hostInfoMapper.getResourcesList(platform);
        } else {
            platform = platformconfigApi.getPlatformSelectList(tenantId.toString()).getData();
            if (CollectionUtil.isNotEmpty(platform)) {
                list = hostInfoMapper.getResourcesList(platform);
            }
        }
        List<Map<String, Object>> dataList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(platform)) {
//        // 云平台告警
//        List<Map<String, Object>> cloudAlarms = alarmConfigMapper.getCloudAlarms(platform, 200l);
            // 赫兹平台告警
            List<Map<String, Object>> hzList = alarmConfigMapper.getHzAlarmsList(platform, 200l);

//        for (Map<String, Object> alarm : cloudAlarms) {
//            Map<String, Object> mappedAlarm = mapAlarm(alarm, "source_name", "actual_context", "alarm_level", "platform_name", "create_time");
//            dataList.add(mappedAlarm);
//        }
            for (Map<String, Object> alarm : hzList) {
                Map<String, Object> mappedAlarm = mapAlarm(alarm, "name", "content", "priority", "platform_name", "last_alarm_time");
                dataList.add(mappedAlarm);
            }
            // 自定义比较器，将"createTime"为"近几分钟"的数据排到最上面
            Comparator<Map<String, Object>> comparator = (o1, o2) -> {
                Object createTime1 = o1.get("createTime");
                Object createTime2 = o2.get("createTime");
                // 如果不是"近几分钟"，按时间戳排序
                Long time1 = createTime1 instanceof Long ? (Long) createTime1 : 0;
                Long time2 = createTime2 instanceof Long ? (Long) createTime2 : 0;
                return Long.compare(time2, time1); // 降序排列
            };
            // 对列表进行排序
            Collections.sort(dataList, comparator);
        }
        return dataList;
    }

    @Override
    public Map<String, Object> getScreenStatistics(Long tenantId, Long platformId) {
        // todo 无平台
        List<Map> platform = new ArrayList<>();
        List<Map> list = new ArrayList<>();
        if (platformId != null) {
            Map m = new HashMap();
            m.put("platformId", platformId);
            platform.add(m);
            list = hardwareInfoMapper.getScreenStatistics(platform);
        } else {
            platform = platformconfigApi.getPlatformSelectList(tenantId.toString()).getData();
            if (CollectionUtil.isNotEmpty(platform)) {
                list = hardwareInfoMapper.getScreenStatistics(platform);
            }
        }
        BigDecimal[] sums = list.stream()
                .map(x -> new BigDecimal[]{
                        parseBigDecimal(x.get("cpu_used")),
                        parseBigDecimal(x.get("disk_used")),
                        parseBigDecimal(x.get("memory_used"))
                })
                .reduce(new BigDecimal[]{BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO},
                        (acc, val) -> new BigDecimal[]{
                                acc[0].add(val[0]),
                                acc[1].add(val[1]),
                                acc[2].add(val[2])
                        });

        BigDecimal size = BigDecimal.valueOf(list.size());
        BigDecimal zero = new BigDecimal(0);
        BigDecimal c = size.compareTo(zero) > 0 ? sums[0].divide(size, RoundingMode.HALF_UP) : zero;
        BigDecimal d = size.compareTo(zero) > 0 ? sums[1].divide(size, RoundingMode.HALF_UP) : zero;
        BigDecimal m = size.compareTo(zero) > 0 ? sums[2].divide(size, RoundingMode.HALF_UP) : zero;
        Map<String, Object> map = new HashMap();
        map.put("cpu", c);
        map.put("disk", d);
        map.put("mem", m);
        return map;
    }

    private BigDecimal parseBigDecimal(Object value) {
        if (value instanceof Number) {
            return new BigDecimal(value.toString());
        }
        if (value instanceof String && ((String) value).matches("-?\\d+(\\.\\d+)?")) {
            return new BigDecimal((String) value);
        }
        return BigDecimal.ZERO;
    }

    @Override
    public Map<String, Object> getScreenUsage(Long userId, Long platformId, String time, Long type, Integer status, Long tenantId) {
        LoginUser currentUser = SecurityFrameworkUtils.getLoginUser();
        log.info("如果currentUser 则输出日志{}",currentUser);
        List<Map> platform = new ArrayList<>();
        if (platformId != null) {
            Map m = new HashMap();
            m.put("platformId", platformId);
            platform.add(m);
        } else {
            if (currentUser == null){
                platform = platformconfigApi.getPlatformSelectList(String.valueOf(tenantId)).getData();
            }else {
                platform = platformconfigApi.getPlatformSelectList(String.valueOf(currentUser.getTenantId())).getData();
            }


        }
        Map<String, Object> map = new HashMap();
        map.put("hostCpuTop5", new ArrayList<>());
        map.put("hostMemTop5", new ArrayList<>());
        map.put("hardCpuTop5", new ArrayList<>());
        map.put("hardMemTop5", new ArrayList<>());

        if (platform.size()>0){
            List<Map<String, Object>> vmInfoCpuUsedTopByPlatformId = hostInfoMapper.getVmInfoCpuUsedTopByPlatformId(platform);
            List<Map<String, Object>> vmInfoMemoryUsedTopByPlatformId = hostInfoMapper.getVmInfoMemoryUsedTopByPlatformId(platform);
            List<Map<String, Object>> hostInfoCpuUsedTopByPlatformId1 = hardwareInfoMapper.getHostInfoCpuUsedTopByPlatformId(platform);
            List<Map<String, Object>> hostInfoMemUsedTopByPlatformId = hardwareInfoMapper.getHostInfoMemUsedTopByPlatformId(platform);
            map.put("hostCpuTop5", vmInfoCpuUsedTopByPlatformId);
            map.put("hostMemTop5", vmInfoMemoryUsedTopByPlatformId);
            map.put("hardCpuTop5", hostInfoCpuUsedTopByPlatformId1);
            map.put("hardMemTop5", hostInfoMemUsedTopByPlatformId);
        }
        return map;
    }

    /**
     * 用该方法移除已停止运行的资产
     *
     * @param objectList
     * @param map
     * @return
     */
    private List<Object> removeStoopd(List<Object> objectList, Map map) {
        Iterator<Object> iterator = objectList.iterator();
        //List在for循环中使用remove方法时会出现数组越界的bug
        while (iterator.hasNext()) {
            Object item = iterator.next();
            if (ObjectUtil.isEmpty(map.get(((Map) item).get("uuid")))) {
                iterator.remove();
            }
        }
        return objectList;
    }

    private Map<String, Object> mapAlarm(Map<String, Object> alarm, String nameKey, String contextKey, String levelKey, String platformNameKey, String timeKey) {
        Map<String, Object> mappedAlarm = new HashMap<>();
        //如果 nameKey 包含 JSON，则从 tags 字段中提取 monitorName
        mappedAlarm.put("context", alarm.getOrDefault(contextKey, ""));
        mappedAlarm.put("level", alarm.getOrDefault(levelKey, ""));
        mappedAlarm.put("platformName", alarm.getOrDefault(platformNameKey, ""));
        mappedAlarm.put("name", alarm.getOrDefault(nameKey, ""));
        Object createTimeObj = alarm.get(timeKey);
        if (createTimeObj instanceof LocalDateTime) {
            mappedAlarm.put("createTime", Timestamp.valueOf((LocalDateTime) createTimeObj).getTime());
        } else if (createTimeObj instanceof Long) {
            Long createTimeLong = (Long) createTimeObj;
            mappedAlarm.put("createTime", createTimeLong);
        } else {
            mappedAlarm.put("createTime", createTimeObj);
        }
        return mappedAlarm;
    }

    private String buildPlatformIds(List<Map> platform) {
        return platform.stream()
                .map(str -> StringUtil.toString(str.get("platformId")))
                .collect(Collectors.joining(","));
    }

    private String buildQuery(String tableName, String metricName, String time, String platformIds) {
        // todo 根据1d 数据判断格式化查询当前now
        String p = platformIds.replace(",", "' or platformId = '");
        return String.format("SELECT value, metricName, uuid FROM " +
                "(SELECT mean(value) AS value FROM %s WHERE metricName = '%s' AND time >= now()-%s " +
                "AND (platformId = '" + p + "')" +
                "GROUP BY metricName, uuid)", tableName, metricName, time);
    }


    public String buildYesterQuery(String tableName, String metricName, String platformIds) {
        // Replace commas with the appropriate SQL OR clause for platformId
        String p = platformIds.replace(",", "' OR platformId = '");

        // Get yesterday's date range in UTC format
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfYesterday = now.minusDays(1).toLocalDate().atStartOfDay();
        LocalDateTime startOfToday = now.toLocalDate().atStartOfDay();

        // Format the time to ISO-8601 standard with 'Z' to indicate UTC time
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");
        String startTime = startOfYesterday.format(formatter);
        String endTime = startOfToday.format(formatter);

        // Construct the SQL query string with proper spacing
        return String.format("SELECT value, metricName, uuid FROM " +
                "(SELECT mean(value) AS value FROM %s " +
                "WHERE metricName = '%s' " +
                "AND time >= '%s' " +
                "AND time < '%s' " +
                "AND (platformId = '%s') " +
                "GROUP BY metricName, uuid)", tableName, metricName, startTime, endTime, p);
    }


    public static List<Map<String, Object>> sortAndRetrieveTopFive(List<Object> list) {
        List<Map<String, Object>> topFive = list.stream()
                .map(obj -> (Map<String, Object>) obj)
                .sorted(Comparator.comparingDouble(map -> {
                    // 获取值并处理可能的科学计数法
                    Object valueObj = ((Map<String, Object>) map).get("value");
                    if (valueObj instanceof Double) {
                        return (Double) valueObj;
                    } else if (valueObj instanceof String) {
                        // 如果是字符串形式的科学计数法，转换为Double
                        return Double.parseDouble((String) valueObj);
                    }
                    return 0.0;
                }).reversed())
                .limit(5)
                .map(map -> {
                    // 处理科学计数法
                    Object valueObj = map.get("value");
                    double value;
                    if (valueObj instanceof Double) {
                        value = (Double) valueObj;
                    } else if (valueObj instanceof String) {
                        value = Double.parseDouble((String) valueObj);
                    } else {
                        value = 0.0;
                    }

                    // 使用BigDecimal处理科学计数法，确保正常显示
                    BigDecimal bd = new BigDecimal(value);
                    // 转换为普通小数形式的字符串
                    String plainString = bd.toPlainString();
                    // 再转回Double并四舍五入到2位小数
                    value = NumberUtil.round(Double.parseDouble(plainString), 2).doubleValue();

                    map.put("value", value);
                    return map;
                })
                .collect(Collectors.toList());
        return topFive;
    }

    // Helper method to map names to metrics
    private void mapNamesToMetrics(List<Map<String, Object>> metrics, List<Map> names) {
        for (Map metric : metrics) {
            for (Map name : names) {
                if (metric.get("uuid").equals(name.get("uuid"))) {
                    metric.put("name", name.get("name"));
                    break; // Break once the name is found
                }
            }
        }
    }

}
