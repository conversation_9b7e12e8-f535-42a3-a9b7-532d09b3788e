package cn.iocoder.zj.module.monitor.convert.hardwareinfo;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespDTO;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostinfo.HostInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.hardwareinfo.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.hardwareinfo.HardwareInfoDO;

/**
 * 硬件设施基本信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface HardwareInfoConvert {

    HardwareInfoConvert INSTANCE = Mappers.getMapper(HardwareInfoConvert.class);

    HardwareInfoDO convert(HardwareInfoCreateReqVO bean);

    HardwareInfoDO convert(HardwareInfoUpdateReqVO bean);

    HardwareInfoRespVO convert(HardwareInfoDO bean);

    List<HardwareInfoRespVO> convertList(List<HardwareInfoDO> list);

    PageResult<HardwareInfoRespVO> convertPage(PageResult<HardwareInfoDO> page);

    List<HardwareInfoExcelVO> convertList02(List<HardwareInfoDO> list);
    
    List<HardWareRespCreateReqDTO> convertListDoToDto(List<HardwareInfoDO> list);

    HardWareRespCreateReqDTO convertToDTO(HardwareInfoDO uuid);

    List<HardwareInfoDO> convertCreateList(List<HardWareRespCreateReqDTO> reqDTO);
}
