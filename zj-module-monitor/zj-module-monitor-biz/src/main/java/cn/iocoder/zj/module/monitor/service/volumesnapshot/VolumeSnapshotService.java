package cn.iocoder.zj.module.monitor.service.volumesnapshot;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeSnapshotDTO;
import cn.iocoder.zj.module.monitor.controller.admin.volumesnapshot.vo.VolumeSnapshotCreateReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.volumesnapshot.vo.VolumeSnapshotExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.volumesnapshot.vo.VolumeSnapshotPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.volumesnapshot.vo.VolumeSnapshotUpdateReqVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.volumesnapshot.VolumeSnapshotDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 云盘快照信息 Service 接口
 *
 * <AUTHOR>
 */
public interface VolumeSnapshotService {

    /**
     * 创建云盘快照信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createVolumeSnapshot(@Valid VolumeSnapshotCreateReqVO createReqVO);

    /**
     * 更新云盘快照信息
     *
     * @param updateReqVO 更新信息
     */
    void updateVolumeSnapshot(@Valid VolumeSnapshotUpdateReqVO updateReqVO);

    /**
     * 删除云盘快照信息
     *
     * @param id 编号
     */
    void deleteVolumeSnapshot(Long id);

    /**
     * 获得云盘快照信息
     *
     * @param id 编号
     * @return 云盘快照信息
     */
    VolumeSnapshotDO getVolumeSnapshot(Long id);

    /**
     * 获得云盘快照信息列表
     *
     * @param ids 编号
     * @return 云盘快照信息列表
     */
    List<VolumeSnapshotDO> getVolumeSnapshotList(Collection<Long> ids);

    /**
     * 获得云盘快照信息分页
     *
     * @param pageReqVO 分页查询
     * @return 云盘快照信息分页
     */
    PageResult<VolumeSnapshotDO> getVolumeSnapshotPage(VolumeSnapshotPageReqVO pageReqVO);

    /**
     * 获得云盘快照信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 云盘快照信息列表
     */
    List<VolumeSnapshotDO> getVolumeSnapshotList(VolumeSnapshotExportReqVO exportReqVO);

    Long getVolumeSnapshotCount(String typeName);

    List<VolumeSnapshotDTO> getAll(String typeName);

    void addSnapshots(List<VolumeSnapshotDO> insertDos);

    void updateSnapshots(List<VolumeSnapshotDO> updateDos);

    void deleteVolumeSnapshotByplatform(Long platformId);

    Map<String, Object> getVolumeSnapshotStatusCount(List<String> tenantIds, Long platformId);

    List<VolumeSnapshotDO> getVolumeSnapshotByHostUuids(List<String> hostUuids);
}
