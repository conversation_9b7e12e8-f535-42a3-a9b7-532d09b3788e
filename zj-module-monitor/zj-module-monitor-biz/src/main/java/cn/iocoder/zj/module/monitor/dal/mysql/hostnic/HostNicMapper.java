package cn.iocoder.zj.module.monitor.dal.mysql.hostnic;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostnic.HostNicDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.monitor.controller.admin.hostnic.vo.*;

/**
 * 云主机网络 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface HostNicMapper extends BaseMapperX<HostNicDO> {

    default PageResult<HostNicDO> selectPage(HostNicPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<HostNicDO>()
                .eqIfPresent(HostNicDO::getUuid, reqVO.getUuid())
                .eqIfPresent(HostNicDO::getHostUuid, reqVO.getHostUuid())
                .eqIfPresent(HostNicDO::getIp6, reqVO.getIp6())
                .likeIfPresent(HostNicDO::getIp, reqVO.getIp())
                .eqIfPresent(HostNicDO::getMac, reqVO.getMac())
                .likeIfPresent(HostNicDO::getDriver, reqVO.getDriver())
                .eqIfPresent(HostNicDO::getInClassicNetwork, reqVO.getInClassicNetwork())
                .eqIfPresent(HostNicDO::getNetworkUuid, reqVO.getNetworkUuid())
                .betweenIfPresent(HostNicDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(HostNicDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(HostNicDO::getName, reqVO.getName())
                .likeIfPresent(HostNicDO::getPlatformName, reqVO.getPlatformName())
                .orderByDesc(HostNicDO::getId));
    }

    default List<HostNicDO> selectList(HostNicExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<HostNicDO>()
                .eqIfPresent(HostNicDO::getUuid, reqVO.getUuid())
                .eqIfPresent(HostNicDO::getHostUuid, reqVO.getHostUuid())
                .eqIfPresent(HostNicDO::getIp6, reqVO.getIp6())
                .eqIfPresent(HostNicDO::getIp, reqVO.getIp())
                .eqIfPresent(HostNicDO::getMac, reqVO.getMac())
                .likeIfPresent(HostNicDO::getDriver, reqVO.getDriver())
                .eqIfPresent(HostNicDO::getInClassicNetwork, reqVO.getInClassicNetwork())
                .eqIfPresent(HostNicDO::getNetworkUuid, reqVO.getNetworkUuid())
                .betweenIfPresent(HostNicDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(HostNicDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(HostNicDO::getPlatformName, reqVO.getPlatformName())
                .orderByDesc(HostNicDO::getId));
    }

}
