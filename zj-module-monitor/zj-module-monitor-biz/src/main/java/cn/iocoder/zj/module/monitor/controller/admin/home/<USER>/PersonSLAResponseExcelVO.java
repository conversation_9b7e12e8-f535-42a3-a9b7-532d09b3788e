package cn.iocoder.zj.module.monitor.controller.admin.home.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class PersonSLAResponseExcelVO {
    @ExcelProperty("姓名")
    private String assigneer;
    @ExcelProperty("工单响应次数")
    private Long total;
    @ExcelProperty("工单超时数")
    private Long overtime;
    @ExcelProperty("超时率")
    private String timeoutRate;
}
