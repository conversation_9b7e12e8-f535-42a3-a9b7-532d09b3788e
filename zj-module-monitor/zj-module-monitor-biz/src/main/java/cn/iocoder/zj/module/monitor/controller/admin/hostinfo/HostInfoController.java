package cn.iocoder.zj.module.monitor.controller.admin.hostinfo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.controller.admin.hostinfo.vo.*;
import cn.iocoder.zj.module.monitor.controller.admin.taggables.vo.TaggablesExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.tags.vo.TagsExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.volumeinfo.vo.VolumeInfoUseReqVO;
import cn.iocoder.zj.module.monitor.convert.hostinfo.HostInfoConvert;
import cn.iocoder.zj.module.monitor.convert.tags.TagsConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostinfo.HostInfoDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.secgroup.SecgroupDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.tags.TagsDO;
import cn.iocoder.zj.module.monitor.service.alarmhostrelation.AlarmHostRelationService;
import cn.iocoder.zj.module.monitor.service.hostinfo.HostInfoService;
import cn.iocoder.zj.module.monitor.service.secgroup.SecgroupService;
import cn.iocoder.zj.module.monitor.service.taggables.enums.TagAssetTypeEnum;
import cn.iocoder.zj.module.monitor.service.tags.TagsService;
import cn.iocoder.zj.module.monitor.service.volumeinfo.VolumeInfoService;
import cn.iocoder.zj.module.monitor.util.StringUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.hertzbeat.common.entity.dto.Message;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.HAVE_NO_PLATFORM;

@Tag(name = "管理后台 - 云主机基本信息")
@RestController
@RequestMapping("/monitor/host-info")
@Validated
public class HostInfoController {

    @Resource
    private HostInfoService hostInfoService;
    @Resource
    private AlarmHostRelationService alarmHostRelationService;
    @Resource
    private VolumeInfoService volumeInfoService;
    @Resource
    private SecgroupService secgroupService;
    @Resource
    private TagsService tagsService;

    @PostMapping("/create")
    @Operation(summary = "创建云主机基本信息")
    @PreAuthorize("@ss.hasPermission('monitor:host-info:create')")
    @OperateLog(type = CREATE)
    public CommonResult<Long> createHostInfo(@Valid @RequestBody HostInfoCreateReqVO createReqVO) {
        return success(hostInfoService.createHostInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新云主机基本信息")
    @PreAuthorize("@ss.hasPermission('monitor:host-info:update')")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateHostInfo(@Valid @RequestBody HostInfoUpdateReqVO updateReqVO) {
        hostInfoService.updateHostInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除云主机基本信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('monitor:host-info:delete')")
    @OperateLog(type = DELETE)
    public CommonResult<Boolean> deleteHostInfo(@RequestParam("id") Long id) {
        hostInfoService.deleteHostInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得云主机基本信息")
    @Parameter(name = "uuid", description = "uuid", required = true, example = "964ecf88ab8e4c75bca0ac1e6a11064b")
    public CommonResult<HostInfoRespVO> getHostInfo(@RequestParam("uuid") String uuid) {
        HostInfoDO hostInfo = hostInfoService.getHostInfo(uuid);
        Collection<Long> alarmIds = alarmHostRelationService.getRelationIdsByUuid(hostInfo.getUuid());
        HostInfoRespVO respVO = HostInfoConvert.INSTANCE.convert(hostInfo);
        //获得云盘总容量和使用量
        List<VolumeInfoUseReqVO> list = volumeInfoService.getVolumeInfoListByUuid(uuid);
        //安全组
        List<SecgroupDO> secgroupListByHostUuid = secgroupService.getSecgroupListByHostUuid(hostInfo.getUuid());
        //标签
        List<TagsDO> tag = tagsService.getTagListByTagGables(new TaggablesExportReqVO()
                .setTaggableId(respVO.getId())
                .setTaggableType(TagAssetTypeEnum.INSTANCE.getCode())
        );
        respVO.setTags(TagsConvert.INSTANCE.convertList(tag));
        respVO.setDiskInfos(list);
        respVO.setSecgroupDOS(secgroupListByHostUuid);
        respVO.setAlarmIds(alarmIds);
        return success(respVO);
    }

    @GetMapping("/list")
    @Operation(summary = "获得云主机基本信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    public CommonResult<List<HostInfoRespVO>> getHostInfoList(@RequestParam("ids") Collection<Long> ids) {
        List<HostInfoDO> list = hostInfoService.getHostInfoList(ids);
        return success(HostInfoConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得云主机基本信息分页")
    @TenantIgnore
    public CommonResult<PageResult<HostInfoRespVO>> getHostInfoPage(@Valid HostInfoPageReqVO pageVO) {
        PageResult<HostInfoDO> pageResult = hostInfoService.getHostInfoPage(pageVO);
        List<HostInfoRespVO> hostInfoRespVOS = new ArrayList<>();
        if (pageResult.getList() != null && !pageResult.getList().isEmpty()){
            hostInfoRespVOS = BeanUtil.copyToList(pageResult.getList(), HostInfoRespVO.class);
        }
        for (HostInfoRespVO hostInfoRespVO : hostInfoRespVOS) {
            List<TagsDO> tag = tagsService.getTagListByTagGables(new TaggablesExportReqVO()
                    .setTaggableId(hostInfoRespVO.getId())
                    .setTaggableType(TagAssetTypeEnum.INSTANCE.getCode())
            );
            hostInfoRespVO.setTags(TagsConvert.INSTANCE.convertList(tag));
        }
        PageResult<HostInfoRespVO> pageResults = new PageResult<>();
        pageResults.setList(hostInfoRespVOS);
        pageResults.setTotal( pageResult.getTotal() );
        return success(pageResults);
    }


    @GetMapping("/export-excel")
    @Operation(summary = "导出云主机基本信息 Excel")
    @OperateLog(type = EXPORT)
    @PreAuthorize("@ss.hasPermission('monitor:host-info:export')")
    public void exportHostInfoExcel(@Valid HostInfoExportReqVO exportReqVO,
                                    HttpServletResponse response) throws IOException {
        List<HostInfoDO> list = hostInfoService.getExportInfoList(exportReqVO);
        // 导出 Excel
        List<HostInfoExcelVO> datas = HostInfoConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "云主机基本信息.xls", "数据", HostInfoExcelVO.class, datas);
    }

    @GetMapping("/getCpuInfo")
    @Operation(summary = "获得云主机CPU数据")
    @Parameter(name = "tenantid", description = "租户ID", required = true, example = "1")
    @Parameter(name = "uuid", description = "主机UUID", required = true, example = "05f8323636324691b815270cda6dbf54")
    @Parameter(name = "timeStr", description = "时间条件,1h(1小时),1d(1天),1M/d(1月),1y/d(1年)", required = true, example = "24h")
    public CommonResult<List<Map>> getHostCpuInfo(@RequestParam("tenantid") Long tenantid, @RequestParam("uuid") String uuid, @RequestParam("timeStr") String timeStr) {
        List<Map> hostCpuInfo = hostInfoService.getHostCpuInfoService(tenantid, uuid, timeStr);
        return success(hostCpuInfo);
    }

    @GetMapping("/getDiskInfo")
    @Operation(summary = "获得云主机磁盘数据")
    @Parameter(name = "tenantid", description = "租户ID", required = true, example = "1")
    @Parameter(name = "uuid", description = "主机UUID", required = true, example = "05f8323636324691b815270cda6dbf54")
    @Parameter(name = "timeStr", description = "时间条件,1h(1小时),1d(1天),1M/d(1月),1y/d(1年)", required = true, example = "24h")
    public CommonResult<List<Map>> getHostDiskInfo(@RequestParam("tenantid") Long tenantid, @RequestParam("uuid") String uuid, @RequestParam("timeStr") String timeStr) {
        List<Map> hostDiskInfo = hostInfoService.getHostDiskInfo(tenantid, uuid, timeStr);
        return success(hostDiskInfo);
    }

    @GetMapping("/getMemoryInfo")
    @Operation(summary = "获得云主机内存数据")
    @Parameter(name = "tenantid", description = "租户ID", required = true, example = "1")
    @Parameter(name = "uuid", description = "主机UUID", required = true, example = "05f8323636324691b815270cda6dbf54")
    @Parameter(name = "timeStr", description = "时间条件,1h(1小时),1d(1天),1M/d(1月),1y/d(1年)", required = true, example = "24h")
    public CommonResult<List<Map>> getHostMemoryInfo(@RequestParam("tenantid") Long tenantid, @RequestParam("uuid") String uuid, @RequestParam("timeStr") String timeStr) {
        List<Map> hostMemoryInfo = hostInfoService.getHostMemoryInfo(tenantid, uuid, timeStr);
        return success(hostMemoryInfo);
    }

    @GetMapping("/getNetworkInfo")
    @Operation(summary = "获得云主机网卡数据")
    @Parameter(name = "tenantid", description = "租户ID", required = true, example = "1")
    @Parameter(name = "uuid", description = "主机UUID", required = true, example = "05f8323636324691b815270cda6dbf54")
    @Parameter(name = "timeStr", description = "时间条件,1h(1小时),1d(1天),1M/d(1月),1y/d(1年)", required = true, example = "24h")
    public CommonResult<List<Map>> getHostNetworkInfo(@RequestParam("tenantid") Long tenantid, @RequestParam("uuid") String uuid, @RequestParam("timeStr") String timeStr) {
        List<Map> hostNetworkInfo = hostInfoService.getHostNetworkInfo(tenantid, uuid, timeStr);
        return success(hostNetworkInfo);
    }

    @GetMapping("/getVmLabel")
    @Operation(summary = "获得云主机详情页标签")
    @Parameter(name = "platformId", description = "平台id", required = true, example = "1")
    @Parameter(name = "uuid", description = "主机UUID", required = true, example = "37cf8f8e774c44e0b7ac8d95541b96ac")
    @Parameter(name = "labelKey", description = "标签值 1：代表CPU下面的标签， 2：代表磁盘下面的标签，3：代表网卡标签", required = true, example = "1")
    public CommonResult<List<Map<String, Object>>> getLabel(@RequestParam("platformId") Long platformId, @RequestParam("uuid") String uuid, @RequestParam("labelKey") int labelKey) {
        List<Map<String, Object>> label = hostInfoService.getLabel(platformId, uuid, labelKey);
        return success(label);
    }

    @GetMapping("/getHostGraphData")
    @Operation(summary = "详情页获取云主机CUP、内存、磁盘、网卡数据")
    public CommonResult<List<MonitorHostinfoDataRespVO>> getHostGraphData(MonitorHostinfoDataReqVo reqVo) {
        List<MonitorHostinfoDataRespVO> hostGraphData = new ArrayList<>();
        if (StrUtil.isNotEmpty(Convert.toStr(reqVo.getSearchWay())) && reqVo.getSearchWay().equals("sample")) {
            hostGraphData = hostInfoService.getHostGraphDataRandom(reqVo);
        } else {
            hostGraphData = hostInfoService.getHostGraphData(reqVo);
        }
        for (MonitorHostinfoDataRespVO hostGraphDatum : hostGraphData) {

        }
        return success(hostGraphData.stream()
                .peek(data -> {
                    if (data.getValue() == null) {
                        data.setValue(0.0);
                    }
                })
                .collect(Collectors.toList()));
    }

    @GetMapping("/selectHostList")
    @Operation(summary = "用于选择告警云主机(只有name做查询条件)")
    @TenantIgnore
    public CommonResult<PageResult<HostInfoRespVO>> selectHostList(@Valid HostInfoPageReqVO pageVO) {
        PageResult<HostInfoDO> pageResult = hostInfoService.selectHostList(pageVO);
        return success(HostInfoConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/getHostValuation")
    @Operation(summary = "获取云主机预测数据-综合")
    @TenantIgnore
    public CommonResult<List<ReckonVO>> getHostValuation(@Valid ReckonReqVO reqVo) {
        List<ReckonVO> result = hostInfoService.getHostValuation(reqVo);
        return success(result);
    }

    @GetMapping("/getCPUValuation")
    @Operation(summary = "获取云主机预测数据-CPU")
    @TenantIgnore
    public CommonResult<List<ReckonVO>> getCPUValuation(@Valid ReckonReqVO reqVo) {
        List<ReckonVO> result = hostInfoService.getCPUValuation(reqVo);
        return success(result);
    }

    @GetMapping("/getMemoryValuation")
    @Operation(summary = "获取云主机预测数据-存储")
    @TenantIgnore
    public CommonResult<List<ReckonVO>> getMemoryValuation(@Valid ReckonReqVO reqVo) {
        List<ReckonVO> result = hostInfoService.getMemoryValuation(reqVo);
        return success(result);
    }

    @GetMapping("/getDickValuation")
    @Operation(summary = "获取云主机预测数据-磁盘")
    @TenantIgnore
    public CommonResult<List<ReckonVO>> getDickValuation(@Valid ReckonReqVO reqVo) {
        List<ReckonVO> result = hostInfoService.getDickValuation(reqVo);
        return success(result);
    }

    @GetMapping("/compareAndForecast")
    @Operation(summary = "获取云主机预测数据-环比和预测")
    @TenantIgnore
    public CommonResult<Map<String, Object>> compareAndForecast(@Valid ReckonReqVO reqVo) {
        Map<String, Object> result = hostInfoService.compareAndForecast(reqVo);
        return success(result);
    }

    @GetMapping("/exportWord")
    @Operation(summary = "导出趋势分析word文档")
    @PermitAll
    @TenantIgnore
    @OperateLog(type = EXPORT)
    public void exportWord(HttpServletResponse response,
                           @RequestParam("platformId") String platformId,
                           @RequestParam("uuid") String uuid) {
        if (StringUtil.isNullOrEmpty(platformId)) {
            throw exception(HAVE_NO_PLATFORM);
        }
        hostInfoService.exportWord(response, platformId, uuid);
    }

    @GetMapping("/getUsage")
    @Operation(summary = "获取云主机当前CPU和存储的使用率")
    @TenantIgnore
    public CommonResult<Map<String, Object>> getUsage(@Valid ReckonReqVO reqVo) {
        Map<String, Object> result = hostInfoService.getUsage(reqVo);
        return success(result);
    }

    @GetMapping("/getListByPlatformId")
    @Operation(summary = "按平台配置获取云主机列表")
    @TenantIgnore
    public CommonResult<List<HostInfoRespVO>> getListByPlatformId(@Valid @RequestParam("platformIds") String platformIds) {
        List<HostInfoDO> result = hostInfoService.getListByPlatformId(platformIds);
        return success(HostInfoConvert.INSTANCE.convertList(result));
    }

    @GetMapping("/getHostAlarmByType")
    @Operation(summary = "获取cup、存储、磁盘告警次数")
    @TenantIgnore
    public CommonResult<Map<String, Object>> getHostAlarmByType(@Valid ReckonReqVO reqVo) {
        Map<String, Object> result = hostInfoService.getHostAlarmByType(reqVo);
        return success(result);
    }

    @GetMapping("/getResourcePage")
    @Operation(summary = "三列表-按平台获取相应类型资源分页列表")
    @TenantIgnore
    public CommonResult<PageResult> getResourcePage(@Valid MixResourceSimpleInfoReqVO reqVO) {
        return CommonResult.success(hostInfoService.getResourcePage(reqVO));
    }

    @GetMapping("/getRecourseByPlatform")
    @Operation(summary = "单列表-按平台获取相应类型资源分页列表")
    @TenantIgnore
    public CommonResult<List<Map<String, String>>> getRecourseByPlatform(@Valid MixResourceSimpleInfoReqVO reqVO) {
        return success(hostInfoService.getRecourseByPlatform(reqVO));
    }


    @GetMapping("/slavePage")
    @Operation(summary = "获得云主机基本信息分页(从库数据)")
    @TenantIgnore
    public CommonResult<PageResult<HostInfoRespVO>> getHostInfoSlavePagePage(@Valid HostInfoPageReqVO pageVO) {
        PageResult<HostInfoDO> pageResult = hostInfoService.getHostInfoSlavePagePage(pageVO);
        return success(HostInfoConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/slaveget")
    @Operation(summary = "获得云主机基本信息(从库数据)")
    @Parameter(name = "uuid", description = "编号", required = true, example = "10")
    public CommonResult<HostInfoRespVO> slaveget(@RequestParam("uuid") String uuid) {
        HostInfoDO hostInfo = hostInfoService.slaveget(uuid);
        HostInfoRespVO respVO = HostInfoConvert.INSTANCE.convert(hostInfo);
        return success(respVO);
    }

    @GetMapping("/masterget")
    @Operation(summary = "获得云主机基本信息(主库数据)")
    @Parameter(name = "uuid", description = "编号", required = false, example = "1024")
    public CommonResult<HostInfoRespVO> masterget(@RequestParam("uuid") String uuid) {
        HostInfoDO hostInfo = hostInfoService.masterget(uuid);
        HostInfoRespVO respVO = HostInfoConvert.INSTANCE.convert(hostInfo);
        return success(respVO);
    }

    @GetMapping("/clusterSimpleInfo")
    @Operation(summary = "集群下拉选择框信息")
    @Parameter(name = "platformId", description = "编号", example = "1024,1023")
    public CommonResult<List<Map<String, String>>> getClusterSimpleInfo(@RequestParam(value = "platformId",required = false) String platformId) {
        return success(hostInfoService.getClusterSimpleInfo(platformId));
    }


    @PostMapping(path = "/alertsnmpTrap")
    @Operation(summary = "alertsnmpTrap", description = "获取告警")
    public ResponseEntity<Message<Void>> alertsnmpTrap(
            @Parameter(description = "host | 主机地址 ", example = "127.0.0.1") @RequestParam(required = false) String hostName,
            @Parameter(description = "采集器地址", example = "136") @RequestParam(required = false) String platformName,
            @Parameter(description = "json | 告警trap Json", example = "6565463543") @RequestParam(required = false) String json) {
        if (hostName != null && json != null) {
            hostInfoService.alertsnmpTrap(platformName, hostName, json);
        }

        Message<Void> message = Message.success();
        return ResponseEntity.ok(message);
    }

    @GetMapping("/wx-authorization")
    @Operation(summary = "微信申请授权")
    @Parameter(name = "platformId", description = "平台id", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('monitor:host-info:auth')")
    public CommonResult<Boolean> authorization(@RequestParam("id") Long id,
                                               @RequestParam("platformId") Long platformId) {

        boolean au = hostInfoService.authorization(platformId, id);
        return success(au);
    }
}
