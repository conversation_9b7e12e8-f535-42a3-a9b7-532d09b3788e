package cn.iocoder.zj.module.monitor.service.alarmconfig;

import java.util.*;
import javax.validation.*;

import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmConfigRespDTO;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo.*;
import cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo.AlarmHostListPageResp;
import cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo.AlarmHostRelationPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo.AlarmHostRelationReqVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmconfig.AlarmConfigDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.monitor.dal.mysql.alarmdoriseinfo.AlarmDorisDO;
import org.apache.hertzbeat.common.entity.alerter.Alert;
import org.apache.hertzbeat.common.entity.alerter.AlertConverge;

/**
 * 告警配置 Service 接口
 *
 * <AUTHOR>
 */
public interface AlarmConfigService {

    /**
     * 创建告警配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAlarmConfig(@Valid AlarmConfigCreateReqVO createReqVO);

    /**
     * 更新告警配置
     *
     * @param updateReqVO 更新信息
     */
    void updateAlarmConfig(@Valid AlarmConfigUpdateReqVO updateReqVO);

    /**
     * 删除告警配置
     *
     * @param id 编号
     */
    void deleteAlarmConfig(Long id);

    /**
     * 获得告警配置
     *
     * @param id 编号
     * @return 告警配置
     */
    AlarmConfigDO getAlarmConfig(Long id);

    /**
     * 获得告警配置列表
     *
     * @param ids 编号
     * @return 告警配置列表
     */
    List<AlarmConfigDO> getAlarmConfigList(Collection<Long> ids,String type);

    /**
     * 获得告警配置分页
     *
     * @param pageReqVO 分页查询
     * @return 告警配置分页
     */
    PageResult<AlarmConfigRespVO> getAlarmConfigPage(AlarmConfigPageReqVO pageReqVO);

    /**
     * 获得告警配置列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @param loginUser
     * @return 告警配置列表
     */

    List<AlarmConfigDO> getAlarmConfigList(AlarmConfigExcelReqVO exportReqVO, Collection<Long> d, LoginUser loginUser);

    List<AlarmConfigDO> getAlarmList();

    PageResult<AlarmInfoRespVo> getAlarmInfoPage(AlarmInfoReqVo pageVO);
    AlarmInfoRespVo getAlarmInfo(Long id);

    PageResult<AlarmInfoRespVo> getAlarmHostInfoDetail(AlarmHostRelationPageReqVO reqVO);
    List<AlarmHostListPageResp> getAlarmAddedList(AlarmHostRelationReqVO reqVO);
    AlarmInfoRespVo getAlarmBaseInfoDetail(Long alarmId);

    List<AlarmConfigDO> getListByTenantId(Long tenantId);

    void insertBatch(List<AlarmConfigRespDTO> toInsertAlarmConfig);

    Map<String,String> getAlarmNoticeByConfigId(Long configId);

    Map<String, Object> getAlarmInfoCountInWeek(Long platformId);

    List<AlarmDorisReqDTO> getSilentTarget(AlertConverge converge);

    void solvedAlarm(String alarmId);

    void alarmWorkOrder(String alarmId);

    void cleanWorkOrder(String alarmId);

    List<AlarmInfoRespVo> getUnsolvedAlarms(Long limit);

    void updateIsRead(Collection<Long> ids);

    void updateIsSolved(Collection<Long> ids);

    List<AlarmConfigRespVO> getAlarmConfigByUuid(String uuid);

    void addStateChangeAlarm(List<AlarmInfoRespVo> alarmRecordDTOList);

    List<AlarmHostListPageResp> getPreparingAddAlarmList(AlarmHostRelationReqVO reqVO);

    PageResult<AlarmHostListPageResp> getAlarmHostPage(AlarmHostRelationPageReqVO reqVO);

    List<AlarmInfoRespVo> getAlarmInfoList(AlarmInfoReqVo alarmInfoReqVo);

    void updateAlarmRecord(Long alertId, Integer isSolved);

    void changeAlertSolvedState(Long alertId, Integer isSolved);

    void createAlarmToDoris(List<AlarmDorisReqDTO> updateAlarmDoris,List<AlarmDorisDO> result);

    AlertConverge getAvailableAlertConverge();

    void updateCollectorAlarm(AlarmDorisDO result);

    Long getMaxAlertId();

    void createCollectorAlert(Map<String, Object> alertMap);

    void deletedCollectorAlert(Map<String, Object> alertMap);

    List<Map<String, Object>> getCollectorAlertsByPlatform(Collection<Long> ids);

    AlarmImportRespVO importAlarmConfigList(List<AlarmConfigExcelVO> list);

    Map<String,Object> getAlarmSummary(Long platformId,Integer priority);

    void changeAlarmIsFallback(Long alertId, Integer isFallback);

    Alert getAlertInfoById(Long id);

    void updateEnabled(Long id,Integer enabled);
}
