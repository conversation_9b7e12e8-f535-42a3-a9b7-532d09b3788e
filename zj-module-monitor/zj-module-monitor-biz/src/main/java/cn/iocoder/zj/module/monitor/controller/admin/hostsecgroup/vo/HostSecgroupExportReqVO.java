package cn.iocoder.zj.module.monitor.controller.admin.hostsecgroup.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 安全组关联云主机 Excel 导出 Request VO，参数和 HostSecgroupPageReqVO 是一致的")
@Data
public class HostSecgroupExportReqVO {

    @Schema(description = "云主机uuid")
    private String hostUuid;

    @Schema(description = "安全组uuid")
    private String secgroupUuid;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

}
