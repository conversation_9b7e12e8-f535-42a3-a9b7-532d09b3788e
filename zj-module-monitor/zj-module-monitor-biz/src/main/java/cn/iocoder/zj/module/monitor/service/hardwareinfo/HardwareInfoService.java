package cn.iocoder.zj.module.monitor.service.hardwareinfo;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.controller.admin.hardwareinfo.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.hardwareinfo.HardwareInfoDO;
import cn.iocoder.zj.module.monitor.pojo.AssetReqVO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 硬件设施基本信息 Service 接口
 *
 * <AUTHOR>
 */
public interface HardwareInfoService {

    /**
     * 创建硬件设施基本信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createHardwareInfo(@Valid HardwareInfoCreateReqVO createReqVO);

    /**
     * 更新硬件设施基本信息
     *
     * @param updateReqVO 更新信息
     */
    void updateHardwareInfo(@Valid HardwareInfoUpdateReqVO updateReqVO);

    /**
     * 删除硬件设施基本信息
     *
     * @param id 编号
     */
    void deleteHardwareInfo(Long id);

    /**
     * 获得硬件设施基本信息
     *
     * @param uuid 编号
     * @return 硬件设施基本信息
     */
    HardwareInfoDO getHardwareInfo(String uuid);

    /**
     * 获得硬件设施基本信息列表
     *
     * @param ids 编号
     * @return 硬件设施基本信息列表
     */
    List<HardwareInfoDO> getHardwareInfoList(Collection<Long> ids);

    /**
     * 获得硬件设施基本信息分页
     *
     * @param pageReqVO 分页查询
     * @return 硬件设施基本信息分页
     */
    PageResult<HardwareInfoDO> getHardwareInfoPage(HardwareInfoPageReqVO pageReqVO);

    /**
     * 获得硬件设施基本信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 硬件设施基本信息列表
     */
    List<HardwareInfoDO> getHardwareInfoList(HardwareInfoExportReqVO exportReqVO);

    List<Map> getHardwareDiskInfo(Long id, String uuid, String timeStr);

    List<Map> getHardwareMemoryInfo(Long id, String uuid, String timeStr);

    List<Map> getHardwareNetworkInfo(Long id, String uuid, String timeStr);
    List<Map> getHardwareCpuInfo(Long id,String uuid,String timeStr);

    List<Map<String, Object>> getLabel(Long platformId, String uuid, int labelKey);
    List<MonitorHardwareDataRespVo> getHardwareGraphData(MonitorHardwareDataReqVo reqVo);
    List<MonitorHardwareDataRespVo> getHardwareGraphDataRandom(MonitorHardwareDataReqVo reqVo);


    Map<String,Object> getHardwareStatusCount(List<String> tenantIds,Long platformId);
    List<Map<String,Object>> getCpuTop(List<String> tenantIds,Long platformId,Long regionId,int top);
    List<Map<String,Object>> getMemoryTop(List<String> tenantIds,Long platformId,Long regionId,int top);
    Map<String,Object> getCpuCapacity(List<String> tenantIds,Long platformId,Long regionId);
    Map<String,Object> getMemoryCapacity(List<String> tenantIds,Long platformId,Long regionId);
    Map<String,Object> getMonitorInfo(List<String> tenantIds, Long platformId, Long regionId,String time,String monitorEntry);

    PageResult<HardwareInfoDO> getHardwareInfoSlavePage(HardwareInfoPageReqVO pageVO);

    HardwareInfoDO getHardwareSlaveInfo(String uuid);

    HardwareInfoDO getHardwareMasterInfo(String uuid);
    List<HardwareInfoDO> getListByPlatformId(Collection<Long> platformId);

    List<Map<String,String>> getClusterSimpleInfo(Collection<Long> platformId);

    List<HardwareInfoDO> getHardwareListByUuids(List<String> uuids);

    PageResult<HardwareInfoDO> selectHardwareList(HardwareInfoPageReqVO pageVO);

    HardwareInfoDO getByUuid(String uuid);

    void deleteHardwareInfoByplatform(Long platformId);

    List<HardWareRespCreateReqDTO> getHardwareByPlatformId(Long platformId);

    List<HardwareInfoDO> findHardwareByplatformIdAndHost(Long platformId, String hostName);

    List<HardwareInfoDO> getHardwareInfoByList(HardwareInfoPageReqVO pageVO);

    List<HardwareInfoDO> getHardwareListByPlatformId(Long platformId);

    Integer getCloudHostCount(String uuid);

    Integer getHardwareCount(Long platformId);

    List<HardwareInfoDO> getHardwareByTenantOrPlatforms(AssetReqVO assetReqVO);
}
