package cn.iocoder.zj.module.monitor.service.gatherdevice;

import java.util.*;
import javax.validation.*;
import cn.iocoder.zj.module.monitor.controller.admin.gatherdevice.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.gatherdevice.GatherDeviceDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 采集设备 Service 接口
 *
 * <AUTHOR>
 */
public interface GatherDeviceService {

    /**
     * 创建采集设备
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createGatherDevice(@Valid GatherDeviceCreateReqVO createReqVO);

    /**
     * 更新采集设备
     *
     * @param updateReqVO 更新信息
     */
    Long updateGatherDevice(@Valid GatherDeviceUpdateReqVO updateReqVO);

    /**
     * 删除采集设备
     *
     * @param id 编号
     */
    void deleteGatherDevice(Long id);

    /**
     * 获得采集设备
     *
     * @param id 编号
     * @return 采集设备
     */
    GatherDeviceDO getGatherDevice(Long id);

    /**
     * 获得采集设备列表
     *
     * @param ids 编号
     * @return 采集设备列表
     */
    List<GatherDeviceDO> getGatherDeviceList(Collection<Long> ids);

    /**
     * 获得采集设备分页
     *
     * @param pageReqVO 分页查询
     * @return 采集设备分页
     */
    PageResult<GatherDeviceDO> getGatherDevicePage(GatherDevicePageReqVO pageReqVO);

    /**
     * 获得采集设备列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 采集设备列表
     */
    List<GatherDeviceDO> getGatherDeviceList(GatherDeviceExportReqVO exportReqVO);

    Map createDevice(String json, String token_desc);

    List<GatherDeviceDO> deviceSelect();


     List<String> getGatherDeviceByIdList();

    void updateOnlineType(List<String> onlineType, int type);

    void getOnlineById(String uuid, String tokenDesc);
}
