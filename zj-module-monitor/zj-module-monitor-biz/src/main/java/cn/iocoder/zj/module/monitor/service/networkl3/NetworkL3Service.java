package cn.iocoder.zj.module.monitor.service.networkl3;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL3DTO;
import cn.iocoder.zj.module.monitor.controller.admin.networkl3.vo.NetworkL3CreateReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.networkl3.vo.NetworkL3ExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.networkl3.vo.NetworkL3PageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.networkl3.vo.NetworkL3UpdateReqVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkl3.NetworkL3DO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 三级网络资源 Service 接口
 *
 * <AUTHOR>
 */
public interface NetworkL3Service {

    /**
     * 创建三级网络资源
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createNetworkL3(@Valid NetworkL3CreateReqVO createReqVO);

    /**
     * 更新三级网络资源
     *
     * @param updateReqVO 更新信息
     */
    void updateNetworkL3(@Valid NetworkL3UpdateReqVO updateReqVO);

    /**
     * 删除三级网络资源
     *
     * @param id 编号
     */
    void deleteNetworkL3(Long id);

    /**
     * 获得三级网络资源
     *
     * @param id 编号
     * @return 三级网络资源
     */
    NetworkL3DO getNetworkL3(Long id,String uuid);

    /**
     * 获得三级网络资源列表
     *
     * @param ids 编号
     * @return 三级网络资源列表
     */
    List<NetworkL3DO> getNetworkL3List(Collection<Long> ids);

    /**
     * 获得三级网络资源分页
     *
     * @param pageReqVO 分页查询
     * @return 三级网络资源分页
     */
    PageResult<NetworkL3DO> getNetworkL3Page(NetworkL3PageReqVO pageReqVO);

    /**
     * 获得三级网络资源列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 三级网络资源列表
     */
    List<NetworkL3DO> getNetworkL3List(NetworkL3ExportReqVO exportReqVO);

    void createNetworkL3List(List<NetworkL3DO> list);

    Long getNetWorkL3Count(String typeName);

    void updateNetWorkL3List(List<NetWorkL3DTO> list);

    List<NetworkL3DO> getNetWorkL3List(String typeName);

    PageResult<NetworkL3DO> getNetworkL3VpcPage(NetworkL3PageReqVO pageVO);

    void deleteNetworkL3Byplatform(Long platformId);

    List<NetWorkL3DTO> getNetworkL3ByPlatformId(Long platformId);
}
