package cn.iocoder.zj.module.monitor.dal.mysql.mediatype;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.monitor.dal.dataobject.mediatype.MediaTypeDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.tags.TagsDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.monitor.controller.admin.mediatype.vo.*;

/**
 * 介质类型 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MediaTypeMapper extends BaseMapperX<MediaTypeDO> {

    default PageResult<MediaTypeDO> selectPage(MediaTypePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MediaTypeDO>()
                .likeIfPresent(MediaTypeDO::getName, reqVO.getName())
                .eqIfPresent(MediaTypeDO::getRemark, reqVO.getRemark())
                .eqIfPresent(MediaTypeDO::getTotalCount, reqVO.getTotalCount())
                .betweenIfPresent(MediaTypeDO::getCreateTime, reqVO.getCreateTime())
                .orderBy(
                        reqVO.getSortBy() != null && reqVO.getSortBy().equals("createTime"),
                        reqVO.getSortDirection() != null && reqVO.getSortDirection().equalsIgnoreCase("asc"),
                        MediaTypeDO::getCreateTime
                )
                .orderByDesc(MediaTypeDO::getId));
    }

    default List<MediaTypeDO> selectList(MediaTypeExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MediaTypeDO>()
                .likeIfPresent(MediaTypeDO::getName, reqVO.getName())
                .eqIfPresent(MediaTypeDO::getRemark, reqVO.getRemark())
                .eqIfPresent(MediaTypeDO::getTotalCount, reqVO.getTotalCount())
                .betweenIfPresent(MediaTypeDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MediaTypeDO::getId));
    }

}
