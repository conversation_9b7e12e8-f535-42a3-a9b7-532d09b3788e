package cn.iocoder.zj.module.monitor.dal.mysql.alarmhostrelation;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmconfig.AlarmConfigDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmhostrelation.AlarmHostRelationDO;
import cn.iocoder.zj.module.system.api.tenant.dto.TenantRespDTO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 告警配置与云主机关联关系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AlarmHostRelationMapper extends BaseMapperX<AlarmHostRelationDO> {

    default PageResult<AlarmHostRelationDO> selectPage(AlarmHostRelationPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AlarmHostRelationDO>()
                .likeIfPresent(AlarmHostRelationDO::getAlarmName, reqVO.getAlarmName())
                .eqIfPresent(AlarmHostRelationDO::getAlarmId, reqVO.getAlarmId())
                .likeIfPresent(AlarmHostRelationDO::getHostName, reqVO.getHostName())
                .eqIfPresent(AlarmHostRelationDO::getHostUuid, reqVO.getHostUuid())
                .likeIfPresent(AlarmHostRelationDO::getTenantName, reqVO.getTenantName())
                .betweenIfPresent(AlarmHostRelationDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AlarmHostRelationDO::getId));
    }

    default List<AlarmHostRelationDO> selectList(AlarmHostRelationExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AlarmHostRelationDO>()
                .likeIfPresent(AlarmHostRelationDO::getAlarmName, reqVO.getAlarmName())
                .eqIfPresent(AlarmHostRelationDO::getAlarmId, reqVO.getAlarmId())
                .likeIfPresent(AlarmHostRelationDO::getHostName, reqVO.getHostName())
                .eqIfPresent(AlarmHostRelationDO::getHostUuid, reqVO.getHostUuid())
                .likeIfPresent(AlarmHostRelationDO::getTenantName, reqVO.getTenantName())
                .betweenIfPresent(AlarmHostRelationDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AlarmHostRelationDO::getId));
    }


    Long getLatestId();

    void updateStatus(@Param("reqVO") RelationRqeVO reqVO);

    void deleteByAlarmConfigId(@Param("alarmConfigId")Long alarmConfigId);

    void createRelationByHost(@Param("hostInfoList")List<Map<String,Object>> host,
                              @Param("alarmConfigs")List<AlarmConfigDO> alarmConfigs,
                              @Param("tenant")TenantRespDTO tenant);

    void deleteByUuid(@Param("uuid")String uuid);

    Collection<Long> getRelationByUuid(@Param("uuid")String uuid);

    default void deleteByUuids(List<String> uuids){
        delete(new LambdaQueryWrapperX<AlarmHostRelationDO>()
                .inIfPresent(AlarmHostRelationDO::getHostUuid,uuids));
    };

    Long getRelationCountByAlarmId(@Param("alarmId")String alarmId, @Param("sourceType")String sourceType);

    List<AlarmHostRelationDO> getRelationListInUsed();
}
