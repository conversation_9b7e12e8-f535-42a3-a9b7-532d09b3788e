package cn.iocoder.zj.module.monitor.dal.mysql.tagplan;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.module.monitor.controller.admin.tagplan.vo.TagRecordExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.tagplan.vo.TagRecordPageReqVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.tagplan.TagRecordDO;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 巡检记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TagRecordMapper extends BaseMapperX<TagRecordDO> {

    default PageResult<TagRecordDO> selectPage(TagRecordPageReqVO reqVO) {
        LambdaQueryWrapperX<TagRecordDO> wrapper = new LambdaQueryWrapperX<TagRecordDO>()
                .likeIfPresent(TagRecordDO::getRecordName, reqVO.getRecordName())
                .eqIfPresent(TagRecordDO::getSysSettingTenant, reqVO.getTenantId());

        if (reqVO.getPlanId() != null) {
            wrapper.eq(TagRecordDO::getPlanId, reqVO.getPlanId());
        } else {
            wrapper.isNull(TagRecordDO::getPlanId);
        }
        if (reqVO.getBeginTime() != null && reqVO.getEndTime() != null) {
            wrapper.between(TagRecordDO::getStartTime, reqVO.getBeginTime(), reqVO.getEndTime());
        }
        if (reqVO.getSortBy() != null && reqVO.getSortDirection() != null) {
            if (reqVO.getSortBy().equals("startTime")) {
                if (reqVO.getSortDirection().equals("asc")) {
                    wrapper.orderByAsc(TagRecordDO::getStartTime);
                } else {
                    wrapper.orderByDesc(TagRecordDO::getStartTime);
                }
            }
            if (reqVO.getSortBy().equals("endTime")) {
                if (reqVO.getSortDirection().equals("asc")) {
                    wrapper.orderByAsc(TagRecordDO::getEndTime);
                } else {
                    wrapper.orderByDesc(TagRecordDO::getEndTime);
                }
            }
        } else {
            wrapper.orderByDesc(TagRecordDO::getId);
        }
        return selectPage(reqVO, wrapper);
    }

    default List<TagRecordDO> selectList(TagRecordExportReqVO reqVO) {
        LambdaQueryWrapperX<TagRecordDO> wrapper = new LambdaQueryWrapperX<TagRecordDO>()
                .likeIfPresent(TagRecordDO::getRecordName, reqVO.getRecordName())
                .eqIfPresent(TagRecordDO::getSysSettingTenant, reqVO.getTenantId())
                .orderByDesc(TagRecordDO::getId);

        if (reqVO.getPlanId() != null) {
            wrapper.eq(TagRecordDO::getPlanId, reqVO.getPlanId());
        } else {
            wrapper.isNull(TagRecordDO::getPlanId);
        }
        if (reqVO.getBeginTime() != null && reqVO.getEndTime() != null) {
            wrapper.between(TagRecordDO::getStartTime, reqVO.getBeginTime(), reqVO.getEndTime());
        }
        return selectList(wrapper);
    }
}
