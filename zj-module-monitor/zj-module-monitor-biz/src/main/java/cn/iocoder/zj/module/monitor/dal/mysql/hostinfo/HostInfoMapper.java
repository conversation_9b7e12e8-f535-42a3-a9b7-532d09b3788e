package cn.iocoder.zj.module.monitor.dal.mysql.hostinfo;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import cn.iocoder.zj.module.monitor.api.cloud.dto.CloudRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.controller.admin.hostinfo.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostinfo.HostInfoDO;
import cn.iocoder.zj.module.monitor.taskTime.cache.TaggableDO;
import com.alibaba.excel.util.StringUtils;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 云主机基本信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface HostInfoMapper extends BaseMapperX<HostInfoDO> {

    default PageResult<HostInfoDO> selectPage(HostInfoPageReqVO reqVO) {
        LambdaQueryWrapperX<HostInfoDO> wrapper = new LambdaQueryWrapperX<>();
        if (StringUtils.isNotBlank(reqVO.getQueryData())) {
            wrapper.likeIfPresent(HostInfoDO::getName, reqVO.getQueryData())
                    .or().like(HostInfoDO::getIp, reqVO.getQueryData())
                    .or().like(HostInfoDO::getMac, reqVO.getQueryData())
                    .or().like(HostInfoDO::getPlatformName, reqVO.getPlatformName());
        }
        if (StringUtils.isNotBlank(reqVO.getState())) {
            wrapper.eqIfPresent(HostInfoDO::getState, reqVO.getState());
        }
        if (StrUtil.isNotEmpty(Convert.toStr(reqVO.getPlatformId()))) {
            wrapper.eqIfPresent(HostInfoDO::getPlatformId, reqVO.getPlatformId());
        }
        wrapper.orderByDesc(HostInfoDO::getId);
        return selectPage(reqVO, wrapper);

    }

    List<HostInfoDO> getExportInfoList(@Param("reqVO") HostInfoExportReqVO reqVO);

    int getCount(@Param("typeName") String typeName);

    void updateHostInfoList(List<HostInfoRpcVO> list);

    Map<String, Object> getHostStatusCount(@Param("tenantIds") List<String> tenantId, @Param("platformId") Long platformId);

    List<Map<String, Object>> getCpuTop(@Param("tenantIds") List<String> tenantIds, @Param("platformId") Long platformId, @Param("regionId") Long regionId, @Param("top") int top);

    List<Map<String, Object>> getMemoryTop(@Param("tenantIds") List<String> tenantIdsd, @Param("platformId") Long platformId, @Param("regionId") Long regionId, @Param("top") int top);

    int getIsDisplay(@Param("tenantId") Long tenantId, @Param("monitorEntry") String monitorEntry);

    String getAllDisplay(@Param("tenantId") Long tenantId);

    int updateDisplay(@Param("tenantId") Long tenantId, @Param("layoutConfig") String layoutConfig);

    List<Map<String, Object>> getModuleList(@Param("tenantId") Long tenantId);

    List<Map<String, Object>> getResourceType(@Param("tenantId") Long tenantId, @Param("module") String module);

    List<Map<String, Object>> getMonitorEntry(@Param("tenantId") Long tenantId, @Param("module") String module, @Param("resourceType") String resourceType);

    Map<String, Object> getLayoutConfig(@Param("tenantId") Long tenantId);

    List<String> getDMonitorEntry();

    List<String> selectTenantHostUUID(@Param("tenantId") String tenantId);

    int getEntryCount(@Param("tenantId") Long tenantId, @Param("module") String module);

    List<HostInfoDO> getHostInfoPage(@Param("mpPage") IPage<HostInfoDO> mpPage, @Param("pageReqVO") HostInfoPageReqVO pageReqVO, @Param("ids") List<String> ids);

    List<HostInfoDO> alarmHostList(@Param("mpPage") IPage<HostInfoDO> mpPage, @Param("pageReqVO") HostInfoPageReqVO pageReqVO);

    List<Long> getRegionId(@Param("tenantId") Long tenantId, @Param("regionId") Long regionId);

    List<Map<String, Object>> getPlatformInfos(@Param("tenantId") Long tenantId, @Param("regionId") String regionId);

    List<Map<String, Object>> getPlatformInfos(@Param("tenantId") Long tenantId, @Param("regionId") Long regionId);

    List<HostInfoDO> selectListByUuid(@Param("uuidList") List<String> uuidList);

    List<HostInfoDO> selectByTenant(@Param("tenantId") Long tenantId);

    String getplatformName(@Param("platformId") Long platformId);

    void updateByUuid(@Param("hostInfoDO") HostInfoUpdateReqVO hostInfoDO);

    int deleteHostList(@Param("list") List<HostInfoDO> list);

    List<Map<String, String>> getRecourseByPlatform(@Param("reqVO") MixResourceSimpleInfoReqVO reqVO);

    default List<HostInfoDO> getListByPlatformId(String platformIds) {

        return selectList(new LambdaQueryWrapperX<HostInfoDO>()
                .eq(HostInfoDO::getPlatformId, platformIds)
                .eq(HostInfoDO::getState, "Running")
                .groupBy(HostInfoDO::getUuid)

        );
    }

    default List<HostInfoDO> getListByPlatform(List<Map> platform) {
        List<String> data = new ArrayList<>();
        if (!platform.isEmpty()) {
            for (Map map : platform) {
                data.add(map.get("platformId").toString());
            }
        }
        LambdaQueryWrapper<HostInfoDO> queryWrapperX = new LambdaQueryWrapperX<>();
        if (!data.isEmpty()) {
            queryWrapperX.in(HostInfoDO::getPlatformId, data);
        } else {
            return List.of();
        }
        return selectList(queryWrapperX);
    }

    default List<HostInfoDO> getListByPlatformIds(List<Long> platformIds) {
        LambdaQueryWrapper<HostInfoDO> queryWrapperX = new LambdaQueryWrapperX<>();
        if (!platformIds.isEmpty()) {
            queryWrapperX.in(HostInfoDO::getPlatformId, platformIds);
        } else {
            return List.of();
        }
        return selectList(queryWrapperX);
    }

    // 从库
    @Slave
    List<HostInfoDO> getHostInfoSlavePagePage(@Param("mpPage") IPage<HostInfoDO> mpPage, @Param("pageReqVO") HostInfoPageReqVO pageReqVO, @Param("ids") List<String> ids);


    default List<HostInfoDO> getAllDeviceByPlatformId(@Param("platformId") Collection<Long> platformId) {
        return selectList(new LambdaQueryWrapperX<HostInfoDO>()
                .in(HostInfoDO::getPlatformId, platformId)
                .eq(HostInfoDO::getState, "Running")
                .groupBy(HostInfoDO::getUuid)

        );
    }

    List<Map<String, String>> getClusterSimpleInfo(@Param("platformId") List<String> platformId);

    List<Map<String, String>> getDuplicateDataIds();

    void removeDuplicateData(@Param("duplicateDatas") List<Map<String, String>> duplicateDatas);

    List<Map> getResourceAppList(@Param("platformId") Long platformId);

    @TenantIgnore
    List<Map> getResourcesList(@Param("platform") List<Map> platform);

    @TenantIgnore
    List<Map> getHostNameByuuid(@Param("hostcpus") List<Map<String, Object>> hostcpus);

    Long selectCountByPlatfrom(@Param("mapList") List<Map> platformConfigList);

    void deleteByPlatformId(@Param("id") Long id);


    @TenantIgnore
    Map<String, Object> getHostAlarmByType(@Param("reqVo") ReckonReqVO reqVo);

    @TenantIgnore
    void deletMonitorByPlatformId(@Param("platformId") Long platformId);

    List<HostInfoRespCreateReqDTO> getVmByPlatformId(@Param("platformId") Long platformId);

    @TenantIgnore
    Long findPlatformName(@Param("platformName") String platformName);

    @TenantIgnore
    List<Monitor> findMonitorsByPlatformIdAndHost(@Param("platformId") Long platformId, @Param("hostName") String hostName);

    @DS("doris")
    @TenantIgnore
    void addAlertList(List<AlarmDorisReqDTO> alerts);

    @DS("doris")
    @TenantIgnore
    Long findMaxId();

    List<CloudRespCreateReqDTO> getCloudByPlatformId(@Param("platformId") Long platformId);

    List<Map> getMonitorAsset(@Param("platformId") Long platformId);
    @TenantIgnore
    int getHostCount();

    void deletMonitorAssetByPlatformId(@Param("platformId")Long platformId);
    @TenantIgnore
    List<String> getUserListByTenantId(@Param("tenantId")Long tenantid);
    @TenantIgnore
    String getTenantInfo(@Param("tenantId") Long tenantid);
    @TenantIgnore
    String getOpenIdByUserId(@Param("userId")String userId);

    @TenantIgnore
    void updateAuthorizationType(@Param("id") Long id, @Param("authorizationType") String authorizationType, @Param("authorizationTime") LocalDateTime authorizationTime);

    @TenantIgnore
    List<Map<String, Object>> getVmInfoMemoryUsedTopByPlatformId(@Param("platformId") List<Map> platformId);

    @TenantIgnore
    List<Map<String, Object>> getVmInfoCpuUsedTopByPlatformId(@Param("platformId") List<Map> platformId);

    @TenantIgnore
    List<HostInfoDO> getHostByList(@Param("list") List<String> list);

    @TenantIgnore
    List<HostInfoDO> getByPlatformIdAndTags(@Param("platformIds") List<Long> platformIds, @Param("tagNames") List<String> tagNames);

    @TenantIgnore
    List<TaggableDO> getListByTag();
}
