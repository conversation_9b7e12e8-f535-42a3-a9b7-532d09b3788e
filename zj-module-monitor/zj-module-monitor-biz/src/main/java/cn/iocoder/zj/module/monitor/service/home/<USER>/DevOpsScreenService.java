package cn.iocoder.zj.module.monitor.service.home.devopsscreen;

import java.util.List;
import java.util.Map;

public interface DevOpsScreenService {
    List<Map> getResourceDistribution();

    List<Map> getLoginRanking();

    Map getRunningAssetAndUsers();

    List<Map> abnormalUsage(Long platformId);

    List<Map> storageExceptions(Long platformId);

    List<Map> getTodoTask(Long platformId);
}
