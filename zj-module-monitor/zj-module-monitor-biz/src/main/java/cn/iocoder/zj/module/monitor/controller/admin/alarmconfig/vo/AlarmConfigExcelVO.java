package cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo;

import cn.iocoder.zj.framework.excel.core.convert.StateConvert;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 告警配置 Excel VO
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false)
public class AlarmConfigExcelVO {

    @ExcelProperty("告警名称")
    private String alarmName;

    @ExcelProperty("资源类型")
    private String sourceType;

    @ExcelProperty("告警级别")
    private Integer alarmLevel;

    @ExcelProperty("触发次数")
    private Long alarmTime;

    @ExcelProperty("告警简介")
    private String description;

    @ExcelProperty("告警类型（字典值）")
    private String dictLabelType;

    @ExcelProperty("告警类型")
    private String dictLabelName;

    @ExcelProperty("告警条目（字典值）")
    private String dictLabelValue;

    @ExcelProperty("逻辑符号")
    private String alarmRule;

    @ExcelProperty("告警阈值")
    private Integer alarmVal;

    @ExcelProperty("单位")
    @ColumnWidth(20)
    private String unit;

    @ExcelProperty("单位（字典值）")
    private String unitType;

}
