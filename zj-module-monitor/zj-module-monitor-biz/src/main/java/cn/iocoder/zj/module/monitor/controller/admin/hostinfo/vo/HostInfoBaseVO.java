package cn.iocoder.zj.module.monitor.controller.admin.hostinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 云主机基本信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class HostInfoBaseVO {

    @Schema(description = "主机id")
    private String uuid;

    @Schema(description = "主机名称")
    private String name;

    @Schema(description = "主机状态")
    private String state;

    @Schema(description = "ip")
    private String ip;

    @Schema(description = "弹性ip")
    private String vipIp;

    @Schema(description = "集群id")
    private String clusterUuid;

    @Schema(description = "CPU架构")
    private String architecture;

    @Schema(description = "操作系统类型")
    private String guestOsType;

    @Schema(description = "主机创建时间")
    private Date vCreateDate;

    @Schema(description = "主机类型")
    private String type;

    @Schema(description = "分配内存")
    private Long memorySize;

    @Schema(description = "分配cpu")
    private Integer cpuNum;

    @Schema(description = "mac 地址")
    private String mac;

    @Schema(description = "硬盘使用率")
    private BigDecimal diskUsed;


    @Schema(description = "cpu使用率")
    private BigDecimal cpuUsed;

    @Schema(description = "内存使用率")
    private BigDecimal memoryUsed;

    @Schema(description = "集群名称")
    private String clusterName;

    @Schema(description = "网卡入速度")
    private BigDecimal networkInBytes;
    @Schema(description = "网卡出速度")
    private BigDecimal networkOutBytes;
    @Schema(description = "硬盘已使用容量")
    private BigDecimal diskUsedBytes;
    @Schema(description = "硬盘剩余容量")
    private BigDecimal diskFreeBytes;
    @Schema(description = "硬盘总容量")
    private BigDecimal totalDiskCapacity;
    @Schema(description = "云盘真实容量")
    private BigDecimal actualSize;
    @Schema(description = "云盘容量")
    private BigDecimal cloudSize;

    @Schema(description = "网卡收包数")
    private BigDecimal networkInPackets;
    @Schema(description = "网卡出包数")
    private BigDecimal networkOutPackets;
    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "平台配置ID")
    private Long platformId;
    @Schema(description = "平台配置名称")
    private String platformName;
    @Schema(description = "地区ID")
    private Long regionId;

    @Schema(description = "平台类型")
    private String typeName;

    @Schema(description = "vmware设备参数")
    private String vms;

    @Schema(description = "可用区uuid")
    private String zoneUuid;

    @Schema(description = "可用区名称")
    private String zoneName;

    @Schema(description = "镜像uuid")
    private String imageUuid;

    @Schema(description = "宿主机uuid")
    private String hardwareUuid;

    @Schema(description = "宿主机名称")
    private String hardwareName;

    @Schema(description = "镜像名称")
    private String imageName;

    @Schema(description = "电源状态")
    private String powerState;

    @Schema(description = "iso")
    private String iso;

    @Schema(description = "自动启动或高可用设置")
    private String autoInitType;

    @Schema(description = "引导模式")
    private String guideMode;
}
