package cn.iocoder.zj.module.monitor.controller.admin.hardwareinfo.vo;

import cn.iocoder.zj.module.monitor.controller.admin.tags.vo.TagsRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Date;
import java.util.List;

@Schema(description = "管理后台 - 硬件设施基本信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HardwareInfoRespVO extends HardwareInfoBaseVO {

    @Schema(description = "主键", required = true)
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

    @Schema(description = "告警配置id")
    private Collection<Long> alarmIds;

    @Schema(description = "标签信息")
    private List<TagsRespVO> tags;
}
