package cn.iocoder.zj.module.monitor.handler;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.iocoder.zj.module.monitor.dal.dataobject.zstack.ZstackLoginInfo;
import cn.iocoder.zj.module.monitor.dal.redis.zstack.ZstackAccessTokenRedisDAO;
import cn.iocoder.zj.module.monitor.service.zstack.IZstackCommon;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CustomRequestInterceptor implements IZstackCommon {


    ZstackAccessTokenRedisDAO zstackAccessTokenRedisDAO;

    public CustomRequestInterceptor(ZstackAccessTokenRedisDAO zstackAccessTokenRedisDAO) {
        this.zstackAccessTokenRedisDAO = zstackAccessTokenRedisDAO;
    }

    @Override
    public HttpRequest intercept(HttpRequest request) {
        // 获取header中的type值
        String type = request.header("Authorization");
        if (type.contains("accessKey")) {
            List<String> list = StrUtil.split(type, ',');
            Long platformId = Long.parseLong(list.get(1));
            ZstackLoginInfo zstackLoginInfo = zstackAccessTokenRedisDAO.get("zstack:" + platformId);
            // 获取原始请求信息
            String method = request.getMethod().toString();
            String url = request.getUrl();
            Map<String, List<String>> headers = request.headers();
            // 使用正则表达式匹配 "/zstack/" 后面的路径
            String regex = "/zstack(/.*)";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(url);
            if (matcher.find()) {
                // 返回匹配到的路径
                System.out.println(matcher.group(1));
            } else {
                System.out.println("No match found.");
            }
            // 获取当前时间戳
            long timestamp = System.currentTimeMillis();
            // 创建SimpleDateFormat对象，定义日期格式，并指定Locale为英文
            SimpleDateFormat sdf = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss 'PRC'", Locale.ENGLISH);
            // 设置时区为中国标准时间（PRC）
            sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
            // 格式化当前时间
            String formattedDate = sdf.format(new Date(timestamp));
            // 打印格式化后的日期
            String accessKeyId = zstackLoginInfo.getAccountUuid();
            String accessKeySecret = zstackLoginInfo.getUserUuid();
//            String accessKeyId = "UqEij8LQa4t2wVJzJ0kG";
//            String accessKeySecret = "x1QV93E51Xh4kX9rpJj3MGcEjoUEB3VkNA7KvR0T";

            String date = formattedDate;
            String uri = matcher.group(1);
            // 生成签名
            String signature = null;
            try {
                signature = generateSignature(accessKeySecret, method, date, uri);
            } catch (NoSuchAlgorithmException e) {
                throw new RuntimeException(e);
            } catch (InvalidKeyException e) {
                throw new RuntimeException(e);
            }
            // 打印认证信息
            String authorization = "ZStack " + accessKeyId + ":" + signature;
            System.out.println("Authorization: " + authorization);

            Map<String, String> newHeaders = new HashMap<>();
            newHeaders.put("Authorization", authorization);
            newHeaders.put("Content-Type", "application/x-www-form-urlencoded");
            newHeaders.put("Date", date);
            newHeaders.forEach(request::header);
        }
        return request;
    }


    public static String generateSignature(String accessKeySecret, String method, String date, String uri) throws NoSuchAlgorithmException, InvalidKeyException {
        // 创建待签名的字符串
        String stringToSign = method + "\n" + date + "\n" + uri;

        // 使用HMAC-SHA1进行签名
        Mac mac = Mac.getInstance("HmacSHA1");
        SecretKeySpec secretKeySpec = new SecretKeySpec(accessKeySecret.getBytes(StandardCharsets.UTF_8), "HmacSHA1");
        try {
            mac.init(secretKeySpec);
        } catch (InvalidKeyException e) {
            throw new RuntimeException(e);
        }
        byte[] rawHmac = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));

        // 将签名进行Base64编码
        return Base64.getEncoder().encodeToString(rawHmac);
    }


}
