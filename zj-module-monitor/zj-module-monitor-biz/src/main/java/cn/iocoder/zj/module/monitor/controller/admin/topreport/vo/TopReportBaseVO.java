package cn.iocoder.zj.module.monitor.controller.admin.topreport.vo;

import cn.iocoder.zj.framework.common.validation.InEnum;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
public class TopReportBaseVO {

    @NotEmpty(message = "报表名称不能为空")
    @Size(max = 100, message = "报表名称长度不能超过100")
    private String reportName;

    @Size(max = 500, message = "描述长度不能超过500")
    private String description;

    @NotNull(message = "TOP N数字不能为空")
    @Min(value = 1, message = "TOP N数字必须大于0")
    private Integer topNum;


    private Integer statisticsType;


    private String assetType;

    private String metricType;

    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;
}
