package cn.iocoder.zj.module.monitor.framework.rpc.config;

import cn.iocoder.cloud.module.cloudedge.api.monitor.MonitorApi;
import cn.iocoder.cloud.module.guacamole.api.OnlineSessionApi;
import cn.iocoder.zj.module.bpm.api.task.BpmProcessInstanceApi;
import cn.iocoder.zj.module.bpm.api.task.BpmTaskApi;
import cn.iocoder.zj.module.collection.api.VolumeApi;
import cn.iocoder.zj.module.collector.api.ScanIPApi;
import cn.iocoder.zj.module.customer.api.custinfo.CustInfoApi;
import cn.iocoder.zj.module.infra.api.file.FileApi;
import cn.iocoder.zj.module.monitor.api.hardware.HardWareInfoApi;
import cn.iocoder.zj.module.system.api.dict.DictDataApi;
import cn.iocoder.zj.module.system.api.mail.MailSendApi;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.region.RegionApi;
import cn.iocoder.zj.module.system.api.sms.SmsSendApi;
import cn.iocoder.zj.module.system.api.tenant.TenantApi;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.wechat.WeChatSendApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName : RpcConfiguration  //类名
 * @Description : RPC 接口  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/23  10:38
 */

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {RegionApi.class,
        PlatformconfigApi.class,
        RoleApi.class,
        PermissionApi.class,
        SmsSendApi.class,
        MailSendApi.class,
        VolumeApi.class,
        WeChatSendApi.class,
        HardWareInfoApi.class,
        CustInfoApi.class,
        BpmTaskApi.class,
        AdminUserApi.class,
        FileApi.class,
        BpmProcessInstanceApi.class,
        TenantApi.class,
        DictDataApi.class, OnlineSessionApi.class,
        MonitorApi.class,
        ScanIPApi.class})
public class RpcConfiguration {
}
