package cn.iocoder.zj.module.monitor.service.tagplan;

import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.dal.mysql.tagplan.TagTaskMapper;
import cn.iocoder.zj.module.monitor.tagtask.job.model.TagTaskModel;
import cn.iocoder.zj.module.monitor.tagtask.job.model.TaskServiceInfo;
import cn.iocoder.zj.module.monitor.taskTime.listener.RedisPublisher;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class TagTaskModelServiceImpl implements TagTaskModelService {

    @Resource
    private TagTaskMapper lulTaskMapper;

    @Resource
    private TaskServiceInfo taskServiceInfo;

    @Resource
    private RedisPublisher redisPublisher;

    @Override
    @TenantIgnore
    public List<TagTaskModel> findAll() {
        return lulTaskMapper.selectList(new LambdaQueryWrapper<TagTaskModel>().eq(TagTaskModel::getStatus, 1));
    }

    @Override
    @TenantIgnore
    public void addTask(TagTaskModel taskCacheModel) {
        lulTaskMapper.insert(taskCacheModel);
    }

    @Override
    @TenantIgnore
    public void updateTasByJobId(TagTaskModel taskCacheModel) {
        int status = taskCacheModel.getStatus() == 1 ? 0 : 1;
        taskCacheModel.setStatus(status);
        if (lulTaskMapper.updateTasByJobId(taskCacheModel) > 0) {
            taskServiceInfo.updateTask(taskCacheModel);
        }
    }

    @Override
    @TenantIgnore
    public void removeTask(TagTaskModel taskCacheModel) {
        redisPublisher.publish("lul_task", String.valueOf(taskCacheModel.getId()));
        lulTaskMapper.deleteById(taskCacheModel.getId());
        taskServiceInfo.removeTask(taskCacheModel.getId());
    }

    @Override
    @TenantIgnore
    public TagTaskModel getTaskByJobId(Long jobId) {
        return lulTaskMapper.selectById(jobId);
    }
}
