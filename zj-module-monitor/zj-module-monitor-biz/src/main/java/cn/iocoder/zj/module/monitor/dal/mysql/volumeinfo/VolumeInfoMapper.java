package cn.iocoder.zj.module.monitor.dal.mysql.volumeinfo;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeAttachableVmDTO;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeDTO;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeSnapshotDTO;
import cn.iocoder.zj.module.monitor.controller.admin.volumeinfo.vo.VolumeInfoExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.volumeinfo.vo.VolumeInfoPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.volumeinfo.vo.VolumeInfoRespVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.volumeinfo.VolumeInfoDO;
import cn.iocoder.zj.module.monitor.taskTime.cache.TaggableDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import jodd.util.StringUtil;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 云盘信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface VolumeInfoMapper extends BaseMapperX<VolumeInfoDO> {

    default PageResult<VolumeInfoDO> selectPage(VolumeInfoPageReqVO reqVO,List<Map> platform) {
        List<String> data = new ArrayList<>();
        if (!platform.isEmpty()){
            for (Map map : platform){
                data.add(map.get("platformId").toString());
            }
        }
        LambdaQueryWrapper<VolumeInfoDO> queryWrapperX = new LambdaQueryWrapperX<VolumeInfoDO>()
                .in(ObjectUtil.isNotEmpty(reqVO.getIds()),VolumeInfoDO::getId, reqVO.getIds())
                .eq(ObjectUtil.isNotEmpty(reqVO.getUuid()),VolumeInfoDO::getUuid, reqVO.getUuid())
                .eq(ObjectUtil.isNotEmpty(reqVO.getPlatformId()),VolumeInfoDO::getPlatformId, reqVO.getPlatformId())
                .like(ObjectUtil.isNotEmpty(reqVO.getName()),VolumeInfoDO::getName, reqVO.getName())
                .like(ObjectUtil.isNotEmpty(reqVO.getDescription()),VolumeInfoDO::getDescription, reqVO.getDescription())
                .eq(ObjectUtil.isNotEmpty(reqVO.getPrimaryStorageUuid()),VolumeInfoDO::getPrimaryStorageUuid, reqVO.getPrimaryStorageUuid())
                .like(ObjectUtil.isNotEmpty(reqVO.getPrimaryStorageName()),VolumeInfoDO::getPrimaryStorageName, reqVO.getPrimaryStorageName())
                .eq(ObjectUtil.isNotEmpty(reqVO.getVmInstanceUuid()),VolumeInfoDO::getVmInstanceUuid, reqVO.getVmInstanceUuid())
                .like(ObjectUtil.isNotEmpty(reqVO.getPrimaryStorageType()),VolumeInfoDO::getPrimaryStorageType, reqVO.getPrimaryStorageType())
                .like(ObjectUtil.isNotEmpty(reqVO.getVmInstanceName()),VolumeInfoDO::getVmInstanceName, reqVO.getVmInstanceName())
                .like(ObjectUtil.isNotEmpty(reqVO.getType()),VolumeInfoDO::getType, reqVO.getType())
                .like(ObjectUtil.isNotEmpty(reqVO.getFormat()),VolumeInfoDO::getFormat, reqVO.getFormat())
                .like(ObjectUtil.isNotEmpty(reqVO.getSize()),VolumeInfoDO::getSize, reqVO.getSize())
                .like(ObjectUtil.isNotEmpty(reqVO.getActualSize()),VolumeInfoDO::getActualSize, reqVO.getActualSize())
                .eq(ObjectUtil.isNotEmpty(reqVO.getState()),VolumeInfoDO::getState, reqVO.getState())
                .eq(ObjectUtil.isNotEmpty(reqVO.getStatus()),VolumeInfoDO::getStatus, reqVO.getStatus())
                .between(ObjectUtil.isNotEmpty(reqVO.getStartTime()) && ObjectUtil.isNotEmpty(reqVO.getEndTime()),VolumeInfoDO::getVCreateDate, reqVO.getStartTime(), reqVO.getEndTime())
                .like(ObjectUtil.isNotEmpty(reqVO.getMaxIops()),VolumeInfoDO::getMaxIops, reqVO.getMaxIops())
                .like(ObjectUtil.isNotEmpty(reqVO.getThroughput()),VolumeInfoDO::getThroughput, reqVO.getThroughput())
                .like(ObjectUtil.isNotEmpty(reqVO.getMediaType()),VolumeInfoDO::getMediaType, reqVO.getMediaType())
                .eq(ObjectUtil.isNotEmpty(reqVO.getIsMount()),VolumeInfoDO::getIsMount, reqVO.getIsMount());

        if (!data.isEmpty()){
            queryWrapperX.in(VolumeInfoDO::getPlatformId,data);
        }else {
            queryWrapperX.eq(VolumeInfoDO::getPlatformId,"null");
        }

        if (StringUtil.isNotEmpty(reqVO.getSortDirection())){
            if (reqVO.getSortDirection().equals("asc")){
                if (reqVO.getSortBy().equals("vCreateDate")){
                    queryWrapperX.orderByAsc(VolumeInfoDO::getVCreateDate);
                }
                if (reqVO.getSortBy().equals("size")){
                    queryWrapperX.orderByAsc(VolumeInfoDO::getSize);
                }
            }else if (reqVO.getSortDirection().equals("desc")){
                if (reqVO.getSortBy().equals("vCreateDate")){
                    queryWrapperX.orderByDesc(VolumeInfoDO::getVCreateDate);
                }
                if (reqVO.getSortBy().equals("size")){
                    queryWrapperX.orderByDesc(VolumeInfoDO::getSize);
                }
            }
        }else {
            queryWrapperX.orderByDesc(VolumeInfoDO::getVCreateDate);
        }
        return selectPage(reqVO, queryWrapperX);

    }

    default List<VolumeInfoDO> selectList(VolumeInfoExportReqVO reqVO, List<Map> platform) {
        List<String> data = new ArrayList<>();
        if (!platform.isEmpty()){
            for (Map map : platform){
                data.add(map.get("platformId").toString());
            }
        }
        LambdaQueryWrapper<VolumeInfoDO> queryWrapperX = new LambdaQueryWrapperX<VolumeInfoDO>()
                .in(ObjectUtil.isNotEmpty(reqVO.getIds()),VolumeInfoDO::getId, reqVO.getIds())
                .eq(ObjectUtil.isNotEmpty(reqVO.getUuid()),VolumeInfoDO::getUuid, reqVO.getUuid())
                .eq(ObjectUtil.isNotEmpty(reqVO.getPlatformId()),VolumeInfoDO::getPlatformId, reqVO.getPlatformId())
                .like(ObjectUtil.isNotEmpty(reqVO.getName()),VolumeInfoDO::getName, reqVO.getName())
                .eq(ObjectUtil.isNotEmpty(reqVO.getDescription()),VolumeInfoDO::getDescription, reqVO.getDescription())
                .eq(ObjectUtil.isNotEmpty(reqVO.getPrimaryStorageUuid()),VolumeInfoDO::getPrimaryStorageUuid, reqVO.getPrimaryStorageUuid())
                .like(ObjectUtil.isNotEmpty(reqVO.getPrimaryStorageName()),VolumeInfoDO::getPrimaryStorageName, reqVO.getPrimaryStorageName())
                .eq(ObjectUtil.isNotEmpty(reqVO.getVmInstanceUuid()),VolumeInfoDO::getVmInstanceUuid, reqVO.getVmInstanceUuid())
                .like(ObjectUtil.isNotEmpty(reqVO.getVmInstanceName()),VolumeInfoDO::getVmInstanceName, reqVO.getVmInstanceName())
                .eq(ObjectUtil.isNotEmpty(reqVO.getType()),VolumeInfoDO::getType, reqVO.getType())
                .eq(ObjectUtil.isNotEmpty(reqVO.getFormat()),VolumeInfoDO::getFormat, reqVO.getFormat())
                .eq(ObjectUtil.isNotEmpty(reqVO.getSize()),VolumeInfoDO::getSize, reqVO.getSize())
                .eq(ObjectUtil.isNotEmpty(reqVO.getActualSize()),VolumeInfoDO::getActualSize, reqVO.getActualSize())
                .eq(ObjectUtil.isNotEmpty(reqVO.getState()),VolumeInfoDO::getState, reqVO.getState())
                .eq(ObjectUtil.isNotEmpty(reqVO.getStatus()),VolumeInfoDO::getStatus, reqVO.getStatus())
                .between(ObjectUtil.isNotEmpty(reqVO.getStartTime()) && ObjectUtil.isNotEmpty(reqVO.getEndTime()),VolumeInfoDO::getVCreateDate, reqVO.getStartTime(), reqVO.getEndTime())
                .eq(ObjectUtil.isNotEmpty(reqVO.getMaxIops()),VolumeInfoDO::getMaxIops, reqVO.getMaxIops())
                .eq(ObjectUtil.isNotEmpty(reqVO.getThroughput()),VolumeInfoDO::getThroughput, reqVO.getThroughput())
                .eq(ObjectUtil.isNotEmpty(reqVO.getMediaType()),VolumeInfoDO::getMediaType, reqVO.getMediaType())
                .eq(ObjectUtil.isNotEmpty(reqVO.getIsMount()),VolumeInfoDO::getIsMount, reqVO.getIsMount())
                .orderByDesc(VolumeInfoDO::getId);
        if (!data.isEmpty()){
            queryWrapperX.in(VolumeInfoDO::getPlatformId,data);
        }else {
            queryWrapperX.eq(VolumeInfoDO::getPlatformId,"null");
        }
        return selectList(queryWrapperX);

    }

    default List<VolumeInfoDO> selectListByPlatform(List<Map> platform) {
        List<String> data = new ArrayList<>();
        if (!platform.isEmpty()) {
            for (Map map : platform) {
                data.add(map.get("platformId").toString());
            }
        }
        LambdaQueryWrapper<VolumeInfoDO> queryWrapperX = new LambdaQueryWrapperX<VolumeInfoDO>();

        if (!data.isEmpty()) {
            queryWrapperX.in(VolumeInfoDO::getPlatformId, data);
        } else {
            return List.of();
        }
        return selectList(queryWrapperX);

    }

    default List<VolumeInfoDO> selectListByPlatformIds(List<Long> platformIds) {
        LambdaQueryWrapper<VolumeInfoDO> queryWrapperX = new LambdaQueryWrapperX<VolumeInfoDO>();

        if (!platformIds.isEmpty()) {
            queryWrapperX.in(VolumeInfoDO::getPlatformId, platformIds);
        } else {
            return List.of();
        }
        return selectList(queryWrapperX);

    }

    Long getAttachableVmCount();

    List<VolumeAttachableVmDTO> getAllVolumeAttachableVm();

    void addVolumeAttachableVmBatch(@Param("shardingData") List<VolumeAttachableVmDTO> shardingData);

    void updateVolumeAttachableVmBatch(@Param("shardingData") List<VolumeAttachableVmDTO> shardingData);

    void delVolumeAttachableVms(@Param("deleteTarget") List<VolumeAttachableVmDTO> deleteTarget);

    List<VolumeInfoRespVO> getVolumeAttachableVmByHostUuid(@Param("mpPage")IPage<VolumeInfoRespVO> mpPage,
                                                           @Param("hostUuid")String hostUuid,
                                                           @Param("queryData")String queryData);

    void updateVolumeBatch(@Param("updateReqVOs")List<VolumeDTO> updateReqVOs);

    List<VolumeInfoDO> getNotUsedVolum(@Param("id")Long id,@Param("typeCode")String typeCode);

    void delVolumes(@Param("deleteTarget")List<VolumeDTO> deleteTarget);

    void delVolumeSnapshots(@Param("deleteTarget")List<VolumeSnapshotDTO> deleteTarget);

    void delVolumesByplatform(@Param("platformId") Long platformId);

    void delVolumeAttachableVmByplatform(@Param("platformId") Long platformId);

    void delVolumeByInstanceUuid(@Param("deleteList")List<String> deleteList);

    List<VolumeDTO> getVolumeByPlatformId(@Param("id") Long id);

    String getVolumeByVmUuid(@Param("domainId")String domainId);

    List<VolumeDTO> getVolumesByPlatformId(@Param("id")Long id);

    Integer getVolumeCountByStorageUuid(@Param("storageUuid")String storageUuid);

    Map<String, Object> getVolumeStatusCount(@Param("tenantIds") List<String> tenantIds, @Param("platformId") Long platformId);

    @TenantIgnore
    List<VolumeDTO> getvolumeByList(@Param("list") List<String> list);

    @TenantIgnore
    List<VolumeInfoDO> getByPlatformIdAndTags(@Param("platformIds") List<Long> platformIds, @Param("tagNames")List<String> tagNames);

    @TenantIgnore
    List<TaggableDO> getListByTag();
}
