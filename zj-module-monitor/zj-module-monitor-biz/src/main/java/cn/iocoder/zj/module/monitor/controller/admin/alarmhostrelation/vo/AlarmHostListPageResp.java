package cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;


@Schema(description = "管理后台 - 执行告警的主机分页列表")
@Data
@ToString(callSuper = true)
public class AlarmHostListPageResp {
    @Schema(description = "关系ID")
    private String relationId;

    @Schema(description = "设备uuid")
    private String hostUuid;

    @Schema(description = "设备名称")
    private String hostName;

    @Schema(description = "设备ip")
    private String ip;

    @Schema(description = "平台配置ID")
    private Long platformId;

    @Schema(description = "平台配置名称")
    private String platformName;

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "租户名")
    private String tenantName;

    @Schema(description = "启用状态，0启用，1停用")
    private Integer  status;

    @Schema(description = "关联状态，0未关联，1已关联")
    private Integer  relationState;
}
