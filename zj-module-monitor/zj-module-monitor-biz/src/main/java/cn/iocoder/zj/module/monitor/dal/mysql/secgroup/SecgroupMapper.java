package cn.iocoder.zj.module.monitor.dal.mysql.secgroup;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.api.secgroup.dto.SecgroupRespDto;
import cn.iocoder.zj.module.monitor.controller.admin.secgroup.vo.SecgroupExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.secgroup.vo.SecgroupPageReqVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.secgroup.SecgroupDO;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 安全组 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SecgroupMapper extends BaseMapperX<SecgroupDO> {

    default PageResult<SecgroupDO> selectPage(SecgroupPageReqVO reqVO) {
        LambdaQueryWrapper<SecgroupDO> wrapper = new LambdaQueryWrapperX<SecgroupDO>()
                .inIfPresent(SecgroupDO::getId, reqVO.getInPks())
                .eqIfPresent(SecgroupDO::getUuid, reqVO.getUuid())
                .likeIfPresent(SecgroupDO::getName, reqVO.getName())
                .eqIfPresent(SecgroupDO::getStatus, reqVO.getStatus())
                .eqIfPresent(SecgroupDO::getIsPublic, reqVO.getIsPublic())
                .eqIfPresent(SecgroupDO::getPublicScope, reqVO.getPublicScope())
                .eqIfPresent(SecgroupDO::getPublicSrc, reqVO.getPublicSrc())
                .eqIfPresent(SecgroupDO::getIsDirty, reqVO.getIsDirty())
                .eqIfPresent(SecgroupDO::getCloudregionId, reqVO.getCloudregionId())
                .eqIfPresent(SecgroupDO::getVpcId, reqVO.getVpcId())
                .eqIfPresent(SecgroupDO::getGlobalvpcId, reqVO.getGlobalvpcId())
                .eqIfPresent(SecgroupDO::getDescription, reqVO.getDescription())
                .betweenIfPresent(SecgroupDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(SecgroupDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(SecgroupDO::getPlatformName, reqVO.getPlatformName())
                .inIfPresent(SecgroupDO::getUuid, reqVO.getUuids())
                .between(
                        ObjectUtil.isNotEmpty(reqVO.getStartTime()) && ObjectUtil.isNotEmpty(reqVO.getEndTime()),
                        SecgroupDO::getCreateTime,
                        reqVO.getStartTime(),
                        reqVO.getEndTime()
                );

        // 排序逻辑
        if (StringUtils.isNotBlank(reqVO.getSortBy()) && StringUtils.isNotBlank(reqVO.getSortDirection())) {
            if ("createTime".equalsIgnoreCase(reqVO.getSortBy())) {
                if ("asc".equalsIgnoreCase(reqVO.getSortDirection())) {
                    wrapper.orderByAsc(SecgroupDO::getCreateTime);
                } else {
                    wrapper.orderByDesc(SecgroupDO::getCreateTime);
                }
            } else {
                wrapper.orderByDesc(SecgroupDO::getId);
            }
        } else {
            wrapper.orderByDesc(SecgroupDO::getId);
        }

        return selectPage(reqVO, wrapper);
    }

    default List<SecgroupDO> selectList(SecgroupExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SecgroupDO>()
                .eqIfPresent(SecgroupDO::getUuid, reqVO.getUuid())
                .likeIfPresent(SecgroupDO::getName, reqVO.getName())
                .eqIfPresent(SecgroupDO::getStatus, reqVO.getStatus())
                .eqIfPresent(SecgroupDO::getIsPublic, reqVO.getIsPublic())
                .eqIfPresent(SecgroupDO::getPublicScope, reqVO.getPublicScope())
                .eqIfPresent(SecgroupDO::getPublicSrc, reqVO.getPublicSrc())
                .eqIfPresent(SecgroupDO::getIsDirty, reqVO.getIsDirty())
                .eqIfPresent(SecgroupDO::getCloudregionId, reqVO.getCloudregionId())
                .eqIfPresent(SecgroupDO::getVpcId, reqVO.getVpcId())
                .eqIfPresent(SecgroupDO::getGlobalvpcId, reqVO.getGlobalvpcId())
                .eqIfPresent(SecgroupDO::getDescription, reqVO.getDescription())
                .betweenIfPresent(SecgroupDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(SecgroupDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(SecgroupDO::getPlatformName, reqVO.getPlatformName())
                .orderByDesc(SecgroupDO::getId));
    }

    @TenantIgnore
    List<SecgroupDO> getSecgroupListByHostUuid(String uuid);

    @TenantIgnore
    List<SecgroupRespDto> getSecgroupListByHostUuids(List<String> uuids);
}
