package cn.iocoder.zj.module.monitor.api.storagepool;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.storagepool.dto.StoragePoolCreateRespDTO;
import cn.iocoder.zj.module.monitor.convert.storagepool.StoragePoolConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.storagepool.StoragePoolDO;
import cn.iocoder.zj.module.monitor.service.storagepool.StoragePoolService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class StoragePoolApiImpl implements StoragePoolApi {
    @Resource
    private StoragePoolService storagePoolService;

    @Override
    public CommonResult<Boolean> adds(List<StoragePoolCreateRespDTO> reqDTO) {
        List<StoragePoolDO> list =reqDTO.stream().map(item->{
            StoragePoolDO storagePoolDO = new StoragePoolDO();
            BeanUtils.copyProperties(item, storagePoolDO);
            return storagePoolDO;
        }).collect(Collectors.toList());
        storagePoolService.createStoragePoolList(list);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> updates(List<StoragePoolCreateRespDTO> reqDTO) {
        List<StoragePoolDO> list =reqDTO.stream().map(item->{
            StoragePoolDO storagePoolDO = new StoragePoolDO();
            BeanUtils.copyProperties(item, storagePoolDO);
            return storagePoolDO;
        }).collect(Collectors.toList());
        storagePoolService.updateStoragePoolList(list);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<List<StoragePoolCreateRespDTO>> getStoragePoolByPlatformId(Long platformId) {
        return CommonResult.success(storagePoolService.getStoragePoolByPlatformId(platformId));
    }

    @Override
    public int deletes(List<StoragePoolCreateRespDTO> reqDTO) {
        List<StoragePoolDO> list = StoragePoolConvert.INSTANCE.convertCreateList(reqDTO);
        return storagePoolService.deleteStoragePoolList(list);
    }
}
