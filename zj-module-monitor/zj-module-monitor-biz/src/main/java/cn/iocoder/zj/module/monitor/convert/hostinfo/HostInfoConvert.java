package cn.iocoder.zj.module.monitor.convert.hostinfo;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.controller.admin.hostinfo.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostinfo.HostInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * 云主机基本信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface HostInfoConvert {

    HostInfoConvert INSTANCE = Mappers.getMapper(HostInfoConvert.class);

    HostInfoDO convert(HostInfoCreateReqVO bean);

    HostInfoDO convert(HostInfoUpdateReqVO bean);

    HostInfoRespVO convert(HostInfoDO bean);

    List<HostInfoRespVO> convertList(List<HostInfoDO> list);

    List<HostInfoDO> convertCreateList(List<HostInfoRespCreateReqDTO> list);

    PageResult<HostInfoRespVO> convertPage(PageResult<HostInfoDO> page);

    List<HostInfoExcelVO> convertList02(List<HostInfoDO> list);
    List<HostInfoRespCreateReqDTO> convertListDoToDto(List<HostInfoDO> list);

    HostInfoUpdateReqVO convertToReqVO(HostInfoRespCreateReqDTO hostInfoRespCreateReqDTOList);

    List<HostInfoRpcVO> convertCreateListReq(List<HostInfoRespCreateReqDTO> reqDTO);

    HostInfoRespCreateReqDTO convertOneDO(HostInfoDO hostInfoDO);
}
