package cn.iocoder.zj.module.monitor.dal.dataobject.alarminfo;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 监控告警详情 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_alarm_info")
@KeySequence("monitor_alarm_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmInfoDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 处理人
     */
    private String processors;
    /**
     * 处理时间
     */
    private LocalDateTime processingTime;
    /**
     * 处理详情
     */
    private String processingDetails;
    /**
     * 文件地址
     */
    private String fileAddress;
    /**
     * 处理状态
     */
    private Integer processingType;
    /**
     * 监控告警id
     */
    private String alarmId;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;

}
