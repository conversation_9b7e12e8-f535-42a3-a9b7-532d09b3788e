package cn.iocoder.zj.module.monitor.controller.admin.gatherlogdetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;

@Schema(description = "管理后台 - 告警日志 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GatherLogdetailRespVO extends GatherLogdetailBaseVO {

    @Schema(description = "主键", required = true)
    private Long id;

    @Schema(description = "创建时间", required = true)
    private Date createTime;
    @Schema(description = "告警类型")
    private String alarmType;
    @Schema(description = "资源类型名称")
    private String deviceName;
    @Schema(description = "资源类型")
    private String deviceType;
    @Schema(description = "资源id")
    private String deviceId;
}
