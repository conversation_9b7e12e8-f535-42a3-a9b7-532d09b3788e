package cn.iocoder.zj.module.monitor.service.alarmconfig;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.exception.ServiceException;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.util.object.BeanUtils;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmConfigRespDTO;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo.*;
import cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo.AlarmHostListPageResp;
import cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo.AlarmHostRelationPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo.AlarmHostRelationReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.alarmnotice.vo.AlarmNoticeBaseVO;
import cn.iocoder.zj.module.monitor.convert.alarmconfig.AlarmConfigConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmconfig.AlarmConfigDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmhostrelation.AlarmHostRelationDO;
import cn.iocoder.zj.module.monitor.dal.mysql.alarmconfig.AlarmConfigMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.alarmdoriseinfo.AlarmDorisDO;
import cn.iocoder.zj.module.monitor.dal.mysql.alarmhostrelation.AlarmHostRelationMapper;
import cn.iocoder.zj.module.monitor.dal.redis.zstack.AlarmInfoRedisDAO;
import cn.iocoder.zj.module.monitor.util.StringUtil;
import cn.iocoder.zj.module.system.api.dict.DictDataApi;
import cn.iocoder.zj.module.system.api.dict.dto.DictDataRespDTO;
import cn.iocoder.zj.module.system.api.mail.MailSendApi;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.sms.SmsSendApi;
import cn.iocoder.zj.module.system.api.tenant.TenantApi;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.alerter.Alert;
import org.apache.hertzbeat.common.entity.alerter.AlertConverge;
import org.frameworkset.elasticsearch.ElasticSearchHelper;
import org.frameworkset.elasticsearch.client.ClientInterface;
import org.frameworkset.elasticsearch.entity.ESDatas;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.singleton;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.*;
import static cn.iocoder.zj.module.system.enums.ErrorCodeConstants.*;

/**
 * 告警配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Component
@Slf4j
public class AlarmConfigServiceImpl implements AlarmConfigService {

    @Resource
    private AlarmConfigMapper alarmConfigMapper;

    @Resource
    private AlarmInfoRedisDAO alarmInfoRedisDAO;

    @Resource
    private DictDataApi dictDataApi;
    @Resource
    private SmsSendApi smsSendApi;
    @Resource
    private MailSendApi mailSendApi;
    @Resource
    private AlarmHostRelationMapper alarmHostRelationMapper;
    @Resource
    private RoleApi roleApi;
    @Resource
    private PermissionApi permissionApi;

    @Resource
    private TenantApi tenantApi;

    @Resource
    private PlatformconfigApi platformconfigApi;
    @Resource
    private AdminUserApi adminUserService;
    @Override
    public Long createAlarmConfig(AlarmConfigCreateReqVO createReqVO) {
        LoginUser user = SecurityFrameworkUtils.getLoginUser();
        // 插入
        AlarmConfigDO alarmConfig = AlarmConfigConvert.INSTANCE.convert(createReqVO);
        //从字典中获取准确的label值
        CommonResult<DictDataRespDTO> dict = dictDataApi.getDictData("monitor_alarm_sourceType", alarmConfig.getSourceType());
        String context = dict.getData().getLabel() + alarmConfig.getDictLabelName() + alarmConfig.getAlarmRule() + alarmConfig.getAlarmVal() + alarmConfig.getUnit();
        alarmConfig.setContext(context);
        alarmConfig.setTenantId(user.getTenantId());
        alarmConfig.setEnabled(createReqVO.getEnabled());
        alarmConfigMapper.insert(alarmConfig);
        //告警配置存到Redis
        setAlarmRedisInfo();
        // 返回
        return alarmConfig.getId();
    }


    @Override
    @TenantIgnore
    @Transactional(rollbackFor = Exception.class)
    public void updateAlarmConfig(AlarmConfigUpdateReqVO updateReqVO) {
        LoginUser user = SecurityFrameworkUtils.getLoginUser();
        // 校验存在
        validateAlarmConfigExists(updateReqVO.getId());
        // 更新
        AlarmConfigDO updateObj = AlarmConfigConvert.INSTANCE.convert(updateReqVO);
        //从字典中获取准确的label值
        updateObj.setEnabled(updateReqVO.getEnabled());
        AlarmConfigDO oldAlarmConfig = alarmConfigMapper.getAlarmConfigById(updateReqVO.getId());
        updateObj.setCreateTime(oldAlarmConfig.getCreateTime());
        CommonResult<DictDataRespDTO> dict = dictDataApi.getDictData("monitor_alarm_sourceType", updateObj.getSourceType());
        String context = dict.getData().getLabel() + updateObj.getDictLabelName() + updateObj.getAlarmRule() + updateObj.getAlarmVal() + updateObj.getUnit();
        updateObj.setContext(context);
        Long newId = alarmConfigMapper.alarmConfigMaxId();
        alarmConfigMapper.updateAlarmConfig(newId, updateObj);
        alarmConfigMapper.updateAlarmHostRelation(newId, updateObj);
        alarmConfigMapper.updateAlarmNotice(newId, updateObj);
        setAlarmRedisInfo();
        setAlarmRelationRedisInfo();
    }

    @Override
    public void deleteAlarmConfig(Long id) {
        AlarmConfigDO alarmConfigDO = alarmConfigMapper.selectById(id);
        // 校验存在
        validateAlarmConfigExists(id);
        // 校验是否被使用
        validateAlarmConfigUsed(id);
        // 删除
        alarmConfigMapper.deleteById(id);
        //删除该配置下的云主机关系数据
        alarmHostRelationMapper.deleteByAlarmConfigId(id);
        setAlarmRedisInfo();
    }

    private void setAlarmRedisInfo() {
        LambdaQueryWrapperX<AlarmConfigDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(AlarmConfigDO::getDeleted,0);
        wrapper.eqIfPresent(AlarmConfigDO::getEnabled,1);
        List<AlarmConfigDO> alarmConfigDOList = alarmConfigMapper.selectList(wrapper);
        alarmInfoRedisDAO.setAlarmConfigList("alarm_config", alarmConfigDOList);
    }

    private void setAlarmRelationRedisInfo() {
        List<AlarmHostRelationDO> alarmHostRelationDOList = this.alarmHostRelationMapper.getRelationListInUsed();
        alarmInfoRedisDAO.setAlarmHostRelationList("alarm_host_relation", alarmHostRelationDOList);
    }

    private void validateAlarmConfigExists(Long id) {
        if (alarmConfigMapper.selectById(id) == null) {
            throw exception(ALARM_CONFIG_NOT_EXISTS);
        }
    }

    private void validateAlarmConfigUsed(Long id) {
        AlarmConfigDO alarmConfigDO = alarmConfigMapper.selectById(id);
        if (alarmHostRelationMapper.getRelationCountByAlarmId(id.toString(),alarmConfigDO.getSourceType()) > 0) {
            if (alarmConfigDO.getSourceType().equals("monitor_alarm_host")) {
                throw exception(ALARM_CONFIG_HOST_USED);
            } else if (alarmConfigDO.getSourceType().equals("monitor_alarm_hardware")) {
                throw exception(ALARM_CONFIG_HARDWARE_USED);
            } else if (alarmConfigDO.getSourceType().equals("monitor_alarm_disk")) {
                throw exception(ALARM_CONFIG_DISK_USED);
            }
        }
    }

    @Override
    public AlarmConfigDO getAlarmConfig(Long id) {
        return alarmConfigMapper.selectById(id);
    }

    @Override
    public List<AlarmConfigDO> getAlarmConfigList(Collection<Long> ids,String type) {
        if(CollectionUtil.isEmpty(ids) && StringUtil.isNotEmpty(type)){
            //这里只是用来接收查询参数
            AlarmConfigPageReqVO pageReqVO = new AlarmConfigPageReqVO();
            if(StringUtil.isNotEmpty(type)) {
                pageReqVO.setSourceType(type);
                List<String> list = Arrays.asList(type.split(","));
                pageReqVO.setSourceTypeList(list);
            }
            LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
            AdminUserRespDTO adminUser = adminUserService.getUser(loginUser.getId()).getData();
            Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
            AdminUserRespDTO adminUserRespDTO = adminUserService.getUser(loginUser.getId()).getData();
            if (!roleApi.hasAnySuperAdmin(roleIds) && !StringUtil.isNotEmpty(pageReqVO.getMaintainerIds())) {
                if(StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
                    pageReqVO.setTenantId(Arrays.asList(String.valueOf(adminUserRespDTO.getTenantId()).split(",")));
                }else {
                    pageReqVO.setTenantId(Arrays.asList(adminUserRespDTO.getServiceTenantId().split(",")));
                }
            }else {
                pageReqVO.setTenantId(null);
            }
            Boolean isRootOperation = roleApi.getIsRootOperation(loginUser.getId());
            if (isRootOperation) {
                // 判断是否为运维人员
                if (StringUtil.isNotEmpty(pageReqVO.getMaintainerIds())) {
                    List<Long> tenantIds = Arrays.stream(pageReqVO.getMaintainerIds().split(","))
                            .map(Long::parseLong)
                            .collect(Collectors.toList());
                    pageReqVO.setTenantList(tenantIds);
                }

                Set<Long> maintenanceRoles = roleApi.getRoleIdByCode("operation_maintenance").getData();
                Set<Long> managerRoles = roleApi.getRoleIdByCode("om_manager").getData();

                boolean isMaintenance = roleIds.stream().anyMatch(maintenanceRoles::contains);
                boolean isManager = roleIds.stream().anyMatch(managerRoles::contains);

                List<Long> tenantIdList = new ArrayList<>();

                if (isManager) {
                    tenantIdList = tenantApi.getTenantIdsByPlatform(adminUser.getTenantId()).getData();
                } else if (isMaintenance) {
                    AdminUserRespDTO userRespDTO = adminUserService.getUser(loginUser.getId()).getData();
                    String serviceTenantId = userRespDTO.getServiceTenantId();
                    if (StringUtil.isNotEmpty(serviceTenantId)) {
                        tenantIdList = Arrays.stream(serviceTenantId.split(","))
                                .map(Long::parseLong)
                                .collect(Collectors.toList());
                    }
                }

                if (tenantIdList.isEmpty()) {
                    if (!isRootOperation){
                        return new ArrayList<>();
                    }
                }

                if (pageReqVO.getTenantList() != null && !pageReqVO.getTenantList().isEmpty()) {
                    List<Long> tenantList = pageReqVO.getTenantList();
                    List<Long> intersection = tenantIdList.stream()
                            .filter(tenantList::contains)
                            .distinct()
                            .collect(Collectors.toList());

                    if (intersection.isEmpty()) {
                        return new ArrayList<>();
                    }
                    pageReqVO.setTenantList(intersection);
                } else {
                    pageReqVO.setTenantList(tenantIdList);
                }
                pageReqVO.setTenantId(null);
            } else {
                pageReqVO.setTenantList(null);
            }
            return alarmConfigMapper.getAlarmConfigList(pageReqVO);
        }else{
            if(CollectionUtil.isNotEmpty(ids)) {
                return alarmConfigMapper.selectBatchIds(ids);
            }else {
                return new ArrayList<>();
            }
        }
    }

    @Override
    public PageResult<AlarmConfigRespVO> getAlarmConfigPage(AlarmConfigPageReqVO pageReqVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        AdminUserRespDTO adminUser = adminUserService.getUser(loginUser.getId()).getData();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        //判断是否为租户管理员（租户）
        Set<Long> tenantAdminRoles = roleApi.getRoleIdByCode("tenant_admin").getData();
        boolean tenantAdmin = roleIds.stream().anyMatch(tenantAdminRoles::contains);
        //判断是否为超管或超管下创建的运维管理、运维人员
        Boolean isRootOperation = roleApi.getIsRootOperation(loginUser.getId());
        if (tenantAdmin){
            pageReqVO.setTenantList(Arrays.asList(adminUser.getTenantId()));
        }else if (isRootOperation){
            if (StringUtil.isNotEmpty(pageReqVO.getMaintainerIds())) {
                List<Long> tenantIds = Arrays.stream(pageReqVO.getMaintainerIds().split(","))
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
                pageReqVO.setTenantList(tenantIds);
            } else {
                Set<Long> maintenanceRoles = roleApi.getRoleIdByCode("operation_maintenance").getData();
                Set<Long> managerRoles = roleApi.getRoleIdByCode("om_manager").getData();
                Set<Long> adminRoles = roleApi.getRoleIdByCode("super_admin").getData();
                Set<Long> csAdminRoles = roleApi.getRoleIdByCode("cs_admin").getData();
                boolean isMaintenance = roleIds.stream().anyMatch(maintenanceRoles::contains);
                boolean isManager = roleIds.stream().anyMatch(managerRoles::contains);
                boolean isAdmin = roleIds.stream().anyMatch(adminRoles::contains);
                boolean csAdmin = roleIds.stream().anyMatch(csAdminRoles::contains);
                List<Long> tenantIdList = new ArrayList<>();
                if (isManager || isAdmin ||csAdmin) {
                    tenantIdList = tenantApi.getTenantIdsByPlatform(adminUser.getTenantId()).getData();
                } else if (isMaintenance) {
                    AdminUserRespDTO userRespDTO = adminUserService.getUser(loginUser.getId()).getData();
                    String serviceTenantId = userRespDTO.getServiceTenantId();
                    if (StringUtil.isNotEmpty(serviceTenantId)) {
                        tenantIdList = Arrays.stream(serviceTenantId.split(","))
                                .map(Long::parseLong)
                                .collect(Collectors.toList());
                    }
                }
                    pageReqVO.setTenantList(tenantIdList);
            }
        }else {
            return new PageResult<>();
        }

        PageParam pageParam = new PageParam().setPageNo(pageReqVO.getPageNo()).setPageSize(pageReqVO.getPageSize());
        IPage<AlarmConfigRespVO> mpPage = MyBatisUtils.buildPage(pageParam);
        if (!StringUtil.isNullOrEmpty(pageReqVO.getAlarmLevel())) {
            pageReqVO.setLevelList(Arrays.asList(pageReqVO.getAlarmLevel().split(",")));
        }
        if (!StringUtil.isNullOrEmpty(pageReqVO.getSourceType())) {
            pageReqVO.setSourceTypeList(Arrays.asList(pageReqVO.getSourceType().split(",")));
        }
        if (jodd.util.StringUtil.isNotEmpty(pageReqVO.getStartTime()) && jodd.util.StringUtil.isNotEmpty(pageReqVO.getEndTime())) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            try {
                date = sdf.parse(pageReqVO.getEndTime());
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(date);
                // 把日期设置为当天最后一秒
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                date = calendar.getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            pageReqVO.setEndTime(sdf.format(date));
        }
        if (StringUtil.isNotEmpty(pageReqVO.getSortBy())){
            String regex = "([a-z])([A-Z]+)";
            String replacement = "$1_$2";
            String output = pageReqVO.getSortBy().replaceAll(regex, replacement).toLowerCase();
            pageReqVO.setSortBy(output);
        }

        return new PageResult<>(alarmConfigMapper.selectAlarmConfigPage(mpPage, pageReqVO), mpPage.getTotal());
    }

    @Override
    public List<AlarmConfigDO> getAlarmConfigList(AlarmConfigExcelReqVO exportReqVO, Collection<Long> d, LoginUser loginUser) {


        if(ObjectUtil.isEmpty(exportReqVO.getIds())){
            exportReqVO.setIds(null);
        }
        if(ObjectUtil.isEmpty(exportReqVO.getAlarmName())){
            exportReqVO.setAlarmName(null);
        }
        if(ObjectUtil.isEmpty(exportReqVO.getSourceType())){
            exportReqVO.setSourceType(null);
        }
        if(ObjectUtil.isEmpty(exportReqVO.getAlarmLevel())){
            exportReqVO.setAlarmLevel(null);
        }
        if(ObjectUtil.isEmpty(exportReqVO.getCreateTime())){
            exportReqVO.setCreateTime(null);
        }
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            d.add(loginUser.getTenantId());
        }
        return alarmConfigMapper.selectList(exportReqVO,d);
    }

    @Override
    public List<AlarmConfigDO> getAlarmList() {
        return alarmConfigMapper.selectList();
    }

    @Override
    public PageResult<AlarmInfoRespVo> getAlarmInfoPage(AlarmInfoReqVo pageReqVO) {
        LoginUser user = SecurityFrameworkUtils.getLoginUser();
        AdminUserRespDTO adminUserRespDTO = adminUserService.getUser(user.getId()).getData();
        PageParam pageParam = new PageParam().setPageNo(pageReqVO.getPageNo()).setPageSize(pageReqVO.getPageSize());
        IPage<AlarmInfoRespVo> mpPage = MyBatisUtils.buildPage(pageParam);
        if(StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
            pageReqVO.setTenantId(Arrays.asList(String.valueOf(adminUserRespDTO.getTenantId()).split(",")));
        }else {
            pageReqVO.setTenantId(Arrays.asList(adminUserRespDTO.getServiceTenantId().split(",")));
        }
        return new PageResult<>(alarmConfigMapper.getAlarmInfoPage(mpPage, pageReqVO), mpPage.getTotal());
    }

    @Override
    public AlarmInfoRespVo getAlarmInfo(Long id) {
        return alarmConfigMapper.getAlarmInfo(id);
    }

    @Override
    public PageResult<AlarmInfoRespVo> getAlarmHostInfoDetail(AlarmHostRelationPageReqVO reqVO) {
        PageResult<AlarmInfoRespVo> pageResult = new PageResult<>();
        if (reqVO.getAlarmId() == null) {
            return pageResult;
        }
        ClientInterface clientUtil = ElasticSearchHelper.getConfigRestClientUtil("esmapper/ESAlarmInfoMapper.xml");
        //设置查询条件
        Map searchFields = new HashMap<>();
        String productsName = "**";
        searchFields.put("id", reqVO.getAlarmId());
        if (StringUtil.isNotEmpty(reqVO.getProductsName())) {
            productsName = "*" + reqVO.getProductsName() + "*";
        }
        searchFields.put("pageNo", (reqVO.getPageNo() - 1) * reqVO.getPageSize());
        searchFields.put("pageSize", reqVO.getPageSize());
        searchFields.put("productsName", productsName);
        ESDatas<Map> data = new ESDatas<>();
        try {
            data = clientUtil.searchList("alarm_info/_search", "alarmHostPage", searchFields, Map.class);
        } catch (Exception e) {
            log.info("连接超时：" + e);
        }
        //获取主机告警记录
        List<Map<String, Object>> result = data.getAggregationBuckets("alarmHost");
        List<AlarmInfoRespVo> alarmInfoRespVoList = new ArrayList<>();
        //提取查询结果
        if (result != null && result.size() > 0) {
            for (int i = 0; i < result.size(); i++) {
                Map<String, Object> map = dealResult(result.get(i));
                String jsonStr = JSONObject.toJSONString(map);
                AlarmInfoRespVo alarmInfoRespVo = JSONObject.parseObject(jsonStr, AlarmInfoRespVo.class);
                alarmInfoRespVo.setAlarmNum(Long.parseLong(result.get(i).get("doc_count").toString()));
                alarmInfoRespVoList.add(alarmInfoRespVo);
            }
        }
        pageResult.setList(alarmInfoRespVoList);
        pageResult.setTotal(Long.valueOf(data.getAggregations().get("total").get("value").toString()));
        return pageResult;
    }

    @Override
    public AlarmInfoRespVo getAlarmBaseInfoDetail(Long alarmId) {
        ClientInterface clientUtil = ElasticSearchHelper.getConfigRestClientUtil("esmapper/ESAlarmInfoMapper.xml");
        //设置查询条件
        Map searchFields = new HashMap<>();
        String productsName = "**";
        searchFields.put("id", alarmId.toString());
        ESDatas<Map> data = new ESDatas<>();
        try {
            //获取主机告警记录
            data = clientUtil.searchList("alarm_info/_search", "alarmBaseInfo", searchFields, Map.class);
        } catch (Exception e) {
            log.info("连接超时：" + e);
        }

        List<Map<String, Object>> result = data.getAggregationBuckets("alarmConfig");
        //提取查询结果
        Map<String, Object> map = dealResult(result.get(0));
        String jsonStr = JSONObject.toJSONString(map);
        AlarmInfoRespVo alarmInfoRespVo = JSONObject.parseObject(jsonStr, AlarmInfoRespVo.class);
        alarmInfoRespVo.setAlarmNum(Long.parseLong(result.get(0).get("doc_count").toString()));
        return alarmInfoRespVo;
    }

    @Override
    public List<AlarmConfigDO> getListByTenantId(Long tenantId) {
        return alarmConfigMapper.selectList("tenant_id", tenantId);
    }

    @Override
    public void insertBatch(List<AlarmConfigRespDTO> toInsertAlarmConfig) {
        List<AlarmConfigDO> alarmConfigDOList = AlarmConfigConvert.INSTANCE.DTOConvertToDo(toInsertAlarmConfig);
        alarmConfigMapper.insertBatch(alarmConfigDOList);
    }

    @Override
    public Map<String, String> getAlarmNoticeByConfigId(Long configId) {
        AlarmNoticeBaseVO alarmNotice = alarmConfigMapper.getAlarmNoticeByConfigId(configId);
        Map<String, String> result = new HashMap<>();
        if (alarmNotice.getTemplateType().equals("mail")) {
            result = mailSendApi.getTemplate(alarmNotice.getTemplateId()).getData();
            result.put("templateType", "mail");
            result.put("mail", alarmNotice.getMail());
        } else if (alarmNotice.getTemplateType().equals("sms")) {
            result = smsSendApi.getTemplate(alarmNotice.getTemplateId()).getData();
            result.put("templateType", "sms");
            result.put("mobile", alarmNotice.getMobile());
        }
        return result;
    }

    @Override
    public Map<String, Object> getAlarmInfoCountInWeek(Long platformId) {

        LoginUser user = SecurityFrameworkUtils.getLoginUser();
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> towDaysGrowth = new ArrayList<>();
        List<Map> platformMapList = platformconfigApi.getPlatformSelectList(String.valueOf(user.getTenantId())).getData();
        if(CollectionUtil.isNotEmpty(platformMapList)) {
        List<String> platformIdList = platformMapList.stream()
                .map(platformMap -> String.valueOf(platformMap.get("platformId")))
                .collect(Collectors.toList());
            result = alarmConfigMapper.getAlarmCount(platformId, platformIdList);
            towDaysGrowth = alarmConfigMapper.getTowDaysGrowth(platformId, platformIdList);
        }

        if(ObjectUtil.isNotEmpty(result)){
//            towDaysGrowth只有两条数据
            Map<String, Object> today = towDaysGrowth.get(0).get("dayStr").equals("yesterday")?towDaysGrowth.get(1):towDaysGrowth.get(0);
            Map<String, Object> yesterday = towDaysGrowth.get(0).get("dayStr").equals("yesterday")?towDaysGrowth.get(0):towDaysGrowth.get(1);
            result.put("level1Growth",StringUtil.toLong(today.get("level1Growth"))-StringUtil.toLong(yesterday.get("level1Growth")));
            result.put("level2Growth",StringUtil.toLong(today.get("level2Growth"))-StringUtil.toLong(yesterday.get("level2Growth")));
            result.put("level3Growth",StringUtil.toLong(today.get("level3Growth"))-StringUtil.toLong(yesterday.get("level3Growth")));
            result.put("isSolvedGrowth",StringUtil.toLong(today.get("isSolvedGrowth"))-StringUtil.toLong(yesterday.get("isSolvedGrowth")));
            return result;
        }else {
            Map<String, Object> defaultMap = new HashMap<>();
            defaultMap.put("level1",0);
            defaultMap.put("level2",0);
            defaultMap.put("level3",0);
            defaultMap.put("isSolved",0);
            defaultMap.put("level1Growth",0);
            defaultMap.put("level2Growth",0);
            defaultMap.put("level3Growth",0);
            defaultMap.put("isSolvedGrowth",0);
            return defaultMap;
        }
    }

    @Override
    @DS("doris")
    public List<AlarmDorisReqDTO> getSilentTarget(AlertConverge converge) {
        return alarmConfigMapper.getSilentTarget(DateUtil.current(),converge);
    }

    @Override
    @TenantIgnore
    public void solvedAlarm(String alarmId) {
        alarmConfigMapper.solvedAlarm(alarmId);
    }

    @Override
    @TenantIgnore
    public void alarmWorkOrder(String alarmId) {
        alarmConfigMapper.alarmWorkOrder(alarmId);
    }

    @Override
    @TenantIgnore
    public void cleanWorkOrder(String alarmId) {
        alarmConfigMapper.cleanWorkOrder(alarmId);
    }

    @Override
    public List<AlarmInfoRespVo> getUnsolvedAlarms(Long limit) {
        LoginUser user = SecurityFrameworkUtils.getLoginUser();
        List<AlarmInfoRespVo> list = new ArrayList<>();
        if (limit == -1) {
            list = alarmConfigMapper.getUnsolvedAlarmsList(user.getTenantId());
        } else {
            list = alarmConfigMapper.getUnsolvedAlarms(user.getTenantId(),limit);
        }
        return list;
    }

    @Override
    public void updateIsRead(Collection<Long> ids) {
        alarmConfigMapper.updateIsRead(ids);
    }

    @Override
    public void updateIsSolved(Collection<Long> ids) {
        alarmConfigMapper.updateIsSolved(ids);
    }

    @Override
    public List<AlarmConfigRespVO> getAlarmConfigByUuid(String uuid) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        return alarmConfigMapper.getAlarmConfigByUuid(uuid,loginUser.getTenantId());
    }

    @Override
    public void addStateChangeAlarm(List<AlarmInfoRespVo> alarmRecordDTOList) {
        alarmConfigMapper.createAlarmRecord(alarmRecordDTOList);
    }

    @Override
    public List<AlarmHostListPageResp> getPreparingAddAlarmList(AlarmHostRelationReqVO reqVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        AdminUserRespDTO adminUserRespDTO = adminUserService.getUser(loginUser.getId()).getData();
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            if(StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
                reqVO.setTenantId(Arrays.asList(String.valueOf(adminUserRespDTO.getTenantId()).split(",")));
            }else {
                reqVO.setTenantId(Arrays.asList(adminUserRespDTO.getServiceTenantId().split(",")));
            }
        }
        AlarmConfigDO alarmConfigDO = alarmConfigMapper.selectById(reqVO.getAlarmId());
        return alarmConfigMapper.getPreparingAddAlarmList(reqVO, alarmConfigDO.getSourceType());
    }

    @Override
    public PageResult<AlarmHostListPageResp> getAlarmHostPage(AlarmHostRelationPageReqVO reqVO) {
        PageParam pageParam = new PageParam();
        IPage<AlarmHostListPageResp> mpPage = MyBatisUtils.buildPage(pageParam);
        AlarmConfigDO alarmConfigDO = alarmConfigMapper.selectById(reqVO.getAlarmId());
        return new PageResult<>(alarmConfigMapper.getAlarmHostPage(mpPage, reqVO, alarmConfigDO.getSourceType()), mpPage.getTotal());
    }

    @Override
    public List<AlarmInfoRespVo> getAlarmInfoList(AlarmInfoReqVo alarmInfoReqVo) {
        LoginUser user = SecurityFrameworkUtils.getLoginUser();
        AdminUserRespDTO adminUserRespDTO = adminUserService.getUser(user.getId()).getData();
        List<String> tenantIds = new ArrayList<>();
        if(StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
            tenantIds = Arrays.asList(String.valueOf(adminUserRespDTO.getTenantId()).split(","));
        }else {
            tenantIds = Arrays.asList(adminUserRespDTO.getServiceTenantId().split(","));
        }
        return alarmConfigMapper.getAlarmInfoList(tenantIds,alarmInfoReqVo);
    }

    @Override
    public void updateAlarmRecord(Long alertId, Integer isSolved) {
        alarmConfigMapper.updateAlarmRecordSolvedState(alertId, isSolved);
    }

    @Override
    @DS("doris")
    @TenantIgnore
    public void changeAlertSolvedState(Long alertId, Integer isSolved) {
        AlarmDorisDO alarmDorisDO = alarmConfigMapper.getAlarmDorisById(alertId);
        if(ObjectUtil.isNotEmpty(alarmDorisDO)){
            List<Long> idList = new ArrayList<>();
            idList.add(alarmDorisDO.getId());
            //doris库不能修改数据，只能删除后再插入新数据
            alarmConfigMapper.deleteAlarmDoris(idList);
            alarmDorisDO.setIsSolved(isSolved);
            List<AlarmDorisDO> alarmDorisDOList = new ArrayList<>();
            alarmDorisDOList.add(alarmDorisDO);
            alarmConfigMapper.createAlarmToDoris(alarmDorisDOList);
        }
    }

    @Override
    @DS("doris")
    @TenantIgnore
    public void createAlarmToDoris(List<AlarmDorisReqDTO> updateAlarmDoris,List<AlarmDorisDO> result) {
        if(ObjectUtil.isNotEmpty(updateAlarmDoris)){
            alarmConfigMapper.updateAlarmToDoris(updateAlarmDoris);
        }
        if(ObjectUtil.isNotEmpty(result)) {
            alarmConfigMapper.createAlarmToDoris(result);
        }
    }

    @Override
    public AlertConverge getAvailableAlertConverge() {
        return alarmConfigMapper.getAvailableAlertConverge(1,1);
    }

    @Override
    @DS("doris")
    public void updateCollectorAlarm(AlarmDorisDO result) {
        alarmConfigMapper.updateCollectorAlarm(result);
    }

    @Override
    @DS("doris")
    @TenantIgnore
    public Long getMaxAlertId() {
        return alarmConfigMapper.getMaxAlertId();
    }

    @Override
    public void createCollectorAlert(Map<String, Object> alertMap) {
        alarmConfigMapper.createCollectorAlert(alertMap);
    }

    @Override
    public void deletedCollectorAlert(Map<String, Object> alertMap) {
        alarmConfigMapper.deletedCollectorAlert(alertMap);
    }

    @Override
    public List<Map<String, Object>> getCollectorAlertsByPlatform(Collection<Long> platform) {
        return  alarmConfigMapper.getCollectorAlertsByPlatform(platform);
    }

    @Override
    public AlarmImportRespVO importAlarmConfigList(List<AlarmConfigExcelVO> list) {
        LoginUser user = SecurityFrameworkUtils.getLoginUser();
        //TODO 运维人员为租户创建数据时如何指定租户的tenantId
        if (CollUtil.isEmpty(list)) {
            throw exception(IMPORT_ALARM_CONFIG_IS_EMPTY);
        }
//        初始化返回信息
        AlarmImportRespVO respVO = AlarmImportRespVO.builder().createAlarmNames(new ArrayList<>())
                .failureAlarmNames(new LinkedHashMap<>()).build();
        list.forEach(alarmConfig -> {
            int index = 2;
            try {
                if (ObjectUtil.isEmpty(alarmConfig.getAlarmName())){
                    throw exception(ALARM_NAME_IS_EMPTY);
                }
                if(ObjectUtil.isEmpty(alarmConfig.getSourceType())){
                    throw exception(SOURCE_TYPE_IS_EMPTY);
                }
                if (ObjectUtil.isEmpty(alarmConfig.getAlarmLevel())){
                    throw exception(ALARM_LEVEL_IS_EMPTY);
                }
                if (ObjectUtil.isEmpty(alarmConfig.getDictLabelType())){
                    throw exception(DICT_LABEL_TYPE_IS_EMPTY);
                }
                if (ObjectUtil.isEmpty(alarmConfig.getDictLabelValue())){
                    throw exception(DICT_LABEL_VALUE_IS_EMPTY);
                }
            } catch (ServiceException ex) {
                    respVO.getFailureAlarmNames().put("第"+index+"行数据", ex.getMessage());
                return;
            }
            AlarmConfigCreateReqVO createReqVO = BeanUtils.toBean(alarmConfig, AlarmConfigCreateReqVO.class);
            createReqVO.setTenantId(user.getTenantId());
            //创建告警配置
            createAlarmConfig(createReqVO);
            respVO.getCreateAlarmNames().add(alarmConfig.getAlarmName());
            index++;
        });
        return respVO;
    }

    @Override
    public Map<String, Object> getAlarmSummary(Long platformId,Integer priority) {
        Map<String, Object> result = new HashMap<>();
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        long state = tenantApi.getStateByTenantId(loginUser.getTenantId()).getData();
        List<Map> platList = platformconfigApi.getPlatformSelectList(String.valueOf(loginUser.getTenantId())).getData();
        List<Long> platformIds = platList.stream()
                .map(map -> StringUtil.toLong(map.get("platformId")))
                .collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(platformIds)){
            List<String> app = new ArrayList<>();
            if (state == 1) {
                app.add("host");
                app.add("hardware");
                app.add("storage");
            }
            result = alarmConfigMapper.getAlarmSummary(platformIds, platformId, priority, app);
        }
        return result;
    }

    @Override
    @DS("doris")
    @TenantIgnore
    public void changeAlarmIsFallback(Long alertId, Integer isFallback) {
        AlarmDorisDO alarmDorisDO = alarmConfigMapper.getAlarmDorisById(alertId);
        if(ObjectUtil.isNotEmpty(alarmDorisDO)){
            List<Long> idList = new ArrayList<>();
            idList.add(alarmDorisDO.getId());
            //doris库不能修改数据，只能删除后再插入新数据
            alarmConfigMapper.deleteAlarmDoris(idList);
            alarmDorisDO.setIsFallback(isFallback);
            alarmDorisDO.setIsSolved(0);
            List<AlarmDorisDO> alarmDorisDOList = new ArrayList<>();
            alarmDorisDOList.add(alarmDorisDO);
            alarmConfigMapper.createAlarmToDoris(alarmDorisDOList);
        }
    }

    @Override
    public Alert getAlertInfoById(Long id) {
        return alarmConfigMapper.getAlertInfoById(id);
    }

    @Override
    public void updateEnabled(Long id,Integer enabled) {
        AlarmConfigDO alarmConfigDO = alarmConfigMapper.getAlarmConfigById(id);
        alarmConfigDO.setEnabled(enabled);
        alarmConfigMapper.updateAlarmConfig(id,alarmConfigDO);
        setAlarmRedisInfo();
        setAlarmRelationRedisInfo();
    }

    @Override
    public List<AlarmHostListPageResp> getAlarmAddedList(AlarmHostRelationReqVO reqVO) {
        LoginUser user = SecurityFrameworkUtils.getLoginUser();
        List<Map> platformMapList = platformconfigApi.getPlatformSelectList(String.valueOf(user.getTenantId())).getData();
        List<String> platformIdList = platformMapList.stream()
                .map(platformMap -> String.valueOf(platformMap.get("platformId")))
                .collect(Collectors.toList());
        AlarmConfigDO alarmConfigDO = alarmConfigMapper.selectById(reqVO.getAlarmId());
        return alarmConfigMapper.getAlarmAddedList(reqVO, alarmConfigDO.getSourceType(),platformIdList);
    }

    private Map<String, Object> dealResult(Object result) {
        Map<String, Object> map;
        if (result instanceof JSONArray) {
            map = JSONObject.parseObject(JSONObject.toJSONString(((JSONArray) result).get(0)), Map.class);
        } else {
            map = JSONObject.parseObject(JSONObject.toJSONString(result), Map.class);
        }
        Map<String, Object> resultMap = new HashMap<>();
        for (String key : map.keySet()) {
            if (key.equals("my_top_hits") || key.equals("hits")) {
                resultMap = dealResult(map.get(key));
            } else if (key.equals("_source")) {
                resultMap = JSONObject.parseObject(JSONObject.toJSONString(map.get("_source")), Map.class);
            }
        }
        return resultMap;
    }

    public static String convertDateStrFrom(String oldDateStr) {
        String result = "";
        Date date = DateUtil.offset(DateUtil.parse(oldDateStr), DateField.HOUR_OF_DAY, 8);
        if (date.compareTo(new Date()) == 1) {
            result = DateUtil.format(DateUtil.parse(oldDateStr), "yyyy-MM-dd HH:mm:ss");
        } else {
            result = DateUtil.format(date, "yyyy-MM-dd HH:mm:ss");
        }
        return result;
    }
}
