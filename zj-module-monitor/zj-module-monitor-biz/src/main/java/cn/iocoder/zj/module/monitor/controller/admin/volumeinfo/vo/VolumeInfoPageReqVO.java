package cn.iocoder.zj.module.monitor.controller.admin.volumeinfo.vo;

import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 云盘信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VolumeInfoPageReqVO extends PageParam {

    @Schema(description = "云盘uuid")
    private String uuid;

    @Schema(description = "平台id")
    private String platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "云盘名称")
    private String name;

    @Schema(description = "云盘详细描述")
    private String description;

    @Schema(description = "主存储uuid")
    private String primaryStorageUuid;

    @Schema(description = "主存储名称")
    private String primaryStorageName;

    @Schema(description = "主存储类型")
    private String primaryStorageType;

    @Schema(description = "云主机uuid")
    private String vmInstanceUuid;

    @Schema(description = "云主机名称")
    private String vmInstanceName;

    @Schema(description = "云盘类型，数据云盘/根云盘")
    private String type;

    @Schema(description = "云盘格式")
    private String format;

    @Schema(description = "云盘大小")
    private Long size;

    @Schema(description = "云盘真实大小")
    private Long actualSize;

    @Schema(description = "云盘是否开启")
    private String state;

    @Schema(description = "云盘状态")
    private String status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] createTime;

    @Schema(description = "云盘创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] vCreateDate;

    @Schema(description = "云盘更新时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] vUpdateDate;

    // 最大iops max_iops
    @Schema(description = "最大iops")
    private Long maxIops;

    //吞吐量
    @Schema(description = "吞吐量")
    private BigDecimal throughput;

    //介质类型
    @Schema(description = "介质类型")
    private String mediaType;

    @Schema(description = "是否挂载")
    private Boolean isMount;

    @Schema(description = "查询参数")
    private String queryData;

    @Schema(required = false,description = "开始时间",example = "2023-06-07")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String startTime;

    @Schema(required = false,description = "结束时间",example = "2023-06-07")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String endTime;

    @Schema(required = false,description = "主机tagId逗号拼接,1,2,3")
    private String tagIds;

    @Schema(required = false,description = "主机id逗号拼接,1,2,3")
    private List<Long> ids;

}
