package cn.iocoder.zj.module.monitor.controller.admin.bulletin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 实时报 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class BulletinBaseVO {

    @Schema(description = "名称")
    private String name;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "监控资产类型")
    private String category;

    @Schema(description = "监控资产小类")
    private String app;

    @Schema(description = "监控选项")
    private String fields;

    @Schema(description = "资产ids")
    private String assetIds;

}
