package cn.iocoder.zj.module.monitor.controller.admin.eip;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.monitor.controller.admin.eip.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.eip.EipDO;
import cn.iocoder.zj.module.monitor.convert.eip.EipConvert;
import cn.iocoder.zj.module.monitor.service.eip.EipService;

@Tag(name = "管理后台 - 弹性公网")
@RestController
@RequestMapping("/monitor/eip")
@Validated
public class EipController {

    @Resource
    private EipService eipService;

    @PostMapping("/create")
    @Operation(summary = "创建弹性公网")
    //@PreAuthorize("@ss.hasPermission('monitor:eip:create')")
    public CommonResult<Long> createEip(@Valid @RequestBody EipCreateReqVO createReqVO) {
        return success(eipService.createEip(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新弹性公网")
    //@PreAuthorize("@ss.hasPermission('monitor:eip:update')")
    public CommonResult<Boolean> updateEip(@Valid @RequestBody EipUpdateReqVO updateReqVO) {
        eipService.updateEip(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除弹性公网")
    @Parameter(name = "id", description = "编号", required = true)
    //@PreAuthorize("@ss.hasPermission('monitor:eip:delete')")
    public CommonResult<Boolean> deleteEip(@RequestParam("id") Long id) {
        eipService.deleteEip(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得弹性公网")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('monitor:eip:query')")
    public CommonResult<EipRespVO> getEip(@RequestParam("id") Long id) {
        EipDO eip = eipService.getEip(id);
        return success(EipConvert.INSTANCE.convert(eip));
    }

    @GetMapping("/list")
    @Operation(summary = "获得弹性公网列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    //@PreAuthorize("@ss.hasPermission('monitor:eip:query')")
    public CommonResult<List<EipRespVO>> getEipList(@RequestParam("ids") Collection<Long> ids) {
        List<EipDO> list = eipService.getEipList(ids);
        return success(EipConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得弹性公网分页")
    //@PreAuthorize("@ss.hasPermission('monitor:eip:query')")
    public CommonResult<PageResult<EipRespVO>> getEipPage(@Valid EipPageReqVO pageVO) {
        PageResult<EipDO> pageResult = eipService.getEipPage(pageVO);
        return success(EipConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出弹性公网 Excel")
    //@PreAuthorize("@ss.hasPermission('monitor:eip:export')")
    @OperateLog(type = EXPORT)
    public void exportEipExcel(@Valid EipExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<EipDO> list = eipService.getEipList(exportReqVO);
        // 导出 Excel
        List<EipExcelVO> datas = EipConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "弹性公网.xls", "数据", EipExcelVO.class, datas);
    }

}
