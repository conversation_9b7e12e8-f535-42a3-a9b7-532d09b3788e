package cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

/**
* 告警配置 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class AlarmConfigBaseVO {

    @Schema(required = true,description = "告警名称,创建时必填")
    @Size(max = 50, message = "告警名称长度最大为50个字符")
    private String alarmName;

    @Schema(description = "告警简介")
    @Size(max = 255, message = "告警简介最大长度为255个字符")
    private String description;

    @Schema(required = true,description = "资源类型，云主机monitor_alarm_host;宿主机monitor_alarm_hardware;存储monitor_alarm_disk;monitor_alarm_image镜像,创建时必填")
    private String sourceType;

    @Schema(required = true,description = "字典名称(对应报警条目的name值),创建时必填")
    private String dictLabelName;

    @Schema(required = true,description = "字典类型(对应报警条目类型的type值),创建时必填")
    private String dictLabelType;

    @Schema(required = true,description = "字典值(对应报警条目的value值),创建时必填")
    private String dictLabelValue;

    @Schema(required = true,description = "触发规则,创建时必填,<=,>=,<,>")
    private String alarmRule;

    @Schema(required = true,description = "告警阈值,创建时必填")
    private Integer alarmVal;

    @Schema(description = "单位,以dictLabelValue为参数查询字典项")
    private String unit;

    @Schema(description = "单位字典项code")
    private String unitType;

    @Schema(required = true,description = "收敛次数，数字")
    private Long alarmTime;

    @Schema(required = true,description = "告警级别,创建时必填。1提示，2警告，3严重")
    private Integer alarmLevel;

    @Schema(description = "消息内容，后台拼接，不传")
    private String context;

    @Schema(description = "是否删除，0否，1是")
    private Integer deleted;

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "是否解决0未创建工单，1未解决，2已解决")
    private Integer isSolved;

    @Schema(description = "是否启用")
    private Integer enabled;
}
