package cn.iocoder.zj.module.monitor.dal.mysql.hostsecgroup;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostsecgroup.HostSecgroupDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.monitor.controller.admin.hostsecgroup.vo.*;

/**
 * 安全组关联云主机 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface HostSecgroupMapper extends BaseMapperX<HostSecgroupDO> {

    default PageResult<HostSecgroupDO> selectPage(HostSecgroupPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<HostSecgroupDO>()
                .eqIfPresent(HostSecgroupDO::getHostUuid, reqVO.getHostUuid())
                .eqIfPresent(HostSecgroupDO::getSecgroupUuid, reqVO.getSecgroupUuid())
                .betweenIfPresent(HostSecgroupDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(HostSecgroupDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(HostSecgroupDO::getPlatformName, reqVO.getPlatformName())
                .orderByDesc(HostSecgroupDO::getId));
    }

    default List<HostSecgroupDO> selectList(HostSecgroupExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<HostSecgroupDO>()
                .eqIfPresent(HostSecgroupDO::getHostUuid, reqVO.getHostUuid())
                .eqIfPresent(HostSecgroupDO::getSecgroupUuid, reqVO.getSecgroupUuid())
                .betweenIfPresent(HostSecgroupDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(HostSecgroupDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(HostSecgroupDO::getPlatformName, reqVO.getPlatformName())
                .orderByDesc(HostSecgroupDO::getId));
    }

}
