package cn.iocoder.zj.module.monitor.convert.taggables;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.api.tag.dto.TaggablesDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.taggables.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.taggables.TaggablesDO;

/**
 * 标签绑定关系 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TaggablesConvert {

    TaggablesConvert INSTANCE = Mappers.getMapper(TaggablesConvert.class);

    TaggablesDO convert(TaggablesCreateReqVO bean);

    TaggablesDO convert(TaggablesUpdateReqVO bean);

    TaggablesRespVO convert(TaggablesDO bean);

    List<TaggablesRespVO> convertList(List<TaggablesDO> list);

    PageResult<TaggablesRespVO> convertPage(PageResult<TaggablesDO> page);

    List<TaggablesExcelVO> convertList02(List<TaggablesDO> list);

    List<TaggablesDTO> convertListDto(List<TaggablesDO> list);

}
