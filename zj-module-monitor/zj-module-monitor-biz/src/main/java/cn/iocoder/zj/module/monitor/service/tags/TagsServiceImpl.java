package cn.iocoder.zj.module.monitor.service.tags;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.controller.admin.taggables.vo.TaggablesExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.tags.vo.TagsCreateReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.tags.vo.TagsExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.tags.vo.TagsPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.tags.vo.TagsUpdateReqVO;
import cn.iocoder.zj.module.monitor.convert.tags.TagsConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.taggables.TaggablesDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.tags.TagsDO;
import cn.iocoder.zj.module.monitor.dal.mysql.taggables.TaggablesMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.tags.TagsMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.*;

/**
 * 标签 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TagsServiceImpl implements TagsService {

    @Resource
    private TagsMapper tagsMapper;

    @Resource
    private TaggablesMapper taggablesMapper;

    @Override
    public Long createTags(TagsCreateReqVO createReqVO) {
        // 插入
        TagsDO tags = TagsConvert.INSTANCE.convert(createReqVO);
        Long exist = tagsMapper.selectCount(TagsDO::getName, tags.getName());
        if (exist > 0) {
            throw exception(TAGS_NAME_DUPLICATE);
        }
        tagsMapper.insert(tags);
        // 返回
        return tags.getId();
    }

    @Override
    public void updateTags(TagsUpdateReqVO updateReqVO) {
        // 校验存在
        validateTagsExists(updateReqVO.getId());
        // 更新
        TagsDO updateObj = TagsConvert.INSTANCE.convert(updateReqVO);
        tagsMapper.updateById(updateObj);
    }

    @Override
    public void deleteTags(Long id) {
        // 校验存在
        validateTagsExists(id);
        TagsDO tagsDO = tagsMapper.selectById(id);
        if(tagsDO.getType() == 1){
            throw exception(TAGS_NOT_DEL);
        }
        // 删除
        tagsMapper.deleteById(id);
    }

    private void validateTagsExists(Long id) {
        if (tagsMapper.selectById(id) == null) {
            throw exception(TAGS_NOT_EXISTS);
        }
    }

    @Override
    public TagsDO getTags(Long id) {
        return tagsMapper.selectById(id);
    }

    @Override
    public List<TagsDO> getTagsList(Collection<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return tagsMapper.selectList();
        }
        return tagsMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<TagsDO> getTagsPage(TagsPageReqVO pageReqVO) {
        return tagsMapper.selectPage(pageReqVO);
    }

    @Override
    public List<TagsDO> getTagsList(TagsExportReqVO exportReqVO) {
        return tagsMapper.selectList(exportReqVO);
    }

    @Override
    @TenantIgnore
    public List<TagsDO> getTagListByTagGables(TaggablesExportReqVO exportReqVO) {
        List<TaggablesDO> gables = taggablesMapper.selectList(exportReqVO);
        return tagsMapper.selectList(TagsDO::getId, gables.stream().map(TaggablesDO::getTagId).toList());
    }

}
