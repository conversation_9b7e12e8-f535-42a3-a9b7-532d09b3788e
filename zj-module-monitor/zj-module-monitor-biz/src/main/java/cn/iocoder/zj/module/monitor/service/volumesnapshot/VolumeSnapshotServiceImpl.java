package cn.iocoder.zj.module.monitor.service.volumesnapshot;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeSnapshotDTO;
import cn.iocoder.zj.module.monitor.controller.admin.volumesnapshot.vo.VolumeSnapshotCreateReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.volumesnapshot.vo.VolumeSnapshotExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.volumesnapshot.vo.VolumeSnapshotPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.volumesnapshot.vo.VolumeSnapshotUpdateReqVO;
import cn.iocoder.zj.module.monitor.convert.volumesnapshot.VolumeSnapshotConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.volumesnapshot.VolumeSnapshotDO;
import cn.iocoder.zj.module.monitor.dal.mysql.volumesnapshot.VolumeSnapshotMapper;
import cn.iocoder.zj.module.monitor.util.StringUtil;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.VOLUME_SNAPSHOT_NOT_EXISTS;

/**
 * 云盘快照信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class VolumeSnapshotServiceImpl implements VolumeSnapshotService {

    @Resource
    private VolumeSnapshotMapper volumeSnapshotMapper;
    @Resource
    private PlatformconfigApi platformconfigApi;

    @Override
    public Long createVolumeSnapshot(VolumeSnapshotCreateReqVO createReqVO) {
        // 插入
        VolumeSnapshotDO volumeSnapshot = VolumeSnapshotConvert.INSTANCE.convert(createReqVO);
        volumeSnapshotMapper.insert(volumeSnapshot);
        // 返回
        return volumeSnapshot.getId();
    }

    @Override
    public void updateVolumeSnapshot(VolumeSnapshotUpdateReqVO updateReqVO) {
        // 校验存在
        validateVolumeSnapshotExists(updateReqVO.getId());
        // 更新
        VolumeSnapshotDO updateObj = VolumeSnapshotConvert.INSTANCE.convert(updateReqVO);
        volumeSnapshotMapper.updateById(updateObj);
    }

    @Override
    public void deleteVolumeSnapshot(Long id) {
        // 校验存在
        validateVolumeSnapshotExists(id);
        // 删除
        volumeSnapshotMapper.deleteById(id);
    }

    private void validateVolumeSnapshotExists(Long id) {
        if (volumeSnapshotMapper.selectById(id) == null) {
            throw exception(VOLUME_SNAPSHOT_NOT_EXISTS);
        }
    }

    @Override
    public VolumeSnapshotDO getVolumeSnapshot(Long id) {
        return volumeSnapshotMapper.selectById(id);
    }

    @Override
    public List<VolumeSnapshotDO> getVolumeSnapshotList(Collection<Long> ids) {
        return volumeSnapshotMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<VolumeSnapshotDO> getVolumeSnapshotPage(VolumeSnapshotPageReqVO pageReqVO) {
        List<Map> platform = new ArrayList<>();
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser != null) {
            platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
        }
//        PageParam pageParam = new PageParam().setPageNo(pageReqVO.getPageNo()).setPageSize(pageReqVO.getPageSize());
//        IPage<VolumeSnapshotDO> mpPage = MyBatisUtils.buildPage(pageParam);
        return volumeSnapshotMapper.selectPage(pageReqVO,platform);
    }

    @Override
    public List<VolumeSnapshotDO> getVolumeSnapshotList(VolumeSnapshotExportReqVO exportReqVO) {
        List<Map> platform = new ArrayList<>();
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser != null) {
            platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
        }
        return volumeSnapshotMapper.selectList(exportReqVO,platform);
    }

    @Override
    public Long getVolumeSnapshotCount(String typeName) {
        LambdaQueryWrapper<VolumeSnapshotDO> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StringUtil.isNotEmpty(typeName),VolumeSnapshotDO::getTypeName,typeName);
        return volumeSnapshotMapper.selectCount(lqw);
    }

    @Override
    public List<VolumeSnapshotDTO> getAll(String typeName) {
        LambdaQueryWrapper<VolumeSnapshotDO> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StringUtil.isNotEmpty(typeName),VolumeSnapshotDO::getTypeName,typeName);
        return VolumeSnapshotConvert.INSTANCE.DOConvertToDTO(volumeSnapshotMapper.selectList(lqw));
    }

    @Override
    public void addSnapshots(List<VolumeSnapshotDO> insertDos) {
        volumeSnapshotMapper.insertBatch(insertDos);
    }

    @Override
    public void updateSnapshots(List<VolumeSnapshotDO> updateDos) {
        volumeSnapshotMapper.updateSnapshotBatch(updateDos);
    }

    @Override
    public void deleteVolumeSnapshotByplatform(Long platformId) {
        volumeSnapshotMapper.deleteVolumeSnapshotByplatform(platformId);
    }

    @Override
    public Map<String, Object> getVolumeSnapshotStatusCount(List<String> tenantIds, Long platformId) {
        Map<String, Object> map = volumeSnapshotMapper.getVolumeSnapshotStatusCount(tenantIds, platformId);
        if (map.size() == 1) {
            map = new HashMap<>();
            map.put("total", 0);
            map.put("available", 0);
            map.put("other", 0);
        }
        return map;
    }

    @Override
    public List<VolumeSnapshotDO> getVolumeSnapshotByHostUuids(List<String> hostUuids) {
        return volumeSnapshotMapper.getVolumeSnapshotByHostUuids(hostUuids);
    }
}
