package cn.iocoder.zj.module.monitor.controller.admin.alarminfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 监控告警详情 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class AlarmInfoExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("处理人")
    private String processors;

    @ExcelProperty("处理时间")
    private LocalDateTime processingTime;

    @ExcelProperty("处理详情")
    private String processingDetails;

    @ExcelProperty("文件地址")
    private String fileAddress;

    @ExcelProperty("处理状态")
    private Integer processingType;

    @ExcelProperty("监控告警id")
    private String alarmId;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("平台id")
    private Long platformId;

    @ExcelProperty("平台名称")
    private String platformName;

}
