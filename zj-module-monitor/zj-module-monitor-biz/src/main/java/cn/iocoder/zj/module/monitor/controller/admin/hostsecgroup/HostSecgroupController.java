package cn.iocoder.zj.module.monitor.controller.admin.hostsecgroup;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.monitor.controller.admin.hostsecgroup.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostsecgroup.HostSecgroupDO;
import cn.iocoder.zj.module.monitor.convert.hostsecgroup.HostSecgroupConvert;
import cn.iocoder.zj.module.monitor.service.hostsecgroup.HostSecgroupService;

@Tag(name = "管理后台 - 安全组关联云主机")
@RestController
@RequestMapping("/monitor/host-secgroup")
@Validated
public class HostSecgroupController {

    @Resource
    private HostSecgroupService hostSecgroupService;

    @PostMapping("/create")
    @Operation(summary = "创建安全组关联云主机")
    @PreAuthorize("@ss.hasPermission('monitor:host-secgroup:create')")
    public CommonResult<Long> createHostSecgroup(@Valid @RequestBody HostSecgroupCreateReqVO createReqVO) {
        return success(hostSecgroupService.createHostSecgroup(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新安全组关联云主机")
    @PreAuthorize("@ss.hasPermission('monitor:host-secgroup:update')")
    public CommonResult<Boolean> updateHostSecgroup(@Valid @RequestBody HostSecgroupUpdateReqVO updateReqVO) {
        hostSecgroupService.updateHostSecgroup(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除安全组关联云主机")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('monitor:host-secgroup:delete')")
    public CommonResult<Boolean> deleteHostSecgroup(@RequestParam("id") Long id) {
        hostSecgroupService.deleteHostSecgroup(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得安全组关联云主机")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('monitor:host-secgroup:query')")
    public CommonResult<HostSecgroupRespVO> getHostSecgroup(@RequestParam("id") Long id) {
        HostSecgroupDO hostSecgroup = hostSecgroupService.getHostSecgroup(id);
        return success(HostSecgroupConvert.INSTANCE.convert(hostSecgroup));
    }

    @GetMapping("/list")
    @Operation(summary = "获得安全组关联云主机列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('monitor:host-secgroup:query')")
    public CommonResult<List<HostSecgroupRespVO>> getHostSecgroupList(@RequestParam("ids") Collection<Long> ids) {
        List<HostSecgroupDO> list = hostSecgroupService.getHostSecgroupList(ids);
        return success(HostSecgroupConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得安全组关联云主机分页")
    @PreAuthorize("@ss.hasPermission('monitor:host-secgroup:query')")
    public CommonResult<PageResult<HostSecgroupRespVO>> getHostSecgroupPage(@Valid HostSecgroupPageReqVO pageVO) {
        PageResult<HostSecgroupDO> pageResult = hostSecgroupService.getHostSecgroupPage(pageVO);
        return success(HostSecgroupConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出安全组关联云主机 Excel")
    @PreAuthorize("@ss.hasPermission('monitor:host-secgroup:export')")
    @OperateLog(type = EXPORT)
    public void exportHostSecgroupExcel(@Valid HostSecgroupExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<HostSecgroupDO> list = hostSecgroupService.getHostSecgroupList(exportReqVO);
        // 导出 Excel
        List<HostSecgroupExcelVO> datas = HostSecgroupConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "安全组关联云主机.xls", "数据", HostSecgroupExcelVO.class, datas);
    }

}
