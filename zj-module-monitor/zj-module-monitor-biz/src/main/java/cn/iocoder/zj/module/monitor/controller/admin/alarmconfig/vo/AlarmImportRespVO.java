package cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Schema(description = "管理后台 - 告警配置 Response VO")
@Data
@Builder
public class AlarmImportRespVO {

    @Schema(description = "创建成功的用户名数组", required = true)
    private List<String> createAlarmNames;

    @Schema(description = "导入失败的用户集合，key 为用户名，value 为失败原因", required = true)
    private Map<String, String> failureAlarmNames;
}
