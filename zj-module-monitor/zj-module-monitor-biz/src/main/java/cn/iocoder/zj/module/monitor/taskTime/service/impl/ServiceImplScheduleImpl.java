package cn.iocoder.zj.module.monitor.taskTime.service.impl;
import cn.iocoder.zj.module.monitor.taskTime.annotation.HSYJobProcessor;
import cn.iocoder.zj.module.monitor.taskTime.cache.TaskCacheModel;
import cn.iocoder.zj.module.monitor.taskTime.service.ScheduleTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;

import static cn.iocoder.zj.module.monitor.taskTime.annotation.HSYJobProcessor.TASK_CACHE;


/**
 * <AUTHOR>
 **/
@Slf4j
@Service
public class ServiceImplScheduleImpl implements ScheduleTaskService {
    @Resource
    private TaskScheduler taskScheduler;

    @Resource
    private HSYJobProcessor hsyJobProcessor;

    private final Map<String, ScheduledFuture<?>> tasks = new ConcurrentHashMap<>(16);

    @Override
    public List<TaskCacheModel> listTasks() {
        return new ArrayList<>(TASK_CACHE.values());
    }

    @Override
    public void removeTask(String jobName) {
        try {
            // 取消并移除调度任务
            Optional.ofNullable(tasks.remove(jobName))
                    .ifPresent(future -> future.cancel(true));

            // 从任务缓存移除
            HSYJobProcessor.TASK_CACHE.remove(jobName);

            log.info("成功移除任务: {}", jobName);
        } catch (Exception e) {
            log.error("移除任务失败: {}", jobName, e);
            throw new RuntimeException("移除任务失败", e);
        }
    }

    @Override
    public void addTask(String taskName, String cronExpression) {
        try {
            // 直接调用注册方法
            boolean result = hsyJobProcessor.registerDynamicTask(taskName, cronExpression);
            if (result) {
                updateTask(taskName,cronExpression);
                log.info("新增任务成功: {}, cron: {}", taskName, cronExpression);
            }
        } catch (Exception e) {
            log.error("新增任务失败: {}, cron: {}", taskName, cronExpression, e);
            return ;
        }
    }

    @Override
    public void updateTask(String taskName, String cronExpression) {
        Optional.ofNullable(TASK_CACHE.get(taskName))
                .ifPresent(model -> {
                    model.setCronExpression(cronExpression);
                    tasks.computeIfPresent(taskName, (k, oldFuture) -> {
                        oldFuture.cancel(true);
                        return null;
                    });
                    ScheduledFuture<?> future = taskScheduler.schedule(
                            () -> {
                                try {
                                    model.getMethod().invoke(model.getBean(), taskName);
                                } catch (Exception e) {
                                    log.error("执行任务[{}]失败", taskName, e);
                                }
                            },
                            new CronTrigger(cronExpression));
                    tasks.put(taskName, future);
                });
    }


    public void clearAllTasks() {
        tasks.values().forEach(future -> future.cancel(true));
        tasks.clear();
        HSYJobProcessor.TASK_CACHE.clear();
    }
}
