package cn.iocoder.zj.module.monitor.api.hardware;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.convert.hardwareinfo.HardwareInfoConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.hardwareinfo.HardwareInfoDO;
import cn.iocoder.zj.module.monitor.pojo.AssetReqVO;
import cn.iocoder.zj.module.monitor.service.hardware.IZstackHardWareService;
import cn.iocoder.zj.module.monitor.service.hardwareinfo.HardwareInfoService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.convertMap;
import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

/**
 * @ClassName : HardWareApiImpk  //类名
 * @Description : 硬件设施实现  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/6/5  17:42
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class HardWareApiImpl implements HardWareInfoApi {
    @Resource
    private IZstackHardWareService iZstackHardWareService;
    @Resource
    private HardwareInfoService hardwareInfoService;

    @Override
    public CommonResult<Boolean> adds(List<HardWareRespCreateReqDTO> reqDTO) {
        Map<String,HardWareRespCreateReqDTO> reqDTOMap = convertMap(reqDTO, HardWareRespCreateReqDTO::getUuid);
        List<HardwareInfoDO> list = HardwareInfoConvert.INSTANCE.convertCreateList(reqDTO);
        list.forEach(item->{
            item.setCreateTime(reqDTOMap.get(item.getUuid()).getCreateTime());
        });
        iZstackHardWareService.createHostInfoList(list);
        return CommonResult.success(true);
    }

    @Override
    public int count(String typeName) {
        return iZstackHardWareService.getCount(typeName);
    }

    @Override
    public CommonResult<Boolean> updates(List<HardWareRespCreateReqDTO> reqDTO) {
        if (reqDTO.size()>0){
            Map<String,HardWareRespCreateReqDTO> reqDTOMap = convertMap(reqDTO, HardWareRespCreateReqDTO::getUuid);
            List<HardwareInfoDO> list = HardwareInfoConvert.INSTANCE.convertCreateList(reqDTO);
            list.forEach(item->{
                item.setCreateTime(reqDTOMap.get(item.getUuid()).getCreateTime());
            });
            iZstackHardWareService.updateHardwareInfoList(list);
        }
        return CommonResult.success(true);
    }

    public CommonResult<List<HardWareRespCreateReqDTO>> getAll(String typeName) {
        List<HardWareRespCreateReqDTO> list = HardwareInfoConvert.INSTANCE.convertListDoToDto(iZstackHardWareService.getAll(typeName));
        return  CommonResult.success(list);
    }

    @Override
    public CommonResult<String> getIp(Map<String,String> map) {
        String ip=iZstackHardWareService.getIp(map.get("uuid"),map.get("sourceType"));
        return CommonResult.success(ip);
    }

    @Override
    public int deleteHardWare(List<HardWareRespCreateReqDTO> hardWareRespCreateReqDTOS) {
        List<HardwareInfoDO> list = HardwareInfoConvert.INSTANCE.convertCreateList(hardWareRespCreateReqDTOS);
        return iZstackHardWareService.deleteHardWare(list);
    }

    @Override
    public CommonResult<List<HardWareRespCreateReqDTO>> getListAll() {
        List<HardWareRespCreateReqDTO> list = HardwareInfoConvert.INSTANCE.convertListDoToDto(iZstackHardWareService.getListAll());
        return  CommonResult.success(list);
    }

    @Override
    public void removeDuplicateData() {
        iZstackHardWareService.removeDuplicateData();
    }

    @Override
    public CommonResult<HardWareRespCreateReqDTO> getByUuid(String uuid) {
        HardWareRespCreateReqDTO reqDTO = HardwareInfoConvert.INSTANCE.convertToDTO(hardwareInfoService.getByUuid(uuid));
        return CommonResult.success(reqDTO);
    }

    @Override
    public CommonResult<Long> getCpuSockets(List<String> platformIds) {
        return  CommonResult.success(iZstackHardWareService.getCpuSockets(platformIds));
    }

    @Override
    public CommonResult<List<HardWareRespCreateReqDTO> > getHardwareByPlatformId(Long platformId) {

        List<HardWareRespCreateReqDTO> hardWareRespCreateReqDTOS =  hardwareInfoService.getHardwareByPlatformId(platformId);
        return CommonResult.success(hardWareRespCreateReqDTOS);
    }

    @Override
    public CommonResult<List<HardWareRespCreateReqDTO>> getHardwareByTenantOrPlatforms(AssetReqVO assetReqVO) {
        List<HardWareRespCreateReqDTO> hardWareRespCreateReqDTOS = BeanUtil.copyToList(hardwareInfoService.getHardwareByTenantOrPlatforms(assetReqVO), HardWareRespCreateReqDTO.class);
        return CommonResult.success(hardWareRespCreateReqDTOS);
    }
}
