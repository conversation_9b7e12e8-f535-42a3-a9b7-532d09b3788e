package cn.iocoder.zj.module.monitor.convert.gatherdevice;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.gatherdevice.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.gatherdevice.GatherDeviceDO;

/**
 * 采集设备 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface GatherDeviceConvert {

    GatherDeviceConvert INSTANCE = Mappers.getMapper(GatherDeviceConvert.class);

    GatherDeviceDO convert(GatherDeviceCreateReqVO bean);

    GatherDeviceDO convert(GatherDeviceUpdateReqVO bean);

    GatherDeviceRespVO convert(GatherDeviceDO bean);

    List<GatherDeviceRespVO> convertList(List<GatherDeviceDO> list);

    PageResult<GatherDeviceRespVO> convertPage(PageResult<GatherDeviceDO> page);

    List<GatherDeviceExcelVO> convertList02(List<GatherDeviceDO> list);

    List<GatherDeviceSelectVO> convertListSelect(List<GatherDeviceDO> list);
}
