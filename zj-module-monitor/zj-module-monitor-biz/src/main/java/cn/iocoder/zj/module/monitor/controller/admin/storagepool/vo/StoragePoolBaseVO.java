package cn.iocoder.zj.module.monitor.controller.admin.storagepool.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 存储池 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class StoragePoolBaseVO {

    @Schema(description = "存储池uuid")
    private String uuid;

    @Schema(description = "存储名称")
    private String name;

    @Schema(description = "存储池类型")
    private String type;

    @Schema(description = "存储名称")
    private String description;

    @Schema(description = "存储池创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime vCreateDate;

    @Schema(description = "存储池修改时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime lastOpDate;

    @Schema(description = "已使用容量")
    private BigDecimal usedCapacity;

    @Schema(description = "虚拟可用容量")
    private BigDecimal availableCapacity;

    @Schema(description = "总容量")
    private BigDecimal totalCapacity;

    @Schema(description = "主存储uuid")
    private String storageUuid;

    @Schema(description = "数据安全类型")
    private String securityPolicy;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

}
