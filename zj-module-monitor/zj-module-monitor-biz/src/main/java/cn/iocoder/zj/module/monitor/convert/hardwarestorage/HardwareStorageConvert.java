package cn.iocoder.zj.module.monitor.convert.hardwarestorage;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.api.hardwarenic.HardWareNicRespDTO;
import cn.iocoder.zj.module.monitor.api.hardwarestorage.HardWareStorageRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.hardwarestorage.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.hardwarestorage.HardwareStorageDO;

/**
 * 宿主机与存储关联 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface HardwareStorageConvert {

    HardwareStorageConvert INSTANCE = Mappers.getMapper(HardwareStorageConvert.class);

    HardwareStorageDO convert(HardwareStorageCreateReqVO bean);

    HardwareStorageDO convert(HardwareStorageUpdateReqVO bean);

    HardwareStorageRespVO convert(HardwareStorageDO bean);

    List<HardwareStorageRespVO> convertList(List<HardwareStorageDO> list);

    PageResult<HardwareStorageRespVO> convertPage(PageResult<HardwareStorageDO> page);

    List<HardwareStorageExcelVO> convertList02(List<HardwareStorageDO> list);

    List<HardwareStorageDO> convertCreateList(List<HardWareStorageRespDTO> reqDTO);
}
