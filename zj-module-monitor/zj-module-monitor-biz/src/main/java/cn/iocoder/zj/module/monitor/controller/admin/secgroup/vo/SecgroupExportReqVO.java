package cn.iocoder.zj.module.monitor.controller.admin.secgroup.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 安全组 Excel 导出 Request VO，参数和 SecgroupPageReqVO 是一致的")
@Data
public class SecgroupExportReqVO {

    @Schema(description = "uuid")
    private String uuid;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "是否共享")
    private Boolean isPublic;

    @Schema(description = "默认共享范围")
    private String publicScope;

    @Schema(description = "共享设置的来源, local: 本地设置, cloud: 从云上同步过来")
    private String publicSrc;

    @Schema(description = "是否垃圾")
    private Boolean isDirty;

    @Schema(description = "区域id")
    private String cloudregionId;

    @Schema(description = "vpcId")
    private String vpcId;

    @Schema(description = "全局vpcId")
    private String globalvpcId;

    @Schema(description = "备注")
    private String description;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

}
