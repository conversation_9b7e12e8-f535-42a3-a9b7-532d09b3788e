package cn.iocoder.zj.module.monitor.controller.admin.gatherasset;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;

import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.monitor.controller.admin.gatherasset.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.gatherasset.GatherAssetDO;
import cn.iocoder.zj.module.monitor.convert.gatherasset.GatherAssetConvert;
import cn.iocoder.zj.module.monitor.service.gatherasset.GatherAssetService;

@Tag(name = "管理后台 - 租户资产")
@RestController
@RequestMapping("/monitor/gather-asset")
@Validated
public class GatherAssetController {

    @Resource
    private GatherAssetService gatherAssetService;

    @PostMapping("/create")
    @Operation(summary = "创建租户资产")
    @PreAuthorize("@ss.hasPermission('monitor:gather-asset:create')")
    @OperateLog(type = CREATE)
    public CommonResult<Long> createGatherAsset(@Valid @RequestBody GatherAssetCreateReqVO createReqVO) {
        return success(gatherAssetService.createGatherAsset(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新租户资产")
    @PreAuthorize("@ss.hasPermission('monitor:gather-asset:update')")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateGatherAsset(@Valid @RequestBody GatherAssetUpdateReqVO updateReqVO) {
        gatherAssetService.updateGatherAsset(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除租户资产")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('monitor:gather-asset:delete')")
    @OperateLog(type = DELETE)
    public CommonResult<Boolean> deleteGatherAsset(@RequestParam("id") Long id) {
        gatherAssetService.deleteGatherAsset(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得租户资产")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<GatherAssetRespVO> getGatherAsset(@RequestParam("id") Long id) {
        GatherAssetDO gatherAsset = gatherAssetService.getGatherAsset(id);
        return success(GatherAssetConvert.INSTANCE.convert(gatherAsset));
    }

    @GetMapping("/list")
    @Operation(summary = "获得租户资产列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    public CommonResult<List<GatherAssetRespVO>> getGatherAssetList(@RequestParam("ids") Collection<Long> ids) {
        List<GatherAssetDO> list = gatherAssetService.getGatherAssetList(ids);
        return success(GatherAssetConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得租户资产分页")
    @TenantIgnore
    public CommonResult<PageResult<GatherAssetRespVO>> getGatherAssetPage(@Valid GatherAssetPageReqVO pageVO) {
        PageResult<GatherAssetDO> pageResult = gatherAssetService.getGatherAssetPage(pageVO);
        return success(GatherAssetConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/getGatherAssetData")
    @Operation(summary = "详情页获取资产CUP、内存、磁盘数据")
    public CommonResult<List<GatherAssetDataRespVO>> getGatherAssetData(GatherAssetDataReqVO reqVo) {
        List<GatherAssetDataRespVO> assetData = gatherAssetService.getAssetData(reqVo);
        return success(assetData);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出租户资产 Excel")
    @PreAuthorize("@ss.hasPermission('monitor:gather-asset:export')")
    @OperateLog(type = EXPORT)
    public void exportGatherAssetExcel(@Valid GatherAssetExportReqVO exportReqVO,
                                       HttpServletResponse response) throws IOException {
        List<GatherAssetDO> list = gatherAssetService.getGatherAssetList(exportReqVO);
        // 导出 Excel
        List<GatherAssetExcelVO> datas = GatherAssetConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "租户资产.xls", "数据", GatherAssetExcelVO.class, datas);
    }


    @TenantIgnore
    @PermitAll
    @RequestMapping("/getAssetListByUuid")
    @ResponseBody
    public Map getAssetListByUuid(@RequestBody String uuid, @RequestParam String token_desc) throws IOException {
        return gatherAssetService.getAssetListByUuid(uuid, token_desc);
    }


    @TenantIgnore
    @PermitAll
    @RequestMapping("/updateOnlieType")
    @ResponseBody
    @OperateLog(type = UPDATE)
    public Map updateOnlieType(@RequestBody String json, @RequestParam String token_desc) throws IOException {
        return gatherAssetService.updateOnlieType(json, token_desc);
    }

    @TenantIgnore
    @PermitAll
    @RequestMapping("/addAssetList")
    @ResponseBody
    @OperateLog(type = CREATE)
    public Map addAssetList(@RequestBody String json, @RequestParam String token_desc) throws IOException {
        return gatherAssetService.addAssetList(json, token_desc);
    }

}
