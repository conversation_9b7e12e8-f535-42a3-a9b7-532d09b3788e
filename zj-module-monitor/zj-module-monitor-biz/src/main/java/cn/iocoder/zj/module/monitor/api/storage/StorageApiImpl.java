package cn.iocoder.zj.module.monitor.api.storage;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.storage.dto.StorageRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.convert.storageinfo.StorageInfoConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.storageinfo.StorageInfoDO;
import cn.iocoder.zj.module.monitor.pojo.AssetReqVO;
import cn.iocoder.zj.module.monitor.service.storageinfo.StorageInfoService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

/**
 * @ClassName : StorageApiImpl  //类名x
 * @Description : 存储RPC 实现  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/6/6  14:39
 */

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class StorageApiImpl implements StorageInfoApi {

    @Resource
    StorageInfoService storageInfoService;


    @Override
    public CommonResult<Boolean> adds(List<StorageRespCreateReqDTO> reqDTO) {
        List<StorageInfoDO> list = StorageInfoConvert.INSTANCE.convertCreateList(reqDTO);
        storageInfoService.createStorageInfoList(list);
        return CommonResult.success(true);
    }

    @Override
    public int count(String typeName) {
        return storageInfoService.getCount(typeName);
    }

    @Override
    public CommonResult<Boolean> updates(List<StorageRespCreateReqDTO> storageRespCreateReqDTOS) {
        List<StorageInfoDO> list = StorageInfoConvert.INSTANCE.convertCreateList(storageRespCreateReqDTOS);
        storageInfoService.updateHostInfoList(storageRespCreateReqDTOS);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<List<StorageRespCreateReqDTO>> getAll(String typeName) {
        List<StorageRespCreateReqDTO> list = StorageInfoConvert.INSTANCE.convertListDoToDto(storageInfoService.getAll(typeName));
        return  CommonResult.success(list);
    }

    @Override
    public int deleteStorageList(List<StorageRespCreateReqDTO> storageRespCreateReqDTOS) {
        List<StorageInfoDO> list = StorageInfoConvert.INSTANCE.convertCreateList(storageRespCreateReqDTOS);
        return storageInfoService.deleteStorageList(list);
    }

    @Override
    public CommonResult<List<StorageRespCreateReqDTO>> getListAll() {
        List<StorageRespCreateReqDTO> list = StorageInfoConvert.INSTANCE.convertListDoToDto(storageInfoService.getListAll());
        return  CommonResult.success(list);
    }

    @Override
    public CommonResult<List<StorageRespCreateReqDTO>> getStoragesByTenantOrPlatforms(AssetReqVO assetReqVO) {
        List<StorageRespCreateReqDTO> storageRespCreateReqDTOS =
                BeanUtil.copyToList(storageInfoService.getStoragesByTenantOrPlatforms(assetReqVO), StorageRespCreateReqDTO.class);
        return CommonResult.success(storageRespCreateReqDTOS);
    }

    @Override
    public List<StorageRespCreateReqDTO> getStorageByPlatformId(Long id) {
        return storageInfoService.getStorageByPlatformId(id);
    }
}
