package cn.iocoder.zj.module.monitor.controller.admin.topologyrelation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 拓扑图关系 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class TopologyRelationExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("拓扑图id")
    private Long topologyId;

    @ExcelProperty("监控id")
    private String monitorId;

    @ExcelProperty("监控设备名称")
    private String monitorName;

    @ExcelProperty("监控设备接口名称")
    private String monitorInterfaces;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
