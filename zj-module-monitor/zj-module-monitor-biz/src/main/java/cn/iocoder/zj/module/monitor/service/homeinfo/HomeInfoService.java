package cn.iocoder.zj.module.monitor.service.homeinfo;

import cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo.AlarmInfoRespVo;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

public interface HomeInfoService {
    int getIsDisplay(Long tenantId,String monitorEntry);
    String getAllDisplay(Long tenantId);
    Map<String, Object> getDisplayModule(Long tenantId);
    Map<String,Object> getResourceType(Long tenantId,String module);
    int updateDisplay(Long tenantId, String layoutConfig);
    Map<String,Object> getLayoutConfig(Long tenantId);
    List<String> getDMonitorEntry();
    List<AlarmInfoRespVo> getAlarmNum(Long tenantId,Long platformConfigId);
    List<Map<String,Object>> getAlarmInfo(Long tenantId,Long platformConfigId);
    List<Map<String,Object>> getAlarmMap(Long tenantId,Long regionId);
}
