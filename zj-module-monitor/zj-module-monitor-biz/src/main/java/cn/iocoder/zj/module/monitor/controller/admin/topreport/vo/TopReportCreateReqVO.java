package cn.iocoder.zj.module.monitor.controller.admin.topreport.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TopReportCreateReqVO extends TopReportBaseVO {

    /**
     * 资产id列表
     */
    private List<TopReportAssetVO> assets;

    /**
     * 指标名称列表
     */
    private List<TopReportMetricVO> metrics;
}