package cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Date;

@Schema(description = "管理后台 - 告警配置 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AlarmConfigRespVO extends AlarmConfigBaseVO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "uuid")
    private String hostUuid;

    @Schema(description = "该主机是否已选")
    private Boolean selected;

    @Schema(description = "关系id")
    private String relationId;

    @Schema(description = "启用状态:0正常，1停用")
    private Integer status;

    @Schema(description = "租户名称")
    private String tenantName;

}
