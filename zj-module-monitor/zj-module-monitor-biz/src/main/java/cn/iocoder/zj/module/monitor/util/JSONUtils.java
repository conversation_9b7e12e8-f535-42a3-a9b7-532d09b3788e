package cn.iocoder.zj.module.monitor.util;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.log4j.Log4j2;


@Log4j2
public class JSONUtils {

    public static <T> T toBean(String body, Class<T> tClass) {
        return toBean(body, tClass, null);
    }
    public static <T> T toBean(String body, Class<T> tClass, String msg) {
        T result = JSONUtil.toBean(body, tClass, true);
        return result;
    }

    public static <T> T toBean(String body, TypeReference<T> typeReference) {
        return toBean(body, typeReference, null);
    }
    public static <T> T toBean(String body, TypeReference<T> typeReference, String msg) {
        T result = JSONUtil.toBean(body, typeReference.getType(), true);
        return result;
    }

    private static void printLog(Object result, String msg) {
        log.info(() -> Thread.currentThread().getStackTrace()[8].getMethodName() + " toBean result: \n" + JSONUtil.toJsonPrettyStr(result) + (StrUtil.isBlank(msg) ? "" : "\nmsg:" + msg));
    }

}
