package cn.iocoder.zj.module.monitor.api.hostnic;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.hostnic.dto.HostNicCreateReqDto;
import cn.iocoder.zj.module.monitor.api.hostnic.dto.HostNicRespDTO;
import cn.iocoder.zj.module.monitor.controller.admin.hostnic.vo.HostNicExportReqVO;
import cn.iocoder.zj.module.monitor.convert.hostnic.HostNicConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostnic.HostNicDO;
import cn.iocoder.zj.module.monitor.service.hostnic.HostNicService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class HostNicApiImpl implements HostNicApi {
    @Resource
    HostNicService hostNicService;

    @Override
    public CommonResult<Boolean> addHostNics(List<HostNicCreateReqDto> reqDTO) {
        List<HostNicDO> list = HostNicConvert.INSTANCE.convertCreateList(reqDTO);
        hostNicService.createHostNicList(list);
        return CommonResult.success(true);
    }


    @Override
    public CommonResult<Boolean> updateHostNics(List<HostNicCreateReqDto> reqDTO) {
        if (!reqDTO.isEmpty()) {
            List<HostNicDO> list = HostNicConvert.INSTANCE.convertCreateList(reqDTO);
            hostNicService.updateHostNics(list);
        }
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> delHostNics(List<HostNicCreateReqDto> hostNicCreateReqDtos) {
        if (!hostNicCreateReqDtos.isEmpty()) {
            List<HostNicDO> list = HostNicConvert.INSTANCE.convertCreateList(hostNicCreateReqDtos);
            hostNicService.deleteHostNics(list);
        }

        return CommonResult.success(true);
    }

    @Override
    public CommonResult<List<HostNicCreateReqDto>> getHostNicsByPlatformId(Long platformId) {
        HostNicExportReqVO reqVo = new HostNicExportReqVO();
        reqVo.setPlatformId(platformId);
        List<HostNicCreateReqDto> list = HostNicConvert.INSTANCE.convertDoToCreateDtoList(hostNicService.getHostNicList(reqVo));
        return CommonResult.success(list);
    }
}
