package cn.iocoder.zj.module.monitor.dal.mysql.storageinfo;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.api.storage.dto.StorageRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.controller.admin.storageinfo.vo.StorageInfoExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.storageinfo.vo.StorageInfoPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.storageinfo.vo.StorageInfoRespVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.storageinfo.StorageInfoDO;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 存储设备信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface StorageInfoMapper extends BaseMapperX<StorageInfoDO> {

    default PageResult<StorageInfoDO> selectPage(StorageInfoPageReqVO reqVO) {
        LambdaQueryWrapperX<StorageInfoDO> wrapper = new LambdaQueryWrapperX<>();
        if(StringUtils.isNotBlank(reqVO.getName())){
            wrapper.likeIfPresent(StorageInfoDO::getName,reqVO.getName())
                    .or().like(StorageInfoDO::getPlatformName, reqVO.getPlatformName());
        }
        if(StringUtils.isNotBlank(reqVO.getStatus())){
            wrapper.eqIfPresent(StorageInfoDO::getStatus,reqVO.getStatus());
        }
        if (StrUtil.isNotEmpty(Convert.toStr(reqVO.getPlatformId()))) {
            wrapper.eqIfPresent(StorageInfoDO::getPlatformId, reqVO.getPlatformId());
        }
        wrapper.orderByDesc(StorageInfoDO::getId);
        return selectPage(reqVO, wrapper);
    }

    List<StorageInfoDO> getStorageInfoList(@Param("reqVO")StorageInfoExportReqVO reqVO);

    int getCount(@Param("typeName")String typeName);

    void updateHostInfoList(List<StorageRespCreateReqDTO> list);

    Map<String, Object> getStorageCapacity(@Param("tenantIds") List<String> tenantIds,@Param("platformId") Long platformId,@Param("regionId") Long regionId);

    List<Long> selectPlatformList(@Param("tenantIds") List<String> tenantId);

    List<StorageInfoDO> getStorageInfoPage(@Param("mpPage") IPage<StorageInfoDO> mpPage, @Param("pageReqVO") StorageInfoPageReqVO pageReqVO, @Param("ids") List<String> ids);

    int deleteStorageList(@Param("list") List<StorageInfoDO> list);

    default  Long getCountByPlatformId(Long platformId){
        return selectCount(StorageInfoDO::getPlatformId,platformId);
    }

    @Slave
    List<StorageInfoDO> getStorageInfoSlavePage(@Param("mpPage") IPage<StorageInfoDO> mpPage, @Param("pageReqVO") StorageInfoPageReqVO pageReqVO, @Param("ids") List<String> ids);

    default List<StorageInfoDO> getListByPlatformId(@Param("platformId")Collection<Long> platformId){
        return selectList(new LambdaQueryWrapperX<StorageInfoDO>()
                .in(StorageInfoDO::getPlatformId, platformId)
                .eq(StorageInfoDO::getDeleted,0)
                .eq(StorageInfoDO::getState,"Enabled")
                .eq(StorageInfoDO::getStatus,"Connected")
                .groupBy(StorageInfoDO::getUuid)

        );
    }

    default List<StorageInfoDO> getListByPlatform(@Param("platformId") Collection<Long> platformId) {
        if (CollectionUtils.isEmpty(platformId)) {
            return List.of();
        } else {
            return selectList(new LambdaQueryWrapperX<StorageInfoDO>()
                    .in(StorageInfoDO::getPlatformId, platformId)
                    .eq(StorageInfoDO::getDeleted, 0)
                    .groupBy(StorageInfoDO::getUuid)
            );
        }
    }


    StorageInfoDO getByUuid(@Param("uuid")String uuid);



    List<StorageInfoDO> getStorageListByUuids(@Param("uuidList")List<String> uuidList);

    List<StorageInfoDO> selectStorageList(IPage<StorageInfoDO> mpPage, StorageInfoPageReqVO pageVO);

    Map<String, Object> getStorageStatusCount(@Param("tenantIds")List<String> tenantIds, @Param("platformId") Long platformId);

    Long selectCountByPlatfrom(@Param("mapList") List<Map> platformConfigList);

    void deleteStorageInfoByplatform(@Param("platformId") Long platformId);

    List<StorageRespCreateReqDTO> getStorageByPlatformId(@Param("id") Long id);

    @TenantIgnore
    List<StorageInfoRespVO> getStorageByList(@Param("list") List<String> list);
}
