package cn.iocoder.zj.module.monitor.service.secgroup;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.monitor.api.secgroup.dto.SecgroupRespDto;
import cn.iocoder.zj.module.monitor.controller.admin.secgroup.vo.SecgroupCreateReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.secgroup.vo.SecgroupExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.secgroup.vo.SecgroupPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.secgroup.vo.SecgroupUpdateReqVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.secgroup.SecgroupDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 安全组 Service 接口
 *
 * <AUTHOR>
 */
public interface SecgroupService {

    /**
     * 创建安全组
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSecgroup(@Valid SecgroupCreateReqVO createReqVO);

    /**
     * 更新安全组
     *
     * @param updateReqVO 更新信息
     */
    void updateSecgroup(@Valid SecgroupUpdateReqVO updateReqVO);

    /**
     * 删除安全组
     *
     * @param id 编号
     */
    void deleteSecgroup(Long id);

    /**
     * 获得安全组
     *
     * @param id 编号
     * @return 安全组
     */
    SecgroupDO getSecgroup(Long id);

    /**
     * 获得安全组列表
     *
     * @param ids 编号
     * @return 安全组列表
     */
    List<SecgroupDO> getSecgroupList(Collection<Long> ids);

    /**
     * 获得安全组分页
     *
     * @param pageReqVO 分页查询
     * @return 安全组分页
     */
    PageResult<SecgroupDO> getSecgroupPage(SecgroupPageReqVO pageReqVO);

    /**
     * 获得安全组列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 安全组列表
     */
    List<SecgroupDO> getSecgroupList(SecgroupExportReqVO exportReqVO);

    void createSecgroupList(List<SecgroupDO> list);

    void updateSecgroups(List<SecgroupDO> list);

    void deleteSecgroups(List<SecgroupDO> list);

    List<SecgroupDO> getSecgroupListByHostUuid(String uuid);

    List<SecgroupRespDto> getSecgroupListByHostUuids(List<String> uuids);
}
