package cn.iocoder.zj.module.monitor.controller.admin.alarmconfigtemplate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 告警配置模板 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class AlarmConfigTemplateExcelVO {

    @ExcelProperty("主键ID")
    private Long id;

    @ExcelProperty("消息内容")
    private String context;

    @ExcelProperty("告警名称")
    private String alarmName;

    @ExcelProperty("告警简介")
    private String description;

    @ExcelProperty("资源类型，host云主机；hardware物理机；storage存储；image镜像")
    private String sourceType;

    @ExcelProperty("字典名称")
    private String dictLabelName;

    @ExcelProperty("字典类型")
    private String dictLabelType;

    @ExcelProperty("字典值")
    private String dictLabelValue;

    @ExcelProperty("触发规则")
    private String alarmRule;

    @ExcelProperty("告警阈值")
    private Integer alarmVal;

    @ExcelProperty("告警条目单位")
    private String unit;

    @ExcelProperty("收敛次数")
    private Long alarmTime;

    @ExcelProperty("告警级别：1提示，2警告，3严重")
    private Integer alarmLevel;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("告警条目单位的字典项code")
    private String unitType;

}
