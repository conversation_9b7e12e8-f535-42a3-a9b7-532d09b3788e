package cn.iocoder.zj.module.monitor.controller.admin.tags.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 标签 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class TagsExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("名称")
    private String name;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("资源总数")
    private Long totalCount;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
