package cn.iocoder.zj.module.monitor.controller.admin.gatherasset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName : GatherAssetDataReqVo  //类名
 * @Description : 资产查询  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/9/4  13:36
 */
@Data
public class GatherAssetDataReqVO {
    @Schema(required = true, description = "开始时间")
    private Long startTime;

    @Schema(required = true, description = "结束时间")
    private Long endTime;

    @Schema(required = true, description = "主机UUID")
    private String uuid;

    @Schema(required = true, description = "设备ip")
    private String ip;
}
