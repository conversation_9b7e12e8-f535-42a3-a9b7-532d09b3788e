package cn.iocoder.zj.module.monitor.dal.mysql.home.devopsscreen;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface DevOpsScreenMapper {
    List<Map> getResourceDistribution();

    List<Map<String,Object>> getLoginRanking();

    Map getRunningAsset();

    Map getUserCount();

    List<Map> abnormalUsage(@Param("platformId") Long platformId);

    List<Map> storageExceptions(@Param("platformId") Long platformId);
}
