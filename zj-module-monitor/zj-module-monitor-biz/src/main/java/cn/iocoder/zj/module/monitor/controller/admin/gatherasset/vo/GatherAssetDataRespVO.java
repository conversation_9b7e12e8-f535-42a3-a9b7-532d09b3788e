package cn.iocoder.zj.module.monitor.controller.admin.gatherasset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName : GatherAssetDataResoVO  //类名
 * @Description : 资产返回数据  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/9/4  13:39
 */
@Data
public class GatherAssetDataRespVO {


    @Schema(description = "设备uuid")
    private String uuid;

    @Schema(description = "采集时间")
    private String time;

    @Schema(description = "cpu使用率")
    private Double cpuValue;
    @Schema(description = "磁盘使用率")
    private Double diskValue;
    @Schema(description = "内存使用率")
    private Double memoryValue;
    @Schema(description = "平台id")
    private Long platformId;
    @Schema(description = "平台名称")
    private String platformName;
    @Schema(description = "主机名称")
    private String productsName;
}
