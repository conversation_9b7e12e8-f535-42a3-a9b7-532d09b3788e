package cn.iocoder.zj.module.monitor.dal.mysql.secgrouprule;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.controller.admin.secgrouprule.vo.SecgroupRuleExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.secgrouprule.vo.SecgroupRulePageReqVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.secgrouprule.SecgroupRuleDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 端口组规则 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SecgroupRuleMapper extends BaseMapperX<SecgroupRuleDO> {

    default PageResult<SecgroupRuleDO> selectPage(SecgroupRulePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SecgroupRuleDO>()
                .eqIfPresent(SecgroupRuleDO::getUuid, reqVO.getUuid())
                .eqIfPresent(SecgroupRuleDO::getStatus, reqVO.getStatus())
                .eqIfPresent(SecgroupRuleDO::getSecgroupUuid, reqVO.getSecgroupUuid())
                .eqIfPresent(SecgroupRuleDO::getPriority, reqVO.getPriority())
                .eqIfPresent(SecgroupRuleDO::getProtocol, reqVO.getProtocol())
                .eqIfPresent(SecgroupRuleDO::getPorts, reqVO.getPorts())
                .eqIfPresent(SecgroupRuleDO::getDirection, reqVO.getDirection())
                .eqIfPresent(SecgroupRuleDO::getCidr, reqVO.getCidr())
                .eqIfPresent(SecgroupRuleDO::getAction, reqVO.getAction())
                .eqIfPresent(SecgroupRuleDO::getDescription, reqVO.getDescription())
                .eqIfPresent(SecgroupRuleDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(SecgroupRuleDO::getPlatformName, reqVO.getPlatformName())
                .orderByDesc(SecgroupRuleDO::getId));
    }

    default List<SecgroupRuleDO> selectList(SecgroupRuleExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SecgroupRuleDO>()
                .eqIfPresent(SecgroupRuleDO::getUuid, reqVO.getUuid())
                .eqIfPresent(SecgroupRuleDO::getStatus, reqVO.getStatus())
                .eqIfPresent(SecgroupRuleDO::getSecgroupUuid, reqVO.getSecgroupUuid())
                .eqIfPresent(SecgroupRuleDO::getPriority, reqVO.getPriority())
                .eqIfPresent(SecgroupRuleDO::getProtocol, reqVO.getProtocol())
                .eqIfPresent(SecgroupRuleDO::getPorts, reqVO.getPorts())
                .eqIfPresent(SecgroupRuleDO::getDirection, reqVO.getDirection())
                .eqIfPresent(SecgroupRuleDO::getCidr, reqVO.getCidr())
                .eqIfPresent(SecgroupRuleDO::getAction, reqVO.getAction())
                .eqIfPresent(SecgroupRuleDO::getDescription, reqVO.getDescription())
                .eqIfPresent(SecgroupRuleDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(SecgroupRuleDO::getPlatformName, reqVO.getPlatformName())
                .orderByDesc(SecgroupRuleDO::getId));
    }

    @TenantIgnore
    default List<SecgroupRuleDO> getListBySecgroupUuids(List<String> secgroupUuids) {
        return selectList(new LambdaQueryWrapperX<SecgroupRuleDO>()
                .inIfPresent(SecgroupRuleDO::getSecgroupUuid, secgroupUuids));
    }

}
