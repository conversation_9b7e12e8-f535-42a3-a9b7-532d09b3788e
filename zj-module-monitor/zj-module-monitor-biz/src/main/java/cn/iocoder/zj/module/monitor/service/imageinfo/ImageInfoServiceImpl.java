package cn.iocoder.zj.module.monitor.service.imageinfo;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.util.object.BeanUtils;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.monitor.controller.admin.imageinfo.vo.ImageInfoCreateReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.imageinfo.vo.ImageInfoExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.imageinfo.vo.ImageInfoPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.imageinfo.vo.ImageInfoUpdateReqVO;
import cn.iocoder.zj.module.monitor.convert.imageinfo.ImageInfoConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.imageinfo.ImageInfoDO;
import cn.iocoder.zj.module.monitor.dal.mysql.imageinfo.ImageInfoMapper;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.IMAGE_INFO_NOT_EXISTS;

/**
 * 镜像信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ImageInfoServiceImpl implements ImageInfoService {

    @Resource
    private ImageInfoMapper imageInfoMapper;
    @Resource
    private PlatformconfigApi platformconfigApi;

    @Override
    public Long createImageInfo(ImageInfoCreateReqVO createReqVO) {
        // 插入
        ImageInfoDO imageInfo = ImageInfoConvert.INSTANCE.convert(createReqVO);
        imageInfoMapper.insert(imageInfo);
        // 返回
        return imageInfo.getId();
    }
    @Override
    public void batchCreateImageInfo(List<ImageInfoCreateReqVO> createReqVOList) {
        List<ImageInfoDO> imageInfos = BeanUtils.toBean(createReqVOList, ImageInfoDO.class);
        imageInfoMapper.insertBatch(imageInfos);
    }

    @Override
    public void batchUpdateImageInfo(List<ImageInfoCreateReqVO> updateReqVOList) {
        List<ImageInfoDO> imageInfos = BeanUtil.copyToList(updateReqVOList, ImageInfoDO.class);
        imageInfoMapper.batchUpdateImageInfo(imageInfos);
    }

    @Override
    public void batchDeleteImageInfo(List<ImageInfoCreateReqVO> deleteReqVOList) {
        List<Long> ids = ImageInfoConvert.INSTANCE.convertCreateList(deleteReqVOList)
                .stream().map(ImageInfoDO::getId).toList();
        imageInfoMapper.deleteBatchIds(ids);
    }

    @Override
    public void updateImageInfo(ImageInfoUpdateReqVO updateReqVO) {
        // 校验存在
        validateImageInfoExists(updateReqVO.getId());
        // 更新
        ImageInfoDO updateObj = ImageInfoConvert.INSTANCE.convert(updateReqVO);
        imageInfoMapper.updateById(updateObj);
    }

    @Override
    public void deleteImageInfo(Long id) {
        // 校验存在
        validateImageInfoExists(id);
        // 删除
        imageInfoMapper.deleteById(id);
    }

    private void validateImageInfoExists(Long id) {
        if (imageInfoMapper.selectById(id) == null) {
            throw exception(IMAGE_INFO_NOT_EXISTS);
        }
    }

    @Override
    public ImageInfoDO getImageInfo(Long id) {
        return imageInfoMapper.selectById(id);
    }

    @Override
    public List<ImageInfoDO> getImageInfoList(Collection<Long> ids) {
        return imageInfoMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ImageInfoDO> getImageInfoPage(ImageInfoPageReqVO pageReqVO) {
        List<Map> platform = new ArrayList<>();
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser != null) {
            platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
        }
        return imageInfoMapper.selectPage(pageReqVO,platform);
    }

    @Override
    public List<ImageInfoDO> getImageInfoList(ImageInfoExportReqVO exportReqVO) {
        List<Map> platform = new ArrayList<>();
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser != null) {
            platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
        }
        return imageInfoMapper.selectList(exportReqVO,platform);
    }

    @Override
    public List<ImageInfoDO> getImageInfosByPlatformId(Long platformId) {
        return imageInfoMapper.getImageInfosByPlatformId(platformId);
    }

    @Override
    public List<ImageInfoDO> getAllImagesByTypeCode(String typeCode) {
        return imageInfoMapper.getAllImagesByTypeCode(typeCode);
    }

    @Override
    public Map<String, Object> getImageStatusCount(List<String> tenantIds, Long platformId) {
        Map<String, Object> map = imageInfoMapper.getImageStatusCount(tenantIds,platformId);
        if (map.size() == 1) {
            map = new HashMap<>();
            map.put("total", 0);
            map.put("available", 0);
            map.put("other", 0);
        }
        return map;
    }

}
