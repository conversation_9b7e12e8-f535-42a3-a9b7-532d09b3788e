package cn.iocoder.zj.module.monitor.dal.mysql.topreport;

import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.controller.admin.topreport.vo.ReportMetrics;
import cn.iocoder.zj.module.monitor.dal.dataobject.topreport.TopReportMetricDO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@Mapper
public interface TopReportMetricMapper extends BaseMapperX<TopReportMetricDO> {

    default List<TopReportMetricDO> selectByReportId(Long reportId) {
        return selectList(TopReportMetricDO::getReportId, reportId);
    }

    default List<TopReportMetricDO> selectByReportIds(Collection<Long> reportIds) {
        return selectList(TopReportMetricDO::getReportId, reportIds);
    }

    default void deleteByReportId(Long reportId) {
        delete(new LambdaQueryWrapperX<TopReportMetricDO>()
                .eq(TopReportMetricDO::getReportId, reportId));
    }

    @TenantIgnore
    void delById(@Param("id")Long id);

    @TenantIgnore
    List<ReportMetrics> getreportmetric(@Param("id")Long id);

    @TenantIgnore
    ReportMetrics getpidmetric(@Param("id")Long id);
    @TenantIgnore
    List<String> getmetrics();

    @TenantIgnore
    @MapKey("id")
    Map<Long,Map<Long,String>> getPids(@Param("pids") List<Long> pids);
}
