package cn.iocoder.zj.module.monitor.controller.admin.eip.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 弹性公网 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class EipBaseVO {

    @Schema(description = "名称")
    private String name;

    @Schema(description = "uuid")
    private String uuid;

    @Schema(description = "ip地址")
    private String ipAddr;

    @Schema(description = "带宽")
    private Integer bandwidth;

    @Schema(description = "网络uuid")
    private String networkId;

    @Schema(description = "资源类型")
    private String associateType;

    @Schema(description = "资源id")
    private String associateId;

    @Schema(description = "计费类型")
    private String chargeType;

    @Schema(description = "区域id")
    private String cloudregionId;

    @Schema(description = "public_ip 公网IP  elastic_ip弹性公网IP")
    private String mode;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

}
