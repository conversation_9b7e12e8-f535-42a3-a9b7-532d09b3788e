package cn.iocoder.zj.module.monitor.service.hostnic;

import cn.iocoder.zj.module.monitor.controller.admin.hostinfo.vo.HostInfoRpcVO;
import cn.iocoder.zj.module.monitor.convert.hostinfo.HostInfoConvert;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.zj.module.monitor.controller.admin.hostnic.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostnic.HostNicDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.convert.hostnic.HostNicConvert;
import cn.iocoder.zj.module.monitor.dal.mysql.hostnic.HostNicMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.*;

/**
 * 云主机网络 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class HostNicServiceImpl implements HostNicService {

    @Resource
    private HostNicMapper hostNicMapper;

    @Override
    public Long createHostNic(HostNicCreateReqVO createReqVO) {
        // 插入
        HostNicDO hostNic = HostNicConvert.INSTANCE.convert(createReqVO);
        hostNicMapper.insert(hostNic);
        // 返回
        return hostNic.getId();
    }

    @Override
    public void updateHostNic(HostNicUpdateReqVO updateReqVO) {
        // 校验存在
        validateHostNicExists(updateReqVO.getId());
        // 更新
        HostNicDO updateObj = HostNicConvert.INSTANCE.convert(updateReqVO);
        hostNicMapper.updateById(updateObj);
    }

    @Override
    public void deleteHostNic(Long id) {
        // 校验存在
        validateHostNicExists(id);
        // 删除
        hostNicMapper.deleteById(id);
    }

    private void validateHostNicExists(Long id) {
        if (hostNicMapper.selectById(id) == null) {
            throw exception(HOST_NIC_NOT_EXISTS);
        }
    }

    @Override
    public HostNicDO getHostNic(Long id) {
        return hostNicMapper.selectById(id);
    }

    @Override
    public List<HostNicDO> getHostNicList(Collection<Long> ids) {
        return hostNicMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<HostNicDO> getHostNicPage(HostNicPageReqVO pageReqVO) {
        return hostNicMapper.selectPage(pageReqVO);
    }

    @Override
    public List<HostNicDO> getHostNicList(HostNicExportReqVO exportReqVO) {
        return hostNicMapper.selectList(exportReqVO);
    }

    @Override
    public void createHostNicList(List<HostNicDO> reqDTO) {
        hostNicMapper.insertBatch(reqDTO);
    }

    @Override
    public void updateHostNics(List<HostNicDO> list) {
        hostNicMapper.updateBatch(list);
    }

    @Override
    public void deleteHostNics(List<HostNicDO> list) {
        List<Long> ids = list.stream().map(HostNicDO::getId).toList();
        hostNicMapper.deleteBatchIds(ids);
    }

}
