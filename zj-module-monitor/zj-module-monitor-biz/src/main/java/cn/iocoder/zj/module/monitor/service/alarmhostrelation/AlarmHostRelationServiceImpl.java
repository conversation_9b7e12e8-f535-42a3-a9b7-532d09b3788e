package cn.iocoder.zj.module.monitor.service.alarmhostrelation;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.query.QueryWrapperX;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmconfig.AlarmConfigDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.hardwareinfo.HardwareInfoDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostinfo.HostInfoDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.storageinfo.StorageInfoDO;
import cn.iocoder.zj.module.monitor.dal.redis.zstack.AlarmHostRelationDAO;
import cn.iocoder.zj.module.monitor.service.alarmconfig.AlarmConfigService;
import cn.iocoder.zj.module.monitor.service.hardwareinfo.HardwareInfoService;
import cn.iocoder.zj.module.monitor.service.hostinfo.HostInfoService;
import cn.iocoder.zj.module.monitor.service.storageinfo.StorageInfoService;
import cn.iocoder.zj.module.system.api.tenant.TenantApi;
import cn.iocoder.zj.module.system.api.tenant.dto.TenantRespDTO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import groovy.lang.Lazy;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.awt.geom.Area;
import java.util.*;
import java.util.stream.Collectors;

import cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmhostrelation.AlarmHostRelationDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.dal.mysql.alarmhostrelation.AlarmHostRelationMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.*;

/**
 * 告警配置与云主机关联关系 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AlarmHostRelationServiceImpl implements AlarmHostRelationService {

    @Resource
    private AlarmHostRelationMapper alarmHostRelationMapper;

    @Resource
    private AlarmHostRelationDAO alarmHostRelationDAO;
    @Resource
    private AlarmConfigService alarmConfigService;
    @Resource
    private HostInfoService hostInfoService;
    @Resource
    private HardwareInfoService hardwareInfoService;
    @Resource
    private StorageInfoService storageInfoService;
    @Resource
    private TenantApi tenantApi;



    @Override
    public void updateAlarmHostRelation(RelationRqeVO reqVO) {
        // 校验存在
        validateAlarmHostRelationExists(reqVO.getId());
        // 更新状态
        alarmHostRelationMapper.updateStatus(reqVO);
        setAlarmRedisInfo();
    }

    @Override
    public void deleteAlarmHostRelation(Long id) {
        // 校验存在
        validateAlarmHostRelationExists(id);
        // 删除
        alarmHostRelationMapper.deleteById(id);
        setAlarmRedisInfo();
    }

    private void validateAlarmHostRelationExists(Long id) {
        if (alarmHostRelationMapper.selectById(id) == null) {
            throw exception(ALARM_HOST_RELATION_NOT_EXISTS);
        }
    }

    @Override
    public AlarmHostRelationDO getAlarmHostRelation(Long id) {
        return alarmHostRelationMapper.selectById(id);
    }

    @Override
    public List<AlarmHostRelationDO> getAlarmHostRelationList(Collection<Long> ids) {
        return alarmHostRelationMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<AlarmHostRelationDO> getAlarmHostRelationPage(AlarmHostRelationPageReqVO pageReqVO) {
        return alarmHostRelationMapper.selectPage(pageReqVO);
    }

    @Override
    public List<AlarmHostRelationDO> getAlarmHostRelationList(AlarmHostRelationExportReqVO exportReqVO) {
        return alarmHostRelationMapper.selectList(exportReqVO);
    }
    private void setAlarmRedisInfo(){
        List<AlarmHostRelationDO> alarmConfigDOList =  this.alarmHostRelationMapper.selectList();
        alarmHostRelationDAO.setAlarmHostRelationList("alarm_host_relation",alarmConfigDOList);
    }
    @Override
    public void deletedByAlarmConfigId(Long alarmConfigId){
        alarmHostRelationMapper.deleteByAlarmConfigId(alarmConfigId);
    }

    @Override
    public Long hostSetAlarms(RelationRqeVO reqVO) {
        List<String> uuids = reqVO.getUuids().stream()
                .map(Object::toString)
                .collect(Collectors.toList());
        List<String> alarmIds = reqVO.getIds().stream()
                .map(Object::toString)
                .collect(Collectors.toList());
        if(reqVO.getConfigWay().equals("device")) {
            alarmHostRelationMapper.deleteByUuids(uuids);
        } else if (reqVO.getConfigWay().equals("alarm_config")) {
            alarmHostRelationMapper.deleteByAlarmConfigId(Long.valueOf(alarmIds.get(0)));
        }
        if(uuids.size()>0) {
                List<Map<String, Object>> targets = new ArrayList<>();
                if (reqVO.getSourceType().equals("host")) {
                    //为虚拟机创建告警关联
                    targets = convertHostListToMaps(hostInfoService.getHostListByUuids(uuids));
                } else if (reqVO.getSourceType().equals("hardware")) {
                    //为宿主机创建告警关联
                    targets = convertHardwareListToMaps(hardwareInfoService.getHardwareListByUuids(uuids));
                } else if (reqVO.getSourceType().equals("storage")) {
                    //为主存储创建告警关联
                    targets = convertStorageListToMaps(storageInfoService.getStorageListByUuids(uuids));
                }
                List<AlarmConfigDO> alarmConfigs = alarmConfigService.getAlarmConfigList(reqVO.getIds(),"");
                LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
                CommonResult<TenantRespDTO> tenant = tenantApi.getTenantSimpleInfo(loginUser.getTenantId());
                if(CollectionUtil.isNotEmpty(targets)&& CollectionUtil.isNotEmpty(alarmConfigs)) {
                    alarmHostRelationMapper.createRelationByHost(targets, alarmConfigs, tenant.getData());
                }
            }
        setAlarmRedisInfo();
        return alarmHostRelationMapper.getLatestId();
    }

    @Override
    public Long hostUpdateAlarms(RelationRqeVO reqVO) {
        alarmHostRelationMapper.deleteByUuid(reqVO.getUuid());
        return  hostSetAlarms(reqVO);
    }

    @Override
    public Collection<Long> getRelationIdsByUuid(String uuid) {
        return alarmHostRelationMapper.getRelationByUuid(uuid);
    }


    private List<Map<String, Object>> convertHostListToMaps(List<HostInfoDO> hostInfoList) {
        return hostInfoList.stream()
                .map(HostInfoDO::toMap)
                .collect(Collectors.toList());
    }

    private List<Map<String, Object>> convertHardwareListToMaps(List<HardwareInfoDO> hardwareInfoList) {
        return hardwareInfoList.stream()
                .map(HardwareInfoDO::toMap)
                .collect(Collectors.toList());
    }
    private List<Map<String, Object>> convertStorageListToMaps(List<StorageInfoDO> hardwareInfoList) {
        return hardwareInfoList.stream()
                .map(StorageInfoDO::toMap)
                .collect(Collectors.toList());
    }
}
