package cn.iocoder.zj.module.monitor.service.alarmnotice;

import java.util.*;
import javax.validation.*;
import cn.iocoder.zj.module.monitor.controller.admin.alarmnotice.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmnotice.AlarmNoticeDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 告警与通知模板关系 Service 接口
 *
 * <AUTHOR>
 */
public interface AlarmNoticeService {

    /**
     * 创建告警与通知模板关系
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAlarmNotice(@Valid AlarmNoticeCreateReqVO createReqVO);

    /**
     * 更新告警与通知模板关系
     *
     * @param updateReqVO 更新信息
     */
    void updateAlarmNotice(@Valid AlarmNoticeUpdateReqVO updateReqVO);

    /**
     * 删除告警与通知模板关系
     *
     * @param id 编号
     */
    void deleteAlarmNotice(Long id);

    /**
     * 获得告警与通知模板关系
     *
     * @param id 编号
     * @return 告警与通知模板关系
     */
    AlarmNoticeDO getAlarmNotice(Long id);

    /**
     * 获得告警与通知模板关系列表
     *
     * @param ids 编号
     * @return 告警与通知模板关系列表
     */
    List<AlarmNoticeDO> getAlarmNoticeList(Collection<Long> ids);

    /**
     * 获得告警与通知模板关系分页
     *
     * @param pageReqVO 分页查询
     * @return 告警与通知模板关系分页
     */
    PageResult<AlarmNoticeDO> getAlarmNoticePage(AlarmNoticePageReqVO pageReqVO);

    /**
     * 获得告警与通知模板关系列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 告警与通知模板关系列表
     */
    List<AlarmNoticeDO> getAlarmNoticeList(AlarmNoticeExportReqVO exportReqVO);

}
