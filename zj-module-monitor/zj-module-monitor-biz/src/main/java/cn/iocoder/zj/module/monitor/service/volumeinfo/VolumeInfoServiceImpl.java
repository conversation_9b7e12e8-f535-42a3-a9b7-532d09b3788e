package cn.iocoder.zj.module.monitor.service.volumeinfo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.collection.api.VolumeApi;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeAttachableVmDTO;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeDTO;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeSnapshotDTO;
import cn.iocoder.zj.module.monitor.controller.admin.volumeinfo.vo.*;
import cn.iocoder.zj.module.monitor.convert.volume.VolumeInfoConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.storageinfo.StorageInfoDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.tags.TagsDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.volumeinfo.VolumeInfoDO;
import cn.iocoder.zj.module.monitor.dal.mysql.storageinfo.StorageInfoMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.tags.TagsMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.volumeinfo.VolumeInfoMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.volumesnapshot.VolumeSnapshotMapper;
import cn.iocoder.zj.module.monitor.pojo.AssetReqVO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.VOLUME_INFO_NOT_EXISTS;

/**
 * 云盘信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class VolumeInfoServiceImpl implements VolumeInfoService {

    @Resource
    private VolumeInfoMapper volumeInfoMapper;

    @Resource
    private VolumeApi volumeApi;

    @Resource
    private PlatformconfigApi platformconfigApi;

    @Resource
    private VolumeSnapshotMapper volumeSnapshotMapper;
    @Resource
    private StorageInfoMapper  storageInfoMapper;

    @Resource
    private TagsMapper tagsMapper;

    @Override
    public Long createVolumeInfo(VolumeInfoCreateReqVO createReqVO) {
        // 插入
        VolumeInfoDO volumeInfo = VolumeInfoConvert.INSTANCE.convert(createReqVO);
        volumeInfoMapper.insert(volumeInfo);
        // 返回
        return volumeInfo.getId();
    }

    @Override
    public void updateVolumeInfo(VolumeInfoUpdateReqVO updateReqVO) {
        // 校验存在
        validateVolumeInfoExists(updateReqVO.getId());
        // 更新
        VolumeInfoDO updateObj = VolumeInfoConvert.INSTANCE.convert(updateReqVO);
        volumeInfoMapper.updateById(updateObj);
    }

    private void validateVolumeInfoExists(Long id) {
        if (volumeInfoMapper.selectById(id) == null) {
            throw exception(VOLUME_INFO_NOT_EXISTS);
        }
    }

    @Override
    public VolumeInfoDO getVolumeInfo(Long id) {
        VolumeInfoDO volumeInfoDO = volumeInfoMapper.selectById(id);
        if (volumeInfoDO.getPrimaryStorageUuid()!=null && !StrUtil.isBlank(volumeInfoDO.getPrimaryStorageUuid())) {
            StorageInfoDO storageInfoDO = storageInfoMapper.getByUuid(volumeInfoDO.getPrimaryStorageUuid());
            if(BeanUtil.isNotEmpty(storageInfoDO) && StrUtil.isNotEmpty(storageInfoDO.getName())){
                volumeInfoDO.setPrimaryStorageName(storageInfoDO.getName());
            }
        }
        return volumeInfoDO;
    }

    @Override
    public List<VolumeInfoDO> getVolumeInfoList(Collection<Long> ids) {
        return volumeInfoMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<VolumeInfoDO> getVolumeInfoPage(VolumeInfoPageReqVO pageReqVO) {
        List<Map> platform = new ArrayList<>();
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser != null) {
            platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
        }
        return volumeInfoMapper.selectPage(pageReqVO,platform);
    }

    @Override
    public List<VolumeInfoDO> getVolumeInfoList(VolumeInfoExportReqVO exportReqVO) {
        List<Map> platform = new ArrayList<>();
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser != null) {
            platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
        }
        return volumeInfoMapper.selectList(exportReqVO,platform);
    }
    @Override
    public Long getVolumeCount() {
        return volumeInfoMapper.selectCount();
    }
    @Override
    public List<VolumeDTO> getAll(Long id,String typeCode) {
        List<VolumeInfoDO> volumeInfoDOList =  volumeInfoMapper.getNotUsedVolum(id,typeCode);
        return VolumeInfoConvert.INSTANCE.DOConvertToDTO(volumeInfoDOList);
    }

    @Override
    public void updateVolumes(List<VolumeDTO> updateReqVOs) {
        for (VolumeDTO volumeDTO : updateReqVOs) {
            volumeDTO.setMediaType(null);
        }
        volumeInfoMapper.updateVolumeBatch(updateReqVOs);
    }

    @Override
    public void addVolumes(List<VolumeInfoDO> updateReqVOs) {
        for (VolumeInfoDO volumeInfoDO : updateReqVOs) {
            volumeInfoDO.setMediaType("");
            if (volumeInfoDO.getPrimaryStorageUuid() != null && !StrUtil.isBlank(volumeInfoDO.getPrimaryStorageUuid())) {
                StorageInfoDO storageInfoDO = storageInfoMapper.getByUuid(volumeInfoDO.getPrimaryStorageUuid());
                if (storageInfoDO != null && storageInfoDO.getMediaType() != null) {
                    volumeInfoDO.setMediaType(storageInfoDO.getMediaType());
                }
            }
        }
        volumeInfoMapper.insertBatch(updateReqVOs);
    }

    @Override
    public CommonResult<Map<String,String>> mountVolumeToHost(String volumeUuid, String hostUuid, Long platformId) {
        return volumeApi.mountVolumeToHost(volumeUuid,hostUuid,platformId);
    }

    @Override
    public void mountVolumeToHardware(String volumeUuid, String hardwareUuid, Long platformId, String mountPath) {
        volumeApi.mountVolumeToHardware(volumeUuid,hardwareUuid,platformId,mountPath);
    }

    @Override
    public CommonResult<Map<String,String>> uninstallVolumeFromHost(String volumeUuid, String hostUuid, Long platformId) {
        return volumeApi.uninstallVolumeFromHost(volumeUuid,hostUuid,platformId);
    }

    @Override
    public void uninstallVolumeFromHardware(String volumeUuid, String hardwareUuid, Long platformId) {
        volumeApi.uninstallVolumeFromHardware(volumeUuid,hardwareUuid,platformId);
    }

    @Override
    public PageResult<VolumeInfoRespVO> getVolumeAttachableVmByHostUuid(String hostUuid, Integer pageNo, Integer pageSize, String queryData) {
        PageParam pageParam = new PageParam().setPageNo(pageNo).setPageSize(pageSize);
        IPage<VolumeInfoRespVO> mpPage = MyBatisUtils.buildPage(pageParam);
        return new PageResult<>(volumeInfoMapper.getVolumeAttachableVmByHostUuid(mpPage,hostUuid,queryData),mpPage.getTotal());
    }

    @Override
    public Long getAttachableVmCount() {
        return volumeInfoMapper.getAttachableVmCount();
    }

    @Override
    public List<VolumeAttachableVmDTO> getAllVolumeAttachableVm() {
        return volumeInfoMapper.getAllVolumeAttachableVm();
    }

    @Override
    public void addVolumeAttachableVms(List<VolumeAttachableVmDTO> shardingData) {
        volumeInfoMapper.addVolumeAttachableVmBatch(shardingData);
    }

    @Override
    public void updateVolumeAttachableVms(List<VolumeAttachableVmDTO> shardingData) {
        volumeInfoMapper.updateVolumeAttachableVmBatch(shardingData);
    }

    @Override
    public void delVolumeAttachableVms(List<VolumeAttachableVmDTO> deleteTarget) {
        volumeInfoMapper.delVolumeAttachableVms(deleteTarget);
    }

    @Override
    public void delVolumes(List<VolumeDTO> deleteTarget) {
        volumeInfoMapper.delVolumes(deleteTarget);
    }

    @Override
    public void delVolumeSnapshots(List<VolumeSnapshotDTO> deleteTarget) {
        volumeInfoMapper.delVolumeSnapshots(deleteTarget);
    }

    @Override
    public void delVolumesByplatform(Long platformId) {
        volumeInfoMapper.delVolumesByplatform(platformId);
    }

    @Override
    public void delVolumeAttachableVmByplatform(Long platformId) {
        volumeInfoMapper.delVolumeAttachableVmByplatform(platformId);
    }

    @Override
    public void delVolumeByInstanceUuid(List<String> deleteList) {
        volumeInfoMapper.delVolumeByInstanceUuid(deleteList);
    }

    @Override
    @TenantIgnore
    public List<VolumeInfoUseReqVO> getVolumeInfoListByUuid(String uuid) {
        LambdaQueryWrapper<VolumeInfoDO> lqw = new LambdaQueryWrapper<>();
        lqw.eq(VolumeInfoDO::getVmInstanceUuid,uuid)
                .groupBy(VolumeInfoDO::getUuid);
        List<VolumeInfoDO> volumeInfoDOS = volumeInfoMapper.selectList(lqw);
        return volumeInfoDOS.stream().map(r->{
            VolumeInfoUseReqVO volumeInfoUseReqVO = new VolumeInfoUseReqVO();
            volumeInfoUseReqVO.setActualUse(unitConvert(r.getActualUse()));
            volumeInfoUseReqVO.setSize(unitConvert(r.getSize()));
            volumeInfoUseReqVO.setName(r.getName());
            volumeInfoUseReqVO.setType(r.getType());
            return volumeInfoUseReqVO;
        }).collect(Collectors.toList());
    }

    @Override
    @TenantIgnore
    public List<VolumeDTO> getVolumeByPlatformId(Long id) {
        return volumeInfoMapper.getVolumeByPlatformId(id);
    }

    @Override
    @TenantIgnore
    public String getVolumeByVmUuid(String domainId) {
        return volumeInfoMapper.getVolumeByVmUuid(domainId);

    }

    @Override
    public List<VolumeDTO> getVolumesByPlatformId(Long id) {
        return volumeInfoMapper.getVolumesByPlatformId(id);
    }

    @Override
    @TenantIgnore
    public Integer getVolumeCountByStorageUuid(String storageUuid) {
        return volumeInfoMapper.getVolumeCountByStorageUuid(storageUuid);
    }

    @Override
    public Map<String, Object> getVolumeStatusCount(List<String> tenantIds, Long platformId) {
        Map<String, Object> map = volumeInfoMapper.getVolumeStatusCount(tenantIds,platformId);
        if (map.size() == 1) {
            map = new HashMap<>();
            map.put("total", 0);
            map.put("available", 0);
            map.put("other", 0);
        }
        return map;
    }

    @Override
    public List<VolumeSnapshotDTO> getVolumeSnapshotByPlatformId(Long id) {
        return volumeSnapshotMapper.getVolumeSnapshotByPlatformId(id);
    }

    @Override
    @TenantIgnore
    public List<VolumeInfoDO> getVolumeByTenantOrPlatforms(AssetReqVO assetReqVO) {
        List<Long> platformIds;
        if (CollectionUtil.isNotEmpty(assetReqVO.getPlatformIds())) {
            platformIds = assetReqVO.getPlatformIds();
            return volumeInfoMapper.selectListByPlatformIds(platformIds);
        } else {
            List<Map> platform = platformconfigApi.getPlatformByTenantId(String.valueOf(assetReqVO.getTenantId())).getData();
            return volumeInfoMapper.selectListByPlatform(platform);
        }
    }

    @Override
    public List<VolumeInfoDO> getByPlatformIdAndTags(AssetReqVO reqVO) {
        List<Long> platformIds = reqVO.getPlatformIds();
        List<Long> tagIds = reqVO.getTags();

        List<String> tagNames = tagsMapper.selectList(TagsDO::getId, tagIds).stream()
                .map(TagsDO::getName)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        return volumeInfoMapper.getByPlatformIdAndTags(platformIds, tagNames);
    }

    public String unitConvert(Long size) {
        if (size <= 0) return "0";
        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        return new DecimalFormat("#,##0.#").format(size / Math.pow(1024, digitGroups)) + units[digitGroups];
    }

}
