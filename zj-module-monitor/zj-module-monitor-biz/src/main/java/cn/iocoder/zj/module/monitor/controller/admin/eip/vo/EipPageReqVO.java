package cn.iocoder.zj.module.monitor.controller.admin.eip.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 弹性公网分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EipPageReqVO extends PageParam {

    @Schema(description = "名称")
    private String name;

    @Schema(description = "uuid")
    private String uuid;

    @Schema(description = "ip地址")
    private String ipAddr;

    @Schema(description = "带宽")
    private Integer bandwidth;

    @Schema(description = "网络uuid")
    private String networkId;

    @Schema(description = "资源类型")
    private String associateType;

    @Schema(description = "资源id")
    private String associateId;

    @Schema(description = "计费类型")
    private String chargeType;

    @Schema(description = "区域id")
    private String cloudregionId;

    @Schema(description = "public_ip 公网IP  elastic_ip弹性公网IP")
    private String mode;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "ip")
    private String ip;

    /**
     * 弹性公网创建时间
     */
    private Date vCreateTime;

}
