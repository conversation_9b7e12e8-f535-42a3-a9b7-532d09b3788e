package cn.iocoder.zj.module.monitor.service.cloud;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.iocoder.zj.module.monitor.dal.redis.zstack.ZstackAccessTokenRedisDAO;
import cn.iocoder.zj.module.monitor.service.zstack.core.AbstractIZstack;
import cn.iocoder.zj.module.monitor.util.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.IdentityHashMap;
import java.util.Map;

/**
 * @ClassName : ZstackCloud  //类名
 * @Description : 云接口实现  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/25  17:23
 */


@Log4j2
@Component
public class ZstackCloudServiceImpl extends AbstractIZstack implements IZstackCloudService {

    @Autowired
    ZstackAccessTokenRedisDAO zstackAccessTokenRedisDAO;

    @Override
    public String cpuUsage() {

        String uuid = zstackAccessTokenRedisDAO.get("zstack").getUuid();

        Map<String, String> header = new HashMap<>();
        // 获取VMUuidList
        String uuidList = VMUuidList(uuid);
        // cpu使用率 top5
        IdentityHashMap<String, Object> p = new IdentityHashMap<>();
        p.put("metricName", "CPUAverageUsedUtilization");
        p.put("namespace", "ZStack/VM");
        p.put("offsetAheadOfCurrentTime", "1");
        p.put(new String("functions"), "average(groupBy=\"VMUuid\")");
        p.put(new String("functions"), "top(num=5)");
        p.put("labels", "VMUuid=~" + uuidList);

        header.put("Authorization", "OAuth " + uuid);
        header.put("Content-Type", "application/json;charset=UTF-8");

        HttpResponse result = HttpRequest.get("")
                .header(Header.AUTHORIZATION, "OAuth " + uuid)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .form(p)
                .execute();
        if (result.getStatus() != 200) {
            throw new RuntimeException("getcpuUsage error");
        }
        return result.body();
    }

    @Override
    public void memoryUsage() {

    }

    @Override
    public void netWorkUpdown() {

    }

    @Override
    public void netWorkSendOrHarvest() {

    }

    private String VMUuidList(String uuid) {
        Map<String, String> header = new HashMap<>();
        header.put("Authorization", "OAuth " + uuid);
        header.put("Content-Type", "application/json;charset=UTF-8");

        Map<String, Object> p = new IdentityHashMap<>();
        p.put("zoneUuid", uuid);
        p.put(new String("q"), "hypervisorType=KVM");
        p.put(new String("q"), "state=Running");
        p.put(new String("q"), "type=UserVm");
        // 获取uuidList
        HttpResponse result = HttpRequest.get("http://172.16.210.253:8080" + "/zstack/v1/vm-instances")
                .header(Header.AUTHORIZATION, "OAuth " + uuid)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .form(p)
                .execute();
        if (result.getStatus() != 200) {
            throw new RuntimeException("getVMUuidList error");
        }

        StringBuffer sb = new StringBuffer();
        JSONObject jsonObject = JSONObject.parseObject(result.body());

        JSONArray jsonArray = jsonObject.getJSONArray("inventories");
        if (jsonArray.size() > 0) {
            for (int i = 0; i < jsonArray.size(); i++) {
                sb.append(StringUtil.toString(jsonArray.getJSONObject(i).get("uuid"))).append("|");
            }
        }
        return sb.deleteCharAt(sb.length() - 1).toString();
    }

    @Override
    public void cloudInfo() {

    }

}
