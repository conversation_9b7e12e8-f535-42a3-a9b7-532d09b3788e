package cn.iocoder.zj.module.monitor.controller.admin.volumeinfo.vo;

import cn.iocoder.zj.module.monitor.controller.admin.tags.vo.TagsRespVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;
import java.util.List;

@Schema(description = "管理后台 - 云盘信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VolumeInfoRespVO extends VolumeInfoBaseVO {

    @Schema(description = "主键ID", required = true)
    private Long id;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Schema(description = "标签信息")
    private List<TagsRespVO> tags;

}
