package cn.iocoder.zj.module.monitor.api.asset;


import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.asset.dto.GatherAssetRespDTO;
import cn.iocoder.zj.module.monitor.convert.gatherasset.GatherAssetConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.gatherasset.GatherAssetDO;
import cn.iocoder.zj.module.monitor.service.gatherasset.GatherAssetService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

/**
 * @ClassName : GatherAssetApiImpl  //类名
 * @Description : 资产实现类  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/9/4  9:35
 */

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class GatherAssetApiImpl implements GatherAssetApi {

    @Resource
    private GatherAssetService gatherAssetService;

    @Override
    public CommonResult<List<GatherAssetRespDTO>> getAssetList() {
        List<GatherAssetRespDTO> list = GatherAssetConvert.INSTANCE.convertDto(gatherAssetService.getAssetList());
        return CommonResult.success(list);
    }

    @Override
    public CommonResult<Boolean> adds(List<GatherAssetRespDTO> reqDTO) {
        List<GatherAssetDO> list = GatherAssetConvert.INSTANCE.convertCreateList(reqDTO);
        gatherAssetService.createAssetInfoList(list);
        return CommonResult.success(true);
    }

    @Override
    public Long getGatherAssetCount(String deviceType) {
        return gatherAssetService.getGatherAssetCount(deviceType);
    }

    @Override
    public Long getGatherAssetCountBySysType(String sysType) {
        return gatherAssetService.getGatherAssetCountBySysType(sysType);
    }
}
