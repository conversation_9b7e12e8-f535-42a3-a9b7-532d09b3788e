package cn.iocoder.zj.module.monitor.controller.admin.gatherasset.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 租户资产分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GatherAssetPageReqVO extends PageParam {

    @Schema(description = "租户绑定的采集设备id")
    private String uuid;

    @Schema(description = "访问设备的ip地址 (例：udp:*************)")
    private String ip;

    @Schema(description = "snmp设置的团体名")
    private String community;

    @Schema(description = "snmp版本 (0=v1; 1=v2c; 2=v3)")
    private Integer version;

    @Schema(description = "snmp 端口号")
    private Integer snmpPort;

    @Schema(description = "主机名称")
    private String name;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "平台名称")
    private String platformName;
    @Schema(description = "平台id")
    private Long platformId;

    @Schema(required = false,description = "主机过滤ids逗号拼接,'1','2','3'")
    private String ids;

    @Schema(description = "资产类型图标")
    private String dictIcon;
    /**
     * @description: 资产类型
     * <AUTHOR>
     * @date 2023/8/14 15:49
     * @version 1.0
     */
    @Schema(description = "资产类型")
    private String deviceType;
    /**
     * @description: 资产类型名称
     * <AUTHOR>
     * @date 2023/8/14 15:49
     * @version 1.0
     */
    @Schema(description = "资产类型名称")
    private String deviceName;
    /**
     * @description: 系统类型
     * <AUTHOR>
     * @date 2023/8/14 15:49
     * @version 1.0
     */
    @Schema(description = "系统类型")
    private String sysType;
    /**
     * @description: 系统类型名称
     * <AUTHOR>
     * @date 2023/8/14 15:49
     * @version 1.0
     */
    @Schema(description = "系统类型名称")
    private String sysName;


    /**
     * @description: 在线状态，0 离线 ，1在线
     * <AUTHOR>
     * @date 2023/8/14 15:52
     * @version 1.0
     */
    @Schema(description = "在线状态，0 离线，1在线")
    private Integer onlineType;



    /**
     * cpu 使用率
     */
    @Schema(description = "cpu使用率")
    private Double cpuUsage;

    /**
     * 内存使用率
     */
    @Schema(description = "内存使用率")
    private Double memoryUsage;


    /**
     * 磁盘使用率
     */
    @Schema(description = "磁盘使用率")
    private Double diskUsage;
}
