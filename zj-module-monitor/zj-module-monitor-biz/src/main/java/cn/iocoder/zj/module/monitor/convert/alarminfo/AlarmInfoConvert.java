package cn.iocoder.zj.module.monitor.convert.alarminfo;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.alarminfo.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarminfo.AlarmInfoDO;

/**
 * 监控告警详情 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface AlarmInfoConvert {

    AlarmInfoConvert INSTANCE = Mappers.getMapper(AlarmInfoConvert.class);

    AlarmInfoDO convert(AlarmInfoCreateReqVO bean);

    AlarmInfoDO convert(AlarmInfoUpdateReqVO bean);

    AlarmInfoRespVO convert(AlarmInfoDO bean);

    List<AlarmInfoRespVO> convertList(List<AlarmInfoDO> list);

    PageResult<AlarmInfoRespVO> convertPage(PageResult<AlarmInfoDO> page);

    List<AlarmInfoExcelVO> convertList02(List<AlarmInfoDO> list);

}
