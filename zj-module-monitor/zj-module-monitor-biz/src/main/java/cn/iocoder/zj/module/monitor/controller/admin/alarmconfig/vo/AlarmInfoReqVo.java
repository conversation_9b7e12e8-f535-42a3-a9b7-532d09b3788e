package cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.List;

@Schema(description = "管理后台 - 告警信息分页 Request VO")
@Data
@ToString(callSuper = true)
public class AlarmInfoReqVo {
    @Schema(required = true,description = "页码")
    private Integer pageNo;
    @Schema(required = true,description = "每页条数")
    private Integer pageSize;
    /**
     * 告警消息内容
     */
    @Schema(description = "告警消息内容")
    private String context;
    /**
     * 资源类型
     */
    @Schema(description = "资源类型")
    private String sourceType;
    /**
     * 告警级别
     */
    @Schema(description = "告警级别,选择多条逗号拼接")
    private Integer alarmLevel;
    /**
     * 地区名称
     */
    @Schema(description = "地区名称")
    private String regionName;
    /**
     * 地区ID
     */
    @Schema(description = "地区ID")
    private Long regionId;
    /**
     * 地区ID
     */
    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "资源名称")
    private String productsName;

    @Schema(description = "租户id")
    private List<String> tenantId;

    @Schema(description = "过去天数")
    private Integer recentlyDay;

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String startTime;

    @Schema(description = "结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String endTime;

    @Schema(required = false,defaultValue = "true",description = "启用分页")
    private boolean enablePagination;
}
