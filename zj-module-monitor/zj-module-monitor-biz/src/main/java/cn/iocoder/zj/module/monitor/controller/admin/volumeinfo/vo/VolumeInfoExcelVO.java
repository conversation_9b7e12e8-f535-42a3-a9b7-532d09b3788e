package cn.iocoder.zj.module.monitor.controller.admin.volumeinfo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * 云盘信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class VolumeInfoExcelVO {

    @ExcelProperty("主键ID")
    private Long id;

    @ExcelProperty("云盘uuid")
    private String uuid;

    @ExcelProperty("平台id")
    private String platformId;

    @ExcelProperty("云盘名称")
    private String name;

    @ExcelProperty("云盘详细描述")
    private String description;

    @ExcelProperty("主存储uuid")
    private String primaryStorageUuid;

    @ExcelProperty("云主机uuid")
    private String vminstanceUuid;

    @ExcelProperty("云盘规格uuid")
    private String diskofferingUuid;

    @ExcelProperty("云盘类型，数据云盘/根云盘")
    private String type;

    @ExcelProperty("云盘格式")
    private String format;

    @ExcelProperty("云盘大小(GB)")
    private BigDecimal size;

    @ExcelProperty("云盘真实大小(GB)")
    private BigDecimal actualSize;

    @ExcelProperty("云盘是否开启")
    private String state;

    @ExcelProperty("云盘状态")
    private String status;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private Date createTime;

    @ExcelProperty("云盘创建时间")
    @ColumnWidth(20)
    private Date vCreateDate;

    @ExcelProperty("云盘更新时间")
    @ColumnWidth(20)
    private Date vUpdateDate;

    // 最大iops max_iops
    @ExcelProperty("最大iops")
    private Long maxIops;

    //吞吐量
    @ExcelProperty("吞吐量")
    private BigDecimal throughput;

    //介质类型
    @ExcelProperty("介质类型")
    private String mediaType;

    @ExcelProperty("是否挂载")
    private Boolean isMount;

    public void setSize(Long size) {
        if (size != null) {
            this.size = BigDecimal.valueOf(size)
                    .divide(BigDecimal.valueOf(1024 * 1024 * 1024), 2, RoundingMode.HALF_UP);
        } else {
            this.size = BigDecimal.ZERO;
        }
    }

    public void setActualSize(Long actualSize) {
        if (actualSize != null) {
            this.actualSize = BigDecimal.valueOf(actualSize)
                    .divide(BigDecimal.valueOf(1024 * 1024 * 1024), 2, RoundingMode.HALF_UP);
        } else {
            this.actualSize = BigDecimal.ZERO;
        }
    }
}
