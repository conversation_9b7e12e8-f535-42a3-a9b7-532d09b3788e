package cn.iocoder.zj.module.monitor.controller.admin.imageinfo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;

/**
 * 镜像信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ImageInfoExcelVO {

    @ExcelProperty("主键ID")
    private Long id;

    @ExcelProperty("镜像名称")
    private String name;

    @ExcelProperty("状态")
    private String status;

    @ExcelProperty("镜像格式")
    private String format;

    @ExcelProperty("CPU架构")
    private String cpuArch;

    @ExcelProperty("操作系统")
    private String osType;

    @ExcelProperty("镜像大小(GB)")
    private BigDecimal size;

    @ExcelProperty("镜像类型")
    private String imageType;

    @ExcelProperty("共享范围")
    private String sharingScope;

    @ExcelProperty("镜像id")
    private String uuid;

//    @ExcelProperty("标签")
//    private String tag;

    @Schema(description = "云主机uuid")
    private String hostUuid;

    @ExcelProperty("镜像创建时间")
    private LocalDateTime vCreateDate;

    @ExcelProperty("镜像更新时间")
    private LocalDateTime vUpdateDate;

    @ExcelProperty("系统语言")
    private String osLanguage;

    @ExcelProperty("最小内存要求(MB)")
    private BigDecimal minMemory;

    @ExcelProperty("最小磁盘要求(GB)")
    private BigDecimal minDisk;

    @ExcelProperty("磁盘驱动")
    private String diskDriver;

    @ExcelProperty("网卡驱动")
    private String networkDriver;

    @ExcelProperty("引导方式")
    private String bootMode;

    @ExcelProperty("远程终端协议")
    private String remoteProtocol;

    @ExcelProperty("应用平台")
    private String applicationPlatform;

    @ExcelProperty("平台id")
    private Long platformId;

    @ExcelProperty("平台名称")
    private String platformName;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    public void setSize(Long size) {
        if (size != null) {
            this.size = BigDecimal.valueOf(size)
                    .divide(BigDecimal.valueOf(1024 * 1024 * 1024), 2, RoundingMode.HALF_UP);
        } else {
            this.size = BigDecimal.ZERO;
        }
    }

}
