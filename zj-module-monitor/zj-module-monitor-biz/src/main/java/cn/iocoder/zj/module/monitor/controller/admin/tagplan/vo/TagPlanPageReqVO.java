package cn.iocoder.zj.module.monitor.controller.admin.tagplan.vo;

import cn.iocoder.zj.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 巡检计划分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TagPlanPageReqVO extends PageParam {

    @Schema(description = "计划名称")
    private String name;

    @Schema(description = "巡检周期(day-日, week-周, month-月)")
    private String periodType;

    @Schema(description = "执行日(日-不需要填;周-(1-7);月-(1-31))")
    private String executionDay;

    @Schema(description = "执行时间点")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] executionTime;

    @Schema(description = "Cron表达式")
    private String executionCron;

    @Schema(description = "巡检项(多个以逗号分隔)")
    private String patrolItem;

    @Schema(description = "状态(0-未启用, 1-启用)")
    private Byte status;

    @Schema(description = "上次执行时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] lastExecutionTime;

    @Schema(description = "下次执行时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] nextExecutionTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "租户编号")
    private Long tenantId;

    @Schema(description = "平台id")
    private String platformId;

    @Schema(description = "job编号")
    private Long jobId;

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date startTime;

    @Schema(description = "结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date endTime;

}
