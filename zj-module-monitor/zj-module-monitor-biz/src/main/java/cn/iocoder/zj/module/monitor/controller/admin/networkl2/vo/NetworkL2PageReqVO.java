package cn.iocoder.zj.module.monitor.controller.admin.networkl2.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 二级网络信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NetworkL2PageReqVO extends PageParam {

    @Schema(description = "二级网络名称")
    private String name;

    @Schema(description = "二级网络uuid")
    private String uuid;

    @Schema(description = "网卡")
    private String physicalInterface;

    @Schema(description = "二级网络类型")
    private String type;

    @Schema(description = "vlan")
    private String vlan;

    @Schema(description = "虚拟网络标识")
    private Integer virtualNetworkId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] createTime;

    @Schema(description = "地区名称")
    private String regionName;

    @Schema(description = "地区id")
    private Long regionId;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;


    @Schema(description = "租户编号")
    private Long tenantId;

    @Schema(required = false,description = "主机过滤ids逗号拼接,'1','2','3'")
    private String ids;
    @Schema(required = false,description = "主键逗号拼接,'1','2','3'")
    private List<Long> inPks;

    private String startTime;

    private String endTime;

}
