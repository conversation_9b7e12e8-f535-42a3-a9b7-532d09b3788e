package cn.iocoder.zj.module.monitor.api.imageinfo;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.api.imageinfo.dto.ImageInfoCreateReqDTO;
import cn.iocoder.zj.module.monitor.controller.admin.imageinfo.vo.ImageInfoCreateReqVO;
import cn.iocoder.zj.module.monitor.convert.imageinfo.ImageInfoConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.imageinfo.ImageInfoDO;
import cn.iocoder.zj.module.monitor.service.imageinfo.ImageInfoService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class ImageInfoApiImpl implements ImageInfoApi{

    @Resource
    private ImageInfoService imageInfoService;

    @Override
    @TenantIgnore
    public CommonResult<Boolean> batchCreateImageInfo(List<ImageInfoCreateReqDTO> createReqDTOs) {
        if (!createReqDTOs.isEmpty()) {
            List<ImageInfoCreateReqVO> imageInfoCreateReqVOs = BeanUtil.copyToList(createReqDTOs, ImageInfoCreateReqVO.class);
            imageInfoService.batchCreateImageInfo(imageInfoCreateReqVOs);
        }
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> batchUpdateImageInfo(List<ImageInfoCreateReqDTO> updateReqDTOs) {
        if (!updateReqDTOs.isEmpty()) {
            List<ImageInfoCreateReqVO> imageInfoUpdateReqVOs = BeanUtil.copyToList(updateReqDTOs, ImageInfoCreateReqVO.class);
            imageInfoService.batchUpdateImageInfo(imageInfoUpdateReqVOs);
        }
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> batchDeleteImageInfo(List<ImageInfoCreateReqDTO> deleteReqDTOs) {
        if (!deleteReqDTOs.isEmpty()) {
            List<ImageInfoCreateReqVO> imageInfoUpdateReqVOs = ImageInfoConvert.INSTANCE.convertCreateListReq(deleteReqDTOs);
            imageInfoService.batchDeleteImageInfo(imageInfoUpdateReqVOs);
        }
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<List<ImageInfoCreateReqDTO>> getImageInfoByPlatformId(Long platformId) {
        List<ImageInfoDO> imageInfoDOList = imageInfoService.getImageInfosByPlatformId(platformId);
        List<ImageInfoCreateReqDTO> imageInfoCreateReqDTOS = BeanUtil.copyToList(imageInfoDOList, ImageInfoCreateReqDTO.class);
        return CommonResult.success(imageInfoCreateReqDTOS);
    }

    @Override
    public CommonResult<List<ImageInfoCreateReqDTO>> getAllImagesByTypeCode(String typeCode) {
        List<ImageInfoDO> imageInfoDOList = imageInfoService.getAllImagesByTypeCode(typeCode);
        List<ImageInfoCreateReqDTO> imageInfoCreateReqDTOS = ImageInfoConvert.INSTANCE.convertListReqDTO(imageInfoDOList);
        return CommonResult.success(imageInfoCreateReqDTOS);
    }
}
