package cn.iocoder.zj.module.monitor.taskTime.annotation;

import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.zj.module.monitor.taskTime.cache.TaskCacheModel;
import cn.iocoder.zj.module.monitor.taskTime.task.TaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 任务处理器
 */
@Slf4j
@Component
public class HSYJobProcessor implements BeanPostProcessor {

    private final ApplicationContext applicationContext;

    // 存储所有任务的缓存
    public static final ConcurrentMap<String, TaskCacheModel> TASK_CACHE = new ConcurrentHashMap<>();

    // 存储通用处理方法的信息
    private static Object commonTaskBean;
    private static Method commonTaskMethod;
    private static final ThreadLocal<String> CURRENT_TASK_NAME = new ThreadLocal<>();

    public HSYJobProcessor(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        Method[] methods = bean.getClass().getDeclaredMethods();
        for (Method method : methods) {
            if (method.isAnnotationPresent(HSYJob.class)) {
                HSYJob yqJob = method.getAnnotation(HSYJob.class);
                String jobName = yqJob.value();
                log.info("任务发现: {}", jobName);

                // 保存通用处理方法的信息
                if (commonTaskBean == null) {
                    commonTaskBean = bean;
                    commonTaskMethod = method;
                }

                // 注册任务
                registerTask(jobName, bean, method, yqJob.cronExpression());
            }
        }
        return bean;
    }

    /**
     * 注册任务
     */
    private void registerTask(String jobName, Object bean, Method method, String cronExpression) {
        TASK_CACHE.put(jobName, new TaskCacheModel(jobName, bean, method, 0, () -> {
            try {
                CURRENT_TASK_NAME.set(jobName);
                method.invoke(applicationContext.getBean(bean.getClass()));
            } catch (Exception e) {
                log.error("执行任务时发生错误 {} {}", jobName, ExceptionUtils.getStackTrace(e));
            } finally {
                // 清除ThreadLocal
                CURRENT_TASK_NAME.remove();
            }
        }, cronExpression));
    }

    /**
     * 动态注册任务
     */
    public boolean registerDynamicTask(String taskName, String cronExpression) {
        try {
            // 直接使用 lambda 简化
            TaskCacheModel taskModel = new TaskCacheModel(
                    taskName,
                    SpringUtil.getBean(TaskService.class),
                    TaskService.class.getMethod("handleTask", String.class),
                    0,
                    null,
                    cronExpression
            );
            TASK_CACHE.put(taskName, taskModel);
            log.info("动态任务注册成功: {}", taskName);
            return true;
        } catch (Exception e) {
            log.error("注册动态任务失败: {}", taskName, e);
            return false;
        }
    }

    /**
     * 获取当前正在执行的任务名称
     */
    public static String getCurrentTaskName() {
        return CURRENT_TASK_NAME.get();
    }

    /**
     * 更新任务的cron表达式
     */
    public boolean updateTaskCron(String jobName, String cronExpression) {
        TaskCacheModel model = TASK_CACHE.get(jobName);
        if (model != null) {
            model.setCronExpression(cronExpression);
            return true;
        }
        return false;
    }

    /**
     * 移除任务
     */
    public boolean removeTask(String jobName) {
        return TASK_CACHE.remove(jobName) != null;
    }
}