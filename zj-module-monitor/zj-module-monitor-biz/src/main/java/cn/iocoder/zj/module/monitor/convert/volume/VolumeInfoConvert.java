package cn.iocoder.zj.module.monitor.convert.volume;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeDTO;
import cn.iocoder.zj.module.monitor.controller.admin.volumeinfo.vo.VolumeInfoCreateReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.volumeinfo.vo.VolumeInfoExcelVO;
import cn.iocoder.zj.module.monitor.controller.admin.volumeinfo.vo.VolumeInfoRespVO;
import cn.iocoder.zj.module.monitor.controller.admin.volumeinfo.vo.VolumeInfoUpdateReqVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.volumeinfo.VolumeInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 云盘信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface VolumeInfoConvert {

    VolumeInfoConvert INSTANCE = Mappers.getMapper(VolumeInfoConvert.class);

    List<VolumeInfoDO> DTOConvertToDO(List<VolumeDTO> reqDTO);

    List<VolumeDTO> DOConvertToDTO(List<VolumeInfoDO> volumeInfoDOList);

    VolumeInfoRespVO convert(VolumeInfoDO volumeInfo);

    VolumeInfoDO convert(VolumeInfoCreateReqVO createReqVO);

    VolumeInfoDO convert(VolumeInfoUpdateReqVO updateReqVO);

    List<VolumeInfoRespVO> convertList(List<VolumeInfoDO> list);

    PageResult<VolumeInfoRespVO> convertPage(PageResult<VolumeInfoDO> pageResult);

    List<VolumeInfoExcelVO> convertList02(List<VolumeInfoDO> list);
}
