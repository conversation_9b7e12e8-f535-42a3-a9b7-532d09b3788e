package cn.iocoder.zj.module.monitor.dal.dataobject.topreport;

import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("top_report_asset")
public class TopReportAssetDO extends BaseDO {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * TOP报表ID
     */
    private Long reportId;

    /**
     * 资产IP
     */
    private String assetId;

    private String assetName;

    private Long platformId;

    private String platformName;
}
