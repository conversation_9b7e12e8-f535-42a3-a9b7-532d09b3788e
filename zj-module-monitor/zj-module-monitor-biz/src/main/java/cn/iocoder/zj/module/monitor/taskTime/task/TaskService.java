package cn.iocoder.zj.module.monitor.taskTime.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.TagData;
import cn.iocoder.zj.module.monitor.dal.dataobject.scanip.IPRangeDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.taggables.TaggablesDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.tags.TagsDO;
import cn.iocoder.zj.module.monitor.dal.mysql.hardwareinfo.HardwareInfoMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.hostinfo.HostInfoMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.scanip.IPRangeMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.taggables.TaggablesMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.tags.TagsMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.volumeinfo.VolumeInfoMapper;
import cn.iocoder.zj.module.monitor.framework.redisson.RedisLockClient;
import cn.iocoder.zj.module.monitor.service.scanip.IPRangeService;
import cn.iocoder.zj.module.monitor.taskTime.annotation.HSYJobProcessor;
import cn.iocoder.zj.module.monitor.taskTime.cache.TaggableDO;
import cn.iocoder.zj.module.monitor.taskTime.config.TaskConfigManager;
import cn.iocoder.zj.module.monitor.taskTime.service.ScheduleTaskService;
import cn.iocoder.zj.module.monitor.util.DealTagUtil;
import cn.iocoder.zj.module.monitor.util.TagDataDiffUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
@Order(1)
public class TaskService {
    private final TaskConfigManager taskConfigManager;
    private final HSYJobProcessor hsyJobProcessor;
    private final ScheduleTaskService scheduleTaskService;
    private static final Map<String, Set<Long>> lastEmptyTagMap = new ConcurrentHashMap<>();

    @Resource
    private IPRangeMapper ipRangeMapper;

    @Resource
    private IPRangeService ipRangeService;

    @Resource
    private RedisLockClient redisLockClient;

    @Resource
    private HostInfoMapper hostInfoMapper;

    @Resource
    private VolumeInfoMapper volumeInfoMapper;

    @Resource
    private HardwareInfoMapper hardwareInfoMapper;

    @Resource
    private TagsMapper tagsMapper;

    @Resource
    private TaggablesMapper taggablesMapper;

    @EventListener(ApplicationReadyEvent.class)
    public void init() {
        String lockKey = "task_service:init:lock";
        boolean locked = false;
        try {
            locked = redisLockClient.tryLock(lockKey, TimeUnit.SECONDS, 5, -1);
            if (!locked) {
                log.info("其他实例已获取锁，本实例将不执行任务初始化");
                return;
            }
            log.info("获取初始化锁成功，本实例将负责任务调度");

            scheduleTaskService.clearAllTasks();
            taskConfigManager.loadAndRegisterTasks(hsyJobProcessor);
            Optional.ofNullable(scheduleTaskService.listTasks())
                    .filter(tasks -> !tasks.isEmpty())
                    .ifPresent(tasks -> {
                        tasks.forEach(task -> {
                            log.info("正在启动任务: {}, cron表达式: {}",
                                    task.getJobName(), task.getCronExpression());
                            scheduleTaskService.updateTask(task.getJobName(),task.getCronExpression());
                        });
                        log.info("所有任务启动完成，共 {} 个任务", tasks.size());
                    });
        } catch (Exception e) {
            log.error("初始化任务失败", e);
            if (locked) {
                redisLockClient.unlock(lockKey);
                log.info("发生异常，释放初始化锁");
            }
        }
    }

    public void handleTask(String taskName) {
        IPRangeDO rangeDO = ipRangeMapper.selectOne("task_id", taskName);
        if(rangeDO.getStatus() == 0 || rangeDO.getState() == 1){
            return;
        }

        if(rangeDO.getPingSupport() == 0 && rangeDO.getTcpSupport() == 0 && rangeDO.getSnmpSupport() == 0){
            return;
        }
        log.info("{}  任务触发", taskName);
        ipRangeService.execute(rangeDO.getId());
    }

    @Scheduled(cron = "0 */5 * * * ?")
    public void runTagsTask() {
        String lockKey = "task_service:tag:lock";
        boolean locked = false;
        try {
            locked = redisLockClient.tryLock(lockKey, TimeUnit.SECONDS, 5, 300);
            if (!locked) {
                log.info("tag - 其他实例已获取锁，本实例将不执行任务");
                return;
            }
            log.info("获取锁成功，本实例将负责任务调度");

            Map<String, List<TaggableDO>> tagDataMap = Map.of(
                    "hardware", hardwareInfoMapper.getListByTag(),
                    "host", hostInfoMapper.getListByTag(),
                    "disk", volumeInfoMapper.getListByTag()
            );

            // 打标签任务执行
            tagDataMap.forEach((type, dataList) -> {
                Map<Long, List<TagData>> tagMap = collectTagData(dataList, type);
                tagMap.forEach((uuid, tags) -> tagTaskRun(tags));
            });

            // 清理空标签逻辑
            tagDataMap.forEach((type, dataList) -> {
                Set<Long> emptyTagIds = dataList.stream()
                        .filter(item -> StrUtil.isEmpty(item.getTag()))
                        .map(TaggableDO::getId)
                        .collect(Collectors.toSet());

                Set<Long> lastTagIds = lastEmptyTagMap.get(type);
                if (!emptyTagIds.equals(lastTagIds)) {
                    lastEmptyTagMap.put(type, emptyTagIds);
                    taggablesMapper.updateByIdDetel(null,emptyTagIds, type);
                }
            });
        } catch (Exception e) {
            redisLockClient.unlock(lockKey);
            log.error("tag 任务调度执行失败", e);
        }
    }

    private <T> Map<Long, List<TagData>> collectTagData(List<T> list, String type) {
        List<TagData> tagList = DealTagUtil.dealData(list, type);
        return tagList.stream().collect(Collectors.groupingBy(TagData::getPlatformId));
    }


    public void tagTaskRun(List<TagData> result) {
        // 获取平台对应的租户信息并构建映射
        List<TagData> platformTenants = tagsMapper.getTenantByPlatform();
        Map<Long, Long> tenantMap = platformTenants.stream().collect(Collectors.groupingBy(TagData::getPlatformId)).entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> determineTenantId(e.getValue())));

        Long tenantId = tenantMap.get(result.get(0).getPlatformId());
        String taggableType = result.get(0).getType();

        // 查询所有标签
        TagsDO baseQuery = new TagsDO();
        baseQuery.setType(1);
        baseQuery.setTenantId(tenantId);
        List<TagsDO> allTags = tagsMapper.getAllList(baseQuery);

        // 首次插入处理
        if (CollUtil.isEmpty(allTags)) {
            result.stream()
                    .collect(Collectors.groupingBy(TagData::getTagUuid))
                    .forEach((uuid, list) -> {
                        TagsDO tag = createTagsDO(list.get(0), tenantMap);
                        tag.setTotalCount(Convert.toLong(list.size()));
                        tagsMapper.insert(tag);

                        List<TaggablesDO> toInsert = list.stream()
                                .map(data -> createTaggablesDO(tag, data))
                                .collect(Collectors.toList());
                        taggablesMapper.insertBatch(toInsert);
                    });
            return;
        }
        // 计算差异
        TagDataDiffUtil.TagDataDiffResult diff = TagDataDiffUtil.compare(result, allTags);
        Map<String, List<TagsDO>> allMap = allTags.stream().collect(Collectors.groupingBy(TagsDO::getTagUuid));

        // 处理新增
        List<TaggablesDO> toInsert = new ArrayList<>();
        diff.getAdded().forEach((uuid, list) -> {
            TagsDO tag = createTagsDO(list.get(0), tenantMap);
            tag.setTotalCount(Convert.toLong(list.size()));

            if (allMap.containsKey(uuid)) {
                tag.setId(allMap.get(uuid).get(0).getId());
            } else {
                tagsMapper.insert(tag);
            }

            list.forEach(data -> toInsert.add(createTaggablesDO(tag, data)));
        });

        if (CollUtil.isNotEmpty(toInsert)) {
            taggablesMapper.insertBatch(toInsert);
        }

        // 处理删除
        Set<String> deletedUuids = diff.getDeleted().keySet();
        if (!deletedUuids.isEmpty()) {
            TagsDO delQuery = new TagsDO();
            delQuery.setType(1);
            delQuery.setTagUuids(new ArrayList<>(deletedUuids));

            List<Long> delIds = tagsMapper.getAllList(delQuery).stream().map(TagsDO::getId).collect(Collectors.toList());

            if (CollUtil.isNotEmpty(delIds)) {
                taggablesMapper.updateByDetel(new HashSet<>(delIds), taggableType);
            }
        }

        // 处理公共部分更新
        if (!diff.getCommon().isEmpty()) {
            TagsDO comQuery = new TagsDO();
            comQuery.setType(1);
            comQuery.setTaggableType(taggableType);
            comQuery.setTagUuids(new ArrayList<>(diff.getCommon().keySet()));
            comQuery.setPlatformId(result.get(0).getPlatformId());
            List<TagsDO> commonTags = tagsMapper.getAllList(comQuery);

            if (CollUtil.isNotEmpty(commonTags)) {
                // 获取现有标签映射
                Set<Long> tagIds = commonTags.stream().map(TagsDO::getId).collect(Collectors.toSet());
                Map<String, List<TaggablesDO>> taggableMap = taggablesMapper.selectListByType(tagIds, taggableType).stream().collect(Collectors.groupingBy(TaggablesDO::getTagUuid));

                // 按UUID分组标签
                Map<String, List<TagsDO>> commonTagMap = commonTags.stream().collect(Collectors.groupingBy(TagsDO::getTagUuid));

                // 处理每个公共标签
                diff.getCommon().forEach((uuid, list) -> {
                    List<TaggablesDO> existing = taggableMap.get(uuid);
                    Map<String, List<TagData>> status = checkUpdateFlag(list, existing);

                    if (status.containsKey("size_mismatch")) {
                        handleDiff(list, existing, commonTagMap);
                    } else if (status.containsKey("content_mismatch")) {
                        tagsMapper.updateByTagName(status.get("content_mismatch"));
                    }
                });
            }
        }
    }
    private Long determineTenantId(List<TagData> tagDataList) {
        if (tagDataList.size() == 1) {
            return tagDataList.get(0).getTenantId();
        }

        return tagDataList.stream()
                .filter(tag -> !tag.getTenantId().equals(1L))
                .map(TagData::getTenantId)
                .findFirst()
                .orElse(1L); // 改为返回1L而不是null，避免空指针
    }

    private TagsDO createTagsDO(TagData tagData, Map<Long, Long> platformToTenantMap) {
        TagsDO tagsDO = new TagsDO();
        tagsDO.setName(tagData.getTagName());
        tagsDO.setType(1);
        tagsDO.setTagName(tagData.getTagName());
        tagsDO.setTagUuid(tagData.getTagUuid());
        tagsDO.setPlatformId(tagData.getPlatformId());
        tagsDO.setTenantId(platformToTenantMap.getOrDefault(tagData.getPlatformId(), 1L));
        tagsDO.setIsUpdate(0);
        return tagsDO;
    }

    private TaggablesDO createTaggablesDO(TagsDO tag, TagData tagData) {
        TaggablesDO taggablesDO = new TaggablesDO();
        taggablesDO.setTagId(tag.getId());
        taggablesDO.setTaggableId(tagData.getId());
        taggablesDO.setTaggableType(tagData.getType());
        taggablesDO.setTenantId(tag.getTenantId());
        taggablesDO.setType(1);
        return taggablesDO;
    }

    public static Map<String, List<TagData>> checkUpdateFlag(List<TagData> tagList, List<TaggablesDO> existingTaggables) {
        Map<String, List<TagData>> result = new HashMap<>();

        if (CollUtil.isEmpty(existingTaggables) || tagList.size() != existingTaggables.size()) {
            result.put("size_mismatch", tagList);
            return result;
        }

        Set<String> idKeys = existingTaggables.stream().map(tag -> tag.getTagUuid() + "-" + tag.getTaggableId()).collect(Collectors.toSet());
        List<TagData> unmatchedIdList = tagList.stream().filter(tag -> !idKeys.contains(tag.getTagUuid() + "-" + tag.getId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(unmatchedIdList)) {
            result.put("size_mismatch", unmatchedIdList);
            return result;
        }

        Set<String> fullKeys = existingTaggables.stream().map(tag -> tag.getTagUuid() + "-" + tag.getTaggableId() + "-" + tag.getTagName()).collect(Collectors.toSet());
        List<TagData> unmatchedContentList = tagList.stream().filter(tag -> !fullKeys.contains(tag.getTagUuid() + "-" + tag.getId() + "-" + tag.getTagName())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(unmatchedContentList)) {
            result.put("content_mismatch", unmatchedContentList);
            return result;
        }

        return result;
    }

    public void handleDiff(List<TagData> tagList, List<TaggablesDO> taggables, Map<String, List<TagsDO>> comMap) {
        if (taggables != null && !taggables.isEmpty()) {
            Set<Long> longs = taggables.stream().map(TaggablesDO::getId).collect(Collectors.toSet());
            taggablesMapper.updateByIdDetel(longs,null ,tagList.get(0).getType());
        }

        List<TaggablesDO> list = new ArrayList<>();
        for (TagData tagData : tagList) {
            List<TagsDO> tagsDOS = comMap.get(tagData.getTagUuid());
            if (tagsDOS != null && !tagsDOS.isEmpty()) {
                TaggablesDO tag = createTaggablesDO(tagsDOS.get(0), tagData);
                list.add(tag);
            }
        }

        if(CollUtil.isNotEmpty(list)){
            taggablesMapper.insertBatch(list);
        }
    }
}