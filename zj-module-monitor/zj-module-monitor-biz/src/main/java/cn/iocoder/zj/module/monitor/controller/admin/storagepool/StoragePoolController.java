package cn.iocoder.zj.module.monitor.controller.admin.storagepool;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.monitor.controller.admin.storagepool.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.storagepool.StoragePoolDO;
import cn.iocoder.zj.module.monitor.convert.storagepool.StoragePoolConvert;
import cn.iocoder.zj.module.monitor.service.storagepool.StoragePoolService;

@Tag(name = "管理后台 - 存储池")
@RestController
@RequestMapping("/monitor/storage-pool")
@Validated
public class StoragePoolController {

    @Resource
    private StoragePoolService storagePoolService;

    @PostMapping("/create")
    @Operation(summary = "创建存储池")
    //@PreAuthorize("@ss.hasPermission('monitor:storage-pool:create')")
    public CommonResult<Long> createStoragePool(@Valid @RequestBody StoragePoolCreateReqVO createReqVO) {
        return success(storagePoolService.createStoragePool(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新存储池")
    //@PreAuthorize("@ss.hasPermission('monitor:storage-pool:update')")
    public CommonResult<Boolean> updateStoragePool(@Valid @RequestBody StoragePoolUpdateReqVO updateReqVO) {
        storagePoolService.updateStoragePool(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除存储池")
    @Parameter(name = "id", description = "编号", required = true)
    //@PreAuthorize("@ss.hasPermission('monitor:storage-pool:delete')")
    public CommonResult<Boolean> deleteStoragePool(@RequestParam("id") Long id) {
        storagePoolService.deleteStoragePool(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得存储池")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('monitor:storage-pool:query')")
    public CommonResult<StoragePoolRespVO> getStoragePool(@RequestParam("id") Long id) {
        StoragePoolDO storagePool = storagePoolService.getStoragePool(id);
        return success(StoragePoolConvert.INSTANCE.convert(storagePool));
    }

    @GetMapping("/list")
    @Operation(summary = "获得存储池列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    //@PreAuthorize("@ss.hasPermission('monitor:storage-pool:query')")
    public CommonResult<List<StoragePoolRespVO>> getStoragePoolList(@RequestParam("ids") Collection<Long> ids) {
        List<StoragePoolDO> list = storagePoolService.getStoragePoolList(ids);
        return success(StoragePoolConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得存储池分页")
    //@PreAuthorize("@ss.hasPermission('monitor:storage-pool:query')")
    public CommonResult<PageResult<StoragePoolRespVO>> getStoragePoolPage(@Valid StoragePoolPageReqVO pageVO) {
        PageResult<StoragePoolDO> pageResult = storagePoolService.getStoragePoolPage(pageVO);
        return success(StoragePoolConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出存储池 Excel")
    //@PreAuthorize("@ss.hasPermission('monitor:storage-pool:export')")
    @OperateLog(type = EXPORT)
    public void exportStoragePoolExcel(@Valid StoragePoolExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<StoragePoolDO> list = storagePoolService.getStoragePoolList(exportReqVO);
        // 导出 Excel
        List<StoragePoolExcelVO> datas = StoragePoolConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "存储池.xls", "数据", StoragePoolExcelVO.class, datas);
    }

}
