package cn.iocoder.zj.module.monitor.service.bulletin;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.cloud.framework.warehouse.service.MetricsDataService;
import cn.iocoder.cloud.module.cloudedge.api.monitor.MonitorApi;
import cn.iocoder.cloud.module.cloudedge.api.monitor.dto.MonitorHierarchy;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.*;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.vo.BulletinCreateReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.vo.BulletinExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.vo.BulletinPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.vo.BulletinUpdateReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.hardwareinfo.vo.HardwareInfoPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.hardwareinfo.vo.HardwareInfoRespVO;
import cn.iocoder.zj.module.monitor.controller.admin.hostinfo.vo.HostInfoPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.hostinfo.vo.HostInfoRespVO;
import cn.iocoder.zj.module.monitor.controller.admin.imageinfo.vo.ImageInfoPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.imageinfo.vo.ImageInfoRespVO;
import cn.iocoder.zj.module.monitor.controller.admin.networkl2.vo.NetworkL2PageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.networkl2.vo.NetworkL2RespVO;
import cn.iocoder.zj.module.monitor.controller.admin.networkl3.vo.NetworkL3PageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.networkl3.vo.NetworkL3RespVO;
import cn.iocoder.zj.module.monitor.controller.admin.networkvpc.vo.NetworkVpcPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.networkvpc.vo.NetworkVpcRespVO;
import cn.iocoder.zj.module.monitor.controller.admin.secgroup.vo.SecgroupPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.secgroup.vo.SecgroupRespVO;
import cn.iocoder.zj.module.monitor.controller.admin.storageinfo.vo.StorageInfoPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.storageinfo.vo.StorageInfoRespVO;
import cn.iocoder.zj.module.monitor.controller.admin.taggables.vo.TaggablesExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.tags.vo.TagsRespVO;
import cn.iocoder.zj.module.monitor.controller.admin.volumeinfo.vo.VolumeInfoPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.volumeinfo.vo.VolumeInfoRespVO;
import cn.iocoder.zj.module.monitor.controller.admin.volumeinfo.vo.VolumeInfoUseReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.volumesnapshot.vo.VolumeSnapshotPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.volumesnapshot.vo.VolumeSnapshotRespVO;
import cn.iocoder.zj.module.monitor.convert.bulletin.BulletinConvert;
import cn.iocoder.zj.module.monitor.convert.hostinfo.HostInfoConvert;
import cn.iocoder.zj.module.monitor.convert.imageinfo.ImageInfoConvert;
import cn.iocoder.zj.module.monitor.convert.networkl2.NetworkL2Convert;
import cn.iocoder.zj.module.monitor.convert.networkl3.NetworkL3Convert;
import cn.iocoder.zj.module.monitor.convert.networkvpc.NetworkVpcConvert;
import cn.iocoder.zj.module.monitor.convert.secgroup.SecgroupConvert;
import cn.iocoder.zj.module.monitor.convert.storageinfo.StorageInfoConvert;
import cn.iocoder.zj.module.monitor.convert.tags.TagsConvert;
import cn.iocoder.zj.module.monitor.convert.volume.VolumeInfoConvert;
import cn.iocoder.zj.module.monitor.convert.volumesnapshot.VolumeSnapshotConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.bulletin.BulletinDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.hardwareinfo.HardwareInfoDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostinfo.HostInfoDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.imageinfo.ImageInfoDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkl2.NetworkL2DO;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkl3.NetworkL3DO;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkvpc.NetworkVpcDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.secgroup.SecgroupDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.storageinfo.StorageInfoDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.tags.TagsDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.volumeinfo.VolumeInfoDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.volumesnapshot.VolumeSnapshotDO;
import cn.iocoder.zj.module.monitor.dal.mysql.bulletin.BulletinMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.hardwareinfo.HardwareInfoMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.hostinfo.HostInfoMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.storageinfo.StorageInfoMapper;
import cn.iocoder.zj.module.monitor.service.bulletin.dto.BulletinFieldItemDto;
import cn.iocoder.zj.module.monitor.service.bulletin.enums.BulletinCloudMonitorItemEnum;
import cn.iocoder.zj.module.monitor.service.hardwareinfo.HardwareInfoServiceImpl;
import cn.iocoder.zj.module.monitor.service.hardwarestorage.HardwareStorageService;
import cn.iocoder.zj.module.monitor.service.hostinfo.HostInfoServiceImpl;
import cn.iocoder.zj.module.monitor.service.imageinfo.ImageInfoService;
import cn.iocoder.zj.module.monitor.service.networkl2.NetworkL2Service;
import cn.iocoder.zj.module.monitor.service.networkl3.NetworkL3Service;
import cn.iocoder.zj.module.monitor.service.networkvpc.NetworkVpcService;
import cn.iocoder.zj.module.monitor.service.secgroup.SecgroupService;
import cn.iocoder.zj.module.monitor.service.storageinfo.StorageInfoService;
import cn.iocoder.zj.module.monitor.service.taggables.enums.TagAssetTypeEnum;
import cn.iocoder.zj.module.monitor.service.tags.TagsService;
import cn.iocoder.zj.module.monitor.service.volumeinfo.VolumeInfoService;
import cn.iocoder.zj.module.monitor.service.volumesnapshot.VolumeSnapshotService;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.merge.OnceAbsoluteMergeStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.apache.hertzbeat.common.entity.dto.MetricsData;
import org.apache.hertzbeat.common.entity.dto.ValueRow;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.BULLETIN_NOT_EXISTS;

/**
 * 实时报 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BulletinServiceImpl implements BulletinService {

    @Resource
    private BulletinMapper bulletinMapper;

    @Resource
    MonitorApi monitorApi;
    @Resource
    HardwareInfoServiceImpl hardwareInfoService;
    @Resource
    HardwareInfoMapper hardwareInfoMapper;
    @Resource
    HostInfoServiceImpl hostInfoService;
    @Resource
    HostInfoMapper hostInfoMapper;
    @Resource
    private ImageInfoService imageInfoService;

    @Resource
    SecgroupService secgroupService;
    @Resource
    VolumeInfoService volumeInfoService;
    @Resource
    private StorageInfoService storageInfoService;
    @Resource
    StorageInfoMapper storageInfoMapper;
    @Resource
    private HardwareStorageService hardwareStorageService;
    @Resource
    private VolumeSnapshotService volumeSnapshotService;
    @Resource
    private NetworkL2Service networkL2Service;
    @Resource
    private NetworkL3Service networkL3Service;
    @Resource
    private NetworkVpcService networkVpcService;
    @Resource
    MetricsDataService metricsDataService;

    @Resource
    TagsService tagsService;

    @Override
    public Long createBulletin(BulletinCreateReqVO createReqVO) {
        // 插入
        BulletinDO bulletin = BulletinConvert.INSTANCE.convert(createReqVO);
        bulletinMapper.insert(bulletin);
        // 返回
        return bulletin.getId();
    }

    @Override
    public void updateBulletin(BulletinUpdateReqVO updateReqVO) {
        // 校验存在
        validateBulletinExists(updateReqVO.getId());
        // 更新
        BulletinDO updateObj = BulletinConvert.INSTANCE.convert(updateReqVO);
        bulletinMapper.updateById(updateObj);
    }

    @Override
    public void deleteBulletin(Long id) {
        // 校验存在
        validateBulletinExists(id);
        // 删除
        bulletinMapper.deleteById(id);
    }

    private void validateBulletinExists(Long id) {
        if (bulletinMapper.selectById(id) == null) {
            throw exception(BULLETIN_NOT_EXISTS);
        }
    }

    @Override
    public BulletinDO getBulletin(Long id) {
        return bulletinMapper.selectById(id);
    }

    @Override
    public List<BulletinDO> getBulletinList(Collection<Long> ids) {
        return bulletinMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<BulletinDO> getBulletinPage(BulletinPageReqVO pageReqVO) {
        return bulletinMapper.selectPage(pageReqVO);
    }

    @Override
    public List<BulletinDO> getBulletinList(BulletinExportReqVO exportReqVO) {
        return bulletinMapper.selectList(exportReqVO);
    }

    @Override
    public List<Map<String, Object>> getAppHierarchy() {
        return monitorApi.getAppHierarchy();
    }

    @Override
    @TenantIgnore
    public List<Object> getDetailList(BulletinDO info) {
        List<String> edgeCategory = Arrays.stream(BulletinCloudMonitorItemEnum.values()).map(BulletinCloudMonitorItemEnum::getApp).toList();
        List<Long> assetsIds = JsonUtils.parseArray(info.getAssetIds(), Long.class);
        List<Object> res = new ArrayList<>();
        if (edgeCategory.contains(info.getApp())) { // 私有云
            for (Long assetsId : assetsIds) {
                Object t = getPrivateMonitorList(assetsId, info.getApp());
                if (t != null) {
                    res.add(t);
                }
            }
        } else {//hzb 赫兹 监控
            for (Long assetsId : assetsIds) {
                List<String> metrics = monitorApi.getAppDefine(info.getApp());
                Map<String, Object> hzTmpList = new HashMap<>();
                int maxCol = 0;
                for (String metric : metrics) {
                    MetricsData tmp = metricsDataService.getMetricsData(assetsId, metric);
                    if (tmp == null) {
                        continue;
                    }
                    List<Map<String, Object>> tmpMetricList = new ArrayList<>();
                    for (ValueRow valueRow : tmp.getValueRows()) {
                        Map<String, Object> tmpItem = new HashMap<>();
                        for (int i = 0; i < valueRow.getValues().size(); i++) {
                            tmpItem.put(tmp.getFields().get(i).getName(), valueRow.getValues().get(i).getOrigin());
                        }
                        tmpMetricList.add(tmpItem);
                    }
                    hzTmpList.put(metric, tmpMetricList);
                }
                Map<String, Object> hzbInfo = bulletinMapper.selectHzbById(assetsId);
                if (hzbInfo != null) {
                    hzTmpList.put("name", hzbInfo.getOrDefault("name", ""));
                    hzTmpList.put("host", hzbInfo.getOrDefault("host", ""));
                }
                if (!hzTmpList.isEmpty()) {
//                    res.add(hzTmpList);
                    List<Map<String, Object>> flatRows = flattenRes(hzTmpList);
                    res.addAll(flatRows);
                }
            }
        }
        return res;
    }

    public static List<Map<String, Object>> flattenRes(Map<String, Object> res) {
        List<Map<String, Object>> result = new ArrayList<>();

        // 获取所有 key（排除 name）
        List<String> metrics = res.keySet().stream()
                .filter(key -> !"name".equals(key) && !"host".equals(key))
                .toList();

        // 找到最大的数组长度
        int maxLength = metrics.stream()
                .mapToInt(key -> ((List<?>) res.get(key)).size())
                .max()
                .orElse(0);
        if (maxLength == 0) {
            Map<String, Object> row = new HashMap<>();
            row.put("name", res.getOrDefault("name", ""));
            row.put("host", res.getOrDefault("host", ""));
            result.add(row);
            return result;
        }
        // 对于每一行进行构造
        for (int i = 0; i < maxLength; i++) {
            Map<String, Object> row = new HashMap<>();
            if (res.containsKey("name") && i == 0) {
                row.put("name", res.get("name"));
            } else {
                row.put("name", "");
            }
            if (res.containsKey("host") && i == 0) {
                row.put("host", res.get("host"));
            } else {
                row.put("host", "");
            }

            for (String metric : metrics) {
                List<?> list = (List<?>) res.get(metric);
                if (i < list.size()) {
                    Object subObj = list.get(i);
                    if (subObj instanceof Map) {
                        ((Map<?, ?>) subObj).forEach((k, v) -> {
                            row.put(metric + "." + k, v);
//                            row.put((String) k, v);
                        });
                    }
                } else {
                    // 补 null 或者留空
                    // 可根据需要改为 put(metric + ".xxx", null)
                    if (!list.isEmpty()) {
                        Object subObj = list.get(0);
                        if (subObj != null) {
                            if (subObj instanceof Map) {
                                ((Map<?, ?>) subObj).forEach((k, v) -> {
                                    row.put(metric + "." + k, "");
//                                    row.put((String) k, "-");
                                });
                            }
                        }
                    }
                }
            }

            result.add(row);
        }

        return result;
    }


    @Override
    @TenantIgnore
    public PageResult<Object> getDetailPage(BulletinDO info, Integer pageSize, Integer pageNo) {
        List<Long> allAssetsIds = JsonUtils.parseArray(info.getAssetIds(), Long.class);
        // 计算起始索引和结束索引
        int total = allAssetsIds.size();
        int fromIndex = (pageNo - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, total);
        // 截取分页数据
        List<Long> assetsIds = allAssetsIds.subList(fromIndex, toIndex);


        List<String> edgeCategory = Arrays.stream(BulletinCloudMonitorItemEnum.values()).map(BulletinCloudMonitorItemEnum::getApp).toList();
        List<Object> res = new ArrayList<>();
        if (edgeCategory.contains(info.getApp())) { // 私有云
            for (Long assetsId : assetsIds) {
                Object t = getPrivateMonitorList(assetsId, info.getApp());
                if (t != null) {
                    res.add(t);
                }
            }
        } else {//hzb 赫兹 监控
            for (Long assetsId : assetsIds) {
                List<String> metrics = monitorApi.getAppDefine(info.getApp());
                Map<String, Object> hzTmpList = new HashMap<>();
                int maxCol = 0;
                for (String metric : metrics) {
                    MetricsData tmp;
                    try {
                        tmp = metricsDataService.getMetricsData(assetsId, metric);
                    } catch (Exception e) {
                        continue;
                    }
                    if (tmp == null) {
                        continue;
                    }
                    List<Map<String, Object>> tmpMetricList = new ArrayList<>();
                    for (ValueRow valueRow : tmp.getValueRows()) {
                        Map<String, Object> tmpItem = new HashMap<>();
                        for (int i = 0; i < valueRow.getValues().size(); i++) {
                            if (valueRow.getValues().get(i).getOrigin() == null) {
                                tmpItem.put(tmp.getFields().get(i).getName(), "无数据");
                            } else {
                                tmpItem.put(tmp.getFields().get(i).getName(), valueRow.getValues().get(i).getOrigin() + tmp.getFields().get(i).getUnit());
                            }
                        }
                        tmpMetricList.add(tmpItem);
                    }
                    hzTmpList.put(metric, tmpMetricList);
                }
                Map<String, Object> hzbInfo = bulletinMapper.selectHzbById(assetsId);
                if (hzbInfo != null) {
                    hzTmpList.put("name", hzbInfo.getOrDefault("name", ""));
                    hzTmpList.put("host", hzbInfo.getOrDefault("host", ""));
                }
                if (!hzTmpList.isEmpty()) {
                    res.add(hzTmpList);
//                    List<Map<String, Object>> flatRows = flattenRes(hzTmpList);
//                    res.addAll(flatRows);
                }
            }
        }

        PageResult<Object> pageResult = PageResult.empty();
        pageResult.setList(res);
        pageResult.setTotal((long) total);
        return pageResult;
    }

    private Object getPrivateMonitorList(Long assetsId, String app) {
        if (app.equals(BulletinCloudMonitorItemEnum.HOST.getApp())) {
            HostInfoDO tmp = hostInfoMapper.selectById(assetsId);
            if (tmp == null) {
                return null;
            }
            String uuid = tmp.getUuid();
            HostInfoDO hostInfo = hostInfoService.getHostInfo(uuid);
            HostInfoRespVO respVO = HostInfoConvert.INSTANCE.convert(hostInfo);
            //获得云盘总容量和使用量
            List<VolumeInfoUseReqVO> list = volumeInfoService.getVolumeInfoListByUuid(uuid);
            //安全组
            List<SecgroupDO> secgroupListByHostUuid = secgroupService.getSecgroupListByHostUuid(hostInfo.getUuid());
            //标签
            List<TagsDO> tag = tagsService.getTagListByTagGables(new TaggablesExportReqVO()
                    .setTaggableId(respVO.getId())
                    .setTaggableType(TagAssetTypeEnum.INSTANCE.getCode())
            );
            respVO.setTags(TagsConvert.INSTANCE.convertList(tag));
            respVO.setDiskInfos(list);
            respVO.setSecgroupDOS(secgroupListByHostUuid);
            BulletinHost res = new BulletinHost();
            BeanUtil.copyProperties(respVO, res);
            res.setSysDiskInfos("");
            res.setDataDiskInfos("");
            if (res.getDiskInfos() != null && !res.getDiskInfos().isEmpty()) {
                for (VolumeInfoUseReqVO volumeInfoUseReqVO : res.getDiskInfos()) {
                    if (volumeInfoUseReqVO.getType().equals("Root")) {
                        res.setSysDiskInfos(volumeInfoUseReqVO.getSize());
                    } else {
                        if (res.getDataDiskInfos().isEmpty()) {
                            res.setDataDiskInfos(volumeInfoUseReqVO.getSize());
                        } else {
                            res.setDataDiskInfos(res.getDataDiskInfos() + " , " + volumeInfoUseReqVO.getSize());
                        }
                    }

                }
                res.setTagsStr(res.getTags().stream().map(TagsRespVO::getName).collect(Collectors.joining(",")));
            }
            if (res.getSecgroupDOS() != null && !res.getSecgroupDOS().isEmpty()) {
                res.setSecgroup(res.getSecgroupDOS().stream().map(SecgroupDO::getName).collect(Collectors.joining(",")));
            }
            res.setTagsStr(res.getTags().stream().map(TagsRespVO::getName).collect(Collectors.joining(",")));
            return res;
        } else if (app.equals(BulletinCloudMonitorItemEnum.HARDWARE.getApp())) {
            HardwareInfoDO tmp = hardwareInfoMapper.selectById(assetsId);
            if (tmp == null) {
                return null;
            }
            String uuid = tmp.getUuid();
            HardwareInfoDO hardwareInfo = hardwareInfoService.getHardwareInfo(uuid);
            HardwareInfoRespVO respVO = BeanUtil.copyProperties(hardwareInfo, HardwareInfoRespVO.class);
            //宿主机对应云主机数量
            Integer cloudHostCount = hardwareInfoService.getCloudHostCount(uuid);
            respVO.setCloudHostCount(cloudHostCount);
            Integer hardwareCount = hardwareInfoService.getHardwareCount(hardwareInfo.getPlatformId());

            //标签
            List<TagsDO> tag = tagsService.getTagListByTagGables(new TaggablesExportReqVO()
                    .setTaggableId(respVO.getId())
                    .setTaggableType(TagAssetTypeEnum.HOST.getCode())
            );
            respVO.setTags(TagsConvert.INSTANCE.convertList(tag));
            respVO.setHardwareCount(hardwareCount);
            BulletinHardware res = new BulletinHardware();
            BeanUtil.copyProperties(respVO, res);
            res.setTagsStr(res.getTags().stream().map(TagsRespVO::getName).collect(Collectors.joining(",")));
            res.setTotalVirtualCpu(res.getCpuNum() * res.getCpuOverPercent().intValue());
            res.setBandwidthStream("上行：" + res.getBandwidthUpstream() + "   ，下行：" + res.getBandwidthDownstream());
            return res;
        } else if (app.equals(BulletinCloudMonitorItemEnum.STORAGE.getApp())) {
            StorageInfoDO tmp = storageInfoMapper.selectById(assetsId);
            if (tmp == null) {
                return null;
            }
            String uuid = tmp.getUuid();
            StorageInfoDO storageInfo = storageInfoService.getStorageInfo(uuid);
            StorageInfoRespVO respVO = StorageInfoConvert.INSTANCE.convert(storageInfo);
            //查询存储对应宿主机数量
            Integer hareWareCount = hardwareStorageService.getHareWareCountByStorageUuid(storageInfo.getUuid());
            Integer volumeCount = volumeInfoService.getVolumeCountByStorageUuid(storageInfo.getUuid());
            respVO.setHardwareCount(hareWareCount);
            respVO.setVolumeCount(volumeCount);
            respVO.setVirtualUtilization(
                    respVO.getTotalCapacity() > 0 ?
                            new BigDecimal(respVO.getUsedCapacity() * 100.00 / respVO.getTotalCapacity()) : new BigDecimal(0));
            respVO.setPhysicalUsage(respVO.getTotalPhysicalCapacity().subtract(respVO.getAvailablePhysicalCapacity()));
            //标签
            List<TagsDO> tag = tagsService.getTagListByTagGables(new TaggablesExportReqVO()
                    .setTaggableId(respVO.getId())
                    .setTaggableType(TagAssetTypeEnum.STORAGE.getCode())
            );
            respVO.setTags(TagsConvert.INSTANCE.convertList(tag));
            if (respVO.getWasteCapacity() == null) {
                respVO.setWasteCapacity(BigDecimal.valueOf(0));
            }
            if (respVO.getReserveCapacity() == null) {
                respVO.setReserveCapacity(BigDecimal.valueOf(0));
            }
            BulletinStorage res = new BulletinStorage();
            BeanUtil.copyProperties(respVO, res);
            res.setTagsStr(res.getTags().stream().map(TagsRespVO::getName).collect(Collectors.joining(",")));
            return res;
        } else if (app.equals(BulletinCloudMonitorItemEnum.IMAGE.getApp())) {
            ImageInfoDO imageInfo = imageInfoService.getImageInfo(assetsId);
            if (imageInfo == null) {
                return null;
            }
            ImageInfoRespVO respVO = ImageInfoConvert.INSTANCE.convert(imageInfo);
            //标签
            List<TagsDO> tag = tagsService.getTagListByTagGables(new TaggablesExportReqVO()
                    .setTaggableId(respVO.getId())
                    .setTaggableType(TagAssetTypeEnum.IMAGE.getCode())
            );
            respVO.setTags(TagsConvert.INSTANCE.convertList(tag));
            BulletinImage res = new BulletinImage();
            BeanUtil.copyProperties(respVO, res);
            res.setTagsStr(res.getTags().stream().map(TagsRespVO::getName).collect(Collectors.joining(",")));
            return res;
        } else if (app.equals(BulletinCloudMonitorItemEnum.DISK.getApp())) {
            VolumeInfoDO volumeInfo;
            try {
                volumeInfo = volumeInfoService.getVolumeInfo(assetsId);
            } catch (Exception e) {
                return null;
            }
            VolumeInfoRespVO respVO = VolumeInfoConvert.INSTANCE.convert(volumeInfo);
            //标签
            List<TagsDO> tag = tagsService.getTagListByTagGables(new TaggablesExportReqVO()
                    .setTaggableId(respVO.getId())
                    .setTaggableType(TagAssetTypeEnum.DISK.getCode())
            );
            respVO.setTags(TagsConvert.INSTANCE.convertList(tag));
            BulletinDisk res = new BulletinDisk();
            BeanUtil.copyProperties(respVO, res);
            res.setTagsStr(res.getTags().stream().map(TagsRespVO::getName).collect(Collectors.joining(",")));
            return res;
        } else if (app.equals(BulletinCloudMonitorItemEnum.SNAPSHOT.getApp())) {
            VolumeSnapshotDO volumeSnapshot = volumeSnapshotService.getVolumeSnapshot(assetsId);
            if (volumeSnapshot == null) {
                return null;
            }
            VolumeSnapshotRespVO respVO = VolumeSnapshotConvert.INSTANCE.convert(volumeSnapshot);
            //标签
            List<TagsDO> tag = tagsService.getTagListByTagGables(new TaggablesExportReqVO()
                    .setTaggableId(respVO.getId())
                    .setTaggableType(TagAssetTypeEnum.SNAPSHOT.getCode())
            );
            respVO.setTags(TagsConvert.INSTANCE.convertList(tag));
            BulletinSnapshot res = new BulletinSnapshot();
            BeanUtil.copyProperties(respVO, res);
            res.setTagsStr(res.getTags().stream().map(TagsRespVO::getName).collect(Collectors.joining(",")));
            return res;
        } else if (app.equals(BulletinCloudMonitorItemEnum.NETWORK_L2.getApp())) {
            NetworkL2DO networkL2 = networkL2Service.getNetworkL2(assetsId, "");
            if (networkL2 == null) {
                return null;
            }
            NetworkL2RespVO respVO = NetworkL2Convert.INSTANCE.convert(networkL2);
            BulletinNetworkL2 res = new BulletinNetworkL2();
            BeanUtil.copyProperties(respVO, res);
            return res;
        } else if (app.equals(BulletinCloudMonitorItemEnum.NETWORK_L3.getApp())) {
            NetworkL3DO networkL3 = networkL3Service.getNetworkL3(assetsId, "");
            if (networkL3 == null) {
                return null;
            }
            NetworkL3RespVO respVO = NetworkL3Convert.INSTANCE.convert(networkL3);
            BulletinNetworkL3 res = new BulletinNetworkL3();
            BeanUtil.copyProperties(respVO, res);
            return res;
        } else if (app.equals(BulletinCloudMonitorItemEnum.NETWORK_VPC.getApp())) {
            NetworkVpcDO networkVpc = networkVpcService.getNetworkVpc(assetsId);
            if (networkVpc == null) {
                return null;
            }
            NetworkVpcRespVO respVO = NetworkVpcConvert.INSTANCE.convert(networkVpc);
            BulletinNetworkVpc res = new BulletinNetworkVpc();
            BeanUtil.copyProperties(respVO, res);
            return res;
        } else if (app.equals(BulletinCloudMonitorItemEnum.NETWORK_SECGROUP.getApp())) {
            SecgroupDO secgroup = secgroupService.getSecgroup(assetsId);
            if (secgroup == null) {
                return null;
            }
            SecgroupRespVO respVO = SecgroupConvert.INSTANCE.convert(secgroup);
            BulletinNetworkSecgroup res = new BulletinNetworkSecgroup();
            BeanUtil.copyProperties(respVO, res);
            return res;
        }
        return null;
    }

    private PageResult<Object> getPrivateMonitorPage(List<Long> assetsIds, String app, Integer pageNo, Integer pageSize) {
        PageResult<Object> pageResults = new PageResult<>();
        if (app.equals(BulletinCloudMonitorItemEnum.HOST.getCategory())) {
            HostInfoPageReqVO pageVO = new HostInfoPageReqVO();
            pageVO.setPageNo(pageNo);
            pageVO.setPageSize(pageSize);
            pageVO.setInPks(assetsIds);
            PageResult<HostInfoDO> pageResult = hostInfoService.getHostInfoPage(pageVO);
            List<HostInfoRespVO> hostInfoRespVOS = new ArrayList<>();
            if (pageResult.getList() != null && !pageResult.getList().isEmpty()) {
                hostInfoRespVOS = BeanUtil.copyToList(pageResult.getList(), HostInfoRespVO.class);
            }
            for (HostInfoRespVO hostInfoRespVO : hostInfoRespVOS) {
                List<TagsDO> tag = tagsService.getTagListByTagGables(new TaggablesExportReqVO()
                        .setTaggableId(hostInfoRespVO.getId())
                        .setTaggableType(TagAssetTypeEnum.INSTANCE.getCode())
                );
                hostInfoRespVO.setTags(TagsConvert.INSTANCE.convertList(tag));
            }
            pageResults.setList(Arrays.asList(hostInfoRespVOS.toArray()));
            pageResults.setTotal(pageResult.getTotal());
            return pageResults;
        } else if (app.equals(BulletinCloudMonitorItemEnum.HARDWARE.getCategory())) {
            HardwareInfoPageReqVO pageVO = new HardwareInfoPageReqVO();
            pageVO.setPageNo(pageNo);
            pageVO.setPageSize(pageSize);
            pageVO.setInPks(assetsIds);
            PageResult<HardwareInfoDO> pageResult = hardwareInfoService.getHardwareInfoPage(pageVO);
            List<HardwareInfoRespVO> infoRespVOS = new ArrayList<>();
            if (pageResult.getList() != null && !pageResult.getList().isEmpty()) {
                infoRespVOS = BeanUtil.copyToList(pageResult.getList(), HardwareInfoRespVO.class);
            }
            for (HardwareInfoRespVO respVO : infoRespVOS) {
                List<TagsDO> tag = tagsService.getTagListByTagGables(new TaggablesExportReqVO()
                        .setTaggableId(respVO.getId())
                        .setTaggableType(TagAssetTypeEnum.HOST.getCode())
                );
                respVO.setTags(TagsConvert.INSTANCE.convertList(tag));
            }
            pageResults.setList(List.of(infoRespVOS.toArray()));
            pageResults.setTotal(pageResult.getTotal());
            return pageResults;
        } else if (app.equals(BulletinCloudMonitorItemEnum.STORAGE.getCategory())) {
            StorageInfoPageReqVO pageVO = new StorageInfoPageReqVO();
            pageVO.setPageNo(pageNo);
            pageVO.setPageSize(pageSize);
            pageVO.setInPks(assetsIds);
            PageResult<StorageInfoDO> pageResult = storageInfoService.getStorageInfoPage(pageVO);
            PageResult<StorageInfoRespVO> list = StorageInfoConvert.INSTANCE.convertPage(pageResult);
            list.getList().forEach(item -> {
                List<TagsDO> tag = tagsService.getTagListByTagGables(new TaggablesExportReqVO()
                        .setTaggableId(item.getId())
                        .setTaggableType(TagAssetTypeEnum.STORAGE.getCode())
                );
                item.setTags(TagsConvert.INSTANCE.convertList(tag)).setVirtualUtilization(
                                item.getTotalCapacity() > 0 ?
                                        new BigDecimal(item.getUsedCapacity() * 100.00 / item.getTotalCapacity()) : new BigDecimal(0))
                        .setPhysicalUsage(item.getTotalPhysicalCapacity().subtract(item.getAvailablePhysicalCapacity()))
                        .setCapacityUtilization(item.getCapacityUtilization() != null ? item.getCapacityUtilization() : new BigDecimal(0));
            });
            pageResults.setList(Arrays.asList(list.getList().toArray()));
            pageResults.setTotal(pageResult.getTotal());
            return pageResults;
        } else if (app.equals(BulletinCloudMonitorItemEnum.IMAGE.getCategory())) {
            ImageInfoPageReqVO pageVO = new ImageInfoPageReqVO();
            pageVO.setPageNo(pageNo);
            pageVO.setPageSize(pageSize);
            pageVO.setIds(assetsIds);
            PageResult<ImageInfoDO> pageResult = imageInfoService.getImageInfoPage(pageVO);
            List<ImageInfoRespVO> infoRespVOS = new ArrayList<>();
            if (pageResult.getList() != null && !pageResult.getList().isEmpty()) {
                infoRespVOS = BeanUtil.copyToList(pageResult.getList(), ImageInfoRespVO.class);
            }
            for (ImageInfoRespVO respVO : infoRespVOS) {
                List<TagsDO> tag = tagsService.getTagListByTagGables(new TaggablesExportReqVO()
                        .setTaggableId(respVO.getId())
                        .setTaggableType(TagAssetTypeEnum.IMAGE.getCode())
                );
                respVO.setTags(TagsConvert.INSTANCE.convertList(tag));
            }
            pageResults.setList(Arrays.asList(infoRespVOS.toArray()));
            pageResults.setTotal(pageResult.getTotal());
            return pageResults;
        } else if (app.equals(BulletinCloudMonitorItemEnum.DISK.getCategory())) {
            VolumeInfoPageReqVO pageVO = new VolumeInfoPageReqVO();
            pageVO.setPageNo(pageNo);
            pageVO.setPageSize(pageSize);
            pageVO.setIds(assetsIds);
            PageResult<VolumeInfoDO> pageResult = volumeInfoService.getVolumeInfoPage(pageVO);
            List<VolumeInfoRespVO> infoRespVOS = new ArrayList<>();
            if (pageResult.getList() != null && !pageResult.getList().isEmpty()) {
                infoRespVOS = BeanUtil.copyToList(pageResult.getList(), VolumeInfoRespVO.class);
            }
            for (VolumeInfoRespVO respVO : infoRespVOS) {
                List<TagsDO> tag = tagsService.getTagListByTagGables(new TaggablesExportReqVO()
                        .setTaggableId(respVO.getId())
                        .setTaggableType(TagAssetTypeEnum.DISK.getCode())
                );
                respVO.setTags(TagsConvert.INSTANCE.convertList(tag));
            }
            pageResults.setList(Arrays.asList(infoRespVOS.toArray()));
            pageResults.setTotal(pageResult.getTotal());
            return pageResults;
        } else if (app.equals(BulletinCloudMonitorItemEnum.SNAPSHOT.getCategory())) {
            VolumeSnapshotPageReqVO pageVO = new VolumeSnapshotPageReqVO();
            pageVO.setPageNo(pageNo);
            pageVO.setPageSize(pageSize);
            pageVO.setIds(assetsIds);
            PageResult<VolumeSnapshotDO> pageResult = volumeSnapshotService.getVolumeSnapshotPage(pageVO);

            List<VolumeSnapshotRespVO> infoRespVOS = new ArrayList<>();
            if (pageResult.getList() != null && !pageResult.getList().isEmpty()) {
                infoRespVOS = BeanUtil.copyToList(pageResult.getList(), VolumeSnapshotRespVO.class);
            }
            for (VolumeSnapshotRespVO respVO : infoRespVOS) {
                List<TagsDO> tag = tagsService.getTagListByTagGables(new TaggablesExportReqVO()
                        .setTaggableId(respVO.getId())
                        .setTaggableType(TagAssetTypeEnum.SNAPSHOT.getCode())
                );
                respVO.setTags(TagsConvert.INSTANCE.convertList(tag));
            }
            pageResults.setList(Arrays.asList(infoRespVOS.toArray()));
            pageResults.setTotal(pageResult.getTotal());
            return pageResults;
        } else if (app.equals(BulletinCloudMonitorItemEnum.NETWORK_L2.getCategory())) {
            NetworkL2PageReqVO pageVO = new NetworkL2PageReqVO();
            pageVO.setInPks(assetsIds);
            pageVO.setPageNo(pageNo);
            pageVO.setPageSize(pageSize);
            PageResult<NetworkL2DO> pageResult = networkL2Service.getNetworkL2Page(pageVO);
            PageResult<NetworkL2RespVO> pageResults2 = NetworkL2Convert.INSTANCE.convertPage(pageResult);
            pageResults.setList(Arrays.asList(pageResults2.getList().toArray()));
            pageResults.setTotal(pageResult.getTotal());
            return pageResults;
        } else if (app.equals(BulletinCloudMonitorItemEnum.NETWORK_L3.getCategory())) {
            NetworkL3PageReqVO pageVO = new NetworkL3PageReqVO();
            pageVO.setInPks(assetsIds);
            pageVO.setPageNo(pageNo);
            pageVO.setPageSize(pageSize);
            PageResult<NetworkL3DO> pageResult = networkL3Service.getNetworkL3Page(pageVO);
            PageResult<NetworkL3RespVO> pageResults2 = NetworkL3Convert.INSTANCE.convertPage(pageResult);
            pageResults.setList(Arrays.asList(pageResults2.getList().toArray()));
            pageResults.setTotal(pageResult.getTotal());
            return pageResults;
        } else if (app.equals(BulletinCloudMonitorItemEnum.NETWORK_VPC.getCategory())) {
            NetworkVpcPageReqVO pageVO = new NetworkVpcPageReqVO();
            pageVO.setInPks(assetsIds);
            pageVO.setPageNo(pageNo);
            pageVO.setPageSize(pageSize);
            PageResult<NetworkVpcDO> pageResult = networkVpcService.getNetworkVpcPage(pageVO);
            PageResult<NetworkVpcRespVO> pageResults2 = NetworkVpcConvert.INSTANCE.convertPage(pageResult);
            pageResults.setList(Arrays.asList(pageResults2.getList().toArray()));
            pageResults.setTotal(pageResult.getTotal());
            return pageResults;
        } else if (app.equals(BulletinCloudMonitorItemEnum.NETWORK_SECGROUP.getCategory())) {
            SecgroupPageReqVO pageVO = new SecgroupPageReqVO();
            pageVO.setInPks(assetsIds);
            pageVO.setPageNo(pageNo);
            pageVO.setPageSize(pageSize);
            PageResult<SecgroupDO> pageResult = secgroupService.getSecgroupPage(pageVO);
            PageResult<SecgroupRespVO> pageResults2 = SecgroupConvert.INSTANCE.convertPage(pageResult);
            pageResults.setList(Arrays.asList(pageResults2.getList().toArray()));
            pageResults.setTotal(pageResult.getTotal());
            return pageResults;
        }
        return PageResult.empty();
    }

    @Override
    public List<BulletinFieldItemDto> getAppBulletinFieldList(String app) {
        Map<String, List<BulletinFieldItemDto>> l = getBulletinFieldList();
        if (!l.containsKey(app)) {
            return new ArrayList<>();
        }
        return l.get(app);
    }

    @Override
    @TenantIgnore
    public void exportDetailExcel(BulletinDO bulletin, HttpServletResponse response) throws IOException {
        // 判断 bulletin 的 app 是否在 BulletinCloudMonitorItemEnum 中
        boolean existsInEnum = Arrays.stream(BulletinCloudMonitorItemEnum.values())
                .anyMatch(e -> e.getApp().equals(bulletin.getApp()));

        if (existsInEnum) {
            privateCloudMonitorExportDetailExcel(bulletin, response);
        } else {
            String fields = bulletin.getFields();
            // fields转换为 List<BulletinFieldItemDto>
            List<Map> bulletinFields = JsonUtils.parseArray(fields, Map.class);
            List<Object> list = this.getDetailPage(bulletin, 20000, 1).getList();
            List<List<String>> head = new ArrayList<>();
            List<String> dataField = new ArrayList<>();
            List<Integer> mergeColumns = new ArrayList<>(); // 需要合并的列索引

            // 构建表头
            for (Map fieldItem : bulletinFields) {
                String code = fieldItem.get("code").toString();
                String label = fieldItem.get("label").toString();
                String field = fieldItem.get("field").toString();

                if (code.equals("monitor_name")) {
                    head.add(Arrays.asList("监控名称", "监控名称"));
                    dataField.add(field);
                    mergeColumns.add(dataField.size() - 1); // 标记为需要合并的列
                    continue;
                }
                if (code.equals("monitor_host")) {
                    head.add(Arrays.asList("host", "host"));
                    dataField.add(field);
                    mergeColumns.add(dataField.size() - 1); // 标记为需要合并的列
                    continue;
                }

                List<Map<String, Object>> child = (List<Map<String, Object>>) fieldItem.get("children");
                if (!child.isEmpty()) {
                    for (Map<String, Object> childItem : child) {
                        String childCode = childItem.get("code").toString();
                        String childLabel = childItem.get("label").toString();
                        head.add(Arrays.asList(label, childLabel));
                        dataField.add(code + "." + childCode);
                        mergeColumns.add(dataField.size() - 1); // 标记为需要合并的列
                    }
                }
            }

            // 构建数据
            List<List<String>> dataList = new ArrayList<>();
            int startMergeRow = 2;
            int nameIndex = -1;
            int hostIndex = -1;
            ArrayList<MergeCellData> mergeCells = new ArrayList<>();
            if (dataField.contains("name")) {
                // 获取对应下标
                 nameIndex = dataField.indexOf("name");
            }
            if (dataField.contains("host")) {
                // 获取对应下标
                hostIndex = dataField.indexOf("host");
            }

            for (Object item : list) {
                Map<String, Object> itemMap = (Map<String, Object>) item;
                List<Map<String, Object>> itemMapRows = flattenRes(itemMap);
                if (nameIndex >= 0 && itemMapRows.size() > 1) {
                    mergeCells.add(new MergeCellData(startMergeRow, startMergeRow + itemMapRows.size() - 1, nameIndex, nameIndex));
                }
                if (hostIndex >= 0 && itemMapRows.size() > 1) {
                    mergeCells.add(new MergeCellData(startMergeRow, startMergeRow + itemMapRows.size() - 1, hostIndex, hostIndex));
                }
                for (Map<String, Object> itemMapVal : itemMapRows) {
                    List<String> dataItem = new ArrayList<>();
                    for (String field : dataField) {
                        String cell = itemMapVal.getOrDefault(field, "") == null ? "" : itemMapVal.getOrDefault(field, "").toString();
                        if (cell.length() > 1024) {
                            cell = cell.substring(0, 1024) + "...";
                        }
                        dataItem.add(cell);
                    }
                    dataList.add(dataItem);
                }
                startMergeRow += itemMapRows.size();
            }

            // 导出Excel
            ExcelWriterBuilder exportEx = EasyExcel.write(response.getOutputStream())
                    .autoCloseStream(false)
                    .head(head)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy());
            for (MergeCellData mergeCellData : mergeCells) {
                exportEx.registerWriteHandler(new OnceAbsoluteMergeStrategy(mergeCellData.startRow, mergeCellData.endRow, mergeCellData.startColumn, mergeCellData.endColumn)); // 注册合并策略
            }
            exportEx.sheet("Sheet1")
                    .doWrite(dataList);

            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("实时报表.xlsx", "UTF-8"));
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        }
    }

    private void privateCloudMonitorExportDetailExcel(BulletinDO bulletin, HttpServletResponse response) throws IOException {
        String fields = bulletin.getFields();
        //fields json 转 List<BulletinFieldItemDto>
        List<BulletinFieldItemDto> fieldItemDtos = JSONObject.parseArray(fields, BulletinFieldItemDto.class);
        Set<String> includeFields = new HashSet<>(fieldItemDtos.stream().map(BulletinFieldItemDto::getField).toList());

        List<Object> list = this.getDetailList(bulletin);
        // 导出 Excel
        if (list.isEmpty()) return;
        if (bulletin.getApp().equals(BulletinCloudMonitorItemEnum.HARDWARE.getApp())) {
            // List<Object> list 转List<BulletinHardware>
            List<BulletinHardware> excelList = list.stream()
                    .filter(item -> item instanceof BulletinHardware)
                    .map(item -> (BulletinHardware) item)
                    .toList();
            this.writeExcel(response, "实时报表.xls", "数据", BulletinHardware.class, excelList, includeFields);
        } else if (bulletin.getApp().equals(BulletinCloudMonitorItemEnum.HOST.getApp())) {
            List<BulletinHost> excelList = list.stream()
                    .filter(item -> item instanceof BulletinHost)
                    .map(item -> (BulletinHost) item)
                    .toList();
            this.writeExcel(response, "实时报表.xls", "数据", BulletinHost.class, excelList, includeFields);
        } else if (bulletin.getApp().equals(BulletinCloudMonitorItemEnum.STORAGE.getApp())) {
            List<BulletinStorage> excelList = list.stream()
                    .filter(item -> item instanceof BulletinStorage)
                    .map(item -> (BulletinStorage) item)
                    .toList();
            this.writeExcel(response, "实时报表.xls", "数据", BulletinStorage.class, excelList, includeFields);
        } else if (bulletin.getApp().equals(BulletinCloudMonitorItemEnum.IMAGE.getApp())) {
            List<BulletinImage> excelList = list.stream()
                    .filter(item -> item instanceof BulletinImage)
                    .map(item -> (BulletinImage) item)
                    .toList();
            this.writeExcel(response, "实时报表.xls", "数据", BulletinImage.class, excelList, includeFields);
        } else if (bulletin.getApp().equals(BulletinCloudMonitorItemEnum.DISK.getApp())) {
            List<BulletinDisk> excelList = list.stream()
                    .filter(item -> item instanceof BulletinDisk)
                    .map(item -> (BulletinDisk) item)
                    .toList();
            this.writeExcel(response, "实时报表.xls", "数据", BulletinDisk.class, excelList, includeFields);
        } else if (bulletin.getApp().equals(BulletinCloudMonitorItemEnum.SNAPSHOT.getApp())) {
            List<BulletinSnapshot> excelList = list.stream()
                    .filter(item -> item instanceof BulletinSnapshot)
                    .map(item -> (BulletinSnapshot) item)
                    .toList();
            this.writeExcel(response, "实时报表.xls", "数据", BulletinSnapshot.class, excelList, includeFields);
        } else if (bulletin.getApp().equals(BulletinCloudMonitorItemEnum.NETWORK_L2.getApp())) {
            List<BulletinNetworkL2> excelList = list.stream()
                    .filter(item -> item instanceof BulletinNetworkL2)
                    .map(item -> (BulletinNetworkL2) item)
                    .toList();
            this.writeExcel(response, "实时报表.xls", "数据", BulletinNetworkL2.class, excelList, includeFields);
        } else if (bulletin.getApp().equals(BulletinCloudMonitorItemEnum.NETWORK_L3.getApp())) {
            List<BulletinNetworkL3> excelList = list.stream()
                    .filter(item -> item instanceof BulletinNetworkL3)
                    .map(item -> (BulletinNetworkL3) item)
                    .toList();
            this.writeExcel(response, "实时报表.xls", "数据", BulletinNetworkL3.class, excelList, includeFields);
        } else if (bulletin.getApp().equals(BulletinCloudMonitorItemEnum.NETWORK_VPC.getApp())) {
            List<BulletinNetworkVpc> excelList = list.stream()
                    .filter(item -> item instanceof BulletinNetworkVpc)
                    .map(item -> (BulletinNetworkVpc) item)
                    .toList();
            this.writeExcel(response, "实时报表.xls", "数据", BulletinNetworkVpc.class, excelList, includeFields);
        } else if (bulletin.getApp().equals(BulletinCloudMonitorItemEnum.NETWORK_SECGROUP.getApp())) {
            List<BulletinNetworkSecgroup> excelList = list.stream()
                    .filter(item -> item instanceof BulletinNetworkSecgroup)
                    .map(item -> (BulletinNetworkSecgroup) item)
                    .toList();
            this.writeExcel(response, "实时报表.xls", "数据", BulletinNetworkSecgroup.class, excelList, includeFields);
        } else {
            return;
        }
    }

    private Map<String, List<BulletinFieldItemDto>> getBulletinFieldList() {
        HashMap<String, List<BulletinFieldItemDto>> res = new HashMap<>();

        //宿主机
        List<BulletinFieldItemDto> hardware = new ArrayList<>();
        hardware.add(new BulletinFieldItemDto("name", "宿主机名称", "name"));
        hardware.add(new BulletinFieldItemDto("platformName", "平台名称", "platformName"));
        hardware.add(new BulletinFieldItemDto("clusterName", "集群名称", "clusterName"));
        hardware.add(new BulletinFieldItemDto("state", "启用状态", "state"));
        hardware.add(new BulletinFieldItemDto("status", "就绪状态", "status"));
        hardware.add(new BulletinFieldItemDto("ip", "宿主机IP", "ip"));
        hardware.add(new BulletinFieldItemDto("cpuUsed", "CPU使用率", "cpuUsed"));
        hardware.add(new BulletinFieldItemDto("memoryUsed", "内存使用率", "memoryUsed"));
//        hardware.add(new BulletinFieldItemDto("bandwidthStream", "宽带上下行", "bandwidthStream"));
        hardware.add(new BulletinFieldItemDto("bandwidthUpstream", "宽带上行", "bandwidthUpstream"));
        hardware.add(new BulletinFieldItemDto("bandwidthDownstream", "宽带下行", "bandwidthDownstream"));
        hardware.add(new BulletinFieldItemDto("tags", "标签", "tagsStr"));
        hardware.add(new BulletinFieldItemDto("ipmi", "IPMI地址", "ipmi"));
        hardware.add(new BulletinFieldItemDto("mac", "MAC地址", "mac"));
        hardware.add(new BulletinFieldItemDto("manager", "区域", "manager"));
        hardware.add(new BulletinFieldItemDto("availableManager", "可用区域", "availableManager"));
        hardware.add(new BulletinFieldItemDto("cpuNum", "物理CPU", "cpuNum"));
        hardware.add(new BulletinFieldItemDto("cpuSockets", "插槽数", "cpuSockets"));
        hardware.add(new BulletinFieldItemDto("cpuOverPercent", "CPU超售比", "cpuOverPercent"));
        hardware.add(new BulletinFieldItemDto("totalVirtualCpu", "虚拟CPU", "totalVirtualCpu"));
        hardware.add(new BulletinFieldItemDto("cpuCommitRate", "已分配CPU", "cpuCommitRate"));
        hardware.add(new BulletinFieldItemDto("cpuType", "CPU型号", "cpuType"));
        hardware.add(new BulletinFieldItemDto("architecture", "CPU架构", "architecture"));
        hardware.add(new BulletinFieldItemDto("totalMemoryCapacity", "物理容量", "totalMemoryCapacity"));
        hardware.add(new BulletinFieldItemDto("totalVirtualMemory", "虚拟容量", "totalVirtualMemory"));
        hardware.add(new BulletinFieldItemDto("memoryCommitRate", "已分配容量", "allocatedMemoryCapacity"));
        hardware.add(new BulletinFieldItemDto("memoryOverPercent", "内存超售比", "memoryOverPercent"));
        hardware.add(new BulletinFieldItemDto("reservedMemory", "系统预留", "reservedMemory"));
        res.put(BulletinCloudMonitorItemEnum.HARDWARE.getCode(), hardware);


        //云主机
        List<BulletinFieldItemDto> host = new ArrayList<>();
        host.add(new BulletinFieldItemDto("name", "云主机名称", "name"));
        host.add(new BulletinFieldItemDto("platformName", "平台名称", "platformName"));
        host.add(new BulletinFieldItemDto("vipIp", "弹性IP", "vipIp"));
        host.add(new BulletinFieldItemDto("state", "启用状态", "state"));
        host.add(new BulletinFieldItemDto("cpuUsed", "CPU使用率", "cpuUsed"));
        host.add(new BulletinFieldItemDto("memoryUsed", "内存使用率", "memoryUsed"));
        host.add(new BulletinFieldItemDto("diskUsed", "磁盘使用率", "diskUsed"));
        host.add(new BulletinFieldItemDto("ip", "IP地址", "ip"));
        host.add(new BulletinFieldItemDto("mac", "MAC地址", "mac"));
        host.add(new BulletinFieldItemDto("clusterName", "集群名称", "clusterName"));
        host.add(new BulletinFieldItemDto("architecture", "CPU架构", "architecture"));
        host.add(new BulletinFieldItemDto("type", "主机类型", "type"));
        host.add(new BulletinFieldItemDto("guestOsType", "系统类型", "guestOsType"));
        host.add(new BulletinFieldItemDto("powerState", "电源状态", "powerState"));
        host.add(new BulletinFieldItemDto("tags", "标签", "tagsStr"));
//        host.add(new BulletinFieldItemDto("manager", "区域", "manager"));
        host.add(new BulletinFieldItemDto("zoneName", "可用区域", "zoneName"));
        host.add(new BulletinFieldItemDto("imageName", "系统镜像", "imageName"));
        host.add(new BulletinFieldItemDto("hardwareName", "宿主机", "hardwareName"));
        host.add(new BulletinFieldItemDto("secgroup", "安全组", "secgroup"));
        host.add(new BulletinFieldItemDto("cpuNum", "CPU", "cpuNum"));
        host.add(new BulletinFieldItemDto("memorySize", "内存", "memorySize"));
        host.add(new BulletinFieldItemDto("sysDiskInfos", "系统盘", "sysDiskInfos"));
        host.add(new BulletinFieldItemDto("dataDiskInfos", "数据盘", "dataDiskInfos"));
        host.add(new BulletinFieldItemDto("iso", "ISO", "iso"));
        host.add(new BulletinFieldItemDto("autoInitType", "自动启动", "autoInitType"));
        host.add(new BulletinFieldItemDto("guideMode", "引导模式", "guideMode"));
        res.put(BulletinCloudMonitorItemEnum.HOST.getCode(), host);

        List<BulletinFieldItemDto> storage = new ArrayList<>();
        storage.add(new BulletinFieldItemDto("name", "云存储名称", "name"));
        storage.add(new BulletinFieldItemDto("platformName", "平台名称", "platformName"));
        storage.add(new BulletinFieldItemDto("url", "URL", "url"));
        storage.add(new BulletinFieldItemDto("state", "启用状态", "state"));
        storage.add(new BulletinFieldItemDto("status", "就绪状态", "status"));
        storage.add(new BulletinFieldItemDto("type", "类型", "type"));
        storage.add(new BulletinFieldItemDto("capacityUtilization", "物理使用率", "capacityUtilization"));
        storage.add(new BulletinFieldItemDto("virtualUtilization", "置备使用率", "virtualUtilization"));
        storage.add(new BulletinFieldItemDto("totalCapacity", "总容量", "totalCapacity"));
        storage.add(new BulletinFieldItemDto("hardwareCount", "宿主机", "hardwareCount"));
        storage.add(new BulletinFieldItemDto("volumeCount", "云盘", "volumeCount"));
        storage.add(new BulletinFieldItemDto("tags", "标签", "tagsStr"));
        storage.add(new BulletinFieldItemDto("mediaType", "介质类型", "mediaType"));
        storage.add(new BulletinFieldItemDto("manager", "区域", "manager"));
        storage.add(new BulletinFieldItemDto("availableManager", "可用区域", "availableManager"));
        storage.add(new BulletinFieldItemDto("storagePercent", "超售比", "storagePercent"));
        storage.add(new BulletinFieldItemDto("commitRate", "分配率", "commitRate"));
        storage.add(new BulletinFieldItemDto("allocation", "分配", "allocation"));
        storage.add(new BulletinFieldItemDto("reserveCapacity", "预留", "reserveCapacity"));
        storage.add(new BulletinFieldItemDto("wasteCapacity", "浪费", "wasteCapacity"));
        res.put(BulletinCloudMonitorItemEnum.STORAGE.getCode(), storage);


        List<BulletinFieldItemDto> image = new ArrayList<>();
        image.add(new BulletinFieldItemDto("name", "镜像名称", "name"));
        image.add(new BulletinFieldItemDto("platformName", "平台名称", "platformName"));
        image.add(new BulletinFieldItemDto("status", "状态", "status"));
        image.add(new BulletinFieldItemDto("format", "镜像格式", "format"));
        image.add(new BulletinFieldItemDto("cpuArch", "CPU架构", "cpuArch"));
        image.add(new BulletinFieldItemDto("osType", "系统", "osType"));
        image.add(new BulletinFieldItemDto("size", "镜像大小", "size"));
        image.add(new BulletinFieldItemDto("imageType", "镜像类型", "imageType"));
        image.add(new BulletinFieldItemDto("sharingScope", "共享范围", "sharingScope"));
        image.add(new BulletinFieldItemDto("tags", "标签", "tagsStr"));
        image.add(new BulletinFieldItemDto("osLanguage", "系统语言", "osLanguage"));
        image.add(new BulletinFieldItemDto("minMemory", "最小内存要求", "minMemory"));
        image.add(new BulletinFieldItemDto("minDisk", "最小磁盘要求", "minDisk"));
        image.add(new BulletinFieldItemDto("diskDriver", "磁盘驱动", "diskDriver"));
        image.add(new BulletinFieldItemDto("networkDriver", "网卡驱动", "networkDriver"));
        image.add(new BulletinFieldItemDto("bootMode", "引导方式", "bootMode"));
        image.add(new BulletinFieldItemDto("remoteProtocol", "远程终端协议", "remoteProtocol"));
        res.put(BulletinCloudMonitorItemEnum.IMAGE.getCode(), image);

        List<BulletinFieldItemDto> disk = new ArrayList<>();
        disk.add(new BulletinFieldItemDto("name", "云盘名称", "name"));
        disk.add(new BulletinFieldItemDto("platformName", "平台名称", "platformName"));
        disk.add(new BulletinFieldItemDto("state", "状态", "state"));
        disk.add(new BulletinFieldItemDto("type", "云盘类型", "type"));
        disk.add(new BulletinFieldItemDto("format", "云盘格式", "format"));
        disk.add(new BulletinFieldItemDto("primaryStorageType", "存储类型", "primaryStorageType"));
        disk.add(new BulletinFieldItemDto("size", "云盘容量", "size"));
        disk.add(new BulletinFieldItemDto("maxIops", "最大IOPS", "maxIops"));
        disk.add(new BulletinFieldItemDto("vmInstanceName", "云主机", "vmInstanceName"));
        disk.add(new BulletinFieldItemDto("primaryStorageName", "云存储", "primaryStorageName"));
        disk.add(new BulletinFieldItemDto("isMount", "是否挂载", "isMount"));
        disk.add(new BulletinFieldItemDto("tags", "标签", "tags"));
        disk.add(new BulletinFieldItemDto("actualSize", "云盘真实容量", "actualSize"));
        disk.add(new BulletinFieldItemDto("throughput", "吞吐量", "throughput"));
        disk.add(new BulletinFieldItemDto("mediaType", "介质类型", "mediaType"));
        res.put(BulletinCloudMonitorItemEnum.DISK.getCode(), disk);

        List<BulletinFieldItemDto> snapSHop = new ArrayList<>();
        snapSHop.add(new BulletinFieldItemDto("name", "快照名称", "name"));
        snapSHop.add(new BulletinFieldItemDto("platformName", "平台名称", "platformName"));
        snapSHop.add(new BulletinFieldItemDto("status", "状态", "status"));
        snapSHop.add(new BulletinFieldItemDto("type", "快照类型", "type"));
        snapSHop.add(new BulletinFieldItemDto("size", "容量", "size"));
        snapSHop.add(new BulletinFieldItemDto("isMemory", "包含内存快照", "isMemory"));
        snapSHop.add(new BulletinFieldItemDto("format", "快照格式", "format"));
        snapSHop.add(new BulletinFieldItemDto("tags", "标签", "tagsStr"));
        snapSHop.add(new BulletinFieldItemDto("volumeType", "云盘类型", "volumeType"));
        snapSHop.add(new BulletinFieldItemDto("installPath", "安装路径", "installPath"));
        res.put(BulletinCloudMonitorItemEnum.SNAPSHOT.getCode(), snapSHop);

        List<BulletinFieldItemDto> networkL2 = new ArrayList<>();
        networkL2.add(new BulletinFieldItemDto("name", "二层网络名称", "name"));
        networkL2.add(new BulletinFieldItemDto("platformName", "平台名称", "platformName"));
        networkL2.add(new BulletinFieldItemDto("physicalInterface", "网卡", "physicalInterface"));
        networkL2.add(new BulletinFieldItemDto("type", "类型", "type"));
        networkL2.add(new BulletinFieldItemDto("uuid", "UUID", "uuid"));
        networkL2.add(new BulletinFieldItemDto("vlan", "VLAN ID/VNI", "vlan"));
        networkL2.add(new BulletinFieldItemDto("virtualNetworkId", "虚拟网络标识", "virtualNetworkId"));
        res.put(BulletinCloudMonitorItemEnum.NETWORK_L2.getCode(), networkL2);

        List<BulletinFieldItemDto> networkL3 = new ArrayList<>();
        networkL3.add(new BulletinFieldItemDto("name", "三层网络名称", "name"));
        networkL3.add(new BulletinFieldItemDto("platformName", "平台名称", "platformName"));
        networkL3.add(new BulletinFieldItemDto("type", "网络类型", "type"));
        networkL3.add(new BulletinFieldItemDto("uuid", "UUID", "uuid"));
        networkL3.add(new BulletinFieldItemDto("networkCidr", "IPv4 CIDR", "networkCidr"));
        networkL3.add(new BulletinFieldItemDto("nextHopIp", "IPv4 DHCP", "nextHopIp"));
        networkL3.add(new BulletinFieldItemDto("dns", "DNS", "dns"));
        networkL3.add(new BulletinFieldItemDto("startIp", "起始IP", "startIp"));
        networkL3.add(new BulletinFieldItemDto("endIp", "结束IP", "endIp"));
        networkL3.add(new BulletinFieldItemDto("gateway", "网关", "gateway"));
        networkL3.add(new BulletinFieldItemDto("networkSegment", "网段名称", "networkSegment"));
        networkL3.add(new BulletinFieldItemDto("l2NetworkName", "二级网络名称", "l2NetworkName"));
        networkL3.add(new BulletinFieldItemDto("networkServices", "网络服务", "networkServices"));
        res.put(BulletinCloudMonitorItemEnum.NETWORK_L3.getCode(), networkL3);

        List<BulletinFieldItemDto> networkVpc = new ArrayList<>();
        networkVpc.add(new BulletinFieldItemDto("name", "vpc名称", "name"));
        networkVpc.add(new BulletinFieldItemDto("platformName", "平台名称", "platformName"));
        networkVpc.add(new BulletinFieldItemDto("state", "启用状态", "state"));
        networkVpc.add(new BulletinFieldItemDto("status", "就绪状态", "status"));
        networkVpc.add(new BulletinFieldItemDto("cpuNum", "CPU", "cpuNum"));
        networkVpc.add(new BulletinFieldItemDto("memorySize", "内存", "memorySize"));
        networkVpc.add(new BulletinFieldItemDto("architecture", "CPU架构", "architecture"));
        networkVpc.add(new BulletinFieldItemDto("ip", "IPv4地址", "ip"));
        networkVpc.add(new BulletinFieldItemDto("managementNetworkIp", "管理IP", "managementNetworkIp"));
        networkVpc.add(new BulletinFieldItemDto("hypervisorType", "虚拟化技术", "hypervisorType"));
        networkVpc.add(new BulletinFieldItemDto("l3NetworkName", "三层网络名称", "l3NetworkName"));
        networkVpc.add(new BulletinFieldItemDto("hostName", "宿主机", "hostName"));
        networkVpc.add(new BulletinFieldItemDto("clusterName", "集群名称", "clusterName"));
        networkVpc.add(new BulletinFieldItemDto("dns", "路由dns", "dns"));
        networkVpc.add(new BulletinFieldItemDto("mac", "MAC地址", "mac"));
        res.put(BulletinCloudMonitorItemEnum.NETWORK_VPC.getCode(), networkVpc);

        List<BulletinFieldItemDto> networkSecgroup = new ArrayList<>();
        networkSecgroup.add(new BulletinFieldItemDto("name", "安全组名称", "name"));
        networkSecgroup.add(new BulletinFieldItemDto("platformName", "平台名称", "platformName"));
        networkSecgroup.add(new BulletinFieldItemDto("isPublic", "是否共享", "isPublic"));
        networkSecgroup.add(new BulletinFieldItemDto("publicScope", "默认共享范围", "publicScope"));
        networkSecgroup.add(new BulletinFieldItemDto("publicSrc", "共享来源", "publicSrc"));
        networkSecgroup.add(new BulletinFieldItemDto("isDirty", "是否垃圾", "isDirty"));
        networkSecgroup.add(new BulletinFieldItemDto("status", "状态", "status"));
        res.put(BulletinCloudMonitorItemEnum.NETWORK_SECGROUP.getCode(), networkSecgroup);

        List<MonitorHierarchy> monitorHierarchy = monitorApi.getAllMonitorHierarchy();
        for (MonitorHierarchy hierarchy : monitorHierarchy) {
            if (hierarchy.getChildren() != null) {
                List<BulletinFieldItemDto> tmpArr = new ArrayList<>();
                tmpArr.add(new BulletinFieldItemDto("monitor_name", "监控名称", "name"));
                tmpArr.add(new BulletinFieldItemDto("monitor_host", "HOST", "host"));
                for (MonitorHierarchy child : hierarchy.getChildren()) { //app
                    if (child.getChildren() != null) {
                        BulletinFieldItemDto item = new BulletinFieldItemDto(child.getValue(), child.getLabel(), child.getValue());
                        List<BulletinFieldItemDto> itemChild = new ArrayList<>();
                        for (MonitorHierarchy child2 : child.getChildren()) { //field
                            itemChild.add(new BulletinFieldItemDto(child2.getValue(), child2.getLabel(), child2.getValue()));
                        }
                        item.setChildren(itemChild);
                        tmpArr.add(item);
                    }
                }
                res.put(hierarchy.getValue(), tmpArr);
            }
        }
        return res;
    }

    public <T> void writeExcel(HttpServletResponse response, String filename, String sheetName,
                               Class<T> head, List<T> data, Set<String> includeFields) throws IOException {
        // 输出 Excel
        EasyExcel.write(response.getOutputStream(), head)
                .autoCloseStream(false) // 不要自动关闭，交给 Servlet 自己处理
                .includeColumnFieldNames(includeFields)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 基于 column 长度，自动适配。最大 255 宽度
                .sheet(sheetName).doWrite(data);
        // 设置 header 和 contentType。写在最后的原因是，避免报错时，响应 contentType 已经被修改了
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
    }


    @Data
    public static class MergeCellData {
        int startRow;
        int endRow;
        int startColumn;
        int endColumn;
        MergeCellData( int startRow, int endRow,int startColumn, int endColumn){
            this.startRow = startRow;
            this.endRow = endRow;
            this.startColumn = startColumn;
            this.endColumn = endColumn;
        }
    }
}
