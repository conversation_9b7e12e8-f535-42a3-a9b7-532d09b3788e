package cn.iocoder.zj.module.monitor.dal.dataobject.topology;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;

import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 监控资源拓扑图 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_topology")
@KeySequence("monitor_topology_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TopologyDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 拓扑图json
     */
    private String topologyJson;
    /**
     * 租户名称
     */
    private String tenantName;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;

    /**
     * @description: 拓扑图名称
     * <AUTHOR>
     * @date 2024/9/3 13:23
     * @version 1.0
     */
    private String topologyName;

    /**
     * @description: 拓扑图图片
     * <AUTHOR>
     * @date 2024/9/3 13:23
     * @version 1.0
     */
    private String topologyImg;


    /**
     * 拓扑图json
     */
    private String resourceJson;
    /**
     * 接口关系json
     */
    private String interfacesJson;

}
