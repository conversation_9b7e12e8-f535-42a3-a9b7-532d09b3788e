package cn.iocoder.zj.module.monitor.dal.redis.zstack;

import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmconfig.AlarmConfigDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmhostrelation.AlarmHostRelationDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.zstack.ZstackLoginInfo;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.zj.module.monitor.dal.redis.RedisKeyConstants.ALARM_CONFIG;
import static cn.iocoder.zj.module.monitor.dal.redis.RedisKeyConstants.ALARM_HOST_RELATION;

@Repository
public class AlarmHostRelationDAO {
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    public void setAlarmHostRelationList(String key, List<AlarmHostRelationDO> alarmInfoList){
        String redisKey = formatKey(key);
        stringRedisTemplate.opsForValue().set(redisKey, JsonUtils.toJsonString(alarmInfoList));
    }
    public void delAlarmInfoHostRelation(String accessToken){
        String redisKey = formatKey(accessToken);
        stringRedisTemplate.delete(redisKey);
    }
    public AlarmHostRelationDO get(String accessToken) {
        String redisKey = formatKey(accessToken);
        return JsonUtils.parseObject(stringRedisTemplate.opsForValue().get(redisKey), AlarmHostRelationDO.class);
    }
    private static String formatKey(String accessToken) {
        return String.format(ALARM_HOST_RELATION, accessToken);
    }
}
