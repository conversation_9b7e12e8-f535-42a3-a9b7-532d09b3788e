package cn.iocoder.zj.module.monitor.dal.mysql.volumesnapshot;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeSnapshotDTO;
import cn.iocoder.zj.module.monitor.controller.admin.volumesnapshot.vo.VolumeSnapshotExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.volumesnapshot.vo.VolumeSnapshotPageReqVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.volumesnapshot.VolumeSnapshotDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import jodd.util.StringUtil;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 云盘快照信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface VolumeSnapshotMapper extends BaseMapperX<VolumeSnapshotDO> {

    default PageResult<VolumeSnapshotDO> selectPage(VolumeSnapshotPageReqVO reqVO, List<Map> platform) {
        List<String> data = new ArrayList<>();
        if (!platform.isEmpty()){
            for (Map map : platform){
                data.add(map.get("platformId").toString());
            }
        }

        LambdaQueryWrapper<VolumeSnapshotDO> queryWrapperX = new LambdaQueryWrapperX<VolumeSnapshotDO>()
                .in(ObjectUtil.isNotEmpty(reqVO.getIds()),VolumeSnapshotDO::getId,reqVO.getIds())
                .eq(ObjectUtil.isNotEmpty(reqVO.getUuid()),VolumeSnapshotDO::getUuid, reqVO.getUuid())
                .eq(ObjectUtil.isNotEmpty(reqVO.getPlatformId()),VolumeSnapshotDO::getPlatformId, reqVO.getPlatformId())
                .like(ObjectUtil.isNotEmpty(reqVO.getName()),VolumeSnapshotDO::getName,reqVO.getName())
                .like(ObjectUtil.isNotEmpty(reqVO.getDescription()),VolumeSnapshotDO::getDescription, reqVO.getDescription())
                .like(ObjectUtil.isNotEmpty(reqVO.getSize()),VolumeSnapshotDO::getSize, reqVO.getSize())
                .eq(ObjectUtil.isNotEmpty(reqVO.getVolumeUuid()),VolumeSnapshotDO::getVolumeUuid, reqVO.getVolumeUuid())
                .like(ObjectUtil.isNotEmpty(reqVO.getVolumeName()),VolumeSnapshotDO::getVolumeName, reqVO.getVolumeName())
                .eq(ObjectUtil.isNotEmpty(reqVO.getHostUuid()),VolumeSnapshotDO::getHostUuid, reqVO.getHostUuid())
                .like(ObjectUtil.isNotEmpty(reqVO.getHostName()),VolumeSnapshotDO::getHostName, reqVO.getHostName())
                .like(ObjectUtil.isNotEmpty(reqVO.getFormat()),VolumeSnapshotDO::getFormat, reqVO.getFormat())
                .eq(ObjectUtil.isNotEmpty(reqVO.getPrimaryStorageUuid()),VolumeSnapshotDO::getPrimaryStorageUuid, reqVO.getPrimaryStorageUuid())
                .eq(ObjectUtil.isNotEmpty(reqVO.getType()),VolumeSnapshotDO::getType, reqVO.getType())
                .eq(ObjectUtil.isNotEmpty(reqVO.getVolumeType()),VolumeSnapshotDO::getVolumeType, reqVO.getVolumeType())
                .eq(ObjectUtil.isNotEmpty(reqVO.getLatest()),VolumeSnapshotDO::getLatest, reqVO.getLatest())
                .eq(ObjectUtil.isNotEmpty(reqVO.getStatus()),VolumeSnapshotDO::getStatus, reqVO.getStatus())
                .eq(ObjectUtil.isNotEmpty(reqVO.getIsMemory()),VolumeSnapshotDO::getIsMemory, reqVO.getIsMemory());
//                .between(ObjectUtil.isNotEmpty(reqVO.getStartTime()) && ObjectUtil.isNotEmpty(reqVO.getEndTime())
//                        ,VolumeSnapshotDO::getCreateTime, reqVO.getStartTime(),reqVO.getEndTime());

        if (jodd.util.StringUtil.isNotEmpty(reqVO.getStartTime()) && jodd.util.StringUtil.isNotEmpty(reqVO.getEndTime())) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            try {
                date = sdf.parse(reqVO.getEndTime());
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(date);
                // 把日期设置为当天最后一秒
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                date = calendar.getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            queryWrapperX.between(VolumeSnapshotDO::getVCreateDate, reqVO.getStartTime(), sdf.format(date));
        }

        if (!data.isEmpty()){
            queryWrapperX.in(VolumeSnapshotDO::getPlatformId,data);
        }else {
            queryWrapperX.eq(VolumeSnapshotDO::getPlatformId,"null");
        }

        if (StringUtil.isNotEmpty(reqVO.getSortDirection())){
            if (reqVO.getSortDirection().equals("asc")){
                if (reqVO.getSortBy().equals("vCreateDate")){
                    queryWrapperX.orderByAsc(VolumeSnapshotDO::getVCreateDate);
                }
                if (reqVO.getSortBy().equals("size")){
                    queryWrapperX.orderByAsc(VolumeSnapshotDO::getSize).orderByAsc(VolumeSnapshotDO::getName);
                }
            }else if (reqVO.getSortDirection().equals("desc")){
                if (reqVO.getSortBy().equals("vCreateDate")){
                    queryWrapperX.orderByDesc(VolumeSnapshotDO::getVCreateDate);
                }
                if (reqVO.getSortBy().equals("size")){
                    queryWrapperX.orderByDesc(VolumeSnapshotDO::getSize).orderByDesc(VolumeSnapshotDO::getName);
                }
            }
        }else {
            queryWrapperX.orderByDesc(VolumeSnapshotDO::getVCreateDate);
        }
        return selectPage(reqVO, queryWrapperX);
    }

    default List<VolumeSnapshotDO> selectList(VolumeSnapshotExportReqVO reqVO, List<Map> platform) {
        List<String> data = new ArrayList<>();
        if (!platform.isEmpty()){
            for (Map map : platform){
                data.add(map.get("platformId").toString());
            }
        }
        LambdaQueryWrapper<VolumeSnapshotDO> queryWrapperX = new LambdaQueryWrapperX<VolumeSnapshotDO>()
                .in(ObjectUtil.isNotEmpty(reqVO.getIds()),VolumeSnapshotDO::getId,reqVO.getIds())
                .eq(ObjectUtil.isNotEmpty(reqVO.getUuid()),VolumeSnapshotDO::getUuid, reqVO.getUuid())
                .eq(ObjectUtil.isNotEmpty(reqVO.getPlatformId()),VolumeSnapshotDO::getPlatformId, reqVO.getPlatformId())
                .like(ObjectUtil.isNotEmpty(reqVO.getName()),VolumeSnapshotDO::getName,reqVO.getQueryData())
                .like(ObjectUtil.isNotEmpty(reqVO.getDescription()),VolumeSnapshotDO::getDescription, reqVO.getDescription())
                .like(ObjectUtil.isNotEmpty(reqVO.getSize()),VolumeSnapshotDO::getSize, reqVO.getSize())
                .eq(ObjectUtil.isNotEmpty(reqVO.getVolumeUuid()),VolumeSnapshotDO::getVolumeUuid, reqVO.getVolumeUuid())
                .like(ObjectUtil.isNotEmpty(reqVO.getVolumeName()),VolumeSnapshotDO::getVolumeName, reqVO.getVolumeName())
                .eq(ObjectUtil.isNotEmpty(reqVO.getHostUuid()),VolumeSnapshotDO::getHostUuid, reqVO.getHostUuid())
                .like(ObjectUtil.isNotEmpty(reqVO.getHostName()),VolumeSnapshotDO::getHostName, reqVO.getHostName())
                .eq(ObjectUtil.isNotEmpty(reqVO.getPrimaryStorageUuid()),VolumeSnapshotDO::getPrimaryStorageUuid, reqVO.getPrimaryStorageUuid())
                .eq(ObjectUtil.isNotEmpty(reqVO.getType()),VolumeSnapshotDO::getType, reqVO.getType())
                .eq(ObjectUtil.isNotEmpty(reqVO.getVolumeType()),VolumeSnapshotDO::getVolumeType, reqVO.getVolumeType())
                .eq(ObjectUtil.isNotEmpty(reqVO.getLatest()),VolumeSnapshotDO::getLatest, reqVO.getLatest())
                .eq(ObjectUtil.isNotEmpty(reqVO.getIsMemory()),VolumeSnapshotDO::getIsMemory, reqVO.getIsMemory())
                .between(ObjectUtil.isNotEmpty(reqVO.getStartTime()) && ObjectUtil.isNotEmpty(reqVO.getEndTime())
                        ,VolumeSnapshotDO::getVCreateDate, reqVO.getStartTime(),reqVO.getEndTime());

        if (!data.isEmpty()){
            queryWrapperX.in(VolumeSnapshotDO::getPlatformId,data);
        }else {
            queryWrapperX.eq(VolumeSnapshotDO::getPlatformId,"null");
        }
        return selectList(queryWrapperX);
    }

    void updateSnapshotBatch(@Param("updateDos") List<VolumeSnapshotDO> updateDos);

    List<VolumeSnapshotDO> getVolumeSnapshotPage(@Param("mpPage") IPage<VolumeSnapshotDO> mpPage, @Param("pageReqVO")VolumeSnapshotPageReqVO pageReqVO);

    void deleteVolumeSnapshotByplatform(@Param("platformId") Long platformId);

    Map<String, Object> getVolumeSnapshotStatusCount(@Param("tenantIds")List<String> tenantIds, @Param("platformId")Long platformId);

    List<VolumeSnapshotDTO> getVolumeSnapshotByPlatformId(@Param("platformId") Long platformId);

    @TenantIgnore
    List<VolumeSnapshotDTO> getsnapByList(@Param("list")List<String> list);

    @TenantIgnore
    List<VolumeSnapshotDO> getVolumeSnapshotByHostUuids(@Param("hostUuids") List<String> hostUuids);
}
