package cn.iocoder.zj.module.monitor.controller.admin.gatherlogdetail.vo;

import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 告警日志 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class GatherLogdetailExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("租户绑定的采集设备id")
    private String uuid;

    @ExcelProperty("trap告警ip")
    private String ip;

    @ExcelProperty("主机名称")
    private String hostName;

    @ExcelProperty("告警位置")
    private String alarmLocation;

    @ExcelProperty("告警时间")
    private Date alarmDate;

    @ExcelProperty("告警详情")
    private String alarmDetail;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private Date createTime;

    @ExcelProperty("租户名称")
    private String tenantName;

}
