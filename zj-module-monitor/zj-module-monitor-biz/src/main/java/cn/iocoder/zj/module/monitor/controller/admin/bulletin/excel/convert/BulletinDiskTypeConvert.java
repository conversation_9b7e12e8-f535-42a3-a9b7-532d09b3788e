package cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.convert;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import lombok.extern.slf4j.Slf4j;

/**
 * Excel 云主机、宿主机状态转换
 */
@Slf4j
public class BulletinDiskTypeConvert implements Converter<Object> {


    @Override
    public Class<?> supportJavaTypeKey() {
        throw new UnsupportedOperationException("暂不支持，也不需要");
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        throw new UnsupportedOperationException("暂不支持，也不需要");
    }

    @Override
    public WriteCellData<String> convertToExcelData(Object object, ExcelContentProperty contentProperty,
                                                    GlobalConfiguration globalConfiguration) {
        // 空时，返回空
        if (object == null) {
            return new WriteCellData<>("");
        }

        String value = String.valueOf(object);
        String convertde = "";
        switch (value) {
            case "Root":
                convertde = "系统盘";
                break;
            case "Data":
                convertde = "数据盘";
                break;

        }

        // 生成 Excel 小表格
        return new WriteCellData<>(convertde);
    }
}