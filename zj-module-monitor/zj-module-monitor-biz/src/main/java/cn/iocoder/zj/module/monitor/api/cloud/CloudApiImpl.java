package cn.iocoder.zj.module.monitor.api.cloud;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.cloud.dto.CloudRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.service.hostinfo.HostInfoService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
public class CloudApiImpl implements CloudInfoApi{

    @Autowired
    HostInfoService hostInfoService;

    @Override
    public CommonResult<List<CloudRespCreateReqDTO>> getCloudByPlatformId(Long platformId) {
        List<CloudRespCreateReqDTO> cloudRespCreateReqDTOS=hostInfoService.getCloudByPlatformId(platformId);
        return CommonResult.success(cloudRespCreateReqDTOS);
    }
}
