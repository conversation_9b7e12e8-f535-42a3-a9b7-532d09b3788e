package cn.iocoder.zj.module.monitor.controller.admin.hardwarestorage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 宿主机与存储关联 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class HardwareStorageBaseVO {

    @Schema(description = "宿主机id")
    private Long hardwareId;

    @Schema(description = "宿主机uuid")
    private String hardwareUuid;

    @Schema(description = "主存储id")
    private Long storageId;

    @Schema(description = "主存储uuid")
    private String storageUuid;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

}
