package cn.iocoder.zj.module.monitor.service.zstack.core;

/**
 * @ClassName : ZstackApiConstant  //类名
 * @Description : API相关接口  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/29  15:53
 */
public class ZstackApiConstant {

    public static final String DEFAULE_DOMAIN = "http://**************:8080";

    public static final String ZSTACK_API_PREFIX = "/zstack/v1";
    public static final String GET_ZSTACK_VM_INSTANCES =ZSTACK_API_PREFIX +  "/vm-instances";

    public static final String GET_ZSTACK_METRICS = ZSTACK_API_PREFIX + "/zwatch/metrics";

    public static final String GET_ZSTACK_LABEL = GET_ZSTACK_METRICS + "/label-values";
}
