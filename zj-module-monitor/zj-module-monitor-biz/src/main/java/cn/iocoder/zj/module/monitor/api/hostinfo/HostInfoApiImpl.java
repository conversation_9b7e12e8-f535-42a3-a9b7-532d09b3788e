package cn.iocoder.zj.module.monitor.api.hostinfo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.dal.manager.SecGroupRuleData;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.secgroup.dto.SecgroupRespDto;
import cn.iocoder.zj.module.monitor.controller.admin.hostinfo.vo.HostInfoRpcVO;
import cn.iocoder.zj.module.monitor.controller.admin.hostinfo.vo.HostInfoUpdateReqVO;
import cn.iocoder.zj.module.monitor.convert.hostinfo.HostInfoConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostinfo.HostInfoDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.secgrouprule.SecgroupRuleDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.volumesnapshot.VolumeSnapshotDO;
import cn.iocoder.zj.module.monitor.dal.mysql.hostinfo.HostInfoMapper;
import cn.iocoder.zj.module.monitor.pojo.AssetReqVO;
import cn.iocoder.zj.module.monitor.service.hostinfo.HostInfoService;
import cn.iocoder.zj.module.monitor.service.secgroup.SecgroupService;
import cn.iocoder.zj.module.monitor.service.secgrouprule.SecgroupRuleService;
import cn.iocoder.zj.module.monitor.service.topology.TopologyService;
import cn.iocoder.zj.module.monitor.service.volumesnapshot.VolumeSnapshotService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

/**
 * @ClassName : HostInfoApiImpl  //类名
 * @Description : 云主机实现类  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/30  15:50
 */

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class HostInfoApiImpl implements HostInfoApi {

    @Resource
    private HostInfoService hostService;
    @Resource
    TopologyService topologyService;
    @Resource
    HostInfoMapper hostInfoMapper;
    @Resource
    private VolumeSnapshotService volumeSnapshotService;
    @Resource
    private SecgroupService secgroupService;
    @Resource
    private SecgroupRuleService secgroupRuleService;

    @Override
    public CommonResult<Boolean> adds(List<HostInfoRespCreateReqDTO> reqDTO) {
        List<HostInfoDO> list = BeanUtil.copyToList(reqDTO, HostInfoDO.class);
        hostService.createHostInfoList(list);
        return CommonResult.success(true);
    }

    @Override
    public int count(String typeName) {
        return hostService.getCount(typeName);
    }

    @Override
    public CommonResult<Boolean> updates(List<HostInfoRespCreateReqDTO> reqDTO) {
        if (reqDTO.size() > 0) {
            List<HostInfoRpcVO> list = HostInfoConvert.INSTANCE.convertCreateListReq(reqDTO);
            hostService.updateHostInfoList(list);
        }
        return CommonResult.success(true);
    }

    public CommonResult<List<HostInfoRespCreateReqDTO>> getAll(String typeName) {
        List<HostInfoRespCreateReqDTO> list = HostInfoConvert.INSTANCE.convertListDoToDto(hostService.getAll(typeName));
        return CommonResult.success(list);
    }

    @Override
    public CommonResult<HostInfoRespCreateReqDTO> getByUuid(String hostUuid) {
        return hostService.getByUuid(hostUuid);
    }

    @Override
    public CommonResult<HostInfoRespCreateReqDTO> getById(Long hostUuid) {
        return hostService.getById(hostUuid);
    }


    @Override
    public CommonResult<Boolean> updateHostSingle(HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO) {
        HostInfoUpdateReqVO hostInfoDO = HostInfoConvert.INSTANCE.convertToReqVO(hostInfoRespCreateReqDTO);
        hostService.updateHostInfoByUuid(hostInfoDO);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> createHostSingle(HostInfoRespCreateReqDTO reqDTO) {
        List<HostInfoRespCreateReqDTO> dtoList = new ArrayList<>();
        dtoList.add(reqDTO);
        List<HostInfoDO> list = HostInfoConvert.INSTANCE.convertCreateList(dtoList);
        hostService.createHostInfoList(list);
        return CommonResult.success(true);
    }

    @Override
    public int deleteHostList(List<HostInfoRespCreateReqDTO> hostInfoRespCreateReqDTOList) {
        List<HostInfoDO> list = HostInfoConvert.INSTANCE.convertCreateList(hostInfoRespCreateReqDTOList);
        return hostService.deleteHostList(list);
    }

    @Override
    public CommonResult<Map<String, List<String>>> getAllDeviceByPlatformId(Collection<Long> platformIds) {
        return CommonResult.success(hostService.getAllDeviceByPlatformId(platformIds));
    }

    @Override
    public CommonResult<List<HostInfoRespCreateReqDTO>> getListAll() {
        List<HostInfoRespCreateReqDTO> list = HostInfoConvert.INSTANCE.convertListDoToDto(hostService.getListAll());
        return CommonResult.success(list);
    }

    @Override
    public void removeDuplicateData() {
        hostService.removeDuplicateData();
    }

    @Override
    public void deleteAssetAndHostJson(String uuid) {
        // 同步删除拓扑图中的信息
        topologyService.deleteAssetAndHostJson(uuid, "host");
    }

    @Override
    public void deleteVm(Long id) {
        hostService.deleteHostInfo(id);
    }

    @Override
    public List<HostInfoRespCreateReqDTO> getVmByPlatformId(Long platformId) {
        return hostInfoMapper.getVmByPlatformId(platformId);
    }

    @Override
    public int selectHostCount() {
        return hostInfoMapper.getHostCount();
    }

    @Override
    public CommonResult<List<HostInfoRespCreateReqDTO>> getHostListByTenantOrPlatforms(AssetReqVO assetReqVO) {
        List<HostInfoRespCreateReqDTO> list = BeanUtil.copyToList(hostService.getHostListByTenantOrPlatforms(assetReqVO), HostInfoRespCreateReqDTO.class);
        if (!CollectionUtils.isEmpty(list)) {
            List<String> uuids = list.stream().map(HostInfoRespCreateReqDTO::getUuid).toList();
            //云主机快照列表
            List<VolumeSnapshotDO> volumeSnapshotDTOList = volumeSnapshotService.getVolumeSnapshotByHostUuids(uuids);
            if (CollectionUtils.isEmpty(volumeSnapshotDTOList)) {
                list.forEach(item -> item.setSnapshotCount(0));
            } else {
                list.forEach(item -> {
                    item.setSnapshotCount((int) volumeSnapshotDTOList.stream()
                            .filter(volumeSnapshotDO -> volumeSnapshotDO.getHostUuid().equals(item.getUuid()))
                            .count());
                });
            }
            //云主机安全组列表
            //筛选掉没有弹性公网的云主机uuids
            List<String> uuidsWithoutEip = list.stream().filter(item -> ObjectUtil.isNotEmpty(item.getVipIp())).map(HostInfoRespCreateReqDTO::getUuid).toList();
            if (!CollectionUtils.isEmpty(uuidsWithoutEip)) {
                List<SecgroupRespDto> secgroupDataList = secgroupService.getSecgroupListByHostUuids(uuidsWithoutEip);
                // 云主机安全组列表
                if (!CollectionUtils.isEmpty(secgroupDataList)) {
                    List<String> secgroupUuids = secgroupDataList.stream().map(SecgroupRespDto::getUuid).toList();
                    //根据安全组uuid查询安全组规则
                    List<SecgroupRuleDO> secgroupRuleDOList = secgroupRuleService.getListBySecgroupUuids(secgroupUuids);
                    List<SecGroupRuleData> secgroupRuleRespDtoList = BeanUtil.copyToList(secgroupRuleDOList, SecGroupRuleData.class);
                    if (!CollectionUtils.isEmpty(secgroupRuleRespDtoList)) {
                        secgroupDataList.forEach(item -> {
                            item.setInRules(secgroupRuleRespDtoList.stream().filter(rule -> rule.getSecgroupUuid().equals(item.getUuid()) && rule.getDirection().equals("in")).toList());
                            item.setOutRules(secgroupRuleRespDtoList.stream().filter(rule -> rule.getSecgroupUuid().equals(item.getUuid()) && rule.getDirection().equals("out")).toList());
                        });
                    }
                }

                if (!CollectionUtils.isEmpty(secgroupDataList)) {
                    list.forEach(item -> {
                        item.setSecgroupDataList(secgroupDataList.stream().filter(secGroup -> secGroup.getHostUuid().equals(item.getUuid())).toList());
                    });
                }
            }
        }
        return CommonResult.success(list);
    }

    @Override
    public CommonResult<List<HostInfoRespCreateReqDTO>> getByPlatformIdAndTags(AssetReqVO assetReqVO) {
        List<HostInfoRespCreateReqDTO> list = BeanUtil.copyToList(hostService.getByPlatformIdAndTags(assetReqVO), HostInfoRespCreateReqDTO.class);
        return CommonResult.success(list);
    }
}


