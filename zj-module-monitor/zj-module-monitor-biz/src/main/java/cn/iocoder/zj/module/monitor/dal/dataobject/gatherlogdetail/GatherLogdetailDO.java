package cn.iocoder.zj.module.monitor.dal.dataobject.gatherlogdetail;

import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 告警日志 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_gather_logdetail")
@KeySequence("monitor_gather_logdetail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GatherLogdetailDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 租户绑定的采集设备id
     */
    private String uuid;
    /**
     * trap告警ip
     */
    private String ip;
    /**
     * 主机名称
     */
    private String hostName;
    /**
     * 告警位置
     */
    private String alarmLocation;
    /**
     * 告警时间
     */
    private Date alarmDate;
    /**
     * 告警详情
     */
    private String alarmDetail;

    private String platformId;

    /**
     * 租户名称
     */
    private String platformName;
    /**
     * @description: 告警状态 0： 未读，1：已读
     * <AUTHOR>
     * @date 2023/7/26 14:48
     * @version 1.0
     */
    private Integer alarmState;
}
