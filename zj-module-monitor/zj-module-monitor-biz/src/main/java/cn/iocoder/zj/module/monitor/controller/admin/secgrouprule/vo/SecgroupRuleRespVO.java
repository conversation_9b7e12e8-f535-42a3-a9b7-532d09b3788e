package cn.iocoder.zj.module.monitor.controller.admin.secgrouprule.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 端口组规则 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SecgroupRuleRespVO extends SecgroupRuleBaseVO {

    @Schema(description = "id", required = true)
    private Long id;

}
