package cn.iocoder.zj.module.monitor.controller.admin.alarmnotice.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 告警与通知模板关系分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AlarmNoticePageReqVO extends PageParam {

    @Schema(description = "告警配置ID")
    private Long alarmConfigId;

    @Schema(description = "通知模板id")
    private Long templateId;

    @Schema(description = "模板类型sms短信，mail邮箱")
    private String templateType;

    @Schema(description = "邮箱地址")
    private String mail;

}
