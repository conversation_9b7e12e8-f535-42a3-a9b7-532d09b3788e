package cn.iocoder.zj.module.monitor.dal.redis.zstack;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.module.monitor.dal.dataobject.zstack.ZstackLoginInfo;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.zj.module.monitor.dal.redis.RedisKeyConstants.*;

/**
 * @ClassName : LabelRedisDAO  //类名
 * @Description : 标签缓存  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/7/26  11:15
 */
@Repository
public class LabelRedisDAO {
    @Resource
    private StringRedisTemplate stringRedisTemplate;


    public void setVm(String LabelKey, List<Map<String, Object>> list) {
        String redisKey = formatVmKey(LabelKey);
        stringRedisTemplate.opsForValue().set(redisKey, JsonUtils.toJsonString(list), 7, TimeUnit.DAYS);
    }

    public List<Map<String, Object>> getVm(String LabelKey) {
        String redisKey = formatVmKey(LabelKey);
        return JsonUtils.parseObject(stringRedisTemplate.opsForValue().get(redisKey), List.class);
    }


    public void setHost(String LabelKey, List<Map<String, Object>> list) {
        String redisKey = formatHostKey(LabelKey);
        stringRedisTemplate.opsForValue().set(redisKey, JsonUtils.toJsonString(list), 7, TimeUnit.DAYS);
    }


    public List<Map<String, Object>> getHost(String LabelKey) {
        String redisKey = formatHostKey(LabelKey);
        return JsonUtils.parseObject(stringRedisTemplate.opsForValue().get(redisKey), List.class);
    }


    private static String formatVmKey(String LabelKey) {
        return String.format(Label_VM, LabelKey);
    }

    private static String formatHostKey(String LabelKey) {
        return String.format(Label_HOST, LabelKey);
    }
}
