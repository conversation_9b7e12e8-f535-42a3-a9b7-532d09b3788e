package cn.iocoder.zj.module.monitor.controller.admin.hardwareinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import javax.validation.constraints.*;

/**
 * 硬件设施基本信息 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class HardwareInfoBaseVO {

    @Schema(description = "主机id")
    private String uuid;

    @Schema(description = "主机名称")
    private String name;

    @Schema(description = "宿主机状态，包括：	Enabled	Disabled	PreMaintenance	Maintenance")
    private String state;

    @Schema(description = "ip")
    private String ip;

    @Schema(description = "Connecting，	Connected，	Disconnected")
    private String status;

    @Schema(description = "集群id")
    private String clusterUuid;

    @Schema(description = "集群名称")
    private String clusterName;

    @Schema(description = "cpu总容量")
    private Long totalCpuCapacity;

    @Schema(description = "cpu可用容量")
    private Long availableCpuCapacity;

    @Schema(description = "cpu 插槽")
    private Integer cpuSockets;

    @Schema(description = "CPU架构")
    private String architecture;

    @Schema(description = "分配cpu")
    private Integer cpuNum;

    @Schema(description = "内存总容量")
    private Long totalMemoryCapacity;

    @Schema(description = "内存可用容量")
    private Long availableMemoryCapacity;

    @Schema(description = "地区id")
    private Long regionId;

    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 带宽上行速率
     */
    @Schema(description = "带宽上行速率")
    private BigDecimal bandwidthUpstream;
    /**
     * 带宽下行速率
     */
    @Schema(description = "带宽下行速率")
    private BigDecimal bandwidthDownstream;
    /**
     * 内存使用率
     */
    @Schema(description = "内存使用率")
    private BigDecimal memoryUsed;
    /**
     * cpu 使用率
     */
    @Schema(description = "cpu 使用率")
    private BigDecimal cpuUsed;
    /**
     * 收包速率
     */
    @Schema(description = "收包速率")
    private BigDecimal packetRate;

    @Schema(description = "硬盘已用容量百分比")
    private BigDecimal diskUsed;
    @Schema(description = "硬盘已使用容量")
    private BigDecimal diskUsedBytes;
    @Schema(description = "硬盘剩余容量")
    private BigDecimal diskFreeBytes;
    @Schema(description = "硬盘总容量")
    private BigDecimal totalDiskCapacity;
    @Schema(description = "平台配置ID")
    private Long platformId;
    @Schema(description = "平台配置名称")
    private String platformName;

    @Schema(description = "平台类型")
    private String typeName;

    @Schema(description = "vmware设备参数")
    private String vms;

    @Schema(description = "IPMI地址")
    private String ipmi;

    @Schema(description = "硬件型号")
    private String manufacturer;

    @Schema(description = "cpu超售比例")
    private BigDecimal cpuOverPercent;

    @Schema(description = "内存超售比例")
    private BigDecimal memoryOverPercent;

    private String totalVirtualMemory;

    @Schema(description = "区域")
    private String manager;

    @Schema(description = "可用区域")
    private String availableManager;

    @Schema(description = "当前cpu超售比例")
    private BigDecimal cpuCommitRate;

    @Schema(description = "cpu类型（描述）")
    private String cpuType;

    @Schema(description = "当前内存超售比例")
    private BigDecimal memoryCommitRate;

    @Schema(description = "系统预留内存")
    private BigDecimal reservedMemory;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "型号")
    private String model;

    @Schema(description = "序列号")
    private String serialNumber;

    @Schema(description = "云主机数量")
    private Integer cloudHostCount;

    @Schema(description = "宿主机数量")
    private Integer hardwareCount;

}
