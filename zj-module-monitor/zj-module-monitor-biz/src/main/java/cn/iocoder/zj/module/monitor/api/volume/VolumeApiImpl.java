package cn.iocoder.zj.module.monitor.api.volume;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.valume.VolumeApi;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeAttachableVmDTO;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeDTO;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeSnapshotDTO;
import cn.iocoder.zj.module.monitor.controller.admin.volumeinfo.vo.VolumeInfoUpdateReqVO;
import cn.iocoder.zj.module.monitor.convert.volumesnapshot.VolumeSnapshotConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.volumeinfo.VolumeInfoDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.volumesnapshot.VolumeSnapshotDO;
import cn.iocoder.zj.module.monitor.pojo.AssetReqVO;
import cn.iocoder.zj.module.monitor.service.volumeinfo.VolumeInfoService;
import cn.iocoder.zj.module.monitor.service.volumesnapshot.VolumeSnapshotService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class VolumeApiImpl implements VolumeApi {
    @Resource
    VolumeInfoService volumeInfoService;
    @Resource
    VolumeSnapshotService volumeSnapshotService;
    //云盘
    @Override
    public CommonResult<Boolean> addVolumes(List<VolumeDTO> reqDTO) {
        List<VolumeInfoDO> insertDos = BeanUtil.copyToList(reqDTO,VolumeInfoDO.class);
        volumeInfoService.addVolumes(insertDos);
        return null;
    }

    @Override
    public CommonResult<Long> getVolumeCount() {
        return CommonResult.success(volumeInfoService.getVolumeCount());
    }

    @Override
    public CommonResult<Boolean> updateVolumes(List<VolumeDTO> volumeDTOList) {
//        List<VolumeInfoDO> updateDos = BeanUtil.copyToList(volumeDTOList,VolumeInfoDO.class);
//        List<VolumeInfoDO> updateDos = VolumeInfoConvert.INSTANCE.DTOConvertToDO(volumeDTOList);
         volumeInfoService.updateVolumes(volumeDTOList);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<List<VolumeDTO>> getAllVolumes(Long id,String typeCode) {
        return CommonResult.success(volumeInfoService.getAll(id,typeCode));
    }


    //云盘快照
    @Override
    public CommonResult<Long> getVolumeSnapshotCount(String typeName) {
        return CommonResult.success(volumeSnapshotService.getVolumeSnapshotCount(typeName));
    }

    @Override
    public CommonResult<List<VolumeSnapshotDTO>> getAllVolumeSnapshots(String typeName) {
        return CommonResult.success(volumeSnapshotService.getAll(typeName));
    }

    @Override
    public CommonResult<Boolean> updateVolumeSnapshots(List<VolumeSnapshotDTO> volumeSnapshotDTOList) {
        List<VolumeSnapshotDO> updateDos = BeanUtil.copyToList(volumeSnapshotDTOList,VolumeSnapshotDO.class);
        volumeSnapshotService.updateSnapshots(updateDos);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> addVolumeSnapshots(List<VolumeSnapshotDTO> volumeSnapshotDTOList) {
        List<VolumeSnapshotDO> insertDos = BeanUtil.copyToList(volumeSnapshotDTOList,VolumeSnapshotDO.class);
        volumeSnapshotService.addSnapshots(insertDos);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> updateSingleVolumes(VolumeDTO volumeDTO) {
        VolumeInfoUpdateReqVO updateReqVO = VolumeSnapshotConvert.INSTANCE.singleDTOConvertToUpdateVo(volumeDTO);
        volumeInfoService.updateVolumeInfo(updateReqVO);
        return null;
    }

    @Override
    public CommonResult<Long> getAttachableVmCount() {
        return CommonResult.success(volumeInfoService.getAttachableVmCount());
    }

    @Override
    public CommonResult<List<VolumeAttachableVmDTO>> getAllVolumeAttachableVm() {
        List<VolumeAttachableVmDTO> resultList = volumeInfoService.getAllVolumeAttachableVm();
        return CommonResult.success(resultList);
    }

    @Override
    public CommonResult<Boolean> addVolumeAttachableVms(List<VolumeAttachableVmDTO> shardingData) {
        volumeInfoService.addVolumeAttachableVms(shardingData);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> updateVolumeAttachableVms(List<VolumeAttachableVmDTO> shardingData) {
        volumeInfoService.updateVolumeAttachableVms(shardingData);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> delVolumeAttachableVms(List<VolumeAttachableVmDTO> deleteTarget) {
        volumeInfoService.delVolumeAttachableVms(deleteTarget);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> delVolumes(List<VolumeDTO> deleteTarget) {
        volumeInfoService.delVolumes(deleteTarget);
        return CommonResult.success(true);
    }

    @Override
    public  CommonResult<Boolean> delVolumeSnapshots(List<VolumeSnapshotDTO> deleteTarget) {
        volumeInfoService.delVolumeSnapshots(deleteTarget);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> delVolumeByInstanceUuid(List<String> deleteList) {
        volumeInfoService.delVolumeByInstanceUuid(deleteList);
        return CommonResult.success(true);
    }

    @Override
    public List<VolumeDTO> getVolumeByPlatformId(Long id) {
        return volumeInfoService.getVolumeByPlatformId(id);
    }

    @Override
    public CommonResult<String> getVolumeByVmUuid(String domainId) {
        return CommonResult.success(volumeInfoService.getVolumeByVmUuid(domainId));
    }

    @Override
    public List<VolumeDTO> getVolumesByPlatformId(Long id) {
        return volumeInfoService.getVolumesByPlatformId(id);
    }

    @Override
    public List<VolumeSnapshotDTO> getVolumeSnapshotByPlatformId(Long id) {
        return volumeInfoService.getVolumeSnapshotByPlatformId(id);
    }

    @Override
    public CommonResult<List<VolumeDTO>> getVolumeByTenantOrPlatforms(AssetReqVO assetReqVO) {
        List<VolumeDTO> resultList = BeanUtil.copyToList(volumeInfoService.getVolumeByTenantOrPlatforms(assetReqVO), VolumeDTO.class);
        return CommonResult.success(resultList);
    }

    @Override
    public CommonResult<List<VolumeDTO>> getByPlatformIdAndTags(AssetReqVO reqVO) {
        List<VolumeDTO> resultList = BeanUtil.copyToList(volumeInfoService.getByPlatformIdAndTags(reqVO), VolumeDTO.class);
        return CommonResult.success(resultList);
    }
}
