package cn.iocoder.zj.module.monitor.util;

import java.util.Map;

public class MetricUnit {
    private static final Map<String, String> UNIT_MAP = Map.ofEntries(
            // 通用指标（百分比）
            Map.entry("CPUUsedUtilization", "(%)"),
            Map.entry("MemoryUsage", "(%)"),
            Map.entry("diskUse", "(%)"),
            Map.entry("DiskUsedUtilization", "(%)"),
            Map.entry("virtuse", "(%)"),
            Map.entry("rate", "(%)"),

            // 容量类（GB）
            Map.entry("memnum", "(GB)"),
            Map.entry("sysdata", "(GB)"),
            Map.entry("data", "(GB)"),
            Map.entry("total", "(GB)"),
            Map.entry("virtnum", "(GB)"),
            Map.entry("rese", "(GB)"),
            Map.entry("waste", "(GB)"),
            Map.entry("size", "(GB)"),
            Map.entry("mem", "(GB)"),
            Map.entry("disk", "(GB)"),
            Map.entry("vmsize", "(GB)"),
            Map.entry("vmactualsize", "(GB)"),
            Map.entry("snapsize", "(GB)"),
            Map.entry("netvpcsize", "(GB)"),

            // 性能指标
            Map.entry("vmiops", "(IOPS)"),
            Map.entry("vmthrou", "(KB/s)"),

            // CPU
            Map.entry("cpunum", "(核)"),
            Map.entry("cpuSockets", "(个)"),
            Map.entry("cpuVirtual", "(核)"),
            Map.entry("cpuAssigned", "(核)")
    );

    /**
     * 给数值追加单位（如存在）
     * @param metricCode 指标编码
     * @param value 原始数值（如 "23.7"）
     * @return 追加单位后的文本（如 "23.7 (%)"）
     */
    public static String appendUnit(String metricCode, String value) {
        String unit = UNIT_MAP.getOrDefault(metricCode, "");
        return unit.isEmpty() ? value : value + " " + unit;
    }

    /**
     * 获取指定指标的单位（用于表头显示或图例）
     * @param metricCode 指标编码
     * @return 单位（如 "(%)"，无则返回空串）
     */
    public static String getUnit(String metricCode) {
        return UNIT_MAP.getOrDefault(metricCode, "");
    }
}
