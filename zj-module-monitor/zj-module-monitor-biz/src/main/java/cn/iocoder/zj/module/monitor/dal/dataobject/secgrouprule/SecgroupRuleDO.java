package cn.iocoder.zj.module.monitor.dal.dataobject.secgrouprule;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 端口组规则 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_secgroup_rule")
@KeySequence("monitor_secgroup_rule_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SecgroupRuleDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * uuid
     */
    private String uuid;
    /**
     * 状态
     */
    private String status;
    /**
     * 安全组uuid
     */
    private String secgroupUuid;
    /**
     * 优先级
     */
    private Integer priority;
    /**
     * 协议
     */
    private String protocol;
    /**
     * 端口
     */
    private String ports;
    /**
     * 方向
     */
    private String direction;
    /**
     * cidr
     */
    private String cidr;
    /**
     * 策略
     */
    private String action;
    /**
     * 备注
     */
    private String description;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;

}
