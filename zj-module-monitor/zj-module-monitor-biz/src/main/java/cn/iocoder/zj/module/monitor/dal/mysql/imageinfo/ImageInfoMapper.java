package cn.iocoder.zj.module.monitor.dal.mysql.imageinfo;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.api.imageinfo.dto.ImageInfoCreateReqDTO;
import cn.iocoder.zj.module.monitor.controller.admin.imageinfo.vo.ImageInfoExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.imageinfo.vo.ImageInfoPageReqVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.imageinfo.ImageInfoDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jodd.util.StringUtil;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 镜像信息 Mapper
 *
 *
 * <AUTHOR>
 */
@Mapper
public interface ImageInfoMapper extends BaseMapperX<ImageInfoDO> {

    default PageResult<ImageInfoDO> selectPage(ImageInfoPageReqVO reqVO,List<Map> platform) {
        List<String> data = new ArrayList<>();
        if (!platform.isEmpty()){
            for (Map map : platform){
                data.add(map.get("platformId").toString());
            }
        }
        LambdaQueryWrapper<ImageInfoDO> queryWrapperX = new LambdaQueryWrapperX<ImageInfoDO>()
                .in(ObjectUtil.isNotEmpty(reqVO.getIds()), ImageInfoDO::getId, reqVO.getIds())
                .like(ObjectUtil.isNotEmpty(reqVO.getName()),ImageInfoDO::getName, reqVO.getName())
                .eq(ObjectUtil.isNotEmpty(reqVO.getStatus()),ImageInfoDO::getStatus, reqVO.getStatus())
                .like(ObjectUtil.isNotEmpty(reqVO.getFormat()),ImageInfoDO::getFormat, reqVO.getFormat())
                .like(ObjectUtil.isNotEmpty(reqVO.getCpuArch()),ImageInfoDO::getCpuArch, reqVO.getCpuArch())
                .like(ObjectUtil.isNotEmpty(reqVO.getOsType()),ImageInfoDO::getOsType, reqVO.getOsType())
                .eq(ObjectUtil.isNotEmpty(reqVO.getSize()),ImageInfoDO::getSize, reqVO.getSize())
                .eq(ObjectUtil.isNotEmpty(reqVO.getImageType()),ImageInfoDO::getImageType, reqVO.getImageType())
                .like(ObjectUtil.isNotEmpty(reqVO.getSharingScope()),ImageInfoDO::getSharingScope, reqVO.getSharingScope())
                .eq(ObjectUtil.isNotEmpty(reqVO.getUuid()),ImageInfoDO::getUuid, reqVO.getUuid())
                .like(ObjectUtil.isNotEmpty(reqVO.getTag()),ImageInfoDO::getTag, reqVO.getTag())
                .eq(ObjectUtil.isNotEmpty(reqVO.getHostUuid()),ImageInfoDO::getHostUuid, reqVO.getHostUuid())
                .like(ObjectUtil.isNotEmpty(reqVO.getOsLanguage()),ImageInfoDO::getOsLanguage, reqVO.getOsLanguage())
                .eq(ObjectUtil.isNotEmpty(reqVO.getMinMemory()),ImageInfoDO::getMinMemory, reqVO.getMinMemory())
                .eq(ObjectUtil.isNotEmpty(reqVO.getMinDisk()),ImageInfoDO::getMinDisk, reqVO.getMinDisk())
                .like(ObjectUtil.isNotEmpty(reqVO.getDiskDriver()),ImageInfoDO::getDiskDriver, reqVO.getDiskDriver())
                .like(ObjectUtil.isNotEmpty(reqVO.getNetworkDriver()),ImageInfoDO::getNetworkDriver, reqVO.getNetworkDriver())
                .like(ObjectUtil.isNotEmpty(reqVO.getBootMode()),ImageInfoDO::getBootMode, reqVO.getBootMode())
                .like(ObjectUtil.isNotEmpty(reqVO.getRemoteProtocol()),ImageInfoDO::getRemoteProtocol, reqVO.getRemoteProtocol())
                .like(ObjectUtil.isNotEmpty(reqVO.getApplicationPlatform()),ImageInfoDO::getApplicationPlatform, reqVO.getApplicationPlatform())
                .eq(ObjectUtil.isNotEmpty(reqVO.getPlatformId()),ImageInfoDO::getPlatformId, reqVO.getPlatformId())
                .like(ObjectUtil.isNotEmpty(reqVO.getPlatformName()),ImageInfoDO::getPlatformName, reqVO.getPlatformName())
                .between(ObjectUtil.isNotEmpty(reqVO.getStartTime()) && ObjectUtil.isNotEmpty(reqVO.getEndTime())
                        ,ImageInfoDO::getVCreateDate, reqVO.getStartTime(),reqVO.getEndTime());

        if (!data.isEmpty()){
            queryWrapperX.in(ImageInfoDO::getPlatformId,data);
        }else {
            queryWrapperX.eq(ImageInfoDO::getPlatformId,"null");
        }

        if (StringUtil.isNotEmpty(reqVO.getSortDirection())){
            if (reqVO.getSortDirection().equals("asc")){
                if (reqVO.getSortBy().equals("vCreateDate")){
                    queryWrapperX.orderByAsc(ImageInfoDO::getVCreateDate);
                }
                if (reqVO.getSortBy().equals("size")){
                    queryWrapperX.orderByAsc(ImageInfoDO::getSize).orderByAsc(ImageInfoDO::getName);
                }
            }else if (reqVO.getSortDirection().equals("desc")){
                if (reqVO.getSortBy().equals("vCreateDate")){
                    queryWrapperX.orderByDesc(ImageInfoDO::getVCreateDate);
                }
                if (reqVO.getSortBy().equals("size")){
                    queryWrapperX.orderByDesc(ImageInfoDO::getSize).orderByDesc(ImageInfoDO::getName);
                }
            }
        }else {
            queryWrapperX.orderByDesc(ImageInfoDO::getVCreateDate);
        }
        return selectPage(reqVO, queryWrapperX);
    }

    default List<ImageInfoDO> selectList(ImageInfoExportReqVO reqVO,List<Map> platform) {
        List<String> data = new ArrayList<>();
        if (platform.size()>0){
            for (Map map : platform){
                data.add(map.get("platformId").toString());
            }
        }
        LambdaQueryWrapper<ImageInfoDO> queryWrapperX = new LambdaQueryWrapperX<ImageInfoDO>()
                .in(ObjectUtil.isNotEmpty(reqVO.getIds()), ImageInfoDO::getId, reqVO.getIds())
                .like(ObjectUtil.isNotEmpty(reqVO.getName()),ImageInfoDO::getName, reqVO.getName())
                .eq(ObjectUtil.isNotEmpty(reqVO.getStatus()),ImageInfoDO::getStatus, reqVO.getStatus())
                .like(ObjectUtil.isNotEmpty(reqVO.getFormat()),ImageInfoDO::getFormat, reqVO.getFormat())
                .like(ObjectUtil.isNotEmpty(reqVO.getCpuArch()),ImageInfoDO::getCpuArch, reqVO.getCpuArch())
                .like(ObjectUtil.isNotEmpty(reqVO.getOsType()),ImageInfoDO::getOsType, reqVO.getOsType())
                .eq(ObjectUtil.isNotEmpty(reqVO.getSize()),ImageInfoDO::getSize, reqVO.getSize())
                .like(ObjectUtil.isNotEmpty(reqVO.getImageType()),ImageInfoDO::getImageType, reqVO.getImageType())
                .like(ObjectUtil.isNotEmpty(reqVO.getSharingScope()),ImageInfoDO::getSharingScope, reqVO.getSharingScope())
                .eq(ObjectUtil.isNotEmpty(reqVO.getUuid()),ImageInfoDO::getUuid, reqVO.getUuid())
                .like(ObjectUtil.isNotEmpty(reqVO.getTag()),ImageInfoDO::getTag, reqVO.getTag())
                .eq(ObjectUtil.isNotEmpty(reqVO.getHostUuid()),ImageInfoDO::getHostUuid, reqVO.getHostUuid())
                .like(ObjectUtil.isNotEmpty(reqVO.getOsLanguage()),ImageInfoDO::getOsLanguage, reqVO.getOsLanguage())
                .eq(ObjectUtil.isNotEmpty(reqVO.getMinMemory()),ImageInfoDO::getMinMemory, reqVO.getMinMemory())
                .eq(ObjectUtil.isNotEmpty(reqVO.getMinDisk()),ImageInfoDO::getMinDisk, reqVO.getMinDisk())
                .like(ObjectUtil.isNotEmpty(reqVO.getDiskDriver()),ImageInfoDO::getDiskDriver, reqVO.getDiskDriver())
                .like(ObjectUtil.isNotEmpty(reqVO.getNetworkDriver()),ImageInfoDO::getNetworkDriver, reqVO.getNetworkDriver())
                .like(ObjectUtil.isNotEmpty(reqVO.getBootMode()),ImageInfoDO::getBootMode, reqVO.getBootMode())
                .like(ObjectUtil.isNotEmpty(reqVO.getRemoteProtocol()),ImageInfoDO::getRemoteProtocol, reqVO.getRemoteProtocol())
                .like(ObjectUtil.isNotEmpty(reqVO.getApplicationPlatform()),ImageInfoDO::getApplicationPlatform, reqVO.getApplicationPlatform())
                .eq(ObjectUtil.isNotEmpty(reqVO.getPlatformId()),ImageInfoDO::getPlatformId, reqVO.getPlatformId())
                .like(ObjectUtil.isNotEmpty(reqVO.getPlatformName()),ImageInfoDO::getPlatformName, reqVO.getPlatformName())
                .between(ObjectUtil.isNotEmpty(reqVO.getStartTime()) && ObjectUtil.isNotEmpty(reqVO.getEndTime())
                        ,ImageInfoDO::getVCreateDate, reqVO.getStartTime(),reqVO.getEndTime())
                .orderByDesc(ImageInfoDO::getVCreateDate);

        if (!data.isEmpty()){
            queryWrapperX.in(ImageInfoDO::getPlatformId,data);
        }else {
            queryWrapperX.eq(ImageInfoDO::getPlatformId,"null");
        }

        return selectList(queryWrapperX);
    }

    default List<ImageInfoDO> getImageInfosByPlatformId(Long platformId) {
        return selectList(new LambdaQueryWrapperX<ImageInfoDO>()
               .eqIfPresent(ImageInfoDO::getPlatformId, platformId));
    }


    List<ImageInfoDO> getAllImagesByTypeCode(@Param("typeCode")String typeCode);

    void batchUpdateImageInfo(@Param("ImageInfoDOs")List<ImageInfoDO> ImageInfoDOs);

    Map<String, Object> getImageStatusCount(@Param("tenantIds") List<String> tenantIds,@Param("platformId") Long platformId);

    @TenantIgnore
    List<ImageInfoDO> getImageByList(@Param("list") List<String> list);
}
