package cn.iocoder.zj.module.monitor.util;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.zj.framework.common.dal.manager.TagData;
import cn.iocoder.zj.module.monitor.dal.dataobject.tags.TagsDO;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class TagDataDiffUtil {
    public static TagDataDiffResult compare(List<TagData> result, List<TagsDO> allList) {
        if (CollUtil.isEmpty(result) || CollUtil.isEmpty(allList)) {
            return new TagDataDiffResult(Collections.emptyMap(), Collections.emptyMap(), Collections.emptyMap());
        }
        // 分组
        Map<String, List<TagData>> tagMap = result.stream().collect(Collectors.groupingBy(TagData::getTagUuid));
        Map<String, List<TagsDO>> allMap = allList.stream().collect(Collectors.groupingBy(TagsDO::getTagUuid));

        Set<String> resultKeys = tagMap.keySet();
        Set<String> allKeys = allMap.keySet();

        Set<String> addedKeys = new HashSet<>(resultKeys);
        addedKeys.removeAll(allKeys);

        TagData sample = result.get(0);
        Set<String> filteredKeys = allList.stream().filter(item -> Objects.equals(item.getTaggableType(), sample.getType()) && Objects.equals(item.getPlatformId(), sample.getPlatformId())).map(TagsDO::getTagUuid).collect(Collectors.toSet());

        Map<String, List<TagsDO>> deletedMap = Collections.emptyMap();
        if (CollUtil.isNotEmpty(filteredKeys)) {
            Set<String> commonKeys = new HashSet<>(resultKeys);
            commonKeys.retainAll(filteredKeys);

            if (tagMap.size() > filteredKeys.size()) {
                Set<String> extraAddKeys = new HashSet<>(resultKeys);
                extraAddKeys.removeAll(filteredKeys);
                addedKeys.addAll(extraAddKeys);
            } else if (tagMap.size() < filteredKeys.size()) {
                Set<String> extraDeleteKeys = new HashSet<>(filteredKeys);
                extraDeleteKeys.removeAll(resultKeys);
                deletedMap = extraDeleteKeys.stream().collect(Collectors.toMap(Function.identity(), allMap::get));
            }

            Map<String, List<TagData>> commonMap = commonKeys.stream().collect(Collectors.toMap(Function.identity(), tagMap::get));
            Map<String, List<TagData>> addedMap = addedKeys.stream().collect(Collectors.toMap(Function.identity(), tagMap::get));
            return new TagDataDiffResult(addedMap, deletedMap, commonMap);
        }else {
            addedKeys.addAll(resultKeys);
        }

        Map<String, List<TagData>> addedMap = addedKeys.stream().collect(Collectors.toMap(Function.identity(), tagMap::get));
        return new TagDataDiffResult(addedMap, Collections.emptyMap(), Collections.emptyMap());
    }


    @Data
    @AllArgsConstructor
    public static class TagDataDiffResult {
        private Map<String, List<TagData>> added;
        private Map<String, List<TagsDO>> deleted;
        private Map<String, List<TagData>> common;
    }
}
