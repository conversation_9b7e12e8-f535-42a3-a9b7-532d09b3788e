package cn.iocoder.zj.module.monitor.controller.admin.hostnic.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 云主机网络 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class HostNicBaseVO {

    @Schema(description = "uuid", required = true)
    @NotNull(message = "uuid不能为空")
    private String uuid;

    @Schema(description = "网卡名称", required = true)
    private String name;

    @Schema(description = "云主机uuid", required = true)
    @NotNull(message = "云主机uuid不能为空")
    private String hostUuid;

    @Schema(description = "ipV6")
    private String ip6;

    @Schema(description = "ip", required = true)
    @NotNull(message = "ip不能为空")
    private String ip;

    @Schema(description = "mac", required = true)
    @NotNull(message = "mac不能为空")
    private String mac;

    @Schema(description = "驱动", required = true)
    @NotNull(message = "驱动不能为空")
    private String driver;

    @Schema(description = "在经典网络", required = true)
    @NotNull(message = "在经典网络不能为空")
    private Byte inClassicNetwork;

    @Schema(description = "网络uuid", required = true)
    @NotNull(message = "网络uuid不能为空")
    private String networkUuid;

    @Schema(description = "网络名称", required = true)
    private String networkName;

    @Schema(description = "队列数量", required = true)
    private String numQueues;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

}
