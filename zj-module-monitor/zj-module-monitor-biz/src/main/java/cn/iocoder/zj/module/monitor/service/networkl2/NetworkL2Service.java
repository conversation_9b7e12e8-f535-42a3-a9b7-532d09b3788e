package cn.iocoder.zj.module.monitor.service.networkl2;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL2DTO;
import cn.iocoder.zj.module.monitor.controller.admin.networkl2.vo.NetworkL2CreateReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.networkl2.vo.NetworkL2ExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.networkl2.vo.NetworkL2PageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.networkl2.vo.NetworkL2UpdateReqVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkl2.NetworkL2DO;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkl3.NetworkL3DO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 二级网络信息 Service 接口
 *
 * <AUTHOR>
 */
public interface NetworkL2Service {

    /**
     * 创建二级网络信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createNetworkL2(@Valid NetworkL2CreateReqVO createReqVO);

    /**
     * 更新二级网络信息
     *
     * @param updateReqVO 更新信息
     */
    void updateNetworkL2(@Valid NetworkL2UpdateReqVO updateReqVO);

    /**
     * 删除二级网络信息
     *
     * @param id 编号
     */
    void deleteNetworkL2(Long id);

    /**
     * 获得二级网络信息
     *
     * @param id 编号
     * @return 二级网络信息
     */
    NetworkL2DO getNetworkL2(Long id,String uuid);

    /**
     * 获得二级网络信息列表
     *
     * @param ids 编号
     * @return 二级网络信息列表
     */
    List<NetworkL2DO> getNetworkL2List(Collection<Long> ids);

    /**
     * 获得二级网络信息分页
     *
     * @param pageReqVO 分页查询
     * @return 二级网络信息分页
     */
    PageResult<NetworkL2DO> getNetworkL2Page(NetworkL2PageReqVO pageReqVO);

    /**
     * 获得二级网络信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 二级网络信息列表
     */
    List<NetworkL2DO> getNetworkL2List(NetworkL2ExportReqVO exportReqVO);

    void createNetworkL2List(List<NetworkL2DO> list);

    Long getNetWorkL2Count(String typeName);

    void updateNetWorkL2List(List<NetWorkL2DTO> list);

    List<NetworkL2DO> getNetWorkL2List(String typeName);

    List<Map> getNetWorkL2ByNameList();

    int deleteNetWorkL2ByNameList(List<NetworkL2DO> list);

    int deleteNetWorkL3ByNameList(List<NetworkL3DO> list);

    void deleteNetworkL2Byplatform(Long platformId);

    List<NetWorkL2DTO> getNetworkL2ByPlatformId(Long platformId);
}
