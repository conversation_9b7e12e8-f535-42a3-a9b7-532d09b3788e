package cn.iocoder.zj.module.monitor.dal.dataobject.hostinfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 云主机基本信息 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_host_info")
@KeySequence("monitor_host_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HostInfoDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 主机id
     */
    private String uuid;
    /**
     * 主机名称
     */
    private String name;
    /**
     * 主机状态
     */
    private String state;
    /**
     * ip
     */
    private String ip;
    /**
     * 弹性ip
     */
    private String vipIp;
    /**
     * 区域id
     */
    private String zoneUuid;
    /**
     * 区域名称
     */
    private String zoneName;
    /**
     * 集群id
     */
    private String clusterUuid;

    private String imageUuid;
    /**
     * 集群名称
     */
    private String clusterName;
    /**
     * 宿主机id
     */
    private String hardwareUuid;
    /**
     * 宿主机名称
     */
    private String hardwareName;
    /**
     * CPU架构
     */
    private String architecture;
    /**
     * 操作系统类型
     */
    private String guestOsType;
    /**
     * 主机创建时间
     */
    private Date vCreateDate;
    /**
     * 主机更新时间
     */
    private Date vUpdateDate;
    /**
     * 主机类型
     */
    private String type;
    /**
     * 分配内存
     */
    private Long memorySize;
    /**
     * 分配cpu
     */
    private Integer cpuNum;
    /**
     * mac 地址
     */
    private String mac;

    /**
     * 硬盘使用率
     */
    private BigDecimal diskUsed;

    /**
     * cpu 使用率
     */
    private BigDecimal cpuUsed;

    /**
     * 内存使用率
     */
    private BigDecimal memoryUsed;


    private Long tenantId;

    private Long regionId;


    private BigDecimal networkInBytes;

    private BigDecimal networkOutBytes;

    private BigDecimal diskUsedBytes;

    private BigDecimal diskFreeBytes;

    private BigDecimal totalDiskCapacity;

    private BigDecimal actualSize;

    private BigDecimal cloudSize;

    private BigDecimal networkInPackets;

    private BigDecimal networkOutPackets;

    // 平台配置id
    private Long platformId;
    // 平台配置名称
    private String platformName;

    private  String typeName;
    private  String vms;

    /**
     * 授权有效时间
     */
    private LocalDateTime authorizationTime;
    /**
     * 授权状态
     */
    private String authorizationType;

    /**
     * 电源状态
     */
    private String powerState;

    private String iso;

    private String autoInitType;

    private String guideMode;

    private String imageName;

    @TableField(exist = false)
    private BigDecimal systemDiskSize;

    @TableField(exist = false)
    private BigDecimal dataDiskSize;

    private String tag;

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("name", this.name);
        map.put("uuid", this.uuid);
        map.put("platformName", this.platformName);
        map.put("platformId",this.platformId);
        map.put("typeName",this.typeName);
        return map;
    }
}
