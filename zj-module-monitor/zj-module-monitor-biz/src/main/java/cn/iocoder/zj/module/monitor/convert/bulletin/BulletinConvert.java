package cn.iocoder.zj.module.monitor.convert.bulletin;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.bulletin.BulletinDO;

/**
 * 实时报 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface BulletinConvert {

    BulletinConvert INSTANCE = Mappers.getMapper(BulletinConvert.class);

    BulletinDO convert(BulletinCreateReqVO bean);

    BulletinDO convert(BulletinUpdateReqVO bean);

    BulletinRespVO convert(BulletinDO bean);

    List<BulletinRespVO> convertList(List<BulletinDO> list);

    PageResult<BulletinRespVO> convertPage(PageResult<BulletinDO> page);

    List<BulletinExcelVO> convertList02(List<BulletinDO> list);

}
