package cn.iocoder.zj.module.monitor.service.taggables;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.monitor.controller.admin.taggables.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.taggables.TaggablesDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 标签绑定关系 Service 接口
 *
 * <AUTHOR>
 */
public interface TaggablesService {

    /**
     * 创建标签绑定关系
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTaggables(@Valid TaggablesCreateReqVO createReqVO);

    /**
     * 更新标签绑定关系
     *
     * @param updateReqVO 更新信息
     */
    void updateTaggables(@Valid TaggablesUpdateReqVO updateReqVO);

    /**
     * 删除标签绑定关系
     *
     * @param id 编号
     */
    void deleteTaggables(Long id);

    /**
     * 获得标签绑定关系
     *
     * @param id 编号
     * @return 标签绑定关系
     */
    TaggablesDO getTaggables(Long id);

    /**
     * 获得标签绑定关系列表
     *
     * @param ids 编号
     * @return 标签绑定关系列表
     */
    List<TaggablesDO> getTaggablesList(Collection<Long> ids);

    /**
     * 获得标签绑定关系分页
     *
     * @param pageReqVO 分页查询
     * @return 标签绑定关系分页
     */
    PageResult<TaggablesDO> getTaggablesPage(TaggablesPageReqVO pageReqVO);

    /**
     * 获得标签绑定关系列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 标签绑定关系列表
     */
    List<TaggablesDO> getTaggablesList(TaggablesExportReqVO exportReqVO);


    Long getTaggableCount(TaggablesExportReqVO exportReqVO);

    void deleteByTagId(Long tagId);

    Boolean batchCreateTaggables(TaggablesBatchCreateReqVO createReqVO);

    void add(Long id, String type, String monitorTags);

    void del(Long id);
}
