package cn.iocoder.zj.module.monitor.controller.admin.secgrouprule.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 端口组规则 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SecgroupRuleExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("uuid")
    private String uuid;

    @ExcelProperty("状态")
    private String status;

    @ExcelProperty("安全组uuid")
    private String secgroupUuid;

    @ExcelProperty("优先级")
    private Integer priority;

    @ExcelProperty("协议")
    private String protocol;

    @ExcelProperty("端口")
    private String ports;

    @ExcelProperty("方向")
    private String direction;

    @ExcelProperty("cidr")
    private String cidr;

    @ExcelProperty("策略")
    private String action;

    @ExcelProperty("备注")
    private String description;

    @ExcelProperty("平台id")
    private Long platformId;

    @ExcelProperty("平台名称")
    private String platformName;

}
