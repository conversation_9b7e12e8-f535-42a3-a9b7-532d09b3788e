package cn.iocoder.zj.module.monitor.service.hardwareinfo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.controller.admin.hardwareinfo.vo.*;
import cn.iocoder.zj.module.monitor.convert.hardwareinfo.HardwareInfoConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.hardwareinfo.HardwareInfoDO;
import cn.iocoder.zj.module.monitor.dal.mysql.hardwareinfo.HardwareInfoMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.hostinfo.HostInfoMapper;
import cn.iocoder.zj.module.monitor.dal.redis.zstack.LabelRedisDAO;
import cn.iocoder.zj.module.monitor.dal.redis.zstack.ZstackAccessTokenRedisDAO;
import cn.iocoder.zj.module.monitor.handler.HttpClientWrapper;
import cn.iocoder.zj.module.monitor.pojo.AssetReqVO;
import cn.iocoder.zj.module.monitor.util.StringUtil;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.googlecode.aviator.utils.ArrayHashMap;
import lombok.extern.slf4j.Slf4j;
import org.frameworkset.elasticsearch.ElasticSearchHelper;
import org.frameworkset.elasticsearch.client.ClientInterface;
import org.frameworkset.elasticsearch.entity.ESDatas;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.singleton;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.HARDWARE_INFO_NOT_EXISTS;

/**
 * 硬件设施基本信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class HardwareInfoServiceImpl implements HardwareInfoService {
    @Resource
    ZstackAccessTokenRedisDAO zstackAccessTokenRedisDAO;

    @Resource
    private HardwareInfoMapper hardwareInfoMapper;


    @Resource
    HostInfoMapper hostInfoMapper;
    @Resource
    private RoleApi roleApi;

    @Resource
    private PermissionApi permissionApi;
    @Resource
    LabelRedisDAO labelRedisDAO;
    @Resource
    PlatformconfigApi platformconfigApi;

    @Resource
    private AdminUserApi adminUserService;

    @Autowired
    private HttpClientWrapper httpClientWrapper;

    @Override
    public Long createHardwareInfo(HardwareInfoCreateReqVO createReqVO) {
        // 插入
        HardwareInfoDO hardwareInfo = HardwareInfoConvert.INSTANCE.convert(createReqVO);
        hardwareInfoMapper.insert(hardwareInfo);
        // 返回
        return hardwareInfo.getId();
    }

    @Override
    public void updateHardwareInfo(HardwareInfoUpdateReqVO updateReqVO) {
        // 校验存在
        validateHardwareInfoExists(updateReqVO.getId());
        // 更新
        HardwareInfoDO updateObj = HardwareInfoConvert.INSTANCE.convert(updateReqVO);
        hardwareInfoMapper.updateById(updateObj);
    }

    @Override
    public void deleteHardwareInfo(Long id) {
        // 校验存在
        validateHardwareInfoExists(id);
        // 删除
        hardwareInfoMapper.deleteById(id);
    }

    private void validateHardwareInfoExists(Long id) {
        if (hardwareInfoMapper.selectById(id) == null) {
            throw exception(HARDWARE_INFO_NOT_EXISTS);
        }
    }

    @Override
    public HardwareInfoDO getHardwareInfo(String uuid) {
        HardwareInfoDO hardwareInfoDO = hardwareInfoMapper.selectOne("uuid", uuid);
        if (hardwareInfoDO.getTypeName().equals("vmware") && hardwareInfoDO.getIsMaintain() == 1) {
            hardwareInfoDO.setState("Maintenance");
        }
        return hardwareInfoDO;
    }

    @Override
    public List<HardwareInfoDO> getHardwareInfoList(Collection<Long> ids) {
        List<HardwareInfoDO> hardwareInfoDOList = hardwareInfoMapper.selectBatchIds(ids);
        for (HardwareInfoDO item : hardwareInfoDOList) {
            if (item.getTypeName().equals("vmware") && item.getIsMaintain() == 1) {
                item.setState("Maintenance");
            }
        }
        return hardwareInfoMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<HardwareInfoDO> getHardwareInfoPage(HardwareInfoPageReqVO pageReqVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        AdminUserRespDTO adminUserRespDTO = adminUserService.getUser(loginUser.getId()).getData();
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            if (StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
                pageReqVO.setTenantId(Arrays.asList(String.valueOf(adminUserRespDTO.getTenantId()).split(",")));
            } else {
                pageReqVO.setTenantId(Arrays.asList(adminUserRespDTO.getServiceTenantId().split(",")));
            }
        }
        PageParam pageParam = new PageParam().setPageNo(pageReqVO.getPageNo()).setPageSize(pageReqVO.getPageSize());
        IPage<HardwareInfoDO> mpPage = MyBatisUtils.buildPage(pageParam);
        List<String> uids = new ArrayList<>();
        if (StringUtil.isNotEmpty(pageReqVO.getIds())) {
            uids = Arrays.asList(pageReqVO.getIds().split(","));
        }
        if (jodd.util.StringUtil.isNotEmpty(pageReqVO.getStartTime()) && jodd.util.StringUtil.isNotEmpty(pageReqVO.getEndTime())) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            try {
                date = sdf.parse(pageReqVO.getEndTime());
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(date);
                // 把日期设置为当天最后一秒
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                date = calendar.getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            pageReqVO.setEndTime(sdf.format(date));
        }
        pageReqVO.setIsMaintain(0);
        if (StringUtil.isNotEmpty(pageReqVO.getState()) && pageReqVO.getState().equals("Maintenance")) {
            pageReqVO.setIsMaintain(1);
        }
        if (StringUtil.isNotEmpty(pageReqVO.getSortBy())) {
            String regex = "([a-z])([A-Z]+)";
            String replacement = "$1_$2";
            String output = pageReqVO.getSortBy().replaceAll(regex, replacement).toLowerCase();
            pageReqVO.setSortBy(output);
        }

        List<HardwareInfoDO> hardwareInfoDOList = hardwareInfoMapper.getHardwareInfoPage(mpPage, pageReqVO, uids);
        for (HardwareInfoDO item : hardwareInfoDOList) {
            if (item.getTypeName().equals("vmware") && item.getIsMaintain() == 1) {
                item.setState("Maintenance");
            }
        }
        return new PageResult<>(hardwareInfoDOList, mpPage.getTotal());

    }

    @Override
    public List<HardwareInfoDO> getHardwareInfoList(HardwareInfoExportReqVO exportReqVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        AdminUserRespDTO adminUserRespDTO = adminUserService.getUser(loginUser.getId()).getData();
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            if (StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
                exportReqVO.setTenantId(Arrays.asList(String.valueOf(adminUserRespDTO.getTenantId()).split(",")));
            } else {
                exportReqVO.setTenantId(Arrays.asList(adminUserRespDTO.getServiceTenantId().split(",")));
            }
        }
        if (jodd.util.StringUtil.isNotEmpty(exportReqVO.getStartTime()) && jodd.util.StringUtil.isNotEmpty(exportReqVO.getEndTime())) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            try {
                date = sdf.parse(exportReqVO.getEndTime());
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(date);
                // 把日期设置为当天最后一秒
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                date = calendar.getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            exportReqVO.setEndTime(sdf.format(date));
        }
        List<HardwareInfoDO> hardwareInfoDOList = hardwareInfoMapper.getHardwareInfoList(exportReqVO);
        for (HardwareInfoDO item : hardwareInfoDOList) {
            if (item.getTypeName().equals("vmware") && item.getIsMaintain() == 1) {
                item.setState("Maintenance");
            }
        }
        return hardwareInfoDOList;
    }

    @Override
    public List<Map> getHardwareDiskInfo(Long id, String uuid, String timeStr) {
        ClientInterface clientUtil = ElasticSearchHelper.getConfigRestClientUtil("esmapper/ESHardWareMapper.xml");
        Map searchFields = new HashMap<>();
        searchFields.put("passTime", "now-" + timeStr);
        searchFields.put("hostUuid", uuid);
        ESDatas<Map> data = clientUtil.searchList("hardware_disk_" + id + "/_search", "hardWareInfo", searchFields, Map.class);
        return data.getDatas();
    }

    @Override
    public List<Map> getHardwareMemoryInfo(Long id, String uuid, String timeStr) {
        ClientInterface clientUtil = ElasticSearchHelper.getConfigRestClientUtil("esmapper/ESHardWareMapper.xml");
        Map searchFields = new HashMap<>();
        searchFields.put("passTime", "now-" + timeStr);
        searchFields.put("hostUuid", uuid);
        ESDatas<Map> data = clientUtil.searchList("hardware_memory_" + id + "/_search", "hardWareInfo", searchFields, Map.class);
        return data.getDatas();
    }

    @Override
    public List<Map> getHardwareNetworkInfo(Long id, String uuid, String timeStr) {
        ClientInterface clientUtil = ElasticSearchHelper.getConfigRestClientUtil("esmapper/ESHardWareMapper.xml");
        Map searchFields = new HashMap<>();
        searchFields.put("passTime", "now-" + timeStr);
        searchFields.put("hostUuid", uuid);
        ESDatas<Map> data = clientUtil.searchList("hardware_network_" + id + "/_search", "hardWareInfo", searchFields, Map.class);
        return data.getDatas();
    }

    @Override
    public List<Map> getHardwareCpuInfo(Long id, String uuid, String timeStr) {
        ClientInterface clientUtil = ElasticSearchHelper.getConfigRestClientUtil("esmapper/ESHardWareMapper.xml");
        Map searchFields = new HashMap<>();
        searchFields.put("passTime", "now-" + timeStr);
        searchFields.put("hostUuid", uuid);
        ESDatas<Map> data = clientUtil.searchList("hardware_cpu_" + id + "/_search", "hardWareInfo", searchFields, Map.class);
        return data.getDatas();
    }

    @Override
    public List<Map<String, Object>> getLabel(Long platformId, String uuid, int labelKey) {
        List<Map<String, Object>> d = new ArrayList<>();

        Map map = new HashMap();
        map.put("type", "all");
        d.add(map);
//        PlatformconfigDTO platformconfigDTO = platformconfigApi.getByConfigId(platformId).getData();
//        if (platformconfigDTO == null) {
//            return d;
//        }
//        if (platformconfigDTO.getTypeCode().equals("zstack")) {
//            if(platformconfigDTO.getAkType() == 0){
//                if (labelRedisDAO.getHost("platromId:" + platformId + ":uuid:" + uuid + ":labelKey:" + labelKey) == null || labelRedisDAO.getHost("platromId:" + platformId + ":uuid:" + uuid + ":labelKey:" + labelKey).size() == 0) {
//                    List<Map<String, Object>> list = new ArrayList<>();
//                    Map<String, Object> map = new HashMap<>();
//                    String token = zstackAccessTokenRedisDAO.get("zstack:" + platformId).getUuid();
//                    if (token == null) {
//                        return null;
//                    }
//                    String url = zstackAccessTokenRedisDAO.get("zstack:" + platformId).getUrl();
//                    IdentityHashMap<String, Object> p = new IdentityHashMap<>();
//
//                    // 设置共同的参数
//                    p.put("filterLabels", "HostUuid=" + uuid);
//                    p.put("namespace", "ZStack/Host");
//                    // 根据labelKey设置不同的参数和类型
//                    if (labelKey == 1) {
//                        p.put("labelNames", "CPUNum");
//                        p.put("metricName", "CPUUsedUtilization");
//                    } else if (labelKey == 2) {
//                        p.put("labelNames", "DiskDeviceLetter");
//                        p.put("metricName", "DiskReadBytes");
//                    } else if (labelKey == 3) {
//                        p.put("labelNames", "NetworkDeviceLetter");
//                        p.put("metricName", "NetworkOutBytes");
//                    }
//
//                    HttpResponse result = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_LABEL)
//                            .header(Header.AUTHORIZATION, "OAuth " + token)
//                            .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
//                            .form(p)
//                            .execute();
//
//
//                    JSONArray labels = JSONObject.parseObject(result.body()).getJSONArray("labels");
//                    for (int i = 0; i < labels.size(); i++) {
//                        Map<String, Object> maps = new HashMap<>();
//                        JSONObject jsonObject = labels.getJSONObject(i);
//                        if (labelKey == 1) {
//
//                        } else if (labelKey == 2) {
//                            maps.put("type", jsonObject.get("DiskDeviceLetter"));
//                        } else if (labelKey == 3) {
//                            maps.put("type", jsonObject.get("NetworkDeviceLetter"));
//                        }
//                        list.add(maps);
//                    }
//
//                    if (labelKey == 1) {
//                        d = list.stream().sorted(Comparator.comparing(map1 ->
//                                StringUtil.toInt(map1.get("type")))).collect(Collectors.toList());
//                    } else {
//                        d = list.stream().sorted(Comparator.comparing(map1 ->
//                                StringUtil.toString(map1.get("type")))).collect(Collectors.toList());
//                    }
//                    labelRedisDAO.setHost("platromId:" + platformId + ":uuid:" + uuid + ":labelKey:" + labelKey, d);
//                }
//
//                if (labelRedisDAO.getHost("platromId:" + platformId + ":uuid:" + uuid + ":labelKey:" + labelKey)==null){
//                    return new ArrayList<>();
//                }else {
//                    return labelRedisDAO.getHost("platromId:" + platformId + ":uuid:" + uuid + ":labelKey:" + labelKey);
//                }
//            }else {
//                List<Map<String, Object>> list = new ArrayList<>();
//                ZstackLoginInfo zstackLoginInfo = zstackAccessTokenRedisDAO.get("zstack:" + platformId);
//                HttpRequest request = HttpRequest.get(zstackLoginInfo.getUrl() + ZstackApiConstant.GET_ZSTACK_LABEL)
//                        .header(Header.AUTHORIZATION, "OAuth " + zstackLoginInfo.getUuid())
//                        .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8");
//                HttpResponse result = httpClientWrapper.execute(request);
//
//
//                JSONArray labels = JSONObject.parseObject(result.body()).getJSONArray("labels");
//                for (int i = 0; i < labels.size(); i++) {
//                    Map<String, Object> maps = new HashMap<>();
//                    JSONObject jsonObject = labels.getJSONObject(i);
//                    if (labelKey == 1) {
//                        maps.put("type", jsonObject.get("CPUNum"));
//                    } else if (labelKey == 2) {
//                        maps.put("type", jsonObject.get("DiskDeviceLetter"));
//                    } else if (labelKey == 3) {
//                        maps.put("type", jsonObject.get("NetworkDeviceLetter"));
//                    }
//                    list.add(maps);
//                }
//
//                if (labelKey == 1) {
//                    d = list.stream().sorted(Comparator.comparing(map1 ->
//                            StringUtil.toInt(map1.get("type")))).collect(Collectors.toList());
//                } else {
//                    d = list.stream().sorted(Comparator.comparing(map1 ->
//                            StringUtil.toString(map1.get("type")))).collect(Collectors.toList());
//                }
//                labelRedisDAO.setHost("platromId:" + platformId + ":uuid:" + uuid + ":labelKey:" + labelKey, d);
//
//                if (labelRedisDAO.getHost("platromId:" + platformId + ":uuid:" + uuid + ":labelKey:" + labelKey)==null){
//                    return new ArrayList<>();
//                }else {
//                    return labelRedisDAO.getHost("platromId:" + platformId + ":uuid:" + uuid + ":labelKey:" + labelKey);
//                }
//            }
//
//
//        } else {
//            if (labelRedisDAO.getHost("platromId:" + platformId + ":uuid:" + uuid + ":labelKey:" + labelKey) == null || labelRedisDAO.getHost("platromId:" + platformId + ":uuid:" + uuid + ":labelKey:" + labelKey).size() == 0) {
//                if (labelKey == 1) {
//                    Map map = new HashMap();
//                    map.put("type", "CPUAverageUsedUtilization");
//                    d.add(map);
//
//                } else if (labelKey == 2) {
//                    String query = "SELECT DISTINCT(type) as type from zj_cloud_hardware WHERE uuid = '" + uuid + "' and label = 'disk' AND time >= now() - 20m and type !='DiskAllReadBytes' and type !='DiskAllWriteBytes' and type != 'disk' and type != 'DiskAllUsedCapacityInBytes' and type != 'DiskAllUsedCapacityInPercent' and type != 'DiskZStackUsedCapacityInPercent' and type != 'DiskZStackUsedCapacityInBytes'";
//                    List<Object> records = influxDBTemplate.fetchRecords(query);
//                    for (Object o : records) {
//                        Map map = new HashMap();
//                        map.put("type", ((Map) o).get("type"));
//                        d.add(map);
//                    }
//                } else if (labelKey == 3) {
//                    String query = "SELECT DISTINCT(type) as type from zj_cloud_hardware WHERE uuid = '" + uuid + "' and label = 'net' AND time >= now() - 20m and type !='NetworkAllInBytes' and type !='NetworkAllOutBytes'";
//                    List<Object> records = influxDBTemplate.fetchRecords(query);
//                    for (Object o : records) {
//                        Map map = new HashMap();
//                        map.put("type", ((Map) o).get("type"));
//                        d.add(map);
//                    }
//                }
//
//                if (labelKey == 1) {
//                    Collections.sort(d, new Comparator<Map<String, Object>>() {
//                        @Override
//                        public int compare(Map<String, Object> m1, Map<String, Object> m2) {
//                            String typeStr1 = StringUtil.toString(m1.get("type"));
//                            String typeStr2 = StringUtil.toString(m2.get("type"));
//
//                            // 检查是否为空或空白字符串，并尝试解析为整数
//                            Integer type1 = parseInteger(typeStr1);
//                            Integer type2 = parseInteger(typeStr2);
//
//                            return type1.compareTo(type2);
//                        }
//
//                        private Integer parseInteger(String str) {
//                            if (str == null || str.trim().isEmpty()) {
//                                return 0; // 默认为 0
//                            }
//                            try {
//                                return Integer.parseInt(str);
//                            } catch (NumberFormatException e) {
//                                return 0; // 如果解析失败，默认为 0
//                            }
//                        }
//                    });
//                }
//                if (!d.isEmpty()) {
//                    labelRedisDAO.setHost("platromId:" + platformId + ":uuid:" + uuid + ":labelKey:" + labelKey, d);
//                }
//            }
//            if (labelRedisDAO.getHost("platromId:" + platformId + ":uuid:" + uuid + ":labelKey:" + labelKey)==null){
//                return new ArrayList<>();
//            }else {
//                return labelRedisDAO.getHost("platromId:" + platformId + ":uuid:" + uuid + ":labelKey:" + labelKey);
//            }
//        }

        return d;
    }


    @Override
    public Map<String, Object> getHardwareStatusCount(List<String> tenantIds, Long platformId) {
        Map<String, Object> hardwareStatusCount = hardwareInfoMapper.getHardwareStatusCount(tenantIds, platformId);
        if (hardwareStatusCount.size() == 1) {
            hardwareStatusCount = new HashMap<>();
            hardwareStatusCount.put("hardwareConnectedNum", 0);
            hardwareStatusCount.put("hardwareDisconnectedNum", 0);
            hardwareStatusCount.put("hardwareConnectingNum", 0);
            hardwareStatusCount.put("hardwareNum", 0);
        }
        return hardwareStatusCount;
    }

    @Override
    public List<Map<String, Object>> getCpuTop(List<String> tenantIds, Long platformId, Long regionId, int top) {
        if (regionId != null && regionId.longValue() != 0) {
            if (regionId.toString().substring(2, 6).equals("0000")) {
                regionId = Long.parseLong(regionId.toString().substring(0, 2));
            } else {
                regionId = Long.parseLong(regionId.toString().substring(0, 4));
            }
        }
        return hardwareInfoMapper.getCpuTop(tenantIds, platformId, regionId, top);
    }

    @Override
    public List<Map<String, Object>> getMemoryTop(List<String> tenantIds, Long platformId, Long regionId, int top) {
        if (regionId != null && regionId.longValue() != 0) {
            if (regionId.toString().substring(2, 6).equals("0000")) {
                regionId = Long.parseLong(regionId.toString().substring(0, 2));
            } else {
                regionId = Long.parseLong(regionId.toString().substring(0, 4));
            }
        }
        return hardwareInfoMapper.getMemoryTop(tenantIds, platformId, regionId, top);
    }

    @Override
    public Map<String, Object> getCpuCapacity(List<String> tenantIds, Long platformId, Long regionId) {
        if (regionId != null && regionId.longValue() != 0) {
            String a = regionId.toString().substring(2, 6);
            if (regionId.toString().substring(2, 6).equals("0000")) {
                regionId = Long.parseLong(regionId.toString().substring(0, 2));
            } else {
                regionId = Long.parseLong(regionId.toString().substring(0, 4));
            }
        }
        Map<String, Object> cpuCapacity = hardwareInfoMapper.getCpuCapacity(tenantIds, platformId, regionId);
        if (cpuCapacity == null) {
            cpuCapacity = new HashMap<>();
            cpuCapacity.put("totalCpuCapacity", 0);
            cpuCapacity.put("usedCpuCapacity", 0);
        }
        return cpuCapacity;
    }

    @Override
    public Map<String, Object> getMemoryCapacity(List<String> tenantIds, Long platformId, Long regionId) {
        if (regionId != null && regionId.longValue() != 0) {
            if (regionId.toString().substring(2, 6).equals("0000")) {
                regionId = Long.parseLong(regionId.toString().substring(0, 2));
            } else {
                regionId = Long.parseLong(regionId.toString().substring(0, 4));
            }
        }
        Map<String, Object> memoryCapacity = hardwareInfoMapper.getMemoryCapacity(tenantIds, platformId, regionId);
        if (memoryCapacity == null) {
            memoryCapacity = new HashMap<>();
            memoryCapacity.put("totalMemoryCapacity", 0);
            memoryCapacity.put("usedMemoryCapacity", 0);
        }
        return memoryCapacity;
    }

    @Override
    public Map<String, Object> getMonitorInfo(List<String> id, Long platformId, Long regionId, String time, String monitorEntry) {
//        List<PlatformconfigDTO> platformconfigDTOS = platformconfigApi.getPlatList().getData();
//        Map<Long, PlatformconfigDTO> platformconfigDTOMap = convertMap(platformconfigDTOS, PlatformconfigDTO::getId);
//        Map<String, Object> allHardwareInfo = new HashMap<>();
//        Map<String, String> timeConfig = getTimeConfig(time);
//        String interval = timeConfig.get("interval");
//        String date = timeConfig.get("date");
//
//        boolean isSangFor = false;
//        StringBuilder platformQuery = new StringBuilder();
//        if (platformId != null && platformId != 0) {
//            platformQuery.append(" AND platformId='").append(platformId).append("'");
//            isSangFor = platformconfigDTOMap.get(platformId).getTypeCode().equals("sangFor");
//        } else {
//            platformQuery.append("AND (");
//            List<Long> platformList = hardwareInfoMapper.selectPlatformList(id);
//            if (CollectionUtil.isEmpty(platformList)) {
//                allHardwareInfo.put("currentInfo", new ArrayList<>().add(0));
//                allHardwareInfo.put(monitorEntry, new ArrayList<>());
//                return allHardwareInfo;
//            }
//            for (int i = 0, len = platformList.size(); i < len; i++) {
//                platformQuery.append(" platformId='").append(platformList.get(i)).append("'");
//                if (i < len - 1) {
//                    platformQuery.append(" or");
//                }
//            }
//            platformQuery.append(")");
//        }
//
//        if (CollectionUtil.isNotEmpty(platformconfigDTOS)) {
//            isSangFor = platformconfigDTOS.stream()
//                    .anyMatch(x -> "sangFor".equals(x.getTypeCode()));
//        }
//        if (regionId != null && regionId != 0) {
//            String regionString = regionId.toString();
//            String simplifiedRegionId = regionString.substring(2, 6).equals("0000") ? regionString.substring(0, 2) : regionString.substring(0, 4);
//            platformQuery.append(" AND regionId =~ /^").append(simplifiedRegionId).append("/");
//        }
//
//
//        if (monitorEntry.equals("diskInfo")) {
//            List currentInfo = new ArrayList();
//            String diskReadMetricName = "DiskAllReadBytes";
//            String diskWriteMetricName = "DiskAllWriteBytes";
//            if (ObjectUtil.isNotEmpty(platformId) && isSangFor) {
//                diskReadMetricName = "DiskReadBytes";
//                diskWriteMetricName = "DiskWriteBytes";
//            }
//
//
//            String query = String.format("SELECT MEAN(value) AS value FROM zj_cloud_hardware WHERE metricName = '%s' %s AND time >= %s GROUP BY time(%s) FILL(0) order by time asc", diskReadMetricName, platformQuery, date, interval);
//            List<Object> DiskAllReadBytesInfo = influxDBTemplate.fetchRecords(query);
//            query = String.format("SELECT SUM(value) as value FROM zj_cloud_hardware WHERE metricName = '%s' %s AND time >= now()-30m GROUP BY time(2s) fill(0) order by time desc limit 1", diskReadMetricName, platformQuery);
//            if (influxDBTemplate.fetchRecords(query).size() == 0) {
//                currentInfo.add(0);
//            } else {
//                Map DiskAllReadBytes = (Map) influxDBTemplate.fetchRecords(query).get(0);
//                currentInfo.add(DiskAllReadBytes.get("value"));
//            }
//
//            query = String.format("SELECT MEAN(value) AS value FROM zj_cloud_hardware WHERE metricName = '%s' %s AND time >= %s GROUP BY time(%s) FILL(0) order by time asc", diskWriteMetricName, platformQuery, date, interval);
//            List<Object> DiskAllWriteBytesInfo = influxDBTemplate.fetchRecords(query);
//            query = String.format("SELECT SUM(value) as value FROM zj_cloud_hardware WHERE metricName = '%s' %s AND time >= now()-30m GROUP BY time(2s) fill(0) order by time desc limit 1", diskWriteMetricName, platformQuery);
//            if (influxDBTemplate.fetchRecords(query).size() == 0) {
//                currentInfo.add(0);
//            } else {
//                Map DiskAllWriteBytes = (Map) influxDBTemplate.fetchRecords(query).get(0);
//                currentInfo.add(DiskAllWriteBytes.get("value"));
//            }
//            Map<String, Object> diskInfo = new HashMap<>();
//            List<Map<String, String>> diskread = DiskAllReadBytesInfo.stream()
//                    .map(obj -> (Map<String, String>) obj)
//                    .map(map -> {
//                        Map<String, String> Map = new HashMap<>();
//                        Map.putAll(map);
//                        return Map;
//                    })
//                    .sorted((map1, map2) -> map1.get("time").compareTo(map2.get("time")))
//                    .collect(Collectors.toList());
//
//            List<Map<String, String>> diskwrite = DiskAllWriteBytesInfo.stream()
//                    .map(obj -> (Map<String, String>) obj)
//                    .map(map -> {
//                        Map<String, String> Map = new HashMap<>();
//                        Map.putAll(map);
//                        return Map;
//                    })
//                    .sorted((map1, map2) -> map1.get("time").compareTo(map2.get("time")))
//                    .collect(Collectors.toList());
//
//            diskInfo.put("DiskAllReadBytesInfo", diskread);
//            diskInfo.put("DiskAllWriteBytesInfo", diskwrite);
//            allHardwareInfo.put("currentInfo", currentInfo);
//            allHardwareInfo.put("diskInfo", diskInfo);
//        } else if (monitorEntry.equals("networkInfo")) {
//            List currentInfo = new ArrayList();
//            String netOutMetricName = "NetworkAllOutBytes";
//            String netInMetricName = "NetworkAllInBytes";
//            if (ObjectUtil.isNotEmpty(platformId) && isSangFor) {
//                netOutMetricName = "NetworkOutBytes";
//                netInMetricName = "NetworkInBytes";
//            }
////            String query = " SELECT MEAN(value) as value from ( SELECT SUM(value) as value FROM zj_cloud_hardware WHERE metricName = "+netOutMetricName + platformQuery + " AND time >= "+date+" GROUP BY time(2s) fill(0)) GROUP BY time("+interval+")";
//
//            String query = String.format("SELECT MEAN(value) AS value FROM zj_cloud_hardware WHERE metricName = '%s' %s AND time >= %s GROUP BY time(%s) FILL(0) order by time asc", netOutMetricName, platformQuery, date, interval);
//            List<Object> NetworkAllOutBytesInfo = influxDBTemplate.fetchRecords(query);
//            query = String.format("SELECT SUM(value) as value FROM zj_cloud_hardware WHERE metricName = '%s' %s AND time >= now()-30m GROUP BY time(2s) fill(0) order by time desc limit 1", netOutMetricName, platformQuery);
//            if (influxDBTemplate.fetchRecords(query).size() == 0) {
//                currentInfo.add(0);
//            } else {
//                Map NetworkAllOutBytes = (Map) influxDBTemplate.fetchRecords(query).get(0);
//                currentInfo.add(NetworkAllOutBytes.get("value"));
//            }
//
//            query = String.format("SELECT MEAN(value) AS value FROM zj_cloud_hardware WHERE metricName = '%s' %s AND time >= %s GROUP BY time(%s) FILL(0) order by time asc", netInMetricName, platformQuery, date, interval);
//            List<Object> NetworkAllInBytesInfo = influxDBTemplate.fetchRecords(query);
//            query = String.format("SELECT SUM(value) as value FROM zj_cloud_hardware WHERE metricName = '%s' %s AND time >= now()-30m GROUP BY time(2s) fill(0) order by time desc limit 1", netInMetricName, platformQuery);
//            if (influxDBTemplate.fetchRecords(query).size() == 0) {
//                currentInfo.add(0);
//            } else {
//                Map NetworkAllInBytes = (Map) influxDBTemplate.fetchRecords(query).get(0);
//                currentInfo.add(NetworkAllInBytes.get("value"));
//            }
//            Map<String, Object> networkInfo = new HashMap<>();
//            List<Map<String, String>> netout = NetworkAllOutBytesInfo.stream()
//                    .map(obj -> (Map<String, String>) obj)
//                    .map(map -> {
//                        Map<String, String> Map = new HashMap<>();
//                        Map.putAll(map);
//                        return Map;
//                    })
//                    .sorted((map1, map2) -> map1.get("time").compareTo(map2.get("time")))
//                    .collect(Collectors.toList());
//
//            List<Map<String, String>> netin = NetworkAllInBytesInfo.stream()
//                    .map(obj -> (Map<String, String>) obj)
//                    .map(map -> {
//                        Map<String, String> Map = new HashMap<>();
//                        Map.putAll(map);
//                        return Map;
//                    })
//                    .sorted((map1, map2) -> map1.get("time").compareTo(map2.get("time")))
//                    .collect(Collectors.toList());
//
//
//            networkInfo.put("NetworkAllOutBytesInfo", netout);
//            networkInfo.put("NetworkAllInBytesInfo", netin);
//            allHardwareInfo.put("currentInfo", currentInfo);
//            allHardwareInfo.put("networkInfo", networkInfo);
//        } else {
//            String cpuUsedMetricName = monitorEntry;
//            if (isSangFor) {
//                cpuUsedMetricName = "CPUAverageUsedUtilization";
//            }
//
//            String query = String.format("SELECT MEAN(value) AS value FROM zj_cloud_hardware WHERE metricName = '%s' %s AND time >= %s GROUP BY time(%s) FILL(0) order by time asc", cpuUsedMetricName, platformQuery, date, interval);
//            List<Object> Info = influxDBTemplate.fetchRecords(query);
//            List<Map<String, String>> sortedList = Info.stream()
//                    .map(obj -> (Map<String, String>) obj)
//                    .map(map -> {
//                        Map<String, String> Map = new HashMap<>();
//                        Map.putAll(map);
//                        return Map;
//                    })
//                    .sorted((map1, map2) -> map1.get("time").compareTo(map2.get("time")))
//                    .collect(Collectors.toList());
//            List<Object> unmodifiableList = Collections.unmodifiableList(sortedList);
//            allHardwareInfo.put(monitorEntry, unmodifiableList);
//            query = " SELECT MEAN(value) as value FROM zj_cloud_hardware WHERE metricName = '" + cpuUsedMetricName + "' " + platformQuery + " AND time >= now()-30m GROUP BY time(2s) fill(0) order by time desc limit 1";
//            List currentInfo = new ArrayList();
//            if (influxDBTemplate.fetchRecords(query).size() == 0) {
//                currentInfo.add(0);
//            } else {
//                Map monitorEntryName = (Map) influxDBTemplate.fetchRecords(query).get(0);
//                currentInfo.add(monitorEntryName.get("value"));
//            }
//            allHardwareInfo.put("currentInfo", currentInfo);
//        }
//        return allHardwareInfo;
        return new ArrayHashMap<>();
    }

    @Override
    public PageResult<HardwareInfoDO> getHardwareInfoSlavePage(HardwareInfoPageReqVO pageVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        AdminUserRespDTO adminUserRespDTO = adminUserService.getUser(loginUser.getId()).getData();
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            if (StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
                pageVO.setTenantId(Arrays.asList(String.valueOf(adminUserRespDTO.getTenantId()).split(",")));
            } else {
                pageVO.setTenantId(Arrays.asList(adminUserRespDTO.getServiceTenantId().split(",")));
            }
        }
        PageParam pageParam = new PageParam().setPageNo(pageVO.getPageNo()).setPageSize(pageVO.getPageSize());
        IPage<HardwareInfoDO> mpPage = MyBatisUtils.buildPage(pageParam);
        List<String> uids = new ArrayList<>();
        if (StringUtil.isNotEmpty(pageVO.getIds())) {
            uids = Arrays.asList(pageVO.getIds().split(","));
        }

        return new PageResult<>(hardwareInfoMapper.getHardwareInfoSlavePage(mpPage, pageVO, uids), mpPage.getTotal());

    }

    @Override
    public HardwareInfoDO getHardwareMasterInfo(String uuid) {
        return hardwareInfoMapper.selectOne("uuid", uuid);
    }

    @Override
    public List<HardwareInfoDO> getListByPlatformId(Collection<Long> platformId) {
        return hardwareInfoMapper.getListByPlatformId(platformId);
    }

    @Override
    public List<Map<String, String>> getClusterSimpleInfo(Collection<Long> platformId) {
        // 如果platformId为null，初始化一个空集合
        Collection<Long> finalPlatformIds = platformId;
        if (finalPlatformIds == null) {
            finalPlatformIds = new ArrayList<>();
        }
        // 如果集合为空，从平台配置中获取所有平台ID
        if (finalPlatformIds.isEmpty()) {
            LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
            List<Map> platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
            finalPlatformIds = new ArrayList<>();
            for (Map map : platform) {
                finalPlatformIds.add(Convert.toLong(map.get("platformId")));
            }
        }
        return hardwareInfoMapper.getClusterSimpleInfo(finalPlatformIds);
    }

    @Override
    public List<HardwareInfoDO> getHardwareListByUuids(List<String> uuids) {
        return hardwareInfoMapper.selectListByUuids(uuids);
    }

    @Override
    public PageResult<HardwareInfoDO> selectHardwareList(HardwareInfoPageReqVO pageVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        AdminUserRespDTO adminUserRespDTO = adminUserService.getUser(loginUser.getId()).getData();
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            if (StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
                pageVO.setTenantId(Arrays.asList(String.valueOf(adminUserRespDTO.getTenantId()).split(",")));
            } else {
                pageVO.setTenantId(Arrays.asList(adminUserRespDTO.getServiceTenantId().split(",")));
            }
        }
        PageParam pageParam = new PageParam().setPageNo(pageVO.getPageNo()).setPageSize(pageVO.getPageSize());
        IPage<HardwareInfoDO> mpPage = MyBatisUtils.buildPage(pageParam);
        return new PageResult<>(hardwareInfoMapper.selectHardwareList(mpPage, pageVO), mpPage.getTotal());
    }

    @Override
    public HardwareInfoDO getByUuid(String uuid) {
        return hardwareInfoMapper.selectOne("uuid", uuid, "deleted", 0);
    }

    @Override
    public void deleteHardwareInfoByplatform(Long platformId) {
        hardwareInfoMapper.deleteHardwareInfoByplatform(platformId);
    }


    @Slave
    @Override
    public HardwareInfoDO getHardwareSlaveInfo(String uuid) {
        return hardwareInfoMapper.selectOne("uuid", uuid);
    }

    @Override
    public List<MonitorHardwareDataRespVo> getHardwareGraphData(MonitorHardwareDataReqVo reqVo) {
//        Long period = reqVo.getEndTime() - reqVo.getStartTime();
//        List<MonitorHardwareDataRespVo> hardwareGraphDatas = new ArrayList<>();
//        if (StringUtil.isNullOrEmpty(reqVo.getMetricName()) && StringUtil.isNullOrEmpty(reqVo.getHostType())) {
//            return hardwareGraphDatas;
//        }
//        String startTime = DateUtil.format(new Date(reqVo.getStartTime()), "yyyy-MM-dd HH:mm:ss");
//        String enTime = DateUtil.format(new Date(reqVo.getEndTime()), "yyyy-MM-dd HH:mm:ss");
//        String mTime = DateUtil.format(DateUtil.beginOfDay(new Date(reqVo.getStartTime())), "yyyy-MM-dd HH:mm:ss");
//        String timeGroup = "";
//        if (period > 3600000 && period <= 21600000) {
//            timeGroup = "group by time(2m)";
//        } else if (period > 21600000 && period <= 86400000) {
//            timeGroup = "group by time(8m)";
//        } else if (period > 86400000 && period <= 604800000) {
//            timeGroup = "group by time(56m)";
//        } else if (period > 604800000 && period <= 2592000000L) {
//            timeGroup = "group by time(2h)";
//        } else if (period > 2592000000L) {
//            timeGroup = "group by time(3h)";
//        } else if (period <= 3600000) {
//            timeGroup = "group by time(20s)";
//        }
//        List<String> metricNames = new ArrayList<String>(Arrays.asList(reqVo.getMetricName().split(",")));
//        List<String> hostTypes = new ArrayList<String>(Arrays.asList(reqVo.getHostType().split(",")));
//        if (reqVo.getHostType().contains("CPUAverageUsedUtilization")) {
//            metricNames.removeIf(s -> s.equals("CPUAverageUsedUtilization"));
//            hostTypes.removeIf(s -> s.equals("CPUAverageUsedUtilization"));
//            String query = "SELECT FIRST(value) as value from zj_cloud_hardware WHERE uuid = '" + reqVo.getUuid() +
//                    "' and metricName = 'CPUAverageUsedUtilization' and type = 'CPUAverageUsedUtilization'" +
//                    " and time>= '" + startTime + "' and time<= '" + enTime + "'" + timeGroup;
//            query = query + " fill(previous) ";
//            List<Object> records = influxDBTemplate.fetchRecords(query);
//            List<MonitorHardwareDataRespVo> result = JSONArray.parseArray(JSON.toJSONString(records), MonitorHardwareDataRespVo.class);
//            if (result.isEmpty() || result.get(0).getValue() == null) {
//                String lastQuery = "SELECT LAST(value) as value from zj_cloud_hardware WHERE uuid = '" + reqVo.getUuid() +
//                        "' and metricName = 'CPUAverageUsedUtilization' and type = 'CPUAverageUsedUtilization'" +
//                        " and time>= '" + mTime + "' and time < '" + startTime + "'";
//                List<Object> lastRecords = influxDBTemplate.fetchRecords(lastQuery);
//                if (!lastRecords.isEmpty()) {
//                    MonitorHardwareDataRespVo lastDataPoint = JSONArray.parseArray(JSON.toJSONString(lastRecords), MonitorHardwareDataRespVo.class).get(0);
//                    if (!result.isEmpty()) {
//                        result.get(0).setValue(lastDataPoint.getValue());
//                    }
//                }
//            }
//            result.forEach(item -> item.setType("CPUAverageUsedUtilization").setHost_metricName("CPUAverageUsedUtilization"));
//            Iterator<MonitorHardwareDataRespVo> iterator = result.iterator();
//            while (iterator.hasNext()) {
//                MonitorHardwareDataRespVo result1 = iterator.next();
//                if (result1.getValue() == null) {
//                    iterator.remove(); // 如果值为 null，移除该对象
//                } else if (result1.getValue() < 0) {
//                    result1.setValue(0.0); // 如果值为负数，将其值修改为 0
//                }
//            }
//            hardwareGraphDatas.addAll(result);
//        }
//        for (String metricName : metricNames) {
//            if (hostTypes.size() > 0 && !hostTypes.get(0).equals("")) {
//                for (String hostType : hostTypes) {
//                    String query = "SELECT FIRST(value) as value from zj_cloud_hardware WHERE uuid = '" + reqVo.getUuid() + "' and metricName = '" + metricName;
//                    query = query + "' and type = '" + hostType;
//                    query = query + "' and time>= '" + startTime + "' and time<= '" + enTime + "'" + timeGroup;
//                    query = query + " fill(previous) ";
//                    List<Object> records = influxDBTemplate.fetchRecords(query);
//                    List<MonitorHardwareDataRespVo> result = JSONArray.parseArray(JSON.toJSONString(records), MonitorHardwareDataRespVo.class);
//                    if (result.isEmpty() || result.get(0).getValue() == null) {
//                        String lastQuery = "SELECT LAST(value) as value from zj_cloud_hardware WHERE uuid = '" + reqVo.getUuid() +
//                                "' and metricName = '" + metricName + "' and type = '" + hostType +
//                                "' and time>= '" + mTime + "' and time < '" + startTime + "'";
//                        List<Object> lastRecords = influxDBTemplate.fetchRecords(lastQuery);
//                        if (!lastRecords.isEmpty()) {
//                            MonitorHardwareDataRespVo lastDataPoint = JSONArray.parseArray(JSON.toJSONString(lastRecords), MonitorHardwareDataRespVo.class).get(0);
//                            if (!result.isEmpty()) {
//                                result.get(0).setValue(lastDataPoint.getValue());
//                            }
//                        }
//                    }
//                    result.forEach(item -> item.setType(hostType).setHost_metricName(metricName));
//                    Iterator<MonitorHardwareDataRespVo> iterator = result.iterator();
//                    while (iterator.hasNext()) {
//                        MonitorHardwareDataRespVo result1 = iterator.next();
//                        if (result1.getValue() == null) {
//                            iterator.remove(); // 如果值为 null，移除该对象
//                        } else if (result1.getValue() < 0) {
//                            result1.setValue(0.0); // 如果值为负数，将其值修改为 0
//                        }
//                    }
//                    hardwareGraphDatas.addAll(result);
//                }
//            } else {
//                String query = "SELECT FIRST(value) as value from zj_cloud_hardware WHERE uuid = '" + reqVo.getUuid() + "' and metricName = '" + metricName;
//                query = query + "' and time>= '" + startTime + "' and time<= '" + enTime + "'" + timeGroup;
//                query = query + " fill(previous) ";
//                List<Object> records = influxDBTemplate.fetchRecords(query);
//                List<MonitorHardwareDataRespVo> result = JSONArray.parseArray(JSON.toJSONString(records), MonitorHardwareDataRespVo.class);
//                if (result.isEmpty() || result.get(0).getValue() == null) {
//                    String lastQuery = "SELECT LAST(value) as value from zj_cloud_hardware WHERE uuid = '" + reqVo.getUuid() +
//                            "' and metricName = '" + metricName + "' and time>= '" + mTime + "' and time < '" + startTime + "'";
//                    List<Object> lastRecords = influxDBTemplate.fetchRecords(lastQuery);
//                    if (!lastRecords.isEmpty()) {
//                        MonitorHardwareDataRespVo lastDataPoint = JSONArray.parseArray(JSON.toJSONString(lastRecords), MonitorHardwareDataRespVo.class).get(0);
//                        if (!result.isEmpty()) {
//                            result.get(0).setValue(lastDataPoint.getValue());
//                        }
//                    }
//                }
//                result.forEach(item -> item.setType(metricName).setHost_metricName(metricName));
//                Iterator<MonitorHardwareDataRespVo> iterator = result.iterator();
//                while (iterator.hasNext()) {
//                    MonitorHardwareDataRespVo result1 = iterator.next();
//                    if (result1.getValue() == null) {
//                        iterator.remove(); // 如果值为 null，移除该对象
//                    } else if (result1.getValue() < 0) {
//                        result1.setValue(0.0); // 如果值为负数，将其值修改为 0
//                    }
//                }
//                hardwareGraphDatas.addAll(result);
//            }
//        }
//
//        return hardwareGraphDatas.stream()
//                .sorted((data1, data2) -> data1.getTime().compareTo(data2.getTime()))
//                .collect(Collectors.toList());
        return new ArrayList<>();
    }

    @Override
    public List<MonitorHardwareDataRespVo> getHardwareGraphDataRandom(MonitorHardwareDataReqVo reqVo) {
//        Long period = reqVo.getEndTime() - reqVo.getStartTime();
        List<MonitorHardwareDataRespVo> hardwareGraphDatas = new ArrayList<>();
//        if (StringUtil.isNullOrEmpty(reqVo.getMetricName()) && StringUtil.isNullOrEmpty(reqVo.getHostType())) {
//            return hardwareGraphDatas;
//        }
//        HardwareInfoDO hardware = hardwareInfoMapper.selectOne("uuid", reqVo.getUuid());
//        List<String> metricNames = Arrays.asList(reqVo.getMetricName().split(","));
//        List<String> hostTypes = Arrays.asList(reqVo.getHostType().split(","));
//        int sampleNum;
//        if (hostTypes.size() == 1 && StringUtil.isNullOrEmpty(hostTypes.get(0))) {
//            sampleNum = metricNames.size();
//        } else {
//            sampleNum = hostTypes.size();
//        }
//        String query = "SELECT SAMPLE(value," + sampleNum + ") as value,type,host_metricName from zj_cloud_hardware WHERE uuid = '" + reqVo.getUuid() + "' and (metricName = '";
//        for (int i = 0; i < metricNames.size() - 1; i++) {
//            query = query + metricNames.get(i) + "' or metricName ='";
//        }
//        query = query + metricNames.get(metricNames.size() - 1);
//        if (StringUtil.isNotEmpty(reqVo.getHostType())) {
//            query += "') and (type= '";
//            for (int i = 0; i < hostTypes.size() - 1; i++) {
//                query = query + hostTypes.get(i) + "' or type ='";
//            }
//            query = query + hostTypes.get(hostTypes.size() - 1);
//        }
//        query = query + "') and time>= '" + DateUtil.format(new Date(reqVo.getStartTime()), "yyyy-MM-dd HH:mm:ss")
//                + "' and time<= '" + DateUtil.format(new Date(reqVo.getEndTime()), "yyyy-MM-dd HH:mm:ss") + "'";
//        if (period > 3600000 && period <= 21600000) {
//            query = query + "group by time(2m)";
//        } else if (period > 21600000 && period <= 86400000) {
//            query = query + "group by time(8m)";
//        } else if (period > 86400000 && period <= 604800000) {
//            query = query + "group by time(56m)";
//        } else if (period > 604800000 && period <= 2592000000L) {
//            query = query + "group by time(2h)";
//        } else if (period > 2592000000L) {
//            query = query + "group by time(3h)";
//        } else if (period <= 3600000) {
//            query = query + "group by time(20s)";
//        }
//        query = query + " fill(none) ";
//        for (Object item : influxDBTemplate.fetchRecords(query)) {
//            hardwareGraphDatas.add(new ObjectMapper().convertValue(item, MonitorHardwareDataRespVo.class).setTenantId(hardware.getTenantId()));
//        }
        return hardwareGraphDatas;
    }


    private Map<String, String> getTimeConfig(String time) {
        Map<String, String> config = new HashMap<>();
        switch (time) {
            case "15m":
                config.put("interval", "3m");
                config.put("date", "now()-15m");
                break;
            case "1d":
                config.put("interval", "20m");
                config.put("date", "now()-1d");
                break;
            case "7d":
                config.put("interval", "2h");
                config.put("date", "'" + DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -6), "yyyy-MM-dd") + "'");
                break;
            case "15d":
                config.put("interval", "4h");
                config.put("date", "'" + DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -14), "yyyy-MM-dd") + "'");
                break;
            case "30d":
                config.put("interval", "8h");
                config.put("date", "'" + DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -29), "yyyy-MM-dd") + "'");
                break;
            case "90d":
                config.put("interval", "24h");
                config.put("date", "'" + DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -89), "yyyy-MM-dd") + "'");
                break;
            default:
                config.put("interval", "2h"); // defaults for unmapped values
                config.put("date", "now()");
        }
        return config;
    }


    @Override
    public List<HardWareRespCreateReqDTO> getHardwareByPlatformId(Long platformId) {
        return hardwareInfoMapper.getHardwareByPlatformId(platformId);
    }

    @Override
    public List<HardwareInfoDO> findHardwareByplatformIdAndHost(Long platformId, String hostName) {
        return hardwareInfoMapper.findHardwareByplatformIdAndHost(platformId, hostName);
    }

    @Override
    public List<HardwareInfoDO> getHardwareInfoByList(HardwareInfoPageReqVO pageVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        AdminUserRespDTO adminUserRespDTO = adminUserService.getUser(loginUser.getId()).getData();
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            if (StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
                pageVO.setTenantId(Arrays.asList(String.valueOf(adminUserRespDTO.getTenantId()).split(",")));
            } else {
                pageVO.setTenantId(Arrays.asList(adminUserRespDTO.getServiceTenantId().split(",")));
            }
        }
        List<String> uids = new ArrayList<>();
        if (StringUtil.isNotEmpty(pageVO.getIds())) {
            uids = Arrays.asList(pageVO.getIds().split(","));
        }

        List<String> inids = new ArrayList<>();
        if (StringUtil.isNotEmpty(pageVO.getInIds())) {
            inids = Arrays.asList(pageVO.getInIds().split(","));
        }
        return hardwareInfoMapper.getHardwareInfoByList(pageVO, uids, inids);
    }

    @Override
    public List<HardwareInfoDO> getHardwareListByPlatformId(Long platformId) {
        LambdaQueryWrapper<HardwareInfoDO> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HardwareInfoDO::getDeleted, 0)
                .eq(platformId != null, HardwareInfoDO::getPlatformId, platformId);
        return hardwareInfoMapper.selectList(lqw);

    }

    @Override
    public Integer getCloudHostCount(String uuid) {
        return hardwareInfoMapper.getCloudHostCount(uuid);
    }

    @Override
    public Integer getHardwareCount(Long platformId) {
        LambdaQueryWrapper<HardwareInfoDO> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HardwareInfoDO::getPlatformId, platformId)
                .eq(HardwareInfoDO::getDeleted, 0);
        return hardwareInfoMapper.selectCount(lqw).intValue();
    }

    @Override
    @TenantIgnore
    public List<HardwareInfoDO> getHardwareByTenantOrPlatforms(AssetReqVO assetReqVO) {
        List<Long> platformIds = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(assetReqVO.getPlatformIds())) {
            platformIds = assetReqVO.getPlatformIds();
        } else {
            List<Map> platform = platformconfigApi.getPlatformByTenantId(String.valueOf(assetReqVO.getTenantId())).getData();
            if (ObjectUtil.isNotEmpty(platform)) {
                platformIds = platform.stream().map(map -> Long.parseLong(String.valueOf(map.get("platformId")))).toList();
            }
        }
        return hardwareInfoMapper.getHardwareListByPlatformId(platformIds);
    }
}
