package cn.iocoder.zj.module.monitor.framework.redisson.config;

import cn.iocoder.zj.module.monitor.framework.redisson.RedisLockClient;
import cn.iocoder.zj.module.monitor.framework.redisson.RedisLockClientImpl;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RedisLockConfig {

    @Bean
    public RedisLockClient redisLockClient(RedissonClient redissonClient) {
        return new RedisLockClientImpl(redissonClient);
    }
}