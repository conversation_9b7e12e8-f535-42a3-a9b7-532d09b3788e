package cn.iocoder.zj.module.monitor.controller.admin.storageinfo.vo;

import cn.iocoder.zj.module.monitor.controller.admin.tags.vo.TagsRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Date;
import java.util.List;

@Schema(description = "管理后台 - 存储设备信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class StorageInfoRespVO extends StorageInfoBaseVO {

    @Schema(description = "逐渐", required = true)
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

    @Schema(description = "告警配置id")
    private Collection<Long> alarmIds;

    @Schema(description = "虚拟使用率")
    private BigDecimal virtualUtilization ;

    @Schema(description = "物理使用量")
    private BigDecimal physicalUsage ;

    @Schema(description = "标签信息")
    private List<TagsRespVO> tags;
}
