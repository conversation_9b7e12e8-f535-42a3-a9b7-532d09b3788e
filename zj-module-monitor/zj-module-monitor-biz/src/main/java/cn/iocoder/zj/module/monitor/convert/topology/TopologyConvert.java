package cn.iocoder.zj.module.monitor.convert.topology;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.monitor.api.topology.dto.TopologyDTO;
import cn.iocoder.zj.module.monitor.controller.admin.topology.vo.TopologyCreateReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.topology.vo.TopologyExcelVO;
import cn.iocoder.zj.module.monitor.controller.admin.topology.vo.TopologyRespVO;
import cn.iocoder.zj.module.monitor.controller.admin.topology.vo.TopologyUpdateReqVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.topology.TopologyDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 监控资源拓扑图 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TopologyConvert {

    TopologyConvert INSTANCE = Mappers.getMapper(TopologyConvert.class);

    TopologyDO convert(TopologyCreateReqVO bean);

    TopologyDO convert(TopologyUpdateReqVO bean);

    TopologyRespVO convert(TopologyDO bean);

    List<TopologyRespVO> convertList(List<TopologyDO> list);

    PageResult<TopologyRespVO> convertPage(PageResult<TopologyDO> page);

    List<TopologyExcelVO> convertList02(List<TopologyDO> list);

    List<TopologyDTO> DOConvertToDTO(List<TopologyDO> list);

    List<TopologyDO> DTOConvertoDo(List<TopologyDTO> topologyUpdateDTOS);
}
