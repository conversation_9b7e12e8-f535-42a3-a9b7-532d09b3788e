package cn.iocoder.zj.module.monitor.controller.admin.hardwareinfo.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 硬件设施基本信息 Excel 导出 Request VO，参数和 HardwareInfoPageReqVO 是一致的")
@Data
public class HardwareInfoExportReqVO {

    @Schema(description = "主机id")
    private String uuid;

    @Schema(description = "主机名称")
    private String name;

    @Schema(description = "宿主机状态，包括：	Enabled	Disabled	PreMaintenance	Maintenance")
    private String state;

    @Schema(description = "ip")
    private String ip;

    @Schema(description = "Connecting，	Connected，	Disconnected")
    private String status;

    @Schema(description = "集群id")
    private String clusterUuid;

    @Schema(description = "集群名称")
    private String clusterName;

    @Schema(description = "cpu总容量")
    private Long totalCpuCapacity;

    @Schema(description = "cpu可用容量")
    private Long availableCpuCapacity;

    @Schema(description = "cpu 插槽")
    private Integer cpuSockets;

    @Schema(description = "CPU架构")
    private String architecture;

    @Schema(description = "分配cpu")
    private Integer cpuNum;

    @Schema(description = "内存总容量")
    private Long totalMemoryCapacity;

    @Schema(description = "内存可用容量")
    private Long availableMemoryCapacity;

    @Schema(description = "平台ID")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "地区id")
    private Long regionId;

    @Schema(description = "租户ID")
    private List<String> tenantId;

    @Schema(required = false,description = "开始时间",example = "2023-06-07")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String startTime;

    @Schema(required = false,description = "结束时间",example = "2023-06-07")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String endTime;

    @Schema(required = false,description = "主机tagId逗号拼接,1,2,3")
    private String tagIds;
}
