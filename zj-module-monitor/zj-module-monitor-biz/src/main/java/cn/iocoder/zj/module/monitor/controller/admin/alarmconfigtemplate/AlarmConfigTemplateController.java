package cn.iocoder.zj.module.monitor.controller.admin.alarmconfigtemplate;

import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo.AlarmConfigCreateReqVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmconfig.AlarmConfigDO;
import cn.iocoder.zj.module.monitor.dal.mysql.alarmconfig.AlarmConfigMapper;
import cn.iocoder.zj.module.monitor.service.alarmconfig.AlarmConfigService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.monitor.controller.admin.alarmconfigtemplate.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmconfigtemplate.AlarmConfigTemplateDO;
import cn.iocoder.zj.module.monitor.convert.alarmconfigtemplate.AlarmConfigTemplateConvert;
import cn.iocoder.zj.module.monitor.service.alarmconfigtemplate.AlarmConfigTemplateService;

@Tag(name = "管理后台 - 告警配置模板")
@RestController
@RequestMapping("/monitor/alarm-config-template")
@Validated
public class AlarmConfigTemplateController {

    @Resource
    private AlarmConfigTemplateService alarmConfigTemplateService;

    @Resource
    private AlarmConfigService alarmConfigService;

    @Resource
    private AlarmConfigMapper alarmConfigMapper;

    @PostMapping("/create")
    @Operation(summary = "创建告警配置模板")
//    @PreAuthorize("@ss.hasPermission('monitor:alarm-config-template:create')")
    public CommonResult<Long> createAlarmConfigTemplate(@Valid @RequestBody AlarmConfigTemplateCreateReqVO createReqVO) {
        return success(alarmConfigTemplateService.createAlarmConfigTemplate(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新告警配置模板")
//    @PreAuthorize("@ss.hasPermission('monitor:alarm-config-template:update')")
    public CommonResult<Boolean> updateAlarmConfigTemplate(@Valid @RequestBody AlarmConfigTemplateUpdateReqVO updateReqVO) {
        alarmConfigTemplateService.updateAlarmConfigTemplate(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除告警配置模板")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('monitor:alarm-config-template:delete')")
    public CommonResult<Boolean> deleteAlarmConfigTemplate(@RequestParam("id") Long id) {
        alarmConfigTemplateService.deleteAlarmConfigTemplate(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得告警配置模板")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('monitor:alarm-config-template:query')")
    public CommonResult<AlarmConfigTemplateRespVO> getAlarmConfigTemplate(@RequestParam("id") Long id) {
        AlarmConfigTemplateDO alarmConfigTemplate = alarmConfigTemplateService.getAlarmConfigTemplate(id);
        return success(AlarmConfigTemplateConvert.INSTANCE.convert(alarmConfigTemplate));
    }

    @GetMapping("/list")
    @Operation(summary = "获得告警配置模板列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('monitor:alarm-config-template:query')")
    public CommonResult<List<AlarmConfigTemplateRespVO>> getAlarmConfigTemplateList(@RequestParam("ids") Collection<Long> ids) {
        List<AlarmConfigTemplateDO> list = alarmConfigTemplateService.getAlarmConfigTemplateList(ids);
        return success(AlarmConfigTemplateConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得告警配置模板分页")
//    @PreAuthorize("@ss.hasPermission('monitor:alarm-config-template:query')")
    public CommonResult<PageResult<AlarmConfigTemplateRespVO>> getAlarmConfigTemplatePage(@Valid AlarmConfigTemplatePageReqVO pageVO) {
        PageResult<AlarmConfigTemplateDO> pageResult = alarmConfigTemplateService.getAlarmConfigTemplatePage(pageVO);
        return success(AlarmConfigTemplateConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出告警配置模板 Excel")
    @PreAuthorize("@ss.hasPermission('monitor:alarm-config-template:export')")
    @OperateLog(type = EXPORT)
    public void exportAlarmConfigTemplateExcel(@Valid AlarmConfigTemplateExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<AlarmConfigTemplateDO> list = alarmConfigTemplateService.getAlarmConfigTemplateList(exportReqVO);
        // 导出 Excel
        List<AlarmConfigTemplateExcelVO> datas = AlarmConfigTemplateConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "告警配置模板.xls", "数据", AlarmConfigTemplateExcelVO.class, datas);
    }

    @PostMapping("/import-template")
    @Operation(summary = "模板导入到告警模板")
//    @PreAuthorize("@ss.hasPermission('monitor:alarm-config-template:import')")
    public CommonResult<Boolean> importTemplate(@Valid @RequestBody AlarmConfigTemplateImportTemplateReqVO importTemplateReqVO) {
        LoginUser user = SecurityFrameworkUtils.getLoginUser();
        for (Long id : importTemplateReqVO.getIds()) {
            AlarmConfigTemplateDO find = alarmConfigTemplateService.getAlarmConfigTemplate(id);
            Long count = alarmConfigMapper.selectCount(
                    new LambdaQueryWrapper<>(AlarmConfigDO.class)
                            .eq(AlarmConfigDO::getSourceType, find.getSourceType())
                            .eq(AlarmConfigDO::getDictLabelValue, find.getDictLabelValue())
                            .eq(AlarmConfigDO::getAlarmRule, find.getAlarmRule())
                            .eq(AlarmConfigDO::getAlarmLevel, find.getAlarmLevel())
                            .eq(AlarmConfigDO::getTenantId, user != null ? user.getTenantId() : TenantContextHolder.getTenantId())
            );
            if (count == 0L) {
                AlarmConfigCreateReqVO createReqVO = new AlarmConfigCreateReqVO();
                createReqVO.setAlarmName(find.getAlarmName());
                createReqVO.setDescription(find.getDescription());
                createReqVO.setSourceType(find.getSourceType());
                createReqVO.setDictLabelName(find.getDictLabelName());
                createReqVO.setDictLabelType(find.getDictLabelType());
                createReqVO.setDictLabelValue(find.getDictLabelValue());
                createReqVO.setAlarmRule(find.getAlarmRule());
                createReqVO.setAlarmVal(find.getAlarmVal());
                createReqVO.setUnit(find.getUnit());
                createReqVO.setAlarmTime(find.getAlarmTime());
                createReqVO.setAlarmLevel(find.getAlarmLevel());
                createReqVO.setUnitType(find.getUnitType());
                alarmConfigService.createAlarmConfig(createReqVO);
            }
        }
        return success(true);
    }
}
