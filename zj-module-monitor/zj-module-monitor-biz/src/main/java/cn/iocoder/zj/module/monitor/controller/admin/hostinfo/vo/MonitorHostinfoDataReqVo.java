package cn.iocoder.zj.module.monitor.controller.admin.hostinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class MonitorHostinfoDataReqVo {
    @Schema(required = true, description = "数据的类型，‘CPUAverageUsedUtilization’代表均值，‘CPUUsedUtilization’代表单核数据，传多个时用逗号拼接")
    private String metricName;

    @Schema(required = true, description = "监控对象分类，传多个时用逗号拼接")
    private String vmType;

    @Schema(required = true, description = "开始时间")
    private Long startTime;

    @Schema(required = true, description = "结束时间")
    private Long endTime;

    @Schema(required = true, description = "主机UUID")
    private String uuid;

    @Schema(required = false, description = "查询方式，sample采样查询；precise精准")
    private String searchWay;
}
