package cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation;

import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmhostrelation.AlarmHostRelationDO;
import cn.iocoder.zj.module.monitor.convert.alarmhostrelation.AlarmHostRelationConvert;
import cn.iocoder.zj.module.monitor.service.alarmhostrelation.AlarmHostRelationService;

@Tag(name = "管理后台 - 告警配置与云主机关联关系")
@RestController
@RequestMapping("/monitor/alarm-host-relation")
@Validated
public class AlarmHostRelationController {

    @Resource
    private AlarmHostRelationService alarmHostRelationService;

    @PutMapping("/update")
    @Operation(summary = "更新告警配置与云主机关联关系")
    @TenantIgnore
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateAlarmHostRelation(@RequestBody RelationRqeVO reqVO) {
        alarmHostRelationService.updateAlarmHostRelation(reqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除告警配置与云主机关联关系")
    @Parameter(name = "id", description = "编号", required = true)
    @OperateLog(type = DELETE)
    public CommonResult<Boolean> deleteAlarmHostRelation(@RequestParam("id") Long id) {
        alarmHostRelationService.deleteAlarmHostRelation(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得告警配置与云主机关联关系")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AlarmHostRelationRespVO> getAlarmHostRelation(@RequestParam("id") Long id) {
        AlarmHostRelationDO alarmHostRelation = alarmHostRelationService.getAlarmHostRelation(id);
        return success(AlarmHostRelationConvert.INSTANCE.convert(alarmHostRelation));
    }

    @GetMapping("/list")
    @Operation(summary = "获得告警配置与云主机关联关系列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    public CommonResult<List<AlarmHostRelationRespVO>> getAlarmHostRelationList(@RequestParam("ids") Collection<Long> ids) {
        List<AlarmHostRelationDO> list = alarmHostRelationService.getAlarmHostRelationList(ids);
        return success(AlarmHostRelationConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得告警配置与云主机关联关系分页")
    public CommonResult<PageResult<AlarmHostRelationRespVO>> getAlarmHostRelationPage(@Valid AlarmHostRelationPageReqVO pageVO) {
        PageResult<AlarmHostRelationDO> pageResult = alarmHostRelationService.getAlarmHostRelationPage(pageVO);
        return success(AlarmHostRelationConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出告警配置与云主机关联关系 Excel")
    @OperateLog(type = EXPORT)
    public void exportAlarmHostRelationExcel(@Valid AlarmHostRelationExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<AlarmHostRelationDO> list = alarmHostRelationService.getAlarmHostRelationList(exportReqVO);
        // 导出 Excel
        List<AlarmHostRelationExcelVO> datas = AlarmHostRelationConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "告警配置与云主机关联关系.xls", "数据", AlarmHostRelationExcelVO.class, datas);
    }

    @PostMapping("/hostSetAlarms")
    @Operation(summary = "为设备批量添加告警配置")
    @TenantIgnore
    @PreAuthorize("@ss.hasPermission('monitor:alarm-relation:create')")
    @OperateLog(type = CREATE)
    public CommonResult<Long> hostSetAlarms(@RequestBody RelationRqeVO reqVO) {
        return success(alarmHostRelationService.hostSetAlarms(reqVO));
    }
}
