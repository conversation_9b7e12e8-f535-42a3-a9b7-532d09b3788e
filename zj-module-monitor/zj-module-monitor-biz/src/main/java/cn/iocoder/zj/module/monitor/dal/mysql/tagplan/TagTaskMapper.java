package cn.iocoder.zj.module.monitor.dal.mysql.tagplan;

import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.tagtask.job.model.TagTaskModel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 */
@Mapper
public interface TagTaskMapper extends BaseMapper<TagTaskModel> {

    @TenantIgnore
    int updateTasByJobId(TagTaskModel taskCacheModel);
}
