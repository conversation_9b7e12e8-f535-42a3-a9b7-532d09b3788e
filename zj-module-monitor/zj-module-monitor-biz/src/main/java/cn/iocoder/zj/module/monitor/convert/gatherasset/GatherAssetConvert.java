package cn.iocoder.zj.module.monitor.convert.gatherasset;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.api.asset.dto.GatherAssetRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.gatherasset.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.gatherasset.GatherAssetDO;

/**
 * 租户资产 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface GatherAssetConvert {

    GatherAssetConvert INSTANCE = Mappers.getMapper(GatherAssetConvert.class);

    GatherAssetDO convert(GatherAssetCreateReqVO bean);

    GatherAssetDO convert(GatherAssetUpdateReqVO bean);

    GatherAssetRespVO convert(GatherAssetDO bean);

    List<GatherAssetRespVO> convertList(List<GatherAssetDO> list);

    PageResult<GatherAssetRespVO> convertPage(PageResult<GatherAssetDO> page);

    List<GatherAssetExcelVO> convertList02(List<GatherAssetDO> list);

    List<GatherAssetRespDTO> convertDto(List<GatherAssetDO> assetList);

    List<GatherAssetDO> convertCreateList(List<GatherAssetRespDTO> reqDTO);
}
