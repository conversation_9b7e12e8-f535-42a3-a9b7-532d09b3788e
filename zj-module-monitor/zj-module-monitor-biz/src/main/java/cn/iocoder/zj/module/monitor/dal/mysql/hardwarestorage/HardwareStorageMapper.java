package cn.iocoder.zj.module.monitor.dal.mysql.hardwarestorage;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.monitor.api.hardwarenic.HardWareNicRespDTO;
import cn.iocoder.zj.module.monitor.api.hardwarestorage.HardWareStorageRespDTO;
import cn.iocoder.zj.module.monitor.dal.dataobject.hardwarestorage.HardwareStorageDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.monitor.controller.admin.hardwarestorage.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 宿主机与存储关联 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface HardwareStorageMapper extends BaseMapperX<HardwareStorageDO> {

    default PageResult<HardwareStorageDO> selectPage(HardwareStoragePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<HardwareStorageDO>()
                .eqIfPresent(HardwareStorageDO::getHardwareId, reqVO.getHardwareId())
                .eqIfPresent(HardwareStorageDO::getHardwareUuid, reqVO.getHardwareUuid())
                .eqIfPresent(HardwareStorageDO::getStorageId, reqVO.getStorageId())
                .eqIfPresent(HardwareStorageDO::getStorageUuid, reqVO.getStorageUuid())
                .betweenIfPresent(HardwareStorageDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(HardwareStorageDO::getId));
    }

    default List<HardwareStorageDO> selectList(HardwareStorageExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<HardwareStorageDO>()
                .eqIfPresent(HardwareStorageDO::getHardwareId, reqVO.getHardwareId())
                .eqIfPresent(HardwareStorageDO::getHardwareUuid, reqVO.getHardwareUuid())
                .eqIfPresent(HardwareStorageDO::getStorageId, reqVO.getStorageId())
                .eqIfPresent(HardwareStorageDO::getStorageUuid, reqVO.getStorageUuid())
                .betweenIfPresent(HardwareStorageDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(HardwareStorageDO::getId));
    }

    List<HardWareStorageRespDTO> getHardwareStorageByPlatformId(@Param("platformId")Long platformId);

    List<String> getStorageIds(@Param("hardwareUuid") String hardwareUuid);

    Integer getHareWareCountByStorageUuid(@Param("storageUuid")String storageUuid);
}
