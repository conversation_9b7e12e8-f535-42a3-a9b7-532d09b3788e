package cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.convert;


import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BulletinVpcStateConvert implements Converter<Object> {


    @Override
    public Class<?> supportJavaTypeKey() {
        throw new UnsupportedOperationException("暂不支持，也不需要");
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        throw new UnsupportedOperationException("暂不支持，也不需要");
    }

    @Override
    public WriteCellData<String> convertToExcelData(Object object, ExcelContentProperty contentProperty,
                                                    GlobalConfiguration globalConfiguration) {
        // 空时，返回空
        if (object == null) {
            return new WriteCellData<>("");
        }

        String value = String.valueOf(object);
        String convertde = "未知";
        switch (value) {
            case "Running":
                convertde = "运行中";
                break;
            case "Maintenance":
                convertde = "维护";
                break;
            case "Disconnected":
                convertde = "未连接";
                break;
            case "Enabled":
                convertde = "启用";
                break;
            case "Stopped":
                convertde = "禁用";
                break;
            case "PreMaintenance":
                convertde = "预维护";
                break;

        }

        // 生成 Excel 小表格
        return new WriteCellData<>(convertde);
    }
}