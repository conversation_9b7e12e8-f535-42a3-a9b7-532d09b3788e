package cn.iocoder.zj.module.monitor.dal.mysql.gatherdevice;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.monitor.dal.dataobject.gatherdevice.GatherDeviceDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.monitor.controller.admin.gatherdevice.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 采集设备 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface GatherDeviceMapper extends BaseMapperX<GatherDeviceDO> {

    default PageResult<GatherDeviceDO> selectPage(GatherDevicePageReqVO reqVO, boolean admin, List<Map> platform) {
        if (admin){
            return selectPage(reqVO, new LambdaQueryWrapperX<GatherDeviceDO>()
                    .eqIfPresent(GatherDeviceDO::getUuid, reqVO.getUuid())
                    .likeIfPresent(GatherDeviceDO::getGatherName, reqVO.getGatherName())
                    .likeIfPresent(GatherDeviceDO::getGatherIp,reqVO.getGatherIp())
                    .eqIfPresent(GatherDeviceDO::getType, reqVO.getType())
                    .betweenIfPresent(GatherDeviceDO::getCreateTime, reqVO.getCreateTime())
                    .eqIfPresent(GatherDeviceDO::getPlatformId,reqVO.getPlatformId())
                    .orderByDesc(GatherDeviceDO::getId));
        }else {
            List<String> data = new ArrayList<>();
            if (platform.size()>0){
                for (Map map : platform){
                    data.add(map.get("platformId").toString());
                }
            }

            LambdaQueryWrapperX<GatherDeviceDO> queryWrapperX = new LambdaQueryWrapperX<GatherDeviceDO>()
                    .eqIfPresent(GatherDeviceDO::getUuid, reqVO.getUuid())
                    .likeIfPresent(GatherDeviceDO::getGatherName, reqVO.getGatherName())
                    .likeIfPresent(GatherDeviceDO::getGatherIp,reqVO.getGatherIp())
                    .eqIfPresent(GatherDeviceDO::getType, reqVO.getType())
                    .betweenIfPresent(GatherDeviceDO::getCreateTime, reqVO.getCreateTime())
                    .orderByDesc(GatherDeviceDO::getId);

            if (data.size()>0){
                queryWrapperX = (LambdaQueryWrapperX<GatherDeviceDO>) queryWrapperX.in(GatherDeviceDO::getPlatformId, data);
            }
            return selectPage(reqVO,queryWrapperX);

        }

    }

    default List<GatherDeviceDO> selectList(GatherDeviceExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<GatherDeviceDO>()
                .eqIfPresent(GatherDeviceDO::getUuid, reqVO.getUuid())
                .eqIfPresent(GatherDeviceDO::getGatherIp, reqVO.getGatherIp())
                .likeIfPresent(GatherDeviceDO::getGatherName, reqVO.getGatherName())
                .eqIfPresent(GatherDeviceDO::getType, reqVO.getType())
                .betweenIfPresent(GatherDeviceDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(GatherDeviceDO::getPlatformId,reqVO.getPlatformId())
                .orderByDesc(GatherDeviceDO::getId));
    }

    void insertDevice(@Param("uuid") String uuid, @Param("gatherIp") String gatherIp, @Param("gatherName") String gatherName);

    int selectCountDeviceByUUid(@Param("uuid") String uuid);

    default List<GatherDeviceDO> deviceSelect(boolean admin, List<Map> platform){
        if (admin){
            LambdaQueryWrapperX<GatherDeviceDO> queryWrapperX = new LambdaQueryWrapperX<GatherDeviceDO>();
            queryWrapperX = (LambdaQueryWrapperX<GatherDeviceDO>) queryWrapperX.isNotNull(GatherDeviceDO::getPlatformId);
            return selectList(queryWrapperX);
        }else {
            List<String> data = new ArrayList<>();
            if (platform.size()>0){
                for (Map map : platform){
                    data.add(map.get("platformId").toString());
                }
            }
            LambdaQueryWrapperX<GatherDeviceDO> queryWrapperX = new LambdaQueryWrapperX<GatherDeviceDO>();
            if (data.size()>0){
                queryWrapperX = (LambdaQueryWrapperX<GatherDeviceDO>) queryWrapperX.isNotNull(GatherDeviceDO::getPlatformId);
                queryWrapperX = (LambdaQueryWrapperX<GatherDeviceDO>) queryWrapperX.in(GatherDeviceDO::getPlatformId, data);
            }
            return selectList(queryWrapperX);
        }
    }

    Long selectCountByPlatformId(@Param("platformId") Long platformId);

    List<String> getGatherDeviceByIdList();

    void updateOnlineType(@Param("uuid") List<String> uuid, @Param("type") int type);
}
