package cn.iocoder.zj.module.monitor.convert.networkl3;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL3DTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.networkl3.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkl3.NetworkL3DO;

/**
 * 三级网络资源 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface NetworkL3Convert {

    NetworkL3Convert INSTANCE = Mappers.getMapper(NetworkL3Convert.class);

    NetworkL3DO convert(NetworkL3CreateReqVO bean);

    NetworkL3DO convert(NetworkL3UpdateReqVO bean);

    NetworkL3RespVO convert(NetworkL3DO bean);

    List<NetworkL3RespVO> convertList(List<NetworkL3DO> list);

    PageResult<NetworkL3RespVO> convertPage(PageResult<NetworkL3DO> page);

    List<NetworkL3ExcelVO> convertList02(List<NetworkL3DO> list);

    List<NetworkL3DO> convertCreateList(List<NetWorkL3DTO> reqDTO);

    List<NetWorkL3DTO> convertListDoToDto(List<NetworkL3DO> netWorkL3List);
}
