package cn.iocoder.zj.module.monitor.controller.admin.topologyrelation.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 拓扑图关系分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TopologyRelationPageReqVO extends PageParam {

    @Schema(description = "拓扑图id")
    private Long topologyId;

    @Schema(description = "监控id")
    private String monitorId;

    @Schema(description = "监控设备名称")
    private String monitorName;

    @Schema(description = "监控设备接口名称")
    private String monitorInterfaces;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
