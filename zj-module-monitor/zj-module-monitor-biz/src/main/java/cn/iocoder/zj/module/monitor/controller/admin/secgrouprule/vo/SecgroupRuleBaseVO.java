package cn.iocoder.zj.module.monitor.controller.admin.secgrouprule.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 端口组规则 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SecgroupRuleBaseVO {

    @Schema(description = "uuid")
    private String uuid;

    @Schema(description = "状态", required = true)
    @NotNull(message = "状态不能为空")
    private String status;

    @Schema(description = "安全组uuid")
    private String secgroupUuid;

    @Schema(description = "优先级")
    private Integer priority;

    @Schema(description = "协议")
    private String protocol;

    @Schema(description = "端口")
    private String ports;

    @Schema(description = "方向")
    private String direction;

    @Schema(description = "cidr")
    private String cidr;

    @Schema(description = "策略")
    private String action;

    @Schema(description = "备注")
    private String description;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

}
