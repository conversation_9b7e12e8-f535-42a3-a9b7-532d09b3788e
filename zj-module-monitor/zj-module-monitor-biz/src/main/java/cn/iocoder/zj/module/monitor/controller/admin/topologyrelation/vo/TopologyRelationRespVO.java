package cn.iocoder.zj.module.monitor.controller.admin.topologyrelation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 拓扑图关系 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TopologyRelationRespVO extends TopologyRelationBaseVO {

    @Schema(description = "主键", required = true)
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
