package cn.iocoder.zj.module.monitor.dal.mysql.alarminfo;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmconfig.AlarmConfigDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarminfo.AlarmInfoDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.monitor.controller.admin.alarminfo.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.hertzbeat.common.entity.alerter.AlertDefine;
import org.apache.hertzbeat.common.entity.alerter.AlertRespVo;

/**
 * 监控告警详情 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AlarmInfoMapper extends BaseMapperX<AlarmInfoDO> {

    default PageResult<AlarmInfoDO> selectPage(AlarmInfoPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AlarmInfoDO>()
                .eqIfPresent(AlarmInfoDO::getProcessors, reqVO.getProcessors())
                .betweenIfPresent(AlarmInfoDO::getProcessingTime, reqVO.getProcessingTime())
                .eqIfPresent(AlarmInfoDO::getProcessingDetails, reqVO.getProcessingDetails())
                .eqIfPresent(AlarmInfoDO::getFileAddress, reqVO.getFileAddress())
                .eqIfPresent(AlarmInfoDO::getProcessingType, reqVO.getProcessingType())
                .eqIfPresent(AlarmInfoDO::getAlarmId, reqVO.getAlarmId())
                .betweenIfPresent(AlarmInfoDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(AlarmInfoDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(AlarmInfoDO::getPlatformName, reqVO.getPlatformName())
                .orderByDesc(AlarmInfoDO::getId));
    }

    default List<AlarmInfoDO> selectList(AlarmInfoExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AlarmInfoDO>()
                .eqIfPresent(AlarmInfoDO::getProcessors, reqVO.getProcessors())
                .betweenIfPresent(AlarmInfoDO::getProcessingTime, reqVO.getProcessingTime())
                .eqIfPresent(AlarmInfoDO::getProcessingDetails, reqVO.getProcessingDetails())
                .eqIfPresent(AlarmInfoDO::getFileAddress, reqVO.getFileAddress())
                .eqIfPresent(AlarmInfoDO::getProcessingType, reqVO.getProcessingType())
                .eqIfPresent(AlarmInfoDO::getAlarmId, reqVO.getAlarmId())
                .betweenIfPresent(AlarmInfoDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(AlarmInfoDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(AlarmInfoDO::getPlatformName, reqVO.getPlatformName())
                .orderByDesc(AlarmInfoDO::getId));
    }

    @DS("doris")
    @TenantIgnore
    AlertRespVo getAlertInfoById(@Param("id") Long id);

    @TenantIgnore
    AlarmInfoDO getAlarmInfoByalarmId(@Param("alarmId") Long alarmId);

    @TenantIgnore
    AlertDefine getAlertDefineById(@Param("alarmId") String alarmId);

    @TenantIgnore
    AlarmConfigDO getAlarmConfigDoById(@Param("alarmId") String alarmId);

    @DS("doris")
    @TenantIgnore
    void updateisSolvedByAlarmId(@Param("alarmId") Long alarmId);

    @DS("doris")
    @TenantIgnore
    List<AlertRespVo> getAlertInfoByMonitorId(@Param("monitorId") List<String> monitorId);
}
