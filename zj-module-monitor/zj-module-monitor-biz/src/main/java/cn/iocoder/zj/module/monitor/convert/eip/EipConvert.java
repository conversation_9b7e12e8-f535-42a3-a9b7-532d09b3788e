package cn.iocoder.zj.module.monitor.convert.eip;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.api.eip.dto.EipCreateReqDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.eip.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.eip.EipDO;

/**
 * 弹性公网 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface EipConvert {

    EipConvert INSTANCE = Mappers.getMapper(EipConvert.class);

    EipDO convert(EipCreateReqVO bean);

    EipDO convert(EipUpdateReqVO bean);

    EipRespVO convert(EipDO bean);

    List<EipRespVO> convertList(List<EipDO> list);

    PageResult<EipRespVO> convertPage(PageResult<EipDO> page);

    List<EipExcelVO> convertList02(List<EipDO> list);

    List<EipDO> convertCreateList(List<EipCreateReqDto> list);

    List<EipCreateReqDto> convertDoToCreateDtoList(List<EipDO> eipList);
}
