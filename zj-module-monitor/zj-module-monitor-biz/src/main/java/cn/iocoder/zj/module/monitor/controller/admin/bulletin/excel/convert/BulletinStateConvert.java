package cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.convert;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import lombok.extern.slf4j.Slf4j;

/**
 * Excel 云主机、宿主机状态转换
 */
@Slf4j
public class BulletinStateConvert implements Converter<Object> {


    @Override
    public Class<?> supportJavaTypeKey() {
        throw new UnsupportedOperationException("暂不支持，也不需要");
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        throw new UnsupportedOperationException("暂不支持，也不需要");
    }

    @Override
    public WriteCellData<String> convertToExcelData(Object object, ExcelContentProperty contentProperty,
                                                    GlobalConfiguration globalConfiguration) {
        // 空时，返回空
        if (object == null) {
            return new WriteCellData<>("");
        }

        String value = String.valueOf(object);
        String convertde = "";
        switch (value) {
            case "Connecting":
                convertde = "正在连接";
                break;
            case "Connected":
                convertde = "已连接";
                break;
            case "Disconnected":
                convertde = "未连接";
                break;
            case "Enabled":
                convertde = "启用";
                break;
            case "Disabled":
                convertde = "停用";
                break;
            case "PreMaintenance":
                convertde = "预维护";
                break;
            case "Maintenance":
                convertde = "维护";
                break;
            case "Created":
                convertde = "已创建";
                break;
            case "Starting":
                convertde = "启动中";
                break;
            case "Running":
                convertde = "运行中";
                break;
            case "Stopping":
                convertde = "停止中";
                break;
            case "Stopped":
                convertde = "停止";
                break;
            case "Rebooting":
                convertde = "重启中";
                break;
            case "Destroying":
                convertde = "销毁中";
                break;
            case "Destroyed":
                convertde = "已销毁";
                break;
            case "Expunging":
                convertde = "删除中";
                break;
            case "Pausing":
                convertde = "暂停中";
                break;
            case "Resuming":
                convertde = "恢复中";
                break;
            case "VolumeMigrating":
                convertde = "卷迁移中";
                break;
            case "Migrating":
                convertde = "迁移中";
                break;
            case "Paused":
                convertde = "已暂停";
                break;
            case "Unknown":
                convertde = "未知";
                break;
            case "wait":
                convertde = "待审核";
                break;
            case "solved":
                convertde = "已处理";
                break;
            case "consent-unresolved":
                convertde = "审核通过";
                break;
            case "reject":
                convertde = "驳回";
                break;

        }

        // 生成 Excel 小表格
        return new WriteCellData<>(convertde);
    }
}