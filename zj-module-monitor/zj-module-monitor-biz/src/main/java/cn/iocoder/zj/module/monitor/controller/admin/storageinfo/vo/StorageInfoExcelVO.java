package cn.iocoder.zj.module.monitor.controller.admin.storageinfo.vo;

import cn.iocoder.zj.framework.excel.core.convert.ByteConvent;
import cn.iocoder.zj.framework.excel.core.convert.StateConvert;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 存储设备信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class StorageInfoExcelVO {

    @ExcelProperty("存储名称")
    private String name;

    @ExcelProperty("URL")
    private String url;

    @ExcelProperty(value = "启用状态",converter = StateConvert.class)
    private String state;

    @ExcelProperty("类型")
    private String type;

    @ExcelProperty(value = "就绪状态",converter = StateConvert.class)
    private String status;

    @ExcelProperty("容量使用率")
    private BigDecimal capacityUtilization;

    @ExcelProperty(value = "总容量",converter = ByteConvent.class)
    private BigDecimal totalCapacity;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private Date createTime;

    @ExcelProperty("地区编号")
    private Long regionId;

    @ExcelProperty("地区")
    private String regionName;

}
