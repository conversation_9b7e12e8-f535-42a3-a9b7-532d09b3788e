package cn.iocoder.zj.module.monitor.controller.admin.secgroup;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.monitor.controller.admin.secgroup.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.secgroup.SecgroupDO;
import cn.iocoder.zj.module.monitor.convert.secgroup.SecgroupConvert;
import cn.iocoder.zj.module.monitor.service.secgroup.SecgroupService;

@Tag(name = "管理后台 - 安全组")
@RestController
@RequestMapping("/monitor/secgroup")
@Validated
public class SecgroupController {

    @Resource
    private SecgroupService secgroupService;

    @PostMapping("/create")
    @Operation(summary = "创建安全组")
    //@PreAuthorize("@ss.hasPermission('monitor:secgroup:create')")
    public CommonResult<Long> createSecgroup(@Valid @RequestBody SecgroupCreateReqVO createReqVO) {
        return success(secgroupService.createSecgroup(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新安全组")
    //@PreAuthorize("@ss.hasPermission('monitor:secgroup:update')")
    public CommonResult<Boolean> updateSecgroup(@Valid @RequestBody SecgroupUpdateReqVO updateReqVO) {
        secgroupService.updateSecgroup(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除安全组")
    @Parameter(name = "id", description = "编号", required = true)
    //@PreAuthorize("@ss.hasPermission('monitor:secgroup:delete')")
    public CommonResult<Boolean> deleteSecgroup(@RequestParam("id") Long id) {
        secgroupService.deleteSecgroup(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得安全组")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('monitor:secgroup:query')")
    public CommonResult<SecgroupRespVO> getSecgroup(@RequestParam("id") Long id) {
        SecgroupDO secgroup = secgroupService.getSecgroup(id);
        return success(SecgroupConvert.INSTANCE.convert(secgroup));
    }

    @GetMapping("/list")
    @Operation(summary = "获得安全组列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    //@PreAuthorize("@ss.hasPermission('monitor:secgroup:query')")
    public CommonResult<List<SecgroupRespVO>> getSecgroupList(@RequestParam("ids") Collection<Long> ids) {
        List<SecgroupDO> list = secgroupService.getSecgroupList(ids);
        return success(SecgroupConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得安全组分页")
    //@PreAuthorize("@ss.hasPermission('monitor:secgroup:query')")
    public CommonResult<PageResult<SecgroupRespVO>> getSecgroupPage(@Valid SecgroupPageReqVO pageVO) {
        PageResult<SecgroupDO> pageResult = secgroupService.getSecgroupPage(pageVO);
        return success(SecgroupConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出安全组 Excel")
    //@PreAuthorize("@ss.hasPermission('monitor:secgroup:export')")
    @OperateLog(type = EXPORT)
    public void exportSecgroupExcel(@Valid SecgroupExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<SecgroupDO> list = secgroupService.getSecgroupList(exportReqVO);
        // 导出 Excel
        List<SecgroupExcelVO> datas = SecgroupConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "安全组.xls", "数据", SecgroupExcelVO.class, datas);
    }

}
