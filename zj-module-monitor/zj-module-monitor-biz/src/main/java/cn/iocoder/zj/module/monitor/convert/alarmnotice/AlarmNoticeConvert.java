package cn.iocoder.zj.module.monitor.convert.alarmnotice;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.alarmnotice.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmnotice.AlarmNoticeDO;

/**
 * 告警与通知模板关系 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface AlarmNoticeConvert {

    AlarmNoticeConvert INSTANCE = Mappers.getMapper(AlarmNoticeConvert.class);

    AlarmNoticeDO convert(AlarmNoticeCreateReqVO bean);

    AlarmNoticeDO convert(AlarmNoticeUpdateReqVO bean);

    AlarmNoticeRespVO convert(AlarmNoticeDO bean);

    List<AlarmNoticeRespVO> convertList(List<AlarmNoticeDO> list);

    PageResult<AlarmNoticeRespVO> convertPage(PageResult<AlarmNoticeDO> page);

    List<AlarmNoticeExcelVO> convertList02(List<AlarmNoticeDO> list);

}
