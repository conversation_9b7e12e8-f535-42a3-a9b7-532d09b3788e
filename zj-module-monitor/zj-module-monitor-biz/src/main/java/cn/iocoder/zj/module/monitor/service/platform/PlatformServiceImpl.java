package cn.iocoder.zj.module.monitor.service.platform;

import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeAttachableVmDTO;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkl2.NetworkL2DO;
import cn.iocoder.zj.module.monitor.mq.message.platform.PlatformSendMessage;
import cn.iocoder.zj.module.monitor.service.hardwareinfo.HardwareInfoService;
import cn.iocoder.zj.module.monitor.service.hostinfo.HostInfoService;
import cn.iocoder.zj.module.monitor.service.networkl2.NetworkL2Service;
import cn.iocoder.zj.module.monitor.service.networkl3.NetworkL3Service;
import cn.iocoder.zj.module.monitor.service.networkvpc.NetworkVpcService;
import cn.iocoder.zj.module.monitor.service.storageinfo.StorageInfoService;
import cn.iocoder.zj.module.monitor.service.volumeinfo.VolumeInfoService;
import cn.iocoder.zj.module.monitor.service.volumesnapshot.VolumeSnapshotService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @ClassName : PlatformServiceImpl  //类名
 * @Description :   接收平台删除消息 批量处理垃圾数据//描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/5/10  15:12
 */
@Service
public class PlatformServiceImpl implements PlatformService {

    @Resource
    HostInfoService hostInfoService;
    @Resource
    HardwareInfoService hardwareInfoService;
    @Resource
    NetworkL2Service networkL2Service;
    @Resource
    NetworkL3Service networkL3Service;
    @Resource
    NetworkVpcService networkVpcService;
    @Resource
    StorageInfoService storageInfoService;
    @Resource
    VolumeInfoService volumeInfoService;

    @Resource
    VolumeSnapshotService volumeSnapshotService;


    /**
     * @description: 根据发送过来的消息解析，并删除所有相关平台的数据
     * @param: platformSendMessage
     * @return: void
     * <AUTHOR>
     * @date: 2024/5/11 13:21
     */
    @Override
    @TenantIgnore
    public void doSendPlatform(PlatformSendMessage platformSendMessage) {
        // 删除hzb_monitor 因为没有delete字段 所以用物理删除
        // todo 待处理触发删除同步删除绑定表中数据
        hostInfoService.deletMonitorByPlatformId(platformSendMessage.getPlatformId());

        hostInfoService.deleteHostInfoByplatform(platformSendMessage.getPlatformId());
        hardwareInfoService.deleteHardwareInfoByplatform(platformSendMessage.getPlatformId());
        networkL2Service.deleteNetworkL2Byplatform(platformSendMessage.getPlatformId());
        networkL3Service.deleteNetworkL3Byplatform(platformSendMessage.getPlatformId());
        networkVpcService.deleteNetworkVpcByplatform(platformSendMessage.getPlatformId());
        storageInfoService.deleteStorageInfoByplatform(platformSendMessage.getPlatformId());
        volumeInfoService.delVolumesByplatform(platformSendMessage.getPlatformId());
//        volumeInfoService.delVolumeAttachableVmByplatform(platformSendMessage.getPlatformId());
        volumeSnapshotService.deleteVolumeSnapshotByplatform(platformSendMessage.getPlatformId());
        hostInfoService.deletMonitorAssetByPlatformId(platformSendMessage.getPlatformId());
    }
}
