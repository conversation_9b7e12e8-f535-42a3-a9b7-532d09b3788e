package cn.iocoder.zj.module.monitor.util.AreaName;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.ip.core.Area;
import cn.iocoder.zj.framework.ip.core.utils.AreaUtils;

public class AreaUtil {
    public static String getFullAreaName(Long areaId){
        Area area = AreaUtils.getArea(Integer.parseInt(areaId.toString()));
        if(BeanUtil.isNotEmpty(area) && BeanUtil.isNotEmpty(area.getParent())){
            String provence = area.getParent().getParent().getName();
            String city = area.getParent().getName();
            return provence+"->"+city+"->"+area.getName();
        }else {
            return "";
        }
    }
}
