package cn.iocoder.zj.module.monitor.service.gatherlogdetail;

import java.util.*;
import javax.validation.*;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.controller.admin.gatherlogdetail.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.gatherlogdetail.GatherLogdetailDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 告警日志 Service 接口
 *
 * <AUTHOR>
 */
public interface GatherLogdetailService {

    /**
     * 创建告警日志
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createGatherLogdetail(@Valid GatherLogdetailCreateReqVO createReqVO);

    /**
     * 更新告警日志
     *
     * @param updateReqVO 更新信息
     */
    void updateGatherLogdetail(@Valid GatherLogdetailUpdateReqVO updateReqVO);

    /**
     * 删除告警日志
     *
     * @param id 编号
     */
    void deleteGatherLogdetail(Long id);

    /**
     * 获得告警日志
     *
     * @param id 编号
     * @return 告警日志
     */
    GatherLogdetailRespVO getGatherLogdetail(Long id);

    /**
     * 获得告警日志列表
     *
     * @param ids 编号
     * @return 告警日志列表
     */
    List<GatherLogdetailDO> getGatherLogdetailList(Collection<Long> ids);

    /**
     * 获得告警日志分页
     *
     * @param pageReqVO 分页查询
     * @return 告警日志分页
     */
    PageResult<GatherLogdetailDO> getGatherLogdetailPage(GatherLogdetailPageReqVO pageReqVO);

    /**
     * 获得告警日志列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 告警日志列表
     */
    List<GatherLogdetailDO> getGatherLogdetailList(GatherLogdetailExportReqVO exportReqVO);

    Map reportingLog(String json, String tokenDesc);

    void updateState(Collection<Long> id);

    List<GatherLogdetailDO> alarmNoticeList(Long tenantId);

    PageResult<GatherLogdetailRespVO> getGatherLogDetailPageInfo(GatherLogdetailPageReqVO pageVO);

    List<GatherLogdetailRespVO> getLatestLog();

    void solvedGather(String gatherId);

    void gatherWorkOrder(String gatherId);

    void cleanWorkOrder(String gatherId);

    void updateIsSolved(Collection<Long> ids);
}
