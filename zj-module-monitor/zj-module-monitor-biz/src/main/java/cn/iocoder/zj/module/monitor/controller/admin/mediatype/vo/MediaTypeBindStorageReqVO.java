package cn.iocoder.zj.module.monitor.controller.admin.mediatype.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 介质类型绑定 Request VO")
@Data
public class MediaTypeBindStorageReqVO {

    @Schema(description = "存储介质", required = true)
    @NotNull(message = "存储介质不能为空")
    private Long mediaTypeId;

    @Schema(description = "存储", required = true)
    @NotNull(message = "存储不能为空")
    private String storageIds;
}
