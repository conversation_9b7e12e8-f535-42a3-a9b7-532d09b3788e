package cn.iocoder.zj.module.monitor.api.secgroup;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.secgroup.dto.SecgroupCreateReqDto;
import cn.iocoder.zj.module.monitor.controller.admin.secgroup.vo.SecgroupExportReqVO;
import cn.iocoder.zj.module.monitor.convert.secgroup.SecgroupConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.secgroup.SecgroupDO;
import cn.iocoder.zj.module.monitor.service.secgroup.SecgroupService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class SecgroupApiImpl implements SecgroupApi {
    @Resource
    private SecgroupService secgroupService;

    @Override
    public CommonResult<Boolean> addSecgroups(List<SecgroupCreateReqDto> reqDTO) {
        List<SecgroupDO> list = SecgroupConvert.INSTANCE.convertCreateList(reqDTO);
        secgroupService.createSecgroupList(list);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> updateSecgroups(List<SecgroupCreateReqDto> secgroupCreateReqDtoList) {
        if (!secgroupCreateReqDtoList.isEmpty()) {
            List<SecgroupDO> list = SecgroupConvert.INSTANCE.convertCreateList(secgroupCreateReqDtoList);
            secgroupService.updateSecgroups(list);
        }
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> delSecgroups(List<SecgroupCreateReqDto> secgroupCreateReqDtoList) {
        if (!secgroupCreateReqDtoList.isEmpty()) {
            List<SecgroupDO> list = SecgroupConvert.INSTANCE.convertCreateList(secgroupCreateReqDtoList);
            secgroupService.deleteSecgroups(list);
        }
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<List<SecgroupCreateReqDto>> getSecgroupsByPlatformId(Long platformId) {
        SecgroupExportReqVO reqVo = new SecgroupExportReqVO();
        reqVo.setPlatformId(platformId);
        List<SecgroupCreateReqDto> list = SecgroupConvert.INSTANCE.convertDoToCreateDtoList(secgroupService.getSecgroupList(reqVo));
        return CommonResult.success(list);
    }
}
