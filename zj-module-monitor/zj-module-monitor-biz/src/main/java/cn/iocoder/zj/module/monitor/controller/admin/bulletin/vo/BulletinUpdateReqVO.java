package cn.iocoder.zj.module.monitor.controller.admin.bulletin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 实时报更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BulletinUpdateReqVO extends BulletinBaseVO {

    @Schema(description = "ID", required = true)
    @NotNull(message = "ID不能为空")
    private Long id;

}
