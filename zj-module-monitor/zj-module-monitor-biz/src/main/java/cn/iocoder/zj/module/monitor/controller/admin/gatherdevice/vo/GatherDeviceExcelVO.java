package cn.iocoder.zj.module.monitor.controller.admin.gatherdevice.vo;

import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 采集设备 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class GatherDeviceExcelVO {

    @ExcelProperty("主键id")
    private Long id;

    @ExcelProperty("采集设备uuid")
    private String uuid;

    @ExcelProperty("采集设备ip")
    private String gatherIp;

    @ExcelProperty("采集设备名称")
    private String gatherName;

    @ExcelProperty("0 未绑定 ,1 已绑定")
    private Integer type;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private LocalDateTime createTime;

    @ExcelProperty("租户名称")
    private String tenantName;

}
