package cn.iocoder.zj.module.monitor.service.hostnic;

import java.util.*;
import javax.validation.*;

import cn.iocoder.zj.module.monitor.controller.admin.hostinfo.vo.HostInfoRpcVO;
import cn.iocoder.zj.module.monitor.controller.admin.hostnic.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostnic.HostNicDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 云主机网络 Service 接口
 *
 * <AUTHOR>
 */
public interface HostNicService {

    /**
     * 创建云主机网络
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createHostNic(@Valid HostNicCreateReqVO createReqVO);

    /**
     * 更新云主机网络
     *
     * @param updateReqVO 更新信息
     */
    void updateHostNic(@Valid HostNicUpdateReqVO updateReqVO);

    /**
     * 删除云主机网络
     *
     * @param id 编号
     */
    void deleteHostNic(Long id);

    /**
     * 获得云主机网络
     *
     * @param id 编号
     * @return 云主机网络
     */
    HostNicDO getHostNic(Long id);

    /**
     * 获得云主机网络列表
     *
     * @param ids 编号
     * @return 云主机网络列表
     */
    List<HostNicDO> getHostNicList(Collection<Long> ids);

    /**
     * 获得云主机网络分页
     *
     * @param pageReqVO 分页查询
     * @return 云主机网络分页
     */
    PageResult<HostNicDO> getHostNicPage(HostNicPageReqVO pageReqVO);

    /**
     * 获得云主机网络列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 云主机网络列表
     */
    List<HostNicDO> getHostNicList(HostNicExportReqVO exportReqVO);

    void createHostNicList(List<HostNicDO> reqDTO);


    void updateHostNics(List<HostNicDO> list);

    void deleteHostNics(List<HostNicDO> list);
}
