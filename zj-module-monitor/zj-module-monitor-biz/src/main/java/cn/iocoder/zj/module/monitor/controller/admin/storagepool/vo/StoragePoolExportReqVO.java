package cn.iocoder.zj.module.monitor.controller.admin.storagepool.vo;

import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 存储池 Excel 导出 Request VO，参数和 StoragePoolPageReqVO 是一致的")
@Data
public class StoragePoolExportReqVO {

    @Schema(description = "存储池uuid")
    private String uuid;

    @Schema(description = "存储名称")
    private String name;

    @Schema(description = "存储池类型")
    private String type;

    @Schema(description = "存储名称")
    private String description;

    @Schema(description = "存储池创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] vCreateDate;

    @Schema(description = "存储池修改时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] lastOpDate;

    @Schema(description = "已使用容量")
    private BigDecimal usedCapacity;

    @Schema(description = "虚拟可用容量")
    private BigDecimal availableCapacity;

    @Schema(description = "总容量")
    private BigDecimal totalCapacity;

    @Schema(description = "主存储uuid")
    private String storageUuid;

    @Schema(description = "数据安全类型")
    private String securityPolicy;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

}
