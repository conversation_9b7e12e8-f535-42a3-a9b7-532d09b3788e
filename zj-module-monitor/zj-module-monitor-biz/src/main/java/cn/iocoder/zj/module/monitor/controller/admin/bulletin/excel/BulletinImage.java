package cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel;

import cn.iocoder.zj.framework.excel.core.convert.ByteConvent;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.convert.BulletinEnableStatusConvert;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.convert.BulletinImageTypeConvert;
import cn.iocoder.zj.module.monitor.controller.admin.tags.vo.TagsRespVO;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ExcelIgnoreUnannotated
public class BulletinImage {

    @ExcelProperty("主键ID")
    private Long id;

    @ExcelProperty("镜像名称")
    private String name;

    @ExcelProperty("平台名称")
    private String platformName;

    @ExcelProperty(value = "状态", converter = BulletinEnableStatusConvert.class)
    private String status;

    @ExcelProperty("镜像格式")
    private String format;

    @ExcelProperty("CPU架构")
    private String cpuArch;

    @ExcelProperty("系统")
    private String osType;

    @ExcelProperty(value = "镜像大小", converter = ByteConvent.class)
    private Long size;

    @ExcelProperty(value = "镜像类型", converter = BulletinImageTypeConvert.class)
    private String imageType;

    @ExcelProperty("共享范围")
    private String sharingScope;

    private String uuid;

    @Schema(description = "标签信息")
    private List<TagsRespVO> tags;

    @ExcelProperty("标签")
    private String tagsStr;

    @ExcelProperty("系统语言")
    private String osLanguage;

    @Schema(description = "云主机uuid")
    private String hostUuid;

    @ExcelProperty(value = "最小内存要求", converter = ByteConvent.class)
    private BigDecimal minMemory;

    @ExcelProperty(value = "最小磁盘要求", converter = ByteConvent.class)
    private BigDecimal minDisk;

    @ExcelProperty("磁盘驱动")
    private String diskDriver;

    @ExcelProperty("网卡驱动")
    private String networkDriver;

    @ExcelProperty("引导方式")
    private String bootMode;

    @ExcelProperty("远程终端协议")
    private String remoteProtocol;

}
