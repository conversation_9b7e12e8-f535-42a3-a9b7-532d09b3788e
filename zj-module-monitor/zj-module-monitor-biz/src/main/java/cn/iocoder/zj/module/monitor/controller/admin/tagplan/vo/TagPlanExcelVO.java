package cn.iocoder.zj.module.monitor.controller.admin.tagplan.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 巡检计划 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class TagPlanExcelVO {

    @ExcelProperty("主键ID")
    private Long id;

    @ExcelProperty("计划名称")
    private String name;

    @ExcelProperty("巡检周期(day-日, week-周, month-月)")
    private String periodType;

    @ExcelProperty("执行日(日-不需要填;周-(1-7);月-(1-31))")
    private String executionDay;

    @ExcelProperty("执行时间点")
    private String executionTime;

    @ExcelProperty("Cron表达式")
    private String executionCron;

    @ExcelProperty("巡检项(多个以逗号分隔)")
    private String patrolItem;

    @ExcelProperty("状态(0-未启用, 1-启用)")
    private Byte status;

    @ExcelProperty("上次执行时间")
    private LocalDateTime lastExecutionTime;

    @ExcelProperty("下次执行时间")
    private LocalDateTime nextExecutionTime;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("租户编号")
    private Long tenantId;

    @ExcelProperty("job编号")
    private Long jobId;

}
