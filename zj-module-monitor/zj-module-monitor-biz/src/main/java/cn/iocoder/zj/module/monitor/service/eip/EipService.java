package cn.iocoder.zj.module.monitor.service.eip;

import java.util.*;
import javax.validation.*;
import cn.iocoder.zj.module.monitor.controller.admin.eip.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.eip.EipDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostnic.HostNicDO;

/**
 * 弹性公网 Service 接口
 *
 * <AUTHOR>
 */
public interface EipService {

    /**
     * 创建弹性公网
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createEip(@Valid EipCreateReqVO createReqVO);

    /**
     * 更新弹性公网
     *
     * @param updateReqVO 更新信息
     */
    void updateEip(@Valid EipUpdateReqVO updateReqVO);

    /**
     * 删除弹性公网
     *
     * @param id 编号
     */
    void deleteEip(Long id);

    /**
     * 获得弹性公网
     *
     * @param id 编号
     * @return 弹性公网
     */
    EipDO getEip(Long id);

    /**
     * 获得弹性公网列表
     *
     * @param ids 编号
     * @return 弹性公网列表
     */
    List<EipDO> getEipList(Collection<Long> ids);

    /**
     * 获得弹性公网分页
     *
     * @param pageReqVO 分页查询
     * @return 弹性公网分页
     */
    PageResult<EipDO> getEipPage(EipPageReqVO pageReqVO);

    /**
     * 获得弹性公网列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 弹性公网列表
     */
    List<EipDO> getEipList(EipExportReqVO exportReqVO);

    void createEipList(List<EipDO> list);

    void updateEips(List<EipDO> list);

    void deleteEips(List<EipDO> list);
}
