package cn.iocoder.zj.module.monitor.dal.redis.device;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.zj.module.monitor.dal.redis.RedisKeyConstants.ASSET_ID;

/**
 * @ClassName : AssetRedisDAO  //类名
 * @Description : 采集节点缓存  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/12/6  10:04
 */

@Repository
public class AssetRedisDAO {
    @Resource
    private StringRedisTemplate stringRedisTemplate;


    public String get(String uuid) {
        return stringRedisTemplate.opsForValue().get(uuid);
    }

    public void set(String uuid) {
        stringRedisTemplate.opsForValue().set(uuid, uuid, 5, TimeUnit.SECONDS);
    }
}
