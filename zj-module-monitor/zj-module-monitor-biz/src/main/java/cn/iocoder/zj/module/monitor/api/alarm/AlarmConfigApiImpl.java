package cn.iocoder.zj.module.monitor.api.alarm;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.util.collection.CollectionUtils;
import cn.iocoder.zj.framework.common.util.object.BeanUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmConfigRespDTO;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmInfoDTO;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmRecordDTO;
import cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo.AlarmInfoRespVo;
import cn.iocoder.zj.module.monitor.controller.admin.alarminfo.vo.AlarmInfoCreateReqVO;
import cn.iocoder.zj.module.monitor.convert.alarmconfig.AlarmConfigConvert;
import cn.iocoder.zj.module.monitor.dal.mysql.alarmdoriseinfo.AlarmDorisDO;
import cn.iocoder.zj.module.monitor.service.alarmconfig.AlarmConfigService;
import cn.iocoder.zj.module.monitor.service.alarminfo.AlarmInfoService;
import cn.iocoder.zj.module.monitor.service.gatherlogdetail.GatherLogdetailService;
import com.alibaba.fastjson.JSONObject;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.hertzbeat.common.entity.alerter.Alert;
import org.apache.hertzbeat.common.entity.alerter.AlertConverge;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
public class AlarmConfigApiImpl implements AlarmConfigApi{
    @Resource
    AlarmConfigService alarmConfigService;
    @Resource
    GatherLogdetailService gatherLogdetailService;
    @Resource
    AlarmInfoService alarmInfoService;
    @Override
    public CommonResult<List<AlarmConfigRespDTO>> list(Long tenantId) {
        List<AlarmConfigRespDTO> result = AlarmConfigConvert.INSTANCE.convertToDto(alarmConfigService.getListByTenantId(tenantId));
        return CommonResult.success(result);
    }

    @Override
    @Validated
    public void insertBatch(List<AlarmConfigRespDTO> toInsertAlarmConfig) {
        alarmConfigService.insertBatch(toInsertAlarmConfig);
    }

    @Override
    @Validated
    public CommonResult<Map<String,String>> getAlarmNotice(Long configId) {
        Map<String,String> result = alarmConfigService.getAlarmNoticeByConfigId(configId);
        return CommonResult.success(result);
    }

    @Override
    @Validated
    public CommonResult<Map<Long, List<AlarmDorisReqDTO>>> getSilentTarget(@Valid AlertConverge converge) {
        List<AlarmDorisReqDTO> alarmRecordVOS = alarmConfigService.getSilentTarget(converge);
        return CommonResult.success(CollectionUtils.convertMultiMap(alarmRecordVOS,
                AlarmDorisReqDTO::getAlarmId));
    }

    @Override
    @Validated
    public CommonResult<Boolean> solvedAlarm(String alarmId) {
        alarmConfigService.solvedAlarm(alarmId);
        return success(true);
    }

    @Override
    @Validated
    public CommonResult<Boolean> alarmWorkOrder(String alarmId) {
        alarmConfigService.alarmWorkOrder(alarmId);
        return success(true);
    }

    @Override
    @Validated
    public CommonResult<Boolean> cleanWorkOrder(String alarmId) {
        alarmConfigService.cleanWorkOrder(alarmId);
        return success(true);
    }

    @Override
    @Validated
    public CommonResult<Boolean> solvedGather(String gatherId) {
        gatherLogdetailService.solvedGather(gatherId);
        return success(true);
    }

    @Override
    @Validated
    public CommonResult<Boolean> gatherWorkOrder(String gatherId) {
        gatherLogdetailService.gatherWorkOrder(gatherId);
        return success(true);
    }

    @Override
    @Validated
    public CommonResult<Boolean> cleanGatherWorkOrder(String gatherId) {
        gatherLogdetailService.cleanWorkOrder(gatherId);
        return success(true);
    }

    @Override
    @TenantIgnore
    @Validated
    public CommonResult<Boolean> stateChangeAlarm(List<AlarmRecordDTO> alarmRecordDTOS) {
        List<AlarmInfoRespVo> alarmRecordDTOList = AlarmConfigConvert.INSTANCE.toAlarmRespVos(alarmRecordDTOS);
        alarmConfigService.addStateChangeAlarm(alarmRecordDTOList);
        return success(true);
    }


    @Override
    @TenantIgnore
    @Validated
    public CommonResult<Boolean> changeAlarmSolvedState(Long alertId, Integer isSolved) {
        alarmConfigService.changeAlertSolvedState(alertId, isSolved);
        return success(true);
    }

    @Override
    @Validated
    public CommonResult<Boolean> createAlarmToDoris(Map<String,List> updateAndInsert) {
        List<AlarmDorisReqDTO> updateList = JSONObject.parseArray(JSONObject.toJSONString(updateAndInsert.get(
                "updateList"))).toJavaList(AlarmDorisReqDTO.class);
        List<AlarmDorisReqDTO> alarmDorisReqDTOList = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(updateAndInsert.get("insertList"))) {
            alarmDorisReqDTOList = JSONObject.parseArray(JSONObject.toJSONString(updateAndInsert.get("insertList"))).toJavaList(AlarmDorisReqDTO.class);
        }
        List<AlarmDorisDO> result = AlarmConfigConvert.INSTANCE.dorisReqDTOConvertToDorisDO(alarmDorisReqDTOList);
        alarmConfigService.createAlarmToDoris(updateList,result);
        System.out.println("告警数据已更新");
        return success(true);
    }
    @Override
    @Validated
    @TenantIgnore
    public CommonResult<Boolean> updateAlarmDoris(List<AlarmDorisReqDTO> result) {
        List<AlarmDorisDO> alarmDorisDOList = AlarmConfigConvert.INSTANCE.dorisReqDTOConvertToDorisDO(result);
        alarmConfigService.updateCollectorAlarm(alarmDorisDOList.get(0));
        return success(true);
    }

    @Override
    public CommonResult<AlertConverge> getAvailableAlertConverge() {
        return success(alarmConfigService.getAvailableAlertConverge());
    }

    @Override
    public CommonResult<Long> getMaxAlertId() {
        return success(alarmConfigService.getMaxAlertId());
    }

    @Override
    public CommonResult<Boolean> createCollectorAlert(Map<String, Object> alertMap) {
        alarmConfigService.createCollectorAlert(alertMap);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> deletedCollectorAlert(Map<String, Object> alertMap) {
        alarmConfigService.deletedCollectorAlert(alertMap);
        return success(true);
    }

    @Override
    public CommonResult<List<Map<String, Object>>> getCollectorAlertsByPlatform(Collection<Long> platform) {
        return CommonResult.success(alarmConfigService.getCollectorAlertsByPlatform(platform));
    }

    @Override
    @TenantIgnore
    public void createAlarmInfo(AlarmInfoDTO alarmInfoDTO) {
        AlarmInfoCreateReqVO reqVO = BeanUtils.toBean(alarmInfoDTO, AlarmInfoCreateReqVO.class);
        alarmInfoService.createAlarmInfo(reqVO);
    }

    @Override
    @TenantIgnore
    @Validated
    public CommonResult<Boolean> changeAlarmIsFallback(Long alertId, Integer isFallback) {
        alarmConfigService.changeAlarmIsFallback(alertId, isFallback);
        return success(true);
    }

    @Override
    public CommonResult<Alert> getAlertInfoById(Long id) {
        return success( alarmConfigService.getAlertInfoById(id));
    }
}
