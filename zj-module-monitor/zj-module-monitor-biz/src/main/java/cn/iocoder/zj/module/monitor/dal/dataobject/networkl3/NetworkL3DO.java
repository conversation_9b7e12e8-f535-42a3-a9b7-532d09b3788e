package cn.iocoder.zj.module.monitor.dal.dataobject.networkl3;

import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 三级网络资源 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_network_l3")
@KeySequence("monitor_network_l3_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NetworkL3DO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 三级网络uuid
     */
    private String uuid;
    /**
     * 二级网络uuid
     */
    private String l2NetworkUuid;

    /**
     * 二级网络名称
     */
    private String l2NetworkName;

    /**
     * 三级网络名称
     */
    private String name;
    /**
     * dns 逗号分割
     */
    private String dns;
    /**
     * 网络资源类型（L3BasicNetwork 公有网络， L3VpcNetwork  vpc 网络 ）
     */
    private String type;
    /**
     * 网络服务 逗号分割
     */
    private String networkServices;
    /**
     * 起始ip
     */
    private String startIp;
    /**
     * 结束ip
     */
    private String endIp;
    /**
     * 子网掩码
     */
    private String netmask;
    /**
     * 网关
     */
    private String gateway;
    /**
     * 网段名称
     */
    private String networkSegment;
    /**
     * 		IPv4 CIDR
     */
    private String networkCidr;
    /**
     * IPV4 DHCP
     */
    private String nextHopIp;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;
    /**
     * @description: 租户编号
     * <AUTHOR>
     * @date 2023/8/8 11:37
     * @version 1.0
     */
    private Long tenantId;

    private String typeName;
}
