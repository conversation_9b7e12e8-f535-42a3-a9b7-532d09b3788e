package cn.iocoder.zj.module.monitor.dal.dataobject.hardwarenic;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 物理机网络关系 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_hardware_nic")
@KeySequence("monitor_hardware_nic_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HardwareNicDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 网卡uuid(v3)
     */
    private String uuid;
    /**
     * 宿主机uuid
     */
    private String hardwareUuid;
    /**
     * mac
     */
    private String mac;
    /**
     * 网卡类型
     */
    private String networkType;
    /**
     * ip地址
     */
    private String ipAddresses;
    /**
     * ip子网
     */
    private String ipSubnet;
    /**
     * 二层网络uuid
     */
    private String l2NetworkUuid;
    /**
     * 二层网络名称
     */
    private String l2NetworkName;
    /**
     * 是否离线
     */
    private Boolean state;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;

}
