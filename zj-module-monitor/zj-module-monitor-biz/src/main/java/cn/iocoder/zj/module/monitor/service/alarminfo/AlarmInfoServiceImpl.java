package cn.iocoder.zj.module.monitor.service.alarminfo;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.constants.FileTypeConstants;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.util.string.StringUtil;
import cn.iocoder.zj.module.bpm.api.task.BpmProcessInstanceApi;
import cn.iocoder.zj.module.bpm.api.task.BpmTaskApi;
import cn.iocoder.zj.module.infra.api.file.FileApi;
import cn.iocoder.zj.module.monitor.controller.admin.alarminfo.vo.*;
import cn.iocoder.zj.module.monitor.convert.alarminfo.AlarmInfoConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmconfig.AlarmConfigDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarminfo.AlarmInfoDO;
import cn.iocoder.zj.module.monitor.dal.mysql.alarminfo.AlarmInfoMapper;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.hertzbeat.common.entity.alerter.AlertDefine;
import org.apache.hertzbeat.common.entity.alerter.AlertRespVo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.ALARM_INFO_NOT_EXISTS;

/**
 * 监控告警详情 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AlarmInfoServiceImpl implements AlarmInfoService {

    @Value("${minio.endpoint}")
    private String endpoint;
    @Value("${minio.bucketName}")
    private String bucketName;

    @Resource
    private AlarmInfoMapper alarmInfoMapper;
    @Resource
    private FileApi fileApi;
    @Resource
    private BpmProcessInstanceApi bpmProcessInstanceApi;
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    private BpmTaskApi taskApi;


    @Override
    public Long createAlarmInfo(AlarmInfoCreateReqVO createReqVO) {
        JSONArray jsonArray = JSONArray.parseArray(createReqVO.getFileAddress());
        AlarmInfoDO alarmInfo = AlarmInfoConvert.INSTANCE.convert(createReqVO);
        if (jsonArray != null) {
            alarmInfo.setFileAddress(jsonArray.toJSONString());
        }
        alarmInfo.setProcessingTime(DateUtil.toLocalDateTime(new Date()));
        alarmInfoMapper.insert(alarmInfo);
        alarmInfoMapper.updateisSolvedByAlarmId(Convert.toLong(createReqVO.getAlarmId()));
        // 返回
        return alarmInfo.getId();
    }

    @Override
    public void updateAlarmInfo(AlarmInfoUpdateReqVO updateReqVO) {
        // 校验存在
        validateAlarmInfoExists(updateReqVO.getId());
        // 更新
        AlarmInfoDO updateObj = AlarmInfoConvert.INSTANCE.convert(updateReqVO);
        alarmInfoMapper.updateById(updateObj);
    }

    @Override
    public void deleteAlarmInfo(Long id) {
        // 校验存在
        validateAlarmInfoExists(id);
        // 删除
        alarmInfoMapper.deleteById(id);
    }

    private void validateAlarmInfoExists(Long id) {
        if (alarmInfoMapper.selectById(id) == null) {
            throw exception(ALARM_INFO_NOT_EXISTS);
        }
    }

    @Override
    public AlarmInfoDO getAlarmInfo(Long id) {
        return alarmInfoMapper.selectById(id);
    }

    @Override
    public List<AlarmInfoDO> getAlarmInfoList(Collection<Long> ids) {
        return alarmInfoMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<AlarmInfoDO> getAlarmInfoPage(AlarmInfoPageReqVO pageReqVO) {
        return alarmInfoMapper.selectPage(pageReqVO);
    }

    @Override
    public List<AlarmInfoDO> getAlarmInfoList(AlarmInfoExportReqVO exportReqVO) {
        return alarmInfoMapper.selectList(exportReqVO);
    }

    @Override
    public Map<String, String> getAlarmInfoUpload(MultipartFile file, HttpServletResponse response) throws IOException {
        Map map = new HashMap();
        String originalFileName = file.getOriginalFilename();
        String fileExtension = "";
        if (StrUtil.isNotBlank(originalFileName) && originalFileName.contains(".")) {
            fileExtension = originalFileName.substring(originalFileName.lastIndexOf("."));
        }

        // 生成UUID文件名，保留扩展名
        String uuidFileName = UUID.randomUUID().toString() + fileExtension;

        String d = StringUtil.getSavePath(uuidFileName, FileTypeConstants.FILE_TYPE, "alarm");
        String url = fileApi.createFileUrl(uuidFileName, d, IoUtil.readBytes(file.getInputStream()));
        map.put("fileUrl", url);
        map.put("fileName",uuidFileName);
        return map;
    }

    @Override
    public AlarmInfoByIdRespVO getAlarmInfoById(Long id) {
        AlertRespVo alert = alarmInfoMapper.getAlertInfoById(id);

        if (alert.getPlatformId() != null) {
            PlatformconfigDTO platformconfigDTO = platformconfigApi.getByConfigId(alert.getPlatformId()).getCheckedData();
            alert.setAddress(platformconfigDTO.getAddress());
        }
        AlarmInfoDO alarmInfoDO = alarmInfoMapper.getAlarmInfoByalarmId(alert.getId());
        AlertDefine alertDefine = alarmInfoMapper.getAlertDefineById(alert.getAlarmId());
        AlarmConfigDO alarmConfigDO = alarmInfoMapper.getAlarmConfigDoById(alert.getAlarmId());
        AlarmInfoByIdRespVO alarmInfoByIdRespVO = new AlarmInfoByIdRespVO();
        // 根据状态获取是从非云平台查还是从云平台查 1：hz平台 2：云平台
        String alarmName = "";
        Long alarmId = 0l;
        String expr = "";
        Map<String, Object> bpmProcess = new HashMap<>();
        if (alarmInfoDO != null && alarmInfoDO.getProcessingType() == 0) {
            alarmInfoByIdRespVO.setProcessingType(alarmInfoDO.getProcessingType());
            String d = alarmInfoDO.getFileAddress();
            if (StringUtil.isNotEmpty(d)) {
                JSONArray jsonArray = JSON.parseArray(d);
                JSONArray jsonArray1 = new JSONArray();

                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    jsonObject.put("fileName", jsonObject.get("fileName").toString());
                    jsonObject.put("fileUrl", jsonObject.get("fileUrl").toString());
                    jsonArray1.add(jsonObject);
                }
                alarmInfoDO.setFileAddress(jsonArray1.toJSONString());
            } else {
                alarmInfoDO.setFileAddress("");
            }
            alarmInfoByIdRespVO.setAlarmInfoDO(alarmInfoDO);
        } else if (alarmInfoDO != null && alarmInfoDO.getProcessingType() == 1) {
            alarmInfoByIdRespVO.setProcessingType(alarmInfoDO.getProcessingType());
            //ProcessingType 是1说明是工单处理
            bpmProcess = taskApi.getProcessInfo(id, 1).getData();
            alarmInfoByIdRespVO.setProcessingOrderInfo(bpmProcess);
        }
        if (alert.getResourceType() == 1) {
            if (alertDefine != null) {
                if (alertDefine.getTenantId().equals(0)) {
                    alarmName = "-";
                    alarmId = 0L;
                    expr = "监控异常";
                } else {
                    if (alert.getIsSolved() == 2) {
                        if (StrUtil.isNotEmpty(alert.getAlarmRule())){
                            if (!alert.getAlarmRule().equals(alertDefine.getExpr())) {
                                alarmName = "-";
                                alarmId = 0L;
                                expr = alert.getAlarmRule();
                            } else {
                                alarmName = alertDefine.getName();
                                alarmId = alertDefine.getId();
                                expr = StringUtil.isNullOrEmpty(alertDefine.getExpr()) ? "监控异常" : alertDefine.getExpr();
                            }
                        }

                    } else {
                        expr = StringUtil.isNullOrEmpty(alertDefine.getExpr()) ? "监控异常" : alertDefine.getExpr();
                        alarmName = alertDefine.getName();
                        alarmId = alertDefine.getId();
                    }
                }
            }
        } else {
            if (alarmConfigDO != null) {
                alarmName = alert.getAlarmConfigName();
                alarmId = alarmConfigDO.getId();
                expr = alert.getAlarmRule();
            }
        }
        alarmInfoByIdRespVO.setAlert(alert);
        alarmInfoByIdRespVO.setAlarmId(alarmId);
        alarmInfoByIdRespVO.setExpr(expr);
        alarmInfoByIdRespVO.setAlarmName(StringUtil.isNotEmpty(alarmName) ? alarmName : "-");
        return alarmInfoByIdRespVO;
    }

    @Override
    public void batchUpdateStatus(List<AlarmInfoCreateReqVO> alarmList) {
        List<AlarmInfoDO> list = new ArrayList<>();
        for (AlarmInfoCreateReqVO createReqVO : alarmList) {
            JSONArray jsonArray = JSONArray.parseArray(createReqVO.getFileAddress());
            AlarmInfoDO alarmInfo = AlarmInfoConvert.INSTANCE.convert(createReqVO);
            if (jsonArray != null) {
                alarmInfo.setFileAddress(jsonArray.toJSONString());
            }
            alarmInfo.setProcessingTime(DateUtil.toLocalDateTime(new Date()));
            list.add(alarmInfo);
        }
        alarmInfoMapper.insertBatch(list);
    }

}
