package cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Collection;
import java.util.Collections;

@Schema(description = "创建或修改告警配置与主机关联关系的请求参数")
@Data
@ToString(callSuper = true)
public class RelationRqeVO {
    @Schema(description = "告警配置ID，创建关系时传")
    private Long id;

    @Schema(description = "资源uuid，创建关系时传")
    private String uuid;

    @Schema(description = "多个告警配置的id，为云主机批量添加告警模板时使用")
    private Collection<Long> ids;

    @Schema(description = "为告警配置关联多个资源时使用")
    private  Collection<String> uuids;

    @Schema(description = "告警配置的关系ID，更新状态时传")
    private Long relationId;

    @Schema(description = "启用状态，0正常，1关闭，更新状态时传")
    private Integer status;

    @Schema(description = "告警资源类型，host云主机，hardware宿主机，storage主存储")
    private String sourceType;

    @Schema(description = "device:从云资源添加，alarm_config:从告警配置添加")
    private String configWay;
}
