package cn.iocoder.zj.module.monitor.api.demo;


import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.service.demo.DemoService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class DemoApiImpl implements DemoApi {

    @Resource
    private DemoService userService;


    @Override
    public CommonResult<Boolean> getUser(Long id) {
        Long user = userService.getUser();
        return success(true);
    }
}
