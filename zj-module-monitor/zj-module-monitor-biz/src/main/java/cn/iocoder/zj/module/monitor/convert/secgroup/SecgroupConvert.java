package cn.iocoder.zj.module.monitor.convert.secgroup;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.api.secgroup.dto.SecgroupCreateReqDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.secgroup.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.secgroup.SecgroupDO;

/**
 * 安全组 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface SecgroupConvert {

    SecgroupConvert INSTANCE = Mappers.getMapper(SecgroupConvert.class);

    SecgroupDO convert(SecgroupCreateReqVO bean);

    SecgroupDO convert(SecgroupUpdateReqVO bean);

    SecgroupRespVO convert(SecgroupDO bean);

    List<SecgroupRespVO> convertList(List<SecgroupDO> list);

    PageResult<SecgroupRespVO> convertPage(PageResult<SecgroupDO> page);

    List<SecgroupExcelVO> convertList02(List<SecgroupDO> list);

    List<SecgroupDO> convertCreateList(List<SecgroupCreateReqDto> reqDTO);

    List<SecgroupCreateReqDto> convertDoToCreateDtoList(List<SecgroupDO> secgroupList);
}
