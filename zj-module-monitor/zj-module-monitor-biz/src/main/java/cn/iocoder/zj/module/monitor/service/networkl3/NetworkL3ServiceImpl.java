package cn.iocoder.zj.module.monitor.service.networkl3;

import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL3DTO;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkl2.NetworkL2DO;
import cn.iocoder.zj.module.monitor.util.StringUtil;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.zj.module.monitor.controller.admin.networkl3.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkl3.NetworkL3DO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.convert.networkl3.NetworkL3Convert;
import cn.iocoder.zj.module.monitor.dal.mysql.networkl3.NetworkL3Mapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.singleton;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.*;

/**
 * 三级网络资源 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class NetworkL3ServiceImpl implements NetworkL3Service {

    @Resource
    private NetworkL3Mapper networkL3Mapper;
    @Resource
    private PermissionApi permissionApi;
    @Resource
    private RoleApi roleApi;
    @Resource
    PlatformconfigApi platformconfigApi;


    @Override
    public Long createNetworkL3(NetworkL3CreateReqVO createReqVO) {
        // 插入
        NetworkL3DO networkL3 = NetworkL3Convert.INSTANCE.convert(createReqVO);
        networkL3Mapper.insert(networkL3);
        // 返回
        return networkL3.getId();
    }

    @Override
    public void updateNetworkL3(NetworkL3UpdateReqVO updateReqVO) {
        // 校验存在
        validateNetworkL3Exists(updateReqVO.getId());
        // 更新
        NetworkL3DO updateObj = NetworkL3Convert.INSTANCE.convert(updateReqVO);
        networkL3Mapper.updateById(updateObj);
    }

    @Override
    public void deleteNetworkL3(Long id) {
        // 校验存在
        validateNetworkL3Exists(id);
        // 删除
        networkL3Mapper.deleteById(id);
    }

    private void validateNetworkL3Exists(Long id) {
        if (networkL3Mapper.selectById(id) == null) {
            throw exception(NETWORK_L3_NOT_EXISTS);
        }
    }

    @Override
    public NetworkL3DO getNetworkL3(Long id, String uuid) {
        if (StringUtil.isNotEmpty(uuid)) {
            LambdaQueryWrapper<NetworkL3DO> lqw = new LambdaQueryWrapper<>();
            lqw.eq(NetworkL3DO::getUuid, uuid)
                    .eq(NetworkL3DO::getDeleted, 0)
                    .last("LIMIT 1");
            return networkL3Mapper.selectOne(lqw);
        } else {
            return networkL3Mapper.selectById(id);
        }
    }

    @Override
    public List<NetworkL3DO> getNetworkL3List(Collection<Long> ids) {
        return networkL3Mapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<NetworkL3DO> getNetworkL3Page(NetworkL3PageReqVO pageReqVO) {
        List<Map> platform = new ArrayList<>();
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
        return networkL3Mapper.selectPage(pageReqVO,platform);
    }

    @Override
    public List<NetworkL3DO> getNetworkL3List(NetworkL3ExportReqVO exportReqVO) {
        return networkL3Mapper.selectList(exportReqVO);
    }

    @Override
    public void createNetworkL3List(List<NetworkL3DO> list) {
        networkL3Mapper.insertBatch(list);
    }

    @Override
    public Long getNetWorkL3Count(String typeName) {
        return networkL3Mapper.selectCount(NetworkL3DO::getTypeName,typeName);
    }

    @Override
    public void updateNetWorkL3List(List<NetWorkL3DTO> list) {
        networkL3Mapper.updateNetWorkL3List(list);
    }

    @Override
    public List<NetworkL3DO> getNetWorkL3List(String typeName) {
        return networkL3Mapper.selectList(NetworkL3DO::getTypeName,typeName);
    }

    @Override
    public PageResult<NetworkL3DO> getNetworkL3VpcPage(NetworkL3PageReqVO pageVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
//        if (!roleApi.hasAnySuperAdmin(roleIds)) {
//            pageReqVO.setTenantId(loginUser.getTenantId());
//        }else {
//            pageReqVO.setTenantId(0L);
//        }


        return networkL3Mapper.selectVpcPage(pageVO);
    }

    @Override
    public void deleteNetworkL3Byplatform(Long platformId) {
        networkL3Mapper.deleteNetworkL3Byplatform(platformId);
    }

    @Override
    public List<NetWorkL3DTO> getNetworkL3ByPlatformId(Long platformId) {
        return networkL3Mapper.getNetworkL3ByPlatformId(platformId);
    }

}
