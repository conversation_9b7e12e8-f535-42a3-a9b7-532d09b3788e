package cn.iocoder.zj.module.monitor.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.TagData;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class DealTagUtil {
    public static <T> List<TagData> dealData(List<T> array, String type) {
        if (CollUtil.isEmpty(array)) {
            return Collections.emptyList();
        }

        List<TagData> tagList = new ArrayList<>();
        for (T obj : array) {
            String tagStr = getField(obj, "tag");
            if (StrUtil.isBlank(tagStr)) continue;

            String[] entries = tagStr.split(",");
            for (String entry : entries) {
                String[] parts = entry.split("&");
                if (parts.length < 2) continue;

                TagData tagData = new TagData();
                tagData.setTagName(parts[0].trim());
                tagData.setTagUuid(parts[1].trim());
                tagData.setId(Convert.toLong(getField(obj, "id")));
                tagData.setPlatformId(Convert.toLong(getField(obj, "platformId")));
                tagData.setName(getField(obj, "name"));
                tagData.setType(type);
                tagList.add(tagData);
            }
        }

        return tagList;
    }

    private static String getField(Object obj, String fieldName) {
        try {
            Method method = obj.getClass().getMethod("get" + StrUtil.upperFirst(fieldName));
            Object value = method.invoke(obj);
            return value != null ? value.toString() : "";
        } catch (Exception e) {
            // 可以根据需要打印日志或抛出异常
            return "";
        }
    }
}




