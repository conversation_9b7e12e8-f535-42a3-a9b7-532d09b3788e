package cn.iocoder.zj.module.monitor.dal.dataobject.hardwareinfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.util.*;

import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 硬件设施基本信息 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_hardware_info")
@KeySequence("monitor_hardware_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HardwareInfoDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 主机id
     */
    private String uuid;
    /**
     * 主机名称
     */
    private String name;
    /**
     * 宿主机状态，包括：	Enabled	Disabled	PreMaintenance	Maintenance
     */
    private String state;
    /**
     * ip
     */
    private String ip;
    /**
     * Connecting，	Connected，	Disconnected
     */
    private String status;
    /**
     * 集群id
     */
    private String clusterUuid;
    /**
     * 集群名称
     */
    private String clusterName;
    /**
     * cpu总容量
     */
    private Long totalCpuCapacity;
    /**
     * cpu可用容量
     */
    private Long availableCpuCapacity;
    /**
     * cpu 插槽
     */
    private Integer cpuSockets;
    /**
     * CPU架构
     */
    private String architecture;
    /**
     * 分配cpu
     */
    private Integer cpuNum;
    /**
     * 内存总容量
     */
    private Long totalMemoryCapacity;
    /**
     * 内存可用容量
     */
    private Long availableMemoryCapacity;
    /**
     * 地区id
     */
    private Long regionId;

    /**
     * @description: 租户编号
     * <AUTHOR>
     * @date 2023/6/6 9:00
     * @version 1.0
     */
    private Long tenantId;

    /**
     * 带宽上行速率
     */
    private BigDecimal bandwidthUpstream;
    /**
     * 带宽下行速率
     */
    private BigDecimal bandwidthDownstream;
    /**
     * 内存使用率
     */
    private BigDecimal memoryUsed;
    /**
     * cpu 使用率
     */
    private BigDecimal cpuUsed;
    /**
     * 收包速率
     */
    private BigDecimal packetRate;


    private BigDecimal diskUsed;

    private BigDecimal diskUsedBytes;

    private BigDecimal diskFreeBytes;

    private BigDecimal totalDiskCapacity;

    private Integer deleted;
    // 平台配置id
    private Long platformId;
    // 平台配置名称
    private String platformName;

    private String typeName;

    private String vms;

    private Integer isMaintain;


    private String ipmi;

    private String manufacturer;

    //cpu超售比例
    private BigDecimal cpuOverPercent;

    //内存超售比例
    private BigDecimal memoryOverPercent;

    //区域
    private String manager;

    //可用区域
    private String availableManager;

    //当前cpu超售比例
    private BigDecimal cpuCommitRate;

    //当前内存超售比例
    private BigDecimal memoryCommitRate;

    //系统预留内存
    private BigDecimal reservedMemory;

    //品牌名称
    private String brandName;

    //型号
    private String model;

    //序列号
    private String serialNumber;

    //cpu类型（描述）
    private String cpuType;

    private String totalVirtualMemory;

    private String tag;

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("name", this.name);
        map.put("uuid", this.uuid);
        map.put("platformName", this.platformName);
        map.put("platformId",this.platformId);
        map.put("typeName",this.typeName);
        return map;
    }
}
