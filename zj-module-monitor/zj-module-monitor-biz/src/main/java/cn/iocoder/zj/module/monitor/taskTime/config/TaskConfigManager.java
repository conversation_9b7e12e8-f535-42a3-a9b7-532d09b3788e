package cn.iocoder.zj.module.monitor.taskTime.config;

import cn.iocoder.zj.module.monitor.dal.mysql.scanip.IPRangeMapper;
import cn.iocoder.zj.module.monitor.taskTime.annotation.HSYJobProcessor;
import cn.iocoder.zj.module.monitor.taskTime.cache.TaskCacheModel;
import cn.iocoder.zj.module.monitor.taskTime.service.ScheduleTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
@RequiredArgsConstructor
public class TaskConfigManager {

    @Resource
    private IPRangeMapper ipRangeMapper;

    private final ScheduleTaskService scheduleTaskService;
    private final Map<String, String> taskConfigs = new ConcurrentHashMap<>();

    public void loadAndRegisterTasks(HSYJobProcessor yqJobProcessor) {
        try {
            loadDefaultConfigs(yqJobProcessor);
        } catch (Exception e) {
            log.error("加载任务配置失败", e);
        }
    }

    private void loadDefaultConfigs(HSYJobProcessor yqJobProcessor) {
        List<Map<String, String>> configList = ipRangeMapper.getTaskConfigs();


        if (configList == null || configList.isEmpty()) {
            log.warn("数据库中未找到任务配置");
            return;
        }

        taskConfigs.clear();
        HSYJobProcessor.TASK_CACHE.clear();

        configList.stream()
                .filter(config -> config.get("task_name") != null && config.get("cron_exp") != null)
                .forEach(config -> {
                    String taskName = config.get("task_name");
                    String cronExpression = config.get("cron_exp");
                    taskConfigs.put(taskName, cronExpression);
                    try {
                        registerOrUpdateTask(taskName, cronExpression, yqJobProcessor);
                        log.info("成功注册任务: {}, cron: {}", taskName, cronExpression);
                    } catch (Exception e) {
                        log.error("注册任务失败: {}, cron: {}", taskName, cronExpression, e);
                    }
                });

        log.info("数据库配置任务注册完成，共{}个任务", configList.size());
    }

    private void registerOrUpdateTask(String taskName, String cronExpression, HSYJobProcessor yqJobProcessor) {
        if (cronExpression == null || cronExpression.trim().isEmpty()) {
            log.error("任务[{}]的cron表达式为空", taskName);
            return;
        }

        try {
            TaskCacheModel model = HSYJobProcessor.TASK_CACHE.get(taskName);
            if (model != null) {
                // 更新现有任务
                model.setCronExpression(cronExpression);
                scheduleTaskService.updateTask(taskName,cronExpression);
                log.info("更新任务配置: {}", taskName);
            } else {
                // 注册新任务
                if (yqJobProcessor.registerDynamicTask(taskName, cronExpression)) {
                    scheduleTaskService.updateTask(taskName,cronExpression);
                    log.info("注册新任务: {}", taskName);
                }
            }
        } catch (Exception e) {
            log.error("处理任务配置失败: {}", taskName, e);
            throw e;
        }
    }

    public Map<String, String> getAllTaskConfigs() {
        return new HashMap<>(taskConfigs);
    }
}