package cn.iocoder.zj.module.monitor.controller.admin.tags;

import cn.iocoder.zj.module.monitor.controller.admin.taggables.vo.TaggablesExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.taggables.vo.TaggablesRespVO;
import cn.iocoder.zj.module.monitor.convert.taggables.TaggablesConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.taggables.TaggablesDO;
import cn.iocoder.zj.module.monitor.dal.mysql.taggables.TaggablesMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.tags.TagsMapper;
import cn.iocoder.zj.module.monitor.service.taggables.TaggablesService;
import cn.iocoder.zj.module.monitor.service.taggables.enums.TagAssetTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;
import java.util.stream.Collectors;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;
import static org.apache.coyote.http11.Constants.a;

import cn.iocoder.zj.module.monitor.controller.admin.tags.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.tags.TagsDO;
import cn.iocoder.zj.module.monitor.convert.tags.TagsConvert;
import cn.iocoder.zj.module.monitor.service.tags.TagsService;

@Tag(name = "管理后台 - 标签")
@RestController
@RequestMapping("/monitor/tags")
@Validated
public class TagsController {

    @Resource
    private TagsService tagsService;
    @Resource
    private TagsMapper tagsMapper;
    @Resource
    private TaggablesService taggablesService;
    @Resource
    private TaggablesMapper taggablesMapper;

    @PostMapping("/create")
    @Operation(summary = "创建标签")
//    @PreAuthorize("@ss.hasPermission('monitor:tags:create')")
    public CommonResult<Long> createTags(@Valid @RequestBody TagsCreateReqVO createReqVO) {
        return success(tagsService.createTags(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新标签")
//    @PreAuthorize("@ss.hasPermission('monitor:tags:update')")
    public CommonResult<Boolean> updateTags(@Valid @RequestBody TagsUpdateReqVO updateReqVO) {
        tagsService.updateTags(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除标签")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('monitor:tags:delete')")
    public CommonResult<Boolean> deleteTags(@RequestParam("id") Long id) {
        tagsService.deleteTags(id);
        taggablesService.deleteByTagId(id);
        return success(true);
    }

    @DeleteMapping("/batch-delete")
    @Operation(summary = "批量删除标签")
    @Parameter(name = "ids", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('monitor:tags:delete')")
    public CommonResult<Boolean> batchDeleteTags(@RequestParam("ids") String ids) {
        String[] idsArr = ids.split(",");
        for (String s : idsArr) {
            long id = Long.parseLong(s);
            tagsService.deleteTags(id);
            taggablesService.deleteByTagId(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得标签")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('monitor:tags:query')")
    public CommonResult<TagsRespVO> getTags(@RequestParam("id") Long id) {
        TagsDO tags = tagsService.getTags(id);
        List<TaggableCountItemResp> tagCountList = new ArrayList<>();
        for (TagAssetTypeEnum tagAssetTypeEnum : TagAssetTypeEnum.values()) {
            TaggableCountItemResp tmp = new TaggableCountItemResp();
            tmp.setType(tagAssetTypeEnum.getCode());
            tmp.setName(tagAssetTypeEnum.getDescription());
            tmp.setCount(taggablesService.getTaggableCount(new TaggablesExportReqVO()
                    .setTagId(id).setTaggableType(tagAssetTypeEnum.getCode())));
            tagCountList.add(tmp);
        }
        TagsRespVO resp = TagsConvert.INSTANCE.convert(tags);
        resp.setTaggableCountList(tagCountList);
        return success(resp);
    }

    @GetMapping("/list")
    @Operation(summary = "获得标签列表")
    @Parameter(name = "ids", description = "编号列表", required = false, example = "1024,2048")
//    @PreAuthorize("@ss.hasPermission('monitor:tags:query')")
    public CommonResult<List<TagsRespVO>> getTagsList(@RequestParam(value = "ids",required = false) Collection<Long> ids) {
        List<TagsDO> list = tagsService.getTagsList(ids);
        return success(TagsConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得标签分页")
//    @PreAuthorize("@ss.hasPermission('monitor:tags:query')")
    public CommonResult<PageResult<TagsRespVO>> getTagsPage(@Valid TagsPageReqVO pageVO) {

        List<TagsDO> all = tagsService.getTagsList(new TagsExportReqVO());
        all.forEach(item ->{
            //去除已删除资源
            for (TagAssetTypeEnum value : TagAssetTypeEnum.values()) {
                List<TaggablesDO> respTaggable = taggablesMapper.selectList(new TaggablesExportReqVO().setTagId(item.getId()).setTaggableType(value.getCode()));
                if(respTaggable == null || respTaggable.isEmpty()){
                    continue;
                }
                List<Map<String, Object>> a = taggablesMapper.selectBindAssetListByIdAndType(
                        value.getTable(),
                        respTaggable.stream().map(it -> it.getTaggableId().toString()).collect(java.util.stream.Collectors.joining(",")),
                        value.getField(),
                        value.getCode()
                );
                if (a != null) {
                    //删除在a中没有id的资产
                    //取出respTaggable所有的id列
                    List<Long> respTaggableIds = respTaggable.stream().map(TaggablesDO::getTaggableId).toList();
                    //取出在所有资产id
                    List<Long> aIds = a.stream().map(it -> Long.parseLong(it.get("id").toString())).toList();

                    List<Long> delTaggableIds = respTaggableIds.stream().filter(it -> !aIds.contains(it)).toList();
                    if (!delTaggableIds.isEmpty()){
                        List<Long> delIds = respTaggable.stream().filter(it -> delTaggableIds.contains(it.getTaggableId())).map(TaggablesDO::getId).toList();
                        taggablesMapper.deleteBatchIds(delIds);
                    }
                }
            }
            item.setTotalCount(taggablesService.getTaggableCount(new TaggablesExportReqVO().setTagId(item.getId())));
            tagsMapper.updateById( item);
        });


        PageResult<TagsDO> pageResult = tagsService.getTagsPage(pageVO);
        PageResult<TagsRespVO> resp = TagsConvert.INSTANCE.convertPage(pageResult);

        return success(resp);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出标签 Excel")
    @OperateLog(type = EXPORT)
    public void exportTagsExcel(@Valid TagsExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<TagsDO> list = tagsService.getTagsList(exportReqVO);
        // 导出 Excel
        List<TagsExcelVO> datas = TagsConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "标签.xls", "数据", TagsExcelVO.class, datas);
    }

}
