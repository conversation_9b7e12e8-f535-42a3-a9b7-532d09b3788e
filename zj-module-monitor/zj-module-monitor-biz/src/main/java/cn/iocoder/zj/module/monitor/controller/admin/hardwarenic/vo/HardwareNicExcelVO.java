package cn.iocoder.zj.module.monitor.controller.admin.hardwarenic.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 物理机网络关系 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class HardwareNicExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("网卡uuid(v3)")
    private String uuid;

    @ExcelProperty("宿主机uuid")
    private String hardwareUuid;

    @ExcelProperty("mac")
    private String mac;

    @ExcelProperty("网卡类型")
    private String networkType;

    @ExcelProperty("ip地址")
    private String ipAddresses;

    @ExcelProperty("ip子网")
    private String ipSubnet;

    @ExcelProperty("二层网络uuid")
    private String l2NetworkUuid;

    @ExcelProperty("二层网络名称")
    private String l2NetworkName;

    @ExcelProperty("是否离线")
    private Boolean state;

    @ExcelProperty("创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    @ExcelProperty("平台id")
    private Long platformId;

    @ExcelProperty("平台名称")
    private String platformName;

}
