package cn.iocoder.zj.module.monitor.service.tagplan;


import cn.iocoder.zj.module.monitor.tagtask.job.model.TagTaskModel;

import java.util.List;

public interface TagTaskModelService {

    List<TagTaskModel> findAll();

    void addTask(TagTaskModel taskCacheModel);

    void updateTasByJobId(TagTaskModel taskCacheModel);

    void removeTask(TagTaskModel taskCacheModel);

    TagTaskModel getTaskByJobId(Long jobId);
}
