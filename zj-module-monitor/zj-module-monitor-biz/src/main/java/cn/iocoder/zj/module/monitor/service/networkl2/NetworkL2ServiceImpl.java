package cn.iocoder.zj.module.monitor.service.networkl2;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL2DTO;
import cn.iocoder.zj.module.monitor.controller.admin.networkl2.vo.NetworkL2CreateReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.networkl2.vo.NetworkL2ExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.networkl2.vo.NetworkL2PageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.networkl2.vo.NetworkL2UpdateReqVO;
import cn.iocoder.zj.module.monitor.convert.networkl2.NetworkL2Convert;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkl2.NetworkL2DO;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkl3.NetworkL3DO;
import cn.iocoder.zj.module.monitor.dal.mysql.networkl2.NetworkL2Mapper;
import cn.iocoder.zj.module.monitor.util.StringUtil;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.NETWORK_L2_NOT_EXISTS;

/**
 * 二级网络信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class NetworkL2ServiceImpl implements NetworkL2Service {

    @Resource
    private NetworkL2Mapper networkL2Mapper;
    @Resource
    private PermissionApi permissionApi;
    @Resource
    private RoleApi roleApi;
    @Resource
    PlatformconfigApi platformconfigApi;

    @Override
    public Long createNetworkL2(NetworkL2CreateReqVO createReqVO) {
        // 插入
        NetworkL2DO networkL2 = NetworkL2Convert.INSTANCE.convert(createReqVO);
        networkL2Mapper.insert(networkL2);
        // 返回
        return networkL2.getId();
    }

    @Override
    public void updateNetworkL2(NetworkL2UpdateReqVO updateReqVO) {
        // 校验存在
        validateNetworkL2Exists(updateReqVO.getId());
        // 更新
        NetworkL2DO updateObj = NetworkL2Convert.INSTANCE.convert(updateReqVO);
        networkL2Mapper.updateById(updateObj);
    }

    @Override
    public void deleteNetworkL2(Long id) {
        // 校验存在
        validateNetworkL2Exists(id);
        // 删除
        networkL2Mapper.deleteById(id);
    }

    private void validateNetworkL2Exists(Long id) {
        if (networkL2Mapper.selectById(id) == null) {
            throw exception(NETWORK_L2_NOT_EXISTS);
        }
    }

    @Override
    public NetworkL2DO getNetworkL2(Long id,String uuid) {
        if (StringUtil.isNotEmpty(uuid)) {
            LambdaQueryWrapper<NetworkL2DO> lqw = new LambdaQueryWrapper<>();
            lqw.eq(NetworkL2DO::getUuid, uuid)
                    .eq(NetworkL2DO::getDeleted, 0)
                    .last("LIMIT 1");
            return networkL2Mapper.selectOne(lqw);
        } else {
            return networkL2Mapper.selectById(id);
        }
    }

    @Override
    public List<NetworkL2DO> getNetworkL2List(Collection<Long> ids) {
        return networkL2Mapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<NetworkL2DO> getNetworkL2Page(NetworkL2PageReqVO pageReqVO) {
        List<Map> platform = new ArrayList<>();
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
        return networkL2Mapper.selectPage(pageReqVO, platform);
    }

    @Override
    public List<NetworkL2DO> getNetworkL2List(NetworkL2ExportReqVO exportReqVO) {
        return networkL2Mapper.selectList(exportReqVO);
    }

    @Override
    public void createNetworkL2List(List<NetworkL2DO> list) {
        networkL2Mapper.insertBatch(list);
    }

    @Override
    public Long getNetWorkL2Count(String typeName) {
        return networkL2Mapper.selectCount(NetworkL2DO::getTypeName,typeName);
    }

    @Override
    public void updateNetWorkL2List(List<NetWorkL2DTO> list) {
        networkL2Mapper.updateNetWorkL2List(list);
    }

    @Override
    public List<NetworkL2DO> getNetWorkL2List(String typeName) {
        return networkL2Mapper.selectList(NetworkL2DO::getTypeName,typeName);
    }

    @Override
    public List<Map> getNetWorkL2ByNameList() {
        return networkL2Mapper.selectListByName();
    }

    @Override
    public int deleteNetWorkL2ByNameList(List<NetworkL2DO> list) {
        return networkL2Mapper.deleteNetWorkL2ByNameList(list);
    }

    @Override
    public int deleteNetWorkL3ByNameList(List<NetworkL3DO> list) {
        return networkL2Mapper.deleteNetWorkL3ByNameList(list);
    }

    @Override
    public void deleteNetworkL2Byplatform(Long platformId) {
        networkL2Mapper.deleteNetworkL2Byplatform(platformId);
    }

    @Override
    public List<NetWorkL2DTO> getNetworkL2ByPlatformId(Long platformId) {
        return networkL2Mapper.getNetworkL2ByPlatformId(platformId);
    }


}
