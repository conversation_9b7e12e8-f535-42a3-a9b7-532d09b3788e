package cn.iocoder.zj.module.monitor.controller.admin.gatherasset.vo;

import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 租户资产 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class GatherAssetExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("租户绑定的采集设备id")
    private String uuid;

    @ExcelProperty("访问设备的ip地址 (例：udp:*************)")
    private String ip;

    @ExcelProperty("snmp设置的团体名")
    private String community;

    @ExcelProperty("snmp版本 (0=v1; 1=v2c; 2=v3)")
    private Integer version;

    @ExcelProperty("snmp 端口号")
    private Integer snmpPort;

    @ExcelProperty("主机名称")
    private String hostName;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private LocalDateTime createTime;

    @ExcelProperty("租户名称")
    private String tenantName;

    /**
     * @description: 资产类型
     * <AUTHOR>
     * @date 2023/8/14 15:49
     * @version 1.0
     */
    @ExcelProperty("资产类型")
    private String deviceType;
    /**
     * @description: 资产类型名称
     * <AUTHOR>
     * @date 2023/8/14 15:49
     * @version 1.0
     */
    @ExcelProperty("资产类型名称")
    private String deviceName;
    /**
     * @description: 系统类型
     * <AUTHOR>
     * @date 2023/8/14 15:49
     * @version 1.0
     */
    @ExcelProperty("系统类型")
    private String sysType;
    /**
     * @description: 系统类型名称
     * <AUTHOR>
     * @date 2023/8/14 15:49
     * @version 1.0
     */
    @ExcelProperty("系统类型名称")
    private String sysName;


    /**
     * @description: 在线状态，0 离线 ，1在线
     * <AUTHOR>
     * @date 2023/8/14 15:52
     * @version 1.0
     */
    @ExcelProperty("在线状态")
    private Integer onlineType;

}
