package cn.iocoder.zj.module.monitor.service.gatherasset;

import java.util.*;
import javax.validation.*;
import cn.iocoder.zj.module.monitor.controller.admin.gatherasset.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.gatherasset.GatherAssetDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 租户资产 Service 接口
 *
 * <AUTHOR>
 */
public interface GatherAssetService {

    /**
     * 创建租户资产
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createGatherAsset(@Valid GatherAssetCreateReqVO createReqVO);

    /**
     * 更新租户资产
     *
     * @param updateReqVO 更新信息
     */
    void updateGatherAsset(@Valid GatherAssetUpdateReqVO updateReqVO);

    /**
     * 删除租户资产
     *
     * @param id 编号
     */
    void deleteGatherAsset(Long id);

    /**
     * 获得租户资产
     *
     * @param id 编号
     * @return 租户资产
     */
    GatherAssetDO getGatherAsset(Long id);

    /**
     * 获得租户资产列表
     *
     * @param ids 编号
     * @return 租户资产列表
     */
    List<GatherAssetDO> getGatherAssetList(Collection<Long> ids);

    /**
     * 获得租户资产分页
     *
     * @param pageReqVO 分页查询
     * @return 租户资产分页
     */
    PageResult<GatherAssetDO> getGatherAssetPage(GatherAssetPageReqVO pageReqVO);

    /**
     * 获得租户资产列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 租户资产列表
     */
    List<GatherAssetDO> getGatherAssetList(GatherAssetExportReqVO exportReqVO);

    Map getAssetListByUuid(String uuid, String token_desc);

    Map updateOnlieType(String json, String tokenDesc);

    Map addAssetList(String json, String tokenDesc);

    List<GatherAssetDO> getAssetList();

    void createAssetInfoList(List<GatherAssetDO> list);

    List<GatherAssetDataRespVO> getAssetData(GatherAssetDataReqVO reqVo);

    Map<String, Object> getGatherAssetStatusCount(List<String> tenantIds, Long platformId);

    Long getGatherAssetCount(String deviceType);

    Long getGatherAssetCountBySysType(String sysType);




}
