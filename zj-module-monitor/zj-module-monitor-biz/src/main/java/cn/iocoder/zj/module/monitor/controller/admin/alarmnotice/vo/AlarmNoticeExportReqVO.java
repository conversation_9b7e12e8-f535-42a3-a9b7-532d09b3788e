package cn.iocoder.zj.module.monitor.controller.admin.alarmnotice.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 告警与通知模板关系 Excel 导出 Request VO，参数和 AlarmNoticePageReqVO 是一致的")
@Data
public class AlarmNoticeExportReqVO {

    @Schema(description = "告警配置ID")
    private Long alarmConfigId;

    @Schema(description = "通知模板id")
    private Long templateId;

    @Schema(description = "模板类型sms短信，mail邮箱")
    private String templateType;

    @Schema(description = "邮箱地址")
    private String mail;

}
