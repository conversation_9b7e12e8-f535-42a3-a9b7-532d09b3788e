package cn.iocoder.zj.module.monitor.controller.admin.bulletin.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 实时报 Excel 导出 Request VO，参数和 BulletinPageReqVO 是一致的")
@Data
public class BulletinExportReqVO {

    @Schema(description = "名称")
    private String name;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "监控资产类型")
    private String category;

    @Schema(description = "监控资产小类")
    private String app;

    @Schema(description = "监控选项")
    private String fields;

    @Schema(description = "资产ids")
    private String assetIds;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
