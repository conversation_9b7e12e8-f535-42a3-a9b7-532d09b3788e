package cn.iocoder.zj.module.monitor.handler.config;

import cn.iocoder.zj.module.monitor.dal.redis.zstack.ZstackAccessTokenRedisDAO;
import cn.iocoder.zj.module.monitor.handler.CustomRequestInterceptor;
import cn.iocoder.zj.module.monitor.handler.HttpClientWrapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

@Configuration
public class HttpClientConfig {
    @Resource
    ZstackAccessTokenRedisDAO zstackAccessTokenRedisDAO;

    @Bean
    public CustomRequestInterceptor customRequestInterceptor() {
        return new CustomRequestInterceptor(zstackAccessTokenRedisDAO);  // 通过构造函数注入
    }
    @Bean
    public HttpClientWrapper httpClientWrapper() {
        HttpClientWrapper wrapper = new HttpClientWrapper();
        wrapper.addInterceptor(new CustomRequestInterceptor(zstackAccessTokenRedisDAO));
        return wrapper;
    }
}
