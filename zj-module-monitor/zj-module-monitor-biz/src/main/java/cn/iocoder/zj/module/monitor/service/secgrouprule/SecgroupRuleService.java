package cn.iocoder.zj.module.monitor.service.secgrouprule;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.monitor.controller.admin.secgrouprule.vo.SecgroupRuleCreateReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.secgrouprule.vo.SecgroupRuleExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.secgrouprule.vo.SecgroupRulePageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.secgrouprule.vo.SecgroupRuleUpdateReqVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.secgrouprule.SecgroupRuleDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 端口组规则 Service 接口
 *
 * <AUTHOR>
 */
public interface SecgroupRuleService {

    /**
     * 创建端口组规则
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSecgroupRule(@Valid SecgroupRuleCreateReqVO createReqVO);

    /**
     * 更新端口组规则
     *
     * @param updateReqVO 更新信息
     */
    void updateSecgroupRule(@Valid SecgroupRuleUpdateReqVO updateReqVO);

    /**
     * 删除端口组规则
     *
     * @param id 编号
     */
    void deleteSecgroupRule(Long id);

    /**
     * 获得端口组规则
     *
     * @param id 编号
     * @return 端口组规则
     */
    SecgroupRuleDO getSecgroupRule(Long id);

    /**
     * 获得端口组规则列表
     *
     * @param ids 编号
     * @return 端口组规则列表
     */
    List<SecgroupRuleDO> getSecgroupRuleList(Collection<Long> ids);

    /**
     * 获得端口组规则分页
     *
     * @param pageReqVO 分页查询
     * @return 端口组规则分页
     */
    PageResult<SecgroupRuleDO> getSecgroupRulePage(SecgroupRulePageReqVO pageReqVO);

    /**
     * 获得端口组规则列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 端口组规则列表
     */
    List<SecgroupRuleDO> getSecgroupRuleList(SecgroupRuleExportReqVO exportReqVO);

    void deleteSecgroupRules(List<SecgroupRuleDO> list);

    void updateSecgroupRules(List<SecgroupRuleDO> list);

    void createSecgroupRuleList(List<SecgroupRuleDO> list);

    List<SecgroupRuleDO> getListBySecgroupUuids(List<String> secgroupUuids);
}
