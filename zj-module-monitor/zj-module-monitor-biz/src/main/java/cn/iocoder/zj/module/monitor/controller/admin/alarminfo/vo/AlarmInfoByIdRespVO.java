package cn.iocoder.zj.module.monitor.controller.admin.alarminfo.vo;

import cn.iocoder.zj.module.monitor.dal.dataobject.alarminfo.AlarmInfoDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.hertzbeat.common.entity.alerter.Alert;

import java.util.Map;

/**
 * @ClassName : AlarmInfoByIdRespVO  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/7/1  14:06
 */

@Schema(description = "管理后台 - 监控告警详情 Response VO")
@Data
public class AlarmInfoByIdRespVO {

    // 监控告警类型详情
    @Schema(description = "处理详情")
    private AlarmInfoDO alarmInfoDO;

    @Schema(description = "告警详情")
    private Alert alert;

    @Schema(description = "模板名称")
    private String AlarmName;
    @Schema(description = "模板id")
    private Long AlarmId;
    @Schema(description = "模板规则")
    private String expr;
    @Schema(description = "处理方式，0手动处理，1工单处理")
    private Integer processingType;

    @Schema(description = "工单流程详情")
    private Map<String,Object> processingOrderInfo;
}
