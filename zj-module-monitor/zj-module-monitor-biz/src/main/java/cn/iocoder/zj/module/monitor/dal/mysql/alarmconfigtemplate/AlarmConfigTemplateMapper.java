package cn.iocoder.zj.module.monitor.dal.mysql.alarmconfigtemplate;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmconfigtemplate.AlarmConfigTemplateDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.monitor.controller.admin.alarmconfigtemplate.vo.*;

/**
 * 告警配置模板 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AlarmConfigTemplateMapper extends BaseMapperX<AlarmConfigTemplateDO> {

    default PageResult<AlarmConfigTemplateDO> selectPage(AlarmConfigTemplatePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AlarmConfigTemplateDO>()
                .eqIfPresent(AlarmConfigTemplateDO::getContext, reqVO.getContext())
                .likeIfPresent(AlarmConfigTemplateDO::getAlarmName, reqVO.getAlarmName())
                .eqIfPresent(AlarmConfigTemplateDO::getDescription, reqVO.getDescription())
                .eqIfPresent(AlarmConfigTemplateDO::getSourceType, reqVO.getSourceType())
                .likeIfPresent(AlarmConfigTemplateDO::getDictLabelName, reqVO.getDictLabelName())
                .eqIfPresent(AlarmConfigTemplateDO::getDictLabelType, reqVO.getDictLabelType())
                .eqIfPresent(AlarmConfigTemplateDO::getDictLabelValue, reqVO.getDictLabelValue())
                .eqIfPresent(AlarmConfigTemplateDO::getAlarmRule, reqVO.getAlarmRule())
                .eqIfPresent(AlarmConfigTemplateDO::getAlarmVal, reqVO.getAlarmVal())
                .eqIfPresent(AlarmConfigTemplateDO::getUnit, reqVO.getUnit())
                .betweenIfPresent(AlarmConfigTemplateDO::getAlarmTime, reqVO.getAlarmTime())
                .eqIfPresent(AlarmConfigTemplateDO::getAlarmLevel, reqVO.getAlarmLevel())
                .betweenIfPresent(AlarmConfigTemplateDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(AlarmConfigTemplateDO::getUnitType, reqVO.getUnitType())
                .orderByDesc(AlarmConfigTemplateDO::getId));
    }

    default List<AlarmConfigTemplateDO> selectList(AlarmConfigTemplateExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AlarmConfigTemplateDO>()
                .eqIfPresent(AlarmConfigTemplateDO::getContext, reqVO.getContext())
                .likeIfPresent(AlarmConfigTemplateDO::getAlarmName, reqVO.getAlarmName())
                .eqIfPresent(AlarmConfigTemplateDO::getDescription, reqVO.getDescription())
                .eqIfPresent(AlarmConfigTemplateDO::getSourceType, reqVO.getSourceType())
                .likeIfPresent(AlarmConfigTemplateDO::getDictLabelName, reqVO.getDictLabelName())
                .eqIfPresent(AlarmConfigTemplateDO::getDictLabelType, reqVO.getDictLabelType())
                .eqIfPresent(AlarmConfigTemplateDO::getDictLabelValue, reqVO.getDictLabelValue())
                .eqIfPresent(AlarmConfigTemplateDO::getAlarmRule, reqVO.getAlarmRule())
                .eqIfPresent(AlarmConfigTemplateDO::getAlarmVal, reqVO.getAlarmVal())
                .eqIfPresent(AlarmConfigTemplateDO::getUnit, reqVO.getUnit())
                .betweenIfPresent(AlarmConfigTemplateDO::getAlarmTime, reqVO.getAlarmTime())
                .eqIfPresent(AlarmConfigTemplateDO::getAlarmLevel, reqVO.getAlarmLevel())
                .betweenIfPresent(AlarmConfigTemplateDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(AlarmConfigTemplateDO::getUnitType, reqVO.getUnitType())
                .orderByDesc(AlarmConfigTemplateDO::getId));
    }

}
