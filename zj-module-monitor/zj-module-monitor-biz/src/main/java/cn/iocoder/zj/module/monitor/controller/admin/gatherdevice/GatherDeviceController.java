package cn.iocoder.zj.module.monitor.controller.admin.gatherdevice;

import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.monitor.controller.admin.gatherdevice.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.gatherdevice.GatherDeviceDO;
import cn.iocoder.zj.module.monitor.convert.gatherdevice.GatherDeviceConvert;
import cn.iocoder.zj.module.monitor.service.gatherdevice.GatherDeviceService;

@Tag(name = "管理后台 - 采集设备")
@RestController
@RequestMapping("/monitor/gather-device")
@Validated
public class GatherDeviceController {

    @Resource
    private GatherDeviceService gatherDeviceService;

    //todo 上报设备修改平台id  过滤租户id 筛选 ，选平台
    @PostMapping("/create")
    @Operation(summary = "创建采集设备")
    @PreAuthorize("@ss.hasPermission('monitor:gather-device:create')")
    @OperateLog(type = CREATE)
    @TenantIgnore
    public CommonResult<Long> createGatherDevice(@Valid @RequestBody GatherDeviceCreateReqVO createReqVO) {
        return success(gatherDeviceService.createGatherDevice(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新采集设备")
    @PreAuthorize("@ss.hasPermission('monitor:gather-device:update')")
    @OperateLog(type = UPDATE)
    public CommonResult<Long> updateGatherDevice(@Valid @RequestBody GatherDeviceUpdateReqVO updateReqVO) {

        return success(gatherDeviceService.updateGatherDevice(updateReqVO));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除采集设备")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('monitor:gather-device:delete')")
    @OperateLog(type = DELETE)
    public CommonResult<Boolean> deleteGatherDevice(@RequestParam("id") Long id) {
        gatherDeviceService.deleteGatherDevice(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得采集设备")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('monitor:gather-device:query')")
    public CommonResult<GatherDeviceRespVO> getGatherDevice(@RequestParam("id") Long id) {
        GatherDeviceDO gatherDevice = gatherDeviceService.getGatherDevice(id);
        return success(GatherDeviceConvert.INSTANCE.convert(gatherDevice));
    }

    @GetMapping("/list")
    @Operation(summary = "获得采集设备列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('monitor:gather-device:query')")
    public CommonResult<List<GatherDeviceRespVO>> getGatherDeviceList(@RequestParam("ids") Collection<Long> ids) {
        List<GatherDeviceDO> list = gatherDeviceService.getGatherDeviceList(ids);
        return success(GatherDeviceConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得采集设备分页")
    @PreAuthorize("@ss.hasPermission('monitor:gather-device:query')")
    public CommonResult<PageResult<GatherDeviceRespVO>> getGatherDevicePage(@Valid GatherDevicePageReqVO pageVO) {
        PageResult<GatherDeviceDO> pageResult = gatherDeviceService.getGatherDevicePage(pageVO);
        return success(GatherDeviceConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出采集设备 Excel")
    @PreAuthorize("@ss.hasPermission('monitor:gather-device:export')")
    @OperateLog(type = EXPORT)
    public void exportGatherDeviceExcel(@Valid GatherDeviceExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<GatherDeviceDO> list = gatherDeviceService.getGatherDeviceList(exportReqVO);
        // 导出 Excel
        List<GatherDeviceExcelVO> datas = GatherDeviceConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "采集设备.xls", "数据", GatherDeviceExcelVO.class, datas);
    }



    @TenantIgnore
    @PermitAll
    @RequestMapping("/createDevice")
    @ResponseBody
    @OperateLog(type = CREATE)
    public Map createDevice(@RequestBody String json,@RequestParam String token_desc) throws IOException {
        return gatherDeviceService.createDevice(json,token_desc);
    }

    @GetMapping("/deviceSelect")
    @Operation(summary = "获得采集设备下拉框")
    @PreAuthorize("@ss.hasPermission('monitor:gather-device:query')")
    public CommonResult<List<GatherDeviceSelectVO>> deviceSelect() {
        List<GatherDeviceDO> list = gatherDeviceService.deviceSelect();
        return success(GatherDeviceConvert.INSTANCE.convertListSelect(list));
    }
    @TenantIgnore
    @PermitAll
    @RequestMapping("/getOnlineById")
    @ResponseBody
    public void getOnlineById(@RequestParam String uuid, @RequestParam String token_desc) throws IOException {
        gatherDeviceService.getOnlineById(uuid, token_desc);
    }
}
