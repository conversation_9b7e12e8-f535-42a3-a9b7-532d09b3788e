package cn.iocoder.zj.module.monitor.dal.mysql.scanip;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.module.monitor.controller.admin.scanip.vo.IPRangeVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.hardwareinfo.HardwareInfoDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.scanip.IPRangeDO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * IP段 Mapper
 */
@Mapper
public interface IPRangeMapper extends BaseMapperX<IPRangeDO> {

    /**
     * 根据状态查询IP段列表
     *
     * @param status 状态
     * @return IP段列表
     */
    default List<IPRangeDO> selectListByStatus(Integer status) {
        return selectList(new LambdaQueryWrapperX<IPRangeDO>()
                .eq(IPRangeDO::getStatus, status));
    }

    /**
     * 根据查询条件获取IP段分页
     *
     * @param pageVO 查询条件
     * @return IP段分页
     */
    default PageResult<IPRangeDO> selectPage(IPRangeVO pageVO) {
        return selectPage(pageVO, new LambdaQueryWrapperX<IPRangeDO>()
                .likeIfPresent(IPRangeDO::getName, pageVO.getName())
                .likeIfPresent(IPRangeDO::getIpRanges, pageVO.getIpRanges())
                .eqIfPresent(IPRangeDO::getStatus, pageVO.getStatus())
                .eqIfPresent(IPRangeDO::getTenantId, pageVO.getTenantId())
                .orderByDesc(IPRangeDO::getId));
    }

    /**
     * 根据查询条件获取IP段列表
     *
     * @param exportReqVO 查询条件
     * @return IP段列表
     */
    default List<IPRangeDO> selectList(IPRangeVO exportReqVO) {
        return selectList(new LambdaQueryWrapperX<IPRangeDO>()
                .likeIfPresent(IPRangeDO::getName, exportReqVO.getName())
                .likeIfPresent(IPRangeDO::getIpRanges, exportReqVO.getIpRanges())
                .eqIfPresent(IPRangeDO::getStatus, exportReqVO.getStatus())
                .eqIfPresent(IPRangeDO::getTenantId, exportReqVO.getTenantId())
                .orderByDesc(IPRangeDO::getId));
    }

    List<IPRangeDO> getPage(@Param("pageVO")IPRangeVO pageVO, @Param("mpPage") IPage<IPRangeDO> mpPage);

    void updateBatchStatus(@Param("ids")List<Long> ids, @Param("status")Integer status);

    @MapKey("task_id")
    List<Map<String, String>> getTaskConfigs();

    void delById(@Param("id") Long id);

    void deleteBatchByIds(@Param("ids") List<Long> ids);
}