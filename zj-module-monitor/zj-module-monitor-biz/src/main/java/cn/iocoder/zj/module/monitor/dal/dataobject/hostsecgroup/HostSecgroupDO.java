package cn.iocoder.zj.module.monitor.dal.dataobject.hostsecgroup;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 安全组关联云主机 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_host_secgroup")
@KeySequence("monitor_host_secgroup_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HostSecgroupDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 云主机uuid
     */
    private String hostUuid;
    /**
     * 安全组uuid
     */
    private String secgroupUuid;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;

}
