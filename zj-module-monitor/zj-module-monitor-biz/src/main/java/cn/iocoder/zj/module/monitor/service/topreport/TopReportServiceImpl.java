package cn.iocoder.zj.module.monitor.service.topreport;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.cloud.framework.warehouse.service.MetricsDataService;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.VictoriaMetricsDTO;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.warehouse.service.ZjMetricsDataService;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeDTO;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeSnapshotDTO;
import cn.iocoder.zj.module.monitor.controller.admin.storageinfo.vo.StorageInfoRespVO;
import cn.iocoder.zj.module.monitor.controller.admin.topreport.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.hardwareinfo.HardwareInfoDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostinfo.HostInfoDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.imageinfo.ImageInfoDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkvpc.NetworkVpcDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.topreport.TopReportAssetDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.topreport.TopReportDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.topreport.TopReportMetricDO;
import cn.iocoder.zj.module.monitor.dal.mysql.hardwareinfo.HardwareInfoMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.hostinfo.HostInfoMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.imageinfo.ImageInfoMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.networkvpc.NetworkVpcMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.storageinfo.StorageInfoMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.topreport.TopReportAssetMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.topreport.TopReportMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.topreport.TopReportMetricMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.volumeinfo.VolumeInfoMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.volumesnapshot.VolumeSnapshotMapper;
import cn.iocoder.zj.module.monitor.util.MetricUnit;
import cn.iocoder.zj.module.monitor.util.StringUtil;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import com.deepoove.poi.data.*;
import com.deepoove.poi.data.style.BorderStyle;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.dto.Field;
import org.apache.hertzbeat.common.entity.dto.MetricsData;
import org.apache.hertzbeat.common.entity.dto.ValueRow;
import org.apache.hertzbeat.common.support.SpringContextHolder;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.drawingml.x2006.chart.*;
import org.openxmlformats.schemas.drawingml.x2006.main.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblBorders;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.singleton;

@Service
@Validated
@Slf4j
public class TopReportServiceImpl implements TopReportService {

    @Resource
    private TopReportMapper topReportMapper;
    @Resource
    private TopReportAssetMapper topReportAssetMapper;
    @Resource
    private TopReportMetricMapper topReportMetricMapper;

    @Resource
    private HardwareInfoMapper hardwareInfoMapper;

    @Resource
    private HostInfoMapper hostInfoMapper;

    @Resource
    private StorageInfoMapper storageInfoMapper;

    @Resource
    private ImageInfoMapper imageInfoMapper;

    @Resource
    private VolumeInfoMapper volumeInfoMapper;

    @Resource
    private VolumeSnapshotMapper volumeSnapshotMapper;

    @Resource
    private NetworkVpcMapper networkVpcMapper;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private RoleApi roleApi;

    @Resource
    private AdminUserApi adminUserService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTopReport(TopReportCreateReqVO createReqVO) {
        // 复制报表对象
        TopReportDO report = BeanUtil.copyProperties(createReqVO, TopReportDO.class);

        // 插入报表
        topReportMapper.insert(report);

        // 处理资产数据
        if (CollUtil.isNotEmpty(createReqVO.getAssets())) {
            List<TopReportAssetDO> assetDOS = createReqVO.getAssets().stream()
                    .peek(x -> x.setReportId(report.getId()))
                    .map(x -> BeanUtil.copyProperties(x, TopReportAssetDO.class))
                    .toList();
            topReportAssetMapper.insertBatch(assetDOS);
        }

        // 处理指标数据
        if (CollUtil.isNotEmpty(createReqVO.getMetrics())) {
            List<TopReportMetricDO> metricDOS = createReqVO.getMetrics().stream()
                    .peek(x -> x.setReportId(report.getId()))
                    .map(x -> BeanUtil.copyProperties(x, TopReportMetricDO.class))
                    .toList();
            topReportMetricMapper.insertBatch(metricDOS);
        }

        return report.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTopReport(TopReportUpdateReqVO updateReqVO) {
        // 复制报表对象
        TopReportDO report = BeanUtil.copyProperties(updateReqVO, TopReportDO.class);

        // 更新报表
        topReportMapper.updateById(report);

        // 删除旧资产 & 旧指标数据
        topReportAssetMapper.delById(report.getId());
        topReportMetricMapper.delById(report.getId());

        // 处理资产 & 指标数据
        if (CollUtil.isNotEmpty(updateReqVO.getAssets()) || CollUtil.isNotEmpty(updateReqVO.getMetrics())) {
            updateReqVO.getAssets().forEach(x -> x.setReportId(report.getId()));
            updateReqVO.getMetrics().forEach(x -> x.setReportId(report.getId()));

            List<TopReportAssetDO> assetDOS = BeanUtil.copyToList(updateReqVO.getAssets(), TopReportAssetDO.class);
            List<TopReportMetricDO> metricDOS = BeanUtil.copyToList(updateReqVO.getMetrics(), TopReportMetricDO.class);

            if (CollUtil.isNotEmpty(assetDOS)) {
                topReportAssetMapper.insertBatch(assetDOS);
            }

            if (CollUtil.isNotEmpty(metricDOS)) {
                topReportMetricMapper.insertBatch(metricDOS);
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTopReport(Long id) {
        // 删除报表
        topReportMapper.deleteById(id);
        // 删除资产
        topReportAssetMapper.delById(id);
        // 删除指标
        topReportMetricMapper.delById(id);
    }

    @Override
    public TopReportRespVO getTopReport(Long id) {
        // 获取报表
        TopReportDO report = topReportMapper.selectById(id);
        if (report == null) {
            return null;
        }
        // 获取资产
        List<TopReportAssetDO> assets = topReportAssetMapper.selectByReportId(id);
        // 获取指标
        List<TopReportMetricDO> metrics = topReportMetricMapper.selectByReportId(id);
        // 拼装返回
        TopReportRespVO respVO = BeanUtil.copyProperties(report, TopReportRespVO.class);
        respVO.setAssets(BeanUtil.copyToList(assets, TopReportAssetVO.class));
        Map<Long, List<TopReportMetricDO>> groupedMap = metrics.stream().collect(Collectors.groupingBy(TopReportMetricDO::getPid));
        List<TopReportMetricVO> list = new ArrayList<>();
        groupedMap.forEach((key, value) -> {
            ReportMetrics metric = topReportMetricMapper.getpidmetric(key);
            TopReportMetricVO vo = new TopReportMetricVO();
            vo.setMetricCode(metric.getMetricCode());
            vo.setMetricName(metric.getMetricName());
            vo.setMetricId(metric.getId());
            vo.setSubList(BeanUtil.copyToList(value, TopReportMetricVO.class));
            list.add(vo);
        });
        respVO.setMetrics(list);
        return respVO;
    }

    @Override
    public PageResult<TopReportRespVO> getTopReportPage(TopReportPageReqVO pageVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        AdminUserRespDTO adminUserRespDTO = adminUserService.getUserById(loginUser.getId()).getData();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            if (StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
                pageVO.setTenantIds(Arrays.asList(String.valueOf(adminUserRespDTO.getTenantId()).split(",")));
            } else {
                pageVO.setTenantIds(Arrays.asList(adminUserRespDTO.getServiceTenantId().split(",")));
            }
        }

        // 分页查询
        PageResult<TopReportDO> pageResult = topReportMapper.selectPage(pageVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return new PageResult<>();
        }
        List<TopReportRespVO> list = pageResult.getList().stream().map(report -> {
            TopReportRespVO respVO = BeanUtil.copyProperties(report, TopReportRespVO.class);
            return respVO;
        }).collect(Collectors.toList());
        return new PageResult<>(list, pageResult.getTotal());
    }

    @Override
    public TopReportRespVO getdetail(TopReportRespVO topReportRespVO) {
        // 获取报表
        TopReportDO report = topReportMapper.selectById(topReportRespVO.getId());
        if (report == null) {
            return null;
        }

        // 获取资产和指标
        List<TopReportAssetDO> assets = topReportAssetMapper.selectByReportId(topReportRespVO.getId());
        List<TopReportMetricDO> metrics = topReportMetricMapper.selectByReportId(topReportRespVO.getId());

        // 确定统计类型
        String type = switch (report.getStatisticsType()) {
            case 1 -> "min";
            case 2 -> "max";
            default -> "avg";
        };

        // 处理时间周期
        String history = null;
        String step = "10s";
        Long start = null, end = null;

        if (StrUtil.isNotEmpty(topReportRespVO.getDateCycle())) {
            if (topReportRespVO.getDateCycle().contains(",")) {
                String[] split = topReportRespVO.getDateCycle().split(",");
                start = getStartOfDayTimestamp(split[0]);
                end = getEndOfDayTimestamp(split[1]);

                long durationInSeconds = (end - start) / 1000;
                step = durationInSeconds <= 24 * 60 * 60 ? "10s" :
                        durationInSeconds <= 10 * 24 * 60 * 60 ? "10m" : "10h";
            } else {
                step = switch (topReportRespVO.getDateCycle()) {
                    case "1w" -> "10m";
                    case "1m", "3m" -> "10h";
                    default -> "10s";
                };
                history = topReportRespVO.getDateCycle();
            }
        }

        // 准备返回对象
        TopReportRespVO respVO = BeanUtil.copyProperties(report, TopReportRespVO.class);

        // 获取资产ID和指标名称列表
        List<String> assetIds = assets.stream()
                .map(TopReportAssetDO::getAssetId).distinct()
                .collect(Collectors.toList());

        // 获取无效指标
        List<String> targetCodes = topReportMetricMapper.getmetrics();
        List<String> metricNames = metrics.stream()
                .map(TopReportMetricDO::getMetricCode)
                .filter(metricCode -> !targetCodes.contains(metricCode))
                .collect(Collectors.toList());


        List<Long> pids = metrics.stream().map(TopReportMetricDO::getPid).distinct().collect(Collectors.toList());
        Map<Long,Map<Long,String>> pidsMap = topReportMetricMapper.getPids(pids);

        // 获取指标数据
        ZjMetricsDataService metricsData = SpringContextHolder.getBean(ZjMetricsDataService.class);
        List<VictoriaMetricsDTO> arrayList = metricsData.getMetricData(assetIds, metricNames, history, start, end, step, type, report.getTopNum());

        MetricsDataService metricsDataService = SpringContextHolder.getBean(MetricsDataService.class);

        Map<String, Map<String, List<VictoriaMetricsDTO>>> result = new HashMap<>();
        Map<String, List<VictoriaMetricsDTO>> metricGroup = arrayList.stream()
                .collect(Collectors.groupingBy(VictoriaMetricsDTO::getAssetId));

        metricGroup.forEach((key, value) -> {
            Map<String, List<VictoriaMetricsDTO>> listMap = value.stream()
                    .collect(Collectors.groupingBy(VictoriaMetricsDTO::getMetric));

            listMap.forEach((item, values) -> {
                boolean allSame = values.stream()
                        .map(VictoriaMetricsDTO::getPidMetric)
                        .distinct()
                        .count() == 1;

                Map<String, List<VictoriaMetricsDTO>> subMap = result.computeIfAbsent(key, k -> new HashMap<>());

                if (allSame) {
                    subMap.put(item, Collections.singletonList(values.get(0)));
                } else {
                    subMap.put(item, values);
                }
            });
        });

        List<ReportAssetVO> arrays = new ArrayList<>();
        Map<String, List<?>> param = new HashMap<>();
        // 根据资产类型处理不同的数据
        switch (report.getAssetType()) {
            case "hardware":
                List<HardwareInfoDO> hardwares = hardwareInfoMapper.getHardwareByList(assetIds);
                hardwares.forEach(hardware -> {
                    Map<String, List<VictoriaMetricsDTO>> metricsMap = Optional.ofNullable(result.get(hardware.getUuid())).orElse(Collections.emptyMap());
                    for (TopReportMetricDO metric : metrics) {
                        ReportAssetVO vo = new ReportAssetVO();
                        vo.setAssetId(hardware.getUuid());
                        vo.setAssetName(hardware.getName());
                        vo.setPlatformId(hardware.getPlatformId());
                        vo.setPlatformName(hardware.getPlatformName());
                        vo.setMetricName(metric.getMetricName());
                        vo.setMetricCode(metric.getMetricCode());
                        vo.setPid(metric.getPid());
                        String value = switch (metric.getMetricCode()) {
                            case "CPUUsedUtilization" -> getMetricValue(metricsMap, "CPUUsedUtilization");
                            case "MemoryUsage" -> getMetricValue(metricsMap, "MemoryUsage");
                            case "cpuNum" -> String.valueOf(hardware.getCpuNum());
                            case "cpuSockets" -> String.valueOf(hardware.getCpuSockets());
                            case "cpuVirtual" -> String.valueOf(hardware.getTotalCpuCapacity());
                            case "cpuAssigned" -> String.valueOf(hardware.getCpuCommitRate());
                            case "memNum" -> convertToGB(hardware.getTotalMemoryCapacity());
                            case "memVirtual" -> convertToGB(hardware.getTotalVirtualMemory());
                            case "memAssigned" -> convertToGB(hardware.getMemoryCommitRate());
                            default -> "0";
                        };

                        vo.setVlaue(MetricUnit.appendUnit(metric.getMetricCode(), value));
                        arrays.add(vo);
                    }
                });
                break;

            case "host":
                List<HostInfoDO> hosts = hostInfoMapper.getHostByList(assetIds);
                hosts.forEach(host -> {
                    Map<String, List<VictoriaMetricsDTO>> metrics_map = Optional.ofNullable(result.get(host.getUuid())).orElse(Collections.emptyMap());
                    for (TopReportMetricDO metric : metrics) {
                        ReportAssetVO vo = new ReportAssetVO();
                        vo.setAssetId(host.getUuid());
                        vo.setAssetName(host.getName());
                        vo.setPlatformId(host.getPlatformId());
                        vo.setPlatformName(host.getPlatformName());
                        vo.setMetricName(metric.getMetricName());
                        vo.setMetricCode(metric.getMetricCode());
                        vo.setPid(metric.getPid());
                        String value = switch (metric.getMetricCode()) {
                            case "CPUUsedUtilization" -> getMetricValue(metrics_map,"CPUUsedUtilization");
                            case "MemoryUsage" -> getMetricValue(metrics_map, "MemoryUsage");
                            case "diskUse" -> String.valueOf(host.getDiskUsed());
                            case "cpunum" -> String.valueOf(host.getCpuNum());
                            case "memnum" -> convertToGB(host.getMemorySize());
                            case "sysdata" -> convertToGB(host.getSystemDiskSize());
                            case "data" -> convertToGB(host.getDataDiskSize());
                            default -> "0";
                        };

                        vo.setVlaue(MetricUnit.appendUnit(metric.getMetricCode(),value));
                        arrays.add(vo);
                    }
                });
                break;

            case "storage":
                List<StorageInfoRespVO> storages = storageInfoMapper.getStorageByList(assetIds);
                storages.forEach(storage -> {
                    Map<String, List<VictoriaMetricsDTO>> metrics_map = Optional.ofNullable(result.get(storage.getUuid())).orElse(Collections.emptyMap());
                    for (TopReportMetricDO metric : metrics) {
                        ReportAssetVO vo = new ReportAssetVO();
                        vo.setAssetId(storage.getUuid());
                        vo.setAssetName(storage.getName());
                        vo.setPlatformId(storage.getPlatformId());
                        vo.setPlatformName(storage.getPlatformName());
                        vo.setMetricName(metric.getMetricName());
                        vo.setMetricCode(metric.getMetricCode());
                        vo.setPid(metric.getPid());
                        String value = switch (metric.getMetricCode()) {
                            case "DiskUsedUtilization" -> getMetricValue(metrics_map, "DiskUsedUtilization");
                            case "virtuse" -> String.format("%.2f", storage.getVirtualUtilization());
                            case "total" -> convertToGB(storage.getTotalPhysicalCapacity());
                            case "virtnum" -> convertToGB(storage.getVirtualCapacity());
                            case "rate" -> String.valueOf(storage.getCommitRate());
                            case "rese" -> convertToGB(storage.getReserveCapacity());
                            case "waste" -> convertToGB(storage.getWasteCapacity());
                            default -> "0";
                        };
                        vo.setVlaue(MetricUnit.appendUnit(metric.getMetricCode(),value));
                        arrays.add(vo);
                    }
                });
                break;

            case "image":
                List<ImageInfoDO> images = imageInfoMapper.getImageByList(assetIds);
                images.forEach(image -> {
                    for (TopReportMetricDO metric : metrics) {
                        ReportAssetVO vo = new ReportAssetVO();
                        vo.setAssetId(image.getUuid());
                        vo.setAssetName(image.getName());
                        vo.setPlatformId(image.getPlatformId());
                        vo.setPlatformName(image.getPlatformName());
                        vo.setMetricName(metric.getMetricName());
                        vo.setMetricCode(metric.getMetricCode());
                        vo.setPid(metric.getPid());
                        String value = switch (metric.getMetricCode()) {
                            case "size" -> convertToGB(image.getSize());
                            case "mem" -> convertToGB(image.getMinMemory());
                            case "disk" -> convertToGB(image.getMinDisk());
                            default -> "0";
                        };
                        vo.setVlaue(MetricUnit.appendUnit(metric.getMetricCode(),value));
                        arrays.add(vo);
                    }
                });
                break;

            case "disk":
                List<VolumeDTO> volumes = volumeInfoMapper.getvolumeByList(assetIds);
                volumes.forEach(volume -> {
                    for (TopReportMetricDO metric : metrics) {
                        ReportAssetVO vo = new ReportAssetVO();
                        vo.setAssetId(volume.getUuid());
                        vo.setAssetName(volume.getName());
                        vo.setPlatformId(volume.getPlatformId());
                        vo.setPlatformName(volume.getPlatformName());
                        vo.setMetricName(metric.getMetricName());
                        vo.setMetricCode(metric.getMetricCode());
                        vo.setPid(metric.getPid());
                        String value = switch (metric.getMetricCode()) {
                            case "vmsize" -> convertToGB(volume.getSize());
                            case "vmactualsize" -> convertToGB(volume.getActualSize());
                            case "vmiops" -> String.valueOf(volume.getMaxIops());
                            case "vmthrou" -> String.valueOf(volume.getThroughput());
                            default -> "0";
                        };
                        vo.setVlaue(MetricUnit.appendUnit(metric.getMetricCode(),value));
                        arrays.add(vo);
                    }
                });
                break;

            case "snapshot":
                List<VolumeSnapshotDTO> snapshots = volumeSnapshotMapper.getsnapByList(assetIds);
                snapshots.forEach(snapshot -> {
                    for (TopReportMetricDO metric : metrics) {
                        ReportAssetVO vo = new ReportAssetVO();
                        vo.setAssetId(snapshot.getUuid());
                        vo.setAssetName(snapshot.getName());
                        vo.setPlatformId(snapshot.getPlatformId());
                        vo.setPlatformName(snapshot.getPlatformName());
                        vo.setMetricName(metric.getMetricName());
                        vo.setMetricCode(metric.getMetricCode());
                        vo.setPid(metric.getPid());
                        if ("snapsize".equals(metric.getMetricCode())) {
                            vo.setVlaue(MetricUnit.appendUnit(metric.getMetricCode(),convertToGB(snapshot.getSize())));
                        }
                        arrays.add(vo);
                    }
                });
                break;

            case "cloudNetwork":
                List<NetworkVpcDO> networks = networkVpcMapper.getnetvpcByList(assetIds);
                networks.forEach(network -> {
                    for (TopReportMetricDO metric : metrics) {
                        ReportAssetVO vo = new ReportAssetVO();
                        vo.setAssetId(network.getUuid());
                        vo.setAssetName(network.getName());
                        vo.setPlatformId(network.getPlatformId());
                        vo.setPlatformName(network.getPlatformName());
                        vo.setMetricName(metric.getMetricName());
                        vo.setMetricCode(metric.getMetricCode());
                        vo.setPid(metric.getPid());
                        if ("netvpcsize".equals(metric.getMetricCode())) {
                            vo.setVlaue(MetricUnit.appendUnit(metric.getMetricCode(),convertToGB(network.getMemorySize())));
                        }
                        arrays.add(vo);
                    }
                });
                break;

            case "service":
            case "db":
            case "mid":
            case "os":
            case "cn":
            case "auto":
            case "firewall":
            case "network":
            case "stored":
                metrics.forEach(metric -> {
                    String metricCode = metric.getMetricCode();
                    String metricName = metric.getMetricName();
                    Long pid = metric.getPid();

                    // 预获取父级指标信息
                    Map<Long, String> pidInfo = pidsMap.get(pid);
                    final String pidName = pidInfo != null ? pidInfo.get("metric_name") : null;
                    final String parentMetricCode = pidInfo != null ? pidInfo.get("metric_code") : null;

                    for (TopReportAssetDO asset : assets) {
                        String assetId = asset.getAssetId();
                        String assetName = asset.getAssetName();

                        if (!result.containsKey(assetId)) continue;

                        List<VictoriaMetricsDTO> dtos = result.get(assetId).get(metricCode);
                        if (dtos == null) continue;

                        // 预获取单位信息
                        final String unit = (parentMetricCode != null) ?
                                Optional.ofNullable(metricsDataService.getMetricData(Long.valueOf(assetId), parentMetricCode))
                                        .filter(data -> CollUtil.isNotEmpty(data.getFields()))
                                        .map(data -> data.getFields().stream()
                                                .filter(field -> field.getName().equals(metricCode))
                                                .map(Field::getUnit)
                                                .findFirst()
                                                .orElse(null))
                                        .orElse(null) : null;

                        // 处理dto对象
                        dtos.stream()
                                .filter(dto -> dto != null
                                        && Objects.equals(dto.getPidMetric(), parentMetricCode)
                                        && Objects.equals(dto.getAssetId(), assetId))
                                .forEach(dto -> {
                                    ReportAssetVO vo = new ReportAssetVO();
                                    vo.setAssetId(assetId);
                                    vo.setPlatformId(asset.getPlatformId());
                                    vo.setPlatformName(asset.getPlatformName());
                                    vo.setMetricCode(metricCode);
                                    vo.setMetricName(metricName);
                                    vo.setPid(pid);
                                    vo.setPidName(pidName);

                                    String value = dto.getValue() != null ? dto.getValue() : "0";
                                    vo.setVlaue(StrUtil.isNotEmpty(unit) ? value + "(" + unit + ")" : value);
                                    vo.setAssetName(StrUtil.isNotEmpty(dto.getInterfaceName())
                                            ? assetName + "/" + dto.getInterfaceName() : assetName);

                                    arrays.add(vo);
                                });
                    }
                });

                break;
            case "infra":
                metrics.forEach(metric -> {
                    for (TopReportAssetDO asset : assets) {
                        String assetId = asset.getAssetId();
                        ReportAssetVO vo = new ReportAssetVO();
                        vo.setAssetId(assetId);
                        vo.setPlatformId(asset.getPlatformId());
                        vo.setPlatformName(asset.getPlatformName());
                        vo.setMetricCode(metric.getMetricCode());
                        vo.setMetricName(metric.getMetricName());
                        vo.setPid(metric.getPid());
                        vo.setAssetName(asset.getAssetName());

                        if (pidsMap.containsKey(metric.getPid())) {
                            String metricCode = pidsMap.get(metric.getPid()).get("metric_code");
                            MetricsData data = metricsDataService.getMetricsData(Long.valueOf(assetId), metricCode);
                            String metricName = pidsMap.get(metric.getPid()).get("metric_name");
                            Map<String, String> tmpItem = new HashMap<>();
                            for (ValueRow valueRow : data.getValueRows()) {
                                for (int i = 0; i < valueRow.getValues().size(); i++) {
                                    if (valueRow.getValues().get(i).getOrigin() == null) {
                                        tmpItem.put(data.getFields().get(i).getName(), "0");
                                    } else {
                                        tmpItem.put(data.getFields().get(i).getName(), valueRow.getValues().get(i).getOrigin() + "(" + data.getFields().get(i).getUnit() + ")");
                                    }
                                }
                            }
                            vo.setPidName(metricName);
                            if(tmpItem.containsKey(metric.getMetricCode())){
                                String value = tmpItem.get(metric.getMetricCode());
                                vo.setVlaue(value);
                            }
                        }
                        arrays.add(vo);
                    }
                });
                break;
        }
        param.putAll(arrays.stream()
                .collect(Collectors.groupingBy(ReportAssetVO::getMetricCode))
                .entrySet()
                .stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> sortAndLimit(entry.getValue(), report.getTopNum(), ReportAssetVO::getVlaue, true)
                )));
        // 设置返回数据
        respVO.setParams(param);
        respVO.setAssets(BeanUtil.copyToList(assets, TopReportAssetVO.class));
        respVO.setMetrics(BeanUtil.copyToList(metrics, TopReportMetricVO.class));

        return respVO;
    }


    public static <T> List<T> sortAndLimit(List<T> items, int num, Function<T, ?> keyExtractor, boolean isDesc) {
        if (items == null || items.isEmpty()) {
            return Collections.emptyList();
        }

        Function<T, Double> keyConverter = obj -> {
            Object value = keyExtractor.apply(obj);
            return convertToDouble(value);
        };

        Comparator<Double> doubleComparator = Comparator.nullsLast(
                isDesc ? Comparator.<Double>reverseOrder() : Comparator.<Double>naturalOrder()
        );

        return items.stream()
                .sorted(Comparator.comparing(keyConverter, doubleComparator))
                .limit(num)
                .collect(Collectors.toList());
    }

    private String getMetricValue(Map<String, List<VictoriaMetricsDTO>> metricsMap, String metricCode) {
        return Optional.ofNullable(metricsMap.get(metricCode))
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(0).getValue())
                .orElse("0");
    }

    private static Double convertToDouble(Object value) {
        if (value == null) return 0.0;
        try {
            BigDecimal decimal;

            if (value instanceof Number) {
                decimal = new BigDecimal(value.toString());
            } else if (value instanceof String str) {
                str = str.trim();
                int idx = str.indexOf("(");
                if (idx != -1) {
                    str = str.substring(0, idx).trim();
                }
                if (str.isEmpty()) return 0.0;
                decimal = new BigDecimal(str);
            } else {
                return 0.0;
            }

            // 保留2位小数并四舍五入
            decimal = decimal.setScale(2, RoundingMode.HALF_UP);
            return decimal.doubleValue();

        } catch (Exception e) {
            System.err.println("转换为Double失败: " + value + " → " + e.getMessage());
            return 0.0;
        }
    }
    public static void formatReportValueWithUnifiedUnit(List<ReportAssetVO> reportItems) {
        if (reportItems == null || reportItems.isEmpty()) return;

        BigDecimal maxNumber = BigDecimal.ZERO;

        // Step 1: 预处理，找出最大值
        for (ReportAssetVO vo : reportItems) {
            String value = vo.getVlaue();
            if (value == null || value.isEmpty()) continue;

            String text = value.trim();
            String numberPart = text.contains("(") ? text.substring(0, text.indexOf("(")).trim() : text;

            try {
                BigDecimal num = new BigDecimal(numberPart);
                if (num.compareTo(maxNumber) > 0) {
                    maxNumber = num;
                }
            } catch (Exception ignored) {}
        }

        // Step 2: 判断统一单位
        String unit = null;
        BigDecimal divisor = BigDecimal.ONE;

        if (maxNumber.compareTo(BigDecimal.valueOf(100_000_000)) >= 0) {
            unit = "亿";
            divisor = BigDecimal.valueOf(100_000_000);
        } else if (maxNumber.compareTo(BigDecimal.valueOf(10_000_000)) >= 0) {
            unit = "千万";
            divisor = BigDecimal.valueOf(10_000_000);
        } else if (maxNumber.compareTo(BigDecimal.valueOf(1_000_000)) >= 0) {
            unit = "百万";
            divisor = BigDecimal.valueOf(1_000_000);
        } else if (maxNumber.compareTo(BigDecimal.valueOf(10_000)) >= 0) {
            unit = "万";
            divisor = BigDecimal.valueOf(10_000);
        }

        if (unit == null) return; // 全部都不需要转换，直接退出

        // Step 3: 应用统一单位缩放
        for (ReportAssetVO vo : reportItems) {
            String value = vo.getVlaue();
            if (value == null || value.isEmpty()) continue;

            String text = value.trim();
            String numberPart = text;
            String suffix = "";
            int bracketIndex = text.indexOf("(");

            if (bracketIndex != -1) {
                numberPart = text.substring(0, bracketIndex).trim();
                suffix = text.substring(bracketIndex);
            }

            try {
                BigDecimal num = new BigDecimal(numberPart);
                BigDecimal scaled = num.divide(divisor, 2, RoundingMode.HALF_UP);
                String scaledStr = scaled.stripTrailingZeros().toPlainString();

                if (!suffix.isEmpty()) {
                    int insertAt = suffix.indexOf("(");
                    if (insertAt != -1) {
                        suffix = new StringBuilder(suffix).insert(insertAt + 1, unit).toString();
                    }
                } else {
                    suffix = "(" + unit + ")";
                }

                vo.setVlaue(scaledStr + suffix);
            } catch (Exception e) {
                System.err.println("统一单位转换失败: " + value);
            }
        }
    }

    @Override
    public List<ReportMetrics> getreportmetric(Long id) {
        if(id == null){
            return topReportMetricMapper.getreportmetric(0L);
        }else {
            List<ReportMetrics> list = new ArrayList<>();
            ReportMetrics metrics = topReportMetricMapper.getpidmetric(id);
            findChildrenRecursively(metrics);
            list.add(metrics);
            return list;
        }
    }

    @Override
    public void exportReport(TopReportRespVO topReportRespVO, HttpServletResponse response) {
        // 获取报表详情数据
        TopReportRespVO detailReport = getdetail(topReportRespVO);
        if (detailReport == null || detailReport.getParams() == null || detailReport.getParams().isEmpty()) {
            return;
        }

        String data = resolveDateRange(topReportRespVO.getDateCycle());

        // 准备基础参数
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("tenantName", detailReport.getReportName());
        paramMap.put("inspectionTime", data);

        // 准备 items 列表，用于模板循环
        List<Map<String, Object>> items = new ArrayList<>();

        // 处理每个指标分组
        for (Map.Entry<String, List<?>> entry : detailReport.getParams().entrySet()) {
            List<?> value = entry.getValue();

            if (value == null || value.isEmpty()) {
                continue;
            }

            List<ReportAssetVO> reportItems = (List<ReportAssetVO>) value;
            Map<Long, List<ReportAssetVO>> pidGroup = reportItems.stream()
                    .collect(Collectors.groupingBy(ReportAssetVO::getPid));

            pidGroup.forEach((pid, pids) -> {
                formatReportValueWithUnifiedUnit(pids);

                if (pids.isEmpty()) {
                    return;
                }

                // 添加标题
                String pidName = pids.get(0).getPidName();
                String metric = pids.get(0).getMetricName();
                String metricName = StrUtil.isNotEmpty(pidName)
                        ? pidName + "/" + metric
                        : metric;

                // 准备图表数据
                int size = pids.size();
                String[] assetNames = new String[size];
                Double[] metricValues = new Double[size];

                for (int i = 0; i < size; i++) {
                    ReportAssetVO item = pids.get(i);
                    assetNames[i] = item.getAssetName();
                    metricValues[i] = convertToDouble(item.getVlaue());
                }

                // 创建图表
                ChartMultiSeriesRenderData chart = new ChartMultiSeriesRenderData();
                chart.setCategories(assetNames);
                chart.setSeriesDatas(Arrays.asList(new SeriesRenderData(metricName, metricValues)));

                // 表格数据
                List<RowRenderData> tableRows = new ArrayList<>();
                tableRows.add(Rows.of("资源名称", "平台名称", "指标", "指标值")
                        .center()
                        .bgColor("0F9ED5")
                        .create());

                for (int i = 0; i < pids.size(); i++) {
                    ReportAssetVO item = pids.get(i);
                    String bgColor = (i % 2 == 0) ? "FFFFFF" : "CAEDFB";
                    tableRows.add(Rows.of(
                                    item.getAssetName(),
                                    item.getPlatformName(),
                                    item.getMetricName(),
                                    item.getVlaue())
                            .center()
                            .bgColor(bgColor)
                            .create());
                }

                TableRenderData table = Tables.of(tableRows.toArray(new RowRenderData[0]))
                        .border(BorderStyle.builder()
                                .withColor("0F9ED5")
                                .withSize(1)
                                .withSpace(1)
                                .withType(XWPFTable.XWPFBorderType.SINGLE)
                                .build())
                        .create();

                // 添加到 items
                Map<String, Object> itemMap = new HashMap<>();
                itemMap.put("title", metricName);
                itemMap.put("chart", chart);
                itemMap.put("table", table);
                items.add(itemMap);
            });
        }

        // 添加到主参数中
        paramMap.put("items", items);

        createAndDownloadWord(paramMap, response);
    }


    private static void createAndDownloadWord(Map<String, Object> paramMap, HttpServletResponse response) {
        log.info("生成word参数paramMap = {}", paramMap);
        try {
            // 创建文档
            XWPFDocument document = new XWPFDocument();

            // 添加标题
            XWPFParagraph title = document.createParagraph();
            title.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun titleRun = title.createRun();
            titleRun.setText("皆信云星辰TOP报表");
            titleRun.setFontSize(20);
            titleRun.setBold(true);

            // 添加导出时间
            XWPFParagraph exportTimeParagraph = document.createParagraph();
            exportTimeParagraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun exportTimeRun = exportTimeParagraph.createRun();
            exportTimeRun.setText("时间：" + paramMap.get("inspectionTime").toString());
            exportTimeRun.setFontSize(12);
            exportTimeRun.setColor("888888");

            // 添加副标题
            XWPFParagraph subTitle = document.createParagraph();
            subTitle.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun subTitleRun = subTitle.createRun();
            subTitleRun.setText(paramMap.get("tenantName").toString());
            subTitleRun.setFontSize(16);

            // 处理每个指标项
            List<Map<String, Object>> items = (List<Map<String, Object>>) paramMap.get("items");
            if (items == null || items.isEmpty()) return; // 确保有数据

            for (Map<String, Object> item : items) {
                // 添加指标标题
                XWPFParagraph metricTitle = document.createParagraph();
                XWPFRun metricTitleRun = metricTitle.createRun();
                metricTitleRun.setText(item.get("title").toString());
                metricTitleRun.setFontSize(14);
                metricTitleRun.setBold(true);

                // 添加图表
                ChartMultiSeriesRenderData chartData = (ChartMultiSeriesRenderData) item.get("chart");
                XWPFChart chart = document.createChart(14 * Units.EMU_PER_CENTIMETER, 6 * Units.EMU_PER_CENTIMETER);

                // 设置图表数据
                CTChart ctChart = chart.getCTChart();
                CTPlotArea ctPlotArea = ctChart.getPlotArea();

                // 创建柱状图
                CTBarChart ctBarChart = ctPlotArea.addNewBarChart();
                ctBarChart.addNewVaryColors().setVal(false); // 使用相同颜色
                ctBarChart.addNewBarDir().setVal(STBarDir.COL);
                int dataCount = chartData.getCategories().length;
                int gapWidth = (dataCount <= 10) ? 1000 : 50; // 默认10个数据以下gap=150，超过则gap=50
                ctBarChart.addNewGapWidth().setVal(gapWidth);

                // 设置图表样式
                CTTitle title1 = ctChart.addNewTitle();
                title1.addNewOverlay().setVal(false);
                title1.addNewTx().addNewRich().addNewBodyPr();
                CTTextBody textBody = title1.getTx().getRich();
                CTTextParagraph para = textBody.addNewP();
                CTRegularTextRun r = para.addNewR();
                r.setT(chartData.getChartTitle());

                // 添加系列
                List<SeriesRenderData> seriesDataList = chartData.getSeriesDatas();
                for (int i = 0; i < seriesDataList.size(); i++) {
                    SeriesRenderData series = seriesDataList.get(i);
                    CTBarSer ctBarSer = ctBarChart.addNewSer();
                    ctBarSer.addNewIdx().setVal(i);

                    // 设置系列样式
                    CTShapeProperties ctShapeProperties = ctBarSer.addNewSpPr();
                    CTSolidColorFillProperties fillProperties = ctShapeProperties.addNewSolidFill();
                    CTSRgbColor color = fillProperties.addNewSrgbClr();
                    color.setVal(new byte[] { (byte) 0x4F, (byte) 0x81, (byte) 0xBD }); // 设置蓝色

                    // 边框
                    CTLineProperties ln = ctShapeProperties.addNewLn();
                    ln.setW(9525);
                    ln.addNewSolidFill().addNewSrgbClr().setVal(new byte[]{(byte) 0xD9, (byte) 0xD9, (byte) 0xD9});

                    // 设置类别轴（X轴）数据
                    CTAxDataSource cttAxDataSource = ctBarSer.addNewCat();
                    CTStrRef ctStrRef = cttAxDataSource.addNewStrRef();
                    CTStrData ctStrData = ctStrRef.addNewStrCache();
                    String[] categories = chartData.getCategories();
                    ctStrData.addNewPtCount().setVal(categories.length);
                    for (int j = 0; j < categories.length; j++) {
                        CTStrVal ctStrVal = ctStrData.addNewPt();
                        ctStrVal.setIdx(j);
                        ctStrVal.setV(categories[j]);
                    }

                    // 设置数值
                    CTNumDataSource ctNumDataSource = ctBarSer.addNewVal();
                    CTNumRef ctNumRef = ctNumDataSource.addNewNumRef();
                    CTNumData ctNumData = ctNumRef.addNewNumCache();
                    Double[] values = (Double[]) series.getValues();
                    ctNumData.addNewPtCount().setVal(values.length);
                    for (int j = 0; j < values.length; j++) {
                        CTNumVal ctNumVal = ctNumData.addNewPt();
                        ctNumVal.setIdx(j);
                        ctNumVal.setV(String.valueOf(values[j]));
                    }
                }

                // 添加坐标轴
                ctBarChart.addNewAxId().setVal(123456);
                ctBarChart.addNewAxId().setVal(123457);

                // 创建分类轴(X轴) - 禁用网格线
                CTCatAx ctCatAx = ctPlotArea.addNewCatAx();
                ctCatAx.addNewAxId().setVal(123456);
                ctCatAx.addNewScaling().addNewOrientation().setVal(STOrientation.MIN_MAX);
                ctCatAx.addNewDelete().setVal(false);
                ctCatAx.addNewAxPos().setVal(STAxPos.B);
                ctCatAx.addNewCrossAx().setVal(123457);
                ctCatAx.addNewTickLblPos().setVal(STTickLblPos.NEXT_TO);

                CTTextBody xAxisText = ctCatAx.addNewTxPr();
                CTTextParagraph xAxisPara = xAxisText.addNewP();
                CTTextParagraphProperties xAxisParaPr = xAxisPara.addNewPPr();
                xAxisParaPr.addNewDefRPr();
                xAxisParaPr.getDefRPr().setSz(600);
                CTSolidColorFillProperties xAxisFill = xAxisParaPr.getDefRPr().addNewSolidFill();
                CTSRgbColor xAxisColor = xAxisFill.addNewSrgbClr();
                xAxisColor.setVal(new byte[]{(byte)0x00, (byte)0x00, (byte)0x00});

                // 创建值轴(Y轴) - 只保留横向网格线
                CTValAx ctValAx = ctPlotArea.addNewValAx();
                ctValAx.addNewAxId().setVal(123457);
                ctValAx.addNewScaling().addNewOrientation().setVal(STOrientation.MIN_MAX);
                ctValAx.addNewDelete().setVal(false);
                ctValAx.addNewAxPos().setVal(STAxPos.L);
                ctValAx.addNewCrossAx().setVal(123456);
                ctValAx.addNewTickLblPos().setVal(STTickLblPos.NEXT_TO);

                // 设置Y轴标签的字体大小和颜色
                CTTextBody yAxisText = ctValAx.addNewTxPr();
                CTTextParagraph yAxisPara = yAxisText.addNewP();
                CTTextParagraphProperties yAxisParaPr = yAxisPara.addNewPPr();
                yAxisParaPr.addNewDefRPr();

                // 设置字体大小（14磅，可根据需要调整）
                yAxisParaPr.getDefRPr().setSz(900);

                // 设置字体颜色
                CTSolidColorFillProperties fill = yAxisParaPr.getDefRPr().addNewSolidFill();
                CTSRgbColor color = fill.addNewSrgbClr();
                color.setVal(new byte[]{(byte)0x00, (byte)0x00, (byte)0x00});

                // 设置Y轴网格线（横向虚线）
                CTShapeProperties gridLineShape = ctPlotArea.addNewSpPr();
                CTLineProperties gridLine = gridLineShape.addNewLn();
                gridLine.setW(9525); // 线宽约1磅
                gridLine.addNewSolidFill().addNewSrgbClr().setVal(new byte[]{(byte)0xD9, (byte)0xD9, (byte)0xD9});
                gridLine.addNewPrstDash().setVal(STPresetLineDashVal.DOT);
                ctValAx.addNewMajorGridlines().addNewSpPr().set(gridLineShape);
                ctValAx.addNewSpPr().addNewLn().addNewNoFill(); // Y轴

                // 表格数据
                TableRenderData table = (TableRenderData) item.get("table");
                List<RowRenderData> rows = table.getRows();
                if (rows == null || rows.isEmpty()) return; // 确保表格有数据

                // 添加图表后间隔
                XWPFParagraph chartAfterSpace = document.createParagraph();
                chartAfterSpace.setSpacingBefore(30);
                chartAfterSpace.setSpacingAfter(30);
                chartAfterSpace.setSpacingLineRule(LineSpacingRule.EXACT);

                // 创建表格
                XWPFTable xwpfTable = document.createTable(rows.size(), 4); // 直接指定行数和列数
                xwpfTable.setWidth("96%");

                // 设置边框样式 - 恢复为默认黑色边框
                CTTblPr tblPr = xwpfTable.getCTTbl().getTblPr();
                CTTblBorders borders = tblPr.addNewTblBorders();
                int borderSize = 5; // 默认边框大小

                // 使用默认黑色边框（不设置颜色属性即为默认黑色）
                borders.addNewTop().setVal(STBorder.SINGLE);
                borders.getTop().setSz(BigInteger.valueOf(borderSize));
                borders.addNewBottom().setVal(STBorder.SINGLE);
                borders.getBottom().setSz(BigInteger.valueOf(borderSize));
                borders.addNewLeft().setVal(STBorder.SINGLE);
                borders.getLeft().setSz(BigInteger.valueOf(borderSize));
                borders.addNewRight().setVal(STBorder.SINGLE);
                borders.getRight().setSz(BigInteger.valueOf(borderSize));
                borders.addNewInsideH().setVal(STBorder.SINGLE);
                borders.getInsideH().setSz(BigInteger.valueOf(borderSize));
                borders.addNewInsideV().setVal(STBorder.SINGLE);
                borders.getInsideV().setSz(BigInteger.valueOf(borderSize));

                // 渲染表头
                RowRenderData headerRowData = rows.get(0);
                List<CellRenderData> headerCells = headerRowData.getCells();
                XWPFTableRow headerRow = xwpfTable.getRow(0); // 获取第一行

                for (int i = 0; i < headerCells.size(); i++) {
                    XWPFTableCell cell = headerRow.getCell(i);
                    cell.setText(extractText(headerCells.get(i)));
                    cell.setColor("D9E1F2"); // 设置表头背景色
                    cell.getParagraphs().get(0).setAlignment(ParagraphAlignment.CENTER);
                }

                // 渲染内容行
                for (int rowIndex = 1; rowIndex < rows.size(); rowIndex++) {
                    RowRenderData rowData = rows.get(rowIndex);
                    List<CellRenderData> cells = rowData.getCells();
                    XWPFTableRow dataRow = xwpfTable.getRow(rowIndex); // 使用已有的行

                    for (int colIndex = 0; colIndex < headerCells.size(); colIndex++) {
                        XWPFTableCell cell = dataRow.getCell(colIndex);
                        XWPFParagraph paragraph = cell.getParagraphs().get(0);
                        // 清除现有内容（如果有）
                        for (int i = paragraph.getRuns().size() - 1; i >= 0; i--) {
                            paragraph.removeRun(i);
                        }
                        // 创建新的Run对象
                        XWPFRun run = paragraph.createRun();
                        String text = (colIndex < cells.size()) ? extractText(cells.get(colIndex)) : "";
                        // 内容超出处理
                        int maxLen = 50;
                        for (int i = 0; i < text.length(); i += maxLen) {
                            String part = text.substring(i, Math.min(i + maxLen, text.length()));
                            if (i > 0) run.addBreak();
                            run.setText(part);
                        }

                        // 设置字体样式
                        run.setFontSize(8); // 字体大小
                        run.setColor("000000"); // 字体颜色（黑色）

                        // 设置单元格背景色（可选）
                        if (rowIndex % 2 == 0) {
                            cell.setColor("F2F2F2"); // 偶数行浅灰色背景
                        } else {
                            cell.setColor("FFFFFF"); // 奇数行白色背景
                        }

                        paragraph.setAlignment(ParagraphAlignment.CENTER); // 居中
                    }
                }

                // 添加换行
                XWPFParagraph chartSpace = document.createParagraph();
                chartSpace.setSpacingBefore(0);
                chartSpace.setSpacingAfter(50); // 控制间距
                chartSpace.setSpacingLineRule(LineSpacingRule.EXACT);
            }

            // 设置文件名
            String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String d = paramMap.get("tenantName") + "-" + dateStr + "-top报表";

            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(d, "UTF-8") + ".docx");

            // 将生成的Word文档写入响应输出流
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                 OutputStream out = response.getOutputStream()) {
                document.write(outputStream);
                out.write(outputStream.toByteArray());
                out.flush();
            }
        } catch (Exception e) {
            log.error("创建Word文档异常，异常详情：\n{}", e);
        }
    }

    private void findChildrenRecursively(ReportMetrics metrics) {
        List<ReportMetrics> children = topReportMetricMapper.getreportmetric(metrics.getId());
        if (!children.isEmpty()) {
            metrics.setChildren(children);
            for (ReportMetrics child : children) {
                findChildrenRecursively(child);
            }
        }
    }

    private static String extractText(CellRenderData cell) {
        if (cell == null || cell.getParagraphs() == null || cell.getParagraphs().isEmpty()) return "";
        ParagraphRenderData para = cell.getParagraphs().get(0);
        List<RenderData> contents = para.getContents();
        if (contents == null || contents.isEmpty()) return "";

        for (RenderData render : contents) {
            if (render instanceof TextRenderData) {
                return ((TextRenderData) render).getText();
            }
        }
        return "";
    }

    private static String convertToGB(Object obj) {
        if (obj == null) return "0";

        try {
            BigDecimal input;

            if (obj instanceof BigDecimal) {
                input = (BigDecimal) obj;
            } else if (obj instanceof Number) {
                input = new BigDecimal(((Number) obj).toString());
            } else if (obj instanceof String) {
                String str = ((String) obj).trim();
                if (str.isEmpty()) return "0";
                input = new BigDecimal(str);
            } else {
                // 其他类型直接返回0或抛异常视业务而定
                return "0";
            }

            return input.divide(BigDecimal.valueOf(1024L * 1024 * 1024), 2, RoundingMode.HALF_UP)
                    .toPlainString();

        } catch (NumberFormatException e) {
            return "0"; // 遇到非法字符串（如 "abc"）返回0
        }
    }

    public static String resolveDateRange(String dateCycle) {
        ZoneId zone = ZoneId.systemDefault();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        long startMillis, endMillis;

        if (StrUtil.isEmpty(dateCycle)) return "";

        if (dateCycle.contains(",")) {
            // 自定义日期范围
            String[] split = dateCycle.split(",");
            startMillis = getStartOfDayTimestamp(split[0]);
            endMillis = getEndOfDayTimestamp(split[1]);
        } else {
            ZonedDateTime now = ZonedDateTime.now(zone);
            ZonedDateTime start;

            switch (dateCycle) {
                case "1h" -> start = now.minusHours(1);
                case "1d" -> start = now.minusDays(1);
                case "1w" -> start = now.minusWeeks(1);
                case "1m" -> start = now.minusMonths(1);
                case "3m" -> start = now.minusMonths(3);
                default -> throw new IllegalArgumentException("不支持的时间粒度：" + dateCycle);
            }

            startMillis = start.toInstant().toEpochMilli();
            endMillis = now.toInstant().toEpochMilli();
        }

        String startStr = Instant.ofEpochMilli(startMillis).atZone(zone).format(formatter);
        String endStr = Instant.ofEpochMilli(endMillis).atZone(zone).format(formatter);
        return startStr + " ~ " + endStr;
    }


    public static Long getStartOfDayTimestamp(String dateStr) {
        LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        return date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    public static Long getEndOfDayTimestamp(String dateStr) {
        LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        return date.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }
}
