package cn.iocoder.zj.module.monitor.controller.admin.gatherdevice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

/**
* 采集设备 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class GatherDeviceBaseVO {

    @Schema(description = "采集设备uuid")
    private String uuid;

    @Schema(description = "采集设备ip")
    private String gatherIp;

    @Schema(description = "采集设备名称")
    private String gatherName;

    @Schema(description = "0 未绑定 ,1 已绑定")
    private Integer type;

    @Schema(description = "平台名称")
    private String platformName;
    @Schema(description = "平台id")
    private Long platformId;
}
