package cn.iocoder.zj.module.monitor.controller.admin.alarmnotice.vo;

import cn.iocoder.zj.framework.common.validation.Mobile;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

/**
* 告警与通知模板关系 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class AlarmNoticeBaseVO {

    @Schema(description = "告警配置ID", required = true)
    private Long alarmConfigId;

    @Schema(description = "通知模板id", required = true)
    @NotNull(message = "通知模板id不能为空")
    private Long templateId;

    @Schema(description = "模板类型sms短信，mail邮箱", required = true)
    @NotNull(message = "模板类型不能为空")
    private String templateType;

    @Schema(description = "邮箱地址", required = true)
    @NotNull(message = "邮箱地址不能为空")
    @Email(message = "邮箱格式不正确")
    private String mail;

    @Schema(description = "邮箱地址", required = true)
    @NotNull(message = "邮箱地址不能为空")
    @Mobile
    private String mobile;

}
