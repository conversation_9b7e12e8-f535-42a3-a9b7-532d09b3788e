//package cn.iocoder.zj.module.monitor.service.chart;
//
//import cn.hutool.core.collection.CollectionUtil;
//import cn.hutool.core.convert.Convert;
//import cn.hutool.core.date.DateTime;
//import cn.hutool.core.date.DateUtil;
//import cn.iocoder.zj.module.monitor.controller.admin.chart.vo.WordExportReqVO;
//import cn.iocoder.zj.module.monitor.controller.admin.chart.vo.WordInfluxDO;
//import cn.iocoder.zj.module.monitor.dal.dataobject.hostinfo.HostInfoDO;
//import cn.iocoder.zj.module.monitor.framework.influx.config.InfluxDBTemplate;
//import cn.iocoder.zj.module.monitor.service.hardwareinfo.HardwareInfoService;
//import cn.iocoder.zj.module.monitor.service.hostinfo.HostInfoService;
//import cn.iocoder.zj.module.monitor.service.storageinfo.StorageInfoService;
//import cn.iocoder.zj.module.monitor.util.StringUtil;
//import com.deepoove.poi.XWPFTemplate;
//import com.deepoove.poi.data.*;
//import com.deepoove.poi.xwpf.NiceXWPFDocument;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.core.io.ClassPathResource;
//import org.springframework.stereotype.Service;
//import org.springframework.validation.annotation.Validated;
//
//import javax.annotation.Resource;
//import javax.servlet.http.HttpServletResponse;
//import java.io.ByteArrayOutputStream;
//import java.io.File;
//import java.io.InputStream;
//import java.io.OutputStream;
//import java.math.BigDecimal;
//import java.math.RoundingMode;
//import java.net.URLEncoder;
//import java.nio.file.Files;
//import java.nio.file.Paths;
//import java.util.*;
//
///**
// * @ClassName : ChartServiceImpl  //类名
// * @Description : word 导出实现类  //描述
// * <AUTHOR> <EMAIL> //作者
// * @Date: 2023/9/7  16:54
// */
//@Service
//@Validated
//@Slf4j
//public class ChartServiceImpl implements ChartService {
//
//    @Resource
//    HostInfoService hostInfoService;
//    @Resource
//    HardwareInfoService hardwareInfoService;
//    @Resource
//    StorageInfoService storageInfoService;
//    @Resource
//    InfluxDBTemplate influxDBTemplate;
//
//    @Override
//    public void exportReport(HttpServletResponse response, WordExportReqVO exportReqVO) {
//        if (exportReqVO.getStartTime() == null || exportReqVO.getEndTime() == null) {
//            DateTime dateTime = DateUtil.offsetMonth(new Date(), -1);
//            exportReqVO.setStartTime(dateTime.getTime());
//            exportReqVO.setEndTime(DateUtil.current());
//        }
//
//        Map<String, Object> hostNum = hostInfoService.getHostStatusCount(null, exportReqVO.getPlatformId());
//        Map<String, Object> hardwareNum = hardwareInfoService.getHardwareStatusCount(null, exportReqVO.getPlatformId());
//        Long ct = storageInfoService.getCountByPlatformId(exportReqVO.getPlatformId());
//
//        // 云主机基础数据
//        List<HostInfoDO> list = hostInfoService.getListByPlatformId(StringUtil.toString(exportReqVO.getPlatformId()));
//
//        Map<String, Object> paramMap = new HashMap<>();
//        paramMap.put("hostName", exportReqVO.getPlatformName());
//        paramMap.put("cloud", list.size());
//        paramMap.put("sTime", DateUtil.format(new Date(exportReqVO.getStartTime()), "yyyy年MM月dd日"));
//        paramMap.put("eTime", DateUtil.format(new Date(exportReqVO.getEndTime()), "yyyy年MM月dd日"));
//        paramMap.put("host", StringUtil.toInt(hardwareNum.get("hardwareNum")));
//        paramMap.put("data", ct);
//        // 云服务器列表
//        cloudList(list, paramMap);
//        // 云服务器性能列表
//        performanceList(list, paramMap);
//
//
//        List<WordInfluxDO> cpulist = new ArrayList<>();
//        String cpu = "SELECT meanValue,uuid FROM ( SELECT mean(value) AS meanValue FROM zj_cloud_host WHERE metricName = 'CPUAverageUsedUtilization' " +
//                "and time >= '" + DateUtil.format(new Date(exportReqVO.getStartTime()), "yyyy-MM-dd HH:mm:ss") + "' AND " +
//                "time <= '" + DateUtil.format(new Date(exportReqVO.getEndTime()), "yyyy-MM-dd HH:mm:ss") + "' and " +
//                "platformId= '" + exportReqVO.getPlatformId() + "' GROUP BY  uuid,time(4d) TZ('Asia/Shanghai'))";
//
//        // cpu
//        for (Object listx : influxDBTemplate.fetchRecords(cpu)) {
//            cpulist.add(new ObjectMapper().convertValue(listx, WordInfluxDO.class));
//        }
//
//        List<WordInfluxDO> memory = new ArrayList<>();
//        String memoryinfo = "SELECT meanValue,uuid FROM ( SELECT mean(value) AS meanValue FROM zj_cloud_host WHERE metricName = 'MemoryUsedInPercent' " +
//                "and time >= '" + DateUtil.format(new Date(exportReqVO.getStartTime()), "yyyy-MM-dd HH:mm:ss") + "' AND " +
//                "time <= '" + DateUtil.format(new Date(exportReqVO.getEndTime()), "yyyy-MM-dd HH:mm:ss") + "' and " +
//                "platformId= '" + exportReqVO.getPlatformId() + "' GROUP BY  uuid,time(4d) TZ('Asia/Shanghai'))";
//
//        // 内存
//        for (Object listx : influxDBTemplate.fetchRecords(memoryinfo)) {
//            memory.add(new ObjectMapper().convertValue(listx, WordInfluxDO.class));
//        }
//
//
//        List<WordInfluxDO> disk = new ArrayList<>();
//        String diskinfo = "SELECT meanValue,uuid FROM ( SELECT mean(value) AS meanValue FROM zj_cloud_host WHERE metricName = 'DiskAllUsedCapacityInPercent' " +
//                "and time >= '" + DateUtil.format(new Date(exportReqVO.getStartTime()), "yyyy-MM-dd HH:mm:ss") + "' AND " +
//                "time <= '" + DateUtil.format(new Date(exportReqVO.getEndTime()), "yyyy-MM-dd HH:mm:ss") + "' and " +
//                "platformId= '" + exportReqVO.getPlatformId() + "' GROUP BY  uuid,time(4d) TZ('Asia/Shanghai'))";
//
//        // 磁盘
//        for (Object listx : influxDBTemplate.fetchRecords(diskinfo)) {
//            disk.add(new ObjectMapper().convertValue(listx, WordInfluxDO.class));
//        }
//        String[] dateList = new String[cpulist.size()];
//        // cpu
//        for (int i = 0; i < cpulist.size(); i++) {
//            String s = DateUtil.format(cpulist.get(i).getTime(), "MM-dd HH:mm");
//            dateList[i] = s;
//        }
//
//        String[] date = removeDuplicates(dateList);
//
//        List<Map> list1 = new ArrayList<>();
//        for (HostInfoDO hostInfoDO : list) {
//            Map map = new HashMap();
//
//            // 初始化CPU、磁盘和内存的数据数组
//            Double[] cpusize = new Double[date.length];
//            Double[] disksize = new Double[date.length];
//            Double[] mesize = new Double[date.length];
//            map.put("uuid", hostInfoDO.getUuid());
//            map.put("name", hostInfoDO.getName());
//            // CPU
//            for (int i = 0; i < cpulist.size(); i++) {
//                if (hostInfoDO.getUuid().equals(cpulist.get(i).getUuid())) {
//                    String s = DateUtil.format(cpulist.get(i).getTime(), "MM-dd HH:mm");
//                    int index = Arrays.asList(date).indexOf(s);
//                    if (index != -1) {
//                        cpusize[index] = cpulist.get(i).getMeanValue();
//                    }
//                }
//            }
//
//            // 磁盘
//            for (int k = 0; k < disk.size(); k++) {
//                if (hostInfoDO.getUuid().equals(disk.get(k).getUuid())) {
//                    String s = DateUtil.format(disk.get(k).getTime(), "MM-dd HH:mm");
//                    int index = Arrays.asList(date).indexOf(s);
//                    if (index != -1) {
//                        disksize[index] = disk.get(k).getMeanValue();
//                    }
//                }
//            }
//
//            // 内存
//            for (int f = 0; f < memory.size(); f++) {
//                if (hostInfoDO.getUuid().equals(memory.get(f).getUuid())) {
//                    String s = DateUtil.format(memory.get(f).getTime(), "MM-dd HH:mm");
//                    int index = Arrays.asList(date).indexOf(s);
//                    if (index != -1) {
//                        mesize[index] = memory.get(f).getMeanValue();
//                    }
//                }
//            }
//            map.put("date", date);
//            map.put("cpu", cpusize);
//            map.put("disk", disksize);
//            map.put("memory", mesize);
//            list1.add(map);
//        }
//
//        List<Map<String, ChartMultiSeriesRenderData>> charts = new ArrayList<>();
//
//        for (Map map : list1) {
//            Map<String, ChartMultiSeriesRenderData> ch = new HashMap<>();
//            ChartMultiSeriesRenderData chartMultiSeriesRenderData = new ChartMultiSeriesRenderData();
//            chartMultiSeriesRenderData.setChartTitle(StringUtil.toString(map.get("name")));
//            chartMultiSeriesRenderData.setCategories((String[]) map.get("date"));
//            List<SeriesRenderData> seriesDatas = new ArrayList<>();
//            seriesDatas.add(new SeriesRenderData("CPU", Convert.toDoubleArray(map.get("cpu"))));
//            seriesDatas.add(new SeriesRenderData("内存", Convert.toDoubleArray(map.get("memory"))));
//            seriesDatas.add(new SeriesRenderData("磁盘", Convert.toDoubleArray(map.get("disk"))));
//            chartMultiSeriesRenderData.setSeriesDatas(seriesDatas);
//            ch.put("barChart", chartMultiSeriesRenderData);
//            charts.add(ch);
//        }
//        paramMap.put("charts", charts);
//
//        // 生成文件
//        createAndDownloadWord(paramMap, response, exportReqVO);
//
//
//    }
//
//    private static void performanceList(List<HostInfoDO> list, Map<String, Object> paramMap) {
//        //验证类型集合
//        List<String> xnlb = Arrays.asList("CPU",
//                "内存",
//                "磁盘");
//        //表头是两行
//        //第一行数据是固定内容 "序号", "章节号", "项目", "内容", "验证方法", "备注"
//        List<String> xnlbTemp1 = Arrays.asList("序号", "主机名称", "私网IP", "平均使用率");
//        List<String> headerCellList3 = new ArrayList<>(xnlbTemp1);
//
//        int xnlbsize = xnlb.size();
//        for (int k = 0; k < xnlbsize - 1; k++) {
//            headerCellList3.add(" "); //赋空字符串
//        }
//        headerCellList3.add("备注");
//
//
//        String[] headerCell3 = headerCellList3.toArray(new String[headerCellList3.size()]);
//
//        RowRenderData header3 = Rows.of(headerCell3).center().bgColor("DFDED9").create();
//
//
//        //headerCell2 :验证方法对应列，是动态内容
//        List<String> headerCellListTemp3 = Arrays.asList("序号", "主机名称", "资源池名称");
//        List<String> headerCellList4 = new ArrayList<>(headerCellListTemp3);
//        // 验证方法对应列
//        headerCellList4.addAll(xnlb);
//        //备注对应的列 赋空值
//        headerCellList4.add(" ");
//
//
//        String[] headerCell4 = headerCellList4.toArray(new String[headerCellList4.size()]);
//        RowRenderData header4 = Rows.of(headerCell4).center().bgColor("DFDED9").create();
//        //表格 列的数量
//        int cellLength4 = headerCell3.length;
//
//
//        //无数据只有表头数据 tableList 无值
//        if (CollectionUtil.isEmpty(list)) {
//            RowRenderData[] RowRenderDataHeader = new RowRenderData[2];
//            RowRenderDataHeader[0] = header3;
//            RowRenderDataHeader[1] = header4;
//
//            TableRenderData check_table2 = setTableRenderDataAndColWidth2(RowRenderDataHeader, cellLength4);
//
//
//            // table 只有表头数据
//            paramMap.put("check_table1", check_table2);
//        } else {
//            int length2 = 2 + list.size();
//            //行数据 数组
//            RowRenderData[] RowRenderData2 = new RowRenderData[length2];
//
//            RowRenderData2[0] = header3;
//            RowRenderData2[1] = header4;
//
//
//            for (int i = 0; i < list.size(); i++) {
//
//                String index = Integer.toString(i + 1);
//                List<String> tempList = Arrays.asList("A", "B", "C");
//
//                String[] cellString = new String[cellLength4];
//                cellString[0] = index; //序号
//                cellString[1] = list.get(i).getName();//标题序号
//                cellString[2] = list.get(i).getIp(); // 项目
//                cellString[3] = list.get(i).getCpuUsed().toString(); // 项目
//                cellString[4] = list.get(i).getMemoryUsed().toString(); // 项目
//                cellString[5] = list.get(i).getDiskUsed().toString(); // 项目
//
//                //备注
//                cellString[cellLength4 - 1] = "备注";
//                RowRenderData rowRenderData = Rows.of(cellString).
//                        center().create();
//                //行数据赋值
//                RowRenderData2[2 + i] = rowRenderData;
//
//            }
//            TableRenderData check_table2 = setTableRenderDataAndColWidth2(RowRenderData2, cellLength4);
//            // table
//            paramMap.put("check_table1", check_table2);
//        }
//    }
//
//    private static void cloudList(List<HostInfoDO> list, Map<String, Object> paramMap) {
//        //验证类型集合
//        List<String> typeList = Arrays.asList("公网IP",
//                "私网IP",
//                "CPU(核)",
//                "内存(G)",
//                "数据盘(G)",
//                "操作系统");
//
//        //表头是两行
//        //第一行数据是固定内容 "序号", "主机名称", "资源池名称", "IP地址", " ", "ESC配置"
//        List<String> headerCellListTemp1 = Arrays.asList("序号", "主机名称", "资源池名称", "IP地址", " ", "ESC配置");
//        List<String> headerCellList1 = new ArrayList<>(headerCellListTemp1);
//        //验证方法，可能是多列数据  第一列是验证方法，后面是赋空字符串，用于合并单元格;
//        //数据类型：验证方法 "" "" ""
//        //验证类型长度
//        int typeListSize = typeList.size();
//        for (int k = 0; k < typeListSize - 3; k++) {
//            headerCellList1.add(" "); //赋空字符串
//        }
//        headerCellList1.add("备注");
//
//        String[] headerCell1 = headerCellList1.toArray(new String[headerCellList1.size()]);
//
//        RowRenderData header1 = Rows.of(headerCell1).center().bgColor("DFDED9").create();
//
//
//        //headerCell2 :验证方法对应列，是动态内容
//        List<String> headerCellListTemp2 = Arrays.asList("序号", "主机名称", "资源池名称");
//        List<String> headerCellList2 = new ArrayList<>(headerCellListTemp2);
//        // 验证方法对应列
//        headerCellList2.addAll(typeList);
//        //备注对应的列 赋空值
//        headerCellList2.add(" ");
//
//        String[] headerCell2 = headerCellList2.toArray(new String[headerCellList2.size()]);
//        RowRenderData header2 = Rows.of(headerCell2).center().bgColor("DFDED9").create();
//        //表格 列的数量
//        int cellLength = headerCell1.length;
//        //无数据只有表头数据 tableList 无值
//        if (CollectionUtil.isEmpty(list)) {
//            RowRenderData[] RowRenderDataHeader = new RowRenderData[2];
//            RowRenderDataHeader[0] = header1;
//            RowRenderDataHeader[1] = header2;
//
//            TableRenderData check_table = setTableRenderDataAndColWidth(RowRenderDataHeader, cellLength);
//
//
//            // table 只有表头数据
//            paramMap.put("check_table", check_table);
//        } else {
//            int length = 2 + list.size();
//            //行数据 数组
//            RowRenderData[] RowRenderData = new RowRenderData[length];
//
//            RowRenderData[0] = header1;
//            RowRenderData[1] = header2;
//
//            for (int i = 0; i < list.size(); i++) {
//                Double me = bytesToGB(list.get(i).getMemorySize());
//                BigDecimal disk = bytesToGBb(list.get(i).getTotalDiskCapacity());
//
//                String index = Integer.toString(i + 1);
//                List<String> tempList = Arrays.asList("A", "B", "C",
//                        "D", "E", "F");
//
//
//                String[] cellString = new String[cellLength];
//                cellString[0] = index; //序号
//                cellString[1] = list.get(i).getName();//标题序号
//                cellString[2] = list.get(i).getPlatformName(); // 资源名称
//                cellString[3] = list.get(i).getIp(); // ip
//                cellString[4] = list.get(i).getVipIp(); // 弹性ip
//                cellString[5] = list.get(i).getCpuNum().toString(); // cpu(核)
//                cellString[6] = me.toString();// 内存（G）
//                cellString[7] = disk.toString();// 数据盘
//                cellString[8] = list.get(i).getGuestOsType();
//
//
////                //验证方法对应数据赋值
////                if (headerCell2.length > 3) {
////                    for (int j = 0; j < tempList.size(); j++) {
////                        cellString[i + 3] = tempList.get(j);
////                    }
////                }
//                //备注
//                cellString[cellLength - 1] = "备注";
//                RowRenderData rowRenderData = Rows.of(cellString).
//                        center().create();
//                //行数据赋值
//                RowRenderData[2 + i] = rowRenderData;
//
//            }
//            TableRenderData check_table = setTableRenderDataAndColWidth(RowRenderData, cellLength);
//
//            // table
//            paramMap.put("check_table", check_table);
//
//        }
//    }
//
//
//    public static double bytesToGB(long bytes) {
//        double gb = bytes / (1024.0 * 1024.0 * 1024.0);
//        return gb;
//    }
//
//    public static BigDecimal bytesToGBb(BigDecimal bytes) {
//        if (bytes == null || bytes.compareTo(BigDecimal.ZERO) < 0) {
//            throw new IllegalArgumentException("字节数必须大于等于0");
//        }
//
//        BigDecimal gb = bytes.divide(new BigDecimal(1073741824), 2, RoundingMode.HALF_UP);
//        return gb;
//    }
//
//
//    /**
//     * 表格赋值，
//     * 设置列宽和合并单元格
//     *
//     * @param rowRenderDataArray
//     * @param cellLength
//     * @return
//     */
//    private static TableRenderData setTableRenderDataAndColWidth(RowRenderData[] rowRenderDataArray, Integer cellLength) {
//        //table赋值set方法需要list
//        List<RowRenderData> RowRenderDataList = Arrays.asList(rowRenderDataArray);
//        //设置列宽：
//        double[] colWidthsCm = new double[cellLength];
//        for (int i = 0; i < cellLength; i++) {
//            // "主机名称", "资源池" 设置为 2
//            if (i == 1 || i == 2) {
//                colWidthsCm[i] = 2D;
//            } else {
//                colWidthsCm[i] = 1D;
//            }
//        }
//        //18.450000762939453D A4纸张
//        TableRenderData check_table = Tables.ofPercentWidth("100%").center().create();
//        check_table.setRows(RowRenderDataList);
//        //合并单元格
//        MergeCellRule.MergeCellRuleBuilder mergeCellRuleBuilder = mergeCell(cellLength);
//        check_table.setMergeRule(mergeCellRuleBuilder.build());
//
//        return check_table;
//    }
//
//    /**
//     * 表格 合并单元格
//     *
//     * @param cellLength
//     * @return
//     */
//    private static MergeCellRule.MergeCellRuleBuilder mergeCell(Integer cellLength) {
//        /**
//         * 设置表格合并规则 从0开始
//         * 1.起始行 MergeCellRule.Grid.of(i, j) i: 行 j: 列
//         * 2.结束行 MergeCellRule.Grid.of(i, j) i: 行 j: 列
//         */
//        //合并单元格
//        //合并到【备注】前一列
//        MergeCellRule.MergeCellRuleBuilder mergeCellRuleBuilder = MergeCellRule.builder();
//        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, 0), MergeCellRule.Grid.of(1, 0));//序号合并
//        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, 1), MergeCellRule.Grid.of(1, 1));//主机名称合并
//        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, 2), MergeCellRule.Grid.of(1, 2));//资源池名称合并
//        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, 3), MergeCellRule.Grid.of(0, cellLength - 6));//内容合并 第一行
//        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, 5), MergeCellRule.Grid.of(0, cellLength - 2));//验证类型合并 第一行
//        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, cellLength - 1), MergeCellRule.Grid.of(1, cellLength - 1));//备注合并
//        return mergeCellRuleBuilder;
//    }
//
//
//    private static TableRenderData setTableRenderDataAndColWidth2(RowRenderData[] rowRenderDataArray, Integer cellLength) {
//        //table赋值set方法需要list
//        List<RowRenderData> RowRenderDataList = Arrays.asList(rowRenderDataArray);
//        //设置列宽：
//        double[] colWidthsCm = new double[cellLength];
//        for (int i = 0; i < cellLength; i++) {
//            // "主机名称", "资源池" 设置为 2
//            if (i == 1 || i == 2) {
//                colWidthsCm[i] = 2D;
//            } else {
//                colWidthsCm[i] = 1D;
//            }
//        }
//        //18.450000762939453D A4纸张
//        TableRenderData check_table = Tables.ofPercentWidth("100%").center().create();
//        check_table.setRows(RowRenderDataList);
//        //合并单元格
//        MergeCellRule.MergeCellRuleBuilder mergeCellRuleBuilder = mergeCell2(cellLength);
//        check_table.setMergeRule(mergeCellRuleBuilder.build());
//
//        return check_table;
//    }
//
//    private static MergeCellRule.MergeCellRuleBuilder mergeCell2(int cellLength) {
//        /**
//         * 设置表格合并规则 从0开始
//         * 1.起始行 MergeCellRule.Grid.of(i, j) i: 行 j: 列
//         * 2.结束行 MergeCellRule.Grid.of(i, j) i: 行 j: 列
//         */
//        //合并单元格
//        //合并到【备注】前一列
//        MergeCellRule.MergeCellRuleBuilder mergeCellRuleBuilder = MergeCellRule.builder();
//        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, 0), MergeCellRule.Grid.of(1, 0));//序号合并
//        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, 1), MergeCellRule.Grid.of(1, 1));//主机名称合并
//        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, 2), MergeCellRule.Grid.of(1, 2));//资源池名称合并
//        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, 3), MergeCellRule.Grid.of(0, cellLength - 2));//内容合并 第一行
//        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, cellLength - 1), MergeCellRule.Grid.of(1, cellLength - 1));//备注合并
//        return mergeCellRuleBuilder;
//    }
//
//
//    private static void createAndDownloadWord(Map<String, Object> paramMap, HttpServletResponse response, WordExportReqVO exportReqVO) {
//        log.info("生成电子协查函参数paramMap = {}", paramMap);
//
//        try {
//            // 创建一个临时的输出流
//            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//
//            // 获取电子协查函模板
//            String path = "file-template" + File.separator + "word.docx";
//            ClassPathResource classPathResource = new ClassPathResource(path);
//
//            try (InputStream inputStream = classPathResource.getInputStream()) {
//                if (Objects.isNull(inputStream)) {
//                    log.error("获取电子协查函模板失败");
//                    return;
//                }
//
//                // 通过协查函模板，开始生成电子协查函
//                try (XWPFTemplate template = XWPFTemplate.compile(inputStream).render(paramMap)) {
//                    // 将生成的Word文档写入临时输出流
//                    template.write(outputStream);
//                }
//
//            } catch (Exception e) {
//                log.error("创建协查函异常，异常详情：\n{}", e);
//            }
//            String d = "云主机运行报告" + DateUtil.format(new Date(exportReqVO.getStartTime()), "yyyy-MM-dd") + "~" + DateUtil.format(new Date(exportReqVO.getEndTime()), "yyyy-MM-dd");
//
//            // 设置响应头
//            response.setContentType("application/octet-stream");
//            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(exportReqVO.getPlatformName() + d, "UTF-8") + ".docx");
//
//            // 将生成的Word文档写入响应输出流
//            try (OutputStream out = response.getOutputStream()) {
//                out.write(outputStream.toByteArray());
//                out.flush();
//                out.close();
//            }
//        } catch (Exception e) {
//            log.error("导出Word文档并下载时发生异常：\n{}", e);
//        }
//    }
//
//    public static String[] removeDuplicates(String[] inputArray) {
//        if (inputArray == null) {
//            return null; // 处理输入为null的情况
//        }
//
//        Set<String> set = new LinkedHashSet<>(Arrays.asList(inputArray)); // 使用LinkedHashSet以保持原始顺序
//        String[] result = set.toArray(new String[0]);
//        return result;
//    }
//}
