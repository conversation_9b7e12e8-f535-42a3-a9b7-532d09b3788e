package cn.iocoder.zj.module.monitor.convert.alarmhostrelation;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmhostrelation.AlarmHostRelationDO;

/**
 * 告警配置与云主机关联关系 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface AlarmHostRelationConvert {

    AlarmHostRelationConvert INSTANCE = Mappers.getMapper(AlarmHostRelationConvert.class);

    AlarmHostRelationDO convert(AlarmHostRelationCreateReqVO bean);

    AlarmHostRelationDO convert(AlarmHostRelationUpdateReqVO bean);

    AlarmHostRelationRespVO convert(AlarmHostRelationDO bean);

    List<AlarmHostRelationRespVO> convertList(List<AlarmHostRelationDO> list);

    PageResult<AlarmHostRelationRespVO> convertPage(PageResult<AlarmHostRelationDO> page);

    List<AlarmHostRelationExcelVO> convertList02(List<AlarmHostRelationDO> list);

}
