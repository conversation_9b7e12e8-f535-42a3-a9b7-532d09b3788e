package cn.iocoder.zj.module.monitor.dal.mysql.taggables;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.dal.dataobject.taggables.TaggablesDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.monitor.controller.admin.taggables.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 标签绑定关系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TaggablesMapper extends BaseMapperX<TaggablesDO> {

    default PageResult<TaggablesDO> selectPage(TaggablesPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TaggablesDO>()
                .eqIfPresent(TaggablesDO::getTagId, reqVO.getTagId())
                .eqIfPresent(TaggablesDO::getTaggableId, reqVO.getTaggableId())
                .inIfPresent(TaggablesDO::getTaggableId, reqVO.getTaggableIds())
                .eqIfPresent(TaggablesDO::getTaggableType, reqVO.getTaggableType())
                .betweenIfPresent(TaggablesDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TaggablesDO::getId));
    }

    default List<TaggablesDO> selectList(TaggablesExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TaggablesDO>()
                .eqIfPresent(TaggablesDO::getTagId, reqVO.getTagId())
                .eqIfPresent(TaggablesDO::getTaggableId, reqVO.getTaggableId())
                .eqIfPresent(TaggablesDO::getTaggableType, reqVO.getTaggableType())
                .betweenIfPresent(TaggablesDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TaggablesDO::getId));
    }

    @TenantIgnore
    @Select("SELECT ${field} FROM ${tableName} WHERE id=${id}")
    Map<String, Object> selectRelationById(@Param("tableName") String tableName,@Param("id") Long id,@Param("field") String field);

    @TenantIgnore
//    @Select("SELECT ${field} FROM ${tableName} WHERE platform_id in (${platformIds}) and id not in (${ids}) limit ${limit} and deleted = 0")
    @Select("<script>" +
            "SELECT ${field} FROM ${tableName} WHERE " +
            "id IN (${ids}) " +
            "<if test='tableName == \"hzb_monitor\"'> AND category = #{taggableType} </if>" +
            "<if test='tableName != \"hzb_monitor\"'> AND deleted = 0 </if>" +
            "</script>")
    List<Map<String, Object>> selectBindAssetListByIdAndType(@Param("tableName") String tableName, @Param("ids") String ids, @Param("field") String field,@Param("taggableType") String taggableType);


    @TenantIgnore
//    @Select("SELECT ${field} FROM ${tableName} WHERE platform_id in (${platformIds}) and id not in (${ids}) limit ${limit} and deleted = 0")
    @Select("<script>" +
            "SELECT ${field} FROM ${tableName} WHERE platform_id IN (${platformIds}) " +
            "AND id NOT IN (${ids}) " +
            "<if test='tableName == \"hzb_monitor\"'> AND category = #{taggableType} </if>" +
            "<if test='tableName != \"hzb_monitor\"'> AND deleted = 0 </if>" +
            "<if test='taggableName != null and taggableName != \"\"'> AND `name` like '%${taggableName}%' </if>" +
            "LIMIT ${limit}" +
            "</script>")
    List<Map<String, Object>> selectAssetPageByNotId(@Param("tableName") String tableName, @Param("ids") String ids, @Param("platformIds") String platformIds, @Param("field") String field,@Param("taggableType") String taggableType,@Param("limit") String limit,@Param("taggableName") String taggableName);

    @TenantIgnore
//    @Select("SELECT count(1) as total FROM ${tableName} WHERE platform_id in (${platformIds}) and id not in (${ids})")
    @Select("<script>" +
            "SELECT count(1) as total FROM ${tableName} WHERE platform_id IN (${platformIds}) " +
            "AND id NOT IN (${ids}) " +
            "<if test='tableName == \"hzb_monitor\"'> AND category = #{taggableType} </if>" +
            "<if test='tableName != \"hzb_monitor\"'> AND deleted = 0 </if>" +
            "<if test='taggableName != null and taggableName != \"\"'> AND `name` like '%${taggableName}%' </if>" +
            "</script>")
    Map<String, Object> selectAssetListByNotIdCount(@Param("tableName") String tableName,@Param("ids") String ids,@Param("platformIds") String platformIds,@Param("taggableType") String taggableType,@Param("taggableName") String taggableName);



    @TenantIgnore
//    @Select("SELECT ${field} FROM ${tableName} WHERE platform_id in (${platformIds}) and id not in (${ids}) limit ${limit} and deleted = 0")
    @Select("<script>" +
            "SELECT ${field} FROM ${tableName} WHERE platform_id IN (${platformIds}) " +
            "AND `name` like '%${name}%' " +
            "<if test='tableName == \"hzb_monitor\"'> AND category = #{taggableType} </if>" +
            "<if test='tableName != \"hzb_monitor\"'> AND deleted = 0 </if>" +
            "</script>")
    List<Map<String, Object>> selectAssetByName(@Param("tableName") String tableName, @Param("name") String name, @Param("platformIds") String platformIds, @Param("field") String field,@Param("taggableType") String taggableType);

    @TenantIgnore
    void updateByDetel(@Param("collect")Set<Long> collect, @Param("type")String type);

    @TenantIgnore
    List<TaggablesDO> selectListByType(@Param("comIds")Set<Long> comIds,@Param("taggableType")String taggableType);

    @TenantIgnore
    void insertBatch(@Param("list")List<TaggablesDO> list);

    @TenantIgnore
    void updateByIdDetel(@Param("longs")Set<Long> longs,@Param("taggableIds")Set<Long> taggableIds,@Param("type")String type);
}
