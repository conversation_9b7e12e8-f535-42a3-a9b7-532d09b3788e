package cn.iocoder.zj.module.monitor.service.topology;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.monitor.controller.admin.topology.vo.TopologyCreateReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.topology.vo.TopologyExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.topology.vo.TopologyPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.topology.vo.TopologyUpdateReqVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.topology.TopologyDO;
import org.apache.hertzbeat.common.entity.alerter.AlertRespVo;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 监控资源拓扑图 Service 接口
 *
 * <AUTHOR>
 */
public interface TopologyService {

    /**
     * 创建监控资源拓扑图
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTopology(@Valid TopologyCreateReqVO createReqVO);

    /**
     * 更新监控资源拓扑图
     *
     * @param updateReqVO 更新信息
     */
    void updateTopology(@Valid TopologyUpdateReqVO updateReqVO);

    /**
     * 删除监控资源拓扑图
     *
     * @param id 编号
     */
    void deleteTopology(Long id);

    /**
     * 获得监控资源拓扑图
     *
     * @param id 编号
     * @return 监控资源拓扑图
     */
    TopologyDO getTopology(Long id);

    /**
     * 获得监控资源拓扑图列表
     *
     * @param ids 编号
     * @return 监控资源拓扑图列表
     */
    List<TopologyDO> getTopologyList(Long platformId);

    /**
     * 获得监控资源拓扑图分页
     *
     * @param pageReqVO 分页查询
     * @return 监控资源拓扑图分页
     */
    PageResult<TopologyDO> getTopologyPage(TopologyPageReqVO pageReqVO);

    /**
     * 获得监控资源拓扑图列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 监控资源拓扑图列表
     */
    List<TopologyDO> getTopologyList(TopologyExportReqVO exportReqVO);


    /**
     * @description: 根据平台id 获取拓扑图
     * <AUTHOR>
     * @date 2023/7/31 13:06
     * @version 1.0
     */
    TopologyDO getTopologyByPlatformId(Long id);

    /**
     * 同步删除监控资源拓扑图中的基础设施和云主机资源json和拓扑图json
     *
     * @param id 编号
     */
    void deleteAssetAndHostJson(String id, String type);

    List<AlertRespVo> getAlarmInfo(String monitorId);

    Map getmonitorStatus(String uuid, String type);

    Map getmonitorCount(String id);

    Map  getmonitorListInfo(String id);

    Map getmonitorInterfacesJson(String id, String monitorId);

    List<Map> getHostInterfacesInfo(String monitorId, String monitorInterfaces);


    List<TopologyDO> getList();


    void updatebatchTopology(List<TopologyDO> topologyDO);
}
