package cn.iocoder.zj.module.monitor.controller.admin.imageinfo.vo;

import cn.iocoder.zj.module.monitor.controller.admin.tags.vo.TagsRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Schema(description = "管理后台 - 镜像信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ImageInfoRespVO extends ImageInfoBaseVO {

    @Schema(description = "主键ID", required = true)
    private Long id;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "标签信息")
    private List<TagsRespVO> tags;

}
