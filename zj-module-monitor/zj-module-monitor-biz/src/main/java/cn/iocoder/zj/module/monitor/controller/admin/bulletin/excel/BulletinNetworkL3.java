package cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel;

import cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.convert.BulletinNetL3TypeConvert;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@ExcelIgnoreUnannotated
public class BulletinNetworkL3 {

    private Long id;

    @ExcelProperty("三层网络名称")
    private String name;

    @ExcelProperty("平台名称")
    private String platformName;

    @ExcelProperty(value = "网络类型", converter = BulletinNetL3TypeConvert.class)
    private String type;

    @ExcelProperty("UUID")
    private String uuid;

    @ExcelProperty("IPv4 CIDR")
    private String networkCidr;

    @ExcelProperty("IPV4 DHCP")
    private String nextHopIp;

    @ExcelProperty("DNS")
    private String dns;

    @ExcelProperty("起始ip")
    private String startIp;

    @ExcelProperty("结束ip")
    private String endIp;

    @ExcelProperty("网关")
    private String gateway;

    @ExcelProperty("网段名称")
    private String networkSegment;

    @ExcelProperty(value = "二级网络名称")
    private String l2NetworkName;

    @ExcelProperty("网络服务")
    private String networkServices;

}
