package cn.iocoder.zj.module.monitor.service.home.devopsscreen;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.enums.ResourceCategoryEnum;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.bpm.api.task.BpmTaskApi;
import cn.iocoder.zj.module.monitor.dal.mysql.home.devopsscreen.DevOpsScreenMapper;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class DevOpsScreenServiceImpl implements DevOpsScreenService {

    @Resource
    DevOpsScreenMapper devOpsScreenMapper;
    @Resource
    AdminUserApi adminUserApi;
    @Resource
    BpmTaskApi bpmTaskApi;
    @Override
    @TenantIgnore
    public List<Map> getResourceDistribution() {
        List<Map> assetMapList = devOpsScreenMapper.getResourceDistribution();
        // 定义一个映射，将应用类型映射到其相应的列表
        Map<String, List<String>> appTypeMap = new HashMap<>();
        appTypeMap.put(ResourceCategoryEnum.APP_SERVICE.getCode(), Arrays.asList("website", "port", "ssl_cert", "ping", "websocket", "api", "dns", "fullsite", "api_code", "udp_port"));
        appTypeMap.put(ResourceCategoryEnum.APP_DB.getCode(), Arrays.asList("mysql", "oracle", "sqlserver", "mariadb", "dm", "redis", "redis_cluster", "redis_sentinel", "memcached", "tidb", "nebulaGraph", "opengauss", "mongodb"));
        appTypeMap.put(ResourceCategoryEnum.APP_MID.getCode(), Arrays.asList("nacos", "zookeeper", "kafka", "rabbitmq", "nginx", "pop3", "ftp", "smtp", "ntp", "spring_gateway", "emqx", "activemq", "rocketmq", "shenyu", "rabbitmq", "almalinux","tomcat"));
        appTypeMap.put(ResourceCategoryEnum.APP_OS.getCode(), Arrays.asList("windows", "linux", "ubuntu", "coreos", "opensuse", "rockylinux", "euleros", "redhat", "centos", "freebsd", "debian","ipmi","linux_snmp"));
        appTypeMap.put(ResourceCategoryEnum.APP_CN.getCode(), Arrays.asList("docker", "kubernetes"));
//        appTypeMap.put(ResourceCategoryEnum.APP_PROMETHEUS.getCode(), Collections.singletonList("prometheus"));
        appTypeMap.put(ResourceCategoryEnum.APP_FIREWALL.getCode(), Arrays.asList("qax_firewall", "sswk_firewall", "sswk_defense", "sxf_firewall", "ad_firewall","ac_firewall","ah_firewall", "ah_waf", "lm_defense", "lm_waf", "trx_defense", "trx_firewall"));
        appTypeMap.put(ResourceCategoryEnum.APP_NETWORK.getCode(), Arrays.asList("h3c_switch", "cisco_switch", "huawei_switch", "tplink_switch", "hpe_switch"));
        appTypeMap.put(ResourceCategoryEnum.APP_STORED.getCode(), Arrays.asList("storage_huawei", "storage_inspur", "storage_synology"));
        appTypeMap.put(ResourceCategoryEnum.APP_INFRA.getCode(), Arrays.asList("infra_ipmi"));
        appTypeMap.put(ResourceCategoryEnum.APP_HOST.getCode(), Arrays.asList("host"));
        appTypeMap.put(ResourceCategoryEnum.APP_HARDWARE.getCode(), Arrays.asList("hardware"));
        appTypeMap.put(ResourceCategoryEnum.APP_STORAGE.getCode(), Arrays.asList("storage"));
        List<Map> datalist = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : appTypeMap.entrySet()) {
            Map<String, Object> maps = new HashMap();
            String label = getResourceLabel(entry.getKey());
            String appType = entry.getKey();
            List<String> apps = entry.getValue();
            // 遍历当前类型的每个应用
            Long enabled = 0L;
            Long disabled = 0L;
            Long other = 0L;
            Long count = 0L;
            for (String app : apps) {
                for (Map map : assetMapList) {
                    if (app.equals(map.get("app"))) {
                        enabled = enabled + Convert.toLong(map.get("enabled"));
                        disabled = disabled +Convert.toLong(map.get("disabled"));
                        other = other +Convert.toLong(map.get("other"));
                        count = count +Convert.toLong(map.get("count"));
                    }
                }
            }
            maps.put("app",label);
            maps.put("count",count);
            maps.put("enabled",enabled);
            maps.put("disabled",disabled);
            maps.put("other",other);
            datalist.add(maps);
        }
        return  datalist.stream()
                .sorted(Comparator.comparingLong(e -> -(Long) e.get("count")))
                .collect(Collectors.toList());
    }

    @Override
    public List<Map> getLoginRanking() {
        DecimalFormat df = new DecimalFormat("#.##");
        List<Map<String,Object>> rankList = devOpsScreenMapper.getLoginRanking();
        if(ObjectUtil.isNotEmpty(rankList)) {
            List<Map> resultMapList = new ArrayList<>();
            Long maxCount = rankList.stream()
                    .map(map -> (Long) map.get("count"))
                    .max(Comparator.naturalOrder())
                    .orElse(0L);
            for (Map<String, Object> map :rankList) {
                Double persent = (double)(Long)map.get("count")/maxCount*100;
                Map result = new HashMap<>();
                result.put("ip",map.get("ip"));
                result.put("count",map.get("count"));
                result.put("rate",df.format(persent));
                resultMapList.add(result);
            }
            return resultMapList;
        }else {
            return new ArrayList<>();
        }

    }

    @Override
    @TenantIgnore
    public Map getRunningAssetAndUsers() {
        Map runningAsset = devOpsScreenMapper.getRunningAsset();
        Map userCount = devOpsScreenMapper.getUserCount();
        runningAsset.put("userCount",userCount.get("usersCount"));
        return runningAsset;
    }

    @Override
    @TenantIgnore
    public List<Map> abnormalUsage(Long platformId) {
        return devOpsScreenMapper.abnormalUsage(platformId);
    }

    @Override
    @TenantIgnore
    public List<Map> storageExceptions(Long platformId) {
        return devOpsScreenMapper.storageExceptions(platformId);
    }

    @Override
    public List<Map> getTodoTask(Long platformId) {

        List<Map> todoTask =  bpmTaskApi.getTodoTask(platformId).getData();
        return todoTask;
    }

    private String getResourceLabel(String app) {
        for (ResourceCategoryEnum resource : ResourceCategoryEnum.values()) {
            if (resource.getCode().equals(app)) {
                return resource.getName();
            }
            // 检查是否包含在当前大类的枚举列表中
            if (resource.getSubEnums() != null && resource.getSubEnums().stream().anyMatch(subEnum -> subEnum.getValue().equals(app))) {
                return resource.getName();
            }
        }
        return null; // 如果没有匹配的枚举值，返回null或者其他默认值
    }
    private void addToMapList(List<Map<String, Object>> list, String app, Long count) {
        Map<String, Object> map = new HashMap<>();
        map.put("app", app);
        map.put("count", count);
        list.add(map);
    }
}
