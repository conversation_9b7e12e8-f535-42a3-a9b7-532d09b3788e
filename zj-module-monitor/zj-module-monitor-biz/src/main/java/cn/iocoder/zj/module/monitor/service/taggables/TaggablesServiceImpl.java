package cn.iocoder.zj.module.monitor.service.taggables;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.module.monitor.controller.admin.taggables.vo.*;
import cn.iocoder.zj.module.monitor.convert.taggables.TaggablesConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.taggables.TaggablesDO;
import cn.iocoder.zj.module.monitor.dal.mysql.taggables.TaggablesMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.*;

/**
 * 标签绑定关系 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TaggablesServiceImpl implements TaggablesService {

    @Resource
    private TaggablesMapper taggablesMapper;

    @Override
    public Long createTaggables(TaggablesCreateReqVO createReqVO) {
        // 插入
        TaggablesDO taggables = TaggablesConvert.INSTANCE.convert(createReqVO);
        //判断是否重复
        Long exist = taggablesMapper.selectCount(new LambdaQueryWrapper<TaggablesDO>()
                .eq(TaggablesDO::getTagId, taggables.getTagId())
                .eq(TaggablesDO::getTaggableId, taggables.getTaggableId())
                .eq(TaggablesDO::getTaggableType, taggables.getTaggableType())
                );
        if (exist > 0) {
            throw exception(TAGGABLES_EXISTS);
        }
        taggablesMapper.insert(taggables);
        // 返回
        return taggables.getId();
    }

    @Override
    public void updateTaggables(TaggablesUpdateReqVO updateReqVO) {
        // 校验存在
        validateTaggablesExists(updateReqVO.getId());
        // 更新
        TaggablesDO updateObj = TaggablesConvert.INSTANCE.convert(updateReqVO);
        taggablesMapper.updateById(updateObj);
    }

    @Override
    public void deleteTaggables(Long id) {
        // 校验存在
        validateTaggablesExists(id);
        // 删除
        taggablesMapper.deleteById(id);
    }

    private void validateTaggablesExists(Long id) {
        TaggablesDO taggablesDO = taggablesMapper.selectById(id);
        if (taggablesDO == null) {
            throw exception(TAGGABLES_NOT_EXISTS);
        }
        if (taggablesDO.getType() == 1) {
            throw exception(TAGS_NOT_DEL);
        }
    }

    @Override
    public TaggablesDO getTaggables(Long id) {
        return taggablesMapper.selectById(id);
    }

    @Override
    public List<TaggablesDO> getTaggablesList(Collection<Long> ids) {
        return taggablesMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<TaggablesDO> getTaggablesPage(TaggablesPageReqVO pageReqVO) {
        return taggablesMapper.selectPage(pageReqVO);
    }

    @Override
    public List<TaggablesDO> getTaggablesList(TaggablesExportReqVO exportReqVO) {
        return taggablesMapper.selectList(exportReqVO);
    }

    @Override
    public Long getTaggableCount(TaggablesExportReqVO exportReqVO) {
        return taggablesMapper.selectCount(new LambdaQueryWrapperX<TaggablesDO>()
                .eqIfPresent(TaggablesDO::getTagId, exportReqVO.getTagId())
                .eqIfPresent(TaggablesDO::getTaggableId, exportReqVO.getTaggableId())
                .eqIfPresent(TaggablesDO::getTaggableType, exportReqVO.getTaggableType())
                .betweenIfPresent(TaggablesDO::getCreateTime, exportReqVO.getCreateTime()));
    }

    @Override
    public void deleteByTagId(Long tagId) {
        taggablesMapper.delete(new LambdaQueryWrapper<TaggablesDO>().eq(TaggablesDO::getTagId, tagId));
    }

    @Override
    public Boolean batchCreateTaggables(TaggablesBatchCreateReqVO createReqVO) {
        String[] taggleIds = createReqVO.getTaggableIds().split(",");
        String[] tagIds = createReqVO.getTagIds().split(",");
        String taggableType = createReqVO.getTaggableType();
        for (String tagId : tagIds) {
            for (String taggleId : taggleIds) {
                TaggablesDO taggables = new TaggablesDO();
                taggables.setTagId(Long.valueOf(tagId));
                taggables.setTaggableId(Long.valueOf(taggleId));
                taggables.setTaggableType(taggableType);
                taggables.setType(0);
                Long exist = taggablesMapper.selectCount(new LambdaQueryWrapper<TaggablesDO>()
                        .eq(TaggablesDO::getTagId, taggables.getTagId())
                        .eq(TaggablesDO::getTaggableId, taggables.getTaggableId())
                        .eq(TaggablesDO::getTaggableType, taggables.getTaggableType())
                );
                if (exist > 0) {
                    continue;
                }
                taggablesMapper.insert(taggables);
            }
        }
        return true;
    }

    @Override
    public void add(Long id, String type, String monitorTags) {
        List<TaggablesDO> taggablesDOS = taggablesMapper.selectList(new LambdaQueryWrapper<TaggablesDO>().eq(TaggablesDO::getTaggableId, id)
                .eq(TaggablesDO::getTaggableType, type));
        if (taggablesDOS.size()>0){
            taggablesMapper.deleteBatchIds(taggablesDOS.stream().map(TaggablesDO::getId).toList());
        }
        String[] tags = monitorTags.split(",");
        for (String tag : tags) {
            TaggablesDO taggables = new TaggablesDO();
            taggables.setTagId(Long.valueOf(tag));
            taggables.setTaggableId(id);
            taggables.setTaggableType(type);
            taggables.setType(0);
            taggablesMapper.insert(taggables);
        }
    }

    @Override
    public void del(Long id) {
        taggablesMapper.delete(new LambdaQueryWrapper<TaggablesDO>()
                .eq(TaggablesDO::getTaggableId, id));
    }

}
