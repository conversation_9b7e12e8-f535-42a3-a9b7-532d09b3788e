package cn.iocoder.zj.module.monitor.dal.dataobject.storagepool;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 存储池 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_storage_pool")
@KeySequence("monitor_storage_pool_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StoragePoolDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 存储池uuid
     */
    private String uuid;
    /**
     * 存储名称
     */
    private String name;
    /**
     * 存储池类型
     */
    private String type;
    /**
     * 存储名称
     */
    private String description;
    /**
     * 存储池创建时间
     */
    private LocalDateTime vCreateDate;
    /**
     * 存储池修改时间
     */
    private LocalDateTime lastOpDate;
    /**
     * 已使用容量
     */
    private BigDecimal usedCapacity;
    /**
     * 虚拟可用容量
     */
    private BigDecimal availableCapacity;
    /**
     * 总容量
     */
    private BigDecimal totalCapacity;
    /**
     * 主存储uuid
     */
    private String storageUuid;
    /**
     * 数据安全类型
     */
    private String securityPolicy;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;

}
