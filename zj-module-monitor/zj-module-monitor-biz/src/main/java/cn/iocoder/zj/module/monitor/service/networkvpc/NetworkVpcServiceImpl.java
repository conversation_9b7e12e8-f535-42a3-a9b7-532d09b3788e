package cn.iocoder.zj.module.monitor.service.networkvpc;

import cn.hutool.core.convert.Convert;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkVpcDTO;
import cn.iocoder.zj.module.monitor.controller.admin.networkvpc.vo.NetworkVpcCreateReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.networkvpc.vo.NetworkVpcExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.networkvpc.vo.NetworkVpcPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.networkvpc.vo.NetworkVpcUpdateReqVO;
import cn.iocoder.zj.module.monitor.convert.networkvpc.NetworkVpcConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkvpc.NetworkVpcDO;
import cn.iocoder.zj.module.monitor.dal.mysql.networkvpc.NetworkVpcMapper;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.NETWORK_VPC_NOT_EXISTS;

/**
 * VPC路由器 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class NetworkVpcServiceImpl implements NetworkVpcService {

    @Resource
    private NetworkVpcMapper networkVpcMapper;
    @Resource
    private PermissionApi permissionApi;
    @Resource
    private RoleApi roleApi;
    @Resource
    PlatformconfigApi platformconfigApi;


    @Override
    public Long createNetworkVpc(NetworkVpcCreateReqVO createReqVO) {
        // 插入
        NetworkVpcDO networkVpc = NetworkVpcConvert.INSTANCE.convert(createReqVO);
        networkVpcMapper.insert(networkVpc);
        // 返回
        return networkVpc.getId();
    }

    @Override
    public void updateNetworkVpc(NetworkVpcUpdateReqVO updateReqVO) {
        // 校验存在
        validateNetworkVpcExists(updateReqVO.getId());
        // 更新
        NetworkVpcDO updateObj = NetworkVpcConvert.INSTANCE.convert(updateReqVO);
        networkVpcMapper.updateById(updateObj);
    }

    @Override
    public void deleteNetworkVpc(Long id) {
        // 校验存在
        validateNetworkVpcExists(id);
        // 删除
        networkVpcMapper.deleteById(id);
    }

    private void validateNetworkVpcExists(Long id) {
        if (networkVpcMapper.selectById(id) == null) {
            throw exception(NETWORK_VPC_NOT_EXISTS);
        }
    }

    @Override
    public NetworkVpcDO getNetworkVpc(Long id) {
        return networkVpcMapper.selectById(id);
    }

    @Override
    public List<NetworkVpcDO> getNetworkVpcList(Collection<Long> ids) {
        return networkVpcMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<NetworkVpcDO> getNetworkVpcPage(NetworkVpcPageReqVO pageReqVO) {
        List<Map> platform = new ArrayList<>();
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
        return networkVpcMapper.selectPage(pageReqVO,platform);
    }

    @Override
    public List<NetworkVpcDO> getNetworkVpcList(NetworkVpcExportReqVO exportReqVO) {
        return networkVpcMapper.selectList(exportReqVO);
    }

    @Override
    public void createNetworkL3List(List<NetworkVpcDO> list) {
         networkVpcMapper.insertBatch(list);
    }

    @Override
    public Long getNetWorkL3Count() {
        return networkVpcMapper.selectCount();
    }

    @Override
    public void updateNetWorkVpcList(List<NetWorkVpcDTO> list) {
        networkVpcMapper.updateNetWorkVpcList(list);
    }

    @Override
    public List<NetworkVpcDO> getNetWorkVpcList() {
        return networkVpcMapper.selectList();
    }

    @Override
    public int deleteNetWorkVPCByNameList(List<NetworkVpcDO> list) {
        return networkVpcMapper.deleteNetWorkVPCByNameList(list);
    }

    @Override
    public List<Map<String, String>> getClusterSimpleInfo(Collection<Long> platformId) {
        Collection<Long> finalPlatformIds = platformId;
        if (finalPlatformIds == null) {
            finalPlatformIds = new ArrayList<>();
        }
        // 如果集合为空，从平台配置中获取所有平台ID
        if (finalPlatformIds.isEmpty()) {
            LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
            List<Map> platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
            finalPlatformIds = new ArrayList<>();
            for (Map map : platform) {
                finalPlatformIds.add(Convert.toLong(map.get("platformId")));
            }
        }
        return networkVpcMapper.getClusterSimpleInfo(finalPlatformIds);
    }

    @Override
    public void deleteNetworkVpcByplatform(Long platformId) {
        networkVpcMapper.deleteNetworkVpcByplatform(platformId);
    }

}
