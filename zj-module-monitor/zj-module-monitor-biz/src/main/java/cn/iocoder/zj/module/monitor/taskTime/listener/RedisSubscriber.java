package cn.iocoder.zj.module.monitor.taskTime.listener;

/**
 * <AUTHOR>
 **/
import cn.iocoder.zj.module.monitor.taskTime.service.ScheduleTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class RedisSubscriber implements MessageListener {

    @Resource
    private ScheduleTaskService scheduleTaskService;


    @Override
    public void onMessage(Message message, byte[] pattern) {
        String channel = new String(message.getChannel());
        String messageBody = new String(message.getBody());
        log.info("订阅消息: {} 订阅任务: {}", messageBody, channel);
        if(messageBody.contains("&")){
            String[] split = messageBody.split("&");
            scheduleTaskService.updateTask(split[0],split[1]);
        }else {
            scheduleTaskService.removeTask(messageBody);
        }
    }
}

