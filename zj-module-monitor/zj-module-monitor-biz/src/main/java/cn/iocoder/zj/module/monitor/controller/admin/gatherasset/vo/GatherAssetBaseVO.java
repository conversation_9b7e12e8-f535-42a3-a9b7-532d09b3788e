package cn.iocoder.zj.module.monitor.controller.admin.gatherasset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

/**
* 租户资产 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class GatherAssetBaseVO {

    @Schema(description = "租户绑定的采集设备id")
    private String uuid;

    @Schema(description = "访问设备的ip地址 (例：udp:*************)")
    @Size(max = 50, message = "ip最大长度为50个字符")
    private String ip;

    @Schema(description = "snmp设置的团体名")
    @Size(max = 50, message = "snmp团体名最大长度为50个字符")
    private String community;

    @Schema(description = "snmp版本 (0=v1; 1=v2c; 2=v3)")
    private Integer version;

    @Schema(description = "snmp 端口号")
    @Max(value = 99999999,message = "snmp端口号最大长度为8个字符")
    private Integer snmpPort;

    @Schema(description = "主机名称")
    @Size(max = 50, message = "主机名称最大长度为50个字符")
    private String hostName;

    @Schema(description = "告警位置")
    @Size(max = 225, message = "告警位置最大长度为225个字符")
    private String alarmLocation;

    @Schema(description = "资产类型")
    private String dictIcon;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "平台id")
    private Long platformId;

    /**
     * @description: 资产类型
     * <AUTHOR>
     * @date 2023/8/14 15:49
     * @version 1.0
     */
    @Schema(description = "资产类型")
    private String deviceType;
    /**
     * @description: 资产类型名称
     * <AUTHOR>
     * @date 2023/8/14 15:49
     * @version 1.0
     */
    @Schema(description = "资产类型名称")
    private String deviceName;
    /**
     * @description: 系统类型
     * <AUTHOR>
     * @date 2023/8/14 15:49
     * @version 1.0
     */
    @Schema(description = "系统类型")
    private String sysType;
    /**
     * @description: 系统类型名称
     * <AUTHOR>
     * @date 2023/8/14 15:49
     * @version 1.0
     */
    @Schema(description = "系统类型名称")
    private String sysName;

    /**
     * cpu 使用率
     */
    @Schema(description = "cpu使用率")
    private Double cpuUsage;

    /**
     * 内存使用率
     */
    @Schema(description = "内存使用率")
    private Double memoryUsage;


    /**
     * 磁盘使用率
     */
    @Schema(description = "磁盘使用率")
    private Double diskUsage;

    @Schema(description = "存活状态 0离线  1在线")
    private Integer onlineType;
}
