package cn.iocoder.zj.module.monitor.controller.admin.hostinfo.vo;

import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName : HostInfoRpcVO  //类名
 * @Description : 返回类  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/9/1  10:28
 */
@Data
public class HostInfoRpcVO extends BaseDO {
    /**
     * 主键
     *
     private Long id;
     /**
     * 主机id
     */
    private String uuid;
    /**
     * 主机名称
     */
    private String name;
    /**
     * 主机状态
     */
    private String state;
    /**
     * ip
     */
    private String ip;
    /**
     * 弹性ip
     */
    private String vipIp;
    /**
     * 集群id
     */
    private String zoneUuid;
    /**
     * 集群id
     */
    private String clusterUuid;

    private String imageUuid;
    private String imageName;
    /**
     * 集群id
     */
    private String hardwareUuid;
    /**
     * CPU架构
     */
    private String architecture;
    /**
     * 操作系统类型
     */
    private String guestOsType;
    /**
     * 主机创建时间
     */
    private Date vCreateDate;

    /**
     * 硬盘使用率
     */
    private BigDecimal diskUsed;

    /**
     * cpu 使用率
     */
    private BigDecimal cpuUsed;

    /**
     * 内存使用率
     */
    private BigDecimal memoryUsed;

    /**
     * 主机类型
     */
    private String type;
    /**
     * 分配内存
     */
    private Long memorySize;
    /**
     * 分配cpu
     */
    private Integer cpuNum;
    /**
     * mac 地址
     */
    private String mac;

    /**
     * 租户id
     */
    private Long tenantId;

    private Long regionId;

    private String zoneName;

    private String clusterName;

    private String hardwareName;

    private BigDecimal networkInBytes;

    private BigDecimal networkOutBytes;

    private BigDecimal diskUsedBytes;

    private BigDecimal diskFreeBytes;

    private BigDecimal totalDiskCapacity;

    private BigDecimal actualSize;

    private BigDecimal cloudSize;

    private BigDecimal networkInPackets;

    private BigDecimal networkOutPackets;

    private Integer deleted;

    private Long platformId;

    private String platformName;
    /**
     * 电源状态
     */
    private String powerState;

    private String iso;

    private String autoInitType;

    private String guideMode;
}
