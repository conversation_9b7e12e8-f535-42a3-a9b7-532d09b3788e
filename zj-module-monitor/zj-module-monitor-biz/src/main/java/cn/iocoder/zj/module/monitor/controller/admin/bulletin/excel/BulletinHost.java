package cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel;

import cn.iocoder.zj.framework.excel.core.convert.ByteConvent;
import cn.iocoder.zj.framework.excel.core.convert.UnitConvert;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.convert.BulletinPercentConvert;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.convert.BulletinPowerConvert;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.convert.BulletinStateConvert;
import cn.iocoder.zj.module.monitor.controller.admin.tags.vo.TagsRespVO;
import cn.iocoder.zj.module.monitor.controller.admin.volumeinfo.vo.VolumeInfoUseReqVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.secgroup.SecgroupDO;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ExcelIgnoreUnannotated
public class BulletinHost {
    @ExcelProperty("主机名称")
    private String name;

    @ExcelProperty("平台名称")
    private String platformName;

    @ExcelProperty("弹性ip")
    private String vipIp;

    @ExcelProperty(value = "启用状态", converter = BulletinStateConvert.class)
    private String state;

    @Schema(description = "云盘信息")
    private List<VolumeInfoUseReqVO> diskInfos;

    @Schema(description = "主机id")
    private String uuid;

    //    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty(value = "CPU使用率",converter = BulletinPercentConvert.class)
    private BigDecimal cpuUsed;

    @ExcelProperty(value = "内存使用率",converter = BulletinPercentConvert.class)
    private BigDecimal memoryUsed;

    @ExcelProperty(value = "硬盘使用率",converter = BulletinPercentConvert.class)
    private BigDecimal diskUsed;

    @ExcelProperty("IP地址")
    private String ip;

    @ExcelProperty("MAC地址")
    private String mac;

    @ExcelProperty("集群名称")
    private String clusterName;

    @ExcelProperty("CPU架构")
    private String architecture;

    @ExcelProperty("主机类型")
    private String type;

    @ExcelProperty("系统类型")
    private String guestOsType;

    @ExcelProperty(value = "电源状态", converter = BulletinPowerConvert.class)
    private String powerState;

    @ExcelProperty("标签")
    private String tagsStr;

    @Schema(description = "标签信息")
    private List<TagsRespVO> tags;


//    @ExcelProperty("区域")
//    private String manager;

    @ExcelProperty("可用区域")
    private String zoneName;

    @ExcelProperty("系统镜像")
    private String imageName;

    @ExcelProperty("宿主机")
    private String hardwareName;

    @Schema(description = "安全组信息")
    private List<SecgroupDO> secgroupDOS;

    @ExcelProperty("安全组")
    private String secgroup;

    @ExcelProperty(value = "CPU", converter = UnitConvert.class)
    private Integer cpuNum;

    @ExcelProperty(value = "内存", converter = ByteConvent.class)
    private Long memorySize;

    // 系统盘
    @ExcelProperty(value = "系统盘")
    private String sysDiskInfos;

    // 数据盘
    @ExcelProperty(value = "数据盘")
    private String dataDiskInfos;

    @ExcelProperty("ISO")
    private String iso;

    @ExcelProperty("自动启动")
    private String autoInitType;

    @ExcelProperty("引导模式")
    private String guideMode;
}
