package cn.iocoder.zj.module.monitor.dal.mysql.monitorauthorization;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.monitor.dal.dataobject.monitorauthorization.MonitorAuthorizationDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 监控申请授权 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MonitorAuthMapper extends BaseMapperX<MonitorAuthorizationDO> {
    void deleteMonitorAuthorizationInfo(@Param("id") Long id);

    void updateMonitorAuthorizationInfo(@Param("id") Long id, @Param("authorizationType") String authorizationType);

    MonitorAuthorizationDO selectByIdInfo(Long id);
}
