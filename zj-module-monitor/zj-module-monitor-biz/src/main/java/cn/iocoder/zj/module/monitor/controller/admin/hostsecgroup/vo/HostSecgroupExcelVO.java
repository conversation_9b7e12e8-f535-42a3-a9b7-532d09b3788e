package cn.iocoder.zj.module.monitor.controller.admin.hostsecgroup.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 安全组关联云主机 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class HostSecgroupExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("云主机uuid")
    private String hostUuid;

    @ExcelProperty("安全组uuid")
    private String secgroupUuid;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("平台id")
    private Long platformId;

    @ExcelProperty("平台名称")
    private String platformName;

}
