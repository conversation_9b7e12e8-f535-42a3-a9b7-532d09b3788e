package cn.iocoder.zj.module.monitor.dal.mysql.tags;

import cn.iocoder.zj.framework.common.dal.manager.TagData;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.controller.admin.tags.vo.TagsExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.tags.vo.TagsPageReqVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.tags.TagsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Arrays;
import java.util.List;

/**
 * 标签 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TagsMapper extends BaseMapperX<TagsDO> {

    default PageResult<TagsDO> selectPage(TagsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TagsDO>()
                .likeIfPresent(TagsDO::getName, reqVO.getName())
                .eqIfPresent(TagsDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(TagsDO::getCreateTime, reqVO.getCreateTime())
                .notInSql(reqVO.getNotInIds() != null && !reqVO.getNotInIds().isEmpty(),TagsDO::getId, reqVO.getNotInIds())
                .orderBy(
                        reqVO.getSortBy() != null && reqVO.getSortBy().equals("totalCount"),
                        reqVO.getSortDirection() != null && reqVO.getSortDirection().equalsIgnoreCase("asc"),
                        TagsDO::getTotalCount
                )
                .orderBy(
                        reqVO.getSortBy() != null && reqVO.getSortBy().equals("createTime"),
                        reqVO.getSortDirection() != null && reqVO.getSortDirection().equalsIgnoreCase("asc"),
                        TagsDO::getCreateTime
                )
                .orderByDesc(TagsDO::getId));
    }

    default List<TagsDO> selectList(TagsExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TagsDO>()
                .likeIfPresent(TagsDO::getName, reqVO.getName())
                .eqIfPresent(TagsDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(TagsDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TagsDO::getId));
    }

    @TenantIgnore
    List<TagsDO> getAllList(@Param("tag") TagsDO tag);

    @TenantIgnore
    List<TagData> getTenantByPlatform();

    @TenantIgnore
    void updateByTagName(@Param("changed")List<TagData> changed);
}
