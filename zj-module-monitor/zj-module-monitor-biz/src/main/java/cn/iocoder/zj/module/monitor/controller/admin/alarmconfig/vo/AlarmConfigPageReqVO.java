package cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 告警配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AlarmConfigPageReqVO extends PageParam {

    @Schema(description = "告警名称")
    private String alarmName;

    @Schema(description = "告警简介")
    private String description;

    @Schema(description = "资源类型，云主机monitor_alarm_host;宿主机monitor_alarm_hardware;存储monitor_alarm_disk;monitor_alarm_image镜像,多个做查询条件时用逗号拼接")
    private String sourceType;

    @Schema(description = "字典名称")
    private String dictLabelName;

    @Schema(description = "字典类型")
    private String dictLabelType;

    @Schema(description = "触发规则")
    private String alarmRule;

    @Schema(description = "告警阈值")
    private Integer alarmVal;

    @Schema(description = "收敛次数")
    private Long alarmTime;

    @Schema(description = "告警级别,多个做查询条件时用逗号拼接")
    private String alarmLevel;

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String startTime;

    @Schema(description = "结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String endTime;

    @Schema(description = "消息内容")
    private String context;

    @Schema(description = "租户ID,当前用户为租户时必传，为管理员时不传")
    private List<String> tenantId;

    @Schema(description = "资源uuid")
    private String uuid;

    private List<String> levelList;

    private List<String> SourceTypeList;

    private String maintainerIds;

    private List<Long> tenantList;
}
