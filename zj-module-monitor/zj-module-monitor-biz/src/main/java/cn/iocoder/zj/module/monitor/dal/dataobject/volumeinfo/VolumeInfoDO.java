package cn.iocoder.zj.module.monitor.dal.dataobject.volumeinfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 云盘信息 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_volume_info")
@KeySequence("monitor_volume_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VolumeInfoDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 云盘uuid
     */
    private String uuid;
    /**
     * 平台id
     */
    private String platformId;
    /**
     * 平台名称
     */
    private String platformName;
    /**
     * 云盘名称
     */
    private String name;
    /**
     * 云盘详细描述
     */
    private String description;
    /**
     * 主存储uuid
     */
    private String primaryStorageUuid;
    /**
     * 主存储名称
     */
    private String primaryStorageName;
    /**
     * 主存储类型
     */
    private String primaryStorageType;
    /**
     * 云主机uuid
     */
    private String vmInstanceUuid;
    /**
     * 云主机名称
     */
    private String vmInstanceName;
    /**
     * 云盘类型，数据云盘/根云盘
     */
    private String type;
    /**
     * 云盘格式
     */
    private String format;
    /**
     * 云盘大小
     */
    private Long size;
    /**
     * 云盘真实大小
     */
    private Long actualSize;
    /**
     * 云盘是否开启
     */
    private String state;
    /**
     * 云盘状态
     */
    private String status;

    private String actualRatio;

    private Long actualFree;

    private Long actualUse;

    // 云盘创建时间
    private Date vCreateDate;

    // 云盘创建时间
    private Date vUpdateDate;

    // 最大iops max_iops
    private Long maxIops;

    //吞吐量
    private BigDecimal throughput;

    //介质类型
    private String mediaType;

    //挂载状态
    private Boolean isMount;

    private String tag;
}
