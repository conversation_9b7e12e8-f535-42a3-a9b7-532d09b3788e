package cn.iocoder.zj.module.monitor.controller.admin.taggables.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.beans.factory.annotation.Required;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 标签绑定关系分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TaggablesPageReqVO extends PageParam {

    @Schema(description = "标签id")
    private Long tagId;

    @Schema(description = "绑定资产id ")
    private Long taggableId;

    @Schema(description = "绑定资产ids ")
    private List<Long> taggableIds;

    @Schema(description = "绑定资产类型")
    private String taggableType;

    @Schema(description = "绑定资产名称")
    private String taggableName;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
