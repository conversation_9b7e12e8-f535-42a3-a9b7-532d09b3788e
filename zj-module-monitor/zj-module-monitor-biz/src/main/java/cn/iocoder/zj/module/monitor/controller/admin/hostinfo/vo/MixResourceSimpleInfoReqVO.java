package cn.iocoder.zj.module.monitor.controller.admin.hostinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class MixResourceSimpleInfoReqVO {

    @Schema(description = "平台ID")
    private Long platformId;

    @Schema(description = "资源类型")
    private String sourceType;

    @Schema(description = "页码")
    private Integer pageNo;

    @Schema(description = "每页条数")
    private Integer pageSize;

    @Schema(description = "查询条件，按名称模糊查询")
    private String queryData;
}
