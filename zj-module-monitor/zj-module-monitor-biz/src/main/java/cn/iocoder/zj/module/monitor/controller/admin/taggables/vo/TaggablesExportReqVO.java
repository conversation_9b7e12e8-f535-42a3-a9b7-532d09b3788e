package cn.iocoder.zj.module.monitor.controller.admin.taggables.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 标签绑定关系 Excel 导出 Request VO，参数和 TaggablesPageReqVO 是一致的")
@Data
public class TaggablesExportReqVO {

    @Schema(description = "标签id")
    private Long tagId;

    @Schema(description = "绑定资产id ")
    private Long taggableId;

    @Schema(description = "绑定资产类型")
    private String taggableType;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
