package cn.iocoder.zj.module.monitor.service.taggables.enums;

import lombok.Getter;

@Getter
public enum TagAssetTypeEnum {
    //资产相关
    HOST("hardware", "宿主机", "monitor_hardware_info","id,`name`,uuid,platform_id,platform_name,create_time"),
    INSTANCE("host", "云主机", "monitor_host_info","id,`name`,uuid,platform_id,platform_name,create_time"),
//    PRIMARY_STORAGE("primary_storage", "主存储", "monitor_storage_info","id,`name`,uuid,platform_id,platform_name,create_time"),
    IMAGE("image", "镜像", "monitor_image_info","id,`name`,uuid,platform_id,platform_name,create_time"),
    DISK("disk", "云硬盘", "monitor_volume_info","id,`name`,uuid,platform_id,platform_name,create_time"),
    SNAPSHOT("snapshot", "快照", "monitor_volume_snapshot","id,`name`,uuid,platform_id,platform_name,create_time"),
    STORAGE("storage", "云存储", "monitor_storage_info","id,`name`,uuid,platform_id,platform_name,create_time"),

    //监控相关
    NETWORK("network", "网络", "hzb_monitor","id,`name`,id as uuid,platform_id,platform_name,DATE_FORMAT(gmt_create, '%Y-%m-%d %H:%i:%s') AS create_time"),
    FIREWALL("firewall", "安全", "hzb_monitor","id,`name`,id as uuid,platform_id,platform_name,DATE_FORMAT(gmt_create, '%Y-%m-%d %H:%i:%s') AS create_time"),
    INFRA("infra", "服务器", "hzb_monitor","id,`name`,id as uuid,platform_id,platform_name,DATE_FORMAT(gmt_create, '%Y-%m-%d %H:%i:%s') AS create_time"),
    STORED("stored", "存储", "hzb_monitor","id,`name`,id as uuid,platform_id,platform_name,DATE_FORMAT(gmt_create, '%Y-%m-%d %H:%i:%s') AS create_time"),
    OS("os", "操作系统", "hzb_monitor","id,`name`,id as uuid,platform_id,platform_name,DATE_FORMAT(gmt_create, '%Y-%m-%d %H:%i:%s') AS create_time"),
    MID("mid", "中间件", "hzb_monitor","id,`name`,id as uuid,platform_id,platform_name,DATE_FORMAT(gmt_create, '%Y-%m-%d %H:%i:%s') AS create_time"),
    DB("db", "数据库", "hzb_monitor","id,`name`,id as uuid,platform_id,platform_name,DATE_FORMAT(gmt_create, '%Y-%m-%d %H:%i:%s') AS create_time"),
    CN("cn", "容器", "hzb_monitor","id,`name`,id as uuid,platform_id,platform_name,DATE_FORMAT(gmt_create, '%Y-%m-%d %H:%i:%s') AS create_time"),
    SERVICE("service", "应用服务", "hzb_monitor","id,`name`,id as uuid,platform_id,platform_name,DATE_FORMAT(gmt_create, '%Y-%m-%d %H:%i:%s') AS create_time"),
    ;

    private final String code;
    private final String description;
    private final String table;
    private final String field;

    TagAssetTypeEnum(String code, String description, String table,String field) {
        this.code = code;
        this.description = description;
        this.table = table;
        this.field = field;
    }
}
