package cn.iocoder.zj.module.monitor.controller.admin.hostinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@Schema(description = "资源趋势分析-请求参数")
@ToString(callSuper = true)
public class ReckonReqVO {
    /**
     * 预测时间跨度
     */
    @Schema(description = "7天：7d,15天：15d")
    String reckonTime;
    /**
     * 主机uuid
     */
    @Schema(description = "多个用逗号拼接")
    String uuid;
    /**
     * 平台ID
     */
    @Schema(description = "平台ID，多个逗号拼接")
    String platformId;

    @Schema(description = "租户平台集合")
    List<Long> platformList;
}
