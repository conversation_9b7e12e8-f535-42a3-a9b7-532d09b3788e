package cn.iocoder.zj.module.monitor.controller.admin.topreport.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TopReportRespVO extends TopReportBaseVO {

    private Long id;
    private Date createTime;

    private Date updateTime;

    private String dateCycle;

    /**
     * 资产id列表
     */
    private List<TopReportAssetVO> assets;

    /**
     * 指标名称列表
     */
    private List<TopReportMetricVO> metrics;

    private Map<String, List<?>> params;
}
