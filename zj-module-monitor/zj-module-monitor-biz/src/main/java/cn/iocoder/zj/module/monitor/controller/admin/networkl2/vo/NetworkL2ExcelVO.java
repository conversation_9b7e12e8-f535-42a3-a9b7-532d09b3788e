package cn.iocoder.zj.module.monitor.controller.admin.networkl2.vo;

import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 二级网络信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class NetworkL2ExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("二级网络名称")
    private String name;

    @ExcelProperty("二级网络uuid")
    private String uuid;

    @ExcelProperty("网卡")
    private String physicalInterface;

    @ExcelProperty("二级网络类型")
    private String type;

    @ExcelProperty("vlan")
    private String vlan;

    @ExcelProperty("虚拟网络标识")
    private Integer virtualNetworkId;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private LocalDateTime createTime;

    @ExcelProperty("地区名称")
    private String regionName;

    @ExcelProperty("地区id")
    private Long regionId;

    @ExcelProperty("平台id")
    private Long platformId;

    @ExcelProperty("平台名称")
    private String platformName;

}
