package cn.iocoder.zj.module.monitor.convert.tags;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.api.tag.dto.TagRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.tags.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.tags.TagsDO;

/**
 * 标签 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TagsConvert {

    TagsConvert INSTANCE = Mappers.getMapper(TagsConvert.class);

    TagsDO convert(TagsCreateReqVO bean);

    TagsDO convert(TagsUpdateReqVO bean);

    TagsRespVO convert(TagsDO bean);

    List<TagsRespVO> convertList(List<TagsDO> list);

    PageResult<TagsRespVO> convertPage(PageResult<TagsDO> page);

    List<TagsExcelVO> convertList02(List<TagsDO> list);

    List<TagRespDTO> convertListDto(List<TagsDO> list);

}
