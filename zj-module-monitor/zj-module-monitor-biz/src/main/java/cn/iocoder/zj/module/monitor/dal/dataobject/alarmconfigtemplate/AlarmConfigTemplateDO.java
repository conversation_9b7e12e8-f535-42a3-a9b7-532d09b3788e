package cn.iocoder.zj.module.monitor.dal.dataobject.alarmconfigtemplate;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 告警配置模板 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_alarm_config_template")
@KeySequence("monitor_alarm_config_template_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmConfigTemplateDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 消息内容
     */
    private String context;
    /**
     * 告警名称
     */
    private String alarmName;
    /**
     * 告警简介
     */
    private String description;
    /**
     * 资源类型，host云主机；hardware物理机；storage存储；image镜像
     */
    private String sourceType;
    /**
     * 字典名称
     */
    private String dictLabelName;
    /**
     * 字典类型
     */
    private String dictLabelType;
    /**
     * 字典值
     */
    private String dictLabelValue;
    /**
     * 触发规则
     */
    private String alarmRule;
    /**
     * 告警阈值
     */
    private Integer alarmVal;
    /**
     * 告警条目单位
     */
    private String unit;
    /**
     * 收敛次数
     */
    private Long alarmTime;
    /**
     * 告警级别：1提示，2警告，3严重
     */
    private Integer alarmLevel;
    /**
     * 告警条目单位的字典项code
     */
    private String unitType;

}
