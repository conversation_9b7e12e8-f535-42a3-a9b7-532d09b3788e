package cn.iocoder.zj.module.monitor.dal.mysql.gatherasset;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.monitor.dal.dataobject.gatherasset.GatherAssetDO;
import cn.iocoder.zj.module.monitor.util.StringUtil;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.monitor.controller.admin.gatherasset.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 租户资产 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface GatherAssetMapper extends BaseMapperX<GatherAssetDO> {

    default PageResult<GatherAssetDO> selectPage(GatherAssetPageReqVO reqVO, boolean admin, List<Map> platform) {
        List<String> uids = new ArrayList<>();
        if (StringUtil.isNotEmpty(reqVO.getIds())) {
            uids = Arrays.asList(reqVO.getIds().split(","));
        }
        if (admin) {
            LambdaQueryWrapperX<GatherAssetDO> queryWrapper = new LambdaQueryWrapperX<GatherAssetDO>()
                    .eqIfPresent(GatherAssetDO::getUuid, reqVO.getUuid())
                    .likeIfPresent(GatherAssetDO::getIp, reqVO.getIp())
                    .eqIfPresent(GatherAssetDO::getCommunity, reqVO.getCommunity())
                    .eqIfPresent(GatherAssetDO::getVersion, reqVO.getVersion())
                    .eqIfPresent(GatherAssetDO::getSnmpPort, reqVO.getSnmpPort())
                    .likeIfPresent(GatherAssetDO::getHostName, reqVO.getName())
                    .betweenIfPresent(GatherAssetDO::getCreateTime, reqVO.getCreateTime())
                    .eqIfPresent(GatherAssetDO::getPlatformId, reqVO.getPlatformId())
                    .eqIfPresent(GatherAssetDO::getSysType, reqVO.getSysType())
                    .eqIfPresent(GatherAssetDO::getDeviceType, reqVO.getDeviceType())
                    .eqIfPresent(GatherAssetDO::getOnlineType, reqVO.getOnlineType())
                    .orderByDesc(GatherAssetDO::getId);
            if (!uids.isEmpty()) {
                queryWrapper = (LambdaQueryWrapperX<GatherAssetDO>) queryWrapper.notIn(GatherAssetDO::getId, uids);
            }
            return selectPage(reqVO, queryWrapper);
        } else {
            List<String> data = new ArrayList<>();
            if (platform.size() > 0) {
                for (Map map : platform) {
                    data.add(map.get("platformId").toString());
                }
            }
            LambdaQueryWrapperX<GatherAssetDO> queryWrapper = new LambdaQueryWrapperX<GatherAssetDO>()
                    .eqIfPresent(GatherAssetDO::getUuid, reqVO.getUuid())
                    .likeIfPresent(GatherAssetDO::getIp, reqVO.getIp())
                    .eqIfPresent(GatherAssetDO::getCommunity, reqVO.getCommunity())
                    .eqIfPresent(GatherAssetDO::getVersion, reqVO.getVersion())
                    .eqIfPresent(GatherAssetDO::getSnmpPort, reqVO.getSnmpPort())
                    .likeIfPresent(GatherAssetDO::getHostName, reqVO.getName())
                    .betweenIfPresent(GatherAssetDO::getCreateTime, reqVO.getCreateTime())
                    .eqIfPresent(GatherAssetDO::getPlatformId, reqVO.getPlatformId())
                    .orderByDesc(GatherAssetDO::getId);
            if (data.size() > 0) {
                queryWrapper = (LambdaQueryWrapperX<GatherAssetDO>) queryWrapper.in(GatherAssetDO::getPlatformId, data);
            }
            if (!uids.isEmpty()) {
                queryWrapper = (LambdaQueryWrapperX<GatherAssetDO>) queryWrapper.notIn(GatherAssetDO::getId, uids);
            }
            return selectPage(reqVO, queryWrapper);
        }

    }

    default List<GatherAssetDO> selectList(GatherAssetExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<GatherAssetDO>()
                .eqIfPresent(GatherAssetDO::getUuid, reqVO.getUuid())
                .eqIfPresent(GatherAssetDO::getIp, reqVO.getIp())
                .eqIfPresent(GatherAssetDO::getCommunity, reqVO.getCommunity())
                .eqIfPresent(GatherAssetDO::getVersion, reqVO.getVersion())
                .eqIfPresent(GatherAssetDO::getSnmpPort, reqVO.getSnmpPort())
                .likeIfPresent(GatherAssetDO::getHostName, reqVO.getHostName())
                .betweenIfPresent(GatherAssetDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(GatherAssetDO::getPlatformId, reqVO.getPlatformId())
                .orderByDesc(GatherAssetDO::getId));
    }

    List<Map> selectByUuId(@Param("assetUuid") String assetUuid);

    Long selectByAssetIp(@Param("ip") String ip, @Param("platformId") Long platformId);

    int updateOnlieType(@Param("list") List<GatherAssetDO> list);

    int updateBatchByList(@Param("list") List<GatherAssetDO> list);

    int selectCountByUuid(@Param("uuid") String uuid);

    Map<String, Object> getGatherAssetStatusCount(@Param("tenantIds") List<String> tenantIds, @Param("platformId")Long platformId);

    default Long getGatherAssetCount(String deviceType) {
        return selectCount(GatherAssetDO::getDeviceType, deviceType);
    }

    default Long getGatherAssetCountBySysType(String sysType) {
        return selectCount(GatherAssetDO::getSysType, sysType);
    }
}
