package cn.iocoder.zj.module.monitor.service.tagplan;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.constants.FileTypeConstants;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.VictoriaMetricsDTO;
import cn.iocoder.zj.framework.common.util.string.StringUtil;
import cn.iocoder.zj.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.framework.warehouse.service.ZjMetricsDataService;
import cn.iocoder.zj.module.infra.api.file.FileApi;
import cn.iocoder.zj.module.monitor.api.hostinfo.HostInfoApi;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.valume.VolumeApi;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeDTO;
import cn.iocoder.zj.module.monitor.controller.admin.tagplan.vo.*;
import cn.iocoder.zj.module.monitor.convert.tagplan.TagPlanConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.tagplan.TagPlanDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.tagplan.TagPlanDTO;
import cn.iocoder.zj.module.monitor.dal.dataobject.tagplan.TagRecordDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.tags.TagsDO;
import cn.iocoder.zj.module.monitor.dal.mysql.tagplan.TagPlanMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.tagplan.TagRecordMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.tags.TagsMapper;
import cn.iocoder.zj.module.monitor.pojo.AssetReqVO;
import cn.iocoder.zj.module.monitor.tagtask.job.model.TagTaskModel;
import cn.iocoder.zj.module.monitor.tagtask.job.model.TaskServiceInfo;
import cn.iocoder.zj.module.monitor.tagtask.job.task.TagTaskService;
import cn.iocoder.zj.module.monitor.taskTime.listener.RedisPublisher;
import cn.iocoder.zj.module.monitor.util.CronExpressionUtil;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import cn.iocoder.zj.module.system.api.tenant.TenantApi;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.RowRenderData;
import com.deepoove.poi.data.Rows;
import com.deepoove.poi.data.TableRenderData;
import com.deepoove.poi.data.Tables;
import com.deepoove.poi.data.style.BorderStyle;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.support.SpringContextHolder;
import org.apache.hertzbeat.common.support.exception.CommonException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.iocoder.zj.module.monitor.service.topreport.TopReportServiceImpl.sortAndLimit;
import static cn.iocoder.zj.module.monitor.util.CronExpressionUtil.getNextExecutionTime;

@Service
@Validated
@Slf4j
public class TagPlanServiceImpl implements TagPlanService {

    @Resource
    private TagPlanMapper tagPlanMapper;

    @Resource
    private TagRecordMapper tagRecordMapper;

    @Autowired
    private TagTaskService tagTaskService;

    @Resource
    private TaskServiceInfo taskServiceInfo;

    @Resource
    private RedisPublisher redisPublisher;

    @Resource
    private TenantApi tenantApi;

    @Resource
    private PlatformconfigApi platformconfigApi;

    @Resource
    private FileApi fileApi;

    @Resource
    private HostInfoApi hostInfoApi;

    @Resource
    private VolumeApi volumeApi;

    @Resource
    private TagsMapper tagsMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTagPlan(TagPlanCreateReqVO createReqVO) {
        //获取当前租户ID
        Long tenantId = SecurityFrameworkUtils.getLoginUser().getTenantId();
        if (ObjectUtil.isEmpty(createReqVO.getTenantId())) {
            createReqVO.setTenantId(tenantId);
        }
        // 插入
        TagPlanDO patrolPlan = TagPlanConvert.INSTANCE.convert(createReqVO);
        patrolPlan.setTenantId(tenantId);
        patrolPlan.setSysSettingTenant(createReqVO.getTenantId());
        patrolPlan.setPlatformIds(createReqVO.getPlatformIds() != null ? createReqVO.getPlatformIds() : "");
        //根据巡检周期和执行日和执行时间生成cron表达式
        switch (patrolPlan.getPeriodType()) {
            case "day":
                patrolPlan.setExecutionCron(CronExpressionUtil.generateDailyCronExpression(patrolPlan.getExecutionTime()));
                break;
            case "week":
                patrolPlan.setExecutionCron(CronExpressionUtil.generateWeeklyCronExpression(patrolPlan.getExecutionTime(), Integer.parseInt(patrolPlan.getExecutionDay())));
                break;
            case "month":
                patrolPlan.setExecutionCron(CronExpressionUtil.generateMonthlyCronExpression(patrolPlan.getExecutionTime(),Integer.parseInt(patrolPlan.getExecutionDay())));
        }
        Date date = new Date();
        Date nextExecutionTime = getNextExecutionTime(patrolPlan.getExecutionCron(), date);
        patrolPlan.setNextExecutionTime(nextExecutionTime);
        //插入job表
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        TagTaskModel task = TagTaskModel
                .builder()
                .jobName(patrolPlan.getName() + "-" + sdf.format(date))
                .cronExpression(patrolPlan.getExecutionCron())
                .beanName("cn.iocoder.zj.module.om.job.Inspection.task.TagTaskService")
                .methodName("processTask")
                .status(1)
                .build();
        tagTaskService.addTask(task);
        taskServiceInfo.scheduleTask(task);
        patrolPlan.setJobId(task.getId());
        tagPlanMapper.insert(patrolPlan);
        return patrolPlan.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTagPlan(TagPlanUpdateReqVO updateReqVO) {
        //获取当前租户ID
        Long tenantId = SecurityFrameworkUtils.getLoginUser().getTenantId();
        // 校验存在
        TagPlanDO patrolPlanDO = validateTagPlanExists(updateReqVO.getId());
        //根据巡检周期和执行日和执行时间生成cron表达式
        switch (updateReqVO.getPeriodType()) {
            case "day":
                updateReqVO.setExecutionCron(CronExpressionUtil
                        .generateDailyCronExpression(updateReqVO.getExecutionTime()));
                break;
            case "week":
                updateReqVO.setExecutionCron(CronExpressionUtil
                        .generateWeeklyCronExpression(updateReqVO.getExecutionTime(),
                                Integer.parseInt(updateReqVO.getExecutionDay())));
                break;
            case "month":
                updateReqVO.setExecutionCron(CronExpressionUtil
                        .generateMonthlyCronExpression(updateReqVO.getExecutionTime(),
                                Integer.parseInt(updateReqVO.getExecutionDay())));
        }
        if (!updateReqVO.getExecutionCron().equals(patrolPlanDO.getExecutionCron())) {
            //需要修改巡检计划表中下次执行时间
            Date nextExecutionTime = getNextExecutionTime(updateReqVO.getExecutionCron(), new Date());
            updateReqVO.setNextExecutionTime(nextExecutionTime);
        }
        //根据巡检Id获取jobId
        Long jobId = tagPlanMapper.selectById(updateReqVO.getId()).getJobId();
        //更新job表
        TagTaskModel lulTaskModel = tagTaskService.getTaskByJobId(jobId);
        if (lulTaskModel != null) {
            lulTaskModel.setCronExpression(updateReqVO.getExecutionCron());
        }
        redisPublisher.publish("lul_task", String.valueOf(jobId));
        tagTaskService.updateTasByJobId(lulTaskModel);
        taskServiceInfo.updateTask(lulTaskModel);
        // 更新
        TagPlanDO updateObj = TagPlanConvert.INSTANCE.convert(updateReqVO);
        updateObj.setSysSettingTenant(updateReqVO.getTenantId());
        updateObj.setTenantId(tenantId);
        // 设置platformIds，如果为null则设置为空字符串
        updateObj.setPlatformIds(updateReqVO.getPlatformIds() != null ? updateReqVO.getPlatformIds() : "");
        tagPlanMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTagPlan(Long id) {
        // 校验存在
        TagPlanDO patrolPlan = validateTagPlanExists(id);
        // 删除
        tagPlanMapper.deleteById(id);
        //删除 job 表
        tagTaskService.removeTask(TagTaskModel.builder().id(patrolPlan.getJobId()).build());
        redisPublisher.publish("lul_task", String.valueOf(patrolPlan.getJobId()));
        taskServiceInfo.removeTask(patrolPlan.getJobId());
    }

    private TagPlanDO validateTagPlanExists(Long id) {
        TagPlanDO patrolPlan = tagPlanMapper.selectById(id);
        if (patrolPlan == null) {
            throw new CommonException("未查询到数据");
        }
        return patrolPlan;
    }

    @Override
    public TagPlanRespVO getTagPlan(Long id) {
        TagPlanDO patrolPlan = tagPlanMapper.selectById(id);
        TagPlanRespVO patrolPlanRespVO = TagPlanConvert.INSTANCE.convert(patrolPlan);
        return patrolPlanRespVO;
    }

    @Override
    public List<TagPlanDO> getTagPlanList(Collection<Long> ids) {
        return tagPlanMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<TagPlanRespVO> getTagPlanPage(TagPlanPageReqVO pageReqVO) {
        Long tenant = SecurityFrameworkUtils.getLoginUser().getTenantId();
        if (ObjectUtil.isEmpty(pageReqVO.getTenantId())) {
            pageReqVO.setTenantId(tenant);
        }
        PageParam pageParam = new PageParam().setPageNo(pageReqVO.getPageNo()).setPageSize(pageReqVO.getPageSize());
        IPage<TagPlanDTO> mpPage = MyBatisUtils.buildPage(pageParam);
        List<TagPlanRespVO> patrolPlanRespVOS = BeanUtil.copyToList(tagPlanMapper.selectTagPlanPage(mpPage, pageReqVO), TagPlanRespVO.class);
        PageResult<TagPlanRespVO> pageResult = new PageResult<>(patrolPlanRespVOS, mpPage.getTotal());
        List<TagPlanRespVO> patrolPlanDOS = pageResult.getList();
        if(ObjectUtil.isNotEmpty(patrolPlanDOS)){
            for (TagPlanRespVO patrolPlanDO : patrolPlanDOS) {
                //根据 sysConfigTenant 获取租户名称
                Long tenantId = patrolPlanDO.getTenantId();
                if (ObjectUtil.isNotEmpty(patrolPlanDO.getSysSettingTenant())) {
                    tenantId = patrolPlanDO.getSysSettingTenant();
                }
                String tenantName = tenantApi.getTenantSimpleInfo(tenantId).getData().getName();
                patrolPlanDO.setTenantName(tenantName);
                //根据 platformIds 获取平台名称
                String platformIds = patrolPlanDO.getPlatformIds();
                if (ObjectUtil.isNotEmpty(platformIds)) {
                    List<Long> platformIdList = Stream.of(platformIds.split(",")).map(Long::parseLong).toList();
                    String platformName = platformconfigApi.getPlatformByIds(platformIdList).getData().stream()
                            .map(PlatformconfigDTO::getName).collect(Collectors.joining(","));
                    patrolPlanDO.setPlatformName(platformName);
                }

                String tags = patrolPlanDO.getTags();
                if (ObjectUtil.isNotEmpty(tags)) {
                    List<Long> tagsList = Stream.of(tags.split(",")).map(Long::parseLong).toList();
                    List<TagsDO> tagsDOS = tagsMapper.selectList(TagsDO::getId, tagsList);
                    patrolPlanDO.setTagsDOS(tagsDOS);
                }
            }
            pageResult.setList(patrolPlanDOS);
        }
        return pageResult;
    }

    @Override
    public List<TagPlanDO> getTagPlanList(TagPlanExportReqVO exportReqVO) {
        return tagPlanMapper.selectList(exportReqVO);
    }

    @Override
    @TenantIgnore
    public TagPlanDO getTagPlanByJobId(Long jobId) {
        return tagPlanMapper.selectOne(TagPlanDO::getJobId, jobId);
    }

    @Override
    public void updateLastTimeAndNextTime(TagPlanDO patrolPlanDO) {
        Date lastExecutionTime = patrolPlanDO.getLastExecutionTime();
        Date nextExecutionTime = patrolPlanDO.getNextExecutionTime();
        tagPlanMapper.update(patrolPlanDO, new LambdaUpdateWrapper<TagPlanDO>()
                .eq(TagPlanDO::getId, patrolPlanDO.getId())
                .set(TagPlanDO::getLastExecutionTime, lastExecutionTime)
                .set(TagPlanDO::getNextExecutionTime, nextExecutionTime)
        );
    }

    @Override
    public void executeTagPlan(Long id) {
        TagPlanDO patrolPlan = tagPlanMapper.selectById(id);
        if (patrolPlan != null) {
            //执行巡检计划
            Date now = new Date();
            String cronExpression = patrolPlan.getExecutionCron();
            Date nextExecutionTime = getNextExecutionTime(cronExpression, now);
            //修改计划任务表中最近执行时间和下次执行时间
            patrolPlan.setLastExecutionTime(now);
            patrolPlan.setNextExecutionTime(nextExecutionTime);
            updateLastTimeAndNextTime(patrolPlan);
            if(StrUtil.isEmpty(patrolPlan.getPlatformIds())){
                String mapList = getPlatformSelectList(patrolPlan.getSysSettingTenant());
                patrolPlan.setPlatformIds(mapList);
            }
            runTagTask(patrolPlan);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startOrStopTagPlan(Long id, int status) {
        //修改状态
        tagPlanMapper.updateById(TagPlanDO.builder().id(id).status(status).build());
        //修改 job 表
        TagPlanDO patrolPlan = tagPlanMapper.selectById(id);
        TagTaskModel lulTaskModel = tagTaskService.getTaskByJobId(patrolPlan.getJobId());
        lulTaskModel.setStatus(status);
        tagTaskService.updateTasByJobId(lulTaskModel);
        if (status == 1) {
            redisPublisher.publish("tag_task", String.valueOf(patrolPlan.getJobId()));
            taskServiceInfo.removeTask(patrolPlan.getJobId());
        }
    }

    @Override
    public Long createTagRecord(TagRecordCreateReqVO createReqVO) {
        // 插入
        TagRecordDO patrolRecord = BeanUtil.copyProperties(createReqVO,TagRecordDO.class);
        tagRecordMapper.insert(patrolRecord);
        // 返回
        return patrolRecord.getId();
    }

    @Override
    public PageResult<TagRecordRespVO> getTagRecordPage(TagRecordPageReqVO pageVO) {
        PageResult<TagRecordDO> pageResult = tagRecordMapper.selectPage(pageVO);
        List<TagRecordRespVO> records = BeanUtil.copyToList(pageResult.getList(), TagRecordRespVO.class);
        if (CollectionUtil.isNotEmpty(records)) {
            records.forEach(record -> {
                Long tenantId = record.getTenantId();
                if (ObjectUtil.isNotEmpty(record.getSysSettingTenant())) {
                    tenantId = record.getSysSettingTenant();
                }
                //根据 sysConfigTenant 获取租户名称
                String tenantName = tenantApi.getTenantSimpleInfo(tenantId).getData().getName();
                record.setTenantName(tenantName);
                //根据 platformIds 获取平台名称
                String platformIds = record.getPlatformIds();
                if (ObjectUtil.isNotEmpty(platformIds)) {
                    List<Long> platformIdList = Stream.of(platformIds.split(",")).map(Long::parseLong).toList();
                    String platformName = platformconfigApi.getPlatformByIds(platformIdList).getData().stream()
                            .map(PlatformconfigDTO::getName).collect(Collectors.joining(","));
                    record.setPlatformName(platformName);
                }

                String tags = record.getTags();
                if (ObjectUtil.isNotEmpty(tags)) {
                    List<Long> tagsList = Stream.of(tags.split(",")).map(Long::parseLong).toList();
                    String platformName = tagsMapper.selectList(TagsDO::getId, tagsList).stream().map(TagsDO::getName).collect(Collectors.joining(","));
                    record.setTagsName(platformName);
                }
            });
        }
        PageResult<TagRecordRespVO> pageResult1 = new PageResult<>();
        pageResult1.setList(records);
        pageResult1.setTotal(pageResult.getTotal());
        return pageResult1;
    }

    @Override
    public void uploadWord(Long id, HttpServletResponse response) {
        //根据id 查询 巡检记录
        TagRecordDO patrolRecord = tagRecordMapper.selectById(id);
        byte[] byteBuff = fileApi.ReaderObjects(patrolRecord.getFilePath());

        try {
            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(patrolRecord.getFileName(), StandardCharsets.UTF_8));

            // 将生成的Word文档写入响应输出流
            OutputStream out = response.getOutputStream();
            out.write(byteBuff);
            out.flush();
            out.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public String getPlatformSelectList(Long sysSettingTenant) {
        return  tagPlanMapper.getPlatformSelectList(sysSettingTenant);
    }

    @Transactional(rollbackFor = Exception.class)
    public void runTagTask(TagPlanDO tagPlanDO) {
        Map<String, Object> paramMap = new HashMap<>();
        AssetReqVO reqVO = new AssetReqVO();
        paramMap.put("tagName", tagPlanDO.getName());
        paramMap.put("tagTime", new SimpleDateFormat("yyyy年MM月").format(new Date()));

        // 处理平台信息
        String platformIds = tagPlanDO.getPlatformIds();
        if (ObjectUtil.isNotEmpty(platformIds)) {
            List<Long> platformIdList = Stream.of(platformIds.split(",")).map(Long::parseLong).toList();
            String platformName = platformconfigApi.getPlatformByIds(platformIdList).getData().stream().map(PlatformconfigDTO::getName).collect(Collectors.joining(","));
            paramMap.put("tenantName", platformName);
        } else {
            paramMap.put("tenantName", "");
        }

        // 初始化表格数据
        List<RowRenderData> hostTable = new ArrayList<>();
        List<RowRenderData> hostTopTable = new ArrayList<>();
        List<RowRenderData> diskTable = new ArrayList<>();
        List<RowRenderData> diskTopTable = new ArrayList<>();

        String today = new SimpleDateFormat("yyyy/MM/HH").format(new Date());
        paramMap.put("todayTime", today);

        // 设置请求参数
        if (StrUtil.isNotBlank(tagPlanDO.getPlatformIds())) {
            reqVO.setPlatformIds(Stream.of(tagPlanDO.getPlatformIds().split(",")).map(Long::parseLong).toList());
        }
        if (StrUtil.isNotBlank(tagPlanDO.getTags())) {
            reqVO.setTags(Stream.of(tagPlanDO.getTags().split(",")).map(Long::parseLong).toList());
        }

        // 获取主机和卷数据
        List<HostInfoRespCreateReqDTO> hostList = hostInfoApi.getByPlatformIdAndTags(reqVO).getData();
        List<VolumeDTO> volumeList = volumeApi.getByPlatformIdAndTags(reqVO).getData();

        // 计算时间参数
        int numDay = Optional.ofNullable(tagPlanDO.getNumDay()).orElse(0);
        long now = System.currentTimeMillis();
        long start = numDay > 0 ? LocalDate.now().minusDays(numDay).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() : 0;
        long ago = numDay > 0 ? LocalDate.now().minusDays(numDay * 2).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() : 0;
        String step = numDay <= 3 ? "10s" : (numDay <= 7 ? "10m" : "1h");

        Map<String, List<HostInfoRespCreateReqDTO>> hostGroup = hostList.stream().collect(Collectors.groupingBy(HostInfoRespCreateReqDTO::getUuid));
        ZjMetricsDataService metricsData = SpringContextHolder.getBean(ZjMetricsDataService.class);

        String hostRiskDescription = "";
        String disksRiskDescription = "云硬盘正常,无风险";

        // 处理主机数据
        if (CollUtil.isNotEmpty(hostList)) {
            List<String> assetIds = hostList.stream().map(HostInfoRespCreateReqDTO::getUuid).distinct().toList();
            int cpuNum = hostList.stream().mapToInt(HostInfoRespCreateReqDTO::getCpuNum).sum();
            long memSum = hostList.stream().mapToLong(HostInfoRespCreateReqDTO::getMemorySize).sum();

            paramMap.put("hostNum", String.valueOf(hostList.size()));
            paramMap.put("cpuNum", String.valueOf(cpuNum));
            paramMap.put("memNum", String.valueOf(convertBytesToGB(memSum)));

            // 批量获取指标数据
            List<VictoriaMetricsDTO> currentMetrics = new ArrayList<>();
            int batchSize = 10;
            for (int i = 0; i < assetIds.size(); i += batchSize) {
                List<String> batch = assetIds.subList(i, Math.min(i + batchSize, assetIds.size()));
                currentMetrics.addAll(metricsData.getMetricData(batch, Arrays.asList("CPUUsedUtilization", "MemoryUsage"), null, start, now, step));
            }

            // 按指标和资产ID分组
            Map<String, List<VictoriaMetricsDTO>> metricMap = currentMetrics.stream().collect(Collectors.groupingBy(VictoriaMetricsDTO::getMetric));
            Map<String, List<VictoriaMetricsDTO>> topMap = currentMetrics.stream().collect(Collectors.groupingBy(m -> m.getAssetId() + "_" + m.getMetric()));

            // 处理CPU风险数据
            List<VictoriaMetricsDTO> cpuRisk = findValuesOverThreshold(metricMap.get("CPUUsedUtilization"), 90);
            paramMap.put("cpuRiskNum", cpuRisk.isEmpty() ? "/" : String.valueOf(cpuRisk.size()));
            paramMap.put("cpuRiskDescription", cpuRisk.isEmpty() ? "无风险" : "存在" + cpuRisk.size() + "台主机CPU使用率超90%");
            paramMap.put("cpuTime", formatDateFromMetrics(cpuRisk));
            String remark = "";
            if (!cpuRisk.isEmpty()) {
                remark = "经以上数据分析，存在" + cpuRisk.size() + "台云主机CPU使用率超过90%，已超系统阈值，建议排查高负载进程是否正常或进行云主机CPU扩容使CPU负载降低。";
            }

            // 处理内存风险数据
            List<VictoriaMetricsDTO> memRisk = findValuesOverThreshold(metricMap.get("MemoryUsage"), 80);
            paramMap.put("memRiskNum", memRisk.isEmpty() ? "/" : String.valueOf(memRisk.size()));
            paramMap.put("memRiskDescription", memRisk.isEmpty() ? "无风险" : "存在" + memRisk.size() + "台主机内存使用率超80%");
            paramMap.put("memTime", formatDateFromMetrics(memRisk));

            String remark1 = "云主机正常,无风险";
            if (!memRisk.isEmpty()) {
                 remark1 = "存在" + memRisk.size() + "台云主机内存使用率超过80% ，已超系统阈值，建议联系系统厂家清理缓存，扩容相关云资源，避免系统性风险，确保系统稳定。";
            }

            AtomicInteger count = new AtomicInteger(0);
            topMap.forEach((key, value) -> {
                if (key.contains("MemoryUsage")) {
                    if(step.equals("1h")){
                        if (hasStableThresholdForDuration(value, 70.0, 120)) {
                            count.incrementAndGet();
                        }
                    }else {
                        if (hasStableThresholdForDuration(value, 70.0, 30)) {
                            count.incrementAndGet();
                        }
                    }
                }
            });

            int aveRiskNum = count.get();
            String remark2 = "";
            if(aveRiskNum == 0){
                paramMap.put("avgRiskNum", "/");
                paramMap.put("avgRiskDescription", "无风险");
            }else {
                paramMap.put("avgRiskNum", aveRiskNum);
                paramMap.put("avgRiskDescription", "存在" + aveRiskNum + "台云主机CPU日均使用率超过阈值");
                remark2 = "存在" + aveRiskNum + "台云主机在过去一段时间内云主机CPU日均使用率持续超过阈值，建议进行云主机CPU扩容使CPU负载降低。";
            }
            hostRiskDescription = remark + remark1 + remark2;
            // 获取TOP5内存使用主机
            List<VictoriaMetricsDTO> top5Host = new ArrayList<>();
            topMap.entrySet().stream().filter(e -> e.getKey().contains("MemoryUsage")).forEach(e -> top5Host.addAll(sortAndLimit(e.getValue(), 1, VictoriaMetricsDTO::getValue, true)));

            List<VictoriaMetricsDTO> topMemory = sortAndLimit(top5Host, 5, VictoriaMetricsDTO::getValue, true);

            // 计算内存平均使用率和风险
            double memAvg = calculateAverage(metricMap.get("MemoryUsage"));
            String memGroupRisk = memAvg > 30.0 ? "经监控数据分析，当前有云主机内存使用率环比增长30%，此类异常增长可能导致系统卡顿、服务响应延迟甚至内存溢出风险，需立即介入处置" : "无风险";

            paramMap.put("memGrowthRate", String.valueOf(memAvg));
            paramMap.put("memGroupRisk", memGroupRisk);

            // 获取历史指标数据
            List<VictoriaMetricsDTO> pastMetrics = new ArrayList<>();
            for (int i = 0; i < assetIds.size(); i += batchSize) {
                List<String> batch = assetIds.subList(i, Math.min(i + batchSize, assetIds.size()));
                pastMetrics.addAll(metricsData.getMetricData(batch, Arrays.asList("CPUUsedUtilization", "MemoryUsage"), null, ago, start, step));
            }

            Map<String, List<VictoriaMetricsDTO>> pastMap = pastMetrics.stream().collect(Collectors.groupingBy(m -> m.getAssetId() + "_" + m.getMetric()));

            // 创建主机表格
            hostTable.add(Rows.of("TOP值", "名称", "主机IP", "内存使用率", "数据提取日期").center().bgColor("B5C6EA").create());
            hostTopTable.add(Rows.of("TOP值", "名称", "主机IP", "上月均值", "本月均值", "环比增长", "下月预测", "数据提取日期").center().bgColor("B5C6EA").create());

            // 填充主机表格数据
            for (int i = 0; i < topMemory.size(); i++) {
                VictoriaMetricsDTO dto = topMemory.get(i);
                HostInfoRespCreateReqDTO info = hostGroup.get(dto.getAssetId()).get(0);
                double curAvg = calculateAverage(topMap.get(dto.getAssetId() + "_" + dto.getMetric()));
                double pastAvg = calculateAverage(pastMap.get(dto.getAssetId() + "_" + dto.getMetric()));
                String avgStr = BigDecimal.valueOf((curAvg + pastAvg) / 2).setScale(2, RoundingMode.HALF_UP).toPlainString();
                hostTable.add(Rows.of(String.valueOf(i + 1), info.getName(), StrUtil.isNotEmpty(info.getIp()) ? info.getIp() : "-", dto.getValue() + "%", today).center().create());
                hostTopTable.add(Rows.of(String.valueOf(i + 1), info.getName(), StrUtil.isNotEmpty(info.getIp()) ? info.getIp() : "-", String.valueOf(pastAvg), String.valueOf(curAvg), compareTrend(curAvg, pastAvg), avgStr, today).center().create());
            }
        }

        // 处理硬盘数据
        if (CollUtil.isNotEmpty(volumeList)) {
            List<String> assetIds = volumeList.stream().map(VolumeDTO::getUuid).distinct().toList();
            Map<String, List<VolumeDTO>> volumeGroup = volumeList.stream().collect(Collectors.groupingBy(VolumeDTO::getUuid));
            long diskSum = volumeList.stream().mapToLong(VolumeDTO::getSize).sum();

            paramMap.put("diskNum", String.valueOf(volumeList.size()));
            paramMap.put("diskTotal", String.valueOf(convertBytesToGB(diskSum)));

            // 批量获取硬盘指标数据
            List<VictoriaMetricsDTO> diskMetrics = new ArrayList<>();
            int batchSize = 10;
            for (int i = 0; i < assetIds.size(); i += batchSize) {
                List<String> batch = assetIds.subList(i, Math.min(i + batchSize, assetIds.size()));
                diskMetrics.addAll(metricsData.getMetricData(batch, List.of("diskUse"), null, start, now, step));
            }

            // 处理硬盘风险数据
            List<VictoriaMetricsDTO> diskRisk = findValuesOverThreshold(diskMetrics, 80);
            paramMap.put("diskRiskNum", diskRisk.isEmpty() ? "/" : String.valueOf(diskRisk.size()));
            paramMap.put("diskRiskDescription", diskRisk.isEmpty() ? "无风险" : "存在" + diskRisk.size() + "台硬盘使用率超80%");

            if (!diskRisk.isEmpty()) {
                StringBuilder riskDiskNames = new StringBuilder();
                for (VictoriaMetricsDTO dto : diskRisk) {
                    riskDiskNames.append(volumeGroup.get(dto.getAssetId()).get(0).getName());
                }
                disksRiskDescription = riskDiskNames + "磁盘使用率超80%，建议关注硬盘占用率使用情况，及时清理垃圾文件，扩容占用率较高硬盘，避免数据丢失";
            }

            double diskAvg = calculateAverage(diskMetrics);
            paramMap.put("diskGrowthRate", String.valueOf(diskAvg));
            paramMap.put("diskTime", formatDateFromMetrics(diskRisk));
            paramMap.put("hostRiskDescription", hostRiskDescription);
            paramMap.put("disksRiskDescription", disksRiskDescription);

            // 获取历史硬盘指标数据
            List<VictoriaMetricsDTO> pastDisk = new ArrayList<>();
            for (int i = 0; i < assetIds.size(); i += batchSize) {
                List<String> batch = assetIds.subList(i, Math.min(i + batchSize, assetIds.size()));
                pastDisk.addAll(metricsData.getMetricData(batch, List.of("diskUse"), null, ago, start, step));
            }

            // 按资产ID分组
            Map<String, List<VictoriaMetricsDTO>> curMap = diskMetrics.stream().collect(Collectors.groupingBy(VictoriaMetricsDTO::getAssetId));
            Map<String, List<VictoriaMetricsDTO>> pastMap = pastDisk.stream().collect(Collectors.groupingBy(VictoriaMetricsDTO::getAssetId));

            // 获取TOP5硬盘使用
            List<VictoriaMetricsDTO> top5Disk = new ArrayList<>();
            curMap.forEach((key, value) -> top5Disk.addAll(sortAndLimit(value, 1, VictoriaMetricsDTO::getValue, true)));
            List<VictoriaMetricsDTO> topDisk = sortAndLimit(top5Disk, 5, VictoriaMetricsDTO::getValue, true);

            // 创建硬盘表格
            diskTable.add(Rows.of("TOP值", "云主机", "名称", "主机IP", "硬盘使用率", "数据提取日期").center().bgColor("B5C6EA").create());
            diskTopTable.add(Rows.of("TOP值", "云主机", "名称", "主机IP", "上月均值", "本月均值", "环比增长", "下月预测", "数据提取日期").center().bgColor("B5C6EA").create());

            // 填充硬盘表格数据
            for (int i = 0; i < topDisk.size(); i++) {
                VictoriaMetricsDTO dto = topDisk.get(i);
                double curAvg = calculateAverage(curMap.get(dto.getAssetId()));
                double pastAvg = calculateAverage(pastMap.get(dto.getAssetId()));

                VolumeDTO volume = volumeGroup.get(dto.getAssetId()).get(0);
                String name = volume.getName();
                String vmName = volume.getVmInstanceName();
                String vmuuid = volume.getVmInstanceUuid();

                String ip = "-";
                if (hostGroup.containsKey(vmuuid) && !hostGroup.get(vmuuid).isEmpty()) {
                    ip = StrUtil.isNotEmpty(hostGroup.get(vmuuid).get(0).getIp()) ? hostGroup.get(vmuuid).get(0).getIp() : "-";
                }

                String avgStr = BigDecimal.valueOf((curAvg + pastAvg) / 2).setScale(2, RoundingMode.HALF_UP).toPlainString();
                diskTable.add(Rows.of(String.valueOf(i + 1), vmName, name, ip, dto.getValue(), today).center().create());
                diskTopTable.add(Rows.of(String.valueOf(i + 1), vmName, name, ip, String.valueOf(pastAvg), String.valueOf(curAvg), compareTrend(curAvg, pastAvg), avgStr, today).center().create());
            }
        }

        // 只有当有主机或硬盘数据时才生成报告
        if (CollUtil.isNotEmpty(hostList) || CollUtil.isNotEmpty(volumeList)) {
            // 创建表格渲染数据
            TableRenderData hostTables = Tables.of(hostTable.toArray(new RowRenderData[0])).border(BorderStyle.DEFAULT).create();
            TableRenderData hostTopTables = Tables.of(hostTopTable.toArray(new RowRenderData[0])).border(BorderStyle.DEFAULT).create();
            TableRenderData diskTables = Tables.of(diskTable.toArray(new RowRenderData[0])).border(BorderStyle.DEFAULT).create();
            TableRenderData diskTopTables = Tables.of(diskTopTable.toArray(new RowRenderData[0])).border(BorderStyle.DEFAULT).create();

            paramMap.put("hostTable", hostTables);
            paramMap.put("hostTopTable", hostTopTables);
            paramMap.put("diskTable", diskTables);
            paramMap.put("diskTopTable", diskTopTables);

            // 生成文件名和记录
            String format = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
            String name = tagPlanDO.getName() + "_" + format + ".docx";

            // 创建记录
            TagRecordDO tagRecordDO = new TagRecordDO();
            tagRecordDO.setRecordName(tagPlanDO.getName() + "_" + format);
            tagRecordDO.setStartTime(tagPlanDO.getLastExecutionTime());
            tagRecordDO.setEndTime(new Date());
            tagRecordDO.setPlanId(tagPlanDO.getId());
            tagRecordDO.setTenantId(tagPlanDO.getTenantId());
            tagRecordDO.setSysSettingTenant(tagPlanDO.getSysSettingTenant());
            tagRecordDO.setPlatformIds(tagPlanDO.getPlatformIds());
            tagRecordDO.setFileName(name);
            tagRecordDO.setTags(tagPlanDO.getTags());

            // 创建并上传Word文档
            String filePath = createAndUploadWord(paramMap, name);
            tagRecordDO.setFilePath(filePath);
            tagRecordMapper.insert(tagRecordDO);
        }
    }

    public static List<VictoriaMetricsDTO> findValuesOverThreshold(List<VictoriaMetricsDTO> list, double threshold) {
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }
        Map<String, VictoriaMetricsDTO> uniqueMap = new LinkedHashMap<>();
        for (VictoriaMetricsDTO dto : list) {
            try {
                double value = Double.parseDouble(dto.getValue());
                if (value > threshold) {
                    uniqueMap.putIfAbsent(dto.getAssetId(), dto); // 根据 assetId 去重
                }
            } catch (Exception ignored) {
            }
        }
        return new ArrayList<>(uniqueMap.values());
    }

    public static String formatDateFromMetrics(List<VictoriaMetricsDTO> list) {
        if (list == null || list.isEmpty()) {
            return "/";
        }
        try {
            LocalDateTime dateTime = parseDateTime(list.get(0).getDateTime());
            return dateTime.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        } catch (Exception e) {
            log.warn("时间解析失败：{}", list.get(0).getDateTime(), e);
            return "/";
        }
    }

    public static double calculateAverage(List<VictoriaMetricsDTO> list) {
        if (CollUtil.isEmpty(list)) {
            return 0.0;
        }
        double average = list.stream()
                .mapToDouble(dto -> {
                    try {
                        return Double.parseDouble(dto.getValue());
                    } catch (Exception e) {
                        return 0.0;
                    }
                }).average().orElse(0.0);
        return BigDecimal.valueOf(average).setScale(2, RoundingMode.HALF_UP).doubleValue();
    }


    public static String compareTrend(double current, double previous) {
        double delta = current - previous;

        if (delta > 0) {
            return String.format("%.2f", delta);
        } else if (delta < 0) {
            return "-" + String.format("%.2f", delta);
        } else {
            return "0";
        }
    }
    public String createAndUploadWord(Map<String, Object> paramMap, String fileName) {
        try {
            String templatePath = "file-template" + File.separator + "tagTemplat.docx";
            ClassPathResource classPathResource = new ClassPathResource(templatePath);

            try (InputStream inputStream = classPathResource.getInputStream();
                 XWPFTemplate template = XWPFTemplate.compile(inputStream).render(paramMap);
                 ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                // 渲染结果写入输出流
                template.write(outputStream);
                // 写入 minio
                byte[] wordBytes = outputStream.toByteArray();
                String minioPath = StringUtil.getSavePath(fileName, FileTypeConstants.FILE_TYPE, "tag");
                return fileApi.createFileUrl(fileName, minioPath, wordBytes);
            }
        } catch (Exception e) {
            log.error("创建并上传 Word 模板出错：{}", e.getMessage(), e);
            return null;
        }
    }

    public static double convertBytesToGB(long bytes) {
        double gbValue = bytes / (1024.0 * 1024 * 1024);
        return BigDecimal.valueOf(gbValue)
                .setScale(2, RoundingMode.HALF_UP)
                .doubleValue();
    }

    public static boolean hasStableThresholdForDuration(List<VictoriaMetricsDTO> metrics, double threshold, long requiredMinutes) {
        if (metrics == null || metrics.isEmpty()) return false;
        metrics.sort(Comparator.comparing(m -> parseDateTime(m.getDateTime())));
        LocalDateTime blockStart = null;
        LocalDateTime blockEnd = null;

        for (VictoriaMetricsDTO dto : metrics) {
            try {
                double value = Double.parseDouble(dto.getValue());
                LocalDateTime currentTime = parseDateTime(dto.getDateTime());
                if (value > threshold) {
                    if (blockStart == null) {
                        blockStart = currentTime;
                    }
                    blockEnd = currentTime;

                    long spanMinutes = Duration.between(blockStart, blockEnd).toMinutes();
                    if (spanMinutes >= requiredMinutes) {
                        return true;
                    }
                } else {
                    // 中断则重置起止时间
                    blockStart = null;
                    blockEnd = null;
                }
            } catch (Exception e) {
                blockStart = null;
                blockEnd = null;
            }
        }

        return false;
    }

    public static LocalDateTime parseDateTime(String timestampStr) {
        long timestamp = Long.parseLong(timestampStr);
        return Instant.ofEpochSecond(timestamp).atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

}
