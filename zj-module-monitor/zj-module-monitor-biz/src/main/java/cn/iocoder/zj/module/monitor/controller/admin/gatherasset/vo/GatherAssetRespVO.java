package cn.iocoder.zj.module.monitor.controller.admin.gatherasset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Date;

@Schema(description = "管理后台 - 租户资产 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GatherAssetRespVO extends GatherAssetBaseVO {

    @Schema(description = "主键", required = true)
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
