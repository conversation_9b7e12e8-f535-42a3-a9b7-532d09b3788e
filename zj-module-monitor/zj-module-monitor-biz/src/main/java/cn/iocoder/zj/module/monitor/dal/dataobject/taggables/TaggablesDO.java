package cn.iocoder.zj.module.monitor.dal.dataobject.taggables;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 标签绑定关系 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_taggables")
@KeySequence("monitor_taggables_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaggablesDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 标签id
     */
    private Long tagId;
    /**
     * 绑定资产id 
     */
    private Long taggableId;
    /**
     * 绑定资产类型
     */
    private String taggableType;

    private Long tenantId;

    private Integer type;

    @TableField(exist = false)
    private String tagUuid;

    @TableField(exist = false)
    private String tagName;
}
