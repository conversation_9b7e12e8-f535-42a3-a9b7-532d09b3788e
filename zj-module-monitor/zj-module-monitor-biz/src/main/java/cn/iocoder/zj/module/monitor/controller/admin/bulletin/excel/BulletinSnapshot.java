package cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel;

import cn.iocoder.zj.framework.excel.core.convert.ByteConvent;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.convert.BulletinBoolConvert;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.convert.BulletinEnableStatusConvert;
import cn.iocoder.zj.module.monitor.controller.admin.tags.vo.TagsRespVO;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@ExcelIgnoreUnannotated
public class BulletinSnapshot {
    private Long id;

    private String uuid;

    @ExcelProperty("快照名称")
    private String name;

    @ExcelProperty("平台名称")
    private String platformName;

    @ExcelProperty(value = "状态", converter = BulletinEnableStatusConvert.class)
    private String status;

    @ExcelProperty("快照类型")
    private String type;

    @ExcelProperty(value = "容量", converter = ByteConvent.class)
    private Long size;

    @ExcelProperty(value = "包含内存快照", converter = BulletinBoolConvert.class)
    private Boolean isMemory;

    @ExcelProperty("快照格式")
    private String format;

    @ExcelProperty("标签")
    private String tagsStr;

    @ExcelProperty("云盘类型")
    private String volumeType;

    @ExcelProperty("云盘快照安装路径")
    private String installPath;

    @Schema(description = "标签信息")
    private List<TagsRespVO> tags;


}
