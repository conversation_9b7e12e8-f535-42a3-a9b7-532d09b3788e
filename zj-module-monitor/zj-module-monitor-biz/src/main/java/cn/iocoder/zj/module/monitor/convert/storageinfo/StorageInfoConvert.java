package cn.iocoder.zj.module.monitor.convert.storageinfo;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.api.storage.dto.StorageRespCreateReqDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.storageinfo.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.storageinfo.StorageInfoDO;

/**
 * 存储设备信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface StorageInfoConvert {

    StorageInfoConvert INSTANCE = Mappers.getMapper(StorageInfoConvert.class);

    StorageInfoDO convert(StorageInfoCreateReqVO bean);

    StorageInfoDO convert(StorageInfoUpdateReqVO bean);

    StorageInfoRespVO convert(StorageInfoDO bean);

    List<StorageInfoRespVO> convertList(List<StorageInfoDO> list);

    PageResult<StorageInfoRespVO> convertPage(PageResult<StorageInfoDO> page);

    List<StorageInfoExcelVO> convertList02(List<StorageInfoDO> list);

    List<StorageInfoDO> convertCreateList(List<StorageRespCreateReqDTO> reqDTO);

    List<StorageRespCreateReqDTO> convertListDoToDto(List<StorageInfoDO> all);
}
