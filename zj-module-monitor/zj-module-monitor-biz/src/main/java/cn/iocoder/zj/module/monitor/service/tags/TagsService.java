package cn.iocoder.zj.module.monitor.service.tags;

import java.util.*;
import javax.validation.*;

import cn.iocoder.zj.module.monitor.controller.admin.taggables.vo.TaggablesExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.tags.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.tags.TagsDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 标签 Service 接口
 *
 * <AUTHOR>
 */
public interface TagsService {

    /**
     * 创建标签
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTags(@Valid TagsCreateReqVO createReqVO);

    /**
     * 更新标签
     *
     * @param updateReqVO 更新信息
     */
    void updateTags(@Valid TagsUpdateReqVO updateReqVO);

    /**
     * 删除标签
     *
     * @param id 编号
     */
    void deleteTags(Long id);

    /**
     * 获得标签
     *
     * @param id 编号
     * @return 标签
     */
    TagsDO getTags(Long id);

    /**
     * 获得标签列表
     *
     * @param ids 编号
     * @return 标签列表
     */
    List<TagsDO> getTagsList(Collection<Long> ids);

    /**
     * 获得标签分页
     *
     * @param pageReqVO 分页查询
     * @return 标签分页
     */
    PageResult<TagsDO> getTagsPage(TagsPageReqVO pageReqVO);

    /**
     * 获得标签列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 标签列表
     */
    List<TagsDO> getTagsList(TagsExportReqVO exportReqVO);

    List<TagsDO> getTagListByTagGables(TaggablesExportReqVO exportReqVO);
}
