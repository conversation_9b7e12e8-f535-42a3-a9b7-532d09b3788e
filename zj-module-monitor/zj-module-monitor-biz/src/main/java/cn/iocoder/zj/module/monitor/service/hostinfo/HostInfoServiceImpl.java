package cn.iocoder.zj.module.monitor.service.hostinfo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.enums.ResourceCategoryEnum;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import cn.iocoder.zj.module.monitor.api.cloud.dto.CloudRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.controller.admin.hardwareinfo.vo.HardwareInfoPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.hostinfo.vo.*;
import cn.iocoder.zj.module.monitor.controller.admin.storageinfo.vo.StorageInfoPageReqVO;
import cn.iocoder.zj.module.monitor.convert.hostinfo.HostInfoConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.hardwareinfo.HardwareInfoDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostinfo.HostInfoDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.monitorauthorization.MonitorAuthorizationDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.monitorauthorizationuser.MonitorAuthorizationUserDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.storageinfo.StorageInfoDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.tags.TagsDO;
import cn.iocoder.zj.module.monitor.dal.mysql.hostinfo.HostInfoMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.monitorauthorization.MonitorAuthMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.monitorauthorizationuser.MonitorAuthUserMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.tags.TagsMapper;
import cn.iocoder.zj.module.monitor.dal.redis.zstack.LabelRedisDAO;
import cn.iocoder.zj.module.monitor.dal.redis.zstack.ZstackAccessTokenRedisDAO;
import cn.iocoder.zj.module.monitor.pojo.AssetReqVO;
import cn.iocoder.zj.module.monitor.service.hardwareinfo.HardwareInfoService;
import cn.iocoder.zj.module.monitor.service.storageinfo.StorageInfoService;
import cn.iocoder.zj.module.monitor.service.topology.TopologyService;
import cn.iocoder.zj.module.monitor.util.StringUtil;
import cn.iocoder.zj.module.system.api.dict.DictDataApi;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.zj.module.system.api.wechat.WeChatSendApi;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.data.ChartMultiSeriesRenderData;
import com.deepoove.poi.data.ChartSingleSeriesRenderData;
import com.deepoove.poi.data.Charts;
import com.deepoove.poi.data.SeriesRenderData;
import com.deepoove.poi.policy.reference.MultiSeriesChartTemplateRenderPolicy;
import com.googlecode.aviator.utils.ArrayHashMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.apache.poi.xddf.usermodel.chart.ChartTypes;
import org.apache.poi.xddf.usermodel.chart.XDDFLineChartData;
import org.apache.poi.xwpf.usermodel.XWPFChart;
import org.apache.xmlbeans.XmlException;
import org.frameworkset.elasticsearch.ElasticSearchHelper;
import org.frameworkset.elasticsearch.client.ClientInterface;
import org.frameworkset.elasticsearch.entity.ESDatas;
import org.frameworkset.util.CollectionUtils;
import org.frameworkset.util.io.ClassPathResource;
import org.openxmlformats.schemas.drawingml.x2006.chart.*;
import org.openxmlformats.schemas.drawingml.x2006.main.CTLineProperties;
import org.openxmlformats.schemas.drawingml.x2006.main.STPresetLineDashVal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.singleton;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.HOST_INFO_NOT_EXISTS;

/**
 * 云主机基本信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class HostInfoServiceImpl implements HostInfoService {

    @Resource
    ZstackAccessTokenRedisDAO zstackAccessTokenRedisDAO;

    @Resource
    LabelRedisDAO labelRedisDAO;
    //    @Resource
//    InfluxDBTemplate influxDBTemplate;
    @Resource
    private HardwareInfoService hardwareInfoService;
    @Resource
    private StorageInfoService storageInfoService;
    @Resource
    private HostInfoMapper hostInfoMapper;

    @Resource
    private RoleApi roleApi;

    @Resource
    private PermissionApi permissionApi;
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    TopologyService topologyService;

    @Resource
    private AdminUserApi adminUserService;
    @Resource
    DictDataApi dictDataApi;
    @Resource
    private MonitorAuthMapper monitorAuthorizationMapper;
    @Resource
    MonitorAuthUserMapper monitorAuthorizationUserMapper;

    @Resource
    private TagsMapper tagsMapper;

    @Autowired
    WeChatSendApi weChatSendApi;

    @Value("${accessing-domain.url}")
    private String address;

    @Override
    public Long createHostInfo(HostInfoCreateReqVO createReqVO) {
        // 插入
        HostInfoDO hostInfo = HostInfoConvert.INSTANCE.convert(createReqVO);
        hostInfoMapper.insert(hostInfo);
        // 返回
        return hostInfo.getId();
    }

    @Override
    public void updateHostInfo(HostInfoUpdateReqVO updateReqVO) {
        // 校验存在
        validateHostInfoExists(updateReqVO.getId());
        // 更新
        HostInfoDO updateObj = HostInfoConvert.INSTANCE.convert(updateReqVO);
        hostInfoMapper.updateById(updateObj);
    }

    @Override
    public void deleteHostInfo(Long id) {
        // 校验存在
        validateHostInfoExists(id);
        String uuid = hostInfoMapper.selectById(id).getUuid();
        // 删除
        hostInfoMapper.deleteById(id);
        // 同步删除拓扑图中的信息
        topologyService.deleteAssetAndHostJson(uuid, "host");
    }

    private void validateHostInfoExists(Long id) {
        if (hostInfoMapper.selectById(id) == null) {
            throw exception(HOST_INFO_NOT_EXISTS);
        }
    }

    @Override
    public HostInfoDO getHostInfo(String uuid) {
        HostInfoDO host = hostInfoMapper.selectOne("uuid", uuid);
        Date vCreateTime = host.getVCreateDate();
        host.setCreateTime(DateUtil.toLocalDateTime(vCreateTime));
        return host;
    }

    @Override
    public List<HostInfoDO> getHostInfoList(Collection<Long> ids) {
        return hostInfoMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<HostInfoDO> getHostInfoPage(HostInfoPageReqVO pageReqVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        AdminUserRespDTO adminUserRespDTO = adminUserService.getUser(loginUser.getId()).getData();
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            if (StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
                pageReqVO.setTenantId(Arrays.asList(String.valueOf(adminUserRespDTO.getTenantId()).split(",")));
            } else {
                pageReqVO.setTenantId(Arrays.asList(adminUserRespDTO.getServiceTenantId().split(",")));
            }
        }
        PageParam pageParam = new PageParam().setPageNo(pageReqVO.getPageNo()).setPageSize(pageReqVO.getPageSize());
        IPage<HostInfoDO> mpPage = MyBatisUtils.buildPage(pageParam);
        List<String> uids = new ArrayList<>();
        if (StringUtil.isNotEmpty(pageReqVO.getIds())) {
            uids = Arrays.asList(pageReqVO.getIds().split(","));
        }
        if (jodd.util.StringUtil.isNotEmpty(pageReqVO.getStartTime()) && jodd.util.StringUtil.isNotEmpty(pageReqVO.getEndTime())) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            try {
                // 如果日期格式不包含时分秒,则添加 00:00:00
                String endTime = pageReqVO.getEndTime();
                if (!endTime.contains(":")) {
                    endTime += " 00:00:00";
                }
                date = sdf.parse(endTime);

                Calendar calendar = new GregorianCalendar();
                calendar.setTime(date);
                // 把日期设置为当天最后一秒
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                date = calendar.getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            pageReqVO.setEndTime(sdf.format(date));
        }
        if (StringUtil.isNotEmpty(pageReqVO.getSortBy())) {
            String regex = "([a-z])([A-Z]+)";
            String replacement = "$1_$2";
            String output = pageReqVO.getSortBy().replaceAll(regex, replacement).toLowerCase();
            pageReqVO.setSortBy(output);
            if (pageReqVO.getSortBy().equals("vcreate_date")) {
                pageReqVO.setSortBy("v_create_date");
            }
        }
        return new PageResult<>(hostInfoMapper.getHostInfoPage(mpPage, pageReqVO, uids), mpPage.getTotal());

    }

    @Override
    public List<HostInfoDO> getExportInfoList(HostInfoExportReqVO exportReqVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        AdminUserRespDTO adminUserRespDTO = adminUserService.getUser(loginUser.getId()).getData();
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            if (StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
                exportReqVO.setTenantId(Arrays.asList(String.valueOf(adminUserRespDTO.getTenantId()).split(",")));
            } else {
                exportReqVO.setTenantId(Arrays.asList(adminUserRespDTO.getServiceTenantId().split(",")));
            }
        }
        if (jodd.util.StringUtil.isNotEmpty(exportReqVO.getStartTime()) && jodd.util.StringUtil.isNotEmpty(exportReqVO.getEndTime())) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            try {
                date = sdf.parse(exportReqVO.getEndTime());
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(date);
                // 把日期设置为当天最后一秒
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                date = calendar.getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            exportReqVO.setEndTime(sdf.format(date));
        }
        return hostInfoMapper.getExportInfoList(exportReqVO);
    }

    public void createHostInfoList(Collection<HostInfoDO> reqDTO) {
        hostInfoMapper.insertBatch(reqDTO);
    }

    @Override
    public int getCount(String typeName) {
        return hostInfoMapper.getCount(typeName);
    }

    @Override
    public void updateHostInfoList(List<HostInfoRpcVO> list) {
        hostInfoMapper.updateHostInfoList(list);
    }

    public List<HostInfoDO> getAll(String typeName) {
        return hostInfoMapper.selectList(HostInfoDO::getTypeName, typeName);
    }

    @Override
    public List<Map> getHostCpuInfoService(Long id, String uuid, String timeStr) {
        ClientInterface clientUtil = ElasticSearchHelper.getConfigRestClientUtil("esmapper/ESCloudMapper.xml");
        Map searchFields = new HashMap<>();
        searchFields.put("passTime", "now-" + timeStr);
        searchFields.put("vmuuid", uuid);
        ESDatas<Map> data = clientUtil.searchList("cloud_cpu_" + id + "/_search", "cloudInfo", searchFields, Map.class);
        return data.getDatas();
    }

    @Override
    public List<Map> getHostMemoryInfo(Long id, String uuid, String timeStr) {
        ClientInterface clientUtil = ElasticSearchHelper.getConfigRestClientUtil("esmapper/ESCloudMapper.xml");
        Map searchFields = new HashMap<>();
        searchFields.put("passTime", "now-" + timeStr);
        searchFields.put("vmuuid", uuid);
        ESDatas<Map> data = clientUtil.searchList("cloud_memory_" + id + "/_search", "cloudInfo", searchFields, Map.class);
        return data.getDatas();
    }

    @Override
    public List<Map> getHostNetworkInfo(Long id, String uuid, String timeStr) {
        ClientInterface clientUtil = ElasticSearchHelper.getConfigRestClientUtil("esmapper/ESCloudMapper.xml");
        Map searchFields = new HashMap<>();
        searchFields.put("passTime", "now-" + timeStr);
        searchFields.put("vmuuid", uuid);
        ESDatas<Map> data = clientUtil.searchList("cloud_network_" + id + "/_search", "cloudInfo", searchFields, Map.class);
        return data.getDatas();
    }

    @Override
    public List<Map> getHostDiskInfo(Long id, String uuid, String timeStr) {
        ClientInterface clientUtil = ElasticSearchHelper.getConfigRestClientUtil("esmapper/ESCloudMapper.xml");
        Map searchFields = new HashMap<>();
        searchFields.put("passTime", "now-" + timeStr);
        searchFields.put("vmuuid", uuid);
        ESDatas<Map> data = clientUtil.searchList("cloud_disk_" + id + "/_search", "cloudInfo", searchFields, Map.class);
        return data.getDatas();
    }

    @Override
    public List<Map<String, Object>> getLabel(Long platformId, String uuid, int labelKey) {

//        PlatformconfigDTO platformconfigDTO = platformconfigApi.getByConfigId(platformId).getData();
//        List<Map<String, Object>> d = new ArrayList<>();
//        if (platformconfigDTO == null) {
//            return d;
//        }
//        if (platformconfigDTO.getTypeCode().equals("zstack")) {
//            if (labelRedisDAO.getVm("platromId:" + platformId + ":uuid:" + uuid + ":labelKey:" + labelKey) == null) {
//                List<Map<String, Object>> list = new ArrayList<>();
//                Map<String, Object> map = new HashMap<>();
//                String token = zstackAccessTokenRedisDAO.get("zstack:" + platformId).getUuid();
//                String url = zstackAccessTokenRedisDAO.get("zstack:" + platformId).getUrl();
//                IdentityHashMap<String, Object> p = new IdentityHashMap<>();
//
//                // 设置共同的参数
//                p.put("filterLabels", "VMUuid=" + uuid);
//                // 根据labelKey设置不同的参数和类型
//                if (labelKey == 1) {
//                    p.put("labelNames", "CPUNum");
//                    p.put("namespace", "ZStack/VM");
//                    p.put("metricName", "CPUUsedUtilization");
//                } else if (labelKey == 2) {
//                    p.put("labelNames", "DiskDeviceLetter");
//                    p.put("namespace", "ZStack/VM");
//                    p.put("metricName", "DiskReadBytes");
//                } else if (labelKey == 3) {
//                    p.put("labelNames", "NetworkDeviceLetter");
//                    p.put("namespace", "ZStack/VM");
//                    p.put("metricName", "NetworkOutBytes");
//                }
//
//                JSONArray labels;
//                try (HttpResponse result = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_LABEL)
//                        .header(Header.AUTHORIZATION, "OAuth " + token)
//                        .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
//                        .form(p)
//                        .execute()) {
//
//                    if (result.getStatus() != 200) {
//                        throw new RuntimeException("getCpuLabel error");
//                    }
//                    labels = JSONObject.parseObject(result.body()).getJSONArray("labels");
//                }
//                for (int i = 0; i < labels.size(); i++) {
//                    Map<String, Object> maps = new HashMap<>();
//                    JSONObject jsonObject = labels.getJSONObject(i);
//                    if (labelKey == 1) {
//                        maps.put("type", jsonObject.get("CPUNum"));
//                    } else if (labelKey == 2) {
//                        maps.put("type", jsonObject.get("DiskDeviceLetter"));
//                    } else if (labelKey == 3) {
//                        maps.put("type", jsonObject.get("NetworkDeviceLetter"));
//                    }
//                    list.add(maps);
//                }
//                if (labelKey == 1) {
//                    d = list.stream().sorted(Comparator.comparing(map1 ->
//                            StringUtil.toInt(map1.get("type")))).collect(Collectors.toList());
//                } else {
//                    d = list.stream().sorted(Comparator.comparing(map1 ->
//                            StringUtil.toString(map1.get("type")))).collect(Collectors.toList());
//                }
//                labelRedisDAO.setVm("platromId:" + platformId + ":uuid:" + uuid + ":labelKey:" + labelKey, d);
//            }
//            if (labelRedisDAO.getVm("platromId:" + platformId + ":uuid:" + uuid + ":labelKey:" + labelKey) == null) {
//                return new ArrayList<>();
//            } else {
//                return labelRedisDAO.getVm("platromId:" + platformId + ":uuid:" + uuid + ":labelKey:" + labelKey);
//            }
//        } else {
//            if (labelRedisDAO.getVm("platromId:" + platformId + ":uuid:" + uuid + ":labelKey:" + labelKey) == null) {
//                if (labelKey == 1) {
//                    String query = "SELECT DISTINCT(type) as type from zj_cloud_host WHERE uuid = '" + uuid + "' and label = 'cpu' AND time >= now() - 20m and type !='CPUWaitUtilization' and type !='CPUAllUsedUtilization' and type !='' and metricName != 'CPUIdleUtilization'";
//                    List<Object> records = influxDBTemplate.fetchRecords(query);
//                    for (Object o : records) {
//                        Map map = new HashMap();
//                        map.put("type", ((Map) o).get("type"));
//
//                        d.add(map);
//                    }
//
//                } else if (labelKey == 2) {
//                    String query = "SELECT DISTINCT(type) as type from zj_cloud_host WHERE uuid = '" + uuid + "' and label = 'disk' AND time >= now() - 20m and type !='DiskAllReadBytes' and type !='DiskAllWriteBytes' and type != 'disk' and type != 'DiskAllUsedCapacityInBytes' and type != 'DiskAllUsedCapacityInPercent' and type != 'DiskZStackUsedCapacityInPercent' and type != 'DiskZStackUsedCapacityInBytes'";
//                    List<Object> records = influxDBTemplate.fetchRecords(query);
//                    for (Object o : records) {
//                        Map map = new HashMap();
//                        map.put("type", ((Map) o).get("type"));
//                        d.add(map);
//                    }
//                } else if (labelKey == 3) {
//                    String query = "SELECT DISTINCT(type) as type from zj_cloud_host WHERE uuid = '" + uuid + "' and label = 'net' AND time >= now() - 20m and type !='NetworkAllInBytes' and type !='NetworkAllOutBytes'";
//                    List<Object> records = influxDBTemplate.fetchRecords(query);
//                    for (Object o : records) {
//                        Map map = new HashMap();
//                        map.put("type", ((Map) o).get("type"));
//                        d.add(map);
//                    }
//                }
//
//                if (labelKey == 1) {
//                    Collections.sort(d, new Comparator<Map<String, Object>>() {
//                        @Override
//                        public int compare(Map<String, Object> m1, Map<String, Object> m2) {
//                            // 在尝试转换之前检查字符串，将空字符串视为"0"
//                            String typeStr1 = StringUtil.toString(m1.get("type"));
//                            String typeStr2 = StringUtil.toString(m2.get("type"));
//
//                            // 检查是否为空或空白字符串，如果是，则默认为"0"
//                            Integer type1 = typeStr1 == null || typeStr1.equals("all") || typeStr1.trim().isEmpty() ? 0 : Integer.parseInt(typeStr1);
//                            Integer type2 = typeStr2 == null || typeStr1.equals("all") || typeStr2.trim().isEmpty() ? 0 : Integer.parseInt(typeStr2);
//
//                            return type1.compareTo(type2);
//                        }
//                    });
//                }
//                if (!d.isEmpty()) {
//                    labelRedisDAO.setVm("platromId:" + platformId + ":uuid:" + uuid + ":labelKey:" + labelKey, d);
//                }
//            }
//            if (labelRedisDAO.getVm("platromId:" + platformId + ":uuid:" + uuid + ":labelKey:" + labelKey) == null) {
//                return new ArrayList<>();
//            } else {
//                return labelRedisDAO.getVm("platromId:" + platformId + ":uuid:" + uuid + ":labelKey:" + labelKey);
//            }
//
//        }
        return new ArrayList<>();
    }

    @Override
    public Map<String, Object> getHostStatusCount(List<String> tenantIds, Long platformId) {
        Map<String, Object> hostStatusCount = hostInfoMapper.getHostStatusCount(tenantIds, platformId);
        if (hostStatusCount.size() == 1) {
            hostStatusCount = new HashMap<>();
            hostStatusCount.put("hostRunningNum", 0);
            hostStatusCount.put("hostStoppedNum", 0);
            hostStatusCount.put("hostOtherNum", 0);
            hostStatusCount.put("hostNum", 0);
        }
        return hostStatusCount;
    }

    @Override
    public List<Map<String, Object>> getCpuTop(List<String> tenantIds, Long platformId, Long regionId, int top) {
        if (regionId != null && regionId.longValue() != 0) {
            if (regionId.toString().substring(2, 6).equals("0000")) {
                regionId = Long.parseLong(regionId.toString().substring(0, 2));
            } else if (regionId.toString().substring(4, 6).equals("00")) {
                regionId = Long.parseLong(regionId.toString().substring(0, 4));
            }
        }
        return hostInfoMapper.getCpuTop(tenantIds, platformId, regionId, top);
    }

    @Override
    public List<Map<String, Object>> getMemoryTop(List<String> tenantIds, Long platformId, Long regionId, int top) {
        if (regionId != null && regionId.longValue() != 0) {
            if (regionId.toString().substring(2, 6).equals("0000")) {
                regionId = Long.parseLong(regionId.toString().substring(0, 2));
            } else if (regionId.toString().substring(4, 6).equals("00")) {
                regionId = Long.parseLong(regionId.toString().substring(0, 4));
            }
        }
        return hostInfoMapper.getMemoryTop(tenantIds, platformId, regionId, top);
    }

    public List<String> selectTenantHostUUID(String tenantId) {
        return hostInfoMapper.selectTenantHostUUID(tenantId);
    }

    @Override
    public List<MonitorHostinfoDataRespVO> getHostGraphData(MonitorHostinfoDataReqVo reqVo) {
//
//        Long period = reqVo.getEndTime() - reqVo.getStartTime();
//        List<MonitorHostinfoDataRespVO> hostGraphDatas = new ArrayList<>();
//        if (StringUtil.isNullOrEmpty(reqVo.getMetricName()) && StringUtil.isNullOrEmpty(reqVo.getVmType())) {
//            return hostGraphDatas;
//        }
//        String startTime = DateUtil.format(new Date(reqVo.getStartTime()), "yyyy-MM-dd HH:mm:ss");
//        String enTime = DateUtil.format(new Date(reqVo.getEndTime()), "yyyy-MM-dd HH:mm:ss");
//        String mTime = DateUtil.format(DateUtil.beginOfDay(new Date(reqVo.getStartTime())), "yyyy-MM-dd HH:mm:ss");
//        String timeGroup = "";
//        if (period > 3600000 && period <= 21600000) {
//            timeGroup = "group by time(2m)";
//        } else if (period > 21600000 && period <= 86400000) {
//            timeGroup = "group by time(8m)";
//        } else if (period > 86400000 && period <= 604800000) {
//            timeGroup = "group by time(56m)";
//        } else if (period > 604800000 && period <= 2592000000L) {
//            timeGroup = "group by time(2h)";
//        } else if (period > 2592000000L) {
//            timeGroup = "group by time(3h)";
//        } else if (period <= 3600000) {
//            timeGroup = " group by time(20s)";
//        }
//        List<String> metricNames = new ArrayList<String>(Arrays.asList(reqVo.getMetricName().split(",")));
//        List<String> hostTypes = new ArrayList<String>(Arrays.asList(reqVo.getVmType().split(",")));
//        if (reqVo.getVmType().contains("CPUAverageUsedUtilization")) {
//            metricNames.removeIf(s -> s.equals("CPUAverageUsedUtilization"));
//            hostTypes.removeIf(s -> s.equals("CPUAverageUsedUtilization"));
//            String query = "SELECT FIRST(value) as value from zj_cloud_host WHERE uuid = '" + reqVo.getUuid() +
//                    "' and metricName = 'CPUAverageUsedUtilization' and type = 'CPUAverageUsedUtilization'" +
//                    " and time>= '" + startTime + "' and time<= '" + enTime + "'" + timeGroup;
//            query = query + " fill(previous) ";
//            List<Object> records = influxDBTemplate.fetchRecords(query);
//            List<MonitorHostinfoDataRespVO> result = JSONArray.parseArray(JSON.toJSONString(records), MonitorHostinfoDataRespVO.class);
//            if (result.isEmpty() || result.get(0).getValue() == null) {
//                String lastQuery = "SELECT LAST(value) as value from zj_cloud_host WHERE uuid = '" + reqVo.getUuid() +
//                        "' and metricName = 'CPUAverageUsedUtilization' and type = 'CPUAverageUsedUtilization'" +
//                        " and time>= '" + mTime + "' and time < '" + startTime + "'";
//                List<Object> lastRecords = influxDBTemplate.fetchRecords(lastQuery);
//                if (!lastRecords.isEmpty()) {
//                    MonitorHostinfoDataRespVO lastDataPoint = JSONArray.parseArray(JSON.toJSONString(lastRecords), MonitorHostinfoDataRespVO.class).get(0);
//                    if (!result.isEmpty()) {
//                        result.get(0).setValue(lastDataPoint.getValue());
//                    }
//                }
//            }
//            result.forEach(item -> item.setType("CPUAverageUsedUtilization").setVm_metricName("CPUAverageUsedUtilization"));
//            Iterator<MonitorHostinfoDataRespVO> iterator = result.iterator();
//            while (iterator.hasNext()) {
//                MonitorHostinfoDataRespVO result1 = iterator.next();
//                if (result1.getValue() == null) {
//                    iterator.remove(); // 如果值为 null，移除该对象
//                } else if (result1.getValue() < 0) {
//                    result1.setValue(0.0); // 如果值为负数，将其值修改为 0
//                }
//            }
//            hostGraphDatas.addAll(result);
//        }
//        for (String metricName : metricNames) {
//            if (hostTypes.size() > 0 && !hostTypes.get(0).equals("")) {
//                for (String vmType : hostTypes) {
//                    String query = "SELECT FIRST(value) as value from zj_cloud_host WHERE uuid = '" + reqVo.getUuid() + "' and metricName = '" + metricName;
//                    query = query + "' and type = '" + vmType;
//                    query = query + "' and time>= '" + startTime + "' and time<= '" + enTime + "'" + timeGroup;
//                    query = query + " fill(previous) ";
//                    List<Object> records = influxDBTemplate.fetchRecords(query);
//                    List<MonitorHostinfoDataRespVO> result = JSONArray.parseArray(JSON.toJSONString(records), MonitorHostinfoDataRespVO.class);
//                    if (result.isEmpty() || result.get(0).getValue() == null) {
//                        String lastQuery = "SELECT LAST(value) as value from zj_cloud_host WHERE uuid = '" + reqVo.getUuid() +
//                                "' and metricName = '" + metricName + "' and type = '" + vmType +
//                                "' and time>= '" + mTime + "' and time < '" + startTime + "'";
//                        List<Object> lastRecords = influxDBTemplate.fetchRecords(lastQuery);
//                        if (!lastRecords.isEmpty()) {
//                            MonitorHostinfoDataRespVO lastDataPoint = JSONArray.parseArray(JSON.toJSONString(lastRecords), MonitorHostinfoDataRespVO.class).get(0);
//                            if (!result.isEmpty()) {
//                                result.get(0).setValue(lastDataPoint.getValue());
//                            }
//                        }
//                    }
//                    result.forEach(item -> item.setType(vmType).setVm_metricName(metricName));
//                    Iterator<MonitorHostinfoDataRespVO> iterator = result.iterator();
//                    while (iterator.hasNext()) {
//                        MonitorHostinfoDataRespVO result1 = iterator.next();
//                        if (result1.getValue() == null) {
//                            iterator.remove(); // 如果值为 null，移除该对象
//                        } else if (result1.getValue() < 0) {
//                            result1.setValue(0.0); // 如果值为负数，将其值修改为 0
//                        }
//                    }
//                    hostGraphDatas.addAll(result);
//                }
//            } else {
//                String query = "SELECT FIRST(value) as value from zj_cloud_host WHERE uuid = '" + reqVo.getUuid() + "' and metricName = '" + metricName;
//                query = query + "' and time>= '" + startTime + "' and time<= '" + enTime + "'" + timeGroup;
//                query = query + " fill(previous) ";
//                List<Object> records = influxDBTemplate.fetchRecords(query);
//                List<MonitorHostinfoDataRespVO> result = JSONArray.parseArray(JSON.toJSONString(records), MonitorHostinfoDataRespVO.class);
//                if (result.isEmpty() || result.get(0).getValue() == null) {
//                    String lastQuery = "SELECT LAST(value) as value from zj_cloud_host WHERE uuid = '" + reqVo.getUuid() +
//                            "' and metricName = '" + metricName + "' and time>= '" + mTime + "' and time < '" + startTime + "'";
//                    List<Object> lastRecords = influxDBTemplate.fetchRecords(lastQuery);
//                    if (!lastRecords.isEmpty()) {
//                        MonitorHostinfoDataRespVO lastDataPoint = JSONArray.parseArray(JSON.toJSONString(lastRecords), MonitorHostinfoDataRespVO.class).get(0);
//                        if (!result.isEmpty()) {
//                            result.get(0).setValue(lastDataPoint.getValue());
//                        }
//                    }
//                }
//                result.forEach(item -> item.setType(metricName).setVm_metricName(metricName));
//                Iterator<MonitorHostinfoDataRespVO> iterator = result.iterator();
//                while (iterator.hasNext()) {
//                    MonitorHostinfoDataRespVO result1 = iterator.next();
//                    if (result1.getValue() == null) {
//                        iterator.remove(); // 如果值为 null，移除该对象
//                    } else if (result1.getValue() < 0) {
//                        result1.setValue(0.0); // 如果值为负数，将其值修改为 0
//                    }
//                }
//                hostGraphDatas.addAll(result);
//            }
//        }
//        return hostGraphDatas.stream()
//                .sorted((data1, data2) -> data1.getTime().compareTo(data2.getTime()))
//                .collect(Collectors.toList());
        return new ArrayList<>();
    }


    @Override
    public List<MonitorHostinfoDataRespVO> getHostGraphDataRandom(MonitorHostinfoDataReqVo reqVo) {
//        Long period = reqVo.getEndTime() - reqVo.getStartTime();
//        List<MonitorHostinfoDataRespVO> hostGraphDatas = new ArrayList<>();
//        if (StringUtil.isNullOrEmpty(reqVo.getMetricName()) && StringUtil.isNullOrEmpty(reqVo.getVmType())) {
//            return hostGraphDatas;
//        }
//        HostInfoDO hostInfoDO = hostInfoMapper.selectOne("uuid", reqVo.getUuid());
//        List<String> metricNames = Arrays.asList(reqVo.getMetricName().split(","));
//        List<String> hostTypes = Arrays.asList(reqVo.getVmType().split(","));
//        int sampleNum;
//        if (hostTypes.size() == 1 && StringUtil.isNullOrEmpty(hostTypes.get(0))) {
//            sampleNum = metricNames.size();
//        } else {
//            sampleNum = hostTypes.size();
//        }
//        String query = "SELECT SAMPLE(value," + sampleNum + ") as value,type,metricName as  vm_metricName from zj_cloud_host WHERE WHERE uuid = '" + reqVo.getUuid() + "' and (metricName = '";
//        for (int i = 0; i < metricNames.size() - 1; i++) {
//            query = query + metricNames.get(i) + "' or metricName ='";
//        }
//        query = query + metricNames.get(metricNames.size() - 1);
//        if (StringUtil.isNotEmpty(reqVo.getVmType())) {
//            query += "') and (type= '";
//            for (int i = 0; i < hostTypes.size() - 1; i++) {
//                query = query + hostTypes.get(i) + "' or type ='";
//            }
//            query = query + hostTypes.get(hostTypes.size() - 1);
//        }
//        query = query + "') and time>= '" + DateUtil.format(new Date(reqVo.getStartTime()), "yyyy-MM-dd HH:mm:ss")
//                + "' and time<= '" + DateUtil.format(new Date(reqVo.getEndTime()), "yyyy-MM-dd HH:mm:ss") + "'";
//        if (period > 3600000 && period <= 21600000) {
//            query = query + "group by time(2m)";
//        } else if (period > 21600000 && period <= 86400000) {
//            query = query + "group by time(8m)";
//        } else if (period > 86400000 && period <= 604800000) {
//            query = query + "group by time(56m)";
//        } else if (period > 604800000 && period <= 2592000000L) {
//            query = query + "group by time(2h)";
//        } else if (period > 2592000000L) {
//            query = query + "group by time(3h)";
//        } else if (period <= 3600000) {
//            query = query + "group by time(20s)";
//        }
//        query = query + " fill(0) ";
//        for (Object item : influxDBTemplate.fetchRecords(query)) {
//            hostGraphDatas.add(new ObjectMapper().convertValue(item, MonitorHostinfoDataRespVO.class).setTenantId(hostInfoDO.getTenantId()));
//        }
//        return hostGraphDatas;
        return new ArrayList<>();
    }

    @Override
    public PageResult<HostInfoDO> selectHostList(HostInfoPageReqVO pageReqVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        AdminUserRespDTO adminUserRespDTO = adminUserService.getUser(loginUser.getId()).getData();
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            if (StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
                pageReqVO.setTenantId(Arrays.asList(String.valueOf(adminUserRespDTO.getTenantId()).split(",")));
            } else {
                pageReqVO.setTenantId(Arrays.asList(adminUserRespDTO.getServiceTenantId().split(",")));
            }
        }
        PageParam pageParam = new PageParam().setPageNo(pageReqVO.getPageNo()).setPageSize(pageReqVO.getPageSize());
        IPage<HostInfoDO> mpPage = MyBatisUtils.buildPage(pageParam);
        return new PageResult<>(hostInfoMapper.alarmHostList(mpPage, pageReqVO), mpPage.getTotal());
    }

    @Override
    public List<HostInfoDO> getHostListByUuids(List<String> uuidList) {
        return hostInfoMapper.selectListByUuid(uuidList);
    }

    @Override
    public List<ReckonVO> getCPUValuation(ReckonReqVO reqVo) {
        return valuationQuery(reqVo, "CPUAverageUsedUtilization");
    }

    public List<ReckonVO> getMemoryValuation(ReckonReqVO reqVo) {
        return valuationQuery(reqVo, "MemoryUsedInPercent");
    }

    public List<ReckonVO> getDickValuation(ReckonReqVO reqVo) {
        return valuationQuery(reqVo, "DiskAllUsedCapacityInPercent");
    }

    @Override
    public List<ReckonVO> getHostValuation(ReckonReqVO reqVo) {
        List<ReckonVO> listVO = new ArrayList<>();
        listVO.addAll(getCPUValuation(reqVo));
        listVO.addAll(getMemoryValuation(reqVo));
        listVO.addAll(getDickValuation(reqVo));
        return listVO;
    }

    @Override
    public Map<String, Object> compareAndForecast(ReckonReqVO reqVo) {
//        Map<String, Object> result = new HashMap<>();
//        List<Map> platform = platformconfigApi.getPlatformSelectList("").getData();
//        List<String> platformIds = platform.stream()
//                .map(map -> String.valueOf(map.get("platformId")))
//                .collect(Collectors.toList());
//        if (CollectionUtil.isNotEmpty(platformIds)) {
//            StringJoiner stringJoiner = new StringJoiner("' or platformId = '");
//            for (String platformId : platformIds) {
//                stringJoiner.add(platformId);
//            }
//            String currentPeriod = "SELECT MEAN(value) as value FROM (SELECT MEAN(value)  as value from zj_cloud_host WHERE metricName = 'CPUAverageUsedUtilization' and time >= now()-7d";
//            //获取上期的值
//            String lastPeriod = currentPeriod + "-7d" + " and time < now()-7d";
//            if (StringUtil.isNotEmpty(reqVo.getPlatformId())) {
//                lastPeriod = lastPeriod + " and (platformId = '" + reqVo.getPlatformId().replace(",", "' or platformId = '") + "')";
//            }
//            if (StringUtil.isNotEmpty(reqVo.getUuid())) {
//                lastPeriod = lastPeriod + " and ( uuid = '" + reqVo.getUuid().replace(",", "' or uuid = '") + "')";
//            }
//            lastPeriod += " group by uuid)";
//            List<Object> lastPeriodResult = influxDBTemplate.fetchRecords(lastPeriod);
//            //获取本期的值
//            if (StringUtil.isNotEmpty(reqVo.getPlatformId())) {
//                currentPeriod = currentPeriod + " and (platformId= '" + reqVo.getPlatformId().replace(",", "' or platformId = '") + "')";
//            }
//
//            if (StringUtil.isNotEmpty(reqVo.getUuid())) {
//                currentPeriod = currentPeriod + " and ( uuid = '" + reqVo.getUuid().replace(",", "' or uuid = '") + "')";
//            }
//            currentPeriod += " group by uuid)";
//            List<Object> currentPeriodResult = influxDBTemplate.fetchRecords(currentPeriod);
//            //计算环比率
//            if (lastPeriodResult.size() == 0 || currentPeriodResult.size() == 0) {
//                result.put("chain", 0.0);
//                result.put("expectedGrowthRate", 0.0);
//            } else {
//                Double current = (Double) ((Map) currentPeriodResult.get(0)).get("value");
//                Double last = (Double) ((Map) lastPeriodResult.get(0)).get("value");
//                Double chain = (current - last) / last * 100;
//                result.put("chain", chain);
//                //预计增长
//                Double expectedGrowthRate = ((0.7 * current + 0.3 * last) - current) / (0.7 * current + 0.3 * last) * 100;
//                result.put("expectedGrowthRate", expectedGrowthRate);
//            }
//        } else {
//            result.put("chain", 0.0);
//            result.put("expectedGrowthRate", 0.0);
//        }
//        return result;
        return new ArrayHashMap<>();
    }

    @Override
    public Map<String, Object> getUsage(ReckonReqVO reqVo) {
//        Map<String, Object> result = new HashMap<>();
////        todo
//        List<Map> platform = platformconfigApi.getPlatformSelectList("").getData();
//        List<String> platformIds = platform.stream()
//                .map(map -> String.valueOf(map.get("platformId")))
//                .collect(Collectors.toList());
//        if (CollectionUtil.isNotEmpty(platformIds)) {
//            StringJoiner stringJoiner = new StringJoiner("' or platformId = '");
//            for (String platformId : platformIds) {
//                stringJoiner.add(platformId);
//            }
//            String platformIdStr = " and (platformId= '" + stringJoiner.toString() + "' )";
//
//            String memoryUsageQuery = "SELECT mean(value) as value from zj_cloud_host WHERE metricName = 'MemoryUsedInPercent' and time >= now()-" + reqVo.getReckonTime() + platformIdStr;
//            //获取存储使用情况
//            if (StringUtil.isNotEmpty(reqVo.getPlatformId())) {
//                memoryUsageQuery = memoryUsageQuery + " and (platformId = '" + reqVo.getPlatformId().replace(",", "' or platformId = '") + "')";
//            }
//            if (StringUtil.isNotEmpty(reqVo.getUuid())) {
//                memoryUsageQuery = memoryUsageQuery + " and ( uuid = '" + reqVo.getUuid().replace(",", "' or uuid = '") + "')";
//            }
//            List<Object> memoryUsage = influxDBTemplate.fetchRecords(memoryUsageQuery);
//            if (memoryUsage.size() > 0) {
//                result.put("memoryUsage", ((Map) memoryUsage.get(0)).get("value"));
//            } else {
//                result.put("memoryUsage", 0.0);
//            }
//            String cpuUsageQuery = "SELECT mean(value) as value from zj_cloud_host WHERE metricName = 'CPUAverageUsedUtilization' and time >= now()-" + reqVo.getReckonTime() + platformIdStr;
//            //获取CPU使用情况
//            if (StringUtil.isNotEmpty(reqVo.getPlatformId())) {
//                cpuUsageQuery = cpuUsageQuery + " and (platformId = '" + reqVo.getPlatformId().replace(",", "' or platformId = '") + "')";
//            }
//            if (StringUtil.isNotEmpty(reqVo.getUuid())) {
//                cpuUsageQuery = cpuUsageQuery + " and ( uuid = '" + reqVo.getUuid().replace(",", "' or uuid = '") + "')";
//            }
//            List<Object> cpuUsage = influxDBTemplate.fetchRecords(cpuUsageQuery);
//            if (cpuUsage.size() > 0) {
//                result.put("cpuUsage", ((Map) cpuUsage.get(0)).get("value"));
//            } else {
//                result.put("cpuUsage", 0.0);
//            }
//            String diskUsageQuery = "SELECT mean(value) as value from zj_cloud_host WHERE metricName = 'DiskAllUsedCapacityInPercent' and time >= now()-" + reqVo.getReckonTime() + platformIdStr;
//            //获取存储使用情况
//            if (StringUtil.isNotEmpty(reqVo.getPlatformId())) {
//                diskUsageQuery = diskUsageQuery + " and (platformId = '" + reqVo.getPlatformId().replace(",", "' or platformId = '") + "')";
//            }
//            if (StringUtil.isNotEmpty(reqVo.getUuid())) {
//                diskUsageQuery = diskUsageQuery + " and ( uuid = '" + reqVo.getUuid().replace(",", "' or uuid = '") + "')";
//            }
//            List<Object> diskUsage = influxDBTemplate.fetchRecords(diskUsageQuery);
//            if (diskUsage.size() > 0) {
//                result.put("diskUsage", ((Map) diskUsage.get(0)).get("value"));
//            } else {
//                result.put("diskUsage", 0.0);
//            }
//        } else {
//            result.put("diskUsage", 0.0);
//            result.put("cpuUsage", 0.0);
//            result.put("memoryUsage", 0.0);
//        }
//        return result;

        return new ArrayHashMap<>();
    }

    @Override
    public List<HostInfoDO> getListByPlatformId(String platformIds) {
        if (StringUtil.isNotEmpty(platformIds)) {
//            List<String>  platformIdList = Arrays.asList(platformIds.split(","));
            return hostInfoMapper.getListByPlatformId(platformIds);
        } else {
            LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
            List<Map> mapList = platformconfigApi.getPlatformListByUserId(String.valueOf(loginUser.getId())).getData();
            if (CollectionUtil.isEmpty(mapList)) {
                return new ArrayList<>();
            }
            Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
            Long tenantId = null;
            if (!roleApi.hasAnySuperAdmin(roleIds)) {
                tenantId = loginUser.getTenantId();
            }
            return hostInfoMapper.selectByTenant(tenantId);
        }
    }

    public List<ReckonVO> valuationQuery(ReckonReqVO reqVo, String metricName) {
//        int offset = Integer.parseInt(reqVo.getReckonTime().replaceAll("[^0-9]", ""));
//        Calendar cal = Calendar.getInstance();
//        List<ReckonVO> listVO = new ArrayList<>();
//        //验证是否存在大于指定天数的数据，没有则返回空
//        String verifyQuery = "SELECT * from zj_cloud_host WHERE metricName = '" + metricName + "'" +
//                " and time <= now()-" + reqVo.getReckonTime() + " and (platformId = '" + reqVo.getPlatformId().replace(",", "' or platformId = '") + "')";
//        List<Object> verifyList = influxDBTemplate.fetchRecords(verifyQuery + "LIMIT 1");
//
//        if (verifyList.size() == 0) {
//            return listVO;
//        }
//        //获取目标数据
//        String targetQuery = "SELECT MEAN(value) as value from zj_cloud_host " +
//                "WHERE metricName = '" + metricName + "'" +
//                "and time >= now()-" + reqVo.getReckonTime();
//        String avg = "SELECT MEAN(value) as value from zj_cloud_host WHERE metricName = '" + metricName + "'" +
//                " and time >= now()-" + reqVo.getReckonTime();
//        if (StringUtil.isNotEmpty(reqVo.getPlatformId())) {
//            targetQuery = targetQuery + " and (platformId = '" + reqVo.getPlatformId().replace(",", "' or platformId = '") + "')";
//            avg = avg + " and (platformId = '" + reqVo.getPlatformId().replace(",", "' or platformId = '") + "')";
//        }
//        if (StringUtil.isNotEmpty(reqVo.getUuid())) {
//            targetQuery = targetQuery + " and (uuid = '" + reqVo.getUuid().replace(",", "' or uuid = '") + "')";
//            avg = avg + " and (uuid = '" + reqVo.getUuid().replace(",", "' or uuid = '") + "')";
//        }
//        String timeSpan = " group by time(12h)";
//        if (offset > 7) {
//            timeSpan = " group by time(24h)";
//        }
//        List<Object> result = influxDBTemplate.fetchRecords(targetQuery + timeSpan + " fill(0.0) ORDER by time asc");
//
//        Gson gson = new Gson();
//        List<ReckonVO> realValList = new ArrayList<>();
//        if (result.size() > 0) {
//            // 使用Gson的fromJson方法进行对象转换
//            realValList = gson.fromJson(gson.toJson(result),
//                    new TypeToken<List<ReckonVO>>() {
//                    }.getType());
//
//            realValList.forEach(item -> item.setType(metricName).setVm_metricName(metricName));
//        }
//
//        listVO.addAll(realValList);
//        //以数据的平均值作为第一条预测值
//        List<Object> average = influxDBTemplate.fetchRecords(avg);
//        Double lastPredictCPU = 0.0;
//        if (average.size() > 0) {
//            lastPredictCPU = (Double) new ObjectMapper().convertValue(average.get(0), Map.class).get("value");
//        }
//        for (ReckonVO item : realValList) {
//            ReckonVO reckonVal = new ReckonVO();
//            if (item.getValue() != null) {
//                lastPredictCPU = 0.7 * item.getValue() + 0.3 * lastPredictCPU;
//            } else {
//                lastPredictCPU = 0.0;
//            }
//            cal.setTime(item.getTime());
//            cal.add(Calendar.DATE, +offset);
//            reckonVal.setValue(lastPredictCPU);
//            reckonVal.setTime(cal.getTime());
//            reckonVal.setType(metricName);
//            reckonVal.setVm_metricName(metricName);
//            listVO.add(reckonVal);
//        }
//        listVO.sort(Comparator.comparing(ReckonVO::getTime));
//        return listVO;
        return new ArrayList<>();
    }

    @Override
    public Map<String, Object> getHostAlarmByType(ReckonReqVO reqVo) {
//        获取租户平台，方法实现中以获取租户id
        List<Map> platform = platformconfigApi.getPlatformSelectList("").getData();

        Map<String, Object> result = new HashMap<>();
        if (CollectionUtil.isNotEmpty(platform)) {
            List<Long> platformIds = platform.stream()
                    .map(map -> StringUtil.toLong(map.get("platformId")))
                    .collect(Collectors.toList());
            reqVo.setPlatformList(platformIds);
            result = hostInfoMapper.getHostAlarmByType(reqVo);
        } else {
            result = new HashMap<>();
            result.put("CPU", 0);
            result.put("Memory", 0);
            result.put("Disk", 0);
        }
        if (ObjectUtil.isEmpty(result)) {
            result = new HashMap<>();
            result.put("CPU", 0);
            result.put("Memory", 0);
            result.put("Disk", 0);
        }
        return result;
    }

    @Override
    public void updateHostInfoByUuid(HostInfoUpdateReqVO hostInfoDO) {
        hostInfoMapper.updateByUuid(hostInfoDO);
    }

    @Override
    public int deleteHostList(List<HostInfoDO> list) {
        return hostInfoMapper.deleteHostList(list);
    }

    @Override
    public PageResult getResourcePage(MixResourceSimpleInfoReqVO reqVO) {
        PageResult pageResult = new PageResult<>();
        if (reqVO.getSourceType().equals("host")) {
            HostInfoPageReqVO hostInfoPageReqVO = new HostInfoPageReqVO();
            hostInfoPageReqVO.setPageNo(reqVO.getPageNo());
            hostInfoPageReqVO.setPageSize(reqVO.getPageSize());
            hostInfoPageReqVO.setPlatformId(reqVO.getPlatformId());
            hostInfoPageReqVO.setQueryData(reqVO.getQueryData());
            pageResult = getHostInfoPage(hostInfoPageReqVO);
        } else if (reqVO.getSourceType().equals("hardware")) {
            HardwareInfoPageReqVO hardwareInfoPageReqVO = new HardwareInfoPageReqVO();
            hardwareInfoPageReqVO.setPageNo(reqVO.getPageNo());
            hardwareInfoPageReqVO.setPageSize(reqVO.getPageSize());
            hardwareInfoPageReqVO.setPlatformId(reqVO.getPlatformId());
            hardwareInfoPageReqVO.setQueryData(reqVO.getQueryData());
            pageResult = hardwareInfoService.getHardwareInfoPage(hardwareInfoPageReqVO);
        } else if (reqVO.getSourceType().equals("storage")) {
            StorageInfoPageReqVO storageInfoPageReqVO = new StorageInfoPageReqVO();
            storageInfoPageReqVO.setPageNo(reqVO.getPageNo());
            storageInfoPageReqVO.setPageSize(reqVO.getPageSize());
            storageInfoPageReqVO.setPlatformId(reqVO.getPlatformId());
            storageInfoPageReqVO.setName(reqVO.getQueryData());
            pageResult = storageInfoService.getStorageInfoPage(storageInfoPageReqVO);
        }
        return pageResult;
    }

    @Override
    public List<Map<String, String>> getRecourseByPlatform(MixResourceSimpleInfoReqVO reqVO) {
        List<String> allowedSourceTypes = Arrays.asList(
                ResourceCategoryEnum.APP_HARDWARE.getCode(),
                ResourceCategoryEnum.APP_HOST.getCode(),
                ResourceCategoryEnum.APP_STORAGE.getCode()
        );
        // 处理hz 数据返回name uuid  相关数据
        if (allowedSourceTypes.contains(reqVO.getSourceType())) {
            return hostInfoMapper.getRecourseByPlatform(reqVO);
        } else {
            List<Map<String, String>> apps = new ArrayList<>();
            List<Map> list = hostInfoMapper.getResourceAppList(reqVO.getPlatformId());

            if (!list.isEmpty()) {
                final Map<String, List<String>> appTypeMap = new HashMap<>();
                appTypeMap.put(ResourceCategoryEnum.APP_SERVICE.getCode(), Arrays.asList("website", "port", "ssl_cert", "ping", "websocket", "api", "dns", "fullsite", "api_code", "udp_port"));
                appTypeMap.put(ResourceCategoryEnum.APP_DB.getCode(), Arrays.asList("mysql", "oracle", "sqlserver", "mariadb", "dm", "redis", "redis_cluster", "redis_sentinel", "memcached", "tidb", "nebulaGraph", "opengauss", "mongodb"));
                appTypeMap.put(ResourceCategoryEnum.APP_MID.getCode(), Arrays.asList("nacos", "zookeeper", "kafka", "rabbitmq", "nginx", "pop3", "ftp", "smtp", "ntp", "spring_gateway", "emqx", "activemq", "rocketmq", "shenyu", "rabbitmq", "almalinux"));
                appTypeMap.put(ResourceCategoryEnum.APP_OS.getCode(), Arrays.asList("windows", "linux", "ubuntu", "coreos", "opensuse", "rockylinux", "euleros", "redhat", "centos", "freebsd", "debian"));
                appTypeMap.put(ResourceCategoryEnum.APP_CN.getCode(), Arrays.asList("docker", "kubernetes"));
//                appTypeMap.put(ResourceCategoryEnum.APP_PROMETHEUS.getCode(), Collections.singletonList("prometheus"));
                appTypeMap.put(ResourceCategoryEnum.APP_FIREWALL.getCode(), Arrays.asList("qax_firewall", "sswk_firewall", "sswk_defense", "sxf_firewall", "ad_firewall", "ac_firewall", "ah_firewall", "ah_waf", "lm_defense", "lm_waf", "trx_defense", "trx_firewall"));
                appTypeMap.put(ResourceCategoryEnum.APP_NETWORK.getCode(), Arrays.asList("h3c_switch", "cisco_switch", "huawei_switch", "tplink_switch", "hpe_switch"));
                appTypeMap.put(ResourceCategoryEnum.APP_STORED.getCode(), Arrays.asList("storage_huawei", "storage_inspur", "storage_synology"));
                appTypeMap.put(ResourceCategoryEnum.APP_INFRA.getCode(), Arrays.asList("infra_ipmi"));
                String sourceType = reqVO.getSourceType();
                List<String> allowedApps = appTypeMap.get(sourceType);
                if (allowedApps != null) {
                    for (Map map : list) {
                        String app = (String) map.get("app");
                        if (allowedApps.contains(app)) {
                            apps.add(map);
                        }
                    }
                }
            }
            return apps;
        }

    }

    @Override
    public CommonResult<HostInfoRespCreateReqDTO> getByUuid(String hostUuid) {
        HostInfoRespCreateReqDTO respDTO = HostInfoConvert.INSTANCE.convertOneDO(hostInfoMapper.selectOne("uuid", hostUuid));
        return CommonResult.success(respDTO);
    }

    @Override

    public PageResult<HostInfoDO> getHostInfoSlavePagePage(HostInfoPageReqVO pageVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        AdminUserRespDTO adminUserRespDTO = adminUserService.getUser(loginUser.getId()).getData();
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            if (StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
                pageVO.setTenantId(Arrays.asList(String.valueOf(adminUserRespDTO.getTenantId()).split(",")));
            } else {
                pageVO.setTenantId(Arrays.asList(adminUserRespDTO.getServiceTenantId().split(",")));
            }
        }
        PageParam pageParam = new PageParam().setPageNo(pageVO.getPageNo()).setPageSize(pageVO.getPageSize());
        IPage<HostInfoDO> mpPage = MyBatisUtils.buildPage(pageParam);
        List<String> uids = new ArrayList<>();
        if (StringUtil.isNotEmpty(pageVO.getIds())) {
            uids = Arrays.asList(pageVO.getIds().split(","));
        }
        return new PageResult<>(hostInfoMapper.getHostInfoSlavePagePage(mpPage, pageVO, uids), mpPage.getTotal());

    }

    @Slave
    @Override
    public HostInfoDO slaveget(String uuid) {
        return hostInfoMapper.selectOne("uuid", uuid);
    }

    @Override
    public HostInfoDO masterget(String uuid) {
        return hostInfoMapper.selectOne("uuid", uuid);
    }

    @Override
    public Map<String, List<String>> getAllDeviceByPlatformId(Collection<Long> platformId) {
        Map<String, List<String>> resultMap = new HashMap<>();
        resultMap.put("host", hostInfoMapper.getAllDeviceByPlatformId(platformId).stream().map(HostInfoDO::getUuid).collect(Collectors.toList()));
        resultMap.put("hardware", hardwareInfoService.getListByPlatformId(platformId).stream().map(HardwareInfoDO::getUuid).collect(Collectors.toList()));
        resultMap.put("storage", storageInfoService.getListByPlatformId(platformId).stream().map(StorageInfoDO::getUuid).collect(Collectors.toList()));
        return resultMap;
    }

    @Override
    public List<Map<String, String>> getClusterSimpleInfo(String platformId) {
        List<String> platformIds = new ArrayList<>();
        if (StringUtil.isNotEmpty(platformId)) {
            platformIds = Arrays.asList(platformId.split(","));
        }else {
            LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
            List<Map> platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
            for (Map map : platform) {
                String platformIdStr =  Convert.toStr(map.get("platformId")) ;
                platformIds.add(platformIdStr); // Add platformId to the lis
            }
        }
        return hostInfoMapper.getClusterSimpleInfo(platformIds);
    }

    @Override
    public HostInfoDO getHardwareMasterInfo(String uuid) {
        return hostInfoMapper.selectOne("uuid", uuid);
    }

    @Override
    public List<HostInfoDO> getListAll() {
        return hostInfoMapper.selectList();
    }

    @Override
    public void removeDuplicateData() {
        List<Map<String, String>> duplicateDatas = hostInfoMapper.getDuplicateDataIds();
        if (duplicateDatas.size() > 0) {
            hostInfoMapper.removeDuplicateData(duplicateDatas);
        }
    }


    @Override
    public void deleteHostInfoByplatform(Long id) {
        hostInfoMapper.deleteByPlatformId(id);
    }

    @Override
    public void exportWord(HttpServletResponse response, String platformId, String uuid) {
        ReckonReqVO reckonReqVO = new ReckonReqVO();
        reckonReqVO.setPlatformId(platformId);
        reckonReqVO.setUuid(uuid);
        Map<String, Object> paramMap = new HashMap<>();
        HostInfoDO host = new HostInfoDO();
        if (StringUtil.isNotEmpty(uuid)) {
            host = hostInfoMapper.selectOne("uuid", uuid);
        }
        PlatformconfigDTO platformconfigDTO = platformconfigApi.getByConfigId(StringUtil.toLong(platformId)).getData();
        paramMap.put("platformName", platformconfigDTO.getName());
        paramMap.put("hostName", StringUtil.isNullOrEmpty(uuid) ? "总览" : host.getName());

//        ReckonReqVO reckonReqVO = BeanUtils.toBean(reqVO, ReckonReqVO.class);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        //环比和下月预测数据
        Map<String, Object> compareAndForecastResult = compareAndForecast(reckonReqVO);
        DecimalFormat df = new DecimalFormat("#.##");
        Double chainValue = Convert.toDouble(compareAndForecastResult.get("chain"));
        Double expectedGrowthRateValue = Convert.toDouble(compareAndForecastResult.get("expectedGrowthRate"));
        String chain = "";
        String expectedGrowthRate = "";
        if (chainValue > 0) {
            chain = "上涨" + df.format(chainValue) + "%";
        } else {
            chain = "下降" + df.format(Math.abs(chainValue)) + "%";
        }
        if (expectedGrowthRateValue > 0) {
            expectedGrowthRate = "上涨" + df.format(expectedGrowthRateValue) + "%";
        } else {
            expectedGrowthRate = "下降" + df.format(Math.abs(expectedGrowthRateValue)) + "%";
        }
        paramMap.put("expectedGrowthRate", expectedGrowthRate);
        paramMap.put("chain", chain);

        paramMap.putAll(getChartDate(reckonReqVO.setReckonTime("7d")));

        paramMap.putAll(getChartDate(reckonReqVO.setReckonTime("15d")));


        createAndDownloadWord(paramMap, response, sdf.format(new Date()));
    }

    @Override
    public void deletMonitorByPlatformId(Long platformId) {
        hostInfoMapper.deletMonitorByPlatformId(platformId);
    }

    @Override
    public void alertsnmpTrap(String platformName, String hostName, String json) {
        Long platformId = hostInfoMapper.findPlatformName(platformName);
        // 根据ip找到相关的监控基础信息
        List<Monitor> monitors = hostInfoMapper.findMonitorsByPlatformIdAndHost(platformId, hostName);
        List<HardwareInfoDO> maps = hardwareInfoService.findHardwareByplatformIdAndHost(platformId, hostName);
        addAllList(monitors, json, maps);

    }

    @Override
    public List<CloudRespCreateReqDTO> getCloudByPlatformId(Long platformId) {
        return hostInfoMapper.getCloudByPlatformId(platformId);
    }

    @Override
    public List<Map> getMonitorAsset(Long platformId) {
        return hostInfoMapper.getMonitorAsset(platformId);
    }

    @Override
    public void deletMonitorAssetByPlatformId(Long platformId) {
        hostInfoMapper.deletMonitorAssetByPlatformId(platformId);
    }

    @Override
    public CommonResult<HostInfoRespCreateReqDTO> getById(Long hostUuid) {
        HostInfoRespCreateReqDTO respDTO = HostInfoConvert.INSTANCE.convertOneDO(hostInfoMapper.selectById(hostUuid));
        return CommonResult.success(respDTO);
    }

    @Override
    public boolean authorization(Long platformId, Long id) {
        HostInfoDO monitorAssetDO = hostInfoMapper.selectById(id);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        AdminUserRespDTO user = adminUserService.getUser(Convert.toLong(loginUser.getId())).getCheckedData();

        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        // 根据租户id查询该租户下所配置的租户管理员角色
        // 资产对应租户管理员（有多个人） 租户id去找该租户的租户管理员的userid， 在通过userid 去system_wechat_binding 查询是否有绑定微信，如果没有绑定
        PlatformconfigDTO platformconfigDTO = platformconfigApi.getByConfigId(platformId).getCheckedData();
        List<Long> tenantIds = platformconfigApi.getTenantsByConfigId(platformId).getCheckedData();

        List<String> userList = new ArrayList<>();
        // 租户下配置的租户管理员角色的人
        List<String> openIds = new ArrayList<>();
        // 如果是租户管理员则查询该用户下所绑定的微信openid
        if (!tenantIds.isEmpty()) {
            for (Long tenantid : tenantIds) {
                List<String> list = hostInfoMapper.getUserListByTenantId(tenantid);
                userList.addAll(list);
                // 查询到租户管理员的id
                String userId = hostInfoMapper.getTenantInfo(tenantid);
                // 租户管理员id再去微信绑定表中查询openid 如果没有查到直接返回false
                String openId = hostInfoMapper.getOpenIdByUserId(userId);
                if (StringUtil.isNullOrEmpty(openId)) {
                    continue;
                }
                openIds.add(openId);
            }
        }

        if (!userList.isEmpty()) {
            for (String userId : userList) {
                String openId = hostInfoMapper.getOpenIdByUserId(userId);
                if (StringUtil.isNullOrEmpty(openId)) {
                    continue;
                }
                openIds.add(openId);
            }
        }
        // 使用 LinkedHashSet 去除重复项，同时保持原有的顺序
        Set<String> uniqueOpenIdsSet = new LinkedHashSet<>(openIds);
        List<String> uniqueOpenIds = new ArrayList<>(uniqueOpenIdsSet);

        // 授权申请记录
        MonitorAuthorizationDO monitorAuthorizationDO = new MonitorAuthorizationDO();
        monitorAuthorizationDO.setAssetId(Convert.toStr(monitorAssetDO.getId()));
        monitorAuthorizationDO.setAssetName(monitorAssetDO.getName());
        monitorAuthorizationDO.setHostName(monitorAssetDO.getPlatformName());
        monitorAuthorizationDO.setCreateTime(DateUtil.toLocalDateTime(new Date()));
        monitorAuthorizationDO.setNickName(user.getNickname());
        monitorAuthorizationDO.setUserId(user.getId());
        monitorAuthorizationDO.setAuthorizationTime(DateUtil.toLocalDateTime(DateUtil.offsetMinute(new Date(), 20)));
        monitorAuthorizationDO.setMonitorAssetId(id);
        monitorAuthorizationDO.setType(1);
        int d = monitorAuthorizationMapper.insert(monitorAuthorizationDO);
        // 授权申请人
        MonitorAuthorizationUserDO monitorAuthorizationUserDO = new MonitorAuthorizationUserDO();
        monitorAuthorizationUserDO.setMonitorAssetId(monitorAssetDO.getId());
        monitorAuthorizationUserDO.setAuthorizationUserId(user.getId());
        monitorAuthorizationUserMapper.insert(monitorAuthorizationUserDO);
        // 如果新增成功以后，如果20分钟后状态没有变成1 将自动修改状态为0
        if (d > 0) {
            ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
            scheduler.schedule(() -> {
                MonitorAuthorizationDO existingAuthorization = monitorAuthorizationMapper.selectById(monitorAuthorizationDO.getId());
                if (existingAuthorization != null && "1".equals(existingAuthorization.getAuthorizationType())) {
                    existingAuthorization.setAuthorizationType("0");
                    monitorAuthorizationMapper.updateById(existingAuthorization);
                    hostInfoMapper.updateAuthorizationType(existingAuthorization.getMonitorAssetId(), "0", DateUtil.toLocalDateTime(new Date()));
                    monitorAuthorizationUserMapper.deleteById(monitorAuthorizationUserDO);
                }
            }, 20, TimeUnit.MINUTES);
        }

        //点击详情跳转的地址
        String redictUrl = "";
        try {
            String encodedUrl = URLEncoder.encode(address, "UTF-8");
            String parameter = URLUtil.encode("/pages/assetAuth/info?id=" + monitorAuthorizationDO.getId());
            redictUrl = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxe3d3d4577c6c41b6&redirect_uri=" + encodedUrl + "&response_type=code&scope=snsapi_userinfo&state=" + parameter + "&connect_redirect=1#wechat_redirect";
        } catch (UnsupportedEncodingException e) {
            System.out.println("Error encoding URL: " + e.getMessage());
        }

        Map<String, Object> templateParams = new HashMap<>();
        // 申请人
        templateParams.put("nickname", user.getNickname());
        // 数据主体
        templateParams.put("hostName", monitorAssetDO.getPlatformName());
        // 授权时间 当前时间
        templateParams.put("date", new Date());
        // 产品名称
        templateParams.put("assetName", monitorAssetDO.getName());
        // url
        templateParams.put("url", redictUrl);
        log.info("进入授权，用户列表为:" + uniqueOpenIds);
        boolean suc = false;
        if (uniqueOpenIds.size() > 0) {
            log.info("进入获取用户列表数据:" + tenantIds);
            for (String openId : uniqueOpenIds) {
                // openid
                templateParams.put("openId", openId);
                suc = weChatSendApi.sendSingleWeChatToAuthorization(templateParams).getCheckedData();
            }
        }
        // 如果发起成功则新增一条数据到数据库中，并且修改状态
        hostInfoMapper.updateAuthorizationType(monitorAssetDO.getId(), "1", monitorAuthorizationDO.getAuthorizationTime());
        return suc;
    }

    @Override
    @TenantIgnore
    public List<HostInfoDO> getHostListByTenantOrPlatforms(AssetReqVO assetReqVO) {
        List<Long> platformIds;
        if (CollectionUtil.isNotEmpty(assetReqVO.getPlatformIds())) {
            platformIds = assetReqVO.getPlatformIds();
            return hostInfoMapper.getListByPlatformIds(platformIds);
        } else {
            List<Map> platform = platformconfigApi.getPlatformByTenantId(String.valueOf(assetReqVO.getTenantId())).getData();
            return hostInfoMapper.getListByPlatform(platform);
        }
    }

    @Override
    public List<HostInfoDO> getByPlatformIdAndTags(AssetReqVO assetReqVO) {
        List<Long> platformIds = assetReqVO.getPlatformIds();
        List<Long> tagIds = assetReqVO.getTags();

        List<String> tagNames = tagsMapper.selectList(TagsDO::getId, tagIds).stream()
                .map(TagsDO::getName)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        return hostInfoMapper.getByPlatformIdAndTags(platformIds, tagNames);
    }



    public void addAllList(List<Monitor> monitors, String json, List<HardwareInfoDO> hardwareInfoDOList) {
        List<AlarmDorisReqDTO> alertList = new ArrayList<>();
        Long currentId = hostInfoMapper.findMaxId();
        cn.hutool.json.JSONArray jsonArray = JSONUtil.parseArray(json);
        List<Map> mapList = JSONUtil.toList(jsonArray, Map.class);
        List<String> manufacturers = dictDataApi.getDictDataListByType("manufacturer").getData();

        // Process each monitor and hardware info object
        processMonitors(monitors, mapList, manufacturers, alertList, currentId);
        processHardwareInfos(hardwareInfoDOList, mapList, manufacturers, alertList, currentId);

        if (!alertList.isEmpty()) {
            hostInfoMapper.addAlertList(alertList);
        }
    }

    private void processMonitors(List<Monitor> monitors, List<Map> mapList, List<String> manufacturers,
                                 List<AlarmDorisReqDTO> alertList, Long currentId) {
        for (Monitor monitor : monitors) {
            List<String> oidData = getOidData(monitor.getManufacturer(), monitor.getApp(), manufacturers);
            generateAlerts(mapList, oidData, monitor.getApp(), monitor.getId(), monitor.getName(),
                    monitor.getPlatformId(), monitor.getPlatformName(), alertList, currentId);
        }
    }

    private void processHardwareInfos(List<HardwareInfoDO> hardwareInfoDOList, List<Map> mapList,
                                      List<String> manufacturers, List<AlarmDorisReqDTO> alertList, Long currentId) {
        for (HardwareInfoDO hardware : hardwareInfoDOList) {
            List<String> oidData = getOidData(hardware.getManufacturer(), "hardware", manufacturers);
            generateAlerts(mapList, oidData, "hardware", hardware.getId(), hardware.getName(),
                    hardware.getPlatformId(), hardware.getPlatformName(), alertList, currentId);
        }
    }

    private List<String> getOidData(String manufacturer, String app, List<String> manufacturers) {
        if (manufacturers.contains(manufacturer)) {
            return dictDataApi.getDictDataListByType(manufacturer).getCheckedData();
        } else {
            return dictDataApi.getDictDataListByType(app).getCheckedData();
        }
    }

    private void generateAlerts(List<Map> mapList, List<String> oidData, String app, Long monitorId, String monitorName,
                                Long platformId, String platformName, List<AlarmDorisReqDTO> alertList, Long currentId) {
        for (Map<String, Object> map : mapList) {
            for (String oid : oidData) {
                if (StrUtil.toString(map.get("Oid")).equals(oid)) {
                    AlarmDorisReqDTO alert = createAlert(app, monitorId, monitorName, platformId, platformName, map, ++currentId);
                    alertList.add(alert);
                }
            }
        }
    }

    private AlarmDorisReqDTO createAlert(String app, Long monitorId, String monitorName, Long platformId,
                                         String platformName, Map<String, Object> map, Long id) {
        AlarmDorisReqDTO alert = new AlarmDorisReqDTO();
        alert.setId(id);
        alert.setApp(app);
        alert.setMonitorId(Convert.toStr(monitorId));
        alert.setMonitorName(monitorName);
        alert.setFirstAlarmTime(DateUtil.current());
        alert.setLastAlarmTime(DateUtil.current());
        alert.setGmtCreate(new Date());
        alert.setGmtUpdate(new Date());
        alert.setPriority(2);
        alert.setTags(JSONUtil.toJsonStr(createTagsMap(app, monitorId)));
        alert.setTarget("SNMP");
        alert.setTimes(1);
        alert.setPlatformId(platformId);
        alert.setPlatformName(platformName);
        alert.setResourceType(3);
        alert.setStatus(0);
        alert.setAlarmName("SNMPTrap告警");
        alert.setAlarmId(288L);
        alert.setIsSolved(0);
        alert.setAlarmConfigName("");
        alert.setAlarmRule("");
        alert.setContent(Convert.toStr(map.get("Name")) + " " + Convert.toStr(map.get("Value")));
        return alert;
    }

    private Map<String, String> createTagsMap(String app, Long monitorId) {
        Map<String, String> tags = new HashMap<>();
        tags.put("app", app);
        tags.put("monitorId", Convert.toStr(monitorId));
        return tags;
    }


    private String getChartTitle(String oldTitle, String dayStr) {
        if (dayStr.equals("7d")) {
            return oldTitle + "(7天)";
        } else {
            return oldTitle + "(15天)";
        }
    }

    private static void createAndDownloadWord(Map<String, Object> paramMap, HttpServletResponse response, String date) {
        log.info("生成电子协查函参数paramMap = {}", paramMap);
        //将圆环图定义为多系列图表
        Configure configure = Configure.createDefault();
        configure.getChartPolicys().put(ChartTypes.PIE, new MultiSeriesChartTemplateRenderPolicy());
//        ConfigureBuilder builder = Configure.builder();
//        builder.useSpringEL();
        try {
            // 创建一个临时的输出流
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            String path = "file-template" + File.separator + "trendAnalysisWordTemplate.docx";
            // 获取电子协查函模板
            ClassPathResource classPathResource = new ClassPathResource(path);
            try (InputStream inputStream = classPathResource.getInputStream()) {
                if (Objects.isNull(inputStream)) {
                    log.error("获取电子协查函模板失败");
                    return;
                }
                // 通过协查函模板，开始生成电子协查函
                try {
                    XWPFTemplate template = XWPFTemplate.compile(inputStream, configure);
                    template = template.render(paramMap);
                    //设置折线图数据线段类型
                    setLineChart(template.getXWPFDocument().getCharts());
                    // 将生成的Word文档写入临时输出流
                    template.write(outputStream);
                } catch (Exception e) {
                    log.error("创建协查函异常，异常详情：\n{}", e);
                }
            } catch (Exception e) {
                log.error("创建协查函异常，异常详情：\n{}", e);
            }
            String d = "资源趋势分析---" + date;
            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(d, "UTF-8") + ".docx");
            // 将生成的Word文档写入响应输出流
            try (OutputStream out = response.getOutputStream()) {
                out.write(outputStream.toByteArray());
                out.flush();
                out.close();
            }
        } catch (Exception e) {
            log.error("导出Word文档并下载时发生异常：\n{}", e);
        }
    }

    private static void setLineChart(List<XWPFChart> charts) throws XmlException {
        for (XWPFChart chart : charts) {
            if (chart.getChartSeries().get(0) instanceof XDDFLineChartData) {
                CTChart ctChart = chart.getCTChart();
                List<CTLineChart> lineCharts = ctChart.getPlotArea().getLineChartList();
                for (CTLineChart lineChart : lineCharts) {
                    for (CTLineSer lineSer : lineChart.getSerList()) {
                        int dataSize = StringUtil.toInt(String.valueOf(lineSer.getVal().getNumRef().getNumCache().getPtCount().getVal()));
                        int halfSize = dataSize / 2;
                        //数据的后半部分是预测数据，用虚线表示
                        for (int i = halfSize + 1; i < dataSize; i++) {
                            //设直线条属性
                            CTDPt ctdPt = lineSer.addNewDPt();
                            CTUnsignedInt unsignedInt = CTUnsignedInt.Factory.newInstance();
                            unsignedInt.setVal((long) i);
                            ctdPt.setIdx(unsignedInt);
                            CTLineProperties ctLineProperties = ctdPt.addNewSpPr().addNewLn();
                            //设置线宽，12700为一磅
                            ctLineProperties.setW(12700);
                            //设置线的类型
                            ctLineProperties.addNewPrstDash().setVal(STPresetLineDashVal.SYS_DOT);
                        }
                    }
                }
            }
        }
    }

    private Map<String, Object> getChartDate(ReckonReqVO reckonReqVO) {
        String suffix = "Week";
        if (reckonReqVO.getReckonTime().equals("15d")) {
            suffix = "HalfAMonth";
        }
        SimpleDateFormat chartDadeSdf = new SimpleDateFormat("MM-dd");
        Map<String, Object> paramMap = new HashMap<>();
        //资源使用情况数据
        Map<String, Object> usageResult = getUsage(reckonReqVO);
        //CPU圆环图
        Double cpuUsage = StringUtil.toDouble(usageResult.get("cpuUsage")) / 100.0;
        Double memoryUsage = StringUtil.toDouble(usageResult.get("memoryUsage")) / 100.0;
        Double diskUsage = StringUtil.toDouble(usageResult.get("diskUsage")) / 100.0;

        ChartMultiSeriesRenderData usage = new ChartMultiSeriesRenderData();
        List<SeriesRenderData> usageResultDatass = new ArrayList<>();
        usage.setChartTitle(getChartTitle("使用情况", reckonReqVO.getReckonTime()));
        usage.setCategories(new String[]{"已使用", "未使用"});
        usageResultDatass.add(new SeriesRenderData("cpu使用率", new Number[]{cpuUsage, 1 - cpuUsage}));
        usageResultDatass.add(new SeriesRenderData("内存使用率", new Number[]{memoryUsage, 1 - memoryUsage}));
        usageResultDatass.add(new SeriesRenderData("磁盘使用率", new Number[]{diskUsage, 1 - diskUsage}));
        usage.setSeriesDatas(usageResultDatass);
        paramMap.put("usage" + suffix, usage);

        //告警分布数据
        Map<String, Object> alarmResult = getHostAlarmByType(reckonReqVO);
        ChartSingleSeriesRenderData alarm = Charts.ofSingleSeries("报警分布", new String[]{"磁盘", "内存", "cpu"}).series("告警数", new Integer[]{Convert.toInt(alarmResult.get("Disk")), Convert.toInt(alarmResult.get("Memory")), Convert.toInt(alarmResult.get("CPU"))}).create();
        alarm.setChartTitle(getChartTitle(alarm.getChartTitle(), reckonReqVO.getReckonTime()));
        paramMap.put("alarm" + suffix, alarm);

        //cpu预测
        List<ReckonVO> cpuResult = getCPUValuation(reckonReqVO);
        List<Double> cpuResultDoubleValues = new ArrayList<>();
        List<Date> cpuDateList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(cpuResult)) {
            //提取日期作为x轴
            cpuDateList = cpuResult.stream()
                    .distinct()
                    .map(ReckonVO::getTime)
                    .collect(Collectors.toList());
            //处理图标数据
            ChartMultiSeriesRenderData cpuResultChartValues = new ChartMultiSeriesRenderData();
            cpuResultChartValues.setChartTitle(getChartTitle("CPU使用率", reckonReqVO.getReckonTime()));
            List<SeriesRenderData> cpuResultDatass = new ArrayList<>();
            List<ReckonVO> cpuSortedList = cpuResult.stream()
                    .sorted(Comparator.comparing(ReckonVO::getTime))
                    .collect(Collectors.toList());
            cpuResultDoubleValues = cpuSortedList.stream()
                    .map(reckonVO -> reckonVO.getValue() / 100.00)
                    .collect(Collectors.toList());

            cpuResultDatass.add(new SeriesRenderData("cpu使用率", cpuResultDoubleValues.stream().toArray(Double[]::new)));
            cpuResultChartValues.setCategories(cpuDateList.stream().map(date -> chartDadeSdf.format(date)).toArray(String[]::new));
            cpuResultChartValues.setSeriesDatas(cpuResultDatass);
            paramMap.put("cpuResult" + suffix, cpuResultChartValues);
        }

        //内存预测
        List<ReckonVO> memoryResult = getMemoryValuation(reckonReqVO);
        List<Double> memoryResultDoubleValues = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(memoryResult)) {
            //提取日期作为x轴
            if (CollectionUtil.isEmpty(cpuDateList)) {
                cpuDateList = memoryResult.stream()
                        .map(ReckonVO::getTime)
                        .collect(Collectors.toList());
            }
            //处理图标数据
            ChartMultiSeriesRenderData memoryResultChartValues = new ChartMultiSeriesRenderData();
            memoryResultChartValues.setChartTitle(getChartTitle("内存使用率", reckonReqVO.getReckonTime()));
            List<SeriesRenderData> memoryResultDatass = new ArrayList<>();
            List<ReckonVO> memorySortedList = memoryResult.stream()
                    .sorted(Comparator.comparing(ReckonVO::getTime))
                    .collect(Collectors.toList());
            memoryResultDoubleValues = memorySortedList.stream()
                    .map(reckonVO -> reckonVO.getValue() / 100.00)
                    .collect(Collectors.toList());
            memoryResultDatass.add(new SeriesRenderData("内存使用率", memoryResultDoubleValues.stream().toArray(Double[]::new)));
            memoryResultChartValues.setCategories(cpuDateList.stream().map(date -> chartDadeSdf.format(date)).toArray(String[]::new));
            memoryResultChartValues.setSeriesDatas(memoryResultDatass);
            paramMap.put("memoryResult" + suffix, memoryResultChartValues);
        }
        //磁盘预测
        List<ReckonVO> diskResult = getDickValuation(reckonReqVO);
        List<Double> diskResultDoubleValues = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(diskResult)) {
            //提取日期作为x轴
            if (CollectionUtil.isEmpty(cpuDateList)) {
                cpuDateList = diskResult.stream()
                        .map(ReckonVO::getTime)
                        .collect(Collectors.toList());
            }
            //处理图标数据
            ChartMultiSeriesRenderData diskResultChartValues = new ChartMultiSeriesRenderData();
            diskResultChartValues.setChartTitle(getChartTitle("磁盘使用率", reckonReqVO.getReckonTime()));
            List<SeriesRenderData> diskResultDatass = new ArrayList<>();
            List<ReckonVO> diskSortedList = diskResult.stream()
                    .sorted(Comparator.comparing(ReckonVO::getTime))
                    .collect(Collectors.toList());
            diskResultDoubleValues = diskSortedList.stream()
                    .map(reckonVO -> reckonVO.getValue() / 100.00)
                    .collect(Collectors.toList());
            diskResultDatass.add(new SeriesRenderData("磁盘使用率", diskResultDoubleValues.stream().toArray(Double[]::new)));
            diskResultChartValues.setCategories(cpuDateList.stream().map(date -> chartDadeSdf.format(date)).toArray(String[]::new));
            diskResultChartValues.setSeriesDatas(diskResultDatass);
            paramMap.put("diskResult" + suffix, diskResultChartValues);
        }
        //综合预测数据
        if (CollectionUtil.isNotEmpty(cpuResultDoubleValues) || CollectionUtil.isNotEmpty(memoryResultDoubleValues) || CollectionUtil.isNotEmpty(diskResultDoubleValues)) {
            ChartMultiSeriesRenderData reckonResultChartValues = new ChartMultiSeriesRenderData();
            reckonResultChartValues.setChartTitle(getChartTitle("同比上月数据总览", reckonReqVO.getReckonTime()));
            List<SeriesRenderData> reckonResultDatass = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(cpuResultDoubleValues)) {
                reckonResultDatass.add(new SeriesRenderData("cpu使用率", cpuResultDoubleValues.stream().toArray(Double[]::new)));
            }
            if (CollectionUtil.isNotEmpty(memoryResultDoubleValues)) {
                reckonResultDatass.add(new SeriesRenderData("内存使用率", memoryResultDoubleValues.stream().toArray(Double[]::new)));
            }
            if (CollectionUtil.isNotEmpty(diskResultDoubleValues)) {
                reckonResultDatass.add(new SeriesRenderData("磁盘使用率", diskResultDoubleValues.stream().toArray(Double[]::new)));
            }
            reckonResultChartValues.setCategories(cpuDateList.stream().map(date -> chartDadeSdf.format(date)).toArray(String[]::new));
            reckonResultChartValues.setSeriesDatas(reckonResultDatass);
            paramMap.put("reckonResult" + suffix, reckonResultChartValues);
        }
        return paramMap;
    }

}
