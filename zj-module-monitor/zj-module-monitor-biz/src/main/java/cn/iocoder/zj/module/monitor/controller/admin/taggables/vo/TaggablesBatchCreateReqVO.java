package cn.iocoder.zj.module.monitor.controller.admin.taggables.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 标签绑定关系批量创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class TaggablesBatchCreateReqVO {
    @Schema(description = "标签ids", required = true)
    @NotNull(message = "标签id不能为空")
    private String tagIds;

    @Schema(description = "绑定资产id ", required = true)
    @NotNull(message = "绑定资产ids 不能为空")
    private String taggableIds;

    @Schema(description = "绑定资产类型", required = true)
    @NotNull(message = "绑定资产类型不能为空")
    private String taggableType;
}
