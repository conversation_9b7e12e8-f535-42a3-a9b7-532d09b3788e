package cn.iocoder.zj.module.monitor.convert.alarmconfigtemplate;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.alarmconfigtemplate.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmconfigtemplate.AlarmConfigTemplateDO;

/**
 * 告警配置模板 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface AlarmConfigTemplateConvert {

    AlarmConfigTemplateConvert INSTANCE = Mappers.getMapper(AlarmConfigTemplateConvert.class);

    AlarmConfigTemplateDO convert(AlarmConfigTemplateCreateReqVO bean);

    AlarmConfigTemplateDO convert(AlarmConfigTemplateUpdateReqVO bean);

    AlarmConfigTemplateRespVO convert(AlarmConfigTemplateDO bean);

    List<AlarmConfigTemplateRespVO> convertList(List<AlarmConfigTemplateDO> list);

    PageResult<AlarmConfigTemplateRespVO> convertPage(PageResult<AlarmConfigTemplateDO> page);

    List<AlarmConfigTemplateExcelVO> convertList02(List<AlarmConfigTemplateDO> list);

}
