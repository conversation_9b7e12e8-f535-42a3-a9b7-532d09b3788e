package cn.iocoder.zj.module.monitor.controller.admin.gatherdevice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;

@Schema(description = "管理后台 - 采集设备 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GatherDeviceRespVO extends GatherDeviceBaseVO {

    @Schema(description = "主键id", required = true)
    private Long id;

    @Schema(description = "创建时间", required = true)
    private Date createTime;

}
