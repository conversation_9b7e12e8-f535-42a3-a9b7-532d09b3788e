package cn.iocoder.zj.module.monitor.service.scanip;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.ScanIPData;
import cn.iocoder.zj.framework.common.dal.manager.ScanIPRequest;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.collector.api.ScanIPApi;
import cn.iocoder.zj.module.monitor.controller.admin.scanip.vo.IPRangeVO;
import cn.iocoder.zj.module.monitor.controller.admin.scanip.vo.IPScanResultVO;
import cn.iocoder.zj.module.monitor.convert.scanip.IPRangeConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.scanip.IPRangeDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.scanip.IPScanResultDO;
import cn.iocoder.zj.module.monitor.dal.mysql.scanip.IPRangeMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.scanip.IPScanResultMapper;
import cn.iocoder.zj.module.monitor.taskTime.listener.RedisPublisher;
import cn.iocoder.zj.module.monitor.taskTime.service.impl.ServiceImplScheduleImpl;
import cn.iocoder.zj.module.monitor.util.StringUtil;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.singleton;

/**
 * IP段管理 Service 实现类
 */
@Service
@Slf4j
@Validated
public class IPRangeServiceImpl implements IPRangeService {

    @Resource
    private IPRangeMapper ipRangeMapper;

    @Resource
    private IPScanResultMapper ipScanResultMapper;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private RoleApi roleApi;

    @Resource
    private AdminUserApi adminUserService;

    @Resource
    private ScanIPApi scanIPApi;

    @Resource
    ServiceImplScheduleImpl serviceImplSchedule;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private RedisPublisher redisPublisher;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createIPRange(@Valid IPRangeVO createReqVO) {
        // 设置租户信息
        setTenantInfo(createReqVO,"create");

        IPRangeDO ipRange = IPRangeConvert.INSTANCE.convert(createReqVO);
        ipRangeMapper.insert(ipRange);

        // 解析IP段范围，生成初始的扫描结果记录
        createScanResults(ipRange);

        //加入任务
        serviceImplSchedule.addTask(createReqVO.getTaskId(),createReqVO.getCronExp());

        if(createReqVO.getIsExecute() != null && createReqVO.getIsExecute()){
            execute(ipRange.getId());
        }
        return ipRange.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateIPRange(@Valid IPRangeVO updateReqVO) {
        setTenantInfo(updateReqVO, "update");

        IPRangeDO oldIPRange = validateIPRangeExists(updateReqVO.getId());
        IPRangeDO updateObj = IPRangeConvert.INSTANCE.convert(updateReqVO);
        ipRangeMapper.updateById(updateObj);

        // 检查IP段和TCP端口变化
        boolean needRescan = (StrUtil.isNotEmpty(updateObj.getIpRanges()) && !oldIPRange.getIpRanges().equals(updateObj.getIpRanges())) ||
                ((StrUtil.isEmpty(oldIPRange.getTcpPort()) && StrUtil.isNotEmpty(updateObj.getTcpPort())) ||
                        (StrUtil.isNotEmpty(oldIPRange.getTcpPort()) && StrUtil.isEmpty(updateObj.getTcpPort())) ||
                        (StrUtil.isNotEmpty(oldIPRange.getTcpPort()) && StrUtil.isNotEmpty(updateObj.getTcpPort()) &&
                                !oldIPRange.getTcpPort().equals(updateObj.getTcpPort()))) || oldIPRange.getPlatformId() != updateObj.getPlatformId();

        if (needRescan && StrUtil.isNotEmpty(updateObj.getCronExp())) {
            ipScanResultMapper.deleteIPScanByIpRangeId(updateObj.getId());
            createScanResults(updateObj);
        }

        // 执行和更新定时任务
        if (Boolean.TRUE.equals(updateReqVO.getIsExecute())) {
            execute(updateReqVO.getId());
        }

        if (StrUtil.isNotEmpty(updateReqVO.getCronExp()) && !oldIPRange.getCronExp().equals(updateReqVO.getCronExp())) {
            redisPublisher.publish("hsy-task",oldIPRange.getTaskId()+"&"+updateReqVO.getCronExp());
        }
    }

    /**
     * 设置租户信息
     */
    private void setTenantInfo(IPRangeVO reqVO, String type) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        AdminUserRespDTO adminUserRespDTO = adminUserService.getUserById(loginUser.getId()).getData();

        // 处理TCP端口
        if (StrUtil.isNotEmpty(reqVO.getTcpPort())) {
            if (!isValidTcpPorts(reqVO.getTcpPort())) {
                throw new IllegalArgumentException("TCP端口格式错误，只能输入1-65535的整数，多个端口用逗号分隔");
            }
            reqVO.setTcps(Arrays.stream(reqVO.getTcpPort().split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList()));
            reqVO.setTcpSupport(1);
        } else {
            reqVO.setTcpSupport(0);
        }

        // 创建时设置租户信息
        if ("create".equals(type)) {
            reqVO.setTaskId(UUID.randomUUID().toString());
            reqVO.setTenantId(adminUserRespDTO.getTenantId());
            reqVO.setTenantName(adminUserRespDTO.getTenantName());
        }

        // 设置定时任务表达式
        if (StrUtil.isNotEmpty(reqVO.getTaskCycle())) {
            Map<String, String> cronExpressions = Map.of(
                    "hour", "0 0 * * * ?",
                    "day", "0 0 0 * * ?",
                    "week", "0 0 0 ? * 1",
                    "month", "0 0 0 1 * ?"
            );
            reqVO.setCronExp(cronExpressions.getOrDefault(reqVO.getTaskCycle(), null));
        }
    }

    /**
     * 创建IP扫描结果记录
     */
    private void createScanResults(IPRangeDO ipRange) {
        List<String> ipList = parseIPRanges(ipRange.getIpRanges());
        if (CollUtil.isEmpty(ipList)) {
            return;
        }

        List<IPScanResultDO> scanResults = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        // 如果有TCP端口，则为每个IP和端口组合创建扫描结果
        if (CollUtil.isNotEmpty(ipRange.getTcps())) {
            ipList.forEach(ip ->
                    ipRange.getTcps().forEach(tcp ->
                            scanResults.add(createScanResult(ipRange, ip, tcp, now))
                    )
            );
        } else {
            // 如果没有TCP端口，则只为每个IP创建扫描结果
            ipList.forEach(ip ->
                    scanResults.add(createScanResult(ipRange, ip, null, now))
            );
        }

        // 批量插入扫描结果
        if (CollUtil.isNotEmpty(scanResults)) {
            List<String> collect = scanResults.stream().map(IPScanResultDO::getIpAddress).collect(Collectors.toList());
            List<Map<String,String>> map = ipScanResultMapper.getMonitorByIds(collect,ipRange.getPlatformId());

            Map<String, String> resultMap = new HashMap<>();
            if (CollUtil.isNotEmpty(map)) {
                for (Map<String, String> item : map) {
                    String ip = item.get("host");
                    String platformId = String.valueOf(item.get("platform_id"));
                    resultMap.put(ip, platformId);
                }
                for (IPScanResultDO scanResult : scanResults) {
                    String ipAddress = scanResult.getIpAddress();
                    if(resultMap.containsKey(ipAddress)){
                        scanResult.setRemark("1");
                    }
                }
            }
            ipScanResultMapper.insertBatch(scanResults);
        }
    }

    private IPScanResultDO createScanResult(IPRangeDO ipRange, String ip, Integer tcpPort, LocalDateTime now) {
        IPScanResultDO scanResult = new IPScanResultDO();
        scanResult.setIpAddress(ip);
        scanResult.setIpRangeId(ipRange.getId());
        scanResult.setIpRangeName(ipRange.getName());
        scanResult.setPingSupport(ipRange.getPingSupport());
        scanResult.setSnmpSupport(ipRange.getSnmpSupport());
        scanResult.setTcpSupport(ipRange.getTcpSupport());
        scanResult.setTcpPort(tcpPort);
        scanResult.setSnmpCommunity(ipRange.getSnmpCommunity());
        scanResult.setSnmpVersion(ipRange.getSnmpVersion());
        scanResult.setSnmpPort(ipRange.getSnmpPort());
        scanResult.setScanTime(now);
        scanResult.setTenantId(ipRange.getTenantId());
        scanResult.setTenantName(ipRange.getTenantName());
        scanResult.setPlatformId(ipRange.getPlatformId());
        scanResult.setPlatformName(ipRange.getPlatformName());
        return scanResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteIPRange(Long id) {
        // 校验存在
        IPRangeDO rangeDO = validateIPRangeExists(id);
        if(rangeDO.getState() == 1){
            throw new IllegalArgumentException("任务运行中，暂不能删除，请稍后再试");
        }

        // 删除IP段（主表数据）
        ipRangeMapper.delById(id);

        // 删除关联的扫描结果（子表数据）
        ipScanResultMapper.deleteIPScanByIpRangeId(id);

        // 删除关联的扫描计划
        redisPublisher.publish("hsy-task",rangeDO.getTaskId());
    }

    /**
     * 解析IP段范围
     */
    private List<String> parseIPRanges(String ipRanges) {
        if (StrUtil.isBlank(ipRanges)) {
            return Collections.emptyList();
        }

        Set<String> ipSet = new HashSet<>();
        String[] ranges = ipRanges.split(",");

        for (String range : ranges) {
            range = range.trim();
            if (StrUtil.isBlank(range)) {
                continue;
            }

            try {
                // 解析格式：********-254
                String[] parts = range.split("-");
                if (parts.length != 2) {
                    log.warn("IP范围格式错误: {}", range);
                    continue;
                }

                // 获取基础IP地址部分
                String baseIp = parts[0].trim();
                String[] ipParts = baseIp.split("\\.");
                if (ipParts.length != 4) {
                    log.warn("IP地址格式错误: {}", baseIp);
                    continue;
                }

                // 获取结束范围
                int endRange;
                try {
                    endRange = Integer.parseInt(parts[1].trim());
                    if (endRange < 1 || endRange > 255) {
                        log.warn("IP范围结束值无效: {}", endRange);
                        continue;
                    }
                } catch (NumberFormatException e) {
                    log.warn("IP范围结束值格式错误: {}", parts[1]);
                    continue;
                }

                // 获取起始IP的最后一位
                int startRange;
                try {
                    startRange = Integer.parseInt(ipParts[3]);
                    if (startRange < 1 || startRange > 255) {
                        log.warn("IP范围起始值无效: {}", startRange);
                        continue;
                    }
                } catch (NumberFormatException e) {
                    log.warn("IP范围起始值格式错误: {}", ipParts[3]);
                    continue;
                }

                // 验证范围
                if (startRange > endRange) {
                    log.warn("IP范围无效，起始值大于结束值: {}-{}", startRange, endRange);
                    continue;
                }

                // 生成IP地址列表
                String baseNetwork = String.format("%s.%s.%s.", ipParts[0], ipParts[1], ipParts[2]);
                for (int i = startRange; i <= endRange; i++) {
                    ipSet.add(baseNetwork + i);
                }
            } catch (Exception e) {
                log.warn("解析IP范围失败: {}", range, e);
            }
        }

        return new ArrayList<>(ipSet);
    }

    /**
     * 校验IP段是否存在
     *
     * @param id IP段ID
     * @return IP段对象
     */
    private IPRangeDO validateIPRangeExists(Long id) {
        IPRangeDO ipRange = ipRangeMapper.selectById(id);
        if (ipRange == null) {
            throw new IllegalArgumentException("未查询到数据");
        }

        return ipRange;
    }

    @Override
    public IPRangeVO getIPRange(Long id) {
        IPRangeDO ipRange = validateIPRangeExists(id);
        PageParam pageParam = new PageParam().setPageNo(1).setPageSize(10);
        IPage<IPScanResultVO> mpPage = MyBatisUtils.buildPage(pageParam);
        IPScanResultVO pageVO = new IPScanResultVO();
        pageVO.setIpRangeId(id);
        ipScanResultMapper.getPage(pageVO,mpPage);
        ipRange.setSuccCount(Long.valueOf(mpPage.getTotal()).intValue());
        return IPRangeConvert.INSTANCE.convert(ipRange);
    }

    @Override
    public List<IPRangeVO> getIPRangeList(IPRangeVO exportReqVO) {
        List<IPRangeDO> list = ipRangeMapper.selectList(exportReqVO);
        return IPRangeConvert.INSTANCE.convertList(list);
    }

    @Override
    public PageResult<IPRangeVO> getIPRangePage(IPRangeVO pageVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        AdminUserRespDTO adminUserRespDTO = adminUserService.getUserById(loginUser.getId()).getData();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            if (StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
                pageVO.setTenantIds(Arrays.asList(String.valueOf(adminUserRespDTO.getTenantId()).split(",")));
            } else {
                pageVO.setTenantIds(Arrays.asList(adminUserRespDTO.getServiceTenantId().split(",")));
            }
        }

        if (StringUtil.isNotEmpty(pageVO.getSortBy())) {
            String regex = "([a-z])([A-Z]+)";
            String replacement = "$1_$2";
            String output = pageVO.getSortBy().replaceAll(regex, replacement).toLowerCase();
            pageVO.setSortBy(output);
        }
        PageParam pageParam = new PageParam().setPageNo(pageVO.getPageNo()).setPageSize(pageVO.getPageSize());
        IPage<IPRangeDO> mpPage = MyBatisUtils.buildPage(pageParam);
        List<IPRangeDO> list = ipRangeMapper.getPage(pageVO,mpPage);
        PageResult pageResult = new PageResult<>();
        pageResult.setTotal(mpPage.getTotal());
        pageResult.setList(list);
        return IPRangeConvert.INSTANCE.convertPage(pageResult);
    }

    @Override
    public PageResult<IPScanResultVO> getIPScanResultPage(IPScanResultVO pageVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        AdminUserRespDTO adminUserRespDTO = adminUserService.getUserById(loginUser.getId()).getData();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            if (StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
                pageVO.setTenantIds(Arrays.asList(String.valueOf(adminUserRespDTO.getTenantId()).split(",")));
            } else {
                pageVO.setTenantIds(Arrays.asList(adminUserRespDTO.getServiceTenantId().split(",")));
            }
        }

        if(StrUtil.isNotEmpty(pageVO.getIpRanges())){
            List<String> ipList = parseIPRanges(pageVO.getIpRanges());
            pageVO.setIps(ipList);
        }

        if(StrUtil.isNotEmpty(pageVO.getTypeName())){
            switch (pageVO.getTypeName().toLowerCase()) {
                case "ping":
                    pageVO.setPingStatus(1);
                    break;
                case "tcp":
                    pageVO.setTcpStatus(1);
                    break;
                case "snmp":
                    pageVO.setSnmpStatus(1);
                    break;
                default:
                    break;
            }
        }

        if (StringUtil.isNotEmpty(pageVO.getSortBy())) {
            String regex = "([a-z])([A-Z]+)";
            String replacement = "$1_$2";
            String output = pageVO.getSortBy().replaceAll(regex, replacement).toLowerCase();
            pageVO.setSortBy(output);
        }

        PageParam pageParam = new PageParam().setPageNo(pageVO.getPageNo()).setPageSize(pageVO.getPageSize());
        IPage<IPScanResultVO> mpPage = MyBatisUtils.buildPage(pageParam);
        List<IPScanResultVO> list = ipScanResultMapper.getPage(pageVO,mpPage);

        if(CollUtil.isNotEmpty(list)){
            Map<String, List<IPScanResultVO>> duplicates = list.stream()
                    .collect(Collectors.groupingBy(item -> item.getIpAddress() + "_" + item.getIpRangeId()))
                    .entrySet()
                    .stream()
                    .filter(entry -> entry.getValue().size() > 1)
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

            list.forEach(x->{
                String statusCovt = getStatusCovt(x.getSnmpStatus(), x.getTcpStatus(), x.getPingStatus());
                x.setTypeName(statusCovt);

                if(CollUtil.isEmpty(duplicates.get(x.getIpAddress() + "_" + x.getIpRangeId()))){
                    x.setTcpPort(null);
                }
            });
        }

        PageResult pageResult = new PageResult<>();
        pageResult.setTotal(mpPage.getTotal());
        pageResult.setList(list);
        return pageResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdate(IPRangeVO updateReqVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        AdminUserRespDTO adminUserRespDTO = adminUserService.getUserById(loginUser.getId()).getData();
        updateReqVO.setTenantId(adminUserRespDTO.getTenantId());
        updateReqVO.setTenantName(adminUserRespDTO.getTenantName());
        if(updateReqVO.getIds().size() > 0){
            if(updateReqVO.getStatus() != null){
                ipRangeMapper.updateBatchStatus(updateReqVO.getIds(), updateReqVO.getStatus());
            }

            if(updateReqVO.getDelete() != null){
                ipRangeMapper.deleteBatchByIds(updateReqVO.getIds());
                ipScanResultMapper.deleteIPScanByIpRangeIds(updateReqVO.getIds());
            }

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void execute(Long id) {
        IPRangeDO dto = ipRangeMapper.selectOne("id", id);
        if (dto.getState() == 1) {
            throw new IllegalArgumentException("计划正在执行中，不要重复执行...");
        }
        List<IPScanResultDO> ipScanResultDOS = ipScanResultMapper.selectListByIPRangeId(id);
        if (CollUtil.isNotEmpty(ipScanResultDOS)) {
            String collectName = ipScanResultMapper.getCollectByTenantId(dto.getPlatformId());
            if (StrUtil.isEmpty(collectName)) {
                throw new IllegalArgumentException(dto.getPlatformName()+" 未绑定采集器，暂无法进行自动发现");
            }

            List<ScanIPData> scanIPDTOS = BeanUtil.copyToList(ipScanResultDOS, ScanIPData.class);
            ScanIPRequest request = new ScanIPRequest()
                    .setIpRangeId(id)
                    .setTenantName(collectName)
                    .setTotal(scanIPDTOS.size())
                    .setSnmpCount(0)
                    .setPingCount(0)
                    .setTcpCount(0)
                    .setRatio(0)
                    .setScanIPDataList(scanIPDTOS);

            // 先更新状态
            IPRangeDO rangeDO = new IPRangeDO();
            rangeDO.setId(id);
            rangeDO.setState(1);
            rangeDO.setUpdateTime(DateUtil.toLocalDateTime(new Date()));
            ipRangeMapper.updateById(rangeDO);
            redisTemplate.delete("SCAN_IP:" + request.getIpRangeId());
            String checkedData = scanIPApi.scanIpChange(request).getCheckedData();
            if(StrUtil.isNotEmpty(checkedData)){
                rangeDO.setState(3);
                rangeDO.setUpdateTime(DateUtil.toLocalDateTime(new Date()));
                rangeDO.setRemark(checkedData);
                ipRangeMapper.updateById(rangeDO);
            }
        }else {
            throw new IllegalArgumentException("未查询到要发现的IP");
        }
    }

    public String getStatusCovt(Integer snmpStatus, Integer tcpStatus, Integer pingStatus) {
        StringJoiner joiner = new StringJoiner(", ");

        if (snmpStatus == 1) {
            joiner.add("SNMP");
        }
        if (tcpStatus == 1) {
            joiner.add("TCP端口");
        }
        if (pingStatus == 1) {
            joiner.add("Ping");
        }

        return joiner.toString();
    }

    public static boolean isValidTcpPorts(String tcpPort) {
        // 分割端口字符串
        String[] ports = tcpPort.split(",");
        for (String port : ports) {
            // 检查是否包含非数字字符
            if (!port.matches("^[1-9]\\d*$")) {
                return false;
            }

            // 检查端口范围
            try {
                int portNum = Integer.parseInt(port);
                if (portNum < 1 || portNum > 65535) {
                    return false;
                }
            } catch (NumberFormatException e) {
                return false;
            }
        }
        return true;
    }

    public static boolean isIpInRange(String ipToCheck, String ipRange) {
        try {
            String[] ipParts = ipToCheck.split("\\.");
            long ipLong = ipToLong(ipParts);

            // 解析IP范围
            String[] rangeParts = ipRange.split("-");
            String baseIp = rangeParts[0];
            String[] baseIpParts = baseIp.split("\\.");

            // 获取最后一个段的范围
            int lastOctetEnd = Integer.parseInt(rangeParts[1]);

            // 构建起始IP和结束IP
            long startIp = ipToLong(baseIpParts);
            baseIpParts[3] = String.valueOf(lastOctetEnd);
            long endIp = ipToLong(baseIpParts);

            // 确保起始IP小于结束IP
            if (startIp > endIp) {
                long temp = startIp;
                startIp = endIp;
                endIp = temp;
            }

            // 检查IP是否在范围内
            return ipLong >= startIp && ipLong <= endIp;

        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    private static long ipToLong(String[] ipParts) {
        long result = 0;
        for (int i = 0; i < 4; i++) {
            result = result << 8 | Integer.parseInt(ipParts[i]);
        }
        return result;
    }

    public static void main(String[] args) {
        // 测试示例
        String ipRange = "************-80";
        String ipToCheck1 = "*************";  // 应该返回 true
        String ipToCheck2 = "**************"; // 应该返回 false

        System.out.println("IP " + ipToCheck1 + " in range " + ipRange + ": "
                + isIpInRange(ipToCheck1, ipRange));
        System.out.println("IP " + ipToCheck2 + " in range " + ipRange + ": "
                + isIpInRange(ipToCheck2, ipRange));
    }
}