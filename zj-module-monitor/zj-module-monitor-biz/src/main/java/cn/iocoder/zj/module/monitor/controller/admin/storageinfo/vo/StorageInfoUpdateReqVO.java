package cn.iocoder.zj.module.monitor.controller.admin.storageinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 存储设备信息更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class StorageInfoUpdateReqVO extends StorageInfoBaseVO {

    @Schema(description = "逐渐", required = true)
    @NotNull(message = "逐渐不能为空")
    private Long id;

}
