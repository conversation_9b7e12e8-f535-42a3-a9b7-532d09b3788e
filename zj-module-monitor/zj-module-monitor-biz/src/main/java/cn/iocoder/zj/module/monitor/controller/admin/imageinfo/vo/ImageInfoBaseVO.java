package cn.iocoder.zj.module.monitor.controller.admin.imageinfo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 镜像信息 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class ImageInfoBaseVO {
    @Schema(description = "表id")
    private Long id;

    @Schema(description = "镜像id")
    private String uuid;

    @Schema(description = "镜像名称")
    private String name;

    @Schema(description = "状态:Ready,Active,Deleted")
    private String status;

    @Schema(description = "镜像格式")
    private String format;

    @Schema(description = "CPU架构")
    private String cpuArch;

    @Schema(description = "操作系统")
    private String osType;

    @Schema(description = "镜像大小(字节)")
    private Long size;

    @Schema(description = "镜像类型")
    private String imageType;

    @Schema(description = "共享范围")
    private String sharingScope;

    @Schema(description = "标签")
    private String tag;

    @Schema(description = "云主机uuid")
    private String hostUuid;

    @Schema(description = "镜像创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime vCreateDate;

    @Schema(description = "镜像更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime vUpdateDate;

    @Schema(description = "系统语言")
    private String osLanguage;

    @Schema(description = "最小内存要求(MB)")
    private BigDecimal minMemory;

    @Schema(description = "最小磁盘要求(GB)")
    private BigDecimal minDisk;

    @Schema(description = "磁盘驱动")
    private String diskDriver;

    @Schema(description = "网卡驱动")
    private String networkDriver;

    @Schema(description = "引导方式")
    private String bootMode;

    @Schema(description = "远程终端协议")
    private String remoteProtocol;

    @Schema(description = "应用平台")
    private String applicationPlatform;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "删除状态")
    private Long deleted;

    @Schema(description = "平台名称")
    private String platformName;
    private String hostName;
}
