package cn.iocoder.zj.module.monitor.dal.mysql.networkl2;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL2DTO;
import cn.iocoder.zj.module.monitor.controller.admin.networkl2.vo.NetworkL2ExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.networkl2.vo.NetworkL2PageReqVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkl2.NetworkL2DO;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkl3.NetworkL3DO;
import cn.iocoder.zj.module.monitor.dal.dataobject.volumeinfo.VolumeInfoDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.volumesnapshot.VolumeSnapshotDO;
import cn.iocoder.zj.module.monitor.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 二级网络信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface NetworkL2Mapper extends BaseMapperX<NetworkL2DO> {

    default PageResult<NetworkL2DO> selectPage(NetworkL2PageReqVO reqVO, List<Map> platform) {
        List<String> uids = new ArrayList<>();
        if (StringUtil.isNotEmpty(reqVO.getIds())){
            uids=Arrays.asList(reqVO.getIds().split(","));
        }

        List<String> data = new ArrayList<>();
        if (platform.size()>0){
            for (Map map : platform){
                data.add(map.get("platformId").toString());
            }
        }

        LambdaQueryWrapper<NetworkL2DO> between = new LambdaQueryWrapperX<NetworkL2DO>()
                .inIfPresent(NetworkL2DO::getId, reqVO.getInPks())
                .likeIfPresent(NetworkL2DO::getName, reqVO.getName())
                .eqIfPresent(NetworkL2DO::getUuid, reqVO.getUuid())
                .eqIfPresent(NetworkL2DO::getPhysicalInterface, reqVO.getPhysicalInterface())
                .likeIfPresent(NetworkL2DO::getType, reqVO.getType())
                .eqIfPresent(NetworkL2DO::getVlan, reqVO.getVlan())
                .eqIfPresent(NetworkL2DO::getVirtualNetworkId, reqVO.getVirtualNetworkId())
                .betweenIfPresent(NetworkL2DO::getCreateTime, reqVO.getCreateTime())
                .likeIfPresent(NetworkL2DO::getRegionName, reqVO.getRegionName())
                .eqIfPresent(NetworkL2DO::getRegionId, reqVO.getRegionId())
                .eqIfPresent(NetworkL2DO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(NetworkL2DO::getPlatformName, reqVO.getPlatformName())
                .between(ObjectUtil.isNotEmpty(reqVO.getStartTime()) && ObjectUtil.isNotEmpty(reqVO.getEndTime())
                        , NetworkL2DO::getCreateTime, reqVO.getStartTime(), reqVO.getEndTime());

        if (StringUtil.isNotEmpty(reqVO.getSortDirection()) && reqVO.getSortDirection().equals("asc")){
            if (reqVO.getSortBy().equals("createTime")){
                between.orderByAsc(NetworkL2DO::getCreateTime);
            }
        }else {
            between.orderByDesc(NetworkL2DO::getCreateTime);
        }
        if (data.size()>0){
            between = (LambdaQueryWrapperX<NetworkL2DO>) between.in(NetworkL2DO::getPlatformId, data);
        }else {
            between = (LambdaQueryWrapperX<NetworkL2DO>) between.in(NetworkL2DO::getPlatformId, "null");
        }
        if (!uids.isEmpty()) {
            between = (LambdaQueryWrapperX<NetworkL2DO>) between.notIn(NetworkL2DO::getId, uids);
        }
        return selectPage(reqVO, between);

    }

    default List<NetworkL2DO> selectList(NetworkL2ExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<NetworkL2DO>()
                .likeIfPresent(NetworkL2DO::getName, reqVO.getName())
                .eqIfPresent(NetworkL2DO::getUuid, reqVO.getUuid())
                .eqIfPresent(NetworkL2DO::getPhysicalInterface, reqVO.getPhysicalInterface())
                .eqIfPresent(NetworkL2DO::getType, reqVO.getType())
                .eqIfPresent(NetworkL2DO::getVlan, reqVO.getVlan())
                .eqIfPresent(NetworkL2DO::getVirtualNetworkId, reqVO.getVirtualNetworkId())
                .betweenIfPresent(NetworkL2DO::getCreateTime, reqVO.getCreateTime())
                .likeIfPresent(NetworkL2DO::getRegionName, reqVO.getRegionName())
                .eqIfPresent(NetworkL2DO::getRegionId, reqVO.getRegionId())
                .eqIfPresent(NetworkL2DO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(NetworkL2DO::getPlatformName, reqVO.getPlatformName())
                .orderByDesc(NetworkL2DO::getId));
    }

    void updateNetWorkL2List(@Param("list") List<NetWorkL2DTO> list);

    List<Map> selectListByName();

    int deleteNetWorkL2ByNameList(@Param("list") List<NetworkL2DO> list);

    int deleteNetWorkL3ByNameList(@Param("list") List<NetworkL3DO> list);

    void deleteNetworkL2Byplatform(@Param("platformId") Long platformId);

    List<NetWorkL2DTO> getNetworkL2ByPlatformId(@Param("platformId") Long platformId);
}
