package cn.iocoder.zj.module.monitor.service.storagepool;

import java.util.*;
import javax.validation.*;

import cn.iocoder.zj.module.monitor.api.storagepool.dto.StoragePoolCreateRespDTO;
import cn.iocoder.zj.module.monitor.controller.admin.storagepool.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.storagepool.StoragePoolDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 存储池 Service 接口
 *
 * <AUTHOR>
 */
public interface StoragePoolService {

    /**
     * 创建存储池
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createStoragePool(@Valid StoragePoolCreateReqVO createReqVO);

    /**
     * 更新存储池
     *
     * @param updateReqVO 更新信息
     */
    void updateStoragePool(@Valid StoragePoolUpdateReqVO updateReqVO);

    /**
     * 删除存储池
     *
     * @param id 编号
     */
    void deleteStoragePool(Long id);

    /**
     * 获得存储池
     *
     * @param id 编号
     * @return 存储池
     */
    StoragePoolDO getStoragePool(Long id);

    /**
     * 获得存储池列表
     *
     * @param ids 编号
     * @return 存储池列表
     */
    List<StoragePoolDO> getStoragePoolList(Collection<Long> ids);

    /**
     * 获得存储池分页
     *
     * @param pageReqVO 分页查询
     * @return 存储池分页
     */
    PageResult<StoragePoolDO> getStoragePoolPage(StoragePoolPageReqVO pageReqVO);

    /**
     * 获得存储池列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 存储池列表
     */
    List<StoragePoolDO> getStoragePoolList(StoragePoolExportReqVO exportReqVO);

    void createStoragePoolList(List<StoragePoolDO> list);

    void updateStoragePoolList(List<StoragePoolDO> list);

    List<StoragePoolCreateRespDTO> getStoragePoolByPlatformId(Long platformId);

    int deleteStoragePoolList(List<StoragePoolDO> list);
}
