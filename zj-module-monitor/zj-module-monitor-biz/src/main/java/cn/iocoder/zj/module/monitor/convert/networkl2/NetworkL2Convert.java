package cn.iocoder.zj.module.monitor.convert.networkl2;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL2DTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.networkl2.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkl2.NetworkL2DO;

/**
 * 二级网络信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface NetworkL2Convert {

    NetworkL2Convert INSTANCE = Mappers.getMapper(NetworkL2Convert.class);

    NetworkL2DO convert(NetworkL2CreateReqVO bean);

    NetworkL2DO convert(NetworkL2UpdateReqVO bean);

    NetworkL2RespVO convert(NetworkL2DO bean);

    List<NetworkL2RespVO> convertList(List<NetworkL2DO> list);

    PageResult<NetworkL2RespVO> convertPage(PageResult<NetworkL2DO> page);

    List<NetworkL2ExcelVO> convertList02(List<NetworkL2DO> list);

    List<NetworkL2DO> convertCreateList(List<NetWorkL2DTO> reqDTO);

    List<NetWorkL2DTO> convertListDoToDto(List<NetworkL2DO> netWorkL2List);
}
