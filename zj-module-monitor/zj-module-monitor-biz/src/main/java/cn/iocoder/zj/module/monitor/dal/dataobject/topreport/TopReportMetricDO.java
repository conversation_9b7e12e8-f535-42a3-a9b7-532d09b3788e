package cn.iocoder.zj.module.monitor.dal.dataobject.topreport;

import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("top_report_metric")
public class TopReportMetricDO extends BaseDO {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * TOP报表ID
     */
    private Long reportId;

    /**
     * 指标名称
     */
    private String metricName;

    private String metricCode;

    private Long metricId;

    private Long pid;
}
