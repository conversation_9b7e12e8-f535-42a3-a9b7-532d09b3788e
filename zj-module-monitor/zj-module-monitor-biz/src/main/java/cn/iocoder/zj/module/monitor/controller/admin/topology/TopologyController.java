package cn.iocoder.zj.module.monitor.controller.admin.topology;

import org.apache.hertzbeat.common.entity.alerter.AlertRespVo;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;

import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.monitor.controller.admin.topology.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.topology.TopologyDO;
import cn.iocoder.zj.module.monitor.convert.topology.TopologyConvert;
import cn.iocoder.zj.module.monitor.service.topology.TopologyService;

@Tag(name = "管理后台 - 监控资源拓扑图")
@RestController
@RequestMapping("/monitor/topology")
@Validated
public class TopologyController {

    @Resource
    private TopologyService topologyService;

    @PostMapping("/create")
    @Operation(summary = "创建监控资源拓扑图")
    @PreAuthorize("@ss.hasPermission('monitor:topology:create')")
    @OperateLog(type = CREATE)
    public CommonResult<Long> createTopology(@Valid @RequestBody TopologyCreateReqVO createReqVO) {
        return success(topologyService.createTopology(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新监控资源拓扑图")
    @PreAuthorize("@ss.hasPermission('monitor:topology:update')")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateTopology(@Valid @RequestBody TopologyUpdateReqVO updateReqVO) {
        topologyService.updateTopology(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除监控资源拓扑图")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('monitor:topology:delete')")
    @OperateLog(type = DELETE)
    public CommonResult<Boolean> deleteTopology(@RequestParam("id") Long id) {
        topologyService.deleteTopology(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得监控资源拓扑图")
    @Parameter(name = "id", description = "拓扑图获取详情id", required = true, example = "1024")
    public CommonResult<TopologyRespVO> getTopology(@RequestParam("id") Long id) {
        TopologyDO topology = topologyService.getTopologyByPlatformId(id);
        return success(TopologyConvert.INSTANCE.convert(topology));
    }


    @GetMapping("/list")
    @Operation(summary = "获得监控资源拓扑图列表")
    @Parameter(name = "platformId", description = "平台id", required = false, example = "1024")
    public CommonResult<List<TopologyRespVO>> getTopologyList(@RequestParam(value = "platformId", required = false) Long platformId) {
        List<TopologyDO> list = topologyService.getTopologyList(platformId);
        return success(TopologyConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得监控资源拓扑图分页")
    public CommonResult<PageResult<TopologyRespVO>> getTopologyPage(@Valid TopologyPageReqVO pageVO) {
        PageResult<TopologyDO> pageResult = topologyService.getTopologyPage(pageVO);
        return success(TopologyConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出监控资源拓扑图 Excel")
    @OperateLog(type = EXPORT)
    public void exportTopologyExcel(@Valid TopologyExportReqVO exportReqVO,
                                    HttpServletResponse response) throws IOException {
        List<TopologyDO> list = topologyService.getTopologyList(exportReqVO);
        // 导出 Excel
        List<TopologyExcelVO> datas = TopologyConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "监控资源拓扑图.xls", "数据", TopologyExcelVO.class, datas);
    }

    @GetMapping("/getAlarmInfo")
    @Operation(summary = "获得监控资源拓扑图监控告警")
    @Parameter(name = "monitorId", description = "资产id", required = true, example = "1024")
    public CommonResult<List<AlertRespVo>> getAlarmInfo(@RequestParam("monitorId") String monitorId) {
        List<AlertRespVo> alertRespVo = topologyService.getAlarmInfo(monitorId);
        return success(alertRespVo);
    }

    // 根据拓扑图给的监控资产id 获取资产的状态，和资产告警状态
    @GetMapping("/getmonitorStatus")
    @Operation(summary = "获得监控资产的状态")
    @Parameter(name = "uuid", description = "资产id", required = false, example = "uuid")
    @Parameter(name = "type", description = "资产状态", required = false, example = "host")
    public CommonResult<Map> getmonitorStatus(@RequestParam("uuid") String uuid,
                                                    @RequestParam("type") String type) {
        Map alertRespVo = topologyService.getmonitorStatus(uuid, type);
        return success(alertRespVo);
    }


    @GetMapping("/getmonitorCount")
    @Operation(summary = "获得监控资产数量和告警数量")
    @Parameter(name = "id", description = "拓扑图id", required = false, example = "id")
    public CommonResult<Map> getmonitorCount(@RequestParam("id") String id) {
        Map alertRespVo = topologyService.getmonitorCount(id);
        return success(alertRespVo);
    }


    @GetMapping("/getmonitorListInfo")
    @Operation(summary = "根据拓扑图id获取资产列表和告警列表")
    @Parameter(name = "id", description = "拓扑图id", required = false, example = "id")
    public CommonResult<Map> getmonitorListInfo(@RequestParam("id") String id) {
        Map alertRespVo = topologyService.getmonitorListInfo(id);
        return success(alertRespVo);
    }

    @GetMapping("/getmonitorInterfacesJson")
    @Operation(summary = "根据资产id获取接口信息数据")
    @Parameter(name = "id", description = "拓扑图id", required = false, example = "id")
    @Parameter(name = "monitorId", description = "资产id", required = false, example = "monitorId")
    public CommonResult<Map> getmonitorInterfacesJson(@RequestParam("id") String id,
                                                      @RequestParam("monitorId") String monitorId) {
        Map alertRespVo = topologyService.getmonitorInterfacesJson(id,monitorId);
        return success(alertRespVo);
    }


    @GetMapping("/getHostInterfacesInfo")
    @Operation(summary = "根据资源id和接口信息获取宿主机接口信息")
    @Parameter(name = "monitorId", description = "资源id", required = false, example = "monitorId")
    @Parameter(name = "monitorInterfaces", description = "资源接口名称", required = false, example = "monitorInterfaces")
    public CommonResult<List<Map>> getHostInterfacesInfo(@RequestParam("monitorId") String monitorId,
                                                      @RequestParam("monitorInterfaces") String monitorInterfaces) {
        List<Map> alertRespVo = topologyService.getHostInterfacesInfo(monitorId,monitorInterfaces);
        return success(alertRespVo);
    }
}
