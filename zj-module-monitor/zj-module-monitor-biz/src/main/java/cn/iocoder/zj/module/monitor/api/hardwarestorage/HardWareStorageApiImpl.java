package cn.iocoder.zj.module.monitor.api.hardwarestorage;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.hardwarenic.HardWareNicRespDTO;
import cn.iocoder.zj.module.monitor.convert.hardwarestorage.HardwareStorageConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.hardwarestorage.HardwareStorageDO;
import cn.iocoder.zj.module.monitor.service.hardwarestorage.HardwareStorageService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class HardWareStorageApiImpl implements HardWareStorageApi {

    @Resource
    private HardwareStorageService hardwareStorageService;

    @Override
    public CommonResult<Boolean> adds(List<HardWareStorageRespDTO> reqDTO) {

        List<HardwareStorageDO> list = HardwareStorageConvert.INSTANCE.convertCreateList(reqDTO);
        hardwareStorageService.createHardwareStorageList(list);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> updates(List<HardWareStorageRespDTO> reqDTO) {
        if (reqDTO.size()>0){
            List<HardwareStorageDO> list = HardwareStorageConvert.INSTANCE.convertCreateList(reqDTO);
            hardwareStorageService.updateHardwareStorageList(list);
        }
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<List<HardWareStorageRespDTO>> getHardWareStorageByPlatformId(Long platformId) {
        return CommonResult.success(hardwareStorageService.getHardwareStorageByPlatformId(platformId));
    }

    @Override
    public int deletes(List<HardWareStorageRespDTO> reqDTO) {
        List<HardwareStorageDO> list = HardwareStorageConvert.INSTANCE.convertCreateList(reqDTO);
        return hardwareStorageService.deleteHardwareStorageList(list);
    }

    @Override
    public int deleteByPlatformId(Long platformId) {
        return hardwareStorageService.deleteByPlatformId(platformId);
    }
}
