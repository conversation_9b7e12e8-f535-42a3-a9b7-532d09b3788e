package cn.iocoder.zj.module.monitor.convert.networkvpc;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkVpcDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.networkvpc.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkvpc.NetworkVpcDO;

/**
 * VPC路由器 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface NetworkVpcConvert {

    NetworkVpcConvert INSTANCE = Mappers.getMapper(NetworkVpcConvert.class);

    NetworkVpcDO convert(NetworkVpcCreateReqVO bean);

    NetworkVpcDO convert(NetworkVpcUpdateReqVO bean);

    NetworkVpcRespVO convert(NetworkVpcDO bean);

    List<NetworkVpcRespVO> convertList(List<NetworkVpcDO> list);

    PageResult<NetworkVpcRespVO> convertPage(PageResult<NetworkVpcDO> page);

    List<NetworkVpcExcelVO> convertList02(List<NetworkVpcDO> list);

    List<NetworkVpcDO> convertCreateList(List<NetWorkVpcDTO> reqDTO);

    List<NetWorkVpcDTO> convertListDoToDto(List<NetworkVpcDO> netWorkVpcList);
}
