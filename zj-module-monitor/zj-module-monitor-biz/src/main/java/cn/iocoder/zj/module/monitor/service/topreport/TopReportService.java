package cn.iocoder.zj.module.monitor.service.topreport;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.monitor.controller.admin.topreport.vo.*;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface TopReportService {

    /**
     * 创建TOP报表
     */
    Long createTopReport(TopReportCreateReqVO createReqVO);

    /**
     * 更新TOP报表
     */
    void updateTopReport(TopReportUpdateReqVO updateReqVO);

    /**
     * 删除TOP报表
     */
    void deleteTopReport(Long id);

    /**
     * 获取TOP报表详情
     */
    TopReportRespVO getTopReport(Long id);

    /**
     * 获取TOP报表列表
     */
    PageResult<TopReportRespVO> getTopReportPage(TopReportPageReqVO pageVO);

    TopReportRespVO getdetail(TopReportRespVO topReportRespVO);

    List<ReportMetrics> getreportmetric(Long id);

    void exportReport(TopReportRespVO topReportRespVO, HttpServletResponse response);
}
