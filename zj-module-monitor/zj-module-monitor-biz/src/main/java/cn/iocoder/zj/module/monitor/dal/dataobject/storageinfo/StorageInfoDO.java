package cn.iocoder.zj.module.monitor.dal.dataobject.storageinfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 存储设备信息 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_storage_info")
@KeySequence("monitor_storage_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StorageInfoDO extends BaseDO {

    /**
     * 逐渐
     */
    @TableId
    private Long id;
    /**
     * 存储名称
     */
    private String name;
    /**
     * 存储uuid
     */
    private String uuid;
    /**
     * url
     */
    private String url;
    /**
     * 状态
     */
    private String state;
    /**
     * 类型：Ceph
     */
    private String type;
    /**
     * 状态，页面展示值（Connected：已连接，DisConnected: 未连接）
     */
    private String status;
    /**
     * 容量使用率
     */
    private BigDecimal capacityUtilization;
    /**
     * 总容量
     */
    private Long totalCapacity;
    /**
     * 已用容量
     */
    private Long usedCapacity;
    /**
     * 地区id
     */
    private Long regionId;

    private Long tenantId;


    private BigDecimal availablePhysicalCapacity;

    private BigDecimal totalPhysicalCapacity;

    private BigDecimal availableCapacity;

    private Integer deleted;
    // 平台配置id
    private Long platformId;
    // 平台配置名称
    private String platformName;

    private String typeName;

    private String clusterName;
    private String clusterUuid;

    private Date sCreateTime;

    //介质类型
    private String  mediaType;

    //介质类型id
    private Long  mediaTypeId;

    //超售比
    private BigDecimal storagePercent;

    //区域
    private String manager;

    //可用区域
    private String availableManager;

//    //分配率
//    private BigDecimal distributionRate;

    //预留容量
    private BigDecimal reserveCapacity;

    //浪费容量
    private BigDecimal wasteCapacity;

    //虚拟容量
    private BigDecimal virtualCapacity;
    //分配率
    private BigDecimal commitRate;
    //分配
    private BigDecimal allocation;
    //更新时间
    private Date vUpdateTime;
    //备注
    private String remark;

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("name", this.name);
        map.put("uuid", this.uuid);
        map.put("platformName", this.platformName);
        map.put("platformId",this.platformId);
        map.put("typeName","");
        return map;
    }
}
