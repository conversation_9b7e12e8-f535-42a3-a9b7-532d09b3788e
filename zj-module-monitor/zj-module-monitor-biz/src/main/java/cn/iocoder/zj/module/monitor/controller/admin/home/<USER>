package cn.iocoder.zj.module.monitor.controller.admin.home;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.zj.module.customer.api.custinfo.dto.CustinfoDTO;
import cn.iocoder.zj.module.customer.api.custinfo.dto.IndustryDTO;
import cn.iocoder.zj.module.monitor.controller.admin.home.vo.PersonSLAResponseExcelVO;
import cn.iocoder.zj.module.monitor.service.home.AdminScreenService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

/**
 * @ClassName : AdminScreenController  //类名
 * @Description : 管理者大屏  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/3/25  10:08
 */

@Tag(name = "大屏 - 管理者大屏")
@RestController
@RequestMapping("/monitor/adminScreen")
@Validated
public class AdminScreenController {

    @Autowired
    AdminScreenService adminScreenService;

    @GetMapping("/userAssetCount")
    @Operation(summary = "用户资产统计")
    @PreAuthenticated
    public CommonResult<Map> userAssetCount() {
        Map list = adminScreenService.userAssetCount();
        return CommonResult.success(list);
    }

    @GetMapping("/customers")
    @Operation(summary = "客户一览")
    @PreAuthenticated
    public CommonResult<List<Map>> getCustomers() {
        List<Map> list = adminScreenService.getCustomers();
        return CommonResult.success(list);
    }

    @GetMapping("/cloudPlatform")
    @Operation(summary = "云平台分布")
    @PreAuthenticated
    public CommonResult<List<Map>> cloudPlatform() {
        List<Map> list = adminScreenService.cloudPlatform();
        return CommonResult.success(list);
    }

    @GetMapping("/assetList")
    @Operation(summary = "资产数量")
    @PreAuthenticated
    public CommonResult<List<Map<String, Object>>> assetList() {
        List<Map<String, Object>> list = adminScreenService.assetList();
        return CommonResult.success(list);
    }

    @GetMapping("/customerIndustry")
    @Operation(summary = "客户行业分布")
    @PreAuthenticated
    public CommonResult<List<Map>> customerIndustry() {
        List<Map> list = adminScreenService.customerIndustry();
        return CommonResult.success(list);
    }
    @GetMapping("/person-SLAResponse")
    @Operation(summary = "工单SLA响应列表")
    @PreAuthenticated
    public CommonResult<List<Map>> getPersonSLAResponse(@RequestParam(value = "userName",required = false)String userName,@RequestParam(value = "startTime",required = false)String startTime,@RequestParam(value = "endTime",required = false)String endTime) {
        List<Map> list = adminScreenService.getPersonSLAResponse(userName,startTime,endTime);
        return CommonResult.success(list);
    }

    @GetMapping("/SLAResponse")
    @Operation(summary = "工单SLA响应统计")
    @PreAuthenticated
    public CommonResult<Map> getSLAResponse() {
        Map list = adminScreenService.getSLAResponse();
        return CommonResult.success(list);
    }
    @GetMapping("/exportPersonSLAResponse")
    @Operation(summary = "导出工单SLA响应 Excel")
    @OperateLog(type = EXPORT)
    @PreAuthorize("@ss.hasPermission('monitor:adminScreen:export')")
    public void exportPersonSLAResponse(@RequestParam(value = "userName",required = false)String userName,@RequestParam(value = "startTime",required = false)String startTime,@RequestParam(value = "endTime",required = false)String endTime,HttpServletResponse response) throws IOException {
        List<PersonSLAResponseExcelVO> datas = adminScreenService.exportPersonSLAResponse(userName,startTime,endTime);
        // 导出 Excel
        ExcelUtils.write(response, "工单SLA响应.xlsx", "数据", PersonSLAResponseExcelVO.class, datas);
    }
}
