package cn.iocoder.zj.module.monitor.controller.admin.home;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.service.home.devopsscreen.DevOpsScreenService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Tag(name = "大屏 - 运维大屏")
@RestController
@RequestMapping("/monitor/devOpsScreen")
@Validated
public class DevOpsScreenController {
@Resource
private DevOpsScreenService devOpsScreenService;

    @GetMapping("/resource-distribution")
    @Operation(summary = "资产类型分布及状态")
    @PreAuthenticated
    public CommonResult<List<Map>> getResourceDistribution() {
        List<Map> list = devOpsScreenService.getResourceDistribution();
        return CommonResult.success(list);
    }

    @GetMapping("/login-ranking")
    @Operation(summary = "登录排行")
    @PreAuthenticated
    public CommonResult<List<Map>> getLoginRanking() {
        List<Map> list = devOpsScreenService.getLoginRanking();
        return CommonResult.success(list);
    }
    @GetMapping("/running-asset-users")
    @Operation(summary = "在线资产数和用户总数")
    @PreAuthenticated
    public CommonResult<Map> getRunningAssetAndUsers() {
        Map list = devOpsScreenService.getRunningAssetAndUsers();
        return CommonResult.success(list);
    }

    @GetMapping("/abnormalUsage")
    @Operation(summary = "使用率异常")
    @Parameter(name = "platformId", description = "平台id", required = false)
    @PreAuthenticated
    public CommonResult<List<Map>> abnormalUsage(@RequestParam(required = false, defaultValue = "") Long platformId) {
        List<Map> list = devOpsScreenService.abnormalUsage(platformId);
        return CommonResult.success(list);
    }

    @GetMapping("/storageExceptions")
    @Operation(summary = "存储容量异常")
    @Parameter(name = "platformId", description = "平台id", required = false)
    @PreAuthenticated
    public CommonResult<List<Map>> storageExceptions(@RequestParam(required = false, defaultValue = "") Long platformId) {
        List<Map> list = devOpsScreenService.storageExceptions(platformId);
        return CommonResult.success(list);
    }
    @GetMapping("/todo-task")
    @Operation(summary = "待办工单")
    @Parameter(name = "platformId", description = "平台id", required = false)
    @PreAuthenticated
    @TenantIgnore
    public CommonResult<List<Map>> getTodoTask(@RequestParam(required = false, defaultValue = "") Long platformId) {
        List<Map> list = devOpsScreenService.getTodoTask(platformId);
        return CommonResult.success(list);
    }
}
