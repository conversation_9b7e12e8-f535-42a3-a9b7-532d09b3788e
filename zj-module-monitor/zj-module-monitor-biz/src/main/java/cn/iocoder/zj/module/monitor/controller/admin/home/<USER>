package cn.iocoder.zj.module.monitor.controller.admin.home;

import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.monitor.service.home.tenantscreen.TenantScreenService;
import cn.iocoder.zj.module.monitor.util.StringUtil;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

import static java.util.Collections.singleton;

/**
 * @ClassName : TenantScreenController  //类名
 * @Description : 租户大屏  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/2/26  14:31
 */
@Tag(name = "大屏 - 租户大屏")
@RestController
@RequestMapping("/monitor/tenantScreen")
@Validated
public class TenantScreenController {

    @Resource
    TenantScreenService tenantScreenService;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private RoleApi roleApi;


    @GetMapping("/resources-hz")
    @Operation(summary = "资源一栏")
    @Parameter(name = "tenantId", description = "租户id", required = false)
    @Parameter(name = "platformId", description = "平台id", required = false)
    @PreAuthenticated
    public CommonResult<Map> getResourcesList(@RequestParam(required = false, defaultValue = "") Long tenantId,
                                                    @RequestParam(required = false, defaultValue = "") Long platformId) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(loginUser.getId()).getData();
        List<String> tenantIds = new ArrayList<>();
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            if(StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
                tenantIds = Arrays.asList(String.valueOf(adminUserRespDTO.getTenantId()).split(","));
            }else {
                tenantIds = Arrays.asList(adminUserRespDTO.getServiceTenantId().split(","));
            }
        }
        Map list = tenantScreenService.getResourcesList(tenantIds,tenantId, platformId);
        return CommonResult.success(list);
    }


    @GetMapping("/screenAlarm")
    @Operation(summary = "告警消息")
    @Parameter(name = "tenantId", description = "租户id", required = false)
    @Parameter(name = "platformId", description = "平台id", required = false)
    @PreAuthenticated
    public CommonResult<List<Map<String, Object>>> getScreenAlarm(@RequestParam(required = false, defaultValue = "") Long tenantId,
                                                    @RequestParam(required = false, defaultValue = "") Long platformId) {
        List<Map<String, Object>> list = tenantScreenService.getScreenAlarm(tenantId, platformId);
        return CommonResult.success(list);
    }

    @GetMapping("/screenStatistics")
    @Operation(summary = "用量统计")
    @Parameter(name = "tenantId", description = "租户id", required = false)
    @Parameter(name = "platformId", description = "平台id", required = false)
    @PreAuthenticated
    public CommonResult<Map<String, Object>> getScreenStatistics(@RequestParam(required = false, defaultValue = "") Long tenantId,
                                                                  @RequestParam(required = false, defaultValue = "") Long platformId) {
        Map<String, Object> list = tenantScreenService.getScreenStatistics(tenantId, platformId);
        return CommonResult.success(list);
    }


    @GetMapping("/screenUsage")
    @Operation(summary = "使用率排行")
    @Parameter(name = "tenantId", description = "租户id", required = false)
    @Parameter(name = "platformId", description = "平台id", required = false)
    @Parameter(name = "time", description = "时间", required = false)
    @PreAuthenticated
    public CommonResult<Map<String, Object>> getScreenUsage(@RequestParam(required = false, defaultValue = "") Long tenantId,
                                                                 @RequestParam(required = false, defaultValue = "") Long platformId,
                                                            @RequestParam(required = false,defaultValue = "15m")String time) {
        Map<String, Object> list = tenantScreenService.getScreenUsage(tenantId, platformId,time,2L,0, tenantId);
        return CommonResult.success(list);
    }

}
