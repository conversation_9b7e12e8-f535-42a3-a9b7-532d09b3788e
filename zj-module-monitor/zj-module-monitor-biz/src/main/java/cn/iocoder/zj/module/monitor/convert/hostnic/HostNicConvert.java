package cn.iocoder.zj.module.monitor.convert.hostnic;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.api.hostnic.dto.HostNicCreateReqDto;
import cn.iocoder.zj.module.monitor.api.hostnic.dto.HostNicRespDTO;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL2DTO;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkl2.NetworkL2DO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.hostnic.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostnic.HostNicDO;

/**
 * 云主机网络 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface HostNicConvert {

    HostNicConvert INSTANCE = Mappers.getMapper(HostNicConvert.class);

    HostNicDO convert(HostNicCreateReqVO bean);

    HostNicDO convert(HostNicUpdateReqVO bean);

    HostNicRespVO convert(HostNicDO bean);

    List<HostNicRespVO> convertList(List<HostNicDO> list);

    PageResult<HostNicRespVO> convertPage(PageResult<HostNicDO> page);

    List<HostNicExcelVO> convertList02(List<HostNicDO> list);

    List<HostNicDO> convertCreateList(List<HostNicCreateReqDto> reqDTO);

    List<HostNicRespDTO> convertDoToDtoList(List<HostNicDO> reqDTO);

    List<HostNicCreateReqDto> convertDoToCreateDtoList(List<HostNicDO> reqDTO);
}
