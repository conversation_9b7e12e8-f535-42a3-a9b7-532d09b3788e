package cn.iocoder.zj.module.monitor.controller.admin.homeinfo;

import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.monitor.service.gatherasset.GatherAssetService;
import cn.iocoder.zj.module.monitor.service.hardwareinfo.HardwareInfoService;
import cn.iocoder.zj.module.monitor.service.homeinfo.HomeInfoService;
import cn.iocoder.zj.module.monitor.service.hostinfo.HostInfoService;
import cn.iocoder.zj.module.monitor.service.storageinfo.StorageInfoService;
import cn.iocoder.zj.module.monitor.util.StringUtil;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.UPDATE;
import static java.util.Collections.singleton;

@Tag(name = "首页 - 基本信息")
@RestController
@RequestMapping("/monitor/home-info")
@Validated
public class HomeInfoController {
    @Resource
    private HostInfoService hostInfoService;

    @Resource
    private HardwareInfoService hardwareInfoService;

    @Resource
    private StorageInfoService storageInfoService;

    @Resource
    private GatherAssetService gatherAssetService;

    @Resource
    private HomeInfoService homeInfoService;

    @Resource
    private RoleApi roleApi;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private AdminUserApi adminUserApi;


    @GetMapping("/all")
    @Operation(summary = "获得首页信息")
    @Parameter(name = "tenantId", description = "租户id", required = false)
    @Parameter(name = "platformId", description = "平台id", required = false)
    @PreAuthenticated
    public CommonResult<Map<String, Object>> getAllHomeInfo(@RequestParam(required = false, defaultValue = "") Long tenantId,
                                                            @RequestParam(required = false, defaultValue = "") Long platformId) {
        Map<String, Object> homeInfo = new HashMap<>();
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(loginUser.getId()).getData();
        List<String> tenantIds = new ArrayList<>();
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            if(StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
                tenantIds = Arrays.asList(String.valueOf(adminUserRespDTO.getTenantId()).split(","));
            }else {
                tenantIds = Arrays.asList(adminUserRespDTO.getServiceTenantId().split(","));
            }
        }

        Map<String, Object> hostNum = hostInfoService.getHostStatusCount(tenantIds, platformId);
        homeInfo.put("hostNum", hostNum);

        Map<String, Object> hardwareNum = hardwareInfoService.getHardwareStatusCount(tenantIds, platformId);
        homeInfo.put("hardwareNum", hardwareNum);

        Map<String, Object> hardwareCpuCapacity = hardwareInfoService.getCpuCapacity(tenantIds, platformId, null);
        homeInfo.put("hardwareCpuCapacity", hardwareCpuCapacity);

        Map<String, Object> hardwareMemoryCapacity = hardwareInfoService.getMemoryCapacity(tenantIds, platformId, null);
        homeInfo.put("hardwareMemoryCapacity", hardwareMemoryCapacity);

        Map<String, Object> storageCapacity = storageInfoService.getStorageCapacity(tenantIds, platformId, null);
        homeInfo.put("storageCapacity", storageCapacity);

        //List<AlarmInfoRespVo> alarmNum = homeInfoService.getAlarmNum(tenantId, platformId);
        //homeInfo.put("alarmNum", alarmNum);

        //List<Map<String, Object>> alarmInfo = homeInfoService.getAlarmInfo(tenantId, platformId);
        //homeInfo.put("alarmInfo", alarmInfo);

        List<String> monitorEntry = homeInfoService.getDMonitorEntry();
        homeInfo.put("monitorEntry", monitorEntry);

        Map<String, Object> gatherAssetNum = gatherAssetService.getGatherAssetStatusCount(tenantIds, platformId);
        Map<String, Object> storageNum = storageInfoService.getStorageStatusCount(tenantIds, platformId);
        Map map = new HashMap();
        map.put("otherStateNum", storageNum.get("other"));
        map.put("assetRunningNum", storageNum.get("online"));
        map.put("assetStoppedNum", storageNum.get("unonline"));
        map.put("assteNum", storageNum.get("total"));

        homeInfo.put("gatherAssetNum", map);
        return success(homeInfo);
    }

    @GetMapping("/getLayoutConfig")
    @Operation(summary = "获取自定义布局")
    @Parameter(name = "tenantId", description = "租户id", required = true, example = "1")
    @PreAuthenticated
    public CommonResult<Map<String, Object>> getLayoutConfig(@RequestParam(required = false, defaultValue = "") Long tenantId, @RequestParam(required = false, defaultValue = "") Long platformId) {
        Map<String, Object> layoutConfig = homeInfoService.getLayoutConfig(tenantId);
        return success(layoutConfig);
    }

    @GetMapping("/monitorModule")
    @Operation(summary = "监控条目数据")
    @Parameter(name = "tenantId", description = "租户id", required = true)
    @Parameter(name = "platformId", description = "平台id", required = false)
    @Parameter(name = "regionId", description = "地区id", required = false)
    @Parameter(name = "monitorEntry", description = "监控条目名称", required = true, example = "")
    @PreAuthenticated
    public CommonResult<Map<String, Object>> monitorModule(@RequestParam(required = true, defaultValue = "") Long tenantId,
                                                           @RequestParam(required = false, defaultValue = "") Long platformId,
                                                           @RequestParam(required = false, defaultValue = "") Long regionId,
                                                           @RequestParam("monitorEntry") String monitorEntry) {
        Map<String, Object> homeInfo = new HashMap<>();

        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(loginUser.getId()).getData();
        List<String> tenantIds = new ArrayList<>();
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            if(StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
                tenantIds = Arrays.asList(String.valueOf(adminUserRespDTO.getTenantId()).split(","));
            }else {
                tenantIds = Arrays.asList(adminUserRespDTO.getServiceTenantId().split(","));
            }
        }

        if (monitorEntry.equals("usedStatistics")) {
            Map<String, Object> hardwareCpuCapacity = hardwareInfoService.getCpuCapacity(tenantIds, platformId, regionId);
            homeInfo.put("hardwareCpuCapacity", hardwareCpuCapacity);
            Map<String, Object> hardwareMemoryCapacity = hardwareInfoService.getMemoryCapacity(tenantIds, platformId, regionId);
            homeInfo.put("hardwareMemoryCapacity", hardwareMemoryCapacity);
            Map<String, Object> storageCapacity = storageInfoService.getStorageCapacity(tenantIds, platformId, regionId);
            homeInfo.put("storageCapacity", storageCapacity);
        } else if (monitorEntry.equals("hostCpuTop")) {
            List<Map<String, Object>> hostCpuTop = hostInfoService.getCpuTop(tenantIds, platformId, regionId, 5);
            homeInfo.put("hostCpuTop", hostCpuTop);
        } else if (monitorEntry.equals("hostMemoryTop")) {
            List<Map<String, Object>> hostMemoryTop = hostInfoService.getMemoryTop(tenantIds, platformId, regionId, 5);
            homeInfo.put("hostMemoryTop", hostMemoryTop);
        } else if (monitorEntry.equals("hostCpuTop5")) {
            List<Map<String, Object>> hostCpuTop = hostInfoService.getCpuTop(tenantIds, platformId, regionId, 5);
            homeInfo.put("hostCpuTop5", hostCpuTop);
        } else if (monitorEntry.equals("hostMemoryTop5")) {
            List<Map<String, Object>> hostMemoryTop = hostInfoService.getMemoryTop(tenantIds, platformId, regionId, 5);
            homeInfo.put("hostMemoryTop5", hostMemoryTop);
        } else if (monitorEntry.equals("hardwareCpuTop")) {
            List<Map<String, Object>> hardwareCpuTop = hardwareInfoService.getCpuTop(tenantIds, platformId, regionId, 5);
            homeInfo.put("hardwareCpuTop", hardwareCpuTop);
        } else if (monitorEntry.equals("hardwareMemoryTop")) {
            List<Map<String, Object>> hardwareMemoryTop = hardwareInfoService.getMemoryTop(tenantIds, platformId, regionId, 5);
            homeInfo.put("hardwareMemoryTop", hardwareMemoryTop);
        } else if (monitorEntry.equals("hardwareCpuTop5")) {
            List<Map<String, Object>> hardwareCpuTop = hardwareInfoService.getCpuTop(tenantIds, platformId, regionId, 5);
            homeInfo.put("hardwareCpuTop5", hardwareCpuTop);
        } else if (monitorEntry.equals("hardwareMemoryTop5")) {
            List<Map<String, Object>> hardwareMemoryTop = hardwareInfoService.getMemoryTop(tenantIds, platformId, regionId, 5);
            homeInfo.put("hardwareMemoryTop5", hardwareMemoryTop);
        }
        return success(homeInfo);
    }

    @GetMapping("/getStatusTrend")
    @Operation(summary = "资源走势预览")
    @Parameter(name = "tenantId", description = "租户id", required = true)
    @Parameter(name = "platformId", description = "平台id", required = false)
    @Parameter(name = "regionId", description = "地区id", required = false)
    @Parameter(name = "monitorEntry", description = "监控条目名称", required = true, example = "")
    @Parameter(name = "time", description = "时间条件，一周:7d，15天:15d，一个月:30d")
    @PreAuthenticated
    public CommonResult<Map<String, Object>> getStatusTrend(@RequestParam(required = true, defaultValue = "") Long tenantId,
                                                            @RequestParam(required = false, defaultValue = "") Long platformId,
                                                            @RequestParam(required = false, defaultValue = "") Long regionId,
                                                            @RequestParam("monitorEntry") String monitorEntry,
                                                            @RequestParam("time") String time) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(loginUser.getId()).getData();
        List<String> tenantIds = new ArrayList<>();
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            if(StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
                tenantIds = Arrays.asList(String.valueOf(adminUserRespDTO.getTenantId()).split(","));
            }else {
                tenantIds = Arrays.asList(adminUserRespDTO.getServiceTenantId().split(","));
            }
        }
        if (roleApi.hasAnySuperAdmin(roleIds)) {
            tenantId = null;
        }
        if (monitorEntry.equals("storageUsedCapacity")) {
            return success(storageInfoService.getAllUsedCapacityInPercent(tenantIds, platformId, regionId, time));
        } else {
            return success(hardwareInfoService.getMonitorInfo(tenantIds, platformId, regionId, time, monitorEntry));
        }
    }


    @GetMapping("/getDisplayModule")
    @Operation(summary = "查询自定义显示模块")
    @Parameter(name = "tenantId", description = "租户id", required = true, example = "1")
    @PreAuthenticated
    public CommonResult<Map<String, Object>> getDisplayModule(@RequestParam("tenantId") Long tenantId) {
        return success(homeInfoService.getDisplayModule(tenantId));
    }

    @GetMapping("/getResourceType")
    @Operation(summary = "查询自定义显示模块的选项")
    @Parameter(name = "tenantId", description = "租户id", required = true, example = "1")
    @Parameter(name = "module", description = "首页模块", required = true)
    @PreAuthenticated
    public CommonResult<Map<String, Object>> getResourceType(@RequestParam("tenantId") Long tenantId, @RequestParam("module") String module) {
        return success(homeInfoService.getResourceType(tenantId, module));
    }

    @GetMapping("/updateDisplayModule")
    @Operation(summary = "修改首页显示布局")
    @Parameter(name = "tenantId", description = "租户id", required = true, example = "1")
    @Parameter(name = "layoutConfig", description = "布局配置", required = true)
    @PreAuthenticated
    @PreAuthorize("@ss.hasPermission('monitor:Home-info:update')")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateDisplayModule(@RequestParam("tenantId") Long tenantId, String layoutConfig) {
        homeInfoService.updateDisplay(tenantId, layoutConfig);
        return success(true);
    }
/*

    @GetMapping("/alarmMap")
    @Operation(summary = "获取大屏地图信息")
    @Parameter(name = "tenantId", description = "租户编号", required = true, example = "1")
    @Parameter(name = "regionId", description = "地区id")
    public CommonResult<List<Map<String, Object>>> getAlarmMap(@RequestParam(required = true, defaultValue = "") Long tenantId,
                                                               @RequestParam(required = false, defaultValue = "") Long regionId) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        if (roleApi.hasAnySuperAdmin(roleIds)) {
            tenantId = null;
        }
        return success(homeInfoService.getAlarmMap(tenantId, regionId));
    }
*/

}
