package cn.iocoder.zj.module.monitor.convert.volumesnapshot;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeDTO;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeSnapshotDTO;
import cn.iocoder.zj.module.monitor.controller.admin.volumeinfo.vo.VolumeInfoUpdateReqVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.volumeinfo.VolumeInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.volumesnapshot.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.volumesnapshot.VolumeSnapshotDO;

/**
 * 云盘快照信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface VolumeSnapshotConvert {

    VolumeSnapshotConvert INSTANCE = Mappers.getMapper(VolumeSnapshotConvert.class);

    VolumeSnapshotDO convert(VolumeSnapshotCreateReqVO bean);

    VolumeSnapshotDO convert(VolumeSnapshotUpdateReqVO bean);

    VolumeSnapshotRespVO convert(VolumeSnapshotDO bean);

    List<VolumeSnapshotRespVO> convertList(List<VolumeSnapshotDO> list);
    
    List<VolumeSnapshotExcelVO> convertList02(List<VolumeSnapshotDO> list);



    List<VolumeSnapshotDTO> DOConvertToDTO(List<VolumeSnapshotDO> volumeSnapshotDOS);

    VolumeInfoUpdateReqVO singleDTOConvertToUpdateVo(VolumeDTO volumeDTO);

    PageResult<VolumeSnapshotRespVO> convertPage(PageResult<VolumeSnapshotDO> pageResult);

    List<VolumeSnapshotDO> DTOConvertToDO(List<VolumeSnapshotDTO> volumeSnapshotDTOList);
}
