package cn.iocoder.zj.module.monitor.dal.dataobject.gatherdevice;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;

import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 采集设备 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_gather_device")
@KeySequence("monitor_gather_device_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GatherDeviceDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 采集设备uuid
     */
    private String uuid;
    /**
     * 采集设备ip
     */
    private String gatherIp;
    /**
     * 采集设备名称
     */
    private String gatherName;
    /**
     * 0 未绑定 ,1 已绑定
     */
    private Integer type;


    private String platformName;

    private String platformId;

    /**
     * 在线状态，0 离线，1在线
     */
    private String onlineType;

}
