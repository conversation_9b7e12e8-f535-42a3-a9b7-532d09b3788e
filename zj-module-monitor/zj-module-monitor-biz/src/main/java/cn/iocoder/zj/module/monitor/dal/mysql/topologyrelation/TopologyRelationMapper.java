package cn.iocoder.zj.module.monitor.dal.mysql.topologyrelation;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.monitor.dal.dataobject.topologyrelation.TopologyRelationDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.monitor.controller.admin.topologyrelation.vo.*;

/**
 * 拓扑图关系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TopologyRelationMapper extends BaseMapperX<TopologyRelationDO> {

    default PageResult<TopologyRelationDO> selectPage(TopologyRelationPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TopologyRelationDO>()
                .eqIfPresent(TopologyRelationDO::getTopologyId, reqVO.getTopologyId())
                .eqIfPresent(TopologyRelationDO::getMonitorId, reqVO.getMonitorId())
                .likeIfPresent(TopologyRelationDO::getMonitorName, reqVO.getMonitorName())
                .eqIfPresent(TopologyRelationDO::getMonitorInterfaces, reqVO.getMonitorInterfaces())
                .betweenIfPresent(TopologyRelationDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TopologyRelationDO::getId));
    }

    default List<TopologyRelationDO> selectList(TopologyRelationExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TopologyRelationDO>()
                .eqIfPresent(TopologyRelationDO::getTopologyId, reqVO.getTopologyId())
                .eqIfPresent(TopologyRelationDO::getMonitorId, reqVO.getMonitorId())
                .likeIfPresent(TopologyRelationDO::getMonitorName, reqVO.getMonitorName())
                .eqIfPresent(TopologyRelationDO::getMonitorInterfaces, reqVO.getMonitorInterfaces())
                .betweenIfPresent(TopologyRelationDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TopologyRelationDO::getId));
    }

}
