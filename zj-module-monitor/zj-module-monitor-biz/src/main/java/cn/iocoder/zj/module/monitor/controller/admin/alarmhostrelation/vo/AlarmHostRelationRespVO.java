package cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 告警配置与云主机关联关系 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AlarmHostRelationRespVO extends AlarmHostRelationBaseVO {

    @Schema(description = "主键ID", required = true)
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
