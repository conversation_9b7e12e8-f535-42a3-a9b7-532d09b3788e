package cn.iocoder.zj.module.monitor.service.alarmnotice;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.zj.module.monitor.controller.admin.alarmnotice.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmnotice.AlarmNoticeDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.convert.alarmnotice.AlarmNoticeConvert;
import cn.iocoder.zj.module.monitor.dal.mysql.alarmnotice.AlarmNoticeMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.*;

/**
 * 告警与通知模板关系 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AlarmNoticeServiceImpl implements AlarmNoticeService {

    @Resource
    private AlarmNoticeMapper alarmNoticeMapper;

    @Override
    public Long createAlarmNotice(AlarmNoticeCreateReqVO createReqVO) {
        // 插入
        AlarmNoticeDO alarmNotice = AlarmNoticeConvert.INSTANCE.convert(createReqVO);
        alarmNoticeMapper.insert(alarmNotice);
        // 返回
        return alarmNotice.getId();
    }

    @Override
    public void updateAlarmNotice(AlarmNoticeUpdateReqVO updateReqVO) {
        // 校验存在
        validateAlarmNoticeExists(updateReqVO.getId());
        // 更新
        AlarmNoticeDO updateObj = AlarmNoticeConvert.INSTANCE.convert(updateReqVO);
        alarmNoticeMapper.updateById(updateObj);
    }

    @Override
    public void deleteAlarmNotice(Long id) {
        // 校验存在
        validateAlarmNoticeExists(id);
        // 删除
        alarmNoticeMapper.deleteById(id);
    }

    private void validateAlarmNoticeExists(Long id) {
        if (alarmNoticeMapper.selectById(id) == null) {
            throw exception(ALARM_NOTICE_NOT_EXISTS);
        }
    }

    @Override
    public AlarmNoticeDO getAlarmNotice(Long id) {
        return alarmNoticeMapper.selectById(id);
    }

    @Override
    public List<AlarmNoticeDO> getAlarmNoticeList(Collection<Long> ids) {
        return alarmNoticeMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<AlarmNoticeDO> getAlarmNoticePage(AlarmNoticePageReqVO pageReqVO) {
        return alarmNoticeMapper.selectPage(pageReqVO);
    }

    @Override
    public List<AlarmNoticeDO> getAlarmNoticeList(AlarmNoticeExportReqVO exportReqVO) {
        return alarmNoticeMapper.selectList(exportReqVO);
    }

}
