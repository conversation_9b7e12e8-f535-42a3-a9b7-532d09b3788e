package cn.iocoder.zj.module.monitor.controller.admin.networkl3.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

/**
* 三级网络资源 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class NetworkL3BaseVO {

    @Schema(description = "三级网络uuid")
    private String uuid;

    @Schema(description = "二级网络uuid")
    private String l2NetworkUuid;
    @Schema(description = "二级网络名称")
    private String l2NetworkName;
    @Schema(description = "三级网络名称")
    private String name;

    @Schema(description = "dns 逗号分割")
    private String dns;

    @Schema(description = "网络资源类型（L3BasicNetwork 公有网络， L3VpcNetwork  vpc 网络 ）")
    private String type;

    @Schema(description = "网络服务 逗号分割")
    private String networkServices;

    @Schema(description = "起始ip")
    private String startIp;

    @Schema(description = "结束ip")
    private String endIp;

    @Schema(description = "子网掩码")
    private String netmask;

    @Schema(description = "网关")
    private String gateway;

    @Schema(description = "网段名称")
    private String networkSegment;

    @Schema(description = "		IPv4 CIDR")
    private String networkCidr;

    @Schema(description = "IPV4 DHCP")
    private String nextHopIp;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

}
