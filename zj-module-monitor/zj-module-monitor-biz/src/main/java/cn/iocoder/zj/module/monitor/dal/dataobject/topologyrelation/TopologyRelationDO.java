package cn.iocoder.zj.module.monitor.dal.dataobject.topologyrelation;

import lombok.*;

import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 拓扑图关系 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_topology_relation")
@KeySequence("monitor_topology_relation_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TopologyRelationDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 拓扑图id
     */
    private Long topologyId;
    /**
     * 监控id
     */
    private String monitorId;
    /**
     * 监控设备名称
     */
    private String monitorName;
    /**
     * 监控设备接口名称
     */
    private String monitorInterfaces;

}
