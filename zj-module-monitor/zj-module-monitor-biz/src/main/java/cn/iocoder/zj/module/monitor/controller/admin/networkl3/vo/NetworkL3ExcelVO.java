package cn.iocoder.zj.module.monitor.controller.admin.networkl3.vo;

import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 三级网络资源 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class NetworkL3ExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("三级网络uuid")
    private String uuid;

    @ExcelProperty("二级网络uuid")
    private String l2NetworkUuid;
    @Schema(description = "二级网络名称")
    private String l2NetworkName;
    @ExcelProperty("三级网络名称")
    private String name;

    @ExcelProperty("dns 逗号分割")
    private String dns;

    @ExcelProperty("网络资源类型（L3BasicNetwork 公有网络， L3VpcNetwork  vpc 网络 ）")
    private String type;

    @ExcelProperty("网络服务 逗号分割")
    private String networkServices;

    @ExcelProperty("起始ip")
    private String startIp;

    @ExcelProperty("结束ip")
    private String endIp;

    @ExcelProperty("子网掩码")
    private String netmask;

    @ExcelProperty("网关")
    private String gateway;

    @ExcelProperty("网段名称")
    private String networkSegment;

    @ExcelProperty("		IPv4 CIDR")
    private String networkCidr;

    @ExcelProperty("IPV4 DHCP")
    private String nextHopIp;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private LocalDateTime createTime;

    @ExcelProperty("平台id")
    private Long platformId;

    @ExcelProperty("平台名称")
    private String platformName;

}
