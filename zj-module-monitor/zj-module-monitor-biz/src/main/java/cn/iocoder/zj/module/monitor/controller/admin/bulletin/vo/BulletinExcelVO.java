package cn.iocoder.zj.module.monitor.controller.admin.bulletin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 实时报 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class BulletinExcelVO {

    @ExcelProperty("ID")
    private Long id;

    @ExcelProperty("名称")
    private String name;

    @ExcelProperty("描述")
    private String description;

    @ExcelProperty("监控资产类型")
    private String category;

    @ExcelProperty("监控资产小类")
    private String app;

    @ExcelProperty("监控选项")
    private String fields;

    @ExcelProperty("资产ids")
    private String assetIds;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
