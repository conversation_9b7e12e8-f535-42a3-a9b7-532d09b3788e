package cn.iocoder.zj.module.monitor.handler;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.iocoder.zj.module.monitor.service.zstack.IZstackCommon;
import com.alibaba.fastjson.JSONObject;
import feign.RequestInterceptor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class HttpClientWrapper {
    private List<IZstackCommon> interceptors = new ArrayList<>();

    public void addInterceptor(IZstackCommon interceptor) {
        interceptors.add(interceptor);
    }

    public HttpResponse execute(HttpRequest request) {
        // 依次执行所有拦截器
        for (IZstackCommon interceptor : interceptors) {
            request = interceptor.intercept(request);
        }
        System.out.println("请求参数："+ JSONObject.toJSONString(request));
        // 执行最终的HTTP请求
        return request.execute();
    }

}
