package cn.iocoder.zj.module.monitor.controller.admin.networkl2.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

/**
* 二级网络信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class NetworkL2BaseVO {

    @Schema(description = "二级网络名称")
    private String name;

    @Schema(description = "二级网络uuid")
    private String uuid;

    @Schema(description = "网卡")
    private String physicalInterface;

    @Schema(description = "二级网络类型")
    private String type;

    @Schema(description = "vlan")
    private String vlan;

    @Schema(description = "虚拟网络标识")
    private Integer virtualNetworkId;

    @Schema(description = "地区名称")
    private String regionName;

    @Schema(description = "地区id")
    private Long regionId;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "租户编号")
    private Long tenantId;
}
