package cn.iocoder.zj.module.monitor.service.home.tenantscreen;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface TenantScreenService {
    Map getResourcesList(List<String> tenantIds, Long tenantId,Long platformId);

    List<Map<String, Object>> getScreenAlarm(Long tenantId, Long platformId);

    Map<String, Object> getScreenStatistics(@Param("tenantId") Long tenantId, @Param("platformId") Long platformId);

    Map<String, Object> getScreenUsage(Long userId, Long platformId, String time, Long type, Integer status, Long userTenantId);
}
