package cn.iocoder.zj.module.monitor.convert.hardwarenic;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespDTO;
import cn.iocoder.zj.module.monitor.api.hardwarenic.HardWareNicRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.hardwarenic.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.hardwarenic.HardwareNicDO;

/**
 * 物理机网络关系 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface HardwareNicConvert {

    HardwareNicConvert INSTANCE = Mappers.getMapper(HardwareNicConvert.class);

    HardwareNicDO convert(HardwareNicCreateReqVO bean);

    HardwareNicDO convert(HardwareNicUpdateReqVO bean);

    HardwareNicRespVO convert(HardwareNicDO bean);

    List<HardwareNicRespVO> convertList(List<HardwareNicDO> list);

    PageResult<HardwareNicRespVO> convertPage(PageResult<HardwareNicDO> page);

    List<HardwareNicExcelVO> convertList02(List<HardwareNicDO> list);


    List<HardwareNicDO> convertCreateList(List<HardWareNicRespDTO> reqDTO);
}
