package cn.iocoder.zj.module.monitor.controller.admin.storageinfo.vo;

import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 存储设备信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class StorageInfoPageReqVO extends PageParam {

    @Schema(description = "查询条件，存储名称")
    private String name;

    @Schema(description = "存储uuid")
    private String uuid;

    @Schema(description = "url")
    private String url;

    @Schema(description = "状态:启用Enabled，未启用Disabled")
    private String state;

    @Schema(description = "类型：Ceph")
    private String type;

    @Schema(description = "查询条件，状态，页面展示值（Connected：已连接，DisConnected: 未连接）")
    private String status;

    @Schema(description = "容量使用率")
    private BigDecimal capacityUtilization;

    @Schema(description = "总容量")
    private BigDecimal totalCapacity;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(required = false,description = "开始时间",example = "2023-06-07")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String startTime;

    @Schema(required = false,description = "结束时间",example = "2023-06-07")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String endTime;

    @Schema(description = "地区id")
    private Long regionId;


    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "租户ID")
    private List<String> tenantId;

    @Schema(description = "宿主机uuid")
    private String hardWareUuid;

    @Schema(required = false,description = "主机过滤ids逗号拼接,'1','2','3'")
    private String ids;

    @Schema(required = false,description = "主机tagId逗号拼接,1,2,3")
    private String tagIds;

    @Schema(required = false,description = "主键逗号拼接,'1','2','3'")
    private List<Long> inPks;
}
