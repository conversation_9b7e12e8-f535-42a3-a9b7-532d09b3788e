package cn.iocoder.zj.module.monitor.controller.admin.gatherlogdetail;

import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;

import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.singleton;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.monitor.controller.admin.gatherlogdetail.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.gatherlogdetail.GatherLogdetailDO;
import cn.iocoder.zj.module.monitor.convert.gatherlogdetail.GatherLogdetailConvert;
import cn.iocoder.zj.module.monitor.service.gatherlogdetail.GatherLogdetailService;

@Tag(name = "管理后台 - 告警日志")
@RestController
@RequestMapping("/monitor/gather-logdetail")
@Validated
public class GatherLogdetailController {

    @Resource
    private GatherLogdetailService gatherLogdetailService;

//    @PostMapping("/create")
//    @Operation(summary = "创建告警日志")
//    @PreAuthorize("@ss.hasPermission('monitor:gather-logdetail:create')")
//    public CommonResult<Long> createGatherLogdetail(@Valid @RequestBody GatherLogdetailCreateReqVO createReqVO) {
//        return success(gatherLogdetailService.createGatherLogdetail(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新告警日志")
//    @PreAuthorize("@ss.hasPermission('monitor:gather-logdetail:update')")
//    public CommonResult<Boolean> updateGatherLogdetail(@Valid @RequestBody GatherLogdetailUpdateReqVO updateReqVO) {
//        gatherLogdetailService.updateGatherLogdetail(updateReqVO);
//        return success(true);
//    }

//    @DeleteMapping("/delete")
//    @Operation(summary = "删除告警日志")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('monitor:gather-logdetail:delete')")
//    public CommonResult<Boolean> deleteGatherLogdetail(@RequestParam("id") Long id) {
//        gatherLogdetailService.deleteGatherLogdetail(id);
//        return success(true);
//    }

    @GetMapping("/get")
    @Operation(summary = "获得告警日志")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthenticated
    @TenantIgnore
    public CommonResult<GatherLogdetailRespVO> getGatherLogdetail(@RequestParam("id") Long id) {
        return success(gatherLogdetailService.getGatherLogdetail(id));
    }
//
//    @GetMapping("/list")
//    @Operation(summary = "获得告警日志列表")
//    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
//    @PreAuthorize("@ss.hasPermission('monitor:gather-logdetail:query')")
//    public CommonResult<List<GatherLogdetailRespVO>> getGatherLogdetailList(@RequestParam("ids") Collection<Long> ids) {
//        List<GatherLogdetailDO> list = gatherLogdetailService.getGatherLogdetailList(ids);
//        return success(GatherLogdetailConvert.INSTANCE.convertList(list));
//    }

    @GetMapping("/pageInfo")
    @Operation(summary = "获得告警日志分页")
    @PreAuthenticated
    @TenantIgnore
    public CommonResult<PageResult<GatherLogdetailRespVO>> getGatherLogdetailPage(@Valid GatherLogdetailPageReqVO pageVO) {
        PageResult<GatherLogdetailDO> pageResult = gatherLogdetailService.getGatherLogdetailPage(pageVO);
        return success(GatherLogdetailConvert.INSTANCE.convertPage(pageResult));
    }
    @GetMapping("/page")
    @Operation(summary = "获得告警日志分页")
    @PreAuthenticated
    @TenantIgnore
    public CommonResult<PageResult<GatherLogdetailRespVO>> getGatherLogDetailPageInfo(@Valid GatherLogdetailPageReqVO pageVO) {
        PageResult<GatherLogdetailRespVO> pageResult = gatherLogdetailService.getGatherLogDetailPageInfo(pageVO);
        return success(pageResult);
    }
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出告警日志 Excel")
//    @PreAuthorize("@ss.hasPermission('monitor:gather-logdetail:export')")
//    @OperateLog(type = EXPORT)
//    public void exportGatherLogdetailExcel(@Valid GatherLogdetailExportReqVO exportReqVO,
//              HttpServletResponse response) throws IOException {
//        List<GatherLogdetailDO> list = gatherLogdetailService.getGatherLogdetailList(exportReqVO);
//        // 导出 Excel
//        List<GatherLogdetailExcelVO> datas = GatherLogdetailConvert.INSTANCE.convertList02(list);
//        ExcelUtils.write(response, "告警日志.xls", "数据", GatherLogdetailExcelVO.class, datas);
//    }

    @PutMapping("/updateState")
    @Operation(summary = "修改告警状态")
    @PreAuthenticated
    @TenantIgnore
    @PermitAll
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateState(@RequestParam("ids") Collection<Long> ids) {
        gatherLogdetailService.updateState(ids);
        return success(true);
    }


    @GetMapping("/alarmNoticeList")
    @Operation(summary = "获得告警通知列表")
    @PreAuthenticated
    @TenantIgnore
    public CommonResult<List<GatherLogdetailRespVO>> alarmNoticeList(@RequestParam(required = false, defaultValue = "") Long tenantId) {
        List<GatherLogdetailDO> list = gatherLogdetailService.alarmNoticeList(tenantId);
        return success(GatherLogdetailConvert.INSTANCE.convertList(list));
    }


    @TenantIgnore
    @PermitAll
    @PostMapping("/reportingLog")
    @Operation(summary = "告警日志上报")
    @ResponseBody
    public Map reportingLog(@RequestBody String json,@RequestParam String token_desc) throws IOException {
        return gatherLogdetailService.reportingLog(json,token_desc);
    }

    @GetMapping("/latestLog")
    @Operation(summary = "最新5条告警")
    @PreAuthenticated
    @TenantIgnore
    public CommonResult<List<GatherLogdetailRespVO>> getLatestLog() {
        return success(gatherLogdetailService.getLatestLog());
    }

    @PutMapping("/updateIsSolved")
    @Operation(summary = "修改是否解决")
    @PreAuthenticated
    @TenantIgnore
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateIsSolved(@RequestParam("ids") Collection<Long> ids) {
        gatherLogdetailService.updateIsSolved(ids);
        return success(true);
    }

}
