package cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
@ExcelIgnoreUnannotated
public class BulletinNetworkL2 {
    private Long id;

    @ExcelProperty("二层网络名称")
    private String name;

    @ExcelProperty("平台名称")
    private String platformName;

    @ExcelProperty("uuid")
    private String uuid;

    @ExcelProperty("网卡")
    private String physicalInterface;

    @ExcelProperty("类型")
    private String type;

    @ExcelProperty("vlan ID/VNI")
    private String vlan;

    @ExcelProperty("虚拟网络标识")
    private Integer virtualNetworkId;
}
