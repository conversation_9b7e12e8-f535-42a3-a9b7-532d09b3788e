package cn.iocoder.zj.module.monitor.dal.dataobject.secgroup;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 安全组 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_secgroup")
@KeySequence("monitor_secgroup_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SecgroupDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * uuid
     */
    private String uuid;
    /**
     * 名称
     */
    private String name;
    /**
     * 状态
     */
    private String status;
    /**
     * 是否共享
     */
    private Boolean isPublic;
    /**
     * 默认共享范围
     */
    private String publicScope;
    /**
     * 共享设置的来源, local: 本地设置, cloud: 从云上同步过来
     */
    private String publicSrc;
    /**
     * 是否垃圾
     */
    private Boolean isDirty;
    /**
     * 区域id
     */
    private String cloudregionId;
    /**
     * vpcId
     */
    private String vpcId;
    /**
     * 全局vpcId
     */
    private String globalvpcId;
    /**
     * 备注
     */
    private String description;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;

}
