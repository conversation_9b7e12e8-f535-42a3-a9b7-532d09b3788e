package cn.iocoder.zj.module.monitor.service.secgroup;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.api.secgroup.dto.SecgroupRespDto;
import cn.iocoder.zj.module.monitor.controller.admin.secgroup.vo.SecgroupCreateReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.secgroup.vo.SecgroupExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.secgroup.vo.SecgroupPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.secgroup.vo.SecgroupUpdateReqVO;
import cn.iocoder.zj.module.monitor.convert.secgroup.SecgroupConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostsecgroup.HostSecgroupDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.secgroup.SecgroupDO;
import cn.iocoder.zj.module.monitor.dal.mysql.hostsecgroup.HostSecgroupMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.secgroup.SecgroupMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.SECGROUP_NOT_EXISTS;

/**
 * 安全组 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SecgroupServiceImpl implements SecgroupService {

    @Resource
    private SecgroupMapper secgroupMapper;
    @Resource
    private HostSecgroupMapper hostSecgroupMapper;

    @Override
    public Long createSecgroup(SecgroupCreateReqVO createReqVO) {
        // 插入
        SecgroupDO secgroup = SecgroupConvert.INSTANCE.convert(createReqVO);
        secgroupMapper.insert(secgroup);
        // 返回
        return secgroup.getId();
    }

    @Override
    public void updateSecgroup(SecgroupUpdateReqVO updateReqVO) {
        // 校验存在
        validateSecgroupExists(updateReqVO.getId());
        // 更新
        SecgroupDO updateObj = SecgroupConvert.INSTANCE.convert(updateReqVO);
        secgroupMapper.updateById(updateObj);
    }

    @Override
    public void deleteSecgroup(Long id) {
        // 校验存在
        validateSecgroupExists(id);
        // 删除
        secgroupMapper.deleteById(id);
    }

    private void validateSecgroupExists(Long id) {
        if (secgroupMapper.selectById(id) == null) {
            throw exception(SECGROUP_NOT_EXISTS);
        }
    }

    @Override
    @TenantIgnore
    public SecgroupDO getSecgroup(Long id) {
        return secgroupMapper.selectById(id);
    }

    @Override
    public List<SecgroupDO> getSecgroupList(Collection<Long> ids) {
        return secgroupMapper.selectBatchIds(ids);
    }

    @Override
    @TenantIgnore
    public PageResult<SecgroupDO> getSecgroupPage(SecgroupPageReqVO pageReqVO) {
        if (pageReqVO.getCloudHostUuid() != null) {
            // 查询关联的安全组列表
            LambdaQueryWrapperX<HostSecgroupDO> lqw = new LambdaQueryWrapperX<>();
            lqw.eq(HostSecgroupDO::getHostUuid, pageReqVO.getCloudHostUuid())
                    .eq(HostSecgroupDO::getDeleted, 0);

            List<HostSecgroupDO> list = hostSecgroupMapper.selectList(lqw);

            // 去重并提取 UUID
            List<String> result = list.stream()
                    .map(HostSecgroupDO::getSecgroupUuid)
                    .distinct()
                    .collect(Collectors.toList());

            if (result.size() == 0){
                return PageResult.empty();
            }
            pageReqVO.setUuids(result);
        }
        return secgroupMapper.selectPage(pageReqVO);
    }

    @Override
    public List<SecgroupDO> getSecgroupList(SecgroupExportReqVO exportReqVO) {
        return secgroupMapper.selectList(exportReqVO);
    }

    @Override
    public void createSecgroupList(List<SecgroupDO> list) {
        secgroupMapper.insertBatch(list);
    }

    @Override
    public void updateSecgroups(List<SecgroupDO> list) {
        secgroupMapper.updateBatch(list);
    }

    @Override
    public void deleteSecgroups(List<SecgroupDO> list) {
        List<Long> ids = list.stream().map(SecgroupDO::getId).toList();
        secgroupMapper.deleteBatchIds(ids);
    }

    @Override
    public List<SecgroupDO> getSecgroupListByHostUuid(String uuid) {
        return secgroupMapper.getSecgroupListByHostUuid(uuid);
    }

    @Override
    public List<SecgroupRespDto> getSecgroupListByHostUuids(List<String> uuids) {
        return secgroupMapper.getSecgroupListByHostUuids(uuids);
    }

}
