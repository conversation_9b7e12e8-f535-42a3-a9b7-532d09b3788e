package cn.iocoder.zj.module.monitor.controller.admin.hardwareinfo.vo;

import cn.iocoder.zj.framework.excel.core.convert.ByteConvent;
import cn.iocoder.zj.framework.excel.core.convert.StateConvert;
import cn.iocoder.zj.framework.excel.core.convert.UnitConvert;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 硬件设施基本信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class HardwareInfoExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("主机名称")
    private String name;

    @ExcelProperty(value = "启用状态",converter = StateConvert.class)
    private String state;

    @ExcelProperty("宿主机IP")
    private String ip;

    @ExcelProperty(value = "就绪状态",converter = StateConvert.class)
    private String status;

    @ExcelProperty("集群名称")
    private String clusterName;

    @ExcelProperty(value = "CPU总容量",converter = UnitConvert.class)
    private Long totalCpuCapacity;

    @ExcelProperty(value = "CPU可用容量",converter = UnitConvert.class)
    private Long availableCpuCapacity;

    @ExcelProperty("CPU插槽")
    private Integer cpuSockets;

    @ExcelProperty("CPU架构")
    private String architecture;

    @ExcelProperty(value = "分配CPU",converter = UnitConvert.class)
    private Integer cpuNum;

    @ExcelProperty(value = "内存总容量",converter = ByteConvent.class)
    private Long totalMemoryCapacity;

    @ExcelProperty(value = "内存可用容量",converter = ByteConvent.class)
    private Long availableMemoryCapacity;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private LocalDateTime createTime;

    @ExcelProperty("地区编号")
    private Long regionId;

}
