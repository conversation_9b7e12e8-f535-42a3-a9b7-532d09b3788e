package cn.iocoder.zj.module.monitor.dal.mysql.hardwareinfo;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.controller.admin.hardwareinfo.vo.HardwareInfoExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.hardwareinfo.vo.HardwareInfoPageReqVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.hardwareinfo.HardwareInfoDO;
import cn.iocoder.zj.module.monitor.taskTime.cache.TaggableDO;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 硬件设施基本信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface HardwareInfoMapper extends BaseMapperX<HardwareInfoDO> {

    default PageResult<HardwareInfoDO> selectPage(HardwareInfoPageReqVO reqVO) {
        LambdaQueryWrapperX<HardwareInfoDO> wrapper = new LambdaQueryWrapperX<>();
        if (StringUtils.isNotBlank(reqVO.getQueryData())) {
            wrapper.likeIfPresent(HardwareInfoDO::getName, reqVO.getQueryData())
                    .or().like(HardwareInfoDO::getIp, reqVO.getQueryData())
                    .or().like(HardwareInfoDO::getPlatformName, reqVO.getPlatformName());
        }
        if (StringUtils.isNotBlank(reqVO.getStatus())) {
            wrapper.eqIfPresent(HardwareInfoDO::getStatus, reqVO.getStatus());
        }
        if (StrUtil.isNotEmpty(Convert.toStr(reqVO.getPlatformId()))) {
            wrapper.eqIfPresent(HardwareInfoDO::getPlatformId, reqVO.getPlatformId());
        }
        wrapper.orderByDesc(HardwareInfoDO::getId);
        return selectPage(reqVO, wrapper);
    }

    List<HardwareInfoDO> getHardwareInfoList(@Param("reqVO") HardwareInfoExportReqVO reqVO);

    int getCount(@Param("typeName") String typeName);

    void updateHardWareList(List<HardwareInfoDO> list);

    Map<String, Object> getHardwareStatusCount(@Param("tenantIds") List<String> tenantIds, @Param("platformId") Long platformId);

    List<Map<String, Object>> getCpuTop(@Param("tenantIds") List<String> tenantIds, @Param("platformId") Long platformId, @Param("regionId") Long regionId, @Param("top") int top);

    List<Map<String, Object>> getMemoryTop(@Param("tenantIds") List<String> tenantIds, @Param("platformId") Long platformId, @Param("regionId") Long regionId, @Param("top") int top);

    Map<String, Object> getCpuCapacity(@Param("tenantIds") List<String> tenantIds, @Param("platformId") Long platformId, @Param("regionId") Long regionId);

    Map<String, Object> getMemoryCapacity(@Param("tenantIds") List<String> tenantIds, @Param("platformId") Long platformId, @Param("regionId") Long regionId);

    List<String> getUUID(@Param("id") Long id);

    List<Long> selectPlatformList(@Param("tenantIds") List<String> tenantId);

    List<HardwareInfoDO> getHardwareInfoPage(@Param("mpPage") IPage<HardwareInfoDO> mpPage, @Param("pageReqVO") HardwareInfoPageReqVO pageReqVO, @Param("ids") List<String> ids);

    int deleteHardWare(@Param("list") List<HardwareInfoDO> list);

    @Slave
    List<HardwareInfoDO> getHardwareInfoSlavePage(@Param("mpPage") IPage<HardwareInfoDO> mpPage, @Param("pageVO") HardwareInfoPageReqVO pageVO, @Param("ids") List<String> ids);

    default List<HardwareInfoDO> getListByPlatformId(@Param("platformId") Collection<Long> platformId) {
        return selectList(new LambdaQueryWrapperX<HardwareInfoDO>()
                .in(HardwareInfoDO::getPlatformId, platformId)
                .eq(HardwareInfoDO::getDeleted, 0)
                .eq(HardwareInfoDO::getState, "Enabled")
                .eq(HardwareInfoDO::getStatus, "Connected")
                .groupBy(HardwareInfoDO::getUuid)
        );
    }

    default List<HardwareInfoDO> getHardwareListByPlatformId(@Param("platformId") Collection<Long> platformId) {
        if (CollectionUtils.isEmpty(platformId)) {
            return List.of();
        } else {
            return selectList(new LambdaQueryWrapperX<HardwareInfoDO>()
                    .in(HardwareInfoDO::getPlatformId, platformId)
                    .eq(HardwareInfoDO::getDeleted, 0)
                    .groupBy(HardwareInfoDO::getUuid)
            );
        }
    }


    List<Map<String, String>> getClusterSimpleInfo(@Param("platformId") Collection<Long> platformId);

    List<HardwareInfoDO> selectListByUuids(@Param("uuids") List<String> uuids);

    List<HardwareInfoDO> selectHardwareList(@Param("mpPage") IPage<HardwareInfoDO> mpPage, @Param("pageVO") HardwareInfoPageReqVO pageVO);

    List<Map<String, String>> getDuplicateDataIds();

    void removeDuplicateData(@Param("duplicateDatas") List<Map<String, String>> duplicateDatas);

    Long getCpuSockets(@Param("platformIds") List<String> platformIds);

    @TenantIgnore
    List<Map> getScreenStatistics(@Param("platformIds") List<Map> platformIds);

    @TenantIgnore
    List<Map> getHostNameByuuid(@Param("hostcpus") List<Map<String, Object>> hostcpus);

    int selectUserCount();

    int selectCloudCount();

    int selectAssetCount();


    List<Map<String, Object>> getHzAppListCount(@Param("mapList") List<Map> platformConfigList);

    Long selectCountByPlatfrom(@Param("mapList") List<Map> platformConfigList);

    void deleteHardwareInfoByplatform(@Param("platformId") Long platformId);

    List<HardWareRespCreateReqDTO> getHardwareByPlatformId(@Param("platformId") Long platformId);

    List<HardwareInfoDO> findHardwareByplatformIdAndHost(@Param("platformId") Long platformId, @Param("hostName") String hostName);

    List<HardwareInfoDO> getHardwareInfoByList(@Param("pageReqVO") HardwareInfoPageReqVO pageReqVO, @Param("ids") List<String> uids, @Param("inIds") List<String> inIds);

    Integer getCloudHostCount(@Param("hardwareUuid")String hardwareUuid);

    @TenantIgnore
    List<Map<String, Object>>  getHostInfoCpuUsedTopByPlatformId(@Param("platformId") List<Map> platformId);

    @TenantIgnore
    List<Map<String, Object>>  getHostInfoMemUsedTopByPlatformId(@Param("platformId") List<Map> platformId);

    @TenantIgnore
    List<HardwareInfoDO> getHardwareByList(@Param("collect") List<String> collect);

    @TenantIgnore
    List<TaggableDO> getListByTag();
}
