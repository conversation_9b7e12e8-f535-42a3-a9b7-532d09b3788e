package cn.iocoder.zj.module.monitor.service.networkvpc;

import java.util.*;
import javax.validation.*;

import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkVpcDTO;
import cn.iocoder.zj.module.monitor.controller.admin.networkvpc.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkvpc.NetworkVpcDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * VPC路由器 Service 接口
 *
 * <AUTHOR>
 */
public interface NetworkVpcService {

    /**
     * 创建VPC路由器
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createNetworkVpc(@Valid NetworkVpcCreateReqVO createReqVO);

    /**
     * 更新VPC路由器
     *
     * @param updateReqVO 更新信息
     */
    void updateNetworkVpc(@Valid NetworkVpcUpdateReqVO updateReqVO);

    /**
     * 删除VPC路由器
     *
     * @param id 编号
     */
    void deleteNetworkVpc(Long id);

    /**
     * 获得VPC路由器
     *
     * @param id 编号
     * @return VPC路由器
     */
    NetworkVpcDO getNetworkVpc(Long id);

    /**
     * 获得VPC路由器列表
     *
     * @param ids 编号
     * @return VPC路由器列表
     */
    List<NetworkVpcDO> getNetworkVpcList(Collection<Long> ids);

    /**
     * 获得VPC路由器分页
     *
     * @param pageReqVO 分页查询
     * @return VPC路由器分页
     */
    PageResult<NetworkVpcDO> getNetworkVpcPage(NetworkVpcPageReqVO pageReqVO);

    /**
     * 获得VPC路由器列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return VPC路由器列表
     */
    List<NetworkVpcDO> getNetworkVpcList(NetworkVpcExportReqVO exportReqVO);

    void createNetworkL3List(List<NetworkVpcDO> list);

    Long getNetWorkL3Count();

    void updateNetWorkVpcList(List<NetWorkVpcDTO> list);

    List<NetworkVpcDO>  getNetWorkVpcList();

    int deleteNetWorkVPCByNameList(List<NetworkVpcDO> list);

    List<Map<String,String>> getClusterSimpleInfo(Collection<Long> platformId);

    void deleteNetworkVpcByplatform(Long platformId);
}
