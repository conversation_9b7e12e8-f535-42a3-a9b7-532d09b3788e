package cn.iocoder.zj.module.monitor.dal.mysql.scanip;

import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.controller.admin.scanip.vo.IPRangeVO;
import cn.iocoder.zj.module.monitor.controller.admin.scanip.vo.IPScanResultVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.scanip.IPRangeDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.scanip.IPScanResultDO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * IP扫描结果 Mapper
 */
@Mapper
public interface IPScanResultMapper extends BaseMapperX<IPScanResultDO> {

    /**
     * 根据IP段ID查询扫描结果列表
     *
     * @param ipRangeId IP段ID
     * @return 扫描结果列表
     */
    default List<IPScanResultDO> selectListByIPRangeId(Long ipRangeId) {
        return selectList(new LambdaQueryWrapperX<IPScanResultDO>()
                .eq(IPScanResultDO::getIpRangeId, ipRangeId));
    }

    /**
     * 根据IP段ID删除扫描结果
     *
     * @param ipRangeId IP段ID
     * @return 影响行数
     */
    default int deleteByIPRangeId(Long ipRangeId) {
        return delete(new LambdaQueryWrapperX<IPScanResultDO>()
                .eq(IPScanResultDO::getIpRangeId, ipRangeId));
    }

    void deleteIPScanByIpRangeId(@Param("ipRangeId") Long id);

    List<IPScanResultVO> getPage(@Param("pageVO") IPScanResultVO pageVO, @Param("mpPage") IPage<IPScanResultVO> mpPage);

    void deleteIPScanByIpRangeIds(@Param("ids") List<Long> ids);

    @TenantIgnore
    String getCollectByTenantId(@Param("tenantId") Long tenantId);

    @TenantIgnore
    List<Map<String,String>> getMonitorByIds(@Param("collect") List<String> collect,@Param("platformId") Long platformId);
}