package cn.iocoder.zj.module.monitor.service.gatherlogdetail;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.zj.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.convert.message.AssetMessageConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.gatherasset.GatherAssetDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.gatherdevice.GatherDeviceDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostinfo.HostInfoDO;
import cn.iocoder.zj.module.monitor.dal.mysql.gatherasset.GatherAssetMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.gatherdevice.GatherDeviceMapper;
import cn.iocoder.zj.module.monitor.enums.message.AssetMessageEnum;
import cn.iocoder.zj.module.monitor.util.StringUtil;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.sms.SmsSendApi;
import cn.iocoder.zj.module.system.api.wechat.WeChatSendApi;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.util.*;

import cn.iocoder.zj.module.monitor.controller.admin.gatherlogdetail.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.gatherlogdetail.GatherLogdetailDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.convert.gatherlogdetail.GatherLogdetailConvert;
import cn.iocoder.zj.module.monitor.dal.mysql.gatherlogdetail.GatherLogdetailMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.singleton;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.*;

/**
 * 告警日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class GatherLogdetailServiceImpl implements GatherLogdetailService {

    @Resource
    private GatherLogdetailMapper gatherLogdetailMapper;
    @Resource
    private GatherAssetMapper gatherAssetMapper;
    @Resource
    PermissionApi permissionApi;
    @Resource
    RoleApi roleApi;
    @Resource
    SmsSendApi smsSendApi;
    @Resource
    WeChatSendApi weChatSendApi;
    @Resource
    PlatformconfigApi platformconfigApi;

    @Override
    public Long createGatherLogdetail(GatherLogdetailCreateReqVO createReqVO) {
        // 插入
        GatherLogdetailDO gatherLogdetail = GatherLogdetailConvert.INSTANCE.convert(createReqVO);
        gatherLogdetailMapper.insert(gatherLogdetail);
        // 返回
        return gatherLogdetail.getId();
    }

    @Override
    public void updateGatherLogdetail(GatherLogdetailUpdateReqVO updateReqVO) {
        // 校验存在
        validateGatherLogdetailExists(updateReqVO.getId());
        // 更新
        GatherLogdetailDO updateObj = GatherLogdetailConvert.INSTANCE.convert(updateReqVO);
        gatherLogdetailMapper.updateById(updateObj);
    }

    @Override
    public void deleteGatherLogdetail(Long id) {
        // 校验存在
        validateGatherLogdetailExists(id);
        // 删除
        gatherLogdetailMapper.deleteById(id);
    }

    private void validateGatherLogdetailExists(Long id) {
        if (gatherLogdetailMapper.selectById(id) == null) {
            throw exception(GATHER_LOGDETAIL_NOT_EXISTS);
        }
    }

    @Override
    public GatherLogdetailRespVO getGatherLogdetail(Long id) {
        return gatherLogdetailMapper.getGatherLogdetailById(id);
    }

    @Override
    public List<GatherLogdetailDO> getGatherLogdetailList(Collection<Long> ids) {
        return gatherLogdetailMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<GatherLogdetailDO> getGatherLogdetailPage(GatherLogdetailPageReqVO pageReqVO) {
        boolean admin = false;
        List<Map> platform = new ArrayList<>();
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));

        if (roleApi.hasAnySuperAdmin(roleIds)) {
            admin = true;
        } else {
            platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
        }

        return gatherLogdetailMapper.selectPage(pageReqVO, admin, platform);
    }

    @Override
    public List<GatherLogdetailDO> getGatherLogdetailList(GatherLogdetailExportReqVO exportReqVO) {
        return gatherLogdetailMapper.selectList(exportReqVO);
    }

    @Override
    public Map reportingLog(String json, String tokenDesc) {
        Map map = new HashMap();
        if (!SecureUtil.md5("<EMAIL>").equals(tokenDesc)) {
            map.put("code", 500);
            map.put("data", new HashMap<>());
            map.put("msg", "token验证失败");
            return map;
        }

        // 查询数据库是否已存在如果已存在返回存在codel;
        if (StrUtil.isNotEmpty(json)) {
            JSONObject obj = JSON.parseObject(json);
            String ip = obj.getString("ip");
            String uuid = obj.getString("uuid");
            Long platform_id = obj.getLong("platform_id");
            String platform_name = obj.getString("platform_name");
            String alarm_location = obj.getString("alarm_location");
            String alarm_log = obj.getString("alarm_log");
            String host_name = obj.getString("host_name");
            Date alarm_date = obj.getDate("alarmDate");
            GatherLogdetailDO gatherLogdetailDO = new GatherLogdetailDO();
            gatherLogdetailDO.setIp(ip);
            gatherLogdetailDO.setUuid(uuid);
            gatherLogdetailDO.setHostName(host_name);
            gatherLogdetailDO.setPlatformId(StringUtil.toString(platform_id));
            gatherLogdetailDO.setPlatformName(platform_name);
            gatherLogdetailDO.setAlarmDate(alarm_date);
            gatherLogdetailDO.setAlarmLocation(alarm_location);
            gatherLogdetailDO.setAlarmDetail(alarm_log);

            int count = gatherLogdetailMapper.insert(gatherLogdetailDO);

            if (count > 0) {
                map.put("code", 200);
                map.put("data", new HashMap<>());
                map.put("msg", "新增成功");
            }
            List<String> list = platformconfigApi.getByTenantList(platform_id).getData();
            // 通过平台id找到平台下配置的租户
            for (String s : list){
                Map<String, Object> templateParams = new HashMap<>();
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("id", uuid);
                jsonObject.put("createTime", alarm_date);
                jsonObject.put("hostName", host_name);
                jsonObject.put("ip", ip);
                jsonObject.put("alarmLocation", alarm_location);
                jsonObject.put("alarmDetail", alarm_log);
                jsonObject.put("alarmLog", alarm_log);
                jsonObject.put("tenantId", s);
//            smsSendApi.sendSingleSmsToAdmin(AssetMessageConvert.INSTANCE.convert(Convert.toLong(tenant_id),
//                    AssetMessageEnum.AGENT_ALARM.getSmsTemplateCode(), templateParams));
                LambdaQueryWrapper<GatherAssetDO> lqw = new LambdaQueryWrapper<>();
                lqw.eq(uuid != null, GatherAssetDO::getUuid, uuid)
                        .eq(GatherAssetDO::getDeleted, 0)
                        .eq(GatherAssetDO::getIp, ip);
                GatherAssetDO gatherAssetDO = gatherAssetMapper.selectOne(lqw);
                jsonObject.put("deviceName", gatherAssetDO.getDeviceName());

                weChatSendApi.sendSingleWeChatToTenant(jsonObject.toJSONString());
            }

        }
        return map;
    }

    @Override
    public void updateState(Collection<Long> ids) {
        gatherLogdetailMapper.updateState(ids);
    }

    @Override
    public List<GatherLogdetailDO> alarmNoticeList(Long tenantId) {


        boolean admin = false;
        List<Map> platform = new ArrayList<>();
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));

        if (roleApi.hasAnySuperAdmin(roleIds)) {
            admin = true;
        } else {
            platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
        }

        return gatherLogdetailMapper.alarmNoticeList(admin,platform);
    }

    @Override
    public PageResult<GatherLogdetailRespVO> getGatherLogDetailPageInfo(GatherLogdetailPageReqVO pageVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        PageParam pageParam = new PageParam().setPageNo(pageVO.getPageNo()).setPageSize(pageVO.getPageSize());
        IPage<GatherLogdetailRespVO> mpPage = MyBatisUtils.buildPage(pageParam);
        return new PageResult<>(gatherLogdetailMapper.getPageInfo(pageVO, loginUser,mpPage),mpPage.getTotal());
    }

    @Override
    public List<GatherLogdetailRespVO> getLatestLog() {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        List<GatherLogdetailRespVO> resultList = gatherLogdetailMapper.getLatestLog(loginUser);
        return resultList;
    }

    @Override
    @TenantIgnore
    public void solvedGather(String gatherId) {
        gatherLogdetailMapper.solvedGather(gatherId);
    }

    @Override
    @TenantIgnore
    public void gatherWorkOrder(String gatherId) {
        gatherLogdetailMapper.gatherWorkOrder(gatherId);
    }

    @Override
    @TenantIgnore
    public void cleanWorkOrder(String gatherId) {
        gatherLogdetailMapper.cleanWorkOrder(gatherId);
    }

    @Override
    public void updateIsSolved(Collection<Long> ids) {
        gatherLogdetailMapper.updateIsSolved(ids);
    }
}
