package cn.iocoder.zj.module.monitor.controller.admin.networkvpc.vo;

import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * VPC路由器 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class NetworkVpcExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("vpc 路由器uuid")
    private String uuid;

    @ExcelProperty("路由器名称")
    private String name;

    @ExcelProperty("cpu")
    private Integer cpuNum;

    @ExcelProperty("内存")
    private Long memorySize;

    @ExcelProperty("CPU架构")
    private String architecture;

    @ExcelProperty("路由dns")
    private String dns;

    @ExcelProperty("就绪状态")
    private String status;

    @ExcelProperty("启用状态")
    private String state;

    @ExcelProperty("L3网络uuid")
    private String l3NetworkUuid;

    @ExcelProperty("ipv4 Ip")
    private String ip;

    @ExcelProperty("管理网络uuid")
    private String managementNetworkUuid;

    @ExcelProperty("管理网络ip")
    private String managementNetworkIp;

    @ExcelProperty("三层网络名称")
    private String l3NetworkName;

    @ExcelProperty("集群uuid")
    private String clusterUuid;

    @ExcelProperty("集群名称")
    private String clusterName;

    @ExcelProperty("虚拟化技术")
    private String hypervisorType;

    @ExcelProperty("mac地址（IPV4)")
    private String mac;

    @ExcelProperty("宿主机uuid")
    private String hostUuid;

    @ExcelProperty("宿主机名称")
    private String hostName;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private Date createTime;

    @ExcelProperty("平台id")
    private Long platformId;

    @ExcelProperty("平台名称")
    private String platformName;

}
