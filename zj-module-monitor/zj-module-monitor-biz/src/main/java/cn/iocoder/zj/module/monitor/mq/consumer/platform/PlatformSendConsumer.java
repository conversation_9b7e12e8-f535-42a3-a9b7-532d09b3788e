package cn.iocoder.zj.module.monitor.mq.consumer.platform;

import cn.iocoder.zj.module.monitor.mq.message.platform.PlatformSendMessage;
import cn.iocoder.zj.module.monitor.service.platform.PlatformService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.function.Consumer;

/**
 * @ClassName : PlatformSendConsumer  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/5/10  15:08
 */
@Component
@Slf4j
public class PlatformSendConsumer {
    @Resource
    private PlatformService platformService;



    @RabbitListener(bindings = @QueueBinding(
            value = @Queue("platform_id_message"),
            exchange = @Exchange("myExchange")
    ))
    public void accept(@Payload PlatformSendMessage  platformSendMessage) {
        try {
            log.info("[accept][消息内容({})]", platformSendMessage);
            platformService.doSendPlatform(platformSendMessage);
        } catch (Exception e) {
            log.error("处理消息时出错: {}", platformSendMessage, e.getMessage());
        }
    }
}
