package cn.iocoder.zj.module.monitor.service.bulletin.enums;

import lombok.Getter;

@Getter
public enum BulletinCloudMonitorItemEnum {
    HARDWARE("hardware", "宿主机", "hardware", "hardware","monitor_hardware_info"),
    HOST("host", "云主机", "host", "host","monitor_host_info"),
    IMAGE("image", "镜像", "image", "image","monitor_image_info"),
    DISK("disk", "云硬盘", "disk", "disk","monitor_volume_info"),
    SNAPSHOT("snapshot", "快照", "snapshot", "snapshot","monitor_volume_snapshot"),
    STORAGE("storage", "云存储", "storage", "storage","monitor_storage_info"),
    NETWORK_L2("network_l2", "二层网络", "network_l2", "network_l2","monitor_network_l2"),
    NETWORK_L3("network_l3", "三层网络", "network_l3", "network_l3","monitor_network_l3"),
    NETWORK_VPC("network_vpc", "vpc网络", "network_vpc", "network_vpc","monitor_network_vpc"),
    NETWORK_SECGROUP("network_secgroup", "安全组", "network_secgroup", "network_secgroup","monitor_secgroup"),
    ;

    private final String code;
    private final String label;
    private final String category;
    private final String app;
    private final String table;

    BulletinCloudMonitorItemEnum(String code, String label, String category, String app,String table) {
        this.code = code;
        this.label = label;
        this.category = category;
        this.app = app;
        this.table = table;
    }
}
