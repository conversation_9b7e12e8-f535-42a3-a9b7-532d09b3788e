package cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo;

import lombok.Data;

@Data
public class AlarmInfoRespVo{
    /**
     * 消息内容
     */
    private String context;
    /**
     * 资源类型，host云主机；hardware宿主机；storage存储；image镜像
     */
    private String sourceType;
    /**
     * 告警资源名称
     */
    private String sourceName;
    private Long sourceId;
    /**
     * 告警级别
     */
    private Integer alarmLevel;
    /**
     * 告警次数
     */
    private Long alarmNum;
    /**
     * 地区ID
     */
    private Long regionId;
    /**
     * 地区名称
     */
    private String regionName;
    /**
     * 告警日期
     */
    private String createTime;

    private Long platformConfigId;

    private String platformName;

    private String uuid;
    /**
     * 主机、宿主机、存储名称
     */
    private String productsName;
    /**
     * 实际告警值
     */
    private String actualContext;
    /**
     * 告警配置ID
     */
    private Long id;
    /**
     * 是否解决0未创建工单，1未解决，2已解决
     */
    private Integer isSolved;

    /**
     * 是否已读0未读，1已读
     */
    private Integer isRead;
    /**
     * 资源是否销毁状态
     */
    private Integer isDestroyed;

}
