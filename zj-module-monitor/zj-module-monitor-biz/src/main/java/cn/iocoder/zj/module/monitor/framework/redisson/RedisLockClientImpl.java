package cn.iocoder.zj.module.monitor.framework.redisson;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@RequiredArgsConstructor
public class RedisLockClientImpl implements RedisLockClient {

    private final RedissonClient redissonClient;
    private final Map<String, RLock> lockMap = new ConcurrentHashMap<>();

    @Override
    public boolean tryLock(String key, TimeUnit unit, int waitTime, int leaseTime) {
        try {
            RLock lock = redissonClient.getLock(key);
            boolean locked = lock.tryLock(waitTime, leaseTime, unit);
            if (locked) {
                lockMap.put(key, lock);
            }
            return locked;
        } catch (InterruptedException e) {
            return false;
        }
    }

    @Override
    public void unlock(String key) {
        RLock lock = lockMap.remove(key);
        if (lock != null && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }
}