package cn.iocoder.zj.module.monitor.dal.dataobject.eip;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 弹性公网 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_eip")
@KeySequence("monitor_eip_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EipDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * uuid
     */
    private String uuid;
    /**
     * ip地址
     */
    private String ipAddr;
    /**
     * 带宽
     */
    private Integer bandwidth;
    /**
     * 网络uuid
     */
    private String networkId;
    /**
     * 资源类型
     */
    private String associateType;
    /**
     * 资源id
     */
    private String associateId;
    /**
     * 计费类型
     */
    private String chargeType;
    /**
     * 区域id
     */
    private String cloudregionId;
    /**
     * public_ip 公网IP  elastic_ip弹性公网IP
     */
    private String mode;
    /**
     * 状态
     */
    private String status;
    /**
     * 弹性公网创建时间
     */
    private Date vCreateTime;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;

}
