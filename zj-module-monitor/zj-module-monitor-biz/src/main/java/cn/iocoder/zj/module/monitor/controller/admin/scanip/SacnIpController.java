package cn.iocoder.zj.module.monitor.controller.admin.scanip;
import cn.iocoder.zj.framework.common.dal.manager.ScanIPRequest;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.module.monitor.controller.admin.scanip.vo.IPRangeVO;
import cn.iocoder.zj.module.monitor.controller.admin.scanip.vo.IPScanResultVO;
import cn.iocoder.zj.module.monitor.convert.scanip.IPRangeConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.scanip.IPRangeDO;
import cn.iocoder.zj.module.monitor.service.scanip.IPRangeService;
import com.google.gson.Gson;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

@Tag(name = "管理后台 - 自动发现")
@RestController
@RequestMapping("/monitor/sacn-ip")
@Validated
public class SacnIpController {
    @Resource
    private IPRangeService ipRangeService;
    @Resource
    private RedisTemplate redisTemplate;

    @PostMapping("/create")
    @Operation(summary = "创建计划")
    @OperateLog(type = CREATE)
    public CommonResult<Long> createIPRange(@Valid @RequestBody IPRangeVO createReqVO) {
        return success(ipRangeService.createIPRange(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新计划")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateIPRange(@Valid @RequestBody IPRangeVO updateReqVO) {
        ipRangeService.updateIPRange(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除计划")
    @Parameter(name = "id", description = "编号", required = true)
    @OperateLog(type = DELETE)
    public CommonResult<Boolean> deleteIPRange(@RequestParam("id") Long id) {
        // 删除IP段时，会级联删除对应的扫描结果
        ipRangeService.deleteIPRange(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得计划详情")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<IPRangeDO> getIPRange(@RequestParam("id") Long id) {
        IPRangeVO ipRange = ipRangeService.getIPRange(id);
        return success(IPRangeConvert.INSTANCE.convert(ipRange));
    }

    @GetMapping("/list")
    @Operation(summary = "获得计划列表")
    public CommonResult<List<IPRangeVO>> getIPRangeList(@Valid IPRangeVO exportReqVO) {
        List<IPRangeVO> list = ipRangeService.getIPRangeList(exportReqVO);
        return success(list);
    }

    @GetMapping("/page")
    @Operation(summary = "获得计划分页")
    public CommonResult<PageResult<IPRangeVO>> getIPRangePage(@Valid IPRangeVO pageVO) {
        PageResult<IPRangeVO> pageResult = ipRangeService.getIPRangePage(pageVO);
        return success(pageResult);
    }


    @GetMapping("/pageResult")
    @Operation(summary = "计划ip段分页")
    public CommonResult<PageResult<IPScanResultVO>> getIPScanResultPage(@Valid IPScanResultVO pageVO) {
        PageResult<IPScanResultVO> pageResult = ipRangeService.getIPScanResultPage(pageVO);
        return success(pageResult);
    }

    @PutMapping("/batchUpdate")
    @Operation(summary = "批量修改计划状态及删除")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> batchUpdate(@RequestBody IPRangeVO updateReqVO) {
        ipRangeService.batchUpdate(updateReqVO);
        return success(true);
    }

    @PostMapping("/execute")
    @Operation(summary = "立即执行计划")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> execute(@RequestBody IPRangeVO updateReqVO) {
        ipRangeService.execute(updateReqVO.getId());
        return success(true);
    }

    @GetMapping("/getScanCount")
    @Operation(summary = "计划ip段分页")
    public CommonResult<ScanIPRequest> getScanCount(@RequestParam("id") Long id) {
        if(redisTemplate.hasKey("SCAN_IP:" + id)){
            ScanIPRequest redis = new Gson().fromJson(redisTemplate.opsForValue().get("SCAN_IP:" + id).toString(), ScanIPRequest.class);
            return success(redis);
        }
        return success(new ScanIPRequest());
    }
}
