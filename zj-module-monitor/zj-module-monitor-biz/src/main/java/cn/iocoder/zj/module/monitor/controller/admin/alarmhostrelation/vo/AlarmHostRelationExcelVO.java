package cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo;

import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 告警配置与云主机关联关系 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class AlarmHostRelationExcelVO {

    @ExcelProperty("主键ID")
    private Long id;

    @ExcelProperty("告警配合名称")
    private String alarmName;

    @ExcelProperty("告警配置ID")
    private Long alarmId;

    @ExcelProperty("主机名称")
    private String hostName;

    @ExcelProperty("云主机uuid")
    private String hostUuid;

    @ExcelProperty("租户名称")
    private String tenantName;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private LocalDateTime createTime;

}
