package cn.iocoder.zj.module.monitor.controller.admin.chart.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName : WordInfluxDO  //类名
 * @Description : word 查询类  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/9/8  9:47
 */
@Data
public class WordInfluxDO {
    @Schema(description = "设备uuid")
    private String uuid;
    @Schema(description = "使用率")
    private Double meanValue;

    private Date time;

}
