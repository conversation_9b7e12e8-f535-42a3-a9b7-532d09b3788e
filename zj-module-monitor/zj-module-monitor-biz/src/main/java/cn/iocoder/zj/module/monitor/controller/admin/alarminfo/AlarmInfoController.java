package cn.iocoder.zj.module.monitor.controller.admin.alarminfo;

import cn.iocoder.zj.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import org.apache.hertzbeat.common.entity.dto.Message;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;
import cn.iocoder.zj.module.monitor.controller.admin.alarminfo.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarminfo.AlarmInfoDO;
import cn.iocoder.zj.module.monitor.convert.alarminfo.AlarmInfoConvert;
import cn.iocoder.zj.module.monitor.service.alarminfo.AlarmInfoService;
import org.springframework.web.multipart.MultipartFile;

@Tag(name = "管理后台 - 监控告警详情")
@RestController
@RequestMapping("/monitor/alarm-info")
@Validated
public class AlarmInfoController {

    @Resource
    private AlarmInfoService alarmInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建监控告警详情")
    @PreAuthenticated
    @PreAuthorize("@ss.hasPermission('monitor:alarm-info:solve')")
    public CommonResult<Long> createAlarmInfo(@Valid @RequestBody AlarmInfoCreateReqVO createReqVO) {
        return success(alarmInfoService.createAlarmInfo(createReqVO));
    }

    @PostMapping(path = "/batchUpdate")
    @Operation(summary = "批量修改告警状态,设置已读未读")
    public ResponseEntity<Message<Void>> batchUpdateStatus(@RequestBody List<AlarmInfoCreateReqVO> alarmList) {
        alarmInfoService.batchUpdateStatus(alarmList);
        return ResponseEntity.ok(Message.success());
    }

    @PutMapping("/update")
    @Operation(summary = "更新监控告警详情")
    @PreAuthenticated
    public CommonResult<Boolean> updateAlarmInfo(@Valid @RequestBody AlarmInfoUpdateReqVO updateReqVO) {
        alarmInfoService.updateAlarmInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除监控告警详情")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('monitor:alarm-info:delete')")
    public CommonResult<Boolean> deleteAlarmInfo(@RequestParam("id") Long id) {
        alarmInfoService.deleteAlarmInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得监控告警详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @TenantIgnore
    @PreAuthenticated
    public CommonResult<AlarmInfoByIdRespVO> getAlarmInfo(@RequestParam("id") Long id) {
        AlarmInfoByIdRespVO alarmInfoDO  = alarmInfoService.getAlarmInfoById(id);
        return success(alarmInfoDO);
    }

    @GetMapping("/list")
    @Operation(summary = "获得监控告警详情列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthenticated
    public CommonResult<List<AlarmInfoRespVO>> getAlarmInfoList(@RequestParam("ids") Collection<Long> ids) {
        List<AlarmInfoDO> list = alarmInfoService.getAlarmInfoList(ids);
        return success(AlarmInfoConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得监控告警详情分页")
    @PreAuthenticated
    public CommonResult<PageResult<AlarmInfoRespVO>> getAlarmInfoPage(@Valid AlarmInfoPageReqVO pageVO) {
        PageResult<AlarmInfoDO> pageResult = alarmInfoService.getAlarmInfoPage(pageVO);
        return success(AlarmInfoConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出监控告警详情 Excel")
    @PreAuthorize("@ss.hasPermission('monitor:alarm-info:export')")
    @PreAuthenticated
    public void exportAlarmInfoExcel(@Valid AlarmInfoExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<AlarmInfoDO> list = alarmInfoService.getAlarmInfoList(exportReqVO);
        // 导出 Excel
        List<AlarmInfoExcelVO> datas = AlarmInfoConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "监控告警详情.xls", "数据", AlarmInfoExcelVO.class, datas);
    }


    @PostMapping("/upload")
    @Operation(summary = "上传附件")
    @PreAuthenticated
    public CommonResult<Map<String,String>> getAlarmInfoUpload(MultipartFile file, HttpServletResponse response) throws IOException {
        Map<String,String> pageResult = alarmInfoService.getAlarmInfoUpload(file,response);
        return success(pageResult);
    }

}
