package cn.iocoder.zj.module.monitor.service.mediatype;

import cn.iocoder.zj.module.monitor.dal.dataobject.storageinfo.StorageInfoDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.volumeinfo.VolumeInfoDO;
import cn.iocoder.zj.module.monitor.dal.mysql.storageinfo.StorageInfoMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.volumeinfo.VolumeInfoMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.zj.module.monitor.controller.admin.mediatype.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.mediatype.MediaTypeDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.convert.mediatype.MediaTypeConvert;
import cn.iocoder.zj.module.monitor.dal.mysql.mediatype.MediaTypeMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.*;

/**
 * 介质类型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MediaTypeServiceImpl implements MediaTypeService {

    @Resource
    private MediaTypeMapper mediaTypeMapper;

    @Resource
    private StorageInfoMapper storageInfoMapper;

    @Resource
    private VolumeInfoMapper volumeInfoMapper;

    @Override
    public Long createMediaType(MediaTypeCreateReqVO createReqVO) {
        // 插入
        MediaTypeDO mediaType = MediaTypeConvert.INSTANCE.convert(createReqVO);
        mediaTypeMapper.insert(mediaType);
        // 返回
        return mediaType.getId();
    }

    @Override
    public void updateMediaType(MediaTypeUpdateReqVO updateReqVO) {
        // 校验存在
        validateMediaTypeExists(updateReqVO.getId());
        // 更新
        MediaTypeDO updateObj = MediaTypeConvert.INSTANCE.convert(updateReqVO);
        //修改storageInfo
        List<StorageInfoDO> storageInfos = storageInfoMapper
                .selectList(
                        new LambdaQueryWrapper<StorageInfoDO>()
                                .eq(StorageInfoDO::getMediaTypeId, updateReqVO.getId())
                );
        for (StorageInfoDO storageInfo : storageInfos) {
            storageInfo.setMediaType(updateObj.getName());
            storageInfo.setMediaTypeId(updateObj.getId());
            storageInfoMapper.updateById(storageInfo);
            VolumeInfoDO volInfo = new VolumeInfoDO();
            volInfo.setMediaType(updateObj.getName());
            volumeInfoMapper.update(volInfo,
                    new LambdaQueryWrapper<VolumeInfoDO>()
                            .eq(VolumeInfoDO::getPrimaryStorageUuid, storageInfo.getUuid())
            );
        }

        mediaTypeMapper.updateById(updateObj);
    }

    @Override
    public void deleteMediaType(Long id) {
        // 校验存在
        validateMediaTypeExists(id);
        // 删除
        mediaTypeMapper.deleteById(id);
        List<StorageInfoDO> storageInfos = storageInfoMapper
                .selectList(
                        new LambdaQueryWrapper<StorageInfoDO>()
                                .eq(StorageInfoDO::getMediaTypeId, id)
                );
        for (StorageInfoDO storageInfo : storageInfos) {
            storageInfo.setMediaType("");
            storageInfo.setMediaTypeId(0L);
            storageInfoMapper.updateById(storageInfo);
            VolumeInfoDO volInfo = new VolumeInfoDO();
            volInfo.setMediaType("");
            volumeInfoMapper.update(volInfo,
                    new LambdaQueryWrapper<VolumeInfoDO>()
                    .eq(VolumeInfoDO::getPrimaryStorageUuid, storageInfo.getUuid())
            );
        }
    }

    private void validateMediaTypeExists(Long id) {
        if (mediaTypeMapper.selectById(id) == null) {
            throw exception(MEDIA_TYPE_NOT_EXISTS);
        }
    }

    @Override
    public MediaTypeDO getMediaType(Long id) {
        return mediaTypeMapper.selectById(id);
    }

    @Override
    public List<MediaTypeDO> getMediaTypeList() {
        return mediaTypeMapper.selectList();
    }

    @Override
    public PageResult<MediaTypeDO> getMediaTypePage(MediaTypePageReqVO pageReqVO) {
        return mediaTypeMapper.selectPage(pageReqVO);
    }

    @Override
    public List<MediaTypeDO> getMediaTypeList(MediaTypeExportReqVO exportReqVO) {
        return mediaTypeMapper.selectList(exportReqVO);
    }

    @Override
    public boolean bindStorage(Long mediaId, Long storageId) {
        MediaTypeDO mediaType;
        if (mediaId == 0L) {
            mediaType = new MediaTypeDO();
            mediaType.setName("");
            mediaType.setId(0L);
        } else {
            mediaType = getMediaType(mediaId);
            if (mediaType == null) {
                return false;
            }
        }

        StorageInfoDO storageInfo = storageInfoMapper.selectById(storageId);
        if (storageInfo != null) {
            storageInfo.setMediaType(mediaType.getName());
            storageInfo.setMediaTypeId(mediaType.getId());
            storageInfoMapper.updateById(storageInfo);
            VolumeInfoDO volInfo = new VolumeInfoDO();
            volInfo.setMediaType(mediaType.getName());
            volumeInfoMapper.update(volInfo,
                    new LambdaQueryWrapper<VolumeInfoDO>()
                            .eq(VolumeInfoDO::getPrimaryStorageUuid, storageInfo.getUuid())
            );
            return true;
        }
        return false;
    }
}
