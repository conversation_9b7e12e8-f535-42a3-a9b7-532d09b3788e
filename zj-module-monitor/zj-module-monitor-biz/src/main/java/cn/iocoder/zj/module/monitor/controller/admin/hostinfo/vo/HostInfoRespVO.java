package cn.iocoder.zj.module.monitor.controller.admin.hostinfo.vo;

import cn.iocoder.zj.module.monitor.controller.admin.tags.vo.TagsRespVO;
import cn.iocoder.zj.module.monitor.controller.admin.volumeinfo.vo.VolumeInfoUseReqVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.secgroup.SecgroupDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 云主机基本信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HostInfoRespVO extends HostInfoBaseVO {

    @Schema(description = "主键", required = true)
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

    @Schema(description = "告警配置id")
    private Collection<Long> alarmIds;

    @Schema(description = "云盘信息")
    private List<VolumeInfoUseReqVO> diskInfos;

    @Schema(description = "安全组信息")
    private List<SecgroupDO> secgroupDOS;

    @Schema(description = "标签信息")
    private List<TagsRespVO> tags;

    @Schema(description = "资产授权id")
    private Long omMonitorId;

    @Schema(description = "资产授权状态")
    private String authorizationType;

    @Schema(description = "授权有效时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime authorizationTime;
}
