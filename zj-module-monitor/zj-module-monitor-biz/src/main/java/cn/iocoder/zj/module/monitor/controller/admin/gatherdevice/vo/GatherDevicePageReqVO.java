package cn.iocoder.zj.module.monitor.controller.admin.gatherdevice.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 采集设备分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GatherDevicePageReqVO extends PageParam {

    @Schema(description = "采集设备uuid")
    private String uuid;

    @Schema(description = "采集设备ip")
    private String gatherIp;

    @Schema(description = "采集设备名称")
    private String gatherName;

    @Schema(description = "0 未绑定 ,1 已绑定")
    private Integer type;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "平台名称")
    private String platformName;
    @Schema(description = "平台id")
    private Long platformId;

}
