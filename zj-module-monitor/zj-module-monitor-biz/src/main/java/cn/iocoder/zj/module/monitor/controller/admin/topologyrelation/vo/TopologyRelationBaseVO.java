package cn.iocoder.zj.module.monitor.controller.admin.topologyrelation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
 * 拓扑图关系 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class TopologyRelationBaseVO {

    @Schema(description = "拓扑图id")
    private Long topologyId;

    @Schema(description = "监控id")
    private String monitorId;

    @Schema(description = "监控设备名称")
    private String monitorName;

    @Schema(description = "监控设备接口名称")
    private String monitorInterfaces;

}
