package cn.iocoder.zj.module.monitor.controller.admin.hostnic.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 云主机网络 Excel 导出 Request VO，参数和 HostNicPageReqVO 是一致的")
@Data
public class HostNicExportReqVO {

    @Schema(description = "uuid")
    private String uuid;

    @Schema(description = "云主机uuid")
    private String hostUuid;

    @Schema(description = "ipV6")
    private String ip6;

    @Schema(description = "ip")
    private String ip;

    @Schema(description = "mac")
    private String mac;

    @Schema(description = "驱动")
    private String driver;

    @Schema(description = "在经典网络")
    private Byte inClassicNetwork;

    @Schema(description = "网络uuid")
    private String networkUuid;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

}
