package cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false)
public class AlarmConfigImportVO {
    @ExcelProperty("告警名称")
    private String alarmName;

    @ExcelProperty("资源类型")
    private String sourceType;

    @ExcelProperty("告警级别")
    private String alarmLevel;

    @ExcelProperty("触发次数")
    private Long alarmTime;

    @ExcelProperty("告警简介")
    private String description;

    @ExcelProperty("告警类型（字典值）")
    private String dictLabelType;

    @ExcelProperty("告警类型")
    private String dictLabelName;

    @ExcelProperty("告警条目（字典值）")
    private String dictLabelValue;

    @ExcelProperty("逻辑符号")
    private String alarmRule;

    @ExcelProperty("告警阈值")
    private Integer alarmVal;

    @ExcelProperty("单位")
    @ColumnWidth(20)
    private String unit;

    @ExcelProperty("单位（字典值）")
    private String unitType;
}
