//package cn.iocoder.zj.module.monitor.controller.admin.chart;
//
//import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
//import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
//import cn.iocoder.zj.module.monitor.controller.admin.chart.vo.WordExportReqVO;
//import cn.iocoder.zj.module.monitor.service.chart.ChartService;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//import javax.annotation.security.PermitAll;
//import javax.servlet.http.HttpServletResponse;
//import javax.validation.Valid;
//
//import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;
//
///**
// * @ClassName : ChartController  //类名
// * @Description : 导出word报表  //描述
// * <AUTHOR> <EMAIL> //作者
// * @Date: 2023/9/7  16:24
// */
//
//@Tag(name = "管理后台 - 导出word报表")
//@RestController
//@RequestMapping("/monitor/chart")
//@Validated
//public class ChartController {
//
//    @Resource
//    ChartService chartService;
//
//    @GetMapping("/report")
//    @Operation(summary = "创建word报表数据")
//    @PermitAll
//    @TenantIgnore
//    @PreAuthorize("@ss.hasPermission('monitor:chart:create')")
//    @OperateLog(type = CREATE)
//    public void exportReport(HttpServletResponse response,
//                             @Valid WordExportReqVO exportReqVO) {
//        chartService.exportReport(response,exportReqVO);
//    }
//
//
//}
