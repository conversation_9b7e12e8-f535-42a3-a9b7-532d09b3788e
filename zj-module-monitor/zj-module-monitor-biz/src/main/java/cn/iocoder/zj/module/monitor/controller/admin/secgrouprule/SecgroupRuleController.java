package cn.iocoder.zj.module.monitor.controller.admin.secgrouprule;

import cn.iocoder.zj.framework.security.core.annotations.PreAuthenticated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.monitor.controller.admin.secgrouprule.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.secgrouprule.SecgroupRuleDO;
import cn.iocoder.zj.module.monitor.convert.secgrouprule.SecgroupRuleConvert;
import cn.iocoder.zj.module.monitor.service.secgrouprule.SecgroupRuleService;

@Tag(name = "管理后台 - 端口组规则")
@RestController
@RequestMapping("/monitor/secgroup-rule")
@Validated
public class SecgroupRuleController {

    @Resource
    private SecgroupRuleService secgroupRuleService;

    @PostMapping("/create")
    @Operation(summary = "创建端口组规则")
    @PreAuthorize("@ss.hasPermission('monitor:secgroup-rule:create')")
    public CommonResult<Long> createSecgroupRule(@Valid @RequestBody SecgroupRuleCreateReqVO createReqVO) {
        return success(secgroupRuleService.createSecgroupRule(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新端口组规则")
    @PreAuthorize("@ss.hasPermission('monitor:secgroup-rule:update')")
    public CommonResult<Boolean> updateSecgroupRule(@Valid @RequestBody SecgroupRuleUpdateReqVO updateReqVO) {
        secgroupRuleService.updateSecgroupRule(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除端口组规则")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('monitor:secgroup-rule:delete')")
    public CommonResult<Boolean> deleteSecgroupRule(@RequestParam("id") Long id) {
        secgroupRuleService.deleteSecgroupRule(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得端口组规则")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthenticated
    public CommonResult<SecgroupRuleRespVO> getSecgroupRule(@RequestParam("id") Long id) {
        SecgroupRuleDO secgroupRule = secgroupRuleService.getSecgroupRule(id);
        return success(SecgroupRuleConvert.INSTANCE.convert(secgroupRule));
    }

    @GetMapping("/list")
    @Operation(summary = "获得端口组规则列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthenticated
    public CommonResult<List<SecgroupRuleRespVO>> getSecgroupRuleList(@RequestParam("ids") Collection<Long> ids) {
        List<SecgroupRuleDO> list = secgroupRuleService.getSecgroupRuleList(ids);
        return success(SecgroupRuleConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得端口组规则分页")
    @PreAuthenticated
    public CommonResult<PageResult<SecgroupRuleRespVO>> getSecgroupRulePage(@Valid SecgroupRulePageReqVO pageVO) {
        PageResult<SecgroupRuleDO> pageResult = secgroupRuleService.getSecgroupRulePage(pageVO);
        return success(SecgroupRuleConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出端口组规则 Excel")
    @PreAuthorize("@ss.hasPermission('monitor:secgroup-rule:export')")
    @OperateLog(type = EXPORT)
    public void exportSecgroupRuleExcel(@Valid SecgroupRuleExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<SecgroupRuleDO> list = secgroupRuleService.getSecgroupRuleList(exportReqVO);
        // 导出 Excel
        List<SecgroupRuleExcelVO> datas = SecgroupRuleConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "端口组规则.xls", "数据", SecgroupRuleExcelVO.class, datas);
    }

}
