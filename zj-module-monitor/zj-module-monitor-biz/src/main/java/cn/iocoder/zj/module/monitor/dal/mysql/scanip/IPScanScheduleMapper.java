package cn.iocoder.zj.module.monitor.dal.mysql.scanip;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.module.monitor.dal.dataobject.scanip.IPScanScheduleDO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

/**
 * IP扫描计划 Mapper
 */
@Mapper
public interface IPScanScheduleMapper extends BaseMapperX<IPScanScheduleDO> {

    /**
     * 根据IP段ID删除扫描计划
     *
     * @param ipRangeId IP段ID
     * @return 影响行数
     */
    default int deleteByIPRangeId(Long ipRangeId) {
        return delete(new LambdaQueryWrapperX<IPScanScheduleDO>()
                .eq(IPScanScheduleDO::getIpRangeId, ipRangeId));
    }

    /**
     * 查询指定IP段的扫描计划分页
     *
     * @param ipRangeId IP段ID
     * @param pageNo 页码
     * @param pageSize 每页条数
     * @return 扫描计划分页
     */
    default PageResult<IPScanScheduleDO> selectPage(LambdaQueryWrapperX<IPScanScheduleDO> ipRangeId, LambdaQueryWrapperX<IPScanScheduleDO> pageNo, LambdaQueryWrapperX<IPScanScheduleDO> pageSize) {
        return selectPage(pageNo, pageSize, new LambdaQueryWrapperX<IPScanScheduleDO>()
                .eq(IPScanScheduleDO::getIpRangeId, ipRangeId)
                .orderByDesc(IPScanScheduleDO::getScheduleTime));
    }

    /**
     * 查询所有待执行的扫描计划
     *
     * @return 待执行的扫描计划列表
     */
    default List<IPScanScheduleDO> selectPendingSchedules() {
        return selectList(new LambdaQueryWrapperX<IPScanScheduleDO>()
                .eq(IPScanScheduleDO::getStatus, IPScanScheduleDO.STATUS_PENDING)
                .orderByAsc(IPScanScheduleDO::getScheduleTime));
    }

    /**
     * 查询指定时间之前的扫描计划
     *
     * @param time 时间
     * @return 扫描计划列表
     */
    default List<IPScanScheduleDO> selectListByTimeBefore(LocalDateTime time) {
        return selectList(new LambdaQueryWrapperX<IPScanScheduleDO>()
                .le(IPScanScheduleDO::getScheduleTime, time));
    }

    /**
     * 删除指定时间之前的扫描计划
     *
     * @param time 时间
     * @return 影响行数
     */
    default int deleteByTimeBefore(LocalDateTime time) {
        return delete(new LambdaQueryWrapperX<IPScanScheduleDO>()
                .le(IPScanScheduleDO::getScheduleTime, time));
    }

    /**
     * 查询指定IP段的最新扫描计划
     *
     * @param ipRangeId IP段ID
     * @return 最新扫描计划
     */
    default IPScanScheduleDO selectLatestByIPRangeId(Long ipRangeId) {
        return selectOne(new LambdaQueryWrapperX<IPScanScheduleDO>()
                .eq(IPScanScheduleDO::getIpRangeId, ipRangeId)
                .orderByDesc(IPScanScheduleDO::getScheduleTime));
    }

    /**
     * 查询指定IP段的所有待执行扫描计划
     *
     * @param ipRangeId IP段ID
     * @return 待执行的扫描计划列表
     */
    default List<IPScanScheduleDO> selectPendingByIPRangeId(Long ipRangeId) {
        return selectList(new LambdaQueryWrapperX<IPScanScheduleDO>()
                .eq(IPScanScheduleDO::getIpRangeId, ipRangeId)
                .eq(IPScanScheduleDO::getStatus, IPScanScheduleDO.STATUS_PENDING)
                .orderByAsc(IPScanScheduleDO::getScheduleTime));
    }

    /**
     * 查询需要在系统重启后执行的扫描计划
     *
     * @return 需要在系统重启后执行的扫描计划列表
     */
    default List<IPScanScheduleDO> selectExecuteOnRestart() {
        return selectList(new LambdaQueryWrapperX<IPScanScheduleDO>()
                .eq(IPScanScheduleDO::getExecuteOnRestart, true)
                .eq(IPScanScheduleDO::getStatus, IPScanScheduleDO.STATUS_PENDING)
                .orderByAsc(IPScanScheduleDO::getScheduleTime));
    }
}