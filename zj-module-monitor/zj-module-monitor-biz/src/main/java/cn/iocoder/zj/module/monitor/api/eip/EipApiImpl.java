package cn.iocoder.zj.module.monitor.api.eip;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.eip.dto.EipCreateReqDto;
import cn.iocoder.zj.module.monitor.api.hostnic.dto.HostNicCreateReqDto;
import cn.iocoder.zj.module.monitor.controller.admin.eip.vo.EipExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.hostnic.vo.HostNicExportReqVO;
import cn.iocoder.zj.module.monitor.convert.eip.EipConvert;
import cn.iocoder.zj.module.monitor.convert.hostnic.HostNicConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.eip.EipDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostnic.HostNicDO;
import cn.iocoder.zj.module.monitor.service.eip.EipService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class EipApiImpl implements EipApi {
    @Resource
    EipService eipService;

    @Override
    public CommonResult<Boolean> addEips(List<EipCreateReqDto> reqDTO) {
        List<EipDO> list = EipConvert.INSTANCE.convertCreateList(reqDTO);
        eipService.createEipList(list);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> updateEips(List<EipCreateReqDto> eipCreateReqDto) {
        if (!eipCreateReqDto.isEmpty()) {
            List<EipDO> list = EipConvert.INSTANCE.convertCreateList(eipCreateReqDto);
            eipService.updateEips(list);
        }
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> delEips(List<EipCreateReqDto> eipCreateReqDto) {
        if (!eipCreateReqDto.isEmpty()) {
            List<EipDO> list = EipConvert.INSTANCE.convertCreateList(eipCreateReqDto);
            eipService.deleteEips(list);
        }

        return CommonResult.success(true);
    }

    @Override
    public CommonResult<List<EipCreateReqDto>> getEipsByPlatformId(Long platformId) {
        EipExportReqVO reqVo = new EipExportReqVO();
        reqVo.setPlatformId(platformId);
        List<EipCreateReqDto> list = EipConvert.INSTANCE.convertDoToCreateDtoList(eipService.getEipList(reqVo));
        return CommonResult.success(list);
    }
}
