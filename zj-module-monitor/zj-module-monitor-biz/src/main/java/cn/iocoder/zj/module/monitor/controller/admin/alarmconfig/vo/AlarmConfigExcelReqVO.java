package cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 告警配置 Excel 导出 Request VO，参数和 AlarmConfigPageReqVO 是一致的")
@Data
public class AlarmConfigExcelReqVO {

    @Schema(description = "主键id")
    private Long[] ids;

    @Schema(description = "告警名称")
    private String alarmName;

    @Schema(description = "告警简介")
    private String description;

    @Schema(description = "资源类型，host云主机；hardware宿主机；storage存储；image镜像")
    private String sourceType;

    @Schema(description = "字典名称")
    private String dictLabelName;

    @Schema(description = "字典类型")
    private String dictLabelType;

    @Schema(description = "触发规则")
    private String alarmRule;

    @Schema(description = "告警阈值")
    private Integer alarmVal;

    @Schema(description = "收敛次数")
    private Long alarmTime;

    @Schema(description = "告警级别")
    private Integer alarmLevel;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "消息内容")
    private String context;
}
