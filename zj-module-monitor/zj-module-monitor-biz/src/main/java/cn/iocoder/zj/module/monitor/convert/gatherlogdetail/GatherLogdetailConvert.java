package cn.iocoder.zj.module.monitor.convert.gatherlogdetail;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.gatherlogdetail.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.gatherlogdetail.GatherLogdetailDO;

/**
 * 告警日志 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface GatherLogdetailConvert {

    GatherLogdetailConvert INSTANCE = Mappers.getMapper(GatherLogdetailConvert.class);

    GatherLogdetailDO convert(GatherLogdetailCreateReqVO bean);

    GatherLogdetailDO convert(GatherLogdetailUpdateReqVO bean);

    GatherLogdetailRespVO convert(GatherLogdetailDO bean);

    List<GatherLogdetailRespVO> convertList(List<GatherLogdetailDO> list);

    PageResult<GatherLogdetailRespVO> convertPage(PageResult<GatherLogdetailDO> page);

    List<GatherLogdetailExcelVO> convertList02(List<GatherLogdetailDO> list);

}
