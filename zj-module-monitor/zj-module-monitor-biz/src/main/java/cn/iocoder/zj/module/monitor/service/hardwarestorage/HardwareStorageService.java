package cn.iocoder.zj.module.monitor.service.hardwarestorage;

import java.util.*;
import javax.validation.*;

import cn.iocoder.zj.module.monitor.api.hardwarenic.HardWareNicRespDTO;
import cn.iocoder.zj.module.monitor.api.hardwarestorage.HardWareStorageRespDTO;
import cn.iocoder.zj.module.monitor.controller.admin.hardwarestorage.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.hardwarestorage.HardwareStorageDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 宿主机与存储关联 Service 接口
 *
 * <AUTHOR>
 */
public interface HardwareStorageService {

    /**
     * 创建宿主机与存储关联
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createHardwareStorage(@Valid HardwareStorageCreateReqVO createReqVO);

    /**
     * 更新宿主机与存储关联
     *
     * @param updateReqVO 更新信息
     */
    void updateHardwareStorage(@Valid HardwareStorageUpdateReqVO updateReqVO);

    /**
     * 删除宿主机与存储关联
     *
     * @param id 编号
     */
    void deleteHardwareStorage(Long id);

    /**
     * 获得宿主机与存储关联
     *
     * @param id 编号
     * @return 宿主机与存储关联
     */
    HardwareStorageDO getHardwareStorage(Long id);

    /**
     * 获得宿主机与存储关联列表
     *
     * @param ids 编号
     * @return 宿主机与存储关联列表
     */
    List<HardwareStorageDO> getHardwareStorageList(Collection<Long> ids);

    /**
     * 获得宿主机与存储关联分页
     *
     * @param pageReqVO 分页查询
     * @return 宿主机与存储关联分页
     */
    PageResult<HardwareStorageDO> getHardwareStoragePage(HardwareStoragePageReqVO pageReqVO);

    /**
     * 获得宿主机与存储关联列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 宿主机与存储关联列表
     */
    List<HardwareStorageDO> getHardwareStorageList(HardwareStorageExportReqVO exportReqVO);

    void createHardwareStorageList(List<HardwareStorageDO> list);

    void updateHardwareStorageList(List<HardwareStorageDO> list);

    int deleteHardwareStorageList(List<HardwareStorageDO> list);

    List<HardWareStorageRespDTO> getHardwareStorageByPlatformId(Long platformId);


    Integer getHareWareCountByStorageUuid(String storageUuid);

    int deleteByPlatformId(Long platformId);
}
