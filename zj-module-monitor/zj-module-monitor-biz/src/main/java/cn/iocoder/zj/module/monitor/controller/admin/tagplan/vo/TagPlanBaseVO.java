package cn.iocoder.zj.module.monitor.controller.admin.tagplan.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 计划计划 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class TagPlanBaseVO {

    @Schema(description = "计划名称", required = true)
    @NotNull(message = "计划名称不能为空")
    private String name;

    @Schema(description = "计划周期(day-日, week-周, month-月)", required = true)
    @NotNull(message = "计划周期(day-日, week-周, month-月)不能为空")
    private String periodType;
    
    @Schema(description = "计划天数", required = true)
    private Integer numDay;

    @Schema(description = "执行日(日-不需要填;周-(1-7);月-(1-31))", required = true)
    @NotNull(message = "执行日(周-(1-7);月-(1-31))不能为空")
    private String executionDay;

    @Schema(description = "执行时间点", required = true)
    @NotNull(message = "执行时间点不能为空")
    private String executionTime;

    @Schema(description = "Cron表达式")
    private String executionCron;

    @Schema(description = "状态(0-未启用, 1-启用)")
    private Byte status;

    @Schema(description = "上次执行时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date lastExecutionTime;

    @Schema(description = "下次执行时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date nextExecutionTime;

    @Schema(description = "租户编号")
    private Long tenantId;

    @Schema(description = "管理员设置的租户id")
    private Long sysSettingTenant;

    @Schema(description = "平台id,多个平台ID，以逗号分隔")
    private String platformIds;

    @Schema(description = "标签id,多个标签ID，以逗号分隔")
    private String tags;

    @Schema(description = "job编号")
    private Long jobId;

}
