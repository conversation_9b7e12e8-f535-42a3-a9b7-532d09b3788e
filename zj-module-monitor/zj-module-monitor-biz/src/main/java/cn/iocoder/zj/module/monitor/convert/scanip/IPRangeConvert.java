package cn.iocoder.zj.module.monitor.convert.scanip;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.monitor.controller.admin.scanip.vo.IPRangeVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.scanip.IPRangeDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * IP段 Convert
 */
@Mapper
public interface IPRangeConvert {

    IPRangeConvert INSTANCE = Mappers.getMapper(IPRangeConvert.class);

    /**
     * VO 转 DO
     */
    IPRangeDO convert(IPRangeVO bean);

    /**
     * DO 转 VO
     */
    IPRangeVO convert(IPRangeDO bean);

    /**
     * DO 列表转 VO 列表
     */
    List<IPRangeVO> convertList(List<IPRangeDO> list);

    /**
     * 分页 DO 转 分页 VO
     */
    PageResult<IPRangeVO> convertPage(PageResult<IPRangeDO> page);
}