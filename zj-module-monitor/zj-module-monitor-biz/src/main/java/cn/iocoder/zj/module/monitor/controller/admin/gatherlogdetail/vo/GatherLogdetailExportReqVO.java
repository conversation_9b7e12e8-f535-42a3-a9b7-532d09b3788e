package cn.iocoder.zj.module.monitor.controller.admin.gatherlogdetail.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 告警日志 Excel 导出 Request VO，参数和 GatherLogdetailPageReqVO 是一致的")
@Data
public class GatherLogdetailExportReqVO {

    @Schema(description = "租户绑定的采集设备id")
    private String uuid;

    @Schema(description = "trap告警ip")
    private String ip;

    @Schema(description = "主机名称")
    private String hostName;

    @Schema(description = "告警位置")
    private String alarmLocation;

    @Schema(description = "告警时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] alarmDate;

    @Schema(description = "告警详情")
    private String alarmDetail;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] createTime;

    @Schema(description = "租户名称")
    private String PlatformName;

}
