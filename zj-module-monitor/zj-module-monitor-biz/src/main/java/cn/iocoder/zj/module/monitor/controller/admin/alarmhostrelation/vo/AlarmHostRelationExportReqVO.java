package cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 告警配置与云主机关联关系 Excel 导出 Request VO，参数和 AlarmHostRelationPageReqVO 是一致的")
@Data
public class AlarmHostRelationExportReqVO {

    @Schema(description = "告警配合名称")
    private String alarmName;

    @Schema(description = "告警配置ID")
    private Long alarmId;

    @Schema(description = "主机名称")
    private String hostName;

    @Schema(description = "云主机uuid")
    private String hostUuid;

    @Schema(description = "租户名称")
    private String tenantName;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
