package cn.iocoder.zj.module.monitor.controller.admin.tagplan.vo;

import cn.iocoder.zj.module.monitor.dal.dataobject.tags.TagsDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 巡检计划 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TagPlanRespVO extends TagPlanBaseVO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "报告数")
    private Integer reportCount;

    @Schema(description = "租户名称")
    private String tenantName;

    @Schema(description = "平台名称，多的用,分隔")
    private String platformName;

    @Schema(description = "标签名称，多的用,分隔")
    private String tagsName;

    private List<TagsDO> tagsDOS;

}
