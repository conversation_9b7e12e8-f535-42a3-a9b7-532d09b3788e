package cn.iocoder.zj.module.monitor.convert.imageinfo;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.api.imageinfo.dto.ImageInfoCreateReqDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.imageinfo.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.imageinfo.ImageInfoDO;

/**
 * 镜像信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ImageInfoConvert {

    ImageInfoConvert INSTANCE = Mappers.getMapper(ImageInfoConvert.class);

    ImageInfoDO convert(ImageInfoCreateReqVO bean);

    ImageInfoDO convert(ImageInfoUpdateReqVO bean);

    ImageInfoRespVO convert(ImageInfoDO bean);

    List<ImageInfoDO> convertCreateList(List<ImageInfoCreateReqVO> list);

    List<ImageInfoRespVO> convertList(List<ImageInfoDO> list);

    List<ImageInfoCreateReqVO> convertCreateListReq(List<ImageInfoCreateReqDTO> reqDTO);

    PageResult<ImageInfoRespVO> convertPage(PageResult<ImageInfoDO> page);

    List<ImageInfoExcelVO> convertList02(List<ImageInfoDO> list);

    List<ImageInfoCreateReqDTO> convertListReqDTO(List<ImageInfoDO> list);

}
