package cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo;

import cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo.AlarmConfigBaseVO;
import cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo.AlarmInfoRespVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 告警信息详情")
@Data
public class AlarmDetailVO extends AlarmConfigBaseVO {
    private Long alarmNum;
}
