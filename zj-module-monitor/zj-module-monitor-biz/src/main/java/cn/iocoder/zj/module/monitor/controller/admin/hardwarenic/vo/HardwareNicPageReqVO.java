package cn.iocoder.zj.module.monitor.controller.admin.hardwarenic.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 物理机网络关系分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HardwareNicPageReqVO extends PageParam {

    @Schema(description = "网卡uuid(v3)")
    private String uuid;

    @Schema(description = "宿主机uuid")
    private String hardwareUuid;

    @Schema(description = "mac")
    private String mac;

    @Schema(description = "网卡类型")
    private String networkType;

    @Schema(description = "ip地址")
    private String ipAddresses;

    @Schema(description = "ip子网")
    private String ipSubnet;

    @Schema(description = "二层网络uuid")
    private String l2NetworkUuid;

    @Schema(description = "二层网络名称")
    private String l2NetworkName;

    @Schema(description = "是否离线")
    private Boolean state;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

}
