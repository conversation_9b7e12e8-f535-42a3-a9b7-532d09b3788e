package cn.iocoder.zj.module.monitor.controller.admin.topreport;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.monitor.controller.admin.topreport.vo.*;
import cn.iocoder.zj.module.monitor.service.topreport.TopReportService;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import java.util.List;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

@Tag(name = "管理后台 - TOP报表")
@RestController
@RequestMapping("/monitor/top-report")
@Validated
public class TopReportController {

    @Resource
    private TopReportService topReportService;

    @Resource
    private AdminUserApi adminUserService;

    @PostMapping("/create")
    @Operation(summary = "创建TOP报表")
//    @PreAuthorize("@ss.hasPermission('monitor:top-report:create')")
    public CommonResult<Long> createTopReport(@Valid @RequestBody TopReportCreateReqVO createReqVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        AdminUserRespDTO adminUserRespDTO = adminUserService.getUserById(loginUser.getId()).getData();
        createReqVO.setTenantId(adminUserRespDTO.getTenantId());
        createReqVO.setTenantName(adminUserRespDTO.getTenantName());
        return success(topReportService.createTopReport(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新TOP报表")
//    @PreAuthorize("@ss.hasPermission('monitor:top-report:update')")
    public CommonResult<Boolean> updateTopReport(@Valid @RequestBody TopReportUpdateReqVO updateReqVO) {
        topReportService.updateTopReport(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除TOP报表")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('monitor:top-report:delete')")
    public CommonResult<Boolean> deleteTopReport(@RequestParam("id") Long id) {
        topReportService.deleteTopReport(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得TOP报表")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('monitor:top-report:query')")
    public CommonResult<TopReportRespVO> getTopReport(@RequestParam("id") Long id) {
        TopReportRespVO topReport = topReportService.getTopReport(id);
        return success(topReport);
    }

    @GetMapping("/page")
    @Operation(summary = "获得TOP报表分页")
//    @PreAuthorize("@ss.hasPermission('monitor:top-report:query')")
    public CommonResult<PageResult<TopReportRespVO>> getTopReportPage(@Valid TopReportPageReqVO pageVO) {
        PageResult<TopReportRespVO> pageResult = topReportService.getTopReportPage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/getdetail")
    @Operation(summary = "获得TOP报表")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('monitor:top-report:query')")
    public CommonResult<TopReportRespVO> getdetail(TopReportRespVO topReportRespVO) {
        TopReportRespVO topReport = topReportService.getdetail(topReportRespVO);
        return success(topReport);
    }

    @GetMapping("/getreportmetric")
    @Operation(summary = "获得指标下级信息")
    public CommonResult<List<ReportMetrics>> getreportmetric(Long id) {
        return success(topReportService.getreportmetric(id));
    }

    @GetMapping("/reportword")
    @Operation(summary = "导出TOP报表")
    @OperateLog(type = EXPORT)
//    @PreAuthorize("@ss.hasPermission('monitor:top-report:export')")
    public void exportReport(TopReportRespVO topReportRespVO,HttpServletResponse response) {
        topReportService.exportReport(topReportRespVO,response);
    }
}