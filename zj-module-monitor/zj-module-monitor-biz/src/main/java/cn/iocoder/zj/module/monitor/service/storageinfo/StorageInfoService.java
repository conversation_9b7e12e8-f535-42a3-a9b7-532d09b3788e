package cn.iocoder.zj.module.monitor.service.storageinfo;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.monitor.api.storage.dto.StorageRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.controller.admin.storageinfo.vo.StorageInfoCreateReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.storageinfo.vo.StorageInfoExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.storageinfo.vo.StorageInfoPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.storageinfo.vo.StorageInfoUpdateReqVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.storageinfo.StorageInfoDO;
import cn.iocoder.zj.module.monitor.pojo.AssetReqVO;
import org.apache.ibatis.annotations.Param;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 存储设备信息 Service 接口
 *
 * <AUTHOR>
 */
public interface StorageInfoService {

    /**
     * 创建存储设备信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createStorageInfo(@Valid StorageInfoCreateReqVO createReqVO);

    /**
     * 更新存储设备信息
     *
     * @param updateReqVO 更新信息
     */
    void updateStorageInfo(@Valid StorageInfoUpdateReqVO updateReqVO);

    /**
     * 删除存储设备信息
     *
     * @param id 编号
     */
    void deleteStorageInfo(Long id);

    /**
     * 获得存储设备信息
     *
     * @param uuid@return 存储设备信息
     */
    StorageInfoDO getStorageInfo(String uuid);

    /**
     * 获得存储设备信息列表
     *
     * @param ids 编号
     * @return 存储设备信息列表
     */
    List<StorageInfoDO> getStorageInfoList(Collection<Long> ids);
    PageResult<StorageInfoDO> getStorageInfoPage(StorageInfoPageReqVO pageReqVO);

    /**
     * 获得存储设备信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 存储设备信息列表
     */
    List<StorageInfoDO> getStorageInfoList(StorageInfoExportReqVO exportReqVO);

    void createStorageInfoList(List<StorageInfoDO> list);

    int getCount(String typeName);


    void updateHostInfoList(List<StorageRespCreateReqDTO> list);
    List<StorageInfoDO> getAll(String typeName);

    List<Map> getStorageCpuInfo(Long id, String uuid, String timeStr);

    List<Object> getUsedCapacityInPercent( String uuid, Long startTime, Long endTime);

    Map<String,Object> getStorageCapacity(List<String> tenantIds,Long platformId,Long regionId);

    Map<String,Object> getAllUsedCapacityInPercent(List<String> tenantId, Long platformId, Long regionId,String time);

    int deleteStorageList(List<StorageInfoDO> list);

    Long getCountByPlatformId(Long platformId);


    PageResult<StorageInfoDO> getStorageInfoSlavePage(StorageInfoPageReqVO pageVO);

    StorageInfoDO getStorageSlaveInfo(String uuid);

    StorageInfoDO getStorageMasterInfo(String uuid);
    List<StorageInfoDO> getListByPlatformId(Collection<Long> platformId);

    List<StorageInfoDO> getStorageListByUuids(List<String> uuids);

    PageResult<StorageInfoDO> selectStorageList(StorageInfoPageReqVO pageVO);

    List<StorageInfoDO> getListAll();

    Map<String, Object> getStorageStatusCount(List<String> tenantIds, Long platformId);

    void deleteStorageInfoByplatform(@Param("platformId") Long platformId);

    List<StorageRespCreateReqDTO> getStorageByPlatformId(Long id);

    List<StorageInfoDO> getStoragesByTenantOrPlatforms(AssetReqVO assetReqVO);

}
