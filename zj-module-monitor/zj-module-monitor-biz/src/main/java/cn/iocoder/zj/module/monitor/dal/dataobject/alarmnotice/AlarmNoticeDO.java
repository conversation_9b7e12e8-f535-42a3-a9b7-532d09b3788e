package cn.iocoder.zj.module.monitor.dal.dataobject.alarmnotice;

import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 告警与通知模板关系 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_alarm_notice")
@KeySequence("monitor_alarm_notice_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmNoticeDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 告警配置ID
     */
    private Long alarmConfigId;
    /**
     * 通知模板id
     */
    private Long templateId;
    /**
     * 模板类型sms短信，mail邮箱
     */
    private String templateType;
    /**
     * 邮箱地址
     */
    private String mail;

}
