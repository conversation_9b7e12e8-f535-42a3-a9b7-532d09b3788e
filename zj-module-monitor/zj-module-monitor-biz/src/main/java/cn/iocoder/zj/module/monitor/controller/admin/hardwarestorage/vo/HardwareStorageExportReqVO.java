package cn.iocoder.zj.module.monitor.controller.admin.hardwarestorage.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 宿主机与存储关联 Excel 导出 Request VO，参数和 HardwareStoragePageReqVO 是一致的")
@Data
public class HardwareStorageExportReqVO {

    @Schema(description = "宿主机id")
    private Long hardwareId;

    @Schema(description = "宿主机uuid")
    private String hardwareUuid;

    @Schema(description = "主存储id")
    private Long storageId;

    @Schema(description = "主存储uuid")
    private String storageUuid;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

}
