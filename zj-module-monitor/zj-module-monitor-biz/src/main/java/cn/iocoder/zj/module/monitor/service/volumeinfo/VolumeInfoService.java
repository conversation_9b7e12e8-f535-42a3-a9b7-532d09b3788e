package cn.iocoder.zj.module.monitor.service.volumeinfo;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeAttachableVmDTO;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeDTO;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeSnapshotDTO;
import cn.iocoder.zj.module.monitor.controller.admin.volumeinfo.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.volumeinfo.VolumeInfoDO;
import cn.iocoder.zj.module.monitor.pojo.AssetReqVO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 云盘信息 Service 接口
 *
 * <AUTHOR>
 */
public interface VolumeInfoService {

    /**
     * 创建云盘信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createVolumeInfo(@Valid VolumeInfoCreateReqVO createReqVO);

    /**
     * 更新云盘信息
     *
     * @param updateReqVO 更新信息
     */
    void updateVolumeInfo(@Valid VolumeInfoUpdateReqVO updateReqVO);

    /**
     * 获得云盘信息
     *
     * @param id 编号
     * @return 云盘信息
     */
    VolumeInfoDO getVolumeInfo(Long id);

    /**
     * 获得云盘信息列表
     *
     * @param ids 编号
     * @return 云盘信息列表
     */
    List<VolumeInfoDO> getVolumeInfoList(Collection<Long> ids);

    /**
     * 获得云盘信息分页
     *
     * @param pageReqVO 分页查询
     * @return 云盘信息分页
     */
    PageResult<VolumeInfoDO> getVolumeInfoPage(VolumeInfoPageReqVO pageReqVO);

    /**
     * 获得云盘信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 云盘信息列表
     */
    List<VolumeInfoDO> getVolumeInfoList(VolumeInfoExportReqVO exportReqVO);
    Long getVolumeCount();


    List<VolumeDTO> getAll(Long id,String typeCode);

    void updateVolumes(List<VolumeDTO> updateReqVOs);

    void addVolumes(List<VolumeInfoDO> updateReqVOs);
    CommonResult<Map<String,String>> mountVolumeToHost(String volumeUuid, String hostUuid, Long platformId);

    void mountVolumeToHardware(String volumeUuid, String hardwareUuid, Long platformId, String mountPath);

    CommonResult<Map<String,String>> uninstallVolumeFromHost(String volumeUuid, String hostUuid, Long platformId);

    void uninstallVolumeFromHardware(String volumeUuid, String hardwareUuid, Long platformId);

    PageResult<VolumeInfoRespVO> getVolumeAttachableVmByHostUuid(String hostUuid,Integer pageNo,Integer pageSize, String queryData);

    Long getAttachableVmCount();

    List<VolumeAttachableVmDTO> getAllVolumeAttachableVm();

    void addVolumeAttachableVms(List<VolumeAttachableVmDTO> shardingData);

    void updateVolumeAttachableVms(List<VolumeAttachableVmDTO> shardingData);

    void delVolumeAttachableVms(List<VolumeAttachableVmDTO> deleteTarget);

    void delVolumes(List<VolumeDTO> deleteTarget);

    void delVolumeSnapshots(List<VolumeSnapshotDTO> deleteTarget);

    void delVolumesByplatform(Long platformId);

    void delVolumeAttachableVmByplatform(Long platformId);

    void delVolumeByInstanceUuid(List<String> deleteList);

    List<VolumeInfoUseReqVO> getVolumeInfoListByUuid(String uuid);

    List<VolumeDTO> getVolumeByPlatformId(Long id);


    String getVolumeByVmUuid(String domainId);

    List<VolumeDTO> getVolumesByPlatformId(Long id);

    Integer getVolumeCountByStorageUuid(String storageUuid);

    Map<String, Object> getVolumeStatusCount(List<String> tenantIds, Long platformId);

    List<VolumeSnapshotDTO> getVolumeSnapshotByPlatformId(Long id);

    List<VolumeInfoDO> getVolumeByTenantOrPlatforms(AssetReqVO assetReqVO);

    List<VolumeInfoDO> getByPlatformIdAndTags(AssetReqVO reqVO);
}
