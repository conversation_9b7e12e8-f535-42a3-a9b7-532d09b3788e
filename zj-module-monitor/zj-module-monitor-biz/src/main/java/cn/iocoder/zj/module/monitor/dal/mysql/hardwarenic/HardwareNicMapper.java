package cn.iocoder.zj.module.monitor.dal.mysql.hardwarenic;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.api.hardwarenic.HardWareNicRespDTO;
import cn.iocoder.zj.module.monitor.controller.admin.hardwarenic.vo.HardwareNicExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.hardwarenic.vo.HardwareNicPageReqVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.hardwarenic.HardwareNicDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 物理机网络关系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface HardwareNicMapper extends BaseMapperX<HardwareNicDO> {

    default PageResult<HardwareNicDO> selectPage(HardwareNicPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<HardwareNicDO>()
                .eqIfPresent(HardwareNicDO::getUuid, reqVO.getUuid())
                .eqIfPresent(HardwareNicDO::getHardwareUuid, reqVO.getHardwareUuid())
                .eqIfPresent(HardwareNicDO::getMac, reqVO.getMac())
                .eqIfPresent(HardwareNicDO::getNetworkType, reqVO.getNetworkType())
                .eqIfPresent(HardwareNicDO::getIpAddresses, reqVO.getIpAddresses())
                .eqIfPresent(HardwareNicDO::getIpSubnet, reqVO.getIpSubnet())
                .eqIfPresent(HardwareNicDO::getL2NetworkUuid, reqVO.getL2NetworkUuid())
                .likeIfPresent(HardwareNicDO::getL2NetworkName, reqVO.getL2NetworkName())
                .eqIfPresent(HardwareNicDO::getState, reqVO.getState())
                .betweenIfPresent(HardwareNicDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(HardwareNicDO::getId));
    }

    @TenantIgnore
    default List<HardwareNicDO> selectList(HardwareNicExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<HardwareNicDO>()
                .eqIfPresent(HardwareNicDO::getUuid, reqVO.getUuid())
                .eqIfPresent(HardwareNicDO::getHardwareUuid, reqVO.getHardwareUuid())
                .eqIfPresent(HardwareNicDO::getMac, reqVO.getMac())
                .eqIfPresent(HardwareNicDO::getNetworkType, reqVO.getNetworkType())
                .eqIfPresent(HardwareNicDO::getIpAddresses, reqVO.getIpAddresses())
                .eqIfPresent(HardwareNicDO::getIpSubnet, reqVO.getIpSubnet())
                .eqIfPresent(HardwareNicDO::getL2NetworkUuid, reqVO.getL2NetworkUuid())
                .likeIfPresent(HardwareNicDO::getL2NetworkName, reqVO.getL2NetworkName())
                .eqIfPresent(HardwareNicDO::getState, reqVO.getState())
                .betweenIfPresent(HardwareNicDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(HardwareNicDO::getId));
    }
    @TenantIgnore
    List<HardWareNicRespDTO> getByPlatformId(Long platformId);

    int deletes(@Param("list") List<HardwareNicDO> list);
}
