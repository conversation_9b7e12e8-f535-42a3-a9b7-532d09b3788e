package cn.iocoder.zj.module.monitor.service.hostsecgroup;

import java.util.*;
import javax.validation.*;
import cn.iocoder.zj.module.monitor.controller.admin.hostsecgroup.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostsecgroup.HostSecgroupDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 安全组关联云主机 Service 接口
 *
 * <AUTHOR>
 */
public interface HostSecgroupService {

    /**
     * 创建安全组关联云主机
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createHostSecgroup(@Valid HostSecgroupCreateReqVO createReqVO);

    /**
     * 更新安全组关联云主机
     *
     * @param updateReqVO 更新信息
     */
    void updateHostSecgroup(@Valid HostSecgroupUpdateReqVO updateReqVO);

    /**
     * 删除安全组关联云主机
     *
     * @param id 编号
     */
    void deleteHostSecgroup(Long id);

    /**
     * 获得安全组关联云主机
     *
     * @param id 编号
     * @return 安全组关联云主机
     */
    HostSecgroupDO getHostSecgroup(Long id);

    /**
     * 获得安全组关联云主机列表
     *
     * @param ids 编号
     * @return 安全组关联云主机列表
     */
    List<HostSecgroupDO> getHostSecgroupList(Collection<Long> ids);

    /**
     * 获得安全组关联云主机分页
     *
     * @param pageReqVO 分页查询
     * @return 安全组关联云主机分页
     */
    PageResult<HostSecgroupDO> getHostSecgroupPage(HostSecgroupPageReqVO pageReqVO);

    /**
     * 获得安全组关联云主机列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 安全组关联云主机列表
     */
    List<HostSecgroupDO> getHostSecgroupList(HostSecgroupExportReqVO exportReqVO);

    void createHostSecgroupList(List<HostSecgroupDO> list);

    void updateHostSecgroups(List<HostSecgroupDO> list);

    void deleteHostSecgroups(List<HostSecgroupDO> list);
}
