package cn.iocoder.zj.module.monitor.controller.admin.alarminfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 监控告警详情 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AlarmInfoRespVO extends AlarmInfoBaseVO {

    @Schema(description = "主键", required = true)
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
