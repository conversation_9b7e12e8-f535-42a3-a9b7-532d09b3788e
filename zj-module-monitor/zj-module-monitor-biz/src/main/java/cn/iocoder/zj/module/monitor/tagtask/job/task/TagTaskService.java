package cn.iocoder.zj.module.monitor.tagtask.job.task;

import cn.iocoder.zj.module.monitor.tagtask.job.model.TagTaskModel;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface TagTaskService {

    /**
     * 查询任务列表
     *
     * @return
     */
    List<TagTaskModel> listTasks();

    /**
     * 根据任务id移除任务
     */
    void removeTask(TagTaskModel taskCacheModel);

    /**
     * 新增任务
     */
    void addTask(TagTaskModel taskCacheModel);

    /**
     * 根据任务id更新任务
     */
    void updateTasByJobId(TagTaskModel taskCacheModel);

    /**
     * 根据任务id查询任务
     */
    TagTaskModel getTaskByJobId(Long jobId);

}
