package cn.iocoder.zj.module.monitor.controller.admin.taggables.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 标签绑定关系 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class TaggablesExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("标签id")
    private Long tagId;

    @ExcelProperty("绑定资产id ")
    private Long taggableId;

    @ExcelProperty("绑定资产类型")
    private String taggableType;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
