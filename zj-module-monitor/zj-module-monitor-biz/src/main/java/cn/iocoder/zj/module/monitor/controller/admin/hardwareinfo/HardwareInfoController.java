package cn.iocoder.zj.module.monitor.controller.admin.hardwareinfo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.controller.admin.hardwareinfo.vo.*;
import cn.iocoder.zj.module.monitor.controller.admin.taggables.vo.TaggablesExportReqVO;
import cn.iocoder.zj.module.monitor.convert.hardwareinfo.HardwareInfoConvert;
import cn.iocoder.zj.module.monitor.convert.tags.TagsConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.hardwareinfo.HardwareInfoDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.tags.TagsDO;
import cn.iocoder.zj.module.monitor.service.alarmhostrelation.AlarmHostRelationService;
import cn.iocoder.zj.module.monitor.service.hardwareinfo.HardwareInfoService;
import cn.iocoder.zj.module.monitor.service.taggables.enums.TagAssetTypeEnum;
import cn.iocoder.zj.module.monitor.service.tags.TagsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

@Tag(name = "管理后台 - 硬件设施基本信息")
@RestController
@RequestMapping("/monitor/hardware-info")
@Validated
public class HardwareInfoController {

    @Resource
    private HardwareInfoService hardwareInfoService;
    @Resource
    private AlarmHostRelationService alarmHostRelationService;
    @Resource
    private TagsService tagsService;

    @PostMapping("/create")
    @Operation(summary = "创建硬件设施基本信息")
    @PreAuthorize("@ss.hasPermission('monitor:hardware-info:create')")
    @OperateLog(type = CREATE)
    public CommonResult<Long> createHardwareInfo(@Valid @RequestBody HardwareInfoCreateReqVO createReqVO) {
        return success(hardwareInfoService.createHardwareInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新硬件设施基本信息")
    @PreAuthorize("@ss.hasPermission('monitor:hardware-info:update')")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateHardwareInfo(@Valid @RequestBody HardwareInfoUpdateReqVO updateReqVO) {
        hardwareInfoService.updateHardwareInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除硬件设施基本信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('monitor:hardware-info:delete')")
    @OperateLog(type = DELETE)
    public CommonResult<Boolean> deleteHardwareInfo(@RequestParam("id") Long id) {
        hardwareInfoService.deleteHardwareInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得硬件设施基本信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<HardwareInfoRespVO> getHardwareInfo(@RequestParam("uuid") String uuid) {
        HardwareInfoDO hardwareInfo = hardwareInfoService.getHardwareInfo(uuid);
        Collection<Long> alarmIds = alarmHostRelationService.getRelationIdsByUuid(hardwareInfo.getUuid());
        HardwareInfoRespVO respVO = BeanUtil.copyProperties(hardwareInfo, HardwareInfoRespVO.class);
        //宿主机对应云主机数量
        Integer cloudHostCount=hardwareInfoService.getCloudHostCount(uuid);
        respVO.setCloudHostCount(cloudHostCount);
        Integer hardwareCount=hardwareInfoService.getHardwareCount(hardwareInfo.getPlatformId());

        //标签
        List<TagsDO> tag = tagsService.getTagListByTagGables(new TaggablesExportReqVO()
                .setTaggableId(respVO.getId())
                .setTaggableType(TagAssetTypeEnum.HOST.getCode())
        );
        respVO.setTags(TagsConvert.INSTANCE.convertList(tag));

        respVO.setHardwareCount(hardwareCount);
        respVO.setAlarmIds(alarmIds);
        return success(respVO);
    }

    @GetMapping("/getCpuInfo")
    @Operation(summary = "获得硬件设施CPU数据")
    @Parameter(name = "tenantid", description = "租户ID", required = true, example = "1")
    @Parameter(name = "uuid", description = "主机UUID", required = true, example = "09d9ff4a3df44def95051874fd2d3fe2")
    @Parameter(name = "timeStr", description = "时间条件,1h(1小时),1d(1天),1M/d(1月),1y/d(1年)", required = true, example = "24h")
    public CommonResult<List<Map>> getHardwareCpuInfo(@RequestParam("tenantid") Long tenantid, @RequestParam("uuid") String uuid, @RequestParam("timeSec") String timeStr) {
        List<Map> hardwareCpuInfo = hardwareInfoService.getHardwareCpuInfo(tenantid, uuid, timeStr);
        return success(hardwareCpuInfo);
    }

    @GetMapping("/getDiskInfo")
    @Operation(summary = "获得硬件设施磁盘数据")
    @Parameter(name = "tenantid", description = "租户ID", required = true, example = "1")
    @Parameter(name = "uuid", description = "主机UUID", required = true, example = "09d9ff4a3df44def95051874fd2d3fe2")
    @Parameter(name = "timeStr", description = "时间条件,1h(1小时),1d(1天),1M/d(1月),1y/d(1年)", required = true, example = "24h")
    public CommonResult<List<Map>> getHardwareDiskInfo(@RequestParam("tenantid") Long tenantid, @RequestParam("uuid") String uuid, @RequestParam("timeStr") String timeStr) {
        List<Map> hardwareDiskInfo = hardwareInfoService.getHardwareDiskInfo(tenantid, uuid, timeStr);
        return success(hardwareDiskInfo);
    }

    @GetMapping("/getMemoryInfo")
    @Operation(summary = "获得硬件设施内存数据")
    @Parameter(name = "tenantid", description = "租户ID", required = true, example = "1")
    @Parameter(name = "uuid", description = "主机UUID", required = true, example = "09d9ff4a3df44def95051874fd2d3fe2")
    @Parameter(name = "timeStr", description = "时间条件,1h(1小时),1d(1天),1M/d(1月),1y/d(1年)", required = true, example = "24h")
    public CommonResult<List<Map>> getHardwareMemoryInfo(@RequestParam("tenantid") Long tenantid, @RequestParam("uuid") String uuid, @RequestParam("timeStr") String timeStr) {
        List<Map> hardwareMemoryInfo = hardwareInfoService.getHardwareMemoryInfo(tenantid, uuid, timeStr);
        return success(hardwareMemoryInfo);
    }

    @GetMapping("/getNetworkInfo")
    @Operation(summary = "获得硬件设施网卡数据")
    @Parameter(name = "tenantid", description = "租户ID", required = true, example = "1")
    @Parameter(name = "uuid", description = "主机UUID", required = true, example = "09d9ff4a3df44def95051874fd2d3fe2")
    @Parameter(name = "timeStr", description = "时间条件,1h(1小时),1d(1天),1M/d(1月),1y/d(1年)", required = true, example = "24h")
    public CommonResult<List<Map>> getHardwareNetworkInfo(@RequestParam("tenantid") Long tenantid, @RequestParam("uuid") String uuid, @RequestParam("timeStr") String timeStr) {
        List<Map> hardwareNetworkInfo = hardwareInfoService.getHardwareNetworkInfo(tenantid, uuid, timeStr);
        return success(hardwareNetworkInfo);
    }

    @GetMapping("/list")
    @Operation(summary = "获得硬件设施基本信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    public CommonResult<List<HardwareInfoRespVO>> getHardwareInfoList(@RequestParam("ids") Collection<Long> ids) {
        List<HardwareInfoDO> list = hardwareInfoService.getHardwareInfoList(ids);
        return success(HardwareInfoConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得硬件设施基本信息分页")
    @TenantIgnore
    public CommonResult<PageResult<HardwareInfoRespVO>> getHardwareInfoPage(@Valid HardwareInfoPageReqVO pageVO) {
        PageResult<HardwareInfoDO> pageResult = hardwareInfoService.getHardwareInfoPage(pageVO);
        List<HardwareInfoRespVO> infoRespVOS = new ArrayList<>();
        if (pageResult.getList() != null && !pageResult.getList().isEmpty()) {
            infoRespVOS = BeanUtil.copyToList(pageResult.getList(), HardwareInfoRespVO.class);
        }
        for (HardwareInfoRespVO respVO : infoRespVOS) {
            List<TagsDO> tag = tagsService.getTagListByTagGables(new TaggablesExportReqVO()
                    .setTaggableId(respVO.getId())
                    .setTaggableType(TagAssetTypeEnum.HOST.getCode())
            );
            respVO.setTags(TagsConvert.INSTANCE.convertList(tag));
        }
        PageResult<HardwareInfoRespVO> pageResults = new PageResult<>();
        pageResults.setList(infoRespVOS);
        pageResults.setTotal(pageResult.getTotal());
        return success(pageResults);
    }

    @GetMapping("/getHardwareList")
    @Operation(summary = "获得硬件设施基本信息分页")
    @TenantIgnore
    public CommonResult<List<HardwareInfoDO>> getHardwareList(@RequestParam("platformId") Long platformId) {
        List<HardwareInfoDO> list = hardwareInfoService.getHardwareListByPlatformId(platformId);
        return success(list);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出硬件设施基本信息 Excel")
    @OperateLog(type = EXPORT)
    @PreAuthorize("@ss.hasPermission('monitor:hardware-info:export')")
    public void exportHardwareInfoExcel(@Valid HardwareInfoExportReqVO exportReqVO,
                                        HttpServletResponse response) throws IOException {
        List<HardwareInfoDO> list = hardwareInfoService.getHardwareInfoList(exportReqVO);
        // 导出 Excel
        List<HardwareInfoExcelVO> datas = HardwareInfoConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "硬件设施基本信息.xls", "数据", HardwareInfoExcelVO.class, datas);
    }

    @GetMapping("/getHardwareGraphData")
    @Operation(summary = "详情页获取宿主机CUP、内存、磁盘、网卡数据")
    public CommonResult<List<MonitorHardwareDataRespVo>> getHardwareGraphData(MonitorHardwareDataReqVo reqVo) {
        List<MonitorHardwareDataRespVo> hardwareGraphData = new ArrayList<>();
        if (StrUtil.isNotEmpty(Convert.toStr(reqVo.getSearchWay())) && reqVo.getSearchWay().equals("sample")) {
            hardwareGraphData = hardwareInfoService.getHardwareGraphDataRandom(reqVo);
        } else {
            hardwareGraphData = hardwareInfoService.getHardwareGraphData(reqVo);
        }
        return success(hardwareGraphData.stream()
                .peek(data -> {
                    if (data.getValue() == null) {
                        data.setValue(0.0);
                    }
                })
                .collect(Collectors.toList()));
    }


    @GetMapping("/getHostLabel")
    @Operation(summary = "获得宿主机详情页标签")
    @Parameter(name = "platformId", description = "平台id", required = true, example = "1")
    @Parameter(name = "uuid", description = "主机UUID", required = true, example = "2cec14a81a234ae9be9c917bb8937926")
    @Parameter(name = "labelKey", description = "标签值 1：代表CPU下面的标签， 2：代表磁盘下面的标签，3：代表网卡标签", required = true, example = "1")
    public CommonResult<List<Map<String, Object>>> getLabel(@RequestParam("platformId") Long platformId, @RequestParam("uuid") String uuid, @RequestParam("labelKey") int labelKey) {
        List<Map<String, Object>> label = hardwareInfoService.getLabel(platformId, uuid, labelKey);
        return success(label);
    }



    @GetMapping("/slavePage")
    @Operation(summary = "获得硬件设施基本信息分页(从库数据)")
    @TenantIgnore
    public CommonResult<PageResult<HardwareInfoRespVO>> getHardwareInfoSlavePage(@Valid HardwareInfoPageReqVO pageVO) {
        PageResult<HardwareInfoDO> pageResult = hardwareInfoService.getHardwareInfoSlavePage(pageVO);
        return success(HardwareInfoConvert.INSTANCE.convertPage(pageResult));
    }


    @GetMapping("/slaveget")
    @Operation(summary = "获得硬件设施基本信息(从库数据)")
    @Parameter(name = "uuid", description = "编号", required = true, example = "1024")
    public CommonResult<HardwareInfoRespVO> getHardwareSlaveInfo(@RequestParam("uuid") String uuid) {
        HardwareInfoDO hardwareInfo = hardwareInfoService.getHardwareSlaveInfo(uuid);
        HardwareInfoRespVO respVO = HardwareInfoConvert.INSTANCE.convert(hardwareInfo);
        return success(respVO);
    }


    @GetMapping("/masterget")
    @Operation(summary = "获得硬件设施基本信息(主库数据)")
    @Parameter(name = "uuid", description = "编号", required = true, example = "1024")
    public CommonResult<HardwareInfoRespVO> masterget(@RequestParam("uuid") String uuid) {
        HardwareInfoDO hardwareInfo = hardwareInfoService.getHardwareMasterInfo(uuid);
        HardwareInfoRespVO respVO = HardwareInfoConvert.INSTANCE.convert(hardwareInfo);
        return success(respVO);
    }

    @GetMapping("/clusterSimpleInfo")
    @Operation(summary = "集群下拉选择框信息")
    @Parameter(name = "platformId", description = "平台id", required = true, example = "1024,1023")
    public CommonResult<List<Map<String,String>>> getClusterSimpleInfo(@RequestParam("platformId") Collection<Long> platformId) {
        return success(hardwareInfoService.getClusterSimpleInfo(platformId));
    }

    @GetMapping("/selectHardwareList")
    @Operation(summary = "用于选择告警宿主机(只有name做查询条件)")
    @TenantIgnore
    public CommonResult<PageResult<HardwareInfoRespVO>> selectHardwareList(@Valid HardwareInfoPageReqVO pageVO) {
        PageResult<HardwareInfoDO> pageResult = hardwareInfoService.selectHardwareList(pageVO);
        return success(HardwareInfoConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/getInfoList")
    @Operation(summary = "获得硬件设施基本信息列表")
    @TenantIgnore
    public CommonResult<List<HardwareInfoDO>> getHardwareInfoList(@Valid HardwareInfoPageReqVO pageVO) {
        List<HardwareInfoDO> pageResult = hardwareInfoService.getHardwareInfoByList(pageVO);
        return success(pageResult);
    }
}
