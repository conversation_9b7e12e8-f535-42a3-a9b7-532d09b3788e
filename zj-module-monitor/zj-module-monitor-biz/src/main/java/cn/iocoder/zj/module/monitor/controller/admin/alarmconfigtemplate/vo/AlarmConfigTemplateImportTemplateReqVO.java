package cn.iocoder.zj.module.monitor.controller.admin.alarmconfigtemplate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotNull;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Schema(description = "管理后台 - 告警配置模板导入到模板 Request VO")
@Data
public class AlarmConfigTemplateImportTemplateReqVO {

    @Schema(description = "告警配置模板ID列表" , required = true)
    @NotNull(message = "告警配置模板ID列表不能为空")
    @NotEmpty(message = "告警配置模板ID列表不能为空")
    List<Long> ids;
}
