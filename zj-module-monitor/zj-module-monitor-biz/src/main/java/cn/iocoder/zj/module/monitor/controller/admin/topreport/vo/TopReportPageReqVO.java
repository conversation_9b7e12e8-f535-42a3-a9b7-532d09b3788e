package cn.iocoder.zj.module.monitor.controller.admin.topreport.vo;

import cn.iocoder.zj.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class TopReportPageReqVO extends PageParam {

    private String reportName;

    private Integer statisticsType;

    private String assetType;

    private List<String> tenantIds;
}
