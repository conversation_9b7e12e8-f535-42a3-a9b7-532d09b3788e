package cn.iocoder.zj.module.monitor.service.alarmhostrelation;

import java.util.*;

import cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmhostrelation.AlarmHostRelationDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 告警配置与云主机关联关系 Service 接口
 *
 * <AUTHOR>
 */
public interface AlarmHostRelationService {


    /**
     * 更新告警配置与云主机关联关系
     *
     */
    void updateAlarmHostRelation(RelationRqeVO reqVO);

    /**
     * 删除告警配置与云主机关联关系
     *
     * @param id 编号
     */
    void deleteAlarmHostRelation(Long id);

    /**
     * 获得告警配置与云主机关联关系
     *
     * @param id 编号
     * @return 告警配置与云主机关联关系
     */
    AlarmHostRelationDO getAlarmHostRelation(Long id);

    /**
     * 获得告警配置与云主机关联关系列表
     *
     * @param ids 编号
     * @return 告警配置与云主机关联关系列表
     */
    List<AlarmHostRelationDO> getAlarmHostRelationList(Collection<Long> ids);

    /**
     * 获得告警配置与云主机关联关系分页
     *
     * @param pageReqVO 分页查询
     * @return 告警配置与云主机关联关系分页
     */
    PageResult<AlarmHostRelationDO> getAlarmHostRelationPage(AlarmHostRelationPageReqVO pageReqVO);

    /**
     * 获得告警配置与云主机关联关系列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 告警配置与云主机关联关系列表
     */
    List<AlarmHostRelationDO> getAlarmHostRelationList(AlarmHostRelationExportReqVO exportReqVO);
    void deletedByAlarmConfigId(Long alarmConfigId);

    Long hostSetAlarms(RelationRqeVO reqVO);

    Long hostUpdateAlarms(RelationRqeVO reqVO);

    Collection<Long> getRelationIdsByUuid(String uuid);
}
