package cn.iocoder.zj.module.monitor.controller.admin.hardwareinfo.vo;

import cn.iocoder.zj.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 硬件设施基本信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HardwareInfoPageReqVO extends PageParam {

    @Schema(description = "主机id")
    private String uuid;

    @Schema(description = "主机名称")
    private String name;

    @Schema(description = "宿主机状态，包括：(启用)Enabled	(禁用)Disabled	(预维护)PreMaintenance	(维护)Maintenance")
    private String state;

    @Schema(description = "ip")
    private String ip;

    @Schema(description = "查询状态：(正在连接)Connecting，(已连接)Connected，(未连接)Disconnected")
    private String status;

    @Schema(description = "集群id")
    private String clusterUuid;

    @Schema(description = "集群名称")
    private String clusterName;

    @Schema(description = "cpu总容量")
    private Long totalCpuCapacity;

    @Schema(description = "cpu可用容量")
    private Long availableCpuCapacity;

    @Schema(description = "cpu 插槽")
    private Integer cpuSockets;

    @Schema(description = "CPU架构")
    private String architecture;

    @Schema(description = "分配cpu")
    private Integer cpuNum;

    @Schema(description = "内存总容量")
    private Long totalMemoryCapacity;

    @Schema(description = "内存可用容量")
    private Long availableMemoryCapacity;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "地区id")
    private Long regionId;

    @Schema(description = "查询条件，名称或IP")
    private String queryData;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "租户ID")
    private List<String> tenantId;

    @Schema(required = false,description = "主机过滤ids逗号拼接,'1','2','3'")
    private String ids;

    @Schema(required = false,description = "开始时间",example = "2023-06-07")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String startTime;

    @Schema(required = false,description = "结束时间",example = "2023-06-07")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String endTime;

    private Integer isMaintain;

    @Schema(description = "是否是列表",example = "true")
    private Boolean isList;


    @Schema(required = false,description = "主机篩選inIds逗号拼接,'1','2','3'")
    private String inIds;

    @Schema(required = false,description = "主键逗号拼接,'1','2','3'")
    private List<Long> inPks;

    @Schema(required = false,description = "主机tagId逗号拼接,1,2,3")
    private String tagIds;
}
