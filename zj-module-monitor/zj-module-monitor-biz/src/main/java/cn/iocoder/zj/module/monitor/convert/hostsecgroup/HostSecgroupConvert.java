package cn.iocoder.zj.module.monitor.convert.hostsecgroup;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.api.hostsecgroup.dto.HostSecgroupCreateReqDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.hostsecgroup.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostsecgroup.HostSecgroupDO;

/**
 * 安全组关联云主机 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface HostSecgroupConvert {

    HostSecgroupConvert INSTANCE = Mappers.getMapper(HostSecgroupConvert.class);

    HostSecgroupDO convert(HostSecgroupCreateReqVO bean);

    HostSecgroupDO convert(HostSecgroupUpdateReqVO bean);

    HostSecgroupRespVO convert(HostSecgroupDO bean);

    List<HostSecgroupRespVO> convertList(List<HostSecgroupDO> list);

    PageResult<HostSecgroupRespVO> convertPage(PageResult<HostSecgroupDO> page);

    List<HostSecgroupExcelVO> convertList02(List<HostSecgroupDO> list);

    List<HostSecgroupDO> convertCreateList(List<HostSecgroupCreateReqDto> reqDTO);

    List<HostSecgroupCreateReqDto> convertDoToCreateDtoList(List<HostSecgroupDO> hostSecgroupList);
}
