package cn.iocoder.zj.module.monitor.controller.admin.volumeinfo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.controller.admin.taggables.vo.TaggablesExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.volumeinfo.vo.*;
import cn.iocoder.zj.module.monitor.convert.tags.TagsConvert;
import cn.iocoder.zj.module.monitor.convert.volume.VolumeInfoConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.taggables.TaggablesDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.tags.TagsDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.volumeinfo.VolumeInfoDO;
import cn.iocoder.zj.module.monitor.service.taggables.TaggablesService;
import cn.iocoder.zj.module.monitor.service.taggables.enums.TagAssetTypeEnum;
import cn.iocoder.zj.module.monitor.service.tags.TagsService;
import cn.iocoder.zj.module.monitor.service.volumeinfo.VolumeInfoService;
import cn.iocoder.zj.module.monitor.util.StringUtil;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;
import static java.util.Collections.singleton;

@Tag(name = "管理后台 - 云盘信息")
@RestController
@RequestMapping("/monitor/volume-info")
@Validated
public class VolumeInfoController {

    @Resource
    private VolumeInfoService volumeInfoService;

    @Resource
    private RoleApi roleApi;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private TagsService tagsService;
    @Resource
    private TaggablesService taggablesService;

    @PostMapping("/create")
    @Operation(summary = "创建云盘信息")
    @TenantIgnore
    //@PreAuthorize("@ss.hasPermissionrmission('monitor:volume-info:create')")
    @OperateLog(type = CREATE)
    public CommonResult<Long> createVolumeInfo(@Valid @RequestBody VolumeInfoCreateReqVO createReqVO) {
        return success(volumeInfoService.createVolumeInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新云盘信息")
    @TenantIgnore
    //@PreAuthorize("@ss.hasPermissionrmission('monitor:volume-info:update')")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateVolumeInfo(@Valid @RequestBody VolumeInfoUpdateReqVO updateReqVO) {
        volumeInfoService.updateVolumeInfo(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得云盘信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @TenantIgnore
    public CommonResult<VolumeInfoRespVO> getVolumeInfo(@RequestParam("id") Long id) {
        VolumeInfoDO volumeInfo = volumeInfoService.getVolumeInfo(id);
        VolumeInfoRespVO respVO = VolumeInfoConvert.INSTANCE.convert(volumeInfo);
        //标签
        List<TagsDO> tag = tagsService.getTagListByTagGables(new TaggablesExportReqVO()
                .setTaggableId(respVO.getId())
                .setTaggableType(TagAssetTypeEnum.DISK.getCode())
        );
        respVO.setTags(TagsConvert.INSTANCE.convertList(tag));
        return success(respVO);
    }

    @GetMapping("/list")
    @Operation(summary = "获得云盘信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @TenantIgnore
    public CommonResult<List<VolumeInfoRespVO>> getVolumeInfoList(@RequestParam("ids") Collection<Long> ids) {
        List<VolumeInfoDO> list = volumeInfoService.getVolumeInfoList(ids);
        return success(VolumeInfoConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得云盘信息分页")
    @TenantIgnore
    public CommonResult<PageResult<VolumeInfoRespVO>> getVolumeInfoPage(@Valid VolumeInfoPageReqVO pageVO) {
        if (ObjectUtil.isNotEmpty(pageVO.getTagIds())) {
            List<TaggablesDO> tmp = taggablesService.getTaggablesList(new TaggablesExportReqVO()
                    .setTaggableType(TagAssetTypeEnum.DISK.getCode())
                    .setTagId(Long.valueOf(pageVO.getTagIds()))
            );

            if (!tmp.isEmpty()) {
                pageVO.setIds(tmp.stream().map(TaggablesDO::getTaggableId).distinct().toList());
            } else {
                return success(PageResult.empty());
            }
        }

        PageResult<VolumeInfoDO> pageResult = volumeInfoService.getVolumeInfoPage(pageVO);
        List<VolumeInfoRespVO> infoRespVOS = new ArrayList<>();
        if (pageResult.getList() != null && !pageResult.getList().isEmpty()) {
            infoRespVOS = BeanUtil.copyToList(pageResult.getList(), VolumeInfoRespVO.class);
        }
        for (VolumeInfoRespVO respVO : infoRespVOS) {
            List<TagsDO> tag = tagsService.getTagListByTagGables(new TaggablesExportReqVO()
                    .setTaggableId(respVO.getId())
                    .setTaggableType(TagAssetTypeEnum.DISK.getCode())
            );
            respVO.setTags(TagsConvert.INSTANCE.convertList(tag));
        }
        PageResult<VolumeInfoRespVO> pageResults = new PageResult<>();
        pageResults.setList(infoRespVOS);
        pageResults.setTotal(pageResult.getTotal());
        return success(pageResults);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出云盘信息 Excel")
    @OperateLog(type = EXPORT)
    @TenantIgnore
    public void exportVolumeInfoExcel(@Valid VolumeInfoExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        if (ObjectUtil.isNotEmpty(exportReqVO.getTagIds())) {
            List<TaggablesDO> tmp = taggablesService.getTaggablesList(new TaggablesExportReqVO()
                    .setTaggableType(TagAssetTypeEnum.DISK.getCode())
                    .setTagId(Long.valueOf(exportReqVO.getTagIds()))
            );

            if (!tmp.isEmpty()) {
                exportReqVO.setIds(tmp.stream().map(TaggablesDO::getTaggableId).distinct().toList());
            } else {
                return;
            }
        }
        List<VolumeInfoDO> list = volumeInfoService.getVolumeInfoList(exportReqVO);
        list.forEach(volume -> {
            String mediaType = volume.getMediaType();
            if (mediaType != null) {
                switch (mediaType.toLowerCase()) {
                    case "rotate":
                        volume.setMediaType("机械盘");
                        break;
                    case "ssd":
                        volume.setMediaType("固态硬盘");
                        break;
                    case "hybrid":
                    case "hybird":  // 处理枚举中的拼写
                        volume.setMediaType("混合盘");
                        break;
                    default:
                        // 保持原值不变
                        break;
                }
            }
        });
        // 导出 Excel
        List<VolumeInfoExcelVO> datas = VolumeInfoConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "云盘信息.xls", "数据", VolumeInfoExcelVO.class, datas);
    }
    @PostMapping("/mountVolumeToHost")
    @Operation(summary = "挂载云盘到云主机")
    @Parameter(name = "volumeUuid", description = "云盘uuid", required = true)
    @Parameter(name = "hostUuid", description = "云主机uuid", required = true)
    @Parameter(name = "platformId", description = "平台ID", required = true)
    @TenantIgnore
    //@PreAuthorize("@ss.hasPermissionrmission('monitor:volume-info:mount')")
    @OperateLog(type = UPDATE)
    public CommonResult<Map<String,String>> mountVolumeToHost(@Valid @RequestParam("volumeUuid")String volumeUuid,
                                                   @RequestParam("hostUuid")String hostUuid,
                                                   @RequestParam("platformId")Long platformId) {
        return volumeInfoService.mountVolumeToHost(volumeUuid,hostUuid,platformId);
    }

    @PostMapping("/uninstallVolumeFromHost")
    @Operation(summary = "从云主机卸载云盘")
    @Parameter(name = "volumeUuid", description = "云盘uuid", required = true)
    @Parameter(name = "hostUuid", description = "云主机uuid", required = true)
    @Parameter(name = "platformId", description = "平台ID", required = true)
    @TenantIgnore
    //@PreAuthorize("@ss.hasPermissionrmission('monitor:volume-info:remove')")
    @OperateLog(type = UPDATE)
    public CommonResult<Map<String,String>> uninstallVolumeFromHost(@Valid @RequestParam("volumeUuid")String volumeUuid,
                                                         @RequestParam("hostUuid")String hostUuid,
                                                         @RequestParam("platformId")Long platformId) {
        return volumeInfoService.uninstallVolumeFromHost(volumeUuid,hostUuid,platformId);
    }

    @PostMapping("/mountVolumeToHardware")
    @Operation(summary = "挂载云盘到宿主机")
    @Parameter(name = "volumeUuid", description = "云盘uuid", required = true)
    @Parameter(name = "hardwareUuid", description = "宿主机uuid", required = true)
    @Parameter(name = "platformId", description = "平台ID", required = true)
    @Parameter(name = "mountPath", description = "挂载路径", required = true, example="/test/mount/path")
    @TenantIgnore
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> mountVolumeToHardware(@Valid @RequestParam("volumeUuid")String volumeUuid,
                                                       @RequestParam("hardwareUuid")String hardwareUuid,
                                                       @RequestParam("platformId")Long platformId,
                                                       @RequestParam("mountPath")String mountPath) {
        volumeInfoService.mountVolumeToHardware(volumeUuid,hardwareUuid,platformId,mountPath);
        return success(true);
    }

    @PostMapping("/uninstallVolumeFromHardware")
    @Operation(summary = "从宿主机卸载云盘")
    @Parameter(name = "volumeUuid", description = "云盘uuid", required = true)
    @Parameter(name = "hardwareUuid", description = "宿主机uuid(选填)", required = false)
    @Parameter(name = "platformId", description = "平台ID", required = true)
    @TenantIgnore
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> uninstallVolumeFromHardware(@Valid @RequestParam("volumeUuid")String volumeUuid,
                                                             @RequestParam("hardwareUuid")String hardwareUuid,
                                                             @RequestParam("platformId")Long platformId) {
        volumeInfoService.uninstallVolumeFromHardware(volumeUuid,hardwareUuid,platformId);
        return success(true);
    }

    @PostMapping("/getDataVolumeAttachableVm")
    @Operation(summary = "获取可挂载的云盘列表")
    @Parameter(name = "hostUuid", description = "云主机uuid", required = true)
    @TenantIgnore
    public CommonResult<PageResult<VolumeInfoRespVO>> getDataVolumeAttachableVm(@Valid @RequestParam("hostUuid")String hostUuid,
                                                                                @RequestParam("pageNo") Integer pageNo,
                                                                                @RequestParam("pageSize") Integer pageSize,
                                                                                @RequestParam("queryData") String queryData) {

        return success( volumeInfoService.getVolumeAttachableVmByHostUuid(hostUuid,pageNo,pageSize,queryData));
    }


    /**
     * 获取卷状态统计信息
     *
     * 该函数根据当前登录用户的角色和租户信息，获取卷的状态统计信息。如果用户不是超级管理员，则根据用户的租户ID或服务租户ID进行过滤。
     *
     * @param platformId 平台ID，可选参数，默认为空。用于指定查询的平台ID。
     * @return CommonResult<Map<String,Object>> 返回一个包含卷状态统计信息的通用结果对象，其中Map的键为状态名称，值为对应的统计数量。
     */
    @GetMapping("/volume-status-count")
    @Operation(summary = "获取卷状态统计信息")
    //@PreAuthorize("@ss.hasPermissionrmission('monitor:volume-info:query')")
    @TenantIgnore
    public CommonResult<Map<String,Object>> getVolumeStatusCount(@RequestParam(required = false, defaultValue = "") Long platformId) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(loginUser.getId()).getData();
        List<String> tenantIds = new ArrayList<>();
        String tenantIdStr;
        if (!roleApi.hasAnySuperAdmin(roleIds) && !StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
            tenantIdStr = adminUserRespDTO.getServiceTenantId();
        } else {
            tenantIdStr = String.valueOf(adminUserRespDTO.getTenantId());
        }
        tenantIds = Arrays.asList(tenantIdStr.split(","));
        return success(volumeInfoService.getVolumeStatusCount(tenantIds,platformId));
    }
}
