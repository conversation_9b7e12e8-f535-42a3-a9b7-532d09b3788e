package cn.iocoder.zj.module.monitor.dal.mysql.gatherlogdetail;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.module.monitor.dal.dataobject.gatherlogdetail.GatherLogdetailDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkvpc.NetworkVpcDO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.monitor.controller.admin.gatherlogdetail.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 告警日志 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface GatherLogdetailMapper extends BaseMapperX<GatherLogdetailDO> {

    default PageResult<GatherLogdetailDO> selectPage(GatherLogdetailPageReqVO reqVO, boolean admin, List<Map> platform) {

        if (admin) {
            LambdaQueryWrapperX<GatherLogdetailDO> queryWrapperX = new LambdaQueryWrapperX<GatherLogdetailDO>()
                    .eqIfPresent(GatherLogdetailDO::getPlatformId, reqVO.getPlatformId())
                    .eqIfPresent(GatherLogdetailDO::getUuid, reqVO.getUuid())
                    .eqIfPresent(GatherLogdetailDO::getIp, reqVO.getIp())
                    .likeIfPresent(GatherLogdetailDO::getHostName, reqVO.getHostName())
                    .eqIfPresent(GatherLogdetailDO::getAlarmLocation, reqVO.getAlarmLocation())
                    .betweenIfPresent(GatherLogdetailDO::getAlarmDate, reqVO.getAlarmDate())
                    .eqIfPresent(GatherLogdetailDO::getAlarmDetail, reqVO.getAlarmDetail())
                    .betweenIfPresent(GatherLogdetailDO::getCreateTime, reqVO.getCreateTime())
                    .likeIfPresent(GatherLogdetailDO::getPlatformName, reqVO.getPlatformName())
                    .orderByDesc(GatherLogdetailDO::getId);
            return selectPage(reqVO, queryWrapperX);
        } else {
            List<String> data = new ArrayList<>();
            if (platform.size() > 0) {
                for (Map map : platform) {
                    data.add(map.get("platformId").toString());
                }
            }
            LambdaQueryWrapperX<GatherLogdetailDO> queryWrapperX = new LambdaQueryWrapperX<GatherLogdetailDO>()
                    .eqIfPresent(GatherLogdetailDO::getPlatformId, reqVO.getPlatformId())
                    .eqIfPresent(GatherLogdetailDO::getUuid, reqVO.getUuid())
                    .eqIfPresent(GatherLogdetailDO::getIp, reqVO.getIp())
                    .likeIfPresent(GatherLogdetailDO::getHostName, reqVO.getHostName())
                    .eqIfPresent(GatherLogdetailDO::getAlarmLocation, reqVO.getAlarmLocation())
                    .betweenIfPresent(GatherLogdetailDO::getAlarmDate, reqVO.getAlarmDate())
                    .eqIfPresent(GatherLogdetailDO::getAlarmDetail, reqVO.getAlarmDetail())
                    .betweenIfPresent(GatherLogdetailDO::getCreateTime, reqVO.getCreateTime())
                    .likeIfPresent(GatherLogdetailDO::getPlatformName, reqVO.getPlatformName())
                    .orderByDesc(GatherLogdetailDO::getId);

            if (data.size() > 0) {
                queryWrapperX = (LambdaQueryWrapperX<GatherLogdetailDO>) queryWrapperX.in(GatherLogdetailDO::getPlatformId, data);
            }

            return selectPage(reqVO, queryWrapperX);
        }
    }

    default List<GatherLogdetailDO> selectList(GatherLogdetailExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<GatherLogdetailDO>()
                .eqIfPresent(GatherLogdetailDO::getUuid, reqVO.getUuid())
                .eqIfPresent(GatherLogdetailDO::getIp, reqVO.getIp())
                .likeIfPresent(GatherLogdetailDO::getHostName, reqVO.getHostName())
                .eqIfPresent(GatherLogdetailDO::getAlarmLocation, reqVO.getAlarmLocation())
                .betweenIfPresent(GatherLogdetailDO::getAlarmDate, reqVO.getAlarmDate())
                .eqIfPresent(GatherLogdetailDO::getAlarmDetail, reqVO.getAlarmDetail())
                .betweenIfPresent(GatherLogdetailDO::getCreateTime, reqVO.getCreateTime())
                .likeIfPresent(GatherLogdetailDO::getPlatformName, reqVO.getPlatformName())
                .orderByDesc(GatherLogdetailDO::getId));
    }

    void updateState(@Param("ids") Collection<Long> ids);


    List<GatherLogdetailDO> getGatherLogdetailPage(@Param("mpPage") IPage<GatherLogdetailDO> mpPage, @Param("pageReqVO") GatherLogdetailPageReqVO pageReqVO);

    default List<GatherLogdetailDO> alarmNoticeList(boolean admin, List<Map> platform) {

        if (admin) {
            LambdaQueryWrapperX<GatherLogdetailDO> queryWrapperX = new LambdaQueryWrapperX<GatherLogdetailDO>();
            queryWrapperX.eq(GatherLogdetailDO::getAlarmState, 0);
            queryWrapperX.eq(GatherLogdetailDO::getDeleted, 0);
            queryWrapperX.last("limit 5");
            return selectList(queryWrapperX);
        } else {
            List<String> data = new ArrayList<>();
            if (platform.size() > 0) {
                for (Map map : platform) {
                    data.add(map.get("platformId").toString());
                }
            }
            LambdaQueryWrapperX<GatherLogdetailDO> queryWrapperX = new LambdaQueryWrapperX<GatherLogdetailDO>();
            queryWrapperX.eq(GatherLogdetailDO::getAlarmState, 0);
            queryWrapperX.eq(GatherLogdetailDO::getDeleted, 0);
            queryWrapperX.last("limit 5");
            if (data.size() > 0) {
                queryWrapperX = (LambdaQueryWrapperX<GatherLogdetailDO>) queryWrapperX.in(GatherLogdetailDO::getPlatformId, data);
            }
            return selectList(queryWrapperX);
        }
    }

    List<GatherLogdetailRespVO> getPageInfo(@Param("pageVO") GatherLogdetailPageReqVO pageVO,
                                            @Param("user") LoginUser user,
                                            IPage<GatherLogdetailRespVO> mpPage);

    List<GatherLogdetailRespVO> getLatestLog(@Param("user")LoginUser loginUser);

    void solvedGather(@Param("gatherId") String gatherId);

    void gatherWorkOrder(@Param("gatherId") String gatherId);

    void cleanWorkOrder(@Param("gatherId") String gatherId);

    GatherLogdetailRespVO getGatherLogdetailById(@Param("id")Long id);

    void updateIsSolved(@Param("ids")Collection<Long> ids);
}
