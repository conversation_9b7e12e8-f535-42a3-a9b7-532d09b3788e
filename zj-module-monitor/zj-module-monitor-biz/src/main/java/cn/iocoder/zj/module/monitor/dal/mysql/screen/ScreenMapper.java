package cn.iocoder.zj.module.monitor.dal.mysql.screen;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface ScreenMapper {

    List<Map<String, Object>> getHostCpuTop(@Param("tenantId") Long tenantId);
    List<Map<String, Object>> getHardwareCpuTop(@Param("tenantId") Long tenantId);
    List<Map<String, Object>> getHostMemoryTop(@Param("tenantId") Long tenantId);
    List<Map<String, Object>> getHardwareMemoryTop(@Param("tenantId") Long tenantId);

}
