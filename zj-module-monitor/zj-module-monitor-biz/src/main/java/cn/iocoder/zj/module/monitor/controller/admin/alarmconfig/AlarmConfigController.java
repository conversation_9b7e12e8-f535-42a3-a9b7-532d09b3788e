package cn.iocoder.zj.module.monitor.controller.admin.alarmconfig;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.exception.ErrorCode;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo.*;
import cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo.AlarmHostListPageResp;
import cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo.AlarmHostRelationPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo.AlarmHostRelationReqVO;
import cn.iocoder.zj.module.monitor.convert.alarmconfig.AlarmConfigConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmconfig.AlarmConfigDO;
import cn.iocoder.zj.module.monitor.service.alarmconfig.AlarmConfigService;
import cn.iocoder.zj.module.monitor.util.StringUtil;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

@Tag(name = "管理后台 - 告警配置")
@RestController
@RequestMapping("/monitor/alarm-config")
@Validated
public class AlarmConfigController {

    @Resource
    private AlarmConfigService alarmConfigService;
    @Resource
    AdminUserApi adminUserApi;

    @PostMapping("/create")
    @Operation(summary = "创建告警配置")
    @PreAuthorize("@ss.hasPermission('monitor:alarm-config:create')")
    @OperateLog(type = CREATE)
    public CommonResult<Long> createAlarmConfig(@Valid @RequestBody AlarmConfigCreateReqVO createReqVO) {
        return success(alarmConfigService.createAlarmConfig(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新告警配置")
    @PreAuthorize("@ss.hasPermission('monitor:alarm-config:update')")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateAlarmConfig(@Valid @RequestBody AlarmConfigUpdateReqVO updateReqVO) {
        alarmConfigService.updateAlarmConfig(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除告警配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('monitor:alarm-config:delete')")
    @OperateLog(type = DELETE)
    public CommonResult<Boolean> deleteAlarmConfig(@RequestParam("id") Long id) {
        alarmConfigService.deleteAlarmConfig(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得告警配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AlarmConfigRespVO> getAlarmConfig(@RequestParam("id") Long id) {
        AlarmConfigDO alarmConfig = alarmConfigService.getAlarmConfig(id);
        return success(AlarmConfigConvert.INSTANCE.convert(alarmConfig));
    }

    @GetMapping("/list")
    @Operation(summary = "获得告警配置列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    public CommonResult<List<AlarmConfigRespVO>> getAlarmConfigList(@RequestParam(name = "ids",required = false) Collection<Long> ids,
                                                                    @RequestParam(name = "type",required = false) String type) {
        List<AlarmConfigDO> list = alarmConfigService.getAlarmConfigList(ids,type);
        return success(AlarmConfigConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得告警配置分页")
    @PreAuthorize("@ss.hasPermission('monitor:alarm-config:query')")
    public CommonResult<PageResult<AlarmConfigRespVO>> getAlarmConfigPage(@Valid AlarmConfigPageReqVO pageVO) {
        return success(alarmConfigService.getAlarmConfigPage(pageVO));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出告警配置 Excel")
    @OperateLog(type = EXPORT)
    @PreAuthorize("@ss.hasPermission('monitor:alarm-config:export')")
    public void exportAlarmConfigExcel(@Valid AlarmConfigExcelReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        // 2024-06-24 新加平台列表
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        AdminUserRespDTO adminUserRespDTO =  adminUserApi.getUser(loginUser.getId()).getCheckedData();

        Collection<Long> d = new HashSet<>();
        // 不为空的时候是运维人员，拆除用逗号分割解析
        if (StringUtil.isNotEmpty( adminUserRespDTO.getServiceTenantId())){
            // 获取字符串并解析
            String tenantIdStr = adminUserRespDTO.getServiceTenantId();

            String[] ids = StrUtil.splitToArray(tenantIdStr, ',');
            // 遍历分割结果，并转换成Long类型后添加到Set集合中
            for (String id : ids) {
                Long num = Convert.toLong(id, null);  // 在转换过程中，如果遇到无法转换的情况，则返回null
                if (num != null) {
                    d.add(num);
                }
            }
        }
        List<AlarmConfigDO> list = alarmConfigService.getAlarmConfigList(exportReqVO,d,loginUser);
        // 导出 Excel
        List<AlarmConfigExcelTemVO> datas = AlarmConfigConvert.INSTANCE.convertList03(list);
        for (AlarmConfigExcelTemVO data : datas) {
            String alarmLevel = data.getAlarmLevel();
            if (StringUtil.isNotEmpty(alarmLevel)) {
                switch (alarmLevel) {
                    case "1":
                        data.setAlarmLevel("提示");
                        break;
                    case "2":
                        data.setAlarmLevel("警告");
                        break;
                    default:
                        data.setAlarmLevel("严重");
                        break;
                }
            }
        }
        ExcelUtils.write(response, "告警配置.xls", "数据", AlarmConfigExcelTemVO.class, datas);
    }

    @PostMapping("/import")
    @Operation(summary = "导入告警配置")
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('monitor:alarm-config:import')")
    public CommonResult<AlarmImportRespVO> importExcel(@RequestParam("file") MultipartFile file){
        List<AlarmConfigExcelVO> list=new ArrayList<>();
        try {
            List<AlarmConfigImportVO> results = ExcelUtils.read(file, AlarmConfigImportVO.class);
            list=results.stream().map(item->{
                AlarmConfigExcelVO alarmConfigExcelVO = new AlarmConfigExcelVO();
                BeanUtils.copyProperties(item,alarmConfigExcelVO);
                //1提示，2警告，3严重
                switch (item.getAlarmLevel()) {
                    case "提示":
                        alarmConfigExcelVO.setAlarmLevel(1);
                        break;
                    case "警告":
                        alarmConfigExcelVO.setAlarmLevel(2);
                        break;
                    default:
                        alarmConfigExcelVO.setAlarmLevel(3);
                        break;
                }
                return alarmConfigExcelVO;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            throw exception(new ErrorCode(2008000000,"导入文件格式有误"));
        }
        return success(alarmConfigService.importAlarmConfigList(list));
    }
    @GetMapping("/alarmInfo")
    @Operation(summary = "获得告警信息分页")
    @TenantIgnore
    public CommonResult<?> getAlarmInfoPage(@Valid AlarmInfoReqVo pageVO) {
        if (pageVO.isEnablePagination()){
            PageResult<AlarmInfoRespVo> pageReault = alarmConfigService.getAlarmInfoPage(pageVO);
            return success(pageReault);
        }else {
            List<AlarmInfoRespVo> respVos = alarmConfigService.getAlarmInfoList(pageVO);
            return success(respVos);
        }
    }

    @GetMapping("/getAlarmInfo")
    @Operation(summary = "获得告警信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @TenantIgnore
    public CommonResult<AlarmInfoRespVo> getAlarmInfo(@RequestParam("id") Long id) {
        AlarmInfoRespVo alarmInfo = alarmConfigService.getAlarmInfo(id);
        return success(alarmInfo);
    }

    @GetMapping("/getAlarmHostInfoDetail")
    @Operation(summary = "获得主机告警信息分页")
    public CommonResult<PageResult<AlarmInfoRespVo>> getAlarmHostInfoDetail(@Valid AlarmHostRelationPageReqVO reqVO) {
        PageResult<AlarmInfoRespVo> pageResult = alarmConfigService.getAlarmHostInfoDetail(reqVO);
        return success(pageResult);
    }
    @GetMapping("/getAlarmAddedList")
    @Operation(summary = "获得已添加告警设备分页列表")
    @TenantIgnore
    public CommonResult<List<AlarmHostListPageResp> > getAlarmAddedList(@Valid AlarmHostRelationReqVO reqVO) {
        List<AlarmHostListPageResp>  pageResult = alarmConfigService.getAlarmAddedList(reqVO);
        return success(pageResult);
    }
    @GetMapping("/getPreparingAddAlarmList")
    @Operation(summary = "获得待添加告警设备分页列表")
    @TenantIgnore
    public CommonResult<List<AlarmHostListPageResp> > getPreparingAddAlarmList(@Valid AlarmHostRelationReqVO reqVO) {
        List<AlarmHostListPageResp>  pageResult = alarmConfigService.getPreparingAddAlarmList(reqVO);
        return success(pageResult);
    }
    @GetMapping("/getAlarmConfigByUuid")
    @Operation(summary = "获取设备已配置的告警")
    @TenantIgnore
    public CommonResult<List<AlarmConfigRespVO>> getAlarmConfigByUuid(@Valid String uuid) {
        List<AlarmConfigRespVO> pageResult = alarmConfigService.getAlarmConfigByUuid(uuid);
        return success(pageResult);
    }
    @GetMapping("/alarmBaseInfoDetail")
    @Operation(summary = "告警信息详情页告警配置基本信息")
    public CommonResult<AlarmInfoRespVo> alarmBaseInfoDetail(@RequestParam("alarmId")Long alarmId) {
        AlarmInfoRespVo result = alarmConfigService.getAlarmBaseInfoDetail(alarmId);
        return success(result);
    }
    @PostMapping("/alarmNotice")
    @Operation(summary = "创建告警通知")
    @PreAuthorize("@ss.hasPermission('monitor:alarm-config:create')")
    @OperateLog(type = CREATE)
    public CommonResult<Long> alarmNotice(@Valid @RequestBody AlarmConfigCreateReqVO createReqVO) {
        return success(alarmConfigService.createAlarmConfig(createReqVO));
    }

    @GetMapping("/alarmInfoCountInWeek")
    @Operation(summary = "近7天告警信息统计")
    @Parameter(name = "platformId", description = "平台id", required = false, example = "1")
    @TenantIgnore
    public CommonResult<Map<String,Object>> getAlarmInfoCountInWeek(@RequestParam(value = "platformId",required = false)Long platformId) {
        Map<String,Object> result = alarmConfigService.getAlarmInfoCountInWeek(platformId);
        return success(result);
    }
    @GetMapping("/getUnsolvedAlarms")
    @Operation(summary = "获得最近五条未处理的告警（支持查询所有告警数据）")
    @Parameter(name = "limit", description = "告警条数", required = false, example = "5")
    @TenantIgnore
    public CommonResult<List<AlarmInfoRespVo>> getUnsolvedAlarms(@RequestParam(required = false,defaultValue = "5") Long limit) {
        return success(alarmConfigService.getUnsolvedAlarms(limit));
    }


    @PutMapping("/updateIsRead")
    @Operation(summary = "修改是否已读")
    @PreAuthenticated
    @TenantIgnore
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateIsRead(@RequestParam("ids") Collection<Long> ids) {
        alarmConfigService.updateIsRead(ids);
        return success(true);
    }
    @PutMapping("/updateEnabled")
    @Operation(summary = "是否启用告警")
    @PreAuthenticated
    @TenantIgnore
    @PreAuthorize("@ss.hasPermission('monitor:alarm-config:update')")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateEnabled(@RequestParam("id") Long id,@RequestParam("enabled") Integer enabled){
        alarmConfigService.updateEnabled(id,enabled);
        return success(true);
    }

    @PutMapping("/updateIsSolved")
    @Operation(summary = "修改是否解决")
    @PreAuthenticated
    @TenantIgnore
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateIsSolved(@RequestParam("ids") Collection<Long> ids) {
        alarmConfigService.updateIsSolved(ids);
        return success(true);
    }
    @GetMapping("/alarmHostPage")
    @Operation(summary = "获得已添加告警主机分页列表")
    @TenantIgnore
    public CommonResult<PageResult<AlarmHostListPageResp>> getAlarmHostPage(@Valid AlarmHostRelationPageReqVO reqVO) {
        PageResult<AlarmHostListPageResp> pageResult = alarmConfigService.getAlarmHostPage(reqVO);
        return success(pageResult);
    }

    @GetMapping("/alarmSummary")
    @Operation(summary = "告警状态统计")
    @TenantIgnore
    public CommonResult<Map<String,Object>> getAlarmSummary(@RequestParam(value = "platformId",required = false)Long platformId,
                                                            @RequestParam(value = "priority",required = false)Integer priority) {
        return success(alarmConfigService.getAlarmSummary(platformId,priority));
    }
}
