package cn.iocoder.zj.module.monitor.service.mediatype;

import java.util.*;
import javax.validation.*;
import cn.iocoder.zj.module.monitor.controller.admin.mediatype.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.mediatype.MediaTypeDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 介质类型 Service 接口
 *
 * <AUTHOR>
 */
public interface MediaTypeService {

    /**
     * 创建介质类型
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMediaType(@Valid MediaTypeCreateReqVO createReqVO);

    /**
     * 更新介质类型
     *
     * @param updateReqVO 更新信息
     */
    void updateMediaType(@Valid MediaTypeUpdateReqVO updateReqVO);

    /**
     * 删除介质类型
     *
     * @param id 编号
     */
    void deleteMediaType(Long id);

    /**
     * 获得介质类型
     *
     * @param id 编号
     * @return 介质类型
     */
    MediaTypeDO getMediaType(Long id);

    /**
     * 获得介质类型列表
     *
     * @return 介质类型列表
     */
    List<MediaTypeDO> getMediaTypeList();

    /**
     * 获得介质类型分页
     *
     * @param pageReqVO 分页查询
     * @return 介质类型分页
     */
    PageResult<MediaTypeDO> getMediaTypePage(MediaTypePageReqVO pageReqVO);

    /**
     * 获得介质类型列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 介质类型列表
     */
    List<MediaTypeDO> getMediaTypeList(MediaTypeExportReqVO exportReqVO);

    boolean bindStorage(Long mediaId,  Long storageId);
}
