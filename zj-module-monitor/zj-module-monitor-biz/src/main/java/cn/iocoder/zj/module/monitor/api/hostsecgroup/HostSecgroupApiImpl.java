package cn.iocoder.zj.module.monitor.api.hostsecgroup;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.hostsecgroup.dto.HostSecgroupCreateReqDto;
import cn.iocoder.zj.module.monitor.api.secgroup.dto.SecgroupCreateReqDto;
import cn.iocoder.zj.module.monitor.controller.admin.hostsecgroup.vo.HostSecgroupExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.secgroup.vo.SecgroupExportReqVO;
import cn.iocoder.zj.module.monitor.convert.hostsecgroup.HostSecgroupConvert;
import cn.iocoder.zj.module.monitor.convert.secgroup.SecgroupConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostsecgroup.HostSecgroupDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.secgroup.SecgroupDO;
import cn.iocoder.zj.module.monitor.service.hostsecgroup.HostSecgroupService;
import cn.iocoder.zj.module.monitor.service.secgroup.SecgroupService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class HostSecgroupApiImpl implements HostSecgroupApi{
    @Resource
    private HostSecgroupService hostSecgroupService;
    @Override
    public CommonResult<Boolean> addHostSecgroups(List<HostSecgroupCreateReqDto> reqDTO) {
        List<HostSecgroupDO> list = HostSecgroupConvert.INSTANCE.convertCreateList(reqDTO);
        hostSecgroupService.createHostSecgroupList(list);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> updateHostSecgroups(List<HostSecgroupCreateReqDto> hostSecgroupCreateReqDtos) {
        if (!hostSecgroupCreateReqDtos.isEmpty()) {
            List<HostSecgroupDO> list = HostSecgroupConvert.INSTANCE.convertCreateList(hostSecgroupCreateReqDtos);
            hostSecgroupService.updateHostSecgroups(list);
        }
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> delHostSecgroups(List<HostSecgroupCreateReqDto> hostSecgroupCreateReqDtos) {
        if (!hostSecgroupCreateReqDtos.isEmpty()) {
            List<HostSecgroupDO> list = HostSecgroupConvert.INSTANCE.convertCreateList(hostSecgroupCreateReqDtos);
            hostSecgroupService.deleteHostSecgroups(list);
        }
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<List<HostSecgroupCreateReqDto>> getHostSecgroupsByPlatformId(Long platformId) {
        HostSecgroupExportReqVO reqVo = new HostSecgroupExportReqVO();
        reqVo.setPlatformId(platformId);
        List<HostSecgroupCreateReqDto> list = HostSecgroupConvert.INSTANCE.convertDoToCreateDtoList(hostSecgroupService.getHostSecgroupList(reqVo));
        return CommonResult.success(list);
    }
}
