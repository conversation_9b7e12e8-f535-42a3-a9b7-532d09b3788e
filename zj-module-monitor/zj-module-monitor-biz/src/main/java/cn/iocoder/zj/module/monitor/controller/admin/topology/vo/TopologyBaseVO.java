package cn.iocoder.zj.module.monitor.controller.admin.topology.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

/**
* 监控资源拓扑图 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class TopologyBaseVO {

    @Schema(description = "拓扑图json")
    private String topologyJson;

    @Schema(description = "租户Id")
    private String tenantId;
    @Schema(description = "租户名称")
    private String tenantName;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "拓扑图名称")
    private String topologyName;
    @Schema(description = "拓扑图图标")
    private String topologyImg;
    @Schema(description = "资源json")
    private String resourceJson;

    @Schema(description = "接口json")
    private String interfacesJson;
}
