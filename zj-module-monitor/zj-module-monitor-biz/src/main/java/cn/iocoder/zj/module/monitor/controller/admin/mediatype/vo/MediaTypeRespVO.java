package cn.iocoder.zj.module.monitor.controller.admin.mediatype.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 介质类型 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MediaTypeRespVO extends MediaTypeBaseVO {

    @Schema(description = "id", required = true)
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
