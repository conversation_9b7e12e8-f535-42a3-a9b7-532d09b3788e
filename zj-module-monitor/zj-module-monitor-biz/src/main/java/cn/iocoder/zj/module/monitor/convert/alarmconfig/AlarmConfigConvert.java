package cn.iocoder.zj.module.monitor.convert.alarmconfig;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmConfigRespDTO;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmRecordDTO;
import cn.iocoder.zj.module.monitor.dal.mysql.alarmdoriseinfo.AlarmDorisDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.monitor.controller.admin.alarmconfig.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmconfig.AlarmConfigDO;

/**
 * 告警配置 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface AlarmConfigConvert {

    AlarmConfigConvert INSTANCE = Mappers.getMapper(AlarmConfigConvert.class);

    AlarmConfigDO convert(AlarmConfigCreateReqVO bean);

    AlarmConfigDO convert(AlarmConfigUpdateReqVO bean);

    AlarmConfigRespVO convert(AlarmConfigDO bean);

    List<AlarmConfigRespVO> convertList(List<AlarmConfigDO> list);

    PageResult<AlarmConfigRespVO> convertPage(PageResult<AlarmConfigDO> page);

    List<AlarmConfigExcelVO> convertList02(List<AlarmConfigDO> list);

    List<AlarmConfigExcelTemVO> convertList03(List<AlarmConfigDO> list);

    Object convertAlarmInfoPage(PageResult<AlarmInfoRespVo> pageResult);

    List<AlarmConfigRespDTO> convertToDto(List<AlarmConfigDO> listByTenantId);

    List<AlarmConfigDO> DTOConvertToDo(List<AlarmConfigRespDTO> toInsertAlarmConfig);

    List<AlarmRecordDTO> recodeToDTO(List<AlarmInfoRespVo> alarmRecordVOS);

    List<AlarmInfoRespVo> toAlarmRespVos(List<AlarmRecordDTO> alarmRecordDTOS);
    List<AlarmDorisDO> dorisReqDTOConvertToDorisDO(List<AlarmDorisReqDTO> alarmDorisReqDTOList);

}
