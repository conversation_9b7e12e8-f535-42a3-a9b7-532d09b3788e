package cn.iocoder.zj.module.monitor.service.home;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.enums.ResourceCategoryEnum;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.bpm.api.task.BpmTaskApi;
import cn.iocoder.zj.module.customer.api.custinfo.CustInfoApi;
import cn.iocoder.zj.module.monitor.controller.admin.home.vo.PersonSLAResponseExcelVO;
import cn.iocoder.zj.module.monitor.dal.mysql.hardwareinfo.HardwareInfoMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.hostinfo.HostInfoMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.storageinfo.StorageInfoMapper;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import com.alibaba.fastjson.JSONArray;
import org.flowable.engine.HistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : AdminScreenServiceImpl  //类名
 * @Description : 管理视角大屏  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/3/25  10:25
 */
@Service
public class AdminScreenServiceImpl implements AdminScreenService {

    @Autowired
    CustInfoApi custInfoApi;
    @Autowired
    PlatformconfigApi platformconfigApi;
    @Resource
    HardwareInfoMapper hardwareInfoMapper;
    @Resource
    private HostInfoMapper hostInfoMapper;
    @Resource
    private StorageInfoMapper storageInfoMapper;
    @Resource
    private HistoryService historyService;
    @Resource
    AdminUserApi adminUserApi;
    @Resource
    RoleApi roleApi;
    @Resource
    PermissionApi permissionApi;
    @Resource
    BpmTaskApi bpmTaskApi;


    @Override
    public List<Map> getCustomers() {
        return custInfoApi.getCustomersList().getData();
    }

    @Override
    public List<Map> cloudPlatform() {
        return platformconfigApi.cloudPlatform();
    }

    @Override
    @TenantIgnore
    public Map userAssetCount() {
        int usercount = hardwareInfoMapper.selectUserCount();
        int cloudcount = hardwareInfoMapper.selectCloudCount();
        int assetCount = hardwareInfoMapper.selectAssetCount();

        Map map = new HashMap();
        map.put("user", usercount);
        map.put("cloud", cloudcount);
        map.put("asset", assetCount);

        return map;
    }

    @Override
    @TenantIgnore
    public List<Map<String, Object>> assetList() {
        LoginUser currentUser = SecurityFrameworkUtils.getLoginUser();
        List<Map> platformConfigList = platformconfigApi.getPlatformSelectList(String.valueOf(currentUser.getTenantId())).getData();

        Long hardCount = hardwareInfoMapper.selectCountByPlatfrom(platformConfigList);
        Long cloudCount = hostInfoMapper.selectCountByPlatfrom(platformConfigList);
        Long storageCount = storageInfoMapper.selectCountByPlatfrom(platformConfigList);
        List<Map<String, Object>> list = hardwareInfoMapper.getHzAppListCount(platformConfigList);
        for (Map<String, Object> item : list) {
            String app = (String) item.get("app");
            String label = getResourceLabel(app);
            item.put("app", label);
            item.put("code", app);
        }
        Map<String, Object> hamap = new HashMap();
        // 动态创建并添加Map
        addToMapList(list, ResourceCategoryEnum.APP_HARDWARE.getName(), ResourceCategoryEnum.APP_HARDWARE.getCode(), hardCount);
        addToMapList(list, ResourceCategoryEnum.APP_HOST.getName(), ResourceCategoryEnum.APP_HOST.getCode(), cloudCount);
        addToMapList(list, ResourceCategoryEnum.APP_STORAGE.getName(), ResourceCategoryEnum.APP_STORAGE.getCode(), storageCount);

        // 定义一个映射，将应用类型映射到其相应的列表
        Map<String, List<String>> appTypeMap = new HashMap<>();
        appTypeMap.put(ResourceCategoryEnum.APP_SERVICE.getCode(), Arrays.asList("website", "port", "ssl_cert", "ping", "websocket", "api", "dns", "fullsite", "api_code", "udp_port"));
        appTypeMap.put(ResourceCategoryEnum.APP_DB.getCode(), Arrays.asList("mysql", "oracle", "sqlserver", "mariadb", "dm", "redis", "redis_cluster", "redis_sentinel", "memcached", "tidb", "nebulaGraph", "opengauss", "mongodb"));
        appTypeMap.put(ResourceCategoryEnum.APP_MID.getCode(), Arrays.asList("nacos", "zookeeper", "kafka", "rabbitmq", "nginx", "pop3", "ftp", "smtp", "ntp", "spring_gateway", "emqx", "activemq", "rocketmq", "shenyu", "rabbitmq", "almalinux"));
        appTypeMap.put(ResourceCategoryEnum.APP_OS.getCode(), Arrays.asList("windows", "linux", "ubuntu", "coreos", "opensuse", "rockylinux", "euleros", "redhat", "centos", "freebsd", "debian"));
        appTypeMap.put(ResourceCategoryEnum.APP_CN.getCode(), Arrays.asList("docker", "kubernetes"));
//        appTypeMap.put(ResourceCategoryEnum.APP_PROMETHEUS.getCode(), Collections.singletonList("prometheus"));
        appTypeMap.put(ResourceCategoryEnum.APP_FIREWALL.getCode(), Arrays.asList("qax_firewall", "sswk_firewall", "sswk_defense", "sxf_firewall","ad_firewall" ,"ac_firewall","ah_firewall", "ah_waf", "lm_defense", "lm_waf", "trx_defense", "trx_firewall"));
        appTypeMap.put(ResourceCategoryEnum.APP_NETWORK.getCode(), Arrays.asList("h3c_switch", "cisco_switch", "huawei_switch", "tplink_switch", "hpe_switch"));
        appTypeMap.put(ResourceCategoryEnum.APP_STORED.getCode(), Arrays.asList("storage_huawei", "storage_inspur", "storage_synology"));
        appTypeMap.put(ResourceCategoryEnum.APP_INFRA.getCode(), Arrays.asList("infra_ipmi"));
        appTypeMap.put(ResourceCategoryEnum.APP_HOST.getCode(), Arrays.asList("host"));
        appTypeMap.put(ResourceCategoryEnum.APP_HARDWARE.getCode(), Arrays.asList("hardware"));
        appTypeMap.put(ResourceCategoryEnum.APP_STORAGE.getCode(), Arrays.asList("storage"));


        List<Map<String, Object>> datalist = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : appTypeMap.entrySet()) {
            Map<String, Object> maps = new HashMap();
            String appType = entry.getKey();
            String label = getResourceLabel(entry.getKey());
            List<String> apps = entry.getValue();
            // 遍历当前类型的每个应用
            Long count = 0L;
            for (String app : apps) {
                for (Map map : list) {
                    if (app.equals(map.get("code"))) {
                        count = count + Convert.toLong(map.get("count"));
                    }
                }
            }
            maps.put("code",appType);
            maps.put("app",label);
            maps.put("count",count);
            datalist.add(maps);
        }

        // 对列表按照 count 字段进行倒序排序
        List<Map<String, Object>> resultList = datalist.stream()
                .sorted(Comparator.comparingLong(e -> -(Long) e.get("count")))
                .collect(Collectors.toList());

        // 计算总的 count 值
        long firstCount = (long) resultList.get(0).get("count");

        // 计算百分比并更新到列表中
        for (Map<String, Object> item : resultList) {

            long count = (long) item.get("count");
            double percentage;
            if (count == firstCount) {
                percentage = 100.0; // 排行第一个的值百分比为100
            } else {
                percentage = (count / (double) firstCount) * 100; // 其他值的百分比相对于第一个值
            }
            // 四舍五入保留两位小数
            percentage = Math.round(percentage * 100.0) / 100.0;
            item.put("percentage", percentage);
        }
        return resultList;
    }

    @Override
    public List<Map> customerIndustry() {
        return custInfoApi.customerIndustry().getData();
    }

    @Override
    public List<Map> getPersonSLAResponse(String userName,String startTime,String endTime) {
        DecimalFormat df = new DecimalFormat("#.##");
        LoginUser currentUser = SecurityFrameworkUtils.getLoginUser();
        List<Map> taskCount =  bpmTaskApi.getTasksCount(userName,currentUser.getTenantId(),startTime,endTime).getData();
        if(ObjectUtil.isNotEmpty(taskCount)) {
            for (Map item : taskCount) {
                Double timeoutRate = 0.0;
                if(ObjectUtil.isNotEmpty(item.get("overtime"))&&ObjectUtil.isNotEmpty(item.get("total"))) {
                    if( (Integer) item.get("total") >0) {
                        timeoutRate = (double) (Integer) item.get("overtime") / (Integer) item.get("total") * 100;
                    }
                }
                item.put("timeoutRate", df.format(timeoutRate));
            }
        }
        return taskCount;
    }

    @Override
    public Map getSLAResponse() {
        DecimalFormat df = new DecimalFormat("#.##");
        LoginUser currentUser = SecurityFrameworkUtils.getLoginUser();
        List<Long> homologousUsers = adminUserApi.getHomologousUsers(currentUser.getId()).getData();
        Map result = bpmTaskApi.getSLAResponse(homologousUsers).getData();
        Double timeoutRate = 0.0;
        if(ObjectUtil.isNotEmpty(result)){
            if(ObjectUtil.isNotEmpty(result.get("overtimeCount"))&&ObjectUtil.isNotEmpty(result.get("total"))) {
                if((Integer) result.get("total") >0) {
                    timeoutRate = (double) (Integer) result.get("overtimeCount") / (Integer) result.get("total") * 100;
                }
            }
        }
        result.put("timeoutRate",df.format(timeoutRate));
        return result;
    }

    @Override
    public List<PersonSLAResponseExcelVO> exportPersonSLAResponse(String userName,String startTime,String endTime) {
        List<Map> personSLAResponseMapList = getPersonSLAResponse(userName, startTime,endTime);
        JSONArray jsonArray = new JSONArray();
        jsonArray.addAll(personSLAResponseMapList);
        List<PersonSLAResponseExcelVO> datas = jsonArray.toJavaList(PersonSLAResponseExcelVO.class);
        datas.forEach(item->item.setTimeoutRate(item.getTimeoutRate()+"%"));
        return datas;
    }

    private void addToMapList(List<Map<String, Object>> list, String app, String code, Long count) {
        Map<String, Object> map = new HashMap<>();
        map.put("app", app);
        map.put("count", count);
        map.put("code", code);
        list.add(map);
    }

    private String getResourceLabel(String app) {
        for (ResourceCategoryEnum resource : ResourceCategoryEnum.values()) {
            if (resource.getCode().equals(app)) {
                return resource.getName();
            }
            // 检查是否包含在当前大类的枚举列表中
            if (resource.getSubEnums() != null && resource.getSubEnums().stream().anyMatch(subEnum -> subEnum.getValue().equals(app))) {
                return resource.getName();
            }
        }
        return null; // 如果没有匹配的枚举值，返回null或者其他默认值
    }
}
