package cn.iocoder.zj.module.monitor.service.home;

import cn.iocoder.zj.module.customer.api.custinfo.dto.CustinfoDTO;
import cn.iocoder.zj.module.customer.api.custinfo.dto.IndustryDTO;
import cn.iocoder.zj.module.monitor.controller.admin.home.vo.PersonSLAResponseExcelVO;

import java.util.List;
import java.util.Map;

public interface AdminScreenService {
    List<Map> getCustomers();

    List<Map> cloudPlatform();

    Map userAssetCount();

    List<Map<String, Object>> assetList();

    List<Map> customerIndustry();

    List<Map> getPersonSLAResponse(String userName,String startTime,String endTime);

    Map getSLAResponse();

    List<PersonSLAResponseExcelVO> exportPersonSLAResponse(String userName,String startTime,String endTime);
}
