package cn.iocoder.zj.module.monitor.controller.admin.hostinfo.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 云主机基本信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HostInfoPageReqVO extends PageParam {

    @Schema(description = "主机id")
    private String uuid;

    @Schema(description = "主机名称")
    private String name;

    @Schema(description = "主机状态")
    private String state;

    @Schema(description = "ip")
    private String ip;

    @Schema(description = "弹性ip")
    private String vipIp;

    @Schema(description = "集群id")
    private String clusterUuid;

    @Schema(description = "CPU架构")
    private String architecture;

    @Schema(description = "操作系统类型")
    private String guestOsType;

    @Schema(description = "主机创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] vCreateDate;

    @Schema(description = "主机类型")
    private String type;

    @Schema(description = "分配内存")
    private Long memorySize;

    @Schema(description = "分配cpu")
    private Integer cpuNum;

    @Schema(description = "mac 地址")
    private String mac;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "查询条件，名称或IP")
    private String queryData;

    @Schema(description = "集群名称")
    private String clusterName;

    @Schema(description = "物理id")
    private String hardWareUuid;


    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "租户ID")
    private List<String> tenantId;

    @Schema(required = false,description = "主机过滤ids逗号拼接,'1','2','3'")
    private String ids;

    @Schema(required = false,description = "主机tagId逗号拼接,1,2,3")
    private String tagIds;

    @Schema(required = false,description = "开始时间",example = "2023-06-07")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String startTime;

    @Schema(required = false,description = "结束时间",example = "2023-06-07")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String endTime;

    @Schema(required = false,description = "主键逗号拼接,'1','2','3'")
    private List<Long> inPks;

}
