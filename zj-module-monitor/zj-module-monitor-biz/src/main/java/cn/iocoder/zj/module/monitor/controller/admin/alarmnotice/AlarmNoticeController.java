package cn.iocoder.zj.module.monitor.controller.admin.alarmnotice;

import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.monitor.controller.admin.alarmnotice.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.alarmnotice.AlarmNoticeDO;
import cn.iocoder.zj.module.monitor.convert.alarmnotice.AlarmNoticeConvert;
import cn.iocoder.zj.module.monitor.service.alarmnotice.AlarmNoticeService;

@Tag(name = "管理后台 - 告警与通知模板关系")
@RestController
@RequestMapping("/monitor/alarm-notice")
@Validated
public class AlarmNoticeController {

    @Resource
    private AlarmNoticeService alarmNoticeService;

    @PostMapping("/create")
    @Operation(summary = "创建告警与通知模板关系")
    @TenantIgnore
    @PreAuthorize("@ss.hasPermission('monitor:alarm-notice:create')")
    @OperateLog(type = CREATE)
    public CommonResult<Long> createAlarmNotice(@Valid @RequestBody AlarmNoticeCreateReqVO createReqVO) {
        return success(alarmNoticeService.createAlarmNotice(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新告警与通知模板关系")
    @TenantIgnore
    @PreAuthorize("@ss.hasPermission('monitor:alarm-notice:update')")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateAlarmNotice(@Valid @RequestBody AlarmNoticeUpdateReqVO updateReqVO) {
        alarmNoticeService.updateAlarmNotice(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除告警与通知模板关系")
    @Parameter(name = "id", description = "编号", required = true)
    @TenantIgnore
    @PreAuthorize("@ss.hasPermission('monitor:alarm-notice:delete')")
    @OperateLog(type = DELETE)
    public CommonResult<Boolean> deleteAlarmNotice(@RequestParam("id") Long id) {
        alarmNoticeService.deleteAlarmNotice(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得告警与通知模板关系")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('monitor:alarm-notice:query')")
    public CommonResult<AlarmNoticeRespVO> getAlarmNotice(@RequestParam("id") Long id) {
        AlarmNoticeDO alarmNotice = alarmNoticeService.getAlarmNotice(id);
        return success(AlarmNoticeConvert.INSTANCE.convert(alarmNotice));
    }

    @GetMapping("/list")
    @Operation(summary = "获得告警与通知模板关系列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('monitor:alarm-notice:query')")
    public CommonResult<List<AlarmNoticeRespVO>> getAlarmNoticeList(@RequestParam("ids") Collection<Long> ids) {
        List<AlarmNoticeDO> list = alarmNoticeService.getAlarmNoticeList(ids);
        return success(AlarmNoticeConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得告警与通知模板关系分页")
    @PreAuthorize("@ss.hasPermission('monitor:alarm-notice:query')")
    public CommonResult<PageResult<AlarmNoticeRespVO>> getAlarmNoticePage(@Valid AlarmNoticePageReqVO pageVO) {
        PageResult<AlarmNoticeDO> pageResult = alarmNoticeService.getAlarmNoticePage(pageVO);
        return success(AlarmNoticeConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出告警与通知模板关系 Excel")
    @PreAuthorize("@ss.hasPermission('monitor:alarm-notice:export')")
    @OperateLog(type = EXPORT)
    public void exportAlarmNoticeExcel(@Valid AlarmNoticeExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<AlarmNoticeDO> list = alarmNoticeService.getAlarmNoticeList(exportReqVO);
        // 导出 Excel
        List<AlarmNoticeExcelVO> datas = AlarmNoticeConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "告警与通知模板关系.xls", "数据", AlarmNoticeExcelVO.class, datas);
    }

}
