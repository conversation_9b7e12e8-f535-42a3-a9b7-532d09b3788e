package cn.iocoder.zj.module.monitor.dal.dataobject.volumesnapshot;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 云盘快照信息 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_volume_snapshot")
@KeySequence("monitor_volume_snapshot_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VolumeSnapshotDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 云盘快照uuid
     */
    private String uuid;
    /**
     * 平台ID
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;
    /**
     * 云盘快照名称
     */
    private String name;
    /**
     * 云盘快照描述
     */
    private String description;
    /**
     * 云主机uuid
     */
    private String hostUuid;
    /**
     * 云主机名称
     */
    private String hostName;
    /**
     * 云盘uuid
     */
    private String volumeUuid;
    /**
     * 云盘名称
     */
    private String volumeName;
    /**
     * 主存储uuid
     */
    private String primaryStorageUuid;
    /**
     * 主存储名称
     */
    private String primaryStorageName;
    /**
     * 云盘快照类型
     */
    private String type;
    /**
     * 云盘类型
     */
    private String volumeType;
    /**
     * 是否最新
     */
    private String latest;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    private String typeName;

    /**
     * 云盘快照大小
     */
    private Long size;

    /**
     * 云盘快照状态
     */
    private String status;

    /**
     * 云盘快照创建时间
     */
    private Date vCreateDate;
    /**
     * 云盘快照更新时间
     */
    private Date vUpdateDate;
    /**
     * 云盘快照安装路径
     */
    private String installPath;
    /**
     * 云盘快照格式
     */
    private String format;

    /**
     * 包含内存快照
     */
    private Boolean isMemory;
}
