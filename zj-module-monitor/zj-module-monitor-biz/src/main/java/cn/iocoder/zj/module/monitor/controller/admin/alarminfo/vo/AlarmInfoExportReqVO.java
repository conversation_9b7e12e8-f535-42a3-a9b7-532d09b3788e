package cn.iocoder.zj.module.monitor.controller.admin.alarminfo.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 监控告警详情 Excel 导出 Request VO，参数和 AlarmInfoPageReqVO 是一致的")
@Data
public class AlarmInfoExportReqVO {

    @Schema(description = "处理人")
    private String processors;

    @Schema(description = "处理时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] processingTime;

    @Schema(description = "处理详情")
    private String processingDetails;

    @Schema(description = "文件地址")
    private String fileAddress;

    @Schema(description = "处理状态")
    private Integer processingType;

    @Schema(description = "监控告警id")
    private String alarmId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

}
