package cn.iocoder.zj.module.monitor.dal.mysql.topology;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.dal.dataobject.topology.TopologyDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.monitor.controller.admin.topology.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 监控资源拓扑图 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TopologyMapper extends BaseMapperX<TopologyDO> {

    default PageResult<TopologyDO> selectPage(TopologyPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TopologyDO>()
                .eqIfPresent(TopologyDO::getTopologyJson, reqVO.getTopologyJson())
                .likeIfPresent(TopologyDO::getTenantName, reqVO.getTenantName())
                .eqIfPresent(TopologyDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(TopologyDO::getPlatformName, reqVO.getPlatformName())
                .betweenIfPresent(TopologyDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TopologyDO::getId));
    }

    default List<TopologyDO> selectList(TopologyExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TopologyDO>()
                .eqIfPresent(TopologyDO::getTopologyJson, reqVO.getTopologyJson())
                .likeIfPresent(TopologyDO::getTenantName, reqVO.getTenantName())
                .eqIfPresent(TopologyDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(TopologyDO::getPlatformName, reqVO.getPlatformName())
                .betweenIfPresent(TopologyDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TopologyDO::getId));
    }

    int updateByPlatform(@Param("updateObj") TopologyDO updateObj);

    default List<TopologyDO> selectListByPlatformId(List<Map> platformId) {
        List<String> data = new ArrayList<>();
        if (platformId.size() > 0) {
            for (Map map : platformId) {
                data.add(map.get("platformId").toString());
            }
        }
        LambdaQueryWrapperX<TopologyDO> queryWrapperX = new LambdaQueryWrapperX<TopologyDO>();
        if (data.size() > 0) {
            queryWrapperX = (LambdaQueryWrapperX<TopologyDO>) queryWrapperX.in(TopologyDO::getPlatformId, data);
        }else {
            return new ArrayList<>();
        }
        return selectList(queryWrapperX.orderByDesc(TopologyDO::getId));
    }
    @TenantIgnore
    Map<String, Map> selectHostState(@Param("hostUuids") List<String> hostUuids);
    @TenantIgnore
    Map<String, Map> selectHzState(@Param("hzUuids") List<String> hzUuids);
    @TenantIgnore
    @DS("doris")
    Map selectAlertBystate(@Param("uuid") String uudi);

    @TenantIgnore
    List<Map<String, Map>> selectHostStates(@Param("hostUuids") List<String> hostUuids);

    @TenantIgnore
    List<Map<String, Map>> selectHzStates(@Param("hzUuids") List<String> hzUuids);

    @DS("doris")
    @TenantIgnore
    List<Map<String, Map>> selectAlertStates(@Param("allUuids") List<String> allUuids);


    @TenantIgnore
    List<Map> selectHzStatesByList(@Param("hzUuids") List<String> hzUuids, @Param("platformId") Long platformId);


    @TenantIgnore
    List<Map> selectHostStatesByList(@Param("hostUuids") List<String> hostUuids, @Param("platformId") Long platformId);

    @DS("doris")
    @TenantIgnore
    List<Map> selectAlertStatesByList(@Param("allUuids") List<String> allUuids, @Param("platformId") Long platformId);
}
