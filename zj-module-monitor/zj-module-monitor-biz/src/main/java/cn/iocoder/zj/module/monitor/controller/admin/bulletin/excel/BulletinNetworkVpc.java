package cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel;

import cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.convert.BulletinStateConvert;
import cn.iocoder.zj.module.monitor.controller.admin.bulletin.excel.convert.BulletinVpcStateConvert;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
@ExcelIgnoreUnannotated
public class BulletinNetworkVpc {

    private Long id;
    @ExcelProperty("路由器名称")
    private String name;

    @ExcelProperty("平台名称")
    private String platformName;

    @ExcelProperty(value = "启用状态", converter = BulletinVpcStateConvert.class)
    private String state;

    @ExcelProperty(value = "就绪状态", converter = BulletinStateConvert.class)
    private String status;

    private String uuid;

    @ExcelProperty("cpu")
    private Integer cpuNum;

    @ExcelProperty("内存")
    private Long memorySize;

    @ExcelProperty("CPU架构")
    private String architecture;

    @ExcelProperty("ip")
    private String ip;

    @ExcelProperty("管理网络ip")
    private String managementNetworkIp;

    @ExcelProperty("虚拟化技术")
    private String hypervisorType;

    @ExcelProperty("三层网络名称")
    private String l3NetworkName;

    @ExcelProperty("宿主机名称")
    private String hostName;

    @ExcelProperty("集群名称")
    private String clusterName;

    @ExcelProperty("路由dns")
    private String dns;

    @ExcelProperty("mac地址")
    private String mac;

    //    @ExcelProperty("宿主机uuid")
    private String hostUuid;
}
