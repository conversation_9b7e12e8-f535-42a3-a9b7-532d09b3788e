package cn.iocoder.zj.module.monitor.dal.dataobject.imageinfo;

import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 镜像信息 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_image_info")
@KeySequence("monitor_image_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImageInfoDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 镜像id
     */
    private String uuid;
    /**
     * 镜像名称
     */
    private String name;
    /**
     * 状态
     */
    private String status;
    /**
     * 镜像格式
     */
    private String format;
    /**
     * CPU架构
     */
    private String cpuArch;
    /**
     * 操作系统
     */
    private String osType;
    /**
     * 镜像大小(字节)
     */
    private Long size;
    /**
     * 镜像类型
     */
    private String imageType;
    /**
     * 共享范围
     */
    private String sharingScope;
    /**
     * 标签
     */
    private String tag;
    /**
     * 云主机uuid
     */
    private String hostUuid;
    /**
     * 镜像创建时间
     */
    private LocalDateTime vCreateDate;
    /**
     * 镜像更新时间
     */
    private LocalDateTime vUpdateDate;
    /**
     * 系统语言
     */
    private String osLanguage;
    /**
     * 最小内存要求(MB)
     */
    private BigDecimal minMemory;
    /**
     * 最小磁盘要求(GB)
     */
    private BigDecimal minDisk;
    /**
     * 磁盘驱动
     */
    private String diskDriver;
    /**
     * 网卡驱动
     */
    private String networkDriver;
    /**
     * 引导方式
     */
    private String bootMode;
    /**
     * 远程终端协议
     */
    private String remoteProtocol;
    /**
     * 应用平台
     */
    private String applicationPlatform;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;

    private String hostName;

}
