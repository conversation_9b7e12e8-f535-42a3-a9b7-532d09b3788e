package cn.iocoder.zj.module.monitor.service.hostinfo;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.monitor.api.cloud.dto.CloudRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.controller.admin.hostinfo.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.hostinfo.HostInfoDO;
import cn.iocoder.zj.module.monitor.pojo.AssetReqVO;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 云主机基本信息 Service 接口
 *
 * <AUTHOR>
 */

public interface HostInfoService {

    /**
     * 创建云主机基本信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createHostInfo(@Valid HostInfoCreateReqVO createReqVO);

    /**
     * 更新云主机基本信息
     *
     * @param updateReqVO 更新信息
     */
    void updateHostInfo(@Valid HostInfoUpdateReqVO updateReqVO);

    /**
     * 删除云主机基本信息
     *
     * @param id 编号
     */
    void deleteHostInfo(Long id);

    /**
     * 获得云主机基本信息
     *
     * @param uuid 编号
     * @return 云主机基本信息
     */
    HostInfoDO getHostInfo(String uuid);

    /**
     * 获得云主机基本信息列表
     *
     * @param ids 编号
     * @return 云主机基本信息列表
     */
    List<HostInfoDO> getHostInfoList(Collection<Long> ids);

    /**
     * 获得云主机基本信息分页
     *
     * @param pageReqVO 分页查询
     * @return 云主机基本信息分页
     */
    PageResult<HostInfoDO> getHostInfoPage(HostInfoPageReqVO pageReqVO);

    /**
     * 获得云主机基本信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 云主机基本信息列表
     */
    List<HostInfoDO> getExportInfoList(HostInfoExportReqVO exportReqVO);


    void createHostInfoList(Collection<HostInfoDO> reqDTO);

    int getCount(String typeName);

    void updateHostInfoList(List<HostInfoRpcVO> list);
    List<HostInfoDO> getAll(String typeName);

    List<Map> getHostCpuInfoService(Long id, String uuid, String timeStr);

    List<Map> getHostMemoryInfo(Long id, String uuid, String timeStr);

    List<Map> getHostNetworkInfo(Long id, String uuid, String timeStr);

    List<Map> getHostDiskInfo(Long id, String uuid, String timeStr);

    List<Map<String, Object>> getLabel(Long platformId, String uuid, int labelKey);

    List<MonitorHostinfoDataRespVO> getHostGraphData(MonitorHostinfoDataReqVo reqVo);
    List<MonitorHostinfoDataRespVO> getHostGraphDataRandom(MonitorHostinfoDataReqVo reqVo);
    Map<String,Object> getHostStatusCount(List<String> tenantIds,Long platformId);
    List<Map<String,Object>> getCpuTop(List<String> tenantIds,Long platformId,Long regionId,int top);
    List<Map<String,Object>> getMemoryTop(List<String> tenantIds,Long platformId,Long regionId,int top);
    PageResult<HostInfoDO> selectHostList(HostInfoPageReqVO pageReqVO);

    List<HostInfoDO> getHostListByUuids(List<String> list);
    List<ReckonVO> getHostValuation(ReckonReqVO reqVo);
    List<ReckonVO> getMemoryValuation(ReckonReqVO reqVo);
    List<ReckonVO> getDickValuation(ReckonReqVO reqVo);
    List<ReckonVO> getCPUValuation(ReckonReqVO reqVo);
    Map<String,Object> compareAndForecast(ReckonReqVO reqVo);

    Map<String, Object> getUsage(ReckonReqVO reqVo);

    List<HostInfoDO> getListByPlatformId(String platformIds);
    Map<String,Object> getHostAlarmByType(ReckonReqVO reqVo);

    void updateHostInfoByUuid(HostInfoUpdateReqVO hostInfoDO);

    int deleteHostList(List<HostInfoDO> list);

    PageResult getResourcePage(MixResourceSimpleInfoReqVO reqVO);

    List<Map<String, String>> getRecourseByPlatform(MixResourceSimpleInfoReqVO reqVO);

    CommonResult<HostInfoRespCreateReqDTO> getByUuid(String hostUuid);


    PageResult<HostInfoDO> getHostInfoSlavePagePage(HostInfoPageReqVO pageVO);

    HostInfoDO slaveget(String uuid);

    HostInfoDO masterget(String uuid);
    Map<String,List<String>> getAllDeviceByPlatformId(Collection<Long> platformIds);

    List<Map<String,String>> getClusterSimpleInfo(String platformId);


    HostInfoDO getHardwareMasterInfo(String uuid);

    List<HostInfoDO> getListAll();

    void removeDuplicateData();


    void deleteHostInfoByplatform(Long id);

    void exportWord(HttpServletResponse response,String platformId,String uuid);

    void deletMonitorByPlatformId(Long platformId);


    void alertsnmpTrap(String platformName, String hostName, String json);

    List<CloudRespCreateReqDTO> getCloudByPlatformId(Long platformId);

    List<Map> getMonitorAsset(Long platformId);

    void deletMonitorAssetByPlatformId(Long platformId);

    CommonResult<HostInfoRespCreateReqDTO> getById(Long hostUuid);

    boolean authorization(Long platformId, Long id);

    List<HostInfoDO> getHostListByTenantOrPlatforms(AssetReqVO assetReqVO);

    List<HostInfoDO> getByPlatformIdAndTags(AssetReqVO assetReqVO);
}
