package cn.iocoder.zj.module.monitor.service.topology;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.StrSplitter;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import cn.iocoder.cloud.module.cloudedge.api.monitor.MonitorApi;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.framework.warehouse.service.ZjMetricsDataService;
import cn.iocoder.zj.module.monitor.controller.admin.topology.vo.TopologyCreateReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.topology.vo.TopologyExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.topology.vo.TopologyPageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.topology.vo.TopologyUpdateReqVO;
import cn.iocoder.zj.module.monitor.convert.topology.TopologyConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.topology.TopologyDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.topologyrelation.TopologyRelationDO;
import cn.iocoder.zj.module.monitor.dal.mysql.alarminfo.AlarmInfoMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.topology.TopologyMapper;
import cn.iocoder.zj.module.monitor.dal.mysql.topologyrelation.TopologyRelationMapper;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import com.alibaba.fastjson.JSONObject;
import org.apache.hertzbeat.common.entity.alerter.AlertRespVo;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.format.DateTimeParseException;
import java.util.*;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.TOPOLOGY_NOT_EXISTS;

/**
 * 监控资源拓扑图 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TopologyServiceImpl implements TopologyService {

    @Resource
    private TopologyMapper topologyMapper;
    @Resource
    private TopologyRelationMapper topologyRelationMapper;
    @Resource
    private AlarmInfoMapper alarmInfoMapper;
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    MonitorApi monitorApi;


    private final ZjMetricsDataService metricsDataService;

    TopologyServiceImpl(ZjMetricsDataService metricsDataService) {
        this.metricsDataService = metricsDataService;
    }

    private static final String QUERY_HARDWARE_SQL = "SELECT type, value FROM zj_cloud_hardware WHERE time >= now() - 6h and type = '%s' and uuid = '%s' and metricName = '%s' order by time desc limit 1";

    @Override
    @TenantIgnore
    public Long createTopology(TopologyCreateReqVO createReqVO) {

        // Long count = topologyMapper.selectCount("platform_id",
        // createReqVO.getPlatformId());
        // if (count > 0) {
        // return -1L;
        // }
        // 插入
        TopologyDO topology = TopologyConvert.INSTANCE.convert(createReqVO);
        topologyMapper.insert(topology);
        if (StrUtil.isNotEmpty(createReqVO.getInterfacesJson())) {

            JSONArray jsonArray = new JSONArray(topology.getInterfacesJson());
            List<Map> list = JSONUtil.toList(jsonArray, Map.class);

            List<TopologyRelationDO> topologyRelationDOS = new ArrayList<>();
            if (list.size() > 0) {
                for (Map map : list) {
                    TopologyRelationDO topologyRelationDO = new TopologyRelationDO();
                    topologyRelationDO.setTopologyId(topology.getId());
                    topologyRelationDO.setMonitorId(Convert.toStr(map.get("monitorId")));
                    topologyRelationDO.setMonitorName(Convert.toStr(map.get("monitorName")));
                    topologyRelationDO.setMonitorInterfaces(Convert.toStr(map.get("monitorInterfaces")));
                    topologyRelationDOS.add(topologyRelationDO);
                }
                topologyRelationMapper.insertBatch(topologyRelationDOS);
            }
        }

        return topology.getId();
    }

    @Override
    @TenantIgnore
    public void updateTopology(TopologyUpdateReqVO updateReqVO) {
        // // 校验存在
        // validateTopologyExists(updateReqVO.getId());
        //
        //
        // LambdaQueryWrapper<TopologyDO> updateWrapper = new
        // LambdaQueryWrapper<TopologyDO>()
        // .eq(TopologyDO::getPlatformId, updateReqVO.getPlatformId()); // 这里假设 getId()
        // 是获取主键的方法，你可以根据实际情况调整

        // 更新
        TopologyDO updateObj = TopologyConvert.INSTANCE.convert(updateReqVO);
        topologyMapper.updateById(updateObj);
    }

    @Override
    @TenantIgnore
    public void deleteTopology(Long id) {
        // 校验存在
        validateTopologyExists(id);
        // 删除
        topologyMapper.deleteById(id);
    }

    private void validateTopologyExists(Long id) {
        if (topologyMapper.selectById(id) == null) {
            throw exception(TOPOLOGY_NOT_EXISTS);
        }
    }

    @Override
    @TenantIgnore
    public TopologyDO getTopology(Long id) {
        return topologyMapper.selectById(id);
    }

    @Override
    @TenantIgnore
    public List<TopologyDO> getTopologyList(Long platformId) {
        if (platformId != null) {
            return topologyMapper.selectList("platform_id", platformId);
        }
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        List<Map> platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
        return topologyMapper.selectListByPlatformId(platform);
    }

    @Override
    @TenantIgnore
    public PageResult<TopologyDO> getTopologyPage(TopologyPageReqVO pageReqVO) {
        return topologyMapper.selectPage(pageReqVO);
    }

    @Override
    @TenantIgnore
    public List<TopologyDO> getTopologyList(TopologyExportReqVO exportReqVO) {
        return topologyMapper.selectList(exportReqVO);
    }

    @Override
    @TenantIgnore
    public TopologyDO getTopologyByPlatformId(Long id) {
        return topologyMapper.selectById(id);
    }

    @Override
    @TenantIgnore
    public void deleteAssetAndHostJson(String id, String type) {
        List<TopologyDO> topologyDOList = topologyMapper.selectList();
        if (topologyDOList.size() == 0) {
            return;
        }
        for (TopologyDO topology : topologyDOList) {
            String resourceJson = topology.getResourceJson();

            // 将JSON字符串转化为JSONObject
            JSONObject resourceJsonObject = JSONObject.parseObject(resourceJson);
            // 将JSONObject转化为Map
            Map<String, Object> resourceJsonMap = resourceJsonObject.getInnerMap();

            if (type.equals("asset")) {
                List<Map<String, Object>> assetsList = (List<Map<String, Object>>) resourceJsonMap.get("assetsList");
                int assetFlag = 0;
                for (int i = 0; i < assetsList.size(); i++) {
                    if (assetsList.get(i).get("id").toString().equals(id)) {
                        assetsList.remove(i);
                        assetFlag = 1;
                        i--;
                    }
                }
                if (assetFlag == 1) {
                    resourceJsonMap.put("assetsList", assetsList);
                    resourceJsonObject = (JSONObject) JSONObject.toJSON(resourceJsonMap);
                    topology.setResourceJson(resourceJsonObject.toJSONString());
                }
            } else if (type.equals("host")) {
                List<Map<String, Object>> hostList = (List<Map<String, Object>>) resourceJsonMap.get("hostList");
                int hostFlag = 0;
                for (int i = 0; i < hostList.size(); i++) {
                    if (hostList.get(i).get("uuid").toString().equals(id)) {
                        hostList.remove(i);
                        hostFlag = 1;
                        i--;
                    }
                }
                if (hostFlag == 1) {
                    resourceJsonMap.put("hostList", hostList);
                    resourceJsonObject = (JSONObject) JSONObject.toJSON(resourceJsonMap);
                    topology.setResourceJson(resourceJsonObject.toJSONString());
                }
            }

            String topologyJson = topology.getTopologyJson();
            // 将JSON字符串转化为JSONObject
            JSONObject topologyJsonObject = JSONObject.parseObject(topologyJson);
            // 将JSONObject转化为Map
            Map<String, Object> topologyJsonMap = topologyJsonObject.getInnerMap();

            List<Map<String, Object>> nodesList = (List<Map<String, Object>>) topologyJsonMap.get("nodes");
            List<Map<String, Object>> edgesList = (List<Map<String, Object>>) topologyJsonMap.get("edges");
            int nodeFlag = 0;
            for (int i = 0; i < nodesList.size(); i++) {
                if (nodesList.get(i).get("id").toString().equals(id)) {
                    nodesList.remove(i);
                    nodeFlag = 1;
                    i--;
                }
            }
            int edgeFlag = 0;
            for (int i = 0; i < edgesList.size(); i++) {
                if (edgesList.get(i).get("target").toString().equals(id)) {
                    edgesList.remove(i);
                    edgeFlag = 1;
                    i--;
                }
            }
            if (nodeFlag == 1 || edgeFlag == 1) {
                topologyJsonMap.put("nodes", nodesList);
                topologyJsonMap.put("edges", edgesList);
                topologyJsonObject = (JSONObject) JSONObject.toJSON(topologyJsonMap);
                topology.setTopologyJson(topologyJsonObject.toJSONString());
            }

        }
        topologyMapper.updateBatch(topologyDOList);
    }

    @Override
    public List<AlertRespVo> getAlarmInfo(String monitorId) {
        List<String> str = StrSplitter.split(monitorId, ',', 0, true, true);
        return alarmInfoMapper.getAlertInfoByMonitorId(str);
    }

    @Override
    public Map getmonitorStatus(String uuid, String type) {
        List<Map> list = new ArrayList<>();
        List<String> hostUuids = new ArrayList<>();
        List<String> hzUuids = new ArrayList<>();
        // 分类UUID
        if ("host".equals(type)) {
            hostUuids = StrSplitter.split(uuid, ',', 0, true, true);
        } else {
            hzUuids = StrSplitter.split(uuid, ',', 0, true, true);
        }
        // 批量查询
        List<Map<String, Map>> hostStates = new ArrayList<>();
        List<Map<String, Map>> hzStates = new ArrayList<>();
        List<Map<String, Map>> alertStates = new ArrayList<>();
        if (!hostUuids.isEmpty()) {
            hostStates = topologyMapper.selectHostStates(hostUuids);
        }
        if (!hzUuids.isEmpty()) {
            hzStates = topologyMapper.selectHzStates(hzUuids);
        }
        // 处理结果
        List<String> str = StrSplitter.split(uuid, ',', 0, true, true);
        Map statusMap = new HashMap<>();
        statusMap.put("hostState", "");
        statusMap.put("alertState", "");
        for (String uuids : str) {
            if ("host".equals(type)) {
                for (Map hostState : hostStates) {
                    if (Convert.toStr(hostState.get("status")).equals("Disconnected")) {
                        statusMap.put("hostState", Convert.toStr(hostState != null ? hostState.get("status") : ""));
                        break;
                    }
                    if (Convert.toStr(hostState.get("status")).equals("Connected")) {
                        statusMap.put("hostState", Convert.toStr(hostState != null ? hostState.get("status") : ""));
                        break;
                    }
                    if (Convert.toStr(hostState.get("status")).equals("Connecting")) {
                        statusMap.put("hostState", Convert.toStr(hostState != null ? hostState.get("status") : ""));
                        break;
                    }
                }
            } else {
                for (Map hzState : hzStates) {
                    if (Convert.toStr(hzState.get("status")).equals("2")) {
                        statusMap.put("hostState", Convert.toStr(hzState != null ? hzState.get("status") : ""));
                        break;
                    }
                    if (Convert.toStr(hzState.get("status")).equals("1")) {
                        statusMap.put("hostState", Convert.toStr(hzState != null ? hzState.get("status") : ""));
                        break;
                    }
                    if (Convert.toStr(hzState.get("status")).equals("0")) {
                        statusMap.put("hostState", Convert.toStr(hzState != null ? hzState.get("status") : ""));
                        break;
                    }
                }
            }
            Map map1 = topologyMapper.selectAlertBystate(uuids);
            if (map1 != null) {
                statusMap.put("alertState", Convert.toStr(map1.get("priority") != null ? map1.get("priority") : ""));
            }
            list.add(statusMap);
        }
        return statusMap;
    }

    @Override
    @TenantIgnore
    public Map getmonitorCount(String id) {

        Map maps = new HashMap();
        maps.put("alertCount", 0);
        maps.put("assetsCount", 0);

        TopologyDO topologyDO = topologyMapper.selectById(id);
        List<String> alertUuid = new ArrayList<>();

        List<Map> assetsList = JSONObject.parseArray(
                JSONObject.parseObject(topologyDO.getResourceJson()).getJSONArray("assetsList").toJSONString(),
                Map.class);
        List<Map> physicalList = JSONObject.parseArray(
                JSONObject.parseObject(topologyDO.getResourceJson()).getJSONArray("physicalList").toJSONString(),
                Map.class);

        if (!assetsList.isEmpty()) {
            for (Map map : assetsList) {
                alertUuid.add(Convert.toStr(map.get("id")));
            }
        }
        if (!physicalList.isEmpty()) {
            for (Map map : physicalList) {
                alertUuid.add(Convert.toStr(map.get("uuid")));
            }
        }
        if (alertUuid.size() > 0) {
            List<Map<String, Map>> list = topologyMapper.selectAlertStates(alertUuid);
            if (!list.isEmpty()) {
                maps.put("alertCount", list.size());
            }
        }

        double assetsCount = NumberUtil.add(assetsList.size(), physicalList.size());
        maps.put("assetsCount", Convert.toLong(assetsCount));
        return maps;
    }

    @Override
    @TenantIgnore
    public Map getmonitorListInfo(String id) {
        List<Map> assets = new ArrayList<>();
        List<Map> alerts = new ArrayList<>();
        List<String> alertUuid = new ArrayList<>();
        List<String> assetsUuid = new ArrayList<>();
        List<String> hostUuid = new ArrayList<>();
        Map maps = new HashMap();
        maps.put("alertList", alerts);
        maps.put("assetsList", assets);
        TopologyDO topologyDO = topologyMapper.selectById(id);
        List<Map> assetsList = JSONObject.parseArray(
                JSONObject.parseObject(topologyDO.getResourceJson()).getJSONArray("assetsList").toJSONString(),
                Map.class);
        List<Map> physicalList = JSONObject.parseArray(
                JSONObject.parseObject(topologyDO.getResourceJson()).getJSONArray("physicalList").toJSONString(),
                Map.class);
        List<Map> nodeList = JSONObject.parseArray(
                JSONObject.parseObject(topologyDO.getTopologyJson()).getJSONArray("nodes").toJSONString(), Map.class);
        for (Map map : nodeList) {
            if (Convert.toStr(map.get("sysDeviceType")).equals("host")) {
                List<Map> as = JSONObject.parseArray(JSONUtil.toJsonStr(map.get("assetsList")), Map.class);
                for (Map map1 : as) {
                    alertUuid.add(Convert.toStr(map1.get("uuid")));
                }
            } else {
                List<Map> as = JSONObject.parseArray(JSONUtil.toJsonStr(map.get("assetsList")), Map.class);
                for (Map map1 : as) {
                    alertUuid.add(Convert.toStr(map1.get("id")));
                }
            }
        }
        if (!assetsList.isEmpty()) {
            for (Map map : assetsList) {
                assetsUuid.add(Convert.toStr(map.get("id")));
            }
        }
        if (!physicalList.isEmpty()) {
            for (Map map : physicalList) {

                hostUuid.add(Convert.toStr(map.get("uuid")));
            }
        }
        if (!assetsUuid.isEmpty()) {
            List<Map> list = topologyMapper.selectHzStatesByList(assetsUuid, topologyDO.getPlatformId());
            for (Map map : list) {
                List<Map<String, Object>> Map = monitorApi.getAppHierarchy();
                // 使用流的方式，将Map中value值和map.get("app")值相同的Map找出来
                Map<String, Object> map2 = Map.stream()
                        .filter(m -> Convert.toStr(m.get("value")).equals(Convert.toStr(map.get("app"))))
                        .findFirst()
                        .orElse(null);

                // todo 通过app 找到大类类型
                Map<String, Object> map1 = new HashMap<>();
                map1.put("id", Convert.toStr(map.get("uuid")));
                map1.put("category", Convert.toStr(map2.get("category")));
                map1.put("name", Convert.toStr(map.get("name")));
                map1.put("app", Convert.toStr(map.get("app")));
                map1.put("host", Convert.toStr(map.get("host")));
                map1.put("status", Convert.toStr(map.get("status")));
                map1.put("platformName", Convert.toStr(map.get("platform_name")));
                assets.add(map1);
            }
        }
        if (!hostUuid.isEmpty()) {
            List<Map> list = topologyMapper.selectHostStatesByList(hostUuid, topologyDO.getPlatformId());
            for (Map map : list) {

                Map<String, Object> map1 = new HashMap<>();
                map1.put("id", Convert.toStr(map.get("uuid")));
                map1.put("name", Convert.toStr(map.get("name")));
                map1.put("category", "host");
                map1.put("app", Convert.toStr(map.get("app")));
                map1.put("host", Convert.toStr(map.get("host")));
                map1.put("status", Convert.toStr(map.get("status")));
                map1.put("platformName", Convert.toStr(map.get("platform_name")));
                assets.add(map1);
            }
        }
        if (alertUuid.size() > 0) {
            List<Map> list = topologyMapper.selectAlertStatesByList(alertUuid, topologyDO.getPlatformId());
            list.forEach(map -> {
                if (!map.containsKey("category")) {
                    map.put("category", null);
                }
            });
            if (list.size() > 0) {
                maps.put("alertList", list);
            }
        } else {
            maps.put("alertList", new ArrayList<>());
        }

        if (assets.size() > 0) {
            maps.put("assetsList", assets);
        }
        return maps;
    }

    @Override
    @TenantIgnore
    public Map getmonitorInterfacesJson(String id, String monitorId) {
        Map maps = new HashMap();
        maps.put("monitorId", monitorId);
        maps.put("interfacesName", "");
        TopologyDO topologyDO = topologyMapper.selectById(id);
        JSONArray jsonArray = new JSONArray(topologyDO.getInterfacesJson());
        List<Map> list = JSONUtil.toList(jsonArray, Map.class);

        Set<String> uniqueInterfaces = new HashSet<>();
        StringBuilder concatenatedInterfaces = new StringBuilder();
        for (Map map : list) {
            if (monitorId.equals(Convert.toStr(map.get("monitorId")))) {
                uniqueInterfaces.add(Convert.toStr(map.get("monitorInterfaces")));
            }
        }
        String result = String.join(",", uniqueInterfaces);
        maps.put("interfacesName", result);
        return maps;
    }

    @Override
    public List<Map> getHostInterfacesInfo(String monitorId, String monitorInterfaces) {
        List<Map> instanceValueMap = new ArrayList<>();
        String origin = metricsDataService.getMetricHistoryData(monitorId, "net_task", "NetworkInBytes", "1d", false, null, null, "1d", "avg", null).getValues().get("NetworkInBytes").get(0).getOrigin();
        String outOrigin = metricsDataService.getMetricHistoryData(monitorId, "net_task", "NetworkOutBytes", "1d", false, null, null, "1d", "avg", null).getValues().get("NetworkOutBytes").get(0).getOrigin();
        Map<String, String> map = new HashMap<>();
        map.put("name", "all");
        map.put("origin", origin);
        map.put("outigin", outOrigin);
        instanceValueMap.add(map);
        return instanceValueMap;
    }

    @Override
    @TenantIgnore
    public List<TopologyDO> getList() {
        return topologyMapper.selectList();
    }

    @Override
    @TenantIgnore
    public void updatebatchTopology(List<TopologyDO> topologyDO) {
        topologyMapper.updateBatch(topologyDO);
    }

    private long parseTimeToMillis(Object time) {
        if (time == null) {
            return 0L;
        }

        if (time instanceof String) {
            try {
                Instant instant = Instant.parse((String) time);
                return instant.toEpochMilli();
            } catch (DateTimeParseException e) {
                throw new IllegalArgumentException("Invalid time format. Expected ISO 8601 format.", e);
            }
        } else if (time instanceof Number) {
            return ((Number) time).longValue();
        }
        throw new IllegalArgumentException("Time must be a String in ISO 8601 format or a Number");
    }

    private String parseDoubleValue(String value) {
        return (new BigDecimal(value)).setScale(4, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
    }
}
