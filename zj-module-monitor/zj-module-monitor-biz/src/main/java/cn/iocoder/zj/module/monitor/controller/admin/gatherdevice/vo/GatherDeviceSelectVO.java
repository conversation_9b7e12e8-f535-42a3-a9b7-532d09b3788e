package cn.iocoder.zj.module.monitor.controller.admin.gatherdevice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName : GatherDeviceSelectVO  //类名
 * @Description : 设备下拉框  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/8/15  11:13
 */
@Data
public class GatherDeviceSelectVO {
    @Schema(description = "采集设备uuid")
    private String uuid;
    @Schema(description = "平台名称")
    private String platformName;
    @Schema(description = "平台id")
    private Long platformId;
}
