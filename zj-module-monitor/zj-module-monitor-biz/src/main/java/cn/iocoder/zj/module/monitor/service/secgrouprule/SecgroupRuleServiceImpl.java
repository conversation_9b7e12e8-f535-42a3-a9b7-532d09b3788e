package cn.iocoder.zj.module.monitor.service.secgrouprule;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.controller.admin.secgrouprule.vo.SecgroupRuleCreateReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.secgrouprule.vo.SecgroupRuleExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.secgrouprule.vo.SecgroupRulePageReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.secgrouprule.vo.SecgroupRuleUpdateReqVO;
import cn.iocoder.zj.module.monitor.convert.secgrouprule.SecgroupRuleConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.secgrouprule.SecgroupRuleDO;
import cn.iocoder.zj.module.monitor.dal.mysql.secgrouprule.SecgroupRuleMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * 端口组规则 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SecgroupRuleServiceImpl implements SecgroupRuleService {

    @Resource
    private SecgroupRuleMapper secgroupRuleMapper;

    @Override
    public Long createSecgroupRule(SecgroupRuleCreateReqVO createReqVO) {
        // 插入
        SecgroupRuleDO secgroupRule = SecgroupRuleConvert.INSTANCE.convert(createReqVO);
        secgroupRuleMapper.insert(secgroupRule);
        // 返回
        return secgroupRule.getId();
    }

    @Override
    public void updateSecgroupRule(SecgroupRuleUpdateReqVO updateReqVO) {
        // 校验存在
        validateSecgroupRuleExists(updateReqVO.getId());
        // 更新
        SecgroupRuleDO updateObj = SecgroupRuleConvert.INSTANCE.convert(updateReqVO);
        secgroupRuleMapper.updateById(updateObj);
    }

    @Override
    public void deleteSecgroupRule(Long id) {
        // 校验存在
        validateSecgroupRuleExists(id);
        // 删除
        secgroupRuleMapper.deleteById(id);
    }

    private void validateSecgroupRuleExists(Long id) {
//        if (secgroupRuleMapper.selectById(id) == null) {
//            throw exception(SECGROUP_RULE_NOT_EXISTS);
//        }
    }

    @Override
    @TenantIgnore
    public SecgroupRuleDO getSecgroupRule(Long id) {
        return secgroupRuleMapper.selectById(id);
    }

    @Override
    public List<SecgroupRuleDO> getSecgroupRuleList(Collection<Long> ids) {
        return secgroupRuleMapper.selectBatchIds(ids);
    }

    @Override
    @TenantIgnore
    public PageResult<SecgroupRuleDO> getSecgroupRulePage(SecgroupRulePageReqVO pageReqVO) {
        return secgroupRuleMapper.selectPage(pageReqVO);
    }

    @Override
    public List<SecgroupRuleDO> getSecgroupRuleList(SecgroupRuleExportReqVO exportReqVO) {
        return secgroupRuleMapper.selectList(exportReqVO);
    }

    @Override
    public void deleteSecgroupRules(List<SecgroupRuleDO> list) {
        List<Long> ids = list.stream().map(SecgroupRuleDO::getId).toList();
        secgroupRuleMapper.deleteBatchIds(ids);
    }

    @Override
    public void updateSecgroupRules(List<SecgroupRuleDO> list) {
        secgroupRuleMapper.updateBatch(list);
    }

    @Override
    public void createSecgroupRuleList(List<SecgroupRuleDO> list) {
        secgroupRuleMapper.insertBatch(list);
    }

    @Override
    public List<SecgroupRuleDO> getListBySecgroupUuids(final List<String> secgroupUuids) {
        return secgroupRuleMapper.getListBySecgroupUuids(secgroupUuids);
    }

}
