package cn.iocoder.zj.module.monitor.controller.admin.topreport.vo;

import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.List;

@Data
public class TopReportMetricVO extends BaseDO {


    private Long id;

    /**
     * TOP报表ID
     */
    private Long reportId;

    /**
     * 指标名称
     */
    private String metricName;

    private String metricCode;

    private Long metricId;

    private Long pid;

    private List<TopReportMetricVO> subList;
}
