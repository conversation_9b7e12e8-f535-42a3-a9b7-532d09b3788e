package cn.iocoder.zj.module.monitor.controller.admin.imageinfo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.util.object.ObjectUtils;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.controller.admin.hardwareinfo.vo.HardwareInfoRespVO;
import cn.iocoder.zj.module.monitor.controller.admin.taggables.vo.TaggablesExportReqVO;
import cn.iocoder.zj.module.monitor.convert.tags.TagsConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.taggables.TaggablesDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.tags.TagsDO;
import cn.iocoder.zj.module.monitor.service.taggables.TaggablesService;
import cn.iocoder.zj.module.monitor.service.taggables.enums.TagAssetTypeEnum;
import cn.iocoder.zj.module.monitor.service.tags.TagsService;
import cn.iocoder.zj.module.monitor.util.StringUtil;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;
import static java.util.Collections.singleton;

import cn.iocoder.zj.module.monitor.controller.admin.imageinfo.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.imageinfo.ImageInfoDO;
import cn.iocoder.zj.module.monitor.convert.imageinfo.ImageInfoConvert;
import cn.iocoder.zj.module.monitor.service.imageinfo.ImageInfoService;

@Tag(name = "管理后台 - 镜像信息")
@RestController
@RequestMapping("/monitor/image-info")
@Validated
public class ImageInfoController {

    @Resource
    private ImageInfoService imageInfoService;
    @Resource
    private RoleApi roleApi;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private TagsService tagsService;
    @Resource
    private TaggablesService taggablesService;

    @PostMapping("/create")
    @Operation(summary = "创建镜像信息")
    //@PreAuthorize("@ss.hasPermissionission('monitor:image-info:create')")
    public CommonResult<Long> createImageInfo(@Valid @RequestBody ImageInfoCreateReqVO createReqVO) {
        return success(imageInfoService.createImageInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新镜像信息")
    //@PreAuthorize("@ss.hasPermissionission('monitor:image-info:update')")
    public CommonResult<Boolean> updateImageInfo(@Valid @RequestBody ImageInfoUpdateReqVO updateReqVO) {
        imageInfoService.updateImageInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除镜像信息")
    @Parameter(name = "id", description = "编号", required = true)
    //@PreAuthorize("@ss.hasPermissionission('monitor:image-info:delete')")
    public CommonResult<Boolean> deleteImageInfo(@RequestParam("id") Long id) {
        imageInfoService.deleteImageInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得镜像信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermissionission('monitor:image-info:query')")
    @TenantIgnore
    public CommonResult<ImageInfoRespVO> getImageInfo(@RequestParam("id") Long id) {
        ImageInfoDO imageInfo = imageInfoService.getImageInfo(id);
        ImageInfoRespVO respVO = ImageInfoConvert.INSTANCE.convert(imageInfo);
        //标签
        List<TagsDO> tag = tagsService.getTagListByTagGables(new TaggablesExportReqVO()
                .setTaggableId(respVO.getId())
                .setTaggableType(TagAssetTypeEnum.IMAGE.getCode())
        );
        respVO.setTags(TagsConvert.INSTANCE.convertList(tag));
        return success(respVO);
    }

    @GetMapping("/list")
    @Operation(summary = "获得镜像信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    //@PreAuthorize("@ss.hasPermissionission('monitor:image-info:query')")
    @TenantIgnore
    public CommonResult<List<ImageInfoRespVO>> getImageInfoList(@RequestParam("ids") Collection<Long> ids) {
        List<ImageInfoDO> list = imageInfoService.getImageInfoList(ids);
        return success(ImageInfoConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得镜像信息分页")
    //@PreAuthorize("@ss.hasPermissionission('monitor:image-info:query')")
    @TenantIgnore
    public CommonResult<PageResult<ImageInfoRespVO>> getImageInfoPage(@Valid ImageInfoPageReqVO pageVO) {
        if (ObjectUtil.isNotEmpty(pageVO.getTagIds())) {
            List<TaggablesDO> tmp = taggablesService.getTaggablesList(new TaggablesExportReqVO()
                    .setTaggableType(TagAssetTypeEnum.IMAGE.getCode())
                    .setTagId(Long.valueOf(pageVO.getTagIds()))
            );

            if (!tmp.isEmpty()) {
                pageVO.setIds(tmp.stream().map(TaggablesDO::getTaggableId).distinct().toList());
            } else {
                return success(PageResult.empty());
            }
        }

        PageResult<ImageInfoDO> pageResult = imageInfoService.getImageInfoPage(pageVO);
        List<ImageInfoRespVO> infoRespVOS = new ArrayList<>();
        if (pageResult.getList() != null && !pageResult.getList().isEmpty()) {
            infoRespVOS = BeanUtil.copyToList(pageResult.getList(), ImageInfoRespVO.class);
        }
        for (ImageInfoRespVO respVO : infoRespVOS) {
            List<TagsDO> tag = tagsService.getTagListByTagGables(new TaggablesExportReqVO()
                    .setTaggableId(respVO.getId())
                    .setTaggableType(TagAssetTypeEnum.IMAGE.getCode())
            );
            respVO.setTags(TagsConvert.INSTANCE.convertList(tag));
        }
        PageResult<ImageInfoRespVO> pageResults = new PageResult<>();
        pageResults.setList(infoRespVOS);
        pageResults.setTotal(pageResult.getTotal());
        return success(pageResults);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出镜像信息 Excel")
    //@PreAuthorize("@ss.hasPermissionission('monitor:image-info:export')")
    @OperateLog(type = EXPORT)
    @TenantIgnore
    public void exportImageInfoExcel(@Valid ImageInfoExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        if (ObjectUtil.isNotEmpty(exportReqVO.getTagIds())) {
            List<TaggablesDO> tmp = taggablesService.getTaggablesList(new TaggablesExportReqVO()
                    .setTaggableType(TagAssetTypeEnum.IMAGE.getCode())
                    .setTagId(Long.valueOf(exportReqVO.getTagIds()))
            );

            if (!tmp.isEmpty()) {
                exportReqVO.setIds(tmp.stream().map(TaggablesDO::getTaggableId).distinct().toList());
            } else {
                return;
            }
        }
        List<ImageInfoDO> list = imageInfoService.getImageInfoList(exportReqVO);
        // 导出 Excel
        List<ImageInfoExcelVO> datas = ImageInfoConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "镜像信息.xls", "数据", ImageInfoExcelVO.class, datas);
    }
    @GetMapping("/image-status-count")
    @Operation(summary = "获取镜像状态统计")
    //@PreAuthorize("@ss.hasPermissionission('monitor:image-info:query')")
    @TenantIgnore
    public CommonResult<Map<String, Object>> getImageStatusCount(@RequestParam(required = false, defaultValue = "") Long platformId) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(loginUser.getId()).getData();
        List<String> tenantIds = new ArrayList<>();
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            if(StringUtil.isNullOrEmpty(adminUserRespDTO.getServiceTenantId())) {
                tenantIds = Arrays.asList(String.valueOf(adminUserRespDTO.getTenantId()).split(","));
            }else {
                tenantIds = Arrays.asList(adminUserRespDTO.getServiceTenantId().split(","));
            }
        }
        return success(imageInfoService.getImageStatusCount(tenantIds,platformId));
    }
}
