package cn.iocoder.zj.module.monitor.dal.mysql.storagepool;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.monitor.api.storagepool.dto.StoragePoolCreateRespDTO;
import cn.iocoder.zj.module.monitor.dal.dataobject.storagepool.StoragePoolDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.monitor.controller.admin.storagepool.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 存储池 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface StoragePoolMapper extends BaseMapperX<StoragePoolDO> {

    default PageResult<StoragePoolDO> selectPage(StoragePoolPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<StoragePoolDO>()
                .eqIfPresent(StoragePoolDO::getUuid, reqVO.getUuid())
                .likeIfPresent(StoragePoolDO::getName, reqVO.getName())
                .eqIfPresent(StoragePoolDO::getType, reqVO.getType())
                .eqIfPresent(StoragePoolDO::getDescription, reqVO.getDescription())
                .betweenIfPresent(StoragePoolDO::getVCreateDate, reqVO.getVCreateDate())
                .betweenIfPresent(StoragePoolDO::getLastOpDate, reqVO.getLastOpDate())
                .eqIfPresent(StoragePoolDO::getUsedCapacity, reqVO.getUsedCapacity())
                .eqIfPresent(StoragePoolDO::getAvailableCapacity, reqVO.getAvailableCapacity())
                .eqIfPresent(StoragePoolDO::getTotalCapacity, reqVO.getTotalCapacity())
                .eqIfPresent(StoragePoolDO::getStorageUuid, reqVO.getStorageUuid())
                .eqIfPresent(StoragePoolDO::getSecurityPolicy, reqVO.getSecurityPolicy())
                .betweenIfPresent(StoragePoolDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(StoragePoolDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(StoragePoolDO::getPlatformName, reqVO.getPlatformName())
                .orderByDesc(StoragePoolDO::getId));
    }

    default List<StoragePoolDO> selectList(StoragePoolExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<StoragePoolDO>()
                .eqIfPresent(StoragePoolDO::getUuid, reqVO.getUuid())
                .likeIfPresent(StoragePoolDO::getName, reqVO.getName())
                .eqIfPresent(StoragePoolDO::getType, reqVO.getType())
                .eqIfPresent(StoragePoolDO::getDescription, reqVO.getDescription())
                .betweenIfPresent(StoragePoolDO::getVCreateDate, reqVO.getVCreateDate())
                .betweenIfPresent(StoragePoolDO::getLastOpDate, reqVO.getLastOpDate())
                .eqIfPresent(StoragePoolDO::getUsedCapacity, reqVO.getUsedCapacity())
                .eqIfPresent(StoragePoolDO::getAvailableCapacity, reqVO.getAvailableCapacity())
                .eqIfPresent(StoragePoolDO::getTotalCapacity, reqVO.getTotalCapacity())
                .eqIfPresent(StoragePoolDO::getStorageUuid, reqVO.getStorageUuid())
                .eqIfPresent(StoragePoolDO::getSecurityPolicy, reqVO.getSecurityPolicy())
                .betweenIfPresent(StoragePoolDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(StoragePoolDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(StoragePoolDO::getPlatformName, reqVO.getPlatformName())
                .orderByDesc(StoragePoolDO::getId));
    }

    List<StoragePoolCreateRespDTO> getStoragePoolByPlatformId(@Param("id") Long platformId);

    int deletes(@Param("list") List<StoragePoolDO> list);
}
