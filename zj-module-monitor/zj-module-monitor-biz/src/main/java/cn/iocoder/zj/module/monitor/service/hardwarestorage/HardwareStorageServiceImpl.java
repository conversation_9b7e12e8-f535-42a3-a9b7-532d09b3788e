package cn.iocoder.zj.module.monitor.service.hardwarestorage;

import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.api.hardwarenic.HardWareNicRespDTO;
import cn.iocoder.zj.module.monitor.api.hardwarestorage.HardWareStorageRespDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.zj.module.monitor.controller.admin.hardwarestorage.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.hardwarestorage.HardwareStorageDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.convert.hardwarestorage.HardwareStorageConvert;
import cn.iocoder.zj.module.monitor.dal.mysql.hardwarestorage.HardwareStorageMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.*;

/**
 * 宿主机与存储关联 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class HardwareStorageServiceImpl implements HardwareStorageService {

    @Resource
    private HardwareStorageMapper hardwareStorageMapper;

    @Override
    public Long createHardwareStorage(HardwareStorageCreateReqVO createReqVO) {
        // 插入
        HardwareStorageDO hardwareStorage = HardwareStorageConvert.INSTANCE.convert(createReqVO);
        hardwareStorageMapper.insert(hardwareStorage);
        // 返回
        return hardwareStorage.getId();
    }

    @Override
    public void updateHardwareStorage(HardwareStorageUpdateReqVO updateReqVO) {
        // 校验存在
        validateHardwareStorageExists(updateReqVO.getId());
        // 更新
        HardwareStorageDO updateObj = HardwareStorageConvert.INSTANCE.convert(updateReqVO);
        hardwareStorageMapper.updateById(updateObj);
    }

    @Override
    public void deleteHardwareStorage(Long id) {
        // 校验存在
        validateHardwareStorageExists(id);
        // 删除
        hardwareStorageMapper.deleteById(id);
    }

    private void validateHardwareStorageExists(Long id) {
        if (hardwareStorageMapper.selectById(id) == null) {
//            throw exception(HARDWARE_STORAGE_NOT_EXISTS);
        }
    }

    @Override
    public HardwareStorageDO getHardwareStorage(Long id) {
        return hardwareStorageMapper.selectById(id);
    }

    @Override
    public List<HardwareStorageDO> getHardwareStorageList(Collection<Long> ids) {
        return hardwareStorageMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<HardwareStorageDO> getHardwareStoragePage(HardwareStoragePageReqVO pageReqVO) {
        return hardwareStorageMapper.selectPage(pageReqVO);
    }

    @Override
    public List<HardwareStorageDO> getHardwareStorageList(HardwareStorageExportReqVO exportReqVO) {
        return hardwareStorageMapper.selectList(exportReqVO);
    }

    @Override
    public void createHardwareStorageList(List<HardwareStorageDO> list) {
        hardwareStorageMapper.insertBatch(list);
    }

    @Override
    public void updateHardwareStorageList(List<HardwareStorageDO> list) {
        hardwareStorageMapper.updateBatch(list);
    }

    @Override
    public int deleteHardwareStorageList(List<HardwareStorageDO> list) {
        return hardwareStorageMapper.deleteBatchIds(list);
    }

    @Override
    public List<HardWareStorageRespDTO> getHardwareStorageByPlatformId(Long platformId) {
        return hardwareStorageMapper.getHardwareStorageByPlatformId(platformId);
    }

    @Override
    @TenantIgnore
    public Integer getHareWareCountByStorageUuid(String storageUuid) {
        return hardwareStorageMapper.getHareWareCountByStorageUuid(storageUuid);
    }

    @Override
    public int deleteByPlatformId(Long platformId) {
        LambdaQueryWrapper<HardwareStorageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HardwareStorageDO::getPlatformId, platformId);
        return hardwareStorageMapper.delete(queryWrapper);
    }

}
