package cn.iocoder.zj.module.monitor.dal.mysql.monitorauthorizationuser;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.monitor.dal.dataobject.monitorauthorizationuser.MonitorAuthorizationUserDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户资产授权申请 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MonitorAuthUserMapper extends BaseMapperX<MonitorAuthorizationUserDO> {

    void deleteAuthorizationUserType(@Param("monitorAssetId") Long monitorAssetId, @Param("userId") Long userId);

    void updateAuthorizationUserType(@Param("monitorAssetId") Long monitorAssetId, @Param("userId") Long userId);

    List<MonitorAuthorizationUserDO>  selectTypeByUserId(@Param("userId") Long userId);
}
