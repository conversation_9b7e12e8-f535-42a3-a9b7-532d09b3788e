package cn.iocoder.zj.module.monitor.dal.mysql.tagplan;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.controller.admin.tagplan.vo.TagPlanExportReqVO;
import cn.iocoder.zj.module.monitor.controller.admin.tagplan.vo.TagPlanPageReqVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.tagplan.TagPlanDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.tagplan.TagPlanDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 巡检计划 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TagPlanMapper extends BaseMapperX<TagPlanDO> {

    default PageResult<TagPlanDO> selectPage(TagPlanPageReqVO reqVO) {

        LambdaQueryWrapperX<TagPlanDO> queryWrapper = new LambdaQueryWrapperX<TagPlanDO>()
                .likeIfPresent(TagPlanDO::getName, reqVO.getName())
                .eqIfPresent(TagPlanDO::getStatus, reqVO.getStatus())
                .eqIfPresent(TagPlanDO::getSysSettingTenant, reqVO.getTenantId())
                .likeIfPresent(TagPlanDO::getPlatformIds, reqVO.getPlatformId());

        if (reqVO.getStartTime() != null && reqVO.getEndTime() != null) {
            queryWrapper.between(TagPlanDO::getLastExecutionTime, reqVO.getStartTime(), reqVO.getEndTime());
        }
        if (reqVO.getSortBy() != null && reqVO.getSortDirection() != null) {
            if (reqVO.getSortBy().equals("lastExecutionTime")) {
                if (reqVO.getSortDirection().equals("asc")) {
                    queryWrapper.orderByAsc(TagPlanDO::getLastExecutionTime);
                } else {
                    queryWrapper.orderByDesc(TagPlanDO::getLastExecutionTime);
                }
            }
            if (reqVO.getSortBy().equals("nextExecutionTime")) {
                if (reqVO.getSortDirection().equals("asc")) {
                    queryWrapper.orderByAsc(TagPlanDO::getNextExecutionTime);
                } else {
                    queryWrapper.orderByDesc(TagPlanDO::getNextExecutionTime);
                }
            }
        }

        return selectPage(reqVO, queryWrapper);
    }

    default List<TagPlanDO> selectList(TagPlanExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TagPlanDO>()
                .likeIfPresent(TagPlanDO::getName, reqVO.getName())
                .eqIfPresent(TagPlanDO::getPeriodType, reqVO.getPeriodType())
                .eqIfPresent(TagPlanDO::getExecutionDay, reqVO.getExecutionDay())
                .betweenIfPresent(TagPlanDO::getExecutionTime, reqVO.getExecutionTime())
                .eqIfPresent(TagPlanDO::getExecutionCron, reqVO.getExecutionCron())
                .eqIfPresent(TagPlanDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(TagPlanDO::getLastExecutionTime, reqVO.getLastExecutionTime())
                .betweenIfPresent(TagPlanDO::getNextExecutionTime, reqVO.getNextExecutionTime())
                .betweenIfPresent(TagPlanDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(TagPlanDO::getJobId, reqVO.getJobId())
                .orderByDesc(TagPlanDO::getId));
    }

    @TenantIgnore
    List<TagPlanDTO> selectTagPlanPage(@Param("mpPage") IPage<TagPlanDTO> mpPage, @Param("reqVO") TagPlanPageReqVO reqVO);

    @TenantIgnore
    String getPlatformSelectList(@Param("tenantId")Long tenantId);
}
