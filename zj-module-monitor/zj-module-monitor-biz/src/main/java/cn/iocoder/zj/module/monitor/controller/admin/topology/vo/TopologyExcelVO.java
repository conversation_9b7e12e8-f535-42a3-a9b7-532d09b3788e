package cn.iocoder.zj.module.monitor.controller.admin.topology.vo;

import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 监控资源拓扑图 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class TopologyExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("拓扑图json")
    private String topologyJson;

    @ExcelProperty("租户名称")
    private String tenantName;

    @ExcelProperty("平台id")
    private Long platformId;

    @ExcelProperty("平台名称")
    private String platformName;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private Date createTime;

}
