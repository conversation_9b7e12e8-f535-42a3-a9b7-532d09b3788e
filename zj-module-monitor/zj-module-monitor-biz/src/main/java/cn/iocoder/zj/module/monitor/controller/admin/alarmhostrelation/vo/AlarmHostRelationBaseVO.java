package cn.iocoder.zj.module.monitor.controller.admin.alarmhostrelation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

/**
* 告警配置与云主机关联关系 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class AlarmHostRelationBaseVO {

    @Schema(description = "告警配置名称,创建时必填")
    private String alarmName;

    @Schema(description = "告警配置ID,创建时必填")
    private Long alarmId;

    @Schema(description = "平台配置ID,创建时必填")
    private Long platformConfigId;

    @Schema(description = "平台配置名称,创建时必填")
    private Long platformConfigName;

    @Schema(description = "主机名称,创建时必填")
    private String hostName;

    @Schema(description = "云主机uuid,创建时必填")
    private String hostUuid;

    @Schema(description = "启用状态:（默认）0正常，1停用")
    private Integer status;

}
