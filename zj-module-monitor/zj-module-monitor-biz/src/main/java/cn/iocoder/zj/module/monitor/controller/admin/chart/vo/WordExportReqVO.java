package cn.iocoder.zj.module.monitor.controller.admin.chart.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName : WordExportReqVO  //类名
 * @Description : word导出  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/9/7  16:30
 */

@Schema(description = "管理后台 - 报表数据 Word 导出 Request VO")
@Data
public class WordExportReqVO {

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "平台ID")
    private Long platformId;

    @Schema(required = false,description = "开始时间")
    private Long startTime;

    @Schema(required = false,description = "结束时间")
    private Long endTime;

}
