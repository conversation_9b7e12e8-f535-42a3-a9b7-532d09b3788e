package cn.iocoder.zj.module.monitor.service.topologyrelation;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.zj.module.monitor.controller.admin.topologyrelation.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.topologyrelation.TopologyRelationDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.monitor.convert.topologyrelation.TopologyRelationConvert;
import cn.iocoder.zj.module.monitor.dal.mysql.topologyrelation.TopologyRelationMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.*;

/**
 * 拓扑图关系 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TopologyRelationServiceImpl implements TopologyRelationService {

    @Resource
    private TopologyRelationMapper topologyRelationMapper;

    @Override
    public Long createTopologyRelation(TopologyRelationCreateReqVO createReqVO) {
        // 插入
        TopologyRelationDO topologyRelation = TopologyRelationConvert.INSTANCE.convert(createReqVO);
        topologyRelationMapper.insert(topologyRelation);
        // 返回
        return topologyRelation.getId();
    }

    @Override
    public void updateTopologyRelation(TopologyRelationUpdateReqVO updateReqVO) {
        // 校验存在
        validateTopologyRelationExists(updateReqVO.getId());
        // 更新
        TopologyRelationDO updateObj = TopologyRelationConvert.INSTANCE.convert(updateReqVO);
        topologyRelationMapper.updateById(updateObj);
    }

    @Override
    public void deleteTopologyRelation(Long id) {
        // 校验存在
        validateTopologyRelationExists(id);
        // 删除
        topologyRelationMapper.deleteById(id);
    }

    private void validateTopologyRelationExists(Long id) {
        if (topologyRelationMapper.selectById(id) == null) {
            throw exception(TOPOLOGY_RELATION_NOT_EXISTS);
        }
    }

    @Override
    public TopologyRelationDO getTopologyRelation(Long id) {
        return topologyRelationMapper.selectById(id);
    }

    @Override
    public List<TopologyRelationDO> getTopologyRelationList(Collection<Long> ids) {
        return topologyRelationMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<TopologyRelationDO> getTopologyRelationPage(TopologyRelationPageReqVO pageReqVO) {
        return topologyRelationMapper.selectPage(pageReqVO);
    }

    @Override
    public List<TopologyRelationDO> getTopologyRelationList(TopologyRelationExportReqVO exportReqVO) {
        return topologyRelationMapper.selectList(exportReqVO);
    }

}
