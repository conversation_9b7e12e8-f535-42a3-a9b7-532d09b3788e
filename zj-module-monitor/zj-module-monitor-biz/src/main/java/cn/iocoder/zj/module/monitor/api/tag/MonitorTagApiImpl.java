package cn.iocoder.zj.module.monitor.api.tag;

import cn.iocoder.zj.module.monitor.api.tag.dto.TagRespDTO;
import cn.iocoder.zj.module.monitor.api.tag.dto.TaggablesDTO;
import cn.iocoder.zj.module.monitor.controller.admin.taggables.vo.TaggablesExportReqVO;
import cn.iocoder.zj.module.monitor.convert.taggables.TaggablesConvert;
import cn.iocoder.zj.module.monitor.convert.tags.TagsConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.taggables.TaggablesDO;
import cn.iocoder.zj.module.monitor.dal.dataobject.tags.TagsDO;
import cn.iocoder.zj.module.monitor.service.taggables.TaggablesService;
import cn.iocoder.zj.module.monitor.service.tags.TagsService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class MonitorTagApiImpl implements MonitorTagApi{
    @Autowired
    private TagsService tagsService;

    @Resource
    private TaggablesService taggablesService;

    @Override
    public List<TagRespDTO> getByTaggable(Long id, String type) {
        List<TagsDO> tag = tagsService.getTagListByTagGables(new TaggablesExportReqVO()
                .setTaggableId(id)
                .setTaggableType(type)
        );
        return TagsConvert.INSTANCE.convertListDto(tag);
    }
    @Override
    public List<TaggablesDTO> getByTag(Long id, String type) {
        List<TaggablesDO> tag = taggablesService.getTaggablesList(new TaggablesExportReqVO()
                .setTagId(id)
                .setTaggableType(type)
        );
        return TaggablesConvert.INSTANCE.convertListDto(tag);
    }

    @Override
    public void add(Long id, String type, String monitorTags) {
        taggablesService.add(id, type, monitorTags);
    }

    @Override
    public void del(Long id) {
        taggablesService.del(id);
    }

}
