package cn.iocoder.zj.module.monitor.controller.admin.storageinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import javax.validation.constraints.*;

/**
* 存储设备信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class StorageInfoBaseVO {

    @Schema(description = "存储名称")
    private String name;

    @Schema(description = "存储uuid")
    private String uuid;

    @Schema(description = "url")
    private String url;

    @Schema(description = "状态")
    private String state;

    @Schema(description = "类型：Ceph")
    private String type;

    @Schema(description = "状态，页面展示值（Connected：已连接，DisConnected: 未连接）")
    private String status;

    @Schema(description = "容量使用率")
    private BigDecimal capacityUtilization;


    @Schema(description = "地区id")
    private Long regionId;

    /**
     * 总容量
     */
    @Schema(description = "总容量")
    private Long totalCapacity;
    /**
     * 已用容量
     */
    @Schema(description = "已用容量")
    private Long usedCapacity;

    @Schema(description = "可用物理容量")
    private BigDecimal availablePhysicalCapacity;
    @Schema(description = "总物理容量")
    private BigDecimal totalPhysicalCapacity;
    @Schema(description = "虚拟可用容量")
    private BigDecimal availableCapacity;
    @Schema(description = "平台类型")
    private String typeName;
    @Schema(description = "平台配置ID")
    private Long platformId;
    @Schema(description = "平台配置名称")
    private String platformName;
    @Schema(description = "介质类型")
    private String  mediaType;
    @Schema(description = "介质类型Id")
    private String  mediaTypeId;
    @Schema(description = "超售比")
    private BigDecimal storagePercent;
    @Schema(description = "区域")
    private String manager;
    @Schema(description = "可用区域")
    private String availableManager;
    @Schema(description = "预留容量")
    private BigDecimal reserveCapacity;
    @Schema(description = "浪费容量")
    private BigDecimal wasteCapacity;
    @Schema(description = "宿主机数量")
    private Integer hardwareCount;
    @Schema(description = "云盘数量")
    private Integer volumeCount;
    @Schema(description = "虚拟容量")
    private BigDecimal virtualCapacity;
    @Schema(description = "分配率")
    private BigDecimal commitRate;
    @Schema(description = "分配")
    private BigDecimal allocation;
    @Schema(description = "更新时间")
    private Date vUpdateTime;
    @Schema(description = "备注")
    private String remark;

}
