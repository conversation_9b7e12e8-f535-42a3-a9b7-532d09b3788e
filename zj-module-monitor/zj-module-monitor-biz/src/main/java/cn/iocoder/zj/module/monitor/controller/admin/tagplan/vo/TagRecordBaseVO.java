package cn.iocoder.zj.module.monitor.controller.admin.tagplan.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 巡检记录 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class TagRecordBaseVO {

    @Schema(description = "报告名称")
    private String recordName;

    @Schema(description = "巡检计划ID")
    private Long planId;

    @Schema(description = "巡检开始时间", required = true)
    @NotNull(message = "巡检开始时间不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startTime;

    @Schema(description = "巡检结束时间", required = true)
    @NotNull(message = "巡检结束时间不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endTime;

    /**
     * 租户ID
     */
    private Long tenantId;

    @Schema(description = "管理员设置的租户id")
    private Long sysSettingTenant;

    @Schema(description = "平台id,多个平台ID，以逗号分隔")
    private String platformIds;

    @Schema(description = "平台id,多个平台ID，以逗号分隔")
    private String tags;

}
