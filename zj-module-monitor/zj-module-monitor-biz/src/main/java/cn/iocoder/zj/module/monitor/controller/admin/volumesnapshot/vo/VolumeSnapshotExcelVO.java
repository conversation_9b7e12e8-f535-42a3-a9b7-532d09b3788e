package cn.iocoder.zj.module.monitor.controller.admin.volumesnapshot.vo;

import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 云盘快照信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class VolumeSnapshotExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("云盘快照uuid")
    private String uuid;

    @ExcelProperty("平台ID")
    private Long platformId;

    @ExcelProperty("云盘快照名称")
    private String name;

    @ExcelProperty("云盘快照描述")
    private String description;

    @ExcelProperty("云主机uuid")
    private String hostUuid;

    @ExcelProperty("云主机名称")
    private String hostName;

    @ExcelProperty("云盘uuid")
    private String volumeUuid;

    @ExcelProperty("云盘名称")
    private String volumeName;

    @ExcelProperty("主存储uuid")
    private String primaryStorageUuid;

    @ExcelProperty("主存储名称")
    private String primaryStorageName;

    @ExcelProperty("云盘快照类型")
    private String type;

    @ExcelProperty("云盘类型")
    private String volumeType;

    @ExcelProperty("是否最新")
    private String latest;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private Date createDate;

    @ExcelProperty("云盘快照大小")
    private BigDecimal size;

    @ExcelProperty("云盘快照状态")
    private String status;

    @ExcelProperty("云盘快照创建时间")
    @ColumnWidth(20)
    private Date vCreateDate;

    @ExcelProperty("云盘快照更新时间")
    @ColumnWidth(20)
    private Date vUpdateDate;

    @ExcelProperty("云盘快照安装路径")
    private String installPath;

    @ExcelProperty("云盘快照格式")
    private String format;

    public void setSize(Long size) {
        if (size != null) {
            this.size = BigDecimal.valueOf(size)
                    .divide(BigDecimal.valueOf(1024 * 1024 * 1024), 2, RoundingMode.HALF_UP);
        } else {
            this.size = BigDecimal.ZERO;
        }
    }
}
