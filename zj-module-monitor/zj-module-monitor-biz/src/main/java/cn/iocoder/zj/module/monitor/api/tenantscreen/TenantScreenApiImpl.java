package cn.iocoder.zj.module.monitor.api.tenantscreen;

import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.service.home.tenantscreen.TenantScreenService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

/**
 * @ClassName : TenantScreenApiImpl  //类名
 * @Description : 使用率  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/4/17  17:36
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class TenantScreenApiImpl implements TenantScreenApi {
    @Resource
    TenantScreenService tenantScreenService;

    @Override
    @TenantIgnore
    public Map<String, Object> getScreenUsageTop(Long userId, Long platformId, String time, Integer status, Long userTenantId) {
        return tenantScreenService.getScreenUsage(userId,platformId,time,1L,status,userTenantId);
    }
}
