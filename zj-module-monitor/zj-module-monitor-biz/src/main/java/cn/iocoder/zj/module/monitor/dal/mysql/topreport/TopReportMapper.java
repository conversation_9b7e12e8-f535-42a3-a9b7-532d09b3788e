package cn.iocoder.zj.module.monitor.dal.mysql.topreport;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.module.monitor.controller.admin.topreport.vo.TopReportPageReqVO;
import cn.iocoder.zj.module.monitor.dal.dataobject.topreport.TopReportDO;
import cn.iocoder.zj.module.monitor.util.StringUtil;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface TopReportMapper extends BaseMapperX<TopReportDO> {

    default PageResult<TopReportDO> selectPage(TopReportPageReqVO reqVO) {
        LambdaQueryWrapperX<TopReportDO> queryWrapper = new LambdaQueryWrapperX<TopReportDO>()
                .likeIfPresent(TopReportDO::getReportName, reqVO.getReportName())
                .eqIfPresent(TopReportDO::getStatisticsType, reqVO.getStatisticsType())
                .eqIfPresent(TopReportDO::getAssetType, reqVO.getAssetType());

        // 添加tenantIds条件（当tenantIds不为空时）
        if (reqVO.getTenantIds() != null && !reqVO.getTenantIds().isEmpty()) {
            queryWrapper.in(TopReportDO::getTenantId, reqVO.getTenantIds());
        }

        if (StringUtil.isNotEmpty(reqVO.getSortDirection()) && reqVO.getSortDirection().equals("asc")){
            if (reqVO.getSortBy().equals("updateTime")){
                queryWrapper.orderByAsc(TopReportDO::getUpdateTime);
            }
        }else {
            queryWrapper.orderByDesc(TopReportDO::getUpdateTime);
        }

        return selectPage(reqVO, queryWrapper);
    }
}
