package cn.iocoder.zj.module.monitor.controller.admin.volumesnapshot.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.*;
import javax.validation.constraints.*;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 云盘快照信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class VolumeSnapshotBaseVO {

    @Schema(description = "云盘快照uuid")
    private String uuid;

    @Schema(description = "平台ID")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "云盘快照名称")
    private String name;

    @Schema(description = "云盘快照描述")
    private String description;

    @Schema(description = "云主机uuid")
    private String hostUuid;

    @Schema(description = "云主机名称")
    private String hostName;

    @Schema(description = "云盘uuid")
    private String volumeUuid;

    @Schema(description = "云盘名称")
    private String volumeName;

    @Schema(description = "主存储uuid")
    private String primaryStorageUuid;

    @Schema(description = "主存储名称")
    private String primaryStorageName;

    @Schema(description = "云盘快照类型")
    private String type;

    @Schema(description = "云盘类型")
    private String volumeType;

    @Schema(description = "是否最新")
    private String latest;

    @Schema(description = "是否删除，0否，1是")
    private Integer deleted;

    @Schema(description = "云盘快照大小")
    private Long size;

    @Schema(description = "云盘快照状态")
    private String status;

    @Schema(description = "云盘快照创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date vCreateDate;

    @Schema(description = "云盘快照更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date vUpdateDate;

    @Schema(description = "云盘快照安装路径")
    private String installPath;

    @Schema(description = "云盘快照格式")
    private String format;

    @Schema(description = "是否内存快照")
    private Boolean isMemory;
}
