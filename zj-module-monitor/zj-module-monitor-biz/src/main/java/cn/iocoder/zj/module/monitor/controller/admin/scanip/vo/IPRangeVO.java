package cn.iocoder.zj.module.monitor.controller.admin.scanip.vo;

import cn.iocoder.zj.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - IP段更新 Request VO")
@Data
public class IPRangeVO extends PageParam {

    @Schema(description = "IP段编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "租户编号", example = "1")
    private Long tenantId;

    @Schema(description = "是否立即执行", example = "1")
    private Boolean isExecute;

    @Schema(description = "租户名称", example = "芋道源码")
    private String tenantName;

    @Schema(description = "执行周期", example = "60")
    private String taskCycle;

    @Schema(description = "状态", example = "1")
    private Integer status;

    @Schema(description = "SNMP端口", example = "161")
    private Integer snmpPort;

    @Schema(description = "SNMP团体名", example = "public")
    private String snmpCommunity;

    @Schema(description = "SNMP版本", example = "public")
    private String snmpVersion;

    @Schema(description = "TCP端口", example = "22")
    private String tcpPort;

    @Schema(description = "备注", example = "这是一个测试IP段")
    private String remark;

    @Schema(description = "IP段名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "办公网段")
    private String name;

    @Schema(description = "IP段范围，支持单个IP、IP段、IP范围，多个用逗号分隔", requiredMode = Schema.RequiredMode.REQUIRED, example = "***********-*************,***********/24")
    private String ipRanges;

    @Schema(description = "Ping支持", example = "0 - 不支持  1 - 支持")
    private Integer pingSupport;

    @Schema(description = "SNMP支持", example = "0 - 不支持  1 - 支持")
    private Integer snmpSupport;

    @Schema(description = "TCP支持", example = "0 - 不支持  1 - 支持")
    private Integer tcpSupport;

    @Schema(description = "租户ID")
    private List<String> tenantIds;

    @Schema(description = "批量更新id")
    private List<Long> ids;

    @Schema(description = "删除状态")
    private Integer delete;

    @Schema(description = "任务id", example = "60")
    private String taskId;

    @Schema(description = "cron表达式", example = "60")
    private String cronExp;


    @Schema(description = "添加时间", example = "60")
    private LocalDateTime createTime;


    @Schema(description = "修改时间", example = "60")
    private LocalDateTime updateTime;

    private Integer succCount;

    private Integer state;

    private List<Integer> tcps;

    @Schema(description = "平台名称", example = "ss")
    private Long platformId;

    @Schema(description = "平台id", example = "ss")
    private String platformName;

    private String startTime;

    private String endTime;
}
