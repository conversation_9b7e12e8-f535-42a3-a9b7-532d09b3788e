package cn.iocoder.zj.module.monitor.controller.admin.taggables.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 标签绑定关系 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class TaggablesBaseVO {

    @Schema(description = "标签id", required = true)
    @NotNull(message = "标签id不能为空")
    private Long tagId;

    @Schema(description = "绑定资产id ", required = true)
    @NotNull(message = "绑定资产id 不能为空")
    private Long taggableId;

    @Schema(description = "绑定资产类型", required = true)
    @NotNull(message = "绑定资产类型不能为空")
    private String taggableType;

}
