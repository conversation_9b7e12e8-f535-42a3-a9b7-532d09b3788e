package cn.iocoder.zj.module.monitor.controller.admin.tagplan;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.module.monitor.controller.admin.tagplan.vo.*;
import cn.iocoder.zj.module.monitor.convert.tagplan.TagPlanConvert;
import cn.iocoder.zj.module.monitor.dal.dataobject.tagplan.TagPlanDO;
import cn.iocoder.zj.module.monitor.service.tagplan.TagPlanService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

@Tag(name = "管理后台 - 标签计划")
@RestController
@RequestMapping("/monitor/tag-plan")
@Validated
public class TagPlanController {

    @Resource
    private TagPlanService tagPlanService;

    @PostMapping("/create")
    @Operation(summary = "创建标签计划")
    @PreAuthorize("@ss.hasPermission('monitor:tag-plan:create')")
    public CommonResult<Long> createTagPlan(@Valid @RequestBody TagPlanCreateReqVO createReqVO) {
        return success(tagPlanService.createTagPlan(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新标签计划")
    @PreAuthorize("@ss.hasPermission('monitor:tag-plan:update')")
    public CommonResult<Boolean> updateTagPlan(@Valid @RequestBody TagPlanUpdateReqVO updateReqVO) {
        tagPlanService.updateTagPlan(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除标签计划")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('monitor:tag-plan:delete')")
    public CommonResult<Boolean> deleteTagPlan(@RequestParam("id") Long id) {
        tagPlanService.deleteTagPlan(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得标签计划")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('monitor:tag-plan:query')")
    public CommonResult<TagPlanRespVO> getTagPlan(@RequestParam("id") Long id) {
        TagPlanRespVO patrolPlan = tagPlanService.getTagPlan(id);
        return success(patrolPlan);
    }

    @GetMapping("/list")
    @Operation(summary = "获得标签计划列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('monitor:tag-plan:query')")
    public CommonResult<List<TagPlanRespVO>> getTagPlanList(@RequestParam("ids") Collection<Long> ids) {
        List<TagPlanDO> list = tagPlanService.getTagPlanList(ids);
        return success(TagPlanConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得标签计划分页")
    @PreAuthorize("@ss.hasPermission('monitor:tag-plan:query')")
    public CommonResult<PageResult<TagPlanRespVO>> getTagPlanPage(@Valid TagPlanPageReqVO pageVO) {
        PageResult<TagPlanRespVO> pageResult = tagPlanService.getTagPlanPage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出标签计划 Excel")
    @PreAuthorize("@ss.hasPermission('monitor:tag-plan:export')")
    @OperateLog(type = EXPORT)
    public void exportTagPlanExcel(@Valid TagPlanExportReqVO exportReqVO,
                                      HttpServletResponse response) throws IOException {
        List<TagPlanDO> list = tagPlanService.getTagPlanList(exportReqVO);
        // 导出 Excel
        List<TagPlanExcelVO> datas = TagPlanConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "标签计划.xls", "数据", TagPlanExcelVO.class, datas);
    }

    @PostMapping("/execute")
    @PreAuthorize("@ss.hasPermission('monitor:tag-plan:execute')")
    @Operation(summary = "执行标签计划")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<Boolean> executeTagPlan(@RequestParam("id") Long id) {
        tagPlanService.executeTagPlan(id);
        return success(true);
    }

    @PostMapping("/startOrStop")
    @PreAuthorize("@ss.hasPermission('monitor:tag-plan:start')")
    @Operation(summary = "启动或停止标签计划")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @Parameter(name = "status", description = "状态", required = true, example = "1")
    public CommonResult<Boolean> startOrStopTagPlan(@RequestParam("id") Long id, @RequestParam("status") int status) {
        tagPlanService.startOrStopTagPlan(id, status);
        return success(true);
    }


    @PostMapping("/addRecord")
    @Operation(summary = "创建报告记录")
    public CommonResult<Long> createPatrolRecord(@Valid @RequestBody TagRecordCreateReqVO createReqVO) {
        return success(tagPlanService.createTagRecord(createReqVO));
    }

    @GetMapping("/pageRecord")
    @Operation(summary = "获得巡检记录分页")
    public CommonResult<PageResult<TagRecordRespVO>> getPatrolRecordPage(@Valid TagRecordPageReqVO pageVO) {
        return success(tagPlanService.getTagRecordPage(pageVO));
    }

    @GetMapping("/uploadWord")
    @Operation(summary = "巡检报告下载")
    public void uploadWord(@RequestParam("id") Long id, HttpServletResponse response) {
        tagPlanService.uploadWord(id, response);
    }

    @GetMapping("/getRun")
    @Operation(summary = "巡检报告下载")
    public void runTagTask(@RequestParam("id") Long id) {
        TagPlanDO patrolPlanDO = tagPlanService.getTagPlanByJobId(id);
        tagPlanService.runTagTask(patrolPlanDO);
    }
}
