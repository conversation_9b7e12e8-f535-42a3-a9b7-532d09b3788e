package cn.iocoder.zj.module.monitor.dal.dataobject.gatherasset;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;

import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 租户资产 DO
 *
 * <AUTHOR>
 */
@TableName("monitor_gather_asset")
@KeySequence("monitor_gather_asset_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GatherAssetDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 租户绑定的采集设备id
     */
    private String uuid;
    /**
     * 访问设备的ip地址 (例：udp:*************)
     */
    private String ip;
    /**
     * snmp设置的团体名
     */
    private String community;
    /**
     * snmp版本 (0=v1; 1=v2c; 2=v3)
     */
    private Integer version;
    /**
     * snmp 端口号
     */
    private Integer snmpPort;
    /**
     * 主机名称
     */
    private String hostName;


    private String platformName;

    private String platformId;

    private String alarmLocation;

    private String dictIcon;
    /**
     * @description: 资产类型
     * <AUTHOR>
     * @date 2023/8/14 15:49
     * @version 1.0
     */
    private String deviceType;
    /**
     * @description: 资产类型名称
     * <AUTHOR>
     * @date 2023/8/14 15:49
     * @version 1.0
     */
    private String deviceName;
    /**
     * @description: 系统类型
     * <AUTHOR>
     * @date 2023/8/14 15:49
     * @version 1.0
     */
    private String sysType;
    /**
     * @description: 系统类型名称
     * <AUTHOR>
     * @date 2023/8/14 15:49
     * @version 1.0
     */
    private String sysName;


    /**
     * @description: 在线状态，0 离线 ，1在线
     * <AUTHOR>
     * @date 2023/8/14 15:52
     * @version 1.0
     */
    private Integer onlineType;

    /**
     * cpu 使用率
     */
    private Double cpuUsage;

    /**
     * 内存使用率
     */
    private Double memoryUsage;


    /**
     * 磁盘使用率
     */
    private Double diskUsage;
}
