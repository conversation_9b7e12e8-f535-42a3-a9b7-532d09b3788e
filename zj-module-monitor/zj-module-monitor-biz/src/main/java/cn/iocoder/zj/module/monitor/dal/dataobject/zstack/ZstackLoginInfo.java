package cn.iocoder.zj.module.monitor.dal.dataobject.zstack;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @ClassName : ZstackLoginInfo  //类名
 * @Description : 登录获取实体类  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/24  12:11
 */

@Data
public class ZstackLoginInfo {

    private String uuid;
    private String tenanName;
    private Long  tenantId;
    private String regionName;
    private Long regionId;

    private String url;
    private String accountUuid;
    private String userUuid;
    private LocalDateTime expiredDate;
    private LocalDateTime createDate;
}
