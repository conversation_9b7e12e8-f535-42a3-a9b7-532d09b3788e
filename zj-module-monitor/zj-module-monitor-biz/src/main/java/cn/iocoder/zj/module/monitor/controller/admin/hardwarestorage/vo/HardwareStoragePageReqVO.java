package cn.iocoder.zj.module.monitor.controller.admin.hardwarestorage.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 宿主机与存储关联分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HardwareStoragePageReqVO extends PageParam {

    @Schema(description = "宿主机id")
    private Long hardwareId;

    @Schema(description = "宿主机uuid")
    private String hardwareUuid;

    @Schema(description = "主存储id")
    private Long storageId;

    @Schema(description = "主存储uuid")
    private String storageUuid;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

}
