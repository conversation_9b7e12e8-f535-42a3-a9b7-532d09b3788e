package cn.iocoder.zj.module.monitor.controller.admin.networkl2;

import cn.iocoder.zj.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.monitor.controller.admin.networkl2.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.networkl2.NetworkL2DO;
import cn.iocoder.zj.module.monitor.convert.networkl2.NetworkL2Convert;
import cn.iocoder.zj.module.monitor.service.networkl2.NetworkL2Service;

@Tag(name = "管理后台 - 二级网络信息")
@RestController
@RequestMapping("/monitor/network-l2")
@Validated
public class NetworkL2Controller {

    @Resource
    private NetworkL2Service networkL2Service;

    @PostMapping("/create")
    @Operation(summary = "创建二级网络信息")
    @PreAuthenticated
    @PreAuthorize("@ss.hasPermission('monitor:network-l2:create')")
    @OperateLog(type = CREATE)
    public CommonResult<Long> createNetworkL2(@Valid @RequestBody NetworkL2CreateReqVO createReqVO) {
        return success(networkL2Service.createNetworkL2(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新二级网络信息")
    @PreAuthenticated
    @PreAuthorize("@ss.hasPermission('monitor:network-l2:update')")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateNetworkL2(@Valid @RequestBody NetworkL2UpdateReqVO updateReqVO) {
        networkL2Service.updateNetworkL2(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除二级网络信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthenticated
    @PreAuthorize("@ss.hasPermission('monitor:network-l2:delete')")
    @OperateLog(type = DELETE)
    public CommonResult<Boolean> deleteNetworkL2(@RequestParam("id") Long id) {
        networkL2Service.deleteNetworkL2(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得二级网络信息")
    @Parameter(name = "id", description = "编号", required = false, example = "1024")
    @Parameter(name = "uuid", description = "UUID", required = false, example = "1024")
    @PreAuthenticated
    public CommonResult<NetworkL2RespVO> getNetworkL2( Long id, String uuid) {
        NetworkL2DO networkL2 = networkL2Service.getNetworkL2(id,uuid);
        return success(NetworkL2Convert.INSTANCE.convert(networkL2));
    }

    @GetMapping("/list")
    @Operation(summary = "获得二级网络信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthenticated
    public CommonResult<List<NetworkL2RespVO>> getNetworkL2List(@RequestParam("ids") Collection<Long> ids) {
        List<NetworkL2DO> list = networkL2Service.getNetworkL2List(ids);
        return success(NetworkL2Convert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得二级网络信息分页")
    @PreAuthenticated
    @TenantIgnore
    public CommonResult<PageResult<NetworkL2RespVO>> getNetworkL2Page(@Valid NetworkL2PageReqVO pageVO) {
        PageResult<NetworkL2DO> pageResult = networkL2Service.getNetworkL2Page(pageVO);
        return success(NetworkL2Convert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出二级网络信息 Excel")
    @PreAuthenticated
    @OperateLog(type = EXPORT)
    public void exportNetworkL2Excel(@Valid NetworkL2ExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<NetworkL2DO> list = networkL2Service.getNetworkL2List(exportReqVO);
        // 导出 Excel
        List<NetworkL2ExcelVO> datas = NetworkL2Convert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "二级网络信息.xls", "数据", NetworkL2ExcelVO.class, datas);
    }

}
