package cn.iocoder.zj.module.monitor.service.tags;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import cn.iocoder.zj.framework.test.core.ut.BaseDbUnitTest;

import cn.iocoder.zj.module.monitor.controller.admin.tags.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.tags.TagsDO;
import cn.iocoder.zj.module.monitor.dal.mysql.tags.TagsMapper;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.*;
import static cn.iocoder.zj.framework.test.core.util.AssertUtils.*;
import static cn.iocoder.zj.framework.test.core.util.RandomUtils.*;
import static cn.iocoder.zj.framework.common.util.date.LocalDateTimeUtils.*;
import static cn.iocoder.zj.framework.common.util.object.ObjectUtils.*;
import static cn.iocoder.zj.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
* {@link TagsServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(TagsServiceImpl.class)
public class TagsServiceImplTest extends BaseDbUnitTest {

    @Resource
    private TagsServiceImpl tagsService;

    @Resource
    private TagsMapper tagsMapper;

    @Test
    public void testCreateTags_success() {
        // 准备参数
        TagsCreateReqVO reqVO = randomPojo(TagsCreateReqVO.class);

        // 调用
        Long tagsId = tagsService.createTags(reqVO);
        // 断言
        assertNotNull(tagsId);
        // 校验记录的属性是否正确
        TagsDO tags = tagsMapper.selectById(tagsId);
        assertPojoEquals(reqVO, tags);
    }

    @Test
    public void testUpdateTags_success() {
        // mock 数据
        TagsDO dbTags = randomPojo(TagsDO.class);
        tagsMapper.insert(dbTags);// @Sql: 先插入出一条存在的数据
        // 准备参数
        TagsUpdateReqVO reqVO = randomPojo(TagsUpdateReqVO.class, o -> {
            o.setId(dbTags.getId()); // 设置更新的 ID
        });

        // 调用
        tagsService.updateTags(reqVO);
        // 校验是否更新正确
        TagsDO tags = tagsMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, tags);
    }

    @Test
    public void testUpdateTags_notExists() {
        // 准备参数
        TagsUpdateReqVO reqVO = randomPojo(TagsUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> tagsService.updateTags(reqVO), TAGS_NOT_EXISTS);
    }

    @Test
    public void testDeleteTags_success() {
        // mock 数据
        TagsDO dbTags = randomPojo(TagsDO.class);
        tagsMapper.insert(dbTags);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbTags.getId();

        // 调用
        tagsService.deleteTags(id);
       // 校验数据不存在了
       assertNull(tagsMapper.selectById(id));
    }

    @Test
    public void testDeleteTags_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> tagsService.deleteTags(id), TAGS_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetTagsPage() {
       // mock 数据
       TagsDO dbTags = randomPojo(TagsDO.class, o -> { // 等会查询到
           o.setName(null);
           o.setRemark(null);
           o.setCreateTime(null);
       });
       tagsMapper.insert(dbTags);
       // 测试 name 不匹配
       tagsMapper.insert(cloneIgnoreId(dbTags, o -> o.setName(null)));
       // 测试 remark 不匹配
       tagsMapper.insert(cloneIgnoreId(dbTags, o -> o.setRemark(null)));
       // 测试 createTime 不匹配
       tagsMapper.insert(cloneIgnoreId(dbTags, o -> o.setCreateTime(null)));
       // 准备参数
       TagsPageReqVO reqVO = new TagsPageReqVO();
       reqVO.setName(null);
       reqVO.setRemark(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<TagsDO> pageResult = tagsService.getTagsPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbTags, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetTagsList() {
       // mock 数据
       TagsDO dbTags = randomPojo(TagsDO.class, o -> { // 等会查询到
           o.setName(null);
           o.setRemark(null);
           o.setCreateTime(null);
       });
       tagsMapper.insert(dbTags);
       // 测试 name 不匹配
       tagsMapper.insert(cloneIgnoreId(dbTags, o -> o.setName(null)));
       // 测试 remark 不匹配
       tagsMapper.insert(cloneIgnoreId(dbTags, o -> o.setRemark(null)));
       // 测试 createTime 不匹配
       tagsMapper.insert(cloneIgnoreId(dbTags, o -> o.setCreateTime(null)));
       // 准备参数
       TagsExportReqVO reqVO = new TagsExportReqVO();
       reqVO.setName(null);
       reqVO.setRemark(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<TagsDO> list = tagsService.getTagsList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbTags, list.get(0));
    }

}
