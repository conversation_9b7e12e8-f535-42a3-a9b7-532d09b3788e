package cn.iocoder.zj.module.monitor.service.taggables;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import cn.iocoder.zj.framework.test.core.ut.BaseDbUnitTest;

import cn.iocoder.zj.module.monitor.controller.admin.taggables.vo.*;
import cn.iocoder.zj.module.monitor.dal.dataobject.taggables.TaggablesDO;
import cn.iocoder.zj.module.monitor.dal.mysql.taggables.TaggablesMapper;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static cn.iocoder.zj.module.monitor.enums.ErrorCodeConstants.*;
import static cn.iocoder.zj.framework.test.core.util.AssertUtils.*;
import static cn.iocoder.zj.framework.test.core.util.RandomUtils.*;
import static cn.iocoder.zj.framework.common.util.date.LocalDateTimeUtils.*;
import static cn.iocoder.zj.framework.common.util.object.ObjectUtils.*;
import static cn.iocoder.zj.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
* {@link TaggablesServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(TaggablesServiceImpl.class)
public class TaggablesServiceImplTest extends BaseDbUnitTest {

    @Resource
    private TaggablesServiceImpl taggablesService;

    @Resource
    private TaggablesMapper taggablesMapper;

    @Test
    public void testCreateTaggables_success() {
        // 准备参数
        TaggablesCreateReqVO reqVO = randomPojo(TaggablesCreateReqVO.class);

        // 调用
        Long taggablesId = taggablesService.createTaggables(reqVO);
        // 断言
        assertNotNull(taggablesId);
        // 校验记录的属性是否正确
        TaggablesDO taggables = taggablesMapper.selectById(taggablesId);
        assertPojoEquals(reqVO, taggables);
    }

    @Test
    public void testUpdateTaggables_success() {
        // mock 数据
        TaggablesDO dbTaggables = randomPojo(TaggablesDO.class);
        taggablesMapper.insert(dbTaggables);// @Sql: 先插入出一条存在的数据
        // 准备参数
        TaggablesUpdateReqVO reqVO = randomPojo(TaggablesUpdateReqVO.class, o -> {
            o.setId(dbTaggables.getId()); // 设置更新的 ID
        });

        // 调用
        taggablesService.updateTaggables(reqVO);
        // 校验是否更新正确
        TaggablesDO taggables = taggablesMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, taggables);
    }

    @Test
    public void testUpdateTaggables_notExists() {
        // 准备参数
        TaggablesUpdateReqVO reqVO = randomPojo(TaggablesUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> taggablesService.updateTaggables(reqVO), TAGGABLES_NOT_EXISTS);
    }

    @Test
    public void testDeleteTaggables_success() {
        // mock 数据
        TaggablesDO dbTaggables = randomPojo(TaggablesDO.class);
        taggablesMapper.insert(dbTaggables);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbTaggables.getId();

        // 调用
        taggablesService.deleteTaggables(id);
       // 校验数据不存在了
       assertNull(taggablesMapper.selectById(id));
    }

    @Test
    public void testDeleteTaggables_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> taggablesService.deleteTaggables(id), TAGGABLES_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetTaggablesPage() {
       // mock 数据
       TaggablesDO dbTaggables = randomPojo(TaggablesDO.class, o -> { // 等会查询到
           o.setTagId(null);
           o.setTaggableId(null);
           o.setTaggableType(null);
           o.setCreateTime(null);
       });
       taggablesMapper.insert(dbTaggables);
       // 测试 tagId 不匹配
       taggablesMapper.insert(cloneIgnoreId(dbTaggables, o -> o.setTagId(null)));
       // 测试 taggableId 不匹配
       taggablesMapper.insert(cloneIgnoreId(dbTaggables, o -> o.setTaggableId(null)));
       // 测试 taggableType 不匹配
       taggablesMapper.insert(cloneIgnoreId(dbTaggables, o -> o.setTaggableType(null)));
       // 测试 createTime 不匹配
       taggablesMapper.insert(cloneIgnoreId(dbTaggables, o -> o.setCreateTime(null)));
       // 准备参数
       TaggablesPageReqVO reqVO = new TaggablesPageReqVO();
       reqVO.setTagId(null);
       reqVO.setTaggableId(null);
       reqVO.setTaggableType(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<TaggablesDO> pageResult = taggablesService.getTaggablesPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbTaggables, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetTaggablesList() {
       // mock 数据
       TaggablesDO dbTaggables = randomPojo(TaggablesDO.class, o -> { // 等会查询到
           o.setTagId(null);
           o.setTaggableId(null);
           o.setTaggableType(null);
           o.setCreateTime(null);
       });
       taggablesMapper.insert(dbTaggables);
       // 测试 tagId 不匹配
       taggablesMapper.insert(cloneIgnoreId(dbTaggables, o -> o.setTagId(null)));
       // 测试 taggableId 不匹配
       taggablesMapper.insert(cloneIgnoreId(dbTaggables, o -> o.setTaggableId(null)));
       // 测试 taggableType 不匹配
       taggablesMapper.insert(cloneIgnoreId(dbTaggables, o -> o.setTaggableType(null)));
       // 测试 createTime 不匹配
       taggablesMapper.insert(cloneIgnoreId(dbTaggables, o -> o.setCreateTime(null)));
       // 准备参数
       TaggablesExportReqVO reqVO = new TaggablesExportReqVO();
       reqVO.setTagId(null);
       reqVO.setTaggableId(null);
       reqVO.setTaggableType(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<TaggablesDO> list = taggablesService.getTaggablesList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbTaggables, list.get(0));
    }

}
