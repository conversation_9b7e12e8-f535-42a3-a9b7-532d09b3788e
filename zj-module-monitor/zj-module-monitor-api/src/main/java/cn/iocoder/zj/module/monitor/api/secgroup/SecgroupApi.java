package cn.iocoder.zj.module.monitor.api.secgroup;


import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.secgroup.dto.SecgroupCreateReqDto;
import cn.iocoder.zj.module.monitor.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME) // ① @FeignClient 注解
@Tag(name = "RPC 服务 - 安全组相关") // ② Swagger 接口文档
public interface SecgroupApi {

    String PREFIX = ApiConstants.PREFIX + "/zstack";

    @PostMapping(PREFIX + "/addSecgroups")
    @Operation(summary = "批量新增安全组")
        // ② Swagger 接口文档
    CommonResult<Boolean> addSecgroups(@RequestBody List<SecgroupCreateReqDto> reqDTO);

    @PostMapping(PREFIX + "/updateSecgroups") // ③ Spring MVC 接口注解
    @Operation(summary = "更新安全组信息")  // ② Swagger 接口文档
    CommonResult<Boolean> updateSecgroups(@RequestBody List<SecgroupCreateReqDto> secgroupCreateReqDtoList);

    @PostMapping(PREFIX + "/delSecgroups")
    @Operation(summary = "删除安全组信息")
    CommonResult<Boolean> delSecgroups(@RequestBody List<SecgroupCreateReqDto> secgroupCreateReqDtoList);

    @PostMapping(PREFIX + "/getSecgroupsByPlatformId")
    @Operation(summary = "查询安全组信息")
    CommonResult<List<SecgroupCreateReqDto>> getSecgroupsByPlatformId(@RequestParam("platformId") Long platformId);

}
