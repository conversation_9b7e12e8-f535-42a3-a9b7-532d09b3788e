package cn.iocoder.zj.module.monitor.api.hardware.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @ClassName : HardWareRespDTO  //类名
 * @Description : 硬件设施实体类  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/6/5  17:44
 */

@Schema(description = "RPC 服务 - Zstack 访问硬件设施的信息 Response DTO")
@Data
@Accessors(chain = true)
public class HardWareRespDTO {

    /**
     * 主键
     */
    private Long id;
    /**
     * 主机id
     */
    private String uuid;
    /**
     * 主机名称
     */
    private String name;
    /**
     * 宿主机状态，包括：	Enabled	Disabled	PreMaintenance	Maintenance
     */
    private String state;
    /**
     * ip
     */
    private String ip;
    /**
     * Connecting，	Connected，	Disconnected
     */
    private String status;
    /**
     * 集群id
     */
    private String clusterUuid;
    /**
     * 集群名称
     */
    private String clusterName;
    /**
     * cpu总容量
     */
    private Long totalCpuCapacity;
    /**
     * cpu可用容量
     */
    private Long availableCpuCapacity;
    /**
     * cpu 插槽
     */
    private Integer cpuSockets;
    /**
     * CPU架构
     */
    private String architecture;
    /**
     * 分配cpu
     */
    private Integer cpuNum;
    /**
     * 内存总容量
     */
    private Long totalMemoryCapacity;
    /**
     * 内存可用容量
     */
    private Long availableMemoryCapacity;
    /**
     * 地区id
     */
    private Long regionId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 带宽上行速率
     */
    private BigDecimal bandwidthUpstream;
    /**
     * 带宽下行速率
     */
    private BigDecimal bandwidthDownstream;
    /**
     * 内存使用率
     */
    private BigDecimal memoryUsed;
    /**
     * cpu 使用率
     */
    private BigDecimal cpuUsed;
    /**
     * 收包速率
     */
    private BigDecimal packetRate;

    private BigDecimal diskUsed;

    private BigDecimal diskUsedBytes;

    private BigDecimal diskFreeBytes;

    private BigDecimal totalDiskCapacity;

    // 平台配置id
    private Long platformId;
    // 平台配置名称
    private String platformName;
}
