package cn.iocoder.zj.module.monitor.api.valume;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeAttachableVmDTO;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeDTO;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeSnapshotDTO;
import cn.iocoder.zj.module.monitor.enums.ApiConstants;
import cn.iocoder.zj.module.monitor.pojo.AssetReqVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME) // ① @FeignClient 注解
@Tag(name = "RPC 服务 - 云盘相关") // ② Swagger 接口文档
public interface VolumeApi {

    String PREFIX = ApiConstants.PREFIX + "/volume";

    //云盘
    @PostMapping(PREFIX + "/addVolumes") // ③ Spring MVC 接口注解
    @Operation(summary = "批量新增云盘信息")  // ② Swagger 接口文档
    CommonResult<Boolean> addVolumes(@RequestBody List<VolumeDTO> volumeDTOList);

    @PostMapping(PREFIX + "/getVolumeCount") // ③ Spring MVC 接口注解
    @Operation(summary = "获取云盘数量")  // ② Swagger 接口文档
    CommonResult<Long> getVolumeCount();

    @PostMapping(PREFIX + "/updateVolumes") // ③ Spring MVC 接口注解
    @Operation(summary = "获取更新云盘信息")  // ② Swagger 接口文档
    CommonResult<Boolean> updateVolumes(@RequestBody List<VolumeDTO> volumeDTOList);

    @PostMapping(PREFIX + "/getAllVolumes") // ③ Spring MVC 接口注解
    @Operation(summary = "获取云盘列表")  // ② Swagger 接口文档
    CommonResult<List<VolumeDTO>> getAllVolumes(@RequestParam("id") Long id,@RequestParam("typeCode")String typeCode);


    //云盘快照
    @PostMapping(PREFIX + "/getVolumeSnapshotCount") // ③ Spring MVC 接口注解
    @Operation(summary = "获取云盘快照数量")  // ② Swagger 接口文档
    CommonResult<Long> getVolumeSnapshotCount(@RequestParam("typeName")String typeName);

    @PostMapping(PREFIX + "/getAllVolumeSnapshots") // ③ Spring MVC 接口注解
    @Operation(summary = "获取云盘快照列表")
    CommonResult<List<VolumeSnapshotDTO>>  getAllVolumeSnapshots(@RequestParam("typeName")String typeName);

    @PostMapping(PREFIX + "/updateVolumeSnapshots") // ③ Spring MVC 接口注解
    @Operation(summary = "获取更新云盘快照信息")  // ② Swagger 接口文档
    CommonResult<Boolean> updateVolumeSnapshots(@RequestBody List<VolumeSnapshotDTO> volumeSnapshotDTOList);

    @PostMapping(PREFIX + "/addVolumeSnapshots") // ③ Spring MVC 接口注解
    @Operation(summary = "批量新增云盘快照信息")  // ② Swagger 接口文档
    CommonResult<Boolean> addVolumeSnapshots(@RequestBody List<VolumeSnapshotDTO> volumeSnapshotDTOList);
    @PostMapping(PREFIX + "/updateSingleVolumes") // ③ Spring MVC 接口注解
    @Operation(summary = "更新单个云盘信息")  // ② Swagger 接口文档
    CommonResult<Boolean> updateSingleVolumes(@RequestBody VolumeDTO volumeDTO);
    @PostMapping(PREFIX + "/getAttachableVmCount") // ③ Spring MVC 接口注解
    @Operation(summary = "获取可挂载云盘数量")  // ② Swagger 接口文档
    CommonResult<Long> getAttachableVmCount();
    @PostMapping(PREFIX + "/getAllVolumeAttachableVm") // ③ Spring MVC 接口注解
    @Operation(summary = "获取可挂载云盘列表")
    CommonResult<List<VolumeAttachableVmDTO>> getAllVolumeAttachableVm();
    @PostMapping(PREFIX + "/addVolumeAttachableVms") // ③ Spring MVC 接口注解
    @Operation(summary = "批量新增可挂载云盘信息")
    CommonResult<Boolean> addVolumeAttachableVms(@RequestBody List<VolumeAttachableVmDTO> shardingData);

    @PostMapping(PREFIX + "/updateVolumeAttachableVms") // ③ Spring MVC 接口注解
    @Operation(summary = "批量更新可挂载云盘信息")
    CommonResult<Boolean> updateVolumeAttachableVms(@RequestBody List<VolumeAttachableVmDTO> shardingData);
    @PostMapping(PREFIX + "/delVolumeAttachableVms") // ③ Spring MVC 接口注解
    @Operation(summary = "批量删除可挂载云盘信息")
    CommonResult<Boolean> delVolumeAttachableVms(@RequestBody List<VolumeAttachableVmDTO> deleteTarget);
    @PostMapping(PREFIX + "/delVolumes") // ③ Spring MVC 接口注解
    @Operation(summary = "批量删除云盘信息")
    CommonResult<Boolean> delVolumes(@RequestBody List<VolumeDTO> deleteTarget);
    @PostMapping(PREFIX + "/delVolumeSnapshots") // ③ Spring MVC 接口注解
    @Operation(summary = "批量删除云盘快照信息")
    CommonResult<Boolean> delVolumeSnapshots(@RequestBody List<VolumeSnapshotDTO> deleteTarget);

    @PostMapping(PREFIX + "/delVolumeByInstanceUuid") // ③ Spring MVC 接口注解
    @Operation(summary = "根据云主机uuid批量删除磁盘数据")
    CommonResult<Boolean> delVolumeByInstanceUuid(@RequestBody List<String> deleteList);

    @PostMapping(PREFIX + "/getVolumeByPlatformId") // ③ Spring MVC 接口注解
    @Operation(summary = "获取云盘列表")  // ② Swagger 接口文档
    List<VolumeDTO> getVolumeByPlatformId(@RequestParam("id") Long id);

    @PostMapping(PREFIX + "/getVolumeByVmUuid") // ③ Spring MVC 接口注解
    @Operation(summary = "根据云主机id获取根云盘uuid")
    CommonResult<String> getVolumeByVmUuid(@RequestParam("domainId")String domainId);

    @PostMapping(PREFIX + "/getVolumesByPlatformId") // ③ Spring MVC 接口注解
    @Operation(summary = "获取云盘列表")
    List<VolumeDTO> getVolumesByPlatformId(@RequestParam("id")Long id);

    @PostMapping(PREFIX + "/getVolumeSnapshotByPlatformId")
    @Operation(summary = "根据平台Id获取云盘快照列表")
    List<VolumeSnapshotDTO> getVolumeSnapshotByPlatformId(@RequestParam("id")Long id);

    @PostMapping(PREFIX + "/getVolumeByTenantOrPlatforms")
    @Operation(summary = "根据租户Id获取云盘列表")
    CommonResult<List<VolumeDTO>> getVolumeByTenantOrPlatforms(@RequestBody AssetReqVO assetReqVO);

    @PostMapping(PREFIX + "/getByPlatformIdAndTags")
    @Operation(summary = "根据租户Id获取云盘列表")
    CommonResult<List<VolumeDTO>> getByPlatformIdAndTags(@RequestBody AssetReqVO reqVO);
}
