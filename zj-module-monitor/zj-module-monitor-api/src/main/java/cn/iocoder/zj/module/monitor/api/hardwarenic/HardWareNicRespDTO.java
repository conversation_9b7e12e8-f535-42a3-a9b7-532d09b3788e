package cn.iocoder.zj.module.monitor.api.hardwarenic;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "RPC 服务 - 创建硬件网络设施信息 Request DTO")
@Data
public class HardWareNicRespDTO {
    /**
     * 主键
     */
    private Long id;
    /**
     * 网卡uuid(v3)
     */
    private String uuid;
    /**
     * 宿主机uuid
     */
    private String hardwareUuid;
    /**
     * mac
     */
    private String mac;
    /**
     * 网卡类型
     */
    private String networkType;
    /**
     * ip地址
     */
    private String ipAddresses;
    /**
     * ip子网
     */
    private String ipSubnet;
    /**
     * 二层网络uuid
     */
    private String l2NetworkUuid;
    /**
     * 二层网络名称
     */
    private String l2NetworkName;
    /**
     * 是否离线
     */
    private Boolean state;


    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;



}
