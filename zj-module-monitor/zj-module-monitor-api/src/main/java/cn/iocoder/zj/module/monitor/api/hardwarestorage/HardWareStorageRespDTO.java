package cn.iocoder.zj.module.monitor.api.hardwarestorage;

import lombok.Data;

@Data
public class HardWareStorageRespDTO {

    private Long id;
    /**
     * 宿主机id
     */
    private Long hardwareId;
    /**
     * 宿主机uuid
     */
    private String hardwareUuid;
    /**
     * 主存储id
     */
    private Long storageId;
    /**
     * 主存储uuid
     */
    private String storageUuid;

    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;
}
