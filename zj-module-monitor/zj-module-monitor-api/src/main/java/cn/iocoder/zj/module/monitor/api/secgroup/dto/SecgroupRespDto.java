package cn.iocoder.zj.module.monitor.api.secgroup.dto;

import cn.iocoder.zj.framework.common.dal.manager.SecGroupRuleData;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class SecgroupRespDto {
    /**
     * id
     */
    private Long id;
    /**
     * uuid
     */
    private String uuid;
    /**
     * 名称
     */
    private String name;
    /**
     * 状态
     */
    private String status;
    /**
     * 是否共享
     */
    private Boolean isPublic;
    /**
     * 默认共享范围
     */
    private String publicScope;
    /**
     * 共享设置的来源, local: 本地设置, cloud: 从云上同步过来
     */
    private String publicSrc;
    /**
     * 是否垃圾
     */
    private Boolean isDirty;
    /**
     * 区域id
     */
    private String cloudregionId;
    /**
     * vpcId
     */
    private String vpcId;
    /**
     * 全局vpcId
     */
    private String globalvpcId;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建者，目前使用 SysUser 的 id 编号
     * <p>
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    private String creator;
    /**
     * 更新者，目前使用 SysUser 的 id 编号
     * <p>
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    private String updater;
    private String description;


    //云主机id
    private String hostUuid;

    //安全组入规则
    private List<SecGroupRuleData> inRules;

    //安全组出规则
    private List<SecGroupRuleData> outRules;

}
