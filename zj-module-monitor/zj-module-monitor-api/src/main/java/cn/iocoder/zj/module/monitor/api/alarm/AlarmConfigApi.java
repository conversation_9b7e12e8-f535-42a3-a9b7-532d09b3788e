package cn.iocoder.zj.module.monitor.api.alarm;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmConfigRespDTO;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmInfoDTO;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmRecordDTO;
import cn.iocoder.zj.module.monitor.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.hertzbeat.common.entity.alerter.Alert;
import org.apache.hertzbeat.common.entity.alerter.AlertConverge;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@FeignClient(name = ApiConstants.NAME) // ① @FeignClient 注解
@Tag(name = "RPC 服务 - 告警配置相关") // ② Swagger 接口文档
public interface AlarmConfigApi {
    String PREFIX = ApiConstants.PREFIX + "/zstack";

    @PostMapping(PREFIX + "/alarmConfigList") // ③ Spring MVC 接口注解
    @Operation(summary = "获取管理员的告警配置")  // ② Swagger 接口文档
    CommonResult<List<AlarmConfigRespDTO>> list(@Valid  @RequestParam("tenantId") Long tenantId);
    @PostMapping(PREFIX + "/insertBatch") // ③ Spring MVC 接口注解
    @Operation(summary = "分配告警配置给新租户")  // ② Swagger 接口文档
    void insertBatch(@Valid @RequestBody List<AlarmConfigRespDTO> toInsertAlarmConfig);

    @GetMapping(PREFIX + "/getAlarmNotice") // ③ Spring MVC 接口注解
    @Operation(summary = "按告警配置ID获取短信通知的模板信息")  // ② Swagger 接口文档
    CommonResult<Map<String,String>> getAlarmNotice(@Valid @RequestParam("configId") Long configId);

    @PostMapping(PREFIX + "/getSilentTarget") // ③ Spring MVC 接口注解
    @Operation(summary = "获取告警收敛配置")  // ② Swagger 接口文档
    CommonResult<Map<Long, List<AlarmDorisReqDTO>>> getSilentTarget(@RequestBody AlertConverge converge);

    @PostMapping(PREFIX + "/solvedAlarm") // ③ Spring MVC 接口注解
    @Operation(summary = "处理告警信息")  // ② Swagger 接口文档
    CommonResult<Boolean> solvedAlarm(@RequestParam("alarmId") String alarmId);

    @PostMapping(PREFIX + "/alarmWorkOrder") // ③ Spring MVC 接口注解
    @Operation(summary = "告警信息创建工单")  // ② Swagger 接口文档
    CommonResult<Boolean> alarmWorkOrder(@RequestParam("alarmId") String alarmId);

    @PostMapping(PREFIX + "/cleanWorkOrder") // ③ Spring MVC 接口注解
    @Operation(summary = "告警信息工单被删除")  // ② Swagger 接口文档
    CommonResult<Boolean> cleanWorkOrder(@RequestParam("alarmId") String alarmId);

    @PostMapping(PREFIX + "/solvedGather") // ③ Spring MVC 接口注解
    @Operation(summary = "处理告警信息")  // ② Swagger 接口文档
    CommonResult<Boolean> solvedGather(@RequestParam("gatherId") String gatherId);

    @PostMapping(PREFIX + "/gatherWorkOrder") // ③ Spring MVC 接口注解
    @Operation(summary = "告警信息创建工单")  // ② Swagger 接口文档
    CommonResult<Boolean> gatherWorkOrder(@RequestParam("gatherId") String gatherId);

    @PostMapping(PREFIX + "/cleanGatherWorkOrder") // ③ Spring MVC 接口注解
    @Operation(summary = "告警信息工单被删除")  // ② Swagger 接口文档
    CommonResult<Boolean> cleanGatherWorkOrder(@RequestParam("gatherId") String gatherId);
    @PostMapping(PREFIX + "/stateChangeAlarm") // ③ Spring MVC 接口注解
    @Operation(summary = "资源状态变更告警")  // ② Swagger 接口文档
    CommonResult<Boolean> stateChangeAlarm(@Valid @RequestBody List<AlarmRecordDTO> alarmRecordDTOS);

    @PostMapping(PREFIX + "/changeAlarmSolvedState") // ③ Spring MVC 接口注解
    @Operation(summary = "非云平台告警信息变更")// ② Swagger 接口文档
    CommonResult<Boolean> changeAlarmSolvedState(@RequestParam("alertId")Long alertId,
                                                 @RequestParam("isSolved")Integer isSolved);

    @PostMapping(PREFIX + "/createAlarmToDoris") // ③ Spring MVC 接口注解
    @Operation(summary = "创建告警信息到Doris")  // ② Swagger 接口文档
    CommonResult<Boolean> createAlarmToDoris(@Valid @RequestBody Map<String,List> updateAndInsert);
    @PostMapping(PREFIX + "/updateAlarmToDoris") // ③ Spring MVC 接口注解
    @Operation(summary = "更新告警信息到Doris")  // ② Swagger 接口文档
    CommonResult<Boolean> updateAlarmDoris(@Valid @RequestBody List<AlarmDorisReqDTO> updateAndInsert);

    @PostMapping(PREFIX + "/getAvailableAlertConverge") // ③ Spring MVC 接口注解
    @Operation(summary = "获得告警收敛全局配置")  // ② Swagger 接口文档
    CommonResult<AlertConverge> getAvailableAlertConverge();
    @GetMapping(PREFIX + "/getMaxAlertId") // ③ Spring MVC 接口注解
    @Operation(summary = "获取当前告警的最大ID值")
    CommonResult<Long> getMaxAlertId();

    @PostMapping(PREFIX + "/createCollectorAlarm") // ③ Spring MVC 接口注解
    @Operation(summary = "创建采集器状态变更告警")  // ② Swagger 接口文档
    CommonResult<Boolean> createCollectorAlert(@RequestBody Map<String,Object> mapList);
    @PostMapping(PREFIX + "/deletedCollectorAlert") // ③ Spring MVC 接口注解
    @Operation(summary = "创建采集器状态变更告警")  // ② Swagger 接口文档
    CommonResult<Boolean> deletedCollectorAlert(@RequestBody Map<String,Object> mapList);
    @GetMapping(PREFIX + "/getCollectorAlertsByPlatform") // ③ Spring MVC 接口注解
    @Operation(summary = "按平台获取未删除的采集器告警")  // ② Swagger 接口文档
    CommonResult<List<Map<String, Object>>> getCollectorAlertsByPlatform(@RequestParam("platform") Collection<Long> ids);
    @PostMapping(PREFIX + "/createAlarmInfo") // ③ Spring MVC 接口注解
    @Operation(summary = "问题工单创建处理记录")
    void createAlarmInfo(@RequestBody AlarmInfoDTO alarmInfoDTO);


    @PostMapping(PREFIX + "/changeAlarmIsFallback") // ③ Spring MVC 接口注解
    @Operation(summary = "修改告警退回状态")// ② Swagger 接口文档
    CommonResult<Boolean> changeAlarmIsFallback(@RequestParam("alertId")Long alertId,
                                                 @RequestParam("isFallback")Integer isFallback);

    @GetMapping(PREFIX + "/getAlertInfoById") // ③ Spring MVC 接口注解
    @Operation(summary = "获取告警信息")  // ② Swagger 接口文档
    CommonResult<Alert> getAlertInfoById(@RequestParam("alertId") Long id);
}
