package cn.iocoder.zj.module.monitor.api.secgrouprule;


import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.secgrouprule.dto.SecgroupRuleCreateReqDto;
import cn.iocoder.zj.module.monitor.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME) // ① @FeignClient 注解
@Tag(name = "RPC 服务 - 安全组规则相关") // ② Swagger 接口文档
public interface SecgroupruleApi {

    String PREFIX = ApiConstants.PREFIX + "/zstack";

    @PostMapping(PREFIX + "/addSecgroupRules")
    @Operation(summary = "批量新增安全组规则")
        // ② Swagger 接口文档
    CommonResult<Boolean> addSecgroupRules(@RequestBody List<SecgroupRuleCreateReqDto> reqDTO);

    @PostMapping(PREFIX + "/updateSecgroupRules") // ③ Spring MVC 接口注解
    @Operation(summary = "更新安全组规则信息")
        // ② Swagger 接口文档
    CommonResult<Boolean> updateSecgroupRules(@RequestBody List<SecgroupRuleCreateReqDto> secgroupRuleCreateReqDtoList);

    @PostMapping(PREFIX + "/delSecgroupRules")
    @Operation(summary = "删除安全组规则")
    CommonResult<Boolean> delSecgroupRules(@RequestBody List<SecgroupRuleCreateReqDto> secgroupRuleCreateReqDtoList);

    @PostMapping(PREFIX + "/getSecgroupRulesByPlatformId")
    @Operation(summary = "查询安全组规则")
    CommonResult<List<SecgroupRuleCreateReqDto>> getSecgroupRulesByPlatformId(@RequestParam("platformId") Long platformId);

}
