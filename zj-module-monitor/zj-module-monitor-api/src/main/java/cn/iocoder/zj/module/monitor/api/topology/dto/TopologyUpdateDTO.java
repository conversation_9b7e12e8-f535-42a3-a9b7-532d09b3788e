package cn.iocoder.zj.module.monitor.api.topology.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @ClassName : TopologyUpdateDTO  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/9/25  15:22
 */
@Schema(description = "RPC 服务 - Topology 拓扑图的表 Response DTO")
@Data
@Accessors(chain = true)
public class TopologyUpdateDTO {
    /**
     * 主键
     */
    private Long id;
    /**
     * 拓扑图json
     */
    private String topologyJson;
    /**
     * 租户名称
     */
    private String tenantName;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;

    /**
     * @description: 拓扑图名称
     * <AUTHOR>
     * @date 2024/9/3 13:23
     * @version 1.0
     */
    private String topologyName;

    /**
     * @description: 拓扑图图片
     * <AUTHOR>
     * @date 2024/9/3 13:23
     * @version 1.0
     */
    private String topologyImg;


    /**
     * 拓扑图json
     */
    private String resourceJson;
    /**
     * 接口关系json
     */
    private String interfacesJson;
}
