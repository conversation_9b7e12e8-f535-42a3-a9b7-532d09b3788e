package cn.iocoder.zj.module.monitor.api.hardwarenic;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME) // ① @FeignClient 注解
@Tag(name = "RPC 服务 - 硬件设施相关") // ② Swagger 接口文档
public interface HardWareNicApi {
    String PREFIX = ApiConstants.PREFIX + "/zstack";

    @PostMapping(PREFIX + "/addHardWareNic") // ③ Spring MVC 接口注解
    @Operation(summary = "批量新增物理机网络信息")  // ② Swagger 接口文档
    CommonResult<Boolean> adds(@RequestBody List<HardWareNicRespDTO> reqDTO);

    @PostMapping(PREFIX + "/updateHardWareNic") // ③ Spring MVC 接口注解
    @Operation(summary = "获取更新物理机网络信息")  // ② Swagger 接口文档
    CommonResult<Boolean> updates(@RequestBody List<HardWareNicRespDTO> reqDTO);

    @PostMapping(PREFIX + "/getHardWareNicByPlatformId") // ③ Spring MVC 接口注解
    @Operation(summary = "根据平台Id物理机网络信息")  // ② Swagger 接口文档
    CommonResult<List<HardWareNicRespDTO>> getHardwareNicByPlatformId(@RequestParam("platformId")Long platformId);

    @PostMapping(PREFIX + "/deleteHardWareNic") // ③ Spring MVC 接口注解
    @Operation(summary = "删除更新物理机网络信息")  // ② Swagger 接口文档
    int deletes(@RequestBody List<HardWareNicRespDTO> reqDTO);
}
