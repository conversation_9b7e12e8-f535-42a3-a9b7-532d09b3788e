package cn.iocoder.zj.module.monitor.enums.message;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName : AssetMessageEnum  //类名
 * @Description : Asset消息枚举  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/8/2  15:0·6
 */

@AllArgsConstructor
@Getter
public enum AssetMessageEnum {


    AGENT_ALARM("agent_alarm"); // 告警消息

    /**
     * 短信模板的标识
     *
     * 关联 SmsTemplateDO 的 code 属性
     */
    private final String smsTemplateCode;
}
