package cn.iocoder.zj.module.monitor.api.hostinfo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName : HostInfoRespDTO  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/30  15:42
 */

@Schema(description = "RPC 服务 - Zstack 访问主机的信息 Response DTO")
@Data
@Accessors(chain = true)
public class HostInfoRespDTO {


    /**
     * 主键
     * <p>
     * private Long id;
     * /**
     * 主机id
     */
    private String uuid;
    /**
     * 主机名称
     */
    private String name;
    /**
     * 主机状态
     */
    private String state;
    /**
     * ip
     */
    private String ip;
    /**
     * 弹性ip
     */
    private String vipIp;
    /**
     * 集群id
     */
    private String clusterUuid;
    /**
     * CPU架构
     */
    private String architecture;
    /**
     * 操作系统类型
     */
    private String guestOsType;
    /**
     * 主机创建时间
     */
    private Date vCreateDate;
    /**
     * 主机更新时间
     */
    private Date vUpdateDate;
    /**
     * 主机类型
     */
    private String type;
    /**
     * 分配内存
     */
    private Long memorySize;

    /**
     * 硬盘使用率
     */
    private BigDecimal diskUsed;

    /**
     * cpu 使用率
     */
    private BigDecimal cpuUsed;

    /**
     * 内存使用率
     */
    private BigDecimal memoryUsed;

    /**
     * 分配cpu
     */
    private Integer cpuNum;
    /**
     * mac 地址
     */
    private String mac;

    /**
     * 租户id
     */
    private Long tenantId;

    private String clusterName;

    private BigDecimal networkInBytes;

    private BigDecimal networkOutBytes;

    private BigDecimal diskUsedBytes;

    private BigDecimal diskFreeBytes;

    private BigDecimal totalDiskCapacity;

    private BigDecimal actualSize;

    private BigDecimal cloudSize;

    private BigDecimal networkInPackets;

    private BigDecimal networkOutPackets;

    // 平台配置id
    private Long platformId;
    // 平台配置名称
    private String platformName;
}
