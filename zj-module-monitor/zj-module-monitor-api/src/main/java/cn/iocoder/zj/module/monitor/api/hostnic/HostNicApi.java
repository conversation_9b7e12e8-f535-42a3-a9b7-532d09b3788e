package cn.iocoder.zj.module.monitor.api.hostnic;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.hostnic.dto.HostNicCreateReqDto;
import cn.iocoder.zj.module.monitor.api.hostnic.dto.HostNicRespDTO;
import cn.iocoder.zj.module.monitor.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME) // ① @FeignClient 注解
@Tag(name = "RPC 服务 - 云主机网络相关") // ② Swagger 接口文档
public interface HostNicApi {
    String PREFIX = ApiConstants.PREFIX + "/zstack";

    @PostMapping(PREFIX + "/addHostNics")
    @Operation(summary = "批量新增云主机网络信息")
        // ② Swagger 接口文档
    CommonResult<Boolean> addHostNics(@RequestBody List<HostNicCreateReqDto> reqDTO);

    @PostMapping(PREFIX + "/updateHostNics") // ③ Spring MVC 接口注解
    @Operation(summary = "更新云主机网卡信息")  // ② Swagger 接口文档
    CommonResult<Boolean> updateHostNics(@RequestBody List<HostNicCreateReqDto> hostNicCreateReqDtos);

    @PostMapping(PREFIX + "/delHostNics")
    @Operation(summary = "删除云主机网卡信息")
    CommonResult<Boolean> delHostNics(@RequestBody List<HostNicCreateReqDto> hostNicCreateReqDtos);

    @PostMapping(PREFIX + "/getHostNicsByPlatformId")
    @Operation(summary = "查询云主机网卡信息")
    CommonResult<List<HostNicCreateReqDto>> getHostNicsByPlatformId(@RequestParam("platformId") Long platformId);
}
