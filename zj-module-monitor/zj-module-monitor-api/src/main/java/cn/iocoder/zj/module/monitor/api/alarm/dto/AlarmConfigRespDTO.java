package cn.iocoder.zj.module.monitor.api.alarm.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AlarmConfigRespDTO {
    @Schema(description = "告警名称,创建时必填")
    private String alarmName;

    @Schema(description = "告警简介")
    private String description;

    @Schema(description = "资源类型，云主机monitor_alarm_host;宿主机monitor_alarm_hardware;存储monitor_alarm_disk;monitor_alarm_image镜像,创建时必填")
    private String sourceType;

    @Schema(description = "字典名称(对应报警条目的name值),创建时必填")
    private String dictLabelName;

    @Schema(description = "字典类型(对应报警条目类型的type值),创建时必填")
    private String dictLabelType;

    @Schema(description = "字典值(对应报警条目的value值),创建时必填")
    private String dictLabelValue;

    @Schema(description = "触发规则,创建时必填,<=,>=,<,>")
    private String alarmRule;

    @Schema(description = "告警阈值,创建时必填")
    private Integer alarmVal;

    @Schema(description = "单位,以dictLabelValue为参数查询字典项")
    private String unit;

    @Schema(description = "收敛次数,数字")
    private Long alarmTime;

    @Schema(description = "告警级别,创建时必填。1提示，2警告，3严重")
    private Integer alarmLevel;

    @Schema(description = "消息内容，后台拼接，不传")
    private String context;

    @Schema(description = "是否删除，0否，1是")
    private Integer deleted;

    @Schema(description = "租户ID")
    private Long tenantId;
}
