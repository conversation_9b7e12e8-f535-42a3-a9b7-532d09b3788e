package cn.iocoder.zj.module.monitor.api.cloud;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.cloud.dto.CloudRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME) // ① @FeignClient 注解
@Tag(name = "RPC 服务 - 硬件设施相关") // ② Swagger 接口文档
public interface CloudInfoApi {
    String PREFIX = ApiConstants.PREFIX + "/zstack";

    @PostMapping(PREFIX + "/getCloudByPlatformId") // ③ Spring MVC 接口注解
    @Operation(summary = "按平台获取宿主机")
        // ② Swagger 接口文档
    CommonResult<List<CloudRespCreateReqDTO>> getCloudByPlatformId(@RequestParam("platformId") Long platformId);
}
