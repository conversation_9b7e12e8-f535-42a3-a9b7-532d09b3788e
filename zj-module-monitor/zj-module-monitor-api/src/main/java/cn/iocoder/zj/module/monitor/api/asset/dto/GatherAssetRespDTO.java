package cn.iocoder.zj.module.monitor.api.asset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @ClassName : GatherAssetRespDTO  //类名
 * @Description : 资产数据  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/9/4  9:38
 */


@Schema(description = "RPC 服务 - Zstack 访问资产的信息 Response DTO")
@Data
@Accessors(chain = true)
public class GatherAssetRespDTO {
    /**
     * 租户绑定的采集设备id
     */
    private String uuid;
    /**
     * 访问设备的ip地址 (例：udp:*************)
     */
    private String ip;

    /**
     * 主机名称
     */
    private String hostName;

    private String platformName;

    private String platformId;

    private String alarmLocation;

    /**
     * @description: 在线状态，0 离线 ，1在线
     * <AUTHOR>
     * @date 2023/8/14 15:52
     * @version 1.0
     */
    private Integer onlineType;

    /**
     * cpu 使用率
     */
    private Double cpuUsage;

    /**
     * 内存使用率
     */
    private Double memoryUsage;


    /**
     * 磁盘使用率
     */
    private Double diskUsage;
}
