package cn.iocoder.zj.module.monitor.api.topology;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.topology.dto.TopologyDTO;
import cn.iocoder.zj.module.monitor.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


@FeignClient(name = ApiConstants.NAME) // ① @FeignClient 注解
@Tag(name = "RPC 服务 - 使用率排行") // ② Swagger 接口文档
public interface TopologyApi {

    String PREFIX = ApiConstants.PREFIX + "/topology";

    @PostMapping(PREFIX + "/get") // ③ Spring MVC 接口注解
    @Operation(summary = "获取列表")
        // ② Swagger 接口文档
    CommonResult<List<TopologyDTO>> get();


    @PostMapping(PREFIX + "/updatebatch") // ③ Spring MVC 接口注解
    @Operation(summary = "批量更新")
        // ② Swagger 接口文档
    CommonResult<Void> updatebatch( @RequestBody List<TopologyDTO> topologyUpdateDTOS);
}
