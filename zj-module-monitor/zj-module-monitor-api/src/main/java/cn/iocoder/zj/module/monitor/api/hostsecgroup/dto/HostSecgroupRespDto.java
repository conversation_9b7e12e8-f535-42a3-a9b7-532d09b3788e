package cn.iocoder.zj.module.monitor.api.hostsecgroup.dto;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class HostSecgroupRespDto {
    private Long id;
    /**
     * 云主机uuid
     */
    private String hostUuid;
    /**
     * 安全组uuid
     */
    private String secgroupUuid;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建者，目前使用 SysUser 的 id 编号
     * <p>
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    private String creator;
    /**
     * 更新者，目前使用 SysUser 的 id 编号
     * <p>
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    private String updater;
}
