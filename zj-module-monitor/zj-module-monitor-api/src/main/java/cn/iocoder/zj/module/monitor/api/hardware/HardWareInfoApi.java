package cn.iocoder.zj.module.monitor.api.hardware;


import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.enums.ApiConstants;
import cn.iocoder.zj.module.monitor.pojo.AssetReqVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(name = ApiConstants.NAME) // ① @FeignClient 注解
@Tag(name = "RPC 服务 - 硬件设施相关") // ② Swagger 接口文档
public interface HardWareInfoApi {


    String PREFIX = ApiConstants.PREFIX + "/zstack";

    @PostMapping(PREFIX + "/addHardWare") // ③ Spring MVC 接口注解
    @Operation(summary = "批量新增硬件设施信息")
        // ② Swagger 接口文档
    CommonResult<Boolean> adds(@RequestBody List<HardWareRespCreateReqDTO> reqDTO);

    @PostMapping(PREFIX + "/getHardWareCount") // ③ Spring MVC 接口注解
    @Operation(summary = "获取硬件设施数量")
        // ② Swagger 接口文档
    int count(@RequestParam("typeName") String typeName);

    @PostMapping(PREFIX + "/updateHardWares") // ③ Spring MVC 接口注解
    @Operation(summary = "获取更新硬件设施信息")
        // ② Swagger 接口文档
    CommonResult<Boolean> updates(@RequestBody List<HardWareRespCreateReqDTO> hardWareRespCreateReqDTOS);

    @GetMapping(PREFIX + "/getHardWareAll") // ③ Spring MVC 接口注解
    @Operation(summary = "获取硬件设施列表")
        // ② Swagger 接口文档
    CommonResult<List<HardWareRespCreateReqDTO>> getAll(@RequestParam("typeName") String typeName);

    @PostMapping(PREFIX + "/getIp")
    @Operation(summary = "通过uuid获取ip")
    CommonResult<String> getIp(@RequestBody Map<String, String> map);

    @PostMapping(PREFIX + "/deleteHardWare") // ③ Spring MVC 接口注解
    @Operation(summary = "删除宿主机列表")
    int deleteHardWare(@RequestBody List<HardWareRespCreateReqDTO> hardWareRespCreateReqDTOS);

    @PostMapping(PREFIX + "/getHardWareListAll") // ③ Spring MVC 接口注解
    @Operation(summary = "获取硬件设施所有列表")
        // ② Swagger 接口文档
    CommonResult<List<HardWareRespCreateReqDTO>> getListAll();

    @PostMapping(PREFIX + "/removeDuplicateHardwareData") // ③ Spring MVC 接口注解
    @Operation(summary = "删除重复数据")
    void removeDuplicateData();

    @PostMapping(PREFIX + "/getHardwareByUuid") // ③ Spring MVC 接口注解
    @Operation(summary = "按uuid获取宿主机")
        // ② Swagger 接口文档
    CommonResult<HardWareRespCreateReqDTO> getByUuid(@RequestParam("uuid") String uuid);

    @PostMapping(PREFIX + "/getCpuSockets") // ③ Spring MVC 接口注解
    @Operation(summary = "根据平台id集合获取总的平台下CPU核心数")
        // ② Swagger 接口文档
    CommonResult<Long> getCpuSockets(List<String> platformIds);


    @PostMapping(PREFIX + "/getHardwareByPlatformId") // ③ Spring MVC 接口注解
    @Operation(summary = "按平台获取宿主机")
        // ② Swagger 接口文档
    CommonResult<List<HardWareRespCreateReqDTO>> getHardwareByPlatformId(@RequestParam("platformId") Long platformId);

    @PostMapping(PREFIX + "/getHardwareByTenantOrPlatforms")
    @Operation(summary = "按租户Id获取宿主机")
    CommonResult<List<HardWareRespCreateReqDTO>> getHardwareByTenantOrPlatforms(@RequestBody AssetReqVO assetReqVO);


}
