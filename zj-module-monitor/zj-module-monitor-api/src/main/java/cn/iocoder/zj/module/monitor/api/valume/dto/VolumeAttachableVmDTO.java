package cn.iocoder.zj.module.monitor.api.valume.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class VolumeAttachableVmDTO {
    @Schema(description = "云盘uuid")
    private String volumeUuid;

    @Schema(description = "云盘名称")
    private String volumeName;

    @Schema(description = "云主机uuid")
    private String hostUuid;

    @Schema(description = "云主机名称")
    private String hostName;

    @Schema(description = "平台ID")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;
}
