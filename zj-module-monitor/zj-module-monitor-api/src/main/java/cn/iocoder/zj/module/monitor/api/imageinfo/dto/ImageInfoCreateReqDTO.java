package cn.iocoder.zj.module.monitor.api.imageinfo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "RPC 服务 - 镜像信息创建 Request DTO")
@Data
public class ImageInfoCreateReqDTO implements Serializable {

    @Schema(description = "镜像表id")
    @NotNull(message = "镜像表id不能为空")
    private Long id;

    @Schema(description = "镜像id")
    private String uuid;

    @Schema(description = "镜像名称")
    private String name;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "镜像格式")
    private String format;

    @Schema(description = "CPU架构")
    private String cpuArch;

    @Schema(description = "操作系统")
    private String osType;

    @Schema(description = "镜像大小(字节)")
    private Long size;

    @Schema(description = "镜像类型")
    private String imageType;

    @Schema(description = "共享范围")
    private String sharingScope;

    @Schema(description = "标签")
    private String tag;

    @Schema(description = "镜像创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime vCreateDate;

    @Schema(description = "镜像更新时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime vUpdateDate;

    @Schema(description = "系统语言")
    private String osLanguage;

    @Schema(description = "最小内存要求(MB)")
    private BigDecimal minMemory;

    @Schema(description = "最小磁盘要求(GB)")
    private BigDecimal minDisk;

    @Schema(description = "磁盘驱动")
    private String diskDriver;

    @Schema(description = "网卡驱动")
    private String networkDriver;

    @Schema(description = "引导方式")
    private String bootMode;

    @Schema(description = "远程终端协议")
    private String remoteProtocol;

    @Schema(description = "应用平台")
    private String applicationPlatform;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "删除状态")
    private Long deleted;

    private String hostUuid;
    private String hostName;
}