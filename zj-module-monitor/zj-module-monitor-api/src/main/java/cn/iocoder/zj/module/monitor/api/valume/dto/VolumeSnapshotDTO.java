package cn.iocoder.zj.module.monitor.api.valume.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;


/**
 * @ClassName : VolumeSnapshotDTO  //类名
 * @Description : 云盘快照信息DTO  //描述
 */
@Data
public class VolumeSnapshotDTO {
    @Schema(description = "云盘快照uuid")
    private String uuid;

    @Schema(description = "平台ID")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "云盘快照名称")
    private String name;

    @Schema(description = "云盘快照描述")
    private String description;

    @Schema(description = "云盘uuid")
    private String volumeUuid;

    @Schema(description = "云盘名称")
    private String volumeName;

    @Schema(description = "主存储uuid")
    private String primaryStorageUuid;

    @Schema(description = "主存储名称")
    private String primaryStorageName;

    @Schema(description = "云盘快照类型")
    private String type;

    @Schema(description = "云盘类型")
    private String volumeType;

    @Schema(description = "是否最新")
    private String latest;

    @Schema(description = "是否最新")
    private LocalDateTime createTime;

    @Schema(description = "类型vmware /zstack")
    private String typeName;

    @Schema(description = "云盘快照大小")
    private Long size;

    @Schema(description = "云盘快照状态")
    private String status;

    @Schema(description = "云盘快照创建时间")
    private Date vCreateDate;

    @Schema(description = "云盘快照更新时间")
    private Date vUpdateDate;

    @Schema(description = "云盘快照安装路径")
    private String installPath;

    @Schema(description = "云盘快照格式")
    private String format;

    @Schema(description = "包含内存快照")
    private Boolean isMemory;

    @Schema(description = "删除")
    private Long deleted;

    /**
     * 云主机uuid
     */
    private String hostUuid;
    private String hostName;

}
