package cn.iocoder.zj.module.monitor.api.hostnic.dto;

import lombok.Data;

@Data
public class HostNicCreateReqDto {
    private Long id;
    /**
     * uuid
     */
    private String uuid;
    /**
     * 名称
     */
    private String name;
    /**
     * 云主机uuid
     */
    private String hostUuid;
    /**
     * ipV6
     */
    private String ip6;
    /**
     * ip
     */
    private String ip;
    /**
     * mac
     */
    private String mac;
    /**
     * 驱动
     */
    private String driver;
    /**
     * 在经典网络
     */
    private Byte inClassicNetwork;
    /**
     * 网络uuid
     */
    private String networkUuid;
    /**
     * 三级网络名称
     */
    private String networkName;
    /**
     * 队列数量
     */
    private String numQueues;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;
}
