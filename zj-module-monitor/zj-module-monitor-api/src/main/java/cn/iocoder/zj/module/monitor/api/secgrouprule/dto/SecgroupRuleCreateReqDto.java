package cn.iocoder.zj.module.monitor.api.secgrouprule.dto;

import lombok.Data;

@Data
public class SecgroupRuleCreateReqDto {

    /**
     * id
     */
    private Long id;
    /**
     * uuid
     */
    private String uuid;
    /**
     * 状态
     */
    private String status;
    /**
     * 安全组uuid
     */
    private String secgroupUuid;
    /**
     * 优先级
     */
    private Integer priority;
    /**
     * 协议
     */
    private String protocol;
    /**
     * 端口
     */
    private String ports;
    /**
     * 方向
     */
    private String direction;
    /**
     * cidr
     */
    private String cidr;
    /**
     * 策略
     */
    private String action;
    /**
     * 备注
     */
    private String description;
    private Long platformId;
    private String platformName;

}
