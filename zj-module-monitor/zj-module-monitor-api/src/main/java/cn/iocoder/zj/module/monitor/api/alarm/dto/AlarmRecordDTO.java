package cn.iocoder.zj.module.monitor.api.alarm.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AlarmRecordDTO {
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "设备uuid")
    private String uuid;

    @Schema(description = "告警配置id")
    private Long alarmId;

    @Schema(description = "告警等级")
    private Integer alarmLevel;

    @Schema(description = "告警等级")
    private Long alarmNum;

    @Schema(description = "处理")
    private Integer isSolved;

    @Schema(description = "平台id")
    private Long platformId;
    /**
     * 消息内容
     */
    private String context;
    /**
     * 资源类型，host云主机；hardware宿主机；storage存储；image镜像
     */
    private String sourceType;
    /**
     * 告警资源名称
     */
    private String sourceName;
    /**
     * 地区ID
     */
    private Long regionId;
    /**
     * 地区名称
     */
    private String regionName;
    /**
     * 告警日期
     */
    private String createTime;

    private String updateTime;
    private Long platformConfigId;

    private String platformName;

    /**
     * 主机、宿主机、存储名称
     */
    private String productsName;
    /**
     * 实际告警值
     */
    private String actualContext;

    /**
     * 是否已读0未读，1已读
     */
    private Integer isRead;
}
