package cn.iocoder.zj.module.monitor.api.eip.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
public class EipCreateReqDto {
    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * uuid
     */
    private String uuid;
    /**
     * ip地址
     */
    private String ipAddr;
    /**
     * 带宽
     */
    private Integer bandwidth;
    /**
     * 网络uuid
     */
    private String networkId;
    /**
     * 资源类型
     */
    private String associateType;
    /**
     * 资源id
     */
    private String associateId;
    /**
     * 计费类型
     */
    private String chargeType;
    /**
     * 区域id
     */
    private String cloudregionId;
    /**
     * public_ip 公网IP  elastic_ip弹性公网IP
     */
    private String mode;
    /**
     * 状态
     */
    private String status;
    /**
     * 弹性公网创建时间
     */
    private Date vCreateTime;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;
}
