package cn.iocoder.zj.module.monitor.api.tenantscreen;


import cn.iocoder.zj.module.monitor.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@FeignClient(name = ApiConstants.NAME) // ① @FeignClient 注解
@Tag(name = "RPC 服务 - 使用率排行") // ② Swagger 接口文档
public interface TenantScreenApi {
    String PREFIX = ApiConstants.PREFIX + "/top";


    @GetMapping(PREFIX + "/getScreenUsageTop")  // ③ Spring MVC 接口注解
    @Operation(summary = "获取使用率排行")  // ② Swagger 接口文档
    Map<String, Object> getScreenUsageTop(@RequestParam("userId") Long userId,
                                          @RequestParam(value = "platformId",required = false) Long platformId,
                                          @RequestParam("time") String time, @RequestParam("status") Integer status,@RequestParam("userTenantId") Long userTenantId);
}
