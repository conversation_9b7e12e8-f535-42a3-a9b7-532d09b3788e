package cn.iocoder.zj.module.monitor.api.storagepool.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
@Data
public class StoragePoolCreateRespDTO {
    /**
     * 主键
     */
    private Long id;
    /**
     * 存储池uuid
     */
    private String uuid;
    /**
     * 存储名称
     */
    private String name;
    /**
     * 存储池类型
     */
    private String type;
    /**
     * 存储名称
     */
    private String description;
    /**
     * 存储池创建时间
     */
    private LocalDateTime vCreateDate;
    /**
     * 存储池修改时间
     */
    private LocalDateTime lastOpDate;
    /**
     * 已使用容量
     */
    private BigDecimal usedCapacity;
    /**
     * 虚拟可用容量
     */
    private BigDecimal availableCapacity;
    /**
     * 总容量
     */
    private BigDecimal totalCapacity;
    /**
     * 主存储uuid
     */
    private String storageUuid;
    /**
     * 数据安全类型
     */
    private String securityPolicy;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;

}
