package cn.iocoder.zj.module.monitor.api.eip.dto;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class EipRespDto {

    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * uuid
     */
    private String uuid;
    /**
     * ip地址
     */
    private String ipAddr;
    /**
     * 带宽
     */
    private Integer bandwidth;
    /**
     * 网络uuid
     */
    private String networkId;
    /**
     * 资源类型
     */
    private String associateType;
    /**
     * 资源id
     */
    private String associateId;
    /**
     * 计费类型
     */
    private String chargeType;
    /**
     * 区域id
     */
    private String cloudregionId;
    /**
     * public_ip 公网IP  elastic_ip弹性公网IP
     */
    private String mode;
    /**
     * 状态
     */
    private String status;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建者，目前使用 SysUser 的 id 编号
     *
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    private String creator;
    /**
     * 更新者，目前使用 SysUser 的 id 编号
     *
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    private String updater;
}
