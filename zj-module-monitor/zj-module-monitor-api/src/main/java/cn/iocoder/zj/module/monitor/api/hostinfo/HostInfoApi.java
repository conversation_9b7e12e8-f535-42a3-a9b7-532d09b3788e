package cn.iocoder.zj.module.monitor.api.hostinfo;


import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.enums.ApiConstants;
import cn.iocoder.zj.module.monitor.pojo.AssetReqVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@FeignClient(name = ApiConstants.NAME) // ① @FeignClient 注解
@Tag(name = "RPC 服务 - 云主机相关") // ② Swagger 接口文档
public interface HostInfoApi {

    String PREFIX = ApiConstants.PREFIX + "/zstack";

    @PostMapping(PREFIX + "/adds") // ③ Spring MVC 接口注解
    @Operation(summary = "批量新增云主机信息")  // ② Swagger 接口文档
    CommonResult<Boolean> adds(@RequestBody List<HostInfoRespCreateReqDTO>  reqDTO);

    @PostMapping(PREFIX + "/count") // ③ Spring MVC 接口注解
    @Operation(summary = "获取云主机数量")  // ② Swagger 接口文档
    int count(@RequestParam("typeName") String typeName);

    @PostMapping(PREFIX + "/updates") // ③ Spring MVC 接口注解
    @Operation(summary = "获取更新云主机信息")  // ② Swagger 接口文档
    CommonResult<Boolean> updates(@RequestBody List<HostInfoRespCreateReqDTO> hostInfoRespCreateReqDTOList);

    @PostMapping(PREFIX + "/getall") // ③ Spring MVC 接口注解
    @Operation(summary = "获取云主机列表")  // ② Swagger 接口文档
    CommonResult<List<HostInfoRespCreateReqDTO>> getAll(@RequestParam("typeName")String typeName);
    @PostMapping(PREFIX + "/getHostByUuid") // ③ Spring MVC 接口注解
    @Operation(summary = "按uuid获取云主机")  // ② Swagger 接口文档
    CommonResult<HostInfoRespCreateReqDTO> getByUuid(@RequestParam("hostUuid") String hostUuid);
    @PostMapping(PREFIX + "/getHostById") // ③ Spring MVC 接口注解
    @Operation(summary = "按uuid获取云主机")
    CommonResult<HostInfoRespCreateReqDTO> getById(@RequestParam("hostId") Long hostUuid);
    @PostMapping(PREFIX + "/updateHostSingle") // ③ Spring MVC 接口注解
    @Operation(summary = "更新云主机信息")  // ② Swagger 接口文档
    CommonResult<Boolean> updateHostSingle(@RequestBody HostInfoRespCreateReqDTO hostInfoRespCreateReqDTO);
    @PostMapping(PREFIX + "/createHostSingle") // ③ Spring MVC 接口注解
    @Operation(summary = "创建云主机信息")  // ② Swagger 接口文档
    CommonResult<Boolean> createHostSingle(@RequestBody HostInfoRespCreateReqDTO reqDTO);

    @PostMapping(PREFIX + "/deleteHostList") // ③ Spring MVC 接口注解
    @Operation(summary = "删除云主机列表")
    int deleteHostList(@RequestBody List<HostInfoRespCreateReqDTO> hostInfoRespCreateReqDTOList);
    @PostMapping(PREFIX + "/getByPlatformId") // ③ Spring MVC 接口注解
    @Operation(summary = "按平台ID获取云主机")  // ② Swagger 接口文档
    CommonResult<Map<String,List<String>>> getAllDeviceByPlatformId(@RequestParam("platformIds") Collection<Long> platformId);

    @PostMapping(PREFIX + "/getListAll") // ③ Spring MVC 接口注解
    @Operation(summary = "获取云主机所有数据")  // ② Swagger 接口文档
    CommonResult<List<HostInfoRespCreateReqDTO>> getListAll();

    @PostMapping(PREFIX + "/removeDuplicateData") // ③ Spring MVC 接口注解
    @Operation(summary = "删除重复数据")
    void removeDuplicateData();

    @PostMapping(PREFIX + "/deleteAssetAndHostJson") // ③ Spring MVC 接口注解
    @Operation(summary = "删除云主机拓扑图")
    void deleteAssetAndHostJson(@RequestParam("uuid") String uuid);


    @PostMapping(PREFIX + "/deletedVm") // ③ Spring MVC 接口注解
    @Operation(summary = "删除云主机")
    void deleteVm(@RequestParam("uuid")Long id);


    @PostMapping(PREFIX + "/getVmByPlatformId") // ③ Spring MVC 接口注解
    @Operation(summary = "按平台获取云主机")
    List<HostInfoRespCreateReqDTO> getVmByPlatformId(@RequestParam("platformId") Long platformId);

    @GetMapping(PREFIX + "/count") // ③ Spring MVC 接口注解
    @Operation(summary = "查询云主机数量")  // ② Swagger 接口文档
    int selectHostCount();

    /**
     * 根据当前租户查询云主机列表
     *
     * @return 云主机列表
     */
    @PostMapping(PREFIX + "/getHostListByTenantOrPlatforms") // ③ Spring MVC 接口注解
    @Operation(summary = "查询云主机列表")
    // ② Swagger 接口文档
    CommonResult<List<HostInfoRespCreateReqDTO>> getHostListByTenantOrPlatforms(@RequestBody AssetReqVO assetReqVO);

    @PostMapping(PREFIX + "/getByPlatformIdAndTags") // ③ Spring MVC 接口注解
    @Operation(summary = "按平台ID获取云主机")  // ② Swagger 接口文档
    CommonResult<List<HostInfoRespCreateReqDTO>> getByPlatformIdAndTags(@RequestBody AssetReqVO assetReqVO);

}
