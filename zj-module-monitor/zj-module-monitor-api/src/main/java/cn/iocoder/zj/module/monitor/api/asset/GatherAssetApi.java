package cn.iocoder.zj.module.monitor.api.asset;


import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.asset.dto.GatherAssetRespDTO;
import cn.iocoder.zj.module.monitor.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME) // ① @FeignClient 注解
@Tag(name = "RPC 服务 - 资产相关") // ② Swagger 接口文档
public interface GatherAssetApi {

    String PREFIX = ApiConstants.PREFIX + "/zstack";


    @PostMapping(PREFIX + "/getAsset") // ③ Spring MVC 接口注解
    @Operation(summary = "获取云主机列表")
        // ② Swagger 接口文档
    CommonResult<List<GatherAssetRespDTO>> getAssetList();


    @PostMapping(PREFIX + "/addAsset") // ③ Spring MVC 接口注解
    @Operation(summary = "批量新增云主机信息")
        // ② Swagger 接口文档
    CommonResult<Boolean> adds(@RequestBody List<GatherAssetRespDTO> reqDTO);

    @PostMapping(PREFIX + "/getGatherAssetCount") // ③ Spring MVC 接口注解
    @Operation(summary = "获取资产类型对应租户资产数量")
        // ② Swagger 接口文档
    Long getGatherAssetCount(@RequestBody String deviceType);

    @PostMapping(PREFIX + "/getGatherAssetCountBySysType") // ③ Spring MVC 接口注解
    @Operation(summary = "获取资产类型对应租户资产数量")
        // ② Swagger 接口文档
    Long getGatherAssetCountBySysType(@RequestBody String sysType);

}
