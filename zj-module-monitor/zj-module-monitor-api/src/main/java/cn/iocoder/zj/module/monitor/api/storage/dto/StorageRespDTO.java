package cn.iocoder.zj.module.monitor.api.storage.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @ClassName : StorageRespDTO  //类名
 * @Description : 存储  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/6/6  14:42
 */
@Schema(description = "RPC 服务 - Zstack 访问存储信息 Response DTO")
@Data
@Accessors(chain = true)
public class StorageRespDTO {

    /**
     * 主键
     */
    private Long id;
    /**
     * 存储名称
     */
    private String name;
    /**
     * 存储uuid
     */
    private String uuid;
    /**
     * url
     */
    private String url;
    /**
     * 状态
     */
    private String state;
    /**
     * 类型：Ceph
     */
    private String type;
    /**
     * 状态，页面展示值（Connected：已连接，DisConnected: 未连接）
     */
    private String status;
    /**
     * 容量使用率
     */
    private BigDecimal capacityUtilization;
    /**
     * 总容量
     */
    private Long totalCapacity;
    /**
     * 已用容量
     */
    private Long usedCapacity;
    /**
     * 地区id
     */
    private Long regionId;

    /**
     * 租户id
     */
    private Long tenantId;
    private BigDecimal availablePhysicalCapacity;

    private BigDecimal totalPhysicalCapacity;

    private BigDecimal availableCapacity;

    // 平台配置id
    private Long platformId;
    // 平台配置名称
    private String platformName;
}
