package cn.iocoder.zj.module.monitor.api.network;


import cn.iocoder.zj.framework.common.message.ClusterMsg;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL2DTO;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkL3DTO;
import cn.iocoder.zj.module.monitor.api.network.dto.NetWorkVpcDTO;
import cn.iocoder.zj.module.monitor.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(name = ApiConstants.NAME) // ① @FeignClient 注解
@Tag(name = "RPC 服务 - 网络相关接口") // ② Swagger 接口文档
public interface NetworkApi {

    String PREFIX = ApiConstants.PREFIX + "/zstack";


    @PostMapping(PREFIX + "/addNetWorkL2") // ③ Spring MVC 接口注解
    @Operation(summary = "批量新增L2网络信息")
        // ② Swagger 接口文档
    CommonResult<Boolean> addNetWorkL2(@RequestBody List<NetWorkL2DTO> reqDTO);

    @PostMapping(PREFIX + "/getNetWorkL2Count") // ③ Spring MVC 接口注解
    @Operation(summary = "获取二级网络数量")
        // ② Swagger 接口文档
    Long getNetWorkL2Count(@RequestParam("typeName") String typeName);

    @PostMapping(PREFIX + "/updateNetWorkL2") // ③ Spring MVC 接口注解
    @Operation(summary = "获取更新二级网络信息")
        // ② Swagger 接口文档
    CommonResult<Boolean> updateNetWorkL2(@RequestBody List<NetWorkL2DTO> netWorkL2DTOS);

    @PostMapping(PREFIX + "/getNetWorkL2List") // ③ Spring MVC 接口注解
    @Operation(summary = "获取二级网络列表")
        // ② Swagger 接口文档
    CommonResult<List<NetWorkL2DTO>> getNetWorkL2List(@RequestParam("typeName") String typeName);

    @PostMapping(PREFIX + "/getNetWorkL2ByNameList") // ③ Spring MVC 接口注解
    @Operation(summary = "获取二级网络名称")
        // ② Swagger 接口文档
    List<Map> getNetWorkL2ByNameList();

    @PostMapping(PREFIX + "/deleteNetWorkL2ByNameList") // ③ Spring MVC 接口注解
    @Operation(summary = "删除二级网络列表")
    int deleteNetWorkL2ByNameList(@RequestBody List<NetWorkL2DTO> netWorkL2DTOS);

    //==========================三级网络===================================

    @PostMapping(PREFIX + "/addNetWorkL3") // ③ Spring MVC 接口注解
    @Operation(summary = "批量新增L3网络信息")
        // ② Swagger 接口文档
    CommonResult<Boolean> addNetWorkL3(@RequestBody List<NetWorkL3DTO> reqDTO);

    @PostMapping(PREFIX + "/getNetWorkL3Count") // ③ Spring MVC 接口注解
    @Operation(summary = "获取二级网络数量")
        // ② Swagger 接口文档
    Long getNetWorkL3Count(@RequestParam("typeName") String typeName);

    @PostMapping(PREFIX + "/updateNetWorkL3") // ③ Spring MVC 接口注解
    @Operation(summary = "获取更新二级网络信息")
        // ② Swagger 接口文档
    CommonResult<Boolean> updateNetWorkL3(@RequestBody List<NetWorkL3DTO> netWorkL3DTOS);

    @PostMapping(PREFIX + "/getNetWorkL3List") // ③ Spring MVC 接口注解
    @Operation(summary = "获取二级网络列表")
        // ② Swagger 接口文档
    CommonResult<List<NetWorkL3DTO>> getNetWorkL3List(@RequestParam("typeName") String typeName);


    @PostMapping(PREFIX + "/deleteNetWorkL3ByNameList") // ③ Spring MVC 接口注解
    @Operation(summary = "删除三级网络列表")
    int deleteNetWorkL3ByNameList(@RequestBody List<NetWorkL3DTO> netWorkL2DTOS);


    //==========================VPC路由器网络===================================

    @PostMapping(PREFIX + "/addNetWorkVPC") // ③ Spring MVC 接口注解
    @Operation(summary = "批量新增VPC路由器信息")
        // ② Swagger 接口文档
    CommonResult<Boolean> addNetWorkVPC(@RequestBody List<NetWorkVpcDTO> reqDTO);


    @PostMapping(PREFIX + "/getNetWorkVPCCount") // ③ Spring MVC 接口注解
    @Operation(summary = "获取vpc路由器数量")
        // ② Swagger 接口文档
    Long getNetWorkVpcCount();

    @PostMapping(PREFIX + "/updateNetWorkVpc") // ③ Spring MVC 接口注解
    @Operation(summary = "获取更新路由器信息")
        // ② Swagger 接口文档
    CommonResult<Boolean> updateNetWorkVpc(@RequestBody List<NetWorkVpcDTO> netWorkVpcDTOS);

    @PostMapping(PREFIX + "/getNetWorkVpcList") // ③ Spring MVC 接口注解
    @Operation(summary = "获取vpc路由器列表")
        // ② Swagger 接口文档
    CommonResult<List<NetWorkVpcDTO>> getNetWorkVpcList();

    @PostMapping(PREFIX + "/deleteNetWorkVPCByNameList") // ③ Spring MVC 接口注解
    @Operation(summary = "删除VPC列表")
    int deleteNetWorkVPCByNameList(@RequestBody List<NetWorkVpcDTO> netWorkVpcDTOS);

    @PostMapping(PREFIX + "/getNetworkL2ByPlatformId")
    @Operation(summary = "按平台获取二层网络")
    CommonResult<List<NetWorkL2DTO>> getNetworkL2ByPlatformId(@RequestParam("platformId") Long platformId);

    @PostMapping(PREFIX + "/getNetworkL3ByPlatformId")
    @Operation(summary = "按平台获取三层网络")
    CommonResult<List<NetWorkL3DTO>> getNetworkL3ByPlatformId(@RequestParam("platformId") Long platformId);
}
