package cn.iocoder.zj.module.monitor.api.network.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName : NetWorkL3DTO  //类名
 * @Description : 三级网络  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/8/7  18:05
 */
@Schema(description = "RPC 服务 - 二级网络DTO")
@Data
public class NetWorkL3DTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 三级网络uuid
     */
    private String uuid;
    /**
     * 二级网络名称
     */
    private String l2NetworkName;
    /**
     * 二级网络uuid
     */
    private String l2NetworkUuid;
    /**
     * 三级网络名称
     */
    private String name;
    /**
     * dns 逗号分割
     */
    private String dns;
    /**
     * 网络资源类型（L3BasicNetwork 公有网络， L3VpcNetwork  vpc 网络 ）
     */
    private String type;
    /**
     * 网络服务 逗号分割
     */
    private String networkServices;
    /**
     * 起始ip
     */
    private String startIp;
    /**
     * 结束ip
     */
    private String endIp;
    /**
     * 子网掩码
     */
    private String netmask;
    /**
     * 网关
     */
    private String gateway;
    /**
     * 网段名称
     */
    private String networkSegment;
    /**
     * IPv4 CIDR
     */
    private String networkCidr;
    /**
     * IPV4 DHCP
     */
    private String nextHopIp;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;

    private Long tenantId;

    private Date createTime;

    private String typeName;
}
