package cn.iocoder.zj.module.monitor.api.alarm.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
public class AlarmInfoDTO {
    @Schema(description = "处理人")
    private String processors;

    @Schema(description = "处理时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime processingTime;

    @Schema(description = "处理详情")
    private String processingDetails;

    @Schema(description = "文件地址")
    private String fileAddress;

    @Schema(description = "处理状态   0:自行处理，1：工单处理")
    private Integer processingType;

    @Schema(description = "监控告警id")
    private String alarmId;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;
}
