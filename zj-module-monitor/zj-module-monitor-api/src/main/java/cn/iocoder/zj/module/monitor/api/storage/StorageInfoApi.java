package cn.iocoder.zj.module.monitor.api.storage;


import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.storage.dto.StorageRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.enums.ApiConstants;
import cn.iocoder.zj.module.monitor.pojo.AssetReqVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME) // ① @FeignClient 注解
@Tag(name = "RPC 服务 - 存储相关") // ② Swagger 接口文档
public interface StorageInfoApi {
    String PREFIX = ApiConstants.PREFIX + "/zstack";

    @PostMapping(PREFIX + "/addstorage") // ③ Spring MVC 接口注解
    @Operation(summary = "批量新增存储信息")  // ② Swagger 接口文档
    CommonResult<Boolean> adds(@RequestBody List<StorageRespCreateReqDTO> reqDTO);

    @PostMapping(PREFIX + "/getstoragecount") // ③ Spring MVC 接口注解
    @Operation(summary = "获取存储数量")  // ② Swagger 接口文档
    int count(@RequestParam("typeName") String typeName);

    @PostMapping(PREFIX + "/updatestorage") // ③ Spring MVC 接口注解
    @Operation(summary = "获取更新存储信息")  // ② Swagger 接口文档
    CommonResult<Boolean> updates(@RequestBody List<StorageRespCreateReqDTO> storageRespCreateReqDTOS);

    @GetMapping(PREFIX + "/getstorageall") // ③ Spring MVC 接口注解
    @Operation(summary = "获取存储列表")  // ② Swagger 接口文档
    CommonResult<List<StorageRespCreateReqDTO>> getAll(@RequestParam("typeName") String typeName);

    @PostMapping(PREFIX + "/deleteStorageList") // ③ Spring MVC 接口注解
    @Operation(summary = "删除存储列表")
    int deleteStorageList(@RequestBody List<StorageRespCreateReqDTO> netWorkL2DTOS);

    @PostMapping(PREFIX + "/getstorageListAll") // ③ Spring MVC 接口注解
    @Operation(summary = "获取所有存储列表")  // ② Swagger 接口文档
    CommonResult<List<StorageRespCreateReqDTO>> getListAll();

    @PostMapping(PREFIX + "/getStoragesByTenantOrPlatforms") // ③ Spring MVC 接口注解
    @Operation(summary = "按照租户ID获取存储信息")
    CommonResult<List<StorageRespCreateReqDTO>> getStoragesByTenantOrPlatforms(@RequestBody AssetReqVO assetReqVO);

    @PostMapping(PREFIX + "/getstorageByPlatformId") // ③ Spring MVC 接口注解
    @Operation(summary = "按平台获取宿主机")
    List<StorageRespCreateReqDTO> getStorageByPlatformId( @RequestParam("id") Long id);
}
