package cn.iocoder.zj.module.monitor.api.hostinfo.dto;

import cn.iocoder.zj.module.monitor.api.secgroup.dto.SecgroupRespDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @ClassName : HostInfoRespCreateReqDTO  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/30  15:46
 */

@Schema(description = "RPC 服务 - Zstack 创建主机信息 Request DTO")
@Data
public class HostInfoRespCreateReqDTO {

    /**
     * 主键
     */
    private Long id;

     /**
     * 主机id
     */
    private String uuid;
    /**
     * 主机名称
     */
    private String name;
    /**
     * 主机状态
     */
    private String state;
    /**
     * ip
     */
    private String ip;
    /**
     * 弹性ip
     */
    private String vipIp;
    /**
     * 区域id
     */
    private String zoneUuid;
    /**
     * 集群id
     */
    private String clusterUuid;
    /**
     * 宿主机id
     */
    private String hardwareUuid;
    /**
     * CPU架构
     */
    private String architecture;
    /**
     * 操作系统类型
     */
    private String guestOsType;
    /**
     * 主机创建时间
     */
    private Date vCreateDate;

    /**
     * 主机更新时间
     */
    private Date vUpdateDate;

    /**
     * 硬盘使用率
     */
    private BigDecimal diskUsed;

    /**
     * cpu 使用率
     */
    private BigDecimal cpuUsed;

    /**
     * 内存使用率
     */
    private BigDecimal memoryUsed;

    /**
     * 主机类型
     */
    private String type;
    /**
     * 分配内存
     */
    private Long memorySize;
    /**
     * 分配cpu
     */
    private Integer cpuNum;
    /**
     * mac 地址
     */
    private String mac;

    private String imageUuid;

    private String imageName;

    /**
     * 租户id
     */
    private Long tenantId;

    private Long regionId;

    private String zoneName;

    private String clusterName;

    private String hardwareName;

    private BigDecimal networkInBytes;

    private BigDecimal networkOutBytes;

    private BigDecimal diskUsedBytes;

    private BigDecimal diskFreeBytes;

    private BigDecimal totalDiskCapacity;

    private BigDecimal actualSize;

    private BigDecimal cloudSize;

    private BigDecimal networkInPackets;

    private BigDecimal networkOutPackets;



    private Long platformId;

    private String platformName;
    private Integer deleted;

    private String typeName;
    private String vms;

    /**
     * 电源状态
     */
    private String powerState;

    private String iso;

    private String autoInitType;

    private String guideMode;

    //云主机快照数量
    private Integer snapshotCount;

    //安全组
    private List<SecgroupRespDto> secgroupDataList;

}
