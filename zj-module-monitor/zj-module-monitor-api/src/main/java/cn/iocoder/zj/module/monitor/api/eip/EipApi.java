package cn.iocoder.zj.module.monitor.api.eip;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.eip.dto.EipCreateReqDto;
import cn.iocoder.zj.module.monitor.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME) // ① @FeignClient 注解
@Tag(name = "RPC 服务 - 弹性公网eip相关") // ② Swagger 接口文档
public interface EipApi {
    String PREFIX = ApiConstants.PREFIX + "/zstack";

    @PostMapping(PREFIX + "/addEips")
    @Operation(summary = "批量新增Eip")
        // ② Swagger 接口文档
    CommonResult<Boolean> addEips(@RequestBody List<EipCreateReqDto> reqDTO);

    @PostMapping(PREFIX + "/updateEips") // ③ Spring MVC 接口注解
    @Operation(summary = "更新云主机网卡信息")
        // ② Swagger 接口文档
    CommonResult<Boolean> updateEips(@RequestBody List<EipCreateReqDto> eipCreateReqDto);

    @PostMapping(PREFIX + "/delEips")
    @Operation(summary = "删除云主机网卡信息")
    CommonResult<Boolean> delEips(@RequestBody List<EipCreateReqDto> eipCreateReqDto);

    @PostMapping(PREFIX + "/getEipsByPlatformId")
    @Operation(summary = "查询云主机网卡信息")
    CommonResult<List<EipCreateReqDto>> getEipsByPlatformId(@RequestParam("platformId") Long platformId);

}
