package cn.iocoder.zj.module.monitor.api.tag;


import cn.iocoder.zj.module.monitor.api.tag.dto.TagRespDTO;
import cn.iocoder.zj.module.monitor.api.tag.dto.TaggablesDTO;
import cn.iocoder.zj.module.monitor.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Tag(name = "RPC 服务 - 标签服务")
public interface MonitorTagApi {
    String PREFIX = ApiConstants.PREFIX + "/tag";

    @GetMapping(PREFIX + "/getByTaggable")
    @Operation(summary = "通过资源查询标签")
    @Parameter(name = "id", description = "资源id", example = "1", required = true)
    @Parameter(name = "type", description = "资源类型", example = "instance", required = true)
    List<TagRespDTO> getByTaggable(@RequestParam("id") Long id, @RequestParam("type") String type);


    @GetMapping(PREFIX + "/getByTag")
    @Operation(summary = "通过标签查询资源")
    @Parameter(name = "id", description = "资源id", example = "1", required = true)
    @Parameter(name = "type", description = "资源类型", example = "instance", required = true)
    List<TaggablesDTO> getByTag(@RequestParam("id") Long id, @RequestParam("type") String type);


    @GetMapping(PREFIX + "/add")
    @Operation(summary = "标签新增")
    @Parameter(name = "id", description = "资源id", example = "1", required = true)
    @Parameter(name = "type", description = "资源类型", example = "instance", required = true)
    @Parameter(name = "monitorTags", description = "标签id集合", example = "1,2,3,4", required = true)
    void add(@RequestParam("id") Long id, @RequestParam("type") String type,@RequestParam("monitorTags") String monitorTags);


    @GetMapping(PREFIX + "/del")
    @Operation(summary = "标签新增")
    @Parameter(name = "id", description = "资源id", example = "1", required = true)
    void del(@RequestParam("id") Long id);

}
