package cn.iocoder.zj.module.monitor.api.valume.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @ClassName : VolumeDTO  //类名
 * @Description : 云盘信息DTO  //描述
 */
@Data
public class VolumeDTO {

    @Schema(description = "云盘uuid")
    private String uuid;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "云盘名称")
    private String name;

    @Schema(description = "云盘详细描述")
    private String description;

    @Schema(description = "主存储uuid")
    private String primaryStorageUuid;

    @Schema(description = "主存储名称")
    private String primaryStorageName;

    @Schema(description = "主存储类型")
    private String primaryStorageType;

    @Schema(description = "云主机uuid")
    private String vmInstanceUuid;

    @Schema(description = "云主机名称")
    private String vmInstanceName;

    @Schema(description = "云盘类型，数据云盘/根云盘")
    private String type;

    @Schema(description = "云盘格式")
    private String format;

    @Schema(description = "云盘大小")
    private Long size;

    @Schema(description = "云盘真实大小")
    private Long actualSize;

    @Schema(description = "云盘是否开启")
    private String state;

    @Schema(description = "云盘状态")
    private String status;

    @Schema(description = "云盘使用率")
    private String actualRatio;

    @Schema(description = "云盘空闲大小")
    private Long actualFree;

    @Schema(description = "云盘已用大小")
    private Long actualUse;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "删除状态")
    private Long deleted;

    @Schema(description = "云盘创建时间")
    private Date vCreateDate;

    @Schema(description = "云盘更新时间")
    private Date vUpdateDate;

    // 最大iops max_iops
    @Schema(description = "最大iops")
    private Long maxIops;

    //吞吐量
    @Schema(description = "吞吐量")
    private BigDecimal throughput;

    //介质类型
    @Schema(description = "介质类型")
    private String mediaType;

    @Schema(description = "是否挂载")
    private Boolean isMount;

}
