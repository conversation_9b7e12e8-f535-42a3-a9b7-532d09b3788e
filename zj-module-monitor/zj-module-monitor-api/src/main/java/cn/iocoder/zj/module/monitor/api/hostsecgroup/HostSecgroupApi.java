package cn.iocoder.zj.module.monitor.api.hostsecgroup;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.hostsecgroup.dto.HostSecgroupCreateReqDto;
import cn.iocoder.zj.module.monitor.api.secgroup.dto.SecgroupCreateReqDto;
import cn.iocoder.zj.module.monitor.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME) // ① @FeignClient 注解
@Tag(name = "RPC 服务 - 云主机安全组相关") // ② Swagger 接口文档
public interface HostSecgroupApi {
    String PREFIX = ApiConstants.PREFIX + "/zstack";

    @PostMapping(PREFIX + "/addHostSecgroups")
    @Operation(summary = "批量新增云主机安全组")
        // ② Swagger 接口文档
    CommonResult<Boolean> addHostSecgroups(@RequestBody List<HostSecgroupCreateReqDto> reqDTO);

    @PostMapping(PREFIX + "/updateHostSecgroups") // ③ Spring MVC 接口注解
    @Operation(summary = "更新云主机安全组信息")  // ② Swagger 接口文档
    CommonResult<Boolean> updateHostSecgroups(@RequestBody List<HostSecgroupCreateReqDto> hostSecgroupCreateReqDtos);

    @PostMapping(PREFIX + "/delHostSecgroups")
    @Operation(summary = "删除云主机安全组信息")
    CommonResult<Boolean> delHostSecgroups(@RequestBody List<HostSecgroupCreateReqDto> hostSecgroupCreateReqDtos);

    @PostMapping(PREFIX + "/getHostSecgroupsByPlatformId")
    @Operation(summary = "查询云主机安全组信息")
    CommonResult<List<HostSecgroupCreateReqDto>> getHostSecgroupsByPlatformId(@RequestParam("platformId") Long platformId);

}
