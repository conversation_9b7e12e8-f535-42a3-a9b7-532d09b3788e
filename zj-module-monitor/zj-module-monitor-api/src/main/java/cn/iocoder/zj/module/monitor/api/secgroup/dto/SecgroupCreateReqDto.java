package cn.iocoder.zj.module.monitor.api.secgroup.dto;

import lombok.Data;

@Data
public class SecgroupCreateReqDto {
    /**
     * id
     */
    private Long id;
    /**
     * uuid
     */
    private String uuid;
    /**
     * 名称
     */
    private String name;
    /**
     * 状态
     */
    private String status;
    /**
     * 是否共享
     */
    private Boolean isPublic;
    /**
     * 默认共享范围
     */
    private String publicScope;
    /**
     * 共享设置的来源, local: 本地设置, cloud: 从云上同步过来
     */
    private String publicSrc;
    /**
     * 是否垃圾
     */
    private Boolean isDirty;
    /**
     * 区域id
     */
    private String cloudregionId;
    /**
     * vpcId
     */
    private String vpcId;
    /**
     * 全局vpcId
     */
    private String globalvpcId;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;

    private String description;
}
