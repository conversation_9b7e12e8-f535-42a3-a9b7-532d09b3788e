package cn.iocoder.zj.module.monitor.api.hostnic.dto;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class HostNicRespDTO {
    private Long id;
    /**
     * uuid
     */
    private String uuid;
    /**
     * 云主机uuid
     */
    private String hostUuid;
    /**
     * ipV6
     */
    private String ip6;
    /**
     * ip
     */
    private String ip;
    /**
     * mac
     */
    private String mac;
    /**
     * 驱动
     */
    private String driver;
    /**
     * 在经典网络
     */
    private Byte inClassicNetwork;
    /**
     * 网络uuid
     */
    private String networkUuid;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;

    private LocalDateTime createTime;
    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;

    private Integer deleted;
}
