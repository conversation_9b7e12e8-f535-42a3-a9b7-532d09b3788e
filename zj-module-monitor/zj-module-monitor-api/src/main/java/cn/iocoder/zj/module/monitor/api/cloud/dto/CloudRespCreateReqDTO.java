package cn.iocoder.zj.module.monitor.api.cloud.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class CloudRespCreateReqDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 主机id
     */
    private String uuid;
    /**
     * 主机名称
     */
    private String name;
    /**
     * 主机状态
     */
    private String state;
    /**
     * ip
     */
    private String ip;
    /**
     * 弹性ip
     */
    private String vipIp;
    /**
     * 集群id
     */
    private String clusterUuid;
    /**
     * CPU架构
     */
    private String architecture;
    /**
     * 操作系统类型
     */
    private String guestOsType;
    /**
     * 主机创建时间
     */
    private Date vCreateDate;

    /**
     * 硬盘使用率
     */
    private BigDecimal diskUsed;

    /**
     * cpu 使用率
     */
    private BigDecimal cpuUsed;

    /**
     * 内存使用率
     */
    private BigDecimal memoryUsed;

    /**
     * 主机类型
     */
    private String type;
    /**
     * 分配内存
     */
    private Long memorySize;
    /**
     * 分配cpu
     */
    private Integer cpuNum;
    /**
     * mac 地址
     */
    private String mac;

    /**
     * 租户id
     */
    private Long tenantId;

    private Long regionId;

    private String clusterName;

    private BigDecimal networkInBytes;

    private BigDecimal networkOutBytes;

    private BigDecimal diskUsedBytes;

    private BigDecimal diskFreeBytes;

    private BigDecimal totalDiskCapacity;

    private BigDecimal actualSize;

    private BigDecimal cloudSize;

    private BigDecimal networkInPackets;

    private BigDecimal networkOutPackets;



    private Long platformId;

    private String platformName;
    private Integer deleted;

    private String typeName;
    private String vms;

}
