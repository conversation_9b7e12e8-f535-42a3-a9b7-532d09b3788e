package cn.iocoder.zj.module.monitor.api.imageinfo;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.imageinfo.dto.ImageInfoCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeSnapshotDTO;
import cn.iocoder.zj.module.monitor.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 镜像信息")
public interface ImageInfoApi {

    String PREFIX = ApiConstants.PREFIX + "/zstack";

    @PostMapping(PREFIX + "/batchCreate")
    @Operation(summary = "批量创建镜像信息")
    CommonResult<Boolean> batchCreateImageInfo(@RequestBody List<ImageInfoCreateReqDTO> createReqDTOs);

    @PostMapping(PREFIX + "/batchUpdate")
    @Operation(summary = "批量更新镜像信息")
    CommonResult<Boolean> batchUpdateImageInfo(@RequestBody List<ImageInfoCreateReqDTO> updateReqDTOs);

    @PostMapping(PREFIX + "/batchDelete")
    @Operation(summary = "批量删除镜像信息")
    CommonResult<Boolean> batchDeleteImageInfo(@RequestBody List<ImageInfoCreateReqDTO> deleteReqDTOs);

    @PostMapping(PREFIX + "/getImageInfoByPlatformId")
    @Operation(summary = "按平台获取镜像信息")
    CommonResult<List<ImageInfoCreateReqDTO>> getImageInfoByPlatformId(@RequestBody Long platformId);

    @PostMapping(PREFIX + "/getAllImagesByTypeCode") // ③ Spring MVC 接口注解
    @Operation(summary = "按类型获取所有镜像信息")
    CommonResult<List<ImageInfoCreateReqDTO>> getAllImagesByTypeCode(@RequestParam("typeCode")String typeCode);

}