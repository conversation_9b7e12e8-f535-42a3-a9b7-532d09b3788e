package cn.iocoder.zj.module.monitor.api.storage.dto;

import cn.hutool.core.date.DateUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName : StorageRespCreateReqDTO  //类名
 * @Description : 存储信息  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/6/6  14:42
 */

@Schema(description = "RPC 服务 - Zstack 创建存储信息 Request DTO")
@Data
public class StorageRespCreateReqDTO {

    /**
     * 逐渐
     */
    private Long id;
    /**
     * 存储名称
     */
    private String name;
    /**
     * 存储uuid
     */
    private String uuid;
    /**
     * url
     */
    private String url;
    /**
     * 状态
     */
    private String state;
    /**
     * 类型：Ceph
     */
    private String type;
    /**
     * 状态，页面展示值（Connected：已连接，DisConnected: 未连接）
     */
    private String status;
    /**
     * 容量使用率
     */
    private BigDecimal capacityUtilization;

    /**
     * 总容量
     */
    private Long totalCapacity;
    /**
     * 已用容量
     */
    private Long usedCapacity;
    /**
     * 地区id
     */
    private Long regionId;

    /**
     * 租户id
     */
    private Long tenantId;

    private BigDecimal availablePhysicalCapacity;

    private BigDecimal totalPhysicalCapacity;

    private BigDecimal availableCapacity;

    // 平台配置id
    private Long platformId;
    // 平台配置名称
    private String platformName;

    private Integer deleted;

    private String typeName;
    private String clusterName;
    private String clusterUuid;

    private Date createTime;

    private Date sCreateTime;


    //介质类型
    private String  mediaType;
    //介质类型ID
    private Long  mediaTypeId;

    //虚拟容量
    private BigDecimal virtualCapacity;

    //超售比
    private BigDecimal storagePercent;

    //分配率
    private BigDecimal commitRate;

    //分配
    private BigDecimal allocation;

    //区域
    private String manager;

    //可用区域
    private String availableManager;

//    //分配率
//    private BigDecimal distributionRate;

    //预留容量
    private BigDecimal reserveCapacity;

    //浪费容量
    private BigDecimal wasteCapacity;

    //更新时间
    private Date vUpdateTime;

    //备注
    private String remark;
}
