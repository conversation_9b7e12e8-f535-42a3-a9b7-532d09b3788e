package cn.iocoder.zj.module.monitor.api.network.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName : NetWorkVpcDTO  //类名
 * @Description : VPC路由器  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/8/9  14:09
 */
@Schema(description = "RPC 服务 - 二级网络DTO")
@Data
public class NetWorkVpcDTO {
    /**
     * 主键
     */
    private Long id;
    /**
     * vpc 路由器uuid
     */
    private String uuid;
    /**
     * 路由器名称
     */
    private String name;
    /**
     * cpu
     */
    private Integer cpuNum;
    /**
     * 内存
     */
    private Long memorySize;
    /**
     * CPU架构
     */
    private String architecture;
    /**
     * 路由dns
     */
    private String dns;
    /**
     * 就绪状态
     */
    private String status;
    /**
     * 启用状态
     */
    private String state;
    /**
     * L3网络uuid
     */
    private String l3NetworkUuid;
    /**
     * ipv4 Ip
     */
    private String ip;
    /**
     * 管理网络uuid
     */
    private String managementNetworkUuid;
    /**
     * 管理网络ip
     */
    private String managementNetworkIp;
    /**
     * 三层网络名称
     */
    private String l3NetworkName;
    /**
     * 集群uuid
     */
    private String clusterUuid;
    /**
     * 集群名称
     */
    private String clusterName;
    /**
     * 虚拟化技术
     */
    private String hypervisorType;
    /**
     * mac地址（IPV4)
     */
    private String mac;
    /**
     * 宿主机uuid
     */
    private String hostUuid;
    /**
     * 宿主机名称
     */
    private String hostName;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;

    private Long tenantId;

    private Date createTime;
}
