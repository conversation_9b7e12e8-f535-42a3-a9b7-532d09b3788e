package cn.iocoder.zj.module.monitor.api.secgrouprule.dto;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SecgroupRuleRespDto {
    /**
     * id
     */
    private Long id;
    /**
     * uuid
     */
    private String uuid;
    /**
     * 状态
     */
    private String status;
    /**
     * 安全组uuid
     */
    private String secgroupUuid;
    /**
     * 优先级
     */
    private Integer priority;
    /**
     * 协议
     */
    private String protocol;
    /**
     * 端口
     */
    private String ports;
    /**
     * 方向
     */
    private String direction;
    /**
     * cidr
     */
    private String cidr;
    /**
     * 策略
     */
    private String action;
    /**
     * 备注
     */
    private String description;

    private Long platformId;
    private String platformName;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建者，目前使用 SysUser 的 id 编号
     * <p>
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    private String creator;
    /**
     * 更新者，目前使用 SysUser 的 id 编号
     * <p>
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    private String updater;
}
