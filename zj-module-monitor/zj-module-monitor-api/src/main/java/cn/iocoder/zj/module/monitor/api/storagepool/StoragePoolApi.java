package cn.iocoder.zj.module.monitor.api.storagepool;

import cn.iocoder.zj.framework.common.pojo.CommonResult;

import cn.iocoder.zj.module.monitor.api.storagepool.dto.StoragePoolCreateRespDTO;
import cn.iocoder.zj.module.monitor.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME) // ① @FeignClient 注解
@Tag(name = "RPC 服务 - 存储池相关") // ② Swagger 接口文档
public interface StoragePoolApi {
    String PREFIX = ApiConstants.PREFIX + "/zstack";

    @PostMapping(PREFIX + "/addStoragePool") // ③ Spring MVC 接口注解
    @Operation(summary = "批量新增存储池网络信息")  // ② Swagger 接口文档
    CommonResult<Boolean> adds(@RequestBody List<StoragePoolCreateRespDTO> reqDTO);

    @PostMapping(PREFIX + "/updateStoragePool") // ③ Spring MVC 接口注解
    @Operation(summary = "获取更新存储池网络信息")  // ② Swagger 接口文档
    CommonResult<Boolean> updates(@RequestBody List<StoragePoolCreateRespDTO> reqDTO);

    @PostMapping(PREFIX + "/getStoragePoolByPlatformId") // ③ Spring MVC 接口注解
    @Operation(summary = "根据平台Id存储池网络信息")  // ② Swagger 接口文档
    CommonResult<List<StoragePoolCreateRespDTO>> getStoragePoolByPlatformId(@RequestParam("platformId")Long platformId);

    @PostMapping(PREFIX + "/deleteStoragePool") // ③ Spring MVC 接口注解
    @Operation(summary = "删除存储池网络信息")  // ② Swagger 接口文档
    int deletes(@RequestBody List<StoragePoolCreateRespDTO> reqDTO);
}
