package cn.iocoder.zj.module.monitor.enums;

import cn.iocoder.zj.framework.common.exception.ErrorCode;

/**
 * System 错误码枚举类
 * <p>
 * system 监控，使用 6-002-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== HostInfo 模块 6002000000 ==========
    ErrorCode HAVE_NO_PLATFORM = new ErrorCode(2001000000, "暂无平台数据");
    ErrorCode HOST_INFO_NOT_EXISTS = new ErrorCode(2002000000, "云主机基本信息不存在");

    ErrorCode HARDWARE_INFO_NOT_EXISTS = new ErrorCode(2003000000, "硬件设施基本信息不存在");
    ErrorCode HARDWARE_NIC_NOT_EXISTS = new ErrorCode(2003000001, "硬件网卡不存在");

    ErrorCode STORAGE_INFO_NOT_EXISTS = new ErrorCode(2004000000, "存储设备信息不存在");

    ErrorCode HOST_NIC_NOT_EXISTS = new ErrorCode(2005000000, "云主机网卡不存在");
    ErrorCode EIP_NOT_EXISTS = new ErrorCode(2006000000, "弹性公网不存在");
    ErrorCode SECGROUP_NOT_EXISTS = new ErrorCode(2007000000, "安全组不存在");
    // ========== AlarmHostRelation 模块 6003000000 ==========
    ErrorCode ALARM_HOST_RELATION_NOT_EXISTS = new ErrorCode(2005000000, "告警配置与云主机关联关系不存在");
    ErrorCode ALARM_NOTICE_NOT_EXISTS = new ErrorCode(2005000001, "告警通知不存在");

    // ========== 采集设备 2006000000 ==========
    ErrorCode GATHER_DEVICE_NOT_EXISTS = new ErrorCode(2006000000, "采集设备不存在");

    ErrorCode GATHER_DEVICE_IS_ASSET = new ErrorCode(2006000001, "采集设备下存在资产无法删除");

    // ========== 租户资产 2007000000 ==========
    ErrorCode GATHER_ASSET_NOT_EXISTS = new ErrorCode(2007000000, "租户资产不存在");

    // ========== 告警日志 2008000000  =========
    ErrorCode GATHER_LOGDETAIL_NOT_EXISTS = new ErrorCode(2008000000, "告警日志不存在");

    // ========== 监控资源拓扑图 =============
    ErrorCode TOPOLOGY_NOT_EXISTS = new ErrorCode(2009000000, "监控资源拓扑图不存在");

    ErrorCode TOPOLOGY_EXISTS = new ErrorCode(2009000001, "监控资源拓扑图存在");


    // ========== 二级网络信息  ===============
    ErrorCode NETWORK_L2_NOT_EXISTS = new ErrorCode(2010000001, "二级网络信息不存在");
    // ========== 三级网络资源 ==================
    ErrorCode NETWORK_L3_NOT_EXISTS = new ErrorCode(202000000, "三级网络资源不存在");

    // ========== VPC路由器  补充编号 ==============
    ErrorCode NETWORK_VPC_NOT_EXISTS = new ErrorCode(203000000, "VPC路由器不存在");

    // ========== 云盘快照信息 补充编号 ==========
    ErrorCode VOLUME_SNAPSHOT_NOT_EXISTS = new ErrorCode(204000000, "云盘快照信息不存在");
    // ========== 云盘信息 补充编号 ==========
    ErrorCode VOLUME_INFO_NOT_EXISTS = new ErrorCode(205000000, "云盘信息不存在");

    // ========== 告警配置 补充编号 ==========
    ErrorCode IMPORT_ALARM_CONFIG_IS_EMPTY = new ErrorCode(206000001, "导入告警配置数据不能为空！");
    ErrorCode ALARM_NAME_IS_EMPTY = new ErrorCode(206000002, "告警名称不能为空！");
    ErrorCode SOURCE_TYPE_IS_EMPTY = new ErrorCode(206000003, "资源类型不能为空！");
    ErrorCode ALARM_LEVEL_IS_EMPTY = new ErrorCode(206000004, "告警级别不能为空！");
    ErrorCode DICT_LABEL_TYPE_IS_EMPTY = new ErrorCode(206000005, "告警类型（字典值）不能为空！");
    ErrorCode DICT_LABEL_VALUE_IS_EMPTY = new ErrorCode(206000006, "告警条目（字典值）不能为空！");


    // ========== 监控告警详情 ==========
    ErrorCode ALARM_INFO_NOT_EXISTS = new ErrorCode(207000001, "监控告警详情不存在");

    // ========== 拓扑图关系  ==========
    ErrorCode TOPOLOGY_RELATION_NOT_EXISTS = new ErrorCode(208000001, "拓扑图关系不存在");

    // ========== 镜像信息 ===========
    ErrorCode IMAGE_INFO_NOT_EXISTS = new ErrorCode(209000001,"镜像信息不存在");

    // ========== 标签  ==========
    ErrorCode TAGS_NOT_EXISTS = new ErrorCode(210000001, "标签不存在");
    ErrorCode TAGS_NOT_DEL = new ErrorCode(210000003, "系统同步回来的标签暂不支持删除");
    ErrorCode TAGS_NAME_DUPLICATE = new ErrorCode(210000002, "标签名称重复");
    ErrorCode TAGGABLES_NOT_EXISTS = new ErrorCode(220000001, "标签绑定关系不存在");
    ErrorCode TAGGABLES_EXISTS = new ErrorCode(220000002, "标签绑定关系已存在");

    ErrorCode BULLETIN_NOT_EXISTS = new ErrorCode(230000001, "实时报表不存在");
    ErrorCode MEDIA_TYPE_NOT_EXISTS = new ErrorCode(240000001, "介质类型不存在");
}
