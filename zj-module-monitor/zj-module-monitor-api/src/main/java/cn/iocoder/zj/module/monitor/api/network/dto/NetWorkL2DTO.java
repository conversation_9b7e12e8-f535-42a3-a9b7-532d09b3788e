package cn.iocoder.zj.module.monitor.api.network.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName : NetWorkL2DTO  //类名
 * @Description : 二级网络类型DTO  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/8/7  10:53
 */

@Schema(description = "RPC 服务 - 二级网络DTO")
@Data
public class NetWorkL2DTO {
    /**
     * 主键
     */
    private Long id;
    /**
     * 二级网络名称
     */
    private String name;
    /**
     * 二级网络uuid
     */
    private String uuid;
    /**
     * 网卡
     */
    private String physicalInterface;
    /**
     * 二级网络类型
     */
    private String type;
    /**
     * vlan
     */
    private String vlan;
    /**
     * 虚拟网络标识
     */
    private Integer virtualNetworkId;
    /**
     * 地区名称
     */
    private String regionName;
    /**
     * 地区id
     */
    private Long regionId;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;

    private Long tenantId;

    private String typeName;

    private Date createTime;
}
