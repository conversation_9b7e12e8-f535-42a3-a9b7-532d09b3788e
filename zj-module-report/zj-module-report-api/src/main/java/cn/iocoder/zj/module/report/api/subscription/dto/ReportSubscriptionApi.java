package cn.iocoder.zj.module.report.api.subscription.dto;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.report.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

@FeignClient(name = ApiConstants.NAME) // ① @FeignClient 注解
@Tag(name = "RPC 服务 - 告警配置相关") // ② Swagger 接口文档

public interface ReportSubscriptionApi {
    String PREFIX = ApiConstants.PREFIX + "/subscription";

    @PostMapping(PREFIX + "/getAlarmSubscription") // ③ Spring MVC 接口注解
    @Operation(summary = "获取告警订阅消息的任务")  // ② Swagger 接口文档
    CommonResult<List<ReportSubscriptionDTO>> getAlarmSubscription();

    @PostMapping(PREFIX + "/removeTimedTask") // ③ Spring MVC 接口注解
    @Operation(summary = "移除用户的所有订阅任务")  // ② Swagger 接口文档
    CommonResult<Boolean> removeTimedTask(Long userId);
}