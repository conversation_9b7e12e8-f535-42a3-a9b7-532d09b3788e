<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.report.dal.mysql.zjreport.ZjreportMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->



    <select id="listBpmProcessList" resultType="java.util.Map">
        SELECT count(*) count,sourceId from (SELECT JSON_UNQUOTE(JSON_EXTRACT(form_variables, '$.sourceId')) AS sourceId
                                             FROM bpm_process_instance_ext where  deleted = 0 and tenant_id = #{tenantId}
                                            ) d
        WHERE sourceId is not null
        GROUP BY sourceId
    </select>

    <select id="getAlertList" resultType="java.util.Map">
        SELECT sum(times) count,monitor_id,monitor_name,app,resource_type FROM hzb_alert
        <if test="platformList.size >0">
            where
             platform_id in
            <foreach collection="platformList" item="platform" open="(" close=")" separator=",">
                #{platform.platformId}
            </foreach>
        </if>
        GROUP BY monitor_id,monitor_name,app,resource_type
        ORDER BY count desc
    </select>

    <select id="selectReportPage" resultType="java.util.Map"
            parameterType="com.baomidou.mybatisplus.extension.plugins.pagination.Page">
        SELECT uuid, name, platform_name, platform_id
        FROM monitor_host_info
        WHERE deleted = 0
        UNION ALL
        SELECT CONVERT(uuid USING utf8mb4) COLLATE utf8mb4_unicode_ci,
               CONVERT(name USING utf8mb4) COLLATE utf8mb4_unicode_ci,
               platform_name,
               platform_id
        FROM monitor_storage_info
        WHERE deleted = 0
        UNION ALL
        SELECT CONVERT(uuid USING utf8mb4) COLLATE utf8mb4_unicode_ci,
               CONVERT(name USING utf8mb4) COLLATE utf8mb4_unicode_ci,
               platform_name,
               platform_id
        FROM monitor_hardware_info
        WHERE deleted = 0
        UNION ALL
        SELECT CONVERT(CAST(id AS CHAR(50)) USING utf8mb4) COLLATE utf8mb4_unicode_ci,
               name,
               platform_name,
               platform_id
        FROM hzb_monitor
    </select>

    <select id="selectMyzjreportPage"
            resultType="cn.iocoder.zj.module.report.controller.admin.zjreport.vo.ZjReportPageReqVO"
            parameterType="com.baomidou.mybatisplus.extension.plugins.pagination.Page">

        <choose>
            <when test="pageVO.app=='host'">
                (SELECT uuid,
                cast(name as char character set utf8) AS name,
                platform_name                         AS platformName,
                platform_id                           AS platformId,
                "host" as app
                FROM monitor_host_info
                WHERE deleted = 0  and state != 'Destroyed'
                <if test="platformList.size >0">
                    and platform_id in
                    <foreach collection="platformList" item="platform" open="(" close=")" separator=",">
                        #{platform.platformId}
                    </foreach>
                </if>
                <if test="pageVO.name!= null and pageVO.name!=''">
                AND name like concat("%",#{pageVO.name},"%")
            </if>
                <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                    AND platform_id like concat("%",#{pageVO.platformId},"%")
                </if>
                )
            </when>

            <when test="pageVO.app=='hardware'">
                (SELECT uuid,
                cast(name as char character set utf8) AS name,
                platform_name                         AS platformName,
                platform_id                           AS platformId,
                "hardware" as app
                FROM monitor_hardware_info
                WHERE deleted = 0
                <if test="platformList.size >0">
                    and platform_id in
                    <foreach collection="platformList" item="platform" open="(" close=")" separator=",">
                        #{platform.platformId}
                    </foreach>
                </if>
                <if test="pageVO.name!= null and pageVO.name!=''">
                    AND name like concat("%",#{pageVO.name},"%")
                </if>
                <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                    AND platform_id like concat("%",#{pageVO.platformId},"%")
                </if>
                )
            </when>
            <when test="pageVO.app=='storage'">
                (SELECT uuid,
                cast(name as char character set utf8) AS name,
                platform_name AS platformName,
                platform_id   AS platformId,
                "storage" as app
                FROM monitor_storage_info
                WHERE deleted = 0
                <if test="pageVO.name!= null and pageVO.name!=''">
                    AND name like concat("%",#{pageVO.name},"%")
                </if>
                <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                    AND platform_id like concat("%",#{pageVO.platformId},"%")
                </if>
                <if test="platformList.size >0">
                    and platform_id in
                    <foreach collection="platformList" item="platform" open="(" close=")" separator=",">
                        #{platform.platformId}
                    </foreach>
                </if>)
            </when>
            <when test="pageVO.app=='hz'">
                (SELECT CAST(id AS CHAR(50) character set utf8) as uuid,
                name,
                platform_name                           AS platformName,
                platform_id                             AS platformIdm,
                app
                FROM hzb_monitor
                where app in
                <foreach close=")" collection="subEnums" item="item" open="(" separator=", ">
                    #{item.value}
                </foreach>
                <if test="pageVO.name!= null and pageVO.name!=''">
                    AND name like concat("%",#{pageVO.name},"%")
                </if>
                <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                    AND platform_id like concat("%",#{pageVO.platformId},"%")
                </if>
                <if test="platformList.size >0">
                    and platform_id in
                    <foreach collection="platformList" item="platform" open="(" close=")" separator=",">
                        #{platform.platformId}
                    </foreach>
                </if>
                )
            </when>
            <otherwise>
                select *
                from ((SELECT uuid,
                cast(name as char character set utf8) AS name,
                platform_name                         AS platformName,
                platform_id                           AS platformId,
                "host" as app
                FROM monitor_host_info
                WHERE deleted = 0  and state != 'Destroyed'
                <if test="platformList.size >0">
                    and platform_id in
                    <foreach collection="platformList" item="platform" open="(" close=")" separator=",">
                        #{platform.platformId}
                    </foreach>
                </if>
                <if test="pageVO.name!= null and pageVO.name!=''">
                AND name like concat("%",#{pageVO.name},"%")
            </if>
                <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                    AND platform_id like concat("%",#{pageVO.platformId},"%")
                </if>
                )

                UNION ALL

                (SELECT uuid,
                cast(name as char character set utf8) AS name,
                platform_name AS platformName,
                platform_id   AS platformId,
                "storage" as app
                FROM monitor_storage_info
                WHERE deleted = 0
                <if test="platformList.size >0">
                    and platform_id in
                    <foreach collection="platformList" item="platform" open="(" close=")" separator=",">
                        #{platform.platformId}
                    </foreach>
                </if>
                <if test="pageVO.name!= null and pageVO.name!=''">
                    AND name like concat("%",#{pageVO.name},"%")
                </if>
                <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                    AND platform_id like concat("%",#{pageVO.platformId},"%")
                </if>
                )

                UNION ALL

                (SELECT uuid,

                cast(name as char character set utf8) AS name,
                platform_name                         AS platformName,
                platform_id                           AS platformId,
                "hardware" as app
                FROM monitor_hardware_info
                WHERE deleted = 0
                <if test="platformList.size >0">
                    and platform_id in
                    <foreach collection="platformList" item="platform" open="(" close=")" separator=",">
                        #{platform.platformId}
                    </foreach>
                </if>
                <if test="pageVO.name!= null and pageVO.name!=''">
                    AND name like concat("%",#{pageVO.name},"%")
                </if>
                <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                    AND platform_id like concat("%",#{pageVO.platformId},"%")
                </if>
                )

                UNION ALL

                (SELECT CAST(id AS CHAR(50) character set utf8) as uuid,
                name,
                platform_name                           AS platformName,
                platform_id                             AS platformIdm,
                app
                FROM hzb_monitor
                <where>
                    <if test="pageVO.name!= null and pageVO.name!=''">
                        name like concat("%",#{pageVO.name},"%")
                    </if>
                    <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                        AND platform_id like concat("%",#{pageVO.platformId},"%")
                    </if>
                    <if test="platformList.size >0">
                        and platform_id in
                        <foreach collection="platformList" item="platform" open="(" close=")" separator=",">
                            #{platform.platformId}
                        </foreach>
                    </if>
                </where>
                )) as t
            </otherwise>
        </choose>
    </select>

    <select id="getAlarmReport" resultType="java.util.Map">
        select mix.*
        from (
        SELECT
            monitor_id,
            monitor_name,
            MAX(platform_name) platform_name,
            app AS appType,
            SUM( times ) times,
            MAX( platform_id ) platformId,
            priority priority,
            content content
        FROM
            hzb_alert
        WHERE
            platform_id IS NOT NULL
          AND resource_type = 1
        <if test="mapList.size>0">
            and platform_id in
            <foreach
                    collection="mapList" open="(" separator="," close=")" item="platform">
                #{platform.platformId}
            </foreach>
        </if>
        GROUP BY
            monitor_id,
            monitor_name,
            content,
            alarm_id,
            priority,
            app UNION ALL
        SELECT
            monitor_id,
            monitor_name,
            MAX(platform_name) platform_name,
            app AS appType,
            SUM( times ) times,
            MAX( platform_id ) platformId,
            priority priority,
            MAX( content ) content
        FROM
            hzb_alert
        WHERE
            platform_id IS NOT NULL
          AND resource_type = 0
        <if test="mapList.size>0">
            and platform_id in
            <foreach
                    collection="mapList" open="(" separator="," close=")" item="platform">
                #{platform.platformId}
            </foreach>
        </if>
        GROUP BY
            monitor_id,
            monitor_name,
            alarm_id,
            priority,
            app) mix
        order by
        mix.monitor_name,mix.monitor_id
    </select>

    <select id="getAlarmReports" resultType="java.util.Map">
        select mix.*
        from (
        SELECT
        monitor_id,
        monitor_name,
        MAX(platform_name) platform_name,
        app AS appType,
        SUM( times ) times,
        MAX( platform_id ) platformId,
        priority priority,
        content content
        FROM
        hzb_alert
        WHERE
        platform_id IS NOT NULL
        AND resource_type = 1
        <if test="mapList.size>0">
            and platform_id in
            <foreach
                    collection="mapList" open="(" separator="," close=")" item="platform">
                #{platform.platformId}
            </foreach>
        </if>
        <if test="subEnums.size>0">
        AND app in
        <foreach close=")" collection="subEnums" item="item" open="(" separator=", ">
            #{item.value}
        </foreach>
        </if>
        GROUP BY
        monitor_id,
        monitor_name,
        content,
        alarm_id,
        priority,
        app UNION ALL
        SELECT
        monitor_id,
        monitor_name,
        MAX(platform_name) platform_name,
        app AS appType,
        SUM( times ) times,
        MAX( platform_id ) platformId,
        priority priority,
        MAX( content ) content
        FROM
        hzb_alert
        WHERE
        platform_id IS NOT NULL
        AND resource_type = 0
        <if test="mapList.size>0">
            and platform_id in
            <foreach
                    collection="mapList" open="(" separator="," close=")" item="platform">
                #{platform.platformId}
            </foreach>
        </if>
        <if test="subEnums.size>0">
            AND app in
            <foreach close=")" collection="subEnums" item="item" open="(" separator=", ">
                #{item.value}
            </foreach>
        </if>
        GROUP BY
        monitor_id,
        monitor_name,
        alarm_id,
        priority,
        app) mix
        WHERE 1=1
        <if test="monitorName != null and monitorName!= ''">
            AND mix.monitor_name LIKE concat('%', #{monitorName}, '%')
        </if>
        <if test="appType != null and appType!= ''">
            AND mix.appType= #{appType}
        </if>
        <if test="priority != null">
            AND mix.priority= #{priority}
        </if>
        <if test="platformId != null and platformId!= ''">
            AND mix.platformId= #{platformId}
        </if>
        order by
        <if test="pageParam.sortBy == null or pageParam.sortBy =='' or pageParam.sortDirection== null or pageParam.sortDirection ==''">
            mix.monitor_name,mix.monitor_id
        </if>
        <if test="pageParam.sortBy != null and pageParam.sortBy !='' and pageParam.sortDirection!= null and pageParam.sortDirection !=''">
            ${pageParam.sortBy} ${pageParam.sortDirection}
        </if>
    </select>

    <select id="getAlarmStatistics" resultType="java.util.Map">
        select
        (case when alert.total is null then 0 else alert.total end) as total,
        (case when alert.solved is null then 0 else alert.solved end) as solved,
        (case when alert.unsolved is null then 0 else alert.unsolved end) as unsolved,
        <if test="timeCycle == 'day'">
            date_format(now(), '%Y-%m-%d' ) timeStart,
            date_format(now(), '%Y-%m-%d' ) timeEnd
        </if>
        <if test="timeCycle == 'week'">
         date_format(date_sub(curdate(),INTERVAL WEEKDAY(curdate()) + 0 DAY), '%Y-%m-%d') timeStart,
         date_format(date_sub(curdate(),INTERVAL WEEKDAY(curdate()) - 6 DAY), '%Y-%m-%d') timeEnd
        </if>
        <if test="timeCycle == 'month'">
         date_format(date_add(curdate(), interval - day(curdate()) + 1 day), '%Y-%m-%d' ) timeStart,
         date_format(last_day(curdate()), '%Y-%m-%d' ) timeEnd
        </if>
        from(
        SELECT
        count(*) total,
        sum(if(is_solved = 2,1,0)) solved,
        sum(if(is_solved = 0,1,0)) unsolved
        FROM
        hzb_alert
        WHERE
        <if test="timeCycle == 'day'">
            date_format(gmt_create, '%Y-%m-%d' ) = #{today}
        </if>
        <if test="timeCycle == 'week'">
            YEARWEEK( date_format(gmt_create, '%Y-%m-%d' ), 1 ) = YEARWEEK( #{today}, 1 )
        </if>
        <if test="timeCycle == 'month'">
            DATE_FORMAT( gmt_create, '%Y%m' ) = DATE_FORMAT(#{today},'%Y%m')
        </if>
        and platform_id in
        <foreach
                collection="mapList" open="(" separator="," close=")" item="platform">
                #{platform.platformId}
            </foreach>
        <if test="platformId != null">
            and platform_id = #{platformId}
        </if>
        ) alert
    </select>

    <select id="getProcessStatistics" resultType="java.util.Map">
        SELECT
        (CASE WHEN process.total IS NULL THEN 0 ELSE process.total END ) AS total,
        (CASE WHEN process.unfinished IS NULL THEN 0 ELSE process.unfinished END ) AS unfinished,
        (CASE WHEN process.finished IS NULL THEN 0 ELSE process.finished END ) AS finished,
        (CASE WHEN process.reject IS NULL THEN 0 ELSE process.reject END ) AS reject
        FROM
        (
        SELECT
        count(pro.id) total,
        sum(if(pro.end_time is null,1,0)) unfinished,
        sum(if(pro.end_time is not null,1,0)) finished,
        sum(if(task.process_instance_id is not null ,1,0)) reject
        FROM
        bpm_process_instance_ext pro
        LEFT JOIN (
        SELECT
        process_instance_id
        from
        bpm_task_ext
        WHERE result in (4,5)
        GROUP BY process_instance_id
        ) task on task.process_instance_id = pro.process_instance_id and pro.is_fall_back=1
        WHERE
        pro.deleted = 0
        <if test="platformId != null">
            AND JSON_EXTRACT(pro.form_variables, '$.platformId' ) = #{platformId}
        </if>
        <if test="timeCycle == 'day'">
            AND date_format(create_time, '%Y-%m-%d' ) = #{today}
        </if>
        <if test="timeCycle == 'week'">
            AND YEARWEEK( date_format(create_time, '%Y-%m-%d' ), 1 ) = YEARWEEK( #{today}, 1 )
        </if>
        <if test="timeCycle == 'month'">
            AND DATE_FORMAT( create_time, '%Y%m' ) = DATE_FORMAT(#{today},'%Y%m')
        </if>
        and pro.start_user_id in (SELECT id from system_users WHERE tenant_id = #{tenantId})
        ) process
    </select>

    <select id="getAlarmTrigger" resultType="java.util.Map">
        SELECT
        *
        FROM
        (
            SELECT
                monitor_name,
                monitor_id,
                MAX(alarm_id) alarm_id,
                content,
                MAX(priority) priority,
                sum( times ) times
            FROM
                hzb_alert
            WHERE resource_type = 1
                <if test="timeCycle == 'day'">
                    AND  date_format(gmt_create, '%Y-%m-%d' ) = #{today}
                </if>
                <if test="timeCycle == 'week'">
                    AND  YEARWEEK( date_format(gmt_create, '%Y-%m-%d' ), 1 ) = YEARWEEK( #{today}, 1 )
                </if>
                <if test="timeCycle == 'month'">
                    AND  DATE_FORMAT( gmt_create, '%Y%m' ) = DATE_FORMAT(#{today},'%Y%m')
                </if>
            GROUP BY
                content,
                monitor_id,
                monitor_name UNION ALL
            (
                SELECT
                    monitor_name,
                    monitor_id,
                    alarm_id,
                    MAX(content) content,
                    MAX(priority) priority,
                    sum( times )  times
                FROM
                    hzb_alert
                WHERE resource_type = 0
                    <if test="timeCycle == 'day'">
                        AND  date_format(gmt_create, '%Y-%m-%d' ) = #{today}
                    </if>
                    <if test="timeCycle == 'week'">
                        AND  YEARWEEK( date_format(gmt_create, '%Y-%m-%d' ), 1 ) = YEARWEEK( #{today}, 1 )
                    </if>
                    <if test="timeCycle == 'month'">
                        AND  DATE_FORMAT( gmt_create, '%Y%m' ) = DATE_FORMAT(#{today},'%Y%m')
                    </if>
                GROUP BY
                    alarm_id,
                    monitor_id,
                    monitor_name
            )
        ) mix  where mix.monitor_id in
            <foreach collection="targetUuids" item="monitorId" open="(" close=")" separator=",">
                #{monitorId}
            </foreach>
        ORDER BY
            mix.times DESC
            LIMIT 10
    </select>

    <select id="getUuidsByPlatform" resultType="java.lang.String">
        SELECT uuid from (
                             SELECT id as uuid,platform_id from hzb_monitor
                             UNION ALL
                             SELECT uuid,platform_id from monitor_host_info where deleted = 0
                             UNION ALL
                             SELECT uuid,platform_id from monitor_hardware_info where deleted = 0
                             UNION ALL
                             SELECT uuid,platform_id from monitor_storage_info where deleted = 0
                         ) mix WHERE platform_id in
            <foreach collection="mapList" separator="," close=")" open="(" item="platform">
                #{platform.platformId}
            </foreach>
        <if test="platformId != null">
            and platform_id = #{platformId}
        </if>
    </select>

    <select id="getAlarmByType" resultType="java.util.Map">
        SELECT
            CASE WHEN COUNT(*) IS NULL THEN 0 ELSE COUNT(*) END total,
            app
        FROM
            hzb_alert
        WHERE
            platform_id IN
            <foreach collection="mapList" separator="," close=")" open="(" item="platform">
                #{platform.platformId}
            </foreach>
            <if test="platformId != null">
                and platform_id = #{platformId}
            </if>
          AND date_format(gmt_create, '%Y-%m-%d' ) = date_format(NOW(), '%Y-%m-%d' )
        GROUP BY app

    </select>

    <select id="getAlarmByTypeYesterday" resultType="java.util.Map">
        SELECT
        CASE WHEN COUNT(*) IS NULL THEN 0 ELSE COUNT(*) END total,
        app
        FROM
        hzb_alert
        WHERE
        platform_id IN
        <foreach collection="mapList" separator="," close=")" open="(" item="platform">
            #{platform.platformId}
        </foreach>
        <if test="platformId != null">
            and platform_id = #{platformId}
        </if>
        AND date_format(gmt_create, '%Y-%m-%d' ) = date_format(DATE_SUB(now(), INTERVAL 1 DAY),'%Y-%m-%d')
        GROUP BY app

    </select>

    <select id="getProcessByType" resultType="java.util.Map">
        SELECT
            count(*) total,
        REPLACE(JSON_EXTRACT( form_variables, '$.sourceType' ), '"', '') app
        FROM
            bpm_process_instance_ext
        WHERE
            JSON_EXTRACT( form_variables, '$.sourceType' ) IS NOT NULL
        AND date_format( create_time, '%Y-%m-%d' ) = date_format (now(),'%Y-%m-%d')
        <if test="isSuperAdmin!=true">
            AND start_user_id IN (select id from system_users where tenant_id = #{tenantId} and deleted = 0)
        </if>
        GROUP BY
            JSON_EXTRACT(
            form_variables,
            '$.sourceType')
    </select>
    <select id="getProcessByYesterType" resultType="java.util.Map">
        SELECT
        count(*) total,
        REPLACE(JSON_EXTRACT( form_variables, '$.sourceType' ), '"', '') app
        FROM
        bpm_process_instance_ext
        WHERE
        JSON_EXTRACT( form_variables, '$.sourceType' ) IS NOT NULL
        AND date_format( create_time, '%Y-%m-%d' ) = date_format (DATE_SUB(now(), INTERVAL 1 DAY),'%Y-%m-%d')
        <if test="isSuperAdmin!=true">
            AND start_user_id IN (select id from system_users where tenant_id = #{tenantId} and deleted = 0)
        </if>
        GROUP BY
        JSON_EXTRACT(
        form_variables,
        '$.sourceType')
    </select>

    <select id="getAlarmByPlatform" resultType="java.util.Map">
        SELECT
            CASE

                WHEN
                    COUNT(*) IS NULL THEN
                    0 ELSE COUNT(*)
                END total,
            MAX( platform_name ) platformName,
            platform_id platformId
        FROM
            hzb_alert
        WHERE
            platform_id IN
        <foreach collection="mapList" separator="," close=")" open="(" item="platform">
            #{platform.platformId}
        </foreach>
        <if test="platformId != null">
            and platform_id = #{platformId}
        </if>
          AND date_format( gmt_create, '%Y-%m-%d' ) = date_format( NOW(), '%Y-%m-%d' )
        GROUP BY
            platform_id
    </select>
    <select id="getAlarmByYesterPlatform" resultType="java.util.Map">
        SELECT
        CASE

        WHEN
        COUNT(*) IS NULL THEN
        0 ELSE COUNT(*)
        END total,
        MAX( platform_name ) platformName,
        platform_id platformId
        FROM
        hzb_alert
        WHERE
        platform_id IN
        <foreach collection="mapList" separator="," close=")" open="(" item="platform">
            #{platform.platformId}
        </foreach>
        <if test="platformId != null">
            and platform_id = #{platformId}
        </if>
        AND date_format( gmt_create, '%Y-%m-%d' ) = date_format (DATE_SUB(now(), INTERVAL 1 DAY),'%Y-%m-%d')
        GROUP BY
        platform_id
    </select>

    <select id="getProcessByPlatform" resultType="java.util.Map">
        SELECT
            count(*) total,
            JSON_EXTRACT(form_variables, '$.platformId' ) platformId
        FROM
            bpm_process_instance_ext
        WHERE
            JSON_EXTRACT(form_variables, '$.platformId' ) IS NOT NULL
          AND JSON_EXTRACT(form_variables, '$.platformId' ) in
        <foreach collection="mapList" separator="," close=")" open="(" item="platform">
            #{platform.platformId}
        </foreach>
          AND date_format(create_time, '%Y-%m-%d' ) = date_format ( now(), '%Y-%m-%d' )
        <if test="platformId != null">
            and JSON_EXTRACT(form_variables,'$.platformId') = #{platformId}
        </if>
        GROUP BY
            JSON_EXTRACT(form_variables,'$.platformId')
    </select>

    <select id="getProcessByYesterPlatform" resultType="java.util.Map">
        SELECT
        count(*) total,
        JSON_EXTRACT(form_variables, '$.platformId' ) platformId
        FROM
        bpm_process_instance_ext
        WHERE
        JSON_EXTRACT(form_variables, '$.platformId' ) IS NOT NULL
        AND JSON_EXTRACT(form_variables, '$.platformId' ) in
        <foreach collection="mapList" separator="," close=")" open="(" item="platform">
            #{platform.platformId}
        </foreach>
        AND date_format(create_time, '%Y-%m-%d' ) = date_format (DATE_SUB(now(), INTERVAL 1 DAY),'%Y-%m-%d')
        <if test="platformId != null">
            and JSON_EXTRACT(form_variables,'$.platformId') = #{platformId}
        </if>
        GROUP BY
        JSON_EXTRACT(form_variables,'$.platformId')
    </select>

    <select id="exportzjReportInfoExcel"
            resultType="cn.iocoder.zj.module.report.controller.admin.zjreport.vo.ZjReportExcelReqVO">
        <choose>
            <when test="pageVO.app=='host'">
                (SELECT uuid,
                cast(name as char character set utf8) AS name,
                platform_name                         AS platformName,
                platform_id                           AS platformId,
                "host" as app
                FROM monitor_host_info
                WHERE deleted = 0  and state != 'Destroyed'
                <if test="pageVO.name!= null and pageVO.name!=''">
                    AND name like concat("%",#{pageVO.name},"%")
                </if>
                <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                    AND platform_id like concat("%",#{pageVO.platformId},"%")
                </if>
                <if test="pageVO.platformIds != null and pageVO.platformIds.size>0">
                    AND platform_id in
                    <foreach collection="pageVO.platformIds" separator="," open="(" close=")" item="platformId">
                        #{platformId}
                    </foreach>
                </if>
                )
            </when>

            <when test="pageVO.app=='hardware'">
                (SELECT uuid,
                cast(name as char character set utf8) AS name,
                platform_name                         AS platformName,
                platform_id                           AS platformId,
                "hardware" as app
                FROM monitor_hardware_info
                WHERE deleted = 0
                <if test="pageVO.name!= null and pageVO.name!=''">
                    AND name like concat("%",#{pageVO.name},"%")
                </if>
                <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                    AND platform_id like concat("%",#{pageVO.platformId},"%")
                </if>
                <if test="pageVO.platformIds != null and pageVO.platformIds.size>0">
                    AND platform_id in
                    <foreach collection="pageVO.platformIds" separator="," open="(" close=")" item="platformId">
                        #{platformId}
                    </foreach>
                </if>
                )
            </when>
            <when test="pageVO.app=='storage'">
                (SELECT uuid,
                cast(name as char character set utf8) AS name,
                platform_name AS platformName,
                platform_id   AS platformId,
                "storage" as app
                FROM monitor_storage_info
                WHERE deleted = 0
                <if test="pageVO.name!= null and pageVO.name!=''">
                    AND name like concat("%",#{pageVO.name},"%")
                </if>
                <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                    AND platform_id like concat("%",#{pageVO.platformId},"%")
                </if>
                <if test="pageVO.platformIds != null and pageVO.platformIds.size>0">
                    AND platform_id in
                    <foreach collection="pageVO.platformIds" separator="," open="(" close=")" item="platformId">
                        #{platformId}
                    </foreach>
                </if>
                )
            </when>
            <when test="pageVO.app=='hz'">
                (SELECT CAST(id AS CHAR(50) character set utf8) as uuid,
                name,
                platform_name                           AS platformName,
                platform_id                             AS platformIdm,
                app
                FROM hzb_monitor
                where app in
                <foreach close=")" collection="subEnums" item="item" open="(" separator=", ">
                    #{item.value}
                </foreach>
                <if test="pageVO.name!= null and pageVO.name!=''">
                    AND name like concat("%",#{pageVO.name},"%")
                </if>
                <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                    AND platform_id like concat("%",#{pageVO.platformId},"%")
                </if>
                <if test="pageVO.platformIds != null and pageVO.platformIds.size>0">
                    AND platform_id in
                    <foreach collection="pageVO.platformIds" separator="," open="(" close=")" item="platformId">
                        #{platformId}
                    </foreach>
                </if>
                )
            </when>
            <otherwise>
                select *
                from ((SELECT uuid,
                cast(name as char character set utf8) AS name,
                platform_name                         AS platformName,
                platform_id                           AS platformId,
                "host" as app
                FROM monitor_host_info
                WHERE deleted = 0  and state != 'Destroyed'
                <if test="pageVO.name!= null and pageVO.name!=''">
                    AND name like concat("%",#{pageVO.name},"%")
                </if>
                <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                    AND platform_id like concat("%",#{pageVO.platformId},"%")
                </if>
                <if test="pageVO.platformIds != null and pageVO.platformIds.size>0">
                    AND platform_id in
                    <foreach collection="pageVO.platformIds" separator="," open="(" close=")" item="platformId">
                        #{platformId}
                    </foreach>
                </if>
                )

                UNION ALL

                (SELECT uuid,
                cast(name as char character set utf8) AS name,
                platform_name AS platformName,
                platform_id   AS platformId,
                "storage" as app
                FROM monitor_storage_info
                WHERE deleted = 0
                <if test="pageVO.name!= null and pageVO.name!=''">
                    AND name like concat("%",#{pageVO.name},"%")
                </if>
                <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                    AND platform_id like concat("%",#{pageVO.platformId},"%")
                </if>
                <if test="pageVO.platformIds != null and pageVO.platformIds.size>0">
                    AND platform_id in
                    <foreach collection="pageVO.platformIds" separator="," open="(" close=")" item="platformId">
                        #{platformId}
                    </foreach>
                </if>
                )

                UNION ALL

                (SELECT uuid,

                cast(name as char character set utf8) AS name,
                platform_name                         AS platformName,
                platform_id                           AS platformId,
                "hardware" as app
                FROM monitor_hardware_info
                WHERE deleted = 0
                <if test="pageVO.name!= null and pageVO.name!=''">
                    AND name like concat("%",#{pageVO.name},"%")
                </if>
                <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                    AND platform_id like concat("%",#{pageVO.platformId},"%")
                </if>
                <if test="pageVO.platformIds != null and pageVO.platformIds.size>0">
                    AND platform_id in
                    <foreach collection="pageVO.platformIds" separator="," open="(" close=")" item="platformId">
                        #{platformId}
                    </foreach>
                </if>
                )

                UNION ALL

                (SELECT CAST(id AS CHAR(50) character set utf8) as uuid,
                name,
                platform_name                           AS platformName,
                platform_id                             AS platformIdm,
                app
                FROM hzb_monitor
                <where>
                    <if test="pageVO.name!= null and pageVO.name!=''">
                        name like concat("%",#{pageVO.name},"%")
                    </if>
                    <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                        AND platform_id like concat("%",#{pageVO.platformId},"%")
                    </if>
                    <if test="pageVO.platformIds != null and pageVO.platformIds.size>0">
                        AND platform_id in
                        <foreach collection="pageVO.platformIds" separator="," open="(" close=")" item="platformId">
                            #{platformId}
                        </foreach>
                    </if>
                </where>
                )) as t
            </otherwise>
        </choose>
    </select>

    <select id="getAlertTopList" resultType="cn.iocoder.zj.module.report.controller.admin.zjreport.vo.ZjReportProcessRespVO">
        <choose>
            <when test="time=='week'">
                SELECT
                sum( times ) as alertCount,
                monitor_name as name,
                platform_name as platformName
                FROM
                ( SELECT monitor_name, platform_name, gmt_create, times FROM hzb_alert WHERE YEARWEEK(gmt_create) =  YEARWEEK( NOW() )  and
                platform_id IN
                <foreach collection="mapList" separator="," close=")" open="(" item="platform">
                    #{platform.platformId}
                </foreach>
                <if test="platformId != null">
                    and platform_id = #{platformId}
                </if>
                ) s

                GROUP BY
                monitor_name,
                platform_name
                ORDER BY
                alertCount DESC
                LIMIT 10
            </when>
            <when test="time=='month'">
                SELECT
                sum( times ) as alertCount,
                monitor_name as name,
                platform_name as platformName
                FROM
                ( SELECT monitor_name, platform_name, gmt_create, times FROM hzb_alert WHERE DATE_FORMAT( gmt_create, '%Y%m' ) = DATE_FORMAT(NOW(),'%Y%m') and
                platform_id IN
                <foreach collection="mapList" separator="," close=")" open="(" item="platform">
                    #{platform.platformId}
                </foreach>
                <if test="platformId != null">
                    and platform_id = #{platformId}
                </if>
                ) s
                GROUP BY
                monitor_name,
                platform_name
                ORDER BY
                alertCount DESC
                LIMIT 10
            </when>
            <otherwise>
                SELECT
                sum( times ) as alertCount,
                monitor_name as name,
                platform_name as platformName
                FROM
                ( SELECT monitor_name, platform_name, gmt_create, times FROM hzb_alert WHERE DATE_FORMAT( gmt_create, '%Y%m%d' ) = DATE_FORMAT(NOW(),'%Y%m%d') and
                platform_id IN
                <foreach collection="mapList" separator="," close=")" open="(" item="platform">
                    #{platform.platformId}
                </foreach>
                <if test="platformId != null">
                    and platform_id = #{platformId}
                </if>
                ) s
                GROUP BY
                monitor_name,
                platform_name
                ORDER BY
                alertCount DESC
                LIMIT 10
            </otherwise>
        </choose>
    </select>

    <select id="getAlertTopListYest"
            resultType="cn.iocoder.zj.module.report.controller.admin.zjreport.vo.ZjReportProcessRespVO">
        <choose>
            <when test="time=='week'">
                SELECT
                sum( times ) as alertCount,
                monitor_name as name,
                platform_name as platformName
                FROM
                ( SELECT monitor_name, platform_name, gmt_create, times FROM hzb_alert WHERE YEARWEEK(gmt_create) = YEARWEEK(NOW() - INTERVAL 1 WEEK)  and
                platform_id IN
                <foreach collection="mapList" separator="," close=")" open="(" item="platform">
                    #{platform.platformId}
                </foreach>
                <if test="platformId != null">
                    and platform_id = #{platformId}
                </if>
                ) s

                GROUP BY
                monitor_name,
                platform_name
                ORDER BY
                alertCount DESC
                LIMIT 10
            </when>
            <when test="time=='month'">
                SELECT
                sum( times ) as alertCount,
                monitor_name as name,
                platform_name as platformName
                FROM
                ( SELECT monitor_name, platform_name, gmt_create, times FROM hzb_alert WHERE DATE_FORMAT( gmt_create, '%Y%m' ) = DATE_FORMAT(NOW() - INTERVAL 1 MONTH, '%Y%m') and
                platform_id IN
                <foreach collection="mapList" separator="," close=")" open="(" item="platform">
                    #{platform.platformId}
                </foreach>
                <if test="platformId != null">
                    and platform_id = #{platformId}
                </if>
                ) s
                GROUP BY
                monitor_name,
                platform_name
                ORDER BY
                alertCount DESC
                LIMIT 10
            </when>
            <otherwise>
                SELECT
                sum( times ) as alertCount,
                monitor_name as name,
                platform_name as platformName
                FROM
                ( SELECT monitor_name, platform_name, gmt_create, times FROM hzb_alert WHERE DATE_FORMAT( gmt_create, '%Y%m%d' ) = DATE_FORMAT( DATE_SUB( NOW(), INTERVAL 1 DAY ), '%Y%m%d' ) and
                platform_id IN
                <foreach collection="mapList" separator="," close=")" open="(" item="platform">
                    #{platform.platformId}
                </foreach>
                <if test="platformId != null">
                    and platform_id = #{platformId}
                </if>
                ) s
                GROUP BY
                monitor_name,
                platform_name
                ORDER BY
                alertCount DESC
                LIMIT 10
            </otherwise>
        </choose>
    </select>

    <select id="listBpmProcessTopList" resultType="java.util.Map">
        SELECT
            count(*) count,
	        sourceId
        FROM
            ( SELECT JSON_UNQUOTE( JSON_EXTRACT( form_variables, '$.sourceId' )) AS sourceId,
            create_time FROM
            bpm_process_instance_ext
            WHERE deleted = 0
            <if test="mapList.size>0 and mapList != null">
                AND JSON_EXTRACT( form_variables, '$.platformId' ) in
                <foreach collection="mapList" separator="," open="(" close=")" item="platform">
                    #{platform.platformId}
                </foreach>
            </if>) d
        WHERE
            sourceId IS NOT NULL
        <if test="time == 'month'">
          AND YEAR ( create_time ) = YEAR (NOW())
          AND MONTH ( create_time ) = MONTH (NOW())
        </if>
        <if test="time == 'week'">
          AND YEARWEEK( create_time ) = YEARWEEK(NOW())
        </if>
        GROUP BY
            sourceId
        ORDER BY
            count DESC
            LIMIT 10
    </select>

    <select id="getMonitorList" resultType="java.util.Map">
        SELECT
            *
        FROM
            ((
                 SELECT
                     uuid,
                     cast( NAME AS CHAR CHARACTER SET utf8 ) AS name,
                     platform_name AS platformName,
                     platform_id AS platformId,
                     "host" AS app
                 FROM
                     monitor_host_info
                 WHERE
                     deleted = 0
                   AND state != 'Destroyed'
             ) UNION ALL
             (
                 SELECT
                     uuid,
                     cast( NAME AS CHAR CHARACTER SET utf8 ) AS NAME,
                     platform_name AS platformName,
                     platform_id AS platformId,
                     "storage" AS app
                 FROM
                     monitor_storage_info
                 WHERE
                     deleted = 0
             ) UNION ALL
             (
                 SELECT
                     uuid,
                     cast( NAME AS CHAR CHARACTER SET utf8 ) AS NAME,
                     platform_name AS platformName,
                     platform_id AS platformId,
                     "hardware" AS app
                 FROM
                     monitor_hardware_info
                 WHERE
                     deleted = 0
             ) UNION ALL
             (
                 SELECT
                     CAST( id AS CHAR ( 50 ) CHARACTER SET utf8 ) AS uuid,
                     NAME,
                     platform_name AS platformName,
                     platform_id AS platformId,
                     app
                 FROM
                     hzb_monitor
             )) AS t

            WHERE
        platformId IN
            <foreach collection="mapList" separator="," close=")" open="(" item="platform">
                #{platform.platformId}
            </foreach>
        <if test="platformId != null">
            and platformId = #{platformId}
        </if>
    </select>

    <select id="getPlatformAlertList"
            resultType="cn.iocoder.zj.module.report.controller.admin.zjreport.vo.ZjReportPlatformRespVO">
        <choose>
            <when test="time=='week'">
                SELECT platform_id as platformId, DATE(gmt_create) AS date, COUNT(*) AS count
                FROM hzb_alert
                WHERE gmt_create >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND platform_id IS NOT NULL
                and         platform_id IN
                <foreach collection="mapList" separator="," close=")" open="(" item="platform">
                    #{platform.platformId}
                </foreach>
                <if test="platformId != null">
                    and platform_id = #{platformId}
                </if>
                GROUP BY  platform_id,DATE(gmt_create)
                ORDER BY  platform_id,DATE(gmt_create)
            </when>
            <otherwise>
                SELECT platform_id as platformId, DATE(gmt_create) AS date, COUNT(*) AS count
                FROM hzb_alert
                WHERE gmt_create >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH) AND platform_id IS NOT NULL
                and         platform_id IN
                <foreach collection="mapList" separator="," close=")" open="(" item="platform">
                    #{platform.platformId}
                </foreach>
                <if test="platformId != null">
                    and platform_id = #{platformId}
                </if>
                GROUP BY  platform_id,DATE(gmt_create)
                ORDER BY  platform_id,DATE(gmt_create)
            </otherwise>
        </choose>
    </select>

    <select id="getPlatformBpmList"
            resultType="cn.iocoder.zj.module.report.controller.admin.zjreport.vo.ZjReportPlatformRespVO">
        <choose>
            <when test="time=='week'">
                SELECT platformId,DATE(create_time) as date,count(*) count  FROM (SELECT JSON_UNQUOTE(JSON_EXTRACT(form_variables, '$.platformId')) AS platformId ,create_time
                FROM bpm_process_instance_ext where  deleted = 0) d
                WHERE platformId is not null and  create_time  >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
                and         platformId IN
                <foreach collection="mapList" separator="," close=")" open="(" item="platform">
                    #{platform.platformId}
                </foreach>
                <if test="platformId != null">
                    and platformId = #{platformId}
                </if>
                GROUP BY platformId, DATE(date)
                ORDER BY platformId, DATE(date)
            </when>
            <otherwise>
                SELECT platformId,DATE(create_time) as date,count(*) count  FROM (SELECT JSON_UNQUOTE(JSON_EXTRACT(form_variables, '$.platformId')) AS platformId ,create_time
                FROM bpm_process_instance_ext where  deleted = 0) d
                WHERE platformId is not null and  create_time  >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)
                and         platformId IN
                <foreach collection="mapList" separator="," close=")" open="(" item="platform">
                    #{platform.platformId}
                </foreach>
                <if test="platformId != null">
                    and platformId = #{platformId}
                </if>
                GROUP BY platformId, DATE(date)
                ORDER BY platformId, DATE(date)
            </otherwise>
        </choose>
    </select>

    <select id="getAlertGrowthTrendByType" resultType="java.util.Map">
        SELECT * FROM(
        SELECT
        app,
        date_format(gmt_create,'%Y-%m-%d') dateStr,
        count(*) total
        FROM
        hzb_alert
        WHERE platform_id in
        <foreach collection="mapList" open="(" item="platform" close=")" separator=",">
            #{platform.platformId}
        </foreach>
        <if test="time == 'week'">
            AND date_format( gmt_create, '%Y-%m-%d' ) &gt;= date_format( date_sub( curdate(), INTERVAL WEEKDAY( curdate()) + 0 DAY ), '%Y-%m-%d' )
            AND date_format( gmt_create, '%Y-%m-%d' ) &lt;= date_format( date_sub( curdate(), INTERVAL WEEKDAY( curdate()) - 6 DAY ), '%Y-%m-%d' )
        </if>
        <if test="time == 'month'">
            AND date_format( gmt_create, '%Y-%m-%d' ) &gt;= date_format(date_add(curdate(), interval - day(curdate()) + 1 day), '%Y-%m-%d' )
            AND date_format( gmt_create, '%Y-%m-%d' ) &lt;= date_format(last_day(curdate()), '%Y-%m-%d' )
        </if>
        <if test="platformId != null">
            and platform_id = #{platformId}
        </if>
        GROUP BY
        app,
        date_format( gmt_create, '%Y-%m-%d')
        ) mix ORDER BY mix.dateStr
    </select>

    <select id="getProcessGrowthTrendByType" resultType="java.util.Map">
        SELECT
        count(*) total,
        REPLACE ( JSON_EXTRACT( form_variables, '$.sourceType' ), '"', '' ) app,
        date_format( create_time, '%Y-%m-%d' ) dateStr
        FROM
        bpm_process_instance_ext
        WHERE
        JSON_EXTRACT( form_variables, '$.sourceType' ) IS NOT NULL
        <if test="platformId!=null">
            AND JSON_EXTRACT( form_variables, '$.platformId' ) = #{platformId}
        </if>
        <if test="isSuperAdmin!=true">
            AND JSON_EXTRACT( form_variables, '$.platformId' ) in
            <foreach collection="mapList" item="platform" open="(" close=")" separator=",">
                #{platform.platformId}
            </foreach>
        </if>
        <if test="time == 'week'">
        AND date_format( create_time, '%Y-%m-%d' ) &gt;= date_format( date_sub( curdate(), INTERVAL WEEKDAY( curdate()) + 0 DAY ), '%Y-%m-%d' )
        AND date_format( create_time, '%Y-%m-%d' ) &lt;= date_format( date_sub( curdate(), INTERVAL WEEKDAY( curdate()) - 6 DAY ), '%Y-%m-%d' )
        </if>
        <if test="time == 'month'">
            AND date_format( create_time, '%Y-%m-%d' ) &gt;= date_format(date_add(curdate(), interval - day(curdate()) + 1 day), '%Y-%m-%d' )
            AND date_format( create_time, '%Y-%m-%d' ) &lt;= date_format(last_day(curdate()), '%Y-%m-%d' )
        </if>
        GROUP BY
        JSON_EXTRACT( form_variables, '$.sourceType' ),
        date_format( create_time, '%Y-%m-%d' )
    </select>

    <select id="getWeChatUserOpenId" resultType="java.lang.String">
        select open_id from system_wechat_binding where user_id = #{userId} and deleted = 0
    </select>

    <select id="getAlertCountByUserPlatformIds" resultType="java.lang.Long">
        SELECT
            count(*) total
        FROM
            hzb_alert
        WHERE is_solved = 0
            <if test="mapList.size>0 and mapList != null">
                and platform_id IN
                <foreach collection="mapList" separator="," close=")" open="(" item="plat">
                   #{plat.platformId}
                </foreach>
            </if>
    </select>
    <select id="selectMyzjreport"
            resultType="cn.iocoder.zj.module.report.controller.admin.zjreport.vo.ZjReportPageReqVO">
        <choose>
            <when test="pageVO.app=='host'">
                (SELECT uuid,
                cast(name as char character set utf8) AS name,
                platform_name                         AS platformName,
                platform_id                           AS platformId,
                "host" as app
                FROM monitor_host_info
                WHERE deleted = 0  and state != 'Destroyed'
                <if test="platformList.size >0">
                    and platform_id in
                    <foreach collection="platformList" item="platform" open="(" close=")" separator=",">
                        #{platform.platformId}
                    </foreach>
                </if>
                <if test="pageVO.name!= null and pageVO.name!=''">
                    AND name like concat("%",#{pageVO.name},"%")
                </if>
                <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                    AND platform_id like concat("%",#{pageVO.platformId},"%")
                </if>
                )
            </when>

            <when test="pageVO.app=='hardware'">
                (SELECT uuid,
                cast(name as char character set utf8) AS name,
                platform_name                         AS platformName,
                platform_id                           AS platformId,
                "hardware" as app
                FROM monitor_hardware_info
                WHERE deleted = 0
                <if test="platformList.size >0">
                    and platform_id in
                    <foreach collection="platformList" item="platform" open="(" close=")" separator=",">
                        #{platform.platformId}
                    </foreach>
                </if>
                <if test="pageVO.name!= null and pageVO.name!=''">
                    AND name like concat("%",#{pageVO.name},"%")
                </if>
                <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                    AND platform_id like concat("%",#{pageVO.platformId},"%")
                </if>
                )
            </when>
            <when test="pageVO.app=='storage'">
                (SELECT uuid,
                cast(name as char character set utf8) AS name,
                platform_name AS platformName,
                platform_id   AS platformId,
                "storage" as app
                FROM monitor_storage_info
                WHERE deleted = 0
                <if test="pageVO.name!= null and pageVO.name!=''">
                    AND name like concat("%",#{pageVO.name},"%")
                </if>
                <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                    AND platform_id like concat("%",#{pageVO.platformId},"%")
                </if>
                <if test="platformList.size >0">
                    and platform_id in
                    <foreach collection="platformList" item="platform" open="(" close=")" separator=",">
                        #{platform.platformId}
                    </foreach>
                </if>)
            </when>
            <when test="pageVO.app=='hz'">
                (SELECT CAST(id AS CHAR(50) character set utf8) as uuid,
                name,
                platform_name                           AS platformName,
                platform_id                             AS platformIdm,
                app
                FROM hzb_monitor
                where app in
                <foreach close=")" collection="subEnums" item="item" open="(" separator=", ">
                    #{item.value}
                </foreach>
                <if test="pageVO.name!= null and pageVO.name!=''">
                    AND name like concat("%",#{pageVO.name},"%")
                </if>
                <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                    AND platform_id like concat("%",#{pageVO.platformId},"%")
                </if>
                <if test="platformList.size >0">
                    and platform_id in
                    <foreach collection="platformList" item="platform" open="(" close=")" separator=",">
                        #{platform.platformId}
                    </foreach>
                </if>
                )
            </when>
            <otherwise>
                select *
                from ((SELECT uuid,
                cast(name as char character set utf8) AS name,
                platform_name                         AS platformName,
                platform_id                           AS platformId,
                "host" as app
                FROM monitor_host_info
                WHERE deleted = 0  and state != 'Destroyed'
                <if test="platformList.size >0">
                    and platform_id in
                    <foreach collection="platformList" item="platform" open="(" close=")" separator=",">
                        #{platform.platformId}
                    </foreach>
                </if>
                <if test="pageVO.name!= null and pageVO.name!=''">
                    AND name like concat("%",#{pageVO.name},"%")
                </if>
                <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                    AND platform_id like concat("%",#{pageVO.platformId},"%")
                </if>
                )

                UNION ALL

                (SELECT uuid,
                cast(name as char character set utf8) AS name,
                platform_name AS platformName,
                platform_id   AS platformId,
                "storage" as app
                FROM monitor_storage_info
                WHERE deleted = 0
                <if test="platformList.size >0">
                    and platform_id in
                    <foreach collection="platformList" item="platform" open="(" close=")" separator=",">
                        #{platform.platformId}
                    </foreach>
                </if>
                <if test="pageVO.name!= null and pageVO.name!=''">
                    AND name like concat("%",#{pageVO.name},"%")
                </if>
                <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                    AND platform_id like concat("%",#{pageVO.platformId},"%")
                </if>
                )

                UNION ALL

                (SELECT uuid,

                cast(name as char character set utf8) AS name,
                platform_name                         AS platformName,
                platform_id                           AS platformId,
                "hardware" as app
                FROM monitor_hardware_info
                WHERE deleted = 0
                <if test="platformList.size >0">
                    and platform_id in
                    <foreach collection="platformList" item="platform" open="(" close=")" separator=",">
                        #{platform.platformId}
                    </foreach>
                </if>
                <if test="pageVO.name!= null and pageVO.name!=''">
                    AND name like concat("%",#{pageVO.name},"%")
                </if>
                <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                    AND platform_id like concat("%",#{pageVO.platformId},"%")
                </if>
                )

                UNION ALL

                (SELECT CAST(id AS CHAR(50) character set utf8) as uuid,
                name,
                platform_name                           AS platformName,
                platform_id                             AS platformIdm,
                app
                FROM hzb_monitor
                <where>
                    <if test="pageVO.name!= null and pageVO.name!=''">
                        name like concat("%",#{pageVO.name},"%")
                    </if>
                    <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                        AND platform_id like concat("%",#{pageVO.platformId},"%")
                    </if>
                    <if test="platformList.size >0">
                        and platform_id in
                        <foreach collection="platformList" item="platform" open="(" close=")" separator=",">
                            #{platform.platformId}
                        </foreach>
                    </if>
                </where>
                )) as t
            </otherwise>
        </choose>
    </select>
    <select id="getStateByTenantId" resultType="java.lang.Long">
        select state from system_tenant where id = #{tenantId} and deleted = 0
    </select>

    <select id="selectMyzjreportBystate"
            resultType="cn.iocoder.zj.module.report.controller.admin.zjreport.vo.ZjReportPageReqVO">
        <choose>
            <when test="pageVO.app=='hz'">
                (SELECT CAST(id AS CHAR(50) character set utf8) as uuid,
                name,
                platform_name                           AS platformName,
                platform_id                             AS platformIdm,
                app
                FROM hzb_monitor
                where app in
                <foreach close=")" collection="subEnums" item="item" open="(" separator=", ">
                    #{item.value}
                </foreach>
                <if test="pageVO.name!= null and pageVO.name!=''">
                    AND name like concat("%",#{pageVO.name},"%")
                </if>
                <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                    AND platform_id like concat("%",#{pageVO.platformId},"%")
                </if>
                <if test="platformList.size >0">
                    and platform_id in
                    <foreach collection="platformList" item="platform" open="(" close=")" separator=",">
                        #{platform.platformId}
                    </foreach>
                </if>
                )
            </when>
            <otherwise>
                select *
                from (
                (SELECT CAST(id AS CHAR(50) character set utf8) as uuid,
                name,
                platform_name                           AS platformName,
                platform_id                             AS platformIdm,
                app
                FROM hzb_monitor
                <where>
                    <if test="pageVO.name!= null and pageVO.name!=''">
                        name like concat("%",#{pageVO.name},"%")
                    </if>
                    <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                        AND platform_id like concat("%",#{pageVO.platformId},"%")
                    </if>
                    <if test="platformList.size >0">
                        and platform_id in
                        <foreach collection="platformList" item="platform" open="(" close=")" separator=",">
                            #{platform.platformId}
                        </foreach>
                    </if>
                </where>
                )) as t
            </otherwise>
        </choose>
    </select>
    <select id="exportzjReportInfoByStateExcel"
            resultType="cn.iocoder.zj.module.report.controller.admin.zjreport.vo.ZjReportExcelReqVO">
        <choose>
            <when test="pageVO.app=='hz'">
                (SELECT CAST(id AS CHAR(50) character set utf8) as uuid,
                name,
                platform_name                           AS platformName,
                platform_id                             AS platformIdm,
                app
                FROM hzb_monitor
                where app in
                <foreach close=")" collection="subEnums" item="item" open="(" separator=", ">
                    #{item.value}
                </foreach>
                <if test="pageVO.name!= null and pageVO.name!=''">
                    AND name like concat("%",#{pageVO.name},"%")
                </if>
                <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                    AND platform_id like concat("%",#{pageVO.platformId},"%")
                </if>
                <if test="pageVO.platformIds != null and pageVO.platformIds.size>0">
                    AND platform_id in
                    <foreach collection="pageVO.platformIds" separator="," open="(" close=")" item="platformId">
                        #{platformId}
                    </foreach>
                </if>
                )
            </when>
            <otherwise>
                select *
                from (

                (SELECT CAST(id AS CHAR(50) character set utf8) as uuid,
                name,
                platform_name                           AS platformName,
                platform_id                             AS platformIdm,
                app
                FROM hzb_monitor
                <where>
                    <if test="pageVO.name!= null and pageVO.name!=''">
                        name like concat("%",#{pageVO.name},"%")
                    </if>
                    <if test="pageVO.platformId!= null and pageVO.platformId!=''">
                        AND platform_id like concat("%",#{pageVO.platformId},"%")
                    </if>
                    <if test="pageVO.platformIds != null and pageVO.platformIds.size>0">
                        AND platform_id in
                        <foreach collection="pageVO.platformIds" separator="," open="(" close=")" item="platformId">
                            #{platformId}
                        </foreach>
                    </if>
                </where>
                )) as t
            </otherwise>
        </choose>
    </select>

    <select id="getAlarmReportsByState" resultType="java.util.Map">
        select mix.*
        from (
        SELECT
        monitor_id,
        monitor_name,
        MAX(platform_name) platform_name,
        app AS appType,
        SUM( times ) times,
        MAX( platform_id ) platformId,
        priority priority,
        content content
        FROM
        hzb_alert
        WHERE
        platform_id IS NOT NULL
        AND resource_type = 1
        <if test="mapList.size>0">
            and platform_id in
            <foreach
                    collection="mapList" open="(" separator="," close=")" item="platform">
                #{platform.platformId}
            </foreach>
        </if>
        <if test="subEnums.size>0">
            AND app in
            <foreach close=")" collection="subEnums" item="item" open="(" separator=", ">
                #{item.value}
            </foreach>
        </if>
        GROUP BY
        monitor_id,
        monitor_name,
        content,
        alarm_id,
        priority,
        app UNION ALL
        SELECT
        monitor_id,
        monitor_name,
        MAX(platform_name) platform_name,
        app AS appType,
        SUM( times ) times,
        MAX( platform_id ) platformId,
        priority priority,
        MAX( content ) content
        FROM
        hzb_alert
        WHERE
        platform_id IS NOT NULL
        AND resource_type = 0
        <if test="mapList.size>0">
            and platform_id in
            <foreach
                    collection="mapList" open="(" separator="," close=")" item="platform">
                #{platform.platformId}
            </foreach>
        </if>
        <if test="subEnums.size>0">
            AND app in
            <foreach close=")" collection="subEnums" item="item" open="(" separator=", ">
                #{item.value}
            </foreach>
        </if>
        GROUP BY
        monitor_id,
        monitor_name,
        alarm_id,
        priority,
        app) mix
        WHERE mix.appType not in('storage','hardware','host')
        <if test="monitorName != null and monitorName!= ''">
            AND mix.monitor_name LIKE concat('%', #{monitorName}, '%')
        </if>
        <if test="appType != null and appType!= ''">
            AND mix.appType= #{appType}
        </if>
        <if test="priority != null">
            AND mix.priority= #{priority}
        </if>
        <if test="platformId != null and platformId!= ''">
            AND mix.platformId= #{platformId}
        </if>
        order by
        <if test="pageParam.sortBy == null or pageParam.sortBy =='' or pageParam.sortDirection== null or pageParam.sortDirection ==''">
            mix.monitor_name,mix.monitor_id
        </if>
        <if test="pageParam.sortBy != null and pageParam.sortBy !='' and pageParam.sortDirection!= null and pageParam.sortDirection !=''">
            ${pageParam.sortBy} ${pageParam.sortDirection}
        </if>
    </select>
    <select id="exportAlarmReportByState" resultType="java.util.Map">
        select mix.*
        from (
        SELECT
        monitor_id,
        monitor_name,
        MAX(platform_name) platform_name,
        app AS appType,
        SUM( times ) times,
        MAX( platform_id ) platformId,
        priority priority,
        content content
        FROM
        hzb_alert
        WHERE
        platform_id IS NOT NULL
        AND resource_type = 1
        <if test="mapList.size>0">
            and platform_id in
            <foreach
                    collection="mapList" open="(" separator="," close=")" item="platform">
                #{platform.platformId}
            </foreach>
        </if>
        GROUP BY
        monitor_id,
        monitor_name,
        content,
        alarm_id,
        priority,
        app UNION ALL
        SELECT
        monitor_id,
        monitor_name,
        MAX(platform_name) platform_name,
        app AS appType,
        SUM( times ) times,
        MAX( platform_id ) platformId,
        priority priority,
        MAX( content ) content
        FROM
        hzb_alert
        WHERE
        platform_id IS NOT NULL
        AND resource_type = 0
        <if test="mapList.size>0">
            and platform_id in
            <foreach
                    collection="mapList" open="(" separator="," close=")" item="platform">
                #{platform.platformId}
            </foreach>
        </if>
        GROUP BY
        monitor_id,
        monitor_name,
        alarm_id,
        priority,
        app) mix
        WHERE mix.appType not in('storage','host','hardware')
        order by
        mix.monitor_name,mix.monitor_id
    </select>
</mapper>
