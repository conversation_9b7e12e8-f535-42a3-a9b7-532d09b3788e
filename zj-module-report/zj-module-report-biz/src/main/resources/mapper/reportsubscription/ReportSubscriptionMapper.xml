<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.report.dal.mysql.zjreport.ReportSubscriptionMapper">

<select id="getByUserId" resultMap="reportSubscriptionMap">
    SELECT id userId,nickname,email  from system_users WHERE id = #{id}
    </select>
    <resultMap id="reportSubscriptionMap" type="cn.iocoder.zj.module.report.controller.admin.reporsubscription.VO.SubscriptionRespVO">
        <collection property="reportSubscription" column="{id=userId}" select="getUserSubscriotion"/>
    </resultMap>

    <select id="getUserSubscriotion" resultType="cn.iocoder.zj.module.report.controller.admin.reporsubscription.VO.ReportSubscriptionUserVO">
        SELECT
            type.bean_name,
            type.NAME,
            type.method_name,
            type.subscription_cycle,
            type.is_timed_task,
            users.email email,
            typeUser.*
        FROM
            report_subscription type
                LEFT JOIN report_subscription_user typeUser on type.uuid = typeUser.subscription_uuid
                LEFT JOIN system_users users on users.id = typeUser.user_id
        WHERE typeUser.user_id = #{id} and typeUser.id is not null
    </select>
<select id="getTimedTasks"
            resultType="cn.iocoder.zj.module.report.controller.admin.reporsubscription.VO.ReportSubscriptionUserVO">
    SELECT
        type.bean_name,
        type.NAME,
        type.method_name,
        type.subscription_cycle,
        type.is_timed_task,
        users.email email,
        typeUser.*
    FROM
        report_subscription_user typeUser
            LEFT JOIN report_subscription type ON type.uuid = typeUser.subscription_uuid
            LEFT JOIN system_users users on users.id = typeUser.user_id
    WHERE
        ( typeUser.wechat_state = 1 OR typeUser.email_state = 1 )
      AND typeUser.task_state = 1 and type.id is not null and type.method_name != 'alertSubscription'
    </select>

<update id="updateReportSubscriptionById">
    <foreach collection="taskList" item="task" index="index" open="" close="" separator=";">
        update report_subscription_user
        <set>
            <if test="task.userId!=null">
                user_id = #{task.userId},
            </if>
            <if test="task.userName!=null and task.userName!=''">
                user_name = #{task.userName},
            </if>
            <if test="task.subscriptionUuid!=null and task.subscriptionUuid!=''">
                subscription_uuid = #{task.subscriptionUuid},
            </if>
            <if test="task.subscriptionTime!=null and task.subscriptionTime != ''">
                subscription_time = #{task.subscriptionTime},
            </if>
            <if test="task.cronExp!=null and task.cronExp !=''">
                cron_exp=#{task.cronExp},
            </if>
            <if test="task.cronDescribe!=null and task.cronDescribe != ''">
                cron_describe=#{task.cronDescribe},
            </if>
            <if test="task.email!=null and task.email != ''">
                email=#{task.email},
            </if>
            <if test="task.tenantId!=null and task.tenantId != ''">
                tenant_id=#{task.tenantId},
            </if>
            <if test="task.wechatState!=null">
                wechat_state=#{task.wechatState},
            </if>
            <if test="task.emailState!=null">
                email_state=#{task.emailState},
            </if>
            <if test="task.taskState!=null">
                task_state= #{task.taskState},
            </if>
            <if test="task.taskState!=null">
                do_not_disturb= #{task.doNotDisturb},
            </if>

            <if test="task.dingtalkState!=null">
                dingtalk_state= #{task.dingtalkState},
            </if>
            <if test="task.wecomState!=null">
                wecom_state= #{task.wecomState},
            </if>
            <if test="task.dingtalkPhone !=null">
                dingtalk_phone  = #{task.dingtalkPhone},
            </if>
            <if test="task.wecomPhone !=null">
                wecom_phone  = #{task.wecomPhone},
            </if>
            update_time = now()
        </set>
        WHERE id = #{task.id}
    </foreach>
    </update>

<update id="updateSingle">
    update report_subscription_user
        set
    <if test="task.userId!=null">
        user_id = #{task.userId},
    </if>
    <if test="task.userName!=null and task.userName!=''">
        user_name = #{task.userName},
    </if>
    <if test="task.subscriptionUuid!=null and task.subscriptionUuid!=''">
        subscription_uuid = #{task.subscriptionUuid},
    </if>
    <if test="task.subscriptionTime!=null and task.subscriptionTime != ''">
        subscription_time = #{task.subscriptionTime},
    </if>
    <if test="task.cronExp!=null and task.cronExp !=''">
        cron_exp=#{task.cronExp},
    </if>
    <if test="task.cronDescribe!=null and task.cronDescribe != ''">
        cron_describe=#{task.cronDescribe},
    </if>
    <if test="task.wechatState!=null">
        email_state=#{task.wechatState},
    </if>
    <if test="task.emailState!=null">
        email_state=#{task.emailState},
    </if>
    <if test="task.taskState!=null">
        task_state= #{task.taskState},
    </if>
    update_time = now()
    WHERE id = #{task.id}
    </update>

<select id="getByUuid"
            resultType="cn.iocoder.zj.module.report.controller.admin.reporsubscription.VO.ReportSubscriptionUserVO">
    SELECT
        type.bean_name,
        type.NAME,
        type.subscription_cycle,
        type.method_name,
        typeUser.*
    FROM
        report_subscription type
            LEFT JOIN report_subscription_user typeUser on type.uuid = typeUser.subscription_uuid
    WHERE type.uuid in
          <foreach collection="uuidList" open="(" item="uuid" close=")" separator=",">
              #{uuid}
          </foreach>
      and typeUser.id is not null
    </select>

<select id="getAllSubscriptionType"
            resultType="cn.iocoder.zj.module.report.dal.dataobject.reporsubscription.ReportSubscriptionDO">
    select * from report_subscription
    </select>

<insert id="createDefaultReportSubscriptionBatch">
    <if test="toBeAddList.size>0">
    insert into report_subscription_user
    (user_id, user_name, subscription_uuid, subscription_time, cron_exp, cron_describe, wechat_state, email_state, task_state,create_time,update_time )
    values
        <foreach collection="toBeAddList" item="item" separator=",">
                (#{item.userId},#{item.userName},#{item.subscriptionUuid},#{item.subscriptionTime},
                #{item.cronExp},#{item.cronDescribe},#{item.wechatState},#{item.emailState},#{item.taskState},now(),now())
        </foreach>
    </if>
    </insert>

<insert id="addSubscriptionTypeBatch">
    <if test="toBeAddedList.size>0">
        insert into report_subscription
        (uuid, name, bean_name, method_name, subscription_cycle,is_timed_task,create_time,update_time)
        values
        <foreach collection="toBeAddedList" item="item" separator=",">
            (#{item.uuid},#{item.name},#{item.beanName},#{item.methodName},
            #{item.subscriptionCycle},#{item.isTimedTask},now(),now())
        </foreach>
    </if>
    </insert>

<delete id="deletedSubscriptionTypeByMethodName">
    DELETE
    FROM
    report_subscription_user
    WHERE
    subscription_uuid IN ( SELECT uuid FROM report_subscription WHERE method_name in
    <foreach collection="toBeDeleted" separator="," close=")" open="(" item="methodName">
        #{methodName}
    </foreach>
    ;DELETE
    FROM
    report_subscription
    WHERE method_name in
    <foreach collection="toBeDeleted" separator="," close=")" open="(" item="methodName">
        #{methodName}
    </foreach>
    </delete>

<delete id="deletedUserSubscriptionByMethodName">
    DELETE
    FROM
    report_subscription_user
    WHERE
    subscription_uuid IN ( SELECT uuid FROM report_subscription WHERE method_name in
    <foreach collection="methodNames" separator="," close=")" open="(" item="methodName">
        #{methodName})
    </foreach>
    </delete>

<select id="getAlarmSubscription"
            resultType="cn.iocoder.zj.module.report.api.subscription.dto.ReportSubscriptionDTO">
    SELECT
        subu.user_id,
        subu.wechat_state,
        subu.email_state,
        subu.dingtalk_state,
        subu.wecom_state,
        users.email,
        swb.open_id,
        subu.dingtalk_phone,
        subu.wecom_phone,
        GROUP_CONCAT( plat.platform_id ) platformId
    FROM
        report_subscription_user subu
            LEFT JOIN system_users users ON subu.user_id = users.id
            AND users.deleted = 0
            LEFT JOIN system_platform_tenant plat ON plat.tenant_id = users.tenant_id
            AND plat.deleted = 0
            LEFT JOIN system_wechat_binding swb ON swb.user_id = subu.user_id
    WHERE
            subu.subscription_uuid IN ( SELECT uuid FROM report_subscription WHERE method_name = 'alertSubscription' )
      AND ( wechat_state = 1 OR email_state = 1 OR dingtalk_state = 1 OR wecom_state = 1)
      and do_not_disturb = 0
    GROUP BY subu.user_id
    </select>
    <select id="getPlatFormIdByTenants" resultType="java.lang.String">
        SELECT
        GROUP_CONCAT( config.id ) platformId
        FROM
        system_platform_tenant tenant
        LEFT JOIN system_platform_config config ON tenant.platform_id = config.id
        WHERE
        tenant.tenant_id IN
        <foreach collection="tenantIds" open="(" item="tenantId" close=")" separator=",">
            #{tenantId}
        </foreach>
        AND tenant.deleted = 0
        AND config.deleted = 0
    </select>

</mapper>