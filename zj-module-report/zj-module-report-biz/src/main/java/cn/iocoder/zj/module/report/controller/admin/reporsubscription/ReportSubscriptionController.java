package cn.iocoder.zj.module.report.controller.admin.reporsubscription;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.web.core.util.WebFrameworkUtils;
import cn.iocoder.zj.module.report.controller.admin.reporsubscription.VO.ReportSubscriptionUserVO;
import cn.iocoder.zj.module.report.controller.admin.reporsubscription.VO.SubscriptionRespVO;
import cn.iocoder.zj.module.report.service.reporsubscription.ReportSubscriptionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import java.util.List;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

@Tag(name = "订阅管理")
@RestController
@RequestMapping("/report/subscription")
@Validated
public class ReportSubscriptionController {

    @Resource
    ReportSubscriptionService reportSubscriptionService;

    @GetMapping("/mypage")
    @Operation(summary = "报表页订阅",description = "报表页订阅")
    @PermitAll
    public CommonResult<SubscriptionRespVO> getByUserId() {
        return success(reportSubscriptionService.getByUserId());
    }

    @PutMapping("/update")
    @Operation(summary = "更新订阅任务",description = "更新订阅任务")
    @PreAuthorize("@ss.hasPermission('report:subscription:update')")
    public CommonResult<Boolean> updateSubscription(@RequestHeader(value = WebFrameworkUtils.HEADER_TENANT_ID,
                                                                required = false) Long tenantId,
                                                    @RequestBody SubscriptionRespVO subscriptionVO) {
        // 更新订阅任务
        subscriptionVO.setTenantId(tenantId);
        reportSubscriptionService.updateSubscription(subscriptionVO);
        return success(true);
    }
}
