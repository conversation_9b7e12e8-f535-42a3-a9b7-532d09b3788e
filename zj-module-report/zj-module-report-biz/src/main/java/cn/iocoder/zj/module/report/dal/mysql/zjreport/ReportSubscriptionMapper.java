package cn.iocoder.zj.module.report.dal.mysql.zjreport;

import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.report.api.subscription.dto.ReportSubscriptionDTO;
import cn.iocoder.zj.module.report.controller.admin.reporsubscription.VO.ReportSubscriptionUserVO;
import cn.iocoder.zj.module.report.controller.admin.reporsubscription.VO.SubscriptionRespVO;
import cn.iocoder.zj.module.report.dal.dataobject.reporsubscription.ReportSubscriptionDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ReportSubscriptionMapper {
    @TenantIgnore
    SubscriptionRespVO getByUserId(@Param("id") Long id);
    @TenantIgnore
    List<ReportSubscriptionUserVO> getTimedTasks();
    @TenantIgnore
    void updateReportSubscriptionById(@Param("taskList") List<ReportSubscriptionUserVO> taskList);
    @TenantIgnore
    void updateSingle(@Param("task") ReportSubscriptionUserVO task);
    @TenantIgnore
    List<ReportSubscriptionUserVO> getByUuid(@Param("uuidList")List<String> uuid);
    @TenantIgnore
    List<ReportSubscriptionDO> getAllSubscriptionType();

    void createDefaultReportSubscriptionBatch(@Param("toBeAddList")List<ReportSubscriptionUserVO> toBeAddList);
    void addSubscriptionTypeBatch(@Param("toBeAddedList")List<ReportSubscriptionDO> toBeAddedList);

    void deletedSubscriptionTypeByMethodName(@Param("toBeDeleted")List<String> toBeDeleted);

    void deletedUserSubscriptionByMethodName(@Param("methodNames")List<String> methodNames);
    @TenantIgnore
    List<ReportSubscriptionDTO> getAlarmSubscription();

    String getPlatFormIdByTenants(@Param("tenantIds")List<Long> tenantIds);
}
