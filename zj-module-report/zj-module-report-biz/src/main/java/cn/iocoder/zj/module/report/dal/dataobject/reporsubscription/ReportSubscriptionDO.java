package cn.iocoder.zj.module.report.dal.dataobject.reporsubscription;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@Schema(description = "报表订阅类")
public class ReportSubscriptionDO {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "订阅名称")
    private String name;

    @Schema(description = "bean名称")
    private String beanName;

    @Schema(description = "执行定时任务的方法名")
    private String methodName;

    @Schema(description = "订阅信息uuid")
    private String uuid;

    @Schema(description = "订阅周期，day每天，week每周，month每月")
    private String subscriptionCycle;

    @Schema(description = "发送时间")
    private String subscriptionTime;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "是否定时任务,1是，0否")
    private Integer isTimedTask;
}
