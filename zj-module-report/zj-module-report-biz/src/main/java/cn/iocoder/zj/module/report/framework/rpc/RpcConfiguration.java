package cn.iocoder.zj.module.report.framework.rpc;

import cn.iocoder.cloud.module.cloudedge.api.monitor.MonitorApi;
import cn.iocoder.zj.module.infra.api.file.FileApi;
import cn.iocoder.zj.module.monitor.api.tenantscreen.TenantScreenApi;
import cn.iocoder.zj.module.om.api.userbind.UserBindApi;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.tenant.TenantApi;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = { FileApi.class,
        TenantApi.class,
        PlatformconfigApi.class,
        PermissionApi.class,
        RoleApi.class,
        TenantScreenApi.class,
        AdminUserApi.class, UserBindApi.class, MonitorApi.class} )
public class RpcConfiguration {

}
