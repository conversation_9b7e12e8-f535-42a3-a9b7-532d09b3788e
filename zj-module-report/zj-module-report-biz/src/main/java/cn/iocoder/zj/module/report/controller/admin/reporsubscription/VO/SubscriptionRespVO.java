package cn.iocoder.zj.module.report.controller.admin.reporsubscription.VO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class SubscriptionRespVO {
    @Schema(description = "是否免打扰，1是，0否")
    private int doNotDisturb;

    @Schema(description = "邮箱")
    @Email(message = "邮箱格式不正确")
    @Size(max = 50, message = "邮箱长度不能超过 50 个字符")
    private String email;

    private String dingtalkPhone;

    private String wecomPhone;

    @Schema(description = "发送时间")
    private String subscriptionTime;

    /**
     * 租户编号
     */
    private Long tenantId;

    private List<ReportSubscriptionUserVO> reportSubscription;
}
