package cn.iocoder.zj.module.report.controller.admin.zjreport.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName : ZjReportExcelVO  //类名
 * @Description : excel导出  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/4/9  13:58
 */
@Data
public class ZjReportExcelVO {


    @ExcelProperty("资源名称")
    private String name;

    @ExcelProperty("平台名称")
    private String platformName;


    @ExcelProperty("资源类型")
    private String app;

    @ExcelProperty("告警数量")
    private Long alertCount;

    @ExcelProperty("工单数量")
    private Long TicketsCount;
}
