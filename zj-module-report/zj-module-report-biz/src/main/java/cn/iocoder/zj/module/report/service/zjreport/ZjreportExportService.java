package cn.iocoder.zj.module.report.service.zjreport;

import cn.iocoder.zj.module.report.controller.admin.zjreport.vo.ZjReportProcessRespVO;

import java.util.List;
import java.util.Map;

public interface ZjreportExportService {
    List<Map<String, Object>> getAlarmAndProcessByType(Long userId,Long platformId);

    List<Map<String, Object>> getAlarmAndProcessByPlatform(Long userId,Long platformId);

    List<Map<String, Object>> getAlarmTrigger(String timeCycle, Long userId,Long platformId,Boolean isYester);

    Map<String, Object> platformTrends(String time, Long userId,Long platformId);

    Map<String, Object> getGrowthTrendByType(String time, Long userId,Long platformIds);

    Map<String, List<ZjReportProcessRespVO>> processtop(String time, Long userId,Long platformId);

    Map<String, Object> platformTrendsNow(String week, Long userId, Long platformId);

    List<Map<String, Object>> getAlarmAndProcessByPlatformNow(Long userId, Long platformId);

    List<Map<String, Object>> getAlarmAndProcessByTypeYesterday(Long userId, Long platformId);

    Map<String, Object> getGrowthTrendByTypeNow(String date, Long userId, Long platformId);

    List<Map<String, Object>> getAlarmTriggerWeek(String date, Long userId, Long platformId, boolean b);

    Map<String, List<ZjReportProcessRespVO>> processtopYest(String date, Long userId, Long platformId);
}
