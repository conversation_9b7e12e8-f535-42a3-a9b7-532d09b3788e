package cn.iocoder.zj.module.report.timedTaskUtils;

import cn.hutool.core.util.ArrayUtil;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;

public class CronConverter {
    public static String generateCronExpression(String cycle, String timeStr) {
        List<String> time = Arrays.asList(timeStr.split(":"));
        String cronExpression = String.format("%s %s %s", time.get(2), time.get(1),time.get(0));
        if(cycle.equals("week")){
            cronExpression = cronExpression + " ? * MON";
        }else if(cycle.equals("month")){
            cronExpression = cronExpression + " 1 * ?";
        } else if (cycle.equals("day")) {
            cronExpression = cronExpression + " * * ?";
        }
        return cronExpression;
    }
}
