package cn.iocoder.zj.module.report.dal.dataobject.category;

import lombok.Data;

import java.util.List;

/**
 * @ClassName : CategoryDTO  //类名
 * @Description : DTO  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/3/19  15:09
 */
@Data
public class CategoryDTO {
    private String value;
    private String label;
    private List<SubCategoryDTO> children;

    public CategoryDTO(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public void setChildren(List<SubCategoryDTO> children) {
        this.children = children;
    }
}
