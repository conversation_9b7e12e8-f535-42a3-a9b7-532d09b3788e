package cn.iocoder.zj.module.report.controller.admin.zjreport;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.report.controller.admin.zjreport.vo.ZjReportProcessRespVO;
import cn.iocoder.zj.module.report.service.reporsubscription.ReportSubscriptionService;
import cn.iocoder.zj.module.report.service.zjreport.ZjreportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

/**
 * @ClassName : ZjProcessController  //类名
 * @Description : 过程报告  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/4/10  15:51
 */
@Tag(name = "报表管理 - 过程报告")
@RestController
@RequestMapping("/report/zjreport")
@Validated
public class ZjProcessController {

    @Resource
    ZjreportService zjreportService;

    @Resource
    ReportSubscriptionService reportSubscriptionService;

    @GetMapping("/processtop")
    @Operation(summary = "过程报告top10",description = "资产运维报表分页")
    @Parameter(name = "time", description = "报表周期，周报，week;月报，month", required = true, example = "week")
    @Parameter(name = "platformId", description = "平台id", required = true, example = "1")
    @TenantIgnore
    @PermitAll
    public CommonResult<Map<String,List<ZjReportProcessRespVO>>> processtop(@RequestParam("time")String time,
                                                                            @RequestParam(name = "platformId",required = false) Long platformId) {

        return success(zjreportService.processtop(time,platformId));
    }


    @GetMapping("/platformTrends")
    @Operation(summary = "平台告警和工单增长趋势",description = "过程报告")
    @Parameter(name = "time", description = "报表周期，周报，week;月报，month", required = true, example = "week")
    @Parameter(name = "platformId", description = "平台id", required = true, example = "1")
    @TenantIgnore
    @PermitAll
    public CommonResult<Map<String, Object>> platformTrends(@RequestParam("time")String time,
                                                            @RequestParam(name = "platformId",required = false) Long platformId) {

        return success(zjreportService.platformTrends(time,platformId));
    }


    @GetMapping("/growthTrendByType")
    @Operation(summary = "资产告警增长趋势")
    @Parameter(name = "time", description = "报表周期，周报，week;月报，month", required = true, example = "week")
    @Parameter(name = "platformId", description = "平台id", required = true, example = "1")
    @TenantIgnore
    @PermitAll
    public CommonResult<Map<String,Object>> getGrowthTrendByType(@RequestParam("time")String time,
                                                                 @RequestParam(name = "platformId",required = false) Long platformId) {
        return success(zjreportService.getGrowthTrendByType(time,platformId));
    }

    @GetMapping("/reportword")
    @Operation(summary = "导出周报日报")
    @PermitAll
    @TenantIgnore
    @Parameter(name = "type", description = "day日报,week周报,month月报", required = true, example = "dayReport")
    @OperateLog(type = EXPORT)
    @PreAuthorize("@ss.hasPermission('report:reportword:export')")
    public void exportReport(@RequestParam("type") String type,
                             @RequestParam("userId") Long userId,
                             @RequestParam(name = "platformId",required = false) Long platformId,
                             HttpServletResponse response) {
        if(type.equals("day")) {
            zjreportService.exportDayReportNow(userId, platformId,response);
        }else {
            zjreportService.exportWeekOrMonthReportNow(type,userId,platformId,response);
        }
    }
}
