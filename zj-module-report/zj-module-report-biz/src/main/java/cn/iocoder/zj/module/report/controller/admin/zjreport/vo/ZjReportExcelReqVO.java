package cn.iocoder.zj.module.report.controller.admin.zjreport.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @ClassName : ZjReportExcelReqVO  //类名
 * @Description : excel  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/4/9  14:07
 */
@Schema(description = "报表管理 - 资产运维报表 Excel 导出 Request VO，参数和 ZjReportPageReqVO 是一致的")
@Data
public class ZjReportExcelReqVO {

    @Schema(description = "客户ID")
    private String uuid;

    @Schema(description = "资源名称")
    private String name;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "平台id")
    private String platformId;

    @Schema(description = "资源类型")
    private String app;

    @Schema(description = "告警数量")
    private Long alertCount;

    @Schema(description = "工单数量")
    private Long TicketsCount;

    @Schema(description = "平台ids")
    private List<Long> platformIds;

}
