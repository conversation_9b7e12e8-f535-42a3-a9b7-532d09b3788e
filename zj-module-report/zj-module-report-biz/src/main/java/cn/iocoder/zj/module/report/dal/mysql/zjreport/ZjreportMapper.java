package cn.iocoder.zj.module.report.dal.mysql.zjreport;

import cn.iocoder.zj.framework.common.enums.ResourceAppEnum;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.report.controller.admin.zjreport.vo.ZjReportExcelReqVO;
import cn.iocoder.zj.module.report.controller.admin.zjreport.vo.ZjReportPageReqVO;
import cn.iocoder.zj.module.report.controller.admin.zjreport.vo.ZjReportPlatformRespVO;
import cn.iocoder.zj.module.report.controller.admin.zjreport.vo.ZjReportProcessRespVO;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @ClassName : ZjreportMapper  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/4/3  14:10
 */
@Mapper
public interface ZjreportMapper {

    @TenantIgnore
    List<Map> listBpmProcessList(@Param("tenantId")Long tenantId);

    @TenantIgnore
    @Slave
    List<Map> getAlertList(@Param("platformList")List<Map> platform);

    @TenantIgnore
    List<Map> selectReportPage(@Param("page") Page page, @Param("name") String name, @Param("type") String type, @Param("platformId") String platformId);

    @TenantIgnore
    List<ZjReportPageReqVO> selectMyzjreportPage(@Param("page") Page page, @Param("pageVO") ZjReportPageReqVO pageVO, @Param("subEnums") List<ResourceAppEnum> subEnums,@Param("platformList")List<Map> platform);

    @Slave
    @TenantIgnore
    List<Map<String,Object>> getAlarmReport(@Param("mpPage") IPage mpPage,
                                            @Param("mapList") List<Map> mapList);
    @Slave
    @TenantIgnore
    List<Map<String,Object>> getAlarmReports(@Param("mpPage") IPage mpPage,
                                            @Param("mapList") List<Map> mapList,
                                            @Param("pageParam") PageParam pageParam,
                                            @Param("monitorName")String monitorName,
                                            @Param("platformName")String platformName,
                                            @Param("appType")String appType,
                                            @Param("priority")Integer priority,
                                            @Param("platformId")Long platformId,
                                            @Param("subEnums") List<ResourceAppEnum> subEnums);
    @Slave
    @TenantIgnore
    Map<String, Object> getAlarmStatistics(@Param("today") String today,
                                              @Param("mapList")List<Map> mapList,
                                              @Param("timeCycle")String timeCycle,
                                              @Param("platformId")Long platformId);
    @TenantIgnore
    Map<String, Object> getProcessStatistics(@Param("today") String today,
                                             @Param("tenantId")Long tenantId,
                                             @Param("timeCycle")String timeCycle,
                                             @Param("platformId")Long platformId);

    @Slave
    @TenantIgnore
    List<Map<String, Object>> getAlarmTrigger(@Param("targetUuids")List<String> targetUuids,
                                              @Param("today") String today,
                                              @Param("timeCycle") String timeCycle);
    @TenantIgnore
    List<String> getUuidsByPlatform(@Param("mapList")List<Map> mapList,@Param("platformId")Long platformId);

    @Slave
    @TenantIgnore
    List<Map<String, Object>> getAlarmByType(@Param("mapList")List<Map> mapList,@Param("platformId")Long platformId);
    @TenantIgnore
    List<Map<String, Object>> getProcessByType(@Param("tenantId")Long tenantId,
                                               @Param("isSuperAdmin")Boolean isSuperAdmin);
    @TenantIgnore
    List<Map<String, Object>> getProcessByYesterType(@Param("tenantId")Long tenantId,
                                                     @Param("isSuperAdmin")Boolean isSuperAdmin);
    @Slave
    @TenantIgnore
    List<Map<String, Object>> getAlarmByPlatform(@Param("mapList")List<Map> mapList,@Param("platformId")Long platformId);
    @Slave
    @TenantIgnore
    List<Map<String, Object>> getAlarmByYesterPlatform(@Param("mapList")List<Map> mapList,@Param("platformId")Long platformId);
    @TenantIgnore
    List<Map<String, Object>> getProcessByPlatform(@Param("mapList")List<Map> mapList,@Param("platformId")Long platformId);
    @TenantIgnore
    List<Map<String, Object>> getProcessByYesterPlatform(@Param("mapList")List<Map> mapList,@Param("platformId")Long platformId);

    @TenantIgnore
    List<ZjReportExcelReqVO> exportzjReportInfoExcel(@Param("pageVO") ZjReportExcelReqVO exportReqVO, @Param("subEnums") List<ResourceAppEnum> subEnums);

    @Slave
    @TenantIgnore
    List<ZjReportProcessRespVO> getAlertTopList(@Param("time") String time, @Param("mapList") List<Map> mapList,@Param("platformId") Long platformId);
    @TenantIgnore
    List<Map> listBpmProcessTopList(@Param("time") String time,@Param("mapList")List<Map> mapList);

    @TenantIgnore
    List<Map> getMonitorList(@Param("mapList") List<Map> mapList,@Param("platformId") Long platformId);

    @Slave
    @TenantIgnore
    List<ZjReportPlatformRespVO> getPlatformAlertList(@Param("time") String time, @Param("mapList") List<Map> mapList, @Param("platformId") Long platformId);

    @TenantIgnore
    List<ZjReportPlatformRespVO> getPlatformBpmList(@Param("time") String time, @Param("mapList") List<Map> mapList, @Param("platformId") Long platformId);

    @Slave
    @TenantIgnore
    List<Map<String, Object>> getAlertGrowthTrendByType(@Param("time")String time,@Param("mapList") List<Map> mapList,@Param("platformId") Long platformId);

    @TenantIgnore
    List<Map<String, Object>> getProcessGrowthTrendByType(@Param("mapList") List<Map> mapList,
                                                          @Param("time")String time,
                                                          @Param("isSuperAdmin") boolean isSuperAdmin,
                                                          @Param("tenantId")Long tenantId,
                                                          @Param("platformId") Long platformId);

    @TenantIgnore
    String getWeChatUserOpenId(@Param("userId")Long userId);

    @Slave
    Long getAlertCountByUserPlatformIds(@Param("mapList")List<Map> mapList);

    @TenantIgnore
    List<ZjReportPageReqVO> selectMyzjreport(@Param("pageVO") ZjReportPageReqVO pageVO, @Param("subEnums") List<ResourceAppEnum> subEnums,@Param("platformList")List<Map> platform);

    @Slave
    @TenantIgnore
    List<Map<String, Object>> getAlarmByTypeYesterday(@Param("mapList")List<Map> mapList,@Param("platformId")Long platformId);

    @Slave
    @TenantIgnore
    List<ZjReportProcessRespVO> getAlertTopListYest(@Param("time") String time, @Param("mapList") List<Map> mapList,@Param("platformId") Long platformId);

    @TenantIgnore
    long getStateByTenantId(@Param("tenantId") Long tenantId);

    @TenantIgnore
    List<ZjReportPageReqVO> selectMyzjreportBystate(@Param("pageVO") ZjReportPageReqVO pageVO, @Param("subEnums") List<ResourceAppEnum> subEnums,@Param("platformList")List<Map> platform);

    @TenantIgnore
    List<ZjReportExcelReqVO> exportzjReportInfoByStateExcel(@Param("pageVO") ZjReportExcelReqVO exportReqVO, @Param("subEnums") List<ResourceAppEnum> subEnums);

    @Slave
    @TenantIgnore
    List<Map<String, Object>> getAlarmReportsByState(@Param("mpPage") IPage mpPage,
                                                     @Param("mapList") List<Map> mapList,
                                                     @Param("pageParam") PageParam pageParam,
                                                     @Param("monitorName")String monitorName,
                                                     @Param("platformName")String platformName,
                                                     @Param("appType")String appType,
                                                     @Param("priority")Integer priority,
                                                     @Param("platformId")Long platformId,
                                                     @Param("subEnums") List<ResourceAppEnum> subEnums);

    @Slave
    @TenantIgnore
    List<Map<String, Object>> exportAlarmReportByState(@Param("mpPage") IPage mpPage,
                                                       @Param("mapList") List<Map> mapList);
}
