package cn.iocoder.zj.module.report;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 订阅类型和相关菜单权限
 */
@Getter
@AllArgsConstructor
public enum SubscriptionEnum {
    REPORT("/report","reportSubscription",1,Arrays.asList(new String[]{"day","week","month"})),
    ALERT("warning","alertSubscription",1,Arrays.asList(new String[]{"day"}));

    //对应菜单表中的code
    private final String menu;
    //对应用户定时任务表中的方法名
    private final String methodName;
    //是否定时任务,0否，1是，为配合类似告警订阅的条件触发
    private final Integer isTimedTask;
    //推送周期
    private final List<String> defaultSubscriptionCycle;
}
