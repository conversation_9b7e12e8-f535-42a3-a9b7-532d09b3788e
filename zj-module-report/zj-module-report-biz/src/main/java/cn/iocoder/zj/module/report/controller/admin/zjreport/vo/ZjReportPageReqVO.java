package cn.iocoder.zj.module.report.controller.admin.zjreport.vo;

import cn.iocoder.zj.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @ClassName : ZjReportPageReqVO  //类名
 * @Description : 资产运维报表  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/4/8  10:26
 */
@Schema(description = "报表管理 - 资产运维报表分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ZjReportPageReqVO extends PageParam {

    @Schema(description = "客户ID")
    private String uuid;

    @Schema(description = "资源名称")
    private String name;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "平台id")
    private String platformId;

    @Schema(description = "资源类型")
    private String app;

    @Schema(description = "告警数量")
    private Long alertCount;

    @Schema(description = "工单数量")
    private Long TicketsCount;
}
