package cn.iocoder.zj.module.report.controller.admin.zjreport;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.module.report.controller.admin.zjreport.vo.AlarmReportExcelVo;
import cn.iocoder.zj.module.report.controller.admin.zjreport.vo.ZjReportExcelReqVO;
import cn.iocoder.zj.module.report.controller.admin.zjreport.vo.ZjReportExcelVO;
import cn.iocoder.zj.module.report.controller.admin.zjreport.vo.ZjReportPageReqVO;
import cn.iocoder.zj.module.report.convert.zjreport.ZjReportConvert;
import cn.iocoder.zj.module.report.dal.dataobject.category.CategoryDTO;
import cn.iocoder.zj.module.report.service.zjreport.ZjreportService;
import com.alibaba.fastjson.JSONArray;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.ibatis.annotations.Param;
import org.apache.hertzbeat.common.entity.dto.Message;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

/**
 * @ClassName : ZjReportController  //类名
 * @Description : 报表管理  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/4/3  11:34
 */
@Tag(name = "报表管理 - 资产运维报表")
@RestController
@RequestMapping("/report/zjreport")
@Validated
public class ZjReportController {

    @Resource
    ZjreportService zjreportService;


    @GetMapping("/my-page")
    @Operation(summary = "资产运维报表分页",description = "资产运维报表分页")
    public CommonResult<PageResult<ZjReportPageReqVO>> getMyzjreportPage(@Valid ZjReportPageReqVO pageVO) {
        PageResult<ZjReportPageReqVO> pageResult = zjreportService.getMyzjreportPage(pageVO);
        return success(pageResult);
    }


    @GetMapping("/select")
    @Operation(summary = "资产运维报表下拉框", description = "资产运维报表下拉框")
    public ResponseEntity<Message<List<CategoryDTO>>> getMonitorsBySelect() {
        List<CategoryDTO> monitorList = zjreportService.getMonitorsBySelect();
        Message<List<CategoryDTO>> message = Message.success(monitorList);
        return ResponseEntity.ok(message);
    }


    @GetMapping("/export-excel")
    @Operation(summary = "资产运维报表信息 Excel", description = "产运维报表信息 Excel")
    @OperateLog(type = EXPORT)
    @PreAuthorize("@ss.hasPermission('report:zjreport:export')")
    public void exportzjReportInfoExcel(@Valid ZjReportExcelReqVO exportReqVO,
                                        HttpServletResponse response) throws IOException {
        List<ZjReportExcelReqVO> list = zjreportService.exportzjReportInfoExcel(exportReqVO);
        // 导出 Excel
        List<ZjReportExcelVO> datas = ZjReportConvert.INSTANCE.convert(list);
        ExcelUtils.write(response, "资产运维报表信息.xls", "数据", ZjReportExcelVO.class, datas);
    }
    @GetMapping("/alarmReport")
    @Operation(summary = "获得告警报表页列表")
    @TenantIgnore
    @PermitAll
    public CommonResult<PageResult<Map<String,Object>>> getAlarmReport(@RequestParam("pageSize")Integer pageSize,
                                                                 @RequestParam("pageNo")Integer pageNo,
                                                                       @Param("sortBy")String sortBy,
                                                                       @Param("sortDirection")String sortDirection,
                                                                       @Param("monitorName")String monitorName,
                                                                       @Param("platformName")String platformName,
                                                                       @Param("appType")String appType,
                                                                       @Param("platformId")Long platformId,
                                                                       @Param("priority")Integer priority) {
        return success(zjreportService.getAlarmReport(pageSize,pageNo,sortBy,sortDirection,monitorName,platformName,appType,priority,platformId));
    }
    @GetMapping("/alarmStatistics")
    @Operation(summary = "过程报表——告警统计")
    @TenantIgnore
    @PermitAll
    @Parameter(name = "time", description = "报表周期，日报，day;周报，week;月报，month", required = true, example = "day")
    public CommonResult<Map<String,Object>> getAlarmStatistics(@RequestParam("time")String time,@RequestParam(name = "platformId",required = false) Long platformId) {
        return success(zjreportService.getAlarmStatistics(time,platformId));
    }
    @GetMapping("/processStatistics")
    @Operation(summary = "过程报表——工单统计")
    @TenantIgnore
    @PermitAll
    @Parameter(name = "time", description = "报表周期，日报，day;周报，week;月报，month", required = true, example = "day")
    public CommonResult<Map<String,Object>> getProcessStatistics(@RequestParam("time")String time,
                                                                 @RequestParam(name = "platformId",required = false) Long platformId) {
        return success(zjreportService.getProcessStatistics(time,platformId));
    }

    @GetMapping("/alarmTrigger")
    @Operation(summary = "过程报表——告警TOP10")
    @Parameter(name = "time", description = "报表周期，日报，day;周报，week;月报，month", required = true, example = "day")
    @TenantIgnore
    @PermitAll
    public CommonResult<List<Map<String,Object>>> getAlarmTrigger(@RequestParam("time")String time,@RequestParam(name = "platformId",required = false) Long platformId) {
        return success(zjreportService.getAlarmTrigger(time,platformId));
    }
    @GetMapping("/alarmAndProcessByType")
    @Operation(summary = "告警/工单资产分布统计")
    @TenantIgnore
    @PermitAll
    public CommonResult<List<Map<String,Object>>> getAlarmAndProcessByType(@RequestParam(name = "platformId",required = false) Long platformId) {
        return success(zjreportService.getAlarmAndProcessByType(platformId));
    }
    @GetMapping("/alarmAndProcessByPlatform")
    @Operation(summary = "告警/工单平台分布统计")
    @TenantIgnore
    @PermitAll
    public CommonResult<List<Map<String,Object>>> getAlarmAndProcessByPlatform(@RequestParam(name = "platformId",required = false) Long platformId) {
        return success(zjreportService.getAlarmAndProcessByPlatform(platformId));
    }
    @GetMapping("/exportAlarmReport")
    @Operation(summary = "导出告警报表 Excel")
    @OperateLog(type = EXPORT)
    @PreAuthorize("@ss.hasPermission('report:alarmreport:export')")
    public void exportAlarmReport(HttpServletResponse response) throws IOException {
        List<AlarmReportExcelVo> datas = zjreportService.exportAlarmReport();
        // 导出 Excel
        ExcelUtils.writeAlert(response, "告警报表.xlsx", "数据", AlarmReportExcelVo.class, datas);
    }
}
