package cn.iocoder.zj.module.report.controller.admin.reporsubscription.VO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@Schema(description = "报表订阅类")
public class ReportSubscriptionUserVO {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "订阅信息uuid")
    private String subscriptionUuid;

    @Schema(description = "订阅名称")
    private String name;

    @Schema(description = "bean名称")
    private String beanName;

    @Schema(description = "执行定时任务的方法名")
    private String methodName;

    @Schema(description = "订阅人id")
    private Long userId;

    @Schema(description = "订阅人名称")
    private String userName;

    @Schema(description = "发送时间")
    private String subscriptionTime;

    @Schema(description = "订阅周期，day每天，week每周，month每月")
    private String subscriptionCycle;

    @Schema(description = "cron表达式")
    private String cronExp;

    @Schema(description = "cron描述")
    private String cronDescribe;

    @Schema(description = "微信推送启用状态，0未启用，1已启用")
    private Integer wechatState;

    @Schema(description = "短信推送启用状态，0未启用，1已启用")
    private Integer emailState;

    @Schema(description = "定时任务状态，0暂停中,1进行中")
    private Integer taskState;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "用户邮箱")
    private String email;

    @Schema(description = "是否启用cron表达式，0（默认）不启用，1启用")
    private Integer cronIsEnable;

    @Schema(description = "是否定时任务,1是，0否")
    private Integer isTimedTask;

    @Schema(description = "是否免打扰，1是，0否")
    private int doNotDisturb;

    @Schema(description = "钉钉推送启用状态，0未启用，1已启用")
    private Integer dingtalkState;

    @Schema(description = "企微推送启用状态，0未启用，1已启用")
    private Integer wecomState;

    @Schema(description = "钉钉应用key")
    private String appKey;

    @Schema(description = "钉钉应用秘钥")
    private String appSecret;

    @Schema(description = "钉钉应用id")
    private String agentId;

    @Schema(description = "钉钉用户")
    private String dingtalkPhone;

    @Schema(description = "企微应用id")
    private String wecomAgentId;

    @Schema(description = "企微企业id")
    private String corpid;

    @Schema(description = "企微企业秘钥")
    private String corpsecret;

    @Schema(description = "企微手机号")
    private String wecomPhone;

    private String tenantId;
}
