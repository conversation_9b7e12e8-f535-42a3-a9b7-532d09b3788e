package cn.iocoder.zj.module.report.convert.zjreport;

import cn.iocoder.zj.module.report.controller.admin.zjreport.vo.ZjReportExcelReqVO;
import cn.iocoder.zj.module.report.controller.admin.zjreport.vo.ZjReportExcelVO;
import cn.iocoder.zj.module.report.controller.admin.zjreport.vo.ZjReportPageReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ZjReportConvert {
    
    ZjReportConvert INSTANCE = Mappers.getMapper(ZjReportConvert.class);

    List<ZjReportExcelVO> convert(List<ZjReportExcelReqVO> list);
}
