package cn.iocoder.zj.module.report.service.zjreport;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.report.controller.admin.zjreport.vo.AlarmReportExcelVo;
import cn.iocoder.zj.module.report.controller.admin.zjreport.vo.ZjReportExcelReqVO;
import cn.iocoder.zj.module.report.controller.admin.zjreport.vo.ZjReportPageReqVO;
import cn.iocoder.zj.module.report.controller.admin.zjreport.vo.ZjReportProcessRespVO;
import cn.iocoder.zj.module.report.dal.dataobject.category.CategoryDTO;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.Map;

public interface ZjreportService {

    PageResult<ZjReportPageReqVO> getMyzjreportPage(ZjReportPageReqVO pageVO);

    List<CategoryDTO> getMonitorsBySelect();
    PageResult<Map<String,Object>> getAlarmReport(Integer pageSize,Integer pageNo,String sortBy,String sortDirection,String monitorName,String platformName,String appType,Integer priority,Long platformId);

    Map<String,Object> getAlarmStatistics(String timeCycle,Long platformId);

    Map<String,Object> getProcessStatistics(String timeCycle,Long platformId);

    List<Map<String,Object>> getAlarmTrigger(String time,Long platformId);

    List<Map<String,Object>> getAlarmAndProcessByType(Long platformId);

    List<Map<String,Object>> getAlarmAndProcessByPlatform( Long platformId);

    List<ZjReportExcelReqVO> exportzjReportInfoExcel(@Valid ZjReportExcelReqVO exportReqVO);

    List<AlarmReportExcelVo> exportAlarmReport();

    Map<String, List<ZjReportProcessRespVO>> processtop(String time,Long platformId);

    Map<String, Object> platformTrends(String time,Long platformId);

    Map<String,Object> getGrowthTrendByType(String time,Long platformId);

    ByteArrayOutputStream exportDayReport(Long userId,Long platformId,HttpServletResponse response);

    ByteArrayOutputStream exportWeekOrMonthReport(String date,Long userId,Long platformId,HttpServletResponse response);

    String getWeChatUserOpenId(Long userId);

    Long getAlertCountByUserPlatformIds(Long userId);

    ByteArrayOutputStream exportDayReportNow(Long userId, Long platformId, HttpServletResponse response);

    ByteArrayOutputStream exportWeekOrMonthReportNow(String type, Long userId, Long platformId, HttpServletResponse response);
}
