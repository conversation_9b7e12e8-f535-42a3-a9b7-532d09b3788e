package cn.iocoder.zj.module.report.controller.admin.zjreport.vo;

import cn.iocoder.zj.framework.excel.core.annotations.DictFormat;
import cn.iocoder.zj.framework.excel.core.annotations.MergeCell;
import cn.iocoder.zj.framework.excel.core.convert.AlertLevelConvert;
import cn.iocoder.zj.framework.excel.core.convert.CellMergeStrategy;
import cn.iocoder.zj.framework.excel.core.convert.DictConvert;
import cn.iocoder.zj.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Map;

import static com.alibaba.excel.enums.poi.HorizontalAlignmentEnum.CENTER;

@Data
@ToString(callSuper = true)
//没有@ExcelProperty注解的字段不导出
@ExcelIgnoreUnannotated
@HeadStyle(horizontalAlignment = CENTER,verticalAlignment = VerticalAlignmentEnum.CENTER)
public class AlarmReportExcelVo {

    @MergeCell(needMerge = true,isMk = true)
    @ExcelProperty(value = "资源uuid",index = 0)
    @ContentStyle(horizontalAlignment = CENTER,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String monitorId;

    @ExcelProperty(value = "资源名称",index = 1)
    @MergeCell(needMerge = true,isMk = true)
    @ContentStyle(horizontalAlignment = CENTER,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String monitorName;

    @ExcelProperty(value = "平台名称",index = 2)
    @MergeCell(needMerge = true)
    @ContentStyle(horizontalAlignment = CENTER,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String platformName;

    @ExcelProperty(value = "资源类型",index = 3)
    @MergeCell(needMerge = true)
    @ContentStyle(horizontalAlignment = CENTER,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String appType;

    @ExcelProperty(value = "告警内容",index = 4)
    @ContentStyle(horizontalAlignment = CENTER,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String content;

    @ExcelProperty(value = "告警级别",index = 5,converter = AlertLevelConvert.class)
    @ContentStyle(horizontalAlignment = CENTER,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private Integer priority;

    @ExcelProperty(value = "告警次数",index = 6)
    @ContentStyle(horizontalAlignment = CENTER,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private Long times;

    private Long platformId;
}
