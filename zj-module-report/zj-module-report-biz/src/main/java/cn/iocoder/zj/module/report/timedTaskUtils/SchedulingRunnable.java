package cn.iocoder.zj.module.report.timedTaskUtils;


import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.module.report.controller.admin.reporsubscription.VO.ReportSubscriptionUserVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ReflectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Objects;

/**
 * 定时任务启动类
 */
@Slf4j
public class SchedulingRunnable implements Runnable{
    //定时任务业务实现类TaskBeans中Component注解的值
    private String beanName;
//    TaskBeans类中的方法名称
    private String methodName;
    //定时任务的uuid，唯一识别
    private String uuid;

    private ReportSubscriptionUserVO subscriptionVO;


    public SchedulingRunnable(String beanName,String methodName, String uuid) {
        this.beanName = beanName;
        this.methodName = methodName;
        this.uuid = uuid;
    }

    public SchedulingRunnable(String beanName,String methodName, String uuid, ReportSubscriptionUserVO subscriptionVO) {
        this.beanName = beanName;
        this.methodName = methodName;
        this.uuid = uuid;
        this.subscriptionVO = subscriptionVO;
    }


    @Override
    public void run() {
        log.info("定时任务开始执行 - bean：{}，任务id：{}，", beanName, uuid);
        long startTime = System.currentTimeMillis();
        try {
            //获取bean
            Object target = SpringContextUtils.getBean(beanName);
            log.info("target{}",target);
            Method method = null;
            //根据定时任务的methodName获取bean中对应的方法
            if (ObjectUtil.isNotEmpty(subscriptionVO)) {
                method = target.getClass().getDeclaredMethod(methodName,ReportSubscriptionUserVO.class);
                log.info("method1{}",method);
            } else {
                method = target.getClass().getDeclaredMethod(methodName);
                log.info("method2{}",method);
            }
            ReflectionUtils.makeAccessible(method);

            //向方法中传递参数
            log.info("subscriptionVO{}",subscriptionVO);
            if (ObjectUtil.isNotEmpty(subscriptionVO)) {
                method.invoke(target,subscriptionVO);
            } else {
                method.invoke(target);
            }

        } catch (Exception ex) {
            log.error(String.format("定时任务执行异常 - bean：%s，任务id：%s，", beanName, uuid), ex);
        }

        long times = System.currentTimeMillis() - startTime;
        log.info("定时任务执行结束 - bean：{}，任务id：{}，耗时：{} 毫秒", beanName, subscriptionVO.getUserId()+"-"+uuid, times);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SchedulingRunnable that = (SchedulingRunnable) o;
            return beanName.equals(that.beanName) &&
                    uuid.equals(that.uuid);
    }

    @Override
    public int hashCode() {
      return Objects.hash(beanName, beanName);
    }
}
