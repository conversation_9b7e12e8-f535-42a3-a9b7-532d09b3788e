package cn.iocoder.zj.module.report.controller.admin.zjreport.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @ClassName : ZjReportProcessRespVO  //类名
 * @Description : 过程报告top  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/4/10  17:20
 */
@Schema(description = "报表管理 - 过程报告top10 Response VO")
@Data
public class ZjReportProcessRespVO {
    @Schema(description = "资源名称")
    private String name;
    @Schema(description = "平台名称")
    private String platformName;
    @Schema(description = "告警次数")
    private Long alertCount;
    @Schema(description = "工单数量")
    private Long TicketsCount;
}
