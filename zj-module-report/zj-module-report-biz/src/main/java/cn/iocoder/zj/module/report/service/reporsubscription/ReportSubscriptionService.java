package cn.iocoder.zj.module.report.service.reporsubscription;

import cn.iocoder.zj.module.report.api.subscription.dto.ReportSubscriptionDTO;
import cn.iocoder.zj.module.report.controller.admin.reporsubscription.VO.SubscriptionRespVO;

import java.util.List;

public interface ReportSubscriptionService {
    SubscriptionRespVO getByUserId();

    void updateSubscription(SubscriptionRespVO subscriptionVO);

    List<ReportSubscriptionDTO> getAlarmSubscription();
    void removeTimedTask(Long userId);
}
