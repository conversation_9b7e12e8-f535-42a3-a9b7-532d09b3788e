package cn.iocoder.zj.module.report.service.zjreport;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import cn.iocoder.cloud.module.cloudedge.api.monitor.MonitorApi;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.enums.ResourceAppEnum;
import cn.iocoder.zj.framework.common.enums.ResourceCategoryEnum;
import cn.iocoder.zj.framework.common.exception.ErrorCode;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.util.category.ResourceCategoryUtil;
import cn.iocoder.zj.framework.common.util.date.DateUtils;
import cn.iocoder.zj.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.api.tenantscreen.TenantScreenApi;
import cn.iocoder.zj.module.report.controller.admin.zjreport.vo.*;
import cn.iocoder.zj.module.report.dal.dataobject.category.CategoryDTO;
import cn.iocoder.zj.module.report.dal.dataobject.category.SubCategoryDTO;
import cn.iocoder.zj.module.report.dal.mysql.zjreport.ZjreportMapper;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.*;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.singleton;


/**
 * @ClassName : ZjreportServiceImpl  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/4/3  14:01
 */
@Service
@Validated
@Slf4j
public class ZjreportServiceImpl implements ZjreportService {
    @Resource
    ZjreportMapper zjreportMapper;
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    RoleApi roleApi;
    @Resource
    PermissionApi permissionApi;

    @Resource
    AdminUserApi adminUserApi;
    @Resource
    TenantScreenApi tenantScreenApi;
    @Resource
    ZjreportExportService zjreportExportService;
    @Resource
    MonitorApi monitorApi;


    public static List<ZjReportPageReqVO> page(List<ZjReportPageReqVO> dataList, int pageSize, int currentPage) {
        List<ZjReportPageReqVO> currentPageList = new ArrayList<>();
        if (dataList != null && dataList.size() > 0) {
            int currIdx = (currentPage > 1 ? (currentPage - 1) * pageSize : 0);
            for (int i = 0; i < pageSize && i < dataList.size() - currIdx; i++) {
                ZjReportPageReqVO data = dataList.get(currIdx + i);
                currentPageList.add(data);
            }
        }
        return currentPageList;
    }

    @Override
    public PageResult<ZjReportPageReqVO> getMyzjreportPage(ZjReportPageReqVO pageVO) {
        Integer pageNo = pageVO.getPageNo();
        Integer pageSize = pageVO.getPageSize();
        Page page = new Page();
        page.setCurrent(pageNo);
        page.setSize(pageSize);
        LoginUser currentUser = SecurityFrameworkUtils.getLoginUser();
        long state = zjreportMapper.getStateByTenantId(currentUser.getTenantId());
        List<Map> platfrom = platformconfigApi.getPlatformSelectList(String.valueOf(currentUser.getTenantId())).getData();
        List<ResourceAppEnum> subEnums = new ArrayList<>();
        if (StrUtil.isNotEmpty(pageVO.getApp())) {
            if (!pageVO.getApp().equals("host") && !pageVO.getApp().equals("hardware") && !pageVO.getApp().equals("storage")) {
                subEnums = ResourceCategoryUtil.getSubEnumsByCategory(pageVO.getApp());

                if (subEnums != null) {
                    pageVO.setApp("hz");
                }
            }

        }
        List<ZjReportPageReqVO> list = new ArrayList<>();
        if (StringUtil.isNotEmpty(pageVO.getSortBy()) && StringUtil.isNotEmpty(pageVO.getSortDirection()) && (pageVO.getSortBy().equals("alertCount") || pageVO.getSortBy().equals("ticketsCount"))) {
            if (CollectionUtil.isNotEmpty(platfrom)) {
                if (state == 1) {
                    list = zjreportMapper.selectMyzjreportBystate(pageVO, subEnums, platfrom);
                } else {
                    list = zjreportMapper.selectMyzjreport(pageVO, subEnums, platfrom);
                }
            }

            List<Map> bpm = zjreportMapper.listBpmProcessList(currentUser.getTenantId());
            List<Map> alert = zjreportMapper.getAlertList(platfrom);

            for (ZjReportPageReqVO zjReportPageReqVO : list) {
                zjReportPageReqVO.setTicketsCount(0L);
                zjReportPageReqVO.setAlertCount(0L);
                for (Map map : bpm) {
                    if (zjReportPageReqVO.getUuid().equals(map.get("sourceId"))) {
                        zjReportPageReqVO.setTicketsCount(Convert.toLong(map.get("count")));
                    }
                }

                for (Map map : alert) {
                    if (zjReportPageReqVO.getUuid().equals(map.get("monitor_id"))) {
                        zjReportPageReqVO.setAlertCount(Convert.toLong(map.get("count")));
                    }
                }
            }
            if (pageVO.getSortDirection().equals("asc")) {
                if (pageVO.getSortBy().equals("alertCount")) {
                    list.sort(Comparator.comparingLong(ZjReportPageReqVO::getAlertCount));
                }
                if (pageVO.getSortBy().equals("ticketsCount")) {
                    list.sort(Comparator.comparingLong(ZjReportPageReqVO::getTicketsCount));
                }
            } else {
                if (pageVO.getSortBy().equals("alertCount")) {
                    list.sort(Comparator.comparingLong(ZjReportPageReqVO::getAlertCount).reversed());
                }
                if (pageVO.getSortBy().equals("ticketsCount")) {
                    list.sort(Comparator.comparingLong(ZjReportPageReqVO::getTicketsCount).reversed());
                }
            }
            for (ZjReportPageReqVO zjReportPageReqVO : list) {
                if (zjReportPageReqVO.getApp().equals("host")) {
                    zjReportPageReqVO.setApp("云主机");
                } else if (zjReportPageReqVO.getApp().equals("storage")) {
                    zjReportPageReqVO.setApp("云存储");
                } else if (zjReportPageReqVO.getApp().equals("hardware")) {
                    zjReportPageReqVO.setApp("宿主机");
                } else {
                    String app = ResourceCategoryUtil.getResourceLabel(zjReportPageReqVO.getApp());
                    zjReportPageReqVO.setApp(app);
                }
            }
            page.setTotal(list.size());
            list = page(list, pageSize, pageNo);
        } else {
            if (CollectionUtil.isNotEmpty(platfrom)) {
                if (state == 1) {
                    list = zjreportMapper.selectMyzjreportBystate(pageVO, subEnums, platfrom);
                } else {
                    list = zjreportMapper.selectMyzjreport(pageVO, subEnums, platfrom);
                }
            }
            List<Map> bpm = zjreportMapper.listBpmProcessList(currentUser.getTenantId());
            List<Map> alert = zjreportMapper.getAlertList(platfrom);

            for (ZjReportPageReqVO zjReportPageReqVO : list) {
                zjReportPageReqVO.setTicketsCount(0L);
                zjReportPageReqVO.setAlertCount(0L);
                for (Map map : bpm) {
                    if (zjReportPageReqVO.getUuid().equals(map.get("sourceId"))) {
                        zjReportPageReqVO.setTicketsCount(Convert.toLong(map.get("count")));
                    }
                }

                for (Map map : alert) {
                    if (zjReportPageReqVO.getUuid().equals(map.get("monitor_id"))) {
                        zjReportPageReqVO.setAlertCount(Convert.toLong(map.get("count")));
                    }
                }
            }
            for (ZjReportPageReqVO zjReportPageReqVO : list) {
                if (zjReportPageReqVO.getApp().equals("host")) {
                    zjReportPageReqVO.setApp("云主机");
                } else if (zjReportPageReqVO.getApp().equals("storage")) {
                    zjReportPageReqVO.setApp("云存储");
                } else if (zjReportPageReqVO.getApp().equals("hardware")) {
                    zjReportPageReqVO.setApp("宿主机");
                } else {
                    String app = ResourceCategoryUtil.getResourceLabel(zjReportPageReqVO.getApp());
                    zjReportPageReqVO.setApp(app);
                }
            }
            page.setTotal(list.size());
            list = page(list, pageSize, pageNo);
        }
        PageResult<ZjReportPageReqVO> result = new PageResult<ZjReportPageReqVO>().setList(list).setTotal(page.getTotal());
        return result;
    }

    @Override
    public List<CategoryDTO> getMonitorsBySelect() {
        List<CategoryDTO> categories = new ArrayList<>();

        // 遍历枚举中的每个父级
        for (ResourceCategoryEnum parentEnum : ResourceCategoryEnum.values()) {


            // 创建父级对象
            CategoryDTO parentDTO = new CategoryDTO(parentEnum.getCode(), parentEnum.getName());
            List<SubCategoryDTO> children = new ArrayList<>();

            if (parentEnum.getSubEnums() != null) {
                // 遍历父级的子级枚举
                for (ResourceAppEnum childEnum : parentEnum.getSubEnums()) {
                    // 创建子级对象
                    SubCategoryDTO childDTO = new SubCategoryDTO(childEnum.getValue(), childEnum.getLabel());
                    // 将子级对象添加到父级的子级列表中
                    children.add(childDTO);
                }
            }
            // 设置父级对象的子级列表
            parentDTO.setChildren(children);
            // 将父级对象添加到最终的列表中
            categories.add(parentDTO);

        }
        return categories;
    }

    @Override
    public List<ZjReportExcelReqVO> exportzjReportInfoExcel(@Valid ZjReportExcelReqVO exportReqVO) {
        List<ZjReportExcelReqVO> list = new ArrayList<>();
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        long state = zjreportMapper.getStateByTenantId(loginUser.getTenantId());
        List<ResourceAppEnum> subEnums = new ArrayList<>();
        if (StrUtil.isNotEmpty(exportReqVO.getApp())) {
            if (!exportReqVO.getApp().equals("host") && !exportReqVO.getApp().equals("hardware") && !exportReqVO.getApp().equals("storage")) {
                subEnums = ResourceCategoryUtil.getSubEnumsByCategory(exportReqVO.getApp());
                if (subEnums != null) {
                    exportReqVO.setApp("hz");
                }
            }
        }
        List<Long> platformIds = new ArrayList<>();
        List<Map> mapList = platformconfigApi.getPlatformListByUserId(String.valueOf(loginUser.getId())).getData();
        if (CollectionUtil.isNotEmpty(mapList)) {
            platformIds = mapList.stream()
                    .map(map -> Long.parseLong(String.valueOf(map.get("platformId"))))
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(platformIds)) {
                exportReqVO.setPlatformIds(platformIds);
            }

            if (state == 1) {
                list = zjreportMapper.exportzjReportInfoByStateExcel(exportReqVO, subEnums);
            } else {
                list = zjreportMapper.exportzjReportInfoExcel(exportReqVO, subEnums);
            }


            List<Map> bpm = zjreportMapper.listBpmProcessList(loginUser.getTenantId());
            List<Map> alert = zjreportMapper.getAlertList(mapList);

            for (ZjReportExcelReqVO zjReportPageReqVO : list) {
                zjReportPageReqVO.setTicketsCount(0L);
                zjReportPageReqVO.setAlertCount(0L);
                for (Map map : bpm) {
                    if (zjReportPageReqVO.getUuid().equals(map.get("sourceId"))) {
                        zjReportPageReqVO.setTicketsCount(Convert.toLong(map.get("count")));
                    }
                }
                for (Map map : alert) {
                    if (zjReportPageReqVO.getUuid().equals(map.get("monitor_id"))) {
                        zjReportPageReqVO.setAlertCount(Convert.toLong(map.get("count")));
                    }
                }

                if (zjReportPageReqVO.getApp().equals("host")) {
                    zjReportPageReqVO.setApp("云主机");
                } else if (zjReportPageReqVO.getApp().equals("storage")) {
                    zjReportPageReqVO.setApp("存储");
                } else if (zjReportPageReqVO.getApp().equals("hardware")) {
                    zjReportPageReqVO.setApp("宿主机");
                } else {
                    String app = ResourceCategoryUtil.getResourceLabel(zjReportPageReqVO.getApp());
                    zjReportPageReqVO.setApp(app);
                }
            }
        }

        return list;
    }

    @Override
    public List<AlarmReportExcelVo> exportAlarmReport() {
        LoginUser currentUser = SecurityFrameworkUtils.getLoginUser();
        long state = zjreportMapper.getStateByTenantId(currentUser.getTenantId());
        List<Map> mapList = platformconfigApi.getPlatformSelectList(String.valueOf(currentUser.getTenantId())).getData();
        List<AlarmReportExcelVo> datas = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(mapList)) {
            List<Map<String, Object>> alarmReportList;
            if (state == 1) {
                alarmReportList = zjreportMapper.exportAlarmReportByState(null, mapList);
            } else {
                alarmReportList = zjreportMapper.getAlarmReport(null, mapList);
            }

            JSONArray jsonArray = new JSONArray();
            jsonArray.addAll(alarmReportList);
            datas = jsonArray.toList(AlarmReportExcelVo.class);
            datas.forEach(alert -> {
                mapList.forEach(plat -> {
                    if (StringUtils.equalsIgnoreCase(String.valueOf(alert.getPlatformId()), String.valueOf(plat.get("platformId")))) {
                        alert.setPlatformName(String.valueOf(plat.get("platformName")));
                    }
                });
            });
            ResourceAppEnum[] values = ResourceAppEnum.values();
            for (ResourceAppEnum item : values) {
                for (AlarmReportExcelVo alarmReportExcelVo : datas) {
                    if (item.getValue().equals(alarmReportExcelVo.getAppType())) {
                        alarmReportExcelVo.setAppType(item.getLabel());
                    } else if (alarmReportExcelVo.getAppType().equals("host")) {
                        alarmReportExcelVo.setAppType("云主机");
                    } else if (alarmReportExcelVo.getAppType().equals("hardware")) {
                        alarmReportExcelVo.setAppType("宿主机");
                    } else if (alarmReportExcelVo.getAppType().equals("storage")) {
                        alarmReportExcelVo.setAppType("主存储");
                    }
                }
            }
        }
        return datas;
    }

    @Override
    public PageResult<Map<String, Object>> getAlarmReport(Integer pageSize, Integer pageNo, String sortBy,
                                                          String sortDirection, String monitorName, String platformName, String appType, Integer priority, Long platformId) {
        PageParam pageParam = new PageParam();
        pageParam.setPageNo(pageNo);
        pageParam.setPageSize(pageSize);
        pageParam.setSortBy(sortBy);
        pageParam.setSortDirection(sortDirection);
        IPage mpPage = MyBatisUtils.buildPage(pageParam);
        LoginUser currentUser = SecurityFrameworkUtils.getLoginUser();
        long state = zjreportMapper.getStateByTenantId(currentUser.getTenantId());
        List<ResourceAppEnum> subEnums = new ArrayList<>();
        if (StrUtil.isNotEmpty(appType)) {
            if (!appType.equals("host") && !appType.equals("hardware") && !appType.equals("storage")) {
                subEnums = ResourceCategoryUtil.getSubEnumsByCategory(appType);
                appType = null;
            }
        }
        List<Map> mapList = platformconfigApi.getPlatformSelectList(String.valueOf(currentUser.getTenantId())).getData();
        List<Map<String, Object>> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(mapList)) {
            if (state == 1) {
                result = zjreportMapper.getAlarmReportsByState(mpPage, mapList, pageParam, monitorName, platformName, appType, priority, platformId, subEnums);
            } else {
                result = zjreportMapper.getAlarmReports(mpPage, mapList, pageParam, monitorName, platformName, appType, priority, platformId, subEnums);
            }
        }
        if (CollectionUtil.isNotEmpty(result)) {
            ResourceAppEnum[] values = ResourceAppEnum.values();
            for (ResourceAppEnum item : values) {
                for (Map<String, Object> map : result) {
                    if (item.getValue().equals(map.get("appType"))) {
                        map.put("appType", item.getLabel());
                    } else if (map.get("appType").equals("host")) {
                        map.put("appType", "云主机");
                    } else if (map.get("appType").equals("hardware")) {
                        map.put("appType", "宿主机");
                    } else if (map.get("appType").equals("storage")) {
                        map.put("appType", "云存储");
                    }
                }
            }
            result.forEach(alert -> {
                mapList.forEach(plat -> {
                    if (StringUtils.equalsIgnoreCase(String.valueOf(alert.get("platformId")), String.valueOf(plat.get("platformId")))) {
                        alert.put("platform_name", plat.get("platformName"));
                    }
                });
            });
        }
        return new PageResult<>(result, mpPage.getTotal());
    }

    @Override
    public Map<String, Object> getAlarmStatistics(String timeCycle, Long platformId) {
        LoginUser currentUser = SecurityFrameworkUtils.getLoginUser();
        List<Map> mapList = platformconfigApi.getPlatformSelectList(String.valueOf(currentUser.getTenantId())).getData();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String today = sdf.format(new Date());
        String endDay = today;
        if (timeCycle.equals("week")) {
            endDay = sdf.format(DateUtil.beginOfWeek(DateUtil.date()));
        } else if (timeCycle.equals("month")) {
            endDay = sdf.format(DateUtil.beginOfMonth(DateUtil.date()));
        }
        if (mapList != null && mapList.size() > 0) {
            return zjreportMapper.getAlarmStatistics(today, mapList, timeCycle, platformId);
        } else {
            Map<String, Object> result = new HashMap<>();
            result.put("solved", 0);
            result.put("total", 0);
            result.put("unsolved", 0);
            result.put("timeEnd", today);
            result.put("timeStart", endDay);
            return result;
        }
    }

    @Override
    public Map<String, Object> getProcessStatistics(String timeCycle, Long platformId) {
        LoginUser currentUser = SecurityFrameworkUtils.getLoginUser();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String today = sdf.format(new Date());
        return zjreportMapper.getProcessStatistics(today, currentUser.getTenantId(), timeCycle, platformId);
    }

    @Override
    public List<Map<String, Object>> getAlarmTrigger(String timeCycle, Long platformId) {
        LoginUser currentUser = SecurityFrameworkUtils.getLoginUser();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String today = sdf.format(new Date());
        List<Map> mapList = platformconfigApi.getPlatformSelectList(String.valueOf(currentUser.getTenantId())).getData();
        List<Map<String, Object>> resultList = new ArrayList<>();
        List<Map<String, Object>> top10List = new ArrayList<>();

        if (mapList != null && mapList.size() > 0) {
            List<String> targetUuids = zjreportMapper.getUuidsByPlatform(mapList, platformId);
            if (CollectionUtil.isNotEmpty(targetUuids)) {
                // 设置批次大小为1000
                int batchSize = 1000;
                int totalSize = targetUuids.size();

                // 分批处理
                for (int i = 0; i < totalSize; i += batchSize) {
                    int endIndex = Math.min(i + batchSize, totalSize);
                    List<String> batchUuids = targetUuids.subList(i, endIndex);
                    // 查询当前批次的数据
                    List<Map<String, Object>> batchResult = zjreportMapper.getAlarmTrigger(batchUuids, today, timeCycle);
                    if (CollectionUtil.isNotEmpty(batchResult)) {
                        resultList.addAll(batchResult);
                    }
                }
            }
        }

        if(resultList.size() > 10){
            resultList = resultList.stream()
                    .sorted((m1, m2) -> {
                        Number n1 = (Number) m1.get("times");
                        Number n2 = (Number) m2.get("times");
                        return Long.compare(n2.longValue(), n1.longValue());
                    })
                    .limit(10)
                    .collect(Collectors.toList());

        }

        return resultList;
    }

    @Override
    public List<Map<String, Object>> getAlarmAndProcessByType(Long platformId) {


        //获取当前用户的平台信息
        LoginUser currentUser = SecurityFrameworkUtils.getLoginUser();
        // 如果是状态是1 定制版，则过滤私有云数据
        long state = zjreportMapper.getStateByTenantId(currentUser.getTenantId());
        List<Map> mapList = platformconfigApi.getPlatformSelectList(String.valueOf(currentUser.getTenantId())).getData();
        List<Map<String, Object>> datalist = new ArrayList<>();
        //枚举资源类型
        List<String> appTypeList = new ArrayList<>();
        appTypeList.add("host");
        appTypeList.add("hardware");
        appTypeList.add("storage");
        ResourceAppEnum[] values = ResourceAppEnum.values();
        for (ResourceAppEnum item : values) {
            appTypeList.add(item.getValue());
        }

        // 定义一个映射，将应用类型映射到其相应的列表
        Map<String, List<String>> appTypeMap = defineTypeMa();

        //合并doris和mysql数据
        List<Map<String, Object>> alarmByType = new ArrayList<>();
        if (mapList != null && mapList.size() > 0) {
            alarmByType = zjreportMapper.getAlarmByType(mapList, platformId);

            alarmByType = supplementType(appTypeList, alarmByType);

            Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(currentUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
            Boolean isSuperAdmin = roleApi.decideAnySuperAdmin(roleIds);

            List<Map<String, Object>> processByType = zjreportMapper.getProcessByType(currentUser.getTenantId(), isSuperAdmin);
            processByType = supplementType(appTypeList, processByType);
            List<Map<String, Object>> mixMapList = new ArrayList<>();
            for (Map<String, Object> a : alarmByType) {
                for (Map<String, Object> p : processByType) {
                    if (p.get("app").equals(a.get("app"))) {
                        Map<String, Object> mix = new HashMap<>();
                        mix.put("app", a.get("app"));
                        mix.put("alarm", a.get("total"));
                        mix.put("process", p.get("total"));
                        mixMapList.add(mix);
                    }
                }
            }
            //资源类型归并

            for (Map.Entry<String, List<String>> entry : appTypeMap.entrySet()) {
                Map<String, Object> maps = new HashMap();
                String appType = entry.getKey();
                String label = getResourceLabel(entry.getKey());
                List<String> apps = entry.getValue();
                // 遍历当前类型的每个应用
                Long alarm = 0L;
                Long process = 0L;
                for (String app : apps) {
                    for (Map map : mixMapList) {
                        if (app.equals(map.get("app"))) {
                            alarm = alarm + Convert.toLong(map.get("alarm"));
                            process = process + Convert.toLong(map.get("process"));
                        }
                    }
                }
                maps.put("code", appType);
                maps.put("app", label);
                maps.put("alarm", alarm);
                maps.put("process", process);
                datalist.add(maps);
            }
        }

        if (!datalist.isEmpty()) {
            // 如果 state 等于 1，过滤掉指定的指标
            if (state == 1) {
                // 定义需要过滤的指标名称列表
                List<String> filterApps = Arrays.asList("云主机", "宿主机", "云存储", "存储", "服务器", "网络");
                datalist = datalist.stream()
                        .filter(map -> !filterApps.contains(map.get("app")))
                        .collect(Collectors.toList());
            }
        }

        return datalist;
    }

    @Override
    public List<Map<String, Object>> getAlarmAndProcessByPlatform(Long platformId) {
        //获取当前用户的平台信息
        LoginUser currentUser = SecurityFrameworkUtils.getLoginUser();
        List<Map> mapList = platformconfigApi.getPlatformSelectList(String.valueOf(currentUser.getTenantId())).getData();
        //过滤掉非选择的平台
        if (ObjectUtil.isNotEmpty(platformId)) {
            mapList = mapList.stream()
                    .filter(map -> map.containsKey("platformId") && Long.parseLong(String.valueOf(map.get("platformId"))) == platformId)
                    .collect(Collectors.toList());
        }
        List<Map<String, Object>> mixMapList = new ArrayList<>();
        List<Map<String, Object>> alarmByType = new ArrayList<>();
        List<Map<String, Object>> processByType = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(mapList)) {
            alarmByType = zjreportMapper.getAlarmByPlatform(mapList, platformId);
            processByType = zjreportMapper.getProcessByPlatform(mapList, platformId);
        }
        for (Map plat : mapList) {
            Map<String, Object> mixMap = new HashMap<>();
            mixMap.put("platformId", plat.get("platformId"));
            mixMap.put("platformName", plat.get("platformName"));
            mixMap.put("alarm", 0);
            mixMap.put("process", 0);
            for (Map alarm : alarmByType) {
                if (String.valueOf(alarm.get("platformId")).equals(String.valueOf(plat.get("platformId")))) {
                    mixMap.put("alarm", alarm.get("total"));
                }
            }
            for (Map process : processByType) {
                if (String.valueOf(process.get("platformId")).equals(String.valueOf(plat.get("platformId")))) {
                    mixMap.put("process", process.get("total"));
                }
            }
            mixMapList.add(mixMap);
        }


        return mixMapList;
    }

    private List<Map<String, Object>> supplementType(List<String> appTypeList, List<Map<String, Object>> mapList) {
        Integer size = mapList.size();
        for (String app : appTypeList) {
            boolean exist = false;
            if (size > 0) {
                for (int i = 0; i < size; i++) {
                    if (mapList.get(i).get("app").equals(app)) {
                        exist = true;
                        break;
                    }
                }
                if (!exist) {
                    Map<String, Object> alarmMap = new HashMap<>();
                    alarmMap.put("app", app);
                    alarmMap.put("total", 0);
                    mapList.add(alarmMap);
                }
            } else {
                Map<String, Object> alarmMap = new HashMap<>();
                alarmMap.put("app", app);
                alarmMap.put("total", 0);
                mapList.add(alarmMap);
            }
        }
        return mapList;
    }

    private String getResourceLabel(String app) {
        for (ResourceCategoryEnum resource : ResourceCategoryEnum.values()) {
            if (resource.getCode().equals(app)) {
                return resource.getName();
            }
            // 检查是否包含在当前大类的枚举列表中
            if (resource.getSubEnums() != null && resource.getSubEnums().stream().anyMatch(subEnum -> subEnum.getValue().equals(app))) {
                return resource.getName();
            }
        }
        return null; // 如果没有匹配的枚举值，返回null或者其他默认值
    }

    private Map<String, List<String>> defineTypeMa() {
        Map<String, List<String>> appTypeMap = new HashMap<>();
        appTypeMap.put(ResourceCategoryEnum.APP_SERVICE.getCode(), Arrays.asList("website", "port", "ssl_cert", "ping", "websocket", "api", "dns", "fullsite", "api_code", "udp_port"));
        appTypeMap.put(ResourceCategoryEnum.APP_DB.getCode(), Arrays.asList("mysql", "oracle", "sqlserver", "mariadb", "dm", "redis", "redis_cluster", "redis_sentinel", "memcached", "tidb", "nebulaGraph", "opengauss", "mongodb"));
        appTypeMap.put(ResourceCategoryEnum.APP_MID.getCode(), Arrays.asList("nacos", "zookeeper", "kafka", "rabbitmq", "nginx", "pop3", "ftp", "smtp", "ntp", "spring_gateway", "emqx", "activemq", "rocketmq", "shenyu", "rabbitmq", "almalinux"));
        appTypeMap.put(ResourceCategoryEnum.APP_OS.getCode(), Arrays.asList("windows", "linux", "ubuntu", "coreos", "opensuse", "rockylinux", "euleros", "redhat", "centos", "freebsd", "debian"));
        appTypeMap.put(ResourceCategoryEnum.APP_CN.getCode(), Arrays.asList("docker", "kubernetes"));
//        appTypeMap.put(ResourceCategoryEnum.APP_PROMETHEUS.getCode(), Collections.singletonList("prometheus"));
        appTypeMap.put(ResourceCategoryEnum.APP_FIREWALL.getCode(), Arrays.asList("qax_firewall", "sswk_firewall", "sswk_defense", "sxf_firewall", "ad_firewall", "ac_firewall", "ah_firewall", "ah_waf", "lm_defense", "lm_waf", "trx_defense", "trx_firewall"));
        appTypeMap.put(ResourceCategoryEnum.APP_NETWORK.getCode(), Arrays.asList("h3c_switch", "cisco_switch", "huawei_switch", "tplink_switch", "hpe_switch"));
        appTypeMap.put(ResourceCategoryEnum.APP_STORED.getCode(), Arrays.asList("storage_huawei", "storage_inspur", "storage_synology"));
        appTypeMap.put(ResourceCategoryEnum.APP_INFRA.getCode(), Arrays.asList("infra_ipmi"));
        appTypeMap.put(ResourceCategoryEnum.APP_HOST.getCode(), Arrays.asList("host"));
        appTypeMap.put(ResourceCategoryEnum.APP_HARDWARE.getCode(), Arrays.asList("hardware"));
        appTypeMap.put(ResourceCategoryEnum.APP_STORAGE.getCode(), Arrays.asList("storage"));
        return appTypeMap;
    }


    @Override
    public Map<String, List<ZjReportProcessRespVO>> processtop(String time, Long platformId) {
        LoginUser currentUser = SecurityFrameworkUtils.getLoginUser();
        List<Map> mapList = platformconfigApi.getPlatformSelectList(String.valueOf(currentUser.getTenantId())).getData();
        if (ObjectUtil.isNotEmpty(platformId)) {
            mapList = mapList.stream()
                    .filter(map -> map.containsKey("platformId") && Long.parseLong(String.valueOf(map.get("platformId"))) == platformId)
                    .collect(Collectors.toList());
        }
        if (mapList != null && mapList.size() > 0) {
            List<Map> list = zjreportMapper.getMonitorList(mapList, platformId);
            List<Map> bpm = zjreportMapper.listBpmProcessTopList(time, mapList);
            List<ZjReportProcessRespVO> zjReportProcessRespVOS = new ArrayList<>();
            for (Map map : bpm) {
                for (Map lists : list) {
                    if (map.get("sourceId").equals(lists.get("uuid"))) {
                        ZjReportProcessRespVO zjReportProcessRespVO = new ZjReportProcessRespVO();
                        zjReportProcessRespVO.setName(lists.get("name").toString());
                        zjReportProcessRespVO.setPlatformName(lists.get("platformName").toString());
                        zjReportProcessRespVO.setTicketsCount(Convert.toLong(map.get("count")));
                        zjReportProcessRespVOS.add(zjReportProcessRespVO);

                    }
                }
            }
            List<ZjReportProcessRespVO> alert = zjreportMapper.getAlertTopList(time, mapList, platformId);
            Map map = new HashMap();
            map.put("alert", alert);
            map.put("bpm", zjReportProcessRespVOS);
            return map;
        } else {
            List nullList = new ArrayList<>();
            Map map = new HashMap();
            map.put("alert", nullList);
            map.put("bpm", nullList);
            return map;
        }
    }


    @Override
    public Map<String, Object> platformTrends(String time, Long platformId) {
        LoginUser currentUser = SecurityFrameworkUtils.getLoginUser();
        Map<String, Object> resultMap = new HashMap<>();
        List<String> dates = "week".equals(time) ? DateUtils.getLastWeekDates() : DateUtils.getLastMonthDates();
        List<Map> platformConfigList = platformconfigApi.getPlatformSelectList(String.valueOf(currentUser.getTenantId())).getData();
        //过滤掉非选择的平台
        if (ObjectUtil.isNotEmpty(platformId)) {
            platformConfigList = platformConfigList.stream()
                    .filter(map -> map.containsKey("platformId") && Long.parseLong(String.valueOf(map.get("platformId"))) == platformId)
                    .collect(Collectors.toList());
        }
        if (platformConfigList != null && platformConfigList.size() > 0) {
            List<ZjReportPlatformRespVO> alertData = zjreportMapper.getPlatformAlertList(time, platformConfigList, platformId);
            List<ZjReportPlatformRespVO> bpmData = zjreportMapper.getPlatformBpmList(time, platformConfigList, platformId);
            Map<String, Map<String, Object>> platformNameMapping = platformConfigList.stream().collect(Collectors.toMap(config -> config.get("platformId").toString(), config -> config));
            resultMap.put("alert", processPlatformData(dates, alertData, platformNameMapping));
            resultMap.put("bpm", processPlatformData(dates, bpmData, platformNameMapping));
            resultMap.put("date", dates);
            return resultMap;
        } else {
            List nullList = new ArrayList<>();
            resultMap.put("alert", nullList);
            resultMap.put("bpm", nullList);
            resultMap.put("date", nullList);
            return resultMap;
        }
    }

    private List<Map<String, Object>> processPlatformData(List<String> dates, List<ZjReportPlatformRespVO> platformData, Map<String, Map<String, Object>> platformNameMapping) {

        // 如果 platformData 为空或者尺寸为0
        if (platformData == null || platformData.isEmpty()) {
            // 构建并返回默认platformMap列表，每个platformMap的count都是0
            return platformNameMapping.entrySet().stream().map(entry -> {
                Map<String, Object> platformMap = new HashMap<>();
                platformMap.put("platformId", entry.getKey());
                platformMap.put("platformName", entry.getValue().get("platformName"));
                platformMap.put("date", dates);
                // 注意，这里我们将count置为一个所有值都为0的列表，列表的长度与dates相同
                List<Integer> counts = dates.stream().map(date -> 0).collect(Collectors.toList());
                platformMap.put("count", counts);
                return platformMap;
            }).collect(Collectors.toList());
        }

        Map<String, List<ZjReportPlatformRespVO>> reportMap = platformData.stream()
                .collect(Collectors.groupingBy(ZjReportPlatformRespVO::getPlatformId));
        return platformNameMapping.entrySet().stream().map(entry -> {
            Map<String, Object> platformMap = new HashMap<>();
            platformMap.put("platformId", entry.getKey());
            platformMap.put("platformName", entry.getValue().get("platformName"));
            platformMap.put("date", dates);
            // 注意，这里我们将count置为一个所有值都为0的列表，列表的长度与dates相同
            List<Long> counts = dates.stream().map(date -> 0L).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(reportMap.get(entry.getKey()))) {
                counts = calculateCounts(reportMap.get(entry.getKey()), dates);
            }
            platformMap.put("count", counts);
            return platformMap;
        }).collect(Collectors.toList());

//        return platformData.stream().collect(Collectors.groupingBy(ZjReportPlatformRespVO::getPlatformId)).entrySet().stream().map(entry -> {
//            Map<String, Object> platformMap = new HashMap<>();
//            platformMap.put("platformId", entry.getKey());
//            platformMap.put("platformName", platformNameMapping.get(entry.getKey()).get("platformName"));
//            platformMap.put("date", dates);
//            platformMap.put("count", calculateCounts(entry.getValue(), dates));
//            return platformMap;
//        }).collect(Collectors.toList());
    }

    private List<Long> calculateCounts(List<ZjReportPlatformRespVO> platformVOs, List<String> dates) {
        return dates.stream().map(date -> platformVOs.stream().filter(vo -> DateUtil.format(vo.getDate(), "yyyy-MM-dd").equals(date)).mapToLong(ZjReportPlatformRespVO::getCount).sum()).collect(Collectors.toList());
    }


    @Override
    public Map<String, Object> getGrowthTrendByType(String time, Long platformId) {
        LoginUser currentUser = SecurityFrameworkUtils.getLoginUser();

        long state = zjreportMapper.getStateByTenantId(currentUser.getTenantId());
        List<Map> mapList = platformconfigApi.getPlatformSelectList(String.valueOf(currentUser.getId())).getData();
        //过滤掉非选择的平台
        if (ObjectUtil.isNotEmpty(platformId)) {
            mapList = mapList.stream()
                    .filter(map -> map.containsKey("platformId") && Long.parseLong(String.valueOf(map.get("platformId"))) == platformId)
                    .collect(Collectors.toList());
        }
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(currentUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        Boolean isSuperAdmin = roleApi.decideAnySuperAdmin(roleIds);
        Map<String, Object> result = new HashMap<>();
        if (mapList != null && mapList.size() > 0) {
            //获取日期列表
            List<String> dateList = "week".equals(time) ? DateUtils.getLastWeekDates() : DateUtils.getLastMonthDates();
            result.put("dateList", dateList);
            //枚举资源类型
            List<String> appTypeList = new ArrayList<>();
            appTypeList.add("host");
            appTypeList.add("hardware");
            appTypeList.add("storage");
            ResourceAppEnum[] values = ResourceAppEnum.values();
            for (ResourceAppEnum item : values) {
                appTypeList.add(item.getValue());
            }

            //为模板map赋值
            List<Map<String, Object>> alertGrowthMap = typeGroupCoulate(appTypeList, zjreportMapper.getAlertGrowthTrendByType(time, mapList, platformId), dateList);
            List<Map<String, Object>> processGrowthMap = typeGroupCoulate(appTypeList, zjreportMapper.getProcessGrowthTrendByType(mapList, time, isSuperAdmin, currentUser.getTenantId(), platformId), dateList);

            // 如果 state 等于 1，过滤掉指定的指标
            if (state == 1) {
                // 定义需要过滤的指标名称列表
                List<String> filterApps = Arrays.asList("云主机", "宿主机", "云存储", "存储", "服务器", "网络");
                alertGrowthMap = alertGrowthMap.stream()
                        .filter(map -> !filterApps.contains(map.get("app")))
                        .collect(Collectors.toList());
                processGrowthMap = processGrowthMap.stream()
                        .filter(map -> !filterApps.contains(map.get("app")))
                        .collect(Collectors.toList());
            }
            result.put("alertValueList", alertGrowthMap);
            result.put("processValueList", processGrowthMap);
        } else {
            result.put("alertValueList", new ArrayList<>());
            result.put("processValueList", new ArrayList<>());
        }
        return result;
    }

    private List<Map<String, Object>> typeGroupCoulate(List<String> appTypeList, List<Map<String, Object>> alertGrowthMap, List<String> dateList) {
        //建立模板map
        List<Map<String, Object>> zeroGrowthMapList = new ArrayList<>();
        dateList.forEach(date -> {
            appTypeList.forEach(app -> {
                Map<String, Object> zeroGrowthMap = new HashMap<>();
                zeroGrowthMap.put("dateStr", date);
                zeroGrowthMap.put("app", app);
                zeroGrowthMap.put("total", 0);
                zeroGrowthMapList.add(zeroGrowthMap);
            });
        });
        zeroGrowthMapList.forEach(zeroGrowthMap -> {
            alertGrowthMap.forEach(item -> {
                if (zeroGrowthMap.get("dateStr").equals(item.get("dateStr")) && zeroGrowthMap.get("app").equals(item.get("app"))) {
                    zeroGrowthMap.put("total", item.get("total"));
                }
            });
        });

        //按日期分组，并将小类归并到大类
        Map<String, List<String>> appTypeMap = defineTypeMa();
        List<Map<String, Object>> dateGroupList = new ArrayList<>();
        zeroGrowthMapList.stream().collect(Collectors.groupingBy(map -> map.get("dateStr"))).forEach((k, v) -> {
            for (Map.Entry<String, List<String>> entry : appTypeMap.entrySet()) {
                Map<String, Object> maps = new HashMap();
                String label = getResourceLabel(entry.getKey());
                List<String> apps = entry.getValue();
                // 遍历当前类型的每个应用
                Long total = 0L;
                for (String app : apps) {
                    for (Map map : v) {
                        if (app.equals(map.get("app"))) {
                            total = total + Convert.toLong(map.get("total"));
                        }
                    }
                }
                maps.put("app", label);
                maps.put("total", total);
                maps.put("dateStr", k);
                dateGroupList.add(maps);
            }
        });

        //按类型分组，获取每个大类每天的count数
        List<Map<String, Object>> appGroupList = dateGroupList.stream().collect(Collectors.groupingBy(map -> map.get("app"))).entrySet().stream().map(entry -> {
            Map<String, Object> platformMap = new HashMap<>();
            platformMap.put("app", entry.getKey());
            platformMap.put("date", dateList);
            List<Long> total = dateList.stream().map(date -> entry.getValue().stream().filter(vo -> vo.get("dateStr").equals(date)).mapToLong(vo -> Convert.toLong(vo.get("total"))).sum()).collect(Collectors.toList());
            ;
            platformMap.put("total", total);
            return platformMap;
        }).collect(Collectors.toList());

        return appGroupList;
    }


    @Override
    public ByteArrayOutputStream exportDayReport(Long userId, Long platformId, HttpServletResponse response) {
        Map<String, Object> paramMap = new HashMap<>();
        LocalDate todayNow = LocalDate.now();
        LocalDate yesterday = todayNow.minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = yesterday.format(formatter);
        String[] duration = new String[]{"报告周期", formattedDate + " 00:00:00" + " — " + formattedDate + " 24:00:00"};
        RowRenderData durationRow = Rows.of(duration).center().create();
        String[] date = new String[]{"报告生成时间", DateUtil.format(DateUtil.date(), "yyyy-MM-dd HH:mm:ss")};
        RowRenderData dateRow = Rows.of(date).center().create();

        Tables.TableBuilder tableBuilder = Tables.of().width(14.63f, new double[]{3.8, 10.83});
        tableBuilder.addRow(durationRow);
        tableBuilder.addRow(dateRow);
        paramMap.put("durationInfo", tableBuilder.create());
        AdminUserRespDTO user = adminUserApi.getUser(userId).getData();
        log.info("user获取到的数据为{}", user);
        List<Map> mapList = platformconfigApi.getPlatformListByUserId(String.valueOf(user.getId())).getData();
        if (CollectionUtil.isEmpty(mapList)) {
            throw exception(new ErrorCode(2001000001, "暂无平台数据"));
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String today = sdf.format(DateUtils.getYesterday());
        // 告警饼图
        Map<String, Object> map = zjreportMapper.getAlarmStatistics(today, mapList, "day", platformId);

        ChartSingleSeriesRenderData pie = Charts.ofSingleSeries("告警统计", new String[]{"未处理", "已处理"}).series("告警数", new Integer[]{Convert.toInt(map.get("unsolved")), Convert.toInt(map.get("solved"))}).create();
        paramMap.put("pieChart", pie);


        //  工单饼图
        Map<String, Object> mapst = zjreportMapper.getProcessStatistics(today, user.getTenantId(), "day", platformId);
        ChartSingleSeriesRenderData ststat = Charts.ofSingleSeries("工单统计", new String[]{"已处理", "未处理", "回退"}).series("工单数", new Integer[]{Convert.toInt(mapst.get("finished")), Convert.toInt(mapst.get("unfinished")), Convert.toInt(mapst.get("reject"))}).create();
        paramMap.put("pieChart2", ststat);


        // 柱状图(告警/工单资产分布统计)
        List<Map<String, Object>> list = zjreportExportService.getAlarmAndProcessByTypeYesterday(userId, platformId);
        // 准备存储柱状图所需的数据集
        String[] categories = new String[list.size()];
        Long[] alarmCounts = new Long[list.size()];
        Long[] processCounts = new Long[list.size()];
        // 聚集数据
        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> map1 = list.get(i);
            categories[i] = (String) map1.get("app"); // 名称
            alarmCounts[i] = Long.parseLong(String.valueOf(map1.get("alarm"))); // 告警数量
            processCounts[i] = Long.parseLong(String.valueOf(map1.get("process"))); // 工单数量
        }
        List<SeriesRenderData> seriesDatas = new ArrayList<>();
        seriesDatas.add(new SeriesRenderData("告警数量", alarmCounts));
        seriesDatas.add(new SeriesRenderData("工单数量", processCounts));
        // 创建柱状图数据
        ChartMultiSeriesRenderData barChart = new ChartMultiSeriesRenderData();
        barChart.setChartTitle("告警与工单统计");
        barChart.setCategories(categories);
        barChart.setSeriesDatas(seriesDatas);
        paramMap.put("barChart", barChart);

        // 告警/工单平台分布统计
        List<Map<String, Object>> platformList = zjreportExportService.getAlarmAndProcessByPlatform(userId, platformId);
        // 准备存储柱状图所需的数据集
        String[] platformName = new String[platformList.size()];
        Long[] platformAlarmCounts = new Long[platformList.size()];
        Integer[] platformProcessCounts = new Integer[platformList.size()];
        // 聚集数据
        for (int i = 0; i < platformList.size(); i++) {
            Map<String, Object> map1 = platformList.get(i);
            platformName[i] = (String) map1.get("platformName"); // 名称
            platformAlarmCounts[i] = Long.parseLong(String.valueOf(map1.get("alarm"))); // 告警数量
            platformProcessCounts[i] = Integer.parseInt(String.valueOf(map1.get("process"))); // 工单数量
        }
        List<SeriesRenderData> platdata = new ArrayList<>();
        platdata.add(new SeriesRenderData("告警数量", platformAlarmCounts));
        platdata.add(new SeriesRenderData("工单数量", platformProcessCounts));
        // 创建柱状图数据
        ChartMultiSeriesRenderData platchart = new ChartMultiSeriesRenderData();
        platchart.setChartTitle("告警/工单平台分布统计");
        platchart.setCategories(platformName);
        platchart.setSeriesDatas(platdata);
        paramMap.put("barChart2", platchart);

        // 使用率排行榜
        log.info("user 输出日志{}", user);
        Long userTenantId = user.getTenantId();
        Map<String, Object> mapTop5 = tenantScreenApi.getScreenUsageTop(user.getId(), platformId, "1d", 1, userTenantId);

        log.info("TOP5资源：" + mapTop5.size());
        List<String> xnlbTemp1 = Arrays.asList("主机名称", "平均使用率");
        List<String> headerCellList1 = new ArrayList<>(xnlbTemp1);
        String[] headerCell1 = headerCellList1.toArray(new String[headerCellList1.size()]);
        RowRenderData header1 = Rows.of(headerCell1).center().bgColor("DFDED9").create();
        log.info("TOP5之JSONStr：" + mapTop5);
        for (String key : mapTop5.keySet()) {
            JSONArray arrList = JSONUtil.parseArray(JSONUtil.toJsonPrettyStr(mapTop5.get(key)));
            List<RowRenderData> rows = new ArrayList<>();
            rows.add(header1);
            if (CollectionUtil.isEmpty(arrList)) {
                // 将行数据转换为表格数据
                TableRenderData table = Tables.create(rows.toArray(new RowRenderData[0]));
                // table 只有表头数据
                paramMap.put(key, table);
            } else {
                for (Object m : arrList) {
                    JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(m));
                    String name = obj.getString("name");
                    double value = obj.getDoubleValue("value");
                    RowRenderData dataRow = Rows.of(name, String.format("%.2f%%", value)).create();
                    rows.add(dataRow);
                }
                // 将行数据转换为表格数据
                TableRenderData table = Tables.create(rows.toArray(new RowRenderData[0]));
                paramMap.put(key, table);
            }
        }
        List<Map<String, Object>> kd = zjreportExportService.getAlarmTrigger("day", userId, platformId, true);
        List<RowRenderData> kdrows = new ArrayList<>();


        List<String> alarm = Arrays.asList("告警规则", "资源名称", "告警级别", "告警次数");
        List<String> alarmCellList1 = new ArrayList<>(alarm);
        String[] alarmCell1 = alarmCellList1.toArray(new String[alarmCellList1.size()]);
        RowRenderData alarm1 = Rows.of(alarmCell1).center().bgColor("DFDED9").create();
        kdrows.add(alarm1); // 首先添加标题行
        //无数据只有表头数据 tableList 无值
        if (CollectionUtil.isEmpty(kd)) {
            // 将行数据转换为表格数据
            TableRenderData table = Tables.create(kdrows.toArray(new RowRenderData[0]));
            // table 只有表头数据
            paramMap.put("check_table4", table);
        } else {
            for (Map m : kd) {
                String content = (String) m.get("content");
                String monitor_name = (String) m.get("monitor_name");
                int priorityValue = Convert.toInt(m.get("priority"));
                // 转换优先级值到对应的文字描述
                String priority = "";
                switch (priorityValue) {
                    case 0:
                        priority = "严重";
                        break;
                    case 1:
                        priority = "警告";
                        break;
                    case 2:
                        priority = "提示";
                        break;
                    default:
                        priority = "未知"; // 处理未知或者其他值的情况
                }
                String times = Convert.toStr(m.get("times"));
                RowRenderData dataRow = Rows.of(content, monitor_name, priority, times).create();
                kdrows.add(dataRow);
            }
            // 将行数据转换为表格数据
            TableRenderData table = Tables.create(kdrows.toArray(new RowRenderData[0]));
            paramMap.put("check_table4", table);
        }

        Map<String, Object> maps = zjreportExportService.platformTrends("week", userId, platformId);
        List<Map> alertLine = (List<Map>) maps.get("alert");

        // 创建一个用于保存所有系列的列表
        ChartMultiSeriesRenderData chartMultiSeriesRenderData = new ChartMultiSeriesRenderData();
        chartMultiSeriesRenderData.setChartTitle("平台使用情况统计");
        List<SeriesRenderData> seriesDatass = new ArrayList<>(); // 创建存储多个系列数据的列表
        List<String> categoriesDate = null; // 日期，
        for (Map map1 : alertLine) {
            List<String> dates = (List<String>) map1.get("date");
            categoriesDate = dates;
            String pname = (String) map1.get("platformName"); // 系列名称将使用平台名称

            // 将count列表转换为Double数组，以满足POI-TL组件的要求
            Double[] countValues = (Double[]) ((List) map1.get("count")).stream()
                    .map(obj -> Double.valueOf(obj.toString())).toArray(Double[]::new);

            // 为每个平台创建一个系列并添加到系列列表中
            seriesDatass.add(new SeriesRenderData(pname, countValues));
        }
        // 设置图表的类别（X轴）和系列（Y轴）
        chartMultiSeriesRenderData.setCategories(categoriesDate.toArray(new String[0]));
        chartMultiSeriesRenderData.setSeriesDatas(seriesDatass);

        paramMap.put("lineCharts", chartMultiSeriesRenderData);
        if (ObjectUtil.isEmpty(response)) {
            return createAndDownloadWord(paramMap, "day", null);
        } else {
            createAndDownloadWord(paramMap, response, "day", null);
            return null;
        }
    }

    public ByteArrayOutputStream exportDayReportNow(Long userId, Long platformId, HttpServletResponse response) {
        Map<String, Object> paramMap = new HashMap<>();
        String[] duration = new String[]{"报告周期", DateUtil.format(DateUtil.date(), "yyyy-MM-dd") + " 00:00:00" + " — " + DateUtil.format(DateUtil.date(), "yyyy-MM-dd") + " 24:00:00"};
        RowRenderData durationRow = Rows.of(duration).center().create();
        String[] date = new String[]{"报告生成时间", DateUtil.format(DateUtil.date(), "yyyy-MM-dd HH:mm:ss")};
        RowRenderData dateRow = Rows.of(date).center().create();

        Tables.TableBuilder tableBuilder = Tables.of().width(14.63f, new double[]{3.8, 10.83});
        tableBuilder.addRow(durationRow);
        tableBuilder.addRow(dateRow);
        paramMap.put("durationInfo", tableBuilder.create());
        AdminUserRespDTO user = adminUserApi.getUser(userId).getData();
        long state = zjreportMapper.getStateByTenantId(user.getTenantId());
        List<Map> mapList = platformconfigApi.getPlatformListByUserId(String.valueOf(user.getId())).getData();
        if (CollectionUtil.isEmpty(mapList)) {
            throw exception(new ErrorCode(2001000001, "暂无平台数据"));
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String today = sdf.format(new Date());
        // 告警饼图
        Map<String, Object> map = zjreportMapper.getAlarmStatistics(today, mapList, "day", platformId);

        ChartSingleSeriesRenderData pie = Charts.ofSingleSeries("告警统计", new String[]{"未处理", "已处理"}).series("告警数", new Integer[]{Convert.toInt(map.get("unsolved")), Convert.toInt(map.get("solved"))}).create();
        paramMap.put("pieChart", pie);


        //  工单饼图
        Map<String, Object> mapst = zjreportMapper.getProcessStatistics(today, user.getTenantId(), "day", platformId);
        ChartSingleSeriesRenderData ststat = Charts.ofSingleSeries("工单统计", new String[]{"已处理", "未处理", "回退"}).series("工单数", new Integer[]{Convert.toInt(mapst.get("finished")), Convert.toInt(mapst.get("unfinished")), Convert.toInt(mapst.get("reject"))}).create();
        paramMap.put("pieChart2", ststat);


        // 柱状图(告警/工单资产分布统计)
        List<Map<String, Object>> list = zjreportExportService.getAlarmAndProcessByType(userId, platformId);
        // 准备存储柱状图所需的数据集
        String[] categories = new String[list.size()];
        Long[] alarmCounts = new Long[list.size()];
        Long[] processCounts = new Long[list.size()];
        // 聚集数据
        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> map1 = list.get(i);
            categories[i] = (String) map1.get("app"); // 名称
            alarmCounts[i] = Long.parseLong(String.valueOf(map1.get("alarm"))); // 告警数量
            processCounts[i] = Long.parseLong(String.valueOf(map1.get("process"))); // 工单数量
        }
        List<SeriesRenderData> seriesDatas = new ArrayList<>();
        seriesDatas.add(new SeriesRenderData("告警数量", alarmCounts));
        seriesDatas.add(new SeriesRenderData("工单数量", processCounts));
        // 创建柱状图数据
        ChartMultiSeriesRenderData barChart = new ChartMultiSeriesRenderData();
        barChart.setChartTitle("告警与工单统计");
        barChart.setCategories(categories);
        barChart.setSeriesDatas(seriesDatas);
        paramMap.put("barChart", barChart);

        // 告警/工单平台分布统计
        List<Map<String, Object>> platformList = zjreportExportService.getAlarmAndProcessByPlatformNow(userId, platformId);
        // 准备存储柱状图所需的数据集
        String[] platformName = new String[platformList.size()];
        Long[] platformAlarmCounts = new Long[platformList.size()];
        Integer[] platformProcessCounts = new Integer[platformList.size()];
        // 聚集数据
        for (int i = 0; i < platformList.size(); i++) {
            Map<String, Object> map1 = platformList.get(i);
            platformName[i] = (String) map1.get("platformName"); // 名称
            platformAlarmCounts[i] = Long.parseLong(String.valueOf(map1.get("alarm"))); // 告警数量
            platformProcessCounts[i] = Integer.parseInt(String.valueOf(map1.get("process"))); // 工单数量
        }
        List<SeriesRenderData> platdata = new ArrayList<>();
        platdata.add(new SeriesRenderData("告警数量", platformAlarmCounts));
        platdata.add(new SeriesRenderData("工单数量", platformProcessCounts));
        // 创建柱状图数据
        ChartMultiSeriesRenderData platchart = new ChartMultiSeriesRenderData();
        platchart.setChartTitle("告警/工单平台分布统计");
        platchart.setCategories(platformName);
        platchart.setSeriesDatas(platdata);
        paramMap.put("barChart2", platchart);

        List<String> xnlbTemp1 = Arrays.asList("主机名称", "平均使用率");
        List<String> headerCellList1 = new ArrayList<>(xnlbTemp1);
        String[] headerCell1 = headerCellList1.toArray(new String[headerCellList1.size()]);
        RowRenderData header1 = Rows.of(headerCell1).center().bgColor("DFDED9").create();

        // 使用率排行榜
        if (state == 1) {
            // 使用新接口获取数据
            String s = monitorApi.getOsMonitorList(platformId, "os", 5);
            cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(s);

            // 处理内存使用率数据
            JSONArray memArray = jsonObject.getJSONArray("MEM");
            List<RowRenderData> memRows = new ArrayList<>();
            memRows.add(header1);
            if (CollectionUtil.isNotEmpty(memArray)) {
                for (Object m : memArray) {
                    cn.hutool.json.JSONObject item = (cn.hutool.json.JSONObject) m;
                    String[] rowData = new String[]{
                            item.getStr("name"),
                            item.getStr("value") + "%"
                    };
                    memRows.add(Rows.of(rowData).center().create());
                }
            }
            TableRenderData memTable = Tables.create(memRows.toArray(new RowRenderData[0]));
            paramMap.put("MEM", memTable);

            // 处理CPU使用率数据
            JSONArray cpuArray = jsonObject.getJSONArray("CPU");
            List<RowRenderData> cpuRows = new ArrayList<>();
            cpuRows.add(header1);
            if (CollectionUtil.isNotEmpty(cpuArray)) {
                for (Object c : cpuArray) {
                    cn.hutool.json.JSONObject item = (cn.hutool.json.JSONObject) c;
                    String[] rowData = new String[]{
                            item.getStr("name"),
                            item.getStr("value") + "%"
                    };
                    cpuRows.add(Rows.of(rowData).center().create());
                }
            }
            TableRenderData cpuTable = Tables.create(cpuRows.toArray(new RowRenderData[0]));
            paramMap.put("CPU", cpuTable);

            // 处理CPU使用率数据
            JSONArray diskArray = jsonObject.getJSONArray("DISK");
            List<RowRenderData> diskRows = new ArrayList<>();
            diskRows.add(header1);
            if (CollectionUtil.isNotEmpty(diskArray)) {
                for (Object c : diskArray) {
                    cn.hutool.json.JSONObject item = (cn.hutool.json.JSONObject) c;
                    String[] rowData = new String[]{
                            item.getStr("name"),
                            item.getStr("value") + "%"
                    };
                    diskRows.add(Rows.of(rowData).center().create());
                }
            }
            TableRenderData diskTable = Tables.create(diskRows.toArray(new RowRenderData[0]));
            paramMap.put("DISK", diskTable);


            // 处理CPU使用率数据
            JSONArray timeArray = jsonObject.getJSONArray("TIME");
            List<RowRenderData> timeRows = new ArrayList<>();
            timeRows.add(header1);
            if (CollectionUtil.isNotEmpty(timeArray)) {
                for (Object c : timeArray) {
                    cn.hutool.json.JSONObject item = (cn.hutool.json.JSONObject) c;
                    String[] rowData = new String[]{
                            item.getStr("name"),
                            item.getStr("value") + "ms"
                    };
                    timeRows.add(Rows.of(rowData).center().create());
                }
            }
            TableRenderData timeTable = Tables.create(timeRows.toArray(new RowRenderData[0]));
            paramMap.put("TIME", timeTable);

        } else {
            Map<String, Object> mapTop5 = tenantScreenApi.getScreenUsageTop(user.getId(), platformId, "1d", 0, user.getTenantId());

            log.info("TOP5之JSONStr：" + mapTop5);
            for (String key : mapTop5.keySet()) {
                JSONArray arrList = JSONUtil.parseArray(JSONUtil.toJsonPrettyStr(mapTop5.get(key)));
                List<RowRenderData> rows = new ArrayList<>();
                rows.add(header1);
                if (CollectionUtil.isEmpty(arrList)) {
                    // 将行数据转换为表格数据
                    TableRenderData table = Tables.create(rows.toArray(new RowRenderData[0]));
                    // table 只有表头数据
                    paramMap.put(key, table);
                } else {
                    for (Object m : arrList) {
                        JSONObject obj = JSONObject.parseObject(JSONUtil.toJsonStr(m));
                        String name = obj.getString("name");
                        double value = obj.getDoubleValue("value");
                        RowRenderData dataRow = Rows.of(name, String.format("%.2f%%", value)).create();
                        rows.add(dataRow);
                    }
                    // 将行数据转换为表格数据
                    TableRenderData table = Tables.create(rows.toArray(new RowRenderData[0]));
                    paramMap.put(key, table);
                }
            }
        }

        String s = monitorApi.getOsMonitorList(platformId, "os", 5);
        cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(s);


        List<Map<String, Object>> kd = zjreportExportService.getAlarmTrigger("day", userId, platformId, false);
        List<RowRenderData> kdrows = new ArrayList<>();


        List<String> alarm = Arrays.asList("告警规则", "资源名称", "告警级别", "告警次数");
        List<String> alarmCellList1 = new ArrayList<>(alarm);
        String[] alarmCell1 = alarmCellList1.toArray(new String[alarmCellList1.size()]);
        RowRenderData alarm1 = Rows.of(alarmCell1).center().bgColor("DFDED9").create();
        kdrows.add(alarm1); // 首先添加标题行
        //无数据只有表头数据 tableList 无值
        if (CollectionUtil.isEmpty(kd)) {
            // 将行数据转换为表格数据
            TableRenderData table = Tables.create(kdrows.toArray(new RowRenderData[0]));
            // table 只有表头数据
            paramMap.put("check_table4", table);
        } else {
            for (Map m : kd) {
                String content = (String) m.get("content");
                String monitor_name = (String) m.get("monitor_name");
                int priorityValue = Convert.toInt(m.get("priority"));
                // 转换优先级值到对应的文字描述
                String priority = "";
                switch (priorityValue) {
                    case 0:
                        priority = "严重";
                        break;
                    case 1:
                        priority = "警告";
                        break;
                    case 2:
                        priority = "提示";
                        break;
                    default:
                        priority = "未知"; // 处理未知或者其他值的情况
                }
                String times = Convert.toStr(m.get("times"));
                RowRenderData dataRow = Rows.of(content, monitor_name, priority, times).create();
                kdrows.add(dataRow);
            }
            // 将行数据转换为表格数据
            TableRenderData table = Tables.create(kdrows.toArray(new RowRenderData[0]));
            paramMap.put("check_table4", table);
        }

        Map<String, Object> maps = zjreportExportService.platformTrendsNow("week", userId, platformId);
        List<Map> alertLine = (List<Map>) maps.get("alert");

        // 创建一个用于保存所有系列的列表
        ChartMultiSeriesRenderData chartMultiSeriesRenderData = new ChartMultiSeriesRenderData();
        chartMultiSeriesRenderData.setChartTitle("平台使用情况统计");
        List<SeriesRenderData> seriesDatass = new ArrayList<>(); // 创建存储多个系列数据的列表
        List<String> categoriesDate = null; // 日期，
        for (Map map1 : alertLine) {
            List<String> dates = (List<String>) map1.get("date");
            categoriesDate = dates;
            String pname = (String) map1.get("platformName"); // 系列名称将使用平台名称

            // 将count列表转换为Double数组，以满足POI-TL组件的要求
            Double[] countValues = (Double[]) ((List) map1.get("count")).stream()
                    .map(obj -> Double.valueOf(obj.toString())).toArray(Double[]::new);

            // 为每个平台创建一个系列并添加到系列列表中
            seriesDatass.add(new SeriesRenderData(pname, countValues));
        }
        // 设置图表的类别（X轴）和系列（Y轴）
        chartMultiSeriesRenderData.setCategories(categoriesDate.toArray(new String[0]));
        chartMultiSeriesRenderData.setSeriesDatas(seriesDatass);

        paramMap.put("lineCharts", chartMultiSeriesRenderData);
        if (ObjectUtil.isEmpty(response)) {
            return createAndDownloadWord(paramMap, "day", state);
        } else {
            createAndDownloadWord(paramMap, response, "day", state);
            return null;
        }
    }

    public ByteArrayOutputStream exportWeekOrMonthReport(String date, Long userId, Long platformId, HttpServletResponse response) {
        AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(userId).getData();
        Map<String, Object> paramMap = new HashMap<>();
        String fromDate = "";
        String toDate = "";
        LocalDate currentDate = LocalDate.now();

        if (date.equals("week")) {
            LocalDate lastWeekStart = currentDate.with(DayOfWeek.MONDAY).minusWeeks(1);
            LocalDate lastWeekEnd = lastWeekStart.plusDays(6);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            fromDate = lastWeekStart.format(formatter);
            toDate = lastWeekEnd.format(formatter);
        } else if (date.equals("month")) {
            LocalDate firstDayOfLastMonth = currentDate.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
            LocalDate lastDayOfLastMonth = currentDate.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            fromDate = firstDayOfLastMonth.format(formatter);
            toDate = lastDayOfLastMonth.format(formatter);
        }
        String[] duration = new String[]{"报告周期", fromDate + " — " + toDate};
        RowRenderData durationRow = Rows.of(duration).center().create();
        String[] createTime = new String[]{"报告生成时间", DateUtil.format(DateUtil.date(), "yyyy-MM-dd HH:mm:ss")};
        RowRenderData dateRow = Rows.of(createTime).center().create();
        Tables.TableBuilder tableBuilder = Tables.of().width(14.63f, new double[]{3.8, 10.83});
        tableBuilder.addRow(durationRow);
        tableBuilder.addRow(dateRow);
        paramMap.put("durationInfo", tableBuilder.create());
        List<Map> mapList = platformconfigApi.getPlatformListByUserId(String.valueOf(userId)).getData();
        if (CollectionUtil.isEmpty(mapList)) {
            throw exception(new ErrorCode(2001000001, "暂无平台数据"));
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        // 告警饼图
        Map<String, Object> map = zjreportMapper.getAlarmStatistics(fromDate, mapList, date, platformId);
        map.put("timeEnd", toDate);
        map.put("timeStart", fromDate);

        ChartSingleSeriesRenderData pie = Charts.ofSingleSeries("告警统计", new String[]{"未处理", "已处理"}).series("告警数", new Integer[]{Convert.toInt(map.get("unsolved")), Convert.toInt(map.get("solved"))}).create();
        paramMap.put("pieChart", pie);


        //  工单饼图
        Map<String, Object> mapst = zjreportMapper.getProcessStatistics(fromDate, adminUserRespDTO.getTenantId(), date, platformId);
        ChartSingleSeriesRenderData ststat = Charts.ofSingleSeries("工单统计", new String[]{"已处理", "未处理", "回退"}).series("工单数", new Integer[]{Convert.toInt(mapst.get("finished")), Convert.toInt(mapst.get("unfinished")), Convert.toInt(mapst.get("reject"))}).create();
        paramMap.put("pieChart2", ststat);

        // 资产告警趋势
        Map<String, Object> assertTrend = zjreportExportService.getGrowthTrendByType(date, userId, platformId);
        List<Map> assertTrendLine = (List<Map>) assertTrend.get("alertValueList");
        // 创建一个用于保存所有系列的列表
        ChartMultiSeriesRenderData assertTrendLineRenderData = new ChartMultiSeriesRenderData();
        assertTrendLineRenderData.setChartTitle("资产告警增长趋势");
        List<SeriesRenderData> assertDatass = new ArrayList<>(); // 创建存储多个系列数据的列表
        List<String> assertCategoriesDate = null; // 日期，
        for (Map map1 : assertTrendLine) {
            List<String> dates = (List<String>) map1.get("date");
            assertCategoriesDate = dates;
            String app = (String) map1.get("app"); // 系列名称将使用平台名称

            // 将count列表转换为Double数组，以满足POI-TL组件的要求
            Double[] countValues = (Double[]) ((List) map1.get("total")).stream()
                    .map(obj -> Double.valueOf(obj.toString())).toArray(Double[]::new);

            // 为每个平台创建一个系列并添加到系列列表中
            assertDatass.add(new SeriesRenderData(app, countValues));
        }

        // 设置图表的类别（X轴）和系列（Y轴）
        assertTrendLineRenderData.setCategories(assertCategoriesDate.toArray(new String[0]));
        assertTrendLineRenderData.setSeriesDatas(assertDatass);

        paramMap.put("lalertValueList", assertTrendLineRenderData);


        // 资产工单增长趋势
        List<Map> processTrendLine = (List<Map>) assertTrend.get("processValueList");
        // 创建一个用于保存所有系列的列表
        ChartMultiSeriesRenderData processMultiSeriesRenderData = new ChartMultiSeriesRenderData();
        processMultiSeriesRenderData.setChartTitle("资产工单增长趋势");
        List<SeriesRenderData> processDatass = new ArrayList<>(); // 创建存储多个系列数据的列表
        List<String> processDate = null; // 日期，
        for (Map map1 : processTrendLine) {
            List<String> dates = (List<String>) map1.get("date");
            processDate = dates;
            String pname = (String) map1.get("app"); // 系列名称将使用平台名称

            // 将count列表转换为Double数组，以满足POI-TL组件的要求
            Double[] countValues = (Double[]) ((List) map1.get("total")).stream()
                    .map(obj -> Double.valueOf(obj.toString())).toArray(Double[]::new);

            // 为每个平台创建一个系列并添加到系列列表中
            processDatass.add(new SeriesRenderData(pname, countValues));
        }

        // 设置图表的类别（X轴）和系列（Y轴）
        processMultiSeriesRenderData.setCategories(processDate.toArray(new String[0]));
        processMultiSeriesRenderData.setSeriesDatas(processDatass);

        paramMap.put("processValueList", processMultiSeriesRenderData);

        //平台告警增长趋势
        Map<String, Object> maps = zjreportExportService.platformTrends(date, userId, platformId);
        List<Map> alertLine = (List<Map>) maps.get("alert");
        // 创建一个用于保存所有系列的列表
        ChartMultiSeriesRenderData chartMultiSeriesRenderData = new ChartMultiSeriesRenderData();
        chartMultiSeriesRenderData.setChartTitle("平台告警增长趋势");
        List<SeriesRenderData> seriesDatass = new ArrayList<>(); // 创建存储多个系列数据的列表
        List<String> categoriesDate = null; // 日期，
        for (Map map1 : alertLine) {
            List<String> dates = (List<String>) map1.get("date");
            categoriesDate = dates;
            List<Integer> counts = (List<Integer>) map1.get("count");
            String pname = (String) map1.get("platformName"); // 系列名称将使用平台名称

            // 将count列表转换为Double数组，以满足POI-TL组件的要求
            Double[] countValues = (Double[]) ((List) map1.get("count")).stream()
                    .map(obj -> Double.valueOf(obj.toString())).toArray(Double[]::new);
            // 为每个平台创建一个系列并添加到系列列表中
            seriesDatass.add(new SeriesRenderData(pname, countValues));
        }
        // 设置图表的类别（X轴）和系列（Y轴）
        chartMultiSeriesRenderData.setCategories(categoriesDate.toArray(new String[0]));
        chartMultiSeriesRenderData.setSeriesDatas(seriesDatass);

        paramMap.put("alert", chartMultiSeriesRenderData);

        List<Map> bpmLine = (List<Map>) maps.get("bpm");
        // 创建一个用于保存所有系列的列表
        ChartMultiSeriesRenderData bpmchartMultiSeriesRenderData = new ChartMultiSeriesRenderData();
        bpmchartMultiSeriesRenderData.setChartTitle("平台工单增长趋势");
        List<SeriesRenderData> bpmseriesDatass = new ArrayList<>(); // 创建存储多个系列数据的列表
        List<String> bpmcategoriesDate = null; // 日期，
        for (Map map1 : bpmLine) {
            List<String> dates = (List<String>) map1.get("date");
            bpmcategoriesDate = dates;
            // 获取count列表，可能是Integer也可能是Long类型
            List<?> rawCounts = (List<?>) map1.get("count");

            String pname = (String) map1.get("platformName"); // 系列名称将使用平台名称

            // 将rawCounts列表转换为Double数组，以满足POI-TL组件的要求
            Double[] countValues = rawCounts.stream().map(count -> {
                if (count instanceof Integer) {
                    return Double.valueOf((Integer) count);
                } else if (count instanceof Long) {
                    return Double.valueOf((Long) count);
                } else {
                    throw new IllegalArgumentException("Unsupported count type: " + count.getClass().getName());
                }
            }).toArray(Double[]::new);

            // 为每个平台创建一个系列并添加到系列列表中
            bpmseriesDatass.add(new SeriesRenderData(pname, countValues));
        }
        // 设置图表的类别（X轴）和系列（Y轴）
        bpmchartMultiSeriesRenderData.setCategories(bpmcategoriesDate.toArray(new String[0]));
        bpmchartMultiSeriesRenderData.setSeriesDatas(bpmseriesDatass);
        paramMap.put("bpm", bpmchartMultiSeriesRenderData);

        Map<String, List<ZjReportProcessRespVO>> list = zjreportExportService.processtopYest(date, userId, platformId);
        List<ZjReportProcessRespVO> zjReportProcessRespVOS = (List<ZjReportProcessRespVO>) list.get("alert");

        List<String> xnlbTemp1 = Arrays.asList("资源名称", "平台名称", "告警次数");
        List<String> headerCellList1 = new ArrayList<>(xnlbTemp1);
        String[] headerCell1 = headerCellList1.toArray(new String[headerCellList1.size()]);
        RowRenderData header1 = Rows.of(headerCell1).center().bgColor("DFDED9").create();

        List<RowRenderData> hostCpurows = new ArrayList<>();
        hostCpurows.add(header1); // 首先添加标题行
        //无数据只有表头数据 tableList 无值
        if (CollectionUtil.isEmpty(zjReportProcessRespVOS)) {
            // 将行数据转换为表格数据
            TableRenderData table = Tables.create(hostCpurows.toArray(new RowRenderData[0]));
            // table 只有表头数据
            paramMap.put("alerttop10", table);
        } else {
            for (ZjReportProcessRespVO zjReportProcessRespVO : zjReportProcessRespVOS) {
                String name = zjReportProcessRespVO.getName();
                String pname = zjReportProcessRespVO.getPlatformName();
                String value = StringUtil.toString(zjReportProcessRespVO.getAlertCount());
                RowRenderData dataRow = Rows.of(name, pname, value).create();
                hostCpurows.add(dataRow);
            }
            // 将行数据转换为表格数据
            TableRenderData table = Tables.create(hostCpurows.toArray(new RowRenderData[0]));
            paramMap.put("alerttop10", table);
        }

        List<ZjReportProcessRespVO> bpmProcessRespVOS = (List<ZjReportProcessRespVO>) list.get("bpm");
        List<String> bpmTemp = Arrays.asList("资源名称", "平台名称", "工单次数");
        List<String> bpmCellList = new ArrayList<>(bpmTemp);
        String[] bpmCell = bpmCellList.toArray(new String[bpmCellList.size()]);
        RowRenderData bpmHeader = Rows.of(bpmCell).center().bgColor("DFDED9").create();

        List<RowRenderData> bpmrows = new ArrayList<>();
        bpmrows.add(bpmHeader); // 首先添加标题行
        //无数据只有表头数据 tableList 无值
        if (CollectionUtil.isEmpty(bpmProcessRespVOS)) {
            // 将行数据转换为表格数据
            TableRenderData table = Tables.create(bpmrows.toArray(new RowRenderData[0]));
            // table 只有表头数据
            paramMap.put("bpmtop10", table);
        } else {
            for (ZjReportProcessRespVO zjReportProcessRespVO : bpmProcessRespVOS) {
                String name = zjReportProcessRespVO.getName();
                String pname = zjReportProcessRespVO.getPlatformName();
                String value = StringUtil.toString(zjReportProcessRespVO.getTicketsCount());
                RowRenderData dataRow = Rows.of(name, pname, value).create();
                bpmrows.add(dataRow);
            }
            // 将行数据转换为表格数据
            TableRenderData table = Tables.create(bpmrows.toArray(new RowRenderData[0]));
            paramMap.put("bpmtop10", table);
        }

        // 告警top10
        List<Map<String, Object>> kd = zjreportExportService.getAlarmTriggerWeek(date, userId, platformId, true);
        List<RowRenderData> kdrows = new ArrayList<>();


        List<String> alarm = Arrays.asList("告警规则", "资源名称", "告警级别", "告警次数");
        List<String> alarmCellList1 = new ArrayList<>(alarm);
        String[] alarmCell1 = alarmCellList1.toArray(new String[alarmCellList1.size()]);
        RowRenderData alarm1 = Rows.of(alarmCell1).center().bgColor("DFDED9").create();
        kdrows.add(alarm1); // 首先添加标题行
        //无数据只有表头数据 tableList 无值
        if (CollectionUtil.isEmpty(kd)) {
            // 将行数据转换为表格数据
            TableRenderData table = Tables.create(kdrows.toArray(new RowRenderData[0]));
            // table 只有表头数据
            paramMap.put("alarmTrigger", table);
        } else {
            for (Map m : kd) {
                String content = (String) m.get("content");
                String monitor_name = (String) m.get("monitor_name");
                int priorityValue = Convert.toInt(m.get("priority"));
                // 转换优先级值到对应的文字描述
                String priority = "";
                switch (priorityValue) {
                    case 0:
                        priority = "严重";
                        break;
                    case 1:
                        priority = "警告";
                        break;
                    case 2:
                        priority = "提示";
                        break;
                    default:
                        priority = "未知"; // 处理未知或者其他值的情况
                }
                String times = Convert.toStr(m.get("times"));
                RowRenderData dataRow = Rows.of(content, monitor_name, priority, times).create();
                kdrows.add(dataRow);
            }
            // 将行数据转换为表格数据
            TableRenderData table = Tables.create(kdrows.toArray(new RowRenderData[0]));
            paramMap.put("alarmTrigger", table);
        }
        if (ObjectUtil.isEmpty(response)) {
            return createAndDownloadWord(paramMap, date, null);
        } else {
            createAndDownloadWord(paramMap, response, date, null);
            return null;
        }
    }

    public ByteArrayOutputStream exportWeekOrMonthReportNow(String date, Long userId, Long platformId,
                                                            HttpServletResponse response) {
        AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(userId).getData();
        Map<String, Object> paramMap = new HashMap<>();
        String fromDate = "";
        String toDate = "";
        LocalDate currentDate = LocalDate.now();

        if (date.equals("week")) {
            LocalDate lastWeekStart = currentDate.with(DayOfWeek.MONDAY);
            LocalDate lastWeekEnd = lastWeekStart.plusDays(6);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            fromDate = lastWeekStart.format(formatter);
            toDate = lastWeekEnd.format(formatter);
        } else if (date.equals("month")) {
            LocalDate firstDayOfLastMonth = currentDate.with(TemporalAdjusters.firstDayOfMonth());
            LocalDate lastMonth = currentDate.with(TemporalAdjusters.lastDayOfMonth());
            LocalDate lastDayOfLastMonth = lastMonth.with(TemporalAdjusters.lastDayOfMonth());
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            fromDate = firstDayOfLastMonth.format(formatter);
            toDate = lastDayOfLastMonth.format(formatter);
        }
        String[] duration = new String[]{"报告周期", fromDate + " — " + toDate};
        RowRenderData durationRow = Rows.of(duration).center().create();
        String[] createTime = new String[]{"报告生成时间", DateUtil.format(DateUtil.date(), "yyyy-MM-dd HH:mm:ss")};
        RowRenderData dateRow = Rows.of(createTime).center().create();
        Tables.TableBuilder tableBuilder = Tables.of().width(14.63f, new double[]{3.8, 10.83});
        tableBuilder.addRow(durationRow);
        tableBuilder.addRow(dateRow);
        paramMap.put("durationInfo", tableBuilder.create());
        List<Map> mapList = platformconfigApi.getPlatformListByUserId(String.valueOf(userId)).getData();
        if (CollectionUtil.isEmpty(mapList)) {
            throw exception(new ErrorCode(2001000001, "暂无平台数据"));
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        // 告警饼图
        Map<String, Object> map = zjreportMapper.getAlarmStatistics(fromDate, mapList, date, platformId);
        map.put("timeEnd", toDate);
        map.put("timeStart", fromDate);

        ChartSingleSeriesRenderData pie = Charts.ofSingleSeries("告警统计", new String[]{"未处理", "已处理"}).series("告警数", new Integer[]{Convert.toInt(map.get("unsolved")), Convert.toInt(map.get("solved"))}).create();
        paramMap.put("pieChart", pie);


        //  工单饼图
        Map<String, Object> mapst = zjreportMapper.getProcessStatistics(fromDate, adminUserRespDTO.getTenantId(), date, platformId);
        ChartSingleSeriesRenderData ststat = Charts.ofSingleSeries("工单统计", new String[]{"已处理", "未处理", "回退"}).series("工单数", new Integer[]{Convert.toInt(mapst.get("finished")), Convert.toInt(mapst.get("unfinished")), Convert.toInt(mapst.get("reject"))}).create();
        paramMap.put("pieChart2", ststat);

        // 资产告警趋势
        Map<String, Object> assertTrend = zjreportExportService.getGrowthTrendByTypeNow(date, userId, platformId);
        List<Map> assertTrendLine = (List<Map>) assertTrend.get("alertValueList");
        // 创建一个用于保存所有系列的列表
        ChartMultiSeriesRenderData assertTrendLineRenderData = new ChartMultiSeriesRenderData();
        assertTrendLineRenderData.setChartTitle("资产告警增长趋势");
        List<SeriesRenderData> assertDatass = new ArrayList<>(); // 创建存储多个系列数据的列表
        List<String> assertCategoriesDate = null; // 日期，
        for (Map map1 : assertTrendLine) {
            List<String> dates = (List<String>) map1.get("date");
            assertCategoriesDate = dates;
            String app = (String) map1.get("app"); // 系列名称将使用平台名称

            // 将count列表转换为Double数组，以满足POI-TL组件的要求
            Double[] countValues = (Double[]) ((List) map1.get("total")).stream()
                    .map(obj -> Double.valueOf(obj.toString())).toArray(Double[]::new);

            // 为每个平台创建一个系列并添加到系列列表中
            assertDatass.add(new SeriesRenderData(app, countValues));
        }

        // 设置图表的类别（X轴）和系列（Y轴）
        assertTrendLineRenderData.setCategories(assertCategoriesDate.toArray(new String[0]));
        assertTrendLineRenderData.setSeriesDatas(assertDatass);

        paramMap.put("lalertValueList", assertTrendLineRenderData);


        // 资产工单增长趋势
        List<Map> processTrendLine = (List<Map>) assertTrend.get("processValueList");
        // 创建一个用于保存所有系列的列表
        ChartMultiSeriesRenderData processMultiSeriesRenderData = new ChartMultiSeriesRenderData();
        processMultiSeriesRenderData.setChartTitle("资产工单增长趋势");
        List<SeriesRenderData> processDatass = new ArrayList<>(); // 创建存储多个系列数据的列表
        List<String> processDate = null; // 日期，
        for (Map map1 : processTrendLine) {
            List<String> dates = (List<String>) map1.get("date");
            processDate = dates;
            String pname = (String) map1.get("app"); // 系列名称将使用平台名称

            // 将count列表转换为Double数组，以满足POI-TL组件的要求
            Double[] countValues = (Double[]) ((List) map1.get("total")).stream()
                    .map(obj -> Double.valueOf(obj.toString())).toArray(Double[]::new);

            // 为每个平台创建一个系列并添加到系列列表中
            processDatass.add(new SeriesRenderData(pname, countValues));
        }

        // 设置图表的类别（X轴）和系列（Y轴）
        processMultiSeriesRenderData.setCategories(processDate.toArray(new String[0]));
        processMultiSeriesRenderData.setSeriesDatas(processDatass);

        paramMap.put("processValueList", processMultiSeriesRenderData);

        //平台告警增长趋势
        Map<String, Object> maps = zjreportExportService.platformTrendsNow(date, userId, platformId);
        List<Map> alertLine = (List<Map>) maps.get("alert");
        // 创建一个用于保存所有系列的列表
        ChartMultiSeriesRenderData chartMultiSeriesRenderData = new ChartMultiSeriesRenderData();
        chartMultiSeriesRenderData.setChartTitle("平台告警增长趋势");
        List<SeriesRenderData> seriesDatass = new ArrayList<>(); // 创建存储多个系列数据的列表
        List<String> categoriesDate = null; // 日期，
        for (Map map1 : alertLine) {
            List<String> dates = (List<String>) map1.get("date");
            categoriesDate = dates;
            List<Integer> counts = (List<Integer>) map1.get("count");
            String pname = (String) map1.get("platformName"); // 系列名称将使用平台名称

            // 将count列表转换为Double数组，以满足POI-TL组件的要求
            Double[] countValues = (Double[]) ((List) map1.get("count")).stream()
                    .map(obj -> Double.valueOf(obj.toString())).toArray(Double[]::new);
            // 为每个平台创建一个系列并添加到系列列表中
            seriesDatass.add(new SeriesRenderData(pname, countValues));
        }
        // 设置图表的类别（X轴）和系列（Y轴）
        chartMultiSeriesRenderData.setCategories(categoriesDate.toArray(new String[0]));
        chartMultiSeriesRenderData.setSeriesDatas(seriesDatass);

        paramMap.put("alert", chartMultiSeriesRenderData);

        List<Map> bpmLine = (List<Map>) maps.get("bpm");
        // 创建一个用于保存所有系列的列表
        ChartMultiSeriesRenderData bpmchartMultiSeriesRenderData = new ChartMultiSeriesRenderData();
        bpmchartMultiSeriesRenderData.setChartTitle("平台工单增长趋势");
        List<SeriesRenderData> bpmseriesDatass = new ArrayList<>(); // 创建存储多个系列数据的列表
        List<String> bpmcategoriesDate = null; // 日期，
        for (Map map1 : bpmLine) {
            List<String> dates = (List<String>) map1.get("date");
            bpmcategoriesDate = dates;
            // 获取count列表，可能是Integer也可能是Long类型
            List<?> rawCounts = (List<?>) map1.get("count");

            String pname = (String) map1.get("platformName"); // 系列名称将使用平台名称

            // 将rawCounts列表转换为Double数组，以满足POI-TL组件的要求
            Double[] countValues = rawCounts.stream().map(count -> {
                if (count instanceof Integer) {
                    return Double.valueOf((Integer) count);
                } else if (count instanceof Long) {
                    return Double.valueOf((Long) count);
                } else {
                    throw new IllegalArgumentException("Unsupported count type: " + count.getClass().getName());
                }
            }).toArray(Double[]::new);

            // 为每个平台创建一个系列并添加到系列列表中
            bpmseriesDatass.add(new SeriesRenderData(pname, countValues));
        }
        // 设置图表的类别（X轴）和系列（Y轴）
        bpmchartMultiSeriesRenderData.setCategories(bpmcategoriesDate.toArray(new String[0]));
        bpmchartMultiSeriesRenderData.setSeriesDatas(bpmseriesDatass);
        paramMap.put("bpm", bpmchartMultiSeriesRenderData);

        Map<String, List<ZjReportProcessRespVO>> list = zjreportExportService.processtop(date, userId, platformId);
        List<ZjReportProcessRespVO> zjReportProcessRespVOS = (List<ZjReportProcessRespVO>) list.get("alert");

        List<String> xnlbTemp1 = Arrays.asList("资源名称", "平台名称", "告警次数");
        List<String> headerCellList1 = new ArrayList<>(xnlbTemp1);
        String[] headerCell1 = headerCellList1.toArray(new String[headerCellList1.size()]);
        RowRenderData header1 = Rows.of(headerCell1).center().bgColor("DFDED9").create();

        List<RowRenderData> hostCpurows = new ArrayList<>();
        hostCpurows.add(header1); // 首先添加标题行
        //无数据只有表头数据 tableList 无值
        if (CollectionUtil.isEmpty(zjReportProcessRespVOS)) {
            // 将行数据转换为表格数据
            TableRenderData table = Tables.create(hostCpurows.toArray(new RowRenderData[0]));
            // table 只有表头数据
            paramMap.put("alerttop10", table);
        } else {
            for (ZjReportProcessRespVO zjReportProcessRespVO : zjReportProcessRespVOS) {
                String name = zjReportProcessRespVO.getName();
                String pname = zjReportProcessRespVO.getPlatformName();
                String value = StringUtil.toString(zjReportProcessRespVO.getAlertCount());
                RowRenderData dataRow = Rows.of(name, pname, value).create();
                hostCpurows.add(dataRow);
            }
            // 将行数据转换为表格数据
            TableRenderData table = Tables.create(hostCpurows.toArray(new RowRenderData[0]));
            paramMap.put("alerttop10", table);
        }

        List<ZjReportProcessRespVO> bpmProcessRespVOS = (List<ZjReportProcessRespVO>) list.get("bpm");
        List<String> bpmTemp = Arrays.asList("资源名称", "平台名称", "工单次数");
        List<String> bpmCellList = new ArrayList<>(bpmTemp);
        String[] bpmCell = bpmCellList.toArray(new String[bpmCellList.size()]);
        RowRenderData bpmHeader = Rows.of(bpmCell).center().bgColor("DFDED9").create();

        List<RowRenderData> bpmrows = new ArrayList<>();
        bpmrows.add(bpmHeader); // 首先添加标题行
        //无数据只有表头数据 tableList 无值
        if (CollectionUtil.isEmpty(bpmProcessRespVOS)) {
            // 将行数据转换为表格数据
            TableRenderData table = Tables.create(bpmrows.toArray(new RowRenderData[0]));
            // table 只有表头数据
            paramMap.put("bpmtop10", table);
        } else {
            for (ZjReportProcessRespVO zjReportProcessRespVO : bpmProcessRespVOS) {
                String name = zjReportProcessRespVO.getName();
                String pname = zjReportProcessRespVO.getPlatformName();
                String value = StringUtil.toString(zjReportProcessRespVO.getTicketsCount());
                RowRenderData dataRow = Rows.of(name, pname, value).create();
                bpmrows.add(dataRow);
            }
            // 将行数据转换为表格数据
            TableRenderData table = Tables.create(bpmrows.toArray(new RowRenderData[0]));
            paramMap.put("bpmtop10", table);
        }

        // 告警top10
        List<Map<String, Object>> kd = zjreportExportService.getAlarmTrigger(date, userId, platformId, false);
        List<RowRenderData> kdrows = new ArrayList<>();


        List<String> alarm = Arrays.asList("告警规则", "资源名称", "告警级别", "告警次数");
        List<String> alarmCellList1 = new ArrayList<>(alarm);
        String[] alarmCell1 = alarmCellList1.toArray(new String[alarmCellList1.size()]);
        RowRenderData alarm1 = Rows.of(alarmCell1).center().bgColor("DFDED9").create();
        kdrows.add(alarm1); // 首先添加标题行
        //无数据只有表头数据 tableList 无值
        if (CollectionUtil.isEmpty(kd)) {
            // 将行数据转换为表格数据
            TableRenderData table = Tables.create(kdrows.toArray(new RowRenderData[0]));
            // table 只有表头数据
            paramMap.put("alarmTrigger", table);
        } else {
            for (Map m : kd) {
                String content = (String) m.get("content");
                String monitor_name = (String) m.get("monitor_name");
                int priorityValue = Convert.toInt(m.get("priority"));
                // 转换优先级值到对应的文字描述
                String priority = "";
                switch (priorityValue) {
                    case 0:
                        priority = "严重";
                        break;
                    case 1:
                        priority = "警告";
                        break;
                    case 2:
                        priority = "提示";
                        break;
                    default:
                        priority = "未知"; // 处理未知或者其他值的情况
                }
                String times = Convert.toStr(m.get("times"));
                RowRenderData dataRow = Rows.of(content, monitor_name, priority, times).create();
                kdrows.add(dataRow);
            }
            // 将行数据转换为表格数据
            TableRenderData table = Tables.create(kdrows.toArray(new RowRenderData[0]));
            paramMap.put("alarmTrigger", table);
        }
        if (ObjectUtil.isEmpty(response)) {
            return createAndDownloadWord(paramMap, date, null);
        } else {
            createAndDownloadWord(paramMap, response, date, null);
            return null;
        }
    }

    public static void main(String[] args) {
        String fromDate = "";
        String toDate = "";
        String date = "month";
        LocalDate currentDate = LocalDate.now();
        if (date.equals("week")) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//            LocalDate lastWeekStart = currentDate.with(DayOfWeek.MONDAY);
//            LocalDate lastWeekEnd = lastWeekStart.plusDays(6);

            LocalDate lastWeekStart = currentDate.with(DayOfWeek.MONDAY).minusWeeks(1);
            LocalDate lastWeekEnd = lastWeekStart.plusDays(6);
            fromDate = lastWeekStart.format(formatter);
            toDate = lastWeekEnd.format(formatter);
        } else if (date.equals("month")) {
            LocalDate firstDayOfLastMonth = currentDate.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
            LocalDate lastDayOfLastMonth = currentDate.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
//            LocalDate firstDayOfLastMonth = currentDate.with(TemporalAdjusters.firstDayOfMonth());
//            LocalDate lastMonth = currentDate.with(TemporalAdjusters.lastDayOfMonth());
//            LocalDate lastDayOfLastMonth = lastMonth.with(TemporalAdjusters.lastDayOfMonth());
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            fromDate = firstDayOfLastMonth.format(formatter);
            toDate = lastDayOfLastMonth.format(formatter);
        }

        System.out.println(fromDate + "====" + toDate);
    }

    @Override
    public String getWeChatUserOpenId(Long userId) {
        return zjreportMapper.getWeChatUserOpenId(userId);
    }

    @Override
    @TenantIgnore
    public Long getAlertCountByUserPlatformIds(Long userId) {
        List<Map> mapList = platformconfigApi.getPlatformListByUserId(String.valueOf(userId)).getCheckedData();
        return zjreportMapper.getAlertCountByUserPlatformIds(mapList);
    }

    private static ByteArrayOutputStream createAndDownloadWord(Map<String, Object> paramMap, String date, Long state) {
        log.info("生成电子协查函参数paramMap = {}", paramMap);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            // 创建一个临时的输出流
            String path = "";
            if (date.equals("day")) {
                path = "file-template" + File.separator + "day.docx";
            } else if (date.equals("week")) {
                path = "file-template" + File.separator + "week.docx";
            } else if (date.equals("month")) {
                path = "file-template" + File.separator + "month.docx";
            }
            // 获取电子协查函模板

            ClassPathResource classPathResource = new ClassPathResource(path);

            try (InputStream inputStream = classPathResource.getInputStream()) {
                if (Objects.isNull(inputStream)) {
                    log.error("获取电子协查函模板失败");
                    return null;
                }

                // 通过协查函模板，开始生成电子协查函
                try (XWPFTemplate template = XWPFTemplate.compile(inputStream).render(paramMap)) {
                    // 将生成的Word文档写入临时输出流
                    template.write(outputStream);
                }

            } catch (Exception e) {
                log.error("创建协查函异常，异常详情：\n{}", e);
            }
        } catch (Exception e) {
            log.error("导出Word文档并下载时发生异常：\n{}", e);
        }
        return outputStream;
    }

    private static void createAndDownloadWord(Map<String, Object> paramMap, HttpServletResponse response, String date, Long state) {
        log.info("生成电子协查函参数paramMap = {}", paramMap);

        try {
            // 创建一个临时的输出流
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            String path = "";
            String dateTime = "";
            if (date.equals("day")) {
                if (state != null & state == 1) {
                    path = "file-template" + File.separator + "day1.docx";
                } else {
                    path = "file-template" + File.separator + "day.docx";
                }
                dateTime = "日报";
            } else if (date.equals("week")) {
                path = "file-template" + File.separator + "week.docx";
                dateTime = "周报";
            } else if (date.equals("month")) {
                path = "file-template" + File.separator + "month.docx";
                dateTime = "月报";
            }
            // 获取电子协查函模板

            ClassPathResource classPathResource = new ClassPathResource(path);

            try (InputStream inputStream = classPathResource.getInputStream()) {
                if (Objects.isNull(inputStream)) {
                    log.error("获取电子协查函模板失败");
                    return;
                }

                // 通过协查函模板，开始生成电子协查函
                try (XWPFTemplate template = XWPFTemplate.compile(inputStream).render(paramMap)) {
                    // 将生成的Word文档写入临时输出流
                    template.write(outputStream);
                }

            } catch (Exception e) {
                log.error("创建协查函异常，异常详情：\n{}", e);
            }
            String d = "过程报告---" + dateTime;

            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(d, "UTF-8") + ".docx");

            // 将生成的Word文档写入响应输出流
            try (OutputStream out = response.getOutputStream()) {
                out.write(outputStream.toByteArray());
                out.flush();
                out.close();
            }
        } catch (Exception e) {
            log.error("导出Word文档并下载时发生异常：\n{}", e);
        }
    }

    public static String[] removeDuplicates(String[] inputArray) {
        if (inputArray == null) {
            return null; // 处理输入为null的情况
        }

        Set<String> set = new LinkedHashSet<>(Arrays.asList(inputArray)); // 使用LinkedHashSet以保持原始顺序
        String[] result = set.toArray(new String[0]);
        return result;
    }

    /**
     * 表格赋值，
     * 设置列宽和合并单元格
     *
     * @param rowRenderDataArray
     * @param cellLength
     * @return
     */
    private static TableRenderData setTableRenderDataAndColWidth(RowRenderData[] rowRenderDataArray, Integer cellLength) {
        //table赋值set方法需要list
        List<RowRenderData> RowRenderDataList = Arrays.asList(rowRenderDataArray);
        //设置列宽：
        double[] colWidthsCm = new double[cellLength];
        for (int i = 0; i < cellLength; i++) {
            // "主机名称", "资源池" 设置为 2
            if (i == 1 || i == 2) {
                colWidthsCm[i] = 2D;
            } else {
                colWidthsCm[i] = 1D;
            }
        }
        //18.450000762939453D A4纸张
        TableRenderData check_table = Tables.ofPercentWidth("100%").center().create();
        check_table.setRows(RowRenderDataList);
        //合并单元格
        MergeCellRule.MergeCellRuleBuilder mergeCellRuleBuilder = mergeCell(cellLength);
        check_table.setMergeRule(mergeCellRuleBuilder.build());

        return check_table;
    }

    /**
     * 表格 合并单元格
     *
     * @param cellLength
     * @return
     */
    private static MergeCellRule.MergeCellRuleBuilder mergeCell(Integer cellLength) {
        /**
         * 设置表格合并规则 从0开始
         * 1.起始行 MergeCellRule.Grid.of(i, j) i: 行 j: 列
         * 2.结束行 MergeCellRule.Grid.of(i, j) i: 行 j: 列
         */
        //合并单元格
        //合并到【备注】前一列
        MergeCellRule.MergeCellRuleBuilder mergeCellRuleBuilder = MergeCellRule.builder();
        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, 0), MergeCellRule.Grid.of(1, 0));//序号合并
        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, 1), MergeCellRule.Grid.of(1, 1));//主机名称合并
        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, 2), MergeCellRule.Grid.of(1, 2));//资源池名称合并
        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, 3), MergeCellRule.Grid.of(0, cellLength - 6));//内容合并 第一行
        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, 5), MergeCellRule.Grid.of(0, cellLength - 2));//验证类型合并 第一行
        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, cellLength - 1), MergeCellRule.Grid.of(1, cellLength - 1));//备注合并
        return mergeCellRuleBuilder;
    }

}
