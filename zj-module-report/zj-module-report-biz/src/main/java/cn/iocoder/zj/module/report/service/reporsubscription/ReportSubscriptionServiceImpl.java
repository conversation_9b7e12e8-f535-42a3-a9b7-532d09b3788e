package cn.iocoder.zj.module.report.service.reporsubscription;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.pojo.UserBindDTO;
import cn.iocoder.zj.framework.common.util.dingtalk.DingTalkUtil;
import cn.iocoder.zj.framework.common.util.dingtalk.QyWxUtils;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.om.api.userbind.UserBindApi;
import cn.iocoder.zj.module.report.SubscriptionEnum;
import cn.iocoder.zj.module.report.api.subscription.dto.ReportSubscriptionDTO;
import cn.iocoder.zj.module.report.controller.admin.reporsubscription.VO.ReportSubscriptionUserVO;
import cn.iocoder.zj.module.report.controller.admin.reporsubscription.VO.SubscriptionRespVO;
import cn.iocoder.zj.module.report.dal.dataobject.reporsubscription.ReportSubscriptionDO;
import cn.iocoder.zj.module.report.dal.mysql.zjreport.ReportSubscriptionMapper;
import cn.iocoder.zj.module.report.timedTaskUtils.CronConverter;
import cn.iocoder.zj.module.report.timedTaskUtils.CronTaskRegistrar;
import cn.iocoder.zj.module.report.timedTaskUtils.SchedulingRunnable;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.UuidUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.convertMap;
import static cn.iocoder.zj.module.system.enums.ErrorCodeConstants.*;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.singleton;
import static cn.iocoder.zj.module.system.enums.ErrorCodeConstants.USER_EMAIL_EXISTS;

@Service
@Validated
@Slf4j
@EnableScheduling
public class ReportSubscriptionServiceImpl implements ReportSubscriptionService, CommandLineRunner {

    @Resource
    ReportSubscriptionMapper reportSubscriptionMapper;
    @Resource
    AdminUserApi adminUserApi;
    @Autowired
    private CronTaskRegistrar cronTaskRegistrar;
    @Autowired
    private UserBindApi userBindApi;
    @Resource
    private RedisTemplate redisTemplate;
    @Autowired
    private RoleApi roleApi;
    @Autowired
    private PermissionApi permissionApi;

    @Override
    public SubscriptionRespVO getByUserId() {
        LoginUser loginUser= SecurityFrameworkUtils.getLoginUser();
        AdminUserRespDTO userRespDTO = adminUserApi.getUser(loginUser.getId()).getData();
        SubscriptionRespVO subscriptionRespVO = reportSubscriptionMapper.getByUserId(userRespDTO.getId());
        //验证订阅通知类型是否存在
        validateReportSubscriptionType();
        //验证用户是否有订阅信息，没有则创建默认订阅信息，状态为关闭;返回补全后的订阅信息
        subscriptionRespVO = validateReportSubscription(subscriptionRespVO,userRespDTO);
        int doNotDisturb = 0;
        for (ReportSubscriptionUserVO item:subscriptionRespVO.getReportSubscription()) {
            if(item.getDoNotDisturb()==1){
                doNotDisturb =1;
            }
            if(StrUtil.isNotEmpty(item.getDingtalkPhone())){
                subscriptionRespVO.setDingtalkPhone(item.getDingtalkPhone());
            }
            if(StrUtil.isNotEmpty(item.getWecomPhone())){
                subscriptionRespVO.setWecomPhone(item.getWecomPhone());
            }
        }

        subscriptionRespVO.setDoNotDisturb(doNotDisturb);
        subscriptionRespVO.setSubscriptionTime(subscriptionRespVO.getReportSubscription().get(0).getSubscriptionTime());
        return subscriptionRespVO;
    }

    private void validateReportSubscriptionType() {
        List<ReportSubscriptionDO> toBeAddedList = new ArrayList<>();
        List<ReportSubscriptionDO> subscriptionTypeList = reportSubscriptionMapper.getAllSubscriptionType();
        SubscriptionEnum[] subscriptionEnums = SubscriptionEnum.values();
        Map<String, List<ReportSubscriptionDO>> groupedByMethodName = subscriptionTypeList.stream()
                .collect(Collectors.groupingBy(ReportSubscriptionDO::getMethodName));
        //补充枚举类中存在的定时任务类型到数据库
        for (SubscriptionEnum item:subscriptionEnums) {
            if(item.getIsTimedTask()==1) {
                if (!ObjectUtil.isEmpty(groupedByMethodName.get(item.getMethodName()))) {
                    Map<String, ReportSubscriptionDO> groupByCycle = convertMap(groupedByMethodName.get(item.getMethodName()), ReportSubscriptionDO::getSubscriptionCycle);
                    for (String cycle : item.getDefaultSubscriptionCycle()) {
                        if (ObjectUtil.isEmpty(groupByCycle.get(cycle))) {
                            String name = "";
                            switch (item.getMethodName()){
                                case "reportSubscription":name = cycle.equals("day") ? "日报" : cycle.equals("week") ? "周报" : "月报";
                                    break;
                                case "alertSubscription":name = "告警通知";
                                    break;
                            }
                            ReportSubscriptionDO toBeAddedItem = new ReportSubscriptionDO();
                            toBeAddedItem.setName(name);
                            toBeAddedItem.setUuid(UuidUtils.generateUuid().replace("-", ""));
                            toBeAddedItem.setBeanName("subscription");
                            toBeAddedItem.setMethodName(item.getMethodName());
                            toBeAddedItem.setSubscriptionCycle(cycle);
                            toBeAddedItem.setIsTimedTask(item.getIsTimedTask());
                            toBeAddedList.add(toBeAddedItem);
                        }
                    }
                } else {
                    for (String cycle : item.getDefaultSubscriptionCycle()) {

                        ReportSubscriptionDO toBeAddedItem = new ReportSubscriptionDO();
                        String name = "";
                        switch (item.getMethodName()){
                            case "reportSubscription":name = cycle.equals("day") ? "日报" : cycle.equals("week") ? "周报" : "月报";
                                break;
                            case "alertSubscription":name = "告警通知";
                                break;
                        }
                        toBeAddedItem.setName(name);
                        toBeAddedItem.setUuid(UuidUtils.generateUuid().replace("-", ""));
                        toBeAddedItem.setBeanName("subscription");
                        toBeAddedItem.setMethodName(item.getMethodName());
                        toBeAddedItem.setSubscriptionCycle(cycle);
                        toBeAddedItem.setIsTimedTask(item.getIsTimedTask());
                        toBeAddedList.add(toBeAddedItem);
                    }
                }
            }else {
//                可能弃用
                if (ObjectUtil.isEmpty(groupedByMethodName.get(item.getMethodName()))) {
                    ReportSubscriptionDO toBeAddedItem = new ReportSubscriptionDO();
                    toBeAddedItem.setName("告警通知");
                    toBeAddedItem.setUuid(UuidUtils.generateUuid().replace("-", ""));
                    toBeAddedItem.setBeanName("/");
                    toBeAddedItem.setMethodName(item.getMethodName());
                    toBeAddedItem.setSubscriptionCycle("");
                    toBeAddedItem.setIsTimedTask(item.getIsTimedTask());
                    toBeAddedList.add(toBeAddedItem);
                }
            }
        }
        //从数据库中删除已经不在枚举类中的定时任务类型
        List<String> toBeDeleted = new ArrayList<>();
        for (String key : groupedByMethodName.keySet()){
            boolean exist = false;
            for (SubscriptionEnum item:subscriptionEnums) {
                if(item.getMethodName().equals(key)){
                    exist = true;
                    break;
                }
            }
            if(!exist){
                toBeDeleted.add(key);
            }
        }

        if(ObjectUtil.isNotEmpty(toBeDeleted)) {
            reportSubscriptionMapper.deletedSubscriptionTypeByMethodName(toBeDeleted);
        }
        if(ObjectUtil.isNotEmpty(toBeAddedList)) {
            reportSubscriptionMapper.addSubscriptionTypeBatch(toBeAddedList);
        }
    }


    private SubscriptionRespVO  validateReportSubscription(SubscriptionRespVO subscriptionRespVO,AdminUserRespDTO userRespDTO) {
        Map<String, List<ReportSubscriptionUserVO>> groupedByMethodName = subscriptionRespVO.getReportSubscription().stream()
                .collect(Collectors.groupingBy(ReportSubscriptionUserVO::getMethodName));
            //判断用户的所拥有的菜单权限,按照对应的菜单权限添加定时任务
            List<ReportSubscriptionDO> subscriptionTypeList = reportSubscriptionMapper.getAllSubscriptionType();
            List<String> menus = adminUserApi.getUserMenus(userRespDTO.getId()).getData();
            List<ReportSubscriptionUserVO> toBeAddList = new ArrayList<>();
            SubscriptionEnum[] subscriptionEnums = SubscriptionEnum.values();
            List<String> methodNames = new ArrayList<>();
            for (SubscriptionEnum item:subscriptionEnums) {
                //用于找出用户没有菜单权限的定时任务
                boolean exist = false;
                if(menus.contains(item.getMenu())){
                    exist = true;
                    if(item.getMethodName().equals("reportSubscription")) {
                        List<String> result = new ArrayList<>();
                        if (ObjectUtil.isNotEmpty(groupedByMethodName.get(item.getMethodName()))) {
                            List<String> subscriptionCycleList = groupedByMethodName.get(item.getMethodName()).stream().map(ReportSubscriptionUserVO::getSubscriptionCycle)
                                    .collect(Collectors.toList());
                            result = item.getDefaultSubscriptionCycle().stream()
                                    .filter(s -> !subscriptionCycleList.contains(s))
                                    .collect(Collectors.toList());
                            if (ObjectUtil.isEmpty(result)) {
                                continue;
                            }
                        } else {
                            result = item.getDefaultSubscriptionCycle();
                        }
                        if (ObjectUtil.isNotEmpty(result)){
                            result.forEach(cycle -> {
                                subscriptionTypeList.forEach(type -> {
                                    if (type.getMethodName().equals(item.getMethodName()) && type.getSubscriptionCycle().equals(cycle)) {
                                        toBeAddList.add(generateSubscriptionUserVo(cycle, type, userRespDTO));
                                    }
                                });
                            });
                    }
                    }else {
                        List<String> result = new ArrayList<>();
                        if(ObjectUtil.isNotEmpty(groupedByMethodName.get(item.getMethodName()))) {
//                           找出缺失的定时任务
                            List<String> subscriptionCycleList = groupedByMethodName.get(item.getMethodName()).stream().map(ReportSubscriptionUserVO::getSubscriptionCycle)
                                    .collect(Collectors.toList());
                            if(CollectionUtils.isNotEmpty(item.getDefaultSubscriptionCycle())) {
                                result = item.getDefaultSubscriptionCycle().stream()
                                        .filter(s -> !subscriptionCycleList.contains(s))
                                        .collect(Collectors.toList());
                            }
                            if (ObjectUtil.isEmpty(result)) {
                                continue;
                            }
                        }else {
                            result = item.getDefaultSubscriptionCycle();
                        }
                        if (ObjectUtil.isNotEmpty(result)) {
                            result.forEach(cycle -> {
                                subscriptionTypeList.forEach(type -> {
                                    if (type.getMethodName().equals(item.getMethodName()) && type.getIsTimedTask()==1) {
                                        toBeAddList.add(generateSubscriptionUserVo(cycle, type, userRespDTO));
                                    }
                                });
                            });
                        } else {
//                            可能弃用
                                subscriptionTypeList.forEach(type -> {
                                    if (type.getMethodName().equals(item.getMethodName())) {
                                        toBeAddList.add(generateSubscriptionUserVo("day", type, userRespDTO));
                                    }
                                });
                        }
                    }
                }else {
                    exist = false;
                }
                if(!exist){
                    methodNames.add(item.getMethodName());
                }
            }
        if(ObjectUtil.isNotEmpty(methodNames)) {
            reportSubscriptionMapper.deletedUserSubscriptionByMethodName(methodNames);
        }
        if(ObjectUtil.isNotEmpty(toBeAddList)) {
            reportSubscriptionMapper.createDefaultReportSubscriptionBatch(toBeAddList);
        }
        return reportSubscriptionMapper.getByUserId(userRespDTO.getId());
    }

    private ReportSubscriptionUserVO generateSubscriptionUserVo(String cycle,ReportSubscriptionDO subscriptionType,AdminUserRespDTO userRespDTO){
        ReportSubscriptionUserVO subscriptionUserVO = new ReportSubscriptionUserVO();
        String desc = "推送一次";
        if(cycle.equals("week")){
            desc = "每周一"+desc;
        }else if(cycle.equals("day")){
            desc = "每天"+desc;
        }else if(cycle.equals("month")){
            desc = "每月1号"+desc;
        }
        subscriptionUserVO.setSubscriptionUuid(subscriptionType.getUuid());
        subscriptionUserVO.setUserId(userRespDTO.getId());
        subscriptionUserVO.setUserName(userRespDTO.getNickname());
        subscriptionUserVO.setSubscriptionTime("08:00:00");
        subscriptionUserVO.setCronExp(CronConverter.generateCronExpression(cycle,"08:00:00"));
        subscriptionUserVO.setCronDescribe(desc);
        subscriptionUserVO.setWechatState(0);
        subscriptionUserVO.setEmailState(0);
        subscriptionUserVO.setTaskState(0);
        return subscriptionUserVO;
    }
    @Override
     public void updateSubscription(SubscriptionRespVO reportSubscriptionVO){
        Boolean isRepetitive = adminUserApi.validateEmail(reportSubscriptionVO.getEmail()).getData();
        if(isRepetitive){
            throw exception(USER_EMAIL_EXISTS);
        }
        List<ReportSubscriptionUserVO> newSubscriptionVOS = reportSubscriptionVO.getReportSubscription();
        if(StrUtil.isNotEmpty(reportSubscriptionVO.getDingtalkPhone()) || StrUtil.isNotEmpty(reportSubscriptionVO.getWecomPhone()) ){
            newSubscriptionVOS.forEach(x->{
                x.setDingtalkPhone(reportSubscriptionVO.getDingtalkPhone());
                x.setWecomPhone(reportSubscriptionVO.getWecomPhone());
            });
        }
        List<String> uuids = newSubscriptionVOS.stream()
                .map(ReportSubscriptionUserVO::getSubscriptionUuid)
                .collect(Collectors.toList());


        List<ReportSubscriptionUserVO> dingtalk =
                newSubscriptionVOS.stream().filter(item -> item.getDingtalkState() == 1).collect(Collectors.toList());
        UserBindDTO userBindDO = JsonUtils.parseObject(redisTemplate.opsForValue().get("app_key_secret:app").toString(), UserBindDTO.class);
        if(CollectionUtils.isNotEmpty(dingtalk) && StrUtil.isNotEmpty(dingtalk.get(0).getDingtalkPhone())){
            String userIdByMobile = null;
            try {
                String accessToken = DingTalkUtil.getAccessToken(userBindDO.getDingtalkAppKey(),
                        userBindDO.getDingtalkAppSecret());
                 userIdByMobile = DingTalkUtil.getUserIdByMobile(dingtalk.get(0).getDingtalkPhone(), accessToken);
            }catch (Exception e) {
                throw new RuntimeException(e);
            }

           if(StrUtil.isEmpty(userIdByMobile)){
               throw exception(DTPHONE_NOT_EXISTS);
           }
        }

        List<ReportSubscriptionUserVO> wecom =
                newSubscriptionVOS.stream().filter(item -> item.getWecomState() == 1).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(wecom) && StrUtil.isNotEmpty(wecom.get(0).getWecomPhone())){
            String token = QyWxUtils.getToken(userBindDO.getWxCorpid(), userBindDO.getWxCorpsecret());
            String userID = QyWxUtils.getUserID(wecom.get(0).getWecomPhone(), token);
            if(StrUtil.isEmpty(userID)){
                throw exception(WXPHONE_NOT_EXISTS);
            }
        }

        List<ReportSubscriptionUserVO> mail =
                newSubscriptionVOS.stream().filter(item -> item.getEmailState() == 1).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(mail) && (StrUtil.isEmpty(userBindDO.getMailAddress()) || StrUtil.isEmpty(userBindDO.getMailSmtpHost()) || StrUtil.isEmpty(userBindDO.getMailPassword()))){
            throw exception(MAIL_NOT_EXISTS);
        }
        List<ReportSubscriptionUserVO> oldSubscriptionVOs = reportSubscriptionMapper.getByUuid(uuids);
        if(ObjectUtil.isEmpty(oldSubscriptionVOs)){
            //如果用户不存在订阅任务，则创建默认订阅任务
            oldSubscriptionVOs = getByUserId().getReportSubscription();
        }
        //更新定时任务
        for (ReportSubscriptionUserVO newItem:newSubscriptionVOS) {
            //设置免打扰
            newItem.setDoNotDisturb(reportSubscriptionVO.getDoNotDisturb());
            //启用状态更新的要重新设置定时任务
            for(ReportSubscriptionUserVO oldItem:oldSubscriptionVOs){
                if(oldItem.getSubscriptionUuid().equals(newItem.getSubscriptionUuid())&&
                   oldItem.getUserId().equals(newItem.getUserId())) {
                    newItem = updateTask(newItem, oldItem, reportSubscriptionVO.getSubscriptionTime());
                }
            }
        }

        //更新订阅任务的信息
        reportSubscriptionMapper.updateReportSubscriptionById(newSubscriptionVOS);
        adminUserApi.updateUserEmail(reportSubscriptionVO.getEmail());
    }

    @Override
    public List<ReportSubscriptionDTO> getAlarmSubscription() {
        List<ReportSubscriptionDTO> alarmSubscription = reportSubscriptionMapper.getAlarmSubscription();
        Set<Long> maintenanceRoles = roleApi.getRoleIdByCode("operation_maintenance").getData();
//        Set<Long> managerRoles = roleApi.getRoleIdByCode("om_manager").getData();
        for (ReportSubscriptionDTO reportSubscriptionDTO : alarmSubscription) {
            //判断用户角色
            Long userId = reportSubscriptionDTO.getUserId();
            Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(userId, singleton(CommonStatusEnum.ENABLE.getStatus()));
            //运维人员
            boolean isMaintenance = roleIds.stream().anyMatch(maintenanceRoles::contains);
            if (isMaintenance){
                String serviceTenantId = adminUserApi.getUser(userId).getData().getServiceTenantId();
                if (StringUtils.isNotEmpty(serviceTenantId)){
                    List<String> tenantIdsStr = Arrays.asList(serviceTenantId.split(","));
                    List<Long> tenantIds = tenantIdsStr.stream()
                            .map(Long::parseLong)
                            .collect(Collectors.toList());
                    //查询对应租户下的平台
                    String plantFormId=reportSubscriptionMapper.getPlatFormIdByTenants(tenantIds);
                    reportSubscriptionDTO.setPlatformId(plantFormId);
                }else {
                    reportSubscriptionDTO.setPlatformId("");
                }
            }
        }
        return alarmSubscription;
    }


    @Override
    public void run(String... args) throws Exception {
        List<ReportSubscriptionUserVO> jobList = reportSubscriptionMapper.getTimedTasks();
        if (CollectionUtils.isNotEmpty(jobList)) {
            UserBindDTO userBindDO = null;
            if (redisTemplate.hasKey("app_key_secret:app")) {
                userBindDO = JsonUtils.parseObject(redisTemplate.opsForValue().get("app_key_secret:app").toString(),
                        UserBindDTO.class);
            }
            for (ReportSubscriptionUserVO job : jobList) {
                String subscriptionUuid = job.getUserId()+"-"+job.getSubscriptionUuid();
                if(job.getIsTimedTask()==1) {
                    if(BeanUtil.isNotEmpty(userBindDO)){
                        job.setAppKey(userBindDO.getDingtalkAppKey());
                        job.setAppSecret(userBindDO.getDingtalkAppSecret());
                        job.setAgentId(userBindDO.getDingtalkAgentId());
                        job.setCorpid(userBindDO.getWxCorpid());
                        job.setCorpsecret(userBindDO.getWxCorpsecret());
                        job.setWecomAgentId(userBindDO.getWxAgentId());
                    }
                    SchedulingRunnable task = new SchedulingRunnable(job.getBeanName(), job.getMethodName(), subscriptionUuid, job);
                    cronTaskRegistrar.addCronTask(task, job.getCronExp());
                    log.info("定时任务" + subscriptionUuid + "已加载完毕...\n");
                }
            }
            jobList.forEach(item->{
                item.setTaskState(1);
            });
            //标记添加过的任务
            reportSubscriptionMapper.updateReportSubscriptionById(jobList);
        }
    }

    private  ReportSubscriptionUserVO updateTask(ReportSubscriptionUserVO reportSubscriptionVO,ReportSubscriptionUserVO oldSubscriptionVO,String subscriptionTime){
        // 生成 Cron 表达式
        String cronExp = reportSubscriptionVO.getCronIsEnable() == 1 ?
                reportSubscriptionVO.getCronExp() :
                CronConverter.generateCronExpression(reportSubscriptionVO.getSubscriptionCycle(), subscriptionTime);

        // 生成描述
        String desc = generateDescription(reportSubscriptionVO.getSubscriptionCycle());

        // 设置订阅 UUID
        String subscriptionUuid =reportSubscriptionVO.getUserId()+"-"+ reportSubscriptionVO.getSubscriptionUuid();

        // 更新订阅信息
        reportSubscriptionVO.setCronDescribe(desc);
        reportSubscriptionVO.setCronExp(cronExp);
        reportSubscriptionVO.setSubscriptionTime(subscriptionTime);

        // 设置任务状态
        if(reportSubscriptionVO.getWechatState() == 0 && reportSubscriptionVO.getEmailState()==0){
            reportSubscriptionVO.setTaskState(0);
        }

        // 处理定时任务
        if (reportSubscriptionVO.getIsTimedTask() == 1) { // 如果需要加入定时任务
            if(reportSubscriptionVO.getDoNotDisturb()==0) {
                handleCronTask(reportSubscriptionVO, oldSubscriptionVO, subscriptionUuid);
            } else {
                cancelOldTask(oldSubscriptionVO, reportSubscriptionVO, subscriptionUuid);
            }
        }

        return reportSubscriptionVO;
    }

    // 生成描述
    private String generateDescription(String subscriptionCycle) {
        switch (subscriptionCycle) {
            case "week":
                return "每周一推送一次";
            case "day":
                return "每天推送一次";
            case "month":
                return "每月1号推送一次";
            default:
                return "推送一次";
        }
    }

    // 处理定时任务的逻辑
    private void handleCronTask(ReportSubscriptionUserVO reportSubscriptionVO, ReportSubscriptionUserVO oldSubscriptionVO, String subscriptionUuid) {
        // 如果 Cron 表达式变更且推送状态为启用
        // 检查是否需要更新任务
        boolean isCronExpChanged = !reportSubscriptionVO.getCronExp().equals(oldSubscriptionVO.getCronExp());
        boolean isNotificationEnabled = reportSubscriptionVO.getWechatState() == 1 ||
                reportSubscriptionVO.getEmailState() == 1 ||
                reportSubscriptionVO.getDingtalkState() == 1 ||
                reportSubscriptionVO.getWecomState() == 1;



        boolean isContactChanged = !Objects.equals(oldSubscriptionVO.getDingtalkPhone(), reportSubscriptionVO.getDingtalkPhone()) ||
                !Objects.equals(oldSubscriptionVO.getWecomPhone(), reportSubscriptionVO.getWecomPhone()) ||
                !Objects.equals(oldSubscriptionVO.getEmail(), reportSubscriptionVO.getEmail());

        // 如果条件满足，则移除旧任务并添加新任务
        if ((isCronExpChanged && isNotificationEnabled) || isContactChanged) {
            updateTaskTwo(oldSubscriptionVO, reportSubscriptionVO, subscriptionUuid);
        }

        // 如果推送状态都关闭，取消定时任务
        if (oldSubscriptionVO.getTaskState() == 1 &&
                (reportSubscriptionVO.getWechatState() == 0 && reportSubscriptionVO.getEmailState() == 0 && reportSubscriptionVO.getDingtalkState() == 0 && reportSubscriptionVO.getWechatState() == 0)) {

            cancelOldTask(oldSubscriptionVO, reportSubscriptionVO, subscriptionUuid);
                }

        // 如果原定时任务未启动，且现在启用推送
        if (oldSubscriptionVO.getTaskState() == 0 &&
                (reportSubscriptionVO.getWechatState() == 1 || reportSubscriptionVO.getEmailState() == 1 && reportSubscriptionVO.getDingtalkState() == 1 && reportSubscriptionVO.getWechatState() == 1)) {

            addNewTask(reportSubscriptionVO, subscriptionUuid);
                }
    }

    // 更新任务的方法
    private void updateTaskTwo(ReportSubscriptionUserVO oldSubscriptionVO, ReportSubscriptionUserVO reportSubscriptionVO, String subscriptionUuid) {
        // 移除旧的任务
        removeOldTask(oldSubscriptionVO, reportSubscriptionVO, subscriptionUuid);

        // 创建新的任务
        addNewTask(reportSubscriptionVO, subscriptionUuid);
    }

                    //移除旧的任务
    private void removeOldTask(ReportSubscriptionUserVO oldSubscriptionVO, ReportSubscriptionUserVO reportSubscriptionVO, String subscriptionUuid) {
                    SchedulingRunnable taskOld = new SchedulingRunnable(oldSubscriptionVO.getBeanName(), reportSubscriptionVO.getMethodName(), subscriptionUuid, oldSubscriptionVO);
                    cronTaskRegistrar.removeCronTask(taskOld);
        log.info("定时任务" + subscriptionUuid + "已移除...\n");
    }

                    //创建新的任务
    private void addNewTask(ReportSubscriptionUserVO reportSubscriptionVO, String subscriptionUuid) {
                    SchedulingRunnable taskNew = new SchedulingRunnable(reportSubscriptionVO.getBeanName(), reportSubscriptionVO.getMethodName(), subscriptionUuid, reportSubscriptionVO);
                    cronTaskRegistrar.addCronTask(taskNew, reportSubscriptionVO.getCronExp());
                    reportSubscriptionVO.setTaskState(1);
        log.info("定时任务" + subscriptionUuid + "已加载完毕...\n");
                }

    // 取消旧的任务
    private void cancelOldTask(ReportSubscriptionUserVO oldSubscriptionVO, ReportSubscriptionUserVO reportSubscriptionVO, String subscriptionUuid) {
                SchedulingRunnable task = new SchedulingRunnable(oldSubscriptionVO.getBeanName(), reportSubscriptionVO.getMethodName(), subscriptionUuid, oldSubscriptionVO);
                reportSubscriptionVO.setTaskState(0);
                cronTaskRegistrar.removeCronTask(task);
                log.info("定时任务" + subscriptionUuid + "已取消...\n");
            }

    public void removeTimedTask(Long userId) {
        SubscriptionRespVO subscriptionRespVO = reportSubscriptionMapper.getByUserId(userId);
        for (ReportSubscriptionUserVO subscription : subscriptionRespVO.getReportSubscription()) {
            if (subscription.getTaskState()==1) {
                String subscriptionUuid = userId + "-" + subscription.getSubscriptionUuid();
                SchedulingRunnable taskOld = new SchedulingRunnable(subscription.getBeanName(), subscription.getMethodName(), subscriptionUuid, subscription);
                cronTaskRegistrar.removeCronTask(taskOld);
                log.info("用户:" + userId + "的定时任务(" + subscription.getSubscriptionUuid() + "[" + subscription.getName() + "])已移除");
            }
        }
    }
}
