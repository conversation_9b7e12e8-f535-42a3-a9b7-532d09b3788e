package cn.iocoder.zj.module.report.controller.admin.zjreport.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName : ZjReportPlatformListRespVO  //类名
 * @Description : 平台趋势集合  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/4/11  11:28
 */
@Schema(description = "报表管理 - 平台趋势 Response VO")
@Data
public class ZjReportPlatformListRespVO {
    @Schema(description = "平台")
    private String platformName;
}
