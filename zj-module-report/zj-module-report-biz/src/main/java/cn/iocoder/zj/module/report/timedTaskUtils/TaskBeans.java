package cn.iocoder.zj.module.report.timedTaskUtils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.mail.MailAccount;
import cn.hutool.extra.mail.MailUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.iocoder.zj.framework.common.constants.FileTypeConstants;
import cn.iocoder.zj.framework.common.pojo.DingDingAuthorizationDTO;
import cn.iocoder.zj.framework.common.pojo.MinioConfigDTO;
import cn.iocoder.zj.framework.common.pojo.UserBindDTO;
import cn.iocoder.zj.framework.common.util.dingtalk.DingTalkUtil;
import cn.iocoder.zj.framework.common.util.dingtalk.QyWxUtils;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.module.infra.api.file.FileApi;
import cn.iocoder.zj.module.report.controller.admin.reporsubscription.VO.ReportSubscriptionUserVO;
import cn.iocoder.zj.module.report.service.zjreport.ZjreportService;
import com.google.gson.Gson;
import com.taobao.api.ApiException;
import jodd.util.StringUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;



@Component("subscription")
@Slf4j
public class TaskBeans {
    @Autowired
    ZjreportService zjreportService;

    @Resource
    private FileApi fileApi;

    @Resource
    private RedisTemplate redisTemplate;

    @Value("${wx.mp.app-id}")
    private String appId;

    @Value("${wx.mp.secret}")
    private String secret;

    @Value("${wx.mp.report_template_id}")
    private String reportTemplateId;
    @Value("${wx.mp.alert_template_id}")
    private String alertTemplateId;

//    @Value("${e-mail.address}")
//    private String address;
//
//    @Value("${e-mail.password}")
//    private String password;
//
//    @Value("${e-mail.host}")
//    private String mailHost;

    @Value("${cloud-domain.url}")
    private String cloudDomain;


    private String accessTokenUrl = "https://api.weixin.qq.com/cgi-bin/stable_token";
    private String path =  "file-template" + File.separator + "email-template.html";
    private String topImgPath =  "https://s3.zjiecn.com/zj-server-fh/20240711101838微信图片_20240706144526.png";
    private String bottomImgPath =  "https://s3.zjiecn.com/zj-server-fh/20240711104229微信图片_20240711101759.png";

    public void reportSubscription(ReportSubscriptionUserVO subscriptionVO) {
        String title = subscriptionVO.getName() + "(" + DateUtil.format(new Date(), "yyyy-MM-dd") + ")";
        String content = "您订阅的" + subscriptionVO.getName() + "已送达，请查阅！";
        File tempFile = null;
        try {
            // Create a temporary file
            tempFile = File.createTempFile(title, ".docx");
            UserBindDTO userBindDO = JsonUtils.parseObject(redisTemplate.opsForValue().get("app_key_secret:app").toString(), UserBindDTO.class);
            // Process the email HTML template
            String templateString = loadTemplate();
            templateString = templateString.replace("${reportName}", subscriptionVO.getName())
                    .replace("${topImageUrl}", topImgPath)
                    .replace("${bottomImageUrl}", bottomImgPath);

            // Generate report content
            ByteArrayOutputStream reportStream;
            if ("day".equals(subscriptionVO.getSubscriptionCycle())) {
                reportStream = zjreportService.exportDayReport(subscriptionVO.getUserId(), null, null);
            } else {
                reportStream = zjreportService.exportWeekOrMonthReport(subscriptionVO.getSubscriptionCycle(), subscriptionVO.getUserId(), null, null);
            }

            // Write report to temporary file
            try (OutputStream fileOutputStream = new FileOutputStream(tempFile)) {
                fileOutputStream.write(reportStream.toByteArray());
            }

            try {
                if (subscriptionVO.getEmailState() == 1 && userBindDO.getEmailState() == 1) {
                    sendEmail(subscriptionVO, templateString, tempFile);
                }
            } catch (Exception e) {
                log.error("发送邮件失败", e);
            }

            try {
                if (subscriptionVO.getWechatState() == 1 ) {
                    sendWeChatMessage(subscriptionVO, tempFile);
                }
            } catch (Exception e) {
                log.error("发送微信消息失败", e);
            }

            try {
                if (subscriptionVO.getDingtalkState() == 1 && StringUtil.isNotEmpty(subscriptionVO.getDingtalkPhone()) && BeanUtil.isNotEmpty(userBindDO) && userBindDO.getDingtalkState() == 1) {
                    sendDingTalkMessage(subscriptionVO, tempFile, userBindDO);
                }
            } catch (Exception e) {
                log.error("发送钉钉消息失败", e);
            }

            try {
                if (subscriptionVO.getWecomState() == 1 && StringUtil.isNotEmpty(subscriptionVO.getWecomPhone()) && BeanUtil.isNotEmpty(userBindDO)  && userBindDO.getWxState() == 1) {
                    sendWeWorkMessage(subscriptionVO, tempFile, userBindDO);
                }
            } catch (Exception e) {
                log.error("发送企业微信消息失败", e);
            }
        } catch (Exception e) {
            log.error("日报发送失败", e);
        } finally {
            // Delete the temporary file
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    private String loadTemplate() throws IOException {
        ClassPathResource classPathResource = new ClassPathResource(path);
        try (InputStream inputStream = classPathResource.getInputStream()) {
            return IOUtils.toString(inputStream, StandardCharsets.UTF_8);
        }
    }

    private void sendEmail(ReportSubscriptionUserVO subscriptionVO, String templateString, File tempFile) {
        UserBindDTO userBindDO = JsonUtils.parseObject(redisTemplate.opsForValue().get("app_key_secret:app").toString(), UserBindDTO.class);
        if (isUserBindInfoInvalid(userBindDO)) {
            log.info("邮箱参数为空");
            return;
        }
        MailAccount mailAccount = new MailAccount();
        mailAccount.setHost(userBindDO.getMailSmtpHost());
        mailAccount.setPort(465);
        mailAccount.setSslEnable(true);
        mailAccount.setUser(userBindDO.getMailAddress());
        mailAccount.setAuth(true);
        mailAccount.setPass(userBindDO.getMailPassword());
        mailAccount.setFrom(userBindDO.getMailAddress());
        MailUtil.send(mailAccount, subscriptionVO.getEmail(), "云星辰" + subscriptionVO.getName(), templateString, true, tempFile);
        log.info(subscriptionVO.getUserName() + "的" + subscriptionVO.getName() + "已送达");
    }

    private boolean isUserBindInfoInvalid(UserBindDTO userBindDO) {
        return BeanUtil.isEmpty(userBindDO) ||
                StrUtil.isEmpty(userBindDO.getMailAddress()) ||
                StrUtil.isEmpty(userBindDO.getMailPassword()) ||
                StrUtil.isEmpty(userBindDO.getMailSmtpHost());
    }

    private void sendWeChatMessage(ReportSubscriptionUserVO subscriptionVO, File tempFile) throws IOException {
        OkHttpClient client = new OkHttpClient();
        String openId = zjreportService.getWeChatUserOpenId(subscriptionVO.getUserId());
        if (StringUtil.isNotEmpty(openId)) {
            String url = uploadFileToMinio(tempFile);
            String accessToken = getAccessToken();

            String weChatUrl = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=" + accessToken;
            Map<String, WeChatTemplateMsg> sendMsg = new HashMap<>();
            sendMsg.put("time9", new WeChatTemplateMsg(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss")));
            sendMsg.put("thing2", new WeChatTemplateMsg(subscriptionVO.getName()));

            Map<String, Object> sendBody = new HashMap<>();
            sendBody.put("touser", openId);
            sendBody.put("url", url);
            sendBody.put("topcolor", "#FF0000");
            sendBody.put("data", sendMsg);
            sendBody.put("template_id", reportTemplateId);

            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> response = restTemplate.postForEntity(weChatUrl, sendBody, String.class);
            log.info("微信报表已推送: {}", response.getBody());
        }
    }

    @SneakyThrows
    private String uploadFileToMinio(File tempFile){
        try (InputStream iss = Files.newInputStream(tempFile.toPath())) {
            String d = cn.iocoder.zj.framework.common.util.string.StringUtil.getSavePath(tempFile.getName(),
                    FileTypeConstants.FILE_TYPE,null);
            MinioConfigDTO minioConfig = fileApi.getMinioConfig();
            String url = fileApi.createFileUrl(tempFile.getName(), d, IoUtil.readBytes(iss));
            return minioConfig.getEndpoint() + "/" +  minioConfig.getBucket() + "/" + url;
        }
    }

    private String getAccessToken() {
        String accessToken = "";
        if (redisTemplate.hasKey("wechat_access_token")) {
            accessToken = String.valueOf(redisTemplate.opsForValue().get("wechat_access_token"));
        } else {
            HashMap<String, Object> map = new HashMap<>();
            map.put("grant_type", "client_credential");
            map.put("appid", appId);
            map.put("secret", secret);
            map.put("force_refresh", false);

            Gson gson = new Gson();
            String json = gson.toJson(map);
            String resp = HttpUtil.post(accessTokenUrl, json);
            JSONObject result = JSONUtil.parseObj(resp);
            accessToken = result.getStr("access_token");
            redisTemplate.opsForValue().set("wechat_access_token", accessToken, 1, TimeUnit.HOURS);
        }
        return accessToken;
    }

    @SneakyThrows
    private void sendDingTalkMessage(ReportSubscriptionUserVO subscriptionVO, File tempFile,UserBindDTO userBindDO) {
        String accessToken = DingTalkUtil.getAccessToken(userBindDO.getDingtalkAppKey(), userBindDO.getDingtalkAppSecret());
        if (StringUtil.isEmpty(accessToken)) {
            log.info("钉钉token获取为空" + accessToken);
            throw new RuntimeException("钉钉token获取为空");
        }

        DingDingAuthorizationDTO dingDingAuthorization = new DingDingAuthorizationDTO();
        dingDingAuthorization.setAppKey(userBindDO.getDingtalkAppKey());
        dingDingAuthorization.setAppSecret(userBindDO.getDingtalkAppSecret());
        dingDingAuthorization.setAgentId(userBindDO.getDingtalkAgentId());
        dingDingAuthorization.setMobile(subscriptionVO.getDingtalkPhone());
        dingDingAuthorization.setUrl(tempFile.getAbsolutePath());

        DingTalkUtil.sendDingDingMessage(dingDingAuthorization, accessToken, "File");
        log.info(subscriptionVO.getUserName() + "的" + subscriptionVO.getName() + "已送达");
    }

    @SneakyThrows
    private void sendWeWorkMessage(ReportSubscriptionUserVO subscriptionVO, File tempFile,UserBindDTO userBindDO) {
        String accessToken = QyWxUtils.getToken(userBindDO.getWxCorpid(), userBindDO.getWxCorpsecret());
        if (StringUtil.isEmpty(accessToken)) {
            log.info("企微token获取为空" + accessToken);
            throw new RuntimeException("企微token获取为空");
        }

        DingDingAuthorizationDTO dingDingAuthorization = new DingDingAuthorizationDTO();
        dingDingAuthorization.setAppKey(userBindDO.getWxCorpid());
        dingDingAuthorization.setAppSecret(userBindDO.getWxCorpsecret());
        dingDingAuthorization.setAgentId(userBindDO.getWxAgentId());
        dingDingAuthorization.setMobile(subscriptionVO.getWecomPhone());
        dingDingAuthorization.setUrl(tempFile.getAbsolutePath());

        QyWxUtils.sendTextMsg(accessToken,dingDingAuthorization, "File");
        log.info(subscriptionVO.getUserName() + "的" + subscriptionVO.getName() + "已送达");
    }

    @SneakyThrows
    public void alertSubscription(ReportSubscriptionUserVO subscriptionVO) {
        Long alertNum = zjreportService.getAlertCountByUserPlatformIds(subscriptionVO.getUserId());
        String uuid = subscriptionVO.getSubscriptionUuid();
        UserBindDTO userBindDO = getUserBindData();
        try {
            if (subscriptionVO.getWechatState() == 1) {
                sendWeChatNotification(subscriptionVO, alertNum);
                log.info("公众号发送成功, {}", alertNum);
            }
        } catch (Exception e) {
            log.error("公众号发送失败", e);
        }

        try {
            if (subscriptionVO.getDingtalkState() == 1 && StringUtil.isNotEmpty(subscriptionVO.getDingtalkPhone()) && BeanUtil.isNotEmpty(userBindDO) && userBindDO.getDingtalkState() == 1) {
                sendDingTalkNotification(subscriptionVO, userBindDO, alertNum);
                log.info("钉钉发送成功, {}", alertNum);
            }
        } catch (Exception e) {
            log.error("钉钉发送失败", e);
        }

        try {
            if (subscriptionVO.getWecomState() == 1 && StringUtil.isNotEmpty(subscriptionVO.getWecomPhone()) && BeanUtil.isNotEmpty(userBindDO)  && userBindDO.getWxState() == 1) {
                sendWeComNotification(subscriptionVO, userBindDO, alertNum);
                log.info("企微发送成功, {}", alertNum);
            }
        } catch (Exception e) {
            log.error("企微发送失败", e);
        }

        System.out.println("任务" + uuid + "执行成功！");
    }

    private UserBindDTO getUserBindData() {
        return JsonUtils.parseObject(redisTemplate.opsForValue().get("app_key_secret:app").toString(), UserBindDTO.class);
    }

    private void sendWeChatNotification(ReportSubscriptionUserVO subscriptionVO, Long alertNum) {
        String openId = zjreportService.getWeChatUserOpenId(subscriptionVO.getUserId());
        if (StringUtil.isEmpty(openId)) {
            return;
        }

        String redirectUrl = generateWeChatRedirectUrl();
        String accessToken = getWeChatAccessToken();

        if (StringUtil.isEmpty(accessToken)) {
            log.error("微信AccessToken获取失败");
            return;
        }

        Map<String, WeChatTemplateMsg> templateParams = new HashMap<>();
        templateParams.put("time1", new WeChatTemplateMsg(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss")));
        templateParams.put("thing3", new WeChatTemplateMsg("当前有" + alertNum + "条告警待处理，请注意查看"));
        templateParams.put("character_string25", new WeChatTemplateMsg(Convert.toStr(alertNum)));

        sendWeChatTemplateMessage(accessToken, openId, redirectUrl, templateParams);
    }

    private String generateWeChatRedirectUrl() {
        try {
            String encodedUrl = URLEncoder.encode(cloudDomain, "UTF-8");
            String parameter = URLUtil.encode("/pages/alarm/index");
            return "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxe3d3d4577c6c41b6&redirect_uri=" + encodedUrl +
                    "&response_type=code&scope=snsapi_userinfo&state=" + parameter + "&connect_redirect=1#wechat_redirect";
        } catch (UnsupportedEncodingException e) {
            log.error("URL编码错误: {}", e.getMessage());
            return "";
        }
    }

    private String getWeChatAccessToken() {
        String accessToken = "";
        if (redisTemplate.hasKey("wechat_access_token")) {
            accessToken = String.valueOf(redisTemplate.opsForValue().get("wechat_access_token"));
        } else {
            HashMap<String, Object> params = new HashMap<>();
            params.put("grant_type", "client_credential");
            params.put("appid", appId);
            params.put("secret", secret);
            String resp = HttpUtil.post(accessTokenUrl, new Gson().toJson(params));
            JSONObject result = JSONUtil.parseObj(resp);
            accessToken = result.getStr("access_token");
            redisTemplate.opsForValue().set("wechat_access_token", accessToken, 1, TimeUnit.HOURS);
        }
        return accessToken;
    }

    private void sendWeChatTemplateMessage(String accessToken, String openId, String redirectUrl, Map<String, WeChatTemplateMsg> templateParams) {
        String weChatUrl = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=" + accessToken;
        Map<String, Object> sendBody = new HashMap<>();
        sendBody.put("touser", openId);
        sendBody.put("url", redirectUrl);
        sendBody.put("topcolor", "#FF0000");
        sendBody.put("data", templateParams);
        sendBody.put("template_id", alertTemplateId);

        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(weChatUrl, sendBody, String.class);
        log.info("微信告警通知已推送: {}", responseEntity.getBody());
    }

    private void sendDingTalkNotification(ReportSubscriptionUserVO subscriptionVO, UserBindDTO userBindDO, Long alertNum) throws ApiException {
        String accessToken = DingTalkUtil.getAccessToken(userBindDO.getDingtalkAppKey(), userBindDO.getDingtalkAppSecret());
        if (StringUtil.isEmpty(accessToken)) {
            log.error("钉钉token获取为空");
            throw new RuntimeException("钉钉token获取为空");
        }

        DingDingAuthorizationDTO dingDingAuthorization = new DingDingAuthorizationDTO();
        dingDingAuthorization.setAppKey(userBindDO.getDingtalkAppKey());
        dingDingAuthorization.setAppSecret(userBindDO.getDingtalkAppSecret());
        dingDingAuthorization.setAgentId(userBindDO.getDingtalkAgentId());
        dingDingAuthorization.setMobile(subscriptionVO.getDingtalkPhone());
        dingDingAuthorization.setWarningTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        dingDingAuthorization.setWarningObject(Convert.toStr(alertNum));
        dingDingAuthorization.setWarningItem("当前有" + alertNum + "条告警待处理，请注意查看");
        dingDingAuthorization.setUrl("");

        DingTalkUtil.sendDingDingMessage(dingDingAuthorization, accessToken, "Num");
    }

    private void sendWeComNotification(ReportSubscriptionUserVO subscriptionVO, UserBindDTO userBindDO, Long alertNum) {
        String accessToken = QyWxUtils.getToken(userBindDO.getWxCorpid(), userBindDO.getWxCorpsecret());
        if (StringUtil.isEmpty(accessToken)) {
            log.error("企微token获取为空");
            throw new RuntimeException("企微token获取为空");
        }

        DingDingAuthorizationDTO dingDingAuthorization = new DingDingAuthorizationDTO();
        dingDingAuthorization.setAppKey(userBindDO.getWxCorpid());
        dingDingAuthorization.setAppSecret(userBindDO.getWxCorpsecret());
        dingDingAuthorization.setAgentId(userBindDO.getWxAgentId());
        dingDingAuthorization.setMobile(subscriptionVO.getWecomPhone());
        dingDingAuthorization.setWarningTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        dingDingAuthorization.setWarningObject("当前有" + alertNum + "条告警待处理，请注意查看");
        dingDingAuthorization.setWarningItem(Convert.toStr(alertNum));
        dingDingAuthorization.setUrl("www");

        QyWxUtils.sendTextMsg(accessToken, dingDingAuthorization, "num");
    }
}
