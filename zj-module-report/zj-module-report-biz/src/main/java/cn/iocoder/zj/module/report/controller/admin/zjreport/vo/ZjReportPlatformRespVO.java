package cn.iocoder.zj.module.report.controller.admin.zjreport.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName : ZjReportPlatformRespVO  //类名
 * @Description : 平台趋势增长  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/4/11  10:13
 */
@Schema(description = "报表管理 - 平台趋势 Response VO")
@Data
public class ZjReportPlatformRespVO {

    @Schema(description = "平台id")
    private String platformId;

    @Schema(description = "日期")
    private Date date;

    @Schema(description = "数量")
    private Long count;
}
