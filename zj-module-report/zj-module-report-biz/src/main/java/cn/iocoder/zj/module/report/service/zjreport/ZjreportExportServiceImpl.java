package cn.iocoder.zj.module.report.service.zjreport;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.enums.ResourceAppEnum;
import cn.iocoder.zj.framework.common.enums.ResourceCategoryEnum;
import cn.iocoder.zj.framework.common.util.date.DateUtils;
import cn.iocoder.zj.module.monitor.api.tenantscreen.TenantScreenApi;
import cn.iocoder.zj.module.report.controller.admin.zjreport.vo.ZjReportPlatformRespVO;
import cn.iocoder.zj.module.report.controller.admin.zjreport.vo.ZjReportProcessRespVO;
import cn.iocoder.zj.module.report.dal.mysql.zjreport.ZjreportMapper;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.singleton;

@Service
@Validated
@Slf4j
public class ZjreportExportServiceImpl implements ZjreportExportService{
    @Resource
    ZjreportMapper zjreportMapper;
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    RoleApi roleApi;
    @Resource
    PermissionApi permissionApi;

    @Resource
    AdminUserApi adminUserApi;
    @Resource
    TenantScreenApi tenantScreenApi;

    @Override
    public List<Map<String, Object>> getAlarmAndProcessByType(Long userId,Long platformId) {

        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(userId, singleton(CommonStatusEnum.ENABLE.getStatus()));
        Boolean isSuperAdmin = Boolean.valueOf(roleApi.decideAnySuperAdmin(roleIds));
        //获取当前用户的平台信息
        AdminUserRespDTO currentUser = adminUserApi.getUser(userId).getData();
        long state = zjreportMapper.getStateByTenantId(currentUser.getTenantId());
        List<Map> mapList = platformconfigApi.getPlatformListByUserId(String.valueOf(userId)).getData();
        //枚举资源类型
        List<String> appTypeList = new ArrayList<>();
        appTypeList.add("host");
        appTypeList.add("hardware");
        appTypeList.add("storage");
        ResourceAppEnum[] values = ResourceAppEnum.values();
        for (ResourceAppEnum item : values) {
            appTypeList.add(item.getValue());
        }

        // 定义一个映射，将应用类型映射到其相应的列表
        Map<String, List<String>> appTypeMap = defineTypeMa();

        //合并doris和mysql数据
        List<Map<String, Object>> alarmByType = zjreportMapper.getAlarmByType(mapList,platformId);
        alarmByType = supplementType(appTypeList, alarmByType);

        List<Map<String, Object>> processByType = zjreportMapper.getProcessByType(currentUser.getTenantId(), isSuperAdmin);
        processByType = supplementType(appTypeList, processByType);
        List<Map<String, Object>> mixMapList = new ArrayList<>();
        for (Map<String, Object> a : alarmByType) {
            for (Map<String, Object> p : processByType) {
                if (p.get("app").equals(a.get("app"))) {
                    Map<String, Object> mix = new HashMap<>();
                    mix.put("app", a.get("app"));
                    mix.put("alarm", a.get("total"));
                    mix.put("process", p.get("total"));
                    mixMapList.add(mix);
                }
            }
        }
        //资源类型归并
        List<Map<String, Object>> datalist = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : appTypeMap.entrySet()) {
            Map<String, Object> maps = new HashMap();
            String appType = entry.getKey();
            String label = getResourceLabel(entry.getKey());
            List<String> apps = entry.getValue();
            // 遍历当前类型的每个应用
            Long alarm = 0L;
            Long process = 0L;
            for (String app : apps) {
                for (Map map : mixMapList) {
                    if (app.equals(map.get("app"))) {
                        alarm = alarm + Convert.toLong(map.get("alarm"));
                        process = process + Convert.toLong(map.get("process"));
                    }
                }
            }
            maps.put("code", appType);
            maps.put("app", label);
            maps.put("alarm", alarm);
            maps.put("process", process);
            datalist.add(maps);
        }

        if (!datalist.isEmpty()){
            // 如果 state 等于 1，过滤掉指定的指标
            if (state == 1) {
                // 定义需要过滤的指标名称列表
                List<String> filterApps = Arrays.asList("云主机", "宿主机", "云存储", "存储", "服务器", "网络");
                datalist = datalist.stream()
                        .filter(map -> !filterApps.contains(map.get("app")))
                        .collect(Collectors.toList());
            }
        }

        return datalist;
    }

    @Override
    public List<Map<String, Object>> getAlarmAndProcessByTypeYesterday(Long userId,Long platformId) {
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(userId, singleton(CommonStatusEnum.ENABLE.getStatus()));
        Boolean isSuperAdmin = Boolean.valueOf(roleApi.decideAnySuperAdmin(roleIds));
        //获取当前用户的平台信息
        AdminUserRespDTO currentUser = adminUserApi.getUser(userId).getData();
        List<Map> mapList = platformconfigApi.getPlatformListByUserId(String.valueOf(userId)).getData();
        //枚举资源类型
        List<String> appTypeList = new ArrayList<>();
        appTypeList.add("host");
        appTypeList.add("hardware");
        appTypeList.add("storage");
        ResourceAppEnum[] values = ResourceAppEnum.values();
        for (ResourceAppEnum item : values) {
            appTypeList.add(item.getValue());
        }

        // 定义一个映射，将应用类型映射到其相应的列表
        Map<String, List<String>> appTypeMap = defineTypeMa();

        //合并doris和mysql数据
        List<Map<String, Object>> alarmByType = zjreportMapper.getAlarmByTypeYesterday(mapList,platformId);
        alarmByType = supplementType(appTypeList, alarmByType);

        List<Map<String, Object>> processByType = zjreportMapper.getProcessByYesterType(currentUser.getTenantId(), isSuperAdmin);
        processByType = supplementType(appTypeList, processByType);
        List<Map<String, Object>> mixMapList = new ArrayList<>();
        for (Map<String, Object> a : alarmByType) {
            for (Map<String, Object> p : processByType) {
                if (p.get("app").equals(a.get("app"))) {
                    Map<String, Object> mix = new HashMap<>();
                    mix.put("app", a.get("app"));
                    mix.put("alarm", a.get("total"));
                    mix.put("process", p.get("total"));
                    mixMapList.add(mix);
                }
            }
        }
        //资源类型归并
        List<Map<String, Object>> datalist = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : appTypeMap.entrySet()) {
            Map<String, Object> maps = new HashMap();
            String appType = entry.getKey();
            String label = getResourceLabel(entry.getKey());
            List<String> apps = entry.getValue();
            // 遍历当前类型的每个应用
            Long alarm = 0L;
            Long process = 0L;
            for (String app : apps) {
                for (Map map : mixMapList) {
                    if (app.equals(map.get("app"))) {
                        alarm = alarm + Convert.toLong(map.get("alarm"));
                        process = process + Convert.toLong(map.get("process"));
                    }
                }
            }
            maps.put("code", appType);
            maps.put("app", label);
            maps.put("alarm", alarm);
            maps.put("process", process);
            datalist.add(maps);
        }
        return datalist;
    }
    @Override
    public List<Map<String, Object>> getAlarmAndProcessByPlatform(Long userId,Long platformId) {
        //获取当前用户的平台信息
        AdminUserRespDTO currentUser = adminUserApi.getUser(userId).getData();
        List<Map> mapList = platformconfigApi.getPlatformListByUserId(String.valueOf(currentUser.getId())).getData();
        //过滤掉非选择的平台
        if(ObjectUtil.isNotEmpty(platformId)) {
            mapList = mapList.stream()
                    .filter(map -> map.containsKey("platformId") && Long.parseLong(String.valueOf(map.get("platformId"))) == platformId)
                    .collect(Collectors.toList());
        }
        List<Map<String, Object>> alarmByType = zjreportMapper.getAlarmByYesterPlatform(mapList,platformId);
        List<Map<String, Object>> processByType = zjreportMapper.getProcessByYesterPlatform(mapList,platformId);
        List<Map<String, Object>> mixMapList = new ArrayList<>();
        for (Map plat : mapList) {
            Map<String, Object> mixMap = new HashMap<>();
            mixMap.put("platformId", plat.get("platformId"));
            mixMap.put("platformName", plat.get("platformName"));
            mixMap.put("alarm", 0);
            mixMap.put("process", 0);
            for (Map alarm : alarmByType) {
                if (String.valueOf(alarm.get("platformId")).equals(String.valueOf(plat.get("platformId")))) {
                    mixMap.put("alarm", alarm.get("total"));
                }
            }
            for (Map process : processByType) {
                if (String.valueOf(process.get("platformId")).equals(String.valueOf(plat.get("platformId")))) {
                    mixMap.put("process", process.get("total"));
                }
            }
            mixMapList.add(mixMap);
        }
        return mixMapList;
    }

    @Override
    public List<Map<String, Object>> getAlarmAndProcessByPlatformNow(Long userId,Long platformId) {
        //获取当前用户的平台信息
        AdminUserRespDTO currentUser = adminUserApi.getUser(userId).getData();
        List<Map> mapList = platformconfigApi.getPlatformListByUserId(String.valueOf(currentUser.getId())).getData();
        //过滤掉非选择的平台
        if(ObjectUtil.isNotEmpty(platformId)) {
            mapList = mapList.stream()
                    .filter(map -> map.containsKey("platformId") && Long.parseLong(String.valueOf(map.get("platformId"))) == platformId)
                    .collect(Collectors.toList());
        }
        List<Map<String, Object>> alarmByType = zjreportMapper.getAlarmByPlatform(mapList,platformId);
        List<Map<String, Object>> processByType = zjreportMapper.getProcessByPlatform(mapList,platformId);
        List<Map<String, Object>> mixMapList = new ArrayList<>();
        for (Map plat : mapList) {
            Map<String, Object> mixMap = new HashMap<>();
            mixMap.put("platformId", plat.get("platformId"));
            mixMap.put("platformName", plat.get("platformName"));
            mixMap.put("alarm", 0);
            mixMap.put("process", 0);
            for (Map alarm : alarmByType) {
                if (String.valueOf(alarm.get("platformId")).equals(String.valueOf(plat.get("platformId")))) {
                    mixMap.put("alarm", alarm.get("total"));
                }
            }
            for (Map process : processByType) {
                if (String.valueOf(process.get("platformId")).equals(String.valueOf(plat.get("platformId")))) {
                    mixMap.put("process", process.get("total"));
                }
            }
            mixMapList.add(mixMap);
        }
        return mixMapList;
    }
    @Override
    public List<Map<String, Object>> getAlarmTrigger(String timeCycle, Long userId,Long platformId,Boolean isYester) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String today = sdf.format(new Date());
        List<Map> mapList = platformconfigApi.getPlatformListByUserId(String.valueOf(userId)).getData();
        List<Map<String, Object>> resultList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(mapList)) {
            List<String> targetUuids = zjreportMapper.getUuidsByPlatform(mapList, platformId);
            if (CollectionUtil.isNotEmpty(targetUuids)) {
                if (isYester){
                    today=sdf.format(DateUtils.getYesterday());
                    resultList =  zjreportMapper.getAlarmTrigger(targetUuids, today, timeCycle);
                }else {
                    resultList =  zjreportMapper.getAlarmTrigger(targetUuids, today, timeCycle);
                }
            }
        }
        return resultList;
    }

    @Override
    public Map<String, Object> platformTrends(String time, Long userId,Long platformId) {
        List<Map> platformConfigList = platformconfigApi.getPlatformListByUserId(String.valueOf(userId)).getData();
        List<String> dates = "week".equals(time) ? DateUtils.getWeekDates() : DateUtils.getMonthDates();
        List<ZjReportPlatformRespVO> alertData = zjreportMapper.getPlatformAlertList(time, platformConfigList,platformId);
        List<ZjReportPlatformRespVO> bpmData = zjreportMapper.getPlatformBpmList(time, platformConfigList,platformId);

        Map<String, Map<String, Object>> platformNameMapping = platformConfigList.stream().collect(Collectors.toMap(config -> config.get("platformId").toString(), config -> config));

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("alert", processPlatformData(dates, alertData, platformNameMapping));
        resultMap.put("bpm", processPlatformData(dates, bpmData, platformNameMapping));
        resultMap.put("date", dates);

        return resultMap;
    }

    @Override
    public Map<String, Object> platformTrendsNow(String time, Long userId,Long platformId) {
        List<Map> platformConfigList = platformconfigApi.getPlatformListByUserId(String.valueOf(userId)).getData();
        List<String> dates = "week".equals(time) ? DateUtils.getLastWeekDates() : DateUtils.getLastMonthDates();
        List<ZjReportPlatformRespVO> alertData = zjreportMapper.getPlatformAlertList(time, platformConfigList,platformId);
        List<ZjReportPlatformRespVO> bpmData = zjreportMapper.getPlatformBpmList(time, platformConfigList,platformId);

        Map<String, Map<String, Object>> platformNameMapping = platformConfigList.stream().collect(Collectors.toMap(config -> config.get("platformId").toString(), config -> config));

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("alert", processPlatformData(dates, alertData, platformNameMapping));
        resultMap.put("bpm", processPlatformData(dates, bpmData, platformNameMapping));
        resultMap.put("date", dates);

        return resultMap;
    }

    public static void main(String[] args) {
        System.out.println(DateUtils.getLastWeekDates());
    }
    @Override
    public Map<String, Object> getGrowthTrendByType(String time, Long userId,Long platformId) {
        AdminUserRespDTO currentUser = adminUserApi.getUser(userId).getData();
        List<Map> mapList = platformconfigApi.getPlatformListByUserId(String.valueOf(userId)).getData();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(userId, singleton(CommonStatusEnum.ENABLE.getStatus()));
        Boolean isSuperAdmin = Boolean.valueOf(roleApi.decideAnySuperAdmin(roleIds));
        Map<String, Object> result = new HashMap<>();
        //获取日期列表
        List<String> dateList = "week".equals(time) ? DateUtils.getWeekDates() : DateUtils.getMonthDates();
        result.put("dateList", dateList);
        //枚举资源类型
        List<String> appTypeList = new ArrayList<>();
        appTypeList.add("host");
        appTypeList.add("hardware");
        appTypeList.add("storage");
        ResourceAppEnum[] values = ResourceAppEnum.values();
        for (ResourceAppEnum item : values) {
            appTypeList.add(item.getValue());
        }

        //为模板map赋值
        List<Map<String, Object>> alertGrowthMap = typeGroupCoulate(appTypeList, zjreportMapper.getAlertGrowthTrendByType(time, mapList,platformId), dateList);
        List<Map<String, Object>> processGrowthMap = typeGroupCoulate(appTypeList, zjreportMapper.getProcessGrowthTrendByType(mapList,time, isSuperAdmin, currentUser.getTenantId(),platformId), dateList);

        result.put("alertValueList", alertGrowthMap);
        result.put("processValueList", processGrowthMap);

        return result;
    }

    @Override
    public Map<String, Object> getGrowthTrendByTypeNow(String time, Long userId,Long platformId) {
        AdminUserRespDTO currentUser = adminUserApi.getUser(userId).getData();
        long state = zjreportMapper.getStateByTenantId(currentUser.getTenantId());
        List<Map> mapList = platformconfigApi.getPlatformListByUserId(String.valueOf(userId)).getData();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(userId, singleton(CommonStatusEnum.ENABLE.getStatus()));
        Boolean isSuperAdmin = Boolean.valueOf(roleApi.decideAnySuperAdmin(roleIds));
        Map<String, Object> result = new HashMap<>();
        //获取日期列表
        List<String> dateList = "week".equals(time) ? DateUtils.getLastWeekDates() : DateUtils.getLastMonthDates();
        result.put("dateList", dateList);
        //枚举资源类型
        List<String> appTypeList = new ArrayList<>();
        appTypeList.add("host");
        appTypeList.add("hardware");
        appTypeList.add("storage");
        ResourceAppEnum[] values = ResourceAppEnum.values();
        for (ResourceAppEnum item : values) {
            appTypeList.add(item.getValue());
        }

        //为模板map赋值
        List<Map<String, Object>> alertGrowthMap = typeGroupCoulate(appTypeList, zjreportMapper.getAlertGrowthTrendByType(time, mapList,platformId), dateList);
        List<Map<String, Object>> processGrowthMap = typeGroupCoulate(appTypeList, zjreportMapper.getProcessGrowthTrendByType(mapList,time, isSuperAdmin, currentUser.getTenantId(),platformId), dateList);

        // 如果 state 等于 1，过滤掉指定的指标
        if (state == 1) {
            // 定义需要过滤的指标名称列表
            List<String> filterApps = Arrays.asList("云主机", "宿主机", "云存储", "存储", "服务器", "网络");
            alertGrowthMap = alertGrowthMap.stream()
                    .filter(map -> !filterApps.contains(map.get("app")))
                    .collect(Collectors.toList());
            processGrowthMap = processGrowthMap.stream()
                    .filter(map -> !filterApps.contains(map.get("app")))
                    .collect(Collectors.toList());
        }

        result.put("alertValueList", alertGrowthMap);
        result.put("processValueList", processGrowthMap);
        return result;
    }

    @Override
    public List<Map<String, Object>> getAlarmTriggerWeek(String timeCycle, Long userId, Long platformId, boolean isYester) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String today = sdf.format(new Date());
        List<Map> mapList = platformconfigApi.getPlatformListByUserId(String.valueOf(userId)).getData();
        List<Map<String, Object>> resultList = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(mapList)) {
            List<String> targetUuids = zjreportMapper.getUuidsByPlatform(mapList, platformId);
            if (CollectionUtil.isNotEmpty(targetUuids)) {
                // 确定日期
                if (isYester) {
                    if (timeCycle.equals("week")) {
                        today = sdf.format(DateUtils.getLastWeek());
                    } else if (timeCycle.equals("month")) {
                        today = sdf.format(DateUtils.getFirstDayOfLastMonth());
                    } else {
                        today = sdf.format(DateUtils.getYesterday());
                    }
                }

                // 获取结果
                resultList = zjreportMapper.getAlarmTrigger(targetUuids, today, timeCycle);
            }
        }
        return resultList;
    }

    @Override
    public Map<String, List<ZjReportProcessRespVO>> processtop(String time, Long userId, Long platformId) {
        List<Map> mapList = platformconfigApi.getPlatformListByUserId(String.valueOf(userId)).getData();
        List<ZjReportProcessRespVO> zjReportProcessRespVOS = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(mapList)) {
            List<Map> list = zjreportMapper.getMonitorList(mapList, platformId);
            List<Map> bpm = zjreportMapper.listBpmProcessTopList(time, mapList);

            Map<Object, Map> listMap = list.stream()
                    .collect(Collectors.toMap(item -> item.get("uuid"), item -> item,(existing, replacement) -> existing));

            if(CollectionUtil.isNotEmpty(bpm)){
                for (Map map : bpm) {
                    Map matchedList = listMap.get(map.get("sourceId"));
                    if (matchedList != null) {
                        ZjReportProcessRespVO zjReportProcessRespVO = new ZjReportProcessRespVO();
                        zjReportProcessRespVO.setName(matchedList.get("name").toString());
                        zjReportProcessRespVO.setPlatformName(matchedList.get("platformName").toString());
                        zjReportProcessRespVO.setTicketsCount(Convert.toLong(map.get("count")));
                        zjReportProcessRespVOS.add(zjReportProcessRespVO);
                    }
                }
            }
        }

        List<ZjReportProcessRespVO> alert = zjreportMapper.getAlertTopList(time, mapList, platformId);
        Map<String, List<ZjReportProcessRespVO>> resultMap = new HashMap<>();
        resultMap.put("alert", alert);
        resultMap.put("bpm", zjReportProcessRespVOS);
        return resultMap;
    }

    @Override
    public Map<String, List<ZjReportProcessRespVO>> processtopYest(String time, Long userId, Long platformId) {
        List<Map> mapList = platformconfigApi.getPlatformListByUserId(String.valueOf(userId)).getData();
        List<ZjReportProcessRespVO> zjReportProcessRespVOS = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(mapList)) {
            List<Map> list = zjreportMapper.getMonitorList(mapList, platformId);
            List<Map> bpm = zjreportMapper.listBpmProcessTopList(time, mapList);

            Map<Object, Map> listMap = list.stream()
                    .collect(Collectors.toMap(item -> item.get("uuid"), item -> item,(existing, replacement) -> existing));

            if(CollectionUtil.isNotEmpty(bpm)){
                for (Map map : bpm) {
                    Map matchedList = listMap.get(map.get("sourceId"));
                    if (matchedList != null) {
                        ZjReportProcessRespVO zjReportProcessRespVO = new ZjReportProcessRespVO();
                        zjReportProcessRespVO.setName(matchedList.get("name").toString());
                        zjReportProcessRespVO.setPlatformName(matchedList.get("platformName").toString());
                        zjReportProcessRespVO.setTicketsCount(Convert.toLong(map.get("count")));
                        zjReportProcessRespVOS.add(zjReportProcessRespVO);
                    }
                }
            }
        }

        List<ZjReportProcessRespVO> alert = zjreportMapper.getAlertTopListYest(time, mapList, platformId);
        Map<String, List<ZjReportProcessRespVO>> resultMap = new HashMap<>();
        resultMap.put("alert", alert);
        resultMap.put("bpm", zjReportProcessRespVOS);
        return resultMap;
    }

    private String getResourceLabel(String app) {
        for (ResourceCategoryEnum resource : ResourceCategoryEnum.values()) {
            if (resource.getCode().equals(app)) {
                return resource.getName();
            }
            // 检查是否包含在当前大类的枚举列表中
            if (resource.getSubEnums() != null && resource.getSubEnums().stream().anyMatch(subEnum -> subEnum.getValue().equals(app))) {
                return resource.getName();
            }
        }
        return null; // 如果没有匹配的枚举值，返回null或者其他默认值
    }
    private List<Map<String, Object>> supplementType(List<String> appTypeList, List<Map<String, Object>> mapList) {
        Integer size = mapList.size();
        for (String app : appTypeList) {
            boolean exist = false;
            if (size > 0) {
                for (int i = 0; i < size; i++) {
                    if (mapList.get(i).get("app").equals(app)) {
                        exist = true;
                        break;
                    }
                }
                if (!exist) {
                    Map<String, Object> alarmMap = new HashMap<>();
                    alarmMap.put("app", app);
                    alarmMap.put("total", 0);
                    mapList.add(alarmMap);
                }
            } else {
                Map<String, Object> alarmMap = new HashMap<>();
                alarmMap.put("app", app);
                alarmMap.put("total", 0);
                mapList.add(alarmMap);
            }
        }
        return mapList;
    }
    private Map<String, List<String>> defineTypeMa() {
        Map<String, List<String>> appTypeMap = new HashMap<>();
        appTypeMap.put(ResourceCategoryEnum.APP_SERVICE.getCode(), Arrays.asList("website", "port", "ssl_cert", "ping", "websocket", "api", "dns", "fullsite", "api_code", "udp_port"));
        appTypeMap.put(ResourceCategoryEnum.APP_DB.getCode(), Arrays.asList("mysql", "oracle", "sqlserver", "mariadb", "dm", "redis", "redis_cluster", "redis_sentinel", "memcached", "tidb", "nebulaGraph", "opengauss", "mongodb"));
        appTypeMap.put(ResourceCategoryEnum.APP_MID.getCode(), Arrays.asList("nacos", "zookeeper", "kafka", "rabbitmq", "nginx", "pop3", "ftp", "smtp", "ntp", "spring_gateway", "emqx", "activemq", "rocketmq", "shenyu", "rabbitmq", "almalinux"));
        appTypeMap.put(ResourceCategoryEnum.APP_OS.getCode(), Arrays.asList("windows", "linux", "ubuntu", "coreos", "opensuse", "rockylinux", "euleros", "redhat", "centos", "freebsd", "debian"));
        appTypeMap.put(ResourceCategoryEnum.APP_CN.getCode(), Arrays.asList("docker", "kubernetes"));
//        appTypeMap.put(ResourceCategoryEnum.APP_PROMETHEUS.getCode(), Collections.singletonList("prometheus"));
        appTypeMap.put(ResourceCategoryEnum.APP_FIREWALL.getCode(), Arrays.asList("qax_firewall", "sswk_firewall", "sswk_defense", "sxf_firewall","ad_firewall","ac_firewall","ah_firewall", "ah_waf", "lm_defense", "lm_waf", "trx_defense", "trx_firewall"));
        appTypeMap.put(ResourceCategoryEnum.APP_NETWORK.getCode(), Arrays.asList("h3c_switch", "cisco_switch", "huawei_switch", "tplink_switch", "hpe_switch"));
        appTypeMap.put(ResourceCategoryEnum.APP_STORED.getCode(), Arrays.asList("storage_huawei", "storage_inspur", "storage_synology"));
        appTypeMap.put(ResourceCategoryEnum.APP_INFRA.getCode(), Arrays.asList("infra_ipmi"));
        appTypeMap.put(ResourceCategoryEnum.APP_HOST.getCode(), Arrays.asList("host"));
        appTypeMap.put(ResourceCategoryEnum.APP_HARDWARE.getCode(), Arrays.asList("hardware"));
        appTypeMap.put(ResourceCategoryEnum.APP_STORAGE.getCode(), Arrays.asList("storage"));
        return appTypeMap;
    }
    private List<Map<String, Object>> processPlatformData(List<String> dates, List<ZjReportPlatformRespVO> platformData, Map<String, Map<String, Object>> platformNameMapping) {

        // 如果 platformData 为空或者尺寸为0
        if (platformData == null || platformData.isEmpty()) {
            // 构建并返回默认platformMap列表，每个platformMap的count都是0
            return platformNameMapping.entrySet().stream().map(entry -> {
                Map<String, Object> platformMap = new HashMap<>();
                platformMap.put("platformId", entry.getKey());
                platformMap.put("platformName", entry.getValue().get("platformName"));
                platformMap.put("date", dates);
                // 注意，这里我们将count置为一个所有值都为0的列表，列表的长度与dates相同
                List<Integer> counts = dates.stream().map(date -> 0).collect(Collectors.toList());
                platformMap.put("count", counts);
                return platformMap;
            }).collect(Collectors.toList());
        }

        return platformData.stream().collect(Collectors.groupingBy(ZjReportPlatformRespVO::getPlatformId)).entrySet().stream().map(entry -> {
            Map<String, Object> platformMap = new HashMap<>();
            platformMap.put("platformId", entry.getKey());
            platformMap.put("platformName", platformNameMapping.get(entry.getKey()).get("platformName"));
            platformMap.put("date", dates);
            platformMap.put("count", calculateCounts(entry.getValue(), dates));
            return platformMap;
        }).collect(Collectors.toList());
    }
    private List<Long> calculateCounts(List<ZjReportPlatformRespVO> platformVOs, List<String> dates) {
        return dates.stream().map(date -> platformVOs.stream().filter(vo -> DateUtil.format(vo.getDate(), "yyyy-MM-dd").equals(date)).mapToLong(ZjReportPlatformRespVO::getCount).sum()).collect(Collectors.toList());
    }
    private List<Map<String, Object>> typeGroupCoulate(List<String> appTypeList, List<Map<String, Object>> alertGrowthMap, List<String> dateList) {
        //建立模板map
        List<Map<String, Object>> zeroGrowthMapList = new ArrayList<>();
        dateList.forEach(date -> {
            appTypeList.forEach(app -> {
                Map<String, Object> zeroGrowthMap = new HashMap<>();
                zeroGrowthMap.put("dateStr", date);
                zeroGrowthMap.put("app", app);
                zeroGrowthMap.put("total", 0);
                zeroGrowthMapList.add(zeroGrowthMap);
            });
        });
        zeroGrowthMapList.forEach(zeroGrowthMap -> {
            alertGrowthMap.forEach(item -> {
                if (zeroGrowthMap.get("dateStr").equals(item.get("dateStr")) && zeroGrowthMap.get("app").equals(item.get("app"))) {
                    zeroGrowthMap.put("total", item.get("total"));
                }
            });
        });

        //按日期分组，并将小类归并到大类
        Map<String, List<String>> appTypeMap = defineTypeMa();
        List<Map<String, Object>> dateGroupList = new ArrayList<>();
        zeroGrowthMapList.stream().collect(Collectors.groupingBy(map -> map.get("dateStr"))).forEach((k, v) -> {
            for (Map.Entry<String, List<String>> entry : appTypeMap.entrySet()) {
                Map<String, Object> maps = new HashMap();
                String label = getResourceLabel(entry.getKey());
                List<String> apps = entry.getValue();
                // 遍历当前类型的每个应用
                Long total = 0L;
                for (String app : apps) {
                    for (Map map : v) {
                        if (app.equals(map.get("app"))) {
                            total = total + Convert.toLong(map.get("total"));
                        }
                    }
                }
                maps.put("app", label);
                maps.put("total", total);
                maps.put("dateStr", k);
                dateGroupList.add(maps);
            }
        });

        //按类型分组，获取每个大类每天的count数
        List<Map<String, Object>> appGroupList = dateGroupList.stream().collect(Collectors.groupingBy(map -> map.get("app"))).entrySet().stream().map(entry -> {
            Map<String, Object> platformMap = new HashMap<>();
            platformMap.put("app", entry.getKey());
            platformMap.put("date", dateList);
            List<Long> total = dateList.stream().map(date -> entry.getValue().stream().filter(vo -> vo.get("dateStr").equals(date)).mapToLong(vo -> Convert.toLong(vo.get("total"))).sum()).collect(Collectors.toList());
            ;
            platformMap.put("total", total);
            return platformMap;
        }).collect(Collectors.toList());

        return appGroupList;
    }
}
