package cn.iocoder.zj.module.report.api;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.report.api.subscription.dto.ReportSubscriptionApi;
import cn.iocoder.zj.module.report.api.subscription.dto.ReportSubscriptionDTO;
import cn.iocoder.zj.module.report.service.reporsubscription.ReportSubscriptionService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.util.List;

import static cn.iocoder.zj.module.report.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION)
@Validated
public class ReportSubscriptionApiImpl implements ReportSubscriptionApi {

    @Resource
    ReportSubscriptionService reportSubscriptionService;

    @Override
    @TenantIgnore
    public CommonResult<List<ReportSubscriptionDTO>> getAlarmSubscription() {
        return  CommonResult.success(reportSubscriptionService.getAlarmSubscription());
    }

    @Override
    public CommonResult<Boolean> removeTimedTask(Long userId) {
        reportSubscriptionService.removeTimedTask(userId);
        return CommonResult.success(true);
    }
}
