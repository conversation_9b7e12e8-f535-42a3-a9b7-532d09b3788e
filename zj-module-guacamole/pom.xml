<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>cn.iocoder.cloud</groupId>
        <artifactId>zj</artifactId>
        <version>${revision}</version>
    </parent>
    <modules>
        <module>zj-module-guacamole-biz</module>
        <module>zj-module-guacamole-api</module>
    </modules>


    <modelVersion>4.0.0</modelVersion>

    <artifactId>zj-module-guacamole</artifactId>
    <packaging>pom</packaging>

    <name>${project.artifactId}</name> <!-- 3. 新增 name 为 ${project.artifactId} -->

    <description> <!-- 4. 新增 description 为该模块的描述 -->
        堡垒机 模块，主要xxx 等功能。
    </description>



</project>