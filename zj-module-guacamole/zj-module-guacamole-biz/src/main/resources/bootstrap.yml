# Tomcat
server:
  port: 48091

# Spring
spring:
  main:
    allow-circular-references: true  # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许 Bean 覆盖，例如说 Dubbo 或者 Feign 等会存在重复定义的服务
  application:
    # 应用名称
    name: guacamole-server
  profiles:
    # 环境配置
    active: prod
  cloud:
    nacos:
      discovery:
        namespace: ${spring.profiles.active} # 命名空间。这里使用 dev 开发环境
        # 服务注册地址
        server-addr: nacos-production.zj-cloud:8848
      config:
        # 配置中心地址
        server-addr: nacos-production.zj-cloud:8848
        namespace: ${spring.profiles.active} # 命名空间。这里使用 dev 开发环境
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP
        name: ${spring.application.name}-${spring.profiles.active} # 使用的 Nacos 配置集的 dataId，默认为 spring.application.name
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - data-id: application.yml
            group: DEFAULT_GROUP
            refresh: true # 是否自动刷新配置，默认为 false
logging:
  file:
    name: ${user.home}/logs/${spring.application.name}.log # 日志文件名，全路径

guacamole_server_host: yxc-modules-osm
guacamole_server_port: 4822



upload:
  filePath: /home/<USER>/file/
  minioFilePath: file/guacamole/
  videoPath: /home/<USER>/video/

proxy:
  url: http://*************:48092
  ip: *************