<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.guacamole.dal.mysql.onlinesession.OnlineSessionMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <update id="updateStatusByUuid">
        UPDATE guacamole_online_session
        SET status = "disconnected",
            disconnected_time = #{disconnectedTime}
        WHERE id = #{uuid,jdbcType=VARCHAR}
    </update>

    <update id="updateAssetByUuid">
        UPDATE om_monitor_asset
        SET last_access_time = #{disconnectedTime},
            authorization_type = 0
        WHERE id = #{id}
    </update>

    <update id="updateHostByUuid">
        UPDATE monitor_host_info
        SET last_access_time = #{disconnectedTime},
            authorization_type = 0
        WHERE id = #{id}
    </update>

    <select id="selectNameByPlatformId" resultType="java.lang.String">
        select name from hzb_collector where platform_id = #{platformId} limit 1
    </select>

    <select id="selectIdByName" resultType="java.lang.Long">
        select id from license where name = #{name}
    </select>

    <select id="selectPordMappingIdByUuid" resultType="java.lang.Long">
        select port_mapping_id from guacamole_online_session where id = #{uuid}
    </select>

    <delete id="deleteAssetByUserId">
        DELETE FROM om_monitor_authorization_user
        WHERE monitor_asset_id = #{monitorAssetId} and authorization_user_id = #{userId}
    </delete>

    <select id="selectNamesByPlatformId" resultType="java.lang.String">
        select collector_name from hzb_collector_platform where platform_id = #{platformId} limit 1
    </select>
</mapper>
