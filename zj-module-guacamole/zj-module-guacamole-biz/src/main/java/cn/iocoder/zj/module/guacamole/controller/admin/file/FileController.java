package cn.iocoder.zj.module.guacamole.controller.admin.file;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.extra.ssh.JschUtil;
import cn.hutool.extra.ssh.Sftp;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.guacamole.controller.admin.file.vo.*;
import cn.iocoder.zj.module.guacamole.framework.minio.util.MinioUtil;
import cn.iocoder.zj.module.guacamole.framework.rpc.ws.config.GuacamoleService;
import cn.iocoder.zj.module.guacamole.service.ws.GuacamoleTunnelSocket;
import cn.iocoder.zj.module.guacamole.utli.SFTPUtil;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.guacamole.protocol.ConfiguredGuacamoleSocket;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.error;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;


/**
 * @ClassName : FileController  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/6/24  15:28
 */

@Tag(name = "堡垒机 - 会话详情")
@RestController
@RequestMapping("/guacamole/file")
@Validated
public class FileController {

    private static final Logger logger = LoggerFactory.getLogger(FileController.class);

    @Autowired
    GuacamoleService guacamoleService;

    @Value("${upload.minioFilePath}")
    private String filePath;

    @Autowired
    SFTPUtil sftpUtil;

    @Resource
    private MinioUtil minioUtil;

    @Value("${minio.bucketName}")
    private String minioBucketName;


    @PostMapping("/tunnels/sessions/ls")
    @Operation(summary = "文件列表")
    public CommonResult<List<FileRespVO>> ls(HttpServletResponse response, @RequestBody FilelsReqVO filelsReqVO) {
        List<FileRespVO> list = new ArrayList<>();
        if (filelsReqVO.getProtocol().equals("ssh")) {
            ConfiguredGuacamoleSocket guacamoleSocket = guacamoleService.tunnels.get(filelsReqVO.getSnid());
            Map<String, String> map = guacamoleSocket.getConfiguration().getParameters();
            String username = map.get("username");
            String hostname = map.get("hostname");
            int port = Convert.toInt(map.get("port"));
            String password = map.get("password");
            Session session = JschUtil.createSession(hostname, port, username, password);
            Sftp sftp = JschUtil.createSftp(session);
            try {
                List<ChannelSftp.LsEntry> lsEntryList = sftp.lsEntries(filelsReqVO.getDir());
                for (ChannelSftp.LsEntry lsEntry : lsEntryList) {
                    FileRespVO fileRespVO = new FileRespVO();
                    fileRespVO.setName(lsEntry.getFilename());
                    fileRespVO.setPath(filelsReqVO.getDir() + "/" + lsEntry.getFilename());
                    fileRespVO.setSize(lsEntry.getAttrs().getSize());
                    fileRespVO.setMode(lsEntry.getAttrs().getPermissionsString());
                    fileRespVO.setDir(lsEntry.getAttrs().isDir());
                    fileRespVO.setModTime(DateUtil.date(DateUtil.parse(lsEntry.getAttrs().getMtimeString()).getTime()));
                    fileRespVO.setLink(lsEntry.getAttrs().isLink());
                    list.add(fileRespVO);
                }
            } catch (Exception e) {
                JschUtil.close(session);
                return error(-1, e.getMessage());
            }

            JschUtil.close(session);
        } else {
            LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
            String tenantId = String.valueOf(loginUser.getTenantId());
            String dir = filelsReqVO.getDir();
            String prefix = filePath + tenantId + (dir.startsWith("/") ? dir : "/" + dir);
            if (!prefix.endsWith("/")) prefix += "/";
            try {
                List<io.minio.messages.Item> items = minioUtil.listObjects(prefix);
                for (io.minio.messages.Item item : items) {
                    String objectName = item.objectName();
                    if (!objectName.startsWith(prefix) || objectName.equals(prefix)) continue;
                    String relativePath = objectName.substring(prefix.length());
                    if (relativePath.contains("/")) {
                        // 只列出当前目录下的文件和文件夹
                        String first = relativePath.split("/")[0];
                        if (list.stream().noneMatch(f -> f.getName().equals(first) && f.isDir())) {
                            FileRespVO vo = new FileRespVO();
                            vo.setName(first);
                            vo.setPath((dir.endsWith("/") ? dir : dir + "/") + first);
                            vo.setDir(true);
                            vo.setSize(0L);
                            vo.setMode("drwxr-xr-x");
                            vo.setModTime(null);
                            vo.setLink(false);
                            list.add(vo);
                        }
                        continue;
                    }
                    FileRespVO vo = new FileRespVO();
                    vo.setName(relativePath);
                    vo.setPath((dir.endsWith("/") ? dir : dir + "/") + relativePath);
                    vo.setDir(false);
                    vo.setSize(item.size());
                    vo.setMode("-rw-r--r--");
                    vo.setModTime(item.lastModified() != null ? Date.from(item.lastModified().toInstant()) : null);
                    vo.setLink(false);
                    list.add(vo);
                }
            } catch (Exception e) {
                return error(-1, e.getMessage());
            }
        }
        return success(list);
    }

    @GetMapping("/tunnels/sessions/download")
    @Operation(summary = "下载文件")
    public void download(@Parameter(description = "session id | session id", example = "6565463543") @RequestParam("snid") String snid,
                         @Parameter(description = "文件名称", example = "6565463543") @RequestParam("fileName") String fileName,
                         @Parameter(description = "服务器的下载文件路径", example = "/root/admin.txt") @RequestParam("remotePath") String remotePath,
                         HttpServletResponse response,
                         HttpServletRequest request,
                         @Parameter(description = "协议类型", example = "ssh or rdp") @RequestParam("protocol") String protocol) throws IOException {
        if (protocol.equals("ssh")) {
            ConfiguredGuacamoleSocket guacamoleSocket = GuacamoleTunnelSocket.getTunnel(snid);
            Map<String, String> map = guacamoleSocket.getConfiguration().getParameters();
            String username = map.get("username");
            String hostname = map.get("hostname");
            int port = Convert.toInt(map.get("port"));
            String password = map.get("password");
            Session session = JschUtil.createSession(hostname, port, username, password);
            Sftp sftp = JschUtil.createSftp(session);
            sftp.download(remotePath, response.getOutputStream());
            JschUtil.close(session);
        } else {
            LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
            String tenantId = String.valueOf(loginUser.getTenantId());
            String objectName = filePath + tenantId + (remotePath.startsWith("/") ? remotePath : "/" + remotePath);
            try (InputStream is = minioUtil.getObject(minioBucketName, objectName)) {
                response.reset();
                String userAgent = request.getHeader("USER-AGENT").toLowerCase();
                String downloadName = fileName;
                if (userAgent.contains("firefox")) {
                    downloadName = new String(fileName.getBytes(), "ISO8859-1");
                } else {
                    downloadName = URLEncoder.encode(fileName, "UTF-8");
                }
                response.setContentType("application/download");
                response.setHeader("Content-disposition", "attachment;filename=" + downloadName);
                byte[] buffer = new byte[1024];
                int len;
                OutputStream os = response.getOutputStream();
                while ((len = is.read(buffer)) != -1) {
                    os.write(buffer, 0, len);
                }
                os.flush();
            } catch (Exception e) {
                response.sendError(500, "MinIO 下载失败: " + e.getMessage());
            }
        }


    }

    @PostMapping("/tunnels/sessions/upload")
    @Operation(summary = "上传文件")
    public CommonResult<Boolean> uploadFile(FileuploadReqVO fileReq,
                                            MultipartFile file) throws IOException {
        boolean isSuccess = false;
        if (fileReq.getProtocol().equals("ssh")) {
            ConfiguredGuacamoleSocket guacamoleSocket = GuacamoleTunnelSocket.getTunnel(fileReq.getSnid());
            Map<String, String> map = guacamoleSocket.getConfiguration().getParameters();
            String username = map.get("username");
            String hostname = map.get("hostname");
            int port = Convert.toInt(map.get("port"));
            String password = map.get("password");

            Session session = null;
            Sftp sftp = null;
            InputStream inputStream = null;

            try {
                session = JschUtil.createSession(hostname, port, username, password);
                sftp = JschUtil.createSftp(session);
                String path = fileReq.getTargetPath().replaceAll("//", "/");
                logger.info("path===============>文件路径" + path);
                logger.info("path===============>文件路径全名称" + path + file.getOriginalFilename());
                String uploadFileName = FileUtil.getName(file.getOriginalFilename());
                logger.info("path===============>文件全路径" + path + uploadFileName);
                if (!sftp.exist(fileReq.getTargetPath())) {
                    sftp.mkdir(fileReq.getTargetPath());
                }
                sftp.upload(path, uploadFileName, file.getInputStream());
                isSuccess = true;
            } catch (Exception e) {
                e.printStackTrace();
                isSuccess = false;
            } finally {
                if (sftp != null) {
                    sftp.close();
                }
                if (session != null) {
                    JschUtil.close(session);
                }
                if (inputStream != null) {
                    inputStream.close();
                }
            }
        } else {
            LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
            String tenantId = String.valueOf(loginUser.getTenantId());
            String targetPath = fileReq.getTargetPath();
            String objectName = filePath + tenantId + (targetPath.startsWith("/") ? targetPath : "/" + targetPath);
            if (!objectName.endsWith("/")) objectName += "/";
            objectName += file.getOriginalFilename();
            try {
                minioUtil.putObject(minioBucketName, objectName, file.getInputStream());
                isSuccess = true;
            } catch (Exception e) {
                return error(-1, "MinIO 上传失败: " + e.getMessage());
            }
        }
        return success(isSuccess);
    }


    @PostMapping("/tunnels/sessions/rm")
    @Operation(summary = "删除文件")
    public CommonResult<Boolean> rm(@RequestBody FilermReqVO filermReqVO) throws IOException {
        boolean d = false;
        if (filermReqVO.getProtocol().equals("ssh")) {
            ConfiguredGuacamoleSocket guacamoleSocket = GuacamoleTunnelSocket.getTunnel(filermReqVO.getSnid());
            Map<String, String> map = guacamoleSocket.getConfiguration().getParameters();
            String username = map.get("username");
            String hostname = map.get("hostname");
            int port = Convert.toInt(map.get("port"));
            String password = map.get("password");
            Session session = JschUtil.createSession(hostname, port, username, password);
            Sftp sftp = JschUtil.createSftp(session);
            boolean file = sftp.isDir(filermReqVO.getTargetPath());
            if (!file) {
                d = sftp.delFile(filermReqVO.getTargetPath());
                if (d) {
                    JschUtil.close(session);
                }
                return success(d);
            } else {
                d = sftp.delDir(filermReqVO.getTargetPath());
                if (d) {
                    JschUtil.close(session);
                }
                return success(d);
            }

        } else {
            LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
            String tenantId = String.valueOf(loginUser.getTenantId());
            String objectName = filePath + tenantId + (filermReqVO.getTargetPath().startsWith("/") ? filermReqVO.getTargetPath() : "/" + filermReqVO.getTargetPath());
            try {
                d = minioUtil.remove(objectName);
            } catch (Exception e) {
                return error(-1, "MinIO 删除失败: " + e.getMessage());
            }
            return success(d);
        }

    }

    @PostMapping("/tunnels/sessions/mkdir")
    @Operation(summary = "创建文件")
    public CommonResult<Boolean> mkdir(@RequestBody FilemkdirVO fileReqVO) throws IOException {
        boolean d = false;
        if (fileReqVO.getProtocol().equals("ssh")) {
            ConfiguredGuacamoleSocket guacamoleSocket = GuacamoleTunnelSocket.getTunnel(fileReqVO.getSnid());
            Map<String, String> map = guacamoleSocket.getConfiguration().getParameters();
            String username = map.get("username");
            String hostname = map.get("hostname");
            int port = Convert.toInt(map.get("port"));
            String password = map.get("password");
            Session session = JschUtil.createSession(hostname, port, username, password);
            Sftp sftp = JschUtil.createSftp(session);
            d = sftp.mkdir(fileReqVO.getDir());
            if (d) {
                JschUtil.close(session);
            }
        } else {
            LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
            String tenantId = String.valueOf(loginUser.getTenantId());
            String dir = fileReqVO.getDir();
            // 目录对象名以 / 结尾
            String objectName = filePath + tenantId + (dir.startsWith("/") ? dir : "/" + dir);
            if (!objectName.endsWith("/")) {
                objectName += "/";
            }
            try {
                minioUtil.putObject(minioBucketName, objectName, new ByteArrayInputStream(new byte[0]));
                d = true;
            } catch (Exception e) {
                return error(-1, "MinIO 创建目录失败: " + e.getMessage());
            }
        }
        return success(d);
    }


    @PostMapping("/tunnels/sessions/rename")
    @Operation(summary = "重命名文件")
    public CommonResult<Boolean> rename(@RequestBody FilerenameReqVO fileReqVO) throws IOException, SftpException {
        if (fileReqVO.getProtocol().equals("ssh")) {
            ConfiguredGuacamoleSocket guacamoleSocket = GuacamoleTunnelSocket.getTunnel(fileReqVO.getSnid());
            Map<String, String> map = guacamoleSocket.getConfiguration().getParameters();
            String username = map.get("username");
            String hostname = map.get("hostname");
            int port = Convert.toInt(map.get("port"));
            String password = map.get("password");
            Session session = JschUtil.createSession(hostname, port, username, password);
            Sftp sftp = JschUtil.createSftp(session);
            ChannelSftp channelSftp = sftp.getClient();
            channelSftp.rename(fileReqVO.getOldName(), fileReqVO.getNewName());
            JschUtil.close(session);
        } else {
            LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
            String tenantId = String.valueOf(loginUser.getTenantId());
            String oldObjectName = filePath + tenantId + (fileReqVO.getOldName().startsWith("/") ? fileReqVO.getOldName() : "/" + fileReqVO.getOldName());
            String newObjectName = filePath + tenantId + (fileReqVO.getNewName().startsWith("/") ? fileReqVO.getNewName() : "/" + fileReqVO.getNewName());
            try (InputStream is = minioUtil.getObject(minioBucketName, oldObjectName)) {
                minioUtil.putObject(minioBucketName, newObjectName, is);
                minioUtil.remove(oldObjectName);
            } catch (Exception e) {
                return error(-1, "MinIO 重命名失败: " + e.getMessage());
            }
        }
        return success(true);
    }


    @PostMapping("/tunnels/sessions/edit")
    @Operation(summary = "编辑文件")
    public CommonResult<Boolean> edit(@RequestBody FileeditReqVO fileReqVO) throws IOException, SftpException {
        if (fileReqVO.getProtocol().equals("ssh")) {
            ConfiguredGuacamoleSocket guacamoleSocket = GuacamoleTunnelSocket.getTunnel(fileReqVO.getSnid());
            Map<String, String> map = guacamoleSocket.getConfiguration().getParameters();
            String username = map.get("username");
            String hostname = map.get("hostname");
            int port = Convert.toInt(map.get("port"));
            String password = map.get("password");
            Session session = JschUtil.createSession(hostname, port, username, password);
            Sftp sftp = JschUtil.createSftp(session);

            ChannelSftp channelSftp = sftp.getClient();
            InputStream inputStream = channelSftp.get(fileReqVO.getFile());
            InputStream editedInputStream = new ByteArrayInputStream(fileReqVO.getFileContent().getBytes(StandardCharsets.UTF_8));
            channelSftp.put(editedInputStream, fileReqVO.getFile(), ChannelSftp.OVERWRITE);
            JschUtil.close(session);
        } else {
            LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
            String tenantId = String.valueOf(loginUser.getTenantId());
            String objectName = filePath + tenantId + (fileReqVO.getFile().startsWith("/") ? fileReqVO.getFile() : "/" + fileReqVO.getFile());
            try (InputStream is = new ByteArrayInputStream(fileReqVO.getFileContent().getBytes(StandardCharsets.UTF_8))) {
                minioUtil.putObject(minioBucketName, objectName, is);
            } catch (Exception e) {
                return error(-1, "MinIO 编辑失败: " + e.getMessage());
            }
        }
        return success(true);
    }


}
