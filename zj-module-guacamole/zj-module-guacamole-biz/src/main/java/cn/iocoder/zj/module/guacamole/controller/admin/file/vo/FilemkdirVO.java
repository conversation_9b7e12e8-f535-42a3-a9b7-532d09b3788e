package cn.iocoder.zj.module.guacamole.controller.admin.file.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName : FilemkdirVO  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/7/12  11:13
 */
@Data
public class FilemkdirVO {
    @Schema(description = "session id", example = "1")
    private String snid;
    @Schema(description = "协议类型", example = "ssh or rdp")
    private String protocol;
    @Schema(description = "文件路径 创建操作需传值")
    private String dir;
}
