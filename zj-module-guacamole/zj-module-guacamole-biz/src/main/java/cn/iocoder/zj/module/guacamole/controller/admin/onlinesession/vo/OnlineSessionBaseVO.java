package cn.iocoder.zj.module.guacamole.controller.admin.onlinesession.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 会话审计 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class OnlineSessionBaseVO {

    @Schema(description = "来源ip")
    private String clientIp;

    @Schema(description = "资产id")
    private String assetId;

    @Schema(description = "资产名称")
    private String assetName;

    @Schema(description = "连接协议")
    private String protocol;

    @Schema(description = "ip地址")
    private String ip;

    @Schema(description = "端口号")
    private String port;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "接入时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime connectedTime;

    @Schema(description = "连接id")
    private String connectionId;

    @Schema(description = "高")
    private String height;

    @Schema(description = "宽")
    private String width;

    @Schema(description = "用户名称")
    private String userName;

    @Schema(description = "在线状态")
    private String status;

    @Schema(description = "文件地址")
    private String filePath;

    @Schema(description = "断开时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime disconnectedTime;

}
