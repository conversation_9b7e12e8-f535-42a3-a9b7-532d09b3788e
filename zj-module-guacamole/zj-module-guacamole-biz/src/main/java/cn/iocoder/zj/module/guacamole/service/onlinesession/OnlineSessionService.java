package cn.iocoder.zj.module.guacamole.service.onlinesession;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.guacamole.controller.admin.onlinesession.vo.OnlineSessionCreateReqVO;
import cn.iocoder.zj.module.guacamole.controller.admin.onlinesession.vo.OnlineSessionExportReqVO;
import cn.iocoder.zj.module.guacamole.controller.admin.onlinesession.vo.OnlineSessionPageReqVO;
import cn.iocoder.zj.module.guacamole.controller.admin.onlinesession.vo.OnlineSessionUpdateReqVO;
import cn.iocoder.zj.module.guacamole.dal.dataobject.onlinesession.OnlineSessionDO;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

/**
 * 会话审计 Service 接口
 *
 * <AUTHOR>
 */
public interface OnlineSessionService {

    /**
     * 创建会话审计
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createOnlineSession(@Valid OnlineSessionCreateReqVO createReqVO);

    /**
     * 更新会话审计
     *
     * @param updateReqVO 更新信息
     */
    void updateOnlineSession(@Valid OnlineSessionUpdateReqVO updateReqVO);

    /**
     * 删除会话审计
     *
     * @param id 编号
     */
    void deleteOnlineSession(String id);

    /**
     * 获得会话审计
     *
     * @param id 编号
     * @return 会话审计
     */
    OnlineSessionDO getOnlineSession(String id);

    /**
     * 获得会话审计列表
     *
     * @param ids 编号
     * @return 会话审计列表
     */
    List<OnlineSessionDO> getOnlineSessionList(Collection<String> ids);

    /**
     * 获得会话审计分页
     *
     * @param pageReqVO 分页查询
     * @return 会话审计分页
     */
    PageResult<OnlineSessionDO> getOnlineSessionPage(OnlineSessionPageReqVO pageReqVO);

    /**
     * 获得会话审计列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 会话审计列表
     */
    List<OnlineSessionDO> getOnlineSessionList(OnlineSessionExportReqVO exportReqVO);

    PageResult<OnlineSessionDO> getOnlineSessionVideoPage(OnlineSessionPageReqVO pageVO);

    String uploadVideo(MultipartFile file) throws IOException;

    String selectNameByPlatformId(Long platformId);

    Long selectIdByName(String name);


}
