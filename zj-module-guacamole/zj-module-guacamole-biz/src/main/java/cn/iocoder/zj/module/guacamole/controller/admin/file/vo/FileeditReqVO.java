package cn.iocoder.zj.module.guacamole.controller.admin.file.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName : FileeditReqVO  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/7/12  11:14
 */
@Data
public class FileeditReqVO {
    @Schema(description = "session id", example = "1")
    private String snid;
    @Schema(description = "协议类型", example = "ssh or rdp")
    private String protocol;
    @Schema(description = "文件内容")
    private String fileContent;
    @Schema(description = "文件名",example = "/t_manage_person_visitor3.sql")
    private String file;
}
