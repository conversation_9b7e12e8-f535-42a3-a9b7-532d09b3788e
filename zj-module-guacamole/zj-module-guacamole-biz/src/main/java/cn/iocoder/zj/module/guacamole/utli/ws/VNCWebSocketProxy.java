package cn.iocoder.zj.module.guacamole.utli.ws;
import cn.iocoder.zj.module.guacamole.service.ws.GuacamoleTunnelSocket;
import okhttp3.*;
import okio.ByteString;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * VNC WebSocket 代理服务
 * 用于转发 VNC 客户端请求到 WebSocket 服务器
 */
public class VNCWebSocketProxy {
    private static final Logger log = LoggerFactory.getLogger(VNCWebSocketProxy.class);
    // 基础配置
    private final String websocketUrl;
    private final String cookie;
    private final int port;
    private final String uid;

    // 连接对象
    private OkHttpClient client;
    private ServerSocket serverSocket;

    // 添加连接管理
    private final Map<String, ConnectionInfo> activeConnections = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    private static final long CONNECTION_TIMEOUT_MINUTES = 30; // 连接超时时间

    // 添加线程池配置
    private static final ThreadPoolExecutor proxyExecutor = new ThreadPoolExecutor(
            50,                       // 核心线程数
            200,                     // 最大线程数
            60L,                    // 空闲线程存活时间
            TimeUnit.SECONDS,       // 时间单位
            new LinkedBlockingQueue<>(100),  // 任务队列
            new ThreadFactory() {
                private final AtomicInteger counter = new AtomicInteger(1);
                @Override
                public Thread newThread(Runnable r) {
                    Thread thread = new Thread(r, "VNCProxy-Thread-" + counter.getAndIncrement());
                    thread.setDaemon(true);
                    return thread;
                }
            },
            new ThreadPoolExecutor.CallerRunsPolicy()
    );

    // 添加静态集合来管理所有代理实例
    private static final List<VNCWebSocketProxy> proxyList = new ArrayList<>();
    private static final List<CompletableFuture<Boolean>> proxyFutures = new ArrayList<CompletableFuture<Boolean>>();
    private static volatile boolean isRunning = false;

    // 连接信息类
    private static class ConnectionInfo {
        final Socket clientSocket;
        final WebSocket webSocket;
        final long startTime;

        ConnectionInfo(Socket clientSocket, WebSocket webSocket) {
            this.clientSocket = clientSocket;
            this.webSocket = webSocket;
            this.startTime = System.currentTimeMillis();
        }
    }

    public VNCWebSocketProxy(String websocketUrl, String cookie, int port,String uid) {
        this.websocketUrl = websocketUrl;
        this.cookie = cookie;
        this.port = port;
        this.uid = uid;
    }

    /**
     * 启动代理服务
     * @return boolean - true表示启动成功，false表示启动失败
     */
    public boolean start() {
        try {
            client = createUnsafeClient();
            serverSocket = new ServerSocket(port);
            log.info("VNC代理启动，端口:{}, ws地址:{},token:{}", port,websocketUrl,cookie);

            startCleanupTask();

            proxyExecutor.execute(() -> {
                try {
                    while (!Thread.currentThread().isInterrupted()) {
                        Socket clientSocket = serverSocket.accept();
                        handleClientConnection(clientSocket, uid);
                    }
                } catch (Exception e) {
                    if (!serverSocket.isClosed()) {
                        log.error("接受连接异常", e);
                        stop();
                    }
                }
            });

            return true;
        } catch (Exception e) {
            log.error("启动异常", e);
            stop();
            return false;
        }
    }

    /**
     * 处理客户端连接
     * @return 连接是否成功建立
     */
    private boolean handleClientConnection(Socket clientSocket, String connectionId) {
        try {
            Request request = new Request.Builder()
                    .url(websocketUrl)
                    .addHeader("Sec-WebSocket-Protocol", "binary")
                    .addHeader("Cookie", cookie)
                    .build();

            // 创建一个CompletableFuture来等待连接结果
            CompletableFuture<Boolean> connectionFuture = new CompletableFuture<>();

            WebSocket webSocket = client.newWebSocket(request,
                    new VNCWebSocketListener(clientSocket, connectionId) {
                        @Override
                        public void onOpen(WebSocket webSocket, Response response) {
                            super.onOpen(webSocket, response);
                            connectionFuture.complete(true);
                        }

                        @Override
                        public void onFailure(WebSocket webSocket, Throwable t, Response response) {
                            super.onFailure(webSocket, t, response);
                            connectionFuture.complete(false);
                        }
                    });

            // 记录新连接
            activeConnections.put(connectionId, new ConnectionInfo(clientSocket, webSocket));

            // 等待连接结果，设置超时时间为10秒
            return connectionFuture.get(10, TimeUnit.SECONDS);

        } catch (Exception e) {
            log.error("处理客户端连接失败: {}", connectionId, e);
            closeConnection(connectionId);
            return false;
        }
    }

    /**
     * WebSocket监听器
     */
    private class VNCWebSocketListener extends WebSocketListener {
        private final Socket clientSocket;
        private final String connectionId;

        public VNCWebSocketListener(Socket clientSocket, String connectionId) {
            this.clientSocket = clientSocket;
            this.connectionId = connectionId;
        }

        @Override
        public void onOpen(WebSocket webSocket, Response response) {
            log.info("WebSocket连接已建立");
            startDataForwarding(clientSocket, webSocket);
        }

        @Override
        public void onMessage(WebSocket webSocket, ByteString bytes) {
            try {
                // 转发WebSocket数据到VNC客户端
                OutputStream out = clientSocket.getOutputStream();
                out.write(bytes.toByteArray());
                out.flush();
            } catch (IOException e) {
                log.error("数据转发失败", e);
                closeConnection(connectionId);
            }
        }

        @Override
        public void onFailure(WebSocket webSocket, Throwable t, Response response) {
            if (response != null) {
                log.error("WebSocket连接失败 - 状态码: {}, 信息: {}", response.code(), response.message());
            }
            closeConnection(connectionId);
        }

        @Override
        public void onClosing(WebSocket webSocket, int code, String reason) {
            closeConnection(connectionId);
            removeProxy(port);
        }

        @Override
        public void onClosed(WebSocket webSocket, int code, String reason) {
            closeConnection(connectionId);
            removeProxy(port);
        }
    }

    /**
     * 启动数据转发
     */
    private void startDataForwarding(Socket clientSocket, WebSocket webSocket) {
        // 使用已有的proxyExecutor线程池
        proxyExecutor.execute(() -> {
            byte[] buffer = new byte[4096];
            try (InputStream in = clientSocket.getInputStream()) {
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    webSocket.send(ByteString.of(buffer, 0, bytesRead));
                }
            } catch (IOException e) {
                log.error("数据读取失败", e);
            } finally {
                closeQuietly(clientSocket);
                webSocket.close(1000, "数据传输完成");
            }
        });
    }

    /**
     * 停止代理服务
     */
    public void stop() {
        // 关闭所有活动连接
        activeConnections.keySet().forEach(this::closeConnection);
        activeConnections.clear();

        // 关闭调度器
        scheduler.shutdown();

        // 关闭其他资源
        if (serverSocket != null) {
            closeQuietly(serverSocket);
        }
        if (client != null) {
            client.dispatcher().executorService().shutdown();
        }

        log.info("VNC代理服务已停止");
    }

    /**
     * 安全关闭资源
     */
    private void closeQuietly(AutoCloseable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (Exception e) {
                log.error("关闭资源失败", e);
            }
        }
    }

    /**
     * 创建OkHttpClient客户端
     */
    private OkHttpClient createUnsafeClient() {
        try {
            final TrustManager[] trustAllCerts = new TrustManager[]{
                    new X509TrustManager() {
                        @Override
                        public void checkClientTrusted(java.security.cert.X509Certificate[] chain, String authType) {
                        }

                        @Override
                        public void checkServerTrusted(java.security.cert.X509Certificate[] chain, String authType) {
                        }

                        @Override
                        public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                            return new java.security.cert.X509Certificate[]{};
                        }
                    }
            };

            final SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());

            return new OkHttpClient.Builder()
                    .sslSocketFactory(sslContext.getSocketFactory(), (X509TrustManager) trustAllCerts[0])
                    .hostnameVerifier((hostname, session) -> true)
                    .connectTimeout(30, TimeUnit.SECONDS)
                    .readTimeout(30, TimeUnit.SECONDS)
                    .writeTimeout(30, TimeUnit.SECONDS)
                    .retryOnConnectionFailure(true)  // 启用重试
                    .addInterceptor(chain -> {
                        Request request = chain.request();
                        log.debug("发送请求: {} {}", request.method(), request.url());
                        log.debug("请求头: {}", request.headers());
                        Response response = chain.proceed(request);
                        log.debug("收到响应: {} {}", response.code(), response.message());
                        return response;
                    })
                    .build();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void startCleanupTask() {
        scheduler.scheduleAtFixedRate(() -> {
            long now = System.currentTimeMillis();
            activeConnections.forEach((id, info) -> {
                if (now - info.startTime > TimeUnit.MINUTES.toMillis(CONNECTION_TIMEOUT_MINUTES)) {
                    log.info("连接超时，正在关闭: {}", id);
                    closeConnection(id);
                }
            });
        }, 1, 1, TimeUnit.MINUTES);
    }

    private void closeConnection(String connectionId) {
//        GuacamoleTunnelSocket.closeGuacamle(connectionId);
        ConnectionInfo info = activeConnections.remove(connectionId);
        if (info != null) {
            closeQuietly(info.clientSocket);
            if (info.webSocket != null) {
                info.webSocket.close(1000, "连接关闭");
            }
            log.info("连接已关闭: {}", connectionId);
        }
    }

    /**
     * 关闭线程池
     */
    public static void shutdownProxyExecutor() {
        proxyExecutor.shutdown();
        try {
            if (!proxyExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                proxyExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            proxyExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 初始化VNC代理系统
     */
    public static synchronized void initVNCProxySystem() {
        if (isRunning) {
            log.info("VNC代理系统已经在运行中");
            return;
        }

        log.info("初始化VNC代理系统");
        isRunning = true;

        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("正在关闭所有VNC代理...");
            stopAllProxies();
        }));

        // 启动守护线程
        Thread daemonThread = new Thread(() -> {
            try {
                while (isRunning) {
                    Thread.sleep(1000);
                    // 可以在这里添加系统状态监控
                    if (log.isDebugEnabled()) {
                        log.debug("当前活动代理数量: {}", getActiveProxyCount());
                    }
                }
            } catch (InterruptedException e) {
                log.error("守护线程被中断", e);
            }
        }, "VNCProxy-Daemon");
        daemonThread.setDaemon(false);
        daemonThread.start();

        log.info("VNC代理系统初始化完成，等待添加代理...");
    }

    /**
     * 添加新的VNC代理
     */
    public static synchronized void addNewProxy(String wsUrl, String token, int port,String uid) {
        if (!isRunning) {
            initVNCProxySystem();
        }
        token = (token == null || token.isEmpty()) ? "" : token;
        
        if (proxyList.stream().anyMatch(p -> p.port == port)) {
            log.error("端口 {} 已被使用", port);
            return;
        }

        VNCWebSocketProxy proxy = new VNCWebSocketProxy(wsUrl, token, port,uid);
        proxyList.add(proxy);

        CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(() -> {
            try {
                boolean success = proxy.start();
                if (!success) {
                    proxyList.remove(proxy);
                    proxy.stop();
                }
                return success;
            } catch (Exception e) {
                log.error("代理启动异常 - Port: " + port, e);
                proxyList.remove(proxy);
                proxy.stop();
                return false;
            }
        }, proxyExecutor);

        proxyFutures.add(future);

        future.orTimeout(30, TimeUnit.SECONDS)
                .exceptionally(throwable -> {
                    log.error("代理启动失败 - Port: " + port, throwable);
                    proxyList.remove(proxy);
                    proxy.stop();
                    return null;
                });
    }

    /**
     * 移除指定端口的代理
     */
    public static synchronized void removeProxy(int port) {
        proxyList.removeIf(proxy -> {
            if (proxy.port == port) {
                proxy.stop();
                log.info("已移除端口 {} 的代理", port);
                return true;
            }
            return false;
        });
    }

    /**
     * 停止所有代理
     */
    public static synchronized void stopAllProxies() {
        if (!isRunning) {
            return;
        }

        log.info("开始停止所有代理...");
        isRunning = false;
        proxyList.forEach(VNCWebSocketProxy::stop);
        proxyList.clear();
        proxyFutures.clear();
        shutdownProxyExecutor();
        log.info("所有代理已停止");
    }

    /**
     * 获取当前活动的代理数量
     */
    public static int getActiveProxyCount() {
        return proxyList.size();
    }

    // 示例main方法
    public static void main(String[] args) {
        // 初始化空的代理系统
        initVNCProxySystem();

        // 可以在需要时添加代理
        addNewProxy("wss://*************:8443/vnc/websocket-tunnel?token=1B5C8CDB-A0A4-4CC4-B838-75924FBDBFB10&GUAC_DATA_SOURCE=default&GUAC_ID=i-00000001&GUAC_TYPE=c&GUAC_WIDTH=1903&GUAC_HEIGHT=937&GUAC_DPI=125&GUAC_AUDIO=audio%2FL8&GUAC_AUDIO=audio%2FL16&GUAC_IMAGE=image%2Fjpeg&GUAC_IMAGE=image%2Fpng&GUAC_IMAGE=image%2Fwebp",
                "JSESSIONID=847946DDDD44B069D42A508640A5D15C",
                5900,"");
//
//        // 稍后可以继续添加更多代理
//        addNewProxy("wss://**************/websockify?host_ip=**************&host_port=5902",
//                "SESSION=6f7287bb-4fda-4377-9944-cc1d699e915f",
//                5901,"");

        // 系统会持续运行，直到调用stopAllProxies()或程序退出
    }
}