package cn.iocoder.zj.module.guacamole.dal.dataobject.onlinesession;

import lombok.*;

import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 会话审计 DO
 *
 * <AUTHOR>
 */
@TableName("guacamole_online_session")
@KeySequence("guacamole_online_session_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OnlineSessionDO extends BaseDO {

    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private String id;
    /**
     * 来源ip
     */
    private String clientIp;
    /**
     * 资产id
     */
    private String assetId;
    /**
     * 资产名称
     */
    private String assetName;
    /**
     * 连接协议
     */
    private String protocol;
    /**
     * ip地址
     */
    private String ip;
    /**
     * 端口号
     */
    private String port;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 接入时间
     */
    private LocalDateTime connectedTime;
    /**
     * 连接id
     */
    private String connectionId;
    /**
     * 高
     */
    private String height;
    /**
     * 宽
     */
    private String width;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 在线状态
     */
    private String status;
    /**
     * 文件地址
     */
    private String filePath;
    /**
     * 断开时间
     */
    private LocalDateTime disconnectedTime;

    /**
     * 代理id
     */
    private Long portMappingId;

}
