package cn.iocoder.zj.module.guacamole.controller.admin.onlinesession.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;

@Schema(description = "管理后台 - 会话审计更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OnlineSessionUpdateReqVO extends OnlineSessionBaseVO {

    @Schema(description = "主键", required = true)
    @NotNull(message = "主键不能为空")
    private String id;

}
