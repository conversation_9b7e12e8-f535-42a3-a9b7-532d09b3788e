package cn.iocoder.zj.module.guacamole.controller.admin.auditvideo.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 堡垒机视频录像 Excel 导出 Request VO，参数和 AuditVideoPageReqVO 是一致的")
@Data
public class AuditVideoExportReqVO {

    @Schema(description = "设备id")
    private String deviceId;

    @Schema(description = "主机ip")
    private String host;

    @Schema(description = "监控名称")
    private String name;

    @Schema(description = "文件地址")
    private String path;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
