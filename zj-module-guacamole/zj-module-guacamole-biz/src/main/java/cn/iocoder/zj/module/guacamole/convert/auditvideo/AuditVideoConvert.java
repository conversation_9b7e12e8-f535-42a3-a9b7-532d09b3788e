package cn.iocoder.zj.module.guacamole.convert.auditvideo;

import java.util.*;

import cn.iocoder.zj.module.guacamole.controller.admin.auditvideo.vo.AuditVideoCreateReqVO;
import cn.iocoder.zj.module.guacamole.controller.admin.auditvideo.vo.AuditVideoExcelVO;
import cn.iocoder.zj.module.guacamole.controller.admin.auditvideo.vo.AuditVideoRespVO;
import cn.iocoder.zj.module.guacamole.controller.admin.auditvideo.vo.AuditVideoUpdateReqVO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.guacamole.dal.dataobject.auditvideo.AuditVideoDO;

/**
 * 堡垒机视频录像 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface AuditVideoConvert {

    AuditVideoConvert INSTANCE = Mappers.getMapper(AuditVideoConvert.class);

    AuditVideoDO convert(AuditVideoCreateReqVO bean);

    AuditVideoDO convert(AuditVideoUpdateReqVO bean);

    AuditVideoRespVO convert(AuditVideoDO bean);

    List<AuditVideoRespVO> convertList(List<AuditVideoDO> list);

    PageResult<AuditVideoRespVO> convertPage(PageResult<AuditVideoDO> page);

    List<AuditVideoExcelVO> convertList02(List<AuditVideoDO> list);

}
