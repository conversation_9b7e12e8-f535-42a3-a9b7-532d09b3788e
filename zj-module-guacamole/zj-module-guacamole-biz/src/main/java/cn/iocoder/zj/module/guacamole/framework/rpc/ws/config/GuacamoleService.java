package cn.iocoder.zj.module.guacamole.framework.rpc.ws.config;

//import cn.iocoder.cloud.guacamole.framework.rpc.ws.config.GuacamoleMonitor;
import cn.iocoder.zj.module.guacamole.dal.mysql.onlinesession.OnlineSessionMapper;
import cn.iocoder.zj.module.guacamole.service.ws.GuacamoleMonitorSocket;
import cn.iocoder.cloud.module.cloudedge.api.monitor.MonitorApi;
import cn.iocoder.zj.module.guacamole.service.ws.GuacamoleTunnelSocket;
import cn.iocoder.zj.module.om.api.asset.MonitorassetApi;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.usercertificate.UsercertificateApi;
import org.apache.guacamole.protocol.ConfiguredGuacamoleSocket;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.websocket.Session;
import javax.websocket.server.ServerEndpointConfig;
import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @ClassName : GuacamoleService  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/6/7  16:49
 */
@Service
@Component
public class GuacamoleService implements InitializingBean {

    @Value("${guacamole_server_host}")
    private String GUAC_HOSTNAME;

    @Value("${guacamole_server_port}")
    private Integer GUAC_PORT;

    @Value("${upload.videoPath}")
    private String videoPath;

    @Value("${upload.filePath}")
    private String filePath;

    @Value("${proxy.url}")
    private String proxyUrl;
    @Value("${proxy.ip}")
    private String proxyName;

    public final Map<String, Session> sessions = new ConcurrentHashMap<>();

    public final Map<String, Session> monitorSessions = new ConcurrentHashMap<>();

    public final Map<String, ConfiguredGuacamoleSocket> tunnels = new ConcurrentHashMap<>();

    @Autowired
    private GuacamoleEndpointExporter guacamoleEndpointExporter;

    @Autowired
    private MonitorApi monitorApi;

    @Autowired
    private MonitorassetApi monitorassetApi;
    @Autowired
    private AdminUserApi adminUserApi;
    @Resource
    private OnlineSessionMapper onlineSessionMapper;

    @Autowired
    GuacamoleMonitorSocket guacamoleMonitorSocket;
    @Autowired
    UsercertificateApi usercertificateApi;
    @Resource
    RedisTemplate redisTemplate;

    @Override
    public void afterPropertiesSet() throws Exception {
        ServerEndpointConfig config = ServerEndpointConfig.Builder.create(GuacamoleTunnelSocket.class, "/ws").subprotocols(Arrays.asList(new String[]{"guacamole"})).build();
        ServerEndpointConfig gu = ServerEndpointConfig.Builder.create(GuacamoleMonitorSocket.class, "/ws/monitor").subprotocols(Arrays.asList(new String[]{"guacamole"})).build();
        guacamoleEndpointExporter.getServerContainer().addEndpoint(config);
        guacamoleEndpointExporter.getServerContainer().addEndpoint(gu);
        // 接入
        GuacamoleTunnelSocket.sessions = sessions;
        GuacamoleTunnelSocket.tunnels = tunnels;
        GuacamoleTunnelSocket.GUAC_HOSTNAME = GUAC_HOSTNAME;
        GuacamoleTunnelSocket.GUAC_PORT = GUAC_PORT;
        GuacamoleTunnelSocket.proxyUrl = proxyUrl;
        GuacamoleTunnelSocket.proxyName = proxyName;
        GuacamoleTunnelSocket.videoPath = videoPath;
        GuacamoleTunnelSocket.filePath = filePath;
        GuacamoleTunnelSocket.monitorassetApi = monitorassetApi;
        GuacamoleTunnelSocket.adminUserApi = adminUserApi;
        GuacamoleTunnelSocket.onlineSessionMapper = onlineSessionMapper;
        GuacamoleTunnelSocket.guacamoleMonitorSocket = guacamoleMonitorSocket;
        GuacamoleTunnelSocket.usercertificateApi = usercertificateApi;
        GuacamoleTunnelSocket.redisTemplate = redisTemplate;
        //监控
        GuacamoleMonitorSocket.monitorApi = monitorApi;
        GuacamoleMonitorSocket.GUAC_PORT = GUAC_PORT;
        GuacamoleMonitorSocket.GUAC_HOSTNAME = GUAC_HOSTNAME;
        GuacamoleMonitorSocket.tunnels = tunnels;
        GuacamoleMonitorSocket.onlineSessionMapper = onlineSessionMapper;
        GuacamoleMonitorSocket.monitorSessions = monitorSessions;
    }
}
