package cn.iocoder.zj.module.guacamole.dal.mysql.onlinesession;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.guacamole.controller.admin.onlinesession.vo.OnlineSessionExportReqVO;
import cn.iocoder.zj.module.guacamole.controller.admin.onlinesession.vo.OnlineSessionPageReqVO;
import cn.iocoder.zj.module.guacamole.dal.dataobject.onlinesession.OnlineSessionDO;
import jodd.util.StringUtil;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 会话审计 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface OnlineSessionMapper extends BaseMapperX<OnlineSessionDO> {

    default PageResult<OnlineSessionDO> selectPage(OnlineSessionPageReqVO reqVO, List<Map> platform) {
        List<String> data = new ArrayList<>();
        if (platform.size() > 0) {
            for (Map map : platform) {
                data.add(map.get("platformId").toString());
            }
        }
        LambdaQueryWrapperX<OnlineSessionDO> wrapperX = new LambdaQueryWrapperX<OnlineSessionDO>()
                .eq(OnlineSessionDO::getStatus, "connected")
                .likeIfPresent(OnlineSessionDO::getClientIp, reqVO.getClientIp())
                .eqIfPresent(OnlineSessionDO::getAssetId, reqVO.getAssetId())
                .likeIfPresent(OnlineSessionDO::getAssetName, reqVO.getAssetName())
                .likeIfPresent(OnlineSessionDO::getProtocol, reqVO.getProtocol())
                .eqIfPresent(OnlineSessionDO::getIp, reqVO.getIp())
                .eqIfPresent(OnlineSessionDO::getPort, reqVO.getPort())
                .eqIfPresent(OnlineSessionDO::getPlatformId, reqVO.getPlatformId())
                .eqIfPresent(OnlineSessionDO::getUserId, reqVO.getUserId())
                .betweenIfPresent(OnlineSessionDO::getConnectedTime, reqVO.getConnectedTime())
                .eqIfPresent(OnlineSessionDO::getConnectionId, reqVO.getConnectionId())
                .eqIfPresent(OnlineSessionDO::getHeight, reqVO.getHeight())
                .eqIfPresent(OnlineSessionDO::getWidth, reqVO.getWidth())
                .likeIfPresent(OnlineSessionDO::getUserName, reqVO.getUserName())
                .betweenIfPresent(OnlineSessionDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(OnlineSessionDO::getStatus, reqVO.getStatus())
                .eqIfPresent(OnlineSessionDO::getFilePath, reqVO.getFilePath());

        if (jodd.util.StringUtil.isNotEmpty(reqVO.getStartTime()) && jodd.util.StringUtil.isNotEmpty(reqVO.getEndTime())) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            try {
                date = sdf.parse(reqVO.getEndTime());
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(date);
                // 把日期设置为当天最后一秒
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                date = calendar.getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            wrapperX.betweenIfPresent(OnlineSessionDO::getCreateTime, reqVO.getStartTime(), sdf.format(date));
        }

        if (data.size() > 0) {
            wrapperX = (LambdaQueryWrapperX<OnlineSessionDO>) wrapperX.in(OnlineSessionDO::getPlatformId, data);
        } else {
            wrapperX = (LambdaQueryWrapperX<OnlineSessionDO>) wrapperX.in(OnlineSessionDO::getPlatformId, "null");
        }
        if (StringUtil.isNotEmpty(reqVO.getSortDirection()) && reqVO.getSortDirection().equals("asc")) {
            if (reqVO.getSortBy().equals("connectedTime")) {
                wrapperX.orderByAsc(OnlineSessionDO::getConnectedTime);
            }
        } else {
            wrapperX.orderByDesc(OnlineSessionDO::getConnectedTime);
        }
        return selectPage(reqVO, wrapperX);
    }

    default List<OnlineSessionDO> selectList(OnlineSessionExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<OnlineSessionDO>()
                .likeIfPresent(OnlineSessionDO::getClientIp, reqVO.getClientIp())
                .eqIfPresent(OnlineSessionDO::getAssetId, reqVO.getAssetId())
                .likeIfPresent(OnlineSessionDO::getAssetName, reqVO.getAssetName())
                .likeIfPresent(OnlineSessionDO::getProtocol, reqVO.getProtocol())
                .eqIfPresent(OnlineSessionDO::getIp, reqVO.getIp())
                .eqIfPresent(OnlineSessionDO::getPort, reqVO.getPort())
                .eqIfPresent(OnlineSessionDO::getPlatformId, reqVO.getPlatformId())
                .eqIfPresent(OnlineSessionDO::getUserId, reqVO.getUserId())
                .betweenIfPresent(OnlineSessionDO::getConnectedTime, reqVO.getConnectedTime())
                .eqIfPresent(OnlineSessionDO::getConnectionId, reqVO.getConnectionId())
                .eqIfPresent(OnlineSessionDO::getHeight, reqVO.getHeight())
                .eqIfPresent(OnlineSessionDO::getWidth, reqVO.getWidth())
                .likeIfPresent(OnlineSessionDO::getUserName, reqVO.getUserName())
                .betweenIfPresent(OnlineSessionDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(OnlineSessionDO::getStatus, reqVO.getStatus())
                .eqIfPresent(OnlineSessionDO::getFilePath, reqVO.getFilePath())
                .orderByDesc(OnlineSessionDO::getId));
    }

    default PageResult<OnlineSessionDO> selectPageVideo(OnlineSessionPageReqVO reqVO, List<Map> platform) {
        List<String> data = new ArrayList<>();
        if (platform.size() > 0) {
            for (Map map : platform) {
                data.add(map.get("platformId").toString());
            }
        }

        LambdaQueryWrapperX<OnlineSessionDO> wrapperX = new LambdaQueryWrapperX<OnlineSessionDO>()
                .eq(OnlineSessionDO::getStatus, "disconnected")
                .likeIfPresent(OnlineSessionDO::getClientIp, reqVO.getClientIp())
                .eqIfPresent(OnlineSessionDO::getAssetId, reqVO.getAssetId())
                .likeIfPresent(OnlineSessionDO::getAssetName, reqVO.getAssetName())
                .likeIfPresent(OnlineSessionDO::getProtocol, reqVO.getProtocol())
                .eqIfPresent(OnlineSessionDO::getIp, reqVO.getIp())
                .eqIfPresent(OnlineSessionDO::getPort, reqVO.getPort())
                .eqIfPresent(OnlineSessionDO::getPlatformId, reqVO.getPlatformId())
                .eqIfPresent(OnlineSessionDO::getUserId, reqVO.getUserId())
                .betweenIfPresent(OnlineSessionDO::getConnectedTime, reqVO.getConnectedTime())
                .eqIfPresent(OnlineSessionDO::getConnectionId, reqVO.getConnectionId())
                .eqIfPresent(OnlineSessionDO::getHeight, reqVO.getHeight())
                .eqIfPresent(OnlineSessionDO::getWidth, reqVO.getWidth())
                .likeIfPresent(OnlineSessionDO::getUserName, reqVO.getUserName())
                .betweenIfPresent(OnlineSessionDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(OnlineSessionDO::getStatus, reqVO.getStatus())
                .eqIfPresent(OnlineSessionDO::getFilePath, reqVO.getFilePath());

        if (jodd.util.StringUtil.isNotEmpty(reqVO.getStartTime()) && jodd.util.StringUtil.isNotEmpty(reqVO.getEndTime())) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            try {
                date = sdf.parse(reqVO.getEndTime());
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(date);
                // 把日期设置为当天最后一秒
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                date = calendar.getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            wrapperX.betweenIfPresent(OnlineSessionDO::getCreateTime, reqVO.getStartTime(), sdf.format(date));
        }

        if (data.size() > 0) {
            wrapperX = (LambdaQueryWrapperX<OnlineSessionDO>) wrapperX.in(OnlineSessionDO::getPlatformId, data);
        } else {
            wrapperX = (LambdaQueryWrapperX<OnlineSessionDO>) wrapperX.in(OnlineSessionDO::getPlatformId, "null");
        }

        if (StringUtil.isNotEmpty(reqVO.getSortBy()) && reqVO.getSortDirection().equals("asc")) {
            if (reqVO.getSortBy().equals("disconnectedTime")) {
                wrapperX.last("order by  TIMESTAMPDIFF( SECOND,connected_time, disconnected_time) asc");
            }

            if (reqVO.getSortBy().equals("connectedTime")) {
                wrapperX.orderByAsc(OnlineSessionDO::getConnectedTime);
            }
        } else {
            if (StringUtil.isNotEmpty(reqVO.getSortBy())) {
                if (reqVO.getSortBy().equals("disconnectedTime")) {
                    wrapperX.last("order by TIMESTAMPDIFF( SECOND, connected_time,disconnected_time) desc");
                }
                if (reqVO.getSortBy().equals("connectedTime")) {
                    wrapperX.orderByDesc(OnlineSessionDO::getConnectedTime);
                }
            } else {
                wrapperX.orderByDesc(OnlineSessionDO::getConnectedTime);
            }
        }

        return selectPage(reqVO, wrapperX);
    }

    void updateStatusByUuid(@Param("uuid") String uuid, @Param("disconnectedTime") LocalDateTime disconnectedTime);

    @TenantIgnore
    void updateAssetByUuid(@Param("id") String id, @Param("disconnectedTime") LocalDateTime disconnectedTime);

    @TenantIgnore
    String selectNameByPlatformId(@Param("platformId") Long platformId);

    @TenantIgnore
    Long selectIdByName(@Param("name") String name);

    @TenantIgnore
    Long selectPordMappingIdByUuid(@Param("uuid") String uuid);

    @TenantIgnore
    void deleteAssetByUserId(@Param("monitorAssetId") String monitorAssetId, @Param("userId") String userId);

    @TenantIgnore
    void updateHostByUuid(@Param("id") String id, @Param("disconnectedTime") LocalDateTime disconnectedTime);

    @TenantIgnore
    String selectNamesByPlatformId(@Param("platformId")Long platformId);
}
