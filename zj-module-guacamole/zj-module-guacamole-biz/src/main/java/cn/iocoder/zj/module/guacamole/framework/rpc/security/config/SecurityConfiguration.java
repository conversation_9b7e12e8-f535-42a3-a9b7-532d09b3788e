package cn.iocoder.zj.module.guacamole.framework.rpc.security.config;

import cn.iocoder.cloud.module.guacamole.enums.ApiConstants;
import cn.iocoder.zj.framework.security.config.AuthorizeRequestsCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.ExpressionUrlAuthorizationConfigurer;

/**
 * monitor 模块的 Security 配置
 */
@Configuration(proxyBeanMethods = false, value = "guacamoleSecurityConfiguration")
public class SecurityConfiguration {

    @Bean("guacamoleAuthorizeRequestsCustomizer")
    public AuthorizeRequestsCustomizer authorizeRequestsCustomizer() {
        return new AuthorizeRequestsCustomizer() {

            @Override
            public void customize(ExpressionUrlAuthorizationConfigurer<HttpSecurity>.ExpressionInterceptUrlRegistry registry) {
                // websocket 放开
                registry.antMatchers("/admin-api/schedule/**").permitAll()
                        .antMatchers("/ws/**").permitAll();

                registry.antMatchers("/api/monitor/**").permitAll();
                // Swagger 接口文档
                registry.antMatchers("/v3/api-docs/**").permitAll() // 元数据
                        .antMatchers("/swagger-ui.html").permitAll(); // Swagger UI
                // Druid 监控
                registry.antMatchers("/druid/**").anonymous();
                // Spring Boot Actuator 的安全配置
                registry.antMatchers("/actuator").anonymous()
                        .antMatchers("/actuator/**").anonymous();
                // RPC 服务的安全配置
                registry.antMatchers(ApiConstants.PREFIX + "/**").permitAll();
            }

        };
    }



}
