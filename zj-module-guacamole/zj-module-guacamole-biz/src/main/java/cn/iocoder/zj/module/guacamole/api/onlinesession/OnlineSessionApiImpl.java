package cn.iocoder.zj.module.guacamole.api.onlinesession;

import cn.iocoder.cloud.module.guacamole.api.OnlineSessionApi;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.guacamole.service.onlinesession.OnlineSessionService;
import cn.iocoder.zj.module.guacamole.service.ws.GuacamoleTunnelSocket;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

/**
 * @ClassName : OnlineSessionApiImpl  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/10/9  15:10
 */

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class OnlineSessionApiImpl implements OnlineSessionApi {

    @Resource
    OnlineSessionService onlineSessionService;
    @Resource
    GuacamoleTunnelSocket guacamoleTunnelSocket;

    @Override
    public CommonResult<String> selectNameByPlatformId(Long platformId) {
        return CommonResult.success(onlineSessionService.selectNameByPlatformId(platformId));
    }

    @Override
    public CommonResult<Long> selectIdByName(String name) {
        return CommonResult.success(onlineSessionService.selectIdByName(name));
    }

    @Override
    public void closeGuacamle(String connectionId) {
        guacamoleTunnelSocket.closeGuacamle(connectionId);
    }
}
