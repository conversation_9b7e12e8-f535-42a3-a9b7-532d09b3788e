package cn.iocoder.zj.module.guacamole.service.ws;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.framework.common.util.servlet.ServletUtils;
import cn.iocoder.zj.framework.common.util.threadpool.ThreadPool;
import cn.iocoder.zj.module.guacamole.dal.dataobject.onlinesession.OnlineSessionDO;
import cn.iocoder.zj.module.guacamole.dal.mysql.onlinesession.OnlineSessionMapper;
import cn.iocoder.zj.module.guacamole.utli.ws.VNCWebSocketProxy;
import cn.iocoder.zj.module.om.api.asset.MonitorassetApi;
import cn.iocoder.zj.module.om.api.asset.dto.MonitorAssetDTO;
import cn.iocoder.zj.module.om.api.asset.dto.VncInfoDTO;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.zj.module.system.api.usercertificate.UsercertificateApi;
import cn.iocoder.zj.module.system.api.usercertificate.dto.UserCertificateRespDTO;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.net.GuacamoleTunnel;
import org.apache.guacamole.net.InetGuacamoleSocket;
import org.apache.guacamole.net.SimpleGuacamoleTunnel;
import org.apache.guacamole.protocol.ConfiguredGuacamoleSocket;
import org.apache.guacamole.protocol.GuacamoleClientInformation;
import org.apache.guacamole.protocol.GuacamoleConfiguration;
import org.apache.guacamole.websocket.GuacamoleWebSocketTunnelEndpoint;
import org.apache.hertzbeat.common.util.AesUtil;
import org.apache.hertzbeat.common.util.IpDomainUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.websocket.CloseReason;
import javax.websocket.EndpointConfig;
import javax.websocket.Session;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.Method;
import java.net.*;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @ClassName : WebSocketTunnel  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/5/8  14:21
 */
@Component
@Slf4j
public class GuacamoleTunnelSocket extends GuacamoleWebSocketTunnelEndpoint {

    public static RedisTemplate redisTemplate;
    public static GuacamoleMonitorSocket guacamoleMonitorSocket;
    public static MonitorassetApi monitorassetApi;
    public static UsercertificateApi usercertificateApi;
    public static String proxyUrl;
    public static String proxyName;
    private GuacamoleTunnel guacamoleTunnel;

    private static final Logger logger = LoggerFactory.getLogger(GuacamoleTunnelSocket.class);

    public static Map<String, ConfiguredGuacamoleSocket> tunnels;
    public static Map<String, Session> sessions;

    public static String GUAC_HOSTNAME = "";
    public static Integer GUAC_PORT = 0;
    public static String videoPath;
    public static AdminUserApi adminUserApi;
    public static OnlineSessionMapper onlineSessionMapper;
    public static String filePath;

    /**
     * 返回给定会话的新隧道。如何创建此隧道
     * 或检索到的是依赖于实现的。
     *
     * @param session        与活动WebSocket关联的会话连接。
     * @param endpointConfig 为处理这个单一的连接而创建的端点。
     * @return 连接的隧道，如果不存在此类隧道，则为null。
     * @throws GuacamoleException 如果在检索或者如果对隧道的访问被拒绝。
     */
    @Override
    protected GuacamoleTunnel createTunnel(Session session, EndpointConfig endpointConfig) throws GuacamoleException {
        try {
            logger.info("[createTunnel] 开始创建连接, sessionId: {}", session.getId());

            // 1. 提取请求参数
            Map<String, String> queryParams = getParams(session.getQueryString());
            queryParams.remove("undefined");
            TunnelParams params = extractTunnelParams(queryParams);

            // 2. 准备基础配置
            GuacamoleConfiguration configuration = new GuacamoleConfiguration();
            ConnectionInfo connInfo = ConnectionInfo.builder().build();
            String recordingPath = prepareRecordingPath(params.getUuid());
            connInfo.setRecordingPath(recordingPath);

            // 3. 根据类型配置连接参数
            if (params.isCloudVncType()) {
                configureCloudVncConnection(configuration, connInfo, params);
            } else {
                configureAssetConnection(configuration, connInfo, params);
            }

            // 4. 创建连接并保存会话信息
            GuacamoleTunnel tunnel = establishConnection(configuration, params, connInfo, session);
            // 获取客户端的 IP 地址
            String clientIp = ServletUtils.getClientIP();
            log.info("客户端IP: {}", clientIp);
            String localhostIp = IpDomainUtil.getLocalhostIp();
            log.info("服务DNS: {}", localhostIp);
            params.setHost(localhostIp);
            saveSessionInfo(tunnel, params, connInfo);
            logger.info("[createTunnel] 连接创建成功, uuid: {}, protocol: {}", params.getUuid(), connInfo.getProtocol());
            return tunnel;

        } catch (Exception e) {
            logger.error("[createTunnel] 创建隧道失败", e);
            throw new GuacamoleException("创建连接失败: " + e.getMessage());
        }
    }

    private TunnelParams extractTunnelParams(Map<String, String> queryParams) {
        String userId = Convert.toStr(queryParams.get("userId"));
        AdminUserRespDTO user = adminUserApi.getUser(Convert.toLong(userId)).getCheckedData();

        return TunnelParams.builder()
                .type(Convert.toStr(queryParams.get("type")))
                .id(queryParams.get("id"))
                .userId(userId)
                .uuid(Convert.toStr(queryParams.get("uuid")))
                .width(queryParams.get("wid"))
                .height(queryParams.get("hei"))
                .user(user)
                .build();
    }

    private String prepareRecordingPath(String uuid) {
        String dt = DateUtil.format(new Date(), "yyyyMMdd");
        String path = videoPath + dt + "/";
        File recordingDir = new File(path);
        if (!recordingDir.exists()) {
            recordingDir.mkdirs();
        }
        return path + uuid + ".guac";
    }

    private void configureCommonParameters(GuacamoleConfiguration config, String hostname, String port,
                                           TunnelParams params, String recordingPath) {
        config.setParameter("hostname", hostname);
        config.setParameter("port", port);
        config.setParameter("width", params.getWidth());
        config.setParameter("height", params.getHeight());
        config.setParameter("dpi", "96");

        File recordingFile = new File(recordingPath);
        String directory = recordingFile.getParent();
        String filename = recordingFile.getName();
        // 确保录制参数完整且正确
        config.setParameter("enable-recording", "true");
        config.setParameter("recording-path", directory);
        config.setParameter("create-recording-path", "true");
        config.setParameter("recording-name", filename);
        config.setParameter("recording-exclude-output", "false");
        config.setParameter("recording-exclude-mouse", "false");
        config.setParameter("recording-include-keys", "true");

        long startTime = System.currentTimeMillis();
        config.setParameter("timestamp", String.valueOf(startTime));
        config.setParameter("recording-timing", "true");
        config.setParameter("recording-timing-ms", "100");
    }

    private void configureCloudVncConnection(GuacamoleConfiguration config, ConnectionInfo connInfo,
                                             TunnelParams params) {
        logger.info("[configureCloudVncConnection] 配置云主机VNC连接, id: {}", params.getId());
        VncInfoDTO vncInfo = monitorassetApi.getVncInfoByUuid(params.getId()).getCheckedData();
        if ("vmware".equals(vncInfo.getVncType())) {
            MonitorAssetDTO asset = new MonitorAssetDTO();
            asset.setPlatformId(vncInfo.getPlatformId());
            asset.setHostname(vncInfo.getTcpHost());
            asset.setProtocolProd(Convert.toInt(vncInfo.getPortMappingId()));

            // 添加错误处理
            PortMappingResult mapping = createPortMapping(asset);
            if (mapping == null) {
                throw new RuntimeException("创建端口映射失败");
            }
            String tcpUrl = replaceHostAndPort(vncInfo.getWebsocketPath(), proxyName, Convert.toInt(mapping.getPort()));
            vncInfo.setPortMappingId(Convert.toStr(mapping.getMappingId()));
            vncInfo.setWebsocketPath(tcpUrl);
        }

        createTCPServer(vncInfo, params.getUuid());
        params.setHost(vncInfo.getTcpHost());
        String localhostIp = IpDomainUtil.getLocalhostIp();
        log.info("服务DNS:{},返回vncInfo:{}", localhostIp, JSONObject.toJSONString(vncInfo));
        // 设置VNC配置
        config.setProtocol("vnc");
        configureVncParameters(config, vncInfo.getVncToken());
        configureCommonParameters(config, localhostIp, Convert.toStr(vncInfo.getTcpPort()),
                params, connInfo.getRecordingPath());

        // 保存连接信息
        connInfo.setHostname(localhostIp);
        connInfo.setPort(Convert.toStr(vncInfo.getTcpPort()));
        connInfo.setProtocol("vnc");
        connInfo.setAssetInfo(createCloudAssetInfo(vncInfo));
        connInfo.setPortMappingId(Convert.toLong(vncInfo.getPortMappingId()));
    }

    private static String replaceHostAndPort(String url, String newHost, int newPort) {
        return url.replaceFirst("//[^:/]+", "//" + newHost) // 替换主机地址
                .replaceFirst(":(\\d+)", ":" + newPort); // 替换端口号
    }

    private MonitorAssetDTO createCloudAssetInfo(VncInfoDTO vncInfo) {
        MonitorAssetDTO assetInfo = new MonitorAssetDTO();
        assetInfo.setAssetName(vncInfo.getCloudName());
        assetInfo.setPlatformId(vncInfo.getPlatformId());
        assetInfo.setAssetId(Convert.toStr(vncInfo.getCloudId()));
        return assetInfo;
    }

    private void configureVncParameters(GuacamoleConfiguration config, String vncToken) {
        config.setParameter("ignore-cert", "true");
        config.setParameter("security", "any");
        config.setParameter("cursor", "remote");
        config.setParameter("color-depth", "24");
        config.setParameter("swap-red-blue", "false");

        if (vncToken != null) {
            config.setParameter("password", vncToken);
        }
    }

    private void configureAssetConnection(GuacamoleConfiguration config, ConnectionInfo connInfo,
                                          TunnelParams params) {
        logger.info("[configureAssetConnection] 配置资产连接, id: {}", params.getId());

        MonitorAssetDTO asset = monitorassetApi.getAssetInfoById(Convert.toLong(params.getId())).getCheckedData();
        configureCommonParameters(config, proxyName, null, params, connInfo.getRecordingPath());
        if (asset.getAssetType() == 2) {
            configureVncAssetConnection(config, connInfo, asset, params);
        } else {
            configureGeneralAssetConnection(config, connInfo, asset, params);
        }

        connInfo.setAssetInfo(asset);
    }

    private void configureVncAssetConnection(GuacamoleConfiguration config, ConnectionInfo connInfo,
                                             MonitorAssetDTO asset, TunnelParams params) {
        VncInfoDTO vncInfo = monitorassetApi.getVncInfoByUuid(asset.getAssetId()).getCheckedData();
        logger.info("[configureVncAssetConnection] VNC资产信息: {}", vncInfo);

        config.setProtocol("vnc");
        configureVncParameters(config, null);

        // 确保这些参数在VNC连接中被正确设置
        config.setParameter("hostname", vncInfo.getTcpHost());
        config.setParameter("port", Convert.toStr(vncInfo.getTcpPort()));
        // 添加额外的VNC特定参数
        config.setParameter("autoretry", "10");
        config.setParameter("read-only", "false");

        params.setHost(vncInfo.getTcpHost());

        connInfo.setHostname(vncInfo.getTcpHost());
        connInfo.setPort(Convert.toStr(vncInfo.getTcpPort()));
        connInfo.setPortMappingId(Convert.toLong(vncInfo.getPortMappingId()));
        connInfo.setProtocol("vnc");
    }

    private void configureGeneralAssetConnection(GuacamoleConfiguration config, ConnectionInfo connInfo,
                                                 MonitorAssetDTO asset, TunnelParams params) {
        // 创建端口映射
        PortMappingResult mapping = createPortMapping(asset);

        // 设置凭证信息
        if (!asset.getCertificate().equals("custom")) {
            UserCertificateRespDTO certInfo = usercertificateApi.getUser(asset.getPassword()).getData();
            asset.setUsername(certInfo.getUsername());
            asset.setPassword(AesUtil.aesDecode(certInfo.getPassword()));
        }
        // 配置协议特定参数
        String protocol = asset.getProtocol();
        config.setParameter("hostname", proxyName);
        config.setParameter("port", mapping.getPort());
        configureProtocolSpecificParameters(config, protocol, params, asset);

        // 设置连接信息
        connInfo.setHostname(proxyName);
        connInfo.setPort(mapping.getPort());
        connInfo.setPortMappingId(mapping.getMappingId());
        connInfo.setProtocol(protocol);
    }


    private void configureProtocolSpecificParameters(GuacamoleConfiguration config, String protocol,
                                                     TunnelParams params, MonitorAssetDTO asset) {
        config.setProtocol(protocol);
        config.setParameter("ignore-cert", "true");

        // 设置凭证
        config.setParameter("username", asset.getUsername());
        config.setParameter("password", AesUtil.aesDecode(String.valueOf(asset.getPassword())));

        switch (protocol) {
            case "ssh":
                configureSshParameters(config);
                break;
            case "vnc":
                configureVncParameters(config, null);
                break;
            case "rdp":
                configureRdpParameters(config, params);
                break;
            case "telnet":
                config.setProtocol("telnet");
                break;
            case "kubernetes":
                config.setProtocol("kubernetes");
                break;
        }
    }

    private void configureSshParameters(GuacamoleConfiguration config) {
        config.setParameter("color-scheme", "white-black");
        config.setParameter("terminal-type", "xterm-256color");
        config.setParameter("font-name", "Courier New");
        config.setParameter("enable-sftp", "true");
    }

    private void configureRdpParameters(GuacamoleConfiguration config, TunnelParams params) {
        File dir = new File(filePath + params.getId());
        if (!dir.exists()) {
            dir.mkdir();
        }
        config.setParameter("enable-drive", "true");
        config.setParameter("create-path", "true");
        config.setParameter("drive-path", filePath + params.getUser().getTenantId());
        config.setParameter("create-drive-path", "true");
    }

    private GuacamoleTunnel establishConnection(GuacamoleConfiguration config, TunnelParams params,
                                                ConnectionInfo connInfo, Session session) throws GuacamoleException {
        GuacamoleClientInformation clientInfo = new GuacamoleClientInformation();
        clientInfo.setOptimalScreenHeight(Integer.parseInt(params.getHeight()));
        clientInfo.setOptimalScreenWidth(Integer.parseInt(params.getWidth()));

        System.out.println("连接参数：" + JSONObject.toJSONString(config));

        ConfiguredGuacamoleSocket socket = new ConfiguredGuacamoleSocket(
                new InetGuacamoleSocket(GUAC_HOSTNAME, GUAC_PORT),
                config, clientInfo
        );

        GuacamoleTunnel tunnel = new SimpleGuacamoleTunnel(socket);
        guacamoleTunnel = tunnel;
        tunnels.put(params.getUuid(), socket);
        sessions.put(params.getUuid(), session);
        return tunnel;
    }

    private void saveSessionInfo(GuacamoleTunnel tunnel, TunnelParams params, ConnectionInfo connInfo) {
        OnlineSessionDO sessionDO = OnlineSessionDO.builder()
                .id(params.getUuid())
                .clientIp(params.getHost())
                .assetId(params.isCloudVncType() ?
                        connInfo.getAssetInfo().getAssetId() :
                        connInfo.getAssetInfo().getAssetId())
                .assetName(connInfo.getAssetInfo().getAssetName())
                .protocol(connInfo.getProtocol())
                .ip(connInfo.getHostname())
                .port(connInfo.getPort())
                .platformId(connInfo.getAssetInfo().getPlatformId())
                .userId(params.getUser().getId())
                .connectedTime(DateUtil.toLocalDateTime(new Date()))
                .connectionId(((ConfiguredGuacamoleSocket) tunnel.getSocket()).getConnectionID())
                .disconnectedTime(DateUtil.toLocalDateTime(DateUtil.parse("0001-01-01 00:00:00")))
                .height(params.getHeight())
                .width(params.getWidth())
                .userName(params.getUser().getNickname())
                .status("connected")
                .filePath(connInfo.getRecordingPath())
                .portMappingId(connInfo.getPortMappingId())
                .build();

        onlineSessionMapper.insert(sessionDO);
    }

    private PortMappingResult createPortMapping(MonitorAssetDTO asset) {
        try {
            log.info("proxyUrl:{}", proxyUrl);
            if (asset == null || asset.getPlatformId() == null) {
                log.error("createPortMapping失败: asset 或 platformId 为空");
                return null;
            }

            String name = onlineSessionMapper.selectNamesByPlatformId(asset.getPlatformId());
            if (name == null) {
                log.error("createPortMapping失败: 未能通过platformId获取name, platformId: {}", asset.getPlatformId());
                return null;
            }

            Long licenseId = onlineSessionMapper.selectIdByName(name);
            if (licenseId == null) {
                log.error("createPortMapping失败: 未能通过name获取licenseId, name: {}", name);
                return null;
            }

            // 获取端口
            Map<String, Object> params = new HashMap<>();
            params.put("id", licenseId);
            String response = HttpUtil.get(proxyUrl + "/port-mapping/port-server", params);
            if (response == null) {
                log.error("createPortMapping失败: 获取端口请求返回为空");
                return null;
            }

            log.info("createPortMapping: 获取端口返回" + response);
            Integer result = JSONObject.parseObject(response).getInteger("data");

            // 创建端口映射
            Map<String, Object> protMapping = new HashMap<>();
            protMapping.put("licenseId", licenseId);
            protMapping.put("licenseName", name);
            protMapping.put("clientIp", asset.getHostname().replaceAll("^(https?://)", ""));
            protMapping.put("clientPort", asset.getProtocolProd());
            protMapping.put("protocal", "TCP");
            protMapping.put("prodType", "API");
            protMapping.put("serverPort", result);

            String protMappingRes = HttpUtil.post(proxyUrl + "/port-mapping/create", protMapping);
            if (protMappingRes == null) {
                log.error("createPortMapping失败: 创建端口映射请求返回为空");
                return null;
            }

            Long mappingId = JSONObject.parseObject(protMappingRes).getJSONObject("data").getLong("id");
            return PortMappingResult.builder()
                    .port(Convert.toStr(result))
                    .mappingId(mappingId)
                    .build();

        } catch (Exception e) {
            log.error("createPortMapping发生异常: {}", e.getMessage(), e);
            return null;
        }
    }

    @SneakyThrows
    private Map<String, String> getParams(String queryString) {
        Map<String, String> queryMap = new HashMap<>();
        String[] pairs = queryString.split("&");

        for (String pair : pairs) {
            int idx = pair.indexOf("=");
            if (idx > 0 && idx < pair.length() - 1) {
                String key = URLDecoder.decode(pair.substring(0, idx), "UTF-8");
                String value = URLDecoder.decode(pair.substring(idx + 1), "UTF-8");
                queryMap.put(key, value);
            }
        }
        return queryMap;
    }

    @Override
    public void onClose(Session session, CloseReason closeReason) {
        Map<String, String> params = getParams(session.getQueryString());
        String uuid = Convert.toStr(params.get("uuid"));
        String id = Convert.toStr(params.get("id"));
        String userId = Convert.toStr(params.get("userId"));
        if (StringUtils.isEmpty(uuid)) {
            return;
        }

        super.onClose(session, closeReason);
        tunnels.remove(session.getId());
        guacamoleMonitorSocket.disconnect(uuid, closeReason);
        sessions.remove(uuid, session);

        LocalDateTime disconnectedTime = DateUtil.toLocalDateTime(new Date());
        boolean isCloudVnc = StrUtil.isNotEmpty(params.get("type")) &&
                Convert.toStr(params.get("type")).equals("2");

        // 删除端口映射
        Long portMappingId = onlineSessionMapper.selectPordMappingIdByUuid(uuid);
        if (portMappingId != null) {
            HttpUtil.post(proxyUrl + "/port-mapping/delete",
                    Collections.singletonMap("id", portMappingId));
        }

        if (isCloudVnc) {
            // 云主机VNC断开处理
            OnlineSessionDO onlineSessionDO = onlineSessionMapper.selectById(uuid);
            onlineSessionMapper.updateHostByUuid(onlineSessionDO.getAssetId(), disconnectedTime);
            onlineSessionMapper.deleteAssetByUserId(onlineSessionDO.getAssetId(), userId);
        } else {
            // 普通资产断开处理
            onlineSessionMapper.deleteAssetByUserId(id, userId);
            onlineSessionMapper.updateAssetByUuid(id, disconnectedTime);
        }

        onlineSessionMapper.updateStatusByUuid(uuid, disconnectedTime);
    }

    public static ConfiguredGuacamoleSocket getTunnel(String sessionId) {
        return tunnels.get(sessionId);
    }

    public static void closeGuacamle(String connectionId) {
        String redisKey = "websocket:" + connectionId;
        if (redisTemplate.hasKey(redisKey)) {
            String sessionInfoJson = redisTemplate.opsForValue().get(redisKey).toString();
            Map<String, String> sessionInfo = JsonUtils.parseObject(sessionInfoJson, Map.class);

            // 从 map 中移除关闭的隧道
            tunnels.remove(sessionInfo.get("sessionId"));
            String uuid = Convert.toStr(sessionInfo.get("uuid"));
            String id = Convert.toStr(sessionInfo.get("id"));
            String userId = Convert.toStr(sessionInfo.get("userId"));
            if (StringUtils.isEmpty(uuid)) {
                return;
            }

            sessions.remove(uuid);
            LocalDateTime disconnectedTime = DateUtil.toLocalDateTime(new Date());
            onlineSessionMapper.updateStatusByUuid(uuid, disconnectedTime);
            onlineSessionMapper.deleteAssetByUserId(id, userId);
            onlineSessionMapper.updateHostByUuid(id, disconnectedTime);
            // 清理Redis中的数据
            redisTemplate.delete(redisKey);
        }
    }


    private Integer configureLocalPort() {
        try (ServerSocket socket = new ServerSocket(0)) {
            Integer localPort = socket.getLocalPort();
            log.info("随机可用端口: {}", localPort);
            return localPort;
        } catch (IOException e) {
            log.error("无法找到可用端口: {}", e.getMessage());
        }
        return null;
    }

    private void createTCPServer(VncInfoDTO vncInfo, String uid) {
        // 获取随机可用端口
        Integer localPort = configureLocalPort();
        if (localPort == null) {
            log.error("无法获取可用端口");
        }
        vncInfo.setTcpPort(localPort);
        ThreadPool.asyn(() -> {
            VNCWebSocketProxy.addNewProxy(vncInfo.getWebsocketPath(), vncInfo.getVncToken(), vncInfo.getTcpPort(), uid);
        });
    }
}
