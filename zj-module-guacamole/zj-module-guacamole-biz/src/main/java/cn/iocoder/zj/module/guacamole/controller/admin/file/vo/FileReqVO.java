package cn.iocoder.zj.module.guacamole.controller.admin.file.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class FileReqVO {
    @Schema(description = "session id", example = "1")
    private String snid;
    @Schema(description = "协议类型", example = "ssh or rdp")
    private String protocol;
    @Schema(description = "文件路径 创建操作需传值")
    private String dir;
}
