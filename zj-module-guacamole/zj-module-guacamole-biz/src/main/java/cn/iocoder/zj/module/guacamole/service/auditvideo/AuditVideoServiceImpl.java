package cn.iocoder.zj.module.guacamole.service.auditvideo;

import cn.iocoder.zj.module.guacamole.convert.auditvideo.AuditVideoConvert;
import cn.iocoder.zj.module.guacamole.dal.dataobject.onlinesession.OnlineSessionDO;
import cn.iocoder.zj.module.guacamole.dal.mysql.auditvideo.AuditVideoMapper;
import cn.iocoder.zj.module.guacamole.controller.admin.auditvideo.vo.AuditVideoCreateReqVO;
import cn.iocoder.zj.module.guacamole.controller.admin.auditvideo.vo.AuditVideoExportReqVO;
import cn.iocoder.zj.module.guacamole.controller.admin.auditvideo.vo.AuditVideoPageReqVO;
import cn.iocoder.zj.module.guacamole.controller.admin.auditvideo.vo.AuditVideoUpdateReqVO;
import cn.iocoder.zj.module.guacamole.dal.mysql.onlinesession.OnlineSessionMapper;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.io.File;
import java.util.*;
import cn.iocoder.zj.module.guacamole.dal.dataobject.auditvideo.AuditVideoDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;




import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 堡垒机视频录像 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AuditVideoServiceImpl implements AuditVideoService {

    @Resource
    private AuditVideoMapper auditVideoMapper;
    @Resource
    OnlineSessionMapper onlineSessionMapper;

    @Override
    public Long createAuditVideo(AuditVideoCreateReqVO createReqVO) {
        // 插入
        AuditVideoDO auditVideo = AuditVideoConvert.INSTANCE.convert(createReqVO);
        auditVideoMapper.insert(auditVideo);
        // 返回
        return auditVideo.getId();
    }

    @Override
    public void updateAuditVideo(AuditVideoUpdateReqVO updateReqVO) {
        // 校验存在
        validateAuditVideoExists(updateReqVO.getId());
        // 更新
        AuditVideoDO updateObj = AuditVideoConvert.INSTANCE.convert(updateReqVO);
        auditVideoMapper.updateById(updateObj);
    }

    @Override
    public void deleteAuditVideo(Long id) {
        // 校验存在
        validateAuditVideoExists(id);
        // 删除
        auditVideoMapper.deleteById(id);
    }

    private void validateAuditVideoExists(Long id) {
        if (auditVideoMapper.selectById(id) == null) {
            throw exception(500);
        }
    }

    @Override
    public AuditVideoDO getAuditVideo(Long id) {
        return auditVideoMapper.selectById(id);
    }

    @Override
    public List<AuditVideoDO> getAuditVideoList(Collection<Long> ids) {
        return auditVideoMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<AuditVideoDO> getAuditVideoPage(AuditVideoPageReqVO pageReqVO) {
        return auditVideoMapper.selectPage(pageReqVO);
    }

    @Override
    public List<AuditVideoDO> getAuditVideoList(AuditVideoExportReqVO exportReqVO) {
        return auditVideoMapper.selectList(exportReqVO);
    }

    @Override
    public File getVideoFile(String id) {
        OnlineSessionDO onlineSessionDO = onlineSessionMapper.selectById(id);
        if (onlineSessionDO == null) {
            return null;
        }
        File file = new File(onlineSessionDO.getFilePath());
        if (file.exists()) {
            return file;
        }
        return null;
    }

}
