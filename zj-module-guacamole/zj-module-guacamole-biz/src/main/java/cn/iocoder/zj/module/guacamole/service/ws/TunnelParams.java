package cn.iocoder.zj.module.guacamole.service.ws;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class TunnelParams {
    private String type;
    private String id;
    private String userId;
    private String uuid;
    private String width;
    private String height;
    private String host;
    private AdminUserRespDTO user;

    public boolean isCloudVncType() {
        return StrUtil.isNotEmpty(type) && type.equals("2");
    }
}
