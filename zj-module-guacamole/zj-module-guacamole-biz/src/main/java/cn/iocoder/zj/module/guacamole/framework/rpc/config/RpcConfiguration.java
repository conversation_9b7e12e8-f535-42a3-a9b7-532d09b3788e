package cn.iocoder.zj.module.guacamole.framework.rpc.config;


import cn.iocoder.cloud.module.cloudedge.api.monitor.MonitorApi;
import cn.iocoder.zj.module.infra.api.file.FileApi;
import cn.iocoder.zj.module.om.api.asset.MonitorassetApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.usercertificate.UsercertificateApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName : RpcConfiguration  //类名
 * @Description : RPC 接口  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/23  10:38
 */

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {MonitorApi.class, AdminUserApi.class,
        MonitorassetApi.class,
        UsercertificateApi.class,
        FileApi.class, PlatformconfigApi.class})
public class RpcConfiguration {
}
