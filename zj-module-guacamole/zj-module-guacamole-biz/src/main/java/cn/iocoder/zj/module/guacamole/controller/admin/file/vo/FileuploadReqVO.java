package cn.iocoder.zj.module.guacamole.controller.admin.file.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * @ClassName : FileUploadReqVO  //类名
 * @Description : 上传文件  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/7/12  11:12
 */
@Data
public class FileuploadReqVO {

    @Schema(description = "session id", example = "1")
    private String snid;
    @Schema(description = "文件路径", example = "/")
    private String targetPath;
    @Schema(description = "协议类型", example = "ssh")
    private String protocol;
}
