package cn.iocoder.zj.module.guacamole.framework.rpc.ws.config;

import org.springframework.web.socket.server.standard.ServerEndpointExporter;

import javax.websocket.server.ServerContainer;

/**
 * @ClassName : GuacamoleEndpointExporter  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/6/7  16:54
 */
public class GuacamoleEndpointExporter  extends ServerEndpointExporter {

    public ServerContainer getServerContainer() {
        return super.getServerContainer();
    }
}
