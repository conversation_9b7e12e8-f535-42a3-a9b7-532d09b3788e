package cn.iocoder.zj.module.guacamole.framework.rpc.ws.config;


import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName : WebSocketConfig  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/5/8  16:27
 */
@Configuration
public class WebSocketConfig {
    @Bean
    public GuacamoleEndpointExporter serverEndpointExporter(){
        return  new GuacamoleEndpointExporter();
    }




}
