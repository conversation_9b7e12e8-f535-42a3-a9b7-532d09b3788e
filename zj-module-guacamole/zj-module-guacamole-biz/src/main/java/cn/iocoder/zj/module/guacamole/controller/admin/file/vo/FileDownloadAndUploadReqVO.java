package cn.iocoder.zj.module.guacamole.controller.admin.file.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

@Data
public class FileDownloadAndUploadReqVO {
    @Schema(description = "session id", example = "1")
    private String snid;
    @Schema(description = "文件路径", example = "/")
    private String targetPath;
    @Schema(description = "协议类型", example = "ssh")
    private String protocol;
    @Schema(description = "文件附件")
    private MultipartFile file;
}
