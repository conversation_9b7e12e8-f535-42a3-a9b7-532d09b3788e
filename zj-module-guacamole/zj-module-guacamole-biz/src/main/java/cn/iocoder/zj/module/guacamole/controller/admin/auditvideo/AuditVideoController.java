package cn.iocoder.zj.module.guacamole.controller.admin.auditvideo;

import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.guacamole.controller.admin.onlinesession.vo.OnlineSessionPageReqVO;
import cn.iocoder.zj.module.guacamole.controller.admin.onlinesession.vo.OnlineSessionRespVO;
import cn.iocoder.zj.module.guacamole.convert.auditvideo.AuditVideoConvert;
import cn.iocoder.zj.module.guacamole.convert.onlinesession.OnlineSessionConvert;
import cn.iocoder.zj.module.guacamole.dal.dataobject.onlinesession.OnlineSessionDO;
import cn.iocoder.zj.module.guacamole.service.auditvideo.AuditVideoService;
import cn.iocoder.zj.module.guacamole.controller.admin.auditvideo.vo.*;
import cn.iocoder.zj.module.guacamole.service.onlinesession.OnlineSessionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import javax.validation.*;
import javax.servlet.http.*;
import java.io.*;
import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;


import cn.iocoder.zj.module.guacamole.dal.dataobject.auditvideo.AuditVideoDO;


@Tag(name = "管理后台 - 历史会话")
@RestController
@RequestMapping("/guacamole/audit-video")
@Validated
public class AuditVideoController {

    private static final Logger logger = LoggerFactory.getLogger(AuditVideoController.class);

    @Resource
    private OnlineSessionService onlineSessionService;

    @Resource
    private AuditVideoService auditVideoService;


    @GetMapping("/page")
    @Operation(summary = "历史会话分页")
    @PreAuthorize("@ss.hasPermission('guacamole:audit-video:query')")
    public CommonResult<PageResult<OnlineSessionRespVO>> getOnlineSessionPage(@Valid OnlineSessionPageReqVO pageVO) {
        PageResult<OnlineSessionDO> pageResult = onlineSessionService.getOnlineSessionVideoPage(pageVO);
        return success(OnlineSessionConvert.INSTANCE.convertPage(pageResult));
    }


    @GetMapping("/recording")
    @Operation(summary = "堡垒机回放")
    @PermitAll
    @TenantIgnore
    public void recording(HttpServletRequest request, HttpServletResponse response) {
        try {
            String id = request.getParameter("id");
            File file = auditVideoService.getVideoFile(id);

            if (file == null) {
                logger.error("not find file info by uuid:{}", id);
                return;
            }
            response.setContentType("application/force-download");
            response.addHeader("Content-Length", file.length() + "");
            response.addHeader("Content-Disposition", "attachment;fileName=" + java.net.URLEncoder.encode(file.getName(), "UTF-8"));
            byte[] buffer = new byte[1024];
            FileInputStream fis = null;
            BufferedInputStream bis = null;
            try {
                fis = new FileInputStream(file);
                bis = new BufferedInputStream(fis);
                OutputStream os = response.getOutputStream();
                int i = bis.read(buffer);
                while (i != -1) {
                    os.write(buffer, 0, i);
                    i = bis.read(buffer);
                }
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            } finally {
                if (bis != null) {
                    try {
                        bis.close();
                    } catch (Exception e) {
                        logger.error(e.getMessage(), e);
                    }
                }
                if (fis != null) {
                    try {
                        fis.close();
                    } catch (Exception e) {
                        logger.error(e.getMessage(), e);
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }


    }

    @DeleteMapping("/delete")
    @Operation(summary = "历史会话删除")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('guacamole:audit-video:delete')")
    public CommonResult<Boolean> deleteOnlineSession(@RequestParam("id") String id) {
        onlineSessionService.deleteOnlineSession(id);
        return success(true);
    }


//    @Resource
//    private AuditVideoService auditVideoService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建堡垒机视频录像")
//    @PreAuthorize("@ss.hasPermission('guacamole:audit-video:create')")
//    public CommonResult<Long> createAuditVideo(@Valid @RequestBody AuditVideoCreateReqVO createReqVO) {
//        return success(auditVideoService.createAuditVideo(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新堡垒机视频录像")
//    @PreAuthorize("@ss.hasPermission('guacamole:audit-video:update')")
//    public CommonResult<Boolean> updateAuditVideo(@Valid @RequestBody AuditVideoUpdateReqVO updateReqVO) {
//        auditVideoService.updateAuditVideo(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除堡垒机视频录像")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('guacamole:audit-video:delete')")
//    public CommonResult<Boolean> deleteAuditVideo(@RequestParam("id") Long id) {
//        auditVideoService.deleteAuditVideo(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得堡垒机视频录像")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('guacamole:audit-video:query')")
//    public CommonResult<AuditVideoRespVO> getAuditVideo(@RequestParam("id") Long id) {
//        AuditVideoDO auditVideo = auditVideoService.getAuditVideo(id);
//        return success(AuditVideoConvert.INSTANCE.convert(auditVideo));
//    }
//
//    @GetMapping("/list")
//    @Operation(summary = "获得堡垒机视频录像列表")
//    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
//    @PreAuthorize("@ss.hasPermission('guacamole:audit-video:query')")
//    public CommonResult<List<AuditVideoRespVO>> getAuditVideoList(@RequestParam("ids") Collection<Long> ids) {
//        List<AuditVideoDO> list = auditVideoService.getAuditVideoList(ids);
//        return success(AuditVideoConvert.INSTANCE.convertList(list));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得堡垒机视频录像分页")
//    @PreAuthorize("@ss.hasPermission('guacamole:audit-video:query')")
//    public CommonResult<PageResult<AuditVideoRespVO>> getAuditVideoPage(@Valid AuditVideoPageReqVO pageVO) {
//        PageResult<AuditVideoDO> pageResult = auditVideoService.getAuditVideoPage(pageVO);
//        return success(AuditVideoConvert.INSTANCE.convertPage(pageResult));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出堡垒机视频录像 Excel")
//    @PreAuthorize("@ss.hasPermission('guacamole:audit-video:export')")
//    @OperateLog(type = EXPORT)
//    public void exportAuditVideoExcel(@Valid AuditVideoExportReqVO exportReqVO,
//              HttpServletResponse response) throws IOException {
//        List<AuditVideoDO> list = auditVideoService.getAuditVideoList(exportReqVO);
//        // 导出 Excel
//        List<AuditVideoExcelVO> datas = AuditVideoConvert.INSTANCE.convertList02(list);
//        ExcelUtils.write(response, "堡垒机视频录像.xls", "数据", AuditVideoExcelVO.class, datas);
//    }

}
