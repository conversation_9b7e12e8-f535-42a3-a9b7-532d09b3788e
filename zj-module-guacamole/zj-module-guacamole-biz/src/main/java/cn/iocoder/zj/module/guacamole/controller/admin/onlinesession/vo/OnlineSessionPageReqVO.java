package cn.iocoder.zj.module.guacamole.controller.admin.onlinesession.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 会话审计分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OnlineSessionPageReqVO extends PageParam {

    @Schema(description = "来源ip")
    private String clientIp;

    @Schema(description = "资产id")
    private String assetId;

    @Schema(description = "资产名称")
    private String assetName;

    @Schema(description = "连接协议")
    private String protocol;

    @Schema(description = "ip地址")
    private String ip;

    @Schema(description = "端口号")
    private String port;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "接入时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] connectedTime;

    @Schema(description = "连接id")
    private String connectionId;

    @Schema(description = "高")
    private String height;

    @Schema(description = "宽")
    private String width;

    @Schema(description = "用户名称")
    private String userName;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "在线状态")
    private String status;

    @Schema(description = "文件地址")
    private String filePath;

    @Schema(description = "断开时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] disconnectedTime;

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String startTime;

    @Schema(description = "结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String endTime;

}
