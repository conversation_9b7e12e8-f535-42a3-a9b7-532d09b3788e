package cn.iocoder.zj.module.guacamole.dal.mysql.auditvideo;

import java.util.*;

import cn.iocoder.zj.module.guacamole.controller.admin.auditvideo.vo.AuditVideoExportReqVO;
import cn.iocoder.zj.module.guacamole.controller.admin.auditvideo.vo.AuditVideoPageReqVO;
import cn.iocoder.zj.module.guacamole.dal.dataobject.auditvideo.AuditVideoDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;

/**
 * 堡垒机视频录像 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AuditVideoMapper extends BaseMapperX<AuditVideoDO> {

    default PageResult<AuditVideoDO> selectPage(AuditVideoPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AuditVideoDO>()
                .eqIfPresent(AuditVideoDO::getDeviceId, reqVO.getDeviceId())
                .eqIfPresent(AuditVideoDO::getHost, reqVO.getHost())
                .likeIfPresent(AuditVideoDO::getName, reqVO.getName())
                .eqIfPresent(AuditVideoDO::getPath, reqVO.getPath())
                .betweenIfPresent(AuditVideoDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AuditVideoDO::getId));
    }

    default List<AuditVideoDO> selectList(AuditVideoExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AuditVideoDO>()
                .eqIfPresent(AuditVideoDO::getDeviceId, reqVO.getDeviceId())
                .eqIfPresent(AuditVideoDO::getHost, reqVO.getHost())
                .likeIfPresent(AuditVideoDO::getName, reqVO.getName())
                .eqIfPresent(AuditVideoDO::getPath, reqVO.getPath())
                .betweenIfPresent(AuditVideoDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AuditVideoDO::getId));
    }

}
