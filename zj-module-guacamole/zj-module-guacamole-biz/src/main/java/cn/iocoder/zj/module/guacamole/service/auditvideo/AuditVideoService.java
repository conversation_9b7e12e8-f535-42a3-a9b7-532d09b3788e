package cn.iocoder.zj.module.guacamole.service.auditvideo;

import java.io.File;
import java.util.*;
import javax.validation.*;

import cn.iocoder.zj.module.guacamole.controller.admin.auditvideo.vo.AuditVideoCreateReqVO;
import cn.iocoder.zj.module.guacamole.controller.admin.auditvideo.vo.AuditVideoExportReqVO;
import cn.iocoder.zj.module.guacamole.controller.admin.auditvideo.vo.AuditVideoPageReqVO;
import cn.iocoder.zj.module.guacamole.controller.admin.auditvideo.vo.AuditVideoUpdateReqVO;
import cn.iocoder.zj.module.guacamole.dal.dataobject.auditvideo.AuditVideoDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 堡垒机视频录像 Service 接口
 *
 * <AUTHOR>
 */
public interface AuditVideoService {

    /**
     * 创建堡垒机视频录像
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAuditVideo(@Valid AuditVideoCreateReqVO createReqVO);

    /**
     * 更新堡垒机视频录像
     *
     * @param updateReqVO 更新信息
     */
    void updateAuditVideo(@Valid AuditVideoUpdateReqVO updateReqVO);

    /**
     * 删除堡垒机视频录像
     *
     * @param id 编号
     */
    void deleteAuditVideo(Long id);

    /**
     * 获得堡垒机视频录像
     *
     * @param id 编号
     * @return 堡垒机视频录像
     */
    AuditVideoDO getAuditVideo(Long id);

    /**
     * 获得堡垒机视频录像列表
     *
     * @param ids 编号
     * @return 堡垒机视频录像列表
     */
    List<AuditVideoDO> getAuditVideoList(Collection<Long> ids);

    /**
     * 获得堡垒机视频录像分页
     *
     * @param pageReqVO 分页查询
     * @return 堡垒机视频录像分页
     */
    PageResult<AuditVideoDO> getAuditVideoPage(AuditVideoPageReqVO pageReqVO);

    /**
     * 获得堡垒机视频录像列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 堡垒机视频录像列表
     */
    List<AuditVideoDO> getAuditVideoList(AuditVideoExportReqVO exportReqVO);

    File getVideoFile(String id);
}
