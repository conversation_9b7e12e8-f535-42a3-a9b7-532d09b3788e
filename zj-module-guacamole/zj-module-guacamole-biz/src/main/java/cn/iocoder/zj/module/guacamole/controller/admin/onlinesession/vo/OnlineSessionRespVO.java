package cn.iocoder.zj.module.guacamole.controller.admin.onlinesession.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 会话审计 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OnlineSessionRespVO extends OnlineSessionBaseVO {

    @Schema(description = "主键", required = true)
    private String id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
