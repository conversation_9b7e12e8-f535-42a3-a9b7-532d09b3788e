package cn.iocoder.zj.module.guacamole.service.ws;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.cloud.module.cloudedge.api.monitor.MonitorApi;
import cn.iocoder.zj.module.guacamole.dal.dataobject.onlinesession.OnlineSessionDO;
import cn.iocoder.zj.module.guacamole.dal.mysql.onlinesession.OnlineSessionMapper;
import lombok.SneakyThrows;
import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.net.GuacamoleSocket;
import org.apache.guacamole.net.GuacamoleTunnel;
import org.apache.guacamole.net.InetGuacamoleSocket;
import org.apache.guacamole.net.SimpleGuacamoleTunnel;
import org.apache.guacamole.protocol.ConfiguredGuacamoleSocket;
import org.apache.guacamole.protocol.GuacamoleClientInformation;
import org.apache.guacamole.protocol.GuacamoleConfiguration;
import org.apache.guacamole.websocket.GuacamoleWebSocketTunnelEndpoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.websocket.CloseReason;
import javax.websocket.EndpointConfig;
import javax.websocket.Session;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName : GuacamoleMonitor  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/6/11  15:48
 */
@Component
public class GuacamoleMonitorSocket extends GuacamoleWebSocketTunnelEndpoint {

    private static final Logger logger = LoggerFactory.getLogger(GuacamoleMonitorSocket.class);
    public static MonitorApi monitorApi;

    public static String GUAC_HOSTNAME = "";
    public static Integer GUAC_PORT = 0;
    public static OnlineSessionMapper onlineSessionMapper;
    public static Map<String, ConfiguredGuacamoleSocket> tunnels;
    public static Map<String, Session> monitorSessions;

    @Override
    protected GuacamoleTunnel createTunnel(Session session, EndpointConfig endpointConfig) throws GuacamoleException {
        Map<String, List<String>> map = session.getRequestParameterMap();
        logger.info("sessionMap:{}", map);
        // 获取url的值
        // 可添加下方代码接收参数
        // 通过前端Vue或者其他前端语言请求后端时动态添加参数可使得下方远程windows服务地址动态注入
        GuacamoleConfiguration configuration = new GuacamoleConfiguration();

        Map<String, String> map1 = getParams(session.getQueryString());
        // 移除可能的"undefined"
        map1.remove("undefined");
        Long monitorId = Convert.toLong(map1.get("monitorId"));
        String uuid = Convert.toStr(map1.get("uuid"));


        OnlineSessionDO onlineSessionDO = onlineSessionMapper.selectById(uuid);
        String hostname = "";
        String port = "";
        String username = "";
        String password = "";
        String protocol = "";

        // 标识是否已找到port，防止在不存在protocol_port时多次设置port值
        boolean portFound = false;


        String wid = StrUtil.toString(map1.get("wid"));
        String hei = StrUtil.toString(map1.get("hei"));

//        configuration.setParameter("hostname", hostname);
//        configuration.setParameter("port", port);
//        configuration.setParameter("username", username);
        configuration.setParameter("read-only", "true");
        configuration.setConnectionID(onlineSessionDO.getConnectionId());

        GuacamoleClientInformation information = new GuacamoleClientInformation();
        information.setOptimalScreenHeight(Integer.parseInt(hei));
        information.setOptimalScreenWidth(Integer.parseInt(wid));


        GuacamoleSocket socket = new ConfiguredGuacamoleSocket(
                new InetGuacamoleSocket(GUAC_HOSTNAME, GUAC_PORT),
                configuration, information
        );
        monitorSessions.put(uuid, session);
        GuacamoleTunnel tunnel = new SimpleGuacamoleTunnel(socket);
        return tunnel;
    }


    @Override
    public void onClose(Session session, CloseReason closeReason) {
        super.onClose(session, closeReason);
    }


    public void disconnect(String uuid, CloseReason closeReason) {
        Session session = monitorSessions.get(uuid);
        if (session != null) {
            try {
                onClose(session, closeReason);
                session.close(closeReason);
            } catch (IOException e) {
                logger.error("Error closing monitor session: {}", e.getMessage());
            }
            monitorSessions.remove(uuid, session);
        }
    }

    @SneakyThrows
    private Map<String, String> getParams(String queryString) {
        Map<String, String> queryMap = new HashMap<>();
        String[] pairs = queryString.split("&");

        for (String pair : pairs) {
            int idx = pair.indexOf("=");
            if (idx > 0 && idx < pair.length() - 1) {
                String key = URLDecoder.decode(pair.substring(0, idx), "UTF-8");
                String value = URLDecoder.decode(pair.substring(idx + 1), "UTF-8");
                queryMap.put(key, value);
            }
        }
        return queryMap;
    }
}
