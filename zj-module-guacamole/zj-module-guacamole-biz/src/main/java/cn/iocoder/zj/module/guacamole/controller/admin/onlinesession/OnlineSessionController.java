package cn.iocoder.zj.module.guacamole.controller.admin.onlinesession;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.module.guacamole.controller.admin.onlinesession.vo.OnlineSessionPageReqVO;
import cn.iocoder.zj.module.guacamole.controller.admin.onlinesession.vo.OnlineSessionRespVO;
import cn.iocoder.zj.module.guacamole.convert.onlinesession.OnlineSessionConvert;
import cn.iocoder.zj.module.guacamole.dal.dataobject.onlinesession.OnlineSessionDO;
import cn.iocoder.zj.module.guacamole.service.onlinesession.OnlineSessionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.CREATE;
import static cn.iocoder.zj.module.infra.enums.ErrorCodeConstants.FILE_IS_EMPTY;

@Tag(name = "管理后台 - 会话审计")
@RestController
@RequestMapping("/guacamole/online-session")
@Validated
public class OnlineSessionController {

    @Resource
    private OnlineSessionService onlineSessionService;

//    @PostMapping("/create")
//    @Operation(summary = "创建会话审计")
//    @PreAuthorize("@ss.hasPermission('guacamole:online-session:create')")
//    public CommonResult<String> createOnlineSession(@Valid @RequestBody OnlineSessionCreateReqVO createReqVO) {
//        return success(onlineSessionService.createOnlineSession(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新会话审计")
//    @PreAuthorize("@ss.hasPermission('guacamole:online-session:update')")
//    public CommonResult<Boolean> updateOnlineSession(@Valid @RequestBody OnlineSessionUpdateReqVO updateReqVO) {
//        onlineSessionService.updateOnlineSession(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除会话审计")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('guacamole:online-session:delete')")
//    public CommonResult<Boolean> deleteOnlineSession(@RequestParam("id") String id) {
//        onlineSessionService.deleteOnlineSession(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得会话审计")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('guacamole:online-session:query')")
//    public CommonResult<OnlineSessionRespVO> getOnlineSession(@RequestParam("id") String id) {
//        OnlineSessionDO onlineSession = onlineSessionService.getOnlineSession(id);
//        return success(OnlineSessionConvert.INSTANCE.convert(onlineSession));
//    }

//    @GetMapping("/list")
//    @Operation(summary = "获得会话审计列表")
//    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
//    @PreAuthorize("@ss.hasPermission('guacamole:online-session:query')")
//    public CommonResult<List<OnlineSessionRespVO>> getOnlineSessionList(@RequestParam("ids") Collection<String> ids) {
//        List<OnlineSessionDO> list = onlineSessionService.getOnlineSessionList(ids);
//        return success(OnlineSessionConvert.INSTANCE.convertList(list));
//    }

    @GetMapping("/page")
    @Operation(summary = "在线会话分页")
    @PreAuthorize("@ss.hasPermission('guacamole:online-session:query')")
    public CommonResult<PageResult<OnlineSessionRespVO>> getOnlineSessionPage(@Valid OnlineSessionPageReqVO pageVO) {
        PageResult<OnlineSessionDO> pageResult = onlineSessionService.getOnlineSessionPage(pageVO);
        return success(OnlineSessionConvert.INSTANCE.convertPage(pageResult));
    }

//    @GetMapping("/export-excel")
//    @Operation(summary = "导出会话审计 Excel")
//    @PreAuthorize("@ss.hasPermission('guacamole:online-session:export')")
//    @OperateLog(type = EXPORT)
//    public void exportOnlineSessionExcel(@Valid OnlineSessionExportReqVO exportReqVO,
//              HttpServletResponse response) throws IOException {
//        List<OnlineSessionDO> list = onlineSessionService.getOnlineSessionList(exportReqVO);
//        // 导出 Excel
//        List<OnlineSessionExcelVO> datas = OnlineSessionConvert.INSTANCE.convertList02(list);
//        ExcelUtils.write(response, "会话审计.xls", "数据", OnlineSessionExcelVO.class, datas);
//    }

    @PostMapping("/video")
    @Operation(summary = "上传录制视频")
    @OperateLog(type=CREATE)
    @PreAuthorize("@ss.hasPermission('guacamole:online-session:query')")
    public CommonResult<String> uploadVideo(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw exception(FILE_IS_EMPTY);
        }
        String avatar = onlineSessionService.uploadVideo(file);
        return success(avatar);
    }


}
