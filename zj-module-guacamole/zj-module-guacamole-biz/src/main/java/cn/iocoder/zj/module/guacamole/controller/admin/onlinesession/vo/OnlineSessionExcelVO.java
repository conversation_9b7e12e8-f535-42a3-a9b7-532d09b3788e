package cn.iocoder.zj.module.guacamole.controller.admin.onlinesession.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 会话审计 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class OnlineSessionExcelVO {

    @ExcelProperty("主键")
    private String id;

    @ExcelProperty("来源ip")
    private String clientIp;

    @ExcelProperty("资产id")
    private String assetId;

    @ExcelProperty("资产名称")
    private String assetName;

    @ExcelProperty("连接协议")
    private String protocol;

    @ExcelProperty("ip地址")
    private String ip;

    @ExcelProperty("端口号")
    private String port;

    @ExcelProperty("平台id")
    private Long platformId;

    @ExcelProperty("用户id")
    private Long userId;

    @ExcelProperty("接入时间")
    private LocalDateTime connectedTime;

    @ExcelProperty("连接id")
    private String connectionId;

    @ExcelProperty("高")
    private String height;

    @ExcelProperty("宽")
    private String width;

    @ExcelProperty("用户名称")
    private String userName;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("在线状态")
    private String status;

    @ExcelProperty("文件地址")
    private String filePath;

}
