package cn.iocoder.zj.module.guacamole.dal.dataobject.auditvideo;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 堡垒机视频录像 DO
 *
 * <AUTHOR>
 */
@TableName("guacamole_audit_video")
@KeySequence("guacamole_audit_video_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuditVideoDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 主机ip
     */
    private String host;
    /**
     * 监控名称
     */
    private String name;
    /**
     * 文件地址
     */
    private String path;

}
