package cn.iocoder.zj.module.guacamole.controller.admin.auditvideo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 堡垒机视频录像 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class AuditVideoExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("设备id")
    private String deviceId;

    @ExcelProperty("主机ip")
    private String host;

    @ExcelProperty("监控名称")
    private String name;

    @ExcelProperty("文件地址")
    private String path;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
