package cn.iocoder.zj.module.guacamole.service.onlinesession;

import cn.hutool.core.io.IoUtil;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.guacamole.controller.admin.onlinesession.vo.OnlineSessionCreateReqVO;
import cn.iocoder.zj.module.guacamole.controller.admin.onlinesession.vo.OnlineSessionExportReqVO;
import cn.iocoder.zj.module.guacamole.controller.admin.onlinesession.vo.OnlineSessionPageReqVO;
import cn.iocoder.zj.module.guacamole.controller.admin.onlinesession.vo.OnlineSessionUpdateReqVO;
import cn.iocoder.zj.module.guacamole.convert.onlinesession.OnlineSessionConvert;
import cn.iocoder.zj.module.guacamole.dal.dataobject.onlinesession.OnlineSessionDO;
import cn.iocoder.zj.module.guacamole.dal.mysql.onlinesession.OnlineSessionMapper;
import cn.iocoder.zj.module.guacamole.utli.StringUtil;
import cn.iocoder.zj.module.infra.api.file.FileApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static cn.iocoder.cloud.module.guacamole.enums.ErrorCodeConstants.ONLINE_SESSION_NOT_EXISTS;
import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 会话审计 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class OnlineSessionServiceImpl implements OnlineSessionService {

    @Resource
    private OnlineSessionMapper onlineSessionMapper;
    @Resource
    private FileApi fileApi;
    @Autowired
    PlatformconfigApi platformconfigApi;

    @Override
    public String createOnlineSession(OnlineSessionCreateReqVO createReqVO) {
        // 插入
        OnlineSessionDO onlineSession = OnlineSessionConvert.INSTANCE.convert(createReqVO);
        onlineSessionMapper.insert(onlineSession);
        // 返回
        return onlineSession.getId();
    }

    @Override
    public void updateOnlineSession(OnlineSessionUpdateReqVO updateReqVO) {
        // 校验存在
        validateOnlineSessionExists(updateReqVO.getId());
        // 更新
        OnlineSessionDO updateObj = OnlineSessionConvert.INSTANCE.convert(updateReqVO);
        onlineSessionMapper.updateById(updateObj);
    }

    @Override
    public void deleteOnlineSession(String id) {
        // 校验存在
        validateOnlineSessionExists(id);
        // 删除
        onlineSessionMapper.deleteById(id);
    }

    private void validateOnlineSessionExists(String id) {
        if (onlineSessionMapper.selectById(id) == null) {
            throw exception(ONLINE_SESSION_NOT_EXISTS);
        }
    }

    @Override
    public OnlineSessionDO getOnlineSession(String id) {
        return onlineSessionMapper.selectById(id);
    }

    @Override
    public List<OnlineSessionDO> getOnlineSessionList(Collection<String> ids) {
        return onlineSessionMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<OnlineSessionDO> getOnlineSessionPage(OnlineSessionPageReqVO pageReqVO) {
        List<Map> platform = new ArrayList<>();
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
        return onlineSessionMapper.selectPage(pageReqVO,platform);
    }

    @Override
    public List<OnlineSessionDO> getOnlineSessionList(OnlineSessionExportReqVO exportReqVO) {
        return onlineSessionMapper.selectList(exportReqVO);
    }

    @Override
    public PageResult<OnlineSessionDO> getOnlineSessionVideoPage(OnlineSessionPageReqVO pageVO) {
        List<Map> platform = new ArrayList<>();
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();

        return onlineSessionMapper.selectPageVideo(pageVO,platform);
    }

    @Override
    public String uploadVideo(MultipartFile file) throws IOException {
        String d = StringUtil.getSavePath(file.getOriginalFilename(), "video");
        // 存储文件
        return fileApi.createFileUrl(file.getOriginalFilename(), d, IoUtil.readBytes(file.getInputStream()));
    }

    @Override
    public String selectNameByPlatformId(Long platformId) {
        return onlineSessionMapper.selectNameByPlatformId(platformId);
    }

    @Override
    public Long selectIdByName(String name) {
        return onlineSessionMapper.selectIdByName(name);
    }

}
