package cn.iocoder.zj.module.guacamole.convert.onlinesession;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.guacamole.controller.admin.onlinesession.vo.*;
import cn.iocoder.zj.module.guacamole.dal.dataobject.onlinesession.OnlineSessionDO;

/**
 * 会话审计 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface OnlineSessionConvert {

    OnlineSessionConvert INSTANCE = Mappers.getMapper(OnlineSessionConvert.class);

    OnlineSessionDO convert(OnlineSessionCreateReqVO bean);

    OnlineSessionDO convert(OnlineSessionUpdateReqVO bean);

    OnlineSessionRespVO convert(OnlineSessionDO bean);

    List<OnlineSessionRespVO> convertList(List<OnlineSessionDO> list);

    PageResult<OnlineSessionRespVO> convertPage(PageResult<OnlineSessionDO> page);

    List<OnlineSessionExcelVO> convertList02(List<OnlineSessionDO> list);

}
