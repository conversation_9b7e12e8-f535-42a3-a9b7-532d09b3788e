package cn.iocoder.zj.module.guacamole.controller.admin.file.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName : FilerenameReqVO  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/7/12  11:13
 */
@Data
public class FilerenameReqVO {
    @Schema(description = "session id", example = "1")
    private String snid;
    @Schema(description = "协议类型", example = "ssh or rdp")
    private String protocol;
    @Schema(description = "老文件名", example = "/123.sql")
    private String oldName;

    @Schema(description = "文件路径", example = "/1234.sql")
    private String newName;


}
