package cn.iocoder.zj.module.guacamole.controller.admin.file.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName : FilelsReqVO  //类名
 * @Description : 文件列表  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/7/12  11:09
 */

@Data
public class FilelsReqVO {
    @Schema(description = "session id", example = "1")
    private String snid;
    @Schema(description = "协议类型", example = "ssh or rdp")
    private String protocol;
    @Schema(description = "路径")
    private String dir;
}
