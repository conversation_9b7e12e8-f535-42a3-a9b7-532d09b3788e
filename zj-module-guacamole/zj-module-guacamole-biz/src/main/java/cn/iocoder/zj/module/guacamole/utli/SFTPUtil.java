package cn.iocoder.zj.module.guacamole.utli;

import cn.hutool.extra.ssh.Sftp;
import cn.iocoder.zj.module.guacamole.controller.admin.file.FileController;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.SftpException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.FileInputStream;
import java.io.InputStream;

/**
 * @ClassName : SFTPUtil  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/7/17  18:58
 */
@Component
public class SFTPUtil {
    private static final Logger log = LoggerFactory.getLogger(FileController.class);

    public void mkdirDir(String[] dirs, String tempPath, int length, int index, ChannelSftp sftp) {
        // 以"/a/b/c/d"为例按"/"分隔后,第0位是"";顾下标从1开始
        index++;
        if (index < length) {
            // 目录不存在，则创建文件夹
            tempPath += "/" + dirs[index];
        }
        try {
            log.info("检测目录[" + tempPath + "]");
            sftp.cd(tempPath);
            if (index < length) {
                mkdirDir(dirs, tempPath, length, index,sftp);
            }
        } catch (SftpException ex) {
            log.warn("创建目录[" + tempPath + "]");
            try {
                sftp.mkdir(tempPath);
                sftp.cd(tempPath);
            } catch (SftpException e) {
                e.printStackTrace();
                log.error("创建目录[" + tempPath + "]失败,异常信息[" + e.getMessage() + "]");
            }
            log.info("进入目录[" + tempPath + "]");
            mkdirDir(dirs, tempPath, length, index,sftp);
        }
    }


    public void uploadMore(String directory, String sftpFileName, InputStream input, ChannelSftp sftp) throws SftpException {
        try {
            // Ensure the directory path is correctly formatted
            directory = directory.replaceAll("//", "/");
            sftp.cd(directory);
        } catch (SftpException e) {
            // Directory does not exist, create it
            String[] dirs = directory.split("/");
            String tempPath = "";
            mkdirDir(dirs, tempPath, dirs.length, 0, sftp);
        }
        // Now attempt to upload the file
        sftp.put(input, sftpFileName);
    }



}
