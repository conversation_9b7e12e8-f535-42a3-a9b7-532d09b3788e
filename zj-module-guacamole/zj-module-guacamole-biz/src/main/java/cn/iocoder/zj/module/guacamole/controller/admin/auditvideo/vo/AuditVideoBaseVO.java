package cn.iocoder.zj.module.guacamole.controller.admin.auditvideo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 堡垒机视频录像 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class AuditVideoBaseVO {

    @Schema(description = "设备id")
    private String deviceId;

    @Schema(description = "主机ip")
    private String host;

    @Schema(description = "监控名称")
    private String name;

    @Schema(description = "文件地址")
    private String path;

}
