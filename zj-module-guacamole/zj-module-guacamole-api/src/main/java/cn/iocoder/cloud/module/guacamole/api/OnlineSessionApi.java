package cn.iocoder.cloud.module.guacamole.api;

import cn.iocoder.cloud.module.guacamole.enums.ApiConstants;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @ClassName : OnlineSessionApi  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/10/9  15:10
 */
@FeignClient(name = ApiConstants.NAME) // ① @FeignClient 注解
@Tag(name = "RPC 服务 - 资产相关") // ② Swagger 接口文档
public interface OnlineSessionApi {

    String PREFIX = ApiConstants.PREFIX + "/onlineSessions";

    @PostMapping(PREFIX + "/selectNameByPlatformId")
    @Operation(summary = "通过平台id获取采集器名称")
    CommonResult<String> selectNameByPlatformId(@RequestParam("platformId")Long platformId);


    @PostMapping(PREFIX + "/selectIdByName")
    @Operation(summary = "通过采集名称获取licenseId")
    CommonResult<Long> selectIdByName(@RequestParam("name")String name);

    @PostMapping(PREFIX + "/closeGuacamle")
    @Operation(summary = "关闭管道")
    void closeGuacamle(@RequestParam("connectionId")String connectionId);
}
