package cn.iocoder.cloud.module.guacamole.enums;

import cn.iocoder.zj.framework.common.exception.ErrorCode;

/**
 * System 错误码枚举类
 * <p>
 * system 监控，使用 6-002-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== HostInfo 模块 6002000000 ==========

    ErrorCode HOST_INFO_NOT_EXISTS = new ErrorCode(2002000000, "云主机基本信息不存在");
    ErrorCode ONLINE_SESSION_NOT_EXISTS = new ErrorCode(2002000001, "会话审计不存在");
}
