<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>cn.iocoder.cloud</groupId>
        <artifactId>zj-module-private</artifactId>
        <version>${revision}</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>cn.iocoder.zj</groupId>
    <artifactId>zj-module-private-common</artifactId>
    <packaging>jar</packaging>
    <name>${project.artifactId}</name>
    <description>私有化定制公共模块</description>

    <dependencies>
        <dependency>
            <groupId>cn.iocoder.cloud</groupId>
            <artifactId>zj-common</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.iocoder.cloud</groupId>
            <artifactId>zj-spring-boot-starter-biz-operatelog</artifactId>
        </dependency>
        <!-- Web 相关 -->
        <dependency>
            <groupId>cn.iocoder.cloud</groupId>
            <artifactId>zj-spring-boot-starter-web</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
    </dependencies>

</project>