package cn.iocoder.zj.module.common.controller.admin;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName : LicenceController  //类名
 * @Description : 监控授权  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/1/30  14:11
 */
@Tag(name = "管理后台 - 监控授权")
@RestController
@RequestMapping("/api/common/licence")
@Validated
public class LicenceController {


}
