<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Email Template</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }

        .container {
            width: 100%;
            padding: 20px;
            background-color: #ffffff;
            margin: 0 auto;
            max-width: 600px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .content {
            padding: 20px;
            box-sizing: border-box;
            width: 100%;
            text-align: center;
            display: flex;
            justify-content: center;
            text-align: left;
        }

        .content-box {
            width: 500px;
        }

        .image {
            width: 100%;
            max-width: 100%;
            height: auto;
        }

        .p1 {
            font-weight: 800;
            font-size: 18px;
            width: 100%;
            text-align: center;
            color: rgb(255, 102, 51);
        }

        .p2 {
            width: 100%;
            text-align: center;
        }

        .content-text {
            display: flex;
            justify-content: center;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header">
        <img th:src="${topImageUrl}" alt="Top Image" class="image"/>
    </div>
    <div class="content">
        <div class="content-box">
            <p class="p1">重要提醒</p>
            <p class="p2">您的云星辰运维管理平台</p>
            <p class="p2">授权将于 <span style="font-size: 20px;color:  rgb(255, 102, 51);" th:text="${day}+'天'"></span>后到期</p>
            <p class="p5">尊敬的用户，您好:</p>
            <p style=" text-indent: 4ch;"  th:text="'您的团队在云星辰运维管理平台中总共拥有' + ${assetNum} + '个资产授权，已用授权' + ${totalCount} + '个,剩余' + ${surplus} + '个，您的平台使用授权将于' + ${day} + '天后到期。授权到期后平台无法登录。为避免影响您的正常使用,请及时联系相关人员进行续费。'"></p>
            <p>到期时间: <span style="color:  rgb(255, 102, 51);;" th:text="${time}"></span> </p>
        </div>
    </div>
    <div class="footer">
        <img th:src="${bottomImageUrl}" alt="Bottom Image" class="image"/>
    </div>
</div>
</body>
</html>