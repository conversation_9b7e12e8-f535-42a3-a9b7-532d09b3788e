<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.system.dal.mysql.tenant.TenantMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="getTenantPage" resultType="cn.iocoder.zj.module.system.controller.admin.tenant.vo.tenant.TenantRespVO">
        SELECT ten.NAME,users.nickname contactName,users.mobile contactMobile,users.email contactEmail,ten.STATUS,ten.domain,ten.package_id,ten.expire_time,ten.account_count,
        ten.platform_json,ten.platform_names,ten.id,ten.create_time,ten.organization,ten.dept_str,ten.maintainer_id,
        ci.asset_num FROM
        system_tenant ten
        LEFT JOIN system_users users ON ten.contact_user_id = users.id
        LEFT JOIN customer_contract_info ci ON ten.id = ci.tenant_id
        where ten.deleted = 0 and ten.package_id !=0
        <if test="pageReqVO.name != null and pageReqVO.name !=''">
            and ten.name like concat("%",#{pageReqVO.name},"%")
        </if>
        <if test="pageReqVO.contactName != null and pageReqVO.contactName !=''">
            and users.nickname like concat("%",#{pageReqVO.contactName},"%")
        </if>
        <if test="pageReqVO.contactMobile != null and pageReqVO.contactMobile !=''">
            and users.mobile like concat("%",#{pageReqVO.contactMobile},"%")
        </if>
        <choose>
            <when test="pageReqVO.status == 0 || pageReqVO.status == 1">
                and ten.status = #{pageReqVO.status}
            </when>
        </choose>
        <if test="pageReqVO.packageId != null and pageReqVO.packageId !=''">
            and ten.package_id = #{pageReqVO.packageId}
        </if>
        <if test="pageReqVO.organization != null and pageReqVO.organization !=''">
            and ten.organization = #{pageReqVO.organization}
        </if>
        <if test="pageReqVO.startTime != '' and pageReqVO.endTime != ''and pageReqVO.startTime != null and pageReqVO.endTime != null">
            and DATE_FORMAT( ten.create_time, "%Y-%m-%d" )  between DATE_FORMAT( #{pageReqVO.startTime}, "%Y-%m-%d" ) and  DATE_FORMAT( #{pageReqVO.endTime}, "%Y-%m-%d" )
        </if>
        <if test="pageReqVO.maintainerIdList != null and pageReqVO.maintainerIdList.size > 0">
        AND (
        <foreach collection="pageReqVO.maintainerIdList" item="id" separator=" OR ">
            FIND_IN_SET(#{id}, ten.maintainer_id) > 0
        </foreach>
        )
        </if>
        ORDER BY
        <if test="pageReqVO.sortBy == null or pageReqVO.sortBy =='' or pageReqVO.sortDirection== null or pageReqVO.sortDirection ==''">
          ten.create_time DESC
        </if>
        <if test="pageReqVO.sortBy != null and pageReqVO.sortBy !='' and pageReqVO.sortDirection!= null and pageReqVO.sortDirection !=''">
            ten.${pageReqVO.sortBy} ${pageReqVO.sortDirection}
        </if>
    </select>

    <select id="getTenantExcelList" resultType="cn.iocoder.zj.module.system.controller.admin.tenant.vo.tenant.TenantExcelVO">
        SELECT ten.id,ten.NAME,users.nickname contactName,users.mobile contactMobile,users.email contactEmail,ten.STATUS,ten.create_time FROM
        system_tenant ten
        LEFT JOIN system_users users ON ten.contact_user_id = users.id
        where ten.deleted = 0
        <if test="exportReqVO.name != null and exportReqVO.name !=''">
            and ten.name like concat("%",#{exportReqVO.name},"%")
        </if>
        <if test="exportReqVO.contactName != null and exportReqVO.contactName !=''">
            and users.nickname like concat("%",#{exportReqVO.contactName},"%")
        </if>
        <if test="exportReqVO.contactMobile != null and exportReqVO.contactMobile !=''">
            and users.mobile like concat("%",#{exportReqVO.contactMobile},"%")
        </if>
        <choose>
            <when test="exportReqVO.status == 0 || exportReqVO.status == 1">
                and ten.status = #{exportReqVO.status}
            </when>
        </choose>
        <if test="exportReqVO.packageId != null and exportReqVO.packageId !=''">
            and ten.package_id = #{exportReqVO.packageId}
        </if>
        <if test="exportReqVO.startTime != '' and exportReqVO.endTime != ''and exportReqVO.startTime != null and exportReqVO.endTime != null">
            and DATE_FORMAT( ten.create_time, "%Y-%m-%d" )  between DATE_FORMAT( #{exportReqVO.startTime}, "%Y-%m-%d" ) and  DATE_FORMAT( #{exportReqVO.endTime}, "%Y-%m-%d" )
        </if>
        <if test="id != null and id !=''">
            and ten.id != #{id}
        </if>
        order by ten.id desc
    </select>

    <select id="getTenantById" resultType="cn.iocoder.zj.module.system.controller.admin.tenant.vo.tenant.TenantRespVO">
        SELECT ten.NAME,users.nickname contactName,users.mobile contactMobile,users.email contactEmail,users.username userName,ten.STATUS,ten.domain,ten.package_id,ten.expire_time,ten.account_count,
        ten.platform_json,ten.platform_names,ten.id,ten.create_time,ten.is_test,ten.organization,ten.dept_str,ten.maintainer_id,ten.maintainer_name
        <if test="id != null and id !='' and id !=1">
        ,con.contract_no,con.project_name
        </if>
         FROM
        system_tenant ten
        LEFT JOIN system_users users ON ten.contact_user_id = users.id
        <if test="id != null and id !='' and id !=1">
        LEFT JOIN customer_contract_info con ON ten.id = con.tenant_id and con.deleted=0 and con.is_test=1
        </if>
        where ten.deleted = 0 and ten.id=#{id}
    </select>

    <select id="getTestContractNo" resultType="string">
        SELECT con.contract_no FROM
        system_tenant ten
        LEFT JOIN customer_contract_info con ON ten.id = con.tenant_id and con.deleted=0
        where ten.deleted = 0 and ten.id=#{id}
    </select>

    <select id="getSuperIdByUser" resultType="java.lang.Long">
        SELECT st.id FROM system_users su
        LEFT JOIN system_tenant st on st.id = su.tenant_id
        WHERE su.id = #{id} and su.deleted = 0

    </select>

    <update id="updatePlatformJsonById">
        UPDATE system_tenant
        SET platform_json = #{modifiedJson},
            platform_names = #{platformNames}
        WHERE id = #{id}
    </update>

    <select id="selectUserByPlatformId" resultType="java.util.Map">
        SELECT
            p.id,
            p.platform_json,
            p.platform_names
        FROM
            system_tenant p
        WHERE
            p.deleted = 0
          AND p.platform_json IS NOT NULL
          AND p.platform_json != ''
            AND JSON_SEARCH(p.platform_json, 'one', CAST(#{id} AS CHAR), NULL, '$[*].platformId') IS NOT NULL
    </select>
    <select id="getTenantByRole"
            resultType="cn.iocoder.zj.module.system.controller.admin.tenant.vo.tenant.TenantUserRespVO">
        SELECT
        id,
        system_tenant.NAME AS NAME
        FROM
        system_tenant
        WHERE
        deleted =0
        <if test="list !=null and list.size()>0">
            and id in
            <foreach collection="list" open="(" separator="," close=")" item="list">
                #{list}
            </foreach>
        </if>
    </select>
    <select id="getOrderEnforcer" resultType="cn.iocoder.zj.module.system.dal.dataobject.tenant.TenantDO">
        SELECT
            tenant.*
        FROM
            system_tenant tenant
        WHERE
            1 = 1
          AND tenant.id IN ( SELECT spt.tenant_id FROM system_platform_tenant spt WHERE spt.platform_id = #{platformId} )
          AND tenant.deleted = 0
    </select>
    <select id="getTenantByPlatform"
            resultType="cn.iocoder.zj.module.system.controller.admin.tenant.vo.tenant.TenantUserRespVO">
        SELECT
            tenant.id,
            tenant.NAME AS NAME
        FROM
            system_tenant tenant
        WHERE
            1 = 1
        <if test="tenantId != null and tenantId != 1L">
          AND tenant.id IN (
            SELECT
                pla.tenant_id
            FROM
                system_platform_tenant pla
            WHERE
                1 = 1
              AND pla.platform_id IN ( SELECT platform_id FROM system_platform_tenant WHERE tenant_id = #{tenantId} AND deleted = 0 GROUP BY platform_id )
              AND pla.deleted = 0
            GROUP BY
                pla.tenant_id
        )
        </if>

          AND tenant.deleted = 0
        ORDER BY
            id ASC
    </select>

    <update id="updateBatchById">
        update system_tenant
        <trim prefix="set" suffixOverrides=",">

            <trim prefix="platform_json = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.platform_json}
                </foreach>
            </trim>
            <trim prefix="platform_names = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.platform_names}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id}
        </foreach>
    </update>
    <update id="updateCollectorInfoByCollectorId">
        update hzb_collector
        set platform_id = #{platformId}, platform_name = #{platformName}
        where id in
        <foreach collection="collectorId" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="updateCollectorInfoByCollectorIds">
        update hzb_collector
        set platform_id = #{platformId}, platform_name = #{platformName}
        where id = #{collectorId}
    </update>

    <select id="getTenantIdByDefine" resultType="java.lang.String">
        select id from hzb_alert_define where tenant_id = #{tenantId}
    </select>
    <select id="getTenantIdByUserId" resultType="java.lang.Long">
        select tenant_id from system_users where id = #{maintainer} and deleted = 0
    </select>

    <select id="selectCollectorInfo" resultType="java.lang.Integer">
        select count(*) count from hzb_collector_platform where platform_id = #{id} and collector_name = #{collectorName}
    </select>

    <select id="selectCollectorName" resultType="java.util.Map">
        SELECT * FROM hzb_collector WHERE id = #{collectorId}
    </select>

    <select id="selectCollectorInfoByPlatformId" resultType="java.util.Map">
        select collector_id,collector_name FROM hzb_collector_platform where platform_id = #{platformId}
    </select>
    <select id="selectHzbplatformById" resultType="java.lang.String">
        select id from  hzb_collector where project_id = #{id}
    </select>


    <delete id="deleteBatchAlertId">
        DELETE FROM hzb_alert_define
        WHERE id in
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteBatchAlertBindId">
        DELETE FROM hzb_alert_define_monitor_bind
        WHERE alert_define_id in
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteCollectorByPlatformId">
        delete from hzb_collector_platform where platform_id = #{platformId}
    </delete>
    <delete id="deleteByHzbCollector">
        DELETE FROM hzb_collector_platform
        WHERE collector_id in
        <foreach collection="hzb" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>

    <update id="updateByHzbCollector">
        update hzb_collector
        set project_id = null, project_name = null
        where project_id = #{id}
    </update>


    <insert id="insertCollectorInfo">
        INSERT INTO `hzb_collector_platform` (
            `collector_id`, `collector_name`, `platform_id`, `platform_name`, `status`
        )
        VALUES
            (
                #{map.id},
                #{map.name},
                #{platformId},
                #{platformName},
                #{map.status, jdbcType=INTEGER}
            )
    </insert>

    <select id="getPlatformByIds" resultType="java.util.Map">
        SELECT
        t.tenant_id,
        COALESCE ( SUM( c.count ), 0 ) AS total_count
        FROM
        (
        SELECT
        tenant_id,
        GROUP_CONCAT( platform_id ) AS platform_ids
        FROM
        system_platform_tenant
        WHERE deleted = 0
        <if test="ids !=null and ids.size()>0">
            and tenant_id in
            <foreach collection="ids" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        GROUP BY
        tenant_id
        ) AS t
        LEFT JOIN (
        SELECT
        platform_id,
        COUNT( 1 ) AS count
        FROM
        ( SELECT platform_id FROM monitor_host_info WHERE deleted = 0 UNION ALL SELECT platform_id FROM hzb_monitor WHERE STATUS = 1 ) AS combined
        GROUP BY
        platform_id
        ) AS c ON FIND_IN_SET( c.platform_id, t.platform_ids ) > 0
        GROUP BY
        t.tenant_id;
    </select>

    <select id="getTenantByIdOne" resultType="cn.iocoder.zj.module.system.controller.admin.tenant.vo.tenant.TenantRespVO">
        SELECT ten.NAME,users.nickname contactName,users.mobile contactMobile,users.email contactEmail,users.username userName,ten.STATUS,ten.domain,ten.package_id,ten.expire_time,ten.account_count,
        ten.platform_json,ten.platform_names,ten.id,ten.create_time,ten.is_test,ten.organization,ten.dept_str,ten.maintainer_id,ten.maintainer_name,ten.state
        <if test="id != null and id !='' and id !=1">
            ,con.contract_no,con.project_name,con.asset_num
        </if>
        FROM
        system_tenant ten
        LEFT JOIN system_users users ON ten.contact_user_id = users.id
        <if test="id != null and id !='' and id !=1">
            LEFT JOIN customer_contract_info con ON ten.id = con.tenant_id and con.deleted=0
        </if>
        where ten.deleted = 0 and ten.id=#{id}
    </select>
    <select id="getStateByTenantId" resultType="java.lang.Long">
        select state from system_tenant where id = #{tenantId}
    </select>
</mapper>