<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.system.dal.mysql.platformtenant.PlatformTenantMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <update id="deleteByTenantId">
        update system_platform_tenant set deleted = 1 where tenant_id = #{tenantId}
    </update>

    <delete id="deleteByPlatformId">
        delete from system_platform_tenant where platform_id = #{platformId}
    </delete>
    <select id="getByConfigMailList" resultType="java.lang.String">
        SELECT
            email
        FROM
            system_users
        WHERE
                tenant_id IN ( SELECT tenant_id FROM system_platform_tenant ten WHERE ten.platform_id = #{platformId} AND ten.deleted = 0 )
          AND deleted = 0
          AND email IS NOT NULL
          AND email != ''
    </select>

    <select id="getByTenantList" resultType="java.lang.String">
        SELECT tenant_id from system_platform_tenant WHERE platform_id = #{platformId} and deleted = 0
        GROUP BY tenant_id
    </select>

    <select id="selectPlatformListByTenantId" resultType="java.lang.String">
        SELECT platform_id FROM system_platform_tenant WHERE tenant_id = #{tenantId} and deleted = 0
        GROUP BY platform_id
    </select>

    <select id="selectHzStatusByplatformId" resultType="int">
        select count(*) count from hzb_monitor where status = 1
        and platform_id  in
        <foreach collection="platformId.split(',')" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getContractList" resultType="java.lang.Long">
        select sum(asset_num) num from customer_contract_info where tenant_id = #{tenantId}
        GROUP BY tenant_id
    </select>

    <select id="getCpuSockets" resultType="java.lang.Long">
        select ifnull(sum(cpu_num),0) cpuNum from monitor_hardware_info
        <where>
            deleted = 0 and  platform_id  in
            <foreach collection="platformIds.split(',')" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="getCloudCount" resultType="java.lang.Long">
        SELECT count(*) count FROM monitor_host_info
        <where>
            deleted = 0 and  platform_id  in
            <foreach collection="platformIds.split(',')" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
    </select>
    <select id="getByPlatformId" resultType="java.lang.String">
        select platform_id from system_platform_tenant where tenant_id = #{id} and deleted = 0
    </select>

    <update id="updateByName">
        UPDATE system_platform_tenant
        SET platform_name = #{name}
        WHERE platform_id = #{id}
    </update>

</mapper>
