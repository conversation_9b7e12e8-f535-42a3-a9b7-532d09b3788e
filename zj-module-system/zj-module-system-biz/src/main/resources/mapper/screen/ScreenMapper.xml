<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.system.dal.mysql.screen.ScreenMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="selectLayoutConfig" resultType="string">
        select layout_config from monitor_homepage_display where tenant_id=1
    </select>
    <insert id="createMonitorHomepageDisplay">
        insert into monitor_homepage_display (tenant_id,layout_config) values (#{tenantId},#{layoutConfig})
    </insert>
    <delete id="deleteConfigByTenantId">
        delete from monitor_homepage_display where tenant_id=#{tenantId}
    </delete>
    <delete id="deleteScreenByTenantId">
        delete from system_screen where tenant_id=#{tenantId}
    </delete>

<select id="getScreenList" resultType="cn.iocoder.zj.module.system.dal.dataobject.screen.ScreenDO">
    select monitor_entry,monitor_entry_name,resource_type,resource_type_name,
    sort,creator,create_time,updater,update_time,deleted,region_id,region_name
    from system_screen where tenant_id = #{tenantId} and deleted=0
    </select>

    <select id="getScreenConfigByTenantId"
            resultType="cn.iocoder.zj.module.system.dal.dataobject.screen.ScreenConfigDO">
        SELECT
            *
        FROM
            system_screen_config
        WHERE
            tenant_id = #{tenantId}
          AND deleted = 0
    </select>

    <select id="getScreenConfigById" resultType="cn.iocoder.zj.module.system.dal.dataobject.screen.ScreenConfigDO">
        SELECT
            *
        FROM
            system_screen_config
        WHERE
            id = #{id}
          AND deleted = 0
    </select>

    <insert id="createScreenConfig">
        INSERT INTO system_screen_config ( title, logo,system_name ,favicon, tenant_id, creator, updater,create_time,update_time)
        VALUES
            (#{screenConfig.title}, #{screenConfig.logo},#{screenConfig.systemName}, #{screenConfig.favicon}, #{screenConfig.tenantId}, #{screenConfig.creator}, #{screenConfig.updater},now(),now())
    </insert>

    <update id="updateScreenConfig">
        UPDATE system_screen_config
        SET title = #{screenConfig.title},
            logo = #{screenConfig.logo},
            favicon = #{screenConfig.favicon},
            tenant_id = #{screenConfig.tenantId},
            update_time = now(),
            updater = #{screenConfig.updater},
            system_name = #{screenConfig.systemName}
        WHERE
            id = #{screenConfig.id}
    </update>
</mapper>
