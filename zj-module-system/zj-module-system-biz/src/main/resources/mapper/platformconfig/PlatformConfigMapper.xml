<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.system.dal.mysql.platformconfig.PlatformConfigMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


<select id="selectListByDto"
            resultType="cn.iocoder.zj.module.system.dal.dataobject.platformconfig.PlatformConfigDO">
    select * from system_platform_config where deleted = 0
    </select>

<select id="getPlatformSelectList"
        resultType="java.util.Map">
    select  spc.id as platformId, spc.name as platformName,spc.address ,spc.type_code,spt.tenant_id as tenantId,spt.tenant_name as tenantName from  system_platform_config spc
    LEFT JOIN system_platform_tenant  spt on  spt.platform_id = spc.id and spt.deleted = 0
    where spc.deleted = 0
    <if test="tenantId != null and tenantId.size>0">
        and spt.tenant_id in
        <foreach collection="tenantId" separator="," close=")" open="(" item="item">
            #{item}
        </foreach>
    </if>
    GROUP BY spc.id,spc.NAME
    </select>

    <select id="getPlatformProject" resultType="java.util.Map">
        SELECT id as platformId,name as platformName from system_platform_config WHERE  deleted = 0
    </select>

    <select id="cloudPlatform" resultType="java.util.Map">
        SELECT name,type_code,count(*) count FROM system_platform_config WHERE deleted = 0
        GROUP BY   type_code
    </select>

<update id="deleteByTenantId">
    update system_platform_config set deleted = 1 where tenant_id = #{id}
    </update>

<select id="getTenantsByConfigId" resultType="java.lang.Long">
    select tenant_id from system_platform_tenant where platform_id = #{id} and deleted = 0
    </select>

    <select id="getLicense" resultType="java.util.Map">
        SELECT l.id,l.name FROM license l
                                    LEFT JOIN hzb_collector_platform hc on hc.collector_name = l.name
        WHERE hc.platform_id = #{platformId}
        limit 1
    </select>

    <select id="getProdMapping" resultType="java.util.Map">
        select id, client_ip,client_port from port_mapping where license_id =#{licenseId} and server_port = #{serverPort}
    </select>

    <select id="portMappingList" resultType="java.util.Map">
        select id from port_mapping where license_id = #{licenseId}
    </select>

    <select id="getPlatformCollector" resultType="java.util.Map">
        SELECT platform_id,
        GROUP_CONCAT(collector_name ORDER BY collector_name SEPARATOR ',') AS collectorName,
        GROUP_CONCAT(collector_id ORDER BY collector_id SEPARATOR ',') AS collectorId
        FROM hzb_collector_platform
        WHERE platform_id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY platform_id;
    </select>
    <select id="getByBaseInfoVo"
            resultType="cn.iocoder.zj.module.system.dal.dataobject.platformconfig.PlatformConfigDO">
        SELECT
            config.*
        FROM
            system_platform_config config
                LEFT JOIN system_platform_tenant tenant ON config.id = tenant.platform_id
        WHERE
            config.deleted = 0
          AND tenant.deleted = 0
          AND config.url = #{url}
          AND tenant.tenant_id = #{tenantId}
            LIMIT 1
    </select>
    <select id="getPlatFormListByCollectorId" resultType="java.lang.Long">
        SELECT
            platform_id AS platformId
        FROM
            hzb_collector_platform
        WHERE
            collector_id = #{collectorId}
          and deleted=0
    </select>
    <select id="getCollectorNameByPlatformId" resultType="java.lang.String">
        SELECT collector_name FROM hzb_collector_platform WHERE platform_id = #{id} and deleted=0
    </select>
    <select id="getAdminUserId" resultType="cn.iocoder.zj.module.system.dal.dataobject.user.AdminUserDO">
        select * from system_users  WHERE id = #{usrId} AND deleted = 0
    </select>

    <delete id="deleteCollectorPlatform">
        delete from hzb_collector_platform where platform_id=#{id}
    </delete>

    <update id="updateAlert">
        update hzb_alert
        set platform_name = #{name}
        where platform_id = #{id}
    </update>

    <update id="updateTop">
        update top_report_asset
        set platform_name = #{name}
        where platform_id = #{id}
    </update>
</mapper>
