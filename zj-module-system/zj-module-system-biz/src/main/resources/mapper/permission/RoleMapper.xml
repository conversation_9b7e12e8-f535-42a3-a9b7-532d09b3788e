<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.system.dal.mysql.permission.RoleMapper">

<select id="getRoleIdByCode" resultType="java.lang.Long">
    SELECT id from system_role where code = #{code} and deleted = 0
    </select>
    <select id="getIsRootOperation" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM system_users
        WHERE id = #{id}
          AND tenant_id = (SELECT tenant_id FROM system_role WHERE CODE = 'super_admin')
AND deleted =0
    </select>

    <select id="getUserIdByCode" resultType="java.lang.Long">
        SELECT
            sur.user_id
        FROM
            system_user_role sur
        LEFT JOIN system_role sr ON sr.id = sur.role_id
        WHERE
            sr.CODE = #{code} and sr.deleted = 0 and sur.deleted = 0
    </select>
</mapper>