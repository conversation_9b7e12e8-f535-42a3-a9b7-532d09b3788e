<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.system.dal.mysql.user.AdminUserMapper">
    <update id="deleteByUserId">
        update system_users set deleted = 1
        where id = #{id}
    </update>

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id="getOperations" resultType="cn.iocoder.zj.module.system.controller.admin.user.vo.user.UserSimpleRespVO">
        SELECT
        *
        FROM
        system_users
        WHERE
        (tenant_id = ( SELECT tenant_id FROM system_users WHERE id = #{userId} ) or tenant_id = 1)
        AND deleted = 0
        <if test="roleCode !='' and roleCode != null">
            AND id IN (
            SELECT
            user_id
            FROM
            system_user_role
            WHERE
            role_id IN ( SELECT id FROM system_role WHERE CODE = 'operation_maintenance')
            and deleted = 0)
        </if>
    </select>

    <select id="getRoleMapByUserIds" resultType="cn.iocoder.zj.module.system.controller.admin.permission.vo.role.RoleSimpleRespVO">
        SELECT
            urole.user_id userId,
            GROUP_CONCAT( role.`name` SEPARATOR ',' ) `name`
        FROM
            system_user_role urole
                LEFT JOIN system_role role ON role.id = urole.role_id
        WHERE
        urole.user_id IN
        <foreach collection="userIds" open="(" separator="," close=")" item="list">
            #{list}
        </foreach>
        AND urole.deleted = 0
        GROUP BY
        urole.user_id
    </select>

    <select id="getPlatformAdmin" resultType="cn.iocoder.zj.module.system.dal.dataobject.user.AdminUserDO">
        SELECT
            *
        FROM
            system_users su
        WHERE su.id =#{userId}
    </select>

    <select id="getPlatformAdminId" resultType="java.lang.Long">
        SELECT
            sur.user_id
        FROM
            system_user_role sur
        LEFT JOIN system_role sr ON  sr.id = sur.role_id
        WHERE
            sr.code = "cs_admin"
        limit 1
    </select>

    <select id="getCountByRoleId" resultType="java.lang.Long">
        SELECT
            count(*)
        FROM
            system_user_role
        WHERE
            deleted = 0
        AND
            role_id = #{id}
    </select>
    <select id="getEnforcer"
            resultType="cn.iocoder.zj.module.system.controller.admin.user.vo.user.UserSimpleRespVO">

        SELECT
        *
        FROM
        system_users
        WHERE
        tenant_id = ( SELECT tenant_id FROM system_users WHERE id = #{userId} )
        AND deleted = 0
        <if test="roleCode !='' and roleCode != null">
            AND id IN (
            SELECT
            user_id
            FROM
            system_user_role
            WHERE
            role_id IN ( SELECT id FROM system_role WHERE CODE = 'enforcer')
            and deleted = 0)
        </if>


    </select>

    <select id="getUserIdsByTenantId" resultType="java.lang.Long">
        select id
        from system_users
        where tenant_id =
              (SELECT tenant_id from system_users WHERE id = #{userId})
        and deleted = 0
    </select>

    <update id="deleteByTenantId">
        update system_users set deleted = 1
        where tenant_id = #{tenantId}
    </update>

    <select id="getUserIdByTenantId" resultType="java.lang.Long">
        SELECT contact_user_id FROM system_tenant WHERE id = #{tenantId} and deleted=0
    </select>

    <select id="getHomologousUsers" resultType="java.lang.Long">
        SELECT
            id
        FROM
            system_users
        WHERE
            tenant_id = ( SELECT tenant_id FROM system_users WHERE id = #{userId} )
          AND deleted = 0
    </select>

    <select id="getUserMenus" resultType="java.lang.String">
        SELECT
            path
        FROM
            system_menu
        WHERE
                id IN ( SELECT menu_id FROM system_role_menu WHERE role_id IN ( SELECT role_id FROM system_user_role WHERE user_id = #{userId} ) and deleted = 0)
          AND path IS NOT NULL
          AND path != ''
	AND deleted = 0
    </select>

    <select id="selectUserById" resultType="cn.iocoder.zj.module.system.dal.dataobject.user.AdminUserDO">
        select * from system_users where id = #{userId} and deleted = 0
    </select>

    <select id="getOrderEnforcer"
            resultType="cn.iocoder.zj.module.system.controller.admin.user.vo.user.UserSimpleRespVO">
        SELECT
        *
        FROM
        system_users
        WHERE
        id IN (
        SELECT
        SUBSTRING_INDEX( SUBSTRING_INDEX( st.maintainer_id, ',', help_topic_id + 1 ), ',',- 1 ) AS maintainer_id
        FROM
        system_platform_tenant spt
        LEFT JOIN system_tenant st ON st.id = spt.tenant_id
        INNER JOIN mysql.help_topic b ON b.help_topic_id &lt;( length( st.maintainer_id ) - length( REPLACE ( st.maintainer_id, ',', '' )) + 1 )
        WHERE
        spt.platform_id = #{platformId}
        AND spt.deleted = 0
        AND st.deleted = 0
        AND st.maintainer_id IS NOT NULL
        GROUP BY
        st.maintainer_id
        )
    </select>
    <select id="getUserByIds" resultType="cn.iocoder.zj.module.system.dal.dataobject.user.AdminUserDO">
        SELECT
            id,
            username,
            PASSWORD,
            nickname,
            remark,
            dept_id,
            post_ids,
            email,
            mobile,
            sex,
            avatar,
            STATUS,
            login_ip,
            login_date,
            tenant_id,
            organization,
            dept_str,
            first_login,
            service_tenant_id,
            create_time,
            update_time,
            creator,
            updater,
            deleted
        FROM
            system_users
        WHERE
            deleted = 0
          AND  id IN
        <foreach collection="ids" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>

    <update id="updateOrganizationByTenantId">
        update system_users set organization = #{organization} where tenant_id = #{tenantId} and deleted = 0
    </update>

    <select id="getAssetNum" resultType="java.lang.Integer">
        select asset_num from customer_contract_info
        where
            tenant_id = #{tenantId}
          and deleted = 0
          and service_end > NOW()
    </select>
    <select id="getContractCount" resultType="java.lang.Integer">
        select count(*) as count
        from customer_contract_info
        where
            tenant_id = #{tenantId}
          and deleted = 0
          and service_end
            > NOW()
    </select>
    <select id="getUserByUserId" resultType="cn.iocoder.zj.module.system.dal.dataobject.user.AdminUserDO">
        select * from system_users where wx_user_id = #{userID}
    </select>
    <select id="getStateByTenantId" resultType="java.lang.Integer">
        select state from system_tenant
        where id = #{tenantId}
    </select>

    <select id="getAdminUserByUserId" resultType="cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO">
        select su.*,st.name as tenantName from system_users su
        LEFT JOIN system_tenant st ON st.id = su.tenant_id
        where su.id = #{userID}  AND su.deleted = 0
    </select>

</mapper>
