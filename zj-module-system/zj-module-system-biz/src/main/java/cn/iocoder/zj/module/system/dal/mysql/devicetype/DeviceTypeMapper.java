package cn.iocoder.zj.module.system.dal.mysql.devicetype;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.system.dal.dataobject.devicetype.DeviceTypeDO;
import cn.iocoder.zj.module.system.dal.dataobject.dict.DictDataDO;
import jodd.util.StringUtil;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.system.controller.admin.devicetype.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 资产类型 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DeviceTypeMapper extends BaseMapperX<DeviceTypeDO> {

    default PageResult<DeviceTypeDO> selectPage(DeviceTypePageReqVO reqVO) {
        LambdaQueryWrapperX<DeviceTypeDO> deviceTypeDOLambdaQueryWrapperX = new LambdaQueryWrapperX<DeviceTypeDO>()
                .likeIfPresent(DeviceTypeDO::getSysDeviceName, reqVO.getSysDeviceName())
                .eqIfPresent(DeviceTypeDO::getSysDeviceType, reqVO.getSysDeviceType())
                .betweenIfPresent(DeviceTypeDO::getCreateTime, reqVO.getCreateTime());

        if (StringUtil.isNotEmpty(reqVO.getSortBy()) && reqVO.getSortDirection().equals("asc")){
            deviceTypeDOLambdaQueryWrapperX.orderByAsc(DeviceTypeDO::getCreateTime);
        }else {
            deviceTypeDOLambdaQueryWrapperX.orderByDesc(DeviceTypeDO::getCreateTime);
        }
        return selectPage(reqVO, deviceTypeDOLambdaQueryWrapperX);

    }

    default List<DeviceTypeDO> selectList(DeviceTypeExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DeviceTypeDO>()
                .likeIfPresent(DeviceTypeDO::getSysDeviceName, reqVO.getSysDeviceName())
                .eqIfPresent(DeviceTypeDO::getSysDeviceType, reqVO.getSysDeviceType())
                .betweenIfPresent(DeviceTypeDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(DeviceTypeDO::getId));
    }

    void updateDeviceIcon(@Param("id") Long id, @Param("avatar") String avatar);

    List<DeviceTypeDO> selectListBySort();
}
