package cn.iocoder.zj.module.system.service.licence;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.constants.FileTypeConstants;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.util.string.StringUtil;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.customer.api.contractInfo.ContractInfoApi;
import cn.iocoder.zj.module.infra.api.file.FileApi;
import cn.iocoder.zj.module.monitor.api.hardware.HardWareInfoApi;
import cn.iocoder.zj.module.monitor.api.hostinfo.HostInfoApi;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.controller.admin.licence.LicenseVo;
import cn.iocoder.zj.module.system.dal.dataobject.info.CodeDO;
import cn.iocoder.zj.module.system.dal.dataobject.info.InfoDO;
import cn.iocoder.zj.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.zj.module.system.dal.mysql.info.CodeMapper;
import cn.iocoder.zj.module.system.dal.mysql.info.InfoMapper;
import cn.iocoder.zj.module.system.dal.mysql.platformtenant.PlatformTenantMapper;
import cn.iocoder.zj.module.system.service.tenant.TenantService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.gson.Gson;
import org.apache.hertzbeat.common.util.AesUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.SecureRandom;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.singleton;

/**
 * @ClassName : LicenceServiceImpl  //类名
 * @Description : 监控授权实现  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/1/30  14:16
 */
@Service
@Validated
public class LicenceServiceImpl implements LicenceService {
    @Resource
    ContractInfoApi contractInfoApi;
    @Resource
    private PlatformTenantMapper platformTenantMapper;

    @Resource
    private HardWareInfoApi hardWareInfoApi;
    @Autowired
    PermissionApi permissionApi;
    @Autowired
    RoleApi roleApi;
    @Autowired
    TenantService tenantService;


    @Autowired
    InfoMapper infoMapper;
    @Autowired
    CodeMapper codeMapper;
    @Resource
    HostInfoApi hostInfoApi;
    @Resource
    private FileApi fileApi;

    @Override
    @TenantIgnore
    public Map selectLicenByState(Long tenantId) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        // 不是超级管理员但是属于超级管理员角色下， 根据租户id 去查询 用户，查到用户是否配置了超级管理员如果配置了，则属于超管体系下
        // 根据该用户的租户id 去查询上级租户下的用户配置的角色id
        Set<Long> roleIds = permissionApi.getTeanIdRoleIdsFromCache(loginUser.getTenantId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        Map map = new HashMap();
        map.put("remainderCount", 0);
        map.put("totalCount", 0);
        map.put("used", 0);
        map.put("state", "unavailable");
        map.put("remainderCount", 0);
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            // 查询合同下配置的资产数
            Long count = platformTenantMapper.getContractList(tenantId);
            if (count == null) {
                map.put("state", "unavailable");
                return map;
            }
            // 查询该用户下配置的平台，根据平台去查询该用户平台下配置的云主机的数量和监控管理中资产数量
            List<String> getPlatformList = platformTenantMapper.selectPlatformListByTenantId(tenantId);

            String d = getPlatformList.stream().collect(Collectors.joining(","));
            Long cpusockets = platformTenantMapper.getCloudCount(d);
            // 根据平台id 查询平台下 正在监控的数据监控状态有多少数量
            int hzcount = platformTenantMapper.selectHzStatusByplatformId(d);

            // 剩余
            Long remainderCount = count - (cpusockets + hzcount);
            // 如果剩余的数量 小于0了  说明资产耗尽 ，提示客户增加资产或者取消监控应用数
            map.put("remainderCount", remainderCount);
            map.put("totalCount", count);
            map.put("used", cpusockets + hzcount);
            if (remainderCount < 0) {
                map.put("state", "unavailable");
                map.put("remainderCount", 0);
            } else {
                map.put("state", "available");
            }
        } else {
            map.put("totalCount", -1);
            map.put("state", "available");
            map.put("remainderCount", -1);
            map.put("used", -1);
        }
        return map;
    }

    @Override
    @TenantIgnore
    public Map<String, Object> getLicence() {
        Map<String, Object> map = new HashMap<>();
        QueryWrapper<InfoDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("enable",1);
        queryWrapper.orderByDesc("license_id").last("LIMIT 1");
        InfoDO infoDO = infoMapper.selectOne(queryWrapper);

        if (infoDO != null) {
            populateLicenseInfo(map, infoDO);
        }

        return map;
    }

    private void populateLicenseInfo(Map<String, Object> map, InfoDO infoDO) {
        String expiryDate = decodeAndFormatExpiryDate(infoDO.getExpiryDate());
        int currentHostCount = hostInfoApi.selectHostCount();
        String count = decodeLicenseCount(infoDO.getLicenseCount());

        map.put("expiryDate", expiryDate);
        map.put("totalCount", count);
        map.put("used", currentHostCount);
        map.put("startDate", DateUtil.format(infoDO.getStartDate(), "yyyy-MM-dd HH:mm:ss"));
        map.put("machineCode", infoDO.getMachineCode());
        map.put("licenseName", infoDO.getLicenseName());
        map.put("version", infoDO.getVersion());
        long time = calculateDaysUntilExpiry(expiryDate);
        if(time < 0){
            map.put("surplusDays", "授权已到期");
        }else {
            map.put("surplusDays", time+"天");
        }

        // 计算授权状态
        int remainingLicenses = 0;
        if(count.equals("error")){
            remainingLicenses = -1;
        }else {
            remainingLicenses = Integer.parseInt(count) - currentHostCount;
        }
        int state = determineState(infoDO, remainingLicenses);
        map.put("state", state);

        // 设置 enable 状态
        int enableStatus = determineEnableStatus(state, count, expiryDate);
        map.put("enable", enableStatus);
    }

    public static long calculateDaysUntilExpiry(String dateString) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        try {
            LocalDate expiryDate = LocalDate.parse(dateString, formatter);
            LocalDate currentDate = LocalDate.now();
            return ChronoUnit.DAYS.between(currentDate, expiryDate);
        } catch (Exception e) {
            System.err.println("无效的日期格式: " + dateString);
            return -1;
        }
    }

    private String decodeAndFormatExpiryDate(String expiryDate) {
        String decoded = AesUtil.aesDecodeMsg(expiryDate, "zj@cloudzj@cloud");
        return "error".equals(decoded) ? decoded : convertDateString(decoded);
    }

    private String decodeLicenseCount(String licenseCount) {
        return AesUtil.aesDecodeMsg(licenseCount, "zj@cloudzj@cloud");
    }

    private int determineEnableStatus(int state, String count, String expiryDate) {
        if ("error".equals(count) || "error".equals(expiryDate)) {
            return 2;
        }
        return state != 2 ? 0 : 1;
    }

    public static String convertDateString(String dateString) {
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        try {
            LocalDate localDate = LocalDate.parse(dateString, inputFormatter);
            LocalDateTime localDateTime = localDate.atStartOfDay();
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            return localDateTime.format(outputFormatter);
        }catch (Exception e){
            return "2024-12-31 00:00:00";
        }
    }

        @Override
    @TenantIgnore
    public String getAppCode() {
        QueryWrapper<CodeDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("app_id").last("LIMIT 1");
        CodeDO codeDO = codeMapper.selectOne(new LambdaQueryWrapper<CodeDO>());
        if(BeanUtil.isNotEmpty(codeDO) && StrUtil.isNotEmpty(codeDO.getAppCode())){
            return codeDO.getAppCode();
        }else {
            return null;
        }
    }

    @Override
    @TenantIgnore
    public String creatLicenseCode() {
        String uuid = StrUtil.uuid();
        CodeDO codeDO = new CodeDO();
        codeDO.setAppCode(uuid);
        codeDO.setCreateTime(LocalDateTimeUtil.of(new Date()));
        codeMapper.insert(codeDO);
        return uuid;
    }


    @Override
    public Map<String, String> getInfoUpload(MultipartFile file, HttpServletResponse response) throws IOException {
        Map map = new HashMap();
        String d = StringUtil.getSavePath(file.getOriginalFilename(), FileTypeConstants.FILE_TYPE,"lice");
        String url = fileApi.createFileUrl(file.getOriginalFilename(), d, IoUtil.readBytes(file.getInputStream()));
        map.put("fileUrl", url);
        map.put("fileName", file.getOriginalFilename().substring(0, file.getOriginalFilename().lastIndexOf(".")));
        return map;
    }

    @Override
    public String createLicense(LicenseVo licenseVo) {
        // 设置开始日期和机器码
        licenseVo.setStartDate(DateUtil.format(new Date(), "yyyy-MM-dd"));
        licenseVo.setMachineCode(generateLicenseKey());

        // 将 LicenseVo 转换为 JSON 并进行 AES 加密
        byte[] bytes = AesUtil.aesEncodeByte(new Gson().toJson(licenseVo), "zj@cloudzj@cloud");
        String filename = "license.lic";

        // 创建输入流
        try (InputStream inputStream = new ByteArrayInputStream(bytes)) {
            String d = StringUtil.getSavePath(filename, FileTypeConstants.FILE_TYPE,"lice");
            String path = fileApi.createFileUrl(filename, d, IoUtil.readBytes(inputStream));
            InfoDO info = createInfoDO(licenseVo, path);
            infoMapper.insert(info);
            return path; // 返回上传的路径或其他需要的信息
        } catch (Exception e) {
            return "Error creating license: " + e.getMessage();
        }
    }

    private InfoDO createInfoDO(LicenseVo licenseVo, String licenseUrl) {
        InfoDO info = new InfoDO();
        info.setAppCode(licenseVo.getAppCode());
        info.setMachineCode(licenseVo.getMachineCode());
        info.setLicenseUrl(licenseUrl);
        info.setLicenseCount( AesUtil.aesEncode(String.valueOf(licenseVo.getLicenseCount()), "zj@cloudzj@cloud"));
        info.setExpiryDate(AesUtil.aesEncode(String.valueOf(licenseVo.getExpiryDate()), "zj@cloudzj@cloud"));
        info.setStartDate(LocalDateTimeUtil.of(new Date()));
        info.setCreateTime(LocalDateTimeUtil.of(new Date()));
        info.setUseCount(0);
        info.setVersion(licenseVo.getVersion());
        info.setEnable(1);
        info.setUpdateTime(LocalDateTimeUtil.of(new Date()));
        info.setLicenseName(licenseVo.getLicenseName());
        return info;
    }

    // 辅助方法来确定状态
    private int determineState(InfoDO infoDO, int remainingLicenses) {
        if (infoDO.getVersion() == 0) {
            String decode = AesUtil.aesDecodeMsg(infoDO.getExpiryDate(), "zj@cloudzj@cloud");
            int compare = DateUtil.compare(convertStringToDate(decode), new Date());
            if (compare < 0) {
                return 0; // 时间过期
            } else if (remainingLicenses < 0) {
                return 1; // 超过授权数量
            }
        } else if (infoDO.getVersion() == 1) {
            if (remainingLicenses < 0) {
                return 1; // 超过授权数量
            }
        }
        return 2; // 正常状态
    }

    public static Date convertStringToDate(String dateString) {
        if(dateString.equals("error")){
            return new Date();
        }else {
            LocalDate localDate = LocalDate.parse(dateString);
            return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        }
    }

    @Override
    @TenantIgnore
    public Map getAssetInfoByTenantId(Long tenantId) {

        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        // 不是超级管理员但是属于超级管理员角色下， 根据租户id 去查询 用户，查到用户是否配置了超级管理员如果配置了，则属于超管体系下
        // 根据该用户的租户id 去查询上级租户下的用户配置的角色id
        Set<Long> roleIds = permissionApi.getTeanIdRoleIdsFromCache(loginUser.getTenantId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        Map map = new HashMap();
        map.put("remainderCount", 0);
        map.put("totalCount", 0);
        map.put("used", 0);
        map.put("state", "unavailable");
        map.put("remainderCount", 0);
        if (!roleApi.hasAnySuperAdmin(roleIds)) {

            //查询租户有有效期
            TenantDO tenantDO = tenantService.getTenant(tenantId);
            Long surplusDays = tenantDO.getExpireTime().toLocalDate().toEpochDay() - LocalDate.now().toEpochDay();
            // 查询合同下配置的资产数
            Long count = platformTenantMapper.getContractList(tenantId);
            if (count == null) {
                map.put("state", "unavailable");
                return map;
            }
            // 查询该用户下配置的平台，根据平台去查询该用户平台下配置的宿主机的cpu 核数， 核数的比例是1:5
            List<String> getPlatformList = platformTenantMapper.selectPlatformListByTenantId(tenantId);
            // 根据平台查询CPU 有多少颗
            String d = getPlatformList.stream().collect(Collectors.joining(","));
            Long cpusockets = platformTenantMapper.getCloudCount(d);
            // 根据平台id 查询平台下 正在监控的数据监控状态有多少数量
            int hzcount = platformTenantMapper.selectHzStatusByplatformId(d);
            // 剩余
            Long remainderCount = count - (cpusockets + hzcount);
            map.put("expirationDate", String.valueOf(tenantDO.getExpireTime().toLocalDate()));
            map.put("surplusDays", surplusDays);
            map.put("totalCount", count);
            map.put("used", cpusockets + hzcount);
            map.put("remainderCount", remainderCount);
        } else {
            map.put("expirationDate", "2099年12月31日");
            map.put("surplusDays", "9999天");
            map.put("totalCount", -1);
            map.put("used", -1);
            map.put("remainderCount", -1);
        }
        return map;
    }

    @Override
    public String updateLicense(LicenseVo licenseVo) {
        // 参数校验
        validateLicenseVo(licenseVo);

        byte[] licenseData;
        try {
            licenseData = fileApi.ReaderObjects(licenseVo.getLicenseUrl());
        } catch (Exception e) {
            throw new RuntimeException("无法读取授权文件：" + e.getMessage(), e);
        }

        LicenseVo license = parseLicenseData(licenseData);
        validateLicense(license, licenseVo);

        // 获取当前主机数量
        int currentHostCount = hostInfoApi.selectHostCount();

        InfoDO latestInfoDO = getLatestInfoDO();
        return updateInfoDO(latestInfoDO, licenseVo, license, currentHostCount);
    }

    @TenantIgnore
    private InfoDO getLatestInfoDO() {
        QueryWrapper<InfoDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("license_id").last("LIMIT 1");
        return infoMapper.selectOne(queryWrapper);
    }

    @TenantIgnore
    private String updateInfoDO(InfoDO latestInfoDO, LicenseVo licenseVo, LicenseVo license, int currentHostCount) {
        InfoDO info = createInfoDO(licenseVo, license, currentHostCount);

        if (BeanUtil.isNotEmpty(latestInfoDO) && latestInfoDO.getMachineCode().equals(license.getMachineCode())) {
            info.setLicenseId(latestInfoDO.getLicenseId());
            infoMapper.updateById(info);
        } else {
            info.setEnable(1);
            infoMapper.insert(info);
            if(BeanUtil.isNotEmpty(latestInfoDO)){
                latestInfoDO.setEnable(0);
                infoMapper.updateById(latestInfoDO);
            }
        }
        return null;
    }

    @Override
    @TenantIgnore
    public PageResult<InfoDO> getLicenseList(LicenseVo licenseVo) {
        LambdaQueryWrapperX<InfoDO> queryWrapper = new LambdaQueryWrapperX<>();
        if (licenseVo.getLicenseName() != null) {
            queryWrapper.like(InfoDO::getLicenseName, licenseVo.getLicenseName());
        }
        if (licenseVo.getVersion() != null) {
            queryWrapper.eq(InfoDO::getVersion, licenseVo.getVersion());
        }
        if (jodd.util.StringUtil.isNotEmpty(licenseVo.getSortDirection())){
            if (licenseVo.getSortDirection().equals("asc")){
                if (licenseVo.getSortBy().equals("createTime")){
                    queryWrapper.orderByAsc(InfoDO::getCreateTime);
                }
                if (licenseVo.getSortBy().equals("expiryDate")){
                    queryWrapper.orderByDesc(InfoDO::getExpiryDate);
                }
            }else if (licenseVo.getSortDirection().equals("desc")){
                if (licenseVo.getSortBy().equals("createTime")){
                    queryWrapper.orderByDesc(InfoDO::getCreateTime);
                }
                if (licenseVo.getSortBy().equals("expiryDate")){
                    queryWrapper.orderByDesc(InfoDO::getExpiryDate);
                }
            }
        }else {
            queryWrapper.orderByDesc(InfoDO::getLicenseId);
        }
        PageResult<InfoDO> infoDOPageResult = infoMapper.selectPage(licenseVo, queryWrapper);
        infoDOPageResult.getList().forEach(x->{
            x.setLicenseCount(AesUtil.aesDecode(x.getLicenseCount(), "zj@cloudzj@cloud"));
            x.setExpiryDate(convertDateString(AesUtil.aesDecode(x.getExpiryDate(), "zj@cloudzj@cloud")));
        });
        return infoDOPageResult;
    }

    // 参数校验
    private void validateLicenseVo(LicenseVo licenseVo) {
        if (StrUtil.isEmpty(licenseVo.getLicenseUrl()) || StrUtil.isEmpty(licenseVo.getAppCode())) {
            throw new RuntimeException("申请码或文件地址为空");
        }
    }

    // 解析授权数据
    private LicenseVo parseLicenseData(byte[] licenseData) {
        try {
            String decryptedData = AesUtil.aesDecodeByte(licenseData, "zj@cloudzj@cloud");
            return JSONObject.parseObject(decryptedData, LicenseVo.class);
        } catch (Exception e) {
            throw new RuntimeException("授权证书解析有误：" + e.getMessage(), e);
        }
    }

    // 校验授权信息
    private void validateLicense(LicenseVo license, LicenseVo licenseVo) {
        if (license == null) {
            throw new RuntimeException("授权证书解析有误");
        }
        if (!license.getAppCode().equals(licenseVo.getAppCode())) {
            throw new RuntimeException("申请码与证书不匹配");
        }
    }

    // 创建 InfoDO 对象
    private InfoDO createInfoDO(LicenseVo licenseVo, LicenseVo license, int currentHostCount) {
        InfoDO infoDO = new InfoDO();
        infoDO.setAppCode(licenseVo.getAppCode());
        infoDO.setMachineCode(license.getMachineCode());
        infoDO.setLicenseUrl(licenseVo.getLicenseUrl());
        infoDO.setLicenseCount( AesUtil.aesEncode(String.valueOf(license.getLicenseCount()), "zj@cloudzj@cloud"));
        infoDO.setExpiryDate(AesUtil.aesEncode(String.valueOf(license.getExpiryDate()), "zj@cloudzj@cloud"));
        infoDO.setStartDate(LocalDateTimeUtil.of(new Date()));
        infoDO.setCreateTime(LocalDateTimeUtil.of(new Date()));
        infoDO.setUseCount(currentHostCount);
        infoDO.setVersion(license.getVersion());
        infoDO.setUpdateTime(LocalDateTimeUtil.of(new Date()));
        infoDO.setLicenseName(license.getLicenseName());
        return infoDO;
    }

    public static String generateLicenseKey() {
        String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        SecureRandom random = new SecureRandom();
        StringBuilder sb = new StringBuilder(128);

        for (int i = 0; i < 128; i++) {
            int index = random.nextInt(CHARACTERS.length());
            sb.append(CHARACTERS.charAt(index));
        }
        return sb.toString();
    }

}
