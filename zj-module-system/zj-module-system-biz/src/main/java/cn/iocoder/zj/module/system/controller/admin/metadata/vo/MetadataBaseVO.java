package cn.iocoder.zj.module.system.controller.admin.metadata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 元数据 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class MetadataBaseVO {

    @Schema(description = "类型")
    private String type;

    @Schema(description = "文本")
    private String value;


}
