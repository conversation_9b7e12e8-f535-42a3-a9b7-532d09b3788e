package cn.iocoder.zj.module.system.dal.dataobject.deviceoid;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;

import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * OID管理 DO
 *
 * <AUTHOR>
 */
@TableName("system_device_oid")
@KeySequence("system_device_oid_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceOidDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 设备类型key
     */
    private String deviceType;
    /**
     * 设备类型名称
     */
    private String deviceName;
    /**
     * 系统类型名称
     */
    private String sysName;
    /**
     * 系统类型key
     */
    private String sysType;
    /**
     * cpu使用率oid
     */
    private String cpuUse;

    /**
     * 内存总容量oid
     */
    private String memoryAll;
    /**
     * 内存使用量oid
     */
    private String memoryUse;
    /**
     * 磁盘总容量oid
     */
    private String diskAll;
    /**
     * 磁盘使用量oid
     */
    private String diskUse;

    /**
     * 资产类型图标
     */
    private String icon;


}
