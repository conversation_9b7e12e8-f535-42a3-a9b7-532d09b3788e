package cn.iocoder.zj.module.system.controller.admin.licence;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.module.system.dal.dataobject.info.InfoDO;
import cn.iocoder.zj.module.system.service.licence.LicenceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Map;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.GET;

/**
 * @ClassName : LicenceController  //类名
 * @Description : 监控授权  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/1/30  14:11
 */
@Tag(name = "管理后台 - 监控授权")
@RestController
@RequestMapping("/system/licence")
@Validated
public class LicenceController {

    @Resource
    LicenceService licenceService;
    @Resource
    private RedisTemplate redisTemplate;

    @PostMapping("/getLicence")
    @OperateLog(enable = false) // 不记录操作日志，因为不需要
    public CommonResult<Map> createTenant(@RequestParam Long tenantId) {
        return success(licenceService.selectLicenByState(tenantId));
    }

    @GetMapping("/getLicenceInfo")
    @OperateLog(enable = false)
    @PermitAll
    public CommonResult<Map> getLicence() {
        return success(licenceService.getLicence());
    }
    @GetMapping("/getAssetInfo")
    @Operation(summary = "获取资产授权信息")
    @OperateLog(type=GET)
    public CommonResult<Map> getAssetInfo(@RequestParam("tenantId") Long tenantId) {
        return success(licenceService.getAssetInfoByTenantId(tenantId));
    }

    @GetMapping("/getLicenseCode")
    @Operation(summary = "获取申请码")
    @OperateLog(enable = false)
    @PermitAll
    public CommonResult<String> getLicenseCode() {
        return success(licenceService.getAppCode());
    }

    @GetMapping("/creatLicenseCode")
    @Operation(summary = "生成申请码")
    @OperateLog(enable = false)
    @PermitAll
    public CommonResult<String> creatLicenseCode() {
        return success(licenceService.creatLicenseCode());
    }

    @PostMapping("/creatLicense")
    @OperateLog(enable = false)
    @PermitAll
    public CommonResult<String> createLicense(@RequestBody LicenseVo licenseVo) {
        return success(licenceService.createLicense(licenseVo));
    }

    @GetMapping("/getLicenseList")
    @Operation(summary = "获取授权列表")
    @OperateLog(enable = false)
    @PermitAll
    public CommonResult<PageResult<InfoDO>> getLicenseInfo(@Valid LicenseVo licenseVo) {
        return success(licenceService.getLicenseList(licenseVo));
    }

    @PostMapping("/updateLicense")
    @Operation(summary = "更新授权内容")
    @OperateLog(enable = false)
    @PreAuthorize("@ss.hasPermission('system:licence:update')")
    public CommonResult<String> updateLicense(@RequestBody LicenseVo licenseVo) {
        return success(licenceService.updateLicense(licenseVo));
    }

    @PostMapping("/upload")
    @Operation(summary = "上传附件")
    @PermitAll
    public CommonResult<Map<String,String>> getUpload(MultipartFile file, HttpServletResponse response) throws IOException {
        Map<String,String> pageResult = licenceService.getInfoUpload(file,response);
        return success(pageResult);
    }
}
