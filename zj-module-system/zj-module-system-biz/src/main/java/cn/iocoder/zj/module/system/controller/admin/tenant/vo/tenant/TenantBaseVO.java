package cn.iocoder.zj.module.system.controller.admin.tenant.vo.tenant;

import cn.iocoder.zj.framework.common.validation.Mobile;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.URL;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
* 租户 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class TenantBaseVO {

    @Schema(description = "项目名", required = true, example = "芋道")
    @NotNull(message = "项目名不能为空")
    @Size(min = 2, max = 30, message = "项目名长度为 2-30个字符")
    private String name;

    @Schema(description = "联系人", required = false, example = "芋艿")
    private String contactName;

    @Schema(description = "联系手机", example = "15601691300")
    @Mobile
    private String contactMobile;

    @Schema(description = "邮箱", example = "15601691300")
    @Email(message = "必须是 Email 格式")
    @Size(max = 50, message = "邮箱长度最多50个字符")
    private String contactEmail;

    @Schema(description = "租户状态", required = true, example = "1")
    @NotNull(message = "租户状态")
    private Integer status;

    @Schema(description = "绑定域名", example = "https://www.iocoder.cn")
    @URL(message = "绑定域名的地址非 URL 格式")
    private String domain;

    @Schema(description = "租户套餐编号", required = true, example = "1024")
    @NotNull(message = "租户套餐编号不能为空")
    private Long packageId;

    @Schema(description = "过期时间", required = true)
    @NotNull(message = "过期时间不能为空")
    private LocalDateTime expireTime;

    @Schema(description = "账号数量", required = true, example = "1024")
    @NotNull(message = "账号数量不能为空")
    private Integer accountCount;

    @Schema(description = "是否测试账号（0是 1否）", required = true, example = "1024")
    //@NotNull(message = "是否测试账号不能为空")
    private Integer isTest;

    @Schema(description = "配置平台json")
    private String platformJson;

    @Schema(description = "配置平台名称")
    private String platformNames;

    @Schema(description = "公司")
    private String organization;

    @Schema(description = "部门")
    private String deptStr;

    @Schema(description = "运维人员ID", required = true, example = "12")
    private String maintainerId;

    @Schema(description = "运维人员名称", required = true, example = "12")
    private String maintainerName;

    @Schema(description = "用户定制大屏版本状态，0：官方版本，1：公有定制版本", example = "0")
    private Integer state;
}