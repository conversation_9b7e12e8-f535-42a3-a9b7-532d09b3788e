package cn.iocoder.zj.module.system.convert.platformtenant;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.system.controller.admin.platformtenant.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.platformtenant.PlatformTenantDO;

/**
 * 平台租户关系 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface PlatformTenantConvert {

    PlatformTenantConvert INSTANCE = Mappers.getMapper(PlatformTenantConvert.class);

    PlatformTenantDO convert(PlatformTenantCreateReqVO bean);

    PlatformTenantDO convert(PlatformTenantUpdateReqVO bean);

    PlatformTenantRespVO convert(PlatformTenantDO bean);

    List<PlatformTenantRespVO> convertList(List<PlatformTenantDO> list);

    PageResult<PlatformTenantRespVO> convertPage(PageResult<PlatformTenantDO> page);

    List<PlatformTenantExcelVO> convertList02(List<PlatformTenantDO> list);

}
