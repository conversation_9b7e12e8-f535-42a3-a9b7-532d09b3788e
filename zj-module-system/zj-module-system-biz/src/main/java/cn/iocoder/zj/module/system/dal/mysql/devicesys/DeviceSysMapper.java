package cn.iocoder.zj.module.system.dal.mysql.devicesys;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.system.dal.dataobject.devicesys.DeviceSysDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.system.controller.admin.devicesys.vo.*;

/**
 * 系统类型 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DeviceSysMapper extends BaseMapperX<DeviceSysDO> {

    default PageResult<DeviceSysDO> selectPage(DeviceSysPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DeviceSysDO>()
                .eqIfPresent(DeviceSysDO::getSysType, reqVO.getSysType())
                .likeIfPresent(DeviceSysDO::getSysName, reqVO.getSysName())
                .betweenIfPresent(DeviceSysDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(DeviceSysDO::getId));
    }

    default List<DeviceSysDO> selectList(DeviceSysExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DeviceSysDO>()
                .eqIfPresent(DeviceSysDO::getSysType, reqVO.getSysType())
                .likeIfPresent(DeviceSysDO::getSysName, reqVO.getSysName())
                .betweenIfPresent(DeviceSysDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(DeviceSysDO::getId));
    }

}
