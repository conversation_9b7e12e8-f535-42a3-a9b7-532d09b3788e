package cn.iocoder.zj.module.system.controller.admin.platformtenant;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.system.controller.admin.platformtenant.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.platformtenant.PlatformTenantDO;
import cn.iocoder.zj.module.system.convert.platformtenant.PlatformTenantConvert;
import cn.iocoder.zj.module.system.service.platformtenant.PlatformTenantService;

@Tag(name = "管理后台 - 平台租户关系")
@RestController
@RequestMapping("/system/platform-tenant")
@Validated
public class PlatformTenantController {

    @Resource
    private PlatformTenantService platformTenantService;

    @PostMapping("/create")
    @Operation(summary = "创建平台租户关系")
    @OperateLog(type=CREATE)
    @PreAuthorize("@ss.hasPermission('system:platform-tenant:create')")
    public CommonResult<Long> createPlatformTenant(@Valid @RequestBody PlatformTenantCreateReqVO createReqVO) {
        return success(platformTenantService.createPlatformTenant(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新平台租户关系")
    @OperateLog(type=UPDATE)
    @PreAuthorize("@ss.hasPermission('system:platform-tenant:update')")
    public CommonResult<Boolean> updatePlatformTenant(@Valid @RequestBody PlatformTenantUpdateReqVO updateReqVO) {
        platformTenantService.updatePlatformTenant(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除平台租户关系")
    @OperateLog(type=DELETE)
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:platform-tenant:delete')")
    public CommonResult<Boolean> deletePlatformTenant(@RequestParam("id") Long id) {
        platformTenantService.deletePlatformTenant(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得平台租户关系")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:platform-tenant:query')")
    public CommonResult<PlatformTenantRespVO> getPlatformTenant(@RequestParam("id") Long id) {
        PlatformTenantDO platformTenant = platformTenantService.getPlatformTenant(id);
        return success(PlatformTenantConvert.INSTANCE.convert(platformTenant));
    }

    @GetMapping("/list")
    @Operation(summary = "获得平台租户关系列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('system:platform-tenant:query')")
    public CommonResult<List<PlatformTenantRespVO>> getPlatformTenantList(@RequestParam("ids") Collection<Long> ids) {
        List<PlatformTenantDO> list = platformTenantService.getPlatformTenantList(ids);
        return success(PlatformTenantConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得平台租户关系分页")
    @PreAuthorize("@ss.hasPermission('system:platform-tenant:query')")
    public CommonResult<PageResult<PlatformTenantRespVO>> getPlatformTenantPage(@Valid PlatformTenantPageReqVO pageVO) {
        PageResult<PlatformTenantDO> pageResult = platformTenantService.getPlatformTenantPage(pageVO);
        return success(PlatformTenantConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出平台租户关系 Excel")
    @PreAuthorize("@ss.hasPermission('system:platform-tenant:export')")
    @OperateLog(type = EXPORT)
    public void exportPlatformTenantExcel(@Valid PlatformTenantExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<PlatformTenantDO> list = platformTenantService.getPlatformTenantList(exportReqVO);
        // 导出 Excel
        List<PlatformTenantExcelVO> datas = PlatformTenantConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "平台租户关系.xls", "数据", PlatformTenantExcelVO.class, datas);
    }

}
