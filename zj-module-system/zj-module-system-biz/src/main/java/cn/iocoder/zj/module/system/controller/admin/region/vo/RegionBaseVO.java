package cn.iocoder.zj.module.system.controller.admin.region.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

/**
* 地区 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class RegionBaseVO {

    @Schema(description = "地区名称", required = true)
    @NotNull(message = "地区名称不能为空")
    private String regionName;

    @Schema(description = "地区缩写")
    private String regionShortName;

    @Schema(description = "行政地区编号")
    private String regionCode;

    @Schema(description = "地区父id")
    private String regionParentId;

    @Schema(description = "地区级别 1-省、自治区、直辖市 2-地级市、地区、自治州、盟 3-市辖区、县级市、县")
    private Integer regionLevel;

}
