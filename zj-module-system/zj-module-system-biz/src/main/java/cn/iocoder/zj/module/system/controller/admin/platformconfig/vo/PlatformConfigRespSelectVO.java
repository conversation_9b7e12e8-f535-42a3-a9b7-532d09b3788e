package cn.iocoder.zj.module.system.controller.admin.platformconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * @ClassName : PlatformConfigRespSelectVO  //类名
 * @Description : 平台配置下拉框  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/7/4  14:28
 */

@Schema(description = "管理后台 - 平台配置 select VO")
@Data
public class PlatformConfigRespSelectVO {
    @Schema(description = "平台ID")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;
}
