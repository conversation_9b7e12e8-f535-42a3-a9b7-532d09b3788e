package cn.iocoder.zj.module.system.controller.admin.tenant.vo.packages;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Set;

/**
* 租户套餐 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class TenantPackageBaseVO {

    @Schema(description = "套餐名", required = true, example = "VIP")
    @NotNull(message = "套餐名不能为空")
    @Size(max = 30, message = "套餐名最大长度为30个字符")
    private String name;

    @Schema(description = "状态，参见 CommonStatusEnum 枚举", required = true, example = "1" )
    @NotNull(message = "状态不能为空")
    private Integer status;

    @Schema(description = "备注", example = "好")
    @Size(max = 255, message = "备注最大长度为255个字符")
    private String remark;

    @Schema(description = "关联的菜单编号", required = true)
    @NotNull(message = "关联的菜单编号不能为空")
    private Set<Long> menuIds;

}