package cn.iocoder.zj.module.system.convert.screen;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.system.controller.admin.screen.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.screen.ScreenDO;

/**
 * 系统设置大屏 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ScreenConvert {

    ScreenConvert INSTANCE = Mappers.getMapper(ScreenConvert.class);

    ScreenDO convert(ScreenCreateReqVO bean);

    ScreenDO convert(ScreenUpdateReqVO bean);

    ScreenRespVO convert(ScreenDO bean);

    List<ScreenRespVO> convertList(List<ScreenDO> list);

    PageResult<ScreenRespVO> convertPage(PageResult<ScreenDO> page);

    List<ScreenExcelVO> convertList02(List<ScreenDO> list);

}
