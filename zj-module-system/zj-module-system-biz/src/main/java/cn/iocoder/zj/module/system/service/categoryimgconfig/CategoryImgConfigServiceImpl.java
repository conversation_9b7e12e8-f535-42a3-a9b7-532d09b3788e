package cn.iocoder.zj.module.system.service.categoryimgconfig;

import cn.hutool.core.io.IoUtil;
import cn.iocoder.zj.framework.common.constants.FileTypeConstants;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.util.string.StringUtil;
import cn.iocoder.zj.module.infra.api.file.FileApi;
import cn.iocoder.zj.module.system.controller.admin.categoryimgconfig.vo.*;
import cn.iocoder.zj.module.system.convert.categoryimgconfig.CategoryImgConfigConvert;
import cn.iocoder.zj.module.system.dal.dataobject.categoryimgconfig.CategoryImgConfigDO;
import cn.iocoder.zj.module.system.dal.mysql.categoryimgconfig.CategoryImgConfigMapper;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.system.enums.ErrorCodeConstants.CATEGORY_IMG_CONFIG_NOT_EXISTS;

/**
 * 类别图片管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CategoryImgConfigServiceImpl implements CategoryImgConfigService {

    @Resource
    private CategoryImgConfigMapper categoryImgConfigMapper;

    @Resource
    private FileApi fileApi;

    @Override
    public Long createCategoryImgConfig(CategoryImgConfigCreateReqVO createReqVO) {
        // 插入
        CategoryImgConfigDO categoryImgConfig = CategoryImgConfigConvert.INSTANCE.convert(createReqVO);
        categoryImgConfigMapper.insert(categoryImgConfig);
        // 返回
        return categoryImgConfig.getId();
    }

    @Override
    public void updateCategoryImgConfig(CategoryImgConfigUpdateReqVO updateReqVO) {
        // 校验存在
        validateCategoryImgConfigExists(updateReqVO.getId());
        // 更新
        CategoryImgConfigDO updateObj = CategoryImgConfigConvert.INSTANCE.convert(updateReqVO);
        categoryImgConfigMapper.updateById(updateObj);
    }

    @Override
    public void deleteCategoryImgConfig(Long id) {
        // 校验存在
        validateCategoryImgConfigExists(id);
        // 删除
        categoryImgConfigMapper.deleteById(id);
    }

    private void validateCategoryImgConfigExists(Long id) {
        if (categoryImgConfigMapper.selectById(id) == null) {
            throw exception(CATEGORY_IMG_CONFIG_NOT_EXISTS);
        }
    }

    @Override
    public CategoryImgConfigDO getCategoryImgConfig(Long id) {
        return categoryImgConfigMapper.selectById(id);
    }

    @Override
    public List<CategoryImgConfigDO> getCategoryImgConfigList(Collection<Long> ids) {
        return categoryImgConfigMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<CategoryImgConfigDO> getCategoryImgConfigPage(CategoryImgConfigPageReqVO pageReqVO) {
        return categoryImgConfigMapper.selectPage(pageReqVO);
    }

    @Override
    public List<CategoryImgConfigDO> getCategoryImgConfigList(CategoryImgConfigReqVO baseVO) {
        return categoryImgConfigMapper.selectList(baseVO);
    }

    @Override
    public List<CategoryImgConfigDO> getCategoryImgConfigList(CategoryImgConfigExportReqVO exportReqVO) {
        return categoryImgConfigMapper.selectList(exportReqVO);
    }

    @Override
    @SneakyThrows
    public String uploadCategoryImg(MultipartFile file) {
        // 存储文件
        String path = StringUtil.getSavePath(file.getOriginalFilename(), FileTypeConstants.IMG_TYPE, null);
        return fileApi.createFileUrl(file.getOriginalFilename(), path, IoUtil.readBytes(file.getInputStream()));
    }

}
