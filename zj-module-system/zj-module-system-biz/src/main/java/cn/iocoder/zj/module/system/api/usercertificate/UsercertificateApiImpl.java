package cn.iocoder.zj.module.system.api.usercertificate;

import cn.hutool.core.convert.Convert;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.util.object.BeanUtils;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.zj.module.system.api.usercertificate.dto.UserCertificateRespDTO;
import cn.iocoder.zj.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.zj.module.system.dal.dataobject.usercertificate.UserCertificateDO;
import cn.iocoder.zj.module.system.service.usercertificate.UserCertificateService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

/**
 * @ClassName : UsercertificateApiImpl  //类名
 * @Description : 密码管理实现  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/4/1  16:02
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class UsercertificateApiImpl implements UsercertificateApi {
    @Autowired
    UserCertificateService userCertificateService;
    @Override
    public CommonResult<UserCertificateRespDTO> getUser(String id) {
        UserCertificateDO user = userCertificateService.getUserCertificate(id);
        return success(BeanUtils.toBean(user, UserCertificateRespDTO.class));
    }
}
