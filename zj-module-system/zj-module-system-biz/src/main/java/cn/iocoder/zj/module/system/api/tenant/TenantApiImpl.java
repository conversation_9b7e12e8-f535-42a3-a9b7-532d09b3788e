package cn.iocoder.zj.module.system.api.tenant;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.ip.core.Area;
import cn.iocoder.zj.framework.ip.core.utils.AreaUtils;
import cn.iocoder.zj.module.system.api.tenant.dto.TenantRespDTO;
import cn.iocoder.zj.module.system.api.tenant.dto.TenantUserDTO;
import cn.iocoder.zj.module.system.service.tenant.TenantService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class TenantApiImpl implements TenantApi {

    @Resource
    private TenantService tenantService;

    @Override
    public CommonResult<List<Long>> getTenantIdList() {
        return success(tenantService.getTenantIdList());
    }

    @Override
    public CommonResult<Boolean> validTenant(Long id) {
        tenantService.validTenant(id);
        return success(true);
    }
    @Override
    public CommonResult<TenantRespDTO> getTenantSimpleInfo(Long id) {
        return success(BeanUtil.copyProperties(tenantService.getTenant(id),TenantRespDTO.class));
    }

    @Override
    public CommonResult<TenantRespDTO> getRegionInfo(Long id) {
        TenantRespDTO tenantRespDTO = new TenantRespDTO();
        Area area = AreaUtils.getArea(Integer.parseInt(id.toString()));
        tenantRespDTO.setRegionName(area.getName());
        tenantRespDTO.setRegionId(area.getId());
        return success(tenantRespDTO);
    }

    @Override
    public CommonResult<List<String>>  getParentRegionInfo(List<Long> regionIds,Integer type) {
        List<String> areas=new ArrayList<>();
        for (int i=0;i<regionIds.size();i++){
            Area area = AreaUtils.getArea(Integer.parseInt(regionIds.get(i).toString()));
            Area parentArea = getCity(area,type);
            areas.add(parentArea.getId().toString());
        }
        List<String> area=areas.stream().distinct().collect(Collectors.toList());
        return success(area);
    }

    @Override
    public CommonResult<List<String>> getChildrenRegionInfo(Long regionId) {
        Area area = AreaUtils.getArea(Integer.parseInt(regionId.toString()));
        List<String> areas=new ArrayList<>();
        for (int i=0;i<area.getChildren().size();i++){
            areas.add(area.getChildren().get(i).getId().toString());
        }
        return success(areas);
    }

    @Override
    public CommonResult<List<TenantUserDTO>> getTenantAllIds() {
        List<TenantUserDTO> list=tenantService.getTenantAllIds();
        return success(list);
    }

    @Override
    public CommonResult<List<Long>> getTenantIdsByPlatform(Long tenantId) {
        List<Long> list=tenantService.getTenantIdsByPlatform(tenantId);
        return success(list);
    }

    @Override
    public CommonResult<Long> getStateByTenantId(Long tenantId) {
        return success(tenantService.getStateByTenantId(tenantId));
    }

    private Area getProvince(Area area){
        if(area.getType()==2){
            return area;
        }
        return getProvince(area.getParent());
    }

    private Area getCity(Area area,Integer type){
        if(area.getType()==type){
            return area;
        }
        return getCity(area.getParent(),type);
    }
}
