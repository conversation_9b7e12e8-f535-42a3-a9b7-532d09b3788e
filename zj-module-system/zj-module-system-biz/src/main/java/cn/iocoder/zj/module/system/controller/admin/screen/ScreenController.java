package cn.iocoder.zj.module.system.controller.admin.screen;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.module.system.dal.dataobject.screen.ScreenConfigDO;
import com.alibaba.nacos.common.utils.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.system.controller.admin.screen.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.screen.ScreenDO;
import cn.iocoder.zj.module.system.convert.screen.ScreenConvert;
import cn.iocoder.zj.module.system.service.screen.ScreenService;

@Tag(name = "管理后台 - 系统设置大屏")
@RestController
@RequestMapping("/system/screen")
@Validated
public class ScreenController {

    @Resource
    private ScreenService screenService;

    @PostMapping("/create")
    @Operation(summary = "创建大屏监控项")
    @OperateLog(type=CREATE)
    @PreAuthorize("@ss.hasPermission('system:screen:create')")
    public CommonResult<Long> createScreen(@Valid @RequestBody ScreenCreateReqVO createReqVO) {
        return success(screenService.createScreen(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新大屏监控项顺序")
    @OperateLog(type=UPDATE)
    @PreAuthorize("@ss.hasPermission('system:screen:update')")
    public CommonResult<Boolean> updateScreen(@RequestBody ScreenUpdateReqVO updateReqVO) {
        screenService.updateScreen(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除大屏监控项")
    @OperateLog(type=DELETE)
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:screen:delete')")
    public CommonResult<Boolean> deleteScreen(@RequestParam("id") Long id) {
        screenService.deleteScreen(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得大屏监控项")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:screen:query')")
    public CommonResult<ScreenRespVO> getScreen(@RequestParam("id") Long id) {
        ScreenDO screen = screenService.getScreen(id);
        return success(ScreenConvert.INSTANCE.convert(screen));
    }

    @GetMapping("/list")
    @Operation(summary = "获得大屏监控项列表")
    @Parameter(name = "tenantId", description = "租户编号", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('system:screen:query')")
    public CommonResult<List<ScreenRespVO>> getScreenList(@RequestParam("tenantId") Long tenantId) {
        List<ScreenDO> list = screenService.getScreenList(tenantId);
        return success(ScreenConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得大屏监控项分页")
    @PreAuthorize("@ss.hasPermission('system:screen:query')")
    public CommonResult<PageResult<ScreenRespVO>> getScreenPage(@Valid ScreenPageReqVO pageVO) {
        PageResult<ScreenDO> pageResult = screenService.getScreenPage(pageVO);
        return success(ScreenConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出系统设置大屏 Excel")
    @OperateLog(type = EXPORT)
    public void exportScreenExcel(@Valid ScreenExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<ScreenDO> list = screenService.getScreenList(exportReqVO);
        // 导出 Excel
        List<ScreenExcelVO> datas = ScreenConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "系统设置大屏.xls", "数据", ScreenExcelVO.class, datas);
    }

    @GetMapping("/tenantScreenList")
    @Operation(summary = "获得租户自定义大屏监控项列表")
    @Parameter(name = "tenantId", description = "租户编号", required = true, example = "1")
    public CommonResult<List<ScreenRespVO>> getTenantScreenList(@RequestParam("tenantId") Long tenantId) {
        List<ScreenDO> list = screenService.getTenantScreenList(tenantId);
        return success(ScreenConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/getScreenConfig")
    @Operation(summary = "获取租户的大屏配置")
    @Parameter(name = "tenantId", description = "租户编号", required = true, example = "1")
    public CommonResult<List<ScreenConfigDO>> getScreenConfigByTenantId(@RequestParam("tenantId") Long tenantId) {
        List<ScreenConfigDO> list = screenService.getScreenConfigByTenantId(tenantId);
        if(CollectionUtils.isEmpty(list)){
            list.add(new ScreenConfigDO().setTenantId(tenantId));
        }
        return success(list);
    }
    @PostMapping("/updateScreenConfig")
    @Operation(summary = "创建大屏配置")
    @OperateLog(type=CREATE)
    @PreAuthorize("@ss.hasPermission('system:screenconfig:update')")
    public CommonResult<Boolean> updateScreenConfig(@Valid @RequestBody ScreenConfigReqVO createReqVO) {
        return success(screenService.updateScreenConfig(createReqVO));
    }
}
