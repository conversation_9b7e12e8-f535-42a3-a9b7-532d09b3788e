package cn.iocoder.zj.module.system.dal.mysql.dict;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.module.system.controller.admin.dict.vo.data.DictDataExportReqVO;
import cn.iocoder.zj.module.system.controller.admin.dict.vo.data.DictDataPageReqVO;
import cn.iocoder.zj.module.system.dal.dataobject.dict.DictDataDO;
import cn.iocoder.zj.module.system.dal.dataobject.dict.DictTypeDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jodd.util.StringUtil;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

@Mapper
public interface DictDataMapper extends BaseMapperX<DictDataDO> {

    default DictDataDO selectByDictTypeAndValue(String dictType, String value) {

        return  selectOne(new LambdaQueryWrapper<DictDataDO>()
                .eq(DictDataDO::getDictType, dictType)
                .eq(DictDataDO::getValue, value)
                .eq(DictDataDO::getDeleted,0));
    }

    default DictDataDO selectByDictTypeAndLabel(String dictType, String label) {
        return selectOne(DictDataDO::getDictType, dictType, DictDataDO::getLabel, label);
    }

    default List<DictDataDO> selectByDictTypeAndValues(String dictType, Collection<String> values) {
        return selectList(new LambdaQueryWrapper<DictDataDO>().eq(DictDataDO::getDictType, dictType)
                .in(DictDataDO::getValue, values));
    }

    default long selectCountByDictType(String dictType) {
        return selectCount(DictDataDO::getDictType, dictType);
    }

    default PageResult<DictDataDO> selectPage(DictDataPageReqVO reqVO) {
        LambdaQueryWrapper<DictDataDO> dictDataDOLambdaQueryWrapper = new LambdaQueryWrapperX<DictDataDO>()
                .likeIfPresent(DictDataDO::getLabel, reqVO.getLabel())
                .eqIfPresent(DictDataDO::getDictType, reqVO.getDictType())
                .eqIfPresent(DictDataDO::getStatus, reqVO.getStatus());

        if (StringUtil.isNotEmpty(reqVO.getSortBy()) && reqVO.getSortDirection().equals("asc")){
            dictDataDOLambdaQueryWrapper.orderByAsc(DictDataDO::getCreateTime);
        }else {
            dictDataDOLambdaQueryWrapper.orderByDesc(DictDataDO::getCreateTime);
        }
        return selectPage(reqVO,dictDataDOLambdaQueryWrapper);
    }

    default List<DictDataDO> selectList(DictDataExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DictDataDO>()
                .likeIfPresent(DictDataDO::getLabel, reqVO.getLabel())
                .eqIfPresent(DictDataDO::getDictType, reqVO.getDictType())
                .eqIfPresent(DictDataDO::getStatus, reqVO.getStatus()));
    }

    @Delete("DELETE FROM system_dict_data WHERE id = #{id}")
    void deleteDictData(@Param("id") Long id);

    default List<DictDataDO> selectOpenList(String dictType) {
        return selectList(new LambdaQueryWrapperX<DictDataDO>()
                .eqIfPresent(DictDataDO::getDictType, dictType)
                .eqIfPresent(DictDataDO::getStatus, 0));
    }

}
