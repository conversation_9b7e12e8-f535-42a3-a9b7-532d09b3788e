package cn.iocoder.zj.module.system.controller.admin.user.vo.user;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Set;

@Schema(description = "管理后台 - 用户信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UserRespVO extends UserBaseVO {

    @Schema(description = "用户编号", required = true, example = "1")
    private Long id;

    @Schema(description = "状态，参见 CommonStatusEnum 枚举类", required = true, example = "1" )
    private Integer status;

    @Schema(description = "状态，参见 CommonStatusEnum 枚举类", required = true, example = "1" )
    private Set<Long> roleIds;

    @Schema(description = "最后登录 IP", required = true, example = "***********")
    private String loginIp;

    @Schema(description = "最后登录时间", required = true, example = "时间戳格式")
    private LocalDateTime loginDate;

    @Schema(description = "创建时间", required = true, example = "时间戳格式")
    private LocalDateTime createTime;

    @Schema(description = "用户角色", required = true, example = "1")
    private String roleNames;

    @Schema(description = "账号是否锁定（0正常 1锁定）", required = true, example = "0")
    private Integer locking;

    @Schema(description = "是否租户管理员账号")
    private Integer isTenantAdmin;
}