package cn.iocoder.zj.module.system.controller.admin.tenant;

import cn.hutool.core.util.ReUtil;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.system.controller.admin.tenant.vo.tenant.*;
import cn.iocoder.zj.module.system.convert.tenant.TenantConvert;
import cn.iocoder.zj.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.zj.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.zj.module.system.enums.logger.LoginLogTypeEnum;
import cn.iocoder.zj.module.system.enums.logger.LoginResultEnum;
import cn.iocoder.zj.module.system.job.wxcom.WxcomSyncDataJob;
import cn.iocoder.zj.module.system.service.auth.AdminAuthService;
import cn.iocoder.zj.module.system.service.tenant.TenantService;
import cn.iocoder.zj.module.system.service.user.AdminUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;
import static cn.iocoder.zj.module.system.enums.ErrorCodeConstants.AUTH_LOGIN_BAD_CREDENTIALS;

@Tag(name = "管理后台 - 租户")
@RestController
@RequestMapping("/system/tenant")
public class TenantController {

    @Resource
    private TenantService tenantService;
    @Resource
    private AdminUserService userService;
    @Resource
    private AdminAuthService authService;
    @Resource
    private WxcomSyncDataJob wxcomSyncDataJob;

    @GetMapping("/get-id-by-name")
    @PermitAll
    @Operation(summary = "使用手机号/邮箱号，获得租户编号", description = "登录界面，根据用户的租户名，获得租户编号")
    @Parameter(name = "name", description = "手机号/邮箱号", required = true, example = "1024")
    @TenantIgnore
    public CommonResult<Long> getTenantIdByName(@RequestParam("name") String name) {
        final LoginLogTypeEnum logTypeEnum = LoginLogTypeEnum.LOGIN_USERNAME;
        AdminUserDO user = new AdminUserDO();
        boolean isMatch = ReUtil.contains("@", name);
        // 如果是邮箱
        if (isMatch) {
            user = userService.getUserByMail(name);
        } else {
            // 手机号
            user = userService.getUserByMobile(name);
        }
        if (user == null) {
            authService.createLoginLog(null, name, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
        }
        TenantDO tenantDOs = tenantService.getTenant(user.getTenantId());
        TenantDO tenantDO = tenantService.getTenantByName(tenantDOs.getName());
        return success(tenantDO != null ? tenantDO.getId() : null);
    }

    @PostMapping("/create")
    @Operation(summary = "创建租户")
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('system:tenant:create')")
    public CommonResult<Long> createTenant(@Valid @RequestBody TenantCreateReqVO createReqVO) {
        return success(tenantService.createTenant(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新租户")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('system:tenant:update')")
    @TenantIgnore
    public CommonResult<Boolean> updateTenant(@Valid @RequestBody TenantUpdateReqVO updateReqVO) {
        tenantService.updateTenant(updateReqVO);
        return success(true);
    }

    @PutMapping("/updateBasicTenant")
    @Operation(summary = "租户基本信息修改")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('system:tenant:updateBasicTenant')")
    @TenantIgnore
    public CommonResult<Boolean> updateBasicTenant(@Valid @RequestBody TenantUpdateBasicReqVo updateReqVO) {
        TenantUpdateReqVO tenantUpdateReqVO = new TenantUpdateReqVO();
        BeanUtils.copyProperties(updateReqVO, tenantUpdateReqVO);
        tenantService.updateTenant(tenantUpdateReqVO);
        return success(true);
    }


    @PutMapping("/updateRootTenant")
    @Operation(summary = "租户信息管理员修改")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('system:tenant:updateRootTenant')")
    @TenantIgnore
    public CommonResult<Boolean> updateRootTenant(@Valid @RequestBody TenantUpdateRootReqVo updateReqVO) {
        tenantService.updateRootTenant(updateReqVO);
        return success(true);
    }

    @PutMapping("/updateMaintainTenant")
    @Operation(summary = "租户信息运维修改")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('system:tenant:updateMaintainTenant')")
    @TenantIgnore
    public CommonResult<Boolean> updateMaintainTenant(@Valid @RequestBody TenantUpdateMaintainReqVo updateReqVO) {
        tenantService.updateMaintainTenant(updateReqVO);
        return success(true);
    }


    @DeleteMapping("/delete")
    @Operation(summary = "删除租户")
    @OperateLog(type = DELETE)
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:tenant:delete')")
    public CommonResult<Boolean> deleteTenant(@RequestParam("id") Long id) {
        tenantService.deleteTenant(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得租户")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @TenantIgnore
    public CommonResult<TenantRespVO> getTenant(@RequestParam("id") Long id) {
        TenantRespVO tenant = tenantService.getTenantById(id);
        return success(tenant);
    }

    @GetMapping("/getTenantByRole")
    @Operation(summary = "获取用户绑定的租户")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @TenantIgnore
    public CommonResult<List<TenantUserRespVO>> getTenantByRole() {
        List<TenantUserRespVO> list = tenantService.getTenantByRole();
        return success(list);
    }

    @GetMapping("/page")
    @Operation(summary = "获得租户分页")
    @TenantIgnore
    public CommonResult<PageResult<TenantRespVO>> getTenantPage(@Valid TenantPageReqVO pageVO) {
        PageResult<TenantRespVO> pageResult = tenantService.getTenantUserPage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出租户 Excel")
    @OperateLog(type = EXPORT)
    @TenantIgnore
    public void exportTenantExcel(@Valid TenantExportReqVO exportReqVO,
                                  HttpServletResponse response) throws IOException {
        // 导出 Excel
        List<TenantExcelVO> datas = tenantService.getTenantExcelList(exportReqVO);
        ExcelUtils.write(response, "租户.xls", "数据", TenantExcelVO.class, datas);
    }

    @GetMapping("/list-all-simple")
    @Operation(summary = "获取用户精简信息列表", description = "只包含被开启的租户，主要用于前端的下拉选项")
    public CommonResult<List<TenantSimpleRespVo>> getSimpleUsers() {
        // 获取租户列表，只要开启状态的
        List<TenantDO> list = tenantService.getUserListByStatus(CommonStatusEnum.ENABLE.getStatus());
        // 排序后，返回给前端
        return success(TenantConvert.INSTANCE.convertList04(list));
    }


    @GetMapping("/list")
    @Operation(summary = "获得项目下拉框")
    @TenantIgnore
    public CommonResult<List<TenantDO>> getTenantList() {
        List<TenantDO> pageResult = tenantService.getTenantSelectList();
        return success(pageResult);
    }

    @GetMapping("/checkLicense")
    public void checkLicense() {
        wxcomSyncDataJob.checkLicense();
    }
}