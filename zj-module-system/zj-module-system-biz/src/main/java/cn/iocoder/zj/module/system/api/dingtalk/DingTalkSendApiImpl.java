package cn.iocoder.zj.module.system.api.dingtalk;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.system.service.dingtalk.DingTalkSendService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class DingTalkSendApiImpl implements DingTalkSendApi {

    @Resource
    private DingTalkSendService dingTalkSendService;


    @Override
    public CommonResult<Boolean> sendSingleDingTalkToMember(Map<String, Object> templateParams) {
        try {
            dingTalkSendService.sendSingleWeChatToMember(templateParams);
        }catch (Exception e){
            e.printStackTrace();
        }
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> sendSingleWeComToMember(Map<String, Object> templateParams) {
        try {
            dingTalkSendService.sendSingleWeComToMember(templateParams);
        }catch (Exception e){
            e.printStackTrace();
        }
        return CommonResult.success(true);
    }
}
