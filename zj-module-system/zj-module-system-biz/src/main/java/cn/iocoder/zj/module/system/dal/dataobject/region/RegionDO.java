package cn.iocoder.zj.module.system.dal.dataobject.region;

import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 地区 DO
 *
 * <AUTHOR>
 */
@TableName("system_region")
@KeySequence("system_region_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RegionDO extends BaseDO {


    /**
     * 菜单编号 - 根节点
     */
    public static final String REG_ID  = "-1";
    /**
     * 地区主键编号
     */
    @TableId(type = IdType.INPUT)
    private String id;
    /**
     * 地区名称
     */
    private String regionName;
    /**
     * 地区缩写
     */
    private String regionShortName;
    /**
     * 行政地区编号
     */
    private String regionCode;
    /**
     * 地区父id
     */
    private String regionParentId;
    /**
     * 地区级别 1-省、自治区、直辖市 2-地级市、地区、自治州、盟 3-市辖区、县级市、县
     */
    private Integer regionLevel;

}
