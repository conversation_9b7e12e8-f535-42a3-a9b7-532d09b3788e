package cn.iocoder.zj.module.system.controller.admin.wechatbinding.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

/**
* 微信公众号OpenId与租户绑定关系 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class WechatBindingBaseVO {

    @Schema(description = "用户编号")
    private Long userId;

    @Schema(description = "微信公众号唯一标识")
    private String openId;

    @Schema(description = "邮箱地址")
    private String email;

    @Schema(description = "是否启用微信公众号")
    private Boolean isWechat;

    @Schema(description = "是否启用微信邮箱")
    private Boolean isEmail;

}
