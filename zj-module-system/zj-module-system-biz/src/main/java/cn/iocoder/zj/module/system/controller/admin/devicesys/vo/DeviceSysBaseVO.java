package cn.iocoder.zj.module.system.controller.admin.devicesys.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

/**
* 系统类型 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class DeviceSysBaseVO {

    @Schema(description = "系统类型")
    @Size(max = 50, message = "系统类型最大长度为50个字符")
    private String sysType;

    @Schema(description = "系统类型名称")
    @Size(max = 50, message = "系统类型名称最大长度为50个字符")
    private String sysName;

}
