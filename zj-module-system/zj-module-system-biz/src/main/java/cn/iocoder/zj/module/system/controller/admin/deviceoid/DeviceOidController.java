package cn.iocoder.zj.module.system.controller.admin.deviceoid;

import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.system.api.deviceoid.dto.DeviceOidDTO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;

import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.system.controller.admin.deviceoid.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.deviceoid.DeviceOidDO;
import cn.iocoder.zj.module.system.convert.deviceoid.DeviceOidConvert;
import cn.iocoder.zj.module.system.service.deviceoid.DeviceOidService;

@Tag(name = "管理后台 - OID管理")
@RestController
@RequestMapping("/system/device-oid")
@Validated
public class DeviceOidController {

    @Resource
    private DeviceOidService deviceOidService;

    @PostMapping("/create")
    @OperateLog(type=CREATE)
    @PreAuthorize("@ss.hasPermission('system:device-oid:create')")
    @Operation(summary = "创建OID管理")
    @PermitAll
    public CommonResult<Long> createDeviceOid(@Valid @RequestBody DeviceOidCreateReqVO createReqVO) {
        return success(deviceOidService.createDeviceOid(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新OID管理")
    @OperateLog(type=UPDATE)
    @PreAuthorize("@ss.hasPermission('system:device-oid:update')")
    @PermitAll
    public CommonResult<Boolean> updateDeviceOid(@Valid @RequestBody DeviceOidUpdateReqVO updateReqVO) {
        deviceOidService.updateDeviceOid(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除OID管理")
    @OperateLog(type=DELETE)
    @PreAuthorize("@ss.hasPermission('system:device-oid:delete')")
    @Parameter(name = "id", description = "编号", required = true)
    @PermitAll
    public CommonResult<Boolean> deleteDeviceOid(@RequestParam("id") Long id) {
        deviceOidService.deleteDeviceOid(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得OID管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PermitAll
    public CommonResult<DeviceOidRespVO> getDeviceOid(@RequestParam("id") Long id) {
        DeviceOidDO deviceOid = deviceOidService.getDeviceOid(id);
        return success(DeviceOidConvert.INSTANCE.convert(deviceOid));
    }

    @GetMapping("/list")
    @Operation(summary = "获得OID管理列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PermitAll
    public CommonResult<List<DeviceOidRespVO>> getDeviceOidList(@RequestParam("ids") Collection<Long> ids) {
        List<DeviceOidDO> list = deviceOidService.getDeviceOidList(ids);
        return success(DeviceOidConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得OID管理分页")
    @PermitAll
    public CommonResult<PageResult<DeviceOidRespVO>> getDeviceOidPage(@Valid DeviceOidPageReqVO pageVO) {
        PageResult<DeviceOidDO> pageResult = deviceOidService.getDeviceOidPage(pageVO);
        return success(DeviceOidConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出OID管理 Excel")
    @PermitAll
    @OperateLog(type = EXPORT)
    public void exportDeviceOidExcel(@Valid DeviceOidExportReqVO exportReqVO,
                                     HttpServletResponse response) throws IOException {
        List<DeviceOidDO> list = deviceOidService.getDeviceOidList(exportReqVO);
        // 导出 Excel
        List<DeviceOidExcelVO> datas = DeviceOidConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "OID管理.xls", "数据", DeviceOidExcelVO.class, datas);
    }


    @TenantIgnore
    @PermitAll
    @RequestMapping("/getDeviceOidList")
    @ResponseBody
    public Map getDeviceOidList(@RequestBody(required = false) String deviceType,@RequestParam(required = false,defaultValue = "") String token_desc) {
       return deviceOidService.getDeviceOidListByDevice(deviceType,token_desc);
    }

}
