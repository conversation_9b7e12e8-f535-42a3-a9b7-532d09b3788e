package cn.iocoder.zj.module.system.convert.devicesys;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.system.controller.admin.devicesys.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.devicesys.DeviceSysDO;

/**
 * 系统类型 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DeviceSysConvert {

    DeviceSysConvert INSTANCE = Mappers.getMapper(DeviceSysConvert.class);

    DeviceSysDO convert(DeviceSysCreateReqVO bean);

    DeviceSysDO convert(DeviceSysUpdateReqVO bean);

    DeviceSysRespVO convert(DeviceSysDO bean);

    List<DeviceSysRespVO> convertList(List<DeviceSysDO> list);

    PageResult<DeviceSysRespVO> convertPage(PageResult<DeviceSysDO> page);

    List<DeviceSysExcelVO> convertList02(List<DeviceSysDO> list);

}
