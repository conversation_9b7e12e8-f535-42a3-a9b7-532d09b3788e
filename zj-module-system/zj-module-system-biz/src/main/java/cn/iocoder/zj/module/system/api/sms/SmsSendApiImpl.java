package cn.iocoder.zj.module.system.api.sms;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.system.api.sms.dto.send.SmsSendSingleToUserReqDTO;
import cn.iocoder.zj.module.system.dal.dataobject.sms.SmsTemplateDO;
import cn.iocoder.zj.module.system.service.sms.SmsSendService;
import com.alibaba.fastjson.JSON;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.util.Map;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class SmsSendApiImpl implements SmsSendApi {

    @Resource
    private SmsSendService smsSendService;

    @Override
    public CommonResult<Long> sendSingleSmsToAdmin(SmsSendSingleToUserReqDTO reqDTO) {
        return success(smsSendService.sendSingleSmsToAdmin(reqDTO.getMobile(), reqDTO.getUserId(),
                reqDTO.getTemplateCode(), reqDTO.getTemplateParams()));
    }

    @Override
    public CommonResult<Long> sendSingleSmsToMember(SmsSendSingleToUserReqDTO reqDTO) {
        return success(smsSendService.sendSingleSmsToMember(reqDTO.getMobile(), reqDTO.getUserId(),
                reqDTO.getTemplateCode(), reqDTO.getTemplateParams()));
    }

    @Override
    public CommonResult<Map<String, String>> getTemplate(Long id) {
        SmsTemplateDO result = smsSendService.getTemplate(id);
        return success(JSON.parseObject(JSON.toJSONString(result), Map.class));
    }

}
