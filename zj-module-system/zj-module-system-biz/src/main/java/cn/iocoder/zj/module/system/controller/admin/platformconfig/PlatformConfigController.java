package cn.iocoder.zj.module.system.controller.admin.platformconfig;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.util.crypto.CryptoUtil;
import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.zj.module.system.controller.admin.platformconfig.vo.*;
import cn.iocoder.zj.module.system.convert.platformconfig.PlatformConfigConvert;
import cn.iocoder.zj.module.system.dal.dataobject.platformconfig.PlatformConfigDO;
import cn.iocoder.zj.module.system.mq.producer.platform.PlatformProducer;
import cn.iocoder.zj.module.system.service.platformconfig.PlatformConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

@Tag(name = "管理后台 - 平台配置")
@RestController
@RequestMapping("/system/platform-config")
@Validated
public class PlatformConfigController {

    @Resource
    private PlatformConfigService platformConfigService;
    @Resource
    PlatformProducer platformProducer;

    @PostMapping("/create")
    @Operation(summary = "创建平台配置")
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('system:platform-config:create')")
    public CommonResult<Long> createPlatformConfig(@Valid @RequestBody PlatformConfigCreateReqVO createReqVO) {
//        if (createReqVO.isDetected()) {
//            // Probe    进行探测
//            platformConfigService.detectMonitor(createReqVO.getUrlHttp());
//        }
        return success(platformConfigService.createPlatformConfig(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新平台配置")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('system:platform-config:update')")
    public CommonResult<Boolean> updatePlatformConfig(@Valid @RequestBody PlatformConfigUpdateReqVO updateReqVO) {
//        if (updateReqVO.isDetected()) {
//            // Probe    进行探测
//            platformConfigService.detectMonitor(updateReqVO.getUrlHttp());
//        }
        platformConfigService.updatePlatformConfig(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除平台配置")
    @OperateLog(type = DELETE)
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:platform-config:delete')")
    public CommonResult<Boolean> deletePlatformConfig(@RequestParam("id") Long id) {
        platformConfigService.deletePlatformConfig(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得平台配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:platform-config:query')")
    public CommonResult<PlatformConfigRespVO> getPlatformConfig(@RequestParam("id") Long id) {
        List<String> idList = new ArrayList<>();
        idList.add(String.valueOf(id));
        List<Map> collectors = platformConfigService.getPlatformCollector(idList);
        String name = "";
        String ids = "";
        for (Map collector : collectors) {
            name = Convert.toStr(collector.get("collectorName"));
            ids = Convert.toStr(collector.get("collectorId"));
        }
        PlatformConfigDO platformConfig = platformConfigService.getPlatformConfig(id);
        platformConfig.setCollectorName(name);
        platformConfig.setCollectorId(ids);

        return success(PlatformConfigConvert.INSTANCE.convert(platformConfig));
    }

    @GetMapping("/list")
    @Operation(summary = "获得平台配置列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('system:platform-config:query')")
    public CommonResult<List<PlatformConfigRespVO>> getPlatformConfigList(@RequestParam("ids") Collection<Long> ids) {
        List<PlatformConfigDO> list = platformConfigService.getPlatformConfigList(ids);
        return success(PlatformConfigConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得平台配置分页")
    @PreAuthorize("@ss.hasPermission('system:platform-config:query')")
    public CommonResult<PageResult<PlatformConfigRespVO>> getPlatformConfigPage(@Valid PlatformConfigPageReqVO pageVO) {
        // 获取分页数据
        PageResult<PlatformConfigDO> pageResult = platformConfigService.getPlatformConfigPage(pageVO);

        // 如果没有数据，直接返回空结果
        if (CollectionUtil.isEmpty(pageResult.getList())) {
            return success(PlatformConfigConvert.INSTANCE.convertPage(pageResult));
        }

        // 处理密码加密并生成 idList
        List<String> idList = pageResult.getList().stream()
                .peek(configDO -> {
                    try {
                        configDO.setPassword(CryptoUtil.encrypt(configDO.getPassword()));
                    } catch (Exception e) {
                        throw new RuntimeException("密码加密失败", e);
                    }
                })
                .map(configDO -> Convert.toStr(configDO.getId()))
                .collect(Collectors.toList());

        // 获取与平台配置相关的 Collector 数据
        List<Map> collectors = platformConfigService.getPlatformCollector(idList);
        Map<String, String> collectorMap = collectors.stream()
                .filter(collector -> collector.get("platform_id") != null && collector.get("collectorName") != null)
                .collect(Collectors.toMap(
                        collector -> collector.get("platform_id").toString(),
                        collector -> collector.get("collectorName").toString(),
                        (existing, replacement) -> existing
                ));
        // 设置每个配置的 collectorName
        pageResult.getList().forEach(configDO -> {
            String collectorName = collectorMap.get(Convert.toStr(configDO.getId()));
            if (collectorName != null) {
                configDO.setCollectorName(collectorName);
            }
        });

        // 返回处理后的分页结果
        return success(PlatformConfigConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出平台配置 Excel")
    @PreAuthorize("@ss.hasPermission('system:platform-config:export')")
    @OperateLog(type = EXPORT)
    public void exportPlatformConfigExcel(@Valid PlatformConfigExportReqVO exportReqVO,
                                          HttpServletResponse response) throws IOException {
        List<PlatformConfigDO> list = platformConfigService.getPlatformConfigList(exportReqVO);
        // 导出 Excel
        List<PlatformConfigExcelVO> datas = PlatformConfigConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "平台配置.xls", "数据", PlatformConfigExcelVO.class, datas);
    }


    @GetMapping("/platformSelect")
    @Operation(summary = "获取平台配置 下拉框")
    @Parameter(name = "tenantId", description = "租户id", required = false)
    @PreAuthenticated
    public CommonResult<List<Map>> getPlatformSelectList(@RequestParam(required = false, defaultValue = "") String tenantId) throws IOException {
        List<Map> list = platformConfigService.getPlatformSelectList(tenantId);
        return success(list);
    }


    @GetMapping("/platformProject")
    @Operation(summary = "获取平台配置 项目下拉框")
    @PreAuthenticated
    public CommonResult<List<Map>> platformProject() throws IOException {
        List<Map> list = platformConfigService.getPlatformProject();
        return success(list);
    }

    @GetMapping("/detect")
    @Operation(summary = "根据监控信息去对此监控进行可用性探测")
    @PreAuthenticated
    public CommonResult<Boolean> detectMonitor(@RequestParam Integer id) throws IOException {
        platformConfigService.detectMonitor(id);
        return success(true);
    }


    @GetMapping("/platformSelectByTenantId")
    @Operation(summary = "获取租户平台下拉框")
    @Parameter(name = "tenantId", description = "租户id", required = false)
    @PreAuthenticated
    public CommonResult<List<Map>> getPlatformSelectByTenantIdList(@RequestParam(required = false, defaultValue = "") String tenantId) {
        return success(platformConfigService.getPlatformSelectByTenantIdList(tenantId));
    }

}
