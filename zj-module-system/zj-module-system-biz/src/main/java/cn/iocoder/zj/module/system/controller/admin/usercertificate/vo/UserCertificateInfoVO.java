package cn.iocoder.zj.module.system.controller.admin.usercertificate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @ClassName : UserCertificateInfoVO  //类名
 * @Description : 密码返回  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/4/2  14:01
 */
@Data
public class UserCertificateInfoVO {

    @Schema(description = "密码id")
    private Long id;

    @Schema(description = "名称")
    private String name;


}
