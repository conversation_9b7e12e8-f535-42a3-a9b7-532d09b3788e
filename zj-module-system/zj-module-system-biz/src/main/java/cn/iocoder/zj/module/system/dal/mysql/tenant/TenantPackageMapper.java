package cn.iocoder.zj.module.system.dal.mysql.tenant;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.module.system.controller.admin.tenant.vo.packages.TenantPackagePageReqVO;
import cn.iocoder.zj.module.system.dal.dataobject.logger.LoginLogDO;
import cn.iocoder.zj.module.system.dal.dataobject.tenant.TenantPackageDO;
import cn.iocoder.zj.module.system.dal.dataobject.user.AdminUserDO;
import jodd.util.StringUtil;
import org.apache.ibatis.annotations.Mapper;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

/**
 * 租户套餐 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantPackageMapper extends BaseMapperX<TenantPackageDO> {

    default PageResult<TenantPackageDO> selectPage(TenantPackagePageReqVO reqVO) {
        LambdaQueryWrapperX<TenantPackageDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.likeIfPresent(TenantPackageDO::getName, reqVO.getName())
                .eqIfPresent(TenantPackageDO::getStatus, reqVO.getStatus())
                .likeIfPresent(TenantPackageDO::getRemark, reqVO.getRemark());
        if(StringUtil.isNotEmpty(reqVO.getStartTime()) && StringUtil.isNotEmpty(reqVO.getEndTime())){
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            try {
                date = sdf.parse(reqVO.getEndTime());
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(date);
                // 把日期设置为当天最后一秒
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                date = calendar.getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            wrapper.betweenIfPresent(TenantPackageDO::getCreateTime,reqVO.getStartTime(),sdf.format(date));
        }
        if (StringUtil.isNotEmpty(reqVO.getSortDirection()) && reqVO.getSortDirection().equals("asc")){
            if (reqVO.getSortBy().equals("createTime")){
                wrapper.orderByAsc(TenantPackageDO::getCreateTime);
            }
        }else {
            wrapper.orderByDesc(TenantPackageDO::getCreateTime);
        }
//        wrapper.orderByDesc(TenantPackageDO::getId);
        return selectPage(reqVO, wrapper);
    }

    default List<TenantPackageDO> selectListByStatus(Integer status) {
        return selectList(TenantPackageDO::getStatus, status);
    }
}
