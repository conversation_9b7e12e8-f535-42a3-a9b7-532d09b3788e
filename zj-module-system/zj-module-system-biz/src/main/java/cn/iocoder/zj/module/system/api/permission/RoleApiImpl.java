package cn.iocoder.zj.module.system.api.permission;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.system.service.permission.RoleService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Set;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class RoleApiImpl implements RoleApi {

    @Resource
    private RoleService roleService;

    @Override
    public CommonResult<Boolean> validRoleList(Collection<Long> ids) {
        roleService.validateRoleList(ids);
        return success(true);
    }

    @Override
    public Boolean hasAnySuperAdmin(Set<Long> ids) {

        return roleService.hasAnySuperAdmin(ids);
    }
    public Boolean hasAnyTenantAdmin(Set<Long> ids) {

        return roleService.hasAnyTenantAdmin(ids);
    }
    @Override
    public CommonResult<Set<Long>> getRoleIdByCode(String code) {
        return success(roleService.getRoleIdByCode(code));
    }

    @Override
    public Boolean hasAnyPlatformAdmin(Set<Long> ids) {
        return roleService.hasAnyPlatformAdmin(ids);
    }

    @Override
    public Boolean hasAnyPlatformMaintenance(Set<Long> ids) {
        return roleService.hasAnyPlatformisMaintenance(ids);
    }

    @Override
    public Boolean hasAnyOMManager(Set<Long> ids) {
        return roleService.hasAnyOMManager(ids);
    }

    @Override
    @TenantIgnore
    public Boolean decideAnySuperAdmin(Set<Long> ids) {
        return roleService.hasAnyTenantAdmin(ids);
    }

    @Override
    public Boolean getIsRootOperation(Long id) {
        return roleService.getIsRootOperation(id);
    }

    @Override
    public CommonResult<Set<Long>> getUserIdByCode(String code) {
        return success(roleService.getUserIdByCode(code));
    }
}
