package cn.iocoder.zj.module.system.controller.admin.devicetype.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 资产类型 Excel 导出 Request VO，参数和 DeviceTypePageReqVO 是一致的")
@Data
public class DeviceTypeExportReqVO {

    @Schema(description = "资产类型名称")
    private String sysDeviceName;

    @Schema(description = "资产类型type")
    private String sysDeviceType;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] createTime;

}
