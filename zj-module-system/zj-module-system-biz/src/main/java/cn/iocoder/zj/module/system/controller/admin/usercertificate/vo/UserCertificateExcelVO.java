package cn.iocoder.zj.module.system.controller.admin.usercertificate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 授权凭证 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class UserCertificateExcelVO {

    @ExcelProperty("主键")
    private Integer id;

    @ExcelProperty("所有者id")
    private Long userId;

    @ExcelProperty("名称")
    private String name;

    @ExcelProperty("所有者名称")
    private String ownerName;

    @ExcelProperty("授权账户")
    private String username;

    @ExcelProperty("密码")
    private String password;

    @ExcelProperty("私钥密码")
    private String passphrase;

    @ExcelProperty("私钥")
    private String privateKey;

    @ExcelProperty("账户类型")
    private String type;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
