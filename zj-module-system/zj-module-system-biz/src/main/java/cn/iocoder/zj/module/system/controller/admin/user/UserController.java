package cn.iocoder.zj.module.system.controller.admin.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.system.controller.admin.permission.vo.role.RoleSimpleRespVO;
import cn.iocoder.zj.module.system.controller.admin.user.vo.user.*;
import cn.iocoder.zj.module.system.convert.user.UserConvert;
import cn.iocoder.zj.module.system.dal.dataobject.dept.DeptDO;
import cn.iocoder.zj.module.system.dal.dataobject.permission.UserRoleDO;
import cn.iocoder.zj.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.zj.module.system.service.dept.DeptService;
import cn.iocoder.zj.module.system.service.permission.PermissionService;
import cn.iocoder.zj.module.system.service.user.AdminUserService;
import cn.iocoder.zj.module.system.enums.common.SexEnum;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.util.collection.MapUtils;
import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.*;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;
import static cn.iocoder.zj.module.infra.enums.ErrorCodeConstants.FILE_IS_EMPTY;

@Tag(name = "管理后台 - 用户管理")
@RestController
@RequestMapping("/system/user")
@Validated
public class UserController {

    @Resource
    private AdminUserService userService;
    @Resource
    private DeptService deptService;
    @Resource
    private PermissionService permissionService;

    @PostMapping("/create")
    @Operation(summary = "新增用户")
    @OperateLog(type=CREATE)
    @PreAuthorize("@ss.hasPermission('system:user:create')")
    public CommonResult<Long> createUser(@Valid @RequestBody UserCreateReqVO reqVO) {
        Long id = userService.createUser(reqVO);
        permissionService.assignUserRole(id,reqVO.getRoleIds());
        return success(id);
    }

    @PutMapping("update")
    @Operation(summary = "修改用户")
    @OperateLog(type=UPDATE)
    @PreAuthorize("@ss.hasPermission('system:user:update')")
    public CommonResult<Boolean> updateUser(@Valid @RequestBody UserUpdateReqVO reqVO) {
        userService.updateUser(reqVO);
        permissionService.assignUserRole(reqVO.getId(),reqVO.getRoleIds());
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除用户")
    @OperateLog(type=DELETE)
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:user:delete')")
    public CommonResult<Boolean> deleteUser(@RequestParam("id") Long id) {
        userService.deleteUser(id);
        return success(true);
    }

    @PutMapping("/update-password")
    @Operation(summary = "重置用户密码")
    @OperateLog(type=UPDATE)
    @PreAuthorize("@ss.hasPermission('system:user:update-password')")
    public CommonResult<Boolean> updateUserPassword(@Valid @RequestBody UserUpdatePasswordReqVO reqVO) {
        userService.updateUserPassword(reqVO.getId(), reqVO.getPassword());
        return success(true);
    }

    @PutMapping("/update-status")
    @Operation(summary = "修改用户状态")
    @OperateLog(type=UPDATE)
    @PreAuthorize("@ss.hasPermission('system:user:update')")
    public CommonResult<Boolean> updateUserStatus(@Valid @RequestBody UserUpdateStatusReqVO reqVO) {
        userService.updateUserStatus(reqVO.getId(), reqVO.getStatus());
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得用户分页列表")
    @PreAuthorize("@ss.hasPermission('system:user:list')")
    public CommonResult<PageResult<UserPageItemRespVO>> getUserPage(@Valid UserPageReqVO reqVO) {
        LoginUser currentUser = SecurityFrameworkUtils.getLoginUser();
        Long id = userService.getUserIdByTenantId(currentUser.getTenantId());
        // 获得用户分页列表
        PageResult<AdminUserDO> pageResult = userService.getUserPage(reqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(new PageResult<>(pageResult.getTotal())); // 返回空
        }
        //获取用户角色信息
        List<Long> userIds = pageResult.getList().stream().map(AdminUserDO::getId).collect(Collectors.toList());
        Map<Long, RoleSimpleRespVO> roleMap = userService.getRoleMapByUserIds(userIds);
        // 获得拼接需要的数据
        Collection<Long> deptIds = convertList(pageResult.getList(), AdminUserDO::getDeptId);
        Map<Long, DeptDO> deptMap = deptService.getDeptMap(deptIds);
        // 拼接结果返回
        List<UserPageItemRespVO> userList = new ArrayList<>(pageResult.getList().size());
        pageResult.getList().forEach(user -> {
            UserPageItemRespVO respVO = UserConvert.INSTANCE.convert(user);
            respVO.setDept(UserConvert.INSTANCE.convert(deptMap.get(user.getDeptId())));
            if (roleMap.get(user.getId()) != null) {
                respVO.setRoleName(roleMap.get(user.getId()).getName());
            } else {
                respVO.setRoleName("");
            }
            respVO.setIsTenantAdmin(ObjectUtil.equals(respVO.getId(),id)?1:0);
            userList.add(respVO);
        });
        return success(new PageResult<>(userList, pageResult.getTotal()));
    }

    @GetMapping("/list-all-simple")
    @Operation(summary = "获取用户精简信息列表", description = "只包含被开启的用户，主要用于前端的下拉选项")
    public CommonResult<List<UserSimpleRespVO>> getSimpleUsers() {
        // 获用户门列表，只要开启状态的
        List<AdminUserDO> list = userService.getUserListByStatus(CommonStatusEnum.ENABLE.getStatus());
        // 排序后，返回给前端
        return success(UserConvert.INSTANCE.convertList04(list));
    }

    @GetMapping("/getOperations")
    @Operation(summary = "获取运维人员信息列表")
    public CommonResult<List<UserSimpleRespVO>> getOperations() {
        LoginUser user = SecurityFrameworkUtils.getLoginUser();
        List<UserSimpleRespVO> list = userService.getOperations(user.getId(), "operation_maintenance");
        // 排序后，返回给前端
        return success(list);
    }

    @GetMapping("/getOrderEnforcer")
    @Operation(summary = "获取工单执行人员列表")
    public CommonResult<List<UserSimpleRespVO>> getOrderEnforcer(@RequestParam("platformId")Long platformId) {
        List<UserSimpleRespVO> list = userService.getOrderEnforcer(platformId);
        // 排序后，返回给前端
        return success(list);
    }

    @GetMapping("/getEnforcer")
    @Operation(summary = "获取实施人员信息列表")
    public CommonResult<List<UserSimpleRespVO>> getEnforcer() {
        AdminUserDO user = userService.getPlatformAdmin();
        List<UserSimpleRespVO> list = userService.getEnforcer(user.getId());
        // 排序后，返回给前端
        return success(list);
    }


    @GetMapping("/get")
    @Operation(summary = "获得用户详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @TenantIgnore
    public CommonResult<UserRespVO> getInfo(@RequestParam("id") Long id) {
        AdminUserDO user = userService.getUser(id);
        Set<Long> roleIds = permissionService.getUserRoleIdListByUserId(id);
        List<Long> userIds = new ArrayList<>();
        userIds.add(id);
        // 获得部门数据
        DeptDO dept = deptService.getDept(user.getDeptId());
        UserRespVO userRespVO = UserConvert.INSTANCE.convert(user).setDept(UserConvert.INSTANCE.convert(dept));
        userRespVO.setRoleIds(roleIds);
        Map<Long, RoleSimpleRespVO> roleMap = userService.getRoleMapByUserIds(userIds);
        String roleNames = roleMap.values().stream()
                .map(RoleSimpleRespVO::getName)
                .collect(Collectors.joining(","));
        userRespVO.setRoleNames(roleNames);
        return success(userRespVO);
    }

    @GetMapping("/export")
    @Operation(summary = "导出用户")
    @PreAuthorize("@ss.hasPermission('system:user:export')")
    @OperateLog(type = EXPORT)
    public void exportUsers(@Validated UserExportReqVO reqVO,
                            HttpServletResponse response) throws IOException {
        // 获得用户列表
        List<AdminUserDO> users = userService.getUserList(reqVO);
        List<UserExcelVO> excelUsers = new ArrayList<>(users.size());
        users.forEach(user -> {
            UserExcelVO excelVO = UserConvert.INSTANCE.convert02(user);
            excelUsers.add(excelVO);
        });
        // 输出
        ExcelUtils.write(response, "用户数据.xls", "用户列表", UserExcelVO.class, excelUsers);
    }

    @GetMapping("/get-import-template")
    @Operation(summary = "获得导入用户模板")
    public void importTemplate(HttpServletResponse response) throws IOException {
        // 手动创建导出 demo
        List<UserImportExcelVO> list = Arrays.asList(
                UserImportExcelVO.builder().email("<EMAIL>").mobile("15601691300")
                        .nickname("芋道").status(CommonStatusEnum.ENABLE.getStatus()).sex(SexEnum.MALE.getSex()).build(),
                UserImportExcelVO.builder().email("<EMAIL>").mobile("15601701300")
                        .nickname("源码").status(CommonStatusEnum.DISABLE.getStatus()).sex(SexEnum.FEMALE.getSex()).build()
        );

        // 输出
        ExcelUtils.write(response, "用户导入模板.xls", "用户列表", UserImportExcelVO.class, list);
    }

    @PostMapping("/import")
    @Operation(summary = "导入用户")
    @Parameters({
            @Parameter(name = "file", description = "Excel 文件", required = true),
            @Parameter(name = "updateSupport", description = "是否支持更新，默认为 false", example = "true")
    })
    @PreAuthorize("@ss.hasPermission('system:user:import')")
    public CommonResult<UserImportRespVO> importExcel(@RequestParam("file") MultipartFile file,
                                                      @RequestParam(value = "updateSupport", required = false, defaultValue = "false") Boolean updateSupport) throws Exception {
        List<UserImportExcelVO> list = ExcelUtils.read(file, UserImportExcelVO.class);
        return success(userService.importUserList(list, updateSupport));
    }

}
