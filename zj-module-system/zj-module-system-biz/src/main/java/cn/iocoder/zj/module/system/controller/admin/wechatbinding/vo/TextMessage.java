package cn.iocoder.zj.module.system.controller.admin.wechatbinding.vo;
 
import lombok.Data;
 
/**
 * TextMessage
 *
 * @Description:
 * @Author: bb
 * @Date: 2022-08-19
 * @Version: V1.0.0
 */
@Data
public class TextMessage {
    /**
     * 开发者微信号
     */
    private String ToUserName;
    /**
     * 发送方账号
     */
    private String FromUserName;
    /**
     * 创建时间
     */
    private Long CreateTime;
 
    /**
     * 消息类型
     */
    private String MsgType;
 
    /**
     * 消息文本
     */
    private String Content;
 
 
 
 
}