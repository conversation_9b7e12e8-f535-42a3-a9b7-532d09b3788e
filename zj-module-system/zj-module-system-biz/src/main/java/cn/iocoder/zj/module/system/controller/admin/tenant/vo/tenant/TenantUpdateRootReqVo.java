package cn.iocoder.zj.module.system.controller.admin.tenant.vo.tenant;

import cn.iocoder.zj.framework.common.validation.Mobile;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;

@Data
public class TenantUpdateRootReqVo {

    @Schema(description = "租户编号", required = true, example = "1024")
    @NotNull(message = "租户编号不能为空")
    private Long id;

    @Schema(description = "联系人", required = false, example = "芋艿")
    private String contactName;

    @Schema(description = "联系手机", example = "15601691300")
    @Mobile
    private String contactMobile;

    @Schema(description = "用户名称", required = true, example = "zj")
    private String username;

    @Schema(description = "密码", required = true, example = "123456")
    private String password;

    @Schema(description = "邮箱", example = "15601691300")
    @Email(message = "必须是 Email 格式")
    @Size(max = 50, message = "邮箱长度最多50个字符")
    private String contactEmail;


    @Schema(description = "公司")
    private String organization;

    @Schema(description = "部门")
    private String deptStr;



}
