package cn.iocoder.zj.module.system.service.platformconfig;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.system.controller.admin.platformconfig.vo.PlatformConfigCreateReqVO;
import cn.iocoder.zj.module.system.controller.admin.platformconfig.vo.PlatformConfigExportReqVO;
import cn.iocoder.zj.module.system.controller.admin.platformconfig.vo.PlatformConfigPageReqVO;
import cn.iocoder.zj.module.system.controller.admin.platformconfig.vo.PlatformConfigUpdateReqVO;
import cn.iocoder.zj.module.system.dal.dataobject.platformconfig.PlatformConfigDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 平台配置 Service 接口
 *
 * <AUTHOR>
 */
public interface PlatformConfigService {

    /**
     * 创建平台配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPlatformConfig(@Valid PlatformConfigCreateReqVO createReqVO);

    /**
     * 更新平台配置
     *
     * @param updateReqVO 更新信息
     */
    void updatePlatformConfig(@Valid PlatformConfigUpdateReqVO updateReqVO);

    /**
     * 删除平台配置
     *
     * @param id 编号
     */
    void deletePlatformConfig(Long id);

    /**
     * 获得平台配置
     *
     * @param id 编号
     * @return 平台配置
     */
    PlatformConfigDO getPlatformConfig(Long id);

    /**
     * 获得平台配置列表
     *
     * @param ids 编号
     * @return 平台配置列表
     */
    List<PlatformConfigDO> getPlatformConfigList(Collection<Long> ids);

    /**
     * 获得平台配置分页
     *
     * @param pageReqVO 分页查询
     * @return 平台配置分页
     */
    PageResult<PlatformConfigDO> getPlatformConfigPage(PlatformConfigPageReqVO pageReqVO);

    /**
     * 获得平台配置列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     *
     * @return 平台配置列表
     */
    List<PlatformConfigDO> getPlatformConfigList(PlatformConfigExportReqVO exportReqVO);

    List<PlatformConfigDO> getPlatList();

    List<Map> getPlatformSelectList(String tenantId);
    List<Map> getPlatformByTenantId(String tenantId);

    List<Map> getPlatformSelectListAlerts(Long userId);


    List<String> getByConfigMailList(Long platformId);

    List<String> getByTenantList(Long platformId);

    List<Map> getPlatformProject();

    List<Map> cloudPlatform();

    void detectMonitor(Integer id);

    List<Map> getPlatformListByUserId(String userId);

    List<Long> getTenantsByConfigId(Long platformId);

    List<Map> getPlatformCollector(List<String> idList);

    List<Map> getPlatformSelectByTenantIdList(String tenantId);

    List<PlatformConfigDO> getPlatformByIds(List<Long> platformIds);
}
