package cn.iocoder.zj.module.system.convert.deviceoid;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.system.controller.admin.deviceoid.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.deviceoid.DeviceOidDO;

/**
 * OID管理 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DeviceOidConvert {

    DeviceOidConvert INSTANCE = Mappers.getMapper(DeviceOidConvert.class);

    DeviceOidDO convert(DeviceOidCreateReqVO bean);

    DeviceOidDO convert(DeviceOidUpdateReqVO bean);

    DeviceOidRespVO convert(DeviceOidDO bean);

    List<DeviceOidRespVO> convertList(List<DeviceOidDO> list);

    PageResult<DeviceOidRespVO> convertPage(PageResult<DeviceOidDO> page);

    List<DeviceOidExcelVO> convertList02(List<DeviceOidDO> list);

}
