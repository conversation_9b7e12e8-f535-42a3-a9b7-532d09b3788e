package cn.iocoder.zj.module.system.dal.dataobject.screen;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;

@Data
public class ScreenConfigDO {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "大屏标题")
    private String title;

    @Schema(description = "logo")
    private String logo;

    @Schema(description = "云星辰系统找用户自定义名称")
    private String systemName;

    @Schema(description = "浏览器图标")
    private String favicon;

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private Long creator;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private Long updater;

    @Schema(description = "删除标识")
    private String deleted;
}
