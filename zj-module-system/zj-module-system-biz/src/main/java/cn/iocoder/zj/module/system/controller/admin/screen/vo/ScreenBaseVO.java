package cn.iocoder.zj.module.system.controller.admin.screen.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

/**
* 系统设置大屏 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ScreenBaseVO {

    @Schema(description = "首页模块")
    private String module;

    @Schema(description = "监控条目")
    private String monitorEntry;

    @Schema(description = "监控条目名称")
    private String monitorEntryName;

    @Schema(description = "资源类型")
    private String resourceType;

    @Schema(description = "资源类型名称")
    private String resourceTypeName;

    @Schema(description = "显示顺序")
    private Integer sort;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "地区id")
    private Long regionId;

    @Schema(description = "租户名称")
    private String tenantName;

    @Schema(description = "地区名称")
    private String regionName;

}
