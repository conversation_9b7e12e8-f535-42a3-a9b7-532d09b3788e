package cn.iocoder.zj.module.system.dal.dataobject.wechatbinding;

import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 微信公众号OpenId与租户绑定关系 DO
 *
 * <AUTHOR>
 */
@TableName("system_wechat_binding")
@KeySequence("system_wechat_binding_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WechatBindingDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 租户编号
     */
    private Long userId;
    /**
     * 微信公众号唯一标识
     */
    private String openId;
    /**
     * 邮箱地址
     */
    private String email;
    /**
     * 是否启用微信公众号
     */
    private Boolean isWechat;
    /**
     * 是否启用微信邮箱
     */
    private Boolean isEmail;

}
