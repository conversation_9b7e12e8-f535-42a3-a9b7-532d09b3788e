package cn.iocoder.zj.module.system.controller.admin.devicetype.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;
import javax.validation.constraints.*;

/**
 * 资产类型 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class DeviceTypeBaseVO {

    @Schema(description = "资产类型名称")
    @Size(max = 50, message = "资产类型名称最大长度为50个字符")
    private String sysDeviceName;

    @Schema(description = "资产类型type")
    @Size(max = 50, message = "资产类型最大长度为50个字符")
    private String sysDeviceType;

    /**
     * @description: 图标
     * <AUTHOR>
     * @date 2023/8/14 19:37
     * @version 1.0
     */
    @Schema(description = "图标")
    private String icon;

    @Schema(description = "1：单选 2：多选")
    private Integer checkbox;
    @Schema(description = "1: 横向 2：纵向")
    private Integer direction;
    @Schema(description = "排序")
    private Integer sort;

}
