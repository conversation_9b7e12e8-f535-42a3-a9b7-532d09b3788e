package cn.iocoder.zj.module.system.controller.admin.usercertificate.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 授权凭证 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class UserCertificateBaseVO {

    @Schema(description = "所有者id")
    private Long userId;

    @Schema(description = "名称")
    @Size(max = 30, message = "名称长度不能超过30个字符")
    private String name;

    @Schema(description = "所有者名称")
    private String ownerName;

    @Schema(description = "授权账户")
    private String username;


    @Schema(description = "密码")
    private String password;

    @Schema(description = "私钥密码")
    private String passphrase;

    @Schema(description = "私钥")
    private String privateKey;

    @Schema(description = "账户类型")
    private String type;
}
