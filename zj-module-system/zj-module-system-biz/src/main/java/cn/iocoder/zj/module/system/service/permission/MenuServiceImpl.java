package cn.iocoder.zj.module.system.service.permission;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.module.system.controller.admin.permission.vo.menu.MenuCreateReqVO;
import cn.iocoder.zj.module.system.controller.admin.permission.vo.menu.MenuListReqVO;
import cn.iocoder.zj.module.system.controller.admin.permission.vo.menu.MenuUpdateReqVO;
import cn.iocoder.zj.module.system.convert.permission.MenuConvert;
import cn.iocoder.zj.module.system.dal.dataobject.permission.MenuDO;
import cn.iocoder.zj.module.system.dal.mysql.permission.MenuMapper;
import cn.iocoder.zj.module.system.dal.redis.RedisKeyConstants;
import cn.iocoder.zj.module.system.enums.permission.MenuTypeEnum;
import cn.iocoder.zj.module.system.service.tenant.TenantService;
import com.google.common.annotations.VisibleForTesting;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.convertList;
import static cn.iocoder.zj.module.system.dal.dataobject.permission.MenuDO.ID_ROOT;
import static cn.iocoder.zj.module.system.enums.ErrorCodeConstants.*;

/**
 * 菜单 Service 实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MenuServiceImpl implements MenuService {

    @Resource
    private MenuMapper menuMapper;
    @Resource
    private PermissionService permissionService;
    @Resource
    @Lazy // 延迟，避免循环依赖报错
    private TenantService tenantService;


    @Override
    @CacheEvict(value = RedisKeyConstants.PERMISSION_MENU_ID_LIST, key = "#reqVO.permission",
            condition = "#reqVO.permission != null")
    public Long createMenu(MenuCreateReqVO reqVO) {
        // 校验父菜单存在
        validateParentMenu(reqVO.getParentId(), null);
        // 校验菜单（自己）
        validateMenu(reqVO.getParentId(), reqVO.getName(), null);

        // 插入数据库
        MenuDO menu = MenuConvert.INSTANCE.convert(reqVO);
        initMenuProperty(menu);
        menuMapper.insert(menu);
        // 返回
        return menu.getId();
    }

    @Override
    @CacheEvict(value = RedisKeyConstants.PERMISSION_MENU_ID_LIST,
            allEntries = true)
    public void updateMenu(MenuUpdateReqVO reqVO) {
        // 校验更新的菜单是否存在
        if (menuMapper.selectById(reqVO.getId()) == null) {
            throw exception(MENU_NOT_EXISTS);
        }
        // 校验父菜单存在
        validateParentMenu(reqVO.getParentId(), reqVO.getId());
        // 校验菜单（自己）
        validateMenu(reqVO.getParentId(), reqVO.getName(), reqVO.getId());

        List<MenuDO> menuDOS = menuMapper.getMenuListByParentId(reqVO.getId());
        // 查询所有菜单数据
        List<MenuDO> menuAll = getMenuList();
        List<MenuDO> childs = menuAll.stream()
                .filter(menu -> Objects.equals(menu.getParentId(), reqVO.getId()))
                .collect(Collectors.toList());
        List<MenuDO> allChild = new ArrayList<>(childs);
        for (MenuDO child:childs) {
            List<MenuDO> subChild = menuAll.stream()
                    .filter(menu -> Objects.equals(menu.getParentId(), child.getId()))
                    .collect(Collectors.toList());
            if(subChild.size()>0){
                allChild.addAll(subChild);
            }
        }
        //去重
        // 如果菜单是目录或菜单并且状态为关闭，则关闭该目录下所有数据
        if ((2 == reqVO.getType()||1 == reqVO.getType()) && 1 == reqVO.getStatus()) {
//            List<MenuDO> children = updateMenuAndChildrenStatus(menuDOS, menuAll, reqVO.getStatus());
            allChild.forEach(item-> item.setStatus(reqVO.getStatus()));
            MenuDO updateObject = MenuConvert.INSTANCE.convert(reqVO);
            menuDOS.add(updateObject);
            menuDOS.addAll(allChild);
            menuDOS= menuDOS.stream()
                    .distinct()
                    .collect(Collectors.toList());
            menuMapper.updateBatch(menuDOS);
            // 如果菜单是目录或菜单并且状态选择了开启，则开启该目录下所有状态
        } else if ((2 == reqVO.getType()||1 == reqVO.getType()) && 0 == reqVO.getStatus()) {
            allChild.forEach(item-> item.setStatus(reqVO.getStatus()));
            MenuDO updateObject = MenuConvert.INSTANCE.convert(reqVO);
            menuDOS.add(updateObject);
            menuDOS.addAll(allChild);
            menuDOS= menuDOS.stream()
                    .distinct()
                    .collect(Collectors.toList());
//            findChildren(reqVO.getId(),menuAll);
            menuMapper.updateBatch(menuDOS);
        } else {
            // 更新到数据库
            MenuDO updateObject = MenuConvert.INSTANCE.convert(reqVO);
            initMenuProperty(updateObject);
            menuMapper.updateById(updateObject);
        }
    }


    public List<MenuDO> updateMenuAndChildrenStatus(List<MenuDO> menuDOS, List<MenuDO> menuAll, int status) {
        List<MenuDO> children = new ArrayList<>();
        for (MenuDO menu : menuDOS) {
            // 更新当前菜单的状态
            menu.setStatus(status);

            // 递归处理子菜单
            children = findChildren(menu.getId(), menuAll);
            if (!children.isEmpty()) {
                updateMenuAndChildrenStatus(children, menuAll, status);
            }
        }
        return children;
    }

    // 辅助函数，查找指定父菜单ID的子菜单
    private List<MenuDO> findChildren(Long parentId, List<MenuDO> menuList) {
        List<MenuDO> children = new ArrayList<>();
        for (MenuDO menu : menuList) {
            if (menu.getParentId().equals(parentId)) {
                children.add(menu);
            }
        }
        return children;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = RedisKeyConstants.PERMISSION_MENU_ID_LIST,
            allEntries = true)
    public void deleteMenu(Long id) {
        // 校验是否还有子菜单
        if (menuMapper.selectCountByParentId(id) > 0) {
            throw exception(MENU_EXISTS_CHILDREN);
        }
        // 校验删除的菜单是否存在
        if (menuMapper.selectById(id) == null) {
            throw exception(MENU_NOT_EXISTS);
        }
        // 标记删除
        menuMapper.deleteById(id);
        // 删除授予给角色的权限
        permissionService.processMenuDeleted(id);
    }

    @Override
    public List<MenuDO> getMenuList() {
        LambdaQueryWrapperX<MenuDO> wrapperX = new LambdaQueryWrapperX<MenuDO>();
        wrapperX.eq(MenuDO::getStatus,0)
                .eq(MenuDO::getDeleted,0);
        return menuMapper.selectList(wrapperX);
    }

    @Override
    public List<MenuDO> getMenuListByTenant(MenuListReqVO reqVO) {
        List<MenuDO> menus = getMenuList(reqVO);
        // 开启多租户的情况下，需要过滤掉未开通的菜单
        tenantService.handleTenantMenu(menuIds -> menus.removeIf(menu -> !CollUtil.contains(menuIds, menu.getId())));
        return menus;
    }

    @Override
    public List<MenuDO> getMenuList(MenuListReqVO reqVO) {
        return menuMapper.selectList(reqVO);
    }


    @Override
    @Cacheable(value = RedisKeyConstants.PERMISSION_MENU_ID_LIST, key = "#permission")
    public List<Long> getMenuIdListByPermissionFromCache(String permission) {
        List<MenuDO> menus = menuMapper.selectListByPermission(permission);
        return convertList(menus, MenuDO::getId);
    }

    @Override
    public MenuDO getMenu(Long id) {
        return menuMapper.selectById(id);
    }

    @Override
    public List<MenuDO> getMenuList(Collection<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return menuMapper.selectBatchIds(ids);
    }

    /**
     * 校验父菜单是否合法
     * <p>
     * 1. 不能设置自己为父菜单
     * 2. 父菜单不存在
     * 3. 父菜单必须是 {@link MenuTypeEnum#MENU} 菜单类型
     *
     * @param parentId 父菜单编号
     * @param childId  当前菜单编号
     */
    @VisibleForTesting
    void validateParentMenu(Long parentId, Long childId) {
        if (parentId == null || ID_ROOT.equals(parentId)) {
            return;
        }
        // 不能设置自己为父菜单
        if (parentId.equals(childId)) {
            throw exception(MENU_PARENT_ERROR);
        }
        MenuDO menu = menuMapper.selectById(parentId);
        // 父菜单不存在
        if (menu == null) {
            throw exception(MENU_PARENT_NOT_EXISTS);
        }
        // 父菜单必须是目录或者菜单类型
        if (!MenuTypeEnum.DIR.getType().equals(menu.getType())
                && !MenuTypeEnum.MENU.getType().equals(menu.getType())) {
            throw exception(MENU_PARENT_NOT_DIR_OR_MENU);
        }
    }

    /**
     * 校验菜单是否合法
     * <p>
     * 1. 校验相同父菜单编号下，是否存在相同的菜单名
     *
     * @param name     菜单名字
     * @param parentId 父菜单编号
     * @param id       菜单编号
     */
    @VisibleForTesting
    void validateMenu(Long parentId, String name, Long id) {
        MenuDO menu = menuMapper.selectByParentIdAndName(parentId, name);
        if (menu == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的菜单
        if (id == null) {
            throw exception(MENU_NAME_DUPLICATE);
        }
        if (!menu.getId().equals(id)) {
            throw exception(MENU_NAME_DUPLICATE);
        }
    }

    /**
     * 初始化菜单的通用属性。
     * <p>
     * 例如说，只有目录或者菜单类型的菜单，才设置 icon
     *
     * @param menu 菜单
     */
    private void initMenuProperty(MenuDO menu) {
        // 菜单为按钮类型时，无需 component、icon、path 属性，进行置空
        if (MenuTypeEnum.BUTTON.getType().equals(menu.getType())) {
            menu.setComponent("");
            menu.setComponentName("");
            menu.setIcon("");
            menu.setPath("");
        }
    }

}
