package cn.iocoder.zj.module.system.convert.metadata;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.system.controller.admin.metadata.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.metadata.MetadataDO;

/**
 * 元数据 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface MetadataConvert {

    MetadataConvert INSTANCE = Mappers.getMapper(MetadataConvert.class);

    MetadataDO convert(MetadataCreateReqVO bean);

    MetadataDO convert(MetadataUpdateReqVO bean);

    MetadataRespVO convert(MetadataDO bean);

    List<MetadataRespVO> convertList(List<MetadataDO> list);

    PageResult<MetadataRespVO> convertPage(PageResult<MetadataDO> page);

    List<MetadataExcelVO> convertList02(List<MetadataDO> list);

}
