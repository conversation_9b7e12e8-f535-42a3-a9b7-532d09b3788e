package cn.iocoder.zj.module.system.api.dict;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.util.collection.CollectionUtils;
import cn.iocoder.zj.framework.common.util.object.BeanUtils;
import cn.iocoder.zj.module.system.api.dict.dto.DictDataRespDTO;
import cn.iocoder.zj.module.system.convert.dict.DictDataConvert;
import cn.iocoder.zj.module.system.dal.dataobject.dict.DictDataDO;
import cn.iocoder.zj.module.system.service.dict.DictDataService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class DictDataApiImpl implements DictDataApi {

    @Resource
    private DictDataService dictDataService;

    @Override
    public CommonResult<Boolean> validateDictDataList(String dictType, Collection<String> values) {
        dictDataService.validateDictDataList(dictType, values);
        return success(true);
    }

    @Override
    public CommonResult<DictDataRespDTO> getDictData(String dictType, String value) {
        DictDataDO dictData = dictDataService.getDictData(dictType, value);
        return success(BeanUtils.toBean(dictData, DictDataRespDTO.class));
    }

    @Override
    public CommonResult<DictDataRespDTO> parseDictData(String dictType, String label) {
        DictDataDO dictData = dictDataService.parseDictData(dictType, label);
        return success(BeanUtils.toBean(dictData, DictDataRespDTO.class));
    }

    @Override
    public CommonResult<List<String>> getDictDataListByType(String dictType) {
        List<DictDataDO> dictData = dictDataService.getOpenDictDataList(dictType);
        List<String> values = dictData.stream().map(DictDataDO::getValue).collect(Collectors.toList());

        return success(values);
    }

    @Override
    public CommonResult<List<DictDataRespDTO>> getDictDataInfoByType(String dictType) {
        List<DictDataDO> dictData = dictDataService.getOpenDictDataList(dictType);
        return success(CollectionUtils.convertList(dictData, s -> BeanUtils.toBean(s, DictDataRespDTO.class)));
    }

}
