package cn.iocoder.zj.module.system.controller.admin.tenant.vo.tenant;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 租户基本信息修改")
@Data
@ToString(callSuper = true)
public class TenantUpdateBasicReqVo{

    @Schema(description = "租户编号", required = true, example = "1024")
    @NotNull(message = "租户编号不能为空")
    private Long id;

    @Schema(description = "项目名", required = true, example = "芋道")
    @NotNull(message = "项目名不能为空")
    @Size(min = 2, max = 30, message = "项目名长度为 2-30个字符")
    private String name;

    @Schema(description = "租户套餐编号", required = true, example = "1024")
    @NotNull(message = "租户套餐编号不能为空")
    private Long packageId;

    @Schema(description = "是否测试账号（0是 1否）", required = true, example = "1024")
    //@NotNull(message = "是否测试账号不能为空")
    private Integer isTest;

    @Schema(description = "账号数量", required = true, example = "1024")
    @NotNull(message = "账号数量不能为空")
    private Integer accountCount;

    @Schema(description = "过期时间", required = true)
    @NotNull(message = "过期时间不能为空")
    private LocalDateTime expireTime;

    @Schema(description = "租户状态", required = true, example = "1")
    @NotNull(message = "租户状态不能为空")
    private Integer status;

    @Schema(description = "租户状态", required = true, example = "1")
    private Integer state;
}
