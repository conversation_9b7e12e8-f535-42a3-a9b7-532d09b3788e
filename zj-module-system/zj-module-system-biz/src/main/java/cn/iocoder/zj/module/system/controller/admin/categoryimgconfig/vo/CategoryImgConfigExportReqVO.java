package cn.iocoder.zj.module.system.controller.admin.categoryimgconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 类别图片管理 Excel 导出 Request VO，参数和 CategoryImgConfigPageReqVO 是一致的")
@Data
public class CategoryImgConfigExportReqVO {

    @Schema(description = "大类或者平台")
    private String category;

    @Schema(description = "小类")
    private String app;

    @Schema(description = "类型：1：资产  2：平台")
    private Integer type;

    @Schema(description = "icon")
    private String icon;

    @Schema(description = "详情icon")
    private String detailIcon;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
