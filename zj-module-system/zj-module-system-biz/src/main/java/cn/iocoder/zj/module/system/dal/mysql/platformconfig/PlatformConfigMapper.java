package cn.iocoder.zj.module.system.dal.mysql.platformconfig;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.system.controller.admin.platformconfig.vo.PlatformConfigExportReqVO;
import cn.iocoder.zj.module.system.controller.admin.platformconfig.vo.PlatformConfigPageReqVO;
import cn.iocoder.zj.module.system.dal.dataobject.platformconfig.PlatformConfigDO;
import cn.iocoder.zj.module.system.dal.dataobject.user.AdminUserDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import jodd.util.StringUtil;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 平台配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PlatformConfigMapper extends BaseMapperX<PlatformConfigDO> {

    default PageResult<PlatformConfigDO> selectPage(PlatformConfigPageReqVO reqVO) {
        LambdaQueryWrapperX<PlatformConfigDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.likeIfPresent(PlatformConfigDO::getUsername, reqVO.getUsername())
                .eqIfPresent(PlatformConfigDO::getPassword, reqVO.getPassword())
                .eqIfPresent(PlatformConfigDO::getUrl, reqVO.getUrl())
                .eqIfPresent(PlatformConfigDO::getState, reqVO.getState())
                .inIfPresent(PlatformConfigDO::getId, reqVO.getPlatformIds())
                .likeIfPresent(PlatformConfigDO::getName, reqVO.getName())
                .likeIfPresent(PlatformConfigDO::getTypeCode, reqVO.getTypeCode());
        if (StringUtil.isNotEmpty(reqVO.getStartTime()) && StringUtil.isNotEmpty(reqVO.getEndTime())) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            try {
                date = sdf.parse(reqVO.getEndTime());
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(date);
                // 把日期设置为当天最后一秒
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                date = calendar.getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            wrapper.betweenIfPresent(PlatformConfigDO::getCreateTime, reqVO.getStartTime(), sdf.format(date));
        }
        // 设置默认值
        String sortBy = StringUtil.isNotEmpty(reqVO.getSortBy()) ? reqVO.getSortBy() : "createTime";
        boolean isAsc = "asc".equalsIgnoreCase(reqVO.getSortDirection());
        // 根据字段名进行排序
        switch (sortBy) {
            case "createTime":
                wrapper.orderBy(true, isAsc, PlatformConfigDO::getCreateTime);
                break;
            case "onlineTime":
                wrapper.orderBy(true, isAsc, PlatformConfigDO::getOnlineTime);
                break;
            case "diffTime":
                wrapper.orderBy(true, isAsc, PlatformConfigDO::getDiffTime);
                break;
            default:
                // 不支持的字段，使用默认排序
                wrapper.orderByDesc(PlatformConfigDO::getCreateTime);
                break;
        }

        return selectPage(reqVO, wrapper);
    }

    default List<PlatformConfigDO> selectList(PlatformConfigExportReqVO reqVO) {
        LambdaQueryWrapperX<PlatformConfigDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.likeIfPresent(PlatformConfigDO::getUsername, reqVO.getUsername())
                .eqIfPresent(PlatformConfigDO::getPassword, reqVO.getPassword())
                .eqIfPresent(PlatformConfigDO::getUrl, reqVO.getUrl())
                .likeIfPresent(PlatformConfigDO::getName, reqVO.getName());
        if (StringUtil.isNotEmpty(reqVO.getStartTime()) && StringUtil.isNotEmpty(reqVO.getEndTime())) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            try {
                date = sdf.parse(reqVO.getEndTime());
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(date);
                // 把日期设置为当天最后一秒
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                date = calendar.getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            wrapper.betweenIfPresent(PlatformConfigDO::getCreateTime, reqVO.getStartTime(), sdf.format(date));
        }
        wrapper.orderByDesc(PlatformConfigDO::getId);
        return selectList(wrapper);
    }

    default PlatformConfigDO getByBaseInfo(PlatformConfigPageReqVO platformConfigVO) {
        LambdaQueryWrapperX<PlatformConfigDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(PlatformConfigDO::getUsername, platformConfigVO.getUsername())
                .or().eq(PlatformConfigDO::getUrl, platformConfigVO.getUrl());
        return selectOne(wrapper);
    }

    ;

    List<PlatformConfigDO> selectListByDto();


    @TenantIgnore
    List<Map> getPlatformSelectList(@Param("tenantId") List<String> tenantId);

    @TenantIgnore
    List<Map> getPlatformProject();

    @TenantIgnore
    List<Map> cloudPlatform();

    @TenantIgnore
    void deleteByTenantId(@Param("id") Long id);

    @TenantIgnore
    List<Long> getTenantsByConfigId(@Param("id") Long platformId);

    @TenantIgnore
    List<Map> getLicense(@Param("platformId") Long platformId);

    @TenantIgnore
    Map getProdMapping(@Param("licenseId") String licenseId, @Param("serverPort") String serverPort);

    @TenantIgnore
    List<Map> portMappingList(@Param("licenseId") String licenseId);

    @TenantIgnore
    List<Map> getPlatformCollector(@Param("idList") List<String> idList);

    PlatformConfigDO getByBaseInfoVo(@Param("url") String url, @Param("tenantId") Long tenantId);

    @TenantIgnore
    void deleteCollectorPlatform(@Param("id") Long id);

    @TenantIgnore
    @DS("slave")
    void updateAlert(@Param("id") Long id, @Param("name") String name);

    List<Long> getPlatFormListByCollectorId(@Param("collectorId") Long collectorId);

    @TenantIgnore
    String getCollectorNameByPlatformId(@Param("id") Long id);

    @TenantIgnore
    AdminUserDO getAdminUserId(@Param("usrId") Long uerId);

    @TenantIgnore
    void updateTop(@Param("id") Long id, @Param("name") String name);
}