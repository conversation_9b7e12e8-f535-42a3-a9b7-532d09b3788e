package cn.iocoder.zj.module.system.controller.admin.categoryimgconfig.vo;

import cn.iocoder.zj.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 类别图片管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CategoryImgConfigPageReqVO extends PageParam {

    @Schema(description = "大类或者平台")
    private String category;

    @Schema(description = "小类")
    private String app;

    @Schema(description = "类型：1：资产  2：平台")
    private Integer type;

    @Schema(description = "icon")
    private String icon;

    @Schema(description = "详情icon")
    private String detailIcon;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
