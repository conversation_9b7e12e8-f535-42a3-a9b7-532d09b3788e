package cn.iocoder.zj.module.system.service.deviceoid;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.iocoder.zj.module.monitor.api.asset.GatherAssetApi;
import cn.iocoder.zj.module.system.api.deviceoid.dto.DeviceOidDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.util.*;

import cn.iocoder.zj.module.system.controller.admin.deviceoid.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.deviceoid.DeviceOidDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.system.convert.deviceoid.DeviceOidConvert;
import cn.iocoder.zj.module.system.dal.mysql.deviceoid.DeviceOidMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.system.enums.ErrorCodeConstants.*;

/**
 * OID管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DeviceOidServiceImpl implements DeviceOidService {

    @Resource
    private DeviceOidMapper deviceOidMapper;

    @Resource
    private GatherAssetApi gatherAssetApi;

    @Override
    public Long createDeviceOid(DeviceOidCreateReqVO createReqVO) {
        // 插入
        DeviceOidDO deviceOid = DeviceOidConvert.INSTANCE.convert(createReqVO);
        deviceOidMapper.insert(deviceOid);
        // 返回
        return deviceOid.getId();
    }

    @Override
    public void updateDeviceOid(DeviceOidUpdateReqVO updateReqVO) {
        // 校验存在
        validateDeviceOidExists(updateReqVO.getId());
        // 更新
        DeviceOidDO updateObj = DeviceOidConvert.INSTANCE.convert(updateReqVO);
        deviceOidMapper.updateById(updateObj);
    }

    @Override
    public void deleteDeviceOid(Long id) {
        // 校验存在
        validateDeviceOidExists(id);
        // 校验正在使用
        validateDeviceOidUsed(id);
        // 删除
        deviceOidMapper.deleteById(id);
    }

    private void validateDeviceOidExists(Long id) {
        if (deviceOidMapper.selectById(id) == null) {
            throw exception(DEVICE_OID_NOT_EXISTS);
        }
    }

    private void validateDeviceOidUsed(Long id) {
        String deviceType=deviceOidMapper.selectById(id).getDeviceType();
        if (gatherAssetApi.getGatherAssetCount(deviceType) > 0) {
            throw exception(DEVICE_OID_USED);
        }
    }


    @Override
    public DeviceOidDO getDeviceOid(Long id) {
        return deviceOidMapper.selectById(id);
    }

    @Override
    public List<DeviceOidDO> getDeviceOidList(Collection<Long> ids) {
        return deviceOidMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<DeviceOidDO> getDeviceOidPage(DeviceOidPageReqVO pageReqVO) {
        return deviceOidMapper.selectPage(pageReqVO);
    }

    @Override
    public List<DeviceOidDO> getDeviceOidList(DeviceOidExportReqVO exportReqVO) {
        return deviceOidMapper.selectList(exportReqVO);
    }

    @Override
    public Map getDeviceOidListByDevice(String deviceType, String token_desc) {
        Map map = new HashMap();
        if (!SecureUtil.md5("<EMAIL>").equals(token_desc)) {
            map.put("code", 500);
            map.put("msg", "token验证失败");
            return map;
        }

        if (StrUtil.isNotEmpty(deviceType)) {
            JSONObject obj = JSON.parseObject(deviceType);
            String deviceTypes = obj.getString("deviceType");
            List<DeviceOidDTO> deviceOidDOS = deviceOidMapper.getDeviceOidListByDevice(deviceTypes);
            if (deviceOidDOS.size() > 0) {
                map.put("code", 200);
                map.put("data", deviceOidDOS);
                map.put("msg", "请求成功");
            } else {
                map.put("code", 200);
                map.put("data", new ArrayList<>());
                map.put("msg", "请求成功");
            }
        } else {
            map.put("code", 500);
            map.put("data", new ArrayList<>());
            map.put("msg", "deviceType不能为空！");
        }

        return map;
    }

    @Override
    public Long getCountByDeviceSysType(String sysType) {
        return deviceOidMapper.getCountByDeviceSysType(sysType);
    }

    @Override
    public Long getCountByDeviceType(String sysDeviceType) {
        return deviceOidMapper.getCountByDeviceType(sysDeviceType);
    }

}
