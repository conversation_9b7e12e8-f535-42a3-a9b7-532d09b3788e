package cn.iocoder.zj.module.system.api.licence;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.system.service.licence.LicenceService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

/**
 * @ClassName : LicenceApiImpl  //类名
 * @Description : 授权api实现  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/2/1  9:56
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class LicenceApiImpl implements LicenceApi {
    @Resource
    LicenceService licenceService;

    @Override
    public CommonResult<Map> selectLicence(Long tenantId) {
        return CommonResult.success(licenceService.selectLicenByState(tenantId));
    }
}
