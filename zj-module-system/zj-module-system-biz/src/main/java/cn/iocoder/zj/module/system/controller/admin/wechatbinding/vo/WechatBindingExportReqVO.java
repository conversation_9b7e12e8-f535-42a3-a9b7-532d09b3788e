package cn.iocoder.zj.module.system.controller.admin.wechatbinding.vo;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 微信公众号OpenId与租户绑定关系 Excel 导出 Request VO，参数和 WechatBindingPageReqVO 是一致的")
@Data
public class WechatBindingExportReqVO {


    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "微信公众号唯一标识")
    private String openId;

    @Schema(description = "邮箱地址")
    private String email;

    @Schema(description = "是否启用微信公众号")
    private Boolean isWechat;

    @Schema(description = "是否启用微信邮箱")
    private Boolean isEmail;

    @Schema(description = "创建时间")
    @DateTimeFormat(FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] createTime;

}
