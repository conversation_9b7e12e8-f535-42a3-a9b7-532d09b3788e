package cn.iocoder.zj.module.system.service.devicetype;

import cn.hutool.core.io.IoUtil;
import cn.iocoder.zj.framework.common.constants.FileTypeConstants;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.util.string.StringUtil;
import cn.iocoder.zj.module.infra.api.file.FileApi;
import cn.iocoder.zj.module.monitor.api.asset.GatherAssetApi;
import cn.iocoder.zj.module.system.controller.admin.devicetype.vo.DeviceTypeCreateReqVO;
import cn.iocoder.zj.module.system.controller.admin.devicetype.vo.DeviceTypeExportReqVO;
import cn.iocoder.zj.module.system.controller.admin.devicetype.vo.DeviceTypePageReqVO;
import cn.iocoder.zj.module.system.controller.admin.devicetype.vo.DeviceTypeUpdateReqVO;
import cn.iocoder.zj.module.system.convert.devicetype.DeviceTypeConvert;
import cn.iocoder.zj.module.system.dal.dataobject.devicetype.DeviceTypeDO;
import cn.iocoder.zj.module.system.dal.mysql.devicetype.DeviceTypeMapper;
import cn.iocoder.zj.module.system.service.deviceoid.DeviceOidService;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.system.enums.ErrorCodeConstants.*;

/**
 * 资产类型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DeviceTypeServiceImpl implements DeviceTypeService {

    @Resource
    private DeviceTypeMapper deviceTypeMapper;
    @Resource
    private FileApi fileApi;
    @Resource
    private DeviceOidService deviceOidService;
    @Resource
    private GatherAssetApi gatherAssetApi;

    @Override
    public Long createDeviceType(DeviceTypeCreateReqVO createReqVO) {
//        validateDeviceTypeByType(createReqVO.getSysDeviceType());
        // 插入
        DeviceTypeDO deviceType = DeviceTypeConvert.INSTANCE.convert(createReqVO);
        deviceTypeMapper.insert(deviceType);
        // 返回
        return deviceType.getId();
    }

    @Override
    public void updateDeviceType(DeviceTypeUpdateReqVO updateReqVO) {
        // 校验存在
        validateDeviceTypeExists(updateReqVO.getId());
        // 更新
        DeviceTypeDO updateObj = DeviceTypeConvert.INSTANCE.convert(updateReqVO);
        deviceTypeMapper.updateById(updateObj);
    }

    @Override
    public void deleteDeviceType(Long id) {
        deviceTypeMapper.deleteById(id);
    }

    private void validateDeviceTypeExists(Long id) {
        if (deviceTypeMapper.selectById(id) == null) {
            throw exception(DEVICE_TYPE_NOT_EXISTS);
        }
    }
    private void validateDeviceTypeByType(String type) {
        if (deviceTypeMapper.selectOne("sys_device_type",type,"deleted",0)!=null) {
            throw exception(DEVICE_TYPE_EXISTS);
        }
    }
    private void validateDeviceTypeUsed(Long id) {
        String sysDeviceType=deviceTypeMapper.selectById(id).getSysDeviceType();
        if (deviceOidService.getCountByDeviceType(sysDeviceType) > 0) {
            throw exception(DEVICE_TYPE_USED);
        }
        if (gatherAssetApi.getGatherAssetCount(sysDeviceType) > 0) {
            throw exception(DEVICE_TYPE_USED_GATHER);
        }
    }

    @Override
    public DeviceTypeDO getDeviceType(Long id) {
        return deviceTypeMapper.selectById(id);
    }

    @Override
    public List<DeviceTypeDO> getDeviceTypeList(Collection<Long> ids) {
        return deviceTypeMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<DeviceTypeDO> getDeviceTypePage(DeviceTypePageReqVO pageReqVO) {
        return deviceTypeMapper.selectPage(pageReqVO);
    }

    @Override
    public List<DeviceTypeDO> getDeviceTypeList(DeviceTypeExportReqVO exportReqVO) {
        return deviceTypeMapper.selectList(exportReqVO);
    }

    @Override
    public List<DeviceTypeDO> getDeviceSelect() {
        return deviceTypeMapper.selectList();
    }

    @Override
    @SneakyThrows
    public String updateDeviceIcon(MultipartFile file) {
        // 存储文件
        String path = StringUtil.getSavePath(file.getOriginalFilename(), FileTypeConstants.IMG_TYPE,null);
        return  fileApi.createFileUrl(file.getOriginalFilename(), path, IoUtil.readBytes(file.getInputStream()));
    }

    @Override
    public List<DeviceTypeDO> getDeviceTypeByList() {
        return deviceTypeMapper.selectListBySort();
    }

}
