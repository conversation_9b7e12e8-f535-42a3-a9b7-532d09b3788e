package cn.iocoder.zj.module.system.controller.admin.wechatbinding;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.system.controller.admin.wechatbinding.vo.WechatBindingCreateReqVO;
import cn.iocoder.zj.module.system.dal.dataobject.wechatbinding.WechatBindingDO;
import cn.iocoder.zj.module.system.service.wechatbinding.WechatBindingService;
import cn.iocoder.zj.module.system.util.wechat.CheckoutUtil;
import com.alibaba.nacos.common.utils.StringUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.awt.image.BufferedImage;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.MessageDigest;
import java.util.Arrays;
import java.util.Base64;
import java.util.Locale;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 微信公众号OpenId与租户绑定关系")
@RestController
@RequestMapping("/system/wechat")
@Validated
@Slf4j
public class WechatBindingController {

    @Resource
    private WechatBindingService wechatBindingService;

    /**
     * 微信服务器 对 微信公众号管理平台的 服务器地址(URL) 进行校验
     *
     * @param signature
     * @param timestamp
     * @param nonce
     * @param echostr
     * @return
     */
    @PermitAll
    @GetMapping("/checkSign")
    @Operation(summary = "微信服务器校验")
    public String firstWxVerify(@RequestParam(value = "signature", required = false) String signature,
                                @RequestParam(value = "timestamp", required = false) String timestamp,
                                @RequestParam(value = "nonce", required = false) String nonce,
                                @RequestParam(value = "echostr", required = false) String echostr) {
        System.out.println(" 微信服务端返回的数据 用于校验 微信配置中心的网址是否正确: >>>" + signature + "\t" + timestamp + "\t" + nonce + "\t" + echostr);
        log.info("开始签名验证：" + " PARAM VAL: >>>" + signature + "\t" + timestamp + "\t" + nonce + "\t" + echostr);
        if (StringUtils.isNotEmpty(signature) && StringUtils.isNotEmpty(timestamp)
                && StringUtils.isNotEmpty(nonce) && StringUtils.isNotEmpty(echostr)) {
            String sTempStr = "";
            if (StringUtils.isNotEmpty(signature)) {
                log.info("验证成功：-----------：" + sTempStr);
                return echostr;
            } else {
                log.info("验证失败：-----------：00000");
                return "-1";
            }
        } else {
            log.info("验证失败：-----------：11111");
            return "-1";
        }
    }

    /**
     * 创建二维码
     *
     * @param
     * @return
     * @throws Exception
     */
    @GetMapping("/getImg")
    @Operation(summary = "创建生成公众号二维码")
     public CommonResult<String> getImg(@RequestParam(value = "userId", required = true)String userId) {
        String accessToken = wechatBindingService.getAccessToken();
        //真实业务代码
        //1.根据account账号信息查询redis是否存在该ticket数据，ticket有效期最大不超过2592000（即30天）
        //2、不存在调用getTemporaryQR返回ticket,存在则直接使用redis里面的请求
        //获取ticket
        String ticket = wechatBindingService.getTemporaryQR(accessToken, userId);
        String url2 = "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=" + ticket + "";
        String base64Image = "";
        try {
            URL url = new URL(url2);
            HttpURLConnection httpUrl = (HttpURLConnection) url.openConnection();
            httpUrl.connect();
            httpUrl.getInputStream();
            InputStream is = httpUrl.getInputStream();
            byte[] bytes = IOUtils.toByteArray(is);
            base64Image = Base64.getEncoder().encodeToString(bytes);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return success(base64Image);
    }

    /**
     * 微信公众号回调
     *
     * @param
     * @return
     * @throws Exception
     */
    @PermitAll
    @PostMapping("/checkSign")
    @Operation(summary = "微信服务器回调")
    public String checkSign(HttpServletRequest request, HttpServletResponse response) throws Exception {
        //获取微信请求参数
        log.info("接收微信公众号事件触发回调请求");
        String signature = request.getParameter("signature");
        String timestamp = request.getParameter("timestamp");
        String nonce = request.getParameter("nonce");
//        String echostr = request.getParameter ("echostr");
        //参数排序。 token 就要换成自己实际写的 token
        String[] params = new String[]{timestamp, nonce, "zjToken"};
        Arrays.sort(params);
        //拼接
        String paramstr = params[0] + params[1] + params[2];
        //加密
        //获取 shal 算法封装类
        MessageDigest Sha1Dtgest = MessageDigest.getInstance("SHA-1");
        //进行加密
        byte[] digestResult = Sha1Dtgest.digest(paramstr.getBytes("UTF-8"));
        //拿到加密结果
        String mysignature = CheckoutUtil.byteToStr(digestResult);
        mysignature = mysignature.toLowerCase(Locale.ROOT);
        log.info("微信加密，signature：" + signature);
        log.info("本地加密，mysignature：" + mysignature);
        //是否正确
        boolean signsuccess = mysignature.equals(signature);
        //逻辑处理
        if (signsuccess) {
            //peizhi  token
            wechatBindingService.callback(request, response);
        } else {
            return "false";
        }
        return "success";
    }



    @GetMapping("/isChange")
    @Operation(summary = "查询绑定关系是否存在变动")
    public CommonResult<Boolean> isChange() {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        return success(wechatBindingService.isChange(loginUser.getId()));
    }

    @PostMapping("/change")
    @Operation(summary = "变动绑定关系")
    public CommonResult<Boolean> change() {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        return success(wechatBindingService.change(loginUser.getId()));
    }

    @PostMapping("/cancelChange")
    @Operation(summary = "取消变动")
    public CommonResult<Boolean> cancelChange() {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        wechatBindingService.cancelChange(loginUser.getId());
        return success(true);
    }



    @PostMapping("/create")
    @Operation(summary = "创建微信公众号OpenId与用户绑定关系")
    @PreAuthorize("@ss.hasPermission('system:wechat-binding:create')")
    public CommonResult<Boolean> createWechatBinding(@Valid @RequestBody WechatBindingCreateReqVO createReqVO) {
        wechatBindingService.createWechatBinding(createReqVO);
        return success(true);
    }

    @PostMapping("/getWeChatBinding")
    @Operation(summary = "通过用户id获取绑定关系")
    public CommonResult<WechatBindingCreateReqVO> getWeChatBinding(@Valid @RequestBody WechatBindingCreateReqVO createReqVO) {
        WechatBindingDO weChatBinding=wechatBindingService.getWeChatBinding(createReqVO.getUserId());
        BeanUtil.copyProperties(weChatBinding,createReqVO);
        if (weChatBinding==null){
            createReqVO.setIsWechat(false);
            createReqVO.setIsEmail(false);
            createReqVO.setOpenId("");
            createReqVO.setEmail("");
        }
        return success(createReqVO);
    }

    @GetMapping("/getIsOpenId")
    @Operation(summary = "通过用户id获取是否绑定openId")
    public CommonResult<Boolean> getIsOpenId(@RequestParam(value = "userId", required = true)String userId){
       Boolean result= wechatBindingService.getIsOpenId(userId);
       return success(result);
    }

    /**
     * 创建二维码
     * @param userId
     * @return
     * @throws Exception
     */
    @GetMapping("/getImg1")
    @Operation(summary = "创建生成公众号二维码")
    public void getImg1(String userId,HttpServletResponse response) {
        String accessToken = wechatBindingService.getAccessToken();

        //真实业务代码
        //1.根据account账号信息查询redis是否存在该ticket数据，ticket有效期最大不超过2592000（即30天）
        //2、不存在调用getTemporaryQR返回ticket,存在则直接使用redis里面的请求

        //获取ticket
        String ticket = wechatBindingService.getTemporaryQR(accessToken, userId);
        String url2 = "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=" + ticket + "";
        try {
            URL url = new URL(url2);
            HttpURLConnection httpUrl = (HttpURLConnection) url.openConnection();
            httpUrl.connect();
            httpUrl.getInputStream();
            InputStream is = httpUrl.getInputStream();
            BufferedImage image = ImageIO.read(is);
            // 可通过输出流输出到页面
            ImageIO.write(image, "png", response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }





//    @PermitAll
//    @GetMapping("/sendWeChatMsg1")
//    @Operation(summary = "微信公众号推送告警测试用")
//    public  boolean sendWeChatMsg1(){
////        String result=wechatBindingService.sendMessage1();
//        log.info("result=============================================================="+result);
//        if (result!=null && result.contains("ok")) {
//            return true;
//        }
//        return false;
//    }



//    @PutMapping("/update")
//    @Operation(summary = "更新微信公众号OpenId与租户绑定关系")
//    @PreAuthorize("@ss.hasPermission('system:wechat-binding:update')")
//    public CommonResult<Boolean> updateWechatBinding(@Valid @RequestBody WechatBindingUpdateReqVO updateReqVO) {
//        wechatBindingService.updateWechatBinding(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除微信公众号OpenId与租户绑定关系")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('system:wechat-binding:delete')")
//    public CommonResult<Boolean> deleteWechatBinding(@RequestParam("id") Long id) {
//        wechatBindingService.deleteWechatBinding(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得微信公众号OpenId与租户绑定关系")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('system:wechat-binding:query')")
//    public CommonResult<WechatBindingRespVO> getWechatBinding(@RequestParam("id") Long id) {
//        WechatBindingDO wechatBinding = wechatBindingService.getWechatBinding(id);
//        return success(WechatBindingConvert.INSTANCE.convert(wechatBinding));
//    }
//
//    @GetMapping("/list")
//    @Operation(summary = "获得微信公众号OpenId与租户绑定关系列表")
//    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
//    @PreAuthorize("@ss.hasPermission('system:wechat-binding:query')")
//    public CommonResult<List<WechatBindingRespVO>> getWechatBindingList(@RequestParam("ids") Collection<Long> ids) {
//        List<WechatBindingDO> list = wechatBindingService.getWechatBindingList(ids);
//        return success(WechatBindingConvert.INSTANCE.convertList(list));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得微信公众号OpenId与租户绑定关系分页")
//    @PreAuthorize("@ss.hasPermission('system:wechat-binding:query')")
//    public CommonResult<PageResult<WechatBindingRespVO>> getWechatBindingPage(@Valid WechatBindingPageReqVO pageVO) {
//        PageResult<WechatBindingDO> pageResult = wechatBindingService.getWechatBindingPage(pageVO);
//        return success(WechatBindingConvert.INSTANCE.convertPage(pageResult));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出微信公众号OpenId与租户绑定关系 Excel")
//    @PreAuthorize("@ss.hasPermission('system:wechat-binding:export')")
//    @OperateLog(type = EXPORT)
//    public void exportWechatBindingExcel(@Valid WechatBindingExportReqVO exportReqVO,
//                                         HttpServletResponse response) throws IOException {
//        List<WechatBindingDO> list = wechatBindingService.getWechatBindingList(exportReqVO);
//        // 导出 Excel
//        List<WechatBindingExcelVO> datas = WechatBindingConvert.INSTANCE.convertList02(list);
//        ExcelUtils.write(response, "微信公众号OpenId与租户绑定关系.xls", "数据", WechatBindingExcelVO.class, datas);
//    }

}
