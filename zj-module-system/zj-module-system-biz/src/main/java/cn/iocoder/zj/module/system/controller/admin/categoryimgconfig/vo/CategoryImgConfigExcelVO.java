package cn.iocoder.zj.module.system.controller.admin.categoryimgconfig.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 类别图片管理 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class CategoryImgConfigExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("大类或者平台")
    private String category;

    @ExcelProperty("小类")
    private String app;

    @ExcelProperty("类型：1：资产  2：平台")
    private Integer type;

    @ExcelProperty("icon")
    private String icon;

    @ExcelProperty("详情icon")
    private String detailIcon;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
