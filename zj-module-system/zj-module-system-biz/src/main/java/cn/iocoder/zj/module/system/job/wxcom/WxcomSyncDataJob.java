package cn.iocoder.zj.module.system.job.wxcom;

import cn.hutool.core.date.DateUtil;
import cn.iocoder.zj.framework.common.pojo.UserBindDTO;
import cn.iocoder.zj.framework.common.util.collection.CollectionUtils;
import cn.iocoder.zj.framework.common.util.dingtalk.QyWxUtils;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.tenant.core.job.TenantJob;
import cn.iocoder.zj.module.monitor.api.hostinfo.HostInfoApi;
import cn.iocoder.zj.module.system.dal.dataobject.info.InfoDO;
import cn.iocoder.zj.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.zj.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.zj.module.system.dal.dataobject.usersync.UserSyncDO;
import cn.iocoder.zj.module.system.dal.mysql.info.InfoMapper;
import cn.iocoder.zj.module.system.dal.mysql.tenant.TenantMapper;
import cn.iocoder.zj.module.system.dal.mysql.user.AdminUserMapper;
import cn.iocoder.zj.module.system.dal.mysql.usersync.UserSyncMapper;
import cn.iocoder.zj.module.system.service.mail.MailSendService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
@TenantJob
@Slf4j
public class WxcomSyncDataJob {

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private AdminUserMapper userMapper;

    @Resource
    private UserSyncMapper userSyncMapper;

    @Resource
    private TenantMapper tenantMapper;

    @Autowired
    InfoMapper infoMapper;

    @Resource
    private MailSendService mailSendService;

    @XxlJob("wxcomsyncdata")
    public void wxcomSyncData() {
        LambdaQueryWrapperX<AdminUserDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(AdminUserDO::getSyncState,1);
        List<AdminUserDO> userDOList = userMapper.selectList(wrapper);
        if(!CollectionUtils.isAnyEmpty(userDOList) && redisTemplate.hasKey("app_key_secret:app")){
            List<UserSyncDO> list = new ArrayList<>();
            List<AdminUserDO> succlist = new ArrayList<>();
            UserBindDTO userBindDTO =
                    JsonUtils.parseObject(redisTemplate.opsForValue().get("app_key_secret:app").toString(),UserBindDTO.class);
            userDOList.forEach(x->{
                if(StringUtil.isNotEmpty(x.getMobile()) && isValidChinesePhoneNumber(x.getMobile())){
                    String token = QyWxUtils.getToken(userBindDTO.getWxCorpid(), userBindDTO.getWxCorpsecret());
                    String userID = QyWxUtils.getUserID(x.getMobile(), token);
                    UserSyncDO syncDO = new UserSyncDO();
                    if(StringUtil.isNotEmpty(userID)){
                        syncDO.setWxUserId(userID);
                        syncDO.setNickname(x.getNickname());
                        syncDO.setPhone(x.getMobile());
                        x.setSyncState(0);
                        x.setUpdateTime(DateUtil.toLocalDateTime(new Date()));
                        succlist.add(x);
                    }else {
                        syncDO.setWxUserId(userID);
                        syncDO.setNickname(x.getNickname());
                        syncDO.setPhone(x.getMobile());
                        syncDO.setRemark("未查询到userId");
                    }
                    list.add(syncDO);
                }
            });

            if(!CollectionUtils.isAnyEmpty(list)){
                userSyncMapper.insertBatch(list);
            }

            if(!CollectionUtils.isAnyEmpty(succlist)){
                userMapper.updateBatch(succlist);
            }
        }
    }

    public boolean isValidChinesePhoneNumber(String phoneNumber) {
        // 中国手机号码正则表达式
        String regex = "^1[3-9]\\d{9}$";
        return phoneNumber != null && phoneNumber.matches(regex);
    }

    @XxlJob("checklicencenum")
    public void checkLicense() {
        try {
            List<TenantDO> tenants = getActiveTenants();
            int emailCount = processTenantsLicense(tenants);
            log.info("许可证检查邮件发送完成，共发送 {} 次", emailCount);
        } catch (Exception e) {
            log.error("许可证检查任务执行异常", e);
        }
    }

    private List<TenantDO> getActiveTenants() {
        LambdaQueryWrapper<TenantDO> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TenantDO::getDeleted, 0)
           .eq(TenantDO::getStatus, 0)
           .ne(TenantDO::getId, 1)
           .ne(TenantDO::getPackageId, 0);
        return tenantMapper.selectList(lqw);
    }

    private int processTenantsLicense(List<TenantDO> tenants) {
        int emailCount = 0;
        LocalDateTime currentTime = LocalDateTime.now();
        
        for (TenantDO tenant : tenants) {
            LocalDateTime expireTime = tenant.getExpireTime();
            if (expireTime == null || !expireTime.isAfter(currentTime)) {
                continue;
            }

            long daysUntilExpiration = ChronoUnit.DAYS.between(currentTime, expireTime);
            log.info("租户[{}]许可证到期剩余天数: {}", tenant.getId(), daysUntilExpiration);
            
            if (daysUntilExpiration <= 7) {
                emailCount += sendExpirationNotices(tenant, daysUntilExpiration, expireTime);
            }
        }
        return emailCount;
    }

    private int sendExpirationNotices(TenantDO tenant, long daysUntilExpiration, LocalDateTime expireTime) {
        int emailCount = 0;
        List<AdminUserDO> maintainers = getMaintainers(tenant.getMaintainerId());
        String formattedExpireTime = DateUtil.format(expireTime, "yyyy年MM月dd日");
        
        for (AdminUserDO maintainer : maintainers) {
            String email = maintainer.getEmail();
            if (StringUtil.isNotEmpty(email)) {
                try {
                    mailSendService.sendMail(email, tenant.getId(), daysUntilExpiration + 1, formattedExpireTime);
                    log.info("已发送许可证到期提醒邮件至：{}", email);
                    emailCount++;
                } catch (Exception e) {
                    log.error("发送邮件失败，收件人: {}", email, e);
                }
            }
        }
        return emailCount;
    }

    private List<AdminUserDO> getMaintainers(String maintainerIds) {
        if (StringUtil.isEmpty(maintainerIds)) {
            return new ArrayList<>();
        }
        
        List<Long> maintainerIdList = Arrays.stream(maintainerIds.split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
                
        LambdaQueryWrapper<AdminUserDO> userLqw = new LambdaQueryWrapper<>();
        userLqw.in(AdminUserDO::getId, maintainerIdList)
               .eq(AdminUserDO::getDeleted, 0);
        return userMapper.selectList(userLqw);
    }

}


