package cn.iocoder.zj.module.system.controller.admin.wechatbinding.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 微信公众号OpenId与租户绑定关系创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WechatBindingCreateReqVO extends WechatBindingBaseVO {

}
