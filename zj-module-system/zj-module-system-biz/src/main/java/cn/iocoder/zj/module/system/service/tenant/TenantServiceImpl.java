package cn.iocoder.zj.module.system.service.tenant;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.util.collection.CollectionUtils;
import cn.iocoder.zj.framework.common.util.date.DateUtils;
import cn.iocoder.zj.framework.datapermission.core.util.DataPermissionUtils;
import cn.iocoder.zj.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.config.TenantProperties;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.zj.framework.tenant.core.util.TenantUtils;
import cn.iocoder.zj.module.customer.api.contractInfo.ContractInfoApi;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmConfigRespDTO;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import cn.iocoder.zj.module.system.api.tenant.dto.TenantUserDTO;
import cn.iocoder.zj.module.system.controller.admin.permission.vo.role.RoleCreateReqVO;
import cn.iocoder.zj.module.system.controller.admin.tenant.vo.tenant.*;
import cn.iocoder.zj.module.system.convert.tenant.TenantConvert;
import cn.iocoder.zj.module.system.dal.dataobject.dept.UserPostDO;
import cn.iocoder.zj.module.system.dal.dataobject.permission.MenuDO;
import cn.iocoder.zj.module.system.dal.dataobject.permission.RoleDO;
import cn.iocoder.zj.module.system.dal.dataobject.permission.UserRoleDO;
import cn.iocoder.zj.module.system.dal.dataobject.screen.ScreenDO;
import cn.iocoder.zj.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.zj.module.system.dal.dataobject.tenant.TenantPackageDO;
import cn.iocoder.zj.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.zj.module.system.dal.mysql.dept.UserPostMapper;
import cn.iocoder.zj.module.system.dal.mysql.permission.RoleMapper;
import cn.iocoder.zj.module.system.dal.mysql.permission.UserRoleMapper;
import cn.iocoder.zj.module.system.dal.mysql.platformconfig.PlatformConfigMapper;
import cn.iocoder.zj.module.system.dal.mysql.platformtenant.PlatformTenantMapper;
import cn.iocoder.zj.module.system.dal.mysql.screen.ScreenMapper;
import cn.iocoder.zj.module.system.dal.mysql.tenant.TenantMapper;
import cn.iocoder.zj.module.system.dal.mysql.user.AdminUserMapper;
import cn.iocoder.zj.module.system.dal.mysql.wechatbinding.WechatBindingMapper;
import cn.iocoder.zj.module.system.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.system.enums.permission.RoleCodeEnum;
import cn.iocoder.zj.module.system.enums.permission.RoleTypeEnum;
import cn.iocoder.zj.module.system.mq.producer.platform.PlatformProducer;
import cn.iocoder.zj.module.system.service.mail.MailSendService;
import cn.iocoder.zj.module.system.service.permission.MenuService;
import cn.iocoder.zj.module.system.service.permission.PermissionService;
import cn.iocoder.zj.module.system.service.permission.RoleService;
import cn.iocoder.zj.module.system.service.tenant.handler.TenantInfoHandler;
import cn.iocoder.zj.module.system.service.tenant.handler.TenantMenuHandler;
import cn.iocoder.zj.module.system.service.user.AdminUserService;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.net.MalformedURLException;
import java.net.URL;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.convertSet;
import static cn.iocoder.zj.module.system.enums.ErrorCodeConstants.*;
import static cn.iocoder.zj.module.system.util.StringUtil.extractPort;
import static java.util.Collections.singleton;

/**
 * 租户 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class TenantServiceImpl implements TenantService {

    @SuppressWarnings("SpringJavaAutowiredFieldsWarningInspection")
    @Autowired(required = false) // 由于 zj.tenant.enable 配置项，可以关闭多租户的功能，所以这里只能不强制注入
    private TenantProperties tenantProperties;

    @Resource
    private TenantMapper tenantMapper;
    @Resource
    private ScreenMapper screenMapper;
    @Resource
    private PlatformTenantMapper platformTenantMapper;

    @Resource
    private TenantPackageService tenantPackageService;
    @Resource
    @Lazy // 延迟，避免循环依赖报错
    private AdminUserService userService;
    @Resource
    private RoleService roleService;
    @Resource
    private MenuService menuService;
    @Resource
    private PermissionService permissionService;
    @Resource
    AlarmConfigApi alarmConfigApi;
    @Resource
    private UserPostMapper userPostMapper;
    @Resource
    private ContractInfoApi contractInfoApi;
    @Resource
    private AdminUserMapper userMapper;

    @Resource
    private MailSendService mailSendService;

    @Resource
    PlatformConfigMapper platformConfigMapper;

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private UserRoleMapper userRoleMapper;

    @Resource
    PlatformProducer platformProducer;

    @Resource
    WechatBindingMapper wechatBindingMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    PlatformRedisDAO platformRedisDAO;
    @Value("${proxy.url}")
    private String proxyUrl;
//    @Resource
//    private ReportSubscriptionApi reportSubscriptionApi;

    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()-_=+[{]}\\|;:'\",<.>/?";


    @Override
    public List<Long> getTenantIdList() {
        List<TenantDO> tenants = tenantMapper.selectList();
        return CollectionUtils.convertList(tenants, TenantDO::getId);
    }

    @Override
    public void validTenant(Long id) {
        TenantDO tenant = getTenant(id);
        if (tenant == null) {
            throw exception(TENANT_NOT_EXISTS);
        }
        if (tenant.getStatus().equals(CommonStatusEnum.DISABLE.getStatus())) {
            throw exception(TENANT_DISABLE, tenant.getName());
        }
        if (DateUtils.isExpired(tenant.getExpireTime())) {
            throw exception(TENANT_OVERDUE_EXPIRE);
        }
    }

    @Override
    public List<TenantDO> getUserListByStatus(Integer status) {
        return tenantMapper.selectListByStatus(status);
    }


    @DSTransactional // 多数据源，使用 @DSTransactional 保证本地事务，以及数据源的切换
    @Override
    @TenantIgnore
    public Long createTenant(TenantCreateReqVO createReqVO) {
        //获取登录人
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        // 校验租户名称是否重复
        validTenantNameDuplicate(createReqVO.getName(), null);
        // 校验套餐被禁用
        TenantPackageDO tenantPackage = tenantPackageService.validTenantPackage(createReqVO.getPackageId());

        // 创建租户
        TenantDO tenant = TenantConvert.INSTANCE.convert(createReqVO);
        tenant.setCreator(loginUser.getId().toString());
        tenantMapper.insert(tenant);

        //生成8-16位密码
        createReqVO.setPassword(generatePassword());

        TenantUtils.execute(tenant.getId(), () -> {
            // 创建角色
            Long roleId = createRole(tenantPackage);
            // 创建用户，并分配角色
            Long userId = createUser(roleId, createReqVO);
            // 修改租户的管理员
            tenantMapper.updateById(new TenantDO().setId(tenant.getId()).setContactUserId(userId));
        });
        //创建租户首页默认布局
        String layoutConfig = screenMapper.selectLayoutConfig();
        screenMapper.createMonitorHomepageDisplay(tenant.getId(), layoutConfig);
        //创建租户大屏默认布局顺序
        List<ScreenDO> list = screenMapper.getScreenList(1L);
        List<ScreenDO> screenDOList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            ScreenDO screenDO = new ScreenDO();
            screenDO.setModule(list.get(i).getModule());
            screenDO.setMonitorEntryName(list.get(i).getMonitorEntryName());
            screenDO.setMonitorEntry(list.get(i).getMonitorEntry());
            screenDO.setResourceType(list.get(i).getResourceType());
            screenDO.setResourceTypeName(list.get(i).getResourceTypeName());
            screenDO.setSort(list.get(i).getSort());
            screenDO.setCreator(list.get(i).getCreator());
            screenDO.setTenantId(tenant.getId());
            screenDO.setTenantName(tenant.getName());
            screenDOList.add(screenDO);
        }

        screenMapper.insertBatch(screenDOList);
        //管理员的告警配置传递给新租户
        CommonResult<List<AlarmConfigRespDTO>> resultList = alarmConfigApi.list(loginUser.getTenantId());
        if (resultList.getData().size() > 0) {
            List<AlarmConfigRespDTO> toInsertAlarmConfig = new ArrayList<>();
            for (AlarmConfigRespDTO alarmConfig : resultList.getData()) {
                alarmConfig.setTenantId(tenant.getId());
                toInsertAlarmConfig.add(alarmConfig);
            }
            alarmConfigApi.insertBatch(toInsertAlarmConfig);
        }

        //判断是否为测试租户
        if (createReqVO.getIsTest() == 0) {
            String expireTime = LocalDateTimeUtil.format(tenant.getExpireTime(), "yyyy-MM-dd");
            contractInfoApi.createTestContract(tenant.getId(), tenant.getName(), expireTime);
        } else {
            if (StrUtil.isNotEmpty(createReqVO.getContractNo())) {
                contractInfoApi.formalContract(tenant.getId(), createReqVO.getContractNo());
            }
        }
        //将租户关联到运维人员
//        AdminUserDO maintainer = userService.selectUserById(createReqVO.getMaintainerId());

        //        //多个运维人员查询到多个租户
        String maintainerId = createReqVO.getMaintainerId();
        List<String> maintainerIdList = Arrays.asList(maintainerId.split(","));
        //多个租户
        DataPermissionUtils.executeIgnore(() -> {
            //校验存在性
            List<AdminUserDO> adminUserDOList = userService.getUserByIds(maintainerIdList);
            //修改租户绑定的运维人员，此为添加
            adminUserDOList = adminUserDOList.stream().map(item -> {
                String serviceTenantId = item.getServiceTenantId();
                if (StringUtil.isNotEmpty(serviceTenantId)) {
                    List<String> tenantIds = Arrays.asList(maintainerId.split(","));
                    if (!tenantIds.contains(tenant.getId())) {
                        item.setServiceTenantId(serviceTenantId + "," + tenant.getId());
                    }
                } else {
                    item.setServiceTenantId(tenant.getId().toString());
                }
                return item;
            }).collect(Collectors.toList());
            if (adminUserDOList.size() > 0) {
                userMapper.updateBatch(adminUserDOList);
            }
        });
        //发送邮件通知租户
//        if(StringUtil.isEmpty(maintainer.getServiceTenantId())){
//            maintainer.setServiceTenantId(String.valueOf(tenant.getId()));
//        }else {
//            maintainer.setServiceTenantId(maintainer.getServiceTenantId()+","+tenant.getId());
//        }
//        maintainer.setPostIds(new HashSet<>());
//        userService.updateUser(BeanUtil.copyProperties(maintainer, UserUpdateReqVO.class));
        mailSendService.sendHtmlEmailWithImage(createReqVO.getContactMobile(), createReqVO.getPassword(), createReqVO.getContactEmail());
        log.info("用户" + createReqVO.getContactMobile() + "账号开通邮件已发送");
//        platformProducer.sendPlatformSendMailCollection(createReqVO.getUsername(), createReqVO.getPassword(), createReqVO.getContactEmail());
        return 1L;
    }

    public static String generatePassword() {
        int length = new SecureRandom().nextInt(9) + 8; // 随机长度在8-16位之间

        SecureRandom random = new SecureRandom();
        byte[] bytes = new byte[length];
        random.nextBytes(bytes);

        List<Character> characters = new ArrayList<>(length);
        for (byte b : bytes) {
            characters.add(CHARACTERS.charAt(Math.abs(b) % CHARACTERS.length()));
        }
        Collections.shuffle(characters);

        char[] password = new char[length];
        for (int i = 0; i < length; i++) {
            password[i] = characters.get(i);
        }

        return new String(password);
    }

    private Long createUser(Long roleId, TenantCreateReqVO createReqVO) {
        // 创建用户
        Long userId = userService.createUser(TenantConvert.INSTANCE.convert02(createReqVO));
        // 分配角色
        permissionService.assignUserRole(userId, singleton(roleId));
        return userId;
    }

    @Override
    @Transactional
    public void updateRootTenant(TenantUpdateRootReqVo updateReqVO) {
        TenantDO tenant = validateUpdateTenant(updateReqVO.getId());
        Long oldUserId = tenant.getContactUserId();
        AdminUserDO userDO = userMapper.selectById(oldUserId);
        //校验联系人是否修改
        if (!updateReqVO.getContactMobile().equals(tenant.getContactMobile()) || !updateReqVO.getContactEmail().equals(userDO.getEmail())) {
            //生成8-16位密码
            updateReqVO.setPassword(generatePassword());

            //将旧的联系人删除
            userMapper.deleteByUserId(oldUserId);
            //将redis中的登录失败信息删除
            stringRedisTemplate.delete("login_fail_" + tenant.getContactMobile());
            //公众号绑定历史userId的数据删除
            wechatBindingMapper.deleteByUserId(oldUserId);
            //移除用户的所有订阅任务
//            reportSubscriptionApi.removeTimedTask(oldUserId);
            //新增新的联系人
            TenantUtils.execute(tenant.getId(), () -> {
                Long userId = userService.createUser(TenantConvert.INSTANCE.convert04(updateReqVO));
                //查询角色
                List<UserRoleDO> userRoles = userRoleMapper.selectListByUserId(oldUserId);
                //添加角色数据
                for (UserRoleDO userRole : userRoles) {
                    userRole.setId(null);
                    //放入新的userId
                    userRole.setUserId(userId);
                }
                userRoleMapper.insertBatch(userRoles);
                //修改租户的管理员
                tenantMapper.updateById(new TenantDO().setId(tenant.getId()).setContactUserId(userId));
            });
            //发送邮件通知租户
            mailSendService.sendHtmlEmailWithImage(updateReqVO.getContactMobile(), updateReqVO.getPassword(), updateReqVO.getContactEmail());
            log.info("用户" + updateReqVO.getContactMobile() + "账号开通邮件已发送");
//            platformProducer.sendPlatformSendMailCollection(updateReqVO.getUsername(), updateReqVO.getPassword(), updateReqVO.getContactEmail());
        } else {
            //修改联系人基本信息
            Set<Long> dbPostIds = convertSet(userPostMapper.selectListByUserId(tenant.getContactUserId()), UserPostDO::getPostId);
            TenantUpdateReqVO reqVO = new TenantUpdateReqVO();
            BeanUtils.copyProperties(updateReqVO, reqVO);
            userService.updateUser(TenantConvert.INSTANCE.convert03(reqVO, tenant.getContactUserId(), dbPostIds));
        }
        //                更新租户同时变更该租户下用户的公司信息
        userService.updateOrganizationByTenantId(updateReqVO.getId(), updateReqVO.getOrganization());
        TenantDO tenantDO = new TenantDO();
        BeanUtils.copyProperties(updateReqVO, tenantDO);
        tenantMapper.updateById(tenantDO);
    }

    @Override
    @DSTransactional
    @TenantIgnore
    public void updateMaintainTenant(TenantUpdateMaintainReqVo updateReqVO) {
        TenantDO tenant = validateUpdateTenant(updateReqVO.getId());
        //校验运维人员是否修改
        if (StringUtil.isNotEmpty(updateReqVO.getMaintainerId())) {
            if (tenant.getMaintainerId() == null || !tenant.getMaintainerId().equals(updateReqVO.getMaintainerId())) {
                List<String> list = Arrays.asList(updateReqVO.getMaintainerId().split(","));
                List<String> oldList = Arrays.asList(tenant.getMaintainerId().split(","));
                //新增
                List<String> differentDataInList1 = list.stream()
                        .filter(e -> !oldList.contains(e))
                        .collect(Collectors.toList());
                //去除
                List<String> differentDataInList2 = oldList.stream()
                        .filter(e -> !list.contains(e))
                        .collect(Collectors.toList());

                if (differentDataInList2.size() > 0) {
                    LambdaQueryWrapper<AdminUserDO> lqw = new LambdaQueryWrapper<>();
                    lqw.in(AdminUserDO::getId, differentDataInList2);
                    //旧数据
                    List<AdminUserDO> oldUserList = userMapper.selectList(lqw);
                    for (AdminUserDO adminUserDO : oldUserList) {
                        if (adminUserDO != null && StringUtil.isNotEmpty(adminUserDO.getServiceTenantId())) {
                            List<String> serviceTenantList = new ArrayList<>(Arrays.asList(adminUserDO.getServiceTenantId().split(",")));
                            serviceTenantList = serviceTenantList.stream()
                                    .filter(tenantId -> !tenantId.equals(updateReqVO.getId().toString()))
                                    .collect(Collectors.toList());
                            String join = String.join(",", serviceTenantList);
                            adminUserDO.setServiceTenantId(join);
                            userMapper.updateById(adminUserDO);
                        }
                    }
                }

                //新数据
                if (differentDataInList1.size() > 0) {
                    LambdaQueryWrapper<AdminUserDO> lqw2 = new LambdaQueryWrapper<>();
                    lqw2.in(AdminUserDO::getId, differentDataInList1);
                    List<AdminUserDO> newUserList = userMapper.selectList(lqw2);
                    for (AdminUserDO adminUserDO : newUserList) {
                        if (StringUtil.isNotEmpty(adminUserDO.getServiceTenantId())) {
                            List<String> serviceTenantList = new ArrayList<>(Arrays.asList(adminUserDO.getServiceTenantId().split(",")));
                            if (!serviceTenantList.contains(updateReqVO.getId().toString())) {
                                serviceTenantList.add(updateReqVO.getId().toString());
                                String join = String.join(",", serviceTenantList);
                                adminUserDO.setServiceTenantId(join);
                                userMapper.updateById(adminUserDO);
                            }
                        } else {
                            adminUserDO.setServiceTenantId(updateReqVO.getId().toString());
                            userMapper.updateById(adminUserDO);
                        }
                    }
                }
            }
        }
        tenant.setMaintainerId(updateReqVO.getMaintainerId());
        tenant.setMaintainerName(updateReqVO.getMaintainerName());
        tenantMapper.updateById(tenant);

    }

    @Override
    @TenantIgnore
    public List<TenantUserRespVO> getTenantByRole() {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionService.getUserRoleIdListByUserId(loginUser.getId());

        // 查询当前用户是否有运维人员或运维管理权限
        Set<Long> managerRoles = roleService.getRoleIdByCode("om_manager");
        Set<Long> superRoles = roleService.getRoleIdByCode("super_admin");
        Set<Long> csRoles = roleService.getRoleIdByCode("cs_admin");

        boolean isManager = roleIds.stream().anyMatch(managerRoles::contains);
        boolean isSuperadmin = roleIds.stream().anyMatch(superRoles::contains);
        boolean isCsadmin = roleIds.stream().anyMatch(csRoles::contains);

        if (isManager || isSuperadmin || isCsadmin) {
            Long tenantId = loginUser.getTenantId();
            //获取租户绑定的所有平台，返回当前平台下的所有租户
            return tenantMapper.getTenantByPlatform(tenantId);
        } else {
            // 检查是否为运维人员
            Set<Long> maintenanceRoles = roleService.getRoleIdByCode("operation_maintenance");
            boolean isMaintenance = roleIds.stream().anyMatch(maintenanceRoles::contains);

            if (isMaintenance) {
                AdminUserDO adminUser = userMapper.selectById(loginUser.getId());
                String serviceTenantId = adminUser.getServiceTenantId();

                if (StringUtil.isNotEmpty(serviceTenantId)) {
                    List<Long> tenantIds = Arrays.stream(serviceTenantId.split(","))
                            .map(Long::parseLong)
                            .collect(Collectors.toList());
                    return tenantMapper.getTenantByRole(tenantIds);
                }
            }
        }
        return null;
    }

    @Override
    public List<TenantUserDTO> getTenantAllIds() {
        List<TenantUserRespVO> list = tenantMapper.getTenantByRole(null);
        List<TenantUserDTO> dtos = list.stream().map(item -> {
            TenantUserDTO tenantUserDTO = new TenantUserDTO();
            tenantUserDTO.setId(item.getId());
            tenantUserDTO.setName(item.getName());
            return tenantUserDTO;
        }).collect(Collectors.toList());
        return dtos;

    }

    @Override
    public List<TenantDO> getOrderEnforcer(Long platformId) {
        return tenantMapper.getOrderEnforcer(platformId);
    }

    @Override
    @TenantIgnore
    public List<Long> getTenantIdsByPlatform(Long tenantId) {
        List<TenantUserRespVO> list = tenantMapper.getTenantByPlatform(tenantId);
        List<Long> tenantIds = list.stream()
                .map(item -> Long.valueOf(item.getId()))
                .collect(Collectors.toList());
        return tenantIds;
    }

    @Override
    public List<TenantDO> getTenantSelectList() {
        return tenantMapper.selectList();
    }

    @Override
    public long getStateByTenantId(Long tenantId) {
        return tenantMapper.getStateByTenantId(tenantId);
    }

    private Long createRole(TenantPackageDO tenantPackage) {
        // 创建角色
        RoleCreateReqVO reqVO = new RoleCreateReqVO();
        reqVO.setName(RoleCodeEnum.TENANT_ADMIN.getName()).setCode(RoleCodeEnum.TENANT_ADMIN.getCode())
                .setSort(0).setRemark("系统自动生成");
        Long roleId = roleService.createRole(reqVO, RoleTypeEnum.SYSTEM.getType());
        // 分配权限
        permissionService.assignRoleMenu(roleId, tenantPackage.getMenuIds());
        createManager(tenantPackage);
        createMaintenance(tenantPackage);
        return roleId;
    }

    private void createManager(TenantPackageDO tenantPackage) {
        // 创建角色
        RoleCreateReqVO reqVO = new RoleCreateReqVO();
        reqVO.setName(RoleCodeEnum.OM_MANAGER.getName()).setCode(RoleCodeEnum.OM_MANAGER.getCode())
                .setSort(1).setRemark("系统自动生成");
        Long roleId = roleService.createRole(reqVO, RoleTypeEnum.SYSTEM.getType());
        // 分配权限
        permissionService.assignRoleMenu(roleId, tenantPackage.getMenuIds());
    }

    private Long createMaintenance(TenantPackageDO tenantPackage) {
        // 创建角色
        RoleCreateReqVO reqVO = new RoleCreateReqVO();
        reqVO.setName(RoleCodeEnum.OPERATION_MAINTENANCE.getName()).setCode(RoleCodeEnum.OPERATION_MAINTENANCE.getCode())
                .setSort(2).setRemark("系统自动生成");
        Long roleId = roleService.createRole(reqVO, RoleTypeEnum.SYSTEM.getType());
        // 分配权限
        permissionService.assignRoleMenu(roleId, tenantPackage.getMenuIds());
        return roleId;
    }

    @Override
    @DSTransactional
    public void updateTenant(TenantUpdateReqVO updateReqVO) {

        // 校验存在
        TenantDO tenant = validateUpdateTenant(updateReqVO.getId());
        // 校验租户名称是否重复
        validTenantNameDuplicate(updateReqVO.getName(), updateReqVO.getId());
        // 校验套餐被禁用

        TenantPackageDO tenantPackage = new TenantPackageDO();
        // 套餐id为0的时候代表是管理员跳过套餐选项
        if (updateReqVO.getPackageId() != 0) {
            tenantPackage = tenantPackageService.validTenantPackage(updateReqVO.getPackageId());
        }
        List<Map> platformList = platformConfigMapper.getPlatformSelectList(Arrays.asList(String.valueOf(updateReqVO.getId()).split(",")));
        String platformJson = JSONArray.toJSONString(platformList);
        String platformNames = "";
        for (int i = 0; i < platformList.size(); i++) {
            platformNames += platformList.get(i).get("platformName") + ",";
        }
        if (StringUtil.isNotEmpty(platformNames)) {
            updateReqVO.setPlatformNames(platformNames.substring(0, platformNames.length() - 1));
        }
        //若测试租户修改有效期，修改租户对应合同和客户服务日期
        if (updateReqVO.getIsTest() != null) {
            if (updateReqVO.getIsTest() == 0 && tenant.getExpireTime().compareTo(updateReqVO.getExpireTime()) != 0 && updateReqVO.getId() != 1) {
                String expireTime = LocalDateTimeUtil.format(updateReqVO.getExpireTime(), "yyyy-MM-dd");
                String testContractNo = tenantMapper.getTestContractNo(updateReqVO.getId());
                contractInfoApi.updateExpireTime(expireTime, testContractNo);
            }
            //若为租户转正式，修改租户对应合同和客户
            if (tenant.getIsTest() == 0 && updateReqVO.getIsTest() == 1) {
                contractInfoApi.transferContract(updateReqVO.getId(), updateReqVO.getContractNo());
            }
            //若正式租户对应合同改变，修改租户对应合同和客户
            if (tenant.getIsTest() == 1) {
                String oldContractNo = tenantMapper.getTenantById(updateReqVO.getId()).getContractNo();
                if (oldContractNo != null || !oldContractNo.equals(updateReqVO.getContractNo())) {
                    contractInfoApi.updateContract(updateReqVO.getId(), updateReqVO.getContractNo());
                }
            }
        }

        // 更新租户
        TenantDO updateObj = TenantConvert.INSTANCE.convert(updateReqVO);
        updateObj.setPlatformJson(platformJson);
        tenantMapper.updateById(updateObj);
        // 更新联系人对应用户
        TenantDO tenantDO = tenantMapper.selectById(updateObj.getId());
        Set<Long> dbPostIds = convertSet(userPostMapper.selectListByUserId(tenantDO.getContactUserId()), UserPostDO::getPostId);
        userService.updateUser(TenantConvert.INSTANCE.convert03(updateReqVO, tenantDO.getContactUserId(), dbPostIds));
        // 如果套餐发生变化，则修改其角色的权限
        if (ObjectUtil.notEqual(tenant.getPackageId(), updateReqVO.getPackageId())) {
            updateTenantRoleMenu(tenant.getId(), tenantPackage.getMenuIds());
        }
//        //将原先的运维人员与该租户的关系解除
//        if(ObjectUtil.isNotEmpty(tenant.getMaintainerId())) {
//            AdminUserDO oldMaintainer = userService.getUser(tenant.getMaintainerId());
//            List<String> serviceTenantIds = new ArrayList<>(Arrays.asList(oldMaintainer.getServiceTenantId().split(",")));
//            serviceTenantIds.remove(String.valueOf(tenant.getId()));
//            oldMaintainer.setServiceTenantId(String.join(",", serviceTenantIds));
//            userService.updateUser(BeanUtil.copyProperties(oldMaintainer, UserUpdateReqVO.class));
//        }
//
//        //将租户与新的运维人员建立关系
//        AdminUserDO maintainer = userService.getUser(updateReqVO.getMaintainerId());
//        if(StringUtil.isEmpty(maintainer.getServiceTenantId())){
//            maintainer.setServiceTenantId(String.valueOf(tenant.getId()));
//        }else {
//            maintainer.setServiceTenantId(maintainer.getServiceTenantId()+","+tenant.getId());
//        }
//        userService.updateUser(BeanUtil.copyProperties(maintainer, UserUpdateReqVO.class));
    }

    private void validTenantNameDuplicate(String name, Long id) {
        TenantDO tenant = tenantMapper.selectByName(name);
        if (tenant == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同名字的租户
        if (id == null) {
            throw exception(TENANT_NAME_DUPLICATE, name);
        }
        if (!tenant.getId().equals(id)) {
            throw exception(TENANT_NAME_DUPLICATE, name);
        }
    }

    @Override
    @DSTransactional
    public void updateTenantRoleMenu(Long tenantId, Set<Long> menuIds) {
        TenantUtils.execute(tenantId, () -> {
            // 获得所有角色
            List<RoleDO> roles = roleService.getRoleList();
            roles.forEach(role -> Assert.isTrue(tenantId.equals(role.getTenantId()), "角色({}/{}) 租户不匹配",
                    role.getId(), role.getTenantId(), tenantId)); // 兜底校验
            // 重新分配每个角色的权限
            roles.forEach(role -> {
                // 如果是租户管理员，重新分配其权限为租户套餐的权限
                if (Objects.equals(role.getCode(), RoleCodeEnum.TENANT_ADMIN.getCode())) {
                    permissionService.assignRoleMenu(role.getId(), menuIds);
                    log.info("[updateTenantRoleMenu][租户管理员({}/{}) 的权限修改为({})]", role.getId(), role.getTenantId(), menuIds);
                    return;
                }
                // 如果是其他角色，则去掉超过套餐的权限
                Set<Long> roleMenuIds = permissionService.getRoleMenuListByRoleId(role.getId());
                roleMenuIds = CollUtil.intersectionDistinct(roleMenuIds, menuIds);
                permissionService.assignRoleMenu(role.getId(), roleMenuIds);
                log.info("[updateTenantRoleMenu][角色({}/{}) 的权限修改为({})]", role.getId(), role.getTenantId(), roleMenuIds);
            });
        });
    }

    @Override
    @TenantIgnore
    public void deleteTenant(Long id) {
        // 校验存在
        TenantDO tenant = validateUpdateTenant(id);
        //解除运维人员与该租户的关系
        //为多个
        if (ObjectUtil.isNotEmpty(tenant.getMaintainerId())) {
//            AdminUserDO oldMaintainer = userService.getUser(tenant.getMaintainerId());
            ArrayList<String> list = new ArrayList<>(Arrays.asList(tenant.getMaintainerId().split(",")));
            LambdaQueryWrapper<AdminUserDO> lqw = new LambdaQueryWrapper<>();
            lqw.in(AdminUserDO::getId, list);
            List<AdminUserDO> userList = userMapper.selectList(lqw);
            for (AdminUserDO adminUserDO : userList) {
                if (adminUserDO != null && StringUtil.isNotEmpty(adminUserDO.getServiceTenantId())) {
                    List<String> serviceTenantList = new ArrayList<>(Arrays.asList(adminUserDO.getServiceTenantId().split(",")));
                    serviceTenantList = serviceTenantList.stream()
                            .filter(tenantId -> !tenantId.equals(tenant.getId().toString()))
                            .collect(Collectors.toList());
                    String join = String.join(",", serviceTenantList);
                    adminUserDO.setServiceTenantId(join);
                    userMapper.updateById(adminUserDO);
                }
            }

//            if(ObjectUtil.isNotEmpty(oldMaintainer)) {
//                List<String> serviceTenantIds = new ArrayList<>(Arrays.asList(oldMaintainer.getServiceTenantId().split(",")));
//                serviceTenantIds.remove(String.valueOf(tenant.getId()));
//                oldMaintainer.setServiceTenantId(String.join(",", serviceTenantIds));
//                userService.updateUser(BeanUtil.copyProperties(oldMaintainer, UserUpdateReqVO.class));
//            }
        }
        // 删除管理员和平台的关系并且删除网络相关数据
        deleteProxyInfo(id, tenant);
        // 删除
        tenantMapper.deleteById(id);
        // 删除租户的用户
        userService.deleteByTenantId(id);
        //删除租户首页默认布局
        screenMapper.deleteConfigByTenantId(id);
        //删除租户大屏默认布局顺序
        screenMapper.deleteScreenByTenantId(id);
        //删除租户平台关系
        platformTenantMapper.deleteByTenantId(id);
        //删除租户的平台
        platformConfigMapper.deleteByTenantId(id);
        //删除对应的测试、正式合同及工单
        contractInfoApi.tenantDelete(id);
        // 获取租户配置的告警模版id 删除相关关系
        List<String> list = tenantMapper.getTenantIdByDefine(id);
        // 获取租户下的采集器数据
        List<String> hzb = tenantMapper.selectHzbplatformById(id);
        if (!hzb.isEmpty()) {
            tenantMapper.deleteByHzbCollector(hzb);
        }
        // 更新采集器与租户的关系
        tenantMapper.updateByHzbCollector(id);

        if (list.size() > 0) {
            tenantMapper.deleteBatchAlertId(list);
            tenantMapper.deleteBatchAlertBindId(list);
        }
    }

    private void deleteProxyInfo(Long id, TenantDO tenant) {
        try {
            List<String> platform = platformTenantMapper.getByPlatformId(tenant.getId());
            if (platform.size() > 0) {
                List<PlatformconfigDTO> platformList = platformRedisDAO.get("platform");
                for (String platformId : platform) {
                    platformTenantMapper.deleteByPlatformId(Convert.toLong(platformId));

                    if (platformList != null && !platformList.isEmpty()) {
                        for (PlatformconfigDTO platformconfigDTO : platformList) {
                            if (platformconfigDTO.getId().equals(Convert.toLong(platformId))) {
                                int port = 0;
                                try {
                                    URL url = new URL(StrUtil.toString(platformconfigDTO.getUrl()));
                                    port = url.getPort();
                                    if (port == -1) {
                                        port = Convert.toInt(extractPort(StrUtil.toString(platformconfigDTO.getUrl())));
                                    }
                                } catch (MalformedURLException e) {
                                    log.error("Malformed URL: {}", platformconfigDTO.getUrl(), e);
                                }
                                try {
                                    // 查询采集器和内网穿透的关系
                                    List<Map> map = platformConfigMapper.getLicense(Convert.toLong(platformId));
                                    if (!map.isEmpty()) {
                                        // 拿license ip 端口，进行查询prodId
                                        Map portMapping = platformConfigMapper.getProdMapping(Convert.toStr(map.get(0).get("id")), Convert.toStr(port));
                                        String portId = Convert.toStr(portMapping.get("id"));
                                        HashMap<String, Object> delete = new HashMap<>();
                                        delete.put("id", portId);
                                        HttpUtil.post(proxyUrl + "/port-mapping/delete", delete);
                                    }
                                } catch (Exception e) {
                                    log.error("/port-mapping/delete,deleteProxyInfo删除代理相关数据失败", e);
                                }
                                // 删除vnc端口
                                if (Objects.nonNull(platformconfigDTO.getConsoleIp()) && platformconfigDTO.getConsoleProd() != null) {
                                    int vncPort = platformconfigDTO.getConsoleProd();
                                    try {
                                        // 查询采集器和内网穿透的关系
                                        List<Map> map = platformConfigMapper.getLicense(Convert.toLong(platformId));
                                        if (!map.isEmpty()) {

                                            // 拿license ip 端口，进行查询prodId
                                            Map portMapping = platformConfigMapper.getProdMapping(Convert.toStr(map.get(0).get("id")), Convert.toStr(vncPort));
                                            String portId = Convert.toStr(portMapping.get("id"));
                                            HashMap<String, Object> delete = new HashMap<>();
                                            delete.put("id", portId);
                                            HttpUtil.post(proxyUrl + "/port-mapping/delete", delete);

                                        }
                                    } catch (Exception e) {
                                        log.error("/port-mapping/delete,deleteProxyInfo删除代理相关数据失败", e);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("deleteProxyInfo删除项目中的内网穿透端口报错: {}", id, e);
        }
    }

    private TenantDO validateUpdateTenant(Long id) {
        TenantDO tenant = tenantMapper.selectById(id);
        if (tenant == null) {
            throw exception(TENANT_NOT_EXISTS);
        }
        // 内置租户，不允许删除(超级管理员除外)
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionService.getUserRoleIdListByUserId(loginUser.getId());
        if (!roleService.hasAnySuperAdmin(roleIds)) {
            if (isSystemTenant(tenant)) {
                throw exception(TENANT_CAN_NOT_UPDATE_SYSTEM);
            }
        }
        return tenant;
    }

    @Override
    public TenantDO getTenant(Long id) {
        return tenantMapper.selectById(id);
    }

    @Override
    public PageResult<TenantDO> getTenantPage(TenantPageReqVO pageReqVO) {
        return tenantMapper.selectPage(pageReqVO);
    }

    @Override
    public List<TenantDO> getTenantList(TenantExportReqVO exportReqVO) {
        return tenantMapper.selectList(exportReqVO);
    }

    @Override
    public TenantDO getTenantByName(String name) {
        return tenantMapper.selectByName(name);
    }

    @Override
    public Long getTenantCountByPackageId(Long packageId) {
        return tenantMapper.selectCountByPackageId(packageId);
    }

    @Override
    public List<TenantDO> getTenantListByPackageId(Long packageId) {
        return tenantMapper.selectListByPackageId(packageId);
    }

    @Override
    public void handleTenantInfo(TenantInfoHandler handler) {
        // 如果禁用，则不执行逻辑
        if (isTenantDisable()) {
            return;
        }
        // 获得租户
        TenantDO tenant = getTenant(TenantContextHolder.getRequiredTenantId());
        // 执行处理器
        handler.handle(tenant);
    }

    @Override
    public void handleTenantMenu(TenantMenuHandler handler) {
        // 如果禁用，则不执行逻辑
        if (isTenantDisable()) {
            return;
        }
        // 获得租户，然后获得菜单
        TenantDO tenant = getTenant(TenantContextHolder.getRequiredTenantId());
        Set<Long> menuIds;
        if (isSystemTenant(tenant)) { // 系统租户，菜单是全量的
            menuIds = CollectionUtils.convertSet(menuService.getMenuList(), MenuDO::getId);
        } else {
            menuIds = tenantPackageService.getTenantPackage(tenant.getPackageId()).getMenuIds();
        }
        // 执行处理器
        handler.handle(menuIds);
    }

    private static boolean isSystemTenant(TenantDO tenant) {
        return Objects.equals(tenant.getPackageId(), TenantDO.PACKAGE_ID_SYSTEM);
    }

    private boolean isTenantDisable() {
        return tenantProperties == null || Boolean.FALSE.equals(tenantProperties.getEnable());
    }

    @Override
    public PageResult<TenantRespVO> getTenantUserPage(TenantPageReqVO pageReqVO) {
        if (StringUtil.isNotEmpty(pageReqVO.getMaintainerIds())) {
            pageReqVO.setMaintainerIdList(Arrays.asList(pageReqVO.getMaintainerIds().split(",")));
        }
        if (StringUtil.isNotEmpty(pageReqVO.getSortBy())) {
            String regex = "([a-z])([A-Z]+)";
            String replacement = "$1_$2";
            String output = pageReqVO.getSortBy().replaceAll(regex, replacement).toLowerCase();
            pageReqVO.setSortBy(output);
        }
        PageParam pageParam = new PageParam().setPageNo(pageReqVO.getPageNo()).setPageSize(pageReqVO.getPageSize());
        IPage<TenantRespVO> mpPage = MyBatisUtils.buildPage(pageParam);
        List<TenantRespVO> tenantPage = tenantMapper.getTenantPage(mpPage, pageReqVO);
        if (CollUtil.isNotEmpty(tenantPage)) {
            List<Long> collect = tenantPage.stream().map(x -> x.getId()).collect(Collectors.toList());
            List<Map> map = tenantMapper.getPlatformByIds(collect);
            if (CollUtil.isNotEmpty(map)) {
                for (TenantRespVO tenant : tenantPage) {
                    tenant.setTotalCount("0");
                    for (Map<String, Object> m : map) {
                        if (m.get("tenant_id") != null && (Convert.toLong(m.get("tenant_id")).equals(tenant.getId()))) {
                            tenant.setTotalCount(Convert.toStr(m.get("total_count")));
                        }
                    }
                }
            }
        }


        return new PageResult<>(tenantPage, mpPage.getTotal());
    }

    @Override
    public List<TenantExcelVO> getTenantExcelList(TenantExportReqVO exportReqVO) {
        LoginUser user = SecurityFrameworkUtils.getLoginUser();
        Long id = userMapper.getUserIdByTenantId(user.getTenantId());
        return tenantMapper.getTenantExcelList(exportReqVO, id);
    }

    @Override
    public TenantRespVO getTenantById(Long id) {
        TenantRespVO tenantById = tenantMapper.getTenantByIdOne(id);
        List<Map> map = tenantMapper.getPlatformByIds(Arrays.asList(tenantById.getId()));
        if (CollUtil.isNotEmpty(map)) {
            tenantById.setTotalCount("0");
            for (Map<String, Object> m : map) {
                if (m.get("tenant_id") != null && (Convert.toLong(m.get("tenant_id")).equals(tenantById.getId()))) {
                    tenantById.setTotalCount(Convert.toStr(m.get("total_count")));
                }
            }
        }
        return tenantById;
    }

    @Override
    public Long getSuperIdByUser(Long id) {
        return tenantMapper.getSuperIdByUser(id);
    }


    @Scheduled(cron = "0 0 1 * * ?")
    @TenantIgnore
    public void checkLicense() {
        //查询租户列表
        LambdaQueryWrapper<TenantDO> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TenantDO::getDeleted, 0)
                .ne(TenantDO::getPackageId, 0);
        List<TenantDO> list = tenantMapper.selectList(lqw);
        for (TenantDO item : list) {
            LocalDateTime expireTime = item.getExpireTime();
            //判断当前日期距离到期时间天数
            if (expireTime != null) {
                LocalDateTime currentTime = LocalDateTime.now();
                if (expireTime.isAfter(currentTime)) {
                    long daysUntilExpiration = ChronoUnit.DAYS.between(currentTime, expireTime);
                    System.out.println("当前时间到过期时间还有 " + daysUntilExpiration + " 天");
                    if (daysUntilExpiration <= 7) {
                        //查询运维人员信息
                        List<Long> tenantIds = Arrays.stream(item.getMaintainerId().split(","))
                                .map(Long::parseLong)
                                .collect(Collectors.toList());
                        LambdaQueryWrapper<AdminUserDO> userLqw = new LambdaQueryWrapper<>();
                        userLqw.in(AdminUserDO::getId, tenantIds)
                                .eq(AdminUserDO::getDeleted, 0);
                        List<AdminUserDO> userList = userMapper.selectList(userLqw);
                        //发送邮件
                        for (AdminUserDO user : userList) {
                            String email = user.getEmail();
                            if (StringUtil.isNotEmpty(email)) {
                                String formattedExpireTime = DateUtil.format(expireTime, "yyyy年MM月dd日");
                                mailSendService.sendMail(email, item.getId(), daysUntilExpiration + 1, formattedExpireTime);
                            }
                        }
                    }
                }
            }
        }
    }


}
