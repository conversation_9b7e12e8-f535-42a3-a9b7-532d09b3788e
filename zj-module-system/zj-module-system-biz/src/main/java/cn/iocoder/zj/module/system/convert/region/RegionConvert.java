package cn.iocoder.zj.module.system.convert.region;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.system.api.region.dto.RegionDTO;
import cn.iocoder.zj.module.system.convert.auth.AuthConvert;
import cn.iocoder.zj.module.system.dal.dataobject.permission.MenuDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.system.controller.admin.region.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.region.RegionDO;
import org.slf4j.LoggerFactory;

import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.filterList;
import static cn.iocoder.zj.module.system.dal.dataobject.region.RegionDO.REG_ID;

/**
 * 地区 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface RegionConvert {

    RegionConvert INSTANCE = Mappers.getMapper(RegionConvert.class);

    RegionDO convert(RegionCreateReqVO bean);

    RegionDO convert(RegionUpdateReqVO bean);

    RegionRespVO convert(RegionDO bean);

    List<RegionRespVO> convertList(List<RegionDO> list);

    PageResult<RegionRespVO> convertPage(PageResult<RegionDO> page);

    List<RegionExcelVO> convertList02(List<RegionDO> list);


    RegionTreeVO convertTreeNode(RegionDO regionDO);


    default List<RegionTreeVO> buildMenuTree(List<RegionDO> menuList) {
        // 构建菜单树
        // 使用 LinkedHashMap 的原因，是为了排序 。实际也可以用 Stream API ，就是太丑了。
        Map<String, RegionTreeVO> treeNodeMap = new LinkedHashMap<>();
        menuList.forEach(reg -> treeNodeMap.put(reg.getId(), RegionConvert.INSTANCE.convertTreeNode(reg)));
        // 处理父子关系
        treeNodeMap.values().stream().filter(node -> !node.getId().equals(REG_ID)).forEach(childNode -> {
            // 获得父节点
            RegionTreeVO parentNode = treeNodeMap.get(childNode.getRegionParentId());
            if (parentNode == null) {
                LoggerFactory.getLogger(getClass()).error("[buildRouterTree][resource({}) 找不到父资源({})]",
                        childNode.getId(), childNode.getRegionParentId());
                return;
            }
            // 将自己添加到父节点中
            if (parentNode.getChildren() == null) {
                parentNode.setChildren(new ArrayList<>());
            }
            parentNode.getChildren().add(childNode);
        });

        // 获得到所有的根节点
        return filterList(treeNodeMap.values(), node -> REG_ID.equals(node.getRegionParentId()));
    }

    RegionDTO covertDtoRegion(RegionDO regionDO);
}
