package cn.iocoder.zj.module.system.dal.mysql.user;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.zj.module.system.controller.admin.permission.vo.role.RoleSimpleRespVO;
import cn.iocoder.zj.module.system.controller.admin.user.vo.user.UserExportReqVO;
import cn.iocoder.zj.module.system.controller.admin.user.vo.user.UserPageReqVO;
import cn.iocoder.zj.module.system.controller.admin.user.vo.user.UserSimpleRespVO;
import cn.iocoder.zj.module.system.dal.dataobject.user.AdminUserDO;
import jodd.util.StringUtil;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Mapper
public interface AdminUserMapper extends BaseMapperX<AdminUserDO> {

    default AdminUserDO selectByUsername(String username) {
        return selectOne(AdminUserDO::getUsername, username);
    }

    @TenantIgnore
    default AdminUserDO selectByEmail(String email) {
        return selectOne(AdminUserDO::getEmail, email,AdminUserDO::getDeleted,0);
    }

    @TenantIgnore
    default AdminUserDO selectByMobile(String mobile) {
        return selectOne(AdminUserDO::getMobile, mobile);
    }

    default PageResult<AdminUserDO> selectPage(UserPageReqVO reqVO, Collection<Long> deptIds) {


        LambdaQueryWrapperX<AdminUserDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.likeIfPresent(AdminUserDO::getNickname, reqVO.getNickname())
                .likeIfPresent(AdminUserDO::getMobile, reqVO.getMobile())
                .eqIfPresent(AdminUserDO::getStatus, reqVO.getStatus())
                .likeIfPresent(AdminUserDO::getDeptStr, reqVO.getDeptStr());
        if (reqVO.getUserIds()!= null){
            wrapper.in(AdminUserDO::getId, reqVO.getUserIds());
        }
        if(StringUtil.isNotEmpty(reqVO.getStartTime()) && StringUtil.isNotEmpty(reqVO.getEndTime())){
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            try {
                date = sdf.parse(reqVO.getEndTime());
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(date);
                // 把日期设置为当天最后一秒
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                date = calendar.getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            wrapper.betweenIfPresent(AdminUserDO::getCreateTime,reqVO.getStartTime(),sdf.format(date));
        }
        wrapper.inIfPresent(AdminUserDO::getDeptId, deptIds);

        if (StringUtil.isNotEmpty(reqVO.getSortDirection()) && reqVO.getSortDirection().equals("asc")){
            if (reqVO.getSortBy().equals("createTime")){
                wrapper.orderByAsc(AdminUserDO::getCreateTime);
            }
        }else {
            wrapper.orderByDesc(AdminUserDO::getCreateTime);
        }

        return selectPage(reqVO, wrapper);
    }

    default List<AdminUserDO> selectList(UserExportReqVO reqVO, Collection<Long> deptIds,Long id) {
        LambdaQueryWrapperX<AdminUserDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.likeIfPresent(AdminUserDO::getUsername, reqVO.getUsername())
                .likeIfPresent(AdminUserDO::getMobile, reqVO.getMobile())
                .eqIfPresent(AdminUserDO::getStatus, reqVO.getStatus());
        if(StringUtil.isNotEmpty(reqVO.getStartTime()) && StringUtil.isNotEmpty(reqVO.getEndTime())){
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            try {
                date = sdf.parse(reqVO.getEndTime());
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(date);
                // 把日期设置为当天最后一秒
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                date = calendar.getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            wrapper.betweenIfPresent(AdminUserDO::getCreateTime,reqVO.getStartTime(),sdf.format(date));
        }
        wrapper.notIn(AdminUserDO::getId,id);
        wrapper.inIfPresent(AdminUserDO::getDeptId, deptIds);
        return selectList(wrapper);
    }

    default List<AdminUserDO> selectListByNickname(String nickname) {
        return selectList(new LambdaQueryWrapperX<AdminUserDO>().like(AdminUserDO::getNickname, nickname));
    }

    default List<AdminUserDO> selectListByStatus(Integer status) {
        return selectList(AdminUserDO::getStatus, status);
    }

    default List<AdminUserDO> selectListByDeptIds(Collection<Long> deptIds) {
        return selectList(AdminUserDO::getDeptId, deptIds);
    }

    List<UserSimpleRespVO> getOperations(@Param("userId") Long userId,@Param("roleCode")String roleCode);

    List<RoleSimpleRespVO> getRoleMapByUserIds(@Param("userIds")List<Long> userIds);

    AdminUserDO getPlatformAdmin(@Param("userId")Long id);

    Long getPlatformAdminId();

    Long getCountByRoleId(@Param("id")Long id);

    List<UserSimpleRespVO> getEnforcer(Long userId);

    List<Long> getUserIdsByTenantId(@Param("userId")Long userId);

    void deleteByTenantId(@Param("tenantId")Long tenantId);


    Long getUserIdByTenantId(Long tenantId);

    List<Long> getHomologousUsers(@Param("userId")Long userId);

    List<String> getUserMenus(@Param("userId")Long userId);

    AdminUserDO selectUserById(@Param("userId")Long userId);

    List<UserSimpleRespVO> getOrderEnforcer(@Param("platformId") Long platformId);

    List<AdminUserDO> getUserByIds(@Param("ids")List<String> ids);

    int deleteByUserId(@Param("id")Long id);

    void updateOrganizationByTenantId(@Param("tenantId")Long tenantId, @Param("organization")String organization);

    Integer getAssetNum(@Param("tenantId")Long tenantId);

    Integer getContractCount(@Param("tenantId")Long tenantId);

    AdminUserDO getUserByUserId(@Param("userID") String userID);

    Integer getStateByTenantId(@Param("tenantId") Long tenantId);

    @TenantIgnore
    AdminUserRespDTO getAdminUserByUserId(@Param("userID")Long id);
}
