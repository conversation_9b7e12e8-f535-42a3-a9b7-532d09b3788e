package cn.iocoder.zj.module.system.controller.admin.devicetype;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.module.system.controller.admin.devicetype.vo.*;
import cn.iocoder.zj.module.system.convert.devicetype.DeviceTypeConvert;
import cn.iocoder.zj.module.system.dal.dataobject.devicetype.DeviceTypeDO;
import cn.iocoder.zj.module.system.service.devicetype.DeviceTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;
import static cn.iocoder.zj.module.infra.enums.ErrorCodeConstants.FILE_IS_EMPTY;

@Tag(name = "管理后台 - 资产类型")
@RestController
@RequestMapping("/system/device-type")
@Validated
public class DeviceTypeController {

    @Resource
    private DeviceTypeService deviceTypeService;

    @PostMapping("/create")
    @Operation(summary = "创建资产类型")
    @OperateLog(type=CREATE)
    @PreAuthorize("@ss.hasPermission('system:device-type:create')")
    @PermitAll
    public CommonResult<Long> createDeviceType(@Valid @RequestBody DeviceTypeCreateReqVO createReqVO) {
        return success(deviceTypeService.createDeviceType(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新资产类型")
    @OperateLog(type=UPDATE)
    @PreAuthorize("@ss.hasPermission('system:device-type:update')")
    @PermitAll
    public CommonResult<Boolean> updateDeviceType(@Valid @RequestBody DeviceTypeUpdateReqVO updateReqVO) {
        deviceTypeService.updateDeviceType(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除资产类型")
    @OperateLog(type=DELETE)
    @PreAuthorize("@ss.hasPermission('system:device-type:delete')")
    @Parameter(name = "id", description = "编号", required = true)
    @PermitAll
    public CommonResult<Boolean> deleteDeviceType(@RequestParam("id") Long id) {
        deviceTypeService.deleteDeviceType(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得资产类型")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:device-type:query')")
    public CommonResult<DeviceTypeRespVO> getDeviceType(@RequestParam("id") Long id) {
        DeviceTypeDO deviceType = deviceTypeService.getDeviceType(id);
        return success(DeviceTypeConvert.INSTANCE.convert(deviceType));
    }

    @GetMapping("/list")
    @Operation(summary = "获得资产类型列表")
    @Parameter(name = "ids", description = "编号列表", required = false, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('system:device-type:query')")
    public CommonResult<List<DeviceTypeRespVO>> getDeviceTypeList(@RequestParam(value = "ids",required = false,defaultValue = "") Collection<Long> ids) {
        List<DeviceTypeDO> list = new ArrayList<>();
        if (!ids.isEmpty()){
            list   = deviceTypeService.getDeviceTypeList(ids);
        }else {
            list = deviceTypeService.getDeviceTypeByList();
        }

        return success(DeviceTypeConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得资产类型分页")
    @PreAuthorize("@ss.hasPermission('system:device-type:query')")
    public CommonResult<PageResult<DeviceTypeRespVO>> getDeviceTypePage(@Valid DeviceTypePageReqVO pageVO) {
        PageResult<DeviceTypeDO> pageResult = deviceTypeService.getDeviceTypePage(pageVO);
        return success(DeviceTypeConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出资产类型 Excel")
    @PermitAll
    @OperateLog(type = EXPORT)
    public void exportDeviceTypeExcel(@Valid DeviceTypeExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<DeviceTypeDO> list = deviceTypeService.getDeviceTypeList(exportReqVO);
        // 导出 Excel
        List<DeviceTypeExcelVO> datas = DeviceTypeConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "资产类型.xls", "数据", DeviceTypeExcelVO.class, datas);
    }

    @GetMapping("/getDeviceSelect")
    @Operation(summary = "资产类型下拉框")
    @PermitAll
    public CommonResult<List<DeviceTypeRespVO>> getDeviceSelect() {
        List<DeviceTypeDO> list = deviceTypeService.getDeviceSelect();
        return success(DeviceTypeConvert.INSTANCE.convertList(list));
    }


    @PostMapping("/update-deviceicon")
    @Operation(summary = "上传设备图片")
    @OperateLog(type=CREATE)
    @PreAuthorize("@ss.hasPermission('system:device-type:update')")
    public CommonResult<String> deviceimp(@RequestParam("avatarFile") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw exception(FILE_IS_EMPTY);
        }
        String avatar = deviceTypeService.updateDeviceIcon(file);
        return success(avatar);
    }
}
