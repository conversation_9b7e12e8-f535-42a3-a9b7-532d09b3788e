package cn.iocoder.zj.module.system.service.mail;

import cn.iocoder.zj.module.system.dal.dataobject.mail.MailTemplateDO;
import cn.iocoder.zj.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.zj.module.system.mq.message.mail.MailSendMessage;
import cn.iocoder.zj.module.system.mq.message.platform.HtmlEmailMessage;

import java.util.Map;

/**
 * 邮件发送 Service 接口
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
public interface MailSendService {

    /**
     * 发送单条邮件给管理后台的用户
     *
     * @param mail 邮箱
     * @param userId 用户编码
     * @param templateCode 邮件模版编码
     * @param templateParams 邮件模版参数
     * @return 发送日志编号
     */
    Long sendSingleMailToAdmin(String mail, Long userId,
                               String templateCode, Map<String, Object> templateParams,String alarmMail);

    /**
     * 发送单条邮件给用户 APP 的用户
     *
     * @param mail 邮箱
     * @param userId 用户编码
     * @param templateCode 邮件模版编码
     * @param templateParams 邮件模版参数
     * @return 发送日志编号
     */
    Long sendSingleMailToMember(String mail, Long userId,
                                String templateCode, Map<String, Object> templateParams);

    /**
     * 发送单条邮件给用户
     *
     * @param mail 邮箱
     * @param userId 用户编码
     * @param userType 用户类型
     * @param templateCode 邮件模版编码
     * @param templateParams 邮件模版参数
     * @return 发送日志编号
     */
    Long sendSingleMail(String mail, Long userId, Integer userType,
                        String templateCode, Map<String, Object> templateParams,String alarmMail);

    /**
     * 执行真正的邮件发送
     * 注意，该方法仅仅提供给 MQ Consumer 使用
     *
     * @param message 邮件
     */
    void doSendMail(MailSendMessage message);

    MailTemplateDO getTemplate(Long id);

    String getTemplateByName(String name);

    void sendHtmlEmailWithImage(String username, String password, String contactEmail);

    Long sendSingleMailToBpm(String mail, Long userId, String templateCode, Map<String, Object> templateParams);

    //发送邮件到指定邮箱
    void sendMail(String email, Long tenantId,long daysUntilExpiration,String formattedExpireTime);
}
