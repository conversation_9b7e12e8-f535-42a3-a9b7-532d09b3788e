package cn.iocoder.zj.module.system.controller.admin.licence;

import cn.iocoder.zj.framework.common.pojo.PageParam;
import lombok.Data;

@Data
public class LicenseVo extends PageParam {
    private  String machineCode;
    private  String licenseCount;
    private  String appCode;
    private  String expiryDate;
    private  String startDate;
    private  String licenseUrl;
    private  String useCount;
    private Integer enable;
    private Integer version;
    private  String licenseName;
    private String usageRestrictions = "产权归属安徽中杰科技有限公司，本公司对未授权时而产生的任何损失或损害不承担责任，在何种情况下有权对许可终止";
}
