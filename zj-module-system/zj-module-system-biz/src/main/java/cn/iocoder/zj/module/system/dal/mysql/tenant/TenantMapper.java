package cn.iocoder.zj.module.system.dal.mysql.tenant;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.system.controller.admin.tenant.vo.tenant.*;
import cn.iocoder.zj.module.system.dal.dataobject.tenant.TenantDO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 租户 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantMapper extends BaseMapperX<TenantDO> {

    default PageResult<TenantDO> selectPage(TenantPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TenantDO>()
                .likeIfPresent(TenantDO::getName, reqVO.getName())
                .likeIfPresent(TenantDO::getContactName, reqVO.getContactName())
                .likeIfPresent(TenantDO::getContactMobile, reqVO.getContactMobile())
                .eqIfPresent(TenantDO::getStatus, reqVO.getStatus())
                .eqIfPresent(TenantDO::getPackageId, reqVO.getPackageId())
                .betweenIfPresent(TenantDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TenantDO::getId));
    }

    default List<TenantDO> selectList(TenantExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TenantDO>()
                .likeIfPresent(TenantDO::getName, reqVO.getName())
                .likeIfPresent(TenantDO::getContactName, reqVO.getContactName())
                .likeIfPresent(TenantDO::getContactMobile, reqVO.getContactMobile())
                .eqIfPresent(TenantDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(TenantDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TenantDO::getId));
    }

    default TenantDO selectByName(String name) {
        return selectOne(TenantDO::getName, name);
    }

    default Long selectCountByPackageId(Long packageId) {
        return selectCount(TenantDO::getPackageId, packageId);
    }

    default List<TenantDO> selectListByPackageId(Long packageId) {
        return selectList(TenantDO::getPackageId, packageId);
    }

    default List<TenantDO> selectListByStatus(Integer status){
        return selectList(TenantDO::getStatus, status);
    };

    default List<TenantDO> selectListByplatformId(Integer status){
        return selectList(TenantDO::getStatus, status);
    };

    List<TenantRespVO> getTenantPage(@Param("mpPage") IPage<TenantRespVO> mpPage, @Param("pageReqVO") TenantPageReqVO pageReqVO);

    List<TenantExcelVO> getTenantExcelList(@Param("exportReqVO") TenantExportReqVO exportReqVO,@Param("id")Long id);

    TenantRespVO getTenantById(@Param("id") Long id);

    String getTestContractNo(@Param("id") Long id);

    Long getSuperIdByUser(@Param("id") Long id);

    void updatePlatformJsonById(@Param("id") Long id, @Param("modifiedJson") String modifiedJson, @Param("platformNames") String platformNames);

    List<Map> selectUserByPlatformId(@Param("id") String id);

    void updateBatchById(@Param("list") List<Map> listTenant);

    List<TenantUserRespVO> getTenantByRole(@Param("list") List<Long> list);

    List<TenantDO> getOrderEnforcer(@Param("platformId") Long platformId);

    List<TenantUserRespVO> getTenantByPlatform(@Param("tenantId")Long tenantId);

    List<String> getTenantIdByDefine(@Param("tenantId") Long tenantId);

    void deleteBatchAlertId(@Param("list") List<String> list);

    void deleteBatchAlertBindId(@Param("list") List<String> list);

    @TenantIgnore
    Long getTenantIdByUserId(@Param("maintainer") Long maintainer);

    @TenantIgnore
    void updateCollectorInfoByCollectorId(@Param("platformId") Long platformId, @Param("platformName") String platformName, @Param("collectorId") List<String> collectorId);

    @TenantIgnore
    int selectCollectorInfo(@Param("id") Long id, @Param("collectorName") String collectorName);

    @TenantIgnore
    Map<String,String> selectCollectorName(@Param("collectorId") String collectorId);
    @TenantIgnore
    void insertCollectorInfo(@Param("platformId") Long id, @Param("platformName") String name, @Param("map") Map map);
    @TenantIgnore
    void updateCollectorInfoByCollectorIds(@Param("platformId") Long id, @Param("platformName") String name, @Param("collectorId") String collectorId);
    @TenantIgnore
    List<Map> selectCollectorInfoByPlatformId(@Param("platformId") Long platformId);

    @TenantIgnore
    void deleteCollectorByPlatformId(@Param("platformId") Long platformId);

    @TenantIgnore
    void updateByHzbCollector(@Param("id") Long id);
    @TenantIgnore
    List<String> selectHzbplatformById(@Param("id") Long id);
    @TenantIgnore
    void deleteByHzbCollector(@Param("hzb") List<String> hzb);
    @TenantIgnore
    List<Map> getPlatformByIds(@Param("ids") List<Long> ids);
    @TenantIgnore
    TenantRespVO getTenantByIdOne(@Param("id") Long id);

    @TenantIgnore
    long getStateByTenantId(@Param("tenantId") Long tenantId);
}
