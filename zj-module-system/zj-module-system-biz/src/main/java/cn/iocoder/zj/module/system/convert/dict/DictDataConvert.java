package cn.iocoder.zj.module.system.convert.dict;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.system.api.dict.dto.DictDataRespDTO;
import cn.iocoder.zj.module.system.controller.admin.dict.vo.data.*;
import cn.iocoder.zj.module.system.dal.dataobject.dict.DictDataDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface DictDataConvert {

    DictDataConvert INSTANCE = Mappers.getMapper(DictDataConvert.class);

  

    DictDataRespVO convert(DictDataDO bean);


    DictDataDO convert(DictDataUpdateReqVO bean);

    DictDataDO convert(DictDataCreateReqVO bean);

    List<DictDataExcelVO> convertList02(List<DictDataDO> bean);

    DictDataRespDTO convert02(DictDataD<PERSON> bean);


    List<DictDataSimpleRespVO> convertList(List<DictDataDO> list);

    PageResult<DictDataRespVO> convertPage(PageResult<DictDataDO> dictDataPage);
}
