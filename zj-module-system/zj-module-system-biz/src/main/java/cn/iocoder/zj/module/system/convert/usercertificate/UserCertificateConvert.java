package cn.iocoder.zj.module.system.convert.usercertificate;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.system.controller.admin.usercertificate.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.usercertificate.UserCertificateDO;

/**
 * 授权凭证 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface UserCertificateConvert {

    UserCertificateConvert INSTANCE = Mappers.getMapper(UserCertificateConvert.class);

    UserCertificateDO convert(UserCertificateCreateReqVO bean);

    UserCertificateDO convert(UserCertificateUpdateReqVO bean);

    UserCertificateRespVO convert(UserCertificateDO bean);

    List<UserCertificateRespVO> convertList(List<UserCertificateDO> list);

    PageResult<UserCertificateRespVO> convertPage(PageResult<UserCertificateDO> page);

    List<UserCertificateExcelVO> convertList02(List<UserCertificateDO> list);

    UserCertificateInfoVO convert2(UserCertificateDO userCertificate);
}
