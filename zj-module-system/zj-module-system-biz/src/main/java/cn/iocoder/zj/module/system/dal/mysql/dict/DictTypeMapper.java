package cn.iocoder.zj.module.system.dal.mysql.dict;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.module.system.controller.admin.dict.vo.type.DictTypeExportReqVO;
import cn.iocoder.zj.module.system.controller.admin.dict.vo.type.DictTypePageReqVO;
import cn.iocoder.zj.module.system.dal.dataobject.dict.DictTypeDO;
import jodd.util.StringUtil;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

@Mapper
public interface DictTypeMapper extends BaseMapperX<DictTypeDO> {

    default PageResult<DictTypeDO> selectPage(DictTypePageReqVO reqVO) {
        LambdaQueryWrapperX<DictTypeDO> dictTypeDOLambdaQueryWrapperX = new LambdaQueryWrapperX<DictTypeDO>()
                .likeIfPresent(DictTypeDO::getName, reqVO.getName())
                .likeIfPresent(DictTypeDO::getType, reqVO.getType())
                .eqIfPresent(DictTypeDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(DictTypeDO::getCreateTime, reqVO.getCreateTime());

        if (jodd.util.StringUtil.isNotEmpty(reqVO.getStartTime()) && jodd.util.StringUtil.isNotEmpty(reqVO.getEndTime())) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            try {
                date = sdf.parse(reqVO.getEndTime());
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(date);
                // 把日期设置为当天最后一秒
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                date = calendar.getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            dictTypeDOLambdaQueryWrapperX.betweenIfPresent(DictTypeDO::getCreateTime, reqVO.getStartTime(), sdf.format(date));
        }

        if (StringUtil.isNotEmpty(reqVO.getSortBy()) && reqVO.getSortDirection().equals("asc")){
            dictTypeDOLambdaQueryWrapperX.orderByAsc(DictTypeDO::getCreateTime);
        }else {
            dictTypeDOLambdaQueryWrapperX.orderByDesc(DictTypeDO::getCreateTime);
        }
        return selectPage(reqVO,dictTypeDOLambdaQueryWrapperX);
    }

    default List<DictTypeDO> selectList(DictTypeExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DictTypeDO>()
                .likeIfPresent(DictTypeDO::getName, reqVO.getName())
                .likeIfPresent(DictTypeDO::getType, reqVO.getType())
                .eqIfPresent(DictTypeDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(DictTypeDO::getCreateTime, reqVO.getCreateTime()));
    }

    default DictTypeDO selectByType(String type) {
        return selectOne(DictTypeDO::getType, type,DictTypeDO::getDeleted,0);
    }

    default DictTypeDO selectByName(String name) {
        return selectOne(DictTypeDO::getName, name,DictTypeDO::getDeleted,0);
    }

    int deleteById(@Param("id") Long id, @Param("deletedTime") LocalDateTime deletedTime);

    @Update("UPDATE system_dict_type SET deleted = 1, deleted_time = #{deletedTime} WHERE id = #{id}")
    void updateToDelete(@Param("id") Long id, @Param("deletedTime") LocalDateTime deletedTime);

     default List<DictTypeDO> getDicTypeList(String type){
         return selectList(new LambdaQueryWrapperX<DictTypeDO>()
                 .likeIfPresent(DictTypeDO::getType,type)
                 .eqIfPresent(DictTypeDO::getStatus, 0)
         );
     }
    @Delete("DELETE FROM system_dict_type WHERE id = #{id}")
    void deleteDictType(@Param("id") Long id);
}
