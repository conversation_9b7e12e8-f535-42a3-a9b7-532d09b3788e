package cn.iocoder.zj.module.system.api.deviceoid;

import cn.iocoder.zj.module.system.api.deviceoid.dto.DeviceOidDTO;
import cn.iocoder.zj.module.system.service.deviceoid.DeviceOidService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

/**
 * @ClassName : DeviceOidImpl  //类名
 * @Description : 资产oid实现类  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/8/14  16:55
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class DeviceOidImpl {

}
