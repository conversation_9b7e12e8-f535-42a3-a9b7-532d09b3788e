package cn.iocoder.zj.module.system.controller.admin.devicesys.vo;

import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 系统类型 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class DeviceSysExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("系统类型")
    private String sysType;

    @ExcelProperty("系统类型名称")
    private String sysName;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private Date createTime;

}
