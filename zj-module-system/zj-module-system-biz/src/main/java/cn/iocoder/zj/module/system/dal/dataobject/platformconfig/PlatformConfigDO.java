package cn.iocoder.zj.module.system.dal.dataobject.platformconfig;

import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * 平台配置 DO
 *
 * <AUTHOR>
 */
@TableName("system_platform_config")
@KeySequence("system_platform_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlatformConfigDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 用户账号
     */
    private String username;
    /**
     * 密码
     */
    private String password;
    /**
     * 平台地址
     */
    private String url;
    /**
     * 平台名称
     */
    private String name;

    private String typeName;

    private String typeCode;

    private Long tenantId;

    private Long regionId;

    private String regionName;

    private String tenantName;
    /**
     * 机房位置
     */
    private String address;

    /**
     * @description: 网页地址
     * <AUTHOR>
     * @date 2024/4/15 13:08
     * @version 1.0
     */
    private String urlHttp;

    private Integer consoleProd;

    private String consoleIp;

    private Integer akType;

    @TableField(exist = false)
    private String collectorName;
    @TableField(exist = false)
    private String collectorId;

    private Long state;

    private Long diffTime;

    private Date onlineTime;
}
