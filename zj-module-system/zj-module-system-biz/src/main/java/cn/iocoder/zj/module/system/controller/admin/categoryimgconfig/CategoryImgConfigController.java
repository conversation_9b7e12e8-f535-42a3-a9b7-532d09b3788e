package cn.iocoder.zj.module.system.controller.admin.categoryimgconfig;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.system.controller.admin.categoryimgconfig.vo.*;
import cn.iocoder.zj.module.system.convert.categoryimgconfig.CategoryImgConfigConvert;
import cn.iocoder.zj.module.system.dal.dataobject.categoryimgconfig.CategoryImgConfigDO;
import cn.iocoder.zj.module.system.service.categoryimgconfig.CategoryImgConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.CREATE;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.zj.module.om.enums.ErrorCodeConstants.FILE_IS_EMPTY;

@Tag(name = "管理后台 - 类别图片管理")
@RestController
@RequestMapping("/system/category-img-config")
@Validated
public class CategoryImgConfigController {

    @Resource
    private CategoryImgConfigService categoryImgConfigService;

    @PostMapping("/create")
    @Operation(summary = "创建类别图片管理")
    @PreAuthorize("@ss.hasPermission('system:category-img-config:create')")
    @TenantIgnore
    public CommonResult<Long> createCategoryImgConfig(@Valid @RequestBody CategoryImgConfigCreateReqVO createReqVO) {
        return success(categoryImgConfigService.createCategoryImgConfig(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新类别图片管理")
    @PreAuthorize("@ss.hasPermission('system:category-img-config:update')")
    @TenantIgnore
    public CommonResult<Boolean> updateCategoryImgConfig(@Valid @RequestBody CategoryImgConfigUpdateReqVO updateReqVO) {
        categoryImgConfigService.updateCategoryImgConfig(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除类别图片管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:category-img-config:delete')")
    @TenantIgnore
    public CommonResult<Boolean> deleteCategoryImgConfig(@RequestParam("id") Long id) {
        categoryImgConfigService.deleteCategoryImgConfig(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得类别图片管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @TenantIgnore
    public CommonResult<CategoryImgConfigRespVO> getCategoryImgConfig(@RequestParam("id") Long id) {
        CategoryImgConfigDO categoryImgConfig = categoryImgConfigService.getCategoryImgConfig(id);
        return success(CategoryImgConfigConvert.INSTANCE.convert(categoryImgConfig));
    }

    @GetMapping("/list")
    @Operation(summary = "获得类别图片管理列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @TenantIgnore
    public CommonResult<List<CategoryImgConfigRespVO>> getCategoryImgConfigList(@RequestParam("ids") Collection<Long> ids) {
        List<CategoryImgConfigDO> list = categoryImgConfigService.getCategoryImgConfigList(ids);
        return success(CategoryImgConfigConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得类别图片管理分页")
    @TenantIgnore
    public CommonResult<PageResult<CategoryImgConfigRespVO>> getCategoryImgConfigPage(@Valid CategoryImgConfigPageReqVO pageVO) {
        PageResult<CategoryImgConfigDO> pageResult = categoryImgConfigService.getCategoryImgConfigPage(pageVO);
        return success(CategoryImgConfigConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/base-list")
    @Operation(summary = "获得类别图片管理列表")
    @TenantIgnore
    public CommonResult<List<CategoryImgConfigReqVO>> getCategoryImgConfigPage(@Valid CategoryImgConfigReqVO reqVO) {
        List<CategoryImgConfigDO> list = categoryImgConfigService.getCategoryImgConfigList(reqVO);
        return success(BeanUtil.copyToList(list, CategoryImgConfigReqVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出类别图片管理 Excel")
    @PreAuthorize("@ss.hasPermission('system:category-img-config:export')")
    @OperateLog(type = EXPORT)
    @TenantIgnore
    public void exportCategoryImgConfigExcel(@Valid CategoryImgConfigExportReqVO exportReqVO,
                                             HttpServletResponse response) throws IOException {
        List<CategoryImgConfigDO> list = categoryImgConfigService.getCategoryImgConfigList(exportReqVO);
        // 导出 Excel
        List<CategoryImgConfigExcelVO> datas = CategoryImgConfigConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "类别图片管理.xls", "数据", CategoryImgConfigExcelVO.class, datas);
    }

    @PostMapping("/upload-category-img")
    @Operation(summary = "上传类别图片")
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('system:category-img-config:create')")
    @TenantIgnore
    public CommonResult<String> uploadCategoryImg(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            throw exception(FILE_IS_EMPTY);
        }
        return success(categoryImgConfigService.uploadCategoryImg(file));
    }

}
