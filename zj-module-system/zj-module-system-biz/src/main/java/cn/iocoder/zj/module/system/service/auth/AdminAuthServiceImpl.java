package cn.iocoder.zj.module.system.service.auth;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.enums.UserTypeEnum;
import cn.iocoder.zj.framework.common.exception.ErrorCode;
import cn.iocoder.zj.framework.common.pojo.UserBindDTO;
import cn.iocoder.zj.framework.common.util.dingtalk.DingTalkUtil;
import cn.iocoder.zj.framework.common.util.dingtalk.QyWxUtils;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.framework.common.util.monitor.TracerUtils;
import cn.iocoder.zj.framework.common.util.servlet.ServletUtils;
import cn.iocoder.zj.framework.common.util.validation.ValidationUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.system.api.logger.dto.LoginLogCreateReqDTO;
import cn.iocoder.zj.module.system.api.sms.SmsCodeApi;
import cn.iocoder.zj.module.system.api.social.dto.SocialUserBindReqDTO;
import cn.iocoder.zj.module.system.controller.admin.auth.vo.*;
import cn.iocoder.zj.module.system.convert.auth.AuthConvert;
import cn.iocoder.zj.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import cn.iocoder.zj.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.zj.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.zj.module.system.dal.mysql.info.InfoMapper;
import cn.iocoder.zj.module.system.enums.logger.LoginLogTypeEnum;
import cn.iocoder.zj.module.system.enums.logger.LoginResultEnum;
import cn.iocoder.zj.module.system.enums.oauth2.OAuth2ClientConstants;
import cn.iocoder.zj.module.system.service.logger.LoginLogService;
import cn.iocoder.zj.module.system.service.member.MemberService;
import cn.iocoder.zj.module.system.service.oauth2.OAuth2TokenService;
import cn.iocoder.zj.module.system.service.permission.PermissionService;
import cn.iocoder.zj.module.system.service.permission.RoleService;
import cn.iocoder.zj.module.system.service.social.SocialUserService;
import cn.iocoder.zj.module.system.service.tenant.TenantService;
import cn.iocoder.zj.module.system.service.user.AdminUserService;
import cn.iocoder.zj.module.system.service.wechatbinding.WechatBindingService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiSnsGetuserinfoBycodeRequest;
import com.dingtalk.api.request.OapiUserGetbyunionidRequest;
import com.dingtalk.api.request.OapiV2UserGetRequest;
import com.dingtalk.api.response.OapiSnsGetuserinfoBycodeResponse;
import com.dingtalk.api.response.OapiUserGetbyunionidResponse;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.google.common.annotations.VisibleForTesting;
import com.google.gson.Gson;
import com.taobao.api.ApiException;
import com.xingyuv.captcha.model.common.ResponseModel;
import com.xingyuv.captcha.model.vo.CaptchaVO;
import com.xingyuv.captcha.service.CaptchaService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.internal.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.Validator;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.system.enums.ErrorCodeConstants.*;

/**
 * Auth Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AdminAuthServiceImpl implements AdminAuthService {

    @Resource
    private AdminUserService userService;
    @Resource
    private RoleService roleService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private TenantService tenantService;
    @Resource
    private LoginLogService loginLogService;
    @Resource
    private OAuth2TokenService oauth2TokenService;
    @Resource
    private SocialUserService socialUserService;
    @Resource
    private MemberService memberService;
    @Resource
    private Validator validator;
    @Resource
    private CaptchaService captchaService;
    @Resource
    private SmsCodeApi smsCodeApi;
    @Resource
    private PasswordEncoder passwordEncoder;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private WechatBindingService wechatBindingService;
    @Resource
    private RedisTemplate redisTemplate;
    @Autowired
    InfoMapper infoMapper;

    @Value("${aliyun.sms.access_key_id}")
    private String accessKeyId;

    @Value("${aliyun.sms.access_key_secret}")
    private String accessKeySecret;

    @Value("${aliyun.sms.sign_name}")
    private String signName;

    @Value("${aliyun.sms.template_code}")
    private String templateCode;

    /**
     * 验证码的开关，默认为 true
     */
    @Value("${zj.captcha.enable:true}")
    private Boolean captchaEnable;

    @Override
    public AdminUserDO authenticate(String username, String password) {
        final LoginLogTypeEnum logTypeEnum = LoginLogTypeEnum.LOGIN_USERNAME;
        // 校验账号是否存在
        AdminUserDO user = userService.getUserByUsername(username);
        if (user == null) {
            createLoginLog(null, username, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
        }
        if (!userService.isPasswordMatch(password, user.getPassword())) {
            createLoginLog(user.getId(), username, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
        }
        // 校验是否禁用
        if (ObjectUtil.notEqual(user.getStatus(), CommonStatusEnum.ENABLE.getStatus())) {
            createLoginLog(user.getId(), username, logTypeEnum, LoginResultEnum.USER_DISABLED);
            throw exception(AUTH_LOGIN_USER_DISABLED);
        }
        return user;
    }

    @Override
    public AuthLoginRespVO login(AuthLoginReqVO reqVO) {
        final LoginLogTypeEnum logTypeEnum = LoginLogTypeEnum.LOGIN_USERNAME;
        // 校验验证码
        validateCaptcha(reqVO);

        AdminUserDO user = new AdminUserDO();
        boolean isMatch = ReUtil.contains("@", reqVO.getUsername());
        // 如果是邮箱
        if (isMatch) {
            user = userService.getUserByMail(reqVO.getUsername());
        } else {
            // 手机号
            user = userService.getUserByMobile(reqVO.getUsername());
        }
        if (user == null) {
            createLoginLog(null, reqVO.getUsername(), logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
        }
        //账户已锁定
        if (user.getLocking()==1){
            //查找redis中对应的key是否过期，如果过期说明时间已到
            if (!stringRedisTemplate.hasKey("login_fail_"+user.getMobile())){
                //将锁定解除
                user.setLocking(0);
                userService.updateById(user);
            }else {
                throw exception(new ErrorCode(1002000008, "当前密码5次输入错误，请联系管理员进行重置"));
            }
        }
        if (!userService.isPasswordMatch(reqVO.getPassword(), user.getPassword())) {
            createLoginLog(user.getId(), reqVO.getUsername(), logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);

            // 获取当前用户名在 Redis 中的剩余尝试次数
            final int maxAttempts = 4;
            Integer attemptsLeft = maxAttempts;

            if (stringRedisTemplate.hasKey("login_fail_"+user.getMobile())) {
                attemptsLeft = Integer.valueOf(stringRedisTemplate.opsForValue().get("login_fail_"+user.getMobile()));
                if (attemptsLeft <= 1) {
                    if (user.getLocking()==0){
                        user.setLocking(1);
                        userService.updateById(user);
                    }
                    throw exception(new ErrorCode(1002000008, "当前密码5次输入错误，请联系管理员进行重置"));
                } else {
                    attemptsLeft--;
                    stringRedisTemplate.opsForValue().set("login_fail_" + user.getMobile(), attemptsLeft.toString(), 1, TimeUnit.DAYS);
                    throw exception(new ErrorCode(1002000008, "当前登录密码输入错误，还可尝试" + attemptsLeft + "次"));
                }
            } else {
                stringRedisTemplate.opsForValue().set("login_fail_" + user.getMobile(), attemptsLeft.toString(), 1, TimeUnit.DAYS);
            }

            throw exception(new ErrorCode(1002000008, "当前登录密码输入错误，还可尝试" + attemptsLeft + "次"));
        }
        // 校验是否禁用
        if (ObjectUtil.notEqual(user.getStatus(), CommonStatusEnum.ENABLE.getStatus())) {
            createLoginLog(user.getId(), reqVO.getUsername(), logTypeEnum, LoginResultEnum.USER_DISABLED);
            throw exception(AUTH_LOGIN_USER_DISABLED);
        }
        TenantDO tenantDO = tenantService.getTenant(user.getTenantId());
        //判断登录用户是否是超管下的用户，如果是，不验证资产数量
        Set<Long> roleIds = permissionService.getUserRoleIdListByUserId(tenantDO.getContactUserId());
        if(!roleService.hasAnySuperAdmin(roleIds)) {
            //查询未过期的合同数
            Integer count = userService.getContractCount(user.getTenantId());
            if (count==0){
                throw exception(HAS_EXPIRED_CONTRACT);
            }
            Integer assetNum = userService.getAssetNum(user.getTenantId());
            //如果不是，则验证是否有资产数量
            if(ObjectUtil.isEmpty(assetNum) || assetNum == 0){
                throw exception(HAS_NO_ENOUGH_ASSET_NUM);
            }
        }
        // 如果 socialType 非空，说明需要绑定社交用户
        if (reqVO.getSocialType() != null) {
            socialUserService.bindSocialUser(new SocialUserBindReqDTO(user.getId(), getUserType().getValue(),
                    reqVO.getSocialType(), reqVO.getSocialCode(), reqVO.getSocialState()));
        }
        // 创建 Token 令牌，记录登录日志
        //将redis中的错误登录记录删除
        stringRedisTemplate.delete("login_fail_"+reqVO.getUsername());
        return createTokenAfterLoginSuccess(user.getId(), user.getNickname(), LoginLogTypeEnum.LOGIN_USERNAME);
    }

    @Override
    public Boolean sendSmsCode(AuthSmsSendReqVO reqVO) {
//        // 登录场景，验证是否存在
//        if (userService.getUserByMobile(reqVO.getMobile()) == null) {
//            throw exception(AUTH_MOBILE_NOT_EXISTS);
//        }
//        // 发送验证码
//        smsCodeApi.sendSmsCode(AuthConvert.INSTANCE.convert(reqVO).setCreateIp(getClientIP()));
        //判断验证码是否存在
        if (stringRedisTemplate.hasKey(reqVO.getMobile())){
            throw exception(SMS_CODE_SEND_TOO_FAST);
        }
        Random random = new Random();
        StringBuilder code = new StringBuilder();

        for (int i = 0; i < 4; i++) {
            int digit = random.nextInt(10);
            code.append(digit);
        }

        //判断是否发送成功
        if (reqVO.getScene()==1){
            //登录验证码
            boolean flag = sendSms(reqVO.getMobile(),code.toString());
            if (flag){
                //将验证码存入redis,时效为一分钟
                stringRedisTemplate.opsForValue().set("loging_sms_"+reqVO.getMobile(),code.toString(),1, TimeUnit.MINUTES);
            }else {
                throw exception(SMS_CODE_REQUEST_FAIL);
            }
        }else if (reqVO.getScene()==2){
            //找回密码验证码
            boolean flag = sendSms(reqVO.getMobile(),code.toString());
            if (flag){
                //将验证码存入redis,时效为三分钟
                stringRedisTemplate.opsForValue().set("retrieve_sms_"+reqVO.getMobile(),code.toString(),3, TimeUnit.MINUTES);
            }else {
                throw exception(SMS_CODE_REQUEST_FAIL);
            }
        }

        return true;
    }

    @Override
    @OperateLog
    public Boolean resetPassword(AuthResetPasswordReqVO reqVO) {
        if (!stringRedisTemplate.hasKey("retrieve_sms_"+reqVO.getMobile())){
            throw exception(SMS_CODE_EXPIRED);
        }else {
            //验证码存在
            String code = stringRedisTemplate.opsForValue().get("retrieve_sms_" + reqVO.getMobile());
            if (StringUtil.isBlank(code) || !reqVO.getCode().equals(code)){
                throw exception(SMS_CODE_NOT_CORRECT);
            }
        }
        // 获得用户信息
        AdminUserDO user = userService.getUserByMobile(reqVO.getMobile());
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }
        String password = passwordEncoder.encode(reqVO.getPassword());
        user.setPassword(password);
        user.setLocking(0);
        userService.updateById(user);
        //取消锁定
        stringRedisTemplate.delete("login_fail_"+reqVO.getMobile());
        //添加日志

        return true;
    }

//    @Override
//    @TenantIgnore
//    public AuthLoginRespVO publicLogin(String code) {
//        log.info("code："+code);
//        if (stringRedisTemplate.hasKey("wechat:"+code)){
//            String json = stringRedisTemplate.opsForValue().get("wechat:" + code);
//            stringRedisTemplate.delete("wechat:" + code);
//            return new Gson().fromJson(json, AuthLoginRespVO.class);
//        }
//        //通过code获取绑定的userId
//        Long userId=wechatBindingService.getUserIdByCode(code);
//        AdminUserDO user = userService.getUser(userId);
//        log.info("查询到user:"+user);
//        AuthLoginRespVO tokenAfterLoginSuccess = createTokenAfterLoginSuccess(user.getId(), user.getNickname(), LoginLogTypeEnum.LOGIN_USERNAME);
//        log.info("获取的token:"+tokenAfterLoginSuccess);
//        Gson gson = new Gson();
//        String json = gson.toJson(tokenAfterLoginSuccess);
//        stringRedisTemplate.opsForValue().set("wechat:"+code,json,2,TimeUnit.MINUTES);
//        return tokenAfterLoginSuccess;
//    }

    @Override
    @TenantIgnore
    public AuthLoginRespVO publicLogin(String code) {
        log.info("code：" + code);

        // 如果 code 为空，直接返回错误
        if (code == null || code.trim().isEmpty()) {
            log.warn("无效的 code，无法处理请求。");
            return null;
        }

        // 锁的键
        String lockKey = "lock:wechat:" + code;

        // 尝试获取锁，设置过期时间为10秒
        Boolean lockAcquired = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, "locked", 10, TimeUnit.SECONDS);

        if (Boolean.TRUE.equals(lockAcquired)) {
            try {
                // 如果 Redis 中存在该 code
                if (stringRedisTemplate.hasKey("wechat:" + code)) {
                    String jsonStr = stringRedisTemplate.opsForValue().get("wechat:" + code);
                    stringRedisTemplate.delete("wechat:" + code);

                    // 使用FastJSON解析JSON字符串
                    return JSON.parseObject(jsonStr, AuthLoginRespVO.class);
                }

                // 通过 code 获取绑定的 userId
                Long userId = wechatBindingService.getUserIdByCode(code);
                AdminUserDO user = userService.getUser(userId);
                log.info("查询到user:" + user);

                AuthLoginRespVO tokenAfterLoginSuccess = createTokenAfterLoginSuccess(user.getId(), user.getNickname(), LoginLogTypeEnum.LOGIN_USERNAME);
                log.info("获取的token:" + tokenAfterLoginSuccess);

                // 使用FastJSON序列化对象
                String json = JSON.toJSONString(tokenAfterLoginSuccess, SerializerFeature.WriteDateUseDateFormat);
                stringRedisTemplate.opsForValue().set("wechat:" + code, json, 2, TimeUnit.MINUTES);

                return tokenAfterLoginSuccess;
            } finally {
                // 释放锁
                stringRedisTemplate.delete(lockKey);
            }
        } else {
            // 如果锁未获取，说明该 code 被其他请求占用
            log.warn("请求太频繁，请稍后重试。");
            throw new RuntimeException("请求太频繁，请稍后重试。");
        }
    }

    @Override
    public AuthLoginRespVO scanQRLogin(Map<String, String> requestBody) throws ApiException {
        final LoginLogTypeEnum logTypeEnum = LoginLogTypeEnum.LOGIN_USERNAME;
        String state = requestBody.get("state");
        String code = requestBody.get("code");
        if(!redisTemplate.hasKey("app_key_secret:app")){
            throw exception(USER_APP_NOT_EXISTS);
        }
        UserBindDTO userBindDTO =
                JsonUtils.parseObject(redisTemplate.opsForValue().get("app_key_secret:app").toString(),
                        UserBindDTO.class);
        if (state.equals("1")) {
            String accessToken = getAccessToken(userBindDTO);
            String unionid = getUnionId(code, userBindDTO, accessToken);

            if (StringUtils.isNotEmpty(unionid)) {
                String userid = getUserIdByUnionId(unionid, accessToken);
                return processUser(userid, logTypeEnum,accessToken);
            }
        }else {
            String token = QyWxUtils.getToken(userBindDTO.getWxCorpid(), userBindDTO.getWxCorpsecret());
            String userID = QyWxUtils.getUserid(token, code);
            if(StringUtils.isNotEmpty(userID)){
                AdminUserDO user = userService.getUserByUserId(userID);
                if(BeanUtil.isNotEmpty(user)){
                    if (ObjectUtil.notEqual(user.getStatus(), CommonStatusEnum.ENABLE.getStatus())) {
                        createLoginLog(user.getId(), user.getNickname(), logTypeEnum, LoginResultEnum.USER_DISABLED);
                        throw exception(AUTH_LOGIN_USER_DISABLED);
                    }
//                    if (redisTemplate.hasKey("license_access")) {
//                        InfoDO infoDO = JsonUtils.parseObject(redisTemplate.opsForValue().get("license_access").toString(),InfoDO.class);
//                        if(infoDO.getSubCount() < 0){
//                            throw exception(COUNT_NOT_EXISTS);
//                        }
//
//                        if(new Date().getTime() > infoDO.getExpiryDate().atZone(ZoneId.systemDefault()).toEpochSecond()){
//                            throw exception(EXPIRY_NOT_EXISTS);
//                        }
//                    }
                    return createTokenAfterLoginSuccess(user.getId(), user.getNickname(), logTypeEnum);
                }else {
                    throw exception(PHONE_QY_NOT_EXISTS);
                }
            }else {
                throw exception(USER_QY_NOT_EXISTS);
            }
        }
        return null;
    }

    private String getAccessToken(UserBindDTO userBindDTO) throws ApiException {
        return DingTalkUtil.getAccessToken(userBindDTO.getDingtalkAppKey(), userBindDTO.getDingtalkAppSecret());
    }

    private String getUnionId(String code, UserBindDTO userBindDTO, String accessToken) throws ApiException {
        DefaultDingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/sns/getuserinfo_bycode");
        OapiSnsGetuserinfoBycodeRequest req = new OapiSnsGetuserinfoBycodeRequest();
        req.setTmpAuthCode(code);
        OapiSnsGetuserinfoBycodeResponse response = client.execute(req, userBindDTO.getDingtalkAppKey(), userBindDTO.getDingtalkAppSecret());
        return response.getUserInfo() != null ? response.getUserInfo().getUnionid() : null;
    }

    private String getUserIdByUnionId(String unionid, String accessToken) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/user/getbyunionid");
        OapiUserGetbyunionidRequest req = new OapiUserGetbyunionidRequest();
        req.setUnionid(unionid);
        OapiUserGetbyunionidResponse response = client.execute(req, accessToken);
        return response.getResult() != null ? response.getResult().getUserid() : null;
    }

    private AuthLoginRespVO processUser(String userid, LoginLogTypeEnum logTypeEnum,String accessToken) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/get");
        OapiV2UserGetRequest req = new OapiV2UserGetRequest();
        req.setUserid(userid);
        req.setLanguage("zh_CN");
        OapiV2UserGetResponse response = client.execute(req, accessToken);

        if ("ok".equals(response.getErrmsg()) && BeanUtil.isNotEmpty(response.getResult().getMobile())) {
            AdminUserDO user = userService.getUserByMobile(response.getResult().getMobile());
            if (user == null) {
                throw exception(USER_NOT_EXISTS);
            }

            if (ObjectUtil.notEqual(user.getStatus(), CommonStatusEnum.ENABLE.getStatus())) {
                createLoginLog(user.getId(), user.getNickname(), logTypeEnum, LoginResultEnum.USER_DISABLED);
                throw exception(AUTH_LOGIN_USER_DISABLED);
            }

//            if (redisTemplate.hasKey("license_access")) {
//                InfoDO infoDO = JsonUtils.parseObject(redisTemplate.opsForValue().get("license_access").toString(),InfoDO.class);
//                if(infoDO.getSubCount() < 0){
//                    throw exception(COUNT_NOT_EXISTS);
//                }
//
//                if(new Date().getTime() > infoDO.getExpiryDate().atZone(ZoneId.systemDefault()).toEpochSecond()){
//                    throw exception(EXPIRY_NOT_EXISTS);
//                }
//            }
            return createTokenAfterLoginSuccess(user.getId(), user.getNickname(), logTypeEnum);
        }
        return null;
    }

    @Override
    public AuthLoginRespVO smsLogin(AuthSmsLoginReqVO reqVO) {
        // 校验验证码
//        smsCodeApi.useSmsCode(AuthConvert.INSTANCE.convert(reqVO, SmsSceneEnum.ADMIN_MEMBER_LOGIN.getScene(), getClientIP()));
        if (!stringRedisTemplate.hasKey("loging_sms_"+reqVO.getMobile())){
            throw exception(SMS_CODE_EXPIRED);
        }else {
            //验证码存在
            String code = stringRedisTemplate.opsForValue().get("loging_sms_" + reqVO.getMobile());
            if (StringUtil.isBlank(code) || !reqVO.getCode().equals(code)){
                throw exception(SMS_CODE_NOT_CORRECT);
            }
        }

        // 获得用户信息
        AdminUserDO user = userService.getUserByMobile(reqVO.getMobile());
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }

        // 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(user.getId(), user.getNickname(), LoginLogTypeEnum.LOGIN_MOBILE);
    }
    @Override
    public void createLoginLog(Long userId, String username,
                               LoginLogTypeEnum logTypeEnum, LoginResultEnum loginResult) {
        // 插入登录日志
        LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
        reqDTO.setLogType(logTypeEnum.getType());
        reqDTO.setTraceId(TracerUtils.getTraceId());
        reqDTO.setUserId(userId);
        reqDTO.setUserType(getUserType().getValue());
        reqDTO.setUsername(username);
        reqDTO.setUserAgent(ServletUtils.getUserAgent());
        reqDTO.setUserIp(ServletUtils.getClientIP());
        reqDTO.setResult(loginResult.getResult());
        loginLogService.createLoginLog(reqDTO);
        // 更新最后登录时间
        if (userId != null && Objects.equals(LoginResultEnum.SUCCESS.getResult(), loginResult.getResult())) {
            userService.updateUserLogin(userId, ServletUtils.getClientIP());
        }
    }

    @Override
    public AuthLoginRespVO socialLogin(AuthSocialLoginReqVO reqVO) {
        // 使用 code 授权码，进行登录。然后，获得到绑定的用户编号
        Long userId = socialUserService.getBindUserId(UserTypeEnum.ADMIN.getValue(), reqVO.getType(),
                reqVO.getCode(), reqVO.getState());
        if (userId == null) {
            throw exception(AUTH_THIRD_LOGIN_NOT_BIND);
        }

        // 获得用户
        AdminUserDO user = userService.getUser(userId);
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }

        // 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(user.getId(), user.getNickname(), LoginLogTypeEnum.LOGIN_SOCIAL);
    }

    @VisibleForTesting
    void validateCaptcha(AuthLoginReqVO reqVO) {
        // 如果验证码关闭，则不进行校验
        if (!captchaEnable) {
            return;
        }
        // 校验验证码
        ValidationUtils.validate(validator, reqVO, AuthLoginReqVO.CodeEnableGroup.class);
        CaptchaVO captchaVO = new CaptchaVO();
        captchaVO.setCaptchaVerification(reqVO.getCaptchaVerification());
        ResponseModel response = captchaService.verification(captchaVO);
        // 验证不通过
        if (!response.isSuccess()) {
            // 创建登录失败日志（验证码不正确)
            createLoginLog(null, reqVO.getUsername(), LoginLogTypeEnum.LOGIN_USERNAME, LoginResultEnum.CAPTCHA_CODE_ERROR);
            throw exception(AUTH_LOGIN_CAPTCHA_CODE_ERROR, response.getRepMsg());
        }
    }

    private AuthLoginRespVO createTokenAfterLoginSuccess(Long userId, String username, LoginLogTypeEnum logType) {
        // 插入登陆日志
        createLoginLog(userId, username, logType, LoginResultEnum.SUCCESS);
        // 创建访问令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.createAccessToken(userId, getUserType().getValue(),
                OAuth2ClientConstants.CLIENT_ID_DEFAULT, null);
        // 构建返回结果
        return AuthConvert.INSTANCE.convert(accessTokenDO);
    }

    @Override
    public AuthLoginRespVO refreshToken(String refreshToken) {
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.refreshAccessToken(refreshToken, OAuth2ClientConstants.CLIENT_ID_DEFAULT);
        return AuthConvert.INSTANCE.convert(accessTokenDO);
    }

    @Override
    public void logout(String token, Integer logType) {
        // 删除访问令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.removeAccessToken(token);
        if (accessTokenDO == null) {
            return;
        }
        // 删除成功，则记录登出日志
        createLogoutLog(accessTokenDO.getUserId(), accessTokenDO.getUserType(), logType);
    }

    private void createLogoutLog(Long userId, Integer userType, Integer logType) {
        LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
        reqDTO.setLogType(logType);
        reqDTO.setTraceId(TracerUtils.getTraceId());
        reqDTO.setUserId(userId);
        reqDTO.setUserType(userType);
        reqDTO.setUsername(getNickname(userId));
        reqDTO.setUserAgent(ServletUtils.getUserAgent());
        reqDTO.setUserIp(ServletUtils.getClientIP());
        reqDTO.setResult(LoginResultEnum.SUCCESS.getResult());
        loginLogService.createLoginLog(reqDTO);
    }

    private String getNickname(Long userId) {
        if (userId == null) {
            return null;
        }
        AdminUserDO user = userService.getUser(userId);
        return user != null ? user.getNickname() : null;
    }

    private UserTypeEnum getUserType() {
        return UserTypeEnum.ADMIN;
    }

    @Override
    public Boolean firstLogin(Long userId) {
        AdminUserDO adminUserDO = userService.getUser(userId);
        return adminUserDO.getFirstLogin()>0?true:false;
    }


    public boolean sendSms(String phoneNumber,String code) {
        DefaultProfile profile = DefaultProfile.getProfile(
                "cn-hu", accessKeyId, accessKeySecret);
        IAcsClient client = new DefaultAcsClient(profile);
        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysDomain("dysmsapi.aliyuncs.com");
        request.setSysVersion("2017-05-25");
        request.setSysAction("SendSms");

        request.putQueryParameter("PhoneNumbers", phoneNumber);
        request.putQueryParameter("SignName", signName);
        request.putQueryParameter("TemplateCode", templateCode);

        Map<String, String> templateParam = new HashMap<>();
        templateParam.put("code", code);
        request.putQueryParameter("TemplateParam", new Gson().toJson(templateParam));

        try {
            CommonResponse response = client.getCommonResponse(request);
            System.out.println(response.getData());
            // 解析响应，判断短信是否发送成功
            return response.getHttpResponse().isSuccess();
        } catch (ClientException e) {
            e.printStackTrace();
            return false;
        }
    }
}
