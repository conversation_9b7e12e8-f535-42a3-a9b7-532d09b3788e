package cn.iocoder.zj.module.system.dal.mysql.platformtenant;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.module.system.controller.admin.platformtenant.vo.PlatformTenantExportReqVO;
import cn.iocoder.zj.module.system.controller.admin.platformtenant.vo.PlatformTenantPageReqVO;
import cn.iocoder.zj.module.system.dal.dataobject.platformtenant.PlatformTenantDO;
import jodd.util.StringUtil;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

/**
 * 平台租户关系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PlatformTenantMapper extends BaseMapperX<PlatformTenantDO> {

    default PageResult<PlatformTenantDO> selectPage(PlatformTenantPageReqVO reqVO) {
        LambdaQueryWrapperX<PlatformTenantDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(PlatformTenantDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(PlatformTenantDO::getPlatformName, reqVO.getPlatformName())
                .likeIfPresent(PlatformTenantDO::getTenantName, reqVO.getTenantName());
        if(StringUtil.isNotEmpty(reqVO.getStartTime()) && StringUtil.isNotEmpty(reqVO.getEndTime())){
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            try {
                date = sdf.parse(reqVO.getEndTime());
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(date);
                // 把日期设置为当天最后一秒
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                date = calendar.getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            wrapper.betweenIfPresent(PlatformTenantDO::getCreateTime,reqVO.getStartTime(),sdf.format(date));
        }
        wrapper.orderByDesc(PlatformTenantDO::getId);
        return selectPage(reqVO, wrapper);
    }

    default List<PlatformTenantDO> selectList(PlatformTenantExportReqVO reqVO) {
        LambdaQueryWrapperX<PlatformTenantDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(PlatformTenantDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(PlatformTenantDO::getPlatformName, reqVO.getPlatformName())
                .likeIfPresent(PlatformTenantDO::getTenantName, reqVO.getTenantName());
        if(StringUtil.isNotEmpty(reqVO.getStartTime()) && StringUtil.isNotEmpty(reqVO.getEndTime())){
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            try {
                date = sdf.parse(reqVO.getEndTime());
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(date);
                // 把日期设置为当天最后一秒
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                date = calendar.getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            wrapper.betweenIfPresent(PlatformTenantDO::getCreateTime,reqVO.getStartTime(),sdf.format(date));
        }
        wrapper.orderByDesc(PlatformTenantDO::getId);
        return selectList(wrapper);
    }

    default List<PlatformTenantDO> selectListByPlatformId(Long platformId) {
        return selectList(PlatformTenantDO::getPlatformId, platformId);
    }

    void deleteByTenantId(@Param("tenantId") Long tenantId);

    void deleteByPlatformId(@Param("platformId") Long platformId);

    List<String> getByConfigMailList(@Param("platformId") Long platformId);

    List<String> getByTenantList(@Param("platformId") Long platformId);

    List<String> selectPlatformListByTenantId(@Param("tenantId") Long tenantId);

    int selectHzStatusByplatformId(@Param("platformId") String platformId);


    Long getContractList(@Param("tenantId") Long tenantId);


    Long getCpuSockets(@Param("platformIds") String platformIds);

    void updateByName(@Param("id") Long id, @Param("name") String name);

    Long getCloudCount(@Param("platformIds") String platformIds);

    List<String> getByPlatformId(@Param("id") Long id);
}
