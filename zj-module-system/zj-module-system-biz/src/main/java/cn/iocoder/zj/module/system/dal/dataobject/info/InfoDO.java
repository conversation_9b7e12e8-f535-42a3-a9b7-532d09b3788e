package cn.iocoder.zj.module.system.dal.dataobject.info;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 授权证书 DO
 *
 * <AUTHOR>
 */
@TableName("license_info")
@KeySequence("license_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
public class InfoDO{

    /**
     * 主键
     */
    @TableId
    private Long licenseId;
    /**
     * 证书地址
     */
    private String licenseUrl;
    /**
     * 开始时间
     */
    private LocalDateTime startDate;
    /**
     * 过期时间
     */
    private String expiryDate;
    /**
     * 授权数量
     */
    private String licenseCount;
    /**
     * 授权码
     */
    private String machineCode;

    private String appCode;

    private String licenseName;
    /**
     * 剩余数量
     */
    private Integer useCount;

    private Integer enable;

    private Integer version;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;


}
