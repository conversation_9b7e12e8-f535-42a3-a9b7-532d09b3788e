package cn.iocoder.zj.module.system.dal.mysql.metadata;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.system.dal.dataobject.metadata.MetadataDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.system.controller.admin.metadata.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 元数据 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MetadataMapper extends BaseMapperX<MetadataDO> {

    default PageResult<MetadataDO> selectPage(MetadataPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MetadataDO>()
                .eqIfPresent(MetadataDO::getType, reqVO.getType())
                .eqIfPresent(MetadataDO::getValue, reqVO.getValue())
                .betweenIfPresent(MetadataDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MetadataDO::getId));
    }

    default List<MetadataDO> selectList(MetadataExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MetadataDO>()
                .eqIfPresent(MetadataDO::getType, reqVO.getType())
                .eqIfPresent(MetadataDO::getValue, reqVO.getValue())
                .betweenIfPresent(MetadataDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MetadataDO::getId));
    }

    MetadataDO selectByType(@Param("type") String type, @Param("tenantId")Long tenantId);
}
