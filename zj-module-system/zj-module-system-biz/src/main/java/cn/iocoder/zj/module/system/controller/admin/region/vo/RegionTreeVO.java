package cn.iocoder.zj.module.system.controller.admin.region.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName : RegionTreeVo  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/22  15:31
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class RegionTreeVO {
    /**
     * id
     */
    private String id;

    /**
     * @description: 父级菜单id
     * <AUTHOR>
     * @date 2023/5/22 16:46
     * @version 1.0
     */
    private String regionParentId;

    /**
     * 区域名称
     */
    private String regionName;

    /**
     * 子节点
     */
    private List<RegionTreeVO> children;

}
