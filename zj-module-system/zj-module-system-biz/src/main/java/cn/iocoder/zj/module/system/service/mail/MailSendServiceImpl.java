package cn.iocoder.zj.module.system.service.mail;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.mail.MailAccount;
import cn.hutool.extra.mail.MailUtil;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.enums.UserTypeEnum;
import cn.iocoder.zj.framework.common.pojo.UserBindDTO;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.module.system.controller.admin.tenant.vo.tenant.TenantRespVO;
import cn.iocoder.zj.module.system.convert.mail.MailAccountConvert;
import cn.iocoder.zj.module.system.dal.dataobject.mail.MailAccountDO;
import cn.iocoder.zj.module.system.dal.dataobject.mail.MailTemplateDO;
import cn.iocoder.zj.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.zj.module.system.dal.mysql.tenant.TenantMapper;
import cn.iocoder.zj.module.system.mq.message.mail.MailSendMessage;
import cn.iocoder.zj.module.system.mq.producer.mail.MailProducer;
import cn.iocoder.zj.module.system.service.member.MemberService;
import cn.iocoder.zj.module.system.service.user.AdminUserService;
import com.google.common.annotations.VisibleForTesting;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring5.SpringTemplateEngine;

import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.system.enums.ErrorCodeConstants.*;

/**
 * 邮箱发送 Service 实现类
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@Service
@Validated
@Slf4j
public class MailSendServiceImpl implements MailSendService {

    @Resource
    private AdminUserService adminUserService;
    @Resource
    private MemberService memberService;

    @Resource
    private MailAccountService mailAccountService;
    @Resource
    private MailTemplateService mailTemplateService;

    @Resource
    private MailLogService mailLogService;
    @Resource
    private MailProducer mailProducer;
    @Resource
    private RedisTemplate redisTemplate;



    @Value("${e-mail.address}")
    private String address;
//
//    @Value("${e-mail.password}")
//    private String password;
//
//    @Value("${e-mail.host}")
//    private String mailHost;

    @Autowired
    private SpringTemplateEngine templateEngine;

    @Value("${imgPath.top}")
    private String topImgPath;

    @Value("${imgPath.bottom}")
    private String bottomImgPath;
//    private String topImgPath =  "https://s3.zjiecn.com/zj-server-fh/20240723135304ea106530c640cec368c451d8b772f4f.png";
//    private String bottomImgPath =  "https://s3.zjiecn.com/zj-server-fh/2024072313532359c54e83629daaa0cd9517d15043b9f.png";
    @Resource
    private TenantMapper tenantMapper;

    @Override
    public Long sendSingleMailToAdmin(String mail, Long userId,
                                      String templateCode, Map<String, Object> templateParams,String alarmMail) {
        // 如果 mail 为空，则加载用户编号对应的邮箱
        if (StrUtil.isEmpty(mail)) {
            AdminUserDO user = adminUserService.getUser(userId);
            if (user != null) {
                mail = user.getEmail();
            }
        }
        // 执行发送
        return sendSingleMail(mail, userId, UserTypeEnum.ADMIN.getValue(), templateCode, templateParams,alarmMail);
    }

    @Override
    public Long sendSingleMailToMember(String mail, Long userId,
                                       String templateCode, Map<String, Object> templateParams) {
        // 如果 mail 为空，则加载用户编号对应的邮箱
        if (StrUtil.isEmpty(mail)) {
            mail = memberService.getMemberUserEmail(userId);
        }
        // 执行发送
        return sendSingleMail(mail, userId, UserTypeEnum.MEMBER.getValue(), templateCode, templateParams,"false");
    }

    @Override
    public Long sendSingleMail(String mail, Long userId, Integer userType,
                               String templateCode, Map<String, Object> templateParams,String alarmMail) {
        // 校验邮箱模版是否合法
        MailTemplateDO template = validateMailTemplate(templateCode);
        // 校验邮箱账号是否合法
        MailAccountDO account = validateMailAccount(template.getAccountId());

        // 校验邮箱是否存在
        mail = validateMail(mail);
        validateTemplateParams(template, templateParams);

        // 创建发送日志。如果模板被禁用，则不发送短信，只记录日志
        Boolean isSend = CommonStatusEnum.ENABLE.getStatus().equals(template.getStatus());
        Context context = new Context();
        context.setVariable("topImageUrl", topImgPath);
        context.setVariable("bottomImageUrl", bottomImgPath);
        context.setVariable("alarmName",templateParams.get("alarmName"));
        context.setVariable("app", ObjectUtil.isNotEmpty(templateParams.get("app"))?templateParams.get("app"):"-");
        context.setVariable("time", ObjectUtil.isNotEmpty(templateParams.get("time"))?templateParams.get("time"):"-");
        context.setVariable("priority", ObjectUtil.isNotEmpty(templateParams.get("priority"))?templateParams.get("priority"):"-");
        context.setVariable("times", ObjectUtil.isNotEmpty(templateParams.get("times"))?templateParams.get("times"):"-");
        context.setVariable("platformName", ObjectUtil.isNotEmpty(templateParams.get("platformName"))?templateParams.get("platformName"):"-");
        context.setVariable("content", ObjectUtil.isNotEmpty(templateParams.get("content"))?templateParams.get("content"):"-");
        context.setVariable("address", ObjectUtil.isNotEmpty(templateParams.get("address"))?templateParams.get("address"):"-");
        String htmlBody = templateEngine.process("alert-mail", context); // 不需要 .html 后缀
        Long sendLogId = mailLogService.createMailLog(userId, userType, mail,
                account, template, htmlBody, templateParams, isSend);

        MailSendMessage message = new MailSendMessage()
                .setLogId(sendLogId).setMail(mail).setAccountId(account.getId())
                .setNickname( template.getNickname())
                .setTitle(template.getTitle()).setContent(htmlBody)
                .setAlarmMail(alarmMail);

        doSendMail(message);
//        // 发送 MQ 消息，异步执行发送短信
//        if (isSend) {
//            mailProducer.sendMailSendMessage(sendLogId, mail, account.getId(),
//                    template.getNickname(), template.getTitle(), content);
//        }
        return sendLogId;
    }

    @Override
    public void doSendMail(MailSendMessage message) {
        UserBindDTO userBindDO = JsonUtils.parseObject(redisTemplate.opsForValue().get("app_key_secret:app").toString(), UserBindDTO.class);
        if (isUserBindInfoInvalid(userBindDO)) {
            mailLogService.updateMailSendResult(message.getLogId(), null, new Exception());
            log.info("邮箱参数为空");
            return;
        }
        // 1. 创建发送账号
        MailAccountDO account = validateMailAccount(message.getAccountId());
        MailAccount mailAccount  = MailAccountConvert.INSTANCE.convert(account, message.getNickname());
        // 2.
        try {
            mailAccount.setHost(userBindDO.getMailSmtpHost());
            mailAccount.setPort(465);
            mailAccount.setSslEnable(true);
            mailAccount.setUser(userBindDO.getMailAddress());
            mailAccount.setAuth(true);
            mailAccount.setPass(userBindDO.getMailPassword());
            mailAccount.setFrom(userBindDO.getMailAddress());
            String content = message.getContent();
            content = content.replaceAll("<p>", "").replaceAll("</p>", "");
             content = content.replaceAll("告警配置：", "\n告警配置")
                    .replaceAll("，告警阈值是", "\n告警阈值是")
                    .replaceAll("，触发告警的时间是", "\n触发告警的时间是");
            System.out.println(content);
            String messageId = MailUtil.send(mailAccount, message.getMail(),
                    message.getTitle(), content,true);

            // 3. 更新结果（成功）
            mailLogService.updateMailSendResult(message.getLogId(), messageId, null);
        } catch (Exception e) {
            // 3. 更新结果（异常）
            mailLogService.updateMailSendResult(message.getLogId(), null, e);
        }
    }

    public void doSendMailBpm(MailSendMessage message) {
        UserBindDTO userBindDO = JsonUtils.parseObject(redisTemplate.opsForValue().get("app_key_secret:app").toString(), UserBindDTO.class);
        if (isUserBindInfoInvalid(userBindDO)) {
            mailLogService.updateMailSendResult(message.getLogId(), null, new Exception());
            log.info("邮箱参数为空");
            return;
        }
        // 1. 创建发送账号
        MailAccountDO account = validateMailAccount(message.getAccountId());
        MailAccount mailAccount  = MailAccountConvert.INSTANCE.convert(account, message.getNickname());
        // 2. 发送邮件
        try {
            mailAccount.setHost(userBindDO.getMailSmtpHost());
            mailAccount.setPort(465);
            mailAccount.setSslEnable(true);
            mailAccount.setUser(userBindDO.getMailAddress());
            mailAccount.setAuth(true);
            mailAccount.setPass(userBindDO.getMailPassword());
            mailAccount.setFrom(userBindDO.getMailAddress());
            String content = message.getContent();
            String messageId = MailUtil.send(mailAccount, message.getMail(),
                    message.getTitle(), content,true);

            // 3. 更新结果（成功）
            mailLogService.updateMailSendResult(message.getLogId(), messageId, null);
        } catch (Exception e) {
            // 3. 更新结果（异常）
            mailLogService.updateMailSendResult(message.getLogId(), null, e);
        }
    }

    public void sendHtmlEmailWithImage(String userName,String passWord,String mail){
        UserBindDTO userBindDO = JsonUtils.parseObject(redisTemplate.opsForValue().get("app_key_secret:app").toString(), UserBindDTO.class);
        if (isUserBindInfoInvalid(userBindDO)) {
            log.info("邮箱参数为空");
            return;
        }
        try {
            JavaMailSender mailSender = getJavaMailSender(userBindDO.getMailSmtpHost(),userBindDO.getMailPassword());
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, StandardCharsets.UTF_8.name());

            helper.setFrom(userBindDO.getMailAddress()); // 设置发件人地址，必须与你的 QQ 邮箱地址一致
            helper.setTo(mail);
            helper.setSubject("云星辰账号开通提醒");

            // 加载 HTML 模板
            Context context = new Context();
            context.setVariable("topImageUrl", topImgPath);
            context.setVariable("bottomImageUrl", bottomImgPath);
            context.setVariable("user",userName);
            context.setVariable("pass",passWord);

            String htmlBody = templateEngine.process("email-template", context); // 不需要 .html 后缀
            helper.setText(htmlBody, true);


            mailSender.send(message);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    public Long sendSingleMailToBpm(String mail, Long userId, String templateCode, Map<String, Object> templateParams) {
        // 校验邮箱模版是否合法
        MailTemplateDO template = validateMailTemplate(templateCode);
        // 校验邮箱账号是否合法
        MailAccountDO account = validateMailAccount(template.getAccountId());

        // 校验邮箱是否存在
        mail = validateMail(mail);
        validateTemplateParams(template, templateParams);

        // 创建发送日志。如果模板被禁用，则不发送短信，只记录日志
        Boolean isSend = CommonStatusEnum.ENABLE.getStatus().equals(template.getStatus());
        Context context = new Context();
        context.setVariable("topImageUrl", topImgPath);
        context.setVariable("bottomImageUrl", bottomImgPath);
        context.setVariable("platformName",templateParams.get("platformName"));
        context.setVariable("createTime", ObjectUtil.isNotEmpty(templateParams.get("createTime"))?templateParams.get("createTime"):"-");
        context.setVariable("nickname", ObjectUtil.isNotEmpty(templateParams.get("nickname"))?templateParams.get("nickname"):"-");
        context.setVariable("content", ObjectUtil.isNotEmpty(templateParams.get("content"))?templateParams.get("content"):"-");
        context.setVariable("number", ObjectUtil.isNotEmpty(templateParams.get("number"))?templateParams.get("number"):"-");
        String htmlBody = templateEngine.process("bpm-mail", context); // 不需要 .html 后缀
        Long sendLogId = mailLogService.createMailLog(userId, UserTypeEnum.ADMIN.getValue(), mail,
                account, template, htmlBody, templateParams, isSend);

        MailSendMessage message = new MailSendMessage()
                .setLogId(sendLogId).setMail(mail).setAccountId(account.getId())
                .setNickname( template.getNickname())
                .setTitle(template.getTitle()).setContent(htmlBody)
                .setAlarmMail("false");

        doSendMailBpm(message);
        return sendLogId;
    }

    public JavaMailSender getJavaMailSender(String mailHost,String password) {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
        mailSender.setHost(mailHost);
        mailSender.setPort(465);
        mailSender.setUsername(address);
        mailSender.setPassword(password);

        Properties props = mailSender.getJavaMailProperties();
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.starttls.enable", "true");
        props.put("mail.smtp.ssl.enable", "true"); // 启用 SSL
        props.put("mail.debug", "true");

        return mailSender;
    }

    @Override
    public MailTemplateDO getTemplate(Long id) {
        return  mailTemplateService.getMailTemplate(id);
    }

    @Override
    public String getTemplateByName(String name) {
        return mailTemplateService.getTemplateByName(name).getCode();
    }

    @VisibleForTesting
    MailTemplateDO validateMailTemplate(String templateCode) {
        // 获得邮件模板。考虑到效率，从缓存中获取
        MailTemplateDO template = mailTemplateService.getMailTemplateByCodeFromCache(templateCode);
        // 邮件模板不存在
        if (template == null) {
            throw exception(MAIL_TEMPLATE_NOT_EXISTS);
        }
        return template;
    }

    @VisibleForTesting
    MailAccountDO validateMailAccount(Long accountId) {
        // 获得邮箱账号。考虑到效率，从缓存中获取
        MailAccountDO account = mailAccountService.getMailAccountFromCache(accountId);
        // 邮箱账号不存在
        if (account == null) {
            throw exception(MAIL_ACCOUNT_NOT_EXISTS);
        }
        return account;
    }

    @VisibleForTesting
    String validateMail(String mail) {
        if (StrUtil.isEmpty(mail)) {
            throw exception(MAIL_SEND_MAIL_NOT_EXISTS);
        }
        return mail;
    }

    /**
     * 校验邮件参数是否确实
     *
     * @param template 邮箱模板
     * @param templateParams 参数列表
     */
    @VisibleForTesting
    void validateTemplateParams(MailTemplateDO template, Map<String, Object> templateParams) {
        template.getParams().forEach(key -> {
            Object value = templateParams.get(key);
            if (value == null) {
                throw exception(MAIL_SEND_TEMPLATE_PARAM_MISS, key);
            }
        });
    }

    private boolean isUserBindInfoInvalid(UserBindDTO userBindDO) {
        return BeanUtil.isEmpty(userBindDO) ||
                StrUtil.isEmpty(userBindDO.getMailAddress()) ||
                StrUtil.isEmpty(userBindDO.getMailPassword()) ||
                StrUtil.isEmpty(userBindDO.getMailSmtpHost());
    }

    @Override
    public void sendMail(String email, Long tenantId,long day,String time) {
        UserBindDTO userBindDO = JsonUtils.parseObject(redisTemplate.opsForValue().get("app_key_secret:app").toString(), UserBindDTO.class);
        if (isUserBindInfoInvalid(userBindDO)) {
            log.info("邮箱参数为空");
            return;
        }
        //查询授权资产剩余
        //总资产
        Integer assetNum = 0;
        Integer totalCount = 0;
        Integer surplus = 0;
        TenantRespVO tenantRespVO =  tenantMapper.getTenantByIdOne(tenantId);
        List<Map> map = tenantMapper.getPlatformByIds(Arrays.asList(tenantRespVO.getId()));
        if(CollUtil.isNotEmpty(map)){
            assetNum =  Convert.toInt(tenantRespVO.getAssetNum(), 0);
            for (Map<String, Object> m : map) {
                if (m.get("tenant_id") != null && (Convert.toLong(m.get("tenant_id")).equals(tenantRespVO.getId()))) {
                    totalCount = Convert.toInt(m.get("total_count"));
                }
            }
            surplus = assetNum - totalCount;
        }

        try {
            JavaMailSender mailSender = getJavaMailSender(userBindDO.getMailSmtpHost(),userBindDO.getMailPassword());
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, StandardCharsets.UTF_8.name());

            helper.setFrom(userBindDO.getMailAddress()); // 设置发件人地址，必须与你的 QQ 邮箱地址一致
            helper.setTo(email);
            helper.setSubject("云星辰授权到期提醒");

            // 加载 HTML 模板
            Context context = new Context();
            context.setVariable("topImageUrl", topImgPath);
            context.setVariable("bottomImageUrl", bottomImgPath);
            context.setVariable("assetNum",assetNum);
            context.setVariable("totalCount",totalCount);
            context.setVariable("surplus",surplus);
            context.setVariable("day",day);
            context.setVariable("time",time);

            String htmlBody = templateEngine.process("email-license", context); // 不需要 .html 后缀
            helper.setText(htmlBody, true);
            mailSender.send(message);
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
