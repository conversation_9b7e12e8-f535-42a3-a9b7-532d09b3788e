package cn.iocoder.zj.module.system.dal.mysql.usercertificate;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.zj.module.system.dal.dataobject.usercertificate.UserCertificateDO;
import com.alibaba.nacos.common.utils.StringUtils;
import jodd.util.StringUtil;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.system.controller.admin.usercertificate.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 授权凭证 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserCertificateMapper extends BaseMapperX<UserCertificateDO> {

    default PageResult<UserCertificateDO> selectPage(UserCertificatePageReqVO reqVO) {
        if(StringUtils.isEmpty(reqVO.getType())){
            reqVO.setType(null);
        }
        LambdaQueryWrapperX<UserCertificateDO> userCertificateDOLambdaQueryWrapperX =
                new LambdaQueryWrapperX<UserCertificateDO>()
                .eqIfPresent(UserCertificateDO::getUserId, reqVO.getUserId())
                .likeIfPresent(UserCertificateDO::getName, reqVO.getName())
                .likeIfPresent(UserCertificateDO::getOwnerName, reqVO.getOwnerName())
                .likeIfPresent(UserCertificateDO::getUsername, reqVO.getUsername())
                .eqIfPresent(UserCertificateDO::getPassword, reqVO.getPassword())
                .eqIfPresent(UserCertificateDO::getPassphrase, reqVO.getPassphrase())
                .eqIfPresent(UserCertificateDO::getPrivateKey, reqVO.getPrivateKey())
                .eqIfPresent(UserCertificateDO::getType, reqVO.getType())
                .betweenIfPresent(UserCertificateDO::getCreateTime, reqVO.getCreateTime());

        if (jodd.util.StringUtil.isNotEmpty(reqVO.getStartTime()) && jodd.util.StringUtil.isNotEmpty(reqVO.getEndTime())) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            try {
                date = sdf.parse(reqVO.getEndTime());
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(date);
                // 把日期设置为当天最后一秒
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                date = calendar.getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            userCertificateDOLambdaQueryWrapperX.betweenIfPresent(UserCertificateDO::getCreateTime, reqVO.getStartTime(), sdf.format(date));
        }


        if (StringUtil.isNotEmpty(reqVO.getSortDirection()) && reqVO.getSortDirection().equals("asc")){
            if (reqVO.getSortBy().equals("createTime")){
                userCertificateDOLambdaQueryWrapperX.orderByAsc(UserCertificateDO::getCreateTime);
            }
        }else {
            userCertificateDOLambdaQueryWrapperX.orderByDesc(UserCertificateDO::getCreateTime);
        }

        return selectPage(reqVO, userCertificateDOLambdaQueryWrapperX);
    }

    default List<UserCertificateDO> selectList(UserCertificateExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<UserCertificateDO>()
                .eqIfPresent(UserCertificateDO::getUserId, reqVO.getUserId())
                .likeIfPresent(UserCertificateDO::getName, reqVO.getName())
                .likeIfPresent(UserCertificateDO::getOwnerName, reqVO.getOwnerName())
                .likeIfPresent(UserCertificateDO::getUsername, reqVO.getUsername())
                .eqIfPresent(UserCertificateDO::getPassword, reqVO.getPassword())
                .eqIfPresent(UserCertificateDO::getPassphrase, reqVO.getPassphrase())
                .eqIfPresent(UserCertificateDO::getPrivateKey, reqVO.getPrivateKey())
                .eqIfPresent(UserCertificateDO::getType, reqVO.getType())
                .betweenIfPresent(UserCertificateDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(UserCertificateDO::getId));
    }

    UserCertificateInfoRespVO selectByIdInfo(@Param("str") String str);
}
