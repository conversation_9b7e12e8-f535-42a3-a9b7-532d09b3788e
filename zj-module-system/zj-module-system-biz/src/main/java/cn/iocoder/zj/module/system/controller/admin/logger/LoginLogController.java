package cn.iocoder.zj.module.system.controller.admin.logger;

import cn.iocoder.zj.module.system.dal.dataobject.logger.LoginLogDO;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.module.system.controller.admin.logger.vo.loginlog.LoginLogExcelVO;
import cn.iocoder.zj.module.system.controller.admin.logger.vo.loginlog.LoginLogExportReqVO;
import cn.iocoder.zj.module.system.controller.admin.logger.vo.loginlog.LoginLogPageReqVO;
import cn.iocoder.zj.module.system.controller.admin.logger.vo.loginlog.LoginLogRespVO;
import cn.iocoder.zj.module.system.convert.logger.LoginLogConvert;
import cn.iocoder.zj.module.system.service.logger.LoginLogService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

@Tag(name =  "管理后台 - 登录日志")
@RestController
@RequestMapping("/system/login-log")
@Validated
public class LoginLogController {

    @Resource
    private LoginLogService loginLogService;

    @GetMapping("/page")
    @Operation(summary = "获得登录日志分页列表")
    @PreAuthorize("@ss.hasPermission('system:login-log:query')")
    public CommonResult<PageResult<LoginLogRespVO>> getLoginLogPage(@Valid LoginLogPageReqVO reqVO) {
        PageResult<LoginLogDO> page = loginLogService.getLoginLogPage(reqVO);
        return CommonResult.success(LoginLogConvert.INSTANCE.convertPage(page));
    }

    @GetMapping("/export")
    @Operation(summary = "导出登录日志 Excel")
//    @PreAuthorize("@ss.hasPermission('system:login-log:export')")
    @OperateLog(type = EXPORT)
    @PermitAll
    public void exportLoginLog(HttpServletResponse response, @Valid LoginLogExportReqVO reqVO) throws IOException {
        List<LoginLogDO> list = loginLogService.getLoginLogList(reqVO);
        // 拼接数据
        List<LoginLogExcelVO> data = LoginLogConvert.INSTANCE.convertList(list);
        // 输出
        ExcelUtils.write(response, "登录日志.xls", "数据列表", LoginLogExcelVO.class, data);
    }
    @GetMapping("/getPreviousLoginLog")
    @Operation(summary = "上次登录")
    public CommonResult<LoginLogRespVO> getPreviousLoginLog(@Valid @RequestParam("userId") Long userId) {
        LoginLogDO loginLog = loginLogService.getPreviousLoginLog(userId);
        return CommonResult.success(LoginLogConvert.INSTANCE.convert(loginLog));
    }
}