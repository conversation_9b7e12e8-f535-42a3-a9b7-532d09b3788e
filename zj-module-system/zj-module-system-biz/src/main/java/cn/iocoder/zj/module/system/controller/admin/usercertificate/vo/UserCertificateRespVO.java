package cn.iocoder.zj.module.system.controller.admin.usercertificate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 授权凭证 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserCertificateRespVO extends UserCertificateBaseVO {

    @Schema(description = "主键", required = true)
    private Integer id;
    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;
}
