package cn.iocoder.zj.module.system.controller.admin.usersync.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
* 企业微信userid Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class UserSyncBaseVO {

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "用户手机")
    private String phone;

    @Schema(description = "微信userId")
    private String wxUserId;

    @Schema(description = "用户名称")
    private String nickname;

    @Schema(description = "备注")
    private String remark;

}
