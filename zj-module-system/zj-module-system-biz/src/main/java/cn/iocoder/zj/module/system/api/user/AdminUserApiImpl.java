package cn.iocoder.zj.module.system.api.user;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.util.collection.CollectionUtils;
import cn.iocoder.zj.framework.common.util.object.BeanUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.zj.module.system.convert.user.UserConvert;
import cn.iocoder.zj.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.zj.module.system.service.user.AdminUserService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class AdminUserApiImpl implements AdminUserApi {

    @Resource
    private AdminUserService userService;

    @Override
    @TenantIgnore
    public CommonResult<AdminUserRespDTO> getUser(Long id) {
        AdminUserDO user = userService.getUser(id);
        return success(BeanUtils.toBean(user, AdminUserRespDTO.class));
    }

    @Override
    public CommonResult<AdminUserRespDTO> getUserById(Long id) {
        return success(userService.getUserById(id));
    }

    @Override
    public CommonResult<List<AdminUserRespDTO>> getUserList(Collection<Long> ids) {
        List<AdminUserDO> users = userService.getUserList(ids);
        return success(BeanUtils.toBean(users, AdminUserRespDTO.class));
    }

    @Override
    public CommonResult<List<AdminUserRespDTO>> getUserListByDeptIds(Collection<Long> deptIds) {
        List<AdminUserDO> users = userService.getUserListByDeptIds(deptIds);
        return success(BeanUtils.toBean(users, AdminUserRespDTO.class));
    }

    @Override
    public CommonResult<List<AdminUserRespDTO>> getUserListByPostIds(Collection<Long> postIds) {
        List<AdminUserDO> users = userService.getUserListByPostIds(postIds);
        return success(BeanUtils.toBean(users, AdminUserRespDTO.class));
    }

    @Override
    @TenantIgnore
    public CommonResult<Map<Long, AdminUserRespDTO>> getUserMap(Collection<Long> ids) {
        List<AdminUserRespDTO> users = getUserList(ids).getCheckedData();
        return success(CollectionUtils.convertMap(users, AdminUserRespDTO::getId));
    }

    @Override
    public CommonResult<Boolean> validateUserList(Set<Long> ids) {
        userService.validateUserList(ids);
        return success(true);
    }

    @Override
    public CommonResult<List<Long>> getUserIdsByTenantId(Long userId) {
        return success(userService.getUserIdsByTenantId(userId));
    }

    @Override
    public CommonResult<List<Long>> getHomologousUsers(Long userId) {
        return success(userService.getHomologousUsers(userId));
    }

    @Override
    public CommonResult<List<String>> getUserMenus(Long userId) {
        return success(userService.getUserMenus(userId));
    }

    @Override
    public CommonResult<Boolean> updateUserEmail(String email) {
        userService.updateUserEmail(email);
        return success(true);
    }

    @Override
    @TenantIgnore
    public CommonResult<Boolean> validateEmail(String email) {

        return success(userService.validateEmail(email));
    }

    @Override
    @TenantIgnore
    public CommonResult<List<AdminUserRespDTO>> getUserListByNickname(String name) {
        List<AdminUserDO> users = userService.getUserListByNickname(name);
        return success(BeanUtils.toBean(users, AdminUserRespDTO.class));
    }
}
