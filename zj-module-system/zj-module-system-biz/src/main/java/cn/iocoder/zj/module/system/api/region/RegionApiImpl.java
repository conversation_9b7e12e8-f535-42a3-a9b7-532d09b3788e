package cn.iocoder.zj.module.system.api.region;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.system.api.region.dto.RegionDTO;
import cn.iocoder.zj.module.system.convert.region.RegionConvert;
import cn.iocoder.zj.module.system.dal.dataobject.region.RegionDO;
import cn.iocoder.zj.module.system.service.region.RegionService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

/**
 * @ClassName : RegionApiImpl  //类名
 * @Description : RPC --- 地区树形图  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/22  17:27
 */

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class RegionApiImpl implements RegionApi {

    @Resource
    private RegionService regionService;


    @Override
    public CommonResult<RegionDTO> getRegion(String id) {
        RegionDO regionDO = regionService.getRegion(id);
        return success(RegionConvert.INSTANCE.covertDtoRegion(regionDO));
    }

}
