package cn.iocoder.zj.module.system.controller.admin.auth;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.UserBindDTO;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.framework.security.config.SecurityProperties;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.system.controller.admin.auth.vo.*;
import cn.iocoder.zj.module.system.convert.auth.AuthConvert;
import cn.iocoder.zj.module.system.dal.dataobject.permission.MenuDO;
import cn.iocoder.zj.module.system.dal.dataobject.permission.RoleDO;
import cn.iocoder.zj.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.zj.module.system.enums.logger.LoginLogTypeEnum;
import cn.iocoder.zj.module.system.service.auth.AdminAuthService;
import cn.iocoder.zj.module.system.service.permission.MenuService;
import cn.iocoder.zj.module.system.service.permission.PermissionService;
import cn.iocoder.zj.module.system.service.permission.RoleService;
import cn.iocoder.zj.module.system.service.social.SocialUserService;
import cn.iocoder.zj.module.system.service.user.AdminUserService;
import com.taobao.api.ApiException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.convertSet;
import static cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils.obtainAuthorization;

@Tag(name = "管理后台 - 认证")
@RestController
@RequestMapping("/system/auth")
@Validated
@Slf4j
public class AuthController {

    @Resource
    private AdminAuthService authService;
    @Resource
    private AdminUserService userService;
    @Resource
    private RoleService roleService;
    @Resource
    private MenuService menuService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private SocialUserService socialUserService;

    @Resource
    private SecurityProperties securityProperties;
    @Resource
    private RedisTemplate redisTemplate;

    @PostMapping("/login")
    @PermitAll
    @Operation(summary = "使用账号密码登录")
    @OperateLog(enable = false) // 避免 Post 请求被记录操作日志
    public CommonResult<AuthLoginRespVO> login(@RequestBody @Valid AuthLoginReqVO reqVO) {
        return success(authService.login(reqVO));
    }

    @PostMapping("/logout")
    @PermitAll
    @Operation(summary = "登出系统")
    @OperateLog(enable = false) // 避免 Post 请求被记录操作日志
    public CommonResult<Boolean> logout(HttpServletRequest request) {
        String token = obtainAuthorization(request, securityProperties.getTokenHeader());
        if (StrUtil.isNotBlank(token)) {
            authService.logout(token, LoginLogTypeEnum.LOGOUT_SELF.getType());
        }
        return success(true);
    }

    @PostMapping("/refresh-token")
    @PermitAll
    @Operation(summary = "刷新令牌")
    @Parameter(name = "refreshToken", description = "刷新令牌", required = true)
    @OperateLog(enable = false) // 避免 Post 请求被记录操作日志
    public CommonResult<AuthLoginRespVO> refreshToken(@RequestParam("refreshToken") String refreshToken) {
        return success(authService.refreshToken(refreshToken));
    }

    @GetMapping("/get-permission-info")
    @Operation(summary = "获取登录用户的权限信息")
    public CommonResult<AuthPermissionInfoRespVO> getPermissionInfo() {
        // 1.1 获得用户信息
        AdminUserDO user = userService.getUser(getLoginUserId());
        if (user == null) {
            return null;
        }
        // 1.2 获得角色列表
        Set<Long> roleIds = permissionService.getUserRoleIdListByUserId(getLoginUserId());
        List<RoleDO> roles = roleService.getRoleList(roleIds);
        roles.removeIf(role -> !CommonStatusEnum.ENABLE.getStatus().equals(role.getStatus())); // 移除禁用的角色

        //判断当前登录用户是否为管理员运维
        Boolean flag = roleService.getIsRootOperation(user.getId());

        // 查询版本
        Integer state = userService.getStateByTenantId(user.getTenantId()) ;

        // 1.3 获得菜单列表
        Set<Long> menuIds = permissionService.getRoleMenuListByRoleId(convertSet(roles, RoleDO::getId));
        List<MenuDO> menuList = menuService.getMenuList(menuIds);
        menuList.removeIf(menu -> !CommonStatusEnum.ENABLE.getStatus().equals(menu.getStatus())); // 移除禁用的菜单

        // 2. 拼接结果返回
        AuthPermissionInfoRespVO convert = AuthConvert.INSTANCE.convert(user, roles, menuList);
        AuthPermissionInfoRespVO.UserVO userVO = convert.getUser();
        userVO.setIsRootOperation(flag);
        userVO.setState(state);
        convert.setUser(userVO);
        return success(convert);
    }


    // ========== 公众号登录相关 ==========
    @PostMapping("/public-login")
    @PermitAll
    @Operation(summary = "微信公众号通过code登录")
    @OperateLog(enable = false)
    public CommonResult<AuthLoginRespVO> publicLogin(@RequestBody Map<String, String> requestBody) {
        log.info("requestBody:{}", requestBody);
        if (requestBody.get("code") == null) {
            return success(null);
        }
        String code = requestBody.get("code");
        return success(authService.publicLogin(code));
    }


    // ========== 扫码登录 ==========
    @RequestMapping(value = "/scanqrlogin", method = RequestMethod.POST)
    @PermitAll
    @Operation(summary = "扫码登录")
    @OperateLog(enable = false)
    @TenantIgnore
    public CommonResult<AuthLoginRespVO> scanQRLogin(@RequestBody Map<String, String> requestBody) throws ApiException {
        return success(authService.scanQRLogin(requestBody));
    }

    // ========== 短信登录相关 ==========

    @PostMapping("/sms-login")
    @PermitAll
    @Operation(summary = "使用短信验证码登录")
    @OperateLog(enable = false) // 避免 Post 请求被记录操作日志
    public CommonResult<AuthLoginRespVO> smsLogin(@RequestBody @Valid AuthSmsLoginReqVO reqVO) {
        return success(authService.smsLogin(reqVO));
    }

    @PostMapping("/sms-resetPassword")
    @PermitAll
    @Operation(summary = "验证码重置密码")
    @OperateLog(enable = false) // 避免 Post 请求被记录操作日志
    public CommonResult<Boolean> resetPassword(@RequestBody @Valid AuthResetPasswordReqVO reqVO) {
        return success(authService.resetPassword(reqVO));
    }

    @PostMapping("/send-sms-code")
    @PermitAll
    @Operation(summary = "发送手机验证码")
    @OperateLog(enable = false) // 避免 Post 请求被记录操作日志
    public CommonResult<Boolean> sendLoginSmsCode(@RequestBody @Valid AuthSmsSendReqVO reqVO) {
        return success(authService.sendSmsCode(reqVO));
    }

    // ========== 社交登录相关 ==========

    @GetMapping("/social-auth-redirect")
    @PermitAll
    @Operation(summary = "社交授权的跳转")
    @Parameters({
            @Parameter(name = "type", description = "社交类型", required = true),
            @Parameter(name = "redirectUri", description = "回调路径")
    })
    public CommonResult<String> socialLogin(@RequestParam("type") Integer type,
                                            @RequestParam("redirectUri") String redirectUri) {
        return CommonResult.success(socialUserService.getAuthorizeUrl(type, redirectUri));
    }

    @PostMapping("/social-login")
    @PermitAll
    @Operation(summary = "社交快捷登录，使用 code 授权码", description = "适合未登录的用户，但是社交账号已绑定用户")
    @OperateLog(enable = false) // 避免 Post 请求被记录操作日志
    public CommonResult<AuthLoginRespVO> socialQuickLogin(@RequestBody @Valid AuthSocialLoginReqVO reqVO) {
        return success(authService.socialLogin(reqVO));
    }

    @GetMapping("/first-login")
    @PermitAll
    @Operation(summary = "首次登录验证")
    public CommonResult<Boolean> firstLogin(@RequestParam("userId") Long userId) {

        return CommonResult.success(authService.firstLogin(userId));
    }

    @PostMapping("/update-password")
    @PermitAll
    @Operation(summary = "更改密码")
    @OperateLog(enable = false) // 避免 Post 请求被记录操作日志
    public CommonResult<Boolean> updatePassword(@RequestBody AuthUpdatePasswordReqVO reqVO) {
        return success(userService.updatePassword(reqVO));
    }

    @GetMapping("/getappkeysecret")
    @PermitAll
    @Operation(summary = "获取应用秘钥数据")
    public CommonResult<UserBindDTO> getAppKeySecret(@RequestParam(value = "type") Integer type) {
        if (redisTemplate.hasKey("app_key_secret:app")) {
            UserBindDTO userBindDO = JsonUtils.parseObject(redisTemplate.opsForValue().get("app_key_secret:app").toString(), UserBindDTO.class);
            return success(userBindDO);
        } else {
            return success(new UserBindDTO());
        }
    }
}