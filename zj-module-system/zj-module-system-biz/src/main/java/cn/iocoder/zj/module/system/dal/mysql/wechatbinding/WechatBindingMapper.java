package cn.iocoder.zj.module.system.dal.mysql.wechatbinding;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.system.dal.dataobject.wechatbinding.WechatBindingDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.system.controller.admin.wechatbinding.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 微信公众号OpenId与租户绑定关系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WechatBindingMapper extends BaseMapperX<WechatBindingDO> {

    default PageResult<WechatBindingDO> selectPage(WechatBindingPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<WechatBindingDO>()
                .eqIfPresent(WechatBindingDO::getUserId, reqVO.getUserId())
                .eqIfPresent(WechatBindingDO::getOpenId, reqVO.getOpenId())
                .eqIfPresent(WechatBindingDO::getEmail, reqVO.getEmail())
                .eqIfPresent(WechatBindingDO::getIsWechat, reqVO.getIsWechat())
                .eqIfPresent(WechatBindingDO::getIsEmail, reqVO.getIsEmail())
                .betweenIfPresent(WechatBindingDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(WechatBindingDO::getId));
    }

    default List<WechatBindingDO> selectList(WechatBindingExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<WechatBindingDO>()
                .eqIfPresent(WechatBindingDO::getUserId, reqVO.getUserId())
                .eqIfPresent(WechatBindingDO::getOpenId, reqVO.getOpenId())
                .eqIfPresent(WechatBindingDO::getEmail, reqVO.getEmail())
                .eqIfPresent(WechatBindingDO::getIsWechat, reqVO.getIsWechat())
                .eqIfPresent(WechatBindingDO::getIsEmail, reqVO.getIsEmail())
                .betweenIfPresent(WechatBindingDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(WechatBindingDO::getId));
    }

    void deleteByUserId(@Param("userId") Long userId);

    void unbind(@Param("userId") String userId);
}
