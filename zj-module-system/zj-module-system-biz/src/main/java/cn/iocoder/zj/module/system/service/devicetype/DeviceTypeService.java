package cn.iocoder.zj.module.system.service.devicetype;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.system.controller.admin.devicetype.vo.DeviceTypeCreateReqVO;
import cn.iocoder.zj.module.system.controller.admin.devicetype.vo.DeviceTypeExportReqVO;
import cn.iocoder.zj.module.system.controller.admin.devicetype.vo.DeviceTypePageReqVO;
import cn.iocoder.zj.module.system.controller.admin.devicetype.vo.DeviceTypeUpdateReqVO;
import cn.iocoder.zj.module.system.dal.dataobject.devicetype.DeviceTypeDO;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 资产类型 Service 接口
 *
 * <AUTHOR>
 */
public interface DeviceTypeService {

    /**
     * 创建资产类型
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDeviceType(@Valid DeviceTypeCreateReqVO createReqVO);

    /**
     * 更新资产类型
     *
     * @param updateReqVO 更新信息
     */
    void updateDeviceType(@Valid DeviceTypeUpdateReqVO updateReqVO);

    /**
     * 删除资产类型
     *
     * @param id 编号
     */
    void deleteDeviceType(Long id);

    /**
     * 获得资产类型
     *
     * @param id 编号
     * @return 资产类型
     */
    DeviceTypeDO getDeviceType(Long id);

    /**
     * 获得资产类型列表
     *
     * @param ids 编号
     * @return 资产类型列表
     */
    List<DeviceTypeDO> getDeviceTypeList(Collection<Long> ids);

    /**
     * 获得资产类型分页
     *
     * @param pageReqVO 分页查询
     * @return 资产类型分页
     */
    PageResult<DeviceTypeDO> getDeviceTypePage(DeviceTypePageReqVO pageReqVO);

    /**
     * 获得资产类型列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 资产类型列表
     */
    List<DeviceTypeDO> getDeviceTypeList(DeviceTypeExportReqVO exportReqVO);

    List<DeviceTypeDO> getDeviceSelect();

    String updateDeviceIcon(MultipartFile file);

    List<DeviceTypeDO> getDeviceTypeByList();
}
