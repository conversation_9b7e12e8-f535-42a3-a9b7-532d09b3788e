package cn.iocoder.zj.module.system.dal.dataobject.devicesys;

import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 系统类型 DO
 *
 * <AUTHOR>
 */
@TableName("system_device_sys")
@KeySequence("system_device_sys_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceSysDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 系统类型
     */
    private String sysType;
    /**
     * 系统类型名称
     */
    private String sysName;

}
