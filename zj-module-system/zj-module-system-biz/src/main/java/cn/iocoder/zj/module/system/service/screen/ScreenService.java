package cn.iocoder.zj.module.system.service.screen;

import java.util.*;
import javax.validation.*;
import cn.iocoder.zj.module.system.controller.admin.screen.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.screen.ScreenConfigDO;
import cn.iocoder.zj.module.system.dal.dataobject.screen.ScreenDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 系统设置大屏 Service 接口
 *
 * <AUTHOR>
 */
public interface ScreenService {

    /**
     * 创建系统设置大屏
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createScreen(@Valid ScreenCreateReqVO createReqVO);

    /**
     * 更新系统设置大屏
     *
     * @param updateReqVO 更新信息
     */
    void updateScreen(@Valid ScreenUpdateReqVO updateReqVO);

    /**
     * 删除系统设置大屏
     *
     * @param id 编号
     */
    void deleteScreen(Long id);

    /**
     * 获得系统设置大屏
     *
     * @param id 编号
     * @return 系统设置大屏
     */
    ScreenDO getScreen(Long id);

    /**
     * 获得系统设置大屏列表
     *
     * @param tenantId 编号
     * @return 系统设置大屏列表
     */
    List<ScreenDO> getScreenList(Long tenantId);

    /**
     * 获得系统设置大屏列表
     *
     * @param tenantId 编号
     * @return 获得租户自定义大屏列表
     */
    List<ScreenDO> getTenantScreenList(Long tenantId);

    /**
     * 获得系统设置大屏分页
     *
     * @param pageReqVO 分页查询
     * @return 系统设置大屏分页
     */
    PageResult<ScreenDO> getScreenPage(ScreenPageReqVO pageReqVO);

    /**
     * 获得系统设置大屏列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 系统设置大屏列表
     */
    List<ScreenDO> getScreenList(ScreenExportReqVO exportReqVO);

    List<ScreenConfigDO> getScreenConfigByTenantId(Long tenantId);

    Boolean updateScreenConfig(ScreenConfigReqVO createReqVO);
}
