package cn.iocoder.zj.module.system.convert.tenant;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.system.api.tenant.dto.TenantRespDTO;
import cn.iocoder.zj.module.system.controller.admin.tenant.vo.tenant.*;
import cn.iocoder.zj.module.system.controller.admin.user.vo.user.UserCreateReqVO;
import cn.iocoder.zj.module.system.controller.admin.user.vo.user.UserUpdateReqVO;
import cn.iocoder.zj.module.system.dal.dataobject.tenant.TenantDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 租户 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantConvert {

    TenantConvert INSTANCE = Mappers.getMapper(TenantConvert.class);

    TenantDO convert(TenantCreateReqVO bean);

    TenantDO convert(TenantUpdateReqVO bean);

    TenantRespVO convert(TenantDO bean);

    List<TenantRespVO> convertList(List<TenantDO> list);

    PageResult<TenantRespVO> convertPage(PageResult<TenantDO> page);

    List<TenantExcelVO> convertList02(List<TenantDO> list);

    default UserCreateReqVO convert02(TenantCreateReqVO bean) {
        UserCreateReqVO reqVO = new UserCreateReqVO();
        reqVO.setUsername(bean.getUsername());
        reqVO.setPassword(bean.getPassword());
        reqVO.setNickname(bean.getContactName()).setMobile(bean.getContactMobile()).setEmail(bean.getContactEmail());
        reqVO.setOrganization(bean.getOrganization());
        reqVO.setDeptStr(bean.getDeptStr());
        return reqVO;
    }

    default UserUpdateReqVO convert03(TenantUpdateReqVO bean,Long contactUserId,Set<Long> dbPostIds) {
        UserUpdateReqVO reqVO = new UserUpdateReqVO();
        reqVO.setId(contactUserId);
        reqVO.setNickname(bean.getContactName()).setMobile(bean.getContactMobile()).setEmail(bean.getContactEmail());
        reqVO.setPostIds(dbPostIds);
        reqVO.setOrganization(bean.getOrganization());
        reqVO.setDeptStr(bean.getDeptStr());
        return reqVO;
    }

    default UserCreateReqVO convert04(TenantUpdateRootReqVo bean) {
        UserCreateReqVO reqVO = new UserCreateReqVO();
        reqVO.setUsername(bean.getUsername());
        reqVO.setPassword(bean.getPassword());
        reqVO.setNickname(bean.getContactName()).setMobile(bean.getContactMobile()).setEmail(bean.getContactEmail());
        reqVO.setOrganization(bean.getOrganization());
        reqVO.setDeptStr(bean.getDeptStr());
        return reqVO;
    }

    List<TenantSimpleRespVo> convertList04(List<TenantDO> list);

    TenantRespDTO convertToDTO(TenantDO tenant);
}
