package cn.iocoder.zj.module.system.controller.admin.tenant.vo.tenant;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Schema(description = "管理后台 - 租户创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TenantCreateReqVO extends TenantBaseVO {

    @Schema(description = "用户名称", required = true, example = "zj")
    private String username;

    @Schema(description = "密码", required = true, example = "123456")
    private String password;

    @Schema(description = "合同编号", required = true, example = "123456")
    private String contractNo;

}