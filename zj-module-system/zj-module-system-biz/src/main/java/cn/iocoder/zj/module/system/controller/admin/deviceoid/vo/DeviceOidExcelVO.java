package cn.iocoder.zj.module.system.controller.admin.deviceoid.vo;

import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * OID管理 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class DeviceOidExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("设备类型key")
    private String deviceType;

    @ExcelProperty("设备类型名称")
    private String deviceName;

    @ExcelProperty("系统类型名称")
    private String sysName;

    @ExcelProperty("系统类型key")
    private String sysType;

    @ExcelProperty("资产oid")
    private String oid;

    @ExcelProperty("资产oid 名称")
    private String oidName;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private Date createTime;

    @ExcelProperty("资产类型图标")
    private String icon;

}
