package cn.iocoder.zj.module.system.controller.admin.deviceoid.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

/**
* OID管理 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class DeviceOidBaseVO {

    @Schema(description = "设备类型key")
    private String deviceType;

    @Schema(description = "设备类型名称")
    private String deviceName;

    @Schema(description = "系统类型名称")
    private String sysName;

    @Schema(description = "系统类型key")
    private String sysType;

    /**
     * cpu使用率oid
     */
    @Schema(description = "cpu使用率oid")
    @NotNull(message = "cpu使用率oid不能为空")
    @Size(max =255, message = "cpu使用率oid长度为1-255个字符")
    private String cpuUse;

    /**
     * 内存总容量oid
     */
    @Schema(description = "内存总容量oid")
    @NotNull(message = "内存总容量oid不能为空")
    @Size(max =255, message = "内存总容量oid长度为1-255个字符")
    private String memoryAll;
    /**
     * 内存使用量oid
     */
    @Schema(description = "内存使用量oid")
    @NotNull(message = "内存使用量oid不能为空")
    @Size(max =255, message = "内存总容量oid长度为1-255个字符")
    private String memoryUse;
    /**
     * 磁盘总容量oid
     */
    @Schema(description = "磁盘总容量oid")
    @NotNull(message = "磁盘总容量oid不能为空")
    @Size(max =255, message = "内存总容量oid长度为1-255个字符")
    private String diskAll;
    /**
     * 磁盘使用量oid
     */
    @Schema(description = "磁盘使用量oid")
    @NotNull(message = "磁盘使用量oid不能为空")
    @Size(max =255, message = "内存总容量oid长度为1-255个字符")
    private String diskUse;

    @Schema(description = "资产类型图标")
    private String icon;
}
