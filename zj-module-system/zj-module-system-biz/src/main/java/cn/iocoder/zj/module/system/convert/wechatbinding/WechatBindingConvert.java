package cn.iocoder.zj.module.system.convert.wechatbinding;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.system.controller.admin.wechatbinding.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.wechatbinding.WechatBindingDO;

/**
 * 微信公众号OpenId与租户绑定关系 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface WechatBindingConvert {

    WechatBindingConvert INSTANCE = Mappers.getMapper(WechatBindingConvert.class);

    WechatBindingDO convert(WechatBindingCreateReqVO bean);

    WechatBindingDO convert(WechatBindingUpdateReqVO bean);

    WechatBindingRespVO convert(WechatBindingDO bean);

    List<WechatBindingRespVO> convertList(List<WechatBindingDO> list);

    PageResult<WechatBindingRespVO> convertPage(PageResult<WechatBindingDO> page);

    List<WechatBindingExcelVO> convertList02(List<WechatBindingDO> list);

}
