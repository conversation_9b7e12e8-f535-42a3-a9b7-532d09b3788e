package cn.iocoder.zj.module.system.controller.admin.ip;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.ip.core.Area;
import cn.iocoder.zj.framework.ip.core.utils.AreaUtils;
import cn.iocoder.zj.framework.ip.core.utils.IPUtils;
import cn.iocoder.zj.module.system.controller.admin.ip.vo.AreaNodeRespVO;
import cn.iocoder.zj.module.system.convert.ip.AreaConvert;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 地区")
@RestController
@RequestMapping("/system/area")
@Validated
public class AreaController {

    @GetMapping("/tree")
    @Operation(summary = "获得地区树")
    @Parameter(name = "areaId", description = "areaId地区id", required = false)
    public CommonResult<List<AreaNodeRespVO>> getAreaTree(@RequestParam(required = false, defaultValue = "") String areaId) {
        Area area = null;
        if (StrUtil.isNotEmpty(areaId)) {
            area = AreaUtils.getArea(Convert.toInt(areaId) );
        } else {
            area = AreaUtils.getArea(Area.ID_CHINA);
        }

        Assert.notNull(area, "获取不到中国");
        return success(AreaConvert.INSTANCE.convertList(area.getChildren()));
    }

    @GetMapping("/get-by-ip")
    @Operation(summary = "获得 IP 对应的地区名")
    @Parameter(name = "ip", description = "IP", required = true)
    public CommonResult<String> getAreaByIp(@RequestParam("ip") String ip) {
        // 获得城市
        Area area = IPUtils.getArea(ip);
        if (area == null) {
            return success("未知");
        }
        // 格式化返回
        return success(AreaUtils.format(area.getId()));
    }

}