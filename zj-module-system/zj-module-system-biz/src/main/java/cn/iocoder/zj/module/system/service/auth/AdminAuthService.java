package cn.iocoder.zj.module.system.service.auth;

import cn.iocoder.zj.module.system.controller.admin.auth.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.zj.module.system.enums.logger.LoginLogTypeEnum;
import cn.iocoder.zj.module.system.enums.logger.LoginResultEnum;
import com.taobao.api.ApiException;

import javax.validation.Valid;
import java.util.Map;

/**
 * 管理后台的认证 Service 接口
 *
 * 提供用户的登录、登出的能力
 *
 * <AUTHOR>
 */
public interface AdminAuthService {

    /**
     * 验证账号 + 密码。如果通过，则返回用户
     *
     * @param username 账号
     * @param password 密码
     * @return 用户
     */
    AdminUserDO authenticate(String username, String password);

    /**
     * 账号登录
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    AuthLoginRespVO login(@Valid AuthLoginReqVO reqVO);

    /**
     * 基于 token 退出登录
     *
     * @param token token
     * @param logType 登出类型
     */
    void logout(String token, Integer logType);

    /**
     * 短信验证码发送
     *
     * @param reqVO 发送请求
     */
    Boolean sendSmsCode(AuthSmsSendReqVO reqVO);

    /**
     * 短信登录
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    AuthLoginRespVO smsLogin(AuthSmsLoginReqVO reqVO) ;

    void createLoginLog(Long userId, String username,
                        LoginLogTypeEnum logTypeEnum, LoginResultEnum loginResult);

    /**
     * 社交快捷登录，使用 code 授权码
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    AuthLoginRespVO socialLogin(@Valid AuthSocialLoginReqVO reqVO);

    /**
     * 刷新访问令牌
     *
     * @param refreshToken 刷新令牌
     * @return 登录结果
     */
    AuthLoginRespVO refreshToken(String refreshToken);

    Boolean firstLogin(Long username);

    Boolean resetPassword(AuthResetPasswordReqVO reqVO);

    AuthLoginRespVO publicLogin(String code);

    AuthLoginRespVO scanQRLogin(Map<String, String> requestBody) throws ApiException;
}
