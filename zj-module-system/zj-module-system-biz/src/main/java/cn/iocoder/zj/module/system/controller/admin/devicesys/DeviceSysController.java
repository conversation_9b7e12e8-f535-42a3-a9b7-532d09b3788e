package cn.iocoder.zj.module.system.controller.admin.devicesys;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.system.controller.admin.devicesys.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.devicesys.DeviceSysDO;
import cn.iocoder.zj.module.system.convert.devicesys.DeviceSysConvert;
import cn.iocoder.zj.module.system.service.devicesys.DeviceSysService;

@Tag(name = "管理后台 - 系统类型")
@RestController
@RequestMapping("/system/device-sys")
@Validated
public class DeviceSysController {

    @Resource
    private DeviceSysService deviceSysService;

    @PostMapping("/create")
    @Operation(summary = "创建系统类型")
    @OperateLog(type=CREATE)
    @PreAuthorize("@ss.hasPermission('system:device-sys:create')")
    @PermitAll
    public CommonResult<Long> createDeviceSys(@Valid @RequestBody DeviceSysCreateReqVO createReqVO) {
        return success(deviceSysService.createDeviceSys(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新系统类型")
    @OperateLog(type=UPDATE)
    @PreAuthorize("@ss.hasPermission('system:device-sys:update')")
    @PermitAll
    public CommonResult<Boolean> updateDeviceSys(@Valid @RequestBody DeviceSysUpdateReqVO updateReqVO) {
        deviceSysService.updateDeviceSys(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除系统类型")
    @OperateLog(type=DELETE)
    @PreAuthorize("@ss.hasPermission('system:device-sys:delete')")
    @Parameter(name = "id", description = "编号", required = true)
    @PermitAll
    public CommonResult<Boolean> deleteDeviceSys(@RequestParam("id") Long id) {
        deviceSysService.deleteDeviceSys(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得系统类型")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PermitAll
    public CommonResult<DeviceSysRespVO> getDeviceSys(@RequestParam("id") Long id) {
        DeviceSysDO deviceSys = deviceSysService.getDeviceSys(id);
        return success(DeviceSysConvert.INSTANCE.convert(deviceSys));
    }

    @GetMapping("/list")
    @Operation(summary = "获得系统类型列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PermitAll
    public CommonResult<List<DeviceSysRespVO>> getDeviceSysList(@RequestParam("ids") Collection<Long> ids) {
        List<DeviceSysDO> list = deviceSysService.getDeviceSysList(ids);
        return success(DeviceSysConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得系统类型分页")
    @PermitAll
    public CommonResult<PageResult<DeviceSysRespVO>> getDeviceSysPage(@Valid DeviceSysPageReqVO pageVO) {
        PageResult<DeviceSysDO> pageResult = deviceSysService.getDeviceSysPage(pageVO);
        return success(DeviceSysConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出系统类型 Excel")
    @PermitAll
    @OperateLog(type = EXPORT)
    public void exportDeviceSysExcel(@Valid DeviceSysExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<DeviceSysDO> list = deviceSysService.getDeviceSysList(exportReqVO);
        // 导出 Excel
        List<DeviceSysExcelVO> datas = DeviceSysConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "系统类型.xls", "数据", DeviceSysExcelVO.class, datas);
    }

    @GetMapping("/getDeviceSysSelect")
    @Operation(summary = "获得系统类型下拉框")
    @PermitAll
    public CommonResult<List<DeviceSysRespVO>> getDeviceSysSelect() {
        List<DeviceSysDO> list = deviceSysService.getDeviceSysSelect();
        return success(DeviceSysConvert.INSTANCE.convertList(list));
    }

}
