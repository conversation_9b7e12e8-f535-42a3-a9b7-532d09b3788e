package cn.iocoder.zj.module.system.controller.admin.metadata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 元数据 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class MetadataExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("类型")
    private String type;

    @ExcelProperty("文本")
    private String value;


    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
