package cn.iocoder.zj.module.system.controller.admin.region;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.system.controller.admin.region.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.region.RegionDO;
import cn.iocoder.zj.module.system.convert.region.RegionConvert;
import cn.iocoder.zj.module.system.service.region.RegionService;

@Tag(name = "管理后台 - 地区")
@RestController
@RequestMapping("/system/region")
public class RegionController {

    @Resource
    private RegionService regionService;

    @PostMapping("/tree")
    @PermitAll
    @Operation(summary = "获取地区树结构")
    public CommonResult<List<RegionTreeVO>> getRegionTree(){
        List<RegionDO> list = regionService.getAllRegion();
        List<RegionTreeVO> regionTree = RegionConvert.INSTANCE.buildMenuTree(list);
        return success(regionTree);
    }

}
