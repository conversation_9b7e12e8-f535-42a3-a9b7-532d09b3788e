package cn.iocoder.zj.module.system.controller.admin.socail;

import cn.iocoder.zj.framework.common.enums.UserTypeEnum;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.system.controller.admin.socail.vo.SocialUserBindReqVO;
import cn.iocoder.zj.module.system.controller.admin.socail.vo.SocialUserUnbindReqVO;
import cn.iocoder.zj.module.system.convert.social.SocialUserConvert;
import cn.iocoder.zj.module.system.service.social.SocialUserService;
import cn.iocoder.zj.module.system.util.wechat.CheckoutUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import static cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name =  "管理后台 - 社交用户")
@RestController
@RequestMapping("/system/social-user")
@Validated
public class SocialUserController {

    @Resource
    private SocialUserService socialUserService;

    @PostMapping("/bind")
    @Operation(summary = "社交绑定，使用 code 授权码")
    public CommonResult<Boolean> socialBind(@RequestBody @Valid SocialUserBindReqVO reqVO) {
        socialUserService.bindSocialUser(SocialUserConvert.INSTANCE.convert(getLoginUserId(), UserTypeEnum.ADMIN.getValue(), reqVO));
        return CommonResult.success(true);
    }

    @DeleteMapping("/unbind")
    @Operation(summary = "取消社交绑定")
    public CommonResult<Boolean> socialUnbind(@RequestBody SocialUserUnbindReqVO reqVO) {
        socialUserService.unbindSocialUser(getLoginUserId(), UserTypeEnum.ADMIN.getValue(), reqVO.getType(), reqVO.getOpenid());
        return CommonResult.success(true);
    }

}