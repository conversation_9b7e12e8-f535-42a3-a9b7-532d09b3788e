package cn.iocoder.zj.module.system.service.region;

import java.util.*;
import javax.validation.*;
import cn.iocoder.zj.module.system.controller.admin.region.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.region.RegionDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 地区 Service 接口
 *
 * <AUTHOR>
 */
public interface RegionService {

    /**
     * 创建地区
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createRegion(@Valid RegionCreateReqVO createReqVO);

    /**
     * 更新地区
     *
     * @param updateReqVO 更新信息
     */
    void updateRegion(@Valid RegionUpdateReqVO updateReqVO);

    /**
     * 删除地区
     *
     * @param id 编号
     */
    void deleteRegion(String id);

    /**
     * 获得地区
     *
     * @param id 编号
     * @return 地区
     */
    RegionDO getRegion(String id);

    /**
     * 获得地区列表
     *
     * @param ids 编号
     * @return 地区列表
     */
    List<RegionDO> getRegionList(Collection<String> ids);

    /**
     * 获得地区分页
     *
     * @param pageReqVO 分页查询
     * @return 地区分页
     */
    PageResult<RegionDO> getRegionPage(RegionPageReqVO pageReqVO);

    /**
     * 获得地区列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 地区列表
     */
    List<RegionDO> getRegionList(RegionExportReqVO exportReqVO);

    List<RegionTreeVO> getRegionTree();

    List<RegionDO> getAllRegion();
}
