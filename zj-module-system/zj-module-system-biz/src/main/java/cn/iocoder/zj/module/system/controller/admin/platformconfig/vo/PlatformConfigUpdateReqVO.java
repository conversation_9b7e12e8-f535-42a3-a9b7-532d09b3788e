package cn.iocoder.zj.module.system.controller.admin.platformconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

import static io.swagger.v3.oas.annotations.media.Schema.AccessMode.READ_WRITE;

@Schema(description = "管理后台 - 平台配置更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PlatformConfigUpdateReqVO extends PlatformConfigBaseVO {

    @Schema(description = "主键", required = true)
    @NotNull(message = "主键不能为空")
    private Long id;

    @Schema(description = "Whether to Detect", accessMode = READ_WRITE)
    private boolean detected;

    @Schema(description = "采集器id", accessMode = READ_WRITE)
    private String collectorId;
}
