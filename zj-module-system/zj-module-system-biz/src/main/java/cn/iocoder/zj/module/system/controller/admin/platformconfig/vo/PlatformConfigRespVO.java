package cn.iocoder.zj.module.system.controller.admin.platformconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 平台配置 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PlatformConfigRespVO extends PlatformConfigBaseVO {

    @Schema(description = "主键", required = true)
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

    @Schema(description = "租户名", required = true)
    private String tenantName;


    @Schema(description = "采集器名称", required = true)
    private String collectorName;

    @Schema(description = "采集器Id", required = true)
    private String collectorId;
}
