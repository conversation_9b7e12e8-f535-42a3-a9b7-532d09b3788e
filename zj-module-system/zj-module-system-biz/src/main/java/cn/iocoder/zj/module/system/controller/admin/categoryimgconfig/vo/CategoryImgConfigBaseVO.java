package cn.iocoder.zj.module.system.controller.admin.categoryimgconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 类别图片管理 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class CategoryImgConfigBaseVO {

    @Schema(description = "大类或者平台")
    private String category;

    @Schema(description = "小类")
    private String app;

    @Schema(description = "类型：1：资产  2：平台")
    private Integer type;

    @Schema(description = "icon")
    private String icon;

    @Schema(description = "详情icon")
    private String detailIcon;

}
