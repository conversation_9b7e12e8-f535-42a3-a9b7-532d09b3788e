package cn.iocoder.zj.module.system.service.devicesys;

import java.util.*;
import javax.validation.*;
import cn.iocoder.zj.module.system.controller.admin.devicesys.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.devicesys.DeviceSysDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 系统类型 Service 接口
 *
 * <AUTHOR>
 */
public interface DeviceSysService {

    /**
     * 创建系统类型
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDeviceSys(@Valid DeviceSysCreateReqVO createReqVO);

    /**
     * 更新系统类型
     *
     * @param updateReqVO 更新信息
     */
    void updateDeviceSys(@Valid DeviceSysUpdateReqVO updateReqVO);

    /**
     * 删除系统类型
     *
     * @param id 编号
     */
    void deleteDeviceSys(Long id);

    /**
     * 获得系统类型
     *
     * @param id 编号
     * @return 系统类型
     */
    DeviceSysDO getDeviceSys(Long id);

    /**
     * 获得系统类型列表
     *
     * @param ids 编号
     * @return 系统类型列表
     */
    List<DeviceSysDO> getDeviceSysList(Collection<Long> ids);

    /**
     * 获得系统类型分页
     *
     * @param pageReqVO 分页查询
     * @return 系统类型分页
     */
    PageResult<DeviceSysDO> getDeviceSysPage(DeviceSysPageReqVO pageReqVO);

    /**
     * 获得系统类型列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 系统类型列表
     */
    List<DeviceSysDO> getDeviceSysList(DeviceSysExportReqVO exportReqVO);

    List<DeviceSysDO> getDeviceSysSelect();
}
