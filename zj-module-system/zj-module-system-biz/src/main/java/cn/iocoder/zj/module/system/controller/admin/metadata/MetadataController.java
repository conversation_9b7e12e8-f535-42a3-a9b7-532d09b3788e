package cn.iocoder.zj.module.system.controller.admin.metadata;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.system.controller.admin.metadata.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.metadata.MetadataDO;
import cn.iocoder.zj.module.system.convert.metadata.MetadataConvert;
import cn.iocoder.zj.module.system.service.metadata.MetadataService;

@Tag(name = "管理后台 - 元数据")
@RestController
@RequestMapping("/system/metadata")
@Validated
public class MetadataController {

    @Resource
    private MetadataService metadataService;

    @PostMapping("/create")
    @Operation(summary = "创建元数据")
    @PreAuthorize("@ss.hasPermission('system:metadata:create')")
    public CommonResult<Long> createMetadata(@Valid @RequestBody MetadataCreateReqVO createReqVO) {
        return success(metadataService.createMetadata(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新元数据")
    public CommonResult<Boolean> updateMetadata(@Valid @RequestBody MetadataUpdateReqVO updateReqVO) {
        metadataService.updateMetadata(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除元数据")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:metadata:delete')")
    public CommonResult<Boolean> deleteMetadata(@RequestParam("id") Long id) {
        metadataService.deleteMetadata(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得元数据")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:metadata:query')")
    public CommonResult<MetadataRespVO> getMetadata(@RequestParam("id") Long id) {
        MetadataDO metadata = metadataService.getMetadata(id);
        return success(MetadataConvert.INSTANCE.convert(metadata));
    }

    @GetMapping("/getByType")
    @Operation(summary = "根据类型获得元数据")
    @Parameter(name = "type", description = "类型", required = true, example = "host")
    public CommonResult<MetadataRespVO> getByType(@RequestParam("type") String type) {
        MetadataDO metadata = metadataService.getMetadataByType(type);
        return success(MetadataConvert.INSTANCE.convert(metadata));
    }

    @GetMapping("/list")
    @Operation(summary = "获得元数据列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('system:metadata:query')")
    public CommonResult<List<MetadataRespVO>> getMetadataList(@RequestParam("ids") Collection<Long> ids) {
        List<MetadataDO> list = metadataService.getMetadataList(ids);
        return success(MetadataConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得元数据分页")
    @PreAuthorize("@ss.hasPermission('system:metadata:query')")
    public CommonResult<PageResult<MetadataRespVO>> getMetadataPage(@Valid MetadataPageReqVO pageVO) {
        PageResult<MetadataDO> pageResult = metadataService.getMetadataPage(pageVO);
        return success(MetadataConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出元数据 Excel")
    @PreAuthorize("@ss.hasPermission('system:metadata:export')")
    @OperateLog(type = EXPORT)
    public void exportMetadataExcel(@Valid MetadataExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<MetadataDO> list = metadataService.getMetadataList(exportReqVO);
        // 导出 Excel
        List<MetadataExcelVO> datas = MetadataConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "元数据.xls", "数据", MetadataExcelVO.class, datas);
    }

}
