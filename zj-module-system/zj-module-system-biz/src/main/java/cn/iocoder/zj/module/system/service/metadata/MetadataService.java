package cn.iocoder.zj.module.system.service.metadata;

import java.util.*;
import javax.validation.*;
import cn.iocoder.zj.module.system.controller.admin.metadata.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.metadata.MetadataDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 元数据 Service 接口
 *
 * <AUTHOR>
 */
public interface MetadataService {

    /**
     * 创建元数据
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMetadata(@Valid MetadataCreateReqVO createReqVO);

    /**
     * 更新元数据
     *
     * @param updateReqVO 更新信息
     */
    void updateMetadata(@Valid MetadataUpdateReqVO updateReqVO);

    /**
     * 删除元数据
     *
     * @param id 编号
     */
    void deleteMetadata(Long id);

    /**
     * 获得元数据
     *
     * @param id 编号
     * @return 元数据
     */
    MetadataDO getMetadata(Long id);

    /**
     * 获得元数据列表
     *
     * @param ids 编号
     * @return 元数据列表
     */
    List<MetadataDO> getMetadataList(Collection<Long> ids);

    /**
     * 获得元数据分页
     *
     * @param pageReqVO 分页查询
     * @return 元数据分页
     */
    PageResult<MetadataDO> getMetadataPage(MetadataPageReqVO pageReqVO);

    /**
     * 获得元数据列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 元数据列表
     */
    List<MetadataDO> getMetadataList(MetadataExportReqVO exportReqVO);

    MetadataDO getMetadataByType(String type);
}
