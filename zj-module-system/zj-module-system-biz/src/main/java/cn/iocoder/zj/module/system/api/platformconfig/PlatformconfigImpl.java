package cn.iocoder.zj.module.system.api.platformconfig;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import cn.iocoder.zj.module.system.convert.platformconfig.PlatformConfigConvert;
import cn.iocoder.zj.module.system.dal.dataobject.platformconfig.PlatformConfigDO;
import cn.iocoder.zj.module.system.service.platformconfig.PlatformConfigService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

/**
 * @ClassName : PlatformconfigImpl  //类名
 * @Description : 配置Api实现  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/31  15:47
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class PlatformconfigImpl implements PlatformconfigApi{
    @Resource
    PlatformConfigService platformConfigService;


    @Override
    public CommonResult<List<PlatformconfigDTO>> getPlatList() {
      List<PlatformConfigDO> list =  platformConfigService.getPlatList();
        return success(PlatformConfigConvert.INSTANCE.convertToDTO(list));
    }

    @Override
    public CommonResult<Map<Long,Date>> getPlatCreateTimeList() {
        List<PlatformConfigDO> list =  platformConfigService.getPlatList();
        HashMap<Long, Date> map = new HashMap<>();
        list.forEach(item->{
            Date date = Date.from(item.getCreateTime().atZone(ZoneId.systemDefault()).toInstant());
            map.put(item.getId(), date);
        });
        return success(map);
    }

    @Override
    public CommonResult<PlatformconfigDTO> getPlatConfig(PlatformconfigDTO req) {
        PlatformConfigDO platformConfig=  platformConfigService.getPlatformConfig(req.getId());
        PlatformconfigDTO resp = PlatformConfigConvert.INSTANCE.singleToDTO(platformConfig);
        return CommonResult.success(resp);
    }
    @Override
    public CommonResult<List<Map>> getPlatformSelectList(String tenantId) {
        return CommonResult.success(platformConfigService.getPlatformSelectList(tenantId));
    }

    @Override
    public CommonResult<List<Map>> getPlatformByTenantId(String tenantId) {
        return CommonResult.success(platformConfigService.getPlatformByTenantId(tenantId));
    }

    @Override
    public CommonResult<List<Map>> getPlatformSelectListByUserId(Long userId) {
        return CommonResult.success(platformConfigService.getPlatformSelectListAlerts(userId));
    }

    @Override
    public CommonResult<PlatformconfigDTO> getByConfigId(Long platformId) {
        PlatformConfigDO platformConfig = platformConfigService.getPlatformConfig(platformId);
        PlatformconfigDTO resp = PlatformConfigConvert.INSTANCE.singleToDTO(platformConfig);
        return CommonResult.success(resp);
    }

    @Override
    public CommonResult<List<String>> getByConfigMailList(Long platformId) {
        List<String> byConfigMailList = platformConfigService.getByConfigMailList(platformId);
        return CommonResult.success(byConfigMailList);
    }

    @Override
    public CommonResult<List<String>> getByTenantList(Long platformId) {
        List<String> tenantList = platformConfigService.getByTenantList(platformId);
        return CommonResult.success(tenantList);
    }

    @Override
    public List<Map> cloudPlatform() {
        return platformConfigService.cloudPlatform();
    }

    @Override
    @TenantIgnore
    public CommonResult<List<Map>> getPlatformListByUserId(String userId) {
        return CommonResult.success(platformConfigService.getPlatformListByUserId(userId));
    }

    @Override
    public CommonResult<List<Long>> getTenantsByConfigId(Long platformId) {
        return CommonResult.success(platformConfigService.getTenantsByConfigId(platformId));
    }

    @Override
    public CommonResult<List<PlatformconfigDTO>> getPlatformByIds(final List<Long> platformIds) {
        List<PlatformConfigDO> platformByIds = platformConfigService.getPlatformByIds(platformIds);
        List<PlatformconfigDTO> resp = BeanUtil.copyToList(platformByIds, PlatformconfigDTO.class);
        return CommonResult.success(resp);
    }
}
