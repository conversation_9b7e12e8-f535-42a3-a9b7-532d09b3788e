package cn.iocoder.zj.module.system.controller.admin.screen.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 系统设置大屏分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ScreenPageReqVO extends PageParam {

    @Schema(description = "首页模块")
    private String module;

    @Schema(description = "监控条目")
    private String monitorEntry;

    @Schema(description = "监控条目名称")
    private String monitorEntryName;

    @Schema(description = "资源类型")
    private String resourceType;

    @Schema(description = "资源类型名称")
    private String resourceTypeName;

    @Schema(description = "显示顺序")
    private Integer sort;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "地区id")
    private Long regionId;

    @Schema(description = "租户名称")
    private String tenantName;

    @Schema(description = "地区名称")
    private String regionName;

}
