package cn.iocoder.zj.module.system.convert.platformconfig;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import cn.iocoder.zj.module.system.dal.dataobject.platformtenant.PlatformTenantDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.system.controller.admin.platformconfig.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.platformconfig.PlatformConfigDO;

/**
 * 平台配置 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface PlatformConfigConvert {

    PlatformConfigConvert INSTANCE = Mappers.getMapper(PlatformConfigConvert.class);

    PlatformConfigDO convert(PlatformConfigCreateReqVO bean);

    PlatformConfigDO convert(PlatformConfigUpdateReqVO bean);

    PlatformConfigRespVO convert(PlatformConfigDO bean);

    List<PlatformConfigRespVO> convertList(List<PlatformConfigDO> list);


    List<PlatformConfigExcelVO> convertList02(List<PlatformConfigDO> list);

    List<PlatformconfigDTO> convertToDTO(List<PlatformConfigDO> list);

    List<PlatformConfigRespSelectVO> convertListSelect(List<PlatformConfigDO> list);

    PlatformconfigDTO singleToDTO(PlatformConfigDO platformConfig);

    PageResult<PlatformConfigRespVO> convertPage(PageResult<PlatformConfigDO> pageResult);
}
