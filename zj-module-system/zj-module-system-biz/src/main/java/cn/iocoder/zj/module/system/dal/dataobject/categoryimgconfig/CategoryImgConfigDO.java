package cn.iocoder.zj.module.system.dal.dataobject.categoryimgconfig;

import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 类别图片管理 DO
 *
 * <AUTHOR>
 */
@TableName("system_category_img_config")
@KeySequence("system_category_img_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CategoryImgConfigDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 大类或者平台
     */
    private String category;
    /**
     * 小类
     */
    private String app;
    /**
     * 类型：1：资产  2：平台
     */
    private Integer type;
    /**
     * icon
     */
    private String icon;

    /**
     * 详情icon
     */
    private String detailIcon;

}
