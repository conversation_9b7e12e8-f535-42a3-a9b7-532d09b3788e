package cn.iocoder.zj.module.system.api.wechat;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.system.api.wechat.dto.WechatBindingDTO;
import cn.iocoder.zj.module.system.dal.dataobject.wechatbinding.WechatBindingDO;
import cn.iocoder.zj.module.system.service.wechatbinding.WechatBindingService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
@Slf4j
public class WeChatSendApiImpl implements WeChatSendApi{

    @Resource
    private WechatBindingService wechatBindingService;

    @Override
    public CommonResult<Boolean> sendSingleWeChatToMember(Map<String,Object> templateParams) {
        try {
            wechatBindingService.sendSingleWeChatToMember(templateParams);
        }catch (Exception e){
            e.printStackTrace();
        }
         return CommonResult.success(true);
    }

    @Override
    public void sendSingleWeChatToTenant(String templateParams) {
        wechatBindingService.sendMessage(templateParams);
    }

    @Override
    @TenantIgnore
    public CommonResult<WechatBindingDTO> getWeChatBinding(String json) {
        com.alibaba.fastjson.JSONObject map = JSON.parseObject(json);
        Long tenantId = Long.valueOf(map.get("tenantId").toString());
        WechatBindingDO weChatBinding = wechatBindingService.getWeChatBinding(tenantId);
        WechatBindingDTO wechatBindingDTO = new WechatBindingDTO();
        if (weChatBinding==null){
            wechatBindingDTO.setIsEmail(false);
            wechatBindingDTO.setIsWechat(false);
        }else {
            BeanUtils.copyProperties(weChatBinding,wechatBindingDTO);
        }
        return success(wechatBindingDTO);
    }

    @Override
    public CommonResult<Boolean> sendSingleWeChatToAuthorization(Map<String, Object> templateParams) {
        try {
            log.info("进入公众号推送授权RPC接口数据为:"+templateParams);
            wechatBindingService.sendSingleWeChatToAuthorization(templateParams);
        }catch (Exception e){
            e.printStackTrace();
        }
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> sendMessage(String accessToken, String openId, String message) {
        wechatBindingService.sendMessage(accessToken, openId, message);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<String> getWechatAccessToken() {
        return CommonResult.success(wechatBindingService.getAccessToken());
    }

    @Override
    public CommonResult<Boolean> sendSingleWeChatToAuthorizationType(Map<String, Object> templateParams) {
        try {
            log.info("进入公众号推送授权申请状态RPC接口数据为:"+templateParams);
            wechatBindingService.sendSingleWeChatToAuthorizationType(templateParams);
        }catch (Exception e){
            e.printStackTrace();
        }
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> sendBpmMessage(Map<String, Object> templateParams) {
        try {
            log.info("进入公众号推送bpm:"+templateParams);
            wechatBindingService.sendBpmMessage(templateParams);
        }catch (Exception e){
            e.printStackTrace();
        }
        return CommonResult.success(true);
    }
}
