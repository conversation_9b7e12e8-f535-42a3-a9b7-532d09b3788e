package cn.iocoder.zj.module.system.controller.admin.region.vo;

import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 地区 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class RegionExcelVO {

    @ExcelProperty("地区主键编号")
    private String id;

    @ExcelProperty("地区名称")
    private String regionName;

    @ExcelProperty("地区缩写")
    private String regionShortName;

    @ExcelProperty("行政地区编号")
    private String regionCode;

    @ExcelProperty("地区父id")
    private String regionParentId;

    @ExcelProperty("地区级别 1-省、自治区、直辖市 2-地级市、地区、自治州、盟 3-市辖区、县级市、县")
    private Integer regionLevel;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private Date createTime;

}
