package cn.iocoder.zj.module.system.service.usercertificate;

import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.zj.module.system.controller.admin.usercertificate.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.usercertificate.UserCertificateDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.system.convert.usercertificate.UserCertificateConvert;
import cn.iocoder.zj.module.system.dal.mysql.usercertificate.UserCertificateMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.system.enums.ErrorCodeConstants.*;

/**
 * 授权凭证 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UserCertificateServiceImpl implements UserCertificateService {

    @Resource
    private UserCertificateMapper userCertificateMapper;

    @Override
    public Integer createUserCertificate(UserCertificateCreateReqVO createReqVO) {
        // 插入
        UserCertificateDO userCertificate = UserCertificateConvert.INSTANCE.convert(createReqVO);
        userCertificateMapper.insert(userCertificate);
        // 返回
        return userCertificate.getId();
    }

    @Override
    public void updateUserCertificate(UserCertificateUpdateReqVO updateReqVO) {
        // 校验存在
        validateUserCertificateExists(updateReqVO.getId());
        // 更新
        UserCertificateDO updateObj = UserCertificateConvert.INSTANCE.convert(updateReqVO);
        userCertificateMapper.updateById(updateObj);
    }

    @Override
    public void deleteUserCertificate(Integer id) {
        // 校验存在
        validateUserCertificateExists(id);
        // 删除
        userCertificateMapper.deleteById(id);
    }

    private void validateUserCertificateExists(Integer id) {
        if (userCertificateMapper.selectById(id) == null) {
            throw exception(USER_CERTIFICATE_NOT_EXISTS);
        }
    }

    @Override
    @TenantIgnore
    public UserCertificateDO getUserCertificate(String id) {
        return userCertificateMapper.selectById(id);
    }

    @Override
    public List<UserCertificateDO> getUserCertificateList(Long userId) {
        return userCertificateMapper.selectList(UserCertificateDO::getUserId, userId);
    }

    @Override
    @TenantIgnore
    public PageResult<UserCertificateDO> getUserCertificatePage(UserCertificatePageReqVO pageReqVO) {
        return userCertificateMapper.selectPage(pageReqVO);
    }

    @Override
    public List<UserCertificateDO> getUserCertificateList(UserCertificateExportReqVO exportReqVO) {
        return userCertificateMapper.selectList(exportReqVO);
    }

    @Override
    public UserCertificateInfoRespVO getUserCertificateInfo(String str) {
        return userCertificateMapper.selectByIdInfo(str);
    }

}
