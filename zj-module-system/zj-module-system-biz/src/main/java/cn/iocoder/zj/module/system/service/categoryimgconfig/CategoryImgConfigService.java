package cn.iocoder.zj.module.system.service.categoryimgconfig;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.system.controller.admin.categoryimgconfig.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.categoryimgconfig.CategoryImgConfigDO;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 类别图片管理 Service 接口
 *
 * <AUTHOR>
 */
public interface CategoryImgConfigService {

    /**
     * 创建类别图片管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCategoryImgConfig(@Valid CategoryImgConfigCreateReqVO createReqVO);

    /**
     * 更新类别图片管理
     *
     * @param updateReqVO 更新信息
     */
    void updateCategoryImgConfig(@Valid CategoryImgConfigUpdateReqVO updateReqVO);

    /**
     * 删除类别图片管理
     *
     * @param id 编号
     */
    void deleteCategoryImgConfig(Long id);

    /**
     * 获得类别图片管理
     *
     * @param id 编号
     * @return 类别图片管理
     */
    CategoryImgConfigDO getCategoryImgConfig(Long id);

    /**
     * 获得类别图片管理列表
     *
     * @param ids 编号
     * @return 类别图片管理列表
     */
    List<CategoryImgConfigDO> getCategoryImgConfigList(Collection<Long> ids);

    /**
     * 获得类别图片管理分页
     *
     * @param pageReqVO 分页查询
     * @return 类别图片管理分页
     */
    PageResult<CategoryImgConfigDO> getCategoryImgConfigPage(CategoryImgConfigPageReqVO pageReqVO);


    /**
     * 获得类别图片管理列表
     *
     * @param reqVO 查询条件
     * @return 类别图片管理列表
     */
    List<CategoryImgConfigDO> getCategoryImgConfigList(CategoryImgConfigReqVO reqVO);

    /**
     * 获得类别图片管理列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 类别图片管理列表
     */
    List<CategoryImgConfigDO> getCategoryImgConfigList(CategoryImgConfigExportReqVO exportReqVO);

    /**
     * 上传类别图片
     *
     * @param file 文件
     * @return 图片地址
     */
    String uploadCategoryImg(MultipartFile file);


}
