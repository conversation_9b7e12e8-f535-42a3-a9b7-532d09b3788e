package cn.iocoder.zj.module.system.service.dingtalk;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.iocoder.zj.framework.common.exception.ErrorCode;
import cn.iocoder.zj.framework.common.pojo.DingDingAuthorizationDTO;
import cn.iocoder.zj.framework.common.util.dingtalk.DingTalkUtil;
import cn.iocoder.zj.framework.common.util.dingtalk.QyWxUtils;
import cn.iocoder.zj.module.system.controller.admin.wechatbinding.vo.WeChatTemplateMsg;
import cn.iocoder.zj.module.system.dal.mysql.wechatbinding.WechatBindingMapper;
import com.google.gson.Gson;
import com.taobao.api.ApiException;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;


/**
 * 微信公众号OpenId与租户绑定关系 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class DingTalkSendServiceImpl implements DingTalkSendService {

    @Resource
    private WechatBindingMapper wechatBindingMapper;

    @Resource
    private RedisTemplate redisTemplate;

    @Value("${wx.mp.app-id}")
    private String appId;

    @Value("${wx.mp.secret}")
    private String secret;

    @Value("${wx.mp.template_id}")
    private String templateId;

    @Value("${wx.mp.authorization_id}")
    private String authorizationId;


    private String accessTokenUrl = "https://api.weixin.qq.com/cgi-bin/stable_token";

    private static final String SEND_MESSAGE_URL = "https://api.weixin.qq.com/cgi-bin/message/custom/send";

    public String getAccessToken() {
        String accessToken="";
        Object token = redisTemplate.opsForValue().get("wechat_access_token");
        if (redisTemplate.hasKey("wechat_access_token") && ObjectUtil.isNotEmpty(token) && token.toString()!=""){
            accessToken=String.valueOf(redisTemplate.opsForValue().get("wechat_access_token"));
        }else {
            HashMap<String, Object> map = new HashMap<>();
            map.put("grant_type","client_credential");
            map.put("appid",appId);
            map.put("secret",secret);
            map.put("force_refresh",false);
            Gson gson = new Gson();
            String json = gson.toJson(map);
            String resp = HttpUtil.post(accessTokenUrl,json);
            JSONObject result = JSONUtil.parseObj(resp);
            System.out.println("获取access_token:" + resp);
            accessToken = result.getStr("access_token");
            Long expired = Long.valueOf(result.getStr("expires_in"))-60;
            //同步微信token的过期时间,减60秒是为了提前更新，防止失效


            redisTemplate.delete("wechat_access_token");
            redisTemplate.opsForValue().set("wechat_access_token",accessToken,expired, TimeUnit.SECONDS);
        }
        return accessToken;
    }

    @Override
    public void sendSingleWeChatToMember(Map<String, Object> params) throws ApiException {
        Map<String, WeChatTemplateMsg> sendMag = new HashMap<>();
        String format = DateUtil.format(new Date(), "yyyy年MM月dd日 HH:mm");
        String app = String.valueOf(params.get("monitorName"));
        String objectStr = app.length() >= 20 ? app.substring(0, 17) + "..." : app;

        //告警项目
        String content = String.valueOf(params.get("content"));
        String itemStr = content.length() >= 20 ? content.substring(0, 17) + "..." : content;
        sendMag.put("thing3", new WeChatTemplateMsg(content.length()>=20?content.substring(0,17)+"...":content));

        String appKey = String.valueOf(params.get("appKey"));
        String appSecret = String.valueOf(params.get("appSecret"));
        String agentId = String.valueOf(params.get("agentId"));
        String dingtalkPhone = String.valueOf(params.get("dingtalkPhone"));
        String accessToken = DingTalkUtil.getAccessToken(appKey, appSecret);
        if(StringUtil.isEmpty(accessToken)){
            log.info("钉钉token获取为空" + accessToken);
            throw exception(new ErrorCode(1002000008, "钉钉token获取为空"));
        }

        DingDingAuthorizationDTO dingDingAuthorization = new DingDingAuthorizationDTO();
        dingDingAuthorization.setAppKey(appKey);
        dingDingAuthorization.setAppSecret(appSecret);
        dingDingAuthorization.setAgentId(agentId);
        dingDingAuthorization.setMobile(dingtalkPhone);
        //告警时间
        dingDingAuthorization.setWarningTime(format);
        //告警对象
        dingDingAuthorization.setWarningObject(objectStr);
        //告警项目
        dingDingAuthorization.setWarningItem(itemStr);
        dingDingAuthorization.setUrl("");
        DingTalkUtil.sendDingDingMessage(dingDingAuthorization,accessToken,"Oa");
    }

    @Override
    public void sendSingleWeComToMember(Map<String, Object> params){
        Map<String, WeChatTemplateMsg> sendMag = new HashMap<>();
        String format = DateUtil.format(new Date(), "yyyy年MM月dd日 HH:mm");
        String app = String.valueOf(params.get("monitorName"));
        String objectStr = app.length() >= 20 ? app.substring(0, 17) + "..." : app;

        //告警项目
        String content = String.valueOf(params.get("content"));
        String itemStr = content.length() >= 20 ? content.substring(0, 17) + "..." : content;
        sendMag.put("thing3", new WeChatTemplateMsg(content.length()>=20?content.substring(0,17)+"...":content));

        String appKey = String.valueOf(params.get("appKey"));
        String appSecret = String.valueOf(params.get("appSecret"));
        String agentId = String.valueOf(params.get("agentId"));
        String wecomPhone = String.valueOf(params.get("wecomPhone"));
        String accessToken = QyWxUtils.getToken(appKey,appSecret);
        if(StringUtil.isEmpty(accessToken)){
            log.info("企微token获取为空" + accessToken);
            throw exception(new ErrorCode(1002000008, "企微token获取为空"));
        }

        DingDingAuthorizationDTO dingDingAuthorization = new DingDingAuthorizationDTO();
        dingDingAuthorization.setAppKey(appKey);
        dingDingAuthorization.setAppSecret(appSecret);
        dingDingAuthorization.setAgentId(agentId);
        dingDingAuthorization.setMobile(wecomPhone);
        //告警时间
        dingDingAuthorization.setWarningTime(format);
        //告警对象
        dingDingAuthorization.setWarningObject(objectStr);
        //告警项目
        dingDingAuthorization.setWarningItem(itemStr);
        dingDingAuthorization.setUrl("www");
        QyWxUtils.sendTextMsg(accessToken,dingDingAuthorization,"text");
    }
}
