package cn.iocoder.zj.module.system.service.licence;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.system.controller.admin.licence.LicenseVo;
import cn.iocoder.zj.module.system.dal.dataobject.info.InfoDO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * @ClassName : LicenceService  //类名
 * @Description : 监控授权  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/1/30  14:14
 */
public interface LicenceService {

    Map selectLicenByState(Long tenantId);
    Map getAssetInfoByTenantId(Long tenantId);

    String updateLicense(LicenseVo licenseVo);
    Map getLicence();

    String getAppCode();

    String creatLicenseCode();

    Map<String, String> getInfoUpload(MultipartFile file, HttpServletResponse response) throws IOException;

    String createLicense(LicenseVo licenseVo);
    PageResult<InfoDO> getLicenseList(LicenseVo licenseVo);
}
