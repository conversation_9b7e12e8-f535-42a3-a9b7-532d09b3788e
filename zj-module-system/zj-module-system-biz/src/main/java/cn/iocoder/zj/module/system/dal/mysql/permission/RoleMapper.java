package cn.iocoder.zj.module.system.dal.mysql.permission;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.module.system.controller.admin.permission.vo.role.RoleExportReqVO;
import cn.iocoder.zj.module.system.controller.admin.permission.vo.role.RolePageReqVO;
import cn.iocoder.zj.module.system.dal.dataobject.permission.RoleDO;
import jodd.util.StringUtil;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.Nullable;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Mapper
public interface RoleMapper extends BaseMapperX<RoleDO> {

    default PageResult<RoleDO> selectPage(RolePageReqVO reqVO) {
        LambdaQueryWrapperX<RoleDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.likeIfPresent(RoleDO::getName, reqVO.getName())
                .likeIfPresent(RoleDO::getCode, reqVO.getCode())
                .eqIfPresent(RoleDO::getStatus, reqVO.getStatus());
        if (StringUtil.isNotEmpty(reqVO.getSortDirection()) && reqVO.getSortDirection().equals("asc")){
            if (reqVO.getSortBy().equals("createTime")){
                wrapper.orderByAsc(RoleDO::getCreateTime);
            }
        }else {
            wrapper.orderByDesc(RoleDO::getCreateTime);
        }
        if(StringUtil.isNotEmpty(reqVO.getStartTime()) && StringUtil.isNotEmpty(reqVO.getEndTime())){
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            try {
                date = sdf.parse(reqVO.getEndTime());
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(date);
                // 把日期设置为当天最后一秒
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                date = calendar.getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            wrapper.betweenIfPresent(BaseDO::getCreateTime,reqVO.getStartTime(),sdf.format(date));
        }
        wrapper.orderByDesc(RoleDO::getId);
        return selectPage(reqVO, wrapper);
    }

    default List<RoleDO> selectList(RoleExportReqVO reqVO) {
        LambdaQueryWrapperX<RoleDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.likeIfPresent(RoleDO::getName, reqVO.getName())
                .likeIfPresent(RoleDO::getCode, reqVO.getCode())
                .eqIfPresent(RoleDO::getStatus, reqVO.getStatus());
        if(StringUtil.isNotEmpty(reqVO.getStartTime()) && StringUtil.isNotEmpty(reqVO.getEndTime())){
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            try {
                date = sdf.parse(reqVO.getEndTime());
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(date);
                // 把日期设置为当天最后一秒
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                date = calendar.getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            wrapper.betweenIfPresent(BaseDO::getCreateTime,reqVO.getStartTime(),sdf.format(date));
        }
        wrapper.orderByDesc(RoleDO::getId);
        return selectList(wrapper);
    }

    default RoleDO selectByName(String name) {
        return selectOne(RoleDO::getName, name);
    }

    default RoleDO selectByCode(String code) {
        return selectOne(RoleDO::getCode, code);
    }

    default List<RoleDO> selectListByStatus(@Nullable Collection<Integer> statuses) {
        return selectList(RoleDO::getStatus, statuses);
    }

    Set<Long> getRoleIdByCode(@Param("code") String code);

    Integer getIsRootOperation(@Param("id")Long id);

    Set<Long> getUserIdByCode(String code);
}
