package cn.iocoder.zj.module.system.controller.admin.platformconfig.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;

import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 平台配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PlatformConfigPageReqVO extends PageParam {

    @Schema(description = "用户账号")
    private String username;

    @Schema(description = "密码")
    private String password;

    @Schema(description = "平台地址")
    private String url;

    @Schema(description = "平台名称")
    private String name;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String startTime;

    @Schema(description = "结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String endTime;

    @Schema(description = "平台Id")
    private List<Long> platformIds;

    @Schema(description = "平台类型")
    private String typeCode;

    @Schema(description = "采集器id")
    private Long collectorId;

    private Long state;

}
