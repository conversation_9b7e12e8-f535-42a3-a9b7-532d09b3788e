package cn.iocoder.zj.module.system.service.devicesys;

import cn.iocoder.zj.module.monitor.api.asset.GatherAssetApi;
import cn.iocoder.zj.module.system.service.deviceoid.DeviceOidService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.zj.module.system.controller.admin.devicesys.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.devicesys.DeviceSysDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.system.convert.devicesys.DeviceSysConvert;
import cn.iocoder.zj.module.system.dal.mysql.devicesys.DeviceSysMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.system.enums.ErrorCodeConstants.*;

/**
 * 系统类型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DeviceSysServiceImpl implements DeviceSysService {

    @Resource
    private DeviceSysMapper deviceSysMapper;

    @Resource
    private DeviceOidService deviceOidService;

    @Resource
    private GatherAssetApi gatherAssetApi;

    @Override
    public Long createDeviceSys(DeviceSysCreateReqVO createReqVO) {
        //校验存在
        validateDeviceSysByType(createReqVO.getSysType());
        // 插入
        DeviceSysDO deviceSys = DeviceSysConvert.INSTANCE.convert(createReqVO);
        deviceSysMapper.insert(deviceSys);
        // 返回
        return deviceSys.getId();
    }

    @Override
    public void updateDeviceSys(DeviceSysUpdateReqVO updateReqVO) {
        // 校验存在
        validateDeviceSysExists(updateReqVO.getId());
        validateDeviceSysByType(updateReqVO.getSysType());
        // 更新
        DeviceSysDO updateObj = DeviceSysConvert.INSTANCE.convert(updateReqVO);
        deviceSysMapper.updateById(updateObj);
    }

    @Override
    public void deleteDeviceSys(Long id) {
        // 校验存在
        validateDeviceSysExists(id);
        // 校验正在使用
        validateDeviceSysUsed(id);
        // 删除
        deviceSysMapper.deleteById(id);
    }

    private void validateDeviceSysExists(Long id) {
        if (deviceSysMapper.selectById(id) == null) {
            throw exception(DEVICE_SYS_NOT_EXISTS);
        }
    }
    private void validateDeviceSysByType(String  type) {
        if (deviceSysMapper.selectOne("sys_type",type,"deleted",0) != null) {
            throw exception(DEVICE_SYS_EXISTS);
        }
    }

    private void validateDeviceSysUsed(Long id) {
        String sysType=deviceSysMapper.selectById(id).getSysType();
        if (deviceOidService.getCountByDeviceSysType(sysType) > 0) {
            throw exception(DEVICE_SYS_USED);
        }
        if (gatherAssetApi.getGatherAssetCountBySysType(sysType) > 0) {
            throw exception(DEVICE_SYS_USED_GATHER);
        }
    }

    @Override
    public DeviceSysDO getDeviceSys(Long id) {
        return deviceSysMapper.selectById(id);
    }

    @Override
    public List<DeviceSysDO> getDeviceSysList(Collection<Long> ids) {
        return deviceSysMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<DeviceSysDO> getDeviceSysPage(DeviceSysPageReqVO pageReqVO) {
        return deviceSysMapper.selectPage(pageReqVO);
    }

    @Override
    public List<DeviceSysDO> getDeviceSysList(DeviceSysExportReqVO exportReqVO) {
        return deviceSysMapper.selectList(exportReqVO);
    }

    @Override
    public List<DeviceSysDO> getDeviceSysSelect() {
        return deviceSysMapper.selectList();
    }

}
