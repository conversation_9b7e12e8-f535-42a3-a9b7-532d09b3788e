package cn.iocoder.zj.module.system.controller.admin.usersync.vo;

import cn.iocoder.zj.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 企业微信userid分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserSyncPageReqVO extends PageParam {

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "用户手机")
    private String phone;

    @Schema(description = "微信userId")
    private String wxUserId;

    @Schema(description = "用户名称")
    private String nickname;

    @Schema(description = "备注")
    private String remark;

}
