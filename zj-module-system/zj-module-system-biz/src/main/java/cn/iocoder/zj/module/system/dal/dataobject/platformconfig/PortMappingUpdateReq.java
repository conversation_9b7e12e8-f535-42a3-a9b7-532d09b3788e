package cn.iocoder.zj.module.system.dal.dataobject.platformconfig;

import lombok.Data;

/**
 * @ClassName : PortMappingUpdateReq  //类名
 * @Description :  端口映射更新请求 //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/10/14  16:17
 */
@Data
public class PortMappingUpdateReq {
    /**
     * id
     */
    private Integer id;
    /**
     * licenseId
     */
    private Integer licenseId;
    /**
     * 协议
     */
    private String protocal;
    /**
     * 子域名
     */
    private String subdomain;
    /**
     * 服务端端口
     */
    private Integer serverPort;
    /**
     * 客户端ip
     */
    private String clientIp;
    /**
     * 客户端端口
     */
    private Integer clientPort;
    /**
     * 上传限速
     */
    private String upLimitRate;
    /**
     * 下载限速
     */
    private String downLimitRate;
    /**
     * 代理响应数量（响应数据包数量，如果没有拆包则等于数据条数）
     */
    private Integer proxyResponses;
    /**
     * 代理超时时间
     */
    private Long proxyTimeoutMs;

    /**
     * 安全组Id
     */
    private Integer securityGroupId;

    /**
     * 描述
     */
    private String description;
    /**
     * 端口类型，API,VNC,HTTP
     */
    private String prodType;
}
