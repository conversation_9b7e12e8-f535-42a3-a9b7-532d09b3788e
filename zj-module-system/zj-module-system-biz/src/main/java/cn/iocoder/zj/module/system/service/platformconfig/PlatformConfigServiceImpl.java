package cn.iocoder.zj.module.system.service.platformconfig;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.cloud.module.cloudedge.api.monitor.MonitorApi;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.exception.MonitorDetectException;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.collector.api.PlatformInfoApi;
import cn.iocoder.zj.module.om.api.asset.MonitorassetApi;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import cn.iocoder.zj.module.system.controller.admin.platformconfig.vo.PlatformConfigCreateReqVO;
import cn.iocoder.zj.module.system.controller.admin.platformconfig.vo.PlatformConfigExportReqVO;
import cn.iocoder.zj.module.system.controller.admin.platformconfig.vo.PlatformConfigPageReqVO;
import cn.iocoder.zj.module.system.controller.admin.platformconfig.vo.PlatformConfigUpdateReqVO;
import cn.iocoder.zj.module.system.convert.platformconfig.PlatformConfigConvert;
import cn.iocoder.zj.module.system.dal.dataobject.platformconfig.PlatformConfigDO;
import cn.iocoder.zj.module.system.dal.dataobject.platformconfig.PortMappingUpdateReq;
import cn.iocoder.zj.module.system.dal.dataobject.platformtenant.PlatformTenantDO;
import cn.iocoder.zj.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.zj.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.zj.module.system.dal.mysql.platformconfig.PlatformConfigMapper;
import cn.iocoder.zj.module.system.dal.mysql.platformtenant.PlatformTenantMapper;
import cn.iocoder.zj.module.system.dal.mysql.tenant.TenantMapper;
import cn.iocoder.zj.module.system.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.system.mq.producer.platform.PlatformProducer;
import cn.iocoder.zj.module.system.service.permission.PermissionService;
import cn.iocoder.zj.module.system.service.permission.RoleService;
import cn.iocoder.zj.module.system.service.user.AdminUserService;
import cn.iocoder.zj.module.system.util.AreaName.AreaUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.MalformedURLException;
import java.net.Socket;
import java.net.URL;
import java.util.*;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.singleton;
import static cn.iocoder.zj.module.system.enums.ErrorCodeConstants.*;
import static cn.iocoder.zj.module.system.util.StringUtil.extractPort;

/**
 * 平台配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PlatformConfigServiceImpl implements PlatformConfigService {

    @Resource
    private PlatformConfigMapper platformConfigMapper;
    @Resource
    private PlatformTenantMapper platformTenantMapper;
    @Resource
    private TenantMapper tenantMapper;
    @Resource
    private PermissionService permissionService;
    @Resource
    private RoleService roleService;
    @Resource
    private AdminUserService adminUserService;
    @Resource
    PlatformRedisDAO platformRedisDAO;
    @Resource
    PlatformProducer platformProducer;
    @Resource
    private PermissionApi permissionApi;
    @Resource
    private MonitorApi monitorApi;
    @Resource
    private MonitorassetApi monitorassetApi;
    @Resource
    private PlatformInfoApi platformInfoApi;

    @Value("${proxy.url}")
    private String proxyUrl;
    @Value("${proxy.ip}")
    private String proxyIp;

    @Override
    public Long createPlatformConfig(PlatformConfigCreateReqVO createReqVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (StrUtil.isEmpty(createReqVO.getCollectorId())) {
            throw exception(PLATFORM_CONFIG_COLLECTOR_DUPLICATE, createReqVO.getCollectorId());
        }
        // 不是虚拟平台则进行校验
        if (!createReqVO.getTypeCode().equals("dummy")) {
//            validTenantNameDuplicate(createReqVO.getUrl(), loginUser.getTenantId());
        }
        // 插入
        PlatformConfigDO platformConfig = PlatformConfigConvert.INSTANCE.convert(createReqVO);
        String areaName = AreaUtil.getFullAreaName(createReqVO.getRegionId());
        platformConfig.setRegionName(areaName);
        platformConfig.setRegionId(createReqVO.getRegionId());
        platformConfigMapper.insert(platformConfig);
        // 增加平台配置同时，需要增加system_platform_tenant 关系表中数据，同时更新system_tenant 表中platform_json，platform_name
        // 业务逻辑执行部分
        TenantDO tenantDOs = tenantMapper.selectById(loginUser.getTenantId());
        insertPlatformTenant(platformConfig, tenantDOs, tenantDOs.getMaintainerId());
        // 平台json数据
        String pjson = tenantDOs.getPlatformJson();
        // 平台名称
        JSONArray jsonArray = new JSONArray();
        if (StringUtil.isNotEmpty(pjson)) {
            jsonArray = JSON.parseArray(pjson);
        }

        // Add a new entry
        JSONObject newEntry = new JSONObject();
        newEntry.put("platformId", platformConfig.getId());
        newEntry.put("platformName", platformConfig.getName());
        jsonArray.add(newEntry);

        String modifiedJson = jsonArray.toJSONString();

        String platformNames = jsonArray.stream()
                .map(obj -> ((JSONObject) obj).getString("platformName"))
                .reduce((name1, name2) -> StrUtil.join(", ", name1, name2))
                .orElse("");
        // todo 待修改 写死超级管理员id 可能会有问题
        tenantMapper.updatePlatformJsonById(loginUser.getTenantId(), modifiedJson, platformNames);
        // 如果有多条
        String collectorIdString = createReqVO.getCollectorId();
        String[] collectorIdArray = collectorIdString.split(",");
        List<String> collectorIdList = Arrays.asList(collectorIdArray);

        // 查询该平台是否绑定采集器
        List<Map> collector = tenantMapper.selectCollectorInfoByPlatformId(platformConfig.getId());
        if (collector.isEmpty()) {
            for (String collectorId : collectorIdList) {
                // 查询采集器数据
                Map<String, String> map = tenantMapper.selectCollectorName(collectorId);
                tenantMapper.insertCollectorInfo(platformConfig.getId(), platformConfig.getName(), map);
                // 刷新 job任务
                platformInfoApi.informationChange(map.get("name").toString());
            }
        } else {
            // 先删除在新增
            tenantMapper.deleteCollectorByPlatformId(platformConfig.getId());
            for (String collectorId : collectorIdList) {
                // 查询采集器数据
                Map<String, String> map = tenantMapper.selectCollectorName(collectorId);
                tenantMapper.insertCollectorInfo(platformConfig.getId(), platformConfig.getName(), map);
                // 刷新 job任务
                platformInfoApi.informationChange(map.get("name").toString());
            }
        }
        // 开通采集器相关端口
        List<Map> map = platformConfigMapper.getLicense(platformConfig.getId());
        // 如果开通了内网穿透相关数据则进行开通内网穿透相关协议
        if (map.size() > 0) {
            collectorAddPort(createReqVO, platformConfig.getId(), map);
        }
        // 返回
        platformProducer.sendPlatformSendMessageCollection(platformConfig.getId());

        return platformConfig.getId();

    }

    @Override
    public void updatePlatformConfig(PlatformConfigUpdateReqVO updateReqVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (StrUtil.isEmpty(updateReqVO.getCollectorId())) {
            throw exception(PLATFORM_CONFIG_COLLECTOR_DUPLICATE, updateReqVO.getCollectorId());
        }
        // 校验存在
        validatePlatformConfigExists(updateReqVO.getId());

        PlatformConfigDO platformConfigDO = platformConfigMapper.selectById(updateReqVO.getId());
        // 更新
        PlatformConfigDO updateObj = PlatformConfigConvert.INSTANCE.convert(updateReqVO);
        String areaName = AreaUtil.getFullAreaName(updateReqVO.getRegionId());
        updateObj.setRegionName(areaName);
        updateObj.setRegionId(updateReqVO.getRegionId());
        platformConfigMapper.updateById(updateObj);

        // 如果有多条
        String collectorIdString = updateReqVO.getCollectorId();
        String[] collectorIdArray = collectorIdString.split(",");
        List<String> collectorIdList = Arrays.asList(collectorIdArray);

        // 查询该平台是否绑定采集器
        List<Map> collector = tenantMapper.selectCollectorInfoByPlatformId(updateReqVO.getId());
        if (collector.isEmpty()) {
            for (String collectorId : collectorIdList) {
                // 查询采集器数据
                Map<String, String> map = tenantMapper.selectCollectorName(collectorId);
                tenantMapper.insertCollectorInfo(updateReqVO.getId(), updateReqVO.getName(), map);
                // 刷新 job任务
                platformInfoApi.informationChange(map.get("name").toString());
            }
        } else {
            // 先删除在新增
            tenantMapper.deleteCollectorByPlatformId(updateReqVO.getId());
            for (String collectorId : collectorIdList) {
                // 查询采集器数据
                Map<String, String> map = tenantMapper.selectCollectorName(collectorId);
                tenantMapper.insertCollectorInfo(updateReqVO.getId(), updateReqVO.getName(), map);
                // 刷新 job任务
                platformInfoApi.informationChange(map.get("name").toString());
            }
        }

        List<PlatformconfigDTO> platformList = collectorUpdatePort(updateReqVO, updateObj);
        List<PlatformConfigDO> listByDto = platformConfigMapper.selectListByDto();
        if (platformList.isEmpty()) {
            platformRedisDAO.set("platform", PlatformConfigConvert.INSTANCE.convertToDTO(listByDto));
        } else {
            platformRedisDAO.set("platform", platformList);
        }

        // 更新平台配置同时，需要更新system_platform_tenant 关系表中数据，同时更新system_tenant 表中platform_json，platform_names
        platformTenantMapper.updateByName(updateObj.getId(), updateObj.getName());

        List<Map> list1 = new ArrayList<>();
        for (PlatformconfigDTO platformTenantDO : platformList) {
            Map map = new HashMap();
            map.put("platformId", platformTenantDO.getId());
            map.put("platformName", platformTenantDO.getName());
            list1.add(map);
        }
        String jsonString = JSON.toJSONString(list1);

        String platformNames = list1.stream()
                .map(map -> (String) map.get("platformName"))
                .reduce((name1, name2) -> StrUtil.join(", ", name1, name2))
                .orElse("");

        // todo 待修改 写死超级管理员id 可能会有问题
        tenantMapper.updatePlatformJsonById(loginUser.getTenantId(), jsonString, platformNames);
        //修改关联监控和运维数据
        if (StrUtil.isNotEmpty(updateReqVO.getName()) && !updateReqVO.getName().equals(platformConfigDO.getName())) {
            monitorApi.updateMonitor(platformConfigDO.getId(), updateReqVO.getName());
            monitorassetApi.updateAsset(platformConfigDO.getId(), updateReqVO.getName());
            platformConfigMapper.updateAlert(platformConfigDO.getId(), updateReqVO.getName());
            platformConfigMapper.updateTop(platformConfigDO.getId(), updateReqVO.getName());
        }

        TenantDO tenantDOs = tenantMapper.selectById(loginUser.getTenantId());

    }


    @Override
    public void deletePlatformConfig(Long id) {
        // 校验存在
        validatePlatformConfigExists(id);
        // 删除
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        PlatformConfigDO platformConfigDO = platformConfigMapper.selectById(id);
        List<PlatformTenantDO> platformTenantDOList = platformTenantMapper.selectListByPlatformId(id);
        String collectorName = platformConfigMapper.getCollectorNameByPlatformId(id);

        platformConfigMapper.deleteById(id);
        platformTenantMapper.deleteByPlatformId(id);
        platformConfigMapper.deleteCollectorPlatform(id);
        // 刷新 job任务
        platformInfoApi.informationChange(collectorName);
        // 删除代理中的配置
        try {
            collectordeletePort(id);
        } catch (Exception e) {
            log.error("删除代理中的配置失败", e);
        }
        // sql 查询租户表含有该id的数据，拿到id 批量修改更新
        List<Map> listTenant = tenantMapper.selectUserByPlatformId(JSON.toJSONString(id));
        // 遍历查询结果
        for (Map<String, Object> resultMap : listTenant) {
            // 获取 platform_json 字段
            String platformJsonString = (String) resultMap.get("platform_json");
            // 解析 platform_json 字符串为 JSONArray
            JSONArray jsonArray = JSONArray.parseArray(platformJsonString);

            // 遍历 JSONArray 中的每个元素
            Iterator<Object> iterator = jsonArray.iterator();
            while (iterator.hasNext()) {
                JSONObject jsonObject = (JSONObject) iterator.next();
                Long platformId = jsonObject.getLong("platformId");
                // 如果 platformId 为 1，移除该元素
                if (platformId != null && platformId == id) {
                    iterator.remove();
                }
            }
            // 更新 platform_json 字段
            resultMap.put("platform_json", jsonArray.toJSONString());

            // 删除对应的 platform_names 字段中的值
            String platformNames = (String) resultMap.get("platform_names");
            if (platformNames != null) {
                String[] namesArray = platformNames.split(", ");
                StringBuilder updatedNames = new StringBuilder();
                for (String name : namesArray) {
                    if (!name.equals(platformConfigDO.getName())) {
                        if (updatedNames.length() > 0) {
                            updatedNames.append(", ");
                        }
                        updatedNames.append(name);
                    }
                }
                resultMap.put("platform_names", updatedNames.toString());
            }
        }

        if (CollectionUtil.isNotEmpty(listTenant)) {
            tenantMapper.updateBatchById(listTenant);
        }

        List<PlatformconfigDTO> platformList = platformRedisDAO.get("platform");
        if (!platformList.isEmpty()) {
            for (PlatformconfigDTO platformconfigDTO : platformList) {
                if (platformconfigDTO.getId().equals(id)) {
                    platformList.remove(platformconfigDTO);
                    break;
                }
            }
        }
        platformRedisDAO.set("platform", platformList);
        // 删除平台配置同时，需要删除system_platform_tenant 关系表中数据，同时更新system_tenant 表中platform_json，platform_names
        TenantDO tenantDOs = tenantMapper.selectById(loginUser.getTenantId());
        // 平台json数据
        String pjson = tenantDOs.getPlatformJson();
        if (StringUtil.isNotEmpty(pjson)) {
            JSONArray array = JSON.parseArray(pjson);
            Iterator<Object> iterator = array.iterator();
            while (iterator.hasNext()) {
                JSONObject jsonObject = (JSONObject) iterator.next();
                Long platformId = jsonObject.getLong("platformId");
                if (platformId != null && platformId == id) {
                    iterator.remove();
                }
            }
        }

        String trim = tenantDOs.getPlatformNames().replaceAll(",?\\s*" + platformConfigDO.getName(), "").trim();
        tenantMapper.updatePlatformJsonById(loginUser.getTenantId(), pjson, trim);
        platformProducer.sendPlatformSendMessage(id);

    }

    private void validatePlatformConfigExists(Long id) {
        if (platformConfigMapper.selectById(id) == null) {
            throw exception(PLATFORM_CONFIG_NOT_EXISTS);
        }
    }

    @TenantIgnore
    private void validTenantNameDuplicate(String url, Long tenantId) {
//        PlatformConfigPageReqVO platformConfigVO = new PlatformConfigPageReqVO();
//        platformConfigVO.setUrl(url);
        PlatformConfigDO platformConfigDO = platformConfigMapper.getByBaseInfoVo(url, tenantId);
        if (platformConfigDO == null) {
            return;
        }
        if (platformConfigDO.getUrl().equals(url)) {
            throw exception(PLATFORM_CONFIG_URL_DUPLICATE, url);
        }
    }

    @Override
    public PlatformConfigDO getPlatformConfig(Long id) {
        return platformConfigMapper.selectById(id);
    }

    @Override
    public List<PlatformConfigDO> getPlatformConfigList(Collection<Long> ids) {
        return platformConfigMapper.selectBatchIds(ids);
    }

    @Override
    @TenantIgnore
    public PageResult<PlatformConfigDO> getPlatformConfigPage(PlatformConfigPageReqVO pageReqVO) {
        Long collectorId = pageReqVO.getCollectorId();
        //查询对应平台id集合
        List<Long> platformIds = platformConfigMapper.getPlatFormListByCollectorId(collectorId);
        if (pageReqVO.getPlatformIds() == null) {
            pageReqVO.setPlatformIds(platformIds);
        } else {
            List<Long> platformIdList = pageReqVO.getPlatformIds();
            platformIdList.addAll(platformIds);
            pageReqVO.setPlatformIds(platformIdList);
        }
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        PageResult<PlatformConfigDO> result = new PageResult<>();
        if (!roleService.hasAnySuperAdmin(roleIds)) {
            AdminUserDO adminUserDO = adminUserService.getUser(loginUser.getId());
            if (!StringUtil.isEmpty(adminUserDO.getServiceTenantId())) {
                //如果是租户的运维服务人员，则查看被服务租户的平台
                List<Map> platformConfigDOList = platformConfigMapper.getPlatformSelectList(Arrays.asList(adminUserDO.getServiceTenantId().split(",")));
                if (CollectionUtil.isNotEmpty(platformConfigDOList)) {
                    List<Long> platformIdList = platformConfigDOList.stream()
                            .map(platformMap -> Long.valueOf(platformMap.get("platformId").toString()))
                            .toList();
                    if (!pageReqVO.getPlatformIds().isEmpty()) {
                        //筛选出平台id集合
                        List<Long> collect = platformIdList.stream()
                                .filter(pageReqVO.getPlatformIds()::contains)
                                .toList();
                        if (collect.isEmpty()) {
                            return new PageResult<>();
                        } else {
                            platformIdList = collect;
                        }
                    }
                    pageReqVO.setPlatformIds(platformIdList);
                    result = platformConfigMapper.selectPage(pageReqVO);
                } else {
                    result.setList(new ArrayList<>()).setTotal(0L);
                }
            } else {
                //租户自己查看自己的平台
                List<Map> platformConfigDOList = platformConfigMapper.getPlatformSelectList(Arrays.asList(String.valueOf(adminUserDO.getTenantId()).split(",")));
                if (CollectionUtil.isNotEmpty(platformConfigDOList)) {
                    List<Long> platformIdList = platformConfigDOList.stream()
                            .map(platformMap -> Long.valueOf(platformMap.get("platformId").toString()))
                            .toList();
                    if (!pageReqVO.getPlatformIds().isEmpty()) {
                        //筛选出平台id集合
                        List<Long> collect = platformIdList.stream()
                                .filter(pageReqVO.getPlatformIds()::contains)
                                .toList();
                        if (collect.isEmpty()) {
                            return new PageResult<>();
                        } else {
                            platformIdList = collect;
                        }
                    }
                    pageReqVO.setPlatformIds(platformIdList);
                    result = platformConfigMapper.selectPage(pageReqVO);
                } else {
                    result.setList(new ArrayList<>()).setTotal(0L);
                }
            }
        } else {
            result = platformConfigMapper.selectPage(pageReqVO);
        }
        return result;
    }

    @Override
    public List<PlatformConfigDO> getPlatformConfigList(PlatformConfigExportReqVO exportReqVO) {
        return platformConfigMapper.selectList(exportReqVO);
    }

    @Override
    public List<PlatformConfigDO> getPlatList() {
        return platformConfigMapper.selectListByDto();
    }

    @Override
    public List<Map> getPlatformSelectList(String tenantId) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        // 如果为null 可能是因为通过线程进入的没有拿到上下文所以单独处理
        AdminUserDO adminUserDO;
        Set<Long> roleIds;
        if (loginUser == null) {
            tenantId = tenantId;
            adminUserDO = adminUserService.getUser(adminUserService.getUserIdByTenantId(Convert.toLong(tenantId)));
            roleIds = permissionService.getUserRoleIdListByUserId(adminUserService.getUserIdByTenantId(Convert.toLong(tenantId)));
        } else {
            tenantId = String.valueOf(loginUser.getTenantId());
            adminUserDO = adminUserService.getUser(loginUser.getId());
            roleIds = permissionService.getUserRoleIdListByUserId(loginUser.getId());
        }
        // 获得用户的角色

        if (roleService.hasAnySuperAdmin(roleIds)) {
            return platformConfigMapper.getPlatformSelectList(null);
        } else {
            if (StringUtil.isNotEmpty(adminUserDO.getServiceTenantId())) {
                tenantId = adminUserDO.getServiceTenantId();
            }
            if (StrUtil.isNotEmpty(tenantId)) {
                return platformConfigMapper.getPlatformSelectList(Arrays.asList(tenantId.split(",")));
            } else {
                throw exception(500, "租户id未传递");
            }

        }

    }

    @Override
    public List<Map> getPlatformByTenantId(String tenantId) {
        log.info("getPlatformByTenantId 租户id为{}", tenantId);
        //根据租户id查询出用户
        Long uerId = adminUserService.getUserIdByTenantId(Long.parseLong(tenantId));
//        AdminUserDO adminUserDO = adminUserService.getUser(uerId);
        AdminUserDO adminUserDO = platformConfigMapper.getAdminUserId(uerId);
        log.info("adminUserDO 获取adminUser为{} 用户id{}", adminUserDO, uerId);
        // 获得用户的角色
        Set<Long> roleIds = permissionService.getUserRoleIdListByUserId(uerId);
        if (roleService.hasAnySuperAdmin(roleIds)) {
            return platformConfigMapper.getPlatformSelectList(null);
        } else {
            if (StringUtil.isNotEmpty(adminUserDO.getServiceTenantId())) {
                tenantId = adminUserDO.getServiceTenantId();
            }
            if (StrUtil.isNotEmpty(tenantId)) {
                return platformConfigMapper.getPlatformSelectList(Arrays.asList(tenantId.split(",")));
            } else {
                throw exception(500, "租户id未传递");
            }

        }

    }

    @Override
    public List<Map> getPlatformSelectListAlerts(Long userId) {
        AdminUserDO adminUserDO = adminUserService.getUser(userId);
        String tenantId = adminUserDO.getTenantId().toString();
        // 获得用户的角色
        Set<Long> roleIds = permissionService.getUserRoleIdListByUserId(adminUserDO.getId());
        if (roleService.hasAnySuperAdmin(roleIds)) {
            return platformConfigMapper.getPlatformSelectList(null);
        } else {
            if (StringUtil.isNotEmpty(adminUserDO.getServiceTenantId())) {
                tenantId = adminUserDO.getServiceTenantId();
            }
            if (StrUtil.isNotEmpty(tenantId)) {
                return platformConfigMapper.getPlatformSelectList(Arrays.asList(tenantId.split(",")));
            } else {
                throw exception(500, "租户id未传递");
            }
        }
    }

    @Override
    public List<String> getByConfigMailList(Long platformId) {
        return platformTenantMapper.getByConfigMailList(platformId);
    }

    @Override
    public List<String> getByTenantList(Long platformId) {
        return platformTenantMapper.getByTenantList(platformId);
    }

    @Override
    public List<Map> getPlatformProject() {
        return platformConfigMapper.getPlatformProject();
    }

    @Override
    public List<Map> cloudPlatform() {
        return platformConfigMapper.cloudPlatform();
    }

    @Override
    public void detectMonitor(Integer id) {
        List<PlatformconfigDTO> platformList = platformRedisDAO.get("platform");
        for (PlatformconfigDTO platform : platformList) {
            if (platform.getId().equals(Convert.toLong(id))) {
                try {
                    URL targetUrl = new URL(platform.getUrl());
                    String host = targetUrl.getHost(); // 获取主机名
                    int port = targetUrl.getPort(); // 获取端口号
                    if (port == -1) { // 如果URL中未明确指定端口号，使用默认端口
                        port = "https".equals(targetUrl.getProtocol()) ? 443 : 80;
                    }

                    // 尝试连接到主机和端口
                    try (Socket socket = new Socket()) {
                        // 设置连接超时时间，比如5000毫秒（5秒）
                        socket.connect(new InetSocketAddress(host, port), 5000);
                        // 如果到这里没有抛出异常，那么连接成功
                    }
                } catch (IOException e) {
                    // 如果有IO异常，表明连接失败（可以获取更具体的信息）
                    throw new MonitorDetectException("收集超时无响应");
                }
            }
        }


    }

    @Override
    public List<Map> getPlatformListByUserId(String userId) {
        AdminUserDO adminUserDO = adminUserService.getUser(Long.parseLong(userId));
        String tenantId = String.valueOf(adminUserDO.getTenantId());
        if (StringUtil.isNotEmpty(adminUserDO.getServiceTenantId())) {
            tenantId = adminUserDO.getServiceTenantId();
        }
        // 获得用户的角色
        Set<Long> roleIds = permissionService.getUserRoleIdListByUserId(Long.parseLong(userId));
        if (roleService.hasAnySuperAdmin(roleIds)) {
            return platformConfigMapper.getPlatformSelectList(null);
        } else {
            if (StrUtil.isNotEmpty(userId)) {
                return platformConfigMapper.getPlatformSelectList(Arrays.asList(tenantId.split(",")));
            } else {
                throw exception(500, "租户id未传递");
            }

        }
    }

    @Override
    public List<Long> getTenantsByConfigId(Long platformId) {
        return platformConfigMapper.getTenantsByConfigId(platformId);
    }

    @Override
    public List<Map> getPlatformCollector(List<String> idList) {
        return platformConfigMapper.getPlatformCollector(idList);
    }

    @Override
    public List<Map> getPlatformSelectByTenantIdList(String tenantId) {
        return platformConfigMapper.getPlatformSelectList(Arrays.asList(tenantId.split(",")));
    }

    @Override
    public List<PlatformConfigDO> getPlatformByIds(final List<Long> platformIds) {
        return platformConfigMapper.selectList(new LambdaQueryWrapperX<PlatformConfigDO>()
                .inIfPresent(PlatformConfigDO::getId, platformIds));
    }

    private void insertPlatformTenant(PlatformConfigDO platformConfig, TenantDO tenantDOs, String maintainerId) {
        PlatformTenantDO tenantDO = new PlatformTenantDO();
        tenantDO.setPlatformId(platformConfig.getId());
        tenantDO.setPlatformName(platformConfig.getName());
        tenantDO.setTenantId(tenantDOs.getId());
        tenantDO.setTenantName(tenantDOs.getName());
        platformTenantMapper.insert(tenantDO);

        // 如果有维护人员ID，插入对应的云平台用户数据
        if (maintainerId != null && !maintainerId.isEmpty()) {
            ArrayList<String> tenantDos = new ArrayList<>(Arrays.asList(maintainerId.split(",")));
            // 获取运维人员id根据运维人员id查询租户id
            Long maintainer = Convert.toLong(tenantDos.get(0));
            // 根据运维人员id查询租户id
            Long tenantId = tenantMapper.getTenantIdByUserId(maintainer);
            AdminUserDO adminUserDO = adminUserService.selectUserById(tenantId);
            PlatformTenantDO tenantDO1 = new PlatformTenantDO();
            tenantDO1.setPlatformId(platformConfig.getId());
            tenantDO1.setPlatformName(platformConfig.getName());
            tenantDO1.setTenantId(tenantId);  // Convert to Long
            // 1月8日修改，为了适配查询超级管理员下是哪个租户的名称而修改之前是根据超级管理员的名称来的，后改为租户名称
            tenantDO1.setTenantName(tenantDOs.getName());
            platformTenantMapper.insert(tenantDO1);

        }
    }


    private void collectorAddPort(PlatformConfigCreateReqVO platformConfigCreateReqVO, Long id, List<Map> map) {
        // 采集器平台

        // 采集器和内网穿透名称(对应的是代理地址key) 取第一个采集器作为代理地址
        Long licenseId = Convert.toLong(map.get(0).get("id"));
        // 根据名称查询内网穿透id
        Long licenseName = Convert.toLong(map.get(0).get("name"));
        String protocol = "";
        String host = "";
        int port = 0;
        try {
            URL url = new URL(StrUtil.toString(platformConfigCreateReqVO.getUrl()));
            // 使用URLUtil来解码url（如果它是编码的）
            host = URLUtil.decode(url.getHost());
            port = url.getPort();
            protocol = url.getProtocol();
            if (port == -1) { // 如果没有明确指定端口号，getPort()返回-1，此时应使用getDefaulPort()
                port = Convert.toInt(extractPort(platformConfigCreateReqVO.getUrl()));
            }
        } catch (MalformedURLException e) {
            log.error("Malformed URL: {}", platformConfigCreateReqVO.getUrl(), e);
        }

        String result = JSONObject.parseObject(HttpUtil.get(proxyUrl + "/port-mapping/port-server", Collections.singletonMap("id", licenseId))).get("data").toString();

        Map<String, Object> protMapping = new HashMap<>();
        protMapping.put("licenseId", licenseId);
        protMapping.put("licenseName", licenseName);
        protMapping.put("clientIp", host);
        protMapping.put("clientPort", port);
        protMapping.put("protocal", "TCP");
        protMapping.put("prodType", "API");
        protMapping.put("serverPort", Convert.toInt(result));
        // 创建端口映射
        JSONObject.parseObject(HttpUtil.post(proxyUrl + "/port-mapping/create", protMapping)).getJSONObject("data").toString();

        String url = String.format(protocol + "://%s:%s", proxyIp, Convert.toInt(result));
        List<PlatformconfigDTO> platformList = platformRedisDAO.get("platform");

        // 开通vnc端口
        String vncProd = createVncPort(platformConfigCreateReqVO.getConsoleIp(), platformConfigCreateReqVO.getConsoleProd(), platformConfigCreateReqVO.getUrl(), licenseId, licenseName);
        // 取缓存数据，新增由于缓存中没有所以直接加入缓存数据
        if (!platformList.isEmpty()) {
            PlatformconfigDTO platformconfigDTO = new PlatformconfigDTO();
            platformconfigDTO.setId(id);
            platformconfigDTO.setUsername(platformConfigCreateReqVO.getUsername());
            platformconfigDTO.setPassword(platformConfigCreateReqVO.getPassword());
            platformconfigDTO.setUrl(url);
            platformconfigDTO.setTypeName(platformConfigCreateReqVO.getTypeName());
            platformconfigDTO.setTypeCode(platformConfigCreateReqVO.getTypeCode());
            platformconfigDTO.setName(platformConfigCreateReqVO.getName());
            platformconfigDTO.setRegionName(platformConfigCreateReqVO.getRegionName());
            platformconfigDTO.setRegionId(platformConfigCreateReqVO.getRegionId());
            platformconfigDTO.setAddress(platformConfigCreateReqVO.getAddress());
            platformconfigDTO.setUrlHttp(platformConfigCreateReqVO.getUrlHttp());
            platformconfigDTO.setConsoleIp(proxyIp);
            platformconfigDTO.setConsoleProd(Convert.toInt(vncProd));
            platformconfigDTO.setAkType(platformConfigCreateReqVO.getAkType());
            platformconfigDTO.setTenantId(0L);
            platformList.add(platformconfigDTO);
        }

//            List<PlatformConfigDO> list = platformConfigMapper.selectListByDto();
        platformRedisDAO.set("platform", platformList);
    }

    @Nullable
    private String createVncPort(String consoleIp, Integer port, String url, Long licenseId, Long licenseName) {
        // 开通VNC端口
        String vncProd = null;
        String vncHost = "";
        if (Objects.nonNull(consoleIp) && port != null) {
            try {
                URL vncUrl = new URL(StrUtil.toString(consoleIp));
                // 使用URLUtil来解码url（如果它是编码的）
                vncHost = URLUtil.decode(vncUrl.getHost());
            } catch (MalformedURLException e) {
                log.error("Malformed URL: {}", url, e);
            }

            vncProd = JSONObject.parseObject(HttpUtil.get(proxyUrl + "/port-mapping/port-server", Collections.singletonMap("id", licenseId))).get("data").toString();
            Map<String, Object> vncMapping = new HashMap<>();
            vncMapping.put("licenseId", licenseId);
            vncMapping.put("licenseName", licenseName);
            vncMapping.put("clientIp", vncHost);
            vncMapping.put("clientPort", port);
            vncMapping.put("protocal", "TCP");
            vncMapping.put("prodType", "VNC");
            vncMapping.put("serverPort", Convert.toInt(vncProd));
            // 创建端口映射
            JSONObject.parseObject(HttpUtil.post(proxyUrl + "/port-mapping/create", vncMapping)).getJSONObject("data").toString();
        }
        return vncProd;
    }


    @NotNull
    private List<PlatformconfigDTO> collectorUpdatePort(PlatformConfigUpdateReqVO updateReqVO, PlatformConfigDO updateObj) {
        List<PlatformconfigDTO> platformList = platformRedisDAO.get("platform");
        if (!platformList.isEmpty()) {
            for (PlatformconfigDTO platformconfigDTO : platformList) {
                if (platformconfigDTO.getId().equals(updateObj.getId())) {
                    platformconfigDTO.setId(updateObj.getId());
                    platformconfigDTO.setUsername(updateObj.getUsername());
                    platformconfigDTO.setPassword(updateObj.getPassword());
                    platformconfigDTO.setTypeName(updateObj.getTypeName());
                    platformconfigDTO.setTypeCode(updateObj.getTypeCode());
                    platformconfigDTO.setName(updateObj.getName());
                    platformconfigDTO.setTenantId(0L);
                    platformconfigDTO.setRegionName(updateObj.getRegionName());
                    platformconfigDTO.setRegionId(updateObj.getRegionId());
                    platformconfigDTO.setAddress(updateObj.getAddress());
                    platformconfigDTO.setUrlHttp(updateObj.getUrlHttp());
                    platformconfigDTO.setAkType(updateObj.getAkType());
//                    platformconfigDTO.setConsoleIp(updateObj.getConsoleIp());
//                    platformconfigDTO.setConsoleProd(updateObj.getConsoleProd());
                    String host = "";
                    int port = 0;
                    String platformHost = "";
                    String protocol = "";
                    int platformPort = 0;
                    try {
                        URL newPlatformUrl = new URL(updateReqVO.getUrl());
                        URL url = new URL(StrUtil.toString(platformconfigDTO.getUrl()));
                        URL platformUrl = new URL(StrUtil.toString(updateObj.getUrl()));
                        // 使用URLUtil来解码url（如果它是编码的）
                        host = URLUtil.decode(url.getHost());
                        platformHost = URLUtil.decode(platformUrl.getHost());
                        port = url.getPort();
                        protocol = newPlatformUrl.getProtocol();
                        platformPort = platformUrl.getPort();
                        if (port == -1) { // 如果没有明确指定端口号，getPort()返回-1，此时应使用getDefaulPort()
                            port = Convert.toInt(extractPort(StrUtil.toString(platformconfigDTO.getUrl())));
                        }
                    } catch (MalformedURLException e) {
                        log.error("Malformed URL: {}", platformconfigDTO.getUrl(), e);
                    }
                    try {
                        // 查询采集器和内网穿透的关系
                        List<Map> map = platformConfigMapper.getLicense(updateReqVO.getId());
                        if (!map.isEmpty()) {

                            // 拿license ip 端口，进行查询prodId
                            Map portMapping = platformConfigMapper.getProdMapping(Convert.toStr(map.get(0).get("id")), Convert.toStr(port));

                            String portId = Convert.toStr(portMapping.get("id"));
                            String clientIp = Convert.toStr(portMapping.get("client_ip"));
                            int clientPort = Convert.toInt(portMapping.get("client_port"));

                            // 如果ip和端口被更新的话则进行更新操作
                            boolean isIpTrue = false;
                            boolean isPortTrue = false;
                            if (!clientIp.equals(platformHost)) {
                                isIpTrue = true;
                            }
                            if (clientPort != platformPort) {
                                isPortTrue = true;
                            }
                            if (isIpTrue || isPortTrue) {

                                HashMap<String, Object> detail = new HashMap<>();
                                detail.put("id", portId);
                                // 获取基础数据
                                String portJson = HttpUtil.get(proxyUrl + "/port-mapping/detail", detail);

                                PortMappingUpdateReq portMappingUpdateReq = JSONUtil.toBean(JSONUtil.parseObj(portJson).get("data").toString(), PortMappingUpdateReq.class);
                                portMappingUpdateReq.setClientIp(platformHost);
                                portMappingUpdateReq.setClientPort(platformPort);

                                HttpResponse result = HttpRequest.post(proxyUrl + "/port-mapping/update")
                                        .body(JSONUtil.toJsonStr(portMappingUpdateReq))
                                        .execute();
                                if (result.getStatus() != 200) {
                                    System.out.println("/port-mapping/update，远程接口更新失败");
                                }
                            }
                            String url = String.format(protocol + "://%s:%s", proxyIp, Convert.toInt(port));
                            platformconfigDTO.setUrl(url);

                            if (Objects.nonNull(platformconfigDTO.getConsoleIp()) && updateReqVO.getConsoleProd() != null) {
                                // 采集器和内网穿透名称(对应的是代理地址key) 取第一个采集器作为代理地址
                                Long licenseId = Convert.toLong(map.get(0).get("id"));
                                // 根据名称查询内网穿透id
                                Long licenseName = Convert.toLong(map.get(0).get("name"));
                                Map<String, Object> params = new HashMap<>();
                                params.put("ip", updateReqVO.getConsoleIp());
                                params.put("port", Convert.toStr(updateReqVO.getConsoleProd()));

                                String portExists = JSONObject.parseObject(HttpUtil.get(proxyUrl + "/port-mapping/portExists", params)).get("data").toString();
                                // 如果端口没有的话，则进行添加操作
                                if (Convert.toInt(portExists) == 0) {
                                    String vncPort = createVncPort(updateReqVO.getConsoleIp(), updateReqVO.getConsoleProd(), updateReqVO.getUrl(), licenseId, licenseName);

                                    platformconfigDTO.setConsoleIp(proxyIp);
                                    platformconfigDTO.setConsoleProd(Convert.toInt(vncPort));
                                } else {
                                    // 更新端口
                                    String oldIp = "";
                                    String newIp = "";
                                    int oldPort = 0;
                                    int newPort = 0;
                                    try {
                                        // 新的ip
                                        URL newUrl = new URL(StrUtil.toString(updateReqVO.getConsoleIp()));
                                        // 使用URLUtil来解码url（如果它是编码的）
                                        newIp = URLUtil.decode(newUrl.getHost());
                                        newPort = updateReqVO.getConsoleProd();
                                        oldPort = platformconfigDTO.getConsoleProd();
                                    } catch (MalformedURLException e) {
                                        log.error("Malformed URL: {}", platformconfigDTO.getUrl(), e);
                                    }
                                    // 拿license ip 端口，进行查询prodId
                                    Map vncPortMapping = platformConfigMapper.getProdMapping(Convert.toStr(map.get(0).get("id")), Convert.toStr(oldPort));
                                    String vncPortId = Convert.toStr(vncPortMapping.get("id"));
                                    String vnClientIp = Convert.toStr(vncPortMapping.get("client_ip"));
                                    int vnClientPort = Convert.toInt(vncPortMapping.get("client_port"));

                                    // 如果ip和端口被更新的话则进行更新操作
                                    boolean vncIsIpTrue = false;
                                    boolean vncIsPortTrue = false;
                                    if (!vnClientIp.equals(newIp)) {
                                        vncIsIpTrue = true;
                                    }
                                    if (vnClientPort != newPort) {
                                        vncIsPortTrue = true;
                                    }
                                    if (vncIsIpTrue || vncIsPortTrue) {

                                        HashMap<String, Object> detail = new HashMap<>();
                                        detail.put("id", vncPortId);
                                        // 获取基础数据
                                        String portJson = HttpUtil.get(proxyUrl + "/port-mapping/detail", detail);

                                        PortMappingUpdateReq vncPortMappingUpdateReq = JSONUtil.toBean(JSONUtil.parseObj(portJson).get("data").toString(), PortMappingUpdateReq.class);
                                        vncPortMappingUpdateReq.setClientIp(newIp);
                                        vncPortMappingUpdateReq.setClientPort(newPort);

                                        HttpResponse result = HttpRequest.post(proxyUrl + "/port-mapping/update")
                                                .body(JSONUtil.toJsonStr(vncPortMappingUpdateReq))
                                                .execute();
                                        if (result.getStatus() != 200) {
                                            System.out.println("/port-mapping/update，远程接口更新失败");
                                        }
                                    }
                                }


                            }
                        }
                    } catch (Exception e) {
                        log.error("调用接口异常: ", e);
                    }
                }
            }
        }
        return platformList;
    }


    private void collectordeletePort(Long id) {
        List<PlatformconfigDTO> platformList = platformRedisDAO.get("platform");
        if (platformList != null && !platformList.isEmpty()) {
            for (PlatformconfigDTO platformconfigDTO : platformList) {
                if (platformconfigDTO.getId().equals(id)) {
                    int port = 0;
                    try {
                        URL url = new URL(StrUtil.toString(platformconfigDTO.getUrl()));
                        port = url.getPort();
                        if (port == -1) {
                            port = Convert.toInt(extractPort(StrUtil.toString(platformconfigDTO.getUrl())));
                        }
                    } catch (MalformedURLException e) {
                        log.error("Malformed URL: {}", platformconfigDTO.getUrl(), e);
                    }
                    try {
                        // 查询采集器和内网穿透的关系
                        List<Map> map = platformConfigMapper.getLicense(id);
                        if (!map.isEmpty()) {
                            // 拿license ip 端口，进行查询prodId
                            Map portMapping = platformConfigMapper.getProdMapping(Convert.toStr(map.get(0).get("id")), Convert.toStr(port));
                            String portId = Convert.toStr(portMapping.get("id"));
                            HashMap<String, Object> delete = new HashMap<>();
                            delete.put("id", portId);
                            HttpUtil.post(proxyUrl + "/port-mapping/delete", delete);
                        }
                    } catch (Exception e) {
                        log.error("/port-mapping/delete,删除代理相关数据失败", e);
                    }
                    // 删除vnc端口
                    if (Objects.nonNull(platformconfigDTO.getConsoleIp()) && platformconfigDTO.getConsoleProd() != null) {
                        int vncPort = platformconfigDTO.getConsoleProd();
                        try {
                            // 查询采集器和内网穿透的关系
                            List<Map> map = platformConfigMapper.getLicense(id);
                            if (!map.isEmpty()) {

                                // 拿license ip 端口，进行查询prodId
                                Map portMapping = platformConfigMapper.getProdMapping(Convert.toStr(map.get(0).get("id")), Convert.toStr(vncPort));
                                String portId = Convert.toStr(portMapping.get("id"));
                                HashMap<String, Object> delete = new HashMap<>();
                                delete.put("id", portId);
                                HttpUtil.post(proxyUrl + "/port-mapping/delete", delete);

                            }
                        } catch (Exception e) {
                            log.error("/port-mapping/delete,删除代理相关数据失败", e);
                        }

                    }
                }
            }
        }
    }


}
