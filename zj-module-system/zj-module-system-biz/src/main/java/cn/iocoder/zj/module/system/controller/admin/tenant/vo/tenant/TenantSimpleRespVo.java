package cn.iocoder.zj.module.system.controller.admin.tenant.vo.tenant;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "租户精简信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TenantSimpleRespVo {
    @Schema(description = "租户编号", required = true, example = "1024")
    private Long id;

    @Schema(description = "租户名", required = true, example = "芋道")
    private String name;

    @Schema(description = "地区ID", required = true, example = "1003210")
    private String regionId;

    @Schema(description = "地区名称", example = "乌鲁木齐")
    private String regionName;
}
