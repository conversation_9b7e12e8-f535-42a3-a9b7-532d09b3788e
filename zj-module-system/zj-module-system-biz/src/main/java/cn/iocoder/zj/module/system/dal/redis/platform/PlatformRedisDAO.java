package cn.iocoder.zj.module.system.dal.redis.platform;

import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.zj.module.system.dal.redis.RedisKeyConstants.PLATFORM_LIST;


/**
 * @ClassName : PlatformRedisDAO  //类名
 * @Description : 平台配置缓存  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/7/27  17:38
 */
@Repository
public class PlatformRedisDAO {
    private static ObjectMapper objectMapper = new ObjectMapper();
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    public void set(String key, List<PlatformconfigDTO> list) {
        String redisKey = formatVmKey(key);
        try {
            stringRedisTemplate.opsForValue().set(redisKey, objectMapper.writeValueAsString(list), 8, TimeUnit.HOURS);
        } catch (JsonProcessingException e) {
            // 处理序列化异常
            e.printStackTrace();
        }
    }

    public List<PlatformconfigDTO> get(String key) {
        String redisKey = formatVmKey(key);
        String jsonValue = stringRedisTemplate.opsForValue().get(redisKey);
        if (jsonValue != null) {
            try {
                return objectMapper.readValue(jsonValue, new TypeReference<List<PlatformconfigDTO>>() {});
            } catch (JsonProcessingException e) {
                // 处理反序列化异常
                e.printStackTrace();
            }
        }
        return null;
    }
    private static String formatVmKey(String LabelKey) {
        return String.format(PLATFORM_LIST, LabelKey);
    }

}
