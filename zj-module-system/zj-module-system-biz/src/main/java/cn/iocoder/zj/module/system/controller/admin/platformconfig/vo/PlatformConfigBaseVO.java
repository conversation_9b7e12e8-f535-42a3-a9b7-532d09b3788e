package cn.iocoder.zj.module.system.controller.admin.platformconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.URL;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 平台配置 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class PlatformConfigBaseVO {

    @Schema(description = "用户账号")
    @Size(max = 30, message = "用户账号最大长度为30个字符")
    private String username;

    @Schema(description = "密码")
    @NotEmpty(message = "密码不能为空")
    @Length(min = 4, message = "密码长度不少于4位")
    @Pattern(regexp = "^[a-zA-Z0-9\\p{Punct}]+$", message = "密码只能包含字母、数字和特殊字符")
    private String password;

    @Schema(description = "平台地址")
    @URL(message = "平台地址非 URL 格式")
    @Size(max = 150, message = "平台地址最大长度为150个字符")
    private String url;

    @Schema(description = "平台名称")
    @Size(max = 100, message = "平台名称最大长度为100个字符")
    private String name;

    @Schema(description = "平台类型名称，从字典获取")
    private String typeName;

    @Schema(description = "平台类型编码，从字典获取")
    private String typeCode;

    @Schema(description = "地区id")
    private Long regionId;

    @Schema(description = "地区名称")
    private String regionName;

    @Size(max = 255, message = "平台名称最大长度为100个字符")
    @Schema(description = "机房位置")
    private String address;

    @Schema(description = "网页地址")
    private String urlHttp;

    @Schema(description = "控制台端口")
    private Integer consoleProd;

    @Schema(description = "控制台Ip地址")
    private String consoleIp;

    @Schema(description = "使用授权的状态：0：正常授权，1：使用ak授权")
    private Integer akType;

    @Schema(description = "平台在线状态 0 在线 1 离线")
    private Long state;


    @Schema(description = "时差，单位秒")
    private Long diffTime;
    @Schema(description = "采集时间（15分钟更新一次）")
    private Date onlineTime;
}
