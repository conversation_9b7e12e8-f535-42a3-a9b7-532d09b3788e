package cn.iocoder.zj.module.system.controller.admin.auth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;

@Schema(description = "管理后台 - 用户验证码重置密码Request VO")
@Data
public class AuthResetPasswordReqVO {

    /**
     * 手机号码
     */
    @Schema(description = "手机号")
    @NotEmpty(message = "手机号不能为空")
    @Pattern(regexp = "^[1][3-9]\\d{9}$", message = "手机号格式不正确")
    private String mobile;


    @Schema(description = "密码")
    @NotEmpty(message = "密码不能为空")
    @Length(min = 8, message = "密码长度不少于8位")
    @Pattern(regexp = "^[a-zA-Z0-9\\p{Punct}]+$", message = "密码只能包含字母、数字和特殊字符")
    private String password;


    @Schema(description = "短信验证码", required = true, example = "1024")
    @NotEmpty(message = "验证码不能为空")
    private String code;
}
