package cn.iocoder.zj.module.system.controller.admin.usercertificate;

import cn.hutool.core.convert.Convert;
import org.apache.hertzbeat.common.util.AesUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;

import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.system.controller.admin.usercertificate.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.usercertificate.UserCertificateDO;
import cn.iocoder.zj.module.system.convert.usercertificate.UserCertificateConvert;
import cn.iocoder.zj.module.system.service.usercertificate.UserCertificateService;

@Tag(name = "管理后台 - 授权凭证")
@RestController
@RequestMapping("/system/user-certificate")
@Validated
public class UserCertificateController {

    @Resource
    private UserCertificateService userCertificateService;

    @PostMapping("/create")
    @Operation(summary = "创建授权凭证")
    @PreAuthorize("@ss.hasPermission('system:user-certificate:create')")
    public CommonResult<Integer> createUserCertificate(@Valid @RequestBody UserCertificateCreateReqVO createReqVO) {
        createReqVO.setPassword(AesUtil.aesEncode(createReqVO.getPassword()));
        return success(userCertificateService.createUserCertificate(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新授权凭证")
    @PreAuthorize("@ss.hasPermission('system:user-certificate:update')")
    public CommonResult<Boolean> updateUserCertificate(@Valid @RequestBody UserCertificateUpdateReqVO updateReqVO) throws Exception {
        updateReqVO.setPassword(AesUtil.aesEncode(updateReqVO.getPassword()));
        userCertificateService.updateUserCertificate(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除授权凭证")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:user-certificate:delete')")
    public CommonResult<Boolean> deleteUserCertificate(@RequestParam("id") Integer id) {
        userCertificateService.deleteUserCertificate(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得授权凭证")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('system:user-certificate:query')")
    public CommonResult<UserCertificateInfoVO> getUserCertificate(@RequestParam("id") Integer id) {
        UserCertificateDO userCertificate = userCertificateService.getUserCertificate(Convert.toStr(id));
        return success(UserCertificateConvert.INSTANCE.convert2(userCertificate));
    }

    @GetMapping("/list")
    @Operation(summary = "获得授权凭证列表")
    @Parameter(name = "userId", description = "用户id", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('system:user-certificate:query')")
    public CommonResult<List<UserCertificateRespVO>> getUserCertificateList(@RequestParam("userId") Long userId) {
        List<UserCertificateDO> list = userCertificateService.getUserCertificateList(userId);
        return success(UserCertificateConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得授权凭证分页")
//    @PreAuthorize("@ss.hasPermission('system:user-certificate:query')")
    public CommonResult<PageResult<UserCertificateRespVO>> getUserCertificatePage(@Valid UserCertificatePageReqVO pageVO) {
        PageResult<UserCertificateDO> pageResult = userCertificateService.getUserCertificatePage(pageVO);
        return success(UserCertificateConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出授权凭证 Excel")
    @PreAuthorize("@ss.hasPermission('system:user-certificate:export')")
    @OperateLog(type = EXPORT)
    public void exportUserCertificateExcel(@Valid UserCertificateExportReqVO exportReqVO,
                                           HttpServletResponse response) throws IOException {
        List<UserCertificateDO> list = userCertificateService.getUserCertificateList(exportReqVO);
        // 导出 Excel
        List<UserCertificateExcelVO> datas = UserCertificateConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "授权凭证.xls", "数据", UserCertificateExcelVO.class, datas);
    }

    @GetMapping("/getinfo")
    @Operation(summary = "获得授权凭证")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('system:user-certificate:query')")
    public CommonResult<UserCertificateInfoRespVO> getUserCertificateInfo(@RequestParam("id") Integer id) {
        UserCertificateInfoRespVO userCertificate = userCertificateService.getUserCertificateInfo(Convert.toStr(id));
        return success(userCertificate);
    }


}
