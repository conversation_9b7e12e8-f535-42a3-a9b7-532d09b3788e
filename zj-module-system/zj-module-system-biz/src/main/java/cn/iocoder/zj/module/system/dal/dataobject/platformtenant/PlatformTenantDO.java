package cn.iocoder.zj.module.system.dal.dataobject.platformtenant;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 平台租户关系 DO
 *
 * <AUTHOR>
 */
@TableName("system_platform_tenant")
@KeySequence("system_platform_tenant_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlatformTenantDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;
    /**
     * 租户名称
     */
    private String tenantName;
    /**
     * 租户id
     */
    private Long tenantId;


}
