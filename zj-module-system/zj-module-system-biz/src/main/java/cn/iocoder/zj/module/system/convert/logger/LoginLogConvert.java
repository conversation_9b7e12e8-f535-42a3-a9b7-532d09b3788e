package cn.iocoder.zj.module.system.convert.logger;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.system.api.logger.dto.LoginLogCreateReqDTO;
import cn.iocoder.zj.module.system.controller.admin.logger.vo.loginlog.LoginLogExcelVO;
import cn.iocoder.zj.module.system.controller.admin.logger.vo.loginlog.LoginLogRespVO;
import cn.iocoder.zj.module.system.dal.dataobject.logger.LoginLogDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface LoginLogConvert {

    LoginLogConvert INSTANCE = Mappers.getMapper(LoginLogConvert.class);

    PageResult<LoginLogRespVO> convertPage(PageResult<LoginLogDO> page);

    List<LoginLogExcelVO> convertList(List<LoginLogDO> list);

    LoginLogDO convert(LoginLogCreateReqDTO bean);

    LoginLogRespVO convert(LoginLogDO loginLog);
}
