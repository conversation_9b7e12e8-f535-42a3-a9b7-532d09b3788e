package cn.iocoder.zj.module.system.dal.mysql.usersync;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.module.system.controller.admin.usersync.vo.UserSyncExportReqVO;
import cn.iocoder.zj.module.system.controller.admin.usersync.vo.UserSyncPageReqVO;
import cn.iocoder.zj.module.system.dal.dataobject.usersync.UserSyncDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 企业微信userid Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserSyncMapper extends BaseMapperX<UserSyncDO> {

    default PageResult<UserSyncDO> selectPage(UserSyncPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<UserSyncDO>()
                .eqIfPresent(UserSyncDO::getUserId, reqVO.getUserId())
                .eqIfPresent(UserSyncDO::getPhone, reqVO.getPhone())
                .eqIfPresent(UserSyncDO::getWxUserId, reqVO.getWxUserId())
                .likeIfPresent(UserSyncDO::getNickname, reqVO.getNickname())
                .eqIfPresent(UserSyncDO::getRemark, reqVO.getRemark())
                .orderByDesc(UserSyncDO::getWxcomId));
    }

    default List<UserSyncDO> selectList(UserSyncExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<UserSyncDO>()
                .eqIfPresent(UserSyncDO::getUserId, reqVO.getUserId())
                .eqIfPresent(UserSyncDO::getPhone, reqVO.getPhone())
                .eqIfPresent(UserSyncDO::getWxUserId, reqVO.getWxUserId())
                .likeIfPresent(UserSyncDO::getNickname, reqVO.getNickname())
                .eqIfPresent(UserSyncDO::getRemark, reqVO.getRemark())
                .orderByDesc(UserSyncDO::getWxcomId));
    }

}
