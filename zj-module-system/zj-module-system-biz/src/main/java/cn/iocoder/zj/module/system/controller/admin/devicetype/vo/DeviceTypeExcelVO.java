package cn.iocoder.zj.module.system.controller.admin.devicetype.vo;

import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 资产类型 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class DeviceTypeExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("资产类型名称")
    private String sysDeviceName;

    @ExcelProperty("资产类型type")
    private String sysDeviceType;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private Date createTime;

}
