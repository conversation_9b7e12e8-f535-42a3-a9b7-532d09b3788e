package cn.iocoder.zj.module.system.service.deviceoid;

import java.util.*;
import javax.validation.*;

import cn.iocoder.zj.module.system.api.deviceoid.dto.DeviceOidDTO;
import cn.iocoder.zj.module.system.controller.admin.deviceoid.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.deviceoid.DeviceOidDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * OID管理 Service 接口
 *
 * <AUTHOR>
 */
public interface DeviceOidService {

    /**
     * 创建OID管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDeviceOid(@Valid DeviceOidCreateReqVO createReqVO);

    /**
     * 更新OID管理
     *
     * @param updateReqVO 更新信息
     */
    void updateDeviceOid(@Valid DeviceOidUpdateReqVO updateReqVO);

    /**
     * 删除OID管理
     *
     * @param id 编号
     */
    void deleteDeviceOid(Long id);

    /**
     * 获得OID管理
     *
     * @param id 编号
     * @return OID管理
     */
    DeviceOidDO getDeviceOid(Long id);

    /**
     * 获得OID管理列表
     *
     * @param ids 编号
     * @return OID管理列表
     */
    List<DeviceOidDO> getDeviceOidList(Collection<Long> ids);

    /**
     * 获得OID管理分页
     *
     * @param pageReqVO 分页查询
     * @return OID管理分页
     */
    PageResult<DeviceOidDO> getDeviceOidPage(DeviceOidPageReqVO pageReqVO);

    /**
     * 获得OID管理列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return OID管理列表
     */
    List<DeviceOidDO> getDeviceOidList(DeviceOidExportReqVO exportReqVO);

    Map getDeviceOidListByDevice(String deviceType,String token_desc);

    Long getCountByDeviceSysType(String sysType);

    Long getCountByDeviceType(String sysDeviceType);
}
