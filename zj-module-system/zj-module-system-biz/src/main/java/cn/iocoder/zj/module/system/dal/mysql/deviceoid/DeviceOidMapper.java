package cn.iocoder.zj.module.system.dal.mysql.deviceoid;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.system.api.deviceoid.dto.DeviceOidDTO;
import cn.iocoder.zj.module.system.dal.dataobject.deviceoid.DeviceOidDO;
import cn.iocoder.zj.module.system.dal.dataobject.tenant.TenantDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.system.controller.admin.deviceoid.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * OID管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DeviceOidMapper extends BaseMapperX<DeviceOidDO> {

    default PageResult<DeviceOidDO> selectPage(DeviceOidPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DeviceOidDO>()
                .eqIfPresent(DeviceOidDO::getDeviceType, reqVO.getDeviceType())
                .likeIfPresent(DeviceOidDO::getDeviceName, reqVO.getDeviceName())
                .likeIfPresent(DeviceOidDO::getSysName, reqVO.getSysName())
                .eqIfPresent(DeviceOidDO::getSysType, reqVO.getSysType())
                .betweenIfPresent(DeviceOidDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(DeviceOidDO::getId));
    }

    default List<DeviceOidDO> selectList(DeviceOidExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DeviceOidDO>()
                .eqIfPresent(DeviceOidDO::getDeviceType, reqVO.getDeviceType())
                .likeIfPresent(DeviceOidDO::getDeviceName, reqVO.getDeviceName())
                .likeIfPresent(DeviceOidDO::getSysName, reqVO.getSysName())
                .eqIfPresent(DeviceOidDO::getSysType, reqVO.getSysType())
                .betweenIfPresent(DeviceOidDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(DeviceOidDO::getId));
    }

    default Long getCountByDeviceSysType(String sysType) {
        return selectCount(DeviceOidDO::getSysType, sysType);
    }

    default Long getCountByDeviceType(String sysDeviceType) {
        return selectCount(DeviceOidDO::getDeviceType, sysDeviceType);
    }

    List<DeviceOidDTO> getDeviceOidListByDevice(@Param("deviceType") String deviceType);
}
