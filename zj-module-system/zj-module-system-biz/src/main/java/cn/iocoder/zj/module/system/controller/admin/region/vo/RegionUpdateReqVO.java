package cn.iocoder.zj.module.system.controller.admin.region.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 地区更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RegionUpdateReqVO extends RegionBaseVO {

    @Schema(description = "地区主键编号", required = true)
    @NotNull(message = "地区主键编号不能为空")
    private String id;

}
