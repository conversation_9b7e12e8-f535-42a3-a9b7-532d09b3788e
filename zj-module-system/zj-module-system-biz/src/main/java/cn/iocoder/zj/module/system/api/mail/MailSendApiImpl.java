package cn.iocoder.zj.module.system.api.mail;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.system.api.mail.dto.MailSendSingleToUserReqDTO;
import cn.iocoder.zj.module.system.dal.dataobject.mail.MailTemplateDO;
import cn.iocoder.zj.module.system.service.mail.MailSendService;
import com.alibaba.fastjson.JSON;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class MailSendApiImpl implements MailSendApi {

    @Resource
    private MailSendService mailSendService;

    @Override
    @TenantIgnore
    public CommonResult<Long> sendSingleMailToAdmin(MailSendSingleToUserReqDTO reqDTO) {
        return success(mailSendService.sendSingleMailToAdmin(reqDTO.getMail(), reqDTO.getUserId(),
                reqDTO.getTemplateCode(), reqDTO.getTemplateParams(),reqDTO.getAlertMail()));
    }

    @Override
    public CommonResult<Long> sendSingleMailToMember(MailSendSingleToUserReqDTO reqDTO) {
        return success(mailSendService.sendSingleMailToMember(reqDTO.getMail(), reqDTO.getUserId(),
                reqDTO.getTemplateCode(), reqDTO.getTemplateParams()));
    }

    @Override
    public CommonResult<Map<String, String>> getTemplate(Long id) {
        MailTemplateDO result = mailSendService.getTemplate(id);
        return success(JSON.parseObject(JSON.toJSONString(result), Map.class)) ;
    }

    @Override
    @TenantIgnore
    public CommonResult<String> getTemplateByName(String name) {
        String code =mailSendService.getTemplateByName(name);
        return success(code);
    }

    @Override
    public CommonResult<Long> sendSingleMailToBpm(MailSendSingleToUserReqDTO reqDTO) {
        return success(mailSendService.sendSingleMailToBpm(reqDTO.getMail(), reqDTO.getUserId(),
                reqDTO.getTemplateCode(), reqDTO.getTemplateParams()));
    }
}
