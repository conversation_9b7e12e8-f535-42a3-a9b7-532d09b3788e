package cn.iocoder.zj.module.system.dal.dataobject.metadata;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 元数据 DO
 *
 * <AUTHOR>
 */
@TableName("system_metadata")
@KeySequence("system_metadata_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MetadataDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 类型
     */
    private String type;
    /**
     * 文本
     */
    private String value;

    /**
     * 租户id
     */
    private Long tenantId;

}
