package cn.iocoder.zj.module.system.convert.sms;

import cn.iocoder.zj.module.system.controller.admin.sms.vo.log.SmsLogExcelVO;
import cn.iocoder.zj.module.system.controller.admin.sms.vo.log.SmsLogRespVO;
import cn.iocoder.zj.module.system.dal.dataobject.sms.SmsLogDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 短信日志 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface SmsLogConvert {

    SmsLogConvert INSTANCE = Mappers.getMapper(SmsLogConvert.class);

    SmsLogRespVO convert(SmsLogDO bean);

    List<SmsLogRespVO> convertList(List<SmsLogDO> list);

    PageResult<SmsLogRespVO> convertPage(PageResult<SmsLogDO> page);

    List<SmsLogExcelVO> convertList02(List<SmsLogDO> list);

}
