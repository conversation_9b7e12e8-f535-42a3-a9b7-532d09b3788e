package cn.iocoder.zj.module.system.controller.admin.wechatbinding.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;

@Schema(description = "管理后台 - 微信公众号OpenId与租户绑定关系 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WechatBindingRespVO extends WechatBindingBaseVO {

    @Schema(description = "主键", required = true)
    private Long id;

    @Schema(description = "创建时间")
    private Date createTime;

}
