package cn.iocoder.zj.module.system.controller.admin.platformtenant.vo;

import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 平台租户关系 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class PlatformTenantExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("平台id")
    private Long platformId;

    @ExcelProperty("平台名称")
    private String platformName;

    @ExcelProperty("租户名称")
    private String tenantName;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private Date createTime;

}
