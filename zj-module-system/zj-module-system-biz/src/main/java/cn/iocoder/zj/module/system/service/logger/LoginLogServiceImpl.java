package cn.iocoder.zj.module.system.service.logger;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.zj.module.system.api.logger.dto.LoginLogCreateReqDTO;
import cn.iocoder.zj.module.system.controller.admin.logger.vo.loginlog.LoginLogExportReqVO;
import cn.iocoder.zj.module.system.controller.admin.logger.vo.loginlog.LoginLogPageReqVO;
import cn.iocoder.zj.module.system.convert.logger.LoginLogConvert;
import cn.iocoder.zj.module.system.dal.dataobject.logger.LoginLogDO;
import cn.iocoder.zj.module.system.dal.mysql.logger.LoginLogMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * 登录日志 Service 实现
 */
@Service
@Validated
public class LoginLogServiceImpl implements LoginLogService {

    @Resource
    private LoginLogMapper loginLogMapper;

    @Override
    public PageResult<LoginLogDO> getLoginLogPage(LoginLogPageReqVO reqVO) {
        return loginLogMapper.selectPage(reqVO);
    }

    @Override
    public List<LoginLogDO> getLoginLogList(LoginLogExportReqVO reqVO) {
        return loginLogMapper.selectList(reqVO);
    }

    @Override
    public void createLoginLog(LoginLogCreateReqDTO reqDTO) {
        LoginLogDO loginLog = LoginLogConvert.INSTANCE.convert(reqDTO);
        loginLogMapper.insert(loginLog);
    }

    @Override
    public LoginLogDO getPreviousLoginLog(Long userId) {
        LambdaQueryWrapper<LoginLogDO> lqw = new LambdaQueryWrapper<>();
        lqw.eq(LoginLogDO::getUserId,userId)
                .eq(BaseDO::getDeleted,0)
                .orderByDesc(LoginLogDO::getCreateTime)
                .last("LIMIT 1");
        return loginLogMapper.selectOne(lqw);
    }

}
