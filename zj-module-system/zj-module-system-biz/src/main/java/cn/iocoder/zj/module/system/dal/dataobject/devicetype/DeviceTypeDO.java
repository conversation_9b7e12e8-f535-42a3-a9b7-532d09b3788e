package cn.iocoder.zj.module.system.dal.dataobject.devicetype;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;

import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 资产类型 DO
 *
 * <AUTHOR>
 */
@TableName("system_device_type")
@KeySequence("system_device_type_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceTypeDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 资产类型名称
     */
    private String sysDeviceName;
    /**
     * 资产类型type
     */
    private String sysDeviceType;


    /**
     * @description: 图标
     * <AUTHOR>
     * @date 2023/8/14 19:37
     * @version 1.0
     */
    private String icon;

    private Integer checkbox;

    private Integer direction;

    private Integer sort;

}
