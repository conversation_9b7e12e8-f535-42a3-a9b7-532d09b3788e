package cn.iocoder.zj.module.system.controller.admin.platformconfig.vo;

import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 平台配置 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class PlatformConfigExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("用户账号")
    private String username;

    @ExcelProperty("密码")
    private String password;

    @ExcelProperty("平台地址")
    private String url;

    @ExcelProperty("平台名称")
    private String name;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private Date createTime;

}
