package cn.iocoder.zj.module.system.dal.dataobject.usersync;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 企业微信userid DO
 *
 * <AUTHOR>
 */
@TableName("wx_user_sync")
@KeySequence("wx_user_sync_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserSyncDO{

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long wxcomId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户手机
     */
    private String phone;
    /**
     * 微信userId
     */
    private String wxUserId;
    /**
     * 用户名称
     */
    private String nickname;
    /**
     * 备注
     */
    private String remark;

    private LocalDateTime createTime;
    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;

}
