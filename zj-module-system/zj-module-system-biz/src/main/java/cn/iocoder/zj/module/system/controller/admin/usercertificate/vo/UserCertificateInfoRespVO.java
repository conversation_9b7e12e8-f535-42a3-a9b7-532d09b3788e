package cn.iocoder.zj.module.system.controller.admin.usercertificate.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @ClassName : UserCertificateInfoRespVO  //类名
 * @Description : 详情  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/4/17  10:30
 */
@Schema(description = "管理后台 - 授权凭证详情 Response VO")
@Data
public class UserCertificateInfoRespVO {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "所有者id")
    private Long userId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "所有者名称")
    private String ownerName;

    @Schema(description = "授权账户")
    private String username;

    @Schema(description = "密码")
    private String password;

    @Schema(description = "私钥密码")
    private String passphrase;

    @Schema(description = "私钥")
    private String privateKey;

    @Schema(description = "账户类型")
    private String type;
}
