package cn.iocoder.zj.module.system.service.wechatbinding;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.iocoder.zj.framework.common.exception.ErrorCode;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.system.controller.admin.wechatbinding.vo.*;
import cn.iocoder.zj.module.system.convert.wechatbinding.WechatBindingConvert;
import cn.iocoder.zj.module.system.dal.dataobject.wechatbinding.WechatBindingDO;
import cn.iocoder.zj.module.system.dal.mysql.wechatbinding.WechatBindingMapper;
import cn.iocoder.zj.module.system.util.wechat.WxMessageUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.gson.Gson;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;


/**
 * 微信公众号OpenId与租户绑定关系 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class WechatBindingServiceImpl implements WechatBindingService {

    @Resource
    private WechatBindingMapper wechatBindingMapper;

    @Resource
    private RedisTemplate redisTemplate;

    @Value("${wx.mp.app-id}")
    private String appId;

    @Value("${wx.mp.secret}")
    private String secret;

    @Value("${wx.mp.template_id}")
    private String templateId;

    @Value("${wx.mp.authorization_id}")
    private String authorizationId;

    @Value("${wx.mp.authorization_type_id}")
    private String authorizationTypeId;

    @Value("${wx.mp.template_bpm_id}")
    private String templateBpmId;

    private String accessTokenUrl = "https://api.weixin.qq.com/cgi-bin/stable_token";

    private static final String SEND_MESSAGE_URL = "https://api.weixin.qq.com/cgi-bin/message/custom/send";

    @Override
    public String getAccessToken() {
        String accessToken = "";
        Object token = redisTemplate.opsForValue().get("wechat_access_token");
        if (redisTemplate.hasKey("wechat_access_token") && ObjectUtil.isNotEmpty(token) && token.toString() != "") {
            accessToken = String.valueOf(redisTemplate.opsForValue().get("wechat_access_token"));
        } else {
            HashMap<String, Object> map = new HashMap<>();
            map.put("grant_type", "client_credential");
            map.put("appid", appId);
            map.put("secret", secret);
            map.put("force_refresh", false);
            Gson gson = new Gson();
            String json = gson.toJson(map);
            String resp = HttpUtil.post(accessTokenUrl, json);
            JSONObject result = JSONUtil.parseObj(resp);
            System.out.println("获取access_token:" + resp);
            accessToken = result.getStr("access_token");
            Long expired = Long.valueOf(result.getStr("expires_in")) - 60;
            //同步微信token的过期时间,减60秒是为了提前更新，防止失效


            redisTemplate.delete("wechat_access_token");
            redisTemplate.opsForValue().set("wechat_access_token", accessToken, expired, TimeUnit.SECONDS);
        }
        return accessToken;
    }

    @Override
    public String getTemporaryQR(String accessToken, String userId) {
        //获取数据的地址（微信提供）
        String url = "https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=" + accessToken + "";

        //发送给微信服务器的数据 expire_seconds为时间，单位秒，最大2592000,30天，这里设置120秒，QR_STR_SCENE标识可以设置字符串
        String jsonStr = "{\"expire_seconds\": 120,\"action_name\": \"QR_STR_SCENE\", \"action_info\": {\"scene\": {\"scene_str\": " + userId + "}}}";

        //将得到的字符串转化成json对象
        String ticketJson = HttpUtil.post(url, jsonStr);
        System.out.println(ticketJson);
        JSONObject result = JSONUtil.parseObj(ticketJson);
        String ticket = result.getStr("ticket");
        return ticket;

    }

    @Override
    @TenantIgnore
    public void callback(HttpServletRequest request, HttpServletResponse response) throws Exception {
        request.setCharacterEncoding("UTF-8");
        response.setCharacterEncoding("UTF-8");
        Map<String, String> requestMap = WxMessageUtil.parseXml(request);
        log.info("解析====>{}", request);
        String messageType = requestMap.get("MsgType");
        log.info("微信类型===>{}", messageType);
        // 发送方帐号（open_id）
        String openid = requestMap.get("FromUserName");
        log.info("openId==================" + openid);
        // 公众帐号
        String toUserName = requestMap.get("ToUserName");
        // 消息类型
        String msgType = requestMap.get("MsgType");
        String event = requestMap.get("Event");
        log.info("event==================" + event);
        //扫码 eventKey为扫码绑定的值,方便区分, qrscene_ 为未关注用户的前缀
        String eventKey = requestMap.get("EventKey");
        log.info("requestMap==================" + requestMap);
        log.info("eventKey==================" + eventKey);

        //为未关注用户的前缀  qrscene_bind_123  关注是不会有qrscene_前缀信息的  bind可以作为我们扫码事件的标识,区分入口，123就是对应的id
        if (openid != null && openid != "" && event != null && event != "") {
            if ((event.equals("SCAN") || event.equals("subscribe")) && (eventKey != null && eventKey != "")) {
                if (event.equals("subscribe")) {
                    eventKey = eventKey.substring(8);
                }
                Long userId = Long.valueOf(eventKey);
                //判断是否为修改绑定
                //判断是否已经绑定用户
                LambdaQueryWrapper<WechatBindingDO> wrapper = new LambdaQueryWrapper<>();
                LambdaQueryWrapper<WechatBindingDO> queryWrapper = wrapper.eq(openid != null, WechatBindingDO::getOpenId, openid)
                        .ne(userId != null, WechatBindingDO::getUserId, userId)
                        .eq(WechatBindingDO::getDeleted, 0);
                WechatBindingDO wechatBindingDO = wechatBindingMapper.selectOne(queryWrapper);
                log.info("查询出的结果wechatBindingDO：" + wechatBindingDO);
                if (wechatBindingDO != null) {
                    //存入redis中
                    HashMap<String, String> map = new HashMap<>();
                    map.put("openId", openid);
                    map.put("newUserId", userId.toString());
                    map.put("oldUserId", wechatBindingDO.getUserId().toString());
                    Gson gson = new Gson();
                    String json = gson.toJson(map);
                    redisTemplate.delete("wechatBinding:" + userId);
                    redisTemplate.opsForValue().set("wechatBinding:" + userId, json, 1, TimeUnit.DAYS);
                    log.info("绑定校验存入redis中");
                    return;
                }
                LambdaQueryWrapper<WechatBindingDO> lqw = new LambdaQueryWrapper<>();
                lqw.eq(userId != null, WechatBindingDO::getUserId, userId);
                Long count = wechatBindingMapper.selectCount(lqw);
                if (count > 0) {
                    WechatBindingDO bindingDO = wechatBindingMapper.selectOne(lqw);
                    bindingDO.setOpenId(openid);
                    wechatBindingMapper.updateById(bindingDO);

                } else {
                    WechatBindingDO weChat = new WechatBindingDO();
                    weChat.setUserId(userId);
                    weChat.setOpenId(openid);
                    wechatBindingMapper.insert(weChat);
                }
                String accessToken = getAccessToken();
                String message = "您已成功绑定";
                //推送成功绑定消息
                sendMessage(accessToken, openid, message);
            } else if (event.equals("unsubscribe")) {
                LambdaQueryWrapper<WechatBindingDO> lqw = new LambdaQueryWrapper<>();
                lqw.eq(WechatBindingDO::getOpenId, openid).eq(BaseDO::getDeleted, 0);
                List<WechatBindingDO> wechatBindingDOS = wechatBindingMapper.selectList(lqw);
                for (WechatBindingDO wechatBindingDO : wechatBindingDOS) {
                    wechatBindingDO.setOpenId("");
                    wechatBindingMapper.updateById(wechatBindingDO);
                }

            }
        }


//
//        if ( event.equals("SCAN")) {
//
//            // todo 需要提供判断事件,确定是否为扫码绑定的
//
//        }else if (event != null && event.equals("subscribe") && eventKey!="") {
//            String userId = eventKey.substring(8);
//
//            //关注代码
//            // todo 需要提供判断事件,确定是否为扫码绑定的
//        }

//        if (event.equals("unsubscribe")) {
//            //取消关注代码
//            //删除openId为fromUser的数据,openId的值，从fromUser中取
//        }

    }


    public void sendMessage(String accessToken, String openId, String message) {
        OkHttpClient client = new OkHttpClient();

        String url = SEND_MESSAGE_URL + "?access_token=" + accessToken;

        String requestBody = "{\"touser\":\"" + openId + "\",\"msgtype\":\"text\",\"text\":{\"content\":\"" + message + "\"}}";

        Request request = new Request.Builder()
                .url(url)
                .post(RequestBody.create(MediaType.parse("application/json"), requestBody))
                .build();

        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();
            // 处理发送消息的结果
            log.info("处理微信发送的消息" + responseBody);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public Boolean isChange(Long userId) {
        return redisTemplate.hasKey("wechatBinding:" + userId);
    }

    @Override
    @TenantIgnore
    public Boolean change(Long userId) {
        if (redisTemplate.hasKey("wechatBinding:" + userId)) {
            String json = redisTemplate.opsForValue().get("wechatBinding:" + userId).toString();
            Gson gson = new Gson();
            Map<String, String> map = gson.fromJson(json, Map.class);
            //将旧的解绑
            String oldUserId = map.get("oldUserId");
            wechatBindingMapper.unbind(oldUserId);
            String openid = map.get("openId");
            LambdaQueryWrapper<WechatBindingDO> lqw = new LambdaQueryWrapper<>();
            lqw.eq(userId != null, WechatBindingDO::getUserId, userId);
            Long count = wechatBindingMapper.selectCount(lqw);
            if (count > 0) {
                WechatBindingDO bindingDO = wechatBindingMapper.selectOne(lqw);
                bindingDO.setOpenId(openid);
                wechatBindingMapper.updateById(bindingDO);

            } else {
                WechatBindingDO weChat = new WechatBindingDO();
                weChat.setUserId(userId);
                weChat.setOpenId(openid);
                wechatBindingMapper.insert(weChat);
            }
            String accessToken = getAccessToken();
            redisTemplate.delete("wechatBinding:" + userId);
            String message = "您已成功绑定";
            //推送成功绑定消息
            sendMessage(accessToken, openid, message);
            return true;
        } else {
            return false;
        }
    }

    @Override
    public void cancelChange(Long userId) {
        redisTemplate.delete("wechatBinding:" + userId);
    }


    @Override
    public void sendSingleWeChatToMember(Map<String, Object> params) {
        Map<String, WeChatTemplateMsg> sendMag = new HashMap<>();
        String format = DateUtil.format(new Date(), "yyyy年MM月dd日 HH:mm");


        //告警时间
        sendMag.put("time1", new WeChatTemplateMsg(format));
        //告警对象
        String app = String.valueOf(params.get("monitorName"));
        sendMag.put("thing4", new WeChatTemplateMsg(app.length() >= 20 ? app.substring(0, 17) + "..." : app));
        //告警项目
        String content = String.valueOf(params.get("content"));
        sendMag.put("thing3", new WeChatTemplateMsg(content.length() >= 20 ? content.substring(0, 17) + "..." : content));
        sendMag.put("thing13", new WeChatTemplateMsg(String.valueOf(params.get("platformName"))));
        sendMag.put("const17", new WeChatTemplateMsg(String.valueOf(params.get("priority"))));
        String accessToken = getAccessToken();
        getSendMessage(sendMag, accessToken, params.get("openId").toString(), params.get("url").toString());

    }

    @Override
    @TenantIgnore
    public void sendMessage(String json) {
        com.alibaba.fastjson.JSONObject map = JSON.parseObject(json);
        Map<String, WeChatTemplateMsg> sendMag = new HashMap<>();
        Date date = new Date(Long.valueOf(map.get("createTime").toString()));
        String format = DateUtil.format(date, "yyyy年MM月dd日 hh:mm");
        //告警时间
        sendMag.put("time2", new WeChatTemplateMsg(format));
        //所属租户
        sendMag.put("thing8", new WeChatTemplateMsg(map.get("hostName").toString()));
        //设备名称
        sendMag.put("thing5", new WeChatTemplateMsg(map.get("deviceName").toString()));
        //设备id
        sendMag.put("character_string21", new WeChatTemplateMsg(map.get("ip").toString()));
        //告警消息
        String alarm_log = map.get("alarmLog").toString();
        if (alarm_log.length() > 20) {
            alarm_log = alarm_log.substring(0, 20);
        }
        sendMag.put("thing3", new WeChatTemplateMsg(alarm_log));
        String accessToken = getAccessToken();
        String tenantId = map.get("tenantId").toString();
        if (tenantId != null && tenantId != "") {
            LambdaQueryWrapper<WechatBindingDO> lqw = new LambdaQueryWrapper<>();
            lqw.eq(WechatBindingDO::getUserId, tenantId).eq(BaseDO::getDeleted, 0).eq(WechatBindingDO::getIsWechat, 0);
            WechatBindingDO wechatBindingDO = wechatBindingMapper.selectOne(lqw);
            if (wechatBindingDO != null && wechatBindingDO.getIsWechat()) {
                getSendMessage(sendMag, accessToken, wechatBindingDO.getOpenId(), "");
            }
        }
    }

    //推送消息通用
    public void getSendMessage(Map<String, WeChatTemplateMsg> sendMag, String accessToken, String openId, String infoUrl) {
        // 模板参数
        // openId代表一个唯一微信用户，即微信消息的接收人
        // 公众号的模板id(也有相应的接口可以查询到)
        String url = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=" + accessToken;
        RestTemplate restTemplate = new RestTemplate();
        Map<String, Object> sendBody = new HashMap<>();
        sendBody.put("touser", openId);               // openId
        sendBody.put("url", infoUrl);         // 点击模板信息跳转地址
        sendBody.put("topcolor", "#FF0000");          // 顶色
        sendBody.put("data", sendMag);                   // 模板参数
        sendBody.put("template_id", templateId);      // 模板Id
        ResponseEntity<String> forEntity = restTemplate.postForEntity(url, sendBody, String.class);
        log.info("结果是: {}", forEntity.getBody());
        com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(forEntity.getBody());
        String messageCode = jsonObject.getString("errcode");
        String msgId = jsonObject.getString("msgid");
        System.out.println("messageCode : " + messageCode + ", msgId: " + msgId);
        log.info("result------------->" + forEntity.getBody());
    }


    //测试用
    @Override
    public String sendMessage1() {
        // 模板参数
        Map<String, WeChatTemplateMsg> sendMag = new HashMap<>();
        // openId代表一个唯一微信用户，即微信消息的接收人
        String openId = "ocabiwvHaq0HJPdRaNahAVkZTQwo";
        // 公众号的模板id(也有相应的接口可以查询到)
        String accessToken = getAccessToken();
        String url = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=" + accessToken;
//        sendMag.put("first", new WeChatTemplateMsg("f123"));
        sendMag.put("time2", new WeChatTemplateMsg("2023年8月14日"));
        sendMag.put("thing8", new WeChatTemplateMsg("所属租户"));
        sendMag.put("thing5", new WeChatTemplateMsg("宿主机"));
        sendMag.put("character_string21", new WeChatTemplateMsg("192.168.136.1"));
        sendMag.put("thing3", new WeChatTemplateMsg("宿主机CPU空闲率>=85%"));
        RestTemplate restTemplate = new RestTemplate();
        Map<String, Object> sendBody = new HashMap<>();
        sendBody.put("touser", openId);               // openId
        sendBody.put("url", "");         // 点击模板信息跳转地址
        sendBody.put("topcolor", "#FF0000");          // 顶色
        sendBody.put("data", sendMag);                   // 模板参数
        sendBody.put("template_id", templateId);      // 模板Id
        ResponseEntity<String> forEntity = restTemplate.postForEntity(url, sendBody, String.class);
        log.info("结果是: {}", forEntity.getBody());
        com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(forEntity.getBody());
        String messageCode = jsonObject.getString("errcode");
        String msgId = jsonObject.getString("msgid");
        System.out.println("messageCode : " + messageCode + ", msgId: " + msgId);
        return forEntity.getBody();
    }


    @Override
    @TenantIgnore
    public Long createWechatBinding(WechatBindingCreateReqVO createReqVO) {
        // 插入
        WechatBindingDO wechatBinding = WechatBindingConvert.INSTANCE.convert(createReqVO);
        Long userId = wechatBinding.getUserId();
        LambdaQueryWrapper<WechatBindingDO> lqw = new LambdaQueryWrapper<>();
        lqw.eq(userId != null, WechatBindingDO::getUserId, userId)
                .eq(BaseDO::getDeleted, 0);
        //判断表中是当前租户是否已经存在数据
        Long count = wechatBindingMapper.selectCount(lqw);
        if (count > 0) {
            WechatBindingDO bindingDO = wechatBindingMapper.selectOne(lqw);
            if (wechatBinding.getEmail() != null) {
                bindingDO.setEmail(wechatBinding.getEmail());
            }
            bindingDO.setIsWechat(wechatBinding.getIsWechat());
            bindingDO.setIsEmail(wechatBinding.getIsEmail());
            wechatBindingMapper.updateById(bindingDO);
        } else {
            wechatBindingMapper.insert(wechatBinding);
        }
        // 返回
        return wechatBinding.getId();
    }

    @Override
    public void updateWechatBinding(WechatBindingUpdateReqVO updateReqVO) {
        // 校验存在
        validateWechatBindingExists(updateReqVO.getId());
        // 更新
        WechatBindingDO updateObj = WechatBindingConvert.INSTANCE.convert(updateReqVO);
        wechatBindingMapper.updateById(updateObj);
    }

    @Override
    public void deleteWechatBinding(Long id) {
        // 校验存在
        validateWechatBindingExists(id);
        // 删除
        wechatBindingMapper.deleteById(id);
    }

    private void validateWechatBindingExists(Long id) {
        if (wechatBindingMapper.selectById(id) == null) {
//            throw exception(WECHAT_BINDING_NOT_EXISTS);
        }
    }

    @Override
    public WechatBindingDO getWechatBinding(Long id) {
        return wechatBindingMapper.selectById(id);
    }

    @Override
    public List<WechatBindingDO> getWechatBindingList(Collection<Long> ids) {
        return wechatBindingMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<WechatBindingDO> getWechatBindingPage(WechatBindingPageReqVO pageReqVO) {
        return wechatBindingMapper.selectPage(pageReqVO);
    }

    @Override
    public List<WechatBindingDO> getWechatBindingList(WechatBindingExportReqVO exportReqVO) {
        return wechatBindingMapper.selectList(exportReqVO);
    }

    @Override
    @TenantIgnore
    public WechatBindingDO getWeChatBinding(Long tenantId) {
        LambdaQueryWrapper<WechatBindingDO> lqw = new LambdaQueryWrapper<>();
        lqw.eq(tenantId != null, WechatBindingDO::getUserId, tenantId);
        WechatBindingDO wechatBindingDO = wechatBindingMapper.selectOne(lqw);
        return wechatBindingDO;
    }

    @Override
    @TenantIgnore
    public Boolean getIsOpenId(String tenantId) {
        Long aLong = Long.valueOf(tenantId);
        LambdaQueryWrapper<WechatBindingDO> lqw = new LambdaQueryWrapper<>();
        lqw.eq(aLong != null, WechatBindingDO::getUserId, aLong)
                .eq(BaseDO::getDeleted, 0);
        Long count = wechatBindingMapper.selectCount(lqw);
        if (count > 0) {
            WechatBindingDO wechatBindingDO = wechatBindingMapper.selectOne(lqw);
            if (wechatBindingDO.getOpenId() != null && wechatBindingDO.getOpenId().length() != 0) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    @Override
    public Boolean sendSingleWeChatToAuthorization(Map<String, Object> templateParams) {
        Map<String, WeChatTemplateMsg> sendMag = new HashMap<>();
        String format = DateUtil.format(new Date(), "yyyy年MM月dd日 HH:mm");

        //申请方

        sendMag.put("thing1", new WeChatTemplateMsg(templateParams.get("nickname").toString()));
        //数据主体
        sendMag.put("thing2", new WeChatTemplateMsg(templateParams.get("hostName").toString()));
        // 授权时间
        sendMag.put("time3", new WeChatTemplateMsg(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss")));
        // 产品名称
        String assetName = String.valueOf(templateParams.get("assetName").toString());
        sendMag.put("thing4", new WeChatTemplateMsg(assetName.length() >= 20 ? assetName.substring(0, 17) + "..." : assetName));

        String accessToken = getAccessToken();
        log.info("进入获取用户列表数据实现方法，sendSingleWeChatToAuthorization，模板数据为：" + templateParams);
        getSendMessage(sendMag, accessToken, templateParams.get("openId").toString(), templateParams.get("url").toString(), authorizationId);
        return true;

    }

    @Override
    public Boolean sendSingleWeChatToAuthorizationType(Map<String, Object> templateParams) {
        Map<String, WeChatTemplateMsg> sendMag = new HashMap<>();
        //授权ID
        sendMag.put("character_string7",new WeChatTemplateMsg(templateParams.get("assetsName").toString()));
        //授权状态
        sendMag.put("phrase4",new WeChatTemplateMsg(templateParams.get("type").toString()));
        //授权账号
        sendMag.put("thing3",new WeChatTemplateMsg(templateParams.get("authorizedAccount").toString()));
        String accessToken = getAccessToken();
        log.info("进入获取用户列表数据实现方法，sendSingleWeChatToAuthorizationType，模板数据为：" + templateParams);
        getSendMessage(sendMag, accessToken, templateParams.get("openId").toString(), templateParams.get("url").toString(), authorizationTypeId);
        return true;
    }

    @Override
    public void sendBpmMessage(Map<String, Object> templateParams) {
        Map<String, WeChatTemplateMsg> sendMag = new HashMap<>();
        sendMag.put("thing13",new WeChatTemplateMsg(templateParams.get("platformName").toString()));
        sendMag.put("thing6",new WeChatTemplateMsg(templateParams.get("content").toString()));
        sendMag.put("character_string14",new WeChatTemplateMsg(templateParams.get("number").toString()));
        sendMag.put("thing53",new WeChatTemplateMsg(templateParams.get("nickname").toString()));
        sendMag.put("time33",new WeChatTemplateMsg(templateParams.get("createTime").toString()));
        String accessToken = getAccessToken();
        getSendMessage(sendMag, accessToken, templateParams.get("openId").toString(), templateParams.get("url").toString(), templateBpmId);
    }


    @Override
    @TenantIgnore
    public Long getUserIdByCode(String code) {
        String openId = weiXinLoginGetOpenId(code);
        if (StringUtil.isEmpty(openId)) {
            log.info("openId获取失败");
            throw exception(new ErrorCode(40163, ""));
        }
        log.info("获取的openId:" + openId);
        LambdaQueryWrapper<WechatBindingDO> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StringUtil.isNotEmpty(openId), WechatBindingDO::getOpenId, openId)
                .eq(WechatBindingDO::getDeleted, 0);
        Long userId = null;
        try {
            userId = wechatBindingMapper.selectOne(lqw).getUserId();
            log.info("openId:openId获取的userId" + userId);
            return userId;
        } catch (NullPointerException e) {
            log.info("获取的openId未查询到对应用户，openId:" + openId);
            e.printStackTrace();
        }
        if (userId == null) {
            log.info("获取的openId未查询到对应用户，openId:" + openId);
        }
        return userId;

    }


    public String weiXinLoginGetOpenId(String code) {
        try {
            String url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=" + appId +
                    "&secret=" + secret + "&code=" + code + "&grant_type=authorization_code";
            RestTemplate restTemplate = new RestTemplate();
            String response = restTemplate.getForObject(url, String.class);
            log.info("获取openId微信返回" + response);

            com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(response);
//            String refresh_token = jsonObject.getString("refresh_token");
//            String accessTokenUrl = "https://api.weixin.qq.com/sns/oauth2/refresh_token?appid=" + appId+ "&grant_type=refresh_token&refresh_token=" + refresh_token;
//            log.info("refresh_token返回：-->"+accessTokenUrl);
////            String responseAccessTokenUrl = restTemplate.getForObject(accessTokenUrl, String.class);
            String openid = jsonObject.getString("openid");
//            log.info("refresh_token返回：-->"+jsonObject);
            return openid;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    public void getSendMessage(Map<String, WeChatTemplateMsg> sendMag, String accessToken, String openId, String infoUrl, String templateId) {
        // 模板参数
        // openId代表一个唯一微信用户，即微信消息的接收人
        // 公众号的模板id(也有相应的接口可以查询到)
        String url = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=" + accessToken;
        RestTemplate restTemplate = new RestTemplate();
        Map<String, Object> sendBody = new HashMap<>();
        sendBody.put("touser", openId);               // openId
        if (StringUtil.isNotEmpty(infoUrl)){
            sendBody.put("url", infoUrl);         // 点击模板信息跳转地址
        }
        sendBody.put("topcolor", "#FF0000");          // 顶色
        sendBody.put("data", sendMag);                   // 模板参数
        sendBody.put("template_id", templateId);      // 模板Id
        ResponseEntity<String> forEntity = restTemplate.postForEntity(url, sendBody, String.class);
        log.info("结果是: {}", forEntity.getBody());
        com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(forEntity.getBody());
        String messageCode = jsonObject.getString("errcode");
        String msgId = jsonObject.getString("msgid");
        System.out.println("messageCode : " + messageCode + ", msgId: " + msgId);
        log.info("进入发送实现方法，getSendMessage，返回数据：" + forEntity.getBody());
        log.info("result------------->" + forEntity.getBody());
    }

}
