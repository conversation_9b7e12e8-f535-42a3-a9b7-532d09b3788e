package cn.iocoder.zj.module.system.service.usercertificate;

import java.util.*;
import javax.validation.*;
import cn.iocoder.zj.module.system.controller.admin.usercertificate.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.usercertificate.UserCertificateDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 授权凭证 Service 接口
 *
 * <AUTHOR>
 */
public interface UserCertificateService {

    /**
     * 创建授权凭证
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createUserCertificate(@Valid UserCertificateCreateReqVO createReqVO);

    /**
     * 更新授权凭证
     *
     * @param updateReqVO 更新信息
     */
    void updateUserCertificate(@Valid UserCertificateUpdateReqVO updateReqVO);

    /**
     * 删除授权凭证
     *
     * @param id 编号
     */
    void deleteUserCertificate(Integer id);

    /**
     * 获得授权凭证
     *
     * @param id 编号
     * @return 授权凭证
     */
    UserCertificateDO getUserCertificate(String id);

    /**
     * 获得授权凭证列表
     *
     * @param ids 编号
     * @return 授权凭证列表
     */
    List<UserCertificateDO> getUserCertificateList(Long userId);

    /**
     * 获得授权凭证分页
     *
     * @param pageReqVO 分页查询
     * @return 授权凭证分页
     */
    PageResult<UserCertificateDO> getUserCertificatePage(UserCertificatePageReqVO pageReqVO);

    /**
     * 获得授权凭证列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 授权凭证列表
     */
    List<UserCertificateDO> getUserCertificateList(UserCertificateExportReqVO exportReqVO);

    UserCertificateInfoRespVO getUserCertificateInfo(String str);
}
