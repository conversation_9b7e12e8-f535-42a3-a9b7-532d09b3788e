package cn.iocoder.zj.module.system.controller.admin.wechatbinding.vo;

import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 微信公众号OpenId与租户绑定关系 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class WechatBindingExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("租户编号")
    private Long tenant;

    @ExcelProperty("微信公众号唯一标识")
    private String openId;

    @ExcelProperty("邮箱地址")
    private String email;

    @ExcelProperty("是否启用微信公众号")
    private Boolean isWechat;

    @ExcelProperty("是否启用微信邮箱")
    private Boolean isEmail;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private Date createTime;

}
