package cn.iocoder.zj.module.system.controller.admin.screen.vo;

import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 系统设置大屏 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ScreenExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "首页模块")
    private String module;

    @Schema(description = "监控条目")
    private String monitorEntry;

    @Schema(description = "监控条目名称")
    private String monitorEntryName;

    @ExcelProperty("资源类型")
    private String resourceType;

    @ExcelProperty("资源类型名称")
    private String resourceTypeName;

    @ExcelProperty("显示顺序")
    private Integer sort;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private Date createTime;

    @ExcelProperty("租户id")
    private Long tenantId;

    @ExcelProperty("地区id")
    private Long regionId;

    @ExcelProperty("租户名称")
    private String tenantName;

    @ExcelProperty("地区名称")
    private String regionName;

}
