package cn.iocoder.zj.module.system.dal.mysql.region;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.system.dal.dataobject.region.RegionDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.system.controller.admin.region.vo.*;

/**
 * 地区 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RegionMapper extends BaseMapperX<RegionDO> {

    List<RegionDO> getAllRegion();

    default PageResult<RegionDO> selectPage(RegionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<RegionDO>()
                .likeIfPresent(RegionDO::getRegionName, reqVO.getRegionName())
                .likeIfPresent(RegionDO::getRegionShortName, reqVO.getRegionShortName())
                .eqIfPresent(RegionDO::getRegionCode, reqVO.getRegionCode())
                .eqIfPresent(RegionDO::getRegionParentId, reqVO.getRegionParentId())
                .eqIfPresent(RegionDO::getRegionLevel, reqVO.getRegionLevel())
                .orderByDesc(RegionDO::getId));
    }

    default List<RegionDO> selectList(RegionExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<RegionDO>()
                .likeIfPresent(RegionDO::getRegionName, reqVO.getRegionName())
                .likeIfPresent(RegionDO::getRegionShortName, reqVO.getRegionShortName())
                .eqIfPresent(RegionDO::getRegionCode, reqVO.getRegionCode())
                .eqIfPresent(RegionDO::getRegionParentId, reqVO.getRegionParentId())
                .eqIfPresent(RegionDO::getRegionLevel, reqVO.getRegionLevel())
                .orderByDesc(RegionDO::getId));
    }

}
