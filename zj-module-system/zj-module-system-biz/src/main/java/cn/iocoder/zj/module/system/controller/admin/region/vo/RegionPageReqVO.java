package cn.iocoder.zj.module.system.controller.admin.region.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 地区分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RegionPageReqVO extends PageParam {

    @Schema(description = "地区名称")
    private String regionName;

    @Schema(description = "地区缩写")
    private String regionShortName;

    @Schema(description = "行政地区编号")
    private String regionCode;

    @Schema(description = "地区父id")
    private String regionParentId;

    @Schema(description = "地区级别 1-省、自治区、直辖市 2-地级市、地区、自治州、盟 3-市辖区、县级市、县")
    private Integer regionLevel;


}
