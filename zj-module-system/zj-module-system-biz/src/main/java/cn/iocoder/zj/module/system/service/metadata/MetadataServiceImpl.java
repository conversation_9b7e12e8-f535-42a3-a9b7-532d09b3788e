package cn.iocoder.zj.module.system.service.metadata;

import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.zj.module.system.controller.admin.metadata.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.metadata.MetadataDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.system.convert.metadata.MetadataConvert;
import cn.iocoder.zj.module.system.dal.mysql.metadata.MetadataMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.system.enums.ErrorCodeConstants.*;

/**
 * 元数据 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MetadataServiceImpl implements MetadataService {

    @Resource
    private MetadataMapper metadataMapper;

    @Override
    public Long createMetadata(MetadataCreateReqVO createReqVO) {
        // 插入
        MetadataDO metadata = MetadataConvert.INSTANCE.convert(createReqVO);
        metadataMapper.insert(metadata);
        // 返回
        return metadata.getId();
    }

    @Override
    public void updateMetadata(MetadataUpdateReqVO updateReqVO) {
        // 校验存在
//        validateMetadataExists(updateReqVO.getId());
        // 更新
        MetadataDO updateObj = MetadataConvert.INSTANCE.convert(updateReqVO);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Long tenantId = loginUser.getTenantId();
        updateObj.setTenantId(tenantId);
        metadataMapper.updateById(updateObj);
    }

    @Override
    public void deleteMetadata(Long id) {
        // 校验存在
//        validateMetadataExists(id);
        // 删除
        metadataMapper.deleteById(id);
    }

//    private void validateMetadataExists(Long id) {
//        if (metadataMapper.selectById(id) == null) {
//            throw exception(METADATA_NOT_EXISTS);
//        }
//    }

    @Override
    public MetadataDO getMetadata(Long id) {
        return metadataMapper.selectById(id);
    }

    @Override
    public List<MetadataDO> getMetadataList(Collection<Long> ids) {
        return metadataMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<MetadataDO> getMetadataPage(MetadataPageReqVO pageReqVO) {
        return metadataMapper.selectPage(pageReqVO);
    }

    @Override
    public List<MetadataDO> getMetadataList(MetadataExportReqVO exportReqVO) {
        return metadataMapper.selectList(exportReqVO);
    }

    @Override
    @TenantIgnore
    public MetadataDO getMetadataByType(String type) {
        //判断数据是否存在
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Long tenantId = loginUser.getTenantId();
        MetadataDO metadataDO=metadataMapper.selectByType(type,tenantId);
        if (metadataDO==null){
            //该租户没有数据，添加一条数据返回
            metadataDO=new MetadataDO();
            metadataDO.setType(type);
            metadataDO.setTenantId(tenantId);
            metadataMapper.insert(metadataDO);
        }
        return metadataDO;
    }

}
