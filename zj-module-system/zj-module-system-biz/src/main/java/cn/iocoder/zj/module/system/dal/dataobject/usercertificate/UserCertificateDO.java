package cn.iocoder.zj.module.system.dal.dataobject.usercertificate;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 授权凭证 DO
 *
 * <AUTHOR>
 */
@TableName("system_user_certificate")
@KeySequence("system_user_certificate_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserCertificateDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Integer id;
    /**
     * 所有者id
     */
    private Long userId;
    /**
     * 名称
     */
    private String name;
    /**
     * 所有者名称
     */
    private String ownerName;
    /**
     * 授权账户
     */
    private String username;
    /**
     * 密码
     */
    private String password;
    /**
     * 私钥密码
     */
    private String passphrase;
    /**
     * 私钥
     */
    private String privateKey;
    /**
     * 账户类型
     */
    private String type;

}
