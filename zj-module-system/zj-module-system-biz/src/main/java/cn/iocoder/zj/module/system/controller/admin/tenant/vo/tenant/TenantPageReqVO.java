package cn.iocoder.zj.module.system.controller.admin.tenant.vo.tenant;

import cn.iocoder.zj.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 租户分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TenantPageReqVO extends PageParam {

    @Schema(description = "租户名", example = "芋道")
    private String name;

    @Schema(description = "联系人", example = "芋艿")
    private String contactName;

    @Schema(description = "联系手机", example = "15601691300")
    private String contactMobile;

    @Schema(description = "联系邮箱")
    private String contactEmail;

    @Schema(description = "租户状态（0正常 1停用）", example = "1")
    private Integer status;

    @Schema(description = "项目套餐", example = "1")
    private Long packageId;

    @Schema(description = "地区ID", example = "1003210")
    private Integer regionId;

    @Schema(description = "地区名称", example = "乌鲁木齐")
    private String regionName;

    @Schema(description = "是否测试账号", example = "1024")
    private Integer isTest;

    @Schema(description = "配置平台json")
    private String platformJson;

    @Schema(description = "配置平台名称")
    private String platformNames;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @Schema(description = "创建时间")
    private LocalDateTime[] createTime;

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String startTime;

    @Schema(description = "结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String endTime;

    private String maintainerIds;

    @Schema(description = "运维人员id集合")
    private List maintainerIdList;

    @Schema(description = "公司名称")
    private String organization;
}