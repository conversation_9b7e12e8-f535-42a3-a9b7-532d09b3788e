package cn.iocoder.zj.module.system.service.platformtenant;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.zj.module.system.controller.admin.platformtenant.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.platformtenant.PlatformTenantDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.system.convert.platformtenant.PlatformTenantConvert;
import cn.iocoder.zj.module.system.dal.mysql.platformtenant.PlatformTenantMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.system.enums.ErrorCodeConstants.*;

/**
 * 平台租户关系 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PlatformTenantServiceImpl implements PlatformTenantService {

    @Resource
    private PlatformTenantMapper platformTenantMapper;

    @Override
    public Long createPlatformTenant(PlatformTenantCreateReqVO createReqVO) {
        // 插入
        PlatformTenantDO platformTenant = PlatformTenantConvert.INSTANCE.convert(createReqVO);
        platformTenantMapper.insert(platformTenant);
        // 返回
        return platformTenant.getId();
    }

    @Override
    public void updatePlatformTenant(PlatformTenantUpdateReqVO updateReqVO) {
        // 校验存在
        validatePlatformTenantExists(updateReqVO.getId());
        // 更新
        PlatformTenantDO updateObj = PlatformTenantConvert.INSTANCE.convert(updateReqVO);
        platformTenantMapper.updateById(updateObj);
    }

    @Override
    public void deletePlatformTenant(Long id) {
        // 校验存在
        validatePlatformTenantExists(id);
        // 删除
        platformTenantMapper.deleteById(id);
    }

    private void validatePlatformTenantExists(Long id) {
        if (platformTenantMapper.selectById(id) == null) {
            throw exception(PLATFORM_TENANT_NOT_EXISTS);
        }
    }

    @Override
    public PlatformTenantDO getPlatformTenant(Long id) {
        return platformTenantMapper.selectById(id);
    }

    @Override
    public List<PlatformTenantDO> getPlatformTenantList(Collection<Long> ids) {
        return platformTenantMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<PlatformTenantDO> getPlatformTenantPage(PlatformTenantPageReqVO pageReqVO) {
        return platformTenantMapper.selectPage(pageReqVO);
    }

    @Override
    public List<PlatformTenantDO> getPlatformTenantList(PlatformTenantExportReqVO exportReqVO) {
        return platformTenantMapper.selectList(exportReqVO);
    }

}
