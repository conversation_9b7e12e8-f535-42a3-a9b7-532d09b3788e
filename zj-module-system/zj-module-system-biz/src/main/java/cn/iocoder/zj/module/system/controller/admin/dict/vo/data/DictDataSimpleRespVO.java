package cn.iocoder.zj.module.system.controller.admin.dict.vo.data;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 数据字典精简 Response VO")
@Data
public class DictDataSimpleRespVO {

    @Schema(description = "字典类型", required = true, example = "gender")
    private String dictType;

    @Schema(description = "字典键值", required = true, example = "1")
    private String value;

    @Schema(description = "字典标签", required = true, example = "男")
    private String label;

    @Schema(description = "颜色类型，default、primary、success、info、warning、danger", example = "default" )
    private String colorType;

    @Schema(description = "css 样式", example = "btn-visible")
    private String cssClass;

    @Schema(description = "单位类型，数据传输速率：unit-bytesSpeed，数据量：unit-bytes，包数量：unit-number，百分比：unit-percent", example = "unit-bytesSpeed")
    private String unitType;

    @Schema(description = "图标")
    private String icon;

}