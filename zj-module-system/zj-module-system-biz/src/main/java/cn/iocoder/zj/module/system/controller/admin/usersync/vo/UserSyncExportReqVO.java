package cn.iocoder.zj.module.system.controller.admin.usersync.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 企业微信userid Excel 导出 Request VO，参数和 UserSyncPageReqVO 是一致的")
@Data
public class UserSyncExportReqVO {

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "用户手机")
    private String phone;

    @Schema(description = "微信userId")
    private String wxUserId;

    @Schema(description = "用户名称")
    private String nickname;

    @Schema(description = "备注")
    private String remark;

}
