package cn.iocoder.zj.module.system.convert.categoryimgconfig;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.system.controller.admin.categoryimgconfig.vo.CategoryImgConfigCreateReqVO;
import cn.iocoder.zj.module.system.controller.admin.categoryimgconfig.vo.CategoryImgConfigExcelVO;
import cn.iocoder.zj.module.system.controller.admin.categoryimgconfig.vo.CategoryImgConfigRespVO;
import cn.iocoder.zj.module.system.controller.admin.categoryimgconfig.vo.CategoryImgConfigUpdateReqVO;
import cn.iocoder.zj.module.system.dal.dataobject.categoryimgconfig.CategoryImgConfigDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 类别图片管理 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface CategoryImgConfigConvert {

    CategoryImgConfigConvert INSTANCE = Mappers.getMapper(CategoryImgConfigConvert.class);

    CategoryImgConfigDO convert(CategoryImgConfigCreateReqVO bean);

    CategoryImgConfigDO convert(CategoryImgConfigUpdateReqVO bean);

    CategoryImgConfigRespVO convert(CategoryImgConfigDO bean);

    List<CategoryImgConfigRespVO> convertList(List<CategoryImgConfigDO> list);

    PageResult<CategoryImgConfigRespVO> convertPage(PageResult<CategoryImgConfigDO> page);

    List<CategoryImgConfigExcelVO> convertList02(List<CategoryImgConfigDO> list);

}
