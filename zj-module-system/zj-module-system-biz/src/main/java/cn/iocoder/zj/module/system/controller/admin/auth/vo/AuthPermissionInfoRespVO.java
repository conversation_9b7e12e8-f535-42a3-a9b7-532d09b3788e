package cn.iocoder.zj.module.system.controller.admin.auth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

@Schema(description = "管理后台 - 登录用户的权限信息 Response VO，额外包括用户信息和角色列表")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AuthPermissionInfoRespVO {

    @Schema(description = "用户信息", required = true)
    private UserVO user;

    @Schema(description = "角色标识数组", required = true)
    private Set<String> roles;

    @Schema(description = "操作权限数组", required = true)
    private Set<String> permissions;

    @Schema(description = "菜单树", required = true)
    private List<MenuVO> menus;


    @Schema(description = "用户信息 VO")
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class UserVO {

        @Schema(description = "用户编号", required = true, example = "1024")
        private Long id;

        @Schema(description = "用户昵称", required = true, example = "芋道源码")
        private String nickname;

        @Schema(description = "用户头像", required = true, example = "http://www.iocoder.cn/xx.jpg")
        private String avatar;

        @Schema(description = "公司")
        private String organization;

        @Schema(description = "公司")
        private String deptStr;

        @Schema(description = "是否为管理员运维")
        private Boolean isRootOperation ;

        @Schema(description = "用户定制大屏版本状态，0：官方版本，1：公有定制版本")
        private Integer state;
    }


    @Schema(description = "管理后台 - 登录用户的菜单信息 Response VO")
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class MenuVO {

        @Schema(description = "菜单名称", required = true, example = "芋道")
        private Long id;

        @Schema(description = "父菜单 ID", required = true, example = "1024")
        private Long parentId;

        @Schema(description = "菜单名称", required = true, example = "芋道")
        private String name;

        @Schema(description = "路由地址,仅菜单类型为菜单或者目录时，才需要传", example = "post")
        private String path;

        @Schema(description = "组件路径,仅菜单类型为菜单时，才需要传", example = "system/post/index")
        private String component;

        @Schema(description = "组件名", example = "SystemUser")
        private String componentName;

        @Schema(description = "菜单图标,仅菜单类型为菜单或者目录时，才需要传", example = "/menu/list")
        private String icon;

        @Schema(description = "是否可见", required = true, example = "false")
        private Boolean visible;

        @Schema(description = "是否缓存", required = true, example = "false")
        private Boolean keepAlive;

        @Schema(description = "是否总是显示", example = "false")
        private Boolean alwaysShow;

        @Schema(description = "标签名称", example = "系统设置")
        private String labelName;

        /**
         * 子路由
         */
        private List<MenuVO> children;

    }

}