### 请求 /login 接口 => 成功
POST {{systemBaseUrl}}/system/auth/login
Content-Type: application/json
tenant-id: {{adminTenentId}}
tag: {{tag}}

{
  "username": "admin",
  "password": "admin123",
  "uuid": "3acd87a09a4f48fb9118333780e94883",
  "code": "1024"
}

### 请求 /login 接口 => 成功（无验证码)
POST {{systemBaseUrl}}/system/auth/login
Content-Type: application/json
tenant-id: {{adminTenentId}}

{
  "username": "admin",
  "password": "admin123"
}

### 请求 /get-permission-info 接口 => 成功
GET {{systemBaseUrl}}/system/auth/get-permission-info
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}

### 请求 /list-menus 接口 => 成功
GET {{systemBaseUrl}}/system/auth/list-menus
#Authorization: Bearer {{token}}
Authorization: Bearer c347026e805e4d99b0d116eae66eda8c
tenant-id: {{adminTenentId}}
