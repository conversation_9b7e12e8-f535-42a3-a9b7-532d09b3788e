package cn.iocoder.zj.module.system.service.screen;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.module.infra.api.file.FileApi;
import cn.iocoder.zj.module.system.dal.dataobject.screen.ScreenConfigDO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.io.IOException;
import java.util.*;
import cn.iocoder.zj.module.system.controller.admin.screen.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.screen.ScreenDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.system.convert.screen.ScreenConvert;
import cn.iocoder.zj.module.system.dal.mysql.screen.ScreenMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.iocoder.zj.module.system.enums.ErrorCodeConstants.*;

/**
 * 系统设置大屏 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ScreenServiceImpl implements ScreenService {

    @Resource
    private ScreenMapper screenMapper;

    @Resource
    private FileApi fileApi;


    @Override
    public Long createScreen(ScreenCreateReqVO createReqVO) {
        // 插入
        ScreenDO screen = ScreenConvert.INSTANCE.convert(createReqVO);
        screenMapper.insert(screen);
        // 返回
        return screen.getId();
    }

    @Override
    public void updateScreen(ScreenUpdateReqVO updateReqVO) {
        // 校验存在
        validateScreenExists(updateReqVO.getId());
        // 更新
        ScreenDO updateObj = ScreenConvert.INSTANCE.convert(updateReqVO);
        screenMapper.updateById(updateObj);
    }

    @Override
    public void deleteScreen(Long id) {
        // 校验存在
        validateScreenExists(id);
        // 删除
        screenMapper.deleteById(id);
    }

    private void validateScreenExists(Long id) {
        if (screenMapper.selectById(id) == null) {
            throw exception(SCREEN_NOT_EXISTS);
        }
    }

    @Override
    public ScreenDO getScreen(Long id) {
        return screenMapper.selectById(id);
    }

    @Override
    public List<ScreenDO> getScreenList(Long tenantId) {
        return screenMapper.selectByTenantId(tenantId);
    }

    @Override
    public List<ScreenDO> getTenantScreenList(Long tenantId) {
        return screenMapper.selectScreenListByTenantId(tenantId);
    }

    @Override
    public PageResult<ScreenDO> getScreenPage(ScreenPageReqVO pageReqVO) {
        return screenMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ScreenDO> getScreenList(ScreenExportReqVO exportReqVO) {
        return screenMapper.selectList(exportReqVO);
    }

    @Override
    public List<ScreenConfigDO> getScreenConfigByTenantId(Long tenantId) {
        return screenMapper.getScreenConfigByTenantId(tenantId);
    }

    @Override
    public Boolean updateScreenConfig(ScreenConfigReqVO createReqVO) {
        ScreenConfigDO screenConfig = null;
        if(ObjectUtil.isNotEmpty(createReqVO.getId())) {
            screenConfig = screenMapper.getScreenConfigById(createReqVO.getId());
        }
        if(ObjectUtil.isEmpty(screenConfig)){
            screenConfig = new ScreenConfigDO();
            screenConfig.setUpdater(getLoginUserId());
            screenConfig.setCreator(getLoginUserId());
            screenConfig.setSystemName(createReqVO.getSystemName());
            screenConfig.setFavicon(createReqVO.getFavicon());
            screenConfig.setLogo(createReqVO.getLogo());
            screenConfig.setTitle(createReqVO.getTitle());
            screenConfig.setTenantId(createReqVO.getTenantId());
            screenMapper.createScreenConfig(screenConfig);
        }else {
            screenConfig.setUpdater(getLoginUserId());
            screenConfig.setFavicon(createReqVO.getFavicon());
            screenConfig.setLogo(createReqVO.getLogo());
            screenConfig.setSystemName(createReqVO.getSystemName());
            screenConfig.setTitle(createReqVO.getTitle());
            screenMapper.updateScreenConfig(screenConfig);
        }
        return true;
    }

}
