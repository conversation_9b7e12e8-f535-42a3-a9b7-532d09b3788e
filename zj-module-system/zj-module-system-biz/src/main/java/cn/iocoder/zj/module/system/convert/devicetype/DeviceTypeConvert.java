package cn.iocoder.zj.module.system.convert.devicetype;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.system.controller.admin.devicetype.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.devicetype.DeviceTypeDO;

/**
 * 资产类型 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DeviceTypeConvert {

    DeviceTypeConvert INSTANCE = Mappers.getMapper(DeviceTypeConvert.class);

    DeviceTypeDO convert(DeviceTypeCreateReqVO bean);

    DeviceTypeDO convert(DeviceTypeUpdateReqVO bean);

    DeviceTypeRespVO convert(DeviceTypeDO bean);

    List<DeviceTypeRespVO> convertList(List<DeviceTypeDO> list);

    PageResult<DeviceTypeRespVO> convertPage(PageResult<DeviceTypeDO> page);

    List<DeviceTypeExcelVO> convertList02(List<DeviceTypeDO> list);

}
