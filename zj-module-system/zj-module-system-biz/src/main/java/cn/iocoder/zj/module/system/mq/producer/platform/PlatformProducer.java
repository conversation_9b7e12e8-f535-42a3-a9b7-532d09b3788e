package cn.iocoder.zj.module.system.mq.producer.platform;


import cn.iocoder.zj.module.system.mq.message.mail.MailSendMessage;
import cn.iocoder.zj.module.system.mq.message.platform.HtmlEmailMessage;
import cn.iocoder.zj.module.system.mq.message.platform.PlatformSendMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName : PlatformProducer  //类名
 * @Description : 平台发送消息  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/5/10  14:40
 */
@Slf4j
@Component
public class PlatformProducer{
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    RabbitTemplate rabbitTemplate;

    public void sendPlatformSendMessage(Long platformId) {
        PlatformSendMessage message = new PlatformSendMessage()
                .setPlatformId(platformId);
        rabbitTemplate.convertAndSend("platform_id_message",message);
    }

    public void sendPlatformSendMessageCollection(Long platformId) {
        PlatformSendMessage message = new PlatformSendMessage()
                .setPlatformId(platformId);
        rabbitTemplate.convertAndSend("platform_collection",message);
    }

    public void sendPlatformSendMailCollection(String userName,String passWord,String mail) {
        HtmlEmailMessage message = new HtmlEmailMessage();
        message.setUserName(userName);
        message.setPassWord(passWord);
        message.setMail(mail);
        rabbitTemplate.convertAndSend("platform_html_mail_message",message);
    }

    public void sendDelayedPlatformSendMessage(Long platformId,long delay, TimeUnit timeUnit) {
        scheduler.schedule(() -> sendPlatformSendMessageCollection(platformId), delay, timeUnit);
    }
}
