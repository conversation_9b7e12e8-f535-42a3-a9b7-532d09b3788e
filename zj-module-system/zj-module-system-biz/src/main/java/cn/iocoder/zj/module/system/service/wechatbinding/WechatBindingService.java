package cn.iocoder.zj.module.system.service.wechatbinding;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.system.controller.admin.wechatbinding.vo.WechatBindingCreateReqVO;
import cn.iocoder.zj.module.system.controller.admin.wechatbinding.vo.WechatBindingExportReqVO;
import cn.iocoder.zj.module.system.controller.admin.wechatbinding.vo.WechatBindingPageReqVO;
import cn.iocoder.zj.module.system.controller.admin.wechatbinding.vo.WechatBindingUpdateReqVO;
import cn.iocoder.zj.module.system.dal.dataobject.wechatbinding.WechatBindingDO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 微信公众号OpenId与租户绑定关系 Service 接口
 *
 * <AUTHOR>
 */
public interface WechatBindingService {

    String getAccessToken();

    String getTemporaryQR(String accessToken, String account);

    void callback(HttpServletRequest request, HttpServletResponse response) throws Exception;

    void sendMessage(String map);

    String sendMessage1();

    void sendSingleWeChatToMember(Map<String,Object> templateParams) throws Exception;

    /**
     * 创建微信公众号OpenId与租户绑定关系
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createWechatBinding(@Valid WechatBindingCreateReqVO createReqVO);

    /**
     * 更新微信公众号OpenId与租户绑定关系
     *
     * @param updateReqVO 更新信息
     */
    void updateWechatBinding(@Valid WechatBindingUpdateReqVO updateReqVO);

    /**
     * 删除微信公众号OpenId与租户绑定关系
     *
     * @param id 编号
     */
    void deleteWechatBinding(Long id);

    /**
     * 获得微信公众号OpenId与租户绑定关系
     *
     * @param id 编号
     * @return 微信公众号OpenId与租户绑定关系
     */
    WechatBindingDO getWechatBinding(Long id);

    /**
     * 获得微信公众号OpenId与租户绑定关系列表
     *
     * @param ids 编号
     * @return 微信公众号OpenId与租户绑定关系列表
     */
    List<WechatBindingDO> getWechatBindingList(Collection<Long> ids);

    /**
     * 获得微信公众号OpenId与租户绑定关系分页
     *
     * @param pageReqVO 分页查询
     * @return 微信公众号OpenId与租户绑定关系分页
     */
    PageResult<WechatBindingDO> getWechatBindingPage(WechatBindingPageReqVO pageReqVO);

    /**
     * 获得微信公众号OpenId与租户绑定关系列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 微信公众号OpenId与租户绑定关系列表
     */
    List<WechatBindingDO> getWechatBindingList(WechatBindingExportReqVO exportReqVO);


    WechatBindingDO getWeChatBinding(Long tenantId);

    Boolean getIsOpenId(String tenantId);

    Boolean sendSingleWeChatToAuthorization(Map<String, Object> templateParams);

    Long getUserIdByCode(String code);

    void sendMessage(String accessToken, String openId, String message);

    Boolean isChange(Long id);

    Boolean change(Long id);

    void cancelChange(Long id);

    Boolean sendSingleWeChatToAuthorizationType(Map<String, Object> templateParams);

    void sendBpmMessage(Map<String, Object> templateParams);
}
