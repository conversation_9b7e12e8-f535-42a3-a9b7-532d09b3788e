package cn.iocoder.zj.module.system.controller.admin.screen.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;

@Data
public class ScreenConfigReqVO {
    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "大屏标题")
    private String title;

    @Schema(description = "云星辰系统找用户自定义名称")
    private String systemName;

    @Schema(description = "logo")
    private String logo;

    @Schema(description = "浏览器图标")
    private String favicon;

    @Schema(description = "租户ID")
    private Long tenantId;

}
