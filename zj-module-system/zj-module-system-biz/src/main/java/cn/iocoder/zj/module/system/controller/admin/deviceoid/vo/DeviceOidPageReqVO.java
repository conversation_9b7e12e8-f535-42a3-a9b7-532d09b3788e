package cn.iocoder.zj.module.system.controller.admin.deviceoid.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - OID管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DeviceOidPageReqVO extends PageParam {

    @Schema(description = "设备类型key")
    private String deviceType;

    @Schema(description = "设备类型名称")
    private String deviceName;

    @Schema(description = "系统类型名称")
    private String sysName;

    @Schema(description = "系统类型key")
    private String sysType;

    /**
     * cpu使用率oid
     */
    @Schema(description = "cpu使用率oid")
    private String cpuUse;

    /**
     * 内存总容量oid
     */
    @Schema(description = "内存总容量oid")
    private String memoryAll;
    /**
     * 内存使用量oid
     */
    @Schema(description = "内存使用量oid")
    private String memoryUse;
    /**
     * 磁盘总容量oid
     */
    @Schema(description = "磁盘总容量oid")
    private String diskAll;
    /**
     * 磁盘使用量oid
     */
    @Schema(description = "磁盘使用量oid")
    private String diskUse;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] createTime;

}
