package cn.iocoder.zj.module.system.controller.admin.usersync.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 企业微信userid Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserSyncRespVO extends UserSyncBaseVO {

    @Schema(description = "主键", required = true)
    private Long wxcomId;

}
