package cn.iocoder.zj.module.system.util;


import cn.hutool.core.date.DateUtil;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringUtil {

    public StringUtil() {
    }

    public static <T> String joinlistWithSeparator(List<T> objects, String separator) {
        String str = "";

        for (int i = 0; i < objects.size(); ++i) {
            str = str + objects.get(i).toString();
            str = str + separator;
        }

        if (str.length() > 0) {
            str = str.substring(0, str.length() - separator.length());
        }

        return str;
    }

    public static String ArrayToDelimitedString(String[] source, String delimiter, String surround) {
        String str = "";

        for (int i = 0; i < source.length; ++i) {
            str = str + surround + source[i] + surround + delimiter;
        }

        if (str.length() > 0) {
            str = str.substring(0, str.length() - delimiter.length());
        }

        return str;
    }

    public static String trim(String str) {
        return trimEnd(trimStart(str));
    }

    public static String trimStart(String str) {
        return trimStart(str, "\\s");
    }

    public static String trimStart(String str, String prefix) {
        return str.replaceFirst("^" + prefix + "+", "");
    }

    public static String trimEnd(String str) {
        return str.replaceFirst("\\s+$", "");
    }

    public static String subEnd(String str) {
        return subEnd(str, ".");
    }

    public static String subEnd(String str, String prefix) {
        return str.substring(str.lastIndexOf(prefix) + 1).toLowerCase();
    }

    public static boolean isNullOrEmpty(String s) {
        return s == null || s.length() == 0;
    }

    public static boolean isNotEmpty(String s) {
        return !StringUtil.isNullOrEmpty(s);
    }


    public static int toInt(Object obj) {
        return toInt(obj, 0);
    }

    public static int toInt(Object obj, int value) {
        try {
            return Integer.parseInt(obj.toString());
        } catch (Exception var3) {
            return value;
        }
    }

    public static boolean toBoolean(Object obj) {
        return toBoolean(obj, false);
    }

    public static boolean toBoolean(Object obj, boolean value) {
        if (obj == null) {
            return value;
        } else if (Pattern.compile("y|yes|true|1|是|对").matcher(obj.toString().toLowerCase()).matches()) {
            return true;
        } else {
            return Pattern.compile("n|no|false|0|否|错").matcher(obj.toString().toLowerCase()).matches() ? false : value;
        }
    }

    public static double toDouble(Object obj) {
        double a = 0.0D;
        if (obj != null) {
            try {
                a = Double.parseDouble(obj.toString());
            } catch (Exception var4) {
                ;
            }
        }

        return a;
    }

    public static Long toLong(Object obj) {
        Long a = 0L;
        if (obj != null) {
            try {
                a = Long.parseLong(obj.toString());
            } catch (Exception var3) {
                ;
            }
        }

        return a;
    }

    public static String UniqueId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    public static String toString(Object obj) {
        String str = "";

        try {
            if (obj == null) {
                str = "";
            }

            str = obj.toString();
        } catch (Exception var3) {
            ;
        }

        return str;
    }

    public static String decode(String str) {
        String result = "";

        try {
            result = URLDecoder.decode(str, "UTF-8");
        } catch (Exception var3) {
            result = str;
        }

        return result;
    }

    public static String encode(String str) {
        String result = "";

        try {
            result = URLEncoder.encode(str, "UTF-8");
        } catch (Exception var3) {
            result = str;
        }

        return result;
    }

    public static String getCnASCII(String cnStr) {
        StringBuffer strBuf = new StringBuffer();
        byte[] bGBK = cnStr.getBytes();

        for (int i = 0; i < bGBK.length; ++i) {
            strBuf.append(Integer.toHexString(bGBK[i] & 255));
        }

        return strBuf.toString();
    }

    public static String[] union(String[] arr1, String[] arr2) {
        Set<String> set = new HashSet();
        String[] result = arr1;
        int len$ = arr1.length;

        int i$;
        String str;
        for (i$ = 0; i$ < len$; ++i$) {
            str = result[i$];
            set.add(str);
        }

        result = arr2;
        len$ = arr2.length;

        for (i$ = 0; i$ < len$; ++i$) {
            str = result[i$];
            set.add(str);
        }

        result = new String[0];
        return (String[]) set.toArray(result);
    }

    public static String[] minus(String[] arr1, String[] arr2) {
        LinkedList<String> list = new LinkedList();
        LinkedList<String> history = new LinkedList();
        String[] longerArr = arr1;
        String[] shorterArr = arr2;
        if (arr1.length > arr2.length) {
            longerArr = arr2;
            shorterArr = arr1;
        }

        String[] result = longerArr;
        int len$ = longerArr.length;

        int i$;
        String str;
        for (i$ = 0; i$ < len$; ++i$) {
            str = result[i$];
            if (!list.contains(str)) {
                list.add(str);
            }
        }

        result = shorterArr;
        len$ = shorterArr.length;

        for (i$ = 0; i$ < len$; ++i$) {
            str = result[i$];
            if (list.contains(str)) {
                history.add(str);
                list.remove(str);
            } else if (!history.contains(str)) {
                list.add(str);
            }
        }

        result = new String[0];
        return (String[]) list.toArray(result);
    }

    public static String toUpperCaseFirstOne(String str) {
        if (Character.isUpperCase(str.charAt(0)))
            return str;
        else
            return (new StringBuilder()).append(Character.toUpperCase(str.charAt(0))).append(str.substring(1)).toString();
    }

    public static String[] split(String str, String regex) {
        if (isNullOrEmpty(str)) return new String[0];
        return str.split(regex);
    }


    /**
     * 功能：判断一个字符串是否包含特殊字符
     *
     * @param string 要判断的字符串
     * @return false 提供的参数string包含特殊字符
     */
    public static boolean isConSpeCharacters(String string) {
        if (string.replaceAll("[\u4e00-\u9fa5]*[a-z]*[A-Z]*\\d*-*_*\\s*", "").length() == 0) {
            //如果不包含特殊字符
            return true;
        }
        return false;
    }

    /**
     * 数字转中文
     *
     * @param num 数字
     * @return
     */
    public static String toChinese(int num) {
        String[] numeric = new String[]{"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
        StringBuilder builder = new StringBuilder();
        builder.append(numeric[num / 1000] + "千").append(numeric[num / 100 % 10] + "百").append(numeric[num / 10 % 10] + "十").append(numeric[num % 10]);

        //去掉了零千
        int index = -1;
        while ((index = builder.indexOf(numeric[0], index + 1)) != -1) {
            if (index < builder.length() - 1) {
                builder.deleteCharAt(index + 1);
            }
        }

        //去掉双零
        index = 0;
        while ((index = builder.indexOf("零零", index)) != -1) {
            builder.deleteCharAt(index);
        }

        if (builder.length() > 1) {
            //去掉开头的零
            if (builder.indexOf(numeric[0]) == 0) {
                builder.deleteCharAt(0);
            }
            //去掉末尾的零
            if (builder.indexOf(numeric[0]) == builder.length() - 1) {
                builder.deleteCharAt(builder.length() - 1);
            }

        }

        //把开头一十换成十
        if (builder.indexOf("一十") == 0) {
            builder.deleteCharAt(0);
        }
        return builder.toString();
    }

    /***
     * @param htmlStr
     * @return
     */
    public static String delHTMLTag(String htmlStr) {
        String regEx_script = "<script[^>]*?>[\\s\\S]*?<\\/script>"; //定义script的正则表达式
        String regEx_style = "<style[^>]*?>[\\s\\S]*?<\\/style>"; //定义style的正则表达式
        String regEx_html = "<[^>]+>"; //定义HTML标签的正则表达式

        Pattern p_script = Pattern.compile(regEx_script, Pattern.CASE_INSENSITIVE);
        Matcher m_script = p_script.matcher(htmlStr);
        htmlStr = m_script.replaceAll(""); //过滤script标签

        Pattern p_style = Pattern.compile(regEx_style, Pattern.CASE_INSENSITIVE);
        Matcher m_style = p_style.matcher(htmlStr);
        htmlStr = m_style.replaceAll(""); //过滤style标签

        Pattern p_html = Pattern.compile(regEx_html, Pattern.CASE_INSENSITIVE);
        Matcher m_html = p_html.matcher(htmlStr);
        htmlStr = m_html.replaceAll(""); //过滤html标签

        return htmlStr.trim(); //返回文本字符串
    }

    public static String[] chars = new String[]{"a", "b", "c", "d", "e", "f",
            "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s",
            "t", "u", "v", "w", "x", "y", "z", "0", "1", "2", "3", "4", "5",
            "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "G", "H", "I",
            "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V",
            "W", "X", "Y", "Z"};


    /**
     * @return
     * @name 返回八位随机唯一短码
     */
    public static String generateShortUuid() {
        StringBuffer shortBuffer = new StringBuffer();
        String uuid = UUID.randomUUID().toString().replace("-", "");
        for (int i = 0; i < 8; i++) {
            String str = uuid.substring(i * 4, i * 4 + 4);
            int x = Integer.parseInt(str, 16);
            shortBuffer.append(chars[x % 0x3E]);
        }
//        shortBuffer.insert(3,"-");
//        shortBuffer.insert(7,"-");
        return shortBuffer.toString();

    }


    public static List<String> str2List(String str, String ch) {
        List<String> list = new ArrayList<String>();
        String[] s = str.split(ch);
        for (int i = 0; i < s.length; i++) {
            list.add(s[i]);
        }
        return list;
    }


    /**
     * 将一个list均分成n个list,主要通过偏移量来实现的
     *
     * @param list
     * @return
     */
    public static <T> List<List<T>> averageAssign(List<T> list, int n) {
        List<List<T>> result = new ArrayList<>();
        int remaider = list.size() % n;  //(先计算出余数)
        int number = list.size() / n;  //然后是商
        int offset = 0;//偏移量
        for (int i = 0; i < n; i++) {
            List<T> value;
            if (remaider > 0) {
                value = list.subList(i * number + offset, (i + 1) * number + offset + 1);
                remaider--;
                offset++;
            } else {
                value = list.subList(i * number + offset, (i + 1) * number + offset);
            }
            result.add(value);
        }
        return result;
    }

    public static String randomNumber(int length) {
        String sources = "0123456789";
        Random rand = new Random();
        StringBuilder sb = new StringBuilder();
        for (int j = 0; j < length; j++) {
            sb.append(sources.charAt(rand.nextInt(9)));
        }
        return sb.toString();
    }

    public static String getSavePath(String fileName, String fileType) {

        String dayStr = DateUtil.now();
        String days = dayStr.substring(0, dayStr.lastIndexOf(" "));
        String[] dayArr = days.split("-");

        String path = dayArr[0] + "/" + dayArr[1] + "/" + dayArr[2] + "/" + fileType + "/" + fileName;

        return path;
    }

    public static String extractPort(String url) {
        // 定义正则表达式模式来匹配端口号
        Pattern pattern = Pattern.compile(":\\d+$");
        Matcher matcher = pattern.matcher(url);

        // 查找匹配的端口号
        if (matcher.find()) {
            // 去掉冒号并返回端口号
            return matcher.group().substring(1);
        } else {
            // 如果没有找到端口号，根据协议返回默认端口号
            if (url.startsWith("https://")) {
                return "443";
            } else if (url.startsWith("http://")) {
                return "80";
            }
        }

        // 如果没有找到协议，返回空字符串
        return "";
    }
}
