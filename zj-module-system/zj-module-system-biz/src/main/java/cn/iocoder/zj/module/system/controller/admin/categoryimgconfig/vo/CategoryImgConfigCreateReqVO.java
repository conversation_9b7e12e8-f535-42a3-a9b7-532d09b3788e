package cn.iocoder.zj.module.system.controller.admin.categoryimgconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 类别图片管理创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CategoryImgConfigCreateReqVO extends CategoryImgConfigBaseVO {

}
