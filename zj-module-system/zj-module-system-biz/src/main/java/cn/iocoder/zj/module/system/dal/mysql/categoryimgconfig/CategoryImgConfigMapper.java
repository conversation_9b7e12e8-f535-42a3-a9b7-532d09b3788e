package cn.iocoder.zj.module.system.dal.mysql.categoryimgconfig;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.module.system.controller.admin.categoryimgconfig.vo.CategoryImgConfigExportReqVO;
import cn.iocoder.zj.module.system.controller.admin.categoryimgconfig.vo.CategoryImgConfigPageReqVO;
import cn.iocoder.zj.module.system.controller.admin.categoryimgconfig.vo.CategoryImgConfigReqVO;
import cn.iocoder.zj.module.system.dal.dataobject.categoryimgconfig.CategoryImgConfigDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 类别图片管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CategoryImgConfigMapper extends BaseMapperX<CategoryImgConfigDO> {

    default PageResult<CategoryImgConfigDO> selectPage(CategoryImgConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<CategoryImgConfigDO>()
                .eqIfPresent(CategoryImgConfigDO::getCategory, reqVO.getCategory())
                .eqIfPresent(CategoryImgConfigDO::getApp, reqVO.getApp())
                .eqIfPresent(CategoryImgConfigDO::getType, reqVO.getType())
                .eqIfPresent(CategoryImgConfigDO::getIcon, reqVO.getIcon())
                .eqIfPresent(CategoryImgConfigDO::getDetailIcon, reqVO.getDetailIcon())
                .betweenIfPresent(CategoryImgConfigDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(CategoryImgConfigDO::getId));
    }

    default List<CategoryImgConfigDO> selectList(CategoryImgConfigExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<CategoryImgConfigDO>()
                .eqIfPresent(CategoryImgConfigDO::getCategory, reqVO.getCategory())
                .eqIfPresent(CategoryImgConfigDO::getApp, reqVO.getApp())
                .eqIfPresent(CategoryImgConfigDO::getType, reqVO.getType())
                .eqIfPresent(CategoryImgConfigDO::getIcon, reqVO.getIcon())
                .eqIfPresent(CategoryImgConfigDO::getDetailIcon, reqVO.getDetailIcon())
                .betweenIfPresent(CategoryImgConfigDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(CategoryImgConfigDO::getId));
    }

    default List<CategoryImgConfigDO> selectList(CategoryImgConfigReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<CategoryImgConfigDO>()
                .eqIfPresent(CategoryImgConfigDO::getCategory, reqVO.getCategory())
                .eqIfPresent(CategoryImgConfigDO::getApp, reqVO.getApp())
                .eqIfPresent(CategoryImgConfigDO::getType, reqVO.getType())
                .eqIfPresent(CategoryImgConfigDO::getIcon, reqVO.getIcon())
                .eqIfPresent(CategoryImgConfigDO::getDetailIcon, reqVO.getDetailIcon())
                .betweenIfPresent(CategoryImgConfigDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(CategoryImgConfigDO::getId));
    }
}
