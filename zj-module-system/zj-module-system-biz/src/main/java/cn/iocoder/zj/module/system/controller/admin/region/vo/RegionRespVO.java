package cn.iocoder.zj.module.system.controller.admin.region.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;

@Schema(description = "管理后台 - 地区 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RegionRespVO extends RegionBaseVO {

    @Schema(description = "地区主键编号", required = true)
    private String id;

    @Schema(description = "创建时间", required = true)
    private Date createTime;

}
