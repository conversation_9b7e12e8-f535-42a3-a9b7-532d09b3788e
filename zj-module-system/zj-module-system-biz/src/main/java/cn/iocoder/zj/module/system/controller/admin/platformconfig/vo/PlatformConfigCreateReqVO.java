package cn.iocoder.zj.module.system.controller.admin.platformconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import static io.swagger.v3.oas.annotations.media.Schema.AccessMode.READ_WRITE;

@Schema(description = "管理后台 - 平台配置创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PlatformConfigCreateReqVO extends PlatformConfigBaseVO {

    @Schema(description = "Whether to Detect", accessMode = READ_WRITE)
    private boolean detected;

    @Schema(description = "采集器id", accessMode = READ_WRITE)
    private String collectorId;
}
