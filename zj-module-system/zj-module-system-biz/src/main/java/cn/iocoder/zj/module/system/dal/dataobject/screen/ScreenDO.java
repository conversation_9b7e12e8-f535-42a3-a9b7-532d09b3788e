package cn.iocoder.zj.module.system.dal.dataobject.screen;

import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 系统设置大屏 DO
 *
 * <AUTHOR>
 */
@TableName("system_screen")
@KeySequence("system_screen_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScreenDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 首页模块
     */
    private String module;
    /**
     * 监控条目
     */
    private String monitorEntry;
    /**
     * 监控条目名称
     */
    private String monitorEntryName;
    /**
     * 资源类型
     */
    private String resourceType;
    /**
     * 资源类型名称
     */
    private String resourceTypeName;
    /**
     * 显示顺序
     */
    private Integer sort;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 地区id
     */
    private Long regionId;
    /**
     * 租户名称
     */
    private String tenantName;
    /**
     * 地区名称
     */
    private String regionName;

}
