package cn.iocoder.zj.module.system.service.region;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.util.*;

import cn.iocoder.zj.module.system.controller.admin.region.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.region.RegionDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.system.convert.region.RegionConvert;
import cn.iocoder.zj.module.system.dal.mysql.region.RegionMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.system.enums.ErrorCodeConstants.*;

/**
 * 地区 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RegionServiceImpl implements RegionService {

    @Resource
    private RegionMapper regionMapper;

    @Override
    public String createRegion(RegionCreateReqVO createReqVO) {
        // 插入
        RegionDO region = RegionConvert.INSTANCE.convert(createReqVO);
        regionMapper.insert(region);
        // 返回
        return region.getId();
    }

    @Override
    public void updateRegion(RegionUpdateReqVO updateReqVO) {
        // 校验存在
        validateRegionExists(updateReqVO.getId());
        // 更新
        RegionDO updateObj = RegionConvert.INSTANCE.convert(updateReqVO);
        regionMapper.updateById(updateObj);
    }

    @Override
    public void deleteRegion(String id) {
        // 校验存在
        validateRegionExists(id);
        // 删除
        regionMapper.deleteById(id);
    }

    private void validateRegionExists(String id) {
        if (regionMapper.selectById(id) == null) {
            throw exception(REGION_NOT_EXISTS);
        }
    }

    @Override
    public RegionDO getRegion(String id) {
        return regionMapper.selectById(id);
    }

    @Override
    public List<RegionDO> getRegionList(Collection<String> ids) {
        return regionMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<RegionDO> getRegionPage(RegionPageReqVO pageReqVO) {
        return regionMapper.selectPage(pageReqVO);
    }

    @Override
    public List<RegionDO> getRegionList(RegionExportReqVO exportReqVO) {
        return regionMapper.selectList(exportReqVO);
    }

    @Override
    public List<RegionTreeVO> getRegionTree() {
        return null;
    }

    @Override
    public List<RegionDO> getAllRegion() {
        return regionMapper.getAllRegion();
    }
}
