package cn.iocoder.zj.module.system.dal.mysql.screen;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.system.dal.dataobject.screen.ScreenConfigDO;
import cn.iocoder.zj.module.system.dal.dataobject.screen.ScreenDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.system.controller.admin.screen.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 系统设置大屏 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ScreenMapper extends BaseMapperX<ScreenDO> {

    default PageResult<ScreenDO> selectPage(ScreenPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ScreenDO>()
                .eqIfPresent(ScreenDO::getTenantId, reqVO.getTenantId())
                .likeIfPresent(ScreenDO::getResourceTypeName, reqVO.getResourceTypeName())
                .likeIfPresent(ScreenDO::getMonitorEntryName, reqVO.getMonitorEntryName())
                .orderByAsc(ScreenDO::getSort)
                .orderByDesc(ScreenDO::getUpdateTime));
    }

    default List<ScreenDO> selectList(ScreenExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ScreenDO>()
                .eqIfPresent(ScreenDO::getMonitorEntry, reqVO.getMonitorEntry())
                .eqIfPresent(ScreenDO::getResourceType, reqVO.getResourceType())
                .likeIfPresent(ScreenDO::getResourceTypeName, reqVO.getResourceTypeName())
                .eqIfPresent(ScreenDO::getSort, reqVO.getSort())
                .betweenIfPresent(ScreenDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(ScreenDO::getRegionId, reqVO.getRegionId())
                .likeIfPresent(ScreenDO::getTenantName, reqVO.getTenantName())
                .likeIfPresent(ScreenDO::getRegionName, reqVO.getRegionName())
                .orderByAsc(ScreenDO::getSort));
    }

    default List<ScreenDO> selectByTenantId(Long tenantId) {
        return selectList(new LambdaQueryWrapperX<ScreenDO>()
                .orderByAsc(ScreenDO::getSort)
                .orderByDesc(ScreenDO::getUpdateTime));
    }

    List<ScreenDO> getScreenList(Long tenantId);

    default List<ScreenDO> selectScreenListByTenantId(Long tenantId) {
        return selectList(new LambdaQueryWrapperX<ScreenDO>()
                .orderByAsc(ScreenDO::getSort)
                .orderByDesc(ScreenDO::getUpdateTime)
                .last("limit 0,6"));
    }

    String selectLayoutConfig();

    void createMonitorHomepageDisplay(@Param("tenantId") Long tenantId,@Param("layoutConfig") String layoutConfig);

    void deleteConfigByTenantId(@Param("tenantId") Long tenantId);

    void deleteScreenByTenantId(@Param("tenantId") Long tenantId);

    List<ScreenConfigDO> getScreenConfigByTenantId(@Param("tenantId")Long tenantId);

    ScreenConfigDO getScreenConfigById(@Param("id")Long id);

    void createScreenConfig(@Param("screenConfig")ScreenConfigDO screenConfig);

    void updateScreenConfig(@Param("screenConfig")ScreenConfigDO screenConfig);
}
