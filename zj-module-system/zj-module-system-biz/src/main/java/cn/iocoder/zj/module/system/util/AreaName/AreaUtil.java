package cn.iocoder.zj.module.system.util.AreaName;

import cn.iocoder.zj.framework.ip.core.Area;
import cn.iocoder.zj.framework.ip.core.utils.AreaUtils;

public class AreaUtil {
    public static String getFullAreaName(Long areaId){
        Area area = AreaUtils.getArea(Integer.parseInt(areaId.toString()));
        String provence =area.getParent().getParent().getName();
        String city = area.getParent().getName();
        return provence+"->"+city+"->"+area.getName();
    }
}
