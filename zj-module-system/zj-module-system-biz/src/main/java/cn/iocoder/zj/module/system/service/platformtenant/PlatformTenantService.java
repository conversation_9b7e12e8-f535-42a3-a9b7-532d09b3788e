package cn.iocoder.zj.module.system.service.platformtenant;

import java.util.*;
import javax.validation.*;
import cn.iocoder.zj.module.system.controller.admin.platformtenant.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.platformtenant.PlatformTenantDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 平台租户关系 Service 接口
 *
 * <AUTHOR>
 */
public interface PlatformTenantService {

    /**
     * 创建平台租户关系
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPlatformTenant(@Valid PlatformTenantCreateReqVO createReqVO);

    /**
     * 更新平台租户关系
     *
     * @param updateReqVO 更新信息
     */
    void updatePlatformTenant(@Valid PlatformTenantUpdateReqVO updateReqVO);

    /**
     * 删除平台租户关系
     *
     * @param id 编号
     */
    void deletePlatformTenant(Long id);

    /**
     * 获得平台租户关系
     *
     * @param id 编号
     * @return 平台租户关系
     */
    PlatformTenantDO getPlatformTenant(Long id);

    /**
     * 获得平台租户关系列表
     *
     * @param ids 编号
     * @return 平台租户关系列表
     */
    List<PlatformTenantDO> getPlatformTenantList(Collection<Long> ids);

    /**
     * 获得平台租户关系分页
     *
     * @param pageReqVO 分页查询
     * @return 平台租户关系分页
     */
    PageResult<PlatformTenantDO> getPlatformTenantPage(PlatformTenantPageReqVO pageReqVO);

    /**
     * 获得平台租户关系列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 平台租户关系列表
     */
    List<PlatformTenantDO> getPlatformTenantList(PlatformTenantExportReqVO exportReqVO);

}
