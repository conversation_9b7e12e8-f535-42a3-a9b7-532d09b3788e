package cn.iocoder.zj.module.system.framework.rpc.config;

import cn.iocoder.cloud.module.cloudedge.api.monitor.MonitorApi;
import cn.iocoder.zj.module.collector.api.PlatformInfoApi;
import cn.iocoder.zj.module.customer.api.contractInfo.ContractInfoApi;
import cn.iocoder.zj.module.infra.api.file.FileApi;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.monitor.api.asset.GatherAssetApi;
import cn.iocoder.zj.module.monitor.api.demo.DemoApi;
import cn.iocoder.zj.module.monitor.api.hardware.HardWareInfoApi;
import cn.iocoder.zj.module.monitor.api.hostinfo.HostInfoApi;
import cn.iocoder.zj.module.om.api.asset.MonitorassetApi;
import cn.iocoder.zj.module.om.api.dbfile.DbfileApi;
import cn.iocoder.zj.module.report.api.subscription.dto.ReportSubscriptionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.tenant.TenantApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = { FileApi.class,AlarmConfigApi.class,DemoApi.class,TenantApi.class, GatherAssetApi.class, DbfileApi.class, ContractInfoApi.class,
        ContractInfoApi.class,
        HardWareInfoApi.class,
        ReportSubscriptionApi.class, RoleApi.class, HostInfoApi.class,FileApi.class, MonitorApi.class, MonitorassetApi.class, PlatformInfoApi.class} )
public class RpcConfiguration {

}
