package cn.iocoder.zj.module.system.dal.mysql.logger;

import cn.iocoder.zj.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.module.system.controller.admin.logger.vo.operatelog.OperateLogExportReqVO;
import cn.iocoder.zj.module.system.controller.admin.logger.vo.operatelog.OperateLogPageReqVO;
import cn.iocoder.zj.module.system.dal.dataobject.logger.OperateLogDO;
import jodd.util.StringUtil;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

@Mapper
public interface OperateLogMapper extends BaseMapperX<OperateLogDO> {

    default PageResult<OperateLogDO> selectPage(OperateLogPageReqVO reqVO, Collection<Long> userIds) {
        LambdaQueryWrapperX<OperateLogDO> query = new LambdaQueryWrapperX<OperateLogDO>()
                .likeIfPresent(OperateLogDO::getModule, reqVO.getModule())
                .inIfPresent(OperateLogDO::getUserId, userIds)
                .eqIfPresent(OperateLogDO::getType, reqVO.getType())
                .betweenIfPresent(OperateLogDO::getStartTime, reqVO.getStartTime())
                .eqIfPresent(OperateLogDO::getBusinessId, reqVO.getBusinessId());
        if (Boolean.TRUE.equals(reqVO.getSuccess())) {
            query.eq(OperateLogDO::getResultCode, GlobalErrorCodeConstants.SUCCESS.getCode());
        } else if (Boolean.FALSE.equals(reqVO.getSuccess())) {
            query.gt(OperateLogDO::getResultCode, GlobalErrorCodeConstants.SUCCESS.getCode());
        }

        if (StringUtil.isNotEmpty(reqVO.getSortDirection())){
            if (reqVO.getSortDirection().equals("asc")){
                if (reqVO.getSortBy().equals("startTime")){
                    query.orderByAsc(OperateLogDO::getStartTime);
                }
                if (reqVO.getSortBy().equals("duration")){
                    query.orderByAsc(OperateLogDO::getDuration);
                }
            }else if (reqVO.getSortDirection().equals("desc")){
                if (reqVO.getSortBy().equals("startTime")){
                    query.orderByDesc(OperateLogDO::getStartTime);
                }
                if (reqVO.getSortBy().equals("duration")){
                    query.orderByDesc(OperateLogDO::getDuration);
                }
            }
        }else {
            query.orderByDesc(OperateLogDO::getStartTime);
        }
//        query.orderByDesc(OperateLogDO::getId); // 降序

        return selectPage(reqVO, query);
    }

    default List<OperateLogDO> selectList(OperateLogExportReqVO reqVO, Collection<Long> userIds) {
        LambdaQueryWrapperX<OperateLogDO> query = new LambdaQueryWrapperX<OperateLogDO>()
                .likeIfPresent(OperateLogDO::getModule, reqVO.getModule())
                .inIfPresent(OperateLogDO::getUserId, userIds)
                .eqIfPresent(OperateLogDO::getType, reqVO.getType())
                .betweenIfPresent(OperateLogDO::getStartTime, reqVO.getStartTime());
        if (Boolean.TRUE.equals(reqVO.getSuccess())) {
            query.eq(OperateLogDO::getResultCode, GlobalErrorCodeConstants.SUCCESS.getCode());
        } else if (Boolean.FALSE.equals(reqVO.getSuccess())) {
            query.gt(OperateLogDO::getResultCode, GlobalErrorCodeConstants.SUCCESS.getCode());
        }
        query.orderByDesc(OperateLogDO::getId); // 降序
        return selectList(query);
    }

}
