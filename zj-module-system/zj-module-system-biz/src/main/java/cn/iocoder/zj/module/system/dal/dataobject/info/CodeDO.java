package cn.iocoder.zj.module.system.dal.dataobject.info;

import lombok.*;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 申请码 DO
 *
 * <AUTHOR>
 */
@TableName("app_code")
@KeySequence("app_code_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CodeDO {
    /**
     * 主键
     */
    @TableId
    private Long appId;
    /**
     * 申请码
     */
    private String appCode;
    /**
     * 申请企业
     */
    private String appCustName;

    private LocalDateTime createTime;
}
