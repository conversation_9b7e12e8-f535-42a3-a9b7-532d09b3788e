package cn.iocoder.zj.module.system.service.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.constants.FileTypeConstants;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.enums.UserTypeEnum;
import cn.iocoder.zj.framework.common.exception.ServiceException;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.UserBindDTO;
import cn.iocoder.zj.framework.common.util.collection.CollectionUtils;
import cn.iocoder.zj.framework.common.util.dingtalk.QyWxUtils;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.framework.common.util.monitor.TracerUtils;
import cn.iocoder.zj.framework.common.util.servlet.ServletUtils;
import cn.iocoder.zj.framework.common.util.string.StringUtil;
import cn.iocoder.zj.framework.datapermission.core.annotation.DataPermission;
import cn.iocoder.zj.framework.datapermission.core.util.DataPermissionUtils;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.infra.api.file.FileApi;
import cn.iocoder.zj.module.monitor.api.demo.DemoApi;
import cn.iocoder.zj.module.om.api.dbfile.DbfileApi;
import cn.iocoder.zj.module.system.api.logger.dto.LoginLogCreateReqDTO;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.zj.module.system.controller.admin.auth.vo.AuthUpdatePasswordReqVO;
import cn.iocoder.zj.module.system.controller.admin.permission.vo.role.RoleSimpleRespVO;
import cn.iocoder.zj.module.system.controller.admin.user.vo.profile.UserProfileUpdatePasswordReqVO;
import cn.iocoder.zj.module.system.controller.admin.user.vo.profile.UserProfileUpdateReqVO;
import cn.iocoder.zj.module.system.controller.admin.user.vo.user.*;
import cn.iocoder.zj.module.system.convert.user.UserConvert;
import cn.iocoder.zj.module.system.dal.dataobject.dept.DeptDO;
import cn.iocoder.zj.module.system.dal.dataobject.dept.UserPostDO;
import cn.iocoder.zj.module.system.dal.dataobject.permission.UserRoleDO;
import cn.iocoder.zj.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.zj.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.zj.module.system.dal.mysql.dept.UserPostMapper;
import cn.iocoder.zj.module.system.dal.mysql.permission.UserRoleMapper;
import cn.iocoder.zj.module.system.dal.mysql.user.AdminUserMapper;
import cn.iocoder.zj.module.system.enums.logger.LoginLogTypeEnum;
import cn.iocoder.zj.module.system.enums.logger.LoginResultEnum;
import cn.iocoder.zj.module.system.service.dept.DeptService;
import cn.iocoder.zj.module.system.service.dept.PostService;
import cn.iocoder.zj.module.system.service.logger.LoginLogService;
import cn.iocoder.zj.module.system.service.permission.PermissionService;
import cn.iocoder.zj.module.system.service.tenant.TenantService;
import com.google.common.annotations.VisibleForTesting;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.thymeleaf.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.convertList;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.convertSet;
import static cn.iocoder.zj.module.system.enums.ErrorCodeConstants.*;

/**
 * 后台用户 Service 实现类
 *
 * <AUTHOR>
 */
@Service("adminUserService")
@Slf4j
public class AdminUserServiceImpl implements AdminUserService {

    @Value("${sys.user.init-password:zjyuanma}")
    private String userInitPassword;

    @Resource
    private AdminUserMapper userMapper;

    @Resource
    private DeptService deptService;
    @Resource
    private PostService postService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private PasswordEncoder passwordEncoder;
    @Resource
    @Lazy // 延迟，避免循环依赖报错
    private TenantService tenantService;

    @Resource
    private UserPostMapper userPostMapper;

    @Resource
    private FileApi fileApi;
    @Resource
    private DemoApi demoApi;
    @Resource
    private DbfileApi dbfileApi;
    @Resource
    private LoginLogService loginLogService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private UserRoleMapper userRoleMapper;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createUser(UserCreateReqVO reqVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        TenantDO tenantDO = tenantService.getTenant(loginUser.getTenantId());
        // 校验账户配合
        tenantService.handleTenantInfo(tenant -> {
            long count = userMapper.selectCount();
            if (count >= tenant.getAccountCount()) {
                throw exception(USER_COUNT_MAX, tenant.getAccountCount());
            }
        });

        // 校验正确性
        validateUserForCreateOrUpdate(null, reqVO.getUsername(), reqVO.getMobile(), reqVO.getEmail(),
                reqVO.getDeptId(), reqVO.getPostIds());
        // 插入用户
        AdminUserDO user = UserConvert.INSTANCE.convert(reqVO);
        user.setOrganization(tenantDO.getOrganization());
        if(StrUtil.isNotEmpty(reqVO.getOrganization())){
            user.setOrganization(reqVO.getOrganization());
        }
        user.setStatus(CommonStatusEnum.ENABLE.getStatus()); // 默认开启
        user.setPassword(encodePassword(reqVO.getPassword())); // 加密密码
        //企微校验
        if (redisTemplate.hasKey("app_key_secret:app")) {
            UserBindDTO userBindDO = JsonUtils.parseObject(redisTemplate.opsForValue().get("app_key_secret:app").toString(), UserBindDTO.class);
            String token = QyWxUtils.getToken(userBindDO.getWxCorpid(), userBindDO.getWxCorpsecret());
            String userID = QyWxUtils.getUserID(reqVO.getMobile(), token);
            if(StrUtil.isNotBlank(userID)){
                user.setWxCheckState(0);
                user.setWxUserId(userID);
            }else {
                user.setWxCheckState(1);
            }
        }
        userMapper.insert(user);
        // 插入关联岗位
        if (CollectionUtil.isNotEmpty(user.getPostIds())) {
            userPostMapper.insertBatch(convertList(user.getPostIds(),
                    postId -> new UserPostDO().setUserId(user.getId()).setPostId(postId)));
        }
        return user.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUser(UserUpdateReqVO reqVO) {
        // 校验正确性
        validateUserForCreateOrUpdate(reqVO.getId(), reqVO.getUsername(), reqVO.getMobile(), reqVO.getEmail(),
                reqVO.getDeptId(), reqVO.getPostIds());
        // 更新用户
        AdminUserDO toUpdateuser = userMapper.selectOne("id",reqVO.getId());
        TenantDO tenantDO = tenantService.getTenant(toUpdateuser.getTenantId());
        AdminUserDO updateObj = UserConvert.INSTANCE.convert(reqVO);
        updateObj.setOrganization(tenantDO.getOrganization());
        //企微校验
        if (redisTemplate.hasKey("app_key_secret:app") && StrUtil.isNotBlank(reqVO.getMobile()) && !toUpdateuser.getMobile().equals(reqVO.getMobile())) {
            UserBindDTO userBindDO = JsonUtils.parseObject(redisTemplate.opsForValue().get("app_key_secret:app").toString(), UserBindDTO.class);
            String token = QyWxUtils.getToken(userBindDO.getWxCorpid(), userBindDO.getWxCorpsecret());
            String userID = QyWxUtils.getUserID(reqVO.getMobile(), token);
            if(StrUtil.isNotBlank(userID)){
                updateObj.setWxCheckState(0);
                updateObj.setWxUserId(userID);
            }else {
                updateObj.setWxCheckState(1);
                updateObj.setWxUserId(null);
            }
        }
        userMapper.updateById(updateObj);
        // 更新岗位
        updateUserPost(reqVO, updateObj);
    }

    private void updateUserPost(UserUpdateReqVO reqVO, AdminUserDO updateObj) {
        Long userId = reqVO.getId();
        Set<Long> dbPostIds = convertSet(userPostMapper.selectListByUserId(userId), UserPostDO::getPostId);
        // 计算新增和删除的岗位编号
        Set<Long> postIds = updateObj.getPostIds();
        Collection<Long> createPostIds = CollUtil.subtract(postIds, dbPostIds);
        Collection<Long> deletePostIds = CollUtil.subtract(dbPostIds, postIds);
        // 执行新增和删除。对于已经授权的菜单，不用做任何处理
        if (!CollectionUtil.isEmpty(createPostIds)) {
            userPostMapper.insertBatch(convertList(createPostIds,
                    postId -> new UserPostDO().setUserId(userId).setPostId(postId)));
        }
        if (!CollectionUtil.isEmpty(deletePostIds)) {
            userPostMapper.deleteByUserIdAndPostId(userId, deletePostIds);
        }
    }

    @Override
    public void updateUserLogin(Long id, String loginIp) {
        userMapper.updateById(new AdminUserDO().setId(id).setLoginIp(loginIp).setLoginDate(LocalDateTime.now()));
    }

    @Override
    public void updateUserProfile(Long id, UserProfileUpdateReqVO reqVO) {
        // 校验正确性
        validateUserExists(id);
        validateEmailUnique(id, reqVO.getEmail());
        validateMobileUnique(id, reqVO.getMobile());
        // 执行更新
        userMapper.updateById(UserConvert.INSTANCE.convert(reqVO).setId(id));
    }

    @Override
    public void updateUserPassword(Long id, UserProfileUpdatePasswordReqVO reqVO) {
        // 校验旧密码密码
        validateOldPassword(id, reqVO.getOldPassword());
        // 执行更新
        AdminUserDO updateObj = new AdminUserDO().setId(id);
        updateObj.setPassword(encodePassword(reqVO.getNewPassword())); // 加密密码
        userMapper.updateById(updateObj);
    }

    @Override
    public String updateUserAvatar(Long id, MultipartFile file) throws Exception {
        validateUserExists(id);
        // 存储文件
        String fileName = "";
        if (file.getOriginalFilename().equals("blob")){
            fileName = StrUtil.uuid();
        }else {
            fileName = file.getOriginalFilename();
        }
        String path = StringUtil.getSavePath(fileName, FileTypeConstants.IMG_TYPE,"avatar");
        String avatar = fileApi.createFileUrl(null, path, IoUtil.readBytes(file.getInputStream()));
        // 更新路径
        AdminUserDO sysUserDO = new AdminUserDO();
        sysUserDO.setId(id);
        sysUserDO.setAvatar(avatar);
        userMapper.updateById(sysUserDO);
        return avatar;
    }

    @Override
    @TenantIgnore
    public void updateUserPassword(Long id, String password) {
        // 校验用户存在
        validateUserExists(id);
        AdminUserDO adminUserDO = userMapper.selectById(id);
        // 更新密码
        AdminUserDO updateObj = new AdminUserDO();
        updateObj.setId(id);
        updateObj.setPassword(encodePassword(password)); // 加密密码
        updateObj.setFirstLogin(1);
        updateObj.setLocking(0);
        stringRedisTemplate.delete("login_fail_"+adminUserDO.getMobile());
        userMapper.updateById(updateObj);
    }

    @Override
    public void updateUserStatus(Long id, Integer status) {
        // 校验用户存在
        validateUserExists(id);
        // 更新状态
        AdminUserDO updateObj = new AdminUserDO();
        updateObj.setId(id);
        updateObj.setStatus(status);
        userMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteUser(Long id) {
        // 校验用户存在
        validateUserExists(id);
        // 校验用户是否有排班
        validateSchedul(id);
        // 校验用户是否为工单执行人
        validateWorkOrder(id);
        // 删除用户
        userMapper.deleteById(id);
        // 删除用户关联数据
        permissionService.processUserDeleted(id);
        // 删除用户岗位
        userPostMapper.deleteByUserId(id);
    }

    @Override
    public AdminUserDO getUserByUsername(String username) {
        return userMapper.selectByUsername(username);
    }

    @Override
    public AdminUserDO getUserByMobile(String mobile) {
        return userMapper.selectByMobile(mobile);
    }

    @Override
    public AdminUserDO getUserByMail(String mail) {
        return userMapper.selectByEmail(mail);
    }

    @Override
    public PageResult<AdminUserDO> getUserPage(UserPageReqVO reqVO) {
        if (reqVO.getRoleIds() != null) {
            // 查询角色对应用户列表
            ArrayList<Long> roleIds = new ArrayList<>();
            roleIds.add(reqVO.getRoleIds());
            List<UserRoleDO> userIds = userRoleMapper.selectListByRoleIds(roleIds);
            if (CollUtil.isEmpty(userIds)) {
                // 如果查询不到用户，直接返回空分页结果
                return new PageResult<>(Collections.emptyList(), 0L);
            }
            reqVO.setUserIds(userIds.stream().map(UserRoleDO::getUserId).collect(Collectors.toList()));
        }
        return userMapper.selectPage(reqVO, getDeptCondition(reqVO.getDeptId()));
    }

    @Override
    public AdminUserDO getUser(Long id) {
        return userMapper.selectById(id);
    }

    @Override
    public List<AdminUserDO> getUserListByDeptIds(Collection<Long> deptIds) {
        if (CollUtil.isEmpty(deptIds)) {
            return Collections.emptyList();
        }
        return userMapper.selectListByDeptIds(deptIds);
    }

    @Override
    public List<AdminUserDO> getUserListByPostIds(Collection<Long> postIds) {
        if (CollUtil.isEmpty(postIds)) {
            return Collections.emptyList();
        }
        Set<Long> userIds = convertSet(userPostMapper.selectListByPostIds(postIds), UserPostDO::getUserId);
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        return userMapper.selectBatchIds(userIds);
    }

    @Override
    public List<AdminUserDO> getUserList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return userMapper.selectBatchIds(ids);
    }

    @Override
    public void validateUserList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        // 获得岗位信息
        List<AdminUserDO> users = userMapper.selectBatchIds(ids);
        Map<Long, AdminUserDO> userMap = CollectionUtils.convertMap(users, AdminUserDO::getId);
        // 校验
        ids.forEach(id -> {
            AdminUserDO user = userMap.get(id);
            if (user == null) {
                throw exception(USER_NOT_EXISTS);
            }
            if (!CommonStatusEnum.ENABLE.getStatus().equals(user.getStatus())) {
                throw exception(USER_IS_DISABLE, user.getNickname());
            }
        });
    }

    @Override
    public List<AdminUserDO> getUserList(UserExportReqVO reqVO) {
        LoginUser user = SecurityFrameworkUtils.getLoginUser();
        Long id = userMapper.getUserIdByTenantId(user.getTenantId());
        return userMapper.selectList(reqVO, getDeptCondition(reqVO.getDeptId()),id);
    }

    @Override
    public List<AdminUserDO> getUserListByNickname(String nickname) {
        return userMapper.selectListByNickname(nickname);
    }

    /**
     * 获得部门条件：查询指定部门的子部门编号们，包括自身
     *
     * @param deptId 部门编号
     * @return 部门编号集合
     */
    private Set<Long> getDeptCondition(Long deptId) {
        if (deptId == null) {
            return Collections.emptySet();
        }
        Set<Long> deptIds = convertSet(deptService.getChildDeptList(deptId), DeptDO::getId);
        deptIds.add(deptId); // 包括自身
        return deptIds;
    }

    @TenantIgnore
    private void validateUserForCreateOrUpdate(Long id, String username, String mobile, String email,
                                               Long deptId, Set<Long> postIds) {
        // 关闭数据权限，避免因为没有数据权限，查询不到数据，进而导致唯一校验不正确
        DataPermissionUtils.executeIgnore(() -> {
            // 校验用户存在
            validateUserExists(id);
            // 校验用户名唯一
            validateUsernameUnique(id, username);
            // 校验手机号唯一
            validateMobileUnique(id, mobile);
            // 校验邮箱唯一
            validateEmailUnique(id, email);
            // 校验部门处于开启状态
            deptService.validateDeptList(CollectionUtils.singleton(deptId));
            // 校验岗位处于开启状态
            postService.validatePostList(postIds);
        });
    }

    @VisibleForTesting
    void validateUserExists(Long id) {
        if (id == null) {
            return;
        }
        AdminUserDO user = userMapper.selectById(id);
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }
    }

    void validateSchedul(Long id) {
        AdminUserDO user = userMapper.selectById(id);
        String name=user.getNickname();
        if (dbfileApi.getSchedulCount(id,name) > 0){
            throw exception(USER_HAVE_SCHEDUL);
        }
    }

    void validateWorkOrder(Long id) {
        AdminUserDO user = userMapper.selectById(id);
        String name=user.getNickname();
        if (dbfileApi.getWorkOrderCount(id,name) > 0){
            throw exception(USER_HAVE_WORK_ORDER);
        }
    }

    @VisibleForTesting
    void validateUsernameUnique(Long id, String username) {
        if (StrUtil.isBlank(username)) {
            return;
        }
        AdminUserDO user = userMapper.selectByUsername(username);
        if (user == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的用户
        if (id == null) {
            throw exception(USER_USERNAME_EXISTS);
        }
        if (!user.getId().equals(id)) {
            throw exception(USER_USERNAME_EXISTS);
        }
    }

    @VisibleForTesting
    void validateEmailUnique(Long id, String email) {
        if (StrUtil.isBlank(email)) {
            return;
        }
        AdminUserDO user = userMapper.selectByEmail(email);
        if (user == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的用户
        if (id == null) {
            throw exception(USER_EMAIL_EXISTS);
        }
        if (!user.getId().equals(id)) {
            throw exception(USER_EMAIL_EXISTS);
        }
    }

    @VisibleForTesting
    void validateMobileUnique(Long id, String mobile) {
        if (StrUtil.isBlank(mobile)) {
            return;
        }
        AdminUserDO user = userMapper.selectByMobile(mobile);
        if (user == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的用户
        if (id == null) {
            throw exception(USER_MOBILE_EXISTS);
        }
        if (!user.getId().equals(id)) {
            throw exception(USER_MOBILE_EXISTS);
        }
    }

    /**
     * 校验旧密码
     *
     * @param id          用户 id
     * @param oldPassword 旧密码
     */
    @VisibleForTesting
    void validateOldPassword(Long id, String oldPassword) {
        AdminUserDO user = userMapper.selectById(id);
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }
        if (!isPasswordMatch(oldPassword, user.getPassword())) {
            throw exception(USER_PASSWORD_FAILED);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class) // 添加事务，异常则回滚所有导入
    public UserImportRespVO importUserList(List<UserImportExcelVO> importUsers, boolean isUpdateSupport) {
        if (CollUtil.isEmpty(importUsers)) {
            throw exception(USER_IMPORT_LIST_IS_EMPTY);
        }
        UserImportRespVO respVO = UserImportRespVO.builder().createUsernames(new ArrayList<>())
                .updateUsernames(new ArrayList<>()).failureUsernames(new LinkedHashMap<>()).build();
        importUsers.forEach(importUser -> {
            // 校验，判断是否有不符合的原因
            try {
                // 校验，判断是否有空值
                if ((null==importUser.getMobile() || importUser.getMobile().equals("")) && (null==importUser.getEmail() || importUser.getEmail().equals(""))){
                    throw exception(USER_IMPORT_MOBILE_AND_EMAIL_IS_EMPTY);
                }
                if ((null==importUser.getDeptStr() || importUser.getDeptStr().equals(""))){
                    throw exception(USER_IMPORT_DEPT_NOT_VALID);
                }
                if (null!=importUser.getMobile() && !importUser.getMobile().equals("")){
                    if (!validPhoneNo(importUser.getMobile())){
                        throw exception(USER_IMPORT_MOBILE_NOT_VALID);
                    }
                }
                if (null!=importUser.getEmail() && !importUser.getEmail().equals("")){
                    if (!isValidEmail(importUser.getEmail())){
                        throw exception(USER_IMPORT_EMAIL_NOT_VALID);
                    }
                }
                validateUserForCreateOrUpdate(null, null, importUser.getMobile(), importUser.getEmail(),
                        null, null);
            } catch (ServiceException ex) {
                return;
            }
            // 判断如果不存在，在进行插入
            AdminUserDO existUser = userMapper.selectByMobile(importUser.getMobile());
            if (existUser == null) {
                userMapper.insert(UserConvert.INSTANCE.convert(importUser)
                        .setPassword(encodePassword(userInitPassword)).setPostIds(new HashSet<>())); // 设置默认密码及空岗位编号数组
                respVO.getCreateUsernames().add(importUser.getNickname());
                return;
            }
            // 如果存在，判断是否允许更新
            AdminUserDO updateUser = UserConvert.INSTANCE.convert(importUser);
            updateUser.setId(existUser.getId());
            userMapper.updateById(updateUser);
        });
        return respVO;
    }

    @Override
    public List<AdminUserDO> getUserListByStatus(Integer status) {
        return userMapper.selectListByStatus(status);
    }

    @Override
    public boolean isPasswordMatch(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    @Override
    @TenantIgnore
    public List<UserSimpleRespVO> getOperations(Long userId,String roleCode) {
        return userMapper.getOperations(userId,roleCode);
    }

    @Override
    public Map<Long, RoleSimpleRespVO> getRoleMapByUserIds(List<Long> userIds) {
        List<RoleSimpleRespVO> userRole = userMapper.getRoleMapByUserIds(userIds);
        return CollectionUtils.convertMap(userRole, RoleSimpleRespVO::getUserId);
    }

    @Override
    public AdminUserDO getPlatformAdmin() {
        return userMapper.getPlatformAdmin(userMapper.getPlatformAdminId());
    }

    @Override
    public Long getCountByRoleId(Long id) {
        return userMapper.getCountByRoleId(id);
    }

    @Override
    public List<UserSimpleRespVO> getEnforcer(Long userId) {
        return userMapper.getEnforcer(userId);
    }

    @Override
    public List<Long> getUserIdsByTenantId(Long userId) {
        return userMapper.getUserIdsByTenantId(userId);
    }

    @Override
    @TenantIgnore
    public void deleteByTenantId(Long tenantId) {
        userMapper.deleteByTenantId(tenantId);
    }

    @Override
    public List<Long> getHomologousUsers(Long userId) {
        return userMapper.getHomologousUsers(userId);
    }

    @Override
    public List<String> getUserMenus(Long userId) {
        return userMapper.getUserMenus(userId);
    }

    @Override
    public void updateUserEmail(String email) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        AdminUserDO userDO = new AdminUserDO();
        userDO.setId(loginUser.getId());
        userDO.setEmail(email);
        userMapper.updateById(userDO);
    }


    @Override
    public Boolean updatePassword(AuthUpdatePasswordReqVO reqVO) {
        AdminUserDO user = userMapper.selectOne("id",reqVO.getUserId(),"deleted",0);
        String psdw = encodePassword(reqVO.getPassword());
        user.setPassword(psdw);
        user.setFirstLogin(0);
        userMapper.updateById(user);
        createLoginLog(reqVO.getUserId(), user.getNickname(), LoginLogTypeEnum.LOGIN_USERNAME, LoginResultEnum.SUCCESS);
        return true;
    }

    @Override
    @DataPermission(enable = false)
    @TenantIgnore
    public AdminUserDO selectUserById(Long userId) {
        return userMapper.selectUserById(userId);
    }

    @Override
    @TenantIgnore
    public List<UserSimpleRespVO> getOrderEnforcer(Long platformId) {
        List<TenantDO> list = tenantService.getOrderEnforcer(platformId);
        List<UserSimpleRespVO> result = new ArrayList<>();
        for (TenantDO item : list) {
            String maintainerId = item.getMaintainerId();
            String maintainerName = item.getMaintainerName();
            List<String> maintainerIdList = maintainerId != null && !maintainerId.isEmpty()
                    ? Arrays.asList(maintainerId.split(","))
                    : Collections.emptyList();
            List<String> maintainerNameList = maintainerName != null && !maintainerName.isEmpty()
                    ? Arrays.asList(maintainerName.split(","))
                    : Collections.emptyList();
            for (int i = 0; i < maintainerIdList.size(); i++) {
                UserSimpleRespVO respVO = new UserSimpleRespVO();
                respVO.setId(Long.valueOf(maintainerIdList.get(i)));
                respVO.setNickname(maintainerNameList.get(i));
                result.add(respVO);
            }
        }
        List<UserSimpleRespVO> uniqueResultStream = result.stream()
                .collect(Collectors.toMap(
                        UserSimpleRespVO::getId,
                        user -> user,
                        (existing, replacement) -> existing))
                .values()
                .stream()
                .collect(Collectors.toList());
        return uniqueResultStream;
    }

    @Override
    @TenantIgnore
    public List<AdminUserDO> getUserByIds(List<String> ids) {
        LambdaQueryWrapperX<AdminUserDO> lqw = new LambdaQueryWrapperX<>();
        lqw.in(AdminUserDO::getId,ids);
        return userMapper.selectList(lqw);
    }

    @Override
    @TenantIgnore
    public void updateById(AdminUserDO user) {
        userMapper.updateById(user);
    }

    @Override
    public Boolean validateEmail(String email) {

        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (StrUtil.isBlank(email)) {
            return false;
        }
        AdminUserDO user = userMapper.selectByEmail(email);
        if (user == null||(ObjectUtil.isNotEmpty(user) && StringUtils.toString(loginUser.getId()).equals(StringUtils.toString(user.getId())))) {
            return false;
        }else{
            return true;
        }
    }

    @Override
    public void updateOrganizationByTenantId(Long tenantId, String organization) {
        userMapper.updateOrganizationByTenantId(tenantId,organization);
    }

    @Override
    public Integer getAssetNum(Long tenantId) {
        return userMapper.getAssetNum(tenantId);
    }

    @Override
    public Long getUserIdByTenantId(Long tenantId) {
        return userMapper.getUserIdByTenantId(tenantId);
    }

    @Override
    public Integer getContractCount(Long tenantId) {
        return userMapper.getContractCount(tenantId);
    }

    @Override
    public AdminUserDO getUserByUserId(String userID) {
        return userMapper.getUserByUserId(userID);
    }

    @Override
    public Integer getStateByTenantId(Long tenantId) {
        return userMapper.getStateByTenantId(tenantId);
    }

    @Override
    public AdminUserRespDTO getUserById(Long id) {
        return userMapper.getAdminUserByUserId(id);
    }

    /**
     * 对密码进行加密
     *
     * @param password 密码
     * @return 加密后的密码
     */
    private String encodePassword(String password) {
        return passwordEncoder.encode(password);
    }

    /**
     * 验证手机号格式
     *
     * @param phoneNo 手机号
     * @return 手机号格式是否正确
     */
    public static boolean validPhoneNo(String phoneNo) {
        if ((phoneNo!= null) && (!phoneNo.isEmpty())) {
            return Pattern.matches("^1[3-9]\\d{9}$", phoneNo);
        }
        return false;
    }
    /**
     * 验证邮箱格式
     *
     * @param email 邮箱
     * @return 邮箱格式是否正确
     */
    public static boolean isValidEmail(String email) {
        if ((email != null) && (!email.isEmpty())) {
            return Pattern.matches("^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*\\.[a-zA-Z0-9]{2,6}$", email);
        }
        return false;
    }
    public void createLoginLog(Long userId, String username,
                               LoginLogTypeEnum logTypeEnum, LoginResultEnum loginResult) {
        // 插入登录日志
        LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
        reqDTO.setLogType(logTypeEnum.getType());
        reqDTO.setTraceId(TracerUtils.getTraceId());
        reqDTO.setUserId(userId);
        reqDTO.setUserType(getUserType().getValue());
        reqDTO.setUsername(username);
        reqDTO.setUserAgent(ServletUtils.getUserAgent());
        reqDTO.setUserIp(ServletUtils.getClientIP());
        reqDTO.setResult(loginResult.getResult());
        loginLogService.createLoginLog(reqDTO);
        // 更新最后登录时间
        if (userId != null && Objects.equals(LoginResultEnum.SUCCESS.getResult(), loginResult.getResult())) {
            updateUserLogin(userId, ServletUtils.getClientIP());
        }
    }
    private UserTypeEnum getUserType() {
        return UserTypeEnum.ADMIN;
    }
}
