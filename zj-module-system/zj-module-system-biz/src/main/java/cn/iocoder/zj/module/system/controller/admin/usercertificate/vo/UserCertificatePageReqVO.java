package cn.iocoder.zj.module.system.controller.admin.usercertificate.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 授权凭证分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserCertificatePageReqVO extends PageParam {

    @Schema(description = "所有者id")
    private Long userId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "所有者名称")
    private String ownerName;

    @Schema(description = "授权账户")
    private String username;

    @Schema(description = "密码")
    private String password;

    @Schema(description = "私钥密码")
    private String passphrase;

    @Schema(description = "私钥")
    private String privateKey;

    @Schema(description = "账户类型")
    private String type;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String startTime;

    @Schema(description = "结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String endTime;

}
