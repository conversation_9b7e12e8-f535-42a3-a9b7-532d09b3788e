package cn.iocoder.zj.module.system.service.usercertificate;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import cn.iocoder.zj.framework.test.core.ut.BaseDbUnitTest;

import cn.iocoder.zj.module.system.controller.admin.usercertificate.vo.*;
import cn.iocoder.zj.module.system.dal.dataobject.usercertificate.UserCertificateDO;
import cn.iocoder.zj.module.system.dal.mysql.usercertificate.UserCertificateMapper;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static cn.iocoder.zj.module.system.enums.ErrorCodeConstants.*;
import static cn.iocoder.zj.framework.test.core.util.AssertUtils.*;
import static cn.iocoder.zj.framework.test.core.util.RandomUtils.*;
import static cn.iocoder.zj.framework.common.util.date.LocalDateTimeUtils.*;
import static cn.iocoder.zj.framework.common.util.object.ObjectUtils.*;
import static cn.iocoder.zj.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
* {@link UserCertificateServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(UserCertificateServiceImpl.class)
public class UserCertificateServiceImplTest extends BaseDbUnitTest {

    @Resource
    private UserCertificateServiceImpl userCertificateService;

    @Resource
    private UserCertificateMapper userCertificateMapper;

    @Test
    public void testCreateUserCertificate_success() {
        // 准备参数
        UserCertificateCreateReqVO reqVO = randomPojo(UserCertificateCreateReqVO.class);

        // 调用
        Integer userCertificateId = userCertificateService.createUserCertificate(reqVO);
        // 断言
        assertNotNull(userCertificateId);
        // 校验记录的属性是否正确
        UserCertificateDO userCertificate = userCertificateMapper.selectById(userCertificateId);
        assertPojoEquals(reqVO, userCertificate);
    }

    @Test
    public void testUpdateUserCertificate_success() {
        // mock 数据
        UserCertificateDO dbUserCertificate = randomPojo(UserCertificateDO.class);
        userCertificateMapper.insert(dbUserCertificate);// @Sql: 先插入出一条存在的数据
        // 准备参数
        UserCertificateUpdateReqVO reqVO = randomPojo(UserCertificateUpdateReqVO.class, o -> {
            o.setId(dbUserCertificate.getId()); // 设置更新的 ID
        });

        // 调用
        userCertificateService.updateUserCertificate(reqVO);
        // 校验是否更新正确
        UserCertificateDO userCertificate = userCertificateMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, userCertificate);
    }

    @Test
    public void testUpdateUserCertificate_notExists() {
        // 准备参数
        UserCertificateUpdateReqVO reqVO = randomPojo(UserCertificateUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> userCertificateService.updateUserCertificate(reqVO), USER_CERTIFICATE_NOT_EXISTS);
    }

    @Test
    public void testDeleteUserCertificate_success() {
        // mock 数据
        UserCertificateDO dbUserCertificate = randomPojo(UserCertificateDO.class);
        userCertificateMapper.insert(dbUserCertificate);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Integer id = dbUserCertificate.getId();

        // 调用
        userCertificateService.deleteUserCertificate(id);
       // 校验数据不存在了
       assertNull(userCertificateMapper.selectById(id));
    }

    @Test
    public void testDeleteUserCertificate_notExists() {
        // 准备参数
        Integer id = randomInteger();

        // 调用, 并断言异常
        assertServiceException(() -> userCertificateService.deleteUserCertificate(id), USER_CERTIFICATE_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetUserCertificatePage() {
       // mock 数据
       UserCertificateDO dbUserCertificate = randomPojo(UserCertificateDO.class, o -> { // 等会查询到
           o.setUserId(null);
           o.setName(null);
           o.setOwnerName(null);
           o.setUsername(null);
           o.setPassword(null);
           o.setPassphrase(null);
           o.setPrivateKey(null);
           o.setType(null);
           o.setCreateTime(null);
       });
       userCertificateMapper.insert(dbUserCertificate);
       // 测试 userId 不匹配
       userCertificateMapper.insert(cloneIgnoreId(dbUserCertificate, o -> o.setUserId(null)));
       // 测试 name 不匹配
       userCertificateMapper.insert(cloneIgnoreId(dbUserCertificate, o -> o.setName(null)));
       // 测试 ownerName 不匹配
       userCertificateMapper.insert(cloneIgnoreId(dbUserCertificate, o -> o.setOwnerName(null)));
       // 测试 username 不匹配
       userCertificateMapper.insert(cloneIgnoreId(dbUserCertificate, o -> o.setUsername(null)));
       // 测试 password 不匹配
       userCertificateMapper.insert(cloneIgnoreId(dbUserCertificate, o -> o.setPassword(null)));
       // 测试 passphrase 不匹配
       userCertificateMapper.insert(cloneIgnoreId(dbUserCertificate, o -> o.setPassphrase(null)));
       // 测试 privateKey 不匹配
       userCertificateMapper.insert(cloneIgnoreId(dbUserCertificate, o -> o.setPrivateKey(null)));
       // 测试 type 不匹配
       userCertificateMapper.insert(cloneIgnoreId(dbUserCertificate, o -> o.setType(null)));
       // 测试 createTime 不匹配
       userCertificateMapper.insert(cloneIgnoreId(dbUserCertificate, o -> o.setCreateTime(null)));
       // 准备参数
       UserCertificatePageReqVO reqVO = new UserCertificatePageReqVO();
       reqVO.setUserId(null);
       reqVO.setName(null);
       reqVO.setOwnerName(null);
       reqVO.setUsername(null);
       reqVO.setPassword(null);
       reqVO.setPassphrase(null);
       reqVO.setPrivateKey(null);
       reqVO.setType(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<UserCertificateDO> pageResult = userCertificateService.getUserCertificatePage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbUserCertificate, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetUserCertificateList() {
       // mock 数据
       UserCertificateDO dbUserCertificate = randomPojo(UserCertificateDO.class, o -> { // 等会查询到
           o.setUserId(null);
           o.setName(null);
           o.setOwnerName(null);
           o.setUsername(null);
           o.setPassword(null);
           o.setPassphrase(null);
           o.setPrivateKey(null);
           o.setType(null);
           o.setCreateTime(null);
       });
       userCertificateMapper.insert(dbUserCertificate);
       // 测试 userId 不匹配
       userCertificateMapper.insert(cloneIgnoreId(dbUserCertificate, o -> o.setUserId(null)));
       // 测试 name 不匹配
       userCertificateMapper.insert(cloneIgnoreId(dbUserCertificate, o -> o.setName(null)));
       // 测试 ownerName 不匹配
       userCertificateMapper.insert(cloneIgnoreId(dbUserCertificate, o -> o.setOwnerName(null)));
       // 测试 username 不匹配
       userCertificateMapper.insert(cloneIgnoreId(dbUserCertificate, o -> o.setUsername(null)));
       // 测试 password 不匹配
       userCertificateMapper.insert(cloneIgnoreId(dbUserCertificate, o -> o.setPassword(null)));
       // 测试 passphrase 不匹配
       userCertificateMapper.insert(cloneIgnoreId(dbUserCertificate, o -> o.setPassphrase(null)));
       // 测试 privateKey 不匹配
       userCertificateMapper.insert(cloneIgnoreId(dbUserCertificate, o -> o.setPrivateKey(null)));
       // 测试 type 不匹配
       userCertificateMapper.insert(cloneIgnoreId(dbUserCertificate, o -> o.setType(null)));
       // 测试 createTime 不匹配
       userCertificateMapper.insert(cloneIgnoreId(dbUserCertificate, o -> o.setCreateTime(null)));
       // 准备参数
       UserCertificateExportReqVO reqVO = new UserCertificateExportReqVO();
       reqVO.setUserId(null);
       reqVO.setName(null);
       reqVO.setOwnerName(null);
       reqVO.setUsername(null);
       reqVO.setPassword(null);
       reqVO.setPassphrase(null);
       reqVO.setPrivateKey(null);
       reqVO.setType(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<UserCertificateDO> list = userCertificateService.getUserCertificateList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbUserCertificate, list.get(0));
    }

}
