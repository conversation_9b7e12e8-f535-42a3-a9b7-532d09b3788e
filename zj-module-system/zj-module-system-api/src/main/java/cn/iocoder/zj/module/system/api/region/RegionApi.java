package cn.iocoder.zj.module.system.api.region;


import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.system.api.region.dto.RegionDTO;
import cn.iocoder.zj.module.system.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 地区")
public interface RegionApi {

    String PREFIX = ApiConstants.PREFIX + "/region";

    @GetMapping(PREFIX + "/get")
    @Operation(summary = "获得地区详情")
    @Parameter(name = "id", description = "地区编号", example = "1000000", required = true)
    CommonResult<RegionDTO> getRegion(@RequestParam("id") String id);


}
