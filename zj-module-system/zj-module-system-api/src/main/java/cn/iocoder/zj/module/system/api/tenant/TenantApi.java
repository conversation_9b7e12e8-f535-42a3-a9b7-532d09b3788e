package cn.iocoder.zj.module.system.api.tenant;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.system.api.tenant.dto.TenantRespDTO;
import cn.iocoder.zj.module.system.api.tenant.dto.TenantUserDTO;
import cn.iocoder.zj.module.system.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Tag(name = "RPC 服务 - 多租户")
public interface TenantApi {

    String PREFIX = ApiConstants.PREFIX + "/tenant";

    @GetMapping(PREFIX + "/id-list")
    @Operation(summary = "获得所有租户编号")
    CommonResult<List<Long>> getTenantIdList();

    @GetMapping(PREFIX + "/valid")
    @Operation(summary = "校验租户是否合法")
    @Parameter(name = "id", description = "租户编号", required = true, example = "1024")
    CommonResult<Boolean> validTenant(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/getInfo")
    @Operation(summary = "获取租户精简信息")
    @Parameter(name = "id", description = "租户编号", required = true, example = "1024")
    CommonResult<TenantRespDTO> getTenantSimpleInfo(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/getRegionInfo")
    @Operation(summary = "获取地区信息")
    @Parameter(name = "id", description = "地区id", required = true, example = "340000")
    CommonResult<TenantRespDTO> getRegionInfo(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/getParentRegionInfo")
    @Operation(summary = "获取省份地区信息")
    @Parameter(name = "regionIds", description = "地区id", required = true)
    @Parameter(name = "type", description = "地区类型", required = true)
    CommonResult<List<String>> getParentRegionInfo(@RequestParam("regionIds") List<Long> regionIds, @RequestParam("type") Integer type);

    @GetMapping(PREFIX + "/getChildrenRegionInfo")
    @Operation(summary = "获取省份地区信息")
    @Parameter(name = "regionId", description = "地区id", required = true)
    CommonResult<List<String>> getChildrenRegionInfo(@RequestParam("regionIds") Long regionId);

    @GetMapping(PREFIX + "/getTenantAllIds")
    @Operation(summary = "获得所有租户id和姓名")
    CommonResult<List<TenantUserDTO>> getTenantAllIds();

    @PostMapping(PREFIX + "/getTenantIdsByPlatform")
    @Operation(summary = "获得租户对应平台下的所有租户id")
    CommonResult<List<Long>> getTenantIdsByPlatform(@RequestParam("tenantId") Long tenantId);

    @PostMapping(PREFIX + "/getStateByTenantId")
    @Operation(summary = "获得租户对应平台下的所有租户id")
    CommonResult<Long> getStateByTenantId(@RequestParam("tenantId") Long tenantId);
}