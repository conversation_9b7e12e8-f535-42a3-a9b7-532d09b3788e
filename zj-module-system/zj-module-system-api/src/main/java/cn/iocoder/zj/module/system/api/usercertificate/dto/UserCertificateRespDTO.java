package cn.iocoder.zj.module.system.api.usercertificate.dto;

import cn.iocoder.zj.module.system.enums.ApiConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * @ClassName : UserCertificateRespDTO  //类名
 * @Description : 密码返回数据  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/4/1  15:53
 */
@Schema(description = "RPC 服务 - 密码管理返回数据 Resquest DTO")
@Data
public class UserCertificateRespDTO {

    @Schema(description = "id", example = "admin")
    private String id;
    @Schema(description = "授权账户", example = "admin")
    private String username;
    @Schema(description = "密码", example = "admin")
    private String password;
    @Schema(description = "私钥", example = "admin")
    private String privateKey;
    @Schema(description = "账户类型", example = "admin")
    private String type;
    @Schema(description = "私钥密码", example = "admin")
    private String passphrase;
}
