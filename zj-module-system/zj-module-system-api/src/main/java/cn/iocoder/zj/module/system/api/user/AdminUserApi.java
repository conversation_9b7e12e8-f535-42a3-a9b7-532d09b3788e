package cn.iocoder.zj.module.system.api.user;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.util.collection.CollectionUtils;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.zj.module.system.enums.ApiConstants;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Tag(name = "RPC 服务 - 管理员用户")
public interface AdminUserApi {

    String PREFIX = ApiConstants.PREFIX + "/user";

    @GetMapping(PREFIX + "/get")
    @Operation(summary = "通过用户 ID 查询用户")
    @Parameter(name = "id", description = "用户编号", example = "1", required = true)
    CommonResult<AdminUserRespDTO> getUser(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/getId")
    @Operation(summary = "通过用户 ID 查询用户")
    @Parameter(name = "id", description = "用户编号", example = "1", required = true)
    CommonResult<AdminUserRespDTO> getUserById(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/list")
    @Operation(summary = "通过用户 ID 查询用户们")
    @Parameter(name = "ids", description = "部门编号数组", example = "1,2", required = true)
    CommonResult<List<AdminUserRespDTO>> getUserList(@RequestParam("ids") Collection<Long> ids);

    @GetMapping(PREFIX + "/list-by-dept-id")
    @Operation(summary = "获得指定部门的用户数组")
    @Parameter(name = "deptIds", description = "部门编号数组", example = "1,2", required = true)
    CommonResult<List<AdminUserRespDTO>> getUserListByDeptIds(@RequestParam("deptIds") Collection<Long> deptIds);

    @GetMapping(PREFIX + "/list-by-post-id")
    @Operation(summary = "获得指定岗位的用户数组")
    @Parameter(name = "postIds", description = "岗位编号数组", example = "2,3", required = true)
    CommonResult<List<AdminUserRespDTO>> getUserListByPostIds(@RequestParam("postIds") Collection<Long> postIds);

    /**
     * 获得用户 Map
     *
     * @param ids 用户编号数组
     * @return 用户 Map
     */
    @PostMapping(PREFIX + "/getUserMap")
    @Operation(summary = "获取用户")
    @Parameter(name = "ids", description = "用户id", example = "2,3", required = true)
    CommonResult<Map<Long, AdminUserRespDTO>> getUserMap(@RequestParam("ids")Collection<Long> ids);

    @GetMapping(PREFIX + "/valid")
    @Operation(summary = "校验用户们是否有效")
    @Parameter(name = "ids", description = "用户编号数组", example = "3,5", required = true)
    CommonResult<Boolean> validateUserList(@RequestParam("ids") Set<Long> ids);

    @GetMapping(PREFIX + "/getUserIdsByTenantId")
    @Operation(summary = "根据用户id获取用户")
    @Parameter(name = "userId", description = "用户ID", example = "3", required = true)
    CommonResult<List<Long>> getUserIdsByTenantId(@RequestParam("ids") Long userId);
    @GetMapping(PREFIX + "/getHomologousUsers")
    @Operation(summary = "获取租户下的所有用户")
    @Parameter(name = "userId", description = "用户ID", example = "3", required = true)
    CommonResult<List<Long>> getHomologousUsers(@RequestParam("ids") Long userId);

    @GetMapping(PREFIX + "/getUserMenus")
    @Operation(summary = "获取用户菜单")
    @Parameter(name = "userId", description = "用户ID", example = "3", required = true)
    CommonResult<List<String>> getUserMenus(@RequestParam("ids") Long userId);

    @PutMapping(PREFIX + "/updateUserEmail")
    @Operation(summary = "随订阅信息更新用户邮箱")
    @Parameter(name = "email", description = "用户邮箱", example = "<EMAIL>", required = true)
    CommonResult<Boolean> updateUserEmail(@Valid @RequestParam("email")String email);

    @GetMapping(PREFIX + "/validateEmail")
    @Operation(summary = "验证用户邮箱")
    CommonResult<Boolean> validateEmail(@RequestParam("email") String email);

    @GetMapping(PREFIX + "/getUserListByNickname")
    @Operation(summary = "获得用户列表，基于昵称模糊匹配")
    @Parameter(name = "name", description = "用户名称", example = "管理员", required = true)
    CommonResult<List<AdminUserRespDTO>> getUserListByNickname(@RequestParam("name")String name);
}
