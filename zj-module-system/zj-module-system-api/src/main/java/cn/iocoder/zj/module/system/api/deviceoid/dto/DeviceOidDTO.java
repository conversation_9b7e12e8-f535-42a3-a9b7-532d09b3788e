package cn.iocoder.zj.module.system.api.deviceoid.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName : DeviceOidDTO  //类名
 * @Description : OID 实体类  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/8/14  16:39
 */
@Schema(description = "RPC 服务 - OID数据 Response DTO")
@Data
public class DeviceOidDTO {
    @Schema(description = "设备类型key")
    private String deviceType;

    @Schema(description = "设备类型名称")
    private String deviceName;

    @Schema(description = "系统类型名称")
    private String sysName;

    @Schema(description = "系统类型key")
    private String sysType;

    /**
     * cpu使用率oid
     */
    @Schema(description = "cpu使用率oid")
    private String cpuUse;

    /**
     * 内存总容量oid
     */
    @Schema(description = "内存总容量oid")
    private String memoryAll;
    /**
     * 内存使用量oid
     */
    @Schema(description = "内存使用量oid")
    private String memoryUse;
    /**
     * 磁盘总容量oid
     */
    @Schema(description = "磁盘总容量oid")
    private String diskAll;
    /**
     * 磁盘使用量oid
     */
    @Schema(description = "磁盘使用量oid")
    private String diskUse;
}
