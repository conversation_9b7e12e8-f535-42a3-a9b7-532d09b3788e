package cn.iocoder.zj.module.system.api.platformconfig.dto;

import lombok.Data;

/**
 * @ClassName : PlatformconfigDTO  //类名
 * @Description : 平台配置类  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/31  15:49
 */
@Data
public class PlatformconfigDTO {

    private Long id;

    private String username;


    private String password;


    private String url;

    private String typeName;

    private String typeCode;

    private String name;

    private Long tenantId;


    private String regionName;

    private Long regionId;

    private String address;

    private String urlHttp;

    private Integer consoleProd;

    private String consoleIp;

    private Integer akType;
}
