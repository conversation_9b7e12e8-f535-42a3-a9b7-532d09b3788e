package cn.iocoder.zj.module.system.api.deviceoid;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.system.api.deviceoid.dto.DeviceOidDTO;
import cn.iocoder.zj.module.system.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME)
@Tag(name =  "RPC 服务 - OID数据")
public interface DeviceOidApi {

    String PREFIX = ApiConstants.PREFIX + "/oid";


    @GetMapping(PREFIX + "/list")
    @Operation(summary = "OId列表")
    List<DeviceOidDTO> getDeviceOidList(@RequestParam("deviceType") String deviceType);
}
