package cn.iocoder.zj.module.system.api.dingtalk;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.system.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

@FeignClient(name = ApiConstants.NAME)
@Tag(name =  "RPC 服务 - 钉钉消息推送")
public interface DingTalkSendApi {

    String PREFIX = ApiConstants.PREFIX + "/dingtalk/send";

    @PostMapping(PREFIX + "/sendSingleDingTalkToMember")
    @Operation(summary = "钉钉推送消息给 Member 用户")
    @Parameter(name = "templateParams", description = "json", required = true, example = "tudou")
    CommonResult<Boolean> sendSingleDingTalkToMember(@RequestBody Map<String,Object> templateParams);

    @PostMapping(PREFIX + "/sendSingleWecomToMember")
    @Operation(summary = "企微推送消息给 Member 用户")
    @Parameter(name = "templateParams", description = "json", required = true, example = "tudou")
    CommonResult<Boolean> sendSingleWeComToMember(@RequestBody Map<String,Object> templateParams);
}
