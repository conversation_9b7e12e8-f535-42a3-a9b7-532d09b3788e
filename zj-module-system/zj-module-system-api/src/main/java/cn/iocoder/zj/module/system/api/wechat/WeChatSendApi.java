package cn.iocoder.zj.module.system.api.wechat;

import cn.iocoder.zj.framework.common.pojo.CommonResult;


import cn.iocoder.zj.module.system.api.wechat.dto.WechatBindingDTO;
import cn.iocoder.zj.module.system.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Tag(name =  "RPC 服务 - 公众号消息推送")
public interface WeChatSendApi {

    String PREFIX = ApiConstants.PREFIX + "/wechat/send";

    @PostMapping(PREFIX + "/sendSingleWeChatToMember")
    @Operation(summary = "公众号推送消息给 Member 用户")
    @Parameter(name = "templateParams", description = "json", required = true, example = "tudou")
    CommonResult<Boolean> sendSingleWeChatToMember(@RequestBody Map<String,Object> templateParams);



    @PostMapping(PREFIX + "/sendSingleWeChatToTenant")
    @Operation(summary = "公众号推送消息给指定租客")
    @Parameter(name = "templateParams", description = "json", required = true, example = "tudou")
    void sendSingleWeChatToTenant(@RequestParam("templateParams") String templateParams);

    @PostMapping(PREFIX + "/getWeChatBinding")
    @Operation(summary = "获取绑定关系")
    @Parameter(name = "templateParams", description = "json", required = true, example = "tudou")
    CommonResult<WechatBindingDTO> getWeChatBinding(@RequestParam("templateParams") String templateParams);

    @PostMapping(PREFIX + "/sendSingleWeChatToAuthorization")
    @Operation(summary = "公众号推送授权")
    @Parameter(name = "templateParams", description = "json", required = true, example = "tudou")
    CommonResult<Boolean> sendSingleWeChatToAuthorization(@RequestBody Map<String, Object> templateParams);

    @PutMapping(PREFIX + "/sendSampleMessage")
    @Operation(summary = "公众号推送简单消息")
    CommonResult<Boolean> sendMessage(@RequestParam("accessToken")String accessToken,
                                      @RequestParam("openId")String openId,
                                      @RequestParam("message")String message);

    @GetMapping(PREFIX + "/getWechatAccessToken")
    @Operation(summary = "获取微信token")
    CommonResult<String> getWechatAccessToken();

    @PostMapping(PREFIX + "/sendSingleWeChatToAuthorizationType")
    @Operation(summary = "公众号推送授权申请状态")
    @Parameter(name = "templateParams", description = "json", required = true, example = "tudou")
    CommonResult<Boolean> sendSingleWeChatToAuthorizationType(@RequestBody Map<String, Object> templateParams);

    @PostMapping(PREFIX + "/sendBpmMessage")
    @Operation(summary = "公众号推送消息给 Member 用户")
    @Parameter(name = "templateParams", description = "json", required = true, example = "tudou")
    CommonResult<Boolean> sendBpmMessage(@RequestBody Map<String,Object> templateParams);
}
