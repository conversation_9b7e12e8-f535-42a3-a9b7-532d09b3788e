package cn.iocoder.zj.module.system.api.licence;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.system.api.logger.dto.LoginLogCreateReqDTO;
import cn.iocoder.zj.module.system.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.Map;

/**
 * @ClassName : LicenceApi  //类名
 * @Description : 授权api  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/2/1  9:39
 */
@FeignClient(name = ApiConstants.NAME) //
@Tag(name =  "RPC 服务 - 查询授权")
public interface LicenceApi {
    String PREFIX = ApiConstants.PREFIX + "/licence";

    @PostMapping(PREFIX + "/selectLicence")
    @Operation(summary = "根据租户id查询授权数")
    CommonResult<Map> selectLicence(@Valid  @RequestParam("tenantId") Long tenantId);

}
