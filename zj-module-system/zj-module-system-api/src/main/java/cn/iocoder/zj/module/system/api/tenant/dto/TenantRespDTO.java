package cn.iocoder.zj.module.system.api.tenant.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "RPC 服务 - 租户精简信息 Response DTO")
@Data
public class TenantRespDTO {

    @Schema(description = "id", example = "1")
    private Long id;
    @Schema(description = "租户名", example = "安徽中杰")
    private String name;
    @Schema(description = "地区ID", example = "340100")
    private Integer regionId;
    @Schema(description = "父地区ID", example = "340000")
    private Integer parentRegionId;
    @Schema(description = "地区名称", example = "合肥市")
    private String regionName;
    @Schema(description = "父地区名称", example = "安徽省")
    private String parentRegionName;
    @Schema(description = "平台json", example = "[{\"platformId\":2,\"platformName\":\"安徽中杰\"}]")
    private String platformJson;
    @Schema(description = "运维人员ID", required = true, example = "12")
    private Long maintainerId;
    @Schema(description = "运维人员名称", required = true, example = "12")
    private String maintainerName;
}