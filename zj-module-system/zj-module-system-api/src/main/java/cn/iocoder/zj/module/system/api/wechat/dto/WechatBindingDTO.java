package cn.iocoder.zj.module.system.api.wechat.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotNull;
import java.util.Map;

@Schema(description = "RPC 服务 - 微信公众号消息发送给 Admin 或者 Member 用户 Request DTO")
@Data
public class WechatBindingDTO {
    @Schema(description = "id", example = "1")
    private Long id;
    @Schema(description = "租户编号", example = "1")
    private Long tenant;
    @Schema(description = "微信公众号唯一标识", example = "ascdvfbv")
    private String openId;
    @Schema(description = "邮箱地址", example = "<EMAIL>")
    private String email;
    @Schema(description = "是否启用微信", example = "合肥市")
    private Boolean isWechat;
    @Schema(description = "是否启用邮箱", example = "安徽省")
    private Boolean isEmail;

}
