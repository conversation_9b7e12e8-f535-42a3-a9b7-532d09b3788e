package cn.iocoder.zj.module.system.api.platformconfig;


import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import cn.iocoder.zj.module.system.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;
import java.util.Map;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 平台配置")
public interface PlatformconfigApi {


    String PREFIX = ApiConstants.PREFIX + "/plat";

    @GetMapping(PREFIX + "/list")
    @Operation(summary = "平台配置集合")
    CommonResult<List<PlatformconfigDTO>> getPlatList();

    @GetMapping(PREFIX + "/createTimeList")
    @Operation(summary = "平台创建时间集合")
    CommonResult<Map<Long, Date>> getPlatCreateTimeList();

    @PostMapping(PREFIX + "/getPlatConfig")
    @Operation(summary = "获取平台配置信息")
    CommonResult<PlatformconfigDTO> getPlatConfig(@RequestBody PlatformconfigDTO req);

    @GetMapping(PREFIX + "/getPlatformSelectList")
    @Operation(summary = "获取租户的平台配置信息")
    CommonResult<List<Map>> getPlatformSelectList(@RequestParam("tenantId") String tenantId);

    @GetMapping(PREFIX + "/getPlatformByTenantId")
    @Operation(summary = "根据租户Id获取平台配置信息")
    CommonResult<List<Map>> getPlatformByTenantId(@RequestParam("tenantId") String tenantId);

    @GetMapping(PREFIX + "/getPlatformSelectListByUserId")
    @Operation(summary = "根据用户id获取租户的平台配置信息")
    CommonResult<List<Map>> getPlatformSelectListByUserId(@RequestParam("userId") Long userId);


    @GetMapping(PREFIX + "/getByConfigId")
    @Operation(summary = "按平台ID获取配置信息")
    CommonResult<PlatformconfigDTO> getByConfigId(@RequestParam("platformId") Long platformId);


    @GetMapping(PREFIX + "/getByConfigMailList")
    @Operation(summary = "按平台ID获取租户邮箱")
    CommonResult<List<String>> getByConfigMailList(@RequestParam("platformId") Long platformId);

    @GetMapping(PREFIX + "/getByTenantList")
    @Operation(summary = "根据平台id获取租户信息")
    CommonResult<List<String>> getByTenantList(@RequestParam("platformId") Long platformId);

    @GetMapping(PREFIX + "/getCloudPlatform")
    @Operation(summary = "云平台分布")
    List<Map> cloudPlatform();

    @GetMapping(PREFIX + "/getPlatformListByUserId")
    @Operation(summary = "获取用户的平台配置信息")
    CommonResult<List<Map>> getPlatformListByUserId(@RequestParam("userId") String userId);

    @GetMapping(PREFIX + "/getTenantsByConfigId")
    @Operation(summary = "获取绑定该平台的租户")
    CommonResult<List<Long>> getTenantsByConfigId(@RequestParam("platformId") Long platformId);

    @GetMapping(PREFIX + "/getPlatformByIds")
    @Operation(summary = "根据平台id获取平台配置信息")
    CommonResult<List<PlatformconfigDTO>> getPlatformByIds(@RequestParam("platformIds") List<Long> platformIds);

}
