package cn.iocoder.zj.module.system.enums.permission;

import cn.iocoder.zj.framework.common.util.object.ObjectUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 角色标识枚举
 */
@Getter
@AllArgsConstructor
public enum RoleCodeEnum {

    SUPER_ADMIN("super_admin", "超级管理员"),
    TENANT_ADMIN("tenant_admin", "租户管理员"),
    OM_MANAGER("om_manager","运维管理"),
    OPERATION_MAINTENANCE("operation_maintenance","运维人员"),
    PLATFORM_ADMIN("cs_admin", "平台管理员")
    ;

    /**
     * 角色编码
     */
    private final String code;
    /**
     * 名字
     */
    private final String name;

    public static boolean isSuperAdmin(String code) {
        return ObjectUtils.equalsAny(code, SUPER_ADMIN.getCode());
    }
    public static boolean isTenantAdmin(String code) {
        return ObjectUtils.equalsAny(code, TENANT_ADMIN.getCode());
    }

    public static boolean isPlatformAdmin(String code) {
        return ObjectUtils.equalsAny(code, PLATFORM_ADMIN.getCode());
    }

    public static boolean isMaintenance(String code) {
        return ObjectUtils.equalsAny(code, OPERATION_MAINTENANCE.getCode());
    }


    public static boolean isOMManager(String code) {
        return ObjectUtils.equalsAny(code, OM_MANAGER.getCode());
    }
}
