package cn.iocoder.zj.module.system.api.region.dto;

import lombok.Data;

/**
 * @ClassName : RegionDTO  //类名
 * @Description : 地区  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/22  17:35
 */
@Data
public class RegionDTO {

    /**
     * 地区主键编号
     */
    private String id;
    /**
     * 地区名称
     */
    private String regionName;
    /**
     * 地区缩写
     */
    private String regionShortName;
    /**
     * 地区父id
     */
    private String regionParentId;
    /**
     * 地区级别 1-省、自治区、直辖市 2-地级市、地区、自治州、盟 3-市辖区、县级市、县
     */
    private Integer regionLevel;

}
