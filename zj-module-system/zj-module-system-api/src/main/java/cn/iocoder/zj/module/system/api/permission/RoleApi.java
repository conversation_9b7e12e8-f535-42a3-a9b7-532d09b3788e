package cn.iocoder.zj.module.system.api.permission;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.system.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.Set;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Tag(name = "RPC 服务 - 角色")
public interface RoleApi {

    String PREFIX = ApiConstants.PREFIX + "/role";

    @GetMapping(PREFIX + "/valid")
    @Operation(summary = "校验角色是否合法")
    @Parameter(name = "ids", description = "角色编号数组", example = "1,2", required = true)
    CommonResult<Boolean> validRoleList(@RequestParam("ids") Collection<Long> ids);

    @GetMapping(PREFIX + "/hasAnySuperAdmin")
    @Operation(summary = "校验角色是否合法")
    @Parameter(name = "ids", description = "角色编号数组", example = "1,2", required = true)
    Boolean hasAnySuperAdmin(@RequestParam("ids") Set<Long> ids);
    @GetMapping(PREFIX + "/hasAnyTenantAdmin")
    @Operation(summary = "校验角色是否有租户管理员")
    @Parameter(name = "ids", description = "角色编号数组", example = "1,2", required = true)
    Boolean hasAnyTenantAdmin(@RequestParam("ids") Set<Long> ids);
    @PostMapping(PREFIX + "/getRoleIdByCode")
    @Operation(summary = "按code获取角色ID")
    @Parameter(name = "code", description = "角色code", example = "om_manager", required = true)
    CommonResult<Set<Long>> getRoleIdByCode(@RequestParam("code")String code);

    @GetMapping(PREFIX + "/hasAnyPlatformAdmin")
    @Operation(summary = "校验角色是否有平台管理员")
    @Parameter(name = "ids", description = "角色编号数组", example = "1,2", required = true)
    Boolean hasAnyPlatformAdmin(@RequestParam("ids") Set<Long> ids);

    @GetMapping(PREFIX + "/hasAnyPlatformMaintenance")
    @Operation(summary = "校验角色是否有运营人员角色")
    @Parameter(name = "ids", description = "角色编号数组", example = "1,2", required = true)
    Boolean hasAnyPlatformMaintenance(@RequestParam("ids") Set<Long> ids);

    @GetMapping(PREFIX + "/hasAnyOMManager")
    @Operation(summary = "校验角色是否有运维管理的角色")
    @Parameter(name = "ids", description = "角色编号数组", example = "1,2", required = true)
    Boolean hasAnyOMManager(@RequestParam("ids") Set<Long> ids);


    @PostMapping(PREFIX + "/hasAnySuperAdmin")
    @Operation(summary = "是否有超管角色")
    @Parameter(name = "ids", description = "角色编号数组", example = "1,2", required = true)
    Boolean decideAnySuperAdmin(@RequestParam("ids") Set<Long> ids);

    @PostMapping(PREFIX + "/getIsRootOperation")
    @Operation(summary = "查询用户租户是否为管理员租户")
    @Parameter(name = "id", description = "用户id", example = "2", required = true)
    Boolean getIsRootOperation(@RequestParam("id")Long id);

    @PostMapping(PREFIX + "/getUserIdByCode")
    @Operation(summary = "按code获取userID")
    @Parameter(name = "code", description = "角色code", example = "om_manager", required = true)
    CommonResult<Set<Long>> getUserIdByCode(@RequestParam("code")String code);
}