package cn.iocoder.zj.module.system.api.user.dto;

import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import lombok.Data;

import java.util.Set;

/**
 * Admin 用户 Response DTO
 *
 * <AUTHOR>
 */
@Data
public class AdminUserRespDTO {

    /**
     * 用户ID
     */
    private Long id;
    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 帐号状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;

    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 岗位编号数组
     */
    private Set<Long> postIds;
    /**
     * 手机号码
     */
    private String mobile;

    private Long tenantId;

    private String tenantName;

    /**
     * 运维人员服务的租户对象(以逗号拼接的租户ID)
     */
    private String serviceTenantId;

    private Integer wxCheckState;

    private String email;
}
