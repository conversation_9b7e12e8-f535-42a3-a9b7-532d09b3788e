package cn.iocoder.zj.module.system.api.usercertificate;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.system.api.usercertificate.dto.UserCertificateRespDTO;
import cn.iocoder.zj.module.system.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Tag(name = "RPC 服务 - 密码管理")
public interface UsercertificateApi {
    String PREFIX = ApiConstants.PREFIX + "/certificate";


    @GetMapping(PREFIX + "/get")
    @Operation(summary = "通过密码 ID 查询密码类型")
    @Parameter(name = "id", description = "密码id", example = "1", required = true)
    CommonResult<UserCertificateRespDTO> getUser(@RequestParam("id") String id);

}
