#!/bin/sh

# 复制项目的文件到对应docker路径，便于一键生成镜像。
usage() {
	echo "Usage: sh copy.sh"
	exit 1
}

copy_if_exists() {
    source_file=$1
    destination_dir=$2

    if [ -e $source_file ]; then
        echo "Copying $source_file to $destination_dir"
        cp $source_file $destination_dir
    else
        echo "File $source_file not found. Skipping."
    fi
}

# copy jar
echo "begin copy zj-gateway "
copy_if_exists "../zj-gateway/target/zj-gateway.jar" "./zj/gateway/jar"

echo "begin copy zj-module-system-biz "
copy_if_exists "../zj-module-system/zj-module-system-biz/target/zj-module-system-biz.jar" "./zj/gateway/jar"

echo "begin copy zj-module-infra-biz "
copy_if_exists "../zj-module-infra/zj-module-infra-biz/target/zj-module-infra-biz.jar" "./zj/gateway/jar"

echo "begin copy zj-module-bpm-biz "
copy_if_exists "../zj-module-bpm/zj-module-bpm-biz/target/zj-module-bpm-biz.jar" "./zj/gateway/jar"

echo "begin copy zj-module-report-biz "
copy_if_exists "../zj-module-report/zj-module-report-biz/target/zj-module-report-biz.jar" "./zj/gateway/jar"

echo "begin copy zj-module-monitor-biz "
copy_if_exists "../zj-module-monitor/zj-module-monitor-biz/target/zj-module-monitor-biz.jar" "./zj/gateway/jar"

echo "begin copy zj-module-om-biz "
copy_if_exists "../zj-module-om/zj-module-om-biz/target/zj-module-om-biz.jar" "./zj/gateway/jar"

echo "begin copy zj-module-collection-biz "
copy_if_exists "../zj-module-collection/zj-module-collection-biz/target/zj-module-collection-biz.jar" "./zj/gateway/jar"

echo "begin copy zj-module-asset "
copy_if_exists "../zj-module-asset/target/zj-module-asset.jar" "./zj/gateway/jar"

echo "begin copy zj-module-vmware-biz "
copy_if_exists "../zj-module-vmware/zj-module-vmware-biz/target/zj-module-vmware-biz.jar" "./zj/gateway/jar"

echo "begin copy zj-module-customer-biz "
copy_if_exists "../zj-module-customer/zj-module-customer-biz/target/zj-module-customer-biz.jar" "./zj/gateway/jar"

echo "begin copy zj-module-herzbeat-biz "
copy_if_exists "../zj-module-herzbeat/zj-module-herzbeat-biz/target/zj-module-herzbeat-biz.jar" "./zj/gateway/jar"

echo "begin copy zj-module-cloudedge-biz "
copy_if_exists "../zj-module-cloudedge/zj-module-cloudedge-biz/target/zj-module-cloudedge-biz.jar" "./zj/gateway/jar"

echo "begin copy zj-module-proxy-biz "
copy_if_exists "../zj-module-proxy/zj-module-proxy-biz/target/zj-module-proxy-biz.jar" "./zj/gateway/jar"

echo "begin copy zj-module-guacamole-biz "
copy_if_exists "../zj-module-guacamole/zj-module-guacamole-biz/target/zj-module-guacamole-biz.jar" "./zj/gateway/jar"


echo "begin copy hertzbeat-collector-collector "
copy_if_exists "../zj-framework/zj-spring-boot-starter-hertzbeat-collector/hertzbeat-collector-collector/target/hertzbeat-collector-collector.jar" "./zj/gateway/jar"

echo "begin copy zj-module-server-collector "
copy_if_exists "../zj-module-server-collector/zj-module-server-collector-biz/target/zj-module-server-collector-biz.jar" "./zj/gateway/jar"

