apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: zj-module-server-collection
  name: zj-module-server-collection
  namespace: zj-server   #一定要写名称空间
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  selector:
    matchLabels:
      app: zj-module-server-collection
  strategy:
    type: RollingUpdate # Recreate：在创建新Pods之前，所有现有的Pods会被杀死 RollingUpdate：滚动升级，逐步替换的策略，同时滚动升级时，支持更多的附加参数
    rollingUpdate:
      maxSurge: 50%  #maxSurge：1 表示滚动升级时会先启动1个pod
      maxUnavailable: 50% #maxUnavailable：1 表示滚动升级时允许的最大Unavailable的pod个数，也可以填写比例，maxUnavailable=50%
  template:
    metadata:
      labels:
        app: zj-module-server-collection
    spec:
      containers:
        - image: harbor.kube.com/zj-server-cloud/zj-module-server-collector
          #         readinessProbe:
          #           httpGet:
          #             path: /actuator/health
          #             port: 8080
          #           timeoutSeconds: 10
          #           failureThreshold: 30
          #           periodSeconds: 5
          imagePullPolicy: Always
          name: zj-module-server-collection
          ports:
            - name: http
              containerPort: 48090
              protocol: TCP
            - name: cluster
              containerPort: 9098
              protocol: TCP
          resources:
            limits:
              cpu: 2
              memory: 2048Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: zj-module-server-collection
  name: zj-module-server-collection
  namespace: zj-server
spec:
  ports:
    - name: http
      port: 48090
      protocol: TCP
      nodePort: 48090
    - name: cluster
      port: 9098
      protocol: TCP
      targetPort: 9098
      nodePort: 19098
  #      使用NodePort方式暴露端口  固定端口
  #      nodePort: 30887
  selector:
    app: zj-module-server-collection
  sessionAffinity: None
  type: NodePort