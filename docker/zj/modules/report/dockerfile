# 基础镜像
FROM harbor.zjiecn.com/cache/library/openjdk:17-slim-buster
# author
MAINTAINER zjie

EXPOSE 48084
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' >/etc/timezone
RUN apt-get update && apt-get install -y \
    fontconfig \
    fonts-dejavu \
    && rm -rf /var/lib/apt/lists/*

# 复制jar文件到路径
ADD docker/zj/gateway/jar/zj-module-report-biz.jar ./
COPY docker/zj/agent /usr/local/agent

# 启动大屏服务
ENTRYPOINT [ "sh", "-c", "java -jar zj-module-report-biz.jar"]