<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Document</title>
</head>
<style>
  *{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  .content {
    width: 100%;
    height: 100vh;
    padding: 10px;
    font-size: 14px;

  }
  .list {
    width: 100%;
    background-color: #ffff;
    border-radius: 10px;
  }
  .title {
    width: 100%;
    height: 50px;
    background: linear-gradient(to right, #fff1f1 0%, #fff 100%);
    display: flex;
    align-items: center;
    padding-left: 20px;
    font-weight: 600;
  }
  .name{
    width: 80px;
  }
  .text{
    width: 300px;
    /* margin-left: 20px; */
    overflow-x: auto;
  }
  .item{
    width: 100%;
    padding: 10px 0;
    display: flex;
    justify-content: flex-start;
    padding-left: 20px;
    padding-right: 20px;
  }
</style>
<body>
<div class="content">

  <div class="list">
    <div class="title">
      <div class="name">资源名称：</div>
      <div class="text" id="monitorName"></div>
    </div>
    <div class="item">
      <span class="name">资源类型</span>
      <span class="text" id="app"></span>
    </div>
    <div class="item">
      <span class="name">告警时间</span>
      <span class="text" id = "lastAlarmTime"></span>
    </div>
    <div class="item">
      <span class="name">告警级别</span>
      <span class="text" id="priority"></span>
    </div>
    <div class="item">
      <span class="name">触发次数</span>
      <span class="text" id="alarmTimes"></span>
    </div>
    <div class="item">
      <span class="name" >机架位置</span>
      <span class="text" id="address"></span>
    </div>
    <div class="item">
      <span class="name">具体消息</span>
      <span class="text" id="content"></span>
    </div>
    <div class="item">
      <span class="name">平台名称</span>
      <span class="text" id="platformName"></span>
    </div>
  </div>
</div>
</body>
</html>







































































































































































































