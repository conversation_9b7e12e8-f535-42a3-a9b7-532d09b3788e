apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: zj-modules-om
  name: zj-modules-om
  namespace: zj-cloud   #一定要写名称空间
spec:
  replicas: 3
  strategy:
    type: RollingUpdate # Recreate：在创建新Pods之前，所有现有的Pods会被杀死 RollingUpdate：滚动升级，逐步替换的策略，同时滚动升级时，支持更多的附加参数
    rollingUpdate:
      maxSurge: 1  #maxSurge：1 表示滚动升级时会先启动1个pod
      maxUnavailable: 1 #maxUnavailable：1 表示滚动升级时允许的最大Unavailable的pod个数，也可以填写比例，maxUnavailable=50%
  selector:
    matchLabels:
      app: zj-modules-om
  template:
    metadata:
      labels:
        app: zj-modules-om
    spec:
      containers:
        - image: harbor.kube.com/zj-cloud/zj-modules-om
          #         readinessProbe:
          #           httpGet:
          #             path: /actuator/health
          #             port: 8080
          #           timeoutSeconds: 10
          #           failureThreshold: 30
          #           periodSeconds: 5
          imagePullPolicy: Always
          name: zj-modules-om
          ports:
            - containerPort: 48086
              protocol: TCP
          resources:
            limits:
              cpu: 700m
              memory: 800Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: zj-modules-om
  name: zj-modules-om
  namespace: zj-cloud
spec:
  ports:
    - name: http
      port: 48086
      protocol: TCP
      targetPort: 48086
      nodePort: 30996
  #      使用NodePort方式暴露端口  固定端口
  #      nodePort: 30887
  selector:
    app: zj-modules-om
  sessionAffinity: None
  type: NodePort