# 基础镜像
FROM harbor.zjiecn.com/cache/library/openjdk:17-slim-buster
# author
MAINTAINER zj

RUN sed -i s@/deb.debian.org/@/mirrors.163.com/@g /etc/apt/sources.list

RUN sed -i s@/security.debian.org/@/mirrors.163.com/@g /etc/apt/sources.list

RUN apt-get update --fix-missing -o Acquire::http::No-Cache=True && apt-get install -y mariadb-client
# 复制jar文件到路径
EXPOSE 48086
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' >/etc/timezone

RUN apt-get update && apt-get install -y \
    fontconfig \
    fonts-dejavu \
    && rm -rf /var/lib/apt/lists/*

ADD docker/zj/gateway/jar/zj-module-om-biz.jar ./
# 创建日志目录并设置权限
RUN mkdir -p /var/log/monitor && chmod -R 777 /var/log/monitor


# 启动系统服务
ENTRYPOINT [ "sh", "-c", "java -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/var/log/monitor/zj-module-om-heapdump.hprof -jar zj-module-om-biz.jar"]