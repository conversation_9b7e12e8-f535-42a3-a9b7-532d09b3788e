pipeline {
  agent {
    node {
      label 'maven'
    }

  }
  stages {
    stage('拉取') {
      agent none
      steps {
        git(url: 'http://gitlab.kube.com/zj-cloud-stardust/zj-server-cloud.git', credentialsId: 'git', branch: 'master', changelog: true, poll: false)
        sh 'ls'
      }
    }

    stage('编译') {
      agent none
      steps {
        container('maven') {
          sh 'ls'
          sh '''mvn clean install -pl zj-module-monitor -amd -Dmaven.test.skip
'''
          sh '''cd docker
chmod +x copy.sh
/bin/bash copy.sh
'''
          sh 'find -name \'*.jar\''
        }

      }
    }

    stage('构建monitor镜像') {
      agent none
      steps {
        container('maven') {
          sh '''ls -l docker/zj/modules/monitor/
docker build -f ./docker/zj/modules/monitor/dockerfile -t $REGISTRY/$DOCKERHUB_NAMESPACE/zj-modules-monitor:$BUILD_NUMBER .'''
        }

      }
    }

    stage('推送monitor镜像') {
      agent none
      steps {
        container('maven') {
          withCredentials([usernamePassword(credentialsId: 'hub', passwordVariable: 'DOCKER_PASSWORD', usernameVariable: 'DOCKER_USERNAME')]) {
            sh 'docker login $REGISTRY -u "$DOCKER_USERNAME" -p "$DOCKER_PASSWORD"'
          }

          sh '''docker push $REGISTRY/$DOCKERHUB_NAMESPACE/zj-modules-monitor:$BUILD_NUMBER
docker image tag $REGISTRY/$DOCKERHUB_NAMESPACE/zj-modules-monitor:$BUILD_NUMBER $REGISTRY/$DOCKERHUB_NAMESPACE/zj-modules-monitor:latest
docker push $REGISTRY/$DOCKERHUB_NAMESPACE/zj-modules-monitor:latest'''
        }

      }
    }

    stage('部署k8s') {
      agent none
      steps {
        container('maven') {
          withCredentials([kubeconfigContent(credentialsId: 'k8s', variable: 'KUBECONFIG_CONTENT')]) {
            sh '''mkdir ~/.kube
echo "$KUBECONFIG_CONTENT" > ~/.kube/config
envsubst < ./docker/zj/modules/monitor/deploy.yaml | kubectl apply -f -

'''
          }

        }

      }
    }
  }

}