# 基础镜像
FROM harbor.zjiecn.com/cache/library/openjdk:17-slim-buster
# author
MAINTAINER zj

# 复制jar文件到路径
EXPOSE 48085
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' >/etc/timezone
# 创建日志目录并设置权限
RUN mkdir -p /var/log/monitor && chmod -R 777 /var/log/monitor
RUN apt-get update && apt-get install -y \
    fontconfig \
    fonts-dejavu \
    && rm -rf /var/lib/apt/lists/*

COPY docker/zj/gateway/jar/zj-module-monitor-biz.jar ./

# 启动系统服务
ENTRYPOINT [ "sh", "-c", "java -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/var/log/monitor/zj-module-monitor-heapdump.hprof -jar zj-module-monitor-biz.jar"]