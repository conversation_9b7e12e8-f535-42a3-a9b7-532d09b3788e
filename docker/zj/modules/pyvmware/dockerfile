FROM python:3.9.18

LABEL authors="yq"

# 设置环境变量 profile 的值为默认值
ENV PROFILE=prod
# 设置环境变量 PIP_INDEX_URL 和 PIP_TRUSTED_HOST
ENV PIP_INDEX_URL=https://pypi.tuna.tsinghua.edu.cn/simple
ENV PIP_TRUSTED_HOST=pypi.tuna.tsinghua.edu.cn

# 设置工作目录
WORKDIR /app

# 复制整个项目到容器中
COPY ./zj-module-py-vmware/ /app

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# 赋予/app目录下的文件夹及文件写入权限
RUN chmod -R +w /app

# 执行 main.py
CMD ["python", "main.py"]
