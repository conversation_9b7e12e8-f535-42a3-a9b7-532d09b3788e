apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: zj-modules-proxy
  name: zj-modules-proxy
  namespace: zj-server   #一定要写名称空间
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  selector:
    matchLabels:
      app: zj-modules-proxy
  strategy:
    type: RollingUpdate # Recreate：在创建新Pods之前，所有现有的Pods会被杀死 RollingUpdate：滚动升级，逐步替换的策略，同时滚动升级时，支持更多的附加参数
    rollingUpdate:
      maxSurge: 50%  #maxSurge：1 表示滚动升级时会先启动1个pod
      maxUnavailable: 50% #maxUnavailable：1 表示滚动升级时允许的最大Unavailable的pod个数，也可以填写比例，maxUnavailable=50%
  template:
    metadata:
      labels:
        app: zj-modules-proxy
    spec:
      containers:
        - image: harbor.kube.com/zj-server-cloud/zj-modules-proxy
          #         readinessProbe:
          #           httpGet:
          #             path: /actuator/health
          #             port: 8080
          #           timeoutSeconds: 10
          #           failureThreshold: 30
          #           periodSeconds: 5
          imagePullPolicy: Always
          name: zj-modules-proxy
          ports:
            - name: openport
              containerPort: 9000
              protocol: TCP
            - name: sslport
              containerPort: 9002
              protocol: TCP
          resources:
            limits:
              cpu: 2
              memory: 2048Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: zj-modules-proxy
  name: zj-modules-proxy
  namespace: zj-server
spec:
  ports:
    - name: openport
      port: 9000
      protocol: TCP
      targetPort: 9000
      nodePort: 19000
    - name: sslport
      port: 9002
      protocol: TCP
      targetPort: 9002
      nodePort: 19002
  #      使用NodePort方式暴露端口  固定端口
  #      nodePort: 30887
  selector:
    app: zj-modules-proxy
  sessionAffinity: None
  type: NodePort