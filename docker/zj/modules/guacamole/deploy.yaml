apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: zj-modules-guacamole
  name: zj-modules-guacamole
  namespace: zj-server   #一定要写名称空间
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  selector:
    matchLabels:
      app: zj-modules-guacamole
  strategy:
    type: RollingUpdate # Recreate：在创建新Pods之前，所有现有的Pods会被杀死 RollingUpdate：滚动升级，逐步替换的策略，同时滚动升级时，支持更多的附加参数
    rollingUpdate:
      maxSurge: 50%  #maxSurge：1 表示滚动升级时会先启动1个pod
      maxUnavailable: 50% #maxUnavailable：1 表示滚动升级时允许的最大Unavailable的pod个数，也可以填写比例，maxUnavailable=50%
  template:
    metadata:
      labels:
        app: zj-modules-guacamole
    spec:
      volumes:
        - name: host-time
          hostPath:
            path: /etc/localtime
            type: ''
        - name: volume-hs1yhu
          persistentVolumeClaim:
            claimName: moniter-log
      containers:
        - image: harbor.kube.com/zj-server-cloud/zj-modules-guacamole
          #         readinessProbe:
          #           httpGet:
          #             path: /actuator/health
          #             port: 8080
          #           timeoutSeconds: 10
          #           failureThreshold: 30
          #           periodSeconds: 5
          imagePullPolicy: Always
          name: zj-modules-guacamole
          ports:
            - containerPort: 48085
              protocol: TCP
          resources:
            limits:
              cpu: 2
              memory: 2048Mi
          volumeMounts:
            - name: volume-hs1yhu
              mountPath: /home/<USER>/video/
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: Always

        - name: container-luh8vp
          image: 'harbor.zjiecn.com/cache/dushixiang/guacd:latest'
          ports:
            - name: http-4822
              containerPort: 4822
              protocol: TCP
          resources:
            limits:
              memory: 2Gi
          volumeMounts:
            - name: host-time
              readOnly: true
              mountPath: /etc/localtime
            - name: volume-hs1yhu
              mountPath: /home/<USER>/video/
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent

      dnsPolicy: ClusterFirst
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: zj-modules-guacamole
  name: zj-modules-guacamole
  namespace: zj-server
spec:
  ports:
    - name: http
      protocol: TCP
      port: 48091
      targetPort: 48091
      nodePort: 48256
    - name: http-4822
      protocol: TCP
      port: 4822
      targetPort: 4822
      nodePort: 56223
  selector:
    app: zj-modules-guacamole
  sessionAffinity: None
  type: NodePort