# 基础镜像
FROM harbor.zjiecn.com/cache/library/openjdk:17-slim-buster
# author
MAINTAINER zj

RUN sed -i s@/deb.debian.org/@/mirrors.163.com/@g /etc/apt/sources.list

RUN sed -i s@/security.debian.org/@/mirrors.163.com/@g /etc/apt/sources.list

RUN apt-get update --fix-missing -o Acquire::http::No-Cache=True && apt-get install -y mariadb-client
# 复制jar文件到路径
EXPOSE 48087
EXPOSE 9999
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' >/etc/timezone

RUN mkdir -p /home/<USER>/home/<USER>

ADD docker/zj/gateway/jar/zj-module-collection-biz.jar ./
ADD docker/zj/modules/collection/file-template/alertTemplate.html ./home/<USER>
# 启动系统服务
ENTRYPOINT [ "sh", "-c", "java -Xms1g -Xmx1g -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/var/log/monitor/collection.hprof -jar zj-module-collection-biz.jar"]