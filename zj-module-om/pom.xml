<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <artifactId>zj</artifactId>
        <groupId>cn.iocoder.cloud</groupId>
        <version>${revision}</version>
    </parent>
    <modules>
        <module>zj-module-om-api</module>
        <module>zj-module-om-biz</module>
    </modules>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>zj-module-om</artifactId>
    <packaging>pom</packaging> <!-- 2. 新增 packaging 为 pom -->

    <name>${project.artifactId}</name> <!-- 3. 新增 name 为 ${project.artifactId} -->

    <description> <!-- 4. 新增 description 为该模块的描述 -->
        om 模块，主要实现 运维 等功能。
    </description>


</project>