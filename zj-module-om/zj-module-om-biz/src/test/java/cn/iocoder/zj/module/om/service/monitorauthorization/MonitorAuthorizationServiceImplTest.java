package cn.iocoder.zj.module.om.service.monitorauthorization;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import cn.iocoder.zj.framework.test.core.ut.BaseDbUnitTest;

import cn.iocoder.zj.module.om.controller.admin.monitorauthorization.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.monitorauthorization.MonitorAuthorizationDO;
import cn.iocoder.zj.module.om.dal.mysql.monitorauthorization.MonitorAuthorizationMapper;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static cn.iocoder.zj.module.om.enums.ErrorCodeConstants.*;
import static cn.iocoder.zj.framework.test.core.util.AssertUtils.*;
import static cn.iocoder.zj.framework.test.core.util.RandomUtils.*;
import static cn.iocoder.zj.framework.common.util.date.LocalDateTimeUtils.*;
import static cn.iocoder.zj.framework.common.util.object.ObjectUtils.*;
import static cn.iocoder.zj.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
* {@link MonitorAuthorizationServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(MonitorAuthorizationServiceImpl.class)
public class MonitorAuthorizationServiceImplTest extends BaseDbUnitTest {

    @Resource
    private MonitorAuthorizationServiceImpl monitorAuthorizationService;

    @Resource
    private MonitorAuthorizationMapper monitorAuthorizationMapper;

    @Test
    public void testCreateMonitorAuthorization_success() {
        // 准备参数
        MonitorAuthorizationCreateReqVO reqVO = randomPojo(MonitorAuthorizationCreateReqVO.class);

        // 调用
        Long monitorAuthorizationId = monitorAuthorizationService.createMonitorAuthorization(reqVO);
        // 断言
        assertNotNull(monitorAuthorizationId);
        // 校验记录的属性是否正确
        MonitorAuthorizationDO monitorAuthorization = monitorAuthorizationMapper.selectById(monitorAuthorizationId);
        assertPojoEquals(reqVO, monitorAuthorization);
    }

    @Test
    public void testUpdateMonitorAuthorization_success() {
        // mock 数据
        MonitorAuthorizationDO dbMonitorAuthorization = randomPojo(MonitorAuthorizationDO.class);
        monitorAuthorizationMapper.insert(dbMonitorAuthorization);// @Sql: 先插入出一条存在的数据
        // 准备参数
        MonitorAuthorizationUpdateReqVO reqVO = randomPojo(MonitorAuthorizationUpdateReqVO.class, o -> {
            o.setId(dbMonitorAuthorization.getId()); // 设置更新的 ID
        });

        // 调用
        monitorAuthorizationService.updateMonitorAuthorization(reqVO);
        // 校验是否更新正确
        MonitorAuthorizationDO monitorAuthorization = monitorAuthorizationMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, monitorAuthorization);
    }

    @Test
    public void testUpdateMonitorAuthorization_notExists() {
        // 准备参数
        MonitorAuthorizationUpdateReqVO reqVO = randomPojo(MonitorAuthorizationUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> monitorAuthorizationService.updateMonitorAuthorization(reqVO), MONITOR_AUTHORIZATION_NOT_EXISTS);
    }

    @Test
    public void testDeleteMonitorAuthorization_success() {
        // mock 数据
        MonitorAuthorizationDO dbMonitorAuthorization = randomPojo(MonitorAuthorizationDO.class);
        monitorAuthorizationMapper.insert(dbMonitorAuthorization);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbMonitorAuthorization.getId();

        // 调用
        monitorAuthorizationService.deleteMonitorAuthorization(id);
       // 校验数据不存在了
       assertNull(monitorAuthorizationMapper.selectById(id));
    }

    @Test
    public void testDeleteMonitorAuthorization_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> monitorAuthorizationService.deleteMonitorAuthorization(id), MONITOR_AUTHORIZATION_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetMonitorAuthorizationPage() {
       // mock 数据
       MonitorAuthorizationDO dbMonitorAuthorization = randomPojo(MonitorAuthorizationDO.class, o -> { // 等会查询到
           o.setAssetId(null);
           o.setAssetName(null);
           o.setNickName(null);
           o.setHostName(null);
           o.setCreateTime(null);
           o.setAuthorizationTime(null);
           o.setUserId(null);
       });
       monitorAuthorizationMapper.insert(dbMonitorAuthorization);
       // 测试 assetId 不匹配
       monitorAuthorizationMapper.insert(cloneIgnoreId(dbMonitorAuthorization, o -> o.setAssetId(null)));
       // 测试 assetName 不匹配
       monitorAuthorizationMapper.insert(cloneIgnoreId(dbMonitorAuthorization, o -> o.setAssetName(null)));
       // 测试 nickName 不匹配
       monitorAuthorizationMapper.insert(cloneIgnoreId(dbMonitorAuthorization, o -> o.setNickName(null)));
       // 测试 hostName 不匹配
       monitorAuthorizationMapper.insert(cloneIgnoreId(dbMonitorAuthorization, o -> o.setHostName(null)));
       // 测试 createTime 不匹配
       monitorAuthorizationMapper.insert(cloneIgnoreId(dbMonitorAuthorization, o -> o.setCreateTime(null)));
       // 测试 authorizationTime 不匹配
       monitorAuthorizationMapper.insert(cloneIgnoreId(dbMonitorAuthorization, o -> o.setAuthorizationTime(null)));
       // 测试 userId 不匹配
       monitorAuthorizationMapper.insert(cloneIgnoreId(dbMonitorAuthorization, o -> o.setUserId(null)));
       // 准备参数
       MonitorAuthorizationPageReqVO reqVO = new MonitorAuthorizationPageReqVO();
       reqVO.setAssetId(null);
       reqVO.setAssetName(null);
       reqVO.setNickName(null);
       reqVO.setHostName(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setAuthorizationTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setUserId(null);

       // 调用
       PageResult<MonitorAuthorizationDO> pageResult = monitorAuthorizationService.getMonitorAuthorizationPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbMonitorAuthorization, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetMonitorAuthorizationList() {
       // mock 数据
       MonitorAuthorizationDO dbMonitorAuthorization = randomPojo(MonitorAuthorizationDO.class, o -> { // 等会查询到
           o.setAssetId(null);
           o.setAssetName(null);
           o.setNickName(null);
           o.setHostName(null);
           o.setCreateTime(null);
           o.setAuthorizationTime(null);
           o.setUserId(null);
       });
       monitorAuthorizationMapper.insert(dbMonitorAuthorization);
       // 测试 assetId 不匹配
       monitorAuthorizationMapper.insert(cloneIgnoreId(dbMonitorAuthorization, o -> o.setAssetId(null)));
       // 测试 assetName 不匹配
       monitorAuthorizationMapper.insert(cloneIgnoreId(dbMonitorAuthorization, o -> o.setAssetName(null)));
       // 测试 nickName 不匹配
       monitorAuthorizationMapper.insert(cloneIgnoreId(dbMonitorAuthorization, o -> o.setNickName(null)));
       // 测试 hostName 不匹配
       monitorAuthorizationMapper.insert(cloneIgnoreId(dbMonitorAuthorization, o -> o.setHostName(null)));
       // 测试 createTime 不匹配
       monitorAuthorizationMapper.insert(cloneIgnoreId(dbMonitorAuthorization, o -> o.setCreateTime(null)));
       // 测试 authorizationTime 不匹配
       monitorAuthorizationMapper.insert(cloneIgnoreId(dbMonitorAuthorization, o -> o.setAuthorizationTime(null)));
       // 测试 userId 不匹配
       monitorAuthorizationMapper.insert(cloneIgnoreId(dbMonitorAuthorization, o -> o.setUserId(null)));
       // 准备参数
       MonitorAuthorizationExportReqVO reqVO = new MonitorAuthorizationExportReqVO();
       reqVO.setAssetId(null);
       reqVO.setAssetName(null);
       reqVO.setNickName(null);
       reqVO.setHostName(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setAuthorizationTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setUserId(null);

       // 调用
       List<MonitorAuthorizationDO> list = monitorAuthorizationService.getMonitorAuthorizationList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbMonitorAuthorization, list.get(0));
    }

}
