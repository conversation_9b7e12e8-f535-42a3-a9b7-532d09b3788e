package cn.iocoder.zj.module.om.service.monitorasset;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import cn.iocoder.zj.framework.test.core.ut.BaseDbUnitTest;

import cn.iocoder.zj.module.om.controller.admin.monitorasset.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.monitorasset.MonitorAssetDO;
import cn.iocoder.zj.module.om.dal.mysql.monitorasset.MonitorAssetMapper;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static cn.iocoder.zj.module.om.enums.ErrorCodeConstants.*;
import static cn.iocoder.zj.framework.test.core.util.AssertUtils.*;
import static cn.iocoder.zj.framework.test.core.util.RandomUtils.*;
import static cn.iocoder.zj.framework.common.util.date.LocalDateTimeUtils.*;
import static cn.iocoder.zj.framework.common.util.object.ObjectUtils.*;
import static cn.iocoder.zj.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
* {@link MonitorAssetServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(MonitorAssetServiceImpl.class)
public class MonitorAssetServiceImplTest extends BaseDbUnitTest {

    @Resource
    private MonitorAssetServiceImpl monitorAssetService;

    @Resource
    private MonitorAssetMapper monitorAssetMapper;

    @Test
    public void testCreateMonitorAsset_success() {
        // 准备参数
        MonitorAssetCreateReqVO reqVO = randomPojo(MonitorAssetCreateReqVO.class);

        // 调用
        Long monitorAssetId = monitorAssetService.createMonitorAsset(reqVO);
        // 断言
        assertNotNull(monitorAssetId);
        // 校验记录的属性是否正确
        MonitorAssetDO monitorAsset = monitorAssetMapper.selectById(monitorAssetId);
        assertPojoEquals(reqVO, monitorAsset);
    }

    @Test
    public void testUpdateMonitorAsset_success() {
        // mock 数据
        MonitorAssetDO dbMonitorAsset = randomPojo(MonitorAssetDO.class);
        monitorAssetMapper.insert(dbMonitorAsset);// @Sql: 先插入出一条存在的数据
        // 准备参数
        MonitorAssetUpdateReqVO reqVO = randomPojo(MonitorAssetUpdateReqVO.class, o -> {
            o.setId(dbMonitorAsset.getId()); // 设置更新的 ID
        });

        // 调用
        monitorAssetService.updateMonitorAsset(reqVO);
        // 校验是否更新正确
        MonitorAssetDO monitorAsset = monitorAssetMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, monitorAsset);
    }

    @Test
    public void testUpdateMonitorAsset_notExists() {
        // 准备参数
        MonitorAssetUpdateReqVO reqVO = randomPojo(MonitorAssetUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> monitorAssetService.updateMonitorAsset(reqVO), MONITOR_ASSET_NOT_EXISTS);
    }

    @Test
    public void testDeleteMonitorAsset_success() {
        // mock 数据
        MonitorAssetDO dbMonitorAsset = randomPojo(MonitorAssetDO.class);
        monitorAssetMapper.insert(dbMonitorAsset);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbMonitorAsset.getId();

        // 调用
        monitorAssetService.deleteMonitorAsset(id);
       // 校验数据不存在了
       assertNull(monitorAssetMapper.selectById(id));
    }

    @Test
    public void testDeleteMonitorAsset_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> monitorAssetService.deleteMonitorAsset(id), MONITOR_ASSET_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetMonitorAssetPage() {
       // mock 数据
       MonitorAssetDO dbMonitorAsset = randomPojo(MonitorAssetDO.class, o -> { // 等会查询到
           o.setAssetId(null);
           o.setAssetName(null);
           o.setProtocol(null);
           o.setProtocolProd(null);
           o.setCertificate(null);
           o.setPlatformId(null);
           o.setPlatformName(null);
           o.setAssetType(null);
           o.setAuthorizationType(null);
           o.setAuthorizationTime(null);
           o.setCreateTime(null);
           o.setUsername(null);
           o.setPassword(null);
       });
       monitorAssetMapper.insert(dbMonitorAsset);
       // 测试 assetId 不匹配
       monitorAssetMapper.insert(cloneIgnoreId(dbMonitorAsset, o -> o.setAssetId(null)));
       // 测试 assetName 不匹配
       monitorAssetMapper.insert(cloneIgnoreId(dbMonitorAsset, o -> o.setAssetName(null)));
       // 测试 protocol 不匹配
       monitorAssetMapper.insert(cloneIgnoreId(dbMonitorAsset, o -> o.setProtocol(null)));
       // 测试 protocolProd 不匹配
       monitorAssetMapper.insert(cloneIgnoreId(dbMonitorAsset, o -> o.setProtocolProd(null)));
       // 测试 certificate 不匹配
       monitorAssetMapper.insert(cloneIgnoreId(dbMonitorAsset, o -> o.setCertificate(null)));
       // 测试 platformId 不匹配
       monitorAssetMapper.insert(cloneIgnoreId(dbMonitorAsset, o -> o.setPlatformId(null)));
       // 测试 platformName 不匹配
       monitorAssetMapper.insert(cloneIgnoreId(dbMonitorAsset, o -> o.setPlatformName(null)));
       // 测试 assetType 不匹配
       monitorAssetMapper.insert(cloneIgnoreId(dbMonitorAsset, o -> o.setAssetType(null)));
       // 测试 authorizationType 不匹配
       monitorAssetMapper.insert(cloneIgnoreId(dbMonitorAsset, o -> o.setAuthorizationType(null)));
       // 测试 authorizationTime 不匹配
       monitorAssetMapper.insert(cloneIgnoreId(dbMonitorAsset, o -> o.setAuthorizationTime(null)));
       // 测试 createTime 不匹配
       monitorAssetMapper.insert(cloneIgnoreId(dbMonitorAsset, o -> o.setCreateTime(null)));
       // 测试 username 不匹配
       monitorAssetMapper.insert(cloneIgnoreId(dbMonitorAsset, o -> o.setUsername(null)));
       // 测试 password 不匹配
       monitorAssetMapper.insert(cloneIgnoreId(dbMonitorAsset, o -> o.setPassword(null)));
       // 准备参数
       MonitorAssetPageReqVO reqVO = new MonitorAssetPageReqVO();
       reqVO.setAssetId(null);
       reqVO.setAssetName(null);
       reqVO.setProtocol(null);
       reqVO.setProtocolProd(null);
       reqVO.setCertificate(null);
       reqVO.setPlatformId(null);
       reqVO.setPlatformName(null);
       reqVO.setAssetType(null);
       reqVO.setAuthorizationType(null);
       reqVO.setAuthorizationTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setUsername(null);
       reqVO.setPassword(null);

       // 调用
       PageResult<MonitorAssetDO> pageResult = monitorAssetService.getMonitorAssetPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbMonitorAsset, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetMonitorAssetList() {
       // mock 数据
       MonitorAssetDO dbMonitorAsset = randomPojo(MonitorAssetDO.class, o -> { // 等会查询到
           o.setAssetId(null);
           o.setAssetName(null);
           o.setProtocol(null);
           o.setProtocolProd(null);
           o.setCertificate(null);
           o.setPlatformId(null);
           o.setPlatformName(null);
           o.setAssetType(null);
           o.setAuthorizationType(null);
           o.setAuthorizationTime(null);
           o.setCreateTime(null);
           o.setUsername(null);
           o.setPassword(null);
       });
       monitorAssetMapper.insert(dbMonitorAsset);
       // 测试 assetId 不匹配
       monitorAssetMapper.insert(cloneIgnoreId(dbMonitorAsset, o -> o.setAssetId(null)));
       // 测试 assetName 不匹配
       monitorAssetMapper.insert(cloneIgnoreId(dbMonitorAsset, o -> o.setAssetName(null)));
       // 测试 protocol 不匹配
       monitorAssetMapper.insert(cloneIgnoreId(dbMonitorAsset, o -> o.setProtocol(null)));
       // 测试 protocolProd 不匹配
       monitorAssetMapper.insert(cloneIgnoreId(dbMonitorAsset, o -> o.setProtocolProd(null)));
       // 测试 certificate 不匹配
       monitorAssetMapper.insert(cloneIgnoreId(dbMonitorAsset, o -> o.setCertificate(null)));
       // 测试 platformId 不匹配
       monitorAssetMapper.insert(cloneIgnoreId(dbMonitorAsset, o -> o.setPlatformId(null)));
       // 测试 platformName 不匹配
       monitorAssetMapper.insert(cloneIgnoreId(dbMonitorAsset, o -> o.setPlatformName(null)));
       // 测试 assetType 不匹配
       monitorAssetMapper.insert(cloneIgnoreId(dbMonitorAsset, o -> o.setAssetType(null)));
       // 测试 authorizationType 不匹配
       monitorAssetMapper.insert(cloneIgnoreId(dbMonitorAsset, o -> o.setAuthorizationType(null)));
       // 测试 authorizationTime 不匹配
       monitorAssetMapper.insert(cloneIgnoreId(dbMonitorAsset, o -> o.setAuthorizationTime(null)));
       // 测试 createTime 不匹配
       monitorAssetMapper.insert(cloneIgnoreId(dbMonitorAsset, o -> o.setCreateTime(null)));
       // 测试 username 不匹配
       monitorAssetMapper.insert(cloneIgnoreId(dbMonitorAsset, o -> o.setUsername(null)));
       // 测试 password 不匹配
       monitorAssetMapper.insert(cloneIgnoreId(dbMonitorAsset, o -> o.setPassword(null)));
       // 准备参数
       MonitorAssetExportReqVO reqVO = new MonitorAssetExportReqVO();
       reqVO.setAssetId(null);
       reqVO.setAssetName(null);
       reqVO.setProtocol(null);
       reqVO.setProtocolProd(null);
       reqVO.setCertificate(null);
       reqVO.setPlatformId(null);
       reqVO.setPlatformName(null);
       reqVO.setAssetType(null);
       reqVO.setAuthorizationType(null);
       reqVO.setAuthorizationTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setUsername(null);
       reqVO.setPassword(null);

       // 调用
       List<MonitorAssetDO> list = monitorAssetService.getMonitorAssetList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbMonitorAsset, list.get(0));
    }

}
