package cn.iocoder.zj.module.om.controller.admin.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import javax.validation.constraints.*;

/**
* 工单管理数据 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class WorkOrderBaseVO {

    @Schema(description = "名称",required = true)
    private String name;

    @Schema(description = "平台名称）")
    private String platformName;

    @Schema(description = "平台ID")
    private Long platformId;

    @Schema(description = "资源名称）")
    private String sourceName;

    @Schema(description = "资源uuid")
    private String sourceUuid;

    @Schema(description = "简介")
    private String description;

    @Schema(description = "事件")
    private String event;

    @Schema(description = "审批人ID")
    private Long auditorId;

    @Schema(description = "审批人名称")
    private String auditorName;

    @Schema(description = "实施人ID")
    private String enforcerId;

    @Schema(description = "实施人名称")
    private String enforcerName;

    @Schema(description = "开始实施时间")
    private Date enforcerStartTime;

    @Schema(description = "结束实施时间")
    private Date enforcerEndTime;

    @Schema(description = "审批状态，consent同意，reject驳回,wait待审核")
    private String ratifyStatus;

    @Schema(description = "准备状态，valid有效的，invalid无效")
    private String readinessStatus;

    @Schema(description = "创建人id")
    private Long creator;

    @Schema(description = "创建人名称")
    private String creatorName;

    @Schema(description = "创建类型：auto自动创建，hand手动创建")
    private String createType;

    @Schema(description = "操作（自动创建时填写，operateHost操作云主机，operateHardware操作宿主机，operateStorage操作主存储，createHost创建云主机，createHardware创建宿主机）")
    private String operation;

    @Schema(description = "资源类型对应的字典编码")
    private String dictCode;

    @Schema(description = "资源类型对应的字典名称")
    private String dictName;

    @Schema(description = "工单类型对应的字典编码")
    private String typeCode;

    @Schema(description = "工单类型对应的字典名称")
    private String typeName;

    @Schema(description = "是否已读：0未读，1已读")
    private Integer isRead;

}
