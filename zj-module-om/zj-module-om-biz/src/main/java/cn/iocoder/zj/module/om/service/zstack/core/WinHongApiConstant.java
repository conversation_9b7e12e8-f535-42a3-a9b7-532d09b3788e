package cn.iocoder.zj.module.om.service.zstack.core;

public class WinHongApiConstant {

    /**
     * 云主机相关
     */

    public static final String WIN_HONG_API_PREFIX = "/api";
    public static final String LOGIN = WIN_HONG_API_PREFIX + "/login";

    public static final String MONITOR = "/monitor";

    public static final String COMPUTE = "/compute";

    public static final String GET_FLOW_INFO = WIN_HONG_API_PREFIX + MONITOR + "/host/network/flow";

    public static final String PATCH_HOST_START = WIN_HONG_API_PREFIX + COMPUTE + "/domains/{domainId}/act/start"; //PATCH
    public static final String PATCH_HOST_SAVE = WIN_HONG_API_PREFIX + COMPUTE + "/domains/{domainId}/act/save"; //PATCH
    public static final String PATCH_HOST_REBOOT = WIN_HONG_API_PREFIX + COMPUTE + "/domains/{domainId}/act/reboot";  //POST
    public static final String PATCH_HOST_DELETE = WIN_HONG_API_PREFIX + COMPUTE + "/domains/{domainId}/2";  //DELETE

    public static final String GET_TASK_INFO = WIN_HONG_API_PREFIX +"/notify/tasks/{taskId}/info";  //GET

    public static final String GET_VNC_INFO = WIN_HONG_API_PREFIX + COMPUTE +"/domains/{domainId}/vncInfo";  //GET
    public static final String GET_VNC_URL = WIN_HONG_API_PREFIX +COMPUTE+"/domains/{domainId}/noVNC/vnc";  //GET

    public static final String GET_GROUPS_INFO = WIN_HONG_API_PREFIX + COMPUTE + "/hosts/{hostId}/portGroups/v2?id={id}";  //GET


    public static final String POST_DOMAINS = WIN_HONG_API_PREFIX + COMPUTE + "/domains";  //POST
    public static final String GET_HOST_DOMAINS = WIN_HONG_API_PREFIX + COMPUTE + "/domains";  //POST

    public static final String GET_SUMMARY = WIN_HONG_API_PREFIX + "/compute/domains/{domainId}/summary";


    //--------------宿主机--------------
    public static final String PATCH_HOSTS_REBOOT = WIN_HONG_API_PREFIX + COMPUTE + "/hosts/reboot/{hostId}"; //PATCH
    public static final String PATCH_HOSTS_SHUTDOWN = WIN_HONG_API_PREFIX + COMPUTE + "/hosts/shutdown/{hostId}"; //PATCH
    public static final String PATCH_HOSTS_MAINTAIN = WIN_HONG_API_PREFIX + COMPUTE + "/hosts/{hostId}/act/maintain"; //PATCH
    public static final String PATCH_HOSTS_WORK = WIN_HONG_API_PREFIX + COMPUTE + "/hosts/{hostId}/act/work"; //PATCH






}
