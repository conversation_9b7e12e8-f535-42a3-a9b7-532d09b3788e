package cn.iocoder.zj.module.om.controller.admin.monitorauthorization.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 监控申请授权 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MonitorAuthorizationRespVO extends MonitorAuthorizationBaseVO {

    @Schema(description = "主键", required = true)
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;


    /**
     * 授权状态
     */
    @Schema(description = "授权状态")
    private String authorizationType;

}
