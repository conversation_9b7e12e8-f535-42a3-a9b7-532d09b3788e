package cn.iocoder.zj.module.om.controller.admin.patrolabnormaldetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 巡检异常明细 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class PatrolAbnormalDetailBaseVO {

    @Schema(description = "巡检记录ID", required = true)
    @NotNull(message = "巡检记录ID不能为空")
    private Long recordId;

    @Schema(description = "巡检结果分类ID", required = true)
    @NotNull(message = "巡检结果分类ID不能为空")
    private Long categoryId;

    @Schema(description = "资源类型(存储资源/宿主机资源/云主机资源/云硬盘资源等)", required = true)
    @NotNull(message = "资源类型(存储资源/宿主机资源/云主机资源/云硬盘资源等)不能为空")
    private String resourceType;

    @Schema(description = "指标名称(CPU分配/内存分配/网卡入速度等)", required = true)
    @NotNull(message = "指标名称(CPU分配/内存分配/网卡入速度等)不能为空")
    private String metricName;

    @Schema(description = "资源ID")
    private String resourceId;

    @Schema(description = "资源名称")
    private String resourceName;

    @Schema(description = "平台ID")
    private String platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "详情")
    private String detail;

    @Schema(description = "风险等级(正常/低风险/中风险/高风险)", required = true)
    @NotNull(message = "风险等级(正常/低风险/中风险/高风险)不能为空")
    private String riskLevel;

    @Schema(description = "建议")
    private String suggest;

}
