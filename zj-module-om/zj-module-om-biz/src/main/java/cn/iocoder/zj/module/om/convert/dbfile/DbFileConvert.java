package cn.iocoder.zj.module.om.convert.dbfile;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.om.api.dbfile.dto.DbfileCreateReqDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.om.controller.admin.dbfile.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.dbfile.DbFileDO;

/**
 * 配置备份 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DbFileConvert {

    DbFileConvert INSTANCE = Mappers.getMapper(DbFileConvert.class);

    DbFileDO convert(DbFileCreateReqVO bean);

    DbFileDO convert(DbFileUpdateReqVO bean);

    DbFileRespVO convert(DbFileDO bean);

    List<DbFileRespVO> convertList(List<DbFileDO> list);

    PageResult<DbFileRespVO> convertPage(PageResult<DbFileDO> page);

    List<DbFileExcelVO> convertList02(List<DbFileDO> list);


    DbFileCreateReqVO convertReq(DbfileCreateReqDTO reqDTO);
}
