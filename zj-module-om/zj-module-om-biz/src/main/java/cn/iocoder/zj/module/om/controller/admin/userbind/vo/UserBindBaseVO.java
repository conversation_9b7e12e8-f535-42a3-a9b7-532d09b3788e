package cn.iocoder.zj.module.om.controller.admin.userbind.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 用户推送绑定 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class UserBindBaseVO {

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "用户名称")
    private String userName;

    @Schema(description = "企业微信推送启用状态，0未启用，1已启用")
    private Integer wxState;

    @Schema(description = "邮箱推送启用状态，0未启用，1已启用")
    private Integer emailState;

    @Schema(description = "钉钉推送启用状态，0未启用,1启用中")
    private Integer dingtalkState;

    @Schema(description = "钉钉应用key")
    private String dingtalkAppKey;

    @Schema(description = "钉钉应用秘钥")
    private String dingtalkAppSecret;

    @Schema(description = "应用id")
    private String dingtalkAgentId;

    @Schema(description = "钉钉手机号")
    private String dingtalkPhone;

    @Schema(description = "企微应用id")
    private String wxAgentId;

    @Schema(description = "企微企业id")
    private String wxCorpid;

    @Schema(description = "企微企业秘钥")
    private String wxCorpsecret;

    @Schema(description = "企微手机号")
    private String wxPhone;

    @Schema(description = "回调地址")
    private String callbackUrl;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updateTime;

    @Schema(description = "邮箱地址")
    private String mailAddress;

    @Schema(description = "邮箱密码")
    private String mailPassword;

    @Schema(description = "smtp地址")
    private String mailSmtpHost;
}
