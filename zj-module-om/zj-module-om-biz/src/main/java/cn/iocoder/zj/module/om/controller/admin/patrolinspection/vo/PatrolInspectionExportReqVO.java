package cn.iocoder.zj.module.om.controller.admin.patrolinspection.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import java.time.LocalDateTime;

import org.apache.poi.hpsf.Decimal;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 自动巡检规则 Excel 导出 Request VO，参数和 PatrolInspectionPageReqVO 是一致的")
@Data
public class PatrolInspectionExportReqVO {

    @ExcelProperty("名称")
    private String name;

    @ExcelProperty("type")
    private String type;

    @ExcelProperty("数据源")
    private String dataResource;

    @ExcelProperty(value = "阈值")
    private BigDecimal threshold;

    @ExcelProperty(value ="总分")
    private BigDecimal value;

    @ExcelProperty(value ="资产类型")
    private String assetType;

    @ExcelProperty(value = "扣分规则")
    private String rule;

    @ExcelProperty(value ="计算公式")
    private String formula;

    @ExcelProperty(value ="参数")
    private String param;

}
