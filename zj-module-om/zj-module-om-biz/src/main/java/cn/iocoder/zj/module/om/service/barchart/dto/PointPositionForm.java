//package cn.iocoder.zj.module.om.service.barchart.dto;
//
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
//import java.util.List;
//
///**
// * @ClassName : PointPositionForm  //类名
// * @Description : s  //描述
// * <AUTHOR> <EMAIL> //作者
// * @Date: 2023/9/6  14:57
// */
//
//@Data
//@NoArgsConstructor
//@AllArgsConstructor
//@Builder
//public class PointPositionForm {
//    private List<String> healthKitScreenshots;
//    private String healthKitGeneratorName;
//    private String phoneNum;
//    private String idNum;
//
//}
