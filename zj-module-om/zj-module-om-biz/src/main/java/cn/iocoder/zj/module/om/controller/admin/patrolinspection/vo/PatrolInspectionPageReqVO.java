package cn.iocoder.zj.module.om.controller.admin.patrolinspection.vo;

import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.apache.poi.hpsf.Decimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 自动巡检规则分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PatrolInspectionPageReqVO extends PageParam {

    @Schema(description = "检测项名称")
    private String name;

    @Schema(description = "检测项类型")
    private String type;

    @Schema(description = "资产类型")
    private String assetType;

    @Schema(description = "阈值")
    private BigDecimal threshold;

    @Schema(description = "分值")
    private BigDecimal value;

    @Schema(description = "扣分规则")
    private String rule;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
