package cn.iocoder.zj.module.om.Inspection.job.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("timer_task")
public class LulTaskModel {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 任务名称
     */
    private String jobName;

    /**
     * cron表达式
     */
    private String cronExpression;

    /**
     * bean名称
     */
    private String beanName;

    /**
     * 方法名称
     */
    private String methodName;

    /**
     * 任务执行结果
     */
    private String jobResult;


    /**
     * 要执行的任务
     */
    @TableField(exist = false)
    private Runnable task;

    /**
     * 创建时间
     */
    @Builder.Default
    private Date createDate = new Date();

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建人
     */
    @Builder.Default
    private String createBy = "default";

    /**
     * 更新人
     */
    @Builder.Default
    private String updateBy = "default";

    /**
     * 版本号
     */
    private Integer version;
}
