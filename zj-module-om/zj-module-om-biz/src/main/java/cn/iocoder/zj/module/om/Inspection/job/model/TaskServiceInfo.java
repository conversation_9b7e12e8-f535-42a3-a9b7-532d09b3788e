package cn.iocoder.zj.module.om.Inspection.job.model;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.zj.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.zj.module.om.Inspection.job.task.LulTaskService;
import cn.iocoder.zj.module.om.Inspection.service.PatrolMetricService;
import cn.iocoder.zj.module.om.dal.dataobject.patrolplan.PatrolPlanDO;
import cn.iocoder.zj.module.om.service.patrolplan.PatrolPlanService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.stream.Collectors;

import static cn.iocoder.zj.module.om.util.CronExpressionUtil.getNextExecutionTime;

@Slf4j
@Component
public class TaskServiceInfo {

    @Autowired
    @Lazy
    private PatrolMetricService patrolMetricService;
    @Autowired
    @Lazy
    private PatrolPlanService patrolPlanService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private LulTaskService lulTaskService;

    private final ThreadPoolTaskScheduler threadPoolTaskScheduler;

    private final Map<Long, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>(32);

    public TaskServiceInfo(ThreadPoolTaskScheduler threadPoolTaskScheduler) {
        this.threadPoolTaskScheduler = threadPoolTaskScheduler;
    }

    public void updateTask(LulTaskModel updatedTask) {
        removeTask(updatedTask.getId());
        scheduleTask(updatedTask);
    }

    public void removeTask(Long taskId) {
        Optional.ofNullable(scheduledTasks.remove(taskId))
                .ifPresent(scheduledFuture -> scheduledFuture.cancel(false));
    }

    @EventListener(ContextRefreshedEvent.class)
    public void startPolling() {
        String lockKey = "lul_task";
        RLock lock = redissonClient.getLock(lockKey);
        if (!lock.tryLock()) {
            log.warn("Failed to acquire lock for task: {}", lockKey);
            return;
        }
        try {
            List<LulTaskModel> tasks = Optional.ofNullable(lulTaskService.listTasks())
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(task -> task.getStatus() == 1)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(tasks)) {
                tasks.forEach(this::scheduleTask);
            }
        } catch (Exception e) {
            lock.unlock();
            log.error("Error scheduling task: {}", lockKey, e);
        }
    }

    public void scheduleTask(LulTaskModel task) {
//        String lockKey = "lul_task:".concat(task.getId().toString());
//        RLock lock = redissonClient.getLock(lockKey);
//        if (!lock.tryLock()) {
//            log.warn("Failed to acquire lock for task: {}", task.getJobName());
//            return;
//        }
//        try {
            scheduledTasks.computeIfAbsent(task.getId(), id -> {
                Runnable runnableTask = () -> {
                    try {
                        toDoTask(id);
                    } catch (Exception e) {
                        log.error("Error executing task: {}", id, e);
                    }
                };
                ScheduledFuture<?> scheduledFuture =
                        threadPoolTaskScheduler.schedule(runnableTask, new CronTrigger(task.getCronExpression()));
                log.info("Scheduled task: {}, expression: {}", task.getJobName(), task.getCronExpression());
                return scheduledFuture;
            });
//        } catch (Exception e) {
//            lock.unlock();
//            log.error("Error scheduling task: {}", task.getJobName(), e);
//        }
    }


    /**
     * 需要执行的任务
     *
     * @param jobId
     */
    private void toDoTask(Long jobId) {
        log.info("toDoTask: {}", jobId);
        Date now = new Date();
        // 根据jobId 查询计划任务
        PatrolPlanDO patrolPlanDO = patrolPlanService.getPatrolPlanByJobId(jobId);
        if (patrolPlanDO != null) {
            TenantContextHolder.setTenantId(patrolPlanDO.getTenantId());
            String cronExpression = patrolPlanDO.getExecutionCron();
            Date nextExecutionTime = getNextExecutionTime(cronExpression, now);
            //修改计划任务表中最近执行时间和下次执行时间
            patrolPlanDO.setLastExecutionTime(now);
            patrolPlanDO.setNextExecutionTime(nextExecutionTime);
            patrolPlanService.updateLastTimeAndNextTime(patrolPlanDO);
            log.info("修改任务最近执行时间: {}", patrolPlanDO);
            patrolMetricService.patrolMetric(patrolPlanDO);
        }
    }

}
