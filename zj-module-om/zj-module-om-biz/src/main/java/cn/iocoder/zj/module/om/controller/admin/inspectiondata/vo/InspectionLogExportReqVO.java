package cn.iocoder.zj.module.om.controller.admin.inspectiondata.vo;

import lombok.*;

import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 巡检记录 Excel 导出 Request VO，参数和 InspectionLogPageReqVO 是一致的")
@Data
public class InspectionLogExportReqVO {

    @Schema(description = "巡检名称")
    private String inspectionName;

    @Schema(description = "巡检记录uuid")
    private String recordUuid;

    @Schema(description = "阈值")
    private BigDecimal threshold;

    @Schema(description = "检测结果")
    private String result;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
