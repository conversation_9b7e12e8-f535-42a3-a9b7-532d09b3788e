package cn.iocoder.zj.module.om.controller.admin.vncinfo.vo;

import lombok.Data;

@Data
public class VncInfoVo {
    // WebSocket 服务器主机
    private String websocketHost;

    // WebSocket 服务器端口
    private Integer websocketPort;

    // WebSocket 路径
    private String websocketPath;

    private String vncConsoleUrl;

    // 本地 TCP 服务器端口
    private Integer tcpPort;

    private String vncToken;

    // 是否使用 SSL (wss://)
    private Boolean useSsl;

    private String portMappingId;

    private String username;

    private String password;

    private Long PlatformId;

}
