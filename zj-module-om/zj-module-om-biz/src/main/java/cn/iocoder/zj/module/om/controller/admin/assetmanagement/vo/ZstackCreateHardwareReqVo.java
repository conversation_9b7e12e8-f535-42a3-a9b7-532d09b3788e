package cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ZstackCreateHardwareReqVo {
    @Schema(description = "平台ID",required = true)
    private Long platformId;

    @Schema(description = "ssh用户名",required = true)
    private String username;

    @Schema(description = "ssh密码",required = true)
    private String password;

    @Schema(description = "ssh端口号，用户填写",required = false)
    private Integer sshPort;

    @Schema(description = "资源名称",required = true)
    private String name;

    @Schema(description = "资源的详细描述",required = false)
    private String description ;

    @Schema(description = "宿主机管理节点IP，用户填写",required = true)
    private String managementIp ;

    @Schema(description = "集群UUID",required = true)
    private String clusterUuid ;
}
