package cn.iocoder.zj.module.om.service.patrolinspection;

import java.util.*;
import javax.validation.*;

import cn.iocoder.zj.module.om.controller.admin.patrolinspection.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspection.PatrolInspectionDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 自动巡检规则 Service 接口
 *
 * <AUTHOR>
 */
public interface PatrolInspectionService {

    /**
     * 更新自动巡检规则
     *
     * @param updateReqVO 更新信息
     */
    void updatePatrolInspection(@Valid PatrolInspectionUpdateReqVO updateReqVO);

    /**
     * 删除自动巡检规则
     *
     * @param id 编号
     */
    void deletePatrolInspection(Long id);

    /**
     * 获得自动巡检规则
     *
     * @param id 编号
     * @return 自动巡检规则
     */
    PatrolInspectionDO getPatrolInspection(Long id);

    /**
     * 获得自动巡检规则列表
     *
     * @param ids 编号
     * @return 自动巡检规则列表
     */
    List<PatrolInspectionDO> getPatrolInspectionList(Collection<Long> ids);

    /**
     * 获得自动巡检规则分页
     *
     * @param pageReqVO 分页查询
     * @return 自动巡检规则分页
     */
    PageResult<PatrolInspectionDO> getPatrolInspectionPage(PatrolInspectionPageReqVO pageReqVO);

    /**
     * 获得自动巡检规则列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 自动巡检规则列表
     */
    List<PatrolInspectionDO> getPatrolInspectionList(PatrolInspectionExportReqVO exportReqVO);

    PatrolInspectionImportRespVO importPatrolInspectionList(List<PatrolInspectionImportExcelVO> list, Boolean updateSupport);

    Map<String,Object> patrolInspectionStart(Long tenantId);

    Map getInspectionBaseInfo(Long tenantId);
}
