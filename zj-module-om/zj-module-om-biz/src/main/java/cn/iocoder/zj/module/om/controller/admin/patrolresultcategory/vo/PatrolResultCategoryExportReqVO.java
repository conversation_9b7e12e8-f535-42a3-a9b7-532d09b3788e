package cn.iocoder.zj.module.om.controller.admin.patrolresultcategory.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 巡检结果分类 Excel 导出 Request VO，参数和 PatrolResultCategoryPageReqVO 是一致的")
@Data
public class PatrolResultCategoryExportReqVO {

    @Schema(description = "巡检记录ID")
    private Long recordId;

    @Schema(description = "资源类型(存储资源/宿主机资源/云主机资源/云硬盘资源/等)")
    private String resourceType;

    @Schema(description = "指标名称(CPU分配/内存分配/网卡入速度等)")
    private String metricName;

    @Schema(description = "巡检资源数")
    private Integer resourceCount;

    @Schema(description = "检测结果：0 正常 1 异常")
    private Integer status;

    @Schema(description = "正常数量")
    private Integer normalCount;

    @Schema(description = "低风险数量")
    private Integer lowRiskCount;

    @Schema(description = "中风险数量")
    private Integer mediumRiskCount;

    @Schema(description = "高风险数量")
    private Integer highRiskCount;

    @Schema(description = "详情")
    private String detail;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
