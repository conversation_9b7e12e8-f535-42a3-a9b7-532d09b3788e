package cn.iocoder.zj.module.om.dal.mysql.patrolresultcategory;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.module.om.controller.admin.patrolresultcategory.vo.PatrolResultCategoryExportReqVO;
import cn.iocoder.zj.module.om.controller.admin.patrolresultcategory.vo.PatrolResultCategoryPageReqVO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolresultcategory.PatrolResultCategoryDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 巡检结果分类 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PatrolResultCategoryMapper extends BaseMapperX<PatrolResultCategoryDO> {

    default PageResult<PatrolResultCategoryDO> selectPage(PatrolResultCategoryPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PatrolResultCategoryDO>()
                .eqIfPresent(PatrolResultCategoryDO::getRecordId, reqVO.getRecordId())
                .eqIfPresent(PatrolResultCategoryDO::getResourceType, reqVO.getResourceType())
                .likeIfPresent(PatrolResultCategoryDO::getMetricName, reqVO.getMetricName())
                .eqIfPresent(PatrolResultCategoryDO::getResourceCount, reqVO.getResourceCount())
                .eqIfPresent(PatrolResultCategoryDO::getStatus, reqVO.getStatus())
                .eqIfPresent(PatrolResultCategoryDO::getNormalCount, reqVO.getNormalCount())
                .eqIfPresent(PatrolResultCategoryDO::getLowRiskCount, reqVO.getLowRiskCount())
                .eqIfPresent(PatrolResultCategoryDO::getMediumRiskCount, reqVO.getMediumRiskCount())
                .eqIfPresent(PatrolResultCategoryDO::getHighRiskCount, reqVO.getHighRiskCount())
                .eqIfPresent(PatrolResultCategoryDO::getDetail, reqVO.getDetail())
                .betweenIfPresent(PatrolResultCategoryDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PatrolResultCategoryDO::getId));
    }

    default List<PatrolResultCategoryDO> selectList(PatrolResultCategoryExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PatrolResultCategoryDO>()
                .eqIfPresent(PatrolResultCategoryDO::getRecordId, reqVO.getRecordId())
                .eqIfPresent(PatrolResultCategoryDO::getResourceType, reqVO.getResourceType())
                .likeIfPresent(PatrolResultCategoryDO::getMetricName, reqVO.getMetricName())
                .eqIfPresent(PatrolResultCategoryDO::getResourceCount, reqVO.getResourceCount())
                .eqIfPresent(PatrolResultCategoryDO::getStatus, reqVO.getStatus())
                .eqIfPresent(PatrolResultCategoryDO::getNormalCount, reqVO.getNormalCount())
                .eqIfPresent(PatrolResultCategoryDO::getLowRiskCount, reqVO.getLowRiskCount())
                .eqIfPresent(PatrolResultCategoryDO::getMediumRiskCount, reqVO.getMediumRiskCount())
                .eqIfPresent(PatrolResultCategoryDO::getHighRiskCount, reqVO.getHighRiskCount())
                .eqIfPresent(PatrolResultCategoryDO::getDetail, reqVO.getDetail())
                .betweenIfPresent(PatrolResultCategoryDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PatrolResultCategoryDO::getId));
    }

}
