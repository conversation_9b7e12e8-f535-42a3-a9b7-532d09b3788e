package cn.iocoder.zj.module.om.service.inspectionlogs;

import java.util.*;
import javax.validation.*;
import cn.iocoder.zj.module.om.controller.admin.inspectionlogs.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.inspectionlogs.InspectionLogsDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 自动巡检异常记录 Service 接口
 *
 * <AUTHOR>
 */
public interface InspectionLogsService {

    /**
     * 创建自动巡检异常记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInspectionLogs(@Valid InspectionLogsCreateReqVO createReqVO);

    /**
     * 更新自动巡检异常记录
     *
     * @param updateReqVO 更新信息
     */
    void updateInspectionLogs(@Valid InspectionLogsUpdateReqVO updateReqVO);

    /**
     * 删除自动巡检异常记录
     *
     * @param id 编号
     */
    void deleteInspectionLogs(Long id);

    /**
     * 获得自动巡检异常记录
     *
     * @param id 编号
     * @return 自动巡检异常记录
     */
    InspectionLogsDO getInspectionLogs(Long id);

    /**
     * 获得自动巡检异常记录列表
     *
     * @param ids 编号
     * @return 自动巡检异常记录列表
     */
    List<InspectionLogsDO> getInspectionLogsList(Collection<Long> ids);

    /**
     * 获得自动巡检异常记录分页
     *
     * @param pageReqVO 分页查询
     * @return 自动巡检异常记录分页
     */
    PageResult<InspectionLogsDO> getInspectionLogsPage(InspectionLogsPageReqVO pageReqVO);

    /**
     * 获得自动巡检异常记录列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 自动巡检异常记录列表
     */
    List<InspectionLogsDO> getInspectionLogsList(InspectionLogsExportReqVO exportReqVO);

    void batchInspectionLogs(List<InspectionLogsDO> inspectionLogsDOS);

}
