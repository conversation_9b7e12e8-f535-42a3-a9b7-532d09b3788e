package cn.iocoder.zj.module.om.controller.admin.monitorauthorizationuser.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 用户资产授权申请创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MonitorAuthorizationUserCreateReqVO extends MonitorAuthorizationUserBaseVO {

}
