package cn.iocoder.zj.module.om.service.monitorauthorization;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.util.*;

import cn.iocoder.zj.module.om.controller.admin.monitorauthorization.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.monitorauthorization.MonitorAuthorizationDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.om.convert.monitorauthorization.MonitorAuthorizationConvert;
import cn.iocoder.zj.module.om.dal.mysql.monitorauthorization.MonitorAuthorizationMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.om.enums.ErrorCodeConstants.*;

/**
 * 监控申请授权 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MonitorAuthorizationServiceImpl implements MonitorAuthorizationService {

    @Resource
    private MonitorAuthorizationMapper monitorAuthorizationMapper;

    @Override
    public Long createMonitorAuthorization(MonitorAuthorizationCreateReqVO createReqVO) {
        // 插入
        MonitorAuthorizationDO monitorAuthorization = MonitorAuthorizationConvert.INSTANCE.convert(createReqVO);
        monitorAuthorizationMapper.insert(monitorAuthorization);
        // 返回
        return monitorAuthorization.getId();
    }

    @Override
    public void updateMonitorAuthorization(MonitorAuthorizationUpdateReqVO updateReqVO) {
        // 校验存在
        validateMonitorAuthorizationExists(updateReqVO.getId());
        // 更新
        MonitorAuthorizationDO updateObj = MonitorAuthorizationConvert.INSTANCE.convert(updateReqVO);
        monitorAuthorizationMapper.updateById(updateObj);
    }

    @Override
    public void deleteMonitorAuthorization(Long id) {
        // 校验存在
        validateMonitorAuthorizationExists(id);
        // 删除
        monitorAuthorizationMapper.deleteById(id);
    }

    private void validateMonitorAuthorizationExists(Long id) {
        if (monitorAuthorizationMapper.selectById(id) == null) {
            throw exception(MONITOR_AUTHORIZATION_NOT_EXISTS);
        }
    }

    @Override
    public MonitorAuthorizationDO getMonitorAuthorization(Long id) {
        return monitorAuthorizationMapper.selectByIdInfo(id);
    }

    @Override
    public List<MonitorAuthorizationDO> getMonitorAuthorizationList(Collection<Long> ids) {
        return monitorAuthorizationMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<MonitorAuthorizationDO> getMonitorAuthorizationPage(MonitorAuthorizationPageReqVO pageReqVO) {
        return monitorAuthorizationMapper.selectPage(pageReqVO);
    }

    @Override
    public List<MonitorAuthorizationDO> getMonitorAuthorizationList(MonitorAuthorizationExportReqVO exportReqVO) {
        return monitorAuthorizationMapper.selectList(exportReqVO);
    }

    @Override
    public void deleteMonitorAuthorizationInfo(Long id) {
        monitorAuthorizationMapper.deleteMonitorAuthorizationInfo(id);
    }

    @Override
    public void updateMonitorAuthorizationInfo(Long id,String authorizationType) {
        monitorAuthorizationMapper.updateMonitorAuthorizationInfo(id,authorizationType);
    }

}
