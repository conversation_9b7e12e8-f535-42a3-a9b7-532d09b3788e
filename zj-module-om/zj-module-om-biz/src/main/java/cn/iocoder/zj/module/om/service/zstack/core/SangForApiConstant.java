package cn.iocoder.zj.module.om.service.zstack.core;

public class SangForApiConstant {
    public static final String SANG_FOR_API_PREFIX = "/vapi/json";
    public static final String LOGIN = SANG_FOR_API_PREFIX + "/access/ticket";
    public static final String GET_PUBLIC_KEY = SANG_FOR_API_PREFIX + "/public_key";
    public static final String DELETE_VM = SANG_FOR_API_PREFIX + "/cluster/vm/{vmid}/delete";
    public static final String SWITCH_STATE_VM = SANG_FOR_API_PREFIX + "/cluster/vm/{vmid}/status";
    //获取操作任务日志
    public static final String PROCESS_STATE = SANG_FOR_API_PREFIX + "/log/process";
    //获取可供创建虚拟机时选择的存储列表
    public static final String VTPSTORAGE_LIST = SANG_FOR_API_PREFIX + "/log/process";
    public static final String HOST_CHOOSE ="/vapi/extjs/vtpstorage/access_nodes?storage={storageId}";
    public static final String MAC_ADDRESS ="/vapi/extjs/cluster/gen_mac_addr";
    public static final String NET_INFO =SANG_FOR_API_PREFIX+"/hci/sdn/ui/network-portal/evswitches/";
    public static final String CREATE_VM =SANG_FOR_API_PREFIX+"/nodes/{nodeid}/qemu/create";
    public static final String HARDWARE_MAINTAIN = "/nodes/{node}";
    public static final String VM_INFO = SANG_FOR_API_PREFIX+"/cluster/vm/{vmid}/info";
    public static final String VM_MIRRO = "/vapi/extjs/vtpstorage/load_all_iso";
    public static final String VNC_PROXY = SANG_FOR_API_PREFIX + "/cluster/vm/{vmid}/vncproxy";
}
