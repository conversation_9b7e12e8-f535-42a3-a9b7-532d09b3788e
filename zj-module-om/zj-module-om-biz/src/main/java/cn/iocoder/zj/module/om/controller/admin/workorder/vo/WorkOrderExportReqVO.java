package cn.iocoder.zj.module.om.controller.admin.workorder.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 工单管理数据 Excel 导出 Request VO，参数和 WorkOrderPageReqVO 是一致的")
@Data
public class WorkOrderExportReqVO {

    @Schema(description = "名称")
    private String name;

    @Schema(description = "项目名称（平台资源）")
    private String sourceName;

    @Schema(description = "项目ID（ziyuanId）")
    private String sourceUuid;

    @Schema(description = "简介")
    private String description;

    @Schema(description = "事件")
    private String event;

    @Schema(description = "处理人ID")
    private Long auditorId;

    @Schema(description = "处理人名称")
    private String auditorName;

    @Schema(description = "实施人ID")
    private Long enforcerId;

    @Schema(description = "实施人名称")
    private String enforcerName;

    @Schema(description = "审批状态，consent同意，reject驳回")
    private String ratifyStatus;

    @Schema(description = "准备状态，valid有效的，invalid无效")
    private String readinessStatus;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] createTime;

    @Schema(description = "平台id集合，不传")
    private List platformIds;

    @Schema(description = "创建者Id")
    private Long creator;

    @Schema(description = "资源类型字典项code")
    private String dictCode;

    @Schema(description = "工单类型对应的字典编码")
    private String typeCode;

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String startTime;

    @Schema(description = "结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String endTime;

}
