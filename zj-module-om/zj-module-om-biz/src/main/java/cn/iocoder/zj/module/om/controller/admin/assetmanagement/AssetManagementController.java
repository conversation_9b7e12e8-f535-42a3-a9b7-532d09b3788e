package cn.iocoder.zj.module.om.controller.admin.assetmanagement;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.om.api.asset.dto.VncInfoDTO;
import cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo.*;
import cn.iocoder.zj.module.om.framework.websocket.WebSocketClient;
import cn.iocoder.zj.module.om.service.assetmanagement.AssetManagementService;
import cn.iocoder.zj.module.om.service.cloud.ISangForCloudService;
import cn.iocoder.zj.module.om.service.cloud.IVmwareService;
import cn.iocoder.zj.module.om.service.cloud.IWinHongCloudService;
import cn.iocoder.zj.module.om.service.cloud.IZstackCloudService;
import cn.iocoder.zj.module.om.service.hardware.IZstackHardwareService;
import cn.iocoder.zj.module.om.service.storage.IZstackStorageService;
import cn.iocoder.zj.module.om.service.workorder.WorkOrderService;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.CREATE;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.UPDATE;

@Tag(name = "管理后台 - 资产管理")
@RestController
@RequestMapping("/om/assetManagement")
@Validated
public class AssetManagementController {
    @Resource
    AssetManagementService assetManagementService;
    @Resource
    IZstackCloudService iZstackCloudService;
    @Resource
    IVmwareService iVmwareService;
    @Resource
    IZstackHardwareService iZstackHardwareService;
    @Resource
    IZstackStorageService iZstackStorageService;
    @Resource
    WorkOrderService workOrderService;
    @Resource
    private PlatformconfigApi platformconfigApi;
    @Resource
    private ISangForCloudService iSangForCloudService;
    @Resource
    private IWinHongCloudService iWinHongCloudService;

    @Resource
    private WebSocketClient webSocketClient;

    @GetMapping("/list")
    @Operation(summary = "左侧树列表")
    @TenantIgnore
    @Parameter(name = "tenantId", description = "租户ID", required = true)
    public CommonResult<List<Map<String,Object>>> createSchedulingInfo(@RequestParam("tenantId")String tenantId) {
        return CommonResult.success(assetManagementService.list(tenantId));
    }
    @GetMapping("/assetInfo")
    @Operation(summary = "资产信息")
    @TenantIgnore
    @Parameter(name = "platformId", description = "平台ID", required = true)
    public CommonResult<Map<String,Object>> getAssetInfo(@RequestParam("platformId")String platformId) {
        return CommonResult.success(assetManagementService.getAssetInfo(platformId));
    }
    @GetMapping("/hardwareInfo")
    @Operation(summary = "硬件信息信息")
    @TenantIgnore
    @Parameter(name = "platformId", description = "平台ID", required = true)
    public CommonResult<Map<String,Object>> getHardwareInfo(@RequestParam("platformId")String platformId) {
        return CommonResult.success(assetManagementService.hardwareInfo(platformId));
    }
    @GetMapping("/getVmClusters")
    @Operation(summary = "获取集群分页列表")
    @TenantIgnore
    @Parameter(name = "platformId", description = "平台ID", required = true)
    @Parameter(name = "pageNo", description = "页码", required = false)
    @Parameter(name = "pageSize", description = "每页条数", required = false)
    @Parameter(name = "queryData", description = "按名称查询", required = false)
    public CommonResult<Map<String,Object>> getVmClusters(@RequestParam("platformId")Long platformId,
                                                          Integer pageNo,
                                                          Integer pageSize,
                                                          String queryData) {
        return CommonResult.success(iZstackCloudService.getVmClusters(platformId,pageNo,pageSize,queryData));
    }
    @GetMapping("/getImages")
    @Operation(summary = "获取镜像分页列表")
    @TenantIgnore
    @Parameter(name = "platformId", description = "平台ID", required = true)
    @Parameter(name = "pageNo", description = "页码,页码与条数为空时返回所有集群信息", required = true)
    @Parameter(name = "pageSize", description = "每页条数", required = true)

    public CommonResult<Map<String,Object>> getImages(@RequestParam("platformId")Long platformId,
                                                      @RequestParam("pageNo")Integer pageNo,
                                                      @RequestParam("pageSize")Integer pageSize,
                                                      @RequestParam("queryData")String queryData) {
        return CommonResult.success(iZstackCloudService.getImages(platformId,pageNo,pageSize,queryData));
    }
    @GetMapping("/getDiskOfferings")
    @Operation(summary = "获取根云盘规格分页列表")
    @TenantIgnore
    @Parameter(name = "platformId", description = "平台ID", required = true)
    @Parameter(name = "pageNo", description = "页码", required = true)
    @Parameter(name = "pageSize", description = "每页条数", required = true)
    @Parameter(name = "diskSize", description = "镜像大小", required = false)
    public CommonResult<Map<String,Object>> getDiskOfferings(@RequestParam("platformId")Long platformId,
                                                 @RequestParam("pageNo")Integer pageNo,
                                                 @RequestParam("pageSize")Integer pageSize,
                                                 @RequestParam("queryData")String queryData,
                                                 Long diskSize) {
        return CommonResult.success(iZstackCloudService.getDiskOfferings(platformId,pageNo,pageSize,queryData,diskSize));
    }
    @GetMapping("/getInstanceOfferings")
    @Operation(summary = "获取云主机规格分页列表")
    @TenantIgnore
    @Parameter(name = "platformId", description = "平台ID", required = true)
    @Parameter(name = "pageNo", description = "页码", required = true)
    @Parameter(name = "pageSize", description = "每页条数", required = true)

    public CommonResult<Map<String,Object>> getInstanceOfferings(@RequestParam("platformId")Long platformId,
                                                             @RequestParam("pageNo")Integer pageNo,
                                                             @RequestParam("pageSize")Integer pageSize,
                                                             @RequestParam("queryData")String queryData) {
        return CommonResult.success(iZstackCloudService.getInstanceOfferings(platformId,pageNo,pageSize,queryData));
    }
    @PostMapping("/createHostToZstack")
    @Operation(summary = "远端创建云主机")
    @PreAuthorize("@ss.hasPermission('om:asset-management:createHost')")
    @TenantIgnore
    @OperateLog(type = CREATE)
    public CommonResult<Map<String,String>> createHostToZstack(@Valid @RequestBody ZstackCreateHostReqVo reqVo) {
        CommonResult<Map<String,String>> result = iZstackCloudService.createHostToZstack(reqVo);
        PlatformconfigDTO platformconfigDTO = platformconfigApi.getByConfigId(reqVo.getPlatformId()).getData();
        switch (result.getData().get("success")) {
            case "true":
                assetManagementService.createOperateLog(reqVo.getName(), result.getData().get("uuid"), "创建云主机", "success", "host", "", platformconfigDTO);
                break;
            case "false":
                assetManagementService.createOperateLog(reqVo.getName(),"", "创建云主机", "error", "host", result.getData().get("msg"), platformconfigDTO);
        }
        return result;
    }
    @PostMapping("/createHardwareToZtack")
    @Operation(summary = "远端创建宿主机")
    @PreAuthorize("@ss.hasPermission('om:asset-management:createHaradware')")
    @TenantIgnore
    @OperateLog(type = CREATE)
    public CommonResult<Map<String,String>> createHardwareToZstack(@Valid @RequestBody ZstackCreateHardwareReqVo reqVo) {
        CommonResult<Map<String,String>> result = iZstackHardwareService.createHardwareToZstack(reqVo);
        PlatformconfigDTO platformconfigDTO = platformconfigApi.getByConfigId(reqVo.getPlatformId()).getData();
        switch (result.getData().get("success")) {
            case "true":
                assetManagementService.createOperateLog(reqVo.getName(),"", "创建宿主机", "success", "host", "", platformconfigDTO);
                break;
            case "false":
                assetManagementService.createOperateLog(reqVo.getName(),"", "创建宿主机", "error", "host", result.getData().get("msg"), platformconfigDTO);
        }
        return result;
    }
    @PutMapping("/vmInstanceStart")
    @Operation(summary = "云主机开机")
    @TenantIgnore
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('om:asset-management:startHost')")
    @Parameter(name = "platformId", description = "平台ID", required = true)
    @Parameter(name = "uuid", description = "云主机uuid(vmware平台传vms的值)", required = true)
    @Parameter(name = "hostName", description = "云主机名称", required = true)
    @Parameter(name = "vms", description = "vmware虚拟机vms参数,vmware设备需传递",required = false)
    public CommonResult<Map<String,String>> startVmInstance(@RequestParam("platformId")Long platformId,
                                                            @RequestParam("vms")String vms,
                                                            @RequestParam("uuid")String uuid,
                                                            @RequestParam("hostName")String hostName) {

        return  assetManagementService.operateVmInstance(platformId,vms,uuid,"","start",hostName);
    }
    @PutMapping("/vmInstanceReboot")
    @Operation(summary = "云主机重启")
    @TenantIgnore
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('om:asset-management:rebootHost')")
    @Parameter(name = "platformId", description = "平台ID", required = true)
    @Parameter(name = "uuid", description = "云主机uuid", required = true)
    @Parameter(name = "hostName", description = "云主机名称", required = true)
    @Parameter(name = "vms", description = "vmware虚拟机vms参数,vmware设备需传递",required = false)
    public CommonResult<Map<String,String>> rebootVmInstance(@RequestParam("platformId")Long platformId,
                                                            @RequestParam("vms")String vms,
                                                            @RequestParam("uuid")String uuid,
                                                            @RequestParam("hostName")String hostName) {

        return  assetManagementService.operateVmInstance(platformId,vms,uuid,"","reboot",hostName);
    }
    @PutMapping("/vmInstanceGrace")
    @Operation(summary = "停止虚拟机")
    @TenantIgnore
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('om:asset-management:graceHost')")
    @Parameter(name = "platformId", description = "平台ID", required = true)
    @Parameter(name = "uuid", description = "云主机uuid", required = true)
    @Parameter(name = "hostName", description = "云主机名称", required = true)
    @Parameter(name = "vms", description = "vmware虚拟机vms参数,vmware设备需传递",required = false)
    public CommonResult<Map<String,String>> graceVmInstance(@RequestParam("platformId")Long platformId,
                                                            @RequestParam("vms")String vms,
                                                            @RequestParam("uuid")String uuid,
                                                            @RequestParam("hostName")String hostName) {

        return  assetManagementService.operateVmInstance(platformId,vms,uuid,"grace","stop",hostName);
    }
    @PutMapping("/vmInstanceCold")
    @Operation(summary = "关闭操作系统")
    @TenantIgnore
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('om:asset-management:coldHost')")
    @Parameter(name = "platformId", description = "平台ID", required = true)
    @Parameter(name = "uuid", description = "云主机uuid", required = true)
    @Parameter(name = "hostName", description = "云主机名称", required = true)
    @Parameter(name = "vms", description = "vmware虚拟机vms参数,vmware设备需传递",required = false)
    public CommonResult<Map<String,String>> coldVmInstance(@RequestParam("platformId")Long platformId,
                                                            @RequestParam(value = "vms",required = false)String vms,
                                                            @RequestParam("uuid")String uuid,
                                                            @RequestParam("hostName")String hostName) {
        return  assetManagementService.operateVmInstance(platformId,vms,uuid,"cold","stop",hostName);
    }
    @PutMapping("/vmInstanceDestroy")
    @Operation(summary = "虚拟机回收")
    @TenantIgnore
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('om:asset-management:destroyHost')")
    @Parameter(name = "platformId", description = "平台ID", required = true)
    @Parameter(name = "uuid", description = "云主机uuid", required = true)
    @Parameter(name = "hostName", description = "云主机名称", required = true)
    @Parameter(name = "vms", description = "vmware虚拟机vms参数,vmware设备需传递",required = false)
    public CommonResult<Map<String,String>> destroyVmInstance(@RequestParam("platformId")Long platformId,
                                                            @RequestParam("vms")String vms,
                                                            @RequestParam("uuid")String uuid,
                                                            @RequestParam("hostName")String hostName) {

        return  assetManagementService.operateVmInstance(platformId,vms,uuid,"","destroy",hostName);
    }
    @PutMapping("/reconnectHardware")
    @Operation(summary = "重连宿主机")
    @TenantIgnore
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('om:asset-management:reconnectHardware')")
    @Parameter(name = "platformId", description = "平台ID", required = true)
    @Parameter(name = "uuid", description = "宿主机uuid(vmware平台传vms的值)", required = true)
    @Parameter(name = "hardwareName", description = "宿主机名称",required = true)
    @Parameter(name = "vms", description = "vmware设备vms参数,vmware设备需传递",required = false)
    public CommonResult<Map<String,String>> reconnectHardware(@RequestParam("platformId") Long platformId,
                                                            @RequestParam("vms") String vms,
                                                            @RequestParam("uuid") String uuid,
                                                            @RequestParam("hardwareName") String hardwareName) {
        return assetManagementService.operateHardware(platformId,vms,uuid,"","reconnect",hardwareName);
    }
    @PutMapping("/enableHardware")
    @Operation(summary = "启用宿主机")
    @TenantIgnore
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('om:asset-management:enableHardware')")
    @Parameter(name = "platformId", description = "平台ID", required = true)
    @Parameter(name = "uuid", description = "宿主机uuid(vmware平台传vms的值)", required = true)
    @Parameter(name = "hardwareName", description = "宿主机名称",required = true)
    @Parameter(name = "vms", description = "vmware设备vms参数,vmware设备需传递",required = false)
    public CommonResult<Map<String,String>> enableHardware(@RequestParam("platformId") Long platformId,
                                                              @RequestParam("vms") String vms,
                                                              @RequestParam("uuid") String uuid,
                                                              @RequestParam("hardwareName") String hardwareName) {
        return assetManagementService.operateHardware(platformId,vms,uuid,"enable","stateChange",hardwareName);
    }
    @PutMapping("/disableHardware")
    @Operation(summary = "禁用宿主机")
    @TenantIgnore
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('om:asset-management:disableHardware')")
    @Parameter(name = "platformId", description = "平台ID", required = true)
    @Parameter(name = "uuid", description = "宿主机uuid(vmware平台传vms的值)", required = true)
    @Parameter(name = "hardwareName", description = "宿主机名称",required = true)
    @Parameter(name = "vms", description = "vmware设备vms参数,vmware设备需传递",required = false)
    public CommonResult<Map<String,String>> disableHardware(@RequestParam("platformId") Long platformId,
                                                              @RequestParam("vms") String vms,
                                                              @RequestParam("uuid") String uuid,
                                                              @RequestParam("hardwareName") String hardwareName) {
        return assetManagementService.operateHardware(platformId,vms,uuid,"disable","stateChange",hardwareName);
    }
    @PutMapping("/maintainHardware")
    @Operation(summary = "进入维护")
    @TenantIgnore
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('om:asset-management:maintainHardware')")
    @Parameter(name = "platformId", description = "平台ID", required = true)
    @Parameter(name = "uuid", description = "宿主机uuid(vmware平台传vms的值)", required = true)
    @Parameter(name = "hardwareName", description = "宿主机名称",required = true)
    @Parameter(name = "vms", description = "vmware设备vms参数,vmware设备需传递",required = false)
    public CommonResult<Map<String,String>> maintainHardware(@RequestParam("platformId") Long platformId,
                                                              @RequestParam("vms") String vms,
                                                              @RequestParam("uuid") String uuid,
                                                              @RequestParam("hardwareName") String hardwareName) {
        return assetManagementService.operateHardware(platformId,vms,uuid,"maintain","stateChange",hardwareName);
    }
    @PutMapping("/extMaintainHardware")
    @Operation(summary = "退出维护")
    @TenantIgnore
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('om:asset-management:extMaintainHardware')")
    @Parameter(name = "platformId", description = "平台ID", required = true)
    @Parameter(name = "uuid", description = "宿主机uuid(vmware平台传vms的值)", required = true)
    @Parameter(name = "hardwareName", description = "宿主机名称",required = true)
    @Parameter(name = "vms", description = "vmware设备vms参数,vmware设备需传递",required = false)
    public CommonResult<Map<String,String>> extMaintainHardware(@RequestParam("platformId") Long platformId,
                                                              @RequestParam("vms") String vms,
                                                              @RequestParam("uuid") String uuid,
                                                              @RequestParam("hardwareName") String hardwareName) {
        return assetManagementService.operateHardware(platformId,vms,uuid,"ext_maintain","stateChange",hardwareName);
    }
    @PutMapping("/operateStorage")
    @Operation(summary = "操作主存储")
    @TenantIgnore
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('om:asset-management:operateStorage')")
    @Parameter(name = "platformId", description = "平台ID", required = true)
    @Parameter(name = "uuid", description = "主存储uuid", required = true)
    @Parameter(name = "state", description = "转换主存储状态时必传，宿主机状态：enable启用，disable停用，maintain维护")
    @Parameter(name = "actions", description = "操作类型：reconnect重连，stateChange转换宿主机状态",required = true)
    @Parameter(name = "storageName", description = "主存储名称", required = true)
    public CommonResult<Map<String,String>> operateStorage(@RequestParam("platformId")Long platformId,
                                                @RequestParam("uuid")String uuid,
                                                @RequestParam("state")String state,
                                                @RequestParam("actions")String actions,
                                                @RequestParam("storageName")String storageName) {
        Map<String,String> resultMap = new HashMap<>();
        resultMap.put("success","true");
        resultMap.put("msg","");
        if(workOrderService.getWorkerOrderCountBySourceUuid(uuid,state,actions)>0){
            resultMap.put("success","false");
            resultMap.put("msg","此资源已存在相同操作的待审核工单，请勿重复操作。");
            return CommonResult.success(resultMap);
        }
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        return iZstackStorageService.operateStorage(uuid,platformId,actions,state);
    }
    @GetMapping("/getDatacenters")
    @Operation(summary = "获取vmware数据中心下拉框列表")
    @TenantIgnore
    public CommonResult<List<Map>> getDatacenters(@RequestParam("platformId")Long platformId) {
        return CommonResult.success(iVmwareService.getDatacenters(platformId));
    }
    @GetMapping("/getFolders")
    @Operation(summary = "获取vmware文件夹下拉框列表")
    @TenantIgnore
    public CommonResult<List<Map>> getFoldersByDatacenter(@RequestParam("platformId")Long platformId,
                                                          @RequestParam("datacenter")String datacenter) {
        return CommonResult.success(iVmwareService.getFoldersByDatacenter(platformId,datacenter));
    }

    @GetMapping("/getClusters")
    @Operation(summary = "获取vmware集群下拉框列表")
    @TenantIgnore
    public CommonResult<List<Map>> getClustersByDatacenter(@RequestParam("platformId")Long platformId,
                                                          @RequestParam("datacenter")String datacenter) {
        return CommonResult.success(iVmwareService.getClustersByDatacenter(platformId,datacenter));
    }
    @GetMapping("/getNetworks")
    @Operation(summary = "获取vmware网络下拉框列表")
    @TenantIgnore
    public CommonResult<List<Map>> getNetworksByDatacenter(@RequestParam("platformId")Long platformId,
                                                           @RequestParam("datacenter")String datacenter,
                                                           @RequestParam("networkType")String networkType) {
        return CommonResult.success(iVmwareService.getNetworks(platformId,datacenter,networkType));
    }
    @GetMapping("/getDatastores")
    @Operation(summary = "获取vmware数据存储下拉框列表")
    @TenantIgnore
    public CommonResult<List<Map>> getDatastoresByDatacenter(@RequestParam("platformId")Long platformId,
                                                             @RequestParam("datacenter")String datacenter) {
        return CommonResult.success(iVmwareService.getDatastoresByDatacenter(platformId,datacenter));
    }

    @PostMapping("/createVmwareVm")
    @Operation(summary = "创建vmware虚拟机")
    @PreAuthorize("@ss.hasPermission('om:asset-management:createHost')")
    @TenantIgnore
    @OperateLog(type = CREATE)
    public CommonResult<Map<String,String>> createVmwareVm(@Valid @RequestBody VmwareCreateVmReqVo reqVo) {
        PlatformconfigDTO platformconfigDTO = platformconfigApi.getByConfigId(reqVo.getPlatformId()).getData();
        Map<String,String> result = iVmwareService.createVmwareVm(reqVo);
        switch (result.get("success")) {
            case "true":
                assetManagementService.createOperateLog(reqVo.getName(), result.get("uuid"), "创建云主机", "success", "host", "", platformconfigDTO);
                break;
            case "false":
                assetManagementService.createOperateLog(reqVo.getName(),"", "创建云主机", "error", "host", result.get("msg"), platformconfigDTO);
        }
        return CommonResult.success(result);
    }

    @PostMapping("/createWinHongVm")
    @Operation(summary = "创建云宏虚拟机")
    @PreAuthorize("@ss.hasPermission('om:asset-management:createHost')")
    @TenantIgnore
    @OperateLog(type = CREATE)
    public CommonResult<Map<String,String>> createWinHongVm(@Valid @RequestBody WinHongCreateHostReqVo reqVo) {
        PlatformconfigDTO platformconfigDTO = platformconfigApi.getByConfigId(reqVo.getPlatformId()).getData();
        Map<String,String> result = iWinHongCloudService.createWinHongVm(reqVo);
        return CommonResult.success(result);
    }

    @PostMapping("/createSangForVm")
    @Operation(summary = "创建深信服虚拟机")
    @PreAuthorize("@ss.hasPermission('om:asset-management:createHost')")
    @TenantIgnore
    @OperateLog(type = CREATE)
    public CommonResult<Map<String,String>> createSangForVm(@Valid @RequestBody SangForCreateVmReqVO reqVo) {
        PlatformconfigDTO platformconfigDTO = platformconfigApi.getByConfigId(reqVo.getPlatformId()).getData();
        Map<String,String> result = iSangForCloudService.createSangForVm(reqVo);
        return CommonResult.success(result);
    }
    @GetMapping("/getVmMirror")
    @Operation(summary = "获取深信服虚拟机镜像")
    @TenantIgnore
    @OperateLog(type = CREATE)
    public CommonResult<JSONArray> getVmMirror(@RequestParam("platformId") Long platformId,
                                                        @RequestParam("uuid")String uuid) {
        JSONArray mirrors = assetManagementService.getVmMirror(platformId,uuid);
        return CommonResult.success(mirrors);
    }
    @GetMapping("/getOperateLogPage")
    @Operation(summary = "获得云资源操作日志分页")
    @TenantIgnore
    public CommonResult<PageResult<ResourceOperateLog>> getWorkOrderMyPage(@Valid OperateLogPageReqVO pageVO) {
        return  CommonResult.success(assetManagementService.getOperateLogPage(pageVO));
    }

    @PutMapping("/closeClient")
    @Operation(summary = "关闭客户端的sse连接")
    @TenantIgnore
    public CommonResult<Boolean> closeClient( @RequestParam("userId")Long userId) {
        webSocketClient.onClose(userId);
        return  CommonResult.success(true);
    }



    @GetMapping("/getVncInfo")
    @Operation(summary = "获取云主机控制台链接")
    @TenantIgnore
    public CommonResult<VncInfoDTO> getVncInfo(@RequestParam("uuid")String uuid) {
        return CommonResult.success(assetManagementService.getVncInfo(uuid));
    }
}
