package cn.iocoder.zj.module.om.controller.admin.userbind;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.om.controller.admin.userbind.vo.*;
import cn.iocoder.zj.module.om.convert.userbind.UserBindConvert;
import cn.iocoder.zj.module.om.dal.dataobject.userbind.UserBindDO;
import cn.iocoder.zj.module.om.service.userbind.UserBindService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

@Tag(name = "管理后台 - 用户推送绑定")
@RestController
@RequestMapping("/om/user-bind")
@Validated
public class UserBindController {

    @Resource
    private UserBindService userBindService;

    @PostMapping("/create")
    @Operation(summary = "创建用户推送绑定")
    @PreAuthorize("@ss.hasPermission('om:user-bind:create')")
    public CommonResult<Long> createUserBind(@Valid @RequestBody UserBindCreateReqVO createReqVO) {
        return success(userBindService.createUserBind(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新用户推送绑定")
    @PreAuthorize("@ss.hasPermission('om:user-bind:update')")
    public CommonResult<Boolean> updateUserBind(@Valid @RequestBody UserBindUpdateReqVO updateReqVO) {
        userBindService.updateUserBind(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除用户推送绑定")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('om:user-bind:delete')")
    public CommonResult<Boolean> deleteUserBind(@RequestParam("id") Long id) {
        userBindService.deleteUserBind(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得用户推送绑定")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('om:user-bind:query')")
    public CommonResult<UserBindRespVO> getUserBind(@RequestParam("id") Long id) {
        UserBindDO userBind = userBindService.getUserBind(id);
        return success(UserBindConvert.INSTANCE.convert(userBind));
    }

    @GetMapping("/list")
    @Operation(summary = "获得用户推送绑定列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('om:user-bind:query')")
    public CommonResult<List<UserBindRespVO>> getUserBindList(@RequestParam("ids") Collection<Long> ids) {
        List<UserBindDO> list = userBindService.getUserBindList(ids);
        return success(UserBindConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得用户推送绑定分页")
    @PreAuthorize("@ss.hasPermission('om:user-bind:query')")
    public CommonResult<PageResult<UserBindRespVO>> getUserBindPage(@Valid UserBindPageReqVO pageVO) {
        PageResult<UserBindDO> pageResult = userBindService.getUserBindPage(pageVO);
        return success(UserBindConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出用户推送绑定 Excel")
    @PreAuthorize("@ss.hasPermission('om:user-bind:export')")
    @OperateLog(type = EXPORT)
    public void exportUserBindExcel(@Valid UserBindExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<UserBindDO> list = userBindService.getUserBindList(exportReqVO);
        // 导出 Excel
        List<UserBindExcelVO> datas = UserBindConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "用户推送绑定.xls", "数据", UserBindExcelVO.class, datas);
    }

    @GetMapping("/getbyuserid")
    @Operation(summary = "获得用户推送绑定")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PermitAll
    @TenantIgnore
    public CommonResult<UserBindRespVO> getByUserId(@RequestParam("userId") Long userId) {
        UserBindDO userBind = userBindService.getByUserId(userId);
        UserBindRespVO convert = BeanUtil.copyProperties(userBind, UserBindRespVO.class);
        return success(convert);
    }
}
