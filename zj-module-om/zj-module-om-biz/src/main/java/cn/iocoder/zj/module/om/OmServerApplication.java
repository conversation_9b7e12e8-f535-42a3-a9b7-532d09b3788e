package cn.iocoder.zj.module.om;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.web.socket.config.annotation.EnableWebSocket;

@EnableWebSocket
@SpringBootApplication
@EnableJpaAuditing
@EnableJpaRepositories(basePackages = {"cn.iocoder.cloud"})
@EntityScan(basePackages = {"cn.iocoder.cloud", "org.apache.hertzbeat"})
public class OmServerApplication {
    public static void main(String[] args) {
        SpringApplication.run(OmServerApplication.class, args);
    }
}