package cn.iocoder.zj.module.om.controller.admin.workorder;

import cn.iocoder.zj.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.om.controller.admin.workorder.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.workorder.WorkOrderDO;
import cn.iocoder.zj.module.om.convert.workorder.WorkOrderConvert;
import cn.iocoder.zj.module.om.service.workorder.WorkOrderService;

@Tag(name = "管理后台 - 工单管理数据")
@RestController
@RequestMapping("/om/work-order")
@Validated
public class WorkOrderController {

    @Resource
    private WorkOrderService workOrderService;

    @PostMapping("/create")
    @Operation(summary = "创建工单管理数据")
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('om:work-order:create')")
    @TenantIgnore
    public CommonResult<Long> createWorkOrder(@Valid @RequestBody WorkOrderCreateReqVO createReqVO) {
        createReqVO.setCreateType("hand");
        return success(workOrderService.createWorkOrder(createReqVO));
    }


    @GetMapping("/listOnDay")
    @Operation(summary = "获得当天工单管理数据列表")
    @Parameter(name = "day", description = "当天日期", required = true, example = "2023-08-01")
    @TenantIgnore
    public CommonResult<PageResult<WorkOrderRespVO>> listOnDay(String day,WorkOrderPageReqVO pageVO) {
        PageResult<WorkOrderRespVO> list = workOrderService.listOnDay(day,pageVO);
        return success(list);
    }

    @PutMapping("/update")
    @Operation(summary = "更新工单管理数据")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('om:work-order:update')")
    @TenantIgnore
    public CommonResult<Boolean> updateWorkOrder(@Valid @RequestBody WorkOrderUpdateMyReqVO updateReqVO) {
        workOrderService.updateWorkOrder(updateReqVO);
        return success(true);
    }

    @PutMapping("/ratify")
    @Operation(summary = "工单审核")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('om:work-order:ratify')")
    @TenantIgnore
    public CommonResult<Boolean> ratifyWorkOrder(@RequestBody OrderRatifyReqVO reqVo) {
        //如果是自动创建的对云主机等操作的工单，则审批通过后会执行相应的操作，否则只更新工单状态
        workOrderService.ratifyWorkOrder(reqVo);
        return success(true);
    }
    @DeleteMapping("/delete")
    @Operation(summary = "删除工单管理数据")
    @OperateLog(type = DELETE)
    @PreAuthorize("@ss.hasPermission('om:work-order:delete')")
    @Parameter(name = "id", description = "编号", required = true)
    @TenantIgnore
    public CommonResult<Boolean> deleteWorkOrder(@RequestParam("id") Long id) {
        workOrderService.deleteWorkOrder(id);
        return success(true);
    }

    @PutMapping("/finish")
    @Operation(summary = "完成工单")
    @PreAuthorize("@ss.hasPermission('om:work-order:finish')")
    @OperateLog(type = UPDATE)
    @Parameter(name = "id", description = "工单ID", required = true)
    @TenantIgnore
    public CommonResult<Boolean> finishWorkOrder(@RequestParam("id") Long id) {
        workOrderService.finishWorkOrder(id);
        return success(true);
    }
    @GetMapping("/get")
    @Operation(summary = "获得工单管理数据")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @TenantIgnore
    public CommonResult<WorkOrderRespVO> getWorkOrder(@RequestParam("id") Long id) {
        WorkOrderRespVO workOrder = WorkOrderConvert.INSTANCE.convert(workOrderService.getWorkOrder(id));
        String rejectReason = workOrderService.getLatestRejectReason(id);
        workOrder.setRejectReason(rejectReason);
        List<OrderProcessBaseVo> orderProcessBaseVos = workOrderService.getOrderProcessList(id);
        workOrder.setOrderProcessList(orderProcessBaseVos);
        OrderProcessBaseVo orderProcessRatify = workOrderService.getOrderProcessRatify(id);
        workOrder.setOrderProcessRatify(orderProcessRatify);
        OrderProcessBaseVo orderProcessSolved = workOrderService.getOrderProcessSolved(id);
        workOrder.setOrderProcessSolved(orderProcessSolved);
        return success(workOrder);
    }

    @GetMapping("/list")
    @Operation(summary = "获得工单管理数据列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @TenantIgnore
    public CommonResult<List<WorkOrderRespVO>> getWorkOrderList(@RequestParam("ids") Collection<Long> ids) {
        List<WorkOrderDO> list = workOrderService.getWorkOrderList(ids);
        return success(WorkOrderConvert.INSTANCE.convertList(list));
    }



    @GetMapping("/my/page")
    @Operation(summary = "获得工单管理数据分页 - 我的工单")
    @TenantIgnore
    public CommonResult<PageResult<WorkOrderRespVO>> getWorkOrderMyPage(@Valid WorkOrderPageReqVO pageVO) {
        PageResult<WorkOrderDO> pageResult = workOrderService.getWorkOrderMyPage(pageVO);
        return success(WorkOrderConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/getUnsolvedWorkOrder")
    @Operation(summary = "获得最近五条未处理的工单")
    @TenantIgnore
    public CommonResult<List<WorkOrderRespVO>> getUnsolvedWorkOrder() {
        return success(workOrderService.getUnsolvedWorkOrder());
    }


    @PutMapping("/updateIsRead")
    @Operation(summary = "修改是否已读")
    @PreAuthenticated
    @TenantIgnore
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateIsRead(@RequestParam("ids") Collection<Long> ids) {
        workOrderService.updateIsRead(ids);
        return success(true);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出工单管理数据 Excel")
    @OperateLog(type = EXPORT)
    @TenantIgnore
    public void exportWorkOrderExcel(@Valid WorkOrderExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<WorkOrderDO> list = workOrderService.getWorkOrderList(exportReqVO);
        // 导出 Excel
        List<WorkOrderExcelVO> datas = WorkOrderConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "工单管理数据.xls", "数据", WorkOrderExcelVO.class, datas);
    }

    @GetMapping("/workOrderStatistics")
    @Operation(summary = "运维看板-工单处理与审批统计")
    @TenantIgnore
    public CommonResult<Map<String,Object>> workOrderStatistics() {
        Map<String,Object> result = workOrderService.getWorkOrderStatistics();
        return success(result);
    }
    @GetMapping("/workOrderStatisticsInWeek")
    @Operation(summary = "运维看板-近5天工单处理统计")
    @TenantIgnore
    public CommonResult<List<Map<String, Object>> > workOrderStatisticsInWeek() {
        List<Map<String, Object>>  result = workOrderService.getWorkOrderStatisticsInWeek();
        return success(result);
    }
    @GetMapping("/workOrderSourceType")
    @Operation(summary = "运维看板-工单资源分布")
    @TenantIgnore
    public CommonResult<List<Map<String, String>> > getWorkOrderSourceType() {
        List<Map<String, String>>  result = workOrderService.getWorkOrderSourceType();
        return success(result);
    }
    @GetMapping("/getRanking")
    @Operation(summary = "运维看板—工单完成排行")
    @TenantIgnore
    public CommonResult<List<Map<String, Object>>> getRanking() {
        List<Map<String, Object>>  result = workOrderService.getRanking();
        return success(result);
    }

    @GetMapping("/createInWeek")
    @Operation(summary = "运维看板—近7天工单处理走势")
    @TenantIgnore
    public CommonResult<List<Map<String, String>>> getCreateInWeek() {
        List<Map<String, String>>  result = workOrderService.getCreateInWeek();
        return success(result);
    }

    @GetMapping("/getPersonWorkOrderInfo")
    @Operation(summary = "首页—个人工单情况统计")
    @TenantIgnore
    public CommonResult<Map<String,String>> workOrderStatistics(@RequestParam("userId") Long userId) {
        Map<String,String> result = workOrderService.getPersonWorkOrderInfo(userId);
        return success(result);
    }
}
