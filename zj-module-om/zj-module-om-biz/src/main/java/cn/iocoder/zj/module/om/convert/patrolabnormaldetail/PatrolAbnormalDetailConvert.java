package cn.iocoder.zj.module.om.convert.patrolabnormaldetail;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.om.controller.admin.patrolabnormaldetail.vo.PatrolAbnormalDetailCreateReqVO;
import cn.iocoder.zj.module.om.controller.admin.patrolabnormaldetail.vo.PatrolAbnormalDetailExcelVO;
import cn.iocoder.zj.module.om.controller.admin.patrolabnormaldetail.vo.PatrolAbnormalDetailRespVO;
import cn.iocoder.zj.module.om.controller.admin.patrolabnormaldetail.vo.PatrolAbnormalDetailUpdateReqVO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolabnormaldetail.PatrolAbnormalDetailDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 巡检异常明细 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface PatrolAbnormalDetailConvert {

    PatrolAbnormalDetailConvert INSTANCE = Mappers.getMapper(PatrolAbnormalDetailConvert.class);

    PatrolAbnormalDetailDO convert(PatrolAbnormalDetailCreateReqVO bean);

    PatrolAbnormalDetailDO convert(PatrolAbnormalDetailUpdateReqVO bean);

    PatrolAbnormalDetailRespVO convert(PatrolAbnormalDetailDO bean);

    List<PatrolAbnormalDetailRespVO> convertList(List<PatrolAbnormalDetailDO> list);

    PageResult<PatrolAbnormalDetailRespVO> convertPage(PageResult<PatrolAbnormalDetailDO> page);

    List<PatrolAbnormalDetailExcelVO> convertList02(List<PatrolAbnormalDetailDO> list);

}
