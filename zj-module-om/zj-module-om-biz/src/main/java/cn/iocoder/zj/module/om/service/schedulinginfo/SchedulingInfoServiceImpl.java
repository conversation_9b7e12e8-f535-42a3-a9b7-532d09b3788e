package cn.iocoder.zj.module.om.service.schedulinginfo;

import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Date;

import cn.iocoder.zj.module.om.controller.admin.schedulinginfo.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.schedulinginfo.SchedulingInfoDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.om.convert.schedulinginfo.SchedulingInfoConvert;
import cn.iocoder.zj.module.om.dal.mysql.schedulinginfo.SchedulingInfoMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.util.date.DateUtils.*;
import static cn.iocoder.zj.module.om.enums.ErrorCodeConstants.*;

/**
 * 运维排班信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SchedulingInfoServiceImpl implements SchedulingInfoService {

    @Resource
    private SchedulingInfoMapper schedulingInfoMapper;



    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

    @Override
    @TenantIgnore
    public Long createSchedulingInfo(SchedulingInfoCreateReqVO createReqVO) {
        // 插入
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Long tenantId = loginUser.getTenantId();
        createReqVO.setTenantId(tenantId);
        SchedulingInfoDO schedulingInfo = SchedulingInfoConvert.INSTANCE.convert(createReqVO);
        schedulingInfoMapper.insert(schedulingInfo);
        // 返回


        return schedulingInfo.getId();
    }

    @Override
    public void updateSchedulingInfo(SchedulingInfoUpdateReqVO updateReqVO) {
        // 校验存在
        validateSchedulingInfoExists(updateReqVO.getId());
        // 更新
        SchedulingInfoDO updateObj = SchedulingInfoConvert.INSTANCE.convert(updateReqVO);
        schedulingInfoMapper.updateById(updateObj);
    }

    @Override
    public void deleteSchedulingInfo(Long id) {
        // 校验存在
        validateSchedulingInfoExists(id);
        // 删除
        schedulingInfoMapper.deleteById(id);
    }

    private void validateSchedulingInfoExists(Long id) {
        if (schedulingInfoMapper.selectById(id) == null) {
            throw exception(SCHEDULING_INFO_NOT_EXISTS);
        }
    }

    @Override
    public SchedulingInfoDO getSchedulingInfo(Long id) {
        return schedulingInfoMapper.selectById(id);
    }

    @Override
    public List<SchedulingInfoDO> getSchedulingInfoList(Collection<Long> ids) {
        return schedulingInfoMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<SchedulingInfoDO> getSchedulingInfoPage(SchedulingInfoPageReqVO pageReqVO) {
        return schedulingInfoMapper.selectPage(pageReqVO);
    }

    @Override
    public List<SchedulingInfoDO> getSchedulingInfoList(SchedulingInfoExportReqVO exportReqVO) {
        return schedulingInfoMapper.selectList(exportReqVO);
    }


    @Override
    public List<SchedulingInfoRespVO> queryScheduling(Long startMillis,Long endMillis) {

        String start = getCurrentDate(startMillis);
        String end = getCurrentDate(endMillis);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Long tenantId = loginUser.getTenantId();
        List<SchedulingInfoDO> schedulingInfoDOS=schedulingInfoMapper.queryScheduling(start,end,tenantId);
        Date headDate = toDate(start);
        Date tailDate = toDate(end);

        for (SchedulingInfoDO schedulingInfoDO : schedulingInfoDOS) {
            //在之前c
            if (schedulingInfoDO.getStartTime().compareTo(headDate)<0){
                schedulingInfoDO.setStartTime(headDate);
            }
            if (schedulingInfoDO.getEndTime().compareTo(tailDate)>0){
                schedulingInfoDO.setEndTime(tailDate);
            }

        }
        List<SchedulingInfoRespVO> list = SchedulingInfoConvert.INSTANCE.convertList(schedulingInfoDOS);
        for (SchedulingInfoRespVO vo : list) {
            vo.setStart(dateFormatExample(vo.getStartTime()));
            vo.setEnd(dateFormatExample(vo.getEndTime()));
        }

        return list;
    }

    @Override
    public Long getSchedulCount(Long id, String name) {
        return schedulingInfoMapper.getSchedulCount(id,name);
    }


}
