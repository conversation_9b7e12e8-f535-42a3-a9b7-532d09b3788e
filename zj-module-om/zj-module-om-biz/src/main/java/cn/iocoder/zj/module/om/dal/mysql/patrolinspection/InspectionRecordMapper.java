package cn.iocoder.zj.module.om.dal.mysql.patrolinspection;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.module.om.controller.admin.inspectiondata.vo.InspectionRecordPageReqVO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspection.InspectionRecordDO;
import jodd.util.StringUtil;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 巡检记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InspectionRecordMapper extends BaseMapperX<InspectionRecordDO> {

    default PageResult<InspectionRecordDO> selectPage(InspectionRecordPageReqVO reqVO) {
        LambdaQueryWrapperX<InspectionRecordDO> wrapperX = new LambdaQueryWrapperX<InspectionRecordDO>()
                .eqIfPresent(InspectionRecordDO::getResult, reqVO.getResult())
                .eqIfPresent(InspectionRecordDO::getCreator, reqVO.getCreator().toString())
                .betweenIfPresent(InspectionRecordDO::getCreateTime, reqVO.getCreateTime());
        if (StringUtil.isNotEmpty(reqVO.getSortDirection()) && reqVO.getSortDirection().equals("asc")){
            if (reqVO.getSortBy().equals("createTime")){
                wrapperX.orderByAsc(InspectionRecordDO::getCreateTime);
            }
        }else {
            wrapperX.orderByDesc(InspectionRecordDO::getCreateTime);
        }
        return selectPage(reqVO, wrapperX);

    }

}
