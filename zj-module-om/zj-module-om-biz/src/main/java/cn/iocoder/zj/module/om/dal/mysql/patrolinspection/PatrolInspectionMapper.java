package cn.iocoder.zj.module.om.dal.mysql.patrolinspection;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.om.controller.admin.inspectiondata.vo.InspectionRecordRespVO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspection.PatrolInspectionDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.om.controller.admin.patrolinspection.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 自动巡检规则 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PatrolInspectionMapper extends BaseMapperX<PatrolInspectionDO> {

    default PageResult<PatrolInspectionDO> selectPage(PatrolInspectionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PatrolInspectionDO>()
                .likeIfPresent(PatrolInspectionDO::getName, reqVO.getName())
                .eqIfPresent(PatrolInspectionDO::getType, reqVO.getType())
                .eqIfPresent(PatrolInspectionDO::getAssetType, reqVO.getAssetType())
                .eqIfPresent(PatrolInspectionDO::getThreshold, reqVO.getThreshold())
                .eqIfPresent(PatrolInspectionDO::getValue, reqVO.getValue())
                .eqIfPresent(PatrolInspectionDO::getRule, reqVO.getRule())
                .betweenIfPresent(PatrolInspectionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PatrolInspectionDO::getId));
    }

    default List<PatrolInspectionDO> selectList(PatrolInspectionExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PatrolInspectionDO>()
                .likeIfPresent(PatrolInspectionDO::getName, reqVO.getName())
                .eqIfPresent(PatrolInspectionDO::getType, reqVO.getType())
                .eqIfPresent(PatrolInspectionDO::getThreshold, reqVO.getThreshold())
                .eqIfPresent(PatrolInspectionDO::getValue, reqVO.getValue())
                .eqIfPresent(PatrolInspectionDO::getRule, reqVO.getRule())
                .orderByDesc(PatrolInspectionDO::getId));
    }

    InspectionRecordRespVO getLastInspectionRecord(@Param("id") Long id);

    Map getAssetNum(@Param("tenantId")Long tenantId);
}
