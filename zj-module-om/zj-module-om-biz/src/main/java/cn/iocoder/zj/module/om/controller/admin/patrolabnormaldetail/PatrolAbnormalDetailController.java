package cn.iocoder.zj.module.om.controller.admin.patrolabnormaldetail;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.module.om.controller.admin.patrolabnormaldetail.vo.*;
import cn.iocoder.zj.module.om.convert.patrolabnormaldetail.PatrolAbnormalDetailConvert;
import cn.iocoder.zj.module.om.dal.dataobject.patrolabnormaldetail.PatrolAbnormalDetailDO;
import cn.iocoder.zj.module.om.service.patrolabnormaldetail.PatrolAbnormalDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

@Tag(name = "管理后台 - 巡检异常明细")
@RestController
@RequestMapping("/om/patrol-abnormal-detail")
@Validated
public class PatrolAbnormalDetailController {

    @Resource
    private PatrolAbnormalDetailService patrolAbnormalDetailService;

    @PostMapping("/create")
    @Operation(summary = "创建巡检异常明细")
    @PreAuthorize("@ss.hasPermission('om:patrol-abnormal-detail:create')")
    public CommonResult<Long> createPatrolAbnormalDetail(@Valid @RequestBody PatrolAbnormalDetailCreateReqVO createReqVO) {
        return success(patrolAbnormalDetailService.createPatrolAbnormalDetail(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新巡检异常明细")
    @PreAuthorize("@ss.hasPermission('om:patrol-abnormal-detail:update')")
    public CommonResult<Boolean> updatePatrolAbnormalDetail(@Valid @RequestBody PatrolAbnormalDetailUpdateReqVO updateReqVO) {
        patrolAbnormalDetailService.updatePatrolAbnormalDetail(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除巡检异常明细")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('om:patrol-abnormal-detail:delete')")
    public CommonResult<Boolean> deletePatrolAbnormalDetail(@RequestParam("id") Long id) {
        patrolAbnormalDetailService.deletePatrolAbnormalDetail(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得巡检异常明细")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('om:patrol-abnormal-detail:query')")
    public CommonResult<PatrolAbnormalDetailRespVO> getPatrolAbnormalDetail(@RequestParam("id") Long id) {
        PatrolAbnormalDetailDO patrolAbnormalDetail = patrolAbnormalDetailService.getPatrolAbnormalDetail(id);
        return success(PatrolAbnormalDetailConvert.INSTANCE.convert(patrolAbnormalDetail));
    }

    @GetMapping("/list")
    @Operation(summary = "获得巡检异常明细列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('om:patrol-abnormal-detail:query')")
    public CommonResult<List<PatrolAbnormalDetailRespVO>> getPatrolAbnormalDetailList(@RequestParam("ids") Collection<Long> ids) {
        List<PatrolAbnormalDetailDO> list = patrolAbnormalDetailService.getPatrolAbnormalDetailList(ids);
        return success(PatrolAbnormalDetailConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得巡检异常明细分页")
    @PreAuthorize("@ss.hasPermission('om:patrol-abnormal-detail:query')")
    public CommonResult<PageResult<PatrolAbnormalDetailRespVO>> getPatrolAbnormalDetailPage(@Valid PatrolAbnormalDetailPageReqVO pageVO) {
        PageResult<PatrolAbnormalDetailDO> pageResult = patrolAbnormalDetailService.getPatrolAbnormalDetailPage(pageVO);
        return success(PatrolAbnormalDetailConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出巡检异常明细 Excel")
    @PreAuthorize("@ss.hasPermission('om:patrol-abnormal-detail:export')")
    @OperateLog(type = EXPORT)
    public void exportPatrolAbnormalDetailExcel(@Valid PatrolAbnormalDetailExportReqVO exportReqVO,
                                                HttpServletResponse response) throws IOException {
        List<PatrolAbnormalDetailDO> list = patrolAbnormalDetailService.getPatrolAbnormalDetailList(exportReqVO);
        // 导出 Excel
        List<PatrolAbnormalDetailExcelVO> datas = PatrolAbnormalDetailConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "巡检异常明细.xls", "数据", PatrolAbnormalDetailExcelVO.class, datas);
    }

}
