package cn.iocoder.zj.module.om.service.monitorasset;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.iocoder.cloud.module.cloudedge.api.monitor.MonitorApi;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.exception.ErrorCode;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.UserBindDTO;
import cn.iocoder.zj.framework.common.util.dingtalk.DingTalkUtil;
import cn.iocoder.zj.framework.common.util.dingtalk.QyWxUtils;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.framework.common.util.threadpool.ThreadPool;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.api.cloud.CloudInfoApi;
import cn.iocoder.zj.module.monitor.api.hardware.HardWareInfoApi;
import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespCreateReqDTO;
import cn.iocoder.zj.module.om.api.asset.dto.MonitorAssetDTO;
import cn.iocoder.zj.module.om.controller.admin.monitorasset.vo.*;
import cn.iocoder.zj.module.om.convert.monitorasset.MonitorAssetConvert;
import cn.iocoder.zj.module.om.dal.dataobject.monitorasset.MonitorAssetDO;
import cn.iocoder.zj.module.om.dal.dataobject.monitorauthorization.MonitorAuthorizationDO;
import cn.iocoder.zj.module.om.dal.dataobject.monitorauthorizationuser.MonitorAuthorizationUserDO;
import cn.iocoder.zj.module.om.dal.mysql.monitorasset.MonitorAssetMapper;
import cn.iocoder.zj.module.om.dal.mysql.monitorauthorization.MonitorAuthorizationMapper;
import cn.iocoder.zj.module.om.dal.mysql.monitorauthorizationuser.MonitorAuthorizationUserMapper;
import cn.iocoder.zj.module.om.dal.redis.asset.AssetRedisDAO;
import cn.iocoder.zj.module.om.service.monitorauthorization.MonitorAuthorizationService;
import cn.iocoder.zj.module.om.util.StringUtil;
import cn.iocoder.zj.module.om.util.XMLUtil;
import cn.iocoder.zj.module.om.util.aes.AesException;
import cn.iocoder.zj.module.om.util.aes.WXBizMsgCrypt;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.zj.module.system.api.wechat.WeChatSendApi;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.apache.hertzbeat.common.util.AesUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.singleton;
import static cn.iocoder.zj.module.om.dal.redis.RedisKeyConstants.AUTH_KEY;
import static cn.iocoder.zj.module.om.enums.ErrorCodeConstants.MONITOR_ASSET_EXISTS;
import static cn.iocoder.zj.module.om.enums.ErrorCodeConstants.MONITOR_ASSET_NOT_EXISTS;

/**
 * 监控资产 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class MonitorAssetServiceImpl implements MonitorAssetService {

    @Resource
    private MonitorAssetMapper monitorAssetMapper;
    @Autowired
    MonitorApi monitorApi;
    @Autowired
    HardWareInfoApi hardWareInfoApi;
    @Autowired
    WeChatSendApi weChatSendApi;
    @Autowired
    PlatformconfigApi platformconfigApi;
    @Autowired
    AdminUserApi adminUserApi;
    @Autowired
    AssetRedisDAO assetRedisDAO;
    @Autowired
    PermissionApi permissionApi;
    @Autowired
    RoleApi roleApi;
    @Resource
    private MonitorAuthorizationMapper monitorAuthorizationMapper;
    @Autowired
    MonitorAuthorizationService monitorAuthorizationService;
    @Resource
    MonitorAuthorizationUserMapper monitorAuthorizationUserMapper;
    @Resource
    private RedisTemplate redisTemplate;
    @Autowired
    CloudInfoApi cloudInfoApi;

    @Value("${accessing-domain.url}")
    private String address;

    private static String token = "zjToken";
    private static String encodingAesKey = "SqVQTUOWzV9jIU1ihgGk11YGOfY8tp4L6XchwwauPlb";
    private static String appKey = "wwd9be18959419627f";
    private static String appSecret = "g_HWBOobtjrm4hGK0vEfDSai8pML80hdyfwcXnM7wPU";
    private static String agentId = "1000006";
    private static final String BASE_URL = "https://qyapi.weixin.qq.com/cgi-bin/message/";
    private static final String MSG_TYPE = "template_card";
    private static final String openApiHost = "https://api.dingtalk.com";
    private static final String clientId = "dingvkpgrknyfcdghv9w";

    private static String talkAppKey = "dingvkpgrknyfcdghv9w";
    private static String talkAppSecret = "SG8hZ_T5h6t8VknpUjXaCjVpGGAJzKe6qdliuNiSbOrpAP8MnNQnQ-5H5-8PVY2J";

    @Override
    @TenantIgnore
    public Long createMonitorAsset(MonitorAssetCreateReqVO createReqVO) {
        validateMonitorAssetExists(createReqVO.getHostname(), createReqVO.getPlatformId());
        // 插入
        MonitorAssetDO monitorAsset = MonitorAssetConvert.INSTANCE.convert(createReqVO);
        String password = AesUtil.aesEncode(String.valueOf(createReqVO.getPassword()));
        monitorAsset.setPassword(password);
        monitorAsset.setAuthorizationType("0");
        monitorAsset.setLastAccessTime(DateUtil.toLocalDateTime(DateUtil.parse("0001-01-01 00:00:00")));
        monitorAssetMapper.insert(monitorAsset);
        // 返回
        return monitorAsset.getId();
    }

    @Override
    @TenantIgnore
    public void updateMonitorAsset(MonitorAssetUpdateReqVO updateReqVO) {
//        validateMonitorAssetExists(updateReqVO.getHostname(), updateReqVO.getPlatformId());
        // 校验存在
        validateMonitorAssetExists(updateReqVO.getId());
        // 更新
        MonitorAssetDO updateObj = MonitorAssetConvert.INSTANCE.convert(updateReqVO);
        String password = AesUtil.aesEncode(String.valueOf(updateObj.getPassword()));
        updateObj.setPassword(password);
        monitorAssetMapper.updateById(updateObj);
    }

    @Override
    @TenantIgnore
    public void deleteMonitorAsset(Long id) {
        // 校验存在
        validateMonitorAssetExists(id);
        // 删除
        monitorAssetMapper.deleteById(id);
    }

    private void validateMonitorAssetExists(Long id) {
        if (monitorAssetMapper.selectById(id) == null) {
            throw exception(MONITOR_ASSET_NOT_EXISTS);
        }
    }

    private void validateMonitorAssetExists(String hostName, Long platformId) {
        Long count = monitorAssetMapper.selectBycount(hostName, platformId);
        if (count > 0) {
            throw exception(MONITOR_ASSET_EXISTS);
        }
    }


    @Override
    @TenantIgnore
    public MonitorAssetDO getMonitorAsset(Long id) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();

        List<MonitorAuthorizationUserDO> monitorAuthorizationUserDO = monitorAuthorizationUserMapper.selectTypeByUserId(loginUser.getId());
        MonitorAssetDO monitorAssetDO = monitorAssetMapper.selectById(id);
        if (!monitorAuthorizationUserDO.isEmpty()) {
            for (MonitorAuthorizationUserDO monitorAuthorizationUserDO1 : monitorAuthorizationUserDO) {
                if (monitorAuthorizationUserDO1.getAuthorizationUserId().equals(loginUser.getId()) && monitorAuthorizationUserDO1.getMonitorAssetId().equals(monitorAssetDO.getId())) {
                    monitorAssetDO.setAuthorizationType(monitorAuthorizationUserDO1.getAuthorizationType());
                }
            }
        }
        String password = AesUtil.aesDecode(String.valueOf(monitorAssetDO.getPassword()));
        monitorAssetDO.setPassword(password);
        return monitorAssetDO;
    }

    @Override
    @TenantIgnore
    public List<MonitorAssetDO> getMonitorAssetList(Collection<Long> ids) {
        return monitorAssetMapper.selectBatchIds(ids);
    }

    @Override
    @TenantIgnore
    public PageResult<MonitorAssetDO> getMonitorAssetPage(MonitorAssetPageReqVO pageReqVO) {
        List<Map> platform = new ArrayList<>();
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
        PageResult<MonitorAssetDO> pageResult = monitorAssetMapper.selectPage(pageReqVO, platform);
        List<MonitorAuthorizationUserDO> monitorAuthorizationUserDO = monitorAuthorizationUserMapper.selectTypeByUserId(loginUser.getId());
        if (monitorAuthorizationUserDO != null) {
            for (MonitorAssetDO monitorAssetDO : pageResult.getList()) {
                monitorAssetDO.setAuthorizationType("0");
                for (MonitorAuthorizationUserDO monitorAuthorizationUserDO1 : monitorAuthorizationUserDO) {
                    if (monitorAuthorizationUserDO1.getAuthorizationUserId().equals(loginUser.getId()) && monitorAuthorizationUserDO1.getMonitorAssetId().equals(monitorAssetDO.getId())) {
                        monitorAssetDO.setAuthorizationType(monitorAuthorizationUserDO1.getAuthorizationType());
                    }
                }
            }
        }
        return pageResult;
    }

    @Override
    @TenantIgnore
    public List<MonitorAssetDO> getMonitorAssetList(MonitorAssetExportReqVO exportReqVO) {
        return monitorAssetMapper.selectList(exportReqVO);
    }

    @Override
    @TenantIgnore
    public List<MonitorAssetSelectRespVO> getMonitorAssetSelect(Long platformId,String category) {
        List<MonitorAssetSelectRespVO> selectRespVOS = new ArrayList<>();

        if ("hardware".equals(category)) {
            addHardwareAssets(platformId, selectRespVOS);
        } else {
            addMonitorAssets(platformId, category, selectRespVOS);
        }

        return selectRespVOS;
        }
//    public List<MonitorAssetSelectRespVO> getMonitorAssetSelect(Long platformId,String category) {
//        // 非云平台
//        List<MonitorAssetSelectRespVO> selectRespVOS = new ArrayList<>();
//        if(category.equals("hardware")){
//            List<HardWareRespCreateReqDTO> hardWareRespCreateReqDTOS = hardWareInfoApi.getHardwareByPlatformId(platformId).getCheckedData();
//            if(CollectionUtil.isNotEmpty(hardWareRespCreateReqDTOS)){
//                //物理机
//                for (HardWareRespCreateReqDTO hardWareRespCreateReqDTO : hardWareRespCreateReqDTOS) {
//            MonitorAssetSelectRespVO monitorAssetSelectRespVO = new MonitorAssetSelectRespVO();
//                    monitorAssetSelectRespVO.setAssetId(hardWareRespCreateReqDTO.getUuid());
//                    monitorAssetSelectRespVO.setAssetName(hardWareRespCreateReqDTO.getName());
//                    monitorAssetSelectRespVO.setHostname(hardWareRespCreateReqDTO.getIp());
//                    monitorAssetSelectRespVO.setAssetType(1);
//            selectRespVOS.add(monitorAssetSelectRespVO);
//        }
//            }
//        }else {
//            List<Monitor> monitors = monitorApi.getMonitorByplatformIdAndCategory(platformId,category);
//            if(CollectionUtil.isNotEmpty(monitors)){
//                for (Monitor monitor : monitors) {
//                    MonitorAssetSelectRespVO monitorAssetSelectRespVO = new MonitorAssetSelectRespVO();
//                    monitorAssetSelectRespVO.setAssetId(monitor.getId().toString());
//                    monitorAssetSelectRespVO.setAssetName(monitor.getName());
//                    monitorAssetSelectRespVO.setHostname(monitor.getHost());
//                    monitorAssetSelectRespVO.setAssetType(0);
//                    selectRespVOS.add(monitorAssetSelectRespVO);
//                }
//            }
//        }
//        List<CloudRespCreateReqDTO> cloudRespCreateReqDTOS = cloudInfoApi.getCloudByPlatformId(platformId).getCheckedData();
//
//
//        //云主机
////        for (CloudRespCreateReqDTO cloudRespCreateReqDTO : cloudRespCreateReqDTOS) {
////            MonitorAssetSelectRespVO monitorAssetSelectRespVO = new MonitorAssetSelectRespVO();
////            monitorAssetSelectRespVO.setAssetId(cloudRespCreateReqDTO.getUuid());
////            monitorAssetSelectRespVO.setAssetName(cloudRespCreateReqDTO.getName());
////            monitorAssetSelectRespVO.setHostname(cloudRespCreateReqDTO.getIp());
////            monitorAssetSelectRespVO.setAssetType(2);
////            selectRespVOS.add(monitorAssetSelectRespVO);
////        }
//        return selectRespVOS;
//    }



    private void addHardwareAssets(Long platformId, List<MonitorAssetSelectRespVO> selectRespVOS) {
        List<HardWareRespCreateReqDTO> hardWareRespCreateReqDTOS = hardWareInfoApi.getHardwareByPlatformId(platformId).getCheckedData();

        if (CollectionUtil.isNotEmpty(hardWareRespCreateReqDTOS)) {
            hardWareRespCreateReqDTOS.forEach(hardWare -> {
                selectRespVOS.add(createMonitorAssetSelectRespVO(hardWare.getUuid(), hardWare.getName(), hardWare.getIp(), 1));
            });
        }
    }

    private void addMonitorAssets(Long platformId, String category, List<MonitorAssetSelectRespVO> selectRespVOS) {
        List<Monitor> monitors = monitorApi.getMonitorByplatformIdAndCategory(platformId, category);

        if (CollectionUtil.isNotEmpty(monitors)) {
            monitors.forEach(monitor -> {
                selectRespVOS.add(createMonitorAssetSelectRespVO(monitor.getId().toString(), monitor.getName(), monitor.getHost(), 0));
            });
        }
    }

    private MonitorAssetSelectRespVO createMonitorAssetSelectRespVO(String assetId, String assetName, String hostname, int assetType) {
        MonitorAssetSelectRespVO monitorAssetSelectRespVO = new MonitorAssetSelectRespVO();
        monitorAssetSelectRespVO.setAssetId(assetId);
        monitorAssetSelectRespVO.setAssetName(assetName);
        monitorAssetSelectRespVO.setHostname(hostname);
        monitorAssetSelectRespVO.setAssetType(assetType);
        return monitorAssetSelectRespVO;
    }
    @Override
    @TenantIgnore
    public MonitorAssetDTO getAssetInfoById(Long id) {
        return monitorAssetMapper.getAssetInfoById(id);
    }

    @Override
    @TenantIgnore
    public boolean authorization(Long platformId, Long id) {
        //如果缓存中有数据说明已经申请中了 不允许再次申请
//        MonitorAuthorizationDO monitorAuthorizationDO1 = assetRedisDAO.get("authorization_" + id);
//        if (monitorAuthorizationDO1 != null) {
//            return false;
//        }
        MonitorAssetDO monitorAssetDO = monitorAssetMapper.selectById(id);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        AdminUserRespDTO user = adminUserApi.getUser(Convert.toLong(loginUser.getId())).getCheckedData();

        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        // 根据租户id查询该租户下所配置的租户管理员角色
        // 资产对应租户管理员（有多个人） 租户id去找该租户的租户管理员的userid， 在通过userid 去system_wechat_binding 查询是否有绑定微信，如果没有绑定
        PlatformconfigDTO platformconfigDTO = platformconfigApi.getByConfigId(platformId).getCheckedData();
        List<Long> tenantIds = platformconfigApi.getTenantsByConfigId(platformId).getCheckedData();

        List<String> userList = new ArrayList<>();
        // 租户下配置的租户管理员角色的人
        List<String> openIds = new ArrayList<>();
        // 如果是租户管理员则查询该用户下所绑定的微信openid
        if (!tenantIds.isEmpty()) {
            for (Long tenantid : tenantIds) {
                List<String> list = monitorAssetMapper.getUserListByTenantId(tenantid);
                userList.addAll(list);
                // 查询到租户管理员的id
                String userId = monitorAssetMapper.getTenantInfo(tenantid);
                // 租户管理员id再去微信绑定表中查询openid 如果没有查到直接返回false
                String openId = monitorAssetMapper.getOpenIdByUserId(userId);
                if (StringUtil.isNullOrEmpty(openId)) {
                    continue;
                }
                openIds.add(openId);
            }
        }

        if (!userList.isEmpty()) {
            for (String userId : userList) {
                String openId = monitorAssetMapper.getOpenIdByUserId(userId);
                if (StringUtil.isNullOrEmpty(openId)) {
                    continue;
                }
                openIds.add(openId);
            }
        }
// 使用 LinkedHashSet 去除重复项，同时保持原有的顺序
        Set<String> uniqueOpenIdsSet = new LinkedHashSet<>(openIds);
        List<String> uniqueOpenIds = new ArrayList<>(uniqueOpenIdsSet);

        // 授权申请记录
        MonitorAuthorizationDO monitorAuthorizationDO = new MonitorAuthorizationDO();
        monitorAuthorizationDO.setAssetId(monitorAssetDO.getAssetId());
        monitorAuthorizationDO.setAssetName(monitorAssetDO.getAssetName());
        monitorAuthorizationDO.setHostName(monitorAssetDO.getHostname());
        monitorAuthorizationDO.setCreateTime(DateUtil.toLocalDateTime(new Date()));
        monitorAuthorizationDO.setNickName(user.getNickname());
        monitorAuthorizationDO.setUserId(user.getId());
        monitorAuthorizationDO.setAuthorizationTime(DateUtil.toLocalDateTime(DateUtil.offsetMinute(new Date(), 20)));
        monitorAuthorizationDO.setMonitorAssetId(id);
        int d = monitorAuthorizationMapper.insert(monitorAuthorizationDO);
        // 授权申请人
        MonitorAuthorizationUserDO monitorAuthorizationUserDO = new MonitorAuthorizationUserDO();
        monitorAuthorizationUserDO.setMonitorAssetId(monitorAssetDO.getId());
        monitorAuthorizationUserDO.setAuthorizationUserId(user.getId());
        monitorAuthorizationUserMapper.insert(monitorAuthorizationUserDO);
        // 如果新增成功以后，如果20分钟后状态没有变成1 将自动修改状态为0
        if (d > 0) {
            ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
            scheduler.schedule(() -> {
                MonitorAuthorizationDO existingAuthorization = monitorAuthorizationMapper.selectById(monitorAuthorizationDO.getId());
                if (existingAuthorization != null && "1".equals(existingAuthorization.getAuthorizationType())) {
                    monitorAuthorizationMapper.updateMonitorAuthorizationInfo(existingAuthorization.getId(), "0");
                    monitorAssetMapper.updateAuthorizationType(existingAuthorization.getMonitorAssetId(), "0", DateUtil.toLocalDateTime(new Date()));
                    monitorAuthorizationUserMapper.deleteAuthorizationUserType(existingAuthorization.getMonitorAssetId(), existingAuthorization.getUserId());
                }
            }, 20, TimeUnit.MINUTES);
        }

        //点击详情跳转的地址
        String redictUrl = "";
        try {
            String encodedUrl = URLEncoder.encode(address, "UTF-8");
            String parameter = URLUtil.encode("/pages/assetAuth/info?id=" + monitorAuthorizationDO.getId());
            redictUrl = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxe3d3d4577c6c41b6&redirect_uri=" + encodedUrl + "&response_type=code&scope=snsapi_userinfo&state=" + parameter + "&connect_redirect=1#wechat_redirect";
        } catch (UnsupportedEncodingException e) {
            System.out.println("Error encoding URL: " + e.getMessage());
        }

        Map<String, Object> templateParams = new HashMap<>();
        // 申请人
        templateParams.put("nickname", user.getNickname());
        // 数据主体
        templateParams.put("hostName", monitorAssetDO.getHostname());
        // 授权时间 当前时间
        templateParams.put("date", new Date());
        // 产品名称
        templateParams.put("assetName", monitorAssetDO.getAssetName());
        // url
        templateParams.put("url", redictUrl);
        log.info("进入授权，用户列表为:" + uniqueOpenIds);
        boolean suc = false;
        if (uniqueOpenIds.size() > 0) {
            log.info("进入获取用户列表数据:" + tenantIds);
            for (String openId : uniqueOpenIds) {
                // openid
                templateParams.put("openId", openId);
                suc = weChatSendApi.sendSingleWeChatToAuthorization(templateParams).getCheckedData();
            }
        }
        // 如果发起成功则新增一条数据到数据库中，并且修改状态
//        assetRedisDAO.set("authorization_" + id, monitorAuthorizationDO);
        monitorAssetMapper.updateAuthorizationType(monitorAssetDO.getId(), "1", monitorAuthorizationDO.getAuthorizationTime());
        return suc;
    }

    @Override
    public boolean weworkAuthorization(Long platformId, Long id) {
        //如果缓存中有数据说明已经申请中了 不允许再次申请
        MonitorAssetDO monitorAssetDO = monitorAssetMapper.selectById(id);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        AdminUserRespDTO user = adminUserApi.getUser(Convert.toLong(loginUser.getId())).getCheckedData();

        List<Long> tenantIds = platformconfigApi.getTenantsByConfigId(platformId).getCheckedData();
        List<String> openIds = new ArrayList<>();
        for (Long tenantid : tenantIds) {
            // 查询到租户管理员的id
            String userId = monitorAssetMapper.getTenantInfo(tenantid);
            // 租户管理员id再去微信绑定表中查询openid 如果没有查到直接返回false
            String openId = monitorAssetMapper.getOpenIdByUserId(userId);
            if (StringUtil.isNullOrEmpty(openId)) {
                continue;
            }
            openIds.add(openId);
        }

        MonitorAuthorizationDO monitorAuthorizationDO = new MonitorAuthorizationDO();
        monitorAuthorizationDO.setAssetId(monitorAssetDO.getAssetId());
        monitorAuthorizationDO.setAssetName(monitorAssetDO.getAssetName());
        monitorAuthorizationDO.setHostName(monitorAssetDO.getHostname());
        monitorAuthorizationDO.setCreateTime(DateUtil.toLocalDateTime(new Date()));
        monitorAuthorizationDO.setNickName(user.getNickname());
        monitorAuthorizationDO.setUserId(user.getId());
        monitorAuthorizationDO.setAuthorizationTime(DateUtil.toLocalDateTime(DateUtil.offsetMinute(new Date(), 20)));
        monitorAuthorizationDO.setMonitorAssetId(id);
        int d = monitorAuthorizationMapper.insert(monitorAuthorizationDO);

        String format = DateUtil.format(new Date(), "yyyy年MM月dd日 HH:mm");
        String newDate = addOneMinute();
        Map<String, String> templateParams = new HashMap<>();
        // 申请人
        templateParams.put("nickname", user.getNickname());
        // 数据主体
        templateParams.put("hostName", monitorAssetDO.getHostname());
        // 授权时间 当前时间
        templateParams.put("date", format);
        templateParams.put("dateEnd", newDate);
        // 产品名称
        templateParams.put("assetName", monitorAssetDO.getAssetName());
        templateParams.put("uuid", UUID.randomUUID().toString());
        boolean suc = false;
        if (CollectionUtil.isNotEmpty(tenantIds)) {
            for (String openId : openIds) {
                // openid
                templateParams.put("openId", openId);
                sendMessageNotify("13515645394",templateParams,null,null);
            }
        }

        // 如果新增成功以后，如果20分钟后状态没有变成1 将自动修改状态为0
        if (d > 0) {
            ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
            scheduler.schedule(() -> {
                MonitorAuthorizationDO existingAuthorization = monitorAuthorizationMapper.selectById(monitorAuthorizationDO.getId());
                if (existingAuthorization != null && "1".equals(existingAuthorization.getAuthorizationType())) {
                    monitorAuthorizationMapper.updateMonitorAuthorizationInfo(existingAuthorization.getId(), "0");
                    monitorAssetMapper.updateAuthorizationType(existingAuthorization.getMonitorAssetId(), "0", DateUtil.toLocalDateTime(new Date()));
                    if(redisTemplate.hasKey(templateParams.get("uuid"))){
                        String json = redisTemplate.opsForValue().get(templateParams.get("uuid")).toString();
                        WxTempLateBtnVo wxTempLateBtnVo = JsonUtils.parseObject(json, WxTempLateBtnVo.class);
                        String date = DateUtil.format(new Date(), "yyyy年MM月dd日 HH:mm");
                        wxTempLateBtnVo.getHorizontal_content_list().get(3).setValue(date);
                        wxTempLateBtnVo.getButton_list().clear();
                        List<WxTempLateBtnVo.ButtonListBean> buttonList = new ArrayList<>();
                        wxTempLateBtnVo.getMain_title().setDesc("资产接入申请已过期，如有需要请重新申请");
                        buttonList.add(createButtonBean("已过期", 4, "button_key_1"));
                        wxTempLateBtnVo.setButton_list(buttonList);
                        updateTemplateBtnMsg("13515645394", wxTempLateBtnVo.getAppointId(),wxTempLateBtnVo);
                    }
                }
            }, 20, TimeUnit.MINUTES);
        }
        // 如果发起成功则新增一条数据到数据库中，并且修改状态
        monitorAssetMapper.updateAuthorizationType(monitorAssetDO.getId(), "1", monitorAuthorizationDO.getAuthorizationTime());
        return suc;
    }

    @Override
    @TenantIgnore
    public boolean updateAuthorizationTypeImpl(MonitorAssetUpdateTypeReqVO monitorAssetUpdateTypeReqVO) {
        MonitorAuthorizationDO monitorAuthorizationDO = monitorAuthorizationService.getMonitorAuthorization(monitorAssetUpdateTypeReqVO.getId());
        monitorAuthorizationService.updateMonitorAuthorizationInfo(monitorAssetUpdateTypeReqVO.getId(), monitorAssetUpdateTypeReqVO.getAuthorizationType());
        if(monitorAuthorizationDO.getType() == 1){
            monitorAssetMapper.updateAuthType(monitorAuthorizationDO.getMonitorAssetId(), monitorAssetUpdateTypeReqVO.getAuthorizationType(), monitorAuthorizationDO.getAuthorizationTime());
        }else {
            monitorAssetMapper.updateAuthorizationType(monitorAuthorizationDO.getMonitorAssetId(), monitorAssetUpdateTypeReqVO.getAuthorizationType(), monitorAuthorizationDO.getAuthorizationTime());
        }

        // 如果拒绝授权则删除相关数据
        if (monitorAssetUpdateTypeReqVO.getAuthorizationType().equals("0")) {
            monitorAuthorizationUserMapper.deleteAuthorizationUserType(monitorAuthorizationDO.getMonitorAssetId(), monitorAuthorizationDO.getUserId());
        } else {
            // 同意授权则更新相关状态
            monitorAuthorizationUserMapper.updateAuthorizationUserType(monitorAuthorizationDO.getMonitorAssetId(), monitorAuthorizationDO.getUserId());
        }
        log.info("微信是否同意授权申请" + monitorAuthorizationDO.getUserId());
        String userName = adminUserApi.getUser(monitorAuthorizationDO.getUserId()).getData().getNickname();
        String openId = monitorAssetMapper.getOpenIdByUserId(Convert.toStr(monitorAuthorizationDO.getUserId()));
        log.info("查询的openid" + openId);
        if (openId != null) {
            String accessToken = weChatSendApi.getWechatAccessToken().getCheckedData();
            log.info("微信token获取===========>" + accessToken);

            Map<String, Object> templateParams = new HashMap<>();
            //授权id
            templateParams.put("assetsName", monitorAuthorizationDO.getAssetName());
            //授权状态
            templateParams.put("type","通过");
            if (monitorAssetUpdateTypeReqVO.getAuthorizationType().equals("0")) {
                templateParams.put("type","未通过");
            }
            //授权账号
            templateParams.put("authorizedAccount",userName);
            templateParams.put("url","");
            templateParams.put("openId", openId);

//            String content = "您的资产接入申请已通过\n";
//            if (monitorAssetUpdateTypeReqVO.getAuthorizationType().equals("0")) {
//                content = "您的资产接入申请已驳回\n";
//            }
//
//            content = content + "申请方：" + monitorAuthorizationDO.getNickName() + "\n"
//                    + "主机host：" + monitorAuthorizationDO.getHostName() + "\n"
//                    + "授权时间：" + DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss") + "\n"
//                    + "资源名称：" + monitorAuthorizationDO.getAssetName();
//            Boolean success = weChatSendApi.sendMessage(accessToken, openId, content).getCheckedData();

            Boolean success = weChatSendApi.sendSingleWeChatToAuthorizationType(templateParams).getCheckedData();
            log.info("微信推送是否成功============>" + success);
        }
        return true;
    }

    @Override
    public void updateAssetById(MonitorAssetDTO checkedData) {
        monitorAssetMapper.updateAssetById(checkedData);
    }

    @Override
    public MonitorAssetDTO getAssetInfoByIdAndFormId(String monitorId, Long platformId) {
        return monitorAssetMapper.selectOneDo(monitorId,platformId);
    }

    @Override
    public void updateAssetByAssetId(MonitorAssetDTO dto) {
        monitorAssetMapper.updateAssetByAssetId(dto);
    }

    @Override
    public void updateAsset(Long id, String name) {
        monitorAssetMapper.updateAsset(id,name);
    }

    @Override
    public String weworkCallback(String msgSignature, String timestamp, String nonce, String echoStr, String xml) {
        if (echoStr == null && xml == null) {
            return "解析内容为空，解析失败";
        }
        if (msgSignature == null || timestamp == null || nonce == null) {
            return "辅助解析参数为空，解析失败";
        }

        String result = "成功";
        String status = null;

        try {
            WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(token, encodingAesKey, appKey);
            String plainXml = wxcpt.DecryptMsg(msgSignature, timestamp, nonce, xml);
            CommonEventData xmlObject = XMLUtil.convertXmlStrToObject(CommonEventData.class, plainXml);
            log.info("企微回调参数, {}", JsonUtils.toJsonString(xmlObject));

            String trackId = xmlObject.getTaskId().split("-")[0];
            String redisKey = formatKey("wx-" + trackId + ":" + xmlObject.getFromUserName());

            if (redisTemplate.hasKey(redisKey)) {
                WxTempLateBtnVo wxTempLateBtnVo = updateWxTemplate(xmlObject, redisKey);
                status = wxTempLateBtnVo.getStatus();
                updateDingTalkCards(trackId, status);
                updateOtherWxCards(trackId, status);
                handleAuthorization(status,Long.parseLong(wxTempLateBtnVo.getAuthuserId()));
            }
        } catch (AesException e) {
            throw new RuntimeException(e);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }

    private WxTempLateBtnVo updateWxTemplate(CommonEventData xmlObject, String redisKey) {
        String json = redisTemplate.opsForValue().get(redisKey).toString();
        WxTempLateBtnVo wxTempLateBtnVo = JsonUtils.parseObject(json, WxTempLateBtnVo.class);

        String format = DateUtil.format(new Date(), "yyyy年MM月dd日 HH:mm");
        wxTempLateBtnVo.getHorizontal_content_list().get(3).setValue(format);
        wxTempLateBtnVo.getButton_list().clear();

        String type;
        String status;
        if ("button_key_1".equals(xmlObject.getEventKey())) {
            wxTempLateBtnVo.getMain_title().setDesc("您的资产接入申请已通过，申请人的所有操作将会被记录和审计");
            type = "已同意";
            status = "2";
        } else {
            wxTempLateBtnVo.getMain_title().setDesc("您的资产接入申请已驳回，如有需要请从新发起申请");
            type = "已驳回";
            status = "0";
        }

        wxTempLateBtnVo.setButton_list(Collections.singletonList(createButtonBean(type, 4, "button_key_1")));
        updateAuthorizationType(wxTempLateBtnVo.getAuthId(), wxTempLateBtnVo.getAssetId(), status);
        updateTemplateBtnMsg(xmlObject.getFromUserName(), xmlObject.getResponseCode(), wxTempLateBtnVo);
        log.info("企微修改状态 : " + redisKey);
        redisTemplate.delete(redisKey);

        wxTempLateBtnVo.setStatus(status);
        return wxTempLateBtnVo;
    }

    private void updateDingTalkCards(String trackId, String status) throws Exception {
        String redisKeyDk = formatKey("dingtalk-" + trackId + ":*");
        Set<String> keys = redisTemplate.keys(redisKeyDk);

        if (CollectionUtil.isNotEmpty(keys)) {
            for (String key : keys) {
                String jsonString = redisTemplate.opsForValue().get(key).toString();
                Map<String, String> stringStringMap = new ObjectMapper().readValue(jsonString, new TypeReference<Map<String, String>>() {});
                stringStringMap.put("dateEnd", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
                String accessToken = stringStringMap.get("accessToken");
                String uuid = stringStringMap.get("uuid");

                DingTalkUtil.updateCardMessage("4", accessToken, uuid, stringStringMap);
                log.info("钉钉更新状态：" + key);
                String authId = stringStringMap.get("authId");
                if (StringUtil.isNotEmpty(authId)) {
                    updateAuthType(Long.valueOf(authId), status);
                }
                redisTemplate.delete(key);
            }
        }
    }

    private void updateOtherWxCards(String trackId, String status) {
        String redisKeyWx = formatKey("wx-" + trackId + ":*");
        Set<String> wxKeys = redisTemplate.keys(redisKeyWx);

        if (CollectionUtil.isNotEmpty(wxKeys)) {
            for (String key : wxKeys) {
                String json = redisTemplate.opsForValue().get(key).toString();
                WxTempLateBtnVo wxTempLateBtnVo = JsonUtils.parseObject(json, WxTempLateBtnVo.class);
                String date = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
                wxTempLateBtnVo.getHorizontal_content_list().get(3).setValue(date);
                wxTempLateBtnVo.getButton_list().clear();
                wxTempLateBtnVo.getMain_title().setDesc("资产接入申请已授权");
                wxTempLateBtnVo.setButton_list(Collections.singletonList(createButtonBean("已授权", 4, "button_key_1")));
                updateTemplateBtnMsg(wxTempLateBtnVo.getUserId(), wxTempLateBtnVo.getAppointId(), wxTempLateBtnVo);
                updateAuthType(wxTempLateBtnVo.getAuthId(), status);
                log.info("企微更新卡片: " + key);
                redisTemplate.delete(key);
                monitorAuthorizationUserMapper.deleteById(wxTempLateBtnVo.getAuthuserId());
            }
        }
    }

    @Override
    @SneakyThrows
    public String dingtalkCallback(JSONObject body) {
        log.info("钉钉回调参数, {}", body);

        String outTrackId = body.getString("outTrackId");
        String content = body.getString("content");
        String userId = body.getString("userId");
        String trackId = outTrackId.split("-")[0];
        String redisKey = formatKey("dingtalk-" + trackId + ":" + userId);

        Map<String, String> templateParams = getTemplateParams(redisKey);
        String accessToken = DingTalkUtil.getAccessToken(talkAppKey, talkAppSecret);
        validateAccessToken(accessToken);

        String actionIds = getActionIdsFromContent(content);
        String type = updateDingTalkCard(outTrackId, accessToken, templateParams, actionIds,redisKey);

        updateWeChatCards(trackId);
        updateOtherDingTalkData(trackId, type);
        String authuserId = templateParams.get("authuserId");
        handleAuthorization(type,Long.parseLong(authuserId));

        return null;
    }

    private void validateAccessToken(String accessToken) {
        if (StringUtil.isNullOrEmpty(accessToken)) {
            log.info("钉钉token获取为空: {}", accessToken);
            throw exception(new ErrorCode(1002000008, "钉钉token获取为空"));
        }
    }

    private String getActionIdsFromContent(String content) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(content);
            JsonNode actionIdsNode = root.path("cardPrivateData").path("actionIds");
            return mapper.writeValueAsString(actionIdsNode);
        } catch (JsonProcessingException e) {
            log.error("解析内容中的 actionIds 时出错: {}", e.getMessage());
            return "";
        }
    }

    private Map<String, String> getTemplateParams(String redisKey) {
        if (!redisTemplate.hasKey(redisKey)) return new HashMap<>();

        String jsonString = redisTemplate.opsForValue().get(redisKey).toString();
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, String> params = objectMapper.readValue(jsonString, new TypeReference<Map<String, String>>() {});
            String date = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
            params.put("dateEnd", date);
            return params;
        } catch (JsonProcessingException e) {
            log.error("从 Redis 中读取模板参数时出错: {}", e.getMessage());
            return new HashMap<>();
        }
    }

    private String updateDingTalkCard(String outTrackId, String accessToken, Map<String, String> templateParams,
                                      String actionIds,String redisKey) throws Exception {
        String type = StringUtil.isNotEmpty(actionIds) && actionIds.contains("1") ? "2" : "0";
        DingTalkUtil.updateCardMessage(type.equals("2") ? "1" : "2", accessToken, outTrackId, templateParams);

        log.info("钉钉修改状态: {}", redisKey);

        String authId = templateParams.get("authId");
        String assetId = templateParams.get("assetId");
        if (StringUtil.isNotEmpty(authId) && StringUtil.isNotEmpty(assetId)) {
            updateAuthorizationType(Long.valueOf(authId), Long.valueOf(assetId), type);
        }

        redisTemplate.delete(redisKey);
        return type;
    }

    private void updateWeChatCards(String trackId) {
        String redisKeyWx = formatKey("wx-" + trackId + ":*");
        Set<String> wxKeys = redisTemplate.keys(redisKeyWx);
        if (CollectionUtil.isEmpty(wxKeys)) return;

        for (String key : wxKeys) {
            String json = redisTemplate.opsForValue().get(key).toString();
            WxTempLateBtnVo wxTemplate = JsonUtils.parseObject(json, WxTempLateBtnVo.class);
            String date = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");

            wxTemplate.getHorizontal_content_list().get(3).setValue(date);
            wxTemplate.getButton_list().clear();
            wxTemplate.getMain_title().setDesc("资产接入申请已授权");
            wxTemplate.setButton_list(Collections.singletonList(createButtonBean("已授权", 4, "button_key_1")));

            updateTemplateBtnMsg(wxTemplate.getUserId(), wxTemplate.getAppointId(), wxTemplate);
            updateAuthType(wxTemplate.getAuthId(), "2");

            log.info("企微更新卡片: {}", key);
            redisTemplate.delete(key);
        }
    }

    private void updateOtherDingTalkData(String trackId, String type) {
        String redisKeyDk = formatKey("dingtalk-" + trackId + ":*");
        Set<String> keys = redisTemplate.keys(redisKeyDk);
        if (CollectionUtil.isEmpty(keys)) return;

        for (String key : keys) {
            String jsonString = redisTemplate.opsForValue().get(key).toString();
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                Map<String, String> params = objectMapper.readValue(jsonString, new TypeReference<Map<String, String>>() {});
                String date = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
                params.put("dateEnd", date);

                String token = params.get("accessToken");
                String uuid = params.get("uuid");
                DingTalkUtil.updateCardMessage("4", token, uuid, params);

                log.info("钉钉更新状态: {}", key);
                String authId = params.get("authId");
                if (StringUtil.isNotEmpty(authId)) {
                    updateAuthType(Long.valueOf(authId), type);
                }
                redisTemplate.delete(key);
                monitorAuthorizationUserMapper.deleteById(params.get("authuserId"));
            } catch (Exception e) {
                log.error("处理钉钉数据时出错: {}", e.getMessage());
            }
        }
    }

    @Override
    public String checkSign(String msgSignature, String timestamp, String nonce, String echoStr, String xml) {
        String result = "成功";
        try {
            WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(token, encodingAesKey, appKey);
            if (echoStr!=null&&!echoStr.isBlank()){
                return wxcpt.VerifyURL(msgSignature, timestamp, nonce, echoStr);
            }
        } catch (AesException e) {
            throw new RuntimeException(e);
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAuthorizationType(Long authId,Long assetId,String type){
        monitorAuthorizationService.updateMonitorAuthorizationInfo(authId, type);
        monitorAssetMapper.updateAuthorizationType(assetId, type,DateUtil.toLocalDateTime(new Date()));
    }

    public void updateAuthType(Long authId,String type){
        monitorAuthorizationService.updateMonitorAuthorizationInfo(authId, type);
    }

    @Override
    @SneakyThrows
    public boolean dingtalkAuthorization(Long platformId, Long id) {
        //如果缓存中有数据说明已经申请中了 不允许再次申请
        MonitorAssetDO monitorAssetDO = monitorAssetMapper.selectById(id);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        AdminUserRespDTO user = adminUserApi.getUser(Convert.toLong(loginUser.getId())).getCheckedData();

        List<Long> tenantIds = platformconfigApi.getTenantsByConfigId(platformId).getCheckedData();
        List<String> openIds = new ArrayList<>();
        for (Long tenantid : tenantIds) {
            // 查询到租户管理员的id
            String userId = monitorAssetMapper.getTenantInfo(tenantid);
            // 租户管理员id再去微信绑定表中查询openid 如果没有查到直接返回false
            String openId = monitorAssetMapper.getOpenIdByUserId(userId);
            if (StringUtil.isNullOrEmpty(openId)) {
                continue;
            }
            openIds.add(openId);
        }

        MonitorAuthorizationDO monitorAuthorizationDO = new MonitorAuthorizationDO();
        monitorAuthorizationDO.setAssetId(monitorAssetDO.getAssetId());
        monitorAuthorizationDO.setAssetName(monitorAssetDO.getAssetName());
        monitorAuthorizationDO.setHostName(monitorAssetDO.getHostname());
        monitorAuthorizationDO.setCreateTime(DateUtil.toLocalDateTime(new Date()));
        monitorAuthorizationDO.setNickName(user.getNickname());
        monitorAuthorizationDO.setUserId(user.getId());
        monitorAuthorizationDO.setAuthorizationTime(DateUtil.toLocalDateTime(DateUtil.offsetMinute(new Date(), 20)));
        monitorAuthorizationDO.setMonitorAssetId(id);
        int d = monitorAuthorizationMapper.insert(monitorAuthorizationDO);

        String format = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        String newDate = addOneMinuteTwo();
        Map<String, String> templateParams = new HashMap<>();
        // 申请人
        templateParams.put("nickname", user.getNickname());
        // 数据主体
        templateParams.put("hostName", monitorAssetDO.getHostname());
        // 授权时间 当前时间
        templateParams.put("date", format);
        templateParams.put("dateEnd", newDate);
        // 产品名称
        templateParams.put("assetName", monitorAssetDO.getAssetName());
        templateParams.put("uuid", UUID.randomUUID().toString());
        String accessToken = DingTalkUtil.getAccessToken(talkAppKey, talkAppSecret);
        if(jodd.util.StringUtil.isEmpty(accessToken)){
            log.info("钉钉token获取为空" + accessToken);
            throw exception(new ErrorCode(1002000008, "钉钉token获取为空"));
        }
        String userIdByMobile = DingTalkUtil.getUserIdByMobile("13515645394", accessToken);
        boolean suc = false;
        if (CollectionUtil.isNotEmpty(tenantIds)) {
            for (String openId : openIds) {
                templateParams.put("openId", openId);
                DingTalkUtil.sendCardMessage("0", accessToken, userIdByMobile,templateParams.get("uuid"),templateParams);
                if(!redisTemplate.hasKey(templateParams.get("uuid"))){
                    redisTemplate.opsForValue().set(templateParams.get("uuid"),
                            JsonUtils.toJsonString(templateParams),21,
                            TimeUnit.MINUTES);
                }
            }
        }

        // 如果新增成功以后，如果20分钟后状态没有变成1 将自动修改状态为0
        if (d > 0) {
            ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
            scheduler.schedule(() -> {
                MonitorAuthorizationDO existingAuthorization = monitorAuthorizationMapper.selectById(monitorAuthorizationDO.getId());
                if (existingAuthorization != null && "1".equals(existingAuthorization.getAuthorizationType())) {
                    monitorAuthorizationMapper.updateMonitorAuthorizationInfo(existingAuthorization.getId(), "0");
                    monitorAssetMapper.updateAuthorizationType(existingAuthorization.getMonitorAssetId(), "0", DateUtil.toLocalDateTime(new Date()));
                    if(redisTemplate.hasKey(templateParams.get("uuid"))){
                        ObjectMapper objectMapper = new ObjectMapper();
                        String jsonString = redisTemplate.opsForValue().get(templateParams.get("uuid")).toString();
                        try {
                            Map<String, String> stringStringMap = objectMapper.readValue(jsonString,
                                    new TypeReference<Map<String, String>>() {
                            });
                            String date = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
                            stringStringMap.put("dateEnd",date);
                            DingTalkUtil.updateCardMessage("3", accessToken,templateParams.get("uuid"),templateParams);
                        } catch (Exception e) {
                            System.out.println(e.getMessage());
                        }

                    }
                }
            }, 1, TimeUnit.MINUTES);
        }
        // 如果发起成功则新增一条数据到数据库中，并且修改状态
        monitorAssetMapper.updateAuthorizationType(monitorAssetDO.getId(), "1", monitorAuthorizationDO.getAuthorizationTime());
        return suc;
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public boolean selectAuthorization(Long platformId, Long id) {
        // 查询监控资产
        MonitorAssetDO monitorAssetDO = monitorAssetMapper.selectById(id);
        if(monitorAssetDO.getAuthorizationType().equals("1")){
            throw exception(new ErrorCode(1002000011, "已经申请过授权，请不要重复操作"));
        }
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        AdminUserRespDTO user = adminUserApi.getUser(Convert.toLong(loginUser.getId())).getCheckedData();

        // 获取特定角色的用户ID
        Set<Long> userIds = roleApi.getUserIdByCode("devops").getData();
        if (CollectionUtil.isEmpty(userIds)) {
            throw exception(new ErrorCode(1002000009, "授权处理角色未配置，暂无法进行申请"));
        }

        // 获取符合条件的用户列表
        List<AdminUserRespDTO> checkedData = adminUserApi.getUserList(userIds).getCheckedData();
        List<AdminUserRespDTO> collect =
                checkedData.stream().filter(x -> x.getStatus() == 0).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(collect)) {
            throw exception(new ErrorCode(1002000010, "未查询到授权人员，暂无法进行申请"));
        }
        // 处理用户绑定
        return processUserBindings(monitorAssetDO, user, id, collect);
    }

    @FunctionalInterface
    interface AuthFunction {
        void apply(MonitorAssetDO monitorAssetDO, AdminUserRespDTO user, Long id, UserBindDTO userBindDO, String uuid) throws Exception;
    }

    private boolean processUserBindings(MonitorAssetDO monitorAssetDO, AdminUserRespDTO user, Long id, List<AdminUserRespDTO> list){
        String uuid = UUID.randomUUID().toString().replace("-", "");
        log.info("准备发送信息: {}", JsonUtils.toJsonString(list));

        if (!redisTemplate.hasKey("app_key_secret:app")) {
            log.info("企微或钉钉redis未查询到绑定信息: {}", JsonUtils.toJsonString(list));
            return false;
        }
        monitorAssetMapper.updateAuthorizationType(monitorAssetDO.getId(), "1", DateUtil.toLocalDateTime(DateUtil.offsetMinute(new Date(), 20)));
        UserBindDTO userBindDO = JsonUtils.parseObject(redisTemplate.opsForValue().get("app_key_secret:app").toString(), UserBindDTO.class);
        ThreadPool.asyn(() -> {
            if (userBindDO.getDingtalkState() == 1) {
                processMessages(list, monitorAssetDO, user, id, userBindDO, uuid, this::dingtalkauth, "钉钉");
            }

            if (userBindDO.getWxState() == 1) {
                List<AdminUserRespDTO> eligibleUsers = list.stream().filter(x -> x.getWxCheckState() == 0).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(eligibleUsers)){
                    processMessages(eligibleUsers, monitorAssetDO, user, id, userBindDO, uuid, this::wxauth, "企微");
                }
            }
        });


        ThreadUtil.sleep(1, TimeUnit.SECONDS);
        return true;
    }

    private void processMessages(List<AdminUserRespDTO> list, MonitorAssetDO monitorAssetDO, AdminUserRespDTO user,
                              Long id, UserBindDTO userBindDO, String uuid, AuthFunction authFunction, String serviceName) {

        for (AdminUserRespDTO x : list) {
            UserBindDTO userBindDTO = BeanUtil.copyProperties(userBindDO, UserBindDTO.class);
            userBindDTO.setDingtalkPhone(x.getMobile());
            userBindDTO.setWxPhone(x.getMobile());
            try {
                authFunction.apply(monitorAssetDO, x, id, userBindDTO, uuid);
            } catch (Exception e) {
                log.error("发送{}信息时发生异常 (用户: {}): {}", serviceName, x.getMobile(), e.getMessage());
            }
        }

        log.info("{}发送信息: {}", serviceName, JsonUtils.toJsonString(list));
    }

    private void wxauth(MonitorAssetDO monitorAssetDO, AdminUserRespDTO user, Long id, UserBindDTO userBindDO, String uuid) {
        String token = QyWxUtils.getToken(userBindDO.getWxCorpid(), userBindDO.getWxCorpsecret());
        String userID = QyWxUtils.getUserID(userBindDO.getWxPhone(), token);
        if(StringUtil.isNullOrEmpty(userID)){
            throw new RuntimeException("手机号未在企微里："+userBindDO.getWxPhone());
        }
        MonitorAuthorizationDO monitorAuthorizationDO = createMonitorAuthorization(monitorAssetDO, user, id);
//        monitorAuthorizationDO.setStatus(2);
        if ( monitorAuthorizationMapper.insert(monitorAuthorizationDO) > 0) {
            // 授权申请人
            MonitorAuthorizationUserDO monitorAuthorizationUserDO = new MonitorAuthorizationUserDO();
            monitorAuthorizationUserDO.setMonitorAssetId(monitorAssetDO.getId());
            monitorAuthorizationUserDO.setAuthorizationUserId(user.getId());
//            monitorAuthorizationUserDO.setStatus(2);
            monitorAuthorizationUserMapper.insert(monitorAuthorizationUserDO);
            Map<String, String> templateParams = createTemplateParams(monitorAssetDO, user, uuid + "-" +userID);
            templateParams.put("authuserId", monitorAuthorizationUserDO.getId().toString());
            String redisKey = formatKey("wx-" + uuid + ":" + userID);

            sendMessageNotify(userID, templateParams, redisKey, monitorAuthorizationDO);
            scheduleStatusCheckWx(monitorAuthorizationDO, userID, redisKey,2);
        }
    }

    private void dingtalkauth(MonitorAssetDO monitorAssetDO, AdminUserRespDTO user, Long id, UserBindDTO userBindDO, String uuid) throws Exception {
        String accessToken = DingTalkUtil.getAccessToken(userBindDO.getDingtalkAppKey(), userBindDO.getDingtalkAppSecret());
        String userIdByMobile = DingTalkUtil.getUserIdByMobile(userBindDO.getDingtalkPhone(), accessToken);
        if(StringUtil.isNullOrEmpty(userIdByMobile)){
            throw new RuntimeException("手机号未在钉钉里: "+userBindDO.getDingtalkPhone());
        }
        MonitorAuthorizationDO monitorAuthorizationDO = createMonitorAuthorization(monitorAssetDO, user, id);
//        monitorAuthorizationDO.setStatus(1);
        if (monitorAuthorizationMapper.insert(monitorAuthorizationDO) > 0) {
            // 授权申请人
            MonitorAuthorizationUserDO monitorAuthorizationUserDO = new MonitorAuthorizationUserDO();
            monitorAuthorizationUserDO.setMonitorAssetId(monitorAssetDO.getId());
            monitorAuthorizationUserDO.setAuthorizationUserId(user.getId());
//            monitorAuthorizationUserDO.setStatus(1);
            monitorAuthorizationUserMapper.insert(monitorAuthorizationUserDO);
            Map<String, String> templateParams = createTemplateParams(monitorAssetDO, user, uuid+ "-" +userIdByMobile);
            String redisKey = formatKey("dingtalk-" + uuid  + ":" + userIdByMobile);

            templateParams.put("accessToken", accessToken);
            templateParams.put("assetId", monitorAuthorizationDO.getMonitorAssetId().toString());
            templateParams.put("authId", monitorAuthorizationDO.getId().toString());
            templateParams.put("authuserId", monitorAuthorizationUserDO.getId().toString());

            DingTalkUtil.sendCardMessage("0", accessToken, userIdByMobile, templateParams.get("uuid"), templateParams);

            if (!redisTemplate.hasKey(redisKey)) {
                redisTemplate.opsForValue().set(redisKey, JsonUtils.toJsonString(templateParams), 21, TimeUnit.MINUTES);
            }
            scheduleStatusCheckDingtalk(monitorAuthorizationDO, templateParams, accessToken, redisKey,1);
        }
    }
    private MonitorAuthorizationDO createMonitorAuthorization(MonitorAssetDO monitorAssetDO, AdminUserRespDTO user, Long id) {
        MonitorAuthorizationDO monitorAuthorizationDO = new MonitorAuthorizationDO();
        monitorAuthorizationDO.setAssetId(monitorAssetDO.getAssetId());
        monitorAuthorizationDO.setAssetName(monitorAssetDO.getAssetName());
        monitorAuthorizationDO.setHostName(monitorAssetDO.getHostname());
        monitorAuthorizationDO.setCreateTime(DateUtil.toLocalDateTime(new Date()));
        monitorAuthorizationDO.setNickName(user.getNickname());
        monitorAuthorizationDO.setUserId(user.getId());
        monitorAuthorizationDO.setAuthorizationTime(DateUtil.toLocalDateTime(DateUtil.offsetMinute(new Date(), 20)));
        monitorAuthorizationDO.setMonitorAssetId(id);
        return monitorAuthorizationDO;
    }

    private Map<String, String> createTemplateParams(MonitorAssetDO monitorAssetDO, AdminUserRespDTO user,String uuid) {
        String format = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        String newDate = addOneMinuteTwo();
        Map<String, String> templateParams = new HashMap<>();
        templateParams.put("nickname", user.getNickname());
        templateParams.put("hostName", monitorAssetDO.getHostname());
        templateParams.put("date", format);
        templateParams.put("dateEnd", newDate);
        templateParams.put("assetName", monitorAssetDO.getAssetName());
        templateParams.put("uuid", uuid);
        return templateParams;
    }

    private void scheduleStatusCheckWx(MonitorAuthorizationDO monitorAuthorizationDO,String phoneNumber,
                                       String redisKey,int status) {
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
        scheduler.schedule(() -> {
            try {
                MonitorAuthorizationDO existingAuthorization = monitorAuthorizationMapper.selectById(monitorAuthorizationDO.getId());
                if (existingAuthorization != null && "1".equals(existingAuthorization.getAuthorizationType())) {
                    monitorAuthorizationMapper.updateMonitorAuthorizationInfo(existingAuthorization.getId(), "0");
                    monitorAssetMapper.updateAuthorizationType(existingAuthorization.getMonitorAssetId(), "0", DateUtil.toLocalDateTime(new Date()));
//                    monitorAuthorizationUserMapper.deleteAuthorizationUserTypeStatus(existingAuthorization.getMonitorAssetId(), existingAuthorization.getUserId());
                    if (redisTemplate.hasKey(redisKey)) {
                        String json = redisTemplate.opsForValue().get(redisKey).toString();
                        WxTempLateBtnVo wxTempLateBtnVo = JsonUtils.parseObject(json, WxTempLateBtnVo.class);
                        String date = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
                        wxTempLateBtnVo.getHorizontal_content_list().get(3).setValue(date);
                        wxTempLateBtnVo.getButton_list().clear();
                        List<WxTempLateBtnVo.ButtonListBean> buttonList = new ArrayList<>();
                        wxTempLateBtnVo.getMain_title().setDesc("资产接入申请已过期，如有需要请重新申请");
                        buttonList.add(createButtonBean("已过期", 4, "button_key_1"));
                        wxTempLateBtnVo.setButton_list(buttonList);
                        updateTemplateBtnMsg(phoneNumber, wxTempLateBtnVo.getAppointId(), wxTempLateBtnVo);
                        log.info("企微过期卡片更新");
                    }
                }
            } catch (Exception e) {
                log.error("Error while updating authorization status: ", e);
            }
        }, 20, TimeUnit.MINUTES);
    }

    private void scheduleStatusCheckDingtalk(MonitorAuthorizationDO monitorAuthorizationDO,
                                       Map<String, String> templateParams, String accessToken,String redisKey,
                                             int status) {
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
        scheduler.schedule(() -> {
            try {
                MonitorAuthorizationDO existingAuthorization = monitorAuthorizationMapper.selectById(monitorAuthorizationDO.getId());
                if (existingAuthorization != null && "1".equals(existingAuthorization.getAuthorizationType())) {
                    monitorAuthorizationMapper.updateMonitorAuthorizationInfo(existingAuthorization.getId(), "0");
                    monitorAssetMapper.updateAuthorizationType(existingAuthorization.getMonitorAssetId(), "0", DateUtil.toLocalDateTime(new Date()));
//                    monitorAuthorizationUserMapper.deleteAuthorizationUserTypeStatus(existingAuthorization.getMonitorAssetId(), existingAuthorization.getUserId());
                    if (redisTemplate.hasKey(redisKey)) {
                        ObjectMapper objectMapper = new ObjectMapper();
                        String jsonString = redisTemplate.opsForValue().get(redisKey).toString();
                        Map<String, String> stringStringMap = objectMapper.readValue(jsonString, new TypeReference<Map<String, String>>() {});
                        String date = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
                        stringStringMap.put("dateEnd", date);
                        DingTalkUtil.updateCardMessage("3", accessToken, templateParams.get("uuid"), templateParams);
                        log.info("钉钉过期卡片更新");
                    }
                }
            } catch (Exception e) {
                log.error("Error while updating authorization status: ", e);
            }
        }, 20, TimeUnit.MINUTES);
    }
    private String getAccessToken() {
        String token = QyWxUtils.getToken(appKey, appSecret);
        if (jodd.util.StringUtil.isEmpty(token)) {
            log.error("企微token获取为空");
            throw new RuntimeException("企微token获取为空");
        }
        return token;
    }

    private String executeRequest(String endpoint, Map<String, Object> params) {
        String token = getAccessToken();
        HttpRequest post = HttpRequest.post(BASE_URL + endpoint + "?access_token=" + token);
        post.body(JSON.toJSONString(params));
        log.info("企微请求参数: " + JSON.toJSONString(params));
        HttpResponse response = post.execute();
        String body = response.body();
        QyWxUtils.WeComResponse weComResponse = JsonUtils.parseObject2(body, QyWxUtils.WeComResponse.class);
        log.info("企微响应数据: " + body);
        return weComResponse.getResponse_code();
    }

    // 发送消息通知 带交互按钮
    public String sendNotifyBtn(String userId, WxTempLateBtnVo wxMessageVo) {
        Map<String, Object> params = new LinkedHashMap<>();
        params.put("touser", userId);
        params.put("msgtype", MSG_TYPE);
        params.put("agentid", agentId);
        params.put("template_card", wxMessageVo);
        String code = executeRequest("send", params);
        return code;
    }

    /**
     * 更新交互模板
     * 该函数与sendNotifyBtn只有一处url不同
     * @param userId
     * @param wxMessageVo
     */
    public void updateTemplateBtnMsg(String userId, String responseCode, WxTempLateBtnVo wxMessageVo) {
        Map<String, Object> params = new LinkedHashMap<>();
        params.put("userids", Collections.singletonList(userId));
        params.put("response_code", responseCode);
        params.put("msgtype", MSG_TYPE);
        params.put("agentid", agentId);
        params.put("template_card", wxMessageVo);
        executeRequest("update_template_card", params);
    }

    /**
     * 推送交互按钮卡片模板
     * @param userId
     */
    public void sendMessageNotify(String userId, Map<String, String> templateParams,String redisKey,MonitorAuthorizationDO monitorAuthorizationDO) {
        WxTempLateBtnVo wxTempLateBtnVo = createTemplateCard(templateParams);
        String code = sendNotifyBtn(userId, wxTempLateBtnVo);
        if(!redisTemplate.hasKey(redisKey)){
            wxTempLateBtnVo.setAppointId(code);
            wxTempLateBtnVo.setAuthuserId(templateParams.get("authuserId"));
            wxTempLateBtnVo.setUserId(userId);
            wxTempLateBtnVo.setAssetId(monitorAuthorizationDO.getMonitorAssetId());
            wxTempLateBtnVo.setAuthId(monitorAuthorizationDO.getId());
            redisTemplate.opsForValue().set(redisKey, JsonUtils.toJsonString(wxTempLateBtnVo),21,TimeUnit.MINUTES);
        }
    }

    private void handleAuthorization(String type, Long id) {
        MonitorAuthorizationUserDO monDO = monitorAuthorizationUserMapper.selectById(id);
        if ("0".equals(type)) {
            monitorAuthorizationUserMapper.deleteAuthorizationUserType(monDO.getMonitorAssetId(),monDO.getAuthorizationUserId());
        } else {
            monDO.setAuthorizationType("2");
            monitorAuthorizationUserMapper.updateById(monDO);
//            monitorAuthorizationUserMapper.deleteAuthorizationUserTypeStatus(monDO.getMonitorAssetId(),monDO.getAuthorizationUserId());
        }
    }

    private WxTempLateBtnVo createTemplateCard(Map<String, String> templateParams) {
        WxTempLateBtnVo wxTempLateBtnVo = new WxTempLateBtnVo();
        wxTempLateBtnVo.setCard_type("button_interaction");

        WxTempLateBtnVo.MainTitleBean mainTitleBean = new WxTempLateBtnVo.MainTitleBean();
        mainTitleBean.setTitle("授权申请");
        mainTitleBean.setDesc("您的资产正在申请接入授权，如果您同意，申请人将可接入下述资产，申请人的所有操作将会被记录和审计。");
        wxTempLateBtnVo.setMain_title(mainTitleBean);

        wxTempLateBtnVo.setCard_action(null);
        wxTempLateBtnVo.setTask_id( templateParams.get("uuid"));

        List<WxTempLateBtnVo.HorizontalContentListBean> horizontalContentList = new ArrayList<>();
        horizontalContentList.add(createContentBean("资源名称", templateParams.get("assetName")));
        horizontalContentList.add(createContentBean("主机地址", templateParams.get("hostName")));
        horizontalContentList.add(createContentBean("申请时间", templateParams.get("date")));
        horizontalContentList.add(createContentBean("结束时间", templateParams.get("dateEnd")));
        horizontalContentList.add(createContentBean("申请人", templateParams.get("nickname")));

        wxTempLateBtnVo.setHorizontal_content_list(horizontalContentList);

        List<WxTempLateBtnVo.ButtonListBean> buttonList = new ArrayList<>();
        buttonList.add(createButtonBean("同意", 1, "button_key_1"));
        buttonList.add(createButtonBean("拒绝", 3, "button_key_2"));

        wxTempLateBtnVo.setButton_list(buttonList);
        return wxTempLateBtnVo;
    }

    private WxTempLateBtnVo.HorizontalContentListBean createContentBean(String keyName, String value) {
        WxTempLateBtnVo.HorizontalContentListBean bean = new WxTempLateBtnVo.HorizontalContentListBean();
        bean.setKeyname(keyName);
        bean.setValue(value);
        return bean;
    }

    private WxTempLateBtnVo.ButtonListBean createButtonBean(String text, int style, String key) {
        WxTempLateBtnVo.ButtonListBean bean = new WxTempLateBtnVo.ButtonListBean();
        bean.setText(text);
        bean.setStyle(style);
        bean.setKey(key);
        return bean;
    }

    public static String addOneMinute() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime result = now.plusMinutes(20);
        return result.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm"));
    }

    public static String addOneMinuteTwo() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime result = now.plusMinutes(20);
        return result.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    private static String formatKey(String key) {
        return String.format(AUTH_KEY, key);
    }
}
