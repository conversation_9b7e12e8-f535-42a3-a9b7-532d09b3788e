package cn.iocoder.zj.module.om.dal.dataobject.inspectionlogs;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 自动巡检异常记录 DO
 *
 * <AUTHOR>
 */
@TableName("om_inspection_logs")
@KeySequence("om_inspection_logs_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InspectionLogsDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 巡检规则id
     */
    private Long inspectionId;
    /**
     * 巡检记录id
     */
    private String recordUuid;
    /**
     * 巡检名称
     */
    private String inspectionName;
    /**
     * 阈值
     */
    private BigDecimal threshold;
    /**
     * 资产名称
     */
    private String assetName;
    /**
     * 资产id
     */
    private String assetUuid;
    /**
     * 平台id
     */
    private String platformId;
    /**
     * 平台名称
     */
    private String platformName;

    private Double cpu;
    private Double mem;
    private Double disk;
    private String abnormalItem;
    private String assetType;

    /**
     * 创建人名称
     */
    private String creatorName;
    /**
     * 检测结果
     */
    private String result;

}
