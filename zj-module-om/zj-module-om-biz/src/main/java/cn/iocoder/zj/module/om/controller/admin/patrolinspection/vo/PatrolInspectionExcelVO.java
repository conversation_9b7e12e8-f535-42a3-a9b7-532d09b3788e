package cn.iocoder.zj.module.om.controller.admin.patrolinspection.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;
import org.apache.poi.hpsf.Decimal;

/**
 * 自动巡检规则 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class PatrolInspectionExcelVO {

    @ExcelProperty("名称")
    private String name;

    @ExcelProperty("规则类型")
    private String type;

    @ExcelProperty("数据源")
    private String dataResource;

    @ExcelProperty(value = "阈值")
    private BigDecimal threshold;

    @ExcelProperty(value ="分值")
    private BigDecimal value;

    @ExcelProperty(value ="资产类型")
    private String assetType;

    @ExcelProperty(value = "扣分规则")
    private String rule;

    @ExcelProperty(value ="计算公式")
    private String formula;

    @ExcelProperty(value ="参数")
    private String param;

}
