package cn.iocoder.zj.module.om.controller.admin.patrolinspectionconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 巡检设置 Excel 导出 Request VO，参数和 PatrolInspectionConfigPageReqVO 是一致的")
@Data
public class PatrolInspectionConfigExportReqVO {

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "资源类型(存储资源/宿主机资源/云主机资源/云硬盘资源)")
    private String resourceType;

    @Schema(description = "指标名称(CPU分配/内存分配/网卡入速度等)")
    private String metricName;

    @Schema(description = "阈值类型(正常/低风险/中风险/高风险/异常)")
    private String thresholdType;

    @Schema(description = "比较运算符(>, <, >=, <=, =, !=, between)',")
    private String operator;

    @Schema(description = "阈值值")
    private BigDecimal thresholdValue;

    @Schema(description = "阈值最大值(用于范围比较)")
    private BigDecimal thresholdValueMax;

    @Schema(description = "单位(%, MB/S, G等)")
    private String unit;

    @Schema(description = "持续时间")
    private Integer duration;

    @Schema(description = "持续时间单位(分钟/小时/天)")
    private String durationUnit;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
