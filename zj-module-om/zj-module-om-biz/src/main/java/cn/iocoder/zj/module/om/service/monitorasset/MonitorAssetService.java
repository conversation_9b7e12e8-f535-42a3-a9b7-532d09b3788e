package cn.iocoder.zj.module.om.service.monitorasset;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.om.api.asset.dto.MonitorAssetDTO;
import cn.iocoder.zj.module.om.controller.admin.monitorasset.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.monitorasset.MonitorAssetDO;
import com.alibaba.fastjson.JSONObject;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 监控资产 Service 接口
 *
 * <AUTHOR>
 */
public interface MonitorAssetService {

    /**
     * 创建监控资产
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMonitorAsset(@Valid MonitorAssetCreateReqVO createReqVO);

    /**
     * 更新监控资产
     *
     * @param updateReqVO 更新信息
     */
    void updateMonitorAsset(@Valid MonitorAssetUpdateReqVO updateReqVO);

    /**
     * 删除监控资产
     *
     * @param id 编号
     */
    void deleteMonitorAsset(Long id);

    /**
     * 获得监控资产
     *
     * @param id 编号
     * @return 监控资产
     */
    MonitorAssetDO getMonitorAsset(Long id);

    /**
     * 获得监控资产列表
     *
     * @param ids 编号
     * @return 监控资产列表
     */
    List<MonitorAssetDO> getMonitorAssetList(Collection<Long> ids);

    /**
     * 获得监控资产分页
     *
     * @param pageReqVO 分页查询
     * @return 监控资产分页
     */
    PageResult<MonitorAssetDO> getMonitorAssetPage(MonitorAssetPageReqVO pageReqVO);

    /**
     * 获得监控资产列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 监控资产列表
     */
    List<MonitorAssetDO> getMonitorAssetList(MonitorAssetExportReqVO exportReqVO);

    List<MonitorAssetSelectRespVO> getMonitorAssetSelect(Long platformId,String category);

    MonitorAssetDTO getAssetInfoById(Long id);

    boolean authorization(Long platformId, Long id);

    boolean weworkAuthorization(Long platformId, Long id);

    String weworkCallback(String msgSignature, String timestamp, String nonce, String echoStr, String xml);

    String dingtalkCallback(JSONObject body);

    String checkSign(String msgSignature, String timestamp, String nonce, String echoStr, String xml);

    boolean dingtalkAuthorization(Long platformId, Long id);

    boolean selectAuthorization(Long platformId, Long id);
    boolean updateAuthorizationTypeImpl(MonitorAssetUpdateTypeReqVO monitorAssetUpdateTypeReqVO);

    void updateAssetById(MonitorAssetDTO checkedData);

    MonitorAssetDTO getAssetInfoByIdAndFormId(String monitorId, Long platformId);

    void updateAssetByAssetId(MonitorAssetDTO dto);

    void updateAsset(Long id, String name);
}
