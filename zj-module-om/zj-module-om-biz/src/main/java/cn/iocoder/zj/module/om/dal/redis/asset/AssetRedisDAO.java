package cn.iocoder.zj.module.om.dal.redis.asset;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;

import cn.iocoder.zj.module.om.dal.dataobject.monitorauthorization.MonitorAuthorizationDO;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.zj.module.om.dal.redis.RedisKeyConstants.AUTHORIZAT;


/**
 * @ClassName : AssetRedisDAO  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/7/17  12:34
 */
@Repository
public class AssetRedisDAO {
    @Resource
    private StringRedisTemplate stringRedisTemplate;


    public MonitorAuthorizationDO get(String key) {
        String redisKey = formatKey(key);
        return JsonUtils.parseObject(stringRedisTemplate.opsForValue().get(redisKey), MonitorAuthorizationDO.class);
    }
    public void set(String key, MonitorAuthorizationDO monitorAuthorizationDO) {
        String redisKey = formatKey(key);
        stringRedisTemplate.opsForValue().set(redisKey, JsonUtils.toJsonString(monitorAuthorizationDO), 20, TimeUnit.MINUTES);
    }

    public void delete(String key) {
        String redisKey = formatKey(key);
        stringRedisTemplate.delete(redisKey);
    }

    private static String formatKey(String key) {
        return String.format(AUTHORIZAT, key);
    }
}
