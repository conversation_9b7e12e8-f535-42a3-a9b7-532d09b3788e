package cn.iocoder.zj.module.om.Inspection.metric.handler;

import cn.iocoder.zj.module.om.Inspection.data.PatrolCompareResult;
import cn.iocoder.zj.module.om.dal.dataobject.patrolabnormaldetail.PatrolAbnormalDetailDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspectionconfig.PatrolInspectionConfigDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolresultcategory.PatrolResultCategoryDO;
import cn.iocoder.zj.module.om.enums.PatrolMetricEnum;
import cn.iocoder.zj.module.om.enums.PatrolResourceTypeEnum;
import cn.iocoder.zj.module.om.enums.PatrolRiskEnum;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

import static cn.iocoder.zj.module.om.Inspection.util.InspectionUtil.convertDurationUnitToChinese;

/**
 * 指标处理器基类
 * 提供通用的指标处理逻辑
 */
@Getter
public abstract class MetricHandler<T> {

    /**
     * -- GETTER --
     * 获取指标类型
     */
    protected final PatrolMetricEnum metricType;

    public MetricHandler(PatrolMetricEnum metricType) {
        this.metricType = metricType;
    }

    /**
     * 获取资源类型
     */
    public abstract PatrolResourceTypeEnum getResourceType();

    /**
     * 过滤有效资源
     */
    protected abstract List<T> filterValidResources(List<?> resourceDataList);

    /**
     * 检查资源是否异常
     */
    protected abstract boolean checkResourceAbnormal(T resource, List<PatrolInspectionConfigDO> configList);

    /**
     * 创建异常详情
     */
    protected abstract PatrolAbnormalDetailDO createAbnormalDetail(T resource, List<PatrolInspectionConfigDO> configList, Long tenantId);

    /**
     * 比较资源与配置
     */
    public PatrolCompareResult compare(List<?> resourceDataList, List<PatrolInspectionConfigDO> configList, Long tenantId) {
        AtomicInteger normalCount = new AtomicInteger(0);
        AtomicInteger lowCount = new AtomicInteger(0);
        AtomicInteger mediumCount = new AtomicInteger(0);
        AtomicInteger highCount = new AtomicInteger(0);
        AtomicInteger abnormalCount = new AtomicInteger(0);
        AtomicInteger resourceCount = new AtomicInteger(0);

        // 异常资源列表
        List<PatrolAbnormalDetailDO> abnormalList = new ArrayList<>();

        if (resourceDataList != null && !resourceDataList.isEmpty()) {
            // 过滤有效资源
            List<T> validResources = filterValidResources(resourceDataList);

            // 处理每个资源
            validResources.forEach(resource -> {
                if (checkResourceAbnormal(resource, configList)) {
                    PatrolAbnormalDetailDO abnormalDetail = createAbnormalDetail(resource, configList, tenantId);
                    if (abnormalDetail != null) {
                        abnormalList.add(abnormalDetail);

                        // 根据风险等级计数
                        String riskLevel = abnormalDetail.getRiskLevel();
                        if (PatrolRiskEnum.LOW.getCode().equals(riskLevel)) {
                            lowCount.incrementAndGet();
                        } else if (PatrolRiskEnum.MEDIUM.getCode().equals(riskLevel)) {
                            mediumCount.incrementAndGet();
                        } else if (PatrolRiskEnum.HIGH.getCode().equals(riskLevel)) {
                            highCount.incrementAndGet();
                        } else {
                            abnormalCount.incrementAndGet();
                        }
                    } else {
                        normalCount.incrementAndGet();
                    }
                } else {
                    normalCount.incrementAndGet();
                }
            });

            resourceCount.set(validResources.size());
        }

        // 创建结果
        return createResult(normalCount, lowCount, mediumCount, highCount, abnormalCount, resourceCount, tenantId, abnormalList, configList);
    }

    /**
     * 创建结果
     */
    protected PatrolCompareResult createResult(AtomicInteger normalCount, AtomicInteger lowCount, AtomicInteger mediumCount,
                                               AtomicInteger highCount, AtomicInteger abnormalCount, AtomicInteger resourceCount,
                                               Long tenantId, List<PatrolAbnormalDetailDO> abnormalList, List<PatrolInspectionConfigDO> configList) {
        PatrolCompareResult result = new PatrolCompareResult();
        PatrolResultCategoryDO category = new PatrolResultCategoryDO();
        category.setNormalCount(normalCount.get());
        category.setLowRiskCount(lowCount.get());
        category.setMediumRiskCount(mediumCount.get());
        category.setHighRiskCount(highCount.get());
        category.setAbnormalCount(abnormalCount.get());
        category.setResourceCount(resourceCount.get());
        category.setTenantId(tenantId);
        category.setResourceType(getMetricType().getResourceType().getCode());
        category.setMetricName(getMetricType().getCode());
        category.setTenantId(tenantId);
        //判断 low  medium high abnormal 只要值大于0，则表示有异常
        AtomicInteger totalAbnormalCount = new AtomicInteger(lowCount.get() + mediumCount.get() + highCount.get() + abnormalCount.get());
        category.setStatus(totalAbnormalCount.get() > 0 ? 1 : 0);
        if (totalAbnormalCount.get() == 0) {
            if (configList != null && !configList.isEmpty()) {
                Optional<PatrolInspectionConfigDO> config = configList.stream().filter(c -> c.getThresholdType().equals("normal")).findFirst();
                config.ifPresent(c -> {
                    String unit = c.getUnit() != null ? c.getUnit() : "";
                    unit = convertDurationUnitToChinese(unit);
                    //判断是否持续
                    if (c.getDuration() != null && c.getDuration() > 0) {
                        if (getMetricType().getTitle().contains("环比")) {
                            category.setDetail(getMetricType().getTitle() + "低于" + c.getThresholdValue() + unit);
                        } else {
                            String durationUnit = convertDurationUnitToChinese(c.getDurationUnit());
                            category.setDetail(getMetricType().getTitle() + "持续" + c.getDuration() + durationUnit +
                                    "低于" + c.getThresholdValue() + unit);
                        }
                    } else {
                        if (c.getDiffRate() != null) {
                            if (c.getThresholdValue() != null) {
                                category.setDetail(getMetricType().getTitle() + "低于" + c.getThresholdValue() + unit +
                                        "或高于" + c.getThresholdValue() + unit + "但差值率大于等于" + c.getDiffRate() + "%");
                            } else {
                                category.setDetail(getMetricType().getTitle() + "差值率高于" + c.getDiffRate() + "%");
                            }

                        } else {
                            category.setDetail(getMetricType().getTitle() + "低于" + c.getThresholdValue() + unit);
                        }
                    }
                });
            } else {
                category.setDetail(getMetricType().getTitle() + "正常");
            }
        }
        result.setCategory(category);
        result.setAbnormalDetails(abnormalList);
        result.setTotalAbnormalCount(totalAbnormalCount.get());
        return result;
    }


    /**
     * 检查origins列表中是否存在持续duration时间的值都大于等于阈值
     *
     * @param origins        数据值列表
     * @param thresholdValue 阈值
     * @param duration       持续时间数值
     * @param durationUnit   持续时间单位
     * @return 是否存在持续满足条件的数据
     */
    protected boolean checkContinuousThreshold(List<String> origins, BigDecimal thresholdValue,
                                               Integer duration, String durationUnit) {
        if (origins == null || origins.isEmpty() || duration == null || duration <= 0) {
            return false;
        }

        // 计算需要连续满足条件的数据点数量
        int requiredContinuousCount = calculateRequiredDataPoints(duration, durationUnit);

        if (requiredContinuousCount <= 0 || requiredContinuousCount > origins.size()) {
            return false;
        }

        // 遍历origins列表，寻找满足条件的起始点
        for (int i = 0; i <= origins.size() - requiredContinuousCount; i++) {
            // 检查当前位置的值是否大于等于阈值
            try {
                BigDecimal currentValue = new BigDecimal(origins.get(i));
                if (currentValue.compareTo(thresholdValue) >= 0) {
                    // 从当前位置开始检查是否有连续requiredContinuousCount个值都大于等于阈值
                    boolean isContinuous = true;

                    for (int j = i; j < i + requiredContinuousCount; j++) {
                        BigDecimal checkValue = new BigDecimal(origins.get(j));
                        if (checkValue.compareTo(thresholdValue) < 0) {
                            isContinuous = false;
                            // 跳到第一个不满足条件的位置，继续寻找下一个起始点
                            i = j;
                            break;
                        }
                    }

                    if (isContinuous) {
                        return true; // 找到了满足条件的连续数据段
                    }
                }
            } catch (NumberFormatException e) {
                // 如果数据格式不正确，跳过该数据点
                continue;
            }
        }

        return false; // 没有找到满足条件的连续数据段
    }

    /**
     * 根据持续时间和单位计算需要的数据点数量
     *
     * @param duration     持续时间数值
     * @param durationUnit 持续时间单位 (m-分钟, h-小时, D-天)
     * @return 需要的数据点数量
     */
    protected int calculateRequiredDataPoints(Integer duration, String durationUnit) {
        if (duration == null || duration <= 0 || durationUnit == null) {
            return 0;
        }

        // origins列表中的数据点是每分钟一个点
        // 所以当durationUnit是"m"时，直接返回duration值
        // 当是其他单位时，需要转换为分钟数
        return switch (durationUnit) {
            case "m" -> duration; // 分钟：直接返回duration值
            case "h" -> duration * 60; // 小时：转换为分钟数
            case "D" -> duration * 24 * 60; // 天：转换为分钟数
            default -> duration; // 默认按分钟处理
        };
    }
}