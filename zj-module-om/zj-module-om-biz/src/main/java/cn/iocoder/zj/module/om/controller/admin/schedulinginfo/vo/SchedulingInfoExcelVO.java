package cn.iocoder.zj.module.om.controller.admin.schedulinginfo.vo;

import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 运维排班信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SchedulingInfoExcelVO {

    @ExcelProperty("主键id")
    private Long id;

    @ExcelProperty("实施人id")
    private String enforcerId;

    @ExcelProperty("实施人名称")
    private String enforcerName;

    @ExcelProperty("开始时间")
    private Date startTime;

    @ExcelProperty("结束时间")
    private Date endTime;

    @ExcelProperty("备注")
    private String remarks;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private Date createTime;

}
