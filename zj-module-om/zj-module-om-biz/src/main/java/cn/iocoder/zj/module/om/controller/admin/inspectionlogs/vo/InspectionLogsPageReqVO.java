package cn.iocoder.zj.module.om.controller.admin.inspectionlogs.vo;

import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 自动巡检异常记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InspectionLogsPageReqVO extends PageParam {

    @Schema(description = "巡检记录id")
    private String recordUuid;

    @Schema(description = "巡检名称")
    private String inspectionName;

    @Schema(description = "阈值")
    private BigDecimal threshold;

    @Schema(description = "资产名称")
    private String assetName;

    @Schema(description = "资产id")
    private String assetUuid;

    @Schema(description = "平台id")
    private String platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "资产的值")
    private String value;

    @Schema(description = "创建人名称")
    private String creatorName;

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] startTime;


    @Schema(description = "检测结果")
    private String result;

}
