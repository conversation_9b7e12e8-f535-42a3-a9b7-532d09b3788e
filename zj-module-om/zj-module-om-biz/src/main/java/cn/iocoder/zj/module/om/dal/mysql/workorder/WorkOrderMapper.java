package cn.iocoder.zj.module.om.dal.mysql.workorder;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.module.om.dal.dataobject.workorder.OrderProcessDO;
import cn.iocoder.zj.module.om.dal.dataobject.workorder.WorkOrderDO;
import cn.iocoder.zj.module.om.util.StringUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.om.controller.admin.workorder.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 工单管理数据 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WorkOrderMapper extends BaseMapperX<WorkOrderDO> {

    List<WorkOrderDO> getWorkOrderPage(@Param("mpPage") IPage<WorkOrderDO> mpPage,
                                             @Param("reqVO")WorkOrderPageReqVO reqVO,
                                             @Param("tenantUsers")List<Long> tenantUsers,
                                             @Param("isTenantAdmin")boolean isTenantAdmin);

    default List<WorkOrderDO> getWorkOrderList(WorkOrderExportReqVO reqVO,List<Long> homologousUsers){
        LambdaQueryWrapperX<WorkOrderDO> wrapper = new LambdaQueryWrapperX<>();
        if(StringUtil.isNotEmpty(reqVO.getName())){
            wrapper.likeIfPresent(WorkOrderDO::getName,reqVO.getName());
        }
        if(StringUtil.isNotEmpty(reqVO.getSourceName())){
            wrapper.likeIfPresent(WorkOrderDO::getSourceName,reqVO.getSourceName());
        }
        if(StringUtil.isNotEmpty(reqVO.getEnforcerName())){
            wrapper.likeIfPresent(WorkOrderDO::getEnforcerName,reqVO.getEnforcerName());
        }
        if(StringUtil.isNotEmpty(reqVO.getDictCode())){
            wrapper.eqIfPresent(WorkOrderDO::getDictCode,reqVO.getDictCode());
        }
        if(StringUtil.isNotEmpty(reqVO.getTypeCode())){
            wrapper.eqIfPresent(WorkOrderDO::getTypeCode,reqVO.getTypeCode());
        }
        if(StringUtil.isNotEmpty(reqVO.getRatifyStatus())){
            wrapper.eqIfPresent(WorkOrderDO::getRatifyStatus,reqVO.getRatifyStatus());
        }
        if(reqVO.getCreator()!=null){
            wrapper.eqIfPresent(WorkOrderDO::getCreator,reqVO.getCreator());
        }
        if(reqVO.getPlatformIds()!= null && reqVO.getPlatformIds().size()>0){
            wrapper.inIfPresent(WorkOrderDO::getPlatformId,reqVO.getPlatformIds());
        }
        if(homologousUsers!= null && homologousUsers.size()>0){
            wrapper.inIfPresent(WorkOrderDO::getCreator,homologousUsers);
        }
        if(StringUtil.isNotEmpty(reqVO.getStartTime()) && StringUtil.isNotEmpty(reqVO.getEndTime())){
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            try {
                date = sdf.parse(reqVO.getEndTime());
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(date);
                // 把日期设置为当天最后一秒
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                date = calendar.getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            wrapper.betweenIfPresent(WorkOrderDO::getCreateTime,reqVO.getStartTime(),sdf.format(date));
        }
        wrapper.eq(WorkOrderDO::getDeleted,0);
        wrapper.orderByDesc(WorkOrderDO::getId);
        return selectList(wrapper);
    }



    List<WorkOrderRespVO> listOnDay(@Param("day") String day,@Param("page") Page page,@Param("tenantId")Long tenantId);


    Map<String, String> getWorkOrderStatistics(@Param("userIds") List<Long> userIds,@Param("hasPermission")boolean hasPermission);

    List<Map<String, String>>  getWorkOrderStatisticsInWeek(@Param("userIds")List<Long> userIds,
                                                            @Param("hasPermission")Boolean hasPermission);


    Integer getWorkerOrderCountBySourceUuid(@Param("uuid")String uuid,
                                            @Param("type")String type,
                                            @Param("actions")String actions);

    List<Long> getHomologousUsers(@Param("userId")Long userId,@Param("roleCode")String roleCode);

    Map<String, String> getCountVal(@Param("userIds")List<Long> userIds);

    List<Map<String, String>> getRanking(@Param("userIds")List<Long> userIds,@Param("hasPermission")Boolean hasPermission);

    void addOrderProcessResult(@Param("orderProcessDO")OrderProcessDO orderProcessDO);

    void deleteOrderProcess(@Param("workOrderId")Long workOrderId);

    List<OrderProcessBaseVo> getOrderProcessListByWorkOrderId(@Param("workOrderId")Long workOrderId);

    Map<String, String> getPersonWorkOrderInfo(@Param("userId")Long userId,
                                               @Param("role")String role);

    List<Map<String, String>> getWorkOrderSourceType(@Param("userIds")List<Long> homologousUsers,
                                                     @Param("hasPermission")Boolean hasPermission);

    List<Map<String, String>> getCreateInWeek(@Param("dateStrList")List<String> dateStrList,
                                              @Param("userIds") List<Long> homologousUsers,
                                              @Param("hasPermission")boolean hasPermission);

    String getLatestRejectReason(@Param("id")Long id);

    OrderProcessBaseVo getOrderProcessRatifyByWorkOrderId(@Param("workOrderId")Long workOrderId);

    OrderProcessBaseVo getOrderProcessSolvedByWorkOrderId(@Param("workOrderId")Long workOrderId);

    default Long getWorkOrderCount(Long id,String name) {
        return selectCount(new LambdaQueryWrapperX<WorkOrderDO>()
                .likeIfPresent(WorkOrderDO::getEnforcerId, id.toString())
                .likeIfPresent(WorkOrderDO::getEnforcerName, name)
                .eq(WorkOrderDO::getIsFinished, 0));
    }

    default WorkOrderDO getWorkOrderByContractId(String contractId){
        return selectOne(new LambdaQueryWrapperX<WorkOrderDO>()
                .eq(WorkOrderDO::getContractId, contractId));
    }

    WorkOrderDO getById(@Param("id") Long id);

    List<WorkOrderRespVO> getUnsolvedWorkOrder(@Param("user") LoginUser user);

    void updateIsRead(@Param("ids") Collection<Long> ids);
}
