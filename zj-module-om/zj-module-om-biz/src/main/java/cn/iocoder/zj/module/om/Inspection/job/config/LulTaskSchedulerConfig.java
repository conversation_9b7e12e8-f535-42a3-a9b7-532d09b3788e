package cn.iocoder.zj.module.om.Inspection.job.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

@Configuration
public class LulTaskSchedulerConfig {

    @Bean
    @Primary
    public ThreadPoolTaskScheduler lulTaskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(50);
        scheduler.setThreadNamePrefix("lulTask-");
        scheduler.initialize();
        return scheduler;
    }
}
