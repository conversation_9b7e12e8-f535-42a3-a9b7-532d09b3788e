package cn.iocoder.zj.module.om.Inspection.metric.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.MetricHistoryData;
import cn.iocoder.zj.framework.warehouse.service.ZjMetricsDataService;
import cn.iocoder.zj.module.om.dal.dataobject.patrolabnormaldetail.PatrolAbnormalDetailDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspectionconfig.PatrolInspectionConfigDO;
import cn.iocoder.zj.module.om.enums.PatrolMetricEnum;
import cn.iocoder.zj.module.om.enums.PatrolResourceTypeEnum;
import org.apache.hertzbeat.common.entity.dto.Value;
import org.apache.hertzbeat.common.support.SpringContextHolder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Predicate;

import static cn.iocoder.zj.module.om.Inspection.util.InspectionUtil.convertDurationUnitToChinese;

public class ZjVmThresholdMetricHandler<T> extends MetricHandler<T> {


    private final PatrolResourceTypeEnum resourceType;
    private final Class<T> resourceClass;
    private final Predicate<T> validResourceFilter;
    private final Function<T, String> resourceIdGetter;
    private final Function<T, String> resourceNameGetter;
    private final Function<T, String> platformIdGetter;
    private final Function<T, String> platformNameGetter;
    private final String metrics;
    private final String metric;
    private final String step;
    private final String type;
    private final Integer flag;// 0:非持续 1:持续 2:差值率,3:差值率+平均值,4:环比

    private BigDecimal hb_metricValue;

    //满足条件的阈值配置
    private PatrolInspectionConfigDO patrolInspectionConfigDO;

    public ZjVmThresholdMetricHandler(PatrolMetricEnum metricType, PatrolResourceTypeEnum resourceType,
                                      Class<T> resourceClass, Predicate<T> validResourceFilter,
                                      Function<T, String> resourceIdGetter, Function<T, String> resourceNameGetter,
                                      Function<T, String> platformIdGetter, Function<T, String> platformNameGetter,
                                      String metrics, String metric, String step, String type,
                                      Integer flag) {
        super(metricType);
        this.resourceType = resourceType;
        this.resourceClass = resourceClass;
        this.validResourceFilter = validResourceFilter;
        this.resourceIdGetter = resourceIdGetter;
        this.resourceNameGetter = resourceNameGetter;
        this.platformIdGetter = platformIdGetter;
        this.platformNameGetter = platformNameGetter;
        this.metrics = metrics;
        this.metric = metric;
        this.step = step;
        this.type = type;
        this.flag = flag;
    }

    @Override
    public PatrolResourceTypeEnum getResourceType() {
        return resourceType;
    }

    @Override
    protected List<T> filterValidResources(List<?> resourceDataList) {
        List<T> resources = BeanUtil.copyToList(resourceDataList, resourceClass);
        if (validResourceFilter != null) {
            return resources.stream().filter(validResourceFilter).toList();
        }
        return resources;
    }

    @Override
    protected boolean checkResourceAbnormal(T resource, List<PatrolInspectionConfigDO> configList) {
        String uuid = resourceIdGetter.apply(resource);
        BigDecimal metricValue = null;
        List<String> origins = List.of();
        //差值率
        BigDecimal diffRate = null;
        AtomicReference<String> zjMetricsDataStr = new AtomicReference<>();
        //拿第一条获取持续天数和单位
        PatrolInspectionConfigDO config_1 = configList.get(0);
        //巡检周期
        Integer patrolTime = config_1.getPatrolTime();
        Integer duration_1 = config_1.getDuration();
        String durationUnit_1 = config_1.getDurationUnit();
        String queryDuration;
        boolean isDuration = duration_1 != null && duration_1 > 0 && durationUnit_1 != null;
        if (isDuration) {
            queryDuration = duration_1 + durationUnit_1;
        } else {
            queryDuration = patrolTime + "D";
        }
        String stepQry = step;
        if (stepQry == null || stepQry.isEmpty()) {
            stepQry = queryDuration;
        }

        ZjMetricsDataService zjMetricsDataService = SpringContextHolder.getBean(ZjMetricsDataService.class);
        //持续需要查询巡检周期内的数据
        if (flag == 1) {
            // 根据巡检周期（天数）按天查询 metricHistoryData
            //
            // 逻辑说明：
            // 1. 巡检周期以天为单位，例如：1天、2天、3天等
            // 2. 从当前时间往后推迟24小时为一个时间段
            // 3. 按照巡检周期天数，分别查询每个24小时时间段的数据
            LocalDateTime now = LocalDateTime.now();
            Map<String, List<Value>> values = new HashMap<>();
            // 按照巡检周期天数，分别查询每个24小时时间段的数据
            for (int i = 0; i < patrolTime; i++) {
                // 计算每个24小时时间段的开始和结束时间
                LocalDateTime segmentEnd = now.minusDays(i);
                LocalDateTime segmentStart = segmentEnd.minusDays(1);

                // 转换为时间戳（毫秒）
                long startTimestamp = segmentStart.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                long endTimestamp = segmentEnd.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();

                // 查询当前24小时时间段的数据
                MetricHistoryData segmentData = zjMetricsDataService.getMetricHistoryData(
                        uuid, metrics, metric, null,
                        startTimestamp, endTimestamp, stepQry, type
                );

                // 合并数据
                if (segmentData != null && segmentData.getValues() != null) {
                    for (Map.Entry<String, List<Value>> entry : segmentData.getValues().entrySet()) {
                        String key = entry.getKey();
                        List<Value> segmentValues = entry.getValue();

                        if (segmentValues != null && !segmentValues.isEmpty()) {
                            values.computeIfAbsent(key, k -> new ArrayList<>()).addAll(segmentValues);
                        }
                    }
                }
            }

            // 对合并后的数据按时间戳排序
            for (List<Value> valueList : values.values()) {
                valueList.sort(Comparator.comparing(Value::getTime));
            }

            if (ObjectUtil.isEmpty(values)) {
                return false;
            }
            for (Map.Entry<String, List<Value>> entry : values.entrySet()) {
                String key = entry.getKey();
                if (key.equals(metric)) {
                    List<Value> valueList = entry.getValue();
                    if (metric.equals("DiskWriteBytes")
                            || metric.equals("DiskReadBytes")
                            || metric.equals("NetworkInBytes")
                            || metric.equals("NetworkOutBytes")) {
                        valueList = valueList.stream().peek(value -> {
                            try {
                                BigDecimal originValue = new BigDecimal(value.getOrigin());
                                BigDecimal mbValue = originValue.divide(new BigDecimal("1048576"), 2, RoundingMode.HALF_UP);
                                value.setOrigin(mbValue.stripTrailingZeros().toPlainString());
                            } catch (NumberFormatException e) {
                                // 处理非法数值格式，根据业务需求可记录日志或设默认值
                                value.setOrigin("0.00");
                            }
                        }).toList();
                    }
                    origins = valueList.stream().map(Value::getOrigin).toList();
                    break;
                }
            }

        } else if (flag == 0) {
            // 没有巡检周期，使用原有逻辑
            MetricHistoryData metricHistoryData = zjMetricsDataService.getMetricHistoryData(
                    uuid, metrics, metric, queryDuration, null, null, stepQry, type
            );
            if (metricHistoryData == null) {
                return false;
            }
            Map<String, List<Value>> values = metricHistoryData.getValues();
            if (ObjectUtil.isEmpty(values)) {
                return false;
            }
            for (Map.Entry<String, List<Value>> entry : values.entrySet()) {
                String key = entry.getKey();
                if (key.equals(metric)) {
                    List<Value> valueList = entry.getValue();
                    // 获取最大值
                    valueList.stream()
                            .max(Comparator.comparingDouble(v -> Double.parseDouble(v.getOrigin())))
                            .ifPresent(value -> zjMetricsDataStr.set(value.getOrigin()));
                    break;
                }
            }
            metricValue = new BigDecimal(zjMetricsDataStr.get() != null ? zjMetricsDataStr.get() : "0");
        } else if (flag == 2 || flag == 3) {
            //获取差值 获取一个最大值 一个最小值
            MetricHistoryData maxMetricHistoryData = zjMetricsDataService.getMetricHistoryData(
                    uuid, metrics, metric, queryDuration, null, null, stepQry, "max"
            );
            Map<String, List<Value>> maxValues = maxMetricHistoryData != null ? maxMetricHistoryData.getValues() : new HashMap<>();

            MetricHistoryData minMetricHistoryData = zjMetricsDataService.getMetricHistoryData(
                    uuid, metrics, metric, queryDuration, null, null, stepQry, "min"
            );
            Map<String, List<Value>> minValues = minMetricHistoryData != null ? minMetricHistoryData.getValues() : new HashMap<>();


            if (CollectionUtil.isNotEmpty(maxValues) && CollectionUtil.isNotEmpty(minValues)) {
                List<Value> maxValuesList = maxValues.get(metric);
                List<Value> minValuesList = minValues.get(metric);
                //差值率
                BigDecimal max = StrUtil.isNotEmpty(maxValuesList.get(0).getOrigin()) ? new BigDecimal(maxValuesList.get(0).getOrigin()) : BigDecimal.ZERO;
                BigDecimal min = StrUtil.isNotEmpty(minValuesList.get(0).getOrigin()) ? new BigDecimal(minValuesList.get(0).getOrigin()) : BigDecimal.ZERO;
                diffRate = max.subtract(min);
            } else {
                return false;
            }

            if (flag == 3) {
                //取个平均值
                MetricHistoryData meanMetricHistoryData = zjMetricsDataService.getMetricHistoryData(
                        uuid, metrics, metric, queryDuration, null, null, stepQry, "avg"
                );
                Map<String, List<Value>> avgValues = meanMetricHistoryData != null ? meanMetricHistoryData.getValues() : new HashMap<>();
                if (avgValues != null) {
                    List<Value> avgValuesList = avgValues.get(metric);
                    metricValue = new BigDecimal(StrUtil.isNotEmpty(avgValuesList.get(0).getOrigin()) ? avgValuesList.get(0).getOrigin() : "0");
                } else {
                    return false;
                }
            }
        } else if (flag == 4) {
            //环比计算 当前时间这个点查出来的数据与duration周期前一个时间点数据进行相减 计算出环比值
            // 1. 获取当前时间这个点数据
            MetricHistoryData metricHistory_current = zjMetricsDataService.getMetricHistoryData(
                    uuid, metrics, metric, "1m", null, null, "1m", type
            );
            Map<String, List<Value>> values_current = metricHistory_current != null ? metricHistory_current.getValues() : new HashMap<>();
            if (ObjectUtil.isEmpty(values_current)) {
                return false;
            }
            List<Value> valueList_current = values_current.get(metric);
            if (valueList_current == null || valueList_current.isEmpty()) {
                return false;
            }
            Value value_current = valueList_current.get(0);
            BigDecimal origin_current = StrUtil.isNotEmpty(value_current.getOrigin()) ? new BigDecimal(value_current.getOrigin()) : BigDecimal.ZERO;
            // 2. 获取duration周期前一个时间点数据
            MetricHistoryData metricHistory_front = zjMetricsDataService.getMetricHistoryData(
                    uuid, metrics, metric, queryDuration, null, null, stepQry, type
            );
            Map<String, List<Value>> values_front = metricHistory_front != null ? metricHistory_front.getValues() : new HashMap<>();
            if (ObjectUtil.isEmpty(values_front)) {
                return false;
            }
            List<Value> valueList_front = values_front.get(metric);
            if (valueList_front == null || valueList_front.isEmpty()) {
                return false;
            }
            Value value_front = valueList_front.get(0);
            BigDecimal origin_front = StrUtil.isNotEmpty(value_front.getOrigin()) ? new BigDecimal(value_front.getOrigin()) : BigDecimal.ZERO;
            metricValue = origin_current.subtract(origin_front);
            hb_metricValue = metricValue;
        }

        for (PatrolInspectionConfigDO config : configList) {
            String operator = config.getOperator();
            Integer duration = config.getDuration();
            String durationUnit = config.getDurationUnit();
            BigDecimal thresholdValue = config.getThresholdValue();
            BigDecimal thresholdValueMax = config.getThresholdValueMax();
            BigDecimal diffRate1 = config.getDiffRate();
            String diffRateOperator = config.getDiffRateOperator();
            if (flag == 1) {
                if (">=".equals(operator)) {
                    // 实现持续性检查逻辑：
                    // 1. 找到大于等于阈值的值并记录下标
                    // 2. 从该下标开始检查持续duration时间的值是否都大于等于阈值
                    // 3. 如果满足条件返回true，否则继续从下一个满足条件的下标开始检查
                    // 4. 直到检查完所有数据，没有满足条件则返回false
                    boolean isAbnormal = checkContinuousThreshold(origins, thresholdValue, duration, durationUnit);
                    if (isAbnormal) {
                        patrolInspectionConfigDO = config;
                    }
                    return isAbnormal;
                }
            } else if (flag == 0 || flag == 4) {
                if (">=".equals(operator) && metricValue.compareTo(thresholdValue) >= 0) {
                    patrolInspectionConfigDO = config;
                    return true;
                } else if ("between".equals(operator) && metricValue.compareTo(thresholdValue) >= 0
                        && metricValue.compareTo(thresholdValueMax) < 0) {
                    patrolInspectionConfigDO = config;
                    return true;
                } else if ("<".equals(operator) && metricValue.compareTo(thresholdValue) < 0) {
                    return false;
                }
            } else if (flag == 2) {
                if (">=".equals(diffRateOperator) && diffRate.compareTo(diffRate1) >= 0) {
                    patrolInspectionConfigDO = config;
                    return true;
                }
            } else if (flag == 3) {
                if (">=".equals(operator)) {
                    boolean isAbnormal = metricValue.compareTo(thresholdValue) >= 0;
                    if (!isAbnormal) {
                        return false;
                    }
                    if ("<".equals(diffRateOperator) && diffRate.compareTo(diffRate1) < 0) {
                        patrolInspectionConfigDO = config;
                        return true;
                    }
                }
            }
        }
        return false;
    }

    @Override
    protected PatrolAbnormalDetailDO createAbnormalDetail(T resource, List<PatrolInspectionConfigDO> configList, Long tenantId) {
        if (patrolInspectionConfigDO == null) {
            return null;
        }
        Integer patrolTime = patrolInspectionConfigDO.getPatrolTime();
        String unit = patrolInspectionConfigDO.getUnit();
        Integer duration = patrolInspectionConfigDO.getDuration();
        String durationUnit = patrolInspectionConfigDO.getDurationUnit();
        BigDecimal thresholdValue = patrolInspectionConfigDO.getThresholdValue();
        BigDecimal diffRate = patrolInspectionConfigDO.getDiffRate();
        String riskLevel = patrolInspectionConfigDO.getThresholdType();
        PatrolAbnormalDetailDO abnormalDetail = new PatrolAbnormalDetailDO();
        String detail = getResourceType().getName() + resourceNameGetter.apply(resource)
                + getMetricType().getName() + "在过去" + patrolTime + "天内，";
        if (flag == 0) {
            detail += "大于等于" + thresholdValue + unit;
        } else if (flag == 1) {
            durationUnit = convertDurationUnitToChinese(durationUnit);
            detail += "存在持续" + duration + durationUnit + "大于等于" + thresholdValue + unit;
        } else if (flag == 2) {
            detail += "差值率大于等于" + diffRate + "%";
        } else if (flag == 3) {
            detail += "大于等于" + thresholdValue + unit + "且差值率小于" + diffRate + "%";
        }
        if (flag == 4) {
            detail = getResourceType().getName() + resourceNameGetter.apply(resource)
                    + getMetricType().getName() + "为" + hb_metricValue + unit + ",阈值:" + thresholdValue + unit;
        }
        abnormalDetail.setDetail(detail);
        abnormalDetail.setResourceType(getResourceType().getCode());
        abnormalDetail.setMetricName(getMetricType().getCode());
        abnormalDetail.setResourceId(resourceIdGetter.apply(resource));
        abnormalDetail.setResourceName(resourceNameGetter.apply(resource));
        abnormalDetail.setPlatformId(Long.valueOf(platformIdGetter.apply(resource)));
        abnormalDetail.setPlatformName(platformNameGetter.apply(resource));
        abnormalDetail.setRiskLevel(riskLevel);
        abnormalDetail.setTenantId(tenantId);
        String suggest = getMetricType().getSuggest().get(riskLevel);
        abnormalDetail.setSuggest(suggest);

        return abnormalDetail;
    }
}
