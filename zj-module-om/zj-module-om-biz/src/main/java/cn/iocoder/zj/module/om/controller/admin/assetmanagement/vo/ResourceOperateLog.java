package cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo;

import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ResourceOperateLog extends BaseDO {
    @Schema(description = "主键")
    private Long id;

    @Schema(description = "资源uuid")
    private String resourceUuid;

    @Schema(description = "资源名称")
    private String resourceName;

    @Schema(description = "操作")
    private String operation;

    @Schema(description = "操作结果，success成功，error失败")
    private String result;

    @Schema(description = "备注")
    private String content;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "资源类型，云主机host,宿主机hardware,主存储storage")
    private String resourceType;
}
