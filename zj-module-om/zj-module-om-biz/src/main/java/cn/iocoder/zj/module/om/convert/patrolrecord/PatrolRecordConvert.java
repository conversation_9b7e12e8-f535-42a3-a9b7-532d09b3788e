package cn.iocoder.zj.module.om.convert.patrolrecord;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.om.controller.admin.patrolrecord.vo.PatrolRecordCreateReqVO;
import cn.iocoder.zj.module.om.controller.admin.patrolrecord.vo.PatrolRecordExcelVO;
import cn.iocoder.zj.module.om.controller.admin.patrolrecord.vo.PatrolRecordRespVO;
import cn.iocoder.zj.module.om.controller.admin.patrolrecord.vo.PatrolRecordUpdateReqVO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolrecord.PatrolRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 巡检记录 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface PatrolRecordConvert {

    PatrolRecordConvert INSTANCE = Mappers.getMapper(PatrolRecordConvert.class);

    PatrolRecordDO convert(PatrolRecordCreateReqVO bean);

    PatrolRecordDO convert(PatrolRecordUpdateReqVO bean);

    PatrolRecordRespVO convert(PatrolRecordDO bean);

    List<PatrolRecordRespVO> convertList(List<PatrolRecordDO> list);

    PageResult<PatrolRecordRespVO> convertPage(PageResult<PatrolRecordDO> page);

    List<PatrolRecordExcelVO> convertList02(List<PatrolRecordDO> list);

}
