package cn.iocoder.zj.module.om.controller.admin.patrolplan.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 巡检计划 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class PatrolPlanBaseVO {

    @Schema(description = "计划名称", required = true)
    @NotNull(message = "计划名称不能为空")
    private String name;

    @Schema(description = "巡检周期(day-日, week-周, month-月)", required = true)
    @NotNull(message = "巡检周期(day-日, week-周, month-月)不能为空")
    private String periodType;

    @Schema(description = "执行日(日-不需要填;周-(1-7);月-(1-31))", required = true)
    @NotNull(message = "执行日(周-(1-7);月-(1-31))不能为空")
    private String executionDay;

    @Schema(description = "执行时间点", required = true)
    @NotNull(message = "执行时间点不能为空")
    private String executionTime;

    @Schema(description = "Cron表达式")
    private String executionCron;

    @Schema(description = "资源类型(存储资源/宿主机资源/云主机资源/云硬盘资源)", required = true)
    @NotNull(message = "资源类型(存储资源/宿主机资源/云主机资源/云硬盘资源)不能为空")
    private String resourceType;

    @Schema(description = "巡检项(多个以逗号分隔)", required = true)
    @NotNull(message = "巡检项(多个以逗号分隔)不能为空")
    private String patrolItem;

    @Schema(description = "状态(0-未启用, 1-启用)")
    private Byte status;

    @Schema(description = "上次执行时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date lastExecutionTime;

    @Schema(description = "下次执行时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date nextExecutionTime;

    @Schema(description = "租户编号")
    private Long tenantId;

    @Schema(description = "管理员设置的租户id")
    private Long sysSettingTenant;

    @Schema(description = "平台id,多个平台ID，以逗号分隔")
    private String platformIds;

    @Schema(description = "job编号")
    private Long jobId;

}
