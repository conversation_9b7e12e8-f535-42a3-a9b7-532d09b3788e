package cn.iocoder.zj.module.om.Inspection.util;

public class InspectionUtil {
    /**
     * 转换持续时间单位为中文
     */
    public static String convertDurationUnitToChinese(String durationUnit) {
        return switch (durationUnit) {
            case "D" -> "天";
            case "h" -> "小时";
            case "m" -> "分钟";
            case "s" -> "秒";
            default -> durationUnit;
        };
    }

    /**
     * 格式化消息，替换{}占位符
     *
     * @param pattern 包含{}占位符的模式字符串
     * @param args    要替换占位符的参数
     * @return 格式化后的字符串
     */
    public static String formatMessage(String pattern, Object... args) {
        if (pattern == null) {
            return "";
        }

        StringBuilder result = new StringBuilder(pattern);
        for (Object arg : args) {
            int index = result.indexOf("{}");
            if (index >= 0) {
                result.replace(index, index + 2, arg == null ? "null" : arg.toString());
            } else {
                break;
            }
        }

        return result.toString();
    }
}
