package cn.iocoder.zj.module.om.dal.mysql.inspectionlogs;

import java.util.*;

import cn.hutool.core.date.DateUtil;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.om.dal.dataobject.inspectionlogs.InspectionLogsDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.om.controller.admin.inspectionlogs.vo.*;

/**
 * 自动巡检异常记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InspectionLogsMapper extends BaseMapperX<InspectionLogsDO> {

    default PageResult<InspectionLogsDO> selectPage(InspectionLogsPageReqVO reqVO) {
        Date[] dates = Arrays.stream(reqVO.getStartTime())
                .map(java.sql.Timestamp::valueOf)
                .toArray(Date[]::new);

        return selectPage(reqVO, new LambdaQueryWrapperX<InspectionLogsDO>()
                .eqIfPresent(InspectionLogsDO::getRecordUuid, reqVO.getRecordUuid())
                .likeIfPresent(InspectionLogsDO::getInspectionName, reqVO.getInspectionName())
                .eqIfPresent(InspectionLogsDO::getThreshold, reqVO.getThreshold())
                .likeIfPresent(InspectionLogsDO::getAssetName, reqVO.getAssetName())
                .eqIfPresent(InspectionLogsDO::getAssetUuid, reqVO.getAssetUuid())
                .eqIfPresent(InspectionLogsDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(InspectionLogsDO::getPlatformName, reqVO.getPlatformName())
                .likeIfPresent(InspectionLogsDO::getCreatorName, reqVO.getCreatorName())
                .betweenIfPresent(InspectionLogsDO::getCreateTime,dates[0],  DateUtil.offsetMinute(new Date(), 5))
                .eqIfPresent(InspectionLogsDO::getResult, reqVO.getResult())
                .orderByDesc(InspectionLogsDO::getCreateTime));
    }

    default List<InspectionLogsDO> selectList(InspectionLogsExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<InspectionLogsDO>()
                .eqIfPresent(InspectionLogsDO::getRecordUuid, reqVO.getRecordUuid())
                .likeIfPresent(InspectionLogsDO::getInspectionName, reqVO.getInspectionName())
                .eqIfPresent(InspectionLogsDO::getThreshold, reqVO.getThreshold())
                .likeIfPresent(InspectionLogsDO::getAssetName, reqVO.getAssetName())
                .eqIfPresent(InspectionLogsDO::getAssetUuid, reqVO.getAssetUuid())
                .eqIfPresent(InspectionLogsDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(InspectionLogsDO::getPlatformName, reqVO.getPlatformName())
                .likeIfPresent(InspectionLogsDO::getCreatorName, reqVO.getCreatorName())
                .betweenIfPresent(InspectionLogsDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(InspectionLogsDO::getResult, reqVO.getResult())
                .orderByDesc(InspectionLogsDO::getId));
    }

}
