package cn.iocoder.zj.module.om.service.patrolresultcategory;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.om.controller.admin.patrolresultcategory.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.patrolresultcategory.PatrolResultCategoryDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 巡检结果分类 Service 接口
 *
 * <AUTHOR>
 */
public interface PatrolResultCategoryService {

    /**
     * 创建巡检结果分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPatrolResultCategory(@Valid PatrolResultCategoryCreateReqVO createReqVO);

    /**
     * 更新巡检结果分类
     *
     * @param updateReqVO 更新信息
     */
    void updatePatrolResultCategory(@Valid PatrolResultCategoryUpdateReqVO updateReqVO);

    /**
     * 删除巡检结果分类
     *
     * @param id 编号
     */
    void deletePatrolResultCategory(Long id);

    /**
     * 获得巡检结果分类
     *
     * @param id 编号
     * @return 巡检结果分类
     */
    PatrolResultCategoryDO getPatrolResultCategory(Long id);

    /**
     * 获得巡检结果分类列表
     *
     * @param ids 编号
     * @return 巡检结果分类列表
     */
    List<PatrolResultCategoryDO> getPatrolResultCategoryList(Collection<Long> ids);

    /**
     * 获得巡检结果分类分页
     *
     * @param pageReqVO 分页查询
     * @return 巡检结果分类分页
     */
    PageResult<PatrolResultCategoryDO> getPatrolResultCategoryPage(PatrolResultCategoryPageReqVO pageReqVO);

    /**
     * 获得巡检结果分类列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 巡检结果分类列表
     */
    List<PatrolResultCategoryDO> getPatrolResultCategoryList(PatrolResultCategoryExportReqVO exportReqVO);

    /**
     * 获得巡检结果分类列表
     * @param vo 分页查询
     * @return 巡检结果分类列表
     */
    List<PatrolResultCategoryDO> listByRecordId(PatrolResultCategoryReqVO vo);

}
