package cn.iocoder.zj.module.om.controller.admin.monitorasset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * @ClassName : MonitorAssetUpdateTypeReqVO  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/7/19  13:49
 */
@Schema(description = "管理后台 - 监控资产更新授权状态 Request VO")
@Data
public class MonitorAssetUpdateTypeReqVO {


    @Schema(description = "主键", required = true)
    @NotNull(message = "主键不能为空")
    private Long id;



    @Schema(description = "主键", required = true)
    @NotNull(message = "主键不能为空")
    private String authorizationType;
}
