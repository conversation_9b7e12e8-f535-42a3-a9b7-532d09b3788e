package cn.iocoder.zj.module.om.api.dbfile;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.om.api.dbfile.dto.DbfileCreateReqDTO;
import cn.iocoder.zj.module.om.api.dbfile.dto.WorkOrderCreateReqDTO;
import cn.iocoder.zj.module.om.controller.admin.dbfile.vo.DbFileCreateReqVO;
import cn.iocoder.zj.module.om.controller.admin.workorder.vo.WorkOrderCreateReqVO;
import cn.iocoder.zj.module.om.convert.dbfile.DbFileConvert;
import cn.iocoder.zj.module.om.convert.workorder.WorkOrderConvert;
import cn.iocoder.zj.module.om.dal.dataobject.dbfile.DbFileDO;
import cn.iocoder.zj.module.om.dal.dataobject.workorder.OrderProcessDO;
import cn.iocoder.zj.module.om.dal.dataobject.workorder.WorkOrderDO;
import cn.iocoder.zj.module.om.dal.mysql.workorder.WorkOrderMapper;
import cn.iocoder.zj.module.om.service.dbfile.DbFileService;
import cn.iocoder.zj.module.om.service.schedulinginfo.SchedulingInfoService;
import cn.iocoder.zj.module.om.service.workorder.WorkOrderService;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

/**
 * @ClassName : DbFileApiImpl  //类名
 * @Description : 配置备份RPC实现  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/10/12  10:59
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class DbFileApiImpl implements DbfileApi {

    @Resource
    private DbFileService dbFileService;
    @Resource
    private SchedulingInfoService schedulingInfoService;
    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private WorkOrderMapper workOrderMapper;


    @Override
    public CommonResult<Boolean> adds(DbfileCreateReqDTO reqDTO) {
        DbFileCreateReqVO list = DbFileConvert.INSTANCE.convertReq(reqDTO);
        dbFileService.createDbFile(list);
        return CommonResult.success(true);
    }

    @Override
    public Long getSchedulCount(Long id, String name) {
        return schedulingInfoService.getSchedulCount(id,name);
    }

    @Override
    public Long getWorkOrderCount(Long id, String name) {
        return workOrderService.getWorkOrderCount(id,name);
    }

    @Override
    @TenantIgnore
    public CommonResult<Boolean> addWorkOrder(WorkOrderCreateReqDTO reqDTO) {
        WorkOrderCreateReqVO workOrderCreateReqVO = WorkOrderConvert.INSTANCE.convertReq(reqDTO);
        workOrderService.createWorkOrder(workOrderCreateReqVO);
        return CommonResult.success(true);
    }

    @Override
    @DSTransactional
    @TenantIgnore
    public CommonResult<Boolean> transferWorkOrder(String contractId, String enforcerId, String enforcerName) {
        WorkOrderDO workOrderDO=workOrderService.getWorkOrderByContractId(contractId);

        LoginUser loginUser =  SecurityFrameworkUtils.getLoginUser();
        OrderProcessDO orderProcessDO = new OrderProcessDO();
        orderProcessDO.setProcessResult("update");
        orderProcessDO.setWorkOrderId(workOrderDO.getId());
        orderProcessDO.setContext("工单执行人由"+workOrderDO.getEnforcerName()+"更改为"+enforcerName);
        orderProcessDO.setCreator(loginUser.getId().toString());
        workOrderMapper.addOrderProcessResult(orderProcessDO);

        workOrderDO.setEnforcerId(enforcerId);
        workOrderDO.setEnforcerName(enforcerName);
        workOrderMapper.updateById(workOrderDO);
        return CommonResult.success(true);
    }

    @Override
    @DSTransactional
    @TenantIgnore
    public CommonResult<Boolean> updateWorkOrderByContId(String testContractId, String contractId,String projectName) {
        WorkOrderDO workOrderDO=workOrderService.getWorkOrderByContractId(testContractId);

        LoginUser loginUser =  SecurityFrameworkUtils.getLoginUser();
        OrderProcessDO orderProcessDO = new OrderProcessDO();
        orderProcessDO.setProcessResult("update");
        orderProcessDO.setWorkOrderId(workOrderDO.getId());
        orderProcessDO.setContext("工单名称由"+workOrderDO.getName()+"更改为 合同"+projectName+"审核通过待交付");
        orderProcessDO.setCreator(loginUser.getId().toString());
        workOrderMapper.addOrderProcessResult(orderProcessDO);

        workOrderDO.setContractId(contractId);
        workOrderDO.setName("合同"+projectName+"审核通过待交付");
        workOrderMapper.updateById(workOrderDO);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> deleteWorkOrder(String contractId) {
        Map map=new HashMap();
        map.put("contract_id",contractId);
        workOrderMapper.deleteByMap(map);
        return CommonResult.success(true);
    }
}
