package cn.iocoder.zj.module.om.service.monitorauthorizationuser;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.zj.module.om.controller.admin.monitorauthorizationuser.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.monitorauthorizationuser.MonitorAuthorizationUserDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.om.convert.monitorauthorizationuser.MonitorAuthorizationUserConvert;
import cn.iocoder.zj.module.om.dal.mysql.monitorauthorizationuser.MonitorAuthorizationUserMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.om.enums.ErrorCodeConstants.*;

/**
 * 用户资产授权申请 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MonitorAuthorizationUserServiceImpl implements MonitorAuthorizationUserService {

    @Resource
    private MonitorAuthorizationUserMapper monitorAuthorizationUserMapper;

    @Override
    public Long createMonitorAuthorizationUser(MonitorAuthorizationUserCreateReqVO createReqVO) {
        // 插入
        MonitorAuthorizationUserDO monitorAuthorizationUser = MonitorAuthorizationUserConvert.INSTANCE.convert(createReqVO);
        monitorAuthorizationUserMapper.insert(monitorAuthorizationUser);
        // 返回
        return monitorAuthorizationUser.getId();
    }

    @Override
    public void updateMonitorAuthorizationUser(MonitorAuthorizationUserUpdateReqVO updateReqVO) {
        // 校验存在
        validateMonitorAuthorizationUserExists(updateReqVO.getId());
        // 更新
        MonitorAuthorizationUserDO updateObj = MonitorAuthorizationUserConvert.INSTANCE.convert(updateReqVO);
        monitorAuthorizationUserMapper.updateById(updateObj);
    }

    @Override
    public void deleteMonitorAuthorizationUser(Long id) {
        // 校验存在
        validateMonitorAuthorizationUserExists(id);
        // 删除
        monitorAuthorizationUserMapper.deleteById(id);
    }

    private void validateMonitorAuthorizationUserExists(Long id) {
        if (monitorAuthorizationUserMapper.selectById(id) == null) {
            throw exception(MONITOR_AUTHORIZATION_USER_NOT_EXISTS);
        }
    }

    @Override
    public MonitorAuthorizationUserDO getMonitorAuthorizationUser(Long id) {
        return monitorAuthorizationUserMapper.selectById(id);
    }

    @Override
    public List<MonitorAuthorizationUserDO> getMonitorAuthorizationUserList(Collection<Long> ids) {
        return monitorAuthorizationUserMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<MonitorAuthorizationUserDO> getMonitorAuthorizationUserPage(MonitorAuthorizationUserPageReqVO pageReqVO) {
        return monitorAuthorizationUserMapper.selectPage(pageReqVO);
    }

    @Override
    public List<MonitorAuthorizationUserDO> getMonitorAuthorizationUserList(MonitorAuthorizationUserExportReqVO exportReqVO) {
        return monitorAuthorizationUserMapper.selectList(exportReqVO);
    }

}
