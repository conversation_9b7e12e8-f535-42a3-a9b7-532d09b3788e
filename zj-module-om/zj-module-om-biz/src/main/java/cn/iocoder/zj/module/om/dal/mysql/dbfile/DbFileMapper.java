package cn.iocoder.zj.module.om.dal.mysql.dbfile;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.om.dal.dataobject.dbfile.DbFileDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspection.InspectionRecordDO;
import com.baomidou.dynamic.datasource.annotation.Slave;
import jodd.util.StringUtil;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.om.controller.admin.dbfile.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 配置备份 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DbFileMapper extends BaseMapperX<DbFileDO> {

    default PageResult<DbFileDO> selectPage(DbFilePageReqVO reqVO) {
        LambdaQueryWrapperX<DbFileDO> wrapperX = new LambdaQueryWrapperX<DbFileDO>()
                .likeIfPresent(DbFileDO::getName, reqVO.getName())
                .eqIfPresent(DbFileDO::getPath, reqVO.getPath())
                .eqIfPresent(DbFileDO::getUrl, reqVO.getUrl())
                .eqIfPresent(DbFileDO::getSize, reqVO.getSize())
                .betweenIfPresent(DbFileDO::getCreateTime, reqVO.getCreateTime());
        if (StringUtil.isNotEmpty(reqVO.getSortDirection()) && reqVO.getSortDirection().equals("asc")){
            if (reqVO.getSortBy().equals("createTime")){
                wrapperX.orderByAsc(DbFileDO::getCreateTime);
            }
        }else {
            wrapperX.orderByDesc(DbFileDO::getCreateTime);
        }
        return selectPage(reqVO, wrapperX);
    }

    default List<DbFileDO> selectList(DbFileExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DbFileDO>()
                .likeIfPresent(DbFileDO::getName, reqVO.getName())
                .eqIfPresent(DbFileDO::getPath, reqVO.getPath())
                .eqIfPresent(DbFileDO::getUrl, reqVO.getUrl())
                .eqIfPresent(DbFileDO::getSize, reqVO.getSize())
                .betweenIfPresent(DbFileDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(DbFileDO::getId));
    }

    void updateByName(@Param("name") String name);

    void updateState();

}
