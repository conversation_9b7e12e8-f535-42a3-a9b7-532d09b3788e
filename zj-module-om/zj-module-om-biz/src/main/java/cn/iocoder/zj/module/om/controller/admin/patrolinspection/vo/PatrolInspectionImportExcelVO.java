package cn.iocoder.zj.module.om.controller.admin.patrolinspection.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 巡检规则 Excel 导入 VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免导入有问题
public class PatrolInspectionImportExcelVO {

    @ExcelProperty("名称")
    private String name;

    @ExcelProperty("规则类型")
    private String type;

    @ExcelProperty("数据源")
    private String dataResource;

    @ExcelProperty(value = "阈值")
    private BigDecimal threshold;

    @ExcelProperty(value ="分值")
    private BigDecimal value;

    @ExcelProperty(value ="资产类型")
    private String assetType;

    @ExcelProperty(value = "扣分规则")
    private String rule;

    @ExcelProperty(value ="计算公式")
    private String formula;

    @ExcelProperty(value ="参数")
    private String param;

}
