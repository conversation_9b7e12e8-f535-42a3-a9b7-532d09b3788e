package cn.iocoder.zj.module.om.service.cloud;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo.SangForCreateVmReqVO;
import cn.iocoder.zj.module.om.controller.admin.vncinfo.vo.VncInfoVo;
import com.alibaba.fastjson.JSONArray;

import java.util.Map;

public interface ISangForCloudService {
    CommonResult<Map<String, String>> operateSangForVm(String uuid, Long platformId, String type, String action);

    CommonResult<Map<String, String>> operateSangForHardware(String uuid, Long platformId, String actions, String state, String hardwareName);

    Map<String, String> createSangForVm(SangForCreateVmReqVO reqVo);

    JSONArray getVmMirror(Long platformId, String uuid);

    VncInfoVo getVncInfo(HostInfoRespCreateReqDTO hostInfo);
}
