package cn.iocoder.zj.module.om.controller.admin.monitorauthorization.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 监控申请授权 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class MonitorAuthorizationBaseVO {

    @Schema(description = "资产id")
    private String assetId;

    @Schema(description = "资产名称")
    private String assetName;

    @Schema(description = "申请人")
    private String nickName;

    @Schema(description = "主机地址")
    private String hostName;

    @Schema(description = "授权有效时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime authorizationTime;

    @Schema(description = "申请人id")
    private Long userId;

}
