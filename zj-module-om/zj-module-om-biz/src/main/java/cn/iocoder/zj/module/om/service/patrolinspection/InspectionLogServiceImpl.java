package cn.iocoder.zj.module.om.service.patrolinspection;

import cn.iocoder.zj.framework.common.exception.ErrorCode;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import cn.iocoder.zj.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.zj.module.om.convert.patrolinspection.InspectionLogConvert;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspection.InspectionLogDO;
import cn.iocoder.zj.module.om.dal.mysql.patrolinspection.InspectionLogMapper;
import cn.iocoder.zj.module.om.util.StringUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.zj.module.om.controller.admin.inspectiondata.vo.*;
import cn.iocoder.zj.framework.common.pojo.PageResult;


import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.om.enums.ErrorCodeConstants.*;

/**
 * 巡检记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InspectionLogServiceImpl implements InspectionLogService {

    @Resource
    private InspectionLogMapper inspectionLogMapper;

    @Override
    public Long createInspectionLog(InspectionLogCreateReqVO createReqVO) {
        // 插入
        InspectionLogDO inspectionLog = InspectionLogConvert.INSTANCE.convert(createReqVO);
        inspectionLogMapper.insert(inspectionLog);
        // 返回
        return inspectionLog.getId();
    }

    @Override
    public void updateInspectionLog(InspectionLogUpdateReqVO updateReqVO) {
        // 校验存在
        validateInspectionLogExists(updateReqVO.getId());
        // 更新
        InspectionLogDO updateObj = InspectionLogConvert.INSTANCE.convert(updateReqVO);
        inspectionLogMapper.updateById(updateObj);
    }

    @Override
    public void deleteInspectionLog(Long id) {
        // 校验存在
        validateInspectionLogExists(id);
        // 删除
        inspectionLogMapper.deleteById(id);
    }

    private void validateInspectionLogExists(Long id) {
        if (inspectionLogMapper.selectById(id) == null) {
            throw exception(INSPECTION_LOG_NOT_EXISTS);
        }
    }

    @Override
    public InspectionLogDO getInspectionLog(Long id) {
        return inspectionLogMapper.selectById(id);
    }

    @Override
    public List<InspectionLogDO> getInspectionLogList(Collection<Long> ids) {
        return inspectionLogMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<InspectionLogRespVO> getInspectionLogPage(InspectionLogPageReqVO pageReqVO) {
        PageParam pageParam = new PageParam().setPageNo(pageReqVO.getPageNo()).setPageSize(pageReqVO.getPageSize());
        IPage<InspectionLogRespVO> mpPage = MyBatisUtils.buildPage(pageParam);
        return new PageResult<>(inspectionLogMapper.selectInspectionLogPage(mpPage, pageReqVO), mpPage.getTotal());
    }

    @Override
    public List<InspectionLogDO> getInspectionLogList(InspectionLogExportReqVO exportReqVO) {
        if(StringUtil.isNullOrEmpty(exportReqVO.getRecordUuid())){
            throw exception(new ErrorCode(2001000001, "暂无平台数据"));
        }
        return inspectionLogMapper.selectList(exportReqVO);
    }
    @Override
    public void createInspectionLogList( List<InspectionLogDO> inspectionLogDOList) {
        inspectionLogMapper.insertBatch(inspectionLogDOList);
    }
}
