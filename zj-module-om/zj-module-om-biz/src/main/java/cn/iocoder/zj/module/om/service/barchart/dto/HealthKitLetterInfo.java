//package cn.iocoder.zj.module.om.service.barchart.dto;
//
//import com.deepoove.poi.data.ChartMultiSeriesRenderData;
//import com.deepoove.poi.data.PictureRenderData;
//import lombok.Builder;
//import lombok.Data;
//
//import java.util.List;
//import java.util.Map;
//
///**
// * @ClassName : HealthKitLetterInfo  //类名
// * @Description : s  //描述
// * <AUTHOR> <EMAIL> //作者
// * @Date: 2023/9/6  14:57
// */
//@Data
//@Builder
//public class HealthKitLetterInfo {
//    private String hostName;
//    private String healthKitGeneratorName;
//    private String phone;
//    private String icNumber;
//    private List<Map<String, PictureRenderData>> qrCode;
//    private List<Map<String, ChartMultiSeriesRenderData>> charts;
//}