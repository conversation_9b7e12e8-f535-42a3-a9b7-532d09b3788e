package cn.iocoder.zj.module.om.controller.admin.patrolresultcategory.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 巡检结果分类 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class PatrolResultCategoryExcelVO {

    @ExcelProperty("主键ID")
    private Long id;

    @ExcelProperty("巡检记录ID")
    private Long recordId;

    @ExcelProperty("资源类型(存储资源/宿主机资源/云主机资源/云硬盘资源/等)")
    private String resourceType;

    @ExcelProperty("指标名称(CPU分配/内存分配/网卡入速度等)")
    private String metricName;

    @ExcelProperty("巡检资源数")
    private Integer resourceCount;

    @ExcelProperty("检测结果：0 正常 1 异常")
    private Integer status;

    @ExcelProperty("正常数量")
    private Integer normalCount;

    @ExcelProperty("低风险数量")
    private Integer lowRiskCount;

    @ExcelProperty("中风险数量")
    private Integer mediumRiskCount;

    @ExcelProperty("高风险数量")
    private Integer highRiskCount;

    @ExcelProperty("详情")
    private String detail;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
