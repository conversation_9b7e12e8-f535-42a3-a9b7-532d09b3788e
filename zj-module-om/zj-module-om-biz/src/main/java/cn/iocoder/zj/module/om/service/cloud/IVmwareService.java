package cn.iocoder.zj.module.om.service.cloud;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo.VmwareCreateVmReqVo;
import cn.iocoder.zj.module.om.controller.admin.vncinfo.vo.VncInfoVo;

import java.util.List;
import java.util.Map;

public interface IVmwareService {
    List<Map> getDatacenters(Long platformId);
    List<Map> getNetworks(Long platformId,String datacenter,String networkType);

    List<Map> getFoldersByDatacenter(Long platformId,String datacenter);

    List<Map> getClustersByDatacenter(Long platformId, String datacenter);

    List<Map> getDatastoresByDatacenter(Long platformId, String datacenter);

    Map<String,String> createVmwareVm(VmwareCreateVmReqVo reqVo);

    CommonResult<Map<String, String>> operateVmwareVm(String uuid, String vms, Long platformId, String type, String action);

    CommonResult<Map<String, String>> operateVmwareHost(String uuid, String vms,Long platformId, String actions, String state,String hardwareName);

    String hardwareOperateConvert(String operation, String state);

    String VmwareHostOperateConvert(String operation);

    VncInfoVo getVncInfo(HostInfoRespCreateReqDTO hostInfo);
}
