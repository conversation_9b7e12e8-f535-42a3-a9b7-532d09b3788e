package cn.iocoder.zj.module.om.service.workorder;

import cn.hutool.core.date.DateUtil;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import cn.iocoder.zj.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.bpm.api.task.BpmTaskApi;
import cn.iocoder.zj.module.customer.api.contractInfo.ContractInfoApi;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.om.dal.dataobject.workorder.OrderProcessDO;
import cn.iocoder.zj.module.om.service.cloud.IZstackCloudService;
import cn.iocoder.zj.module.om.util.StringUtil;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.SneakyThrows;
import org.apache.http.client.utils.DateUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.Max;

import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import cn.iocoder.zj.module.om.controller.admin.workorder.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.workorder.WorkOrderDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.om.convert.workorder.WorkOrderConvert;
import cn.iocoder.zj.module.om.dal.mysql.workorder.WorkOrderMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.singleton;
import static cn.iocoder.zj.module.om.enums.ErrorCodeConstants.*;

/**
 * 工单管理数据 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WorkOrderServiceImpl implements WorkOrderService {

    @Resource
    private WorkOrderMapper workOrderMapper;

    @Resource
    private PermissionApi permissionApi;
    @Resource
    private RoleApi roleApi;
    @Resource
    IZstackCloudService iZstackCloudService;
    @Resource
    AdminUserApi adminUserApi;
    @Resource
    AlarmConfigApi alarmConfigApi;
    @Resource
    ContractInfoApi contractInfoApi;

    @Resource
    BpmTaskApi bpmTaskApi;

    @Override
    public Long createWorkOrder(WorkOrderCreateReqVO createReqVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        AdminUserRespDTO user = adminUserApi.getUser(loginUser.getId()).getData();
        if(createReqVO.getEnforcerTime() == null || createReqVO.getEnforcerTime().length==0){
            createReqVO.setEnforcerEndTime(DateUtil.tomorrow());
            createReqVO.setEnforcerStartTime(new Date());
        }else {
            createReqVO.setEnforcerStartTime(createReqVO.getEnforcerTime()[0]);
            createReqVO.setEnforcerEndTime(createReqVO.getEnforcerTime()[1]);
        }
        // 插入
        WorkOrderDO workOrder = WorkOrderConvert.INSTANCE.convert(createReqVO);
        workOrder.setCreator(user.getId().toString());
        workOrder.setCreatorName(user.getNickname());
        workOrderMapper.insert(workOrder);

        if (StringUtil.isNotEmpty(workOrder.getAlarmId())){
            String alarmId=workOrder.getAlarmId();
            alarmConfigApi.alarmWorkOrder(alarmId);
        }
        if (StringUtil.isNotEmpty(workOrder.getGatherId())){
            String gatherId=workOrder.getGatherId();
            alarmConfigApi.gatherWorkOrder(gatherId);
        }

        OrderProcessDO orderProcessDO = new OrderProcessDO();
        orderProcessDO.setProcessResult("create");
        orderProcessDO.setWorkOrderId(workOrder.getId());
        orderProcessDO.setCreator(loginUser.getId().toString());
        workOrderMapper.addOrderProcessResult(orderProcessDO);

        // 返回
        return workOrder.getId();
    }

    @Override
    public void updateWorkOrder(@Valid WorkOrderUpdateMyReqVO updateReqVO) {
        if(updateReqVO.getEnforcerTime()==null||updateReqVO.getEnforcerTime().length==0){
            updateReqVO.setEnforcerEndTime(new Date());
            updateReqVO.setEnforcerStartTime(new Date());
        }else {
            updateReqVO.setEnforcerStartTime(updateReqVO.getEnforcerTime()[0]);
            updateReqVO.setEnforcerEndTime(updateReqVO.getEnforcerTime()[1]);
        }
        // 校验存在
        validateWorkOrderOp(updateReqVO.getId());
        // 更新
        WorkOrderDO updateObj = WorkOrderConvert.INSTANCE.convert(updateReqVO);
        updateObj.setRatifyStatus("wait");
        workOrderMapper.updateById(updateObj);

        LoginUser loginUser =  SecurityFrameworkUtils.getLoginUser();
        OrderProcessDO orderProcessDO = new OrderProcessDO();
        orderProcessDO.setProcessResult("update");
        orderProcessDO.setWorkOrderId(updateObj.getId());
        orderProcessDO.setCreator(loginUser.getId().toString());
        workOrderMapper.addOrderProcessResult(orderProcessDO);

    }

    @Override
    public void deleteWorkOrder(Long id) {
        validateWorkOrderOp(id);
        WorkOrderDO workOrder=workOrderMapper.selectById(id);
        //若是告警信息工单，修改告警完成状态
        if (StringUtil.isNotEmpty(workOrder.getAlarmId())){
            String alarmId=workOrder.getAlarmId();
            alarmConfigApi.cleanWorkOrder(alarmId);
        }
        if (StringUtil.isNotEmpty(workOrder.getGatherId())){
            String gatherId=workOrder.getGatherId();
            alarmConfigApi.cleanGatherWorkOrder(gatherId);
        }
        // 删除审核记录
        workOrderMapper.deleteOrderProcess(id);
        // 删除工单
        workOrderMapper.deleteById(id);
    }

    private void validateWorkOrderExists(Long id) {
        if (workOrderMapper.selectById(id) == null) {
            throw exception(WORK_ORDER_NOT_EXISTS);
        }
    }

    private void validateWorkOrderOp(Long id) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        WorkOrderDO workOrder  = getWorkOrder(id);
        //校验存在
        if (workOrder == null) {
            throw exception(WORK_ORDER_NOT_EXISTS);
        }
        Long createId = Long.parseLong(workOrder.getCreator());
        if (loginUser != null && !createId.equals(loginUser.getId())) {
            throw exception(WORK_ORDER_NOT_EXISTS);
        }

    }

    @Override
    public WorkOrderDO getWorkOrder(Long id) {
        return workOrderMapper.getById(id);
    }

    @Override
    public List<OrderProcessBaseVo> getOrderProcessList(Long workOrderId) {
        return workOrderMapper.getOrderProcessListByWorkOrderId(workOrderId);
    }

    @Override
    public List<WorkOrderDO> getWorkOrderList(Collection<Long> ids) {
        return workOrderMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<WorkOrderDO> getWorkOrderMyPage(WorkOrderPageReqVO pageReqVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        PageParam pageParam = new PageParam().setPageNo(pageReqVO.getPageNo()).setPageSize(pageReqVO.getPageSize());
        IPage<WorkOrderDO> mpPage = MyBatisUtils.buildPage(pageParam);
        List<Long> userDTOs = new ArrayList<>();
        Map<String,Object> permission = getUserOmRole(loginUser);
            //非超管、非租户管理、非运维管理角色，即普通用户，查看自身数据
        if (permission.get("role").equals("commonUser")) {
            pageReqVO.setCreator(loginUser.getId());
        }else if(permission.get("role").equals("superAdmin")){
            //超级管理员查看所有数据
            return new PageResult<>(workOrderMapper.getWorkOrderPage(mpPage,pageReqVO,userDTOs,(Boolean) permission.get("isTenantAdmin")), mpPage.getTotal());
        }else if(permission.get("role").equals("tenantOrOmManager")){
            //租户管理员和运维管理员看本租户下的数据
            userDTOs = adminUserApi.getUserIdsByTenantId(loginUser.getId()).getData();
        }
        return new PageResult<>(workOrderMapper.getWorkOrderPage(mpPage,pageReqVO,userDTOs,(Boolean) permission.get("isTenantAdmin")), mpPage.getTotal());
    }

    @SneakyThrows
    @Override
    public List<WorkOrderDO> getWorkOrderList(WorkOrderExportReqVO exportReqVO) {
        WorkOrderPageReqVO pageReqVO = new WorkOrderPageReqVO();
        pageReqVO.setDictCode(exportReqVO.getDictCode());
        pageReqVO.setName(exportReqVO.getName());
        pageReqVO.setSourceName(exportReqVO.getSourceName());
        pageReqVO.setStartTime(exportReqVO.getStartTime());
        pageReqVO.setEndTime(exportReqVO.getEndTime());
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(100000);
        return getWorkOrderMyPage(pageReqVO).getList();
    }

    @Override
    public Map<String, Object> getWorkOrderStatistics() {
        Map<String, Object> identityAndHomologousUsers = getIdentityAndHomologousUsers();
        String role = String.valueOf(identityAndHomologousUsers.get("role"));
        List<Long> homologousUsers = (List<Long>) identityAndHomologousUsers.get("homologousUsers");
        return bpmTaskApi.workOrderStatistics(homologousUsers,role).getData();
    }
    @Override
    public List<Map<String,Object>> getWorkOrderStatisticsInWeek() {
        Map<String, Object> identityAndHomologousUsers = getIdentityAndHomologousUsers();
        String role = String.valueOf(identityAndHomologousUsers.get("role"));
        List<Long> homologousUsers = (List<Long>) identityAndHomologousUsers.get("homologousUsers");
        return bpmTaskApi.getWorkOrderStatisticsInWeek(homologousUsers,role);
    }

    @Override
    public void ratifyWorkOrder(OrderRatifyReqVO reqVo) {
        LoginUser loginUser =  SecurityFrameworkUtils.getLoginUser();
        AdminUserRespDTO user = adminUserApi.getUser(loginUser.getId()).getData();
        WorkOrderDO workOrderDO = workOrderMapper.selectById(reqVo.getId());
        workOrderDO.setAuditorId(loginUser.getId());
        workOrderDO.setAuditorName(user.getNickname());
        if(StringUtil.isNotEmpty(reqVo.getEnforcerId())){
            workOrderDO.setEnforcerId(reqVo.getEnforcerId());
        }
        if(StringUtil.isNotEmpty(reqVo.getEnforcerName())){
            workOrderDO.setEnforcerName(reqVo.getEnforcerName());
        }
        OrderProcessDO orderProcessDO = new OrderProcessDO();
        orderProcessDO.setProcessResult(reqVo.getRatifyStatus());
        orderProcessDO.setWorkOrderId(workOrderDO.getId());
        orderProcessDO.setContext(reqVo.getReason());
        orderProcessDO.setCreator(loginUser.getId().toString());
        workOrderMapper.addOrderProcessResult(orderProcessDO);

        workOrderDO.setRatifyStatus(reqVo.getRatifyStatus());
        workOrderMapper.updateById(workOrderDO);
    }

    @Override
    @TenantIgnore
    public PageResult<WorkOrderRespVO> listOnDay(String day,WorkOrderPageReqVO pageVO) {
        Integer pageNo = pageVO.getPageNo();
        Integer pageSize = pageVO.getPageSize();
        Page page = new Page();
        page.setCurrent(pageNo);
        page.setSize(pageSize);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        List<WorkOrderRespVO> workOrderRespVOS = workOrderMapper.listOnDay(day, page,loginUser.getTenantId());
        PageResult<WorkOrderRespVO> result=new PageResult<>();
        result.setList(workOrderRespVOS);
        result.setTotal(Long.valueOf(workOrderRespVOS.size()));
        return result;
    }

    @Override
    public void finishWorkOrder(Long id) {

        LoginUser loginUser =  SecurityFrameworkUtils.getLoginUser();
        OrderProcessDO orderProcessDO = new OrderProcessDO();
        orderProcessDO.setProcessResult("solved");
        orderProcessDO.setWorkOrderId(id);
        orderProcessDO.setCreator(loginUser.getId().toString());
        workOrderMapper.addOrderProcessResult(orderProcessDO);

        WorkOrderDO workOrder=workOrderMapper.selectById(id);
        if (StringUtil.isNotEmpty(workOrder.getAlarmId())){
            String alarmId=workOrder.getAlarmId();
            alarmConfigApi.solvedAlarm(alarmId);
        }
        if (StringUtil.isNotEmpty(workOrder.getGatherId())){
            String gatherId=workOrder.getGatherId();
            alarmConfigApi.solvedGather(gatherId);
        }

        if (StringUtil.isNotEmpty(workOrder.getContractId())){
            String contractId=workOrder.getContractId();
            contractInfoApi.updateStatus(contractId);
        }

        WorkOrderDO workOrderDO = new WorkOrderDO();
        workOrderDO.setId(id);
        workOrderDO.setRatifyStatus("solved");
        workOrderMapper.updateById(workOrderDO);
    }

    @Override
    public Integer getWorkerOrderCountBySourceUuid(String uuid,String type,String actions) {
        return workOrderMapper.getWorkerOrderCountBySourceUuid(uuid,type,actions);
    }

    @Override
    public List<Map<String, Object>> getRanking() {
        Map<String, Object> identityAndHomologousUsers = getIdentityAndHomologousUsers();
        String role = String.valueOf(identityAndHomologousUsers.get("role"));
        List<Long> homologousUsers = (List<Long>) identityAndHomologousUsers.get("homologousUsers");
        return bpmTaskApi.getRanking(homologousUsers,role);
    }

    @Override
    public Map<String, String> getPersonWorkOrderInfo(Long userId) {
        Map<String, Object> identityAndHomologousUsers = getIdentityAndHomologousUsers();
        String role = String.valueOf(identityAndHomologousUsers.get("role"));
        List<Long> homologousUsers = (List<Long>) identityAndHomologousUsers.get("homologousUsers");
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(userId, singleton(CommonStatusEnum.ENABLE.getStatus()));
        Set<Long> managerIds = roleApi.getRoleIdByCode("om_manager").getData();
        Set<Long> managerMixed = new HashSet<Long>(roleIds);
        managerMixed.retainAll(managerIds);
        if(role.equals("superAdmin")||role.equals("tenantAdmin")){
            return bpmTaskApi.getPersonWorkOrderInfo(homologousUsers,userId,role).getData();
        }else {
            if(managerMixed.size()>0){
                return bpmTaskApi.getPersonWorkOrderInfo(homologousUsers,userId,"omManager").getData();
            }else {
                return bpmTaskApi.getPersonWorkOrderInfo(homologousUsers,userId,"personal").getData();
            }
        }
    }

    @Override
    public List<Map<String, String>> getWorkOrderSourceType() {
        Map<String, Object> identityAndHomologousUsers = getIdentityAndHomologousUsers();
        String role = String.valueOf(identityAndHomologousUsers.get("role"));
        List<Long> homologousUsers = (List<Long>) identityAndHomologousUsers.get("homologousUsers");
        return bpmTaskApi.getWorkOrderSourceType(homologousUsers,role).getData();
    }

    @Override
    public List<Map<String, String>> getCreateInWeek() {
        Map<String, Object> identityAndHomologousUsers = getIdentityAndHomologousUsers();
        String role = String.valueOf(identityAndHomologousUsers.get("role"));
        List<Long> homologousUsers = (List<Long>) identityAndHomologousUsers.get("homologousUsers");
        return bpmTaskApi.getCreateInWeek(homologousUsers,role).getData();
    }

    @Override
    public String getLatestRejectReason(Long id) {
        return workOrderMapper.getLatestRejectReason(id);
    }

    @Override
    public OrderProcessBaseVo getOrderProcessRatify(Long workOrderId) {
        return workOrderMapper.getOrderProcessRatifyByWorkOrderId(workOrderId);
    }

    @Override
    public OrderProcessBaseVo getOrderProcessSolved(Long workOrderId) {
        return workOrderMapper.getOrderProcessSolvedByWorkOrderId(workOrderId);
    }

    @Override
    public Long getWorkOrderCount(Long id, String name) {
        return workOrderMapper.getWorkOrderCount(id,name);
    }

    @Override
    public WorkOrderDO getWorkOrderByContractId(String contractId) {
        return workOrderMapper.getWorkOrderByContractId(contractId);
    }

    @Override
    public List<WorkOrderRespVO> getUnsolvedWorkOrder() {
        LoginUser user = SecurityFrameworkUtils.getLoginUser();
        return workOrderMapper.getUnsolvedWorkOrder(user);
    }

    @Override
    public void updateIsRead(Collection<Long> ids) {
        workOrderMapper.updateIsRead(ids);
    }

    /**
     * 获取同单位下某个角色的所有用户ID
     */
    public List<Long> getHomologousUsers(Long userId,String roleCode) {
        return workOrderMapper.getHomologousUsers(userId,roleCode);
    }

    private Map<String,Object> getUserOmRole(LoginUser loginUser){
        Map<String,Object> permission = new HashMap<>();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        Boolean hasPermission = iZstackCloudService.getPermission(loginUser);
        Set<Long> loginUserRoleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        Set<Long> mixed = new HashSet<Long>(loginUserRoleIds);
        Set<Long> targetIds = roleApi.getRoleIdByCode("tenant_admin").getData();
        mixed.retainAll(targetIds);
        boolean isTenantAdmin = mixed.size()>0;
        String role = "";
        if (!hasPermission && !roleApi.hasAnySuperAdmin(roleIds) && !isTenantAdmin) {
            //普通用户
            role = "commonUser";
        }else if(roleApi.hasAnySuperAdmin(roleIds)){
            //超级管理员查看所有数据
            role = "superAdmin";
        }else {
            //租户管理员和运维管理员看本租户下的数据
            role = "tenantOrOmManager";
        }
        permission.put("role",role);
        permission.put("isTenantAdmin",isTenantAdmin);
        return permission;
    }
    private Map<String, Object> getIdentityAndHomologousUsers(){
        Map<String, Object> resultMap = new HashMap<>();
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        List<Long> homologousUsers =new ArrayList<>();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        Boolean hasAnySuperAdmin = roleApi.hasAnySuperAdmin(roleIds);
        Boolean hasAnyTenantAdmin = roleApi.hasAnyTenantAdmin(roleIds);
        String role = "personal";
        if(hasAnyTenantAdmin){
            role = "tenantAdmin";
        }
        if(hasAnySuperAdmin){
            role="superAdmin";
        }
        homologousUsers = getHomologousUsers(loginUser.getId(),"");
        resultMap.put("role",role);
        resultMap.put("homologousUsers",homologousUsers);
        return resultMap;
    }
}
