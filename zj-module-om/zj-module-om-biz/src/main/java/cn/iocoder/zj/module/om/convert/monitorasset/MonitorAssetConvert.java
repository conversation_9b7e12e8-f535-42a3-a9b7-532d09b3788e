package cn.iocoder.zj.module.om.convert.monitorasset;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.om.controller.admin.monitorasset.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.monitorasset.MonitorAssetDO;

/**
 * 监控资产 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface MonitorAssetConvert {

    MonitorAssetConvert INSTANCE = Mappers.getMapper(MonitorAssetConvert.class);

    MonitorAssetDO convert(MonitorAssetCreateReqVO bean);

    MonitorAssetDO convert(MonitorAssetUpdateReqVO bean);

    MonitorAssetRespVO convert(MonitorAssetDO bean);

    List<MonitorAssetRespVO> convertList(List<MonitorAssetDO> list);

    PageResult<MonitorAssetRespVO> convertPage(PageResult<MonitorAssetDO> page);

    List<MonitorAssetExcelVO> convertList02(List<MonitorAssetDO> list);

}
