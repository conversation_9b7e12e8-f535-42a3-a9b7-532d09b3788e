package cn.iocoder.zj.module.om.controller.admin.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class OrderRatifyReqVO {
    @Schema(description = "工单ID",required = true)
    private Long id;
    @Schema(description = "退回原因")
    private String reason;
    @Schema(description = "执行人ID，多个逗号拼接",required = true)
    private String enforcerId;
    @Schema(description = "执行人，多个逗号拼接",required = true)
    private String enforcerName;
    @Schema(description = "审核状态，solved已处理,consent-unresolved审核通过未处理，reject驳回，wait待审核")
    private String ratifyStatus;
}
