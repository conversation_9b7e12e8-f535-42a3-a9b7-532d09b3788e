package cn.iocoder.zj.module.om.convert.patrolplan;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.om.controller.admin.patrolplan.vo.PatrolPlanCreateReqVO;
import cn.iocoder.zj.module.om.controller.admin.patrolplan.vo.PatrolPlanExcelVO;
import cn.iocoder.zj.module.om.controller.admin.patrolplan.vo.PatrolPlanRespVO;
import cn.iocoder.zj.module.om.controller.admin.patrolplan.vo.PatrolPlanUpdateReqVO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolplan.PatrolPlanDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 巡检计划 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface PatrolPlanConvert {

    PatrolPlanConvert INSTANCE = Mappers.getMapper(PatrolPlanConvert.class);

    PatrolPlanDO convert(PatrolPlanCreateReqVO bean);

    PatrolPlanDO convert(PatrolPlanUpdateReqVO bean);

    PatrolPlanRespVO convert(PatrolPlanDO bean);

    List<PatrolPlanRespVO> convertList(List<PatrolPlanDO> list);

    PageResult<PatrolPlanRespVO> convertPage(PageResult<PatrolPlanDO> page);

    List<PatrolPlanExcelVO> convertList02(List<PatrolPlanDO> list);

}
