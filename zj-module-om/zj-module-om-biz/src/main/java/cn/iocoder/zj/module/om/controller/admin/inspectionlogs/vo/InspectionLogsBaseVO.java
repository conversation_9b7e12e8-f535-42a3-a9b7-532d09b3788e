package cn.iocoder.zj.module.om.controller.admin.inspectionlogs.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 自动巡检异常记录 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class InspectionLogsBaseVO {

    @Schema(description = "巡检记录id")
    private String recordUuid;

    @Schema(description = "巡检名称")
    private String inspectionName;

    @Schema(description = "阈值")
    private BigDecimal threshold;

    @Schema(description = "资产名称")
    private String assetName;

    @Schema(description = "资产id")
    private String assetUuid;

    @Schema(description = "平台id")
    private String platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "资产的值")
    private String value;

    @Schema(description = "创建人名称")
    private String creatorName;

    @Schema(description = "检测结果")
    private String result;

}
