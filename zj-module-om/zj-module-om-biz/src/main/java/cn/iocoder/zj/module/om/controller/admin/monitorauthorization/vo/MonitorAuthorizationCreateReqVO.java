package cn.iocoder.zj.module.om.controller.admin.monitorauthorization.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 监控申请授权创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MonitorAuthorizationCreateReqVO extends MonitorAuthorizationBaseVO {

}
