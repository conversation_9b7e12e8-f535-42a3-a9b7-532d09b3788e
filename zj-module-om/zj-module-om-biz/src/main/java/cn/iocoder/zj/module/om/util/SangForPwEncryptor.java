package cn.iocoder.zj.module.om.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.iocoder.zj.module.om.service.zstack.core.SangForApiConstant;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONObject;

import javax.crypto.Cipher;
import java.math.BigInteger;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.RSAPublicKeySpec;

public class SangForPwEncryptor {
    private static String encryptor(String publicKey,String psw){
        try {
            //对密码加密
            byte[] pass = encrypt(psw.getBytes(),publicKey);
            //转换为16进制字符后返回
            return bytesToHex(pass);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    private static RSAPublicKey getPublicKey(String modulus, String exponent) {
        try {
            BigInteger b1 = new BigInteger(modulus,16);
            //此处为进制数
            BigInteger b2 = new BigInteger(exponent,16);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            RSAPublicKeySpec keySpec = new RSAPublicKeySpec(b1, b2);
            return (RSAPublicKey) keyFactory.generatePublic(keySpec);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    //对密码进行加密
    private static byte[] encrypt(byte[] bt_plaintext, String key) throws Exception {
        PublicKey publicKey = getPublicKey(key, "10001");
        Cipher cipher = Cipher.getInstance("RSA"); cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        return cipher.doFinal(bt_plaintext);
    }
    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexBuilder = new StringBuilder();
        for (byte b : bytes) {
            String hex = String.format("%02X", b);
            hexBuilder.append(hex);
        }
        return hexBuilder.toString();
    }

    public static JSONObject sangForLogin(PlatformconfigDTO platform){
        String publicKey = getPublicKey(platform);
        String password = SangForPwEncryptor.encryptor(publicKey,platform.getPassword());
        HttpResponse res = HttpRequest.post(platform.getUrl()+ SangForApiConstant.LOGIN)
                .form("username",platform.getUsername())
                .form("password",password)
                .execute();
        if (200 != res.getStatus()) {
            throw new RuntimeException("深信服平台登录失败！");
        }
        return JSONObject.parseObject(res.body()).getJSONObject("data");
    }
    private static String getPublicKey(PlatformconfigDTO platform) {
        HttpResponse res = HttpRequest.get(platform.getUrl() + SangForApiConstant.GET_PUBLIC_KEY).execute();
        if (200 != res.getStatus()) {
            throw new RuntimeException("公钥获取失败！");
        }
        JSONObject cpu = JSONObject.parseObject(res.body());
        return cpu.getString("data");
    }
}
