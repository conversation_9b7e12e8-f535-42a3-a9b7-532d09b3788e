package cn.iocoder.zj.module.om.framework.minio.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FastByteArrayOutputStream;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.module.om.framework.minio.config.MinioConfig;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.alibaba.nacos.shaded.com.google.common.io.ByteStreams;
import io.minio.*;
import io.minio.errors.MinioException;
import io.minio.http.Method;
import io.minio.messages.Bucket;
import io.minio.messages.Item;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : MinioUtil  //类名
 * @Description : minio 工具类  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/10/11  10:31
 */

@Slf4j
@Component
public class MinioUtil {
    @Autowired
    private MinioConfig prop;

    @Resource
    private MinioClient minioClient;


    /**
     * 上传对象到minio，目标位置和存储名称存在重复时会覆盖
     *
     * @param bucketName 目标桶
     * @param filePath   目标位置和存储名称，如 tempdir/123.txt
     * @param file
     */
    public  void putObject(String bucketName, String filePath, File file) throws Exception {

        PutObjectArgs.builder()
                .bucket(bucketName)
                .object(removeSlash(filePath))
                .stream(new FileInputStream(file), file.length(), -1)
                .build();

    }

    /**
     * 上传对象到minio，目标位置和存储名称存在重复时会覆盖
     *
     * @param bucketName 目标桶
     * @param filePath   目标位置和存储名称，如 tempdir/123.txt
     * @param file
     */
    public void putObject(String bucketName, String filePath, InputStream file) throws Exception {
        PutObjectArgs objectArgs =  PutObjectArgs.builder()
                .bucket(bucketName)
                .object(removeSlash(filePath))
                .stream(file, file.available(), -1)
                .build();
        //文件名称相同会覆盖
        minioClient.putObject(objectArgs);
    }


    /**
     * 删除削减
     *
     * @param str 入参
     * @return * @return java.lang.String
     * <AUTHOR>
     * @date 9:33 2021/9/26
     */
    private  String removeSlash(String str) {
        if (str.substring(0, 1).equals("/")) {
            return str.substring(1);
        }
        return str;
    }


    /**
     * 创建存储bucket
     *
     * @return Boolean
     */
    public Boolean makeBucket(String bucketName) {
        try {
            minioClient.makeBucket(MakeBucketArgs.builder()
                    .bucket(bucketName)
                    .build());
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * 删除存储bucket
     *
     * @return Boolean
     */
    public Boolean removeBucket(String bucketName) {
        try {
            minioClient.removeBucket(RemoveBucketArgs.builder()
                    .bucket(bucketName)
                    .build());
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * 获取全部bucket
     */
    public List<Bucket> getAllBuckets() {
        try {
            List<Bucket> buckets = minioClient.listBuckets();
            return buckets;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 文件上传
     *
     * @param file 文件
     * @return Boolean
     */
    public String upload(MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isBlank(originalFilename)) {
            throw new RuntimeException();
        }
        String fileName = UuidUtils.generateUuid() + originalFilename.substring(originalFilename.lastIndexOf("."));
        String objectName = DateUtil.format(new Date(), "yyyy-MM/dd") + "/" + fileName;
        try {
            PutObjectArgs objectArgs = PutObjectArgs.builder().bucket(prop.getBucketName()).object(objectName)
                    .stream(file.getInputStream(), file.getSize(), -1).contentType(file.getContentType()).build();
            //文件名称相同会覆盖
            minioClient.putObject(objectArgs);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return objectName;
    }

    /**
     * 预览图片
     *
     * @param fileName
     * @return
     */
    public String preview(String fileName) {
        // 查看文件地址
        GetPresignedObjectUrlArgs build = new GetPresignedObjectUrlArgs().builder().bucket(prop.getBucketName()).object(fileName).method(Method.GET).build();
        try {
            String url = minioClient.getPresignedObjectUrl(build);
            return url;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @SneakyThrows
    public InputStream getObject(String bucketName, String objectName) {
        GetObjectArgs getObjectArgs = GetObjectArgs.builder().bucket(bucketName).object(objectName).build();
        return minioClient.getObject(getObjectArgs);
    }


    /**
     * 文件下载
     *
     * @param fileName 文件名称
     * @param res      response
     * @return Boolean
     */
    public void download(String fileName, HttpServletResponse res) {
        GetObjectArgs objectArgs = GetObjectArgs.builder().bucket(prop.getBucketName())
                .object(fileName).build();
        try (GetObjectResponse response = minioClient.getObject(objectArgs)) {
            byte[] buf = new byte[1024];
            int len;
            try (FastByteArrayOutputStream os = new FastByteArrayOutputStream()) {
                while ((len = response.read(buf)) != -1) {
                    os.write(buf, 0, len);
                }
                os.flush();
                byte[] bytes = os.toByteArray();
                res.setCharacterEncoding("utf-8");
                // 设置强制下载不打开
                // res.setContentType("application/force-download");
                res.addHeader("Content-Disposition", "attachment;fileName=" + fileName);
                try (ServletOutputStream stream = res.getOutputStream()) {
                    stream.write(bytes);
                    stream.flush();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 查看文件对象
     *
     * @return 存储bucket内文件对象信息
     */
    public List<Item> listObjects() {
        Iterable<Result<Item>> results = minioClient.listObjects(
                ListObjectsArgs.builder().bucket(prop.getBucketName()).build());
        List<Item> items = new ArrayList<>();
        try {
            for (Result<Item> result : results) {
                items.add(result.get());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return items;
    }

    /**
     * 删除
     *
     * @param fileName
     * @return
     * @throws Exception
     */
    public boolean remove(String fileName) {
        try {
            minioClient.removeObject(RemoveObjectArgs.builder().bucket(prop.getBucketName()).object(fileName).build());
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    /**
     * 直接上传
     *
     * @param fileName    文件名
     * @param inputStream 文件流
     * @return
     * @throws Exception
     */
    public  String upload(String fileName, InputStream inputStream, String fileBucketName) throws Exception {
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        return upload(inputStream, fileName, fileBucketName);
    }

    /**
     * 直接上传
     *
     * @param inputStream 文件流
     * @param suffix      文件后缀
     * @return
     * @throws Exception
     */
    public  String upload(InputStream inputStream, String suffix, String fileBucketName) throws Exception {
        String uuid = UUID.randomUUID().toString();
        String savePath = getSavePath( suffix);
        putObject(fileBucketName, savePath, inputStream);
        return savePath;
    }

    private  String getSavePath(String fileName) {

        String dayStr = DateUtil.now();
        String days = dayStr.substring(0, dayStr.lastIndexOf(" "));
        String[] dayArr = days.split("-");

        String path = dayArr[0] + "/" + dayArr[1] + "/" + dayArr[2] + "/" + fileName;

        return path;
    }



    /**
     * 将分钟数转换为秒数
     *
     * @param expiry 过期时间(分钟数)
     * @return expiry
     */
    private  int expiryHandle(Integer expiry) {
        expiry = expiry * 60;
        if (expiry > 604800) {
            return 604800;
        }
        return expiry;
    }

}
