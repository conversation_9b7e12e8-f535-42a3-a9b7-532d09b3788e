package cn.iocoder.zj.module.om.controller.admin.monitorauthorizationuser.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 用户资产授权申请 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MonitorAuthorizationUserRespVO extends MonitorAuthorizationUserBaseVO {

    @Schema(description = "主键", required = true)
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
