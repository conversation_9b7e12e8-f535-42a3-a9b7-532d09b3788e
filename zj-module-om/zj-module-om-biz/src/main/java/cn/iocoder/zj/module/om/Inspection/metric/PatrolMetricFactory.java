
package cn.iocoder.zj.module.om.Inspection.metric;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.zj.framework.common.dal.manager.SecGroupRuleData;
import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.secgroup.dto.SecgroupRespDto;
import cn.iocoder.zj.module.monitor.api.storage.dto.StorageRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeDTO;
import cn.iocoder.zj.module.om.Inspection.metric.handler.*;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspectionconfig.PatrolInspectionConfigDO;
import cn.iocoder.zj.module.om.enums.PatrolMetricEnum;
import cn.iocoder.zj.module.om.enums.PatrolResourceTypeEnum;
import cn.iocoder.zj.module.om.enums.PatrolRiskEnum;
import org.apache.hertzbeat.common.entity.manager.Monitor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * 巡检指标工厂类
 * 负责创建不同类型的巡检指标实例
 *
 * <AUTHOR>
 */
public class PatrolMetricFactory {

    /**
     * 指标创建策略映射
     * 键为指标枚举，值为创建该指标的函数
     */
    private static final Map<PatrolMetricEnum, Function<PatrolMetricEnum, ? extends MetricHandler<?>>> METRIC_CREATORS = new HashMap<>();

    // 静态初始化块，注册所有指标的创建策略
    static {
        // 注册状态类指标
        registerStateMetrics();

        // 注册阈值类指标
        registerThresholdMetrics();

        // 注册 hertzVm 阈值类指标
        registerHzVmThresholdMetrics();

        // 注册当前类指标
        registerCurrentMetrics();
    }

    /**
     * 注册状态类指标
     */
    private static void registerStateMetrics() {
        // 云主机状态指标
        registerStrategy(PatrolMetricEnum.CLOUD_STATE, metricType ->
                new StateMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.CLOUD,
                        HostInfoRespCreateReqDTO.class,
                        null,
                        cloud -> !"Running".equals(cloud.getState()),
                        HostInfoRespCreateReqDTO::getUuid,
                        HostInfoRespCreateReqDTO::getName,
                        cloud -> cloud.getPlatformId().toString(),
                        HostInfoRespCreateReqDTO::getPlatformName,
                        cloud -> {
                            String cloudState = cloud.getState();
                            if (cloudState == null) {
                                return "状态未知";
                            }
                            return switch (cloudState) {
                                case "Stopped" -> "已停止";
                                case "Stopping" -> "停止中";
                                case "Rebooting" -> "重启中";
                                case "Destroying" -> "回收中";
                                case "Migrating" -> "迁移中";
                                case "Expunging" -> "销毁中";
                                case "Paused" -> "已暂停";
                                case "Resuming" -> "恢复中";
                                default -> "未知状态";
                            };
                        },
                        cloud -> PatrolRiskEnum.LOW.getCode()
                )
        );

        // 宿主机状态指标
        registerStrategy(PatrolMetricEnum.HOST_STATE, metricType ->
                new StateMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.HOST,
                        HardWareRespCreateReqDTO.class,
                        null,
                        host -> !("Enabled".equals(host.getState()) && "Connected".equals(host.getStatus())),
                        HardWareRespCreateReqDTO::getUuid,
                        HardWareRespCreateReqDTO::getName,
                        host -> host.getPlatformId().toString(),
                        HardWareRespCreateReqDTO::getPlatformName,
                        host -> "未连接",
                        host -> PatrolRiskEnum.HIGH.getCode()
                )
        );

        // 存储状态指标
        registerStrategy(PatrolMetricEnum.STORAGE_STATE, metricType ->
                new StateMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.STORAGE,
                        StorageRespCreateReqDTO.class,
                        null,
                        storage -> !("Enabled".equals(storage.getState()) && "Connected".equals(storage.getStatus())),
                        StorageRespCreateReqDTO::getUuid,
                        StorageRespCreateReqDTO::getName,
                        storage -> storage.getPlatformId().toString(),
                        StorageRespCreateReqDTO::getPlatformName,
                        storage -> "未连接",
                        storage -> PatrolRiskEnum.LOW.getCode()
                )
        );

        // 云硬盘挂载状态指标
        registerStrategy(PatrolMetricEnum.MOUNT_STATE, metricType ->
                new StateMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.DISK,
                        VolumeDTO.class,
                        null,
                        volume -> volume.getIsMount() == null || !volume.getIsMount(),
                        VolumeDTO::getUuid,
                        VolumeDTO::getName,
                        volume -> volume.getPlatformId().toString(),
                        VolumeDTO::getPlatformName,
                        volume -> "未挂载",
                        volume -> PatrolRiskEnum.LOW.getCode()
                )
        );

        //监控资产状态
        registerStrategy(PatrolMetricEnum.MONITOR_STATE, metricType ->
                new StateMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.MONITOR,
                        Monitor.class,
                        null,
                        monitor -> monitor.getStatus() != 1,
                        monitor -> String.valueOf(monitor.getId()),
                        Monitor::getName,
                        monitor -> monitor.getPlatformId().toString(),
                        Monitor::getPlatformName,
                        monitor -> switch (monitor.getStatus()) {
                            case 0 -> "未管理";
                            case 2 -> "不可用";
                            default -> "未知状态";
                        },
                        monitor -> switch (monitor.getStatus()) {
                            case 0 -> PatrolRiskEnum.LOW.getCode();
                            case 2 -> PatrolRiskEnum.HIGH.getCode();
                            default -> PatrolRiskEnum.NORMAL.getCode();
                        }
                )
        );

        //互联网暴露面指标
        registerStrategy(PatrolMetricEnum.INTERNET_EXPOSE_FACE, metricType ->
                new StateMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.CLOUD,
                        HostInfoRespCreateReqDTO.class,
                        cloud -> cloud.getVipIp() != null
                                && !cloud.getVipIp().isEmpty(),
                        cloud -> {
                            String ip = cloud.getVipIp();
                            String[] parts = ip.split("\\.");
                            if (parts.length != 4) {
                                return false;
                            }
                            try {
                                int first = Integer.parseInt(parts[0]);
                                int second = Integer.parseInt(parts[1]);
                                // A类私有：10.0.0.0～**************
                                if (first == 10) {
                                    return false;
                                }
                                // B类私有：**********～**************
                                if (first == 172 && second >= 16 && second <= 31) {
                                    return false;
                                }
                                // C类私有：***********～***************
                                if (first == 192 && second == 168) {
                                    return false;
                                }
                                // 只要不是私有IP就认为是互联网暴露
                                // 如果安全组有22、3389端口，未限制源地址访问
                                // 这里假设cloud对象有getSecurityGroupRules()方法，返回安全组规则列表
                                // 每个规则有getPorts()和getCidr()方法
                                if (CollectionUtil.isNotEmpty(cloud.getSecgroupDataList())) {
                                    for (SecgroupRespDto secGroup : cloud.getSecgroupDataList()) {
                                        List<SecGroupRuleData> inRules = secGroup.getInRules();
                                        if (CollectionUtil.isEmpty(inRules)) {
                                            continue;
                                        }
                                        for (SecGroupRuleData rule : inRules) {
                                            String port = rule.getPorts();
                                            String cidr = rule.getCidr();
                                            // 检查端口是否为22或3389，且源地址为0.0.0.0/0
                                            if ((port.contains("22") || port.contains("3389")) &&
                                                    ("0.0.0.0/0".equals(cidr.trim()))) {
                                                return true;
                                            }
                                        }
                                    }
                                    return false;
                                } else {
                                    return false;
                                }
                            } catch (NumberFormatException e) {
                                return false;
                            }
                        },
                        HostInfoRespCreateReqDTO::getUuid,
                        HostInfoRespCreateReqDTO::getName,
                        cloud -> cloud.getPlatformId().toString(),
                        HostInfoRespCreateReqDTO::getPlatformName,
                        cloud -> "未暴露",
                        cloud -> PatrolRiskEnum.HIGH.getCode()
                )
        );

    }

    private static void registerCurrentMetrics() {
        //网络运行时长指标
        registerStrategy(PatrolMetricEnum.NETWORK_RUNNING_TIME, metricType ->
                new CurrentMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.NETWORK,
                        Monitor.class,
                        monitor -> monitor.getStatus() == 1,
                        Monitor::getId,
                        Monitor::getName,
                        Monitor::getPlatformId,
                        Monitor::getPlatformName,
                        Monitor::getApp,
                        "uptime"
                )
        );

        //安全运行时长指标
        registerStrategy(PatrolMetricEnum.FIREWALL_RUNNING_TIME, metricType ->
                new CurrentMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.FIREWALL,
                        Monitor.class,
                        monitor -> monitor.getStatus() == 1,
                        Monitor::getId,
                        Monitor::getName,
                        Monitor::getPlatformId,
                        Monitor::getPlatformName,
                        Monitor::getApp,
                        "uptime"
                )
        );
        //操作系统运行时长指标
        registerStrategy(PatrolMetricEnum.OS_RUNNING_TIME, metricType ->
                new CurrentMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.OS,
                        Monitor.class,
                        monitor -> monitor.getStatus() == 1,
                        Monitor::getId,
                        Monitor::getName,
                        Monitor::getPlatformId,
                        Monitor::getPlatformName,
                        Monitor::getApp,
                        "uptime"
                )
        );
    }

    /**
     * 注册阈值类指标
     */
    private static void registerThresholdMetrics() {
        // 宿主机CPU分配指标
        registerStrategy(PatrolMetricEnum.CPU_ALLOCATE, metricType ->
                new ThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.HOST,
                        HardWareRespCreateReqDTO.class,
                        host -> "Enabled".equals(host.getState()) && "Connected".equals(host.getStatus()),
                        host -> host.getCpuOverPercent() != null ? host.getCpuOverPercent() : BigDecimal.ZERO,
                        HardWareRespCreateReqDTO::getUuid,
                        HardWareRespCreateReqDTO::getName,
                        host -> host.getPlatformId().toString(),
                        HardWareRespCreateReqDTO::getPlatformName,
                        PatrolInspectionConfigDO::getThresholdType
                )
        );

        // 宿主机内存分配指标
        registerStrategy(PatrolMetricEnum.MEMORY_ALLOCATE, metricType ->
                new ThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.HOST,
                        HardWareRespCreateReqDTO.class,
                        host -> "Enabled".equals(host.getState()) && "Connected".equals(host.getStatus()),
                        host -> host.getMemoryOverPercent() != null ? host.getMemoryOverPercent() : BigDecimal.ZERO,
                        HardWareRespCreateReqDTO::getUuid,
                        HardWareRespCreateReqDTO::getName,
                        host -> host.getPlatformId().toString(),
                        HardWareRespCreateReqDTO::getPlatformName,
                        PatrolInspectionConfigDO::getThresholdType
                )
        );

        //宿主机CPU使用率峰值指标
        registerStrategy(PatrolMetricEnum.CPU_MAX_LOAD, metricType ->
                new ZjVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.HOST,
                        HardWareRespCreateReqDTO.class,
                        host -> "Enabled".equals(host.getState()) && "Connected".equals(host.getStatus()),
                        HardWareRespCreateReqDTO::getUuid,
                        HardWareRespCreateReqDTO::getName,
                        host -> host.getPlatformId().toString(),
                        HardWareRespCreateReqDTO::getPlatformName,
                        "cpu_task",
                        "CPUUsedUtilization",
                        "1m",
                        "max",
                        1
                )
        );

        //宿主机持续性CPU饱和指标
        registerStrategy(PatrolMetricEnum.HOST_CPU_SATURATION, metricType ->
                new ZjVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.HOST,
                        HardWareRespCreateReqDTO.class,
                        host -> "Enabled".equals(host.getState()) && "Connected".equals(host.getStatus()),
                        HardWareRespCreateReqDTO::getUuid,
                        HardWareRespCreateReqDTO::getName,
                        host -> host.getPlatformId().toString(),
                        HardWareRespCreateReqDTO::getPlatformName,
                        "cpu_task",
                        "CPUUsedUtilization",
                        null,
                        null,
                        3
                )
        );

        //宿主机CPU波动指标
        registerStrategy(PatrolMetricEnum.HOST_CPU_WAVE, metricType ->
                new ZjVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.HOST,
                        HardWareRespCreateReqDTO.class,
                        host -> "Enabled".equals(host.getState()) && "Connected".equals(host.getStatus()),
                        HardWareRespCreateReqDTO::getUuid,
                        HardWareRespCreateReqDTO::getName,
                        host -> host.getPlatformId().toString(),
                        HardWareRespCreateReqDTO::getPlatformName,
                        "cpu_task",
                        "CPUUsedUtilization",
                        null,
                        null,
                        2
                )
        );

        //宿主机内存波动指标
        registerStrategy(PatrolMetricEnum.HOST_MEMORY_WAVE, metricType ->
                new ZjVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.HOST,
                        HardWareRespCreateReqDTO.class,
                        host -> "Enabled".equals(host.getState()) && "Connected".equals(host.getStatus()),
                        HardWareRespCreateReqDTO::getUuid,
                        HardWareRespCreateReqDTO::getName,
                        host -> host.getPlatformId().toString(),
                        HardWareRespCreateReqDTO::getPlatformName,
                        "mem_task",
                        "MemoryUsage",
                        null,
                        null,
                        2
                )
        );

        //宿主机CPU平均负载指标
        registerStrategy(PatrolMetricEnum.CPU_AVERAGE_LOAD, metricType ->
                new ZjVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.HOST,
                        HardWareRespCreateReqDTO.class,
                        host -> "Enabled".equals(host.getState()) && "Connected".equals(host.getStatus()),
                        HardWareRespCreateReqDTO::getUuid,
                        HardWareRespCreateReqDTO::getName,
                        host -> host.getPlatformId().toString(),
                        HardWareRespCreateReqDTO::getPlatformName,
                        "cpu_task",
                        "CPUUsedUtilization",
                        null,
                        "avg",
                        0
                )
        );

        //宿主机网卡入速度指标
        registerStrategy(PatrolMetricEnum.NETWORK_IN_BYTES, metricType ->
                new ZjVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.HOST,
                        HardWareRespCreateReqDTO.class,
                        host -> "Enabled".equals(host.getState()) && "Connected".equals(host.getStatus()),
                        HardWareRespCreateReqDTO::getUuid,
                        HardWareRespCreateReqDTO::getName,
                        host -> host.getPlatformId().toString(),
                        HardWareRespCreateReqDTO::getPlatformName,
                        "net_task",
                        "NetworkInBytes",
                        "1m",
                        "avg",
                        1
                )
        );

        //宿主机网卡出速度指标
        registerStrategy(PatrolMetricEnum.NETWORK_OUT_BYTES, metricType ->
                new ZjVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.HOST,
                        HardWareRespCreateReqDTO.class,
                        host -> "Enabled".equals(host.getState()) && "Connected".equals(host.getStatus()),
                        HardWareRespCreateReqDTO::getUuid,
                        HardWareRespCreateReqDTO::getName,
                        host -> host.getPlatformId().toString(),
                        HardWareRespCreateReqDTO::getPlatformName,
                        "net_task",
                        "NetworkOutBytes",
                        "1m",
                        "avg",
                        1
                )
        );


        // 宿主机内存负载指标
        registerStrategy(PatrolMetricEnum.MEMORY_LOAD, metricType ->
                new ZjVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.HOST,
                        HardWareRespCreateReqDTO.class,
                        host -> "Enabled".equals(host.getState()) && "Connected".equals(host.getStatus()),
                        HardWareRespCreateReqDTO::getUuid,
                        HardWareRespCreateReqDTO::getName,
                        host -> host.getPlatformId().toString(),
                        HardWareRespCreateReqDTO::getPlatformName,
                        "mem_task",
                        "MemoryUsage",
                        null,
                        "avg",
                        0
                )
        );
        // 宿主机云主机数量指标
        registerStrategy(PatrolMetricEnum.CLOUD_NUM, metricType ->
                new ThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.HOST,
                        HardWareRespCreateReqDTO.class,
                        host -> "Enabled".equals(host.getState()) && "Connected".equals(host.getStatus()),
                        host -> new BigDecimal(host.getVmCount() != null ? host.getVmCount() : 0),
                        HardWareRespCreateReqDTO::getUuid,
                        HardWareRespCreateReqDTO::getName,
                        host -> host.getPlatformId().toString(),
                        HardWareRespCreateReqDTO::getPlatformName,
                        PatrolInspectionConfigDO::getThresholdType
                )
        );

        // 存储分配容量指标
        registerStrategy(PatrolMetricEnum.ALLOCATE_CAPACITY, metricType ->
                new ThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.STORAGE,
                        StorageRespCreateReqDTO.class,
                        storage -> "Enabled".equals(storage.getState()) && "Connected".equals(storage.getStatus()),
                        storage -> {
                            BigDecimal usedCapacity = new BigDecimal(storage.getUsedCapacity());
                            BigDecimal totalCapacity = new BigDecimal(storage.getTotalCapacity());
                            return totalCapacity.compareTo(BigDecimal.ZERO) > 0 ?
                                    usedCapacity.multiply(BigDecimal.valueOf(100)).divide(totalCapacity, 2, RoundingMode.HALF_UP) : new BigDecimal(0);

                        },
                        StorageRespCreateReqDTO::getUuid,
                        StorageRespCreateReqDTO::getName,
                        storage -> storage.getPlatformId().toString(),
                        StorageRespCreateReqDTO::getPlatformName,
                        PatrolInspectionConfigDO::getThresholdType
                )
        );
        // 存储真实容量指标
        registerStrategy(PatrolMetricEnum.REAL_CAPACITY, metricType ->
                new ThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.STORAGE,
                        StorageRespCreateReqDTO.class,
                        storage -> "Enabled".equals(storage.getState()) && "Connected".equals(storage.getStatus()),
                        storage -> storage.getTotalPhysicalCapacity() != null &&
                                storage.getTotalPhysicalCapacity().compareTo(BigDecimal.ZERO) > 0 ?
                                (storage.getTotalPhysicalCapacity().subtract(storage.getAvailablePhysicalCapacity()))
                                        .multiply(BigDecimal.valueOf(100))
                                        .divide(storage.getTotalPhysicalCapacity(), 2, RoundingMode.HALF_UP) : new BigDecimal(0),
                        StorageRespCreateReqDTO::getUuid,
                        StorageRespCreateReqDTO::getName,
                        storage -> storage.getPlatformId().toString(),
                        StorageRespCreateReqDTO::getPlatformName,
                        PatrolInspectionConfigDO::getThresholdType
                )
        );

        //云主机CPU平均使用率
        registerStrategy(PatrolMetricEnum.CPU_AVERAGE_USAGE, metricType ->
                new ZjVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.CLOUD,
                        HostInfoRespCreateReqDTO.class,
                        cloud -> "Running".equals(cloud.getState()),
                        HostInfoRespCreateReqDTO::getUuid,
                        HostInfoRespCreateReqDTO::getName,
                        cloud -> cloud.getPlatformId().toString(),
                        HostInfoRespCreateReqDTO::getPlatformName,
                        "cpu_task",
                        "CPUUsedUtilization",
                        null,
                        "avg",
                        0
                )
        );

        //云主机CPU使用率峰值指标
        registerStrategy(PatrolMetricEnum.CPU_MAX_USAGE, metricType ->
                new ZjVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.CLOUD,
                        HostInfoRespCreateReqDTO.class,
                        cloud -> "Running".equals(cloud.getState()),
                        HostInfoRespCreateReqDTO::getUuid,
                        HostInfoRespCreateReqDTO::getName,
                        cloud -> cloud.getPlatformId().toString(),
                        HostInfoRespCreateReqDTO::getPlatformName,
                        "cpu_task",
                        "CPUUsedUtilization",
                        "1m",
                        "max",
                        1
                )
        );

        //云主机磁盘写速率指标
        registerStrategy(PatrolMetricEnum.DISK_WRITE_RATE, metricType ->
                new ZjVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.CLOUD,
                        HostInfoRespCreateReqDTO.class,
                        cloud -> "Running".equals(cloud.getState()),
                        HostInfoRespCreateReqDTO::getUuid,
                        HostInfoRespCreateReqDTO::getName,
                        cloud -> cloud.getPlatformId().toString(),
                        HostInfoRespCreateReqDTO::getPlatformName,
                        "disk_task",
                        "DiskWriteBytes",
                        "1m",
                        "avg",
                        1
                )
        );

        //云主机磁盘读速率指标
        registerStrategy(PatrolMetricEnum.DISK_READ_RATE, metricType ->
                new ZjVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.CLOUD,
                        HostInfoRespCreateReqDTO.class,
                        cloud -> "Running".equals(cloud.getState()),
                        HostInfoRespCreateReqDTO::getUuid,
                        HostInfoRespCreateReqDTO::getName,
                        cloud -> cloud.getPlatformId().toString(),
                        HostInfoRespCreateReqDTO::getPlatformName,
                        "disk_task",
                        "DiskReadBytes",
                        "1m",
                        "avg",
                        1
                )
        );

        //云主机网卡接收速率指标
        registerStrategy(PatrolMetricEnum.CLOUD_NET_IN_RATE, metricType ->
                new ZjVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.CLOUD,
                        HostInfoRespCreateReqDTO.class,
                        cloud -> "Running".equals(cloud.getState()),
                        HostInfoRespCreateReqDTO::getUuid,
                        HostInfoRespCreateReqDTO::getName,
                        cloud -> cloud.getPlatformId().toString(),
                        HostInfoRespCreateReqDTO::getPlatformName,
                        "net_task",
                        "NetworkInBytes",
                        "1m",
                        "avg",
                        1
                )
        );

        //云主机网卡发送速率指标
        registerStrategy(PatrolMetricEnum.CLOUD_NET_OUT_RATE, metricType ->
                new ZjVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.CLOUD,
                        HostInfoRespCreateReqDTO.class,
                        cloud -> "Running".equals(cloud.getState()),
                        HostInfoRespCreateReqDTO::getUuid,
                        HostInfoRespCreateReqDTO::getName,
                        cloud -> cloud.getPlatformId().toString(),
                        HostInfoRespCreateReqDTO::getPlatformName,
                        "net_task",
                        "NetworkOutBytes",
                        "1m",
                        "avg",
                        1
                )
        );

        //云主机系统盘使用率指标
        registerStrategy(PatrolMetricEnum.CLOUD_SYS_DISK_USAGE, metricType ->
                new ThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.CLOUD,
                        HostInfoRespCreateReqDTO.class,
                        cloud -> "Running".equals(cloud.getState()),
                        cloud -> cloud != null ? cloud.getDiskUsed() : BigDecimal.ZERO,
                        HostInfoRespCreateReqDTO::getUuid,
                        HostInfoRespCreateReqDTO::getName,
                        cloud -> cloud.getPlatformId().toString(),
                        HostInfoRespCreateReqDTO::getPlatformName,
                        PatrolInspectionConfigDO::getThresholdType
                )
        );

        //云主机内存使用率指标
        registerStrategy(PatrolMetricEnum.CLOUD_MEMORY_USAGE, metricType ->
                new ZjVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.CLOUD,
                        HostInfoRespCreateReqDTO.class,
                        cloud -> "Running".equals(cloud.getState()),
                        HostInfoRespCreateReqDTO::getUuid,
                        HostInfoRespCreateReqDTO::getName,
                        cloud -> cloud.getPlatformId().toString(),
                        HostInfoRespCreateReqDTO::getPlatformName,
                        "memory_task",
                        "MemoryUsedUtilization",
                        null,
                        "avg",
                        0
                )
        );

        //云主机内存波动指标
        registerStrategy(PatrolMetricEnum.CLOUD_MEMORY_WAVE, metricType ->
                new ZjVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.CLOUD,
                        HostInfoRespCreateReqDTO.class,
                        cloud -> "Running".equals(cloud.getState()),
                        HostInfoRespCreateReqDTO::getUuid,
                        HostInfoRespCreateReqDTO::getName,
                        cloud -> cloud.getPlatformId().toString(),
                        HostInfoRespCreateReqDTO::getPlatformName,
                        "mem_task",
                        "MemoryUsage",
                        null,
                        null,
                        2
                )
        );

        //云主机持续性CPU饱和指标
        registerStrategy(PatrolMetricEnum.CLOUD_CPU_SATURATION, metricType ->
                new ZjVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.CLOUD,
                        HostInfoRespCreateReqDTO.class,
                        cloud -> "Running".equals(cloud.getState()),
                        HostInfoRespCreateReqDTO::getUuid,
                        HostInfoRespCreateReqDTO::getName,
                        cloud -> cloud.getPlatformId().toString(),
                        HostInfoRespCreateReqDTO::getPlatformName,
                        "cpu_task",
                        "CPUUsedUtilization",
                        null,
                        null,
                        3
                )
        );

        //云主机CPU波动
        registerStrategy(PatrolMetricEnum.CLOUD_CPU_WAVE, metricType ->
                new ZjVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.CLOUD,
                        HostInfoRespCreateReqDTO.class,
                        cloud -> "Running".equals(cloud.getState()),
                        HostInfoRespCreateReqDTO::getUuid,
                        HostInfoRespCreateReqDTO::getName,
                        cloud -> cloud.getPlatformId().toString(),
                        HostInfoRespCreateReqDTO::getPlatformName,
                        "cpu_task",
                        "CPUUsedUtilization",
                        null,
                        null,
                        2
                )
        );

        //云主机快照数量
        registerStrategy(PatrolMetricEnum.CLOUD_SNAPSHOT_NUM, metricType ->
                new ThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.CLOUD,
                        HostInfoRespCreateReqDTO.class,
                        cloud -> "Running".equals(cloud.getState()),
                        cloud -> new BigDecimal(cloud.getSnapshotCount() != null ? cloud.getSnapshotCount() : 0),
                        HostInfoRespCreateReqDTO::getUuid,
                        HostInfoRespCreateReqDTO::getName,
                        cloud -> cloud.getPlatformId().toString(),
                        HostInfoRespCreateReqDTO::getPlatformName,
                        PatrolInspectionConfigDO::getThresholdType
                )
        );

        //云主机内存环比
        registerStrategy(PatrolMetricEnum.CLOUD_MEMORY_RATIO, metricType ->
                new ZjVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.CLOUD,
                        HostInfoRespCreateReqDTO.class,
                        cloud -> "Running".equals(cloud.getState()),
                        HostInfoRespCreateReqDTO::getUuid,
                        HostInfoRespCreateReqDTO::getName,
                        cloud -> cloud.getPlatformId().toString(),
                        HostInfoRespCreateReqDTO::getPlatformName,
                        "mem_task",
                        "MemoryUsage",
                        null,
                        "avg",
                        4
                )
        );

    }

    private static void registerHzVmThresholdMetrics() {
        //网络CPU平均使用率指标
//        registerStrategy(PatrolMetricEnum.NETWORK_CPU_AVG_USAGE, metricType ->
//                new HzVmThresholdMetricHandler<>(
//                        metricType,
//                        PatrolResourceTypeEnum.NETWORK,
//                        Monitor.class,
//                        monitor -> monitor.getStatus() == 1,
//                        Monitor::getId,
//                        Monitor::getName,
//                        monitor -> monitor.getPlatformId().toString(),
//                        Monitor::getPlatformName,
//                        Monitor::getApp
//                )
//        );
//        //网络内存平均使用率指标
//        registerStrategy(PatrolMetricEnum.NETWORK_MEMORY_AVG_USAGE, metricType ->
//                new HzVmThresholdMetricHandler<>(
//                        metricType,
//                        PatrolResourceTypeEnum.NETWORK,
//                        Monitor.class,
//                        monitor -> monitor.getStatus() == 1,
//                        Monitor::getId,
//                        Monitor::getName,
//                        monitor -> monitor.getPlatformId().toString(),
//                        Monitor::getPlatformName,
//                        Monitor::getApp
//                )
//        );
//
//        //安全CPU平均使用率指标
//        registerStrategy(PatrolMetricEnum.FIREWALL_CPU_AVG_USAGE, metricType ->
//                new HzVmThresholdMetricHandler<>(
//                        metricType,
//                        PatrolResourceTypeEnum.FIREWALL,
//                        Monitor.class,
//                        monitor -> monitor.getStatus() == 1,
//                        Monitor::getId,
//                        Monitor::getName,
//                        monitor -> monitor.getPlatformId().toString(),
//                        Monitor::getPlatformName,
//                        Monitor::getApp
//                )
//        );
//        //安全内存平均使用率指标
//        registerStrategy(PatrolMetricEnum.FIREWALL_MEMORY_AVG_USAGE, metricType ->
//                new HzVmThresholdMetricHandler<>(
//                        metricType,
//                        PatrolResourceTypeEnum.FIREWALL,
//                        Monitor.class,
//                        monitor -> monitor.getStatus() == 1,
//                        Monitor::getId,
//                        Monitor::getName,
//                        monitor -> monitor.getPlatformId().toString(),
//                        Monitor::getPlatformName,
//                        Monitor::getApp
//                )
//        );
//
        //操作系统CPU平均使用率指标
        registerStrategy(PatrolMetricEnum.OS_CPU_AVG_USAGE, metricType ->
                new HzVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.OS,
                        Monitor.class,
                        monitor -> monitor.getStatus() == 1,
                        Monitor::getId,
                        Monitor::getName,
                        monitor -> monitor.getPlatformId().toString(),
                        Monitor::getPlatformName,
                        Monitor::getApp,
                        null,
                        "avg",
                        0
                )
        );

        //操作系统内存平均使用率指标
        registerStrategy(PatrolMetricEnum.OS_MEMORY_AVG_USAGE, metricType ->
                new HzVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.OS,
                        Monitor.class,
                        monitor -> monitor.getStatus() == 1,
                        Monitor::getId,
                        Monitor::getName,
                        monitor -> monitor.getPlatformId().toString(),
                        Monitor::getPlatformName,
                        Monitor::getApp,
                        null,
                        "avg",
                        0
                )
        );

        //操作系统内存波动指标
        registerStrategy(PatrolMetricEnum.OS_MEMORY_WAVE, metricType ->
                new HzVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.OS,
                        Monitor.class,
                        monitor -> monitor.getStatus() == 1,
                        Monitor::getId,
                        Monitor::getName,
                        monitor -> monitor.getPlatformId().toString(),
                        Monitor::getPlatformName,
                        Monitor::getApp,
                        null,
                        null,
                        2
                )
        );

        //操作系统持续性CPU饱和指标
        registerStrategy(PatrolMetricEnum.OS_CPU_SATURATION, metricType ->
                new HzVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.OS,
                        Monitor.class,
                        monitor -> monitor.getStatus() == 1,
                        Monitor::getId,
                        Monitor::getName,
                        monitor -> monitor.getPlatformId().toString(),
                        Monitor::getPlatformName,
                        Monitor::getApp,
                        null,
                        null,
                        3
                )
        );

        //操作系统CPU波动指标
        registerStrategy(PatrolMetricEnum.OS_CPU_WAVE, metricType ->
                new HzVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.OS,
                        Monitor.class,
                        monitor -> monitor.getStatus() == 1,
                        Monitor::getId,
                        Monitor::getName,
                        monitor -> monitor.getPlatformId().toString(),
                        Monitor::getPlatformName,
                        Monitor::getApp,
                        null,
                        null,
                        2
                )
        );

        //操作系统CPU进程平均占用率指标
        registerStrategy(PatrolMetricEnum.OS_CPU_PROCESS_AVG_USAGE, metricType ->
                new HzVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.OS,
                        Monitor.class,
                        monitor -> monitor.getStatus() == 1 && !monitor.getApp().equals("windows") && !monitor.getApp().equals("linux_snmp"),
                        Monitor::getId,
                        Monitor::getName,
                        monitor -> monitor.getPlatformId().toString(),
                        Monitor::getPlatformName,
                        Monitor::getApp,
                        null,
                        "avg",
                        0
                )
        );

        //操作系统CPU进程占用率峰值指标
        registerStrategy(PatrolMetricEnum.OS_CPU_PROCESS_PEAK_USAGE, metricType ->
                new HzVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.OS,
                        Monitor.class,
                        monitor -> monitor.getStatus() == 1 && !monitor.getApp().equals("windows") && !monitor.getApp().equals("linux_snmp"),
                        Monitor::getId,
                        Monitor::getName,
                        monitor -> monitor.getPlatformId().toString(),
                        Monitor::getPlatformName,
                        Monitor::getApp,
                        "1m",
                        "max",
                        1
                )
        );

        //操作系统内存进程平均占用率指标
        registerStrategy(PatrolMetricEnum.OS_MEMORY_PROCESS_AVG_USAGE, metricType ->
                new HzVmThresholdMetricHandler<>(
                        metricType,
                        PatrolResourceTypeEnum.OS,
                        Monitor.class,
                        monitor -> monitor.getStatus() == 1,
                        Monitor::getId,
                        Monitor::getName,
                        monitor -> monitor.getPlatformId().toString(),
                        Monitor::getPlatformName,
                        Monitor::getApp,
                        null,
                        "avg",
                        0
                )
        );

    }

    /**
     * 注册指标创建策略
     *
     * @param metricType 指标类型
     * @param creator    创建函数
     */
    public static <T extends MetricHandler<?>> void registerStrategy(PatrolMetricEnum metricType, Function<PatrolMetricEnum, T> creator) {
        if (metricType == null || creator == null) {
            throw new IllegalArgumentException("指标类型和创建函数不能为空");
        }
        METRIC_CREATORS.put(metricType, creator);
    }

    /**
     * 根据指标枚举创建指标实例
     *
     * @param metricType 指标枚举
     * @return 指标实例
     */
    public static MetricHandler<?> createMetric(PatrolMetricEnum metricType) {
        if (metricType == null) {
            throw new IllegalArgumentException("指标类型不能为空");
        }

        Function<PatrolMetricEnum, ? extends MetricHandler<?>> creator = METRIC_CREATORS.get(metricType);
        if (creator == null) {
            throw new IllegalArgumentException("[" + metricType.getName() + "]该巡检项未设置");
        }
        return creator.apply(metricType);
    }
}