package cn.iocoder.zj.module.om.dal.dataobject.patrolresultcategory;

import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 巡检结果分类 DO
 *
 * <AUTHOR>
 */
@TableName("om_patrol_result_category")
@KeySequence("om_patrol_result_category_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PatrolResultCategoryDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 巡检记录ID
     */
    private Long recordId;
    /**
     * 资源类型(存储资源/宿主机资源/云主机资源/云硬盘资源/等)
     */
    private String resourceType;
    /**
     * 指标名称(CPU分配/内存分配/网卡入速度等)
     */
    private String metricName;
    /**
     * 巡检资源数
     */
    private Integer resourceCount;
    /**
     * 检测结果：0 正常 1 异常
     */
    private Integer status;
    /**
     * 正常数量
     */
    private Integer normalCount;
    /**
     * 低风险数量
     */
    private Integer lowRiskCount;
    /**
     * 中风险数量
     */
    private Integer mediumRiskCount;
    /**
     * 高风险数量
     */
    private Integer highRiskCount;
    /**
     * 异常数量
     */
    private Integer abnormalCount;
    /**
     * 详情
     */
    private String detail;

    /**
     * 租户编号
     */
    private Long tenantId;

}
