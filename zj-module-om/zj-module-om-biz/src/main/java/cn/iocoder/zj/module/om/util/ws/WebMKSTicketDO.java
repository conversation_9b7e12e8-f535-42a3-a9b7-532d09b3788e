package cn.iocoder.zj.module.om.util.ws;

import lombok.Data;

@Data
public class WebMKSTicketDO {

    /**
     * 主机地址
     */
    private String host;

    /**
     * 端口号
     */
    private int port;

    /**
     * 票据信息
     */
    private String ticket;

    /**
     * SSL指纹
     */
    private String sslThumbprint;

    /**
     * WebMKS访问URL
     */
    private String url;

    @Override
    public String toString() {
        return String.format("WebMKSTicket{host='%s', port=%d, ticket='%s', url='%s'}",
                host, port, ticket, url);
    }
}
