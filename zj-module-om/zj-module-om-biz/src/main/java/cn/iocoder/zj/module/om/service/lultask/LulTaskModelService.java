package cn.iocoder.zj.module.om.service.lultask;


import cn.iocoder.zj.module.om.Inspection.job.model.LulTaskModel;

import java.util.List;

public interface LulTaskModelService {

    List<LulTaskModel> findAll();

    void addTask(LulTaskModel taskCacheModel);

    void updateTasByJobId(LulTaskModel taskCacheModel);

    void removeTask(LulTaskModel taskCacheModel);

    LulTaskModel getTaskByJobId(Long jobId);
}
