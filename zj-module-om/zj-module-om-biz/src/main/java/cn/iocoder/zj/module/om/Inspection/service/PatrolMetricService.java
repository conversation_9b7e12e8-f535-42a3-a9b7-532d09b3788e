package cn.iocoder.zj.module.om.Inspection.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.mail.MailAccount;
import cn.hutool.extra.mail.MailUtil;
import cn.iocoder.cloud.module.cloudedge.api.monitor.MonitorApi;
import cn.iocoder.cloud.module.cloudedge.api.monitor.dto.MonitorReq;
import cn.iocoder.zj.framework.common.constants.FileTypeConstants;
import cn.iocoder.zj.framework.common.pojo.UserBindDTO;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.framework.common.util.string.StringUtil;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.zj.module.infra.api.file.FileApi;
import cn.iocoder.zj.module.monitor.api.hardware.HardWareInfoApi;
import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.hostinfo.HostInfoApi;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.storage.StorageInfoApi;
import cn.iocoder.zj.module.monitor.api.storage.dto.StorageRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.valume.VolumeApi;
import cn.iocoder.zj.module.monitor.api.valume.dto.VolumeDTO;
import cn.iocoder.zj.module.monitor.pojo.AssetReqVO;
import cn.iocoder.zj.module.om.Inspection.data.PatrolCompareResult;
import cn.iocoder.zj.module.om.Inspection.data.Section;
import cn.iocoder.zj.module.om.Inspection.metric.PatrolMetricFactory;
import cn.iocoder.zj.module.om.controller.admin.patrolinspectionconfig.vo.PatrolExecuteReqVO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolabnormaldetail.PatrolAbnormalDetailDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspectionconfig.PatrolInspectionConfigDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolplan.PatrolPlanDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolrecord.PatrolRecordConfigDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolrecord.PatrolRecordDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolresultcategory.PatrolResultCategoryDO;
import cn.iocoder.zj.module.om.dal.mysql.patrolabnormaldetail.PatrolAbnormalDetailMapper;
import cn.iocoder.zj.module.om.dal.mysql.patrolinspectionconfig.PatrolInspectionConfigMapper;
import cn.iocoder.zj.module.om.dal.mysql.patrolrecord.PatrolRecordMapper;
import cn.iocoder.zj.module.om.dal.mysql.patrolresultcategory.PatrolResultCategoryMapper;
import cn.iocoder.zj.module.om.enums.PatrolAssetType;
import cn.iocoder.zj.module.om.enums.PatrolMetricEnum;
import cn.iocoder.zj.module.om.enums.PatrolResourceTypeEnum;
import cn.iocoder.zj.module.om.enums.PatrolRiskEnum;
import cn.iocoder.zj.module.system.api.tenant.TenantApi;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.*;
import com.deepoove.poi.data.style.BorderStyle;
import com.deepoove.poi.data.style.Style;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static cn.iocoder.zj.module.om.Inspection.util.InspectionUtil.convertDurationUnitToChinese;

@Slf4j
@Component
public class PatrolMetricService {

    @Resource
    private StorageInfoApi storageInfoApi;

    @Resource
    private HardWareInfoApi hostInfoApi;

    @Resource
    private HostInfoApi cloudInfoApi;

    @Resource
    private VolumeApi volumeApi;

    @Resource
    private MonitorApi monitorApi;

    @Resource
    private PatrolRecordMapper patrolRecordMapper;

    @Resource
    private PatrolResultCategoryMapper patrolResultCategoryMapper;

    @Resource
    private PatrolAbnormalDetailMapper patrolAbnormalDetailMapper;

    @Resource
    private PatrolInspectionConfigMapper patrolInspectionConfigMapper;

    @Resource
    AdminUserApi adminUserApi;

    @Resource
    private TenantApi tenantApi;

    @Resource
    private FileApi fileApi;
    @Resource
    private RedisTemplate redisTemplate;

    private static void sleepUntilTotalIsAtLeast3To5Min(long minutes) {
        final long MIN_TOTAL = 3 * 60 * 1000L;
        final long MAX_TOTAL = 5 * 60 * 1000L;

        if (minutes >= MAX_TOTAL) {
            return; // 已经超过5分钟，无需等待
        }

        // 计算随机等待时间范围：[MIN_TOTAL - duration, MAX_TOTAL - duration]
        long minWait = Math.max(0, MIN_TOTAL - minutes);
        long maxWait = MAX_TOTAL - minutes;
        long waitMillis = minWait + (long) (Math.random() * (maxWait - minWait));

        try {
            Thread.sleep(waitMillis);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("等待过程中被中断", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void patrolMetric(PatrolPlanDO patrolPlanDO) {
        if (patrolPlanDO != null) {
            Long tenantId = patrolPlanDO.getSysSettingTenant();
            Long loginTenantId = patrolPlanDO.getTenantId();
            String tenantName = tenantApi.getTenantSimpleInfo(tenantId).getData().getName();
            Map<String, List<?>> resourceDataMap = new HashMap<>();
            AssetReqVO reqVO = new AssetReqVO();
            reqVO.setTenantId(tenantId);
            List<Long> platformIds = StrUtil.isEmpty(patrolPlanDO.getPlatformIds()) ? new ArrayList<>()
                    : Arrays.stream(patrolPlanDO.getPlatformIds().split(",")).map(Long::valueOf).toList();
            reqVO.setPlatformIds(platformIds);
            //获取资源类型
            List<String> resourceTypes = List.of(patrolPlanDO.getResourceType().split(","));
            List<String> monitorTypes = resourceTypes.stream().filter(r -> PatrolResourceTypeEnum.getByCode(r).getAssetType()
                    .equals(PatrolAssetType.MONITOR)).toList();
            List<Monitor> monitorList = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(monitorTypes)) {
                MonitorReq req = new MonitorReq();
                req.setTenantId(tenantId);
                req.setPlatformIds(platformIds);
                monitorList = monitorApi.getMonitorByTenantId(req).getData();
            }
            List<StorageRespCreateReqDTO> storageList = new ArrayList<>();
            List<HardWareRespCreateReqDTO> hostList = new ArrayList<>();
            for (String resourceType : resourceTypes) {
                //根据资源类型获取资产列表
                switch (resourceType) {
                    case "storage":
                        //从数据库中获取存储数据
                        storageList = storageInfoApi.getStoragesByTenantOrPlatforms(reqVO).getData();
                        resourceDataMap.put(resourceType, storageList == null ? new ArrayList<>() : storageList);
                        break;
                    case "host":
                        //从数据库中获取宿主机数据
                        hostList = hostInfoApi.getHardwareByTenantOrPlatforms(reqVO).getData();
                        //循环hostList,根据uuid 获取cloudInfoList 中hardwareUuid相同的云主机数量
                        if (ObjectUtil.isNotEmpty(hostList)) {
                            List<HostInfoRespCreateReqDTO> cloudInfoList = cloudInfoApi.getHostListByTenantOrPlatforms(reqVO).getData();
                            for (HardWareRespCreateReqDTO host : hostList) {
                                long cloudHostCount = cloudInfoList.stream()
                                        .filter(cloud -> cloud != null && cloud.getHardwareUuid() != null) // 先去除 null
                                        .filter(cloud -> cloud.getHardwareUuid().equals(host.getUuid())) // 再匹配 UUID
                                        .count();
                                host.setVmCount((int) cloudHostCount);
                            }
                            resourceDataMap.put(resourceType, hostList);
                        } else {
                            resourceDataMap.put(resourceType, new ArrayList<>());
                        }
                        break;
                    case "cloud":
                        //从数据库中获取云主机数据
                        List<HostInfoRespCreateReqDTO> cloudList = cloudInfoApi.getHostListByTenantOrPlatforms(reqVO).getData();
                        resourceDataMap.put(resourceType, cloudList == null ? new ArrayList<>() : cloudList);
                        break;
                    case "disk":
                        //从数据库中获取云盘数据
                        List<VolumeDTO> volumeList = volumeApi.getVolumeByTenantOrPlatforms(reqVO).getData();
                        resourceDataMap.put(resourceType, volumeList == null ? new ArrayList<>() : volumeList);
                        break;
                    case "monitor":
                        resourceDataMap.put(resourceType, monitorList);
                        break;
                    case "network":
                        //从数据库中获取网络数据
                        List<Monitor> networkList = new ArrayList<>();
                        if (ObjectUtil.isNotEmpty(monitorList)) {
                            networkList = monitorList.stream().filter(m -> m.getCategory().equals("network")).toList();
                        }
                        resourceDataMap.put(resourceType, networkList);
                        break;
                    case "firewall":
                        //从数据库中获取安全数据
                        List<Monitor> firewallList = new ArrayList<>();
                        if (ObjectUtil.isNotEmpty(monitorList)) {
                            firewallList = monitorList.stream().filter(m -> m.getCategory().equals("firewall")).toList();
                        }
                        resourceDataMap.put(resourceType, firewallList);
                        break;
                    case "os":
                        //从数据库中获取操作系统数据
                        List<Monitor> osList = new ArrayList<>();
                        if (ObjectUtil.isNotEmpty(monitorList)) {
                            osList = monitorList.stream().filter(m -> m.getCategory().equals("os")).toList();
                        }
                        resourceDataMap.put(resourceType, osList);
                        break;
                }
            }
            List<String> patrolItems = List.of(patrolPlanDO.getPatrolItem().split(","));
            //根据巡检计划id获取巡检设置列表
            List<PatrolInspectionConfigDO> patrolInspectionConfigDOList = patrolInspectionConfigMapper
                    .getConfigsByPlanId(patrolPlanDO.getId());

            //根据不同的指标类型比较指标的阈值
            List<PatrolCompareResult> patrolCompareResults = new ArrayList<>();
            int abnormalCount = 0;
            for (String patrolItem : patrolItems) {
                List<PatrolInspectionConfigDO> configs = patrolInspectionConfigDOList.stream()
                        .filter(config -> config.getMetricName().equals(patrolItem))
                        .toList();
                PatrolMetricEnum patrolMetricEnum = PatrolMetricEnum.getByCode(patrolItem);
                PatrolCompareResult result = PatrolMetricFactory.createMetric(patrolMetricEnum)
                        .compare(resourceDataMap.get(patrolMetricEnum.getResourceType().getCode()), configs, loginTenantId);
                patrolCompareResults.add(result);
                abnormalCount += result.getTotalAbnormalCount();
            }
            Date endTime = new Date();
            //计算 巡检时间是多少分钟
            long minutes = (endTime.getTime() - patrolPlanDO.getLastExecutionTime().getTime()) / 1000 / 60;
            //小于5分钟的 休眠一会
            sleepUntilTotalIsAtLeast3To5Min(minutes);

            List<PatrolResultCategoryDO> patrolResultCategoryList = new ArrayList<>();
            List<PatrolAbnormalDetailDO> details = new ArrayList<>();
            patrolCompareResults.forEach(result -> {
                PatrolResultCategoryDO category = result.getCategory();
                patrolResultCategoryList.add(category);
                List<PatrolAbnormalDetailDO> abnormalList = result.getAbnormalDetails();
                details.addAll(abnormalList);
            });

            PatrolRecordDO patrolRecordDO = new PatrolRecordDO();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            patrolRecordDO.setRecordName(patrolPlanDO.getName() + "_" + sdf.format(patrolPlanDO.getLastExecutionTime()));
            patrolRecordDO.setStartTime(patrolPlanDO.getLastExecutionTime());
            patrolRecordDO.setTotalItemCount(patrolItems.size());
            patrolRecordDO.setAbnormalCount(abnormalCount);
            patrolRecordDO.setTotalResourceCount(getTotalResourceCount(patrolCompareResults));
            patrolRecordDO.setPlanId(patrolPlanDO.getId());
            patrolRecordDO.setTenantId(loginTenantId);
            patrolRecordDO.setSysSettingTenant(patrolPlanDO.getSysSettingTenant());
            patrolRecordDO.setPlatformIds(patrolPlanDO.getPlatformIds());
            patrolRecordDO.setEndTime(new Date());
            //生成报告
            Map<String, String> file = generatePatrolReport(patrolRecordDO,
                    patrolInspectionConfigDOList,
                    patrolPlanDO,
                    patrolResultCategoryList,
                    details,
                    hostList,
                    storageList,
                    tenantName);
            String fileName = file.get("fileName");
            String filePath = file.get("filePath");
            patrolRecordDO.setFileName(fileName);
            patrolRecordDO.setFilePath(filePath);
            //巡检记录入库
            patrolRecordMapper.insert(patrolRecordDO);
            //巡检记录设置入库
            if (ObjectUtil.isNotEmpty(patrolInspectionConfigDOList)) {
                patrolInspectionConfigDOList.forEach(p -> {
                    PatrolRecordConfigDO pillRecordDO = PatrolRecordConfigDO
                            .builder()
                            .recordId(patrolRecordDO.getId())
                            .configId(p.getId())
                            .build();
                    patrolRecordMapper.insertRecordConfig(pillRecordDO);
                });
            }
            //巡检分类结果入库
            patrolCompareResults.forEach(result -> {
                PatrolResultCategoryDO category = result.getCategory();
                category.setRecordId(patrolRecordDO.getId());
                patrolResultCategoryMapper.insert(category);
                List<PatrolAbnormalDetailDO> abnormalList = result.getAbnormalDetails();
                abnormalList.forEach(abnormalDetail -> {
                    abnormalDetail.setRecordId(patrolRecordDO.getId());
                    abnormalDetail.setCategoryId(category.getId());
                    patrolAbnormalDetailMapper.insert(abnormalDetail);
                });
            });
            //发送邮件
            sendEmailAsync(patrolRecordDO, Long.valueOf(patrolPlanDO.getCreator()));
        }
    }

    /**
     * 获取巡检的总资源数量
     */
    private int getTotalResourceCount(List<PatrolCompareResult> patrolCompareResults) {
        //首先取资产大类为监控的资源数量
        List<PatrolResourceTypeEnum> monitorResourceTypes = PatrolResourceTypeEnum.getByAssetType(PatrolAssetType.MONITOR);
        List<PatrolCompareResult> monitorResults = patrolCompareResults.stream()
                .filter(result -> monitorResourceTypes.contains(PatrolResourceTypeEnum.getByCode(result.getCategory().getResourceType())))
                .toList();

        //获取监控下的最大资源数
        int monitor = monitorResults.stream()
                .mapToInt(result -> result.getCategory().getResourceCount())
                .max()
                .orElse(0);

        //巡检分类根据 资源类型分组 取分组中 resourceCount 的最大值 然后求和 这次巡检的总资源数量

        List<PatrolCompareResult> notMonitorResults = patrolCompareResults.stream()
                .filter(result -> !monitorResourceTypes.contains(PatrolResourceTypeEnum.getByCode(result.getCategory().getResourceType())))
                .toList();
        Map<String, List<PatrolCompareResult>> groupByResourceType = notMonitorResults.stream()
                .collect(Collectors.groupingBy(result -> result.getCategory().getResourceType()));

        // 遍历分组，找到每个资源类型的最大 resourceCount
        Map<String, Integer> maxResourceCountByType = new HashMap<>();
        for (Map.Entry<String, List<PatrolCompareResult>> entry : groupByResourceType.entrySet()) {
            String resourceType = entry.getKey();
            List<PatrolCompareResult> results = entry.getValue();
            int maxResourceCount = results.stream()
                    .mapToInt(result -> result.getCategory().getResourceCount())
                    .max()
                    .orElse(0);
            maxResourceCountByType.put(resourceType, maxResourceCount);
        }
        // 计算总资源数量
        int notMonitor = maxResourceCountByType.values().stream().mapToInt(Integer::intValue).sum();
        return monitor + notMonitor;
    }

    /**
     * 一键巡检 执行一次巡检任务
     *
     */
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void executePatrol(PatrolExecuteReqVO reqVO, LoginUser loginUser) {
        Long loginTenantId = loginUser.getTenantId();
        TenantContextHolder.setTenantId(loginTenantId);
        List<Long> platformIds = reqVO.getPlatformIds() != null ? reqVO.getPlatformIds() : new ArrayList<>();
        AssetReqVO assetReqVO = new AssetReqVO();
        assetReqVO.setPlatformIds(platformIds);
        assetReqVO.setTenantId(reqVO.getTenantId());
        Long tenantId = reqVO.getTenantId();
        String tenantName = tenantApi.getTenantSimpleInfo(tenantId).getData().getName();
        Long sysSettingTenant = null;
        if (loginTenantId == null) {
            throw new RuntimeException("用户未登录");
        }
        //内存中添加这次巡检的完成标记 一开始设置成 false 正在执行中 true 执行完成
        // 使用缓存服务存储巡检状态
        String cacheKey = "patrol_status_";
        String cacheKey1 = "patrol_insert_log_";
        if (loginTenantId == 1L) {
            if (tenantId == null) {
                throw new RuntimeException("请选择租户");
            }
            cacheKey = cacheKey + loginTenantId + "_" + tenantId;
            cacheKey1 = cacheKey1 + loginTenantId + "_" + tenantId;
            sysSettingTenant = tenantId;
        } else {
            cacheKey = cacheKey + tenantId;
            cacheKey1 = cacheKey1 + tenantId;
        }
        //添加到redis
        redisTemplate.opsForValue().set(cacheKey, false);
        redisTemplate.opsForValue().set(cacheKey1, true);

        //巡检开始时间
        Date startTime = new Date();
        //查询当前租户下planId为空的巡检设置

        List<PatrolInspectionConfigDO> configDOS = patrolInspectionConfigMapper
                .selectLists(loginTenantId);
        //获取资源类型
        List<String> resourceTypes = new ArrayList<>(configDOS.stream().map(PatrolInspectionConfigDO::getResourceType).distinct().toList());
        resourceTypes.add("disk");
        resourceTypes.add("monitor");
        List<String> monitorTypes = resourceTypes.stream().filter(r -> PatrolResourceTypeEnum.getByCode(r).getAssetType()
                .equals(PatrolAssetType.MONITOR)).toList();
        List<Monitor> monitorList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(monitorTypes)) {
            MonitorReq req = new MonitorReq();
//            req.setCategoryList(monitorTypes);
            req.setTenantId(tenantId);
            req.setPlatformIds(reqVO.getPlatformIds());
            monitorList = monitorApi.getMonitorByTenantId(req).getData();
//            if (ObjectUtil.isNotEmpty(monitorList)) {
//                //筛选出状态正常的 监控资产
//                monitorList = monitorList.stream().filter(m -> m.getStatus() == 1).toList();
//            }
        }
        Map<String, List<?>> resourceDataMap = new HashMap<>();
        List<StorageRespCreateReqDTO> storageList = new ArrayList<>();
        List<HardWareRespCreateReqDTO> hostList = new ArrayList<>();
        for (String resourceType : resourceTypes) {
            //根据资源类型获取资产列表
            switch (resourceType) {
                case "storage":
                    //从数据库中获取存储数据
                    storageList = storageInfoApi.getStoragesByTenantOrPlatforms(assetReqVO).getData();
                    resourceDataMap.put(resourceType, storageList == null ? new ArrayList<>() : storageList);
                    break;
                case "host":
                    //从数据库中获取宿主机数据
                    hostList = hostInfoApi.getHardwareByTenantOrPlatforms(assetReqVO).getData();
                    //循环hostList,根据uuid 获取cloudInfoList 中hardwareUuid相同的云主机数量
                    if (ObjectUtil.isNotEmpty(hostList)) {
                        List<HostInfoRespCreateReqDTO> cloudInfoList = cloudInfoApi.getHostListByTenantOrPlatforms(assetReqVO).getData();
                        for (HardWareRespCreateReqDTO host : hostList) {
                            long cloudHostCount = cloudInfoList.stream()
                                    .filter(cloud -> cloud != null && cloud.getHardwareUuid() != null) // 先去除 null
                                    .filter(cloud -> cloud.getHardwareUuid().equals(host.getUuid())) // 再匹配 UUID
                                    .count();
                            host.setVmCount((int) cloudHostCount);
                        }
                        resourceDataMap.put(resourceType, hostList);
                    } else {
                        resourceDataMap.put(resourceType, new ArrayList<>());
                    }

                    break;
                case "cloud":
                    //从数据库中获取云主机数据
                    List<HostInfoRespCreateReqDTO> cloudList = cloudInfoApi.getHostListByTenantOrPlatforms(assetReqVO).getData();
                    resourceDataMap.put(resourceType, cloudList == null ? new ArrayList<>() : cloudList);
                    break;
                case "disk":
                    //从数据库中获取云盘数据
                    List<VolumeDTO> volumeList = volumeApi.getVolumeByTenantOrPlatforms(assetReqVO).getData();
                    resourceDataMap.put(resourceType, volumeList == null ? new ArrayList<>() : volumeList);
                    break;
                case "monitor":
                    resourceDataMap.put(resourceType, monitorList);
                    break;
                case "network":
                    //从数据库中获取网络数据
                    List<Monitor> networkList = new ArrayList<>();
                    if (ObjectUtil.isNotEmpty(monitorList)) {
                        networkList = monitorList.stream().filter(m -> m.getCategory().equals("network")).toList();
                    }
                    resourceDataMap.put(resourceType, networkList);
                    break;
                case "firewall":
                    //从数据库中获取安全数据
                    List<Monitor> firewallList = new ArrayList<>();
                    if (ObjectUtil.isNotEmpty(monitorList)) {
                        firewallList = monitorList.stream().filter(m -> m.getCategory().equals("firewall")).toList();
                    }
                    resourceDataMap.put(resourceType, firewallList);
                    break;
                case "os":
                    //从数据库中获取操作系统数据
                    List<Monitor> osList = new ArrayList<>();
                    if (ObjectUtil.isNotEmpty(monitorList)) {
                        osList = monitorList.stream().filter(m -> m.getCategory().equals("os")).toList();
                    }
                    resourceDataMap.put(resourceType, osList);
                    break;
            }
        }
        //获取巡检项
        List<String> patrolItems = configDOS.stream().map(PatrolInspectionConfigDO::getMetricName).distinct().toList();
        patrolItems = addPatrolItems(patrolItems);
        //根据不同的指标类型比较指标的阈值
        List<PatrolCompareResult> patrolCompareResults = new ArrayList<>();
        int abnormalCount = 0;
        for (String patrolItem : patrolItems) {
//            if (patrolItem.equals(PatrolMetricEnum.HOST_MEMORY_USAGE.getCode()) ||
//                    patrolItem.equals(PatrolMetricEnum.CLOUD_MEMORY_USAGE.getCode())) {
//                continue;
//            }
            List<PatrolInspectionConfigDO> configs = configDOS.stream()
                    .filter(config -> config.getMetricName().equals(patrolItem))
                    .toList();
            PatrolMetricEnum patrolMetricEnum = PatrolMetricEnum.getByCode(patrolItem);
            PatrolCompareResult result = PatrolMetricFactory.createMetric(patrolMetricEnum)
                    .compare(resourceDataMap.get(patrolMetricEnum.getResourceType().getCode()), configs, loginTenantId);
            patrolCompareResults.add(result);
            abnormalCount += result.getTotalAbnormalCount();
        }
        Date endTime = new Date();
        //计算 巡检时间是多少分钟
        long minutes = (endTime.getTime() - startTime.getTime()) / 1000 / 60;
        //小于5分钟的 休眠一会
        sleepUntilTotalIsAtLeast3To5Min(minutes);
        //判断缓存日志中是否插入日志
        boolean found = Boolean.TRUE.equals(redisTemplate.opsForValue().get(cacheKey1));
        if (!found) {
            redisTemplate.delete(cacheKey);
            redisTemplate.delete(cacheKey1);
            return;
        }
        PatrolRecordDO patrolRecordDO = new PatrolRecordDO();

        List<PatrolResultCategoryDO> patrolResultCategoryList = new ArrayList<>();
        List<PatrolAbnormalDetailDO> details = new ArrayList<>();
        patrolCompareResults.forEach(result -> {
            PatrolResultCategoryDO category = result.getCategory();
            patrolResultCategoryList.add(category);
            List<PatrolAbnormalDetailDO> abnormalList = result.getAbnormalDetails();
            details.addAll(abnormalList);
        });

        patrolRecordDO.setStartTime(startTime);
        patrolRecordDO.setTotalItemCount(patrolItems.size());
        patrolRecordDO.setAbnormalCount(abnormalCount);
        patrolRecordDO.setTotalResourceCount(getTotalResourceCount(patrolCompareResults));
        patrolRecordDO.setTenantId(loginTenantId);
        patrolRecordDO.setSysSettingTenant(sysSettingTenant);
        patrolRecordDO.setPlatformIds(platformIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        patrolRecordDO.setEndTime(new Date());
        //生成报告
        Map<String, String> file = generatePatrolReport(patrolRecordDO,
                configDOS,
                null,
                patrolResultCategoryList,
                details,
                hostList,
                storageList,
                tenantName);
        String fileName = file.get("fileName");
        String filePath = file.get("filePath");
        patrolRecordDO.setFileName(fileName);
        patrolRecordDO.setFilePath(filePath);
        patrolRecordDO.setCreator(String.valueOf(loginUser.getId()));
        patrolRecordDO.setUpdater(String.valueOf(loginUser.getId()));
        //巡检记录入库
        patrolRecordMapper.insert(patrolRecordDO);
        Long recordId = patrolRecordDO.getId();
        //巡检记录设置入库
        if (ObjectUtil.isNotEmpty(configDOS)) {
            configDOS.forEach(p -> {
                PatrolRecordConfigDO pillRecordDO = PatrolRecordConfigDO
                        .builder()
                        .recordId(recordId)
                        .configId(p.getId())
                        .build();
                patrolRecordMapper.insertRecordConfig(pillRecordDO);
            });
        }
        //巡检分类结果入库
        patrolCompareResults.forEach(result -> {
            PatrolResultCategoryDO category = result.getCategory();
            category.setRecordId(recordId);
            patrolResultCategoryMapper.insert(category);
            Long categoryId = category.getId();
            List<PatrolAbnormalDetailDO> abnormalList = result.getAbnormalDetails();
            abnormalList.forEach(abnormalDetail -> {
                abnormalDetail.setRecordId(recordId);
                abnormalDetail.setCategoryId(categoryId);
                patrolAbnormalDetailMapper.insert(abnormalDetail);
            });
        });
        // 巡检完成，将状态设置为true
        // 更新缓存中的巡检状态
        redisTemplate.opsForValue().set(cacheKey, true);

        //发送邮件
        sendEmailAsync(patrolRecordDO, loginUser.getId());
    }

    /**
     * 一键巡检添加状态巡检项
     */
    private List<String> addPatrolItems(List<String> patrolItemList) {
        List<String> patrolItems = new ArrayList<>();
        patrolItems.add(PatrolMetricEnum.STORAGE_STATE.getCode());
        patrolItems.add(PatrolMetricEnum.HOST_STATE.getCode());
        patrolItems.add(PatrolMetricEnum.CLOUD_STATE.getCode());
        patrolItems.add(PatrolMetricEnum.MOUNT_STATE.getCode());
        patrolItems.add(PatrolMetricEnum.MONITOR_STATE.getCode());
        patrolItems.add(PatrolMetricEnum.INTERNET_EXPOSE_FACE.getCode());
        patrolItems.addAll(patrolItemList);
        return patrolItems;
    }

    @TenantIgnore
    private String createAndDownloadWord(Map<String, Object> paramMap, String fileName) {
        log.info("生成电子协查函参数paramMap = {}", paramMap);
        //文件名称
//        String d = recordName + "_" + createTime ;
        //截取fileName 去掉后缀
        fileName = fileName.substring(0, fileName.lastIndexOf("."));
        fileName = fileName + UUID.fastUUID() + ".docx";

        try {
            // 创建一个临时的输出流
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            String path = "file-template" + File.separator + "inspection.docx";

            // 获取电子协查函模板

            ClassPathResource classPathResource = new ClassPathResource(path);

            try (InputStream inputStream = classPathResource.getInputStream()) {
                // 通过协查函模板，开始生成电子协查函
                try (XWPFTemplate template = XWPFTemplate.compile(inputStream).render(paramMap)) {
                    // 将生成的Word文档写入临时输出流
                    template.write(outputStream);
                    byte[] wordBytes = outputStream.toByteArray();
                    //写入minio
                    String minioPath = StringUtil.getSavePath(fileName, FileTypeConstants.FILE_TYPE, "inspection");
                    String url = fileApi.createFileUrl(fileName, minioPath, wordBytes);
                    template.close();
                    outputStream.close();
                    inputStream.close();
                    return url;
                }
            } catch (Exception e) {
                log.error("创建协查函异常，异常详情：\n{}", e, e);
            }
        } catch (Exception e) {
            log.error("导出Word文档并下载时发生异常：\n{}", e, e);
        }
        return null;
    }

    @Async
    public void sendEmail(PatrolRecordDO patrolRecord, Long userId) {
        String topImgPath = "https://s3.zjiecn.com/zj-server-fh/20240711101838微信图片_20240706144526.png";
        String bottomImgPath = "https://s3.zjiecn.com/zj-server-fh/20240711104229微信图片_20240711101759.png";
        try {
            //根据userId查询用户信息
            AdminUserRespDTO adminUser = adminUserApi.getUserById(userId).getData();
            String fileName = patrolRecord.getFileName().replace(".docx", "");
            byte[] byteBuff = fileApi.ReaderObjects(patrolRecord.getFilePath());
            String templateString = loadTemplate();
            File tempFile = File.createTempFile(fileName, ".docx");
            templateString = templateString.replace("${reportName}", fileName)
                    .replace("${topImageUrl}", topImgPath)
                    .replace("${bottomImageUrl}", bottomImgPath);

            try (OutputStream fileOutputStream = new FileOutputStream(tempFile)) {
                fileOutputStream.write(byteBuff);
            }
            UserBindDTO userBindDO = JsonUtils.parseObject(redisTemplate.opsForValue().get("app_key_secret:app").toString(), UserBindDTO.class);
            try {
                if (userBindDO.getEmailState() == 1) {
                    if (isUserBindInfoInvalid(userBindDO)) {
                        log.info("邮箱参数为空");
                        return;
                    }
                    MailAccount mailAccount = new MailAccount();
                    mailAccount.setHost(userBindDO.getMailSmtpHost());
                    mailAccount.setPort(465);
                    mailAccount.setSslEnable(true);
                    mailAccount.setUser(userBindDO.getMailAddress());
                    mailAccount.setAuth(true);
                    mailAccount.setPass(userBindDO.getMailPassword());
                    mailAccount.setFrom(userBindDO.getMailAddress());
                    MailUtil.send(mailAccount, adminUser.getEmail(), "云星辰智能巡检" + fileName, templateString, true, tempFile);
                    log.info(adminUser.getTenantName() + "的" + fileName + "已送达");
                }
            } catch (Exception e) {
                log.error("发送邮件失败", e);
            }
        } catch (IOException e) {
            log.error("发送邮件失败", e);
        }
    }

    /**
     * 异步发送邮件，不影响主事务
     */
    @Async
    public void sendEmailAsync(PatrolRecordDO patrolRecord, Long userId) {
        try {
            sendEmail(patrolRecord, userId);
        } catch (Exception e) {
            log.error("发送邮件失败", e);
        }
    }

    private String loadTemplate() throws IOException {
        String path = "file-template" + File.separator + "email-template.html";
        ClassPathResource classPathResource = new ClassPathResource(path);
        try (InputStream inputStream = classPathResource.getInputStream()) {
            return IOUtils.toString(inputStream, StandardCharsets.UTF_8);
        }
    }

    private boolean isUserBindInfoInvalid(UserBindDTO userBindDO) {
        return BeanUtil.isEmpty(userBindDO) ||
                StrUtil.isEmpty(userBindDO.getMailAddress()) ||
                StrUtil.isEmpty(userBindDO.getMailPassword()) ||
                StrUtil.isEmpty(userBindDO.getMailSmtpHost());
    }

    /**
     * 生成巡检报告
     */

    public Map<String, String> generatePatrolReport(PatrolRecordDO patrolRecord,
                                                    List<PatrolInspectionConfigDO> configs,
                                                    PatrolPlanDO patrolPlan,
                                                    List<PatrolResultCategoryDO> patrolResultCategoryList,
                                                    List<PatrolAbnormalDetailDO> detail,
                                                    List<HardWareRespCreateReqDTO> hostList,
                                                    List<StorageRespCreateReqDTO> storageList,
                                                    String tenantName) {
        Map<String, Object> paramMap = new HashMap<>();

        if (patrolRecord == null) {
            throw new RuntimeException("巡检记录不存在");
        }
        Long planId = patrolRecord.getPlanId();
        String recordName;
        if (planId != null) {
            //根据巡检计划id 查询巡检计划和巡检设置
            switch (patrolPlan.getPeriodType()) {
                case "day":
                    paramMap.put("type", "巡检日报");
                    break;
                case "week":
                    paramMap.put("type", "巡检周报");
                    break;
                case "month":
                    paramMap.put("type", "巡检月报");
                    break;
            }
            recordName = patrolRecord.getRecordName();
        } else {
            paramMap.put("type", "巡检报告");
            String startTime = DateUtil.format(patrolRecord.getStartTime(), "yyyyMMddHHmmss");
            recordName = "巡检报告_" + startTime;
        }
        paramMap.put("tenantName", tenantName);
        Date startTime = patrolRecord.getStartTime();
        String startTimeStr = DateUtil.format(startTime, "yyyy-MM-dd HH:mm:ss");
        Date endTime = patrolRecord.getEndTime();
        String endTimeStr = DateUtil.format(endTime, "yyyy-MM-dd HH:mm:ss");
        String inspectionTime = startTimeStr + "-" + endTimeStr;
        paramMap.put("inspectionTime", inspectionTime);

        //基本信息
        //检测结果
        String patrolResult = "共检测" + patrolRecord.getTotalItemCount() + "个项目，" +
                patrolRecord.getTotalResourceCount() + "个资源，检测到异常情况" +
                patrolRecord.getAbnormalCount() + "个";
        String createTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        String[][] content = {
                {"报告名称", recordName, "报告生成时间", createTime},
                {"巡检项数量", String.valueOf(patrolRecord.getTotalItemCount()), "巡检资源数", String.valueOf(patrolRecord.getTotalResourceCount())},
                {"检测开始时间", startTimeStr, "检测结束时间", endTimeStr},
                {"检测结果", patrolResult, "", ""}
        };
        String[] colColors = {"C2E5F5", "FFFFFF", "C2E5F5", "FFFFFF"}; // 每列的颜色

        List<RowRenderData> rows = new ArrayList<>();
        for (final String[] strings : content) {
            List<CellRenderData> cells = new ArrayList<>();
            for (int j = 0; j < strings.length; j++) {
                cells.add(Cells.of(strings[j])
                        .bgColor(colColors[j]) // 按列索引取色
                        .create());
            }
            rows.add(Rows.of(cells.toArray(new CellRenderData[0])).create());
        }

        // 设置合并规则：合并第3行（即第4行），列索引1到3
        MergeCellRule mergeRule = MergeCellRule.builder()
                .map(MergeCellRule.Grid.of(3, 1), MergeCellRule.Grid.of(3, 3))
                .build();

        TableRenderData tableRenderData = Tables.of(rows.toArray(new RowRenderData[0]))
                .mergeRule(mergeRule)
                .border(BorderStyle.DEFAULT)
                .create();

        paramMap.put("patrolRecord", tableRenderData);

        //资产信息

        List<Section> sections = new ArrayList<>();
        int titleIndex = 3;

        Map<String, PatrolResultCategoryDO> resourceTypeMap = new HashMap<>();
        //获取巡检分类中的resourceType 是云主机状态，宿主机状态,云存储状态
        if (ObjectUtil.isNotEmpty(patrolResultCategoryList)) {
            //宿主机
            Optional<PatrolResultCategoryDO> hostState = patrolResultCategoryList.stream().filter(
                    result -> result.getMetricName().equals(PatrolMetricEnum.HOST_STATE.getCode())).findFirst();
            hostState.ifPresent(host -> resourceTypeMap.put(host.getResourceType(), host));
            //云主机
            Optional<PatrolResultCategoryDO> cloudHostState = patrolResultCategoryList.stream().filter(
                    result -> result.getMetricName().equals(PatrolMetricEnum.CLOUD_STATE.getCode())).findFirst();
            cloudHostState.ifPresent(cloud -> resourceTypeMap.put(cloud.getResourceType(), cloud));
            //云存储
            Optional<PatrolResultCategoryDO> cloudStorageState = patrolResultCategoryList.stream().filter(
                    result -> result.getMetricName().equals(PatrolMetricEnum.STORAGE_STATE.getCode())).findFirst();
            cloudStorageState.ifPresent(storage -> resourceTypeMap.put(storage.getResourceType(), storage));

            //监控
            Optional<PatrolResultCategoryDO> monitorState = patrolResultCategoryList.stream().filter(
                    result -> result.getMetricName().equals(PatrolMetricEnum.MONITOR_STATE.getCode())).findFirst();
            monitorState.ifPresent(monitor -> resourceTypeMap.put(monitor.getResourceType(), monitor));
        }
        List<RowRenderData> resourceTypeRows = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(resourceTypeMap)) {
            Section assetInfoSection = new Section();
            assetInfoSection.setTitle("资产信息");
            RowRenderData row0 = Rows.of("组件名称", "总数", "正常状态", "其他").center().bgColor("0F9ED5").create();
            resourceTypeRows.add(row0);
            final int[] index = {0};
            resourceTypeMap.forEach((resourceType, patrolResultCategoryDO) -> {
                // 根据索引判断是基数还是偶数,设置对应的背景色
                String bgColor = (index[0] % 2 == 0) ? "FFFFFF" : "CAEDFB";
                int otherCount = patrolResultCategoryDO.getLowRiskCount() + patrolResultCategoryDO.getMediumRiskCount() + patrolResultCategoryDO.getHighRiskCount();
                RowRenderData row = Rows.of(PatrolResourceTypeEnum.getByCode(resourceType).getName(),
                        String.valueOf(patrolResultCategoryDO.getResourceCount()),
                        String.valueOf(patrolResultCategoryDO.getNormalCount()),
                        String.valueOf(otherCount)).center().bgColor(bgColor).create();
                resourceTypeRows.add(row);
                index[0]++;
            });
            TableRenderData assetInfo = Tables.of(ArrayUtil.toArray(resourceTypeRows, RowRenderData.class))
                    .border(BorderStyle.builder().withColor("0F9ED5").withSize(1).withSpace(1).withType(XWPFTable.XWPFBorderType.SINGLE).build())
                    .create();
//            paramMap.put("assetInfo", assetInfo);
            assetInfoSection.setTable(assetInfo);
            sections.add(assetInfoSection);
        }


        BigDecimal totalCpu = BigDecimal.ZERO;
        BigDecimal cpuCommitRate = BigDecimal.ZERO;
        BigDecimal cpuRatio = BigDecimal.ZERO;
        BigDecimal cpuUsedRate = BigDecimal.ZERO;
        BigDecimal totalVirtualMemoryGB = BigDecimal.ZERO;
        BigDecimal memoryCommitRateGB = BigDecimal.ZERO;
        BigDecimal memoryRatio = BigDecimal.ZERO;
        BigDecimal memoryUsedRate = BigDecimal.ZERO;
        BigDecimal totalCapacityGB = BigDecimal.ZERO;
        BigDecimal usedCapacityGB = BigDecimal.ZERO;
        BigDecimal storageRatio = BigDecimal.ZERO;
        BigDecimal storageUsedRate = BigDecimal.ZERO;
        boolean isHost = false;
        boolean isStorage = false;
        if (ObjectUtil.isNotEmpty(hostList)) {
            isHost = true;
            // 计算总和
            BigDecimal cpuUsedNum = BigDecimal.ZERO;
            BigDecimal totalVirtualMemory = BigDecimal.ZERO;
            BigDecimal memoryCommitRate = BigDecimal.ZERO;
            BigDecimal useTotalMemory = BigDecimal.ZERO;
            for (HardWareRespCreateReqDTO host : hostList) {
                //cpu总核数
                BigDecimal totalCpuCapacity = host.getTotalCpuCapacity() != null ? BigDecimal.valueOf(host.getTotalCpuCapacity()) : BigDecimal.ZERO;
                totalCpu = totalCpu.add(totalCpuCapacity);
                cpuCommitRate = cpuCommitRate.add(host.getCpuCommitRate() != null ? host.getCpuCommitRate() : BigDecimal.ZERO);
                //cpu使用率
                BigDecimal cpuUsed = host.getCpuUsed() != null ? host.getCpuUsed().divide(BigDecimal.valueOf(100)) : BigDecimal.ZERO;
                //cpu使用核数
                cpuUsedNum = cpuUsedNum.add(cpuUsed.multiply(totalCpuCapacity));

                //内存总量
                BigDecimal totalMemory = host.getTotalVirtualMemory() != null ? BigDecimal.valueOf(Long.parseLong(host.getTotalVirtualMemory())) : BigDecimal.ZERO;
                totalVirtualMemory = totalVirtualMemory.add(totalMemory);
                memoryCommitRate = memoryCommitRate.add(host.getMemoryCommitRate() != null ? host.getMemoryCommitRate() : BigDecimal.ZERO);

                //内存使用率
                BigDecimal memoryUsed = host.getMemoryUsed() != null ? host.getMemoryUsed().divide(BigDecimal.valueOf(100)) : BigDecimal.ZERO;
                //内存使用量
                useTotalMemory = useTotalMemory.add(memoryUsed.multiply(totalMemory));
            }
            cpuRatio = totalCpu.compareTo(BigDecimal.ZERO) > 0 ? cpuCommitRate.divide(totalCpu, 2, RoundingMode.HALF_UP) : new BigDecimal(0);
            cpuUsedRate = totalCpu.compareTo(BigDecimal.ZERO) > 0 ? cpuUsedNum.divide(totalCpu, 2, RoundingMode.HALF_UP) : new BigDecimal(0);
            totalVirtualMemoryGB = totalVirtualMemory.divide(BigDecimal.valueOf(1024L * 1024L * 1024L), 2, RoundingMode.HALF_UP);
            memoryCommitRateGB = memoryCommitRate.divide(BigDecimal.valueOf(1024L * 1024L * 1024L), 2, RoundingMode.HALF_UP);
            memoryRatio = totalVirtualMemory.compareTo(BigDecimal.ZERO) > 0 ? memoryCommitRate.divide(totalVirtualMemory, 2, RoundingMode.HALF_UP) : new BigDecimal(0);
            BigDecimal useTotalMemoryGB = useTotalMemory.divide(BigDecimal.valueOf(1024L * 1024L * 1024L), 2, RoundingMode.HALF_UP);
            memoryUsedRate = totalVirtualMemoryGB.compareTo(BigDecimal.ZERO) > 0 ? useTotalMemoryGB.divide(totalVirtualMemoryGB, 2, RoundingMode.HALF_UP) : new BigDecimal(0);
        }
//        //获取云存储数据
        if (ObjectUtil.isNotEmpty(storageList)) {
            isStorage = true;
            BigDecimal usedCapacity = BigDecimal.ZERO;
            BigDecimal totalCapacity = BigDecimal.ZERO;
            BigDecimal usedTotalStorage = BigDecimal.ZERO;
            for (StorageRespCreateReqDTO storage : storageList) {
                BigDecimal totalMem = BigDecimal.valueOf(storage.getTotalCapacity() != null ? storage.getTotalCapacity() : 0);
                totalCapacity = totalCapacity.add(totalMem);
                usedCapacity = usedCapacity.add(BigDecimal.valueOf(storage.getUsedCapacity() != null ? storage.getUsedCapacity() : 0));
                //存储使用率
                BigDecimal capacityUtilization = storage.getCapacityUtilization() != null ? storage.getCapacityUtilization().divide(BigDecimal.valueOf(100)) : BigDecimal.ZERO;
                //储存使用量
                usedTotalStorage = usedTotalStorage.add(capacityUtilization.multiply(totalMem));
            }
            totalCapacityGB = totalCapacity.divide(BigDecimal.valueOf(1024L * 1024L * 1024L), 2, RoundingMode.HALF_UP);
            usedCapacityGB = usedCapacity.divide(BigDecimal.valueOf(1024L * 1024L * 1024L), 2, RoundingMode.HALF_UP);
            storageRatio = totalCapacity.compareTo(BigDecimal.ZERO) > 0 ? usedCapacity.divide(totalCapacity, 2, RoundingMode.HALF_UP) : new BigDecimal(0);
            BigDecimal usedTotalStorageGB = usedTotalStorage.divide(BigDecimal.valueOf(1024L * 1024L * 1024L), 2, RoundingMode.HALF_UP);
            storageUsedRate = totalCapacityGB.compareTo(BigDecimal.ZERO) > 0 ? usedTotalStorageGB.divide(totalCapacityGB, 2, RoundingMode.HALF_UP) : new BigDecimal(0);
        }
        String cpuRatioStr = cpuRatio.multiply(BigDecimal.valueOf(100)) + "%";
        String memoryRatioStr = memoryRatio.multiply(BigDecimal.valueOf(100)) + "%";
        String storageRatioStr = storageRatio.multiply(BigDecimal.valueOf(100)) + "%";

        String cpuUsedRateStr = cpuUsedRate.multiply(BigDecimal.valueOf(100)) + "%";
        String memoryUsedRateStr = memoryUsedRate.multiply(BigDecimal.valueOf(100)) + "%";
        String storageUsedRateStr = storageUsedRate.multiply(BigDecimal.valueOf(100)) + "%";
        if (isHost && isStorage) {
            //资源分配率
            Section fenPeiSection = new Section();
            fenPeiSection.setTitle("资源分配率");
            //从数据库中获取宿主机数据
            List<RowRenderData> fenPeiRow = new ArrayList<>();
            RowRenderData fenPeiRow0 = Rows.of("组件名称", "总量", "已分配", "占比").center().bgColor("0F9ED5").create();
            fenPeiRow.add(fenPeiRow0);

            RowRenderData row1 = Rows.of("CPU（核）",
                    String.valueOf(totalCpu),
                    String.valueOf(cpuCommitRate),
                    cpuRatioStr).center().bgColor("CAEDFB").create();
            fenPeiRow.add(row1);
            RowRenderData row2 = Rows.of("内存（GB）",
                    String.valueOf(totalVirtualMemoryGB),
                    String.valueOf(memoryCommitRateGB),
                    memoryRatioStr).center().bgColor("FFFFFF").create();
            fenPeiRow.add(row2);
            RowRenderData row3 = Rows.of("云存储（GB）",
                    String.valueOf(totalCapacityGB),
                    String.valueOf(usedCapacityGB),
                    storageRatioStr).center().bgColor("CAEDFB").create();
            fenPeiRow.add(row3);

            TableRenderData fenPei_table = Tables.of(ArrayUtil.toArray(fenPeiRow, RowRenderData.class))
                    .border(BorderStyle.builder().withColor("0F9ED5").withSize(1).withSpace(1).withType(XWPFTable.XWPFBorderType.SINGLE).build())
                    .create();
            fenPeiSection.setTable(fenPei_table);
            sections.add(fenPeiSection);
            //paramMap.put("resourceAllocation", fenPei_table);

            //资产使用率
            Section resourceUseSection = new Section();
            resourceUseSection.setTitle("资产使用率");
            //资产使用率
            List<RowRenderData> resourceUseRows = new ArrayList<>();
            RowRenderData resourceUseRow0 = Rows.of("组件名称", "占比").center().bgColor("0F9ED5").create();
            resourceUseRows.add(resourceUseRow0);
            RowRenderData useRow1 = Rows.of("CPU",
                    String.valueOf(cpuUsedRateStr)).center().bgColor("CAEDFB").create();
            resourceUseRows.add(useRow1);
            RowRenderData useRow2 = Rows.of("内存",
                    String.valueOf(memoryUsedRateStr)).center().bgColor("CAEDFB").create();
            resourceUseRows.add(useRow2);
            RowRenderData useRow3 = Rows.of("云存储",
                    String.valueOf(storageUsedRateStr)).center().bgColor("CAEDFB").create();
            resourceUseRows.add(useRow3);

            TableRenderData resourceUse_table = Tables.of(ArrayUtil.toArray(resourceUseRows, RowRenderData.class))
                    .border(BorderStyle.builder().withColor("0F9ED5").withSize(1).withSpace(1).withType(XWPFTable.XWPFBorderType.SINGLE).build())
                    .create();
            resourceUseSection.setTable(resourceUse_table);
            sections.add(resourceUseSection);
            //paramMap.put("resourceUse", resourceUse_table);
        } else if (isHost) {
            //资源分配率
            Section fenPeiSection = new Section();
            fenPeiSection.setTitle("资源分配率");
            //从数据库中获取宿主机数据
            List<RowRenderData> fenPeiRow = new ArrayList<>();
            RowRenderData fenPeiRow0 = Rows.of("组件名称", "总量", "已分配", "占比").center().bgColor("0F9ED5").create();
            fenPeiRow.add(fenPeiRow0);

            RowRenderData row1 = Rows.of("CPU（核）",
                    String.valueOf(totalCpu),
                    String.valueOf(cpuCommitRate),
                    cpuRatioStr).center().bgColor("CAEDFB").create();
            fenPeiRow.add(row1);
            RowRenderData row2 = Rows.of("内存（GB）",
                    String.valueOf(totalVirtualMemoryGB),
                    String.valueOf(memoryCommitRateGB),
                    memoryRatioStr).center().bgColor("FFFFFF").create();
            fenPeiRow.add(row2);

            TableRenderData fenPei_table = Tables.of(ArrayUtil.toArray(fenPeiRow, RowRenderData.class))
                    .border(BorderStyle.builder().withColor("0F9ED5").withSize(1).withSpace(1).withType(XWPFTable.XWPFBorderType.SINGLE).build())
                    .create();
            fenPeiSection.setTable(fenPei_table);
            sections.add(fenPeiSection);
            //paramMap.put("resourceAllocation", fenPei_table);


            //资产使用率
            Section resourceUseSection = new Section();
            resourceUseSection.setTitle("资产使用率");
            List<RowRenderData> resourceUseRows = new ArrayList<>();
            RowRenderData resourceUseRow0 = Rows.of("组件名称", "占比").center().bgColor("0F9ED5").create();
            resourceUseRows.add(resourceUseRow0);

            RowRenderData useRow1 = Rows.of("CPU",
                    String.valueOf(cpuUsedRateStr)).center().bgColor("CAEDFB").create();
            resourceUseRows.add(useRow1);
            RowRenderData useRow2 = Rows.of("内存",
                    String.valueOf(memoryUsedRateStr)).center().bgColor("CAEDFB").create();
            resourceUseRows.add(useRow2);

            TableRenderData resourceUse_table = Tables.of(ArrayUtil.toArray(resourceUseRows, RowRenderData.class))
                    .border(BorderStyle.builder().withColor("0F9ED5").withSize(1).withSpace(1).withType(XWPFTable.XWPFBorderType.SINGLE).build())
                    .create();
            resourceUseSection.setTable(resourceUse_table);
            sections.add(resourceUseSection);
            // paramMap.put("resourceUse", resourceUse_table);
        } else if (isStorage) {
            //资源分配率
            Section fenPeiSection = new Section();
            fenPeiSection.setTitle("资源分配率");
            //从数据库中获取宿主机数据
            List<RowRenderData> fenPeiRow = new ArrayList<>();
            RowRenderData fenPeiRow0 = Rows.of("组件名称", "总量", "已分配", "占比").center().bgColor("0F9ED5").create();
            fenPeiRow.add(fenPeiRow0);

            RowRenderData row3 = Rows.of("云存储（GB）",
                    String.valueOf(totalCapacityGB),
                    String.valueOf(usedCapacityGB),
                    storageRatioStr).center().bgColor("CAEDFB").create();
            fenPeiRow.add(row3);

            TableRenderData fenPei_table = Tables.of(ArrayUtil.toArray(fenPeiRow, RowRenderData.class))
                    .border(BorderStyle.builder().withColor("0F9ED5").withSize(1).withSpace(1).withType(XWPFTable.XWPFBorderType.SINGLE).build())
                    .create();
            fenPeiSection.setTable(fenPei_table);
            sections.add(fenPeiSection);
            //paramMap.put("resourceAllocation", fenPei_table);

            //资产使用率
            Section resourceUseSection = new Section();
            resourceUseSection.setTitle("资产使用率");
            List<RowRenderData> resourceUseRows = new ArrayList<>();
            RowRenderData resourceUseRow0 = Rows.of("组件名称", "占比").center().bgColor("0F9ED5").create();
            resourceUseRows.add(resourceUseRow0);

            RowRenderData useRow3 = Rows.of("云存储",
                    String.valueOf(storageUsedRateStr)).center().bgColor("CAEDFB").create();
            resourceUseRows.add(useRow3);

            TableRenderData resourceUse_table = Tables.of(ArrayUtil.toArray(resourceUseRows, RowRenderData.class))
                    .border(BorderStyle.builder().withColor("0F9ED5").withSize(1).withSpace(1).withType(XWPFTable.XWPFBorderType.SINGLE).build())
                    .create();
            resourceUseSection.setTable(resourceUse_table);
            sections.add(resourceUseSection);
            //paramMap.put("resourceUse", resourceUse_table);
        }

        //存储使用情况
        if (ObjectUtil.isNotEmpty(storageList)) {
            Section storageSection = new Section();
            storageSection.setTitle("存储使用情况");
            List<RowRenderData> storageRows = new ArrayList<>();
            RowRenderData storageRow0 = Rows.of("存储名", "启用状态", "就绪状态", "物理使用率", "置备使用率", "总容量").center().bgColor("0F9ED5").create();
            storageRows.add(storageRow0);
            final int[] index = {0};
            storageList.forEach(storage -> {
                BigDecimal usedCapacity1 = storage.getUsedCapacity() != null ? BigDecimal.valueOf(storage.getUsedCapacity()) : BigDecimal.ZERO;
                BigDecimal totalCapacity1 = storage.getTotalPhysicalCapacity();
                BigDecimal virtualUtilization = totalCapacity1.compareTo(BigDecimal.ZERO) > 0 ?
                        usedCapacity1.multiply(BigDecimal.valueOf(100)).divide(totalCapacity1, 2, RoundingMode.HALF_UP) : new BigDecimal(0);
                // 根据索引判断是基数还是偶数,设置对应的背景色
                String bgColor = (index[0] % 2 == 0) ? "FFFFFF" : "CAEDFB";
                RowRenderData row = Rows.of(storage.getName(),
                        storage.getState().equals("Enabled") ? "启用" : "未启用",
                        storage.getStatus().equals("Connected") ? "已连接" : "未连接",
                        storage.getCapacityUtilization() + "%",
                        virtualUtilization + "%",
                        storage.getTotalCapacity() / 1024 / 1024 / 1024 + "GB"
                ).center().bgColor(bgColor).create();
                storageRows.add(row);
                index[0]++;
            });
            TableRenderData storageInfo = Tables.of(ArrayUtil.toArray(storageRows, RowRenderData.class))
                    .border(BorderStyle.builder().withColor("0F9ED5").withSize(1).withSpace(1).withType(XWPFTable.XWPFBorderType.SINGLE).build())
                    .create();
            storageSection.setTable(storageInfo);
            sections.add(storageSection);
            //paramMap.put("storageInfo", storageInfo);
        }


        //巡检项列表
        //根据巡检记录ID 查询巡检分类结果
        if (ObjectUtil.isNotEmpty(patrolResultCategoryList)) {
            Section itemSection = new Section();
            itemSection.setTitle("巡检项列表");
            List<RowRenderData> itemRows = new ArrayList<>();
            RowRenderData itemRows0 = Rows.of("巡检项", "检测值", "资源数", "正常项", "异常项").center().bgColor("0F9ED5").create();
            itemRows.add(itemRows0);
            final List<PatrolInspectionConfigDO> finalConfigs1 = configs;
            final int[] index = {0};
            final AtomicReference<String> checkValue = new AtomicReference<>("-");
            patrolResultCategoryList.forEach(result -> {
                String bgColor = (index[0] % 2 == 0) ? "FFFFFF" : "CAEDFB";
                PatrolInspectionConfigDO config = finalConfigs1.stream()
                        .filter(c -> c.getMetricName().equals(result.getMetricName())
                                && c.getThresholdType().equals("normal"))
                        .findFirst().orElse(null);
                if (config != null) {
                    String unit = config.getUnit() != null ? config.getUnit() : "";
                    unit = convertDurationUnitToChinese(unit);
                    if (config.getThresholdValue() != null) {
                        if (config.getDuration() != null && config.getDuration() > 0) {
                            if (config.getMetricName().equals(PatrolMetricEnum.CLOUD_MEMORY_RATIO.getCode())) {
                                checkValue.set(config.getDuration() + convertDurationUnitToChinese(config.getDurationUnit()) + "内，环比" + config.getThresholdValue() + unit);
                            } else {
                                checkValue.set(config.getThresholdValue() + unit +
                                        ",持续" + config.getDuration() + convertDurationUnitToChinese(config.getDurationUnit()));
                            }
                        } else {
                            checkValue.set(config.getThresholdValue() + unit);
                        }
                        if (config.getDiffRate() != null) {
                            checkValue.set(checkValue.get() + ",且差值率大于等于" + config.getDiffRate() + "%");
                        }
                    } else if (config.getDiffRate() != null) {
                        checkValue.set("差值率" + config.getDiffRate() + "%");
                    } else {
                        checkValue.set("-");
                    }
                } else {
                    checkValue.set("-");
                }
                RowRenderData row = Rows.of(PatrolMetricEnum.getByCode(result.getMetricName()).getTitle(),
                        checkValue.get(),
                        String.valueOf(result.getResourceCount()),
                        String.valueOf(result.getNormalCount()),
                        String.valueOf(result.getLowRiskCount() + result.getMediumRiskCount() + result.getHighRiskCount() + result.getAbnormalCount())
                ).center().bgColor(bgColor).create();
                index[0]++;
                itemRows.add(row);
            });
            TableRenderData itemInfo = Tables.of(ArrayUtil.toArray(itemRows, RowRenderData.class))
                    .border(BorderStyle.builder().withColor("0F9ED5").withSize(1).withSpace(1).withType(XWPFTable.XWPFBorderType.SINGLE).build())
                    .create();
            itemSection.setTable(itemInfo);
            sections.add(itemSection);
            //paramMap.put("itemInfo", itemInfo);
        }


        //根据recordId 查询巡检异常明细列表
        //根据resourceType 分组
        Map<String, List<PatrolAbnormalDetailDO>> detailMap = detail.stream()
                .collect(Collectors.groupingBy(PatrolAbnormalDetailDO::getResourceType));

        //云主机检测异常项
        if (ObjectUtil.isNotEmpty(detailMap)) {
            List<PatrolAbnormalDetailDO> cloudDetails = detailMap.get(PatrolResourceTypeEnum.CLOUD.getCode());
            if (ObjectUtil.isNotEmpty(cloudDetails)) {
                sections.add(createAbnormalSection("云主机检测异常项", cloudDetails));
                //paramMap.put("cloudAbnormalInfo", cloudAbnormalInfo);
            }
        }

        //宿主机检测异常项
        if (ObjectUtil.isNotEmpty(detailMap)) {
            List<PatrolAbnormalDetailDO> hostDetails = detailMap.get(PatrolResourceTypeEnum.HOST.getCode());
            if (ObjectUtil.isNotEmpty(hostDetails)) {
                sections.add(createAbnormalSection("宿主机检测异常项", hostDetails));
                //paramMap.put("hostAbnormalInfo", hostAbnormalInfo);
            }
        }

        //云存储检测异常项
        if (ObjectUtil.isNotEmpty(detailMap)) {
            List<PatrolAbnormalDetailDO> storageDetails = detailMap.get(PatrolResourceTypeEnum.STORAGE.getCode());
            if (ObjectUtil.isNotEmpty(storageDetails)) {
                sections.add(createAbnormalSection("云存储检测异常项", storageDetails));
                // paramMap.put("storageAbnormalInfo", storageAbnormalInfo);
            }
        }
        //云硬盘检测异常项
        if (ObjectUtil.isNotEmpty(detailMap)) {
            List<PatrolAbnormalDetailDO> volumeDetails = detailMap.get(PatrolResourceTypeEnum.DISK.getCode());
            if (ObjectUtil.isNotEmpty(volumeDetails)) {
                sections.add(createAbnormalSection("云硬盘检测异常项", volumeDetails));
                // paramMap.put("storageAbnormalInfo", storageAbnormalInfo);
            }
        }

        //监控异常项
        if (ObjectUtil.isNotEmpty(detailMap)) {
            List<PatrolResourceTypeEnum> resourceTypes = PatrolResourceTypeEnum.getByAssetType(PatrolAssetType.MONITOR);
            List<PatrolAbnormalDetailDO> monitorDetails = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(resourceTypes)) {
                resourceTypes.forEach(resourceType -> {
                    List<PatrolAbnormalDetailDO> resourceDetails = detailMap.get(resourceType.getCode());
                    if (ObjectUtil.isNotEmpty(resourceDetails)) {
                        monitorDetails.addAll(resourceDetails);
                    }
                });
            }
            if (ObjectUtil.isNotEmpty(monitorDetails)) {
                sections.add(createAbnormalSection("监控异常项", monitorDetails));
                //paramMap.put("monitorAbnormalInfo", monitorAbnormalInfo);
            }
        }
        paramMap.put("tables", sections);
        Map<String, String> fileMap = new HashMap<>();
        String fileName = recordName + ".docx";
        fileMap.put("fileName", fileName);
        fileMap.put("filePath", createAndDownloadWord(paramMap, fileName));
        return fileMap;
    }

    private TextRenderData setTitleStyle(String title) {
        Style style = new Style();
        style.setFontSize(14);                          // 四号字体
        style.setBold(true);                            // 加粗
        style.setFontFamily("Microsoft YaHei Light");   // 中文字体（等线 Light）

        return new TextRenderData(title, style);
    }

    private Section createAbnormalSection(String title, List<PatrolAbnormalDetailDO> details) {
        Section section = new Section();
        section.setTitle(title);
        List<RowRenderData> errorRows = new ArrayList<>();
        RowRenderData errorRows0 = Rows.of("巡检项", "资源名称", "平台名称", "检测结果", "详情", "建议").center().bgColor("0F9ED5").create();
        errorRows.add(errorRows0);
        final int[] index = {0};
        details.forEach(volumeDetail -> {
            String bgColor = (index[0] % 2 == 0) ? "FFFFFF" : "CAEDFB";
            String resourceName = volumeDetail.getResourceName();
            RowRenderData row = Rows.of(PatrolMetricEnum.getByCode(volumeDetail.getMetricName()).getTitle(),
                    resourceName,
                    volumeDetail.getPlatformName(),
                    PatrolRiskEnum.getByCode(volumeDetail.getRiskLevel()).getName(),
                    volumeDetail.getDetail(),
                    volumeDetail.getSuggest()
            ).center().bgColor(bgColor).create();
            errorRows.add(row);
            index[0]++;
        });
        TableRenderData abnormalInfo = Tables.of(ArrayUtil.toArray(errorRows, RowRenderData.class))
                .border(BorderStyle.builder().withColor("0F9ED5").withSize(1).withSpace(1).withType(XWPFTable.XWPFBorderType.SINGLE).build())
                .create();
        section.setTable(abnormalInfo);
        return section;
    }
}


