package cn.iocoder.zj.module.om.controller.admin.monitorasset.vo;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.compress.utils.Lists;

import java.util.List;



public class WxTempLateBtnVo {
    /**
     * card_type : button_interaction
     * source : {"icon_url":"图片的url","desc":"企业微信"}
     * main_title : {"title":"欢迎使用企业微信","desc":"您的好友正在邀请您加入企业微信"}
     * sub_title_text : 下载企业微信还能抢红包！
     * horizontal_content_list : [{"keyname":"邀请人","value":"张三"},{"type":1,"keyname":"企业微信官网","value":"点击访问","url":"https://work.weixin.qq.com"},{"type":2,"keyname":"企业微信下载","value":"企业微信.apk","media_id":"文件的media_id"}]
     * card_action : {"type":2,"url":"https://work.weixin.qq.com","appid":"小程序的appid","pagepath":"/index.html"}
     * task_id : task_id
     * button_list : [{"text":"按钮1","style":1,"key":"button_key_1"},{"text":"按钮2","style":2,"key":"button_key_2"}]
     */

    private String card_type;
    private SourceBean source;
    private MainTitleBean main_title;
    private String sub_title_text;
    private CardActionBean card_action;
    private  String appointId;
    private  String userId;
    private  Long assetId;
    private  Long authId;
    private  String status;
    private  String authuserId;

    public Long getAuthId() {
        return authId;
    }

    public void setAuthId(Long authId) {
        this.authId = authId;
    }

    public Long getAssetId() {
        return assetId;
    }

    public void setAssetId(Long assetId) {
        this.assetId = assetId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAppointId() {
        return appointId;
    }

    public void setAppointId(String appointId) {
        this.appointId = appointId;
    }

    public String getReplace_text() {
        return replace_text;
    }

    public void setReplace_text(String replace_text) {
        this.replace_text = replace_text;
    }
    public String getAuthuserId() {
        return authuserId;
    }

    public void setAuthuserId(String authuserId) {
        this.authuserId = authuserId;
    }

    private String task_id;
    private List<HorizontalContentListBean> horizontal_content_list;
    private List<ButtonListBean> button_list;
    private String replace_text;

    public static void main(String[] args) {
        WxTempLateBtnVo wxTempLateBtnVo = new WxTempLateBtnVo();
        wxTempLateBtnVo.setCard_type("");
        wxTempLateBtnVo.setSource(new SourceBean());
        wxTempLateBtnVo.setMain_title(new MainTitleBean());
        wxTempLateBtnVo.setSub_title_text("");
        wxTempLateBtnVo.setCard_action(new CardActionBean());
        wxTempLateBtnVo.setTask_id("");
        wxTempLateBtnVo.setHorizontal_content_list(Lists.newArrayList());
        wxTempLateBtnVo.setButton_list(Lists.newArrayList());

        String s = JSONObject.toJSONString(wxTempLateBtnVo);
        System.out.println(s);


    }

    public String getCard_type() {
        return card_type;
    }

    public void setCard_type(String card_type) {
        this.card_type = card_type;
    }

    public SourceBean getSource() {
        return source;
    }

    public void setSource(SourceBean source) {
        this.source = source;
    }

    public MainTitleBean getMain_title() {
        return main_title;
    }

    public void setMain_title(MainTitleBean main_title) {
        this.main_title = main_title;
    }

    public String getSub_title_text() {
        return sub_title_text;
    }

    public void setSub_title_text(String sub_title_text) {
        this.sub_title_text = sub_title_text;
    }

    public CardActionBean getCard_action() {
        return card_action;
    }

    public void setCard_action(CardActionBean card_action) {
        this.card_action = card_action;
    }

    public String getTask_id() {
        return task_id;
    }

    public void setTask_id(String task_id) {
        this.task_id = task_id;
    }

    public List<HorizontalContentListBean> getHorizontal_content_list() {
        return horizontal_content_list;
    }

    public void setHorizontal_content_list(List<HorizontalContentListBean> horizontal_content_list) {
        this.horizontal_content_list = horizontal_content_list;
    }

    public List<ButtonListBean> getButton_list() {
        return button_list;
    }

    public void setButton_list(List<ButtonListBean> button_list) {
        this.button_list = button_list;
    }

    public static class SourceBean {
        /**
         * icon_url : 图片的url
         * desc : 企业微信
         */

        private String icon_url;
        private String desc;

        public String getIcon_url() {
            return icon_url;
        }

        public void setIcon_url(String icon_url) {
            this.icon_url = icon_url;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }

    public static class MainTitleBean {
        /**
         * title : 欢迎使用企业微信
         * desc : 您的好友正在邀请您加入企业微信
         */

        private String title;
        private String desc;

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }

    public static class CardActionBean {
        /**
         * type : 2
         * url : https://work.weixin.qq.com
         * appid : 小程序的appid
         * pagepath : /index.html
         */

        private int type;
        private String url;
        private String appid;
        private String pagepath;

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getAppid() {
            return appid;
        }

        public void setAppid(String appid) {
            this.appid = appid;
        }

        public String getPagepath() {
            return pagepath;
        }

        public void setPagepath(String pagepath) {
            this.pagepath = pagepath;
        }
    }

    public static class HorizontalContentListBean {
        /**
         * keyname : 邀请人
         * value : 张三
         * type : 1
         * url : https://work.weixin.qq.com
         * media_id : 文件的media_id
         */

        private String keyname;
        private String value;
        private int type;
        private String url;
        private String media_id;

        public String getKeyname() {
            return keyname;
        }

        public void setKeyname(String keyname) {
            this.keyname = keyname;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getMedia_id() {
            return media_id;
        }

        public void setMedia_id(String media_id) {
            this.media_id = media_id;
        }
    }

    public static class ButtonListBean {
        /**
         * text : 按钮1
         * style : 1
         * key : button_key_1
         */

        private String text;
        private int style;
        private String key;

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public int getStyle() {
            return style;
        }

        public void setStyle(int style) {
            this.style = style;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }
    }
    //按钮模板消息

}
