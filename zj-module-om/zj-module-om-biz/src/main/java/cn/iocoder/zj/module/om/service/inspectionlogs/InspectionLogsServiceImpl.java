package cn.iocoder.zj.module.om.service.inspectionlogs;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.zj.module.om.controller.admin.inspectionlogs.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.inspectionlogs.InspectionLogsDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.om.convert.inspectionlogs.InspectionLogsConvert;
import cn.iocoder.zj.module.om.dal.mysql.inspectionlogs.InspectionLogsMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.om.enums.ErrorCodeConstants.*;

/**
 * 自动巡检异常记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InspectionLogsServiceImpl implements InspectionLogsService {

    @Resource
    private InspectionLogsMapper inspectionLogsMapper;

    @Override
    public Long createInspectionLogs(InspectionLogsCreateReqVO createReqVO) {
        // 插入
        InspectionLogsDO inspectionLogs = InspectionLogsConvert.INSTANCE.convert(createReqVO);
        inspectionLogsMapper.insert(inspectionLogs);
        // 返回
        return inspectionLogs.getId();
    }

    @Override
    public void updateInspectionLogs(InspectionLogsUpdateReqVO updateReqVO) {
        // 校验存在
        validateInspectionLogsExists(updateReqVO.getId());
        // 更新
        InspectionLogsDO updateObj = InspectionLogsConvert.INSTANCE.convert(updateReqVO);
        inspectionLogsMapper.updateById(updateObj);
    }

    @Override
    public void deleteInspectionLogs(Long id) {
        // 校验存在
        validateInspectionLogsExists(id);
        // 删除
        inspectionLogsMapper.deleteById(id);
    }

    private void validateInspectionLogsExists(Long id) {
        if (inspectionLogsMapper.selectById(id) == null) {
            throw exception(INSPECTION_LOGS_NOT_EXISTS);
        }
    }

    @Override
    public InspectionLogsDO getInspectionLogs(Long id) {
        return inspectionLogsMapper.selectById(id);
    }

    @Override
    public List<InspectionLogsDO> getInspectionLogsList(Collection<Long> ids) {
        return inspectionLogsMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<InspectionLogsDO> getInspectionLogsPage(InspectionLogsPageReqVO pageReqVO) {
        return inspectionLogsMapper.selectPage(pageReqVO);
    }

    @Override
    public List<InspectionLogsDO> getInspectionLogsList(InspectionLogsExportReqVO exportReqVO) {
        return inspectionLogsMapper.selectList(exportReqVO);
    }

    @Override
    public void batchInspectionLogs(List<InspectionLogsDO> inspectionLogsDOS) {
        inspectionLogsMapper.insertBatch(inspectionLogsDOS);
    }

}
