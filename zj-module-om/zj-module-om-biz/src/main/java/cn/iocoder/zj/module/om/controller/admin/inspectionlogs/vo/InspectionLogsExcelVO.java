package cn.iocoder.zj.module.om.controller.admin.inspectionlogs.vo;

import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 自动巡检异常记录 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class InspectionLogsExcelVO {

    @ExcelProperty("主键id")
    private Long id;

    @ExcelProperty("巡检记录id")
    private String recordUuid;

    @ExcelProperty("巡检名称")
    private String inspectionName;

    @ExcelProperty("阈值")
    private BigDecimal threshold;

    @ExcelProperty("资产名称")
    private String assetName;

    @ExcelProperty("资产id")
    private String assetUuid;

    @ExcelProperty("平台id")
    private String platformId;

    @ExcelProperty("平台名称")
    private String platformName;

    @ExcelProperty("资产的值")
    private String value;

    @ExcelProperty("创建人名称")
    private String creatorName;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private LocalDateTime createTime;

    @ExcelProperty("检测结果")
    private String result;

}
