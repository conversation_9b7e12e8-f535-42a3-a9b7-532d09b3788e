package cn.iocoder.zj.module.om.service.monitorauthorizationuser;

import java.util.*;
import javax.validation.*;
import cn.iocoder.zj.module.om.controller.admin.monitorauthorizationuser.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.monitorauthorizationuser.MonitorAuthorizationUserDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 用户资产授权申请 Service 接口
 *
 * <AUTHOR>
 */
public interface MonitorAuthorizationUserService {

    /**
     * 创建用户资产授权申请
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMonitorAuthorizationUser(@Valid MonitorAuthorizationUserCreateReqVO createReqVO);

    /**
     * 更新用户资产授权申请
     *
     * @param updateReqVO 更新信息
     */
    void updateMonitorAuthorizationUser(@Valid MonitorAuthorizationUserUpdateReqVO updateReqVO);

    /**
     * 删除用户资产授权申请
     *
     * @param id 编号
     */
    void deleteMonitorAuthorizationUser(Long id);

    /**
     * 获得用户资产授权申请
     *
     * @param id 编号
     * @return 用户资产授权申请
     */
    MonitorAuthorizationUserDO getMonitorAuthorizationUser(Long id);

    /**
     * 获得用户资产授权申请列表
     *
     * @param ids 编号
     * @return 用户资产授权申请列表
     */
    List<MonitorAuthorizationUserDO> getMonitorAuthorizationUserList(Collection<Long> ids);

    /**
     * 获得用户资产授权申请分页
     *
     * @param pageReqVO 分页查询
     * @return 用户资产授权申请分页
     */
    PageResult<MonitorAuthorizationUserDO> getMonitorAuthorizationUserPage(MonitorAuthorizationUserPageReqVO pageReqVO);

    /**
     * 获得用户资产授权申请列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 用户资产授权申请列表
     */
    List<MonitorAuthorizationUserDO> getMonitorAuthorizationUserList(MonitorAuthorizationUserExportReqVO exportReqVO);

}
