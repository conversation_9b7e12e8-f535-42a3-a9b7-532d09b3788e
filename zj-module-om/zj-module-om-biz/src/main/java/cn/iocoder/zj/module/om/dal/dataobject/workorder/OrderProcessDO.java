package cn.iocoder.zj.module.om.dal.dataobject.workorder;

import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@TableName("om_order_process")
@KeySequence("om_order_process_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderProcessDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * id
     */
    private Long workOrderId;
    /**
     * 处理结果；consent同意，reject驳回
     */
    private String processResult;
    /**
     * 退回原因
     */
    private String context;
}
