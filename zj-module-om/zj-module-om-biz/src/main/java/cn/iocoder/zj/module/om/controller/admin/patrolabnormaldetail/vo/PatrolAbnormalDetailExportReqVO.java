package cn.iocoder.zj.module.om.controller.admin.patrolabnormaldetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 巡检异常明细 Excel 导出 Request VO，参数和 PatrolAbnormalDetailPageReqVO 是一致的")
@Data
public class PatrolAbnormalDetailExportReqVO {

    @Schema(description = "巡检记录ID")
    private Long recordId;

    @Schema(description = "巡检结果分类ID")
    private Long categoryId;

    @Schema(description = "资源类型(存储资源/宿主机资源/云主机资源/云硬盘资源等)")
    private String resourceType;

    @Schema(description = "指标名称(CPU分配/内存分配/网卡入速度等)")
    private String metricName;

    @Schema(description = "资源ID")
    private String resourceId;

    @Schema(description = "资源名称")
    private String resourceName;

    @Schema(description = "平台ID")
    private String platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "详情")
    private String detail;

    @Schema(description = "风险等级(正常/低风险/中风险/高风险)")
    private String riskLevel;

    @Schema(description = "建议")
    private String suggest;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
