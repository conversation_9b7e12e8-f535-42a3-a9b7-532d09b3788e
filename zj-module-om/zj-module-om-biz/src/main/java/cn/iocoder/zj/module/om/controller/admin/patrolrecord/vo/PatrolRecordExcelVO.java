package cn.iocoder.zj.module.om.controller.admin.patrolrecord.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 巡检记录 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class PatrolRecordExcelVO {

    @ExcelProperty("主键ID")
    private Long id;

    @ExcelProperty("报告名称")
    private String recordName;

    @ExcelProperty("巡检开始时间")
    private LocalDateTime startTime;

    @ExcelProperty("巡检结束时间")
    private LocalDateTime endTime;

    @ExcelProperty("巡检项总数")
    private Integer totalItemCount;

    @ExcelProperty("资源总数")
    private Integer totalResourceCount;

    @ExcelProperty("异常情况数")
    private Integer abnormalCount;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
