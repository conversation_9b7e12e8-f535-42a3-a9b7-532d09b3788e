package cn.iocoder.zj.module.om.controller.admin.monitorauthorizationuser;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.om.controller.admin.monitorauthorizationuser.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.monitorauthorizationuser.MonitorAuthorizationUserDO;
import cn.iocoder.zj.module.om.convert.monitorauthorizationuser.MonitorAuthorizationUserConvert;
import cn.iocoder.zj.module.om.service.monitorauthorizationuser.MonitorAuthorizationUserService;

@Tag(name = "管理后台 - 用户资产授权申请")
@RestController
@RequestMapping("/om/monitor-authorization-user")
@Validated
public class MonitorAuthorizationUserController {

    @Resource
    private MonitorAuthorizationUserService monitorAuthorizationUserService;

    @PostMapping("/create")
    @Operation(summary = "创建用户资产授权申请")
    @PreAuthorize("@ss.hasPermission('om:monitor-authorization-user:create')")
    public CommonResult<Long> createMonitorAuthorizationUser(@Valid @RequestBody MonitorAuthorizationUserCreateReqVO createReqVO) {
        return success(monitorAuthorizationUserService.createMonitorAuthorizationUser(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新用户资产授权申请")
    @PreAuthorize("@ss.hasPermission('om:monitor-authorization-user:update')")
    public CommonResult<Boolean> updateMonitorAuthorizationUser(@Valid @RequestBody MonitorAuthorizationUserUpdateReqVO updateReqVO) {
        monitorAuthorizationUserService.updateMonitorAuthorizationUser(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除用户资产授权申请")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('om:monitor-authorization-user:delete')")
    public CommonResult<Boolean> deleteMonitorAuthorizationUser(@RequestParam("id") Long id) {
        monitorAuthorizationUserService.deleteMonitorAuthorizationUser(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得用户资产授权申请")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('om:monitor-authorization-user:query')")
    public CommonResult<MonitorAuthorizationUserRespVO> getMonitorAuthorizationUser(@RequestParam("id") Long id) {
        MonitorAuthorizationUserDO monitorAuthorizationUser = monitorAuthorizationUserService.getMonitorAuthorizationUser(id);
        return success(MonitorAuthorizationUserConvert.INSTANCE.convert(monitorAuthorizationUser));
    }

    @GetMapping("/list")
    @Operation(summary = "获得用户资产授权申请列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('om:monitor-authorization-user:query')")
    public CommonResult<List<MonitorAuthorizationUserRespVO>> getMonitorAuthorizationUserList(@RequestParam("ids") Collection<Long> ids) {
        List<MonitorAuthorizationUserDO> list = monitorAuthorizationUserService.getMonitorAuthorizationUserList(ids);
        return success(MonitorAuthorizationUserConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得用户资产授权申请分页")
    @PreAuthorize("@ss.hasPermission('om:monitor-authorization-user:query')")
    public CommonResult<PageResult<MonitorAuthorizationUserRespVO>> getMonitorAuthorizationUserPage(@Valid MonitorAuthorizationUserPageReqVO pageVO) {
        PageResult<MonitorAuthorizationUserDO> pageResult = monitorAuthorizationUserService.getMonitorAuthorizationUserPage(pageVO);
        return success(MonitorAuthorizationUserConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出用户资产授权申请 Excel")
    @PreAuthorize("@ss.hasPermission('om:monitor-authorization-user:export')")
    @OperateLog(type = EXPORT)
    public void exportMonitorAuthorizationUserExcel(@Valid MonitorAuthorizationUserExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<MonitorAuthorizationUserDO> list = monitorAuthorizationUserService.getMonitorAuthorizationUserList(exportReqVO);
        // 导出 Excel
        List<MonitorAuthorizationUserExcelVO> datas = MonitorAuthorizationUserConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "用户资产授权申请.xls", "数据", MonitorAuthorizationUserExcelVO.class, datas);
    }

}
