package cn.iocoder.zj.module.om.convert.workorder;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.om.api.dbfile.dto.DbfileCreateReqDTO;
import cn.iocoder.zj.module.om.api.dbfile.dto.WorkOrderCreateReqDTO;
import cn.iocoder.zj.module.om.controller.admin.dbfile.vo.DbFileCreateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.om.controller.admin.workorder.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.workorder.WorkOrderDO;

/**
 * 工单管理数据 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface WorkOrderConvert {

    WorkOrderConvert INSTANCE = Mappers.getMapper(WorkOrderConvert.class);


    WorkOrderDO convert(WorkOrderCreateReqVO createReqVO);

    WorkOrderDO convert(WorkOrderUpdateMyReqVO updateReqVO);

    List<WorkOrderExcelVO> convertList02(List<WorkOrderDO> list);

    PageResult<WorkOrderRespVO> convertPage(PageResult<WorkOrderDO> pageResult);

    List<WorkOrderRespVO> convertList(List<WorkOrderDO> list);

    WorkOrderRespVO convert(WorkOrderDO workOrder);

    WorkOrderCreateReqVO convertReq(WorkOrderCreateReqDTO reqDTO);
}
