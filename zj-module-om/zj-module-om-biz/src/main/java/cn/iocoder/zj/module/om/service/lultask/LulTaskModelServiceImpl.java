package cn.iocoder.zj.module.om.service.lultask;

import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.om.Inspection.job.model.LulTaskModel;
import cn.iocoder.zj.module.om.Inspection.job.model.TaskServiceInfo;
import cn.iocoder.zj.module.om.Inspection.listener.RedisPublisher;
import cn.iocoder.zj.module.om.dal.mysql.lultask.LulTaskMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class LulTaskModelServiceImpl implements LulTaskModelService {

    @Resource
    private LulTaskMapper lulTaskMapper;

    @Resource
    private TaskServiceInfo taskServiceInfo;

    @Resource
    private RedisPublisher redisPublisher;

    @Override
    @TenantIgnore
    public List<LulTaskModel> findAll() {
        return lulTaskMapper.selectList(new LambdaQueryWrapper<LulTaskModel>().eq(LulTaskModel::getStatus, 1));
    }

    @Override
    @TenantIgnore
    public void addTask(LulTaskModel taskCacheModel) {
        lulTaskMapper.insert(taskCacheModel);
    }

    @Override
    @TenantIgnore
    public void updateTasByJobId(LulTaskModel taskCacheModel) {
        if (lulTaskMapper.updateTasByJobId(taskCacheModel) > 0) {
            taskServiceInfo.updateTask(taskCacheModel);
        }
    }

    @Override
    @TenantIgnore
    public void removeTask(LulTaskModel taskCacheModel) {
        redisPublisher.publish("lul_task", String.valueOf(taskCacheModel.getId()));
        lulTaskMapper.deleteById(taskCacheModel.getId());
        taskServiceInfo.removeTask(taskCacheModel.getId());
    }

    @Override
    @TenantIgnore
    public LulTaskModel getTaskByJobId(Long jobId) {
        return lulTaskMapper.selectById(jobId);
    }
}
