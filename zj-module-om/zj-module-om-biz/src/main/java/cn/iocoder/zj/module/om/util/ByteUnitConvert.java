package cn.iocoder.zj.module.om.util;

import java.text.DecimalFormat;

public class ByteUnitConvert {
    /**
     *  将字节(Byte单位数值)转化为带合适单位的字符串
     */
    public String unitConvert(Long size) {
        if (size <= 0) return "0";
        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        return new DecimalFormat("#,##0.#").format(size / Math.pow(1024, digitGroups)) + units[digitGroups];
    }

    /**
     * 将不同字节单位的值转化为B为单位的值
     */
    public Long unitConvert(Double size,String oldUnit) {
        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        Long convertVal = 0L;
        for (int i = 0; i < units.length; i++) {
            if (units[i].equals(oldUnit)){
                convertVal = new Double(Math.pow(2,10*i)*size).longValue();
            }
        }
        return convertVal;
    }
    public String unitConvert(String val) {
        String unit = val.replaceAll("[^a-zA-Z]", "");
        Double num = Double.parseDouble(val.replaceAll(unit,""));
        return unitConvert(unitConvert(num,unit));
    }
}
