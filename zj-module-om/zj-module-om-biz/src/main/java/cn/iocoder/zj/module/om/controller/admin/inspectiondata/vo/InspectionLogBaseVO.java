package cn.iocoder.zj.module.om.controller.admin.inspectiondata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;

/**
* 巡检记录 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class InspectionLogBaseVO {

    @Schema(description = "巡检ID")
    private Long inspectionId;

    @Schema(description = "巡检名称")
    private String inspectionName;

    @Schema(description = "阈值")
    private BigDecimal threshold;

    @Schema(description = "得分")
    private BigDecimal score;

    @Schema(description = "检测结果")
    private String result;

    @Schema(description = "异常设备uuid，多个逗号拼接")
    private String assetUuid;

    @Schema(description = "异常设备数量")
    private Integer assetNum;

    @Schema(description = "平台id")
    private String platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "巡检uuid")
    private String recordUuid;

    @Schema(description = "pcu使用率")
    private Double cpu;
    @Schema(description = "内存使用率")
    private Double mem;
    @Schema(description = "磁盘使用率")
    private Double disk;
    @Schema(description = "异常值类型：cpu,men,disk")
    private String abnormalItem;
    @Schema(description = "资产类型：host云主机、hardware宿主机、storage主存储")
    private String assetType;

}
