package cn.iocoder.zj.module.om.dal.dataobject.schedulinginfo;

import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 运维排班信息 DO
 *
 * <AUTHOR>
 */
@TableName("om_scheduling_info")
@KeySequence("om_scheduling_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SchedulingInfoDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 实施人id
     */
    private String enforcerId;
    /**
     * 实施人名称
     */
    private String enforcerName;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 租户id
     */
    private Long tenantId;
}
