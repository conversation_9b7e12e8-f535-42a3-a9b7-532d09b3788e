package cn.iocoder.zj.module.om.controller.admin.monitorasset.vo;
import lombok.Data;
import lombok.ToString;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

/**
 * 描述回调基类
 **/
@Data
@ToString
@XmlAccessorType(XmlAccessType.FIELD)
public abstract class BaseEventData {

    @XmlElement(name = "ToUserName")
    private String toUserName;

    @XmlElement(name = "FromUserName")
    private String fromUserName;

    @XmlElement(name = "CreateTime")
    private String createTime;

    @XmlElement(name = "MsgType")
    private String msgType;

    @XmlElement(name = "Event")
    private String event;

    @XmlElement(name = "EventKey")
    private String eventKey;

    @XmlElement(name = "ChangeType")
    private String changeType;

    @XmlElement(name = "MsgId")
    private Long msgId;

    @XmlElement(name = "AgentID")
    private Integer agentId;

    @XmlElement(name = "ResponseCode")
    private String ResponseCode;

    @XmlElement(name = "TaskId")
    private String TaskId;
}
