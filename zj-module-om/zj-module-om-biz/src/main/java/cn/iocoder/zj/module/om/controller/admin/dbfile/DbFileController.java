package cn.iocoder.zj.module.om.controller.admin.dbfile;

import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.om.controller.admin.dbfile.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.dbfile.DbFileDO;
import cn.iocoder.zj.module.om.convert.dbfile.DbFileConvert;
import cn.iocoder.zj.module.om.service.dbfile.DbFileService;

@Tag(name = "管理后台 - 配置备份")
@RestController
@RequestMapping("/om/db-file")
@Validated
public class DbFileController {

    @Resource
    private DbFileService dbFileService;

    @TenantIgnore
    @PostMapping("/create")
    @Operation(summary = "创建配置备份")
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('om:db-file:create')")
    public CommonResult<Long> createDbFile(@Valid @RequestBody DbFileCreateReqVO createReqVO) {
        return success(dbFileService.createDbFile(createReqVO));
    }

    @TenantIgnore
    @PutMapping("/update")
    @Operation(summary = "更新配置备份")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('om:db-file:update')")
    public CommonResult<Boolean> updateDbFile(@Valid @RequestBody DbFileUpdateReqVO updateReqVO) {
        dbFileService.updateDbFile(updateReqVO);
        return success(true);
    }

    @TenantIgnore
    @DeleteMapping("/delete")
    @Operation(summary = "删除配置备份")
    @OperateLog(type = DELETE)
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('om:db-file:delete')")
    public CommonResult<Boolean> deleteDbFile(@RequestParam("id") Long id) {
        dbFileService.deleteDbFile(id);
        return success(true);
    }

    @TenantIgnore
    @GetMapping("/get")
    @Operation(summary = "获得配置备份")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<DbFileRespVO> getDbFile(@RequestParam("id") Long id) {
        DbFileDO dbFile = dbFileService.getDbFile(id);
        return success(DbFileConvert.INSTANCE.convert(dbFile));
    }

    @TenantIgnore
    @GetMapping("/list")
    @Operation(summary = "获得配置备份列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    public CommonResult<List<DbFileRespVO>> getDbFileList(@RequestParam("ids") Collection<Long> ids) {
        List<DbFileDO> list = dbFileService.getDbFileList(ids);
        return success(DbFileConvert.INSTANCE.convertList(list));
    }

    @TenantIgnore
    @GetMapping("/page")
    @Operation(summary = "获得配置备份分页")
    public CommonResult<PageResult<DbFileRespVO>> getDbFilePage(@Valid DbFilePageReqVO pageVO) {
        PageResult<DbFileDO> pageResult = dbFileService.getDbFilePage(pageVO);
        return success(DbFileConvert.INSTANCE.convertPage(pageResult));
    }

//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出配置备份 Excel")
//    @PreAuthorize("@ss.hasPermission('om:db-file:export')")
//    @OperateLog(type = EXPORT)
//    public void exportDbFileExcel(@Valid DbFileExportReqVO exportReqVO,
//              HttpServletResponse response) throws IOException {
//        List<DbFileDO> list = dbFileService.getDbFileList(exportReqVO);
//        // 导出 Excel
//        List<DbFileExcelVO> datas = DbFileConvert.INSTANCE.convertList02(list);
//        ExcelUtils.write(response, "配置备份.xls", "数据", DbFileExcelVO.class, datas);
//    }


    @GetMapping("/dbRestore")
    @Operation(summary = "恢复数据")
    @PreAuthorize("@ss.hasPermission('om:db-file:restore')")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> dbRestore(@RequestParam String url,
                          @RequestParam String name) throws IOException {

        return success( dbFileService.dbRestore(url,name));
    }



    @PutMapping("/updateByName")
    @Operation(summary = "更新数据状态")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateByName(@RequestParam String name) {
        dbFileService.updateByName(name);
        return success(true);
    }



    @PutMapping("/updateState")
    @Operation(summary = "还原数据状态")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateState() {
        dbFileService.updateState();
        return success(true);
    }


    @PutMapping("/selectByState")
    @Operation(summary = "查看是否有正在执行的文件")
    @OperateLog(type = GET)
    public CommonResult<Long> selectByState() {
        return success(dbFileService.selectByState());
    }
}
