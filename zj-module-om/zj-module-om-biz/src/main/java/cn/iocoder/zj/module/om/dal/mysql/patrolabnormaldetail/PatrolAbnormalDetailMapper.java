package cn.iocoder.zj.module.om.dal.mysql.patrolabnormaldetail;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.module.om.controller.admin.patrolabnormaldetail.vo.PatrolAbnormalDetailExportReqVO;
import cn.iocoder.zj.module.om.controller.admin.patrolabnormaldetail.vo.PatrolAbnormalDetailPageReqVO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolabnormaldetail.PatrolAbnormalDetailDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 巡检异常明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PatrolAbnormalDetailMapper extends BaseMapperX<PatrolAbnormalDetailDO> {

    default PageResult<PatrolAbnormalDetailDO> selectPage(PatrolAbnormalDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PatrolAbnormalDetailDO>()
                .eqIfPresent(PatrolAbnormalDetailDO::getRecordId, reqVO.getRecordId())
                .eqIfPresent(PatrolAbnormalDetailDO::getResourceType, reqVO.getResourceType())
                .eqIfPresent(PatrolAbnormalDetailDO::getCategoryId, reqVO.getCategoryId())
                .likeIfPresent(PatrolAbnormalDetailDO::getMetricName, reqVO.getMetricName())
                .eqIfPresent(PatrolAbnormalDetailDO::getResourceId, reqVO.getResourceId())
                .likeIfPresent(PatrolAbnormalDetailDO::getResourceName, reqVO.getResourceName())
                .eqIfPresent(PatrolAbnormalDetailDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(PatrolAbnormalDetailDO::getPlatformName, reqVO.getPlatformName())
                .eqIfPresent(PatrolAbnormalDetailDO::getDetail, reqVO.getDetail())
                .eqIfPresent(PatrolAbnormalDetailDO::getRiskLevel, reqVO.getRiskLevel())
                .eqIfPresent(PatrolAbnormalDetailDO::getSuggest, reqVO.getSuggest())
                .betweenIfPresent(PatrolAbnormalDetailDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PatrolAbnormalDetailDO::getId));
    }

    default List<PatrolAbnormalDetailDO> selectList(PatrolAbnormalDetailExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PatrolAbnormalDetailDO>()
                .eqIfPresent(PatrolAbnormalDetailDO::getRecordId, reqVO.getRecordId())
                .eqIfPresent(PatrolAbnormalDetailDO::getCategoryId, reqVO.getCategoryId())
                .eqIfPresent(PatrolAbnormalDetailDO::getResourceType, reqVO.getResourceType())
                .likeIfPresent(PatrolAbnormalDetailDO::getMetricName, reqVO.getMetricName())
                .eqIfPresent(PatrolAbnormalDetailDO::getResourceId, reqVO.getResourceId())
                .likeIfPresent(PatrolAbnormalDetailDO::getResourceName, reqVO.getResourceName())
                .eqIfPresent(PatrolAbnormalDetailDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(PatrolAbnormalDetailDO::getPlatformName, reqVO.getPlatformName())
                .eqIfPresent(PatrolAbnormalDetailDO::getDetail, reqVO.getDetail())
                .eqIfPresent(PatrolAbnormalDetailDO::getRiskLevel, reqVO.getRiskLevel())
                .eqIfPresent(PatrolAbnormalDetailDO::getSuggest, reqVO.getSuggest())
                .betweenIfPresent(PatrolAbnormalDetailDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PatrolAbnormalDetailDO::getId));
    }

}
