package cn.iocoder.zj.module.om.service.patrolinspection;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.exception.ServiceException;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.datapermission.core.util.DataPermissionUtils;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.monitor.api.hardware.HardWareInfoApi;
import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.hostinfo.HostInfoApi;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.storage.StorageInfoApi;
import cn.iocoder.zj.module.monitor.api.storage.dto.StorageRespCreateReqDTO;
import cn.iocoder.zj.module.om.controller.admin.inspectiondata.vo.InspectionLogRespVO;
import cn.iocoder.zj.module.om.controller.admin.inspectiondata.vo.InspectionRecordCreateReqVO;
import cn.iocoder.zj.module.om.controller.admin.inspectiondata.vo.InspectionRecordRespVO;
import cn.iocoder.zj.module.om.controller.admin.patrolinspection.vo.*;
import cn.iocoder.zj.module.om.convert.patrolinspection.InspectionLogConvert;
import cn.iocoder.zj.module.om.convert.patrolinspection.PatrolInspectionConvert;
import cn.iocoder.zj.module.om.dal.dataobject.inspectionlogs.InspectionLogsDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspection.InspectionLogDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspection.PatrolInspectionDO;
import cn.iocoder.zj.module.om.dal.mysql.patrolinspection.PatrolInspectionMapper;
import cn.iocoder.zj.module.om.enums.AssetType;
import cn.iocoder.zj.module.om.service.inspectionlogs.InspectionLogsService;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.tenant.TenantApi;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.googlecode.aviator.AviatorEvaluator;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.om.enums.ErrorCodeConstants.*;

/**
 * 自动巡检规则 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PatrolInspectionServiceImpl implements PatrolInspectionService {

    @Resource
    private PatrolInspectionMapper patrolInspectionMapper;
    @Resource
    HostInfoApi hostInfoApi;
    @Resource
    HardWareInfoApi hardWareInfoApi;
    @Resource
    StorageInfoApi storageInfoApi;
    @Resource
    InspectionLogService inspectionLogService;

    @Resource
    InspectionLogsService inspectionLogsService;
    @Resource
    InspectionRecordService inspectionRecordService;

    @Resource
    TenantApi tenantApi;
    @Resource
    PermissionApi permissionApi;
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    RoleApi roleApi;
    @Resource
    AdminUserApi adminUserApi;

    @Override
    public void updatePatrolInspection(PatrolInspectionUpdateReqVO updateReqVO) {
        // 校验存在
        validatePatrolInspectionExists(updateReqVO.getId());
        // 更新
        PatrolInspectionDO updateObj = PatrolInspectionConvert.INSTANCE.convert(updateReqVO);
        patrolInspectionMapper.updateById(updateObj);
    }

    @Override
    public void deletePatrolInspection(Long id) {
        // 校验存在
        validatePatrolInspectionExists(id);
        // 删除
        patrolInspectionMapper.deleteById(id);
    }

    private void validatePatrolInspectionExists(Long id) {
        if (patrolInspectionMapper.selectById(id) == null) {
            throw exception(PATROL_INSPECTION_NOT_EXISTS);
        }
    }

    @Override
    public PatrolInspectionDO getPatrolInspection(Long id) {
        return patrolInspectionMapper.selectById(id);
    }

    @Override
    public List<PatrolInspectionDO> getPatrolInspectionList(Collection<Long> ids) {
        return patrolInspectionMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<PatrolInspectionDO> getPatrolInspectionPage(PatrolInspectionPageReqVO pageReqVO) {
        return patrolInspectionMapper.selectPage(pageReqVO);
    }

    @Override
    public List<PatrolInspectionDO> getPatrolInspectionList(PatrolInspectionExportReqVO exportReqVO) {
        return patrolInspectionMapper.selectList(exportReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PatrolInspectionImportRespVO importPatrolInspectionList(List<PatrolInspectionImportExcelVO> list, Boolean updateSupport) {
        if (CollUtil.isEmpty(list)) {
            throw exception(PATROL_INSPECTION_IMPORT_LIST_IS_EMPTY);
        }
        PatrolInspectionImportRespVO respVO = PatrolInspectionImportRespVO.builder().createPatrolInspectionNames(new ArrayList<>())
                .updatePatrolInspectionNames(new ArrayList<>()).failurePatrolInspectionNames(new LinkedHashMap<>()).build();
        list.forEach(item -> {
            try {
                // 校验，判断是否有空值
                if (StringUtil.isEmpty(item.getName())) {
                    throw exception(PATROL_INSPECTION_NEME_IS_EMPTY);
                }
                if (StringUtil.isEmpty(item.getType())) {
                    throw exception(PATROL_INSPECTION_TYPE_IS_EMPTY);
                }
                if (StringUtil.isEmpty(item.getDataResource())) {
                    throw exception(PATROL_INSPECTION_DATARESOURCE_IS_EMPTY);
                }
                if (null == item.getValue() || String.valueOf(item.getValue()).equals("")) {
                    throw exception(PATROL_INSPECTION_VALUE_IS_EMPTY);
                }
                if (StringUtil.isEmpty(item.getAssetType())) {
                    throw exception(PATROL_INSPECTION_ASSETTYPE_IS_EMPTY);
                }
                if (StringUtil.isEmpty(item.getRule())) {
                    throw exception(PATROL_INSPECTION_RULE_IS_EMPTY);
                }
                if (StringUtil.isEmpty(item.getFormula())) {
                    throw exception(PATROL_INSPECTION_FORMULA_IS_EMPTY);
                }
                if (StringUtil.isEmpty(item.getParam())) {
                    throw exception(PATROL_INSPECTION_PARAM_IS_EMPTY);
                }
                switch (item.getAssetType()) {
                    case "云主机":
                        item.setAssetType("host");
                        break;
                    case "宿主机":
                        item.setAssetType("hardware");
                        break;
                    case "主存储":
                        item.setAssetType("storage");
                        break;
                    case "网络设备":
                        item.setAssetType("network");
                        break;
                }
                validateForCreateOrUpdate(item.getType(), item.getAssetType());
            } catch (ServiceException ex) {
                respVO.getFailurePatrolInspectionNames().put(item.getName(), ex.getMessage());
                return;
            }
            PatrolInspectionDO existDo = this.patrolInspectionMapper.selectOne(new LambdaQueryWrapperX<PatrolInspectionDO>()
                    .eq(PatrolInspectionDO::getType, item.getType())
                    .eq(PatrolInspectionDO::getAssetType, item.getAssetType())
                    .ne(PatrolInspectionDO::getDeleted, 1));
            if (existDo == null) {
                this.patrolInspectionMapper.insert(PatrolInspectionConvert.INSTANCE.convert(item));
                respVO.getCreatePatrolInspectionNames().add(item.getName());
            }
            if (!updateSupport) {
                try {
                    validateTypeUnique(item.getType());
                } catch (ServiceException ex) {
                    respVO.getFailurePatrolInspectionNames().put(item.getName(), ex.getMessage());
                    return;
                }
            }
            if (existDo != null) {
                PatrolInspectionDO patrolInspectionDO = PatrolInspectionConvert.INSTANCE.convertList03(item);
                patrolInspectionDO.setId(existDo.getId());
                patrolInspectionDO.setParam(convertParamToJsonStr(patrolInspectionDO.getParam()));
                this.patrolInspectionMapper.updateById(patrolInspectionDO);
                respVO.getUpdatePatrolInspectionNames().add(item.getName());
            }
        });
        return respVO;
    }

    private void validateForCreateOrUpdate(String type, String assetType) {
        DataPermissionUtils.executeIgnore(() -> {
            //校验存在性
            validateExists(type, assetType);
        });
    }

    private void validateExists(String type, String assetType) {
        if (StrUtil.isBlank(type) || StrUtil.isBlank(assetType)) {
            return;
        }
        List<PatrolInspectionDO> list = this.patrolInspectionMapper.selectList(new LambdaQueryWrapperX<PatrolInspectionDO>()
                .eq(PatrolInspectionDO::getType,type)
                .eq(PatrolInspectionDO::getAssetType, assetType)
                .eq(PatrolInspectionDO::getDeleted, 0));
        if (list.size() == 0) {
            throw exception(PATROL_INSPECTION_ILLEGAL);
        }
    }

    private void validateTypeUnique(String type) {
        if (StrUtil.isBlank(type)) {
            return;
        }
        List<PatrolInspectionDO> list = this.patrolInspectionMapper.selectList(new LambdaQueryWrapperX<PatrolInspectionDO>()
                .eq(PatrolInspectionDO::getType, type)
                .ne(PatrolInspectionDO::getDeleted, 1));
        if (list.size() > 0) {
            throw exception(TYPE_ALREADY_EXISTS);
        }
    }

    private String convertParamToJsonStr(String param) {
        List<String> paramStrList = Arrays.asList(param.split(";"));
        Map<String, String> paramMap = new HashMap<>();
        for (String str : paramStrList) {
            String key = str.substring(0, str.indexOf(":"));
            String val = str.substring(str.indexOf(":") + 1);
            paramMap.put(key, val);
        }
        return JSON.toJSONString(paramMap);
    }

    @Override
    public Map<String, Object> patrolInspectionStart(Long tenantId) {
        long startTime = System.currentTimeMillis();
        String recordUuid = UuidUtils.generateUuid();
        InspectionRecordCreateReqVO recordReqVO = new InspectionRecordCreateReqVO();
        recordReqVO.setUuid(recordUuid);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        List<Map> platformConfigDOList = platformconfigApi.getPlatformSelectList(String.valueOf(loginUser.getTenantId())).getData();
        if(CollectionUtil.isEmpty(platformConfigDOList)){
            throw exception(HAVE_NO_PLATFORM);
        }
        Collection<Long> platformId = platformConfigDOList.stream().map(item -> Long.parseLong(item.get("platformId").toString())).collect(Collectors.toList());
        //因为实时查询数据存在偏差，所以取5秒前的时间
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.MINUTE, -10);
        Long stamp = c.getTime().getTime() * 1000000;
        //获取平台下的云主机、宿主机、主存储uuid信息
        Map<String, List<String>> uuidList = hostInfoApi.getAllDeviceByPlatformId(platformId).getData();
        List<HostInfoRespCreateReqDTO> hostList = hostInfoApi.getListAll().getData();
        List<HardWareRespCreateReqDTO> hardWareList = hardWareInfoApi.getListAll().getData();
        List<StorageRespCreateReqDTO> storageList = storageInfoApi.getListAll().getData();

        recordReqVO.setHostNum(uuidList.get("host").size());
        recordReqVO.setHardwareNum(uuidList.get("hardware").size());
        recordReqVO.setStorageNum(uuidList.get("storage").size());
        //巡检项，没有巡检项则不巡检
        List<PatrolInspectionDO> inspectionDOList = this.patrolInspectionMapper.selectList(new LambdaQueryWrapperX<PatrolInspectionDO>()
                .eq(PatrolInspectionDO::getDeleted, 0));
        if (inspectionDOList.size() == 0) {
            return null;
        }
        String platformIdStr = StringUtils.join(platformId, ",");
        //存入巡检的结果
        List<InspectionLogRespVO> inspectionLogs = new ArrayList<>();
        //记录异常项数量
        Integer num = 0;
        BigDecimal totalScore = new BigDecimal(0);

        //按照巡检项查询设备信息并计算
        for (PatrolInspectionDO inspectionDO : inspectionDOList) {

            if (StringUtil.isNotBlank(inspectionDO.getAssetType())) {
                String uids = StringUtils.join(uuidList.get(inspectionDO.getAssetType()), ",");
                //influx数据库数据
                if (inspectionDO.getDataResource().equals("influxDB")) {
                    uids = uids.replace(",", "' or uuid = '");
                    String targetQuery = "SELECT uuid,platformName,productsName,platformId,value FROM ( SELECT * from ( SELECT "
                            + inspectionDO.getType().substring(inspectionDO.getType().indexOf(".") + 1) + "(value) as value,uuid,platformName,productsName,platformId from zj_cloud_"
                            + inspectionDO.getAssetType() + " WHERE (uuid = '" + uids + "')  and time >= " + stamp + " and metricName ='"
                            + inspectionDO.getType().substring(0, inspectionDO.getType().indexOf("."))
                            + "') GROUP BY uuid) WHERE value";
                    if (inspectionDO.getType().substring(inspectionDO.getType().indexOf(".") + 1).equals("min")) {
                        targetQuery = targetQuery + " < " + inspectionDO.getThreshold();
                    } else if (inspectionDO.getType().substring(inspectionDO.getType().indexOf(".") + 1).equals("max")) {
                        targetQuery = targetQuery + " >= " + inspectionDO.getThreshold();
                    }
                    // 异常结果集
                    List<Object> result = new ArrayList<>();
//                    List<Object> result = influxDBTemplate.fetchRecords(targetQuery);
//

                    Map<String, Object> paramVal = new HashMap<>();
                    InspectionLogRespVO log = new InspectionLogRespVO();
                    //存入巡检记录
                    log.setInspectionId(inspectionDO.getId());
                    log.setInspectionName(inspectionDO.getName());
                    log.setThreshold(inspectionDO.getThreshold());
                    log.setPlatformId(platformIdStr);
                    BigDecimal score = inspectionDO.getValue();
                    //计算得分
                    List<String> list = new ArrayList<>();
                    if (result.size() > 0) {
                        if (inspectionDO.getType().substring(inspectionDO.getType().indexOf(".") + 1).equals("min")) {
                            paramVal.put("a", result.size() * 1.0 / uuidList.get(inspectionDO.getAssetType()).size());
                        } else if (inspectionDO.getType().substring(inspectionDO.getType().indexOf(".") + 1).equals("max")) {
                            paramVal.put("a", result.size());
                        }
                        //记录异常设备的uuid
                        String uuidStr = "";
                        for (Object item : result) {
                            Map<String, Object> itemMap = JSONObject.parseObject(JSON.toJSONString(item), Map.class);
                            list.add(itemMap.get("uuid").toString());
                            uuidStr = uuidStr + itemMap.get("uuid") + ",";
                        }
                        log.setAssetUuid(uuidStr);
                        score = mathCalculate(inspectionDO.getFormula(), paramVal, JSONObject.parseObject(inspectionDO.getParam(), Map.class));

                    }
                    totalScore = totalScore.add(score);
                    log.setAssetNum(result.size());
                    int compare = score.compareTo(inspectionDO.getValue());
                    log.setScore(compare < 0 ? score : inspectionDO.getValue());
                    log.setResult(compare < 0 ? "异常" : "正常");
                    if (compare < 0) {
                        num++;
                    }
                    log.setRecordUuid(recordReqVO.getUuid());
                    inspectionLogs.add(log);


//                    String normalQuery = "SELECT uuid,platformName,productsName,platformId,value FROM ( SELECT * from ( SELECT "
//                            + inspectionDO.getType().substring(inspectionDO.getType().indexOf(".") + 1) + "(value) as value,uuid,platformName,productsName,platformId from zj_cloud_"
//                            + inspectionDO.getAssetType() + " WHERE (uuid = '" + uids + "')  and time >= " + stamp + " and metricName ='"
//                            + inspectionDO.getType().substring(0, inspectionDO.getType().indexOf("."))
//                            + "') GROUP BY uuid)";
//
//                    if (list.size() > 0) {
//                        normalQuery += "where ";
//                        for (int i = 0; i < list.size(); i++) {
//                            normalQuery += "uuid != '" + list.get(i) + "'";
//                            if (i < list.size() - 1) {
//                                normalQuery += " AND ";
//                            }
//                        }
//                    }
//
//                    // 查询出正常的结果集
//                    List<Object> normalList = influxDBTemplate.fetchRecords(normalQuery);

//                    for (String o : list) {
//                        List<?> assetList = Collections.emptyList();
//                        String resultStatus = "异常";
//
//                        if ("host".equals(inspectionDO.getAssetType())) {
//                            assetList = hostList;
//                        } else if ("hardware".equals(inspectionDO.getAssetType())) {
//                            assetList = hardWareList;
//                        } else {
//                            assetList = storageList;
//                        }
//
//                        processInspectionLogs(inspectionDO, o, assetList, normalList, resultStatus, inspectionLogsDOS);
//                    }

                    List<InspectionLogsDO> inspectionLogsDOS = new ArrayList<>();
                    if (inspectionDO.getAssetType().equals("host")) {
                        // 巡检异常项
                        for (HostInfoRespCreateReqDTO h : hostList) {
                            InspectionLogsDO inspectionLogsDO = new InspectionLogsDO();
                            inspectionLogsDO.setInspectionId(inspectionDO.getId());
                            inspectionLogsDO.setRecordUuid(recordUuid);
                            inspectionLogsDO.setInspectionName(inspectionDO.getName());
                            inspectionLogsDO.setThreshold(inspectionDO.getThreshold());
                            inspectionLogsDO.setAssetName(h.getName());
                            inspectionLogsDO.setAssetUuid(h.getUuid());
                            inspectionLogsDO.setPlatformId(h.getPlatformId().toString());
                            inspectionLogsDO.setPlatformName(h.getPlatformName());
                            if(inspectionDO.getType().toLowerCase(Locale.ROOT).contains("cpu")){
                                inspectionLogsDO.setAbnormalItem("cpu");
                            } else if (inspectionDO.getType().toLowerCase(Locale.ROOT).contains("mem")) {
                                inspectionLogsDO.setAbnormalItem("mem");
                            }else if (inspectionDO.getType().toLowerCase(Locale.ROOT).contains("disk")) {
                                inspectionLogsDO.setAbnormalItem("disk");
                            }
                            inspectionLogsDO.setAssetType(inspectionLogsDO.getAssetType());
                            inspectionLogsDO.setCpu(h.getCpuUsed().doubleValue());
                            double diskUsedValue = h.getDiskUsed() != null ? h.getDiskUsed().doubleValue() : 0.0;
                            inspectionLogsDO.setMem(h.getMemoryUsed().doubleValue());
                            inspectionLogsDO.setDisk(diskUsedValue);
                            inspectionLogsDO.setResult("正常");
                            if (list.size() > 0) {
                                for (String o : list) {
                                    if (StringUtil.toString(o).equals(StringUtil.toString(h.getUuid()))) {
                                        inspectionLogsDO.setResult("异常");
                                    }
                                }
                            }
                            inspectionLogsDOS.add(inspectionLogsDO);
                        }


                    } else if (inspectionDO.getAssetType().equals("hardware")) {
                        for (HardWareRespCreateReqDTO h : hardWareList) {
                            InspectionLogsDO inspectionLogsDO = new InspectionLogsDO();
                            inspectionLogsDO.setInspectionId(inspectionDO.getId());
                            inspectionLogsDO.setRecordUuid(recordUuid);
                            inspectionLogsDO.setInspectionName(inspectionDO.getName());
                            inspectionLogsDO.setThreshold(inspectionDO.getThreshold());
                            inspectionLogsDO.setAssetName(h.getName());
                            inspectionLogsDO.setAssetUuid(h.getUuid());
                            inspectionLogsDO.setPlatformId(h.getPlatformId().toString());
                            inspectionLogsDO.setPlatformName(h.getPlatformName());
                            if(inspectionDO.getType().toLowerCase(Locale.ROOT).contains("cpu")){
                                inspectionLogsDO.setAbnormalItem("cpu");
                            } else if (inspectionDO.getType().toLowerCase(Locale.ROOT).contains("mem")) {
                                inspectionLogsDO.setAbnormalItem("mem");
                            }else if (inspectionDO.getType().toLowerCase(Locale.ROOT).contains("disk")) {
                                inspectionLogsDO.setAbnormalItem("disk");
                            }
                            inspectionLogsDO.setAssetType(inspectionLogsDO.getAssetType());
                            inspectionLogsDO.setCpu(h.getCpuUsed().doubleValue());
                            inspectionLogsDO.setMem(h.getMemoryUsed().doubleValue());
                            inspectionLogsDO.setDisk(h.getDiskUsed().doubleValue());
                            inspectionLogsDO.setResult("正常");
                            // 巡检异常项
                            if (list.size() > 0) {
                                for (String o : list) {
                                    if (StringUtil.toString(o).equals(h.getUuid())) {
                                        inspectionLogsDO.setResult("异常");
                                    }
                                }
                            }
                            inspectionLogsDOS.add(inspectionLogsDO);
                        }

                    } else {

                        for (StorageRespCreateReqDTO h : storageList) {
                            InspectionLogsDO inspectionLogsDO = new InspectionLogsDO();
                            inspectionLogsDO.setInspectionId(inspectionDO.getId());
                            inspectionLogsDO.setRecordUuid(recordUuid);
                            inspectionLogsDO.setInspectionName(inspectionDO.getName());
                            inspectionLogsDO.setThreshold(inspectionDO.getThreshold());
                            inspectionLogsDO.setAssetName(h.getName());
                            inspectionLogsDO.setAssetUuid(h.getUuid());
                            inspectionLogsDO.setPlatformId(h.getPlatformId().toString());
                            inspectionLogsDO.setPlatformName(h.getPlatformName());
                            if(inspectionDO.getType().toLowerCase(Locale.ROOT).contains("cpu")){
                                inspectionLogsDO.setAbnormalItem("cpu");
                            } else if (inspectionDO.getType().toLowerCase(Locale.ROOT).contains("mem")) {
                                inspectionLogsDO.setAbnormalItem("mem");
                            }else if (inspectionDO.getType().toLowerCase(Locale.ROOT).contains("disk")) {
                                inspectionLogsDO.setAbnormalItem("disk");
                            }
                            inspectionLogsDO.setAssetType(inspectionLogsDO.getAssetType());
                            inspectionLogsDO.setResult("正常");
                            // 巡检异常项
                            if (list.size() > 0) {
                                for (String o : list) {
                                    if (StringUtil.toString(o).equals(h.getUuid())) {
                                        inspectionLogsDO.setResult("异常");
                                    }
                                }
                            }
                            inspectionLogsDOS.add(inspectionLogsDO);
                        }

                    }
                    inspectionLogsService.batchInspectionLogs(inspectionLogsDOS);
                    //TODO 数据库变更
                }
//                else if (inspectionDO.getDataResource().equals("ESDB")) {
//                    //从ES库获取要检测的告警数据
//                    InspectionLogRespVO log = new InspectionLogRespVO();
//                    Map searchFields = new HashMap<>();
//                    List<String> uuids = new ArrayList<>();
//                    if (StringUtil.isNotEmpty(uids)) {
//                        uuids = Arrays.asList("\"" + uids.replaceAll(",", "\",\"") + "\"");
//                    }
//                    searchFields.put("time", DateUtil.format(c.getTime(), "yyyy-MM-dd"));
//                    searchFields.put("uuid", uuids);
//                    searchFields.put("sourceType", "monitor_alarm_" + inspectionDO.getAssetType());
//
////                    ESDatas<Map> dataCount = clientUtil.searchList("alarm_info/_search", "alarmInfoCount", Map.class);
//                    List<Map<String, Object>> result = new ArrayList<>();
//                    ESDatas<Map> data = new ESDatas<>();
//                    Object countValue = dataCount.getAggregations().get("total_count").get("value");
//                     if (Convert.toInt(countValue)>0){
//                          data = clientUtil.searchList("alarm_info/_search", "alarmInfo", searchFields, Map.class);
//                          result = data.getAggregationBuckets("group_by_uuid");
//                     }
//
//                    //将数据存到检测记录中
//                    log.setInspectionId(inspectionDO.getId());
//                    log.setInspectionName(inspectionDO.getName());
//                    log.setThreshold(inspectionDO.getThreshold());
//                    log.setPlatformId(platformIdStr);
//                    log.setAssetNum(result != null ? result.size() : 0);
//                    BigDecimal score = inspectionDO.getValue();
//                    //记录存在告警的设备uuid
//                    Map<String, Object> paramVal = new HashMap<>();
//                    String uuidStr = "";
//                    List<String> list = new ArrayList<>();
//                    if (result != null && result.size() > 0) {
//                        for (int i = 0; i < result.size(); i++) {
//                            Map<String, Object> map = dealResult(result.get(i));
//                            list.add(map.get("uuid").toString());
//                            uuidStr = uuidStr + map.get("uuid") + ",";
//                        }
//                    }
//
//
//                    List<InspectionLogsDO> inspectionLogsDOS = new ArrayList<>();
//
//                    for (HostInfoRespCreateReqDTO vm : hostList) {
//                        InspectionLogsDO inspectionLogsDO = new InspectionLogsDO();
//                        inspectionLogsDO.setInspectionId(inspectionDO.getId());
//                        inspectionLogsDO.setRecordUuid(recordUuid);
//                        inspectionLogsDO.setInspectionName(inspectionDO.getName());
//                        inspectionLogsDO.setThreshold(inspectionDO.getThreshold());
//                        inspectionLogsDO.setAssetName(vm.getName());
//                        inspectionLogsDO.setAssetUuid(vm.getUuid());
//                        inspectionLogsDO.setPlatformId(vm.getPlatformId().toString());
//                        inspectionLogsDO.setPlatformName(vm.getPlatformName());
//                        if(inspectionDO.getType().toLowerCase(Locale.ROOT).contains("cpu")){
//                            inspectionLogsDO.setAbnormalItem("cpu");
//                        } else if (inspectionDO.getType().toLowerCase(Locale.ROOT).contains("mem")) {
//                            inspectionLogsDO.setAbnormalItem("mem");
//                        }else if (inspectionDO.getType().toLowerCase(Locale.ROOT).contains("disk")) {
//                            inspectionLogsDO.setAbnormalItem("disk");
//                        }
//                        inspectionLogsDO.setAssetType(inspectionLogsDO.getAssetType());
//                        inspectionLogsDO.setCpu(vm.getCpuUsed().doubleValue());
//                        inspectionLogsDO.setMem(vm.getMemoryUsed().doubleValue());
//                        inspectionLogsDO.setDisk(vm.getDiskUsed().doubleValue());
//                        inspectionLogsDO.setResult("正常");
//                        if (list.size() > 0) {
//                            for (String o : list) {
//                                if (StringUtil.toString(o).equals(StringUtil.toString(vm.getUuid()))) {
//                                    inspectionLogsDO.setResult("异常");
//                                }
//                            }
//                        }
//                        inspectionLogsDOS.add(inspectionLogsDO);
//                    }
//
//
//                    for (HardWareRespCreateReqDTO h : hardWareList) {
//                        InspectionLogsDO inspectionLogsDO = new InspectionLogsDO();
//                        inspectionLogsDO.setInspectionId(inspectionDO.getId());
//                        inspectionLogsDO.setRecordUuid(recordUuid);
//                        inspectionLogsDO.setInspectionName(inspectionDO.getName());
//                        inspectionLogsDO.setThreshold(inspectionDO.getThreshold());
//                        inspectionLogsDO.setAssetName(h.getName());
//                        inspectionLogsDO.setAssetUuid(h.getUuid());
//                        inspectionLogsDO.setPlatformId(h.getPlatformId().toString());
//                        inspectionLogsDO.setPlatformName(h.getPlatformName());
//                        if(inspectionDO.getType().toLowerCase(Locale.ROOT).contains("cpu")){
//                            inspectionLogsDO.setAbnormalItem("cpu");
//                        } else if (inspectionDO.getType().toLowerCase(Locale.ROOT).contains("mem")) {
//                            inspectionLogsDO.setAbnormalItem("mem");
//                        }else if (inspectionDO.getType().toLowerCase(Locale.ROOT).contains("disk")) {
//                            inspectionLogsDO.setAbnormalItem("disk");
//                        }
//                        inspectionLogsDO.setAssetType(inspectionLogsDO.getAssetType());
//                        inspectionLogsDO.setCpu(h.getCpuUsed().doubleValue());
//                        inspectionLogsDO.setMem(h.getMemoryUsed().doubleValue());
//                        inspectionLogsDO.setDisk(h.getDiskUsed().doubleValue());
//                        inspectionLogsDO.setResult("正常");
//                        // 巡检异常项
//                        if (list.size() > 0) {
//                            for (String o : list) {
//                                if (StringUtil.toString(o).equals(h.getUuid())) {
//                                    inspectionLogsDO.setResult("异常");
//                                }
//                            }
//                        }
//                        inspectionLogsDOS.add(inspectionLogsDO);
//                    }
//
//                    for (StorageRespCreateReqDTO s : storageList) {
//                        InspectionLogsDO inspectionLogsDO = new InspectionLogsDO();
//                        inspectionLogsDO.setInspectionId(inspectionDO.getId());
//                        inspectionLogsDO.setRecordUuid(recordUuid);
//                        inspectionLogsDO.setInspectionName(inspectionDO.getName());
//                        inspectionLogsDO.setThreshold(inspectionDO.getThreshold());
//                        inspectionLogsDO.setAssetName(s.getName());
//                        inspectionLogsDO.setAssetUuid(s.getUuid());
//                        inspectionLogsDO.setPlatformId(s.getPlatformId().toString());
//                        inspectionLogsDO.setPlatformName(s.getPlatformName());
//                        if(inspectionDO.getType().toLowerCase(Locale.ROOT).contains("cpu")){
//                            inspectionLogsDO.setAbnormalItem("cpu");
//                        } else if (inspectionDO.getType().toLowerCase(Locale.ROOT).contains("mem")) {
//                            inspectionLogsDO.setAbnormalItem("mem");
//                        }else if (inspectionDO.getType().toLowerCase(Locale.ROOT).contains("disk")) {
//                            inspectionLogsDO.setAbnormalItem("disk");
//                        }
//                        inspectionLogsDO.setAssetType(inspectionLogsDO.getAssetType());
//                        inspectionLogsDO.setResult("正常");
//                        // 巡检异常项
//                        if (list.size() > 0) {
//                            for (String o : list) {
//                                if (StringUtil.toString(o).equals(s.getUuid())) {
//                                    inspectionLogsDO.setResult("异常");
//                                }
//                            }
//                        }
//                        inspectionLogsDOS.add(inspectionLogsDO);
//                    }
//
//                    inspectionLogsService.batchInspectionLogs(inspectionLogsDOS);
//                    //并计算分数
//                    paramVal.put("a", data.getTotalSize());
//                    paramVal.put("b", uuidList.get(inspectionDO.getAssetType()).size());
//                    score = mathCalculate(inspectionDO.getFormula(), paramVal, JSONObject.parseObject(inspectionDO.getParam(), Map.class));
//                    log.setAssetUuid(uuidStr);
//                    //如果得分结果比分值高，说明计算过程中出现了负数
//                    int compare = score.compareTo(inspectionDO.getValue());
//                    log.setScore(compare < 0 ? score : inspectionDO.getValue());
//                    log.setResult(compare < 0 ? "异常" : "正常");
//                    if (compare < 0) {
//                        num++;
//                    }
//                    log.setRecordUuid(recordReqVO.getUuid());
//                    inspectionLogs.add(log);
//                    totalScore = totalScore.add(score);
//                }
            }
        }
        if (num > 0) {
            recordReqVO.setResult(num + "项异常");
        } else {
            recordReqVO.setResult("正常");
        }
        recordReqVO.setNormalNum(inspectionDOList.size() - num);
        recordReqVO.setAbnormalNum(num);
        recordReqVO.setScore(totalScore);
        inspectionRecordService.createInspectionRecord(recordReqVO);
        List<InspectionLogDO> inspectionLogDOList = InspectionLogConvert.INSTANCE.convertList03(inspectionLogs);
        inspectionLogService.createInspectionLogList(inspectionLogDOList);
        long endTime = System.currentTimeMillis();
        Map<String, Object> result = new HashMap<>();
        result.put("timeCost", endTime - startTime);
        result.put("finishTime", new Date().getTime());
        result.put("abnormalNum", num);
        result.put("recordUuid", recordReqVO.getUuid());

        return result;
    }

    private void processInspectionLogs(PatrolInspectionDO inspectionDO, String uuid, List<?> assetList, List<Object> normalList, String resultStatus, List<InspectionLogsDO> inspectionLogsDOS) {
        for (Object obj : assetList) {
            Map<String, Object> itemMap = JSONObject.parseObject(JSON.toJSONString(obj), Map.class);
            if (uuid.equals(itemMap.get("uuid").toString())) {
                InspectionLogsDO inspectionLogsDO = createInspectionLogs(inspectionDO, obj, resultStatus);
                inspectionLogsDOS.add(inspectionLogsDO);
                break;
            }
        }


        if ("正常" .equals(resultStatus)) {
            for (Object obj : normalList) {
                Map<String, Object> itemMap = JSONObject.parseObject(JSON.toJSONString(obj), Map.class);
                if (uuid.equals(itemMap.get("uuid").toString())) {
                    InspectionLogsDO inspectionLogsDO = createInspectionLogs(inspectionDO, obj, resultStatus);
                    inspectionLogsDOS.add(inspectionLogsDO);
                    break;
                }
            }
        }
    }

    private InspectionLogsDO createInspectionLogs(PatrolInspectionDO inspectionDO, Object obj, String resultStatus) {
        Map<String, Object> itemMap = JSONObject.parseObject(JSON.toJSONString(obj), Map.class);
        InspectionLogsDO inspectionLogsDO = new InspectionLogsDO();
        inspectionLogsDO.setRecordUuid(inspectionDO.getId().toString());
        inspectionLogsDO.setInspectionName(inspectionDO.getName());
        inspectionLogsDO.setThreshold(inspectionDO.getThreshold());
        inspectionLogsDO.setAssetName(itemMap.get("name").toString());
        inspectionLogsDO.setAssetUuid(itemMap.get("uuid").toString());
        inspectionLogsDO.setPlatformId(itemMap.get("platformId").toString());
        inspectionLogsDO.setPlatformName(itemMap.get("platformName").toString());
        inspectionLogsDO.setResult(resultStatus);

        return inspectionLogsDO;
    }

    @Override
    public Map getInspectionBaseInfo(Long tenantId) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        PatrolInspectionExportReqVO exportReqVO = new PatrolInspectionExportReqVO();
        List<PatrolInspectionDO> patrolInspectionDOList = patrolInspectionMapper.selectList(exportReqVO);
        Map assetNum = patrolInspectionMapper.getAssetNum(tenantId);
        Map map = new HashMap<>();
        Map<String, List<PatrolInspectionDO>> ruleMmap = patrolInspectionDOList.stream().collect(Collectors.groupingBy(PatrolInspectionDO::getAssetType));
        InspectionRecordRespVO recordRespVO = patrolInspectionMapper.getLastInspectionRecord(loginUser.getId());
        Map rules = new HashMap<>();
        Class<AssetType> assetTypeClass = AssetType.class;
        for (String key : ruleMmap.keySet()) {
            Map<String, Object> info = new HashMap<>();
            info.put("type", key);
            info.put("name", Enum.valueOf(assetTypeClass, key.toUpperCase()).getType());
            info.put("num", ruleMmap.get(key).size());
            rules.put(key, info);
        }
        map.putAll(assetNum);
        map.put("rules", rules);
        map.put("totalInspectionNum", 0);
        map.put("normalNum", 0);
        map.put("abnormalNum", 0);
        map.put("lastInspectionTime", 0);
        if (recordRespVO != null) {
            map.put("totalInspectionNum", recordRespVO.getAbnormalNum() + recordRespVO.getNormalNum());
            map.put("normalNum", recordRespVO.getNormalNum());
            map.put("abnormalNum", recordRespVO.getAbnormalNum());
            map.put("lastInspectionTime", recordRespVO.getCreateTime());
        }
        return map;
    }


    public BigDecimal mathCalculate(String expression, Map<String, Object> paramValue, Map<String, String> paramJson) {
        try {
            Map<String, Object> env = new HashMap<>();

            for (String keyVal : paramJson.keySet()) {
                Double val = Double.parseDouble(paramValue.get(keyVal).toString());
                env.put(keyVal, val);
                expression = expression.replaceAll(keyVal, keyVal);
            }
            // 执行表达式计算
            Number result = (Number) AviatorEvaluator.execute(expression, env);
            return new BigDecimal(result.doubleValue()).setScale(2, RoundingMode.HALF_UP);
        } catch (Exception e) {
            throw new RuntimeException("数学表达式计算失败: " + expression, e);
        }
    }

    private Map<String, Object> dealResult(Object result) {
        Map<String, Object> map;
        if (result instanceof JSONArray) {
            map = JSONObject.parseObject(JSONObject.toJSONString(((JSONArray) result).get(0)), Map.class);
        } else {
            map = JSONObject.parseObject(JSONObject.toJSONString(result), Map.class);
        }
        Map<String, Object> resultMap = new HashMap<>();
        for (String key : map.keySet()) {
            if (key.equals("my_top_hits") || key.equals("hits")) {
                resultMap = dealResult(map.get(key));
            } else if (key.equals("_source")) {
                resultMap = JSONObject.parseObject(JSONObject.toJSONString(map.get("_source")), Map.class);
            }
        }
        return resultMap;
    }
}