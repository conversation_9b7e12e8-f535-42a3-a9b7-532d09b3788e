package cn.iocoder.zj.module.om.controller.admin.inspectiondata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 巡检记录 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InspectionLogRespVO extends InspectionLogBaseVO {

    @Schema(description = "id", required = true)
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

    @Schema(description = "设备名称", required = true)
    private String assetName;

    @Schema(description = "资产类型")
    private String assetType;

    @Schema(description = "资产id")
    private String assetId;
}
