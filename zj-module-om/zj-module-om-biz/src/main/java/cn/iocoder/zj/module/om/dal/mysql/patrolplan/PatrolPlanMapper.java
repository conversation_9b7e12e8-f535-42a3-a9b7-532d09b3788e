package cn.iocoder.zj.module.om.dal.mysql.patrolplan;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.module.om.controller.admin.patrolplan.vo.PatrolPlanExportReqVO;
import cn.iocoder.zj.module.om.controller.admin.patrolplan.vo.PatrolPlanPageReqVO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolplan.PatrolPlanDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolplan.PatrolPlanDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 巡检计划 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PatrolPlanMapper extends BaseMapperX<PatrolPlanDO> {

    default PageResult<PatrolPlanDO> selectPage(PatrolPlanPageReqVO reqVO) {

        LambdaQueryWrapperX<PatrolPlanDO> queryWrapper = new LambdaQueryWrapperX<PatrolPlanDO>()
                .likeIfPresent(PatrolPlanDO::getName, reqVO.getName())
                .eqIfPresent(PatrolPlanDO::getStatus, reqVO.getStatus())
                .eqIfPresent(PatrolPlanDO::getSysSettingTenant, reqVO.getTenantId())
                .likeIfPresent(PatrolPlanDO::getPlatformIds, reqVO.getPlatformId());

        if (reqVO.getStartTime() != null && reqVO.getEndTime() != null) {
            queryWrapper.between(PatrolPlanDO::getLastExecutionTime, reqVO.getStartTime(), reqVO.getEndTime());
        }
        if (reqVO.getSortBy() != null && reqVO.getSortDirection() != null) {
            if (reqVO.getSortBy().equals("lastExecutionTime")) {
                if (reqVO.getSortDirection().equals("asc")) {
                    queryWrapper.orderByAsc(PatrolPlanDO::getLastExecutionTime);
                } else {
                    queryWrapper.orderByDesc(PatrolPlanDO::getLastExecutionTime);
                }
            }
            if (reqVO.getSortBy().equals("nextExecutionTime")) {
                if (reqVO.getSortDirection().equals("asc")) {
                    queryWrapper.orderByAsc(PatrolPlanDO::getNextExecutionTime);
                } else {
                    queryWrapper.orderByDesc(PatrolPlanDO::getNextExecutionTime);
                }
            }
        }

        return selectPage(reqVO, queryWrapper);
    }

    default List<PatrolPlanDO> selectList(PatrolPlanExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PatrolPlanDO>()
                .likeIfPresent(PatrolPlanDO::getName, reqVO.getName())
                .eqIfPresent(PatrolPlanDO::getPeriodType, reqVO.getPeriodType())
                .eqIfPresent(PatrolPlanDO::getExecutionDay, reqVO.getExecutionDay())
                .betweenIfPresent(PatrolPlanDO::getExecutionTime, reqVO.getExecutionTime())
                .eqIfPresent(PatrolPlanDO::getExecutionCron, reqVO.getExecutionCron())
                .eqIfPresent(PatrolPlanDO::getPatrolItem, reqVO.getPatrolItem())
                .eqIfPresent(PatrolPlanDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(PatrolPlanDO::getLastExecutionTime, reqVO.getLastExecutionTime())
                .betweenIfPresent(PatrolPlanDO::getNextExecutionTime, reqVO.getNextExecutionTime())
                .betweenIfPresent(PatrolPlanDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(PatrolPlanDO::getJobId, reqVO.getJobId())
                .orderByDesc(PatrolPlanDO::getId));
    }

    List<PatrolPlanDTO> selectPatrolPlanPage(
            @Param("mpPage") IPage<PatrolPlanDTO> mpPage,
            @Param("reqVO") PatrolPlanPageReqVO reqVO);

}
