package cn.iocoder.zj.module.om.convert.monitorauthorization;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.om.controller.admin.monitorauthorization.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.monitorauthorization.MonitorAuthorizationDO;

/**
 * 监控申请授权 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface MonitorAuthorizationConvert {

    MonitorAuthorizationConvert INSTANCE = Mappers.getMapper(MonitorAuthorizationConvert.class);

    MonitorAuthorizationDO convert(MonitorAuthorizationCreateReqVO bean);

    MonitorAuthorizationDO convert(MonitorAuthorizationUpdateReqVO bean);

    MonitorAuthorizationRespVO convert(MonitorAuthorizationDO bean);

    List<MonitorAuthorizationRespVO> convertList(List<MonitorAuthorizationDO> list);

    PageResult<MonitorAuthorizationRespVO> convertPage(PageResult<MonitorAuthorizationDO> page);

    List<MonitorAuthorizationExcelVO> convertList02(List<MonitorAuthorizationDO> list);

}
