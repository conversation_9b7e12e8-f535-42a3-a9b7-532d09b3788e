package cn.iocoder.zj.module.om.convert.monitorauthorizationuser;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.om.controller.admin.monitorauthorizationuser.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.monitorauthorizationuser.MonitorAuthorizationUserDO;

/**
 * 用户资产授权申请 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface MonitorAuthorizationUserConvert {

    MonitorAuthorizationUserConvert INSTANCE = Mappers.getMapper(MonitorAuthorizationUserConvert.class);

    MonitorAuthorizationUserDO convert(MonitorAuthorizationUserCreateReqVO bean);

    MonitorAuthorizationUserDO convert(MonitorAuthorizationUserUpdateReqVO bean);

    MonitorAuthorizationUserRespVO convert(MonitorAuthorizationUserDO bean);

    List<MonitorAuthorizationUserRespVO> convertList(List<MonitorAuthorizationUserDO> list);

    PageResult<MonitorAuthorizationUserRespVO> convertPage(PageResult<MonitorAuthorizationUserDO> page);

    List<MonitorAuthorizationUserExcelVO> convertList02(List<MonitorAuthorizationUserDO> list);

}
