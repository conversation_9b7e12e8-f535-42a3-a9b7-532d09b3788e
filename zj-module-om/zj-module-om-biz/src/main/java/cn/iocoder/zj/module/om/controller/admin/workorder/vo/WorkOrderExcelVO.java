package cn.iocoder.zj.module.om.controller.admin.workorder.vo;

import cn.iocoder.zj.framework.excel.core.convert.StateConvert;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 工单管理数据 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class WorkOrderExcelVO {

    @ExcelProperty("名称")
    private String name;

    @ExcelProperty("项目（平台资源）")
    private String sourceName;

    @ExcelProperty("简介")
    private String description;

    @ExcelProperty("工单类型")
    private String typeName;

    @ExcelProperty("处理人名称")
    private String auditorName;

    @ExcelProperty("实施人名称")
    private String enforcerName;

    @ExcelProperty(value = "审批状态",converter = StateConvert.class)
    private String ratifyStatus;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private Date createTime;

}
