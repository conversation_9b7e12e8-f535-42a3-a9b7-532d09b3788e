package cn.iocoder.zj.module.om.controller.admin.patrolinspectionconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 巡检设置更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PatrolInspectionConfigUpdateReqVO extends PatrolInspectionConfigBaseVO {

    @Schema(description = "主键ID", required = true)
    @NotNull(message = "主键ID不能为空")
    private Long id;

}
