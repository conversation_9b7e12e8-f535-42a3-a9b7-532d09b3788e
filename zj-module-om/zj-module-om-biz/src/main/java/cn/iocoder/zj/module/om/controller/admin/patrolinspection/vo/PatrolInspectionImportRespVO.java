package cn.iocoder.zj.module.om.controller.admin.patrolinspection.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Schema(description = "管理后台 - 巡检规则导入 Response VO")
@Data
@Builder
public class PatrolInspectionImportRespVO {

    @Schema(description = "创建成功的巡检规则数组", required = true)
    private List<String> createPatrolInspectionNames;

    @Schema(description = "更新成功的巡检规则数组", required = true)
    private List<String> updatePatrolInspectionNames;

    @Schema(description = "导入失败的巡检规则集合，key 为巡检规则名称，value 为失败原因", required = true)
    private Map<String, String> failurePatrolInspectionNames;

}