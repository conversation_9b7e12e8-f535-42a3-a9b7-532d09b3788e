package cn.iocoder.zj.module.om.service.patrolplan;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.om.controller.admin.patrolplan.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.patrolplan.PatrolPlanDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 巡检计划 Service 接口
 *
 * <AUTHOR>
 */
public interface PatrolPlanService {

    /**
     * 创建巡检计划
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPatrolPlan(@Valid PatrolPlanCreateReqVO createReqVO);

    /**
     * 更新巡检计划
     *
     * @param updateReqVO 更新信息
     */
    void updatePatrolPlan(@Valid PatrolPlanUpdateReqVO updateReqVO);

    /**
     * 删除巡检计划
     *
     * @param id 编号
     */
    void deletePatrolPlan(Long id);

    /**
     * 获得巡检计划
     *
     * @param id 编号
     * @return 巡检计划
     */
    PatrolPlanRespVO getPatrolPlan(Long id);

    /**
     * 获得巡检计划列表
     *
     * @param ids 编号
     * @return 巡检计划列表
     */
    List<PatrolPlanDO> getPatrolPlanList(Collection<Long> ids);

    /**
     * 获得巡检计划分页
     *
     * @param pageReqVO 分页查询
     * @return 巡检计划分页
     */
    PageResult<PatrolPlanRespVO> getPatrolPlanPage(PatrolPlanPageReqVO pageReqVO);

    /**
     * 获得巡检计划列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 巡检计划列表
     */
    List<PatrolPlanDO> getPatrolPlanList(PatrolPlanExportReqVO exportReqVO);

    /**
     * 根据jobId获取巡检计划
     *
     * @param jobId
     * @return 巡检计划
     */
    PatrolPlanDO getPatrolPlanByJobId(Long jobId);

    /**
     * 修改最近执行时间和下次执行时间
     *
     * @param patrolPlanDO
     */
    void updateLastTimeAndNextTime(PatrolPlanDO patrolPlanDO);

    void executePatrolPlan(Long id);

    void startOrStopPatrolPlan(Long id, int status);
}
