package cn.iocoder.zj.module.om.dal.dataobject.patrolinspection;

import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import org.apache.poi.hpsf.Decimal;

/**
 * 自动巡检规则 DO
 *
 * <AUTHOR>
 */
@TableName("om_patrol_inspection")
@KeySequence("om_patrol_inspection_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PatrolInspectionDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 检测项名称
     */
    private String name;
    /**
     * 资产类型
     */
    private String assetType;
    /**
     * 检测项类型
     */
    private String type;
    /**
     * 数据来源
     */
    private String dataResource;
    /**
     * 阈值
     */
    private BigDecimal threshold;
    /**
     * 分值
     */
    private BigDecimal value;
    /**
     * 扣分规则
     */
    private String rule;
    /**
     * 计算公式
     */
    private String formula;
    /**
    * 参数设置
    */
    private String param;

}
