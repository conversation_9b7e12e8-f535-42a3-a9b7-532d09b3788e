package cn.iocoder.zj.module.om.controller.admin.patrolinspectionconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class PatrolExecuteReqVO {
    /**
     * 租户编号
     */
    @Schema(description = "租户编号", example = "1024")
    private Long tenantId;
    /**
     * 平台编号
     */
    @Schema(description = "平台编号", example = "1024")
    private List<Long> platformIds;
}
