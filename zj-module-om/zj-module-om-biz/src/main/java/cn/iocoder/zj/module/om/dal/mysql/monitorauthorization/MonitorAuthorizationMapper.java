package cn.iocoder.zj.module.om.dal.mysql.monitorauthorization;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.om.dal.dataobject.monitorauthorization.MonitorAuthorizationDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.om.controller.admin.monitorauthorization.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 监控申请授权 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MonitorAuthorizationMapper extends BaseMapperX<MonitorAuthorizationDO> {

    default PageResult<MonitorAuthorizationDO> selectPage(MonitorAuthorizationPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MonitorAuthorizationDO>()
                .eqIfPresent(MonitorAuthorizationDO::getAssetId, reqVO.getAssetId())
                .likeIfPresent(MonitorAuthorizationDO::getAssetName, reqVO.getAssetName())
                .likeIfPresent(MonitorAuthorizationDO::getNickName, reqVO.getNickName())
                .likeIfPresent(MonitorAuthorizationDO::getHostName, reqVO.getHostName())
                .betweenIfPresent(MonitorAuthorizationDO::getCreateTime, reqVO.getCreateTime())
                .betweenIfPresent(MonitorAuthorizationDO::getAuthorizationTime, reqVO.getAuthorizationTime())
                .eqIfPresent(MonitorAuthorizationDO::getUserId, reqVO.getUserId())
                .orderByDesc(MonitorAuthorizationDO::getId));
    }

    default List<MonitorAuthorizationDO> selectList(MonitorAuthorizationExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MonitorAuthorizationDO>()
                .eqIfPresent(MonitorAuthorizationDO::getAssetId, reqVO.getAssetId())
                .likeIfPresent(MonitorAuthorizationDO::getAssetName, reqVO.getAssetName())
                .likeIfPresent(MonitorAuthorizationDO::getNickName, reqVO.getNickName())
                .likeIfPresent(MonitorAuthorizationDO::getHostName, reqVO.getHostName())
                .betweenIfPresent(MonitorAuthorizationDO::getCreateTime, reqVO.getCreateTime())
                .betweenIfPresent(MonitorAuthorizationDO::getAuthorizationTime, reqVO.getAuthorizationTime())
                .eqIfPresent(MonitorAuthorizationDO::getUserId, reqVO.getUserId())
                .orderByDesc(MonitorAuthorizationDO::getId));
    }

    void deleteMonitorAuthorizationInfo(@Param("id") Long id);

    void updateMonitorAuthorizationInfo(@Param("id") Long id, @Param("authorizationType") String authorizationType);

    MonitorAuthorizationDO selectByIdInfo(Long id);
}
