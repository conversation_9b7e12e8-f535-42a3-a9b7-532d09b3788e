package cn.iocoder.zj.module.om.service.patrolrecord;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.constants.FileTypeConstants;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.util.string.StringUtil;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.infra.api.file.FileApi;
import cn.iocoder.zj.module.monitor.api.hardware.HardWareInfoApi;
import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.storage.StorageInfoApi;
import cn.iocoder.zj.module.monitor.api.storage.dto.StorageRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.pojo.AssetReqVO;
import cn.iocoder.zj.module.om.controller.admin.patrolrecord.vo.*;
import cn.iocoder.zj.module.om.convert.patrolrecord.PatrolRecordConvert;
import cn.iocoder.zj.module.om.dal.dataobject.patrolabnormaldetail.PatrolAbnormalDetailDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspectionconfig.PatrolInspectionConfigDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolplan.PatrolPlanDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolrecord.PatrolRecordConfigDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolrecord.PatrolRecordDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolresultcategory.PatrolResultCategoryDO;
import cn.iocoder.zj.module.om.dal.mysql.patrolabnormaldetail.PatrolAbnormalDetailMapper;
import cn.iocoder.zj.module.om.dal.mysql.patrolinspectionconfig.PatrolInspectionConfigMapper;
import cn.iocoder.zj.module.om.dal.mysql.patrolplan.PatrolPlanMapper;
import cn.iocoder.zj.module.om.dal.mysql.patrolrecord.PatrolRecordMapper;
import cn.iocoder.zj.module.om.dal.mysql.patrolresultcategory.PatrolResultCategoryMapper;
import cn.iocoder.zj.module.om.enums.PatrolMetricEnum;
import cn.iocoder.zj.module.om.enums.PatrolResourceTypeEnum;
import cn.iocoder.zj.module.om.enums.PatrolRiskEnum;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import cn.iocoder.zj.module.system.api.tenant.TenantApi;
import cn.iocoder.zj.module.system.api.tenant.dto.TenantRespDTO;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.*;
import com.deepoove.poi.data.style.BorderStyle;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.om.enums.ErrorCodeConstants.PATROL_RECORD_NOT_EXISTS;

/**
 * 巡检记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PatrolRecordServiceImpl implements PatrolRecordService {

    @Resource
    private PatrolRecordMapper patrolRecordMapper;

    @Resource
    private PatrolPlanMapper patrolPlanMapper;

    @Resource
    private PatrolResultCategoryMapper patrolResultCategoryMapper;

    @Resource
    private PatrolInspectionConfigMapper patrolInspectionConfigMapper;

    @Resource
    private TenantApi tenantApi;

    @Resource
    private HardWareInfoApi hostInfoApi;

    @Resource
    private StorageInfoApi storageInfoApi;
    @Autowired
    private PatrolAbnormalDetailMapper patrolAbnormalDetailMapper;
    @Resource
    private FileApi fileApi;

    @Resource
    private PlatformconfigApi platformconfigApi;

    @Override
    public Long createPatrolRecord(PatrolRecordCreateReqVO createReqVO) {
        // 插入
        PatrolRecordDO patrolRecord = PatrolRecordConvert.INSTANCE.convert(createReqVO);
        patrolRecordMapper.insert(patrolRecord);
        // 返回
        return patrolRecord.getId();
    }

    @Override
    public void updatePatrolRecord(PatrolRecordUpdateReqVO updateReqVO) {
        // 校验存在
        validatePatrolRecordExists(updateReqVO.getId());
        // 更新
        PatrolRecordDO updateObj = PatrolRecordConvert.INSTANCE.convert(updateReqVO);
        patrolRecordMapper.updateById(updateObj);
    }

    @Override
    public void deletePatrolRecord(Long id) {
        // 校验存在
        validatePatrolRecordExists(id);
        // 删除
        patrolRecordMapper.deleteById(id);
    }

    private void validatePatrolRecordExists(Long id) {
        if (patrolRecordMapper.selectById(id) == null) {
            throw exception(PATROL_RECORD_NOT_EXISTS);
        }
    }

    @Override
    public PatrolRecordDO getPatrolRecord(Long id) {
        return patrolRecordMapper.selectById(id);
    }

    @Override
    public List<PatrolRecordDO> getPatrolRecordList(Collection<Long> ids) {
        return patrolRecordMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<PatrolRecordRespVO> getPatrolRecordPage(PatrolRecordPageReqVO pageReqVO) {
        PageResult<PatrolRecordDO> pageResult = patrolRecordMapper.selectPage(pageReqVO);
        List<PatrolRecordRespVO> records = BeanUtil.copyToList(pageResult.getList(), PatrolRecordRespVO.class);
        if (CollectionUtil.isNotEmpty(records)) {
            records.forEach(record -> {
                Long tenantId = record.getTenantId();
                if (ObjectUtil.isNotEmpty(record.getSysSettingTenant())) {
                    tenantId = record.getSysSettingTenant();
                }
                //根据 sysConfigTenant 获取租户名称
                String tenantName = tenantApi.getTenantSimpleInfo(tenantId).getData().getName();
                record.setTenantName(tenantName);
                //根据 platformIds 获取平台名称
                String platformIds = record.getPlatformIds();
                if (ObjectUtil.isNotEmpty(platformIds)) {
                    List<Long> platformIdList = Stream.of(platformIds.split(",")).map(Long::parseLong).toList();
                    String platformName = platformconfigApi.getPlatformByIds(platformIdList).getData().stream()
                            .map(PlatformconfigDTO::getName).collect(Collectors.joining(","));
                    record.setPlatformName(platformName);
                }
            });
        }
        PageResult<PatrolRecordRespVO> pageResult1 = new PageResult<>();
        pageResult1.setList(records);
        pageResult1.setTotal(pageResult.getTotal());
        return pageResult1;
    }

    @Override
    public List<PatrolRecordDO> getPatrolRecordList(PatrolRecordExportReqVO exportReqVO) {
        return patrolRecordMapper.selectList(exportReqVO);
    }

    private void createAndDownloadWord(Map<String, Object> paramMap, HttpServletResponse response, String recordName, String createTime) {
        log.info("生成电子协查函参数paramMap = {}", paramMap);
        //文件名称
        String d = recordName + "_" + createTime;
        try {
            // 创建一个临时的输出流
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            String path = "file-template" + File.separator + "inspection.docx";
            String dateTime = "日报";

            // 获取电子协查函模板

            ClassPathResource classPathResource = new ClassPathResource(path);

            try (InputStream inputStream = classPathResource.getInputStream()) {
                if (Objects.isNull(inputStream)) {
                    log.error("获取电子协查函模板失败");
                    return;
                }
                // 通过协查函模板，开始生成电子协查函
                try (XWPFTemplate template = XWPFTemplate.compile(inputStream).render(paramMap)) {
                    // 将生成的Word文档写入临时输出流
                    template.write(outputStream);
                    byte[] wordBytes = outputStream.toByteArray();
                    //写入minio
                    String minioPath = StringUtil.getSavePath(d, FileTypeConstants.FILE_TYPE, "inspection");
                    String url = fileApi.createFileUrl(d, minioPath, wordBytes);
                }

            } catch (Exception e) {
                log.error("创建协查函异常，异常详情：\n{}", e);
            }
            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(d, "UTF-8") + ".docx");

            // 将生成的Word文档写入响应输出流
            try (OutputStream out = response.getOutputStream()) {
                out.write(outputStream.toByteArray());
                out.flush();
                out.close();
            }
        } catch (Exception e) {
            log.error("导出Word文档并下载时发生异常：\n{}", e);
        }
    }

    @Override
    public void uploadWord(Long id, HttpServletResponse response) {
        //根据id 查询 巡检记录
        PatrolRecordDO patrolRecord = patrolRecordMapper.selectById(id);
        byte[] byteBuff = fileApi.ReaderObjects(patrolRecord.getFilePath());

        try {
            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(patrolRecord.getFileName(), StandardCharsets.UTF_8));

            // 将生成的Word文档写入响应输出流
            OutputStream out = response.getOutputStream();
            out.write(byteBuff);
            out.flush();
            out.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public void uploadWord_old(Long id, HttpServletResponse response) {
        Map<String, Object> paramMap = new HashMap<>();
        //根据巡检记录id 查询巡检计划
        PatrolRecordDO patrolRecord = patrolRecordMapper.selectById(id);

        //根据巡检记录id 查询巡检设置
        List<PatrolRecordConfigDO> patrolConfigDOs = patrolRecordMapper.selectRecordConfigByRecordId(id);
        List<PatrolInspectionConfigDO> configs = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(patrolConfigDOs)) {
            //筛选出 configId
            List<Long> configIds = patrolConfigDOs.stream().map(PatrolRecordConfigDO::getConfigId).toList();
            configs = patrolInspectionConfigMapper.selectListByIds(configIds);
        }

        if (patrolRecord == null) {
            throw new RuntimeException("巡检记录不存在");
        }
        Long planId = patrolRecord.getPlanId();
        String recordName;
        if (planId != null) {
            //根据巡检计划id 查询巡检计划和巡检设置
            PatrolPlanDO patrolPlan = patrolPlanMapper.selectById(planId);
            switch (patrolPlan.getPeriodType()) {
                case "day":
                    paramMap.put("type", "巡检日报");
                    break;
                case "week":
                    paramMap.put("type", "巡检周报");
                    break;
                case "month":
                    paramMap.put("type", "巡检月报");
                    break;
            }
            recordName = patrolPlan.getName();
        } else {
            paramMap.put("type", "巡检报告");
            recordName = "巡检报告";
        }
        //租户名称
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        //根据租户id 查询租户信息
        TenantRespDTO data = tenantApi.getTenantSimpleInfo(loginUser.getTenantId()).getData();
        paramMap.put("tenantName", data.getName());
        Date startTime = patrolRecord.getStartTime();
        String startTimeStr = DateUtil.format(startTime, "yyyy-MM-dd HH:mm:ss");
        Date endTime = patrolRecord.getEndTime();
        String endTimeStr = DateUtil.format(endTime, "yyyy-MM-dd HH:mm:ss");
        String inspectionTime = startTimeStr + "-" + endTimeStr;
        paramMap.put("inspectionTime", inspectionTime);

        //基本信息
        String createTime = DateUtil.format(patrolRecord.getCreateTime(), "yyyy-MM-dd HH:mm:ss");
        String[][] content = {
                {"报告名称", recordName, "报告生成时间", createTime},
                {"巡检项数量", String.valueOf(patrolRecord.getTotalItemCount()), "巡检资源数", String.valueOf(patrolRecord.getTotalResourceCount())},
                {"检测开始时间", startTimeStr, "检测结束时间", endTimeStr}
        };
        String[] colColors = {"C2E5F5", "FFFFFF", "C2E5F5", "FFFFFF"}; // 每列的颜色

        List<RowRenderData> rows = new ArrayList<>();
        for (int i = 0; i < content.length; i++) {
            List<CellRenderData> cells = new ArrayList<>();
            for (int j = 0; j < content[i].length; j++) {
                cells.add(Cells.of(content[i][j])
                        .bgColor(colColors[j]) // 按列索引取色
                        .create());
            }
            rows.add(Rows.of(cells.toArray(new CellRenderData[0])).create());
        }

        TableRenderData tableRenderData = Tables.of(rows.toArray(new RowRenderData[0]))
                .border(BorderStyle.DEFAULT)
                .create();

        paramMap.put("patrolRecord", tableRenderData);

        //资产信息
        //根据巡检记录ID 查询巡检记录分类
        List<PatrolResultCategoryDO> patrolRecordCategoryList = patrolResultCategoryMapper
                .selectList(new LambdaQueryWrapperX<PatrolResultCategoryDO>()
                        .eq(PatrolResultCategoryDO::getRecordId, id));

        Map<String, PatrolResultCategoryDO> resourceTypeMap = new HashMap<>();
        //获取巡检分类中的resourceType 是云主机状态，宿主机状态,云存储状态
        if (ObjectUtil.isNotEmpty(patrolRecordCategoryList)) {
            //宿主机
            Optional<PatrolResultCategoryDO> hostState = patrolRecordCategoryList.stream().filter(
                    result -> result.getMetricName().equals(PatrolMetricEnum.HOST_STATE.getCode())).findFirst();
            hostState.ifPresent(host -> {
                resourceTypeMap.put(host.getResourceType(), host);
            });
            //云主机
            Optional<PatrolResultCategoryDO> cloudHostState = patrolRecordCategoryList.stream().filter(
                    result -> result.getMetricName().equals(PatrolMetricEnum.CLOUD_STATE.getCode())).findFirst();
            cloudHostState.ifPresent(cloud -> {
                resourceTypeMap.put(cloud.getResourceType(), cloud);
            });
            //云存储
            Optional<PatrolResultCategoryDO> cloudStorageState = patrolRecordCategoryList.stream().filter(
                    result -> result.getMetricName().equals(PatrolMetricEnum.STORAGE_STATE.getCode())).findFirst();
            cloudStorageState.ifPresent(cloud -> {
                resourceTypeMap.put(cloud.getResourceType(), cloud);
            });
        }
        List<RowRenderData> resourceTypeRows = new ArrayList<>();
        RowRenderData row0 = Rows.of("组件名称", "总数", "正常状态", "其他").center().bgColor("0F9ED5").create();
        resourceTypeRows.add(row0);
        if (ObjectUtil.isNotEmpty(resourceTypeMap)) {
            final int[] index = {0};
            resourceTypeMap.forEach((resourceType, patrolResultCategoryDO) -> {
                // 根据索引判断是基数还是偶数,设置对应的背景色
                String bgColor = (index[0] % 2 == 0) ? "FFFFFF" : "CAEDFB";
                RowRenderData row = Rows.of(PatrolResourceTypeEnum.getByCode(resourceType).getName(),
                        String.valueOf(patrolResultCategoryDO.getResourceCount()),
                        String.valueOf(patrolResultCategoryDO.getNormalCount()),
                        String.valueOf(patrolResultCategoryDO.getAbnormalCount())).center().bgColor(bgColor).create();
                resourceTypeRows.add(row);
                index[0]++;
            });
        }
        TableRenderData assetInfo = Tables.of(ArrayUtil.toArray(resourceTypeRows, RowRenderData.class))
                .border(BorderStyle.builder().withColor("0F9ED5").withSize(1).withSpace(1).withType(XWPFTable.XWPFBorderType.SINGLE).build())
                .create();
        paramMap.put("assetInfo", assetInfo);

        //资源分配率
        //从数据库中获取宿主机数据
        AssetReqVO assetReqVO = new AssetReqVO();
        Long tenantId = patrolRecord.getTenantId();
        List<Long> platformIds = StrUtil.isNotEmpty(patrolRecord.getPlatformIds()) ?
                Arrays.stream(patrolRecord.getPlatformIds().split(",")).map(Long::valueOf).toList() : null;
        if (tenantId == 1L) {
            tenantId = patrolRecord.getSysSettingTenant();
        }
        assetReqVO.setTenantId(tenantId);
        assetReqVO.setPlatformIds(platformIds);
        List<HardWareRespCreateReqDTO> hostList = hostInfoApi.getHardwareByTenantOrPlatforms(assetReqVO).getData() != null ?
                hostInfoApi.getHardwareByTenantOrPlatforms(assetReqVO).getData() : new ArrayList<>();
        List<RowRenderData> fenPeiRow = new ArrayList<>();
        RowRenderData fenPeiRow0 = Rows.of("组件名称", "总量", "已使用", "占比").center().bgColor("0F9ED5").create();
        fenPeiRow.add(fenPeiRow0);
        BigDecimal totalCpu = BigDecimal.ZERO;
        BigDecimal cpuCommitRate = BigDecimal.ZERO;
        BigDecimal cpuRatio = BigDecimal.ZERO;
        BigDecimal totalVirtualMemoryGB = BigDecimal.ZERO;
        BigDecimal memoryCommitRate = BigDecimal.ZERO;
        BigDecimal memoryRatio = BigDecimal.ZERO;
        BigDecimal totalCapacity = BigDecimal.ZERO;
        BigDecimal usedCapacity = BigDecimal.ZERO;
        BigDecimal storageRatio = BigDecimal.ZERO;
        if (ObjectUtil.isNotEmpty(hostList)){
            totalCpu = new BigDecimal(hostList.stream().mapToDouble(HardWareRespCreateReqDTO::getTotalCpuCapacity).sum());
            cpuCommitRate = new BigDecimal(hostList.stream()
                    .map(host -> Optional.ofNullable(host.getCpuCommitRate()).orElse(BigDecimal.ZERO)) // 获取 BigDecimal
                    .filter(Objects::nonNull) // 过滤 null 值，避免 NullPointerException
                    .mapToDouble(BigDecimal::doubleValue) // 转换为 double
                    .sum()); // 计算总和
            cpuRatio = cpuCommitRate.divide(totalCpu, 2, RoundingMode.HALF_UP);

            totalVirtualMemoryGB = new BigDecimal(hostList.stream()
                    .map(host -> new BigDecimal(Optional.ofNullable(host.getTotalVirtualMemory()).orElse("0"))) // 转换为 BigDecimal
                    .filter(Objects::nonNull) // 过滤 null
                    .map(b -> b.divide(BigDecimal.valueOf(1024L * 1024L * 1024L), 2, RoundingMode.HALF_UP)) // B → GB
                    .mapToDouble(BigDecimal::doubleValue) // 转换为 double
                    .sum()).setScale(2, RoundingMode.HALF_UP);
            memoryCommitRate = new BigDecimal(hostList.stream()
                    .map(host -> Optional.ofNullable(host.getMemoryCommitRate()).orElse(BigDecimal.ZERO)) // 获取 BigDecimal
                    .filter(Objects::nonNull)
                    .map(b -> b.divide(BigDecimal.valueOf(1024L * 1024L * 1024L), 2, RoundingMode.HALF_UP))// 过滤 null 值，避免 NullPointerException
                    .mapToDouble(BigDecimal::doubleValue) // 转换为 double
                    .sum()).setScale(2, RoundingMode.HALF_UP);
            memoryRatio = memoryCommitRate.divide(totalVirtualMemoryGB, 2, RoundingMode.HALF_UP);
        }
        //获取云存储数据
        List<StorageRespCreateReqDTO> storageList =
                storageInfoApi.getStoragesByTenantOrPlatforms(assetReqVO).getData() != null ?
                        storageInfoApi.getStoragesByTenantOrPlatforms(assetReqVO).getData() : new ArrayList<>();
        if(ObjectUtil.isNotEmpty(storageList)){
            totalCapacity = new BigDecimal(storageList.stream().mapToDouble(StorageRespCreateReqDTO::getTotalCapacity).sum())
                    .divide(BigDecimal.valueOf(1024L * 1024L * 1024L)).setScale(2, RoundingMode.HALF_UP);
            usedCapacity = new BigDecimal(storageList.stream().mapToDouble(StorageRespCreateReqDTO::getUsedCapacity).sum())
                    .divide(BigDecimal.valueOf(1024L * 1024L * 1024L)).setScale(2, RoundingMode.HALF_UP);
            storageRatio = usedCapacity.divide(totalCapacity, 2, RoundingMode.HALF_UP);
        }
        RowRenderData row1 = Rows.of("CPU（核）",
                String.valueOf(totalCpu),
                String.valueOf(cpuCommitRate),
                String.valueOf(cpuRatio)).center().bgColor("CAEDFB").create();
        fenPeiRow.add(row1);
        RowRenderData row2 = Rows.of("内存（GB）",
                String.valueOf(totalVirtualMemoryGB),
                String.valueOf(memoryCommitRate),
                String.valueOf(memoryRatio)).center().bgColor("FFFFFF").create();
        fenPeiRow.add(row2);
        RowRenderData row3 = Rows.of("云存储（GB）",
                String.valueOf(totalCapacity),
                String.valueOf(usedCapacity),
                String.valueOf(storageRatio)).center().bgColor("CAEDFB").create();
        fenPeiRow.add(row3);
        TableRenderData fenPei_table = Tables.of(ArrayUtil.toArray(fenPeiRow, RowRenderData.class))
                .border(BorderStyle.builder().withColor("0F9ED5").withSize(1).withSpace(1).withType(XWPFTable.XWPFBorderType.SINGLE).build())
                .create();

        paramMap.put("resourceAllocation", fenPei_table);
        //资产使用率


        //存储使用情况
        //从数据库中获取存储数据

        List<RowRenderData> storageRows = new ArrayList<>();
        RowRenderData storageRow0 = Rows.of("存储名", "启用状态", "就绪状态", "物理使用率", "置备使用率", "总容量").center().bgColor("0F9ED5").create();
        storageRows.add(storageRow0);
        if (ObjectUtil.isNotEmpty(storageList)) {
            final int[] index = {0};
            storageList.forEach(storage -> {
                BigDecimal usedCapacity1 = new BigDecimal(storage.getUsedCapacity());
                BigDecimal totalCapacity1 = storage.getTotalPhysicalCapacity();
                BigDecimal virtualUtilization = usedCapacity1.compareTo(BigDecimal.ZERO) > 0 ?
                        usedCapacity1.multiply(BigDecimal.valueOf(100)).divide(totalCapacity1, 2, RoundingMode.HALF_UP) : new BigDecimal(0);
                // 根据索引判断是基数还是偶数,设置对应的背景色
                String bgColor = (index[0] % 2 == 0) ? "FFFFFF" : "CAEDFB";
                RowRenderData row = Rows.of(storage.getName(),
                        storage.getState().equals("Enabled") ? "启用" : "未启用",
                        storage.getStatus().equals("Connected") ? "已连接" : "未连接",
                        storage.getCapacityUtilization() + "%",
                        virtualUtilization + "%",
                        storage.getTotalCapacity() / 1024 / 1024 / 1024 + "GB"
                ).center().bgColor(bgColor).create();
                storageRows.add(row);
                index[0]++;
            });
        }
        TableRenderData storageInfo = Tables.of(ArrayUtil.toArray(storageRows, RowRenderData.class))
                .border(BorderStyle.builder().withColor("0F9ED5").withSize(1).withSpace(1).withType(XWPFTable.XWPFBorderType.SINGLE).build())
                .create();
        paramMap.put("storageInfo", storageInfo);

        //巡检项列表
        List<RowRenderData> itemRows = new ArrayList<>();
        RowRenderData itemRows0 = Rows.of("巡检项", "检测值", "资源数", "正常项", "异常项").center().bgColor("0F9ED5").create();
        itemRows.add(itemRows0);
        //根据巡检记录ID 查询巡检分类结果
        List<PatrolResultCategoryDO> patrolResultCategoryList = patrolResultCategoryMapper.selectList(
                new LambdaQueryWrapperX<PatrolResultCategoryDO>().eq(PatrolResultCategoryDO::getRecordId, id));
        if (ObjectUtil.isNotEmpty(patrolResultCategoryList)) {
            final List<PatrolInspectionConfigDO> finalConfigs1 = configs;
            final int[] index = {0};
            final AtomicReference<String> checkValue = new AtomicReference<>("-");
            patrolResultCategoryList.stream().forEach(result -> {
                String bgColor = (index[0] % 2 == 0) ? "FFFFFF" : "CAEDFB";
                PatrolInspectionConfigDO config = finalConfigs1.stream()
                        .filter(c -> c.getMetricName().equals(result.getMetricName())
                                && c.getThresholdType().equals("normal"))
                        .findFirst().orElse(null);
                if (config != null) {
                    if (config.getDuration() != null && config.getDuration() > 0) {
                        checkValue.set(config.getThresholdValue() + config.getUnit() +
                                ",持续" + config.getDuration() + convertDurationUnitToChinese(config.getDurationUnit()));
                    } else {
                        checkValue.set(config.getThresholdValue() + config.getUnit());
                    }
                }
                RowRenderData row = Rows.of(PatrolMetricEnum.getByCode(result.getMetricName()).getTitle(),
                        checkValue.get(),
                        String.valueOf(result.getResourceCount()),
                        String.valueOf(result.getNormalCount()),
                        String.valueOf(result.getLowRiskCount() + result.getMediumRiskCount() + result.getHighRiskCount() + result.getAbnormalCount())
                ).center().bgColor(bgColor).create();
                index[0]++;
                itemRows.add(row);
            });
        }
        TableRenderData itemInfo = Tables.of(ArrayUtil.toArray(itemRows, RowRenderData.class))
                .border(BorderStyle.builder().withColor("0F9ED5").withSize(1).withSpace(1).withType(XWPFTable.XWPFBorderType.SINGLE).build())
                .create();
        paramMap.put("itemInfo", itemInfo);

        //根据recordId 查询巡检异常明细列表
        List<PatrolAbnormalDetailDO> detail = patrolAbnormalDetailMapper.selectList(
                new LambdaQueryWrapperX<PatrolAbnormalDetailDO>().eq(PatrolAbnormalDetailDO::getRecordId, id));

        //根据resourceType 分组
        Map<String, List<PatrolAbnormalDetailDO>> detailMap = detail.stream()
                .collect(Collectors.groupingBy(PatrolAbnormalDetailDO::getResourceType));

        //云主机检测异常项
        List<RowRenderData> cloudRows = new ArrayList<>();
        RowRenderData cloudRows0 = Rows.of("巡检项", "资源名称", "平台名称", "检测结果", "详情", "建议").center().bgColor("0F9ED5").create();
        cloudRows.add(cloudRows0);
        if (ObjectUtil.isNotEmpty(detailMap)) {
            List<PatrolAbnormalDetailDO> cloudDetails = detailMap.get(PatrolResourceTypeEnum.CLOUD.getCode());
            if (ObjectUtil.isNotEmpty(cloudDetails)) {
                //根据 metricName 分组
                Map<String, List<PatrolAbnormalDetailDO>> cloudDetailMap = cloudDetails.stream()
                        .collect(Collectors.groupingBy(PatrolAbnormalDetailDO::getMetricName));
                if (ObjectUtil.isNotEmpty(cloudDetailMap)) {
                    final List<PatrolInspectionConfigDO> finalConfigs = configs;
                    final int[] index = {0};
                    cloudDetailMap.forEach((metricName, cloudDetail) -> {
                        //根据 riskLevel 分组
                        Map<String, List<PatrolAbnormalDetailDO>> cloudDetailRiskMap = cloudDetail.stream()
                                .collect(Collectors.groupingBy(PatrolAbnormalDetailDO::getRiskLevel));
                        cloudDetailRiskMap.forEach((riskLevel, cloudDetailRisk) -> {
                            String bgColor = (index[0] % 2 == 0) ? "FFFFFF" : "CAEDFB";
                            int resourceNum = cloudDetailRisk.size();
                            final AtomicReference<String> resourceDetail = new AtomicReference<>("");
                            final AtomicReference<String> suggest = new AtomicReference<>("");
                            if (PatrolMetricEnum.getByCode(metricName) == PatrolMetricEnum.CLOUD_STATE) {
                                resourceDetail.set("有" + resourceNum + "台云主机在非运行状态");
                                suggest.set("当前平台有" + resourceNum + "台" + PatrolMetricEnum.getByCode(metricName).getSuggest());
                            } else if (PatrolMetricEnum.getByCode(metricName) == PatrolMetricEnum.CLOUD_MEMORY_USAGE) {
                                resourceDetail.set("有" + resourceNum + "台云主机" + PatrolMetricEnum.getByCode(metricName).getTitle() + "在这次巡检得过程中存在突然为0的情况");
                                // 设置建议
                                String suggest1 = formatMessage(PatrolMetricEnum.getByCode(metricName).getSuggest().get(riskLevel), "0", "");
                                suggest.set("当前平台有" + resourceNum + "台" + suggest1);
                            } else {
                                finalConfigs.stream().filter(c -> c.getMetricName().equals(metricName)
                                        && c.getThresholdType().equals(riskLevel)).findFirst().ifPresent(i -> {
                                            String unit = i.getUnit();
                                            String duration = i.getDuration() != null ? String.valueOf(i.getDuration()): null;
                                            BigDecimal thresholdValue = i.getThresholdValue();
                                            if (StrUtil.isNotEmpty(duration)) {
                                                String durationUnit = convertDurationUnitToChinese(i.getDurationUnit());
                                                resourceDetail.set("有" + resourceNum + "台云主机" + PatrolMetricEnum.getByCode(metricName).getTitle() + "持续" + duration + durationUnit + "超过" + thresholdValue + unit);
                                                String suggest1 = formatMessage(PatrolMetricEnum.getByCode(metricName).getSuggest().get(riskLevel),
                                                        duration,
                                                        durationUnit,
                                                        thresholdValue.toString(),
                                                        unit);
                                                suggest.set("当前平台有" + resourceNum + "台" + suggest1);
                                            } else {
                                                resourceDetail.set("有" + resourceNum + "台云主机" + PatrolMetricEnum.getByCode(metricName).getTitle() + "超过" + thresholdValue + unit);
                                                String suggest1 = formatMessage(PatrolMetricEnum.getByCode(metricName).getSuggest().get(riskLevel),
                                                        thresholdValue.toString(),
                                                        unit);
                                                suggest.set("当前平台有" + resourceNum + "台" + suggest1);
                                            }
                                        }
                                );
                            }
                            //资源名称以/n分隔
                            String resourceName = cloudDetailRisk.stream().map(PatrolAbnormalDetailDO::getResourceName)
                                    .collect(Collectors.joining("\r\n \r\n"));
                            TextRenderData textRenderData = new TextRenderData(resourceName);
                            RowRenderData row = Rows.of(PatrolMetricEnum.getByCode(metricName).getTitle(),
                                    textRenderData.getText(),
                                    cloudDetailRisk.get(0).getPlatformName(),
                                    PatrolRiskEnum.getByCode(riskLevel).getName(),
                                    resourceDetail.get(),
                                    suggest.get()
                            ).center().bgColor(bgColor).create();
                            cloudRows.add(row);
                            index[0]++;
                        });
                    });
                }
            }
        }
        TableRenderData cloudAbnormalInfo = Tables.of(ArrayUtil.toArray(cloudRows, RowRenderData.class))
                .border(BorderStyle.builder().withColor("0F9ED5").withSize(1).withSpace(1).withType(XWPFTable.XWPFBorderType.SINGLE).build())
                .create();
        paramMap.put("cloudAbnormalInfo", cloudAbnormalInfo);
        //宿主机检测异常项

        List<RowRenderData> hostRows = new ArrayList<>();
        RowRenderData hostRows0 = Rows.of("巡检项", "资源名称", "平台名称", "检测结果", "详情", "建议").center().bgColor("0F9ED5").create();
        hostRows.add(hostRows0);
        if (ObjectUtil.isNotEmpty(detailMap)) {
            List<PatrolAbnormalDetailDO> hostDetails = detailMap.get(PatrolResourceTypeEnum.HOST.getCode());
            if (ObjectUtil.isNotEmpty(hostDetails)){
                //根据 metricName 分组
                Map<String, List<PatrolAbnormalDetailDO>> hostDetailMap = hostDetails.stream()
                        .collect(Collectors.groupingBy(PatrolAbnormalDetailDO::getMetricName));
                if (ObjectUtil.isNotEmpty(hostDetailMap)) {
                    final List<PatrolInspectionConfigDO> finalConfigs = configs;
                    final int[] index = {0};
                    hostDetailMap.forEach((metricName, hostDetail) -> {
                        //根据 riskLevel 分组
                        Map<String, List<PatrolAbnormalDetailDO>> hostDetailRiskMap = hostDetail.stream()
                                .collect(Collectors.groupingBy(PatrolAbnormalDetailDO::getRiskLevel));
                        hostDetailRiskMap.forEach((riskLevel, hsotDetailRisk) -> {
                            String bgColor = (index[0] % 2 == 0) ? "FFFFFF" : "CAEDFB";
                            int resourceNum = hsotDetailRisk.size();
                            final AtomicReference<String> resourceDetail = new AtomicReference<>("");
                            final AtomicReference<String> suggest = new AtomicReference<>("");
                            if (PatrolMetricEnum.getByCode(metricName) == PatrolMetricEnum.HOST_STATE) {
                                resourceDetail.set("有" + resourceNum + "台宿主机在非运行状态");
                                suggest.set("当前平台有" + resourceNum + "台" + PatrolMetricEnum.getByCode(metricName).getSuggest());
                            } else {
                                finalConfigs.stream().filter(c -> c.getMetricName().equals(metricName)
                                        && c.getThresholdType().equals(riskLevel)).findFirst().ifPresent(i -> {
                                            String unit = i.getUnit();
                                            String duration = i.getDuration() != null ? String.valueOf(i.getDuration()): null;
                                            BigDecimal thresholdValue = i.getThresholdValue();
                                            if (StrUtil.isNotEmpty(duration)) {
                                                String durationUnit = convertDurationUnitToChinese(i.getDurationUnit());
                                                resourceDetail.set("有" + resourceNum + "台宿主机" + PatrolMetricEnum.getByCode(metricName).getTitle() + "持续" + duration + durationUnit + "超过" + thresholdValue + unit);
                                                String suggest1 = formatMessage(PatrolMetricEnum.getByCode(metricName).getSuggest().get(riskLevel),
                                                        duration,
                                                        durationUnit,
                                                        thresholdValue.toString(),
                                                        unit);
                                                suggest.set("当前平台有" + resourceNum + "台" + suggest1);
                                            } else {
                                                resourceDetail.set("有" + resourceNum + "台宿主机" + PatrolMetricEnum.getByCode(metricName).getTitle() + "超过" + thresholdValue + unit);
                                                String suggest1 = formatMessage(PatrolMetricEnum.getByCode(metricName).getSuggest().get(riskLevel),
                                                        thresholdValue.toString(),
                                                        unit);
                                                suggest.set("当前平台有" + resourceNum + "台" + suggest1);
                                            }
                                        }
                                );
                            }
                            //资源名称以\n分隔
                            String resourceName = hsotDetailRisk.stream().map(PatrolAbnormalDetailDO::getResourceName)
                                    .collect(Collectors.joining("\r\n \r\n"));
                            TextRenderData textRenderData = new TextRenderData(resourceName);
                            RowRenderData row = Rows.of(PatrolMetricEnum.getByCode(metricName).getTitle(),
                                    textRenderData.getText(),
                                    hsotDetailRisk.get(0).getPlatformName(),
                                    PatrolRiskEnum.getByCode(riskLevel).getName(),
                                    resourceDetail.get(),
                                    suggest.get()
                            ).center().bgColor(bgColor).create();
                            hostRows.add(row);
                            index[0]++;
                        });
                    });
                }
            }
        }
        TableRenderData hostAbnormalInfo = Tables.of(ArrayUtil.toArray(hostRows, RowRenderData.class))
                .border(BorderStyle.builder().withColor("0F9ED5").withSize(1).withSpace(1).withType(XWPFTable.XWPFBorderType.SINGLE).build())
                .create();
        paramMap.put("hostAbnormalInfo", hostAbnormalInfo);
        //云存储检测异常项
        List<RowRenderData> storageErrorRows = new ArrayList<>();
        RowRenderData storageErrorRows0 = Rows.of("巡检项", "资源名称", "平台名称", "检测结果", "详情", "建议").center().bgColor("0F9ED5").create();
        storageErrorRows.add(storageErrorRows0);
        if (ObjectUtil.isNotEmpty(detailMap)) {
            List<PatrolAbnormalDetailDO> storageDetails = detailMap.get(PatrolResourceTypeEnum.STORAGE.getCode());
            if (ObjectUtil.isNotEmpty(storageDetails)){
                //根据 metricName 分组
                Map<String, List<PatrolAbnormalDetailDO>> storageDetailMap = storageDetails.stream()
                        .collect(Collectors.groupingBy(PatrolAbnormalDetailDO::getMetricName));
                if (ObjectUtil.isNotEmpty(storageDetailMap)) {
                    final List<PatrolInspectionConfigDO> finalConfigs = configs;
                    final int[] index = {0};
                    storageDetailMap.forEach((metricName, storageDetail) -> {
                        //根据 riskLevel 分组
                        Map<String, List<PatrolAbnormalDetailDO>> storageDetailRiskMap = storageDetail.stream()
                                .collect(Collectors.groupingBy(PatrolAbnormalDetailDO::getRiskLevel));
                        storageDetailRiskMap.forEach((riskLevel, storageDetailRisk) -> {
                            String bgColor = (index[0] % 2 == 0) ? "FFFFFF" : "CAEDFB";
                            int resourceNum = storageDetailRisk.size();
                            final AtomicReference<String> resourceDetail = new AtomicReference<>("");
                            final AtomicReference<String> suggest = new AtomicReference<>("");
                            if (PatrolMetricEnum.getByCode(metricName) == PatrolMetricEnum.STORAGE_STATE) {
                                resourceDetail.set("有" + resourceNum + "台云存储在非运行状态");
                                suggest.set("当前平台有" + resourceNum + "台" + PatrolMetricEnum.getByCode(metricName).getSuggest());
                            } else {
                                finalConfigs.stream().filter(c -> c.getMetricName().equals(metricName)
                                        && c.getThresholdType().equals(riskLevel)).findFirst().ifPresent(i -> {
                                            String unit = i.getUnit();
                                            String duration = i.getDuration() != null ? String.valueOf(i.getDuration()): null;
                                            BigDecimal thresholdValue = i.getThresholdValue();
                                            if (StrUtil.isNotEmpty(duration)) {
                                                String durationUnit = convertDurationUnitToChinese(i.getDurationUnit());
                                                resourceDetail.set("有" + resourceNum + "台云存储" + PatrolMetricEnum.getByCode(metricName).getTitle() + "持续" + duration + durationUnit + "超过" + thresholdValue + unit);
                                                String suggest1 = formatMessage(PatrolMetricEnum.getByCode(metricName).getSuggest().get(riskLevel),
                                                        duration,
                                                        durationUnit,
                                                        thresholdValue.toString(),
                                                        unit);
                                                suggest.set("当前平台有" + resourceNum + "台" + suggest1);
                                            } else {
                                                resourceDetail.set("有" + resourceNum + "台云存储" + PatrolMetricEnum.getByCode(metricName).getTitle() + "超过" + thresholdValue + unit);
                                                String suggest1 = formatMessage(PatrolMetricEnum.getByCode(metricName).getSuggest().get(riskLevel),
                                                        thresholdValue.toString(),
                                                        unit);
                                                suggest.set("当前平台有" + resourceNum + "台" + suggest1);
                                            }
                                        }
                                );
                            }
                            //资源名称以/n分隔
                            String resourceName = storageDetailRisk.stream().map(PatrolAbnormalDetailDO::getResourceName)
                                    .collect(Collectors.joining("\r\n \r\n"));
                            TextRenderData textRenderData = new TextRenderData(resourceName);
                            RowRenderData row = Rows.of(PatrolMetricEnum.getByCode(metricName).getTitle(),
                                    textRenderData.getText(),
                                    storageDetailRisk.get(0).getPlatformName(),
                                    PatrolRiskEnum.getByCode(riskLevel).getName(),
                                    resourceDetail.get(),
                                    suggest.get()
                            ).center().bgColor(bgColor).create();
                            storageErrorRows.add(row);
                            index[0]++;
                        });
                    });
                }
            }
        }
        TableRenderData storageAbnormalInfo = Tables.of(ArrayUtil.toArray(storageErrorRows, RowRenderData.class))
                .border(BorderStyle.builder().withColor("0F9ED5").withSize(1).withSpace(1).withType(XWPFTable.XWPFBorderType.SINGLE).build())
                .create();
        paramMap.put("storageAbnormalInfo", storageAbnormalInfo);

        createAndDownloadWord(paramMap, response, recordName, createTime);
    }

    private String convertDurationUnitToChinese(String durationUnit) {
        return switch (durationUnit) {
            case "d" -> "天";
            case "h" -> "小时";
            case "m" -> "分钟";
            case "s" -> "秒";
            default -> durationUnit;
        };
    }

    /**
     * 格式化消息，替换{}占位符
     *
     * @param pattern 包含{}占位符的模式字符串
     * @param args    要替换占位符的参数
     * @return 格式化后的字符串
     */
    protected String formatMessage(String pattern, Object... args) {
        if (pattern == null) {
            return "";
        }

        StringBuilder result = new StringBuilder(pattern);
        for (Object arg : args) {
            int index = result.indexOf("{}");
            if (index >= 0) {
                result.replace(index, index + 2, arg == null ? "null" : arg.toString());
            } else {
                break;
            }
        }

        return result.toString();
    }

}
