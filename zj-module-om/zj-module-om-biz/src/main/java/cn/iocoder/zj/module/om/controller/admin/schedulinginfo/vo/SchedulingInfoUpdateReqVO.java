package cn.iocoder.zj.module.om.controller.admin.schedulinginfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 运维排班信息更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SchedulingInfoUpdateReqVO extends SchedulingInfoBaseVO {

    @Schema(description = "主键id", required = true)
    @NotNull(message = "主键id不能为空")
    private Long id;

}
