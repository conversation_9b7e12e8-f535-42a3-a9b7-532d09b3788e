package cn.iocoder.zj.module.om.controller.admin.workorder.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 工单管理数据分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WorkOrderPageReqVO extends PageParam {

    @Schema(description = "名称")
    private String name;

    @Schema(description = "项目（平台资源）")
    private String sourceName;

    @Schema(description = "项目ID（资源Id）")
    private String sourceUuid;

    @Schema(description = "简介")
    private String description;

    @Schema(description = "处理人ID")
    private Long auditorId;

    @Schema(description = "处理人名称")
    private String auditorName;

    @Schema(description = "创建者Id")
    private Long creator;

    @Schema(description = "实施人ID")
    private Long enforcerId;

    @Schema(description = "实施人名称")
    private String enforcerName;

    @Schema(description = "审批状态，consent-unresolved通过未解决，reject驳回，solved解决，wait待审核")
    private String ratifyStatus;

    @Schema(description = "准备状态，valid有效的，invalid无效")
    private String readinessStatus;

    @Schema(description = "查询字段")
    private String query;

    @Schema(description = "资源类型字典项code")
    private String dictCode;

    @Schema(description = "工单类型对应的字典编码")
    private String typeCode;

    @Schema(description = "工单类型对应的字典名称")
    private String typeName;


    @Schema(description = "平台id集合，不传")
    private List platformIds;

    @Schema(description = "开始实施时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date enforcerStartTime;

    @Schema(description = "结束实施时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date enforcerEndTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date createTime;

    @Schema(description = "修改时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date updateTime;

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String startTime;

    @Schema(description = "结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String endTime;

}
