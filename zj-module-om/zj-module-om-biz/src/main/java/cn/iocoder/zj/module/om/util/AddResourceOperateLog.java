package cn.iocoder.zj.module.om.util;

import cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo.ResourceOperateLog;
import cn.iocoder.zj.module.om.service.assetmanagement.AssetManagementService;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;

import javax.annotation.Resource;

public class AddResourceOperateLog {
    @Resource
    AssetManagementService assetManagementService;
//    public static void createOperateLog(String name, String uuid, String operation, String result, String resourceType,String msg ,PlatformconfigDTO platform){
//        ResourceOperateLog operateLog = new ResourceOperateLog();
//        operateLog.setOperation(operation);
//        operateLog.setResourceType(resourceType);
//        operateLog.setResult(result);
//        operateLog.setResourceUuid(uuid);
//        operateLog.setResourceName(name);
//        operateLog.setPlatformName(platform.getName());
//        operateLog.setPlatformId(platform.getId());
//        operateLog.setContent(msg);
//        assetManagementService.createOperateLog(operateLog);
//    }
}
