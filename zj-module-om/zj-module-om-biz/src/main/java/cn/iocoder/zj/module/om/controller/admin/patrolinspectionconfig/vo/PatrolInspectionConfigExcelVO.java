package cn.iocoder.zj.module.om.controller.admin.patrolinspectionconfig.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 巡检设置 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class PatrolInspectionConfigExcelVO {

    @ExcelProperty("主键ID")
    private Long id;

    @ExcelProperty("租户ID")
    private Long tenantId;

    @ExcelProperty("资源类型(存储资源/宿主机资源/云主机资源/云硬盘资源)")
    private String resourceType;

    @ExcelProperty("指标名称(CPU分配/内存分配/网卡入速度等)")
    private String metricName;

    @ExcelProperty("阈值类型(正常/低风险/中风险/高风险/异常)")
    private String thresholdType;

    @ExcelProperty("比较运算符(>, <, >=, <=, =, !=, between)',")
    private String operator;

    @ExcelProperty("阈值值")
    private BigDecimal thresholdValue;

    @ExcelProperty("阈值最大值(用于范围比较)")
    private BigDecimal thresholdValueMax;

    @ExcelProperty("单位(%, MB/S, G等)")
    private String unit;

    @ExcelProperty("持续时间")
    private Integer duration;

    @ExcelProperty("持续时间单位(分钟/小时/天)")
    private String durationUnit;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
