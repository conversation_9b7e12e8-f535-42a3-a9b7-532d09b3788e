package cn.iocoder.zj.module.om.controller.admin.patrolabnormaldetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 巡检异常明细创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PatrolAbnormalDetailCreateReqVO extends PatrolAbnormalDetailBaseVO {

}
