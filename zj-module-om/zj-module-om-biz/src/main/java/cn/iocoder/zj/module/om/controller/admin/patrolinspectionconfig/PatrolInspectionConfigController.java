package cn.iocoder.zj.module.om.controller.admin.patrolinspectionconfig;

import cn.iocoder.cloud.framework.warehouse.service.MetricsDataService;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.warehouse.service.ZjMetricsDataService;
import cn.iocoder.zj.module.om.controller.admin.patrolinspectionconfig.vo.*;
import cn.iocoder.zj.module.om.convert.patrolinspectionconfig.PatrolInspectionConfigConvert;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspectionconfig.PatrolInspectionConfigDO;
import cn.iocoder.zj.module.om.service.patrolinspectionconfig.PatrolInspectionConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.hertzbeat.common.entity.dto.MetricsHistoryData;
import org.apache.hertzbeat.common.entity.dto.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

@Tag(name = "管理后台 - 巡检设置")
@RestController
@RequestMapping("/om/patrol-inspection-config")
@Validated
public class PatrolInspectionConfigController {

    @Resource
    private PatrolInspectionConfigService patrolInspectionConfigService;

    @Resource
    private MetricsDataService metricsDataService;

    @Resource
    private ZjMetricsDataService zjMetricsDataService;

    @PostMapping("/create")
    @Operation(summary = "创建巡检设置")
    @PreAuthorize("@ss.hasPermission('om:patrol-inspection-config:create')")
    public CommonResult<List<Long>> createPatrolInspectionConfig(@Valid @RequestBody List<PatrolInspectionConfigCreateReqVO> createReqVOS) {
        return success(patrolInspectionConfigService.createPatrolInspectionConfigs(createReqVOS));
    }

    @PutMapping("/update")
    @Operation(summary = "更新巡检设置")
    @PreAuthorize("@ss.hasPermission('om:patrol-inspection-config:update')")
    public CommonResult<Boolean> updatePatrolInspectionConfig(@Valid @RequestBody List<PatrolInspectionConfigUpdateReqVO> updateReqVOs) {
        patrolInspectionConfigService.updatePatrolInspectionConfigs(updateReqVOs);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除巡检设置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('om:patrol-inspection-config:delete')")
    public CommonResult<Boolean> deletePatrolInspectionConfig(@RequestParam("id") Long id) {
        patrolInspectionConfigService.deletePatrolInspectionConfig(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得巡检设置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('om:patrol-inspection-config:query')")
    public CommonResult<PatrolInspectionConfigRespVO> getPatrolInspectionConfig(@RequestParam("id") Long id) {
        PatrolInspectionConfigDO patrolInspectionConfig = patrolInspectionConfigService.getPatrolInspectionConfig(id);
        return success(PatrolInspectionConfigConvert.INSTANCE.convert(patrolInspectionConfig));
    }

    @GetMapping("/list")
    @Operation(summary = "获得巡检设置列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('om:patrol-inspection-config:query')")
    public CommonResult<List<PatrolInspectionConfigRespVO>> getPatrolInspectionConfigList(@RequestParam("ids") Collection<Long> ids) {
        List<PatrolInspectionConfigDO> list = patrolInspectionConfigService.getPatrolInspectionConfigList(ids);
        return success(PatrolInspectionConfigConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/listByTenantIdOrPlanId")
    @Operation(summary = "根据租户Id或者计划Id获得巡检设置")
    @PreAuthorize("@ss.hasPermission('om:patrol-inspection-config:query')")
    @Parameter(name = "tenantId", description = "租户Id", required = true, example = "1024")
    @Parameter(name = "planId", description = "计划Id", required = false, example = "1024")
    public CommonResult<List<PatrolInspectionConfigRespVO>> listByTenantIdOrPlanId(@RequestParam("tenantId") Long tenantId,
                                                                                   @RequestParam(value = "planId", required = false) Long planId) {
        List<PatrolInspectionConfigDO> list = patrolInspectionConfigService.listByTenantIdOrPlanId(tenantId, planId);
        return success(PatrolInspectionConfigConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得巡检设置分页")
    @PreAuthorize("@ss.hasPermission('om:patrol-inspection-config:query')")
    public CommonResult<PageResult<PatrolInspectionConfigRespVO>> getPatrolInspectionConfigPage(@Valid PatrolInspectionConfigPageReqVO pageVO) {
        PageResult<PatrolInspectionConfigDO> pageResult = patrolInspectionConfigService.getPatrolInspectionConfigPage(pageVO);
        return success(PatrolInspectionConfigConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出巡检设置 Excel")
    @PreAuthorize("@ss.hasPermission('om:patrol-inspection-config:export')")
    @OperateLog(type = EXPORT)
    public void exportPatrolInspectionConfigExcel(@Valid PatrolInspectionConfigExportReqVO exportReqVO,
                                                  HttpServletResponse response) throws IOException {
        List<PatrolInspectionConfigDO> list = patrolInspectionConfigService.getPatrolInspectionConfigList(exportReqVO);
        // 导出 Excel
        List<PatrolInspectionConfigExcelVO> datas = PatrolInspectionConfigConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "巡检设置.xls", "数据", PatrolInspectionConfigExcelVO.class, datas);
    }

    @PostMapping("/execute")
    @Operation(summary = "执行巡检")
    @PreAuthorize("@ss.hasPermission('om:patrol-inspection-config:execute')")
    public CommonResult<Boolean> executePatrol(PatrolExecuteReqVO reqVO) {
        patrolInspectionConfigService.executePatrol(reqVO);
        return success(true);
    }

    /**
     * 停止巡检计划
     *
     * @return
     */
    @PostMapping("/stop")
    @Operation(summary = "停止巡检计划")
    @PreAuthorize("@ss.hasPermission('om:patrol-inspection-config:execute')")
    public CommonResult<Boolean> stopPatrol() {
        //查询当前租户id
        Long tenantId = SecurityFrameworkUtils.getLoginUser().getTenantId();
        patrolInspectionConfigService.stopPatrol(tenantId);
        return success(true);
    }

    @GetMapping("/getComplete")
    @Operation(summary = "获取巡检完成状态")
    @PreAuthorize("@ss.hasPermission('om:patrol-inspection-config:execute')")
    public CommonResult<Boolean> getComplete(@RequestParam("tenantId") Long tenantId) {
        //查询当前租户id
        return success(patrolInspectionConfigService.getComplete(tenantId));
    }

    @GetMapping("/testMetricsData")
    public void testMetricsData() {
//        String uuid = "c02838ca-fe40-4c77-86a8-50feb4955b92";
        Long uuid = 524004220171008L;
        String metrics = "top_cpu_process";
        String metric = "cpu_usage";
        String app = "linux";
        LocalDateTime segmentEnd = LocalDateTime.now();
        LocalDateTime segmentStart = segmentEnd.minusDays(1);

        // 转换为时间戳（毫秒）
//        long startTimestamp = segmentStart.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
//        long endTimestamp = segmentEnd.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        // 转换为时间戳（秒）
        long startTimestamp = segmentStart.atZone(ZoneId.systemDefault()).toEpochSecond();
        long endTimestamp = segmentEnd.atZone(ZoneId.systemDefault()).toEpochSecond();
//        String queryDuration = "7d";
        String queryDuration = "7d";
        String stepQry = "7d";
        Map<String, List<Value>> values = new HashMap<>();
        MetricsHistoryData segmentData = metricsDataService.getHistoryIntervalMetricData(uuid, app,
                metrics, metric, "", queryDuration, null, null, stepQry, "");
        Map<String, List<Value>> maxValues = segmentData.getValues();

//        MetricHistoryData metricHistoryData = zjMetricsDataService.getMetricHistoryData(uuid, metrics
//                , metric, null, startTimestamp, endTimestamp, stepQry, "max");
//        Map<String, List<Value>> maxValues = metricHistoryData.getValues();
//
//        MetricHistoryData metricHistoryData1 = zjMetricsDataService.getMetricHistoryData(uuid, metrics
//                , metric, null, startTimestamp, endTimestamp, stepQry, "min");
//        Map<String, List<Value>> minValues =  metricHistoryData1.getValues();
//

        // 合并数据
        if (segmentData != null && segmentData.getValues() != null) {
            for (Map.Entry<String, List<Value>> entry : segmentData.getValues().entrySet()) {
                String key = entry.getKey();
                List<Value> segmentValues = entry.getValue();

                if (segmentValues != null && !segmentValues.isEmpty()) {
                    values.computeIfAbsent(key, k -> new ArrayList<>()).addAll(segmentValues);
                }
            }
        }
        // 对合并后的数据按时间戳排序
        for (List<Value> valueList : values.values()) {
            valueList.sort(Comparator.comparing(Value::getTime));
        }

        System.out.println(values);
    }
}
