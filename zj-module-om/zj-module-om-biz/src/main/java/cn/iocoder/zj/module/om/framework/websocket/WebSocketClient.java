package cn.iocoder.zj.module.om.framework.websocket;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@ServerEndpoint("/websocket-server")
@Component
@Slf4j
public class WebSocketClient {

    // 记录连接的客户端
    public static Map<String, Session> clients = new ConcurrentHashMap<>();

    /**
     * userId 关联 sid（解决同一用户 id，在多个 web 端连接的问题）
     */
    public static Map<String, Set<String>> conns = new ConcurrentHashMap<>();

    @Resource
    private StringRedisTemplate stringRedisTemplate= SpringUtils.getBean(StringRedisTemplate.class);


    /**
     * 当客户端连接时触发
     */
    @OnOpen
    public void onOpen(Session session) {
        Map<String, List<String>> params = session.getRequestParameterMap();
        String uuid = params.get("uuid").get(0);
        String sessionId = session.getId();

        if (uuid != null) {
            // 记录客户端连接
            clients.put(sessionId, session);
            conns.computeIfAbsent(uuid, k -> ConcurrentHashMap.newKeySet()).add(sessionId);

            log.info("用户 {} 连接成功，Session ID: {}", uuid, sessionId);

            // 启动线程轮询 Redis 队列，获取消息
            new Thread(() -> {
                pollMessageFromRedis(uuid);
            }).start();
        } else {
            log.error("参数 userId 未传递");
        }
    }

    /**
     * 当客户端断开连接时触发
     */
    @OnClose
    public void onClose(Session session) {
        Map<String, List<String>> params = session.getRequestParameterMap();
        String uuid = params.get("uuid").get(0);
        String sessionId = session.getId();

        if (uuid != null) {
            // 移除客户端连接
            clients.remove(sessionId);
            Set<String> clientSet = conns.get(uuid);
            if (clientSet != null) {
                clientSet.remove(sessionId);
                if (clientSet.isEmpty()) {
                    conns.remove(uuid);
                }
            }

            log.info("用户 {} 断开连接，Session ID: {}", uuid, sessionId);
        }
    }

    public void onClose(Long userId) {
        Set<String> clientSet = conns.get(userId);
        if (clientSet != null) {
            Iterator<String> iterator = new HashSet<>(clientSet).iterator();
            while (iterator.hasNext()) {
                closeSession(iterator.next(), userId);
            }
        }
    }

    public void closeSession(String sid, Long userId) {
        Session s = clients.remove(sid);
        if (s != null) {
            try {
                s.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        Set<String> clientSet = conns.get(userId);
        if (clientSet != null) {
            clientSet.remove(sid);
            int currentConnectCount = clientSet.size();
            if (currentConnectCount == 0) {
                conns.remove(userId);
            }
            log.error("用户(id:{})已经断开连接", userId);
            log.error("DeviceWebsocket---onClose===>id:--{}--{}--连接数：{}--在线数：{}--当前设备连接数：{}", userId, sid, clients.size(), conns.size(), currentConnectCount);
        }
    }

    /**
     * 当 WebSocket 发生错误时触发
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("WebSocket 发生错误: {}", error.getMessage());
        error.printStackTrace();
    }

    /**
     * 轮询 Redis 队列，给单个用户发送消息
     *
     * @param uuid 用户 ID
     */
    public void pollMessageFromRedis(String uuid) {
        String redisKey = "wsuser:" + uuid;
        while (true) {
            try {
                // 从 Redis 集合中随机获取一个消息
                Set<String> messages = stringRedisTemplate.opsForSet().members(redisKey);
                if (messages != null && !messages.isEmpty()) {
                    // 随机选择一个消息
                    String message = messages.iterator().next();
                    // 从集合中移除该消息
                    stringRedisTemplate.opsForSet().remove(redisKey, message);
                    log.info("取出消息 {}", message);
                    // 发送消息给客户端
                    sendMessageToUser(uuid, message);
                }
                // 短暂休眠，避免过高的 CPU 使用率
                Thread.sleep(100);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("轮询消息时发生错误: {}", e.getMessage());
            }
        }
    }

    /**
     * 将消息推送到 Redis 列表中
     *
     * @param userId  用户 ID
     * @param message 要发送的消息
     */
    public void sendMessage(String userId, String message) {
        // 使用用户 ID 创建 Redis 集合的键
        String redisKey = "wsuser:" + userId;
        // 将消息推送到 Redis 集合
        stringRedisTemplate.opsForSet().add(redisKey, message);
        log.info("将消息推送到 Redis 集合 {}", redisKey);
    }

    /**
     * 发送消息给指定用户的 WebSocket 客户端
     *
     * @param userId  用户 ID
     * @param message 消息内容
     */
    public void sendMessageToUser(String userId, String message) {
        Set<String> clientSet = conns.get(userId);
        if (clientSet != null) {
            for (String sid : clientSet) {
                Session session = clients.get(sid);
                if (session != null) {
                    try {
                        session.getBasicRemote().sendText(message);
                    } catch (IOException e) {
                        log.error("消息发送失败，错误信息: {}", e.getMessage());
                    }
                }
            }
        }
    }

    /**
     * 广播消息给所有 WebSocket 客户端
     */
    public static void broadcastMessage(String message) {
        for (Session session : clients.values()) {
            try {
                session.getBasicRemote().sendText(message);
            } catch (IOException e) {
                log.error("广播消息发送失败: {}", e.getMessage());
            }
        }
    }
}