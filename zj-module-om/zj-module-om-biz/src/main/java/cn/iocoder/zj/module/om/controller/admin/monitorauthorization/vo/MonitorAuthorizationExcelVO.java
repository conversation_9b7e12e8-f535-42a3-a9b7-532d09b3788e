package cn.iocoder.zj.module.om.controller.admin.monitorauthorization.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 监控申请授权 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class MonitorAuthorizationExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("资产id")
    private String assetId;

    @ExcelProperty("资产名称")
    private String assetName;

    @ExcelProperty("申请人")
    private String nickName;

    @ExcelProperty("主机地址")
    private String hostName;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("授权有效时间")
    private LocalDateTime authorizationTime;

    @ExcelProperty("申请人id")
    private Long userId;

}
