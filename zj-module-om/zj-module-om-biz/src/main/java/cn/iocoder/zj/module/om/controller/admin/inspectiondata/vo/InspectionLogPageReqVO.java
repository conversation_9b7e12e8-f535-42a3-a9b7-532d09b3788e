package cn.iocoder.zj.module.om.controller.admin.inspectiondata.vo;

import lombok.*;

import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 巡检记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InspectionLogPageReqVO extends PageParam {

    @Schema(description = "巡检ID")
    private Long inspectionId;

    @Schema(description = "巡检记录uuid",required = true)
    private String recordUuid;

    @Schema(description = "巡检名称")
    private String inspectionName;

    @Schema(description = "设备名称")
    private String assetName;

    @Schema(description = "阈值")
    private BigDecimal threshold;

    @Schema(description = "检测结果")
    private String result;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "资产类型")
    private String assetType;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
