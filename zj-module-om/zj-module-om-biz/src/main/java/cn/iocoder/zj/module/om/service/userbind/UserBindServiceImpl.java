package cn.iocoder.zj.module.om.service.userbind;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.UserBindDTO;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.query.QueryWrapperX;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.om.controller.admin.userbind.vo.UserBindCreateReqVO;
import cn.iocoder.zj.module.om.controller.admin.userbind.vo.UserBindExportReqVO;
import cn.iocoder.zj.module.om.controller.admin.userbind.vo.UserBindPageReqVO;
import cn.iocoder.zj.module.om.controller.admin.userbind.vo.UserBindUpdateReqVO;
import cn.iocoder.zj.module.om.convert.userbind.UserBindConvert;
import cn.iocoder.zj.module.om.dal.dataobject.userbind.UserBindDO;
import cn.iocoder.zj.module.om.dal.mysql.userbind.UserBindMapper;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.bpm.enums.ErrorCodeConstants.MODEL_NOT_EXISTS;
import static cn.iocoder.zj.module.om.dal.redis.RedisKeyConstants.APP_KEY_SECRET;

/**
 * 用户推送绑定 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UserBindServiceImpl implements UserBindService {

    @Resource
    private UserBindMapper userBindMapper;

    @Resource
    private RedisTemplate redisTemplate;

    @Override
    public Long createUserBind(UserBindCreateReqVO createReqVO) {
        // 插入
        UserBindDO userBind = UserBindConvert.INSTANCE.convert(createReqVO);
        userBind.setCreateTime(DateUtil.toLocalDateTime(new Date()));
        userBind.setUpdateTime(DateUtil.toLocalDateTime(new Date()));
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        userBind.setUserId(loginUser.getId());
        userBindMapper.insert(userBind);
        String app = formatKey();
        redisTemplate.opsForValue().set(app, JsonUtils.toJsonString(BeanUtil.copyProperties(userBind, UserBindDTO.class)));
        // 返回
        return userBind.getId();
    }

    @Override
    public void updateUserBind(UserBindUpdateReqVO updateReqVO) {
        // 校验存在
        validateUserBindExists(updateReqVO.getId());
        // 更新
        UserBindDO updateObj = BeanUtil.copyProperties(updateReqVO, UserBindDO.class);
        userBindMapper.updateById(updateObj);
        String app = formatKey();
        UserBindDO userBindDO = userBindMapper.selectById(updateReqVO.getId());
        if(redisTemplate.hasKey(app)){
            redisTemplate.delete(app);
        }
        redisTemplate.opsForValue().set(app, JsonUtils.toJsonString(BeanUtil.copyProperties(userBindDO, UserBindDTO.class)));
    }

    @Override
    public void deleteUserBind(Long id) {
        // 校验存在
        validateUserBindExists(id);
        // 删除
        userBindMapper.deleteById(id);
        String app = formatKey();
        if(redisTemplate.hasKey(app)){
            redisTemplate.delete(app);
        }
    }

    private void validateUserBindExists(Long id) {
        if (userBindMapper.selectById(id) == null) {
            throw exception(MODEL_NOT_EXISTS);
        }
    }

    @Override
    public UserBindDO getUserBind(Long id) {
        return userBindMapper.selectById(id);
    }

    @Override
    public List<UserBindDO> getUserBindList(Collection<Long> ids) {
        return userBindMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<UserBindDO> getUserBindPage(UserBindPageReqVO pageReqVO) {
        return userBindMapper.selectPage(pageReqVO);
    }

    @Override
    public List<UserBindDO> getUserBindList(UserBindExportReqVO exportReqVO) {
        return userBindMapper.selectList(exportReqVO);
    }

    @Override
    public UserBindDO getByUserId(Long userId) {
        UserBindDO userBindDO = userBindMapper.selectOne(new QueryWrapperX<UserBindDO>()
                .orderByDesc("update_time")
                .last("limit 1"));
        return userBindDO;
    }

    @Override
    public List<UserBindDO> getUserBingList(List<Long> userIds) {
        LambdaQueryWrapperX<UserBindDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.in(UserBindDO::getUserId, userIds);
        return userBindMapper.selectList(queryWrapperX);
    }

    private static String formatKey() {
        return String.format(APP_KEY_SECRET);
    }

}
