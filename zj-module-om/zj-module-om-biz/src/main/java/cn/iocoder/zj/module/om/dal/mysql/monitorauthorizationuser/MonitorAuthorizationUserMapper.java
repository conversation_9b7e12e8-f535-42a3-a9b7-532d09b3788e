package cn.iocoder.zj.module.om.dal.mysql.monitorauthorizationuser;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.om.dal.dataobject.monitorauthorizationuser.MonitorAuthorizationUserDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.om.controller.admin.monitorauthorizationuser.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 用户资产授权申请 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MonitorAuthorizationUserMapper extends BaseMapperX<MonitorAuthorizationUserDO> {

    default PageResult<MonitorAuthorizationUserDO> selectPage(MonitorAuthorizationUserPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MonitorAuthorizationUserDO>()
                .eqIfPresent(MonitorAuthorizationUserDO::getMonitorAssetId, reqVO.getMonitorAssetId())
                .eqIfPresent(MonitorAuthorizationUserDO::getAuthorizationUserId, reqVO.getAuthorizationUserId())
                .eqIfPresent(MonitorAuthorizationUserDO::getAuthorizationType, reqVO.getAuthorizationType())
                .betweenIfPresent(MonitorAuthorizationUserDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MonitorAuthorizationUserDO::getId));
    }

    default List<MonitorAuthorizationUserDO> selectList(MonitorAuthorizationUserExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MonitorAuthorizationUserDO>()
                .eqIfPresent(MonitorAuthorizationUserDO::getMonitorAssetId, reqVO.getMonitorAssetId())
                .eqIfPresent(MonitorAuthorizationUserDO::getAuthorizationUserId, reqVO.getAuthorizationUserId())
                .eqIfPresent(MonitorAuthorizationUserDO::getAuthorizationType, reqVO.getAuthorizationType())
                .betweenIfPresent(MonitorAuthorizationUserDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MonitorAuthorizationUserDO::getId));
    }

    void deleteAuthorizationUserType(@Param("monitorAssetId") Long monitorAssetId, @Param("userId") Long userId);

    void updateAuthorizationUserType(@Param("monitorAssetId") Long monitorAssetId, @Param("userId") Long userId);

    List<MonitorAuthorizationUserDO>  selectTypeByUserId(@Param("userId") Long userId);
}
