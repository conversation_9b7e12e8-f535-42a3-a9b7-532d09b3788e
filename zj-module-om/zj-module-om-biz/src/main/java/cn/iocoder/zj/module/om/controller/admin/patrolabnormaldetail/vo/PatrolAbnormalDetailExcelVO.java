package cn.iocoder.zj.module.om.controller.admin.patrolabnormaldetail.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 巡检异常明细 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class PatrolAbnormalDetailExcelVO {

    @ExcelProperty("主键ID")
    private Long id;

    @ExcelProperty("巡检记录ID")
    private Long recordId;

    @ExcelProperty("巡检结果分类ID")
    private Long categoryId;

    @ExcelProperty("资源类型(存储资源/宿主机资源/云主机资源/云硬盘资源等)")
    private String resourceType;

    @ExcelProperty("指标名称(CPU分配/内存分配/网卡入速度等)")
    private String metricName;

    @ExcelProperty("资源ID")
    private String resourceId;

    @ExcelProperty("资源名称")
    private String resourceName;

    @ExcelProperty("平台ID")
    private String platformId;

    @ExcelProperty("平台名称")
    private String platformName;

    @ExcelProperty("详情")
    private String detail;

    @ExcelProperty("风险等级(正常/低风险/中风险/高风险)")
    private String riskLevel;

    @ExcelProperty("建议")
    private String suggest;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
