package cn.iocoder.zj.module.om.controller.admin.inspectiondata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 巡检记录 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class InspectionRecordBaseVO {

    @Schema(description = "巡检结果")
    private String result;

    @Schema(description = "uuid")
    private String uuid;

    @Schema(description = "检测云主机数量")
    private Integer hostNum;

    @Schema(description = "检测宿主机数量")
    private Integer hardwareNum;

    @Schema(description = "检测主存储数量")
    private Integer storageNum;

    @Schema(description = "正常项数量")
    private Integer normalNum;

    @Schema(description = "异常项数量")
    private Integer abnormalNum;

    @Schema(description = "创建人名称")
    private String creatorName;

    @Schema(description = "巡检得分")
    private BigDecimal score;
}
