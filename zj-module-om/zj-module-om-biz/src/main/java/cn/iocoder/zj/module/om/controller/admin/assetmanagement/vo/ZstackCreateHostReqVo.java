package cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Size;

@Data
public class ZstackCreateHostReqVo {

    @Schema(description = "云主机名称")
    @Size(max = 50, message = "云主机名称最大长度为50个字符")
    private String name;

    @Schema(description = "根云盘规格UUID,如果imageUuid字段指定的镜像类型是ISO，该字段必须指定以确定需要创建的根云盘大小。" +
            "如果镜像类型是非ISO，该字段无需指定")
    private String rootDiskOfferingUuid ;

    @Schema(description = "数据云盘规格UUID，多个逗号拼接")
    private String dataDiskOfferingUuids ;

    @Schema(description = "云主机规格uuid",required = true)
    private String instanceOfferingUuid;

    @Schema(description = "镜像uuid",required = true)
    private String imageUuid;

    @Schema(description = "三层网络uuid,多个逗号拼接",required = true)
    private String l3NetworkUuids;

    @Schema(description = "集群uuid")
    private String clusterUuid;

    @Schema(description = "平台ID",required = true)
    private Long platformId;
}
