package cn.iocoder.zj.module.om.service.cloud;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.monitor.api.hardware.HardWareInfoApi;
import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.hostinfo.HostInfoApi;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.storage.StorageInfoApi;
import cn.iocoder.zj.module.monitor.api.storage.dto.StorageRespCreateReqDTO;
import cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo.ResourceOperateLog;
import cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo.SangForCreateVmReqVO;
import cn.iocoder.zj.module.om.controller.admin.vncinfo.vo.VncInfoVo;
import cn.iocoder.zj.module.om.dal.mysql.assetmanagement.AssetManagementMapper;
import cn.iocoder.zj.module.om.framework.websocket.WebSocketClient;
import cn.iocoder.zj.module.om.service.zstack.core.SangForApiConstant;
import cn.iocoder.zj.module.om.util.SangForPwEncryptor;
import cn.iocoder.zj.module.om.util.StringUtil;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Log4j2
public class ISangForCloudServiceImpl implements ISangForCloudService{
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    HostInfoApi hostInfoApi;
    @Resource
    HardWareInfoApi hardWareInfoApi;
    @Resource
    AssetManagementMapper assetManagementMapper;
    @Resource WebSocketClient webSocketClient;
    @Override
    public CommonResult<Map<String, String>> operateSangForVm(String uuid, Long platformId, String type, String action) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        HostInfoRespCreateReqDTO resqDTO = hostInfoApi.getByUuid(uuid).getData();
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(platformId).getData();
        JSONObject tokenInfo = SangForPwEncryptor.sangForLogin(platform);
        HostInfoRespCreateReqDTO reqDTO = new HostInfoRespCreateReqDTO();
        String url = platform.getUrl();
        String state = "";
        switch (action){
            case "destroy":
                url = url + SangForApiConstant.DELETE_VM.replace("{vmid}",uuid);
                state = "Destroyed";
                hostInfoApi.deleteAssetAndHostJson(uuid);
                break;
            case "stop":
                url =url + SangForApiConstant.SWITCH_STATE_VM.replace("{vmid}",uuid)+(type.equals("grace")?"/shutdown":"/stop");
                state = "Stopped";
                reqDTO.setState("Stopping");
                break;
            case "start":
                url = url + SangForApiConstant.SWITCH_STATE_VM.replace("{vmid}",uuid)+"/start";
                state = "Running";
                reqDTO.setState("Starting");
                break;
            case "reboot" :
                url = url + SangForApiConstant.SWITCH_STATE_VM.replace("{vmid}",uuid)+"/reset";
                state = "Running";
                reqDTO.setState("Rebooting");
                break;
        }
        Map<String,String> reuslt = new HashMap<>();
        reuslt.put("success","true");
        String finalState = state;
        String op = hostOperateConvert(action);
        reuslt.put("msg","正在"+op+"("+resqDTO.getName()+")");
        String finalUrl = url;

        HttpResponse res = HttpRequest.post(finalUrl)
                .header("CSRFPreventionToken",tokenInfo.getString("CSRFPreventionToken"))
                .execute();
        reqDTO.setUuid(uuid);
        hostInfoApi.updateHostSingle(reqDTO);
        Runnable data = (()->{
        //另起线程处理请求后的结果
            updateHostInfo(res,action,uuid,platform, finalState,"",tokenInfo,loginUser);
        });
        Thread thread = new Thread(data);
        thread.start();
        return CommonResult.success(reuslt);
    }

    @Override
    public CommonResult<Map<String, String>> operateSangForHardware(String uuid, Long platformId, String actions, String state, String hardwareName) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        HardWareRespCreateReqDTO reqDTO = new HardWareRespCreateReqDTO();
        reqDTO.setUuid(uuid);
        List<HardWareRespCreateReqDTO> reqList = new ArrayList<>();
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(platformId).getData();
        JSONObject tokenInfo = SangForPwEncryptor.sangForLogin(platform);
        String url = platform.getUrl();
        switch (state){
            case "maintain":url = url + SangForApiConstant.HARDWARE_MAINTAIN.replace("{node}",uuid)+"/open_host_protect";
                reqDTO.setState("Maintaining");
                break;
            case "ext_maintain":url = url + SangForApiConstant.HARDWARE_MAINTAIN.replace("{node}",uuid)+"/close_host_protect";
                reqDTO.setState("extMaintaining");
        }
        HttpResponse res = HttpRequest.post(url)
                .header("CSRFPreventionToken", tokenInfo.getString("CSRFPreventionToken"))
                .execute();
        reqList.add(reqDTO);
        hardWareInfoApi.updates(reqList);
        Map<String,String> result = new HashMap<>();
        result.put("success","true");
        result.put("msg","");
        Runnable data = (()->{
            updateHardwareInfo(res, uuid, platform,hardwareName,actions,state, tokenInfo,loginUser);
        });
        Thread thread = new Thread(data);
        thread.start();
        return CommonResult.success(result);
    }

    @Override
    public Map<String, String> createSangForVm(SangForCreateVmReqVO reqVo) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        HostInfoRespCreateReqDTO reqDTO = new HostInfoRespCreateReqDTO();
        Map<String,String> result = new HashMap<>();
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(reqVo.getPlatformId()).getData();
        JSONObject tokenInfo = SangForPwEncryptor.sangForLogin(platform);
        HttpResponse hostChoose = HttpRequest.get(platform.getUrl()+(SangForApiConstant.HOST_CHOOSE.replace("{storageId}", reqVo.getCfgstorage())))
                .header("CSRFPreventionToken", tokenInfo.getString("CSRFPreventionToken"))
                .execute();
        if(hostChoose.getStatus()!=200){
            throw new RuntimeException("获取可用宿主机列表时发生错误。");
        }
        Map<String,Object> createMap = new HashMap<>();
        createMap.put("cfgstorage",reqVo.getCfgstorage());
        createMap.put("cores",String.valueOf(reqVo.getCores()));
        createMap.put("memory",String.valueOf(reqVo.getMemory()*(2<<10)));
        createMap.put("ostype",reqVo.getOsType());
        createMap.put("name",reqVo.getName());
        JSONArray hostList = JSONObject.parseObject(hostChoose.body()).getJSONArray("data");
        if(hostList.size()==0){
            throw new RuntimeException("该存储下没有合适的宿主机，请更换存储后再尝试");
        }
        String nodeId = "";
        for (Object hostInfo:hostList) {
            JSONObject hostObj = JSONObject.parseObject(JSONUtil.toJsonStr(hostInfo));
            JSONObject mem = hostObj.getJSONObject("conf_mem");
            JSONObject cpu = hostObj.getJSONObject("conf_cpu");
            Integer usableCpu = cpu.getInteger("conf_total_vcore")- cpu.getInteger("conf_used_vcore");
            Long usableMem = mem.getLong("base_total_byte") - mem.getLong("base_used_byte");
            if(reqVo.getCores()<=usableCpu && reqVo.getMemory()*(Math.pow(2,30))<= usableMem){
                nodeId = hostObj.getString("nodeid");
                break;
            }
        }
        if(StringUtil.isNullOrEmpty(nodeId)){
            throw new RuntimeException("宿主机可分配的内存或cpu不足，请调整参数后重试。");
        }
        String ide0 = reqVo.getCfgstorage() +":"+reqVo.getStorageSize()+",format=qcow2,cache=directsync,preallocate=off,forecast=disable,cache_size=256";
        createMap.put("ide0",ide0);
        HttpResponse macStr = HttpRequest.get(platform.getUrl()+SangForApiConstant.MAC_ADDRESS)
                .header("CSRFPreventionToken", tokenInfo.getString("CSRFPreventionToken"))
                .execute();
        if(macStr.getStatus()!=200){
            throw new RuntimeException("生成物理地址时发生错误。");
        }
        HttpResponse netInfo = HttpRequest.get(platform.getUrl()+SangForApiConstant.NET_INFO+reqVo.getNetwork())
                .header("CSRFPreventionToken", tokenInfo.getString("CSRFPreventionToken"))
                .execute();
        if(netInfo.getStatus()!=200){
            throw new RuntimeException("网络配置错误。");
        }
        JSONObject netObj = JSONObject.parseObject(netInfo.body()).getJSONObject("data");
        String bridgename = netObj.getString("name");
        String peer_device_id = netObj.getString("id");
        String peer_vlan_group_id = JSONObject.parseObject(JSONUtil.toJsonStr(netObj.getJSONArray("vlanGroup").get(0))).getString("id");
        String net0 =  "rtl8139="+JSONObject.parseObject(macStr.body()).getString("data")
                + ",peer_device_id="+peer_device_id
                +",bridgename="+bridgename
                +",peer_vlan_group_id="+peer_vlan_group_id
                +",peer_device_type=evs,connect=on,dhcp_reply_flag=0,dhcp6_reply_flag=0,qos_enable=0";
        createMap.put("net0",net0);
        HttpResponse createVm = HttpRequest.post(platform.getUrl()+(SangForApiConstant.CREATE_VM).replace("{nodeid}",nodeId))
                .header("CSRFPreventionToken", tokenInfo.getString("CSRFPreventionToken"))
                .header(Header.CONTENT_TYPE,"application/x-www-form-urlencoded")
                .form(createMap)
                .execute();
        JSONObject processObj = JSONObject.parseObject(createVm.body());
        if(processObj.getInteger("success")!=1){
            throw new RuntimeException("创建任务失败。");
        }
        //另起线程处理添加云主机任务
        String upid = processObj.getString("data");
        String uuidInfo = upid.substring(upid.indexOf("新建虚拟机:"),upid.length()-1);
        Pattern pattern = Pattern.compile(":(.*):");
        Matcher matcher= pattern.matcher(uuidInfo);
        String vmid = "";
        while (matcher.find()) {
            vmid = matcher.group(1);
        }
        reqDTO.setVms(vmid);
        reqDTO.setUuid(vmid);
        reqDTO.setName(reqVo.getName());
        reqDTO.setPlatformId(platform.getId());
        reqDTO.setPlatformName(platform.getName());
        reqDTO.setState("Creating");
        hostInfoApi.createHostSingle(reqDTO);
        String finalVmid = vmid;
        Runnable data=(()->{
            Map<String,String> processResult = getProcessResult(platform,upid,tokenInfo);
            JSONObject resultMessage = new JSONObject();
            resultMessage.put("uuid",reqDTO.getUuid());
            if(processResult.get("success").equals("true")) {
                HttpResponse vmInfo = HttpRequest.get(platform.getUrl()+SangForApiConstant.VM_INFO.replace("{vmid}", finalVmid))
                        .header("CSRFPreventionToken", tokenInfo.getString("CSRFPreventionToken"))
                        .execute();
                JSONObject vmObj = JSONObject.parseObject(vmInfo.body()).getJSONObject("data");
                reqDTO.setUuid(finalVmid);
                reqDTO.setTypeName(platform.getTypeCode());
                reqDTO.setState("Running");
                reqDTO.setArchitecture(vmObj.getString("cpu"));
                reqDTO.setGuestOsType(vmObj.getString("guestOsType"));
            }else {
                resultMessage.put("success","false");
                resultMessage.put("msg","创建云主机("+reqVo.getName()+")失败，错误信息:"+processResult.get("msg")+",请至"+platform.getName()+"查看详情");
                createOperateLog(reqVo.getName(), finalVmid, "创建云主机", "error", "host",processResult.get("msg"), platform,loginUser);
                webSocketClient.sendMessage(reqDTO.getUuid(),resultMessage.toJSONString());
            }
            resultMessage.put("success","true");
            resultMessage.put("msg","创建云主机("+reqVo.getName()+")成功");
            createOperateLog(reqVo.getName(), finalVmid, "创建云主机", "success", "host","", platform,loginUser);
            webSocketClient.sendMessage(loginUser.getId().toString(),resultMessage.toJSONString());
            hostInfoApi.createHostSingle(reqDTO);
        });
        Thread thread = new Thread(data);
        thread.start();
        result.put("success","true");
        result.put("msg","");
        return result;
    }

    @Override
    public JSONArray getVmMirror(Long platformId, String uuid) {
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(platformId).getData();
        JSONObject tokenInfo = SangForPwEncryptor.sangForLogin(platform);
        String url = platform.getUrl();
        Map<String,Object> createMap = new HashMap<>();
        String storeids = "";
        JSONArray isoList = new JSONArray();
        createMap.put("vmid",uuid);
        createMap.put("storeids",storeids);
        HttpResponse res = HttpRequest.post(url+SangForApiConstant.VM_MIRRO)
                .header("CSRFPreventionToken", tokenInfo.getString("CSRFPreventionToken"))
                .form(createMap)
                .execute();
        JSONObject obj = JSONObject.parseObject(res.body()).getJSONObject("data");
        if(obj.getJSONArray("isos") !=null) {
            isoList.addAll(obj.getJSONArray("isos"));
        }
        while (!obj.getString("status").equals("store-over")){
            storeids = storeids + obj.getString("storeid")+",";
            createMap.put("storeids",storeids);
            HttpResponse next = HttpRequest.post(url+SangForApiConstant.VM_MIRRO)
                    .header("CSRFPreventionToken", tokenInfo.getString("CSRFPreventionToken"))
                    .form(createMap)
                    .execute();
            obj = JSONObject.parseObject(next.body()).getJSONObject("data");
            if(obj.getJSONArray("isos") !=null) {
                isoList.addAll(obj.getJSONArray("isos"));
            }
        }
        isoList = new JSONArray(isoList.stream().filter(item -> !JSONObject.parseObject(JSONUtil.toJsonStr(item)).getString("path").contains("/vmtools"))
                .collect(Collectors.toList()));
        return isoList;
    }

    @Override
    public VncInfoVo getVncInfo(HostInfoRespCreateReqDTO hostInfo) {
        VncInfoVo vncInfoVo = new VncInfoVo();
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(hostInfo.getPlatformId()).getData();
        JSONObject tokenInfo = SangForPwEncryptor.sangForLogin(platform);
        String url = platform.getUrl();
        Map<String,Object> param = new HashMap<>();
        param.put("vncws",0);
        HttpResponse res = HttpRequest.post(url+SangForApiConstant.VNC_PROXY.replace("{vmid}",hostInfo.getVms()))
                .header("CSRFPreventionToken", tokenInfo.getString("CSRFPreventionToken"))
                .contentType("application/x-www-form-urlencoded;charset=UTF-8")
                .cookie("LoginAuthCookie=" + tokenInfo.getString("ticket"))
                .form(param)
                .execute();
        JSONObject obj = JSONObject.parseObject(res.body()).getJSONObject("data");
        String proxystr = obj.getString("proxystr")           ;
        vncInfoVo.setWebsocketPath("/wss/?proxystr=" + proxystr);
        vncInfoVo.setUseSsl(true);
        URL targetUrl = null;
        try {
            targetUrl = new URL(platform.getUrl());
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }
        vncInfoVo.setWebsocketHost(targetUrl.getHost());
        vncInfoVo.setTcpPort(targetUrl.getPort());
        return vncInfoVo;
    }

    private void updateHardwareInfo(HttpResponse res, String uuid, PlatformconfigDTO platform, String hardwareName,String operation,String state, JSONObject tokenInfo,LoginUser loginUser) {
        JSONObject respBody = JSONObject.parseObject(res.body());
        JSONObject result = new JSONObject();
        HardWareRespCreateReqDTO reqDTO = new HardWareRespCreateReqDTO();
        List<HardWareRespCreateReqDTO> reqList = new ArrayList<>();
        Map<String,String> processState = getProcessResult(platform,respBody.getJSONObject("data").getString("data"),tokenInfo);
        result.put("uuid",uuid);
        if(processState.get("success").equals("true")) {
            reqDTO.setUuid(uuid);
            reqDTO.setState(state);
            reqList.add(reqDTO);
            hardWareInfoApi.updates(reqList);
            result.put("msg",hardwareOperateConvert(operation,state)+"("+hardwareName+")成功");
            result.put("success","true");
            createOperateLog(hardwareName,uuid, hardwareOperateConvert(operation,state), "success", "hardware","", platform,loginUser);
            webSocketClient.sendMessage(uuid,result.toJSONString());
        }else {
            result.put("msg",hardwareOperateConvert(operation,state)+"("+hardwareName+")失败，错误信息:"+processState.get("msg")+",请至"+platform.getName()+"查看详情");
            result.put("success","false");
            createOperateLog(hardwareName,uuid, hardwareOperateConvert(operation,state), "error", "hardware",processState.get("msg"), platform,loginUser);
            webSocketClient.sendMessage(uuid,result.toJSONString());
        }

    }

    public void updateHostInfo(HttpResponse resp,String operation,String hostUuid,PlatformconfigDTO platform,String state,String name,JSONObject token,LoginUser loginUser){
        JSONObject respBody = JSONObject.parseObject(resp.body());
        String op = hostOperateConvert(operation);
        JSONObject result = new JSONObject();
        HostInfoRespCreateReqDTO reqDTO = new HostInfoRespCreateReqDTO();
        Map<String,String> processState = getProcessResult(platform,respBody.getString("data"),token);
        result.put("uuid",hostUuid);
        if(processState.get("success").equals("true")) {
            if (!operation.equals("createVm")) {
                reqDTO.setUuid(hostUuid);
                reqDTO.setState(state);
                hostInfoApi.updateHostSingle(reqDTO);
            } else {
                hostInfoApi.createHostSingle(reqDTO);
            }
        }else {
            result.put("success","false");
            result.put("msg",op+"("+name+")失败，错误信息:"+processState.get("msg")+",请至"+platform.getName()+"查看详情");
            //创建操作记录
            createOperateLog(name,hostUuid,op, "error", "host",processState.get("msg"), platform,loginUser);
            webSocketClient.sendMessage(hostUuid,result.toJSONString());
        }
        createOperateLog(name,hostUuid,op, "success", "host","", platform,loginUser);
        result.put("success","true");
        result.put("msg",op+"("+name+")成功");
        webSocketClient.sendMessage(hostUuid,result.toJSONString());
    }
    public Map<String,String> getProcessResult(PlatformconfigDTO platformconfigDTO,String upid,JSONObject tokenInfo){
        JSONObject result;
        Integer process = 0;
        upid =  (upid.contains("UPID:")?"?upid=":"?log_id=") +upid;
        Map<String,String> processState = new HashMap<>();
        while (process<100) {
            try {
                Thread.sleep(2000);
                HttpResponse res = HttpRequest.get(platformconfigDTO.getUrl()+SangForApiConstant.PROCESS_STATE + upid)
                        .header("CSRFPreventionToken", tokenInfo.getString("CSRFPreventionToken"))
                        .execute();
                result = JSONObject.parseObject(res.body());
                if(result.getInteger("success") ==1) {
                    process = result.getJSONObject("data").getInteger("process");
                }else {
                    processState.put("success","false");
                    processState.put("msg",result.getString("message"));
                    return processState;
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        processState.put("success","true");
        processState.put("msg","");
        return processState;
    }
    public void createOperateLog(String name, String uuid, String operation, String result, String resourceType, String msg, PlatformconfigDTO platform,LoginUser loginUser) {
        ResourceOperateLog operateLog = new ResourceOperateLog();
        operateLog.setCreator(String.valueOf(loginUser.getId()));
        operateLog.setOperation(operation);
        operateLog.setResourceType(resourceType);
        operateLog.setResult(result);
        operateLog.setResourceUuid(uuid);
        operateLog.setResourceName(name);
        operateLog.setPlatformName(platform.getName());
        operateLog.setPlatformId(platform.getId());
        operateLog.setContent(msg);
        assetManagementMapper.createOperateLog(operateLog);
    }
    public String hardwareOperateConvert(String operation,String state){
        String op = "";
        switch (operation){
            case "reconnect":
                op = "重启宿主机"; break;
            case "create":
                op = "创建宿主机"; break;
            case "stateChange":
                switch (state){
                    case "enable" :
                        op = "启用宿主机"; break;
                    case "disable" :
                        op = "停用宿主机"; break;
                    case "maintain" :
                        op = "进入维护"; break;
                    case "ext_maintain" :
                        op = "退出维护"; break;
                }
                break;
        }
        return op;
    }
    public String hostOperateConvert(String operation){
        String op = "";
        switch (operation){
            case "create":
                op = "创建虚拟机"; break;
            case "stop":
                op = "停止虚拟机"; break;
            case "start":
                op = "启动虚拟机"; break;
            case "reboot":
                op = "重启虚拟机"; break;
            case "destroy":
                op = "回收虚拟机"; break;
            case "creating":
                op = "创建中"; break;
            case "stopping":
                op = "停止中"; break;
            case "starting":
                op = "启动中"; break;
            case "rebooting":
                op = "重启中"; break;
            case "destroying":
                op = "回收中"; break;
        }
        return op;
    }
}
