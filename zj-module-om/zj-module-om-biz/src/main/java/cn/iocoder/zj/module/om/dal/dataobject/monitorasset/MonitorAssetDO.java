package cn.iocoder.zj.module.om.dal.dataobject.monitorasset;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 监控资产 DO
 *
 * <AUTHOR>
 */
@TableName("om_monitor_asset")
@KeySequence("om_monitor_asset_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MonitorAssetDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 资产id
     */
    private String assetId;
    /**
     * 资产名称
     */
    private String assetName;
    /**
     * 协议类型
     */
    private String protocol;
    /**
     * 协议端口
     */
    private Integer protocolProd;
    /**
     * 账户类型
     */
    private String certificate;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;
    /**
     * 资产类型
     */
    private Integer assetType;
    /**
     * 授权状态
     */
    private String authorizationType;
    /**
     * 授权有效时间
     */
    private LocalDateTime authorizationTime;
    /**
     * 用户名称
     */
    private String username;
    /**
     * 账号密码
     */
    private String password;

    /**
     * ip地址
     */
    private String hostname;


    private String category;
    /**
     * 最后接入时间
     */
    private LocalDateTime lastAccessTime;
}
