package cn.iocoder.zj.module.om.Inspection.job.task;

import cn.iocoder.zj.module.om.Inspection.job.model.LulTaskModel;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface LulTaskService {

    /**
     * 查询任务列表
     *
     * @return
     */
    List<LulTaskModel> listTasks();

    /**
     * 根据任务id移除任务
     */
    void removeTask(LulTaskModel taskCacheModel);

    /**
     * 新增任务
     */
    void addTask(LulTaskModel taskCacheModel);

    /**
     * 根据任务id更新任务
     */
    void updateTasByJobId(LulTaskModel taskCacheModel);

    /**
     * 根据任务id查询任务
     */
    LulTaskModel getTaskByJobId(Long jobId);

}
