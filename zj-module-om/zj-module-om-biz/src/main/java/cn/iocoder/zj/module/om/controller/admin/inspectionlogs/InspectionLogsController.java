package cn.iocoder.zj.module.om.controller.admin.inspectionlogs;

import cn.iocoder.zj.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.om.controller.admin.inspectionlogs.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.inspectionlogs.InspectionLogsDO;
import cn.iocoder.zj.module.om.convert.inspectionlogs.InspectionLogsConvert;
import cn.iocoder.zj.module.om.service.inspectionlogs.InspectionLogsService;

@Tag(name = "管理后台 - 自动巡检异常记录")
@RestController
@RequestMapping("/om/inspection-logs")
@Validated
public class InspectionLogsController {

    @Resource
    private InspectionLogsService inspectionLogsService;

    @PostMapping("/create")
    @Operation(summary = "创建自动巡检异常记录")
    public CommonResult<Long> createInspectionLogs(@Valid @RequestBody InspectionLogsCreateReqVO createReqVO) {
        return success(inspectionLogsService.createInspectionLogs(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新自动巡检异常记录")
    public CommonResult<Boolean> updateInspectionLogs(@Valid @RequestBody InspectionLogsUpdateReqVO updateReqVO) {
        inspectionLogsService.updateInspectionLogs(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除自动巡检异常记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('om:inspection-logs:delete')")
    public CommonResult<Boolean> deleteInspectionLogs(@RequestParam("id") Long id) {
        inspectionLogsService.deleteInspectionLogs(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得自动巡检异常记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<InspectionLogsRespVO> getInspectionLogs(@RequestParam("id") Long id) {
        InspectionLogsDO inspectionLogs = inspectionLogsService.getInspectionLogs(id);
        return success(InspectionLogsConvert.INSTANCE.convert(inspectionLogs));
    }

    @GetMapping("/list")
    @Operation(summary = "获得自动巡检异常记录列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    public CommonResult<List<InspectionLogsRespVO>> getInspectionLogsList(@RequestParam("ids") Collection<Long> ids) {
        List<InspectionLogsDO> list = inspectionLogsService.getInspectionLogsList(ids);
        return success(InspectionLogsConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得自动巡检异常记录分页")
    @PreAuthenticated
    @TenantIgnore
    public CommonResult<PageResult<InspectionLogsRespVO>> getInspectionLogsPage(@Valid InspectionLogsPageReqVO pageVO) {
        PageResult<InspectionLogsDO> pageResult = inspectionLogsService.getInspectionLogsPage(pageVO);
        return success(InspectionLogsConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出自动巡检异常记录 Excel")
    @OperateLog(type = EXPORT)
    public void exportInspectionLogsExcel(@Valid InspectionLogsExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<InspectionLogsDO> list = inspectionLogsService.getInspectionLogsList(exportReqVO);
        // 导出 Excel
        List<InspectionLogsExcelVO> datas = InspectionLogsConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "自动巡检异常记录.xls", "数据", InspectionLogsExcelVO.class, datas);
    }

}
