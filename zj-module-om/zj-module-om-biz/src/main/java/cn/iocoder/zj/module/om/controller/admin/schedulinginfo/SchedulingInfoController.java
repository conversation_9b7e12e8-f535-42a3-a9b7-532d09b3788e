package cn.iocoder.zj.module.om.controller.admin.schedulinginfo;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.om.controller.admin.schedulinginfo.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.schedulinginfo.SchedulingInfoDO;
import cn.iocoder.zj.module.om.convert.schedulinginfo.SchedulingInfoConvert;
import cn.iocoder.zj.module.om.service.schedulinginfo.SchedulingInfoService;

@Tag(name = "管理后台 - 运维排班信息")
@RestController
@RequestMapping("/om/scheduling-info")
@Validated
public class SchedulingInfoController {

    @Resource
    private SchedulingInfoService schedulingInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建运维排班信息")
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('om:scheduling:create')")
    public CommonResult<Long> createSchedulingInfo(@Valid @RequestBody SchedulingInfoCreateReqVO createReqVO) {
        return success(schedulingInfoService.createSchedulingInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新运维排班信息")
    @OperateLog(type = UPDATE)
    @PreAuthorize("@ss.hasPermission('om:scheduling:update')")
    public CommonResult<Boolean> updateSchedulingInfo(@Valid @RequestBody SchedulingInfoUpdateReqVO updateReqVO) {
        schedulingInfoService.updateSchedulingInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除运维排班信息")
    @OperateLog(type = DELETE)
    @PreAuthorize("@ss.hasPermission('om:scheduling:delete')")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteSchedulingInfo(@RequestParam("id") Long id) {
        schedulingInfoService.deleteSchedulingInfo(id);
        return success(true);
    }

    @GetMapping("/queryScheduling")
    @Operation(summary = "获得运维排班信息")
    public CommonResult<List<SchedulingInfoRespVO>> queryScheduling(Long startMillis,Long endMillis) {
        List<SchedulingInfoRespVO> schedulingInfoDOS = schedulingInfoService.queryScheduling(startMillis, endMillis);
        return success(schedulingInfoDOS);
    }

//    @GetMapping("/queryScheduling")
//    @Operation(summary = "获得运维排班信息")
//    public CommonResult<List<SchedulingInfoRespVO>> queryScheduling(SchedulingInfoRespVO schedulingInfoRespVO) {
//        List<SchedulingInfoRespVO> schedulingInfoDOS = schedulingInfoService.queryScheduling(schedulingInfoRespVO);
//        return success(schedulingInfoDOS);
//    }


    @GetMapping("/get")
    @Operation(summary = "获得运维排班信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<SchedulingInfoRespVO> getSchedulingInfo(@RequestParam("id") Long id) {
        SchedulingInfoDO schedulingInfo = schedulingInfoService.getSchedulingInfo(id);
        return success(SchedulingInfoConvert.INSTANCE.convert(schedulingInfo));
    }

//    @GetMapping("/list")
//    @Operation(summary = "获得运维排班信息列表")
//    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
//    @PreAuthorize("@ss.hasPermission('om:scheduling-info:query')")
//    public CommonResult<List<SchedulingInfoRespVO>> getSchedulingInfoList(@RequestParam("ids") Collection<Long> ids) {
//        List<SchedulingInfoDO> list = schedulingInfoService.getSchedulingInfoList(ids);
//        return success(SchedulingInfoConvert.INSTANCE.convertList(list));
//    }

//    @GetMapping("/page")
//    @Operation(summary = "获得运维排班信息分页")
//    @PreAuthorize("@ss.hasPermission('om:scheduling-info:query')")
//    public CommonResult<PageResult<SchedulingInfoRespVO>> getSchedulingInfoPage(@Valid SchedulingInfoPageReqVO pageVO) {
//        PageResult<SchedulingInfoDO> pageResult = schedulingInfoService.getSchedulingInfoPage(pageVO);
//        return success(SchedulingInfoConvert.INSTANCE.convertPage(pageResult));
//    }

//    @GetMapping("/export-excel")
//    @Operation(summary = "导出运维排班信息 Excel")
//    @PreAuthorize("@ss.hasPermission('om:scheduling-info:export')")
//    @OperateLog(type = EXPORT)
//    public void exportSchedulingInfoExcel(@Valid SchedulingInfoExportReqVO exportReqVO,
//              HttpServletResponse response) throws IOException {
//        List<SchedulingInfoDO> list = schedulingInfoService.getSchedulingInfoList(exportReqVO);
//        // 导出 Excel
//        List<SchedulingInfoExcelVO> datas = SchedulingInfoConvert.INSTANCE.convertList02(list);
//        ExcelUtils.write(response, "运维排班信息.xls", "数据", SchedulingInfoExcelVO.class, datas);
//    }

}
