package cn.iocoder.zj.module.om.dal.mysql.patrolrecord;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.om.controller.admin.patrolrecord.vo.PatrolRecordExportReqVO;
import cn.iocoder.zj.module.om.controller.admin.patrolrecord.vo.PatrolRecordPageReqVO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolrecord.PatrolRecordConfigDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolrecord.PatrolRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 巡检记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PatrolRecordMapper extends BaseMapperX<PatrolRecordDO> {

    default PageResult<PatrolRecordDO> selectPage(PatrolRecordPageReqVO reqVO) {
        LambdaQueryWrapperX<PatrolRecordDO> wrapper = new LambdaQueryWrapperX<PatrolRecordDO>()
                .likeIfPresent(PatrolRecordDO::getRecordName, reqVO.getRecordName())
                .eqIfPresent(PatrolRecordDO::getTotalItemCount, reqVO.getTotalItemCount())
                .eqIfPresent(PatrolRecordDO::getTotalResourceCount, reqVO.getTotalResourceCount())
                .eqIfPresent(PatrolRecordDO::getAbnormalCount, reqVO.getAbnormalCount())
                .eqIfPresent(PatrolRecordDO::getSysSettingTenant, reqVO.getTenantId());

        if (reqVO.getPlanId() != null) {
            wrapper.eq(PatrolRecordDO::getPlanId, reqVO.getPlanId());
        } else {
            wrapper.isNull(PatrolRecordDO::getPlanId);
        }
        if (reqVO.getBeginTime() != null && reqVO.getEndTime() != null) {
            wrapper.between(PatrolRecordDO::getStartTime, reqVO.getBeginTime(), reqVO.getEndTime());
        }
        if (reqVO.getSortBy() != null && reqVO.getSortDirection() != null) {
            if (reqVO.getSortBy().equals("startTime")) {
                if (reqVO.getSortDirection().equals("asc")) {
                    wrapper.orderByAsc(PatrolRecordDO::getStartTime);
                } else {
                    wrapper.orderByDesc(PatrolRecordDO::getStartTime);
                }
            }
            if (reqVO.getSortBy().equals("endTime")) {
                if (reqVO.getSortDirection().equals("asc")) {
                    wrapper.orderByAsc(PatrolRecordDO::getEndTime);
                } else {
                    wrapper.orderByDesc(PatrolRecordDO::getEndTime);
                }
            }
        } else {
            wrapper.orderByDesc(PatrolRecordDO::getId);
        }
        return selectPage(reqVO, wrapper);
    }

    default List<PatrolRecordDO> selectList(PatrolRecordExportReqVO reqVO) {
        LambdaQueryWrapperX<PatrolRecordDO> wrapper = new LambdaQueryWrapperX<PatrolRecordDO>()
                .likeIfPresent(PatrolRecordDO::getRecordName, reqVO.getRecordName())
                .eqIfPresent(PatrolRecordDO::getTotalItemCount, reqVO.getTotalItemCount())
                .eqIfPresent(PatrolRecordDO::getTotalResourceCount, reqVO.getTotalResourceCount())
                .eqIfPresent(PatrolRecordDO::getAbnormalCount, reqVO.getAbnormalCount())
                .eqIfPresent(PatrolRecordDO::getSysSettingTenant, reqVO.getTenantId())
                .orderByDesc(PatrolRecordDO::getId);

        if (reqVO.getPlanId() != null) {
            wrapper.eq(PatrolRecordDO::getPlanId, reqVO.getPlanId());
        } else {
            wrapper.isNull(PatrolRecordDO::getPlanId);
        }
        if (reqVO.getBeginTime() != null && reqVO.getEndTime() != null) {
            wrapper.between(PatrolRecordDO::getStartTime, reqVO.getBeginTime(), reqVO.getEndTime());
        }
        return selectList(wrapper);
    }

    @TenantIgnore
    void insertRecordConfig(PatrolRecordConfigDO config);

    @TenantIgnore
    List<PatrolRecordConfigDO> selectRecordConfigByRecordId(@Param("recordId") Long recordId);
}
