package cn.iocoder.zj.module.om.service.assetmanagement;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.om.api.asset.dto.MonitorAssetDTO;
import cn.iocoder.zj.module.om.api.asset.dto.VncInfoDTO;
import cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo.OperateLogPageReqVO;
import cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo.ResourceOperateLog;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;

import java.util.List;
import java.util.Map;

/**
 * 运维看板 Service 接口
 * 作者:安徽中杰
 */
public interface AssetManagementService {
    List<Map<String,Object>> list(String tenantId);

    Map<String,Object> getAssetInfo(String platformId);

    Map<String,Object> hardwareInfo(String platformId);

    PageResult<ResourceOperateLog> getOperateLogPage(OperateLogPageReqVO pageVO);

    void createOperateLog(String name, String uuid, String operation, String result, String resourceType, String msg , PlatformconfigDTO platform);
    CommonResult<Map<String,String>> operateVmInstance(Long platformId, String vms, String uuid, String type, String actions, String hostName );
    CommonResult<Map<String,String>> operateHardware(Long platformId, String vms, String uuid, String state, String actions, String hardwareName);

    JSONArray getVmMirror(Long platformId, String uuid);

    VncInfoDTO getVncInfo(String uuid);
}
