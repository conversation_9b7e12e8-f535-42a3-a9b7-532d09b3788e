package cn.iocoder.zj.module.om.service.patrolabnormaldetail;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.om.controller.admin.patrolabnormaldetail.vo.PatrolAbnormalDetailCreateReqVO;
import cn.iocoder.zj.module.om.controller.admin.patrolabnormaldetail.vo.PatrolAbnormalDetailExportReqVO;
import cn.iocoder.zj.module.om.controller.admin.patrolabnormaldetail.vo.PatrolAbnormalDetailPageReqVO;
import cn.iocoder.zj.module.om.controller.admin.patrolabnormaldetail.vo.PatrolAbnormalDetailUpdateReqVO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolabnormaldetail.PatrolAbnormalDetailDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 巡检异常明细 Service 接口
 *
 * <AUTHOR>
 */
public interface PatrolAbnormalDetailService {

    /**
     * 创建巡检异常明细
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPatrolAbnormalDetail(@Valid PatrolAbnormalDetailCreateReqVO createReqVO);

    /**
     * 更新巡检异常明细
     *
     * @param updateReqVO 更新信息
     */
    void updatePatrolAbnormalDetail(@Valid PatrolAbnormalDetailUpdateReqVO updateReqVO);

    /**
     * 删除巡检异常明细
     *
     * @param id 编号
     */
    void deletePatrolAbnormalDetail(Long id);

    /**
     * 获得巡检异常明细
     *
     * @param id 编号
     * @return 巡检异常明细
     */
    PatrolAbnormalDetailDO getPatrolAbnormalDetail(Long id);

    /**
     * 获得巡检异常明细列表
     *
     * @param ids 编号
     * @return 巡检异常明细列表
     */
    List<PatrolAbnormalDetailDO> getPatrolAbnormalDetailList(Collection<Long> ids);

    /**
     * 获得巡检异常明细分页
     *
     * @param pageReqVO 分页查询
     * @return 巡检异常明细分页
     */
    PageResult<PatrolAbnormalDetailDO> getPatrolAbnormalDetailPage(PatrolAbnormalDetailPageReqVO pageReqVO);

    /**
     * 获得巡检异常明细列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 巡检异常明细列表
     */
    List<PatrolAbnormalDetailDO> getPatrolAbnormalDetailList(PatrolAbnormalDetailExportReqVO exportReqVO);

}
