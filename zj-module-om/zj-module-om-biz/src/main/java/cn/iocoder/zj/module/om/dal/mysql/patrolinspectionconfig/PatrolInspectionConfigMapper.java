package cn.iocoder.zj.module.om.dal.mysql.patrolinspectionconfig;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.om.controller.admin.patrolinspectionconfig.vo.PatrolInspectionConfigExportReqVO;
import cn.iocoder.zj.module.om.controller.admin.patrolinspectionconfig.vo.PatrolInspectionConfigPageReqVO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspectionconfig.PatrolInspectionConfigDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 巡检设置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PatrolInspectionConfigMapper extends BaseMapperX<PatrolInspectionConfigDO> {

    default PageResult<PatrolInspectionConfigDO> selectPage(PatrolInspectionConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PatrolInspectionConfigDO>()
                .eqIfPresent(PatrolInspectionConfigDO::getTenantId, reqVO.getTenantId())
                .eqIfPresent(PatrolInspectionConfigDO::getResourceType, reqVO.getResourceType())
                .likeIfPresent(PatrolInspectionConfigDO::getMetricName, reqVO.getMetricName())
                .eqIfPresent(PatrolInspectionConfigDO::getThresholdType, reqVO.getThresholdType())
                .eqIfPresent(PatrolInspectionConfigDO::getOperator, reqVO.getOperator())
                .eqIfPresent(PatrolInspectionConfigDO::getThresholdValue, reqVO.getThresholdValue())
                .eqIfPresent(PatrolInspectionConfigDO::getThresholdValueMax, reqVO.getThresholdValueMax())
                .eqIfPresent(PatrolInspectionConfigDO::getUnit, reqVO.getUnit())
                .eqIfPresent(PatrolInspectionConfigDO::getDuration, reqVO.getDuration())
                .eqIfPresent(PatrolInspectionConfigDO::getDurationUnit, reqVO.getDurationUnit())
                .betweenIfPresent(PatrolInspectionConfigDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PatrolInspectionConfigDO::getId));
    }

    default List<PatrolInspectionConfigDO> selectList(PatrolInspectionConfigExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PatrolInspectionConfigDO>()
                .eqIfPresent(PatrolInspectionConfigDO::getTenantId, reqVO.getTenantId())
                .eqIfPresent(PatrolInspectionConfigDO::getResourceType, reqVO.getResourceType())
                .likeIfPresent(PatrolInspectionConfigDO::getMetricName, reqVO.getMetricName())
                .eqIfPresent(PatrolInspectionConfigDO::getThresholdType, reqVO.getThresholdType())
                .eqIfPresent(PatrolInspectionConfigDO::getOperator, reqVO.getOperator())
                .eqIfPresent(PatrolInspectionConfigDO::getThresholdValue, reqVO.getThresholdValue())
                .eqIfPresent(PatrolInspectionConfigDO::getThresholdValueMax, reqVO.getThresholdValueMax())
                .eqIfPresent(PatrolInspectionConfigDO::getUnit, reqVO.getUnit())
                .eqIfPresent(PatrolInspectionConfigDO::getDuration, reqVO.getDuration())
                .eqIfPresent(PatrolInspectionConfigDO::getDurationUnit, reqVO.getDurationUnit())
                .betweenIfPresent(PatrolInspectionConfigDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PatrolInspectionConfigDO::getId));
    }

    default List<PatrolInspectionConfigDO> getConfigsByMetricNamesAndTenantId(List<String> metricNames, Long tenantId) {
        return selectList(new LambdaQueryWrapperX<PatrolInspectionConfigDO>()
                .eqIfPresent(PatrolInspectionConfigDO::getTenantId, tenantId)
                .inIfPresent(PatrolInspectionConfigDO::getMetricName, metricNames));
    }

    @TenantIgnore
    default List<PatrolInspectionConfigDO> getConfigsByPlanId(Long planId) {
        return selectList(new LambdaQueryWrapperX<PatrolInspectionConfigDO>()
                .eq(PatrolInspectionConfigDO::getPlanId, planId));
    }

    @TenantIgnore
    default List<PatrolInspectionConfigDO> selectLists(Long tenantId) {
        return selectList(new LambdaQueryWrapperX<PatrolInspectionConfigDO>()
                .eq(PatrolInspectionConfigDO::getTenantId, tenantId)
                .isNull(PatrolInspectionConfigDO::getPlanId));
    }

    @TenantIgnore
    void deleteByTenantIdAndPlanIsNull(@Param("tenantId") Long tenantId);

    @TenantIgnore
    void deleteByPlanId(@Param("planId") Long planId);

    @TenantIgnore
    List<PatrolInspectionConfigDO> selectListByIds(@Param("ids") List<Long> ids);

}
