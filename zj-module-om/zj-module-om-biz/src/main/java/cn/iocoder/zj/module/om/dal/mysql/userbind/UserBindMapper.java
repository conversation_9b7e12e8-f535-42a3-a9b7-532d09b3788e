package cn.iocoder.zj.module.om.dal.mysql.userbind;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.module.om.controller.admin.userbind.vo.UserBindExportReqVO;
import cn.iocoder.zj.module.om.controller.admin.userbind.vo.UserBindPageReqVO;
import cn.iocoder.zj.module.om.dal.dataobject.userbind.UserBindDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用户推送绑定 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserBindMapper extends BaseMapperX<UserBindDO> {

    default PageResult<UserBindDO> selectPage(UserBindPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<UserBindDO>()
                .eqIfPresent(UserBindDO::getUserId, reqVO.getUserId())
                .likeIfPresent(UserBindDO::getUserName, reqVO.getUserName())
                .eqIfPresent(UserBindDO::getWxState, reqVO.getWxState())
                .eqIfPresent(UserBindDO::getEmailState, reqVO.getEmailState())
                .eqIfPresent(UserBindDO::getDingtalkState, reqVO.getDingtalkState())
                .eqIfPresent(UserBindDO::getDingtalkAppKey, reqVO.getDingtalkAppKey())
                .eqIfPresent(UserBindDO::getDingtalkAppSecret, reqVO.getDingtalkAppSecret())
                .eqIfPresent(UserBindDO::getDingtalkAgentId, reqVO.getDingtalkAgentId())
                .eqIfPresent(UserBindDO::getDingtalkPhone, reqVO.getDingtalkPhone())
                .eqIfPresent(UserBindDO::getWxAgentId, reqVO.getWxAgentId())
                .eqIfPresent(UserBindDO::getWxCorpid, reqVO.getWxCorpid())
                .eqIfPresent(UserBindDO::getWxCorpsecret, reqVO.getWxCorpsecret())
                .eqIfPresent(UserBindDO::getWxPhone, reqVO.getWxPhone())
                .betweenIfPresent(UserBindDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(UserBindDO::getId));
    }

    default List<UserBindDO> selectList(UserBindExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<UserBindDO>()
                .eqIfPresent(UserBindDO::getUserId, reqVO.getUserId())
                .likeIfPresent(UserBindDO::getUserName, reqVO.getUserName())
                .eqIfPresent(UserBindDO::getWxState, reqVO.getWxState())
                .eqIfPresent(UserBindDO::getEmailState, reqVO.getEmailState())
                .eqIfPresent(UserBindDO::getDingtalkState, reqVO.getDingtalkState())
                .eqIfPresent(UserBindDO::getDingtalkAppKey, reqVO.getDingtalkAppKey())
                .eqIfPresent(UserBindDO::getDingtalkAppSecret, reqVO.getDingtalkAppSecret())
                .eqIfPresent(UserBindDO::getDingtalkAgentId, reqVO.getDingtalkAgentId())
                .eqIfPresent(UserBindDO::getDingtalkPhone, reqVO.getDingtalkPhone())
                .eqIfPresent(UserBindDO::getWxAgentId, reqVO.getWxAgentId())
                .eqIfPresent(UserBindDO::getWxCorpid, reqVO.getWxCorpid())
                .eqIfPresent(UserBindDO::getWxCorpsecret, reqVO.getWxCorpsecret())
                .eqIfPresent(UserBindDO::getWxPhone, reqVO.getWxPhone())
                .betweenIfPresent(UserBindDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(UserBindDO::getId));
    }

}
