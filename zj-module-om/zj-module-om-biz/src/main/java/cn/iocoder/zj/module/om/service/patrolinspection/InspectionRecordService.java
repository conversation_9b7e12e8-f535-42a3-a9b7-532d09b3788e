package cn.iocoder.zj.module.om.service.patrolinspection;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.om.controller.admin.inspectiondata.vo.InspectionRecordCreateReqVO;
import cn.iocoder.zj.module.om.controller.admin.inspectiondata.vo.InspectionRecordPageReqVO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspection.InspectionRecordDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 巡检记录 Service 接口
 *
 * <AUTHOR>
 */
public interface InspectionRecordService {
    /**
     * 删除巡检记录
     *
     * @param id 编号
     */
    void deleteInspectionRecord(Long id);
    Long createInspectionRecord(InspectionRecordCreateReqVO createReqVO);
    /**
     * 获得巡检记录
     *
     * @param id 编号
     * @return 巡检记录
     */
    InspectionRecordDO getInspectionRecord(Long id);

    /**
     * 获得巡检记录列表
     *
     * @param ids 编号
     * @return 巡检记录列表
     */
    List<InspectionRecordDO> getInspectionRecordList(Collection<Long> ids);

    /**
     * 获得巡检记录分页
     *
     * @param pageReqVO 分页查询
     * @return 巡检记录分页
     */
    PageResult<InspectionRecordDO> getInspectionRecordPage(InspectionRecordPageReqVO pageReqVO);

}
