package cn.iocoder.zj.module.om.dal.dataobject.patrolrecord;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 巡检记录设置表 DO
 *
 * <AUTHOR>
 */
@TableName("om_patrol_record_config")
@KeySequence("om_patrol_record_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PatrolRecordConfigDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 巡检记录ID
     */
    private Long recordId;
    /**
     * 巡检设置ID
     */
    private Long configId;

}
