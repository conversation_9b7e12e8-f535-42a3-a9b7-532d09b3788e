package cn.iocoder.zj.module.om.convert.patrolinspection;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.om.controller.admin.patrolinspection.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspection.PatrolInspectionDO;

/**
 * 自动巡检规则 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface PatrolInspectionConvert {

    PatrolInspectionConvert INSTANCE = Mappers.getMapper(PatrolInspectionConvert.class);

    PatrolInspectionDO convert(PatrolInspectionCreateReqVO bean);

    PatrolInspectionDO convert(PatrolInspectionUpdateReqVO bean);

    PatrolInspectionRespVO convert(PatrolInspectionDO bean);

    List<PatrolInspectionRespVO> convertList(List<PatrolInspectionDO> list);

    PageResult<PatrolInspectionRespVO> convertPage(PageResult<PatrolInspectionDO> page);

    List<PatrolInspectionExcelVO> convertList02(List<PatrolInspectionDO> list);

    PatrolInspectionDO convertList03(PatrolInspectionImportExcelVO item);

    PatrolInspectionDO convert(PatrolInspectionImportExcelVO item);
}
