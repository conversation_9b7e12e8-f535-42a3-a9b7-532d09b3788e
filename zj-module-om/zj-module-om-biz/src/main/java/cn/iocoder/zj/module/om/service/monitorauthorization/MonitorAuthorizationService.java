package cn.iocoder.zj.module.om.service.monitorauthorization;

import java.util.*;
import javax.validation.*;

import cn.iocoder.zj.module.om.controller.admin.monitorauthorization.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.monitorauthorization.MonitorAuthorizationDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import org.apache.ibatis.annotations.Param;

/**
 * 监控申请授权 Service 接口
 *
 * <AUTHOR>
 */
public interface MonitorAuthorizationService {

    /**
     * 创建监控申请授权
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMonitorAuthorization(@Valid MonitorAuthorizationCreateReqVO createReqVO);

    /**
     * 更新监控申请授权
     *
     * @param updateReqVO 更新信息
     */
    void updateMonitorAuthorization(@Valid MonitorAuthorizationUpdateReqVO updateReqVO);

    /**
     * 删除监控申请授权
     *
     * @param id 编号
     */
    void deleteMonitorAuthorization(Long id);

    /**
     * 获得监控申请授权
     *
     * @param id 编号
     * @return 监控申请授权
     */
    MonitorAuthorizationDO getMonitorAuthorization(Long id);

    /**
     * 获得监控申请授权列表
     *
     * @param ids 编号
     * @return 监控申请授权列表
     */
    List<MonitorAuthorizationDO> getMonitorAuthorizationList(Collection<Long> ids);

    /**
     * 获得监控申请授权分页
     *
     * @param pageReqVO 分页查询
     * @return 监控申请授权分页
     */
    PageResult<MonitorAuthorizationDO> getMonitorAuthorizationPage(MonitorAuthorizationPageReqVO pageReqVO);

    /**
     * 获得监控申请授权列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 监控申请授权列表
     */
    List<MonitorAuthorizationDO> getMonitorAuthorizationList(MonitorAuthorizationExportReqVO exportReqVO);

    void deleteMonitorAuthorizationInfo(Long id);

    void updateMonitorAuthorizationInfo(Long id,String authorizationType);

}
