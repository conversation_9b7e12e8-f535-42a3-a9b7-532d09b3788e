package cn.iocoder.zj.module.om.framework.rpc.config;

import cn.iocoder.cloud.module.cloudedge.api.monitor.MonitorApi;
import cn.iocoder.cloud.module.guacamole.api.OnlineSessionApi;
import cn.iocoder.zj.module.bpm.api.task.BpmTaskApi;
import cn.iocoder.zj.module.customer.api.contractInfo.ContractInfoApi;
import cn.iocoder.zj.module.infra.api.file.FileApi;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.monitor.api.cloud.CloudInfoApi;
import cn.iocoder.zj.module.monitor.api.hardware.HardWareInfoApi;
import cn.iocoder.zj.module.monitor.api.hostinfo.HostInfoApi;
import cn.iocoder.zj.module.monitor.api.storage.StorageInfoApi;
import cn.iocoder.zj.module.monitor.api.valume.VolumeApi;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.tenant.TenantApi;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.wechat.WeChatSendApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName : RpcConfiguration  //类名
 * @Description : RPC 接口  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/23  14:38
 */

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {AdminUserApi.class,
        PermissionApi.class, RoleApi.class,
        PlatformconfigApi.class,
        HostInfoApi.class,
        HardWareInfoApi.class,
        StorageInfoApi.class,
        PermissionApi.class,
        RoleApi.class,
        TenantApi.class,
        AlarmConfigApi.class,
        ContractInfoApi.class,
        BpmTaskApi.class,
        MonitorApi.class,
        WeChatSendApi.class,
        OnlineSessionApi.class,
        CloudInfoApi.class,
        FileApi.class,
        VolumeApi.class})
public class RpcConfiguration {
}
