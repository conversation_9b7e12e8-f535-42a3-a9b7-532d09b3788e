package cn.iocoder.zj.module.om.controller.admin.monitorauthorizationuser.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 用户资产授权申请 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class MonitorAuthorizationUserExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("授权资产表")
    private Long monitorAssetId;

    @ExcelProperty("申请人id")
    private Long authorizationUserId;

    @ExcelProperty("授权状态")
    private String authorizationType;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
