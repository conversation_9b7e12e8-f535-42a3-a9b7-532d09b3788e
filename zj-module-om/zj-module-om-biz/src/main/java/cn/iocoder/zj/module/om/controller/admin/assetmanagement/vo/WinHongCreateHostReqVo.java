package cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class WinHongCreateHostReqVo {

    @Schema(description = "平台ID",required = true)
    private Long platformId;

    @Schema(description = "宿主机ID",required = true)
    private String hostId;

    //虚拟机显示名称(界面显示的虚拟机名称)
    //真实名称跟显示名称一样
    //名称唯一判断
    @Schema(description = "虚拟机名称",required = true)
    private String name;

    //系统类型:0.other(默认),1.linux,2.windows
//    @Schema(description = "系统类型",required = true)
//    private Integer osType;

    //系统版本(和操作类型绑定)   需要请求接口获取类型
//    @Schema(description = "系统版本",required = true)
//    private String osVersion;

    //cpu总数
    @Schema(description = "cpu总数",required = true)
    private Integer current;

    //内存
    @Schema(description = "内存",required = true)
    private Integer memorySize;

    //磁盘
    @Schema(description = "磁盘",required = true)
        private Integer capacity;

//    //网卡
//    @Schema(description = "网卡",required = true)
//    private String showPortGroupName;
//
//    //网卡id
//    @Schema(description = "网卡id",required = true)
//    private String portGroupId;

}
