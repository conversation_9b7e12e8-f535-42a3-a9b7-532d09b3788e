package cn.iocoder.zj.module.om.controller.admin.patrolrecord;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.module.om.controller.admin.patrolrecord.vo.*;
import cn.iocoder.zj.module.om.convert.patrolrecord.PatrolRecordConvert;
import cn.iocoder.zj.module.om.dal.dataobject.patrolrecord.PatrolRecordDO;
import cn.iocoder.zj.module.om.service.patrolrecord.PatrolRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

@Tag(name = "管理后台 - 巡检记录")
@RestController
@RequestMapping("/om/patrol-record")
@Validated
public class PatrolRecordController {

    @Resource
    private PatrolRecordService patrolRecordService;

    @PostMapping("/create")
    @Operation(summary = "创建巡检记录")
    @PreAuthorize("@ss.hasPermission('om:patrol-record:create')")
    public CommonResult<Long> createPatrolRecord(@Valid @RequestBody PatrolRecordCreateReqVO createReqVO) {
        return success(patrolRecordService.createPatrolRecord(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新巡检记录")
    @PreAuthorize("@ss.hasPermission('om:patrol-record:update')")
    public CommonResult<Boolean> updatePatrolRecord(@Valid @RequestBody PatrolRecordUpdateReqVO updateReqVO) {
        patrolRecordService.updatePatrolRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除巡检记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('om:patrol-record:delete')")
    public CommonResult<Boolean> deletePatrolRecord(@RequestParam("id") Long id) {
        patrolRecordService.deletePatrolRecord(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得巡检记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('om:patrol-record:query')")
    public CommonResult<PatrolRecordRespVO> getPatrolRecord(@RequestParam("id") Long id) {
        PatrolRecordDO patrolRecord = patrolRecordService.getPatrolRecord(id);
        return success(PatrolRecordConvert.INSTANCE.convert(patrolRecord));
    }

    @GetMapping("/list")
    @Operation(summary = "获得巡检记录列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('om:patrol-record:query')")
    public CommonResult<List<PatrolRecordRespVO>> getPatrolRecordList(@RequestParam("ids") Collection<Long> ids) {
        List<PatrolRecordDO> list = patrolRecordService.getPatrolRecordList(ids);
        return success(PatrolRecordConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得巡检记录分页")
    @PreAuthorize("@ss.hasPermission('om:patrol-record:query')")
    public CommonResult<PageResult<PatrolRecordRespVO>> getPatrolRecordPage(@Valid PatrolRecordPageReqVO pageVO) {
        return success(patrolRecordService.getPatrolRecordPage(pageVO));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出巡检记录 Excel")
    @PreAuthorize("@ss.hasPermission('om:patrol-record:export')")
    @OperateLog(type = EXPORT)
    public void exportPatrolRecordExcel(@Valid PatrolRecordExportReqVO exportReqVO,
                                        HttpServletResponse response) throws IOException {
        List<PatrolRecordDO> list = patrolRecordService.getPatrolRecordList(exportReqVO);
        // 导出 Excel
        List<PatrolRecordExcelVO> datas = PatrolRecordConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "巡检记录.xls", "数据", PatrolRecordExcelVO.class, datas);
    }

    @GetMapping("/uploadWord")
    @Operation(summary = "巡检报告下载")
    @PreAuthorize("@ss.hasPermission('om:patrol-record:upload')")
    public void uploadWord(@RequestParam("id") Long id, HttpServletResponse response) {
        patrolRecordService.uploadWord(id, response);
    }

}
