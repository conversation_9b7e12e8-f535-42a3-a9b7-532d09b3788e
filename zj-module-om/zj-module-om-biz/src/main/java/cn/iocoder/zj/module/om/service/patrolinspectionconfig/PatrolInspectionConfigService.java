package cn.iocoder.zj.module.om.service.patrolinspectionconfig;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.om.controller.admin.patrolinspectionconfig.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspectionconfig.PatrolInspectionConfigDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 巡检设置 Service 接口
 *
 * <AUTHOR>
 */
public interface PatrolInspectionConfigService {

    /**
     * 创建巡检设置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPatrolInspectionConfig(@Valid PatrolInspectionConfigCreateReqVO createReqVO);

    /**
     * 更新巡检设置
     *
     * @param updateReqVO 更新信息
     */
    void updatePatrolInspectionConfig(@Valid PatrolInspectionConfigUpdateReqVO updateReqVO);

    /**
     * 删除巡检设置
     *
     * @param id 编号
     */
    void deletePatrolInspectionConfig(Long id);

    /**
     * 获得巡检设置
     *
     * @param id 编号
     * @return 巡检设置
     */
    PatrolInspectionConfigDO getPatrolInspectionConfig(Long id);

    /**
     * 根据指标名称获得巡检设置
     *
     * @param metricName 指标名称
     * @return 巡检设置
     */

    PatrolInspectionConfigDO getPatrolInspectionConfigByMetricName(String metricName);

    /**
     * 获得巡检设置列表
     *
     * @param ids 编号
     * @return 巡检设置列表
     */
    List<PatrolInspectionConfigDO> getPatrolInspectionConfigList(Collection<Long> ids);

    /**
     * 获得巡检设置分页
     *
     * @param pageReqVO 分页查询
     * @return 巡检设置分页
     */
    PageResult<PatrolInspectionConfigDO> getPatrolInspectionConfigPage(PatrolInspectionConfigPageReqVO pageReqVO);

    /**
     * 获得巡检设置列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 巡检设置列表
     */
    List<PatrolInspectionConfigDO> getPatrolInspectionConfigList(PatrolInspectionConfigExportReqVO exportReqVO);

    /**
     * 批量创建巡检设置
     *
     * @param createReqVOS 创建信息
     * @return 创建的巡检设置编号列表
     */
    List<Long> createPatrolInspectionConfigs(List<PatrolInspectionConfigCreateReqVO> createReqVOS);

    /**
     * 批量更新巡检设置
     *
     * @param updateReqVOs 更新信息列表
     */
    void updatePatrolInspectionConfigs(List<PatrolInspectionConfigUpdateReqVO> updateReqVOs);

    /**
     * 根据多个指标名称和租户ID查询巡检设置
     *
     * @param metricNames 指标名称列表
     * @param tenantId    租户ID
     * @return 巡检设置列表
     **/
    List<PatrolInspectionConfigDO> getConfigsByMetricNamesAndTenantId(List<String> metricNames, Long tenantId);

    /**
     * 根据planId查询巡检设置
     *
     * @param planId 计划ID
     * @return 巡检设置列表
     */
    List<PatrolInspectionConfigDO> getConfigsByPlanId(Long planId);


    List<PatrolInspectionConfigDO> listByTenantIdOrPlanId(Long tenantId, Long planId);

    /**
     * 执行巡检
     */
    void executePatrol(PatrolExecuteReqVO reqVO);

    /**
     * 停止巡检
     */
    void stopPatrol(Long tenantId);

    /**
     * 获取缓存服务存储巡检状态
     */
    Boolean getComplete(Long tenantId);

}
