package cn.iocoder.zj.module.om.dal.dataobject.workorder;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 工单管理数据 DO
 *
 * <AUTHOR>
 */
@TableName("om_work_order")
@KeySequence("om_work_order_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkOrderDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * 平台名称
     */
    private String platformName;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     *资源名称
     */
    private String sourceName;
    /**
     * 资源uuid
     */
    private String sourceUuid;
    /**
     * 事件说明
     */
    private String description;
    /**
     * 事件
     */
    private String event;
    /**
     * 处理人ID
     */
    private Long auditorId;
    /**
     * 处理人名称
     */
    private String auditorName;
    /**
     * 实施人ID
     */
    private String enforcerId;
    /**
     * 实施人名称
     */
    private String enforcerName;
    /**
     * 开始实施时间
     */
    private Date enforcerStartTime;
    /**
     * 结束实施时间
     */
    private Date enforcerEndTime;
    /**
     * 资源类型对应的字典编码
     */
    private String dictCode;
    /**
     * 资源类型对应的字典名称
     */
    private String dictName;
    /**
     * 工单类型对应的字典编码
     */
    private String typeCode;
    /**
     * 云平台告警id
     */
    private String alarmId;
    /**
     * 资产告警id
     */
    private String gatherId;
    /**
     * 合同id
     */
    private String contractId;
    /**
     * 工单类型对应的字典名称
     */
    private String typeName;
    /**
     * 是否完成
     */
    private Integer isFinished;
    /**
     * 审批状态，consent同意，reject驳回，wait待审核
     */
    private String ratifyStatus;
    /**
     * 准备状态，valid有效的，invalid无效
     */
    private String readinessStatus;
    /**
     * 创建类型：auto自动创建，hand手动创建
     */
    private String createType;
    /**
     * 自动创建时的执行参数
     */
    private String param;
    /**
     * 操作（自动创建时填写，operateHost操作云主机，operateHardware操作宿主机，operateStorage操作主存储，createHost创建云主机，createHardware创建宿主机）
     */
    private String operation;
    /**
     * 创建人名称
     */
    private String creatorName;
    /**
     * 是否已读0未读，1已读
     */
    private Integer isRead;

}
