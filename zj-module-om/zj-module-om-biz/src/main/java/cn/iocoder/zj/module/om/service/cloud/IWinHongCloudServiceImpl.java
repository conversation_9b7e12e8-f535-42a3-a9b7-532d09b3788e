package cn.iocoder.zj.module.om.service.cloud;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.monitor.api.hostinfo.HostInfoApi;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo.ResourceOperateLog;
import cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo.WinHongCreateHostReqVo;
import cn.iocoder.zj.module.om.controller.admin.vncinfo.vo.VncInfoVo;
import cn.iocoder.zj.module.om.dal.mysql.assetmanagement.AssetManagementMapper;
import cn.iocoder.zj.module.om.dal.redis.zstack.WinHongAccessTokenRedisDAO;
import cn.iocoder.zj.module.om.framework.websocket.WebSocketClient;
import cn.iocoder.zj.module.om.service.zstack.core.WinHongApiConstant;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.frameworkset.util.StringUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service
@Log4j2
public class IWinHongCloudServiceImpl implements IWinHongCloudService {
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    WinHongAccessTokenRedisDAO winHongAccessTokenRedisDAO;

    @Resource
    HostInfoApi hostInfoApi;

    @Resource
    AssetManagementMapper assetManagementMapper;

    @Resource
    WebSocketClient webSocketClient;

    @Override
    public CommonResult<Map<String, String>> operateVmInstance(String uuid, Long platformId, String type, String actions) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        HostInfoRespCreateReqDTO hostDTO = hostInfoApi.getByUuid(uuid).getData();
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(platformId).getData();
        String token = winHongAccessTokenRedisDAO.get("winhong:" + platformId).get("sessionId").toString();
        HostInfoRespCreateReqDTO reqDTO = new HostInfoRespCreateReqDTO();
        String url = "";
        Method method = null;
        String state;
        Map<String, String> reuslt = new HashMap<>();
        reuslt.put("success", "true");
        String op = winHongHostOperateConvert(actions);
        reuslt.put("msg", "正在" + op + "(" + hostDTO.getName() + ")");
        if (actions.equals("start")) {
            //启动云主机
            url = platform.getUrl() + WinHongApiConstant.PATCH_HOST_START.replace("{domainId}", uuid);
            method = Method.PATCH;
            reqDTO.setState("Starting");
            state = "Running";
        } else if (actions.equals("stop")) {
            url = platform.getUrl() + WinHongApiConstant.PATCH_HOST_SAVE.replace("{domainId}", uuid);
            method = Method.PATCH;
            reqDTO.setState("Stopping");
            state = "Stopped";
        } else if (actions.equals("reboot")) {
            url = platform.getUrl() + WinHongApiConstant.PATCH_HOST_REBOOT.replace("{domainId}", uuid);
            method = Method.PATCH;
            reqDTO.setState("Rebooting");
            state = "Running";
        } else if (actions.equals("destroy")) {
            //删除云主机
            url = platform.getUrl() + WinHongApiConstant.PATCH_HOST_DELETE.replace("{domainId}", uuid);
            HttpResponse res = new HttpRequest(url)
                    .method(Method.DELETE)
                    .cookie("SESSION=" + token)
                    .contentType("application/json;charset=UTF-8")
                    .execute();
            hostInfoApi.deleteAssetAndHostJson(uuid);
            hostDTO.setState("Destroying");
            Runnable data = (() -> {
                //另起线程处理请求后的结果
                updateHostInfo(res, "destroy", uuid, platform, "", hostDTO.getName(), token, loginUser, reqDTO.getState());
            });
            Thread thread = new Thread(data);
            thread.start();
//            hostInfoApi.updateHostSingle(hostDTO);
            return CommonResult.success(reuslt);
        } else {
            state = "";
        }
        String finalState = state;
        HttpResponse res = new HttpRequest(url)
                .method(method)
                .cookie("SESSION=" + token)
                .contentType("application/json;charset=UTF-8")
                .execute();

        reqDTO.setUuid(uuid);
        hostInfoApi.updateHostSingle(reqDTO);
        Runnable data = (() -> {
            //另起线程处理请求后的结果
            updateHostInfo(res, actions, uuid, platform, finalState, hostDTO.getName(), token, loginUser, reqDTO.getState());
        });
        Thread thread = new Thread(data);
        thread.start();
        hostInfoApi.updateHostSingle(hostDTO);
        return CommonResult.success(reuslt);
    }

    @Override
    public Map<String, String> createWinHongVm(WinHongCreateHostReqVo reqVo) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(reqVo.getPlatformId()).getData();
        String token = winHongAccessTokenRedisDAO.get("winhong:" + reqVo.getPlatformId()).get("sessionId").toString();
        Map<String, String> result = new HashMap<>();
        Map<String, Object> createMap = new HashMap<>();
        createMap.put("hostId", reqVo.getHostId());
        createMap.put("name", reqVo.getName());
        createMap.put("remark", null);
        createMap.put("bus", 1);
        createMap.put("osType", 1);
        createMap.put("osVersion", "CentOS 6/7（64 位）");
        createMap.put("blkiotune", 1);
        createMap.put("isStartNewDomain", true);
        createMap.put("isHa", false);
        createMap.put("autoMigrate", true);
        //查询cpu
        JSONObject cpu = new JSONObject();
        cpu.put("sockets", 1);
        cpu.put("cores", 24);
        cpu.put("arch", 2);
        cpu.put("shares", 2);
        cpu.put("current", reqVo.getCurrent());
        cpu.put("mode", 2);
        cpu.put("gurantees", null);
        cpu.put("bindCpu", null);
        cpu.put("quota", 100);
        createMap.put("cpu", cpu);
        double memorySize = reqVo.getMemorySize();
        createMap.put("memorySize", (long) (memorySize * 1024 * 1024 * 1024));
        createMap.put("unit", "GB");

        JSONObject memory = new JSONObject();
        cpu.put("locked", null);
        cpu.put("priority", 1);
        cpu.put("limit", null);
        cpu.put("autoMem", false);
        cpu.put("mode", null);
        cpu.put("isHugePages", false);
        createMap.put("memory", memory);

        JSONArray diskDevices = new JSONArray();
        JSONObject disk = new JSONObject();
        disk.put("bus", 1);
        disk.put("cache", 4);
        disk.put("source", 1);
        disk.put("oldPool", null);
        disk.put("oldVol", null);
        disk.put("poolName", null);
        disk.put("volName", null);
        double capacity = reqVo.getCapacity();
        disk.put("capacity", (long) (capacity * 1024 * 1024 * 1024));
        disk.put("type", 1);
        disk.put("isreadonly", false);
        disk.put("preallocation", "off");
        disk.put("isShow", true);
        disk.put("isshowcapacity", true);
        disk.put("shareable", false);
        disk.put("ioHangTimeout", 120000);
        disk.put("unit", "GB");
        disk.put("hidediskDeviceType", true);
        diskDevices.add(disk);
        createMap.put("diskDevices", diskDevices);

        HttpResponse resp = HttpRequest.post(platform.getUrl() + WinHongApiConstant.POST_DOMAINS)
                .body(JSONUtil.toJsonStr(createMap))
                .cookie("SESSION=" + token)
                .contentType("application/json;charset=UTF-8")
                .execute();
        Runnable run = (() -> {
            createHostInfo(resp.body(), "createVm", platform, token, reqVo.getName(), loginUser);
        });
        Thread thread = new Thread(run);
        thread.start();
        Map<String, String> reuslt = new HashMap<>();
        reuslt.put("success", "true");
        reuslt.put("msg", "正在创建云主机(" + reqVo.getName() + ")");
        return reuslt;

    }

    @Override
    public VncInfoVo getVncInfo(HostInfoRespCreateReqDTO hostInfo) {
        String token = winHongAccessTokenRedisDAO.get("winhong:" + hostInfo.getPlatformId()).get("sessionId").toString();
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(hostInfo.getPlatformId()).getData();
        
        // 构建请求URL
        String vncInfoUrl = platform.getUrl() + WinHongApiConstant.GET_VNC_INFO.replace("{domainId}", hostInfo.getUuid());
        
        // 发送HTTP请求获取VNC信息
        HttpResponse res = HttpRequest.get(vncInfoUrl)
                .cookie("SESSION=" + token)
                .contentType("application/json;charset=UTF-8")
                .execute();
                
        // 解析响应数据
        JSONObject jsonObject = JSONObject.parseObject(res.body());
        VncInfoVo vncInfoVo = new VncInfoVo();
        vncInfoVo.setWebsocketHost(jsonObject.getString("hostIp"));
        vncInfoVo.setWebsocketPort(jsonObject.getInteger("vncPort"));
        vncInfoVo.setVncToken(jsonObject.getString("vncToken"));
        vncInfoVo.setWebsocketPath(buildWebsocketPath(platform.getConsoleIp(), vncInfoVo.getWebsocketHost(), vncInfoVo.getWebsocketPort()));
        vncInfoVo.setUseSsl(true);
        return vncInfoVo;
    }

    /**
     * 构建Websocket路径
     *
     * @param platformUrl 平台URL
     * @param hostIp 主机IP
     * @param hostPort 主机端口
     * @return websocket路径
     */
    private String buildWebsocketPath(String platformUrl, String hostIp, Integer hostPort) {
        return convertUrl(platformUrl) + "/websockify?host_ip=" + hostIp + "&host_port=" + hostPort;
    }

    /**
     * 转换URL从HTTPS到WSS
     *
     * @param url 原始URL
     * @return 转换后的URL
     */
    private static String convertUrl(String url) {
        return url.replaceAll("https?://", "wss://").replace(":443", "");
    }

    private void createHostInfo(String body, String operation, PlatformconfigDTO platform, String token, String name, LoginUser loginUser) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        JSONObject result = new JSONObject();
        String op = winHongHostOperateConvert(operation);

        // 检查是否有 taskId
        String taskId = jsonObject.getString("taskId");
        if (taskId == null) {
            String msg = jsonObject.getString("message");
            result.put("success", "false");
            result.put("msg", op + "(" + name + ")失败,错误信息:" + msg);
            createOperateLog(name, "", op, "error", "host", msg, platform, loginUser);
            webSocketClient.sendMessage(loginUser.getId().toString(), result.toJSONString());
            return;
        }

        // 初始化 HostInfoRespCreateReqDTO 和 URL
        HostInfoRespCreateReqDTO reqDTO = new HostInfoRespCreateReqDTO();
        String baseUrl = platform.getUrl();
        String sessionCookie = "SESSION=" + token;
        String taskUrl = baseUrl + WinHongApiConstant.GET_TASK_INFO.replace("{taskId}", taskId);

        try {
            while (true) {
                HttpResponse res = HttpRequest.get(taskUrl)
                        .cookie(sessionCookie)
                        .contentType("application/json;charset=UTF-8")
                        .execute();

                // 检查 HTTP 响应代码
                if (res.getStatus() != 200) {
                    JSONObject errorObj = jsonObject.getJSONObject("error");
                    String msg = getErrorMsg(errorObj);
                    result.put("msg", op + "(" + name + ")失败，错误信息:" + msg+",请至"+platform.getName()+"查看详情");
                    result.put("success", "false");
                    createOperateLog(name, reqDTO.getUuid(), op, "error", "host", msg, platform, loginUser);
                    webSocketClient.sendMessage(loginUser.getId().toString(), result.toJSONString());
                    return;
                }

                JSONObject taskResult = JSONObject.parseObject(res.body());
                int status = taskResult.getInteger("status");

                // 任务结束或失败处理
                if (status == 3) {
                    // 任务成功，处理 domainId
                    String domainId = taskResult.getString("domainId");
                    String domainUrl = baseUrl + WinHongApiConstant.GET_HOST_DOMAINS + "?id=" + domainId;

                    HttpResponse domainResponse = HttpRequest.get(domainUrl)
                            .cookie(sessionCookie)
                            .contentType("application/json;charset=UTF-8")
                            .execute();

                    // 检查域请求的 HTTP 响应代码
                    if (domainResponse.getStatus() != 200) {
                        result.put("success", "false");
                        result.put("msg", op + "(" + name + ")失败,错误信息: 无法获取域信息，HTTP状态码: " + domainResponse.getStatus());
                        createOperateLog(name, "", op, "error", "host", "HTTP错误", platform, loginUser);
                        webSocketClient.sendMessage(loginUser.getId().toString(), result.toJSONString());
                        return;
                    }

                    JSONObject domain = JSONObject.parseObject(domainResponse.body())
                            .getJSONArray("data").getJSONObject(0);

                    // 填充 reqDTO 对象
                    reqDTO.setUuid(domain.getString("uuid"));
                    reqDTO.setName(domain.getString("name"));
                    reqDTO.setPlatformId(platform.getId());
                    reqDTO.setPlatformName(platform.getName());
                    reqDTO.setTypeName(platform.getTypeCode());
                    reqDTO.setState(stateConvert(domain.getString("status")));

                    // 获取 summary 信息
                    String summaryUrl = baseUrl + WinHongApiConstant.GET_SUMMARY.replace("{domainId}", domainId);
                    HttpResponse summaryResponse = HttpRequest.get(summaryUrl)
                            .cookie(sessionCookie)
                            .contentType("application/json;charset=UTF-8")
                            .execute();

                    JSONObject summary = JSONObject.parseObject(summaryResponse.body());
                    reqDTO.setArchitecture(summary.getString("cpuArchitecture"));
                    reqDTO.setGuestOsType(domain.getString("osVersion"));
                    reqDTO.setIp(StringUtil.isNotEmpty(domain.getString("ip")) ? domain.getString("ip") : "");

                    // 处理 bridgeInterfaces
                    JSONArray bridgeInterfaces = domain.getJSONArray("bridgeInterfaces");
                    if (!bridgeInterfaces.isEmpty()) {
                        JSONObject bridgeInterface = bridgeInterfaces.getJSONObject(0);
                        reqDTO.setMac(StringUtil.isNotEmpty(bridgeInterface.getString("mac")) ? bridgeInterface.getString("mac") : "");
                    } else {
                        reqDTO.setMac("");
                    }

                    // 设置其他默认值
                    reqDTO.setMemoryUsed(BigDecimal.ZERO);
                    reqDTO.setDiskUsed(BigDecimal.ZERO);
                    reqDTO.setCpuUsed(BigDecimal.ZERO);
                    reqDTO.setVCreateDate(new Date());

                    // 创建 host 信息
                    hostInfoApi.createHostSingle(reqDTO);
                    result.put("uuid", domainId);
                    result.put("msg", op + "(" + name + ")成功");
                    result.put("success", "true");
                    createOperateLog(name, reqDTO.getUuid(), op, "success", "host", "", platform, loginUser);
                    webSocketClient.sendMessage(loginUser.getId().toString(), result.toJSONString());
                    break;

                } else if (status == 4) {
                    // 任务失败
                    JSONObject errorObj = jsonObject.getJSONObject("error");
                    String msg = getErrorMsg(errorObj);
                    result.put("msg", op + "(" + name + ")失败,错误信息:" + msg);
                    result.put("success", "false");
                    createOperateLog(name, reqDTO.getUuid(), op, "error", "host", msg, platform, loginUser);
                    webSocketClient.sendMessage(loginUser.getId().toString(), result.toJSONString());
                    break;

                } else if (status == 1) {
                    // 任务进行中，等待
                    Thread.sleep(2000);
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();  // 恢复线程中断状态
            result.put("success", "false");
            result.put("msg", "操作中断");
            webSocketClient.sendMessage(loginUser.getId().toString(), result.toJSONString());
        }
    }

    private String stateConvert(String state) {
        String target = "";
        switch (state) {
            case "1":
                target = "Running";
                break;
            case "starting":
                target = "Starting";
                break;
            case "Stopping":
                target = "Stopping";
                break;
            case "2":
                target = "Stopped";
                break;
            case "resetting":
                target = "Rebooting";
                break;
            case "deleting":
                target = "Destroying";
                break;
            case "suspend":
                target = "Stopped";
                break;
            case "suspending":
                target = "Stopping";
                break;
            default:
                target = "Unknown";
        }
        return target;
    }

    public void updateHostInfo(HttpResponse resp, String operation, String hostUuid, PlatformconfigDTO platform, String state, String name, String token, LoginUser loginUser, String reqState) {
        JSONObject respBody = JSONObject.parseObject(resp.body());
        String op = winHongHostOperateConvert(operation);
        JSONObject result = new JSONObject();
        HostInfoRespCreateReqDTO reqDTO = new HostInfoRespCreateReqDTO();
        if (respBody.get("taskId") == null) {
            //失败返回错误信息
            String msg = respBody.get("message").toString();
            result.put("success", "false");
            result.put("msg", op + "(" + name + ")失败,错误信息:" + msg);
            createOperateLog(name, "", op, "error", "host", msg, platform, loginUser);
            webSocketClient.sendMessage(hostUuid, result.toJSONString());
            return;
        }
        Map<String, String> processState = getProcessResult(platform, token, respBody.get("taskId").toString(), reqState);


        result.put("uuid", hostUuid);
        if (processState.get("success").equals("true")) {
            //
            if (operation.equals("createVm")){
                hostInfoApi.createHostSingle(reqDTO);
            }else if (operation.equals("destroy")){
                hostInfoApi.deleteVm(hostInfoApi.getByUuid(hostUuid).getData().getId());
            }else {
                reqDTO.setUuid(hostUuid);
                reqDTO.setState(state);
                hostInfoApi.updateHostSingle(reqDTO);
            }
        } else {
            result.put("success", "false");
            result.put("msg", op + "(" + name + ")失败，错误信息:" + processState.get("msg")+",请至"+platform.getName()+"查看详情");
            //创建操作记录
            createOperateLog(name, hostUuid, op, "error", "host", processState.get("msg"), platform, loginUser);
            webSocketClient.sendMessage(hostUuid, result.toJSONString());
            return;
        }
        createOperateLog(name, hostUuid, op, "success", "host", "", platform, loginUser);
        result.put("success", "true");
        result.put("msg", op + "(" + name + ")成功");
        webSocketClient.sendMessage(hostUuid, result.toJSONString());
    }

    public Map<String, String> getProcessResult(PlatformconfigDTO platform, String token, String taskId, String state) {
        Map<String, String> processState = new HashMap<>();
        int sleep = state.equals("Rebooting") ? 60000 : 2000;
        String url = platform.getUrl() + WinHongApiConstant.GET_TASK_INFO.replace("{taskId}", taskId);
        String sessionCookie = "SESSION=" + token;

        while (true) {
            HttpResponse res = HttpRequest.get(url)
                    .cookie(sessionCookie)
                    .contentType("application/json;charset=UTF-8")
                    .execute();

            JSONObject result = JSONObject.parseObject(res.body());
            int code = res.getStatus();

            //检查返回的 HTTP 响应代码
            if (code != 200) {
                processState.put("success", "false");
                processState.put("msg", result.getString("message"));
                return processState;
            }

            int status = result.getInteger("status");

            // 检查任务状态
            if (status == 3) {
                // 任务成功结束
                break;
            } else if (status == 4) {
                // 任务失败
                processState.put("success", "false");
                processState.put("msg", result.getString("message"));
                return processState;
            } else if (status == 1) {
                // 任务仍在运行，等待
                try {
                    Thread.sleep(sleep);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();  // 恢复线程中断状态
                    processState.put("success", "false");
                    processState.put("msg", "Thread was interrupted");
                    return processState;
                }
            }
        }

        // 任务成功完成
        processState.put("success", "true");
        processState.put("msg", "");
        return processState;
    }


    public String getErrorMsg(JSONObject obj) {
        String msg = "操作失败！";
        if (jodd.util.StringUtil.isNotBlank(obj.getString("elaboration"))) {
            msg = obj.getString("elaboration");
        } else if (jodd.util.StringUtil.isNotBlank(obj.getString("details"))) {
            msg = obj.getString("details");
        } else if (jodd.util.StringUtil.isNotBlank(obj.getString("description"))) {
            msg = obj.getString("description");
        }
        return msg;
    }


    public void createOperateLog(String name, String uuid, String operation, String result, String resourceType, String msg, PlatformconfigDTO platform, LoginUser loginUser) {
        ResourceOperateLog operateLog = new ResourceOperateLog();
        operateLog.setCreator(String.valueOf(loginUser.getId()));
        operateLog.setOperation(operation);
        operateLog.setResourceType(resourceType);
        operateLog.setResult(result);
        operateLog.setResourceUuid(uuid);
        operateLog.setResourceName(name);
        operateLog.setPlatformName(platform.getName());
        operateLog.setPlatformId(platform.getId());
        operateLog.setContent(msg);
        assetManagementMapper.createOperateLog(operateLog);
    }


    public String winHongHostOperateConvert(String operation) {
        String op = "";
        switch (operation) {
            case "createVm":
                op = "创建虚拟机";
                break;
            case "stop":
                op = "停止虚拟机";
                break;
            case "start":
                op = "启动虚拟机";
                break;
            case "reboot":
                op = "重启虚拟机";
                break;
            case "destroy":
                op = "回收虚拟机";
                break;
            case "creating":
                op = "创建中";
                break;
            case "stopping":
                op = "停止中";
                break;
            case "starting":
                op = "启动中";
                break;
            case "rebooting":
                op = "重启中";
                break;
            case "destroying":
                op = "回收中";
                break;
        }
        return op;
    }
}
