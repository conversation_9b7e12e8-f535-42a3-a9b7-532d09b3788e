package cn.iocoder.zj.module.om.controller.admin.monitorasset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 监控资产 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class MonitorAssetExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("资产id")
    private String assetId;

    @ExcelProperty("资产名称")
    private String assetName;

    @ExcelProperty("协议类型")
    private String protocol;

    @ExcelProperty("协议端口")
    private Integer protocolProd;

    @ExcelProperty("账户类型")
    private String certificate;

    @ExcelProperty("平台id")
    private Long platformId;

    @ExcelProperty("平台名称")
    private String platformName;

    @ExcelProperty("资产类型")
    private Integer assetType;

    @ExcelProperty("授权状态")
    private String authorizationType;

    @ExcelProperty("授权有效时间")
    private LocalDateTime authorizationTime;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("用户名称")
    private String username;

    @ExcelProperty("账号密码")
    private String password;

}
