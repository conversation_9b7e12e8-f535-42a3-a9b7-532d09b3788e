package cn.iocoder.zj.module.om.service.patrolinspectionconfig;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.om.Inspection.service.PatrolMetricService;
import cn.iocoder.zj.module.om.controller.admin.patrolinspectionconfig.vo.*;
import cn.iocoder.zj.module.om.convert.patrolinspectionconfig.PatrolInspectionConfigConvert;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspectionconfig.PatrolInspectionConfigDO;
import cn.iocoder.zj.module.om.dal.mysql.patrolinspectionconfig.PatrolInspectionConfigMapper;
import cn.iocoder.zj.module.system.api.tenant.TenantApi;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.om.enums.ErrorCodeConstants.PATROL_INSPECTION_CONFIG_NOT_EXISTS;

/**
 * 巡检设置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PatrolInspectionConfigServiceImpl implements PatrolInspectionConfigService {

    @Resource
    private PatrolInspectionConfigMapper patrolInspectionConfigMapper;

    @Resource
    private PatrolMetricService patrolMetricService;

    @Resource
    private RedisTemplate<String, Boolean> redisTemplate;

    @Resource
    private TenantApi tenantApi;



    @Override
    public Long createPatrolInspectionConfig(PatrolInspectionConfigCreateReqVO createReqVO) {
        // 插入
        PatrolInspectionConfigDO patrolInspectionConfig = PatrolInspectionConfigConvert.INSTANCE.convert(createReqVO);
        patrolInspectionConfigMapper.insert(patrolInspectionConfig);
        // 返回
        return patrolInspectionConfig.getId();
    }

    @Override
    public void updatePatrolInspectionConfig(PatrolInspectionConfigUpdateReqVO updateReqVO) {
        // 校验存在
        validatePatrolInspectionConfigExists(updateReqVO.getId());
        // 更新
        PatrolInspectionConfigDO updateObj = PatrolInspectionConfigConvert.INSTANCE.convert(updateReqVO);
        patrolInspectionConfigMapper.updateById(updateObj);
    }

    @Override
    public void deletePatrolInspectionConfig(Long id) {
        // 校验存在
        validatePatrolInspectionConfigExists(id);
        // 删除
        patrolInspectionConfigMapper.deleteById(id);
    }

    private void validatePatrolInspectionConfigExists(Long id) {
        if (patrolInspectionConfigMapper.selectById(id) == null) {
            throw exception(PATROL_INSPECTION_CONFIG_NOT_EXISTS);
        }
    }

    @Override
    public PatrolInspectionConfigDO getPatrolInspectionConfig(Long id) {
        return patrolInspectionConfigMapper.selectById(id);
    }

    @Override
    public PatrolInspectionConfigDO getPatrolInspectionConfigByMetricName(String metricName) {
        return patrolInspectionConfigMapper.selectOne("metric_name", metricName);
    }

    @Override
    public List<PatrolInspectionConfigDO> getPatrolInspectionConfigList(Collection<Long> ids) {
        return patrolInspectionConfigMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<PatrolInspectionConfigDO> getPatrolInspectionConfigPage(PatrolInspectionConfigPageReqVO pageReqVO) {
        return patrolInspectionConfigMapper.selectPage(pageReqVO);
    }

    @Override
    public List<PatrolInspectionConfigDO> getPatrolInspectionConfigList(PatrolInspectionConfigExportReqVO exportReqVO) {
        return patrolInspectionConfigMapper.selectList(exportReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> createPatrolInspectionConfigs(List<PatrolInspectionConfigCreateReqVO> createReqVOS) {
        //查询当前租户id
        Long tenantId = SecurityFrameworkUtils.getLoginUser().getTenantId();
        //查询当前租户下planId为空的巡检设置 有先删除
        List<PatrolInspectionConfigDO> existConfigs = patrolInspectionConfigMapper
                .selectList(new LambdaQueryWrapperX<PatrolInspectionConfigDO>()
                        .eq(PatrolInspectionConfigDO::getTenantId, tenantId)
                        .isNull(PatrolInspectionConfigDO::getPlanId));
        if (CollectionUtil.isNotEmpty(existConfigs)) {
            patrolInspectionConfigMapper.deleteByTenantIdAndPlanIsNull(tenantId);
        }
        List<Long> ids = new ArrayList<>(createReqVOS.size());
        for (PatrolInspectionConfigCreateReqVO createReqVO : createReqVOS) {
            PatrolInspectionConfigDO patrolInspectionConfig = BeanUtil.toBean(createReqVO, PatrolInspectionConfigDO.class);
            patrolInspectionConfig.setTenantId(tenantId);
            patrolInspectionConfigMapper.insert(patrolInspectionConfig);
            ids.add(patrolInspectionConfig.getId());
        }
        return ids;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePatrolInspectionConfigs(List<PatrolInspectionConfigUpdateReqVO> updateReqVOs) {
        for (PatrolInspectionConfigUpdateReqVO updateReqVO : updateReqVOs) {
            updatePatrolInspectionConfig(updateReqVO);
        }
    }

    @Override
    @TenantIgnore
    public List<PatrolInspectionConfigDO> getConfigsByMetricNamesAndTenantId(List<String> metricNames, Long tenantId) {
        return patrolInspectionConfigMapper.getConfigsByMetricNamesAndTenantId(metricNames, tenantId);
    }

    @Override
    @TenantIgnore
    public List<PatrolInspectionConfigDO> getConfigsByPlanId(Long planId) {
        return patrolInspectionConfigMapper.selectList(new LambdaQueryWrapperX<PatrolInspectionConfigDO>()
                .eq(PatrolInspectionConfigDO::getPlanId, planId));
    }

    @Override
    @TenantIgnore
    public List<PatrolInspectionConfigDO> listByTenantIdOrPlanId(Long tenantId, Long planId) {
        LambdaQueryWrapperX<PatrolInspectionConfigDO> queryWrapper = new LambdaQueryWrapperX<PatrolInspectionConfigDO>()
                .eq(PatrolInspectionConfigDO::getTenantId, tenantId);
        if (planId != null) {
            queryWrapper.eq(PatrolInspectionConfigDO::getPlanId, planId);
        } else {
            queryWrapper.isNull(PatrolInspectionConfigDO::getPlanId);
        }
        return patrolInspectionConfigMapper.selectList(queryWrapper);
    }

    @Override
    @TenantIgnore
    public void executePatrol(PatrolExecuteReqVO reqVO) {
        //异步执行巡检任务
        LoginUser user = SecurityFrameworkUtils.getLoginUser();
        Long tenantId = user.getTenantId();
        if (ObjectUtil.isNull(reqVO.getTenantId())) {
            reqVO.setTenantId(tenantId);
        }
        patrolMetricService.executePatrol(reqVO, user);
    }

    @Override
    @TenantIgnore
    public void stopPatrol(Long tenantId) {
        //在缓存服务添加一个是否插入日志的标识
        Long login_tenantId = SecurityFrameworkUtils.getLoginUser().getTenantId();
        String cacheKey = "patrol_insert_log_";
        if (login_tenantId == 1L) {
            cacheKey = cacheKey + login_tenantId + "_" + tenantId;
        } else {
            cacheKey = cacheKey + tenantId;
        }
        redisTemplate.opsForValue().set(cacheKey, false);
    }

    @Override
    @TenantIgnore
    public Boolean getComplete(Long tenantId) {
        // 使用缓存服务存储巡检状态
        Long login_tenantId = SecurityFrameworkUtils.getLoginUser().getTenantId();
        String cacheKey = "patrol_status_";
        if (login_tenantId == 1L) {
            cacheKey = cacheKey + login_tenantId + "_" + tenantId;
        } else {
            cacheKey = cacheKey + tenantId;
        }
        return redisTemplate.opsForValue().get(cacheKey);
    }

}
