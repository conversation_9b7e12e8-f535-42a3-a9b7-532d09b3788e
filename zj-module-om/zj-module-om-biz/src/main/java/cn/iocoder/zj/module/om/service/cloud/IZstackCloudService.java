package cn.iocoder.zj.module.om.service.cloud;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo.ZstackCreateHostReqVo;
import cn.iocoder.zj.module.om.controller.admin.vncinfo.vo.VncInfoVo;

import java.util.Map;

public interface IZstackCloudService {
    String createVmInstance(String url,String token);

    Map<String,Object> getVmClusters(Long platformId, Integer pageNo, Integer pageSize,String queryData);

    Map<String,Object> getDiskOfferings(Long platformId, Integer pageNo, Integer pageSize,String queryData,Long diskSize);

    Map<String,Object> getImages(Long platformId, Integer pageNo, Integer pageSize,String queryData);

    Map<String,Object> getInstanceOfferings(Long platformId, Integer pageNo, Integer pageSize,String queryData);

    CommonResult<Map<String,String>> createHostToZstack(ZstackCreateHostReqVo reqVo);

    CommonResult<Map<String, String>> operateVmInstance(String uuid, Long platformId, String type, String actions);
    Boolean getPermission(LoginUser loginUser);

    String ZstackHostOperateConvert(String operation);

    VncInfoVo getWebSocketUrl(HostInfoRespCreateReqDTO hostInfo);

    VncInfoVo getVncInfo(HostInfoRespCreateReqDTO hostInfo);
}
