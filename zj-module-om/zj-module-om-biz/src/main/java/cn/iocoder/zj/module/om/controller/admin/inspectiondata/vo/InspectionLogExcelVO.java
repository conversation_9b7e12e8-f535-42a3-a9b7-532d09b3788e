package cn.iocoder.zj.module.om.controller.admin.inspectiondata.vo;

import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 巡检记录 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class InspectionLogExcelVO {

    @ExcelProperty("巡检名称")
    private String inspectionName;

    @ExcelProperty("阈值")
    private BigDecimal threshold;

    @ExcelProperty("评分")
    private BigDecimal score;

    @ExcelProperty("检测结果")
    private String result;

    @ExcelProperty("异常设备数量")
    private String assetNum;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private LocalDateTime createTime;

}
