package cn.iocoder.zj.module.om.controller.admin.patrolplan;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.module.om.controller.admin.patrolplan.vo.*;
import cn.iocoder.zj.module.om.convert.patrolplan.PatrolPlanConvert;
import cn.iocoder.zj.module.om.dal.dataobject.patrolplan.PatrolPlanDO;
import cn.iocoder.zj.module.om.service.patrolplan.PatrolPlanService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

@Tag(name = "管理后台 - 巡检计划")
@RestController
@RequestMapping("/om/patrol-plan")
@Validated
public class PatrolPlanController {

    @Resource
    private PatrolPlanService patrolPlanService;

    @PostMapping("/create")
    @Operation(summary = "创建巡检计划")
    @PreAuthorize("@ss.hasPermission('om:patrol-plan:create')")
    public CommonResult<Long> createPatrolPlan(@Valid @RequestBody PatrolPlanCreateReqVO createReqVO) {
        return success(patrolPlanService.createPatrolPlan(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新巡检计划")
    @PreAuthorize("@ss.hasPermission('om:patrol-plan:update')")
    public CommonResult<Boolean> updatePatrolPlan(@Valid @RequestBody PatrolPlanUpdateReqVO updateReqVO) {
        patrolPlanService.updatePatrolPlan(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除巡检计划")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('om:patrol-plan:delete')")
    public CommonResult<Boolean> deletePatrolPlan(@RequestParam("id") Long id) {
        patrolPlanService.deletePatrolPlan(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得巡检计划")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('om:patrol-plan:query')")
    public CommonResult<PatrolPlanRespVO> getPatrolPlan(@RequestParam("id") Long id) {
        PatrolPlanRespVO patrolPlan = patrolPlanService.getPatrolPlan(id);
        return success(patrolPlan);
    }

    @GetMapping("/list")
    @Operation(summary = "获得巡检计划列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('om:patrol-plan:query')")
    public CommonResult<List<PatrolPlanRespVO>> getPatrolPlanList(@RequestParam("ids") Collection<Long> ids) {
        List<PatrolPlanDO> list = patrolPlanService.getPatrolPlanList(ids);
        return success(PatrolPlanConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得巡检计划分页")
    @PreAuthorize("@ss.hasPermission('om:patrol-plan:query')")
    public CommonResult<PageResult<PatrolPlanRespVO>> getPatrolPlanPage(@Valid PatrolPlanPageReqVO pageVO) {
        PageResult<PatrolPlanRespVO> pageResult = patrolPlanService.getPatrolPlanPage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出巡检计划 Excel")
    @PreAuthorize("@ss.hasPermission('om:patrol-plan:export')")
    @OperateLog(type = EXPORT)
    public void exportPatrolPlanExcel(@Valid PatrolPlanExportReqVO exportReqVO,
                                      HttpServletResponse response) throws IOException {
        List<PatrolPlanDO> list = patrolPlanService.getPatrolPlanList(exportReqVO);
        // 导出 Excel
        List<PatrolPlanExcelVO> datas = PatrolPlanConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "巡检计划.xls", "数据", PatrolPlanExcelVO.class, datas);
    }

    @PostMapping("/execute")
    @PreAuthorize("@ss.hasPermission('om:patrol-plan:execute')")
    @Operation(summary = "执行巡检计划")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<Boolean> executePatrolPlan(@RequestParam("id") Long id) {
        patrolPlanService.executePatrolPlan(id);
        return success(true);
    }

    @PostMapping("/startOrStop")
    @PreAuthorize("@ss.hasPermission('om:patrol-plan:start')")
    @Operation(summary = "启动或停止巡检计划")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @Parameter(name = "status", description = "状态", required = true, example = "1")
    public CommonResult<Boolean> startOrStopPatrolPlan(@RequestParam("id") Long id, @RequestParam("status") int status) {
        patrolPlanService.startOrStopPatrolPlan(id, status);
        return success(true);
    }
}
