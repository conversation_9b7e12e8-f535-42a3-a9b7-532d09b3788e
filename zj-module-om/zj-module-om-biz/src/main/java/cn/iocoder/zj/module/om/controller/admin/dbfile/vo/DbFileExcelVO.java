package cn.iocoder.zj.module.om.controller.admin.dbfile.vo;

import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 配置备份 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class DbFileExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("文件名")
    private String name;

    @ExcelProperty("文件路径")
    private String path;

    @ExcelProperty("文件url")
    private String url;

    @ExcelProperty("文件大小")
    private Integer size;

    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private LocalDateTime createTime;

}
