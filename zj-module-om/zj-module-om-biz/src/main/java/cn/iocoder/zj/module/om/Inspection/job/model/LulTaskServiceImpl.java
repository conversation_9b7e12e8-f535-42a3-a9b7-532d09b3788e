package cn.iocoder.zj.module.om.Inspection.job.model;


import cn.iocoder.zj.module.om.Inspection.job.task.LulTaskService;
import cn.iocoder.zj.module.om.service.lultask.LulTaskModelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class LulTaskServiceImpl implements LulTaskService {

    @Lazy
    @Resource
    private LulTaskModelService lulTaskModelService;

    @Override
    public List<LulTaskModel> listTasks() {
        return lulTaskModelService.findAll();
    }

    @Override
    public void removeTask(LulTaskModel taskCacheModel) {
        Assert.notNull(taskCacheModel.getId(), "任务ID不能为空");
        lulTaskModelService.removeTask(taskCacheModel);
    }

    @Override
    public void addTask(LulTaskModel taskCacheModel) {
        taskCacheModel.setVersion(0);
        lulTaskModelService.addTask(taskCacheModel);
    }

    @Override
    public void updateTasByJobId(LulTaskModel taskCacheModel) {
        Assert.notNull(taskCacheModel.getId(), "任务ID不能为空");
        lulTaskModelService.updateTasByJobId(taskCacheModel);
    }

    @Override
    public LulTaskModel getTaskByJobId(Long jobId) {
        return lulTaskModelService.getTaskByJobId(jobId);
    }


}
