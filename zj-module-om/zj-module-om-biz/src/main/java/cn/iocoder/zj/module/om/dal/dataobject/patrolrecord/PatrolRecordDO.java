package cn.iocoder.zj.module.om.dal.dataobject.patrolrecord;

import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * 巡检记录 DO
 *
 * <AUTHOR>
 */
@TableName("om_patrol_record")
@KeySequence("om_patrol_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PatrolRecordDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 报告名称
     */
    private String recordName;
    /**
     *巡检计划ID
     */
    private Long planId;
    /**
     * 巡检开始时间
     */
    private Date startTime;
    /**
     * 巡检结束时间
     */
    private Date endTime;
    /**
     * 巡检项总数
     */
    private Integer totalItemCount;
    /**
     * 资源总数
     */
    private Integer totalResourceCount;
    /**
     * 异常情况数
     */
    private Integer abnormalCount;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 管理员设置的租户的ID
     */
    private Long sysSettingTenant;

    /**
     * 多个平台ID，以逗号分隔
     */
    private String platformIds;

    /**
     * 巡检报告文件名
     */
    private String fileName;
    /**
     * 巡检报告文件路径
     */
    private String filePath;

}
