package cn.iocoder.zj.module.om.controller.admin.patrolinspectionconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 巡检设置 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class PatrolInspectionConfigBaseVO {

    @Schema(description = "巡检计划ID")
    private Long planId;

    @Schema(description = "巡检时间")
    private Integer patrolTime;

    @Schema(description = "资源类型(存储资源/宿主机资源/云主机资源/云硬盘资源)", required = true)
    @NotNull(message = "资源类型(存储资源/宿主机资源/云主机资源/云硬盘资源)不能为空")
    private String resourceType;

    @Schema(description = "指标名称(CPU分配/内存分配/网卡入速度等)", required = true)
    @NotNull(message = "指标名称(CPU分配/内存分配/网卡入速度等)不能为空")
    private String metricName;

    @Schema(description = "阈值类型(正常/低风险/中风险/高风险/异常)", required = true)
    @NotNull(message = "阈值类型(正常/低风险/中风险/高风险/异常)不能为空")
    private String thresholdType;

    @Schema(description = "比较运算符(>, <, >=, <=, =, !=, between)',")
    private String operator;

    @Schema(description = "阈值值")
    private BigDecimal thresholdValue;

    @Schema(description = "阈值最大值(用于范围比较)")
    private BigDecimal thresholdValueMax;

    @Schema(description = "单位(%, MB/S, G等)")
    private String unit;

    @Schema(description = "持续时间")
    private Integer duration;

    @Schema(description = "持续时间单位(分钟/小时/天)")
    private String durationUnit;

    @Schema(description = "差值率")
    private BigDecimal diffRate;

    @Schema(description = "差值率运算符")
    private String diffRateOperator;
}
