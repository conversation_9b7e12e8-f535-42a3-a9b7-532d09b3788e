package cn.iocoder.zj.module.om.Inspection.metric;

import cn.iocoder.zj.module.om.Inspection.data.PatrolCompareResult;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspectionconfig.PatrolInspectionConfigDO;
import cn.iocoder.zj.module.om.enums.PatrolMetricEnum;
import cn.iocoder.zj.module.om.enums.PatrolResourceTypeEnum;

import java.util.List;

/**
 * 巡检指标接口
 * 定义巡检指标的基本行为
 *
 * <AUTHOR>
 */
public interface PatrolMetric {

    /**
     * 获取指标类型
     *
     * @return 指标类型枚举
     */
    PatrolMetricEnum getMetricType();

    /**
     * 获取资源类型
     *
     * @return 资源类型枚举
     */
    PatrolResourceTypeEnum getResourceType();

    /**
     * 比较资源数据与配置
     *
     * @param resourceDataList 资源数据列表
     * @param configList       配置列表
     * @param tenantId         租户ID
     * @return 比较结果
     */
    PatrolCompareResult compare(List<?> resourceDataList, List<PatrolInspectionConfigDO> configList, Long tenantId);
}