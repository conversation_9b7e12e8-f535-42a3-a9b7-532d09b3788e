package cn.iocoder.zj.module.om.controller.admin.monitorauthorization;

import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.zj.module.om.controller.admin.monitorauthorization.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.monitorauthorization.MonitorAuthorizationDO;
import cn.iocoder.zj.module.om.convert.monitorauthorization.MonitorAuthorizationConvert;
import cn.iocoder.zj.module.om.service.monitorauthorization.MonitorAuthorizationService;

@Tag(name = "管理后台 - 监控申请授权")
@RestController
@RequestMapping("/om/monitor-authorization")
@Validated
public class    MonitorAuthorizationController {

    @Resource
    private MonitorAuthorizationService monitorAuthorizationService;

    @PostMapping("/create")
    @Operation(summary = "创建监控申请授权")
    @PreAuthorize("@ss.hasPermission('om:monitor-authorization:create')")
    public CommonResult<Long> createMonitorAuthorization(@Valid @RequestBody MonitorAuthorizationCreateReqVO createReqVO) {
        return success(monitorAuthorizationService.createMonitorAuthorization(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新监控申请授权")
    @PreAuthorize("@ss.hasPermission('om:monitor-authorization:update')")
    public CommonResult<Boolean> updateMonitorAuthorization(@Valid @RequestBody MonitorAuthorizationUpdateReqVO updateReqVO) {
        monitorAuthorizationService.updateMonitorAuthorization(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除监控申请授权")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('om:monitor-authorization:delete')")
    public CommonResult<Boolean> deleteMonitorAuthorization(@RequestParam("id") Long id) {
        monitorAuthorizationService.deleteMonitorAuthorization(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得监控申请授权")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('om:monitor-authorization:query')")
    @TenantIgnore
    public CommonResult<MonitorAuthorizationRespVO> getMonitorAuthorization(@RequestParam("id") Long id) {
        MonitorAuthorizationDO monitorAuthorization = monitorAuthorizationService.getMonitorAuthorization(id);
        return success(MonitorAuthorizationConvert.INSTANCE.convert(monitorAuthorization));
    }

    @GetMapping("/list")
    @Operation(summary = "获得监控申请授权列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('om:monitor-authorization:query')")
    public CommonResult<List<MonitorAuthorizationRespVO>> getMonitorAuthorizationList(@RequestParam("ids") Collection<Long> ids) {
        List<MonitorAuthorizationDO> list = monitorAuthorizationService.getMonitorAuthorizationList(ids);
        return success(MonitorAuthorizationConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得监控申请授权分页")
    @PreAuthorize("@ss.hasPermission('om:monitor-authorization:query')")
    public CommonResult<PageResult<MonitorAuthorizationRespVO>> getMonitorAuthorizationPage(@Valid MonitorAuthorizationPageReqVO pageVO) {
        PageResult<MonitorAuthorizationDO> pageResult = monitorAuthorizationService.getMonitorAuthorizationPage(pageVO);
        return success(MonitorAuthorizationConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出监控申请授权 Excel")
    @PreAuthorize("@ss.hasPermission('om:monitor-authorization:export')")
    @OperateLog(type = EXPORT)
    public void exportMonitorAuthorizationExcel(@Valid MonitorAuthorizationExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<MonitorAuthorizationDO> list = monitorAuthorizationService.getMonitorAuthorizationList(exportReqVO);
        // 导出 Excel
        List<MonitorAuthorizationExcelVO> datas = MonitorAuthorizationConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "监控申请授权.xls", "数据", MonitorAuthorizationExcelVO.class, datas);
    }

}
