package cn.iocoder.zj.module.om.service.cloud;

import cn.hutool.core.codec.Base64;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.monitor.api.hardware.HardWareInfoApi;
import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.hostinfo.HostInfoApi;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo.ResourceOperateLog;
import cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo.VmwareCreateVmReqVo;
import cn.iocoder.zj.module.om.controller.admin.vncinfo.vo.VncInfoVo;
import cn.iocoder.zj.module.om.dal.mysql.assetmanagement.AssetManagementMapper;
import cn.iocoder.zj.module.om.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.om.framework.websocket.WebSocketClient;
import cn.iocoder.zj.module.om.service.zstack.core.VmwareApiConstant;
import cn.iocoder.zj.module.om.util.SampleUtil;
import cn.iocoder.zj.module.om.util.ws.VmwareWebConsoleClient;
import cn.iocoder.zj.module.om.util.ws.WebMKSTicketDO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.vmware.vim25.*;
import com.vmware.vim25.mo.*;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.xml.ws.soap.SOAPFaultException;
import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URL;
import java.rmi.RemoteException;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
@Log4j2

public class IVmwareServiceImpl implements IVmwareService {
    //忽略安全证书
    static {
        try {
            trustAllHttpsCertificates();
            HttpsURLConnection.setDefaultHostnameVerifier
                    (
                            (urlHostName, session) -> true
                    );
        } catch (Exception e) {
        }
    }
    private static void trustAllHttpsCertificates()
            throws NoSuchAlgorithmException, KeyManagementException {
        TrustManager[] trustAllCerts = new TrustManager[1];
        trustAllCerts[0] = (TrustManager) new TrustAllManager();
        SSLContext sc = SSLContext.getInstance("SSL");
        sc.init(null, trustAllCerts, null);
        HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
    }

    private static class TrustAllManager implements X509TrustManager {
        public X509Certificate[] getAcceptedIssuers() {
            return null;
        }
        public void checkServerTrusted(X509Certificate[] certs, String authType) {
        }
        public void checkClientTrusted(X509Certificate[] certs, String authType) {
        }
    }
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    HostInfoApi hostInfoApi;
    @Resource
    HardWareInfoApi hardWareInfoApi;
    @Resource
    AssetManagementMapper assetManagementMapper;
    @Resource WebSocketClient webSocketClient;
    @Resource
    PlatformRedisDAO platformRedisDAO;

    @Override
    public List<Map> getDatacenters(Long platformId) {
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(platformId).getData();
        String token = getVmwareSessionId(platform);
        String reqUrl = platform.getUrl() + VmwareApiConstant.GET_DATACENTERS;
        HttpResponse result =  HttpRequest.get(reqUrl)
                .header("vmware-api-session-id",token)
                .execute();
        if (result.getStatus() != 200) {
            throw new RuntimeException("getDatacenters error");
        }
        return JSONArray.parseArray(result.body(), Map.class);
    }
    public List<Map> getNetworks(Long platformId,String datacenter,String networkType) {
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(platformId).getData();
        String token = getVmwareSessionId(platform);
        String reqUrl = platform.getUrl() + VmwareApiConstant.GET_NETWORKS+"?datacenters="+datacenter+"&types="+networkType;
        HttpResponse result =  HttpRequest.get(reqUrl)
                .header("vmware-api-session-id",token)
                .execute();
        if (result.getStatus() != 200) {
            throw new RuntimeException("getNetWorks error");
        }
        return JSONArray.parseArray(result.body(), Map.class);
    }
    @Override
    public List<Map> getFoldersByDatacenter(Long platformId,String datacenter) {
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(platformId).getData();
        String token = getVmwareSessionId(platform);
        String reqUrl = platform.getUrl() + VmwareApiConstant.GET_FOLDERS+"?datacenters="
                + datacenter+"&type=VIRTUAL_MACHINE";
        HttpResponse result =  HttpRequest.get(reqUrl)
                .header("vmware-api-session-id",token)
                .execute();
        if (result.getStatus() != 200) {
            throw new RuntimeException("getFolders error");
        }
        return JSONArray.parseArray(result.body(), Map.class);
    }

    @Override
    public List<Map> getClustersByDatacenter(Long platformId, String datacenter) {
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(platformId).getData();
        String token = getVmwareSessionId(platform);
        String reqUrl = platform.getUrl() + VmwareApiConstant.GET_CLUSTERS+"?datacenters="+ datacenter;
        HttpResponse result =  HttpRequest.get(reqUrl)
                .header("vmware-api-session-id",token)
                .execute();
        if (result.getStatus() != 200) {
            throw new RuntimeException("getClusters error");
        }
        return JSONArray.parseArray(result.body(), Map.class);
    }

    @Override
    public List<Map> getDatastoresByDatacenter(Long platformId, String datacenter) {
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(platformId).getData();
        String token = getVmwareSessionId(platform);
        String reqUrl = platform.getUrl() + VmwareApiConstant.GET_DATASTORES+"?datacenters="+ datacenter;
        HttpResponse result =  HttpRequest.get(reqUrl)
                .header("vmware-api-session-id",token)
                .execute();
        if (result.getStatus() != 200) {
            throw new RuntimeException("getDatastores error");
        }
        return JSONArray.parseArray(result.body(), Map.class);
    }

    @Override
    public Map<String,String> createVmwareVm(VmwareCreateVmReqVo reqVo) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        HostInfoRespCreateReqDTO reqDTO = new HostInfoRespCreateReqDTO();
        Map<String,Object> reqBody = new HashMap<>();
        Map<String,Object> placement = new HashMap<>();
        Map<String,Object> cpu = new HashMap<>();
        List<Object> nics = new ArrayList<>();
        Map<String,Object> nicsBacking = new HashMap<>();
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(reqVo.getPlatformId()).getData();
        String token = getVmwareSessionId(platform);
        placement.put("datastore",reqVo.getDatastore());
        placement.put("folder",reqVo.getFolder());
        placement.put("cluster",reqVo.getCluster());

        cpu.put("count",reqVo.getCpuCount());
        nicsBacking.put("type",reqVo.getNetworkType());
        nicsBacking.put("network",reqVo.getNetwork());
        Map<String,Object> backing = new HashMap<>();
        backing.put("backing",nicsBacking);
        nics.add(backing);

        reqBody.put("nics",nics);
        reqBody.put("cpu",cpu);
        reqBody.put("guest_OS",reqVo.getGuestOS());
        reqBody.put("name",reqVo.getName());
        reqBody.put("placement",placement);
        String JSONStr = JSONUtils.toJSONString(reqBody);
        Map<String,String> resultMap = new HashMap<>();
        String reqUrl = platform.getUrl() +VmwareApiConstant.GET_VMS;
        String casualUuid = UuidUtils.generateUuid();
        reqDTO.setName(reqVo.getName());
        reqDTO.setState("Creating");
        reqDTO.setUuid(casualUuid);
        hostInfoApi.createHostSingle(reqDTO);
        HostInfoRespCreateReqDTO createReqDTO = hostInfoApi.getByUuid(casualUuid).getData();
        final HttpResponse[] resp = new HttpResponse[1];
        //另起一个线程，用于处理请求结束后的操作
        resultMap.put("msg","正在创建云主机("+reqVo.getName()+")");
        resultMap.put("success","true");
        resultMap.put("uuid",casualUuid);
        Runnable data = (()->{
            JSONObject sendResult = new JSONObject();
            sendResult.put("uuid",casualUuid);
            resp[0] =  HttpRequest.post(reqUrl)
                    .header("vmware-api-session-id",token)
                    .body(JSONStr)
                    .execute();

            //通过临时uuid删除状态为“创建中”的该记录，并重新拉取该虚拟机
            if (resp[0].getStatus() == 201) {
                HostInfoRespCreateReqDTO casualVm = hostInfoApi.getByUuid(casualUuid).getData();
                hostInfoApi.deleteVm(casualVm.getId());
                addVmwareVmLocal(platform, reqVo,loginUser,createReqDTO.getId(),casualUuid);
            } else if (resp[0].getStatus() != 201) {
                String errMsg = getErrorMessage(resp[0].body());
                sendResult.put("success","false");
                sendResult.put("uuid",casualUuid);
                sendResult.put("msg","创建云主机("+reqVo.getName()+")失败，错误信息:"+errMsg+",请至"+platform.getName()+"查看详情");
                createOperateLog(reqVo.getName(),"","创建云主机", "error", "host", errMsg, platform);
                webSocketClient.sendMessage(casualUuid,sendResult.toJSONString());
                hostInfoApi.deleteVm(createReqDTO.getId());
            }
        });
        Thread thread = new Thread(data);
        thread.start();
        return resultMap;
    }


    @Override
    public CommonResult<Map<String, String>> operateVmwareVm(String uuid,String vms, Long platformId, String type, String action) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(platformId).getData();
        String token = getVmwareSessionId(platform);
        HostInfoRespCreateReqDTO hostInfo = hostInfoApi.getByUuid(uuid).getData();
        HostInfoRespCreateReqDTO reqDTO = new HostInfoRespCreateReqDTO();
        Map<String,String> reuslt = new HashMap<>();
        final HttpResponse[] result = new HttpResponse[1];
        AtomicReference<String> state = new AtomicReference<>("");
        AtomicReference<Integer> status = null;
        String op = VmwareHostOperateConvert(action);
        if(action.equals("destroy")){
            reqDTO.setState("Destroying");
        } else if (action.equals("start")) {
            reqDTO.setState("Starting");
        }else if (action.equals("stop")) {
            reqDTO.setState("Stopping");
        }else if (action.equals("reboot")) {
            reqDTO.setState("Rebooting");
        }
        hostInfoApi.updateHostSingle(reqDTO);
        //另起一个线程，用于处理请求结束后的操作
        Runnable data = (()->{
            if(action.equals("destroy")) {
                state.set("Destroyed");
                String reqUrl = platform.getUrl() + VmwareApiConstant.GET_VMS + "/" + vms;
                result[0] = HttpRequest.delete(reqUrl)
                        .header("vmware-api-session-id", token)
                        .execute();
                status.set(result[0].getStatus());
                if (status.get() == 204) {
                    // 同步删除拓扑图中的信息
                    hostInfoApi.deleteAssetAndHostJson(uuid);
                }
            } else {
                String reqUrl = platform.getUrl() + VmwareApiConstant.GET_VMS + "/" + vms + "/power?action=";
                if (action.equals("start")) {
                    state.set("Running");
                    reqUrl += "start";
                } else if (action.equals("stop")) {
                    state.set("Stopped");
                    switch (type){
                        case "grace": reqUrl += "suspend";
                            break;
                        case "cold" : reqUrl += "stop";
                            break;
                    }
                } else if (action.equals("reboot")) {
                    state.set("Running");
                    reqUrl += "reset";
                }
                result[0] = HttpRequest.post(reqUrl)
                        .header("vmware-api-session-id", token)
                        .execute();
                if (result[0].getStatus() == 201) {
                    hostInfoApi.updateHostSingle(reqDTO);
                }
            }
            JSONObject sendResult = new JSONObject();
            sendResult.put("uuid",uuid);
            if (result[0].getStatus() != 201) {
                String errMsg = getErrorMessage(result[0].body());
                sendResult.put("msg",op+"("+hostInfo.getName()+")失败，错误信息:"+errMsg+",请至"+platform.getName()+"查看详情");
                sendResult.put("success","false");
                createOperateLog(hostInfo.getName(),hostInfo.getUuid(),op, "error", "host", errMsg, platform);
                webSocketClient.sendMessage(uuid,sendResult.toJSONString());
                throw new RuntimeException(errMsg);
            }else if (result[0].getStatus() == 201) {
                sendResult.put("msg",op+"宿主机("+hostInfo.getName()+")成功");
                sendResult.put("success","true");
                createOperateLog(hostInfo.getName(),hostInfo.getUuid(),op, "success", "host", "", platform);
                webSocketClient.sendMessage(uuid,sendResult.toJSONString());
            }
        });

        Thread thread = new Thread(data);
        thread.start();
        reuslt.put("msg","正在"+op+"("+hostInfo.getName()+")");
        reuslt.put("success","true");
        return CommonResult.success(reuslt);

    }

    @Override
    public CommonResult<Map<String, String>> operateVmwareHost(String uuid, String vms, Long platformId, String actions, String state,String hardwareName) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(platformId).getData();
        Map<String,String> reuslt = new HashMap<>();
        HardWareRespCreateReqDTO reqDTO = new HardWareRespCreateReqDTO();
        List<HardWareRespCreateReqDTO> reqList = new ArrayList<>();
        reuslt.put("msg","");
        reuslt.put("success","true");
        if(actions.equals("reconnect")) {
            reqDTO.setState("Connected");
        }else if(actions.equals("stateChange")&&state.equals("enable")){
            reqDTO.setState("Disconnected");
        }else if(actions.equals("stateChange")&&state.equals("disable")){
            reqDTO.setState("Connected");
        }else if(actions.equals("stateChange")&&state.equals("maintain")){
            reqDTO.setIsMaintain(1);
        }else if(actions.equals("stateChange")&&state.equals("ext_maintain")) {
            reqDTO.setIsMaintain(0);
        }
        hardWareInfoApi.updates(reqList);
        Runnable data = (()->{
            try {
                ServiceInstance si = new ServiceInstance(new URL(platform.getUrl()+"/sdk"), platform.getUsername(), platform.getPassword(), true);
                Folder rootFolder = si.getRootFolder();
                ManagedEntity hostmanagedEntity = new InventoryNavigator(rootFolder).searchManagedEntity("HostSystem",hardwareName);
                HostSystem hostsys = (HostSystem) hostmanagedEntity;
                HostConnectSpec  hcs = new HostConnectSpec();
                String connectionState = hostsys.getRuntime().getConnectionState().name();
                boolean inMaintenanceMode = hostsys.getRuntime().inMaintenanceMode;
                VirtualMachine[] virtualMachines  =  hostsys.getVms();
                JSONObject sendResult = new JSONObject();
                sendResult.put("uuid",uuid);
                if(actions.equals("reconnect")) {
                    //如果主机是已连接状态则断开后重新连接,否则直接重连
                    if (connectionState.equals("connected")) {
                        Task diskConnectTask = hostsys.disconnectHost();
                        diskConnectTask.waitForTask();
                        if (diskConnectTask.getTaskInfo().getState().equals(TaskInfoState.error)) {
                            Task reConnectTask = hostsys.reconnectHost_Task(hcs);
                            reConnectTask.waitForTask();
                            if (reConnectTask.getTaskInfo().getState().equals(TaskInfoState.error)) {
                                sendResult.put("msg","重连宿主机("+hardwareName+"失败");
                                sendResult.put("success","false");
                                createOperateLog(hardwareName,uuid,"重连宿主机", "error", "hardware", "重连失败", platform);
                                webSocketClient.sendMessage(uuid,sendResult.toJSONString());
                            }else if(reConnectTask.getTaskInfo().getState().equals(TaskInfoState.success)) {
                                sendResult.put("msg","重连宿主机("+hardwareName+"成功");
                                sendResult.put("success","true");
                                createOperateLog(hardwareName,uuid,"重连宿主机", "success", "hardware", "", platform);
                                webSocketClient.sendMessage(uuid,sendResult.toJSONString());
                            }
                        }
                        reqDTO.setState("Connected");
                    }
                }else if(actions.equals("stateChange")&&state.equals("enable")){
                    if(connectionState.equals("disconnected")){
                        Task reConnectTask = hostsys.reconnectHost_Task(hcs);
                        reConnectTask.waitForTask();
                        if(reConnectTask.getTaskInfo().getState().equals(TaskInfoState.error)){
                            sendResult.put("msg","连接宿主机("+hardwareName+"失败");
                            sendResult.put("success","false");
                            createOperateLog(hardwareName,uuid,"连接宿主机", "error", "hardware", "连接失败", platform);
                            webSocketClient.sendMessage(uuid,sendResult.toJSONString());
                        }else if(reConnectTask.getTaskInfo().getState().equals(TaskInfoState.success)) {
                            sendResult.put("msg","连接宿主机("+hardwareName+"成功");
                            sendResult.put("success","true");
                            createOperateLog(hardwareName,uuid,"连接宿主机", "success", "hardware", "", platform);
                            webSocketClient.sendMessage(uuid,sendResult.toJSONString());
                        }
                    }
                    reqDTO.setState("Disconnected");
                }else if(actions.equals("stateChange")&&state.equals("disable")){
                    Task diskConnectTask = hostsys.disconnectHost();
                    diskConnectTask.waitForTask();
                    if(diskConnectTask.getTaskInfo().getState().equals(TaskInfoState.error)){
                        sendResult.put("msg","宿主机("+hardwareName+"断开连接失败");
                        sendResult.put("success","false");
                        createOperateLog(hardwareName,uuid,"宿主机断开连接", "error", "hardware", "断开连接失败", platform);
                        webSocketClient.sendMessage(uuid,sendResult.toJSONString());
                    }else if(diskConnectTask.getTaskInfo().getState().equals(TaskInfoState.success)) {
                        sendResult.put("msg","宿主机("+hardwareName+"断开连接成功");
                        sendResult.put("success","true");
                        createOperateLog(hardwareName,uuid,"宿主机断开连接", "success", "hardware", "", platform);
                        webSocketClient.sendMessage(uuid,sendResult.toJSONString());
                    }
                    reqDTO.setState("Connected");
                }else if(actions.equals("stateChange")&&state.equals("maintain")){
                    if(!inMaintenanceMode){
                        if(virtualMachines.length>0) {
                            for (VirtualMachine vm : virtualMachines) {
                                if (vm.getRuntime().powerState.equals(VirtualMachinePowerState.poweredOn)) {
                                    Task suspendVmTask = vm.suspendVM_Task();
                                    suspendVmTask.waitForTask();
                                    if (suspendVmTask.getTaskInfo().getState().equals(TaskInfoState.error)) {
                                        sendResult.put("msg","宿主机("+hardwareName+"断开连接失败");
                                        sendResult.put("success","false");
                                        createOperateLog(hardwareName,uuid,"宿主机断开连接", "error", "hardware", "宿主机断开连接失败", platform);
                                        webSocketClient.sendMessage(uuid,sendResult.toJSONString());
                                    }else if(suspendVmTask.getTaskInfo().getState().equals(TaskInfoState.success)) {
                                        sendResult.put("msg","宿主机("+hardwareName+"断开连接成功");
                                        sendResult.put("success","true");
                                        createOperateLog(hardwareName,uuid,"宿主机断开连接", "success", "hardware", "", platform);
                                        webSocketClient.sendMessage(uuid,sendResult.toJSONString());
                                    }
                                }
                            }
                        }
                    }
                    Task diskConnectTask = hostsys.disconnectHost();
                    diskConnectTask.waitForTask();
                    if(diskConnectTask.getTaskInfo().getState().equals(TaskInfoState.error)){
                        sendResult.put("msg","宿主机("+hardwareName+"断开连接失败");
                        sendResult.put("success","false");
                        createOperateLog(hardwareName,uuid,"宿主机断开连接", "error", "hardware", "进入维护状态失败", platform);
                        webSocketClient.sendMessage(uuid,sendResult.toJSONString());
                    }else if(diskConnectTask.getTaskInfo().getState().equals(TaskInfoState.success)) {
                        sendResult.put("msg","宿主机("+hardwareName+"断开连接成功");
                        sendResult.put("success","true");
                        createOperateLog(hardwareName,uuid,"宿主机断开连接", "success", "hardware", "", platform);
                        webSocketClient.sendMessage(uuid,sendResult.toJSONString());
                    }
                    reqDTO.setIsMaintain(1);
                }else if(actions.equals("stateChange")&&state.equals("ext_maintain")) {
                    if (!inMaintenanceMode) {
                        Task diskConnectTask = hostsys.disconnectHost();
                        diskConnectTask.waitForTask();
                        if (diskConnectTask.getTaskInfo().getState().equals(TaskInfoState.error)) {
                            sendResult.put("msg","宿主机("+hardwareName+"退出维护失败");
                            sendResult.put("success","false");
                            createOperateLog(hardwareName,uuid,"宿主机退出维护", "error", "hardware", "退出维护状态失败", platform);
                            webSocketClient.sendMessage(uuid,sendResult.toJSONString());
                        } else if(diskConnectTask.getTaskInfo().getState().equals(TaskInfoState.success)) {
                            sendResult.put("msg","宿主机("+hardwareName+"退出维护成功");
                            sendResult.put("success","true");
                            createOperateLog(hardwareName,uuid,"宿主机退出维护", "success", "hardware", "", platform);
                            webSocketClient.sendMessage(uuid,sendResult.toJSONString());
                        }
                        if (virtualMachines.length > 0) {
                            for (VirtualMachine vm : virtualMachines) {
                                if (vm.getRuntime().powerState.equals(VirtualMachinePowerState.suspended)) {
                                    Task poweronVmTask = vm.powerOnVM_Task(hostsys);
                                    poweronVmTask.waitForTask();
                                }
                            }
                        }
                    }
                    reqDTO.setIsMaintain(0);
                }
            } catch (RemoteException | MalformedURLException | InterruptedException e) {
                throw new RuntimeException(e);
            }
            reqDTO.setUuid(uuid);
            reqList.add(reqDTO);
            hardWareInfoApi.updates(reqList);
        });
        Thread thread = new Thread(data);
        thread.start();
        return CommonResult.success(reuslt);
    }
    private void addVmwareVmLocal(PlatformconfigDTO platform,VmwareCreateVmReqVo reqVo,LoginUser loginUser,Long id,String casualUuid) {
        HostInfoRespCreateReqDTO reqDTO = new HostInfoRespCreateReqDTO();
        JSONObject sendResult = new JSONObject();
        try {
            ServiceInstance si = SampleUtil.createServiceInstance(platform.getUrl(), platform.getUsername(), platform.getPassword());
            InventoryNavigator inv = new InventoryNavigator(si.getRootFolder());
            VirtualMachine vm = (VirtualMachine) inv.searchManagedEntity("VirtualMachine", reqVo.getName());
            VirtualMachineSummary summary = vm.getSummary();
            reqDTO.setName(reqVo.getName());
            reqDTO.setState("Stopped");
            reqDTO.setPlatformName(platform.getName());
            reqDTO.setTypeName("vmware");
            reqDTO.setPlatformId(platform.getId());
            reqDTO.setTypeName(platform.getTypeCode());
            reqDTO.setClusterUuid(reqVo.getCluster());
            reqDTO.setClusterName(reqVo.getClusterName());
            reqDTO.setMemoryUsed(new BigDecimal(0));
            reqDTO.setDiskUsed(new BigDecimal(0));
            reqDTO.setCpuUsed(new BigDecimal(0));
            reqDTO.setIp(summary.getGuest().getIpAddress());
            reqDTO.setGuestOsType(summary.getGuest().getGuestFullName());
            reqDTO.setType("UserVm");
            reqDTO.setVms(summary.getVm().getVal());
            reqDTO.setUuid(summary.getConfig().getInstanceUuid());
            hostInfoApi.createHostSingle(reqDTO);
            sendResult.put("uuid",casualUuid);
            sendResult.put("success","true");
            sendResult.put("msg","创建云主机("+reqVo.getName()+")成功");
            createOperateLog(reqVo.getName(),"","创建云主机", "success", "host", "", platform);
            webSocketClient.sendMessage(loginUser.getId().toString(),sendResult.toJSONString());
        } catch (SOAPFaultException sfe) {
            sendResult.put("success","false");
            sendResult.put("uuid",casualUuid);
            sendResult.put("msg","创建云主机("+reqVo.getName()+")失败，错误信息:"+sfe.getMessage()+",请至"+platform.getName()+"查看详情");
            createOperateLog(reqVo.getName(),"","创建云主机", "error", "host", sfe.getMessage(), platform);
            webSocketClient.sendMessage(loginUser.getId().toString(),sendResult.toJSONString());
            hostInfoApi.deleteVm(id);
        } catch (Exception e) {
            sendResult.put("uuid",casualUuid);
            sendResult.put("success","false");
            sendResult.put("msg","创建云主机("+reqVo.getName()+")失败，错误信息:"+e.getMessage()+",请至"+platform.getName()+"查看详情");
            createOperateLog(reqVo.getName(),"","创建云主机", "error", "host", e.getMessage(), platform);
            webSocketClient.sendMessage(loginUser.getId().toString(),sendResult.toJSONString());
            hostInfoApi.deleteVm(id);
        }
    }
    private String getVmwareSessionId(PlatformconfigDTO platform) {
        String base64Str = Base64.encode(platform.getUsername()+":"+platform.getPassword());
        HttpRequest res = HttpRequest.post(platform.getUrl() + VmwareApiConstant.
                GET_VMWARE_LOGIN).header("Authorization","Basic "+base64Str);
        return res.execute().body().replaceAll("\"","");
    }

    private String getErrorMessage(String body){
        JSONArray msgArray = JSONObject.parseObject(body).getJSONArray("messages");
        if(msgArray.size()>0) {
            JSONObject errorObj= JSONObject.parseObject(JSONObject.toJSONString(msgArray.get(0)));
            if(errorObj.getString("default_message").length()>0) {
                return errorObj.getString("default_message");
            }else {
                return JSONObject.parseObject(body).getString("error_type");
            }
        }else {
            return JSONObject.parseObject(body).getString("error_type");
        }

    }
    @Override
    public String hardwareOperateConvert(String operation,String state){
        String op = "";
        switch (operation){
            case "reconnect":
                op = "重启宿主机"; break;
            case "create":
                op = "创建宿主机"; break;
            case "stateChange":
                switch (state){
                    case "enable" :
                        op = "启用宿主机"; break;
                    case "disable" :
                        op = "停用宿主机"; break;
                    case "maintain" :
                        op = "进入维护"; break;
                    case "ext_maintain" :
                        op = "退出维护"; break;
                }
                break;
        }
        return op;
    }
    @Override
    public String VmwareHostOperateConvert(String operation){
        String op = "";
        switch (operation){
            case "create":
                op = "创建虚拟机"; break;
            case "stop":
                op = "停止虚拟机"; break;
            case "start":
                op = "启动虚拟机"; break;
            case "reboot":
                op = "重启虚拟机"; break;
            case "destroy":
                op = "回收虚拟机"; break;
        }
        return op;
    }

    @Override
    public VncInfoVo getVncInfo(HostInfoRespCreateReqDTO hostInfo) {
        PlatformconfigDTO platformDTO = platformRedisDAO.get("platform").stream()
                .filter(dto -> "vmware".equals(dto.getTypeCode())
                        && Objects.equals(hostInfo.getPlatformId(), dto.getId()))
                .findFirst()
                .orElse(null);
        try {
            // 获取VNC访问信息
            WebMKSTicketDO vmConsoleAccess = VmwareWebConsoleClient.getVMConsoleAccess(convertUrl(platformDTO.getUrl()), platformDTO.getUsername(), platformDTO.getPassword(),
                hostInfo.getName()
            );
            
            // 构建返回对象
            VncInfoVo vncInfoVo = new VncInfoVo();
            vncInfoVo.setWebsocketHost(vmConsoleAccess.getHost());
            vncInfoVo.setWebsocketPort(vmConsoleAccess.getPort());
            vncInfoVo.setWebsocketPath(vmConsoleAccess.getUrl());
            vncInfoVo.setUseSsl(true);
            return vncInfoVo;
        } catch (Exception e) {
            log.error("[getVncInfo] 获取VNC信息异常, hostInfo={}, error={}", hostInfo, e.getMessage(), e);
            return null;
        }
    }

    private static String convertUrl(String url) {
        if (url.matches(".*:443(/.*)?$")) {
            return url.replaceAll(":(443)", "") + "/sdk"; // 替换端口为443
        } else {
            return url + "/sdk"; // 直接加上/sdk
    }
    }

    public void createOperateLog(String name, String uuid, String operation, String result, String resourceType, String msg, PlatformconfigDTO platform) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        ResourceOperateLog operateLog = new ResourceOperateLog();
        operateLog.setCreator(String.valueOf(loginUser.getId()));
        operateLog.setOperation(operation);
        operateLog.setResourceType(resourceType);
        operateLog.setResult(result);
        operateLog.setResourceUuid(uuid);
        operateLog.setResourceName(name);
        operateLog.setPlatformName(platform.getName());
        operateLog.setPlatformId(platform.getId());
        operateLog.setContent(msg);
        assetManagementMapper.createOperateLog(operateLog);
    }
}
