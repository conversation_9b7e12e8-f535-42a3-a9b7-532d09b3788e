package cn.iocoder.zj.module.om.controller.admin.patrolinspection;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.om.controller.admin.inspectiondata.vo.*;
import cn.iocoder.zj.module.om.controller.admin.patrolinspection.vo.*;
import cn.iocoder.zj.module.om.convert.patrolinspection.InspectionLogConvert;
import cn.iocoder.zj.module.om.convert.patrolinspection.InspectionRecordConvert;
import cn.iocoder.zj.module.om.convert.patrolinspection.PatrolInspectionConvert;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspection.InspectionLogDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspection.InspectionRecordDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspection.PatrolInspectionDO;
import cn.iocoder.zj.module.om.service.patrolinspection.InspectionLogService;
import cn.iocoder.zj.module.om.service.patrolinspection.InspectionRecordService;
import cn.iocoder.zj.module.om.service.patrolinspection.PatrolInspectionService;
import cn.iocoder.zj.module.om.util.ExcelVerifyUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

@Tag(name = "管理后台 - 自动巡检规则")
@RestController
@RequestMapping("/om/patrol-inspection")
@Validated
public class PatrolInspectionController {

    @Resource
    private PatrolInspectionService patrolInspectionService;

    @Resource
    private InspectionLogService inspectionLogService;

    @Resource
    private InspectionRecordService inspectionRecordService;

    @PutMapping("/update")
    @Operation(summary = "更新自动巡检规则")
    @PreAuthorize("@ss.hasPermission('om:patrol-inspection:update')")
    @OperateLog(type=UPDATE)
    @TenantIgnore
    public CommonResult<Boolean> updatePatrolInspection(@Valid @RequestBody PatrolInspectionUpdateReqVO updateReqVO) {
        patrolInspectionService.updatePatrolInspection(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除自动巡检规则")
    @Parameter(name = "id", description = "编号", required = true)
    @OperateLog(type=DELETE)
    @PreAuthorize("@ss.hasPermission('om:patrol-inspection:delete')")
    @TenantIgnore
    public CommonResult<Boolean> deletePatrolInspection(@RequestParam("id") Long id) {
        patrolInspectionService.deletePatrolInspection(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得自动巡检规则")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @TenantIgnore
    @PreAuthorize("@ss.hasPermission('om:patrol-inspection:query')")
    public CommonResult<PatrolInspectionRespVO> getPatrolInspection(@RequestParam("id") Long id) {
        PatrolInspectionDO patrolInspection = patrolInspectionService.getPatrolInspection(id);
        return success(PatrolInspectionConvert.INSTANCE.convert(patrolInspection));
    }

    @GetMapping("/list")
    @Operation(summary = "获得自动巡检规则列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @TenantIgnore
    @PreAuthorize("@ss.hasPermission('om:patrol-inspection:query')")
    public CommonResult<List<PatrolInspectionRespVO>> getPatrolInspectionList(@RequestParam("ids") Collection<Long> ids) {
        List<PatrolInspectionDO> list = patrolInspectionService.getPatrolInspectionList(ids);
        return success(PatrolInspectionConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/patrol-inspection-page")
    @Operation(summary = "获得自动巡检规则分页")
    @TenantIgnore
    @PreAuthorize("@ss.hasPermission('om:patrol-inspection:query')")
    public CommonResult<PageResult<PatrolInspectionRespVO>> getPatrolInspectionPage(@Valid PatrolInspectionPageReqVO pageVO) {
        PageResult<PatrolInspectionDO> pageResult = patrolInspectionService.getPatrolInspectionPage(pageVO);
        return success(PatrolInspectionConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-patrol-inspection-excel")
    @Operation(summary = "导出自动巡检规则 Excel")
    @OperateLog(type = EXPORT)
    @TenantIgnore
    public void exportPatrolInspectionExcel(@Valid PatrolInspectionExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<PatrolInspectionDO> list = patrolInspectionService.getPatrolInspectionList(exportReqVO);
        // 导出 Excel
        List<PatrolInspectionExcelVO> datas = PatrolInspectionConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "自动巡检规则.xls", "数据", PatrolInspectionExcelVO.class, datas);
    }
    @PostMapping("/import")
    @Operation(summary = "导入巡检规则")
    @PreAuthorize("@ss.hasPermission('om:patrol-inspection:import')")
    @OperateLog(type = CREATE)
    @Parameter(name = "updateSupport", description = "是否支持更新，默认为 true", example = "true")
    @TenantIgnore
    public CommonResult<PatrolInspectionImportRespVO> importExcel(@RequestBody MultipartFile file,
                                                      @RequestParam(value = "updateSupport", required = false, defaultValue = "true") Boolean updateSupport) throws Exception {
        //验证上传文件
        ExcelVerifyUtil.fileVerify(file,PatrolInspectionExcelVO.class);
        List<PatrolInspectionImportExcelVO> list = ExcelUtils.read(file, PatrolInspectionImportExcelVO.class);
        return success(patrolInspectionService.importPatrolInspectionList(list, updateSupport));
    }

    @PostMapping("/inspectionStart")
    @Operation(summary = "开始巡检")
    @OperateLog(type = CREATE)
    @PreAuthorize("@ss.hasPermission('om:patrol-inspection:start')")
    @Parameter(name = "tenantId", description = "租户Id", required = true, example = "1")
    @TenantIgnore
    public CommonResult<Map<String,Object>> patrolInspectionStart(@RequestParam Long tenantId){
        return success(patrolInspectionService.patrolInspectionStart(tenantId));
    }
    @GetMapping("/export-inspection-log-excel")
    @Operation(summary = "导出自动巡检结果Excel")
    @OperateLog(type = EXPORT)
    @TenantIgnore
    public void exportInspectionLogExcel(@Valid InspectionLogExportReqVO exportReqVO,
                                            HttpServletResponse response) throws IOException {
        List<InspectionLogDO> list = inspectionLogService.getInspectionLogList(exportReqVO);
        // 导出 Excel
        List<InspectionLogExcelVO> datas = InspectionLogConvert.INSTANCE.convertList02(list);
        // 添加系统属性以解决X11FontManager错误
        System.setProperty("java.awt.headless", "true");
        ExcelUtils.write(response, "巡检记录.xls", "数据", InspectionLogExcelVO.class, datas);
    }
    @GetMapping("/inspectionLog-page")
    @Operation(summary = "获得巡检详情分页")
    @TenantIgnore
    public CommonResult<PageResult<InspectionLogRespVO>> getInspectionLogPage(@Valid InspectionLogPageReqVO pageVO) {
        PageResult<InspectionLogRespVO> pageResult = inspectionLogService.getInspectionLogPage(pageVO);
        return success(pageResult);
    }
    @GetMapping("/record-list")
    @Operation(summary = "获得巡检记录列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @TenantIgnore
    public CommonResult<List<InspectionRecordRespVO>> getInspectionRecordList(@RequestParam("ids") Collection<Long> ids) {
        List<InspectionRecordDO> list = inspectionRecordService.getInspectionRecordList(ids);
        return success(InspectionRecordConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/record-page")
    @Operation(summary = "获得巡检记录分页")
    @TenantIgnore
    public CommonResult<PageResult<InspectionRecordRespVO>> getInspectionRecordPage(@Valid InspectionRecordPageReqVO pageVO) {
        PageResult<InspectionRecordDO> pageResult = inspectionRecordService.getInspectionRecordPage(pageVO);
        return success(InspectionRecordConvert.INSTANCE.convertPage(pageResult));
    }
    @GetMapping("/inspectionAssort")
    @Operation(summary = "获得最近一次巡检基本信息")
    @TenantIgnore
    public CommonResult<Map> getInspectionBaseInfo(@RequestParam Long tenantId) {
        return success(patrolInspectionService.getInspectionBaseInfo(tenantId));
    }

}
