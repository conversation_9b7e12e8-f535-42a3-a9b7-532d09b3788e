package cn.iocoder.zj.module.om.service.hardware;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo.ZstackCreateHardwareReqVo;
import cn.iocoder.zj.module.om.controller.admin.vncinfo.vo.VncInfoVo;

import java.util.Map;

public interface IZstackHardwareService {
    CommonResult<Map<String,String>> createHardwareToZstack(ZstackCreateHardwareReqVo reqVo);
    CommonResult<Map<String,String>> operateHardware(String uuid, Long platformId, String actions, String state);

    String hardwareOperateConvert(String operation, String state);

}
