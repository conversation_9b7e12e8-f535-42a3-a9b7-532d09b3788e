package cn.iocoder.zj.module.om.service.patrolresultcategory;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.module.om.controller.admin.patrolresultcategory.vo.*;
import cn.iocoder.zj.module.om.convert.patrolresultcategory.PatrolResultCategoryConvert;
import cn.iocoder.zj.module.om.dal.dataobject.patrolresultcategory.PatrolResultCategoryDO;
import cn.iocoder.zj.module.om.dal.mysql.patrolresultcategory.PatrolResultCategoryMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.om.enums.ErrorCodeConstants.PATROL_RESULT_CATEGORY_NOT_EXISTS;

/**
 * 巡检结果分类 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PatrolResultCategoryServiceImpl implements PatrolResultCategoryService {

    @Resource
    private PatrolResultCategoryMapper patrolResultCategoryMapper;

    @Override
    public Long createPatrolResultCategory(PatrolResultCategoryCreateReqVO createReqVO) {
        // 插入
        PatrolResultCategoryDO patrolResultCategory = PatrolResultCategoryConvert.INSTANCE.convert(createReqVO);
        patrolResultCategoryMapper.insert(patrolResultCategory);
        // 返回
        return patrolResultCategory.getId();
    }

    @Override
    public void updatePatrolResultCategory(PatrolResultCategoryUpdateReqVO updateReqVO) {
        // 校验存在
        validatePatrolResultCategoryExists(updateReqVO.getId());
        // 更新
        PatrolResultCategoryDO updateObj = PatrolResultCategoryConvert.INSTANCE.convert(updateReqVO);
        patrolResultCategoryMapper.updateById(updateObj);
    }

    @Override
    public void deletePatrolResultCategory(Long id) {
        // 校验存在
        validatePatrolResultCategoryExists(id);
        // 删除
        patrolResultCategoryMapper.deleteById(id);
    }

    private void validatePatrolResultCategoryExists(Long id) {
        if (patrolResultCategoryMapper.selectById(id) == null) {
            throw exception(PATROL_RESULT_CATEGORY_NOT_EXISTS);
        }
    }

    @Override
    public PatrolResultCategoryDO getPatrolResultCategory(Long id) {
        return patrolResultCategoryMapper.selectById(id);
    }

    @Override
    public List<PatrolResultCategoryDO> getPatrolResultCategoryList(Collection<Long> ids) {
        return patrolResultCategoryMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<PatrolResultCategoryDO> getPatrolResultCategoryPage(PatrolResultCategoryPageReqVO pageReqVO) {
        return patrolResultCategoryMapper.selectPage(pageReqVO);
    }

    @Override
    public List<PatrolResultCategoryDO> getPatrolResultCategoryList(PatrolResultCategoryExportReqVO exportReqVO) {
        return patrolResultCategoryMapper.selectList(exportReqVO);
    }

    @Override
    public List<PatrolResultCategoryDO> listByRecordId(PatrolResultCategoryReqVO vo) {
        LambdaQueryWrapperX<PatrolResultCategoryDO> queryWrapper = new LambdaQueryWrapperX<PatrolResultCategoryDO>()
                .eq(PatrolResultCategoryDO::getRecordId, vo.getRecordId());
        if (vo.getStatus() != null) {
            queryWrapper.eq(PatrolResultCategoryDO::getStatus, vo.getStatus());
        }
        return patrolResultCategoryMapper.selectList(queryWrapper);
    }

}
