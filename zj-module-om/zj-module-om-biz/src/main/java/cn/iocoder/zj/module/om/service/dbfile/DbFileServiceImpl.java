package cn.iocoder.zj.module.om.service.dbfile;

import cn.hutool.core.io.FileUtil;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.infra.api.file.FileApi;
import cn.iocoder.zj.module.om.controller.admin.dbfile.vo.DbFileCreateReqVO;
import cn.iocoder.zj.module.om.controller.admin.dbfile.vo.DbFileExportReqVO;
import cn.iocoder.zj.module.om.controller.admin.dbfile.vo.DbFilePageReqVO;
import cn.iocoder.zj.module.om.controller.admin.dbfile.vo.DbFileUpdateReqVO;
import cn.iocoder.zj.module.om.convert.dbfile.DbFileConvert;
import cn.iocoder.zj.module.om.dal.dataobject.dbfile.DbFileDO;
import cn.iocoder.zj.module.om.dal.mysql.dbfile.DbFileMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.io.*;
import java.sql.*;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.zip.GZIPInputStream;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.om.enums.ErrorCodeConstants.DB_FILE_NOT_EXISTS;

/**
 * 配置备份 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class DbFileServiceImpl implements DbFileService {
    /**
     * 用户名
     */
    @Value("${db.username}")
    private String root;
    /**
     * 密码
     */
    @Value("${db.password}")
    private String pwd;
    /**
     * 数据库名字
     */
    @Value("${db.dbname}")
    private String dbname;
    /**
     * ip
     *
     * @param args
     */
    @Value("${db.ip}")
    private String ip;

    @Resource
    private DbFileMapper dbFileMapper;

    @Resource
    private FileApi fileApi;
    String relativelyPath = System.getProperty("user.dir");


    @Override
    public Long createDbFile(DbFileCreateReqVO createReqVO) {
        // 插入
        DbFileDO dbFile = DbFileConvert.INSTANCE.convert(createReqVO);
        dbFileMapper.insert(dbFile);
        // 返回
        return dbFile.getId();
    }

    @Override
    public void updateDbFile(DbFileUpdateReqVO updateReqVO) {
        // 校验存在
        validateDbFileExists(updateReqVO.getId());
        // 更新
        DbFileDO updateObj = DbFileConvert.INSTANCE.convert(updateReqVO);
        dbFileMapper.updateById(updateObj);
    }

    @Override
    public void deleteDbFile(Long id) {
        // 校验存在
        validateDbFileExists(id);
        // 删除
        dbFileMapper.deleteById(id);
    }

    private void validateDbFileExists(Long id) {
        if (dbFileMapper.selectById(id) == null) {
            throw exception(DB_FILE_NOT_EXISTS);
        }
    }

    @Override
    public DbFileDO getDbFile(Long id) {
        return dbFileMapper.selectById(id);
    }

    @Override
    public List<DbFileDO> getDbFileList(Collection<Long> ids) {
        return dbFileMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<DbFileDO> getDbFilePage(DbFilePageReqVO pageReqVO) {
        return dbFileMapper.selectPage(pageReqVO);
    }

    @Override
    public List<DbFileDO> getDbFileList(DbFileExportReqVO exportReqVO) {
        return dbFileMapper.selectList(exportReqVO);
    }

    @SneakyThrows
    @Override
    public Boolean dbRestore(String url, String name) {
        byte[] bytes  = fileApi.ReaderObjects(url);
        byte[] readerObjects = decompressContent(bytes);
        InputStream i = new ByteArrayInputStream(readerObjects);
        System.out.println("下载成功");
        //输出流文件
        File fileSqls = new File(relativelyPath + "/dowfile/" + name);
        //创建备份sql文件
        if (!fileSqls.exists()) {
            fileSqls.getParentFile().mkdirs();
            try {
                fileSqls.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        try (OutputStream os = new FileOutputStream(fileSqls)) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = i.read(buffer)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
        }
        // 关闭文件流
        i.close();

        StringBuilder sb = new StringBuilder();
        sb.append("mysql");
        sb.append(" -h" + ip);
        sb.append(" -u" + root);
        sb.append(" -p" + pwd);
        sb.append(" " + dbname + " < ");
        sb.append(relativelyPath + "/dowfile/" + name);
        System.out.println("cmd命令为：" + sb.toString());
        Runtime runtime = Runtime.getRuntime();
        System.out.println("开始还原数据");
        try {
            String[] command = {"/bin/sh", "-c", sb.toString()};
            Process process = runtime.exec(command);
            //Process process = runtime.exec("/bin/sh -c " + sb.toString());
            InputStream is = process.getInputStream();
            BufferedReader bf = new BufferedReader(new InputStreamReader(is, "utf8"));
            String line = null;
            while ((line = bf.readLine()) != null) {
                System.out.println(line);
            }
            is.close();
            bf.close();
            log.info("获取到的状态为===========>:" + process.waitFor());
            int exitCode = process.waitFor();

            if (exitCode == 0 || exitCode == 1) {// 0 表示线程正常终止
                log.info("数据已从 " + relativelyPath + "/dowfile/" + name + " 导入到数据库中");
                FileUtil.del(relativelyPath + "/dowfile/" + name);
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
            log.error("Mysql数据导入数据库发生异常" + e.getMessage());
        }
        FileUtil.del(relativelyPath + "/dowfile/" + name);
        return false;
    }

    @TenantIgnore
    @Override
    public void updateByName(String name) {
        dbFileMapper.updateByName(name);
    }

    @SneakyThrows
    @TenantIgnore
    @Override
    public void updateState() {
        dbFileMapper.updateState();
        String jdbcUrl = "jdbc:mysql://" + ip + ":3306/" + dbname;
        String username = root;
        String password = pwd;
        try {
            // 1. 建立数据库连接
            Connection connection = DriverManager.getConnection(jdbcUrl, username, password);
            // 2. 获取所有表名
            List<String> tableNames = getAllTableNames(connection);
            // 3. 删除所有表
            for (String tableName : tableNames) {
                if (!tableName.contains("ACT") && !tableName.contains("FLW") && !tableName.contains("qrtz")) {
                    String dropTableSQL = "DROP TABLE IF EXISTS " + tableName;
                    executeSQLStatement(connection, dropTableSQL);
                }
            }
            System.out.println("所有表已成功删除");
            // 4. 关闭连接
            connection.close();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    private static List<String> getAllTableNames(Connection connection) throws SQLException {
        List<String> tableNames = new ArrayList<>();
        Statement statement = connection.createStatement();
        ResultSet resultSet = statement.executeQuery("SHOW TABLES");

        while (resultSet.next()) {
            tableNames.add(resultSet.getString(1));
        }

        resultSet.close();
        statement.close();

        return tableNames;
    }

    private static void executeSQLStatement(Connection connection, String sql) throws SQLException {
        Statement statement = connection.createStatement();
        statement.execute(sql);
        statement.close();
    }

    @TenantIgnore
    @Override
    public Long selectByState() {
        return dbFileMapper.selectCount("state", 1);
    }

    /**
     * 解压缩内容
     *
     * @param compressedContent 压缩后的内容
     * @return 原始内容
     */
    public byte[] decompressContent(byte[] compressedContent) {
        // 检查是否为GZIP格式
        if (compressedContent == null || compressedContent.length < 2 ||
                (compressedContent[0] != (byte) 0x1f || compressedContent[1] != (byte) 0x8b)) {
            // 不是GZIP格式，直接返回
            return compressedContent;
        }

        try (ByteArrayInputStream bais = new ByteArrayInputStream(compressedContent);
             GZIPInputStream gzipIn = new GZIPInputStream(bais);
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {

            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = gzipIn.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }

            byte[] originalData = baos.toByteArray();
            log.info("[decompressContent][解压前大小:{}KB, 解压后大小:{}KB]",
                    compressedContent.length / 1024, originalData.length / 1024);

            return originalData;
        } catch (IOException e) {
            log.error("[decompressContent][解压内容异常]", e);
            // 解压失败时返回原始内容
            return compressedContent;
        }
    }
}
