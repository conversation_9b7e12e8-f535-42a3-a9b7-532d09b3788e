package cn.iocoder.zj.module.om.dal.dataobject.patrolabnormaldetail;

import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 巡检异常明细 DO
 *
 * <AUTHOR>
 */
@TableName("om_patrol_abnormal_detail")
@KeySequence("om_patrol_abnormal_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PatrolAbnormalDetailDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 巡检记录ID
     */
    private Long recordId;
    /**
     * 巡检结果分类ID
     */
    private Long categoryId;
    /**
     * 资源类型(存储资源/宿主机资源/云主机资源/云硬盘资源等)
     */
    private String resourceType;
    /**
     * 指标名称(CPU分配/内存分配/网卡入速度等)
     */
    private String metricName;
    /**
     * 资源ID
     */
    private String resourceId;
    /**
     * 资源名称
     */
    private String resourceName;
    /**
     * 平台ID
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;
    /**
     * 详情
     */
    private String detail;
    /**
     * 风险等级(正常/低风险/中风险/高风险)
     */
    private String riskLevel;
    /**
     * 建议
     */
    private String suggest;
    /**
     * 租户ID
     */
    private Long tenantId;

}
