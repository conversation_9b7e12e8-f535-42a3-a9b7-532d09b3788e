package cn.iocoder.zj.module.om.Inspection.data;

import cn.iocoder.zj.module.om.dal.dataobject.patrolabnormaldetail.PatrolAbnormalDetailDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolresultcategory.PatrolResultCategoryDO;
import lombok.Data;

import java.util.List;

@Data
public class PatrolCompareResult {
    // 巡检结果分类
    private PatrolResultCategoryDO category;
    // 巡检异常列表
    private List<PatrolAbnormalDetailDO> abnormalDetails;
    // 异常总数
    private Integer totalAbnormalCount;
}
