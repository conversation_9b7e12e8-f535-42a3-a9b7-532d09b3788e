package cn.iocoder.zj.module.om.dal.dataobject.patrolplan;

import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * 巡检计划 DO
 *
 * <AUTHOR>
 */
@TableName("om_patrol_plan")
@KeySequence("om_patrol_plan_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PatrolPlanDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 计划名称
     */
    private String name;
    /**
     * 巡检周期(day-日, week-周, month-月)
     */
    private String periodType;
    /**
     * 执行日(日-不需要填;周-(1-7);月-(1-31))
     */
    private String executionDay;
    /**
     * 执行时间点
     */
    private String executionTime;
    /**
     * Cron表达式
     */
    private String executionCron;
    /**
     * 资源类型(多个以逗号分隔)
     */
    private String resourceType;
    /**
     * 巡检项(多个以逗号分隔)
     */
    private String patrolItem;
    /**
     * 状态(0-未启用, 1-启用)
     */
    private int status;
    /**
     * 上次执行时间
     */
    private Date lastExecutionTime;
    /**
     * 下次执行时间
     */
    private Date nextExecutionTime;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 管理员设置的租户的ID
     */
    private Long sysSettingTenant;

    /**
     * 多个平台ID，以逗号分隔
     */
    private String platformIds;

    /**
     * job编号
     */
    private Long jobId;

}
