package cn.iocoder.zj.module.om.dal.dataobject.dbfile;

import lombok.*;

import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 配置备份 DO
 *
 * <AUTHOR>
 */
@TableName("om_db_file")
@KeySequence("om_db_file_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DbFileDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 文件名
     */
    private String name;
    /**
     * 文件路径
     */
    private String path;
    /**
     * 文件url
     */
    private String url;
    /**
     * 文件大小
     */
    private Integer size;

    /**
     * @description: 使用状态 1 使用中 0 未使用
     * <AUTHOR>
     * @date 2023/10/12 15:00
     * @version 1.0
     */
    private Integer state;

}
