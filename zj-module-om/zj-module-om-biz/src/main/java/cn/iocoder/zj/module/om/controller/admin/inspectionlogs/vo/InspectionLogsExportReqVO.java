package cn.iocoder.zj.module.om.controller.admin.inspectionlogs.vo;

import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 自动巡检异常记录 Excel 导出 Request VO，参数和 InspectionLogsPageReqVO 是一致的")
@Data
public class InspectionLogsExportReqVO {

    @Schema(description = "巡检记录id")
    private String recordUuid;

    @Schema(description = "巡检名称")
    private String inspectionName;

    @Schema(description = "阈值")
    private BigDecimal threshold;

    @Schema(description = "资产名称")
    private String assetName;

    @Schema(description = "资产id")
    private String assetUuid;

    @Schema(description = "平台id")
    private String platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "资产的值")
    private String value;

    @Schema(description = "创建人名称")
    private String creatorName;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "检测结果")
    private String result;

}
