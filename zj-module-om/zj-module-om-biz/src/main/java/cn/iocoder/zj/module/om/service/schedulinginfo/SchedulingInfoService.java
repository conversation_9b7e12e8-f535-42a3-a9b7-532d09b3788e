package cn.iocoder.zj.module.om.service.schedulinginfo;

import java.util.*;
import javax.validation.*;
import cn.iocoder.zj.module.om.controller.admin.schedulinginfo.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.schedulinginfo.SchedulingInfoDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 运维排班信息 Service 接口
 *
 * <AUTHOR>
 */
public interface SchedulingInfoService {

    /**
     * 创建运维排班信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSchedulingInfo(@Valid SchedulingInfoCreateReqVO createReqVO);

    /**
     * 更新运维排班信息
     *
     * @param updateReqVO 更新信息
     */
    void updateSchedulingInfo(@Valid SchedulingInfoUpdateReqVO updateReqVO);

    /**
     * 删除运维排班信息
     *
     * @param id 编号
     */
    void deleteSchedulingInfo(Long id);

    /**
     * 获得运维排班信息
     *
     * @param id 编号
     * @return 运维排班信息
     */
    SchedulingInfoDO getSchedulingInfo(Long id);

    /**
     * 获得运维排班信息列表
     *
     * @param ids 编号
     * @return 运维排班信息列表
     */
    List<SchedulingInfoDO> getSchedulingInfoList(Collection<Long> ids);

    /**
     * 获得运维排班信息分页
     *
     * @param pageReqVO 分页查询
     * @return 运维排班信息分页
     */
    PageResult<SchedulingInfoDO> getSchedulingInfoPage(SchedulingInfoPageReqVO pageReqVO);

    /**
     * 获得运维排班信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 运维排班信息列表
     */
    List<SchedulingInfoDO> getSchedulingInfoList(SchedulingInfoExportReqVO exportReqVO);

    List<SchedulingInfoRespVO> queryScheduling(Long startMillis,Long endMillis);

    Long getSchedulCount(Long id, String name);
}
