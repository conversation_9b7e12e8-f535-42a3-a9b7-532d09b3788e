package cn.iocoder.zj.module.om.convert.userbind;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.om.controller.admin.userbind.vo.UserBindCreateReqVO;
import cn.iocoder.zj.module.om.controller.admin.userbind.vo.UserBindExcelVO;
import cn.iocoder.zj.module.om.controller.admin.userbind.vo.UserBindRespVO;
import cn.iocoder.zj.module.om.controller.admin.userbind.vo.UserBindUpdateReqVO;
import cn.iocoder.zj.module.om.dal.dataobject.userbind.UserBindDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 用户推送绑定 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface UserBindConvert {

    UserBindConvert INSTANCE = Mappers.getMapper(UserBindConvert.class);

    UserBindDO convert(UserBindCreateReqVO bean);

    UserBindDO convert(UserBindUpdateReqVO bean);

    UserBindRespVO convert(UserBindDO bean);

    List<UserBindRespVO> convertList(List<UserBindDO> list);

    PageResult<UserBindRespVO> convertPage(PageResult<UserBindDO> page);

    List<UserBindExcelVO> convertList02(List<UserBindDO> list);

}
