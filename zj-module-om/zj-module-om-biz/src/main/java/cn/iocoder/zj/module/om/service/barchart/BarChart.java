//package cn.iocoder.zj.module.om.service.barchart;
//
//
//import cn.hutool.core.collection.CollectionUtil;
//import cn.hutool.core.util.RandomUtil;
//import cn.iocoder.zj.module.om.service.barchart.dto.HealthKitLetterInfo;
//import cn.iocoder.zj.module.om.service.barchart.dto.PointPositionForm;
//import com.deepoove.poi.XWPFTemplate;
//import com.deepoove.poi.data.*;
//import com.deepoove.poi.xwpf.NiceXWPFDocument;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.poi.ooxml.POIXMLDocumentPart;
//import org.apache.poi.xwpf.usermodel.XWPFChart;
//import org.apache.poi.xwpf.usermodel.XWPFDocument;
//import org.springframework.core.io.ClassPathResource;
//
//import java.io.*;
//import java.nio.file.Files;
//import java.nio.file.Paths;
//import java.text.SimpleDateFormat;
//import java.util.*;
//
//
///**
// * 柱状图处理类（柱状图、条形图）图表系列固定的
// */
//@Slf4j
//public class BarChart {
//
//    public static void main(String[] args) throws Exception {
//
//
//        Map<String, Object> paramMap = new HashMap<>();
//        paramMap.put("sTime", "2021-05-01");
//        paramMap.put("eTime", "2021-06-01");
//        paramMap.put("cloud", 6);
//        paramMap.put("host", 5);
//        paramMap.put("data", 65);
//
//
//        // create table
//        //word的 表格
//        List tableList = new ArrayList<>();
//        tableList.add("数据1");
//        tableList.add("数据2");
//        tableList.add("数据3");
//        tableList.add("数据4");
//        tableList.add("数据5");
//        tableList.add("数据6");
//        //验证类型集合
//        List<String> typeList = Arrays.asList("公网IP",
//                "私网IP",
//                "CPU(核)",
//                "内存(G)",
//                "数据盘(G)",
//                "操作系统");
//        //表头是两行
//        //第一行数据是固定内容 "序号", "章节号", "项目", "内容", "验证方法", "备注"
//        List<String> headerCellListTemp1 = Arrays.asList("序号", "主机名称", "资源池名称", "IP地址", " ", "ESC配置");
//        List<String> headerCellList1 = new ArrayList<>(headerCellListTemp1);
//        //验证方法，可能是多列数据  第一列是验证方法，后面是赋空字符串，用于合并单元格;
//        //数据类型：验证方法 "" "" ""
//        //验证类型长度
//        int typeListSize = typeList.size();
//        for (int k = 0; k < typeListSize - 3; k++) {
//            headerCellList1.add(" "); //赋空字符串
//        }
//        headerCellList1.add("备注");
//
//        String[] headerCell1 = headerCellList1.toArray(new String[headerCellList1.size()]);
//
//        RowRenderData header1 = Rows.of(headerCell1).center().bgColor("DFDED9").create();
//
//
//        //headerCell2 :验证方法对应列，是动态内容
//        List<String> headerCellListTemp2 = Arrays.asList("序号", "主机名称", "资源池名称");
//        List<String> headerCellList2 = new ArrayList<>(headerCellListTemp2);
//        // 验证方法对应列
//        headerCellList2.addAll(typeList);
//        //备注对应的列 赋空值
//        headerCellList2.add(" ");
//
//        String[] headerCell2 = headerCellList2.toArray(new String[headerCellList2.size()]);
//        RowRenderData header2 = Rows.of(headerCell2).center().bgColor("DFDED9").create();
//        //表格 列的数量
//        int cellLength = headerCell1.length;
//
//        //无数据只有表头数据 tableList 无值
//        if (CollectionUtil.isEmpty(tableList)) {
//            RowRenderData[] RowRenderDataHeader = new RowRenderData[2];
//            RowRenderDataHeader[0] = header1;
//            RowRenderDataHeader[1] = header2;
//
//            TableRenderData check_table = setTableRenderDataAndColWidth(RowRenderDataHeader, cellLength);
//
//
//            // table 只有表头数据
//            paramMap.put("check_table", check_table);
//        } else {
//            int length = 2 + tableList.size();
//            //行数据 数组
//            RowRenderData[] RowRenderData = new RowRenderData[length];
//
//            RowRenderData[0] = header1;
//            RowRenderData[1] = header2;
//
//            for (int i = 0; i < tableList.size(); i++) {
//
//                String index = Integer.toString(i + 1);
//                List<String> tempList = Arrays.asList("A", "B", "C",
//                        "D", "E", "F");
//
//                String[] cellString = new String[cellLength];
//                cellString[0] = index; //序号
//                cellString[1] = "主机名称";//标题序号
//                cellString[2] = "资源名称"; // 项目
//                cellString[3] = "资源池名称"; //内容 ： 描述
//                //验证方法对应数据赋值
//                if (headerCell2.length > 3) {
//                    for (int j = 0; j < tempList.size(); j++) {
//                        cellString[j + 3] = tempList.get(j);
//                    }
//                }
//                //备注
//                cellString[cellLength - 1] = "备注";
//                RowRenderData rowRenderData = Rows.of(cellString).
//                        center().create();
//                //行数据赋值
//                RowRenderData[2 + i] = rowRenderData;
//
//            }
//            TableRenderData check_table = setTableRenderDataAndColWidth(RowRenderData, cellLength);
//
//            // table
//            paramMap.put("check_table", check_table);
//
//        }
//
//
//        //==============性能数据===============
//
//        //验证类型集合
//        List<String> xnlb = Arrays.asList("CPU",
//                "内存",
//                "磁盘");
//        //表头是两行
//        //第一行数据是固定内容 "序号", "章节号", "项目", "内容", "验证方法", "备注"
//        List<String> xnlbTemp1 = Arrays.asList("序号", "主机名称", "私网IP", "平均使用率");
//        List<String> headerCellList3 = new ArrayList<>(xnlbTemp1);
//
//        int xnlbsize = xnlb.size();
//        for (int k = 0; k < xnlbsize - 1; k++) {
//            headerCellList3.add(" "); //赋空字符串
//        }
//        headerCellList3.add("备注");
//
//
//        String[] headerCell3 = headerCellList3.toArray(new String[headerCellList3.size()]);
//
//        RowRenderData header3 = Rows.of(headerCell3).center().bgColor("DFDED9").create();
//
//
//        //headerCell2 :验证方法对应列，是动态内容
//        List<String> headerCellListTemp3 = Arrays.asList("序号", "主机名称", "资源池名称");
//        List<String> headerCellList4 = new ArrayList<>(headerCellListTemp3);
//        // 验证方法对应列
//        headerCellList4.addAll(xnlb);
//        //备注对应的列 赋空值
//        headerCellList4.add(" ");
//
//
//        String[] headerCell4 = headerCellList4.toArray(new String[headerCellList4.size()]);
//        RowRenderData header4 = Rows.of(headerCell4).center().bgColor("DFDED9").create();
//        //表格 列的数量
//        int cellLength4 = headerCell3.length;
//
//
//        //无数据只有表头数据 tableList 无值
//        if (CollectionUtil.isEmpty(tableList)) {
//            RowRenderData[] RowRenderDataHeader = new RowRenderData[2];
//            RowRenderDataHeader[0] = header3;
//            RowRenderDataHeader[1] = header4;
//
//            TableRenderData check_table2 = setTableRenderDataAndColWidth2(RowRenderDataHeader, cellLength4);
//
//
//            // table 只有表头数据
//            paramMap.put("check_table1", check_table2);
//        } else {
//            int length2 = 2 + tableList.size();
//            //行数据 数组
//            RowRenderData[] RowRenderData2 = new RowRenderData[length2];
//
//            RowRenderData2[0] = header3;
//            RowRenderData2[1] = header4;
//
//
//            for (int i = 0; i < tableList.size(); i++) {
//
//                String index = Integer.toString(i + 1);
//                List<String> tempList = Arrays.asList("A", "B", "C");
//
//                String[] cellString = new String[cellLength4];
//                cellString[0] = index; //序号
//                cellString[1] = "主机名称";//标题序号
//                cellString[2] = "私网ip"; // 项目
//                cellString[3] = "资源池名称"; //内容 ： 描述
//                //验证方法对应数据赋值
//                if (headerCell4.length > 3) {
//                    for (int j = 0; j < tempList.size(); j++) {
//                        cellString[j + 3] = tempList.get(j);
//                    }
//                }
//                //备注
//                cellString[cellLength4 - 1] = "备注";
//                RowRenderData rowRenderData = Rows.of(cellString).
//                        center().create();
//                //行数据赋值
//                RowRenderData2[2 + i] = rowRenderData;
//
//            }
//            TableRenderData check_table2 = setTableRenderDataAndColWidth2(RowRenderData2, cellLength4);
//
//
//            // table
//            paramMap.put("check_table1", check_table2);
//
//
//        }
//
//
//
//        List<Map<String, ChartMultiSeriesRenderData>> charts = new ArrayList<>();
//
//
//        // 创建一个存储日期的数组
//        String[] dates = new String[10];
//        Double[] cpu = new Double[10];
//        Double[] me = new Double[10];
//        Double[] di = new Double[10];
//// 使用循环生成日期并存储到数组中
//        for (int i = 0; i < 10; i++) {
//            // 使用日期格式化工具创建格式化后的日期字符串，这里使用了"yyyy-MM"格式
//            SimpleDateFormat sdf = new SimpleDateFormat("MM-dd HH:mm");
//
//            // 获取当前日期
//            Calendar calendar = Calendar.getInstance();
//            calendar.add(Calendar.MONTH, i); // 添加i个月
//
//            // 格式化日期并存储到数组中
//            dates[i] = sdf.format(calendar.getTime());
//            cpu[i] = RandomUtil.randomDouble(100);
//            me[i] = RandomUtil.randomDouble(200);
//            di[i] = RandomUtil.randomDouble(300);
//        }
//
//
//
//
//        for (int i = 0; i < 10; i++) {
//            Map<String, ChartMultiSeriesRenderData> ch = new HashMap<>();
//            ChartMultiSeriesRenderData chartMultiSeriesRenderData = new ChartMultiSeriesRenderData();
//            chartMultiSeriesRenderData.setChartTitle("标题" + i);
//            chartMultiSeriesRenderData.setCategories(dates);
//            List<SeriesRenderData> seriesDatas = new ArrayList<>();
//
//            seriesDatas.add(new SeriesRenderData("CPU", cpu));
//            seriesDatas.add(new SeriesRenderData("内存",me));
//            seriesDatas.add(new SeriesRenderData("磁盘", di));
//            chartMultiSeriesRenderData.setSeriesDatas(seriesDatas);
//            ch.put("barChart", chartMultiSeriesRenderData);
//            charts.add(ch);
//        }
//
//        paramMap.put("charts", charts);
//
//        // 生成文件
//        Map<String, String> resultMap = createPdf(paramMap);
//
//
//    }
//
//    private static TableRenderData setTableRenderDataAndColWidth2(RowRenderData[] rowRenderDataArray, Integer cellLength) {
//        //table赋值set方法需要list
//        List<RowRenderData> RowRenderDataList = Arrays.asList(rowRenderDataArray);
//        //设置列宽：
//        double[] colWidthsCm = new double[cellLength];
//        for (int i = 0; i < cellLength; i++) {
//            // "主机名称", "资源池" 设置为 2
//            if (i == 1 || i == 2) {
//                colWidthsCm[i] = 2D;
//            } else {
//                colWidthsCm[i] = 1D;
//            }
//        }
//        //18.450000762939453D A4纸张
//        TableRenderData check_table = Tables.ofPercentWidth("100%").center().create();
//        check_table.setRows(RowRenderDataList);
//        //合并单元格
//        MergeCellRule.MergeCellRuleBuilder mergeCellRuleBuilder = mergeCell2(cellLength);
//        check_table.setMergeRule(mergeCellRuleBuilder.build());
//
//        return check_table;
//    }
//
//    private static MergeCellRule.MergeCellRuleBuilder mergeCell2(int cellLength) {
//        /**
//         * 设置表格合并规则 从0开始
//         * 1.起始行 MergeCellRule.Grid.of(i, j) i: 行 j: 列
//         * 2.结束行 MergeCellRule.Grid.of(i, j) i: 行 j: 列
//         */
//        //合并单元格
//        //合并到【备注】前一列
//        MergeCellRule.MergeCellRuleBuilder mergeCellRuleBuilder = MergeCellRule.builder();
//        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, 0), MergeCellRule.Grid.of(1, 0));//序号合并
//        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, 1), MergeCellRule.Grid.of(1, 1));//主机名称合并
//        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, 2), MergeCellRule.Grid.of(1, 2));//资源池名称合并
//        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, 3), MergeCellRule.Grid.of(0, cellLength - 2));//内容合并 第一行
//        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, cellLength - 1), MergeCellRule.Grid.of(1, cellLength - 1));//备注合并
//        return mergeCellRuleBuilder;
//    }
//
//    private static Map<String, String> createPdf(Map<String, Object> paramMap) {
//        log.info("生成电子协查函参数paramMap = {}", paramMap);
//        // 文件存放根目录
//        String rootPath = "E:\\daochu" + File.separator;
//        File dir = new File(rootPath);
//        // 文件夹不存在则创建
//        if (!dir.exists()) {
//            boolean mkdir = dir.mkdir();
//            if (!mkdir) {
//                log.error("文件目录创建失败");
//            }
//        }
//        String docxPath = rootPath + "健康宝-同时空协查函" + ".docx";
//        String imgPath = rootPath + "image" + File.separator;
//        // 获取电子协查函模板
//
//        String path = "file-template" + File.separator + "word.docx";
//        // 根据模板渲染word、并且生成pdf文件
//        ClassPathResource classPathResource = new ClassPathResource(path);
//        try (InputStream inputStream = classPathResource.getInputStream()) {
//            if (Objects.isNull(inputStream)) {
//                log.error("获取电子协查函模板失败");
//            }
//            // 通过协查函模板，开始生成电子协查函
//            try (XWPFTemplate template = XWPFTemplate.compile(inputStream).render(paramMap);
//                 OutputStream outDocx = Files.newOutputStream(Paths.get(docxPath))) {
//                NiceXWPFDocument xwpfDocument = template.getXWPFDocument();
//
//                // 输出word
//                template.getXWPFDocument().write(outDocx);
//            }
//        } catch (Exception e) {
//            log.error("创建协查函异常，异常详情：\n{}", e);
//        }
//        Map<String, String> map = new HashMap<>(4);
//        map.put("rootPath", rootPath);
//        map.put("docxPath", docxPath);
//        return map;
//    }
//
//
//    /**
//     * 表格赋值，
//     * 设置列宽和合并单元格
//     *
//     * @param rowRenderDataArray
//     * @param cellLength
//     * @return
//     */
//    private static TableRenderData setTableRenderDataAndColWidth(RowRenderData[] rowRenderDataArray, Integer cellLength) {
//        //table赋值set方法需要list
//        List<RowRenderData> RowRenderDataList = Arrays.asList(rowRenderDataArray);
//        //设置列宽：
//        double[] colWidthsCm = new double[cellLength];
//        for (int i = 0; i < cellLength; i++) {
//            // "主机名称", "资源池" 设置为 2
//            if (i == 1 || i == 2) {
//                colWidthsCm[i] = 2D;
//            } else {
//                colWidthsCm[i] = 1D;
//            }
//        }
//        //18.450000762939453D A4纸张
//        TableRenderData check_table = Tables.ofPercentWidth("100%").center().create();
//        check_table.setRows(RowRenderDataList);
//        //合并单元格
//        MergeCellRule.MergeCellRuleBuilder mergeCellRuleBuilder = mergeCell(cellLength);
//        check_table.setMergeRule(mergeCellRuleBuilder.build());
//
//        return check_table;
//    }
//
//    /**
//     * 表格 合并单元格
//     *
//     * @param cellLength
//     * @return
//     */
//    private static MergeCellRule.MergeCellRuleBuilder mergeCell(Integer cellLength) {
//        /**
//         * 设置表格合并规则 从0开始
//         * 1.起始行 MergeCellRule.Grid.of(i, j) i: 行 j: 列
//         * 2.结束行 MergeCellRule.Grid.of(i, j) i: 行 j: 列
//         */
//        //合并单元格
//        //合并到【备注】前一列
//        MergeCellRule.MergeCellRuleBuilder mergeCellRuleBuilder = MergeCellRule.builder();
//        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, 0), MergeCellRule.Grid.of(1, 0));//序号合并
//        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, 1), MergeCellRule.Grid.of(1, 1));//主机名称合并
//        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, 2), MergeCellRule.Grid.of(1, 2));//资源池名称合并
//        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, 3), MergeCellRule.Grid.of(0, cellLength - 6));//内容合并 第一行
//        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, 5), MergeCellRule.Grid.of(0, cellLength - 2));//验证类型合并 第一行
//        mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, cellLength - 1), MergeCellRule.Grid.of(1, cellLength - 1));//备注合并
//        return mergeCellRuleBuilder;
//    }
//
//
//}
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
