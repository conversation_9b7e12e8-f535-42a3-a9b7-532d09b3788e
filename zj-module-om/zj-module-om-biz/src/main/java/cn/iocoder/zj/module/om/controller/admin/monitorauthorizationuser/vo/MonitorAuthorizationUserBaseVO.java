package cn.iocoder.zj.module.om.controller.admin.monitorauthorizationuser.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 用户资产授权申请 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class MonitorAuthorizationUserBaseVO {

    @Schema(description = "授权资产表")
    private Long monitorAssetId;

    @Schema(description = "申请人id")
    private Long authorizationUserId;

    @Schema(description = "授权状态")
    private String authorizationType;

}
