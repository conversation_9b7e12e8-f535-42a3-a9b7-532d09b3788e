package cn.iocoder.zj.module.om.controller.admin.monitorasset;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.om.controller.admin.monitorasset.vo.*;
import cn.iocoder.zj.module.om.convert.monitorasset.MonitorAssetConvert;
import cn.iocoder.zj.module.om.dal.dataobject.monitorasset.MonitorAssetDO;
import cn.iocoder.zj.module.om.service.monitorasset.MonitorAssetService;
import cn.iocoder.zj.module.om.util.StringUtil;
import cn.iocoder.zj.module.om.util.dingtalk.DingCallbackCrypto;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.OTHER;

@Tag(name = "管理后台 - 监控资产")
@RestController
@RequestMapping("/om/monitor-asset")
@Validated
public class MonitorAssetController {

    @Resource
    private MonitorAssetService monitorAssetService;


    @PostMapping("/create")
    @Operation(summary = "创建监控资产")
    @PreAuthorize("@ss.hasPermission('om:monitor-asset:create')")
    public CommonResult<Long> createMonitorAsset(@Valid @RequestBody MonitorAssetCreateReqVO createReqVO) {
        return success(monitorAssetService.createMonitorAsset(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新监控资产")
    @PreAuthorize("@ss.hasPermission('om:monitor-asset:update')")
    public CommonResult<Boolean> updateMonitorAsset(@Valid @RequestBody MonitorAssetUpdateReqVO updateReqVO) {
        monitorAssetService.updateMonitorAsset(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除监控资产")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('om:monitor-asset:delete')")
    public CommonResult<Boolean> deleteMonitorAsset(@RequestParam("id") Long id) {
        monitorAssetService.deleteMonitorAsset(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得监控资产")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('om:monitor-asset:query')")
    public CommonResult<MonitorAssetRespVO> getMonitorAsset(@RequestParam("id") Long id) {
        MonitorAssetDO monitorAsset = monitorAssetService.getMonitorAsset(id);
        return success(MonitorAssetConvert.INSTANCE.convert(monitorAsset));
    }

    @GetMapping("/list")
    @Operation(summary = "获得监控资产列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('om:monitor-asset:query')")
    public CommonResult<List<MonitorAssetRespVO>> getMonitorAssetList(@RequestParam("ids") Collection<Long> ids) {
        List<MonitorAssetDO> list = monitorAssetService.getMonitorAssetList(ids);
        return success(MonitorAssetConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得监控资产分页")
    @PreAuthorize("@ss.hasPermission('om:monitor-asset:query')")
    public CommonResult<PageResult<MonitorAssetRespVO>> getMonitorAssetPage(@Valid MonitorAssetPageReqVO pageVO) {
        PageResult<MonitorAssetDO> pageResult = monitorAssetService.getMonitorAssetPage(pageVO);
        return success(MonitorAssetConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出监控资产 Excel")
    @PreAuthorize("@ss.hasPermission('om:monitor-asset:export')")
    @OperateLog(type = EXPORT)
    public void exportMonitorAssetExcel(@Valid MonitorAssetExportReqVO exportReqVO,
                                        HttpServletResponse response) throws IOException {
        List<MonitorAssetDO> list = monitorAssetService.getMonitorAssetList(exportReqVO);
        // 导出 Excel
        List<MonitorAssetExcelVO> datas = MonitorAssetConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "监控资产.xls", "数据", MonitorAssetExcelVO.class, datas);
    }

    @GetMapping("/get-assetselect")
    @Operation(summary = "获得资产下拉框")
    @Parameter(name = "platformId", description = "平台id", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('om:monitor-asset:query')")
    public CommonResult<List<MonitorAssetSelectRespVO>> getMonitorAssetSelect(@RequestParam("platformId") Long platformId,@RequestParam("category") String category) {
        List<MonitorAssetSelectRespVO> list = monitorAssetService.getMonitorAssetSelect(platformId,category);
        return success(list);
    }


    @GetMapping("/wx-authorization")
    @Operation(summary = "微信申请授权")
    @Parameter(name = "platformId", description = "平台id", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('om:monitor-asset:auth')")
    public CommonResult<Boolean> authorization(@RequestParam("id") Long id,
                                               @RequestParam("platformId") Long platformId) {

        boolean au = monitorAssetService.authorization(platformId, id);
        return success(au);
    }

    @PutMapping("/updateAuthorizationType")
    @Operation(summary = "更新授权状态")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @Parameter(name = "authorizationType", description = "授权状态", required = true, example = "0:拒绝 2:同意")
    @PreAuthenticated
    public CommonResult<Boolean> updateAuthorizationType(@Valid @RequestBody MonitorAssetUpdateTypeReqVO monitorAssetUpdateTypeReqVO) {
        monitorAssetService.updateAuthorizationTypeImpl(monitorAssetUpdateTypeReqVO);
        return success(true);
    }

    @GetMapping("/wework-authorization")
    @Operation(summary = "企业微信申请授权")
    @Parameter(name = "platformId", description = "平台id", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('om:monitor-asset:auth')")
    public CommonResult<Boolean> weworkAuthorization(@RequestParam("id") Long id,
                                               @RequestParam("platformId") Long platformId) {

        boolean au = monitorAssetService.weworkAuthorization(platformId, id);
        return success(au);
    }

    @PostMapping("/wework-callback")
    @Operation(summary = "企业微信授权回调")
    @OperateLog(enable = false)
    @PermitAll
    @TenantIgnore
    public String weworkCallback(@RequestParam(value = "msg_signature", required = false) String msgSignature,
                                @RequestParam(value = "timestamp", required = false) String timestamp,
                                @RequestParam(value = "nonce", required = false) String nonce,
                                @RequestParam(value = "echostr", required = false) String echoStr,
                                @RequestBody(required = false) String xml)
    {
        return monitorAssetService.weworkCallback(msgSignature,timestamp,nonce,echoStr,xml);
    }

    @PermitAll
    @GetMapping("/wework-checksign")
    @Operation(summary = "企业微信授权回调验证")
    @OperateLog(enable = false)
    @TenantIgnore
    public String checkSign(@RequestParam(value = "msg_signature", required = false) String msgSignature,
                            @RequestParam(value = "timestamp", required = false) String timestamp,
                            @RequestParam(value = "nonce", required = false) String nonce,
                            @RequestParam(value = "echostr", required = false) String echoStr,
                            @RequestBody(required = false) String xml)
    {
        return monitorAssetService.checkSign(msgSignature,timestamp,nonce,echoStr,xml);
    }


    @GetMapping("/dingtalk-authorization")
    @Operation(summary = "钉钉申请授权")
    @Parameter(name = "platformId", description = "平台id", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('om:monitor-asset:auth')")
    public CommonResult<Boolean> dingtalkAuthorization(@RequestParam("id") Long id,
                                                     @RequestParam("platformId") Long platformId) {

        boolean au = monitorAssetService.dingtalkAuthorization(platformId, id);
        return success(au);
    }

    @PermitAll
    @OperateLog(enable = false)
    @PostMapping("/dingtalk-callback")
    @Operation(summary = "钉钉授权回调")
    @TenantIgnore
    public Map<String, String> dingtalkCheckSign(@RequestParam(value = "signature", required = false) String signature,
                                  @RequestParam(value = "timestamp", required = false) String timestamp,
                                  @RequestParam(value = "nonce", required = false) String nonce,
                                  @RequestBody(required = false) JSONObject body){
        try {
            if(StringUtil.isNotEmpty(signature)&& StringUtil.isNotEmpty(timestamp) && StringUtil.isNotEmpty(nonce)){
                String encryptMsg = body.getString("encrypt");
                DingCallbackCrypto callbackCrypto = new DingCallbackCrypto("uwwF6o6", "XtuA8xEA1VgqExn9iaqeGdR48GewhdCWEFTFe2HaTsL",
                        "dingvkpgrknyfcdghv9w");
                String decryptMsg = callbackCrypto.getDecryptMsg(signature, timestamp, nonce, encryptMsg);
                Map<String, String> successMap = callbackCrypto.getEncryptedMap("success");
                return successMap;
            }else {
                monitorAssetService.dingtalkCallback(body);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @GetMapping("/select-authorization")
    @Operation(summary = "综合申请授权")
    @OperateLog(type = OTHER)
    @Parameter(name = "platformId", description = "平台id", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('om:monitor-asset:auth')")
    public CommonResult<Boolean> selectAuthorization(@RequestParam("id") Long id,
                                                       @RequestParam("platformId") Long platformId) {

        boolean au = monitorAssetService.selectAuthorization(platformId, id);
        return success(au);
    }
}
