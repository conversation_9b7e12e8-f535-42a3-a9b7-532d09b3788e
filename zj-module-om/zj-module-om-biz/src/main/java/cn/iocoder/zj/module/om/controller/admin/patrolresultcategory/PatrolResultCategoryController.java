package cn.iocoder.zj.module.om.controller.admin.patrolresultcategory;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.module.om.controller.admin.patrolresultcategory.vo.*;
import cn.iocoder.zj.module.om.convert.patrolresultcategory.PatrolResultCategoryConvert;
import cn.iocoder.zj.module.om.dal.dataobject.patrolresultcategory.PatrolResultCategoryDO;
import cn.iocoder.zj.module.om.service.patrolresultcategory.PatrolResultCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

@Tag(name = "管理后台 - 巡检结果分类")
@RestController
@RequestMapping("/om/patrol-result-category")
@Validated
public class PatrolResultCategoryController {

    @Resource
    private PatrolResultCategoryService patrolResultCategoryService;

    @PostMapping("/create")
    @Operation(summary = "创建巡检结果分类")
    @PreAuthorize("@ss.hasPermission('om:patrol-result-category:create')")
    public CommonResult<Long> createPatrolResultCategory(@Valid @RequestBody PatrolResultCategoryCreateReqVO createReqVO) {
        return success(patrolResultCategoryService.createPatrolResultCategory(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新巡检结果分类")
    @PreAuthorize("@ss.hasPermission('om:patrol-result-category:update')")
    public CommonResult<Boolean> updatePatrolResultCategory(@Valid @RequestBody PatrolResultCategoryUpdateReqVO updateReqVO) {
        patrolResultCategoryService.updatePatrolResultCategory(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除巡检结果分类")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('om:patrol-result-category:delete')")
    public CommonResult<Boolean> deletePatrolResultCategory(@RequestParam("id") Long id) {
        patrolResultCategoryService.deletePatrolResultCategory(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得巡检结果分类")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('om:patrol-result-category:query')")
    public CommonResult<PatrolResultCategoryRespVO> getPatrolResultCategory(@RequestParam("id") Long id) {
        PatrolResultCategoryDO patrolResultCategory = patrolResultCategoryService.getPatrolResultCategory(id);
        return success(PatrolResultCategoryConvert.INSTANCE.convert(patrolResultCategory));
    }

    @GetMapping("/list")
    @Operation(summary = "获得巡检结果分类列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('om:patrol-result-category:query')")
    public CommonResult<List<PatrolResultCategoryRespVO>> getPatrolResultCategoryList(@RequestParam("ids") Collection<Long> ids) {
        List<PatrolResultCategoryDO> list = patrolResultCategoryService.getPatrolResultCategoryList(ids);
        return success(PatrolResultCategoryConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/listByRecordId")
    @Operation(summary = "获得巡检结果分类列表")
    @PreAuthorize("@ss.hasPermission('om:patrol-result-category:query')")
    public CommonResult<List<PatrolResultCategoryRespVO>> listByRecordId(@Valid PatrolResultCategoryReqVO vo) {
        List<PatrolResultCategoryDO> list = patrolResultCategoryService.listByRecordId(vo);
        return success(PatrolResultCategoryConvert.INSTANCE.convertList(list));
    }


    @GetMapping("/page")
    @Operation(summary = "获得巡检结果分类分页")
    @PreAuthorize("@ss.hasPermission('om:patrol-result-category:query')")
    public CommonResult<PageResult<PatrolResultCategoryRespVO>> getPatrolResultCategoryPage(@Valid PatrolResultCategoryPageReqVO pageVO) {
        PageResult<PatrolResultCategoryDO> pageResult = patrolResultCategoryService.getPatrolResultCategoryPage(pageVO);
        return success(PatrolResultCategoryConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出巡检结果分类 Excel")
    @PreAuthorize("@ss.hasPermission('om:patrol-result-category:export')")
    @OperateLog(type = EXPORT)
    public void exportPatrolResultCategoryExcel(@Valid PatrolResultCategoryExportReqVO exportReqVO,
                                                HttpServletResponse response) throws IOException {
        List<PatrolResultCategoryDO> list = patrolResultCategoryService.getPatrolResultCategoryList(exportReqVO);
        // 导出 Excel
        List<PatrolResultCategoryExcelVO> datas = PatrolResultCategoryConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "巡检结果分类.xls", "数据", PatrolResultCategoryExcelVO.class, datas);
    }

}
