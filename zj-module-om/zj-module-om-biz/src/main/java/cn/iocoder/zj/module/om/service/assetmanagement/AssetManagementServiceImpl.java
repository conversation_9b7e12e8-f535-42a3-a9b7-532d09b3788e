package cn.iocoder.zj.module.om.service.assetmanagement;

import cn.iocoder.cloud.module.guacamole.api.OnlineSessionApi;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.monitor.api.hostinfo.HostInfoApi;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.om.api.asset.dto.VncInfoDTO;
import cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo.OperateLogPageReqVO;
import cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo.ResourceOperateLog;
import cn.iocoder.zj.module.om.controller.admin.vncinfo.vo.VncInfoVo;
import cn.iocoder.zj.module.om.dal.mysql.assetmanagement.AssetManagementMapper;
import cn.iocoder.zj.module.om.service.cloud.ISangForCloudService;
import cn.iocoder.zj.module.om.service.cloud.IVmwareService;
import cn.iocoder.zj.module.om.service.cloud.IWinHongCloudService;
import cn.iocoder.zj.module.om.service.cloud.IZstackCloudService;
import cn.iocoder.zj.module.om.service.hardware.IWinHongHardwareService;
import cn.iocoder.zj.module.om.service.hardware.IZstackHardwareService;
import cn.iocoder.zj.module.om.util.StringUtil;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.singleton;

@Service
@Validated
@Component
@Slf4j
public class AssetManagementServiceImpl implements AssetManagementService {
    @Resource
    AssetManagementMapper assetManagementMapper;
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    private PermissionApi permissionApi;
    @Resource
    private RoleApi roleApi;
    @Resource
    IZstackCloudService iZstackCloudService;
    @Resource
    IVmwareService iVmwareService;
    @Resource
    IZstackHardwareService iZstackHardwareService;
    @Resource
    ISangForCloudService iSangForCloudService;
    @Resource
    IWinHongCloudService iWinHongCloudService;
    @Resource
    IWinHongHardwareService iWinHongHardwareService;
    @Resource
    HostInfoApi hostInfoApi;
    @Resource
    OnlineSessionApi onlineSessionApi;



    @Value("${proxy.url}")
    private String proxyUrl;

    @Value("${proxy.ip}")
    private String ip;
    @Override
    public List<Map<String,Object>> list(String tenantId) {
        //获取当前登录用户的平台配置信息
        List<Map<String,Object>> mapList = new ArrayList<>();
        List<Map> platformList = platformconfigApi.getPlatformSelectList(tenantId).getData();
        //每个平台都默认包含云主机、宿主机和存储资产
        List<Map<String,Object>> assetMap = getBaseAsset(platformList);
        for (Map platform:platformList) {
            List<Map<String,Object>> group = new ArrayList<>();
            Map<String,Object> resultMap = new HashMap<>();
            resultMap.put("label",platform.get("platformName"));
            resultMap.put("id",platform.get("platformId"));
            resultMap.put("platformId",platform.get("platformId"));
            int i = 1;
            for (Map<String,Object> item: assetMap) {
                if((platform.get("platformId").toString()).equals(item.get("platformId").toString())){
                    item.put("id",Double.parseDouble(platform.get("platformId").toString())+i*0.1);
                    group.add(item);
                    i++;
                }

            }
            resultMap.put("children",group);
            mapList.add(resultMap);
            }
        return mapList;
    }

    @Override
    public Map<String,Object> getAssetInfo(String platformId) {
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
        Calendar c = Calendar.getInstance();
        c.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        String lastWeek = sdf1.format(c.getTime());
        return assetManagementMapper.getAssetInfo(platformId,lastWeek);
    }

    @Override
    public Map<String,Object> hardwareInfo(String platformId) {
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
        Calendar c = Calendar.getInstance();
        c.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        String lastWeek = sdf1.format(c.getTime());
        Map<String,Object> assetInfo = assetManagementMapper.getGatherAssetInfo(platformId,lastWeek);
        Map<String,Object> hardwareInfo = assetManagementMapper.getHardwareInfo(platformId,lastWeek);
        hardwareInfo.putAll(assetInfo);
        return hardwareInfo;
    }

    @Override
    public PageResult<ResourceOperateLog> getOperateLogPage(OperateLogPageReqVO pageReqVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> loginUserRoleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        Set<Long> mixed = new HashSet<Long>(loginUserRoleIds);
        Set<Long> targetIds = roleApi.getRoleIdByCode("tenant_admin").getData();
        mixed.retainAll(targetIds);
        boolean hasPermission = mixed.size()>0;
        PageParam pageParam = new PageParam().setPageNo(pageReqVO.getPageNo()).setPageSize(pageReqVO.getPageSize());
        IPage<ResourceOperateLog> mpPage = MyBatisUtils.buildPage(pageParam);
        if (StringUtil.isNotEmpty(pageReqVO.getSortBy())){
            String regex = "([a-z])([A-Z]+)";
            String replacement = "$1_$2";
            String output = pageReqVO.getSortBy().replaceAll(regex, replacement).toLowerCase();
            pageReqVO.setSortBy(output);
        }
        return  new PageResult<>(assetManagementMapper.getOperateLogPage(mpPage,pageReqVO,loginUser,hasPermission),mpPage.getTotal());
    }

    @Override
    public void createOperateLog(String name, String uuid, String operation, String result, String resourceType, String msg, PlatformconfigDTO platform) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        ResourceOperateLog operateLog = new ResourceOperateLog();
        operateLog.setCreator(String.valueOf(loginUser.getId()));
        operateLog.setOperation(operation);
        operateLog.setResourceType(resourceType);
        operateLog.setResult(result);
        operateLog.setResourceUuid(uuid);
        operateLog.setResourceName(name);
        operateLog.setPlatformName(platform.getName());
        operateLog.setPlatformId(platform.getId());
        operateLog.setContent(msg);
        assetManagementMapper.createOperateLog(operateLog);
    }

    @Override
    public CommonResult<Map<String, String>> operateVmInstance(Long platformId, String vms, String uuid, String type, String actions, String hostName) {
        PlatformconfigDTO platformconfigDTO = platformconfigApi.getByConfigId(platformId).getData();
        CommonResult<Map<String,String>> result = new CommonResult<>();
        if(platformconfigDTO.getTypeCode().equals("zstack")){
            result =  iZstackCloudService.operateVmInstance(uuid, platformId, type, actions);
        } else if(platformconfigDTO.getTypeCode().equals("vmware")){
            result = iVmwareService.operateVmwareVm(uuid,vms,platformId, type, actions);
        }else if(platformconfigDTO.getTypeCode().equals("winhong")){
            result = iWinHongCloudService.operateVmInstance(uuid,platformId, type, actions);
        }else{
            result = iSangForCloudService.operateSangForVm(uuid,platformId, type, actions);
        }
        return result;
    }

    @Override
    public CommonResult<Map<String, String>> operateHardware(Long platformId, String vms, String uuid, String state, String actions, String hardwareName) {
        PlatformconfigDTO platformconfigDTO = platformconfigApi.getByConfigId(platformId).getData();
        CommonResult<Map<String,String>> result = new CommonResult<>();
        if(platformconfigDTO.getTypeCode().equals("zstack")) {
            result = iZstackHardwareService.operateHardware(uuid, platformId, actions, state);
        }else if(platformconfigDTO.getTypeCode().equals("vmware")){
            result = iVmwareService.operateVmwareHost(uuid,vms, platformId, actions,state,hardwareName);
        }else if(platformconfigDTO.getTypeCode().equals("winhong")){
            result = iWinHongHardwareService.operateWinHongHardware(uuid,platformId, state, actions);
        }else {
            result = iSangForCloudService.operateSangForHardware(uuid, platformId, actions,state,hardwareName);
        }
        return result;
    }

    @Override
    public JSONArray getVmMirror(Long platformId, String uuid) {
        return iSangForCloudService.getVmMirror(platformId,uuid);
    }

    @Override
    public VncInfoDTO getVncInfo(String uuid) {
        // 通过UUID获取主机信息
        HostInfoRespCreateReqDTO hostInfo = hostInfoApi.getByUuid(uuid).getData();
        String typeName = hostInfo.getTypeName();
        VncInfoDTO vncInfoDTO = new VncInfoDTO();
        if("Management".equals(hostInfo.getGuestOsType())){
            throw new IllegalArgumentException("该云主机暂不支持VNC连接");
        }
        VncInfoVo result;
        try {
            switch (typeName) {
                case "zstack":
                    result = iZstackCloudService.getWebSocketUrl(hostInfo);
                    break;
                case "vmware":
                    result = iVmwareService.getVncInfo(hostInfo);
                    break;
                case "winhong":
                    result = iWinHongCloudService.getVncInfo(hostInfo);
                    break;
                case "istack":
                    result = iZstackCloudService.getVncInfo(hostInfo);
                    break;
                default:
                    throw new IllegalArgumentException("不支持的云平台类型: " + typeName);
            }
        } catch (Exception e) {
            log.error("获取VNC信息失败: {}", e.getMessage());
            throw new IllegalArgumentException("获取VNC信息失败: " + e.getMessage());
        }
        vncInfoDTO.setCloudId(hostInfo.getId());
        vncInfoDTO.setPlatformId(hostInfo.getPlatformId());
        if(StringUtil.isNotEmpty(result.getVncToken())){
        vncInfoDTO.setVncToken(result.getVncToken());
        }else {
            vncInfoDTO.setVncToken("");
        }
        vncInfoDTO.setVncType(typeName);
        vncInfoDTO.setTcpHost(result.getWebsocketHost());
        vncInfoDTO.setCloudName(hostInfo.getName());
        vncInfoDTO.setPortMappingId(result.getWebsocketPort() != null ? result.getWebsocketPort().toString() : null);
        vncInfoDTO.setWebsocketPath(result.getWebsocketPath());
        return vncInfoDTO;
    }


    private List<Map<String, Object>> getBaseAsset(List<Map> platformList) {
        List<Map<String, Object>> baseAsset = new ArrayList<>();
        if(platformList.size()>0) {
            for (Map platform : platformList) {
                Map<String, Object> host = new HashMap<>();
                host.put("platformId", platform.get("platformId"));
                host.put("type", "host");
                host.put("label", "云主机");
                host.put("icon", "icon-bushu");
                baseAsset.add(host);

                Map<String, Object> hardware = new HashMap<>();
                hardware.put("platformId", platform.get("platformId"));
                hardware.put("type", "hardware");
                hardware.put("label", "宿主机");
                hardware.put("icon", "icon-wuliji");
                baseAsset.add(hardware);

                Map<String, Object> storage = new HashMap<>();
                storage.put("platformId", platform.get("platformId"));
                storage.put("type", "storage");
                storage.put("label", "存储");
                storage.put("icon", "icon-yingpan");
                baseAsset.add(storage);
            }
        }
        return baseAsset;
    }
}
