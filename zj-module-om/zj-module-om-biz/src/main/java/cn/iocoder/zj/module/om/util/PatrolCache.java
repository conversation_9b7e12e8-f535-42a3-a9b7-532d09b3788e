package cn.iocoder.zj.module.om.util;

import java.util.concurrent.ConcurrentHashMap;

public class PatrolCache {
    private static final ConcurrentHashMap<String, Boolean> isComplete = new ConcurrentHashMap<>(16 << 1);

    public static void setComplete(String key, Boolean value) {
        isComplete.put(key, value);
    }

    public static Boolean getComplete(String key) {
        return isComplete.get(key);
    }

    public static void removeComplete(String key) {
        isComplete.remove(key);
    }
}
