package cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo;

import cn.iocoder.zj.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

@Data
public class OperateLogPageReqVO extends PageParam {
    @Schema(description = "主键")
    private Long id;

    @Schema(description = "资源名称")
    private String resourceName;

    @Schema(description = "操作")
    private String operation;

    @Schema(description = "操作结果")
    private String result;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "资源类型")
    private String resourceType;

    @Schema(description = "资源uuid")
    private String resourceUuid;

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String startTime;

    @Schema(description = "结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String endTime;
}
