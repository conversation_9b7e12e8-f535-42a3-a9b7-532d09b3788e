package cn.iocoder.zj.module.om.service.patrolinspection;

import java.util.*;
import javax.validation.*;
import cn.iocoder.zj.module.om.controller.admin.inspectiondata.vo.*;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspection.InspectionLogDO;

/**
 * 巡检记录 Service 接口
 *
 * <AUTHOR>
 */
public interface InspectionLogService {

    /**
     * 创建巡检记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInspectionLog(@Valid InspectionLogCreateReqVO createReqVO);

    /**
     * 更新巡检记录
     *
     * @param updateReqVO 更新信息
     */
    void updateInspectionLog(@Valid InspectionLogUpdateReqVO updateReqVO);

    /**
     * 删除巡检记录
     *
     * @param id 编号
     */
    void deleteInspectionLog(Long id);

    /**
     * 获得巡检记录
     *
     * @param id 编号
     * @return 巡检记录
     */
    InspectionLogDO getInspectionLog(Long id);

    /**
     * 获得巡检记录列表
     *
     * @param ids 编号
     * @return 巡检记录列表
     */
    List<InspectionLogDO> getInspectionLogList(Collection<Long> ids);

    /**
     * 获得巡检记录分页
     *
     * @param pageReqVO 分页查询
     * @return 巡检记录分页
     */
    PageResult<InspectionLogRespVO> getInspectionLogPage(InspectionLogPageReqVO pageReqVO);

    /**
     * 获得巡检记录列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 巡检记录列表
     */
    List<InspectionLogDO> getInspectionLogList(InspectionLogExportReqVO exportReqVO);
    void createInspectionLogList(List<InspectionLogDO> inspectionLogDOList);
}
