package cn.iocoder.zj.module.om.controller.admin.userbind.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户推送绑定 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class UserBindExcelVO {

    @ExcelProperty("主键ID")
    private Long id;

    @ExcelProperty("用户id")
    private Long userId;

    @ExcelProperty("用户名称")
    private String userName;

    @ExcelProperty("企业微信推送启用状态，0未启用，1已启用")
    private Integer wxState;

    @ExcelProperty("邮箱推送启用状态，0未启用，1已启用")
    private Integer emailState;

    @ExcelProperty("钉钉推送启用状态，0未启用,1启用中")
    private Integer dingtalkState;

    @ExcelProperty("钉钉应用key")
    private String dingtalkAppKey;

    @ExcelProperty("钉钉应用秘钥")
    private String dingtalkAppSecret;

    @ExcelProperty("应用id")
    private String dingtalkAgentId;

    @ExcelProperty("钉钉手机号")
    private String dingtalkPhone;

    @ExcelProperty("企微应用id")
    private String wxAgentId;

    @ExcelProperty("企微企业id")
    private String wxCorpid;

    @ExcelProperty("企微企业秘钥")
    private String wxCorpsecret;

    @ExcelProperty("企微手机号")
    private String wxPhone;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
