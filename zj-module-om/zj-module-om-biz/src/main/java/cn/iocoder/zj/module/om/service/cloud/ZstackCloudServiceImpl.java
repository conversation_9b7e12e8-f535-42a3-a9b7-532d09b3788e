package cn.iocoder.zj.module.om.service.cloud;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.monitor.api.hostinfo.HostInfoApi;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo.ResourceOperateLog;
import cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo.ZstackCreateHostReqVo;
import cn.iocoder.zj.module.om.controller.admin.vncinfo.vo.VncInfoVo;
import cn.iocoder.zj.module.om.dal.dataobject.zstack.ZstackLoginInfo;
import cn.iocoder.zj.module.om.dal.mysql.assetmanagement.AssetManagementMapper;
import cn.iocoder.zj.module.om.dal.redis.platform.PlatformRedisDAO;
import cn.iocoder.zj.module.om.dal.redis.zstack.ZstackAccessTokenRedisDAO;
import cn.iocoder.zj.module.om.framework.websocket.WebSocketClient;
import cn.iocoder.zj.module.om.service.zstack.core.WinHongApiConstant;
import cn.iocoder.zj.module.om.service.zstack.core.ZstackApiConstant;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.UuidUtils;
import jodd.util.StringUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.singleton;
import static cn.iocoder.zj.module.om.enums.ErrorCodeConstants.HAVE_NO_ACCESS_TOKEN;

@Service
@Log4j2
public class ZstackCloudServiceImpl implements IZstackCloudService{
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    ZstackAccessTokenRedisDAO zstackAccessTokenRedisDAO;
    @Resource
    HostInfoApi hostInfoApi;
    @Resource
    private PermissionApi permissionApi;
    @Resource
    private RoleApi roleApi;

    @Resource
    AssetManagementMapper assetManagementMapper;

    @Resource
    WebSocketClient webSocketClient;

    @Resource
    PlatformRedisDAO platformRedisDAO;

    @Override
    public String createVmInstance(String url, String token) {
        HttpResponse result = HttpRequest.post(url + ZstackApiConstant.POST_ZSTACK_VM_INSTANCES)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .execute();
        if (result.getStatus() != 200) {
            throw new RuntimeException(String.valueOf(result.getStatus()));
        }
        return result.body();
    }

    @Override
    public Map<String,Object> getVmClusters(Long platformId, Integer pageNo, Integer pageSize,String queryData) {
        Map<String,Object> map = new HashMap<>();
        ZstackLoginInfo platformLogInfo = zstackAccessTokenRedisDAO.get("zstack:" + platformId);
        String token = "";
        if(ObjectUtil.isNotEmpty(platformLogInfo)) {
            token = platformLogInfo.getUuid();
        }else {
            throw exception(HAVE_NO_ACCESS_TOKEN);
        }
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(platformId).getData();
        String url = platform.getUrl() + ZstackApiConstant.GET_ZSTACK_CLUSTERS+"?q=type=zstack";
        if(pageNo!=null && pageSize != null) {
            Integer start = (pageNo-1)*pageSize;
            url = platform.getUrl() + ZstackApiConstant.GET_ZSTACK_CLUSTERS+"?limit="+pageSize+"&start="+start+"&replyWithCount=true&q=type=zstack";
        }
        if(StringUtil.isNotBlank(queryData)){
            url+="&q=name~=%25"+queryData+"%25";
        }
        HttpResponse result = HttpRequest.get(url)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .execute();
        if (result.getStatus() != 200) {
            throw new RuntimeException("getVmClusters error");
        }
        map.put("total",JSONObject.parseObject(result.body()).get("total"));
        map.put("inventories",JSONObject.parseObject(result.body()).get("inventories"));
        return map;
    }

    @Override
    public Map<String,Object> getDiskOfferings(Long platformId,Integer pageNo, Integer pageSize,String queryData,Long diskSize) {
        Map<String,Object> map = new HashMap<>();
        ZstackLoginInfo platformLogInfo = zstackAccessTokenRedisDAO.get("zstack:" + platformId);
        String token = "";
        if(ObjectUtil.isNotEmpty(platformLogInfo)) {
            token = platformLogInfo.getUuid();
        }else {
            throw exception(HAVE_NO_ACCESS_TOKEN);
        }
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(platformId).getData();
        Integer start = (pageNo-1)*pageSize;
        String url = platform.getUrl() + ZstackApiConstant.GET_ZSTACK_DISK_OFFERINGS+"?limit="+pageSize+"&start="+start+"&replyWithCount=true";
        if(diskSize!=null){
            url=url+"&q=diskSize%3E="+diskSize;
        }
        if(StringUtil.isNotBlank(queryData)){
            url+="&q=name~=%25"+queryData+"%25";
        }
        HttpResponse result = HttpRequest.get(url)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .execute();
        if (result.getStatus() != 200) {
            throw new RuntimeException("getDiskOfferings error");
        }
        map.put("total",JSONObject.parseObject(result.body()).get("total"));
        map.put("inventories",JSONObject.parseObject(result.body()).get("inventories"));
        return map;
    }
    @Override
    public Map<String,Object> getImages(Long platformId,Integer pageNo, Integer pageSize,String queryData) {
        Map<String,Object> map = new HashMap<>();
        ZstackLoginInfo platformLogInfo = zstackAccessTokenRedisDAO.get("zstack:" + platformId);
        String token = "";
        if(ObjectUtil.isNotEmpty(platformLogInfo)) {
            token = platformLogInfo.getUuid();
        }else {
            throw exception(HAVE_NO_ACCESS_TOKEN);
        }
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(platformId).getData();
        Integer start = (pageNo-1)*pageSize;
        String url = platform.getUrl() + ZstackApiConstant.GET_ZSTACK_IMAGES+"?limit="+pageSize+"&start="+start+"&replyWithCount=true&q=type=zstack";
        if(StringUtil.isNotBlank(queryData)){
            url+="&q=name~=%25"+queryData+"%25";
        }
        HttpResponse result = HttpRequest.get(url)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .execute();
        if (result.getStatus() != 200) {
            throw new RuntimeException("getImages error");
        }
        map.put("total",JSONObject.parseObject(result.body()).get("total"));
        map.put("inventories",JSONObject.parseObject(result.body()).get("inventories"));
        return map;
    }

    @Override
    public Map<String,Object> getInstanceOfferings(Long platformId, Integer pageNo, Integer pageSize,String queryData) {
        Map<String,Object> map = new HashMap<>();
        ZstackLoginInfo platformLogInfo = zstackAccessTokenRedisDAO.get("zstack:" + platformId);
        String token = "";
        if(ObjectUtil.isNotEmpty(platformLogInfo)) {
            token = platformLogInfo.getUuid();
        }else {
            throw exception(HAVE_NO_ACCESS_TOKEN);
        }
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(platformId).getData();
        Integer start = (pageNo-1)*pageSize;
        String url = platform.getUrl() + ZstackApiConstant.GET_ZSTACK_INSTANCE_OFFERINGS+"?limit="+pageSize+"&start="+start+"&replyWithCount=true";
        if(StringUtil.isNotBlank(queryData)){
            url+="&q=name~=%25"+queryData+"%25";
        }
        HttpResponse result = HttpRequest.get(url)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .execute();
        if (result.getStatus() != 200) {
            throw new RuntimeException("getVmInstances error");
        }
        map.put("total",JSONObject.parseObject(result.body()).get("total"));
        map.put("inventories",JSONObject.parseObject(result.body()).get("inventories"));
        return map;
    }
    @Override
    public CommonResult<Map<String,String>> createHostToZstack(ZstackCreateHostReqVo reqVo) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        String token = zstackAccessTokenRedisDAO.get("zstack:" + reqVo.getPlatformId()).getUuid();
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(reqVo.getPlatformId()).getData();
        JSONObject body = new JSONObject();
        JSONObject params = new JSONObject();
        params.put("name",reqVo.getName());
        params.put("instanceOfferingUuid",reqVo.getInstanceOfferingUuid());
        params.put("imageUuid",reqVo.getImageUuid());
        if(StringUtil.isNotBlank(reqVo.getRootDiskOfferingUuid())) {
            params.put("rootDiskOfferingUuid", reqVo.getRootDiskOfferingUuid());
        }
        if(StringUtil.isNotBlank(reqVo.getL3NetworkUuids())) {
            params.put("l3NetworkUuids", Arrays.asList(reqVo.getL3NetworkUuids().split(",")));
        }
        if(StringUtil.isNotBlank(reqVo.getDataDiskOfferingUuids())) {
            params.put("dataDiskOfferingUuids", Arrays.asList(reqVo.getDataDiskOfferingUuids().split(",")));
        }
        params.put("clusterUuid",reqVo.getClusterUuid());
        body.put("params",params);
        HostInfoRespCreateReqDTO reqDTO = new HostInfoRespCreateReqDTO();
        HttpResponse resp = HttpRequest.post(platform.getUrl() + ZstackApiConstant.POST_ZSTACK_CREATE_VM)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .body(JSONObject.toJSONString(body))
                .timeout(900000)
                .execute();
        //待创建的云主机uuid无法获取，使用临时id
        reqDTO.setUuid(UuidUtils.generateUuid());
        reqDTO.setName(reqVo.getName());
        reqDTO.setPlatformId(platform.getId());
        reqDTO.setPlatformName(platform.getName());
        reqDTO.setTypeName(platform.getTypeCode());
        reqDTO.setState("Creating");
        reqDTO.setMemoryUsed(new BigDecimal(0));
        reqDTO.setDiskUsed(new BigDecimal(0));
        reqDTO.setCpuUsed(new BigDecimal(0));
        hostInfoApi.createHostSingle(reqDTO);
        Runnable data = (() -> {
            updateHostInfo(resp.body(),"create",reqDTO.getUuid(),platform,"",reqVo.getName(),loginUser);
        });
        Thread thread = new Thread(data);
        thread.start();
        Map<String,String> reuslt = new HashMap<>();
        reuslt.put("success","true");
        reuslt.put("uuid",reqDTO.getUuid());
        reuslt.put("msg","正在创建云主机("+reqVo.getName()+")");
        return CommonResult.success(reuslt);
//        return updateHostInfo(resp.body(),"createVm","",platform,"",reqVo.getName());
    }

    @Override
    public CommonResult<Map<String, String>> operateVmInstance(String uuid, Long platformId, String type, String actions) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        HostInfoRespCreateReqDTO hostDTO = hostInfoApi.getByUuid(uuid).getData();
        String token = zstackAccessTokenRedisDAO.get("zstack:" + platformId).getUuid();
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(platformId).getData();
        String url = platform.getUrl() + ZstackApiConstant.PUT_VM_ACTIONS+"/"+uuid+"/actions";
        String op = ZstackHostOperateConvert(actions);
        JSONObject body = new JSONObject();
        JSONObject actionType = new JSONObject();
        String state;
        Map<String,String> reuslt = new HashMap<>();
        reuslt.put("success","true");
        reuslt.put("msg","正在"+op+"("+hostDTO.getName()+")");
        if(actions.equals("stop")) {
            //停止云主机
            state = "Stopped";
            actionType.put("type", type);
            body.put("stopVmInstance", actionType);
            hostDTO.setState("Stopping");
        } else if (actions.equals("start")) {
            //启动云主机
            state = "Running";
            body.put("startVmInstance",actionType);
            hostDTO.setState("Starting");
        } else if (actions.equals("reboot")) {
            //重启云主机
            state = "Running";
            body.put("rebootVmInstance",actionType);
            hostDTO.setState("Rebooting");
        } else if (actions.equals("destroy")) {
            //删除云主机
            url = platform.getUrl() + ZstackApiConstant.PUT_VM_ACTIONS+"/"+uuid+"?deleteMode=Permissive";
            HttpResponse resp = HttpRequest.delete(url)
                    .header(Header.AUTHORIZATION, "OAuth " + token)
                    .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                    .timeout(3000000)
                    .execute();
            // 同步删除拓扑图中的信息
            hostInfoApi.deleteAssetAndHostJson(uuid);
            hostDTO.setState("Destroying");
            //新启一个线程用户查询任务的执行情况并更新信息
            Runnable data = (() -> {
                updateHostInfo(resp.body(),"destroy",uuid,platform,"",hostDTO.getName(),loginUser);
            });
            Thread thread = new Thread(data);
            thread.start();
            hostInfoApi.updateHostSingle(hostDTO);
            return CommonResult.success(reuslt);
        }else {
            state = "";
        }
        HttpResponse resp = HttpRequest.put(url)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .body(JSONObject.toJSONString(body))
                .execute();
        //新启一个线程用户查询任务的执行情况并更新信息
        Runnable data = (() -> {
            updateHostInfo(resp.body(),actions,uuid,platform,state,hostDTO.getName(),loginUser);
        });
        Thread thread = new Thread(data);
        thread.start();
        hostInfoApi.updateHostSingle(hostDTO);
        return CommonResult.success(reuslt);
    }
    public void updateHostInfo(String respBody,String operation,String hostUuid,PlatformconfigDTO platform,String state,String name,LoginUser loginUser){
        JSONObject reuslt = new JSONObject();
        HostInfoRespCreateReqDTO reqDTO = new HostInfoRespCreateReqDTO();
        String resultUrl = JSONObject.parseObject(respBody).get("location").toString();
        String body = HttpRequest.get(resultUrl).timeout(3000000).execute().body();
        String op = ZstackHostOperateConvert(operation);

        //为空是因为操作还在执行中
        if(!operation.equals("destroy")) {
            while (body.equals("{}")) {
                body = HttpRequest.get(resultUrl).execute().body();
            }
        }
        if(JSONObject.parseObject(body).get("error")==null){
            JSONObject obj = JSONObject.parseObject(JSONObject.toJSONString(JSONObject.parseObject(body).get("inventory")));
            if(!operation.equals("createVm")){
                reqDTO.setUuid(hostUuid);
                reqDTO.setState(state);
                hostInfoApi.updateHostSingle(reqDTO);
            } else {
                reqDTO.setUuid(obj.getString("uuid"));
                reqDTO.setName(obj.getString("name"));
                reqDTO.setPlatformId(platform.getId());
                reqDTO.setPlatformName(platform.getName());
                reqDTO.setTypeName(platform.getTypeCode());
                reqDTO.setState(obj.getString("state"));
                reqDTO.setArchitecture(obj.getString("architecture"));
                reqDTO.setGuestOsType(obj.getString("guestOsType"));
                JSONArray vmNics = obj.getJSONArray("vmNics");
                String ip = "";
                String mac = "";
                if (vmNics.size() > 0) {
                    List<String> ipList = CollUtil.getFieldValues(vmNics, "ip", String.class);
                    List<String> macList = CollUtil.getFieldValues(vmNics, "mac", String.class);
                    // 拼接IP地址
                    ip = CollUtil.join(ipList, ",");
                    mac = CollUtil.join(macList, ",");
                    if (ip.equals("null")) {
                        ip = "";
                    }
                }
                reqDTO.setIp(ip);
                reqDTO.setMac(mac);
                reqDTO.setMemoryUsed(new BigDecimal(0));
                reqDTO.setDiskUsed(new BigDecimal(0));
                reqDTO.setCpuUsed(new BigDecimal(0));
                hostInfoApi.createHostSingle(reqDTO);
            }
            reuslt.put("uuid",hostUuid);
            reuslt.put("msg",op+"("+name+")成功");
            reuslt.put("success","true");
            createOperateLog(name,reqDTO.getUuid(),op,"success", "host", "", platform,loginUser);
            webSocketClient.sendMessage(hostUuid,reuslt.toJSONString());
        }else {
            JSONObject obj = JSONObject.parseObject(JSONObject.toJSONString(JSONObject.parseObject(body).get("error")));
            String msg = getErrorMsg(obj);
            reuslt.put("uuid",hostUuid);
            reuslt.put("msg",op+"("+name+")失败,错误信息:"+msg+",请至"+platform.getName()+"查看详情");
            reuslt.put("success","false");
            createOperateLog(name,reqDTO.getUuid(),op,"error", "host", msg, platform,loginUser);
            webSocketClient.sendMessage(hostUuid,reuslt.toJSONString());
        }
    }
    @Override
    public Boolean getPermission(LoginUser loginUser){
        boolean hasPermission = false;
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        Set<Long> targetRole = roleApi.getRoleIdByCode("om_manager").getData();
        Set<Long> result = new HashSet<Long>();
        result.clear();
        result.addAll(roleIds);
        result.retainAll(targetRole);
        if(result.size()>0){
            hasPermission = true;
        }
        return hasPermission;
    }
    public String getErrorMsg(JSONObject obj){
        String msg = "操作失败！";
        if(jodd.util.StringUtil.isNotBlank(obj.getString("elaboration"))){
            msg = obj.getString("elaboration");
        } else if (jodd.util.StringUtil.isNotBlank(obj.getString("details"))) {
            msg = obj.getString("details");
        } else if (jodd.util.StringUtil.isNotBlank(obj.getString("description"))) {
            msg = obj.getString("description");
        }
        return msg;
    }

    @Override
    public String ZstackHostOperateConvert(String operation){
        String op = "";
        switch (operation){
            case "create":
                op = "创建虚拟机"; break;
            case "stop":
                op = "停止虚拟机"; break;
            case "start":
                op = "启动虚拟机"; break;
            case "reboot":
                op = "重启虚拟机"; break;
            case "destroy":
                op = "回收虚拟机"; break;
        }
        return op;
    }

    @Override
    public VncInfoVo getWebSocketUrl(HostInfoRespCreateReqDTO hostInfo) {
        // 1. 获取必要参数
        String token = zstackAccessTokenRedisDAO.get("zstack:" + hostInfo.getPlatformId()).getUuid();
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(hostInfo.getPlatformId()).getData();
        
        // 2. 构建请求参数
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("params", Collections.singletonMap("vmInstanceUuid", hostInfo.getUuid()));
        
        // 3. 发送控制台请求
        String resultUrl = sendConsoleRequest(platform.getUrl(), token, requestBody);
        
        // 4. 获取控制台信息
        JSONObject inventory = getConsoleInventory(resultUrl);
        if (inventory == null) {
            throw new IllegalArgumentException("无法获取控制台信息");
        }
        
        // 5. 构建返回结果
        VncInfoVo vncInfoVo = new VncInfoVo();
        vncInfoVo.setPlatformId(hostInfo.getPlatformId());
        return buildVncInfoVo(inventory,vncInfoVo);
    }

    private String sendConsoleRequest(String baseUrl, String token, Map<String, Object> requestBody) {
        HttpResponse resp = HttpRequest.put(baseUrl + ZstackApiConstant.GET_CONSOLES)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .body(JSONObject.toJSONString(requestBody))
                .execute();
        return JSONObject.parseObject(resp.body()).getString("location");
    }

    private JSONObject getConsoleInventory(String resultUrl) {
        // 初始等待
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("等待控制台信息时被中断", e);
        }

        // 重试逻辑
        for (int retryCount = 0; retryCount < 3; retryCount++) {
            try {
                String responseBody = HttpRequest.get(resultUrl)
                        .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                        .execute()
                        .body();
                JSONObject inventory = JSONObject.parseObject(responseBody).getJSONObject("inventory");
                if (inventory != null) {
                    return inventory;
                }
            } catch (Exception e) {
                log.warn("获取控制台信息失败，正在重试... (第{}次)", retryCount + 1, e);
            }
            
            if (retryCount < 2) { // 最后一次重试不需要等待
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("重试等待时被中断", e);
                }
            }
        }
        return null;
    }

    private VncInfoVo buildVncInfoVo(JSONObject inventory,VncInfoVo vncInfoVo) {
        vncInfoVo.setWebsocketHost(inventory.getString("hostname"));
        vncInfoVo.setWebsocketPort(inventory.getInteger("port"));
        vncInfoVo.setWebsocketPath("/websockify?token=" + inventory.getString("token"));
        vncInfoVo.setUseSsl(false);
        
        // 构建完整的 WebSocket URL
        String wsUrl = String.format("ws://%s:%d%s", 
            vncInfoVo.getWebsocketHost(),
            vncInfoVo.getWebsocketPort(),
            vncInfoVo.getWebsocketPath());
        vncInfoVo.setWebsocketPath(wsUrl);
        
        return vncInfoVo;
    }

    @Override
    public VncInfoVo getVncInfo(HostInfoRespCreateReqDTO hostInfo) {
        PlatformconfigDTO platformDTO = platformRedisDAO.get("platform").stream()
                .filter(dto -> "istack".equals(dto.getTypeCode())
                        && Objects.equals(hostInfo.getPlatformId(), dto.getId()))
                .findFirst()
                .orElse(null);

        PlatformconfigDTO platform = platformconfigApi.getByConfigId(hostInfo.getPlatformId()).getData();
        String url = platformDTO.getUrl() + ZstackApiConstant.GET_VNC_CONSOLES;
        Map<String, Object> param = new HashMap<>();
        param.put("os_id",platform.getPassword());
        param.put("ct_user_id",platform.getUsername());
        param.put("action","vnc_console");
        param.put("instance_uuid",Arrays.asList(hostInfo.getUuid()));
        HttpResponse res = HttpRequest.post(url).body(JSONObject.toJSONString(param)).execute();
        VncInfoVo vncInfoVo = new VncInfoVo();
        JSONObject jsonObject = JSONObject.parseObject(res.body());
        if(jsonObject.getInteger("code") == 200){
            vncInfoVo.setVncConsoleUrl(jsonObject.getJSONArray("results").getString(0));
            vncInfoVo.setVncToken(vncInfoVo.getVncConsoleUrl().substring(vncInfoVo.getVncConsoleUrl().indexOf("token=")));
        }
        vncInfoVo.setWebsocketPath(convertUrl(platform.getConsoleIp() + ":" +platform.getConsoleProd())+ "/websockify");
        vncInfoVo.setUseSsl(true);
        return vncInfoVo;
    }

    public static String convertUrl(String url) {
        return url.replace("https://", "ws://");
    }

    public void createOperateLog(String name, String uuid, String operation, String result, String resourceType, String msg, PlatformconfigDTO platform,LoginUser loginUser) {
        ResourceOperateLog operateLog = new ResourceOperateLog();
        operateLog.setCreator(String.valueOf(loginUser.getId()));
        operateLog.setOperation(operation);
        operateLog.setResourceType(resourceType);
        operateLog.setResult(result);
        operateLog.setResourceUuid(uuid);
        operateLog.setResourceName(name);
        operateLog.setPlatformName(platform.getName());
        operateLog.setPlatformId(platform.getId());
        operateLog.setContent(msg);
        assetManagementMapper.createOperateLog(operateLog);
    }
}

