package cn.iocoder.zj.module.om.Inspection.metric;

import cn.iocoder.zj.module.om.enums.PatrolMetricEnum;

/**
 * 抽象巡检指标类
 * 实现巡检指标接口的基本功能
 *
 * <AUTHOR>
 */
public abstract class AbstractPatrolMetric implements PatrolMetric {

    /**
     * 指标类型
     */
    private final PatrolMetricEnum metricType;

    /**
     * 构造函数
     *
     * @param metricType 指标类型
     */
    public AbstractPatrolMetric(PatrolMetricEnum metricType) {
        this.metricType = metricType;
    }

    @Override
    public PatrolMetricEnum getMetricType() {
        return metricType;
    }

    /**
     * 转换持续时间单位为中文
     */
    protected String convertDurationUnitToChinese(String durationUnit) {
        return switch (durationUnit) {
            case "d" -> "天";
            case "h" -> "小时";
            case "m" -> "分钟";
            case "s" -> "秒";
            default -> durationUnit;
        };
    }

    /**
     * 格式化消息，替换占位符
     */
    protected String formatMessage(String message, Object... args) {
        if (message == null) {
            return "";
        }

        String result = message;
        for (int i = 0; i < args.length; i++) {
            result = result.replace("{" + i + "}", String.valueOf(args[i]));
        }
        return result;
    }
}