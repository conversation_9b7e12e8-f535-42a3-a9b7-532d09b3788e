package cn.iocoder.zj.module.om.controller.admin.schedulinginfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

/**
* 运维排班信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SchedulingInfoBaseVO {

    @Schema(description = "实施人id")
    private String enforcerId;

    @Schema(description = "实施人名称")
    private String enforcerName;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;

    @Schema(description = "备注")
    private String remarks;

    @Schema(description = "租户id")
    private Long tenantId;

}
