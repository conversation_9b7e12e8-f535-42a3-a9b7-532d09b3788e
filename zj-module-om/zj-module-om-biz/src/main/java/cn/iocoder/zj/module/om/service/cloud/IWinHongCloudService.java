package cn.iocoder.zj.module.om.service.cloud;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo.WinHongCreateHostReqVo;
import cn.iocoder.zj.module.om.controller.admin.vncinfo.vo.VncInfoVo;

import java.util.Map;

public interface IWinHongCloudService {
    CommonResult<Map<String, String>> operateVmInstance(String uuid, Long platformId, String type, String actions);

    Map<String, String> createWinHongVm(WinHongCreateHostReqVo reqVo);

    VncInfoVo getVncInfo(HostInfoRespCreateReqDTO hostInfo);
}
