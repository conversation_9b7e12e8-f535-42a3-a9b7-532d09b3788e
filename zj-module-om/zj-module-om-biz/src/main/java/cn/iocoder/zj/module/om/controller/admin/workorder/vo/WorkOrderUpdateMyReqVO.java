package cn.iocoder.zj.module.om.controller.admin.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.Date;

@Schema(description = "管理后台 - 工单管理数据更新 Request VO")
@Data
@ToString(callSuper = true)
public class WorkOrderUpdateMyReqVO {

    @Schema(description = "主键ID", required = true)
    @NotNull(message = "主键ID不能为空")
    private Long id;

    @Schema(description = "名称")
    @Size(max = 100, message = "工单名称长度为100个字符")
    private String name;

    @Schema(description = "平台名称）")
    private String platformName;

    @Schema(description = "平台ID")
    private Long platformId;

    @Schema(description = "项目名称（平台资源）")
    private String sourceName;

    @Schema(description = "资源uuid")
    private String sourceUuid;

    @Schema(description = "事件说明")
    private String description;

    @Schema(description = "实施人信息")
    private String[] enforcers;

    @Schema(description = "资源类型对应的字典编码")
    private String dictCode;

    @Schema(description = "资源类型对应的字典名称")
    private String dictName;

    @Schema(description = "工单类型对应的字典编码")
    private String typeCode;

    @Schema(description = "工单类型对应的字典名称")
    private String typeName;


    @Schema(description = "开始实施时间")
    private Date enforcerStartTime;

    @Schema(description = "结束实施时间")
    private Date enforcerEndTime;

    @Schema(description = "实施时间端")
    private Date[] enforcerTime;
}
