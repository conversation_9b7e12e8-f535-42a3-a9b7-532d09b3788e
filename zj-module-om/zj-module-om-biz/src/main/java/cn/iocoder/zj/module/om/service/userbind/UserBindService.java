package cn.iocoder.zj.module.om.service.userbind;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.om.controller.admin.userbind.vo.UserBindCreateReqVO;
import cn.iocoder.zj.module.om.controller.admin.userbind.vo.UserBindExportReqVO;
import cn.iocoder.zj.module.om.controller.admin.userbind.vo.UserBindPageReqVO;
import cn.iocoder.zj.module.om.controller.admin.userbind.vo.UserBindUpdateReqVO;
import cn.iocoder.zj.module.om.dal.dataobject.userbind.UserBindDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 用户推送绑定 Service 接口
 *
 * <AUTHOR>
 */
public interface UserBindService {

    /**
     * 创建用户推送绑定
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createUserBind(@Valid UserBindCreateReqVO createReqVO);

    /**
     * 更新用户推送绑定
     *
     * @param updateReqVO 更新信息
     */
    void updateUserBind(@Valid UserBindUpdateReqVO updateReqVO);

    /**
     * 删除用户推送绑定
     *
     * @param id 编号
     */
    void deleteUserBind(Long id);

    /**
     * 获得用户推送绑定
     *
     * @param id 编号
     * @return 用户推送绑定
     */
    UserBindDO getUserBind(Long id);

    /**
     * 获得用户推送绑定列表
     *
     * @param ids 编号
     * @return 用户推送绑定列表
     */
    List<UserBindDO> getUserBindList(Collection<Long> ids);

    /**
     * 获得用户推送绑定分页
     *
     * @param pageReqVO 分页查询
     * @return 用户推送绑定分页
     */
    PageResult<UserBindDO> getUserBindPage(UserBindPageReqVO pageReqVO);

    /**
     * 获得用户推送绑定列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 用户推送绑定列表
     */
    List<UserBindDO> getUserBindList(UserBindExportReqVO exportReqVO);

    UserBindDO getByUserId(Long userId);

    List<UserBindDO> getUserBingList(List<Long> userIds);
}
