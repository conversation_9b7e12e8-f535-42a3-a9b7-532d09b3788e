package cn.iocoder.zj.module.om.controller.admin.monitorasset.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 监控资产分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MonitorAssetPageReqVO extends PageParam {

    @Schema(description = "资产id")
    private String assetId;

    @Schema(description = "资产名称")
    private String assetName;

    @Schema(description = "协议类型")
    private String protocol;

    @Schema(description = "协议端口")
    private Integer protocolProd;

    @Schema(description = "账户类型")
    private String certificate;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "资产类型")
    private Integer assetType;

    @Schema(description = "授权状态")
    private String authorizationType;

    @Schema(description = "授权有效时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] authorizationTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "用户名称")
    private String username;

    @Schema(description = "账号密码")
    private String password;

    @Schema(description = "主机ip")
    private String hostname;

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String startTime;

    @Schema(description = "结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String endTime;

}
