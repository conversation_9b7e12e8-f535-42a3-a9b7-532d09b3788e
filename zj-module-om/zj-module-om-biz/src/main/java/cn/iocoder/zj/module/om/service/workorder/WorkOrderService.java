package cn.iocoder.zj.module.om.service.workorder;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.module.om.controller.admin.workorder.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.workorder.WorkOrderDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 工单管理数据 Service 接口
 *
 * <AUTHOR>
 */
public interface WorkOrderService {

    /**
     * 创建工单管理数据
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createWorkOrder(@Valid WorkOrderCreateReqVO createReqVO);
    /**
     * 更新工单管理数据
     *
     * @param updateReqVO 更新信息
     */
    void updateWorkOrder(@Valid WorkOrderUpdateMyReqVO updateReqVO);

    /**
     * 删除工单管理数据
     *
     * @param id 编号
     */
    void deleteWorkOrder(Long id);

    /**
     * 获得工单管理数据
     *
     * @param id 编号
     * @return 工单管理数据
     */
    WorkOrderDO getWorkOrder(Long id);

    List<OrderProcessBaseVo> getOrderProcessList(Long workOrderId);
    /**
     * 获得工单管理数据列表
     *
     * @param ids 编号
     * @return 工单管理数据列表
     */
    List<WorkOrderDO> getWorkOrderList(Collection<Long> ids);
    PageResult<WorkOrderDO> getWorkOrderMyPage(WorkOrderPageReqVO pageReqVO);
    /**
     * 获得工单管理数据列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 工单管理数据列表
     */
    List<WorkOrderDO> getWorkOrderList(WorkOrderExportReqVO exportReqVO);

    Map<String, Object> getWorkOrderStatistics();
    List<Map<String, Object>>  getWorkOrderStatisticsInWeek();
    void ratifyWorkOrder(OrderRatifyReqVO reqVo);

    PageResult<WorkOrderRespVO> listOnDay(String day,WorkOrderPageReqVO pageVO);

    void finishWorkOrder(Long id);

    Integer getWorkerOrderCountBySourceUuid(String uuid,String type,String actions);

    List<Map<String, Object>> getRanking();

    Map<String, String> getPersonWorkOrderInfo(Long userId);

    List<Map<String, String>> getWorkOrderSourceType();

    List<Map<String, String>> getCreateInWeek();

    String getLatestRejectReason(Long id);

    OrderProcessBaseVo getOrderProcessRatify(Long workOrderId);

    OrderProcessBaseVo getOrderProcessSolved(Long workOrderId);

    Long getWorkOrderCount(Long id, String name);

    WorkOrderDO getWorkOrderByContractId(String contractId);

    List<WorkOrderRespVO> getUnsolvedWorkOrder();

    void updateIsRead(Collection<Long> ids);
}
