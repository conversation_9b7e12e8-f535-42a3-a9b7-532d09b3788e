package cn.iocoder.zj.module.om.Inspection.metric.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.zj.module.om.dal.dataobject.patrolabnormaldetail.PatrolAbnormalDetailDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspectionconfig.PatrolInspectionConfigDO;
import cn.iocoder.zj.module.om.enums.PatrolMetricEnum;
import cn.iocoder.zj.module.om.enums.PatrolResourceTypeEnum;

import java.util.List;
import java.util.function.Function;
import java.util.function.Predicate;

/**
 * 状态类指标处理器
 * 用于处理资源状态类指标
 */
public class StateMetricHandler<T> extends MetricHandler<T> {

    private final PatrolResourceTypeEnum resourceType;
    private final Class<T> resourceClass;
    private final Predicate<T> validResourceFilter;
    private final Predicate<T> abnormalChecker;
    private final Function<T, String> resourceIdGetter;
    private final Function<T, String> resourceNameGetter;
    private final Function<T, String> platformIdGetter;
    private final Function<T, String> platformNameGetter;
    private final Function<T, String> stateDescriptionGetter;
    private final Function<T, String> riskLevelGetter;

    public StateMetricHandler(PatrolMetricEnum metricType, PatrolResourceTypeEnum resourceType,
                              Class<T> resourceClass, Predicate<T> validResourceFilter,
                              Predicate<T> abnormalChecker, Function<T, String> resourceIdGetter,
                              Function<T, String> resourceNameGetter, Function<T, String> platformIdGetter,
                              Function<T, String> platformNameGetter, Function<T, String> stateDescriptionGetter,
                              Function<T, String> riskLevelGetter) {
        super(metricType);
        this.resourceType = resourceType;
        this.resourceClass = resourceClass;
        this.validResourceFilter = validResourceFilter;
        this.abnormalChecker = abnormalChecker;
        this.resourceIdGetter = resourceIdGetter;
        this.resourceNameGetter = resourceNameGetter;
        this.platformIdGetter = platformIdGetter;
        this.platformNameGetter = platformNameGetter;
        this.stateDescriptionGetter = stateDescriptionGetter;
        this.riskLevelGetter = riskLevelGetter;
    }

    @Override
    public PatrolResourceTypeEnum getResourceType() {
        return resourceType;
    }

    @Override
    protected List<T> filterValidResources(List<?> resourceDataList) {
        List<T> resources = BeanUtil.copyToList(resourceDataList, resourceClass);
        if (validResourceFilter != null) {
            return resources.stream().filter(validResourceFilter).toList();
        }
        return resources;
    }

    @Override
    protected boolean checkResourceAbnormal(T resource, List<PatrolInspectionConfigDO> configList) {
        return abnormalChecker.test(resource);
    }

    @Override
    protected PatrolAbnormalDetailDO createAbnormalDetail(T resource, List<PatrolInspectionConfigDO> configList, Long tenantId) {
        String stateDescription = stateDescriptionGetter != null ? stateDescriptionGetter.apply(resource) : "异常";
        String riskLevel = riskLevelGetter.apply(resource);
        return PatrolAbnormalDetailDO.builder()
                .detail(getResourceType().getName() + resourceNameGetter.apply(resource)
                        + getMetricType().getName() + stateDescription)
                .resourceType(getResourceType().getCode())
                .metricName(getMetricType().getCode())
                .resourceId(resourceIdGetter.apply(resource))
                .resourceName(resourceNameGetter.apply(resource))
                .platformId(Long.valueOf(platformIdGetter.apply(resource)))
                .platformName(platformNameGetter.apply(resource))
                .suggest(getMetricType().getSuggest().get(riskLevel))
                .riskLevel(riskLevel)
                .tenantId(tenantId)
                .build();
    }
}