package cn.iocoder.zj.module.om.dal.redis;


/**
 * System Redis Key 枚举类
 *
 * <AUTHOR>
 */
public interface RedisKeyConstants {

    String ZSTACK_ACCESS_TOKEN = "zstack_access_token:%s";

    String PLATFORM_LIST = "platfrom_list:%s";

    String VMWARE_ACCESS_TOKEN = "vmware_access_token:%s";

    String AUTHORIZAT = "authorizat_key:%s";

    String AUTH_KEY = "auth_key:%s";

    String APP_KEY_SECRET = "app_key_secret:app";
    String WINHONG_ACCESS_TOKEN = "winhong_access_token:%s";
}
