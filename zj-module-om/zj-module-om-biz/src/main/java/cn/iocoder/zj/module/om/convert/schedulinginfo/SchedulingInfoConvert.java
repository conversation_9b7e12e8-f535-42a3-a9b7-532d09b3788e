package cn.iocoder.zj.module.om.convert.schedulinginfo;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.om.controller.admin.schedulinginfo.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.schedulinginfo.SchedulingInfoDO;

/**
 * 运维排班信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface SchedulingInfoConvert {

    SchedulingInfoConvert INSTANCE = Mappers.getMapper(SchedulingInfoConvert.class);

    SchedulingInfoDO convert(SchedulingInfoCreateReqVO bean);

    SchedulingInfoDO convert(SchedulingInfoUpdateReqVO bean);

    SchedulingInfoRespVO convert(SchedulingInfoDO bean);

    List<SchedulingInfoRespVO> convertList(List<SchedulingInfoDO> list);

    PageResult<SchedulingInfoRespVO> convertPage(PageResult<SchedulingInfoDO> page);

    List<SchedulingInfoExcelVO> convertList02(List<SchedulingInfoDO> list);

}
