package cn.iocoder.zj.module.om.util.ws;

import com.vmware.vim25.AboutInfo;
import com.vmware.vim25.VirtualMachinePowerState;
import com.vmware.vim25.VirtualMachineTicket;
import com.vmware.vim25.mo.Folder;
import com.vmware.vim25.mo.InventoryNavigator;
import com.vmware.vim25.mo.ServiceInstance;
import com.vmware.vim25.mo.VirtualMachine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.net.URL;
import java.security.cert.X509Certificate;

public class VmwareWebConsoleClient {
    private static final Logger log = LoggerFactory.getLogger(VmwareWebConsoleClient.class);

    private ServiceInstance serviceInstance;
    private final String vcenterUrl;
    private final String username;
    private final String password;
    private final String vmName;

    public VmwareWebConsoleClient(String vcenterUrl, String username, String password, String vmName) {
        this.vcenterUrl = vcenterUrl;
        this.username = username;
        this.password = password;
        this.vmName = vmName;
        disableSSLVerification();
    }

    /**
     * 获取WebMKS控制台信息
     */
    public WebMKSTicketDO getWebMKSTicket() throws Exception {
        try {
            connect();
            Folder rootFolder = serviceInstance.getRootFolder();
            VirtualMachine vm = (VirtualMachine) new InventoryNavigator(rootFolder)
                    .searchManagedEntity("VirtualMachine", vmName);
            if (vm == null) {
                throw new Exception("未找到虚拟机: " + vmName);
            }
            // 检查虚拟机状态
            if (vm.getRuntime().getPowerState() != VirtualMachinePowerState.poweredOn) {
                throw new Exception("虚拟机未开机，当前状态: " + vm.getRuntime().getPowerState());
            }
            VirtualMachineTicket ticket = vm.acquireTicket("webmks");
            if (ticket == null) {
                throw new Exception("获取WebMKS票据失败");
            }
            WebMKSTicketDO webMKSTicket = new WebMKSTicketDO();
            webMKSTicket.setHost(ticket.getHost() != null ? ticket.getHost() : vm.getGuest().getIpAddress());
            webMKSTicket.setPort(ticket.getPort());
            webMKSTicket.setTicket(ticket.getTicket());
            webMKSTicket.setSslThumbprint(ticket.getSslThumbprint());
            String webmksUrl = constructWebMKSUrl(webMKSTicket);
            webMKSTicket.setUrl(webmksUrl);
            return webMKSTicket;
        } catch (Exception e) {
            log.error("获取WebMKS票据失败", e);
            throw new RuntimeException("获取WebMKS票据失败: " + e.getMessage(), e);
        } finally {
            disconnect();
        }
    }

    /**
     * 构建WebMKS URL
     */
    private String constructWebMKSUrl(WebMKSTicketDO ticket) {
        if (ticket.getHost() == null || ticket.getTicket() == null) {
            throw new IllegalArgumentException("无效的票据信息: host或ticket为空");
        }
        StringBuilder url = new StringBuilder();
        url.append("wss://").append(ticket.getHost());
        if (ticket.getPort() > 0) {
            url.append(":").append(ticket.getPort());
        }
        url.append("/ticket/").append(ticket.getTicket());
        return url.toString();
    }

    private void connect() throws Exception {
        try {
            URL url = new URL(vcenterUrl);
            serviceInstance = new ServiceInstance(url, username, password, true);
            AboutInfo about = serviceInstance.getAboutInfo();
            log.debug("vCenter版本信息: {}, API版本: {}",
                    about.getFullName(), about.getApiVersion());
        } catch (Exception e) {
            throw new RuntimeException("连接vCenter失败: " + e.getMessage(), e);
        }
    }

    private void disconnect() {
        if (serviceInstance != null) {
            try {
                serviceInstance.getServerConnection().logout();
                log.info("已断开vCenter连接");
            } catch (Exception e) {
                log.warn("断开vCenter连接时发生错误", e);
            }
        }
    }

    private void disableSSLVerification() {
        try {
            TrustManager[] trustAllCerts = new TrustManager[]{
                    new X509TrustManager() {
                        public X509Certificate[] getAcceptedIssuers() { return null; }
                        public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                        public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                    }
            };

            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            HttpsURLConnection.setDefaultHostnameVerifier((hostname, session) -> true);
        } catch (Exception e) {
            log.error("禁用SSL证书验证失败", e);
        }
    }

    public boolean testConnection() {
        try {
            connect();
            return true;
        } catch (Exception e) {
            return false;
        } finally {
            disconnect();
        }
    }

    public static WebMKSTicketDO testAndGetWebMKSTicket(String vcenterUrl, String username, String password, String vmName) throws Exception {
        VmwareWebConsoleClient client = new VmwareWebConsoleClient(vcenterUrl, username, password, vmName);
        // 1. 测试连接
        if (!client.testConnection()) {
            throw new Exception("连接测试失败，请检查配置");
        }

        // 2. 获取WebMKS票据
        return client.getWebMKSTicket();
    }

    public static WebMKSTicketDO getVMConsoleAccess(String vcenterUrl, String username, String password, String vmName) throws Exception {
        return testAndGetWebMKSTicket(vcenterUrl, username, password, vmName);
    }

    public static void main(String[] args) {
        try {
            WebMKSTicketDO ticket = testAndGetWebMKSTicket(
                "https://172.16.100.50/sdk",
                "<EMAIL>",
                "Lihulin@123",
                "TX_200.132_控制台2"
            );

            // 打印结果
            System.out.println("\n=== WebMKS票据信息 ===");
            System.out.println("WebMKS URL: " + ticket.getUrl());

        } catch (Exception e) {
            System.err.println("操作失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
