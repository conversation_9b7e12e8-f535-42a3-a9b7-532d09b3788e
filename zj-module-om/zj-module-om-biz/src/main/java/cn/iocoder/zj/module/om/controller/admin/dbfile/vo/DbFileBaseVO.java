package cn.iocoder.zj.module.om.controller.admin.dbfile.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 配置备份 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class DbFileBaseVO {

    @Schema(description = "文件名")
    private String name;

    @Schema(description = "文件路径")
    private String path;

    @Schema(description = "文件url")
    private String url;

    @Schema(description = "文件大小")
    private Long size;
    @Schema(description = "使用状态 1 使用中 0 未使用")
    private Integer state;

}
