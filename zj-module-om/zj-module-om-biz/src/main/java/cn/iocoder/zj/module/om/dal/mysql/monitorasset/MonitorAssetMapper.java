package cn.iocoder.zj.module.om.dal.mysql.monitorasset;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.om.api.asset.dto.MonitorAssetDTO;
import cn.iocoder.zj.module.om.dal.dataobject.monitorasset.MonitorAssetDO;
import cn.iocoder.zj.module.om.util.StringUtil;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.om.controller.admin.monitorasset.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 监控资产 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MonitorAssetMapper extends BaseMapperX<MonitorAssetDO> {

    default PageResult<MonitorAssetDO> selectPage(MonitorAssetPageReqVO reqVO, List<Map> platform) {
        List<String> data = new ArrayList<>();
        if (platform.size() > 0) {
            for (Map map : platform) {
                data.add(map.get("platformId").toString());
            }
        }
        LambdaQueryWrapperX<MonitorAssetDO> queryWrapperX = new LambdaQueryWrapperX<MonitorAssetDO>()
                .likeIfPresent(MonitorAssetDO::getAssetName, reqVO.getAssetName())
                .likeIfPresent(MonitorAssetDO::getProtocol, reqVO.getProtocol())
                .eqIfPresent(MonitorAssetDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(MonitorAssetDO::getPlatformName, reqVO.getPlatformName())
                .likeIfPresent(MonitorAssetDO::getUsername, reqVO.getUsername());

        if (jodd.util.StringUtil.isNotEmpty(reqVO.getStartTime()) && jodd.util.StringUtil.isNotEmpty(reqVO.getEndTime())) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            try {
                date = sdf.parse(reqVO.getEndTime());
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(date);
                // 把日期设置为当天最后一秒
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                date = calendar.getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            queryWrapperX.betweenIfPresent(MonitorAssetDO::getCreateTime, reqVO.getStartTime(), sdf.format(date));
        }

// 处理 OR 逻辑
        if (reqVO.getHostname() != null && !reqVO.getHostname().isEmpty()) {
            queryWrapperX.and(wrapper -> wrapper
                    .like(MonitorAssetDO::getHostname, reqVO.getHostname())
                    .or()
                    .like(MonitorAssetDO::getProtocolProd, reqVO.getHostname()));
        }
        if (StringUtil.isNotEmpty(reqVO.getSortDirection())){
            if (reqVO.getSortDirection().equals("asc")){
                if (reqVO.getSortBy().equals("createTime")){
                    queryWrapperX.orderByAsc(MonitorAssetDO::getCreateTime);
                }
                if (reqVO.getSortBy().equals("lastAccessTime")){
                    queryWrapperX.orderByAsc(MonitorAssetDO::getLastAccessTime);
                }
            }else {
                if (reqVO.getSortBy().equals("createTime")){
                    queryWrapperX.orderByDesc(MonitorAssetDO::getCreateTime);
                }
                if (reqVO.getSortBy().equals("lastAccessTime")){
                    queryWrapperX.orderByDesc(MonitorAssetDO::getLastAccessTime);
                }
            }
        }else {
            queryWrapperX.orderByDesc(MonitorAssetDO::getCreateTime);
        }


        if (data.size() > 0) {
            queryWrapperX = (LambdaQueryWrapperX<MonitorAssetDO>) queryWrapperX.in(MonitorAssetDO::getPlatformId, data);
        } else {
            queryWrapperX = (LambdaQueryWrapperX<MonitorAssetDO>) queryWrapperX.in(MonitorAssetDO::getPlatformId, "null");
        }
        return selectPage(reqVO, queryWrapperX);
    }

    default List<MonitorAssetDO> selectList(MonitorAssetExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MonitorAssetDO>()
                .eqIfPresent(MonitorAssetDO::getAssetId, reqVO.getAssetId())
                .likeIfPresent(MonitorAssetDO::getAssetName, reqVO.getAssetName())
                .eqIfPresent(MonitorAssetDO::getProtocol, reqVO.getProtocol())
                .eqIfPresent(MonitorAssetDO::getProtocolProd, reqVO.getProtocolProd())
                .eqIfPresent(MonitorAssetDO::getCertificate, reqVO.getCertificate())
                .eqIfPresent(MonitorAssetDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(MonitorAssetDO::getPlatformName, reqVO.getPlatformName())
                .eqIfPresent(MonitorAssetDO::getAssetType, reqVO.getAssetType())
                .eqIfPresent(MonitorAssetDO::getAuthorizationType, reqVO.getAuthorizationType())
                .betweenIfPresent(MonitorAssetDO::getAuthorizationTime, reqVO.getAuthorizationTime())
                .betweenIfPresent(MonitorAssetDO::getCreateTime, reqVO.getCreateTime())
                .likeIfPresent(MonitorAssetDO::getUsername, reqVO.getUsername())
                .eqIfPresent(MonitorAssetDO::getHostname, reqVO.getHostname())
                .orderByDesc(MonitorAssetDO::getId));
    }

    MonitorAssetDTO getAssetInfoById(@Param("id") Long id);

    @TenantIgnore
    String getTenantInfo(@Param("tenantId") Long tenantId);

    @TenantIgnore
    String getOpenIdByUserId(@Param("userId") String userId);

    @TenantIgnore
    void updateAuthorizationType(@Param("id") Long id, @Param("authorizationType") String authorizationType, @Param("authorizationTime") LocalDateTime authorizationTime);

    @TenantIgnore
    Long selectBycount(@Param("hostName") String hostName, @Param("platformId") Long platformId);


    @TenantIgnore
    List<String> getUserListByTenantId(@Param("tenantId") Long tenantId);

    @TenantIgnore
    void updateAssetById(@Param("checkedData")MonitorAssetDTO checkedData);

    @TenantIgnore
    MonitorAssetDTO selectOneDo(@Param("monitorId") String monitorId,@Param("platformId") Long platformId);
    @TenantIgnore
    void updateAssetByAssetId(@Param("dto")MonitorAssetDTO dto);
    @TenantIgnore
    void updateAsset(@Param("id")Long id, @Param("name")String name);

    @TenantIgnore
    void updateAuthType(@Param("id") Long id, @Param("authorizationType") String authorizationType, @Param("authorizationTime") LocalDateTime authorizationTime);
}
