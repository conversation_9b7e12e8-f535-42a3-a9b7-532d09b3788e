package cn.iocoder.zj.module.om.controller.admin.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class OrderProcessBaseVo {
    @Schema(description = "工单ID",required = true)
    private Long workOrderId;

    @Schema(description = "处理结果",required = true)
    private String processResult;

    @Schema(description = "退回原因",required = true)
    private String content;

    @Schema(description = "处理时间",required = true)
    private Date createTime;

    @Schema(description = "处理人id")
    private String creator;

    @Schema(description = "处理人名称")
    private String creatorName;
}
