package cn.iocoder.zj.module.om.dal.dataobject.userbind;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 用户推送绑定 DO
 *
 * <AUTHOR>
 */
@TableName("om_user_bind")
@KeySequence("om_user_bind_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserBindDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 企业微信推送启用状态，0未启用，1已启用
     */
    private Integer wxState;
    /**
     * 邮箱推送启用状态，0未启用，1已启用
     */
    private Integer emailState;
    /**
     * 钉钉推送启用状态，0未启用,1启用中
     */
    private Integer dingtalkState;
    /**
     * 钉钉应用key
     */
    private String dingtalkAppKey;
    /**
     * 钉钉应用秘钥
     */
    private String dingtalkAppSecret;
    /**
     * 应用id
     */
    private String dingtalkAgentId;
    /**
     * 钉钉手机号
     */
    private String dingtalkPhone;
    /**
     * 企微应用id
     */
    private String wxAgentId;
    /**
     * 企微企业id
     */
    private String wxCorpid;
    /**
     * 企微企业秘钥
     */
    private String wxCorpsecret;
    /**
     * 企微手机号
     */
    private String wxPhone;

    private String callbackUrl;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String mailAddress;

    private String mailPassword;

    private String mailSmtpHost;
}
