package cn.iocoder.zj.module.om.service.vmhost;

import cn.iocoder.zj.module.om.controller.admin.vmhost.vo.VmHostCreateReqVo;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

@Service
@Validated
public class VmHostServiceImpl implements VmHostService{
    @Override
    public Long createVmHost(VmHostCreateReqVo createReqVO) {
        return null;
    }
}
