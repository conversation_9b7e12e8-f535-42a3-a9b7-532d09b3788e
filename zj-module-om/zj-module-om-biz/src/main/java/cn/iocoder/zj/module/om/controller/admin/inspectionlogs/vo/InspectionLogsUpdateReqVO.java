package cn.iocoder.zj.module.om.controller.admin.inspectionlogs.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 自动巡检异常记录更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InspectionLogsUpdateReqVO extends InspectionLogsBaseVO {

    @Schema(description = "主键id", required = true)
    @NotNull(message = "主键id不能为空")
    private Long id;

}
