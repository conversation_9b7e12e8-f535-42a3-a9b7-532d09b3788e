package cn.iocoder.zj.module.om.Inspection.metric.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.cloud.framework.warehouse.service.MetricsDataService;
import cn.iocoder.zj.module.om.dal.dataobject.patrolabnormaldetail.PatrolAbnormalDetailDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspectionconfig.PatrolInspectionConfigDO;
import cn.iocoder.zj.module.om.enums.PatrolMetricEnum;
import cn.iocoder.zj.module.om.enums.PatrolResourceTypeEnum;
import org.apache.hertzbeat.common.entity.dto.MetricsHistoryData;
import org.apache.hertzbeat.common.entity.dto.Value;
import org.apache.hertzbeat.common.support.SpringContextHolder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;

import static cn.iocoder.zj.module.om.Inspection.util.InspectionUtil.convertDurationUnitToChinese;

public class HzVmThresholdMetricHandler<T> extends MetricHandler<T> {


    private final PatrolResourceTypeEnum resourceType;
    private final Class<T> resourceClass;
    private final Predicate<T> validResourceFilter;
    private final Function<T, Long> resourceIdGetter;
    private final Function<T, String> resourceNameGetter;
    private final Function<T, String> platformIdGetter;
    private final Function<T, String> platformNameGetter;
    private final Function<T, String> appGetter;
    private final String step;
    private final String type;
    private final Integer flag;// 0:非持续 1:持续 2:差值率,3:差值率+平均值

    //满足条件的阈值配置
    private PatrolInspectionConfigDO patrolInspectionConfigDO;

    public HzVmThresholdMetricHandler(PatrolMetricEnum metricType, PatrolResourceTypeEnum resourceType,
                                      Class<T> resourceClass, Predicate<T> validResourceFilter,
                                      Function<T, Long> resourceIdGetter, Function<T, String> resourceNameGetter,
                                      Function<T, String> platformIdGetter, Function<T, String> platformNameGetter,
                                      Function<T, String> appGetter, String step, String type, Integer flag) {
        super(metricType);
        this.resourceType = resourceType;
        this.resourceClass = resourceClass;
        this.validResourceFilter = validResourceFilter;
        this.resourceIdGetter = resourceIdGetter;
        this.resourceNameGetter = resourceNameGetter;
        this.platformIdGetter = platformIdGetter;
        this.platformNameGetter = platformNameGetter;
        this.appGetter = appGetter;
        this.step = step;
        this.type = type;
        this.flag = flag;
    }

    @Override
    public PatrolResourceTypeEnum getResourceType() {
        return resourceType;
    }

    @Override
    protected List<T> filterValidResources(List<?> resourceDataList) {
        List<T> resources = BeanUtil.copyToList(resourceDataList, resourceClass);
        if (validResourceFilter != null) {
            return resources.stream().filter(validResourceFilter).toList();
        }
        return resources;
    }

    @Override
    protected boolean checkResourceAbnormal(T resource, List<PatrolInspectionConfigDO> configList) {
        Long uuid = resourceIdGetter.apply(resource);
        String app = appGetter.apply(resource);

        BigDecimal metricValue = null;
        List<String> origins = new ArrayList<>();
        //差值率
        BigDecimal diffRate = null;
        //拿第一条获取持续天数和单位
        PatrolInspectionConfigDO config_1 = configList.get(0);
        //巡检周期
        Integer patrolTime = config_1.getPatrolTime();
        Integer duration_1 = config_1.getDuration();
        String durationUnit_1 = config_1.getDurationUnit();
        String queryDuration;
        boolean isDuration = duration_1 != null && duration_1 > 0 && durationUnit_1 != null;
        if (isDuration) {
            queryDuration = duration_1 + durationUnit_1;
        } else {
            queryDuration = patrolTime + "D";
        }
        String stepQry = step;
        if (stepQry == null || stepQry.isEmpty()) {
            stepQry = queryDuration;
        }

        MetricsDataService metricsDataService = SpringContextHolder.getBean(MetricsDataService.class);

        Map<String, String> metricMap = getMetricsAndMetricByApp(app);
        String metrics = metricMap.get("metrics");
        String metric = metricMap.get("metric");

        Map<String, List<Value>> values = new HashMap<>();
        Map<String, Map<String, List<Value>>> valuesMap = new HashMap<>();
        if (flag == 1) {
            LocalDateTime now = LocalDateTime.now();
            // 按照巡检周期天数，分别查询每个24小时时间段的数据
            for (int i = 0; i < patrolTime; i++) {
                // 计算每个24小时时间段的开始和结束时间
                LocalDateTime segmentEnd = now.minusDays(i);
                LocalDateTime segmentStart = segmentEnd.minusDays(1);

                // 转换为时间戳（秒）
                long startTimestamp = segmentStart.atZone(ZoneId.systemDefault()).toEpochSecond();
                long endTimestamp = segmentEnd.atZone(ZoneId.systemDefault()).toEpochSecond();

                // 查询当前24小时时间段的数据
                MetricsHistoryData segmentData = metricsDataService.getHistoryIntervalMetricData(uuid, app,
                        metrics, metric, "", null, startTimestamp, endTimestamp, stepQry, type);

                // 合并数据
                if (segmentData != null && segmentData.getValues() != null) {
                    for (Map.Entry<String, List<Value>> entry : segmentData.getValues().entrySet()) {
                        String key = entry.getKey();
                        List<Value> segmentValues = entry.getValue();

                        if (segmentValues != null && !segmentValues.isEmpty()) {
                            values.computeIfAbsent(key, k -> new ArrayList<>()).addAll(segmentValues);
                        }
                    }
                }
                // 对合并后的数据按时间戳排序
                for (List<Value> valueList : values.values()) {
                    valueList.sort(Comparator.comparing(Value::getTime));
                }
            }
        } else if (flag == 0) {
            MetricsHistoryData metricHistoryData = metricsDataService.
                    getHistoryIntervalMetricData(uuid, app,
                            metrics, metric, "", queryDuration, null, null, stepQry, type);
            values = metricHistoryData != null ? metricHistoryData.getValues() : new HashMap<>();
        } else if (flag == 2 || flag == 3) {
            //获取差值 获取一个最大值 一个最小值
            MetricsHistoryData metricHistoryData = metricsDataService.getHistoryIntervalMetricData(uuid, app,
                    metrics, metric, "", queryDuration, null, null, stepQry, null);
            values = metricHistoryData != null ? metricHistoryData.getValues() : new HashMap<>();
        }

        if (ObjectUtil.isEmpty(values)) {
            return false;
        }

        List<Value> valueList = new ArrayList<>();
        if (app.equals("windows")) {
            if (metrics.equals("storages")) {
                for (Map.Entry<String, List<Value>> entry : values.entrySet()) {
                    if (entry.getKey().contains("Physical Memory"))
                        valueList = entry.getValue();
                }
            } else {
                for (Map.Entry<String, List<Value>> entry : values.entrySet()) {
                    valueList = entry.getValue();
                }
            }
        } else {
            if (metrics.equals("top_cpu_process") || metrics.equals("top_mem_process")) {
                //循环获取所有values 的中第一条数据 然后取最大的
                for (Map.Entry<String, List<Value>> entry : values.entrySet()) {
                    valueList.add(entry.getValue().get(0));
                }
                //先把origin转成BigDecimal 在进行倒叙排序

                // valueList.sort(Comparator.comparing(Value::getOrigin).reversed());
                valueList.sort(Comparator.comparing(v -> new BigDecimal(v.getOrigin()), Comparator.reverseOrder()));
            } else {
                for (Map.Entry<String, List<Value>> entry : values.entrySet()) {
                    valueList = entry.getValue();
                }
            }
        }

        if (flag == 0) {
            metricValue = valueList != null && !valueList.isEmpty() ?
                    new BigDecimal(valueList.get(0).getOrigin()) : new BigDecimal(0);

        } else if (flag == 1) {
            origins = valueList.stream().map(Value::getOrigin).toList();
        } else if (flag == 2 || flag == 3) {
            String max = valueList.get(0).getMax();
            String min = valueList.get(0).getMin();
            if (max != null && min != null) {
                diffRate = new BigDecimal(max).subtract(new BigDecimal(min));
            } else {
                return false;
            }
            if (flag == 3) {
                if (valueList.get(0).getMean() != null) {
                    metricValue = new BigDecimal(valueList.get(0).getMean());
                } else {
                    return false;
                }
            }
        }

        for (PatrolInspectionConfigDO config : configList) {
            String operator = config.getOperator();
            BigDecimal thresholdValue = config.getThresholdValue();
            BigDecimal thresholdValueMax = config.getThresholdValueMax();
            Integer duration = config.getDuration();
            String durationUnit = config.getDurationUnit();
            BigDecimal diffRate1 = config.getDiffRate();
            String diffRateOperator = config.getDiffRateOperator();
            if (flag == 0) {
                if (">=".equals(operator) && metricValue.compareTo(thresholdValue) >= 0) {
                    patrolInspectionConfigDO = config;
                    return true;
                } else if ("between".equals(operator) && metricValue.compareTo(thresholdValue) >= 0
                        && metricValue.compareTo(thresholdValueMax) < 0) {
                    patrolInspectionConfigDO = config;
                    return true;
                } else if ("<".equals(operator) && metricValue.compareTo(thresholdValue) < 0) {
                    return false;
                }
            } else if (flag == 1) {
                if (">=".equals(operator)) {
                    // 实现持续性检查逻辑：
                    // 1. 找到大于等于阈值的值并记录下标
                    // 2. 从该下标开始检查持续duration时间的值是否都大于等于阈值
                    // 3. 如果满足条件返回true，否则继续从下一个满足条件的下标开始检查
                    // 4. 直到检查完所有数据，没有满足条件则返回false
                    boolean isAbnormal = checkContinuousThreshold(origins, thresholdValue, duration, durationUnit);
                    if (isAbnormal) {
                        patrolInspectionConfigDO = config;
                    }
                    return isAbnormal;
                }
            } else if (flag == 2) {
                if (">=".equals(diffRateOperator) && diffRate.compareTo(diffRate1) >= 0) {
                    patrolInspectionConfigDO = config;
                    return true;
                }
            } else if (flag == 3) {
                if (">=".equals(operator)) {
                    boolean isAbnormal = metricValue.compareTo(thresholdValue) >= 0;
                    if (!isAbnormal) {
                        return false;
                    }
                    if ("<".equals(diffRateOperator) && diffRate.compareTo(diffRate1) < 0) {
                        patrolInspectionConfigDO = config;
                        return true;
                    }
                }
            }
        }
        return false;
    }

    @Override
    protected PatrolAbnormalDetailDO createAbnormalDetail(T resource, List<PatrolInspectionConfigDO> configList, Long tenantId) {
        if (patrolInspectionConfigDO == null) {
            return null;
        }
        String unit = patrolInspectionConfigDO.getUnit();
        Integer duration = patrolInspectionConfigDO.getDuration();
        String durationUnit = patrolInspectionConfigDO.getDurationUnit();
        BigDecimal thresholdValue = patrolInspectionConfigDO.getThresholdValue();
        String riskLevel = patrolInspectionConfigDO.getThresholdType();
        Integer patrolTime = patrolInspectionConfigDO.getPatrolTime();
        BigDecimal diffRate = patrolInspectionConfigDO.getDiffRate();

        PatrolAbnormalDetailDO abnormalDetail = new PatrolAbnormalDetailDO();
        String detail = getResourceType().getName() + resourceNameGetter.apply(resource)
                + getMetricType().getName() + "在过去" + patrolTime + "天内，";
        if (flag == 0) {
            detail += "大于等于" + thresholdValue + unit;
        } else if (flag == 1) {
            durationUnit = convertDurationUnitToChinese(durationUnit);
            detail += "存在持续" + duration + durationUnit + "大于等于" + thresholdValue + unit;
        } else if (flag == 2) {
            detail += "差值率大于等于" + diffRate + "%";
        } else if (flag == 3) {
            detail += "大于等于" + thresholdValue + unit + "且差值率小于" + diffRate + "%";
        }
        abnormalDetail.setDetail(detail);
        abnormalDetail.setResourceType(getResourceType().getCode());
        abnormalDetail.setMetricName(getMetricType().getCode());
        abnormalDetail.setResourceId(String.valueOf(resourceIdGetter.apply(resource)));
        abnormalDetail.setResourceName(resourceNameGetter.apply(resource));
        abnormalDetail.setPlatformId(Long.valueOf(platformIdGetter.apply(resource)));
        abnormalDetail.setPlatformName(platformNameGetter.apply(resource));
        abnormalDetail.setRiskLevel(riskLevel);
        abnormalDetail.setTenantId(tenantId);
        String suggest = getMetricType().getSuggest().get(riskLevel);
        abnormalDetail.setSuggest(suggest);

        return abnormalDetail;
    }

    private Map<String, String> getMetricsAndMetricByApp(String app) {
        Map<String, String> map = new HashMap<>();
        String metrics = "";
        String metric = "";
        switch (getResourceType().getCode()) {
            case "network":
                metrics = "system";
                if (getMetricType().getCode().equals("networkCpuAvgUsage"))
                    metric = "cpuUsage";
                if (getMetricType().getCode().equals("networkMemAvgUsage"))
                    metric = "memUsage";
                break;
            case "firewall":
                if (getMetricType().getCode().equals("firewallCpuAvgUsage")) {
                    metrics = "cpu";
                    metric = "cpu_used";
                }
                if (getMetricType().getCode().equals("firewallMemAvgUsage")) {
                    metrics = "memory";
                    metric = "mem_usage";
                }
                break;
            case "os":
                if (app.equals("linux_snmp")) {
                    if (getMetricType().getCode().equals("osCpuAvgUsage")) {
                        metrics = "cpu";
                        metric = "hrProcessorLoad";
                    }
                    if (getMetricType().getCode().equals("osMemoryAvgUsage")) {
                        metrics = "system";
                        metric = "memUsage";
                    }
                } else if (app.equals("windows")) {
                    if (getMetricType().getCode().equals("osCpuAvgUsage")) {
                        metrics = "cpu";
                        metric = "cpuUsage";
                    } else if (getMetricType().getCode().equals("osMemoryAvgUsage")) {
                        metrics = "storages";
                        metric = "usage";
                    }
                } else {
                    if (getMetricType().getCode().equals("osCpuProcessUsage")) {
                        metrics = "top_cpu_process";
                        metric = "cpu_usage";
                    }
                    if (getMetricType().getCode().equals("osMemoryProcessUsage")) {
                        metrics = "top_mem_process";
                        metric = "mem_usage";
                    }
                    if (getMetricType().getCode().equals("osCpuAvgUsage")) {
                        metrics = "cpu";
                        metric = "usage";
                    }
                    if (getMetricType().getCode().equals("osMemoryAvgUsage")) {
                        metrics = "memory";
                        metric = "usage";
                    }
                }
                break;
            default:
                metrics = "";
                metric = "";
        }
        map.put("metrics", metrics);
        map.put("metric", metric);
        return map;
    }
}
