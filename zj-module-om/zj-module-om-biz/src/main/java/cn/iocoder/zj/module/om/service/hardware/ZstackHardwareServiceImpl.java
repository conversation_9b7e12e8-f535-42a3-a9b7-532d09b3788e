package cn.iocoder.zj.module.om.service.hardware;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespDTO;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo.ResourceOperateLog;
import cn.iocoder.zj.module.om.controller.admin.vncinfo.vo.VncInfoVo;
import cn.iocoder.zj.module.om.dal.mysql.assetmanagement.AssetManagementMapper;
import cn.iocoder.zj.module.om.framework.websocket.WebSocketClient;
import cn.iocoder.zj.module.om.util.StringUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.iocoder.zj.module.monitor.api.hardware.HardWareInfoApi;
import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespCreateReqDTO;
import cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo.ZstackCreateHardwareReqVo;
import cn.iocoder.zj.module.om.dal.redis.zstack.ZstackAccessTokenRedisDAO;
import cn.iocoder.zj.module.om.service.zstack.core.ZstackApiConstant;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.net.URI;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Validated
@Slf4j
public class ZstackHardwareServiceImpl implements IZstackHardwareService{
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    ZstackAccessTokenRedisDAO zstackAccessTokenRedisDAO;
    @Resource
    HardWareInfoApi hardWareInfoApi;
    @Resource
    AssetManagementMapper assetManagementMapper;

    @Resource WebSocketClient webSocketClient;
    @Override
    public CommonResult<Map<String,String>> createHardwareToZstack(ZstackCreateHardwareReqVo reqVo) {
        String token = zstackAccessTokenRedisDAO.get("zstack:" + reqVo.getPlatformId()).getUuid();
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(reqVo.getPlatformId()).getData();
        //传入参数
        JSONObject body = new JSONObject();
        JSONObject params = new JSONObject();
        params.put("username",reqVo.getUsername());
        params.put("password",reqVo.getPassword());
        if(reqVo.getSshPort() != null) {
            params.put("sshPort ", reqVo.getSshPort());
        }
        if(StringUtil.isNotEmpty(reqVo.getDescription())) {
            params.put("description", reqVo.getDescription());
        }
        params.put("name",reqVo.getName());
        params.put("managementIp", reqVo.getManagementIp());
        params.put("clusterUuid",reqVo.getClusterUuid());
        body.put("params",params);
        //发起创建请求
        HttpResponse resp = HttpRequest.post(platform.getUrl() + ZstackApiConstant.POST_ZSTACK_CREATE_HARDWARE)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .body(JSONObject.toJSONString(body))
                .execute();
        return addHardware(reqVo.getName(),resp.body(), platform.getUrl(), token,platform);
    }
    @Override
    public CommonResult<Map<String,String>> operateHardware(String uuid, Long platformId, String actions, String state) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        HardWareRespCreateReqDTO respDTO = hardWareInfoApi.getByUuid(uuid).getData();
        String op = hardwareOperateConvert(actions,state);
        List<HardWareRespCreateReqDTO> reqList = new ArrayList<>();
        String token = zstackAccessTokenRedisDAO.get("zstack:" + platformId).getUuid();
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(platformId).getData();
        String url = platform.getUrl() + ZstackApiConstant.PUT_HARDWARE_ACTIONS+"/power/"+uuid+"/actions";
        JSONObject body = new JSONObject();
        JSONObject actionType = new JSONObject();
        String persistentState = "";
        if (actions.equals("reconnect")) {
            //重连物理机
            body.put("reconnectHost",actionType);
            persistentState = "Reconnecting";
        } else if (actions.equals("stateChange")) {
            switch (state){
                case "enable":persistentState = "Enabling";break;
                case "disable":persistentState = "Disabling";break;
                case "maintain":persistentState = "Maintaining";break;
                case "ext_maintain":persistentState = "Ext_Maintaining";break;
            }
            //物理机启用状态变更
            actionType.put("stateEvent",state);
            body.put("changeHostState",actionType);
            url = platform.getUrl() + ZstackApiConstant.PUT_HARDWARE_ACTIONS+"/"+uuid+"/actions";
        }
        HttpResponse resp = HttpRequest.put(url)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .body(JSONObject.toJSONString(body))
                .execute();
        HardWareRespCreateReqDTO reqDTO = new HardWareRespCreateReqDTO();
        reqDTO.setUuid(uuid);
        reqDTO.setState(persistentState);
        reqList.add(reqDTO);
        hardWareInfoApi.updates(reqList);
        //新启线程处理任务执行结果
        Runnable data = (() -> {
            updateHardwareInfo(resp.body(),uuid,platform,actions,state,loginUser);
        });
        Thread thread = new Thread(data);
        thread.start();
        Map<String,String> reuslt = new HashMap<>();
        reuslt.put("success","true");
        reuslt.put("msg","正在"+op+"宿主机("+respDTO.getName()+")");
        return CommonResult.success(reuslt);
    }
    public void updateHardwareInfo(String respBody,String hardwareUuid,PlatformconfigDTO platform,String actions,String state,LoginUser loginUser){
        HardWareRespCreateReqDTO reqDTO = new HardWareRespCreateReqDTO();
        String resultUrl = JSONObject.parseObject(respBody).get("location").toString();
        String body = HttpRequest.get(resultUrl).execute().body();
        //为空是因为操作还在执行中
        while (body.equals("{}")) {
            body= HttpRequest.get(resultUrl).execute().body();
        }
        HardWareRespCreateReqDTO hardwareInfo = hardWareInfoApi.getByUuid(hardwareUuid).getData();
        String op = hardwareOperateConvert(actions,state);
        JSONObject reuslt = new JSONObject();
        reuslt.put("success","true");
        reuslt.put("msg",op+"("+hardwareInfo.getName()+")成功");
        List<HardWareRespCreateReqDTO> reqList = new ArrayList<>();
        if(JSONObject.parseObject(body).size()==0||JSONObject.parseObject(body).get("error")==null){
            JSONObject obj = JSONObject.parseObject(JSONObject.toJSONString(JSONObject.parseObject(body).get("inventory")));
            reqDTO.setUuid(hardwareUuid);
            reqDTO.setState(obj.getString("state"));
            reqList.add(reqDTO);
            hardWareInfoApi.updates(reqList);
            createOperateLog(hardwareInfo.getName(), hardwareUuid, op, "success", "hardware", "", platform,loginUser);
            webSocketClient.sendMessage(hardwareUuid,reuslt.toJSONString());
        }else {
            //失败返回错误信息
            JSONObject obj = JSONObject.parseObject(JSONObject.toJSONString(JSONObject.parseObject(body).get("error")));
            String msg = getErrorMsg(obj);
            reuslt.put("success","false");
            reuslt.put("msg",op+"("+hardwareInfo.getName()+")失败,错误信息:"+msg+",请至"+platform.getName()+"查看详情");
            createOperateLog(hardwareInfo.getName(), hardwareUuid, op, "error", "hardware",msg, platform,loginUser);
            webSocketClient.sendMessage(hardwareUuid,reuslt.toJSONString());
        }
    }
    public CommonResult<Map<String,String>> addHardware(String hardwareName,String resp, String url, String token, PlatformconfigDTO platform) {
        String resultUrl = JSONObject.parseObject(resp).get("location").toString();
        String body = HttpRequest.get(resultUrl).execute().body();
        Map<String,String> reuslt = new HashMap<>();
        while (body.equals("{}")) {
            body = HttpRequest.get(resultUrl).execute().body();
        }
        JSONObject obj = JSONObject.parseObject(JSONObject.toJSONString(JSONObject.parseObject(body).get("error")));
        if(obj!=null){
            String msg = getErrorMsg(obj);
            reuslt.put("success","false");
            reuslt.put("msg",msg);
            return CommonResult.success(reuslt);
        }
        List<HardWareRespCreateReqDTO> hardWareRespCreateReqDTOS = new ArrayList<>();
        HttpResponse result = HttpRequest.get(url + ZstackApiConstant.GET_ZSTACK_HOSTS)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .execute();
        if (result.getStatus() != 200) {
            String respBody = result.body();
            JSONObject respObj = JSONObject.parseObject(JSONObject.toJSONString(JSONObject.parseObject(respBody).get("error")));
            String msg = getErrorMsg(respObj);
            reuslt.put("success","false");
            reuslt.put("msg",msg);
            return CommonResult.success(reuslt);
        }
        //添加记录
        JSONArray jsonArray = JSONObject.parseObject(result.body()).getJSONArray("inventories");
        //重新拉取物理机信息
        for (int i = 0; i < jsonArray.size(); i++) {
            HardWareRespCreateReqDTO hardWareRespCreateReqDTO = new HardWareRespCreateReqDTO();
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            if ("KVM".equals(jsonObject.getString("hypervisorType"))) {
                String clusterName = "";
                BigDecimal cpu_useds = new BigDecimal(0);
                BigDecimal bandwidth_upstream = new BigDecimal(0);
                BigDecimal bandwidth_downstream = new BigDecimal(0);
                BigDecimal memory_used = new BigDecimal(0);
                BigDecimal packet_rate = new BigDecimal(0);
                BigDecimal diskUsed = new BigDecimal(0);
                BigDecimal diskUsedBytes = new BigDecimal(0);
                BigDecimal diskFreeBytes = new BigDecimal(0);
                BigDecimal totalDiskCapacity = new BigDecimal(0);
                String uuid = jsonObject.getString("uuid");
                String name = jsonObject.getString("name");
                String state = jsonObject.getString("state");
                String ip = jsonObject.getString("managementIp");
                String status = jsonObject.getString("status");
                Long totalCpuCapacity = jsonObject.getLong("totalCpuCapacity");
                Long availableCpuCapacity = jsonObject.getLong("availableCpuCapacity");
                Integer cpuSockets = jsonObject.getInteger("cpuSockets");
                String architecture = jsonObject.getString("architecture");
                Integer cpuNum = jsonObject.getInteger("cpuNum");
                Long totalMemoryCapacity = jsonObject.getLong("totalMemoryCapacity");
                Long availableMemoryCapacity = jsonObject.getLong("availableMemoryCapacity");
                String clusterUuid = jsonObject.getString("clusterUuid");

                hardWareRespCreateReqDTO.setTotalDiskCapacity(totalDiskCapacity);
                hardWareRespCreateReqDTO.setUuid(uuid);
                hardWareRespCreateReqDTO.setName(name);
                hardWareRespCreateReqDTO.setState(state);
                hardWareRespCreateReqDTO.setIp(ip);
                hardWareRespCreateReqDTO.setTypeName(platform.getTypeCode());
                hardWareRespCreateReqDTO.setStatus(status);
                hardWareRespCreateReqDTO.setClusterUuid(clusterUuid);
                hardWareRespCreateReqDTO.setClusterName(clusterName);
                hardWareRespCreateReqDTO.setTotalCpuCapacity(totalCpuCapacity);
                hardWareRespCreateReqDTO.setAvailableCpuCapacity(availableCpuCapacity);
                hardWareRespCreateReqDTO.setCpuSockets(cpuSockets);
                hardWareRespCreateReqDTO.setArchitecture(architecture);
                hardWareRespCreateReqDTO.setCpuNum(cpuNum);
                hardWareRespCreateReqDTO.setTotalMemoryCapacity(totalMemoryCapacity);
                hardWareRespCreateReqDTO.setAvailableMemoryCapacity(availableMemoryCapacity);
                hardWareRespCreateReqDTO.setBandwidthUpstream(bandwidth_upstream);
                hardWareRespCreateReqDTO.setBandwidthDownstream(bandwidth_downstream);
                hardWareRespCreateReqDTO.setMemoryUsed(memory_used);
                hardWareRespCreateReqDTO.setPacketRate(packet_rate);
                hardWareRespCreateReqDTO.setCpuUsed(cpu_useds);
                hardWareRespCreateReqDTO.setDiskUsed(diskUsed);
                hardWareRespCreateReqDTO.setDiskUsedBytes(diskUsedBytes);
                hardWareRespCreateReqDTO.setDiskFreeBytes(diskFreeBytes);
                hardWareRespCreateReqDTO.setTotalDiskCapacity(diskUsedBytes.add(diskFreeBytes));
                hardWareRespCreateReqDTO.setTenantId(platform.getTenantId());
                hardWareRespCreateReqDTO.setRegionId(platform.getRegionId());
                hardWareRespCreateReqDTO.setPlatformId(platform.getId());
                hardWareRespCreateReqDTO.setPlatformName(platform.getName());
                hardWareRespCreateReqDTOS.add(hardWareRespCreateReqDTO);
            }

        }
        if (hardWareRespCreateReqDTOS.size()>0){
            // 获取当前节点的index 与 总节点数
            int shardIndex = XxlJobHelper.getShardIndex();
            int shardTotal = XxlJobHelper.getShardTotal();
            log.info("当前节点的index = {}, 总结点数 = {}", shardIndex, shardTotal);
            int hardWare = hardWareInfoApi.count("zstack");
            List<HardWareRespCreateReqDTO> dtos = hardWareInfoApi.getAll("zstack").getData();
            List<HardWareRespCreateReqDTO> shardingData = StringUtil.getShardingData(hardWareRespCreateReqDTOS, shardTotal, shardIndex);
            // 对分片数据进行业务处理
            for (HardWareRespCreateReqDTO item : shardingData) {
                // 模拟业务逻辑处理
                log.info("Processing item: " + item.getName());
            }
            if (hardWare == 0) {
                hardWareInfoApi.adds(shardingData);
            } else {
                hardWareInfoApi.updates(shardingData);
                List<HardWareRespCreateReqDTO> collect = shardingData.stream()
                        .filter(item -> !dtos.stream().map(HardWareRespCreateReqDTO::getUuid)
                                .collect(Collectors.toList()).contains(item.getUuid()))
                        .collect(Collectors.toList());
                if (collect.size() > 0) {
                    hardWareInfoApi.adds(collect);
                }
            }
        }
        reuslt.put("success","true");
        reuslt.put("msg","");
        return CommonResult.success(reuslt);
    }
    public String getErrorMsg(JSONObject obj){
        String msg = "操作失败，请检查参数是否正确";
        if(jodd.util.StringUtil.isNotBlank(obj.getString("elaboration"))){
            msg = obj.getString("elaboration");
        } else if (jodd.util.StringUtil.isNotBlank(obj.getString("details"))) {
            msg = obj.getString("details");
        } else if(jodd.util.StringUtil.isNotBlank(obj.getString("description"))){
            msg = obj.getString("description");
        }
        return msg;
    }
    @Override
    public String hardwareOperateConvert(String operation, String state){
        String op = "";
        switch (operation){
            case "reconnect":
                op = "重启宿主机"; break;
            case "create":
                op = "创建宿主机"; break;
            case "stateChange":
                switch (state){
                    case "enable" :
                        op = "启用宿主机"; break;
                    case "disable" :
                        op = "停用宿主机"; break;
                    case "maintain" :
                        op = "进入维护"; break;
                    case "ext_maintain" :
                        op = "退出维护"; break;
                }
                break;
        }
        return op;
    }


    public void createOperateLog(String name, String uuid, String operation, String result, String resourceType, String msg, PlatformconfigDTO platform,LoginUser loginUser) {
        ResourceOperateLog operateLog = new ResourceOperateLog();
        operateLog.setCreator(String.valueOf(loginUser.getId()));
        operateLog.setOperation(operation);
        operateLog.setResourceType(resourceType);
        operateLog.setResult(result);
        operateLog.setResourceUuid(uuid);
        operateLog.setResourceName(name);
        operateLog.setPlatformName(platform.getName());
        operateLog.setPlatformId(platform.getId());
        operateLog.setContent(msg);
        assetManagementMapper.createOperateLog(operateLog);
    }
}
