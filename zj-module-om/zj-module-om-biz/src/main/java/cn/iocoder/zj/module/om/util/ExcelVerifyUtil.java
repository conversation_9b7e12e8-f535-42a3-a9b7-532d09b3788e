package cn.iocoder.zj.module.om.util;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.om.enums.ErrorCodeConstants.*;

public class ExcelVerifyUtil {

    public static void fileVerify(MultipartFile file,Class entity){
        //验证文件格式正确
        String filename = file.getOriginalFilename();
        if (filename != null) {
            //获取其后缀
            String extension = filename.substring(filename.lastIndexOf(".") + 1);
            if (!(extension.equals("xls") || extension.equals("xlsx"))) {
                //此处为自定义异常捕获,可使用其他方式
                throw exception(FILE_FORMAT_WRONG);
            }
        }
        try {
            //inputStream 流只能读取一次！只能读取一次！只能读取一次！
            InputStream inputStream = file.getInputStream();
            ExcelReader reader = ExcelUtil.getReader(inputStream, 0);
            //验证文件不为空
            int rowCount = reader.getRowCount();
            if (rowCount <= 1) {
                throw  exception(FILE_IS_EMPTY);
            }
            //验证文件内容正确
            Field[] fields = entity.getDeclaredFields();
            List<String> targetHeaders = new ArrayList<>();
            for (Field field : fields) {
                ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
                if (excelProperty != null) {
                    targetHeaders.add(excelProperty.value()[0]);
                }
            }
            boolean isRight = true;
            List<String> fileHeaders = new ArrayList<>();
            Sheet sheet = reader.getSheet();
            Row headerRow = sheet.getRow(0);
            for (Cell cell : headerRow) {
                fileHeaders.add(cell.getStringCellValue());
            }
            for (String item:targetHeaders) {
                if(!fileHeaders.contains(item)){
                    isRight = false;
                }
            }
            if (!isRight) {
                throw  exception(FILE_IS_WRONG);
            }

        } catch (IOException e) {
            throw new RuntimeException(e);
        }


    }
}
