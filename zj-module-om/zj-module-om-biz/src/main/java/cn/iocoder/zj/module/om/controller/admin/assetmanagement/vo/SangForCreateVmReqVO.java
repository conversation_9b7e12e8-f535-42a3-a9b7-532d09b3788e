package cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
@Data
public class SangForCreateVmReqVO {
    @Schema(description = "平台ID",required = true)
    private Long platformId;
    @Schema(description = "存储",required = true)
    private String cfgstorage;
    @Schema(description = "内存,以G为单位",required = true)
    private Integer memory;
    @Schema(description = "虚拟机名称，不可重复",required = true)
    private String name;
    @Schema(description = "操作系统，从sangfor_os_type字典取值",required = true)
    private String osType;
    @Schema(description = "cpu核数",required = true)
    private Integer cores;
    @Schema(description = "网络",required = true)
    private String network ;
    @Schema(description = "分配存储空间,以G为单位，受所选存储的剩余可用空间限制",required = true)
    private Integer storageSize;
}
