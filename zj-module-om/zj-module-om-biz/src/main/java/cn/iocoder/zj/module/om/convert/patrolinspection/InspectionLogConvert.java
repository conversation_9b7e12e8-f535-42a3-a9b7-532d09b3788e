package cn.iocoder.zj.module.om.convert.patrolinspection;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.om.dal.dataobject.patrolinspection.InspectionLogDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.om.controller.admin.inspectiondata.vo.*;

/**
 * 巡检记录 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface InspectionLogConvert {

    InspectionLogConvert INSTANCE = Mappers.getMapper(InspectionLogConvert.class);

    InspectionLogDO convert(InspectionLogCreateReqVO bean);

    InspectionLogDO convert(InspectionLogUpdateReqVO bean);

    InspectionLogRespVO convert(InspectionLogDO bean);

    List<InspectionLogRespVO> convertList(List<InspectionLogDO> list);

    PageResult<InspectionLogRespVO> convertPage(PageResult<InspectionLogDO> page);

    List<InspectionLogExcelVO> convertList02(List<InspectionLogDO> list);

    List<InspectionLogDO> convertList03(List<InspectionLogRespVO> inspectionLogs);
}
