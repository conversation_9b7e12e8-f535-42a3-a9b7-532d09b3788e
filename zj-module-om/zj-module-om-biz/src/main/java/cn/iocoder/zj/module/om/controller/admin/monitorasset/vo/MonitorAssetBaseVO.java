package cn.iocoder.zj.module.om.controller.admin.monitorasset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 监控资产 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class MonitorAssetBaseVO {

    @Schema(description = "资产id")
    private String assetId;

    @Schema(description = "资产名称")
    private String assetName;

    @Schema(description = "协议类型")
    private String protocol;

    @Schema(description = "协议端口")
    private Integer protocolProd;

    @Schema(description = "账户类型  密码：custom；   授权凭证：private-key")
    private String certificate;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "资产类型 0：非云平台 1：云平台")
    private Integer assetType;

    @Schema(description = "授权状态")
    private String authorizationType;

    @Schema(description = "授权有效时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime authorizationTime;

    @Schema(description = "用户名称")
    private String username;

    @Schema(description = "账号密码")
    private String password;

    @Schema(description = "主机ip")
    private String hostname;

    @Schema(description = "资产类型")
    private String category;

    @Schema(description = "最后接入时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime lastAccessTime;
}
