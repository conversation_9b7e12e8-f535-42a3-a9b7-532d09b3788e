package cn.iocoder.zj.module.om.convert.patrolinspection;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.om.controller.admin.inspectiondata.vo.InspectionRecordCreateReqVO;
import cn.iocoder.zj.module.om.controller.admin.inspectiondata.vo.InspectionRecordRespVO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspection.InspectionRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 巡检记录 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface InspectionRecordConvert {

    InspectionRecordConvert INSTANCE = Mappers.getMapper(InspectionRecordConvert.class);

    InspectionRecordDO convert(InspectionRecordCreateReqVO bean);

    InspectionRecordRespVO convert(InspectionRecordDO bean);

    List<InspectionRecordRespVO> convertList(List<InspectionRecordDO> list);

    PageResult<InspectionRecordRespVO> convertPage(PageResult<InspectionRecordDO> page);


}
