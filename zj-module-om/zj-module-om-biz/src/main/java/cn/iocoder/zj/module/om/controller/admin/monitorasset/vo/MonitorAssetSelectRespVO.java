package cn.iocoder.zj.module.om.controller.admin.monitorasset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName : MonitorAssetSelectRespVO  //类名
 * @Description : 资产下拉框  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/7/16  11:05
 */
@Data
public class MonitorAssetSelectRespVO {

    @Schema(description = "资产id")
    private String assetId;

    @Schema(description = "资产名称")
    private String assetName;

    @Schema(description = "主机ip")
    private String hostname;

    @Schema(description = "资产类型 0：非云平台 1：云平台")
    private Integer assetType;

}
