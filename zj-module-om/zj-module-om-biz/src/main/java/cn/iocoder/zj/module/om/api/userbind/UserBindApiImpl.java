package cn.iocoder.zj.module.om.api.userbind;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.util.object.BeanUtils;
import cn.iocoder.zj.module.om.api.userbind.dto.UserBindRespVO;
import cn.iocoder.zj.module.om.dal.dataobject.userbind.UserBindDO;
import cn.iocoder.zj.module.om.service.userbind.UserBindService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.zj.module.om.enums.ApiConstants.VERSION;


@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION)
@Validated
public class UserBindApiImpl implements UserBindApi {

    @Resource
    UserBindService userBindService;

    @Override
    public CommonResult<List<UserBindRespVO>> getUserBingList(List<Long> userIds) {
        List<UserBindDO> userBingList = userBindService.getUserBingList(userIds);
        return  CommonResult.success(BeanUtils.toBean(userBingList, UserBindRespVO.class));
    }
}
