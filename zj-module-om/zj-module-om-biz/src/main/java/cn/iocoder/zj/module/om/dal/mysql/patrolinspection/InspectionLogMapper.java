package cn.iocoder.zj.module.om.dal.mysql.patrolinspection;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspection.InspectionLogDO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.om.controller.admin.inspectiondata.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 巡检记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InspectionLogMapper extends BaseMapperX<InspectionLogDO> {

    List<InspectionLogRespVO> selectInspectionLogPage(@Param("mpPage") IPage<InspectionLogRespVO> mpPage,@Param("reqVO")  InspectionLogPageReqVO reqVO);

    default List<InspectionLogDO> selectList(InspectionLogExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<InspectionLogDO>()
                .likeIfPresent(InspectionLogDO::getInspectionName, reqVO.getInspectionName())
                .eqIfPresent(InspectionLogDO::getRecordUuid, reqVO.getRecordUuid())
                .eqIfPresent(InspectionLogDO::getThreshold, reqVO.getThreshold())
                .eqIfPresent(InspectionLogDO::getResult, reqVO.getResult())
                .eqIfPresent(InspectionLogDO::getPlatformId, reqVO.getPlatformId())
                .betweenIfPresent(InspectionLogDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InspectionLogDO::getId));
    }

}
