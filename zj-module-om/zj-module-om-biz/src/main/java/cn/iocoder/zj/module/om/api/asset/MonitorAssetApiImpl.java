package cn.iocoder.zj.module.om.api.asset;


import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.om.api.asset.dto.MonitorAssetDTO;

import cn.iocoder.zj.module.om.api.asset.dto.VncInfoDTO;
import cn.iocoder.zj.module.om.service.assetmanagement.AssetManagementService;
import cn.iocoder.zj.module.om.service.monitorasset.MonitorAssetService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

/**
 * @ClassName : MonitorAssetApiImpl  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/7/16  15:14
 */

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class MonitorAssetApiImpl implements MonitorassetApi {
    @Autowired
    MonitorAssetService monitorAssetService;

    @Resource
    AssetManagementService assetManagementService;


    @Override
    public CommonResult<MonitorAssetDTO> getAssetInfoById(Long id) {
        MonitorAssetDTO monitorAssetDTO =  monitorAssetService.getAssetInfoById(id);
        return CommonResult.success(monitorAssetDTO);
    }

    @Override
    public CommonResult<VncInfoDTO> getVncInfoByUuid(String uuid) {
        return CommonResult.success(assetManagementService.getVncInfo(uuid));
    }

    @Override
    public void updateAssetById(MonitorAssetDTO checkedData) {
        monitorAssetService.updateAssetById(checkedData);
    }

    @Override
    public CommonResult<MonitorAssetDTO> getAssetInfoByIdAndFormId(String monitorId, Long platformId) {
        MonitorAssetDTO monitorAssetDTO =  monitorAssetService.getAssetInfoByIdAndFormId(monitorId,platformId);
        return CommonResult.success(monitorAssetDTO);
    }

    @Override
    public void updateAssetByAssetId(MonitorAssetDTO dto) {
        monitorAssetService.updateAssetByAssetId(dto);
    }

    @Override
    public void updateAsset(Long id, String name) {
        monitorAssetService.updateAsset(id,name);
    }
}
