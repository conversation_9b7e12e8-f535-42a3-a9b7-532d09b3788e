package cn.iocoder.zj.module.om.controller.admin.workorder.vo;

import cn.iocoder.zj.framework.security.core.LoginUser;
import lombok.Data;

@Data
public class WorkOrderAutoCreateReqVo {
    /**
     * 资源uuid
     */
    private String resourceUuid;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 操作的类型
     */
    private String type;
    /**
     * 操作
     */
    private String actions;
    /**
     * 资源名称
     */
    private String resourceName;
    /**
     * 登录用户
     */
    private LoginUser loginUser;
    /**
     * 是否有权限
     */
    private Boolean hasPermission;
    /**
     * 操作的资源类型，operateHost云主机，operateHardware宿主机，operateStorage主存储
     */
    private String operateType;
    /**
     * vmware 设备参数
     */
    private String vms;
}
