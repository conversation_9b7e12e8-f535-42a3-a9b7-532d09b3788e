package cn.iocoder.zj.module.om.service.patrolinspection;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.om.controller.admin.inspectiondata.vo.InspectionRecordCreateReqVO;
import cn.iocoder.zj.module.om.controller.admin.inspectiondata.vo.InspectionRecordPageReqVO;
import cn.iocoder.zj.module.om.convert.patrolinspection.InspectionRecordConvert;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspection.InspectionRecordDO;
import cn.iocoder.zj.module.om.dal.mysql.patrolinspection.InspectionRecordMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.om.enums.ErrorCodeConstants.INSPECTION_RECORD_NOT_EXISTS;

/**
 * 巡检记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InspectionRecordServiceImpl implements InspectionRecordService {

    @Resource
    private InspectionRecordMapper inspectionRecordMapper;
    @Override
    public Long createInspectionRecord(InspectionRecordCreateReqVO createReqVO) {
        // 插入
        InspectionRecordDO inspectionRecord = InspectionRecordConvert.INSTANCE.convert(createReqVO);
        inspectionRecordMapper.insert(inspectionRecord);
        // 返回
        return inspectionRecord.getId();
    }

    @Override
    public void deleteInspectionRecord(Long id) {
        // 校验存在
        validateInspectionRecordExists(id);
        // 删除
        inspectionRecordMapper.deleteById(id);
    }

    private void validateInspectionRecordExists(Long id) {
        if (inspectionRecordMapper.selectById(id) == null) {
            throw exception(INSPECTION_RECORD_NOT_EXISTS);
        }
    }

    @Override
    public InspectionRecordDO getInspectionRecord(Long id) {
        return inspectionRecordMapper.selectById(id);
    }

    @Override
    public List<InspectionRecordDO> getInspectionRecordList(Collection<Long> ids) {
        return inspectionRecordMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<InspectionRecordDO> getInspectionRecordPage(InspectionRecordPageReqVO pageReqVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        pageReqVO.setCreator(loginUser.getId());
        return inspectionRecordMapper.selectPage(pageReqVO);
    }

}
