package cn.iocoder.zj.module.om.service.storage;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.monitor.api.storage.StorageInfoApi;
import cn.iocoder.zj.module.monitor.api.storage.dto.StorageRespCreateReqDTO;
import cn.iocoder.zj.module.om.dal.redis.zstack.ZstackAccessTokenRedisDAO;
import cn.iocoder.zj.module.om.service.zstack.core.ZstackApiConstant;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Validated
public class ZstackStorageServiceImpl implements IZstackStorageService{
    @Resource
    PlatformconfigApi platformconfigApi;

    @Resource
    ZstackAccessTokenRedisDAO zstackAccessTokenRedisDAO;
    @Resource
    StorageInfoApi storageInfoApi;
    @Override
    public CommonResult<Map<String,String>> operateStorage(String uuid, Long platformId, String actions, String state) {
        String token = zstackAccessTokenRedisDAO.get("zstack:" + platformId).getUuid();
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(platformId).getData();
        String url = platform.getUrl() + ZstackApiConstant.PUT_STORAGE_ACTIONS+"/"+uuid+"/actions";
        JSONObject body = new JSONObject();
        JSONObject actionType = new JSONObject();
        if(actions.equals("stateChange")) {
            //主存储状态切换
            actionType.put("stateEvent",state);
            body.put("changePrimaryStorageState", actionType);
        } else if (actions.equals("reconnect")) {
            //主存储重启
            body.put("reconnectPrimaryStorage",actionType);
        }
        HttpResponse resp = HttpRequest.put(url)
                .header(Header.AUTHORIZATION, "OAuth " + token)
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .body(JSONObject.toJSONString(body))
                .execute();
        return updateStorageInfo(resp.body(),token,uuid);
    }
    public CommonResult<Map<String,String>> updateStorageInfo(String respBody, String token, String storageUuid){
        StorageRespCreateReqDTO reqDTO = new StorageRespCreateReqDTO();
        String resultUrl = JSONObject.parseObject(respBody).get("location").toString();
        String body = HttpRequest.get(resultUrl).execute().body();
        //为空是因为操作还在执行中
        while (body.equals("{}")) {
            body= HttpRequest.get(resultUrl).execute().body();
        }
        String errorMsg = "";
        Map<String,String> reuslt = new HashMap<>();
        reuslt.put("success","true");
        reuslt.put("msg",errorMsg);
        if(JSONObject.parseObject(body).get("error")==null){
            JSONObject obj = JSONObject.parseObject(JSONObject.toJSONString(JSONObject.parseObject(body).get("inventory")));
            reqDTO.setUuid(storageUuid);
            reqDTO.setState(obj.getString("state"));
            List<StorageRespCreateReqDTO> reqList = new ArrayList<>();
            reqList.add(reqDTO);
            storageInfoApi.updates(reqList);
            return CommonResult.success(reuslt);
        }else {
            JSONObject obj = JSONObject.parseObject(JSONObject.toJSONString(JSONObject.parseObject(body).get("error")));
            errorMsg = getErrorMsg(obj);
            reuslt.put("success","false");
            reuslt.put("msg",errorMsg);
            return CommonResult.success(reuslt);
        }
    }
    public String getErrorMsg(JSONObject obj){
        String msg = "操作失败！";
        if(jodd.util.StringUtil.isNotBlank(obj.getString("elaboration"))){
            msg = obj.getString("elaboration");
        } else if (jodd.util.StringUtil.isNotBlank(obj.getString("details"))) {
            msg = obj.getString("details");
        } else if (jodd.util.StringUtil.isNotBlank(obj.getString("description"))) {
            msg = obj.getString("description");
        }
        return msg;
    }
}
