package cn.iocoder.zj.module.om.convert.patrolinspectionconfig;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.om.controller.admin.patrolinspectionconfig.vo.PatrolInspectionConfigCreateReqVO;
import cn.iocoder.zj.module.om.controller.admin.patrolinspectionconfig.vo.PatrolInspectionConfigExcelVO;
import cn.iocoder.zj.module.om.controller.admin.patrolinspectionconfig.vo.PatrolInspectionConfigRespVO;
import cn.iocoder.zj.module.om.controller.admin.patrolinspectionconfig.vo.PatrolInspectionConfigUpdateReqVO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspectionconfig.PatrolInspectionConfigDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 巡检设置 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface PatrolInspectionConfigConvert {

    PatrolInspectionConfigConvert INSTANCE = Mappers.getMapper(PatrolInspectionConfigConvert.class);

    PatrolInspectionConfigDO convert(PatrolInspectionConfigCreateReqVO bean);

    PatrolInspectionConfigDO convert(PatrolInspectionConfigUpdateReqVO bean);

    PatrolInspectionConfigRespVO convert(PatrolInspectionConfigDO bean);

    List<PatrolInspectionConfigRespVO> convertList(List<PatrolInspectionConfigDO> list);

    PageResult<PatrolInspectionConfigRespVO> convertPage(PageResult<PatrolInspectionConfigDO> page);

    List<PatrolInspectionConfigExcelVO> convertList02(List<PatrolInspectionConfigDO> list);

}
