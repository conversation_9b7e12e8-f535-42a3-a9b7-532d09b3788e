package cn.iocoder.zj.module.om.dal.dataobject.patrolinspection;

import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;

/**
 * 巡检记录 DO
 *
 * <AUTHOR>
 */
@TableName("om_inspection_record")
@KeySequence("om_inspection_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InspectionRecordDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 巡检结果
     */
    private String result;
    /**
     * uuid
     */
    private String uuid;
    /**
     * 创建人名称
     */
    private String creatorName;

    private Integer hostNum;

    private Integer hardwareNum;

    private Integer storageNum;

    private Integer normalNum;

    private Integer abnormalNum;

    private BigDecimal score;
}
