package cn.iocoder.zj.module.om.Inspection.metric.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.cloud.framework.warehouse.store.realtime.RealTimeDataReader;
import cn.iocoder.zj.module.om.dal.dataobject.patrolabnormaldetail.PatrolAbnormalDetailDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspectionconfig.PatrolInspectionConfigDO;
import cn.iocoder.zj.module.om.enums.PatrolMetricEnum;
import cn.iocoder.zj.module.om.enums.PatrolResourceTypeEnum;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.apache.hertzbeat.common.support.SpringContextHolder;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static cn.iocoder.zj.module.om.Inspection.util.InspectionUtil.convertDurationUnitToChinese;

public class CurrentMetricHandler<T> extends MetricHandler<T> {
    private final PatrolResourceTypeEnum resourceType;
    private final Class<T> resourceClass;
    private final Predicate<T> validResourceFilter;
    private final Function<T, Long> resourceIdGetter;
    private final Function<T, String> resourceNameGetter;
    private final Function<T, Long> platformIdGetter;
    private final Function<T, String> platformNameGetter;
    private final Function<T, String> appGetter;
    private final String metric;
    private PatrolInspectionConfigDO patrolInspectionConfigDO;
    private BigDecimal metricValue;

    public CurrentMetricHandler(final PatrolMetricEnum metricType, final PatrolResourceTypeEnum resourceType,
                                final Class<T> resourceClass, final Predicate<T> validResourceFilter,
                                final Function<T, Long> resourceIdGetter, final Function<T, String> resourceNameGetter,
                                final Function<T, Long> platformIdGetter, final Function<T, String> platformNameGetter,
                                Function<T, String> appGetter, final String metric) {
        super(metricType);
        this.resourceType = resourceType;
        this.resourceClass = resourceClass;
        this.validResourceFilter = validResourceFilter;
        this.resourceIdGetter = resourceIdGetter;
        this.resourceNameGetter = resourceNameGetter;
        this.platformIdGetter = platformIdGetter;
        this.platformNameGetter = platformNameGetter;
        this.appGetter = appGetter;
        this.metric = metric;
    }

    @Override
    public PatrolResourceTypeEnum getResourceType() {
        return resourceType;
    }

    @Override
    protected List<T> filterValidResources(List<?> resourceDataList) {
        List<T> resources = BeanUtil.copyToList(resourceDataList, resourceClass);
        if (validResourceFilter != null) {
            return resources.stream().filter(validResourceFilter).toList();
        }
        return resources;
    }

    @Override
    protected boolean checkResourceAbnormal(T resource, List<PatrolInspectionConfigDO> configList) {
        Long monitorId = resourceIdGetter.apply(resource);
        RealTimeDataReader realTimeDataReader = SpringContextHolder.getBean(RealTimeDataReader.class);
        String app = appGetter.apply(resource);
        String metrics = getMetricsByApp(app);
        if (metrics == null) {
            return false;
        }
        CollectRep.MetricsData storageData = realTimeDataReader.getCurrentMetricsData(monitorId, metrics);
        if (storageData == null) {
            return false;
        }

        // 从fields中找到name等于metric的索引
        int metricIndex = -1;
        List<CollectRep.Field> fields = storageData.getFieldsList();
        for (int i = 0; i < fields.size(); i++) {
            if (metric.equals(fields.get(i).getName())) {
                metricIndex = i;
                break;
            }
        }

        // 如果没有找到对应的metric字段，返回false
        if (metricIndex == -1) {
            return false;
        }

        // 获取valueRows中对应索引的origin值
        List<String> origins = new ArrayList<>();
        List<CollectRep.ValueRow> valueRows = storageData.getValuesList();
        for (CollectRep.ValueRow valueRow : valueRows) {
            origins.add(valueRow.getColumns(metricIndex));
        }

        // 如果没有获取到任何origin值，返回false
        if (origins.isEmpty()) {
            return false;
        }
        if ("uptime".equals(metric)) {
            String uptime = origins.get(0);
            //uptime值格式为  111 days, 13 hours, 58 minutes, 7 seconds 或者 15:58:47up15days
            // 处理 uptime值，提取 days 或 day 前的纯数字字符串
            Pattern pattern = Pattern.compile("(\\d+)\\s*day[s]?|up(\\d+)day[s]?", Pattern.CASE_INSENSITIVE);
            Matcher matcher = pattern.matcher(uptime);
            String daysStr = null;
            if (matcher.find()) {
                daysStr = matcher.group(1) != null ? matcher.group(1) : matcher.group(2);
            }
            if (daysStr == null) {
                return false;
            }
            metricValue = new BigDecimal(daysStr);

        } else {
            metricValue = new BigDecimal(origins.get(0));
        }

        for (PatrolInspectionConfigDO config : configList) {
            String operator = config.getOperator();
            BigDecimal thresholdValue = config.getThresholdValue();
            BigDecimal thresholdValueMax = config.getThresholdValueMax();
            if (">=".equals(operator) && metricValue.compareTo(thresholdValue) >= 0) {
                patrolInspectionConfigDO = config;
                return true;
            } else if ("between".equals(operator) && metricValue.compareTo(thresholdValue) >= 0
                    && metricValue.compareTo(thresholdValueMax) < 0) {
                patrolInspectionConfigDO = config;
                return true;
            } else if ("<".equals(operator) && metricValue.compareTo(thresholdValue) < 0) {
                return false;
            }
        }
        return false;
    }

    @Override
    protected PatrolAbnormalDetailDO createAbnormalDetail(T resource, List<PatrolInspectionConfigDO> configList, Long tenantId) {

        if (patrolInspectionConfigDO == null) {
            return null;
        }
        String unit = patrolInspectionConfigDO.getUnit() != null ? patrolInspectionConfigDO.getUnit() : "";
        unit = convertDurationUnitToChinese(unit);
        BigDecimal thresholdValue = patrolInspectionConfigDO.getThresholdValue();
        String riskLevel = patrolInspectionConfigDO.getThresholdType();
        PatrolAbnormalDetailDO abnormalDetail = new PatrolAbnormalDetailDO();
        abnormalDetail.setDetail(getResourceType().getName() + resourceNameGetter.apply(resource)
                + getMetricType().getName() + "为" + metricValue + unit + ",阈值:" + thresholdValue + unit);
        abnormalDetail.setResourceType(getResourceType().getCode());
        abnormalDetail.setMetricName(getMetricType().getCode());
        abnormalDetail.setResourceId(String.valueOf(resourceIdGetter.apply(resource)));
        abnormalDetail.setResourceName(resourceNameGetter.apply(resource));
        abnormalDetail.setPlatformId(platformIdGetter.apply(resource));
        abnormalDetail.setPlatformName(platformNameGetter.apply(resource));
        abnormalDetail.setRiskLevel(riskLevel);
        abnormalDetail.setSuggest(getMetricType().getSuggest().get(riskLevel));
        abnormalDetail.setTenantId(tenantId);
        return abnormalDetail;
    }

    private String getMetricsByApp(String app) {
        return switch (getResourceType().getCode()) {
            case "network", "firewall" -> "uptime".equals(metric) ? "system" : null;
            case "os" -> {
                if ("uptime".equals(metric)) {
                    yield switch (app) {
                        case "linux_snmp", "windows" -> "system";
                        default -> "basic";
                    };
                } else {
                    yield null;
                }
            }
            default -> null;
        };
    }
}
