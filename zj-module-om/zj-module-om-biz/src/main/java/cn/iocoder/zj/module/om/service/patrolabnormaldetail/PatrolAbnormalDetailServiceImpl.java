package cn.iocoder.zj.module.om.service.patrolabnormaldetail;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.om.controller.admin.patrolabnormaldetail.vo.PatrolAbnormalDetailCreateReqVO;
import cn.iocoder.zj.module.om.controller.admin.patrolabnormaldetail.vo.PatrolAbnormalDetailExportReqVO;
import cn.iocoder.zj.module.om.controller.admin.patrolabnormaldetail.vo.PatrolAbnormalDetailPageReqVO;
import cn.iocoder.zj.module.om.controller.admin.patrolabnormaldetail.vo.PatrolAbnormalDetailUpdateReqVO;
import cn.iocoder.zj.module.om.convert.patrolabnormaldetail.PatrolAbnormalDetailConvert;
import cn.iocoder.zj.module.om.dal.dataobject.patrolabnormaldetail.PatrolAbnormalDetailDO;
import cn.iocoder.zj.module.om.dal.mysql.patrolabnormaldetail.PatrolAbnormalDetailMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.om.enums.ErrorCodeConstants.PATROL_ABNORMAL_DETAIL_NOT_EXISTS;

/**
 * 巡检异常明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PatrolAbnormalDetailServiceImpl implements PatrolAbnormalDetailService {

    @Resource
    private PatrolAbnormalDetailMapper patrolAbnormalDetailMapper;

    @Override
    public Long createPatrolAbnormalDetail(PatrolAbnormalDetailCreateReqVO createReqVO) {
        // 插入
        PatrolAbnormalDetailDO patrolAbnormalDetail = PatrolAbnormalDetailConvert.INSTANCE.convert(createReqVO);
        patrolAbnormalDetailMapper.insert(patrolAbnormalDetail);
        // 返回
        return patrolAbnormalDetail.getId();
    }

    @Override
    public void updatePatrolAbnormalDetail(PatrolAbnormalDetailUpdateReqVO updateReqVO) {
        // 校验存在
        validatePatrolAbnormalDetailExists(updateReqVO.getId());
        // 更新
        PatrolAbnormalDetailDO updateObj = PatrolAbnormalDetailConvert.INSTANCE.convert(updateReqVO);
        patrolAbnormalDetailMapper.updateById(updateObj);
    }

    @Override
    public void deletePatrolAbnormalDetail(Long id) {
        // 校验存在
        validatePatrolAbnormalDetailExists(id);
        // 删除
        patrolAbnormalDetailMapper.deleteById(id);
    }

    private void validatePatrolAbnormalDetailExists(Long id) {
        if (patrolAbnormalDetailMapper.selectById(id) == null) {
            throw exception(PATROL_ABNORMAL_DETAIL_NOT_EXISTS);
        }
    }

    @Override
    public PatrolAbnormalDetailDO getPatrolAbnormalDetail(Long id) {
        return patrolAbnormalDetailMapper.selectById(id);
    }

    @Override
    public List<PatrolAbnormalDetailDO> getPatrolAbnormalDetailList(Collection<Long> ids) {
        return patrolAbnormalDetailMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<PatrolAbnormalDetailDO> getPatrolAbnormalDetailPage(PatrolAbnormalDetailPageReqVO pageReqVO) {
        return patrolAbnormalDetailMapper.selectPage(pageReqVO);
    }

    @Override
    public List<PatrolAbnormalDetailDO> getPatrolAbnormalDetailList(PatrolAbnormalDetailExportReqVO exportReqVO) {
        return patrolAbnormalDetailMapper.selectList(exportReqVO);
    }

}
