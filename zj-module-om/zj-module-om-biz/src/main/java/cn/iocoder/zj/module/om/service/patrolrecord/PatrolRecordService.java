package cn.iocoder.zj.module.om.service.patrolrecord;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.om.controller.admin.patrolrecord.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.patrolrecord.PatrolRecordDO;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 巡检记录 Service 接口
 *
 * <AUTHOR>
 */
public interface PatrolRecordService {

    /**
     * 创建巡检记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPatrolRecord(@Valid PatrolRecordCreateReqVO createReqVO);

    /**
     * 更新巡检记录
     *
     * @param updateReqVO 更新信息
     */
    void updatePatrolRecord(@Valid PatrolRecordUpdateReqVO updateReqVO);

    /**
     * 删除巡检记录
     *
     * @param id 编号
     */
    void deletePatrolRecord(Long id);

    /**
     * 获得巡检记录
     *
     * @param id 编号
     * @return 巡检记录
     */
    PatrolRecordDO getPatrolRecord(Long id);

    /**
     * 获得巡检记录列表
     *
     * @param ids 编号
     * @return 巡检记录列表
     */
    List<PatrolRecordDO> getPatrolRecordList(Collection<Long> ids);

    /**
     * 获得巡检记录分页
     *
     * @param pageReqVO 分页查询
     * @return 巡检记录分页
     */
    PageResult<PatrolRecordRespVO> getPatrolRecordPage(PatrolRecordPageReqVO pageReqVO);

    /**
     * 获得巡检记录列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 巡检记录列表
     */
    List<PatrolRecordDO> getPatrolRecordList(PatrolRecordExportReqVO exportReqVO);


    /**
     * 巡检报告下载
     */
    void uploadWord(Long id, HttpServletResponse response);

}
