package cn.iocoder.zj.module.om.dal.redis.zstack;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.module.om.dal.dataobject.zstack.ZstackLoginInfo;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.zj.module.om.dal.redis.RedisKeyConstants.VMWARE_ACCESS_TOKEN;


@Repository
public class VmwareAccessTokenRedisDAO {
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    public ZstackLoginInfo get(String accessToken) {
        String redisKey = formatKey(accessToken);
        return JsonUtils.parseObject(stringRedisTemplate.opsForValue().get(redisKey), ZstackLoginInfo.class);
    }

    public void set(String accessToken, ZstackLoginInfo zstackLoginInfo) {
        String redisKey = formatKey(accessToken);
        long time = LocalDateTimeUtil.between(zstackLoginInfo.getCreateDate(), zstackLoginInfo.getExpiredDate(), ChronoUnit.SECONDS);
        stringRedisTemplate.opsForValue().set(redisKey, JsonUtils.toJsonString(zstackLoginInfo), time, TimeUnit.SECONDS);
    }
    private static String formatKey(String accessToken) {
        return String.format(VMWARE_ACCESS_TOKEN, accessToken);
    }
}
