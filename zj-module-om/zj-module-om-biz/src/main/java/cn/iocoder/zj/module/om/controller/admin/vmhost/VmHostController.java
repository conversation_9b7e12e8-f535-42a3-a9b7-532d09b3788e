package cn.iocoder.zj.module.om.controller.admin.vmhost;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.om.controller.admin.vmhost.vo.VmHostCreateReqVo;
import cn.iocoder.zj.module.om.controller.admin.workorder.vo.WorkOrderCreateReqVO;
import cn.iocoder.zj.module.om.service.vmhost.VmHostService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 云主机操作")
@RestController
@RequestMapping("/om/vm-host")
@Validated
public class VmHostController {
    @Resource
    VmHostService vmHostService;


    @PostMapping("/create")
    @Operation(summary = "创建云主机")
    @PreAuthorize("@ss.hasPermission('om:work-order:create')")
    public CommonResult<Long> createWorkOrder(@Valid @RequestBody VmHostCreateReqVo createReqVO) {
        return success(vmHostService.createVmHost(createReqVO));
    }
}
