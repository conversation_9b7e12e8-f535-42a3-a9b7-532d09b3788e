package cn.iocoder.zj.module.om.controller.admin.patrolplan.vo;

import cn.iocoder.zj.module.om.controller.admin.patrolinspectionconfig.vo.PatrolInspectionConfigCreateReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 巡检计划更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PatrolPlanUpdateReqVO extends PatrolPlanBaseVO {

    @Schema(description = "主键ID", required = true)
    @NotNull(message = "主键ID不能为空")
    private Long id;

    @Schema(description = "巡检设置列表", required = true)
    @NotNull(message = "巡检设置列表不能为空")
    private List<PatrolInspectionConfigCreateReqVO> patrolInspectionConfigCreateReqVOS;

}
