package cn.iocoder.zj.module.om.convert.inspectionlogs;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.zj.module.om.controller.admin.inspectionlogs.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.inspectionlogs.InspectionLogsDO;

/**
 * 自动巡检异常记录 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface InspectionLogsConvert {

    InspectionLogsConvert INSTANCE = Mappers.getMapper(InspectionLogsConvert.class);

    InspectionLogsDO convert(InspectionLogsCreateReqVO bean);

    InspectionLogsDO convert(InspectionLogsUpdateReqVO bean);

    InspectionLogsRespVO convert(InspectionLogsDO bean);

    List<InspectionLogsRespVO> convertList(List<InspectionLogsDO> list);

    PageResult<InspectionLogsRespVO> convertPage(PageResult<InspectionLogsDO> page);

    List<InspectionLogsExcelVO> convertList02(List<InspectionLogsDO> list);

}
