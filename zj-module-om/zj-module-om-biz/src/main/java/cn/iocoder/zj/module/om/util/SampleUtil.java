package cn.iocoder.zj.module.om.util;

import java.net.MalformedURLException;
import java.net.URL;
import java.rmi.RemoteException;

import com.vmware.vim25.mo.ServiceInstance;

public class SampleUtil {

	public static ServiceInstance createServiceInstance(String url,String userName,String password) throws RemoteException, MalformedURLException {
	   ServiceInstance si = new ServiceInstance(new URL(url+"/sdk"), userName, password, true);
	   si.getSessionManager().setLocale("zh-CN");
	   return si;
	}

}
