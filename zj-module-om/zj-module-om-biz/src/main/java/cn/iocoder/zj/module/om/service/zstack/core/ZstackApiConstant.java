package cn.iocoder.zj.module.om.service.zstack.core;

/**
 * @ClassName : ZstackApiConstant  //类名
 * @Description : API相关接口  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/29  15:53
 */
public class ZstackApiConstant {

    /**
     * 云主机相关
     */
    public static final String DEFAULE_DOMAIN = "http://**************:8080";
    public static final String ZSTACK_API_PREFIX = "/zstack/v1";
    public static final String POST_ZSTACK_VM_INSTANCES = ZSTACK_API_PREFIX + "/vm-instances";
    public static final String GET_ZSTACK_VM_INSTANCES = ZSTACK_API_PREFIX + "/vm-instances";
    public static final String GET_ZSTACK_CLUSTERS = ZSTACK_API_PREFIX + "/clusters";
    public static final String GET_ZSTACK_DISK_OFFERINGS = ZSTACK_API_PREFIX + "/disk-offerings";
    public static final String GET_ZSTACK_IMAGES = ZSTACK_API_PREFIX + "/images";
    public static final String GET_ZSTACK_INSTANCE_OFFERINGS = ZSTACK_API_PREFIX + "/instance-offerings";
    public static final String POST_ZSTACK_CREATE_VM = ZSTACK_API_PREFIX + "/vm-instances";
    public static final String PUT_VM_ACTIONS = ZSTACK_API_PREFIX + "/vm-instances";
    public static final String GET_CONSOLES = ZSTACK_API_PREFIX + "/consoles";
    public static final String GET_VNC_CONSOLES = "/api/api/instances/action/";
    /**
     * 宿主机相关
     */
    public static final String PUT_HARDWARE_ACTIONS = ZSTACK_API_PREFIX + "/hosts";
    public static final String GET_ZSTACK_HOSTS = ZSTACK_API_PREFIX + "/hosts";
    /**
     * 主存储相关
     */
    public static final String PUT_STORAGE_ACTIONS = ZSTACK_API_PREFIX + "/primary-storage";
    public static final String POST_ZSTACK_CREATE_HARDWARE = ZSTACK_API_PREFIX +"/hosts/kvm";

}
