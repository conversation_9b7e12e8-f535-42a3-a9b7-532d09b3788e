package cn.iocoder.zj.module.om.controller.admin.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.Size;
import java.util.Date;

@Schema(description = "管理后台 - 工单管理数据创建 Request VO")
@Data
@ToString(callSuper = true)
public class WorkOrderCreateReqVO {
    @Schema(description = "名称")
    @Size(max = 100, message = "工单名称长度为100个字符")
    private String name;

    @Schema(description = "平台名称）")
    private String platformName;

    @Schema(description = "平台ID")
    private Long platformId;

    @Schema(description = "资源名称")
    private String sourceName;

    @Schema(description = "资源uuid")
    private String sourceUuid;

    @Schema(description = "事件说明")
    private String description;

    @Schema(description = "审批人ID")
    private Long auditorId;

    @Schema(description = "审批人名称")
    private String auditorName;

    @Schema(description = "实施人ID")
    private String enforcerId;

    @Schema(description = "实施人名称")
    private String enforcerName;

    @Schema(description = "开始实施时间")
    private Date enforcerStartTime;

    @Schema(description = "结束实施时间")
    private Date enforcerEndTime;

    @Schema(description = "资源类型对应的字典编码")
    private String dictCode;

    @Schema(description = "资源类型对应的字典名称")
    private String dictName;

    @Schema(description = "工单类型对应的字典编码")
    private String typeCode;

    @Schema(description = "工单类型对应的字典名称")
    private String typeName;

    @Schema(description = "云平台告警id")
    private String alarmId;

    @Schema(description = "资产告警id")
    private String gatherId;

    @Schema(description = "合同id")
    private String contractId;

    @Schema(description = "实施时间段")
    private Date[] enforcerTime;

    @Schema(description = "创建人id")
    private Long creator;

    @Schema(description = "创建人名称")
    private String creatorName;

    @Schema(description = "创建类型：auto自动创建，hand手动创建")
    private String createType;

    @Schema(description = "自动创建时的执行参数")
    private String param;

    @Schema(description = "操作,不传，operateHost操作云主机，operateHardware操作宿主机，operateStorage操作主存储")
    private String operation;

    @Schema(description = "审批状态，consent同意，reject驳回，wait待审核")
    private String ratifyStatus;
}
