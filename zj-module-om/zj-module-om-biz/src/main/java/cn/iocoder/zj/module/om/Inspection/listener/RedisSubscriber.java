package cn.iocoder.zj.module.om.Inspection.listener;

/**
 * <AUTHOR>
 **/
import cn.iocoder.zj.module.om.Inspection.job.model.TaskServiceInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
@Component
public class RedisSubscriber implements MessageListener {

    @Resource
    private TaskServiceInfo taskServiceInfo;


    @Override
    public void onMessage(Message message, byte[] pattern) {

        String channel = new String(message.getChannel());
        String messageBody = new String(message.getBody());
        log.info("Received message: {} from channel: {}", messageBody, channel);
        taskServiceInfo.removeTask(Long.parseLong(messageBody));
    }
}

