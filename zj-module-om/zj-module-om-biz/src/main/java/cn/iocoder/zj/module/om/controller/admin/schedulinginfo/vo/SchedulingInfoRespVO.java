package cn.iocoder.zj.module.om.controller.admin.schedulinginfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;

@Schema(description = "管理后台 - 运维排班信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SchedulingInfoRespVO extends SchedulingInfoBaseVO {

    @Schema(description = "主键id", required = true)
    private Long id;

    @Schema(description = "创建时间", required = true)
    private Date createTime;

    private String start;
    private String end;

}
