package cn.iocoder.zj.module.om.service.patrolplan;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.om.Inspection.job.model.LulTaskModel;
import cn.iocoder.zj.module.om.Inspection.job.model.TaskServiceInfo;
import cn.iocoder.zj.module.om.Inspection.job.task.LulTaskService;
import cn.iocoder.zj.module.om.Inspection.listener.RedisPublisher;
import cn.iocoder.zj.module.om.Inspection.service.PatrolMetricService;
import cn.iocoder.zj.module.om.controller.admin.patrolinspectionconfig.vo.PatrolInspectionConfigCreateReqVO;
import cn.iocoder.zj.module.om.controller.admin.patrolplan.vo.*;
import cn.iocoder.zj.module.om.convert.patrolinspectionconfig.PatrolInspectionConfigConvert;
import cn.iocoder.zj.module.om.convert.patrolplan.PatrolPlanConvert;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspectionconfig.PatrolInspectionConfigDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolplan.PatrolPlanDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolplan.PatrolPlanDTO;
import cn.iocoder.zj.module.om.dal.mysql.patrolinspectionconfig.PatrolInspectionConfigMapper;
import cn.iocoder.zj.module.om.dal.mysql.patrolplan.PatrolPlanMapper;
import cn.iocoder.zj.module.om.dal.mysql.patrolrecord.PatrolRecordMapper;
import cn.iocoder.zj.module.om.util.CronExpressionUtil;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import cn.iocoder.zj.module.system.api.tenant.TenantApi;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.om.enums.ErrorCodeConstants.PATROL_PLAN_NOT_EXISTS;
import static cn.iocoder.zj.module.om.util.CronExpressionUtil.getNextExecutionTime;

/**
 * 巡检计划 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PatrolPlanServiceImpl implements PatrolPlanService {

    @Resource
    private PatrolPlanMapper patrolPlanMapper;

    @Autowired
    private LulTaskService lulTaskService;

    @Resource
    private PatrolInspectionConfigMapper patrolInspectionConfigMapper;

    @Autowired
    @Lazy
    private PatrolMetricService patrolMetricService;

    @Resource
    private PatrolRecordMapper patrolRecordMapper;

    @Resource
    private TaskServiceInfo taskServiceInfo;

    @Resource
    private RedisPublisher redisPublisher;

    @Resource
    private TenantApi tenantApi;

    @Resource
    private PlatformconfigApi platformconfigApi;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createPatrolPlan(PatrolPlanCreateReqVO createReqVO) {
        //获取当前租户ID
        Long tenantId = SecurityFrameworkUtils.getLoginUser().getTenantId();
        if (ObjectUtil.isEmpty(createReqVO.getTenantId())) {
            createReqVO.setTenantId(tenantId);
        }
        // 插入
        PatrolPlanDO patrolPlan = PatrolPlanConvert.INSTANCE.convert(createReqVO);
        patrolPlan.setTenantId(tenantId);
        patrolPlan.setSysSettingTenant(createReqVO.getTenantId());
        patrolPlan.setPlatformIds(createReqVO.getPlatformIds() != null ? createReqVO.getPlatformIds() : "");
        //根据巡检周期和执行日和执行时间生成cron表达式
        switch (patrolPlan.getPeriodType()) {
            case "day":
                patrolPlan.setExecutionCron(CronExpressionUtil
                        .generateDailyCronExpression(patrolPlan.getExecutionTime()));
                break;
            case "week":
                patrolPlan.setExecutionCron(CronExpressionUtil
                        .generateWeeklyCronExpression(patrolPlan.getExecutionTime(),
                                Integer.parseInt(patrolPlan.getExecutionDay())));
                break;
            case "month":
                patrolPlan.setExecutionCron(CronExpressionUtil
                        .generateMonthlyCronExpression(patrolPlan.getExecutionTime(),
                                Integer.parseInt(patrolPlan.getExecutionDay())));
        }
        Date date = new Date();
        Date nextExecutionTime = CronExpressionUtil
                .getNextExecutionTime(patrolPlan.getExecutionCron(), date);
        patrolPlan.setNextExecutionTime(nextExecutionTime);
        //插入job表
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        LulTaskModel lulTaskModel = LulTaskModel
                .builder()
                .jobName(patrolPlan.getName() + "-" + sdf.format(date))
                .cronExpression(patrolPlan.getExecutionCron())
                .beanName("cn.iocoder.zj.module.om.job.Inspection.task.LulTaskService")
                .methodName("processTask")
                .status(1)
                .build();
        lulTaskService.addTask(lulTaskModel);
        taskServiceInfo.scheduleTask(lulTaskModel);
        patrolPlan.setJobId(lulTaskModel.getId());
        patrolPlanMapper.insert(patrolPlan);
        //巡检设置项添加planId 插入到巡检设置表中
        List<PatrolInspectionConfigCreateReqVO> configs = createReqVO.getPatrolInspectionConfigCreateReqVOS();
        for (PatrolInspectionConfigCreateReqVO config : configs) {
            PatrolInspectionConfigDO patrolInspectionConfig = PatrolInspectionConfigConvert.INSTANCE.convert(config);
            patrolInspectionConfig.setTenantId(tenantId);
            patrolInspectionConfig.setPlanId(patrolPlan.getId());
            patrolInspectionConfigMapper.insert(patrolInspectionConfig);
        }
        return patrolPlan.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePatrolPlan(PatrolPlanUpdateReqVO updateReqVO) {
        //获取当前租户ID
        Long tenantId = SecurityFrameworkUtils.getLoginUser().getTenantId();
        // 校验存在
        PatrolPlanDO patrolPlanDO = validatePatrolPlanExists(updateReqVO.getId());
        //根据巡检周期和执行日和执行时间生成cron表达式
        switch (updateReqVO.getPeriodType()) {
            case "day":
                updateReqVO.setExecutionCron(CronExpressionUtil
                        .generateDailyCronExpression(updateReqVO.getExecutionTime()));
                break;
            case "week":
                updateReqVO.setExecutionCron(CronExpressionUtil
                        .generateWeeklyCronExpression(updateReqVO.getExecutionTime(),
                                Integer.parseInt(updateReqVO.getExecutionDay())));
                break;
            case "month":
                updateReqVO.setExecutionCron(CronExpressionUtil
                        .generateMonthlyCronExpression(updateReqVO.getExecutionTime(),
                                Integer.parseInt(updateReqVO.getExecutionDay())));
        }
        if (!updateReqVO.getExecutionCron().equals(patrolPlanDO.getExecutionCron())) {
            //需要修改巡检计划表中下次执行时间
            Date nextExecutionTime = CronExpressionUtil
                    .getNextExecutionTime(updateReqVO.getExecutionCron(), new Date());
            updateReqVO.setNextExecutionTime(nextExecutionTime);
        }
        //根据巡检Id获取jobId
        Long jobId = patrolPlanMapper.selectById(updateReqVO.getId()).getJobId();
        //更新job表
        LulTaskModel lulTaskModel = lulTaskService.getTaskByJobId(jobId);
        if (lulTaskModel != null) {
            lulTaskModel.setCronExpression(updateReqVO.getExecutionCron());
        }
        redisPublisher.publish("lul_task", String.valueOf(jobId));
        lulTaskService.updateTasByJobId(lulTaskModel);
//        taskServiceInfo.updateTask(lulTaskModel);
        // 更新
        PatrolPlanDO updateObj = PatrolPlanConvert.INSTANCE.convert(updateReqVO);
        updateObj.setSysSettingTenant(updateReqVO.getTenantId());
        updateObj.setTenantId(tenantId);
        // 设置platformIds，如果为null则设置为空字符串
        updateObj.setPlatformIds(updateReqVO.getPlatformIds() != null ? updateReqVO.getPlatformIds() : "");
        patrolPlanMapper.updateById(updateObj);

        //删除巡检设置项列表
        patrolInspectionConfigMapper.deleteByPlanId(updateReqVO.getId());
        //新的巡检设置插入到巡检设置表中
        List<PatrolInspectionConfigCreateReqVO> configs = updateReqVO.getPatrolInspectionConfigCreateReqVOS();
        for (PatrolInspectionConfigCreateReqVO config : configs) {
            PatrolInspectionConfigDO patrolInspectionConfig = PatrolInspectionConfigConvert.INSTANCE.convert(config);
            patrolInspectionConfig.setTenantId(tenantId);
            patrolInspectionConfig.setPlanId(updateReqVO.getId());
            patrolInspectionConfigMapper.insert(patrolInspectionConfig);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePatrolPlan(Long id) {
        // 校验存在
        PatrolPlanDO patrolPlan = validatePatrolPlanExists(id);
        // 删除
        patrolPlanMapper.deleteById(id);
        //删除 job 表
        lulTaskService.removeTask(LulTaskModel.builder().id(patrolPlan.getJobId()).build());
        redisPublisher.publish("lul_task", String.valueOf(patrolPlan.getJobId()));
        taskServiceInfo.removeTask(patrolPlan.getJobId());
        //删除巡检设置项列表
        patrolInspectionConfigMapper.deleteByPlanId(patrolPlan.getId());
    }

    private PatrolPlanDO validatePatrolPlanExists(Long id) {
        PatrolPlanDO patrolPlan = patrolPlanMapper.selectById(id);
        if (patrolPlan == null) {
            throw exception(PATROL_PLAN_NOT_EXISTS);
        }
        return patrolPlan;
    }

    @Override
    public PatrolPlanRespVO getPatrolPlan(Long id) {
        PatrolPlanDO patrolPlan = patrolPlanMapper.selectById(id);
        PatrolPlanRespVO patrolPlanRespVO = PatrolPlanConvert.INSTANCE.convert(patrolPlan);
        List<PatrolInspectionConfigDO> inspectionConfigDOS = patrolInspectionConfigMapper.selectList(new LambdaQueryWrapper<PatrolInspectionConfigDO>()
                .eq(PatrolInspectionConfigDO::getPlanId, id)
                .eq(PatrolInspectionConfigDO::getDeleted, 0));
        patrolPlanRespVO.setInspectionConfigs(inspectionConfigDOS);
        return patrolPlanRespVO;
    }

    @Override
    public List<PatrolPlanDO> getPatrolPlanList(Collection<Long> ids) {
        return patrolPlanMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<PatrolPlanRespVO> getPatrolPlanPage(PatrolPlanPageReqVO pageReqVO) {
        PageParam pageParam = new PageParam().setPageNo(pageReqVO.getPageNo()).setPageSize(pageReqVO.getPageSize());
        IPage<PatrolPlanDTO> mpPage = MyBatisUtils.buildPage(pageParam);
        List<PatrolPlanRespVO> patrolPlanRespVOS = BeanUtil.copyToList(patrolPlanMapper.selectPatrolPlanPage(mpPage, pageReqVO), PatrolPlanRespVO.class);
        PageResult<PatrolPlanRespVO> pageResult = new PageResult<>(patrolPlanRespVOS, mpPage.getTotal());
        List<PatrolPlanRespVO> patrolPlanDOS = pageResult.getList();
        if(ObjectUtil.isNotEmpty(patrolPlanDOS)){
            //根据ids 获取巡检记录
//            List<Long> ids = patrolPlanDOS.stream().map(PatrolPlanRespVO::getId).toList();
//            List<PatrolRecordDO> patrolRecordDOS = patrolRecordMapper.selectList(new LambdaQueryWrapper<PatrolRecordDO>()
//                    .in(PatrolRecordDO::getPlanId, ids));
            for (PatrolPlanRespVO patrolPlanDO : patrolPlanDOS) {
//                //根据planId 获取巡检记录的数量
//                int count = (int) patrolRecordDOS.stream().filter(patrolRecordDO -> patrolRecordDO.getPlanId().equals(patrolPlanDO.getId())).count();
//                patrolPlanDO.setReportCount(count);

                //根据 sysConfigTenant 获取租户名称
                Long tenantId = patrolPlanDO.getTenantId();
                if (ObjectUtil.isNotEmpty(patrolPlanDO.getSysSettingTenant())) {
                    tenantId = patrolPlanDO.getSysSettingTenant();
                }
                String tenantName = tenantApi.getTenantSimpleInfo(tenantId).getData().getName();
                patrolPlanDO.setTenantName(tenantName);
                //根据 platformIds 获取平台名称
                String platformIds = patrolPlanDO.getPlatformIds();
                if (ObjectUtil.isNotEmpty(platformIds)) {
                    List<Long> platformIdList = Stream.of(platformIds.split(",")).map(Long::parseLong).toList();
                    String platformName = platformconfigApi.getPlatformByIds(platformIdList).getData().stream()
                            .map(PlatformconfigDTO::getName).collect(Collectors.joining(","));
                    patrolPlanDO.setPlatformName(platformName);
                }
            }
            pageResult.setList(patrolPlanDOS);
        }
        return pageResult;
    }

    @Override
    public List<PatrolPlanDO> getPatrolPlanList(PatrolPlanExportReqVO exportReqVO) {
        return patrolPlanMapper.selectList(exportReqVO);
    }

    @Override
    @TenantIgnore
    public PatrolPlanDO getPatrolPlanByJobId(Long jobId) {
        return patrolPlanMapper.selectOne(PatrolPlanDO::getJobId, jobId);
    }

    @Override
    @TenantIgnore
    public void updateLastTimeAndNextTime(PatrolPlanDO patrolPlanDO) {
        Date lastExecutionTime = patrolPlanDO.getLastExecutionTime();
        Date nextExecutionTime = patrolPlanDO.getNextExecutionTime();
        patrolPlanMapper.update(patrolPlanDO, new LambdaUpdateWrapper<PatrolPlanDO>()
                .eq(PatrolPlanDO::getId, patrolPlanDO.getId())
                .set(PatrolPlanDO::getLastExecutionTime, lastExecutionTime)
                .set(PatrolPlanDO::getNextExecutionTime, nextExecutionTime)
        );
    }

    @Override
    @TenantIgnore
    public void executePatrolPlan(Long id) {
        PatrolPlanDO patrolPlan = patrolPlanMapper.selectById(id);
        if (patrolPlan != null) {
            //执行巡检计划
            Date now = new Date();
            String cronExpression = patrolPlan.getExecutionCron();
            Date nextExecutionTime = getNextExecutionTime(cronExpression, now);
            //修改计划任务表中最近执行时间和下次执行时间
            patrolPlan.setLastExecutionTime(now);
            patrolPlan.setNextExecutionTime(nextExecutionTime);
            updateLastTimeAndNextTime(patrolPlan);
            //执行巡检计划
            patrolMetricService.patrolMetric(patrolPlan);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startOrStopPatrolPlan(Long id, int status) {
        //修改状态
        patrolPlanMapper.updateById(PatrolPlanDO.builder().id(id).status(status).build());
        //修改 job 表
        PatrolPlanDO patrolPlan = patrolPlanMapper.selectById(id);
        LulTaskModel lulTaskModel = lulTaskService.getTaskByJobId(patrolPlan.getJobId());
        lulTaskModel.setStatus(status == 0 ? 1 : 0);
        lulTaskService.updateTasByJobId(lulTaskModel);
        if (status == 1) {
            redisPublisher.publish("lul_task", String.valueOf(patrolPlan.getJobId()));
            taskServiceInfo.removeTask(patrolPlan.getJobId());
        }
    }
}
