package cn.iocoder.zj.module.om.controller.admin.patrolresultcategory.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 巡检结果分类 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class PatrolResultCategoryBaseVO {

    @Schema(description = "巡检记录ID", required = true)
    @NotNull(message = "巡检记录ID不能为空")
    private Long recordId;

    @Schema(description = "资源类型(存储资源/宿主机资源/云主机资源/云硬盘资源/等)", required = true)
    @NotNull(message = "资源类型(存储资源/宿主机资源/云主机资源/云硬盘资源/等)不能为空")
    private String resourceType;

    @Schema(description = "指标名称(CPU分配/内存分配/网卡入速度等)", required = true)
    @NotNull(message = "指标名称(CPU分配/内存分配/网卡入速度等)不能为空")
    private String metricName;

    @Schema(description = "巡检资源数", required = true)
    @NotNull(message = "巡检资源数不能为空")
    private Integer resourceCount;

    @Schema(description = "检测结果：0 正常 1 异常", required = true)
    @NotNull(message = "检测结果：0 正常 1 异常不能为空")
    private Integer status;

    @Schema(description = "正常数量")
    private Integer normalCount;

    @Schema(description = "低风险数量")
    private Integer lowRiskCount;

    @Schema(description = "中风险数量")
    private Integer mediumRiskCount;

    @Schema(description = "高风险数量")
    private Integer highRiskCount;

    @Schema(description = "异常数量")
    private Integer abnormalCount;

    @Schema(description = "详情")
    private String detail;

}
