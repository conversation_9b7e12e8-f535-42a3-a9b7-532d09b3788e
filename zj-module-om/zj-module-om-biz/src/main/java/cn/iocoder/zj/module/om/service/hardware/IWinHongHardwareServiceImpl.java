package cn.iocoder.zj.module.om.service.hardware;

import cn.hutool.core.convert.Convert;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import cn.iocoder.cloud.module.guacamole.api.OnlineSessionApi;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.monitor.api.hardware.HardWareInfoApi;
import cn.iocoder.zj.module.monitor.api.hardware.dto.HardWareRespCreateReqDTO;
import cn.iocoder.zj.module.monitor.api.hostinfo.dto.HostInfoRespCreateReqDTO;
import cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo.ResourceOperateLog;
import cn.iocoder.zj.module.om.controller.admin.vncinfo.vo.VncInfoVo;
import cn.iocoder.zj.module.om.dal.mysql.assetmanagement.AssetManagementMapper;
import cn.iocoder.zj.module.om.dal.redis.zstack.WinHongAccessTokenRedisDAO;
import cn.iocoder.zj.module.om.framework.websocket.WebSocketClient;
import cn.iocoder.zj.module.om.service.zstack.core.WinHongApiConstant;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
@Service
@Validated
@Slf4j
public class IWinHongHardwareServiceImpl implements IWinHongHardwareService{


    @Resource
    PlatformconfigApi platformconfigApi;

    @Resource
    AssetManagementMapper assetManagementMapper;

    @Resource
    WebSocketClient webSocketClient;
    @Resource
    HardWareInfoApi hardWareInfoApi;
    @Resource
    WinHongAccessTokenRedisDAO winHongAccessTokenRedisDAO;
    @Resource
    OnlineSessionApi onlineSessionApi;

    @Override
    public CommonResult<Map<String, String>> operateWinHongHardware(String uuid, Long platformId, String state, String actions) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        HardWareRespCreateReqDTO respDTO = hardWareInfoApi.getByUuid(uuid).getData();
        String op = hardwareOperateConvert(actions,state);
        List<HardWareRespCreateReqDTO> reqList = new ArrayList<>();
        String token = winHongAccessTokenRedisDAO.get("winhong:" + platformId).get("sessionId").toString();
        PlatformconfigDTO platform = platformconfigApi.getByConfigId(platformId).getData();
        String url="";

        JSONObject body = new JSONObject();
        JSONObject actionType = new JSONObject();
        String persistentState = "";
        Map<String, Object> params=new HashMap<>();
        String enState = state;
        if (actions.equals("reconnect")) {
            //重连物理机
            body.put("reconnectHost",actionType);
            persistentState = "Reconnecting";
            url=platform.getUrl() +WinHongApiConstant.PATCH_HOSTS_REBOOT;
            enState="Maintenance";
        }else if (actions.equals("stateChange")){
            if (state.equals("enable")){
                persistentState = "Enabling";
                enState="Enabled";
            }else if (state.equals("disable")){
                url=platform.getUrl() +WinHongApiConstant.PATCH_HOSTS_SHUTDOWN;
                persistentState = "Disabling";
                enState="Disabled";
            }else if (state.equals("maintain")){
                url=platform.getUrl() +WinHongApiConstant.PATCH_HOSTS_MAINTAIN;
                params.put("needShutdownDomains",false);
                persistentState = "Maintaining";
                enState="Maintenance";
            }else if (state.equals("ext_maintain")){
                url=platform.getUrl() +WinHongApiConstant.PATCH_HOSTS_WORK;
                persistentState = "Ext_Maintaining";
                enState="Enabled";
            }
            //物理机启用状态变更
            actionType.put("stateEvent",state);
            body.put("changeHostState",actionType);
        }
        //发送请求
        HttpResponse resp = HttpRequest.patch(url.replace("{hostId}",uuid))
                .body(JSONUtil.toJsonStr(params))
                .cookie("SESSION=" + token)
                .contentType("application/json;charset=UTF-8")
                .execute();
        HardWareRespCreateReqDTO hardwareInfo = hardWareInfoApi.getByUuid(uuid).getData();
        hardwareInfo.setState(persistentState);
        reqList.add(hardwareInfo);
        hardWareInfoApi.updates(reqList);
        //新启线程处理任务执行结果

        String finalEnState = enState;
        Runnable data = (() -> {
            updateHardwareInfo(resp.body(),uuid,platform,actions, state,token,loginUser, finalEnState);
        });

        Thread thread = new Thread(data);
        thread.start();
        Map<String,String> reuslt = new HashMap<>();
        reuslt.put("success","true");
        reuslt.put("msg","正在"+op+"宿主机("+respDTO.getName()+")");
        return CommonResult.success(reuslt);
    }


    private void updateHardwareInfo(String body, String uuid, PlatformconfigDTO platform, String actions, String state, String token, LoginUser loginUser, String enState) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        JSONObject result = new JSONObject();

        // 转换操作
        String op = hardwareOperateConvert(actions, state);
        HardWareRespCreateReqDTO hardwareInfo = hardWareInfoApi.getByUuid(uuid).getData();

        // 提取 taskId
        String taskId = jsonObject.getString("taskId");

        if (taskId != null) {
            final int SUCCESS_STATUS = 3;
            final int FAILURE_STATUS = 4;
            final int POLLING_INTERVAL = 2000; // 轮询间隔 2 秒

            while (true) {
                try {
                    // 发起 HTTP 请求获取任务状态
                    HttpResponse res = HttpRequest.get(platform.getUrl() + WinHongApiConstant.GET_TASK_INFO.replace("{taskId}", taskId))
                            .cookie("SESSION=" + token)
                            .contentType("application/json;charset=UTF-8")
                            .execute();

                    // 检查 HTTP 状态码
                    if (res.getStatus() != 200) {
                        String msg = jsonObject.getString("message");
                        result.put("success", "false");
                        result.put("msg", op + "(" + hardwareInfo.getName() + ")失败，错误信息: " + msg+",请至"+platform.getName()+"查看详情");
                        createOperateLog(hardwareInfo.getName(), uuid, op, "error", "hardware", msg, platform, loginUser);
                        webSocketClient.sendMessage(uuid, result.toJSONString());
                        return;
                    }

                    // 解析响应体
                    JSONObject responseJson = JSONObject.parseObject(res.body());
                    int status = responseJson.getInteger("status");

                    if (status == SUCCESS_STATUS) {
                        // 操作成功
                        result.put("success", "true");
                        result.put("msg", op + "(" + hardwareInfo.getName() + ")成功");

                        // 更新硬件状态
                        hardwareInfo.setState(enState);
                        List<HardWareRespCreateReqDTO> reqList = new ArrayList<>();
                        reqList.add(hardwareInfo);
                        hardWareInfoApi.updates(reqList);

                        // 创建成功的操作日志
                        createOperateLog(hardwareInfo.getName(), uuid, op, "success", "hardware", "", platform, loginUser);

                        // 通过 WebSocket 发送成功消息
                        webSocketClient.sendMessage(uuid, result.toJSONString());
                        return;
                    } else if (status == FAILURE_STATUS) {
                        // 操作失败
                        JSONObject errorObj = responseJson.getJSONObject("error");
                        String errorMsg = getErrorMsg(errorObj);

                        result.put("success", "false");
                        result.put("msg", op + "(" + hardwareInfo.getName() + ")失败，错误信息: " + errorMsg+",请至"+platform.getName()+"查看详情");

                        // 创建失败的操作日志
                        createOperateLog(hardwareInfo.getName(), uuid, op, "error", "hardware", errorMsg, platform, loginUser);

                        // 通过 WebSocket 发送失败消息
                        webSocketClient.sendMessage(uuid, result.toJSONString());
                        return;
                    }

                    // 任务未完成，等待一段时间后继续轮询
                    Thread.sleep(POLLING_INTERVAL);

                } catch (Exception e) {
                    // 捕获所有异常并处理
                    result.put("success", "false");
                    result.put("msg", op + "(" + hardwareInfo.getName() + ")失败，系统错误: " + e.getMessage());
                    createOperateLog(hardwareInfo.getName(), uuid, op, "error", "hardware", "系统错误: " + e.getMessage(), platform, loginUser);
                    webSocketClient.sendMessage(uuid, result.toJSONString());
                    return;
                }
            }
        } else {
            // 如果 taskId 不存在，直接返回失败信息
            String msg = jsonObject.getString("message");
            result.put("success", "false");
            result.put("msg", op + "(" + hardwareInfo.getName() + ")失败，错误信息: " + msg+",请至"+platform.getName()+"查看详情");
            createOperateLog(hardwareInfo.getName(), uuid, op, "error", "hardware", msg, platform, loginUser);
            webSocketClient.sendMessage(uuid, result.toJSONString());
        }
    }


    public String getErrorMsg(JSONObject obj){
        String msg = "操作失败，请检查参数是否正确";
        if(jodd.util.StringUtil.isNotBlank(obj.getString("elaboration"))){
            msg = obj.getString("elaboration");
        } else if (jodd.util.StringUtil.isNotBlank(obj.getString("details"))) {
            msg = obj.getString("details");
        } else if(jodd.util.StringUtil.isNotBlank(obj.getString("description"))){
            msg = obj.getString("description");
        }
        return msg;
    }


    public void createOperateLog(String name, String uuid, String operation, String result, String resourceType, String msg, PlatformconfigDTO platform,LoginUser loginUser) {
        ResourceOperateLog operateLog = new ResourceOperateLog();
        operateLog.setCreator(String.valueOf(loginUser.getId()));
        operateLog.setOperation(operation);
        operateLog.setResourceType(resourceType);
        operateLog.setResult(result);
        operateLog.setResourceUuid(uuid);
        operateLog.setResourceName(name);
        operateLog.setPlatformName(platform.getName());
        operateLog.setPlatformId(platform.getId());
        operateLog.setContent(msg);
        assetManagementMapper.createOperateLog(operateLog);
    }


    public String hardwareOperateConvert(String operation, String state){
        String op = "";
        switch (operation){
            case "reconnect":
                op = "重启宿主机"; break;
            case "create":
                op = "创建宿主机"; break;
            case "stateChange":
                switch (state){
                    case "enable" :
                        op = "启用宿主机"; break;
                    case "disable" :
                        op = "停用宿主机"; break;
                    case "maintain" :
                        op = "进入维护"; break;
                    case "ext_maintain" :
                        op = "退出维护"; break;
                }
                break;
        }
        return op;
    }



    public static String stateToCh(String state){
        if (StringUtils.isEmpty(state)) {
            return "";
        }
        state = StringUtils.lowerCase(state);
        String convertde = "";
        switch (state){
            case "Connecting": convertde = "正在连接";break;
            case "Connected": convertde = "已连接";break;
            case "Disconnected": convertde = "未连接";break;
            case "Enabled": convertde = "启用";break;
            case "Disabled": convertde = "停用";break;
            case "PreMaintenance": convertde = "预维护";break;
            case "Maintenance": convertde = "维护";break;
            case "Created": convertde = "已创建";break;
            case "Starting": convertde = "启动中";break;
            case "Running": convertde = "运行中";break;
            case "Stopping": convertde = "停止中";break;
            case "Stopped": convertde = "已停止";break;
            case "Rebooting": convertde = "重启中";break;
            case "Destroying": convertde = "销毁中";break;
            case "Destroyed": convertde = "已销毁";break;
            case "Expunging": convertde = "删除中";break;
            case "Pausing": convertde = "暂停中";break;
            case "Resuming": convertde = "恢复中";break;
            case "VolumeMigrating": convertde = "卷迁移中";break;
            case "Migrating": convertde = "迁移中";break;
            case "Paused": convertde = "已暂停";break;
            case "Unknown": convertde = "未知";break;
        }
        return convertde;
    }
}
