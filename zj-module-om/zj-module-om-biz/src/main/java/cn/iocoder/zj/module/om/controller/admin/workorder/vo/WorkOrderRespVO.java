package cn.iocoder.zj.module.om.controller.admin.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Schema(description = "管理后台 - 工单管理数据 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WorkOrderRespVO extends WorkOrderBaseVO {

    @Schema(description = "主键ID", required = true)
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

    private Date enforcerStartTime;

    private Date enforcerEndTime;

    @Schema(description = "更新时间", required = true)
    private LocalDateTime updateTime;

    @Schema(description = "操作记录", required = true)
    private List<OrderProcessBaseVo> OrderProcessList;

    @Schema(description = "驳回原因", required = true)
    private String rejectReason;

    @Schema(description = "审批记录", required = true)
    private OrderProcessBaseVo OrderProcessRatify;

    @Schema(description = "完成记录", required = true)
    private OrderProcessBaseVo OrderProcessSolved;
}
