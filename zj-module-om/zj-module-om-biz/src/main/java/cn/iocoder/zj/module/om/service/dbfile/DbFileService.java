package cn.iocoder.zj.module.om.service.dbfile;

import java.util.*;
import javax.validation.*;
import cn.iocoder.zj.module.om.controller.admin.dbfile.vo.*;
import cn.iocoder.zj.module.om.dal.dataobject.dbfile.DbFileDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 配置备份 Service 接口
 *
 * <AUTHOR>
 */
public interface DbFileService {

    /**
     * 创建配置备份
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDbFile(@Valid DbFileCreateReqVO createReqVO);

    /**
     * 更新配置备份
     *
     * @param updateReqVO 更新信息
     */
    void updateDbFile(@Valid DbFileUpdateReqVO updateReqVO);

    /**
     * 删除配置备份
     *
     * @param id 编号
     */
    void deleteDbFile(Long id);

    /**
     * 获得配置备份
     *
     * @param id 编号
     * @return 配置备份
     */
    DbFileDO getDbFile(Long id);

    /**
     * 获得配置备份列表
     *
     * @param ids 编号
     * @return 配置备份列表
     */
    List<DbFileDO> getDbFileList(Collection<Long> ids);

    /**
     * 获得配置备份分页
     *
     * @param pageReqVO 分页查询
     * @return 配置备份分页
     */
    PageResult<DbFileDO> getDbFilePage(DbFilePageReqVO pageReqVO);

    /**
     * 获得配置备份列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 配置备份列表
     */
    List<DbFileDO> getDbFileList(DbFileExportReqVO exportReqVO);

    Boolean dbRestore(String url, String name);

    void updateByName(String name);

    void updateState();

    Long selectByState();
}
