package cn.iocoder.zj.module.om.Inspection.metric.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.zj.module.om.dal.dataobject.patrolabnormaldetail.PatrolAbnormalDetailDO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolinspectionconfig.PatrolInspectionConfigDO;
import cn.iocoder.zj.module.om.enums.PatrolMetricEnum;
import cn.iocoder.zj.module.om.enums.PatrolResourceTypeEnum;

import java.math.BigDecimal;
import java.util.List;
import java.util.function.Function;
import java.util.function.Predicate;

import static cn.iocoder.zj.module.om.Inspection.util.InspectionUtil.convertDurationUnitToChinese;

/**
 * 阈值类指标处理器
 * 用于处理需要与阈值比较的指标
 */
public class ThresholdMetricHandler<T> extends MetricHandler<T> {

    private final PatrolResourceTypeEnum resourceType;
    private final Class<T> resourceClass;
    private final Predicate<T> validResourceFilter;
    private final Function<T, BigDecimal> metricValueGetter;
    private final Function<T, String> resourceIdGetter;
    private final Function<T, String> resourceNameGetter;
    private final Function<T, String> platformIdGetter;
    private final Function<T, String> platformNameGetter;
    private final Function<PatrolInspectionConfigDO, String> riskLevelGetter;
    private PatrolInspectionConfigDO patrolInspectionConfigDO;

    public ThresholdMetricHandler(PatrolMetricEnum metricType, PatrolResourceTypeEnum resourceType,
                                  Class<T> resourceClass, Predicate<T> validResourceFilter,
                                  Function<T, BigDecimal> metricValueGetter, Function<T, String> resourceIdGetter,
                                  Function<T, String> resourceNameGetter, Function<T, String> platformIdGetter,
                                  Function<T, String> platformNameGetter, Function<PatrolInspectionConfigDO, String> riskLevelGetter) {
        super(metricType);
        this.resourceType = resourceType;
        this.resourceClass = resourceClass;
        this.validResourceFilter = validResourceFilter;
        this.metricValueGetter = metricValueGetter;
        this.resourceIdGetter = resourceIdGetter;
        this.resourceNameGetter = resourceNameGetter;
        this.platformIdGetter = platformIdGetter;
        this.platformNameGetter = platformNameGetter;
        this.riskLevelGetter = riskLevelGetter;
    }

    @Override
    public PatrolResourceTypeEnum getResourceType() {
        return resourceType;
    }

    @Override
    protected List<T> filterValidResources(List<?> resourceDataList) {
        List<T> resources = BeanUtil.copyToList(resourceDataList, resourceClass);
        if (validResourceFilter != null) {
            return resources.stream().filter(validResourceFilter).toList();
        }
        return resources;
    }

    @Override
    protected boolean checkResourceAbnormal(T resource, List<PatrolInspectionConfigDO> configList) {
        BigDecimal metricValue = metricValueGetter.apply(resource);

        for (PatrolInspectionConfigDO config : configList) {
            String operator = config.getOperator();
            BigDecimal thresholdValue = config.getThresholdValue();
            BigDecimal thresholdValueMax = config.getThresholdValueMax();

            if (">=".equals(operator) && metricValue.compareTo(thresholdValue) >= 0) {
                patrolInspectionConfigDO = config;
                return true;
            } else if ("between".equals(operator) && metricValue.compareTo(thresholdValue) >= 0
                    && metricValue.compareTo(thresholdValueMax) < 0) {
                patrolInspectionConfigDO = config;
                return true;
            } else if ("<".equals(operator) && metricValue.compareTo(thresholdValue) < 0) {
                return false;
            }
        }
        return false;
    }

    @Override
    protected PatrolAbnormalDetailDO createAbnormalDetail(T resource, List<PatrolInspectionConfigDO> configList, Long tenantId) {
        BigDecimal metricValue = metricValueGetter.apply(resource);
        if (patrolInspectionConfigDO == null) {
            return null;
        }

        String unit = patrolInspectionConfigDO.getUnit() != null ? patrolInspectionConfigDO.getUnit() : "";
        unit = convertDurationUnitToChinese(unit);
        BigDecimal thresholdValue = patrolInspectionConfigDO.getThresholdValue();
        String riskLevel = riskLevelGetter.apply(patrolInspectionConfigDO);

        PatrolAbnormalDetailDO abnormalDetail = new PatrolAbnormalDetailDO();
        abnormalDetail.setDetail(getResourceType().getName() + resourceNameGetter.apply(resource)
                + getMetricType().getName() + "为" + metricValue + unit + ",阈值:" + thresholdValue + unit);
        abnormalDetail.setResourceType(getResourceType().getCode());
        abnormalDetail.setMetricName(getMetricType().getCode());
        abnormalDetail.setResourceId(resourceIdGetter.apply(resource));
        abnormalDetail.setResourceName(resourceNameGetter.apply(resource));
        abnormalDetail.setPlatformId(Long.valueOf(platformIdGetter.apply(resource)));
        abnormalDetail.setPlatformName(platformNameGetter.apply(resource));
        abnormalDetail.setRiskLevel(riskLevel);
        abnormalDetail.setSuggest(getMetricType().getSuggest().get(riskLevel));
        abnormalDetail.setTenantId(tenantId);
        return abnormalDetail;
    }
}