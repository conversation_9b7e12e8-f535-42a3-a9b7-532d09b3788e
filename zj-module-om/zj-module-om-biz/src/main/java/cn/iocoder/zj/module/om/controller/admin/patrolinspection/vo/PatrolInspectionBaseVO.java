package cn.iocoder.zj.module.om.controller.admin.patrolinspection.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.apache.poi.hpsf.Decimal;

import java.math.BigDecimal;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 自动巡检规则 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class PatrolInspectionBaseVO {

    @Schema(description = "检测项名称")
    private String name;

    @Schema(description = "检测项类型")
    private String type;

    @Schema(description = "阈值")
    private BigDecimal threshold;

    @Schema(description = "分值")
    private BigDecimal value;

    @Schema(description = "扣分规则")
    private String rule;

}
