package cn.iocoder.zj.module.om.dal.dataobject.patrolinspection;

import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 巡检记录 DO
 *
 * <AUTHOR>
 */
@TableName("om_inspection_data")
@KeySequence("om_inspection_data_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InspectionLogDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 巡检名称
     */
    private String inspectionName;
    /**
     * 阈值
     */
    private BigDecimal threshold;
    /**
     * 巡检记录uuid
     */
    private String recordUuid;
    /**
     * 得分
     */
    private BigDecimal score;
    /**
     * 异常设备uuid
     */
    private String assetUuid;
    /**
     * 异常设备数量
     */
    private Integer assetNum;
    /**
     * 检测结果
     */
    private String result;
    /**
     * 平台id
     */
    private String platformId;

}
