package cn.iocoder.zj.module.om.controller.admin.demo;


import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.system.api.region.RegionApi;
import cn.iocoder.zj.module.system.api.region.dto.RegionDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - Test")
@RestController
@RequestMapping("/om/test")
@Validated
public class DemoTestController {

/*    @Resource
    private RegionApi regionApi;*/


    @GetMapping("/get")
    @Operation(summary = "获取 test 信息")
    @PermitAll
    public CommonResult<String> get() {
        //CommonResult<RegionDTO> dto = regionApi.getRegion("1000000");
        //dto.getData();

        return success("true");
    }


}
