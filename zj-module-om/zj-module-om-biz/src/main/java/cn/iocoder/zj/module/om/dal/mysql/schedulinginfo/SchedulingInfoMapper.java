package cn.iocoder.zj.module.om.dal.mysql.schedulinginfo;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.module.om.dal.dataobject.schedulinginfo.SchedulingInfoDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.zj.module.om.controller.admin.schedulinginfo.vo.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 运维排班信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SchedulingInfoMapper extends BaseMapperX<SchedulingInfoDO> {

    default PageResult<SchedulingInfoDO> selectPage(SchedulingInfoPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SchedulingInfoDO>()
                .eqIfPresent(SchedulingInfoDO::getEnforcerId, reqVO.getEnforcerId())
                .likeIfPresent(SchedulingInfoDO::getEnforcerName, reqVO.getEnforcerName())
                .betweenIfPresent(SchedulingInfoDO::getStartTime, reqVO.getStartTime())
                .betweenIfPresent(SchedulingInfoDO::getEndTime, reqVO.getEndTime())
                .eqIfPresent(SchedulingInfoDO::getRemarks, reqVO.getRemarks())
                .betweenIfPresent(SchedulingInfoDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SchedulingInfoDO::getId));
    }

    default List<SchedulingInfoDO> selectList(SchedulingInfoExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SchedulingInfoDO>()
                .eqIfPresent(SchedulingInfoDO::getEnforcerId, reqVO.getEnforcerId())
                .likeIfPresent(SchedulingInfoDO::getEnforcerName, reqVO.getEnforcerName())
                .betweenIfPresent(SchedulingInfoDO::getStartTime, reqVO.getStartTime())
                .betweenIfPresent(SchedulingInfoDO::getEndTime, reqVO.getEndTime())
                .eqIfPresent(SchedulingInfoDO::getRemarks, reqVO.getRemarks())
                .betweenIfPresent(SchedulingInfoDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SchedulingInfoDO::getId));
    }

    List<SchedulingInfoDO> queryScheduling(@Param("start") String start, @Param("end") String end,@Param("tenantId")Long tenantId);

    default Long getSchedulCount(Long id,String name) {
        return selectCount(new LambdaQueryWrapperX<SchedulingInfoDO>()
                .likeIfPresent(SchedulingInfoDO::getEnforcerId, id.toString())
                .likeIfPresent(SchedulingInfoDO::getEnforcerName, name)
                .ge(SchedulingInfoDO::getEndTime,new Date()));
    }
}
