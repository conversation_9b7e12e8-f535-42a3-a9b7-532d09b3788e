package cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class VmwareCreateVmReqVo {
    @Schema(description = "平台ID",required = true)
    private Long platformId;
    @Schema(description = "操作系统",required = true)
    private String guestOS;
    @Schema(description = "数据存储",required = true)
    private String datastore;
    @Schema(description = "文件夹",required = true)
    private String folder;
    @Schema(description = "虚拟机名称",required = true)
    private String name;
    @Schema(description = "集群",required = true)
    private String cluster;
    @Schema(description = "集群名称",required = true)
    private String clusterName;
    @Schema(description = "cpu核数",required = true)
    private Integer cpuCount;
    @Schema(description = "网络",required = true)
    private String network ;
    @Schema(description = "网络名称",required = true)
    private String networkName ;
    @Schema(description = "网络类型",required = true)
    private String networkType ;
}
