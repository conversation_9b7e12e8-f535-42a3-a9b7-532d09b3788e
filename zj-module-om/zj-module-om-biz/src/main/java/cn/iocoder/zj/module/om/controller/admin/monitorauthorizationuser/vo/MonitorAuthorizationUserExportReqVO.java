package cn.iocoder.zj.module.om.controller.admin.monitorauthorizationuser.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 用户资产授权申请 Excel 导出 Request VO，参数和 MonitorAuthorizationUserPageReqVO 是一致的")
@Data
public class MonitorAuthorizationUserExportReqVO {

    @Schema(description = "授权资产表")
    private Long monitorAssetId;

    @Schema(description = "申请人id")
    private Long authorizationUserId;

    @Schema(description = "授权状态")
    private String authorizationType;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
