package cn.iocoder.zj.module.om.dal.dataobject.monitorauthorization;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 监控申请授权 DO
 *
 * <AUTHOR>
 */
@TableName("om_monitor_authorization")
@KeySequence("om_monitor_authorization_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MonitorAuthorizationDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 资产id
     */
    private String assetId;
    /**
     * 资产名称
     */
    private String assetName;
    /**
     * 申请人
     */
    private String nickName;
    /**
     * 主机地址
     */
    private String hostName;
    /**
     * 授权有效时间
     */
    private LocalDateTime authorizationTime;
    /**
     * 申请人id
     */
    private Long userId;
    /**
     * 监控资产id
     */
    private Long monitorAssetId;

    /**
     * 授权状态
     */
    private String authorizationType;

    private Integer type;
}
