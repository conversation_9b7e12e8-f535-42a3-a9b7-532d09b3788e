package cn.iocoder.zj.module.om.convert.patrolresultcategory;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.om.controller.admin.patrolresultcategory.vo.PatrolResultCategoryCreateReqVO;
import cn.iocoder.zj.module.om.controller.admin.patrolresultcategory.vo.PatrolResultCategoryExcelVO;
import cn.iocoder.zj.module.om.controller.admin.patrolresultcategory.vo.PatrolResultCategoryRespVO;
import cn.iocoder.zj.module.om.controller.admin.patrolresultcategory.vo.PatrolResultCategoryUpdateReqVO;
import cn.iocoder.zj.module.om.dal.dataobject.patrolresultcategory.PatrolResultCategoryDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 巡检结果分类 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface PatrolResultCategoryConvert {

    PatrolResultCategoryConvert INSTANCE = Mappers.getMapper(PatrolResultCategoryConvert.class);

    PatrolResultCategoryDO convert(PatrolResultCategoryCreateReqVO bean);

    PatrolResultCategoryDO convert(PatrolResultCategoryUpdateReqVO bean);

    PatrolResultCategoryRespVO convert(PatrolResultCategoryDO bean);

    List<PatrolResultCategoryRespVO> convertList(List<PatrolResultCategoryDO> list);

    PageResult<PatrolResultCategoryRespVO> convertPage(PageResult<PatrolResultCategoryDO> page);

    List<PatrolResultCategoryExcelVO> convertList02(List<PatrolResultCategoryDO> list);

}
