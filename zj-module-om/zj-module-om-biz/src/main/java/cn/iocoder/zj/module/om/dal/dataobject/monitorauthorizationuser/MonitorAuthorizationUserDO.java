package cn.iocoder.zj.module.om.dal.dataobject.monitorauthorizationuser;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 用户资产授权申请 DO
 *
 * <AUTHOR>
 */
@TableName("om_monitor_authorization_user")
@KeySequence("om_monitor_authorization_user_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MonitorAuthorizationUserDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 授权资产表
     */
    private Long monitorAssetId;
    /**
     * 申请人id
     */
    private Long authorizationUserId;
    /**
     * 授权状态
     */
    private String authorizationType;

}
