package cn.iocoder.zj.module.om.dal.mysql.assetmanagement;

import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo.OperateLogPageReqVO;
import cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo.ResourceOperateLog;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface AssetManagementMapper {

    List<Map<String,Object>> list(@Param("platformList") List<Map> platformList);

    Map<String,Object> getAssetInfo(@Param("platformId")String platformId, @Param("timeLimit")String timeLimit);

    Map<String, Object> getHardwareInfo(@Param("platformId")String platformId, @Param("timeLimit")String timeLimit);

    Map<String, Object> getGatherAssetInfo(@Param("platformId")String platformId, @Param("timeLimit")String timeLimit);

    List<ResourceOperateLog> getOperateLogPage(@Param("mpPage")IPage<ResourceOperateLog> mpPage,
                                               @Param("pageReqVO")OperateLogPageReqVO pageReqVO,
                                               @Param("user")LoginUser loginUser,
                                               @Param("hasPermission")boolean hasPermission);

    void createOperateLog(@Param("operateLog")ResourceOperateLog operateLog);

}
