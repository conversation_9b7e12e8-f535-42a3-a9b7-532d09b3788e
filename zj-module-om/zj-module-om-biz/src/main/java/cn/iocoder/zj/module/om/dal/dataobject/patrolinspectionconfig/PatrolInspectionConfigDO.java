package cn.iocoder.zj.module.om.dal.dataobject.patrolinspectionconfig;

import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * 巡检设置 DO
 *
 * <AUTHOR>
 */
@TableName("om_patrol_inspection_config")
@KeySequence("om_patrol_inspection_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PatrolInspectionConfigDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 巡检计划ID
     */
    private Long planId;

    /**
     * 巡检时间
     */
    private Integer patrolTime;

    /**
     * 资源类型(存储资源/宿主机资源/云主机资源/云硬盘资源)
     */
    private String resourceType;
    /**
     * 指标名称(CPU分配/内存分配/网卡入速度等)
     */
    private String metricName;
    /**
     * 阈值类型(正常/低风险/中风险/高风险/异常)
     */
    private String thresholdType;
    /**
     * 比较运算符(>, <, >=, <=, =, !=, between)',
     */
    private String operator;
    /**
     * 阈值值
     */
    private BigDecimal thresholdValue;
    /**
     * 阈值最大值(用于范围比较)
     */
    private BigDecimal thresholdValueMax;
    /**
     * 单位(%, MB/S, G等)
     */
    private String unit;
    /**
     * 持续时间
     */
    private Integer duration;
    /**
     * 持续时间单位(分钟/小时/天)
     */
    private String durationUnit;

    /**
     * 差值率
     */
    private BigDecimal diffRate;

    /**
     * 差值率运算符
     */
    private String diffRateOperator;

}
