package cn.iocoder.zj.module.om.controller.admin.patrolresultcategory.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - 巡检结果分类 Request VO")
@Data
@ToString(callSuper = true)
public class PatrolResultCategoryReqVO {

    @Schema(description = "巡检记录ID")
    private Long recordId;

    @Schema(description = "检测结果：0 正常 1 异常")
    private Integer status;

}
