<?xml version="1.0" encoding="UTF-8"?>

<properties>
    <property name="alarmInfo">

<![CDATA[
{
  "query": {
     "bool": {
      "filter": [
       {
        "match_all": {}
        },
        {
          "range": {
            "createTime": {
              "from": "now-30m",
              "to": "now"
            }
          }
        },
        {
          "term": {
            "sourceType.keyword":#[sourceType]
          }
        },
        {
          "terms": {
            "uuid.keyword":#[uuid]
          }
        }
      ]
    }
  },
    "size": 0,
    "aggs": {
    "group_by_uuid": {
      "terms": {
        "script": "doc['uuid.keyword'].value",
        "size":100000
      },
      "aggs": {
        "my_top_hits": {
          "top_hits": {
            "sort": [
              {
                "createTime": {
                  "order": "desc"
                }
              }
            ],
            "_source": {
              "includes": [
                "context",
                "sourceName",
                "sourceType",
                "uuid"
              ]
            },
            "size": 1
          }
        }
      }
    }
   }
}
 ]]>
    </property>
    <property name="alarmInfoCount">

        <![CDATA[
{
    "size":0,
    "aggs":{
        "total_count":{
            "value_count":{
                "field":"_id"
            }
        }
    }
}
 ]]>
    </property>
</properties>