<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.om.dal.mysql.monitorauthorization.MonitorAuthorizationMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <delete id="deleteMonitorAuthorizationInfo">
        delete from om_monitor_authorization where id = #{id}
    </delete>

    <update id="updateMonitorAuthorizationInfo">
        UPDATE om_monitor_authorization
        SET deleted = 1,
            authorization_type = #{authorizationType}
        WHERE id = #{id}
    </update>

    <select id="selectByIdInfo"
            resultType="cn.iocoder.zj.module.om.dal.dataobject.monitorauthorization.MonitorAuthorizationDO">
        select * from om_monitor_authorization where id = #{id}
    </select>
</mapper>
