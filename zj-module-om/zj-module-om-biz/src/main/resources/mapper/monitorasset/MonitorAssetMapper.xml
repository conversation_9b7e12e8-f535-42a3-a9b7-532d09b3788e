<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.om.dal.mysql.monitorasset.MonitorAssetMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    
    <select id="getAssetInfoById" resultType="cn.iocoder.zj.module.om.api.asset.dto.MonitorAssetDTO">
        select asset_id,asset_name,platform_id,protocol,protocol_prod,certificate,username,password,hostname,asset_type
        from om_monitor_asset where id = #{id} and deleted = 0;
    </select>

    <select id="getTenantInfo" resultType="java.lang.String">
        select contact_user_id from system_tenant where id = #{tenantId}
    </select>

    <select id="getOpenIdByUserId" resultType="java.lang.String">
        select open_id from system_wechat_binding where user_id = #{userId}
    </select>

    <update id="updateAuthorizationType">
        UPDATE om_monitor_asset
        SET authorization_type = #{authorizationType},
            authorization_time = #{authorizationTime}
        WHERE id = #{id}
    </update>

    <update id="updateAuthType">
        UPDATE monitor_host_info
        SET authorization_type = #{authorizationType},
            authorization_time = #{authorizationTime}
        WHERE id = #{id}
    </update>

    <select id="selectBycount" resultType="java.lang.Long">
        select count(*) count
        from om_monitor_asset where hostname = #{hostName} and platform_id = #{platformId} and deleted = 0;
    </select>

    <select id="getUserListByTenantId" resultType="java.lang.String">
        SELECT ro.userId
        FROM (SELECT urole.user_id userId,
                     role.`code`
              FROM system_user_role urole
                       LEFT JOIN system_role role ON role.id = urole.role_id
                  AND role.tenant_id = #{tenantId}
              WHERE urole.deleted = 0
                AND urole.tenant_id = #{tenantId}
              GROUP BY urole.user_id) ro
        WHERE ro.`code` = 'tenant_admin'
    </select>

    <update id="updateAssetById">
        UPDATE om_monitor_asset
        SET platform_id = #{checkedData.platformId},
            platform_name = #{checkedData.platformName}
        WHERE id = #{checkedData.id}
    </update>

    <select id="selectOneDo" resultType="cn.iocoder.zj.module.om.api.asset.dto.MonitorAssetDTO">
        select id,asset_id,asset_name,platform_id,protocol,protocol_prod,certificate,username,password,hostname,asset_type
        from om_monitor_asset where asset_id = #{monitorId} and platform_id = #{platformId}  and deleted = 0 LIMIT 1;
    </select>

    <update id="updateAssetByAssetId">
        UPDATE om_monitor_asset
        SET deleted = 1
        WHERE platform_id = #{dto.platformId}  and deleted = 0 and asset_id = #{dto.assetId}
    </update>

    <update id="updateAsset">
        UPDATE om_monitor_asset
        SET platform_name = #{name}
        WHERE platform_id = #{id} and deleted = 0
    </update>
</mapper>
