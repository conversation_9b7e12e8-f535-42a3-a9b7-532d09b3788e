<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.om.dal.mysql.patrolrecord.PatrolRecordMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <insert id="insertRecordConfig"
            parameterType="cn.iocoder.zj.module.om.dal.dataobject.patrolrecord.PatrolRecordConfigDO">
        insert into om_patrol_record_config (id, record_id, config_id)
        values (#{id}, #{recordId}, #{configId})
    </insert>

    <select id="selectRecordConfigByRecordId" parameterType="Long"
            resultType="cn.iocoder.zj.module.om.dal.dataobject.patrolrecord.PatrolRecordConfigDO">
        select * from om_patrol_record_config where record_id = #{recordId}
    </select>

</mapper>
