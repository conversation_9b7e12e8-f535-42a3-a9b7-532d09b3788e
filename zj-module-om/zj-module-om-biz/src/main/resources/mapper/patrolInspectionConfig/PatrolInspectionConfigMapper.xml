<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.om.dal.mysql.patrolinspectionconfig.PatrolInspectionConfigMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <delete id="deleteByTenantIdAndPlanIsNull" parameterType="Long">
        UPDATE om_patrol_inspection_config
        SET deleted = 1
        WHERE tenant_id = #{tenantId}
          AND plan_id IS NULL
    </delete>

    <delete id="deleteByPlanId" parameterType="Long">
        UPDATE om_patrol_inspection_config
        SET deleted = 1
        WHERE plan_id = #{planId}
    </delete>
    <select id="selectListByIds"
            resultType="cn.iocoder.zj.module.om.dal.dataobject.patrolinspectionconfig.PatrolInspectionConfigDO">
        select * from om_patrol_inspection_config
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>
