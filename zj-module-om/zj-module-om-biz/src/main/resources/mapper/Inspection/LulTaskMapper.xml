<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.om.dal.mysql.lultask.LulTaskMapper">

    <update id="updateTasByJobId" parameterType="cn.iocoder.zj.module.om.Inspection.job.model.LulTaskModel">
        update timer_task
        <set>
            <if test="jobName != null and jobName != ''">
                job_name = #{jobName},
            </if>
            <if test="cronExpression != null and cronExpression != ''">
                cron_expression = #{cronExpression},
            </if>
            <if test="beanName != null and beanName != ''">
                bean_name = #{beanName},
            </if>
            <if test="methodName != null and methodName != ''">
                method_name = #{methodName},
            </if>
            <if test="jobResult != null and jobResult != ''">
                job_result = #{jobResult},
            </if>
            <if test="createDate != null">
                create_date = #{createDate},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="version != null and version != ''">
                version = version + 1
            </if>
        </set>
        WHERE id = #{id} AND version = #{version}
    </update>
</mapper>
