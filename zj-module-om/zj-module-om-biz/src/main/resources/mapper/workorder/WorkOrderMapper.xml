<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.om.dal.mysql.workorder.WorkOrderMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


<select id="getWorkOrderStatistics" resultType="java.util.Map">
    SELECT
    count(*) total,
    count(if( ratify_status = "solved" ,1,null)) solved,
    count(if( ratify_status ="consent-unresolved" ,1,null)) unresolved,
    count(if( ratify_status ="wait" ,1,null)) `wait`,
    count(if( ratify_status ="reject" ,1,null)) reject,
    count(if( ratify_status ="consent-unresolved" or ratify_status = "solved",1,null)) consent
    FROM
    om_work_order
    WHERE
    deleted = 0
    <if test="hasPermission == false">
        and creator IN
        <foreach collection="userIds" open="(" separator="," close=")" item="list">
            #{list}
        </foreach>
    </if>

    </select>

<select id="getWorkOrderStatisticsInWeek" resultType="java.util.Map">
    SELECT
    COUNT(*) totalInWeek,
    count(IF(ratify_status = "solved", 1, NULL )) done,
    count(IF(ratify_status = "consent-unresolved", 1, NULL )) undone
    FROM
    om_work_order
    WHERE type_code != "contract"
    <if test="hasPermission == 0">
        AND creator IN
        <foreach collection="userIds" open="(" separator="," close=")" item="list">
            #{list}
        </foreach>
    </if>
    AND deleted = 0
    AND DATE_FORMAT( create_time, "%Y-%m-%d" ) >= date_sub( DATE_FORMAT( NOW(), "%Y-%m-%d" ), INTERVAL 5 DAY )
    AND ratify_status NOT IN ( "reject", "wait" )
    </select>
    <select id="listOnDay" resultType="cn.iocoder.zj.module.om.controller.admin.workorder.vo.WorkOrderRespVO" parameterType="com.baomidou.mybatisplus.extension.plugins.pagination.Page">
        SELECT
            *
        FROM
            om_work_order
        WHERE
                DATE_FORMAT( enforcer_start_time, '%Y-%m-%d' )&lt;= #{day,jdbcType=VARCHAR}
          AND DATE_FORMAT( enforcer_end_time, '%Y-%m-%d' ) &gt;= #{day,jdbcType=VARCHAR}
          AND deleted = 0
          AND platform_id IN (
            SELECT
                platform_id
            FROM
                system_platform_tenant
            WHERE
                tenant_id = #{tenantId}
              AND deleted = 0
        )
    </select>

<select id="getWorkerOrderCountBySourceUuid" resultType="java.lang.Integer">
    select count(*) from om_work_order
    where source_uuid = #{uuid} and param like concat("%",#{type},"%")
    and param like concat("%",#{actions},"%") and ratify_status != 'consent-unresolved'
    and deleted = 0
    </select>

<select id="getHomologousUsers" resultType="java.lang.Long">
    SELECT
    id
    FROM
    system_users
    WHERE
    tenant_id = ( SELECT tenant_id FROM system_users WHERE id = #{userId} )
    AND deleted = 0
    <if test="roleCode !='' and roleCode != null">
    AND id IN (
    SELECT
    user_id
    FROM
    system_user_role
    WHERE
    role_id IN ( SELECT id FROM system_role WHERE CODE = 'operation_maintenance')
    and deleted = 0)
    </if>
    </select>

<select id="getCountVal" resultType="java.util.Map">
    SELECT
    count(if( ratify_status = 'solved' ,1,null)) pieDone,
    count(if( ratify_status = 'consent-unresolved' ,1,null)) pieUndone,
    ROUND((count(if( ratify_status = 'solved' ,1,null)) / count(*)) * 100, 2 ) pieDonePercent,
    ROUND((count(if( ratify_status = 'consent-unresolved' ,1,null)) / count(*)) * 100, 2 ) pieUndonePercent
    FROM
    om_work_order
    WHERE
    creator IN
    <foreach collection="userIds" open="(" separator="," close=")" item="list">
        #{list}
    </foreach>
    and deleted = 0
    </select>

<select id="getRanking" resultType="java.util.Map">
    SELECT
    target.enforcer_id,
    users.nickname enforcer_name,
    sum(ratify_status = 'solved') finished,
    sum( target.ratify_status = 'consent-unresolved' ) unfinished
    FROM
    (
    SELECT
    SUBSTRING_INDEX( SUBSTRING_INDEX( a.enforcer_id, ',', b.help_topic_id + 1 ), ',', - 1 ) AS enforcer_id,
    a.dict_code,
    a.ratify_status
    FROM
    om_work_order a
    JOIN mysql.help_topic b ON b.help_topic_id &lt;
    ( LENGTH( a.enforcer_id ) - LENGTH( REPLACE ( a.enforcer_id, ',', '' )) + 1 )
    WHERE
    a.deleted = 0 and ratify_status != 'reject' and ratify_status != 'wait'
    ) target
    LEFT JOIN system_users users ON enforcer_id = users.id
    WHERE
    users.nickname IS NOT NULL
    <if test="hasPermission == false">
        AND users.id IN
        <foreach collection="userIds" open="(" separator="," close=")" item="list">
            #{list}
        </foreach>
    </if>
    GROUP BY
    target.enforcer_id
    ORDER BY
    sum(ratify_status = 'solved') DESC
    LIMIT 5
</select>

    <insert id="addOrderProcessResult">
        insert into om_order_process
                    (work_order_id,process_result,content,creator)
             values (#{orderProcessDO.workOrderId},#{orderProcessDO.processResult},#{orderProcessDO.context},#{orderProcessDO.creator})
    </insert>

    <update id="deleteOrderProcess">
        update om_order_process set deleted = 1
        where work_order_id = #{workOrderId}
    </update>
    <update id="updateIsRead">
        UPDATE om_work_order
        SET is_read = 1
        WHERE id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>

    <select id="getOrderProcessListByWorkOrderId"
            resultType="cn.iocoder.zj.module.om.controller.admin.workorder.vo.OrderProcessBaseVo">
        select pro.*,u.nickname as creatorName from om_order_process pro left join system_users u on pro.creator=u.id
        where work_order_id = #{workOrderId} and pro.deleted = 0
    </select>

    <select id="getPersonWorkOrderInfo" resultType="java.util.Map">
        <if test="role != 'operation_maintenance'">
            SELECT
            if(SUM(o.ratify_status = 'consent-unresolved')is not null ,SUM(o.ratify_status = 'consent-unresolved'),0 ) unfinish,
            if(SUM(o.ratify_status = 'wait')is not null ,SUM(o.ratify_status = 'wait'),0 ) `wait`,
            if(SUM(o.ratify_status = 'reject')is not null ,SUM(o.ratify_status = 'reject'),0 ) reject,
            if(SUM(o.ratify_status = 'solved')is not null ,SUM(o.ratify_status = 'solved'),0 ) finished
<!--            一般成员工单统计-->
            <if test="role == 'user'">
                from om_work_order o
                where o.creator = #{userId}
                and o.deleted = 0
            </if>
<!--            运维管理工单信息统计-->
            <if test="role == 'om_manager'">
                from om_work_order o
                where (o.creator = #{userId}
                  or o.creator in (
                  SELECT id from system_users WHERE tenant_id = (
                  SELECT tenant_id from system_users WHERE id = #{userId}
                    ) and deleted = 0
                  )
                )
                and o.deleted = 0
            </if>
        </if>
<!--        运维人员工单信息统计-->
        <if test="role == 'operation_maintenance'">
            SELECT
            sum(o.unfinish ) as unfinish,
            sum(o.`wait` ) as `wait`,
            sum(o.reject ) as reject,
            sum(o.finished ) as finished
            from(
            SELECT

            if(SUM(o1.ratify_status = 'consent-unresolved')is not null ,SUM(o1.ratify_status = 'consent-unresolved'),0 ) unfinish,
            if(SUM(o1.ratify_status = 'wait')is not null ,SUM(o1.ratify_status = 'wait'),0 ) `wait`,
            if(SUM(o1.ratify_status = 'reject')is not null ,SUM(o1.ratify_status = 'reject'),0 ) reject,
            if(SUM(o1.ratify_status = 'solved')is not null ,SUM(o1.ratify_status = 'solved'),0 ) finished

            from om_work_order o1
            WHERE o1.creator = #{userId}

            UNION ALL

            SELECT
            if(SUM(o2.ratify_status = 'consent-unresolved')is not null ,SUM(o2.ratify_status = 'consent-unresolved'),0 ) unfinish,
            if(SUM(o2.ratify_status = 'wait')is not null ,SUM(o2.ratify_status = 'wait'),0 ) `wait`,
            if(SUM(o2.ratify_status = 'reject')is not null ,SUM(o2.ratify_status = 'reject'),0 ) reject,
            if(SUM(o2.ratify_status = 'solved')is not null ,SUM(o2.ratify_status = 'solved'),0 ) finished
            from (
            SELECT
            a.create_time,
            a.is_finished,
            a. creator,
            a.ratify_status,
            a.deleted,
            substring_index(
            substring_index(
            a.enforcer_id,
            ',',
            b.help_topic_id + 1
            ),
            ',' ,- 1
            ) AS enforcer_id
            FROM
            om_work_order a
            JOIN mysql.help_topic b ON b.help_topic_id &lt; (
            length(a.enforcer_id) - length(
            REPLACE (a.enforcer_id, ',', '')
            ) + 1
            )
            where deleted = 0
            ) o2 WHERE o2.enforcer_id =#{userId} and creator != #{userId}
            ) o
        </if>
    </select>

    <select id="getWorkOrderSourceType" resultType="java.util.Map">
        SELECT
            COUNT(*) total,
            count(IF(dict_code = "host", 1, NULL )) `host`,
            count(IF(dict_code = "hardware", 1, NULL )) hardware,
            count(IF(dict_code = "storage", 1, NULL )) `storage`,
            count(IF( dict_code = "gather_asset", 1, NULL )) `gather_asset`
        FROM
            om_work_order
        WHERE type_code != "contract"
        <if test="hasPermission == false">
            AND creator IN
            <foreach collection="userIds" open="(" separator="," close=")" item="list">
                #{list}
            </foreach>
        </if>
        AND deleted = 0
    </select>

    <select id="getCreateInWeek" resultType="java.util.Map">
        SELECT
            count(create_time) num,
            SUM( CASE ratify_status WHEN 'solved' THEN 1 ELSE 0 END ) solved,
            SUM( CASE ratify_status WHEN 'consent-unresolved' THEN 1 ELSE 0 END ) unresolved,
            DATE_FORMAT(create_time,"%Y-%m-%d") dateStr
        FROM
            om_work_order
        WHERE
            <foreach collection="dateStrList" open="(" separator="or" close=")" item="date">
                DATE_FORMAT(create_time,"%Y-%m-%d") = #{date}
            </foreach>
        <if test="hasPermission == false">
            and creator in
            <foreach collection="userIds" open="(" separator="," close=")" item="list">
                #{list}
            </foreach>
        </if>
        AND deleted = 0
        GROUP BY DATE_FORMAT(create_time,"%Y-%m-%d")
        ORDER BY DATE_FORMAT(create_time,"%Y-%m-%d") ASC
    </select>

    <select id="getLatestRejectReason" resultType="java.lang.String">
        select content from om_order_process where work_order_id = #{id} and process_result = 'reject'
        order by create_time desc limit 1
    </select>

    <select id="getOrderProcessRatifyByWorkOrderId"
            resultType="cn.iocoder.zj.module.om.controller.admin.workorder.vo.OrderProcessBaseVo">
        select pro.*,u.nickname as creatorName from om_order_process pro left join system_users u on pro.creator=u.id
        where work_order_id = #{workOrderId} and pro.deleted = 0 and (process_result = 'reject' or process_result = 'consent-unresolved')
        order by create_time desc limit 1
    </select>

    <select id="getOrderProcessSolvedByWorkOrderId"
            resultType="cn.iocoder.zj.module.om.controller.admin.workorder.vo.OrderProcessBaseVo">
        select pro.*,u.nickname as creatorName from om_order_process pro left join system_users u on pro.creator=u.id
        where work_order_id = #{workOrderId} and pro.deleted = 0 and process_result = 'solved'
        order by create_time desc limit 1
    </select>

    <select id="getWorkOrderPage" resultType="cn.iocoder.zj.module.om.dal.dataobject.workorder.WorkOrderDO">
        SELECT
        owp.id, owp.`name`, owp.source_name, owp.source_uuid, owp.platform_id,
        owp.platform_name, owp.description, owp.`event`, owp.auditor_id,
        owp.auditor_name, owp.enforcer_id, owp.enforcer_name, owp.enforcer_start_time,
        owp.enforcer_end_time, owp.is_finished, owp.ratify_status, owp.create_type,
        owp.param, owp.dict_code, owp.dict_name, owp.type_code, owp.type_name,
        owp.operation, owp.alarm_id, owp.gather_id, owp.contract_id, owp.creator,
        owp.creator_name, owp.create_time, oop.creator as updater, oop.create_time as update_time,owp.is_read
        FROM
        om_work_order owp
        LEFT JOIN (
        SELECT
        MAX( create_time ) create_time,
        creator,
        work_order_id
        FROM
        om_order_process
        WHERE
        process_result = 'update'
        AND create_time
        AND deleted = 0
        GROUP BY
        work_order_id
        ORDER BY
        create_time ASC
        ) oop ON owp.id = oop.work_order_id
        where owp.deleted = 0
        <if test="isTenantAdmin = true and (tenantUsers!=null and tenantUsers.size>0)">
            and owp.creator in
            <foreach collection="tenantUsers" item="user" separator="," open="(" close=")">
                #{user}
            </foreach>
        </if>
        <if test="reqVO.name != null and reqVO.name !=''">
            and owp.name like concat("%",#{reqVO.name},"%")
        </if>
        <if test="reqVO.sourceName != null and reqVO.sourceName !=''">
            and owp.source_name like concat("%",#{reqVO.sourceName},"%")
        </if>
        <if test="reqVO.enforcerName != null and reqVO.enforcerName !=''">
            and owp.enforcer_name like concat("%",#{reqVO.enforcerName},"%")
        </if>
        <if test="reqVO.dictCode != null and reqVO.dictCode !=''">
            and owp.dict_code like concat("%",#{reqVO.dictCode},"%")
        </if>
        <if test="reqVO.typeCode != null and reqVO.typeCode !=''">
            and owp.type_code like concat("%",#{reqVO.typeCode},"%")
        </if>
        <if test="reqVO.creator != null">
            and (owp.creator = #{reqVO.creator} or enforcer_id like concat("%",#{reqVO.creator},"%"))
        </if>
        <if test="reqVO.platformIds != null and reqVO.platformIds.size>0">
            and owp.platform_id in
            <foreach collection="reqVO.platformIds" item="platformId" separator="," open="(" close=")">
                #{platformId}
            </foreach>
        </if>
        <if test="reqVO.startTime != null and reqVO.startTime !='' and reqVO.endTime != null and reqVO.endTime != ''">
            and owp.create_time BETWEEN DATE_FORMAT(#{reqVO.startTime}, '%Y-%m-%d %H:%i:%s')
            and
            DATE_FORMAT(#{reqVO.endTime}, '%Y-%m-%d %H:%i:%s')
        </if>
        <if test="reqVO.sourceName != null and reqVO.sourceName !=''">
            and owp.source_name like concat("%",#{reqVO.sourceName},"%")
        </if>
        <if test="reqVO.ratifyStatus != null and reqVO.ratifyStatus !=''">
            and owp.ratify_status = #{reqVO.ratifyStatus}
        </if>
        GROUP BY owp.id
        order by create_time desc
    </select>

    <select id="getById" resultType="cn.iocoder.zj.module.om.dal.dataobject.workorder.WorkOrderDO">
        SELECT
            owp.id, owp.`name`, owp.source_name, owp.source_uuid, owp.platform_id,
            owp.platform_name, owp.description, owp.`event`, owp.auditor_id,
            owp.auditor_name, owp.enforcer_id, owp.enforcer_name, owp.enforcer_start_time,
            owp.enforcer_end_time, owp.is_finished, owp.ratify_status, owp.create_type,
            owp.param, owp.dict_code, owp.dict_name, owp.type_code, owp.type_name,
            owp.operation, owp.alarm_id, owp.gather_id, owp.contract_id, owp.creator,
            owp.creator_name, owp.create_time, oop.creator as updater, oop.create_time as update_time
        FROM
            om_work_order owp
                LEFT JOIN (
                SELECT
                    MAX( create_time ) create_time,
                    creator,
                    work_order_id
                FROM
                    om_order_process
                WHERE
                    process_result = 'update'
                  AND create_time
                  AND deleted = 0
                GROUP BY
                    work_order_id
                ORDER BY
                    create_time ASC
            ) oop ON owp.id = oop.work_order_id
        where owp.deleted = 0 and owp.id = #{id}
    </select>
    <select id="getUnsolvedWorkOrder"
            resultType="cn.iocoder.zj.module.om.controller.admin.workorder.vo.WorkOrderRespVO">

        SELECT
            *
        FROM
            om_work_order
        WHERE
                platform_id IN ( SELECT platform_id FROM system_platform_tenant WHERE tenant_id = #{user.tenantId} AND deleted = 0 )
          AND deleted = 0
          AND (enforcer_id = #{user.id} or enforcer_id like concat("%,",#{user.id}) or enforcer_id like concat("%,",#{user.id},",%") or enforcer_id like concat(#{user.id},",%"))
          AND ratify_status = 'consent-unresolved'
          AND is_read = 0
        ORDER BY
            create_time DESC
            LIMIT 5
    </select>
</mapper>
