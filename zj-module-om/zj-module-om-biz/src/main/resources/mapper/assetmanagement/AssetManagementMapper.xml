<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.om.dal.mysql.assetmanagement.AssetManagementMapper">

<select id="list" resultType="java.util.Map">
    SELECT
    device_name label,
    device_type type,
    platform_id platformId,
    substring(dict_icon,10) icon
    FROM
    monitor_gather_asset
    WHERE
    device_type IS NOT NULL
    AND
    <foreach collection="platformList" open="(" close=")" item="platform" separator="or">
        platform_id = #{platform.platformId}
    </foreach>
    GROUP BY
    device_type,platform_id
    </select>

<select id="getAssetInfo" resultType="java.util.Map">
    SELECT
    IFNULL(currHost.num,0) hostNum,
    IFNULL(currHardware.num,0) hardwareNum,
    IFNULL(currStorage.num,0) storageNum,
    IFNULL(addHost.num - delHost.num,0) hostRaiseNum,
    IFNULL(addHardware.num - delHardware.num,0) hardRaiseNum,
    IFNULL(addStorage.num - delStorage.num,0) storageRaiseNum
    FROM
    ( SELECT COUNT( DISTINCT(uuid) ) num FROM monitor_host_info WHERE deleted = 0 AND platform_id = #{platformId} AND state != 'Destroyed' ) currHost,
    ( SELECT COUNT( DISTINCT(uuid) ) num FROM monitor_hardware_info WHERE deleted = 0 AND platform_id = #{platformId} ) currHardware,
    ( SELECT COUNT( DISTINCT(uuid) ) num FROM monitor_storage_info WHERE deleted = 0 AND platform_id = #{platformId} ) currStorage,
    ( SELECT COUNT( DISTINCT(uuid) ) num FROM monitor_host_info WHERE deleted = 0 AND platform_id = #{platformId} AND update_time >= #{timeLimit} AND state = 'Destroyed' ) delHost,
    ( SELECT COUNT( DISTINCT(uuid) ) num FROM monitor_host_info WHERE deleted = 0 AND platform_id = #{platformId} AND create_time >= #{timeLimit} ) addHost,
    ( SELECT COUNT( DISTINCT(uuid) ) num FROM monitor_hardware_info WHERE deleted = 1 AND platform_id = #{platformId} AND update_time >= #{timeLimit}) delHardware,
    ( SELECT COUNT( DISTINCT(uuid) ) num FROM monitor_hardware_info WHERE platform_id = #{platformId} AND create_time >= #{timeLimit} ) addHardware,
    ( SELECT COUNT( DISTINCT(uuid) ) num FROM monitor_storage_info WHERE deleted = 1 AND platform_id = #{platformId} AND update_time >= #{timeLimit}) delStorage,
    ( SELECT COUNT( DISTINCT(uuid) ) num FROM monitor_storage_info WHERE platform_id = #{platformId} AND create_time >= #{timeLimit} ) addStorage

</select>

<select id="getHardwareInfo" resultType="java.util.Map">
    SELECT
        IFNULL(hardware.cpuUseableNum, 0 ) cpuUseableNum,
        IFNULL(hardware.totalMemory, 0 ) totalMemory,
        IFNULL(hardware.totalDisk, 0 ) totalDisk,
<!--在用CPU增加数量-->
        IFNULL(( sum( addHardware.total_cpu_capacity-delHardware.total_cpu_capacity)
            - sum(addHardware.available_cpu_capacity-delHardware.available_cpu_capacity )), 0 ) cpuUseableRaiseNum,
<!--总内存增加量-->
        ROUND(IFNULL(sum(addHardware.total_memory_capacity )
                         - sum(delHardware.total_memory_capacity ), 0 )/ POWER( 2, 40 ), 2 ) totalMemoryRaise,
<!--总磁盘内存增长量-->
        ROUND(IFNULL(sum(addHardware.total_disk_capacity )
                         - sum(delHardware.total_disk_capacity ), 0 )/ POWER( 2, 40 ), 2 ) totalDiskRaise
    FROM
        (
            SELECT
                IFNULL( sum( total_cpu_capacity )- sum( available_cpu_capacity ), 0 ) cpuUseableNum,
                IFNULL( ROUND( sum( total_memory_capacity )/ POWER( 2, 40 ), 2 ), 0 ) totalMemory,
                IFNULL( ROUND( sum( total_disk_capacity )/ POWER( 2, 40 ), 2 ), 0 ) totalDisk
            FROM
                monitor_hardware_info
            WHERE
                platform_id = #{platformId}
              AND deleted = 0
        ) hardware,
        ( SELECT * FROM monitor_hardware_info WHERE deleted = 1 AND platform_id =  #{platformId} AND update_time >= #{timeLimit} ) delHardware,
        ( SELECT * FROM monitor_hardware_info WHERE platform_id =  #{platformId} AND create_time >= #{timeLimit} ) addHardware
    </select>

<select id="getGatherAssetInfo" resultType="java.util.Map">
    SELECT
    IFNULL( currLinux.num, 0 ) linuxNum,
    IFNULL( currFirewall.num, 0 ) firewallNum,
    IFNULL( currSwitch.num, 0 ) switchNum,
    IFNULL( addLinux.num - delLinux.num, 0 ) linuxRaiseNum,
    IFNULL( addFirewall.num - delFirewall.num, 0 ) firewallRaiseNum,
    IFNULL( addSwitch.num - delSwitch.num, 0 ) switchRaiseNum
    FROM
    ( SELECT COUNT( id ) num FROM monitor_gather_asset WHERE deleted = 0 AND platform_id =  #{platformId} AND sys_type = 'Liunx' ) currLinux,
    ( SELECT count( id ) num FROM monitor_gather_asset WHERE deleted = 0 AND platform_id =  #{platformId} AND sys_type = 'Firewall' ) currFirewall,
    ( SELECT count( id ) num FROM monitor_gather_asset WHERE deleted = 0 AND platform_id =  #{platformId} AND sys_type = 'Switch' ) currSwitch,
    ( SELECT COUNT( id ) num FROM monitor_gather_asset WHERE deleted = 1 AND platform_id =  #{platformId} AND sys_type = 'Liunx' AND update_time >= #{timeLimit} ) delLinux,
    ( SELECT COUNT( id ) num FROM monitor_gather_asset WHERE deleted = 0 AND platform_id =  #{platformId} AND sys_type = 'Liunx' AND create_time >= #{timeLimit} ) addLinux,
    ( SELECT COUNT( id ) num FROM monitor_gather_asset WHERE deleted = 1 AND platform_id =  #{platformId} AND sys_type = 'Firewall' AND update_time >= #{timeLimit} ) delFirewall,
    ( SELECT COUNT( id ) num FROM monitor_gather_asset WHERE deleted = 0 AND platform_id =  #{platformId} AND sys_type = 'Firewall' AND create_time >= #{timeLimit} ) addFirewall,
    ( SELECT COUNT( id ) num FROM monitor_gather_asset WHERE deleted = 1 AND platform_id =  #{platformId} AND sys_type = 'Switch' AND update_time >= #{timeLimit} ) delSwitch,
    ( SELECT COUNT( id ) num FROM monitor_gather_asset WHERE deleted = 0 AND platform_id =  #{platformId} AND sys_type = 'Switch' AND create_time >= #{timeLimit} ) addSwitch
</select>

<select id="getOperateLogPage"
            resultType="cn.iocoder.zj.module.om.controller.admin.assetmanagement.vo.ResourceOperateLog">
SELECT * FROM om_resource_operate_log
    WHERE
    <if test="hasPermission">
        creator IN (
        SELECT
        id
        FROM
        system_users
        WHERE
        tenant_id = ( SELECT tenant_id FROM system_users WHERE id = #{user.id} AND deleted = 0 LIMIT 1 )
        AND deleted = 0
        )
    </if>
    <if test="!hasPermission">
        creator = #{user.id}
    </if>
    <if test="pageReqVO.resourceName != null and pageReqVO.resourceName !=''">
        and resource_name like concat("%",#{pageReqVO.resourceName},"%")
    </if>
    <if test="pageReqVO.operation != null and pageReqVO.operation !=''">
        and operation like concat("%",#{pageReqVO.operation},"%")
    </if>
    <if test="pageReqVO.result != null and pageReqVO.result !=''">
        and result = #{pageReqVO.result}
    </if>
    <if test="pageReqVO.platformId != null and pageReqVO.platformId !=''">
        and platform_id = #{pageReqVO.platformId}
    </if>
    <if test="pageReqVO.resourceType != null and pageReqVO.resourceType !=''">
        and resource_type = #{pageReqVO.resourceType}
    </if>
    <if test="pageReqVO.resourceUuid != null and pageReqVO.resourceUuid !=''">
        and resource_uuid = #{pageReqVO.resourceUuid}
    </if>
    <if test="pageReqVO.platformName != null and pageReqVO.platformName !=''">
        and platform_name like concat("%",#{pageReqVO.platformName},"%")
    </if>
    <if test="pageReqVO.startTime != '' and pageReqVO.endTime != ''and pageReqVO.startTime != null and pageReqVO.endTime != null">
        and DATE_FORMAT( create_time, "%Y-%m-%d" )  between DATE_FORMAT( #{pageReqVO.startTime}, "%Y-%m-%d" ) and  DATE_FORMAT( #{pageReqVO.endTime}, "%Y-%m-%d" )
    </if>
    order by
    <if test="pageReqVO.sortBy == null or pageReqVO.sortBy =='' or pageReqVO.sortDirection== null or pageReqVO.sortDirection ==''">
        create_time desc
    </if>
    <if test="pageReqVO.sortBy != null and pageReqVO.sortBy !='' and pageReqVO.sortDirection!= null and pageReqVO.sortDirection !=''">
        ${pageReqVO.sortBy} ${pageReqVO.sortDirection}
    </if>
</select>

    <insert id="createOperateLog">
    insert into om_resource_operate_log
        (resource_uuid,resource_name,resource_type,operation,result,content,platform_id,platform_name,creator)
    values (#{operateLog.resourceUuid},#{operateLog.resourceName},#{operateLog.resourceType}
           ,#{operateLog.operation},#{operateLog.result},#{operateLog.content}
           ,#{operateLog.platformId},#{operateLog.platformName},#{operateLog.creator})
</insert>
</mapper>