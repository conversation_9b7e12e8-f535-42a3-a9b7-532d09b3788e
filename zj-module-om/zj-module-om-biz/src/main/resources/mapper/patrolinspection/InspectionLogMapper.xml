<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.om.dal.mysql.patrolinspection.InspectionLogMapper">

    <select id="selectInspectionLogPage"
            resultType="cn.iocoder.zj.module.om.controller.admin.inspectiondata.vo.InspectionLogRespVO">
        SELECT
        oil.record_uuid,oil.asset_name,oil.asset_uuid,oil.platform_id,oil.platform_name,oil.create_time,
        oil.result,opi.asset_type,
        IF(opi.asset_type = "host",(SELECT id FROM monitor_host_info WHERE uuid = oil.asset_uuid limit 1),
        (IF(opi.asset_type = "hardware",(SELECT id FROM monitor_hardware_info WHERE uuid = oil.asset_uuid limit 1)
        ,(SELECT id FROM monitor_storage_info WHERE uuid = oil.asset_uuid limit 1)) ) ) assetId,
        CONCAT_WS(';', GROUP_CONCAT(CONCAT(oil.inspection_name, ':阈值', oil.threshold))) inspectionName
        FROM om_inspection_logs oil
        left join om_patrol_inspection opi on oil.inspection_id = opi.id
        where oil.deleted = 0
        and oil.record_uuid = #{reqVO.recordUuid}
        and oil.result = "异常"
        <if test="reqVO.inspectionName != null and reqVO.inspectionName!= ''">
            oil.inspection_name like concat("%",#{reqVO.inspectionName},"%")
        </if>
        <if test="reqVO.assetName != null and reqVO.assetName!= ''">
            oil.asset_name like concat("%",#{reqVO.assetName},"%")
        </if>
        <if test="reqVO.platformId != null">
            oil.platform_id = #{reqVO.platformId}
        </if>
        GROUP BY oil.asset_uuid
        order by oil.asset_uuid
    </select>
</mapper>
