<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.om.dal.mysql.patrolinspection.PatrolInspectionMapper">

<select id="getLastInspectionRecord"
            resultType="cn.iocoder.zj.module.om.controller.admin.inspectiondata.vo.InspectionRecordRespVO">
    SELECT * from om_inspection_record WHERE creator = #{id} ORDER BY create_time DESC LIMIT 1
    </select>

    <select id="getAssetNum" resultType="java.util.Map">
        SELECT
            count(
                    DISTINCT (`host`.uuid)) hostNum,
            count(
                    DISTINCT ( hardware.uuid )) hardwareNum,
            count(
                    DISTINCT (`storage`.uuid )) storageNum
        FROM
            system_platform_tenant spt
                LEFT JOIN monitor_host_info `host`
                          ON `host`.platform_id = spt.platform_id
                              AND `host`.deleted = 0
                              AND `host`.state != 'Destroyed'
                LEFT JOIN monitor_storage_info `storage`
                          ON `storage`.platform_id = spt.platform_id
                              AND `storage`.deleted = 0
                LEFT JOIN monitor_hardware_info hardware
                          ON hardware.platform_id = spt.platform_id
                              AND hardware.deleted = 0
                              AND hardware.state != 'Disable'
        WHERE
            spt.tenant_id = #{tenantId}
          AND spt.deleted = 0
    </select>
</mapper>
