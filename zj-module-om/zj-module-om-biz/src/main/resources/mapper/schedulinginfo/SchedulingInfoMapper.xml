<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.om.dal.mysql.schedulinginfo.SchedulingInfoMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getOmUsers" resultType="cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO">
        SELECT
        b.*
        FROM
        system_user_role a
        LEFT JOIN system_users b ON a.user_id = b.id
        LEFT JOIN system_role c ON a.role_id = c.id
        WHERE
        c.name = '运维人员'
    </select>

    <select id="getOmUser" resultType="cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO">
        SELECT
        b.*
        FROM
        om_scheduling_info a
        LEFT JOIN system_users b ON a.user_id = b.id
        WHERE
        #{date,jdbcType=VARCHAR} BETWEEN a.start and a.end
        limit 0,1
    </select>
    <select id="queryScheduling"
            resultType="cn.iocoder.zj.module.om.dal.dataobject.schedulinginfo.SchedulingInfoDO">

        SELECT
            *
        FROM
            om_scheduling_info
        WHERE
        (
        ( DATE_FORMAT( start_time, "%Y-%m-%d" ) &gt;= #{start,jdbcType=VARCHAR} AND ( DATE_FORMAT( start_time, "%Y-%m-%d" ) &lt;= #{end,jdbcType=VARCHAR} ) )
        OR ( DATE_FORMAT( end_time, "%Y-%m-%d" )  &gt;= #{start,jdbcType=VARCHAR} AND ( DATE_FORMAT( end_time, "%Y-%m-%d" )  &lt;= #{end,jdbcType=VARCHAR} ) )
        )
        and tenant_id = #{tenantId}
            and deleted =0

    </select>


</mapper>
