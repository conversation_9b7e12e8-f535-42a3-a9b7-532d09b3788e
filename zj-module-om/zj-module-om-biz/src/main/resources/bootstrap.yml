# Tomcat
server:
  port: 48086

# Spring
spring:
  main:
    allow-circular-references: true  # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许 Bean 覆盖，例如说 Dubbo 或者 Feign 等会存在重复定义的服务
  application:
    # 应用名称
    name: om-server
  profiles:
    # 环境配置
    active: prod
  cloud:
    nacos:
      discovery:
        namespace: ${spring.profiles.active} # 命名空间。这里使用 dev 开发环境
        # 服务注册地址
        server-addr: 172.16.110.215:8848
      config:
        # 配置中心地址
        server-addr: 172.16.110.215:8848
        namespace: ${spring.profiles.active} # 命名空间。这里使用 dev 开发环境
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP
        name: ${spring.application.name}-${spring.profiles.active} # 使用的 Nacos 配置集的 dataId，默认为 spring.application.name
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - data-id: application.yml
            group: DEFAULT_GROUP
            refresh: true # 是否自动刷新配置，默认为 false
logging:
  file:
    name: ${user.home}/logs/${spring.application.name}.log # 日志文件名，全路径


db:
  ip: mariadb-production-mariadb-galera
  dbname: zj_cloud_statistics
  password: zjiecn@2023
  username: zjiecn

# Minio配置
minio:
  endpoint: http://s3.yxc.kube.com #Minio服务所在地址
  bucketName: zj-server-cloud #存储桶名称
  accessKey: 7541 #访问的key
  secretKey: 754159929 #访问的秘钥