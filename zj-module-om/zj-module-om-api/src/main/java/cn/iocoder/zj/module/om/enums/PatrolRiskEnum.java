package cn.iocoder.zj.module.om.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 巡检风险等级枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PatrolRiskEnum {

    NORMAL("normal", "正常"),
    LOW("low", "低风险"),
    MEDIUM("medium", "中风险"),
    HIGH("high", "高风险");
//    ABNORMAL("abnormal", "异常");

    /**
     * 风险等级编码
     */
    private final String code;

    /**
     * 风险等级名称
     */
    private final String name;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static PatrolRiskEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (PatrolRiskEnum value : PatrolRiskEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}