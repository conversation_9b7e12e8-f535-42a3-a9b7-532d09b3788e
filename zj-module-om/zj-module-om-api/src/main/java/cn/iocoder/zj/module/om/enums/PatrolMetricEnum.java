package cn.iocoder.zj.module.om.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 巡检指标枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PatrolMetricEnum {

    // 存储资源指标
    ALLOCATE_CAPACITY("allocateCapacity", "分配容量", "云存储分配容量",
            new HashMap<>() {{
                put("low", "建议在生产环境下存储分配率不超过150%，防止数据数据丢失");
            }}, PatrolResourceTypeEnum.STORAGE),

    REAL_CAPACITY("realCapacity", "真实容量", "云存储真实容量",
            new HashMap<>() {{
                put("low", "建议持续关注该云存储的使用情况，定期进行容量评估");
                put("medium", "建议提前规划扩容或优化空间");
                put("high", "建议启动扩容流程，增加云存储的容量，以防止数据丢失导致的服务中断");
            }}, PatrolResourceTypeEnum.STORAGE),

    STORAGE_STATE("storageState", "状态", "云存储状态",
            new HashMap<>() {{
                put("low", "云存储为未运行状态，建议确认运行状态");
            }}, PatrolResourceTypeEnum.STORAGE),



    // 宿主机资源指标
    CLOUD_NUM("cloudNum", "云主机数量", "宿主机上云主机的数量",
            new HashMap<>() {{
                put("low", "建议平衡宿主机上云主机数量，避免负载分布不均导致云主机性能下降");
            }}, PatrolResourceTypeEnum.HOST),

    CPU_ALLOCATE("cpuAllocate", "CPU分配", "宿主机CPU分配",
            new HashMap<>() {{
                put("low", "在生产环境中，建议CPU超分比不超过1:3");
            }}, PatrolResourceTypeEnum.HOST),

    CPU_AVERAGE_LOAD("cpuAverageLoad", "CPU平均负载", "宿主机CPU平均负载",
            new HashMap<>() {{
                put("medium", "建议迁移部分云主机到其他宿主机上或进行宿主机扩容，使资源均衡");
            }}, PatrolResourceTypeEnum.HOST),

    CPU_MAX_LOAD("cpuMaxLoad", "CPU使用率峰值", "宿主机CPU使用率峰值",
            new HashMap<>() {{
                put("medium", "检查到宿主机CPU峰值使用率超过阈值，建议排查是否存在异常进程或进行云主机迁移，避免峰值超限影响业务");
                put("high", "检查到宿主机CPU峰值使用率超过阈值，建议排查是否存在异常进程或进行CPU扩容，避免峰值超限影响业务");
            }}, PatrolResourceTypeEnum.HOST),

    MEMORY_ALLOCATE("memoryAllocate", "内存分配", "宿主机内存分配",
            new HashMap<>() {{
                put("low", "在生产环境中，不建议对宿主机内存进行超分，防止宿主机内存OOM导致业务受到影响");
            }}, PatrolResourceTypeEnum.HOST),

    MEMORY_LOAD("memoryLoad", "内存负载", "宿主机内存负载",
            new HashMap<>() {{
                put("medium", "宿主机内存使用率超过阈值，建议迁移该台宿主机上云主机到其他物理机上或启动扩容计划");
                put("high", "宿主机内存使用率超过阈值，建议立即迁移该台宿主机上云主机到其他物理机上或进行扩容");
            }}, PatrolResourceTypeEnum.HOST),

    HOST_CPU_SATURATION("hostCpuSaturation", "持续性CPU饱和", "宿主机持续性CPU饱和",
            new HashMap<>() {{
                put("low", "检测到宿主机CPU利用率波动异常平缓，建议立即检查系统响应状态，确认是否存在内核僵死（hang）或调度阻塞情况");
            }}, PatrolResourceTypeEnum.HOST),

    HOST_CPU_WAVE("hostCpuWave", "CPU波动", "宿主机CPU波动",
            new HashMap<>() {{
                put("low", "检测到宿主机CPU利用率波动过高，建议检查是否存在异常进程或开通新业务导致");
            }}, PatrolResourceTypeEnum.HOST),

    HOST_MEMORY_WAVE("hostMemoryWave", "内存波动", "宿主机内存波动",
            new HashMap<>() {{
                put("medium", "检查到宿主机内存差值百分比超过阈值，建议排查宿主机是否存在内存OOM或进行新的业务开通导致");
            }}, PatrolResourceTypeEnum.HOST),

    NETWORK_IN_BYTES("networkInBytes", "网卡入速度", "宿主机网卡入速度",
            new HashMap<>() {{
                put("medium", "检查到宿主机入速度流量过高，建议检查该台宿主机上的云主机入速度流量是否正常");
            }}, PatrolResourceTypeEnum.HOST),

    NETWORK_OUT_BYTES("networkOutBytes", "网卡出速度", "宿主机网卡出速度",
            new HashMap<>() {{
                put("medium", "检查到宿主机出速度流量过高，建议检查该台宿主机上的云主机出速度流量是否正常");
            }}, PatrolResourceTypeEnum.HOST),

    HOST_STATE("hostState", "宿主机状态", "宿主机状态",
            new HashMap<>() {{
                put("high", "宿主机就绪状态为未连接，建议确认宿主机就绪状态");
            }}, PatrolResourceTypeEnum.HOST),



    // 云主机资源指标
    CPU_AVERAGE_USAGE("cpuAverageUsage", "CPU平均使用率", "云主机CPU平均使用率",
            new HashMap<>() {{
                put("medium", "建议排查高负载进程是否正常或进行云主机CPU扩容使CPU负载降低");
                put("high", "建议排查高负载进程是否正常或进行云主机CPU扩容使CPU负载降低");
            }}, PatrolResourceTypeEnum.CLOUD),

    CPU_MAX_USAGE("cpuMaxUsage", "CPU使用率峰值", "云主机CPU使用率峰值",
            new HashMap<>() {{
                put("high", "检查到过去一段时间内云主机CPU峰值使用率持续超过阈值，建议排查是否存在异常进程或进行CPU扩容，避免峰值超限影响业务");
            }}, PatrolResourceTypeEnum.CLOUD),

    DISK_WRITE_RATE("diskWriteRate", "磁盘写速度", "云主机磁盘写速度",
            new HashMap<>() {{
                put("medium", "检查到云主机写速度超过阈值，由于过高的磁盘写速度会占用大量的带宽，导致其他客户磁盘读写速度降低或超时，建议进行限速");
            }}, PatrolResourceTypeEnum.CLOUD),

    DISK_READ_RATE("diskReadRate", "磁盘读速度", "云主机磁盘读速度",
            new HashMap<>() {{
                put("medium", "检查到云主机读速度超过阈值，由于过高的磁盘写速度会占用大量的带宽，导致其他客户磁盘读写速度降低或超时，建议进行限速");
            }}, PatrolResourceTypeEnum.CLOUD),

    CLOUD_NET_IN_RATE("cloudNetInRate", "网卡接收速率", "云主机网卡接收速率",
            new HashMap<>() {{
                put("medium", "检查到云主机发送速率过高，建议检查该台云主机流量是否为正常业务流量");
            }}, PatrolResourceTypeEnum.CLOUD),

    CLOUD_NET_OUT_RATE("cloudNetOutRate", "磁盘使用率", "云主机网卡发送速率",
            new HashMap<>() {{
                put("medium", "检查到云主机接收速率过高，建议检查该台云主机流量是否为正常业务流量");
            }}, PatrolResourceTypeEnum.CLOUD),

    CLOUD_SYS_DISK_USAGE("cloudSysDiskUsage", "磁盘使用率", "云主机系统盘使用率",
            new HashMap<>() {{
                put("high", "建议及时清理系统盘无用文件，避免使用率100%导致云主机业务受到影响");
            }}, PatrolResourceTypeEnum.CLOUD),

    CLOUD_STATE("cloudState", "云主机状态", "云主机状态",
            new HashMap<>() {{
                put("low", "云主机为未运行状态，建议确认运行状态");
            }}, PatrolResourceTypeEnum.CLOUD),

    CLOUD_MEMORY_USAGE("cloudMemoryUsage", "内存使用率", "云主机内存平均使用率",
            new HashMap<>() {{
                put("medium", "云主机内存使用率超过阈值，建议检查是否为缓存占用或进行内存扩容");
                put("high", "云主机内存使用率超过阈值，建议检查是否为缓存占用或进行内存扩容");
            }}, PatrolResourceTypeEnum.CLOUD),

    CLOUD_MEMORY_WAVE("cloudMemoryWave", "内存波动", "云主机内存波动",
            new HashMap<>() {{
                put("medium", "检查到云主机内存差值百分比超过阈值，建议排查云主机是否存在内存OOM或进行内存扩容导致");
            }}, PatrolResourceTypeEnum.CLOUD),

    CLOUD_CPU_SATURATION("cloudCpuSaturation", "持续性CPU饱和", "云主机持续性CPU饱和",
            new HashMap<>() {{
                put("low", "检测到云主机CPU利用率波动异常平缓，建议立即检查系统响应状态，确认是否存在内核僵死（hang）或调度阻塞情况");
            }}, PatrolResourceTypeEnum.CLOUD),

    CLOUD_CPU_WAVE("cloudCpuWave", "CPU波动", "云主机CPU波动",
            new HashMap<>() {{
                put("low", "检测到CPU利用率波动异常剧烈，可能导致系统性能下降、能源消耗增加及响应延迟加剧，建议排查线程竞争、资源争用或调度阻塞问题。");
            }}, PatrolResourceTypeEnum.CLOUD),

    CLOUD_SNAPSHOT_NUM("cloudSnapshotNum", "快照数量", "云主机快照数量",
            new HashMap<>() {{
                put("low", "快照数量超过3张，由于差异镜像过多，读取数据会读取所有差异数据，从而影响I/O，建议快照保留在2张。");
            }}, PatrolResourceTypeEnum.CLOUD),

    //互联网暴露面
    INTERNET_EXPOSE_FACE("internetExposeFace", "互联网暴露面", "互联网暴露面",
            new HashMap<>() {{
                put("high", "云主机存在未受控的高危端口对互联网开放问题。为有效阻断暴力破解、横向渗透等风险，建议对高危端口做源地址访问限制");
            }}, PatrolResourceTypeEnum.CLOUD),

    //云主机内存环比
    CLOUD_MEMORY_RATIO("cloudMemoryRatio", "内存环比", "云主机内存环比",
            new HashMap<>() {{
                put("medium", "云主机内存使用率环比增长超过阈值，可能导致系统卡顿、服务响应延迟甚至内存溢出风险，需立即介入处置。");
                put("high", "云主机内存使用率环比增长超过阈值，可能导致系统卡顿、服务响应延迟甚至内存溢出风险，需立即介入处置。");
            }}, PatrolResourceTypeEnum.CLOUD),



    // 云硬盘资源指标
    //挂载状态
    MOUNT_STATE("mountState", "挂载状态", "云硬盘挂载状态",
            new HashMap<>() {{
                put("low", "建议确认云盘状态，选择是否加载或回收磁盘空间");
            }}, PatrolResourceTypeEnum.DISK),
//    UNMOUNTED_DOWN_TIME("UnmountedDownTime", "持续未挂载时长", "持续未挂载的云硬盘时长", "", PatrolResourceTypeEnum.DISK),

    //监控资源指标
    MONITOR_STATE("monitorState", "监控资产状态", "监控资产状态",
            new HashMap<>() {{
                put("low", "监控资产状态为未运行状态，建议确认运行状态");
                put("high", "当前资产状态为不可用，建议确认运行状态");
            }}, PatrolResourceTypeEnum.MONITOR),



    //网络资源指标
    //网络运行时长
    NETWORK_RUNNING_TIME("networkRunningTime", "运行时长", "网络运行时长",
            new HashMap<>() {{
                put("low", "检测到设备运行时间较短，请核实是否属于计划内维护重启行为");
            }}, PatrolResourceTypeEnum.NETWORK),
    //网络CPU平均使用率
    NETWORK_CPU_AVG_USAGE("networkCpuAvgUsage", "CPU平均使用率", "网络CPU平均使用率",
            new HashMap<>() {{
                put("medium", "针对过去{}{}的数据分析,CPU平均使用率最大值 大于等于 {}{} ,建议升配");
                put("high", "针对过去{}{}的数据分析,CPU平均使用率最大值 大于等于 {}{} ,建议升配");
            }}, PatrolResourceTypeEnum.NETWORK),
    NETWORK_MEMORY_AVG_USAGE("networkMemoryAvgUsage", "内存平均使用率", "网络内存平均使用率",
            new HashMap<>() {{
                put("medium", "针对过去{}{}的数据分析,内存平均使用率最大值 大于等于 {}{} ,建议升配");
                put("high", "针对过去{}{}的数据分析,内存平均使用率最大值 大于等于 {}{} ,建议升配");
            }}, PatrolResourceTypeEnum.NETWORK),

    //安全资源指标
    //安全运行时长
    FIREWALL_RUNNING_TIME("firewallRunningTime", "运行时长", "安全运行时长",
            new HashMap<>() {{
                put("low", "检测到设备运行时间较短，请核实是否属于计划内维护重启行为");
            }}, PatrolResourceTypeEnum.FIREWALL),

    FIREWALL_CPU_AVG_USAGE("firewallCpuAvgUsage", "CPU平均使用率", "安全CPU平均使用率",
            new HashMap<>() {{
                put("medium", "针对过去{}{}的数据分析,CPU平均使用率最大值 大于等于 {}{} ,建议升配");
                put("high", "针对过去{}{}的数据分析,CPU平均使用率最大值 大于等于 {}{} ,建议升配");
            }}, PatrolResourceTypeEnum.FIREWALL),
    FIREWALL_MEMORY_AVG_USAGE("firewallMemoryAvgUsage", "内存平均使用率", "安全内存平均使用率",
            new HashMap<>() {{
                put("medium", "针对过去{}{}的数据分析,内存平均使用率最大值 大于等于 {}{} ,建议升配");
                put("high", "针对过去{}{}的数据分析,内存平均使用率最大值 大于等于 {}{} ,建议升配");
            }}, PatrolResourceTypeEnum.FIREWALL),
    //操作系统资源指标
    //操作系统运行时长
    OS_RUNNING_TIME("osRunningTime", "运行时长", "操作系统运行时长",
            new HashMap<>() {{
                put("low", "检测到设备运行时间较短，请核实是否属于计划内维护重启行为");
            }}, PatrolResourceTypeEnum.OS),

    OS_CPU_AVG_USAGE("osCpuAvgUsage", "CPU平均使用率", "操作系统CPU平均使用率",
            new HashMap<>() {{
                put("medium", "建议排查高负载进程是否正常或进行云主机CPU扩容使CPU负载降低");
                put("high", "建议排查高负载进程是否正常或进行云主机CPU扩容使CPU负载降低");
            }}, PatrolResourceTypeEnum.OS),

    OS_MEMORY_AVG_USAGE("osMemoryAvgUsage", "内存平均使用率", "操作系统内存平均使用率",
            new HashMap<>() {{
                put("medium", "操作系统内存使用率超过阈值，建议排查高负载进程是否正常或进行内存扩容");
                put("high", "操作系统内存使用率超过阈值，建议排查高负载进程是否正常或进行内存扩容");
            }}, PatrolResourceTypeEnum.OS),

    OS_MEMORY_WAVE("osMemoryWave", "内存波动", "操作系统内存波动",
            new HashMap<>() {{
                put("medium", "检查到操作系统内存差值百分比超过阈值，建议排查操作系统是否存在内存OOM或进行内存扩容导致");
            }}, PatrolResourceTypeEnum.OS),

    //操作系统持续性CPU饱和
    OS_CPU_SATURATION("osCpuSaturation", "持续性CPU饱和", "操作系统持续CPU饱和",
            new HashMap<>() {{
                put("high", "检测到操作系统CPU利用率波动异常平缓，建议立即检查系统响应状态，确认是否存在内核僵死（hang）或调度阻塞情况");
            }}, PatrolResourceTypeEnum.OS),

    //操作系统CPU波动
    OS_CPU_WAVE("osCpuWave", "CPU波动", "操作系统CPU波动",
            new HashMap<>() {{
                put("high", "检测到操作系统CPU利用率波动异常剧烈，可能导致系统性能下降、能源消耗增加及响应延迟加剧，建议排查线程竞争、资源争用或调度阻塞问题。");
            }}, PatrolResourceTypeEnum.OS),

    //操作系统CPU进程平均占用率
    OS_CPU_PROCESS_AVG_USAGE("osCpuProcessUsage", "CPU进程占用率", "操作系统CPU进程占用率",
            new HashMap<>() {{
                put("medium", "检测到该操作系统中有单个进程CPU占用率持续达到阈值，存在潜在性能风险，建议检查该进程占用是否正常或进行CPU扩容");
                put("high", "检测到该操作系统中有单个进程CPU占用率持续达到阈值，存在潜在性能风险，建议检查该进程占用是否正常或进行CPU扩容");
            }}, PatrolResourceTypeEnum.OS),

    //操作系统CPU进程占用率峰值
    OS_CPU_PROCESS_PEAK_USAGE("osCpuProcessPeakUsage", "CPU进程占用率峰值", "操作系统CPU进程占用率峰值",
            new HashMap<>() {{
                put("high", "检查到过去一段时间内操作系统CPU峰值使用率持续超过阈值，建议排查是否存在异常进程或进行CPU扩容，避免峰值超限影响业务");
            }}, PatrolResourceTypeEnum.OS),

    //操作系统内存进程占用率
    OS_MEMORY_PROCESS_AVG_USAGE("osMemoryProcessUsage", "内存进程占用率", "操作系统内存进程占用率",
            new HashMap<>() {{
                put("medium", "检测到该操作系统中有单个进程内存占用率持续达到阈值，存在OOM风险，建议检查该进程占用是否正常或进行内存扩容");
                put("high", "检测到该操作系统中有单个进程内存占用率持续达到阈值，存在OOM风险，建议检查该进程占用是否正常或进行内存扩容");
            }}, PatrolResourceTypeEnum.OS);



    /**
     * 指标编码
     */
    private final String code;

    /**
     * 指标名称
     */
    private final String name;

    /**
     * 指标标题
     */
    private final String title;

    /**
     * 指标建议
     */
    private final Map<String, String> suggest;

    /**
     * 所属资源类型
     */
    private final PatrolResourceTypeEnum resourceType;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static PatrolMetricEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (PatrolMetricEnum value : PatrolMetricEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据资源类型获取该类型下的所有指标
     *
     * @param resourceType 资源类型
     * @return 指标列表
     */
    public static PatrolMetricEnum[] getByResourceType(PatrolResourceTypeEnum resourceType) {
        if (resourceType == null) {
            return new PatrolMetricEnum[0];
        }
        return java.util.Arrays.stream(PatrolMetricEnum.values())
                .filter(metric -> metric.getResourceType() == resourceType)
                .toArray(PatrolMetricEnum[]::new);
    }
}