package cn.iocoder.zj.module.om.api.asset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class VncInfoDTO {

    @Schema(description = "本地IP")
    private String tcpHost;

    @Schema(description = "主机id")
    private Long cloudId;

    @Schema(description = "本地 TCP 服务器端口")
    private Integer tcpPort;

    @Schema(description = "portMappingId")
    private String portMappingId;

    private String vncToken;

    private Long PlatformId;

    private String cloudName;

    private String websocketPath;

    private String vncType;
}
