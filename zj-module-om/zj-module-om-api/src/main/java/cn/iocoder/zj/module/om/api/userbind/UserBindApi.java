package cn.iocoder.zj.module.om.api.userbind;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.om.api.userbind.dto.UserBindRespVO;
import cn.iocoder.zj.module.om.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME) // ① @FeignClient 注解
@Tag(name = "RPC 服务 - 用户绑定关系") // ② Swagger 接口文档
public interface UserBindApi {

    String PREFIX = ApiConstants.PREFIX + "/user";

    @GetMapping(PREFIX + "/getuserbinglist")
    @Operation(summary = "通过用户查询所有绑定关系")
    CommonResult<List<UserBindRespVO>> getUserBingList(@RequestParam("userIds") List<Long> userIds);
}
