package cn.iocoder.zj.module.om.enums;

import cn.iocoder.zj.framework.common.exception.ErrorCode;

/**
 * om 错误码枚举类
 * <p>
 * om 运维，使用 6-002-000-000 段
 */
public interface ErrorCodeConstants {
    ErrorCode HAVE_NO_PLATFORM = new ErrorCode(2001000000, "暂无平台数据");
    ErrorCode HAVE_NO_ACCESS_TOKEN = new ErrorCode(2001000001, "获取平台登录授权失败");
    // ========== 运维排班信息 2002000000 ==========
    ErrorCode SCHEDULING_INFO_NOT_EXISTS = new ErrorCode(2002000000, "运维排班信息不存在");
    ErrorCode WORK_ORDER_NOT_EXISTS = new ErrorCode(2003000000, "工单管理信息不存在");
    // ========== 配置备份 2004000000 ==========
    ErrorCode DB_FILE_NOT_EXISTS = new ErrorCode(2004000000, "配置备份不存在");
    ErrorCode PATROL_INSPECTION_NOT_EXISTS = new ErrorCode(2004000000, "自动巡检规则不存在");
    ErrorCode PATROL_INSPECTION_EXISTS = new ErrorCode(2005000000, "自动巡检规则已存在");
    ErrorCode NAME_ALREADY_EXISTS = new ErrorCode(2006000000, "该名称的自动巡检规则已经存在");
    ErrorCode TYPE_ALREADY_EXISTS = new ErrorCode(2007000000, "该类型的自动巡检规则已经存在");
    ErrorCode PATROL_INSPECTION_ILLEGAL = new ErrorCode(2008000000, "导入的列表中出现不存在的自动巡检规则，暂不支持导入新规则");
    ErrorCode PATROL_INSPECTION_NEME_IS_EMPTY = new ErrorCode(2008000001, "巡检规则名称不可为空");
    ErrorCode PATROL_INSPECTION_TYPE_IS_EMPTY = new ErrorCode(2008000002, "巡检规则类型不可为空");
    ErrorCode PATROL_INSPECTION_DATARESOURCE_IS_EMPTY = new ErrorCode(2008000003, "巡检规则数据源不可为空");
    ErrorCode PATROL_INSPECTION_VALUE_IS_EMPTY = new ErrorCode(2008000004, "巡检规则分值不可为空");
    ErrorCode PATROL_INSPECTION_ASSETTYPE_IS_EMPTY = new ErrorCode(2008000005, "巡检规则资产类型不可为空");
    ErrorCode PATROL_INSPECTION_RULE_IS_EMPTY = new ErrorCode(2008000006, "巡检规则扣分规则不可为空");
    ErrorCode PATROL_INSPECTION_FORMULA_IS_EMPTY = new ErrorCode(2008000007, "巡检规则计算公式不可为空");
    ErrorCode PATROL_INSPECTION_PARAM_IS_EMPTY = new ErrorCode(2008000008, "巡检规则参数不可为空");
    ErrorCode PATROL_INSPECTION_IMPORT_LIST_IS_EMPTY = new ErrorCode(2009000000, "导入列表不可为空");
    ErrorCode INSPECTION_LOG_NOT_EXISTS = new ErrorCode(2010000000, "巡检记录不存在");
    ErrorCode INSPECTION_RECORD_NOT_EXISTS = new ErrorCode(2011000000, "巡检记录不存在");

    // ========== 自动巡检异常记录 TODO 补充编号 ==========
    ErrorCode INSPECTION_LOGS_NOT_EXISTS = new ErrorCode(2021000000, "自动巡检异常记录不存在");

    ErrorCode FILE_IS_EMPTY = new ErrorCode(2031000001, "导入文件不可为空，请重新上传文件");
    ErrorCode FILE_IS_WRONG = new ErrorCode(2031000002, "导入文件模板错误，请重新上传文件");
    ErrorCode FILE_FORMAT_WRONG = new ErrorCode(2031000003, "导入文件格式错误，请重新上传文件");


    // ========== 监控资产  ==========
    ErrorCode MONITOR_ASSET_NOT_EXISTS = new ErrorCode(2041000003, "监控资产不存在");
    ErrorCode MONITOR_ASSET_EXISTS = new ErrorCode(2041000004, "监控资产已存在");


    // ========================
    ErrorCode MONITOR_AUTHORIZATION_NOT_EXISTS = new ErrorCode(2051000003, "监控申请授权不存在");

    // ========== 用户资产授权申请 ==========
    ErrorCode MONITOR_AUTHORIZATION_USER_NOT_EXISTS = new ErrorCode(2052000003, "用户资产授权申请不存在");

    // ========== 巡检任务 ==========
    ErrorCode PATROL_INSPECTION_CONFIG_NOT_EXISTS = new ErrorCode(2061000001, "巡检设置不存在");
    ErrorCode PATROL_INSPECTION_CONFIG_NOT_NULL = new ErrorCode(2061000002, "巡检设置项不能为空");
    ErrorCode PATROL_PLAN_NOT_EXISTS = new ErrorCode(2061000020, "巡检计划不存在");
    ErrorCode PATROL_RESULT_CATEGORY_NOT_EXISTS = new ErrorCode(2061000030, "巡检结果分类不存在");
    ErrorCode PATROL_ABNORMAL_DETAIL_NOT_EXISTS = new ErrorCode(2061000040, "巡检异常明细不存在");
    ErrorCode PATROL_RECORD_NOT_EXISTS = new ErrorCode(2061000050, "巡检记录不存在");



}