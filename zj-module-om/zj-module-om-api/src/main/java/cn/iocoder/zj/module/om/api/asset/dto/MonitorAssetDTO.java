package cn.iocoder.zj.module.om.api.asset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * @ClassName : MonitorAssetDTO  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/7/16  15:13
 */
@Data
public class MonitorAssetDTO {
    private Long id;
    @Schema(description = "资产id")
    private String assetId;

    @Schema(description = "协议类型")
    private String protocol;

    @Schema(description = "协议端口")
    private Integer protocolProd;

    @Schema(description = "账户类型  密码：custom；   授权凭证：private-key")
    private String certificate;

    @Schema(description = "用户名称")
    private String username;

    @Schema(description = "账号密码")
    private String password;

    @Schema(description = "主机ip")
    private String hostname;

    @Schema(description = "资产名称")
    private String assetName;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "资产类型")
    private Integer assetType;

    @Schema(description = "平台名称")
    private String platformName;

    private Integer deleted;
}
