package cn.iocoder.zj.module.om.api.dbfile;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.om.api.dbfile.dto.DbfileCreateReqDTO;
import cn.iocoder.zj.module.om.api.dbfile.dto.WorkOrderCreateReqDTO;
import cn.iocoder.zj.module.om.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @ClassName : DbfileApi  //类名
 * @Description : 配置备份 API  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/10/12  11:01
 */
@FeignClient(name = ApiConstants.NAME) // ① @FeignClient 注解
@Tag(name = "RPC 服务 - 数据库配置备份") // ② Swagger 接口文档
public interface DbfileApi {
    String PREFIX = ApiConstants.PREFIX + "/db";

    @PostMapping(PREFIX + "/adds") // ③ Spring MVC 接口注解
    @Operation(summary = "新增云主机信息")  // ② Swagger 接口文档
    CommonResult<Boolean> adds(@RequestBody DbfileCreateReqDTO reqDTO);

    @GetMapping(PREFIX + "/getSchedulCount") // ③ Spring MVC 接口注解
    @Operation(summary = "获取排班数据")  // ② Swagger 接口文档
    @Parameter(name = "id", description = "用户id", required = true)
    @Parameter(name = "name", description = "用户名称", required = true)
    Long getSchedulCount(@RequestParam("id") Long id,@RequestParam("name") String name);

    @GetMapping(PREFIX + "/getWorkOrderCount") // ③ Spring MVC 接口注解
    @Operation(summary = "获取工单数据")  // ② Swagger 接口文档
    @Parameter(name = "id", description = "用户id", required = true)
    @Parameter(name = "name", description = "用户名称", required = true)
    Long getWorkOrderCount(@RequestParam("id") Long id,@RequestParam("name") String name);

    @PostMapping(PREFIX + "/addWorkOrder") // ③ Spring MVC 接口注解
    @Operation(summary = "新增合同交付实施工单")  // ② Swagger 接口文档
    CommonResult<Boolean> addWorkOrder(@RequestBody WorkOrderCreateReqDTO reqDTO);

    @PostMapping(PREFIX + "/transferWorkOrder") // ③ Spring MVC 接口注解
    @Operation(summary = "修改合同交付实施工单对应实施人")  // ② Swagger 接口文档
    @Parameter(name = "contractId", description = "合同id", required = true)
    @Parameter(name = "enforcerId", description = "实施人id", required = true)
    @Parameter(name = "enforcerName", description = "实施人名称", required = true)
    CommonResult<Boolean> transferWorkOrder(@RequestParam("contractId") String contractId,@RequestParam("enforcerId") String enforcerId,@RequestParam("enforcerName") String enforcerName);

    @PostMapping(PREFIX + "/updateWorkOrderByContId") // ③ Spring MVC 接口注解
    @Operation(summary = "修改合同交付实施工单对应合同")  // ② Swagger 接口文档
    @Parameter(name = "testContractId", description = "测试合同id", required = true)
    @Parameter(name = "contractId", description = "合同id", required = true)
    CommonResult<Boolean> updateWorkOrderByContId(@RequestParam("testContractId") String testContractId,@RequestParam("contractId") String contractId,@RequestParam("projectName") String projectName);

    @PostMapping(PREFIX + "/deleteWorkOrder") // ③ Spring MVC 接口注解
    @Operation(summary = "租户删除后同步删除对应合同产生的工单")  // ② Swagger 接口文档
    @Parameter(name = "contractId", description = "合同id", required = true)
    CommonResult<Boolean> deleteWorkOrder(@RequestParam("contractId") String contractId);

}
