package cn.iocoder.zj.module.om.api.asset;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.om.api.asset.dto.MonitorAssetDTO;
import cn.iocoder.zj.module.om.api.asset.dto.VncInfoDTO;
import cn.iocoder.zj.module.om.api.dbfile.dto.DbfileCreateReqDTO;
import cn.iocoder.zj.module.om.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;


@FeignClient(name = ApiConstants.NAME) // ① @FeignClient 注解
@Tag(name = "RPC 服务 - 监控管理API") // ② Swagger 接口文档
public interface MonitorassetApi {
    String PREFIX = ApiConstants.PREFIX + "/asset";

    @PostMapping(PREFIX + "/getAssetInfoById") // ③ Spring MVC 接口注解
    @Operation(summary = "根据资产id获取资产详情")  // ② Swagger 接口文档
    CommonResult<MonitorAssetDTO> getAssetInfoById(@RequestParam("id") Long id);


    @PostMapping(PREFIX + "/getVncInfoByUuid") // ③ Spring MVC 接口注解
    @Operation(summary = "根据资产id获取webSocket转tcp详细信息")  // ② Swagger 接口文档
    CommonResult<VncInfoDTO> getVncInfoByUuid(@RequestParam("uuid") String uuid);

    @PostMapping(PREFIX + "/updateAssetById") // ③ Spring MVC 接口注解
    @Operation(summary = "根据资产更新详细信息")  // ② Swagger 接口文档
    void updateAssetById(@RequestBody MonitorAssetDTO checkedData);
    @PostMapping(PREFIX + "/getAssetInfoByIdAndFormId") // ③ Spring MVC 接口注解
    @Operation(summary = "根据资产id获取资产详情")  // ② Swagger 接口文档
    CommonResult<MonitorAssetDTO> getAssetInfoByIdAndFormId(@RequestParam("monitorId") String monitorId,@RequestParam("platformId") Long platformId);

    @PostMapping(PREFIX + "/updateAssetByAssetId") // ③ Spring MVC 接口注解
    @Operation(summary = "根据资产更新详细信息")
    void updateAssetByAssetId(@RequestBody MonitorAssetDTO dto);

    @PostMapping(PREFIX + "/updateAsset") // ③ Spring MVC 接口注解
    @Operation(summary = "根据资产更新详细信息")
    void updateAsset(@Valid @RequestParam("id") Long id, @Valid @RequestParam("name")String name);
}
