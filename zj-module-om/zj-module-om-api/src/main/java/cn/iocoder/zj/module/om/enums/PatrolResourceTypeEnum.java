package cn.iocoder.zj.module.om.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 巡检资源类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PatrolResourceTypeEnum {

    // 私有云
    STORAGE("storage", "云存储", "存储资源", PatrolAssetType.PRIVATE_CLOUD),
    HOST("host", "宿主机", "宿主机资源", PatrolAssetType.PRIVATE_CLOUD),
    CLOUD("cloud", "云主机", "云主机资源", PatrolAssetType.PRIVATE_CLOUD),
    DISK("disk", "云硬盘", "云硬盘资源", PatrolAssetType.PRIVATE_CLOUD),

    //监控
    MONITOR("monitor", "监控", "监控资源", PatrolAssetType.MONITOR),
    //网络
    NETWORK("network", "网络", "网络资源", PatrolAssetType.MONITOR),
    //安全
    FIREWALL("firewall", "安全", "安全资源", PatrolAssetType.MONITOR),
    //操作系统
    OS("os", "操作系统", "操作系统资源", PatrolAssetType.MONITOR);

    /**
     * 资源类型编码
     */
    private final String code;

    /**
     * 资源类型名称
     */
    private final String name;


    /**
     * 资源类型标题
     */
    private final String title;

    /**
     * 所属资产大类
     */
    private final PatrolAssetType assetType;


    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static PatrolResourceTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (PatrolResourceTypeEnum value : PatrolResourceTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据资产大类获取枚举列表
     *
     * @param assetType 资产大类
     */
    public static List<PatrolResourceTypeEnum> getByAssetType(PatrolAssetType assetType) {
        return Arrays.stream(PatrolResourceTypeEnum.values())
                .filter(value -> value.getAssetType().equals(assetType))
                .toList();
    }
}