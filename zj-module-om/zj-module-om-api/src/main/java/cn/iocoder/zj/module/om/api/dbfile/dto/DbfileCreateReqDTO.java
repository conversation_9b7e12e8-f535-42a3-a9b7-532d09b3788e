package cn.iocoder.zj.module.om.api.dbfile.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName : DbfileCreateReqDTO  //类名
 * @Description : 配置备份DTO  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/10/12  11:22
 */
@Schema(description = "RPC 服务 - 配置备份 Request DTO")
@Data
public class DbfileCreateReqDTO {
    @Schema(description = "文件名")
    private String name;

    @Schema(description = "文件路径")
    private String path;

    @Schema(description = "文件url")
    private String url;

    @Schema(description = "文件大小")
    private Integer size;

}
