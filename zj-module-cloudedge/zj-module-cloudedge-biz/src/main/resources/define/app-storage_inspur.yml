# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring mid-middleware custom-custom monitoring os-operating system monitoring
category: stored
# The monitoring type eg: linux windows tomcat mysql aws...
app: storage_inspur
# The monitoring i18n name
name:
  zh-CN: 浪潮存储
  en-US: Inspur Storage
params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # field-param field key
  - field: port
    # name-param field display i18n name
    name:
      zh-CN: 端口
      en-US: Port
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    # required-true or false
    required: true
    # default value
    defaultValue: 161
  # field-param field key
  - field: version
    # name-param field display i18n name
    name:
      zh-CN: SNMP 版本
      en-US: SNMP Version
    # type-param field type(radio mapping the html radio tag)
    type: radio
    # required-true or false
    required: true
    # when type is radio checkbox, use option to show optional values {name1:value1,name2:value2}
    options:
      - label: SNMPv1
        value: 0
      - label: SNMPv2c
        value: 1
  # field-param field key
  - field: community
    # name-param field display i18n name
    name:
      zh-CN: SNMP 团体字
      en-US: SNMP Community
    # type-param field type(most mapping the html input type)
    type: text
    # when type is text, use limit to limit string length
    limit: 100
    # required-true or false
    required: true
    # param field input placeholder
    placeholder: 'Snmp community for v1 v2c'
  # field-param field key
  - field: timeout
    # name-param field display i18n name
    name:
      zh-CN: 超时时间(ms)
      en-US: Timeout(ms)
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,100000]'
    # required-true or false
    required: false
    # hide-is hide this field and put it in advanced layout
    hide: true
    # default value
    defaultValue: 6000
# collect metrics config list
metrics:
  # metrics - system
  - name: system
    i18n:
      zh-CN: 系统
      en-US: System
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    fields:
      - field: clusterName
        type: 1
        i18n:
          zh-CN: 集群名称
          en-US: clusterName
      - field: clusterStatus
        type: 1
        i18n:
          zh-CN: 集群状态
          en-US: clusterStatus
      - field: cpuused
        type: 1
        i18n:
          zh-CN: CPU使用率
          en-US: cpuused
        label: true
      - field: clusterTime
        type: 1
        i18n:
          zh-CN: 集群时间
          en-US: clusterTime
      - field: clusterSysVersion
        type: 1
        i18n:
          zh-CN: 集群系统版本
          en-US: clusterSysVersion
      - field: clusterID
        type: 1
        i18n:
          zh-CN: 集群ID
          en-US: clusterID

      - field: responseTime
        type: 0
        i18n:
          zh-CN: 响应时间
          en-US: Response Time
        label: true
        unit: ms
      - field: location
        type: 1
        i18n:
          zh-CN: 位置
          en-US: Location

    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: get
      oids:
        clusterName: *******.4.1.48512.2.1.0
        clusterStatus: *******.4.1.48512.2.2.0
        clusterTime: *******.4.1.48512.2.3.0
        clusterSysVersion: *******.4.1.48512.2.4.0
        clusterID: *******.4.1.48512.*******.1
        location: *******.4.1.48512.*******.1
        cpuused: *******.4.1.48512.*********.2.0


  - name: performance
    i18n:
      zh-CN: 系统性能指标
      en-US: performance
    priority: 2
    fields:
      - field: fc-mb
        type: 1
        i18n:
          zh-CN: 光纤通道每秒兆字节数
          en-US: Fibre Channel Megabytes per Second

      - field: fc-io
        type: 1
        i18n:
          zh-CN: 光纤通道每秒I/O操作数
          en-US: Fibre Channel I/O Operations per Second



      - field: iscsi-mb
        type: 1
        i18n:
          zh-CN: iSCSI每秒兆字节数
          en-US: iSCSI Megabytes per Second

      - field: iscsi-io
        type: 1
        i18n:
          zh-CN: iSCSI每秒I/O操作数
          en-US: iSCSI I/O Operations per Second

      - field: vdisk-mb
        type: 1
        i18n:
          zh-CN: 虚拟磁盘每秒兆字节数
          en-US: Virtual Disk Megabytes per Second
        label: true
      - field: vdisk-io
        type: 1
        i18n:
          zh-CN: 虚拟磁盘每秒I/O操作数
          en-US: Virtual Disk I/O Operations per Second
        label: true
      - field: vdisk-ms
        type: 1
        i18n:
          zh-CN: 虚拟磁盘响应毫秒数
          en-US: Virtual Disk Response Milliseconds
        label: true


      - field: cache-write-rate
        type: 1
        i18n:
          zh-CN: 写缓存百分比
          en-US: Write Cache Percentage
        label: true
      - field: cache-read-rate
        type: 1
        i18n:
          zh-CN: 读缓存百分比
          en-US: Read Cache Percentage
        label: true
      - field: vdisk-us
        type: 1
        i18n:
          zh-CN: 虚拟磁盘响应微秒数
          en-US: Virtual Disk Response Microseconds
        label: true

    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: get
      oids:
        fc-mb: *******.4.1.48512.*********.3.0
        fc-io: *******.4.1.48512.*********.4.0
        iscsi-mb: *******.4.1.48512.*********.7.0
        iscsi-io: *******.4.1.48512.*********.8.0
        vdisk-mb: *******.4.1.48512.*********.9.0
        vdisk-io: *******.4.1.48512.*********.10.0
        vdisk-ms: *******.4.1.48512.*********.11.0
        cache-write-rate: *******.4.1.48512.*********.18.0
        cache-read-rate: *******.4.1.48512.*********.19.0
        vdisk-us: *******.4.1.48512.*********.20.0
        mdisk-us: *******.4.1.48512.*********.21.0
        drive-us: *******.4.1.48512.*********.22.0


  - name: systemstorage
    i18n:
      zh-CN: 系统存储容量
      en-US:  systemstorage
    priority: 3
    fields:
      - field: physicalStorageTotal
        type: 1
        i18n:
          zh-CN: 物理存储总容量
          en-US: physicalStorageTotal
      - field: physicalStorageUsed
        type: 1
        i18n:
          zh-CN: 物理存储使用容量
          en-US: physicalStorageUsed

      - field: physicalStorageAvailable
        i18n:
          zh-CN: 物理存储可用容量
          en-US: physicalStorageAvailable
        type: 1
      - field: mdiskStorageTotal
        i18n:
          zh-CN: mdisk存储总容量
          en-US: mdiskStorageTotal
        type: 1

      - field: mdiskStorageUsed
        i18n:
          zh-CN: mdisk存储使用容量
          en-US: mdiskStorageUsed
        type: 1

      - field: mdiskStorageAvailable

        i18n:
          zh-CN: mdisk存储可用容量
          en-US: mdiskStorageAvailable
        type: 1

      - field: poolStorageTotal
        i18n:
          zh-CN: 存储池总容量
          en-US: poolStorageTotal
        type: 1

      - field: poolStorageUsed
        i18n:
          zh-CN: 存储池使用容量
          en-US: poolStorageUsed
        type: 1
      - field: poolStorageAvailable
        i18n:
          zh-CN: 存储池可用容量
          en-US: poolStorageAvailable
        type: 1
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: get
      oids:
        physicalStorageTotal: *******.4.1.48512.*********.0
        physicalStorageUsed: *******.4.1.48512.*********.0
        physicalStorageAvailable: *******.4.1.48512.*********.0
        mdiskStorageTotal: *******.4.1.48512.*********.0
        mdiskStorageUsed: *******.4.1.48512.*********.0
        mdiskStorageAvailable: *******.4.1.48512.*********.0
        poolStorageTotal: *******.4.1.48512.*********.0
        poolStorageUsed: *******.4.1.48512.*********.0
        poolStorageAvailable: *******.4.1.48512.*********.0




  - name: fcPortMbpsEntry
    i18n:
      zh-CN: FC端口性能
      en-US: fcPortMbpsEntry
    priority: 4
    fields:
      - field: fcPortID
        type: 0
        i18n:
          zh-CN: FC端口ID
          en-US: fcPortID
      - field: fcNodeID
        type: 0
        i18n:
          zh-CN: FC节点ID
          en-US: fcPortMbps

      - field: fcPortMbps
        i18n:
          zh-CN: FC端口实时速率
          en-US: fcPortMbps
        label: true
        type: 1

    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        fcPortID: *******.4.1.48512.*********.1
        fcNodeID: *******.4.1.48512.*********.2
        fcPortMbps: *******.4.1.48512.*********.3

  - name: driveInfoTable
    i18n:
      zh-CN: 磁盘组信息
      en-US: driveInfoTable
    priority: 1
    fields:
      - field: driveID
        type: 0
        i18n:
          zh-CN: 磁盘ID
          en-US: driveID
      - field: drive-status
        i18n:
          zh-CN: 磁盘状态
          en-US: drive-status
        type: 1
      - field: drive-capacity
        i18n:
          zh-CN: 磁盘容量
          en-US: drive-capacity
        label: true
        type: 1

      - field: drive-currentRole
        i18n:
          zh-CN: 磁盘类型
          en-US: drive-currentRole
        type: 1

      - field: drive-techType
        type: 1
        i18n:
          zh-CN: 磁盘介质
          en-US: drive-techType
      - field: drive-enclosureID
        type: 1
        i18n:
          zh-CN: 磁盘盘柜ID
          en-US: drive-enclosureID
      - field: drive-slotID
        i18n:
          zh-CN: 磁盘槽位号
          en-US: drive-slotID
        type: 1
      - field: drive-fruID
        i18n:
          zh-CN: 磁盘SN
          en-US: drive-fruID
        type: 1

      - field: drive-util
        i18n:
          zh-CN: 磁盘使用率
          en-US: drive-util
        label: true
        type: 1
      - field: drive-temperature
        i18n:
          zh-CN: 磁盘温度
          en-US: drive-temperature
        type: 0
        unit: '°C'
        label: true
      - field: drive-healthPercentage
        i18n:
          zh-CN: 磁盘健康百分比
          en-US: drive-healthPercentage
        type: 0
        unit: '%'
        label: true
      - field: drive-lifeRemaining
        i18n:
          zh-CN: 磁盘剩余寿命
          en-US: drive-lifeRemaining
        type: 0
        unit: '%'
        label: true
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        driveID: *******.4.1.48512.*********.1
        drive-status: *******.4.1.48512.*********.2
        drive-capacity: *******.4.1.48512.*********.3
        drive-currentRole: *******.4.1.48512.*********.5
        drive-techType: *******.4.1.48512.*********.6
        drive-enclosureID: *******.4.1.48512.*********.7
        drive-slotID: *******.4.1.48512.*********.8
        drive-fruID: *******.4.1.48512.*********.10
        drive-util: *******.4.1.48512.*********.13
        drive-temperature: *******.4.1.48512.*********.14
        drive-healthPercentage: *******.4.1.48512.*********.15
        drive-lifeRemaining: *******.4.1.48512.*********.16




  - name: clusterConfigEntry
    i18n:
      zh-CN: 集群配置清单
      en-US: clusterConfigEntry
    priority: 4
    fields:
      - field: nodeIndex
        type: 0
        i18n:
          zh-CN: 序号
          en-US: nodeIndex
      - field: nodeName
        type: 1
        i18n:
          zh-CN: 节点名称
          en-US: nodeName

      - field: nodeCPUCount
        type: 1
        i18n:
          zh-CN: 节点CPU数
          en-US: nodeCPUCount

      - field: nodeCPUSocket
        type: 1
        i18n:
          zh-CN: 节点CPU插槽数
          en-US: nodeCPUSocket

      - field: nodeCPUConfigured
        type: 1
        i18n:
          zh-CN: 节点CPU配置
          en-US: nodeCPUConfigured

      - field: nodeCPUActual
        type: 1
        i18n:
          zh-CN: 节点当前CPU配置
          en-US: nodeCPUActual

      - field: nodeMemoryConfigured
        type: 1
        i18n:
          zh-CN: 节点内存配置
          en-US: nodeMemoryConfigured

      - field: nodeMemoryReal
        type: 1
        i18n:
          zh-CN: 节点真实内存配置
          en-US: nodeMemoryReal


    aliasFields:
      - nodeIndex
      - nodeName
      - nodeCPUCount
      - nodeCPUSocket
      - nodeCPUConfigured
      - nodeCPUActual
      - nodeMemoryConfigured
      - nodeMemoryReal

    calculates:
      - nodeIndex=nodeIndex
      - nodeName=nodeName
      - nodeCPUCount=nodeCPUCount
      - nodeCPUSocket=nodeCPUSocket
      - nodeCPUConfigured=nodeCPUConfigured
      - nodeCPUActual=nodeCPUActual
      - nodeMemoryConfigured=nodeMemoryConfigured
      - nodeMemoryReal=nodeMemoryReal
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        nodeIndex: *******.4.1.48512.*******
        nodeName: *******.4.1.48512.*******
        nodeCPUCount: *******.4.1.48512.*******
        nodeCPUSocket: *******.4.1.48512.*******
        nodeCPUConfigured: *******.4.1.48512.*******
        nodeCPUActual: *******.4.1.48512.*******
        nodeMemoryConfigured: *******.4.1.48512.*******
        nodeMemoryReal: *******.4.1.48512.*******


  - name: enclosureStatusTable
    i18n:
      zh-CN: 节点信息
      en-US: enclosureStatusTable
    priority: 5
    fields:
      - field: enclosureID
        type: 0
        i18n:
          zh-CN: 盘柜ID
          en-US: enclosureID
      - field: enclosure-status
        type: 1
        i18n:
          zh-CN: 盘柜状态
          en-US: enclosure-status
      - field: enclosure-type
        type: 1
        i18n:
          zh-CN: 盘柜类型
          en-US: enclosure-type
      - field: enclosure-productMTM
        type: 1
        i18n:
          zh-CN: 盘柜型号
          en-US: enclosure-productMTM
      - field: enclosure-serialNumber
        type: 1
        i18n:
          zh-CN: 盘柜SN号
          en-US: enclosure-serialNumber
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        enclosureID: *******.4.1.48512.*********.1
        enclosure-status: *******.4.1.48512.*********.2
        enclosure-type: *******.4.1.48512.*********.3
        enclosure-productMTM: *******.4.1.48512.*********.5
        enclosure-serialNumber: *******.4.1.48512.*********.6


  - name: psuInfoTable
    i18n:
      zh-CN: 电源供应单元信息表
      en-US: PSU Information Table
    priority: 1
    fields:
      - field: psu-fruID
        type: 1
        i18n:
          zh-CN: 电源现场可更换单元标识
          en-US: psu-fruID

      - field: psu-enclosureID
        type: 0
        i18n:
          zh-CN: 电源供应单元机箱标识
          en-US: psu-enclosureID
      - field: psu-status
        type: 1
        i18n:
          zh-CN: 电源供应单元状态
          en-US: psu-status
        label: true
      - field: psu-healthStatus
        type: 1
        i18n:
          zh-CN: 电源健康状态
          en-US: psu-healthStatus
        label: true
      - field: psu-inputFail
        type: 1
        i18n:
          zh-CN: 电源输入故障
          en-US: psu-inputFail
      - field: psu-outputFail
        type: 1
        i18n:
          zh-CN: 电源输出故障
          en-US: psu-outputFail
      - field: psu-inputPower
        type: 0
        i18n:
          zh-CN: 电源输入功率
          en-US: psu-inputPower
        unit: 'W'
        label: true
      - field: psu-outputPower
        type: 0
        i18n:
          zh-CN: 电源输出功率
          en-US: psu-outputPower
        unit: 'W'
        label: true
      - field: psu-powerConsumption
        type: 0
        i18n:
          zh-CN: 电源功耗
          en-US: psu-powerConsumption
        unit: 'W'
        label: true
      - field: psu-redundant
        type: 1
        i18n:
          zh-CN: 电源冗余状态
          en-US: psu-redundant
      - field: psu-efficiency
        type: 0
        i18n:
          zh-CN: 电源效率
          en-US: psu-efficiency
        unit: '%'
      - field: psu-temperature
        type: 0
        i18n:
          zh-CN: 电源温度
          en-US: psu-temperature
        unit: '°C'

    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        psu-enclosureID: *******.4.1.48512.*********.2
        psu-status: *******.4.1.48512.*********.3
        psu-healthStatus: *******.4.1.48512.*********.11
        psu-inputFail: *******.4.1.48512.*********.4
        psu-outputFail: *******.4.1.48512.*********.5
        psu-inputPower: *******.4.1.48512.*********.6
        psu-outputPower: *******.4.1.48512.*********.7
        psu-powerConsumption: *******.4.1.48512.*********.12
        psu-redundant: *******.4.1.48512.*********.8
        psu-efficiency: *******.4.1.48512.*********.13
        psu-temperature: *******.4.1.48512.*********.14
        psu-fruID: *******.4.1.48512.*********.10

  - name: bbuInfoTable
    i18n:
      zh-CN: 电池状态
      en-US: Storages
    priority: 6
    fields:
      - field: bbuID
        type: 0
        i18n:
          zh-CN: 电池模块ID
          en-US: bbuID
      - field: bbu-enclosureID
        type: 0
        i18n:
          zh-CN: 电池盘柜ID
          en-US: bbu-enclosureID

      - field: bbu-status
        i18n:
          zh-CN: 电池状态
          en-US: bbu-status
        type: 1
      - field: bbu-chargingStatus
        i18n:
          zh-CN: 电池充电状态
          en-US: bbu-chargingStatus
        type: 1

      - field: bbu-percentCharged
        i18n:
          zh-CN: 电池容量百分比
          en-US: bbu-percentCharged
        type: 1
        label: true

    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        bbuID: *******.4.1.48512.*********.1
        bbu-enclosureID: *******.4.1.48512.*********.2
        bbu-status: *******.4.1.48512.*********.3
        bbu-chargingStatus: *******.4.1.48512.*********.4
        bbu-percentCharged: *******.4.1.48512.*********.5

  # 新增：磁盘健康度监控
  - name: diskHealthTable
    i18n:
      zh-CN: 磁盘健康度监控
      en-US: Disk Health Monitoring
    priority: 1
    fields:
      - field: diskHealthID
        type: 0
        i18n:
          zh-CN: 磁盘ID
          en-US: Disk ID
      - field: diskHealthStatus
        type: 1
        i18n:
          zh-CN: 磁盘健康状态
          en-US: Disk Health Status
        label: true
      - field: diskLifePercentage
        type: 0
        i18n:
          zh-CN: 磁盘寿命百分比
          en-US: Disk Life Percentage
        unit: '%'
        label: true
      - field: diskTemperature
        type: 0
        i18n:
          zh-CN: 磁盘温度
          en-US: Disk Temperature
        unit: '°C'
        label: true
      - field: diskSerialNumber
        type: 1
        i18n:
          zh-CN: 磁盘序列号
          en-US: Disk Serial Number
      - field: diskSmartStatus
        type: 1
        i18n:
          zh-CN: SMART状态
          en-US: SMART Status

    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        diskHealthID: *******.4.1.48512.*********.1
        diskHealthStatus: *******.4.1.48512.*********.14
        diskLifePercentage: *******.4.1.48512.*********.15
        diskTemperature: *******.4.1.48512.*********.16
        diskSerialNumber: *******.4.1.48512.*********.10
        diskSmartStatus: *******.4.1.48512.*********.17

  # 新增：卷使用率监控
  - name: volumeUsageTable
    i18n:
      zh-CN: 卷使用率监控
      en-US: Volume Usage Monitoring
    priority: 2
    fields:
      - field: volumeID
        type: 0
        i18n:
          zh-CN: 卷ID
          en-US: Volume ID
      - field: volumeName
        type: 1
        i18n:
          zh-CN: 卷名称
          en-US: Volume Name
      - field: volumeTotalCapacity
        type: 0
        i18n:
          zh-CN: 卷总容量
          en-US: Volume Total Capacity
        unit: 'GB'
      - field: volumeUsedCapacity
        type: 0
        i18n:
          zh-CN: 卷已用容量
          en-US: Volume Used Capacity
        unit: 'GB'
      - field: volumeUsagePercentage
        type: 0
        i18n:
          zh-CN: 卷使用率
          en-US: Volume Usage Percentage
        unit: '%'
        label: true
      - field: volumeStatus
        type: 1
        i18n:
          zh-CN: 卷状态
          en-US: Volume Status

    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        volumeID: *******.4.1.48512.*********.1
        volumeName: *******.4.1.48512.*********.2
        volumeTotalCapacity: *******.4.1.48512.*********.3
        volumeUsedCapacity: *******.4.1.48512.*********.4
        volumeUsagePercentage: *******.4.1.48512.*********.5
        volumeStatus: *******.4.1.48512.*********.6

  # 新增：IOPS性能监控
  - name: iopsPerformanceTable
    i18n:
      zh-CN: IOPS性能监控
      en-US: IOPS Performance Monitoring
    priority: 2
    fields:
      - field: iopsDeviceID
        type: 0
        i18n:
          zh-CN: 设备ID
          en-US: Device ID
      - field: iopsReadPerSecond
        type: 0
        i18n:
          zh-CN: 每秒读取IOPS
          en-US: Read IOPS per Second
        unit: 'IOPS'
        label: true
      - field: iopsWritePerSecond
        type: 0
        i18n:
          zh-CN: 每秒写入IOPS
          en-US: Write IOPS per Second
        unit: 'IOPS'
        label: true
      - field: iopsTotalPerSecond
        type: 0
        i18n:
          zh-CN: 每秒总IOPS
          en-US: Total IOPS per Second
        unit: 'IOPS'
        label: true
      - field: iopsReadLatency
        type: 0
        i18n:
          zh-CN: 读取延迟
          en-US: Read Latency
        unit: 'ms'
      - field: iopsWriteLatency
        type: 0
        i18n:
          zh-CN: 写入延迟
          en-US: Write Latency
        unit: 'ms'

    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        iopsDeviceID: *******.4.1.48512.*********.1
        iopsReadPerSecond: *******.4.1.48512.*********.2
        iopsWritePerSecond: *******.4.1.48512.*********.3
        iopsTotalPerSecond: *******.4.1.48512.*********.4
        iopsReadLatency: *******.4.1.48512.*********.5
        iopsWriteLatency: *******.4.1.48512.*********.6

  # 新增：温度监控
  - name: temperatureMonitoringTable
    i18n:
      zh-CN: 温度监控
      en-US: Temperature Monitoring
    priority: 3
    fields:
      - field: tempSensorID
        type: 0
        i18n:
          zh-CN: 温度传感器ID
          en-US: Temperature Sensor ID
      - field: tempSensorLocation
        type: 1
        i18n:
          zh-CN: 传感器位置
          en-US: Sensor Location
      - field: cpuTemperature
        type: 0
        i18n:
          zh-CN: CPU温度
          en-US: CPU Temperature
        unit: '°C'
        label: true
      - field: systemTemperature
        type: 0
        i18n:
          zh-CN: 系统温度
          en-US: System Temperature
        unit: '°C'
        label: true
      - field: ambientTemperature
        type: 0
        i18n:
          zh-CN: 环境温度
          en-US: Ambient Temperature
        unit: '°C'
      - field: tempStatus
        type: 1
        i18n:
          zh-CN: 温度状态
          en-US: Temperature Status
      - field: tempThreshold
        type: 0
        i18n:
          zh-CN: 温度阈值
          en-US: Temperature Threshold
        unit: '°C'

    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        tempSensorID: *******.4.1.48512.**********.1
        tempSensorLocation: *******.4.1.48512.**********.2
        cpuTemperature: *******.4.1.48512.**********.3
        systemTemperature: *******.4.1.48512.**********.4
        ambientTemperature: *******.4.1.48512.**********.5
        tempStatus: *******.4.1.48512.**********.6
        tempThreshold: *******.4.1.48512.**********.7

  # 新增：风扇状态监控
  - name: fanStatusTable
    i18n:
      zh-CN: 风扇状态监控
      en-US: Fan Status Monitoring
    priority: 3
    fields:
      - field: fanID
        type: 0
        i18n:
          zh-CN: 风扇ID
          en-US: Fan ID
      - field: fanLocation
        type: 1
        i18n:
          zh-CN: 风扇位置
          en-US: Fan Location
      - field: fanStatus
        type: 1
        i18n:
          zh-CN: 风扇状态
          en-US: Fan Status
        label: true
      - field: fanSpeed
        type: 0
        i18n:
          zh-CN: 风扇转速
          en-US: Fan Speed
        unit: 'RPM'
        label: true
      - field: fanHealthStatus
        type: 1
        i18n:
          zh-CN: 风扇健康状态
          en-US: Fan Health Status
        label: true
      - field: fanRunningLevel
        type: 1
        i18n:
          zh-CN: 风扇运行级别
          en-US: Fan Running Level
      - field: fanSerialNumber
        type: 1
        i18n:
          zh-CN: 风扇序列号
          en-US: Fan Serial Number

    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        fanID: *******.4.1.48512.*********.1
        fanLocation: *******.4.1.48512.*********.2
        fanStatus: *******.4.1.48512.*********.3
        fanSpeed: *******.4.1.48512.*********.4
        fanHealthStatus: *******.4.1.48512.*********.5
        fanRunningLevel: *******.4.1.48512.*********.6
        fanSerialNumber: *******.4.1.48512.*********.7

  # 新增：硬件序列号监控
  - name: hardwareSerialTable
    i18n:
      zh-CN: 硬件序列号监控
      en-US: Hardware Serial Number Monitoring
    priority: 4
    fields:
      - field: hwComponentID
        type: 0
        i18n:
          zh-CN: 硬件组件ID
          en-US: Hardware Component ID
      - field: hwComponentType
        type: 1
        i18n:
          zh-CN: 硬件组件类型
          en-US: Hardware Component Type
      - field: hwComponentName
        type: 1
        i18n:
          zh-CN: 硬件组件名称
          en-US: Hardware Component Name
      - field: hwSerialNumber
        type: 1
        i18n:
          zh-CN: 硬件序列号
          en-US: Hardware Serial Number
      - field: hwManufacturer
        type: 1
        i18n:
          zh-CN: 制造商
          en-US: Manufacturer
      - field: hwModel
        type: 1
        i18n:
          zh-CN: 型号
          en-US: Model
      - field: hwFirmwareVersion
        type: 1
        i18n:
          zh-CN: 固件版本
          en-US: Firmware Version

    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hwComponentID: *******.4.1.48512.**********.1
        hwComponentType: *******.4.1.48512.**********.2
        hwComponentName: *******.4.1.48512.**********.3
        hwSerialNumber: *******.4.1.48512.**********.4
        hwManufacturer: *******.4.1.48512.**********.5
        hwModel: *******.4.1.48512.**********.6
        hwFirmwareVersion: *******.4.1.48512.**********.7
