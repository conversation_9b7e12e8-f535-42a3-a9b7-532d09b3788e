# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring mid-middleware custom-custom monitoring os-operating system monitoring
category: stored
# The monitoring type eg: linux windows tomcat mysql aws...
app: storage_3par
# The monitoring i18n name
name:
  zh-CN: HP 3PAR存储
  en-US: HP 3PAR Storage
# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # field-param field key
  - field: port
    # name-param field display i18n name
    name:
      zh-CN: REST API端口
      en-US: REST API Port
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    # required-true or false
    required: true
    # default value
    defaultValue: 8080
  # field-param field key
  - field: ssl
    # name-param field display i18n name
    name:
      zh-CN: 启用HTTPS
      en-US: Enable HTTPS
    # type-param field type(radio mapping the html radio tag)
    type: boolean
    # required-true or false
    required: false
    # default value
    defaultValue: false
  # field-param field key
  - field: username
    # name-param field display i18n name
    name:
      zh-CN: API用户名
      en-US: API Username
    # type-param field type(most mapping the html input type)
    type: text
    # when type is text, use limit to limit string length
    limit: 100
    # required-true or false
    required: true
    # param field input placeholder
    placeholder: 'API username for 3PAR system'
  # field-param field key
  - field: password
    # name-param field display i18n name
    name:
      zh-CN: API密码
      en-US: API Password
    # type-param field type(most mapping the html input type)
    type: password
    # required-true or false
    required: true
    # param field input placeholder
    placeholder: 'API password for 3PAR system'
  # field-param field key
  - field: apiVersion
    # name-param field display i18n name
    name:
      zh-CN: API版本
      en-US: API Version
    # type-param field type(most mapping the html input type)
    type: text
    # when type is text, use limit to limit string length
    limit: 10
    # required-true or false
    required: false
    # default value
    defaultValue: 'v1'
    # hide-is hide this field and put it in advanced layout
    hide: true
  # field-param field key
  - field: timeout
    # name-param field display i18n name
    name:
      zh-CN: 超时时间(ms)
      en-US: Timeout(ms)
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,100000]'
    # required-true or false
    required: false
    # hide-is hide this field and put it in advanced layout
    hide: true
    # default value
    defaultValue: 6000
# collect metrics config list
metrics:
  # metrics - system
  - name: system
    i18n:
      zh-CN: 系统信息
      en-US: System Information
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    fields:
      - field: systemName
        type: 1
        i18n:
          zh-CN: 系统名称
          en-US: System Name
      - field: systemModel
        type: 1
        i18n:
          zh-CN: 系统型号
          en-US: System Model
      - field: systemVersion
        type: 1
        i18n:
          zh-CN: 系统版本
          en-US: System Version
      - field: serialNumber
        type: 1
        i18n:
          zh-CN: 序列号
          en-US: Serial Number
      - field: systemStatus
        type: 0
        i18n:
          zh-CN: 系统状态
          en-US: System Status
      - field: uptime
        type: 0
        i18n:
          zh-CN: 运行时间
          en-US: Uptime
        unit: 'days'
      - field: responseTime
        type: 0
        i18n:
          zh-CN: 响应时间
          en-US: Response Time
        unit: ms
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: '/api/^_^apiVersion^_^/system'
      method: GET
      ssl: ^_^ssl^_^
      authorization:
        type: Basic Auth
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
      headers:
        Content-Type: application/json
        Accept: application/json
      timeout: ^_^timeout^_^
      parseType: jsonPath
      parseScript: |
        systemName=$.name
        systemModel=$.model
        systemVersion=$.systemVersion
        serialNumber=$.serialNumber
        systemStatus=$.overallSystemStatus
        uptime=$.uptimeSec
        responseTime=responseTime

  # metrics - controllers
  - name: controllers
    i18n:
      zh-CN: 控制器
      en-US: Controllers
    priority: 1
    fields:
      - field: controllerIndex
        type: 0
        i18n:
          zh-CN: 控制器索引
          en-US: Controller Index
      - field: controllerName
        type: 1
        i18n:
          zh-CN: 控制器名称
          en-US: Controller Name
        label: true
      - field: controllerState
        type: 0
        i18n:
          zh-CN: 控制器状态
          en-US: Controller State
      - field: cpuUsage
        type: 0
        i18n:
          zh-CN: CPU使用率
          en-US: CPU Usage
        unit: '%'
      - field: cacheUsage
        type: 0
        i18n:
          zh-CN: 缓存使用率
          en-US: Cache Usage
        unit: '%'
      - field: memoryTotal
        type: 0
        i18n:
          zh-CN: 总内存
          en-US: Total Memory
        unit: GB
      - field: memoryUsed
        type: 0
        i18n:
          zh-CN: 已用内存
          en-US: Used Memory
        unit: GB
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: '/api/^_^apiVersion^_^/nodes'
      method: GET
      ssl: ^_^ssl^_^
      authorization:
        type: Basic Auth
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
      headers:
        Content-Type: application/json
        Accept: application/json
      timeout: ^_^timeout^_^
      parseType: jsonPath
      parseScript: |
        # Parse controller information from nodes API
        controllerIndex=$.members[*].id
        controllerName=$.members[*].name
        controllerState=$.members[*].state
        cpuUsage=$.members[*].cpuUsage
        cacheUsage=$.members[*].cacheUsage
        memoryTotal=$.members[*].memoryMiB
        memoryUsed=$.members[*].memoryUsedMiB

  # metrics - storage pools (CPG)
  - name: storage_pools
    i18n:
      zh-CN: 存储池
      en-US: Storage Pools
    priority: 2
    fields:
      - field: poolIndex
        type: 0
        i18n:
          zh-CN: 存储池索引
          en-US: Pool Index
      - field: poolName
        type: 1
        i18n:
          zh-CN: 存储池名称
          en-US: Pool Name
        label: true
      - field: poolState
        type: 0
        i18n:
          zh-CN: 存储池状态
          en-US: Pool State
      - field: totalCapacity
        type: 0
        i18n:
          zh-CN: 总容量
          en-US: Total Capacity
        unit: GB
      - field: usedCapacity
        type: 0
        i18n:
          zh-CN: 已用容量
          en-US: Used Capacity
        unit: GB
      - field: freeCapacity
        type: 0
        i18n:
          zh-CN: 可用容量
          en-US: Free Capacity
        unit: GB
      - field: usagePercent
        type: 0
        i18n:
          zh-CN: 使用率
          en-US: Usage Percentage
        unit: '%'
    aliasFields:
      - poolIndex
      - poolName
      - poolState
      - totalCapacity
      - usedCapacity
      - freeCapacity
    calculates:
      - usagePercent = usedCapacity / totalCapacity * 100
    units:
      - totalCapacity=MB->GB
      - usedCapacity=MB->GB
      - freeCapacity=MB->GB
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: '/api/^_^apiVersion^_^/cpgs'
      method: GET
      ssl: ^_^ssl^_^
      authorization:
        type: Basic Auth
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
      headers:
        Content-Type: application/json
        Accept: application/json
      timeout: ^_^timeout^_^
      parseType: jsonPath
      parseScript: |
        # Parse CPG (Common Provisioning Group) information
        poolIndex=$.members[*].id
        poolName=$.members[*].name
        poolState=$.members[*].state
        totalCapacity=$.members[*].SACapacity.totalMiB
        usedCapacity=$.members[*].SACapacity.allocatedMiB
        freeCapacity=$.members[*].SACapacity.freeMiB

  # metrics - physical disks
  - name: physical_disks
    i18n:
      zh-CN: 物理磁盘
      en-US: Physical Disks
    priority: 2
    fields:
      - field: diskIndex
        type: 0
        i18n:
          zh-CN: 磁盘索引
          en-US: Disk Index
      - field: diskId
        type: 1
        i18n:
          zh-CN: 磁盘ID
          en-US: Disk ID
        label: true
      - field: diskType
        type: 1
        i18n:
          zh-CN: 磁盘类型
          en-US: Disk Type
      - field: diskState
        type: 0
        i18n:
          zh-CN: 磁盘状态
          en-US: Disk State
      - field: diskCapacity
        type: 0
        i18n:
          zh-CN: 磁盘容量
          en-US: Disk Capacity
        unit: GB
      - field: diskTemperature
        type: 0
        i18n:
          zh-CN: 磁盘温度
          en-US: Disk Temperature
        unit: '°C'
      - field: diskSerialNumber
        type: 1
        i18n:
          zh-CN: 磁盘序列号
          en-US: Disk Serial Number
      - field: diskManufacturer
        type: 1
        i18n:
          zh-CN: 制造商
          en-US: Manufacturer
      - field: diskModel
        type: 1
        i18n:
          zh-CN: 磁盘型号
          en-US: Disk Model
      - field: diskRPM
        type: 0
        i18n:
          zh-CN: 转速
          en-US: RPM
        unit: 'rpm'
    units:
      - diskCapacity=MB->GB
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: '/api/^_^apiVersion^_^/disks'
      method: GET
      ssl: ^_^ssl^_^
      authorization:
        type: Basic Auth
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
      headers:
        Content-Type: application/json
        Accept: application/json
      timeout: ^_^timeout^_^
      parseType: jsonPath
      parseScript: |
        # Parse Physical Disk information
        diskIndex=$.members[*].id
        diskId=$.members[*].id
        diskType=$.members[*].type
        diskState=$.members[*].state
        diskCapacity=$.members[*].totalMiB
        diskTemperature=$.members[*].temperature
        diskSerialNumber=$.members[*].serialNumber
        diskManufacturer=$.members[*].manufacturer
        diskModel=$.members[*].model
        diskRPM=$.members[*].RPM

  # metrics - volumes
  - name: volumes
    i18n:
      zh-CN: 卷
      en-US: Volumes
    priority: 2
    fields:
      - field: volumeIndex
        type: 0
        i18n:
          zh-CN: 卷索引
          en-US: Volume Index
      - field: volumeName
        type: 1
        i18n:
          zh-CN: 卷名称
          en-US: Volume Name
        label: true
      - field: volumeState
        type: 0
        i18n:
          zh-CN: 卷状态
          en-US: Volume State
      - field: volumeSize
        type: 0
        i18n:
          zh-CN: 卷大小
          en-US: Volume Size
        unit: GB
      - field: volumeUsed
        type: 0
        i18n:
          zh-CN: 已用空间
          en-US: Used Space
        unit: GB
      - field: volumeUsagePercent
        type: 0
        i18n:
          zh-CN: 使用率
          en-US: Usage Percentage
        unit: '%'
      - field: volumeType
        type: 1
        i18n:
          zh-CN: 卷类型
          en-US: Volume Type
      - field: volumePolicy
        type: 1
        i18n:
          zh-CN: 卷策略
          en-US: Volume Policy
    aliasFields:
      - volumeIndex
      - volumeName
      - volumeState
      - volumeSize
      - volumeUsed
      - volumeType
      - volumePolicy
    calculates:
      - volumeUsagePercent = volumeUsed / volumeSize * 100
    units:
      - volumeSize=MB->GB
      - volumeUsed=MB->GB
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: '/api/^_^apiVersion^_^/volumes'
      method: GET
      ssl: ^_^ssl^_^
      authorization:
        type: Basic Auth
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
      headers:
        Content-Type: application/json
        Accept: application/json
      timeout: ^_^timeout^_^
      parseType: jsonPath
      parseScript: |
        # Parse Virtual Volume information
        volumeIndex=$.members[*].id
        volumeName=$.members[*].name
        volumeState=$.members[*].state
        volumeSize=$.members[*].sizeMiB
        volumeUsed=$.members[*].userSpace.usedMiB
        volumeType=$.members[*].provisioningType
        volumePolicy=$.members[*].policies

  # metrics - performance IOPS
  - name: performance_iops
    i18n:
      zh-CN: IOPS性能
      en-US: IOPS Performance
    priority: 3
    fields:
      - field: systemReadIOPS
        type: 0
        i18n:
          zh-CN: 系统读IOPS
          en-US: System Read IOPS
        unit: 'ops/s'
      - field: systemWriteIOPS
        type: 0
        i18n:
          zh-CN: 系统写IOPS
          en-US: System Write IOPS
        unit: 'ops/s'
      - field: systemTotalIOPS
        type: 0
        i18n:
          zh-CN: 系统总IOPS
          en-US: System Total IOPS
        unit: 'ops/s'
      - field: systemReadThroughput
        type: 0
        i18n:
          zh-CN: 系统读吞吐量
          en-US: System Read Throughput
        unit: 'MB/s'
      - field: systemWriteThroughput
        type: 0
        i18n:
          zh-CN: 系统写吞吐量
          en-US: System Write Throughput
        unit: 'MB/s'
      - field: systemTotalThroughput
        type: 0
        i18n:
          zh-CN: 系统总吞吐量
          en-US: System Total Throughput
        unit: 'MB/s'
      - field: averageLatency
        type: 0
        i18n:
          zh-CN: 平均延迟
          en-US: Average Latency
        unit: 'ms'
    aliasFields:
      - systemReadIOPS
      - systemWriteIOPS
      - systemReadThroughput
      - systemWriteThroughput
      - averageLatency
    calculates:
      - systemTotalIOPS = systemReadIOPS + systemWriteIOPS
      - systemTotalThroughput = systemReadThroughput + systemWriteThroughput
    units:
      - systemReadThroughput=KB->MB
      - systemWriteThroughput=KB->MB
      - systemTotalThroughput=KB->MB
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: '/api/^_^apiVersion^_^/systemreporter/attime'
      method: GET
      ssl: ^_^ssl^_^
      authorization:
        type: Basic Auth
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
      headers:
        Content-Type: application/json
        Accept: application/json
      timeout: ^_^timeout^_^
      params:
        reportType: 'system'
        atTime: 'now'
      parseType: jsonPath
      parseScript: |
        # Parse performance statistics
        systemReadIOPS=$.sampleData[0].readIOPS
        systemWriteIOPS=$.sampleData[0].writeIOPS
        systemReadThroughput=$.sampleData[0].readThroughputKBPS
        systemWriteThroughput=$.sampleData[0].writeThroughputKBPS
        averageLatency=$.sampleData[0].totalServiceTimeMS

  # metrics - ports
  - name: ports
    i18n:
      zh-CN: 端口
      en-US: Ports
    priority: 3
    fields:
      - field: portIndex
        type: 0
        i18n:
          zh-CN: 端口索引
          en-US: Port Index
      - field: portName
        type: 1
        i18n:
          zh-CN: 端口名称
          en-US: Port Name
        label: true
      - field: portType
        type: 1
        i18n:
          zh-CN: 端口类型
          en-US: Port Type
      - field: portState
        type: 0
        i18n:
          zh-CN: 端口状态
          en-US: Port State
      - field: portSpeed
        type: 0
        i18n:
          zh-CN: 端口速度
          en-US: Port Speed
        unit: 'Gbps'
      - field: portMode
        type: 1
        i18n:
          zh-CN: 端口模式
          en-US: Port Mode
      - field: portProtocol
        type: 1
        i18n:
          zh-CN: 端口协议
          en-US: Port Protocol
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: '/api/^_^apiVersion^_^/ports'
      method: GET
      ssl: ^_^ssl^_^
      authorization:
        type: Basic Auth
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
      headers:
        Content-Type: application/json
        Accept: application/json
      timeout: ^_^timeout^_^
      parseType: jsonPath
      parseScript: |
        # Parse port information
        portIndex=$.members[*].portPos
        portName=$.members[*].portWWN
        portType=$.members[*].type
        portState=$.members[*].linkState
        portSpeed=$.members[*].configSpeed
        portMode=$.members[*].mode
        portProtocol=$.members[*].protocol

  # metrics - hardware health
  - name: hardware_health
    i18n:
      zh-CN: 硬件健康
      en-US: Hardware Health
    priority: 4
    fields:
      - field: systemTemperature
        type: 0
        i18n:
          zh-CN: 系统温度
          en-US: System Temperature
        unit: '°C'
      - field: controllerTemperature
        type: 0
        i18n:
          zh-CN: 控制器温度
          en-US: Controller Temperature
        unit: '°C'
      - field: fanStatus
        type: 0
        i18n:
          zh-CN: 风扇状态
          en-US: Fan Status
      - field: fanSpeed
        type: 0
        i18n:
          zh-CN: 风扇转速
          en-US: Fan Speed
        unit: 'rpm'
      - field: powerStatus
        type: 0
        i18n:
          zh-CN: 电源状态
          en-US: Power Status
      - field: powerConsumption
        type: 0
        i18n:
          zh-CN: 功耗
          en-US: Power Consumption
        unit: 'W'
      - field: batteryStatus
        type: 0
        i18n:
          zh-CN: 电池状态
          en-US: Battery Status
      - field: overallHealthStatus
        type: 0
        i18n:
          zh-CN: 整体健康状态
          en-US: Overall Health Status
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: '/api/^_^apiVersion^_^/hardware'
      method: GET
      ssl: ^_^ssl^_^
      authorization:
        type: Basic Auth
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
      headers:
        Content-Type: application/json
        Accept: application/json
      timeout: ^_^timeout^_^
      parseType: jsonPath
      parseScript: |
        # Parse hardware health information
        systemTemperature=$.members[?(@.type=='temperature')].value
        controllerTemperature=$.members[?(@.type=='controller_temp')].value
        fanStatus=$.members[?(@.type=='fan')].state
        fanSpeed=$.members[?(@.type=='fan')].speed
        powerStatus=$.members[?(@.type=='power_supply')].state
        powerConsumption=$.members[?(@.type=='power_supply')].powerConsumption
        batteryStatus=$.members[?(@.type=='battery')].state
        overallHealthStatus=$.overallState

  # metrics - alerts and events
  - name: alerts_events
    i18n:
      zh-CN: 告警事件
      en-US: Alerts and Events
    priority: 5
    fields:
      - field: criticalAlerts
        type: 0
        i18n:
          zh-CN: 严重告警数
          en-US: Critical Alerts
      - field: majorAlerts
        type: 0
        i18n:
          zh-CN: 主要告警数
          en-US: Major Alerts
      - field: minorAlerts
        type: 0
        i18n:
          zh-CN: 次要告警数
          en-US: Minor Alerts
      - field: informationalAlerts
        type: 0
        i18n:
          zh-CN: 信息告警数
          en-US: Informational Alerts
      - field: totalAlerts
        type: 0
        i18n:
          zh-CN: 总告警数
          en-US: Total Alerts
    aliasFields:
      - criticalAlerts
      - majorAlerts
      - minorAlerts
      - informationalAlerts
    calculates:
      - totalAlerts = criticalAlerts + majorAlerts + minorAlerts + informationalAlerts
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: '/api/^_^apiVersion^_^/eventlog'
      method: GET
      ssl: ^_^ssl^_^
      authorization:
        type: Basic Auth
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
      headers:
        Content-Type: application/json
        Accept: application/json
      timeout: ^_^timeout^_^
      params:
        query: 'severity:critical OR severity:major OR severity:minor OR severity:informational'
      parseType: jsonPath
      parseScript: |
        # Parse alert information
        criticalAlerts=$.members[?(@.severity=='critical')].length()
        majorAlerts=$.members[?(@.severity=='major')].length()
        minorAlerts=$.members[?(@.severity=='minor')].length()
        informationalAlerts=$.members[?(@.severity=='informational')].length()
