# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring custom-custom monitoring os-operating system monitoring
# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: service
# The monitoring type eg: linux windows tomcat mysql aws...
# 监控类型 eg: linux windows tomcat mysql aws...
app: fullsite
# The monitoring i18n name
# 监控类型国际化名称
name:
  zh-CN: SiteMap全站监控
  en-US: SITE MAP
# The description and help of this monitoring type
# 监控类型的帮助描述信息

# 监控所需输入参数定义(根据定义渲染页面UI)
# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # field-param field key
  - field: port
    # name-param field display i18n name
    name:
      zh-CN: 端口
      en-US: Port
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    # required-true or false
    required: true
    defaultValue: 80
  - field: sitemap
    name:
      zh-CN: 网站地图
      en-US: Site Map
    type: text
    limit: 200
    required: true
    placeholder: 'Web SITEMAP EG:/sitemap.xml'
  - field: ssl
    name:
      zh-CN: 启用HTTPS
      en-US: HTTPS
    # type-param field type(boolean mapping the html switch tag)
    type: boolean
    required: true

# collect metrics config list
metrics:
  # metrics - summary
  - name: summary
    i18n:
      zh-CN: 概要信息
      en-US: Summary
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    # collect metrics content
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      - field: url
        type: 1
        label: true
        i18n:
          zh-CN: URL
          en-US: URL
      - field: statusCode
        type: 1
        i18n:
          zh-CN: 响应状态码
          en-US: Status Code
      - field: responseTime
        type: 0
        unit: ms
        i18n:
          zh-CN: 响应时间
          en-US: Response Time
      - field: errorMsg
        type: 1
        i18n:
          zh-CN: 错误信息
          en-US: Error Msg
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: http
    # the config content when protocol is http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: ^_^sitemap^_^
      method: GET
      ssl: ^_^ssl^_^
      parseType: sitemap
