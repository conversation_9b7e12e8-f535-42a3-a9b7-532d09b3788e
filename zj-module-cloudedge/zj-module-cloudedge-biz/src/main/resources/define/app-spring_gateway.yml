# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring custom-custom monitoring os-operating system monitoring
# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: mid
# The monitoring type eg: springboot2 linux windows tomcat mysql aws...
# 监控类型 eg: springboot2 linux windows tomcat mysql aws...
app: spring_gateway
# The monitoring i18n name
# 监控类型国际化名称
name:
  zh-CN: SpringGateway
  en-US: SpringGateway
# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # field-param field key
  - field: port
    # name-param field display i18n name
    name:
      zh-CN: 端口
      en-US: Port
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    # required-true or false
    required: true
    # default value
    defaultValue: 8008
  # field-param field key
  - field: ssl
    # name-param field display i18n name
    name:
      zh-CN: 启动SSL
      en-US: SSL
    # When the type is boolean, the frontend will display a switch for it.
    type: boolean
    # required-true or false
    required: false
  # field-param field key
  - field: base_path
    # name-param field display i18n name
    name:
      zh-CN: Base Path
      en-US: Base Path
    # type-param field type(most mapping the html input type) The type "text" belongs to a text input field.
    type: text
    # default value
    defaultValue: /actuator
    # required-true or false
    required: true
    # hide-true or false
    hide: true
  # field-param field key
  - field: username
    # name-param field display i18n name
    name:
      zh-CN: Basic Auth Username
      en-US: Basic Auth Username
    # type-param field type(most mapping the html input type)
    type: text
    # when type is text, use limit to limit string length
    limit: 50
    # required-true or false
    required: false
    # hide param-true or false
    hide: true
  # field-param field key
  - field: password
    # name-param field display i18n name
    name:
      zh-CN: Basic Auth Password
      en-US: Basic Auth Password
    # type-param field type(most mapping the html input tag)
    type: password
    # required-true or false
    required: false
    # hide param-true or false
    hide: true
# collect metrics config list
metrics:
  # metrics - available
  - name: available
    i18n:
      zh-CN: 可用性
      en-US: Availability
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    # collect metrics content
    fields:
      # field-metric name, type-metric type(0-number,1-string), label-is label primary key, unit-metric unit
      - field: responseTime
        type: 0
        unit: ms
        i18n:
          zh-CN: 响应时间
          en-US: Response Time
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: http
    # Specific collection configuration when protocol is http protocol
    http:
      # http host: ipv4 ipv6 domain
      host: ^_^host^_^
      # http port
      port: ^_^port^_^
      # http url
      url: ^_^base_path^_^
      # http request method GET POST PUT DELETE PATCH
      method: GET
      # enable SSL/TLS, that is, whether it is http or https, the default is false
      ssl: ^_^ssl^_^
      # http auth
      authorization:
        # http auth type: Basic Auth, Digest Auth, Bearer Token
        type: Basic Auth
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
      # http response data parse type: default-hertzbeat rule, jsonpath-jsonpath script, website-api availability monitoring
      parseType: default

  - name: environment
    i18n:
      zh-CN: 环境
      en-US: Environment
    priority: 1
    fields:
      # The metric information, including field name, type of the field (0-number, 1-string), whether it is an instance primary key, and the unit of the metric.
      - field: profile
        type: 1
        i18n:
          zh-CN: 配置文件
          en-US: Profile
      - field: port
        type: 0
        i18n:
          zh-CN: 端口
          en-US: Port
      - field: os
        type: 1
        i18n:
          zh-CN: 操作系统
          en-US: Operating System
      - field: os_arch
        type: 1
        i18n:
          zh-CN: 操作系统架构
          en-US: Operating System Architecture
      - field: jdk_vendor
        type: 1
        i18n:
          zh-CN: JDK 供应商
          en-US: JDK Vendor
      - field: jvm_version
        type: 1
        i18n:
          zh-CN: JVM 版本
          en-US: JVM Version
    # metric alias list, used to identify metrics in query results
    aliasFields:
      - $.activeProfiles[0]
      - $.propertySources[?(@.name=='server.ports')].properties.*.value
      - $.propertySources[?(@.name=='systemProperties')].properties['os.name'].value
      - $.propertySources[?(@.name=='systemProperties')].properties['jdk.vendor.version'].value
      - $.propertySources[?(@.name=='systemProperties')].properties['java.runtime.version'].value
      - $.propertySources[?(@.name=='systemProperties')].properties['os.arch'].value
    # A list of calculation scripts for metric values.
    calculates:
      - profile=$.activeProfiles[0]
      - port=$.propertySources[?(@.name=='server.ports')].properties.*.value
      - os=$.propertySources[?(@.name=='systemProperties')].properties['os.name'].value
      - jdk_vendor=$.propertySources[?(@.name=='systemProperties')].properties['jdk.vendor.version'].value
      - jvm_version=$.propertySources[?(@.name=='systemProperties')].properties['java.runtime.version'].value
      - os_arch=$.propertySources[?(@.name=='systemProperties')].properties['os.arch'].value
    # The protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: http
    # Specific collection configuration when protocol is http protocol
    http:
      # http host: ipv4 ipv6 domain
      host: ^_^host^_^
      # http port
      port: ^_^port^_^
      # http url
      url: ^_^base_path^_^/env
      # http request method GET POST PUT DELETE PATCH
      method: GET
      # enable SSL/TLS, that is, whether it is http or https, the default is false
      ssl: ^_^ssl^_^
      # http auth
      authorization:
        # http auth type: Basic Auth, Digest Auth, Bearer Token
        type: Basic Auth
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
      # http response data parse type: default-hertzbeat rule, jsonpath-jsonpath script, website-api availability monitoring
      parseType: jsonPath
      # http response data parse script
      parseScript: '$'

  - name: thread_state
    i18n:
      zh-CN: 线程状态
      en-US: Thread State
    visible: false
    priority: 2
    fields:
      - field: state
        type: 1
        i18n:
          zh-CN: 状态
          en-US: State
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: ^_^base_path^_^/metrics/jvm.threads.states
      method: GET
      ssl: ^_^ssl^_^
      authorization:
        type: Basic Auth
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
      parseType: jsonPath
      parseScript: '$.availableTags[?(@.tag == "state")].values[*]'

  - name: threads
    i18n:
      zh-CN: 线程
      en-US: Threads
    priority: 3
    fields:
      - field: state
        type: 1
        i18n:
          zh-CN: 状态
          en-US: State
        label: true
      - field: size
        type: 0
        i18n:
          zh-CN: 数量
          en-US: Size
    aliasFields:
      - $.measurements[?(@.statistic == "VALUE")].value
    calculates:
      - state='^o^state^o^'
      - size=$.measurements[?(@.statistic == "VALUE")].value
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: ^_^base_path^_^/metrics/jvm.threads.states?tag=state:^o^state^o^
      method: GET
      ssl: ^_^ssl^_^
      authorization:
        type: Basic Auth
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
      parseType: jsonPath
      parseScript: '$'

  - name: space_name
    i18n:
      zh-CN: 空间名称
      en-US: Space Name
    visible: false
    priority: 4
    fields:
      - field: id
        type: 1
        i18n:
          zh-CN: 主键
          en-US: ID
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: ^_^base_path^_^/metrics/jvm.memory.used
      method: GET
      ssl: ^_^ssl^_^
      authorization:
        type: Basic Auth
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
      parseType: jsonPath
      parseScript: '$.availableTags[?(@.tag == "id")].values[*]'

  - name: memory_used
    i18n:
      zh-CN: 内存使用
      en-US: Memory Used
    priority: 5
    fields:
      - field: space
        type: 1
        i18n:
          zh-CN: 空间
          en-US: Space
        label: true
      - field: mem_used
        type: 0
        i18n:
          zh-CN: 内存使用
          en-US: Memory Used
        unit: MB
    aliasFields:
      - $.measurements[?(@.statistic == "VALUE")].value
    calculates:
      - space="^o^id^o^"
      - mem_used=$.measurements[?(@.statistic == "VALUE")].value
    units:
      - mem_used=B->MB
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: ^_^base_path^_^/metrics/jvm.memory.used?tag=id:^o^id^o^
      method: GET
      ssl: ^_^ssl^_^
      authorization:
        type: Basic Auth
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
      parseType: jsonPath
      parseScript: '$'

  - name: health
    i18n:
      zh-CN: 健康
      en-US: Health
    priority: 6
    fields:
      - field: status
        type: 1
        i18n:
          zh-CN: 状态
          en-US: Status
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: ^_^base_path^_^/health
      method: GET
      ssl: ^_^ssl^_^
      authorization:
        type: Basic Auth
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
      parseType: default

  - name: route_id
    i18n:
      zh-CN: 路由 ID
      en-US: Route ID
    visible: false
    priority: 7
    fields:
      - field: name
        type: 1
        i18n:
          zh-CN: 名称
          en-US: Name
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: ^_^base_path^_^/gateway/routes
      method: GET
      ssl: ^_^ssl^_^
      authorization:
        type: Basic Auth
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
      parseType: jsonPath
      parseScript: '$[*].route_id'

  - name: route_info
    i18n:
      zh-CN: 路由信息
      en-US: Route Info
    priority: 8
    fields:
      - field: route_id
        type: 1
        i18n:
          zh-CN: 路由 ID
          en-US: Route ID
        label: true
      - field: predicate
        type: 1
        i18n:
          zh-CN: 断言
          en-US: Predicate
      - field: uri
        type: 1
        i18n:
          zh-CN: URI
          en-US: URI
      - field: order
        type: 0
        i18n:
          zh-CN: 顺序
          en-US: Order
    aliasFields:
      - $.predicate
      - $.uri
      - $.order
    calculates:
      - route_id="^o^name^o^"
      - predicate=$.predicate
      - uri=$.uri
      - order=$.order
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: ^_^base_path^_^/gateway/routes/^o^name^o^
      method: GET
      ssl: ^_^ssl^_^
      authorization:
        type: Basic Auth
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
      parseType: jsonPath
      parseScript: '$'
