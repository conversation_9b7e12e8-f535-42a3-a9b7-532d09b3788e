# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring custom-custom monitoring os-operating system monitoring
# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: db
# The monitoring type eg: linux windows tomcat mysql aws...
# 监控类型 eg: linux windows tomcat mysql aws...
app: oracle
# The monitoring i18n name
# 监控类型国际化名称
name:
  zh-CN: Oracle
  en-US: Oracle DB
# 监控所需输入参数定义(根据定义渲染页面UI)
# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # field-param field key
  - field: port
    # name-param field display i18n name
    name:
      zh-CN: 端口
      en-US: Port
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    # required-true or false
    required: true
    # default value
    defaultValue: 1521
  - field: timeout
    name:
      zh-CN: 查询超时时间(ms)
      en-US: Query Timeout(ms)
    type: number
    range: '[400,200000]'
    required: false
    hide: true
    defaultValue: 6000
  - field: database
    name:
      zh-CN: 服务名
      en-US: Service Name
    type: text
    required: false
  - field: username
    name:
      zh-CN: 用户名
      en-US: Username
    type: text
    limit: 50
    required: false
  - field: password
    name:
      zh-CN: 密码
      en-US: Password
    type: password
    required: false
  - field: url
    name:
      zh-CN: URL
      en-US: URL
    type: text
    required: false
    hide: true

# collect metrics config list
metrics:
  # metrics - basic
  - name: basic
    i18n:
      zh-CN: 基本信息
      en-US: Basic Info
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    # collect metrics content
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      - field: database_version
        type: 1
        label: true
        i18n:
          zh-CN: 数据库版本
          en-US: Database Version
      - field: hostname
        type: 1
        i18n:
          zh-CN: 主机名
          en-US: Host Name
      - field: instance_name
        type: 1
        i18n:
          zh-CN: 实例名
          en-US: Instance Name
      - field: startup_time
        type: 1
        i18n:
          zh-CN: 启动时间
          en-US: Startup Time
      - field: status
        type: 1
        i18n:
          zh-CN: 状态
          en-US: Status
    # (optional)metrics field alias name, it is used as an alias field to map and convert the collected data and metrics field
    aliasFields:
      - VERSION
      - HOST_NAME
      - INSTANCE_NAME
      - STARTUP_TIME
      - STATUS
    # (optional)mapping and conversion expressions, use these and aliasField above to calculate metrics value
    # eg: cores=core1+core2, usage=usage, waitTime=allTime-runningTime
    calculates:
      - database_version=VERSION
      - hostname=HOST_NAME
      - instance_name=INSTANCE_NAME
      - startup_time=STARTUP_TIME
      - status=STATUS
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: jdbc
    # the config content when protocol is jdbc
    jdbc:
      # host: ipv4 ipv6 host
      host: ^_^host^_^
      port: ^_^port^_^
      # database platform name
      platform: oracle
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      # SQL Query Method：oneRow, multiRow, columns
      queryType: oneRow
      # sql
      sql: select * from sys.v_$instance
      # JDBC url
      url: ^_^url^_^

  - name: tablespace
    i18n:
      zh-CN: 表空间
      en-US: Tablespace
    priority: 1
    fields:
      - field: file_id
        type: 1
        label: true
        i18n:
          zh-CN: 文件ID
          en-US: File ID
      - field: file_name
        type: 1
        i18n:
          zh-CN: 文件名
          en-US: File Name
      - field: tablespace_name
        type: 1
        i18n:
          zh-CN: 表空间名
          en-US: Tablespace Name
      - field: status
        type: 1
        i18n:
          zh-CN: 状态
          en-US: Status
      - field: bytes
        type: 0
        i18n:
          zh-CN: 字节数
          en-US: Bytes
        unit: MB
      - field: blocks
        type: 0
        i18n:
          zh-CN: 块数
          en-US: Blocks
        unit: 块
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: oracle
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      queryType: multiRow

      sql: select file_id, file_name, tablespace_name, status, bytes / 1024 / 1024 as bytes, blocks　from dba_data_files
      url: ^_^url^_^

  - name: total_sessions
    i18n:
      zh-CN: 会话总数
      en-US: Total Sessions
    priority: 2
    fields:
      - field: count
        type: 0
        i18n:
          zh-CN: 总数
          en-US: Count
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: oracle
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      queryType: oneRow
      sql: select count(*) as count from v$session
      url: ^_^url^_^

  - name: active_sessions
    i18n:
      zh-CN: 活动会话
      en-US: Active Sessions
    priority: 3
    fields:
      - field: count
        type: 0
        i18n:
          zh-CN: 总数
          en-US: Count
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: oracle
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      queryType: oneRow
      sql: select count(*) as count from v$session where username is not null and status = 'ACTIVE'
      url: ^_^url^_^

  - name: background_sessions
    i18n:
      zh-CN: 后台会话
      en-US: Background Sessions
    priority: 4
    fields:
      - field: count
        type: 0
        i18n:
          zh-CN: 总数
          en-US: Count
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: oracle
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      queryType: oneRow
      sql: select count(*) as count from v$session where username is null
      url: ^_^url^_^

  - name: connection
    i18n:
      zh-CN: 连接
      en-US: Connection
    priority: 5
    fields:
      - field: username
        type: 1
        i18n:
          zh-CN: 用户名
          en-US: Username
        label: true
      - field: count
        i18n:
          zh-CN: 总数
          en-US: Count
        type: 0
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: oracle
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      queryType: oneRow
      sql: SELECT username, count( username ) as count FROM v$session WHERE username IS NOT NULL GROUP BY username
      url: ^_^url^_^

  - name: performance
    i18n:
      zh-CN: 性能
      en-US: Performance
    priority: 6
    fields:
      - field: qps
        type: 0
        i18n:
          zh-CN: 每秒查询数
          en-US: QPS
        unit: qps
      - field: tps
        type: 0
        i18n:
          zh-CN: 每秒事务数
          en-US: TPS
        unit: tps
      - field: mbps
        type: 0
        i18n:
          zh-CN: 每秒IO读写
          en-US: MBPS
        unit: mbps
    aliasFields:
      - I/O Requests per Second
      - User Transaction Per Sec
      - I/O Megabytes per Second
    calculates:
      - qps=I/O Requests per Second
      - tps=User Transaction Per Sec
      - mbps=I/O Megabytes per Second
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: oracle
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      queryType: columns
      sql: select metric_name, value from gv$sysmetric where metric_name = 'I/O Megabytes per Second' or  metric_name = 'User Transaction Per Sec' or metric_name = 'I/O Requests per Second'
      url: ^_^url^_^


  - name: percentage
    i18n:
      zh-CN: 百分比
      en-US: Percentage
    priority: 7
    fields:
      - field: tablespace_name
        type: 1
        i18n:
          zh-CN: 表空间名
          en-US: Tablespace Name
        label: true
      - field: total
        i18n:
          zh-CN: 全部
          en-US: Total
        type: 0
      - field: used
        i18n:
          zh-CN: 已用
          en-US: Used
        type: 0
      - field: free
        i18n:
          zh-CN: 空闲
          en-US: Free
        type: 0
      - field: used_percentage
        type: 0
        i18n:
          zh-CN: 已用百分比
          en-US: Used Percentage
        unit: '%'
      - field: free_percentage
        type: 0
        i18n:
          zh-CN: 空闲百分比
          en-US: Free Percentage
        unit: '%'
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: oracle
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      queryType: multiRow
      # DBA_TABLESPACE_USAGE_METRICS可以查出表空间used_max值，它的大小计算单位是block，1kb=8block，把block*8/1024转化为MB单位
      sql: "SELECT tablespace_name,ROUND ( (TABLESPACE_SIZE * 8 / 1024), 0) AS total,ROUND ( (USED_SPACE * 8 / 1024), 0) AS used,ROUND ( ( (TABLESPACE_SIZE * 8 / 1024) - (USED_SPACE * 8 / 1024)), 0) AS free,ROUND ( (USED_PERCENT), 0) AS used_percentage,100 - ROUND ( (USED_PERCENT), 0) AS free_percentage FROM sys.dba_tablespace_usage_metrics ORDER BY used_percent DESC"
      url: ^_^url^_^

  - name: process
    i18n:
      zh-CN: 进程
      en-US: Process
    priority: 8
    fields:
      - field: process_count
        type: 0
        i18n:
          zh-CN: 进程数
          en-US: Process Count
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: oracle
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      queryType: oneRow
      sql: select count(*) as process_count from v$process
      url: ^_^url^_^

  - name: transaction
    i18n:
      zh-CN: 事务
      en-US: Transaction
    priority: 9
    fields:
      - field: commits
        type: 0
        unit: 't/s'
        i18n:
          zh-CN: 提交数
          en-US: Commits
      - field: rollbacks
        type: 0
        unit: 't/s'
        i18n:
          zh-CN: 回滚数
          en-US: Rollbacks
    aliasFields:
      - User Commits Per Sec
      - User Rollbacks Per Sec
    calculates:
      - commits=User Commits Per Sec
      - rollbacks=User Rollbacks Per Sec
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: oracle
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      queryType: columns
      sql: select metric_name, value from gv$sysmetric where metric_name = 'User Commits Per Sec' or  metric_name = 'User Rollbacks Per Sec'
      url: ^_^url^_^

  - name: wait
    i18n:
      zh-CN: 等待
      en-US: Wait
    priority: 10
    fields:
      - field: concurrent_wait_time
        type: 0
        unit: ms
        i18n:
          zh-CN: 并发等待时间
          en-US: Concurrent Wait Time
      - field: commit_wait_time
        type: 0
        unit: ms
        i18n:
          zh-CN: 提交等待时间
          en-US: Commit Wait Time
      - field: app_wait_time
        type: 0
        unit: ms
        i18n:
          zh-CN: 应用等待时间
          en-US: Application Wait Time
      - field: network_wait_time
        type: 0
        unit: us
        i18n:
          zh-CN: 网络等待时间
          en-US: Network Wait Time
      - field: system_io_wait_time
        type: 0
        unit: ms
        i18n:
          zh-CN: 系统I/O等待时间
          en-US: System I/O Wait Time
      - field: user_io_wait_time
        type: 0
        unit: ms
        i18n:
          zh-CN: 用户I/O等待时间
          en-US: User I/O Wait Time
      - field: configure_wait_time
        type: 0
        unit: ms
        i18n:
          zh-CN: 配置等待时间
          en-US: Configure Wait Time
      - field: scheduler_wait_time
        type: 0
        unit: ms
        i18n:
          zh-CN: 调度等待时间
          en-US: Scheduler Wait Time
    aliasFields:
      - System I/O
      - Application
      - User I/O
      - Commit
      - Concurrency
      - Configuration
      - Idle
      - Other
    calculates:
      - system_io_wait_time=System I/O
      - app_wait_time=Application
      - user_io_wait_time=User I/O
      - commit_wait_time=Commit
      - concurrent_wait_time=Concurrency
      - configure_wait_time=Configuration
      - scheduler_wait_time=Idle
      - network_wait_time=Other
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: oracle
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      queryType: columns
      sql: select wait_class, sum(time_waited) total_wait_time from v$active_session_history where session_state = 'WAITING' GROUP BY wait_class ORDER BY total_wait_time DESC
      url: ^_^url^_^

  - name: cpu_stats
    i18n:
      zh-CN: CPU状态
      en-US: CPU Stats
    priority: 11
    fields:
      - field: type
        type: 1
        label: true
        i18n:
          zh-CN: 类型
          en-US: Type
      - field: num
        type: 1
        i18n:
          zh-CN: 数量
          en-US: Num
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: oracle
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      queryType: multiRow
      sql: select stat_name as type, value as num from v$osstat where stat_name like '%CPU%' or stat_name like '%TIME'
      url: ^_^url^_^

  - name: mem_stats
    i18n:
      zh-CN: 内存状态
      en-US: Memory Stats
    priority: 12
    fields:
      - field: type
        type: 1
        label: true
        i18n:
          zh-CN: 类型
          en-US: Type
      - field: num
        type: 1
        i18n:
          zh-CN: 数量
          en-US: Num
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: oracle
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      queryType: multiRow
      sql: select stat_name as type, value as num from v$osstat where stat_name like '%BYTES'
      url: ^_^url^_^

  - name: cache_hit_ratio
    i18n:
      zh-CN: 缓存命中率
      en-US: Cache Hit Ratio
    priority: 13
    fields:
      - field: lib_cache_hit_ratio
        type: 0
        i18n:
          zh-CN: 库缓存命中率
          en-US: LIB CACHE HIT RATIO
      - field: buffer_cache_hit_ratio
        type: 0
        i18n:
          zh-CN: 缓冲区缓存命中率
          en-US: BUFFER CACHE HIT RATIO
    aliasFields:
      - Library Cache Hit Ratio
      - Buffer Cache Hit Ratio
    calculates:
      - lib_cache_hit_ratio=Library Cache Hit Ratio
      - buffer_cache_hit_ratio=Buffer Cache Hit Ratio
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: oracle
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      queryType: columns
      sql: select metric_name, value from gv$sysmetric where metric_name like '%Cache Hit Ratio' order by end_time asc
      url: ^_^url^_^

  - name: slow_query
    i18n:
      zh-CN: 慢查询
      en-US: Slow Query
    priority: 14
    fields:
      - field: sql_id
        type: 1
        i18n:
          zh-CN: sql 主键
          en-US: SQL ID
      - field: child_number
        type: 1
        i18n:
          zh-CN: 子编号
          en-US: Child Number
      - field: executions
        type: 1
        i18n:
          zh-CN: 执行数
          en-US: EXECUTIONS
        unit: 次
      - field: per_secs
        type: 1
        i18n:
          zh-CN: 每秒执行数
          en-US: Per Secs
        unit: 秒
      - field: cpu_secs
        type: 1
        i18n:
          zh-CN: 每秒CPU
          en-US: CPU Secs
        unit: 秒
      - field: buffer_gets
        type: 1
        i18n:
          zh-CN: 获得的缓冲区
          en-US: Buffer Gets
      - field: disk_reads
        type: 1
        i18n:
          zh-CN: 磁盘读取
          en-US: Disk Reads
      - field: fetches
        type: 1
        i18n:
          zh-CN: 获取数量
          en-US: Fetches
      - field: parse_calls
        type: 1
        i18n:
          zh-CN: 解析调用
          en-US: Parse Calls
      - field: optimizer_cost
        type: 1
        i18n:
          zh-CN: 优化器成本
          en-US: Optimizer Cost
      - field: sql_text
        type: 1
        i18n:
          zh-CN: SQL文本
          en-US: SQL Text
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: oracle
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      queryType: multiRow
      sql: SELECT * FROM (SELECT sql_id, child_number, executions, ROUND(CASE WHEN executions = 0 THEN NULL ELSE elapsed_time / (executions*1000000) END,4) AS per_secs, cpu_time / 1000000 AS cpu_secs, buffer_gets, disk_reads, fetches, parse_calls, optimizer_cost, sql_text FROM  v$sql  ) where rownum <= 10 ORDER BY  per_secs DESC
      url: ^_^url^_^
