# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring mid-middleware custom-custom monitoring os-operating system monitoring
# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: db
# The monitoring type eg: linux windows tomcat mysql aws...
# 监控类型 eg: linux windows tomcat mysql aws...
app: dm
# The monitoring i18n name
# 监控类型国际化名称
name:
  zh-CN: 达梦
  en-US: DM DB
# The description and help of this monitoring type
# 监控类型的帮助描述信息

# 监控所需输入参数定义(根据定义渲染页面UI)
# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # field-param field key
  - field: port
    # name-param field display i18n name
    name:
      zh-CN: 端口
      en-US: Port
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    # required-true or false
    required: true
    # default value
    defaultValue: 5236
    # param field input placeholder
    placeholder: 'Please input port'
  # field-param field key
  - field: username
    # name-param field display i18n name
    name:
      zh-CN: 用户名
      en-US: Username
    # type-param field type(most mapping the html input type)
    type: text
    # when type is text, use limit to limit string length
    limit: 50
    # required-true or false
    required: true
    # hide param-true or false
    hide: true
  # field-param field key
  - field: password
    # name-param field display i18n name
    name:
      zh-CN: 用户密码
      en-US: Password
    # type-param field type(most mapping the html input tag)
    type: password
    # required-true or false
    required: true
    # hide param-true or false
    hide: true
  # field-param field key
  - field: timeout
    # name-param field display i18n name
    name:
      zh-CN: 超时时间
      en-US: Timeout
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[400,200000]'
    # required-true or false
    required: true
    # default value
    defaultValue: 3000
# collect metrics config list
metrics:
  # metrics - basic
  - name: basic
    i18n:
      zh-CN: 基本信息
      en-US: Basic Info
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    # collect metrics content
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      - field: PORT_NUM
        i18n:
          zh-CN: 端口号
          en-US: Port Number
        type: 1
      - field: CTL_PATH
        i18n:
          zh-CN: 控制路径
          en-US: Control Path
        type: 1
      - field: MAX_SESSIONS
        i18n:
          zh-CN: 最大会话数
          en-US: Max Sessions
        type: 0
    # (optional)metrics field alias name, it is used as an alias field to map and convert the collected data and metrics field
    aliasFields:
      - PORT_NUM
      - CTL_PATH
      - MAX_SESSIONS
    # mapping and conversion expressions, use these and aliasField above to calculate metrics value
    # eg: cores=core1+core2, usage=usage, waitTime=allTime-runningTime
    calculates:
      - PORT_NUM=PORT_NUM
      - CTL_PATH=CTL_PATH
      - MAX_SESSIONS=MAX_SESSIONS
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: jdbc
    # the config content when protocol is jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: dm
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      # SQL Query Method：oneRow, multiRow, columns
      queryType: columns
      # script sql
      sql: select PARA_NAME, PARA_VALUE from SYS."V$DM_INI" where PARA_NAME = 'MAX_SESSIONS'or PARA_NAME = 'CTL_PATH' or PARA_NAME = 'PORT_NUM';
      # JDBC url
      url: ^_^url^_^

  - name: status
    i18n:
      zh-CN: 状态
      en-US: Status
    priority: 1
    fields:
      - field: status
        i18n:
          zh-CN: 状态
          en-US: Status
        type: 1
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: dm
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      # SQL Query Method：oneRow, multiRow, columns
      queryType: oneRow
      sql: SELECT status$ as status FROM v$instance;
      url: ^_^url^_^


  - name: thread
    i18n:
      zh-CN: 线程
      en-US: Thread
    priority: 2
    fields:
      - field: dm_sql_thd
        i18n:
          zh-CN: SQL线程数
          en-US: SQL Thread
        type: 0
      - field: dm_io_thd
        i18n:
          zh-CN: I/O线程数
          en-US: I/O Thread
        type: 0
      - field: dm_quit_thd
        i18n:
          zh-CN: 退出线程数
          en-US: Quit Thread
        type: 0
    aliasFields:
      - dm_sql_thd
      - dm_io_thd
      - dm_quit_thd
    calculates:
      - dm_sql_thd=dm_sql_thd
      - dm_io_thd=dm_io_thd
      - dm_quit_thd=dm_quit_thd
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: dm
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      # SQL Query Method：oneRow, multiRow, columns
      queryType: columns
      sql: SELECT DISTINCT NAME, COUNT(*)  AS NUM FROM V$THREADS GROUP BY NAME ORDER BY NUM DESC;
      url: ^_^url^_^
