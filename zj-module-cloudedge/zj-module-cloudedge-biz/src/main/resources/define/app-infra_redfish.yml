# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring custom-custom monitoring os-operating system monitoring
# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: infra
# The monitoring type eg: linux windows tomcat mysql aws...
# 监控类型 eg: linux windows tomcat mysql aws...
app: infra_redfish
# The monitoring i18n name
# 监控类型国际化名称
name:
  zh-CN: Redfish监控
  en-US: Redfish
params:
  # field-param field key
  # field-变量字段标识符
  - field: host
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: host
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: true
  # field-param field key
  # field-变量字段标识符
  - field: port
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 端口
      en-US: Port
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: number
    # when type is number, range is required
    # 当type为number时,用range表示范围
    range: '[0,65535]'
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: true
    # default value
    # 默认值
    defaultValue: 443
  # field-param field key
  # field-变量字段标识符
  - field: timeout
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 查询超时时间
      en-US: Query Timeout
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: number
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: false
    # hide param-true or false
    # 是否隐藏字段 true or false
    hide: true
    # default value
    # 默认值
    defaultValue: 6000
  # field-param field key
  # field-变量字段标识符
  - field: username
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 用户名
      en-US: Username
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: text
    # when type is text, use limit to limit string length
    # 当type为text时,用limit表示字符串限制大小
    limit: 50
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: false
  # field-param field key
  # field-变量字段标识符
  - field: password
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 密码
      en-US: Password
    # type-param field type(most mapping the html input tag)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: password
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: false
# collect metrics config list
# 采集指标配置列表
metrics:
  # metrics - cpu
  # 监控指标 - cpu
  - name: Chassis
    i18n:
      zh-CN: 面板信息
      en-US: Chassis
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    # 指标采集调度优先级(0->127)->(优先级高->低) 优先级低的指标会等优先级高的指标采集完成后才会被调度, 相同优先级的指标会并行调度采集
    # 优先级为0的指标为可用性指标,即它会被首先调度,采集成功才会继续调度其它指标,采集失败则中断调度
    priority: 0
    # collect metrics content
    # 具体监控指标列表
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      # field-指标名称, type-指标类型(0-number数字,1-string字符串), unit-指标单位('%','ms','MB'), label-是否是指标标签字段
      - field: id
        type: 1
        i18n:
          zh-CN: 机箱唯一标识符
          en-US: Chassis Unique Identifier
      - field: name
        type: 1
        i18n:
          zh-CN: 机箱名称
          en-US: Chassis Name
      - field: type
        type: 1
        i18n:
          zh-CN: 机箱类型
          en-US: Chasis Type
      - field: state
        type: 1
        i18n:
          zh-CN: 机箱状态
          en-US: Chasis State
      - field: health
        type: 1
        i18n:
          zh-CN: 机箱健康状态
          en-US: Chasis Health
    # (optional)metrics field alias name, it is used as an alias field to map and convert the collected data and metrics field
    # (可选)监控指标别名, 做为中间字段与采集数据字段和指标字段映射转换
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: redfish
    # the config content when protocol is redfish
    redfish:
      # redfish host: ipv4 ipv6 domain
      host: ^_^host^_^
      # redfish port
      port: ^_^port^_^
      # redfish username
      username: ^_^username^_^
      # redfish password
      password: ^_^password^_^
      # timeout unit：ms
      timeout: ^_^timeout^_^
      jsonPath:
        - $.['@odata.id']
        - $.Name
        - $.ChassisType
        - $.Status.State
        - $.Status.Health

  - name: Battery
    i18n:
      zh-CN: 电池
      en-US: Battery
    priority: 1
    fields:
      - field: id
        type: 1
        i18n:
          zh-CN: 电池唯一标识符
          en-US: Battery Unique Identifier
      - field: name
        type: 1
        i18n:
          zh-CN: 电池名称
          en-US: Battery Name
      - field: state
        type: 1
        i18n:
          zh-CN: 电池状态
          en-US: Battery State
      - field: health
        type: 1
        i18n:
          zh-CN: 电池健康状态
          en-US: Battery Health
      - field: charge_status
        type: 1
        i18n:
          zh-CN: 电池充电状态
          en-US: Battery Charge Status
    protocol: redfish
    redfish:
      # redfish host: ipv4 ipv6 domain
      host: ^_^host^_^
      # redfish port
      port: ^_^port^_^
      # redfish username
      username: ^_^username^_^
      # redfish password
      password: ^_^password^_^
      # timeout unit：ms
      timeout: ^_^timeout^_^
      jsonPath:
        - $.['@odata.id']
        - $.Name
        - $.Status.State
        - $.Status.Health
        - $.ChargeState

  - name: Fan
    i18n:
      zh-CN: 风扇
      en-US: Fan
    priority: 2
    fields:
      - field: id
        type: 1
        i18n:
          zh-CN: 风扇唯一标识符
          en-US: Fan Unique Identifier
      - field: name
        type: 1
        i18n:
          zh-CN: 风扇名称
          en-US: Fan Name
      - field: state
        type: 1
        i18n:
          zh-CN: 风扇状态
          en-US: Fan State
      - field: health
        type: 1
        i18n:
          zh-CN: 风扇健康状态
          en-US: Fan Health
      - field: temperature
        type: 0
        i18n:
          zh-CN: 传感器温度
          en-US: Sensor Temperature
      - field: speed
        type: 0
        i18n:
          zh-CN: 风扇转速
          en-US: Fan Speed
    protocol: redfish
    redfish:
      # redfish host: ipv4 ipv6 domain
      host: ^_^host^_^
      # redfish port
      port: ^_^port^_^
      # redfish username
      username: ^_^username^_^
      # redfish password
      password: ^_^password^_^
      # timeout unit：ms
      timeout: ^_^timeout^_^
      # redfish fan collection schema
      schema: /redfish/v1/Chassis/{ChassisId}/ThermalSubsystem/Fans
      jsonPath:
        - $.['@odata.id']
        - $.Name
        - $.Status.State
        - $.Status.Health
        - $.SpeedPercent.Reading
        - $.SpeedPercent.SpeedRPM
