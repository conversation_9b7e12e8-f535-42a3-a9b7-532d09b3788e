category: network
app: switch_h3c_wireless
name:
  zh-CN: 华三无线控制器
  en-US: H3C Wireless Controller
params:
  - field: host
    name:
      zh-CN: 目标Host
      en-US: Target Host
    type: host
    required: true
  - field: port

    name:
      zh-CN: 端口
      en-US: Port

    type: number

    range: '[0,65535]'

    required: true

    defaultValue: 161

  - field: version
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: SNMP 版本
      en-US: SNMP Version
    # type-param field type(radio mapping the html radio tag)
    # type-当type为radio时,前端用radio展示开关
    type: radio
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: true
    # when type is radio checkbox, use option to show optional values {name1:value1,name2:value2}
    # 当type为radio单选框, checkbox复选框时, option表示可选项值列表 {name1:value1,name2:value2}
    options:
      - label: SNMPv1
        value: 0
      - label: SNMPv2c
        value: 1
      - label: SNMPv3
        value: 3
  # field-param field key
  # field-变量字段标识符
  - field: community
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: SNMP 团体字
      en-US: SNMP Community
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: text
    # when type is text, use limit to limit string length
    # 当type为text时,用limit表示字符串限制大小
    limit: 100
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: false
    # 参数输入框提示信息
    # param field input placeholder
    placeholder: 'Snmp community for v1 v2c'
  # field-param field key
  # field-变量字段标识符
  - field: username
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: SNMP username
      en-US: SNMP username
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: text
    # when type is text, use limit to limit string length
    # 当type为text时,用limit表示字符串限制大小
    limit: 20
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: false
    # 参数输入框提示信息
    # param field input placeholder
    placeholder: 'Snmp username for v3'
  # field-param field key
  # field-变量字段标识符
  - field: contextName
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: SNMP contextName
      en-US: SNMP contextName
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: text
    # when type is text, use limit to limit string length
    # 当type为text时,用limit表示字符串限制大小
    limit: 100
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: false
    # 参数输入框提示信息
    # param field input placeholder
    placeholder: 'Snmp contextName for v3'
    # field-param field key
    # field-变量字段标识符
  - field: authPassphrase
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: SNMP authPassword
      en-US: SNMP authPassword
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: text
    # when type is text, use limit to limit string length
    # 当type为text时,用limit表示字符串限制大小
    limit: 100
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: false
    # 参数输入框提示信息
    # param field input placeholder
    placeholder: 'Snmp authPassword for v3'
    # field-param field key
    # field-变量字段标识符
  - field: authPasswordEncryption
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: authPassword 加密方式
      en-US: authPassword Encryption
    # type-param field type(radio mapping the html radio tag)
    # type-当type为radio时,前端用radio展示开关
    type: radio
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: false
    # when type is radio checkbox, use option to show optional values {name1:value1,name2:value2}
    # 当type为radio单选框, checkbox复选框时, option表示可选项值列表 {name1:value1,name2:value2}
    options:
      - label: MD5
        value: 0
      - label: SHA1
        value: 1
    # field-param field key
    # field-变量字段标识符
  - field: privPassphrase
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: SNMP privPassphrase
      en-US: SNMP privPassphrase
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: text
    # when type is text, use limit to limit string length
    # 当type为text时,用limit表示字符串限制大小
    limit: 100
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: false
    # 参数输入框提示信息
    # param field input placeholder
    placeholder: 'Snmp authPassword for v3'
    # field-param field key
    # field-变量字段标识符
  - field: privPasswordEncryption
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: privPassword 加密方式
      en-US: privPassword Encryption
    # type-param field type(radio mapping the html radio tag)
    # type-当type为radio时,前端用radio展示开关
    type: radio
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: false
    # when type is radio checkbox, use option to show optional values {name1:value1,name2:value2}
    # 当type为radio单选框, checkbox复选框时, option表示可选项值列表 {name1:value1,name2:value2}
    options:
      - label: DES
        value: 0
      - label: AES128
        value: 1
    # field-param field key
    # field-变量字段标识符
  - field: timeout
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 超时时间(ms)
      en-US: Timeout(ms)
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: number
    # when type is number, range is required
    # 当type为number时,用range表示范围
    range: '[0,100000]'
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: false
    # hide-is hide this field and put it in advanced layout
    # hide-是否隐藏此参数将其放入高级设置中
    hide: true
    # default value
    # 默认值
    defaultValue: 6000
# collect metrics config list
# 采集指标配置列表
metrics:
  # 系统指标
  - name: system
    i18n:
      zh-CN: 系统信息
      en-US: System Info
    priority: 0
    fields:
      - field: name
        type: 1
        i18n:
          zh-CN: 主机名称
          en-US: Host Name
      - field: descr
        type: 1
        i18n:
          zh-CN: 描述信息
          en-US: Description
      - field: uptime
        type: 1
        i18n:
          zh-CN: 运行时长
          en-US: Uptime
      - field: location
        type: 1
        i18n:
          zh-CN: 位置
          en-US: Location
      - field: contact
        type: 1
        i18n:
          zh-CN: 联系人
          en-US: Contact
      - field: responseTime
        type: 0
        unit: ms
        i18n:
          zh-CN: 响应时间
          en-US: Response Time
      - field: serialNumber
        type: 1
        i18n:
          zh-CN: SN号
          en-US: Serial Number
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      authPassphrase: ^_^authPassphrase^_^
      privPassphrase: ^_^privPassphrase^_^
      privPasswordEncryption: ^_^privPasswordEncryption^_^
      authPasswordEncryption: ^_^authPasswordEncryption^_^
      contextName: ^_^contextName^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: get
      oids:
        name: *******.*******.0
        descr: *******.*******.0
        uptime: *******.*******.0
        location: *******.*******.0
        contact: *******.*******.0
        serialNumber: *******.4.1.25506.********.1.0

  - name: interface
    i18n:
      zh-CN: 接口详情
      en-US: Interfaces Detail
    priority: 1
    fields:
      - field: index
        type: 1
        i18n:
          zh-CN: 编号
          en-US: Index
      - field: interface_name
        type: 1
        label: true
        i18n:
          zh-CN: 接口名称
          en-US: Interface Name
      - field: alias
        type: 1
        i18n:
          zh-CN: 接口备注
          en-US: alias
      - field: admin_status
        type: 1
        i18n:
          zh-CN: 配置状态
          en-US: Config Status
      - field: oper_status
        type: 1
        i18n:
          zh-CN: 当前状态
          en-US: Current Status
      - field: mtu
        type: 0
        unit: 'byte'
        i18n:
          zh-CN: MTU
          en-US: MTU
      - field: speed
        type: 0
        unit: 'MB/s'
        i18n:
          zh-CN: 接口速率
          en-US: Interface Speed
      - field: in_octets
        type: 0
        unit: 'Mbps'
        i18n:
          zh-CN: 入流量
          en-US: In Octets
      - field: in_discards
        type: 0
        unit: 'package'
        i18n:
          zh-CN: 入丢包数
          en-US: In Discards
      - field: in_errors
        type: 0
        unit: 'package'
        i18n:
          zh-CN: 入错包数
          en-US: In Errors
      - field: out_octets
        type: 0
        unit: 'Mbps'
        i18n:
          zh-CN: 出流量
          en-US: Out Octets
      - field: out_discards
        type: 0
        unit: 'package'
        i18n:
          zh-CN: 出丢包数
          en-US: Out Discards
      - field: out_errors
        type: 0
        unit: 'package'
        i18n:
          zh-CN: 出错包数
          en-US: Out Errors
    # (optional)metrics field alias name, it is used as an alias field to map and convert the collected data and metrics field
    # (可选)监控指标别名, 做为中间字段与采集数据字段和指标字段映射转换
    aliasFields:
      - ifIndex
      - ifDescr
      - ifMtu
      - ifSpeed
      - ifInOctets
      - ifInDiscards
      - ifInErrors
      - ifOutOctets
      - ifOutDiscards
      - ifOutErrors
      - ifAdminStatus
      - ifOperStatus
      - ifAlias
    # mapping and conversion expressions, use these and aliasField above to calculate metrics value
    # (可选)指标映射转换计算表达式,与上面的别名一起作用,计算出最终需要的指标值
    # eg: cores=core1+core2, usage=usage, waitTime=allTime-runningTime
    calculates:
      - index=ifIndex
      - interface_name=ifDescr
      - mtu=ifMtu
      - speed=ifSpeed
      - in_octets=ifInOctets / 1024 / 1024
      - in_discards=ifInDiscards
      - in_errors=ifInErrors
      - out_octets=ifOutOctets / 1024 / 1024
      - out_discards=ifOutDiscards
      - out_errors=ifOutErrors
      - admin_status=ifAdminStatus
      - oper_status=ifOperStatus
      - alias=ifAlias
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      authPassphrase: ^_^authPassphrase^_^
      privPassphrase: ^_^privPassphrase^_^
      privPasswordEncryption: ^_^privPasswordEncryption^_^
      authPasswordEncryption: ^_^authPasswordEncryption^_^
      contextName: ^_^contextName^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        ifIndex: *******.*******.1.1
        ifDescr: *******.*******.1.2
        ifMtu: *******.*******.1.4
        ifSpeed: *******.********.1.1.15
        ifInOctets: *******.*******.1.10
        ifInDiscards: *******.*******.1.13
        ifInErrors: *******.*******.1.14
        ifOutOctets: *******.*******.1.16
        ifOutDiscards: *******.*******.1.19
        ifOutErrors: *******.*******.1.20
        ifAdminStatus: *******.*******.1.7
        ifOperStatus: *******.*******.1.8
        ifAlias: *******.********.1.1.18

  # AP接入点信息
  - name: ap
    i18n:
      zh-CN: AP接入点
      en-US: Access Points
    priority: 1
    fields:
      - field: ap_name
        type: 1
        label: true
        i18n:
          zh-CN: AP名称
          en-US: AP Name
      - field: ap_ip
        type: 1
        i18n:
          zh-CN: AP IP地址
          en-US: AP IP Address
      - field: ap_mac
        type: 1
        i18n:
          zh-CN: AP MAC地址
          en-US: AP MAC Address
      - field: ap_user
        type: 0
        i18n:
          zh-CN: 用户数量
          en-US: User Count
      - field: ap_status
        type: 0
        i18n:
          zh-CN: AP状态
          en-US: AP Status
      - field: ap_cpu
        type: 0
        unit: '%'
        i18n:
          zh-CN: AP CPU使用率
          en-US: AP CPU Usage
      - field: ap_mem
        type: 0
        unit: '%'
        i18n:
          zh-CN: AP内存使用率
          en-US: AP Memory Usage
    # 添加别名字段用于数据映射
    aliasFields:
      - apName
      - apIp
      - apMac
      - apUser
      - apStatus
      - apCpu
      - apMem
    calculates:
      - ap_name=apName
      - ap_ip=apIp
      - ap_mac=apMac
      - ap_user=apUser
      - ap_status=apStatus
      - ap_cpu=apCpu
      - ap_mem=apMem
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      authPassphrase: ^_^authPassphrase^_^
      privPassphrase: ^_^privPassphrase^_^
      privPasswordEncryption: ^_^privPasswordEncryption^_^
      authPasswordEncryption: ^_^authPasswordEncryption^_^
      contextName: ^_^contextName^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        apName: *******.4.1.25506.********.2.1.8
        apIp: *******.4.1.25506.********.2.1.2
        apMac: *******.4.1.25506.********.2.1.3
        apUser: *******.4.1.25506.********.2.1.7
        apStatus: *******.4.1.25506.********.10.1.7
        apCpu: *******.4.1.25506.********.10.1.2
        apMem: *******.4.1.25506.********.10.1.4

  # SSID无线服务
  - name: ssid
    i18n:
      zh-CN: 无线服务
      en-US: Wireless Services
    priority: 2
    fields:
      - field: ssid_name
        type: 1
        label: true
        i18n:
          zh-CN: SSID名称
          en-US: SSID Name
      - field: ssid_id
        type: 1
        i18n:
          zh-CN: SSID ID
          en-US: SSID ID
      - field: ssid_user
        type: 0
        i18n:
          zh-CN: 用户数量
          en-US: User Count
      - field: ssid_vlan
        type: 0
        i18n:
          zh-CN: VLAN ID
          en-US: VLAN ID
      - field: ssid_status
        type: 0
        i18n:
          zh-CN: SSID状态
          en-US: SSID Status
    # 添加别名字段用于数据映射
    aliasFields:
      - ssidName
      - ssidId
      - ssidUserCount
      - ssidVlan
      - ssidStatus
    calculates:
      - ssid_name=ssidName
      - ssid_id=ssidId
      - ssid_user=ssidUserCount
      - ssid_vlan=ssidVlan
      - ssid_status=ssidStatus
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      authPassphrase: ^_^authPassphrase^_^
      privPassphrase: ^_^privPassphrase^_^
      privPasswordEncryption: ^_^privPasswordEncryption^_^
      authPasswordEncryption: ^_^authPasswordEncryption^_^
      contextName: ^_^contextName^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        ssidName: *******.4.1.25506.********.2.1.30
        ssidId: *******.4.1.25506.********.2.1.2
        ssidUserCount: *******.4.1.25506.********.7.1.4
        ssidVlan: *******.4.1.25506.********.2.1.14
        ssidStatus: *******.4.1.25506.********.2.1.25

  # AC控制器状态（版本1 - 标准OID）
  - name: ac_status_v1
    i18n:
      zh-CN: 控制器状态V1
      en-US: Controller Status V1
    priority: 4
    fields:
      - field: ac_ap_count
        type: 0
        i18n:
          zh-CN: AP总数
          en-US: Total APs
      - field: ac_user_num
        type: 0
        i18n:
          zh-CN: 用户总数
          en-US: Total Users
      - field: responseTime
        type: 0
        unit: ms
        i18n:
          zh-CN: 响应时间
          en-US: Response Time
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      authPassphrase: ^_^authPassphrase^_^
      privPassphrase: ^_^privPassphrase^_^
      privPasswordEncryption: ^_^privPasswordEncryption^_^
      authPasswordEncryption: ^_^authPasswordEncryption^_^
      contextName: ^_^contextName^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: get
      oids:
        ac_ap_count: *******.4.1.25506.********.2.1.0
        ac_user_num: *******.4.1.25506.********.2.2.0
  # 射频统计信息（方法6 - 射频表）
  - name: radio_stats
    i18n:
      zh-CN: 射频统计信息
      en-US: Radio Statistics
    priority: 12
    fields:
      - field: radio_index
        type: 1
        label: true
        i18n:
          zh-CN: 射频索引
          en-US: Radio Index
      - field: radio_band
        type: 0
        i18n:
          zh-CN: 工作频段
          en-US: Operating Band
      - field: radio_users
        type: 0
        i18n:
          zh-CN: 关联用户数
          en-US: Associated Users
      - field: radio_channel
        type: 0
        i18n:
          zh-CN: 工作信道
          en-US: Operating Channel
      - field: radio_band_type
        type: 0
        i18n:
          zh-CN: 频段类型
          en-US: Band Type
    # 使用别名字段处理射频表数据
    aliasFields:
      - radioIndex
      - radioBand
      - radioUsers
      - radioChannel
      - radioBandType
    calculates:
      - radio_index=radioIndex
      - radio_band=radioBand
      - radio_users=radioUsers
      - radio_channel=radioChannel
      - radio_band_type=radioBandType
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      authPassphrase: ^_^authPassphrase^_^
      privPassphrase: ^_^privPassphrase^_^
      privPasswordEncryption: ^_^privPasswordEncryption^_^
      authPasswordEncryption: ^_^authPasswordEncryption^_^
      contextName: ^_^contextName^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        radioIndex: *******.4.1.25506.********.1.1.1     # 射频索引
        radioBand: *******.4.1.25506.********.1.1.7      # 工作频段
        radioUsers: *******.4.1.25506.********.1.1.11    # 关联用户数
        radioChannel: *******.4.1.25506.********.1.1.26  # 工作信道
        radioBandType: *******.4.1.25506.********.1.1.29 # 频段类型 (16=2.4G)

  # AC控制器系统资源监控
  - name: ac_system_resources
    i18n:
      zh-CN: AC系统资源
      en-US: AC System Resources
    priority: 3
    fields:
      - field: ac_cpu_usage
        type: 0
        unit: '%'
        i18n:
          zh-CN: AC CPU使用率
          en-US: AC CPU Usage
      - field: ac_memory_usage
        type: 0
        unit: '%'
        i18n:
          zh-CN: AC内存使用率
          en-US: AC Memory Usage
    # 添加别名字段用于数据映射和计算
    aliasFields:
      - cpuValues
      - memValues
    # 计算表达式：过滤非零值并计算平均值
    calculates:
      - ac_cpu_usage=cpuValues
      - ac_memory_usage=memValues
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      authPassphrase: ^_^authPassphrase^_^
      privPassphrase: ^_^privPassphrase^_^
      privPasswordEncryption: ^_^privPasswordEncryption^_^
      authPasswordEncryption: ^_^authPasswordEncryption^_^
      contextName: ^_^contextName^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        cpuValues: *******.4.1.25506.*******.1.1.6   # AC CPU使用率
        memValues: *******.4.1.25506.*******.1.1.8   # AC内存使用率

