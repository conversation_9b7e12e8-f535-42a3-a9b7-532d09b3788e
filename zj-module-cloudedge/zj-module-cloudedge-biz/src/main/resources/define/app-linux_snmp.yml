# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring mid-middleware custom-custom monitoring os-operating system monitoring
# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: os
# The monitoring type eg: linux windows tomcat mysql aws...
# 监控类型 eg: linux windows tomcat mysql aws...
app: linux_snmp
# The monitoring i18n name
# 监控类型国际化名称
name:
  zh-CN: Linux-SNMP
  en-US: OS Linux
# 监控所需输入参数定义(根据定义渲染页面UI)
# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  # field-变量字段标识符
  - field: host
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: host
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: true
  # field-param field key
  # field-变量字段标识符
  - field: ipmi
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: IPMI地址
      en-US: IPMI Ip
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: text
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: false
  - field: port
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 端口
      en-US: Port
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: number
    # when type is number, range is required
    # 当type为number时,用range表示范围
    range: '[0,65535]'
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: true
    # default value
    # 默认值
    defaultValue: 161
  # field-param field key
  # field-变量字段标识符
  - field: version
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: SNMP 版本
      en-US: SNMP Version
    # type-param field type(radio mapping the html radio tag)
    # type-当type为radio时,前端用radio展示开关
    type: radio
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: true
    # when type is radio checkbox, use option to show optional values {name1:value1,name2:value2}
    # 当type为radio单选框, checkbox复选框时, option表示可选项值列表 {name1:value1,name2:value2}
    options:
      - label: SNMPv1
        value: 0
      - label: SNMPv2c
        value: 1
  # field-param field key
  # field-变量字段标识符
  - field: community
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: SNMP 团体字
      en-US: SNMP Community
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: text
    # when type is text, use limit to limit string length
    # 当type为text时,用limit表示字符串限制大小
    limit: 100
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: true
    # 参数输入框提示信息
    # param field input placeholder
    placeholder: 'Snmp community for v1 v2c'
  # field-param field key
  # field-变量字段标识符
  - field: timeout
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 超时时间(ms)
      en-US: Timeout(ms)
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: number
    # when type is number, range is required
    # 当type为number时,用range表示范围
    range: '[0,100000]'
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: false
    # hide-is hide this field and put it in advanced layout
    # hide-是否隐藏此参数将其放入高级设置中
    hide: true
    # default value
    # 默认值
    defaultValue: 6000
# collect metrics config list
# 采集指标配置列表
metrics:
  # metrics - system
  # 监控指标 - system
  - name: system
    i18n:
      zh-CN: 系统
      en-US: System
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    # 指标采集调度优先级(0->127)->(优先级高->低) 优先级低的指标会等优先级高的指标采集完成后才会被调度, 相同优先级的指标会并行调度采集
    # 优先级为0的指标为可用性指标,即它会被首先调度,采集成功才会继续调度其它指标,采集失败则中断调度
    priority: 0
    fields:
      - field: name
        type: 1
        i18n:
          zh-CN: 名称
          en-US: Name
      - field: descr
        type: 1
        i18n:
          zh-CN: 描述
          en-US: Description
      - field: uptime
        type: 1
        i18n:
          zh-CN: 运行时间
          en-US: Uptime
      - field: numUsers
        type: 0
        i18n:
          zh-CN: 用户数
          en-US: Number of Users
      - field: services
        type: 0
        i18n:
          zh-CN: 服务数
          en-US: Number of Services
      - field: processes
        type: 0
        i18n:
          zh-CN: 进程数
          en-US: Number of Processes
      - field: responseTime
        type: 0
        i18n:
          zh-CN: 响应时间
          en-US: Response Time
        unit: ms
      - field: location
        type: 1
        i18n:
          zh-CN: 位置
          en-US: Location
      - field: memory
        type: 0
        i18n:
          zh-CN: 内存
          en-US: Memory
        unit: GB
      - field: memUsage
        type: 0
        i18n:
          zh-CN: 内存使用率
          en-US: Memory Usage
        unit: '%'
    units:
      - memory=KB->GB
    aliasFields:
      - memtotal
      - memfree
    calculates:
      - memUsage = (memtotal - memfree) / memtotal * 100
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: get
      oids:
        name: *******.*******.0
        descr: *******.*******.0
        uptime: *******.********.1.0
        numUsers: *******.********.5.0
        services: *******.*******.0
        processes: *******.********.6.0
        location: *******.*******.0
        memory: *******.********.2.0
        memtotal: *******.4.1.2021.4.5.0
        memfree: *******.4.1.2021.4.6.0

  - name: processes
    i18n:
      zh-CN: 进程
      en-US: Processes
    priority: 1
    fields:
      - field: hrSWRunIndex
        type: 0
        i18n:
          zh-CN: 进程 Index
          en-US: Process Index
      - field: hrSWRunName
        type: 1
        i18n:
          zh-CN: 进程名称
          en-US: Process Name
        label: true
      - field: hrSWRunID
        type: 1
        i18n:
          zh-CN: 进程 ID
          en-US: Process ID
      - field: hrSWRunPath
        type: 1
        i18n:
          zh-CN: 进程路径
          en-US: Process Path
      - field: hrSWRunParameters
        type: 1
        i18n:
          zh-CN: 进程参数
          en-US: Process Parameters
      - field: hrSWRunType
        type: 0
        i18n:
          zh-CN: 进程类型
          en-US: Process Type
      - field: hrSWRunStatus
        type: 0
        i18n:
          zh-CN: 进程状态
          en-US: Process Status
      - field: hrSWRunPerfCPU
        type: 0
        i18n:
          zh-CN: 进程占用 CPU
          en-US: Process CPU
        unit: 's'
      - field: hrSWRunPerfMem
        type: 0
        i18n:
          zh-CN: 进程占用内存
          en-US: Process Memory
        unit: 'KB'
    calculates:
      - hrSWRunPerfCPU = hrSWRunPerfCPU / 100
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^snmpVersion^_^
      operation: walk
      oids:
        hrSWRunIndex: *******.********.2.1.1
        hrSWRunName: *******.********.2.1.2
        hrSWRunID: *******.********.2.1.3
        hrSWRunPath: *******.********.2.1.4
        hrSWRunParameters: *******.********.2.1.5
        hrSWRunType: *******.********.2.1.6
        hrSWRunStatus: *******.********.2.1.7
        hrSWRunPerfCPU: *******.********.1.1.1
        hrSWRunPerfMem: *******.********.1.1.2

  - name: installed
    i18n:
      zh-CN: 安装
      en-US: Installed
    priority: 1
    fields:
      - field: hrSWInstalledIndex
        type: 0
        i18n:
          zh-CN: 安装 Index
          en-US: Installed Index
      - field: hrSWInstalledName
        type: 1
        i18n:
          zh-CN: 安装名称
          en-US: Installed Name
        label: true
      - field: hrSWInstalledID
        type: 1
        i18n:
          zh-CN: 安装 ID
          en-US: Installed ID
      - field: hrSWInstalledType
        type: 0
        i18n:
          zh-CN: 安装类型
          en-US: Installed Type
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hrSWInstalledIndex: *******.********.3.1.1
        hrSWInstalledName: *******.********.3.1.2
        hrSWInstalledID: *******.********.3.1.3
        hrSWInstalledType: *******.********.3.1.4

  - name: cpu
    i18n:
      zh-CN: CPU
      en-US: CPU
    priority: 1
    fields:
      - field: hrProcessorFrwID
        type: 1
        i18n:
          zh-CN: 处理器 ID
          en-US: Processor ID
      - field: hrProcessorLoad
        type: 0
        i18n:
          zh-CN: 处理器负载
          en-US: Processor Load
        unit: '%'
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hrProcessorFrwID: *******.********.3.1.1
        hrProcessorLoad: *******.********.3.1.2

  - name: storages
    i18n:
      zh-CN: 存储
      en-US: Storages
    priority: 1
    fields:
      - field: index
        type: 0
        i18n:
          zh-CN: 存储 Index
          en-US: Storage Index
      - field: descr
        type: 1
        i18n:
          zh-CN: 存储描述
          en-US: Storage Description
        label: true
      - field: size
        i18n:
          zh-CN: 存储大小
          en-US: Storage Size
        type: 0
        unit: GB
      - field: free
        type: 0
        i18n:
          zh-CN: 存储空闲
          en-US: Storage Free
        unit: GB
      - field: used
        type: 0
        i18n:
          zh-CN: 存储占用
          en-US: Storage Used
        unit: GB
      - field: usage
        type: 0
        i18n:
          zh-CN: 存储使用率
          en-US: Storage Usage
        unit: '%'
    # (非必须)监控指标别名，与上面的指标名映射。用于采集接口数据字段不直接是最终指标名称,需要此别名做映射转换
    aliasFields:
      - hrStorageIndex
      - hrStorageDescr
      - hrStorageSize
      - hrStorageUsed
      - hrStorageAllocationUnits
    # (非必须)指标计算表达式,与上面的别名一起作用,计算出最终需要的指标值
    # eg: cores=core1+core2, usage=usage, waitTime=allTime-runningTime
    calculates:
      - index=hrStorageIndex
      - descr=hrStorageDescr
      - size=hrStorageSize * hrStorageAllocationUnits
      - free=(hrStorageSize - hrStorageUsed) * hrStorageAllocationUnits
      - used=hrStorageUsed * hrStorageAllocationUnits
      - usage= hrStorageUsed / hrStorageSize * 100
    units:
      - free=B->GB
      - size=B->GB
      - used=B->GB
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hrStorageDescr: *******.********.3.1.3
        hrStorageIndex: *******.********.3.1.1
        hrStorageSize: *******.********.3.1.5
        hrStorageUsed: *******.********.3.1.6
        hrStorageAllocationUnits: *******.********.3.1.4

  - name: disk
    i18n:
      zh-CN: 磁盘
      en-US: Disk
    priority: 2
    fields:
      - field: hrDiskStorageAccess
        type: 0
        i18n:
          zh-CN: 磁盘存储访问
          en-US: Disk Storage Access
      - field: hrDiskStorageMedia
        type: 0
        i18n:
          zh-CN: 磁盘存储介质
          en-US: Disk Storage Media
      - field: hrDiskStorageRemoveble
        type: 0
        i18n:
          zh-CN: 磁盘存储是否可移动
          en-US: Disk Storage Removeble
      - field: hrDiskStorageCapacity
        type: 0
        i18n:
          zh-CN: 磁盘存储容量
          en-US: Disk Storage Capacity
        unit: MB
      # 新增磁盘读写速率指标
      - field: diskReadRate
        type: 0
        i18n:
          zh-CN: 磁盘读取速率
          en-US: Disk Read Rate
        unit: iops
      - field: diskWriteRate
        type: 0
        i18n:
          zh-CN: 磁盘写入速率
          en-US: Disk Write Rate
        unit: iops
    units:
      - hrDiskStorageCapacity=KB->MB
    aliasFields:
      - hrDiskStorageAccess
      - hrDiskStorageMedia
      - hrDiskStorageRemoveble
      - hrDiskStorageCapacity
      - diskReadBytes
      - diskWriteBytes
      - diskReadBytesOld
      - diskWriteBytesOld
    calculates:
      - diskReadRate = (diskReadBytes - diskReadBytesOld) / 5  # 假设采集间隔为5秒
      - diskWriteRate = (diskWriteBytes - diskWriteBytesOld) / 5
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hrDiskStorageAccess: *******.********.6.1.1
        hrDiskStorageMedia: *******.********.6.1.2
        hrDiskStorageRemoveble: *******.********.6.1.3
        hrDiskStorageCapacity: *******.********.6.1.4
        diskReadBytes: *******.4.1.2021.*********.5  # 磁盘读取字节数
        diskWriteBytes: *******.4.1.2021.*********.6  # 磁盘写入字节数

  - name: network
    i18n:
      zh-CN: 网络
      en-US: Network
    priority: 3
    fields:
      - field: number
        type: 1
        i18n:
          zh-CN: 编号
          en-US: Number
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      oids:
        number: *******.*******.0

  - name: interface
    i18n:
      zh-CN: 接口详情
      en-US: Interfaces Detail
    priority: 4
    fields:
      - field: index
        type: 0
        i18n:
          zh-CN: 编号
          en-US: Index
      - field: interface_name
        type: 1
        label: true
        i18n:
          zh-CN: 接口名称
          en-US: Interface Name
      - field: admin_status
        type: 1
        i18n:
          zh-CN: 管理状态
          en-US: Admin Status
      - field: oper_status
        type: 1
        i18n:
          zh-CN: 运行状态
          en-US: Oper Status
      - field: mtu
        type: 0
        i18n:
          zh-CN: 最大传输单元
          en-US: MTU
        unit: 'byte'
      - field: speed
        type: 0
        i18n:
          zh-CN: 速度
          en-US: Speed
        unit: 'KB/s'
      - field: in_octets
        type: 0
        i18n:
          zh-CN: 入流量
          en-US: In Octets
        unit: 'Mbps'
      - field: in_discards
        type: 0
        i18n:
          zh-CN: 输入丢弃数
          en-US: In Discards
        unit: 'package'
      - field: in_errors
        type: 0
        i18n:
          zh-CN: 输入错误数
          en-US: In Errors
        unit: 'package'
      - field: out_octets
        type: 0
        i18n:
          zh-CN: 出流量
          en-US: Out Octets
        unit: 'Mbps'
      - field: out_discards
        type: 0
        i18n:
          zh-CN: 输出丢弃数
          en-US: Out Discards
        unit: 'package'
      - field: out_errors
        type: 0
        i18n:
          zh-CN: 输出错误数
          en-US: Out Errors
        unit: 'package'

    aliasFields:
      - ifIndex
      - ifDescr
      - ifMtu
      - ifSpeed
      - ifInOctets
      - ifInDiscards
      - ifInErrors
      - ifOutOctets
      - ifOutDiscards
      - ifOutErrors
      - ifAdminStatus
      - ifOperStatus
    calculates:
      - index=ifIndex
      - interface_name=ifDescr
      - mtu=ifMtu
      - speed=ifSpeed
      - in_octets=ifInOctets / 1024 / 1024
      - in_discards=ifInDiscards
      - in_errors=ifInErrors
      - out_octets=ifOutOctets / 1024 / 1024
      - out_discards=ifOutDiscards
      - out_errors=ifOutErrors
      - admin_status=ifAdminStatus
      - oper_status=ifOperStatus
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        ifIndex: *******.*******.1.1
        ifDescr: *******.*******.1.2
        ifMtu: *******.*******.1.4
        ifSpeed: *******.********.1.1.15
        ifInOctets: *******.*******.1.10
        ifInDiscards: *******.*******.1.13
        ifInErrors: *******.*******.1.14
        ifOutOctets: *******.*******.1.16
        ifOutDiscards: *******.*******.1.19
        ifOutErrors: *******.*******.1.20
        ifAdminStatus: *******.*******.1.7
        ifOperStatus: *******.*******.1.8

  - name: devices
    i18n:
      zh-CN: 设备
      en-US: Devices
    priority: 4
    fields:
      - field: index
        type: 1
        i18n:
          zh-CN: 序号
          en-US: Index
        label: true
      - field: descr
        type: 1
        i18n:
          zh-CN: 描述
          en-US: Description
      - field: status
        type: 1
        i18n:
          zh-CN: 状态
          en-US: Status
    aliasFields:
      - hrDeviceIndex
      - hrDeviceDescr
      - hrDeviceStatus
    calculates:
      - index=hrDeviceIndex
      - descr=hrDeviceDescr
      - status=hrDeviceStatus
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hrDeviceIndex: *******.********.2.1.1
        hrDeviceDescr: *******.********.2.1.3
        hrDeviceStatus: *******.********.2.1.5

  # 硬件监控 - 温度传感器
  # Hardware Monitoring - Temperature Sensors
  - name: temperature
    i18n:
      zh-CN: 温度传感器
      en-US: Temperature Sensors
    priority: 5
    fields:
      - field: sensorIndex
        type: 0
        i18n:
          zh-CN: 传感器索引
          en-US: Sensor Index
      - field: sensorDescr
        type: 1
        i18n:
          zh-CN: 传感器描述
          en-US: Sensor Description
        label: true
      - field: sensorValue
        type: 0
        i18n:
          zh-CN: 温度值
          en-US: Temperature Value
        unit: '°C'
      - field: sensorStatus
        type: 1
        i18n:
          zh-CN: 传感器状态
          en-US: Sensor Status
    aliasFields:
      - lmTempSensorsIndex
      - lmTempSensorsDevice
      - lmTempSensorsValue
      - lmTempSensorsStatus
    calculates:
      - sensorIndex=lmTempSensorsIndex
      - sensorDescr=lmTempSensorsDevice
      - sensorValue=lmTempSensorsValue / 1000  # 转换为摄氏度
      - sensorStatus=lmTempSensorsStatus
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        # NET-SNMP LM-SENSORS MIB - 温度传感器
        lmTempSensorsIndex: *******.4.1.2021.*********.1    # 温度传感器索引
        lmTempSensorsDevice: *******.4.1.2021.*********.2   # 温度传感器设备名
        lmTempSensorsValue: *******.4.1.2021.*********.3    # 温度值(毫摄氏度)
        lmTempSensorsStatus: *******.4.1.2021.*********.4   # 传感器状态

  # 硬件监控 - 风扇传感器
  # Hardware Monitoring - Fan Sensors
  - name: fans
    i18n:
      zh-CN: 风扇传感器
      en-US: Fan Sensors
    priority: 5
    fields:
      - field: fanIndex
        type: 0
        i18n:
          zh-CN: 风扇索引
          en-US: Fan Index
      - field: fanDescr
        type: 1
        i18n:
          zh-CN: 风扇描述
          en-US: Fan Description
        label: true
      - field: fanSpeed
        type: 0
        i18n:
          zh-CN: 风扇转速
          en-US: Fan Speed
        unit: 'RPM'
      - field: fanStatus
        type: 1
        i18n:
          zh-CN: 风扇状态
          en-US: Fan Status
    aliasFields:
      - lmFanSensorsIndex
      - lmFanSensorsDevice
      - lmFanSensorsValue
      - lmFanSensorsStatus
    calculates:
      - fanIndex=lmFanSensorsIndex
      - fanDescr=lmFanSensorsDevice
      - fanSpeed=lmFanSensorsValue
      - fanStatus=lmFanSensorsStatus
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        # NET-SNMP LM-SENSORS MIB - 风扇传感器
        lmFanSensorsIndex: *******.4.1.2021.*********.1     # 风扇传感器索引
        lmFanSensorsDevice: *******.4.1.2021.*********.2    # 风扇传感器设备名
        lmFanSensorsValue: *******.4.1.2021.*********.3     # 风扇转速(RPM)
        lmFanSensorsStatus: *******.4.1.2021.*********.4    # 风扇状态

  # 硬件监控 - 电压传感器
  # Hardware Monitoring - Voltage Sensors
  - name: voltage
    i18n:
      zh-CN: 电压传感器
      en-US: Voltage Sensors
    priority: 5
    fields:
      - field: voltageIndex
        type: 0
        i18n:
          zh-CN: 电压索引
          en-US: Voltage Index
      - field: voltageDescr
        type: 1
        i18n:
          zh-CN: 电压描述
          en-US: Voltage Description
        label: true
      - field: voltageValue
        type: 0
        i18n:
          zh-CN: 电压值
          en-US: Voltage Value
        unit: 'V'
      - field: voltageStatus
        type: 1
        i18n:
          zh-CN: 电压状态
          en-US: Voltage Status
    aliasFields:
      - lmVoltSensorsIndex
      - lmVoltSensorsDevice
      - lmVoltSensorsValue
      - lmVoltSensorsStatus
    calculates:
      - voltageIndex=lmVoltSensorsIndex
      - voltageDescr=lmVoltSensorsDevice
      - voltageValue=lmVoltSensorsValue / 1000  # 转换为伏特
      - voltageStatus=lmVoltSensorsStatus
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        # NET-SNMP LM-SENSORS MIB - 电压传感器
        lmVoltSensorsIndex: *******.4.1.2021.*********.1    # 电压传感器索引
        lmVoltSensorsDevice: *******.4.1.2021.*********.2   # 电压传感器设备名
        lmVoltSensorsValue: *******.4.1.2021.*********.3    # 电压值(毫伏)
        lmVoltSensorsStatus: *******.4.1.2021.*********.4   # 电压状态

  # 硬件监控 - 磁盘健康状态
  # Hardware Monitoring - Disk Health Status
  - name: disk_health
    i18n:
      zh-CN: 磁盘健康状态
      en-US: Disk Health Status
    priority: 6
    fields:
      - field: diskIndex
        type: 0
        i18n:
          zh-CN: 磁盘索引
          en-US: Disk Index
      - field: diskPath
        type: 1
        i18n:
          zh-CN: 磁盘路径
          en-US: Disk Path
        label: true
      - field: diskDevice
        type: 1
        i18n:
          zh-CN: 磁盘设备
          en-US: Disk Device
      - field: diskErrorFlag
        type: 0
        i18n:
          zh-CN: 磁盘错误标志
          en-US: Disk Error Flag
      - field: diskErrorMsg
        type: 1
        i18n:
          zh-CN: 磁盘错误信息
          en-US: Disk Error Message
      - field: diskHealthPercent
        type: 0
        i18n:
          zh-CN: 磁盘健康百分比
          en-US: Disk Health Percentage
        unit: '%'
    aliasFields:
      - diskIOIndex
      - diskIOPath
      - diskIODevice
      - diskIOErrorFlag
      - diskIOErrorMsg
      - diskIOHealthPercent
    calculates:
      - diskIndex=diskIOIndex
      - diskPath=diskIOPath
      - diskDevice=diskIODevice
      - diskErrorFlag=diskIOErrorFlag
      - diskErrorMsg=diskIOErrorMsg
      - diskHealthPercent=diskIOHealthPercent
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        # NET-SNMP UCD-DISKIO-MIB - 磁盘I/O和健康状态
        diskIOIndex: *******.4.1.2021.*********.1          # 磁盘索引
        diskIOPath: *******.4.1.2021.*********.2           # 磁盘路径
        diskIODevice: *******.4.1.2021.*********.3         # 磁盘设备名
        diskIOErrorFlag: *******.4.1.2021.*********.100    # 磁盘错误标志
        diskIOErrorMsg: *******.4.1.2021.*********.101     # 磁盘错误信息
        diskIOHealthPercent: *******.4.1.2021.*********.102 # 磁盘健康百分比(自定义扩展)

  # 硬件监控 - 系统硬件序列号
  # Hardware Monitoring - System Hardware Serial Numbers
  - name: hardware_serial
    i18n:
      zh-CN: 硬件序列号
      en-US: Hardware Serial Numbers
    priority: 6
    fields:
      - field: systemSerial
        type: 1
        i18n:
          zh-CN: 系统序列号
          en-US: System Serial Number
        label: true
      - field: motherboardSerial
        type: 1
        i18n:
          zh-CN: 主板序列号
          en-US: Motherboard Serial Number
      - field: cpuSerial
        type: 1
        i18n:
          zh-CN: CPU序列号
          en-US: CPU Serial Number
      - field: biosVersion
        type: 1
        i18n:
          zh-CN: BIOS版本
          en-US: BIOS Version
      - field: systemManufacturer
        type: 1
        i18n:
          zh-CN: 系统制造商
          en-US: System Manufacturer
      - field: systemModel
        type: 1
        i18n:
          zh-CN: 系统型号
          en-US: System Model
    aliasFields:
      - hrSystemSerial
      - hrSystemMfgr
      - hrSystemModel
      - hrSystemBios
      - hrSystemCpuSerial
      - hrSystemMbSerial
    calculates:
      - systemSerial=hrSystemSerial
      - motherboardSerial=hrSystemMbSerial
      - cpuSerial=hrSystemCpuSerial
      - biosVersion=hrSystemBios
      - systemManufacturer=hrSystemMfgr
      - systemModel=hrSystemModel
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: get
      oids:
        # HOST-RESOURCES-MIB 和 NET-SNMP 扩展 - 硬件序列号信息
        hrSystemSerial: *******.4.1.2021.100.4.0           # 系统序列号(NET-SNMP扩展)
        hrSystemMfgr: *******.4.1.2021.100.1.0             # 系统制造商
        hrSystemModel: *******.4.1.2021.100.2.0            # 系统型号
        hrSystemBios: *******.4.1.2021.100.3.0             # BIOS版本
        hrSystemCpuSerial: *******.4.1.2021.100.5.0        # CPU序列号(扩展)
        hrSystemMbSerial: *******.4.1.2021.100.6.0         # 主板序列号(扩展)

  # 硬件监控 - 电源状态
  # Hardware Monitoring - Power Supply Status
  - name: power_supply
    i18n:
      zh-CN: 电源状态
      en-US: Power Supply Status
    priority: 6
    fields:
      - field: powerIndex
        type: 0
        i18n:
          zh-CN: 电源索引
          en-US: Power Index
      - field: powerDescr
        type: 1
        i18n:
          zh-CN: 电源描述
          en-US: Power Description
        label: true
      - field: powerStatus
        type: 1
        i18n:
          zh-CN: 电源状态
          en-US: Power Status
      - field: powerConsumption
        type: 0
        i18n:
          zh-CN: 功耗
          en-US: Power Consumption
        unit: 'W'
      - field: powerHealth
        type: 1
        i18n:
          zh-CN: 电源健康状态
          en-US: Power Health Status
    aliasFields:
      - lmPowerIndex
      - lmPowerDescr
      - lmPowerStatus
      - lmPowerConsumption
      - lmPowerHealth
    calculates:
      - powerIndex=lmPowerIndex
      - powerDescr=lmPowerDescr
      - powerStatus=lmPowerStatus
      - powerConsumption=lmPowerConsumption
      - powerHealth=lmPowerHealth
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        # NET-SNMP 扩展 - 电源监控(需要自定义扩展或第三方MIB)
        lmPowerIndex: *******.4.1.2021.*********.1         # 电源索引
        lmPowerDescr: *******.4.1.2021.*********.2         # 电源描述
        lmPowerStatus: *******.4.1.2021.*********.3        # 电源状态
        lmPowerConsumption: *******.4.1.2021.*********.4   # 功耗(瓦特)
        lmPowerHealth: *******.4.1.2021.*********.5        # 电源健康状态

  # 硬件监控 - 磁盘SMART信息
  # Hardware Monitoring - Disk SMART Information
  - name: disk_smart
    i18n:
      zh-CN: 磁盘SMART信息
      en-US: Disk SMART Information
    priority: 7
    fields:
      - field: smartIndex
        type: 0
        i18n:
          zh-CN: SMART索引
          en-US: SMART Index
      - field: smartDevice
        type: 1
        i18n:
          zh-CN: SMART设备
          en-US: SMART Device
        label: true
      - field: smartSerial
        type: 1
        i18n:
          zh-CN: 磁盘序列号
          en-US: Disk Serial Number
      - field: smartModel
        type: 1
        i18n:
          zh-CN: 磁盘型号
          en-US: Disk Model
      - field: smartTemperature
        type: 0
        i18n:
          zh-CN: 磁盘温度
          en-US: Disk Temperature
        unit: '°C'
      - field: smartHealthStatus
        type: 1
        i18n:
          zh-CN: 健康状态
          en-US: Health Status
      - field: smartLifespan
        type: 0
        i18n:
          zh-CN: 剩余寿命
          en-US: Remaining Lifespan
        unit: '%'
    aliasFields:
      - smartDiskIndex
      - smartDiskDevice
      - smartDiskSerial
      - smartDiskModel
      - smartDiskTemp
      - smartDiskHealth
      - smartDiskLifespan
    calculates:
      - smartIndex=smartDiskIndex
      - smartDevice=smartDiskDevice
      - smartSerial=smartDiskSerial
      - smartModel=smartDiskModel
      - smartTemperature=smartDiskTemp
      - smartHealthStatus=smartDiskHealth
      - smartLifespan=smartDiskLifespan
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        # NET-SNMP 扩展 - SMART信息(需要自定义脚本扩展)
        smartDiskIndex: *******.4.1.2021.*********.1       # SMART磁盘索引
        smartDiskDevice: *******.4.1.2021.*********.2      # SMART磁盘设备名
        smartDiskSerial: *******.4.1.2021.*********.3      # 磁盘序列号
        smartDiskModel: *******.4.1.2021.*********.4       # 磁盘型号
        smartDiskTemp: *******.4.1.2021.*********.5        # 磁盘温度
        smartDiskHealth: *******.4.1.2021.*********.6      # 健康状态
        smartDiskLifespan: *******.4.1.2021.*********.7    # 剩余寿命百分比
