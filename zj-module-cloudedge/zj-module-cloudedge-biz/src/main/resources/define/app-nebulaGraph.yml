# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: db
# 监控应用类型名称(与文件名保持一致) eg: linux windows tomcat mysql aws...
app: nebulaGraph
# The app api i18n name
# app api国际化名称
name:
  zh-CN: NebulaGraph
  en-US: NebulaGraph
# 监控类型的帮助描述信息

# Input params define for app api(render web ui by the definition)
params:
  # field-param field key
  # field-字段名称标识符
  - field: host
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: host
    # required-true or false
    # 是否是必输项 true-必填 false-可选
    required: true
  # field-param field key
  # field-变量字段标识符
  - field: graphPort
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: graph端口
      en-US: graphPort
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: number
    # when type is number, range is required
    # 当type为number时,用range表示范围
    range: '[0,65535]'
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: true
    # default value
    # 默认值
    defaultValue: 19669
  - field: timePeriod
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: graph指标统计的时间范围(s)
      en-US: graph indicator statistics time range (s)
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: number
    # when type is number, range is required
    # 当type为number时,用range表示范围
    range: '[0,3600]'
    # required-true or false
    # 是否是必输项 true-必填 false-可选
    required: true
    # default value 6000
    # 默认值 6000
    defaultValue: 60
  - field: storagePort
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: storage端口
      en-US: storagePort
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: number
    # when type is number, range is required
    # 当type为number时,用range表示范围
    range: '[0,65535]'
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: true
    # default value
    # 默认值
    defaultValue: 19779
  # field-param field key
  # field-变量字段标识符
  - field: timeout
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 连接超时时间(ms)
      en-US: Connect Timeout(ms)
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: number
    # when type is number, range is required
    # 当type为number时,用range表示范围
    range: '[0,100000]'
    # required-true or false
    # 是否是必输项 true-必填 false-可选
    required: true
    # default value 6000
    # 默认值 6000
    defaultValue: 6000


# collect metrics config list
# 采集指标配置列表
metrics:
  # metrics - available
  # 监控指标 - available
  - name: nebulaGraph_stats
    i18n:
      zh-CN: nebulaGraph 统计信息
      en-US: nebulaGraph stats
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    # 指标采集调度优先级(0->127)->(优先级高->低) 优先级低的指标会等优先级高的指标采集完成后才会被调度, 相同优先级的指标会并行调度采集
    # 优先级为0的指标为可用性指标,即它会被首先调度,采集成功才会继续调度其它指标,采集失败则中断调度
    priority: 0
    fields:
      - field: responseTime
        type: 0
        unit: ms
        i18n:
          zh-CN: 响应时间
          en-US: Response Time
      - field: num_queries_hit_memory_watermark_rate
        type: 0
        i18n:
          zh-CN: 达到内存水位线的语句的数量(rate)
          en-US: num_queries_hit_memory_watermark.rate
      - field: num_queries_hit_memory_watermark_sum
        type: 0
        i18n:
          zh-CN: 达到内存水位线的语句的数量(sum)
          en-US: num_queries_hit_memory_watermark.sum
      - field: num_reclaimed_expired_sessions_rate
        type: 0
        i18n:
          zh-CN: 服务端主动回收的过期的会话数量(rate)
          en-US: num_reclaimed_expired_sessions.rate
      - field: num_reclaimed_expired_sessions_sum
        type: 0
        i18n:
          zh-CN: 服务端主动回收的过期的会话数量(sum)
          en-US: num_reclaimed_expired_sessions.sum
      - field: slow_query_latency_us_avg
        type: 0
        i18n:
          zh-CN: 慢查询延迟时间(avg)
          en-US: slow_query_latency_us.avg
      - field: slow_query_latency_us_p75
        type: 0
        i18n:
          zh-CN: 慢查询延迟时间(p75)
          en-US: slow_query_latency_us.p75
      - field: slow_query_latency_us_p95
        type: 0
        i18n:
          zh-CN: 慢查询延迟时间(p95)
          en-US: slow_query_latency_us.p95
      - field: slow_query_latency_us_p99
        type: 0
        i18n:
          zh-CN: 慢查询延迟时间(p99)
          en-US: slow_query_latency_us.p99
      - field: slow_query_latency_us_p999
        type: 0
        i18n:
          zh-CN: 慢查询延迟时间(p999)
          en-US: slow_query_latency_us.p999
      - field: query_latency_us_avg
        type: 0
        i18n:
          zh-CN: 查询延迟时间(avg)
          en-US: query_latency_us.avg
      - field: query_latency_us_p75
        type: 0
        i18n:
          zh-CN: 查询延迟时间(p75)
          en-US: query_latency_us.p75
      - field: query_latency_us_p95
        type: 0
        i18n:
          zh-CN: 查询延迟时间(p95)
          en-US: query_latency_us.p95
      - field: query_latency_us_p99
        type: 0
        i18n:
          zh-CN: 查询延迟时间(p99)
          en-US: query_latency_us.p99
      - field: query_latency_us_p999
        type: 0
        i18n:
          zh-CN: 查询延迟时间(p999)
          en-US: query_latency_us_p999
      - field: num_auth_failed_sessions_bad_username_password_rate
        type: 0
        i18n:
          zh-CN: 因用户名密码错误导验证失败的会话数量(rate)
          en-US: num_auth_failed_sessions_bad_username_password.rate
      - field: num_auth_failed_sessions_bad_username_password_sum
        type: 0
        i18n:
          zh-CN: 因用户名密码错误导验证失败的会话数量(sum)
          en-US: num_auth_failed_sessions_bad_username_password.sum
      - field: num_queries_rate
        type: 0
        i18n:
          zh-CN: 查询次数(rate)
          en-US: num_queries.rate
      - field: num_queries_sum
        type: 0
        i18n:
          zh-CN: 查询次数(sum)
          en-US: num_queries.sum
      - field: num_sort_executors_rate
        type: 0
        i18n:
          zh-CN: 排序（Sort）算子执行时间(rate)
          en-US: num_sort_executors.rate
      - field: num_sort_executors_sum
        type: 0
        i18n:
          zh-CN: 排序（Sort）算子执行时间(sum)
          en-US: num_sort_executors.sum
      - field: num_rpc_sent_to_storaged_failed_rate
        type: 0
        i18n:
          zh-CN: Graphd 服务发给 Storaged 服务的 RPC 请求失败的数量(rate)
          en-US: num_rpc_sent_to_storaged_failed.rate
      - field: num_rpc_sent_to_storaged_failed_sum
        type: 0
        i18n:
          zh-CN: Graphd 服务发给 Storaged 服务的 RPC 请求失败的数量(sum)
          en-US: num_rpc_sent_to_storaged_failed.sum
      - field: num_auth_failed_sessions_rate
        type: 0
        i18n:
          zh-CN: 登录验证失败的会话数量(rate)
          en-US: num_auth_failed_sessions.rate
      - field: num_auth_failed_sessions_sum
        type: 0
        i18n:
          zh-CN: 登录验证失败的会话数量(sum)
          en-US: num_auth_failed_sessions.sum
      - field: num_query_errors_rate
        type: 0
        i18n:
          zh-CN: 查询报错语句数量(rate)
          en-US: num_query_errors_rate
      - field: num_query_errors_sum
        type: 0
        i18n:
          zh-CN: 查询报错语句数量(sum)
          en-US: num_query_errors.sum
      - field: num_killed_queries_rate
        type: 0
        i18n:
          zh-CN: 被终止的查询数量(rate)
          en-US: num_killed_queries.rate
      - field: num_killed_queries_sum
        type: 0
        i18n:
          zh-CN: 被终止的查询数量(sum)
          en-US: num_killed_queries.sum
      - field: num_query_errors_leader_changes_rate
        type: 0
        i18n:
          zh-CN: 因查询错误而导致的 Leader 变更的次数(rate)
          en-US: num_query_errors_leader_changes.rate
      - field: num_query_errors_leader_changes_sum
        type: 0
        i18n:
          zh-CN: 因查询错误而导致的 Leader 变更的次数(sum)
          en-US: num_query_errors_leader_changes.sum
      - field: num_rpc_sent_to_metad_rate
        type: 0
        i18n:
          zh-CN: Graphd 服务发给 Metad 服务的 RPC 请求数量(rate)
          en-US: num_rpc_sent_to_metad.rate
      - field: num_rpc_sent_to_metad_sum
        type: 0
        i18n:
          zh-CN: Graphd 服务发给 Metad 服务的 RPC 请求数量(sum)
          en-US: num_rpc_sent_to_metad.sum
      - field: num_slow_queries_rate
        type: 0
        i18n:
          zh-CN: 慢查询次数(rate)
          en-US: num_slow_queries.rate
      - field: num_slow_queries_sum
        type: 0
        i18n:
          zh-CN: 慢查询次数(sum)
          en-US: num_slow_queries.sum
      - field: num_active_sessions_sum
        type: 0
        i18n:
          zh-CN: 活跃的会话数的变化数(sum)
          en-US: num_active_sessions.sum
      - field: num_active_queries_sum
        type: 0
        i18n:
          zh-CN: 活跃的查询语句数的变化数(sum)
          en-US: num_active_queries.sum
      - field: num_sentences_rate
        type: 0
        i18n:
          zh-CN: Graphd 服务接收的语句数(rate)
          en-US: num_sentences.rate
      - field: num_sentences_sum
        type: 0
        i18n:
          zh-CN: Graphd 服务接收的语句数(sum)
          en-US: num_sentences.sum
      - field: num_aggregate_executors_rate
        type: 0
        i18n:
          zh-CN: 聚合（Aggregate）算子执行时间(rate)
          en-US: num_aggregate_executors.rate
      - field: num_aggregate_executors_sum
        type: 0
        i18n:
          zh-CN: 聚合（Aggregate）算子执行时间(sum)
          en-US: num_aggregate_executors.sum
      - field: optimizer_latency_us_avg
        type: 0
        i18n:
          zh-CN: 优化器阶段延迟时间(avg)
          en-US: optimizer_latency_us.avg
      - field: optimizer_latency_us_p75
        type: 0
        i18n:
          zh-CN: 优化器阶段延迟时间(p75)
          en-US: optimizer_latency_us.p75
      - field: optimizer_latency_us_p95
        type: 0
        i18n:
          zh-CN: 优化器阶段延迟时间(p95)
          en-US: optimizer_latency_us.p95
      - field: optimizer_latency_us_p99
        type: 0
        i18n:
          zh-CN: 优化器阶段延迟时间(p99)
          en-US: optimizer_latency_us.p99
      - field: optimizer_latency_us_p999
        type: 0
        i18n:
          zh-CN: 优化器阶段延迟时间(p999)
          en-US: optimizer_latency_us.p999
      - field: num_rpc_sent_to_metad_failed_rate
        type: 0
        i18n:
          zh-CN: Graphd 服务发给 Metad 的 RPC 请求失败的数量(rate)
          en-US: num_rpc_sent_to_metad_failed.rate
      - field: num_rpc_sent_to_metad_failed_sum
        type: 0
        i18n:
          zh-CN: Graphd 服务发给 Metad 的 RPC 请求失败的数量(sum)
          en-US: num_rpc_sent_to_metad_failed.sum
      - field: num_indexscan_executors_rate
        type: 0
        i18n:
          zh-CN: 索引扫描（IndexScan）算子执行时间(rate)
          en-US: num_indexscan_executors.rate
      - field: num_indexscan_executors_sum
        type: 0
        i18n:
          zh-CN: 索引扫描（IndexScan）算子执行时间(sum)
          en-US: num_indexscan_executors.sum
      - field: num_opened_sessions_rate
        type: 0
        i18n:
          zh-CN: 服务端建立过的会话数量(rate)
          en-US: num_opened_sessions.rate
      - field: num_opened_sessions_sum
        type: 0
        i18n:
          zh-CN: 服务端建立过的会话数量(sum)
          en-US: num_opened_sessions.sum
      - field: num_auth_failed_sessions_out_of_max_allowed_rate
        type: 0
        i18n:
          zh-CN: 因为超过FLAG_OUT_OF_MAX_ALLOWED_CONNECTIONS参数导致的验证登录的失败的会话数量(rate)
          en-US: num_auth_failed_sessions_out_of_max_allowed.rate
      - field: num_auth_failed_sessions_out_of_max_allowed_sum
        type: 0
        i18n:
          zh-CN: 因为超过FLAG_OUT_OF_MAX_ALLOWED_CONNECTIONS参数导致的验证登录的失败的会话数量(sum)
          en-US: num_auth_failed_sessions_out_of_max_allowed.sum
      - field: num_rpc_sent_to_storaged_rate
        type: 0
        i18n:
          zh-CN: Graphd 服务发给 Storaged 服务的 RPC 请求数量(rate)
          en-US: num_rpc_sent_to_storaged.rate
      - field: num_rpc_sent_to_storaged_sum
        type: 0
        i18n:
          zh-CN: Graphd 服务发给 Storaged 服务的 RPC 请求数量(sum)
          en-US: num_rpc_sent_to_storaged.sum
    # 指标别名列表，用于在查询结果中识别指标
    aliasFields:
      - responseTime
      - num_queries_hit_memory_watermark.rate
      - num_queries_hit_memory_watermark.sum
      - num_reclaimed_expired_sessions.rate
      - num_reclaimed_expired_sessions.sum
      - slow_query_latency_us.avg
      - slow_query_latency_us.p75
      - slow_query_latency_us.p95
      - slow_query_latency_us.p99
      - slow_query_latency_us.p999
      - query_latency_us.avg
      - query_latency_us.p75
      - query_latency_us.p95
      - query_latency_us.p99
      - query_latency_us.p999
      - num_auth_failed_sessions_bad_username_password.rate
      - num_auth_failed_sessions_bad_username_password.sum
      - num_queries.rate
      - num_queries.sum
      - num_sort_executors.rate
      - num_sort_executors.sum
      - num_rpc_sent_to_storaged_failed.rate
      - num_rpc_sent_to_storaged_failed.sum
      - num_auth_failed_sessions.rate
      - num_auth_failed_sessions.sum
      - num_query_errors.rate
      - num_query_errors.sum
      - num_killed_queries.rate
      - num_killed_queries.sum
      - num_query_errors_leader_changes.rate
      - num_query_errors_leader_changes.sum
      - num_rpc_sent_to_metad.rate
      - num_rpc_sent_to_metad.sum
      - num_slow_queries.rate
      - num_slow_queries.sum
      - num_active_sessions.sum
      - num_active_queries.sum
      - num_sentences.rate
      - num_sentences.sum
      - num_aggregate_executors.rate
      - num_aggregate_executors.sum
      - optimizer_latency_us.avg
      - optimizer_latency_us.p75
      - optimizer_latency_us.p95
      - optimizer_latency_us.p99
      - optimizer_latency_us.p999
      - num_rpc_sent_to_metad_failed.rate
      - num_rpc_sent_to_metad_failed.sum
      - num_indexscan_executors.rate
      - num_indexscan_executors.sum
      - num_opened_sessions.rate
      - num_opened_sessions.sum
      - num_auth_failed_sessions_out_of_max_allowed.rate
      - num_auth_failed_sessions_out_of_max_allowed.sum
      - num_rpc_sent_to_storaged.rate
      - num_rpc_sent_to_storaged.sum
      # mapping and conversion expressions, use thesand aliasField above to calculate metrics value# (可选)指标映射转换计算表达式，与上面的别名一起作用，计算出最终需要的指标值# eg: cores=core1+core2, usage=usage, waitTimeallTime-runningTime
    calculates:
      - responseTime=responseTime
      - num_queries_hit_memory_watermark_rate=num_queries_hit_memory_watermark.rate
      - num_queries_hit_memory_watermark_sum=num_queries_hit_memory_watermark.sum
      - num_reclaimed_expired_sessions_rate=num_reclaimed_expired_sessions.rate
      - num_reclaimed_expired_sessions_sum=num_reclaimed_expired_sessions.sum
      - slow_query_latency_us_avg=slow_query_latency_us.avg
      - slow_query_latency_us_p75=slow_query_latency_us.p75
      - slow_query_latency_us_p95=slow_query_latency_us.p95
      - slow_query_latency_us_p99=slow_query_latency_us.p99
      - slow_query_latency_us_p999=slow_query_latency_us.p999
      - query_latency_us_avg=query_latency_us.avg
      - query_latency_us_p75=query_latency_us.p75
      - query_latency_us_p95=query_latency_us.p95
      - query_latency_us_p99=query_latency_us.p99
      - query_latency_us_p999=query_latency_us.p999
      - num_auth_failed_sessions_bad_username_password_rate=num_auth_failed_sessions_bad_username_password.rate
      - num_auth_failed_sessions_bad_username_password_sum=num_auth_failed_sessions_bad_username_password.sum
      - num_queries_rate=num_queries.rate
      - num_queries_sum=num_queries.sum
      - num_sort_executors_rate=num_sort_executors.rate
      - num_sort_executors_sum=num_sort_executors.sum
      - num_rpc_sent_to_storaged_failed_rate=num_rpc_sent_to_storaged_failed.rate
      - num_rpc_sent_to_storaged_failed_sum=num_rpc_sent_to_storaged_failed.sum
      - num_auth_failed_sessions_rate=num_auth_failed_sessions.rate
      - num_auth_failed_sessions_sum=num_auth_failed_sessions.sum
      - num_query_errors_rate=num_query_errors.rate
      - num_query_errors_sum=num_query_errors.sum
      - num_killed_queries_rate=num_killed_queries.rate
      - num_killed_queries_sum=num_killed_queries.sum
      - num_query_errors_leader_changes_rate=num_query_errors_leader_changes.rate
      - num_query_errors_leader_changes_sum=num_query_errors_leader_changes.sum
      - num_rpc_sent_to_metad_rate=num_rpc_sent_to_metad.rate
      - num_rpc_sent_to_metad_sum=num_rpc_sent_to_metad.sum
      - num_slow_queries_rate=num_slow_queries.rate
      - num_slow_queries_sum=num_slow_queries.sum
      - num_active_sessions_sum=num_active_sessions.sum
      - num_active_queries_sum=num_active_queries.sum
      - num_sentences_rate=num_sentences.rate
      - num_sentences_sum=num_sentences.sum
      - num_aggregate_executors_rate=num_aggregate_executors.rate
      - num_aggregate_executors_sum=num_aggregate_executors.sum
      - optimizer_latency_us_avg=optimizer_latency_us.avg
      - optimizer_latency_us_p75=optimizer_latency_us.p75
      - optimizer_latency_us_p95=optimizer_latency_us.p95
      - optimizer_latency_us_p99=optimizer_latency_us.p99
      - optimizer_latency_us_p999=optimizer_latency_us.p999
      - num_rpc_sent_to_metad_failed_rate=num_rpc_sent_to_metad_failed.rate
      - num_rpc_sent_to_metad_failed_sum=num_rpc_sent_to_metad_failed.sum
      - num_indexscan_executors_rate=num_indexscan_executors.rate
      - num_indexscan_executors_sum=num_indexscan_executors.sum
      - num_opened_sessions_rate=num_opened_sessions.rate
      - num_opened_sessions_sum=num_opened_sessions.sum
      - num_auth_failed_sessions_out_of_max_allowed_rate=num_auth_failed_sessions_out_of_max_allowed.rate
      - num_auth_failed_sessions_out_of_max_allowed_sum=num_auth_failed_sessions_out_of_max_allowed.sum
      - num_rpc_sent_to_storaged_rate=num_rpc_sent_to_storaged.rate
      - num_rpc_sent_to_storaged_sum=num_rpc_sent_to_storaged.sum
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk, nginx
    protocol: nebulaGraph
    nebulaGraph:
      # http host: ipv4 ipv6 domain
      host: ^_^host^_^
      # http port
      port: ^_^graphPort^_^
      # 时间间隔
      timePeriod: ^_^timePeriod^_^
      # http url
      # url请求接口路径
      url: /stats
      # timeout
      # 超时时间
      timeout: ^_^timeout^_^
  - name: rocksdb_stats
    i18n:
      zh-CN: rocksdb 统计数据
      en-US: rocksdb stats
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    # 指标采集调度优先级(0->127)->(优先级高->低) 优先级低的指标会等优先级高的指标采集完成后才会被调度, 相同优先级的指标会并行调度采集
    # 优先级为0的指标为可用性指标,即它会被首先调度,采集成功才会继续调度其它指标,采集失败则中断调度
    priority: 1
    fields:
      - field: responseTime
        type: 0
        unit: ms
        i18n:
          zh-CN: 响应时间
          en-US: Response Time
      - field: rocksdb.backup.read.bytes
      - field: rocksdb.backup.write.bytes
      - field: rocksdb.blobdb.blob.file.bytes.read
      - field: rocksdb.blobdb.blob.file.bytes.written
      - field: rocksdb.blobdb.blob.file.synced
      - field: rocksdb.blobdb.blob.index.evicted.count
      - field: rocksdb.blobdb.blob.index.evicted.size
      - field: rocksdb.blobdb.blob.index.expired.count
      - field: rocksdb.blobdb.blob.index.expired.size
      - field: rocksdb.blobdb.bytes.read
      - field: rocksdb.blobdb.bytes.written
      - field: rocksdb.blobdb.cache.add
      - field: rocksdb.blobdb.cache.add.failures
      - field: rocksdb.blobdb.cache.bytes.read
      - field: rocksdb.blobdb.cache.bytes.write
      - field: rocksdb.blobdb.cache.hit
      - field: rocksdb.blobdb.cache.miss
      - field: rocksdb.blobdb.fifo.bytes.evicted
      - field: rocksdb.blobdb.fifo.num.files.evicted
      - field: rocksdb.blobdb.fifo.num.keys.evicted
      - field: rocksdb.blobdb.gc.bytes.expired
      - field: rocksdb.blobdb.gc.bytes.overwritten
      - field: rocksdb.blobdb.gc.bytes.relocated
      - field: rocksdb.blobdb.gc.failures
      - field: rocksdb.blobdb.gc.num.files
      - field: rocksdb.blobdb.gc.num.keys.expired
      - field: rocksdb.blobdb.gc.num.keys.overwritten
      - field: rocksdb.blobdb.gc.num.keys.relocated
      - field: rocksdb.blobdb.gc.num.new.files
      - field: rocksdb.blobdb.num.get
      - field: rocksdb.blobdb.num.keys.read
      - field: rocksdb.blobdb.num.keys.written
      - field: rocksdb.blobdb.num.multiget
      - field: rocksdb.blobdb.num.next
      - field: rocksdb.blobdb.num.prev
      - field: rocksdb.blobdb.num.put
      - field: rocksdb.blobdb.num.seek
      - field: rocksdb.blobdb.num.write
      - field: rocksdb.blobdb.write.blob
      - field: rocksdb.blobdb.write.blob.ttl
      - field: rocksdb.blobdb.write.inlined
      - field: rocksdb.blobdb.write.inlined.ttl
      - field: rocksdb.block.cache.add
      - field: rocksdb.block.cache.add.failures
      - field: rocksdb.block.cache.add.redundant
      - field: rocksdb.block.cache.bytes.read
      - field: rocksdb.block.cache.bytes.write
      - field: rocksdb.block.cache.compression.dict.add
      - field: rocksdb.block.cache.compression.dict.add.redundant
      - field: rocksdb.block.cache.compression.dict.bytes.evict
      - field: rocksdb.block.cache.compression.dict.bytes.insert
      - field: rocksdb.block.cache.compression.dict.hit
      - field: rocksdb.block.cache.compression.dict.miss
      - field: rocksdb.block.cache.data.add
      - field: rocksdb.block.cache.data.add.redundant
      - field: rocksdb.block.cache.data.bytes.insert
      - field: rocksdb.block.cache.data.hit
      - field: rocksdb.block.cache.data.miss
      - field: rocksdb.block.cache.filter.add
      - field: rocksdb.block.cache.filter.add.redundant
      - field: rocksdb.block.cache.filter.bytes.evict
      - field: rocksdb.block.cache.filter.bytes.insert
      - field: rocksdb.block.cache.filter.hit
      - field: rocksdb.block.cache.filter.miss
      - field: rocksdb.block.cache.hit
      - field: rocksdb.block.cache.index.add
      - field: rocksdb.block.cache.index.add.redundant
      - field: rocksdb.block.cache.index.bytes.evict
      - field: rocksdb.block.cache.index.bytes.insert
      - field: rocksdb.block.cache.index.hit
      - field: rocksdb.block.cache.index.miss
      - field: rocksdb.block.cache.miss
      - field: rocksdb.block.cachecompressed.add
      - field: rocksdb.block.cachecompressed.add.failures
      - field: rocksdb.block.cachecompressed.hit
      - field: rocksdb.block.cachecompressed.miss
      - field: rocksdb.block.checksum.compute.count
      - field: rocksdb.bloom.filter.full.positive
      - field: rocksdb.bloom.filter.full.true.positive
      - field: rocksdb.bloom.filter.micros
      - field: rocksdb.bloom.filter.prefix.checked
      - field: rocksdb.bloom.filter.prefix.useful
      - field: rocksdb.bloom.filter.useful
      - field: rocksdb.bytes.read
      - field: rocksdb.bytes.written
      - field: rocksdb.cold.file.read.bytes
      - field: rocksdb.cold.file.read.count
      - field: rocksdb.compact.read.bytes
      - field: rocksdb.compact.read.marked.bytes
      - field: rocksdb.compact.read.periodic.bytes
      - field: rocksdb.compact.read.ttl.bytes
      - field: rocksdb.compact.write.bytes
      - field: rocksdb.compact.write.marked.bytes
      - field: rocksdb.compact.write.periodic.bytes
      - field: rocksdb.compact.write.ttl.bytes
      - field: rocksdb.compaction.cancelled
      - field: rocksdb.compaction.key.drop.new
      - field: rocksdb.compaction.key.drop.obsolete
      - field: rocksdb.compaction.key.drop.range_del
      - field: rocksdb.compaction.key.drop.user
      - field: rocksdb.compaction.optimized.del.drop.obsolete
      - field: rocksdb.compaction.range_del.drop.obsolete
      - field: rocksdb.db.iter.bytes.read
      - field: rocksdb.db.mutex.wait.micros
      - field: rocksdb.error.handler.autoresume.count
      - field: rocksdb.error.handler.autoresume.retry.total.count
      - field: rocksdb.error.handler.autoresume.success.count
      - field: rocksdb.error.handler.bg.errro.count
      - field: rocksdb.error.handler.bg.io.errro.count
      - field: rocksdb.error.handler.bg.retryable.io.errro.count
      - field: rocksdb.files.deleted.immediately
      - field: rocksdb.files.marked.trash
      - field: rocksdb.filter.operation.time.nanos
      - field: rocksdb.flush.write.bytes
      - field: rocksdb.getupdatessince.calls
      - field: rocksdb.hot.file.read.bytes
      - field: rocksdb.hot.file.read.count
      - field: rocksdb.l0.hit
      - field: rocksdb.l0.num.files.stall.micros
      - field: rocksdb.l0.slowdown.micros
      - field: rocksdb.l1.hit
      - field: rocksdb.l2andup.hit
      - field: rocksdb.last.level.read.bytes
      - field: rocksdb.last.level.read.count
      - field: rocksdb.memtable.compaction.micros
      - field: rocksdb.memtable.garbage.bytes.at.flush
      - field: rocksdb.memtable.hit
      - field: rocksdb.memtable.miss
      - field: rocksdb.memtable.payload.bytes.at.flush
      - field: rocksdb.merge.operation.time.nanos
      - field: rocksdb.multiget.coroutine.count
      - field: rocksdb.no.file.closes
      - field: rocksdb.no.file.errors
      - field: rocksdb.no.file.opens
      - field: rocksdb.non.last.level.read.bytes
      - field: rocksdb.non.last.level.read.count
      - field: rocksdb.num.iterator.created
      - field: rocksdb.num.iterator.deleted
      - field: rocksdb.num.iterators
      - field: rocksdb.number.block.compressed
      - field: rocksdb.number.block.decompressed
      - field: rocksdb.number.block.not_compressed
      - field: rocksdb.number.db.next
      - field: rocksdb.number.db.next.found
      - field: rocksdb.number.db.prev
      - field: rocksdb.number.db.prev.found
      - field: rocksdb.number.db.seek
      - field: rocksdb.number.db.seek.found
      - field: rocksdb.number.deletes.filtered
      - field: rocksdb.number.direct.load.table.properties
      - field: rocksdb.number.iter.skip
      - field: rocksdb.number.keys.read
      - field: rocksdb.number.keys.updated
      - field: rocksdb.number.keys.written
      - field: rocksdb.number.merge.failures
      - field: rocksdb.number.multiget.bytes.read
      - field: rocksdb.number.multiget.get
      - field: rocksdb.number.multiget.keys.found
      - field: rocksdb.number.multiget.keys.read
      - field: rocksdb.number.rate_limiter.drains
      - field: rocksdb.number.reseeks.iteration
      - field: rocksdb.number.superversion_acquires
      - field: rocksdb.number.superversion_cleanups
      - field: rocksdb.number.superversion_releases
      - field: rocksdb.persistent.cache.hit
      - field: rocksdb.persistent.cache.miss
      - field: rocksdb.rate.limit.delay.millis
      - field: rocksdb.read.amp.estimate.useful.bytes
      - field: rocksdb.read.amp.total.read.bytes
      - field: rocksdb.remote.compact.read.bytes
      - field: rocksdb.remote.compact.write.bytes
      - field: rocksdb.row.cache.hit
      - field: rocksdb.row.cache.miss
      - field: rocksdb.secondary.cache.hits
      - field: rocksdb.sim.block.cache.hit
      - field: rocksdb.sim.block.cache.miss
      - field: rocksdb.stall.micros
      - field: rocksdb.txn.get.tryagain
      - field: rocksdb.txn.overhead.duplicate.key
      - field: rocksdb.txn.overhead.mutex.old.commit.map
      - field: rocksdb.txn.overhead.mutex.prepare
      - field: rocksdb.txn.overhead.mutex.snapshot
      - field: rocksdb.verify_checksum.read.bytes
      - field: rocksdb.wal.bytes
      - field: rocksdb.wal.synced
      - field: rocksdb.warm.file.read.bytes
      - field: rocksdb.warm.file.read.count
      - field: rocksdb.write.other
      - field: rocksdb.write.self
      - field: rocksdb.write.timeout
      - field: rocksdb.write.wal
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk, nginx
    protocol: nebulaGraph
    nebulaGraph:
      # http host: ipv4 ipv6 domain
      host: ^_^host^_^
      # http port
      port: ^_^storagePort^_^
      # http url
      # url请求接口路径
      url: /rocksdb_stats
      # timeout
      # 超时时间
      timeout: ^_^timeout^_^