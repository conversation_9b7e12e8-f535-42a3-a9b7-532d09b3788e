# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring custom-custom monitoring os-operating system monitoring
category: mid
# The monitoring type eg: linux windows tomcat mysql aws...
app: elasticsearch
# The monitoring i18n name
name:
  zh-CN: ElasticSearch
  en-US: ElasticSearch
# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # field-param field key
  - field: port
    # name-param field display i18n name
    name:
      zh-CN: 端口
      en-US: Port
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    # required-true or false
    required: true
    # default value
    defaultValue: 9200
  # field-param field key
  - field: timeout
    # name-param field display i18n name
    name:
      zh-CN: 查询超时时间
      en-US: Query Timeout
    # type-param field type(most mapping the html input type)
    type: number
    # required-true or false
    required: false
    # hide param-true or false
    hide: true
    # default value
    defaultValue: 6000
  # field-param field key
  - field: ssl
    # name-param field display i18n name
    name:
      zh-CN: 启用HTTPS
      en-US: SSL
    # type-param field type(boolean mapping the html h tag)
    type: boolean
    # required-true or false
    required: false
  # field-param field key
  - field: authType
    # name-param field display i18n name
    name:
      zh-CN: 认证方式
      en-US: Auth Type
    # type-param field type(most mapping the html input tag)
    type: radio
    # required-true or false
    required: false
    # when type is radio checkbox, use option to show optional values {name1:value1,name2:value2}
    options:
      - label: Basic Auth
        value: Basic Auth
      - label: Digest Auth
        value: Digest Auth
  # field-param field key
  - field: username
    # name-param field display i18n name
    name:
      zh-CN: 用户名
      en-US: Username
    # type-param field type(most mapping the html input type)
    type: text
    # when type is text, use limit to limit string length
    limit: 50
    # required-true or false
    required: false
  # field-param field key
  - field: password
    # name-param field display i18n name
    name:
      zh-CN: 密码
      en-US: Password
    # type-param field type(most mapping the html input tag)
    type: password
    # required-true or false
    required: false
# collect metrics config list
metrics:
  # metrics - cpu
  - name: health
    i18n:
      zh-CN: 集群健康状态
      en-US: Cluster Health Status
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    # collect metrics content
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      - field: cluster_name
        type: 1
        label: true
        i18n:
          zh-CN: 集群名称
          en-US: Cluster Name
      - field: status
        type: 1
        i18n:
          zh-CN: 集群状态
          en-US: status
      - field: nodes
        type: 0
        i18n:
          zh-CN: 集群节点数
          en-US: nodes
      - field: data_nodes
        type: 0
        i18n:
          zh-CN: 数据节点数
          en-US: Data Nodes
      - field: active_primary_shards
        type: 0
        i18n:
          zh-CN: 主节点活跃分片数
          en-US: Active Primary Shards
      - field: active_shards
        type: 0
        i18n:
          zh-CN: 活跃分片数
          en-US: Active Shards
      - field: active_percentage
        type: 0
        unit: '%'
        i18n:
          zh-CN: 分片健康度（%）
          en-US: Active Percentage
      - field: initializing_shards
        type: 0
        i18n:
          zh-CN: 初始化分片数
          en-US: Initializing Shards
      - field: unassigned_shards
        type: 0
        i18n:
          zh-CN: 未分配分片数
          en-US: Unassigned Shards
    # (optional)metrics field alias name, it is used as an alias field to map and convert the collected data and metrics field
    # (可选)监控指标别名, 做为中间字段与采集数据字段和指标字段映射转换
    aliasFields:
      - cluster_name
      - status
      - number_of_nodes
      - number_of_data_nodes
      - active_primary_shards
      - active_shards
      - initializing_shards
      - unassigned_shards
      - active_shards_percent_as_number
    # mapping and conversion expressions, use these and aliasField above to calculate metrics value
    # eg: cores=core1+core2, usage=usage, waitTime=allTime-runningTime
    calculates:
      - cluster_name=cluster_name
      - status=status
      - nodes=number_of_nodes
      - data_nodes=number_of_data_nodes
      - active_primary_shards=active_primary_shards
      - active_shards=active_shards
      - active_percentage=active_shards_percent_as_number
      - initializing_shards=initializing_shards
      - unassigned_shards=unassigned_shards
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: http
    # the config content when protocol is http
    http:
      # http host: ipv4 ipv6 domain
      host: ^_^host^_^
      # http port
      port: ^_^port^_^
      # http url
      url: /_cluster/health?pretty
      # http method: GET POST PUT DELETE PATCH
      method: GET
      # if enabled https
      ssl: ^_^ssl^_^
      # http auth
      authorization:
        # http auth type: Basic Auth, Digest Auth, Bearer Token
        type: ^_^authType^_^
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
        digestAuthUsername: ^_^username^_^
        digestAuthPassword: ^_^password^_^
      # http response data parse type: default-hertzbeat rule, jsonpath-jsonpath script, website-for website monitoring, prometheus-prometheus exporter rule
      parseType: default

  - name: nodes
    i18n:
      zh-CN: 集群节点信息
      en-US: Cluster Nodes
    priority: 1
    fields:
      - field: total
        type: 0
        i18n:
          zh-CN: 节点数
          en-US: total
      - field: successful
        type: 0
        i18n:
          zh-CN: 在线节点数
          en-US: successful
      - field: failed
        type: 0
        i18n:
          zh-CN: 离线节点数
          en-US: failed
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: /_nodes/stats
      method: GET
      ssl: ^_^ssl^_^
      authorization:
        type: ^_^authType^_^
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
        digestAuthUsername: ^_^username^_^
        digestAuthPassword: ^_^password^_^
      # http response data parse type: default-hertzbeat rule, jsonpath-jsonpath script, website-for website monitoring, prometheus-prometheus exporter rule
      parseType: jsonPath
      # parse script when parse type is josnPath
      parseScript: '$._nodes'

  - name: nodes_detail
    i18n:
      zh-CN: 节点详细信息
      en-US: Node Detail
    priority: 2
    fields:
      - field: node_name
        type: 1
        label: true
        i18n:
          zh-CN: 节点名称
          en-US: Node Name
      - field: ip
        type: 1
        i18n:
          zh-CN: IP地址
          en-US: IP Address
      - field: cpu_load_average
        type: 0
        i18n:
          zh-CN: CPU平均负载
          en-US: Cpu Load Average
      - field: cpu_percent
        type: 0
        unit: '%'
        i18n:
          zh-CN: CPU占用率
          en-US: Cpu Percent
      - field: heap_used
        type: 0
        unit: MB
        i18n:
          zh-CN: 内存使用量（MB）
          en-US: Heap Used
      - field: heap_used_percent
        type: 0
        unit: '%'
        i18n:
          zh-CN: 内存使用率
          en-US: Heap Used Percent
      - field: heap_total
        type: 1
        unit: 'MB'
        i18n:
          zh-CN: 总内存（MB）
          en-US: Heap Total
      - field: disk_free
        type: 0
        unit: 'GB'
        i18n:
          zh-CN: 磁盘剩余容量（GB）
          en-US: Disk Free
      - field: disk_total
        type: 1
        unit: 'GB'
        i18n:
          zh-CN: 磁盘总容量
          en-US: Disk Total
      - field: disk_used_percent
        type: 0
        unit: '%'
        i18n:
          zh-CN: 磁盘使用率
          en-US: Disk Used Percent
    aliasFields:
      - $.name
      - $.ip
      # $.os.cpu.load_average.1m 支持 5.x 及以上版本；$.os.load_average 支持 2.x 及以上版本
      - $.os.cpu.load_average.1m
      - $.os.load_average
      # $.os.cpu.percent 支持 5.x 及以上版本；$.os.cpu_percent 支持 2.x 及以上版本
      - $.os.cpu_percent
      - $.os.cpu.percent
      - $.jvm.mem.heap_used_in_bytes
      - $.jvm.mem.heap_used_percent
      - $.jvm.mem.heap_max_in_bytes
      - $.fs.total.free_in_bytes
      - $.fs.total.total_in_bytes
    calculates:
      - node_name=$.name
      - ip=$.ip
      - cpu_load_average=$.os.cpu.load_average.1m!=null?$.os.cpu.load_average.1m:$.os.load_average
      - cpu_percent=$.os.cpu.percent!=null?$.os.cpu.percent:$.os.cpu_percent
      - heap_used=$.jvm.mem.heap_used_in_bytes
      - heap_used_percent=$.jvm.mem.heap_used_percent
      - heap_total=$.jvm.mem.heap_max_in_bytes
      - disk_free=$.fs.total.free_in_bytes
      - disk_total=$.fs.total.total_in_bytes
      - disk_used_percent=(1-$.fs.total.free_in_bytes/$.fs.total.total_in_bytes)*100
    units:
      - heap_used=B->MB
      - heap_total=B->MB
      - disk_free=B->GB
      - disk_total=B->GB
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: /_nodes/stats
      method: GET
      ssl: ^_^ssl^_^
      authorization:
        type: ^_^authType^_^
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
        digestAuthUsername: ^_^username^_^
        digestAuthPassword: ^_^password^_^
      parseType: jsonPath
      parseScript: '$.nodes.*'
