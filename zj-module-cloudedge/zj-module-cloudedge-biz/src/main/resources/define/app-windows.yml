# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring mid-middleware custom-custom monitoring os-operating system monitoring
# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: os
# The monitoring type eg: linux windows tomcat mysql aws...
# 监控类型 eg: linux windows tomcat mysql aws...
app: windows
# The monitoring i18n name
# 监控类型国际化名称
name:
  zh-CN: Windows操作系统
  en-US: OS Windows
# The description and help of this monitoring type
# 监控类型的帮助描述信息

# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # field-param field key
  - field: port
    # name-param field display i18n name
    name:
      zh-CN: 端口
      en-US: Port
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    # required-true or false
    required: true
    # default value
    defaultValue: 161
  # field-param field key
  - field: snmpVersion
    # name-param field display i18n name
    name:
      zh-CN: SNMP 版本
      en-US: SNMP Version
    # type-param field type(radio mapping the html radio tag)
    type: radio
    # required-true or false
    required: true
    # when type is radio checkbox, use option to show optional values {name1:value1,name2:value2}
    options:
      - label: SNMPv1
        value: 0
      - label: SNMPv2c
        value: 1
  # field-param field key
  - field: community
    # name-param field display i18n name
    name:
      zh-CN: SNMP 团体字
      en-US: SNMP Community
    # type-param field type(most mapping the html input type)
    type: text
    # when type is text, use limit to limit string length
    limit: 100
    # required-true or false
    required: true
    # param field input placeholder
    placeholder: 'Snmp community for v1 v2c'
    # dependent parameter values list
    depend:
      snmpVersion:
        - 0
        - 1
  # field-param field key
  - field: timeout
    # name-param field display i18n name
    name:
      zh-CN: 超时时间(ms)
      en-US: Timeout(ms)
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,100000]'
    # required-true or false
    required: false
    # hide-is hide this field and put it in advanced layout
    hide: true
    # default value
    defaultValue: 6000
# collect metrics config list
metrics:
  # metrics - system
  - name: system
    i18n:
      zh-CN: 系统
      en-US: System
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    fields:
      - field: name
        type: 1
        i18n:
          zh-CN: 名称
          en-US: Name
      - field: descr
        type: 1
        i18n:
          zh-CN: 描述
          en-US: Description
      - field: uptime
        type: 1
        i18n:
          zh-CN: 运行时间
          en-US: Uptime
      - field: numUsers
        type: 0
        i18n:
          zh-CN: 用户数
          en-US: Number of Users
      - field: services
        type: 0
        i18n:
          zh-CN: 服务数
          en-US: Number of Services
      - field: processes
        type: 0
        i18n:
          zh-CN: 进程数
          en-US: Number of Processes
      - field: responseTime
        type: 0
        i18n:
          zh-CN: 响应时间
          en-US: Response Time
        unit: ms
      - field: location
        type: 1
        i18n:
          zh-CN: 位置
          en-US: Location

      - field: memory
        type: 0
        i18n:
          zh-CN: 内存
          en-US: Memory
        unit: GB
    units:
      - memory=KB->GB
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: get
      oids:
        name: *******.*******.0
        descr: *******.*******.0
        uptime: *******.********.1.0
        numUsers: *******.********.5.0
        services: *******.*******.0
        processes: *******.********.6.0
        location: *******.*******.0
        memory: *******.********.2.0

  - name: processes
    i18n:
      zh-CN: 进程
      en-US: Processes
    priority: 1
    fields:
      - field: hrSWRunIndex
        type: 0
        i18n:
          zh-CN: 编号
          en-US: Index
      - field: hrSWRunName
        type: 1
        i18n:
          zh-CN: 进程名称
          en-US: Process Name
        label: true
      - field: hrSWRunID
        type: 1
        i18n:
          zh-CN: 进程 ID
          en-US: Process ID
      - field: hrSWRunPath
        type: 1
        i18n:
          zh-CN: 进程路径
          en-US: Process Path
      - field: hrSWRunParameters
        type: 1
        i18n:
          zh-CN: 进程参数
          en-US: Process Parameters
      - field: hrSWRunType
        type: 0
        i18n:
          zh-CN: 进程类型
          en-US: Process Type
      - field: hrSWRunStatus
        type: 0
        i18n:
          zh-CN: 进程状态
          en-US: Process Status
      - field: hrSWRunPerfCPU
        type: 0
        i18n:
          zh-CN: 进程占用 CPU 时间
          en-US: Process CPU
        unit: 's'
      - field: hrSWRunPerfMem
        type: 0
        i18n:
          zh-CN: 进程占用内存
          en-US: Process Memory
        unit: 'KB'
    calculates:
      - hrSWRunPerfCPU = hrSWRunPerfCPU / 100
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^snmpVersion^_^
      operation: walk
      oids:
        hrSWRunIndex: *******.********.2.1.1
        hrSWRunName: *******.********.2.1.2
        hrSWRunID: *******.********.2.1.3
        hrSWRunPath: *******.********.2.1.4
        hrSWRunParameters: *******.********.2.1.5
        hrSWRunType: *******.********.2.1.6
        hrSWRunStatus: *******.********.2.1.7
        hrSWRunPerfCPU: *******.********.1.1.1
        hrSWRunPerfMem: *******.********.1.1.2

  - name: services
    i18n:
      zh-CN: 服务
      en-US: Services
    priority: 2
    fields:
      - field: svSvcName
        type: 1
        i18n:
          zh-CN: 服务名称
          en-US: Service Name
        label: true
      - field: svSvcInstalledState
        type: 0
        i18n:
          zh-CN: 服务安装状态
          en-US: Service Installed State
      - field: svSvcOperatingState
        type: 0
        i18n:
          zh-CN: 服务运行状态
          en-US: Service Operating State
      - field: svSvcCanBeUninstalled
        type: 0
        i18n:
          zh-CN: 服务是否可以被卸载
          en-US: Service Can Be Uninstalled
      - field: svSvcCanBePaused
        type: 0
        i18n:
          zh-CN: 服务是否可以被暂停
          en-US: Service Can Be Paused
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        svSvcName: *******.********.*******
        svSvcInstalledState: *******.********.*******
        svSvcOperatingState: *******.********.*******
        svSvcCanBeUninstalled: *******.********.*******
        svSvcCanBePaused: *******.********.*******

  - name: installed
    i18n:
      zh-CN: 安装
      en-US: Installed
    priority: 3
    fields:
      - field: hrSWInstalledIndex
        type: 0
        i18n:
          zh-CN: 编号
          en-US: 编号
      - field: hrSWInstalledName
        type: 1
        i18n:
          zh-CN: 安装名称
          en-US: Installed Name
        label: true
      - field: hrSWInstalledID
        type: 1
        i18n:
          zh-CN: 安装 ID
          en-US: Installed ID
      - field: hrSWInstalledType
        type: 0
        i18n:
          zh-CN: 安装类型
          en-US: Installed Type
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hrSWInstalledIndex: *******.********.3.1.1
        hrSWInstalledName: *******.********.3.1.2
        hrSWInstalledID: *******.********.3.1.3
        hrSWInstalledType: *******.********.3.1.4

  - name: storages
    i18n:
      zh-CN: 存储
      en-US: Storages
    priority: 5
    fields:
      - field: index
        type: 0
        i18n:
          zh-CN: 编号
          en-US: 编号
      - field: descr
        type: 1
        i18n:
          zh-CN: 存储描述
          en-US: Storage Description
        label: true
      - field: size
        i18n:
          zh-CN: 存储大小
          en-US: Storage Size
        type: 0
        unit: Mb
      - field: free
        type: 0
        i18n:
          zh-CN: 存储空闲
          en-US: Storage Free
        unit: Mb
      - field: used
        type: 0
        i18n:
          zh-CN: 存储占用
          en-US: Storage Used
        unit: GB
        scale: 2
      - field: usage
        type: 0
        i18n:
          zh-CN: 存储使用率
          en-US: Storage Usage
        unit: '%'
    # (Not required) Monitor indicator alias, which maps to the indicator name above. The field used to collect interface data is not directly the final indicator name, and this alias is required for mapping translation
    aliasFields:
      - hrStorageIndex
      - hrStorageDescr
      - hrStorageSize
      - hrStorageUsed
      - hrStorageAllocationUnits
    # The (optional) metric evaluation expression, which works with the aliases above, calculates the final desired metric value
    # eg: cores=core1+core2, usage=usage, waitTime=allTime-runningTime
    calculates:
      - index=hrStorageIndex
      - descr=hrStorageDescr
      - size=hrStorageSize * hrStorageAllocationUnits
      - free=(hrStorageSize - hrStorageUsed) * hrStorageAllocationUnits
      - used=hrStorageUsed * hrStorageAllocationUnits
      - usage= hrStorageUsed / hrStorageSize * 100
    units:
      - size=B->Mb
      - used=B->GB
      - free=B->Mb
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hrStorageDescr: *******.********.3.1.3
        hrStorageIndex: *******.********.3.1.1
        hrStorageSize: *******.********.3.1.5
        hrStorageUsed: *******.********.3.1.6
        hrStorageAllocationUnits: *******.********.3.1.4


  - name: interfaces
    i18n:
      zh-CN: 接口详情
      en-US: Interfaces Detail
    priority: 8
    fields:
      - field: index
        type: 1
        i18n:
          zh-CN: 编号
          en-US: Index
      - field: descr
        type: 1
        label: true
        i18n:
          zh-CN: 接口名称
          en-US: Interface Name
      - field: speed
        type: 0
        unit: 'Mbps'
        i18n:
          zh-CN: 接口速率
          en-US: Interface Speed
      - field: in_octets
        type: 0
        unit: 'Kbps'
        i18n:
          zh-CN: 入流量
          en-US: In Octets
      - field: out_octets
        type: 0
        unit: 'Kbps'
        i18n:
          zh-CN: 出流量
          en-US: Out Octets
      - field: admin_status
        type: 1
        i18n:
          zh-CN: 配置状态
          en-US: Config Status
      - field: oper_status
        type: 1
        i18n:
          zh-CN: 当前状态
          en-US: Current Status
      - field: mtu
        type: 0
        i18n:
          zh-CN: MTU
          en-US: MTU
      - field: in_discards
        type: 0
        i18n:
          zh-CN: 入丢包数
          en-US: In Discards
      - field: in_errors
        type: 0
        i18n:
          zh-CN: 入错包数
          en-US: In Errors
      - field: out_discards
        type: 0
        i18n:
          zh-CN: 出丢包数
          en-US: Out Discards
      - field: out_errors
        type: 0
        i18n:
          zh-CN: 出错包数
          en-US: Out Errors
    # (optional)metrics field alias name, it is used as an alias field to map and convert the collected data and metrics field
    aliasFields:
      - ifIndex
      - ifDescr
      - ifSpeed
      - ifInOctets
      - ifOutOctets
      - ifAdminStatus
      - ifOperStatus
      - ifMtu
      - ifInDiscards
      - ifInErrors
      - ifOutDiscards
      - ifOutErrors
    # mapping and conversion expressions, use these and aliasField above to calculate metrics value
    # eg: cores=core1+core2, usage=usage, waitTime=allTime-runningTime
    calculates:
      - index=ifIndex
      - descr=ifDescr
      - speed=ifSpeed / 1000 / 1000
      - in_octets=ifInOctets / 1000
      - out_octets=ifOutOctets / 1000
      - admin_status=ifAdminStatus
      - oper_status=ifOperStatus
      - mtu=ifMtu
      - in_discards=ifInDiscards
      - in_errors=ifInErrors
      - out_discards=ifOutDiscards
      - out_errors=ifOutErrors
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      authPassphrase: ^_^authPassphrase^_^
      privPassphrase: ^_^privPassphrase^_^
      privPasswordEncryption: ^_^privPasswordEncryption^_^
      authPasswordEncryption: ^_^authPasswordEncryption^_^
      contextName: ^_^contextName^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^snmpVersion^_^
      operation: walk
      oids:
        ifIndex: *******.*******.1.1
        ifDescr: *******.*******.1.2
        ifSpeed: *******.*******.1.5
        ifInOctets: *******.*******.1.10
        ifOutOctets: *******.*******.1.16
        ifAdminStatus: *******.*******.1.7
        ifOperStatus: *******.*******.1.8
        ifMtu: *******.*******.1.4
        ifInDiscards: *******.*******.1.13
        ifInErrors: *******.*******.1.14
        ifOutDiscards: *******.*******.1.19
        ifOutErrors: *******.*******.1.20

  - name: devices
    i18n:
      zh-CN: 设备
      en-US: Devices
    priority: 9
    fields:
      - field: index
        type: 1
        i18n:
          zh-CN: 编号
          en-US: Index
        label: true
      - field: descr
        type: 1
        i18n:
          zh-CN: 描述
          en-US: Description
      - field: status
        type: 1
        i18n:
          zh-CN: 状态
          en-US: Status
    aliasFields:
      - hrDeviceIndex
      - hrDeviceDescr
      - hrDeviceStatus
    calculates:
      - index=hrDeviceIndex
      - descr=hrDeviceDescr
      - status=hrDeviceStatus
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hrDeviceIndex: *******.********.2.1.1
        hrDeviceDescr: *******.********.2.1.3
        hrDeviceStatus: *******.********.2.1.5

  - name: cpu
    i18n:
      zh-CN: CPU负载
      en-US: CPU Load
    priority: 10
    fields:
      - field: cpuIndex
        type: 1
        i18n:
          zh-CN: CPU 序号
          en-US: CPU Index
      - field: cpuUsage
        type: 0
        i18n:
          zh-CN: CPU使用率
          en-US: CPU Usage
        unit: '%'
    aliasFields:
      - hrProcessorLoad
    calculates:
      - cpuIndex=++1
      - cpuUsage=hrProcessorLoad
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hrProcessorLoad: *******.********.3.1.2

  # 硬件监控 - 磁盘健康状态
  # Hardware Monitoring - Disk Health Status
  - name: disk_health
    i18n:
      zh-CN: 磁盘健康状态
      en-US: Disk Health Status
    priority: 11
    fields:
      - field: disk_index
        type: 1
        i18n:
          zh-CN: 磁盘编号
          en-US: Disk Index
        label: true
      - field: disk_descr
        type: 1
        i18n:
          zh-CN: 磁盘描述
          en-US: Disk Description
      - field: disk_size
        type: 0
        i18n:
          zh-CN: 磁盘大小
          en-US: Disk Size
        unit: 'GB'
      - field: disk_status
        type: 1
        i18n:
          zh-CN: 磁盘状态
          en-US: Disk Status
      - field: disk_serial
        type: 1
        i18n:
          zh-CN: 磁盘序列号
          en-US: Disk Serial Number
    aliasFields:
      - hrStorageIndex
      - hrStorageDescr
      - hrStorageSize
      - hrStorageAllocationUnits
      - hrDeviceStatus
    calculates:
      - disk_index=hrStorageIndex
      - disk_descr=hrStorageDescr
      - disk_size=hrStorageSize * hrStorageAllocationUnits
      - disk_status=hrDeviceStatus
      - disk_serial=hrStorageDescr
    units:
      - disk_size=B->GB
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hrStorageIndex: *******.********.3.1.1
        hrStorageDescr: *******.********.3.1.3
        hrStorageSize: *******.********.3.1.5
        hrStorageAllocationUnits: *******.********.3.1.4
        hrDeviceStatus: *******.********.2.1.5