# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring custom-custom monitoring os-operating system monitoring mid-middleware
# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等 mid-中间件
category: mid
# The monitoring: nacos
# 监控类型 nacos
app: nacos
# The monitoring i18n name
# 监控类型国际化名称
name:
  zh-CN: Nacos分布式
  en-US: Nacos Server
# The description and help of this monitoring type
# 监控类型的帮助描述信息

# 监控所需输入参数定义(根据定义渲染页面UI)
# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: 服务器Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # required-true or false
  - field: port
    # name-param field display i18n name
    name:
      zh-CN: Nacos服务端口
      en-US: Nacos Port
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    # required-true or false
    required: true
    # required-true or false
    defaultValue: 8848

# collect metrics config list
metrics:
  - name: system_cpu_usage
    i18n:
      zh-CN: CPU 使用率
      en-US: CPU Usage
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    fields:
      - field: usage
        type: 0
        unit: '%'
        i18n:
          zh-CN: 使用率
          en-US: Usage
    aliasFields:
      - value
    calculates:
      - usage=value
    protocol: http
    # the config content when protocol is http
    http:
      # http host: ipv4 ipv6 domain
      host: ^_^host^_^
      # http port
      port: ^_^port^_^
      # http url
      url: /nacos/actuator/prometheus
      # http method: GET POST PUT DELETE PATCH
      method: GET
      # http response data parse type: default-hertzbeat rule, jsonpath-jsonpath script, website-for website monitoring, prometheus-prometheus exporter rule
      parseType: prometheus

  - name: jvm_memory_used_bytes
    i18n:
      zh-CN: JVM 内存使用
      en-US: JVM Memory Used
    priority: 1
    fields:
      - field: area
        type: 1
        i18n:
          zh-CN: 区域
          en-US: Area
      - field: id
        type: 1
        label: true
        i18n:
          zh-CN: ID
          en-US: ID
      - field: value
        type: 0
        unit: MB
        i18n:
          zh-CN: 大小
          en-US: Size
    units:
      - value=B->MB
    # The protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: http
    # The config content when protocol is http
    http:
      # http host: ipv4 ipv6 domain
      host: ^_^host^_^
      # port
      port: ^_^port^_^
      # url request interface path
      url: /nacos/actuator/prometheus
      timeout: ^_^timeout^_^
      method: GET
      parseType: prometheus

  - name: nacos_monitor
    i18n:
      zh-CN: Nacos 详情
      en-US: Nacos Monitor
    priority: 2
    fields:
      - field: module
        type: 1
        i18n:
          zh-CN: 模块
          en-US: Module
      - field: name
        type: 1
        i18n:
          zh-CN: 指标名称
          en-US: Metric Name
      - field: value
        type: 0
        i18n:
          zh-CN: 大小
          en-US: Value
    # The protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: http
    # The config content when protocol is http
    http:
      # Host: ipv4 ipv6 domain
      host: ^_^host^_^
      # port
      port: ^_^port^_^
      # url request interface path
      url: /nacos/actuator/prometheus
      timeout: ^_^timeout^_^
      method: GET
      parseType: prometheus
