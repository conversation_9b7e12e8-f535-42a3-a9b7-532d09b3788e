# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring mid-middleware custom-custom monitoring os-operating system monitoring
category: stored
# The monitoring type eg: linux windows tomcat mysql aws...
app: storage_synology
# The monitoring i18n name
name:
  zh-CN: 群晖存储
  en-US: Synology Storage
# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # field-param field key
  - field: port
    # name-param field display i18n name
    name:
      zh-CN: 端口
      en-US: Port
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    # required-true or false
    required: true
    # default value
    defaultValue: 161
  # field-param field key
  - field: version
    # name-param field display i18n name
    name:
      zh-CN: SNMP 版本
      en-US: SNMP Version
    # type-param field type(radio mapping the html radio tag)
    type: radio
    # required-true or false
    required: true
    # when type is radio checkbox, use option to show optional values {name1:value1,name2:value2}
    options:
      - label: SNMPv1
        value: 0
      - label: SNMPv2c
        value: 1
  # field-param field key
  - field: community
    # name-param field display i18n name
    name:
      zh-CN: SNMP 团体字
      en-US: SNMP Community
    # type-param field type(most mapping the html input type)
    type: text
    # when type is text, use limit to limit string length
    limit: 100
    # required-true or false
    required: true
    # param field input placeholder
    placeholder: 'Snmp community for v1 v2c'
  # field-param field key
  - field: timeout
    # name-param field display i18n name
    name:
      zh-CN: 超时时间(ms)
      en-US: Timeout(ms)
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,100000]'
    # required-true or false
    required: false
    # hide-is hide this field and put it in advanced layout
    hide: true
    # default value
    defaultValue: 6000
# collect metrics config list
metrics:
  # metrics - system
  - name: system
    i18n:
      zh-CN: 系统
      en-US: System
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    fields:
      - field: modelName
        type: 1
        i18n:
          zh-CN: 名称
          en-US: modelName
      - field: serialNumber
        type: 1
        i18n:
          zh-CN: 序列号
          en-US: serialNumber
      - field: version
        type: 1
        i18n:
          zh-CN: 版本信息
          en-US: version
      - field: temperature
        type: 0
        i18n:
          zh-CN: 系统温度
          en-US: System Temperature
        unit: '°C'
      - field: powerStatus
        type: 1
        i18n:
          zh-CN: 电源状态
          en-US: powerStatus
      - field: systemFanStatus
        type: 1
        i18n:
          zh-CN: 系统风扇状态
          en-US: systemFanStatus
      - field: cpuFanStatus
        type: 1
        i18n:
          zh-CN: CPU风扇状态
          en-US: cpuFanStatus
      - field: systemStatus
        type: 0
        i18n:
          zh-CN: 系统状态
          en-US: systemStatus
      - field: responseTime
        type: 0
        i18n:
          zh-CN: 响应时间
          en-US: Response Time
        unit: ms
    # 根据群晖官方MIB文档修正OID：
    # 移除不存在的OID：
    # - systemFanSpeed (*******.4.1.6574.1.4.3) - 群晖只提供风扇状态，不提供转速
    # - cpuFanSpeed (*******.4.1.6574.1.4.4) - 群晖只提供风扇状态，不提供转速
    # - cpuTemperature (*******.4.1.6574.1.2.1) - 群晖只提供系统温度，不提供CPU温度
    # - powerConsumption (*******.4.1.6574.1.3.1) - 群晖不支持功耗监控
    # - powerHealthStatus (*******.4.1.6574.1.3.2) - 群晖只提供电源状态，不提供健康状态
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        systemStatus: *******.4.1.6574.1.1
        temperature: *******.4.1.6574.1.2
        powerStatus: *******.4.1.6574.1.3
        systemFanStatus: *******.4.1.6574.1.4.1
        cpuFanStatus: *******.4.1.6574.1.4.2
        modelName: *******.4.1.6574.1.5.1
        serialNumber: *******.4.1.6574.1.5.2
        version: *******.4.1.6574.1.5.3


  - name: cpu
    i18n:
      zh-CN: CPU
      en-US: CPU
    priority: 1
    fields:
      - field: laLoadInt1
        type: 0
        i18n:
          zh-CN: 过去 1 分钟内的系统平均负载
          en-US: laLoadInt 1
      - field: laLoadInt2
        type: 0
        i18n:
          zh-CN: 过去 5 分钟内的系统平均负载
          en-US: laLoadInt 2
      - field: laLoadInt3
        type: 0
        i18n:
          zh-CN: 过去 15 分钟内的系统平均负载
          en-US: laLoadInt 3
      - field: ssCpuUser
        type: 0
        i18n:
          zh-CN: 用户进程CPU 时间百分比
          en-US: ssCpuUser
      - field: ssCpuSystem
        type: 0
        i18n:
          zh-CN: 系统进程CPU 时间百分比
          en-US: ssCpuSystem
      - field: ssCpuIdle
        type: 0
        i18n:
          zh-CN: CPU 闲置时间百分比
          en-US: ssCpuIdle
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: get
      oids:
        laLoadInt.1: *******.4.1.2021.********
        laLoadInt.2: *******.4.1.2021.********
        laLoadInt.3: *******.4.1.2021.********
        ssCpuUser: *******.4.1.2021.11.9.0
        ssCpuSystem: *******.4.1.2021.11.10.0
        ssCpuIdle: *******.4.1.2021.11.11.0
  - name: storages
    i18n:
      zh-CN: 存储
      en-US: Storages
    priority: 1
    fields:
      - field: diskIODevice
        type: 1
        i18n:
          zh-CN: 设备名称
          en-US: diskIODevice
      - field: index
        type: 0
        i18n:
          zh-CN: 存储 Index
          en-US: Storage Index
      - field: descr
        type: 1
        i18n:
          zh-CN: 存储描述
          en-US: Storage Description
        label: true
      - field: size
        i18n:
          zh-CN: 存储大小
          en-US: Storage Size
        type: 0
        unit: GB
      - field: free
        type: 0
        i18n:
          zh-CN: 存储空闲
          en-US: Storage Free
        unit: GB
      - field: used
        type: 0
        i18n:
          zh-CN: 存储占用
          en-US: Storage Used
        unit: GB
      - field: usage
        type: 0
        i18n:
          zh-CN: 存储使用率
          en-US: Storage Usage
        unit: '%'


    # (Not required) Monitor indicator alias, which maps to the indicator name above. The field used to collect interface data is not directly the final indicator name, and this alias is required for mapping translation
    aliasFields:
      - hrStorageIndex
      - hrStorageDescr
      - hrStorageSize
      - hrStorageUsed
      - hrStorageAllocationUnits
      - diskIODevice
    # The (optional) metric evaluation expression, which works with the aliases above, calculates the final desired metric value
    # eg: cores=core1+core2, usage=usage, waitTime=allTime-runningTime
    calculates:
      - index=hrStorageIndex
      - descr=hrStorageDescr
      - size=hrStorageSize * hrStorageAllocationUnits
      - free=(hrStorageSize - hrStorageUsed) * hrStorageAllocationUnits
      - used=hrStorageUsed * hrStorageAllocationUnits
      - usage= hrStorageUsed / hrStorageSize * 100
    units:
      - free=B->GB
      - size=B->GB
      - used=B->GB
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hrStorageDescr: *******.********.3.1.3
        hrStorageIndex: *******.********.3.1.1
        hrStorageSize: *******.********.3.1.5
        hrStorageUsed: *******.********.3.1.6
        hrStorageAllocationUnits: *******.********.3.1.4
        diskIODevice: *******.4.1.2021.*********.2


  - name: disk
    i18n:
      zh-CN: 磁盘
      en-US: Disk
    priority: 2
    fields:
      - field: diskID
        type: 1
        i18n:
          zh-CN: 硬盘名称
          en-US: diskID
        label: true
      - field: diskModel
        type: 1
        i18n:
          zh-CN: 硬盘型号
          en-US: diskModel

      - field: diskType
        type: 1
        i18n:
          zh-CN: 硬盘类型
          en-US: diskType
      - field: diskStatus
        type: 0
        i18n:
          zh-CN: 当前硬盘状态
          en-US: diskStatus

      - field: diskHealthStatus
        type: 0
        i18n:
          zh-CN: 当前硬盘运行状况
          en-US: diskHealthStatus
      - field: diskTemperature
        type: 0
        i18n:
          zh-CN: 硬盘温度
          en-US: diskTemperature

      - field: diskRemainLife
        type: 0
        i18n:
          zh-CN: 预计剩余寿命
          en-US: diskRemainLife
        unit: '%'
      - field: diskSerialNumber
        type: 1
        i18n:
          zh-CN: 磁盘序列号
          en-US: Disk Serial Number
      - field: diskUsageHours
        type: 0
        i18n:
          zh-CN: 磁盘使用时间
          en-US: Disk Usage Hours
        unit: 'hours'
      - field: diskHealthPercentage
        type: 0
        i18n:
          zh-CN: 磁盘健康百分比
          en-US: Disk Health Percentage
        unit: '%'

    units:
      - hrDiskStorageCapacity=KB->MB
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        diskEntry: *******.4.1.6574.2.1.1
        diskIndex: *******.4.1.6574.*******
        diskID: *******.4.1.6574.*******
        diskModel: *******.4.1.6574.*******
        diskType: *******.4.1.6574.*******
        diskStatus: *******.4.1.6574.*******
        diskHealthStatus: *******.4.1.6574.*******3
        diskTemperature: *******.4.1.6574.*******
        diskRemainLife: *******.4.1.6574.*******1
        diskSerialNumber: *******.4.1.6574.*******5
        diskUsageHours: *******.4.1.6574.*******
        diskHealthPercentage: *******.4.1.6574.********


  # 新增：卷使用率监控
  - name: volumes
    i18n:
      zh-CN: 存储卷
      en-US: Storage Volumes
    priority: 2
    fields:
      - field: volumeIndex
        type: 0
        i18n:
          zh-CN: 卷索引
          en-US: Volume Index
      - field: volumeName
        type: 1
        i18n:
          zh-CN: 卷名称
          en-US: Volume Name
        label: true
      - field: volumeStatus
        type: 0
        i18n:
          zh-CN: 卷状态
          en-US: Volume Status
      - field: volumeSize
        type: 0
        i18n:
          zh-CN: 卷总容量
          en-US: Volume Total Size
        unit: GB
      - field: volumeUsed
        type: 0
        i18n:
          zh-CN: 卷已用空间
          en-US: Volume Used Space
        unit: GB
      - field: volumeFree
        type: 0
        i18n:
          zh-CN: 卷可用空间
          en-US: Volume Free Space
        unit: GB
      - field: volumeUsagePercent
        type: 0
        i18n:
          zh-CN: 卷使用率
          en-US: Volume Usage Percentage
        unit: '%'
    aliasFields:
      - volumeIndex
      - volumeName
      - volumeStatus
      - volumeSize
      - volumeUsed
      - volumeFree
    calculates:
      - volumeUsagePercent = volumeUsed / volumeSize * 100
    units:
      - volumeSize=B->GB
      - volumeUsed=B->GB
      - volumeFree=B->GB
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        volumeIndex: *******.4.1.6574.*******
        volumeName: *******.4.1.6574.*******
        volumeStatus: *******.4.1.6574.*******
        volumeSize: *******.4.1.6574.*******
        volumeUsed: *******.4.1.6574.*******
        volumeFree: *******.4.1.6574.*******


  # 新增：IOPS性能指标
  - name: disk_performance
    i18n:
      zh-CN: 磁盘性能
      en-US: Disk Performance
    priority: 3
    fields:
      - field: diskIndex
        type: 0
        i18n:
          zh-CN: 磁盘索引
          en-US: Disk Index
      - field: diskName
        type: 1
        i18n:
          zh-CN: 磁盘名称
          en-US: Disk Name
        label: true
      - field: diskReadIOPS
        type: 0
        i18n:
          zh-CN: 读取IOPS
          en-US: Read IOPS
        unit: 'ops/s'
      - field: diskWriteIOPS
        type: 0
        i18n:
          zh-CN: 写入IOPS
          en-US: Write IOPS
        unit: 'ops/s'
      - field: diskTotalIOPS
        type: 0
        i18n:
          zh-CN: 总IOPS
          en-US: Total IOPS
        unit: 'ops/s'
      - field: diskReadThroughput
        type: 0
        i18n:
          zh-CN: 读取吞吐量
          en-US: Read Throughput
        unit: 'MB/s'
      - field: diskWriteThroughput
        type: 0
        i18n:
          zh-CN: 写入吞吐量
          en-US: Write Throughput
        unit: 'MB/s'
      - field: diskTotalThroughput
        type: 0
        i18n:
          zh-CN: 总吞吐量
          en-US: Total Throughput
        unit: 'MB/s'
    aliasFields:
      - diskIndex
      - diskName
      - diskReadIOPS
      - diskWriteIOPS
      - diskReadThroughput
      - diskWriteThroughput
    calculates:
      - diskTotalIOPS = diskReadIOPS + diskWriteIOPS
      - diskTotalThroughput = diskReadThroughput + diskWriteThroughput
    units:
      - diskReadThroughput=B->MB
      - diskWriteThroughput=B->MB
      - diskTotalThroughput=B->MB
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        diskIndex: *******.4.1.6574.*********
        diskName: *******.4.1.6574.*********
        diskReadIOPS: *******.4.1.6574.*********
        diskWriteIOPS: *******.4.1.6574.*********
        diskReadThroughput: *******.4.1.6574.*********2
        diskWriteThroughput: *******.4.1.6574.**********


  - name: interfaces
    i18n:
      zh-CN: 接口
      en-US: Interfaces
    priority: 4
    fields:
      - field: index
        type: 0
        i18n:
          zh-CN: 序号
          en-US: Index
      - field: descr
        type: 1
        i18n:
          zh-CN: 描述
          en-US: Description
        label: true
      - field: mtu
        type: 0
        i18n:
          zh-CN: 最大传输单元
          en-US: MTU
        unit: 'byte'
      - field: speed
        type: 0
        i18n:
          zh-CN: 速度
          en-US: Speed
        unit: 'KB/s'
      - field: in_octets
        type: 0
        i18n:
          zh-CN: 输入字节数
          en-US: In Octets
        unit: 'byte'
      - field: in_discards
        type: 0
        i18n:
          zh-CN: 输入丢弃数
          en-US: In Discards
        unit: 'package'
      - field: in_errors
        type: 0
        i18n:
          zh-CN: 输入错误数
          en-US: In Errors
        unit: 'package'
      - field: out_octets
        type: 0
        i18n:
          zh-CN: 输出字节数
          en-US: Out Octets
        unit: 'byte'
      - field: out_discards
        type: 0
        i18n:
          zh-CN: 输出丢弃数
          en-US: Out Discards
        unit: 'package'
      - field: out_errors
        type: 0
        i18n:
          zh-CN: 输出错误数
          en-US: Out Errors
        unit: 'package'
      - field: admin_status
        type: 1
        i18n:
          zh-CN: 管理状态
          en-US: Admin Status
      - field: oper_status
        type: 1
        i18n:
          zh-CN: 运行状态
          en-US: Oper Status
    aliasFields:
      - ifIndex
      - ifDescr
      - ifMtu
      - ifSpeed
      - ifInOctets
      - ifInDiscards
      - ifInErrors
      - ifOutOctets
      - ifOutDiscards
      - ifOutErrors
      - ifAdminStatus
      - ifOperStatus
    calculates:
      - index=ifIndex
      - descr=ifDescr
      - mtu=ifMtu
      - speed=ifSpeed / 1024
      - in_octets=ifInOctets
      - in_discards=ifInDiscards
      - in_errors=ifInErrors
      - out_octets=ifOutOctets
      - out_discards=ifOutDiscards
      - out_errors=ifOutErrors
      - admin_status=ifAdminStatus
      - oper_status=ifOperStatus
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        ifIndex: *******.*******.1.1
        ifDescr: *******.*******.1.2
        ifMtu: *******.*******.1.4
        ifSpeed: *******.*******.1.5
        ifInOctets: *******.*******.1.10
        ifInDiscards: *******.*******.1.13
        ifInErrors: *******.*******.1.14
        ifOutOctets: *******.*******.1.16
        ifOutDiscards: *******.*******.1.19
        ifOutErrors: *******.*******.1.20
        ifAdminStatus: *******.*******.1.7
        ifOperStatus: *******.*******.1.8

