# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring custom-custom monitoring os-operating system monitoring
# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: db
# The monitoring type eg: linux windows tomcat mysql aws...
# 监控类型 eg: linux windows tomcat mysql aws...
app: tidb
# The monitoring i18n name
# 监控类型国际化名称
name:
  zh-CN: TiDB
  en-US: TiDB
# The description and help of this monitoring type
# 监控类型的帮助描述信息

# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # field-param field key
  - field: port
    # name-param field display i18n name
    name:
      zh-CN: JDBC 端口
      en-US: JDBC Port
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    # required-true or false
    required: false
    defaultValue: 4000
    # hide param-true or false
    hide: true
  - field: service-port
    name:
      zh-CN: Service 端口
      en-US: Service Port
    type: number
    range: '[0,65535]'
    required: true
    defaultValue: 10080

  - field: pd-port
    name:
      zh-CN: PD 端口
      en-US: PD Port
    type: number
    range: '[0,65535]'
    required: false
    defaultValue: 2379

  - field: timeout
    name:
      zh-CN: 查询超时时间(ms)
      en-US: Query Timeout(ms)
    type: number
    required: false
    defaultValue: 6000

  - field: database
    name:
      zh-CN: 数据库名称
      en-US: Database Name
    type: text
    required: false
    hide: true

  - field: username
    name:
      zh-CN: 用户名
      en-US: Username
    type: text
    limit: 50
    required: false
    hide: true

  - field: password
    name:
      zh-CN: 密码
      en-US: Password
    type: password
    required: false
    hide: true

  - field: url
    name:
      zh-CN: JDBC URL
      en-US: JDBC URL
    type: text
    required: false
    hide: true
# collect metrics config list
metrics:
  # metrics - status
  - name: status
    i18n:
      zh-CN: 状态
      en-US: Status
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    # collect metrics content
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      - field: connections
        type: 0
        i18n:
          zh-CN: 连接数
          en-US: Connections
      - field: version
        type: 1
        i18n:
          zh-CN: 版本
          en-US: Version
      - field: git_hash
        type: 1
        i18n:
          zh-CN: Git Hash
          en-US: Git Hash
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: http
    http:
      # http host: ipv4 ipv6 domain
      host: ^_^host^_^
      # http port
      port: ^_^service-port^_^
      # http url
      url: '/status'
      timeout: ^_^timeout^_^
      # http method: GET POST PUT DELETE PATCH
      method: GET
      # http response data parse type: default-hertzbeat rule, jsonpath-jsonpath script, website-for website monitoring, prometheus-prometheus exporter rule
      parseType: jsonPath
      parseScript: '$'

  - name: stores
    i18n:
      zh-CN: 存储
      en-US: Stores
    priority: 1
    fields:
      - field: address
        type: 1
        i18n:
          zh-CN: 地址
          en-US: Address
      - field: version
        type: 1
        i18n:
          zh-CN: 版本
          en-US: Version
      - field: status_address
        type: 1
        i18n:
          zh-CN: 状态地址
          en-US: Status Address
      - field: deploy_path
        type: 1
        i18n:
          zh-CN: 部署路径
          en-US: Deploy Path
      - field: state_name
        type: 1
        i18n:
          zh-CN: 状态名称
          en-US: State Name
      - field: capacity
        type: 1
        i18n:
          zh-CN: 容量
          en-US: Capacity
      - field: available
        type: 1
        i18n:
          zh-CN: 可用
          en-US: Available
      - field: used_size
        type: 1
        i18n:
          zh-CN: 已用
          en-US: Used
      - field: start_ts
        type: 1
        i18n:
          zh-CN: 启动时间
          en-US: Start Time
      - field: last_heartbeat_ts
        type: 1
        i18n:
          zh-CN: 上次心跳时间
          en-US: Last Heartbeat Time
      - field: uptime
        type: 1
        i18n:
          zh-CN: 启动时长
          en-US: Uptime
    aliasFields:
      - $.store.address
      - $.store.version
      - $.store.status_address
      - $.store.deploy_path
      - $.store.state_name
      - $.status.capacity
      - $.status.available
      - $.status.used_size
      - $.status.start_ts
      - $.status.last_heartbeat_ts
      - $.status.uptime
    calculates:
      - address=$.store.address
      - version=$.store.version
      - status_address=$.store.status_address
      - deploy_path=$.store.deploy_path
      - state_name=$.store.state_name
      - capacity=$.status.capacity
      - available=$.status.available
      - used_size=$.status.used_size
      - start_ts=$.status.start_ts
      - last_heartbeat_ts=$.status.last_heartbeat_ts
      - uptime=$.status.uptime
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^pd-port^_^
      url: '/pd/api/v1/stores'
      timeout: ^_^timeout^_^
      method: GET
      parseType: jsonPath
      parseScript: '$.stores.*'

  - name: basic
    i18n:
      zh-CN: 基本信息
      en-US: Basic Info
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 2
    # collect metrics content
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      - field: version
        type: 1
        i18n:
          zh-CN: 版本
          en-US: Version
        label: true
      - field: port
        type: 1
        i18n:
          zh-CN: 端口
          en-US: Port
      - field: datadir
        type: 1
        i18n:
          zh-CN: 数据目录
          en-US: Data Directory
      - field: max_connections
        type: 0
        i18n:
          zh-CN: 最大连接数
          en-US: Max Connections
    # (optional)metrics field alias name, it is used as an alias field to map and convert the collected data and metrics field
    aliasFields:
      - version
      - port
      - datadir
      - max_connections
    # (optional)mapping and conversion expressions, use these and aliasField above to calculate metrics value
    # eg: cores=core1+core2, usage=usage, waitTime=allTime-runningTime
    calculates:
      - port=port
      - datadir=datadir
      - max_connections=max_connections
      - version=version+"_"+version_compile_os+"_"+version_compile_machine
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: jdbc
    # the config content when protocol is jdbc
    jdbc:
      # host: ipv4 ipv6 host
      host: ^_^host^_^
      # port
      port: ^_^port^_^
      # database platform name
      platform: mysql
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      # SQL Query Method：oneRow, multiRow, columns
      queryType: columns
      # sql
      sql: show global variables where Variable_name like 'version%' or Variable_name = 'max_connections' or Variable_name = 'datadir' or Variable_name = 'port';
      # JDBC url
      url: ^_^url^_^

