# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring custom-custom monitoring os-operating system monitoring
# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: infra
# The monitoring type eg: linux windows tomcat mysql aws...
# 监控类型 eg: linux windows tomcat mysql aws...
app: infra_ipmi
# The monitoring i18n name
# 监控类型国际化名称
name:
  zh-CN: IPMI带外管理
  en-US: IPMI
# 监控所需输入参数定义(根据定义渲染页面UI)
# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  # field-变量字段标识符
  - field: host
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: host
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: true
  # field-param field key
  # field-变量字段标识符
  - field: port
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 端口
      en-US: Port
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: number
    # when type is number, range is required
    # 当type为number时,用range表示范围
    range: '[0,65535]'
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: true
    # default value
    # 默认值
    defaultValue: 623
  # field-param field key
  # field-变量字段标识符
  - field: username
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 用户名
      en-US: Username
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: text
    # when type is text, use limit to limit string length
    # 当type为text时,用limit表示字符串限制大小
    limit: 50
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: false
  # field-param field key
  # field-变量字段标识符
  - field: password
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 密码
      en-US: Password
    # type-param field type(most mapping the html input tag)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: password
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: false
# collect metrics config list
# 采集指标配置列表
metrics:
  # metrics - cpu
  # 监控指标 - cpu
  - name: Chassis
    i18n:
      zh-CN: 面板信息
      en-US: Chassis
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    # 指标采集调度优先级(0->127)->(优先级高->低) 优先级低的指标会等优先级高的指标采集完成后才会被调度, 相同优先级的指标会并行调度采集
    # 优先级为0的指标为可用性指标,即它会被首先调度,采集成功才会继续调度其它指标,采集失败则中断调度
    priority: 0
    # collect metrics content
    # 具体监控指标列表
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      # field-指标名称, type-指标类型(0-number数字,1-string字符串), unit-指标单位('%','ms','MB'), label-是否是指标标签字段
      - field: system_power
        type: 1
        i18n:
          zh-CN: 系统电源状态
          en-US: System Power
      - field: power_overload
        type: 1
        i18n:
          zh-CN: 电源过载
          en-US: Power Overload
      - field: power_interlock
        type: 1
        i18n:
          zh-CN: 电源互锁
          en-US: Power Interlock
      - field: power_fault
        type: 1
        i18n:
          zh-CN: 主电源故障
          en-US: Main Power Fault
      - field: power_control_fault
        type: 1
        i18n:
          zh-CN: 电源控制故障
          en-US: Power Control Fault
      - field: power_restore_policy
        type: 1
        i18n:
          zh-CN: 电源恢复策略
          en-US: Power Restore Policy
      - field: last_power_event
        type: 1
        i18n:
          zh-CN: 最后一次电源事件
          en-US: Last Power Event
      - field: fan_fault
        type: 1
        i18n:
          zh-CN: 风扇故障
          en-US: Cooling/Fan Fault
      - field: drive_fault
        type: 1
        i18n:
          zh-CN: 硬盘故障
          en-US: Drive Fault
      - field: front_panel_lockout_active
        type: 1
        i18n:
          zh-CN: 前面板锁定
          en-US: Front-Panel Lockout
    # (optional)metrics field alias name, it is used as an alias field to map and convert the collected data and metrics field
    # (可选)监控指标别名, 做为中间字段与采集数据字段和指标字段映射转换
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: ipmi
    # the config content when protocol is ipmi
    ipmi:
      # ipmi host: ipv4 ipv6 domain
      host: ^_^host^_^
      # ipmi port
      port: ^_^port^_^
      # ipmi username
      username: ^_^username^_^
      # ipmi password
      password: ^_^password^_^
      # ipmi command type: Chassis | Sensor
      type: Chassis
  - name: Sensor
    i18n:
      zh-CN: 传感器
      en-US: Sensor
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    # 指标采集调度优先级(0->127)->(优先级高->低) 优先级低的指标会等优先级高的指标采集完成后才会被调度, 相同优先级的指标会并行调度采集
    # 优先级为0的指标为可用性指标,即它会被首先调度,采集成功才会继续调度其它指标,采集失败则中断调度
    priority: 1
    # collect metrics content
    # 具体监控指标列表
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      # field-指标名称, type-指标类型(0-number数字,1-string字符串), unit-指标单位('%','ms','MB'), label-是否是指标标签字段
      - field: sensor_id
        type: 1
        i18n:
          zh-CN: 传感器标识
          en-US: Sensor ID
      - field: entity_id
        type: 1
        i18n:
          zh-CN: 实体标识
          en-US: Entity ID
      - field: sensor_type
        type: 1
        i18n:
          zh-CN: 传感器类型
          en-US: Sensor Type
      - field: sensor_reading
        type: 1
        i18n:
          zh-CN: 传感器读数
          en-US: Sensor Reading
    # (optional)metrics field alias name, it is used as an alias field to map and convert the collected data and metrics field
    # (可选)监控指标别名, 做为中间字段与采集数据字段和指标字段映射转换
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: ipmi
    # the config content when protocol is ipmi
    ipmi:
      # ipmi host: ipv4 ipv6 domain
      host: ^_^host^_^
      # ipmi port
      port: ^_^port^_^
      # ipmi username
      username: ^_^username^_^
      # ipmi password
      password: ^_^password^_^
      # ipmi command type: Chassis | Sensor
      type: Sensor
  - name: Storage
    i18n:
      zh-CN: 存储设备
      en-US: Storage
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    # 指标采集调度优先级(0->127)->(优先级高->低) 优先级低的指标会等优先级高的指标采集完成后才会被调度, 相同优先级的指标会并行调度采集
    # 优先级为0的指标为可用性指标,即它会被首先调度,采集成功才会继续调度其它指标,采集失败则中断调度
    priority: 2
    # collect metrics content
    # 具体监控指标列表
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      # field-指标名称, type-指标类型(0-number数字,1-string字符串), unit-指标单位('%','ms','MB'), label-是否是指标标签字段
      - field: fru_device_id
        type: 1
        i18n:
          zh-CN: FRU设备ID
          en-US: FRU Device ID
      - field: device_type
        type: 1
        i18n:
          zh-CN: 设备类型
          en-US: Device Type
      - field: health_status
        type: 1
        i18n:
          zh-CN: 健康状态
          en-US: Health Status
      - field: manufacturer
        type: 1
        i18n:
          zh-CN: 制造商
          en-US: Manufacturer
      - field: product_name
        type: 1
        i18n:
          zh-CN: 产品名称
          en-US: Product Name
      - field: part_number
        type: 1
        i18n:
          zh-CN: 部件号
          en-US: Part Number
      - field: serial_number
        type: 1
        i18n:
          zh-CN: 序列号
          en-US: Serial Number
      - field: version
        type: 1
        i18n:
          zh-CN: 版本
          en-US: Version
      - field: board_manufacturer
        type: 1
        i18n:
          zh-CN: 板卡制造商
          en-US: Board Manufacturer
      - field: board_serial_number
        type: 1
        i18n:
          zh-CN: 板卡序列号
          en-US: Board Serial Number
      - field: device_age_days
        type: 0
        unit: days
        i18n:
          zh-CN: 设备使用天数
          en-US: Device Age (Days)
      - field: estimated_lifespan
        type: 1
        i18n:
          zh-CN: 预估寿命状态
          en-US: Estimated Lifespan
      - field: fru_data_size
        type: 0
        unit: bytes
        i18n:
          zh-CN: FRU数据大小
          en-US: FRU Data Size
      - field: access_method
        type: 1
        i18n:
          zh-CN: 访问方式
          en-US: Access Method
    # (optional)metrics field alias name, it is used as an alias field to map and convert the collected data and metrics field
    # (可选)监控指标别名, 做为中间字段与采集数据字段和指标字段映射转换
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: ipmi
    # the config content when protocol is ipmi
    ipmi:
      # ipmi host: ipv4 ipv6 domain
      host: ^_^host^_^
      # ipmi port
      port: ^_^port^_^
      # ipmi username
      username: ^_^username^_^
      # ipmi password
      password: ^_^password^_^
      # ipmi command type: Chassis | Sensor | Storage
      type: Storage
  - name: PhysicalDisk
    i18n:
      zh-CN: 物理磁盘
      en-US: Physical Disk
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    # 指标采集调度优先级(0->127)->(优先级高->低) 优先级低的指标会等优先级高的指标采集完成后才会被调度, 相同优先级的指标会并行调度采集
    # 优先级为0的指标为可用性指标,即它会被首先调度,采集成功才会继续调度其它指标,采集失败则中断调度
    priority: 3
    # collect metrics content
    # 具体监控指标列表
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      # field-指标名称, type-指标类型(0-number数字,1-string字符串), unit-指标单位('%','ms','MB'), label-是否是指标标签字段
      - field: disk_index
        type: 0
        label: true
        i18n:
          zh-CN: 磁盘索引
          en-US: Disk Index
      - field: disk_name
        type: 1
        label: true
        i18n:
          zh-CN: 磁盘名称
          en-US: Disk Name
      - field: disk_status
        type: 1
        i18n:
          zh-CN: 磁盘状态
          en-US: Disk Status
      - field: slot_number
        type: 1
        i18n:
          zh-CN: 插槽编号
          en-US: Slot Number
      - field: disk_capacity
        type: 1
        i18n:
          zh-CN: 磁盘大小
          en-US: Disk Capacity
      - field: disk_interface
        type: 1
        i18n:
          zh-CN: 总线协议
          en-US: Bus Protocol
      - field: disk_type
        type: 1
        i18n:
          zh-CN: 介质类型
          en-US: Media Type
      - field: hot_spare_status
        type: 1
        i18n:
          zh-CN: 热备份状态
          en-US: Hot Spare Status
      - field: disk_health
        type: 1
        i18n:
          zh-CN: 磁盘健康状态
          en-US: Disk Health
      - field: disk_rpm
        type: 1
        unit: 'RPM'
        i18n:
          zh-CN: 磁盘转速
          en-US: Disk RPM
      - field: sensor_id
        type: 1
        i18n:
          zh-CN: 传感器ID
          en-US: Sensor ID
      - field: sensor_type
        type: 1
        i18n:
          zh-CN: 传感器类型
          en-US: Sensor Type
      - field: sensor_reading
        type: 1
        i18n:
          zh-CN: 传感器读数
          en-US: Sensor Reading
      - field: entity_id
        type: 1
        i18n:
          zh-CN: 实体ID
          en-US: Entity ID
      - field: sensor_number
        type: 0
        i18n:
          zh-CN: 传感器编号
          en-US: Sensor Number
    # (optional)metrics field alias name, it is used as an alias field to map and convert the collected data and metrics field
    # (可选)监控指标别名, 做为中间字段与采集数据字段和指标字段映射转换
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: ipmi
    # the config content when protocol is ipmi
    ipmi:
      # ipmi host: ipv4 ipv6 domain
      host: ^_^host^_^
      # ipmi port
      port: ^_^port^_^
      # ipmi username
      username: ^_^username^_^
      # ipmi password
      password: ^_^password^_^
      # ipmi command type: Chassis | Sensor | Storage | PhysicalDisk
      type: PhysicalDisk
