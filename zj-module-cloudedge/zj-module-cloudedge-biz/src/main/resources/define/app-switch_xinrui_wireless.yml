category: network
app: switch_xinrui_wireless
name:
  zh-CN: 信锐无线控制器
  en-US: Xinrui Wireless Controller
params:
  - field: host
    name:
      zh-CN: 目标Host
      en-US: Target Host
    type: host
    required: true
  - field: port
    name:
      zh-CN: 端口
      en-US: Port
    type: number
    range: '[0,65535]'
    required: true
    defaultValue: 161
  - field: version
    name:
      zh-CN: SNMP 版本
      en-US: SNMP Version
    type: radio
    required: true
    options:
      - label: SNMPv1
        value: 0
      - label: SNMPv2c
        value: 1
      - label: SNMPv3
        value: 3
  - field: community
    name:
      zh-CN: SNMP 团体字
      en-US: SNMP Community
    type: text
    limit: 100
    required: false
    placeholder: 'Snmp community for v1 v2c'
  - field: username
    name:
      zh-CN: SNMP username
      en-US: SNMP username
    type: text
    limit: 20
    required: false
    placeholder: 'Snmp username for v3'
  - field: contextName
    name:
      zh-CN: SNMP contextName
      en-US: SNMP contextName
    type: text
    limit: 100
    required: false
    placeholder: 'Snmp contextName for v3'
  - field: authPassphrase
    name:
      zh-CN: SNMP authPassword
      en-US: SNMP authPassword
    type: text
    limit: 100
    required: false
    placeholder: 'Snmp authPassword for v3'
  - field: authPasswordEncryption
    name:
      zh-CN: authPassword 加密方式
      en-US: authPassword Encryption
    type: radio
    required: false
    options:
      - label: MD5
        value: 0
      - label: SHA1
        value: 1
  - field: privPassphrase
    name:
      zh-CN: SNMP privPassphrase
      en-US: SNMP privPassphrase
    type: text
    limit: 100
    required: false
    placeholder: 'Snmp privPassphrase for v3'
  - field: privPasswordEncryption
    name:
      zh-CN: privPassword 加密方式
      en-US: privPassword Encryption
    type: radio
    required: false
    options:
      - label: DES
        value: 0
      - label: AES128
        value: 1
  - field: timeout
    name:
      zh-CN: 超时时间(ms)
      en-US: Timeout(ms)
    type: number
    range: '[0,100000]'
    required: false
    hide: true
    defaultValue: 6000

# 采集指标配置列表 (基于SANGFOR-GENERAL-MIB.txt)
metrics:
  # 系统基本信息
  - name: system
    i18n:
      zh-CN: 系统信息
      en-US: System Info
    priority: 0
    fields:
      - field: name
        type: 1
        i18n:
          zh-CN: 主机名称
          en-US: Host Name
      - field: descr
        type: 1
        i18n:
          zh-CN: 描述信息
          en-US: Description
      - field: uptime
        type: 1
        i18n:
          zh-CN: 运行时长
          en-US: Uptime
      - field: location
        type: 1
        i18n:
          zh-CN: 位置
          en-US: Location
      - field: contact
        type: 1
        i18n:
          zh-CN: 联系人
          en-US: Contact
      - field: responseTime
        type: 0
        unit: ms
        i18n:
          zh-CN: 响应时间
          en-US: Response Time
      - field: deviceDescr
        type: 1
        i18n:
          zh-CN: 设备描述
          en-US: Device Description
      - field: sysName
        type: 1
        i18n:
          zh-CN: 系统名称
          en-US: System Name
      - field: sysIp
        type: 1
        i18n:
          zh-CN: 系统IP
          en-US: System IP
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      authPassphrase: ^_^authPassphrase^_^
      privPassphrase: ^_^privPassphrase^_^
      privPasswordEncryption: ^_^privPasswordEncryption^_^
      authPasswordEncryption: ^_^authPasswordEncryption^_^
      contextName: ^_^contextName^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: get
      oids:
        name: *******.*******.0
        descr: *******.*******.0
        uptime: *******.*******.0
        location: *******.*******.0
        contact: *******.*******.0
        # 信锐企业OID: *******.4.1.35047 (根据MIB文件确认)
        deviceDescr: *******.4.1.35047.1.1.0      # 设备描述
        sysName: *******.4.1.35047.1.10.0         # 系统名称
        sysIp: *******.4.1.35047.1.12.0           # 系统IP

  # 系统性能监控
  - name: system_performance
    i18n:
      zh-CN: 系统性能
      en-US: System Performance
    priority: 1
    fields:
      - field: cpu_usage
        type: 0
        unit: '%'
        i18n:
          zh-CN: CPU使用率
          en-US: CPU Usage
      - field: memory_total
        type: 1
        unit: 'MB'
        i18n:
          zh-CN: 总内存
          en-US: Total Memory
      - field: memory_free
        type: 1
        unit: 'MB'
        i18n:
          zh-CN: 空闲内存
          en-US: Free Memory
      - field: memory_usage
        type: 0
        unit: '%'
        i18n:
          zh-CN: 内存使用率
          en-US: Memory Usage
    aliasFields:
      - cpuCostRate
      - totalMemory
      - freeMemory
    calculates:
      - cpu_usage=cpuCostRate
      - memory_total=totalMemory
      - memory_free=freeMemory
      - memory_usage=(memory_total - memory_free)  / memory_total * 100
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      authPassphrase: ^_^authPassphrase^_^
      privPassphrase: ^_^privPassphrase^_^
      privPasswordEncryption: ^_^privPasswordEncryption^_^
      authPasswordEncryption: ^_^authPasswordEncryption^_^
      contextName: ^_^contextName^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: get
      oids:
        # 信锐企业OID: *******.4.1.35047 (根据MIB文件确认)
        cpuCostRate: *******.4.1.35047.1.3.0      # CPU使用率
        totalMemory: *******.4.1.35047.1.9.0      # 总内存
        freeMemory: *******.4.1.35047.1.4.0       # 空闲内存

  # 接口详情
  - name: interface
    i18n:
      zh-CN: 接口详情
      en-US: Interfaces Detail
    priority: 2
    fields:
      - field: index
        type: 1
        i18n:
          zh-CN: 编号
          en-US: Index
      - field: interface_name
        type: 1
        label: true
        i18n:
          zh-CN: 接口名称
          en-US: Interface Name
      - field: alias
        type: 1
        i18n:
          zh-CN: 接口备注
          en-US: alias
      - field: admin_status
        type: 1
        i18n:
          zh-CN: 配置状态
          en-US: Config Status
      - field: oper_status
        type: 1
        i18n:
          zh-CN: 当前状态
          en-US: Current Status
      - field: mtu
        type: 0
        unit: 'byte'
        i18n:
          zh-CN: MTU
          en-US: MTU
      - field: speed
        type: 0
        unit: 'MB/s'
        i18n:
          zh-CN: 接口速率
          en-US: Interface Speed
      - field: in_octets
        type: 0
        unit: 'Mbps'
        i18n:
          zh-CN: 入流量
          en-US: In Octets
      - field: in_discards
        type: 0
        unit: 'package'
        i18n:
          zh-CN: 入丢包数
          en-US: In Discards
      - field: in_errors
        type: 0
        unit: 'package'
        i18n:
          zh-CN: 入错包数
          en-US: In Errors
      - field: out_octets
        type: 0
        unit: 'Mbps'
        i18n:
          zh-CN: 出流量
          en-US: Out Octets
      - field: out_discards
        type: 0
        unit: 'package'
        i18n:
          zh-CN: 出丢包数
          en-US: Out Discards
      - field: out_errors
        type: 0
        unit: 'package'
        i18n:
          zh-CN: 出错包数
          en-US: Out Errors
    aliasFields:
      - ifIndex
      - ifDescr
      - ifMtu
      - ifSpeed
      - ifInOctets
      - ifInDiscards
      - ifInErrors
      - ifOutOctets
      - ifOutDiscards
      - ifOutErrors
      - ifAdminStatus
      - ifOperStatus
      - ifAlias
    calculates:
      - index=ifIndex
      - interface_name=ifDescr
      - mtu=ifMtu
      - speed=ifSpeed
      - in_octets=ifInOctets / 1024 / 1024
      - in_discards=ifInDiscards
      - in_errors=ifInErrors
      - out_octets=ifOutOctets / 1024 / 1024
      - out_discards=ifOutDiscards
      - out_errors=ifOutErrors
      - admin_status=ifAdminStatus
      - oper_status=ifOperStatus
      - alias=ifAlias
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      authPassphrase: ^_^authPassphrase^_^
      privPassphrase: ^_^privPassphrase^_^
      privPasswordEncryption: ^_^privPasswordEncryption^_^
      authPasswordEncryption: ^_^authPasswordEncryption^_^
      contextName: ^_^contextName^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        ifIndex: *******.*******.1.1
        ifDescr: *******.*******.1.2
        ifMtu: *******.*******.1.4
        ifSpeed: *******.********.1.1.15
        ifInOctets: *******.*******.1.10
        ifInDiscards: *******.*******.1.13
        ifInErrors: *******.*******.1.14
        ifOutOctets: *******.*******.1.16
        ifOutDiscards: *******.*******.1.19
        ifOutErrors: *******.*******.1.20
        ifAdminStatus: *******.*******.1.7
        ifOperStatus: *******.*******.1.8
        ifAlias: *******.********.1.1.18

  # AP接入点信息
  - name: ap
    i18n:
      zh-CN: AP接入点
      en-US: Access Points
    priority: 3
    fields:
      - field: ap_name
        type: 1
        label: true
        i18n:
          zh-CN: AP名称
          en-US: AP Name
      - field: ap_ip
        type: 1
        i18n:
          zh-CN: AP IP地址
          en-US: AP IP Address
      - field: ap_user_count
        type: 0
        i18n:
          zh-CN: 用户数量
          en-US: User Count
      - field: ap_status
        type: 1
        i18n:
          zh-CN: AP状态
          en-US: AP Status
      - field: ap_hardware_version
        type: 1
        i18n:
          zh-CN: 硬件版本
          en-US: Hardware Version
      - field: ap_serial_number
        type: 1
        i18n:
          zh-CN: 序列号
          en-US: Serial Number
      - field: ap_online_time
        type: 1
        i18n:
          zh-CN: 在线时长
          en-US: Online Time
      - field: ap_channel_24g
        type: 0
        i18n:
          zh-CN: 2.4G信道
          en-US: 2.4G Channel
      - field: ap_channel_5g
        type: 0
        i18n:
          zh-CN: 5G信道
          en-US: 5G Channel
    aliasFields:
      - apName
      - apIpAddress
      - apMacAddress
      - apUserCount
      - apStatus
      - apHardver
      - apSeriNum
      - apOnlineTime
      - apSsid1
      - apChannel1
      - apSsid2
      - apChannel2
    calculates:
      - ap_name=apName
      - ap_ip=apIpAddress
      - ap_mac=apMacAddress
      - ap_user_count=apUserCount
      - ap_status=apStatus
      - ap_hardware_version=apHardver
      - ap_serial_number=apSeriNum
      - ap_online_time=apOnlineTime
      - ap_ssid_24g=apSsid1
      - ap_channel_24g=apChannel1
      - ap_ssid_5g=apSsid2
      - ap_channel_5g=apChannel2
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      authPassphrase: ^_^authPassphrase^_^
      privPassphrase: ^_^privPassphrase^_^
      privPasswordEncryption: ^_^privPasswordEncryption^_^
      authPasswordEncryption: ^_^authPasswordEncryption^_^
      contextName: ^_^contextName^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        # 信锐企业OID: *******.4.1.35047 (根据MIB文件确认)
        # AP信息表: *******.4.1.35047.********.1
        apName: *******.4.1.35047.********.1.2          # AP名称
        apHardver: *******.4.1.35047.********.1.3       # AP硬件版本
        apSeriNum: *******.4.1.35047.********.1.4       # AP序列号
        apMacAddress: *******.4.1.35047.********.1.5    # AP MAC地址
        apIpAddress: *******.4.1.35047.********.1.6     # AP IP地址
        apOnlineTime: *******.4.1.35047.********.1.7    # AP在线时长
        apStatus: *******.4.1.35047.********.1.8        # AP状态
        apSsid1: *******.4.1.35047.********.1.9         # 2.4G SSID
        apChannel1: *******.4.1.35047.********.1.12     # 2.4G信道
        apSsid2: *******.4.1.35047.********.1.14        # 5G SSID
        apChannel2: *******.4.1.35047.********.1.17     # 5G信道
        apUserCount: *******.4.1.35047.********.1.19    # AP用户数量

  # AC控制器状态
  - name: ac_status
    i18n:
      zh-CN: 控制器状态
      en-US: Controller Status
    priority: 4
    fields:
      - field: ac_ap_online
        type: 0
        i18n:
          zh-CN: 在线AP数
          en-US: Online APs
      - field: ac_ap_offline
        type: 0
        i18n:
          zh-CN: 离线AP数
          en-US: Offline APs
      - field: ac_ap_deposit
        type: 0
        i18n:
          zh-CN: 预备AP数
          en-US: Deposit APs
      - field: ac_user_total
        type: 0
        i18n:
          zh-CN: 在线用户总数
          en-US: Online Users
      - field: responseTime
        type: 0
        unit: ms
        i18n:
          zh-CN: 响应时间
          en-US: Response Time
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      authPassphrase: ^_^authPassphrase^_^
      privPassphrase: ^_^privPassphrase^_^
      privPasswordEncryption: ^_^privPasswordEncryption^_^
      authPasswordEncryption: ^_^authPasswordEncryption^_^
      contextName: ^_^contextName^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: get
      oids:
        # 信锐企业OID: *******.4.1.35047 (根据MIB文件确认)
        ac_ap_online: *******.4.1.35047.********.0       # 在线AP数
        ac_ap_offline: *******.4.1.35047.********.0      # 离线AP数
        ac_ap_deposit: *******.4.1.35047.********.0      # 预备AP数
        ac_user_total: *******.4.1.35047.********.0      # 在线用户数

  # 无线用户信息
  - name: wireless_users
    i18n:
      zh-CN: 无线用户
      en-US: Wireless Users
    priority: 5
    fields:
      - field: user_name
        type: 1
        label: true
        i18n:
          zh-CN: 用户名
          en-US: User Name
      - field: user_mac
        type: 1
        i18n:
          zh-CN: 用户MAC地址
          en-US: User MAC Address
      - field: user_ip
        type: 1
        i18n:
          zh-CN: 用户IP地址
          en-US: User IP Address
      - field: user_ssid
        type: 1
        i18n:
          zh-CN: 接入SSID
          en-US: Associated SSID
      - field: user_ap_mac
        type: 1
        i18n:
          zh-CN: 接入AP MAC
          en-US: Associated AP MAC
      - field: user_terminal_type
        type: 1
        i18n:
          zh-CN: 终端类型
          en-US: Terminal Type
      - field: user_vlan_id
        type: 0
        i18n:
          zh-CN: VLAN ID
          en-US: VLAN ID
      - field: user_signal
        type: 1
        i18n:
          zh-CN: 信号强度
          en-US: Signal Strength (RSSI)
      - field: user_online_time
        type: 1
        i18n:
          zh-CN: 在线时长
          en-US: Online Duration
      - field: user_up_rate
        type: 1
        i18n:
          zh-CN: 上行速率
          en-US: Upload Rate
      - field: user_down_rate
        type: 1
        i18n:
          zh-CN: 下行速率
          en-US: Download Rate
    aliasFields:
      - userName
      - userMac
      - userIp
      - userSsid
      - userApMac
      - userTermType
      - userVlanId
      - userRssi
      - userOnlineTime
      - userUpRate
      - userDownRate
    calculates:
      - user_name=userName
      - user_mac=userMac
      - user_ip=userIp
      - user_ssid=userSsid
      - user_ap_mac=userApMac
      - user_terminal_type=userTermType
      - user_vlan_id=userVlanId
      - user_signal=userRssi
      - user_online_time=userOnlineTime
      - user_up_rate=userUpRate
      - user_down_rate=userDownRate
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      authPassphrase: ^_^authPassphrase^_^
      privPassphrase: ^_^privPassphrase^_^
      privPasswordEncryption: ^_^privPasswordEncryption^_^
      authPasswordEncryption: ^_^authPasswordEncryption^_^
      contextName: ^_^contextName^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        # 信锐企业OID: *******.4.1.35047 (根据MIB文件确认)
        # 用户信息表: *******.4.1.35047.********.1
        userName: *******.4.1.35047.********.1.2         # 用户名
        userIp: *******.4.1.35047.********.1.3           # 用户IP地址
        userTermType: *******.4.1.35047.********.1.4     # 用户终端类型
        userMac: *******.4.1.35047.********.1.5          # 用户MAC地址
        userApMac: *******.4.1.35047.********.1.6        # 用户AP MAC地址
        userSsid: *******.4.1.35047.********.1.7         # 用户SSID
        userVlanId: *******.4.1.35047.********.1.9       # 用户VLAN ID
        userOnlineTime: *******.4.1.35047.********.1.10  # 用户在线时长
        userRssi: *******.4.1.35047.********.1.11        # 用户信号强度
        userUpRate: *******.4.1.35047.********.1.12      # 用户上行速率
        userDownRate: *******.4.1.35047.********.1.13    # 用户下行速率

  # 磁盘信息
  - name: disk
    i18n:
      zh-CN: 磁盘信息
      en-US: Disk Information
    priority: 6
    fields:
      - field: filesystem_name
        type: 1
        label: true
        i18n:
          zh-CN: 文件系统名称
          en-US: Filesystem Name
      - field: disk_size
        type: 1
        i18n:
          zh-CN: 磁盘大小
          en-US: Disk Size
      - field: disk_used
        type: 1
        i18n:
          zh-CN: 已使用
          en-US: Used
      - field: disk_avail
        type: 1
        i18n:
          zh-CN: 可用空间
          en-US: Available
      - field: disk_used_percent
        type: 1
        i18n:
          zh-CN: 使用率
          en-US: Used Percentage
    aliasFields:
      - filesystemName
      - diskSize
      - diskUsed
      - diskAvail
      - diskUsedPercent
    calculates:
      - filesystem_name=filesystemName
      - disk_size=diskSize
      - disk_used=diskUsed
      - disk_avail=diskAvail
      - disk_used_percent=diskUsedPercent
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      authPassphrase: ^_^authPassphrase^_^
      privPassphrase: ^_^privPassphrase^_^
      privPasswordEncryption: ^_^privPasswordEncryption^_^
      authPasswordEncryption: ^_^authPasswordEncryption^_^
      contextName: ^_^contextName^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        # 信锐企业OID: *******.4.1.35047 (根据MIB文件确认)
        # 磁盘信息表: *******.4.1.35047.1.5.1
        filesystemName: *******.4.1.35047.*******        # 文件系统名称
        diskSize: *******.4.1.35047.*******              # 磁盘大小
        diskUsed: *******.4.1.35047.*******              # 已使用
        diskAvail: *******.4.1.35047.*******             # 可用空间
        diskUsedPercent: *******.4.1.35047.*******       # 使用率

