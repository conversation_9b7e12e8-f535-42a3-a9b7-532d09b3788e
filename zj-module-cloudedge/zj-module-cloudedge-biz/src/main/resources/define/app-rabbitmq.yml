# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring mid-middleware custom-custom monitoring os-operating system monitoring
# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: mid
# The monitoring type eg: linux windows tomcat mysql aws...
# 监控类型 eg: linux windows tomcat mysql aws...
app: rabbitmq
# The monitoring i18n name
# 监控类型国际化名称
name:
  zh-CN: RabbitMQ消息系统
  en-US: RabbitMQ Message
# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # field-param field key
  - field: port
    # name-param field display i18n name
    name:
      zh-CN: HTTP 端口
      en-US: HTTP Port
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    # required-true or false
    required: true
    defaultValue: 15672
  # field-param field key
  - field: ssl
    # name-param field display i18n name
    name:
      zh-CN: 启动SSL
      en-US: SSL
    # When the type is boolean, the front end displays the switch with a switch
    type: boolean
    # required-true or false
    required: false
  # field-param field key
  - field: authType
    # name-param field display i18n name
    name:
      zh-CN: 认证方式
      en-US: Auth Type
    # When the type is radio radio radio and checkbox, option represents a list of optional values {name1: value1, name2: value2}
    type: radio
    # required-true or false
    required: true
    #When the type is radio radio radio and checkbox, option represents a list of optional values {name1: value1, name2: value2}
    options:
      - label: Basic Auth
        value: Basic Auth
    defaultValue: Basic Auth
  # field-param field key
  - field: username
    name:
      zh-CN: 用户名
      en-US: Username
    # type-param field type(most mapping the html input type)
    type: text
    # When the type is text, use limit to indicate the string limit size
    limit: 50
    # required-true or false
    required: true
  # field-param field key
  - field: password
    # name-param field display i18n name
    name:
      zh-CN: 密码
      en-US: Password
    # type-param field type(most mapping the html input tag)
    type: password
    # required-true or false
    required: true

metrics:
  # Note: The built-in monitoring metrics include (responseTime - response time)
  - name: overview
    i18n:
      zh-CN: 概览
      en-US: Overview
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    # collect metrics content
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      - field: product_version
        type: 1
        i18n:
          zh-CN: 产品版本
          en-US: Product Version
      - field: product_name
        type: 1
        i18n:
          zh-CN: 产品名称
          en-US: Product Name
      - field: rabbitmq_version
        type: 1
        i18n:
          zh-CN: RabbitMQ 版本
          en-US: RabbitMQ Version
      - field: management_version
        type: 1
        i18n:
          zh-CN: RabbitMQ Management 版本
          en-US: RabbitMQ Management Version
      - field: erlang_version
        type: 1
        i18n:
          zh-CN: Erlang 版本
          en-US: Erlang Version
      - field: cluster_name
        type: 1
        i18n:
          zh-CN: 集群名称
          en-US: Cluster Name
      - field: rates_mode
        type: 1
        i18n:
          zh-CN: 速率模式
          en-US: Rates Mode
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: http
    # 当protocol为http协议时具体的采集配置
    # the config content when protocol is http
    http:
      # Host: ipv4 ipv6 domain
      host: ^_^host^_^
      # port
      port: ^_^port^_^
      # url Indicates the path of the request interface
      url: /api/overview
      # Request mode GET POST PUT DELETE PATCH
      method: GET
      # Whether to enable SSL/TLS, i.e. whether it is HTTP or HTTPS, default is false.
      ssl: ^_^ssl^_^
      # authentication
      authorization:
        # Authentication mode: Basic Auth, Digest Auth, Bearer Token
        type: ^_^authType^_^
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
      # http response data parse type: default-hertzbeat rule, jsonpath-jsonpath script, website-for website monitoring, prometheus-prometheus exporter rule
      parseType: default

  - name: object_totals
    i18n:
      zh-CN: 对象总数
      en-US: Object Totals
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 1
    # collect metrics content
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      - field: channels
        type: 0
        i18n:
          zh-CN: 通道数
          en-US: Channels
      - field: connections
        type: 0
        i18n:
          zh-CN: 连接数
          en-US: Connections
      - field: consumers
        type: 0
        i18n:
          zh-CN: 消费者数
          en-US: Consumers
      - field: exchanges
        type: 0
        i18n:
          zh-CN: 交换器数
          en-US: Exchanges
      - field: queues
        type: 0
        i18n:
          zh-CN: 队列数
          en-US: Queues
    protocol: http
    http:
      # Host: ipv4 ipv6 domain
      host: ^_^host^_^
      # port
      port: ^_^port^_^
      # url Indicates the path of the request interface
      url: /api/overview
      # Request mode GET POST PUT DELETE PATCH
      method: GET
      # Whether to enable SSL/TLS, i.e. whether it is HTTP or HTTPS, default is false.
      ssl: ^_^ssl^_^
      # Authentication
      authorization:
        # Authentication mode: Basic Auth, Digest Auth, Bearer Token
        type: ^_^authType^_^
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
      # http response data parse type: default-hertzbeat rule, jsonpath-jsonpath script, website-for website monitoring, prometheus-prometheus exporter rule
      parseType: jsonPath
      parseScript: '$.object_totals'

  - name: nodes
    i18n:
      zh-CN: 节点数
      en-US: Nodes
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 2
    # collect metrics content
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      - field: name
        type: 1
        label: true
        i18n:
          zh-CN: 名称
          en-US: Name
      - field: type
        type: 1
        i18n:
          zh-CN: 类型
          en-US: Type
      - field: running
        type: 1
        i18n:
          zh-CN: 运行状态
          en-US: Running
      - field: os_pid
        type: 1
        i18n:
          zh-CN: 操作系统进程ID
          en-US: OS PID
      - field: mem_limit
        type: 0
        unit: MB
        i18n:
          zh-CN: 内存限制
          en-US: Memory Limit
      - field: mem_used
        type: 0
        unit: MB
        i18n:
          zh-CN: 已使用内存
          en-US: Memory Used
      - field: fd_total
        type: 0
        i18n:
          zh-CN: 文件描述符总数
          en-US: Total File Descriptors
      - field: fd_used
        type: 0
        i18n:
          zh-CN: 已使用文件描述符
          en-US: Used File Descriptors
      - field: sockets_total
        type: 0
        i18n:
          zh-CN: 总套接字数
          en-US: Total Sockets
      - field: sockets_used
        type: 0
        i18n:
          zh-CN: 已使用套接字数
          en-US: Used Sockets
      - field: proc_total
        type: 0
        i18n:
          zh-CN: 进程总数
          en-US: Total Processes
      - field: proc_used
        type: 0
        i18n:
          zh-CN: 已使用进程数
          en-US: Used Processes
      - field: disk_free_limit
        type: 0
        unit: GB
        i18n:
          zh-CN: 磁盘可用空间限制
          en-US: Disk Free Limit
      - field: disk_free
        type: 0
        unit: GB
        i18n:
          zh-CN: 可用磁盘空间
          en-US: Disk Free
      - field: gc_num
        type: 0
        i18n:
          zh-CN: 垃圾回收次数
          en-US: GC Count
      - field: gc_bytes_reclaimed
        type: 0
        unit: MB
        i18n:
          zh-CN: 回收的垃圾字节数
          en-US: GC Bytes Reclaimed
      - field: context_switches
        type: 0
        i18n:
          zh-CN: 上下文切换次数
          en-US: Context Switches
      - field: io_read_count
        type: 0
        i18n:
          zh-CN: 读取次数
          en-US: IO Read Count
      - field: io_read_bytes
        type: 0
        unit: KB
        i18n:
          zh-CN: 读取字节数
          en-US: IO Read Bytes
      - field: io_read_avg_time
        type: 0
        unit: ms
        i18n:
          zh-CN: 平均读取时间
          en-US: IO Read Average Time
      - field: io_write_count
        type: 0
        i18n:
          zh-CN: 写入次数
          en-US: IO Write Count
      - field: io_write_bytes
        type: 0
        unit: KB
        i18n:
          zh-CN: 写入字节数
          en-US: IO Write Bytes
      - field: io_write_avg_time
        type: 0
        unit: ms
        i18n:
          zh-CN: 平均写入时间
          en-US: IO Write Average Time
      - field: io_seek_count
        type: 0
        i18n:
          zh-CN: 寻址次数
          en-US: IO Seek Count
      - field: io_seek_avg_time
        type: 0
        unit: ms
        i18n:
          zh-CN: 平均寻址时间
          en-US: IO Seek Average Time
      - field: io_sync_count
        type: 0
        i18n:
          zh-CN: 同步次数
          en-US: IO Sync Count
      - field: io_sync_avg_time
        type: 0
        unit: ms
        i18n:
          zh-CN: 平均同步时间
          en-US: IO Sync Average Time
      - field: connection_created
        type: 0
        i18n:
          zh-CN: 创建的连接数
          en-US: Connections Created
      - field: connection_closed
        type: 0
        i18n:
          zh-CN: 关闭的连接数
          en-US: Connections Closed
      - field: channel_created
        type: 0
        i18n:
          zh-CN: 创建的通道数
          en-US: Channels Created
      - field: channel_closed
        type: 0
        i18n:
          zh-CN: 关闭的通道数
          en-US: Channels Closed
      - field: queue_declared
        type: 0
        i18n:
          zh-CN: 声明的队列数
          en-US: Queues Declared
      - field: queue_created
        type: 0
        i18n:
          zh-CN: 创建的队列数
          en-US: Queues Created
      - field: queue_deleted
        type: 0
        i18n:
          zh-CN: 删除的队列数
          en-US: Queues Deleted
    units:
      - mem_limit=B->MB
      - mem_used=B->MB
      - disk_free_limit=B->GB
      - disk_free=B->GB
      - gc_bytes_reclaimed=B->MB
      - io_read_bytes=B->KB
      - io_write_bytes=B->KB
    protocol: http
    http:
      # Host: ipv4 ipv6 domain
      host: ^_^host^_^
      # port
      port: ^_^port^_^
      # url Indicates the path of the request interface
      url: /api/nodes
      # request mode: GET POST PUT DELETE PATCH
      method: GET
      # Whether to enable SSL/TLS, i.e. whether it is HTTP or HTTPS, default is false.
      ssl: ^_^ssl^_^
      # Authentication
      authorization:
        # Authentication mode: Basic Auth, Digest Auth, Bearer Token
        type: ^_^authType^_^
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
      # response data parse type: default-hertzbeat rule, jsonpath-jsonpath script, website-for website monitoring, prometheus-prometheus exporter rule
      parseType: jsonPath
      parseScript: '$.*'

  - name: queues
    i18n:
      zh-CN: 队列数
      en-US: Queues
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 3
    # collect metrics content
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      - field: name
        type: 1
        label: true
        i18n:
          zh-CN: 名称
          en-US: Name
      - field: node
        type: 1
        i18n:
          zh-CN: 节点
          en-US: Node
      - field: state
        type: 1
        i18n:
          zh-CN: 状态
          en-US: State
      - field: type
        type: 1
        i18n:
          zh-CN: 类型
          en-US: Type
      - field: vhost
        type: 1
        i18n:
          zh-CN: 虚拟主机
          en-US: Vhost
      - field: auto_delete
        type: 1
        i18n:
          zh-CN: 自动删除
          en-US: Auto Delete
      - field: policy
        type: 1
        i18n:
          zh-CN: 策略
          en-US: Policy
      - field: consumers
        type: 0
        i18n:
          zh-CN: 消费者数
          en-US: Consumers
      - field: memory
        type: 0
        unit: B
        i18n:
          zh-CN: 内存占用
          en-US: Memory
      - field: messages_ready
        type: 0
        i18n:
          zh-CN: 就绪消息数
          en-US: Messages Ready
      - field: messages_unacknowledged
        type: 0
        i18n:
          zh-CN: 未确认消息数
          en-US: Messages Unacknowledged
      - field: messages
        type: 0
        i18n:
          zh-CN: 消息总数
          en-US: Messages
      - field: messages_ready_ram
        type: 0
        i18n:
          zh-CN: 就绪消息占用内存
          en-US: Messages Ready RAM
      - field: messages_persistent
        type: 0
        i18n:
          zh-CN: 持久化消息数
          en-US: Persistent Messages
      - field: message_bytes
        type: 0
        unit: B
        i18n:
          zh-CN: 消息字节数
          en-US: Message Bytes
      - field: message_bytes_ready
        type: 0
        unit: B
        i18n:
          zh-CN: 就绪消息字节数
          en-US: Message Bytes Ready
      - field: message_bytes_unacknowledged
        type: 0
        unit: B
        i18n:
          zh-CN: 未确认消息字节数
          en-US: Message Bytes Unacknowledged
      - field: message_bytes_ram
        type: 0
        unit: B
        i18n:
          zh-CN: 消息占用内存字节数
          en-US: Message Bytes RAM
      - field: message_bytes_persistent
        type: 0
        unit: B
        i18n:
          zh-CN: 持久化消息字节数
          en-US: Persistent Message Bytes
    protocol: http
    http:
      # Host: ipv4 ipv6 domain
      host: ^_^host^_^
      # port
      port: ^_^port^_^
      # url Indicates the path of the request interface
      url: /api/queues
      # request mode GET POST PUT DELETE PATCH
      method: GET
      # Whether to enable SSL/TLS, i.e. whether it is HTTP or HTTPS, default is false.
      ssl: ^_^ssl^_^
      # 认证
      # Authentication
      authorization:
        # Authentication mode: Basic Auth, Digest Auth, Bearer Token
        type: ^_^authType^_^
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
      # response data parse type: default-hertzbeat rule, jsonpath-jsonpath script, website-for website monitoring, prometheus-prometheus exporter rule
      parseType: jsonPath
      parseScript: '$.*'

