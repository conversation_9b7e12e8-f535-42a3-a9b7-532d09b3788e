# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: service
# 监控应用类型名称(与文件名保持一致) eg: linux windows tomcat mysql aws...
app: push
# The app api i18n name
# app api国际化名称
name:
  zh-CN: 推送方式监控
  en-US: Push Style Monitor
# Input params define for app api(render web ui by the definition)
params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: 推送模块Host
      en-US: Push Module Host
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
    # field-param field key
    defaultValue: 127.0.0.1
  - field: port
    # name-param field display i18n name
    name:
      zh-CN: 端口
      en-US: Port
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    # required-true or false
    required: true
    # default value
    defaultValue: 1157
  - field: fields
    # name-param field display i18n name
    name:
      zh-CN: 监控数据字段
      en-US: Metrics fields
    # type-param field type(most mapping the html input type)
    type: metrics-field
    # required-true or false
    required: true

# collect metrics config list
metrics:
  # metrics - all
  - name: metrics
    i18n:
      zh-CN: 指标
      en-US: Metrics
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: push
    # the config content when protocol is http
    push:
      # http host: ipv4 ipv6 domain
      host: ^_^host^_^
      # http port
      port: ^_^port^_^
      # http uri
      uri: /api/push