# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring mid-middleware custom-custom monitoring os-operating system monitoring
# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: service
# The monitoring type eg: linux windows tomcat mysql aws...
# 监控类型 eg: linux windows tomcat mysql aws...
app: dns
# 监控类型国际化名称
name:
  zh-CN: DNS服务监控
  en-US: DNS SERVER MONITORS
# The description and help of this monitoring type
# 监控类型的帮助描述信息

params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: DNS服务器IP
      en-US: DNS Server IP
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # field-param field key
  - field: port
    # name-param field display i18n name
    name:
      zh-CN: 端口
      en-US: Port
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    # required-true or false
    required: true
    # default value 53
    defaultValue: 53
  - field: address
    # name-param field display i18n name
    name:
      zh-CN: 域名解析的地址
      en-US: Address For DNS
    # type-param field type(most mapping the html input type)
    type: text
    # required-true or false
    required: true
  # field-param field key
  - field: timeout
    # name-param field display i18n name
    name:
      zh-CN: 连接超时时间(ms)
      en-US: Connect Timeout(ms)
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,100000]'
    # required-true or false
    required: true
    # default value 6000
    defaultValue: 6000
  - field: tcp
    # name-param field display i18n name
    name:
      zh-CN: 是否使用tcp协议
      en-US: Use TCP Protocol
    # type-param field type(boolean mapping the html switch tag)
    type: boolean
    # required-true or false
    required: true
    # default value false
    # 默认值 false
    defaultValue: false
  - field: queryClass
    # name-param field display i18n name
    name:
      zh-CN: 查询类别
      en-US: Query Class
    type: radio
    required: true
    options:
      - label: IN
        value: IN
      - label: CHAOS
        value: CHAOS
      - label: HESIOD
        value: HESIOD
      - label: NONE
        value: NONE
      - label: ANY
        value: ANY
    # default value IN
    defaultValue: IN
    hide: true
# collect metrics config list
metrics:
  # metrics - header
  - name: header
    i18n:
      zh-CN: Header
      en-US: Header
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
    fields:
      - field: responseTime
        type: 0
        unit: ms
        i18n:
          zh-CN: 响应时间
          en-US: Response Time
      - field: opcode
        type: 1
        i18n:
          zh-CN: 操作码
          en-US: Opcode
      - field: status
        type: 1
        i18n:
          zh-CN: 响应状态
          en-US: Response Status
      - field: flags
        type: 1
        i18n:
          zh-CN: 响应标志
          en-US: Response Flags
      - field: questionRowCount
        type: 0
        i18n:
          zh-CN: 请求记录数
          en-US: Question Record Count
      - field: answerRowCount
        type: 0
        i18n:
          zh-CN: 响应记录数
          en-US: Answer Record Count
      - field: authorityRowCount
        type: 0
        i18n:
          zh-CN: 授权记录数
          en-US: Authority Record Count
      - field: additionalRowCount
        type: 0
        i18n:
          zh-CN: 附加记录数
          en-US: Additional Record Count
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk, dns
    protocol: dns
    # Specific collection configuration when protocol is telnet protocol
    dns:
      # DNS Server IP
      dnsServerIP: ^_^host^_^
      # port
      port: ^_^port^_^
      # address that used to run dig command
      address: ^_^address^_^
      # timeout
      timeout: ^_^timeout^_^
      # Use TCP Protocol
      tcp: ^_^tcp^_^
      # Query Class
      queryClass: ^_^queryClass^_^

  # metrics - question
  - name: question
    i18n:
      zh-CN: Question
      en-US: Question
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 1
    # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
    fields:
      - field: section
        type: 1
        i18n:
          zh-CN: Section
          en-US: Section
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk, dns
    protocol: dns
    # Specific collection configuration when protocol is telnet protocol
    dns:
      # DNS Server IP
      dnsServerIP: ^_^host^_^
      # port
      port: ^_^port^_^
      # address that used to run dig command
      address: ^_^address^_^
      # timeout
      timeout: ^_^timeout^_^
      # Use TCP Protocol
      tcp: ^_^tcp^_^
      # Query Class
      queryClass: ^_^queryClass^_^

  # metrics - answer
  - name: answer
    i18n:
      zh-CN: Answer
      en-US: Answer
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 2
    # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
    fields:
      - field: section0
        type: 1
        i18n:
          zh-CN: Section0
          en-US: Section0
      - field: section1
        type: 1
        i18n:
          zh-CN: Section1
          en-US: Section1
      - field: section2
        type: 1
        i18n:
          zh-CN: Section2
          en-US: Section2
      - field: section3
        type: 1
        i18n:
          zh-CN: Section3
          en-US: Section3
      - field: section4
        type: 1
        i18n:
          zh-CN: Section4
          en-US: Section4
      - field: section5
        type: 1
        i18n:
          zh-CN: Section5
          en-US: Section5
      - field: section6
        type: 1
        i18n:
          zh-CN: Section6
          en-US: Section6
      - field: section7
        type: 1
        i18n:
          zh-CN: Section7
          en-US: Section7
      - field: section8
        type: 1
        i18n:
          zh-CN: Section8
          en-US: Section8
      - field: section9
        type: 1
        i18n:
          zh-CN: Section9
          en-US: Section9
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk, dns
    protocol: dns
    # Specific collection configuration when protocol is telnet protocol
    dns:
      # DNS Server IP
      dnsServerIP: ^_^host^_^
      # port
      port: ^_^port^_^
      # address that used to run dig command
      address: ^_^address^_^
      # timeout
      timeout: ^_^timeout^_^
      # Use TCP Protocol
      tcp: ^_^tcp^_^
      # Query Class
      queryClass: ^_^queryClass^_^

  # metrics - authority
  - name: authority
    i18n:
      zh-CN: Authority
      en-US: Authority
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 3
    # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
    fields:
      - field: section0
        type: 1
        i18n:
          zh-CN: Section0
          en-US: Section0
      - field: section1
        type: 1
        i18n:
          zh-CN: Section1
          en-US: Section1
      - field: section2
        type: 1
        i18n:
          zh-CN: Section2
          en-US: Section2
      - field: section3
        type: 1
        i18n:
          zh-CN: Section3
          en-US: Section3
      - field: section4
        type: 1
        i18n:
          zh-CN: Section4
          en-US: Section4
      - field: section5
        type: 1
        i18n:
          zh-CN: Section5
          en-US: Section5
      - field: section6
        type: 1
        i18n:
          zh-CN: Section6
          en-US: Section6
      - field: section7
        type: 1
        i18n:
          zh-CN: Section7
          en-US: Section7
      - field: section8
        type: 1
        i18n:
          zh-CN: Section8
          en-US: Section8
      - field: section9
        type: 1
        i18n:
          zh-CN: Section9
          en-US: Section9
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk, dns
    protocol: dns
    # Specific collection configuration when protocol is telnet protocol
    dns:
      # DNS Server IP
      dnsServerIP: ^_^host^_^
      # port
      port: ^_^port^_^
      # address that used to run dig command
      address: ^_^address^_^
      # timeout
      timeout: ^_^timeout^_^
      # Use TCP Protocol
      tcp: ^_^tcp^_^
      # Query Class
      queryClass: ^_^queryClass^_^

  # metrics - additional
  - name: additional
    i18n:
      zh-CN: Additional
      en-US: Additional
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 4
    # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
    fields:
      - field: section0
        type: 1
        i18n:
          zh-CN: Section0
          en-US: Section0
      - field: section1
        type: 1
        i18n:
          zh-CN: Section1
          en-US: Section1
      - field: section2
        type: 1
        i18n:
          zh-CN: Section2
          en-US: Section2
      - field: section3
        type: 1
        i18n:
          zh-CN: Section3
          en-US: Section3
      - field: section4
        type: 1
        i18n:
          zh-CN: Section4
          en-US: Section4
      - field: section5
        type: 1
        i18n:
          zh-CN: Section5
          en-US: Section5
      - field: section6
        type: 1
        i18n:
          zh-CN: Section6
          en-US: Section6
      - field: section7
        type: 1
        i18n:
          zh-CN: Section7
          en-US: Section7
      - field: section8
        type: 1
        i18n:
          zh-CN: Section8
          en-US: Section8
      - field: section9
        type: 1
        i18n:
          zh-CN: Section9
          en-US: Section9
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk, dns
    protocol: dns
    # Specific collection configuration when protocol is telnet protocol
    dns:
      # DNS Server IP
      dnsServerIP: ^_^host^_^
      # port
      port: ^_^port^_^
      # address that used to run dig command
      address: ^_^address^_^
      # timeout
      timeout: ^_^timeout^_^
      # Use TCP Protocol
      tcp: ^_^tcp^_^
      # Query Class
      queryClass: ^_^queryClass^_^
