# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring mid-middleware custom-custom monitoring os-operating system monitoring
# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: service
# The monitoring type eg: linux windows tomcat mysql aws...
# 监控类型 eg: linux windows tomcat mysql aws...
app: websocket
# 监控类型国际化名称
name:
  zh-CN: WebSocket监控
  en-US: WebSocket
# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: WebSocket服务的Host
      en-US: Host of WebSocket service
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # field-param field key
  - field: port
    # name-param field display i18n name
    name:
      zh-CN: 端口
      en-US: Port
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    # required-true or false
    required: true
  # field-param field key
  # field-字段名称标识符
  - field: path
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: WebSocket服务的路径
      en-US: Path of WebSocket service
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: text
    # required-true or false
    # 是否是必输项 true-必填 false-可选
    required: true
    defaultValue: /
# collect metrics config list
metrics:
  # metrics - summary
  - name: summary
    i18n:
      zh-CN: 概要
      en-US: Summary
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
    fields:
      - field: responseTime
        type: 0
        unit: ms
        i18n:
          zh-CN: 响应时间
          en-US: Response Time
      - field: httpVersion
        type: 1
        i18n:
          zh-CN: Http 版本
          en-US: Http Version
      - field: responseCode
        type: 1
        i18n:
          zh-CN: 响应状态码
          en-US: Response Code
      - field: statusMessage
        type: 1
        i18n:
          zh-CN: 状态消息
          en-US: Status Message
      - field: connection
        type: 1
        i18n:
          # Whether to support the upgrade (If no, the http request is normal; if yes, the upgrade is supported)
          zh-CN: 是否升级
          en-US: Connection
      - field: upgrade
        type: 1
        i18n:
          zh-CN: 升级(协议)
          en-US: Upgrade

    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: websocket
    # Specific collection configuration when protocol is telnet protocol
    websocket:
      # telnet host
      host: ^_^host^_^
      port: ^_^port^_^
      path: ^_^path^_^
