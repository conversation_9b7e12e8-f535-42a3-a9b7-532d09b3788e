# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: mid
# 监控应用类型名称(与文件名保持一致) eg: linux windows tomcat mysql aws...
app: emqx
# The app api i18n name
# app api国际化名称
name:
  zh-CN: EMQX MQTT
  en-US: EMQX MQTT
# Input params define for monitoring(render web ui by the definition)
# 监控所需输入参数定义(根据定义渲染页面UI)
params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # field-param field key
  - field: port
    # name-param field display i18n name
    name:
      zh-CN: 端口
      en-US: Port
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    # required-true or false
    required: true
    # default value
    defaultValue: 18083
  - field: ssl
    name:
      zh-CN: 启用HTTPS
      en-US: HTTPS
    type: boolean
    defaultValue: false
  - field: timeout
    name:
      zh-CN: 超时时间(ms)
      en-US: Timeout(ms)
    type: number
    required: false
    hide: true
  - field: authType
    name:
      zh-CN: 认证方式
      en-US: Auth Type
    type: radio
    required: true
    options:
      - label: Basic Auth
        value: Basic Auth
    defaultValue: Basic Auth
  - field: apikey
    name:
      zh-CN: API Key
      en-US: API Key
    type: text
    required: true
  - field: secretkey
    name:
      zh-CN: Secret Key
      en-US: Secret Key
    type: text
    required: true
metrics:
  # metrics - summary
  - name: summary
    # metrics name i18n label
    i18n:
      zh-CN: 概要
      en-US: Summary
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    # collect metrics content
    fields:
      # metrics content contains field-metric name, type-metric type:0-number,1-string, instance-if is metrics, unit-metric unit('%','ms','MB')
      - field: version
        type: 1
        i18n:
          zh-CN: 系统版本
          en-US: Version
      - field: node_name
        type: 1
        i18n:
          zh-CN: 节点名称
          en-US: Node Name
      - field: broker_status
        type: 1
        i18n:
          zh-CN: Broker 状态
          en-US: Broker Status
      - field: app_status
        type: 1
        i18n:
          zh-CN: 应用状态
          en-US: App Status
    # (optional)metrics field alias name, it is used as an alias field to map and convert the collected data and metrics field
    aliasFields:
      - rel_vsn
      - node_name
      - broker_status
      - app_status
    # mapping and conversion expressions, use these and aliasField above to calculate metrics value
    # eg: cores=core1+core2, usage=usage, waitTime=allTime-runningTime
    calculates:
      - version=rel_vsn
      - node_name=node_name
      - broker_status=broker_status
      - app_status=app_status
    #  the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk, we use HTTP protocol here
    protocol: http
    # the config content when protocol is http
    http:
      # http host: ipv4 ipv6 domain
      host: ^_^host^_^
      # http port
      port: ^_^port^_^
      # http url, we don't need to enter a parameter here, just set the fixed value to /api/summary
      url: /api/v5/status?format=json
      timeout: ^_^timeout^_^
      # http method: GET POST PUT DELETE PATCH, default fixed value is GET
      method: GET
      # if enabled https, default value is false
      ssl: ^_^ssl^_^
      # http auth
      authorization:
        # http auth type: Basic Auth, Digest Auth, Bearer Token
        type: ^_^authType^_^
        basicAuthUsername: ^_^apikey^_^
        basicAuthPassword: ^_^secretkey^_^
      # http response data parse type: default-hertzbeat rule, jsonpath-jsonpath script, website-for website monitoring, we use jsonpath to parse response data here
      parseType: jsonPath
      parseScript: '$'

  - name: metrics
    i18n:
      zh-CN: 指标
      en-US: Metrics
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 1
    # collect metrics content
    fields:
      # metrics content contains field-metric name, type-metric type:0-number,1-string, instance-if is metrics, unit-metric unit('%','ms','MB')
      - field: client_connected
        type: 0
        i18n:
          zh-CN: 客户端连接
          en-US: Client Connected
      - field: client_disconnected
        type: 0
        i18n:
          zh-CN: 客户端断开
          en-US: Client Disconnected
      - field: packets_sent
        type: 0
        i18n:
          zh-CN: 发送数据包
          en-US: Packets Sent
      - field: packets_received
        type: 0
        i18n:
          zh-CN: 接收数据包
          en-US: Packets Received
      - field: bytes_sent
        type: 0
        i18n:
          zh-CN: 发送字节
          en-US: Bytes Sent
      - field: bytes_received
        type: 0
        i18n:
          zh-CN: 接收字节
          en-US: Bytes Received
      - field: messages_sent
        type: 0
        i18n:
          zh-CN: 发送消息
          en-US: Messages Sent
      - field: messages_acked
        type: 0
        i18n:
          zh-CN: 消息确认
          en-US: Messages Acked
      - field: messages_delayed
        type: 0
        i18n:
          zh-CN: 延迟消息
          en-US: Messages Delayed
      - field: authorization_deny
        type: 0
        i18n:
          zh-CN: 授权拒绝
          en-US: Authorization Deny
      - field: client_authorize
        type: 0
        i18n:
          zh-CN: 客户端授权
          en-US: Client Authorize
      - field: session_created
        type: 0
        i18n:
          zh-CN: 会话创建
          en-US: Session Created
      - field: session_discarded
        type: 0
        i18n:
          zh-CN: 会话丢弃
          en-US: Session Discarded
    # (optional)metrics field alias name, it is used as an alias field to map and convert the collected data and metrics field
    aliasFields:
      - client.connected
      - client.disconnected
      - packets.sent
      - packets.received
      - bytes.sent
      - bytes.received
      - messages.sent
      - messages.acked
      - messages.delayed
      - authorization.deny
      - client.authorize
      - session.created
      - session.discarded
    # mapping and conversion expressions, use these and aliasField above to calculate metrics value
    # eg: cores=core1+core2, usage=usage, waitTime=allTime-runningTime
    calculates:
      - client_connected=client.connected
      - client_disconnected=client.disconnected
      - packets_sent=packets.sent
      - packets_received=packets.received
      - bytes_sent=bytes.sent
      - bytes_received=bytes.received
      - messages_sent=messages.sent
      - messages_acked=messages.acked
      - messages_delayed=messages.delayed
      - authorization_deny=authorization.deny
      - client_authorize=client.authorize
      - session_created=session.created
      - session_discarded=session.discarded
    #  the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk, we use HTTP protocol here
    protocol: http
    # the config content when protocol is http
    http:
      # http host: ipv4 ipv6 domain
      host: ^_^host^_^
      # http port
      port: ^_^port^_^
      # http url, we don't need to enter a parameter here, just set the fixed value to /api/summary
      url: /api/v5/metrics?aggregate=true
      timeout: ^_^timeout^_^
      # http method: GET POST PUT DELETE PATCH, default fixed value is GET
      method: GET
      # if enabled https, default value is false
      ssl: ^_^ssl^_^
      # http auth
      authorization:
        # http auth type: Basic Auth, Digest Auth, Bearer Token
        type: ^_^authType^_^
        basicAuthUsername: ^_^apikey^_^
        basicAuthPassword: ^_^secretkey^_^
      # http response data parse type: default-hertzbeat rule, jsonpath-jsonpath script, website-for website monitoring, we use jsonpath to parse response data here
      parseType: jsonPath
      parseScript: '$'

  - name: stats
    i18n:
      zh-CN: 统计
      en-US: Stats
    priority: 2
    fields:
      # metrics content contains field-metric name, type-metric type:0-number,1-string, instance-if is metrics, unit-metric unit('%','ms','MB')
      - field: channels_count
        type: 0
        i18n:
          zh-CN: 通道数
          en-US: Channels Count
      - field: channels_max
        type: 0
        i18n:
          zh-CN: 通道最大数
          en-US: Channels Max
      - field: connections_count
        type: 0
        i18n:
          zh-CN: 连接数
          en-US: Connections Count
      - field: connections_max
        type: 0
        i18n:
          zh-CN: 最大连接数
          en-US: Connections Max
      - field: delayed_count
        type: 0
        i18n:
          zh-CN: 延迟数
          en-US: Delayed Count
      - field: delayed_max
        type: 0
        i18n:
          zh-CN: 最大延迟数
          en-US: Delayed Max
      - field: live_connections_count
        type: 0
        i18n:
          zh-CN: 活动连接数
          en-US: Live Connections Count
      - field: live_connections_max
        type: 0
        i18n:
          zh-CN: 最大活动连接数
          en-US: Live Connections Max
      - field: retained_count
        type: 0
        i18n:
          zh-CN: 保留数
          en-US: Retained Count
      - field: retained_max
        type: 0
        i18n:
          zh-CN: 最大保留数
          en-US: Retained Max
      - field: sessions_count
        type: 0
        i18n:
          zh-CN: 会话数
          en-US: Sessions Count
      - field: sessions_max
        type: 0
        i18n:
          zh-CN: 最大会话数
          en-US: Sessions Max
      - field: suboptions_count
        type: 0
        i18n:
          zh-CN: 订阅选项数
          en-US: Suboptions Count
      - field: suboptions_max
        type: 0
        i18n:
          zh-CN: 最大订阅选项数
          en-US: Suboptions Max
      - field: subscribers_count
        type: 0
        i18n:
          zh-CN: 订阅者数
          en-US: Subscribers Count
      - field: subscribers_max
        type: 0
        i18n:
          zh-CN: 最大订阅者数
          en-US: Subscribers Max
      - field: subscriptions_count
        type: 0
        i18n:
          zh-CN: 订阅数
          en-US: Subscriptions Count
      - field: subscriptions_max
        type: 0
        i18n:
          zh-CN: 最大订阅数
          en-US: Subscriptions Max
      - field: subscriptions_shared_count
        type: 0
        i18n:
          zh-CN: 共享订阅数
          en-US: Subscriptions Shared Count
      - field: subscriptions_shared_max
        type: 0
        i18n:
          zh-CN: 最大共享订阅数
          en-US: Subscriptions Shared Max
      - field: topics_count
        type: 0
        i18n:
          zh-CN: 主题数
          en-US: Topics Count
      - field: topics_max
        type: 0
        i18n:
          zh-CN: 最大主题数
          en-US: Topics Max
    # (optional)metrics field alias name, it is used as an alias field to map and convert the collected data and metrics field
    aliasFields:
      - channels.count
      - channels.max
      - connections.count
      - connections.max
      - delayed.count
      - delayed.max
      - live_connections.count
      - live_connections.max
      - retained.count
      - retained.max
      - sessions.count
      - sessions.max
      - suboptions.count
      - suboptions.max
      - subscribers.count
      - subscribers.max
      - subscriptions.count
      - subscriptions.max
      - subscriptions.shared.count
      - subscriptions.shared.max
      - topics.count
      - topics.max
    # mapping and conversion expressions, use these and aliasField above to calculate metrics value
    # eg: cores=core1+core2, usage=usage, waitTime=allTime-runningTime
    calculates:
      - channels_count=channels.count
      - channels_max=channels.max
      - connections_count=connections.count
      - connections_max=connections.max
      - delayed_count=delayed.count
      - delayed_max=delayed.max
      - live_connections_count=live_connections.count
      - live_connections_max=live_connections.max
      - retained_count=retained.count
      - retained_max=retained.max
      - sessions_count=sessions.count
      - sessions_max=sessions.max
      - suboptions_count=suboptions.count
      - suboptions_max=suboptions.max
      - subscribers_count=subscribers.count
      - subscribers_max=subscribers.max
      - subscriptions_count=subscriptions.count
      - subscriptions_max=subscriptions.max
      - subscriptions_shared_count=subscriptions.shared.count
      - subscriptions_shared_max=subscriptions.shared.max
      - topics_count=topics.count
      - topics_max=topics.max
    #  the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk, we use HTTP protocol here
    protocol: http
    # the config content when protocol is http
    http:
      # http host: ipv4 ipv6 domain
      host: ^_^host^_^
      # http port
      port: ^_^port^_^
      # http url, we don't need to enter a parameter here, just set the fixed value to /api/summary
      url: /api/v5/stats?aggregate=true
      timeout: ^_^timeout^_^
      # http method: GET POST PUT DELETE PATCH, default fixed value is GET
      method: GET
      # if enabled https, default value is false
      ssl: ^_^ssl^_^
      # http auth
      authorization:
        # http auth type: Basic Auth, Digest Auth, Bearer Token
        type: ^_^authType^_^
        basicAuthUsername: ^_^apikey^_^
        basicAuthPassword: ^_^secretkey^_^
      # http response data parse type: default-hertzbeat rule, jsonpath-jsonpath script, website-for website monitoring, we use jsonpath to parse response data here
      parseType: jsonPath
      parseScript: '$'
