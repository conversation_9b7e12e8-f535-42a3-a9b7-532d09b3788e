# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring custom-custom monitoring os-operating system monitoring
# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: bigdata
# The monitoring type eg: linux windows tomcat mysql aws...
# 监控类型 eg: linux windows tomcat mysql aws...
app: iotdb
# The monitoring i18n name
# 监控类型国际化名称
name:
  zh-CN: Apache IoTDB
  en-US: Apache IoTDB
# The description and help of this monitoring type
# 监控类型的帮助描述信息
help:
  zh-CN: HertzBeat 对 Apache IoTDB JVM相关的物联网时序数据库的运行状态，内存任务集群等相关指标(cluster node status、jvm memory committed bytes、jvm memory used bytes、jvm threads states threads、quantity、cache hit、queue、hrift connections) 进行监控。<br><span class='help_module_span'>⚠️注意：您需要在 IoTDB 开启 prometheus exporter metrics 接口，<a class='help_module_content' href='https://iotdb.apache.org/zh/UserGuide/V0.13.x/Maintenance-Tools/Metric-Tool.html'>点击查看开启步骤</a>。</span>
  en-US: HertzBeat monitoring the Apache IoTDB metrics such as operational status, memory, task, clusters, jvm etc. <br><span class='help_module_span'>⚠️Note:You should enable the prometheus metrics api in IoTDB. <a class='help_module_content' href='https://iotdb.apache.org/UserGuide/V0.13.x/Maintenance-Tools/Metric-Tool.html'>Click here to view the specific steps.</a></span>"
  zh-TW: HertzBeat 對 Apache IoTDB JVM相關的物聯網時序數據庫的運行狀態，內存任務集群等相關指標(cluster node status、jvm memory committed bytes、jvm memory used bytes、jvm threads states threads、quantity、cache hit、queue、hrift connections) 進行監控。<br><span class='help_module_span'>⚠️注意：您需要在 IoTDB 開啓 prometheus exporter metrics 接口，<a class='help_module_content' href='https://iotdb.apache.org/zh/UserGuide/V0.13.x/Maintenance-Tools/Metric-Tool.html'>點擊查看開啓步驟</a>。</span>
helpLink:
  zh-CN: https://hertzbeat.com/zh-cn/docs/help/iotdb/
  en-US: https://hertzbeat.com/docs/help/iotdb/
# 监控所需输入参数定义(根据定义渲染页面UI)
# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  # field-变量字段标识符
  - field: host
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: host
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: true
  - field: port
    name:
      zh-CN: 端口
      en-US: Port
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: number
    # when type is number, range is required
    # 当type为number时,用range表示范围
    range: '[0,65535]'
    # default value
    defaultValue: 9091
    required: true
  - field: timeout
    name:
      zh-CN: 查询超时时间
      en-US: Query Timeout
    type: number
    required: false
    # hide param-true or false
    # 是否隐藏字段 true or false
    hide: true
    defaultValue: 6000

# collect metrics config list
# 采集指标配置列表
metrics:
  # metrics - cluster_node_status
  # 监控指标 - cluster_node_status
  - name: cluster_node_status
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    # 指标采集调度优先级(0->127)->(优先级高->低) 优先级低的指标会等优先级高的指标采集完成后才会被调度, 相同优先级的指标会并行调度采集
    # 优先级为0的指标为可用性指标,即它会被首先调度,采集成功才会继续调度其它指标,采集失败则中断调度
    priority: 0
    # collect metrics content
    # 具体监控指标列表
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      # field-指标名称, type-指标类型(0-number数字,1-string字符串), unit-指标单位('%','ms','MB'), label-是否是指标标签字段
      - field: name
        type: 1
        label: true
      - field: status
        type: 0
    # (optional)metrics field alias name, it is used as an alias field to map and convert the collected data and metrics field
    # (可选)监控指标别名, 做为中间字段与采集数据字段和指标字段映射转换
    aliasFields:
      - name
      - value
    # mapping and conversion expressions, use these and aliasField above to calculate metrics value
    # (可选)指标映射转换计算表达式,与上面的别名一起作用,计算出最终需要的指标值
    # eg: cores=core1+core2, usage=usage, waitTime=allTime-runningTime
    calculates:
      - name=name
      - status=value
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: http
    # the config content when protocol is http
    http:
      # http host: ipv4 ipv6 domain
      host: ^_^host^_^
      # http port
      port: ^_^port^_^
      # http url
      url: '/metrics'
      timeout: ^_^timeout^_^
      # http method: GET POST PUT DELETE PATCH
      method: GET
      # http response data parse type: default-hertzbeat rule, jsonpath-jsonpath script, website-for website monitoring, prometheus-prometheus exporter rule
      # http 响应数据解析方式: default-系统规则, jsonPath-jsonPath脚本, website-网站可用性指标监控, prometheus-Prometheus数据规则
      parseType: prometheus

  - name: jvm_memory_committed_bytes
    priority: 1
    fields:
      - field: area
        type: 1
      - field: id
        type: 1
        label: true
      - field: value
        type: 0
        unit: MB
    units:
      - value=B->MB
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: '/metrics'
      timeout: ^_^timeout^_^
      method: GET
      parseType: prometheus

  - name: jvm_memory_used_bytes
    priority: 1
    fields:
      - field: area
        type: 1
      - field: id
        type: 1
        label: true
      - field: value
        type: 0
        unit: MB
    units:
      - value=B->MB
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: '/metrics'
      timeout: ^_^timeout^_^
      method: GET
      parseType: prometheus

  - name: jvm_threads_states_threads
    priority: 1
    fields:
      - field: state
        type: 1
        label: true
      - field: count
        type: 0
    aliasFields:
      - state
      - value
    calculates:
      - count=value
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: '/metrics'
      timeout: ^_^timeout^_^
      method: GET
      parseType: prometheus

  - name: quantity
    priority: 1
    fields:
      - field: id
        type: 1
        label: true
      - field: name
        type: 1
      - field: type
        type: 1
      - field: value
        type: 0
    aliasFields:
      - name
      - type
      - value
    calculates:
      - id=name+"-"+type
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: '/metrics'
      timeout: ^_^timeout^_^
      method: GET
      parseType: prometheus

  - name: cache_hit
    priority: 1
    fields:
      - field: name
        type: 1
        label: true
      - field: value
        type: 0
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: '/metrics'
      timeout: ^_^timeout^_^
      method: GET
      parseType: prometheus

  - name: queue
    priority: 1
    fields:
      - field: id
        type: 1
        label: true
      - field: name
        type: 1
      - field: status
        type: 1
      - field: value
        type: 0
    aliasFields:
      - name
      - status
      - value
    calculates:
      - id=name+"-"+status
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: '/metrics'
      timeout: ^_^timeout^_^
      method: GET
      parseType: prometheus

  - name: thrift_connections
    priority: 1
    fields:
      - field: name
        type: 1
        label: true
      - field: connection
        type: 0
    aliasFields:
      - name
      - value
    calculates:
      - connection=value
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: '/metrics'
      timeout: ^_^timeout^_^
      method: GET
      parseType: prometheus
