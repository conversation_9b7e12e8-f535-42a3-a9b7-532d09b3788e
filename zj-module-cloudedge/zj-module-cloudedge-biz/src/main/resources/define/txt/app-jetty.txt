# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring custom-custom monitoring os-operating system monitoring
# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: webserver
# The monitoring type eg: linux windows tomcat mysql aws...
# 监控类型 eg: linux windows tomcat mysql aws...
app: jetty
# The monitoring i18n name
# 监控类型国际化名称
name:
  zh-CN: Jetty应用服务器
  en-US: Jetty AppServer
# The description and help of this monitoring type
# 监控类型的帮助描述信息
help:
  zh-CN: 云星辰 使用 <a class='help_module_content' href='https://hertzbeat.com/zh-cn/docs/advanced/extend-jmx'>JMX 协议</a> 对 Jetty 应用服务器的通用性能指标（内存信息，类加载，线程状态，JVM等）进行采集监控。<br><span class='help_module_span'>注意⚠️：您需要在 Jetty 应用开启 JMX 服务, <a class='help_module_content' href='https://eclipse.dev/jetty/documentation/jetty-10/operations-guide/index.html#og-jmx-remote'>点击查看开启步骤</a>。</span>
  en-US: HertzBeat monitoring general performance metrics(memory pool, class loading, thread, jvm etc) of Jetty application server through <a class='help_module_content' href='https://hertzbeat.com/zh-cn/docs/advanced/extend-jmx'>JMX protocol</a>. <br><span class='help_module_span'>Note⚠️:You should enable the JMX service in Jetty application. <a class='help_module_content' href='https://eclipse.dev/jetty/documentation/jetty-10/operations-guide/index.html#og-jmx-remote'>Click here to view the specific steps.</a></span>
  zh-TW: 云星辰 使用 <a class='help_module_content' href='https://hertzbeat.com/zh-cn/docs/advanced/extend-jmx'>JMX 協議</a> 對 Jetty 應用服務器的通用性能指標（內存信息，類加載，線程狀態，JVM等）進行采集監控。<br><span class='help_module_span'>注意⚠️：您需要在 Jetty 應用開啓 JMX 服務, <a class='help_module_content' href='https://eclipse.dev/jetty/documentation/jetty-10/operations-guide/index.html#og-jmx-remote'>點擊查看開啓步驟</a>。</span>
helpLink:
  zh-CN: https://hertzbeat.com/zh-cn/docs/help/jetty/
  en-US: https://hertzbeat.com/docs/help/jetty/
# 监控所需输入参数定义(根据定义渲染页面UI)
# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  # field-字段名称标识符
  - field: host
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: host
    # required-true or false
    # 是否是必输项 true-必填 false-可选
    required: true
    # field-param field key
    # field-变量字段标识符
  - field: port
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: JMX端口
      en-US: JMX Port
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: number
    # when type is number, range is required
    # 当type为number时,用range表示范围
    range: '[0,65535]'
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: true
    # port default value
    # 端口默认值
    defaultValue: 1099
    # field-param field key
    # field-变量字段标识符
  - field: url
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: JMX URL
      en-US: JMX URL
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: text
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: false
    # hide param-true or false
    # 是否隐藏字段 true or false
    hide: true
    # 参数输入框提示信息
    # param field input placeholder
    placeholder: 'service:jmx:rmi:///jndi/rmi://host:port/jmxrmi'
    # field-param field key
    # field-变量字段标识符
  - field: username
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 用户名
      en-US: Username
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: text
    # when type is text, use limit to limit string length
    # 当type为text时,用limit表示字符串限制大小
    limit: 20
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: false
    # hide param-true or false
    # 是否隐藏字段 true or false
    hide: true
    # field-param field key
    # field-变量字段标识符
  - field: password
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 密码
      en-US: Password
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: password
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: false
    # hide param-true or false
    # 是否隐藏字段 true or false
    hide: true
# collect metrics config list
# 采集指标配置列表
metrics:
  # metrics - basic
  # 监控指标 - basic
  - name: basic
    i18n:
      zh-CN: 虚拟机基础信息
      en-US: JVM Basic
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    # 指标采集调度优先级(0->127)->(优先级高->低) 优先级低的指标会等优先级高的指标采集完成后才会被调度, 相同优先级的指标会并行调度采集
    # 优先级为0的指标为可用性指标,即它会被首先调度,采集成功才会继续调度其它指标,采集失败则中断调度
    priority: 0
    # monitor metric content in metrics
    # 具体监控指标列表
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      # field-指标名称, type-指标类型(0-number数字,1-string字符串), unit-指标单位('%','ms','MB'), label-是否是指标标签字段
      - field: VmName
        type: 1
        i18n:
          zh-CN: 名称
          en-US: Vm Name
      - field: VmVendor
        type: 1
        i18n:
          zh-CN: 厂商
          en-US: Vm Vendor
      - field: VmVersion
        type: 1
        i18n:
          zh-CN: 版本
          en-US: Vm Version
      - field: Uptime
        type: 0
        unit: ms
        i18n:
          zh-CN: 运行时长
          en-US: Up time
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    # 用于监控的协议, 比如: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: jmx
    # the config content when protocol is http
    # 协议为jmx时的配置内容
    jmx:
      # Host: ipv4 ipv6 domain name
      # 主机host: ipv4 ipv6 域名
      host: ^_^host^_^
      # port
      # 端口
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      # jmx mbean object name
      # jmx-mbean对象名称
      objectName: java.lang:type=Runtime
      url: ^_^url^_^
    # metrics - server
    # 监控指标 - server
  - name: server
    i18n:
      zh-CN: Jetty Server
      en-US: Jetty Server
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    # 指标采集调度优先级(0->127)->(优先级高->低) 优先级低的指标会等优先级高的指标采集完成后才会被调度, 相同优先级的指标会并行调度采集
    # 优先级为0的指标为可用性指标,即它会被首先调度,采集成功才会继续调度其它指标,采集失败则中断调度
    priority: 1
    # monitor metric content in metrics
    # 具体监控指标列表
    fields:
    # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
    # field-指标名称, type-指标类型(0-number数字,1-string字符串), unit-指标单位('%','ms','MB'), label-是否是指标标签字段
      - field: version
        type: 1
        i18n:
          zh-CN: 版本
          en-US: Vm Version
      - field: state
        type: 1
        i18n:
          zh-CN: 运行状态
          en-US: Run State
      - field: startupTime
        type: 0
        i18n:
          zh-CN: 启动时间
          en-US: Startup Time
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    # 用于监控的协议, 比如: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: jmx
    # the config content when protocol is http
    # 协议为jmx时的配置内容
    jmx:
      # 主机host: ipv4 ipv6 域名
      host: ^_^host^_^
      # 端口
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      # jmx mbean object name
      objectName: org.eclipse.jetty.server:type=server,id=0
      url: ^_^url^_^
    # metrics - server
    # 监控指标 - server
  - name: memory_pool
    i18n:
      zh-CN: 内存池
      en-US: Memory Pool
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    # 指标采集调度优先级(0->127)->(优先级高->低) 优先级低的指标会等优先级高的指标采集完成后才会被调度, 相同优先级的指标会并行调度采集
    # 优先级为0的指标为可用性指标,即它会被首先调度,采集成功才会继续调度其它指标,采集失败则中断调度
    priority: 1
    # monitor metric content in metrics
    # 具体监控指标列表
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      # field-指标名称, type-指标类型(0-number数字,1-string字符串), unit-指标单位('%','ms','MB'), label-是否是指标标签字段
      - field: name
        type: 1
        label: true
        i18n:
          zh-CN: 指标名称
          en-US: Name
      - field: committed
        type: 0
        i18n:
          zh-CN: 已分配内存
          en-US: Committed
      - field: init
        type: 0
        i18n:
          zh-CN: 初始化内存
          en-US: Init
      - field: max
        type: 0
        i18n:
          zh-CN: 最大内存
          en-US: Max
      - field: used
        type: 0
        i18n:
          zh-CN: 已使用内存
          en-US: Used
    # (optional)metrics field alias name, it is used as an alias field to map and convert the collected data and metrics field
    # (可选)监控指标别名, 做为中间字段与采集数据字段和指标字段映射转换
    aliasFields:
      - Name
      - Usage->committed
      - Usage->init
      - Usage->max
      - Usage->used
    # mapping and conversion expressions, use these and aliasField above to calculate metrics value
    # (可选)指标映射转换计算表达式,与上面的别名一起作用,计算出最终需要的指标值
    # eg: cores=core1+core2, usage=usage, waitTime=allTime-runningTime
    calculates:
      - name=Name
      - committed=Usage->committed
      - init=Usage->init
      - max=Usage->max
      - used=Usage->used
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    # 用于监控的协议, 比如: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: jmx
    # the config content when protocol is http
    # 协议为jmx时的配置内容
    jmx:
      # 主机host: ipv4 ipv6 域名
      host: ^_^host^_^
      # 端口
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      objectName: java.lang:type=MemoryPool,name=*
      url: ^_^url^_^
    # metrics - class_loading
    # 监控指标 - class_loading
  - name: class_loading
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    # 指标采集调度优先级(0->127)->(优先级高->低) 优先级低的指标会等优先级高的指标采集完成后才会被调度, 相同优先级的指标会并行调度采集
    # 优先级为0的指标为可用性指标,即它会被首先调度,采集成功才会继续调度其它指标,采集失败则中断调度
    priority: 1
    # monitor metric content in metrics
    # 具体监控指标列表
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      # field-指标名称, type-指标类型(0-number数字,1-string字符串), unit-指标单位('%','ms','MB'), label-是否是指标标签字段
      - field: LoadedClassCount
        type: 0
      - field: TotalLoadedClassCount
        type: 0
      - field: UnloadedClassCount
        type: 0
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    # 用于监控的协议, 比如: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: jmx
    # the config content when protocol is http
    # 协议为jmx时的配置内容
    jmx:
      # 主机host: ipv4 ipv6 域名
      host: ^_^host^_^
      # 端口
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      objectName: java.lang:type=ClassLoading
      url: ^_^url^_^
    # metrics - thread
    # 监控指标 - thread
  - name: thread
    i18n:
      zh-CN: 线程信息
      en-US: Thread
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    # 指标采集调度优先级(0->127)->(优先级高->低) 优先级低的指标会等优先级高的指标采集完成后才会被调度, 相同优先级的指标会并行调度采集
    # 优先级为0的指标为可用性指标,即它会被首先调度,采集成功才会继续调度其它指标,采集失败则中断调度
    priority: 1
    # monitor metric content in metrics
    # 具体监控指标列表
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      # field-指标名称, type-指标类型(0-number数字,1-string字符串), unit-指标单位('%','ms','MB'), label-是否是指标标签字段
      - field: TotalStartedThreadCount
        type: 0
        i18n:
          zh-CN: 已启动线程总数
          en-US: Total Started Thread Count
      - field: ThreadCount
        type: 0
        i18n:
          zh-CN: 活跃线程数
          en-US: Thread Count
      - field: PeakThreadCount
        type: 0
        i18n:
          zh-CN: 最大峰值线程数
          en-US: Peak Thread Count
      - field: DaemonThreadCount
        type: 0
        i18n:
          zh-CN: 活跃守护线程数
          en-US: Daemon Thread Count
      - field: CurrentThreadUserTime
        type: 0
        unit: s
        i18n:
          zh-CN: 线程占用的CPU时间(用户态)
          en-US: Current Thread User Time
      - field: CurrentThreadCpuTime
        type: 0
        unit: s
        i18n:
          zh-CN: 线程占用的CPU时间
          en-US: Current Thread CPU Time
    units:
      - CurrentThreadUserTime=NS->S
      - CurrentThreadCpuTime=NS->S
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    # 用于监控的协议, 比如: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: jmx
    # the config content when protocol is http
    # 协议为jmx时的配置内容
    jmx:
      # 主机host: ipv4 ipv6 域名
      host: ^_^host^_^
      # 端口
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      objectName: java.lang:type=Threading
      url: ^_^url^_^
    # metrics - webapp
    # 监控指标 - webapp
  - name: webapp
    i18n:
      zh-CN: Web应用
      en-US: Webapp
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    # 指标采集调度优先级(0->127)->(优先级高->低) 优先级低的指标会等优先级高的指标采集完成后才会被调度, 相同优先级的指标会并行调度采集
    # 优先级为0的指标为可用性指标,即它会被首先调度,采集成功才会继续调度其它指标,采集失败则中断调度
    priority: 2
    # monitor metric content in metrics
    # 具体监控指标列表
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      # field-指标名称, type-指标类型(0-number数字,1-string字符串), unit-指标单位('%','ms','MB'), label-是否是指标标签字段
      - field: displayName
        type: 1
        label: true
        i18n:
          zh-CN: 应用名称
          en-US: App Name
      - field: contextPath
        type: 1
        i18n:
          zh-CN: 上下文路径
          en-US: Context Path
      - field: state
        type: 1
        i18n:
          zh-CN: 状态
          en-US: State
      - field: resourceBase
        type: 1
        i18n:
          zh-CN: 资源
          en-US: Resource Base
      - field: shutdown
        type : 1
        i18n:
          zh-CN: 是否关闭
          en-US: Shutdown
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    # 用于监控的协议, 比如: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: jmx
    # the config content when protocol is http
    # 协议为jmx时的配置内容
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      objectName: org.eclipse.jetty.webapp:context=*,type=webappcontext,id=0
      url: ^_^url^_^
