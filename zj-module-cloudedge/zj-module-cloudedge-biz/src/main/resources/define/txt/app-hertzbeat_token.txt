# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


# The monitoring type category：service-application service monitoring db-database monitoring custom-custom monitoring os-operating system monitoring
# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: custom
# The monitoring type eg: linux windows tomcat mysql aws...
# 监控类型 eg: linux windows tomcat mysql aws...
app: hertzbeat_token
# The monitoring i18n name
# 监控类型国际化名称
name:
  zh-CN: HertzBeat(Token)
  en-US: HertzBeat(Token)
# The description and help of this monitoring type
# 监控类型的帮助描述信息
help:
  zh-CN: Hertzbeat 对 HertzBeat监控(Token)进行测量监控。<br>您可以点击 “<i>新建 HertzBeat监控(Token)</i>” 并进行配置，或者选择“<i>更多操作</i>”，导入已有配置。
  en-US: Hertzbeat monitors HertzBeat Monitor(Token). You could click the "<i>New HertzBeat Monitor(Token)</i>" button and proceed with the configuration or import an existing setup through the "<i>More Actions</i>" menu.
  zh-TW: Hertzbeat對HertzBeat監控（Token）進行量測監控。<br>您可以點擊“<i>新建HertzBeat監控（Token）</i>”並進行配寘，或者選擇“<i>更多操作</i>”，導入已有配寘。
helpLink:
  zh-CN: https://hertzbeat.com/zh-cn/docs/help/hertzbeat_token
  en-US: https://hertzbeat.com/docs/help/hertzbeat_token
# 监控所需输入参数定义(根据定义渲染页面UI)
# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  # field-变量字段标识符
  - field: host
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: host
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: true
  - field: port
    name:
      zh-CN: 端口
      en-US: Port
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: number
    # when type is number, range is required
    # 当type为number时,用range表示范围
    range: '[0,65535]'
    required: true
    defaultValue: 1157
    placeholder: 'Please input port'
  - field: ssl
    name:
      zh-CN: 启动SSL
      en-US: SSL
    # type-param field type(boolean mapping the html switch tag)
    # type-当type为boolean时,前端用switch展示开关
    type: boolean
    required: false
  - field: contentType
    name:
      zh-CN: Content-Type
      en-US: Content-Type
    type: text
    placeholder: 'Request Body Type'
    required: false
  - field: payload
    name:
      zh-CN: 请求BODY
      en-US: BODY
    type: textarea
    placeholder: 'Available When POST PUT'
    required: false
# collect metrics config list
# 采集指标配置列表
metrics:
  # metrics - auth
  # 监控指标 - auth
  - name: auth
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    # 指标采集调度优先级(0->127)->(优先级高->低) 优先级低的指标会等优先级高的指标采集完成后才会被调度, 相同优先级的指标会并行调度采集
    # 优先级为0的指标为可用性指标,即它会被首先调度,采集成功才会继续调度其它指标,采集失败则中断调度
    priority: 0
    # collect metrics content
    # 具体监控指标列表
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      # field-指标名称, type-指标类型(0-number数字,1-string字符串), unit-指标单位('%','ms','MB'), label-是否是指标标签字段
      - field: token
        type: 1
      - field: refreshToken
        type: 1
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: http
    # the config content when protocol is http
    http:
      # http host: ipv4 ipv6 domain
      host: ^_^host^_^
      # http port
      port: ^_^port^_^
      # http url
      url: /api/account/auth/form
      # http method: GET POST PUT DELETE PATCH
      method: POST
      # if enabled https
      ssl: ^_^ssl^_^
      payload: ^_^payload^_^
      # http request header content
      headers:
        content-type: ^_^contentType^_^
        ^_^headers^_^: ^_^headers^_^
      # http request params
      params:
        ^_^params^_^: ^_^params^_^
      # http response data parse type: default-hertzbeat rule, jsonpath-jsonpath script, website-for website monitoring, prometheus-prometheus exporter rule
      # http 响应数据解析方式: default-系统规则, jsonPath-jsonPath脚本, website-网站可用性指标监控, prometheus-Prometheus数据规则
      parseType: jsonPath
      parseScript: '$.data'


  - name: summary
    priority: 1
    fields:
      - field: app
        type: 1
        label: true
      - field: category
        type: 1
      - field: status
        type: 0
      - field: size
        type: 0
      - field: availableSize
        type: 0
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: /api/summary
      method: GET
      ssl: ^_^ssl^_^
      authorization:
        type: Bearer Token
        # ^o^xxx^o^ ^o^ substitution represents the value of the acquisition metric xxx of the previous priority
        # ^o^xxx^o^ 替换符表示上一个优先级的采集指标xxx的值
        bearerTokenToken: ^o^token^o^
      parseType: jsonPath
      parseScript: '$.data.apps.*'

  - name: inner_queue
    priority: 1
    fields:
      - field: responseTime
        type: 0
        unit: ms
      - field: alertDataQueue
        type: 0
      - field: metricsDataToAlertQueue
        type: 0
      - field: metricsDataToPersistentStorageQueue
        type: 0
      - field: metricsDataToMemoryStorageQueue
        type: 0
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: /api/metrics
      timeout: ^_^timeout^_^
      method: GET
      ssl: ^_^ssl^_^
      authorization:
        type: Bearer Token
        # ^o^xxx^o^ ^o^ substitution represents the value of the acquisition metric xxx of the previous priority
        # ^o^xxx^o^ 替换符表示上一个优先级的采集指标xxx的值
        bearerTokenToken: ^o^token^o^
      parseType: jsonPath
      parseScript: '$.data'

  - name: thread_state
    visible: false
    priority: 2
    fields:
      - field: state
        type: 1
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: /actuator/metrics/jvm.threads.states
      method: GET
      ssl: ^_^ssl^_^
      authorization:
        type: Bearer Token
        # ^o^xxx^o^ ^o^ substitution represents the value of the acquisition metric xxx of the previous priority
        # ^o^xxx^o^ 替换符表示上一个优先级的采集指标xxx的值
        bearerTokenToken: ^o^token^o^
      parseType: jsonPath
      parseScript: '$.availableTags[?(@.tag == "state")].values[*]'

  - name: threads
    priority: 3
    fields:
      - field: state
        type: 1
      - field: number
        type: 0
    aliasFields:
      - $.measurements[?(@.statistic == "VALUE")].value
    calculates:
      - state='^o^state^o^'
      - number=#`$.measurements[?(@.statistic == "VALUE")].value`
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: /actuator/metrics/jvm.threads.states?tag=state:^o^state^o^
      method: GET
      ssl: ^_^ssl^_^
      authorization:
        type: Bearer Token
        # ^o^xxx^o^ ^o^ substitution represents the value of the acquisition metric xxx of the previous priority
        # ^o^xxx^o^ 替换符表示上一个优先级的采集指标xxx的值
        bearerTokenToken: ^o^token^o^
      parseType: jsonPath
      parseScript: '$'

  - name: space_name
    visible: false
    priority: 4
    fields:
      - field: id
        type: 1
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: /actuator/metrics/jvm.memory.used
      method: GET
      ssl: ^_^ssl^_^
      authorization:
        type: Bearer Token
        # ^o^xxx^o^ ^o^ substitution represents the value of the acquisition metric xxx of the previous priority
        # ^o^xxx^o^ 替换符表示上一个优先级的采集指标xxx的值
        bearerTokenToken: ^o^token^o^
      parseType: jsonPath
      parseScript: '$.availableTags[?(@.tag == "id")].values[*]'

  - name: memory_used
    priority: 5
    fields:
      - field: space
        type: 1
      - field: mem_used
        type: 0
        unit: MB
    aliasFields:
      - $.measurements[?(@.statistic == "VALUE")].value
    calculates:
      - space="^o^id^o^"
      - mem_used=#`$.measurements[?(@.statistic == "VALUE")].value`
    units:
      - mem_used=B->MB
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: /actuator/metrics/jvm.memory.used?tag=id:^o^id^o^
      method: GET
      ssl: ^_^ssl^_^
      authorization:
        type: Bearer Token
        # ^o^xxx^o^ ^o^ substitution represents the value of the acquisition metric xxx of the previous priority
        # ^o^xxx^o^ 替换符表示上一个优先级的采集指标xxx的值
        bearerTokenToken: ^o^token^o^
      parseType: jsonPath
      parseScript: '$'
