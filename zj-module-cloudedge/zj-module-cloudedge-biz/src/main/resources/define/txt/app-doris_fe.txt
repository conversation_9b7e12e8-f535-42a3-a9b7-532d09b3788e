# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

category: bigdata
# The monitoring type eg: linux windows tomcat mysql aws...
# 监控类型 eg: linux windows tomcat mysql aws...
app: doris_fe
# The monitoring i18n name
# 监控类型国际化名称
name:
  zh-CN: Apache Doris FE
  en-US: Apache Doris FE
# The description and help of this monitoring type
# 监控类型的帮助描述信息
help:
  zh-CN: Hertzbeat 对 Doris 数据库FE的通用指标（doris_fe connection total、doris_fe edit log clean、doris_fe image、doris_fe rps等）进行测量监控，支持版本为DORIS2.0.0。<br>您可以点击 “<i>新建 Doris DatabaseFE</i>” 并进行配置，或者选择“<i>更多操作</i>”，导入已有配置。
  en-US: Hertzbeat monitoring Doris DatabaseFE through general performance metric such as doris_fe connection total, doris_fe edit log clean, doris_fe image and doris_fe rps. The version we support is DORIS2.0.0. You could click the "<i>New Doris DatabaseFE Monitor</i>" button and proceed with the configuration or import an existing setup through the "<i>More Actions</i>" menu.
  zh-TW: Hertzbeat 對 Doris 資料庫FE的通用名額（doris_fe connection total、doris_fe edit log clean、doris_fe image、doris_fe rps等）進行量測監控，支持版本為DORIS2.0.0。<br>您可以點擊“<i>新建Doris DatabaseFE</i>”並進行配寘，或者選擇“<i>更多操作</i>”，導入已有配寘。
helpLink:
  zh-CN: https://hertzbeat.com/zh-cn/docs/help/doris_fe/
  en-US: https://hertzbeat.com/docs/help/doris_fe/
# 监控所需输入参数定义(根据定义渲染页面UI)
# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  # field-变量字段标识符
  - field: host
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: host
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: true
  - field: port
    name:
      zh-CN: 端口
      en-US: Port
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: number
    # when type is number, range is required
    # 当type为number时,用range表示范围
    range: '[0,65535]'
    # default value
    defaultValue: 8030
    required: true
  - field: timeout
    name:
      zh-CN: 查询超时时间
      en-US: Query Timeout
    type: number
    required: false
    # hide param-true or false
    # 是否隐藏字段 true or false
    hide: true
    defaultValue: 6000
metrics:
  - name: doris_fe_connection_total
    priority: 0
    fields:
      - field: value
        type: 0
        label: true
    # 监控采集使用协议 eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: http
    # 当protocol为http协议时具体的采集配置
    http:
      # 主机host: ipv4 ipv6 域名
      host: ^_^host^_^
      # 端口
      port: ^_^port^_^
      # url请求接口路径
      url: '/metrics'
      timeout: ^_^timeout^_^
      method: GET
      parseType: prometheus
  - name: doris_fe_edit_log_clean
    priority: 0
    fields:
      - field: type
        type: 1
        label: true
      - field: value
        type: 0
     # 监控采集使用协议 eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: http
    # 当protocol为http协议时具体的采集配置
    http:
      # 主机host: ipv4 ipv6 域名
      host: ^_^host^_^
      # 端口
      port: ^_^port^_^
      # url请求接口路径
      url: '/metrics'
      timeout: ^_^timeout^_^
      method: GET
      parseType: prometheus
  - name: doris_fe_edit_log
    priority: 0
    fields:
      - field: type
        type: 1
        label: true
      - field: value
        type: 0
     # 监控采集使用协议 eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: http
    # 当protocol为http协议时具体的采集配置
    http:
      # 主机host: ipv4 ipv6 域名
      host: ^_^host^_^
      # 端口
      port: ^_^port^_^
      # url请求接口路径
      url: '/metrics'
      timeout: ^_^timeout^_^
      method: GET
      parseType: prometheus
  - name: doris_fe_image_clean
    priority: 0
    fields:
      - field: type
        type: 1
        label: true
      - field: value
        type: 0
     # 监控采集使用协议 eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: http
    # 当protocol为http协议时具体的采集配置
    http:
      # 主机host: ipv4 ipv6 域名
      host: ^_^host^_^
      # 端口
      port: ^_^port^_^
      # url请求接口路径
      url: '/metrics'
      timeout: ^_^timeout^_^
      method: GET
      parseType: prometheus
  - name: doris_fe_image_write
    priority: 0
    fields:
      - field: type
        type: 1
        label: true
      - field: value
        type: 0
     # 监控采集使用协议 eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: http
    # 当protocol为http协议时具体的采集配置
    http:
      # 主机host: ipv4 ipv6 域名
      host: ^_^host^_^
      # 端口
      port: ^_^port^_^
      # url请求接口路径
      url: '/metrics'
      timeout: ^_^timeout^_^
      method: GET
      parseType: prometheus
  - name: doris_fe_query_err
    priority: 0
    fields:
      - field: value
        type: 0
        label: true
     # 监控采集使用协议 eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: http
    # 当protocol为http协议时具体的采集配置
    http:
      # 主机host: ipv4 ipv6 域名
      host: ^_^host^_^
      # 端口
      port: ^_^port^_^
      # url请求接口路径
      url: '/metrics'
      timeout: ^_^timeout^_^
      method: GET
      parseType: prometheus
  - name: doris_fe_max_journal_id
    priority: 0
    fields:
      - field: value
        type: 0
        label: true
     # 监控采集使用协议 eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: http
    # 当protocol为http协议时具体的采集配置
    http:
      # 主机host: ipv4 ipv6 域名
      host: ^_^host^_^
      # 端口
      port: ^_^port^_^
      # url请求接口路径
      url: '/metrics'
      timeout: ^_^timeout^_^
      method: GET
      parseType: prometheus
  - name: doris_fe_max_tablet_compaction_score
    priority: 0
    fields:
      - field: value
        type: 0
        label: true
     # 监控采集使用协议 eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: http
    # 当protocol为http协议时具体的采集配置
    http:
      # 主机host: ipv4 ipv6 域名
      host: ^_^host^_^
      # 端口
      port: ^_^port^_^
      # url请求接口路径
      url: '/metrics'
      timeout: ^_^timeout^_^
      method: GET
      parseType: prometheus
  - name: doris_fe_qps
    priority: 0
    fields:
      - field: value
        type: 0
        label: true
     # 监控采集使用协议 eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: http
    # 当protocol为http协议时具体的采集配置
    http:
      # 主机host: ipv4 ipv6 域名
      host: ^_^host^_^
      # 端口
      port: ^_^port^_^
      # url请求接口路径
      url: '/metrics'
      timeout: ^_^timeout^_^
      method: GET
      parseType: prometheus
  - name: doris_fe_query_err_rate
    priority: 0
    fields:
      - field: value
        type: 0
        label: true
     # 监控采集使用协议 eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: http
    # 当protocol为http协议时具体的采集配置
    http:
      # 主机host: ipv4 ipv6 域名
      host: ^_^host^_^
      # 端口
      port: ^_^port^_^
      # url请求接口路径
      url: '/metrics'
      timeout: ^_^timeout^_^
      method: GET
      parseType: prometheus
  
  - name: doris_fe_report_queue_size
    priority: 0
    fields:
      - field: value
        type: 0
        label: true
     # 监控采集使用协议 eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: http
    # 当protocol为http协议时具体的采集配置
    http:
      # 主机host: ipv4 ipv6 域名
      host: ^_^host^_^
      # 端口
      port: ^_^port^_^
      # url请求接口路径
      url: '/metrics'
      timeout: ^_^timeout^_^
      method: GET
      parseType: prometheus
  - name: doris_fe_rps
    priority: 0
    fields:
      - field: value
        type: 0
        label: true
     # 监控采集使用协议 eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: http
    # 当protocol为http协议时具体的采集配置
    http:
      # 主机host: ipv4 ipv6 域名
      host: ^_^host^_^
      # 端口
      port: ^_^port^_^
      # url请求接口路径
      url: '/metrics'
      timeout: ^_^timeout^_^
      method: GET
      parseType: prometheus
  - name: doris_fe_scheduled_tablet_num
    priority: 0
    fields:
      - field: value
        type: 0
        label: true
     # 监控采集使用协议 eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: http
    # 当protocol为http协议时具体的采集配置
    http:
      # 主机host: ipv4 ipv6 域名
      host: ^_^host^_^
      # 端口
      port: ^_^port^_^
      # url请求接口路径
      url: '/metrics'
      timeout: ^_^timeout^_^
      method: GET
      parseType: prometheus
  - name: doris_fe_txn_status
    priority: 0
    fields:
      - field: type
        type: 1
        label: true
      - field: value
        type: 0
     # 监控采集使用协议 eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: http
    # 当protocol为http协议时具体的采集配置
    http:
      # 主机host: ipv4 ipv6 域名
      host: ^_^host^_^
      # 端口
      port: ^_^port^_^
      # url请求接口路径
      url: '/metrics'
      timeout: ^_^timeout^_^
      method: GET
      parseType: prometheus
