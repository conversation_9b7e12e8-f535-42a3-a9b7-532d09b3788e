# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring custom-custom monitoring os-operating system monitoring
# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等 mid-中间件
category: bigdata
# The monitoring type eg: linux windows tomcat mysql aws...
# 监控类型 eg: linux windows tomcat mysql aws...
app: spark
# The monitoring i18n name
# 监控类型国际化名称
name:
  zh-CN: Apache Spark
  en-US: Apache Spark
# The description and help of this monitoring type
# 监控类型的帮助描述信息
help:
  zh-CN: 云星辰 使用 <a href="https://hertzbeat.com/docs/advanced/extend-jmx">JMX 协议</a> 对 Apache Spark 的通用性能指标进行采集监控。注意⚠️：为了开启 JMX 服务，您需要在系统IP启动 server 时添加 Spark 的 VM options 选项。Master 和 Worker 的监控在 spark 集群运行时即可监控，Driver 和 Excutor 的监控需要针对某一个 app 来进行监控。请 <a href="https://hertzbeat.com/zh-cn/docs/help/spark/#%E7%AC%AC%E4%B8%80%E6%AD%A5">点击查看配置步骤</a>。
  en-US: HertzBeat uses <a href="https://hertzbeat.com/docs/advanced/extend-jmx">JMX protocol </a> to monitor general performance metric of Apache Spark. Note ⚠️:You should add Spark VM options When Start Server with customIP to enable the JVM services, You need to add Spark's VM options option when the system IP starts the server. <br>The monitoring of Master and Worker can be done then the Spark cluster is running, while the monitoring of Driver and Exciter needs to be targeted at a specific app. <a href='https://hertzbeat.com/docs/help/spark/#%E7%AC%AC%E4%B8%80%E6%AD%A5'>Please click here to view the configuration steps</a>"
  zh-TW: 云星辰 使用 <a href="https://hertzbeat.com/docs/advanced/extend-jmx">JMX 协议</a> 對 Apache Spark 的通用性能指標進行採集監控。注意⚠️：為了開啟 JMX 服務，您需要在系統IP啟動 server 時添加 Spark 的 VM options 選項。Master 和 Worker 的監控在 spark 集羣運行時即可監控，Driver 和 Excutor 的監控需要針對某一個 app 來進行監控。請 <a href=“https://hertzbeat.com/zh-cn/docs/help/spark/#%E7%AC%AC%E4%B8%80%E6%AD%A5“>點擊查看配寘步驟</a>。
helpLink:
  zh-CN: https://hertzbeat.com/zh-cn/docs/help/spark
  en-US: https://hertzbeat.com/docs/help/spark
# Input params define for monitoring(render web ui by the definition)
# 监控所需输入参数定义(根据定义渲染页面UI)
params:
  # field-param field key
  # field-字段名称标识符
  - field: host
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: host
    # required-true or false
    # 是否是必输项 true-必填 false-可选
    required: true
  # field-param field key
  # field-变量字段标识符
  - field: port
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 端口
      en-US: Port
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: number
    # when type is number, range is required
    # 当type为number时,用range表示范围
    range: '[0,65535]'
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: true
    # default value
    # 端口默认值
    defaultValue: 8712
  # field-param field key
  # field-变量字段标识符
  - field: url
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: JMX URL
      en-US: JMX URL
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: text
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: false
    # hide param-true or false
    # 是否隐藏字段 true or false
    hide: true
    # param field input placeholder
    # 参数输入框提示信息
    placeholder: 'service:jmx:rmi:///jndi/rmi://host:port/jmxrmi'
  # field-param field key
  # field-变量字段标识符
  - field: username
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 用户名
      en-US: Username
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: text
    # when type is text, use limit to limit string length
    # 当type为text时,用limit表示字符串限制大小
    limit: 20
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: false
    # hide param-true or false
    # 是否隐藏字段 true or false
    hide: true
  # field-param field key
  # field-变量字段标识符
  - field: password
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 密码
      en-US: Password
    # type-param field type(most mapping the html input tag)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: password
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: false
    # hide param-true or false
    # 是否隐藏字段 true or false
    hide: true
# collect metrics config list
# 采集指标配置列表
metrics:
  # metrics - basic
  # 监控指标 - basic
  - name: basic
    i18n:
      zh-CN: 虚拟机基础信息
      en-US: JVM Basic
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    # 指标采集调度优先级(0->127)->(优先级高->低) 优先级低的指标会等优先级高的指标采集完成后才会被调度, 相同优先级的指标会并行调度采集
    # 优先级为0的指标为可用性指标,即它会被首先调度,采集成功才会继续调度其它指标,采集失败则中断调度
    priority: 0
    # collect metrics content
    # 具体监控指标列表
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      # field-指标名称, type-指标类型(0-number数字,1-string字符串), unit-指标单位('%','ms','MB'), label-是否是指标标签字段
      - field: VmName
        type: 1
        i18n:
          zh-CN: 名称
          en-US: Vm Name
      - field: VmVendor
        type: 1
        i18n:
          zh-CN: 厂商
          en-US: Vm Vendor
      - field: VmVersion
        type: 1
        i18n:
          zh-CN: 版本
          en-US: Vm Version
      - field: Uptime
        type: 0
        unit: ms
        i18n:
          zh-CN: 运行时长
          en-US: Up time
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    # 用于监控的协议，例: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: jmx
    # the config content when protocol is jmx
    jmx:
      # host: ipv4 ipv6 domain
      # 主机host: ipv4 ipv6 域名
      host: ^_^host^_^
      # port
      # 端口
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      # jmx mbean object name
      # jmx mbean 对象名称
      objectName: java.lang:type=Runtime
      url: ^_^url^_^

  - name: memory_pool
    priority: 1
    i18n:
      zh-CN: 内存池
      en-US: Memory Pool
    fields:
      - field: name
        type: 1
        label: true
        i18n:
          zh-CN: 指标名称
          en-US: Name
      - field: committed
        type: 0
        unit: MB
        i18n:
          zh-CN: 已分配内存
          en-US: Committed
      - field: init
        type: 0
        unit: MB
        i18n:
          zh-CN: 初始化内存
          en-US: Init
      - field: max
        type: 0
        unit: MB
        i18n:
          zh-CN: 最大内存
          en-US: Max
      - field: used
        type: 0
        unit: MB
        i18n:
          zh-CN: 已使用内存
          en-US: Used
    units:
      - committed=B->MB
      - init=B->MB
      - max=B->MB
      - used=B->MB
    # (optional)metrics field alias name, it is used as an alias field to map and convert the collected data and metrics field
    # (可选)监控指标别名, 做为中间字段与采集数据字段和指标字段映射转换
    aliasFields:
      - Name
      - Usage->committed
      - Usage->init
      - Usage->max
      - Usage->used
    # mapping and conversion expressions, use these and aliasField above to calculate metrics value
    # (可选)指标映射转换计算表达式,与上面的别名一起作用,计算出最终需要的指标值
    # eg: cores=core1+core2, usage=usage, waitTime=allTime-runningTime
    calculates:
      - name=Name
      - committed=Usage->committed
      - init=Usage->init
      - max=Usage->max
      - used=Usage->used
    protocol: jmx
    jmx:
      # host: ipv4 ipv6 domain
      # 主机host: ipv4 ipv6 域名
      host: ^_^host^_^
      # port
      # 端口
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      objectName: java.lang:type=MemoryPool,name=*
      url: ^_^url^_^

  - name: code_cache
    priority: 2
    i18n:
      zh-CN: 本地代码缓冲区
      en-US: Code Cache
    fields:
      - field: committed
        type: 0
        i18n:
          zh-CN: 已分配内存
          en-US: Committed
      - field: init
        type: 0
        i18n:
          zh-CN: 初始化内存
          en-US: Init
      - field: max
        type: 0
        i18n:
          zh-CN: 最大内存
          en-US: Max
      - field: used
        type: 0
        i18n:
          zh-CN: 已使用内存
          en-US: Used
    aliasFields:
      - Usage->committed
      - Usage->init
      - Usage->max
      - Usage->used
    calculates:
      - committed=Usage->committed
      - init=Usage->init
      - max=Usage->max
      - used=Usage->used
    protocol: jmx
    jmx:
      # host: ipv4 ipv6 domain
      # 主机host: ipv4 ipv6 域名
      host: ^_^host^_^
      # port
      # 端口
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      objectName: java.lang:type=MemoryPool,name=Code Cache
      url: ^_^url^_^

  - name: class_loading
    priority: 3
    i18n:
      zh-CN: 类加载信息
      en-US: Class Loading
    # collect metrics content
    # 具体监控指标列表
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      # field-指标名称, type-指标类型(0-number数字,1-string字符串), unit-指标单位('%','ms','MB'), label-是否是指标标签字段
      - field: LoadedClassCount
        type: 0
        i18n:
          zh-CN: 当前已加载类数量
          en-US: Loaded Class Count
      - field: TotalLoadedClassCount
        type: 0
        i18n:
          zh-CN: 已加载类总数量
          en-US: Total Loaded Class Count
      - field: UnloadedClassCount
        type: 0
        i18n:
          zh-CN: 未加载类总数量
          en-US: Unloaded Class Count
    protocol: jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      objectName: java.lang:type=ClassLoading
      url: ^_^url^_^

  - name: thread
    priority: 4
    i18n:
      zh-CN: 线程信息
      en-US: Thread
    # collect metrics content
    # 具体监控指标列表
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      # field-指标名称, type-指标类型(0-number数字,1-string字符串), unit-指标单位('%','ms','MB'), label-是否是指标标签字段
      - field: TotalStartedThreadCount
        type: 0
        i18n:
          zh-CN: 已启动线程总数
          en-US: Total Started Thread Count
      - field: ThreadCount
        type: 0
        i18n:
          zh-CN: 活跃线程数
          en-US: Thread Count
      - field: PeakThreadCount
        type: 0
        i18n:
          zh-CN: 最大峰值线程数
          en-US: Peak Thread Count
      - field: DaemonThreadCount
        type: 0
        i18n:
          zh-CN: 活跃守护线程数
          en-US: Daemon Thread Count
      - field: CurrentThreadUserTime
        type: 0
        unit: s
        i18n:
          zh-CN: 线程占用的CPU时间(用户态)
          en-US: Current Thread User Time
      - field: CurrentThreadCpuTime
        type: 0
        unit: s
        i18n:
          zh-CN: 线程占用的CPU时间
          en-US: Current Thread CPU Time
    units:
      - CurrentThreadUserTime=NS->S
      - CurrentThreadCpuTime=NS->S
    protocol: jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      objectName: java.lang:type=Threading
      url: ^_^url^_^
