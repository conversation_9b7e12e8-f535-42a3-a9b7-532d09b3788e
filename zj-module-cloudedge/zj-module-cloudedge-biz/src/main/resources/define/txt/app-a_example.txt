# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring custom-custom monitoring os-operating system monitoring
# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: custom
# The monitoring type eg: linux windows tomcat mysql aws...
# 监控类型 eg: linux windows tomcat mysql aws...
app: a_example
# The monitoring i18n name
# 监控类型国际化名称
name:
  zh-CN: 模拟应用
  en-US: EXAMPLE APP
# The description and help of this monitoring type
# 监控类型的帮助描述信息
help:
  zh-CN: HertzBeat 支持自定义监控，您只需配置监控模版 YML 就能适配一款自定义的监控类型。<br>定义流程如下：HertzBeat 页面 -> 监控模版菜单 -> 新增监控类型 -> 配置自定义监控模版YML -> 点击保存应用 -> 使用新监控类型添加监控。
  en-US: "HertzBeat supports custom monitoring, and you only need to configure the monitoring template YML to adapt to a custom monitoring type. <br>Definition process as follow: HertzBeat Pages -> Main Menu -> Monitor Template -> edit and save -> apply this template."
  zh-TW: HertzBeat支持自定義監控，您只需配寘監控模版YML就能適配一款自定義的監控類型。<br>定義流程如下：HertzBeat頁面->監控模版選單->新增監控類型->配寘自定義監控模版YML ->點擊保存應用->使用新監控類型添加監控。
helpLink:
  zh-CN: https://hertzbeat.com/zh-cn/docs/advanced/extend-point/
  en-US: https://hertzbeat.com/docs/advanced/extend-point/
# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  # field-变量字段标识符
  - field: host
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: host
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: true
  # field-param field key
  # field-变量字段标识符
  - field: port
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 端口
      en-US: Port
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: number
    # when type is number, range is required
    # 当type为number时,用range表示范围
    range: '[0,65535]'
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: true
    # default value
    # 默认值
    defaultValue: 80
    # 参数输入框提示信息
    # param field input placeholder
    placeholder: 'Please Input Port'
  # field-param field key
  # field-变量字段标识符
  - field: username
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 用户名
      en-US: Username
    # type-param field type(most mapping the html input type)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: text
    # when type is text, use limit to limit string length
    # 当type为text时,用limit表示字符串限制大小
    limit: 20
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: false
    # hide param-true or false
    # 是否隐藏字段 true or false
    hide: true
  # field-param field key
  # field-变量字段标识符
  - field: password
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 用户密码
      en-US: Password
    # type-param field type(most mapping the html input tag)
    # type-字段类型,样式(大部分映射input标签type属性)
    type: password
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: false
    # hide param-true or false
    # 是否隐藏字段 true or false
    hide: true
  # field-param field key
  # field-变量字段标识符
  - field: ssl
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 启动SSL
      en-US: SSL
    # type-param field type(boolean mapping the html switch tag)
    # type-当type为boolean时,前端用switch展示开关
    type: boolean
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: false
  # field-param field key
  # field-变量字段标识符
  - field: method
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 请求方式
      en-US: Method
    # type-param field type(radio mapping the html radio tag)
    # type-当type为radio时,前端用radio展示开关
    type: radio
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: true
    # when type is radio checkbox, use option to show optional values {name1:value1,name2:value2}
    # 当type为radio单选框, checkbox复选框时, option表示可选项值列表 {name1:value1,name2:value2}
    options:
      - label: GET
        value: GET
      - label: POST
        value: POST
      - label: PUT
        value: PUT
      - label: DELETE
        value: DELETE
  # field-param field key
  # field-变量字段标识符
  - field: headers
    # name-param field display i18n name
    # name-参数字段显示名称
    name:
      zh-CN: 请求Headers
      en-US: Headers
    # type-param field type(key-value mapping the html key-value input tags)
    # type-字段类型(key-value映射为前端key-value的双input格式渲染)
    type: key-value
    # required-true or false
    # required-是否是必输项 true-必填 false-可选
    required: false
    # when type is key-value, use keyAlias to config key alias name
    # 当字段类型为key-value时, 使用keyAlias配置key的展示别名
    keyAlias: Header Name
    # when type is key-value, use valueAlias to config value alias name
    # 当字段类型为key-value时, 使用valueAlias配置value的展示别名
    valueAlias: Header Value
# collect metrics config list
# 采集指标配置列表
metrics:
  # metrics - cpu
  # 监控指标 - cpu
  - name: cpu
    # metrics name i18n label
    # 指标国际化展示名称
    i18n:
      zh-CN: CPU 信息
      en-US: CPU Info
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    # 指标采集调度优先级(0->127)->(优先级高->低) 优先级低的指标会等优先级高的指标采集完成后才会被调度, 相同优先级的指标会并行调度采集
    # 优先级为0的指标为可用性指标,即它会被首先调度,采集成功才会继续调度其它指标,采集失败则中断调度
    priority: 0
    # collect metrics content
    # 具体监控指标列表
    fields:
      # field-metric name, i18n-metric name i18n label, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      # field-指标名称, i18n-指标国际化展示名称, type-指标类型(0-number数字,1-string字符串), unit-指标单位('%','ms','MB'), label-是否是指标标签字段
      - field: hostname
        type: 1
        label: true
        i18n:
          zh-CN: 主机名称
          en-US: Host Name
      - field: usage
        type: 0
        unit: '%'
        i18n:
          zh-CN: 使用率
          en-US: Usage
      - field: cores
        type: 0
        i18n:
          zh-CN: 核数
          en-US: Cores
      - field: waitTime
        type: 0
        unit: s
        i18n:
          zh-CN: 主机名称
          en-US: Host Name
    # (optional)metrics field alias name, it is used as an alias field to map and convert the collected data and metrics field
    # (可选)监控指标别名, 做为中间字段与采集数据字段和指标字段映射转换
    aliasFields:
      - hostname
      - core1
      - core2
      - usage
      - allTime
      - runningTime
    # mapping and conversion expressions, use these and aliasField above to calculate metrics value
    # (可选)指标映射转换计算表达式,与上面的别名一起作用,计算出最终需要的指标值
    # eg: cores=core1+core2, usage=usage, waitTime=allTime-runningTime
    calculates:
      - hostname=hostname
      - cores=core1+core2
      - usage=usage
      - waitTime=allTime-runningTime
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: http
    # the config content when protocol is http
    http:
      # http host: ipv4 ipv6 domain
      host: ^_^host^_^
      # http port
      port: ^_^port^_^
      # http url
      url: /metrics/cpu
      # http method: GET POST PUT DELETE PATCH
      method: GET
      # if enabled https
      ssl: false
      # http request header content
      headers:
        ^_^headers^_^: ^_^headers^_^
      # http request params
      params:
        param1: param1
        param2: param2
      # http auth
      authorization:
        # http auth type: Basic Auth, Digest Auth, Bearer Token
        type: Basic Auth
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
      # http response data parse type: default-hertzbeat rule, jsonpath-jsonpath script, website-for website monitoring, prometheus-prometheus exporter rule
      # http 响应数据解析方式: default-系统规则, jsonPath-jsonPath脚本, website-网站可用性指标监控, prometheus-Prometheus数据规则
      parseType: jsonPath
      parseScript: '$'

  - name: memory
    i18n:
      zh-CN: 内存信息
      en-US: Memory Info
    priority: 1
    fields:
      - field: hostname
        type: 1
        label: true
        i18n:
          zh-CN: 主机名称
          en-US: Hostname
      - field: total
        type: 0
        unit: kb
        i18n:
          zh-CN: 总量
          en-US: Total
      - field: usage
        type: 0
        unit: '%'
        i18n:
          zh-CN: 使用率
          en-US: Usage
      - field: speed
        type: 0
        i18n:
          zh-CN: 速率
          en-US: Speed
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: /metrics/memory
      method: GET
      headers:
        apiVersion: v1
      params:
        param1: param1
        param2: param2
      authorization:
        type: Basic Auth
        basicAuthUsername: ^_^username^_^
        basicAuthPassword: ^_^password^_^
      parseType: default
