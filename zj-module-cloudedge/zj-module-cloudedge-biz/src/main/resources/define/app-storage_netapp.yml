# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring mid-middleware custom-custom monitoring os-operating system monitoring
category: stored
# The monitoring type eg: linux windows tomcat mysql aws...
app: storage_netapp
# The monitoring i18n name
name:
  zh-CN: NetApp存储
  en-US: NetApp Storage
# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # field-param field key
  - field: port
    # name-param field display i18n name
    name:
      zh-CN: SNMP端口
      en-US: SNMP Port
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    # required-true or false
    required: true
    # default value
    defaultValue: 161
  # field-param field key
  - field: community
    # name-param field display i18n name
    name:
      zh-CN: SNMP团体名
      en-US: SNMP Community
    # type-param field type(most mapping the html input type)
    type: text
    # when type is text, use limit to limit string length
    limit: 100
    # required-true or false
    required: true
    # default value
    defaultValue: public
    # param field input placeholder
    placeholder: 'SNMP community string'
  # field-param field key
  - field: version
    # name-param field display i18n name
    name:
      zh-CN: SNMP版本
      en-US: SNMP Version
    # type-param field type(radio mapping the html radio tag)
    type: radio
    # required-true or false
    required: true
    # when type is radio checkbox, use options to show the choice
    options:
      - label: v1
        value: 0
      - label: v2c
        value: 1
      - label: v3
        value: 3
    # default value
    defaultValue: 1
  # field-param field key
  - field: timeout
    # name-param field display i18n name
    name:
      zh-CN: 超时时间(ms)
      en-US: Timeout(ms)
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,100000]'
    # required-true or false
    required: false
    # hide-is hide this field and put it in advanced layout
    hide: true
    # default value
    defaultValue: 6000
# collect metrics config list
metrics:
  # metrics - system
  - name: system
    i18n:
      zh-CN: 系统信息
      en-US: System Information
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    fields:
      - field: systemName
        type: 1
        i18n:
          zh-CN: 系统名称
          en-US: System Name
      - field: systemModel
        type: 1
        i18n:
          zh-CN: 系统型号
          en-US: System Model
      - field: systemVersion
        type: 1
        i18n:
          zh-CN: 系统版本
          en-US: System Version
      - field: systemSerial
        type: 1
        i18n:
          zh-CN: 序列号
          en-US: Serial Number
      - field: systemUptime
        type: 0
        i18n:
          zh-CN: 运行时间
          en-US: Uptime
        unit: 'days'
      - field: globalStatus
        type: 0
        i18n:
          zh-CN: 全局状态
          en-US: Global Status
      - field: responseTime
        type: 0
        i18n:
          zh-CN: 响应时间
          en-US: Response Time
        unit: ms
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      community: ^_^community^_^
      version: ^_^version^_^
      timeout: ^_^timeout^_^
      oids:
        systemName: *******.*******.0
        systemModel: *******.4.1.789.*******
        systemVersion: *******.4.1.789.*******
        systemSerial: *******.4.1.789.*******
        systemUptime: *******.*******.0
        globalStatus: *******.4.1.789.*******.0
        responseTime: responseTime

  # metrics - cpu and memory
  - name: cpu_memory
    i18n:
      zh-CN: CPU和内存
      en-US: CPU and Memory
    priority: 1
    fields:
      - field: cpuUsage
        type: 0
        i18n:
          zh-CN: CPU使用率
          en-US: CPU Usage
        unit: '%'
      - field: cpuBusyTime
        type: 0
        i18n:
          zh-CN: CPU繁忙时间
          en-US: CPU Busy Time
        unit: 'ms'
      - field: memoryTotal
        type: 0
        i18n:
          zh-CN: 总内存
          en-US: Total Memory
        unit: MB
      - field: memoryUsed
        type: 0
        i18n:
          zh-CN: 已用内存
          en-US: Used Memory
        unit: MB
      - field: memoryUsage
        type: 0
        i18n:
          zh-CN: 内存使用率
          en-US: Memory Usage
        unit: '%'
    aliasFields:
      - cpuUsage
      - cpuBusyTime
      - memoryTotal
      - memoryUsed
    calculates:
      - memoryUsage = memoryUsed / memoryTotal * 100
    units:
      - memoryTotal=KB->MB
      - memoryUsed=KB->MB
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      community: ^_^community^_^
      version: ^_^version^_^
      timeout: ^_^timeout^_^
      oids:
        cpuUsage: *******.4.1.789.*******.0
        cpuBusyTime: *******.4.1.789.*******.0
        memoryTotal: *******.4.1.789.*******.0
        memoryUsed: *******.4.1.789.*******.0

  # metrics - storage capacity
  - name: storage_capacity
    i18n:
      zh-CN: 存储容量
      en-US: Storage Capacity
    priority: 2
    fields:
      - field: totalCapacity
        type: 0
        i18n:
          zh-CN: 总容量
          en-US: Total Capacity
        unit: GB
      - field: usedCapacity
        type: 0
        i18n:
          zh-CN: 已用容量
          en-US: Used Capacity
        unit: GB
      - field: freeCapacity
        type: 0
        i18n:
          zh-CN: 可用容量
          en-US: Free Capacity
        unit: GB
      - field: usagePercent
        type: 0
        i18n:
          zh-CN: 使用率
          en-US: Usage Percentage
        unit: '%'
      - field: snapReserve
        type: 0
        i18n:
          zh-CN: 快照预留
          en-US: Snapshot Reserve
        unit: GB
    aliasFields:
      - totalCapacity
      - usedCapacity
      - freeCapacity
      - snapReserve
    calculates:
      - usagePercent = usedCapacity / totalCapacity * 100
    units:
      - totalCapacity=KB->GB
      - usedCapacity=KB->GB
      - freeCapacity=KB->GB
      - snapReserve=KB->GB
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      community: ^_^community^_^
      version: ^_^version^_^
      timeout: ^_^timeout^_^
      oids:
        totalCapacity: *******.4.1.789.*******.14.1
        usedCapacity: *******.4.1.789.*******.15.1
        freeCapacity: *******.4.1.789.*******.16.1
        snapReserve: *******.4.1.789.*******.17.1

  # metrics - disk health
  - name: disk_health
    i18n:
      zh-CN: 磁盘健康
      en-US: Disk Health
    priority: 2
    fields:
      - field: totalDisks
        type: 0
        i18n:
          zh-CN: 磁盘总数
          en-US: Total Disks
      - field: failedDisks
        type: 0
        i18n:
          zh-CN: 故障磁盘数
          en-US: Failed Disks
      - field: spareDisks
        type: 0
        i18n:
          zh-CN: 备用磁盘数
          en-US: Spare Disks
      - field: reconstructingDisks
        type: 0
        i18n:
          zh-CN: 重建磁盘数
          en-US: Reconstructing Disks
      - field: diskReadOps
        type: 0
        i18n:
          zh-CN: 磁盘读操作
          en-US: Disk Read Ops
        unit: 'ops/s'
      - field: diskWriteOps
        type: 0
        i18n:
          zh-CN: 磁盘写操作
          en-US: Disk Write Ops
        unit: 'ops/s'
      - field: diskReadBytes
        type: 0
        i18n:
          zh-CN: 磁盘读字节
          en-US: Disk Read Bytes
        unit: 'KB/s'
      - field: diskWriteBytes
        type: 0
        i18n:
          zh-CN: 磁盘写字节
          en-US: Disk Write Bytes
        unit: 'KB/s'
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      community: ^_^community^_^
      version: ^_^version^_^
      timeout: ^_^timeout^_^
      oids:
        totalDisks: *******.4.1.789.*******.0
        failedDisks: *******.4.1.789.*******.0
        spareDisks: *******.4.1.789.*******.0
        reconstructingDisks: *******.4.1.789.*******.0
        diskReadOps: *******.4.1.789.********.0
        diskWriteOps: *******.4.1.789.********.0
        diskReadBytes: *******.4.1.789.********.0
        diskWriteBytes: *******.4.1.789.********.0

  # metrics - network interfaces
  - name: network_interfaces
    i18n:
      zh-CN: 网络接口
      en-US: Network Interfaces
    priority: 3
    fields:
      - field: networkInBytes
        type: 0
        i18n:
          zh-CN: 网络入流量
          en-US: Network In Bytes
        unit: 'KB/s'
      - field: networkOutBytes
        type: 0
        i18n:
          zh-CN: 网络出流量
          en-US: Network Out Bytes
        unit: 'KB/s'
      - field: networkInPackets
        type: 0
        i18n:
          zh-CN: 网络入包数
          en-US: Network In Packets
        unit: 'pps'
      - field: networkOutPackets
        type: 0
        i18n:
          zh-CN: 网络出包数
          en-US: Network Out Packets
        unit: 'pps'
      - field: networkErrors
        type: 0
        i18n:
          zh-CN: 网络错误数
          en-US: Network Errors
      - field: networkCollisions
        type: 0
        i18n:
          zh-CN: 网络冲突数
          en-US: Network Collisions
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      community: ^_^community^_^
      version: ^_^version^_^
      timeout: ^_^timeout^_^
      oids:
        networkInBytes: *******.4.1.789.********.0
        networkOutBytes: *******.4.1.789.********.0
        networkInPackets: *******.4.1.789.********.0
        networkOutPackets: *******.4.1.789.********.0
        networkErrors: *******.4.1.789.********.0
        networkCollisions: *******.4.1.789.********.0

  # metrics - hardware status
  - name: hardware_status
    i18n:
      zh-CN: 硬件状态
      en-US: Hardware Status
    priority: 4
    fields:
      - field: chassisTemperature
        type: 0
        i18n:
          zh-CN: 机箱温度
          en-US: Chassis Temperature
        unit: '°C'
      - field: temperatureStatus
        type: 0
        i18n:
          zh-CN: 温度状态
          en-US: Temperature Status
      - field: fanStatus
        type: 0
        i18n:
          zh-CN: 风扇状态
          en-US: Fan Status
      - field: powerSupplyStatus
        type: 0
        i18n:
          zh-CN: 电源状态
          en-US: Power Supply Status
      - field: nvramBatteryStatus
        type: 0
        i18n:
          zh-CN: NVRAM电池状态
          en-US: NVRAM Battery Status
      - field: overallHardwareStatus
        type: 0
        i18n:
          zh-CN: 整体硬件状态
          en-US: Overall Hardware Status
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      community: ^_^community^_^
      version: ^_^version^_^
      timeout: ^_^timeout^_^
      oids:
        chassisTemperature: *******.4.1.789.*******.0
        temperatureStatus: *******.4.1.789.*******.0
        fanStatus: *******.4.1.789.*******.0
        powerSupplyStatus: *******.4.1.789.*******.0
        nvramBatteryStatus: *******.4.1.789.*******.0
        overallHardwareStatus: *******.4.1.789.*******.0

  # metrics - protocol statistics
  - name: protocol_stats
    i18n:
      zh-CN: 协议统计
      en-US: Protocol Statistics
    priority: 3
    fields:
      - field: cifsOps
        type: 0
        i18n:
          zh-CN: CIFS操作数
          en-US: CIFS Operations
        unit: 'ops/s'
      - field: nfsOps
        type: 0
        i18n:
          zh-CN: NFS操作数
          en-US: NFS Operations
        unit: 'ops/s'
      - field: httpOps
        type: 0
        i18n:
          zh-CN: HTTP操作数
          en-US: HTTP Operations
        unit: 'ops/s'
      - field: fcpOps
        type: 0
        i18n:
          zh-CN: FCP操作数
          en-US: FCP Operations
        unit: 'ops/s'
      - field: iscsiOps
        type: 0
        i18n:
          zh-CN: iSCSI操作数
          en-US: iSCSI Operations
        unit: 'ops/s'
      - field: totalOps
        type: 0
        i18n:
          zh-CN: 总操作数
          en-US: Total Operations
        unit: 'ops/s'
    aliasFields:
      - cifsOps
      - nfsOps
      - httpOps
      - fcpOps
      - iscsiOps
    calculates:
      - totalOps = cifsOps + nfsOps + httpOps + fcpOps + iscsiOps
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      community: ^_^community^_^
      version: ^_^version^_^
      timeout: ^_^timeout^_^
      oids:
        cifsOps: *******.4.1.789.*******.0
        nfsOps: *******.4.1.789.*******.0
        httpOps: *******.4.1.789.*******.0
        fcpOps: *******.4.1.789.*******.0
        iscsiOps: *******.4.1.789.*******.0

  # metrics - snapshots and backup
  - name: snapshots_backup
    i18n:
      zh-CN: 快照和备份
      en-US: Snapshots and Backup
    priority: 4
    fields:
      - field: snapmirrorOn
        type: 0
        i18n:
          zh-CN: SnapMirror状态
          en-US: SnapMirror Status
      - field: snapmirrorBackups
        type: 0
        i18n:
          zh-CN: SnapMirror备份数
          en-US: SnapMirror Backups
      - field: snapmirrorSuccesses
        type: 0
        i18n:
          zh-CN: SnapMirror成功数
          en-US: SnapMirror Successes
      - field: snapmirrorFailures
        type: 0
        i18n:
          zh-CN: SnapMirror失败数
          en-US: SnapMirror Failures
      - field: snapmirrorRestores
        type: 0
        i18n:
          zh-CN: SnapMirror恢复数
          en-US: SnapMirror Restores
      - field: snapmirrorAborts
        type: 0
        i18n:
          zh-CN: SnapMirror中止数
          en-US: SnapMirror Aborts
      - field: snapmirrorWrittenBytes
        type: 0
        i18n:
          zh-CN: SnapMirror写入字节
          en-US: SnapMirror Written Bytes
        unit: KB
      - field: snapmirrorReadBytes
        type: 0
        i18n:
          zh-CN: SnapMirror读取字节
          en-US: SnapMirror Read Bytes
        unit: KB
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      community: ^_^community^_^
      version: ^_^version^_^
      timeout: ^_^timeout^_^
      oids:
        snapmirrorOn: *******.4.1.789.*******
        snapmirrorBackups: *******.4.1.789.*******
        snapmirrorSuccesses: *******.4.1.789.*******
        snapmirrorFailures: *******.4.1.789.*******
        snapmirrorRestores: *******.4.1.789.*******
        snapmirrorAborts: *******.4.1.789.*******
        snapmirrorWrittenBytes: *******.4.1.789.********
        snapmirrorReadBytes: *******.4.1.789.********

  # metrics - volume statistics
  - name: volume_stats
    i18n:
      zh-CN: 卷统计
      en-US: Volume Statistics
    priority: 3
    fields:
      - field: totalVolumes
        type: 0
        i18n:
          zh-CN: 卷总数
          en-US: Total Volumes
      - field: onlineVolumes
        type: 0
        i18n:
          zh-CN: 在线卷数
          en-US: Online Volumes
      - field: offlineVolumes
        type: 0
        i18n:
          zh-CN: 离线卷数
          en-US: Offline Volumes
      - field: restrictedVolumes
        type: 0
        i18n:
          zh-CN: 受限卷数
          en-US: Restricted Volumes
      - field: volumeReadOps
        type: 0
        i18n:
          zh-CN: 卷读操作
          en-US: Volume Read Ops
        unit: 'ops/s'
      - field: volumeWriteOps
        type: 0
        i18n:
          zh-CN: 卷写操作
          en-US: Volume Write Ops
        unit: 'ops/s'
      - field: volumeOtherOps
        type: 0
        i18n:
          zh-CN: 卷其他操作
          en-US: Volume Other Ops
        unit: 'ops/s'
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      community: ^_^community^_^
      version: ^_^version^_^
      timeout: ^_^timeout^_^
      oids:
        totalVolumes: *******.4.1.789.*******
        onlineVolumes: *******.4.1.789.*******
        offlineVolumes: *******.4.1.789.*******
        restrictedVolumes: *******.4.1.789.*******
        volumeReadOps: *******.4.1.789.********.0
        volumeWriteOps: *******.4.1.789.********.0
        volumeOtherOps: *******.4.1.789.********.0

  # metrics - cache and performance
  - name: cache_performance
    i18n:
      zh-CN: 缓存和性能
      en-US: Cache and Performance
    priority: 4
    fields:
      - field: cacheAge
        type: 0
        i18n:
          zh-CN: 缓存年龄
          en-US: Cache Age
        unit: 'minutes'
      - field: cacheHits
        type: 0
        i18n:
          zh-CN: 缓存命中数
          en-US: Cache Hits
      - field: cacheMisses
        type: 0
        i18n:
          zh-CN: 缓存未命中数
          en-US: Cache Misses
      - field: cacheHitRatio
        type: 0
        i18n:
          zh-CN: 缓存命中率
          en-US: Cache Hit Ratio
        unit: '%'
      - field: readLatency
        type: 0
        i18n:
          zh-CN: 读延迟
          en-US: Read Latency
        unit: 'ms'
      - field: writeLatency
        type: 0
        i18n:
          zh-CN: 写延迟
          en-US: Write Latency
        unit: 'ms'
      - field: totalLatency
        type: 0
        i18n:
          zh-CN: 总延迟
          en-US: Total Latency
        unit: 'ms'
      - field: ndmpSessions
        type: 0
        i18n:
          zh-CN: NDMP会话数
          en-US: NDMP Sessions
    aliasFields:
      - cacheHits
      - cacheMisses
      - readLatency
      - writeLatency
    calculates:
      - cacheHitRatio = cacheHits / (cacheHits + cacheMisses) * 100
      - totalLatency = (readLatency + writeLatency) / 2
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      community: ^_^community^_^
      version: ^_^version^_^
      timeout: ^_^timeout^_^
      oids:
        cacheAge: *******.4.1.789.********.0
        cacheHits: *******.4.1.789.********.0
        cacheMisses: *******.4.1.789.********.0
        readLatency: *******.4.1.789.********.0
        writeLatency: *******.4.1.789.*******0.0
        ndmpSessions: *******.4.1.789.*******1.0

  # metrics - quota statistics
  - name: quota_stats
    i18n:
      zh-CN: 配额统计
      en-US: Quota Statistics
    priority: 5
    fields:
      - field: quotaEntries
        type: 0
        i18n:
          zh-CN: 配额条目数
          en-US: Quota Entries
      - field: quotaExceeded
        type: 0
        i18n:
          zh-CN: 超配额数
          en-US: Quota Exceeded
      - field: quotaErrors
        type: 0
        i18n:
          zh-CN: 配额错误数
          en-US: Quota Errors
      - field: quotaUsage
        type: 0
        i18n:
          zh-CN: 配额使用率
          en-US: Quota Usage
        unit: '%'
    aliasFields:
      - quotaEntries
      - quotaExceeded
    calculates:
      - quotaUsage = quotaExceeded / quotaEntries * 100
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      community: ^_^community^_^
      version: ^_^version^_^
      timeout: ^_^timeout^_^
      oids:
        quotaEntries: *******.4.1.789.*******.0
        quotaExceeded: *******.4.1.789.*******.0
        quotaErrors: *******.4.1.789.*******.0
