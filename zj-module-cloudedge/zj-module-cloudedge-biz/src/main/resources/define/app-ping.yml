# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Category of this monitoring type:
# service - application service monitoring
# db - database monitoring
# custom - custom monitoring
# os - operating system monitoring
# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: service
# Monitoring application type name (consistent with file name) eg: linux windows tomcat mysql aws...
# 监控应用类型名称(与文件名保持一致) eg: linux windows tomcat mysql aws...
app: ping
# 监控类型国际化名称
name:
  zh-CN: Ping连通性
  en-US: PING CONNECT
# 监控所需输入参数定义(根据定义渲染页面UI)
# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # field-param field key
  - field: timeout
    # name-param field display i18n name
    name:
      zh-CN: Ping超时时间(ms)
      en-US: Ping Timeout(ms)
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,100000]'
    # required-true or false
    required: true
    # default value 6000
    defaultValue: 3000
# collect metrics config list
metrics:
  # First monitoring metric group summary
  # Note: The built-in monitoring metrics include (responseTime - response time)
  - name: summary
    i18n:
      zh-CN: 概要
      en-US: Summary
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
    fields:
      - field: responseTime
        type: 0
        unit: ms
        i18n:
          zh-CN: 响应时间
          en-US: Response Time
      - field: packetLoss
        type: 0
        unit: '%'
        i18n:
          zh-CN: 丢包率
          en-US: Packet Loss Rate
    # Monitoring and collection protocols eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: icmp
    # Specific collection configuration when the protocol is the ping protocol
    icmp:
      # icmp host
      host: ^_^host^_^
      # timeout
      timeout: ^_^timeout^_^
      count: 4