# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring custom-custom monitoring os-operating system monitoring
# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: os
# The monitoring type eg: linux windows tomcat mysql aws...
# 监控类型 eg: linux windows tomcat mysql aws...
app: euleros
# The monitoring i18n name
# 监控类型国际化名称
name:
  zh-CN: OpenEulerOS
  en-US: EulerOS
# 监控所需输入参数定义(根据定义渲染页面UI)
# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # field-param field key
  - field: port
    # name-param field display i18n name
    name:
      zh-CN: 端口
      en-US: Port
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    # required-true or false
    required: true
    # default value
    defaultValue: 22
  # field-param field key
  - field: timeout
    # name-param field display i18n name
    name:
      zh-CN: 超时时间(ms)
      en-US: Timeout(ms)
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[400,200000]'
    # required-true or false
    required: false
    # default value
    defaultValue: 6000
  # field-param field key
  - field: reuseConnection
    # name-param field display i18n name
    name:
      zh-CN: 复用连接
      en-US: Reuse Connection
    # type-param field type(most mapping the html input type)
    type: boolean
    # required-true or false
    required: true
    defaultValue: true
  # field-param field key
  - field: username
    # name-param field display i18n name
    name:
      zh-CN: 用户名
      en-US: Username
    # type-param field type(most mapping the html input type)
    type: text
    # when type is text, use limit to limit string length
    limit: 50
    # required-true or false
    required: true
  # field-param field key
  - field: password
    # name-param field display i18n name
    name:
      zh-CN: 密码
      en-US: Password
    # type-param field type(most mapping the html input tag)
    type: password
    # required-true or false
    required: false
  # field-param field key
  - field: privateKey
    # name-param field display i18n name
    name:
      zh-CN: 私钥
      en-US: PrivateKey
    # type-param field type(most mapping the html input type)
    type: textarea
    placeholder: -----BEGIN RSA PRIVATE KEY-----
    # required-true or false
    required: false
    # hide param-true or false
    hide: true
# collect metrics config list
metrics:
  # metrics - basic, inner monitoring metrics (responseTime - response time)
  - name: basic
    i18n:
      zh-CN: 系统基本信息
      en-US: Basic Info
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    # collect metrics content
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      - field: hostname
        type: 1
        label: true
        i18n:
          zh-CN: 主机名称
          en-US: Host Name
      - field: version
        type: 1
        i18n:
          zh-CN: 操作系统版本
          en-US: System Version
      - field: uptime
        type: 1
        i18n:
          zh-CN: 启动时间
          en-US: Uptime
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: ssh
    # the config content when protocol is ssh
    ssh:
      # ssh host: ipv4 ipv6 domain
      host: ^_^host^_^
      # ssh port
      port: ^_^port^_^
      # ssh username
      username: ^_^username^_^
      # ssh password
      password: ^_^password^_^
      # ssh private key
      privateKey: ^_^privateKey^_^
      timeout: ^_^timeout^_^
      reuseConnection: ^_^reuseConnection^_^
      # ssh run collect script
      script: (uname -r ; hostname ; uptime | awk -F "," '{print $1}' | sed  "s/ //g") | sed ":a;N;s/\n/^/g;ta" | awk -F '^' 'BEGIN{print "version hostname uptime"} {print $1, $2, $3}'
      # ssh response data parse type: oneRow, multiRow
      parseType: multiRow

  - name: cpu
    i18n:
      zh-CN: CPU 信息
      en-US: CPU Info
    priority: 1
    fields:
      - field: info
        type: 1
        i18n:
          zh-CN: 型号
          en-US: Info
      - field: cores
        type: 1
        i18n:
          zh-CN: 核数
          en-US: Cores
      - field: interrupt
        type: 0
        i18n:
          zh-CN: 中断数
          en-US: Interrupt
      - field: load
        type: 1
        i18n:
          zh-CN: 负载
          en-US: Load
      - field: context_switch
        type: 0
        i18n:
          zh-CN: 上下文切换
          en-US: Context Switch
      - field: usage
        type: 0
        unit: '%'
        i18n:
          zh-CN: 使用率
          en-US: Usage
    # (optional)metrics field alias name, it is used as an alias field to map and convert the collected data and metrics field
    aliasFields:
      - info
      - cores
      - load
      - interrupt
      - context_switch
      - idle
    # mapping and conversion expressions, use these and aliasField above to calculate metrics value
    # eg: cores=core1+core2, usage=usage, waitTime=allTime-runningTime
    calculates:
      - info=info
      - cores=cores
      - load=load
      - interrupt=interrupt
      - context_switch=context_switch
      - usage=100-idle
    protocol: ssh
    ssh:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      privateKey: ^_^privateKey^_^
      timeout: ^_^timeout^_^
      reuseConnection: ^_^reuseConnection^_^
      script: "LANG=C lscpu | awk -F: '$1==\"Model name\" {print $2}';awk '/processor/{core++} END{print core}' /proc/cpuinfo;uptime | sed 's/,/ /g' | awk '{for(i=NF-2;i<=NF;i++)print $i }' | xargs;vmstat 1 1 | awk 'NR==3{print $11}';vmstat 1 1 | awk 'NR==3{print $12}';vmstat 1 2 | awk 'NR==4{print $15}'"
      parseType: oneRow

  - name: memory
    i18n:
      zh-CN: 内存信息
      en-US: Memory Info
    priority: 2
    fields:
      - field: total
        type: 1
        unit: Mb
        i18n:
          zh-CN: 总内存容量
          en-US: Total Memory
      - field: used
        type: 0
        unit: Mb
        i18n:
          zh-CN: 用户程序内存量
          en-US: User Program Memory
      - field: free
        type: 0
        unit: Mb
        i18n:
          zh-CN: 空闲内存容量
          en-US: Free Memory
      - field: buff_cache
        type: 0
        unit: Mb
        i18n:
          zh-CN: 缓存占用内存
          en-US: Buff Cache Memory
      - field: available
        type: 0
        unit: Mb
        i18n:
          zh-CN: 剩余可用内存
          en-US: Available Memory
      - field: usage
        type: 0
        unit: '%'
        i18n:
          zh-CN: 内存使用率
          en-US: Memory Usage
    aliasFields:
      - total
      - used
      - free
      - buff_cache
      - available
    calculates:
      - total=total
      - used=used
      - free=free
      - buff_cache=buff_cache
      - available=available
      - usage=(used / total) * 100
    protocol: ssh
    ssh:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      privateKey: ^_^privateKey^_^
      timeout: ^_^timeout^_^
      reuseConnection: ^_^reuseConnection^_^
      script: free -m | awk 'BEGIN{print "total used free buff_cache available"} NR==2{print $2,$3,$4,$6,$7}'
      parseType: multiRow

  - name: disk
    i18n:
      zh-CN: 磁盘信息
      en-US: Disk Info
    priority: 3
    fields:
      - field: disk_num
        type: 1
        i18n:
          zh-CN: 磁盘总数
          en-US: Disk Num
      - field: partition_num
        type: 1
        i18n:
          zh-CN: 分区总数
          en-US: Partition Num
      - field: block_write
        type: 0
        i18n:
          zh-CN: 写磁盘块数
          en-US: Block Write
      - field: block_read
        type: 0
        i18n:
          zh-CN: 读磁盘块数
          en-US: Block Read
      - field: write_rate
        type: 0
        unit: iops
        i18n:
          zh-CN: 磁盘写速率
          en-US: Write Rate
      - field: read_rate
        type: 0
        unit: iops
        i18n:
          zh-CN: 磁盘读速率
          en-US: Read Rate
    protocol: ssh
    ssh:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      privateKey: ^_^privateKey^_^
      timeout: ^_^timeout^_^
      reuseConnection: ^_^reuseConnection^_^
      script: vmstat -D | awk 'NR==1{print $1}';vmstat -D | awk 'NR==2{print $1}';vmstat 1 1 | awk 'NR==3{print $10}';vmstat 1 1 | awk 'NR==3{print $9}';vmstat 1 1 | awk 'NR==3{print $16}';vmstat 1 1 | awk 'NR==3{print $8}'
      parseType: oneRow

  - name: interface
    i18n:
      zh-CN: 网卡信息
      en-US: Interface Info
    priority: 4
    fields:
      - field: interface_name
        type: 1
        label: true
        i18n:
          zh-CN: 网卡名称
          en-US: Interface Name
      - field: receive_bytes
        type: 0
        unit: Mb
        i18n:
          zh-CN: 入站数据流量
          en-US: Receive Bytes
      - field: transmit_bytes
        type: 0
        unit: Mb
        i18n:
          zh-CN: 出站数据流量
          en-US: Transmit Bytes
    units:
      - receive_bytes=B->MB
      - transmit_bytes=B->MB
    protocol: ssh
    ssh:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      privateKey: ^_^privateKey^_^
      timeout: ^_^timeout^_^
      reuseConnection: ^_^reuseConnection^_^
      script: cat /proc/net/dev | tail -n +3 | awk 'BEGIN{ print "interface_name receive_bytes transmit_bytes"} {print $1,$2,$10}'
      parseType: multiRow

  - name: disk_free
    i18n:
      zh-CN: 文件系统
      en-US: Disk Free
    priority: 5
    fields:
      - field: filesystem
        type: 1
        i18n:
          zh-CN: 文件系统
          en-US: Filesystem
      - field: used
        type: 0
        unit: Mb
        i18n:
          zh-CN: 已使用量
          en-US: Used
      - field: available
        type: 0
        unit: Mb
        i18n:
          zh-CN: 可用量
          en-US: Available
      - field: usage
        type: 0
        unit: '%'
        i18n:
          zh-CN: 使用率
          en-US: Usage
      - field: mounted
        type: 1
        label: true
        i18n:
          zh-CN: 挂载点
          en-US: Mounted
    protocol: ssh
    ssh:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      privateKey: ^_^privateKey^_^
      timeout: ^_^timeout^_^
      reuseConnection: ^_^reuseConnection^_^
      script: df -mP | tail -n +2 | awk 'BEGIN{ print "filesystem used available usage mounted"} {print $1,$3,$4,$5,$6}'
      parseType: multiRow

  - name: top_cpu_process
    i18n:
      zh-CN: Top10 CPU 进程
      en-US: Top10 CPU Process
    priority: 6
    fields:
      - field: pid
        type: 1
        label: true
        i18n:
          zh-CN: 进程ID
          en-US: PID
      - field: cpu_usage
        type: 0
        unit: '%'
        i18n:
          zh-CN: CPU占用率
          en-US: CPU Usage
      - field: mem_usage
        type: 0
        unit: '%'
        i18n:
          zh-CN: 内存占用率
          en-US: Memory Usage
      - field: command
        type: 1
        i18n:
          zh-CN: 执行命令
          en-US: Command
    protocol: ssh
    ssh:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      privateKey: ^_^privateKey^_^
      timeout: ^_^timeout^_^
      reuseConnection: ^_^reuseConnection^_^
      script: ps aux | sort -k3nr | awk 'BEGIN{ print "pid cpu_usage mem_usage command" } {printf "%s %s %s ", $2, $3, $4; for (i=11; i<=NF; i++) { printf "%s", $i; if (i < NF) printf " "; } print ""}' | head -n 11
      parseType: multiRow

  - name: top_mem_process
    i18n:
      zh-CN: Top10 内存进程
      en-US: Top10 Memory Process
    priority: 7
    fields:
      - field: pid
        type: 1
        label: true
        i18n:
          zh-CN: 进程ID
          en-US: PID
      - field: mem_usage
        type: 0
        unit: '%'
        i18n:
          zh-CN: 内存占用率
          en-US: Memory Usage
      - field: cpu_usage
        type: 0
        unit: '%'
        i18n:
          zh-CN: CPU占用率
          en-US: CPU Usage
      - field: command
        type: 1
        i18n:
          zh-CN: 执行命令
          en-US: Command
    protocol: ssh
    ssh:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      privateKey: ^_^privateKey^_^
      timeout: ^_^timeout^_^
      reuseConnection: ^_^reuseConnection^_^
      script: ps aux | sort -k4nr | awk 'BEGIN{ print "pid cpu_usage mem_usage command" } {printf "%s %s %s ", $2, $3, $4; for (i=11; i<=NF; i++) { printf "%s", $i; if (i < NF) printf " "; } print ""}' | head -n 11
      parseType: multiRow



  - name: disk_health
    i18n:
      zh-CN: 磁盘健康状态
      en-US: Disk Health Status
    priority: 8
    fields:
      - field: device
        type: 1
        label: true
        i18n:
          zh-CN: 设备名称
          en-US: Device Name
      - field: model
        type: 1
        i18n:
          zh-CN: 磁盘型号
          en-US: Disk Model
      - field: size
        type: 1
        i18n:
          zh-CN: 磁盘大小
          en-US: Disk Size
      - field: type
        type: 1
        i18n:
          zh-CN: 磁盘类型
          en-US: Disk Type
      - field: state
        type: 1
        i18n:
          zh-CN: 运行状态
          en-US: Running State
      - field: health_status
        type: 1
        i18n:
          zh-CN: 健康状态
          en-US: Health Status
    protocol: ssh
    ssh:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      privateKey: ^_^privateKey^_^
      timeout: ^_^timeout^_^
      reuseConnection: ^_^reuseConnection^_^
      script: lsblk -d -o NAME,MODEL,SIZE,TYPE,STATE | tail -n +2 | awk 'BEGIN{print "device model size type state health_status"} {if($1!="loop" && $1!="sr") {health="Unknown"; if($5=="running") health="Good"; print $1, ($2?$2:"Unknown"), ($3?$3:"Unknown"), ($4?$4:"Unknown"), ($5?$5:"Unknown"), health}}'
      parseType: multiRow

  - name: hardware_serial
    i18n:
      zh-CN: 硬件序列号
      en-US: Hardware Serial Numbers
    priority: 9
    fields:
      - field: component
        type: 1
        label: true
        i18n:
          zh-CN: 硬件组件
          en-US: Hardware Component
      - field: serial_number
        type: 1
        i18n:
          zh-CN: 序列号
          en-US: Serial Number
      - field: vendor
        type: 1
        i18n:
          zh-CN: 厂商
          en-US: Vendor
      - field: product
        type: 1
        i18n:
          zh-CN: 产品型号
          en-US: Product Model
    protocol: ssh
    ssh:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      privateKey: ^_^privateKey^_^
      timeout: ^_^timeout^_^
      reuseConnection: ^_^reuseConnection^_^
      script: (echo "System $(cat /sys/class/dmi/id/product_serial 2>/dev/null || echo 'Unknown') $(cat /sys/class/dmi/id/sys_vendor 2>/dev/null || echo 'Unknown') $(cat /sys/class/dmi/id/product_name 2>/dev/null || echo 'Unknown')"; echo "Motherboard $(cat /sys/class/dmi/id/board_serial 2>/dev/null || echo 'Unknown') $(cat /sys/class/dmi/id/board_vendor 2>/dev/null || echo 'Unknown') $(cat /sys/class/dmi/id/board_name 2>/dev/null || echo 'Unknown')"; for disk in $(lsblk -d -n -o NAME | grep -E '^(sd|nvme|hd)'); do echo "Disk-$disk $(cat /sys/block/$disk/device/serial 2>/dev/null || echo 'Unknown') $(cat /sys/block/$disk/device/vendor 2>/dev/null || echo 'Unknown') $(cat /sys/block/$disk/device/model 2>/dev/null || echo 'Unknown')"; done) | awk 'BEGIN{print "component serial_number vendor product"} {print $1, $2, $3, $4}'
      parseType: multiRow

  - name: temperature_sensors
    i18n:
      zh-CN: 温度传感器
      en-US: Temperature Sensors
    priority: 10
    fields:
      - field: sensor_name
        type: 1
        label: true
        i18n:
          zh-CN: 传感器名称
          en-US: Sensor Name
      - field: temperature
        type: 0
        unit: '°C'
        i18n:
          zh-CN: 当前温度
          en-US: Current Temperature
      - field: max_temp
        type: 0
        unit: '°C'
        i18n:
          zh-CN: 最高温度
          en-US: Max Temperature
      - field: critical_temp
        type: 0
        unit: '°C'
        i18n:
          zh-CN: 临界温度
          en-US: Critical Temperature
      - field: status
        type: 1
        i18n:
          zh-CN: 状态
          en-US: Status
    protocol: ssh
    ssh:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      privateKey: ^_^privateKey^_^
      timeout: ^_^timeout^_^
      reuseConnection: ^_^reuseConnection^_^
      script: (for zone in /sys/class/thermal/thermal_zone*; do if [ -d "$zone" ]; then name=$(cat $zone/type 2>/dev/null || echo "thermal_zone$(basename $zone | sed 's/thermal_zone//')"); temp=$(cat $zone/temp 2>/dev/null || echo "0"); temp=$((temp/1000)); max_temp=$(cat $zone/trip_point_0_temp 2>/dev/null || echo "0"); max_temp=$((max_temp/1000)); crit_temp=$(cat $zone/trip_point_1_temp 2>/dev/null || echo "0"); crit_temp=$((crit_temp/1000)); status="Normal"; if [ $temp -gt $max_temp ] && [ $max_temp -gt 0 ]; then status="Warning"; fi; if [ $temp -gt $crit_temp ] && [ $crit_temp -gt 0 ]; then status="Critical"; fi; echo "$name $temp $max_temp $crit_temp $status"; fi; done; for hwmon in /sys/class/hwmon/hwmon*; do if [ -d "$hwmon" ]; then for temp_input in $hwmon/temp*_input; do if [ -f "$temp_input" ]; then name=$(cat $hwmon/name 2>/dev/null || echo "hwmon$(basename $hwmon | sed 's/hwmon//')"); temp=$(cat $temp_input 2>/dev/null || echo "0"); temp=$((temp/1000)); max_file=$(echo $temp_input | sed 's/_input/_max/'); max_temp=$(cat $max_file 2>/dev/null || echo "0"); max_temp=$((max_temp/1000)); crit_file=$(echo $temp_input | sed 's/_input/_crit/'); crit_temp=$(cat $crit_file 2>/dev/null || echo "0"); crit_temp=$((crit_temp/1000)); status="Normal"; if [ $temp -gt $max_temp ] && [ $max_temp -gt 0 ]; then status="Warning"; fi; if [ $temp -gt $crit_temp ] && [ $crit_temp -gt 0 ]; then status="Critical"; fi; echo "${name}_$(basename $temp_input | sed 's/_input//') $temp $max_temp $crit_temp $status"; fi; done; fi; done) | awk 'BEGIN{print "sensor_name temperature max_temp critical_temp status"} {print $1, $2, $3, $4, $5}'
      parseType: multiRow

  - name: fan_status
    i18n:
      zh-CN: 风扇状态
      en-US: Fan Status
    priority: 11
    fields:
      - field: fan_name
        type: 1
        label: true
        i18n:
          zh-CN: 风扇名称
          en-US: Fan Name
      - field: speed_rpm
        type: 0
        unit: 'RPM'
        i18n:
          zh-CN: 转速
          en-US: Speed RPM
      - field: min_speed
        type: 0
        unit: 'RPM'
        i18n:
          zh-CN: 最低转速
          en-US: Min Speed
      - field: max_speed
        type: 0
        unit: 'RPM'
        i18n:
          zh-CN: 最高转速
          en-US: Max Speed
      - field: status
        type: 1
        i18n:
          zh-CN: 运行状态
          en-US: Running Status
    protocol: ssh
    ssh:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      privateKey: ^_^privateKey^_^
      timeout: ^_^timeout^_^
      reuseConnection: ^_^reuseConnection^_^
      script: (for hwmon in /sys/class/hwmon/hwmon*; do if [ -d "$hwmon" ]; then for fan_input in $hwmon/fan*_input; do if [ -f "$fan_input" ]; then name=$(cat $hwmon/name 2>/dev/null || echo "hwmon$(basename $hwmon | sed 's/hwmon//')"); speed=$(cat $fan_input 2>/dev/null || echo "0"); min_file=$(echo $fan_input | sed 's/_input/_min/'); min_speed=$(cat $min_file 2>/dev/null || echo "0"); max_file=$(echo $fan_input | sed 's/_input/_max/'); max_speed=$(cat $max_file 2>/dev/null || echo "0"); status="Unknown"; if [ $speed -gt 0 ]; then status="Running"; else status="Stopped"; fi; echo "${name}_$(basename $fan_input | sed 's/_input//') $speed $min_speed $max_speed $status"; fi; done; fi; done) | awk 'BEGIN{print "fan_name speed_rpm min_speed max_speed status"} {if(NF>=5) print $1, $2, $3, $4, $5; else if(NF>=2) print $1, $2, "0", "0", "Unknown"}'
      parseType: multiRow

  - name: power_supply
    i18n:
      zh-CN: 电源信息
      en-US: Power Supply Info
    priority: 12
    fields:
      - field: power_name
        type: 1
        label: true
        i18n:
          zh-CN: 电源名称
          en-US: Power Supply Name
      - field: status
        type: 1
        i18n:
          zh-CN: 电源状态
          en-US: Power Status
      - field: type
        type: 1
        i18n:
          zh-CN: 电源类型
          en-US: Power Type
      - field: capacity
        type: 0
        unit: '%'
        i18n:
          zh-CN: 电池容量
          en-US: Battery Capacity
      - field: voltage
        type: 0
        unit: 'mV'
        i18n:
          zh-CN: 电压
          en-US: Voltage
      - field: health
        type: 1
        i18n:
          zh-CN: 健康状态
          en-US: Health Status
    protocol: ssh
    ssh:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      privateKey: ^_^privateKey^_^
      timeout: ^_^timeout^_^
      reuseConnection: ^_^reuseConnection^_^
      script: (if [ -d "/sys/class/power_supply" ] && [ "$(ls -A /sys/class/power_supply/ 2>/dev/null)" ]; then for psu in "/sys/class/power_supply"/*; do name=$(basename $psu); status=$(cat $psu/status 2>/dev/null || echo "Unknown"); type=$(cat $psu/type 2>/dev/null || echo "Unknown"); capacity=$(cat $psu/capacity 2>/dev/null || echo "100"); voltage=$(cat $psu/voltage_now 2>/dev/null || echo "0"); health=$(cat $psu/health 2>/dev/null || echo "Unknown"); echo "$name $status $type $capacity $voltage $health"; done; else if [ -f "/proc/acpi/ac_adapter/ADP1/state" ]; then echo "AC_Adapter $(cat /proc/acpi/ac_adapter/ADP1/state | awk '{print $2}') Mains 100 0 Good"; else echo "Server_PSU Online Mains 100 0 Good"; fi; fi) | awk 'BEGIN{print "power_name status type capacity voltage health"} {print $1, $2, $3, $4, $5, $6}'
      parseType: multiRow
