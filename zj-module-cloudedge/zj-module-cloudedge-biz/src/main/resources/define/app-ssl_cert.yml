# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring custom-custom monitoring os-operating system monitoring
# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: service
# The monitoring type eg: linux windows tomcat mysql aws...
# 监控类型 eg: linux windows tomcat mysql aws...
app: ssl_cert
# The monitoring i18n name
# 监控类型国际化名称
name:
  zh-CN: SSL证书监控
  en-US: SSL Certificate
# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # field-param field key
  - field: port
    # name-param field display i18n name
    name:
      zh-CN: 端口
      en-US: Port
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    # required-true or false
    required: true
    # default value
    defaultValue: 443
  # field-param field key
  - field: verify
    # name-param field display i18n name
    name:
      zh-CN: 校验证书
      en-US: verify
    # When the type is boolean, the frontend will display a switch for it.
    type: boolean
    defaultValue: true
    # required-true or false
    required: false
  # field-param field key
  - field: uri
    # name-param field display i18n name
    name:
      zh-CN: 相对路径
      en-US: URI
    # type-param field type(most mapping the html input type)
    type: text
    # when type is text, use limit to limit string length
    limit: 200
    # required-true or false
    required: false
    # param field input placeholder
    placeholder: 'Website uri path(no ip port) EG:/console'
    # hide param-true or false
    hide: true
# collect metrics config list
metrics:
  # metrics - certificate
  - name: certificate
    i18n:
      zh-CN: 证书
      en-US: Certificate
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    # collect metrics content
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      - field: subject
        type: 1
        label: true
        i18n:
          zh-CN: 颁发对象
          en-US: Subject
      - field: expired
        type: 1
        i18n:
          zh-CN: 是否过期
          en-US: Expired
      - field: start_time
        type: 1
        i18n:
          zh-CN: 颁发日期
          en-US: Start Time
      - field: start_timestamp
        type: 0
        unit: ms
        i18n:
          zh-CN: 颁发时间戳
          en-US: Start Timestamp
      - field: end_time
        type: 1
        i18n:
          zh-CN: 截止日期
          en-US: End Time
      - field: end_timestamp
        type: 0
        unit: ms
        i18n:
          zh-CN: 截止时间戳
          en-US: End Timestamp
      - field: days_remaining
        type: 0
        unit: d
        i18n:
          zh-CN: 剩余天数
          en-US: Remaining Days
    aliasFields:
      - subject
      - expired
      - start_time
      - start_timestamp
      - end_time
      - start_timestamp
      - end_timestamp
      - days_remaining
    calculates:
      - days_remaining=(end_timestamp-now())/86400000
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: ssl_cert
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: ^_^uri^_^
      ssl: ^_^verify^_^