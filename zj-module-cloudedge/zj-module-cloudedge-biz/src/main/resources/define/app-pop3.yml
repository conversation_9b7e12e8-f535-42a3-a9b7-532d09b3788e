# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: mid
# 监控应用类型名称(与文件名保持一致) eg: linux windows tomcat mysql aws...
app: pop3
# The app api i18n name
# app api国际化名称
name:
  zh-CN: POP3邮件服务
  en-US: POP3 Email Server
# 监控类型的帮助描述信息

# app api所需输入参数定义(根据定义渲染页面UI)
# Input params define for app api(render web ui by the definition)
params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # field-param field key
  - field: port
    # name-param field display i18n name
    name:
      zh-CN: 端口
      en-US: Port
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    # required-true or false
    required: true
    # default value
    defaultValue: 110
  # field-param field key
  - field: timeout
    # name-param field display i18n name
    name:
      zh-CN: 连接超时时间(ms)
      en-US: Connect Timeout(ms)
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,100000]'
    # required-true or false
    required: true
    # default value 6000
    defaultValue: 6000
  - field: ssl
    # name-param field display i18n name
    name:
      zh-CN: 启动SSL
      en-US: SSL
    # When the type is boolean, the frontend will display a switch for it.
    type: boolean
    # required-true or false
    required: false
  # field-param field key
  - field: email
    # name-param field display i18n name
    name:
      zh-CN: POP邮箱地址
      en-US: Email
    # type-param field type(most mapping the html input type) The type "text" belongs to a text input field.
    type: text
    # required-true or false
    required: true
  # field-param field key
  - field: authorize
    # name-param field display i18n name
    name:
      zh-CN: 授权码
      en-US: Authorize Code
    # type-param field type(most mapping the html input type) The type "text" belongs to a text input field.
    type: text
    # required-true or false
    required: true

# collect metrics config list
metrics:
  # metrics - available
  - name: available
    i18n:
      zh-CN: 可用性
      en-US: Available
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    # collect metrics content
    fields:
      # field-metric name, type-metric type(0-number,1-string), instance-is instance primary key, unit-metric unit
      - field: responseTime
        type: 0
        unit: ms
        i18n:
          zh-CN: 响应时间
          en-US: Response Time
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: pop3
    # Specific collection configuration when protocol is http protocol
    pop3:
      # http host: ipv4 ipv6 domain
      host: ^_^host^_^
      # http port
      port: ^_^port^_^
      # timeout
      timeout: ^_^timeout^_^
      # enable SSL/TLS, that is, whether it is http or https, the default is false
      ssl: ^_^ssl^_^
      # email
      email: ^_^email^_^
      # password
      authorize: ^_^authorize^_^

  - name: email_status
    i18n:
      zh-CN: 邮箱状态信息
      en-US: Email Status
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 1
    fields:
      - field: email_count
        type: 0
        i18n:
          zh-CN: 邮箱数量
          en-US: Email Count
      - field: mailbox_size
        type: 0
        unit: KB
        i18n:
          zh-CN: 邮箱大小
          en-US: MailBox Size
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: pop3
    # Specific collection configuration when protocol is http protocol
    pop3:
      # http host: ipv4 ipv6 domain
      host: ^_^host^_^
      # http port
      port: ^_^port^_^
      # timeout
      timeout: ^_^timeout^_^
      # enable SSL/TLS, that is, whether it is http or https, the default is false
      ssl: ^_^ssl^_^
      # email
      email: ^_^email^_^
      # password
      authorize: ^_^authorize^_^
