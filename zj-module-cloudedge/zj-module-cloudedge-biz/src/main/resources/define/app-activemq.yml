# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring custom-custom monitoring os-operating system monitoring
# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: mid
# The monitoring type eg: linux windows tomcat mysql aws...
# 监控类型 eg: linux windows tomcat mysql aws...
app: activemq
# The monitoring i18n name
# 监控类型国际化名称
name:
  zh-CN: ActiveMQ消息系统
  en-US: ActiveMQ Message
# 监控所需输入参数定义(根据定义渲染页面UI)
# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: 目标 Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # field-param field key
  - field: port
    # name-param field display i18n name
    name:
      zh-CN: JMX 端口
      en-US: JMX Port
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    # required-true or false
    required: true
    # default value
    defaultValue: 11099
  # field-param field key
  - field: url
    # name-param field display i18n name
    name:
      zh-CN: JMX URL
      en-US: JMX URL
    # type-param field type(most mapping the html input type)
    type: text
    # required-true or false
    required: false
    # hide param-true or false
    hide: true
    # param field input placeholder
    placeholder: 'service:jmx:rmi:///jndi/rmi://host:port/jmxrmi'
  # field-param field key
  - field: username
    # name-param field display i18n name
    name:
      zh-CN: 用户名
      en-US: Username
    # type-param field type(most mapping the html input type)
    type: text
    # when type is text, use limit to limit string length
    limit: 50
    # required-true or false
    required: false
    # hide param-true or false
    hide: true
  # field-param field key
  - field: password
    # name-param field display i18n name
    name:
      zh-CN: 密码
      en-US: Password
    # type-param field type(most mapping the html input tag)
    type: password
    # required-true or false
    required: false
    # hide param-true or false
    hide: true

# collect metrics config list
metrics:
  # metrics - basic
  - name: basic
    i18n:
      zh-CN: 虚拟机基础信息
      en-US: JVM Basic
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    # collect metrics content
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      - field: VmName
        type: 1
        i18n:
          zh-CN: 名称
          en-US: Vm Name
      - field: VmVendor
        type: 1
        i18n:
          zh-CN: 厂商
          en-US: Vm Vendor
      - field: VmVersion
        type: 1
        i18n:
          zh-CN: 版本
          en-US: Vm Version
      - field: Uptime
        type: 0
        unit: ms
        i18n:
          zh-CN: 运行时长
          en-US: Up time
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: jmx
    # the config content when protocol is jmx
    jmx:
      # jmx host: ipv4 ipv6 domain
      host: ^_^host^_^
      # jmx port
      port: ^_^port^_^
      # jmx username
      username: ^_^username^_^
      # jmx password
      password: ^_^password^_^
      # jmx mbean object name
      objectName: java.lang:type=Runtime
      # jmx url
      url: ^_^url^_^

  - name: broker
    i18n:
      zh-CN: Broker 信息
      en-US: Broker Info
    priority: 1
    fields:
      - field: BrokerName
        type: 1
        label: true
        i18n:
          zh-CN: Broker 名称
          en-US: Broker Name
      - field: BrokerVersion
        i18n:
          zh-CN: Broker 版本
          en-US: Broker Version
        type: 1
      - field: Uptime
        i18n:
          zh-CN: 启动时长
          en-US: Uptime
        type: 1
      - field: UptimeMillis
        i18n:
          zh-CN: 启动时长（毫秒）
          en-US: Uptime Millis
        type: 0
        unit: ms
      - field: Persistent
        i18n:
          zh-CN: 持久化
          en-US: Persistent
        type: 1
      - field: DataDirectory
        i18n:
          zh-CN: 数据目录
          en-US: Data Directory
        type: 1
      - field: MemoryPercentUsage
        i18n:
          zh-CN: 内存使用率
          en-US: Memory Percent Usage
        type: 0
        unit: '%'
      - field: StorePercentUsage
        i18n:
          zh-CN: 存储使用率
          en-US: Store Percent Usage
        type: 0
        unit: '%'
      - field: TempPercentUsage
        i18n:
          zh-CN: 临时使用率
          en-US: Temp Percent Usage
        type: 0
        unit: '%'
      - field: CurrentConnectionsCount
        i18n:
          zh-CN: 当前连接数
          en-US: Current Connections Count
        type: 0
      - field: TotalConnectionsCount
        i18n:
          zh-CN: 总连接数
          en-US: Total Connections Count
        type: 0
      - field: TotalEnqueueCount
        i18n:
          zh-CN: 总入队数
          en-US: Total Enqueue Count
        type: 0
      - field: TotalDequeueCount
        i18n:
          zh-CN: 总出队数
          en-US: Total Dequeue Count
        type: 0
      - field: TotalConsumerCount
        i18n:
          zh-CN: 消费者总数
          en-US: Total Consumer Count
        type: 0
      - field: TotalProducerCount
        i18n:
          zh-CN: 生产者总数
          en-US: Total Producer Count
        type: 0
      - field: TotalMessageCount
        i18n:
          zh-CN: 总消息数
          en-US: Total Message Count
        type: 0
      - field: AverageMessageSize
        i18n:
          zh-CN: 平均消息大小
          en-US: Average Message Size
        type: 0
      - field: MaxMessageSize
        i18n:
          zh-CN: 最大消息大小
          en-US: Max Message Size
        type: 0
      - field: MinMessageSize
        i18n:
          zh-CN: 最小消息大小
          en-US: Min Message Size
        type: 0
    protocol: jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      objectName: org.apache.activemq:type=Broker,brokerName=*
      url: ^_^url^_^

  - name: topic
    priority: 1
    fields:
      - field: Name
        type: 1
        label: true
        i18n:
          zh-CN: Topic 名称
          en-US: Topic Name
      - field: MemoryLimit
        type: 0
        unit: MB
        i18n:
          zh-CN: 内存限制
          en-US: Memory Limit
      - field: MemoryPercentUsage
        type: 0
        unit: '%'
        i18n:
          zh-CN: 内存使用率
          en-US: Memory Percent Usage
      - field: ProducerCount
        type: 0
        i18n:
          zh-CN: 生产者数量
          en-US: Producer Count
      - field: ConsumerCount
        type: 0
        i18n:
          zh-CN: 消费者数量
          en-US: Consumer Count
      - field: EnqueueCount
        type: 0
        i18n:
          zh-CN: 入队数量
          en-US: Enqueue Count
      - field: DequeueCount
        type: 0
        i18n:
          zh-CN: 出队数量
          en-US: Dequeue Count
      - field: ForwardCount
        type: 0
        i18n:
          zh-CN: 转发数量
          en-US: Forward Count
      - field: InFlightCount
        type: 0
        i18n:
          zh-CN: 进行中数量
          en-US: In Flight Count
      - field: DispatchCount
        type: 0
        i18n:
          zh-CN: 分发数量
          en-US: Dispatch Count
      - field: ExpiredCount
        type: 0
        i18n:
          zh-CN: 过期数量
          en-US: Expired Count
      - field: StoreMessageSize
        type: 0
        unit: B
        i18n:
          zh-CN: 存储消息大小
          en-US: Store Message Size
      - field: AverageEnqueueTime
        type: 0
        unit: ms
        i18n:
          zh-CN: 平均入队时间
          en-US: Average Enqueue Time
      - field: MaxEnqueueTime
        type: 0
        unit: ms
        i18n:
          zh-CN: 最大入队时间
          en-US: Max Enqueue Time
      - field: MinEnqueueTime
        type: 0
        unit: ms
        i18n:
          zh-CN: 最小入队时间
          en-US: Min Enqueue Time
      - field: TotalBlockedTime
        type: 0
        unit: ms
        i18n:
          zh-CN: 总阻塞时间
          en-US: Total Blocked Time
      - field: AverageMessageSize
        type: 0
        unit: B
        i18n:
          zh-CN: 平均消息大小
          en-US: Average Message Size
      - field: MaxMessageSize
        type: 0
        unit: B
        i18n:
          zh-CN: 最大消息大小
          en-US: Max Message Size
      - field: MinMessageSize
        type: 0
        unit: B
        i18n:
          zh-CN: 最小消息大小
          en-US: Min Message Size
    units:
      - MemoryLimit=B->MB
    protocol: jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      objectName: org.apache.activemq:type=Broker,brokerName=*,destinationType=Topic,destinationName=*
      url: ^_^url^_^

  - name: memory_pool
    i18n:
      zh-CN: 内存池
      en-US: Memory Pool
    priority: 2
    fields:
      - field: name
        type: 1
        label: true
        i18n:
          zh-CN: 指标名称
          en-US: Name
      - field: committed
        type: 0
        unit: MB
        i18n:
          zh-CN: 已分配内存
          en-US: Committed
      - field: init
        type: 0
        unit: MB
        i18n:
          zh-CN: 初始化内存
          en-US: Init
      - field: max
        type: 0
        unit: MB
        i18n:
          zh-CN: 最大内存
          en-US: Max
      - field: used
        type: 0
        unit: MB
        i18n:
          zh-CN: 已使用内存
          en-US: Used
    units:
      - committed=B->MB
      - init=B->MB
      - max=B->MB
      - used=B->MB
    aliasFields:
      - Name
      - Usage->committed
      - Usage->init
      - Usage->max
      - Usage->used
    calculates:
      - name=Name
      - committed=Usage->committed
      - init=Usage->init
      - max=Usage->max
      - used=Usage->used
    protocol: jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      objectName: java.lang:type=MemoryPool,name=*
      url: ^_^url^_^

  - name: class_loading
    i18n:
      zh-CN: 类加载信息
      en-US: Class Loading
    priority: 2
    fields:
      - field: LoadedClassCount
        type: 0
        i18n:
          zh-CN: 当前已加载类数量
          en-US: Loaded Class Count
      - field: TotalLoadedClassCount
        type: 0
        i18n:
          zh-CN: 已加载类总数量
          en-US: Total Loaded Class Count
      - field: UnloadedClassCount
        type: 0
        i18n:
          zh-CN: 未加载类总数量
          en-US: Unloaded Class Count
    protocol: jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      objectName: java.lang:type=ClassLoading
      url: ^_^url^_^

  - name: thread
    i18n:
      zh-CN: 线程信息
      en-US: Thread
    priority: 2
    fields:
      - field: TotalStartedThreadCount
        type: 0
        i18n:
          zh-CN: 已启动线程总数
          en-US: Total Started Thread Count
      - field: ThreadCount
        type: 0
        i18n:
          zh-CN: 活跃线程数
          en-US: Thread Count
      - field: PeakThreadCount
        type: 0
        i18n:
          zh-CN: 最大峰值线程数
          en-US: Peak Thread Count
      - field: DaemonThreadCount
        type: 0
        i18n:
          zh-CN: 活跃守护线程数
          en-US: Daemon Thread Count
      - field: CurrentThreadUserTime
        type: 0
        unit: s
        i18n:
          zh-CN: 线程占用的CPU时间(用户态)
          en-US: Current Thread User Time
      - field: CurrentThreadCpuTime
        type: 0
        unit: s
        i18n:
          zh-CN: 线程占用的CPU时间
          en-US: Current Thread CPU Time
    units:
      - CurrentThreadUserTime=NS->S
      - CurrentThreadCpuTime=NS->S
    protocol: jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      objectName: java.lang:type=Threading
      url: ^_^url^_^
