# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring mid-middleware custom-custom monitoring os-operating system monitoring
# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: mid
# The monitoring type eg: linux windows tomcat mysql aws...
# 监控类型 eg: linux windows tomcat mysql aws...
app: ntp
# 监控类型国际化名称
name:
  zh-CN: NTP服务
  en-US: NTP Server
# The description and help of this monitoring type
# 监控类型的帮助描述信息

params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: NTP服务Host
      en-US: NTP Host
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # field-param field key
  - field: timeout
    # name-param field display i18n name
    name:
      zh-CN: 连接超时时间(ms)
      en-US: Connect Timeout(ms)
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,100000]'
    # required-true or false
    required: true
    # default value 6000
    defaultValue: 6000
# collect metrics config list
metrics:
  # metrics - summary
  - name: summary
    i18n:
      zh-CN: 概要
      en-US: Summary
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
    fields:
      - field: responseTime
        type: 0
        unit: ms
        i18n:
          zh-CN: 响应时间
          en-US: Response Time
      - field: time
        type: 0
        unit: ms
        i18n:
          zh-CN: 时间戳
          en-US: Timestamp
      - field: date
        type: 1
        i18n:
          zh-CN: 时间
          en-US: Date
      - field: offset
        type: 0
        unit: ms
        i18n:
          zh-CN: 偏移量
          en-US: Offset
      - field: delay
        type: 0
        unit: ms
        i18n:
          zh-CN: 延迟
          en-US: Delay
      - field: version
        type: 0
        i18n:
          zh-CN: 版本号
          en-US: Version
      - field: mode
        type: 0
        i18n:
          zh-CN: 模式
          en-US: Mode
      - field: stratum
        type: 0
        i18n:
          zh-CN: 层级
          en-US: Stratum
      - field: referenceId
        type: 1
        i18n:
          zh-CN: 参考ID
          en-US: ReferenceId
      - field: precision
        type: 0
        i18n:
          zh-CN: 精度
          en-US: Precision

    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: ntp
    # Specific collection configuration when protocol is telnet protocol
    ntp:
      # telnet host
      host: ^_^host^_^
      # timeout
      timeout: ^_^timeout^_^
