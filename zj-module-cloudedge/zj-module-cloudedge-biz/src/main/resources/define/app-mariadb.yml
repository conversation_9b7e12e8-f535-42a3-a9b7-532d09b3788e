# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring custom-custom monitoring os-operating system monitoring
# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: db
# The monitoring type eg: linux windows tomcat mysql aws...
# 监控类型 eg: linux windows tomcat mysql aws...
app: mariadb
# The monitoring i18n name
# 监控类型国际化名称
name:
  zh-CN: MariaDB
  en-US: MariaDB

# 监控所需输入参数定义(根据定义渲染页面UI)
# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # field-param field key
  - field: port
    # name-param field display i18n name
    name:
      zh-CN: 端口
      en-US: Port
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    # required-true or false
    required: true
    # default value
    defaultValue: 3306
  - field: timeout
    name:
      zh-CN: 查询超时时间(ms)
      en-US: Query Timeout(ms)
    type: number
    # when type is number, range is required
    range: '[400,200000]'
    required: false
    defaultValue: 6000
    hide: true
  - field: database
    name:
      zh-CN: 数据库名称
      en-US: Database Name
    type: text
    required: false
  - field: username
    name:
      zh-CN: 用户名
      en-US: Username
    type: text
    limit: 50
    required: false
  - field: password
    name:
      zh-CN: 密码
      en-US: Password
    type: password
    required: false
  - field: url
    name:
      zh-CN: URL
      en-US: URL
    type: text
    required: false
    hide: true

# collect metrics config list
metrics:
  # metrics - basic
  - name: basic
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    i18n:
      zh-CN: 基础 信息
      en-US: Basic Info
    # collect metrics content
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      - field: version
        type: 1
        i18n:
          zh-CN: 版本
          en-US: Version
      - field: port
        type: 1
        i18n:
          zh-CN: 端口
          en-US: Port
      - field: datadir
        type: 1
        i18n:
          zh-CN: 存储目录
          en-US: DataDir
      - field: max_connections
        type: 0
        i18n:
          zh-CN: 最大连接数
          en-US: Max Connections
      - field: thread_cache_size
        type: 0
        i18n:
          zh-CN: 连接池大小
          en-US: Thread Cache Size
      - field: innodb_buffer_pool_size
        type: 0
        unit: KB
        i18n:
          zh-CN: InnoDB缓冲池的大小
          en-US: Innodb Buffer Pool Size
    # (optional)metrics field alias name, it is used as an alias field to map and convert the collected data and metrics field
    aliasFields:
      - version
      - version_compile_os
      - version_compile_machine
      - port
      - datadir
      - max_connections
      - thread_cache_size
      - table_open_cache
      - innodb_buffer_pool_size
    # (optional)mapping and conversion expressions, use these and aliasField above to calculate metrics value
    # eg: cores=core1+core2, usage=usage, waitTime=allTime-runningTime
    calculates:
      - port=port
      - datadir=datadir
      - max_connections=max_connections
      - version=version+"_"+version_compile_os+"_"+version_compile_machine
    # Convert the initial unit of the metric to the final unit
    units:
      - innodb_buffer_pool_size=B->KB
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: jdbc
    # the config content when protocol is jdbc
    jdbc:
      # mysql host: ipv4 ipv6 host
      host: ^_^host^_^
      # mysql port
      port: ^_^port^_^
      # database platform name
      platform: mariadb
      # username
      username: ^_^username^_^
      # password
      password: ^_^password^_^
      # database name
      database: ^_^database^_^
      # timeout unit：ms
      timeout: ^_^timeout^_^
      # SQL Query Method：oneRow, multiRow, columns
      queryType: columns
      # sql
      sql: show global variables where Variable_name like 'version%' or Variable_name = 'max_connections' or Variable_name = 'datadir' or Variable_name = 'port' or Variable_name = 'thread_cache_size' or Variable_name = 'table_open_cache' or Variable_name = 'innodb_buffer_pool_size';
      # JDBC url
      url: ^_^url^_^

  - name: cache
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 1
    # collect metrics content
    i18n:
      zh-CN: 缓存 信息
      en-US: Cache Info
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      - field: query_cache_hit_rate
        type: 0
        unit: '%'
        i18n:
          zh-CN: 缓存命中率
          en-US: Query Cache Hit Rate
      - field: cache_hits
        type: 0
        i18n:
          zh-CN: 缓存命中数
          en-US: Cache Hits
      - field: cache_inserts
        type: 0
        i18n:
          zh-CN: 缓存插入数
          en-US: Cache Inserts
      - field: cache_free_blocks
        type: 0
        i18n:
          zh-CN: 缓存空闲块数量
          en-US: Cache Free Blocks
      - field: cache_free_memory
        type: 0
        unit: KB
        i18n:
          zh-CN: 空闲缓存大小
          en-US: Cache Free Memory
    # (optional)metrics field alias name, it is used as an alias field to map and convert the collected data and metrics field
    aliasFields:
      - Qcache_hits
      - Qcache_inserts
      - Qcache_free_blocks
      - Qcache_free_memory
    # mapping and conversion expressions, use these and aliasField above to calculate metrics value
    # eg: cores=core1+core2, usage=usage, waitTime=allTime-runningTime
    calculates:
      - query_cache_hit_rate= (Qcache_hits + 1) / (Qcache_hits + Qcache_inserts + 1) * 100
      - cache_hits=Qcache_hits
      - cache_inserts=Qcache_inserts
      - cache_free_blocks=Qcache_free_blocks
      - cache_free_memory=Qcache_free_memory
    # Convert the initial unit of the metric to the final unit
    units:
      - cache_free_memory=B->KB
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: jdbc
    # the config content when protocol is jdbc
    jdbc:
      # jdbc host
      # jdbchost
      host: ^_^host^_^
      # port
      port: ^_^port^_^
      # platform name
      platform: mariadb
      # username
      username: ^_^username^_^
      # password
      password: ^_^password^_^
      # database name
      database: ^_^database^_^
      # timeout unit：ms
      timeout: ^_^timeout^_^
      # SQL Query Method：oneRow, multiRow, columns
      queryType: columns
      # sql
      sql: show global status like 'QCache%';
      url: ^_^url^_^

  - name: performance
    priority: 2
    i18n:
      zh-CN: 性能 信息
      en-US: Performance Info
    fields:
      - field: questions
        type: 0
        i18n:
          zh-CN: 查询总数
          en-US: Questions
      - field: qps
        type: 0
        i18n:
          zh-CN: 每秒处理查询数
          en-US: Queries Per Second
    aliasFields:
      - uptime
      - questions
    calculates:
      - qps=uptime / questions
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: mariadb
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      queryType: columns
      sql: show global status where Variable_name = 'questions' or Variable_name = 'uptime';
      url: ^_^url^_^

  - name: innodb
    priority: 3
    i18n:
      zh-CN: Innodb 信息
      en-US: Innodb Info
    fields:
      - field: innodb_data_reads
        type: 0
        unit: Times
        i18n:
          zh-CN: 磁盘读取次数
          en-US: Innodb Data Reads
      - field: innodb_data_writes
        type: 0
        unit: Times
        i18n:
          zh-CN: 磁盘写入次数
          en-US: Innodb Data Writes
      - field: innodb_data_read
        type: 0
        unit: KB
        i18n:
          zh-CN: 磁盘读取数据量
          en-US: Innodb Data Read
      - field: innodb_data_written
        type: 0
        unit: KB
        i18n:
          zh-CN: 磁盘写入数据量
          en-US: Innodb Data Written
      - field: innodb_buffer_hit_rate
        type: 0
        unit: '%'
        i18n:
          zh-CN: Innodb 缓存命中数
          en-US: Innodb Buffer Hit Rate
    aliasFields:
      - Innodb_data_reads
      - Innodb_data_writes
      - Innodb_data_read
      - Innodb_data_written
      - Innodb_buffer_pool_read_requests
      - Innodb_buffer_pool_read_ahead
      - Innodb_buffer_pool_reads
    calculates:
      - innodb_buffer_hit_rate= (Innodb_buffer_pool_read_requests + 1) / (Innodb_buffer_pool_read_requests + Innodb_buffer_pool_read_ahead + Innodb_buffer_pool_reads + 1) * 100
      - innodb_data_reads=Innodb_data_reads
      - innodb_data_writes=Innodb_data_writes
      - innodb_data_read=Innodb_data_read
      - innodb_data_written=Innodb_data_written
    units:
      - innodb_data_read=B->KB
      - innodb_data_written=B->KB
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: mariadb
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      queryType: columns
      sql: show global status where Variable_name like 'innodb%';
      url: ^_^url^_^

  - name: status
    priority: 4
    i18n:
      zh-CN: 状态 信息
      en-US: Status Info
    fields:
      - field: uptime
        type: 0
        unit: s
        i18n:
          zh-CN: 运行时间
          en-US: Uptime
      - field: com_select
        type: 0
        i18n:
          zh-CN: 查询次数
          en-US: Com Select
      - field: com_insert
        type: 0
        i18n:
          zh-CN: 插入次数
          en-US: Com Insert
      - field: com_update
        type: 0
        i18n:
          zh-CN: 更新次数
          en-US: Com Update
      - field: com_delete
        type: 0
        i18n:
          zh-CN: 删除次数
          en-US: Com Delete
      - field: com_commit
        type: 0
        i18n:
          zh-CN: 事务提交次数
          en-US: Com Commit
      - field: com_rollback
        type: 0
        i18n:
          zh-CN: 事务回滚次数
          en-US: Com Rollback
      - field: threads_created
        type: 0
        i18n:
          zh-CN: 线程创建数
          en-US: Threads Created
      - field: threads_connected
        type: 0
        i18n:
          zh-CN: 连接线程数
          en-US: Threads Connected
      - field: threads_cached
        type: 0
        i18n:
          zh-CN: 缓存线程数
          en-US: Threads Cached
      - field: threads_running
        type: 0
        i18n:
          zh-CN: 活动线程数
          en-US: Threads Running
      - field: qps
        type: 0
        i18n:
          zh-CN: 每秒处理查询数
          en-US: Com Insert
    aliasFields:
      - uptime
      - com_select
      - com_insert
      - com_update
      - com_delete
      - com_commit
      - com_rollback
      - threads_created
      - threads_connected
      - threads_cached
      - threads_running
      - questions
    calculates:
      - qps=uptime / questions
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: mariadb
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      queryType: columns
      sql: show global status where Variable_name like 'thread%' or Variable_name = 'com_select' or Variable_name = 'com_insert' or Variable_name = 'com_update' or Variable_name = 'com_delete' or Variable_name = 'com_commit' or Variable_name = 'com_rollback' or Variable_name = 'questions' or Variable_name = 'uptime';
      url: ^_^url^_^

  - name: handler
    priority: 5
    i18n:
      zh-CN: 处理器 信息
      en-US: Handler Info
    fields:
      - field: handler_commit
        type: 0
        i18n:
          zh-CN: 处理的事务提交数
          en-US: Handler Commit
      - field: handler_delete
        type: 0
        i18n:
          zh-CN: 处理的删除操作数
          en-US: Handler Delete
      - field: handler_write
        type: 0
        i18n:
          zh-CN: 处理的写入操作数
          en-US: Handler Write
      - field: handler_update
        type: 0
        i18n:
          zh-CN: 处理的更新操作数
          en-US: Handler Update
      - field: handler_discover
        type: 0
        i18n:
          zh-CN: 处理的发现操作数
          en-US: Handler Discover
      - field: handler_prepare
        type: 0
        i18n:
          zh-CN: 处理的准备操作数
          en-US: Handler Prepare
      - field: handler_read_first
        type: 0
        i18n:
          zh-CN: 读取首行操作数
          en-US: Handler Read First
      - field: handler_read_rnd
        type: 0
        i18n:
          zh-CN: 随机读取操作数
          en-US: Handler Read RND
      - field: handler_read_next
        type: 0
        i18n:
          zh-CN: 读取下一行操作数
          en-US: Handler Read Next
      - field: handler_read_rnd_next
        type: 0
        i18n:
          zh-CN: 随机读取下一行操作数
          en-US: Handler Read RND Next
      - field: handler_read_key
        type: 0
        i18n:
          zh-CN: 按索引键读取操作数
          en-US: Handler Read Key
      - field: handler_external_lock
        type: 0
        i18n:
          zh-CN: 外部锁定操作数
          en-US: Handler External Key
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: mariadb
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      queryType: columns
      sql: show global status like 'Handler%';
      url: ^_^url^_^

  - name: connection
    priority: 6
    i18n:
      zh-CN: 连接 信息
      en-US: Connection Info
    fields:
      - field: connections
        type: 0
        i18n:
          zh-CN: 当前连接数
          en-US: Connections
      - field: max_used_connections
        type: 0
        i18n:
          zh-CN: 历史最大连接数
          en-US: Max Used Connections
      - field: aborted_connects
        type: 0
        i18n:
          zh-CN: 失败连接数
          en-US: Aborted Connects
      - field: aborted_clients
        type: 0
        i18n:
          zh-CN: 客户端中断数
          en-US: Aborted Clients
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: mariadb
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      queryType: columns
      sql: show global status;
      url: ^_^url^_^

  - name: thread
    priority: 7
    i18n:
      zh-CN: 线程 信息
      en-US: Thread Info
    fields:
      - field: threads_created
        type: 0
        i18n:
          zh-CN: 创建线程数
          en-US: Threads Created
      - field: threads_connected
        type: 0
        i18n:
          zh-CN: 连接线程数
          en-US: Threads Connected
      - field: threads_cached
        type: 0
        i18n:
          zh-CN: 缓存线程数
          en-US: CThreads Cached
      - field: threads_running
        type: 0
        i18n:
          zh-CN: 运行线程数
          en-US: Threads Running
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: mariadb
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      queryType: columns
      sql: show global status like 'thread%';
      url: ^_^url^_^

  - name: tmp
    priority: 8
    i18n:
      zh-CN: 临时表 信息
      en-US: TMP Info
    fields:
      - field: created_tmp_tables
        type: 0
        i18n:
          zh-CN: 创建的临时表数量
          en-US: Created TMP Tables
      - field: created_tmp_disk_tables
        type: 0
        i18n:
          zh-CN: 创建的临时磁盘表数量
          en-US: Created TMP Disk Tables
      - field: created_tmp_files
        type: 0
        i18n:
          zh-CN: 创建的临时文件数量
          en-US: Created TMP Files
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: mariadb
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      queryType: columns
      sql: show global status where Variable_name like '%tmp%';
      url: ^_^url^_^

  - name: select_type
    priority: 9
    i18n:
      zh-CN: 查询类型 信息
      en-US: Select Type Info
    fields:
      - field: select_full_join
        type: 0
        i18n:
          zh-CN: 全表连接数
          en-US: Select Full Join
      - field: select_full_range_join
        type: 0
        i18n:
          zh-CN: 带范围的全表连接数
          en-US: Select Full Range Join
      - field: select_range
        type: 0
        i18n:
          zh-CN: 索引范围查询数
          en-US: Select Range
      - field: select_range_check
        type: 0
        i18n:
          zh-CN: 索引范围内二次检查数
          en-US: Select Range Check
      - field: select_scan
        type: 0
        i18n:
          zh-CN: 全表扫描查询数
          en-US: Select Scan
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: mariadb
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      queryType: columns
      sql: show global status where Variable_name like 'select%';
      url: ^_^url^_^

  - name: sort
    priority: 10
    i18n:
      zh-CN: 排序 信息
      en-US: Sort Info
    fields:
      - field: sort_rows
        type: 0
        i18n:
          zh-CN: 排序总行数
          en-US: Sort Rows
      - field: sort_range
        type: 0
        i18n:
          zh-CN: 范围条件排序次数
          en-US: Sort Range
      - field: sort_merge_passes
        type: 0
        i18n:
          zh-CN: 合并排序段次数
          en-US: Sort Merge Passes
      - field: sort_scan
        type: 0
        i18n:
          zh-CN: 全表扫描排序次数
          en-US: Sort Scan
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: mariadb
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      queryType: columns
      sql: show global status where Variable_name like 'sort%';
      url: ^_^url^_^

  - name: table_lock
    priority: 11
    i18n:
      zh-CN: 表级锁 信息
      en-US: Table Lock Info
    fields:
      - field: table_locks_immediate
        type: 0
        i18n:
          zh-CN: 立即获得表锁次数
          en-US: Table Locks Immediate
      - field: table_locks_waited
        type: 0
        i18n:
          zh-CN: 须等待获取表锁次数
          en-US: Table Locks Waited
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: mariadb
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      queryType: columns
      sql: show global status where Variable_name like 'table_lock%';
      url: ^_^url^_^

  - name: process_state
    priority: 12
    i18n:
      zh-CN: 进程状态 信息
      en-US: Process State Info
    fields:
      - field: state
        type: 1
        label: true
        i18n:
          zh-CN: 进程状态
          en-US: State
      - field: num
        type: 0
        i18n:
          zh-CN: 该状态进程数量
          en-US: Num
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: mariadb
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      queryType: multiRow
      sql: select state, count(*) as num from information_schema.PROCESSLIST where state != '' group by state;
      url: ^_^url^_^

  - name: slow_sql
    priority: 13
    i18n:
      zh-CN: 慢查询 信息
      en-US: Slow Sql Info
    fields:
      - field: id
        type: 1
        label: true
        i18n:
          zh-CN: ID
          en-US: ID
      - field: sql_text
        type: 1
        i18n:
          zh-CN: SQL 文本
          en-US: SQL Text
      - field: start_time
        type: 1
        i18n:
          zh-CN: 开始时间
          en-US: Start Time
      - field: db
        type: 1
        i18n:
          zh-CN: 数据库
          en-US: Database
      - field: query_time
        type: 3
        i18n:
          zh-CN: 查询时间
          en-US: Query Time
    aliasFields:
      - sql_text
      - start_time
      - db
      - query_time
    calculates:
      - id= start_time + sql_text
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      platform: mariadb
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      timeout: ^_^timeout^_^
      queryType: multiRow
      sql: select sql_text, start_time, db, query_time from mysql.slow_log;
      url: ^_^url^_^
