# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring mid-middleware custom-custom monitoring os-operating system monitoring
category: stored
# The monitoring type eg: linux windows tomcat mysql aws...
app: storage_huawei
# The monitoring i18n name
name:
  zh-CN: 华为存储
  en-US: Huawei Stored
# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # field-param field key
  - field: port
    # name-param field display i18n name
    name:
      zh-CN: 端口
      en-US: Port
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    # required-true or false
    required: true
    # default value
    defaultValue: 161
  # field-param field key
  - field: version
    # name-param field display i18n name
    name:
      zh-CN: SNMP 版本
      en-US: SNMP Version
    # type-param field type(radio mapping the html radio tag)
    type: radio
    # required-true or false
    required: true
    # when type is radio checkbox, use option to show optional values {name1:value1,name2:value2}
    options:
      - label: SNMPv1
        value: 0
      - label: SNMPv2c
        value: 1
  # field-param field key
  - field: community
    # name-param field display i18n name
    name:
      zh-CN: SNMP 团体字
      en-US: SNMP Community
    # type-param field type(most mapping the html input type)
    type: text
    # when type is text, use limit to limit string length
    limit: 100
    # required-true or false
    required: true
    # param field input placeholder
    placeholder: 'Snmp community for v1 v2c'
  # field-param field key
  - field: timeout
    # name-param field display i18n name
    name:
      zh-CN: 超时时间(ms)
      en-US: Timeout(ms)
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,100000]'
    # required-true or false
    required: false
    # hide-is hide this field and put it in advanced layout
    hide: true
    # default value
    defaultValue: 6000
# collect metrics config list
metrics:
  # metrics - system
  - name: system
    i18n:
      zh-CN: 系统
      en-US: System
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    fields:
      - field: sysName
        type: 1
        i18n:
          zh-CN: 系统名称
          en-US: sysName
      - field: status
        type: 1
        i18n:
          zh-CN: 系统状态
          en-US: status

      - field: sysUpTime
        type: 1
        i18n:
          zh-CN: 系统运行时间
          en-US: sysUpTime

      - field: version
        type: 1
        i18n:
          zh-CN: 系统版本
          en-US: version
      - field: deviceId
        type: 1
        i18n:
          zh-CN: 设备ID
          en-US: deviceId
      - field: deviceType
        type: 1
        i18n:
          zh-CN: 设备类型
          en-US: deviceType


      - field: responseTime
        type: 0
        i18n:
          zh-CN: 响应时间
          en-US: Response Time
        label: true
        unit: ms
      - field: sysLocation
        type: 1
        i18n:
          zh-CN: 位置
          en-US: sysLocation



    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: get
      oids:
        sysUpTime: *******.*******.0
        sysName: *******.*******.0
        deviceId: *******.4.1.34774.*******.0
        deviceType: *******.4.1.34774.*******.0
        status: *******.4.1.34774.*******.0
        version: *******.4.1.34774.*******.0
        sysLocation: *******.*******.0



  - name: performance
    i18n:
      zh-CN: 系统性能指标
      en-US: performance
    priority: 2
    fields:
      - field: hwPerfNodeIfIndex
        type: 1
        i18n:
          zh-CN: 节点序号
          en-US: hwPerfNodeIfIndex

      - field: hwPerfNodeCPUUsage
        type: 1
        i18n:
          zh-CN: 节点CPU使用率
          en-US: hwPerfNodeCPUUsage
        label: true
        unit: '%'


      - field: hwPerfNodeAvgCacheUsage
        type: 1
        i18n:
          zh-CN: 节点缓存使用率
          en-US: hwPerfNodeAvgCacheUsage
        unit: '%'
        label: true

      - field: hwPerfNodeTotalIOPS
        type: 1
        i18n:
          zh-CN: 节点总IOPS
          en-US: hwPerfNodeTotalIOPS
        label: true
      - field: hwPerfNodeReadIOPS
        type: 1
        i18n:
          zh-CN: 节点读IOPS
          en-US: hwPerfNodeReadIOPS
        label: true
      - field: hwPerfNodeWriteIOPS
        type: 1
        i18n:
          zh-CN: 节点写IOPS
          en-US: hwPerfNodeWriteIOPS
        label: true


      - field: hwPerfNodeTotalTraffic
        type: 1
        i18n:
          zh-CN: 节点总流量
          en-US: hwPerfNodeTotalTraffic
        label: true
        unit: MB/s
      - field: hwPerfNodeReadTraffic
        type: 1
        i18n:
          zh-CN: 节点读流量
          en-US: hwPerfNodeReadTraffic
        unit: MB/s
        label: true
      - field: hwPerfNodeWriteTraffic
        type: 1
        i18n:
          zh-CN: 节点写流量
          en-US: hwPerfNodeWriteTraffic
        unit: MB/s
        label: true

    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hwPerfNodeIfIndex: *******.4.1.34774.********.1.1
        hwPerfNodeCPUUsage: *******.4.1.34774.********.1.2
        hwPerfNodeAvgCacheUsage: *******.4.1.34774.********.1.3

        hwPerfNodeTotalIOPS: *******.4.1.34774.********.1.5
        hwPerfNodeReadIOPS: *******.4.1.34774.********.1.6
        hwPerfNodeWriteIOPS: *******.4.1.34774.********.1.7
        hwPerfNodeTotalTraffic: *******.4.1.34774.********.1.8
        hwPerfNodeReadTraffic: *******.4.1.34774.********.1.9
        hwPerfNodeWriteTraffic: *******.4.1.34774.********.1.10



  - name: hwInfoStoragePoolEntry
    i18n:
      zh-CN: 磁盘域信息
      en-US: hwInfoStoragePoolEntry
    priority: 3
    fields:
      - field: hwInfoDiskDomainID
        type: 0
        i18n:
          zh-CN: 磁盘域ID
          en-US: hwInfoDiskDomainID
      - field: hwInfoDiskDomainName
        type: 1
        i18n:
          zh-CN: 磁盘域名称
          en-US: hwInfoDiskDomainName
      - field: hwInfoDiskDomainHealthStatus
        type: 0
        i18n:
          zh-CN: 磁盘域健康状态
          en-US: hwInfoDiskDomainHealthStatus

      - field: hwInfoDiskDomainRunningStatus
        type: 0
        i18n:
          zh-CN: 磁盘域运行状态
          en-US: hwInfoDiskDomainRunningStatus

      - field: hwInfoDiskDomainTotalCapacity
        type: 0
        i18n:
          zh-CN: 磁盘域总容量
          en-US: hwInfoDiskDomainTotalCapacity
        unit: GB
      - field: hwInfoDiskDomainFreeCapacity
        type: 0
        i18n:
          zh-CN: 磁盘域可用容量
          en-US: hwInfoDiskDomainFreeCapacity
        unit: GB
      - field: hwInfoDiskDomainHotSpareCapacity
        type: 0
        i18n:
          zh-CN: 磁盘域热备容量
          en-US: hwInfoDiskDomainHotSpareCapacity
        unit: GB
      - field: hwInfoDiskDomainUsedHotSpareCapacity
        type: 0
        i18n:
          zh-CN: 磁盘域已使用热备容量
          en-US: hwInfoDiskDomainUsedHotSpareCapacity
        unit: GB

    units:
      - hwInfoDiskDomainTotalCapacity=MB->GB
      - hwInfoDiskDomainFreeCapacity=MB->GB
      - hwInfoDiskDomainHotSpareCapacity=MB->GB
      - hwInfoDiskDomainUsedHotSpareCapacity=MB->GB

    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hwInfoDiskDomainID: *******.4.1.34774.4.1.23.*******
        hwInfoDiskDomainName: *******.4.1.34774.4.1.23.*******
        hwInfoDiskDomainHealthStatus: *******.4.1.34774.4.1.23.*******
        hwInfoDiskDomainRunningStatus: *******.4.1.34774.********.1.1.4
        hwInfoDiskDomainTotalCapacity: *******.4.1.34774.********.1.1.5
        hwInfoDiskDomainFreeCapacity: *******.4.1.34774.4.1.23.*******
        hwInfoDiskDomainHotSpareCapacity: *******.4.1.34774.********.1.1.7
        hwInfoDiskDomainUsedHotSpareCapacity: *******.4.1.34774.********.1.1.8





  - name: hwStorageFrontEndHostPortTable
    i18n:
      zh-CN: 端口状态信息
      en-US: hwStorageFrontEndHostPortTable
    priority: 4
    fields:
      - field: hwStorageFrontEndHostPortIfIndex
        type: 0
        i18n:
          zh-CN: 端口ID
          en-US: hwStorageFrontEndHostPortIfIndex
      - field: hwStorageFrontEndHostPortType
        type: 0
        i18n:
          zh-CN: 端口类型(0:SAS 1:FC 5:iSCSI)
          en-US: hwStorageFrontEndHostPortType

      - field: hwStorageFrontEndHostPortStatus
        i18n:
          zh-CN: 端口状态(0:down 1:up)
          en-US: hwStorageFrontEndHostPortStatus

        type: 0


      - field: hwStorageFrontEndHostPortDescription
        i18n:
          zh-CN: 端口描述
          en-US: hwStorageFrontEndHostPortDescription

        type: 1
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hwStorageFrontEndHostPortIfIndex: *******.4.1.34774.********.11.1.1
        hwStorageFrontEndHostPortType: *******.4.1.34774.********.11.1.2
        hwStorageFrontEndHostPortStatus: *******.4.1.34774.********.11.1.3
        hwStorageFrontEndHostPortPhysAddress: *******.4.1.34774.********.11.1.4
        hwStorageFrontEndHostPortDescription: *******.4.1.34774.********.11.1.5


  - name: driveInfoTable
    i18n:
      zh-CN: 磁盘组信息
      en-US: driveInfoTable
    priority: 1
    fields:
      - field: hwInfoDiskID
        type: 0
        i18n:
          zh-CN: 磁盘ID
          en-US: hwInfoDiskID
      - field: hwInfoDiskHealthStatus
        i18n:
          zh-CN: 磁盘健康状态(1:Normal 2:Fault)
          en-US: hwInfoDiskHealthStatus
        type: 1
      - field: hwInfoDiskRunningStatus
        i18n:
          zh-CN: 磁盘运行状态(27:Online)
          en-US: hwInfoDiskRunningStatus
        type: 1

      - field: hwInfoDiskLocation
        type: 1
        i18n:
          zh-CN: 磁盘位置
          en-US: hwInfoDiskLocation
      - field: hwInfoDiskType
        type: 1
        i18n:
          zh-CN: 磁盘类型(0:FC 1:SAS 2:SATA 3:SSD)
          en-US: hwInfoDiskType
      - field: hwInfoDiskCapacity
        i18n:
          zh-CN: 磁盘容量
          en-US: hwInfoDiskCapacity
        unit: GB
        type: 1
      - field: hwInfoDiskRole
        i18n:
          zh-CN: 磁盘角色(1:FREE 2:MEMBER 3:SPARE 4:CACHE)
          en-US: hwInfoDiskRole
        type: 1

      - field: hwInfoDiskManufacturer
        i18n:
          zh-CN: 磁盘制造商
          en-US: hwInfoDiskManufacturer

        type: 1
      - field: hwInfoDiskSerialNumber
        i18n:
          zh-CN: 磁盘序列号
          en-US: hwInfoDiskSerialNumber
        type: 1
      - field: hwInfoDiskDiskDomainName
        i18n:
          zh-CN: 磁盘域名称
          en-US: hwInfoDiskDomainName
        type: 1
      - field: hwInfoDiskLifePercentage
        i18n:
          zh-CN: 磁盘寿命百分比
          en-US: hwInfoDiskLifePercentage
        type: 0
        unit: '%'
        label: true
      - field: hwInfoDiskHealthPercentage
        i18n:
          zh-CN: 磁盘健康百分比
          en-US: hwInfoDiskHealthPercentage
        type: 0
        unit: '%'
        label: true
      - field: hwInfoDiskPowerOnHours
        i18n:
          zh-CN: 磁盘通电时间
          en-US: hwInfoDiskPowerOnHours
        type: 0
        unit: 'hours'
      - field: hwInfoDiskSmartStatus
        i18n:
          zh-CN: SMART状态
          en-US: hwInfoDiskSmartStatus
        type: 1
        label: true

    units:
      - hwInfoDiskCapacity=MB->GB

    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hwInfoDiskID: *******.4.1.34774.********.1.1.1
        hwInfoDiskHealthStatus: *******.4.1.34774.********.1.1.2
        hwInfoDiskRunningStatus: *******.4.1.34774.********.1.1.3
        hwInfoDiskLocation: *******.4.1.34774.********.1.1.4
        hwInfoDiskType: *******.4.1.34774.********.1.1.5
        hwInfoDiskCapacity: *******.4.1.34774.********.1.1.6
        hwInfoDiskRole: *******.4.1.34774.********.1.1.7
        hwInfoDiskTemperature: *******.4.1.34774.********.1.1.11
        hwInfoDiskManufacturer: *******.4.1.34774.********.1.1.14
        hwInfoDiskSerialNumber: *******.4.1.34774.********.1.1.15
        hwInfoDiskDiskDomainName: *******.4.1.34774.********.1.1.18
        hwInfoDiskLifePercentage: *******.4.1.34774.********.1.1.19
        hwInfoDiskHealthPercentage: *******.4.1.34774.********.1.1.20
        hwInfoDiskPowerOnHours: *******.4.1.34774.********.1.1.21
        hwInfoDiskSmartStatus: *******.4.1.34774.********.1.1.22





  - name:  hwStorageControllerEntry
    i18n:
      zh-CN: 存储控制器清单
      en-US:  hwStorageControllerEntry
    priority: 1
    fields:
      - field: hwStorageControllerID
        type: 0
        i18n:
          zh-CN: 序号
          en-US: hwStorageControllerID
      - field: hwStorageControllerName
        type: 1
        i18n:
          zh-CN: 控制器名称
          en-US: hwStorageControllerName

      - field: hwStorageControllerHealthStatus
        type: 0
        i18n:
          zh-CN: 控制器健康状态（1:正常 2:故障）
          en-US: hwStorageControllerHealthStatus

      - field: hwStorageControllerRunningStatus
        type: 0
        i18n:
          zh-CN: 控制器运行状态（27:online）
          en-US: hwStorageControllerRunningStatus


      - field: hwStorageControllerIsMaster
        type: 0
        i18n:
          zh-CN: 控制器主备状态（1:主 0:备）
          en-US: hwStorageControllerIsMaster
      - field: hwStorageControllerCPUInfo
        type: 1
        i18n:
          zh-CN: 控制器CPU配置
          en-US: hwStorageControllerCPUInfo


      - field: hwStorageControllerMemorySize
        type: 0
        i18n:
          zh-CN: 控制器内存配置
          en-US: hwStorageControllerMemorySize
        unit: GB


    units:
      - hwStorageControllerMemorySize=MB->GB
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hwStorageControllerID: *******.4.1.34774.********.12.1.1
        hwStorageControllerName: *******.4.1.34774.********.12.1.2
        hwStorageControllerHealthStatus: *******.4.1.34774.********.12.1.4
        hwStorageControllerRunningStatus: *******.4.1.34774.********.12.1.5
        hwStorageControllerIsMaster: *******.4.1.34774.********.12.1.8
        hwStorageControllerMemorySize: *******.4.1.34774.********.12.1.14
        hwStorageControllerCPUInfo: *******.4.1.34774.********.12.1.15



  - name: enclosureStatusTable
    i18n:
      zh-CN: 节点信息
      en-US: enclosureStatusTable
    priority: 4
    fields:
      - field: hwInfoEnclosureID
        type: 0
        i18n:
          zh-CN: 磁盘柜ID
          en-US: hwInfoEnclosureID
      - field: hwInfoEnclosureName
        type: 1
        i18n:
          zh-CN: 磁盘柜名称
          en-US: hwInfoEnclosureName
      - field: hwInfoEnclosureLogicType
        type: 0
        i18n:
          zh-CN: 磁盘柜逻辑类型(0:EXP 1:CTRL 2:DSW 3:MSW 4:SVP)
          en-US: hwInfoEnclosureLogicType
      - field: hwInfoEnclosureHealthStatus
        type: 0
        i18n:
          zh-CN: 磁盘柜健康状态(1:Normal 2:Fault)
          en-US: hwInfoEnclosureHealthStatus
      - field: hwInfoEnclosureRunningStatus
        type: 1
        i18n:
          zh-CN: 磁盘柜运行状态(27:Online)
          en-US: hwInfoEnclosureRunningStatus

      - field: hwInfoEnclosureTemperature
        type: 0
        i18n:
          zh-CN: 磁盘柜温度
          en-US: hwInfoEnclosureTemperature
        unit: ℃
      - field: hwInfoEnclosureSN
        type: 1
        i18n:
          zh-CN: 磁盘柜序列号
          en-US: hwInfoEnclosureSN

    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hwInfoEnclosureID: *******.4.1.34774.********.6.1.1
        hwInfoEnclosureName: *******.4.1.34774.********.6.1.2
        hwInfoEnclosureLogicType: *******.4.1.34774.********.6.1.3
        hwInfoEnclosureHealthStatus: *******.4.1.34774.********.6.1.4
        hwInfoEnclosureRunningStatus: *******.4.1.34774.********.6.1.5

        hwInfoEnclosureTemperature: *******.4.1.34774.********.6.1.8
        hwInfoEnclosureSN: *******.4.1.34774.********.6.1.9


  - name: psuInfoTable
    i18n:
      zh-CN: 电源供应单元信息表
      en-US: PSU Information Table
    priority: 1
    fields:
      - field: hwInfoPowerID
        type: 1
        i18n:
          zh-CN: 电源ID
          en-US: Power ID
      - field: hwInfoPowerHealthStatus
        type: 1
        i18n:
          zh-CN: 电源健康状态(1:Normal 2:Fault)
          en-US: Power Health Status

      - field: hwInfoPowerRunningStatus
        type: 1
        i18n:
          zh-CN: 电源运行状态(27:Online)
          en-US: Power Running Status

      - field: hwInfoPowerType
        type: 1
        i18n:
          zh-CN: 电源类型(0:DC 1:AC 2:HV)
          en-US: Power Type

      - field: hwInfoPowerManufacturer
        type: 1
        i18n:
          zh-CN: 电源制造商
          en-US: Power Manufacturer

      - field: hwInfoPowerModle
        type: 1
        i18n:
          zh-CN: 电源型号
          en-US: Power Model

      - field: hwInfoPowerSerialNumber
        type: 1
        i18n:
          zh-CN: 电源序列号
          en-US: Power Serial Number
      - field: hwInfoPowerInputPower
        type: 0
        i18n:
          zh-CN: 电源输入功率
          en-US: Power Input Power
        unit: 'W'
        label: true
      - field: hwInfoPowerOutputPower
        type: 0
        i18n:
          zh-CN: 电源输出功率
          en-US: Power Output Power
        unit: 'W'
        label: true
      - field: hwInfoPowerConsumption
        type: 0
        i18n:
          zh-CN: 电源功耗
          en-US: Power Consumption
        unit: 'W'
        label: true
      - field: hwInfoPowerEfficiency
        type: 0
        i18n:
          zh-CN: 电源效率
          en-US: Power Efficiency
        unit: '%'
      - field: hwInfoPowerTemperature
        type: 0
        i18n:
          zh-CN: 电源温度
          en-US: Power Temperature
        unit: '°C'
        label: true
      - field: hwInfoPowerRedundantStatus
        type: 1
        i18n:
          zh-CN: 电源冗余状态
          en-US: Power Redundant Status

    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hwInfoPowerID: *******.4.1.34774.********.3.1.1
        hwInfoPowerHealthStatus: *******.4.1.34774.********.3.1.3
        hwInfoPowerRunningStatus: *******.4.1.34774.********.3.1.4
        hwInfoPowerType: *******.4.1.34774.********.3.1.5
        hwInfoPowerManufacturer: *******.4.1.34774.********.3.1.6
        hwInfoPowerModle: *******.4.1.34774.********.3.1.7
        hwInfoPowerSerialNumber: *******.4.1.34774.********.3.1.10
        hwInfoPowerInputPower: *******.4.1.34774.********.3.1.11
        hwInfoPowerOutputPower: *******.4.1.34774.********.3.1.12
        hwInfoPowerConsumption: *******.4.1.34774.********.3.1.13
        hwInfoPowerEfficiency: *******.4.1.34774.********.3.1.14
        hwInfoPowerTemperature: *******.4.1.34774.********.3.1.15
        hwInfoPowerRedundantStatus: *******.4.1.34774.********.3.1.16


  - name: bbuInfoTable
    i18n:
      zh-CN: 电池状态
      en-US: Storages
    priority: 6
    fields:
      - field: hwInfoBBUID
        type: 1
        i18n:
          zh-CN: 电池ID
          en-US: BBU ID

      - field: hwInfoBBUHealthStatus
        type: 0
        i18n:
          zh-CN: 电池健康状态(1:Normal 2:Fault)
          en-US: BBU Health Status

      - field: hwInfoBBURunningStatus
        type: 0
        i18n:
          zh-CN: 电池运行状态(27:Online)
          en-US: BBU Running Status


      - field: hwInfoBBUCurrentVoltage
        type: 0
        i18n:
          zh-CN: 电池当前电压
          en-US: BBU Current Voltage
        unit: V
      - field: hwInfoBBUNumberOfDischarges
        type: 0

        i18n:
          zh-CN: 电池放电次数
          en-US: BBU Number Of Discharges

      - field: hwInfoBBUOwningController
        type: 1
        i18n:
          zh-CN: 电池所属控制器
          en-US: BBU Owning Controller
    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hwInfoBBUID: *******.4.1.34774.********.5.1.1
        hwInfoBBUHealthStatus: *******.4.1.34774.********.5.1.3
        hwInfoBBURunningStatus: *******.4.1.34774.********.5.1.4

        hwInfoBBUCurrentVoltage: *******.4.1.34774.********.5.1.6
        hwInfoBBUNumberOfDischarges: *******.4.1.34774.********.5.1.7
        hwInfoBBUOwningController: *******.4.1.34774.********.5.1.10

    calculates:
      - hwInfoBBUCurrentVoltage=hwInfoBBUCurrentVoltage*0.1


  - name: hwInfoFanTable
    i18n:
      zh-CN: 风扇状态
      en-US: hwInfoFanTable
    priority: 5
    fields:
      - field: hwInfoFanID
        type: 1
        i18n:
          zh-CN: 风扇ID
          en-US: Fan ID

      - field: hwInfoFanHealthStatus
        type: 0
        i18n:
          zh-CN: 风扇健康状态(1:Normal 2:Fault)
          en-US: Fan Health Status

      - field: hwInfoFanRunningStatus
        type: 0
        i18n:
          zh-CN: 风扇运行状态(1:Normal 2:Running)
          en-US: Fan Running Status

      - field: hwInfoFanRunningLevel
        type: 0
        i18n:
          zh-CN: 风扇运行级别(0:Low 1:Normal 2:High)
          en-US: Fan Running Level

    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hwInfoFanID: *******.4.1.34774.********.4.1.1
        hwInfoFanHealthStatus: *******.4.1.34774.********.4.1.3
        hwInfoFanRunningStatus: *******.4.1.34774.********.4.1.4
        hwInfoFanRunningLevel: *******.4.1.34774.********.4.1.5

  # 新增：磁盘寿命/健康度监控
  - name: hwDiskLifeTable
    i18n:
      zh-CN: 磁盘寿命健康度监控
      en-US: Disk Life Health Monitoring
    priority: 1
    fields:
      - field: hwDiskLifeID
        type: 0
        i18n:
          zh-CN: 磁盘ID
          en-US: Disk ID
      - field: hwDiskLifePercentage
        type: 0
        i18n:
          zh-CN: 磁盘寿命百分比
          en-US: Disk Life Percentage
        unit: '%'
        label: true
      - field: hwDiskHealthPercentage
        type: 0
        i18n:
          zh-CN: 磁盘健康百分比
          en-US: Disk Health Percentage
        unit: '%'
        label: true
      - field: hwDiskSmartStatus
        type: 1
        i18n:
          zh-CN: SMART状态
          en-US: SMART Status
        label: true
      - field: hwDiskPowerOnHours
        type: 0
        i18n:
          zh-CN: 磁盘通电时间
          en-US: Disk Power On Hours
        unit: 'hours'
      - field: hwDiskBadSectorCount
        type: 0
        i18n:
          zh-CN: 坏扇区数量
          en-US: Bad Sector Count

    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hwDiskLifeID: *******.4.1.34774.********.1.1.1
        hwDiskLifePercentage: *******.4.1.34774.********.1.1.20
        hwDiskHealthPercentage: *******.4.1.34774.********.1.1.21
        hwDiskSmartStatus: *******.4.1.34774.********.1.1.22
        hwDiskPowerOnHours: *******.4.1.34774.********.1.1.23
        hwDiskBadSectorCount: *******.4.1.34774.********.1.1.24

  # 新增：卷使用率监控
  - name: hwVolumeUsageTable
    i18n:
      zh-CN: 卷使用率监控
      en-US: Volume Usage Monitoring
    priority: 2
    fields:
      - field: hwVolumeID
        type: 0
        i18n:
          zh-CN: 卷ID
          en-US: Volume ID
      - field: hwVolumeName
        type: 1
        i18n:
          zh-CN: 卷名称
          en-US: Volume Name
      - field: hwVolumeTotalCapacity
        type: 0
        i18n:
          zh-CN: 卷总容量
          en-US: Volume Total Capacity
        unit: 'GB'
      - field: hwVolumeUsedCapacity
        type: 0
        i18n:
          zh-CN: 卷已用容量
          en-US: Volume Used Capacity
        unit: 'GB'
      - field: hwVolumeUsagePercentage
        type: 0
        i18n:
          zh-CN: 卷使用率
          en-US: Volume Usage Percentage
        unit: '%'
        label: true
      - field: hwVolumeHealthStatus
        type: 1
        i18n:
          zh-CN: 卷健康状态
          en-US: Volume Health Status
        label: true
      - field: hwVolumeRunningStatus
        type: 1
        i18n:
          zh-CN: 卷运行状态
          en-US: Volume Running Status

    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hwVolumeID: *******.4.1.34774.********.2.1.1
        hwVolumeName: *******.4.1.34774.********.2.1.2
        hwVolumeTotalCapacity: *******.4.1.34774.********.2.1.3
        hwVolumeUsedCapacity: *******.4.1.34774.********.2.1.4
        hwVolumeUsagePercentage: *******.4.1.34774.********.2.1.5
        hwVolumeHealthStatus: *******.4.1.34774.********.2.1.6
        hwVolumeRunningStatus: *******.4.1.34774.********.2.1.7

  # 新增：IOPS性能监控
  - name: hwIopsPerformanceTable
    i18n:
      zh-CN: IOPS性能监控
      en-US: IOPS Performance Monitoring
    priority: 2
    fields:
      - field: hwIopsDeviceID
        type: 0
        i18n:
          zh-CN: 设备ID
          en-US: Device ID
      - field: hwIopsReadPerSecond
        type: 0
        i18n:
          zh-CN: 每秒读取IOPS
          en-US: Read IOPS per Second
        unit: 'IOPS'
        label: true
      - field: hwIopsWritePerSecond
        type: 0
        i18n:
          zh-CN: 每秒写入IOPS
          en-US: Write IOPS per Second
        unit: 'IOPS'
        label: true
      - field: hwIopsTotalPerSecond
        type: 0
        i18n:
          zh-CN: 每秒总IOPS
          en-US: Total IOPS per Second
        unit: 'IOPS'
        label: true
      - field: hwIopsReadLatency
        type: 0
        i18n:
          zh-CN: 读取延迟
          en-US: Read Latency
        unit: 'ms'
      - field: hwIopsWriteLatency
        type: 0
        i18n:
          zh-CN: 写入延迟
          en-US: Write Latency
        unit: 'ms'
      - field: hwIopsReadThroughput
        type: 0
        i18n:
          zh-CN: 读取吞吐量
          en-US: Read Throughput
        unit: 'MB/s'
      - field: hwIopsWriteThroughput
        type: 0
        i18n:
          zh-CN: 写入吞吐量
          en-US: Write Throughput
        unit: 'MB/s'

    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hwIopsDeviceID: *******.4.1.34774.********.1.1.1
        hwIopsReadPerSecond: *******.4.1.34774.********.1.1.2
        hwIopsWritePerSecond: *******.4.1.34774.********.1.1.3
        hwIopsTotalPerSecond: *******.4.1.34774.********.1.1.4
        hwIopsReadLatency: *******.4.1.34774.********.1.1.5
        hwIopsWriteLatency: *******.4.1.34774.********.1.1.6
        hwIopsReadThroughput: *******.4.1.34774.********.1.1.7
        hwIopsWriteThroughput: *******.4.1.34774.********.1.1.8

  # 新增：增强温度监控
  - name: hwTemperatureMonitoringTable
    i18n:
      zh-CN: 温度监控
      en-US: Temperature Monitoring
    priority: 3
    fields:
      - field: hwTempSensorID
        type: 0
        i18n:
          zh-CN: 温度传感器ID
          en-US: Temperature Sensor ID
      - field: hwTempSensorLocation
        type: 1
        i18n:
          zh-CN: 传感器位置
          en-US: Sensor Location
      - field: hwCpuTemperature
        type: 0
        i18n:
          zh-CN: CPU温度
          en-US: CPU Temperature
        unit: '°C'
        label: true
      - field: hwSystemTemperature
        type: 0
        i18n:
          zh-CN: 系统温度
          en-US: System Temperature
        unit: '°C'
        label: true
      - field: hwAmbientTemperature
        type: 0
        i18n:
          zh-CN: 环境温度
          en-US: Ambient Temperature
        unit: '°C'
      - field: hwTempStatus
        type: 1
        i18n:
          zh-CN: 温度状态
          en-US: Temperature Status
        label: true
      - field: hwTempThresholdHigh
        type: 0
        i18n:
          zh-CN: 高温阈值
          en-US: High Temperature Threshold
        unit: '°C'
      - field: hwTempThresholdLow
        type: 0
        i18n:
          zh-CN: 低温阈值
          en-US: Low Temperature Threshold
        unit: '°C'

    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hwTempSensorID: *******.4.1.34774.********.7.1.1
        hwTempSensorLocation: *******.4.1.34774.********.7.1.2
        hwCpuTemperature: *******.4.1.34774.********.7.1.3
        hwSystemTemperature: *******.4.1.34774.********.7.1.4
        hwAmbientTemperature: *******.4.1.34774.********.7.1.5
        hwTempStatus: *******.4.1.34774.********.7.1.6
        hwTempThresholdHigh: *******.4.1.34774.********.7.1.7
        hwTempThresholdLow: *******.4.1.34774.********.7.1.8

  # 新增：风扇增强监控
  - name: hwFanEnhancedTable
    i18n:
      zh-CN: 风扇增强监控
      en-US: Fan Enhanced Monitoring
    priority: 3
    fields:
      - field: hwFanEnhancedID
        type: 0
        i18n:
          zh-CN: 风扇ID
          en-US: Fan ID
      - field: hwFanLocation
        type: 1
        i18n:
          zh-CN: 风扇位置
          en-US: Fan Location
      - field: hwFanSpeed
        type: 0
        i18n:
          zh-CN: 风扇转速
          en-US: Fan Speed
        unit: 'RPM'
        label: true
      - field: hwFanSpeedPercentage
        type: 0
        i18n:
          zh-CN: 风扇转速百分比
          en-US: Fan Speed Percentage
        unit: '%'
        label: true
      - field: hwFanTemperature
        type: 0
        i18n:
          zh-CN: 风扇温度
          en-US: Fan Temperature
        unit: '°C'
      - field: hwFanSerialNumber
        type: 1
        i18n:
          zh-CN: 风扇序列号
          en-US: Fan Serial Number
      - field: hwFanManufacturer
        type: 1
        i18n:
          zh-CN: 风扇制造商
          en-US: Fan Manufacturer
      - field: hwFanModel
        type: 1
        i18n:
          zh-CN: 风扇型号
          en-US: Fan Model

    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hwFanEnhancedID: *******.4.1.34774.********.4.1.1
        hwFanLocation: *******.4.1.34774.********.4.1.6
        hwFanSpeed: *******.4.1.34774.********.4.1.7
        hwFanSpeedPercentage: *******.4.1.34774.********.4.1.8
        hwFanTemperature: *******.4.1.34774.********.4.1.9
        hwFanSerialNumber: *******.4.1.34774.********.4.1.10
        hwFanManufacturer: *******.4.1.34774.********.4.1.11
        hwFanModel: *******.4.1.34774.********.4.1.12

  # 新增：硬件序列号监控
  - name: hwHardwareSerialTable
    i18n:
      zh-CN: 硬件序列号监控
      en-US: Hardware Serial Number Monitoring
    priority: 4
    fields:
      - field: hwHwComponentID
        type: 0
        i18n:
          zh-CN: 硬件组件ID
          en-US: Hardware Component ID
      - field: hwHwComponentType
        type: 1
        i18n:
          zh-CN: 硬件组件类型
          en-US: Hardware Component Type
      - field: hwHwComponentName
        type: 1
        i18n:
          zh-CN: 硬件组件名称
          en-US: Hardware Component Name
      - field: hwHwSerialNumber
        type: 1
        i18n:
          zh-CN: 硬件序列号
          en-US: Hardware Serial Number
      - field: hwHwManufacturer
        type: 1
        i18n:
          zh-CN: 制造商
          en-US: Manufacturer
      - field: hwHwModel
        type: 1
        i18n:
          zh-CN: 型号
          en-US: Model
      - field: hwHwFirmwareVersion
        type: 1
        i18n:
          zh-CN: 固件版本
          en-US: Firmware Version
      - field: hwHwPartNumber
        type: 1
        i18n:
          zh-CN: 部件号
          en-US: Part Number

    protocol: snmp
    snmp:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      community: ^_^community^_^
      version: ^_^version^_^
      operation: walk
      oids:
        hwHwComponentID: *******.4.1.34774.********.8.1.1
        hwHwComponentType: *******.4.1.34774.********.8.1.2
        hwHwComponentName: *******.4.1.34774.********.8.1.3
        hwHwSerialNumber: *******.4.1.34774.********.8.1.4
        hwHwManufacturer: *******.4.1.34774.********.8.1.5
        hwHwModel: *******.4.1.34774.********.8.1.6
        hwHwFirmwareVersion: *******.4.1.34774.********.8.1.7
        hwHwPartNumber: *******.4.1.34774.********.8.1.8