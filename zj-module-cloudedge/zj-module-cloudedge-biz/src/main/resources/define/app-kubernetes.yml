# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring custom-custom monitoring os-operating system monitoring
# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: cn
# The monitoring type eg: linux windows tomcat mysql aws...
# 监控类型 eg: linux windows tomcat mysql aws...
app: kubernetes
# The monitoring i18n name
# 监控类型国际化名称
name:
  zh-CN: Kubernetes
  en-US: Kubernetes
# The description and help of this monitoring type
# 监控类型的帮助描述信息


# 监控所需输入参数定义(根据定义渲染页面UI)
# Input params define for monitoring(render web ui by the definition)
params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # field-param field key
  - field: port
    # name-param field display i18n name
    name:
      zh-CN: ApiServer端口
      en-US: ApiServer Port
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    # required-true or false
    required: true
    # default value
    defaultValue: 6443
  # field-param field key
  - field: authType
    # name-param field display i18n name
    name:
      zh-CN: 认证方式
      en-US: Auth Type
    # type-param field type(radio mapping the html radio tag)
    type: radio
    # required-true or false
    required: true
    # when type is radio checkbox, use option to show optional values {name1:value1,name2:value2}
    options:
      - label: Bearer Token
        value: Bearer Token
    defaultValue: Bearer Token
  - field: token
    name:
      zh-CN: 认证Token
      en-US: Access Token
    type: text
    required: true
# collect metrics config list
metrics:
  # metrics - nodes
  - name: nodes
    i18n:
      zh-CN: 节点信息
      en-US: nodes
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    # collect metrics content
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      - field: node_name
        type: 1
        i18n:
          zh-CN: 节点名称
          en-US: Node Name
      - field: is_ready
        type: 1
        i18n:
          zh-CN: 节点就绪状态
          en-US: Node Ready Status
      - field: capacity_cpu
        type: 0
        i18n:
          zh-CN: CPU 容量
          en-US: CPU Capacity
      - field: allocatable_cpu
        type: 0
        i18n:
          zh-CN: 可分配 CPU
          en-US: Allocatable CPU
      - field: capacity_memory
        type: 0
        unit: Mi
        i18n:
          zh-CN: 内存容量
          en-US: Memory Capacity
      - field: allocatable_memory
        type: 0
        unit: Mi
        i18n:
          zh-CN: 可分配内存
          en-US: Allocatable Memory
      - field: creation_time
        type: 1
        i18n:
          zh-CN: 创建时间
          en-US: Creation Time
    # (optional)metrics field alias name, it is used as an alias field to map and convert the collected data and metrics field
    aliasFields:
      - $.metadata.name
      - $.status.conditions[?(@.type=='Ready')].status
      - $.status.capacity.cpu
      - $.status.capacity.memory
      - $.status.allocatable.cpu
      - $.status.allocatable.memory
      - $.metadata.creationTimestamp
    # (optional)mapping and conversion expressions, use these and aliasField above to calculate metrics value
    # eg: cores=core1+core2, usage=usage, waitTime=allTime-runningTime
    calculates:
      - node_name=$.metadata.name
      - is_ready=$.status.conditions[?(@.type=='Ready')].status
      - capacity_cpu=$.status.capacity.cpu
      - allocatable_cpu=$.status.allocatable.cpu
      - capacity_memory=$.status.capacity.memory
      - allocatable_memory=$.status.allocatable.memory
      - creation_time=$.metadata.creationTimestamp
    # (optional)field unit mapping and conversion expressions, origin unit -> final unit
    units:
      - capacity_memory=Ki->Mi
      - allocatable_memory=Ki->Mi
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: /api/v1/nodes
      method: GET
      ssl: true
      authorization:
        type: ^_^authType^_^
        bearerTokenToken: ^_^token^_^
      parseType: jsonPath
      parseScript: '$.items.*'

  - name: namespaces
    i18n:
      zh-CN: 命名空间
      en-US: namespaces
    priority: 1
    fields:
      - field: namespace
        type: 1
        i18n:
          zh-CN: 命名空间
          en-US: Namespace
      - field: status
        type: 1
        i18n:
          zh-CN: 状态
          en-US: Status
      - field: creation_time
        type: 1
        i18n:
          zh-CN: 创建时间
          en-US: Creation Time
    aliasFields:
      - $.metadata.name
      - $.status.phase
      - $.metadata.creationTimestamp
    calculates:
      - namespace=$.metadata.name
      - status=$.status.phase
      - creation_time=$.metadata.creationTimestamp
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: /api/v1/namespaces
      method: GET
      ssl: true
      authorization:
        type: ^_^authType^_^
        bearerTokenToken: ^_^token^_^
      parseType: jsonPath
      parseScript: '$.items.*'

  - name: pods
    i18n:
      zh-CN: 最小单元
      en-US: pods
    priority: 2
    fields:
      - field: pod
        type: 1
        i18n:
          zh-CN: Pod名称
          en-US: Pod Name
      - field: namespace
        type: 1
        i18n:
          zh-CN: 命名空间
          en-US: Namespace
      - field: status
        type: 1
        i18n:
          zh-CN: 状态
          en-US: Status
      - field: restart
        type: 1
        i18n:
          zh-CN: 重启次数
          en-US: Restart Count
      - field: host_ip
        type: 1
        i18n:
          zh-CN: 主机IP
          en-US: Host IP
      - field: pod_ip
        type: 1
        i18n:
          zh-CN: Pod IP
          en-US: Pod IP
      - field: creation_time
        type: 1
        i18n:
          zh-CN: 创建时间
          en-US: Creation Time
      - field: start_time
        type: 1
        i18n:
          zh-CN: 启动时间
          en-US: Start Time
    aliasFields:
      - $.metadata.name
      - $.metadata.namespace
      - $.status.phase
      - $.spec.restartPolicy
      - $.status.hostIP
      - $.status.podIP
      - $.metadata.creationTimestamp
      - $.status.startTime
    calculates:
      - pod=$.metadata.name
      - namespace=$.metadata.namespace
      - status=$.status.phase
      - restart=$.spec.restartPolicy
      - host_ip=$.status.hostIP
      - pod_ip=$.status.podIP
      - creation_time=$.metadata.creationTimestamp
      - start_time=$.status.startTime
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: /api/v1/pods
      method: GET
      ssl: true
      authorization:
        type: ^_^authType^_^
        bearerTokenToken: ^_^token^_^
      parseType: jsonPath
      parseScript: '$.items.*'

  - name: services
    i18n:
      zh-CN: 服务信息
      en-US: services
    priority: 3
    fields:
      - field: service
        type: 1
        i18n:
          zh-CN: 服务
          en-US: Service
      - field: namespace
        type: 1
        i18n:
          zh-CN: 命名空间
          en-US: Namespace
      - field: type
        type: 1
        i18n:
          zh-CN: 类型
          en-US: Type
      - field: cluster_ip
        type: 1
        i18n:
          zh-CN: 集群IP
          en-US: Cluster IP
      - field: selector
        type: 1
        i18n:
          zh-CN: 选择器
          en-US: Selector
      - field: creation_time
        type: 1
        i18n:
          zh-CN: 创建时间
          en-US: Creation Time
    aliasFields:
      - $.metadata.name
      - $.metadata.namespace
      - $.spec.type
      - $.spec.clusterIP
      - $.spec.selector
      - $.metadata.creationTimestamp
    calculates:
      - service=$.metadata.name
      - namespace=$.metadata.namespace
      - type=$.spec.type
      - cluster_ip=$.spec.clusterIP
      - selector=$.spec.selector
      - creation_time=$.metadata.creationTimestamp
    protocol: http
    http:
      host: ^_^host^_^
      port: ^_^port^_^
      url: /api/v1/services
      method: GET
      ssl: true
      authorization:
        type: ^_^authType^_^
        bearerTokenToken: ^_^token^_^
      parseType: jsonPath
      parseScript: '$.items.*'
