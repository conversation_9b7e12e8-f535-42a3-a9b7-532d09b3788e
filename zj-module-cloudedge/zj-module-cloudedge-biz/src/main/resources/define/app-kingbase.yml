category: db
app: kingbase
name:
  zh-CN: 人大金仓数据库
params:
  - field: host
    name:
      zh-CN: 目标Host
      en-US: Target Host
      ja-JP: 目標ホスト
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # field-param field key
  - field: port
    # name-param field display i18n name
    name:
      zh-CN: 端口
      en-US: Port
      ja-JP: ポート
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    # required-true or false
    required: true
    # default value
    defaultValue: 5432
  - field: timeout
    name:
      zh-CN: 查询超时时间(ms)
      en-US: Query Timeout(ms)
      ja-JP: クエリタイムアウト（ｍｓ）
    type: number
    range: '[400,200000]'
    required: false
    hide: true
    defaultValue: 6000
  - field: database
    name:
      zh-CN: 数据库名称
      en-US: Database Name
      ja-JP: データベース名
    type: text
    defaultValue: kingbase
    required: false
  - field: username
    name:
      zh-CN: 用户名
      en-US: Username
      ja-JP: ユーザー名
    type: text
    limit: 50
    required: false
  - field: password
    name:
      zh-CN: 密码
      en-US: Password
      ja-JP: パスワード
    type: password
    required: false
  - field: url
    name:
      zh-CN: URL
      en-US: URL
      ja-JP: URL
    type: text
    required: false
    hide: true

# collect metrics config list
metrics:
  # metrics - basic
  - name: basic
    i18n:
      zh-CN: 基本信息
      en-US: Basic Info
      ja-JP: 基礎情報
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    # collect metrics content
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      - field: server_version
        type: 1
        label: true
        i18n:
          zh-CN: 服务器版本
          en-US: Server Version
          ja-JP: バージョン
      - field: port
        type: 1
        i18n:
          zh-CN: 端口
          en-US: Port
          ja-JP: ポート
      - field: server_encoding
        type: 1
        i18n:
          zh-CN: 服务器编码
          en-US: Server Encoding
          ja-JP: サーバーのエンコード
      - field: data_directory
        type: 1
        i18n:
          zh-CN: 数据目录
          en-US: Data Directory
          ja-JP: データディレクトリ
      - field: max_connections
        type: 0
        i18n:
          zh-CN: 最大连接数
          en-US: Max Connections
          ja-JP: 最大接続数
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: jdbc
    # the config content when protocol is jdbc
    jdbc:
      # host: ipv4 ipv6 host
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      # database platform name
      platform: postgresql
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      # SQL Query Method：oneRow, multiRow, columns
      queryType: columns
      # sql
      sql: select name, setting as value from pg_settings where name = 'max_connections' or name = 'server_version' or name = 'server_encoding' or name = 'port' or name = 'data_directory';
      # JDBC url
      url: ^_^url^_^

  - name: state
    i18n:
      zh-CN: 状态信息
      en-US: State Info
      ja-JP: 状態情報
    priority: 1
    fields:
      - field: db_name
        type: 1
        label: true
        i18n:
          zh-CN: 数据库名称
          en-US: Database Name
          ja-JP: データベース名
      - field: conflicts
        type: 0
        unit: times
        i18n:
          zh-CN: 冲突次数
          en-US: Conflicts
          ja-JP: コンフリクト回数
      - field: deadlocks
        type: 0
        unit: times
        i18n:
          zh-CN: 死锁次数
          en-US: Deadlocks
          ja-JP: デッドロック回数
      - field: blks_read
        type: 0
        unit: blocks per second
        i18n:
          zh-CN: 读取块
          en-US: Blocks Read
          ja-JP: 読み取られたブロック
      - field: blks_hit
        type: 0
        unit: blocks per second
        i18n:
          zh-CN: 命中块
          en-US: Blocks Hit
          ja-JP: ヒットブロック
      - field: blk_read_time
        type: 0
        unit: ms
        i18n:
          zh-CN: 读取时间
          en-US: Read Time
          ja-JP: 読み取られタイム
      - field: blk_write_time
        type: 0
        unit: ms
        i18n:
          zh-CN: 写入时间
          en-US: Write Time
          ja-JP: 書き込まれ時間
      - field: stats_reset
        type: 1
        i18n:
          zh-CN: 统计重置
          en-US: Stats Reset
          ja-JP: 統計リセット
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      platform: postgresql
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      queryType: multiRow
      sql: SELECT COALESCE(datname,'shared-object') as db_name, conflicts, deadlocks, blks_read, blks_hit, blk_read_time, blk_write_time, stats_reset from pg_stat_database where (datname != 'template1' and datname != 'template0') or datname is null;
      url: ^_^url^_^

  - name: activity
    i18n:
      zh-CN: 活动信息
      en-US: Activity Info
      ja-JP: 活動情報
    priority: 2
    fields:
      - field: running
        type: 0
        unit: sbc
        i18n:
          zh-CN: 运行中
          en-US: Running
          ja-JP: 実行中
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      platform: postgresql
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      queryType: oneRow
      sql: SELECT count(*) as running FROM pg_stat_activity WHERE NOT pid=pg_backend_pid();
      url: ^_^url^_^

  - name: resource_config
    i18n:
      zh-CN: 资源配置
      en-US: Resource Config
      ja-JP: リソース設定
    priority: 3
    fields:
      - field: work_mem
        type: 0
        unit: MB
        i18n:
          zh-CN: 工作内存
          en-US: Work Memory
          ja-JP: ワークメモリ
      - field: shared_buffers
        type: 0
        unit: MB
        i18n:
          zh-CN: 共享缓冲区
          en-US: Shared Buffers
          ja-JP: 共有バッファ
      - field: autovacuum
        type: 1
        i18n:
          zh-CN: 自动清理
          en-US: Auto Vacuum
          ja-JP: オートバキューム
      - field: max_connections
        type: 0
        i18n:
          zh-CN: 最大连接数
          en-US: Max Connections
          ja-JP: 最大接続数
      - field: effective_cache_size
        type: 0
        unit: MB
        i18n:
          zh-CN: 有效缓存大小
          en-US: Effective Cache Size
          ja-JP: キャッシュサイズ
      - field: wal_buffers
        type: 0
        unit: MB
        i18n:
          zh-CN: WAL缓冲区
          en-US: WAL Buffers
          ja-JP: WALバッファ
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      platform: postgresql
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      queryType: columns
      sql: show all;
      url: ^_^url^_^

  - name: connection
    i18n:
      zh-CN: 连接信息
      en-US: Connection Info
      ja-JP: 接続情報
    priority: 4
    fields:
      - field: active
        type: 0
        i18n:
          zh-CN: 活动连接
          en-US: Active Connection
          ja-JP: 活躍的な接続
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      platform: postgresql
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      queryType: oneRow
      sql: select count(1) as active from pg_stat_activity;
      url: ^_^url^_^

  - name: connection_state
    i18n:
      zh-CN: 连接状态
      en-US: Connection State
      ja-JP: 接続状態
    priority: 5
    fields:
      - field: state
        type: 1
        label: true
        i18n:
          zh-CN: 状态
          en-US: State
          ja-JP: 状態
      - field: num
        type: 0
        i18n:
          zh-CN: 数量
          en-US: Num
          ja-JP: 数量
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      platform: postgresql
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      queryType: multiRow
      sql: select COALESCE(state, 'other') as state, count(*) as num from pg_stat_activity group by state;
      url: ^_^url^_^

  - name: connection_db
    i18n:
      zh-CN: 连接数据库
      en-US: Connection Db
      ja-JP: 接続データベース
    priority: 6
    fields:
      - field: db_name
        type: 1
        label: true
        i18n:
          zh-CN: 数据库名称
          en-US: Database Name
          ja-JP: データベース名
      - field: active
        type: 0
        i18n:
          zh-CN: 活动连接
          en-US: Active Connection
          ja-JP: 活躍的な接続
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      platform: postgresql
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      queryType: multiRow
      sql: select count(*) as active, COALESCE(datname, 'other') as db_name from pg_stat_activity group by datname;
      url: ^_^url^_^

  - name: tuple
    i18n:
      zh-CN: 元组信息
      en-US: Tuple Info
      ja-JP: 組情報
    priority: 7
    fields:
      - field: fetched
        type: 0
        i18n:
          zh-CN: 获取次数
          en-US: Fetched
          ja-JP: フェッチ回数
      - field: returned
        type: 0
        i18n:
          zh-CN: 返回次数
          en-US: Returned
          ja-JP: 戻る回数
      - field: inserted
        type: 0
        i18n:
          zh-CN: 插入次数
          en-US: Inserted
          ja-JP: インサート回数
      - field: updated
        type: 0
        i18n:
          zh-CN: 更新次数
          en-US: Updated
          ja-JP: 更新回数
      - field: deleted
        type: 0
        i18n:
          zh-CN: 删除次数
          en-US: Deleted
          ja-JP: 削除回数
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      platform: postgresql
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      queryType: multiRow
      sql: select sum(tup_fetched) as fetched, sum(tup_updated) as updated, sum(tup_deleted) as deleted, sum(tup_inserted) as inserted, sum(tup_returned) as returned from pg_stat_database;
      url: ^_^url^_^

  - name: temp_file
    i18n:
      zh-CN: 临时文件
      en-US: Temp File
      ja-JP: 一時ファイル
    priority: 8
    fields:
      - field: db_name
        type: 1
        label: true
        i18n:
          zh-CN: 数据库名称
          en-US: Database Name
          ja-JP: データベース名
      - field: num
        type: 0
        i18n:
          zh-CN: 次数
          en-US: Num
          ja-JP: 数量
      - field: size
        type: 0
        unit: B
        i18n:
          zh-CN: 大小
          en-US: Size
          ja-JP: サイズ
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      platform: postgresql
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      queryType: multiRow
      sql: select COALESCE(datname, 'other') as db_name, sum(temp_files) as num, sum(temp_bytes) as size from pg_stat_database group by datname;
      url: ^_^url^_^

  - name: lock
    i18n:
      zh-CN: 锁信息
      en-US: Lock Info
      ja-JP: ロック情報
    priority: 9
    fields:
      - field: db_name
        type: 1
        label: true
        i18n:
          zh-CN: 数据库名称
          en-US: Database Name
          ja-JP: データベース名
      - field: conflicts
        type: 0
        unit: times
        i18n:
          zh-CN: 冲突次数
          en-US: Conflicts
          ja-JP: コンフリクト回数
      - field: deadlocks
        type: 0
        unit: times
        i18n:
          zh-CN: 死锁次数
          en-US: Deadlocks
          ja-JP: デッドロック回数
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      platform: postgresql
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      queryType: multiRow
      sql: SELECT COALESCE(datname,'shared-object') as db_name, conflicts, deadlocks from pg_stat_database where (datname != 'template1' and datname != 'template0') or datname is null;
      url: ^_^url^_^

  - name: slow_sql
    i18n:
      zh-CN: 慢查询
      en-US: Slow Sql
      ja-JP: スローSQL
    priority: 10
    fields:
      - field: sql_text
        type: 1
        label: true
        i18n:
          zh-CN: SQL语句
          en-US: SQL Text
          ja-JP: SQL文のテキスト
      - field: calls
        type: 0
        i18n:
          zh-CN: 调用次数
          en-US: Calls
          ja-JP: コール回数
      - field: rows
        type: 0
        i18n:
          zh-CN: 行数
          en-US: Rows
          ja-JP: 行
      - field: avg_time
        type: 0
        unit: ms
        i18n:
          zh-CN: 平均时间
          en-US: Avg Time
          ja-JP: 平均時間
      - field: total_time
        type: 0
        unit: ms
        i18n:
          zh-CN: 总时间
          en-US: Total Time
          ja-JP: 合計時間
    aliasFields:
      - query
      - calls
      - rows
      - total_exec_time
      - mean_exec_time
    calculates:
      - sql_text=query
      - avg_time=mean_exec_time
      - total_time=total_exec_time
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      platform: postgresql
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      queryType: multiRow
      sql: select * from pg_stat_statements;
      url: ^_^url^_^

  - name: transaction
    i18n:
      zh-CN: 事务信息
      en-US: Transaction Info
      ja-JP: トランザクション情報
    priority: 12
    fields:
      - field: db_name
        type: 1
        label: true
        i18n:
          zh-CN: 数据库名称
          en-US: Database Name
          ja-JP: データベース名
      - field: commits
        type: 0
        unit: times
        i18n:
          zh-CN: 提交次数
          en-US: Commits
          ja-JP: コミット回数
      - field: rollbacks
        type: 0
        unit: times
        i18n:
          zh-CN: 回滚次数
          en-US: Rollbacks
          ja-JP: ロールバック回数
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      platform: postgresql
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      queryType: multiRow
      sql: select COALESCE(datname, 'other') as db_name, sum(xact_commit) as commits, sum(xact_rollback) as rollbacks from pg_stat_database group by datname;
      url: ^_^url^_^

  - name: conflicts
    i18n:
      zh-CN: 冲突信息
      en-US: Conflicts Info
      ja-JP: コンフリクト情報
    priority: 13
    fields:
      - field: db_name
        type: 1
        label: true
        i18n:
          zh-CN: 数据库名称
          en-US: Database Name
          ja-JP: データベース名
      - field: tablespace
        type: 0
        i18n:
          zh-CN: 表空间
          en-US: Tablespace
          ja-JP: 表領域
      - field: lock
        type: 0
        i18n:
          zh-CN: 锁
          en-US: Lock
          ja-JP: ロック
      - field: snapshot
        type: 0
        i18n:
          zh-CN: 快照
          en-US: Snapshot
          ja-JP: スナップショット
      - field: bufferpin
        type: 0
        i18n:
          zh-CN: 缓冲区
          en-US: Bufferpin
          ja-JP: バッファ
      - field: deadlock
        type: 0
        i18n:
          zh-CN: 死锁
          en-US: Deadlock
          ja-JP: デッドロック
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      platform: postgresql
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      queryType: multiRow
      sql: select datname as db_name, confl_tablespace as tablespace, confl_lock as lock, confl_snapshot as snapshot, confl_bufferpin as bufferpin, confl_deadlock as deadlock from pg_stat_database_conflicts;
      url: ^_^url^_^

  - name: cache_hit_ratio
    i18n:
      zh-CN: 缓存命中率
      en-US: Cache Hit Ratio
      ja-JP: キャッシュ命中率
    priority: 14
    fields:
      - field: db_name
        type: 1
        label: true
        i18n:
          zh-CN: 数据库名称
          en-US: Database Name
          ja-JP: データベース名
      - field: ratio
        type: 0
        unit: '%'
        i18n:
          zh-CN: 命中率
          en-US: Hit Ratio
          ja-JP: 命中率
    aliasFields:
      - blks_hit
      - blks_read
      - db_name
    calculates:
      - ratio=(blks_hit + 1) / (blks_read + blks_hit + 1) * 100
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      platform: postgresql
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      queryType: multiRow
      sql: select datname as db_name, blks_hit, blks_read from pg_stat_database;
      url: ^_^url^_^

  - name: checkpoint
    i18n:
      zh-CN: Checkpoint信息
      en-US: Checkpoint Info
      ja-JP: チェックポイント情報
    priority: 15
    fields:
      - field: checkpoint_sync_time
        type: 0
        unit: ms
        i18n:
          zh-CN: Checkpoint同步时间
          en-US: Checkpoint Sync Time
          ja-JP: チェックポイント同期時間
      - field: checkpoint_write_time
        type: 0
        unit: ms
        i18n:
          zh-CN: Checkpoint写入时间
          en-US: Checkpoint Write Time
          ja-JP: Checkpoint書き込まれた時間
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      platform: postgresql
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      queryType: oneRow
      sql: select checkpoint_sync_time, checkpoint_write_time from pg_stat_bgwriter;
      url: ^_^url^_^

  - name: buffer
    i18n:
      zh-CN: Buffer信息
      en-US: Buffer Info
      ja-JP: バッファ情報
    priority: 16
    fields:
      - field: allocated
        type: 0
        i18n:
          zh-CN: 已分配
          en-US: Allocated
          ja-JP: 割り当てバッファ
      - field: fsync_calls_by_backend
        type: 0
        i18n:
          zh-CN: 后端进程直接执行的文件同步调用次数
          en-US: Fsync Calls By Backend
          ja-JP: バックエンド同期コール回数
      - field: written_directly_by_backend
        type: 0
        i18n:
          zh-CN: 后台写入到数据文件
          en-US: Written Directly By Backend
          ja-JP: バックエンドによる直接書き込まれたファイル
      - field: written_by_background_writer
        type: 0
        i18n:
          zh-CN: 后台写入
          en-US: Written By Background Writer
          ja-JP: バックグラウンドライターに書き込まれた
      - field: written_during_checkpoints
        type: 0
        i18n:
          zh-CN: 检查点期间写入
          en-US: Written During Checkpoints
          ja-JP: チェックポイント中の書き込み
    protocol: jdbc
    jdbc:
      host: ^_^host^_^
      port: ^_^port^_^
      timeout: ^_^timeout^_^
      platform: postgresql
      username: ^_^username^_^
      password: ^_^password^_^
      database: ^_^database^_^
      queryType: oneRow
      sql: select buffers_alloc as allocated, buffers_backend_fsync as fsync_calls_by_backend, buffers_backend as written_directly_by_backend, buffers_clean as written_by_background_writer, buffers_checkpoint as written_during_checkpoints from pg_stat_bgwriter;
      url: ^_^url^_^
