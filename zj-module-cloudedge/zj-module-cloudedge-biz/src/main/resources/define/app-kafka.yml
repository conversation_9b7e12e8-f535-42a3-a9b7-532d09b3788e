# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The monitoring type category：service-application service monitoring db-database monitoring custom-custom monitoring os-operating system monitoring
# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: mid
# The monitoring type eg: linux windows tomcat mysql aws...
# 监控类型 eg: linux windows tomcat mysql aws...
app: kafka
# The monitoring i18n name
# 监控类型国际化名称
name:
  zh-CN: Kafka消息系统
  en-US: Kafka Message
# The description and help of this monitoring type
# 监控类型的帮助描述信息

# Input params define for monitoring(render web ui by the definition)
# 监控所需输入参数定义(根据定义渲染页面UI)
params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  - field: port
    name:
      zh-CN: 端口
      en-US: Port
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    required: true
    defaultValue: 9999
  - field: url
    name:
      zh-CN: JMX URL
      en-US: JMX URL
    type: text
    required: false
    hide: true
    placeholder: 'service:jmx:rmi:///jndi/rmi://host:port/jmxrmi'
  - field: username
    name:
      zh-CN: 用户名
      en-US: Username
    type: text
    limit: 50
    required: false
    hide: true
  - field: password
    name:
      zh-CN: 密码
      en-US: Password
    type: password
    required: false
    hide: true
# collect metrics config list
metrics:
  # metrics - server_info
  - name: server_info
    i18n:
      zh-CN: 服务器信息
      en-US: Server Info
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    # collect metrics content
    fields:
      # field-metric name, type-metric type(0-number,1-string), unit-metric unit('%','ms','MB'), label-whether it is a metrics label field
      - field: Version
        type: 1
        i18n:
          zh-CN: 版本
          en-US: Version
      - field: StartTimeMs
        type: 1
        i18n:
          zh-CN: 启动时间
          en-US: Start Time
      - field: CommitId
        type: 1
        i18n:
          zh-CN: CommitId
          en-US: CommitId
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: jmx
    # the config content when protocol is jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      # jmx mbean object name
      objectName: kafka.server:type=app-info,id=*
      # jmx url
      url: ^_^url^_^

  - name: jvm_basic
    i18n:
      zh-CN: 虚拟机基础信息
      en-US: JVM Basic
    priority: 1
    fields:
      - field: VmName
        type: 1
        i18n:
          zh-CN: 名称
          en-US: Vm Name
      - field: VmVendor
        type: 1
        i18n:
          zh-CN: 厂商
          en-US: Vm Vendor
      - field: VmVersion
        type: 1
        i18n:
          zh-CN: 版本
          en-US: Vm Version
      - field: Uptime
        type: 0
        unit: ms
        i18n:
          zh-CN: 运行时长
          en-US: Up time
    protocol: jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      # jmx mbean object name
      objectName: java.lang:type=Runtime
      url: ^_^url^_^

  - name: jvm_memory_pool
    i18n:
      zh-CN: 内存池
      en-US: Memory Pool
    priority: 2
    fields:
      - field: name
        type: 1
        label: true
        i18n:
          zh-CN: 指标名称
          en-US: Name
      - field: committed
        type: 0
        i18n:
          zh-CN: 已分配内存
          en-US: Committed
      - field: init
        type: 0
        i18n:
          zh-CN: 初始化内存
          en-US: Init
      - field: max
        type: 0
        i18n:
          zh-CN: 最大内存
          en-US: Max
      - field: used
        type: 0
        i18n:
          zh-CN: 已使用内存
          en-US: Used
    aliasFields:
      - Name
      - Usage->committed
      - Usage->init
      - Usage->max
      - Usage->used
    calculates:
      - name=Name
      - committed=Usage->committed
      - init=Usage->init
      - max=Usage->max
      - used=Usage->used
    protocol: jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      objectName: java.lang:type=MemoryPool,name=*
      url: ^_^url^_^

  - name: active_controller_count
    i18n:
      zh-CN: 活跃控制器数量
      en-US: Active Controller Count
    priority: 3
    fields:
      - field: Value
        type: 0
        i18n:
          zh-CN: 值
          en-US: Value
    protocol: jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      # jmx mbean object name
      objectName: kafka.controller:type=KafkaController,name=ActiveControllerCount
      url: ^_^url^_^

  - name: broker_partition_count
    i18n:
      zh-CN: Broker分区数量
      en-US: Broker Partition Count
    priority: 4
    fields:
      - field: Value
        type: 0
        i18n:
          zh-CN: 值
          en-US: Value
    protocol: jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      # jmx mbean object name
      objectName: kafka.server:type=ReplicaManager,name=PartitionCount
      url: ^_^url^_^

  - name: broker_leader_count
    i18n:
      zh-CN: Broker领导者数量
      en-US: Broker Leader Count
    priority: 5
    fields:
      - field: Value
        type: 0
        i18n:
          zh-CN: 值
          en-US: Value
    protocol: jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      # jmx mbean object name
      objectName: kafka.server:type=ReplicaManager,name=LeaderCount
      url: ^_^url^_^

  - name: broker_handler_avg_percent
    i18n:
      zh-CN: Broker处理器平均百分比
      en-US: Broker Handler Avg Percent
    priority: 6
    fields:
      - field: EventType
        type: 1
        i18n:
          zh-CN: 事件类型
          en-US: Event Type
      - field: RateUnit
        type: 1
        i18n:
          zh-CN: 速率单位
          en-US: Rate Unit
      - field: Count
        type: 0
        i18n:
          zh-CN: 计数
          en-US: Count
      - field: MeanRate
        type: 0
        i18n:
          zh-CN: 平均速率
          en-US: Mean Rate
      - field: OneMinuteRate
        type: 0
        i18n:
          zh-CN: 一分钟速率
          en-US: One Minute Rate
      - field: FiveMinuteRate
        type: 0
        i18n:
          zh-CN: 五分钟速率
          en-US: Five Minute Rate
      - field: FifteenMinuteRate
        type: 0
        i18n:
          zh-CN: 十五分钟速率
          en-US: Fifteen Minute Rate
    protocol: jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      # jmx mbean object name
      objectName: kafka.server:type=KafkaRequestHandlerPool,name=RequestHandlerAvgIdlePercent
      url: ^_^url^_^

  - name: topic_bytes_in_persec
    i18n:
      zh-CN: 每秒主题流入字节
      en-US: Topic Bytes In PerSec
    priority: 7
    fields:
      - field: EventType
        type: 1
        i18n:
          zh-CN: 事件类型
          en-US: Event Type
      - field: RateUnit
        type: 1
        i18n:
          zh-CN: 速率单位
          en-US: Rate Unit
      - field: MeanRate
        type: 0
        i18n:
          zh-CN: 平均速率
          en-US: Mean Rate
      - field: OneMinuteRate
        type: 0
        i18n:
          zh-CN: 一分钟速率
          en-US: One Minute Rate
      - field: FiveMinuteRate
        type: 0
        i18n:
          zh-CN: 五分钟速率
          en-US: Five Minute Rate
      - field: FifteenMinuteRate
        type: 0
        i18n:
          zh-CN: 十五分钟速率
          en-US: Fifteen Minute Rate
      - field: Count
        type: 0
        i18n:
          zh-CN: 计数
          en-US: Count
    protocol: jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      # jmx mbean object name
      objectName: kafka.server:type=BrokerTopicMetrics,name=BytesInPerSec
      url: ^_^url^_^

  - name: topic_bytes_out_persec
    i18n:
      zh-CN: 每秒主题流出字节
      en-US: Topic Bytes Out PerSec
    priority: 8
    fields:
      - field: EventType
        type: 1
        i18n:
          zh-CN: 事件类型
          en-US: Event Type
      - field: RateUnit
        type: 1
        i18n:
          zh-CN: 速率单位
          en-US: Rate Unit
      - field: MeanRate
        type: 0
        i18n:
          zh-CN: 平均速率
          en-US: Mean Rate
      - field: OneMinuteRate
        type: 0
        i18n:
          zh-CN: 一分钟速率
          en-US: One Minute Rate
      - field: FiveMinuteRate
        type: 0
        i18n:
          zh-CN: 五分钟速率
          en-US: Five Minute Rate
      - field: FifteenMinuteRate
        type: 0
        i18n:
          zh-CN: 十五分钟速率
          en-US: Fifteen Minute Rate
      - field: Count
        type: 0
        i18n:
          zh-CN: 计数
          en-US: Count
    protocol: jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      # jmx mbean object name
      objectName: kafka.server:type=BrokerTopicMetrics,name=BytesOutPerSec
      url: ^_^url^_^

  - name: produce_message_conversions_persec
    i18n:
      zh-CN: 每秒生产消息转换
      en-US: Produce Message Conversions PerSec
    priority: 9
    fields:
      - field: EventType
        type: 1
        i18n:
          zh-CN: 事件类型
          en-US: Event Type
      - field: RateUnit
        type: 1
        i18n:
          zh-CN: 速率单位
          en-US: Rate Unit
      - field: MeanRate
        type: 0
        i18n:
          zh-CN: 平均速率
          en-US: Mean Rate
      - field: OneMinuteRate
        type: 0
        i18n:
          zh-CN: 一分钟速率
          en-US: One Minute Rate
      - field: FiveMinuteRate
        type: 0
        i18n:
          zh-CN: 五分钟速率
          en-US: Five Minute Rate
      - field: FifteenMinuteRate
        type: 0
        i18n:
          zh-CN: 十五分钟速率
          en-US: Fifteen Minute Rate
      - field: Count
        type: 0
        i18n:
          zh-CN: 计数
          en-US: Count
    protocol: jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      # jmx mbean object name
      objectName: kafka.server:type=BrokerTopicMetrics,name=ProduceMessageConversionsPerSec
      url: ^_^url^_^

  - name: produce_total_requests_persec
    i18n:
      zh-CN: 每秒生产总请求数
      en-US: Produce Total Requests PerSec
    priority: 10
    fields:
      - field: EventType
        type: 1
        i18n:
          zh-CN: 事件类型
          en-US: Event Type
      - field: RateUnit
        type: 1
        i18n:
          zh-CN: 速率单位
          en-US: Rate Unit
      - field: MeanRate
        type: 0
        i18n:
          zh-CN: 平均速率
          en-US: Mean Rate
      - field: OneMinuteRate
        type: 0
        i18n:
          zh-CN: 一分钟速率
          en-US: One Minute Rate
      - field: FiveMinuteRate
        type: 0
        i18n:
          zh-CN: 五分钟速率
          en-US: Five Minute Rate
      - field: FifteenMinuteRate
        type: 0
        i18n:
          zh-CN: 十五分钟速率
          en-US: Fifteen Minute Rate
      - field: Count
        type: 0
        i18n:
          zh-CN: 计数
          en-US: Count
    protocol: jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      # jmx mbean object name
      objectName: kafka.server:type=BrokerTopicMetrics,name=TotalProduceRequestsPerSec
      url: ^_^url^_^
