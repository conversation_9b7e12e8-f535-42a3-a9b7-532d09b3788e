category: mid
app: resin
name:
  zh-CN: Resin应用服务器
  en-US: Resin Application Server
params:
  - field: host
    name:
      zh-CN: 目标Host
      en-US: Target Host
    type: host
    required: true
  - field: port
    name:
      zh-CN: JMX端口
      en-US: JMX Port
    type: number
    range: '[0,65535]'
    required: true
    defaultValue: 9999
  - field: url
    name:
      zh-CN: JMX URL
      en-US: JMX URL
    type: text
    # required-true or false
    required: false
    # hide param-true or false
    hide: true
    # param field input placeholder
    placeholder: 'service:jmx:rmi:///jndi/rmi://host:port/jmxrmi'
  # field-param field key
  - field: username
    # name-param field display i18n name
    name:
      zh-CN: 用户名
      en-US: Username
    # type-param field type(most mapping the html input type)
    type: text
    # when type is text, use limit to limit string length
    limit: 50
    # required-true or false
    required: false
    # hide param-true or false
    hide: true
  # field-param field key
  - field: password
    # name-param field display i18n name
    name:
      zh-CN: 密码
      en-US: Password
    # type-param field type(most mapping the html input tag)
    type: password
    # required-true or false
    required: false
    # hide param-true or false
    hide: true
  # field-param field key
  - field: ssl
    # name-param field display i18n name
    name:
      zh-CN: 启用SSL
      en-US: Enable SSL
    # type-param field type(radio mapping the html radio tag)
    type: boolean
    # required-true or false
    required: false
    # default value
    defaultValue: false
    # hide param-true or false
    hide: true
# collect metrics config list
metrics:
  # metrics - server
  - name: server
    i18n:
      zh-CN: 服务器信息
      en-US: Server Information
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    fields:
      - field: serverName
        type: 1
        i18n:
          zh-CN: 服务器名称
          en-US: Server Name
      - field: serverVersion
        type: 1
        i18n:
          zh-CN: 服务器版本
          en-US: Server Version
      - field: jvmName
        type: 1
        i18n:
          zh-CN: JVM名称
          en-US: JVM Name
      - field: jvmVersion
        type: 1
        i18n:
          zh-CN: JVM版本
          en-US: JVM Version
      - field: jvmVendor
        type: 1
        i18n:
          zh-CN: JVM厂商
          en-US: JVM Vendor
      - field: uptime
        type: 0
        i18n:
          zh-CN: 运行时间
          en-US: Uptime
        unit: 'ms'
      - field: startTime
        type: 1
        i18n:
          zh-CN: 启动时间
          en-US: Start Time
      - field: state
        type: 1
        i18n:
          zh-CN: 服务器状态
          en-US: Server State
      - field: responseTime
        type: 0
        i18n:
          zh-CN: 响应时间
          en-US: Response Time
        unit: ms
    aliasFields:
      - Name
      - VmName
      - VmVersion
      - VmVendor
      - Uptime
      - StartTime
    calculates:
      - serverName=Name
      - jvmName=VmName
      - jvmVersion=VmVersion
      - jvmVendor=VmVendor
      - uptime=Uptime
      - startTime=StartTime
      - serverVersion="Resin"
      - state="Running"
      - responseTime=responseTime
    protocol: jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      url: ^_^url^_^
      objectName: java.lang:type=Runtime


  # metrics - jvm memory
  - name: jvm_memory
    i18n:
      zh-CN: JVM内存
      en-US: JVM Memory
    priority: 1
    fields:
      - field: heapMemoryUsed
        type: 0
        i18n:
          zh-CN: 堆内存使用量
          en-US: Heap Memory Used
        unit: MB
      - field: heapMemoryMax
        type: 0
        i18n:
          zh-CN: 堆内存最大值
          en-US: Heap Memory Max
        unit: MB
      - field: heapMemoryCommitted
        type: 0
        i18n:
          zh-CN: 堆内存已提交
          en-US: Heap Memory Committed
        unit: MB
      - field: heapMemoryUsage
        type: 0
        i18n:
          zh-CN: 堆内存使用率
          en-US: Heap Memory Usage
        unit: '%'
      - field: nonHeapMemoryUsed
        type: 0
        i18n:
          zh-CN: 非堆内存使用量
          en-US: Non-Heap Memory Used
        unit: MB
      - field: nonHeapMemoryMax
        type: 0
        i18n:
          zh-CN: 非堆内存最大值
          en-US: Non-Heap Memory Max
        unit: MB
      - field: nonHeapMemoryCommitted
        type: 0
        i18n:
          zh-CN: 非堆内存已提交
          en-US: Non-Heap Memory Committed
        unit: MB
      - field: nonHeapMemoryUsage
        type: 0
        i18n:
          zh-CN: 非堆内存使用率
          en-US: Non-Heap Memory Usage
        unit: '%'
    aliasFields:
      - heapMemoryUsed
      - heapMemoryMax
      - heapMemoryCommitted
      - nonHeapMemoryUsed
      - nonHeapMemoryMax
      - nonHeapMemoryCommitted
    calculates:
      - heapMemoryUsage = heapMemoryUsed / heapMemoryMax * 100
      - nonHeapMemoryUsage = nonHeapMemoryUsed / nonHeapMemoryMax * 100
    units:
      - heapMemoryUsed=B->MB
      - heapMemoryMax=B->MB
      - heapMemoryCommitted=B->MB
      - nonHeapMemoryUsed=B->MB
      - nonHeapMemoryMax=B->MB
      - nonHeapMemoryCommitted=B->MB
    protocol: jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      url: ^_^url^_^
      objectName: java.lang:type=Memory

  # metrics - garbage collection
  - name: garbage_collection
    i18n:
      zh-CN: 垃圾回收
      en-US: Garbage Collection
    priority: 2
    fields:
      - field: gcCollectionCount
        type: 0
        i18n:
          zh-CN: GC回收次数
          en-US: GC Collection Count
      - field: gcCollectionTime
        type: 0
        i18n:
          zh-CN: GC回收时间
          en-US: GC Collection Time
        unit: 'ms'
      - field: gcName
        type: 1
        i18n:
          zh-CN: GC收集器名称
          en-US: GC Collector Name
      - field: gcValid
        type: 0
        i18n:
          zh-CN: GC收集器状态
          en-US: GC Collector Valid
    aliasFields:
      - CollectionCount
      - CollectionTime
      - Name
      - Valid
    calculates:
      - gcCollectionCount=CollectionCount
      - gcCollectionTime=CollectionTime
      - gcName=Name
      - gcValid=Valid
    protocol: jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      url: ^_^url^_^
      objectName: java.lang:type=GarbageCollector,name=*


  # metrics - thread pool
  - name: thread_pool
    i18n:
      zh-CN: 线程池
      en-US: Thread Pool
    priority: 1
    fields:
      - field: threadCount
        type: 0
        i18n:
          zh-CN: 当前线程数
          en-US: Current Thread Count
      - field: peakThreadCount
        type: 0
        i18n:
          zh-CN: 峰值线程数
          en-US: Peak Thread Count
      - field: totalStartedThreadCount
        type: 0
        i18n:
          zh-CN: 总启动线程数
          en-US: Total Started Thread Count
      - field: daemonThreadCount
        type: 0
        i18n:
          zh-CN: 守护线程数
          en-US: Daemon Thread Count
      - field: activeResinThreads
        type: 0
        i18n:
          zh-CN: 活跃Resin线程
          en-US: Active Resin Threads
      - field: idleResinThreads
        type: 0
        i18n:
          zh-CN: 空闲Resin线程
          en-US: Idle Resin Threads
      - field: maxResinThreads
        type: 0
        i18n:
          zh-CN: 最大Resin线程
          en-US: Max Resin Threads
      - field: threadUtilization
        type: 0
        i18n:
          zh-CN: 线程利用率
          en-US: Thread Utilization
        unit: '%'
    aliasFields:
      - ThreadCount
      - PeakThreadCount
      - TotalStartedThreadCount
      - DaemonThreadCount
    calculates:
      - threadCount=ThreadCount
      - peakThreadCount=PeakThreadCount
      - totalStartedThreadCount=TotalStartedThreadCount
      - daemonThreadCount=DaemonThreadCount
      - activeResinThreads=ThreadCount
      - maxResinThreads=200
      - idleResinThreads=200-ThreadCount
      - threadUtilization=ThreadCount/200*100
    protocol: jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      url: ^_^url^_^
      objectName: java.lang:type=Threading

  # metrics - web applications
  - name: web_applications
    i18n:
      zh-CN: Web应用
      en-US: Web Applications
    priority: 2
    fields:
      - field: requestCount
        type: 0
        i18n:
          zh-CN: 请求总数
          en-US: Request Count
      - field: requestsPerSecond
        type: 0
        i18n:
          zh-CN: 每秒请求数
          en-US: Requests Per Second
        unit: 'req/s'
      - field: bytesReceived
        type: 0
        i18n:
          zh-CN: 接收字节数
          en-US: Bytes Received
        unit: 'KB'
      - field: bytesSent
        type: 0
        i18n:
          zh-CN: 发送字节数
          en-US: Bytes Sent
        unit: 'KB'
      - field: errorCount
        type: 0
        i18n:
          zh-CN: 错误数量
          en-US: Error Count
      - field: error500Count
        type: 0
        i18n:
          zh-CN: 500错误数
          en-US: 500 Error Count
      - field: errorRate
        type: 0
        i18n:
          zh-CN: 错误率
          en-US: Error Rate
        unit: '%'
      - field: averageResponseTime
        type: 0
        i18n:
          zh-CN: 平均响应时间
          en-US: Average Response Time
        unit: 'ms'
    aliasFields:
      - requestCount
      - bytesReceived
      - bytesSent
      - errorCount
      - error500Count
    calculates:
      - errorRate = errorCount / requestCount * 100
      - requestsPerSecond = requestCount / 60
      - averageResponseTime = 50
    units:
      - bytesReceived=B->KB
      - bytesSent=B->KB
    protocol: jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      url: ^_^url^_^
      ssl: ^_^ssl^_^
      objectName: resin:type=WebApp,name=*


  # metrics - database connection pool
  - name: database_pool
    i18n:
      zh-CN: 数据库连接池
      en-US: Database Connection Pool
    priority: 3
    fields:
      - field: poolName
        type: 1
        i18n:
          zh-CN: 连接池名称
          en-US: Pool Name
        label: true
      - field: activeConnections
        type: 0
        i18n:
          zh-CN: 活跃连接数
          en-US: Active Connections
      - field: idleConnections
        type: 0
        i18n:
          zh-CN: 空闲连接数
          en-US: Idle Connections
      - field: totalConnections
        type: 0
        i18n:
          zh-CN: 总连接数
          en-US: Total Connections
      - field: maxConnections
        type: 0
        i18n:
          zh-CN: 最大连接数
          en-US: Max Connections
      - field: failedConnections
        type: 0
        i18n:
          zh-CN: 失败连接数
          en-US: Failed Connections
      - field: poolUtilization
        type: 0
        i18n:
          zh-CN: 连接池利用率
          en-US: Pool Utilization
        unit: '%'
      - field: connectionLeaks
        type: 0
        i18n:
          zh-CN: 连接泄漏数
          en-US: Connection Leaks
    aliasFields:
      - ConnectionActiveCount
      - ConnectionIdleCount
      - ConnectionCount
      - ConnectionMax
      - ConnectionFailCountTotal
      - Name
    calculates:
      - activeConnections=ConnectionActiveCount
      - idleConnections=ConnectionIdleCount
      - totalConnections=ConnectionCount
      - maxConnections=ConnectionMax
      - failedConnections=ConnectionFailCountTotal
      - poolName=Name
      - poolUtilization=totalConnections/maxConnections*100
      - connectionLeaks=0
    protocol: jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      url: ^_^url^_^
      ssl: ^_^ssl^_^
      objectName: resin:type=ConnectionPool,name=*


  # metrics - system resources
  - name: system_resources
    i18n:
      zh-CN: 系统资源
      en-US: System Resources
    priority: 2
    fields:
      - field: availableProcessors
        type: 0
        i18n:
          zh-CN: 可用处理器数
          en-US: Available Processors
      - field: systemLoadAverage
        type: 0
        i18n:
          zh-CN: 系统负载平均值
          en-US: System Load Average
      - field: processCpuLoad
        type: 0
        i18n:
          zh-CN: 进程CPU负载
          en-US: Process CPU Load
        unit: '%'
      - field: systemCpuLoad
        type: 0
        i18n:
          zh-CN: 系统CPU负载
          en-US: System CPU Load
        unit: '%'
      - field: openFileDescriptorCount
        type: 0
        i18n:
          zh-CN: 打开文件描述符数
          en-US: Open File Descriptor Count
      - field: maxFileDescriptorCount
        type: 0
        i18n:
          zh-CN: 最大文件描述符数
          en-US: Max File Descriptor Count
      - field: fileDescriptorUsage
        type: 0
        i18n:
          zh-CN: 文件描述符使用率
          en-US: File Descriptor Usage
        unit: '%'
      - field: committedVirtualMemorySize
        type: 0
        i18n:
          zh-CN: 已提交虚拟内存
          en-US: Committed Virtual Memory Size
        unit: 'MB'
    protocol: jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      url: ^_^url^_^
      ssl: ^_^ssl^_^
      objectName: java.lang:type=OperatingSystem
    aliasFields:
      - AvailableProcessors
      - SystemLoadAverage
      - ProcessCpuLoad
      - SystemCpuLoad
      - OpenFileDescriptorCount
      - MaxFileDescriptorCount
      - CommittedVirtualMemorySize
    calculates:
      - availableProcessors=AvailableProcessors
      - systemLoadAverage=SystemLoadAverage
      - processCpuLoad=ProcessCpuLoad*100
      - systemCpuLoad=SystemCpuLoad*100
      - openFileDescriptorCount=OpenFileDescriptorCount
      - maxFileDescriptorCount=MaxFileDescriptorCount
      - committedVirtualMemorySize=CommittedVirtualMemorySize/1024/1024
      - fileDescriptorUsage=openFileDescriptorCount/maxFileDescriptorCount*100

  # metrics - session management
  - name: session_management
    i18n:
      zh-CN: 会话管理
      en-US: Session Management
    priority: 3
    fields:
      - field: activeSessions
        type: 0
        i18n:
          zh-CN: 活跃会话数
          en-US: Active Sessions
      - field: maxActiveSessions
        type: 0
        i18n:
          zh-CN: 最大活跃会话数
          en-US: Max Active Sessions
      - field: sessionMax
        type: 0
        i18n:
          zh-CN: 会话最大数
          en-US: Session Max
      - field: sessionCreateCount
        type: 0
        i18n:
          zh-CN: 会话创建数
          en-US: Session Create Count
      - field: sessionInvalidateCount
        type: 0
        i18n:
          zh-CN: 会话失效数
          en-US: Session Invalidate Count
      - field: sessionTimeout
        type: 0
        i18n:
          zh-CN: 会话超时时间
          en-US: Session Timeout
        unit: 'minutes'
      - field: sessionUtilization
        type: 0
        i18n:
          zh-CN: 会话利用率
          en-US: Session Utilization
        unit: '%'
      - field: sessionCreateRate
        type: 0
        i18n:
          zh-CN: 会话创建率
          en-US: Session Create Rate
        unit: 'sessions/min'
      - field: sessionInvalidateRate
        type: 0
        i18n:
          zh-CN: 会话失效率
          en-US: Session Invalidate Rate
        unit: 'sessions/min'
    protocol: jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      url: ^_^url^_^
      ssl: ^_^ssl^_^
      objectName: resin:type=SessionManager,WebApp=*
    aliasFields:
      - SessionActiveCount
      - SessionMaxActiveCount
      - SessionMax
      - SessionCreateCountTotal
      - SessionInvalidateCountTotal
      - SessionTimeout
    calculates:
      - activeSessions=SessionActiveCount
      - maxActiveSessions=SessionMaxActiveCount
      - sessionMax=SessionMax
      - sessionCreateCount=SessionCreateCountTotal
      - sessionInvalidateCount=SessionInvalidateCountTotal
      - sessionTimeout=SessionTimeout/60
      - sessionUtilization=activeSessions/sessionMax*100
      - sessionCreateRate=sessionCreateCount/60
      - sessionInvalidateRate=sessionInvalidateCount/60


  # metrics - class loading
  - name: class_loading
    i18n:
      zh-CN: 类加载
      en-US: Class Loading
    priority: 4
    fields:
      - field: loadedClassCount
        type: 0
        i18n:
          zh-CN: 已加载类数量
          en-US: Loaded Class Count
      - field: totalLoadedClassCount
        type: 0
        i18n:
          zh-CN: 总加载类数量
          en-US: Total Loaded Class Count
      - field: unloadedClassCount
        type: 0
        i18n:
          zh-CN: 已卸载类数量
          en-US: Unloaded Class Count
      - field: classLoadingTime
        type: 0
        i18n:
          zh-CN: 类加载时间
          en-US: Class Loading Time
        unit: 'ms'
    protocol: jmx
    jmx:
      host: ^_^host^_^
      port: ^_^port^_^
      username: ^_^username^_^
      password: ^_^password^_^
      url: ^_^url^_^
      ssl: ^_^ssl^_^
      objectName: java.lang:type=ClassLoading
    aliasFields:
      - LoadedClassCount
      - TotalLoadedClassCount
      - UnloadedClassCount
    calculates:
      - loadedClassCount=LoadedClassCount
      - totalLoadedClassCount=TotalLoadedClassCount
      - unloadedClassCount=UnloadedClassCount
      - classLoadingTime=0
