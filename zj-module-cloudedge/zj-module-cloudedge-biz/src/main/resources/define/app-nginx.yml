# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# 监控类型所属类别：service-应用服务 program-应用程序 db-数据库 custom-自定义 os-操作系统 bigdata-大数据 mid-中间件 webserver-web服务器 cache-缓存 cn-云原生 network-网络监控等等
category: mid
# 监控应用类型名称(与文件名保持一致) eg: linux windows tomcat mysql aws...
app: nginx
# The app api i18n name
# app api国际化名称
name:
  zh-CN: Nginx服务
  en-US: Nginx Server

# app api所需输入参数定义(根据定义渲染页面UI)
# Input params define for app api(render web ui by the definition)
params:
  # field-param field key
  - field: host
    # name-param field display i18n name
    name:
      zh-CN: 目标Host
      en-US: Target Host
    # type-param field type(most mapping the html input type)
    type: host
    # required-true or false
    required: true
  # field-param field key
  - field: port
    # name-param field display i18n name
    name:
      zh-CN: 端口
      en-US: Port
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,65535]'
    # required-true or false
    required: true
    # default value
    defaultValue: 80
  # field-param field key
  - field: timeout
    # name-param field display i18n name
    name:
      zh-CN: 连接超时时间(ms)
      en-US: Connect Timeout(ms)
    # type-param field type(most mapping the html input type)
    type: number
    # when type is number, range is required
    range: '[0,100000]'
    # required-true or false
    required: true
    # default value 6000
    defaultValue: 6000
  # field-param field key
  - field: ssl
    # name-param field display i18n name
    name:
      zh-CN: 启动SSL
      en-US: SSL
    # When the type is boolean, the frontend will display a switch for it.
    type: boolean
    # required-true or false
    required: false

# collect metrics config list
metrics:
  # metrics - available
  - name: available
    i18n:
      zh-CN: 可用性
      en-US: Available
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 0
    # collect metrics content
    fields:
      # field-metric name, type-metric type(0-number,1-string), instance-is instance primary key, unit-metric unit
      - field: responseTime
        type: 0
        unit: ms
        i18n:
          zh-CN: 响应时间
          en-US: Response Time
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: nginx
    # Specific collection configuration when protocol is http protocol
    nginx:
      # http host: ipv4 ipv6 domain
      host: ^_^host^_^
      # http port
      port: ^_^port^_^
      # timeout
      timeout: ^_^timeout^_^
      # ssl
      ssl: ^_^ssl^_^

  - name: nginx_status
    i18n:
      zh-CN: Nginx 状态信息
      en-US: Nginx Status
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 1
    fields:
      - field: accepts
        type: 0
        i18n:
          zh-CN: 接受连接数
          en-US: Accepts
      - field: handled
        type: 0
        i18n:
          zh-CN: 成功处理连接数
          en-US: Handled
      - field: active
        type: 0
        i18n:
          zh-CN: 当前活跃连接数
          en-US: Active
      - field: dropped
        type: 0
        i18n:
          zh-CN: 已丢弃连接数
          en-US: Dropped
      - field: requests
        type: 0
        i18n:
          zh-CN: 客户端请求数
          en-US: Requests
      - field: reading
        type: 0
        i18n:
          zh-CN: 正在执行读操作的连接数
          en-US: Reading
      - field: writing
        type: 0
        i18n:
          zh-CN: 正在执行写操作的连接数
          en-US: Writing
      - field: waiting
        type: 0
        i18n:
          zh-CN: 正在等待的连接数
          en-US: Waiting
    # Alias field list, used to identify metrics in the query result
    aliasFields:
      - accepts
      - handled
      - active
      - dropped
      - requests
      - reading
      - writing
      - waiting
    # A list of calculation scripts for metric values.
    calculates:
      - dropped=accepts - handled
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk, nginx
    protocol: nginx
    # the config content when protocol is http
    nginx:
      # http host: ipv4 ipv6 domain
      host: ^_^host^_^
      # http port
      port: ^_^port^_^
      # timeout
      timeout: ^_^timeout^_^
      # http url
      url: /nginx-status
      # ssl
      ssl: ^_^ssl^_^

  - name: req_status
    i18n:
      zh-CN: 请求详细信息
      en-US: Req Status
    # metrics scheduling priority(0->127)->(high->low), metrics with the same priority will be scheduled in parallel
    # priority 0's metrics is availability metrics, it will be scheduled first, only availability metrics collect success will the scheduling continue
    priority: 2
    fields:
      - field: zone_name
        type: 1
        i18n:
          zh-CN: 分组类别
          en-US: Zone Name
      - field: key
        type: 1
        i18n:
          zh-CN: 分组名称
          en-US: Key
      - field: max_active
        type: 0
        i18n:
          zh-CN: 最大并发连接数
          en-US: Max Active
      - field: max_bw
        type: 0
        unit: KB
        i18n:
          zh-CN: 最大带宽
          en-US: Max BW
      - field: traffic
        type: 0
        unit: KB
        i18n:
          zh-CN: 总流量
          en-US: Traffic
      - field: requests
        type: 0
        i18n:
          zh-CN: 总请求数
          en-US: Requests
      - field: active
        type: 0
        i18n:
          zh-CN: 当前并发连接数
          en-US: Active
      - field: bandwidth
        type: 0
        unit: KB
        i18n:
          zh-CN: 当前带宽
          en-US: Bandwidth
    # the protocol used for monitoring, eg: sql, ssh, http, telnet, wmi, snmp, sdk
    protocol: nginx
    # the config content when protocol is http
    nginx:
      # http host: ipv4 ipv6 domain
      host: ^_^host^_^
      # http port
      port: ^_^port^_^
      # timeout
      timeout: ^_^timeout^_^
      # http url
      url: /req-status
      # ssl
      ssl: ^_^ssl^_^
