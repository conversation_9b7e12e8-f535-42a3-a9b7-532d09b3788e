//package cn.iocoder.cloud.module.cloudedge.config.config;
//
//import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
//import cn.iocoder.zj.framework.common.pojo.CommonResult;
//import cn.iocoder.zj.framework.common.util.servlet.ServletUtils;
//import cn.iocoder.zj.framework.security.core.LoginUser;
//import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
//import cn.iocoder.zj.module.system.api.licence.LicenceApi;
//import cn.iocoder.zj.module.system.api.permission.PermissionApi;
//import cn.iocoder.zj.module.system.api.permission.RoleApi;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import lombok.extern.slf4j.Slf4j;
//import org.aspectj.lang.ProceedingJoinPoint;
//import org.aspectj.lang.Signature;
//import org.aspectj.lang.annotation.Around;
//import org.aspectj.lang.annotation.Aspect;
//import org.aspectj.lang.reflect.MethodSignature;
//import org.dromara.hertzbeat.common.entity.dto.Message;
//import org.dromara.hertzbeat.common.entity.manager.ParamDefine;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//import org.springframework.stereotype.Component;
//import org.springframework.web.context.request.RequestContextHolder;
//import org.springframework.web.context.request.ServletRequestAttributes;
//
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import java.io.IOException;
//import java.lang.reflect.Method;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//
//import static cn.iocoder.zj.framework.common.exception.enums.GlobalErrorCodeConstants.FORBIDDEN;
//import static java.util.Collections.singleton;
//
///**
// * @ClassName : LicenceInterceptor  //类名
// * @Description : 授权拦截  //描述
// * <AUTHOR> <EMAIL> //作者
// * @Date: 2024/1/31  17:28
// */
//
//@Slf4j
//@Component  //表示它是一个Spring的组件
//@Aspect  //表示它是一个切面
//public class LicenceAspect {
//
//    @Autowired
//    LicenceApi licenceApi;
//    @Autowired
//    PermissionApi permissionApi;
//    @Autowired
//    RoleApi roleApi;
//
//    /**
//     * 通过ProceedingJoinPoint对象的getArgs()我们可以得到传进来的参数。
//     * 通过ProceedingJoinPoint对象的proceed()我们可以得到拿到切面方法返回值的对象。
//     *
//     * @param pjp
//     * @return 环绕通知    首先是:包名  然后是: 类名  然后是方法名:方法名   括号内是:参数
//     */
//    @Around("execution(* cn.iocoder.cloud.module.cloudedge.controller.admin.*.*(..))")
//    public Object handleControllerMethod(ProceedingJoinPoint pjp) throws Throwable {
//        log.info("HttpAspect handleControllerMethod filter start");
//
//        //原始的HTTP请求和响应的信息
//        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
//        HttpServletRequest request = attributes.getRequest();
//        HttpServletResponse response = attributes.getResponse();
//
//        //获取登录人
//        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
//        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
//        if (!roleApi.hasAnySuperAdmin(roleIds)) {
//            Map map = licenceApi.selectLicence(loginUser.getTenantId()).getCheckedData();
//            // 说明授权资产已超出
//            if (map.get("state").equals("unavailable")) {
//                ResponseEntity<Message<Map>> result = new ResponseEntity<>(HttpStatus.OK);
//                returnData(Message.success(map), response);
//            }
//        }
//        return pjp.proceed();//代理方法的返回值
//    }
//
//    /**
//     * 返回数据
//     *
//     * @param result
//     * @param response
//     * @throws IOException
//     */
//    public void returnData(Message<Map> result, HttpServletResponse response) throws IOException {
//        response.setCharacterEncoding("UTF-8");
//        //这里传提示语可以改成自己项目的返回数据封装的类
//        ServletUtils.writeJSON(response, result);
//        return;
//    }
//}
