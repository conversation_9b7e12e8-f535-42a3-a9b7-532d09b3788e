<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.cloud.module.cloudedge.dal.mysql.collectorplatform.CollectorPlatformMapper">
    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getCollectorInfo" resultType="org.apache.hertzbeat.common.entity.manager.Collector">
        select * from hzb_collector where id = #{collectorId}
    </select>

    <select id="getCollectorInfoByName" resultType="org.apache.hertzbeat.common.entity.manager.Collector">
        select * from hzb_collector where name = #{name}
    </select>

    <update id="updateCollectorById">
        update hzb_collector
        set platform_id = #{platformId},
        platform_name = #{platformName}
        where id = #{id}
    </update>
    <update id="updateCollectorByProjectId">
        update hzb_collector
        set project_id = #{projectId},
            project_name = #{projectName}
        where id = #{id}
    </update>

    <select id="collectorBySelect" resultType="java.util.Map">

        SELECT id,name,status FROM hzb_collector WHERE name != 'main-default-collector'

    </select>

    <select id="getTotalType" resultType="java.util.Map">
        SELECT
            COALESCE(sub.count, 0) AS count,
            all_statuses.status
        FROM
            (
                SELECT 0 AS status
                UNION ALL
                SELECT 1 AS status
                UNION ALL
                SELECT 2 AS status
            ) AS all_statuses
                LEFT JOIN
            (
                SELECT
                    COUNT(*) AS count,
                    status
                FROM
                    hzb_monitor WHERE 1=1
                    <if test="platformId != null and platformId != ''">
                       and platform_id = #{platformId}
                    </if>
                    <if test="app != null and app !=''">
                        and app = #{app}
                    </if>
            <if test="subEnums.size>0">
                and         app in
                <foreach close=")" collection="subEnums" item="item" open="(" separator=", ">
                    #{item.value}
                </foreach>
            </if>

                GROUP BY
                    status
            ) AS sub
            ON
                all_statuses.status = sub.status;
    </select>

    <select id="getCollectType" resultType="java.lang.Long">
        SELECT status as collectorType from  hzb_collector WHERE name in (SELECT collector from hzb_collector_monitor_bind WHERE monitor_id =#{id})
    </select>

    <select id="getTotalTypeByPlatform" resultType="java.util.Map">
        SELECT
        COALESCE(sub.count, 0) AS count,
        all_statuses.status
        FROM
        (
        SELECT 0 AS status
        UNION ALL
        SELECT 1 AS status
        UNION ALL
        SELECT 2 AS status
        ) AS all_statuses
        LEFT JOIN
        (
        SELECT
        COUNT(*) AS count,
        status
        FROM
        hzb_monitor WHERE
            platform_id in
            <foreach close=")" collection="platform" item="item" open="(" separator=", ">
                #{item.platformId}
            </foreach>

        <if test="app != null and app !=''">
            and app = #{app}
        </if>
        <if test="subEnums.size>0">
            and         app in
            <foreach close=")" collection="subEnums" item="item" open="(" separator=", ">
                #{item.value}
            </foreach>
        </if>

        GROUP BY
        status
        ) AS sub
        ON
        all_statuses.status = sub.status;
    </select>

    <select id="getMonitorProtocol" resultType="java.lang.String">
        SELECT value as  protocol FROM hzb_param WHERE monitor_id = #{monitorId} AND field = 'protocol'
    </select>

    <select id="getMonitorTopology" resultType="java.util.Map">
        select  * from monitor_topology where deleted = 0
    </select>
    <select id="selectListByIds"
            resultType="cn.iocoder.cloud.module.cloudedge.dal.dataobject.collectorplatform.CollectorPlatformDO">
        select  * from hzb_collector_platform where platform_id = #{platformId} and deleted = 0
    </select>

    <select id="selectListByTenantId" resultType="java.util.Map">
        select status from hzb_collector where project_id = #{tenantId}
    </select>
    <select id="selectListById"
            resultType="cn.iocoder.cloud.module.cloudedge.dal.dataobject.collectorplatform.CollectorPlatformDO">
        select platform_id, platform_name
        from hzb_collector_platform
        where collector_id = #{id}
          and deleted = 0
        limit 1
    </select>

    <update id="updateScanRemark">
        UPDATE ip_scan_result
        SET remark = 1,
        update_time = update_time
        WHERE deleted = 0 and id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getPlatforms" resultType="cn.iocoder.cloud.module.cloudedge.dal.dataobject.collectorplatform.CollectPlatfDTO">
        SELECT
            ip_address,
            platform_id,
            platform_name
        FROM
            ip_scan_result
        WHERE
            id IN
        <foreach collection="sourceIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectCollectorAlarmById"
            resultType="cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO">
        select * from  hzb_alert  WHERE monitor_id = #{monitorId} ORDER BY id desc limit 1
    </select>


    <select id="getDicts" resultType="java.util.Map">
        SELECT label AS `value`, VALUE AS `key`
        FROM system_dict_data
        WHERE dict_type = 'monitor_category_type'
    </select>

    <select id="getAlarmByMonitorId"
            resultType="cn.iocoder.cloud.module.cloudedge.api.monitor.dto.AlarmReqDTO">
        select * from  hzb_alert  WHERE monitor_id = #{monitorId} ORDER BY id desc limit 1
    </select>

    <insert id="saveMetirc" parameterType="java.util.List">
        INSERT INTO metirc (name)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item})
        </foreach>
    </insert>
</mapper>