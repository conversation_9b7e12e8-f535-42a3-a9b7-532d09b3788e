package cn.iocoder.cloud.module.cloudedge.service.impl;

import cn.iocoder.cloud.module.cloudedge.dal.dataobject.herzbeat.ObjectStoreConfigChangeEvent;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.herzbeat.ObjectStoreDTO;
import cn.iocoder.cloud.module.cloudedge.dal.mysql.dao.GeneralConfigDao;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.obs.services.ObsClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.constants.GeneralConfigTypeEnum;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.lang.reflect.Type;

/**
 * 文件存储配置服务
 *
 * <AUTHOR> href="mailto:<EMAIL>">gcdd1993</a>
 * Created by gcdd1993 on 2023/9/13
 */
@Order(Ordered.HIGHEST_PRECEDENCE)
@Slf4j
@Service
public class ObjectStoreConfigServiceImpl extends AbstractGeneralConfigServiceImpl<ObjectStoreDTO<ObjectStoreDTO.ObsConfig>> implements InitializingBean {
    @Resource
    private DefaultListableBeanFactory beanFactory;

    @Resource
    private ApplicationContext ctx;

    private static final String BEAN_NAME = "ObjectStoreService";

    /**
     * <p>Constructor, passing in GeneralConfigDao, ObjectMapper and type.</p>
     *
     * @param generalConfigDao  configDao object
     * @param objectMapper     JSON tool object
     */
    public ObjectStoreConfigServiceImpl(GeneralConfigDao generalConfigDao, ObjectMapper objectMapper) {
        super(generalConfigDao, objectMapper);
    }

    @Override
    public String type() {
        return GeneralConfigTypeEnum.oss.name();
    }

    @Override
    public TypeReference<ObjectStoreDTO<ObjectStoreDTO.ObsConfig>> getTypeReference() {
        return new TypeReference<>() {
            @Override
            public Type getType() {
                return ObjectStoreDTO.class;
            }
        };
    }

    @Override
    public void handler(ObjectStoreDTO<ObjectStoreDTO.ObsConfig> config) {
        // initialize file storage service
        if (config != null) {
            if (config.getType() == ObjectStoreDTO.Type.OBS) {
                initObs(config);
                // case other object store service
            }
            ctx.publishEvent(new ObjectStoreConfigChangeEvent(config));
        }
        log.warn("object store config is null, please check the configuration file.");
    }

    /**
     * init Huawei Cloud OBS
     */
    private void initObs(ObjectStoreDTO<ObjectStoreDTO.ObsConfig> config) {
        var obsConfig = objectMapper.convertValue(config.getConfig(), ObjectStoreDTO.ObsConfig.class);
        Assert.hasText(obsConfig.getAccessKey(), "cannot find obs accessKey");
        Assert.hasText(obsConfig.getSecretKey(), "cannot find obs secretKey");
        Assert.hasText(obsConfig.getEndpoint(), "cannot find obs endpoint");
        Assert.hasText(obsConfig.getBucketName(), "cannot find obs bucket name");

        var obsClient = new ObsClient(obsConfig.getAccessKey(), obsConfig.getSecretKey(), obsConfig.getEndpoint());

        beanFactory.destroySingleton(BEAN_NAME);
        beanFactory.registerSingleton(BEAN_NAME, new ObsObjectStoreServiceImpl(obsClient, obsConfig.getBucketName(), obsConfig.getSavePath()));

        log.info("obs store service init success.");
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // init file storage
        handler(getConfig());
    }
}
