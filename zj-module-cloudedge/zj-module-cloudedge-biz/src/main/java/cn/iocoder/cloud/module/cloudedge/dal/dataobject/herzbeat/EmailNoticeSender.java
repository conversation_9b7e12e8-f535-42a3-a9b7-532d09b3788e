package cn.iocoder.cloud.module.cloudedge.dal.dataobject.herzbeat;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;



/**
 * 邮件账号配置dto
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmailNoticeSender {

    @NotNull(message = "Type cannot be empty")
    private Integer type;

    @javax.validation.constraints.NotBlank(message = "Mail host cannot be empty")
    private String emailHost;

    @javax.validation.constraints.NotBlank(message = "Username cannot be empty")
    @javax.validation.constraints.Email
    private String emailUsername;

    @javax.validation.constraints.NotBlank(message = "Password cannot be empty")
    private String emailPassword;

    @NotNull(message = "Mail port cannot be null")
    @javax.validation.constraints.Max(message = "Mail port must be less than or equal to 65535", value = 65535)
    @Min(message = "Mail port must be greater than or equal to 1", value = 1)
    private Integer emailPort;

    private boolean emailSsl = true;

    private boolean emailStarttls = false;

    private boolean enable = true;
}
