package cn.iocoder.cloud.module.cloudedge.scheduler.netty.process;

import cn.iocoder.cloud.framework.remoting.netty.NettyRemotingProcessor;
import cn.iocoder.cloud.module.cloudedge.scheduler.netty.ManageServer;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.message.ClusterMsg;

/**
 * handle heartbeat message
 */
@Slf4j
public class HeartbeatProcessor implements NettyRemotingProcessor {

    private final ManageServer manageServer;

    public HeartbeatProcessor(final ManageServer manageServer) {
        this.manageServer = manageServer;
    }

    @Override
    public ClusterMsg.Message handle(ChannelHandlerContext ctx, ClusterMsg.Message message) {
        String identity = message.getIdentity();
        boolean isChannelActive = this.manageServer.isChannelActive(identity);
        if (!isChannelActive) {
            this.manageServer.addChannel(identity, ctx.channel());
            isChannelActive = this.manageServer.isChannelActive(identity);
            if (!isChannelActive) {
                log.info("the collector {} is not online.", identity);
                return null;
            } else {
                this.manageServer.getCollectorAndJobScheduler().collectorGoOnline(identity, null);
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("server receive collector {} heartbeat", message.getIdentity());
        }
        return ClusterMsg.Message.newBuilder()
                .setType(ClusterMsg.MessageType.HEARTBEAT)
                .build();
    }
}
