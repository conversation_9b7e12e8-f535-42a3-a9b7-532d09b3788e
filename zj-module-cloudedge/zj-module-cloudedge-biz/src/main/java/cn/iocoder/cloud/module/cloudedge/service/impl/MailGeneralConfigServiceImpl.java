package cn.iocoder.cloud.module.cloudedge.service.impl;

import cn.iocoder.cloud.module.cloudedge.dal.dataobject.herzbeat.EmailNoticeSender;
import cn.iocoder.cloud.module.cloudedge.dal.mysql.dao.GeneralConfigDao;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.hertzbeat.common.constants.GeneralConfigTypeEnum;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;


/**
 * MailGeneralConfigServiceImpl类是通用邮件配置服务实现类，继承了AbstractGeneralConfigServiceImpl<NoticeSender>类。
 * MailGeneralConfigServiceImpl class is the implementation of general email configuration service,
 * which inherits the AbstractGeneralConfigServiceImpl<NoticeSender> class.
 *
 * <AUTHOR>
 */

@Service
public class MailGeneralConfigServiceImpl extends AbstractGeneralConfigServiceImpl<EmailNoticeSender> {


    /**
     * MailGeneralConfigServiceImpl's constructor creates an instance of this class
     * through the default constructor or deserialization construction (setBeanProps).
     * The parameter generalConfigDao is used for dao layer operation data,
     * and objectMapper is used for object mapping.
     * @param generalConfigDao dao layer operation data, needed to create an instance of this class
     * @param objectMapper     object mapping , needed to create an instance of this class
     */
    public MailGeneralConfigServiceImpl(GeneralConfigDao generalConfigDao, ObjectMapper objectMapper) {
        super(generalConfigDao, objectMapper);
    }

    @Override
    public String type() {
        return GeneralConfigTypeEnum.email.name();
    }

    /**
     * This method is used to get the TypeReference of NoticeSender type for subsequent processing.
     * a TypeReference of NoticeSender type
     */
    @Override
    public TypeReference<EmailNoticeSender> getTypeReference() {
        return new TypeReference<>() {
            @Override
            public Type getType() {
                return EmailNoticeSender.class;
            }
        };
    }
}
