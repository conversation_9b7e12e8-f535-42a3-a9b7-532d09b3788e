/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.module.cloudedge.config;

import lombok.Getter;
import lombok.Setter;
import org.apache.hertzbeat.common.constants.ConfigConstants;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * status page properties
 */

@Getter
@Setter
@Component
@ConfigurationProperties(prefix = ConfigConstants.FunctionModuleConstants.STATUS)
public class StatusProperties {

    /**
     * calculate component status properties
     */
    private CalculateProperties calculate;

    /**
     * calculate component status properties
     */
    @Getter
    @Setter
    public static class CalculateProperties {

        /**
         * the component status calculate interval(s)
         */
        private Integer interval = 300;

    }

}
