package cn.iocoder.cloud.module.cloudedge.controller.admin.collectorplatform.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 采集器平台关联 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CollectorPlatformRespVO extends CollectorPlatformBaseVO {

    @Schema(description = "主键", required = true)
    private Long id;

}
