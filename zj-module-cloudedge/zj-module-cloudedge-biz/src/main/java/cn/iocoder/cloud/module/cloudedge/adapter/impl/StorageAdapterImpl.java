package cn.iocoder.cloud.module.cloudedge.adapter.impl;

import cn.iocoder.cloud.module.cloudedge.adapter.AbstractMonitorAdapter;
import cn.iocoder.cloud.module.cloudedge.adapter.MonitorType;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class StorageAdapterImpl extends AbstractMonitorAdapter {
    
    // 存储指标字段名称常量
    private static final String SIZE_FIELD = "size";
    private static final String FREE_FIELD = "free";
    private static final String USED_FIELD = "used";
    private static final String USAGE_FIELD = "usage";
    private static final String DISK_IO_DEVICE_FIELD = "diskIODevice";
    
    @Override
    protected void processMetricsData(Monitor monitor, CollectRep.MetricsData metricsData) {
        // 添加内存使用率和硬盘使用率
        int descr = getFieldIndex(metricsData, "descr");
        int size = getFieldIndex(metricsData, USAGE_FIELD);
        metricsData.getValuesList().forEach(metrics -> {
            String str = metrics.getColumns(descr);
            if (str.startsWith("C:\\")) {
                // 磁盘使用率
                monitor.setDiskUsage(metrics.getColumns(size));
            }
            if (str.startsWith("Physical Memory")) {
                // 内存使用率
                monitor.setMemUsage(metrics.getColumns(size));
            }
        });
    }

    @Override
    public MonitorType getMonitorType() {
        return MonitorType.STORAGES;
    }

    @Override
    public Map<String, List<Map<String, Object>>> handleMonitorTop10MetricsData(CollectRep.MetricsData metricsData) {
        return buildTop10MetricsData(metricsData, 
                DISK_IO_DEVICE_FIELD,
                SIZE_FIELD, FREE_FIELD, USED_FIELD, USAGE_FIELD);
    }
}
