package cn.iocoder.cloud.module.cloudedge.adapter.impl;

import cn.iocoder.cloud.module.cloudedge.adapter.AbstractMonitorAdapter;
import cn.iocoder.cloud.module.cloudedge.adapter.MonitorType;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 系统监控适配器实现
 */
@Slf4j
@Service
public class SystemAdapterImpl extends AbstractMonitorAdapter {

    @Override
    public MonitorType getMonitorType() {
        return MonitorType.SYSTEM;
    }

    @Override
    public Map<String, List<Map<String, Object>>> handleMonitorTop10MetricsData(CollectRep.MetricsData metricsData) {
        return null;
    }


    @Override
    protected void processMetricsData(Monitor monitor, CollectRep.MetricsData metricsData) {
        // 获取系统运行时间
        getFieldValue(metricsData, "responseTime")
                .ifPresent(monitor::setResponseTime);
        getFieldValue(metricsData, "uptime")
                .ifPresent(monitor::setUptime);
        // 获取电源状态
        getFieldValue(metricsData, "powerStatus")
                .ifPresent(monitor::setPowerStatus);

        // 获取系统风扇状态
        getFieldValue(metricsData, "systemFanStatus")
                .ifPresent(monitor::setSystemFanStatus);

        // 获取CPU使用率
        getFieldValue(metricsData, "cpuUsage")
                .ifPresent(monitor::setCpuUsed);

        // 获取内存使用率
        getFieldValue(metricsData, "memUsage")
                .ifPresent(monitor::setMemUsage);
    }
}
