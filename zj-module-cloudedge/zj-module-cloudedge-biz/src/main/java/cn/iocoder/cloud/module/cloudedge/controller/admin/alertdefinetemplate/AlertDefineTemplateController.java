package cn.iocoder.cloud.module.cloudedge.controller.admin.alertdefinetemplate;

import cn.iocoder.cloud.framework.alert.dao.AlertDefineDao;
import cn.iocoder.cloud.framework.alert.service.AlertDefineService;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.context.TenantContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.hertzbeat.common.entity.alerter.AlertDefine;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.cloud.module.cloudedge.controller.admin.alertdefinetemplate.vo.*;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.alertdefinetemplate.AlertDefineTemplateDO;
import cn.iocoder.cloud.module.cloudedge.convert.alertdefinetemplate.AlertDefineTemplateConvert;
import cn.iocoder.cloud.module.cloudedge.service.alertdefinetemplate.AlertDefineTemplateService;

@Tag(name = "管理后台 - 多维告警模板")
@RestController
@RequestMapping("/cloudedge/api/alert-define-template")
@Validated
public class AlertDefineTemplateController {

    @Resource
    private AlertDefineTemplateService alertDefineTemplateService;

    @Resource
    private AlertDefineDao alertDefineDao;

    @Resource
    private AlertDefineService alertDefineService;

    @PostMapping("/create")
    @Operation(summary = "创建多维告警模板")
//    @PreAuthorize("@ss.hasPermission('hzb:alert-define-template:create')")
    public CommonResult<Long> createAlertDefineTemplate(@Valid @RequestBody AlertDefineTemplateCreateReqVO createReqVO) {
        return success(alertDefineTemplateService.createAlertDefineTemplate(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新多维告警模板")
//    @PreAuthorize("@ss.hasPermission('hzb:alert-define-template:update')")
    public CommonResult<Boolean> updateAlertDefineTemplate(@Valid @RequestBody AlertDefineTemplateUpdateReqVO updateReqVO) {
        alertDefineTemplateService.updateAlertDefineTemplate(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除多维告警模板")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('hzb:alert-define-template:delete')")
    public CommonResult<Boolean> deleteAlertDefineTemplate(@RequestParam("id") Long id) {
        alertDefineTemplateService.deleteAlertDefineTemplate(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得多维告警模板")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('hzb:alert-define-template:query')")
    public CommonResult<AlertDefineTemplateRespVO> getAlertDefineTemplate(@RequestParam("id") Long id) {
        AlertDefineTemplateDO alertDefineTemplate = alertDefineTemplateService.getAlertDefineTemplate(id);
        return success(AlertDefineTemplateConvert.INSTANCE.convert(alertDefineTemplate));
    }

    @GetMapping("/list")
    @Operation(summary = "获得多维告警模板列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
//    @PreAuthorize("@ss.hasPermission('hzb:alert-define-template:query')")
    public CommonResult<List<AlertDefineTemplateRespVO>> getAlertDefineTemplateList(@RequestParam("ids") Collection<Long> ids) {
        List<AlertDefineTemplateDO> list = alertDefineTemplateService.getAlertDefineTemplateList(ids);
        return success(AlertDefineTemplateConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得多维告警模板分页")
//    @PreAuthorize("@ss.hasPermission('hzb:alert-define-template:query')")
    public CommonResult<PageResult<AlertDefineTemplateRespVO>> getAlertDefineTemplatePage(@Valid AlertDefineTemplatePageReqVO pageVO) {
        PageResult<AlertDefineTemplateDO> pageResult = alertDefineTemplateService.getAlertDefineTemplatePage(pageVO);
        return success(AlertDefineTemplateConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出多维告警模板 Excel")
//    @PreAuthorize("@ss.hasPermission('hzb:alert-define-template:export')")
    @OperateLog(type = EXPORT)
    public void exportAlertDefineTemplateExcel(@Valid AlertDefineTemplateExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<AlertDefineTemplateDO> list = alertDefineTemplateService.getAlertDefineTemplateList(exportReqVO);
        // 导出 Excel
        List<AlertDefineTemplateExcelVO> datas = AlertDefineTemplateConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "多维告警模板.xls", "数据", AlertDefineTemplateExcelVO.class, datas);
    }


    @PostMapping("/import-template")
    @Operation(summary = "模板导入到告警模板")
//    @PreAuthorize("@ss.hasPermission('monitor:alarm-config-template:import')")
    public CommonResult<Boolean> importTemplate(@Valid @RequestBody AlertDefineTemplateImportTemplateReqVO importTemplateReqVO) {
        LoginUser user = SecurityFrameworkUtils.getLoginUser();
        for (Long id : importTemplateReqVO.getIds()) {
            AlertDefineTemplateDO find = alertDefineTemplateService.getAlertDefineTemplate(id);
            AlertDefine where = new AlertDefine();
            where.setApp(find.getApp());
            where.setMetric(find.getMetric());
            where.setExpr(find.getExpr());
            long count = alertDefineDao.count((Specification<AlertDefine>) (root, query, criteriaBuilder) -> {
                List<javax.persistence.criteria.Predicate> predicates = new ArrayList<>();
                if (find.getApp() != null) {
                    predicates.add(criteriaBuilder.equal(root.get("app"), find.getApp()));
                }
                if (find.getMetric() != null) {
                    predicates.add(criteriaBuilder.equal(root.get("metric"), find.getMetric()));
                }
                if (find.getExpr() != null) {
                    predicates.add(criteriaBuilder.equal(root.get("expr"), find.getExpr()));
                }
                if (find.getPriority() != null) {
                    predicates.add(criteriaBuilder.equal(root.get("priority"), find.getPriority()));
                }
                return query.where(predicates.toArray(new javax.persistence.criteria.Predicate[0])).getRestriction();
            });

            if (count == 0L) {
                AlertDefine alertDefine = new AlertDefine();
                alertDefine.setName(find.getName());
                alertDefine.setApp(find.getApp());
                alertDefine.setExpr(find.getExpr());
                alertDefine.setMetric(find.getMetric());
                alertDefine.setField(find.getField());
                alertDefine.setTimes(find.getTimes());
                alertDefine.setPreset(false);
                alertDefine.setTemplate(find.getTemplate());
                alertDefine.setPriority(find.getPriority());
                alertDefine.setEnable(false);
                alertDefine.setTags(new ArrayList<>());
                alertDefine.setTenantId(Math.toIntExact(user != null ? user.getTenantId() : TenantContextHolder.getTenantId()));
                alertDefineService.validate(alertDefine, false);
                alertDefineService.addAlertDefine(alertDefine);
            }
        }
        return success(true);
    }

}
