package cn.iocoder.cloud.module.cloudedge.convert.collectorplatform;

import cn.iocoder.cloud.module.cloudedge.controller.admin.collectorplatform.vo.CollectorPlatformCreateReqVO;
import cn.iocoder.cloud.module.cloudedge.controller.admin.collectorplatform.vo.CollectorPlatformExcelVO;
import cn.iocoder.cloud.module.cloudedge.controller.admin.collectorplatform.vo.CollectorPlatformRespVO;
import cn.iocoder.cloud.module.cloudedge.controller.admin.collectorplatform.vo.CollectorPlatformUpdateReqVO;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.collectorplatform.CollectorPlatformDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 采集器平台关联 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface CollectorPlatformConvert {

    CollectorPlatformConvert INSTANCE = Mappers.getMapper(CollectorPlatformConvert.class);

    CollectorPlatformDO convert(CollectorPlatformCreateReqVO bean);

    CollectorPlatformDO convert(CollectorPlatformUpdateReqVO bean);

    CollectorPlatformRespVO convert(CollectorPlatformDO bean);

    List<CollectorPlatformRespVO> convertList(List<CollectorPlatformDO> list);

    PageResult<CollectorPlatformRespVO> convertPage(PageResult<CollectorPlatformDO> page);

    List<CollectorPlatformExcelVO> convertList02(List<CollectorPlatformDO> list);

}
