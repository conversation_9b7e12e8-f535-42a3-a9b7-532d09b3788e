package cn.iocoder.cloud.module.cloudedge.dal.dataobject.alertdefinetemplate;

import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 多维告警模板 DO
 *
 * <AUTHOR>
 */
@TableName("hzb_alert_define_template")
@KeySequence("hzb_alert_define_template_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlertDefineTemplateDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 告警名称
     */
    private String name;
    /**
     * app
     */
    private String app;
    /**
     * 规则
     */
    private String expr;
    /**
     * field
     */
    private String field;
    /**
     * 监控
     */
    private String metric;
    /**
     * 告警级别
     */
    private Byte priority;
    /**
     * 告警通知内容模版
     */
    private String template;
    /**
     * 出发次数
     */
    private Integer times;

}
