package cn.iocoder.cloud.module.cloudedge.service.monitormetricsinterfacedata;

import java.util.*;
import javax.validation.*;

import cn.iocoder.cloud.module.cloudedge.controller.admin.monitormetricsinterfacedata.vo.MonitorMetricsInterfaceDataCreateReqVO;
import cn.iocoder.cloud.module.cloudedge.controller.admin.monitormetricsinterfacedata.vo.MonitorMetricsInterfaceDataExportReqVO;
import cn.iocoder.cloud.module.cloudedge.controller.admin.monitormetricsinterfacedata.vo.MonitorMetricsInterfaceDataPageReqVO;
import cn.iocoder.cloud.module.cloudedge.controller.admin.monitormetricsinterfacedata.vo.MonitorMetricsInterfaceDataUpdateReqVO;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.monitormetricsinterfacedata.MonitorMetricsInterfaceDataDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 监控实时数据 Service 接口
 *
 * <AUTHOR>
 */
public interface MonitorMetricsInterfaceDataService {

    /**
     * 创建监控实时数据
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMonitorMetricsInterfaceData(@Valid MonitorMetricsInterfaceDataCreateReqVO createReqVO);

    /**
     * 更新监控实时数据
     *
     * @param updateReqVO 更新信息
     */
    void updateMonitorMetricsInterfaceData(@Valid MonitorMetricsInterfaceDataUpdateReqVO updateReqVO);

    /**
     * 删除监控实时数据
     *
     * @param id 编号
     */
    void deleteMonitorMetricsInterfaceData(Long id);

    /**
     * 获得监控实时数据
     *
     * @param id 编号
     * @return 监控实时数据
     */
    MonitorMetricsInterfaceDataDO getMonitorMetricsInterfaceData(Long id);

    /**
     * 获得监控实时数据列表
     *
     * @param ids 编号
     * @return 监控实时数据列表
     */
    List<MonitorMetricsInterfaceDataDO> getMonitorMetricsInterfaceDataList(Collection<Long> ids);

    /**
     * 获得监控实时数据分页
     *
     * @param pageReqVO 分页查询
     * @return 监控实时数据分页
     */
    PageResult<MonitorMetricsInterfaceDataDO> getMonitorMetricsInterfaceDataPage(MonitorMetricsInterfaceDataPageReqVO pageReqVO);

    /**
     * 获得监控实时数据列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 监控实时数据列表
     */
    List<MonitorMetricsInterfaceDataDO> getMonitorMetricsInterfaceDataList(MonitorMetricsInterfaceDataExportReqVO exportReqVO);

}
