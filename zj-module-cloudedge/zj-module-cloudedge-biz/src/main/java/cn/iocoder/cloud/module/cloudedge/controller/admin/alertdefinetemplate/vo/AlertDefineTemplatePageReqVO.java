package cn.iocoder.cloud.module.cloudedge.controller.admin.alertdefinetemplate.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 多维告警模板分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AlertDefineTemplatePageReqVO extends PageParam {

    @Schema(description = "告警名称")
    private String name;

    @Schema(description = "app")
    private String app;

    @Schema(description = "规则")
    private String expr;

    @Schema(description = "field")
    private String field;

    @Schema(description = "监控")
    private String metric;

    @Schema(description = "告警级别")
    private Byte priority;

    @Schema(description = "告警通知内容模版")
    private String template;

    @Schema(description = "出发次数")
    private Integer times;

}
