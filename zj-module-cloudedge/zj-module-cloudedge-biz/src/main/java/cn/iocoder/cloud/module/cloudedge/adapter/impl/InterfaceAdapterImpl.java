package cn.iocoder.cloud.module.cloudedge.adapter.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.cloud.framework.warehouse.dao.MonitorInfoDao;
import cn.iocoder.cloud.framework.warehouse.store.realtime.RealTimeDataReader;
import cn.iocoder.cloud.module.cloudedge.adapter.AbstractMonitorAdapter;
import cn.iocoder.cloud.module.cloudedge.adapter.MonitorType;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.constants.CommonConstants;
import org.apache.hertzbeat.common.constants.ResourceAppEnum;
import org.apache.hertzbeat.common.entity.dto.Field;
import org.apache.hertzbeat.common.entity.dto.MetricsData;
import org.apache.hertzbeat.common.entity.dto.Value;
import org.apache.hertzbeat.common.entity.dto.ValueRow;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.apache.hertzbeat.common.util.ResourceCategoryUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 接口监控适配器实现
 */
@Slf4j
@Service
public class InterfaceAdapterImpl extends AbstractMonitorAdapter {

    // 定义字段名称常量
    private static final String INTERFACE_NAME_FIELD = "interface_name";
    private static final String IN_OCTETS_FIELD = "in_octets";
    private static final String OUT_OCTETS_FIELD = "out_octets";
    private static final String IN_DISCARDS_FIELD = "in_discards";
    private static final String OUT_DISCARDS_FIELD = "out_discards";
    private static final String ADMIN_STATUS_FIELD = "admin_status";
    private static final String OPER_STATUS_FIELD = "oper_status";

    // 定义状态值常量
    private static final String STATUS_UP = "1";
    private static final String STATUS_DOWN = "2";
    private static final String STATUS_NO_DATA = "&nbsp;";

    @Autowired
    private RealTimeDataReader realTimeDataReader;
    @Autowired
    private MonitorInfoDao monitorInfoDao;

    @Override
    public MonitorType getMonitorType() {
        return MonitorType.LINUX_INTERFACE;
    }

    @Override
    public Optional<CollectRep.MetricsData> getMetricsData(List<CollectRep.MetricsData> metricsDataList) {
        // 优先查找 LINUX_INTERFACE 类型的数据
        Optional<CollectRep.MetricsData> linuxData = metricsDataList.stream()
                .filter(metricsData -> metricsData.getMetrics().equals(MonitorType.LINUX_INTERFACE.getType()))
                .findFirst();
        
        // 如果没有找到 LINUX_INTERFACE 数据，则查找 WINDOW_INTERFACES 数据
        if (linuxData.isEmpty()) {
            return metricsDataList.stream()
                    .filter(metricsData -> metricsData.getMetrics().equals(MonitorType.WINDOW_INTERFACES.getType()))
                    .findFirst();
        }
        
        return linuxData;
    }

    @Override
    public Map<String, List<Map<String, Object>>> handleMonitorTop10MetricsData(CollectRep.MetricsData metricsData) {
        List<Field> fields = metricsData.getFieldsList().stream().map(tmpField ->
                        Field.builder().name(tmpField.getName())
                                .type(Integer.valueOf(tmpField.getType()).byteValue())
                                .label(tmpField.getLabel())
                                .unit(tmpField.getUnit())
                                .build())
                .toList();
        List<ValueRow> valueRows = new LinkedList<>();
        for (CollectRep.ValueRow valueRow : metricsData.getValuesList()) {
            Map<String, String> labels = new HashMap<>(8);
            List<Value> values = new LinkedList<>();
            for (int i = 0; i < fields.size(); i++) {
                Field field = fields.get(i);
                String origin = valueRow.getColumns(i);
                if (CommonConstants.NULL_VALUE.equals(origin)) {
                    values.add(new Value());
                } else {
                    values.add(new Value(origin));
                    if (field.getLabel()) {
                        labels.put(field.getName(), origin);
                    }
                }
            }
            valueRows.add(ValueRow.builder().labels(labels).values(values).build());
        }
        List<String> app = new ArrayList<>();
        List<ResourceAppEnum> subEnums = ResourceCategoryUtil.getSubEnumsByCategory("os");
        for (ResourceAppEnum resourceAppEnum : subEnums) {
            if (resourceAppEnum.getValue() != "windows" && resourceAppEnum.getValue() != "linux_snmp") {
                app.add(resourceAppEnum.getValue());
            }
        }
        Monitor monitor = monitorInfoDao.getMonitorById(metricsData.getId());
        // 老数据
        CollectRep.MetricsData oldStorageData = realTimeDataReader.getOldCurrentMetricsData(metricsData.getId(),metricsData.getMetrics());;
        if (oldStorageData != null) {
            MetricsData.MetricsDataBuilder oldataBuilder = MetricsData.builder();
            oldataBuilder.id(oldStorageData.getId()).app(oldStorageData.getApp()).metrics(oldStorageData.getMetrics())
                    .time(oldStorageData.getTime());
            List<Field> oldfields = oldStorageData.getFieldsList().stream().map(tmpField ->
                            Field.builder().name(tmpField.getName())
                                    .type(Integer.valueOf(tmpField.getType()).byteValue())
                                    .label(tmpField.getLabel())
                                    .unit(tmpField.getUnit())
                                    .build())
                    .collect(Collectors.toList());
            oldataBuilder.fields(oldfields);
            List<ValueRow> oldValueRows = new LinkedList<>();
            for (CollectRep.ValueRow valueRow : oldStorageData.getValuesList()) {
                Map<String, String> labels = new HashMap<>(8);
                List<Value> values = new LinkedList<>();
                for (int i = 0; i < fields.size(); i++) {
                    Field field = fields.get(i);
                    String origin = valueRow.getColumns(i);
                    if (CommonConstants.NULL_VALUE.equals(origin)) {
                        values.add(new Value());
                    } else {
                        values.add(new Value(origin));
                        if (field.getLabel()) {
                            labels.put(field.getName(), origin);
                        }
                    }
                }
                oldValueRows.add(ValueRow.builder().labels(labels).values(values).build());
            }
            for (ValueRow valueRow : valueRows) {
                for (ValueRow oldvalueRow : oldValueRows) {
                    if (valueRow.getLabels().get("interface_name").equals(oldvalueRow.getLabels().get("interface_name"))) {
                        // 端口入方向
                        double t1 = 0;
                        double t2 = 0;
                        if (app.contains(monitor.getApp())) {
                            if (valueRow.getValues().get(1).getOrigin() != null) {
                                t1 = Double.parseDouble(valueRow.getValues().get(1).getOrigin());
                            }
                            if (oldvalueRow.getValues().get(1).getOrigin() != null) {
                                t2 = Double.parseDouble(oldvalueRow.getValues().get(1).getOrigin());
                            }
                        } else {
                            if (valueRow.getValues().get(6).getOrigin() != null) {
                                t1 = Double.parseDouble(valueRow.getValues().get(6).getOrigin());
                            }
                            if (oldvalueRow.getValues().get(6).getOrigin() != null) {
                                t2 = Double.parseDouble(oldvalueRow.getValues().get(6).getOrigin());
                            }
                        }
                        double in = 8 * (t1 - t2) / 60;

                        // 端口出方向
                        double t1out = 0;
                        double t2out = 0;
                        if (app.contains(monitor.getApp())) {
                            if (valueRow.getValues().get(2).getOrigin() != null) {
                                t1out = Double.parseDouble(valueRow.getValues().get(2).getOrigin());
                            }
                            if (oldvalueRow.getValues().get(2).getOrigin() != null) {
                                t2out = Double.parseDouble(oldvalueRow.getValues().get(2).getOrigin());
                            }
                        } else {
                            if (valueRow.getValues().get(9).getOrigin() != null) {
                                t1out = Double.parseDouble(valueRow.getValues().get(9).getOrigin());
                            }
                            if (oldvalueRow.getValues().get(9).getOrigin() != null) {
                                t2out = Double.parseDouble(oldvalueRow.getValues().get(9).getOrigin());
                            }
                        }

                        double out = 8 * (t1out - t2out) / 60;

                        List<Value> values = valueRow.getValues();
                        for (int i = 0; i < values.size(); i++) {
                            if (app.contains(monitor.getApp())) {
                                if (i == 1) {
                                    if (in < 0) {
                                        in = 0;
                                    }
                                    String formattedIn = String.format("%.4f", in);
                                    values.set(1, new Value(formattedIn));  // 使用 set 替代 add
                                }
                                if (i == 2) {
                                    if (out < 0) {
                                        out = 0;
                                    }
                                    String formattedIn = String.format("%.4f", out);
                                    values.set(2, new Value(formattedIn));  // 使用 set 替代 add
                                }
                            } else {
                                if (i == 6) {
                                    if (in < 0) {
                                        in = 0;
                                    }
                                    String formattedIn = String.format("%.4f", in);
                                    values.set(6, new Value(formattedIn));  // 使用 set 替代 add
                                }
                                if (i == 9) {
                                    if (out < 0) {
                                        out = 0;
                                    }
                                    String formattedIn = String.format("%.4f", out);
                                    values.set(9, new Value(formattedIn));  // 使用 set 替代 add
                                }
                            }

                        }
                    }
                }
            }
        }
        Iterable<? extends CollectRep.ValueRow> valueRowIterable = valueRows.stream()
                .map(valueRow -> {
                    Iterable<String> columns = valueRow.getValues().stream()
                           .map(value -> value.getOrigin() != null ? value.getOrigin() : CommonConstants.NULL_VALUE)
                           .collect(Collectors.toList());
                    return CollectRep.ValueRow.newBuilder()
                            .addAllColumns(columns)
                            .build();
                })
                .collect(Collectors.toList());
        CollectRep.MetricsData data = CollectRep.MetricsData.newBuilder()
                .setId(metricsData.getId())
                .setApp(metricsData.getApp())
                .setMetrics(metricsData.getMetrics())
                .setTime(metricsData.getTime())
                .addAllFields(metricsData.getFieldsList())
                .addAllValues(valueRowIterable)
                .build();

        return buildTop10MetricsData(data,
                INTERFACE_NAME_FIELD,
                IN_OCTETS_FIELD, OUT_OCTETS_FIELD,IN_DISCARDS_FIELD,OUT_DISCARDS_FIELD);

    }

    @Override
    protected void processMetricsData(Monitor monitor, CollectRep.MetricsData metricsData) {
        int adminStatusIndex = getFieldIndex(metricsData, ADMIN_STATUS_FIELD);
        int operStatusIndex = getFieldIndex(metricsData, OPER_STATUS_FIELD);

        if (adminStatusIndex == -1 || operStatusIndex == -1) {
            log.warn("未找到接口状态字段");
            return;
        }

        // 统计接口状态
        Map<String, AtomicInteger> statusCounters = initStatusCounters();

        // 处理所有接口数据
        metricsData.getValuesList().forEach(valueRow -> {
            // 处理配置状态
            updateStatusCounter(statusCounters, "configRun", "configStop", "configNoData", 
                    valueRow.getColumns(adminStatusIndex));

            // 处理运行状态
            updateStatusCounter(statusCounters, "currentRun", "currentStop", "currentNoData", 
                    valueRow.getColumns(operStatusIndex));
        });

        // 设置监控对象的接口状态统计
        monitor.setInterfaceConfigRun(statusCounters.get("configRun").get());
        monitor.setInterfaceConfigStop(statusCounters.get("configStop").get());
        monitor.setInterfaceConfigNoData(statusCounters.get("configNoData").get());
        monitor.setInterfaceCurrentRun(statusCounters.get("currentRun").get());
        monitor.setInterfaceCurrentStop(statusCounters.get("currentStop").get());
        monitor.setInterfaceCurrentNoData(statusCounters.get("currentNoData").get());
    }
    
    /**
     * 初始化状态计数器
     */
    private Map<String, AtomicInteger> initStatusCounters() {
        Map<String, AtomicInteger> counters = new HashMap<>();
        counters.put("configRun", new AtomicInteger(0));
        counters.put("configStop", new AtomicInteger(0));
        counters.put("configNoData", new AtomicInteger(0));
        counters.put("currentRun", new AtomicInteger(0));
        counters.put("currentStop", new AtomicInteger(0));
        counters.put("currentNoData", new AtomicInteger(0));
        return counters;
    }
    
    /**
     * 更新状态计数器
     */
    private void updateStatusCounter(
            Map<String, AtomicInteger> counters, 
            String upKey, 
            String downKey, 
            String noDataKey, 
            String statusValue) {
        
        switch (statusValue) {
            case STATUS_UP -> counters.get(upKey).incrementAndGet();
            case STATUS_DOWN -> counters.get(downKey).incrementAndGet();
            case STATUS_NO_DATA -> counters.get(noDataKey).incrementAndGet();
        }
    }
}
