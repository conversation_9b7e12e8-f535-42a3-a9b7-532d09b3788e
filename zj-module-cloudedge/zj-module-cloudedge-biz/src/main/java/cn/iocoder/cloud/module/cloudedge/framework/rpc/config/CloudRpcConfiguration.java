package cn.iocoder.cloud.module.cloudedge.framework.rpc.config;

import cn.iocoder.zj.module.infra.api.file.FileApi;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.monitor.api.tag.MonitorTagApi;
import cn.iocoder.zj.module.monitor.api.topology.TopologyApi;
import cn.iocoder.zj.module.om.api.asset.MonitorassetApi;
import cn.iocoder.zj.module.report.api.subscription.dto.ReportSubscriptionApi;
import cn.iocoder.zj.module.system.api.licence.LicenceApi;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.usercertificate.UsercertificateApi;
import cn.iocoder.zj.module.system.api.wechat.WeChatSendApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName : RpcConfiguration  //类名
 * @Description : RPC 接口  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/5/23  10:38
 */

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {PlatformconfigApi.class,
        LicenceApi.class, RoleApi.class,
        PermissionApi.class,
        AlarmConfigApi.class,
        UsercertificateApi.class,
        ReportSubscriptionApi.class,
        WeChatSendApi.class,
        TopologyApi.class, FileApi.class, MonitorassetApi.class, MonitorTagApi.class})
public class CloudRpcConfiguration {
}
