package cn.iocoder.cloud.module.cloudedge.controller.admin.monitormetricsinterfacedata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 监控实时数据 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class MonitorMetricsInterfaceDataExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("监控id")
    private Long monitorId;

    @ExcelProperty("指标")
    private String metrics;

    @ExcelProperty("interfaceIndex")
    private String interfaceIndex;

    @ExcelProperty("接口名称")
    private String interfaceName;

    @ExcelProperty("接口描述")
    private String alias;

    @ExcelProperty("管理口状态")
    private String adminStatus;

    @ExcelProperty("操作口状态")
    private String operStatus;

}
