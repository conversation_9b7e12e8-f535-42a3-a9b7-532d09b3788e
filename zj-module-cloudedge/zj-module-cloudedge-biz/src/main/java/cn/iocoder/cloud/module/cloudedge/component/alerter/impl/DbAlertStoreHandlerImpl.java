/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.module.cloudedge.component.alerter.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.URLUtil;
import cn.iocoder.cloud.framework.alert.dal.redis.HerzbeatMonitorRedisDAO;
import cn.iocoder.cloud.framework.alert.service.AlertDefineService;
import cn.iocoder.cloud.framework.alert.service.AlertService;
import cn.iocoder.cloud.module.cloudedge.component.alerter.AlertStoreHandler;
import cn.iocoder.cloud.module.cloudedge.service.MonitorService;
import cn.iocoder.zj.framework.common.exception.ErrorCode;
import cn.iocoder.zj.framework.common.exception.IgnoreException;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.DingDingAuthorizationDTO;
import cn.iocoder.zj.framework.common.pojo.UserBindDTO;
import cn.iocoder.zj.framework.common.util.collection.CollectionUtils;
import cn.iocoder.zj.framework.common.util.dingtalk.DingTalkUtil;
import cn.iocoder.zj.framework.common.util.dingtalk.QyWxUtils;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.report.api.subscription.dto.ReportSubscriptionApi;
import cn.iocoder.zj.module.report.api.subscription.dto.ReportSubscriptionDTO;
import cn.iocoder.zj.module.system.api.mail.MailSendApi;
import cn.iocoder.zj.module.system.api.mail.dto.MailSendSingleToUserReqDTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import cn.iocoder.zj.module.system.api.wechat.WeChatSendApi;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.StringUtils;
import io.netty.util.internal.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.constants.CommonConstants;
import org.apache.hertzbeat.common.entity.alerter.Alert;
import org.apache.hertzbeat.common.entity.alerter.AlertDefine;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 报警持久化 - 落地到数据库
 * Alarm data persistence - landing in the database
 *
 * <AUTHOR> href="mailto:<EMAIL>">Musk.Chen</a>
 */
@Component
@RequiredArgsConstructor
@Slf4j
final class DbAlertStoreHandlerImpl implements AlertStoreHandler {

    private final MonitorService monitorService;

    private final AlertService alertService;

    @Autowired
    private PlatformconfigApi platformconfigApi;

    @Autowired
    private AlertDefineService alertDefineService;
    @Resource
    private AlarmConfigApi alarmConfigApi;

    @Autowired
    private ReportSubscriptionApi reportSubscriptionApi;

    @Autowired
    private WeChatSendApi weChatSendApi;

    @Autowired
    private MailSendApi mailSendApi;

    @Resource
    HerzbeatMonitorRedisDAO herzbeatMonitorRedisDAO;

    @Resource
    private RedisTemplate redisTemplate;

    @Value("${cloud-domain.url}")
    private String cloudDomain;

    @Override
    public void store(Alert alert) {
        if (StringUtils.isNotEmpty(alert.getAlarmId())) {
            AlertDefine alertDefine = alertDefineService.getAlertDefine(Long.parseLong(alert.getAlarmId()));
            alert.setAlarmConfigName(alertDefine.getName());
            alert.setAlarmRule(StringUtils.isNotEmpty(alertDefine.getExpr())?alertDefine.getExpr():"监控异常");
        }
        Map<String, String> tags = alert.getTags();
        String monitorIdStr = tags != null ? tags.get(CommonConstants.TAG_MONITOR_ID) : null;
        JSONObject monitorObj = new JSONObject();
        if (monitorIdStr != null) {
            long monitorId = Long.parseLong(monitorIdStr);
            Monitor monitor = monitorService.getMonitor(monitorId);
            if (monitor == null) {
                log.warn("Dispatch alarm the monitorId: {} not existed, ignored. target: {}.", monitorId, alert.getTarget());
                return;
            }
            if(herzbeatMonitorRedisDAO.get("herzbeat_monitor:"+monitorId) != null) {
                monitorObj = herzbeatMonitorRedisDAO.get("herzbeat_monitor:" + monitorId);
                alert.setPlatformId(monitorObj.getLong("platformId"));
                alert.setPlatformName(monitorObj.getString("platformName"));
            }else {
                alert.setPlatformId(monitor.getPlatformId());
                alert.setPlatformName(monitor.getPlatformName());
                herzbeatMonitorRedisDAO.set("herzbeat_monitor:"+monitorId,monitor);
            }
            if (!tags.containsKey(CommonConstants.TAG_MONITOR_NAME)) {
                tags.put(CommonConstants.TAG_MONITOR_NAME, monitor.getName());
            }
            if (!tags.containsKey(CommonConstants.TAG_MONITOR_HOST)) {
                tags.put(CommonConstants.TAG_MONITOR_HOST, monitor.getHost());
            }
            if (monitor.getStatus() == CommonConstants.MONITOR_PAUSED_CODE) {
                // When monitoring is not monitored, ignore and silence its alarm messages
                return;
            }
            if (CommonConstants.AVAILABILITY.equals(alert.getTarget())) {
                if (alert.getStatus() == CommonConstants.ALERT_STATUS_CODE_PENDING && monitor.getStatus() == CommonConstants.MONITOR_UP_CODE) {
                    // Availability Alarm Need to change the monitoring status to unavailable
                    monitorService.updateMonitorStatus(monitor.getId(), CommonConstants.MONITOR_DOWN_CODE);
                } else if (alert.getStatus() == CommonConstants.ALERT_STATUS_CODE_RESTORED && monitor.getStatus() == CommonConstants.MONITOR_DOWN_CODE) {
                    // If the alarm is restored, the monitoring state needs to be restored
                    monitorService.updateMonitorStatus(monitorId, CommonConstants.MONITOR_UP_CODE);
                }
            }
        } else {
            log.debug("store extern alert content: {}.", alert);
        }
        if (tags != null && tags.containsKey(CommonConstants.IGNORE)) {
            throw new IgnoreException("Ignore this alarm.");
        }
        // Alarm store db
        Map<String, Object> result = alertService.addAlert(alert);
        //todo 发送通知放这里是为了确保告警数据已经新增到数据库的有效告警
        if (String.valueOf(result.get("type")).equals("insert")) {
            List<PlatformconfigDTO> platformList = platformconfigApi.getPlatList().getData();
            Map<Long, PlatformconfigDTO> platformMap = CollectionUtils.convertMap(platformList, PlatformconfigDTO::getId);
            List<ReportSubscriptionDTO> report = reportSubscriptionApi.getAlarmSubscription().getData();

            if (!CollectionUtil.isEmpty(report)) {
                UserBindDTO userBindDO = JsonUtils.parseObject(redisTemplate.opsForValue().get("app_key_secret:app").toString(), UserBindDTO.class);
                Long alertId = Long.parseLong(String.valueOf(result.get("alertId")));
                
                // 添加通知去重逻辑，避免重复发送通知
                // 使用监控ID+告警ID+平台ID生成唯一键，确保相同告警不重复通知
                String notificationKey = "alert_notification:" + alert.getPlatformId() + ":" + 
                    (monitorIdStr != null ? monitorIdStr : "unknown") + ":" + 
                    (alert.getAlarmId() != null ? alert.getAlarmId() : "unknown") + ":" +
                    alert.getTarget();
                // 原子操作：如果键不存在则设置，存在则返回false
                Boolean lockAcquired = redisTemplate.opsForValue().setIfAbsent(
                    notificationKey, "sent", 5, TimeUnit.MINUTES);
                if (lockAcquired != null && lockAcquired) {
                    log.info("Sending alert notification for alertId: {}, notificationKey: {}", alertId, notificationKey);
                    report.forEach(item -> {
                        if (StringUtils.isNotEmpty(item.getPlatformId()) && item.getPlatformId().contains(Convert.toStr(alert.getPlatformId()))) {
                            sendNotification(item, alertId, platformMap, userBindDO);
                        }
                    });
                } else {
                    log.warn("Alert notification already sent for alertId: {}, notificationKey: {}, skipping duplicate notification", 
                        alertId, notificationKey);
                }
            }
        }

    }

    private void sendNotification(ReportSubscriptionDTO item, Long alertId, Map<Long, PlatformconfigDTO> platformMap, UserBindDTO userBindDO) {
        try {
            if (item.getWechatState() == 1 && StringUtils.isNotEmpty(item.getOpenId())) {
                sendWechat(item, alertId, platformMap);
                log.info("发送公众号成功: {}", alertId);
            }

            if (userBindDO.getEmailState() == 1 && item.getEmailState() == 1 && StringUtils.isNotEmpty(item.getEmail())) {
                sendEmail(item, alertId, platformMap);
                log.info("发送邮箱成功: {}", alertId);
            }

            if (userBindDO.getDingtalkState() == 1 &&
                    item.getDingtalkState() == 1 && jodd.util.StringUtil.isNotEmpty(item.getDingtalkPhone()) && BeanUtil.isNotEmpty(userBindDO)) {
                sendDingtalk(item, alertId, userBindDO);
                log.info("发送钉钉成功: {}", item.getDingtalkPhone());
            }

            if (userBindDO.getWxState() == 1 && item.getWecomState() == 1 && jodd.util.StringUtil.isNotEmpty(item.getWecomPhone()) && BeanUtil.isNotEmpty(userBindDO)) {
                sendWeWork(item, alertId, userBindDO);
                log.info("发送企微成功: {}", item.getWecomPhone());
            }
        } catch (Exception e) {
            // 记录日志并抛出更具体的异常
            log.info("发送通知失败: {},{}", alertId, JsonUtils.toJsonString(item), e);
        }
    }

    private void sendEmail(ReportSubscriptionDTO subscription, Long alertId, Map<Long, PlatformconfigDTO> platformMap) {
        Alert item = alarmConfigApi.getAlertInfoById(alertId).getData();
        if (ObjectUtil.isEmpty(item)) {
            return;
        }
        Map<String, Object> templateParams = new HashMap<>();
        //邮箱发送
        Map<String, Object> target1 = JSON.parseObject(JSON.toJSONString(item), Map.class);
        templateParams.put("alarmName", item.getMonitorName());
        templateParams.put("productsName", item.getMonitorName());
        templateParams.put("value", "-");
        templateParams.put("time", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        templateParams.put("app", item.getApp());
        String priority1 = String.valueOf(target1.get("priority"));
        templateParams.put("priority", StringUtil.isNullOrEmpty(priority1)?"":(priority1.equals("0")?"严重":priority1.equals("1")?"警告":"提示"));
        templateParams.put("times",item.getTimes());
        templateParams.put("content",item.getContent());
        templateParams.put("platformName",item.getPlatformName());
        String  address = StringUtil.isNullOrEmpty(platformMap.get(item.getPlatformId()).getAddress())?"":platformMap.get(item.getPlatformId()).getAddress();
        templateParams.put("address",address);
        MailSendSingleToUserReqDTO reqDTO = new MailSendSingleToUserReqDTO();
        String name = "资源告警通知";
        CommonResult<String> commonResult = mailSendApi.getTemplateByName(name);
        reqDTO.setTemplateCode(commonResult.getData());
        reqDTO.setTemplateParams(templateParams);
        reqDTO.setMail(subscription.getEmail());
        reqDTO.setUserId(subscription.getUserId());
        Long id = mailSendApi.sendSingleMailToAdmin(reqDTO).getData();
        System.out.println("邮件参数："+templateParams+";收件邮箱："+subscription.getEmail()+"邮件推送编号："+id+"alertId:"+item.getId());
    }

    private void sendWechat(ReportSubscriptionDTO subscription,Long alertId,Map<Long, PlatformconfigDTO> platformMap) {
        Alert alert = alarmConfigApi.getAlertInfoById(alertId).getData();
        if(ObjectUtil.isEmpty(alert)){
            return;
        }
        Map<String, Object> templateParams = new HashMap<>();
        //点击详情跳转的地址
        String redictUrl = "";
        try {
            String encodedUrl = URLEncoder.encode(cloudDomain, "UTF-8");
            String parameter = URLUtil.encode("/pages/alarm/info?id="+alertId);
            redictUrl = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxe3d3d4577c6c41b6&redirect_uri="+encodedUrl+"&response_type=code&scope=snsapi_userinfo&state="+parameter+"&connect_redirect=1#wechat_redirect";
        } catch (UnsupportedEncodingException e) {
            System.out.println("Error encoding URL: " + e.getMessage());
        }
        String priorityDescription = (alert.getPriority() == 0) ? "严重" :
                ((alert.getPriority() == 1) ? "警告" :
                        ((alert.getPriority() == 2) ? "提示" : ""));
        templateParams.put("monitorName", alert.getMonitorName());
        templateParams.put("content", "设备状态异常,注意查看");
        templateParams.put("openId", subscription.getOpenId());
        templateParams.put("url", redictUrl);
        templateParams.put("platformName", alert.getPlatformName());
        templateParams.put("priority",priorityDescription);
        weChatSendApi.sendSingleWeChatToMember(templateParams);
    }


    @SneakyThrows
    private void sendDingtalk(ReportSubscriptionDTO item, long alertId, UserBindDTO userBindDO) {
        String accessToken = DingTalkUtil.getAccessToken(userBindDO.getDingtalkAppKey(), userBindDO.getDingtalkAppSecret());
        if (jodd.util.StringUtil.isEmpty(accessToken)) {
            log.info("钉钉token获取为空" + accessToken);
            throw new RuntimeException("钉钉token获取为空");
        }
        Alert alert = alarmConfigApi.getAlertInfoById(alertId).getData();
        if (ObjectUtil.isEmpty(alert)) {
            return;
        }
        //点击详情跳转的地址
        String redictUrl = "";
        DingDingAuthorizationDTO dingDingAuthorization = new DingDingAuthorizationDTO();
        dingDingAuthorization.setAppKey(userBindDO.getDingtalkAppKey());
        dingDingAuthorization.setAppSecret(userBindDO.getDingtalkAppSecret());
        dingDingAuthorization.setAgentId(userBindDO.getDingtalkAgentId());
        dingDingAuthorization.setMobile(item.getDingtalkPhone());
        //告警时间
        String format = DateUtil.format(new Date(), "yyyy年MM月dd日 HH:mm");
        dingDingAuthorization.setWarningTime(format);
        //告警对象
        dingDingAuthorization.setWarningObject(alert.getMonitorName());
        //告警项目
        dingDingAuthorization.setWarningItem("设备状态异常,注意查看");
        dingDingAuthorization.setUrl(redictUrl);
        DingTalkUtil.sendDingDingMessage(dingDingAuthorization, accessToken, "Oa");
    }

    @SneakyThrows
    private void sendWeWork(ReportSubscriptionDTO item, long alertId, UserBindDTO userBindDO) {
        String accessToken = QyWxUtils.getToken(userBindDO.getWxCorpid(), userBindDO.getWxCorpsecret());
        if (jodd.util.StringUtil.isEmpty(accessToken)) {
            log.info("企微token获取为空" + accessToken);
            throw exception(new ErrorCode(1002000008, "企微token获取为空"));
        }

        Alert alert = alarmConfigApi.getAlertInfoById(alertId).getData();
        if (ObjectUtil.isEmpty(alert)) {
            return;
        }
        //点击详情跳转的地址
        String redictUrl = "www";
        try {
            String encodedUrl = URLEncoder.encode(cloudDomain, "UTF-8");
            String parameter = URLUtil.encode("/pages/alarm/info?id=" + alertId);
            redictUrl = "";
        } catch (UnsupportedEncodingException e) {
            System.out.println("Error encoding URL: " + e.getMessage());
        }
        DingDingAuthorizationDTO dingDingAuthorization = new DingDingAuthorizationDTO();
        dingDingAuthorization.setAppKey(userBindDO.getWxCorpid());
        dingDingAuthorization.setAppSecret(userBindDO.getWxCorpsecret());
        dingDingAuthorization.setAgentId(userBindDO.getWxAgentId());
        dingDingAuthorization.setMobile(item.getWecomPhone());
        //告警时间
        String format = DateUtil.format(new Date(), "yyyy年MM月dd日 HH:mm");
        dingDingAuthorization.setWarningTime(format);
        //告警对象
        dingDingAuthorization.setWarningObject("设备状态异常,注意查看");
        //告警项目
        dingDingAuthorization.setWarningItem(alert.getMonitorName());
        dingDingAuthorization.setUrl("www");
        QyWxUtils.sendTextMsg(accessToken, dingDingAuthorization, "text");

    }

}
