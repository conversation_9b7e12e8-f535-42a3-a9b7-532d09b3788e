package cn.iocoder.cloud.module.cloudedge.adapter;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 监控适配器服务
 * 负责管理和调用各类监控适配器
 */
@Getter
@Slf4j
@Component
public class MonitorsAdapterService implements ApplicationContextAware {

    /**
     * 监控适配器映射表
     * -- GETTER --
     *  获取所有监控适配器
     *
     * @return 监控适配器映射表

     */
    private final Map<String, MonitorsAdapter> monitorsAdapterMap = new ConcurrentHashMap<>();

    /**
     * 处理监控指标数据
     *
     * @param monitorType 监控类型
     * @param monitor 监控对象
     * @param metricsDataList 指标数据列表
     */
    public void handleMonitorMetricsData(String monitorType, Monitor monitor, List<CollectRep.MetricsData> metricsDataList) {
        try {
            MonitorsAdapter monitorsAdapter = Optional.ofNullable(monitorsAdapterMap.get(monitorType))
                    .orElse(null);
//                    .orElseThrow(() -> new IllegalArgumentException("未找到对应的监控类型: " + monitorType));
            if (monitorsAdapter != null) {
                monitorsAdapter.handleMonitorMetricsData(monitor, metricsDataList);
            }
        } catch (Exception e) {
            log.error("处理监控数据异常, 监控类型: {}, 监控ID: {}", monitorType, monitor.getId(), e);
        }
    }


    /**
     * 处理TOP10监控指标数据 监控数据
     *
     * @param monitorType 监控类型@param monitorType 监控类型
     * @param metricsData 指标数据
     * @return TOP10监控指标数据
     */
    public Map<String, List<Map<String, Object>>>  handleTop10MonitorMetricsData(String monitorType, CollectRep.MetricsData metricsData) {
        try {
            MonitorsAdapter monitorsAdapter = monitorsAdapterMap.get(monitorType);
//                    .orElseThrow(() -> new IllegalArgumentException("未找到对应的监控类型: " + monitorType));
            if (monitorsAdapter!= null) {
                return monitorsAdapter.handleMonitorTop10MetricsData(metricsData);
            }
            return null;
        }catch (Exception e) {
            log.error("处理TOP10监控数据异常, 监控类型: {}", monitorType, e);
            return null;
        }
    }

    /**
     * 根据设备类型处理监控指标数据
     *
     * @param category 设备类别
     * @param monitor 监控对象
     * @param metricsDataList 指标数据列表
     */
    public void handleMonitorMetricsDataByCategory(String category, Monitor monitor, List<CollectRep.MetricsData> metricsDataList) {
        if (monitor == null || metricsDataList == null || metricsDataList.isEmpty()) {
            return;
        }
    
        switch (category) {
            case "network", "firewall" -> {
                // 网络设备和防火墙获取CPU、内存、接口状态等指标
                handleMonitorMetricsData(MonitorType.CPU.getType(), monitor, metricsDataList);
                handleMonitorMetricsData(MonitorType.MEMORY.getType(), monitor, metricsDataList);
                handleMonitorMetricsData(MonitorType.LINUX_INTERFACE.getType(), monitor, metricsDataList);
                handleMonitorMetricsData(MonitorType.WINDOW_INTERFACES.getType(), monitor, metricsDataList);
                handleMonitorMetricsData(MonitorType.SYSTEM.getType(), monitor, metricsDataList);
            }
            case "stored" -> {
                // 存储设备获取电源、风扇、硬盘状态
                handleMonitorMetricsData(MonitorType.SYSTEM.getType(), monitor, metricsDataList);
                handleMonitorMetricsData(MonitorType.DISK.getType(), monitor, metricsDataList);
            }
            case "os" -> {
                // 操作系统获取CPU、内存、磁盘使用率
                handleMonitorMetricsData(MonitorType.CPU.getType(), monitor, metricsDataList);
                handleMonitorMetricsData(MonitorType.MEMORY.getType(), monitor, metricsDataList);
                handleMonitorMetricsData(MonitorType.DISK_FREE.getType(), monitor, metricsDataList);
                handleMonitorMetricsData(MonitorType.SYSTEM.getType(), monitor, metricsDataList);
                handleMonitorMetricsData(MonitorType.STORAGES.getType(), monitor, metricsDataList);
            }
            default -> log.warn("未知的设备类别: {}", category);
        }
    }



    public void handleMonitorOSByCategory(String category, Monitor monitor, List<CollectRep.MetricsData> metricsDataList) {
        if (monitor == null || metricsDataList == null || metricsDataList.isEmpty()) {
            return;
        }

        switch (category) {
            case "os" -> {
                // 操作系统获取CPU、内存、磁盘使用率
                handleMonitorMetricsData(MonitorType.CPU.getType(), monitor, metricsDataList);
                handleMonitorMetricsData(MonitorType.MEMORY.getType(), monitor, metricsDataList);
                handleMonitorMetricsData(MonitorType.DISK_FREE.getType(), monitor, metricsDataList);
                handleMonitorMetricsData(MonitorType.SYSTEM.getType(), monitor, metricsDataList);
                handleMonitorMetricsData(MonitorType.STORAGES.getType(), monitor, metricsDataList);
            }
            default -> log.warn("未知的设备类别: {}", category);
        }


    }



    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, MonitorsAdapter> tempMap = applicationContext.getBeansOfType(MonitorsAdapter.class);
        tempMap.values().forEach(adapter -> {
            String type = adapter.getMonitorType().getType();
            monitorsAdapterMap.put(type, adapter);
            log.info("注册监控适配器: {}", type);
        });
    }
}
