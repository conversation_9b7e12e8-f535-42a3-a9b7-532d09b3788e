package cn.iocoder.cloud.module.cloudedge.config;

import cn.iocoder.cloud.module.cloudedge.scheduler.ConsistentHash;
import cn.iocoder.cloud.module.cloudedge.scheduler.SchedulerProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * scheduler config
 * <AUTHOR>
 */
@Configuration
@AutoConfigureAfter(value = {SchedulerProperties.class})
@Slf4j
public class SchedulerConfig {
    
    @Bean
    public ConsistentHash consistentHasInstance() {
        return new ConsistentHash();
    }

}
