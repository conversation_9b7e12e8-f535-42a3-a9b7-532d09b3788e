package cn.iocoder.cloud.module.cloudedge.convert.alertdefinetemplate;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.cloud.module.cloudedge.controller.admin.alertdefinetemplate.vo.*;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.alertdefinetemplate.AlertDefineTemplateDO;

/**
 * 多维告警模板 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface AlertDefineTemplateConvert {

    AlertDefineTemplateConvert INSTANCE = Mappers.getMapper(AlertDefineTemplateConvert.class);

    AlertDefineTemplateDO convert(AlertDefineTemplateCreateReqVO bean);

    AlertDefineTemplateDO convert(AlertDefineTemplateUpdateReqVO bean);

    AlertDefineTemplateRespVO convert(AlertDefineTemplateDO bean);

    List<AlertDefineTemplateRespVO> convertList(List<AlertDefineTemplateDO> list);

    PageResult<AlertDefineTemplateRespVO> convertPage(PageResult<AlertDefineTemplateDO> page);

    List<AlertDefineTemplateExcelVO> convertList02(List<AlertDefineTemplateDO> list);

}
