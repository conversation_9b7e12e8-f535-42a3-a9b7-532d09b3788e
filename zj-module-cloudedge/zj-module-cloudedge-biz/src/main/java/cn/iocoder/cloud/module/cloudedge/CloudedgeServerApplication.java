package cn.iocoder.cloud.module.cloudedge;


import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

import javax.annotation.PostConstruct;


@SpringBootApplication
@EnableJpaAuditing

@ComponentScan(basePackages = {"cn.iocoder.cloud", "org.apache.hertzbeat"})
@EnableJpaRepositories(basePackages = {"cn.iocoder.cloud", "org.apache.hertzbeat"})
@EntityScan(basePackages = {"cn.iocoder.cloud", "org.apache.hertzbeat"})
@ConfigurationPropertiesScan(basePackages = {"cn.iocoder.cloud.framework"})
public class CloudedgeServerApplication {
    public static void main(String[] args) {
        SpringApplication.run(CloudedgeServerApplication.class, args);
    }
    @PostConstruct
    public void init() {
        System.setProperty("jdk.jndi.object.factoriesFilter", "!com.zaxxer.hikari.HikariJNDIFactory");
    }
}