/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.module.cloudedge.service.impl;

import cn.iocoder.cloud.framework.warehouse.store.history.HistoryDataReader;
import cn.iocoder.cloud.framework.warehouse.store.realtime.RealTimeDataReader;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.herzbeat.BulletinMetricsData;
import cn.iocoder.cloud.module.cloudedge.dal.mysql.dao.BulletinDao;
import cn.iocoder.cloud.module.cloudedge.service.BulletinService;
import cn.iocoder.cloud.module.cloudedge.service.MonitorService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hertzbeat.common.entity.manager.Bulletin;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;

/**
 * Bulletin Service Implementation
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class BulletinServiceImpl implements BulletinService {

    private static final String NO_DATA = "No Data";

    private static final String EMPTY_STRING = "";

    @Autowired
    private BulletinDao bulletinDao;

    @Autowired
    private MonitorService monitorService;

    @Autowired
    private RealTimeDataReader realTimeDataReader;

    @Autowired
    Optional<HistoryDataReader> historyDataReader;

    /**
     * validate Bulletin
     */
    @Override
    public void validate(Bulletin bulletin) throws IllegalArgumentException {
        if (bulletin == null) {
            throw new IllegalArgumentException("Bulletin cannot be null");
        }
        if (bulletin.getApp() == null || bulletin.getApp().isEmpty()) {
            throw new IllegalArgumentException("Bulletin app cannot be null or empty");
        }
        if (bulletin.getFields() == null || bulletin.getFields().isEmpty()) {
            throw new IllegalArgumentException("Bulletin fields cannot be null or empty");
        }
//        if (bulletin.getMonitorIds() == null || bulletin.getMonitorIds().isEmpty()) {
//            throw new IllegalArgumentException("Bulletin monitorIds cannot be null or empty");
//        }
//        if (bulletinDao.countByName(bulletin.getName()) > 0) {
//            throw new IllegalArgumentException("Bulletin name duplicated");
//        }
    }

    /**
     * Save Bulletin
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editBulletin(Bulletin bulletin) {
        Optional<Bulletin> optional = bulletinDao.findById(bulletin.getId());
        if (optional.isEmpty()) {
            throw new IllegalArgumentException("Bulletin not found");
        }
        bulletinDao.save(bulletin);
    }

    /**
     * Add Bulletin
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addBulletin(Bulletin bulletin) {
        //根据 app 获取Monitor里所有的monitorId
        List<Monitor> monitors = monitorService.getMonitorByApp(bulletin.getApp());
        if (!monitors.isEmpty()){
            List<Long> monitorIds = monitors.stream().map(Monitor::getId).toList();
            bulletin.setMonitorIds(monitorIds);
        }
        bulletinDao.save(bulletin);
    }

    /**
     * deal with the bulletin
     */
    @Override
    public BulletinMetricsData buildBulletinMetricsData(Long monitorId, String app) {
        //根据app 获取bulletin
//        Optional<Bulletin> optional = bulletinDao.findById(id);
//        if (optional.isEmpty()) {
//            throw new IllegalArgumentException("Bulletin not found");
//        }
//        Bulletin bulletin = optional.get();
        Bulletin bulletin = bulletinDao.findByApp(app);
        if (bulletin == null) {
            throw new IllegalArgumentException("Bulletin not found");
        }
        BulletinMetricsData.BulletinMetricsDataBuilder contentBuilder = BulletinMetricsData.builder()
                .name(bulletin.getName());
        List<BulletinMetricsData.Data> dataList = new ArrayList<>();
        //bulletin 里的MonitorIds 是否包含monitorId
        if (bulletin.getMonitorIds().contains(monitorId)) {
            Monitor monitor = monitorService.getMonitor(monitorId);
            BulletinMetricsData.Data.DataBuilder dataBuilder = BulletinMetricsData.Data.builder()
                    .monitorId(monitorId)
                    .monitorName(monitor.getName())
                    .host(monitor.getHost());

            List<BulletinMetricsData.Metric> metrics = new ArrayList<>();
            Map<String, List<String>> fieldMap = bulletin.getFields();

            if (fieldMap != null) {
                for (Map.Entry<String, List<String>> entry : fieldMap.entrySet()) {
                    String metric = entry.getKey();
                    List<String> fields = entry.getValue();
                    BulletinMetricsData.Metric.MetricBuilder metricBuilder = BulletinMetricsData.Metric.builder()
                            .name(metric);
                    CollectRep.MetricsData currentMetricsData = realTimeDataReader.getCurrentMetricsData(monitorId, metric);

                    List<List<BulletinMetricsData.Field>> fieldsList;
                    if (currentMetricsData != null) {
                        fieldsList = currentMetricsData.getValuesList().stream()
                                .map(valueRow -> {
                                    List<BulletinMetricsData.Field> fieldList = currentMetricsData.getFieldsList().stream()
                                            .map(field -> BulletinMetricsData.Field.builder()
                                                    .key(field.getName())
                                                    .unit(field.getUnit())
                                                    .type(field.getType())
                                                    .build())
                                            .toList();

                                    for (int i = 0; i < fieldList.size(); i++) {
                                        if (fieldList.get(i).getKey().equals("alias") && valueRow.getColumns(i).equals("&nbsp;")) {
                                            fieldList.get(i).setValue("");
                                        } else {
                                            fieldList.get(i).setValue(valueRow.getColumns(i));
                                        }
                                    }
                                    return fieldList.stream().filter(field -> fields.contains(field.getKey())).toList();
                                })
                                .toList();
                        metricBuilder.fields(fieldsList);
                        metrics.add(metricBuilder.build());
                    }
                }
            }
            if(!metrics.isEmpty()){
                dataBuilder.metrics(metrics);
                dataList.add(dataBuilder.build());
            }
            contentBuilder.content(dataList);
        }
        return contentBuilder.build();
    }

    @Override
    public Page<Bulletin> getBulletins(String search, Integer pageIndex, Integer pageSize) {
        pageIndex = pageIndex == null ? 0 : pageIndex;
        pageSize = pageSize == null ? Integer.MAX_VALUE : pageSize;
        Specification<Bulletin> specification = (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.isNotBlank(search)) {
                Predicate predicateName = criteriaBuilder.like(root.get("name"), "%" + search + "%");
                predicate = criteriaBuilder.and(predicateName);
            }
            return predicate;
        };
        PageRequest pageRequest = PageRequest.of(pageIndex, pageSize);
        return bulletinDao.findAll(specification, pageRequest);
    }

    @Override
    public void deleteBulletins(List<Long> ids) {
        bulletinDao.deleteAllById(ids);
    }

    @Override
    public Bulletin getBulletinByApp(String app) {
        return bulletinDao.findByApp(app);
    }

//    @Override
//    public void test() {
//        List<AppDefineReader.AppDefine> appDefines = AppDefineReader.readAppDefines();
//        List<AppDefineReader.AppDefine> list = new ArrayList<>();
//        List<AppDefineReader.AppDefine> list2 = new ArrayList<>();
//        List<AppDefineReader.AppDefine> list3 = new ArrayList<>();
//        for (AppDefineReader.AppDefine appDefine : appDefines) {
//            if(appDefine.getCategory().equals("infra") && appDefine.getApp().equals("infra_nvidia")){
//                Bulletin bulletin = new Bulletin();
//                bulletin.setApp(appDefine.getApp());
//                Map<String, List<String>> stringListMap = convertChassisToMap();
//                bulletin.setFields(stringListMap);
//                bulletin.setName(appDefine.getCategory());
//                boolean b = validateMetricsFields(appDefine.getMetricsFields());
//                if(b){
//                    addBulletin(bulletin);
//                    list2.add(appDefine);
//                }else {
//                    list3.add(appDefine);
//                }
//                list.add(appDefine);
//                addBulletin(bulletin);
//            }
//        }
//        System.out.println(list.size());
//    }

    private static boolean validateMetricsFields(Map<String, List<String>> metricsFields) {
        if (metricsFields == null) {
            return false;
        }
        try {
            return metricsFields.get("cpu").contains("usage") &&
                    metricsFields.get("memory").contains("usage") &&
                    metricsFields.get("interface").contains("transmit_bytes") &&
                    metricsFields.get("interface").contains("receive_bytes")&&metricsFields.get("disk").contains("read_rate")&&metricsFields.get("disk").contains("write_rate");
        } catch (Exception e) {
            log.error("验证指标字段失败: {}", e.getMessage());
            return false;
        }
    }

    public static Map<String, List<String>> convertChassisToMap() {
        Map<String, List<String>> fieldsMap = new HashMap<>();
        List<String> chassisMetrics = Arrays.asList("utilization.gpu","utilization.memory","temperature.gpu");
        fieldsMap.put("basic", chassisMetrics);

        return fieldsMap;
    }

    public static Map<String, List<String>> convertToMap() {
        Map<String, List<String>> fieldsMap = new HashMap<>();

        // CPU
        fieldsMap.put("cpu", Collections.singletonList("usage"));

        // Memory
        fieldsMap.put("memory", Collections.singletonList("usage"));

        // Disk
        List<String> diskMetrics = Arrays.asList("write_rate", "read_rate");
        fieldsMap.put("disk", diskMetrics);

        // Interface
        List<String> interfaceMetrics = Arrays.asList("transmit_bytes", "receive_bytes");
        fieldsMap.put("interface", interfaceMetrics);

        return fieldsMap;
    }
    /**
     * Get Bulletin by id
     */
    @Override
    public Optional<Bulletin> getBulletinById(Long id) {
        return bulletinDao.findById(id);
    }
}
