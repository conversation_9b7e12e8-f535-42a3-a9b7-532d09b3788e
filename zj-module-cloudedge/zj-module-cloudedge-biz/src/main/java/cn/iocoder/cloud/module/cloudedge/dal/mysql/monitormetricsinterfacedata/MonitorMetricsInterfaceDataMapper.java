package cn.iocoder.cloud.module.cloudedge.dal.mysql.monitormetricsinterfacedata;

import java.util.*;

import cn.iocoder.cloud.module.cloudedge.controller.admin.monitormetricsinterfacedata.vo.MonitorMetricsInterfaceDataExportReqVO;
import cn.iocoder.cloud.module.cloudedge.controller.admin.monitormetricsinterfacedata.vo.MonitorMetricsInterfaceDataPageReqVO;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.monitormetricsinterfacedata.MonitorMetricsInterfaceDataDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 监控实时数据 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MonitorMetricsInterfaceDataMapper extends BaseMapperX<MonitorMetricsInterfaceDataDO> {

    default PageResult<MonitorMetricsInterfaceDataDO> selectPage(MonitorMetricsInterfaceDataPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MonitorMetricsInterfaceDataDO>()
                .eqIfPresent(MonitorMetricsInterfaceDataDO::getMonitorId, reqVO.getMonitorId())
                .eqIfPresent(MonitorMetricsInterfaceDataDO::getMetrics, reqVO.getMetrics())
                .eqIfPresent(MonitorMetricsInterfaceDataDO::getInterfaceIndex, reqVO.getInterfaceIndex())
                .likeIfPresent(MonitorMetricsInterfaceDataDO::getInterfaceName, reqVO.getInterfaceName())
                .eqIfPresent(MonitorMetricsInterfaceDataDO::getAlias, reqVO.getAlias())
                .eqIfPresent(MonitorMetricsInterfaceDataDO::getAdminStatus, reqVO.getAdminStatus())
                .eqIfPresent(MonitorMetricsInterfaceDataDO::getOperStatus, reqVO.getOperStatus())
                .orderByDesc(MonitorMetricsInterfaceDataDO::getId));
    }

    default List<MonitorMetricsInterfaceDataDO> selectList(MonitorMetricsInterfaceDataExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MonitorMetricsInterfaceDataDO>()
                .eqIfPresent(MonitorMetricsInterfaceDataDO::getMonitorId, reqVO.getMonitorId())
                .eqIfPresent(MonitorMetricsInterfaceDataDO::getMetrics, reqVO.getMetrics())
                .eqIfPresent(MonitorMetricsInterfaceDataDO::getInterfaceIndex, reqVO.getInterfaceIndex())
                .likeIfPresent(MonitorMetricsInterfaceDataDO::getInterfaceName, reqVO.getInterfaceName())
                .eqIfPresent(MonitorMetricsInterfaceDataDO::getAlias, reqVO.getAlias())
                .eqIfPresent(MonitorMetricsInterfaceDataDO::getAdminStatus, reqVO.getAdminStatus())
                .eqIfPresent(MonitorMetricsInterfaceDataDO::getOperStatus, reqVO.getOperStatus())
                .orderByDesc(MonitorMetricsInterfaceDataDO::getId));
    }

}
