package cn.iocoder.cloud.module.cloudedge.adapter.impl;

import cn.iocoder.cloud.module.cloudedge.adapter.AbstractMonitorAdapter;
import cn.iocoder.cloud.module.cloudedge.adapter.MonitorType;
import lombok.extern.log4j.Log4j;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.springframework.stereotype.Service;

import java.util.*;

@Log4j
@Service
public class SystemstorageAdapterImpl extends AbstractMonitorAdapter {
    private static final String POOL_STORAGE_TOTAL = "poolStorageTotal"; //存储池总容量
    private static final String POOL_STORAGE_USED = "poolStorageUsed"; //存储池使用容量
    private static final String POOL_STORAGE_AVAILABLE = "poolStorageAvailable"; //存储池可用容量

    @Override
    protected void processMetricsData(Monitor monitor, CollectRep.MetricsData metricsData) {

    }

    @Override
    public MonitorType getMonitorType() {
        return MonitorType.SYSTEMSTORAGE;
    }

    @Override
    public Map<String, List<Map<String, Object>>> handleMonitorTop10MetricsData(CollectRep.MetricsData metricsData) {
        return buildTop10MetricsData(metricsData,null, POOL_STORAGE_TOTAL, POOL_STORAGE_USED, POOL_STORAGE_AVAILABLE);
    }
}
