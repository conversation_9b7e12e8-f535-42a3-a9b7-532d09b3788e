package cn.iocoder.cloud.module.cloudedge.component.alerter.impl;

import cn.iocoder.zj.framework.common.exception.AlertNoticeException;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.alerter.Alert;
import org.apache.hertzbeat.common.entity.manager.NoticeReceiver;
import org.apache.hertzbeat.common.entity.manager.NoticeTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Component;

/**
 * Server酱发送
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ServerChanAlertNotifyHandlerImpl extends AbstractAlertNotifyHandlerImpl {
    /**
     * Send alarm notification
     *
     * @param receiver Notification configuration information
     * @param alert    Alarm information
     * @throws AlertNoticeException when send receiver error
     */
    @Override
    public void send(NoticeReceiver receiver, NoticeTemplate noticeTemplate, Alert alert) throws AlertNoticeException {
        try {
            ServerChanWebHookDto serverChanWebHookDto = new ServerChanWebHookDto();
            serverChanWebHookDto.setTitle(bundle.getString("alerter.notify.title"));
            serverChanWebHookDto.setDesp(renderContent(noticeTemplate, alert));
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<ServerChanWebHookDto> httpEntity = new HttpEntity<>(serverChanWebHookDto, headers);
            String webHookUrl = String.format(alerterProperties.getServerChanWebhookUrl(), receiver.getServerChanToken());
            ResponseEntity<CommonRobotNotifyResp> responseEntity = restTemplate.postForEntity(webHookUrl,
                    httpEntity, CommonRobotNotifyResp.class);
            if (responseEntity.getStatusCode() == HttpStatus.OK) {
                log.debug("Send ServerChan webHook: {} Success", webHookUrl);
            } else {
                log.warn("Send ServerChan webHook: {} Failed: {}", webHookUrl, responseEntity.getBody());
                throw new AlertNoticeException("Http StatusCode " + responseEntity.getStatusCode());
            }
        } catch (Exception e) {
            throw new AlertNoticeException("[ServerChan Notify Error] " + e.getMessage());
        }
    }

    /**
     * notification type
     *
     * @return notification type
     */
    @Override
    public byte type() {
        return 12;
    }

    @Data
    protected static class ServerChanWebHookDto {
        private static final String MARKDOWN = "markdown";

        /**
         * title
         */
        private String title;

        /**
         * markdown message content
         */
        private String desp;

    }

}
