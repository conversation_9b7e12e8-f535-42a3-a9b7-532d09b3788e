/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.module.cloudedge.dal.dataobject.herzbeat;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.codec.ServerSentEvent;

import java.util.List;
import java.util.Objects;

/**
 * openAi Response
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class OpenAiResponse {

    /**
     * Task order number generated by the AI open platform. Use this order number when invoking the request result interface
     */
    private String id;

    /**
     * The request creation time is a Unix timestamp in seconds
     */
    private Long created;

    /**
     * response message
     */
    private List<Choice> choices;

    /**
     * Returns the number of tokens invoked by the model at the end.
     */
    private Tokens usage;

    /**
     * Choice
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public class Choice {
        private int index;
        private AiMessage delta;
    }

    /**
     * Tokens
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public class Tokens {

        /**
         * The number of tokens entered by users
         */
        @JsonProperty("prompt_tokens")
        private Integer promptTokens;

        /**
         * The number of tokens that the model outputs
         */
        @JsonProperty("completion_tokens")
        private Integer completionTokens;

        /**
         * Total number of tokens
         */
        @JsonProperty("total_tokens")
        private Integer totalTokens;
    }

    public static ServerSentEvent<String> convertToResponse(String aiRes) {
        try {
            OpenAiResponse openAiResponse = JSON.parseObject(aiRes, OpenAiResponse.class);
            if (Objects.nonNull(openAiResponse)) {
                Choice choice = openAiResponse.getChoices().get(0);
                String content = choice.getDelta().getContent();
                return ServerSentEvent.<String>builder()
                        .data(content)
                        .build();
            }
        } catch (Exception e) {
            log.info("convertToResponse Exception:{}", e.toString());
            throw e;
        }

        return ServerSentEvent.<String>builder().build();
    }
}

