package cn.iocoder.cloud.module.cloudedge.scheduler.netty.process;

import cn.iocoder.cloud.framework.remoting.netty.NettyRemotingProcessor;
import cn.iocoder.cloud.module.cloudedge.scheduler.netty.ManageServer;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hertzbeat.common.entity.dto.CollectorInfo;
import org.apache.hertzbeat.common.entity.message.ClusterMsg;
import org.apache.hertzbeat.common.util.JsonUtil;

import java.net.InetSocketAddress;

/**
 * handle collector online message
 */
@Slf4j
public class CollectorOnlineProcessor implements NettyRemotingProcessor {
    private final ManageServer manageServer;

    public CollectorOnlineProcessor(final ManageServer manageServer) {
        this.manageServer = manageServer;
    }


    @Override
    public ClusterMsg.Message handle(ChannelHandlerContext ctx, ClusterMsg.Message message) {
        String collector = message.getIdentity();
        log.info("the collector {} actively requests to go online.", collector);
        CollectorInfo collectorInfo = JsonUtil.fromJson(message.getMsg(), CollectorInfo.class);
        if (collectorInfo != null && StringUtils.isBlank(collectorInfo.getIp())) {
            // fetch remote ip address
            InetSocketAddress socketAddress = (InetSocketAddress) ctx.channel().remoteAddress();
            String clientIP = socketAddress.getAddress().getHostAddress();
            collectorInfo.setIp(clientIP);
        }
        this.manageServer.addChannel(collector, ctx.channel());
        this.manageServer.getCollectorAndJobScheduler().collectorGoOnline(collector, collectorInfo);
        return null;
    }
}
