package cn.iocoder.cloud.module.cloudedge.controller.admin.collectorplatform.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
* 采集器平台关联 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class CollectorPlatformBaseVO {

    @Schema(description = "采集器id")
    private Long collectorId;

    @Schema(description = "采集器名称")
    private String collectorName;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "存活状态")
    private Byte status;

}
