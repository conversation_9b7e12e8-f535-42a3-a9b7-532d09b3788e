package cn.iocoder.cloud.module.cloudedge.util;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * @ClassName : FrpData  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/5/23  10:51
 */
public class FrpData {


    public static JSONArray frpTcpPort(){
        String d = HttpRequest.get("http://links.kube.com/api/proxy/tcp")
                .basicAuth("admin", "Gzx@7541").execute().body();
        return  JSONObject.parseObject(d).getJSONArray("proxies");
    }
}
