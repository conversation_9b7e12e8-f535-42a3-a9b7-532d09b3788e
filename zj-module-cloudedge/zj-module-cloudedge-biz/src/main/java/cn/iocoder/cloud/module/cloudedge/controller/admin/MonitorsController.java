/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.module.cloudedge.controller.admin;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.cloud.framework.warehouse.service.WarehouseService;
import cn.iocoder.cloud.module.cloudedge.adapter.MonitorsAdapterService;
import cn.iocoder.cloud.module.cloudedge.controller.admin.monitor.vo.MonitorTagRespVo;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.category.CategoryDTO;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.herzbeat.Hierarchy;
import cn.iocoder.cloud.module.cloudedge.dal.mysql.collectorplatform.CollectorPlatformMapper;
import cn.iocoder.cloud.module.cloudedge.service.AppService;
import cn.iocoder.cloud.module.cloudedge.service.MonitorService;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.pojo.VictoriaMetricsDTO;
import cn.iocoder.zj.framework.common.util.string.StringUtil;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.monitor.api.tag.MonitorTagApi;
import cn.iocoder.zj.module.monitor.api.tag.dto.TagRespDTO;
import cn.iocoder.zj.module.monitor.api.tag.dto.TaggablesDTO;
import cn.iocoder.zj.module.system.api.licence.LicenceApi;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.hertzbeat.common.entity.dto.Message;
import org.apache.hertzbeat.common.entity.manager.Collector;
import org.apache.hertzbeat.common.entity.manager.CollectorMonitorBind;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.criteria.*;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.singleton;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.DELETE;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.OTHER;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

/**
 * Monitor and manage batch API
 * 监控管理批量API
 *
 * <AUTHOR>
 */
@Tag(name = "Monitor Manage Batch API | 监控列表API")
@RestController
@RequestMapping(path = "/cloudedge/api/monitors", produces = {APPLICATION_JSON_VALUE})
public class MonitorsController {

    private static final byte ALL_MONITOR_STATUS = 9;

    private static final int TAG_LENGTH = 2;

    @Autowired
    private MonitorService monitorService;
    @Autowired
    LicenceApi licenceApi;
    @Autowired
    PermissionApi permissionApi;
    @Autowired
    RoleApi roleApi;
    @Autowired
    CollectorPlatformMapper collectorPlatformMapper;
    @Autowired
    private PlatformconfigApi platformconfigApi;
    @Autowired
    private WarehouseService warehouseService;
    @Autowired
    private AppService appService;
    @Autowired
    private MonitorsAdapterService monitorsAdapterService;
    @Autowired
    private MonitorTagApi monitorTagApi;


    @GetMapping
    @Operation(summary = "Obtain a list of monitoring information based on query filter items",
            description = "根据查询过滤项获取监控信息列表")
    //@PreAuthorize("@ss.hasPermission('clooud:monitor:query')")
    public ResponseEntity<Message<?>> getMonitors(
            @Parameter(description = "Monitor ID | 监控任务ID", example = "6565463543") @RequestParam(required = false) final List<Long> ids,
            @Parameter(description = "Monitor Type | 监控类型", example = "linux") @RequestParam(required = false) final String app,
            @Parameter(description = "Monitor Type | 监控类型", example = "os") @RequestParam(required = false) final String category,
            @Parameter(description = "Monitor Name | 任务名称，模糊查询", example = "linux-127.0.0.1") @RequestParam(required = false) final String name,
            @Parameter(description = "Monitor Host | 监控Host，模糊查询", example = "127.0.0.1") @RequestParam(required = false) final String host,
            @Parameter(description = "Monitor Status | 任务状态 0:未监控,1:可用,2:不可用,9:全部状态", example = "1") @RequestParam(required = false) final Byte status,
            @Parameter(description = "Monitor collectorType | 采集器类型", example = "0:在线，1:离线") @RequestParam(required = false) final String collectorType,
            @Parameter(description = "Monitor startTime | 监控开始时间", example = "2022-01-01 00:00:00") @DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam(required = false) String startTime,
            @Parameter(description = "Monitor endTime | 监控结束时间", example = "2022-01-01 00:00:00") @DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam(required = false) String endTime,
            @Parameter(description = "Sort Field | 排序字段", example = "name") @RequestParam(defaultValue = "gmtCreate") final String sortBy,
            @Parameter(description = "Sort by | 排序方式，asc:升序，desc:降序", example = "desc") @RequestParam(defaultValue = "desc") final String sortDirection,
            @Parameter(description = "List current page | 列表当前分页", example = "0") @RequestParam(defaultValue = "0") int pageIndex,
            @Parameter(description = "Number of list pagination | 列表分页数量", example = "8") @RequestParam(defaultValue = "8") int pageSize,
            @Parameter(description = "Monitor tag | 监控标签", example = "env:prod") @RequestParam(required = false) final String tag,
            @Parameter(description = "Monitor platformId | 平台id", example = "1024") @RequestParam(required = false) final Long platformId,
            @Parameter(description = "过滤的应用id", example = "1024") @RequestParam(required = false) String moIds,
            @Parameter(description = "monitorTagIds", example = "1024") @RequestParam(required = false) String tagIds,
            @Parameter(description = "是否是列表", example = "true") @RequestParam(required = false) Boolean isList,
            @Parameter(description = "电源状态") @RequestParam(required = false) String powerStatus,
            @Parameter(description = "风扇状态") @RequestParam(required = false) String systemFanStatus,
            @Parameter(description = "查詢存在的應用id信息", example = "1024") @RequestParam(required = false) String inIds) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        List<Map> platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
        Page<Monitor> monitorPage = Page.empty();
        Page<MonitorTagRespVo> monitorResPage = Page.empty();
        if (CollectionUtil.isNotEmpty(platform)) {
            List<String> moid;
            List<String> inId;
            List<Long> inId2;
            if (StringUtil.isNotEmpty(moIds)) {
                moid = Arrays.asList(moIds.split(","));
            } else {
                moid = new ArrayList<>();
            }
            if (StringUtil.isNotEmpty(inIds)) {
                inId = Arrays.asList(inIds.split(","));
            } else {
                inId = new ArrayList<>();
            }
            if (StringUtil.isNotEmpty(tagIds)) {
                List<TaggablesDTO> tmptagList = monitorTagApi.getByTag(Convert.toLong(tagIds), category);
                if (tmptagList.isEmpty()) {
                    inId2 = new ArrayList<>();
                    inId2.add(0L);
                } else {
                    inId2 = tmptagList.stream().map(TaggablesDTO::getTaggableId).toList();
                }
            } else {
                inId2 = new ArrayList<>();
            }

            Specification<Monitor> specification = (root, query, criteriaBuilder) -> {
                List<Predicate> andList = new ArrayList<>();
                if (ids != null && !ids.isEmpty()) {
                    CriteriaBuilder.In<Long> inPredicate = criteriaBuilder.in(root.get("id"));
                    for (long id : ids) {
                        inPredicate.value(id);
                    }
                    andList.add(inPredicate);
                }
                if (platform != null && !platform.isEmpty()) {
                    CriteriaBuilder.In<Long> inPredicate = criteriaBuilder.in(root.get("platformId"));
                    for (Map map : platform) {
                        inPredicate.value(Convert.toLong(map.get("platformId")));
                    }
                    andList.add(inPredicate);
                }
                if (!moid.isEmpty()) {
                    CriteriaBuilder.In<Long> notInPredicate = criteriaBuilder.in(root.get("id"));
                    for (String m : moid) {
                        notInPredicate.value(Convert.toLong(m));
                    }
                    andList.add(criteriaBuilder.not(notInPredicate));
                }

                if (!inId.isEmpty()) {
                    CriteriaBuilder.In<Long> inPredicate = criteriaBuilder.in(root.get("id"));
                    for (String m : inId) {
                        inPredicate.value(Convert.toLong(m));
                    }
                    andList.add(inPredicate);
                }
                if (!inId2.isEmpty()) {
                    CriteriaBuilder.In<Long> inPredicate = criteriaBuilder.in(root.get("id"));
                    for (Long m : inId2) {
                        inPredicate.value(m);
                    }
                    andList.add(inPredicate);
                }
                if (StringUtils.hasText(category)) {

                    List<Hierarchy> categoryHierarchy = appService.getCategoryHierarchy("zh-CN", category);
                    List<String> values = categoryHierarchy.stream()
                            .map(Hierarchy::getValue)
                            .toList();
                    if (!values.isEmpty()) {
                        CriteriaBuilder.In<String> inPredicate = criteriaBuilder.in(root.get("app"));
                        for (String apps : values) {
                            inPredicate.value(apps);
                        }
                        andList.add(inPredicate);
                    }


//                    List<ResourceAppEnum> subEnums = ResourceCategoryUtil.getSubEnumsByCategory(category);
//                    if (!subEnums.isEmpty()) {
//
//                    }
                }
                if (StringUtils.hasText(app)) {
                    Predicate predicateApp = criteriaBuilder.equal(root.get("app"), app);
                    andList.add(predicateApp);
                }
                if (status != null && status >= 0 && status < ALL_MONITOR_STATUS) {
                    Predicate predicateStatus = criteriaBuilder.equal(root.get("status"), status);
                    andList.add(predicateStatus);
                }

                if (StringUtils.hasText(tag)) {
                    String[] tagArr = tag.split(":");
                    String tagName = tagArr[0];
                    ListJoin<Monitor, org.apache.hertzbeat.common.entity.manager.Tag> tagJoin = root
                            .join(root.getModel()
                                    .getList("tags", org.apache.hertzbeat.common.entity.manager.Tag.class), JoinType.LEFT);
                    if (tagArr.length == TAG_LENGTH) {
                        String tagValue = tagArr[1];
                        andList.add(criteriaBuilder.equal(tagJoin.get("name"), tagName));
                        andList.add(criteriaBuilder.equal(tagJoin.get("value"), tagValue));
                    } else {
                        andList.add(criteriaBuilder.equal(tagJoin.get("name"), tag));
                    }
                }
                if (platformId != null) {
                    Predicate predicateStatus = criteriaBuilder.equal(root.get("platformId"), platformId);
                    andList.add(predicateStatus);
                }

                if (StringUtils.hasText(host)) {
                    Predicate predicateHost = criteriaBuilder.like(root.get("host"), "%" + host + "%");
                    andList.add(predicateHost);
                }
                if (StringUtils.hasText(name)) {
                    Predicate predicateName = criteriaBuilder.like(root.get("name"), "%" + name + "%");
                    andList.add(predicateName);
                }
                if (collectorType != null) {
                    // 修复：将字符串转换为Byte类型
                    Byte collectorTypeByte = Byte.valueOf(collectorType);

                    // Monitor 的 id 对应 Param 的 monitorId
                    // 构建子查询获取采集器状态
                    Subquery<Byte> collectorSubquery = query.subquery(Byte.class);
                    Root<Collector> collectorRoot = collectorSubquery.from(Collector.class);

                    // 构建collector_monitor_bind关联子查询
                    Subquery<String> bindSubquery = collectorSubquery.subquery(String.class);
                    Root<CollectorMonitorBind> bindRoot = bindSubquery.from(CollectorMonitorBind.class);
                    bindSubquery.select(bindRoot.get("collector"))
                            .where(criteriaBuilder.equal(bindRoot.get("monitorId"), root.get("id")));

                    // 主查询条件
                    collectorSubquery.select(collectorRoot.get("status"))
                            .where(collectorRoot.get("name").in(bindSubquery));

                    andList.add(criteriaBuilder.equal(collectorSubquery, collectorTypeByte));
                }
                if ((startTime != null && !startTime.isEmpty()) && (endTime != null && !endTime.isEmpty())) {
                    // 将字符串日期转换为LocalDateTime
                    LocalDateTime startDateTime = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    LocalDateTime endDateTime = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    Predicate predicateBetween = criteriaBuilder.between(root.get("gmtCreate"), startDateTime, endDateTime);
                    andList.add(predicateBetween);
                }

                Predicate[] andPredicates = new Predicate[andList.size()];
                Predicate andPredicate = criteriaBuilder.and(andList.toArray(andPredicates));

                List<Predicate> orList = new ArrayList<>();
//            if (StringUtils.hasText(host)) {
//                Predicate predicateHost = criteriaBuilder.like(root.get("host"), "%" + host + "%");
//                orList.add(predicateHost);
//            }
//            if (StringUtils.hasText(name)) {
//                Predicate predicateName = criteriaBuilder.like(root.get("name"), "%" + name + "%");
//                orList.add(predicateName);
//            }
//
                Predicate[] orPredicates = new Predicate[orList.size()];
                Predicate orPredicate = criteriaBuilder.or(orList.toArray(orPredicates));

                if (andPredicate.getExpressions().isEmpty() && orPredicate.getExpressions().isEmpty()) {
                    return query.where().getRestriction();
                } else if (andPredicate.getExpressions().isEmpty()) {
                    return query.where(orPredicate).getRestriction();
                } else if (orPredicate.getExpressions().isEmpty()) {
                    return query.where(andPredicate).getRestriction();
                } else {
                    return query.where(andPredicate, orPredicate).getRestriction();
                }
            };
            // Pagination is a must         分页是必须的
            Sort sortExp = Sort.by(new Sort.Order(Sort.Direction.fromString(sortDirection), sortBy));
            PageRequest pageRequest = PageRequest.of(pageIndex, pageSize, sortExp);
            List<MonitorTagRespVo> monitorResList = new ArrayList<>();
            if (isList) {
//                pageRequest = PageRequest.of(pageIndex, Integer.MAX_VALUE, sortExp);
                monitorPage = monitorService.getMonitors(specification, pageRequest);
                for (Monitor monitor : monitorPage) {
                    Long type = collectorPlatformMapper.getCollectType(monitor.getId().toString());
                    String protocol = monitorService.getMonitorProtocol(monitor.getId().toString());
                    monitor.setProtocol(protocol);
                    monitor.setCollectorType(type);
                    if (category != null) {
                        List<CollectRep.MetricsData> metricsDataList = warehouseService.queryMonitorMetricsData(monitor.getId());
                        monitorsAdapterService.handleMonitorMetricsDataByCategory(category, monitor, metricsDataList);
                    }

                    MonitorTagRespVo rt = new MonitorTagRespVo();
                    BeanUtil.copyProperties(monitor, rt);
                    if (ObjectUtil.isNotEmpty(monitor.getCategory())) {
                        List<TagRespDTO> mTags = monitorTagApi.getByTaggable(monitor.getId(), monitor.getCategory());
                        rt.setMonitorTags(mTags);
                    } else {
                        rt.setMonitorTags(Collections.emptyList());
                    }
                    monitorResList.add(rt);
                }

                List<MonitorTagRespVo> resultList;

                boolean hasPowerStatus = StringUtil.isNotEmpty(powerStatus);
                boolean hasFanStatus = StringUtil.isNotEmpty(systemFanStatus);

                if (hasPowerStatus || hasFanStatus) {
                    resultList = monitorResList.stream()
                            .filter(item -> {
                                boolean matchPower = !hasPowerStatus || Objects.equals(item.getPowerStatus(), powerStatus);
                                boolean matchFan = !hasFanStatus || Objects.equals(item.getSystemFanStatus(), systemFanStatus);
                                return matchPower && matchFan;
                            })
                            .collect(Collectors.toList());
                } else {
                    resultList = monitorResList;
                }

                return ResponseEntity.ok(Message.success(resultList));
            } else {
                monitorPage = monitorService.getMonitors(specification, pageRequest);
                for (Monitor monitor : monitorPage) {
                    Long type = collectorPlatformMapper.getCollectType(monitor.getId().toString());
                    String protocol = monitorService.getMonitorProtocol(monitor.getId().toString());
                    monitor.setProtocol(protocol);
                    monitor.setCollectorType(type);
                    if (category != null) {
                        List<CollectRep.MetricsData> metricsDataList = warehouseService.queryMonitorMetricsData(monitor.getId());
                        monitorsAdapterService.handleMonitorMetricsDataByCategory(category, monitor, metricsDataList);
                    }

                    // 处理CPU使用率,保留两位小数
                    String cpuUsed = monitor.getCpuUsed();
                    if (StringUtils.hasText(cpuUsed)) {
                        try {
                            double cpuValue = Double.parseDouble(cpuUsed);
                            monitor.setCpuUsed(String.format("%.2f", cpuValue));
                        } catch (NumberFormatException ignored) {
                        }
                    }

                    // 处理磁盘使用率,保留两位小数
                    String diskUsage = monitor.getDiskUsage();
                    if (StringUtils.hasText(diskUsage)) {
                        try {
                            double diskValue = Double.parseDouble(diskUsage);
                            monitor.setDiskUsage(String.format("%.2f", diskValue));
                        } catch (NumberFormatException ignored) {
                        }
                    }

                    // 处理内存使用率,保留两位小数
                    String memUsage = monitor.getMemUsage();
                    if (StringUtils.hasText(memUsage)) {
                        try {
                            double memValue = Double.parseDouble(memUsage);
                            monitor.setMemUsage(String.format("%.2f", memValue));
                        } catch (NumberFormatException ignored) {
                        }
                    }
                    MonitorTagRespVo rt = new MonitorTagRespVo();
                    BeanUtil.copyProperties(monitor, rt);
                    if (ObjectUtil.isNotEmpty(monitor.getCategory())) {
                        List<TagRespDTO> mTags = monitorTagApi.getByTaggable(monitor.getId(), monitor.getCategory());
                        rt.setMonitorTags(mTags);
                    } else {
                        List<Hierarchy> appHierarchy = appService.getAppHierarchy(monitor.getApp(), "zh-CN");
                        String cate = !appHierarchy.isEmpty() ? appHierarchy.stream().findFirst().map(Hierarchy::getCategory).orElse(null) : null;
                        List<TagRespDTO> mTags = monitorTagApi.getByTaggable(monitor.getId(), cate);
                        rt.setMonitorTags(mTags);
                    }
                    monitorResList.add(rt);
                }

                boolean hasPowerStatus = StringUtil.isNotEmpty(powerStatus);
                boolean hasFanStatus = StringUtil.isNotEmpty(systemFanStatus);
                List<MonitorTagRespVo> resultList;
                if (hasPowerStatus || hasFanStatus) {
                    resultList = monitorResList.stream()
                            .filter(item -> {
                                boolean matchPower = !hasPowerStatus || Objects.equals(item.getPowerStatus(), powerStatus);
                                boolean matchFan = !hasFanStatus || Objects.equals(item.getSystemFanStatus(), systemFanStatus);
                                return matchPower && matchFan;
                            })
                            .collect(Collectors.toList());
                } else {
                    resultList = monitorResList;
                }

                // 分页封装
                Page<MonitorTagRespVo> page = new PageImpl<>(resultList, pageRequest, monitorPage.getTotalElements());
                Message<Page<MonitorTagRespVo>> message = Message.success(page);
                return ResponseEntity.ok(message);

            }
        }
        // 如果 platform 为空，返回空数据
        return ResponseEntity.ok(Message.success(isList ? Collections.emptyList() : Page.empty()));
    }

    @GetMapping(path = "/{app}")
    @Operation(summary = "Filter all acquired monitoring information lists of the specified monitoring type according to the query",
            description = "根据查询过滤指定监控类型的所有获取监控信息列表")
    public ResponseEntity<Message<List<Monitor>>> getAppMonitors(
            @Parameter(description = "en: Monitoring type,zh: 监控类型", example = "linux") @PathVariable(required = false) final String app) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        List<Map> platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();

        Set<Long> resultSet = new HashSet<>(); // 创建 Set<Long> 来存储 Long 值
// 遍历 List
        for (Map<String, Object> map : platform) {
            // 从 Map 中获取 "id" 键的值
            Object value = map.get("platformId");
            resultSet.add(Convert.toLong(value)); // 将值添加到 Set 中
        }

//        List<Monitor> monitors = monitorService.getAppMonitors(app);
        List<Monitor> monitors = monitorService.getAppMonitorsByPlatformId(app, resultSet);
        Message<List<Monitor>> message = Message.success(monitors);
        return ResponseEntity.ok(message);
    }

    @DeleteMapping
    @Operation(summary = "Delete monitoring items in batches according to the monitoring ID list",
            description = "根据监控任务ID列表批量删除监控项")
    //@PreAuthorize("@ss.hasPermission('clooud:monitor:delete')")
    @OperateLog(type = DELETE)
    public ResponseEntity<Message<Void>> deleteMonitors(
            @Parameter(description = "Monitoring ID List | 监控任务ID列表", example = "6565463543") @RequestParam(required = false) List<Long> ids
    ) {
        if (ids != null && !ids.isEmpty()) {
            monitorService.deleteMonitors(new HashSet<>(ids));
        }
        Message<Void> message = Message.success();
        return ResponseEntity.ok(message);
    }

    @DeleteMapping("manage")
    @Operation(summary = "Unmanaged monitoring items in batches according to the monitoring ID list",
            description = "根据监控任务ID列表批量取消纳管监控项")
    //@PreAuthorize("@ss.hasPermission('clooud:monitor:delete')")
    @OperateLog(type = OTHER)
    public ResponseEntity<Message<Void>> cancelManageMonitors(
            @Parameter(description = "Monitoring ID List | 监控任务ID列表", example = "6565463543") @RequestParam(required = false) List<Long> ids
    ) {
        if (ids != null && !ids.isEmpty()) {
            monitorService.cancelManageMonitors(new HashSet<>(ids));
        }
        Message<Void> message = Message.success();
        return ResponseEntity.ok(message);
    }

    @GetMapping("manage")
    @Operation(summary = "Start the managed monitoring items in batches according to the monitoring ID list",
            description = "根据监控任务ID列表批量启动纳管监控项")
    //@PreAuthorize("@ss.hasPermission('clooud:monitor:query')")
    @OperateLog(type = OTHER)
    public ResponseEntity<Message<Map>> enableManageMonitors(
            @Parameter(description = "Monitor ID List | 监控任务ID列表", example = "6565463543") @RequestParam(required = false) List<Long> ids
    ) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            // 校验查询是否资产授权已超出
            Map map = licenceApi.selectLicence(loginUser.getTenantId()).getData();
            if (map.get("state").equals("unavailable")) {
                return ResponseEntity.ok(Message.success(map));
            }
        }

        if (ids != null && !ids.isEmpty()) {
            monitorService.enableManageMonitors(new HashSet<>(ids));
        }
        Message<Map> message = Message.success();
        return ResponseEntity.ok(message);
    }

    @GetMapping("/export")
    @Operation(summary = "export monitor config", description = "导出监控配置")
    //@PreAuthorize("@ss.hasPermission('clooud:monitor:export')")
    public void export(
            @Parameter(description = "Monitor ID List | 监控任务ID列表", example = "6565463543") @RequestParam List<Long> ids,
            @Parameter(description = "Export Type:JSON,EXCEL,YAML") @RequestParam(defaultValue = "JSON") String type,
            HttpServletResponse res) throws Exception {
        monitorService.export(ids, type, res);
    }

    @PostMapping("/import")
    @Operation(summary = "import monitor config", description = "导入监控配置")
    //@PreAuthorize("@ss.hasPermission('clooud:monitor:import')")
    public ResponseEntity<Message<Void>> export(MultipartFile file) throws Exception {
        monitorService.importConfig(file);
        return ResponseEntity.ok(Message.success("Import success"));
    }

    @PostMapping("/uploadConfig")
    @Operation(summary = "import monitor upload", description = "导入监控配置新")
    public ResponseEntity<Message<Map>> handleFileUpload(MultipartFile file, String app) throws Exception {

        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();

        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            // 校验查询是否资产授权已超出
            Map map = licenceApi.selectLicence(loginUser.getTenantId()).getData();
            if (map.get("state").equals("unavailable")) {
                return ResponseEntity.ok(Message.success(map));
            }
        }
        List<Map> platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();

        Set<Long> resultSet = new HashSet<>(); // 创建 Set<Long> 来存储 Long 值
// 遍历 List
        for (Map<String, Object> map : platform) {
            // 从 Map 中获取 "id" 键的值
            Object value = map.get("platformId");
            resultSet.add(Convert.toLong(value)); // 将值添加到 Set 中
        }
        monitorService.importExcel(file, platform, app);
        // 处理dataMap中的数据
        return ResponseEntity.ok(Message.success("Import success"));

    }


    @PostMapping("/copy")
    @Operation(summary = "copy monitors by ids", description = "根据id批量复制monitor")
    public ResponseEntity<Message<Void>> duplicateMonitors(
            @Parameter(description = "Monitor ID List | 监控任务ID列表", example = "6565463543") @RequestParam List<Long> ids
    ) throws Exception {
        if (ids != null && !ids.isEmpty()) {
            monitorService.copyMonitors(ids);
        }
        return ResponseEntity.ok(Message.success("copy success"));
    }

    @GetMapping("/list")
    @Operation(summary = "Obtain a list of monitoring information based on query filter items",
            description = "根据查询过滤项获取监控信息所有列表")
    public ResponseEntity<Message<List<Monitor>>> getMonitorsByList(
            @Parameter(description = "Monitor platformId | 平台id", example = "1024") @RequestParam(required = false) final Long platformId,
            @Parameter(description = "过滤的应用id", example = "1024") @RequestParam(required = false) List<Long> moIds) {
        List<String> uids = new ArrayList<>();
        Specification<Monitor> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> andList = new ArrayList<>();
            if (platformId != null) {
                Predicate predicateStatus = criteriaBuilder.equal(root.get("platformId"), platformId);
                andList.add(predicateStatus);
            }
            Predicate[] andPredicates = new Predicate[andList.size()];
            Predicate andPredicate = criteriaBuilder.and(andList.toArray(andPredicates));

            List<Predicate> orList = new ArrayList<>();

            Predicate[] orPredicates = new Predicate[orList.size()];
            Predicate orPredicate = criteriaBuilder.or(orList.toArray(orPredicates));

            if (andPredicate.getExpressions().isEmpty() && orPredicate.getExpressions().isEmpty()) {
                return query.where().getRestriction();
            } else if (andPredicate.getExpressions().isEmpty()) {
                return query.where(orPredicate).getRestriction();
            } else if (orPredicate.getExpressions().isEmpty()) {
                return query.where(andPredicate).getRestriction();
            } else {
                return query.where(andPredicate, orPredicate).getRestriction();
            }
        };
        List<Monitor> monitorList = monitorService.getMonitorsByList(specification);
        Message<List<Monitor>> message = Message.success(monitorList);
        return ResponseEntity.ok(message);
    }


    @GetMapping("/select")
    @Operation(summary = "",
            description = "监控列表下拉框")
    public ResponseEntity<Message<List<CategoryDTO>>> getMonitorsBySelect() {
        List<CategoryDTO> monitorList = monitorService.getMonitorsBySelect();
        Message<List<CategoryDTO>> message = Message.success(monitorList);
        return ResponseEntity.ok(message);
    }


    @GetMapping("/getTotalType")
    @Operation(summary = "",
            description = "获取各状态数量")
    public ResponseEntity<Message<Map>> getTotalType(@Parameter(description = "Monitor platformId | 平台id", example = "1024") @RequestParam(required = false) final Long platformId,
                                                     @Parameter(description = "Monitor Type | 监控类型", example = "linux") @RequestParam(required = false) final String app,
                                                     @Parameter(description = "Monitor category | 监控大类") @RequestParam(required = false, defaultValue = "") String category
    ) {
        Map monitorList;
        if (platformId != null) {
            monitorList = monitorService.getTotalType(platformId, app, category);
        } else {
            LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
            List<Map> platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
            monitorList = monitorService.getTotalTypeByPlatform(platform, app, category);
        }

        Message<Map> message = Message.success(monitorList);
        return ResponseEntity.ok(message);
    }

    @GetMapping("/exportConfigTemplate")
    @Operation(summary = "export monitor config", description = "导出配置模板")
    public void exportConfigTemplate(String value, HttpServletResponse response) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        List<Map> platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), Collections.singleton(CommonStatusEnum.ENABLE.getStatus()));
        // todo 根据平台id进行查询列表
        List<Predicate> andList = new ArrayList<>();
        Specification<Collector> specification = (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            // 添加一个过滤条件来排除名称为 "main-default-collector" 的数据
            Predicate excludePredicate = criteriaBuilder.notEqual(root.get("name"), "main-default-collector");
            andList.add(excludePredicate);

            if (!roleApi.hasAnySuperAdmin(roleIds) && !roleApi.hasAnyPlatformAdmin(roleIds)) {
                Predicate excludeNullPlatformIdPredicate = criteriaBuilder.isNotNull(root.get("platformId"));
                andList.add(excludeNullPlatformIdPredicate);
                if (!platform.isEmpty()) {
                    List<Long> platformIds = platform.stream()
                            .map(m -> Convert.toLong(m.get("platformId")))
                            .collect(Collectors.toList());

                    Predicate platformPredicate = root.get("platformId").in(platformIds);
                    andList.add(platformPredicate);
                }
            }

            // 组合所有的条件
            predicate = criteriaBuilder.and(andList.toArray(new Predicate[0]));

            return predicate;
        };
        monitorService.exportConfigTemplate(value, response, specification, platform);
    }

    @GetMapping("/getTop10MonitorList")
    @Operation(summary = "获取top10监控列表", description = "获取top10监控列表")
    public ResponseEntity<Message<Map<String, List<Map<String, Object>>>>> getTop10MonitorList(@Parameter(description = "Monitor MonitorId | 监控id", example = "6565463543") @RequestParam(required = false) final Long monitorId, @Parameter(description = "Monitor Type | 监控类型", example = "linux") @RequestParam(required = false) final String app,
                                                                                               @Parameter(description = "Monitor category | 监控大类") @RequestParam(required = false, defaultValue = "") String category
    ) {
        Map<String, List<Map<String, Object>>> monitorList = monitorService.getTop10MonitorList(monitorId, app, category);
        Message<Map<String, List<Map<String, Object>>>> message = Message.success(monitorList);
        return ResponseEntity.ok(message);
    }

    @GetMapping("/getTop5SwitchWirelessMonitorList")
    @Operation(summary = "获取无线控制器top5数据", description = "获取无线控制器top5数据")
    public ResponseEntity<Message<Map<String, List<VictoriaMetricsDTO>>>> getTop5SwitchWirelessMonitorList(@Parameter(description = "Monitor MonitorId | 监控id", example = "6565463543") @RequestParam(required = false) final Long monitorId, @Parameter(description = "Monitor Type | 监控类型", example = "linux") @RequestParam(required = false) final String app,
                                                                                               @Parameter(description = "Monitor category | 监控大类") @RequestParam(required = false, defaultValue = "") String history
    ) {
        Map<String, List<VictoriaMetricsDTO>> monitorList = monitorService.getTop5SwitchWirelessMonitorList(monitorId, app, history);
        Message<Map<String, List<VictoriaMetricsDTO>>> message = Message.success(monitorList);
        return ResponseEntity.ok(message);
    }

    @GetMapping("/getSwitchWirelessMonitorList")
    @Operation(summary = "获取无线控制器数据", description = "获取无线控制器数据")
    public ResponseEntity<Message<Map<String, List<VictoriaMetricsDTO>>>> getSwitchWirelessMonitorList(@Parameter(description = "Monitor MonitorId | 监控id", example = "6565463543") @RequestParam(required = false) final Long monitorId, @Parameter(description = "Monitor Type | 监控类型", example = "linux") @RequestParam(required = false) final String app,
                                                                                                           @Parameter(description = "Monitor category | 监控大类") @RequestParam(required = false, defaultValue = "") String history
    ) {
        Map<String, List<VictoriaMetricsDTO>> monitorList = monitorService.getSwitchWirelessMonitorList(monitorId, app, history);
        Message<Map<String, List<VictoriaMetricsDTO>>> message = Message.success(monitorList);
        return ResponseEntity.ok(message);
    }

    @GetMapping("/getApUserNum")
    @Operation(summary = "获取无线控制器ap数据", description = "获取无线控制器ap数据")
    public ResponseEntity<Message<Map<String, String>>> getApUserNum(@Parameter(description = "Monitor MonitorId | 监控id", example = "6565463543") @RequestParam(required = false) final Long monitorId, @Parameter(description = "Monitor Type | 监控类型", example = "linux") @RequestParam(required = false) final String app
    ) {
        Map<String, String> monitorList = monitorService.getApUserNum(monitorId, app);
        Message<Map<String, String>> message = Message.success(monitorList);
        return ResponseEntity.ok(message);
    }

    @GetMapping("/getOsMonitorList")
    @Operation(summary = "获取操作系统top相关", description = "获取操作系统top相关")
    public ResponseEntity<Message<Map<String, List<Map<String, Object>>>>> getOsMonitorList(@Parameter(description = "Monitor Type | 监控类型", example = "cpu") @RequestParam(required = false) final String app,
                                                                                            @Parameter(description = "Monitor category | 监控大类", example = "os") @RequestParam(required = false, defaultValue = "os") String category,
                                                                                            @Parameter(description = "返回条数", example = "10") @RequestParam(required = false) final long limit,
                                                                                            @Parameter(description = "平台id", example = "1") @RequestParam(required = false) final Long platformId
    ) {
        Map<String, List<Map<String, Object>>> monitorList = monitorService.getOsMonitorList(app, category, limit, platformId);
        Message<Map<String, List<Map<String, Object>>>> message = Message.success(monitorList);
        return ResponseEntity.ok(message);
    }

    /**
     * 获取首页各类型监控总数
     *
     * @param platformId 平台ID
     * @param app        监控类型
     * @return 各类型监控总数
     */
    @GetMapping("/getHomeType")
    @Operation(summary = "获取首页各类型监控总数", description = "获取首页各类型监控总数")
    public ResponseEntity<Message<Map<String, Object>>> getHomeType(
            @Parameter(description = "Monitor platformId | 平台id", example = "1024")
            @RequestParam(required = false) final Long platformId,
            @Parameter(description = "Monitor Type | 监控类型", example = "linux")
            @RequestParam(required = false) final String app) {

        // 获取监控数据
        Map<String, Object> osList;
        Map<String, Object> firewallList;
        Map<String, Object> dbList;
        Map<String, Object> serverList;

        if (platformId != null) {
            // 根据平台ID获取监控数据
            osList = monitorService.getTotalType(platformId, app, "os");
            firewallList = monitorService.getTotalType(platformId, app, "firewall");
            dbList = monitorService.getTotalType(platformId, app, "db");
            serverList = monitorService.getTotalType(platformId, app, "service");
        } else {
            // 获取当前用户所有平台的监控数据
            LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
            List<Map> platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
            osList = monitorService.getTotalTypeByPlatform(platform, app, "os");
            firewallList = monitorService.getTotalTypeByPlatform(platform, app, "firewall");
            dbList = monitorService.getTotalTypeByPlatform(platform, app, "db");
            serverList = monitorService.getTotalTypeByPlatform(platform, app, "service");
        }

        // 构建返回结果
        Map<String, Object> monitorList = Map.of(
                "osList", osList,
                "firewallList", firewallList,
                "dbList", dbList,
                "serverList", serverList
        );

        return ResponseEntity.ok(Message.success(monitorList));
    }

}
