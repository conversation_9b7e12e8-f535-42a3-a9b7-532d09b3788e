package cn.iocoder.cloud.module.cloudedge.service.collectorplatform;

import cn.iocoder.cloud.module.cloudedge.controller.admin.collectorplatform.vo.CollectorPlatformCreateReqVO;
import cn.iocoder.cloud.module.cloudedge.controller.admin.collectorplatform.vo.CollectorPlatformExportReqVO;
import cn.iocoder.cloud.module.cloudedge.controller.admin.collectorplatform.vo.CollectorPlatformPageReqVO;
import cn.iocoder.cloud.module.cloudedge.controller.admin.collectorplatform.vo.CollectorPlatformUpdateReqVO;
import cn.iocoder.cloud.module.cloudedge.convert.collectorplatform.CollectorPlatformConvert;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.collectorplatform.CollectPlatfDTO;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.collectorplatform.CollectorPlatformDO;
import cn.iocoder.cloud.module.cloudedge.dal.mysql.collectorplatform.CollectorPlatformMapper;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import org.apache.hertzbeat.common.entity.manager.Collector;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static cn.iocoder.cloud.module.cloudedge.enums.ErrorCodeConstants.COLLECTOR_PLATFORM_NOT_EXISTS;
import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 采集器平台关联 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CollectorPlatformServiceImpl implements CollectorPlatformService {

    @Resource
    private CollectorPlatformMapper collectorPlatformMapper;

    @Override
    @TenantIgnore
    public Long createCollectorPlatform(CollectorPlatformCreateReqVO createReqVO) {
        // 插入
        CollectorPlatformDO collectorPlatform = CollectorPlatformConvert.INSTANCE.convert(createReqVO);
        collectorPlatformMapper.insert(collectorPlatform);
        // 返回
        return collectorPlatform.getId();
    }

    @Override
    @TenantIgnore
    public void updateCollectorPlatform(CollectorPlatformUpdateReqVO updateReqVO) {
        // 校验存在
        validateCollectorPlatformExists(updateReqVO.getId());
        // 更新
        CollectorPlatformDO updateObj = CollectorPlatformConvert.INSTANCE.convert(updateReqVO);
        collectorPlatformMapper.updateById(updateObj);
    }

    @Override
    @TenantIgnore
    public void deleteCollectorPlatform(Long id) {
        // 校验存在
        validateCollectorPlatformExists(id);
        // 删除
        collectorPlatformMapper.deleteById(id);
    }

    private void validateCollectorPlatformExists(Long id) {
        if (collectorPlatformMapper.selectById(id) == null) {
            throw exception(COLLECTOR_PLATFORM_NOT_EXISTS);
        }
    }

    @Override
    @TenantIgnore
    public CollectorPlatformDO getCollectorPlatform(Long id) {
        return collectorPlatformMapper.selectById(id);
    }

    @Override
    @TenantIgnore
    public List<CollectorPlatformDO> getCollectorPlatformList(Collection<Long> ids) {
        return collectorPlatformMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<CollectorPlatformDO> getCollectorPlatformPage(CollectorPlatformPageReqVO pageReqVO) {
        return collectorPlatformMapper.selectPage(pageReqVO);
    }

    @Override
    @TenantIgnore
    public List<CollectorPlatformDO> getCollectorPlatformList(CollectorPlatformExportReqVO exportReqVO) {
        return collectorPlatformMapper.selectList(exportReqVO);
    }

    @TenantIgnore
    @Override
    public void bindCollector(Collector collector1, Long platformId, String platformName) {
        // 查询如果已存在则直接更新
        collectorPlatformMapper.updateCollectorById(collector1.getId(),platformId,platformName);


    }

    @Override
    @TenantIgnore
    public List<CollectorPlatformDO> getCollectorPlatformList() {
        return collectorPlatformMapper.selectList();
    }

    @Override
    public void deleteCollectorPlatformByName(String collector) {
        collectorPlatformMapper.deleteByName(collector);
    }

    @Override
    public void bindProjectId(Collector collector1, Long projectId, String projectName) {
        // 查询如果已存在则直接更新
        collectorPlatformMapper.updateCollectorByProjectId(collector1.getId(),projectId,projectName);
    }

    @Override
    public List<CollectorPlatformDO> getCollectorPlatformByIdList(Long platformId) {
        return collectorPlatformMapper.selectListByIds(platformId);
    }

    @Override
    public List<CollectPlatfDTO> getPlatforms(List<Long> sourceIds) {
        return collectorPlatformMapper.getPlatforms(sourceIds);
    }

    public static void main(String[] args) {
        List<String> strings = readMetricsFromExcel("C:\\Users\\<USER>\\Desktop\\指标(4).xlsx");
    }

    public static List<String> readMetricsFromExcel(String filePath) {
        List<String> metrics = new ArrayList<>();

        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = new XSSFWorkbook(fis)) {

            // 只处理"监控管理-TOP报表"这个sheet
            Sheet sheet = workbook.getSheetAt(2); // 第三个sheet

            String currentCategory = "";
            String currentSubCategory = "";
            String currentMetricType = "";

            for (Row row : sheet) {
                // 跳过表头
                if (row.getRowNum() == 0) continue;

                // 获取单元格值
                Cell categoryCell = row.getCell(0); // A列 - 大类
                Cell subCategoryCell = row.getCell(1); // B列 - 小类
                Cell metricTypeCell = row.getCell(2); // C列 - 指标小类
                Cell metricCell = row.getCell(3); // D列 - 具体指标

                // 更新当前分类信息
                if (categoryCell != null && !categoryCell.getStringCellValue().isEmpty()) {
                    currentCategory = categoryCell.getStringCellValue();
                }

                if (subCategoryCell != null && !subCategoryCell.getStringCellValue().isEmpty()) {
                    currentSubCategory = subCategoryCell.getStringCellValue();
                }

                if (metricTypeCell != null && !metricTypeCell.getStringCellValue().isEmpty()) {
                    currentMetricType = metricTypeCell.getStringCellValue();
                }

                // 如果具体指标不为空，则拼接完整指标路径
                if (metricCell != null && !metricCell.getStringCellValue().isEmpty()) {
                    String metric = metricCell.getStringCellValue();
                    String fullMetricPath = currentCategory + "-" + currentSubCategory + "-" +
                            currentMetricType + "-" + metric;
                    metrics.add(fullMetricPath);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

        return metrics;
    }

}
