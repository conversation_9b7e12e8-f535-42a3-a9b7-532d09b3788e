/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.module.cloudedge.service;


import cn.iocoder.cloud.module.cloudedge.dal.dataobject.herzbeat.Hierarchy;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.herzbeat.TemplateConfig;
import org.apache.hertzbeat.common.entity.job.Job;
import org.apache.hertzbeat.common.entity.manager.ParamDefine;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Monitoring Type Management Interface
 * 监控类型管理接口
 *
 * <AUTHOR>
 */
public interface AppService {

    /**
     * Query the defined parameter structure based on the monitoring type
     * @param app Monitoring type
     * @return list of parameter structures
     */
    List<ParamDefine> getAppParamDefines(String app);

    Job getPushDefine(Long monitorId);

    /**
     * get auto generate dynamic template define
     * for prometheus and more type
     * @param monitorId monitor id
     * @return define
     */
    Job getAutoGenerateDynamicDefine(Long monitorId);

    /**
     * Get monitor structure definition based on monitor type name
     *
     * @param app Monitoring type name
     * @return Monitoring Structure Definition
     * @throws IllegalArgumentException Thrown when there is no monitoring type with the corresponding name that is not supported
     */
    Job getAppDefine(String app) throws IllegalArgumentException;

    /**
     * Get monitor structure definition based on monitor type name
     * @param app Monitoring type name
     * @return Monitoring Structure Definition Optional
     */
    Optional<Job> getAppDefineOption(String app);

    /**
     * Get the defined metrics of the app
     *
     * @param app monitoring type
     * @return target
     */
    List<String> getAppDefineMetricNames(String app);


    /**
     * Get defined monitoring I 18 N resources
     *
     * @param lang Language type
     * @return I18N Resources
     */
    Map<String, String> getI18nResources(String lang);

    /**
     * Get the I 18 N resources of the monitoring type
     *
     * @param lang Language type
     * @return I18N Resources
     */
    Map<String, String> getI18nApps(String lang);

    /**
     * Query all types of monitoring hierarchy
     *
     * @param lang language
     * @return hierarchy information
     */
    List<Hierarchy> getAllAppHierarchy(String lang);

    List<Hierarchy> getCategoryHierarchy(String lang,String category);

    /**
     * Get the monitoring hierarchy based on the monitoring type
     *
     * @param app  monitoring type
     * @param lang language
     * @return hierarchy information
     */
    List<Hierarchy> getAppHierarchy(String app, String lang);

    /**
     * Get all app define
     *
     * @return defines
     */
    Map<String, Job> getAllAppDefines();

    /**
     * app define file content str
     *
     * @param app app
     * @return file content
     */
    String getMonitorDefineFileContent(String app);

    /**
     * update and apply app define yml
     *
     * @param ymlContent yml content
     * @param isModify   is modified?
     */
    void applyMonitorDefineYml(String ymlContent, boolean isModify);

    /**
     * delete monitor define yml
     *
     * @param app app
     */
    void deleteMonitorDefine(String app);

    /**
     * update custom template config in memory
     *
     * @param config template config
     */
    void updateCustomTemplateConfig(TemplateConfig config);
}
