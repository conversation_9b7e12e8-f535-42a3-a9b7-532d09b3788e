package cn.iocoder.cloud.module.cloudedge.adapter.impl;

import cn.iocoder.cloud.module.cloudedge.adapter.AbstractMonitorAdapter;
import cn.iocoder.cloud.module.cloudedge.adapter.MonitorType;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 内存监控适配器实现
 */
@Slf4j
@Service
public class MemoryAdapterImpl extends AbstractMonitorAdapter {

    @Override
    public MonitorType getMonitorType() {
        return MonitorType.MEMORY;
    }

    @Override
    public Map<String, List<Map<String, Object>>> handleMonitorTop10MetricsData(CollectRep.MetricsData metricsData) {
        return null;
    }

    @Override
    protected void processMetricsData(Monitor monitor, CollectRep.MetricsData metricsData) {
        // 获取内存使用率，支持多种字段名称
        getFirstMatchingFieldValue(metricsData, "mem_usage", "usage", "memUsage")
                .ifPresent(monitor::setMemUsage);
    }
} 