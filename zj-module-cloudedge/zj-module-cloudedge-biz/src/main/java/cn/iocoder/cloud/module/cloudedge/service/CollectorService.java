package cn.iocoder.cloud.module.cloudedge.service;

import org.apache.hertzbeat.common.entity.dto.CollectorSummary;
import org.apache.hertzbeat.common.entity.manager.Collector;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

/**
 * collector service
 * <AUTHOR>
 */
public interface CollectorService {
    
    /**
     * Dynamic conditional query
     *
     * @param specification Query conditions
     * @param pageRequest   pageIndex pageSize
     * @param platformId
     * @param projectName
     * @return Search result
     */
    Page<CollectorSummary> getCollectors(String name, int pageIndex, Integer pageSize, Long platformId, String sortBy, String sortDirection, String projectName,Integer status,String ip);

    /**
     * delete registered collectors
     * @param collectors collector
     */
    void deleteRegisteredCollector(List<String> collectors);

    /**
     * is has the collector name
     * @param collector collector name
     * @return return true if it has
     */
    boolean hasCollector(String collector);

    /**
     * @description: 绑定采集器
     * @date 2024/1/2 11:05
     * @version 1.0
     */
    void bindCollector(String collector, Long platformId, String platformName);

    List<Map> collectorBySelect();

    Map<String, Object> getstatus();

    void bindProjectId(String collector, Long projectId, String projectName);

    List<Collector> getByPlatformId(Long PlatformId);

    /**
     * Generate Collector Deploy Info
     *
     * @param collector collector name
     */
    Map<String, String> generateCollectorDeployInfo(String collector);

    /**
     * Makes Collectors Offline
     *
     * @param collectors collector names
     */
    void makeCollectorsOffline(List<String> collectors);

    /**
     * Makes Collectors Online
     *
     * @param collectors collector names
     */
    void makeCollectorsOnline(List<String> collectors);
}
