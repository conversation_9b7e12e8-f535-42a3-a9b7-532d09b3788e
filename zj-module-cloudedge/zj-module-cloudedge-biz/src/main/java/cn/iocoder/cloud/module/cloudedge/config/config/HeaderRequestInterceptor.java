//package cn.iocoder.cloud.module.cloudedge.config.config;///*
//// * Licensed to the Apache Software Foundation (ASF) under one or more
//// * contributor license agreements.  See the NOTICE file distributed with
//// * this work for additional information regarding copyright ownership.
//// * The ASF licenses this file to You under the Apache License, Version 2.0
//// * (the "License"); you may not use this file except in compliance with
//// * the License.  You may obtain a copy of the License at
//// *
//// *     http://www.apache.org/licenses/LICENSE-2.0
//// *
//// * Unless required by applicable law or agreed to in writing, software
//// * distributed under the License is distributed on an "AS IS" BASIS,
//// * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//// * See the License for the specific language governing permissions and
//// * limitations under the License.
//// */
////
////package cn.iocoder.cloud.module.cloudedge.config.config;
////
////import org.springframework.http.HttpHeaders;
////import org.springframework.http.HttpRequest;
////import org.springframework.http.MediaType;
////import org.springframework.http.client.ClientHttpRequestExecution;
////import org.springframework.http.client.ClientHttpRequestInterceptor;
////import org.springframework.http.client.ClientHttpResponse;
////
////import java.io.IOException;
////
//
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.HttpRequest;
//import org.springframework.http.MediaType;
//import org.springframework.http.client.ClientHttpRequestExecution;
//import org.springframework.http.client.ClientHttpRequestInterceptor;
//import org.springframework.http.client.ClientHttpResponse;
//import org.springframework.lang.NonNull;
//
//import java.io.IOException;
//
/////**
//// * Rest Template interceptor adds request header information
//// * restTemplate拦截器添加请求头信息
//// *
//// * <AUTHOR>
//// */
//public class HeaderRequestInterceptor implements ClientHttpRequestInterceptor {
//
//    @Override
//    public ClientHttpResponse intercept(HttpRequest request, @NonNull byte[] body, @NonNull ClientHttpRequestExecution execution)
//            throws IOException {
//        // Send json by default
//        if (request.getHeaders().getContentType() == null) {
//            request.getHeaders().setContentType(MediaType.APPLICATION_JSON);
//        }
//        // Use short links
//        request.getHeaders().add(HttpHeaders.CONNECTION, "close");
//        return execution.execute(request, body);
//    }
//}
