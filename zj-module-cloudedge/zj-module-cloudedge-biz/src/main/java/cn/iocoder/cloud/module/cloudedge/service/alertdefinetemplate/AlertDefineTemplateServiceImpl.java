package cn.iocoder.cloud.module.cloudedge.service.alertdefinetemplate;

import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.cloud.module.cloudedge.controller.admin.alertdefinetemplate.vo.*;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.alertdefinetemplate.AlertDefineTemplateDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.cloud.module.cloudedge.convert.alertdefinetemplate.AlertDefineTemplateConvert;
import cn.iocoder.cloud.module.cloudedge.dal.mysql.alertdefinetemplate.AlertDefineTemplateMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.cloud.module.cloudedge.enums.ErrorCodeConstants.*;

/**
 * 多维告警模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AlertDefineTemplateServiceImpl implements AlertDefineTemplateService {

    @Resource
    private AlertDefineTemplateMapper alertDefineTemplateMapper;

    @Override
    @TenantIgnore
    public Long createAlertDefineTemplate(AlertDefineTemplateCreateReqVO createReqVO) {
        // 插入
        AlertDefineTemplateDO alertDefineTemplate = AlertDefineTemplateConvert.INSTANCE.convert(createReqVO);
        alertDefineTemplateMapper.insert(alertDefineTemplate);
        // 返回
        return alertDefineTemplate.getId();
    }

    @Override
    @TenantIgnore
    public void updateAlertDefineTemplate(AlertDefineTemplateUpdateReqVO updateReqVO) {
        // 校验存在
        validateAlertDefineTemplateExists(updateReqVO.getId());
        // 更新
        AlertDefineTemplateDO updateObj = AlertDefineTemplateConvert.INSTANCE.convert(updateReqVO);
        alertDefineTemplateMapper.updateById(updateObj);
    }

    @Override
    @TenantIgnore
    public void deleteAlertDefineTemplate(Long id) {
        // 校验存在
        validateAlertDefineTemplateExists(id);
        // 删除
        alertDefineTemplateMapper.deleteById(id);
    }

    private void validateAlertDefineTemplateExists(Long id) {
        if (alertDefineTemplateMapper.selectById(id) == null) {
//            throw exception(ALERT_DEFINE_TEMPLATE_NOT_EXISTS);
        }
    }

    @Override
    @TenantIgnore
    public AlertDefineTemplateDO getAlertDefineTemplate(Long id) {
        return alertDefineTemplateMapper.selectById(id);
    }

    @Override
    @TenantIgnore
    public List<AlertDefineTemplateDO> getAlertDefineTemplateList(Collection<Long> ids) {
        return alertDefineTemplateMapper.selectBatchIds(ids);
    }

    @Override
    @TenantIgnore
    public PageResult<AlertDefineTemplateDO> getAlertDefineTemplatePage(AlertDefineTemplatePageReqVO pageReqVO) {
        return alertDefineTemplateMapper.selectPage(pageReqVO);
    }

    @Override
    @TenantIgnore
    public List<AlertDefineTemplateDO> getAlertDefineTemplateList(AlertDefineTemplateExportReqVO exportReqVO) {
        return alertDefineTemplateMapper.selectList(exportReqVO);
    }

}
