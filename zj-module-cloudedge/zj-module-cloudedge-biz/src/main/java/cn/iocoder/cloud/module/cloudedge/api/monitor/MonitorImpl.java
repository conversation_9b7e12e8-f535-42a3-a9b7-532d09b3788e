package cn.iocoder.cloud.module.cloudedge.api.monitor;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.cloud.module.cloudedge.api.monitor.dto.AlarmReqDTO;
import cn.iocoder.cloud.module.cloudedge.api.monitor.dto.MonitorDTO;
import cn.iocoder.cloud.module.cloudedge.api.monitor.dto.MonitorHierarchy;
import cn.iocoder.cloud.module.cloudedge.api.monitor.dto.MonitorReq;
import cn.iocoder.cloud.module.cloudedge.convert.monitor.MonitorConvert;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.herzbeat.Hierarchy;
import cn.iocoder.cloud.module.cloudedge.dal.mysql.dao.ParamDao;
import cn.iocoder.cloud.module.cloudedge.service.AppService;
import cn.iocoder.cloud.module.cloudedge.service.MonitorService;
import cn.iocoder.zj.framework.common.enums.ResourceAppEnum;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.util.category.ResourceCategoryUtil;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import jodd.util.StringUtil;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.hertzbeat.common.entity.job.Job;
import org.apache.hertzbeat.common.entity.job.Metrics;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.apache.hertzbeat.common.entity.manager.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

/**
 * @ClassName : MonitorImpl //类名
 * @Description : //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/6/7 11:19
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class MonitorImpl implements MonitorApi {
    @Resource
    private MonitorService monitorService;
    @Resource
    private ParamDao paramDao;
    @Autowired
    private AppService appService;
    @Resource
    private PlatformconfigApi platformconfigApi;

    @Override
    public MonitorDTO selectMonitorInfo(Long id) {
        return MonitorConvert.INSTANCE.convert(monitorService.getMonitorDto(id));
    }

    @Override
    public List<Param> selectMonitorParamInfo(Long id) {
        return paramDao.findParamsByMonitorId(id);
    }

    @Override
    public List<Monitor> getMonitorByplatformId(Long platformId) {
        Set<String> app = new HashSet<>();
        // 安全
        List<ResourceAppEnum> firewall = ResourceCategoryUtil.getSubEnumsByCategory("firewall");
        if (!firewall.isEmpty()) {
            for (ResourceAppEnum resourceAppEnum : firewall) {
                app.add(resourceAppEnum.getValue());
            }

        }
        // 安全
        List<ResourceAppEnum> network = ResourceCategoryUtil.getSubEnumsByCategory("network");
        if (!network.isEmpty()) {
            for (ResourceAppEnum resourceAppEnum : network) {
                app.add(resourceAppEnum.getValue());
            }
        }
        // 操作系统
        List<ResourceAppEnum> os = ResourceCategoryUtil.getSubEnumsByCategory("os");
        if (!os.isEmpty()) {
            for (ResourceAppEnum resourceAppEnum : os) {
                app.add(resourceAppEnum.getValue());
            }
        }
        return monitorService.getMonitorByplatformId(platformId, app);
    }

    @Override
    public List<Monitor> getMonitorByplatformIdAndCategory(Long platformId, String category) {
        Set<String> app = new HashSet<>();
        if (StringUtil.isNotEmpty(category)) {
            List<ResourceAppEnum> resourceApps = ResourceCategoryUtil.getSubEnumsByCategory(category);

            if (CollectionUtil.isNotEmpty(resourceApps)) {
                for (ResourceAppEnum resourceAppEnum : resourceApps) {
                    app.add(resourceAppEnum.getValue());
                }
            }
        }

        return monitorService.getMonitorByplatformId(platformId, app);
    }

    @Override
    public void updateMonitor(Long id, String name) {
        monitorService.updateMonitor(id, name);
    }

    @Override
    public List<Map<String, Object>> getAppHierarchy() {
        List<Hierarchy> hierarchies = appService.getAllAppHierarchy("zh-CN");
        // hierarchies 转 List<Map>
        List<Map<String, Object>> hierarchyMaps = new ArrayList<>();
        for (Hierarchy hierarchy : hierarchies) {
            Map<String, Object> hierarchyMap = new HashMap<>();
            hierarchyMap.put("category", hierarchy.getCategory());
            hierarchyMap.put("value", hierarchy.getValue());
            hierarchyMap.put("label", hierarchy.getLabel());
            hierarchyMaps.add(hierarchyMap);
        }
        return hierarchyMaps;
    }

    @Override
    public List<MonitorHierarchy> getAllMonitorHierarchy() {
        List<Hierarchy> hierarchies = appService.getAllAppHierarchy("zh-CN");
        List<MonitorHierarchy> res = new ArrayList<>();
        for (Hierarchy hierarchy : hierarchies) {
            res.add(BeanUtil.copyProperties(hierarchy, MonitorHierarchy.class));
        }
        return res;
    }

    @Override
    public List<String> getAppDefine(String app) {
        Job job = appService.getAppDefine(app);
        return job.getMetrics().stream()
                .filter(Metrics::isVisible)
                .map(Metrics::getName).collect(Collectors.toList());
    }

    @Override
    public String getOsMonitorList(Long platformId, String category, long limit) {
        Map<String, List<Map<String, Object>>> osMonitorList = monitorService.getOsMonitorList(null, category, limit, platformId);
        return JSONUtil.toJsonStr(osMonitorList);
    }

    @Override
    public CommonResult<List<Monitor>> getMonitorByTenantId(MonitorReq req) {
//        List<String> categoryList = req.getCategoryList();
        Long tenantId = req.getTenantId();
        List<Long> platformIds = req.getPlatformIds();
        Set<String> app = new HashSet<>();
//        if (CollectionUtil.isNotEmpty(categoryList)) {
//            categoryList.forEach(category -> {
//                List<ResourceAppEnum> resourceApps = ResourceCategoryUtil.getSubEnumsByCategory(category);
//                if (CollectionUtil.isNotEmpty(resourceApps)) {
//                    resourceApps.forEach(resourceAppEnum -> {
//                        app.add(resourceAppEnum.getValue());
//                    });
//                }
//            });
//        }
        List<Monitor> monitorByCategoryList;
        if (CollectionUtil.isEmpty(platformIds)) {
            //按照租户id查询数据
            monitorByCategoryList = monitorService.getMonitorByTenantId(tenantId);
        } else {
            //按照平台id查询数据
            monitorByCategoryList = monitorService.getMonitorByPlatformIds(platformIds);
        }
        if (CollectionUtil.isNotEmpty(monitorByCategoryList)) {
            monitorByCategoryList.forEach(monitor -> {
                if (StringUtil.isEmpty(monitor.getCategory())) {
                    List<Hierarchy> appHierarchy = appService.getAppHierarchy(monitor.getApp(), "zh-CN");
                    String category = !appHierarchy.isEmpty() ? appHierarchy.stream().findFirst().map(Hierarchy::getCategory).orElse(null) : null;
                    monitor.setCategory(category);
                }
            });
        }
        return CommonResult.success(monitorByCategoryList);
    }

    @Override
    public AlarmReqDTO getAlarmByMonitorId(Long monitorId) {
        return monitorService.getAlarmByMonitorId(monitorId);
    }
}
