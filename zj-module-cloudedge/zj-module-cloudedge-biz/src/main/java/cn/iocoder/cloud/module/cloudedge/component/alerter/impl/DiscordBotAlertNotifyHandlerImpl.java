package cn.iocoder.cloud.module.cloudedge.component.alerter.impl;

import cn.iocoder.zj.framework.common.exception.AlertNoticeException;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.alerter.Alert;
import org.apache.hertzbeat.common.entity.manager.NoticeReceiver;
import org.apache.hertzbeat.common.entity.manager.NoticeTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> href="mailto:<EMAIL>">gcdd1993</a>
 * Created by gcdd1993 on 2023/1/19
 */
@Component
@RequiredArgsConstructor
@Slf4j
final class DiscordBotAlertNotifyHandlerImpl extends AbstractAlertNotifyHandlerImpl {

    @Override
    public void send(NoticeReceiver receiver, NoticeTemplate noticeTemplate, Alert alert) throws AlertNoticeException {
        try {
            var notifyBody = DiscordNotifyDTO.builder()
                    .embeds(List.of(EmbedDTO.builder()
                            .title("[" + bundle.getString("alerter.notify.title") + "]")
                            .description(renderContent(noticeTemplate, alert))
                            .build()))
                    .build();
            var url = String.format(alerterProperties.getDiscordWebhookUrl(), receiver.getDiscordChannelId());
            var headers = new HttpHeaders();
            headers.add("Authorization", "Bot " + receiver.getDiscordBotToken());
            headers.setContentType(MediaType.APPLICATION_JSON);
            var request = new HttpEntity<>(notifyBody, headers);
            var entity = restTemplate.postForEntity(url, request, DiscordResponseDTO.class);
            if (entity.getStatusCode() == HttpStatus.OK && entity.getBody() != null) {
                var body = entity.getBody();
                if (body.id != null) {
                    log.debug("Send Discord Bot Success");
                } else {
                    log.warn("Send Discord Bot Failed: {}, error_code: {}", body.code, body.message);
                    throw new AlertNoticeException(body.message);
                }
            } else {
                log.warn("Send Discord Bot Failed {}", entity.getBody());
                throw new AlertNoticeException("Http StatusCode " + entity.getStatusCode());
            }
        } catch (Exception e) {
            throw new AlertNoticeException("[Discord Bot Notify Error] " + e.getMessage());
        }
    }

    @Override
    public byte type() {
        return 9;
    }

    @Data
    @Builder
    private static class DiscordNotifyDTO {
        private List<EmbedDTO> embeds;
    }

    @Data
    @Builder
    private static class EmbedDTO {
        private String title;
        private String description;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    private static class DiscordResponseDTO {
        private String id;
        private Integer type;
        private String content;
        private String message;
        private Integer code;
    }

}
