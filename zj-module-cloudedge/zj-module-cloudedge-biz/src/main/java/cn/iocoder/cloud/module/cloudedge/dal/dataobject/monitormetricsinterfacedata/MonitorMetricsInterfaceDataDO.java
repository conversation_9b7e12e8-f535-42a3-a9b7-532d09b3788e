package cn.iocoder.cloud.module.cloudedge.dal.dataobject.monitormetricsinterfacedata;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;

/**
 * 监控实时数据 DO
 *
 * <AUTHOR>
 */
@TableName("hzb_monitor_metrics_interface_data")
@KeySequence("hzb_monitor_metrics_interface_data_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MonitorMetricsInterfaceDataDO{

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 监控id
     */
    private Long monitorId;
    /**
     * 指标
     */
    private String metrics;
    /**
     * index
     */
    private String interfaceIndex;
    /**
     * 接口名称
     */
    private String interfaceName;
    /**
     * 接口描述
     */
    private String alias;
    /**
     * 管理口状态
     */
    private String adminStatus;
    /**
     * 操作口状态
     */
    private String operStatus;

}
