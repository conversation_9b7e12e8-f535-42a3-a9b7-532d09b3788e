package cn.iocoder.cloud.module.cloudedge.controller.admin.collectorplatform.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 采集器平台关联 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class CollectorPlatformExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("采集器id")
    private Long collectorId;

    @ExcelProperty("采集器名称")
    private String collectorName;

    @ExcelProperty("平台id")
    private Long platformId;

    @ExcelProperty("平台名称")
    private String platformName;

    @ExcelProperty("存活状态")
    private Byte status;

}
