package cn.iocoder.cloud.module.cloudedge.adapter;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 监控类型枚举
 */
@Getter
@RequiredArgsConstructor
public enum MonitorType {
    
    SYSTEM(0, "system", "系统监控"),
    CPU(1, "cpu", "CPU监控"),
    MEMORY(2, "memory", "内存监控"),
    LINUX_INTERFACE(3, "interface", "接口监控"),
    WINDOW_INTERFACES(4, "interfaces", "接口监控"),
    DISK(5, "disk", "磁盘监控"),
    DISK_FREE(6, "disk_free", "文件系统监控"),
    STORAGES(7, "storages", "存储监控"),
    STORES(8, "stores", "存储监控"),
    SYSTEMSTORAGE(9, "systemstorage", "系统存储监控"),
    DRIVE_INFO_TABLE(10, "driveInfoTable", "磁盘组信息"),
    PROCESSES(11, "processes", "进程");
    /**
     * 类型编码
     */
    private final int code;
    
    /**
     * 类型标识
     */
    private final String type;
    
    /**
     * 类型描述
     */
    private final String description;

    /**
     * 根据类型获取枚举
     */
    public static MonitorType getByType(String type) {
        if (type == null) {
            return null;
        }
        for (MonitorType value : MonitorType.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
