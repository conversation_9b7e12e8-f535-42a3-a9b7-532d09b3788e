package cn.iocoder.cloud.module.cloudedge.controller.admin.monitormetricsinterfacedata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;

@Schema(description = "管理后台 - 监控实时数据更新 Request VO")
@Data
@ToString(callSuper = true)
public class MonitorMetricsInterfaceDataUpdateReqVO  {

    @Schema(description = "监控id", required = true)
    @NotNull(message = "监控id不能为空")
    private Long monitorId;

    @Schema(description = "指标", required = true)
    @NotNull(message = "指标不能为空")
    private String metrics;

    @Schema(description = "interfaceIndex", required = true)
    @NotNull(message = "index不能为空")
    private String interfaceIndex;

    @Schema(description = "接口描述", required = true)
    @NotNull(message = "接口描述不能为空")
    private String alias;

}
