/// *
// * Licensed to the Apache Software Foundation (ASF) under one or more
// * contributor license agreements.  See the NOTICE file distributed with
// * this work for additional information regarding copyright ownership.
// * The ASF licenses this file to You under the Apache License, Version 2.0
// * (the "License"); you may not use this file except in compliance with
// * the License.  You may obtain a copy of the License at
// *
// *     http://www.apache.org/licenses/LICENSE-2.0
// *
// * Unless required by applicable law or agreed to in writing, software
// * distributed under the License is distributed on an "AS IS" BASIS,
// * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// * See the License for the specific language governing permissions and
// * limitations under the License.
// */
//
//package cn.iocoder.cloud.module.cloudedge.config;
//
//import org.flywaydb.core.Flyway;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.boot.autoconfigure.flyway.FlywayMigrationInitializer;
//import org.springframework.boot.autoconfigure.flyway.FlywayProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.DependsOn;
//
///**
// * flyway database migration config
// */
//@Configuration
//@ConditionalOnProperty(prefix = "spring.flyway", name = "enabled", havingValue = "true")
//public class FlywayConfiguration {
//
//    @Bean
//    public FlywayMigrationInitializer flywayInitializer(Flyway flyway) {
//        return new FlywayMigrationInitializer(flyway, (f) -> {
//        });
//    }
//
//    static class Dummy {
//    }
//
//    @Bean
//    @DependsOn("entityManagerFactory")
//    Dummy delayedFlywayInitializer(Flyway flyway, FlywayProperties flywayProperties) {
//        if (flywayProperties.isEnabled()) {
//            flyway.migrate();
//        }
//        return new Dummy();
//    }
//}
