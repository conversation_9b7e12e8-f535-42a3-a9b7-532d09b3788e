package cn.iocoder.cloud.module.cloudedge.controller.admin.alertdefinetemplate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 多维告警模板 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class AlertDefineTemplateExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("告警名称")
    private String name;

    @ExcelProperty("app")
    private String app;

    @ExcelProperty("规则")
    private String expr;

    @ExcelProperty("field")
    private String field;

    @ExcelProperty("监控")
    private String metric;

    @ExcelProperty("告警级别")
    private Byte priority;

    @ExcelProperty("告警通知内容模版")
    private String template;

    @ExcelProperty("触发次数")
    private Integer times;

}
