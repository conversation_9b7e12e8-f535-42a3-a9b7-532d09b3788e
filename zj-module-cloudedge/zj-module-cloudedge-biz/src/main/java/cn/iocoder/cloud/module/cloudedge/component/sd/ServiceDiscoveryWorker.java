/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.module.cloudedge.component.sd;

import cn.iocoder.cloud.module.cloudedge.dal.mysql.dao.CollectorMonitorBindDao;
import cn.iocoder.cloud.module.cloudedge.dal.mysql.dao.MonitorBindDao;
import cn.iocoder.cloud.module.cloudedge.dal.mysql.dao.MonitorDao;
import cn.iocoder.cloud.module.cloudedge.dal.mysql.dao.ParamDao;
import cn.iocoder.cloud.module.cloudedge.scheduler.ManagerWorkerPool;
import cn.iocoder.cloud.module.cloudedge.service.MonitorService;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hertzbeat.common.constants.CommonConstants;
import org.apache.hertzbeat.common.entity.manager.*;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.apache.hertzbeat.common.queue.CommonDataQueue;
import org.apache.hertzbeat.common.util.SdMonitorOperator;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Service Discovery Worker
 */
@Slf4j
@Component
public class ServiceDiscoveryWorker implements InitializingBean {
    private final MonitorService monitorService;
    private final ParamDao paramDao;
    private final MonitorDao monitorDao;
    private final MonitorBindDao monitorBindDao;
    private final CollectorMonitorBindDao collectorMonitorBindDao;
    private final CommonDataQueue dataQueue;
    private final ManagerWorkerPool workerPool;

    public ServiceDiscoveryWorker(MonitorService monitorService, ParamDao paramDao, MonitorDao monitorDao,
                                  MonitorBindDao monitorBindDao, CollectorMonitorBindDao collectorMonitorBindDao,
                                  CommonDataQueue dataQueue, ManagerWorkerPool workerPool) {
        this.monitorService = monitorService;
        this.paramDao = paramDao;
        this.monitorDao = monitorDao;
        this.monitorBindDao = monitorBindDao;
        this.collectorMonitorBindDao = collectorMonitorBindDao;
        this.dataQueue = dataQueue;
        this.workerPool = workerPool;
    }

    @Override
    public void afterPropertiesSet() {
        workerPool.executeJob(new SdUpdateTask());
    }

    private class SdUpdateTask implements Runnable {
        @Override
        public void run() {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    final CollectRep.MetricsData serviceDiscoveryData = dataQueue.pollServiceDiscoveryData();
                    if (Objects.isNull(serviceDiscoveryData)) {
                        continue;
                    }

                    final Monitor mainMonitor = monitorDao.findMonitorsByIdIn(Sets.newHashSet(serviceDiscoveryData.getId())).get(0);
                    mainMonitor.setTags(mainMonitor.getTags().stream().filter(tag -> tag.getType() != CommonConstants.TAG_TYPE_AUTO_GENERATE).collect(Collectors.toList()));
                    // collector
                    final Optional<CollectorMonitorBind> collectorBind = collectorMonitorBindDao.findCollectorMonitorBindByMonitorId(mainMonitor.getId());
                    String collector = collectorBind.map(CollectorMonitorBind::getCollector).orElse(null);
                    // param
                    List<Param> mainMonitorParamList = paramDao.findParamsByMonitorId(mainMonitor.getId());
                    mainMonitorParamList = SdMonitorOperator.removeSdParam(mainMonitorParamList);

                    final Set<Long> subMonitorIdSet = monitorBindDao.findMonitorBindByBizIdAndType(serviceDiscoveryData.getId(), CommonConstants.MONITOR_BIND_TYPE_SD_SUB_MONITOR)
                            .stream()
                            .map(MonitorBind::getMonitorId)
                            .collect(Collectors.toSet());
                    final Map<String, List<Monitor>> hostMonitorMap = CollectionUtils.isEmpty(subMonitorIdSet)
                            ? Maps.newHashMap()
                            : monitorDao.findMonitorsByIdIn(subMonitorIdSet).stream().collect(Collectors.groupingBy(Monitor::getHost));

                    for (CollectRep.ValueRow row : serviceDiscoveryData.getValuesList()) {
                        final String host = row.getColumns(CommonConstants.SD_HOST_COLUMN);
                        final String port = row.getColumns(CommonConstants.SD_PORT_COLUMN);
                        final List<Monitor> monitorList = hostMonitorMap.get(host);
                        if (CollectionUtils.isEmpty(monitorList)) {
                            monitorService.addAndSaveMonitorJob(mainMonitor.clone(), SdMonitorOperator.cloneParamList(mainMonitorParamList), collector,
                                    SdMonitorParam.builder()
                                            .detectedHost(host)
                                            .detectedPort(port)
                                            .bizId(mainMonitor.getId())
                                            .build(), null);
                            continue;
                        }

                        for (Monitor monitor : monitorList) {
                            // make sure monitor that has the same host and port is not existed.
                            final Optional<Param> samePortParam = paramDao.findParamsByMonitorId(monitor.getId()).stream()
                                    .filter(param -> StringUtils.equals(param.getField(), "port"))
                                    .filter(param -> StringUtils.equals(param.getValue(), port))
                                    .findFirst();
                            if (samePortParam.isEmpty()) {
                                monitorService.addAndSaveMonitorJob(mainMonitor.clone(), SdMonitorOperator.cloneParamList(mainMonitorParamList), collector,
                                        SdMonitorParam.builder()
                                                .detectedHost(host)
                                                .detectedPort(port)
                                                .bizId(mainMonitor.getId())
                                                .build(), null);
                            } else {
                                monitorService.enableManageMonitors(Sets.newHashSet(monitor.getId()));
                            }
                        }

                        // make sure hostMonitorMap contains monitors that have not judged yet.
                        hostMonitorMap.remove(host);
                    }

                    // hostMonitorMap only contains monitors which are already existed but not in service discovery data
                    // due to monitors that coincide with service discovery data are removed.
                    // Thus, all monitors still in hostMonitorMap need to be cancelled.
                    final HashSet<Long> needCancelMonitorIdSet = Sets.newHashSet();
                    hostMonitorMap.forEach((key, value) -> needCancelMonitorIdSet.addAll(value.stream()
                            .map(Monitor::getId)
                            .collect(Collectors.toSet())));
                    monitorService.cancelManageMonitors(needCancelMonitorIdSet);
                } catch (Exception exception) {
                    log.error(exception.getMessage(), exception);
                }
            }
        }
    }
}