package cn.iocoder.cloud.module.cloudedge.service;

/**
 * ConfigService接口，提供配置的增删查改操作。
 *
 * <p>ConfigService interface provides CRUD operations for configurations.</p>
 * <AUTHOR>
 * @param <T> 配置类型
 * @version 1.0
 */
public interface GeneralConfigService<T> {


    /**
     * config type: email, sms
     * @return type string
     */
    String type();

    /**
     * save config
     * @param config need save configuration
     */
    void saveConfig(T config);

    /**
     * get config
     * @return query The configuration is queried
     */
    T getConfig();

    /**
     * handler after save config
     * @param config config
     */
    default void handler(T config) {}
}
