/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.module.cloudedge.controller.admin;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.collectorplatform.CollectPlatfDTO;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.herzbeat.Hierarchy;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.herzbeat.MonitorDefineDto;
import cn.iocoder.cloud.module.cloudedge.dal.mysql.collectorplatform.CollectorPlatformMapper;
import cn.iocoder.cloud.module.cloudedge.service.AppService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import org.apache.hertzbeat.common.entity.dto.Message;
import org.apache.hertzbeat.common.entity.job.Job;
import org.apache.hertzbeat.common.entity.manager.ParamDefine;
import org.apache.hertzbeat.common.util.ResponseUtil;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

/**
 * Monitoring Type Management API
 * 监控类型管理API
 *
 * <AUTHOR>
 */
@Tag(name = "Monitor Type Manage API | 监控类型管理API")
@RestController
@RequestMapping(path = "/cloudedge/api/apps", produces = {APPLICATION_JSON_VALUE})
public class AppController {

    private static final String[] RISKY_STR_ARR = {"ScriptEngineManager", "URLClassLoader", "!!",
            "ClassLoader", "AnnotationConfigApplicationContext", "FileSystemXmlApplicationContext",
            "GenericXmlApplicationContext", "GenericGroovyApplicationContext", "GroovyScriptEngine",
            "GroovyClassLoader", "GroovyShell", "ScriptEngine", "ScriptEngineFactory", "XmlWebApplicationContext",
            "ClassPathXmlApplicationContext", "MarshalOutputStream", "InflaterOutputStream", "FileOutputStream"};

    @Autowired
    private AppService appService;

    @Autowired
    private CollectorPlatformMapper collectorPlatformMapper;

    @GetMapping(path = "/{app}/params")
    @Operation(summary = "The structure of the input parameters required to specify the monitoring type according to the app query",
            description = "The structure of the input parameters required to specify the monitoring type according to the app query")
    public ResponseEntity<Message<List<ParamDefine>>> queryAppParamDefines(
            @Parameter(description = "en: Monitoring type name", example = "api") @PathVariable("app") final String app) {
        return ResponseUtil.handle(() -> appService.getAppParamDefines(app.toLowerCase()));
    }

    @GetMapping(path = "/{monitorId}/pushdefine")
    @Operation(summary = "The definition structure of the specified monitoring type according to the push query",
            description = "The definition structure of the specified monitoring type according to the push query")
    public ResponseEntity<Message<Job>> queryPushDefine(
            @Parameter(description = "en: Monitoring type name", example = "api") @PathVariable("monitorId") final Long monitorId) {
        return ResponseUtil.handle(() -> appService.getPushDefine(monitorId));
    }

    @GetMapping(path = "/{monitorId}/define/dynamic")
    @Operation(summary = "The definition structure of the specified monitoring type according to the push query",
            description = "The definition structure of the specified monitoring type according to the push query")
    public ResponseEntity<Message<Job>> queryAutoGenerateDynamicAppDefine(
            @Parameter(description = "Monitoring id", example = "5435345") @PathVariable("monitorId") final Long monitorId) {
        return ResponseUtil.handle(() -> appService.getAutoGenerateDynamicDefine(monitorId));
    }

    @GetMapping(path = "/{app}/define")
    @Operation(summary = "The definition structure of the specified monitoring type according to the app query",
            description = "The definition structure of the specified monitoring type according to the app query")
    public ResponseEntity<Message<Job>> queryAppDefine(
            @Parameter(description = "en: Monitoring type name", example = "api") @PathVariable("app") final String app) {
        return ResponseUtil.handle(() -> appService.getAppDefine(app.toLowerCase()));
    }

    @GetMapping(path = "/{app}/define/yml")
    @Operation(summary = "The definition yml of the specified monitoring type according to the app query",
            description = "The definition yml of the specified monitoring type according to the app query")
    public ResponseEntity<Message<String>> queryAppDefineYml(
            @Parameter(description = "en: Monitoring type name", example = "api") @PathVariable("app") final String app) {
        return ResponseUtil.handle(() -> appService.getMonitorDefineFileContent(app));
    }

    @DeleteMapping(path = "/{app}/define/yml")
    @Operation(summary = "Delete monitor define yml", description = "Delete the definition YML for the specified monitoring type according to the app")
    public ResponseEntity<Message<Void>> deleteAppDefineYml(
            @Parameter(description = "en: Monitoring type name", example = "api") @PathVariable("app") final String app) {
        return ResponseUtil.handle(() -> appService.deleteMonitorDefine(app));
    }

    @PostMapping(path = "/define/yml")
    @Operation(summary = "Add new monitoring type define yml", description = "Add new monitoring type define yml")
    public ResponseEntity<Message<Void>> newAppDefineYml(@Valid @RequestBody MonitorDefineDto defineDto) {
        return ResponseUtil.handle(() -> {
            for (String riskyToken : RISKY_STR_ARR) {
                if (defineDto.getDefine().contains(riskyToken)) {
                    throw new RuntimeException("can not has malicious remote script");
                }
            }
            appService.applyMonitorDefineYml(defineDto.getDefine(), false);
        });
    }

    @PutMapping(path = "/define/yml")
    @Operation(summary = "Update monitoring type define yml", description = "Update monitoring type define yml")
    public ResponseEntity<Message<Void>> updateAppDefineYml(@Valid @RequestBody MonitorDefineDto defineDto) {
        return ResponseUtil.handle(() -> {
            for (String riskyToken : RISKY_STR_ARR) {
                if (defineDto.getDefine().contains(riskyToken)) {
                    throw new RuntimeException("can not has malicious remote script");
                }
            }
            appService.applyMonitorDefineYml(defineDto.getDefine(), true);
        });
    }

    @GetMapping(path = "/hierarchy")
    @Operation(summary = "Query all monitor metrics level, output in a hierarchical structure", description = "Query all monitor metrics level, output in a hierarchical structure")
    public ResponseEntity<Message<List<Hierarchy>>> queryAppsHierarchy(
            @Parameter(description = "en: language type",
                    example = "zh-CN")
            @RequestParam(name = "lang", required = false) String lang) {
        String newLang = getLang(lang);
        return ResponseUtil.handle(() -> appService.getAllAppHierarchy(newLang));
    }

    @GetMapping(path = "/hierarchy/{app}")
    @Operation(summary = "Query all monitor metrics level, output in a hierarchical structure", description = "Query all monitor metrics level, output in a hierarchical structure")
    public ResponseEntity<Message<List<Hierarchy>>> queryAppsHierarchyByApp(
            @Parameter(description = "en: language type",
                    example = "zh-CN")
            @RequestParam(name = "lang", required = false) String lang,
            @Parameter(description = "en: Monitoring type name", example = "api") @PathVariable("app") final String app) {
        String newLang = getLang(lang);
        return ResponseUtil.handle(() -> appService.getAppHierarchy(app, newLang));
    }

    @GetMapping(path = "/defines")
    @Operation(summary = "Query all monitor types", description = "Query all monitor types")
    public ResponseEntity<Message<Map<String, String>>> getAllAppDefines(
            @Parameter(description = "en: language type",
                    example = "zh-CN")
            @RequestParam(name = "lang", required = false) String lang) {
        String newLang = getLang(lang);
        return ResponseUtil.handle(() -> appService.getI18nApps(newLang));
    }

    private String getLang(@RequestParam(name = "lang", required = false) @Parameter(description = "en: language type", example = "zh-CN") String lang) {
        if (lang == null || lang.isEmpty()) {
            lang = "zh-CN";
        }
        if (lang.contains(Locale.ENGLISH.getLanguage())) {
            lang = "en-US";
        } else if (lang.contains(Locale.CHINESE.getLanguage())) {
            lang = "zh-CN";
        } else {
            lang = "en-US";
        }
        return lang;
    }


    @GetMapping(path = "/savehierarchy")
    public void saveAppsHierarchy(@Parameter(description = "en: language type", example = "zh-CN")
                                  @RequestParam(name = "lang", required = false) String lang) {
        String newLang = getLang(lang);
        List<Hierarchy> allAppHierarchy = appService.getAllAppHierarchy(newLang);
        Map<String, List<Hierarchy>> collect = allAppHierarchy.stream().collect(Collectors.groupingBy(Hierarchy::getCategory));
        List<Map<String, String>> dicts = collectorPlatformMapper.getDicts();

        Map<String, String> result = dicts.stream()
                .filter(m -> m.get("key") != null && m.get("value") != null)
                .collect(Collectors.toMap(
                        m -> m.get("key"),    // 以 key 为 map 的 key
                        m -> m.get("value"),  // 以 value 为 map 的 value
                        (v1, v2) -> v1,       // 如果 key 重复，保留第一个
                        LinkedHashMap::new    // 保持顺序
                ));


        List<String> strings = readMetricsFromExcel("C:\\Users\\<USER>\\Desktop\\指标(4).xlsx");
        collectorPlatformMapper.saveMetirc(strings);

        collect.forEach((category, list) -> {
            String str = result.get(category);
            ReportMetrics reportMetrics = new ReportMetrics();
            reportMetrics.setMetricCode(category);
            reportMetrics.setMetricName(str);
            reportMetrics.setPid(0L);
            reportMetrics.setLevel(0);
            reportMetrics.setNameValue(str);
            reportMetrics.setCodeValue(category);
//            collectorPlatformMapper.saveReportMetrics(reportMetrics);
//            insertMetrics(reportMetrics, 1, list);
        });
        System.out.println("层级数据保存完成");
    }

    private void insertMetrics(ReportMetrics report, int level, List<Hierarchy> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }

        for (Hierarchy hierarchy : list) {
            ReportMetrics reportMetrics = new ReportMetrics();
            reportMetrics.setMetricCode(hierarchy.getValue());
            reportMetrics.setMetricName(hierarchy.getLabel());
            reportMetrics.setLevel(level);
            reportMetrics.setPid(report.id);
            reportMetrics.setNameValue(report.getNameValue()+"-"+hierarchy.getLabel());
            reportMetrics.setCodeValue(report.getCodeValue()+"-"+hierarchy.getValue());
            collectorPlatformMapper.saveReportMetrics(reportMetrics);

            // 递归处理子节点
            if (CollUtil.isNotEmpty(hierarchy.getChildren())) {
                insertMetrics(reportMetrics, level + 1, hierarchy.getChildren());
            }
        }
    }
    public static List<String> readMetricsFromExcel(String filePath) {
        List<String> metrics = new ArrayList<>();

        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = new XSSFWorkbook(fis)) {

            // 只处理"监控管理-TOP报表"这个sheet
            Sheet sheet = workbook.getSheetAt(2); // 第三个sheet

            String currentCategory = "";
            String currentSubCategory = "";
            String currentMetricType = "";

            for (Row row : sheet) {
                // 跳过表头
                if (row.getRowNum() == 0) continue;

                // 获取单元格值
                Cell categoryCell = row.getCell(0); // A列 - 大类
                Cell subCategoryCell = row.getCell(1); // B列 - 小类
                Cell metricTypeCell = row.getCell(2); // C列 - 指标小类
                Cell metricCell = row.getCell(3); // D列 - 具体指标

                // 更新当前分类信息
                if (categoryCell != null && !categoryCell.getStringCellValue().isEmpty()) {
                    currentCategory = categoryCell.getStringCellValue();
                }

                if (subCategoryCell != null && !subCategoryCell.getStringCellValue().isEmpty()) {
                    currentSubCategory = subCategoryCell.getStringCellValue();
                }

                if (metricTypeCell != null && !metricTypeCell.getStringCellValue().isEmpty()) {
                    currentMetricType = metricTypeCell.getStringCellValue();
                }

                // 如果具体指标不为空，则拼接完整指标路径
                if (metricCell != null && !metricCell.getStringCellValue().isEmpty()) {
                    String metric = metricCell.getStringCellValue();
                    String fullMetricPath = currentCategory + "-" + currentSubCategory + "-" +
                            currentMetricType + "-" + metric;
                    metrics.add(fullMetricPath);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

        return metrics;
    }


    @Data
    public class ReportMetrics {
        private Long id; // 主键

        private String metricName; // 指标名称

        private String metricCode; // 指标代码

        private String nameValue;

        private String codeValue;

        private Long pid; // 父级 ID

        private Integer level; // 等级
    }
}
