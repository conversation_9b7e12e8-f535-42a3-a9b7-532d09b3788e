package cn.iocoder.cloud.module.cloudedge.adapter.impl;

import cn.iocoder.cloud.module.cloudedge.adapter.AbstractMonitorAdapter;
import cn.iocoder.cloud.module.cloudedge.adapter.MonitorType;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 磁盘监控适配器实现
 */
@Slf4j
@Service
public class DiskAdapterImpl extends AbstractMonitorAdapter {

    @Override
    public MonitorType getMonitorType() {
        return MonitorType.DISK;
    }

    @Override
    public Map<String, List<Map<String, Object>>> handleMonitorTop10MetricsData(CollectRep.MetricsData metricsData) {
        return null;
    }


    @Override
    protected void processMetricsData(Monitor monitor, CollectRep.MetricsData metricsData) {
        // 获取磁盘状态的字段索引
        int diskStatus = getFieldIndex(metricsData, "diskStatus");
        //遍历磁盘状态字段
        if (diskStatus != -1) {
            // 使用 Stream API 优化遍历和计数逻辑
            long normalCount = metricsData.getValuesList().stream()
                    .map(valueRow -> valueRow.getColumns(diskStatus))
                    .filter("1"::equals)
                    .count();
            
            // 设置正常和异常磁盘数量
            monitor.setDiskNormal((int) normalCount);
            monitor.setDiskAbnormal(metricsData.getValuesCount() - (int) normalCount);
        }
    }
} 