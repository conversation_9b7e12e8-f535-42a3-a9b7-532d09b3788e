package cn.iocoder.cloud.module.cloudedge.adapter.impl;

import cn.iocoder.cloud.module.cloudedge.adapter.AbstractMonitorAdapter;
import cn.iocoder.cloud.module.cloudedge.adapter.MonitorType;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 文件系统监控适配器实现
 */
@Slf4j
@Service
public class DiskFreeAdapterImpl extends AbstractMonitorAdapter {

    @Override
    public MonitorType getMonitorType() {
        return MonitorType.DISK_FREE;
    }

    @Override
    public Map<String, List<Map<String, Object>>> handleMonitorTop10MetricsData(CollectRep.MetricsData metricsData) {
        return null;
    }

    @Override
    protected void processMetricsData(Monitor monitor, CollectRep.MetricsData metricsData) {
        // 获取文件系统的挂载点
        int mountPointIndex = getFieldIndex(metricsData, "mounted");
        if (mountPointIndex != -1) {
            // 获取文件挂载点的位置
            metricsData.getValuesList().forEach(valueRow -> {
                String mountPoint = valueRow.getColumns(mountPointIndex);
                if (mountPoint.equals("/")) {
                    // 获取磁盘使用率
                    int usageIndex = getFieldIndex(metricsData, "usage");
                    if (usageIndex != -1) {
                        String usage = valueRow.getColumns(usageIndex);
                        monitor.setDiskUsage(usage);
                    }
                }
            });
        }
    }
} 