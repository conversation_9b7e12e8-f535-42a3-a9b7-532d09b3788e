package cn.iocoder.cloud.module.cloudedge.framework.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Getter
@AllArgsConstructor
public enum ResourceConfigEnum {
    //应用服务枚举
    APP_SERVICE_WEBSITE("website", "网站",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
}}),
    APP_SERVICE_PORT("port", "端口",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("连接超时时间(ms)","timeout");put("regular","\\d+");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_SERVICE_SSL("ssl_cert", "SSL证书",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_SERVICE_PING("ping", "Ping连通性",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("Ping超时时间(ms)","timeout");put("regular","\\d+");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_SERVICE_WEBSOCKET("websocket", "WebSocket",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_SERVICE_API("api", "HTTP API",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("请求方式","method");put("regular","^.+$");}});
    add(new HashMap<>(){{put("相对路径","uri");put("regular",".*");}});
    add(new HashMap<>(){{put("启用HTTPS","ssl");put("regular","^.+$");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_SERVICE_DNS("dns", "DNS服务器监控",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("域名解析的地址","address");put("regular","^.+$");}});
    add(new HashMap<>(){{put("使用TCP协议","tcp");put("regular","^(?=.*[01])[01]+$");}});
    add(new HashMap<>(){{put("连接超时时间(ms)","timeout");put("regular","\\d+");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_SERVICE_FULLSITE("fullsite", "SiteMap全站",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("网站地图","sitemap");put("regular","^.+$");}});
    add(new HashMap<>(){{put("启用HTTPS","ssl");put("regular","^(?=.*[01])[01]+$");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_SERVICE_APICODE("api_code", "API业务状态码",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("请求方式","method");put("regular","^.+$");}});
    add(new HashMap<>(){{put("相对路径","uri");put("regular",".*");}});
    add(new HashMap<>(){{put("启用HTTPS","ssl");put("regular","^(?=.*[01])[01]+$");}});
    add(new HashMap<>(){{put("JsonPath解析状态码路径","jsonPath");put("regular","^.+$");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_SERVICE_UDP_PORT("udp_port", "UDP端口可用性",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("超时时间(ms)","timeout");put("regular","\\d+");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),


    // 数据库枚举
    APP_DB_MYSQL("mysql","MySQL",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){3}(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("数据库名称","database");put("regular","^.+$");}});
    add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
    add(new HashMap<>(){{put("用户名","username");put("regular","^.+$");}});
    add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_ORACLE("oracle","Oracle",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){3}(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("服务名","database");put("regular",".*");}});
    add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
    add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
    add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_SQLSERVER("sqlserver","SQLServer",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){3}(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("数据库名称","database");put("regular",".*");}});
    add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
    add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
    add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_MARIADB("mariadb","MariaDB",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){3}(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("数据库名称","database");put("regular",".*");}});
    add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
    add(new HashMap<>(){{put("查询超时时间(ms)","timeout");put("regular",".*");}});
    add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
    add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_DM("dm","达梦",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){3}(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
    add(new HashMap<>(){{put("超时时间","timeout");put("regular","\\d+");}});
    add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
    add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_REDIS("redis","Redis",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){3}(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
    add(new HashMap<>(){{put("超时时间","timeout");put("regular","\\d+");}});
    add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
    add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_TIDB("tidb","TiDB",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){3}(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("Service 端口","service-port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[1-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("PD 端口","pd-port");put("regular",".*");}});
    add(new HashMap<>(){{put("JDBC 端口","port");put("regular",".*");}});
    add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
    add(new HashMap<>(){{put("查询超时时间(ms)","timeout");put("regular",".*");}});
    add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
    add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_NEBULAGRAPH("nebulaGraph","NebulaGraph",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){3}(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("graph端口","graphPort");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[1-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("graph指标统计的时间范围(s)","timePeriod");put("regular","\\d+");}});
    add(new HashMap<>(){{put("storage端口","storagePort");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[1-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("连接超时时间(ms)","timeout");put("regular","\\d+");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_POSTGRESQL("postgresql","PostgreSQL",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){3}(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("数据库名称","database");put("regular",".*");}});
    add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
    add(new HashMap<>(){{put("查询超时时间(ms)","timeout");put("regular","\\d+");}});
    add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
    add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_MONGODB("mongodb","MongoDB",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){3}(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("数据库","database");put("regular","^.+$");}});
    add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
    add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
    add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
    add(new HashMap<>(){{put("认证数据库","authenticationDatabase");put("regular","^.+$");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_OPENGAUSS("opengauss","OpenGauss",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){3}(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("数据库名称","database");put("regular",".*");}});
    add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
    add(new HashMap<>(){{put("查询超时时间(ms)","timeout");put("regular",".*");}});
    add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
    add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_CLUSTER("redis_cluster","Redis Cluster",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){3}(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("数据库名称","database");put("regular",".*");}});
    add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
    add(new HashMap<>(){{put("查询超时时间(ms)","timeout");put("regular",".*");}});
    add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
    add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
    add(new HashMap<>(){{put("模式","pattern");put("regular","^.+$");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_SENTINEL("redis_sentinel","Redis Sentinel",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){3}(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
    add(new HashMap<>(){{put("查询超时时间(ms)","timeout");put("regular","\\d+");}});
    add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
    add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
    add(new HashMap<>(){{put("模式","pattern");put("regular","^.+$");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_MEMCACHED("memcached","Memcached",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){3}(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),

    // 中间件枚举
    APP_DB_ZOOKEEPER("zookeeper", "Zookeeper服务",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_KAFKA("kafka", "Kafka消息系统",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("JMX URL","url");put("regular",".*");}});
    add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
    add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_NACOS("nacos", "Nacos分布式",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_RABBITMQ("rabbitmq", "RabbitMQ消息系统",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("HTTP 端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[1-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("启动SSL","ssl");put("regular","^.+$");}});
    add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
    add(new HashMap<>(){{put("用户名","username");put("regular","^.+$");}});
    add(new HashMap<>(){{put("密码","password");put("regular","^.+$");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_NGINX("nginx", "Nginx服务器",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    add(new HashMap<>(){{put("连接超时时间(ms)","timeout");put("regular","\\d+");}});
    
}}),
    APP_DB_POP3("pop3", "POP3邮件服务器",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("启动SSL","ssl");put("regular","^.+$");}});
    add(new HashMap<>(){{put("POP邮箱地址","email");put("regular","^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$");}});
    add(new HashMap<>(){{put("授权码","authorize");put("regular","^.+$");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    add(new HashMap<>(){{put("连接超时时间(ms)","timeout");put("regular","\\d+");}});
    
}}),
    APP_DB_FTP("ftp", "FTP服务器",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("目录","direction");put("regular","^.+$");}});
    add(new HashMap<>(){{put("超时时间","timeout");put("regular","\\d+");}});
    add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
    add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_SMTP("smtp", "SMTP邮件服务器",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("email","email");put("regular","^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$");}});
    add(new HashMap<>(){{put("连接超时时间(ms)","timeout");put("regular","\\d+");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_NTP("ntp", "NTP服务器",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    add(new HashMap<>(){{put("连接超时时间(ms)","timeout");put("regular","\\d+");}});
    
}}),
    APP_DB_SPRING_GATEWAY("spring_gateway", "SpringGateway",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("启动SSL","ssl");put("regular","^.+$");}});
    add(new HashMap<>(){{put("Base Path","base_path");put("regular","^.+$");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_EMQX("emqx", "EMQX MQTT",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("启动HTTPS","ssl");put("regular","^.+$");}});
    add(new HashMap<>(){{put("API Key","apikey");put("regular","^.+$");}});
    add(new HashMap<>(){{put("Secret Key","secretkey");put("regular","^.+$");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),

    APP_SERVICE_TOMCAT("tomcat", "Tomcat应用服务",new ArrayList<>(){{
        add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
        add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
        add(new HashMap<>(){{put("JMX端口","port");put("regular",".*");}});
        add(new HashMap<>(){{put("JMX URL","url");put("regular",".*");}});
        add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
        add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
        add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
        add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
        add(new HashMap<>(){{put("备注","description");put("regular",".*");}});

    }}),

    APP_TOMEE("tomee", "TomEE应用服务",new ArrayList<>(){{
        add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
        add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
        add(new HashMap<>(){{put("JMX端口","port");put("regular",".*");}});
        add(new HashMap<>(){{put("JMX URL","url");put("regular",".*");}});
        add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
        add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
        add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
        add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
        add(new HashMap<>(){{put("备注","description");put("regular",".*");}});

    }}),

    APP_RESIN("resin", "Resin应用服务",new ArrayList<>(){{
        add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
        add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
        add(new HashMap<>(){{put("JMX端口","port");put("regular",".*");}});
        add(new HashMap<>(){{put("JMX URL","url");put("regular",".*");}});
        add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
        add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
        add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
        add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
        add(new HashMap<>(){{put("备注","description");put("regular",".*");}});

    }}),

    APP_DB_ACTIVEMQ("activemq", "ActiveMQ消息系统",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("JMX端口","port");put("regular",".*");}});
    add(new HashMap<>(){{put("JMX URL","url");put("regular",".*");}});
    add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
    add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
    add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_ROCKETMQ("rocketmq", "RocketMQ消息系统",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("accessKey","accessKey");put("regular",".*");}});
    add(new HashMap<>(){{put("secretKey","secretKey");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_SHENYU("shenyu", "ShenYu网关",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),

    // 操作系统
    APP_DB_WINDOWS("windows", "Windows",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
        add(new HashMap<>() {{
            put("SNMP版本", "snmpVersion");
            put("regular", "^.+$");
        }});
    add(new HashMap<>(){{put("SNMP 团体字","community");put("regular","^.+$");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_LINUX("linux", "Linux",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("复用连接","reuseConnection");put("regular","^(?=.*[01])[01]+$");}});  //值为0或1
    add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
    add(new HashMap<>(){{put("用户名","username");put("regular","^.+$");}});
    add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_UBUNTU("ubuntu", "Ubuntu Linux",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("复用连接","reuseConnection");put("regular","^.+$");}});
    add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
    add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
    add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_COREOS("coreos", "Fedora CoreOS",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("复用连接","reuseConnection");put("regular","^.+$");}});
    add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
    add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
    add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_OPENSUSE("opensuse", "OpenSUSE",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("复用连接","reuseConnection");put("regular","^.+$");}});
    add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
    add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
    add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_ROCKYLINUX("rockylinux", "Rocky Linux",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("复用连接","reuseConnection");put("regular","^.+$");}});
    add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
    add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
    add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_EULEROS("euleros", "EulerOS操作系统",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("复用连接","reuseConnection");put("regular","^.+$");}});
    add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
    add(new HashMap<>(){{put("用户名","username");put("regular","^.+$");}});
    add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_REDHAT("redhat", "Red Hat",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("复用连接","reuseConnection");put("regular","^.+$");}});
    add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
    add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
    add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_CENTOS("centos", "CentOS Linux",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("复用连接","reuseConnection");put("regular","^.+$");}});
    add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
    add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
    add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_FREEBSD("freebsd", "FreeBSD操作系统",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("复用连接","reuseConnection");put("regular","^.+$");}});
    add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
    add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
    add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_ALMALINUX("almalinux", "AlmaLinux",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("复用连接","reuseConnection");put("regular","^.+$");}});
    add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
    add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
    add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_DB_DEBIAN("debian", "Debian操作系统",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("复用连接","reuseConnection");put("regular","^.+$");}});
    add(new HashMap<>(){{put("账户类型","certificate");put("regular","^.+$");}});
    add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
    add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
     //值为数字

    }}),
    // 容器
    APP_CN_DOCKER("docker", "Docker",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("SSL连接","ssl");put("regular","^(?=.*[01])[01]+$");}});  //值为0或1
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
     //值为数字
}}),
    APP_CN_KUBERNETES("kubernetes", "Kubernetes",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}});
    add(new HashMap<>(){{put("ApiServer端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[1-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("认证Token","token");put("regular","^.+$");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),

    // 网络
    APP_NETWORK_H3C_SWITCH("h3c_switch", "华三交换机",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("SNMP 版本","version");put("regular","^.+$");}});
    add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP username","username");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP contextName","contextName");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP authPassword","authPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("authPassword 加密方式","authPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP privPassphrase","privPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("privPassphrase 加密方式","privPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_NETWORK_CISCO_SWITCH("cisco_switch", "思科交换机",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("SNMP 版本","version");put("regular","^.+$");}});
    add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP username","username");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP contextName","contextName");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP authPassword","authPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("authPassword 加密方式","authPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP privPassphrase","privPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("privPassphrase 加密方式","privPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_NETWORK_HUAWEI_SWITCH("huawei_switch", "华为交换机",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("SNMP 版本","version");put("regular","^.+$");}});
    add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP username","username");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP contextName","contextName");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP authPassword","authPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("authPassword 加密方式","authPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP privPassphrase","privPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("privPassphrase 加密方式","privPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_NETWORK_TPLINK_SWITCH("tplink_switch", "TP-LINK交换机",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("SNMP 版本","version");put("regular","^.+$");}});
    add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP username","username");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP contextName","contextName");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP authPassword","authPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("authPassword 加密方式","authPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP privPassphrase","privPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("privPassphrase 加密方式","privPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_NETWORK_HPE_SWITCH("hpe_switch", "锐捷交换机",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("SNMP 版本","version");put("regular","^.+$");}});
    add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP username","username");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP contextName","contextName");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP authPassword","authPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("authPassword 加密方式","authPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP privPassphrase","privPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("privPassphrase 加密方式","privPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),

    APP_NETWORK_HUAWEI_ROUTER("huawei_router", "华为路由器",new ArrayList<>(){{
        add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
        add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
        add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
        add(new HashMap<>(){{put("SNMP 版本","version");put("regular","^.+$");}});
        add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
        add(new HashMap<>(){{put("SNMP username","username");put("regular",".*");}});
        add(new HashMap<>(){{put("SNMP contextName","contextName");put("regular",".*");}});
        add(new HashMap<>(){{put("SNMP authPassword","authPassphrase");put("regular",".*");}});
        add(new HashMap<>(){{put("authPassword 加密方式","authPasswordEncryption");put("regular",".*");}});
        add(new HashMap<>(){{put("SNMP privPassphrase","privPassphrase");put("regular",".*");}});
        add(new HashMap<>(){{put("privPassphrase 加密方式","privPasswordEncryption");put("regular",".*");}});
        add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
        add(new HashMap<>(){{put("备注","description");put("regular",".*");}});

    }}),

    APP_NETWORK_SWITCH_H3C_WIRELESS("switch_h3c_wireless", "华三无线控制器",new ArrayList<>(){{
        add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
        add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
        add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
        add(new HashMap<>(){{put("SNMP 版本","version");put("regular","^.+$");}});
        add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
        add(new HashMap<>(){{put("SNMP username","username");put("regular",".*");}});
        add(new HashMap<>(){{put("SNMP contextName","contextName");put("regular",".*");}});
        add(new HashMap<>(){{put("SNMP authPassword","authPassphrase");put("regular",".*");}});
        add(new HashMap<>(){{put("authPassword 加密方式","authPasswordEncryption");put("regular",".*");}});
        add(new HashMap<>(){{put("SNMP privPassphrase","privPassphrase");put("regular",".*");}});
        add(new HashMap<>(){{put("privPassphrase 加密方式","privPasswordEncryption");put("regular",".*");}});
        add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
        add(new HashMap<>(){{put("备注","description");put("regular",".*");}});

    }}),

    APP_NETWORK_SWITCH_XINRUI_WIRELESS("switch_xinrui_wireless", "信锐无线控制器",new ArrayList<>(){{
        add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
        add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
        add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
        add(new HashMap<>(){{put("SNMP 版本","version");put("regular","^.+$");}});
        add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
        add(new HashMap<>(){{put("SNMP username","username");put("regular",".*");}});
        add(new HashMap<>(){{put("SNMP contextName","contextName");put("regular",".*");}});
        add(new HashMap<>(){{put("SNMP authPassword","authPassphrase");put("regular",".*");}});
        add(new HashMap<>(){{put("authPassword 加密方式","authPasswordEncryption");put("regular",".*");}});
        add(new HashMap<>(){{put("SNMP privPassphrase","privPassphrase");put("regular",".*");}});
        add(new HashMap<>(){{put("privPassphrase 加密方式","privPasswordEncryption");put("regular",".*");}});
        add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
        add(new HashMap<>(){{put("备注","description");put("regular",".*");}});

    }}),

    APP_NETWORK_CISCO_ROUTER("cisco_router", "思科路由器",new ArrayList<>(){{
        add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
        add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
        add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
        add(new HashMap<>(){{put("SNMP 版本","version");put("regular","^.+$");}});
        add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
        add(new HashMap<>(){{put("SNMP username","username");put("regular",".*");}});
        add(new HashMap<>(){{put("SNMP contextName","contextName");put("regular",".*");}});
        add(new HashMap<>(){{put("SNMP authPassword","authPassphrase");put("regular",".*");}});
        add(new HashMap<>(){{put("authPassword 加密方式","authPasswordEncryption");put("regular",".*");}});
        add(new HashMap<>(){{put("SNMP privPassphrase","privPassphrase");put("regular",".*");}});
        add(new HashMap<>(){{put("privPassphrase 加密方式","privPasswordEncryption");put("regular",".*");}});
        add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
        add(new HashMap<>(){{put("备注","description");put("regular",".*");}});

    }}),

    // 普罗米修斯
    APP_PROMETHEUS_PH("prometheus","Prometheus",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("端点路径","path");put("regular","^.+$");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    // 安全
    APP_FIREWALL_AH_FIREWALL("ah_firewall", "安恒防火墙",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("SNMP版本","version");put("regular","^.+$");}});
    add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP username","username");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP contextName","contextName");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP authPassword","authPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("authPassword 加密方式","authPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP privPassphrase","privPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("privPassphrase 加密方式","privPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_FIREWALL_QAX_FIREWALL("qax_firewall", "奇安信防火墙",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("SNMP版本","version");put("regular","^.+$");}});
    add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP username","username");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP contextName","contextName");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP authPassword","authPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("authPassword 加密方式","authPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP privPassphrase","privPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("privPassphrase 加密方式","privPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_FIREWALL_SSWK_FIREWALL("sswk_firewall", "山石网科防火墙",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("SNMP版本","version");put("regular","^.+$");}});
    add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP username","username");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP contextName","contextName");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP authPassword","authPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("authPassword 加密方式","authPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP privPassphrase","privPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("privPassphrase 加密方式","privPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_FIREWALL_SXF_FIREWALL("sxf_firewall", "深信服防火墙",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("SNMP版本","version");put("regular","^.+$");}});
    add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP username","username");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP contextName","contextName");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP authPassword","authPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("authPassword 加密方式","authPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP privPassphrase","privPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("privPassphrase 加密方式","privPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_FIREWALL_SXF_AD_FIREWALL("ad_firewall", "深信服AD设备",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("SNMP版本","version");put("regular","^.+$");}});
    add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP username","username");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP contextName","contextName");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP authPassword","authPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("authPassword 加密方式","authPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP privPassphrase","privPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("privPassphrase 加密方式","privPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_FIREWALL_SXF_AC_FIREWALL("ac_firewall", "深信服AC设备",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("SNMP版本","version");put("regular","^.+$");}});
    add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP username","username");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP contextName","contextName");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP authPassword","authPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("authPassword 加密方式","authPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP privPassphrase","privPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("privPassphrase 加密方式","privPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_FIREWALL_TRX_FIREWALL("trx_firewall", "天融信防火墙",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("SNMP版本","version");put("regular","^.+$");}});
    add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP username","username");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP contextName","contextName");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP authPassword","authPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("authPassword 加密方式","authPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP privPassphrase","privPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("privPassphrase 加密方式","privPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_FIREWALL_AH_WAF("ah_waf", "安恒WAF",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("SNMP版本","version");put("regular","^.+$");}});
    add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP username","username");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP contextName","contextName");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP authPassword","authPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("authPassword 加密方式","authPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP privPassphrase","privPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("privPassphrase 加密方式","privPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_FIREWALL_LM_WAF("lm_waf", "绿盟WAF",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("SNMP版本","version");put("regular","^.+$");}});
    add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP username","username");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP contextName","contextName");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP authPassword","authPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("authPassword 加密方式","authPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP privPassphrase","privPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("privPassphrase 加密方式","privPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_FIREWALL_LM_DEFENSE("lm_defense", "绿盟入侵防御",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("SNMP版本","version");put("regular","^.+$");}});
    add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP username","username");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP contextName","contextName");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP authPassword","authPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("authPassword 加密方式","authPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP privPassphrase","privPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("privPassphrase 加密方式","privPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_FIREWALL_SSWK_DEFENSE("sswk_defense", "山石网科入侵防御",new ArrayList<>(){{
    add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
    add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
    add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
    add(new HashMap<>(){{put("SNMP版本","version");put("regular","^.+$");}});
    add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP username","username");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP contextName","contextName");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP authPassword","authPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("authPassword 加密方式","authPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("SNMP privPassphrase","privPassphrase");put("regular",".*");}});
    add(new HashMap<>(){{put("privPassphrase 加密方式","privPasswordEncryption");put("regular",".*");}});
    add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    
}}),
    APP_FIREWALL_TRX_DEFENSE("trx_defense", "天融信入侵防御",new ArrayList<>(){{
        add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
        add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
        add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
        add(new HashMap<>(){{put("SNMP版本","version");put("regular","^.+$");}});
        add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
        add(new HashMap<>(){{put("SNMP username","username");put("regular",".*");}});
        add(new HashMap<>(){{put("SNMP contextName","contextName");put("regular",".*");}});
        add(new HashMap<>(){{put("SNMP authPassword","authPassphrase");put("regular",".*");}});
        add(new HashMap<>(){{put("authPassword 加密方式","authPasswordEncryption");put("regular",".*");}});
        add(new HashMap<>(){{put("SNMP privPassphrase","privPassphrase");put("regular",".*");}});
        add(new HashMap<>(){{put("privPassphrase 加密方式","privPasswordEncryption");put("regular",".*");}});
        add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
    add(new HashMap<>(){{put("备注","description");put("regular",".*");}});

    }}),

    APP_FIREWALL_QMXC_FIREWALL("qmxc_firewall", "启明星辰防火墙",new ArrayList<>(){{
        add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
        add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
        add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
        add(new HashMap<>(){{put("SNMP版本","version");put("regular","^.+$");}});
        add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
        add(new HashMap<>(){{put("SNMP username","username");put("regular",".*");}});
        add(new HashMap<>(){{put("SNMP contextName","contextName");put("regular",".*");}});
        add(new HashMap<>(){{put("SNMP authPassword","authPassphrase");put("regular",".*");}});
        add(new HashMap<>(){{put("authPassword 加密方式","authPasswordEncryption");put("regular",".*");}});
        add(new HashMap<>(){{put("SNMP privPassphrase","privPassphrase");put("regular",".*");}});
        add(new HashMap<>(){{put("privPassphrase 加密方式","privPasswordEncryption");put("regular",".*");}});
        add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
        add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    }}),

    APP_STORAGE_HUAWEI("storage_huawei", "华为存储",new ArrayList<>(){{
        add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
        add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
        add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
        add(new HashMap<>(){{put("SNMP版本","version");put("regular","^.+$");}});
        add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
        add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
        add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    }}),

    APP_STORAGE_INSPUR("storage_inspur", "浪潮存储",new ArrayList<>(){{
        add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
        add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
        add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
        add(new HashMap<>(){{put("SNMP版本","version");put("regular","^.+$");}});
        add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
        add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
        add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    }}),

    APP_STORAGE_SYNOLOGY("storage_synology", "群晖存储",new ArrayList<>(){{
        add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
        add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
        add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
        add(new HashMap<>(){{put("SNMP版本","version");put("regular","^.+$");}});
        add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
        add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
        add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    }}),

    APP_STORAGE_3PAR("storage_3par", "3PAR存储",new ArrayList<>(){{
        add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
        add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
        add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
        add(new HashMap<>(){{put("SNMP版本","version");put("regular","^.+$");}});
        add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
        add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
        add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    }}),

    APP_STORAGE_NETAPP("storage_netapp", "NetApp存储",new ArrayList<>(){{
        add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
        add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
        add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
        add(new HashMap<>(){{put("SNMP版本","version");put("regular","^.+$");}});
        add(new HashMap<>(){{put("SNMP 团体字","community");put("regular",".*");}});
        add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
        add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    }}),



    APP_INFRA_IPMI("infra_ipmi", "IPMI带外管理",new ArrayList<>(){{
        add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
        add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
        add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
        add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
        add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
        add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
        add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    }}),

    APP_INFRA_NVIDIA("infra_nvidia", "NVIDIA显卡监控",new ArrayList<>(){{
        add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
        add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
        add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
        add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
        add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
        add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
        add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    }}),

    APP_INFRA_REDFISH("infra_redfish", "Redfish监控",new ArrayList<>(){{
        add(new HashMap<>(){{put("目标Host","host");put("regular","^.+$");}});
        add(new HashMap<>(){{put("监控名称","name");put("regular","^.+$");}}); //不为空且不能为中文
        add(new HashMap<>(){{put("端口","port");put("regular","^(6553[0-5]|655[0-2]\\d|65[0-4]\\d\\d|6[0-4]\\d{3}|[1-5]\\d{4}|[0-9]\\d{0,3})$");}});
        add(new HashMap<>(){{put("用户名","username");put("regular",".*");}});
        add(new HashMap<>(){{put("密码","password");put("regular",".*");}});
        add(new HashMap<>(){{put("设备平台","platFormName");put("regular","^.+$");}});
        add(new HashMap<>(){{put("备注","description");put("regular",".*");}});
    }});






    /**
     * 角色编码
     */
    private final String value;
    /**
     * 名字
     */
    private final String label;

    private final List<HashMap<String,String>> fieldList;
}
