package cn.iocoder.cloud.module.cloudedge.scheduler.netty.process;

import cn.iocoder.cloud.framework.remoting.netty.NettyRemotingProcessor;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.message.ClusterMsg;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.apache.hertzbeat.common.queue.CommonDataQueue;
import org.apache.hertzbeat.common.support.SpringContextHolder;
import org.apache.hertzbeat.common.util.ProtoJsonUtil;

import java.util.List;

/**
 * handle cyclic data response message
 */
@Slf4j
public class CollectCyclicDataResponseProcessor implements NettyRemotingProcessor {
    @Override
    public ClusterMsg.Message handle(ChannelHandlerContext ctx, ClusterMsg.Message message) {
        CommonDataQueue dataQueue = SpringContextHolder.getBean(CommonDataQueue.class);
        CollectRep.MetricsData metricsData = (CollectRep.MetricsData) ProtoJsonUtil.toProtobuf(message.getMsg(),
                CollectRep.MetricsData.newBuilder());
        if (metricsData != null) {
            dataQueue.sendMetricsData(metricsData);
        }
        return null;
    }
}
