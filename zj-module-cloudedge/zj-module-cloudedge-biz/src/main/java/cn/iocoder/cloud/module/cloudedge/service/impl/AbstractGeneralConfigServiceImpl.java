package cn.iocoder.cloud.module.cloudedge.service.impl;


import cn.iocoder.cloud.module.cloudedge.dal.mysql.dao.GeneralConfigDao;
import cn.iocoder.cloud.module.cloudedge.service.GeneralConfigService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.manager.GeneralConfig;
import org.springframework.transaction.annotation.Transactional;


/**
 * 提供通用配置Service的抽象实现，实现了增删查改等操作。
 *
 * <p>Abstract implementation of GeneralConfigService, providing CRUD operations for configurations.</p>
 *
 * <AUTHOR>
 */
@Slf4j
abstract class AbstractGeneralConfigServiceImpl<T> implements GeneralConfigService<T> {


    protected final GeneralConfigDao generalConfigDao;

    protected final ObjectMapper objectMapper;

    /**
     * <p>Constructor, passing in GeneralConfigDao, ObjectMapper and type.</p>
     * @param generalConfigDao Dao object
     * @param objectMapper     JSON tool object
     */
    protected AbstractGeneralConfigServiceImpl(GeneralConfigDao generalConfigDao, ObjectMapper objectMapper) {
        this.generalConfigDao = generalConfigDao;
        this.objectMapper = objectMapper;
    }

    /**
     * <p>Save a configuration.</p>
     * @param config need to save configuration object
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveConfig(T config) {
        try {
            String contentJson = objectMapper.writeValueAsString(config);

            GeneralConfig generalConfig2Save = GeneralConfig.builder()
                    .type(type())
                    .content(contentJson)
                    .build();
            generalConfigDao.save(generalConfig2Save);
            log.info("Configuration saved successfully");
            handler(getConfig());
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException("Configuration saved failed: " + e.getMessage());
        }
    }

    /**
     * <p>Get a configuration.</p>
     * @return query configuration object
     */
    @Override
    public T getConfig() {
        GeneralConfig generalConfig = generalConfigDao.findByType(type());
        if (generalConfig == null) {
            return null;
        }
        try {
            return objectMapper.readValue(generalConfig.getContent(), getTypeReference());
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException("Get configuration failed: " + e.getMessage());
        }
    }

    /**
     * <p>Get TypeReference object of configuration type.</p>
     * @return TypeReference object
     */
    protected abstract TypeReference<T> getTypeReference();


}
