package cn.iocoder.cloud.module.cloudedge.controller.admin.alertdefinetemplate.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 多维告警模板创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AlertDefineTemplateCreateReqVO extends AlertDefineTemplateBaseVO {

}
