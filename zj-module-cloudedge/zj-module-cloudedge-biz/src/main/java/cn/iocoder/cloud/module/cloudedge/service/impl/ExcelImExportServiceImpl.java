package cn.iocoder.cloud.module.cloudedge.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.hertzbeat.common.util.export.ExcelExportUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.apache.hertzbeat.common.constants.ExportFileConstants.ExcelFile.FILE_SUFFIX;
import static org.apache.hertzbeat.common.constants.ExportFileConstants.ExcelFile.TYPE;

/**
 * Configure the import and export EXCEL format
 * 配置导入导出 EXCEL格式
 *
 * <AUTHOR> href="mailto:<EMAIL>">zqr10159</a>
 * Created by zqr10159 on 2023/4/11
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ExcelImExportServiceImpl extends AbstractImExportServiceImpl {
    /**
     * Export file type
     * @return file type
     */
    @Override
    public String type() {
        return TYPE;
    }

    /**
     * Get Export File Name
     * @return file name
     */
    @Override
    public String getFileName() {
        return fileNamePrefix() + FILE_SUFFIX;
    }

    /**
     * Parsing an input stream into a form
     * @param is input stream
     * @return form
     */
    @Override
    public List<ExportMonitorDTO> parseImport(InputStream is) {
        try (Workbook workbook = WorkbookFactory.create(is)) {
            Sheet sheet = workbook.getSheetAt(0);

            List<ExportMonitorDTO> monitors = new ArrayList<>();
            List<Integer> startRowList = new ArrayList<>();

            for (Row row : sheet) {
                if (row.getRowNum() == 0) {
                    continue;
                }
                String name = getCellValueAsString(row.getCell(0));
                if (StringUtils.isNotBlank(name)) {
                    startRowList.add(row.getRowNum());
                    MonitorDTO monitor = extractMonitorDataFromRow(row);
                    ExportMonitorDTO exportMonitor = new ExportMonitorDTO();
                    exportMonitor.setMonitor(monitor);
                    monitors.add(exportMonitor);
                    String metrics = getCellValueAsString(row.getCell(11));
                    if (StringUtils.isNotBlank(metrics)) {
                        List<String> metricList = Arrays.stream(metrics.split(",")).collect(Collectors.toList());
                        exportMonitor.setMetrics(metricList);
                    }
                }
            }

            List<List<ParamDTO>> paramsList = new ArrayList<>();

            for (int i = 0; i < startRowList.size(); i++) {
                int startRowIndex = startRowList.get(i);
                int endRowIndex = (i + 1 < startRowList.size()) ? startRowList.get(i + 1) : sheet.getLastRowNum() + 1;
                List<ParamDTO> params = new ArrayList<>();

                for (int j = startRowIndex; j < endRowIndex; j++) {
                    Row row = sheet.getRow(j);
                    if (row == null) {
                        continue;
                    }
                    ParamDTO param = extractParamDataFromRow(row);
                    if (param != null) {
                        params.add(param);
                    }
                }

                paramsList.add(params);
            }
            for (int i = 0; i < monitors.size(); i++) {
                monitors.get(i).setParams(paramsList.get(i));
            }
            return monitors;
        } catch (IOException e) {
            throw new RuntimeException("Failed to parse monitor data", e);
        }
    }

    private MonitorDTO extractMonitorDataFromRow(Row row) {
        MonitorDTO monitor = new MonitorDTO();

        monitor.setName(getCellValueAsString(row.getCell(0)));
        monitor.setApp(getCellValueAsString(row.getCell(1)));
        monitor.setHost(getCellValueAsString(row.getCell(2)));
        monitor.setIntervals(getCellValueAsInteger(row.getCell(3)));
        monitor.setStatus(getCellValueAsByte(row.getCell(4)));
        monitor.setDescription(getCellValueAsString(row.getCell(5)));

        String tagsString = getCellValueAsString(row.getCell(6));
        if (StringUtils.isNotBlank(tagsString)) {
            List<Long> tags = Arrays.stream(tagsString.split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            monitor.setTags(tags);
        }
        monitor.setCollector(getCellValueAsString(row.getCell(7)));


        return monitor;
    }

    private ParamDTO extractParamDataFromRow(Row row) {
        String fieldName = getCellValueAsString(row.getCell(8));
        if (StringUtils.isNotBlank(fieldName)) {
            ParamDTO param = new ParamDTO();
            param.setField(fieldName);
            param.setType(getCellValueAsByte(row.getCell(9)));
            param.setValue(getCellValueAsString(row.getCell(10)));
            return param;
        }
        return null;
    }

    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return null;
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return String.valueOf(cell.getNumericCellValue());
            default:
                return null;
        }
    }

    private boolean getCellValueAsBoolean(Cell cell) {
        if (cell == null) {
            return false;
        }
        if (Objects.requireNonNull(cell.getCellType()) == CellType.BOOLEAN) {
            return cell.getBooleanCellValue();
        }
        return false;
    }

    private Integer getCellValueAsInteger(Cell cell) {
        if (cell == null) {
            return null;
        }
        if (Objects.requireNonNull(cell.getCellType()) == CellType.NUMERIC) {
            return (int) cell.getNumericCellValue();
        }
        return null;
    }

    private Byte getCellValueAsByte(Cell cell) {
        if (cell == null) {
            return null;
        }
        if (Objects.requireNonNull(cell.getCellType()) == CellType.NUMERIC) {
            return (byte) cell.getNumericCellValue();
        }
        return null;
    }

    /**
     * Export Configuration to Output Stream
     * @param monitorList config list
     * @param os          output stream
     */
    @Override
    public void writeOs(List<ExportMonitorDTO> monitorList, OutputStream os) {
        try {

            Workbook workbook = WorkbookFactory.create(true);
            String sheetName = "Export Monitor";
            Sheet sheet = ExcelExportUtils.setSheet(sheetName, workbook, ExportMonitorDTO.class);
            // set cell style
            CellStyle cellStyle = ExcelExportUtils.setCellStyle(workbook);

            // foreach monitor, each monitor object corresponds to a row of data
            int rowIndex = 1;
            for (ExportMonitorDTO monitor : monitorList) {
                // get monitor information
                MonitorDTO monitorDTO = monitor.getMonitor();
                // get monitor parameters
                List<ParamDTO> paramList = monitor.getParams();
                // get monitor metrics
                List<String> metricList = monitor.getMetrics();
                // merge monitor information and parameter information into one row
                for (int i = 0; i < Math.max(paramList.size(), 1); i++) {
                    Row row = sheet.createRow(rowIndex++);
                    if (i == 0) {
                        // You need to fill in the monitoring information only once
                        Cell nameCell = row.createCell(0);
                        nameCell.setCellValue(monitorDTO.getName());
                        nameCell.setCellStyle(cellStyle);
                        Cell appCell = row.createCell(1);
                        appCell.setCellValue(monitorDTO.getApp());
                        appCell.setCellStyle(cellStyle);
                        Cell hostCell = row.createCell(2);
                        hostCell.setCellValue(monitorDTO.getHost());
                        hostCell.setCellStyle(cellStyle);
                        Cell intervalsCell = row.createCell(3);
                        intervalsCell.setCellValue(monitorDTO.getIntervals());
                        intervalsCell.setCellStyle(cellStyle);
                        Cell statusCell = row.createCell(4);
                        statusCell.setCellValue(monitorDTO.getStatus());
                        statusCell.setCellStyle(cellStyle);
                        Cell descriptionCell = row.createCell(5);
                        descriptionCell.setCellValue(monitorDTO.getDescription());
                        descriptionCell.setCellStyle(cellStyle);
                        Cell tagsCell = row.createCell(6);
                        tagsCell.setCellValue(monitorDTO.getTags().stream().map(Object::toString).collect(Collectors.joining(",")));
                        tagsCell.setCellStyle(cellStyle);
                        Cell collectorCell = row.createCell(7);
                        collectorCell.setCellValue(monitorDTO.getCollector());
                        collectorCell.setCellStyle(cellStyle);
                        if (metricList != null && i < metricList.size()) {
                            Cell metricCell = row.createCell(11);
                            metricCell.setCellValue(String.join(",", metricList));
                            metricCell.setCellStyle(cellStyle);
                        }
                    }
                    // Fill in parameter information
                    if (i < paramList.size()) {
                        ParamDTO paramDTO = paramList.get(i);
                        Cell fieldCell = row.createCell(8);
                        fieldCell.setCellValue(paramDTO.getField());
                        fieldCell.setCellStyle(cellStyle);
                        Cell typeCell = row.createCell(9);
                        typeCell.setCellValue(paramDTO.getType());
                        typeCell.setCellStyle(cellStyle);
                        Cell valueCell = row.createCell(10);
                        valueCell.setCellValue(paramDTO.getValue());
                        valueCell.setCellStyle(cellStyle);
                    }
                }
                if (CollectionUtils.isNotEmpty(paramList)) {
                    RegionUtil.setBorderTop(BorderStyle.THICK, new CellRangeAddress(rowIndex - paramList.size(), rowIndex - 1, 0, 10), sheet);
                    RegionUtil.setBorderBottom(BorderStyle.THICK, new CellRangeAddress(rowIndex - paramList.size(), rowIndex - 1, 0, 10), sheet);
                    RegionUtil.setBorderLeft(BorderStyle.THICK, new CellRangeAddress(rowIndex - paramList.size(), rowIndex - 1, 0, 10), sheet);
                    RegionUtil.setBorderRight(BorderStyle.THICK, new CellRangeAddress(rowIndex - paramList.size(), rowIndex - 1, 0, 10), sheet);
                }
            }
            workbook.write(os);
            os.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
