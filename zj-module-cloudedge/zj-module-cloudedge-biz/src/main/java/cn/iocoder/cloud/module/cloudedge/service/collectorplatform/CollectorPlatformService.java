package cn.iocoder.cloud.module.cloudedge.service.collectorplatform;

import cn.iocoder.cloud.module.cloudedge.controller.admin.collectorplatform.vo.CollectorPlatformCreateReqVO;
import cn.iocoder.cloud.module.cloudedge.controller.admin.collectorplatform.vo.CollectorPlatformExportReqVO;
import cn.iocoder.cloud.module.cloudedge.controller.admin.collectorplatform.vo.CollectorPlatformPageReqVO;
import cn.iocoder.cloud.module.cloudedge.controller.admin.collectorplatform.vo.CollectorPlatformUpdateReqVO;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.collectorplatform.CollectPlatfDTO;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.collectorplatform.CollectorPlatformDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import org.apache.hertzbeat.common.entity.manager.Collector;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 采集器平台关联 Service 接口
 *
 * <AUTHOR>
 */

public interface CollectorPlatformService {

    /**
     * 创建采集器平台关联
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCollectorPlatform(@Valid CollectorPlatformCreateReqVO createReqVO);

    /**
     * 更新采集器平台关联
     *
     * @param updateReqVO 更新信息
     */
    void updateCollectorPlatform(@Valid CollectorPlatformUpdateReqVO updateReqVO);

    /**
     * 删除采集器平台关联
     *
     * @param id 编号
     */
    void deleteCollectorPlatform(Long id);

    /**
     * 获得采集器平台关联
     *
     * @param id 编号
     * @return 采集器平台关联
     */
    CollectorPlatformDO getCollectorPlatform(Long id);

    /**
     * 获得采集器平台关联列表
     *
     * @param ids 编号
     * @return 采集器平台关联列表
     */
    List<CollectorPlatformDO> getCollectorPlatformList(Collection<Long> ids);

    /**
     * 获得采集器平台关联分页
     *
     * @param pageReqVO 分页查询
     * @return 采集器平台关联分页
     */
    PageResult<CollectorPlatformDO> getCollectorPlatformPage(CollectorPlatformPageReqVO pageReqVO);

    /**
     * 获得采集器平台关联列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 采集器平台关联列表
     */
    List<CollectorPlatformDO> getCollectorPlatformList(CollectorPlatformExportReqVO exportReqVO);

    void bindCollector(Collector collector1, Long platformId, String platformName);


    List<CollectorPlatformDO> getCollectorPlatformList();

    void deleteCollectorPlatformByName(String collector);

    void bindProjectId(Collector collector1, Long projectId, String projectName);

    List<CollectorPlatformDO> getCollectorPlatformByIdList(Long platformId);

    List<CollectPlatfDTO> getPlatforms(List<Long> sourceIds);
}
