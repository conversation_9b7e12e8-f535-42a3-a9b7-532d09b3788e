/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.module.cloudedge.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.cloud.framework.alert.dal.redis.HerzbeatMonitorRedisDAO;
import cn.iocoder.cloud.framework.alert.dao.AlertDefineBindDao;
import cn.iocoder.cloud.framework.collector.dispatch.DispatchConstants;
import cn.iocoder.cloud.framework.warehouse.service.MetricsDataService;
import cn.iocoder.cloud.framework.warehouse.service.WarehouseService;
import cn.iocoder.cloud.framework.warehouse.store.realtime.RealTimeDataReader;
import cn.iocoder.cloud.module.cloudedge.adapter.MonitorType;
import cn.iocoder.cloud.module.cloudedge.adapter.MonitorsAdapterService;
import cn.iocoder.cloud.module.cloudedge.api.monitor.dto.AlarmReqDTO;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.category.CategoryDTO;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.category.SubCategoryDTO;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.collectorplatform.CollectorPlatformDO;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.herzbeat.AppCount;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.herzbeat.Hierarchy;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.herzbeat.MonitorDto;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.herzbeat.MonitorTopDto;
import cn.iocoder.cloud.module.cloudedge.dal.mysql.collectorplatform.CollectorPlatformMapper;
import cn.iocoder.cloud.module.cloudedge.dal.mysql.dao.*;
import cn.iocoder.cloud.module.cloudedge.framework.enums.ResourceConfigEnum;
import cn.iocoder.cloud.module.cloudedge.scheduler.CollectJobScheduling;
import cn.iocoder.cloud.module.cloudedge.service.*;
import cn.iocoder.zj.framework.common.constants.FileTypeConstants;
import cn.iocoder.zj.framework.common.enums.ResourceAppEnum;
import cn.iocoder.zj.framework.common.enums.ResourceCategoryEnum;
import cn.iocoder.zj.framework.common.exception.ErrorCode;
import cn.iocoder.zj.framework.common.exception.MonitorDatabaseException;
import cn.iocoder.zj.framework.common.exception.MonitorDetectException;
import cn.iocoder.zj.framework.common.exception.MonitorMetricsException;
import cn.iocoder.zj.framework.common.pojo.VictoriaMetricsDTO;
import cn.iocoder.zj.framework.common.util.category.ResourceCategoryUtil;
import cn.iocoder.zj.framework.operatelog.core.aop.OperateLogAspect;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.warehouse.service.ZjMetricsDataService;
import cn.iocoder.zj.module.infra.api.file.FileApi;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import cn.iocoder.zj.module.monitor.api.topology.TopologyApi;
import cn.iocoder.zj.module.monitor.api.topology.dto.TopologyDTO;
import cn.iocoder.zj.module.om.api.asset.MonitorassetApi;
import cn.iocoder.zj.module.om.api.asset.dto.MonitorAssetDTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.usercertificate.UsercertificateApi;
import cn.iocoder.zj.module.system.api.usercertificate.dto.UserCertificateRespDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.constants.CommonConstants;
import org.apache.hertzbeat.common.constants.ExportFileConstants;
import org.apache.hertzbeat.common.constants.NetworkConstants;
import org.apache.hertzbeat.common.constants.SignConstants;
import org.apache.hertzbeat.common.entity.dto.MetricsHistoryData;
import org.apache.hertzbeat.common.entity.grafana.GrafanaDashboard;
import org.apache.hertzbeat.common.entity.job.Configmap;
import org.apache.hertzbeat.common.entity.job.Job;
import org.apache.hertzbeat.common.entity.job.Metrics;
import org.apache.hertzbeat.common.entity.job.protocol.CommonRequestProtocol;
import org.apache.hertzbeat.common.entity.manager.*;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.apache.hertzbeat.common.support.SpringContextHolder;
import org.apache.hertzbeat.common.support.event.MonitorDeletedEvent;
import org.apache.hertzbeat.common.util.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriUtils;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.system.enums.ErrorCodeConstants.TEMPLATE_EXISTS;

/**
 * 监控管理服务实现
 *
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class MonitorServiceImpl implements MonitorService {
    private static final Long MONITOR_ID_TMP = 1000000000L;

    public static final String PATTERN_HTTP = "(?i)http://";
    public static final String PATTERN_HTTPS = "(?i)https://";

    private static final byte ALL_MONITOR_STATUS = 9;

    private static final int TAG_LENGTH = 2;


    private static final String CONTENT_VALUE = MediaType.APPLICATION_OCTET_STREAM_VALUE + SignConstants.SINGLE_MARK + "charset=" + StandardCharsets.UTF_8;


    @Autowired
    private AppService appService;

    @Autowired
    private TagService tagService;

    @Autowired
    private CollectJobScheduling collectJobScheduling;

    @Autowired
    private MonitorDao monitorDao;

    @Autowired
    private ParamDao paramDao;

    @Autowired
    private CollectorDao collectorDao;

    @Autowired
    private CollectorMonitorBindDao collectorMonitorBindDao;

    @Autowired
    private AlertDefineBindDao alertDefineBindDao;

    @Autowired
    private TagMonitorBindDao tagMonitorBindDao;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private WarehouseService warehouseService;

    @Autowired
    private HerzbeatMonitorRedisDAO herzbeatMonitorRedisDAO;
    @Resource
    CollectorPlatformMapper collectorPlatformMapper;

    @Autowired
    private UsercertificateApi usercertificateApi;
    @Autowired
    private AlarmConfigApi alarmConfigApi;
    @Autowired
    private TopologyApi topologyApi;
    @Autowired
    private MonitorassetApi monitorassetApi;
    @Resource
    private FileApi fileApi;
    //    @Autowired
//    private DashboardService dashboardService;
    @Autowired
    private MonitorBindDao monitorBindDao;

    @Autowired
    private MonitorsAdapterService monitorsAdapterService;

    @Autowired
    private RealTimeDataReader realTimeDataReader;

    @Autowired
    private BulletinService bulletinService;

    @Autowired
    private PlatformconfigApi platformconfigApi;

    @Autowired
    private BulletinDao bulletinDao;



    private final Map<String, ImExportService> imExportServiceMap = new HashMap<>();

    public MonitorServiceImpl(List<ImExportService> imExportServiceList) {
        imExportServiceList.forEach(it -> imExportServiceMap.put(it.type(), it));
    }

    @Override
    @Transactional(readOnly = true)
    public void detectMonitor(Monitor monitor, List<Param> params, String collector) throws MonitorDetectException {

        final Optional<Param> sdParam = SdMonitorOperator.getSdParam(params);

        if (sdParam.isPresent()) {
            collectOneTimeSdData(monitor, collector, sdParam.get());
        } else {
            detectMonitorDirectly(monitor, params, collector);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addMonitor(Monitor monitor, List<Param> params, String collector, GrafanaDashboard grafanaDashboard) throws RuntimeException {
        final Optional<Param> sdParam = SdMonitorOperator.getSdParam(params);
        if (sdParam.isPresent()) {
            addAndSaveMonitorJob(monitor.clone(), params, collector, SdMonitorParam.builder().sdParam(sdParam.get()).build(), grafanaDashboard);
            return;
        }
        addAndSaveMonitorJob(monitor, params, collector, null, grafanaDashboard);
        //根据 app 查询 Bulletin信息
        Bulletin bulletin = bulletinService.getBulletinByApp(monitor.getApp());
        if (bulletin != null) {
            List<Long> monitorIds = bulletin.getMonitorIds();
            // 创建新的可变列表
            List<Long> newMonitorIds = new ArrayList<>();
            if (monitorIds != null) {
                newMonitorIds.addAll(monitorIds);
            }
            newMonitorIds.add(monitor.getId());
            bulletin.setMonitorIds(newMonitorIds);
            //修改 Bulletin monitorIds的信息
            bulletinService.editBulletin(bulletin);
        }
    }

    @Override
    public void addAndSaveMonitorJob(Monitor monitor, List<Param> params, String collector, SdMonitorParam sdMonitorParam, GrafanaDashboard grafanaDashboard) {
        // Apply for monitor id
        long monitorId = SnowFlakeIdGenerator.generateId();
        // Init Set Default Tags: monitorId monitorName app
        List<Tag> tags = monitor.getTags();
        if (tags == null) {
            tags = new LinkedList<>();
            monitor.setTags(tags);
        }
        MonitorBind monitorBind = null;

        // Construct the collection task Job entity
        Job appDefine = appService.getAppDefine(monitor.getApp());
        if (Objects.nonNull(sdMonitorParam)) {
            monitorBind = Objects.isNull(sdMonitorParam.getSdParam())
                    ? SdMonitorOperator.buildSdSubMonitorBind(sdMonitorParam, monitorId, tags)
                    : SdMonitorOperator.buildSdMainMonitorBind(sdMonitorParam, monitorId);

            appDefine = SdMonitorOperator.constructSdJobAndTag(sdMonitorParam, tags, appDefine);
        }
        if (CommonConstants.PROMETHEUS.equals(monitor.getApp())) {
            appDefine.setApp(CommonConstants.PROMETHEUS_APP_PREFIX + monitor.getName());
        }
        appDefine.setMonitorId(monitorId);
        appDefine.setDefaultInterval(monitor.getIntervals());
        appDefine.setCyclic(true);
        appDefine.setTimestamp(System.currentTimeMillis());
        resetMetricsCommonField(monitor, appDefine, sdMonitorParam);
        tags.add(Tag.builder().name(CommonConstants.TAG_MONITOR_ID).value(String.valueOf(monitorId)).type(CommonConstants.TAG_TYPE_AUTO_GENERATE).build());
        tags.add(Tag.builder().name(CommonConstants.TAG_MONITOR_NAME).value(monitor.getName()).type(CommonConstants.TAG_TYPE_AUTO_GENERATE).build());

        List<Configmap> configmaps = params.stream().map(param -> {
            param.setMonitorId(monitorId);
            return new Configmap(param.getField(), param.getValue(), param.getType());
        }).collect(Collectors.toList());
        appDefine.setConfigmap(configmaps);

        long jobId = collector == null ? collectJobScheduling.addAsyncCollectJob(appDefine, null) :
                collectJobScheduling.addAsyncCollectJob(appDefine, collector);

        try {
            detectMonitor(monitor, params, collector);
        } catch (Exception ignored) {
        }

        try {
            if (collector != null) {
                CollectorMonitorBind collectorMonitorBind = CollectorMonitorBind.builder()
                        .collector(collector)
                        .monitorId(monitorId)
                        .build();
                collectorMonitorBindDao.save(collectorMonitorBind);
            }
            // 判断是否包含id 如果包含id 说明是选择过密码类型的
            boolean containsCeryofocateId = configmaps.stream().anyMatch(p -> "ceryofocateId".equals(p.getKey()));
            if (containsCeryofocateId) {
                for (Configmap configmap : configmaps) {
                    if ("ceryofocateId".equals(configmap.getKey())) {
                        Long certificateId = Convert.toLong(configmap.getValue());
                        monitor.setCertificateId(certificateId);
                        break;
                    }
                }
            }
            monitor.setId(monitorId);
            monitor.setJobId(jobId);
            monitor.setStatus(CommonConstants.MONITOR_UP_CODE);
            herzbeatMonitorRedisDAO.set("herzbeat_monitor:" + monitorId, monitor);
            // create grafana dashboard
            if (monitor.getApp().equals(CommonConstants.PROMETHEUS) && grafanaDashboard != null && grafanaDashboard.isEnabled()) {
//                dashboardService.createOrUpdateDashboard(grafanaDashboard.getTemplate(), monitorId);
            }
            monitorDao.save(monitor);
            if (Objects.nonNull(monitorBind)) {
                monitorBindDao.save(monitorBind);
            }
            paramDao.saveAll(params);
        } catch (Exception e) {
            log.error("Error while adding monitor: {}", e.getMessage(), e);
            collectJobScheduling.cancelAsyncCollectJob(jobId);
            throw new MonitorDatabaseException(e.getMessage());
        }
    }

    @Override
    public void updateMonitor(Long id, String name) {
        monitorDao.updateMonitor(id, name);
        List<Monitor> list = monitorDao.findByPlatformId(id);
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(x -> {
                String key = "herzbeat_monitor:" + x.getId();
                if (herzbeatMonitorRedisDAO.get(key) != null) {
                    herzbeatMonitorRedisDAO.set(key, x);
                }
            });
        }
    }

    @Override
    public Map<String, List<Map<String, Object>>> getTop10MonitorList(Long monitorId, String app, String category) {
        Map<String, List<Map<String, Object>>> top10MetricsData = new HashMap<>();
        switch (category) {
            case "network" -> {
                CollectRep.MetricsData currentMetricsData = realTimeDataReader.getCurrentMetricsData(monitorId, MonitorType.LINUX_INTERFACE.getType());
                //interface 取 出流量、入流量、入丢包数，出丢包数的TOP10数据
                top10MetricsData = monitorsAdapterService.handleTop10MonitorMetricsData(MonitorType.LINUX_INTERFACE.getType(), currentMetricsData);
            }
            case "stored" -> {
                CollectRep.MetricsData currentMetricsData = realTimeDataReader.getCurrentMetricsData(monitorId, MonitorType.STORAGES.getType());
                top10MetricsData = monitorsAdapterService.handleTop10MonitorMetricsData(MonitorType.STORAGES.getType(), currentMetricsData);
            }
            case "db" -> {
                if (app.equals("tidb")) {
                    CollectRep.MetricsData currentMetricsData = realTimeDataReader.getCurrentMetricsData(monitorId, MonitorType.STORES.getType());
                    top10MetricsData = monitorsAdapterService.handleTop10MonitorMetricsData(MonitorType.STORES.getType(), currentMetricsData);
                }
            }
            case "os" -> {
                CollectRep.MetricsData currentMetricsData = realTimeDataReader.getCurrentMetricsData(monitorId, MonitorType.PROCESSES.getType());
                if (currentMetricsData == null) {
                    return top10MetricsData;
                }
                top10MetricsData = monitorsAdapterService.handleTop10MonitorMetricsData(MonitorType.PROCESSES.getType(), currentMetricsData);
            }
            default -> log.warn("未知的设备类别: {}", category);
        }
        return top10MetricsData;
    }

    @Override
    public List<Monitor> getMonitorByApp(String app) {
        return monitorDao.findByApp(app);
    }

    @Override
    public Map<String, List<Map<String, Object>>> getOsMonitorList(String app, String category, long limit, Long platformId) {
        Map<String, List<Map<String, Object>>> top10MetricsData = new HashMap<>();
        List<Hierarchy> categoryHierarchy = appService.getCategoryHierarchy("zh-CN", category);
        Set<String> values = categoryHierarchy.stream()
                .map(Hierarchy::getValue)
                .collect(Collectors.toSet());
        //根据 用户筛选出他有哪些平台
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        List<Map> platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
        Set<Long> platformIds = platform.stream().map(map -> Convert.toLong(map.get("platformId"))).collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(platform)) {
            return top10MetricsData;
        }
        // 根据 platformId 过滤监控列表
        List<Monitor> monitorList;
        if (platformId != null) {
            //对比 平台id是否存在
            if (!platformIds.contains(platformId)) {
                return top10MetricsData;
            }
            monitorList = monitorDao.findMonitorsByAppInAndPlatformId(values, platformId);
        } else {
            monitorList = monitorDao.findMonitorsByAppInAndPlatformIdIn(values, platformIds);
        }

        List<MonitorTopDto> monitorTopDtos = new ArrayList<>();
        for (Monitor monitor : monitorList) {
            switch (category) {
                case "os" -> {
                    List<CollectRep.MetricsData> currentMetricsData = realTimeDataReader.getCurrentMetricsData(monitor.getId());
                    monitorsAdapterService.handleMonitorMetricsDataByCategory(category, monitor, currentMetricsData);
                }
                default -> log.warn("未知的设备类别: {}", category);
            }
            MonitorTopDto monitorTopDto = new MonitorTopDto();
            monitorTopDto.setMonitorId(monitor.getId());
            monitorTopDto.setName(monitor.getName());
            monitorTopDto.setCpuValue(monitor.getCpuUsed());
            monitorTopDto.setMemValue(monitor.getMemUsage());
            monitorTopDto.setDiskValue(monitor.getDiskUsage());
            monitorTopDto.setTimeValue(monitor.getResponseTime());
            monitorTopDto.setPlatformId(monitor.getPlatformId());
            monitorTopDtos.add(monitorTopDto);
        }

        // 处理CPU使用率TOP10
        List<Map<String, Object>> cpuTop10 = monitorTopDtos.stream()
                .sorted((a, b) -> {
                    String valueA = a.getCpuValue();
                    String valueB = b.getCpuValue();
                    if (StrUtil.isEmpty(valueA)) return 1;
                    if (StrUtil.isEmpty(valueB)) return -1;
                    try {
                        return Double.compare(Double.parseDouble(valueB), Double.parseDouble(valueA));
                    } catch (NumberFormatException e) {
                        return valueB.compareTo(valueA);
                    }
                })
                .limit(limit)
                .map(dto -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", dto.getMonitorId());
                    map.put("name", dto.getName());
                    map.put("value", StrUtil.isEmpty(dto.getCpuValue()) ? "-" : NumberUtil.round(dto.getCpuValue(), 2));
                    return map;
                })
                .collect(Collectors.toList());

        // 处理内存使用率TOP10
        List<Map<String, Object>> memTop10 = monitorTopDtos.stream()
                .sorted((a, b) -> {
                    String valueA = a.getMemValue();
                    String valueB = b.getMemValue();
                    if (StrUtil.isEmpty(valueA)) return 1;
                    if (StrUtil.isEmpty(valueB)) return -1;
                    try {
                        return Double.compare(Double.parseDouble(valueB), Double.parseDouble(valueA));
                    } catch (NumberFormatException e) {
                        return valueB.compareTo(valueA);
                    }
                })
                .limit(limit)
                .map(dto -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", dto.getMonitorId());
                    map.put("name", dto.getName());
                    map.put("value", StrUtil.isEmpty(dto.getMemValue()) ? "-" : NumberUtil.round(dto.getMemValue(), 2));
                    return map;
                })
                .collect(Collectors.toList());

        // 处理磁盘使用率TOP10
        List<Map<String, Object>> diskTop10 = monitorTopDtos.stream()
                .sorted((a, b) -> {
                    String valueA = a.getDiskValue();
                    String valueB = b.getDiskValue();
                    if (StrUtil.isEmpty(valueA)) return 1;
                    if (StrUtil.isEmpty(valueB)) return -1;
                    try {
                        return Double.compare(Double.parseDouble(valueB), Double.parseDouble(valueA));
                    } catch (NumberFormatException e) {
                        return valueB.compareTo(valueA);
                    }
                })
                .limit(limit)
                .map(dto -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", dto.getMonitorId());
                    map.put("name", dto.getName());
                    map.put("value", StrUtil.isEmpty(dto.getDiskValue()) ? "-" : NumberUtil.round(dto.getDiskValue(), 2));
                    return map;
                })
                .collect(Collectors.toList());

        // 处理响应时间TOP10
        List<Map<String, Object>> timeTop10 = monitorTopDtos.stream()
                .sorted((a, b) -> {
                    String valueA = a.getTimeValue();
                    String valueB = b.getTimeValue();
                    if (StrUtil.isEmpty(valueA)) return 1;
                    if (StrUtil.isEmpty(valueB)) return -1;
                    try {
                        return Double.compare(Double.parseDouble(valueB), Double.parseDouble(valueA));
                    } catch (NumberFormatException e) {
                        return valueB.compareTo(valueA);
                    }
                })
                .limit(limit)
                .map(dto -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", dto.getMonitorId());
                    map.put("name", dto.getName());
                    map.put("value", StrUtil.isEmpty(dto.getTimeValue()) ? "-" : dto.getTimeValue());
                    return map;
                })
                .collect(Collectors.toList());

        top10MetricsData.put("CPU", cpuTop10);
        top10MetricsData.put("MEM", memTop10);
        top10MetricsData.put("DISK", diskTop10);
        top10MetricsData.put("TIME", timeTop10);

        return top10MetricsData;
    }

    @Override
    public void updateScanRemark(List<Long> ids) {
        collectorPlatformMapper.updateScanRemark(ids);
    }

    @Override
    public List<Monitor> getMonitorByTenantId(Long tenantId) {
        List<Map> platform = platformconfigApi.getPlatformByTenantId(String.valueOf(tenantId)).getData();
        Set<Long> platformIds = platform.stream().map(map -> Convert.toLong(map.get("platformId"))).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(platformIds)) {
            return List.of();
        }
        return monitorDao.findMonitorsByPlatformIdIn(platformIds);
    }

    @Override
    public List<Monitor> getMonitorByPlatformIds(List<Long> platformIds) {
        Set<Long> platformIdSet = new HashSet<>(platformIds);
        return monitorDao.findMonitorsByPlatformIdIn(platformIdSet);
    }

    @Override
    public Map<String, List<VictoriaMetricsDTO>> getTop5SwitchWirelessMonitorList(Long monitorId, String app, String category) {
        Bulletin bulletin = bulletinDao.findByApp(app);
        if (bulletin == null) {
            throw new IllegalArgumentException("Bulletin not found");
        }

        Map<String, List<String>> fields = bulletin.getFields();
        List<String> apFields = fields.get("ap");

        if (CollUtil.isEmpty(apFields)) {
            return Collections.emptyMap();
        }

        ZjMetricsDataService metricsData = SpringContextHolder.getBean(ZjMetricsDataService.class);
        List<VictoriaMetricsDTO> metricData = metricsData.getMetricData(Collections.singletonList(String.valueOf(monitorId)),
                apFields,
                category,
                null,
                null,
                "20s",
                "max",
                5
        );

        return metricData.stream()
                .collect(Collectors.groupingBy(VictoriaMetricsDTO::getMetric));
    }

    @Override
    public Map<String, List<VictoriaMetricsDTO>> getSwitchWirelessMonitorList(Long monitorId, String app, String history) {
        Bulletin bulletin = bulletinDao.findByApp(app);
        if (bulletin == null) {
            throw new IllegalArgumentException("Bulletin not found");
        }

        List<String> apFields = bulletin.getFields().get("controller_system");

        String step = switch (history) {
            case "1w" -> "10m";
            case "1m", "3m" -> "10h";
            default -> "10s";
        };

        Boolean interval = switch (history) {
            case "1w", "1m", "3m" -> true;
            default -> false;
        };

        ZjMetricsDataService metricsData = SpringContextHolder.getBean(ZjMetricsDataService.class);
        MetricsDataService metricsService = SpringContextHolder.getBean(MetricsDataService.class);

        List<VictoriaMetricsDTO> arrayList = metricsData.getMetricData(Collections.singletonList(String.valueOf(monitorId)), apFields, history, null, null, step, null, 10);

        MetricsHistoryData historyData = metricsService.getMetricHistoryData(monitorId, app, "radio_stats", "radio_band_type", null, history, interval);

        // 直接添加历史数据到arrayList
        historyData.getValues().forEach((key, values) ->
                values.forEach(value -> {
                    VictoriaMetricsDTO dto = new VictoriaMetricsDTO();
                    dto.setAssetId(String.valueOf(historyData.getId()));
                    dto.setPidMetric(historyData.getMetrics());
                    dto.setMetric("radio_band_type");
                    dto.setDateTime(String.valueOf(value.getTime()));
                    dto.setValue(value.getOrigin());
                    arrayList.add(dto);
                })
        );

        Map<String, List<VictoriaMetricsDTO>> result = new HashMap<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm");

        arrayList.stream()
                .collect(Collectors.groupingBy(VictoriaMetricsDTO::getMetric))
                .forEach((metric, dtos) -> {
                    if ("radio_band_type".equals(metric)) {
                        List<VictoriaMetricsDTO> array = new ArrayList<>();
                        dtos.stream()
                                .collect(Collectors.groupingBy(VictoriaMetricsDTO::getDateTime))
                                .forEach((time, timeGroup) -> {
                                    if (CollUtil.isNotEmpty(timeGroup)) {
                                        VictoriaMetricsDTO base = timeGroup.get(0);
                                        Map<String, Long> countMap = countValueEquals16(timeGroup);

                                        // 创建2.4G DTO
                                        VictoriaMetricsDTO dto24G = new VictoriaMetricsDTO();
                                        dto24G.setAssetId(base.getAssetId());
                                        dto24G.setPidMetric(base.getPidMetric());
                                        dto24G.setMetric(base.getMetric());
                                        dto24G.setDateTime(time);
                                        dto24G.setInterfaceName("2.4G");
                                        dto24G.setValue(String.valueOf(countMap.getOrDefault("equals16", 0L)));

                                        // 创建5G DTO
                                        VictoriaMetricsDTO dto5G = new VictoriaMetricsDTO();
                                        dto5G.setAssetId(base.getAssetId());
                                        dto5G.setPidMetric(base.getPidMetric());
                                        dto5G.setMetric(base.getMetric());
                                        dto5G.setDateTime(time);
                                        dto5G.setInterfaceName("5G");
                                        dto5G.setValue(String.valueOf(countMap.getOrDefault("notEquals16", 0L)));

                                        array.add(dto24G);
                                        array.add(dto5G);
                                    }
                                });

                        array.sort(Comparator.comparing(dto ->Long.parseLong(dto.getDateTime())));
                        result.put(metric, array);
                    } else {
                        dtos.sort(Comparator.comparing(dto ->Long.parseLong(dto.getDateTime())));
                        result.put(metric, dtos);
                    }
                });

        return result;
    }

    @Override
    public Map<String, String> getApUserNum(Long monitorId, String app) {
        Bulletin bulletin = bulletinDao.findByApp(app);
        if (bulletin == null) {
            throw new IllegalArgumentException("Bulletin not found");
        }

        List<String> apFields = bulletin.getFields().get("ac_status_v1");
        if (CollUtil.isEmpty(apFields)) {
            return Collections.emptyMap();
        }

        ZjMetricsDataService metricsData = SpringContextHolder.getBean(ZjMetricsDataService.class);
        MetricsDataService metricsService = SpringContextHolder.getBean(MetricsDataService.class);

        List<VictoriaMetricsDTO> arrayList = metricsData.getMetricData(
                Collections.singletonList(String.valueOf(monitorId)),
                apFields, "1h", null, null, "10s", null, 10);

        MetricsHistoryData historyData = metricsService.getMetricHistoryData(monitorId, app,
                "radio_stats", "radio_band_type", null, "1h", false);

        historyData.getValues().forEach((key, values) ->
                values.forEach(value -> {
                    VictoriaMetricsDTO dto = new VictoriaMetricsDTO();
                    dto.setAssetId(String.valueOf(historyData.getId()));
                    dto.setPidMetric(historyData.getMetrics());
                    dto.setMetric("radio_band_type");
                    dto.setDateTime(String.valueOf(value.getTime()));
                    dto.setValue(value.getOrigin());
                    arrayList.add(dto);
                })
        );

        Map<String, String> result = new HashMap<>();

        arrayList.stream()
                .collect(Collectors.groupingBy(VictoriaMetricsDTO::getMetric))
                .forEach((metric, dtos) -> {
                    if (CollUtil.isEmpty(dtos)) return;

                    OptionalLong maxTime = dtos.stream()
                            .mapToLong(dto -> {
                                try {
                                    return Long.parseLong(dto.getDateTime());
                                } catch (NumberFormatException e) {
                                    return Long.MIN_VALUE;
                                }
                            })
                            .filter(time -> time != Long.MIN_VALUE)
                            .max();

                    if (maxTime.isEmpty()) return;

                    long maxTimeValue = maxTime.getAsLong();
                    List<VictoriaMetricsDTO> latestGroup = dtos.stream()
                            .filter(dto -> {
                                try {
                                    return Long.parseLong(dto.getDateTime()) == maxTimeValue;
                                } catch (NumberFormatException e) {
                                    return false;
                                }
                            })
                            .collect(Collectors.toList());

                    if ("radio_band_type".equals(metric)) {
                        long equals16 = latestGroup.stream()
                                .mapToLong(dto -> "16".equals(dto.getValue()) ? 1 : 0)
                                .sum();

                        result.put("2.4G", String.valueOf(equals16));
                        result.put("5G", String.valueOf(latestGroup.size() - equals16));
                    } else {
                        result.put(metric, latestGroup.get(0).getValue());
                    }
                });

        return result;
    }



    public static Map<String, Long> countValueEquals16(List<VictoriaMetricsDTO> dtos) {
        long equals16 = dtos == null ? 0L :
                dtos.stream().filter(dto -> "16".equals(dto.getValue())).count();

        long total = dtos == null ? 0L : dtos.size();

        return Map.of(
                "equals16", equals16,
                "notEquals16", total - equals16
        );
    }



    private void collectOneTimeSdData(Monitor monitor, String collector, Param sdParam) {
        Long monitorId = monitor.getId();
        if (monitorId == null || monitorId == 0) {
            monitorId = MONITOR_ID_TMP;
        }
        Job appDefine = appService.getAppDefine(monitor.getApp());
        if (CommonConstants.PROMETHEUS.equals(monitor.getApp())) {
            appDefine.setApp(CommonConstants.PROMETHEUS_APP_PREFIX + monitor.getName());
        }
        appDefine.setMonitorId(monitorId);
        appDefine.setCyclic(false);
        appDefine.setTimestamp(System.currentTimeMillis());
        final Job sdJob = SdMonitorOperator.constructSdJob(appDefine, sdParam);
        List<CollectRep.MetricsData> collectRep;
        if (collector != null) {
            collectRep = collectJobScheduling.collectSyncJobData(sdJob, collector);
        } else {
            collectRep = collectJobScheduling.collectSyncJobData(sdJob);
        }
        monitor.setStatus(CommonConstants.MONITOR_UP_CODE);

        // If the detection result fails, a detection exception is thrown
        if (collectRep == null || collectRep.isEmpty()) {
            monitor.setStatus(CommonConstants.MONITOR_DOWN_CODE);
            throw new MonitorDetectException("Collect Timeout No Response");
        }
        if (collectRep.get(0).getCode() != CollectRep.Code.SUCCESS) {
            monitor.setStatus(CommonConstants.MONITOR_DOWN_CODE);
            throw new MonitorDetectException(collectRep.get(0).getMsg());
        }
    }

    private void detectMonitorDirectly(Monitor monitor, List<Param> params, String collector) {
        Long monitorId = monitor.getId();
        if (monitorId == null || monitorId == 0) {
            monitorId = MONITOR_ID_TMP;
        }
        Job appDefine = appService.getAppDefine(monitor.getApp());
        if (CommonConstants.PROMETHEUS.equals(monitor.getApp())) {
            appDefine.setApp(CommonConstants.PROMETHEUS_APP_PREFIX + monitor.getName());
        }
        appDefine.setMonitorId(monitorId);
        appDefine.setCyclic(false);
        appDefine.setTimestamp(System.currentTimeMillis());
        List<Configmap> configmaps = params.stream().map(param ->
                new Configmap(param.getField(), param.getValue(), param.getType())).collect(Collectors.toList());
        appDefine.setConfigmap(configmaps);
        // To detect availability, you only need to collect the set of availability metrics with a priority of 0.
        List<Metrics> availableMetrics = appDefine.getMetrics().stream()
                .filter(item -> item.getPriority() == 0).collect(Collectors.toList());
        appDefine.setMetrics(availableMetrics);
        List<CollectRep.MetricsData> collectRep;
        if (collector != null) {
            collectRep = collectJobScheduling.collectSyncJobData(appDefine, collector);
        } else {
            collectRep = collectJobScheduling.collectSyncJobData(appDefine);
        }
        monitor.setStatus(CommonConstants.MONITOR_UP_CODE);

        // If the detection result fails, a detection exception is thrown
        if (collectRep == null || collectRep.isEmpty()) {
            monitor.setStatus(CommonConstants.MONITOR_DOWN_CODE);
            throw new MonitorDetectException("Collect Timeout No Response");
        }
        if (collectRep.get(0).getCode() != CollectRep.Code.SUCCESS) {
            monitor.setStatus(CommonConstants.MONITOR_DOWN_CODE);
            throw new MonitorDetectException(collectRep.get(0).getMsg());
        }
    }


    @Override
    public void addNewMonitorOptionalMetrics(List<String> metrics, Monitor monitor, List<Param> params) {
        long monitorId = SnowFlakeIdGenerator.generateId();
        List<Tag> tags = monitor.getTags();
        if (tags == null) {
            tags = new LinkedList<>();
            monitor.setTags(tags);
        }
        tags.add(Tag.builder().name(CommonConstants.TAG_MONITOR_ID).value(String.valueOf(monitorId)).type((byte) 0).build());
        tags.add(Tag.builder().name(CommonConstants.TAG_MONITOR_NAME).value(String.valueOf(monitor.getName())).type((byte) 0).build());
        Job appDefine = appService.getAppDefine(monitor.getApp());
        //设置用户可选指标
        List<Metrics> metricsDefine = appDefine.getMetrics();
        Set<String> metricsDefineNamesSet = metricsDefine.stream()
                .map(Metrics::getName)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(metrics) || !metricsDefineNamesSet.containsAll(metrics)) {
            throw new MonitorMetricsException("no select metrics or select illegal metrics");
        }

        List<Metrics> realMetrics = metricsDefine.stream().filter(m -> metrics.contains(m.getName())).collect(Collectors.toList());
        appDefine.setMetrics(realMetrics);
        appDefine.setMonitorId(monitorId);
        appDefine.setDefaultInterval(monitor.getIntervals());
        appDefine.setCyclic(true);
        appDefine.setTimestamp(System.currentTimeMillis());
        List<Configmap> configmaps = params.stream().map(param -> {
            param.setMonitorId(monitorId);
            return new Configmap(param.getField(), param.getValue(), param.getType());
        }).collect(Collectors.toList());
        appDefine.setConfigmap(configmaps);
        // Send the collection task to get the job ID
        // 下发采集任务得到jobId
        long jobId = collectJobScheduling.addAsyncCollectJob(appDefine, null);
        // Brush the library after the download is successful
        // 下发成功后刷库
        try {
            monitor.setId(monitorId);
            monitor.setJobId(jobId);
            monitorDao.save(monitor);
            paramDao.saveAll(params);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            // Repository brushing abnormally cancels the previously delivered task
            // 刷库异常取消之前的下发任务
            collectJobScheduling.cancelAsyncCollectJob(jobId);
            throw new MonitorDatabaseException(e.getMessage());
        }
    }

    @Override
    public List<String> getMonitorMetrics(String app) {
        return appService.getAppDefineMetricNames(app);
    }

    @Override
    public void export(List<Long> ids, String type, HttpServletResponse res) throws Exception {
        var imExportService = imExportServiceMap.get(type);
        if (imExportService == null) {
            throw new IllegalArgumentException("not support export type: " + type);
        }
        var fileName = imExportService.getFileName();
        res.setHeader(HttpHeaders.CONTENT_DISPOSITION, CONTENT_VALUE);
        res.setContentType(CONTENT_VALUE);
        res.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        res.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, HttpHeaders.CONTENT_DISPOSITION);
        imExportService.exportConfig(res.getOutputStream(), ids);
    }

    @Override
    public void importConfig(MultipartFile file) throws Exception {
        var fileName = FileUtil.getFileName(file);
        var type = FileUtil.getFileType(file);
        if (!imExportServiceMap.containsKey(type)) {
            throw new RuntimeException(ExportFileConstants.FILE + " " + fileName + " is not supported.");
        }
        var imExportService = imExportServiceMap.get(type);
        imExportService.importConfig(file.getInputStream());
    }


    @Override
    @Transactional(readOnly = true)
    public void validate(MonitorDto monitorDto, Boolean isModify) throws IllegalArgumentException {
        // The request monitoring parameter matches the monitoring parameter definition mapping check
        Monitor monitor = monitorDto.getMonitor();
        monitor.setHost(monitor.getHost().trim());
        monitor.setName(monitor.getName().trim());
        Map<String, Param> paramMap = monitorDto.getParams()
                .stream()
                .peek(param -> {
                    param.setMonitorId(monitor.getId());
                    String value = param.getValue() == null ? null : param.getValue().trim();
                    param.setValue(value);
                })
                .collect(Collectors.toMap(Param::getField, param -> param));
        // Check name uniqueness and can not equal app type
        if (isModify != null) {
            Optional<Job> defineOptional = appService.getAppDefineOption(monitor.getName());
            if (defineOptional.isPresent()) {
                throw new IllegalArgumentException("Monitoring name cannot be the existed monitoring type name!");
            }
            List<Monitor> monitorOptional = monitorDao.findMonitorByNameEquals(monitor.getName());
            if (CollectionUtil.isNotEmpty(monitorOptional)) {
                List<Monitor> collect = monitorOptional.stream().filter(x -> x.getPlatformId() == monitor.getPlatformId()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(collect) && isModify) {
                    throw new IllegalArgumentException("监控名称已存在!");
                }
            }
        }
        if (!CollectionUtils.isEmpty(monitor.getTags())) {
            monitor.setTags(monitor.getTags().stream().distinct().collect(Collectors.toList()));
        }
        // the dispatch collector must exist if pin
        if (org.apache.commons.lang3.StringUtils.isNotBlank(monitorDto.getCollector())) {
            Optional<Collector> optionalCollector = collectorDao.findCollectorByName(monitorDto.getCollector());
            if (optionalCollector.isEmpty()) {
                throw new IllegalArgumentException("The pinned collector does not exist.");
            }
        } else {
            monitorDto.setCollector(null);
        }
        // Parameter definition structure verification
        List<ParamDefine> paramDefines = appService.getAppParamDefines(monitorDto.getMonitor().getApp());
        if (!CollectionUtils.isEmpty(paramDefines)) {
            for (ParamDefine paramDefine : paramDefines) {
                String field = paramDefine.getField();
                Param param = paramMap.get(field);
                if (paramDefine.isRequired() && (param == null || param.getValue() == null)) {
                    throw new IllegalArgumentException("Params field " + field + " is required.");
                }
                if (param != null && param.getValue() != null && org.apache.commons.lang3.StringUtils.isNotBlank(param.getValue())) {
                    switch (paramDefine.getType()) {
                        case "number":
                            double doubleValue;
                            try {
                                doubleValue = Double.parseDouble(param.getValue());
                            } catch (Exception e) {
                                throw new IllegalArgumentException("Params field " + field + " type "
                                        + paramDefine.getType() + " is invalid.");
                            }
                            if (paramDefine.getRange() != null) {
                                if (!IntervalExpressionUtil.validNumberIntervalExpress(doubleValue,
                                        paramDefine.getRange())) {
                                    throw new IllegalArgumentException("Params field " + field + " type "
                                            + paramDefine.getType() + " over range " + paramDefine.getRange());
                                }
                            }
                            param.setType(CommonConstants.PARAM_TYPE_NUMBER);
                            break;
                        case "textarea":
                            Short textareaLimit = paramDefine.getLimit();
                            if (textareaLimit != null && param.getValue().length() > textareaLimit) {
                                throw new IllegalArgumentException("Params field " + field + " type "
                                        + paramDefine.getType() + " over limit " + param.getValue());
                            }
                            break;
                        case "text":
                            Short textLimit = paramDefine.getLimit();
                            if (textLimit != null && param.getValue().length() > textLimit) {
                                throw new IllegalArgumentException("Params field " + field + " type "
                                        + paramDefine.getType() + " over limit " + textLimit);
                            }
                            break;
                        case "host":
                            String hostValue = param.getValue();
                            if (hostValue.toLowerCase().contains(NetworkConstants.HTTP_HEADER)) {
                                hostValue = hostValue.replaceAll(PATTERN_HTTP, SignConstants.BLANK);
                            }
                            if (hostValue.toLowerCase().contains(NetworkConstants.HTTPS_HEADER)) {
                                hostValue = hostValue.replace(PATTERN_HTTPS, SignConstants.BLANK);
                            }
                            if (!IpDomainUtil.validateIpDomain(hostValue)) {
                                throw new IllegalArgumentException("Params field " + field + " value "
                                        + hostValue + " is invalid host value.");
                            }
                            break;
                        case "password":
                            // The plaintext password needs to be encrypted for transmission and storage
                            String passwordValue = param.getValue();
                            if (!AesUtil.isCiphertext(passwordValue)) {
                                passwordValue = AesUtil.aesEncode(passwordValue);
                                param.setValue(passwordValue);
                            }
                            param.setType(CommonConstants.PARAM_TYPE_PASSWORD);
                            break;
                        case "boolean":
                            // boolean check
                            String booleanValue = param.getValue();
                            if (!"true".equalsIgnoreCase(booleanValue) && !"false".equalsIgnoreCase(booleanValue)) {
                                throw new IllegalArgumentException("Params field " + field + " value "
                                        + booleanValue + " is invalid boolean value.");
                            }
                            break;
                        case "radio":
                            // radio single value check
                            List<ParamDefine.Option> options = paramDefine.getOptions();
                            boolean invalid = true;
                            if (options != null) {
                                for (ParamDefine.Option option : options) {
                                    if (param.getValue().equalsIgnoreCase(option.getValue())) {
                                        invalid = false;
                                        break;
                                    }
                                }
                            }
                            if (invalid) {
                                throw new IllegalArgumentException("Params field " + field + " value "
                                        + param.getValue() + " is invalid option value");
                            }
                            break;
                        case "checkbox":
                            List<ParamDefine.Option> checkboxOptions = paramDefine.getOptions();
                            boolean checkboxInvalid = true;
                            if (checkboxOptions != null) {
                                for (ParamDefine.Option option : checkboxOptions) {
                                    if (param.getValue().equalsIgnoreCase(option.getValue())) {
                                        checkboxInvalid = false;
                                        break;
                                    }
                                }
                            }
                            if (checkboxInvalid) {
                                throw new IllegalArgumentException("Params field " + field + " value "
                                        + param.getValue() + " is invalid checkbox value");
                            }
                            break;
                        case "metrics-field":
                        case "key-value":
                            if (JsonUtil.fromJson(param.getValue(), new TypeReference<>() {
                            }) == null) {
                                throw new IllegalArgumentException("Params field " + field + " value "
                                        + param.getValue() + " is invalid key-value value");
                            }
                            break;
                        case "array":
                            String[] arrays = param.getValue().split(",");
                            if (arrays.length == 0) {
                                throw new IllegalArgumentException("Param field" + field + " value "
                                        + param.getValue() + " is invalid arrays value");
                            }
                            if (param.getValue().startsWith("[") && param.getValue().endsWith("]")) {
                                param.setValue(param.getValue().substring(1, param.getValue().length() - 1));
                            }
                            break;
                        // todo More parameter definitions and actual value format verification
                        default:
                            throw new IllegalArgumentException("ParamDefine type " + paramDefine.getType() + " is invalid.");
                    }
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyMonitor(Monitor monitor, List<Param> params, String collector, GrafanaDashboard grafanaDashboard) throws RuntimeException {
        final Optional<Param> sdParam = SdMonitorOperator.getSdParam(params);
        long monitorId = monitor.getId();
        // Check to determine whether the monitor corresponding to the monitor id exists
        Optional<Monitor> queryOption = monitorDao.findById(monitorId);
        if (queryOption.isEmpty()) {
            throw new IllegalArgumentException("The Monitor " + monitorId + " not exists");
        }
        Long platformId = 0L;
        Monitor preMonitor = queryOption.get();
        if (!preMonitor.getApp().equals(monitor.getApp())) {
            // The type of monitoring cannot be modified
            throw new IllegalArgumentException("Can not modify monitor's app type");
        }
        platformId = preMonitor.getPlatformId();
        // Auto Update Default Tags: monitorName
        List<Tag> tags = monitor.getTags();
        if (CollectionUtils.isEmpty(tags)) {
            tags = new LinkedList<>();
            monitor.setTags(tags);
        }
        for (Tag tag : tags) {
            if (CommonConstants.TAG_MONITOR_NAME.equals(tag.getName())) {
                tag.setValue(monitor.getName());
            }
        }

        if (preMonitor.getStatus() != CommonConstants.MONITOR_PAUSED_CODE) {
            // Construct the collection task Job entity
            Job appDefine = appService.getAppDefine(monitor.getApp());
            if (sdParam.isPresent()) {
                // not allow to modify a sub monitor to main monitor
                tags.stream()
                        .filter(tag -> org.apache.commons.lang3.StringUtils.equals(tag.getName(), CommonConstants.TAG_AUTO_CREATED))
                        .findFirst()
                        .ifPresent(tag -> {
                            final MonitorBind isSubMonitorBefore = monitorBindDao.findMonitorBindByBizIdAndMonitorIdAndType(Long.valueOf(tag.getValue()),
                                    monitorId, CommonConstants.MONITOR_BIND_TYPE_SD_SUB_MONITOR);
                            if (Objects.nonNull(isSubMonitorBefore)) {
                                throw new UnsupportedOperationException("Can not change a auto-created monitor to sd! ");
                            }
                        });

                // create main monitor bind
                final List<MonitorBind> mainMonitorBind = monitorBindDao.findMonitorBindByBizIdAndType(monitorId, CommonConstants.MONITOR_BIND_TYPE_SD_MAIN_MONITOR);
                if (CollectionUtils.isEmpty(mainMonitorBind)) {
                    monitorBindDao.save(Objects.requireNonNull(
                            SdMonitorOperator.buildSdMainMonitorBind(
                                    SdMonitorParam.builder().sdParam(sdParam.get()).build(), monitorId
                            )));
                }

                appDefine = SdMonitorOperator.constructSdJob(appDefine, sdParam.get());
                monitor.setTags(SdMonitorOperator.addMainMonitorTag(tags, sdParam.get()));
            }

            if (CommonConstants.PROMETHEUS.equals(monitor.getApp())) {
                appDefine.setApp(CommonConstants.PROMETHEUS_APP_PREFIX + monitor.getName());
            }
            appDefine.setId(preMonitor.getJobId());
            appDefine.setMonitorId(monitorId);
            appDefine.setDefaultInterval(monitor.getIntervals());
            appDefine.setCyclic(true);
            appDefine.setTimestamp(System.currentTimeMillis());
            List<Configmap> configmaps = params.stream().map(param ->
                    new Configmap(param.getField(), param.getValue(), param.getType())).collect(Collectors.toList());
            appDefine.setConfigmap(configmaps);
            long newJobId;
            if (collector == null) {
                newJobId = collectJobScheduling.updateAsyncCollectJob(appDefine);
            } else {
                newJobId = collectJobScheduling.updateAsyncCollectJob(appDefine, collector);
            }
            monitor.setJobId(newJobId);
        }

        try {
            detectMonitor(monitor, params, collector);
        } catch (Exception ignored) {
        }

        // After the update is successfully released, refresh the database
        try {
            collectorMonitorBindDao.deleteCollectorMonitorBindsByMonitorId(monitorId);
            if (collector != null) {
                CollectorMonitorBind collectorMonitorBind = CollectorMonitorBind.builder()
                        .collector(collector).monitorId(monitorId)
                        .build();
                collectorMonitorBindDao.save(collectorMonitorBind);
            }
            // force update gmtUpdate time, due the case: monitor not change, param change. we also think monitor change
            monitor.setGmtUpdate(LocalDateTime.now());
            herzbeatMonitorRedisDAO.delete("herzbeat_monitor:" + monitorId);
            herzbeatMonitorRedisDAO.set("herzbeat_monitor:" + monitorId, monitor);
            // update or open grafana dashboard
            if (monitor.getApp().equals(CommonConstants.PROMETHEUS) && grafanaDashboard != null) {
//                if (grafanaDashboard.isEnabled()) {
//                    dashboardService.createOrUpdateDashboard(grafanaDashboard.getTemplate(), monitorId);
//                } else {
//                    dashboardService.closeGrafanaDashboard(monitorId);
//                }
            }
            monitorDao.save(monitor);

            // delete all sub monitor if sd param is removed
            final List<MonitorBind> subMonitorBindList = monitorBindDao.findMonitorBindByBizIdAndType(monitorId, CommonConstants.MONITOR_BIND_TYPE_SD_SUB_MONITOR);
            if (!CollectionUtils.isEmpty(subMonitorBindList) && sdParam.isEmpty()) {
                deleteMonitors(subMonitorBindList.stream().map(MonitorBind::getMonitorId).collect(Collectors.toSet()));
            }
            paramDao.saveAll(params);

            //查看是否有修改平台信息
            if ((platformId != 0L) && (platformId != monitor.getPlatformId())) {
                MonitorAssetDTO checkedData = monitorassetApi.getAssetInfoByIdAndFormId(String.valueOf(monitorId), platformId).getCheckedData();
                if (BeanUtil.isNotEmpty(checkedData)) {
                    checkedData.setPlatformId(monitor.getPlatformId());
                    checkedData.setPlatformName(monitor.getPlatformName());
                    monitorassetApi.updateAssetById(checkedData);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            // Repository brushing abnormally cancels the previously delivered task
            collectJobScheduling.cancelAsyncCollectJob(monitor.getJobId());
            throw new MonitorDatabaseException(e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMonitor(long id) throws RuntimeException {
        Optional<Monitor> monitorOptional = monitorDao.findById(id);
        if (monitorOptional.isPresent()) {
            try {
                collectorMonitorBindDao.deleteCollectorMonitorBindsByMonitorId(id);
                alertDefineBindDao.deleteAlertDefineMonitorBindsByMonitorIdEquals(id);
            } catch (Exception e) {
                log.error("删除监控关联数据失败，监控ID: {}, 错误: {}", id, e.getMessage());
            }
            monitorDao.deleteById(id);
            Monitor monitor = monitorOptional.get();
            // delete tag 删除监控对应的标签
            tagService.deleteMonitorSystemTags(monitor);
            paramDao.deleteParamsByMonitorId(id);
            try {
                tagMonitorBindDao.deleteTagMonitorBindsByMonitorId(id);
            } catch (Exception e) {
                log.error("删除监控关联数据失败，监控ID: {}, 错误: {}", id, e.getMessage());
            }
            collectJobScheduling.cancelAsyncCollectJob(monitor.getJobId());
            herzbeatMonitorRedisDAO.delete("herzbeat_monitor:" + id);
            applicationContext.publishEvent(new MonitorDeletedEvent(applicationContext, monitor.getId()));
            MonitorAssetDTO dto = new MonitorAssetDTO();
            dto.setAssetId(String.valueOf(monitor.getId()));
            dto.setPlatformId(monitor.getPlatformId());
            monitorassetApi.updateAssetByAssetId(dto);

            //根据 app 查询 Bulletin信息
            Bulletin bulletin = bulletinService.getBulletinByApp(monitor.getApp());
            if (bulletin != null) {
                List<Long> monitorIds = bulletin.getMonitorIds();
                // 创建新的可变列表
                List<Long> newMonitorIds = new ArrayList<>();
                if (monitorIds != null) {
                    //移除该监控
                    monitorIds.remove(monitor.getId());
                    newMonitorIds.addAll(monitorIds);
                }
                bulletin.setMonitorIds(newMonitorIds);
                //修改 Bulletin monitorIds的信息
                bulletinService.editBulletin(bulletin);
            }

            // 资源删除触发告警
            List<AlarmDorisReqDTO> toInsert = new ArrayList<>();
            final Long alertId = alarmConfigApi.getMaxAlertId().getData();
            AlarmDorisReqDTO collectorAlert = new AlarmDorisReqDTO();
            JSONObject tags = new JSONObject();
            tags.put("app", monitor.getApp());
            tags.put("monitorId", monitor.getId());
            tags.put("monitorName", monitor.getName());
            collectorAlert.setPriority(0);
            collectorAlert.setStatus(0);
            collectorAlert.setIsSolved(0);
            collectorAlert.setFirstAlarmTime(new Date().getTime());
            collectorAlert.setGmtCreate(new Date());
            collectorAlert.setGmtUpdate(DateUtil.date());
            collectorAlert.setLastAlarmTime(new Date().getTime());
            collectorAlert.setMonitorName(monitor.getName());
            collectorAlert.setMonitorId(Convert.toStr(monitor.getId()));
            collectorAlert.setPlatformName(monitor.getPlatformName());
            collectorAlert.setPlatformId(monitor.getPlatformId());
            collectorAlert.setContent(String.format("该资源(%s)已被删除！", monitor.getName()));
            collectorAlert.setAlarmName(monitor.getName());
            collectorAlert.setTimes(1);
            collectorAlert.setResourceType(1);
            collectorAlert.setApp(monitor.getApp());
            collectorAlert.setTags(JsonUtil.toJson(tags));
            collectorAlert.setTarget("delete");
            collectorAlert.setAlarmId(0L);
            collectorAlert.setId(alertId + 1);
            toInsert.add(collectorAlert);
            Map<String, List> alertMap = new HashMap<>();
            alertMap.put("insertList", toInsert);
            //创建方法中需要有updateList，防止空指针异常
            alertMap.put("updateList", new ArrayList<>());
            alarmConfigApi.createAlarmToDoris(alertMap);

            List<TopologyDTO> list = topologyApi.get().getCheckedData();
            if (!list.isEmpty()) {
                List<TopologyDTO> topologyDTOS = new ArrayList<>();
                list.forEach(topologyDTO -> {
                    try {
                        JSONObject topologyJson = JSONObject.parseObject(topologyDTO.getTopologyJson());
                        JSONArray edgesJson = topologyJson.getJSONArray("edges");
                        JSONArray interfacesJson = JSONObject.parseArray(topologyDTO.getInterfacesJson());

                        List<Map> edges = JSONObject.parseArray(edgesJson.toJSONString(), Map.class);
                        edges.forEach(edge -> {
                            String relationList = StringUtil.toString(edge.get("relationList"));
                            if (StrUtil.isNotEmpty(relationList)) {
                                JSONArray relationJson = JSONArray.parseArray(relationList);
                                if (relationJson != null) {
                                    List<Map> relations = JSONObject.parseArray(relationJson.toJSONString(), Map.class);
                                    if (relations.size() > 0) {
                                        Iterator<Map> iterator = relations.iterator();
                                        while (iterator.hasNext()) {
                                            Map map = iterator.next();
                                            if (monitor.getId().equals(Convert.toLong(map.get("sourceMonitorId"))) || monitor.getId().equals(Convert.toLong(map.get("targetMonitorId")))) {
                                                iterator.remove(); // 使用迭代器来移除元素
                                            }
                                        }
                                    }
                                    if (JsonUtil.toJson(relations).equals("[]")) {
                                        edge.put("relationList", "");
                                    } else {
                                        edge.put("relationList", JsonUtil.toJson(relations));
                                    }
                                }
                            }
                        });

                        List<Map> interfaces = JSONObject.parseArray(interfacesJson.toJSONString(), Map.class);
                        interfaces.removeIf(iface -> monitor.getId().equals(Convert.toLong(iface.get("monitorId"))));

                        JSONObject resourceJson = JSONObject.parseObject(topologyDTO.getResourceJson());
                        JSONArray assetsList = resourceJson.getJSONArray("assetsList");

                        if (!assetsList.isEmpty()) {
                            JSONArray physicalList = resourceJson.getJSONArray("physicalList");
                            JSONObject jsonObject = new JSONObject();
                            List<Map> resource = JSONObject.parseArray(assetsList.toJSONString(), Map.class);
                            resource.removeIf(r -> monitor.getId().equals(Convert.toLong(r.get("id"))));
                            jsonObject.put("assetsList", JSONObject.parseArray(JsonUtil.toJson(resource)));
                            jsonObject.put("physicalList", physicalList);
                            topologyDTO.setResourceJson(JsonUtil.toJson(jsonObject));
                        }

                        JSONObject updatedTopologyJson = new JSONObject();
                        updatedTopologyJson.put("nodes", topologyJson.getJSONArray("nodes"));
                        updatedTopologyJson.put("combos", topologyJson.getJSONArray("combos"));
                        updatedTopologyJson.put("edges", edges);

                        topologyDTO.setTopologyJson(updatedTopologyJson.toJSONString());
                        topologyDTO.setInterfacesJson(JsonUtil.toJson(interfaces));


                        topologyDTOS.add(topologyDTO);
                    } catch (Exception e) {
                        log.error("Error processing topology", e);
                    }
                });
                topologyApi.updatebatch(topologyDTOS);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMonitors(Set<Long> ids) throws RuntimeException {
        List<Monitor> monitors = monitorDao.findMonitorsByIdIn(ids);
        if (!monitors.isEmpty()) {
            monitorDao.deleteAll(monitors);
            paramDao.deleteParamsByMonitorIdIn(ids);
            Set<Long> monitorIds = monitors.stream().map(Monitor::getId).collect(Collectors.toSet());
            tagMonitorBindDao.deleteTagMonitorBindsByMonitorIdIn(monitorIds);
            alertDefineBindDao.deleteAlertDefineMonitorBindsByMonitorIdIn(monitorIds);
            for (Monitor monitor : monitors) {
                // delete tag 删除监控对应的标签
                tagService.deleteMonitorSystemTags(monitor);
                herzbeatMonitorRedisDAO.delete("herzbeat_monitor:" + monitor.getId());
                collectorMonitorBindDao.deleteCollectorMonitorBindsByMonitorId(monitor.getId());
                collectJobScheduling.cancelAsyncCollectJob(monitor.getJobId());
                applicationContext.publishEvent(new MonitorDeletedEvent(applicationContext, monitor.getId()));
            }
        }
    }

    @Override
    @Transactional(readOnly = true)
    public MonitorDto getMonitorDto(long id) throws RuntimeException {
        Optional<Monitor> monitorOptional = monitorDao.findById(id);
        if (monitorOptional.isPresent()) {
            Monitor monitor = monitorOptional.get();
            MonitorDto monitorDto = new MonitorDto();
            List<Param> params = paramDao.findParamsByMonitorId(id);
            monitorDto.setParams(params);
            if (DispatchConstants.PROTOCOL_PROMETHEUS.equalsIgnoreCase(monitor.getApp())) {
                List<CollectRep.MetricsData> metricsDataList = warehouseService.queryMonitorMetricsData(id);
                List<String> metrics = metricsDataList.stream().map(CollectRep.MetricsData::getMetrics).collect(Collectors.toList());
                monitorDto.setMetrics(metrics);
//                monitorDto.setGrafanaDashboard(dashboardService.getDashboardByMonitorId(id));
            } else {
                Job job = appService.getAppDefine(monitor.getApp());
                List<String> metrics = job.getMetrics().stream()
                        .filter(Metrics::isVisible)
                        .map(Metrics::getName).collect(Collectors.toList());
                monitorDto.setMetrics(metrics);
            }
            monitorDto.setMonitor(monitor);
            Optional<CollectorMonitorBind> bindOptional = collectorMonitorBindDao.findCollectorMonitorBindByMonitorId(monitor.getId());
            bindOptional.ifPresent(bind -> monitorDto.setCollector(bind.getCollector()));
            return monitorDto;
        } else {
            return null;
        }
    }

    @Override
    public Page<Monitor> getMonitors(Specification<Monitor> specification, PageRequest pageRequest) {
        return monitorDao.findAll(specification, pageRequest);
    }

    @Override
    public void cancelManageMonitors(HashSet<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        final Set<Long> subMonitorIds = monitorBindDao.findAllByBizIdInAndType(ids, CommonConstants.MONITOR_BIND_TYPE_SD_SUB_MONITOR).stream()
                .map(MonitorBind::getMonitorId)
                .collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(subMonitorIds)) {
            ids.addAll(subMonitorIds);
        }

        // Update monitoring status Delete corresponding monitoring periodic task
        // The jobId is not deleted, and the jobId is reused again after the management is started.
        List<Monitor> managedMonitors = monitorDao.findMonitorsByIdIn(ids)
                .stream().filter(monitor ->
                        monitor.getStatus() != CommonConstants.MONITOR_PAUSED_CODE)
                .peek(monitor -> monitor.setStatus(CommonConstants.MONITOR_PAUSED_CODE))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(managedMonitors)) {
            for (Monitor monitor : managedMonitors) {
                collectJobScheduling.cancelAsyncCollectJob(monitor.getJobId());
            }
            monitorDao.saveAll(managedMonitors);
        }
    }

    @Override
    public void enableManageMonitors(HashSet<Long> ids) {
        // Update monitoring status Add corresponding monitoring periodic task
        List<Monitor> unManagedMonitors = monitorDao.findMonitorsByIdIn(ids)
                .stream().filter(monitor ->
                        monitor.getStatus() == CommonConstants.MONITOR_PAUSED_CODE)
                .peek(monitor -> monitor.setStatus(CommonConstants.MONITOR_UP_CODE))
                .collect(Collectors.toList());
        if (unManagedMonitors.isEmpty()) {
            return;
        }

        for (Monitor monitor : unManagedMonitors) {
            // Construct the collection task Job entity
            List<Param> params = paramDao.findParamsByMonitorId(monitor.getId());
            final Optional<Param> sdParam = SdMonitorOperator.getSdParam(params);
            Job appDefine = appService.getAppDefine(monitor.getApp());
            if (sdParam.isPresent()) {
                final List<MonitorBind> mainMonitorBind = monitorBindDao.findMonitorBindByBizIdAndType(monitor.getId(), CommonConstants.MONITOR_BIND_TYPE_SD_MAIN_MONITOR);
                if (CollectionUtils.isEmpty(mainMonitorBind)) {
                    monitorBindDao.save(Objects.requireNonNull(
                            SdMonitorOperator.buildSdMainMonitorBind(
                                    SdMonitorParam.builder().sdParam(sdParam.get()).build(), monitor.getId()
                            )));
                }

                appDefine = SdMonitorOperator.constructSdJob(appDefine, sdParam.get());
            }

            if (CommonConstants.PROMETHEUS.equals(monitor.getApp())) {
                appDefine.setApp(CommonConstants.PROMETHEUS_APP_PREFIX + monitor.getName());
            }
            appDefine.setMonitorId(monitor.getId());
            appDefine.setDefaultInterval(monitor.getIntervals());
            appDefine.setCyclic(true);
            appDefine.setTimestamp(System.currentTimeMillis());
            List<Configmap> configmaps = params.stream().map(param ->
                    new Configmap(param.getField(), param.getValue(), param.getType())).collect(Collectors.toList());
            List<ParamDefine> paramDefaultValue = appDefine.getParams().stream()
                    .filter(item -> StringUtils.hasText(item.getDefaultValue()))
                    .collect(Collectors.toList());
            paramDefaultValue.forEach(defaultVar -> {
                if (configmaps.stream().noneMatch(item -> item.getKey().equals(defaultVar.getField()))) {
                    Configmap configmap = new Configmap(defaultVar.getField(), defaultVar.getDefaultValue(), CommonConstants.TYPE_STRING);
                    configmaps.add(configmap);
                }
            });
            appDefine.setConfigmap(configmaps);

            // Issue collection tasks
            Optional<CollectorMonitorBind> bindOptional =
                    collectorMonitorBindDao.findCollectorMonitorBindByMonitorId(monitor.getId());
            String collector = bindOptional.map(CollectorMonitorBind::getCollector).orElse(null);
            long newJobId = collectJobScheduling.addAsyncCollectJob(appDefine, collector);
            monitor.setJobId(newJobId);
            applicationContext.publishEvent(new MonitorDeletedEvent(applicationContext, monitor.getId()));
            try {
                detectMonitor(monitor, params, collector);
            } catch (Exception ignored) {
            }
        }
        monitorDao.saveAll(unManagedMonitors);
    }

    @Override
    public List<AppCount> getAllAppMonitorsCount() {
        List<AppCount> appCounts = monitorDao.findAppsStatusCount();
        if (CollectionUtils.isEmpty(appCounts)) {
            return null;
        }
        //Statistical category information, calculate the number of corresponding states for each monitor
        Map<String, AppCount> appCountMap = new HashMap<>(appCounts.size());
        for (AppCount item : appCounts) {
            AppCount appCount = appCountMap.getOrDefault(item.getApp(), new AppCount());
            appCount.setApp(item.getApp());
            switch (item.getStatus()) {
                case CommonConstants.MONITOR_UP_CODE:
                    appCount.setAvailableSize(appCount.getAvailableSize() + item.getSize());
                    break;
                case CommonConstants.MONITOR_DOWN_CODE:
                    appCount.setUnAvailableSize(appCount.getUnAvailableSize() + item.getSize());
                    break;
                case CommonConstants.MONITOR_PAUSED_CODE:
                    appCount.setUnManageSize(appCount.getUnManageSize() + item.getSize());
                    break;
                default:
                    break;
            }
            appCountMap.put(item.getApp(), appCount);
        }
        //Traverse the map obtained by statistics and convert it into a List<App Count> result set
        return appCountMap.values().stream().map(item -> {
            item.setSize(item.getAvailableSize() + item.getUnManageSize() + item.getUnAvailableSize());
            try {
                Job job = appService.getAppDefine(item.getApp());
                item.setCategory(job.getCategory());
            } catch (Exception ignored) {
                return null;
            }
            return item;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyMonitors(List<Long> ids) {

        ids.stream().parallel().forEach(id -> {
            // get monitor and Params according id
            Optional<Monitor> monitorOpt = monitorDao.findById(id);
            List<Param> params = paramDao.findParamsByMonitorId(id);

            monitorOpt.ifPresentOrElse(monitor -> {
                // deep copy original monitor to achieve persist in JPA
                Monitor newMonitor = JsonUtil.fromJson(JsonUtil.toJson(monitor), Monitor.class);
                if (newMonitor != null) {
                    copyMonitor(newMonitor, params);
                }
            }, () -> log.warn("can not find the monitor for id ：{}", id));
        });
    }

    @Override
    public void updateAppCollectJob(Job job) {
        List<Monitor> monitors = monitorDao.findMonitorsByAppEquals(job.getApp()).
                stream().filter(monitor -> monitor.getStatus() != CommonConstants.MONITOR_PAUSED_CODE)
                .collect(Collectors.toList());
        if (monitors.isEmpty()) {
            return;
        }
        List<CollectorMonitorBind> monitorBinds = collectorMonitorBindDao.findCollectorMonitorBindsByMonitorIdIn(
                monitors.stream().map(Monitor::getId).collect(Collectors.toSet()));
        Map<Long, String> monitorIdCollectorMap = monitorBinds.stream().collect(
                Collectors.toMap(CollectorMonitorBind::getMonitorId, CollectorMonitorBind::getCollector));
        for (Monitor monitor : monitors) {
            try {
                Job appDefine = job.clone();
                if (monitor == null || appDefine == null || monitor.getId() == null || monitor.getJobId() == null) {
                    log.error("update monitor job error when template modify, define | id | jobId is null. continue");
                    continue;
                }
                if (CommonConstants.PROMETHEUS.equals(monitor.getApp())) {
                    appDefine.setApp(CommonConstants.PROMETHEUS_APP_PREFIX + monitor.getName());
                }
                appDefine.setId(monitor.getJobId());
                appDefine.setMonitorId(monitor.getId());
                appDefine.setDefaultInterval(monitor.getIntervals());
                appDefine.setCyclic(true);
                appDefine.setTimestamp(System.currentTimeMillis());
                List<Param> params = paramDao.findParamsByMonitorId(monitor.getId());
                List<Configmap> configmaps = params.stream().map(param -> new Configmap(param.getField(),
                        param.getValue(), param.getType())).collect(Collectors.toList());
                List<ParamDefine> paramDefaultValue = appDefine.getParams().stream()
                        .filter(item -> StringUtils.hasText(item.getDefaultValue()))
                        .collect(Collectors.toList());
                paramDefaultValue.forEach(defaultVar -> {
                    if (configmaps.stream().noneMatch(item -> item.getKey().equals(defaultVar.getField()))) {
                        Configmap configmap = new Configmap(defaultVar.getField(), defaultVar.getDefaultValue(), (byte) 1);
                        configmaps.add(configmap);
                    }
                });
                appDefine.setConfigmap(configmaps);
                // if is pinned collector
                String collector = monitorIdCollectorMap.get(monitor.getId());
                // 下发采集任务
                long newJobId = collectJobScheduling.updateAsyncCollectJob(appDefine, collector);
                monitor.setJobId(newJobId);
                monitorDao.save(monitor);
            } catch (Exception e) {
                log.error("update monitor job error when template modify: {}.continue", e.getMessage(), e);
            }
        }
    }

    @Override
    public Monitor getMonitor(Long monitorId) {
        return monitorDao.findById(monitorId).orElse(null);
    }

    @Override
    public void updateMonitorStatus(Long monitorId, byte status) {
        monitorDao.updateMonitorStatus(monitorId, status);
    }

    @Override
    public List<Monitor> getAppMonitors(String app) {
        return monitorDao.findMonitorsByAppEquals(app);
    }

    private void resetMetricsCommonField(Monitor monitor, Job appDefine, SdMonitorParam sdMonitorParam) {
        if (Objects.isNull(sdMonitorParam)
                || org.apache.commons.lang3.StringUtils.isAnyBlank(sdMonitorParam.getDetectedHost(), sdMonitorParam.getDetectedPort())) {
            return;
        }

        for (Metrics metric : appDefine.getMetrics()) {
            Arrays.stream(metric.getClass().getDeclaredFields())
                    .filter(field -> metric.getProtocol().equalsIgnoreCase(field.getName()))
                    .findFirst()
                    .ifPresent(field -> {
                        field.setAccessible(true);
                        final Object obj;
                        try {
                            obj = field.get(metric);
                            if (obj instanceof CommonRequestProtocol protocol) {
                                protocol.setHost(sdMonitorParam.getDetectedHost());
                                protocol.setPort(sdMonitorParam.getDetectedPort());
                            }
                        } catch (IllegalAccessException exception) {
                            log.warn("Not such field {}", field.getName());
                        }
                    });
        }

        monitor.setHost(sdMonitorParam.getDetectedHost());
        monitor.getTags().add(Tag.builder()
                .name(CommonConstants.TAG_MONITOR_HOST)
                .value(String.valueOf(sdMonitorParam.getDetectedHost()))
                .type(CommonConstants.TAG_TYPE_AUTO_GENERATE)
                .build());
        monitor.setName(monitor.getApp().toUpperCase() + CommonConstants.LOCALE_SEPARATOR + sdMonitorParam.getDetectedHost()
                + CommonConstants.LOCALE_SEPARATOR + SnowFlakeIdGenerator.generateId());
    }


    private void copyMonitor(Monitor monitor, List<Param> params) {
        List<Tag> oldTags = monitor.getTags();
        List<Tag> newTags = filterTags(oldTags);

        monitor.setTags(newTags);

        monitor.setName(String.format("%s - copy", monitor.getName()));
        addMonitor(monitor, params, null, null);
    }

    private List<Tag> filterTags(List<Tag> tags) {
        if (tags == null || tags.isEmpty()) {
            return new LinkedList<>();
        }
        return tags.stream()
                .filter(tag -> !(tag.getName().equals(CommonConstants.TAG_MONITOR_ID) || tag.getName().equals(CommonConstants.TAG_MONITOR_NAME)))
                .collect(Collectors.toList());
    }

    @Override

    public List<Monitor> getMonitorsByList(Specification<Monitor> specification) {
        return monitorDao.findAll();
    }

    @Override
    public List<CategoryDTO> getMonitorsBySelect() {
        List<CategoryDTO> categories = new ArrayList<>();

        // 遍历枚举中的每个父级
        for (ResourceCategoryEnum parentEnum : ResourceCategoryEnum.values()) {

            if (!Arrays.asList("hardware", "host", "storage").contains(parentEnum.getCode())) {
                // 创建父级对象
                CategoryDTO parentDTO = new CategoryDTO(parentEnum.getCode(), parentEnum.getName());
                List<SubCategoryDTO> children = new ArrayList<>();

                if (parentEnum.getSubEnums() != null) {
                    // 遍历父级的子级枚举
                    for (ResourceAppEnum childEnum : parentEnum.getSubEnums()) {
                        // 创建子级对象
                        SubCategoryDTO childDTO = new SubCategoryDTO(childEnum.getValue(), childEnum.getLabel());
                        // 将子级对象添加到父级的子级列表中
                        children.add(childDTO);
                    }
                }
                // 设置父级对象的子级列表
                parentDTO.setChildren(children);
                // 将父级对象添加到最终的列表中
                categories.add(parentDTO);
            }
        }
        return categories;
    }

    @Override
    public Map<String, Object> getTotalType(Long platformId, String app, String category) {
        List<ResourceAppEnum> subEnums = new ArrayList<>();
        if (StrUtil.isNotEmpty(category)) {
            subEnums = ResourceCategoryUtil.getSubEnumsByCategory(category);
        }
        List<Map> list = collectorPlatformMapper.getTotalType(platformId, app, subEnums);
//        List<Map> list = collectorPlatformMapper.getTotalType(platformId, app, subEnums);
//        if (StrUtil.isNotEmpty(app)) {
//            subEnums = ResourceCategoryUtil.getSubEnumsByCategory(category);
//            list = collectorPlatformMapper.getTotalType(platformId, app, subEnums);
//        }
        Long total = 0L;
        Map<String, Object> resultMap = new HashMap<>();
        for (Map map1 : list) {
            Long status = Convert.toLong(map1.get("status"));
            Long count = Convert.toLong(map1.get("count"));
            total += count;
            switch (status.intValue()) {
                case 0:
                    resultMap.put("unmanaged", count);
                    break;
                case 1:
                    resultMap.put("available", count);
                    break;
                case 2:
                    resultMap.put("unavailable", count);
                    break;
                default:
                    break;
            }
        }
        resultMap.put("total", total);
        return resultMap;
    }

    @Override
    public Map<String, Object> getTotalTypeByPlatform(List<Map> platform, String app, String category) {
        List<ResourceAppEnum> subEnums = new ArrayList<>();
        if (StrUtil.isNotEmpty(category)) {
            subEnums = ResourceCategoryUtil.getSubEnumsByCategory(category);
        }
        Map<String, Object> map = new HashMap<>();
        if (CollectionUtil.isNotEmpty(platform)) {
            List<Map> list = collectorPlatformMapper.getTotalTypeByPlatform(platform, app, subEnums);
            Long total = 0L;

            for (Map map1 : list) {
                if (Convert.toLong(map1.get("status")) == 0) {
                    total = total + Convert.toLong(map1.get("count"));
                    map.put("unmanaged", map1.get("count"));
                }
                if (Convert.toLong(map1.get("status")) == 1) {
                    total = total + Convert.toLong(map1.get("count"));
                    map.put("available", map1.get("count"));
                }
                if (Convert.toLong(map1.get("status")) == 2) {
                    total = total + Convert.toLong(map1.get("count"));
                    map.put("unavailable", map1.get("count"));
                }
            }
            map.put("total", total);
        } else {
            map.put("unmanaged", 0);
            map.put("available", 0);
            map.put("unavailable", 0);
            map.put("total", 0);
        }
        return map;
    }

    @Override
    public String getMonitorProtocol(String monitorId) {
        return collectorPlatformMapper.getMonitorProtocol(monitorId);
    }

    @Override
    public List<Monitor> getAppMonitorsByPlatformId(String app, Set<Long> platform) {
        return monitorDao.findMonitorsByAppEqualsAndPlatformIdIn(app, platform);
    }

    @Override
    public List<Monitor> getMonitorByplatformId(Long platformId, Set<String> app) {
        return monitorDao.findMonitorsByPlatformIdEqualsAndAppIn(platformId, app);
    }

    @Override
    public void exportConfigTemplate(String value, HttpServletResponse response, Specification<Collector> specification, List<Map> platform) {
        String fileName = "";
        for (ResourceCategoryEnum resourceCategoryEnum : ResourceCategoryEnum.values()) {
            if (resourceCategoryEnum.getCode().equals(value)) {
                fileName = resourceCategoryEnum.getName();
                break;
            }
        }
        if (fileName == "") {
            throw exception(TEMPLATE_EXISTS);
        }
        //查询采集器列表
        List<Collector> collectorDaoList = collectorDao.findAll(specification);
        //platformName
        List<String> names = platform.stream().map(item -> {
            return item.get("platformName").toString();
        }).collect(Collectors.toList());
        //查询平台列表
        downloadExcel(FileTypeConstants.FILE_TYPE + "/template/" + fileName + ".xlsx", fileName + ".xlsx", response,
                names);
//        minioUtil.downloadExcel("config/" + fileName + ".xlsx", fileName + ".xlsx", response, names);
    }

    @Override
    @Transactional
    public void importExcel(MultipartFile file, List<Map> platform, String appType) throws Exception {
        log.info("importExcel方法进入");
        Map<ResourceConfigEnum, List<Map<String, String>>> dataMap = new EnumMap<>(ResourceConfigEnum.class);
        List<MonitorDto> monitorDtos = new ArrayList<>();

        // 根据appType获取对应的ResourceAppEnum列表
        List<ResourceAppEnum> expectedAppEnums = new ArrayList<>();
        for (ResourceCategoryEnum categoryEnum : ResourceCategoryEnum.values()) {
            if (categoryEnum.getCode().equals(appType) && categoryEnum.getSubEnums() != null) {
                expectedAppEnums.addAll(categoryEnum.getSubEnums());
            }
        }

        if (CollectionUtils.isEmpty(expectedAppEnums)) {
            throw exception(new ErrorCode(500, "未找到对应的应用类型: " + appType));
        }

        // 创建一个线程池，线程数可以根据 CPU 核心数调整。这里使用 10 个并发线程。
//        ExecutorService executorService = Executors.newFixedThreadPool(10);
//        List<Future<Void>> futures = new ArrayList<>();
//        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
        Workbook workbook = new XSSFWorkbook(file.getInputStream());
        // 修改：获取ResourceConfigEnum中与expectedAppEnums对应的label集合
        Set<String> expectedSheetNames = new HashSet<>();
        for (ResourceConfigEnum resourceConfigEnum : ResourceConfigEnum.values()) {
            // 只添加appType对应的ResourceConfigEnum的label
            if (expectedAppEnums.stream().anyMatch(appEnum ->
                    appEnum.getValue().equals(resourceConfigEnum.getValue()))) {
                expectedSheetNames.add(resourceConfigEnum.getLabel());
            }
        }

        Set<String> actualSheetNames = new HashSet<>();
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet sheet = workbook.getSheetAt(i);
            if (sheet != null) {
                actualSheetNames.add(sheet.getSheetName());
            }
        }
        // 检查是否包含所有必需的sheet页
        if (!actualSheetNames.containsAll(expectedSheetNames)) {
            Set<String> missingSheets = new HashSet<>(expectedSheetNames);
            missingSheets.removeAll(actualSheetNames);
            throw exception(new ErrorCode(500, "文件格式异常，缺少必需的页脚: " + String.join(", ", missingSheets)));
        }

        for (ResourceConfigEnum resourceAppEnum : ResourceConfigEnum.values()) {
            // 只处理appType对应的ResourceConfigEnum
            if (expectedAppEnums.stream().anyMatch(appEnum -> appEnum.getValue().equals(resourceAppEnum.getValue()))) {
                Sheet sheet = workbook.getSheet(resourceAppEnum.getLabel());
                log.info("sheet：->" + sheet);
                if (sheet != null) {
                    List<Map<String, String>> dataList = processSheet(sheet);
                    log.info("dataList：->" + dataList);

                    // 字段校验规范
                    if (dataList.size() > 0) {
                        List<HashMap<String, String>> fieldList = resourceAppEnum.getFieldList();
                        for (Map<String, String> data : dataList) {
                            for (HashMap<String, String> field : fieldList) {
                                for (String key : new HashSet<>(data.keySet())) {
                                    if (field.containsKey(key)) {
                                        // 正则
                                        String regular = field.get("regular");
                                        // 字段值
                                        String value = data.get(key);
                                        Pattern pattern = Pattern.compile(regular);
                                        Matcher matcher = pattern.matcher(value);
                                        // 符合标准
                                        if (matcher.matches()) {
                                            // 字段名称
                                            String fieldName = field.get(key);
                                            String filedValue = data.get(key);
                                            data.remove(key);
                                            data.put(fieldName, filedValue);
                                        } else {
                                            String error = resourceAppEnum.getLabel() + "的第" + (dataList.indexOf(data) + 2) + "行的" + key + "列格式有误";
//                                            if (key.equals("监控名称")) {
//                                                error = resourceAppEnum.getLabel() + "的第" + (dataList.indexOf(data) + 2) + "行的" + key + "不能出现中文";
//                                            }
                                            // 不符合标准抛出错误,打印出行数和哪个字段
                                            throw exception(new ErrorCode(500, error));
                                        }
                                    }
                                }
                            }
                        }

                        dataMap.put(resourceAppEnum, dataList);
                        // 封装成monitorDto返回
                        List<ParamDefine> paramDefines = appService.getAppParamDefines(resourceAppEnum.getValue().toLowerCase());
                        List<MonitorDto> list = dataList.stream().map(item -> {
                            MonitorDto monitorDto = new MonitorDto();
                            List<Param> params = new ArrayList<>();
                            Monitor monitor = new Monitor();
                            for (ParamDefine paramDefine : paramDefines) {
                                if (item.containsKey(paramDefine.getField())) {
                                    String type = paramDefine.getType();
                                    Param param = new Param();
                                    param.setField(paramDefine.getField());
                                    param.setValue(item.get(paramDefine.getField()));

                                    // type字段类型:0-number数字,1-string字符串
                                    if (type.equals("boolean")) {
                                        Integer integer = Integer.valueOf(item.get(paramDefine.getField()));
                                        if (integer == 1) {
                                            param.setValue("false");
                                        } else {
                                            param.setValue("true");
                                        }
                                    }
                                    //此处写需要手动区分Excel字段的覆盖
                                    if (paramDefine.getField().equals("certificate")) {
                                        String certificate = item.get("certificate");
                                        if (certificate.equals("密码")) {
                                            param.setValue("custom");
                                        } else {
                                            param.setValue("private-key");
                                        }
                                    }
                                    if (paramDefine.getField().equals("version")) {
                                        String version = item.get("version");
                                        if (version.equals("SNMPv1")) {
                                            param.setValue("0");
                                        } else if (version.equals("SNMPv2c")) {
                                            param.setValue("1");
                                        } else {
                                            param.setValue("3");
                                        }
                                    }
                                    if (paramDefine.getField().equals("authPassword")) {
                                        String authPassword = item.get("authPassword");
                                        if (authPassword.equals("DES")) {
                                            param.setValue("0");
                                        } else {
                                            param.setValue("1");
                                        }
                                    }

                                    if (paramDefine.getField().equals("privPasswordEncryption")) {
                                        String privPasswordEncryption = item.get("privPasswordEncryption");
                                        if (privPasswordEncryption.equals("DES")) {
                                            param.setValue("0");
                                        } else {
                                            param.setValue("1");
                                        }
                                    }
                                    if (paramDefine.getField().equals("authPasswordEncryption")) {
                                        String authPasswordEncryption = item.get("authPasswordEncryption");
                                        if (authPasswordEncryption.equals("MD5")) {
                                            param.setValue("0");
                                        } else {
                                            param.setValue("1");
                                        }
                                    }
                                    params.add(param);
                                }
                                //k8s认证方式补全
                                if (paramDefine.getField().equals("authType")) {
                                    Param param = new Param();
                                    param.setField(paramDefine.getField());
                                    param.setValue(item.get(paramDefine.getField()));
                                    param.setValue(paramDefine.getOptions().get(0).getValue());
                                    params.add(param);
                                }
                            }
                            monitor.setName(item.get("name"));
                            monitor.setApp(resourceAppEnum.getValue());
                            monitor.setHost(item.get("host"));
                            monitor.setIntervals(60);
                            monitor.setDescription(item.get("description"));

                            String app = monitor.getApp();
                            List<MonitorDto> monitorList = monitorDtos.stream()
                                    .filter(dto -> dto.getMonitor().getApp().equals(app))
                                    .collect(Collectors.toList());
                            Integer index = monitorList.size() + 2;


                            //平台名称
                            String platFormName = item.get("platFormName");
                            //通过平台名称查询平台id
//                            platformId
                            Long platformId = null;
                            for (Map<String, Object> map : platform) {
                                for (Map.Entry<String, Object> entry : map.entrySet()) {
                                    String key = entry.getKey();
                                    Object value = entry.getValue();
                                    if (key.equals("platformName") && value.toString().equals(platFormName)) {
                                        platformId = Long.valueOf(map.get("platformId").toString());
                                        break;
                                    }
                                }
                            }
                            if (platformId == null) {
                                String error = resourceAppEnum.getLabel() + "的第" + index + "行未查询到对应平台";
                                throw exception(new ErrorCode(500, error));
                            }
                            List<CollectorPlatformDO> collectors = collectorPlatformMapper.selectListByIds(platformId);
                            if (!collectors.isEmpty()) {
                                // 打乱采集器数据
                                Collections.shuffle(collectors);
                                // 选择第一个采集器
                                String collectorName = collectors.get(0).getCollectorName();
                                monitorDto.setCollector(collectorName);
                            } else {
                                String error = resourceAppEnum.getLabel() + "的第" + index + "行该平台下未绑定采集器，无法新增资产，请联系管理员";
                                throw exception(new ErrorCode(500, error));
                            }
                            monitor.setPlatformId(platformId);
                            monitor.setPlatformName(platFormName);
                            monitorDto.setDetected(false);
                            monitorDto.setMonitor(monitor);
                            monitorDto.setParams(params);
                            log.info("封装的monitorDto：->" + monitorDto);
                            return monitorDto;
                        }).toList();
                        monitorDtos.addAll(list);
                    }
                }
            }
            log.info("最终的monitorDto111：->" + monitorDtos);
            monitorDtos.forEach(monitorDto -> {

                List<Param> list = monitorDto.getParams();
                List<Param> paramList = new ArrayList<>(list);

                // 获取证书ID
                String certificateId = null;
                boolean privateKey = false;

                boolean containsceryofocateId = list.stream()
                        .anyMatch(param -> "ceryofocateId".equals(param.getField()));

                for (Param param : list) {
                    if (param.getField().equals("certificate")) {
                        if (!param.getValue().equals("custom")) {
                            privateKey = true;
                        }
                    }

                    if (param.getField().equals("password")) {
                        if (privateKey) {
                            // 使用密码参数的值来查询数据
                            certificateId = Convert.toStr(param.getValue());
                            Param param1 = new Param();
                            if (StringUtil.isNotEmpty(certificateId)) {
                                param1.setField("ceryofocateId");
                                param1.setValue(certificateId.toString());
                                param1.setType((byte) 0);
                                paramList.add(param1);
                            }
                            break;
                        }
                    }
                    //
                    if (containsceryofocateId) {
                        if (param.getField().equals("ceryofocateId")) {
                            param.setValue(certificateId.toString());
                        }
                    }

                }

                if (certificateId != null) {
                    UserCertificateRespDTO userCertificateRespDTO = usercertificateApi.getUser(certificateId).getData();
                    // 查询证书信息
                    if (userCertificateRespDTO != null) {
                        // 更新参数列表中相关参数的值
                        for (Param p : paramList) {
                            if (p.getField().equals("username")) {
                                // 更新名称
                                p.setValue(userCertificateRespDTO.getUsername());
                            } else if (p.getField().equals("password")) {
                                // 更新密码
                                try {
                                    p.setValue(AesUtil.aesDecode(userCertificateRespDTO.getPassword()));
                                } catch (Exception e) {
                                    throw new RuntimeException(e);
                                }
                            } else if (!userCertificateRespDTO.getType().equals("custom") && p.getField().equals("privateKey")) {
                                // 更新私钥
                                p.setValue(userCertificateRespDTO.getPrivateKey());
                            }
                        }
                    }
                }
                monitorDto.setParams(paramList);

                log.info("monitorDto->" + monitorDto);
            });
        }
        List<Long> ids = Collections.synchronizedList(new ArrayList<>());
        for (MonitorDto monitorDto : monitorDtos) {
            try {
                validate(monitorDto, false);  // 验证
                log.info("validate检验通过");
                //detectMonitor(monitorDto.getMonitor(), monitorDto.getParams(), monitorDto.getCollector());
                //log.info("detectMonitor采集器测试通过");
                addMonitor(monitorDto.getMonitor(), monitorDto.getParams(), monitorDto.getCollector(), monitorDto.getGrafanaDashboard());// 添加监控
                log.info("addMonitor监控添加成功");
                ids.add(monitorDto.getMonitor().getId());
            } catch (Exception e) {
                String fileName = "";
                for (ResourceAppEnum resourceCategoryEnum : ResourceAppEnum.values()) {
                    if (resourceCategoryEnum.getValue().equals(monitorDto.getMonitor().getApp())) {
                        fileName = resourceCategoryEnum.getLabel();
                        break;
                    }
                }
                String app = monitorDto.getMonitor().getApp();
                List<MonitorDto> monitorList = monitorDtos.stream()
                        .filter(dto -> dto.getMonitor().getApp().equals(app))
                        .collect(Collectors.toList());
                Integer index = monitorList.indexOf(monitorDto);
                String error = fileName + "的第" + (index + 2) + "行" + e.getMessage();
                throw exception(new ErrorCode(500, error));
            }
        }
        // 添加日志
        if (!ids.isEmpty()) {
            OperateLogAspect.addExt("ids", ids);
        }

//            // 遍历 monitorDtos，提交任务到线程池并行执行
//            monitorDtos.forEach(monitorDto -> {
//                Future<Void> future = executorService.submit(new Callable<Void>() {
//                    @Override
//                    public Void call() throws Exception {
//                        try {
//                            validate(monitorDto, false);  // 验证
//                            log.info("validate检验通过");
//                            //detectMonitor(monitorDto.getMonitor(), monitorDto.getParams(), monitorDto.getCollector());
//                            //log.info("detectMonitor采集器测试通过");
//                            addMonitor(monitorDto.getMonitor(), monitorDto.getParams(), monitorDto.getCollector(), monitorDto.getGrafanaDashboard());// 添加监控
//                            log.info("addMonitor监控添加成功");
//                        } catch (Exception e) {
//                            String fileName = "";
//                            for (ResourceAppEnum resourceCategoryEnum : ResourceAppEnum.values()) {
//                                if (resourceCategoryEnum.getValue().equals(monitorDto.getMonitor().getApp())) {
//                                    fileName = resourceCategoryEnum.getLabel();
//                                    break;
//                                }
//                            }
//                            String app = monitorDto.getMonitor().getApp();
//                            List<MonitorDto> monitorList = monitorDtos.stream()
//                                    .filter(dto -> dto.getMonitor().getApp().equals(app))
//                                    .collect(Collectors.toList());
//                            Integer index = monitorList.indexOf(monitorDto);
//                            String error = fileName + "的第" + (index + 2) + "行" + e.getMessage();
//                            throw exception(new ErrorCode(500, error));
//                        }
//                        return null;
//                    }
//                });
//
//                futures.add(future);  // 将 Future 对象加入列表以便后续等待
//            });
//            // 等待所有的任务执行完成
//            for (Future<Void> future : futures) {
//                try {
//                    future.get();  // 阻塞等待任务完成
//                } catch (InterruptedException | ExecutionException e) {
//                    log.error("任务执行失败", e);
//                    throw new RuntimeException(e);
//                }
//            }
//            // 添加日志
//            if (!ids.isEmpty()) {
//                OperateLogAspect.addExt("ids", ids);
//            }
//        } finally {
//            // 关闭线程池
//            executorService.shutdown();
//            try {
//                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
//                    executorService.shutdownNow();
//                }
//            } catch (InterruptedException e) {
//                executorService.shutdownNow();
//            }
//        }
    }

    private List<Map<String, String>> processSheet(Sheet sheet) {
        List<Map<String, String>> dataList = new ArrayList<>();
        Iterator<Row> rowIterator = sheet.iterator();

        if (rowIterator.hasNext()) {
            Row headerRow = rowIterator.next();
            List<String> headers = new ArrayList<>();
            for (Cell cell : headerRow) {
                headers.add(cell.getStringCellValue());
            }

            while (rowIterator.hasNext()) {
                Row dataRow = rowIterator.next();
                if (isRowEmpty(dataRow)) {
                    continue; // 跳过空行
                }
                Map<String, String> instance = populateInstance(headers, dataRow).get(0);
                dataList.add(instance);
            }
        }

        return dataList;
    }

    private boolean isRowEmpty(Row row) {
        for (Cell cell : row) {
            if (cell.getCellType() != CellType.BLANK) {
                return false; // 行中至少有一个非空单元格
            }
        }
        return true; // 行是空的
    }

    private List<Map<String, String>> populateInstance(List<String> headers, Row dataRow) {
        Map<String, String> rowMap = new HashMap<>();
        for (int i = 0; i < headers.size(); i++) {
            Cell cell = dataRow.getCell(i, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
            String cellValue;

            switch (cell.getCellType()) {
                case STRING:
                    cellValue = cell.getStringCellValue();
                    break;
                case NUMERIC:
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == (long) numericValue) {
                        cellValue = String.valueOf((long) numericValue);
                    } else {
                        cellValue = String.valueOf(numericValue);
                    }
                    break;
                case BOOLEAN:
                    cellValue = String.valueOf(cell.getBooleanCellValue());
                    break;
                default:
                    cellValue = "";
            }

            rowMap.put(headers.get(i), cellValue);
        }
        return Collections.singletonList(rowMap);
    }

    public void downloadExcel(String fileName, String returnFileName, HttpServletResponse res, List<String> names) {
        byte[] readerObjects = fileApi.ReaderObjects(fileName);

        if (readerObjects == null || readerObjects.length == 0) {
            res.setStatus(HttpServletResponse.SC_NOT_FOUND);
            return;
        }

        try {
            // 修改 Excel 文件
            readerObjects = modifyExcel(readerObjects, names);

            // 设置响应头
            res.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            res.setCharacterEncoding("utf-8"); // 确保字符编码在设置内容类型之后
            String encodedFileName = UriUtils.encode(returnFileName, "UTF-8").replace("+", "%20");
            res.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);

            // 写入文件内容到响应
            try (ServletOutputStream stream = res.getOutputStream()) {
                stream.write(readerObjects);
                stream.flush();
            }
        } catch (Exception e) {
            // 使用日志记录异常
            res.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    private byte[] modifyExcel(byte[] fileContent, List<String> newDropdownOptions) throws IOException {
        try (Workbook workbook = new XSSFWorkbook(new ByteArrayInputStream(fileContent));
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {

            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                for (Row row : sheet) {
                    for (Cell cell : row) {
                        if ("设备平台".equals(cell.getStringCellValue())) {
                            // Assume the dropdown is in the next column
                            int columnIndex = cell.getColumnIndex();
                            modifyDropdown(sheet, columnIndex, newDropdownOptions);
                        }
                    }
                }
            }

            workbook.write(out);
            return out.toByteArray();
        }
    }

    private void modifyDropdown(Sheet sheet, int columnIndex, List<String> options) {
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createExplicitListConstraint(options.toArray(new String[0]));
        CellRangeAddressList addressList = new CellRangeAddressList(1, sheet.getLastRowNum(), columnIndex, columnIndex);
        DataValidation validation = helper.createValidation(constraint, addressList);
        validation.setSuppressDropDownArrow(true);
        validation.setShowErrorBox(true);
        sheet.addValidationData(validation);
    }

    @Override
    public AlarmReqDTO getAlarmByMonitorId(Long monitorId) {
        return collectorPlatformMapper.getAlarmByMonitorId(monitorId);
    }
}
