package cn.iocoder.cloud.module.cloudedge.controller.admin.alertdefinetemplate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

/**
* 多维告警模板 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class AlertDefineTemplateBaseVO {

    @Schema(description = "告警名称")
    private String name;

    @Schema(description = "app", required = true)
    @NotNull(message = "app不能为空")
    private String app;

    @Schema(description = "规则")
    private String expr;

    @Schema(description = "field")
    private String field;

    @Schema(description = "监控", required = true)
    @NotNull(message = "监控不能为空")
    private String metric;

    @Schema(description = "告警级别", required = true)
    @NotNull(message = "告警级别不能为空")
    private Byte priority;

    @Schema(description = "告警通知内容模版")
    private String template;

    @Schema(description = "出发次数")
    private Integer times;

}
