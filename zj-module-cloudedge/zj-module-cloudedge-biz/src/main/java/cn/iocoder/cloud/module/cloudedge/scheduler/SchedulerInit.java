package cn.iocoder.cloud.module.cloudedge.scheduler;

import cn.iocoder.cloud.module.cloudedge.dal.mysql.dao.*;
import cn.iocoder.cloud.module.cloudedge.service.AppService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hertzbeat.common.constants.CommonConstants;
import org.apache.hertzbeat.common.entity.job.Configmap;
import org.apache.hertzbeat.common.entity.job.Job;
import org.apache.hertzbeat.common.entity.manager.*;
import org.apache.hertzbeat.common.util.SdMonitorOperator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * scheduler init
 *
 * <AUTHOR>
 */
@Configuration
@Order(value = Ordered.LOWEST_PRECEDENCE - 1)
@Slf4j
public class SchedulerInit implements CommandLineRunner {

    @Autowired
    private CollectorScheduling collectorScheduling;

    @Autowired
    private CollectJobScheduling collectJobScheduling;

    private static final String MAIN_COLLECTOR_NODE_IP = "127.0.0.1";

    @Autowired
    private AppService appService;

    @Autowired
    private MonitorDao monitorDao;

    @Autowired
    private ParamDao paramDao;

    @Autowired
    private CollectorDao collectorDao;

    @Autowired
    private CollectorMonitorBindDao collectorMonitorBindDao;
    @Autowired
    private MonitorBindDao monitorBindDao;

    @Override
    public void run(String... args) throws Exception {
        // init pre collector status 初始化采集器状态为存活状态  过滤默认采集器
        List<Collector> collectors = collectorDao.findByNameNot("main-default-collector").stream()
                .peek(item -> item.setStatus(CommonConstants.COLLECTOR_STATUS_OFFLINE))
                .collect(Collectors.toList());
        collectorDao.saveAll(collectors);
        // todo 修改默认采集器
//        // insert default consistent node
//        CollectorInfo collectorInfo = CollectorInfo.builder()
//                                              .name(CommonConstants.MAIN_COLLECTOR_NODE)
//                                              .ip(MAIN_COLLECTOR_NODE_IP)
//                                              .build();
//        // 触发采集器是否可ping通
//        collectorScheduling.collectorGoOnline(CommonConstants.MAIN_COLLECTOR_NODE, collectorInfo);
//        for (Collector collector : collectors) {
//            CollectorInfo collectorInfo = CollectorInfo.builder()
//                    .name(collector.getName())
//                    .ip(collector.getIp())
//                    .build();
//            collectorScheduling.collectorGoOnline(collector.getName(), collectorInfo);
//        }
        // init jobs
        List<Monitor> monitors = monitorDao.findMonitorsByStatusNotInAndAndJobIdNotNull(List.of(CommonConstants.MONITOR_PAUSED_CODE));
        List<CollectorMonitorBind> monitorBinds = collectorMonitorBindDao.findAll();
        final Set<Long> sdMonitorIds = monitorBindDao.findAllByType(CommonConstants.MONITOR_BIND_TYPE_SD_SUB_MONITOR).stream()
                .map(MonitorBind::getBizId)
                .collect(Collectors.toSet());
//        Map<Long, String> monitorIdCollectorMap = monitorBinds.stream().collect(
//                Collectors.toMap(CollectorMonitorBind::getMonitorId, CollectorMonitorBind::getCollector));
        Map<Long, String> monitorIdCollectorMap = monitorBinds.stream().collect(
                Collectors.toMap(
                        CollectorMonitorBind::getMonitorId,
                        CollectorMonitorBind::getCollector,
                        (existingValue, newValue) -> {
                            return existingValue;
                        }
                )
        );
        for (Monitor monitor : monitors) {
            try {
                // build collect job entity
                Job appDefine = appService.getAppDefine(monitor.getApp());
                List<Param> params = paramDao.findParamsByMonitorId(monitor.getId());
                if (sdMonitorIds.contains(monitor.getId())) {
                    appDefine = SdMonitorOperator.constructSdJob(appDefine, SdMonitorOperator.getSdParam(params).get());
                }

                if (CommonConstants.PROMETHEUS.equals(monitor.getApp())) {
                    appDefine.setApp(CommonConstants.PROMETHEUS_APP_PREFIX + monitor.getName());
                }
                appDefine.setId(monitor.getJobId());
                appDefine.setMonitorId(monitor.getId());
                appDefine.setDefaultInterval(monitor.getIntervals());
                appDefine.setCyclic(true);
                appDefine.setTimestamp(System.currentTimeMillis());

                List<Configmap> configmaps = params.stream()
                        .map(param -> new Configmap(param.getField(), param.getValue(),
                                param.getType())).collect(Collectors.toList());
                List<ParamDefine> paramDefaultValue = appDefine.getParams().stream()
                        .filter(item -> StringUtils.isNotBlank(item.getDefaultValue()))
                        .toList();
                paramDefaultValue.forEach(defaultVar -> {
                    if (configmaps.stream().noneMatch(item -> item.getKey().equals(defaultVar.getField()))) {
                        Configmap configmap = new Configmap(defaultVar.getField(), defaultVar.getDefaultValue(), CommonConstants.TYPE_STRING);
                        configmaps.add(configmap);
                    }
                });
                appDefine.setConfigmap(configmaps);
                String collector = monitorIdCollectorMap.get(monitor.getId());
                long jobId = collectJobScheduling.addAsyncCollectJob(appDefine, collector);
                monitor.setJobId(jobId);
                monitorDao.save(monitor);
            } catch (Exception e) {
                log.error("init monitor job: {} error,continue next monitor", monitor, e);
            }
        }
    }
}
