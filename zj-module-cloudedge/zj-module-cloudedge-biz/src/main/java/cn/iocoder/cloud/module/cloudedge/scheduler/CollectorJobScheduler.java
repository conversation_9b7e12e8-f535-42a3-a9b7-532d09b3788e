package cn.iocoder.cloud.module.cloudedge.scheduler;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.URLUtil;
import cn.iocoder.cloud.framework.collector.dispatch.entrance.internal.CollectJobService;
import cn.iocoder.cloud.framework.collector.dispatch.entrance.internal.CollectResponseEventListener;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.collectorplatform.CollectorPlatformDO;
import cn.iocoder.cloud.module.cloudedge.dal.mysql.collectorplatform.CollectorPlatformMapper;
import cn.iocoder.cloud.module.cloudedge.dal.mysql.dao.CollectorDao;
import cn.iocoder.cloud.module.cloudedge.dal.mysql.dao.CollectorMonitorBindDao;
import cn.iocoder.cloud.module.cloudedge.dal.mysql.dao.MonitorDao;
import cn.iocoder.cloud.module.cloudedge.dal.mysql.dao.ParamDao;
import cn.iocoder.cloud.module.cloudedge.scheduler.netty.ManageServer;
import cn.iocoder.cloud.module.cloudedge.service.AppService;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import cn.iocoder.zj.module.report.api.subscription.dto.ReportSubscriptionApi;
import cn.iocoder.zj.module.report.api.subscription.dto.ReportSubscriptionDTO;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import cn.iocoder.zj.module.system.api.wechat.WeChatSendApi;
import com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.StringUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.hertzbeat.common.constants.CommonConstants;
import org.apache.hertzbeat.common.entity.dto.CollectorInfo;
import org.apache.hertzbeat.common.entity.job.Configmap;
import org.apache.hertzbeat.common.entity.job.Job;
import org.apache.hertzbeat.common.entity.manager.*;
import org.apache.hertzbeat.common.entity.message.ClusterMsg;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.apache.hertzbeat.common.util.JsonUtil;
import org.apache.hertzbeat.common.util.SnowFlakeIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * collector service
 *
 * <AUTHOR>
 */
@Component
@AutoConfigureAfter(value = {SchedulerProperties.class})
@Slf4j
public class CollectorJobScheduler implements CollectorScheduling, CollectJobScheduling {

    private final Map<Long, Job> jobContentCache = new ConcurrentHashMap<>(16);

    private final Map<Long, CollectResponseEventListener> eventListeners = new ConcurrentHashMap<>(16);

    @Autowired
    private CollectorDao collectorDao;

    @Autowired
    private CollectorMonitorBindDao collectorMonitorBindDao;

    @Autowired
    private ConsistentHash consistentHash;

    @Resource
    private CollectJobService collectJobService;

    @Autowired
    private AppService appService;

    @Autowired
    private MonitorDao monitorDao;

    @Autowired
    private ParamDao paramDao;

    private ManageServer manageServer;

    @Autowired
    private AlarmConfigApi alarmConfigApi;
    @Autowired
    private ReportSubscriptionApi reportSubscriptionApi;

    @Autowired
    private WeChatSendApi weChatSendApi;

    @Autowired
    private PlatformconfigApi platformconfigApi;
    @Autowired
    CollectorPlatformMapper collectorPlatformMapper;

    @Value("${cloud-domain.url}")
    private String cloudDomain;

    @Override
    public void collectorGoOnline(String identity, CollectorInfo collectorInfo) {
        // 采集器上线以后根据 上线的采集器 找到该采集对应的平台，根据平台去找到离线的采集器下绑定的采集器数据，并批量修改至新上线的采集器下 重新分配
        Optional<Collector> collectorOptional = collectorDao.findCollectorByName(identity);

        Collector collector;
        if (collectorOptional.isPresent()) {
            collector = collectorOptional.get();
            if (collector.getStatus() == CommonConstants.COLLECTOR_STATUS_ONLINE) {
                return;
            }
            collector.setStatus(CommonConstants.COLLECTOR_STATUS_ONLINE);
            if (collectorInfo != null) {
                collector.setIp(collectorInfo.getIp());
                collector.setMode(collectorInfo.getMode());
            }
        } else {
            if (collectorInfo == null) {
                log.error("collectorInfo can not null when collector not existed");
                return;
            }
            collector = Collector.builder().name(identity).ip(collectorInfo.getIp())
                    .mode(collectorInfo.getMode())
                    .status(CommonConstants.COLLECTOR_STATUS_ONLINE).build();
        }
        collectorDao.save(collector);

        AlarmDorisReqDTO collectorAlert = collectorPlatformMapper.selectCollectorAlarmById(collector.getId());
        if (collectorAlert!=null){
            List<AlarmDorisReqDTO> toUpdate = new ArrayList<>();
            collectorAlert.setIsSolved(2);
            collectorAlert.setMonitorId(String.valueOf(collector.getId()));
            collectorAlert.setMonitorName(collector.getName());
            collectorAlert.setLastAlarmTime(new Date().getTime());
            collectorAlert.setGmtUpdate(new Date());
            toUpdate.add(collectorAlert);
            alarmConfigApi.updateAlarmDoris(toUpdate);
        }

        if (collectorOptional.get().getProjectId() != null) {

            // 查詢離綫的平臺且不等於在线的數據
            List<Collector> collectorList = collectorDao.findByProjectIdAndNameNotAndNameNotAndStatusIs(
                    collectorOptional.get().getProjectId(), collectorOptional.get().getName(), "main-default-collector",
                    CommonConstants.COLLECTOR_STATUS_OFFLINE);
            if (!collectorList.isEmpty()) {
                // 查询离线的采集器下有多少任务
                for (Collector collectors : collectorList) {
                    // 查询离线的采集器下绑定的任务，如果有则更新到存活的下
                    List<CollectorMonitorBind> binds = collectorMonitorBindDao
                            .findCollectorMonitorBindsByCollector(collectors.getName());
                    if (!binds.isEmpty()) {
                        // 找到已经离线绑定的任务
                        for (CollectorMonitorBind bind : binds) {
                            Optional<Monitor> monitorOptional = monitorDao.findById(bind.getMonitorId());
                            if (monitorOptional.isPresent()) {
                                Monitor monitor = monitorOptional.get();
                                if (monitor.getStatus() == CommonConstants.MONITOR_PAUSED_CODE) {
                                    continue;
                                }
                                try {
                                    // 删除老的绑定监控数据
                                    collectorMonitorBindDao.deleteCollectorMonitorBindByMonitorId(monitor.getId());

                                    CollectorMonitorBind collectorMonitorBind = CollectorMonitorBind.builder()
                                            .collector(identity).monitorId(monitor.getId())
                                            .build();
                                    collectorMonitorBindDao.save(collectorMonitorBind);
                                } catch (Exception e) {
                                    log.error(
                                            "insert pinned monitor job: {} in collector: {} error,continue next monitor",
                                            monitor, identity, e);
                                }
                            }
                        }
                    }
                }
            }
        }

        ConsistentHash.Node node = new ConsistentHash.Node(identity, collector.getMode(),
                collector.getIp(), System.currentTimeMillis(), null);
        consistentHash.addNode(node);
        // 采集器重新分配
        reBalanceCollectorAssignJobs();
        // 读取数据库此collector下的固定采集任务并下发
        // todo 待修改是否可根据采集器上线以后自动分配
        // 采集器重构，如有问题可从这里恢复
        // todo 这里先查询采集器绑定的设备，根据采集器绑定的其他平台采集器重新分配
        List<CollectorMonitorBind> binds = collectorMonitorBindDao.findCollectorMonitorBindsByCollector(identity);
        if (CollectionUtils.isEmpty(binds)) {
            return;
        }
        List<Monitor> monitors = monitorDao
                .findMonitorsByIdIn(binds.stream().map(CollectorMonitorBind::getMonitorId).collect(Collectors.toSet()));

        for (Monitor monitor : monitors) {
            if (Objects.isNull(monitor) || monitor.getStatus() == CommonConstants.MONITOR_PAUSED_CODE) {
                continue;
            }
            try {
                // build collect job entity
                Job appDefine = appService.getAppDefine(monitor.getApp());
                if (CommonConstants.PROMETHEUS.equals(monitor.getApp())) {
                    appDefine.setApp(CommonConstants.PROMETHEUS_APP_PREFIX + monitor.getName());
                }
                appDefine.setMonitorId(monitor.getId());
                appDefine.setDefaultInterval(monitor.getIntervals());
                appDefine.setCyclic(true);
                appDefine.setTimestamp(System.currentTimeMillis());
                List<Param> params = paramDao.findParamsByMonitorId(monitor.getId());
                List<Configmap> configmaps = params.stream()
                        .map(param -> Configmap.builder()
                                .key(param.getField())
                                .value(param.getValue())
                                .type(param.getType()).build())
                        .collect(Collectors.toList());
                List<ParamDefine> paramDefaultValue = appDefine.getParams().stream()
                        .filter(item -> org.apache.commons.lang3.StringUtils.isNotBlank(item.getDefaultValue()))
                        .toList();
                paramDefaultValue.forEach(defaultVar -> {
                    if (configmaps.stream().noneMatch(item -> item.getKey().equals(defaultVar.getField()))) {
                        configmaps.add(Configmap.builder()
                                .key(defaultVar.getField())
                                .value(defaultVar.getDefaultValue())
                                .type((byte) 1)
                                .build());
                    }
                });
                appDefine.setConfigmap(configmaps);
                long jobId = addAsyncCollectJob(appDefine, identity);
                monitor.setJobId(jobId);
                monitorDao.save(monitor);
            } catch (Exception e) {
                log.error("insert pinned monitor job: {} in collector: {} error,continue next monitor", monitor,
                        identity, e);
            }
        }



    }

    @Override
    @SneakyThrows
    public void collectorGoOffline(String identity) {

        // 采集器重构，如有问题可从这里恢复
        // todo 采集器离线 这里先查询采集器绑定的设备，采集器离线则全部监控管理离线，否则则根据采集器绑定的项目采集器重新分配
        Optional<Collector> collectorOptional = collectorDao.findCollectorByName(identity);
        // 查詢離綫的平臺且不等於離綫的數據
        List<Collector> collectorList = collectorDao.findByProjectIdAndNameNotAndNameNotAndStatusIs(
                collectorOptional.get().getProjectId(), collectorOptional.get().getName(), "main-default-collector",
                CommonConstants.COLLECTOR_STATUS_ONLINE);
        // 查询到同平台下存在采集器并且在线的则分配至该采集器
        if (!collectorList.isEmpty()) {
            // 查询离线的采集器下有多少任务
            List<CollectorMonitorBind> binds = collectorMonitorBindDao.findCollectorMonitorBindsByCollector(identity);
            for (CollectorMonitorBind bind : binds) {
                Optional<Monitor> monitorOptional = monitorDao.findById(bind.getMonitorId());
                if (monitorOptional.isPresent()) {
                    Monitor monitor = monitorOptional.get();
                    if (monitor.getStatus() == CommonConstants.MONITOR_PAUSED_CODE) {
                        continue;
                    }
                    try {
                        // 构造采集任务Job实体
                        Job appDefine = appService.getAppDefine(monitor.getApp());
                        if (CommonConstants.PROMETHEUS.equals(monitor.getApp())) {
                            appDefine.setApp(CommonConstants.PROMETHEUS_APP_PREFIX + monitor.getName());
                        }
                        appDefine.setMonitorId(monitor.getId());
                        appDefine.setDefaultInterval(monitor.getIntervals());
                        appDefine.setCyclic(true);
                        appDefine.setTimestamp(System.currentTimeMillis());
                        List<Param> params = paramDao.findParamsByMonitorId(monitor.getId());
                        List<Configmap> configmaps = params.stream()
                                .map(param -> new Configmap(param.getField(), param.getValue(),
                                        param.getType()))
                                .collect(Collectors.toList());
                        List<ParamDefine> paramDefaultValue = appDefine.getParams().stream()
                                .filter(item -> StringUtils.hasText(item.getDefaultValue()))
                                .collect(Collectors.toList());
                        paramDefaultValue.forEach(defaultVar -> {
                            if (configmaps.stream().noneMatch(item -> item.getKey().equals(defaultVar.getField()))) {
                                // todo type
                                Configmap configmap = new Configmap(defaultVar.getField(), defaultVar.getDefaultValue(),
                                        (byte) 1);
                                configmaps.add(configmap);
                            }
                        });

                        // 删除老的绑定监控数据
                        collectorMonitorBindDao.deleteCollectorMonitorBindByMonitorId(monitor.getId());
                        // 如果集合中存在数据则随机取一条塞入
                        if (!collectorList.isEmpty()) {
                            Random random = new Random();
                            int index = random.nextInt(collectorList.size());
                            Collector randomCollector = collectorList.get(index);
                            appDefine.setConfigmap(configmaps);
                            long jobId = updateAsyncCollectJob(appDefine, randomCollector.getName());
                            monitor.setJobId(jobId);

                            ConsistentHash.Node node = new ConsistentHash.Node(identity, randomCollector.getMode(),
                                    randomCollector.getIp(), System.currentTimeMillis(), null);
                            consistentHash.addNode(node);

                            CollectorMonitorBind collectorMonitorBind = CollectorMonitorBind.builder()
                                    .collector(randomCollector.getName()).monitorId(monitor.getId())
                                    .build();
                            collectorMonitorBindDao.save(collectorMonitorBind);
                        }
                        // 監控恢復可用
                        monitor.setStatus(CommonConstants.MONITOR_UP_CODE);
                        monitorDao.save(monitor);
                    } catch (Exception e) {
                        log.error("insert pinned monitor job: {} in collector: {} error,continue next monitor", monitor,
                                identity, e);
                    }
                }
            }
        } else {
            Collector collector = collectorDao.findCollectorByName(identity).orElse(null);
            if (Objects.isNull(collector)) {
                log.info("the collector : {} not found.", identity);
                return;
            }
            collector.setStatus(CommonConstants.COLLECTOR_STATUS_OFFLINE);
            collectorDao.save(collector);
            consistentHash.removeNode(identity);
            reBalanceCollectorAssignJobs();
            log.info("the collector: {} go offline success.", identity);
        }

        if (collectorOptional.isPresent()) {
            log.info("the collector: {} is going offline now.", identity);
            Collector collector = collectorOptional.get();
            collector.setStatus(CommonConstants.COLLECTOR_STATUS_OFFLINE);
            collector.setOfflineTime(DateUtil.toLocalDateTime(new Date()));
            collectorDao.save(collector);
            consistentHash.removeNode(identity);
            final Long alertId = alarmConfigApi.getMaxAlertId().getData();
            reBalanceCollectorAssignJobs();
            // 采集器离线告警
            // 如果采集器下绑定多个平台取第一个平台进行告警
            CollectorPlatformDO collectorPlatformDOS = collectorPlatformMapper.selectListById(collector.getId());
            List<AlarmDorisReqDTO> toInsert = new ArrayList<>();
            AlarmDorisReqDTO collectorAlert = new AlarmDorisReqDTO();
            collectorAlert.setPriority(0);
            collectorAlert.setStatus(0);
            collectorAlert.setIsSolved(0);
            collectorAlert.setFirstAlarmTime(new Date().getTime());
            collectorAlert.setGmtCreate(new Date());
            collectorAlert.setGmtUpdate(new Date());
            collectorAlert.setLastAlarmTime(new Date().getTime());
            collectorAlert.setMonitorName(collector.getName());
            collectorAlert.setMonitorId(String.valueOf(collector.getId()));
            if (collectorPlatformDOS!=null){
                collectorAlert.setPlatformName(collectorPlatformDOS.getPlatformName());
                collectorAlert.setPlatformId(collectorPlatformDOS.getPlatformId());
            }else {
                collectorAlert.setPlatformName("");
                collectorAlert.setPlatformId(null);
            }
            collectorAlert.setContent(String.format("采集器(%s)已离线！", collector.getName()));
            collectorAlert.setAlarmName(collector.getName());
            collectorAlert.setTimes(1);
            collectorAlert.setResourceType(1);
            collectorAlert.setApp("collector");
            collectorAlert.setAlarmId(0L);
            collectorAlert.setId(alertId + 1);
            toInsert.add(collectorAlert);
            Map<String, List> alertMap = new HashMap<>();
            alertMap.put("insertList", toInsert);
            // 创建方法中需要有updateList，防止空指针异常
            alertMap.put("updateList", new ArrayList<>());
            alarmConfigApi.createAlarmToDoris(alertMap);
            log.info("创建采集器离线告警成功");

            try {
                List<ReportSubscriptionDTO> report = reportSubscriptionApi.getAlarmSubscription().getData();
                if (report != null) {
                    if (ObjectUtil.isNotEmpty(collector.getPlatformId()) && ObjectUtil.isNotEmpty(report)) {
                        for (ReportSubscriptionDTO item : report) {
                            if (item.getPlatformId() != null) {
                                if (item.getPlatformId().contains(Convert.toStr(collector.getPlatformId()))) {
                                    if (item.getWechatState() == 1 && !StringUtil.isNullOrEmpty(item.getOpenId())) {
                                        Map<String, Object> templateParams = new HashMap<>();
                                        // 点击详情跳转的地址
                                        String redictUrl = "";
                                        try {
                                            String encodedUrl = URLEncoder.encode(cloudDomain, "UTF-8");
                                            String parameter = URLUtil
                                                    .encode("pages/alarm/info?id=" + collectorAlert.getId());
                                            redictUrl = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxe3d3d4577c6c41b6&redirect_uri="
                                                    + encodedUrl + "&response_type=code&scope=snsapi_userinfo&state="
                                                    + parameter + "&connect_redirect=1#wechat_redirect";
                                        } catch (UnsupportedEncodingException e) {
                                            System.out.println("Error encoding URL: " + e.getMessage());
                                        }
                                        templateParams.put("monitorName", collector.getName());
                                        templateParams.put("content",
                                                String.format("采集器(%s)已离线！", collector.getName()));
                                        templateParams.put("openId", item.getOpenId());
                                        templateParams.put("url", redictUrl);
                                        templateParams.put("platformName", collector.getPlatformName());
                                        templateParams.put("priority", "严重");
                                        weChatSendApi.sendSingleWeChatToMember(templateParams);
                                    }
                                    if (item.getEmailState() == 1 && !StringUtil.isNullOrEmpty(item.getEmail())) {
                                        Map<String, Object> templateParams = new HashMap<>();
                                        templateParams.put("alarmName", "采集器离线告警");
                                        templateParams.put("productsName", collector.getName());
                                        templateParams.put("value", "-");
                                        templateParams.put("time", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
                                        templateParams.put("app", "collector");
                                        templateParams.put("priority", "严重");
                                        templateParams.put("times", 1);
                                        templateParams.put("content",
                                                String.format("采集器(%s)已离线！", collector.getName()));
                                        templateParams.put("platformName", collector.getPlatformName());
                                        PlatformconfigDTO platformconfigDTO = platformconfigApi
                                                .getByConfigId(collector.getPlatformId()).getCheckedData();
                                        String address1 = StringUtil.isNullOrEmpty(platformconfigDTO.getAddress()) ? "-"
                                                : platformconfigDTO.getAddress();
                                        templateParams.put("address", address1);
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.info("report离线");
            }
        }
    }

    /**
     * @description: 从新分配采集任务
     * @param:
     * @return: void
     * <AUTHOR>
     * @date: 2024/1/17 10:16
     */
    @Override
    public void reBalanceCollectorAssignJobs() {
        consistentHash.getAllNodes().entrySet().parallelStream().forEach(entry -> {
            String collectorName = entry.getKey();
            AssignJobs assignJobs = entry.getValue().getAssignJobs();
            if (org.apache.commons.lang3.StringUtils.isBlank(collectorName) || Objects.isNull(assignJobs)) {
                return;
            }
            if (CollectionUtils.isNotEmpty(assignJobs.getAddingJobs())) {
                Set<Long> addedJobIds = new HashSet<>(8);
                for (Long addingJobId : assignJobs.getAddingJobs()) {
                    Job job = jobContentCache.get(addingJobId);
                    if (Objects.isNull(job)) {
                        log.error("assigning job {} content is null.", addingJobId);
                        continue;
                    }
                    addedJobIds.add(addingJobId);
                    if (CommonConstants.MAIN_COLLECTOR_NODE.equals(collectorName)) {
                        collectJobService.addAsyncCollectJob(job);
                    } else {
                        ClusterMsg.Message message = ClusterMsg.Message.newBuilder()
                                .setDirection(ClusterMsg.Direction.REQUEST)
                                .setType(ClusterMsg.MessageType.ISSUE_CYCLIC_TASK)
                                .setMsg(JsonUtil.toJson(job))
                                .build();
                        this.manageServer.sendMsg(collectorName, message);
                    }
                }
                assignJobs.addAssignJobs(addedJobIds);
                assignJobs.removeAddingJobs(addedJobIds);
            }
            if (CollectionUtils.isNotEmpty(assignJobs.getRemovingJobs())) {
                if (CommonConstants.MAIN_COLLECTOR_NODE.equals(collectorName)) {
                    assignJobs.getRemovingJobs().forEach(jobId -> collectJobService.cancelAsyncCollectJob(jobId));
                } else {
                    ClusterMsg.Message message = ClusterMsg.Message.newBuilder()
                            .setDirection(ClusterMsg.Direction.REQUEST)
                            .setType(ClusterMsg.MessageType.DELETE_CYCLIC_TASK)
                            .setMsg(JsonUtil.toJson(assignJobs.getRemovingJobs()))
                            .build();
                    this.manageServer.sendMsg(collectorName, message);
                }
                assignJobs.clearRemovingJobs();
            }
        });
    }

    @Override
    public boolean offlineCollector(String identity) {
        ClusterMsg.Message message = ClusterMsg.Message.newBuilder()
                .setType(ClusterMsg.MessageType.GO_OFFLINE)
                .setDirection(ClusterMsg.Direction.REQUEST)
                .setIdentity(identity)
                .build();
        ClusterMsg.Message response = this.manageServer.sendMsgSync(identity, message);
        if (response == null || !String.valueOf(CommonConstants.SUCCESS_CODE).equals(response.getMsg())) {
            return false;
        }
        log.info("send offline collector message to {} success", identity);
        this.collectorGoOffline(identity);
        return true;
    }

    @Override
    public boolean onlineCollector(String identity) {
        Collector collector = collectorDao.findCollectorByName(identity).orElse(null);
        if (Objects.isNull(collector)) {
            return false;
        }
        ClusterMsg.Message message = ClusterMsg.Message.newBuilder()
                .setType(ClusterMsg.MessageType.GO_ONLINE)
                .setDirection(ClusterMsg.Direction.REQUEST)
                .setIdentity(identity)
                .build();
        ClusterMsg.Message response = this.manageServer.sendMsgSync(identity, message);
        if (response == null || !String.valueOf(CommonConstants.SUCCESS_CODE).equals(response.getMsg())) {
            return false;
        }
        log.info("send online collector message to {} success", identity);
        CollectorInfo collectorInfo = CollectorInfo.builder()
                .name(collector.getName())
                .ip(collector.getIp())
                .version(collector.getVersion())
                .mode(collector.getMode())
                .build();
        this.collectorGoOnline(identity, collectorInfo);
        return true;
    }

    @Override
    public boolean getMetricsCollector(String identity, Map<String, Object> map) {
        Collector collector = collectorDao.findCollectorByName(identity).orElse(null);
        if (Objects.isNull(collector)) {
            return false;
        }
        double cpuUsage = Double.parseDouble(map.get("cpuUsage").toString());
        double memUsage = Double.parseDouble(map.get("memUsage").toString());
        double rxMbps = Double.parseDouble(map.get("rxMbps").toString());
        double txMbps = Double.parseDouble(map.get("txMbps").toString());

        collector.setCpuUsage(cpuUsage);
        collector.setMemUsage(memUsage);
        collector.setRxMbps(rxMbps);
        collector.setTxMbps(txMbps);
        collectorDao.save(collector);
        return true;
    }

    @Override
    public List<CollectRep.MetricsData> collectSyncJobData(Job job) {
        // todo dispatchKey ip+port or id
        String dispatchKey = String.valueOf(job.getMonitorId());
        ConsistentHash.Node node = consistentHash.preDispatchJob(dispatchKey);
        if (Objects.isNull(node)) {
            log.error("there is no collector online to assign job.");
            CollectRep.MetricsData metricsData = CollectRep.MetricsData.newBuilder()
                    .setCode(CollectRep.Code.FAIL)
                    .setMsg("no collector online to assign job")
                    .build();
            return Collections.singletonList(metricsData);
        }
        if (CommonConstants.MAIN_COLLECTOR_NODE.equals(node.getIdentity())) {
            return collectJobService.collectSyncJobData(job);
        } else {
            List<CollectRep.MetricsData> metricsData = new LinkedList<>();
            CountDownLatch countDownLatch = new CountDownLatch(1);

            ClusterMsg.Message message = ClusterMsg.Message.newBuilder()
                    .setType(ClusterMsg.MessageType.ISSUE_ONE_TIME_TASK)
                    .setDirection(ClusterMsg.Direction.REQUEST)
                    .setMsg(JsonUtil.toJson(job))
                    .build();
            boolean result = this.manageServer.sendMsg(node.getIdentity(), message);

            if (result) {
                CollectResponseEventListener listener = new CollectResponseEventListener() {
                    @Override
                    public void response(List<CollectRep.MetricsData> responseMetrics) {
                        if (responseMetrics != null) {
                            metricsData.addAll(responseMetrics);
                        }
                        countDownLatch.countDown();
                    }
                };
                eventListeners.put(job.getMonitorId(), listener);
            }
            try {
                countDownLatch.await(120, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.info("The sync task runs for 120 seconds with no response and returns");
            }
            return metricsData;
        }
    }

    @Override
    public List<CollectRep.MetricsData> collectSyncJobData(Job job, String collector) {
        ConsistentHash.Node node = consistentHash.getNode(collector);
        if (Objects.isNull(node)) {
            log.error("there is no collector online to assign job.");
            CollectRep.MetricsData metricsData = CollectRep.MetricsData.newBuilder()
                    .setCode(CollectRep.Code.FAIL)
                    .setMsg("the collector is offline and cannot assign job")
                    .build();
            return Collections.singletonList(metricsData);
        }
        if (CommonConstants.MAIN_COLLECTOR_NODE.equals(node.getIdentity())) {
            return collectJobService.collectSyncJobData(job);
        }
        List<CollectRep.MetricsData> metricsData = new LinkedList<>();
        ClusterMsg.Message message = ClusterMsg.Message.newBuilder()
                .setType(ClusterMsg.MessageType.ISSUE_ONE_TIME_TASK)
                .setDirection(ClusterMsg.Direction.REQUEST)
                .setMsg(JsonUtil.toJson(job))
                .build();
        boolean result = this.manageServer.sendMsg(node.getIdentity(), message);
        if (result) {
            CountDownLatch countDownLatch = new CountDownLatch(1);
            CollectResponseEventListener listener = new CollectResponseEventListener() {
                @Override
                public void response(List<CollectRep.MetricsData> responseMetrics) {
                    if (responseMetrics != null) {
                        metricsData.addAll(responseMetrics);
                    }
                    countDownLatch.countDown();
                }
            };
            eventListeners.put(job.getMonitorId(), listener);
            try {
                countDownLatch.await(120, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.info("The sync task runs for 120 seconds with no response and returns");
            }
        }
        return metricsData;
    }

    @Override
    public long addAsyncCollectJob(Job job, String collector) {
        long jobId = SnowFlakeIdGenerator.generateId();
        job.setId(jobId);
        jobContentCache.put(jobId, job);
        ConsistentHash.Node node;
        if (org.apache.commons.lang3.StringUtils.isBlank(collector)) {
            // todo dispatchKey ip+port or id
            String dispatchKey = String.valueOf(job.getMonitorId());
            node = consistentHash.dispatchJob(dispatchKey, jobId);
            if (node == null) {
                log.error("there is no collector online to assign job.");
                return jobId;
            }
        } else {
            node = consistentHash.getNode(collector);
            if (node == null) {
                log.error("there is no collector name: {} online to assign job.", collector);
                return jobId;
            }
            node.getAssignJobs().addPinnedJob(jobId);
        }
        if (CommonConstants.MAIN_COLLECTOR_NODE.equals(node.getIdentity())) {
            collectJobService.addAsyncCollectJob(job);
        } else {
            ClusterMsg.Message message = ClusterMsg.Message.newBuilder()
                    .setType(ClusterMsg.MessageType.ISSUE_CYCLIC_TASK)
                    .setDirection(ClusterMsg.Direction.REQUEST)
                    .setMsg(JsonUtil.toJson(job))
                    .build();
            this.manageServer.sendMsg(node.getIdentity(), message);
        }
        return jobId;
    }

    @Override
    public long updateAsyncCollectJob(Job modifyJob) {
        // delete and add
        long preJobId = modifyJob.getId();
        long newJobId = addAsyncCollectJob(modifyJob, null);
        jobContentCache.remove(preJobId);
        cancelAsyncCollectJob(preJobId);
        return newJobId;
    }

    @Override
    public long updateAsyncCollectJob(Job modifyJob, String collector) {
        // delete and add
        long preJobId = modifyJob.getId();
        long newJobId = addAsyncCollectJob(modifyJob, collector);
        jobContentCache.remove(preJobId);
        cancelAsyncCollectJob(preJobId);
        return newJobId;
    }

    @Override
    public void cancelAsyncCollectJob(Long jobId) {
        if (jobId == null) {
            return;
        }
        for (ConsistentHash.Node node : consistentHash.getAllNodes().values()) {
            AssignJobs assignJobs = node.getAssignJobs();
            if (assignJobs.getPinnedJobs().remove(jobId)
                    || assignJobs.getJobs().remove(jobId) || assignJobs.getAddingJobs().remove(jobId)) {
                node.removeVirtualNodeJob(jobId);
                if (CommonConstants.MAIN_COLLECTOR_NODE.equals(node.getIdentity())) {
                    collectJobService.cancelAsyncCollectJob(jobId);
                } else {
                    ClusterMsg.Message deleteMessage = ClusterMsg.Message.newBuilder()
                            .setType(ClusterMsg.MessageType.DELETE_CYCLIC_TASK)
                            .setDirection(ClusterMsg.Direction.REQUEST)
                            .setMsg(JsonUtil.toJson(List.of(jobId)))
                            .build();
                    this.manageServer.sendMsg(node.getIdentity(), deleteMessage);
                }
                break;
            }
        }
    }

    @Override
    public void collectSyncJobResponse(List<CollectRep.MetricsData> metricsDataList) {
        if (CollectionUtils.isEmpty(metricsDataList)) {
            return;
        }
        CollectRep.MetricsData metricsData = metricsDataList.get(0);
        long monitorId = metricsData.getId();
        CollectResponseEventListener eventListener = eventListeners.remove(monitorId);
        if (Objects.nonNull(eventListener)) {
            eventListener.response(metricsDataList);
        }
    }

    public void setManageServer(ManageServer manageServer) {
        this.manageServer = manageServer;
    }

}
