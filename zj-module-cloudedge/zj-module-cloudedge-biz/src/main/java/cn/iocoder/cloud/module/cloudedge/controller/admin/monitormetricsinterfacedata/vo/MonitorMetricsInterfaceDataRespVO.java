package cn.iocoder.cloud.module.cloudedge.controller.admin.monitormetricsinterfacedata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Schema(description = "管理后台 - 监控实时数据 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MonitorMetricsInterfaceDataRespVO extends MonitorMetricsInterfaceDataBaseVO {

    @Schema(description = "id", required = true)
    private Long id;

}
