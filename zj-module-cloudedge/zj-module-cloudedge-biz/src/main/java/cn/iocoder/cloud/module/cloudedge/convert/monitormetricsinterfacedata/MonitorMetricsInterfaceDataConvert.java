package cn.iocoder.cloud.module.cloudedge.convert.monitormetricsinterfacedata;

import java.util.*;

import cn.iocoder.cloud.module.cloudedge.controller.admin.monitormetricsinterfacedata.vo.MonitorMetricsInterfaceDataCreateReqVO;
import cn.iocoder.cloud.module.cloudedge.controller.admin.monitormetricsinterfacedata.vo.MonitorMetricsInterfaceDataExcelVO;
import cn.iocoder.cloud.module.cloudedge.controller.admin.monitormetricsinterfacedata.vo.MonitorMetricsInterfaceDataRespVO;
import cn.iocoder.cloud.module.cloudedge.controller.admin.monitormetricsinterfacedata.vo.MonitorMetricsInterfaceDataUpdateReqVO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.monitormetricsinterfacedata.MonitorMetricsInterfaceDataDO;

/**
 * 监控实时数据 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface MonitorMetricsInterfaceDataConvert {

    MonitorMetricsInterfaceDataConvert INSTANCE = Mappers.getMapper(MonitorMetricsInterfaceDataConvert.class);

    MonitorMetricsInterfaceDataDO convert(MonitorMetricsInterfaceDataCreateReqVO bean);

    MonitorMetricsInterfaceDataDO convert(MonitorMetricsInterfaceDataUpdateReqVO bean);

    MonitorMetricsInterfaceDataRespVO convert(MonitorMetricsInterfaceDataDO bean);

    List<MonitorMetricsInterfaceDataRespVO> convertList(List<MonitorMetricsInterfaceDataDO> list);

    PageResult<MonitorMetricsInterfaceDataRespVO> convertPage(PageResult<MonitorMetricsInterfaceDataDO> page);

    List<MonitorMetricsInterfaceDataExcelVO> convertList02(List<MonitorMetricsInterfaceDataDO> list);

}
