package cn.iocoder.cloud.module.cloudedge.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.util.export.YamlExportUtils;
import org.springframework.stereotype.Service;
import org.yaml.snakeyaml.Yaml;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;

import static org.apache.hertzbeat.common.constants.ExportFileConstants.YamlFile.FILE_SUFFIX;
import static org.apache.hertzbeat.common.constants.ExportFileConstants.YamlFile.TYPE;

/**
 * Configure the import and export Yaml format
 * 配置导入导出 Yaml格式
 *
 * <AUTHOR> href="mailto:<EMAIL>">zqr10159</a>
 * Created by zqr10159 on 2023/4/20
 */
@Slf4j
@Service
public class YamlImExportServiceImpl extends AbstractImExportServiceImpl {
    /**
     * Export file type
     * @return file type
     */
    @Override
    public String type() {
        return TYPE;
    }

    /**
     * Get Export File Name
     * @return file name
     */
    @Override
    public String getFileName() {
        return fileNamePrefix() + FILE_SUFFIX;
    }

    /**
     * Parsing an input stream into a form
     * @param is input stream
     * @return form
     */
    @Override
    public List<ExportMonitorDTO> parseImport(InputStream is) {
        // todo now disable this, will enable it in the future.
        // upgrade to snakeyaml 2.2 and springboot3.x to fix the issue
        Yaml yaml = new Yaml();
        return yaml.load(is);
    }

    /**
     * Export Configuration to Output Stream
     * @param monitorList configuration list
     * @param os          output stream
     */
    @Override
    public void writeOs(List<ExportMonitorDTO> monitorList, OutputStream os) {

        YamlExportUtils.exportWriteOs(monitorList, os);
    }

}
