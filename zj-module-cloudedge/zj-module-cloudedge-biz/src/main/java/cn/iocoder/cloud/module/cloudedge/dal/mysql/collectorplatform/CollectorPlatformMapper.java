package cn.iocoder.cloud.module.cloudedge.dal.mysql.collectorplatform;

import cn.iocoder.cloud.module.cloudedge.api.monitor.dto.AlarmReqDTO;
import cn.iocoder.cloud.module.cloudedge.controller.admin.AppController;
import cn.iocoder.cloud.module.cloudedge.controller.admin.collectorplatform.vo.CollectorPlatformExportReqVO;
import cn.iocoder.cloud.module.cloudedge.controller.admin.collectorplatform.vo.CollectorPlatformPageReqVO;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.collectorplatform.CollectPlatfDTO;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.collectorplatform.CollectorPlatformDO;
import cn.iocoder.zj.framework.common.enums.ResourceAppEnum;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import com.baomidou.dynamic.datasource.annotation.Slave;
import org.apache.hertzbeat.common.entity.manager.Collector;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * 采集器平台关联 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CollectorPlatformMapper extends BaseMapperX<CollectorPlatformDO> {

    default PageResult<CollectorPlatformDO> selectPage(CollectorPlatformPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<CollectorPlatformDO>()
                .eqIfPresent(CollectorPlatformDO::getCollectorId, reqVO.getCollectorId())
                .likeIfPresent(CollectorPlatformDO::getCollectorName, reqVO.getCollectorName())
                .eqIfPresent(CollectorPlatformDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(CollectorPlatformDO::getPlatformName, reqVO.getPlatformName())
                .eqIfPresent(CollectorPlatformDO::getStatus, reqVO.getStatus())
                .orderByDesc(CollectorPlatformDO::getId));
    }

    default List<CollectorPlatformDO> selectList(CollectorPlatformExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<CollectorPlatformDO>()
                .eqIfPresent(CollectorPlatformDO::getCollectorId, reqVO.getCollectorId())
                .likeIfPresent(CollectorPlatformDO::getCollectorName, reqVO.getCollectorName())
                .eqIfPresent(CollectorPlatformDO::getPlatformId, reqVO.getPlatformId())
                .likeIfPresent(CollectorPlatformDO::getPlatformName, reqVO.getPlatformName())
                .eqIfPresent(CollectorPlatformDO::getStatus, reqVO.getStatus())
                .orderByDesc(CollectorPlatformDO::getId));
    }

    default void deleteByName(@Param("collector") String collector){
        delete(new LambdaQueryWrapperX<CollectorPlatformDO>()
                .eqIfPresent(CollectorPlatformDO::getCollectorName,collector));
    }

    Collector getCollectorInfo(@Param("collectorId") String collectorId);

    Collector getCollectorInfoByName(@Param("name") String name);


    void updateCollectorById(@Param("id") Long id, @Param("platformId") Long platformId, @Param("platformName") String platformName);
    @TenantIgnore
    List<Map> collectorBySelect(@Param("platform") List<Map> platform);

    List<Map> getTotalType(@Param("platformId") Long platformId, @Param("app") String app, @Param("subEnums") List<ResourceAppEnum> subEnums);

    Long getCollectType(@Param("id") String id);

    List<Map> getTotalTypeByPlatform(@Param("platform") List<Map> platform, @Param("app") String app, @Param("subEnums") List<ResourceAppEnum> subEnums);

    String getMonitorProtocol(@Param("monitorId") String monitorId);

    List<Map> getMonitorTopology();

    void updateCollectorByProjectId(@Param("id") Long id, @Param("projectId") Long projectId, @Param("projectName") String projectName);

    @TenantIgnore
    List<CollectorPlatformDO> selectListByIds(@Param("platformId") Long platformId);

    @TenantIgnore
    List<Map> selectListByTenantId(@Param("tenantId") Long tenantId);

    @TenantIgnore
    CollectorPlatformDO selectListById(@Param("id") Long id);

    @TenantIgnore
    void updateScanRemark(@Param("ids") List<Long> ids);

    @TenantIgnore
    List<CollectPlatfDTO> getPlatforms(@Param("sourceIds") List<Long> sourceIds);


    @Slave
    AlarmDorisReqDTO selectCollectorAlarmById(@Param("monitorId") Long monitorId);

    @TenantIgnore
    @Insert("INSERT INTO report_metrics (metric_name, metric_code, pid, level,name_value,code_value) VALUES (#{metricName}, #{metricCode}, #{pid}, #{level}, #{nameValue}, #{codeValue})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void saveReportMetrics(AppController.ReportMetrics reportMetrics);

    @TenantIgnore
    @MapKey("label")
    List<Map<String,String>> getDicts();
    @TenantIgnore
    void saveMetirc(@Param("list")List<String> strings);

    @Slave
    AlarmReqDTO getAlarmByMonitorId(@Param("monitorId") Long monitorId);
}
