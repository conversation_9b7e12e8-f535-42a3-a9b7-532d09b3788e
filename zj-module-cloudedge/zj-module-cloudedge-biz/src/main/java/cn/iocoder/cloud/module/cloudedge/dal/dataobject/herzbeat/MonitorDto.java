/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.module.cloudedge.dal.dataobject.herzbeat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.hertzbeat.common.entity.grafana.GrafanaDashboard;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.apache.hertzbeat.common.entity.manager.Param;

import javax.persistence.Transient;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

import static io.swagger.v3.oas.annotations.media.Schema.AccessMode.READ_ONLY;
import static io.swagger.v3.oas.annotations.media.Schema.AccessMode.READ_WRITE;

/**
 * Monitoring Information External Interaction Entities
 * <AUTHOR>
 */
@Data
@Schema(description = "Monitoring information entities | 监控信息实体")
public class MonitorDto {
    
    @Schema(description = "Monitor Content", accessMode = READ_WRITE)
    @NotNull
    @Valid
    private Monitor monitor;
    
    @Schema(description = "Monitor Params", accessMode = READ_WRITE)
    @NotNull
    @Valid
    private List<Param> params;
    
    @Schema(description = "Monitor Metrics", accessMode = READ_ONLY)
    private List<String> metrics;
    
    @Schema(description = "Whether to Detect", accessMode = READ_WRITE)
    private boolean detected;
    
    /**
     * which collector this monitoring want to pin
     */
    @Schema(description = "pinned collector, default null if system dispatch", accessMode = READ_WRITE)
    private String collector;

    @Schema(description = "grafana dashboard")
    private GrafanaDashboard grafanaDashboard;

    @Schema(description = "ip列表", accessMode = READ_WRITE)
    private List<String> ips;

    @Schema(description = "来源ids")
    private List<Long> sourceIds;

    @Schema(title = "标签id",example = "1,2,3,4,5")
    @Transient
    private String monitorTagIds;
}
