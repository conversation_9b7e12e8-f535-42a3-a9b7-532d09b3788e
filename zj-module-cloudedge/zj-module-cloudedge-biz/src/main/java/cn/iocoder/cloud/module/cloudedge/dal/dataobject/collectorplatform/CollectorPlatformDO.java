package cn.iocoder.cloud.module.cloudedge.dal.dataobject.collectorplatform;

import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 采集器平台关联 DO
 *
 * <AUTHOR>
 */
@TableName("hzb_collector_platform")
@KeySequence("hzb_collector_platform_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CollectorPlatformDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 采集器id
     */
    private Long collectorId;
    /**
     * 采集器名称
     */
    private String collectorName;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;
    /**
     * 存活状态
     */
    private Byte status;

    /**
     * 平台id
     */
    private Long projectId;
    /**
     * 平台名称
     */
    private String projectName;

}
