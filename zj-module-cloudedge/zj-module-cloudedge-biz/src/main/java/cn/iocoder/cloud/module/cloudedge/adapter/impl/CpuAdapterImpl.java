package cn.iocoder.cloud.module.cloudedge.adapter.impl;

import cn.iocoder.cloud.module.cloudedge.adapter.AbstractMonitorAdapter;
import cn.iocoder.cloud.module.cloudedge.adapter.MonitorType;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

/**
 * CPU监控适配器实现
 */
@Slf4j
@Service
public class CpuAdapterImpl extends AbstractMonitorAdapter {

    @Override
    public MonitorType getMonitorType() {
        return MonitorType.CPU;
    }

    @Override
    public Map<String, List<Map<String, Object>>> handleMonitorTop10MetricsData(CollectRep.MetricsData metricsData) {


        Optional<String> usage = getFieldValue(metricsData, "usage");


        return buildTop10MetricsData(metricsData,"usage");
    }

    @Override
    protected void processMetricsData(Monitor monitor, CollectRep.MetricsData metricsData) {
        if (metricsData.getApp().equals("windows")) {
            AtomicReference<Double> cpuUsed = new AtomicReference<>(0.0);
            int cpuUsedIndex = getFieldIndex(metricsData, "cpuUsage");
            metricsData.getValuesList().forEach(metrics -> {
                // 根据cupUsedIndex获取cpuUsage
                cpuUsed.updateAndGet(v -> v + Double.parseDouble(metrics.getColumns(cpuUsedIndex)));
            });
            // 计算平均CPU使用率
            if (cpuUsed.get() == 0.0) {
                monitor.setCpuUsed("0");
            } else {
                monitor.setCpuUsed(String.valueOf(cpuUsed.get() / metricsData.getValuesCount()));
            }
        } else {
            // 获取CPU使用率，支持多种字段名称
            getFirstMatchingFieldValue(metricsData, "cpu_used", "usage", "cpuUsage")
                    .ifPresent(monitor::setCpuUsed);
        }
    }
}
