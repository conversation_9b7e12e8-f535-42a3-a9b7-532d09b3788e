package cn.iocoder.cloud.module.cloudedge.controller.admin.alertdefinetemplate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 多维告警配置模板导入到模板 Request VO")
@Data
public class AlertDefineTemplateImportTemplateReqVO {
    @Schema(description = "告警配置模板ID列表" , required = true)
    @NotNull(message = "告警配置模板ID列表不能为空")
    @NotEmpty(message = "告警配置模板ID列表不能为空")
    List<Long> ids;
}
