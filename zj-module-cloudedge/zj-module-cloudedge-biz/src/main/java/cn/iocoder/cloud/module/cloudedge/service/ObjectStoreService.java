package cn.iocoder.cloud.module.cloudedge.service;

import cn.iocoder.cloud.module.cloudedge.dal.dataobject.herzbeat.FileDTO;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.herzbeat.ObjectStoreDTO;

import java.io.InputStream;
import java.util.List;

/**
 * 文件存储服务
 *
 * <AUTHOR> href="mailto:<EMAIL>">gcdd1993</a>
 * Created by gcdd1993 on 2023/9/13
 */
public interface ObjectStoreService {

    /**
     * save file
     * @param filePath file path，eg：hertzbeat/111.json
     * @param is       input stream
     */
    boolean upload(String filePath, InputStream is);

    /**
     * remove file
     * @param filePath file path，eg：hertzbeat/111.json
     */
    void remove(String filePath);

    /**
     * whether the file exists
     *
     * @param filePath file path，eg：hertzbeat/111.json
     */
    boolean isExist(String filePath);

    /**
     * read file
     * @param filePath file path，eg：hertzbeat/111.json
     * @return file
     */
    FileDTO download(String filePath);

    /**
     * Enumeration file
     * @param dir file catalog
     * @return file list
     */
    List<FileDTO> list(String dir);

    ObjectStoreDTO.Type type();

}
