package cn.iocoder.cloud.module.cloudedge.controller.admin.collectorplatform.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 采集器平台关联创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CollectorPlatformCreateReqVO extends CollectorPlatformBaseVO {

}
