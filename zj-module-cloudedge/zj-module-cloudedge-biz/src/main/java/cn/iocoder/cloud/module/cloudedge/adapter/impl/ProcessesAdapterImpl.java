package cn.iocoder.cloud.module.cloudedge.adapter.impl;

import cn.iocoder.cloud.framework.warehouse.store.realtime.RealTimeDataReader;
import cn.iocoder.cloud.module.cloudedge.adapter.AbstractMonitorAdapter;
import cn.iocoder.cloud.module.cloudedge.adapter.MonitorType;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class ProcessesAdapterImpl extends AbstractMonitorAdapter {
    @Override
    public MonitorType getMonitorType() {
        return MonitorType.PROCESSES;
    }
    @Override
    public Map<String, List<Map<String, Object>>> handleMonitorTop10MetricsData(CollectRep.MetricsData metricsData) {
        return buildTop10MetricsData(metricsData, "hrSWRunName", "hrSWRunPerfCPU", "hrSWRunPerfMem");
    }

    @Override
    protected void processMetricsData(Monitor monitor, CollectRep.MetricsData metricsData) {
        return;
    }
}
