package cn.iocoder.cloud.module.cloudedge.controller.admin.monitormetricsinterfacedata.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 监控实时数据分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MonitorMetricsInterfaceDataPageReqVO extends PageParam {

    @Schema(description = "监控id")
    private Long monitorId;

    @Schema(description = "指标")
    private String metrics;

    @Schema(description = "interfaceIndex")
    private String interfaceIndex;

    @Schema(description = "接口名称")
    private String interfaceName;

    @Schema(description = "接口描述")
    private String alias;

    @Schema(description = "管理口状态")
    private String adminStatus;

    @Schema(description = "操作口状态")
    private String operStatus;

}
