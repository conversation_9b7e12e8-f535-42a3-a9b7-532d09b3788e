package cn.iocoder.cloud.module.cloudedge.controller.admin;

import cn.iocoder.cloud.module.cloudedge.controller.admin.monitormetricsinterfacedata.vo.*;
import cn.iocoder.cloud.module.cloudedge.dal.mysql.monitormetricsinterfacedata.MonitorMetricsInterfaceDataMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;

import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.cloud.module.cloudedge.dal.dataobject.monitormetricsinterfacedata.MonitorMetricsInterfaceDataDO;
import cn.iocoder.cloud.module.cloudedge.convert.monitormetricsinterfacedata.MonitorMetricsInterfaceDataConvert;
import cn.iocoder.cloud.module.cloudedge.service.monitormetricsinterfacedata.MonitorMetricsInterfaceDataService;

@Tag(name = "管理后台 - 监控实时数据")
@RestController
@RequestMapping("/cloudedge/api/monitor-metrics-interface-data")
@Validated
public class MonitorMetricsInterfaceDataController {

    @Resource
    private MonitorMetricsInterfaceDataService monitorMetricsInterfaceDataService;
    @Resource
    private MonitorMetricsInterfaceDataMapper monitorMetricsInterfaceDataMapper;

    @PostMapping("/create")
    @Operation(summary = "创建监控实时数据")
//    @PreAuthorize("@ss.hasPermission('cloudedge:monitor-metrics-interface-data:create')")
    public CommonResult<Long> createMonitorMetricsInterfaceData(@Valid @RequestBody MonitorMetricsInterfaceDataCreateReqVO createReqVO) {
        return success(monitorMetricsInterfaceDataService.createMonitorMetricsInterfaceData(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新监控实时数据")
//    @PreAuthorize("@ss.hasPermission('cloudedge:monitor-metrics-interface-data:update')")
    public CommonResult<Boolean> updateMonitorMetricsInterfaceData(@Valid @RequestBody MonitorMetricsInterfaceDataUpdateReqVO updateReqVO) {
        MonitorMetricsInterfaceDataDO find = monitorMetricsInterfaceDataMapper.selectOne(
                new LambdaQueryWrapper<>(MonitorMetricsInterfaceDataDO.class)
                        .eq(MonitorMetricsInterfaceDataDO::getMonitorId, updateReqVO.getMonitorId())
                        .eq(MonitorMetricsInterfaceDataDO::getMetrics, updateReqVO.getMetrics())
                        .eq(MonitorMetricsInterfaceDataDO::getInterfaceIndex, updateReqVO.getInterfaceIndex())
        );
        if (find !=  null) {
            find.setAlias(updateReqVO.getAlias());
            monitorMetricsInterfaceDataMapper.updateById(find);
        }else {
            find = new MonitorMetricsInterfaceDataDO();
            find.setMonitorId(updateReqVO.getMonitorId());
            find.setMetrics(updateReqVO.getMetrics());
            find.setInterfaceIndex(updateReqVO.getInterfaceIndex());
            find.setAlias(updateReqVO.getAlias());
            monitorMetricsInterfaceDataMapper.insert(find);
        }
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除监控实时数据")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('cloudedge:monitor-metrics-interface-data:delete')")
    public CommonResult<Boolean> deleteMonitorMetricsInterfaceData(@RequestParam("id") Long id) {
        monitorMetricsInterfaceDataService.deleteMonitorMetricsInterfaceData(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得监控实时数据")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('cloudedge:monitor-metrics-interface-data:query')")
    public CommonResult<MonitorMetricsInterfaceDataRespVO> getMonitorMetricsInterfaceData(@RequestParam("id") Long id) {
        MonitorMetricsInterfaceDataDO monitorMetricsInterfaceData = monitorMetricsInterfaceDataService.getMonitorMetricsInterfaceData(id);
        return success(MonitorMetricsInterfaceDataConvert.INSTANCE.convert(monitorMetricsInterfaceData));
    }

    @GetMapping("/list")
    @Operation(summary = "获得监控实时数据列表")
//    @PreAuthorize("@ss.hasPermission('cloudedge:monitor-metrics-interface-data:query')")
    public CommonResult<List<MonitorMetricsInterfaceDataRespVO>> getMonitorMetricsInterfaceDataList(@RequestParam("monitorId") Long monitorId) {
        List<MonitorMetricsInterfaceDataDO> list = monitorMetricsInterfaceDataMapper.selectList(
                new LambdaQueryWrapper<>(MonitorMetricsInterfaceDataDO.class)
                        .eq(MonitorMetricsInterfaceDataDO::getMonitorId, monitorId)
                        .orderByAsc(MonitorMetricsInterfaceDataDO::getInterfaceIndex)
        );return success(MonitorMetricsInterfaceDataConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得监控实时数据分页")
//    @PreAuthorize("@ss.hasPermission('cloudedge:monitor-metrics-interface-data:query')")
    public CommonResult<PageResult<MonitorMetricsInterfaceDataRespVO>> getMonitorMetricsInterfaceDataPage(@Valid MonitorMetricsInterfaceDataPageReqVO pageVO) {
        PageResult<MonitorMetricsInterfaceDataDO> pageResult = monitorMetricsInterfaceDataService.getMonitorMetricsInterfaceDataPage(pageVO);
        return success(MonitorMetricsInterfaceDataConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出监控实时数据 Excel")
//    @PreAuthorize("@ss.hasPermission('cloudedge:monitor-metrics-interface-data:export')")
    @OperateLog(type = EXPORT)
    public void exportMonitorMetricsInterfaceDataExcel(@Valid MonitorMetricsInterfaceDataExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<MonitorMetricsInterfaceDataDO> list = monitorMetricsInterfaceDataService.getMonitorMetricsInterfaceDataList(exportReqVO);
        // 导出 Excel
        List<MonitorMetricsInterfaceDataExcelVO> datas = MonitorMetricsInterfaceDataConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "监控实时数据.xls", "数据", MonitorMetricsInterfaceDataExcelVO.class, datas);
    }

}
