package cn.iocoder.cloud.module.cloudedge.controller.admin.collectorplatform.vo;

import cn.iocoder.zj.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 采集器平台关联分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CollectorPlatformPageReqVO extends PageParam {

    @Schema(description = "采集器id")
    private Long collectorId;

    @Schema(description = "采集器名称")
    private String collectorName;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "存活状态")
    private Byte status;

}
