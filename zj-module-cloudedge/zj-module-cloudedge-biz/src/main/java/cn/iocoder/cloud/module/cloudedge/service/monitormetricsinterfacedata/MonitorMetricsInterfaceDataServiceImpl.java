package cn.iocoder.cloud.module.cloudedge.service.monitormetricsinterfacedata;

import cn.iocoder.cloud.module.cloudedge.controller.admin.monitormetricsinterfacedata.vo.MonitorMetricsInterfaceDataCreateReqVO;
import cn.iocoder.cloud.module.cloudedge.controller.admin.monitormetricsinterfacedata.vo.MonitorMetricsInterfaceDataExportReqVO;
import cn.iocoder.cloud.module.cloudedge.controller.admin.monitormetricsinterfacedata.vo.MonitorMetricsInterfaceDataPageReqVO;
import cn.iocoder.cloud.module.cloudedge.controller.admin.monitormetricsinterfacedata.vo.MonitorMetricsInterfaceDataUpdateReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import cn.iocoder.cloud.module.cloudedge.dal.dataobject.monitormetricsinterfacedata.MonitorMetricsInterfaceDataDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.cloud.module.cloudedge.convert.monitormetricsinterfacedata.MonitorMetricsInterfaceDataConvert;
import cn.iocoder.cloud.module.cloudedge.dal.mysql.monitormetricsinterfacedata.MonitorMetricsInterfaceDataMapper;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;


/**
 * 监控实时数据 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MonitorMetricsInterfaceDataServiceImpl implements MonitorMetricsInterfaceDataService {

    @Resource
    private MonitorMetricsInterfaceDataMapper monitorMetricsInterfaceDataMapper;

    @Override
    public Long createMonitorMetricsInterfaceData(MonitorMetricsInterfaceDataCreateReqVO createReqVO) {
        // 插入
        MonitorMetricsInterfaceDataDO monitorMetricsInterfaceData = MonitorMetricsInterfaceDataConvert.INSTANCE.convert(createReqVO);
        monitorMetricsInterfaceDataMapper.insert(monitorMetricsInterfaceData);
        // 返回
        return monitorMetricsInterfaceData.getId();
    }

    @Override
    public void updateMonitorMetricsInterfaceData(MonitorMetricsInterfaceDataUpdateReqVO updateReqVO) {
        // 校验存在
//        validateMonitorMetricsInterfaceDataExists(updateReqVO.getId());
        // 更新
        MonitorMetricsInterfaceDataDO updateObj = MonitorMetricsInterfaceDataConvert.INSTANCE.convert(updateReqVO);
        monitorMetricsInterfaceDataMapper.updateById(updateObj);
    }

    @Override
    public void deleteMonitorMetricsInterfaceData(Long id) {
        // 校验存在
        validateMonitorMetricsInterfaceDataExists(id);
        // 删除
        monitorMetricsInterfaceDataMapper.deleteById(id);
    }

    private void validateMonitorMetricsInterfaceDataExists(Long id) {
        if (monitorMetricsInterfaceDataMapper.selectById(id) == null) {
            return;
        }
    }

    @Override
    public MonitorMetricsInterfaceDataDO getMonitorMetricsInterfaceData(Long id) {
        return monitorMetricsInterfaceDataMapper.selectById(id);
    }

    @Override
    public List<MonitorMetricsInterfaceDataDO> getMonitorMetricsInterfaceDataList(Collection<Long> ids) {
        return monitorMetricsInterfaceDataMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<MonitorMetricsInterfaceDataDO> getMonitorMetricsInterfaceDataPage(MonitorMetricsInterfaceDataPageReqVO pageReqVO) {
        return monitorMetricsInterfaceDataMapper.selectPage(pageReqVO);
    }

    @Override
    public List<MonitorMetricsInterfaceDataDO> getMonitorMetricsInterfaceDataList(MonitorMetricsInterfaceDataExportReqVO exportReqVO) {
        return monitorMetricsInterfaceDataMapper.selectList(exportReqVO);
    }

}
