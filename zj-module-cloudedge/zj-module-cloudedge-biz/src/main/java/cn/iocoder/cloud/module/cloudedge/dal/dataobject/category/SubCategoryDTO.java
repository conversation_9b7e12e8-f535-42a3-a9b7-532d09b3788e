package cn.iocoder.cloud.module.cloudedge.dal.dataobject.category;

import lombok.Data;

/**
 * @ClassName : SubCategoryDTO  //类名
 * @Description : app  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/3/19  15:10
 */
@Data
public class SubCategoryDTO {
    private String value;
    private String label;

    public SubCategoryDTO(String value, String label) {
        this.value = value;
        this.label = label;
    }

}
