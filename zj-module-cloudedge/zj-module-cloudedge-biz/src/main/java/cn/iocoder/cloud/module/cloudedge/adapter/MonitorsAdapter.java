package cn.iocoder.cloud.module.cloudedge.adapter;

import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.apache.hertzbeat.common.entity.message.CollectRep;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 监控适配器接口
 * 定义监控数据处理的通用接口
 */
public interface MonitorsAdapter {

    /**
     * 获取监控类型
     *
     * @return 监控类型
     */
    MonitorType getMonitorType();
    
    /**
     * 处理监控数据
     *
     * @param monitor 监控对象
     * @param metricsDataList 监控指标数据列表
     */
    void handleMonitorMetricsData(Monitor monitor, List<CollectRep.MetricsData> metricsDataList);

    /**
     * 处理Top10监控数据
     *
     * @param metricsData 监控指标数据列表
     * @return TOP10监控指标数据列表
     *
     */
    Map<String, List<Map<String, Object>>> handleMonitorTop10MetricsData(CollectRep.MetricsData metricsData);

    /**
     * 获取指标数据
     * 
     * @param metricsDataList 指标数据列表
     * @return 指定类型的指标数据
     */
    default Optional<CollectRep.MetricsData> getMetricsData(List<CollectRep.MetricsData> metricsDataList) {
        return metricsDataList.stream()
                .filter(metricsData -> metricsData.getMetrics().equals(getMonitorType().getType()))
                .findFirst();
    }
    
    /**
     * 获取字段索引
     * 
     * @param metricsData 指标数据
     * @param fieldName 字段名称
     * @return 字段索引
     */
    default int getFieldIndex(CollectRep.MetricsData metricsData, String fieldName) {
        return metricsData.getFieldsList().stream()
                .filter(field -> field.getName().equals(fieldName))
                .findFirst()
                .map(field -> metricsData.getFieldsList().indexOf(field))
                .orElse(-1);
    }

    /**
     * 获取字段的单位
     *
     * @param metricsData 指标数据
     * @param fieldName 字段名称
     * @return 字段的单位
     */
    default String getFieldUnit(CollectRep.MetricsData metricsData, String fieldName) {
        return metricsData.getFieldsList().stream()
                .filter(field -> field.getName().equals(fieldName))
                .findFirst()
                .map(CollectRep.Field::getUnit)
                .orElse("");
    }
}
