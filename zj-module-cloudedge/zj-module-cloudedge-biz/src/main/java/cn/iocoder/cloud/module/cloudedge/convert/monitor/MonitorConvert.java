package cn.iocoder.cloud.module.cloudedge.convert.monitor;


import cn.iocoder.cloud.module.cloudedge.api.monitor.dto.MonitorDTO;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.herzbeat.MonitorDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @ClassName : MonitorConvert  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/6/7  11:26
 */
@Mapper
public interface MonitorConvert {
    MonitorConvert INSTANCE = Mappers.getMapper(MonitorConvert.class);

    MonitorDTO convert(MonitorDto monitorDto);
}
