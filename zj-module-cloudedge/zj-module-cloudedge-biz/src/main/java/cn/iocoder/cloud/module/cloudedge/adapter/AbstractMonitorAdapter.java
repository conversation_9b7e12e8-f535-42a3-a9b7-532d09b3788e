package cn.iocoder.cloud.module.cloudedge.adapter;

import com.alibaba.nacos.shaded.com.google.common.base.CaseFormat;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.apache.hertzbeat.common.entity.message.CollectRep;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 监控适配器抽象基类
 * 提供通用的监控数据处理方法
 */
@Slf4j
public abstract class AbstractMonitorAdapter implements MonitorsAdapter {

    @Override
    public void handleMonitorMetricsData(Monitor monitor, List<CollectRep.MetricsData> metricsDataList) {
        Optional<CollectRep.MetricsData> metricsDataOpt = getMetricsData(metricsDataList);
        if (metricsDataOpt.isEmpty()) {
            log.warn("未找到监控类型为 {} 的指标数据", getMonitorType().getType());
            return;
        }
        
        CollectRep.MetricsData metricsData = metricsDataOpt.get();
        processMetricsData(monitor, metricsData);
    }

    /**
     * 处理指标数据
     * 
     * @param monitor 监控对象
     * @param metricsData 指标数据
     */
    protected abstract void processMetricsData(Monitor monitor, CollectRep.MetricsData metricsData);
    
    /**
     * 获取字段值
     * 
     * @param metricsData 指标数据
     * @param fieldName 字段名称
     * @return 字段值
     */
    protected Optional<String> getFieldValue(CollectRep.MetricsData metricsData, String fieldName) {
        int fieldIndex = getFieldIndex(metricsData, fieldName);
        if (fieldIndex == -1) {
            return Optional.empty();
        }
        
        return metricsData.getValuesList().stream()
                .findFirst()
                .map(valueRow -> valueRow.getColumns(fieldIndex).startsWith("&nbsp") ? null : valueRow.getColumns(fieldIndex));
    }
    
    /**
     * 获取多个字段值
     * 
     * @param metricsData 指标数据
     * @param fieldNames 字段名称数组
     * @return 第一个找到的字段值
     */
    protected Optional<String> getFirstMatchingFieldValue(CollectRep.MetricsData metricsData, String... fieldNames) {
        for (String fieldName : fieldNames) {
            Optional<String> value = getFieldValue(metricsData, fieldName);
            if (value.isPresent()) {
                return value;
            }
        }
        return Optional.empty();
    }

    /**
     * 提取并排序指标数据
     *
     * @param metricsData 指标数据
     * @param nameIndex 名称字段索引
     * @param valueIndex 值字段索引
     * @param unit 单位
     * @return 排序后的前10条数据
     */
    protected List<Map<String, Object>> extractAndSortMetrics(
            CollectRep.MetricsData metricsData,
            Integer nameIndex,
            Integer valueIndex,
            String unit) {

        if (metricsData.getValuesList().isEmpty()) {
            log.warn("指标数据为空");
            return Collections.emptyList();
        }
        if (valueIndex == -1) {
            log.warn("未找到必要的指标字段");
            return Collections.emptyList();
        }
        return metricsData.getValuesList().stream()
                .map(valueRow -> {
                    Map<String, Object> dataMap = new HashMap<>();
                    if (nameIndex == null){
                        //nameIndex为null时，为ValuesList列表的下标值
                       int nameIndexOf = metricsData.getValuesList().indexOf(valueRow);
                        dataMap.put("name", nameIndexOf);
                    }else{
                        dataMap.put("name", valueRow.getColumns(nameIndex));
                    }
                    dataMap.put("value", valueRow.getColumns(valueIndex));
                    dataMap.put("unit", unit);
                    return dataMap;
                })
                .sorted((a, b) -> {
                    try {
                        return Double.compare(
                                Double.parseDouble(b.get("value").toString()),
                                Double.parseDouble(a.get("value").toString()));
                    } catch (NumberFormatException e) {
                        log.warn("排序时发生数值转换错误: {}", e.getMessage());
                        return 0;
                    }
                })
                .limit(10)
                .collect(Collectors.toList());
    }

    /**
     * 构建TOP10指标数据结果
     *
     * @param metricsData 指标数据
     * @param nameField 名称字段
     * @param fields 需要处理的字段列表
     * @return TOP10监控指标数据
     */
    protected Map<String, List<Map<String, Object>>> buildTop10MetricsData(
            CollectRep.MetricsData metricsData,
            String nameField,
            String... fields) {
        // 获取字段索引和单位
        Map<String, Integer> fieldIndexMap = new HashMap<>();
        Map<String, String> fieldUnitMap = new HashMap<>();
        
        // 添加名称字段
        if (nameField != null) {
            fieldIndexMap.put("name", getFieldIndex(metricsData, nameField));
        }
        
        // 添加其他字段
        for (String field : fields) {
            fieldIndexMap.put(field, getFieldIndex(metricsData, field));
            fieldUnitMap.put(field, getFieldUnit(metricsData, field));
        }
        
        // 验证必要字段是否存在
        boolean missingFields = fieldIndexMap.values().stream().anyMatch(index -> index == -1);
        if (missingFields) {
            return Collections.emptyMap();
        }
        
        // 构建结果
        Map<String, List<Map<String, Object>>> result = new HashMap<>();
        for (String field : fields) {
            //field 转 驼峰规则
            String key = CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, field);
            result.put(key, extractAndSortMetrics(
                    metricsData,
                    nameField != null ? fieldIndexMap.get("name") : null,
                    fieldIndexMap.get(field),
                    fieldUnitMap.get(field)
            ));
        }
        
        return result;
    }
}