package cn.iocoder.cloud.module.cloudedge.scheduler;

import lombok.Getter;
import lombok.Setter;
import org.apache.hertzbeat.common.constants.ConfigConstants;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * scheduler properties config
 * <AUTHOR>
 */
@Getter
@Setter
@Component
@ConfigurationProperties(prefix =
        ConfigConstants.FunctionModuleConstants.SCHEDULER)
public class SchedulerProperties {

    private ServerProperties server;

    /**
     * server properties
     */
    @Getter
    @Setter
    public static class ServerProperties {

        private boolean enabled = true;

        private int port = 1158;

        /**
         * an IdleStateEvent whose state is IdleState.ALL_IDLE will be triggered when neither read nor write
         * was performed for the specified period of time.
         * unit: s
         */
        private int idleStateEventTriggerTime = 100;

    }


}
