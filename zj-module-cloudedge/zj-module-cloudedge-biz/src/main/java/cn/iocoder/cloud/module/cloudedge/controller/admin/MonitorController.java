/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.module.cloudedge.controller.admin;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.iocoder.cloud.module.cloudedge.controller.admin.monitor.vo.MonitorTagRespVo;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.collectorplatform.CollectPlatfDTO;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.collectorplatform.CollectorPlatformDO;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.herzbeat.Hierarchy;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.herzbeat.MonitorDto;
import cn.iocoder.cloud.module.cloudedge.dal.mysql.dao.ParamDao;
import cn.iocoder.cloud.module.cloudedge.service.AppService;
import cn.iocoder.cloud.module.cloudedge.service.CollectorService;
import cn.iocoder.cloud.module.cloudedge.service.MonitorService;
import cn.iocoder.cloud.module.cloudedge.service.collectorplatform.CollectorPlatformService;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.framework.operatelog.core.aop.OperateLogAspect;
import cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.monitor.api.tag.MonitorTagApi;
import cn.iocoder.zj.module.monitor.api.tag.dto.TagRespDTO;
import cn.iocoder.zj.module.system.api.licence.LicenceApi;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.usercertificate.UsercertificateApi;
import cn.iocoder.zj.module.system.api.usercertificate.dto.UserCertificateRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jodd.util.StringUtil;
import org.apache.hertzbeat.common.entity.dto.Message;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.apache.hertzbeat.common.entity.manager.Param;
import org.apache.hertzbeat.common.support.exception.CommonException;
import org.apache.hertzbeat.common.util.AesUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.singleton;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.*;
import static org.apache.hertzbeat.common.constants.CommonConstants.MONITOR_NOT_EXIST_CODE;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

/**
 * Monitoring management API
 * 监控管理API
 *
 * <AUTHOR>
 */
@Tag(name = "Monitor Manage API | 监控管理API")
@RestController
@RequestMapping(path = "/cloudedge/api/monitor", produces = {APPLICATION_JSON_VALUE})
public class MonitorController {
    @Autowired
    LicenceApi licenceApi;
    @Autowired
    PermissionApi permissionApi;
    @Autowired
    RoleApi roleApi;
    @Autowired
    private MonitorService monitorService;
    @Autowired
    private UsercertificateApi usercertificateApi;
    @Autowired
    ParamDao paramDao;
    @Autowired
    private CollectorService collectorService;
    @Autowired
    CollectorPlatformService collectorPlatformService;
    @Autowired
    MonitorTagApi monitorTagApi;
    @Autowired
    AppService appService;

    @Resource
    private OperateLogAspect operateLogAspect;

    @OperateLog(type = CREATE)
    @PostMapping
    @Operation(summary = "Add a monitoring application", description = "新增一个监控应用")
    //@PreAuthorize("@ss.hasPermission('clooud:monitor:create')")
    public ResponseEntity<Message<Map>> addNewMonitor(@Valid @RequestBody MonitorDto monitorDto) {
        //
        // todo 先根据授权类型 如果为custom则按照老方法重新赋值数据，如果类型为private-key,则先根据password中的id，
        //  查询授权管理中的数据中的类型为：custom，如果为private-key，如果为custom则重新走老方法赋值password数据，
        //  如果为private-key 则需要去根据模版中的filedid 对应的私钥值的key为privateKey 然后重新赋值privateKey 和 password


        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            // 校验查询是否资产授权已超出
            Map map = licenceApi.selectLicence(loginUser.getTenantId()).getData();
            if (map.get("state").equals("unavailable")) {
                return ResponseEntity.ok(Message.success(map));
            }
        }
        // todo 随机找一个采集器绑定
        // 根据平台id查询该平台下的采集器，选取第一个在线的采集器进行绑定
        Long platformId = monitorDto.getMonitor().getPlatformId();
        List<CollectorPlatformDO> collectors = collectorPlatformService.getCollectorPlatformByIdList(platformId);
        if (!collectors.isEmpty()) {
            // 打乱采集器数据
            Collections.shuffle(collectors);
            // 选择第一个采集器
            String collectorName = collectors.get(0).getCollectorName();
            monitorDto.setCollector(collectorName);
        } else {
            throw new CommonException("该平台下未绑定采集器，无法新增资产，请联系管理员");
        }

        List<Param> list = monitorDto.getParams();
        List<Param> paramList = new ArrayList<>(list);

        // 获取证书ID
        String certificateId = null;
        boolean privateKey = false;

        boolean containsceryofocateId = list.stream()
                .anyMatch(param -> "ceryofocateId".equals(param.getField()));

        for (Param param : list) {
            if (param.getField().equals("certificate")) {
                if (!param.getValue().equals("custom")) {
                    privateKey = true;
                }
            }

            if (param.getField().equals("password")) {
                if (privateKey) {
                    // 使用密码参数的值来查询数据
                    certificateId = Convert.toStr(param.getValue());
                    Param param1 = new Param();
                    if (StringUtil.isNotEmpty(certificateId)) {
                        param1.setField("ceryofocateId");
                        param1.setValue(certificateId.toString());
                        param1.setType((byte) 0);
                        paramList.add(param1);
                    }
                    break;
                }
            }
            //
            if (containsceryofocateId) {
                if (param.getField().equals("ceryofocateId")) {
                    param.setValue(certificateId.toString());
                }
            }

        }
        if (certificateId != null) {
            UserCertificateRespDTO userCertificateRespDTO = usercertificateApi.getUser(certificateId).getData();
            // 查询证书信息
            if (userCertificateRespDTO != null) {
                // 更新参数列表中相关参数的值
                for (Param p : paramList) {
                    if (p.getField().equals("username")) {
                        // 更新名称
                        p.setValue(userCertificateRespDTO.getUsername());
                    } else if (p.getField().equals("password")) {
                        // 更新密码
                        try {
                            p.setValue(AesUtil.aesDecode(userCertificateRespDTO.getPassword()));
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    } else if (!userCertificateRespDTO.getType().equals("custom") && p.getField().equals("privateKey")) {
                        // 更新私钥
                        p.setValue(userCertificateRespDTO.getPrivateKey());
                    }
                }
            }
        }

        monitorDto.setParams(paramList);
        // Verify request data  校验请求数据
        monitorService.validate(monitorDto, false);
//        if (monitorDto.isDetected()) {
//            // Probe    进行探测
//            monitorService.detectMonitor(monitorDto.getMonitor(), monitorDto.getParams(), monitorDto.getCollector());
//        }
        monitorService.addMonitor(monitorDto.getMonitor(), monitorDto.getParams(), monitorDto.getCollector(), monitorDto.getGrafanaDashboard());
        // 标签新增
        monitorTagApi.add(monitorDto.getMonitor().getId(), monitorDto.getMonitor().getCategory(),monitorDto.getMonitorTagIds());

        if(CollectionUtil.isNotEmpty(monitorDto.getSourceIds())){
            monitorService.updateScanRemark(monitorDto.getSourceIds());
        }
        OperateLogAspect.addExt("id", monitorDto.getMonitor().getId());
        return ResponseEntity.ok(Message.success("Add success"));
    }

    @OperateLog(
            name = "批量新增监控应用",
            type = CREATE,
            logArgs = true,
            logResultData = true,
            enable = true,
            module = "监控管理"
    )
    @PostMapping("/batch")
    @Operation(description = "批量新增监控应用")
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity<Message<Map>> batchAddNewMonitor(@Valid @RequestBody MonitorDto monitorDto) {
        // 验证IP列表
        if(CollectionUtil.isEmpty(monitorDto.getIps())){
            throw new CommonException("批量新增资产缺少资产ip,请检查");
        }

        // 权限和授权检查
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            Map map = licenceApi.selectLicence(loginUser.getTenantId()).getData();
            if (map.get("state").equals("unavailable")) {
                throw new CommonException("您当前的授权数已达上限。请联系相关人员增加授权数量或移除冗余资产以继续使用");
            }

            int remainderCount = Convert.toInt(map.get("remainderCount"));
            if (remainderCount < monitorDto.getIps().size()) {
                throw new CommonException("您当前的授权数已达上限。请联系相关人员增加授权数量或移除冗余资产以继续使用");
            }
        }

        // 处理参数列表
        List<Param> originalParams = monitorDto.getParams();
        List<Param> paramList = new ArrayList<>(originalParams);

        // 证书处理
        String certificateId = null;
        boolean privateKey = false;
        boolean containsCertificateId = originalParams.stream().anyMatch(param -> "ceryofocateId".equals(param.getField()));

        // 处理证书相关参数
        for (Param param : originalParams) {
            if ("certificate".equals(param.getField()) && !"custom".equals(param.getValue())) {
                privateKey = true;
            } else if (privateKey && "password".equals(param.getField())) {
                certificateId = Convert.toStr(param.getValue());
                if (StringUtil.isNotEmpty(certificateId)) {
                    Param certParam = new Param();
                    certParam.setField("ceryofocateId");
                    certParam.setValue(certificateId);
                    certParam.setType((byte) 0);
                    paramList.add(certParam);
                }
                break;
            } else if (containsCertificateId && "ceryofocateId".equals(param.getField()) && certificateId != null) {
                param.setValue(certificateId);
            }
        }

        // 如果有证书ID，更新相关参数
        if (certificateId != null) {
            UserCertificateRespDTO cert = usercertificateApi.getUser(certificateId).getData();
            if (cert != null) {
                for (Param p : paramList) {
                    String field = p.getField();
                    if ("username".equals(field)) {
                        p.setValue(cert.getUsername());
                    } else if ("password".equals(field)) {
                        try {
                            p.setValue(AesUtil.aesDecode(cert.getPassword()));
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    } else if ("privateKey".equals(field) && !"custom".equals(cert.getType())) {
                        p.setValue(cert.getPrivateKey());
                    }
                }
            }
        }

        Monitor monitorTemplate = BeanUtil.copyProperties(monitorDto.getMonitor(), Monitor.class);
        Map<String, Long> ipCounts = monitorDto.getIps().stream().collect(Collectors.groupingBy(ip -> ip, Collectors.counting()));
        Map<String, Integer> duplicateCounter = new HashMap<>();
        Map<String, Integer> currentIndexMap = new HashMap<>();

        //获取平台
        List<CollectPlatfDTO> platforms = collectorPlatformService.getPlatforms(monitorDto.getSourceIds());
        Map<String, List<CollectPlatfDTO>> groupedData = platforms.stream().collect(Collectors.groupingBy(CollectPlatfDTO::getIpAddress));



        for (String ip : monitorDto.getIps()) {
            List<CollectPlatfDTO> ipMaps = groupedData.get(ip);
            if (ipMaps == null || ipMaps.isEmpty()) {
                throw new CommonException(ip + " 该平台下未绑定采集器，无法新增资产，请联系管理员");
            }

            int index = currentIndexMap.merge(ip, 0, (oldValue, value) -> (oldValue + 1) % ipMaps.size());
            CollectPlatfDTO collectPlatfDTO = ipMaps.get(index);
            monitorTemplate.setPlatformId(collectPlatfDTO.getPlatformId());
            monitorTemplate.setPlatformName(collectPlatfDTO.getPlatformName());
            List<CollectorPlatformDO> collectors = collectorPlatformService.getCollectorPlatformByIdList(collectPlatfDTO.getPlatformId());
            if (collectors.isEmpty()) {
                throw new CommonException("该平台下未绑定采集器，无法新增资产，请联系管理员");
            }

            // 随机选择采集器
            Collections.shuffle(collectors);
            monitorDto.setCollector(collectors.get(0).getCollectorName());

            Monitor monitor = BeanUtil.copyProperties(monitorTemplate, Monitor.class);
            monitor.setHost(ip);

            if (ipCounts.get(ip) > 1) {
                duplicateCounter.put(ip, duplicateCounter.getOrDefault(ip, 0) + 1);
                monitor.setName(monitor.getName() + "_" + ip + "_" + duplicateCounter.get(ip));
            } else {
                monitor.setName(monitor.getName() + "_" + ip);
            }

            List<Param> currentParams = paramList.stream()
                    .map(param -> {
                        Param newParam = new Param();
                        BeanUtil.copyProperties(param, newParam);
                        newParam.setId(null);
                        return newParam;
                    })
                    .collect(Collectors.toList());
            monitorDto.setParams(currentParams);
            monitorService.validate(monitorDto, false);
            monitorService.addMonitor(monitor, currentParams, monitorDto.getCollector(), monitorDto.getGrafanaDashboard());
            monitorTagApi.add(monitor.getId(), monitorDto.getMonitor().getCategory(),monitorDto.getMonitorTagIds());

            operateLogAspect.saveLog(monitor.getId(),  "监控管理","新增监控",OperateTypeEnum.CREATE.getType(),"批量新增监控应用" );
        }

        if(CollectionUtil.isNotEmpty(monitorDto.getSourceIds())){
            monitorService.updateScanRemark(monitorDto.getSourceIds());
        }
        return ResponseEntity.ok(Message.success("Add success"));
    }

    @OperateLog(type = UPDATE)
    @PutMapping
    @Operation(summary = "Modify an existing monitoring application", description = "修改一个已存在监控应用")
    //@PreAuthorize("@ss.hasPermission('clooud:monitor:update')")
    public ResponseEntity<Message<Void>> modifyMonitor(@Valid @RequestBody MonitorDto monitorDto) {
        //添加日志
        OperateLogAspect.addExt("id", monitorDto.getMonitor().getId());
        List<Param> list = monitorDto.getParams();
        List<Param> paramList = new ArrayList<>(list);

        // 获取证书ID
        String certificateId = null;
        boolean privateKey = false;

        boolean containsceryofocateId = list.stream()
                .anyMatch(param -> "ceryofocateId".equals(param.getField()));

        List<Param> paramList1 = paramDao.findParamsByMonitorId(monitorDto.getMonitor().getId());
        // 如果前端传入ceryofocateId不存在的话则删除字段
        if (!containsceryofocateId) {
            // 删除字段
            Long id = 0L;
            for (Param param : paramList1) {
                if (param.getField().equals("ceryofocateId")) {
                    id = param.getId();
                    break;
                }
            }
            if (id != 0L) {
                paramDao.deleteById(id);
            }
        }

        for (Param param : list) {
            if (param.getField().equals("certificate")) {
                if (!param.getValue().equals("custom")) {
                    privateKey = true;
                }
            }
            if (param.getField().equals("password")) {
                if (privateKey) {
                    // 使用密码参数的值来查询数据
                    certificateId = Convert.toStr(param.getValue());
                    Param param1 = new Param();
                    if (StringUtil.isNotEmpty(certificateId)) {
                        param1.setField("ceryofocateId");
                        param1.setValue(certificateId.toString());
                        param1.setType((byte) 0);
                        paramList.add(param1);
                    }
                    break;
                }
            }
            //
            if (containsceryofocateId) {
                if (param.getField().equals("ceryofocateId")) {
                    param.setValue(certificateId.toString());
                }
            }
        }
        if (certificateId != null) {
            UserCertificateRespDTO userCertificateRespDTO = usercertificateApi.getUser(certificateId).getData();
            // 查询证书信息
            if (userCertificateRespDTO != null) {
                // 更新参数列表中相关参数的值
                for (Param p : paramList) {
                    if (p.getField().equals("username")) {
                        // 更新名称
                        p.setValue(userCertificateRespDTO.getUsername());
                    } else if (p.getField().equals("password")) {
                        try {
                            p.setValue(AesUtil.aesDecode(userCertificateRespDTO.getPassword()));
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    } else if (!userCertificateRespDTO.getType().equals("custom") && p.getField().equals("privateKey")) {
                        // 更新私钥
                        p.setValue(userCertificateRespDTO.getPrivateKey());
                    }
                }
            }
        }
        // 根据平台id查询该平台下的采集器，选取第一个在线的采集器进行绑定
        Long platformId = monitorDto.getMonitor().getPlatformId();
        List<CollectorPlatformDO> collectors = collectorPlatformService.getCollectorPlatformByIdList(platformId);
        if (!collectors.isEmpty()) {
            // 打乱采集器数据
            Collections.shuffle(collectors);
            // 选择第一个采集器
            String collectorName = collectors.get(0).getCollectorName();
            monitorDto.setCollector(collectorName);
        } else {
            throw new CommonException("该平台下未绑定采集器，无法新增资产，请联系管理员");
        }
        monitorDto.setParams(paramList);
        // Verify request data  校验请求数据
        monitorService.validate(monitorDto, false);
//        if (monitorDto.isDetected()) {
//            // Probe    进行探测
//            monitorService.detectMonitor(monitorDto.getMonitor(), monitorDto.getParams(), monitorDto.getCollector());
//        }
        monitorService.modifyMonitor(monitorDto.getMonitor(), monitorDto.getParams(), monitorDto.getCollector(), monitorDto.getGrafanaDashboard());
        // 标签新增
        monitorTagApi.add(monitorDto.getMonitor().getId(), monitorDto.getMonitor().getCategory(),monitorDto.getMonitorTagIds());
        OperateLogAspect.addExt("id", monitorDto.getMonitor().getId());
        return ResponseEntity.ok(Message.success("Modify success"));
    }

    @GetMapping(path = "/{id}")
    @Operation(summary = "Obtain monitoring information based on monitoring ID", description = "根据监控任务ID获取监控信息")
    //@PreAuthorize("@ss.hasPermission('clooud:monitor:query')")
    public ResponseEntity<Message<MonitorDto>> getMonitor(
            @Parameter(description = "监控任务ID", example = "6565463543") @PathVariable("id") final long id) {
        // Get monitoring information
        // 获取监控信息
        MonitorDto monitorDto = monitorService.getMonitorDto(id);

        List<Hierarchy> appHierarchy = appService.getAppHierarchy(monitorDto.getMonitor().getApp(), "zh-CN");
        String category = !appHierarchy.isEmpty() ? appHierarchy.stream().findFirst().map(Hierarchy::getCategory).orElse(null) : null;
        if (monitorDto == null) {
            return ResponseEntity.ok(Message.fail(MONITOR_NOT_EXIST_CODE, "Monitor not exist."));
        } else {
            if (monitorDto.getMonitor().getCategory() == null || monitorDto.getMonitor().getCategory().isEmpty()) {
                monitorDto.getMonitor().setCategory(category);
            }
            MonitorTagRespVo rt =  new MonitorTagRespVo();
            BeanUtil.copyProperties(monitorDto.getMonitor(), rt);
            List<TagRespDTO> mTags = monitorTagApi.getByTaggable(id, monitorDto.getMonitor().getCategory());
            rt.setMonitorTags(mTags);
            monitorDto.setMonitor(rt);
            return ResponseEntity.ok(Message.success(monitorDto));
        }
    }


    @DeleteMapping(path = "/{id}")
//    @Operation(summary = "删除监控资产", description = "根据监控任务ID删除监控应用")
    //@PreAuthorize("@ss.hasPermission('clooud:monitor:delete')")
    public ResponseEntity<Message<Void>> deleteMonitor(@Parameter(description = "en: Monitor ID,zh: 监控任务ID", example = "6565463543") @PathVariable("id") final long id,@RequestParam(value = "remark", required = false, defaultValue = "无") String remark) {
        // delete monitor 删除监控
        Monitor monitor = monitorService.getMonitor(id);
        if (monitor == null) {
            return ResponseEntity.ok(Message.success("The specified monitoring was not queried, please check whether the parameters are correct"));
        }
        monitorService.deleteMonitor(id);
        monitorTagApi.del(id);

        String content = "删除资产：" + monitor.getName();
        Map<String, Object> ext = Map.of("备注", remark);
        Map<String, Object> param = Map.of("id", id);
        operateLogAspect.saveLog(monitor.getId(),"监控管理API","删除监控资产",OperateTypeEnum.DELETE.getType(),content,ext,JsonUtils.toJsonString(param));
        return ResponseEntity.ok(Message.success("Delete success"));
    }

    @OperateLog(type = OTHER)
    @PostMapping(path = "/detect")
    @Operation(summary = "Perform availability detection on this monitoring based on monitoring information", description = "根据监控信息去对此监控进行可用性探测")
    public ResponseEntity<Message<Void>> detectMonitor(@Valid @RequestBody MonitorDto monitorDto) {
        List<Param> list = monitorDto.getParams();
        List<Param> paramList = new ArrayList<>(list);

        // 获取证书ID
        String certificateId = null;
        boolean privateKey = false;

        boolean containsceryofocateId = list.stream()
                .anyMatch(param -> "ceryofocateId".equals(param.getField()));
        List<Param> paramList1 = new ArrayList<>();
        try {
            paramList1 = paramDao.findParamsByMonitorId(monitorDto.getMonitor().getId());
        } catch (Exception e) {

        }
        if (paramList1.size() > 0) {
            if (!containsceryofocateId) {
                // 删除字段
                Long id = 0L;
                for (Param param : paramList1) {
                    if (param.getField().equals("ceryofocateId")) {
                        id = param.getId();
                        break;
                    }
                }
                if (id != 0L) {
                    paramDao.deleteById(id);
                }
            }

        }

        // 如果前端传入ceryofocateId不存在的话则删除字段
        for (Param param : list) {
            if (param.getField().equals("certificate")) {
                if (!param.getValue().equals("custom")) {
                    privateKey = true;
                }
            }
            if (param.getField().equals("password")) {
                if (privateKey) {
                    // 使用密码参数的值来查询数据
                    certificateId = Convert.toStr(param.getValue());
                    Param param1 = new Param();
                    if (StringUtil.isNotEmpty(certificateId)) {
                        param1.setField("ceryofocateId");
                        param1.setValue(certificateId.toString());
                        param1.setType((byte) 0);
                        paramList.add(param1);
                    }
                    break;
                }
            }
            //
            if (containsceryofocateId) {
                if (param.getField().equals("ceryofocateId")) {
                    param.setValue(certificateId.toString());
                }
            }
        }
        if (certificateId != null) {
            UserCertificateRespDTO userCertificateRespDTO = usercertificateApi.getUser(certificateId).getData();
            // 查询证书信息
            if (userCertificateRespDTO != null) {
                // 更新参数列表中相关参数的值
                for (Param p : paramList) {
                    if (p.getField().equals("username")) {
                        // 更新名称
                        p.setValue(userCertificateRespDTO.getUsername());
                    } else if (p.getField().equals("password")) {
                        try {
                            p.setValue(AesUtil.aesDecode(userCertificateRespDTO.getPassword()));
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    } else if (!userCertificateRespDTO.getType().equals("custom") && p.getField().equals("privateKey")) {
                        // 更新私钥
                        p.setValue(userCertificateRespDTO.getPrivateKey());
                    }
                }
            }
        }
        // 根据平台id查询该平台下的采集器，选取第一个在线的采集器进行绑定
        Long platformId = monitorDto.getMonitor().getPlatformId();
        List<CollectorPlatformDO> collectors = collectorPlatformService.getCollectorPlatformByIdList(platformId);
        if (!collectors.isEmpty()) {
            // 打乱采集器数据
            Collections.shuffle(collectors);
            // 选择第一个采集器
            String collectorName = collectors.get(0).getCollectorName();
            monitorDto.setCollector(collectorName);
        } else {
            throw new CommonException("该平台下未绑定采集器，无法新增资产，请联系管理员");
        }
        monitorDto.setParams(paramList);
        monitorService.validate(monitorDto, null);
        monitorService.detectMonitor(monitorDto.getMonitor(), monitorDto.getParams(), monitorDto.getCollector());
        monitorTagApi.add(monitorDto.getMonitor().getId(), monitorDto.getMonitor().getCategory(),monitorDto.getMonitorTagIds());
        return ResponseEntity.ok(Message.success("Detect success."));
    }

    @OperateLog(type = CREATE)
    @PostMapping("/optional")
    @Operation(summary = "Add a monitor that can select metrics", description = "新增一个可选指标的监控器")
    public ResponseEntity<Message<Void>> addNewMonitorOptionalMetrics(@Valid @RequestBody MonitorDto monitorDto) {
        monitorService.validate(monitorDto, false);
//        if (monitorDto.isDetected()) {
//            monitorService.detectMonitor(monitorDto.getMonitor(), monitorDto.getParams(), monitorDto.getCollector());
//        }
        monitorService.addNewMonitorOptionalMetrics(monitorDto.getMetrics(), monitorDto.getMonitor(), monitorDto.getParams());
        return ResponseEntity.ok(Message.success("Add success"));
    }

    @GetMapping(value = {"/metric/{app}", "/metric"})
    @Operation(summary = "get app metric", description = "根据app名称获取该app可监控指标，不传为获取全部指标")
    public ResponseEntity<Message<List<String>>> getMonitorMetrics(
            @PathVariable(value = "app", required = false) String app) {
        List<String> metricNames = monitorService.getMonitorMetrics(app);
        return ResponseEntity.ok(Message.success(metricNames));
    }

}
