/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.module.cloudedge.controller.admin;

import cn.iocoder.cloud.module.cloudedge.scheduler.netty.ManageServer;
import cn.iocoder.cloud.module.cloudedge.service.CollectorService;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.hertzbeat.common.entity.dto.CollectorSummary;
import org.apache.hertzbeat.common.entity.dto.Message;
import org.apache.hertzbeat.common.util.ResponseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

/**
 * collector API
 *
 * <AUTHOR>
 */
@Tag(name = "Collector Manage API | 采集器信息管理API")
@RestController()
@RequestMapping(value = "/cloudedge/api/collector", produces = {APPLICATION_JSON_VALUE})
public class CollectorController {

    @Autowired
    private CollectorService collectorService;

    @Autowired(required = false)
    private ManageServer manageServer;

    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    private PermissionApi permissionApi;
    @Resource
    private RoleApi roleApi;

    @GetMapping
    @Operation(summary = "Get a list of collectors based on query filter items",
            description = "根据查询过滤项获取采集器列表")
    public ResponseEntity<Message<Page<CollectorSummary>>> getCollectors(
            @Parameter(description = "collector name", example = "tom") @RequestParam(required = false) final String name,
            @Parameter(description = "List current page | 列表当前分页", example = "0") @RequestParam(defaultValue = "0") int pageIndex,
            @Parameter(description = "Number of list pagination | 列表分页数量", example = "8") @RequestParam(required = false) Integer pageSize,
            @Parameter(description = "Monitor platformId | 平台id", example = "1024") @RequestParam(required = false) final Long platformId,
            @Parameter(description = "sort field | 排序字段", example = "gmtUpdate") @RequestParam(required = false) String sortBy,
            @Parameter(description = "Sorting rules | 排序规则", example = "desc") @RequestParam(required = false) String sortDirection,
            @Parameter(description = "running status  | 运行状态", example = "0") @RequestParam(required = false) Integer status,
            @Parameter(description = "ip  | ip", example = "0") @RequestParam(required = false) String ip,
            @Parameter(description = "collector name", example = "tom") @RequestParam(required = false) final String projectName) {
        return ResponseUtil.handle(() -> collectorService.getCollectors(name, pageIndex, pageSize, platformId, sortBy, sortDirection, projectName,status,ip));
    }

    @PutMapping("/online")
    @Operation(summary = "Online collectors")
    @PreAuthorize("@ss.hasPermission('cloudedge:collector:online')")
    public ResponseEntity<Message<Void>> onlineCollector(
            @Parameter(description = "collector name", example = "demo-collector")
            @RequestParam(required = false) List<String> collectors) {
        collectorService.makeCollectorsOnline(collectors);

        return ResponseEntity.ok(Message.success("Online success"));
    }

    @PutMapping("/offline")
    @Operation(summary = "Offline collectors")
    @PreAuthorize("@ss.hasPermission('cloudedge:collector:offline')")
    public ResponseEntity<Message<Void>> offlineCollector(
            @Parameter(description = "collector name", example = "demo-collector")
            @RequestParam(required = false) List<String> collectors) {
        collectorService.makeCollectorsOffline(collectors);
        return ResponseEntity.ok(Message.success("Offline success"));
    }

    @DeleteMapping
    @Operation(summary = "Delete collectors")
    @PreAuthorize("@ss.hasPermission('cloudedge:collector:delete')")
    public ResponseEntity<Message<Void>> deleteCollector(
            @Parameter(description = "collector name | 采集器名称", example = "demo-collector")
            @RequestParam(required = false) List<String> collectors) {
        this.collectorService.deleteRegisteredCollector(collectors);
        return ResponseEntity.ok(Message.success("Delete success"));
    }

    @PostMapping("/generate/{collector}")
    @Operation(summary = "Generate deploy collector info")
    public ResponseEntity<Message<Map<String, String>>> generateCollectorDeployInfo(
            @Parameter(description = "collector name", example = "demo-collector")
            @PathVariable() String collector) {
        return ResponseUtil.handle(() -> collectorService.generateCollectorDeployInfo(collector));
    }


    @PostMapping("/bind")
    @Operation(summary = "bind collectors || 采集器平台绑定")
    @PreAuthorize("@ss.hasPermission('cloudedge:collector:bind')")
    public ResponseEntity<Message<Void>> bind(
            @Parameter(description = "collector name", example = "demo-collector")
            @RequestParam(required = true) String collector,
            @RequestParam(required = true) Long platformId,
            @RequestParam(required = false) String platformName) {
        collectorService.bindCollector(collector, platformId, platformName);
        return ResponseEntity.ok(Message.success("Bind success"));
    }

    @GetMapping("/getstatus")
    @Operation(summary = "bind collectors || 采集器状态显示")
    public ResponseEntity<Message<Map>> getstatus() {
        return ResponseEntity.ok(Message.success(collectorService.getstatus()));
    }

    @PostMapping("/bindProjectId")
    @Operation(summary = "bind collectors || 采集器项目绑定")
    @PreAuthorize("@ss.hasPermission('cloudedge:collector:bindProjectId')")
    public ResponseEntity<Message<Void>> bindProjectId(
            @Parameter(description = "collector name", example = "demo-collector")
            @RequestParam(required = true) String collector,
            @RequestParam(required = true) Long projectId,
            @RequestParam(required = false) String projectName) {
        collectorService.bindProjectId(collector, projectId, projectName);
        return ResponseEntity.ok(Message.success("Bind success"));
    }
}
