/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.module.cloudedge.service.impl;

import cn.iocoder.cloud.framework.plugin.runner.PluginRunner;
import cn.iocoder.cloud.module.cloudedge.service.PluginService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.plugin.PluginContext;
import org.springframework.stereotype.Service;

import java.util.function.BiConsumer;
import java.util.function.Consumer;


/**
 * default  plugin runner
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DefaultPluginRunner implements PluginRunner {

    private final PluginService pluginService;

    @Override
    public <T> void pluginExecute(Class<T> clazz, Consumer<T> execute) {
        try {
            pluginService.pluginExecute(clazz, execute);
        } catch (Exception e) {
            log.error("plugin execute failed", e);
        }
    }

    @Override
    public <T> void pluginExecute(Class<T> clazz, BiConsumer<T, PluginContext> execute) {
        try {
            pluginService.pluginExecute(clazz, execute);
        } catch (Exception e) {
            log.error("plugin execute failed", e);
        }
    }
}
