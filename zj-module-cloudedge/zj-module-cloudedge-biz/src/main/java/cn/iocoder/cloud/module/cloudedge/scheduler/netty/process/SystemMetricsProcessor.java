package cn.iocoder.cloud.module.cloudedge.scheduler.netty.process;

import cn.iocoder.cloud.framework.remoting.netty.NettyRemotingProcessor;
import cn.iocoder.cloud.module.cloudedge.scheduler.netty.ManageServer;
import io.netty.channel.ChannelHandlerContext;
import org.apache.hertzbeat.common.entity.message.ClusterMsg;
import org.apache.hertzbeat.common.util.JsonUtil;

import java.util.Map;

public class SystemMetricsProcessor implements NettyRemotingProcessor {

    private final ManageServer manageServer;

    public SystemMetricsProcessor(final ManageServer manageServer) {
        this.manageServer = manageServer;
    }

    @Override
    public ClusterMsg.Message handle(ChannelHandlerContext ctx, ClusterMsg.Message message) {
        String collector = message.getIdentity();
        Map<String, Object> map = JsonUtil.fromJson(message.getMsg(), Map.class);
        this.manageServer.getCollectorAndJobScheduler().getMetricsCollector(collector, map);
        return null;
    }
}
