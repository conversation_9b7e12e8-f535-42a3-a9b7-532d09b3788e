package cn.iocoder.cloud.module.cloudedge.controller.admin.alertdefinetemplate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Schema(description = "管理后台 - 多维告警模板 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AlertDefineTemplateRespVO extends AlertDefineTemplateBaseVO {

    @Schema(description = "id", required = true)
    private Long id;

}
