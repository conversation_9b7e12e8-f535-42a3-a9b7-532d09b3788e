/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.module.cloudedge.config;

import lombok.Data;
import org.apache.hertzbeat.common.constants.ConfigConstants;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * AiProperties
 */

@Data
@Configuration
@ConfigurationProperties(prefix =
        ConfigConstants.FunctionModuleConstants.AI)
public class AiProperties {

    /**
     * AI Type: zhi<PERSON>u, aliba<PERSON>A<PERSON>, kimiAi, sparkDesk
     */
    private String type;

    /**
     * Model name: glm-4, qwen-turboo, moonshot-v1-8k, generalv3.5
     */
    private String model;

    /**
     * API key
     */
    private String apiKey;

    /**
     * At present, only IFLYTEK large model needs to be filled in
     */
    private String apiSecret;

}
