package cn.iocoder.cloud.module.cloudedge.dal.mysql.alertdefinetemplate;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.alertdefinetemplate.AlertDefineTemplateDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.cloud.module.cloudedge.controller.admin.alertdefinetemplate.vo.*;

/**
 * 多维告警模板 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AlertDefineTemplateMapper extends BaseMapperX<AlertDefineTemplateDO> {

    default PageResult<AlertDefineTemplateDO> selectPage(AlertDefineTemplatePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AlertDefineTemplateDO>()
                .likeIfPresent(AlertDefineTemplateDO::getName, reqVO.getName())
                .eqIfPresent(AlertDefineTemplateDO::getApp, reqVO.getApp())
                .eqIfPresent(AlertDefineTemplateDO::getExpr, reqVO.getExpr())
                .eqIfPresent(AlertDefineTemplateDO::getField, reqVO.getField())
                .eqIfPresent(AlertDefineTemplateDO::getMetric, reqVO.getMetric())
                .eqIfPresent(AlertDefineTemplateDO::getPriority, reqVO.getPriority())
                .eqIfPresent(AlertDefineTemplateDO::getTemplate, reqVO.getTemplate())
                .eqIfPresent(AlertDefineTemplateDO::getTimes, reqVO.getTimes())
                .orderByDesc(AlertDefineTemplateDO::getId));
    }

    default List<AlertDefineTemplateDO> selectList(AlertDefineTemplateExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AlertDefineTemplateDO>()
                .likeIfPresent(AlertDefineTemplateDO::getName, reqVO.getName())
                .eqIfPresent(AlertDefineTemplateDO::getApp, reqVO.getApp())
                .eqIfPresent(AlertDefineTemplateDO::getExpr, reqVO.getExpr())
                .eqIfPresent(AlertDefineTemplateDO::getField, reqVO.getField())
                .eqIfPresent(AlertDefineTemplateDO::getMetric, reqVO.getMetric())
                .eqIfPresent(AlertDefineTemplateDO::getPriority, reqVO.getPriority())
                .eqIfPresent(AlertDefineTemplateDO::getTemplate, reqVO.getTemplate())
                .eqIfPresent(AlertDefineTemplateDO::getTimes, reqVO.getTimes())
                .orderByDesc(AlertDefineTemplateDO::getId));
    }

}
