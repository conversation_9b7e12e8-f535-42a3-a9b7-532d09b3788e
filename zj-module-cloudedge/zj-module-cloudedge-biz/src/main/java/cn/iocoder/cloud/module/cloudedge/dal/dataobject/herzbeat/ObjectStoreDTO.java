package cn.iocoder.cloud.module.cloudedge.dal.dataobject.herzbeat;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件存储容器
 *
 * <AUTHOR> href="mailto:<EMAIL>">gcdd1993</a>
 * Created by gcdd1993 on 2023/9/13
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ObjectStoreDTO<T> {
    /**
     * file storage service type
     */
    private Type type;

    /**
     * Configuration item
     */
    private T config;

    /**
     * file storage service type
     */
    public enum Type {

        /**
         * local file
         */
        FILE,

        /**
         * local database
         */
        DATABASE,

        /**
         * <a href="https://support.huaweicloud.com/obs/index.html">Huawei Cloud OBS</a>
         */
        OBS
    }

    /**
     * file storage configuration
     */
    @Data
    public static class ObsConfig {
        private String accessKey;
        private String secretKey;
        private String bucketName;
        private String endpoint;

        /**
         * Save path
         */
        private String savePath = "hertzbeat";
    }

}
