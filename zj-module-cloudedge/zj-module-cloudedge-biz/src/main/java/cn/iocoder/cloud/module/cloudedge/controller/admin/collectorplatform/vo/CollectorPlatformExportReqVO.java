package cn.iocoder.cloud.module.cloudedge.controller.admin.collectorplatform.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 采集器平台关联 Excel 导出 Request VO，参数和 CollectorPlatformPageReqVO 是一致的")
@Data
public class CollectorPlatformExportReqVO {

    @Schema(description = "采集器id")
    private Long collectorId;

    @Schema(description = "采集器名称")
    private String collectorName;

    @Schema(description = "平台id")
    private Long platformId;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "存活状态")
    private Byte status;

}
