package cn.iocoder.cloud.module.cloudedge.service.impl;

import cn.hutool.core.convert.Convert;
import cn.iocoder.cloud.module.cloudedge.dal.mysql.collectorplatform.CollectorPlatformMapper;
import cn.iocoder.cloud.module.cloudedge.dal.mysql.dao.CollectorDao;
import cn.iocoder.cloud.module.cloudedge.dal.mysql.dao.CollectorMonitorBindDao;
import cn.iocoder.cloud.module.cloudedge.scheduler.AssignJobs;
import cn.iocoder.cloud.module.cloudedge.scheduler.ConsistentHash;
import cn.iocoder.cloud.module.cloudedge.scheduler.netty.ManageServer;
import cn.iocoder.cloud.module.cloudedge.service.CollectorService;
import cn.iocoder.cloud.module.cloudedge.service.collectorplatform.CollectorPlatformService;
import cn.iocoder.cloud.module.cloudedge.util.IpDomainUtil;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.util.string.StringUtil;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.hertzbeat.common.constants.CommonConstants;
import org.apache.hertzbeat.common.entity.dto.CollectorSummary;
import org.apache.hertzbeat.common.entity.manager.Collector;
import org.apache.hertzbeat.common.entity.manager.CollectorMonitorBind;
import org.apache.hertzbeat.common.support.exception.CommonException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.*;

import static java.util.Collections.singleton;

/**
 * collector service impl
 *
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class CollectorServiceImpl implements CollectorService {

    @Autowired
    private CollectorDao collectorDao;

    @Autowired
    private CollectorMonitorBindDao collectorMonitorBindDao;

    @Autowired
    private ConsistentHash consistentHash;

    @Autowired(required = false)
    private ManageServer manageServer;
    @Resource
    CollectorPlatformService collectorPlatformService;
    @Resource
    CollectorPlatformMapper collectorPlatformMapper;
    @Resource
    PlatformconfigApi platformconfigApi;
    @Resource
    private PermissionApi permissionApi;
    @Resource
    private RoleApi roleApi;

    @Override
    @Transactional(readOnly = true)
    public Page<CollectorSummary> getCollectors(String name, int pageIndex, Integer pageSize, Long platformId, String sortBy, String sortDirection, String projectName,Integer status,String ip) {
        if (pageSize == null) {
            pageSize = Integer.MAX_VALUE;
        }
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));


        List<Predicate> andList = new ArrayList<>();
        Specification<Collector> specification = (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();

            if (name != null && !name.isEmpty()) {
                Predicate predicateName = criteriaBuilder.like(root.get("name"), "%" + name + "%");
                andList.add(predicateName);
            }
            if (platformId != null) {
                Predicate predicateStatus = criteriaBuilder.equal(root.get("platformId"), platformId);
                andList.add(predicateStatus);
            }

            if (status != null) {
                Predicate predicateStatus = criteriaBuilder.equal(root.get("status"), status);
                andList.add(predicateStatus);
            }

            if (projectName != null && !projectName.isEmpty()) {
                Predicate predicateProjectName = criteriaBuilder.like(root.get("projectName"), "%" + projectName + "%");
                andList.add(predicateProjectName);
            }

            if (ip != null && !ip.isEmpty()) {
                Predicate ipName = criteriaBuilder.like(root.get("ip"), "%" + ip + "%");
                andList.add(ipName);
            }


            // 添加一个过滤条件来排除名称为 "main-default-collector" 的数据
            Predicate excludePredicate = criteriaBuilder.notEqual(root.get("name"), "main-default-collector");
            andList.add(excludePredicate);

            if (!roleApi.hasAnySuperAdmin(roleIds) && !roleApi.hasAnyPlatformAdmin(roleIds) && !roleApi.hasAnyPlatformMaintenance(roleIds)  && loginUser.getTenantId() != 1) {
                Predicate predicateStatus = criteriaBuilder.equal(root.get("projectId"), loginUser.getTenantId());
                andList.add(predicateStatus);
            }
            if (roleApi.hasAnyPlatformMaintenance(roleIds) && loginUser.getTenantId() != 1) {
                Predicate predicateStatus = criteriaBuilder.equal(root.get("projectId"), loginUser.getTenantId());
                andList.add(predicateStatus);
            }

            // 组合所有的条件
            predicate = criteriaBuilder.and(andList.toArray(new Predicate[0]));

            // 添加 order by
            if (StringUtil.isNotEmpty(sortDirection)) {
                String[] sortableFields = {"gmtUpdate", "offlineTime", "memUsage", "cpuUsage", "rxMbps", "txMbps"};

                for (String field : sortableFields) {
                    if (sortBy.equals(field)) {
                        if (sortDirection.equals("asc")) {
                            query.orderBy(criteriaBuilder.asc(root.get(field)));
                        } else {
                            query.orderBy(criteriaBuilder.desc(root.get(field)));
                        }
                        break;
                    }
                }
            } else {
                query.orderBy(criteriaBuilder.desc(root.get("gmtCreate")));
            }
            return predicate;
        };

        PageRequest pageRequest = PageRequest.of(pageIndex, pageSize);
        Page<Collector> collectors = collectorDao.findAll(specification, pageRequest);
        List<CollectorSummary> collectorSummaryList = new LinkedList<>();
        for (Collector collector : collectors.getContent()) {
            CollectorSummary.CollectorSummaryBuilder summaryBuilder = CollectorSummary.builder().collector(collector);
            ConsistentHash.Node node = consistentHash.getNode(collector.getName());
            if (node != null && node.getAssignJobs() != null) {
                AssignJobs assignJobs = node.getAssignJobs();
                summaryBuilder.pinMonitorNum(assignJobs.getPinnedJobs().size());
                summaryBuilder.dispatchMonitorNum(assignJobs.getJobs().size());
            }
            collectorSummaryList.add(summaryBuilder.build());
        }
        return new PageImpl<>(collectorSummaryList, pageRequest, collectors.getTotalElements());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRegisteredCollector(List<String> collectors) {
        if (collectors == null || collectors.isEmpty()) {
            return;
        }
        // Determine whether there are fixed tasks on the collector
        collectors.forEach(collector -> {
            List<CollectorMonitorBind> binds = this.collectorMonitorBindDao.findCollectorMonitorBindsByCollector(collector);
            if (!binds.isEmpty()) {
                throw new CommonException("The collector " + collector + " has pinned tasks that cannot be deleted.");
            }
        });

        // todo 同时删除采集器与平台的关系表
        collectors.forEach(collector -> {
            this.manageServer.closeChannel(collector);
            this.collectorDao.deleteCollectorByName(collector);
            collectorPlatformService.deleteCollectorPlatformByName(collector);
        });
    }

    @Override
    public boolean hasCollector(String collector) {
        return this.collectorDao.findCollectorByName(collector).isPresent();
    }

    @Override
    public void bindCollector(String collector, Long platformId, String platformName) {
        Collector collector1 = collectorDao.findCollectorByName(collector).get();
        collectorPlatformService.bindCollector(collector1, platformId, platformName);
    }

    @Override
    @TenantIgnore
    public List<Map> collectorBySelect() {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        List<Map> platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();

        List<Map> list = new ArrayList<>();
        // 如果是超级管理员则返回所有数据
        if (roleApi.hasAnySuperAdmin(roleIds)) {
            list = collectorPlatformMapper.collectorBySelect(new ArrayList<>());
        } else {
            list = collectorPlatformMapper.collectorBySelect(platform);
        }

        return list;
    }

    @Override
    public Map<String, Object> getstatus() {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));

        List<Map> list = new ArrayList<>();
        if (roleApi.hasAnySuperAdmin(roleIds) || loginUser.getTenantId()==1) {
            list = collectorPlatformMapper.collectorBySelect(new ArrayList<>());
        }else {
            list = collectorPlatformMapper.selectListByTenantId(loginUser.getTenantId());
        }
        // 如果是超级管理员则返回所有数据

        Map<String, Object> result = new HashMap<>();
        if (!list.isEmpty()) {
            result = checkStatus(list);
        } else {
            result.put("status", "-1");
            result.put("datacount", new ArrayList<>());
        }
        return result;
    }

    @Override
    public void bindProjectId(String collector, Long projectId, String projectName) {
        Collector collector1 = collectorDao.findCollectorByName(collector).get();
        collectorPlatformService.bindProjectId(collector1, projectId, projectName);
    }

    @Override
    public List<Collector> getByPlatformId(Long PlatformId) {
        return collectorDao.findByPlatformIdAndStatusIs(PlatformId, CommonConstants.COLLECTOR_STATUS_OFFLINE);
    }


    public static Map<String, Object> checkStatus(List<Map> list) {
        int count0 = 0;
        int count1 = 0;

        // 统计status的数量
        for (Map<String, String> data : list) {
            String status = Convert.toStr(data.get("status"));
            if ("0".equals(status)) {
                count0++;
            } else if ("1".equals(status)) {
                count1++;
            }
        }

        // 确定最终的status值
        String finalStatus;
        if (count0 > 0 && count1 > 0) {
            finalStatus = "1";
        } else if (count1 == 0) {
            finalStatus = "0";
        } else {
            finalStatus = "2";
        }

        // 生成返回的Map
        Map<String, Object> result = new HashMap<>();
        result.put("status", finalStatus);

        List<Map<String, String>> datacount = new ArrayList<>();

        Map<String, String> countMap1 = new HashMap<>();
        countMap1.put("status", "1");
        countMap1.put("count", String.valueOf(count1));
        datacount.add(countMap1);

        Map<String, String> countMap0 = new HashMap<>();
        countMap0.put("status", "0");
        countMap0.put("count", String.valueOf(count0));
        datacount.add(countMap0);

        result.put("datacount", datacount);
        return result;
    }

    @Override
    public Map<String, String> generateCollectorDeployInfo(String collector) {
        if (hasCollector(collector)) {
            throw new CommonException("There already exists a collector with same name.");
        }
        String host = IpDomainUtil.getLocalhostIp();
        Map<String, String> maps = new HashMap<>(6);
        maps.put("identity", collector);
        maps.put("host", host);
        return maps;
    }

    @Override
    public void makeCollectorsOffline(List<String> collectors) {
        if (CollectionUtils.isNotEmpty(collectors)) {
            collectors.forEach(collector -> this.manageServer.getCollectorAndJobScheduler().offlineCollector(collector));
        }
    }

    @Override
    public void makeCollectorsOnline(List<String> collectors) {
        if (CollectionUtils.isNotEmpty(collectors)) {
            collectors.forEach(collector ->
                    this.manageServer.getCollectorAndJobScheduler().onlineCollector(collector));
        }
    }

}
