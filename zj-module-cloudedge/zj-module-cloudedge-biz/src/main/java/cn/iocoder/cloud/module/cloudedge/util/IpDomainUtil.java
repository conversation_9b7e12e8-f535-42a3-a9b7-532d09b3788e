/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.module.cloudedge.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.conn.util.InetAddressUtils;
import org.apache.hertzbeat.common.constants.CollectorConstants;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.UnknownHostException;
import java.util.*;
import java.util.regex.Pattern;

/**
 * ipv4 ipv6 domain util
 * <AUTHOR>
 */
@Slf4j
public class IpDomainUtil {
    
    private static final Pattern DOMAIN_PATTERN =
            Pattern.compile("^[-\\w]+(\\.[-\\w]+)*$");

    private static final String LOCALHOST = "localhost";

    /**
     * HTTP header schema
     */
    private static final Pattern DOMAIN_SCHEMA = Pattern.compile("^([hH][tT]{2}[pP]://|[hH][tT]{2}[pP][sS]://){1}[^\\s]*");

    /**
     * whether it is ip or domain
     * @param ipDomain ip domain string
     * @return true-yes false-no
     */
    public static boolean validateIpDomain(String ipDomain) {
        if (ipDomain == null || "".equals(ipDomain)) {
            return false;
        }
        ipDomain = ipDomain.trim();
        if (LOCALHOST.equalsIgnoreCase(ipDomain)) {
            return true;
        }
        if (InetAddressUtils.isIPv4Address(ipDomain)) {
            return true;
        }
        if (InetAddressUtils.isIPv6Address(ipDomain)) {
            return true;
        }
        return DOMAIN_PATTERN.matcher(ipDomain).matches();
    }

    /**
     * if domain or ip has http / https schema
     * @param domainIp host
     * @return true or false
     */
    public static boolean isHasSchema(String domainIp) {
        if (domainIp == null || "".equals(domainIp)) {
            return false;
        }
        return DOMAIN_SCHEMA.matcher(domainIp).matches();
    }

    /**
     * get localhost IP
     * @return ip
     */
    public static String getLocalhostIp() {
        try {
            Enumeration<NetworkInterface> allNetInterfaces = NetworkInterface.getNetworkInterfaces();
            InetAddress ip;
            while (allNetInterfaces.hasMoreElements()) {
                NetworkInterface netInterface = allNetInterfaces.nextElement();
                if (!netInterface.isLoopback() && !netInterface.isVirtual() && netInterface.isUp()) {
                    Enumeration<InetAddress> addresses = netInterface.getInetAddresses();
                    while (addresses.hasMoreElements()) {
                        ip = addresses.nextElement();
                        if (ip instanceof Inet4Address) {
                            return ip.getHostAddress();
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn(e.getMessage());
        }
        return null;
    }

    /**
     *
     * @param ipDomain ip domain
     * @return IP address type
     */
    public static String checkIpAddressType(String ipDomain){
        if (InetAddressUtils.isIPv6Address(ipDomain)) {
            return CollectorConstants.IPV6;
        }
        return CollectorConstants.IPV4;
    }
    
    /**
     * get current local host name
     * @return hostname
     */
    public static String getCurrentHostName() {
        try {
            InetAddress inetAddress = InetAddress.getLocalHost();
            return inetAddress.getHostName();   
        } catch (UnknownHostException e) {
            return null;
        }
    }

    public static List<Map<String, String>> generateIpList(String startIp, String endIp) {
        List<Map<String, String>> ipList = new ArrayList<>();
        int[] startIpArray = ipToIntArray(startIp);
        int[] endIpArray = ipToIntArray(endIp);

        for (int i = startIpArray[3]; i <= endIpArray[3]; i++) {
            String ip = startIpArray[0] + "." + startIpArray[1] + "." + startIpArray[2] + "." + i;
            Map<String, String> ipMap = new HashMap<>();
            ipMap.put("IP", ip);
            ipList.add(ipMap);

        }

        return ipList;
    }
    public static List<Map<String, String>> generateIpList(String ipRange) {
        List<Map<String, String>> ipList = new ArrayList<>();
        String[] parts = ipRange.split("/");
        String baseIp = parts[0];
        int subnet = Integer.parseInt(parts[1]);

        long startIp = ipToLong(baseIp);
        long endIp = startIp + (1L << (32 - subnet)) - 1;

        for (long i = startIp; i <= endIp; i++) {
            String ip = longToIp(i);
            Map<String, String> ipMap = new HashMap<>();
            ipMap.put("IP", ip);
            ipList.add(ipMap);
        }

        return ipList;
    }

    private static long ipToLong(String ipAddress) {
        String[] octets = ipAddress.split("\\.");
        long result = 0;
        for (int i = 0; i < 4; i++) {
            result <<= 8;
            result |= Integer.parseInt(octets[i]) & 0xFF;
        }
        return result;
    }

    private static String longToIp(long ip) {
        return ((ip >> 24) & 0xFF) + "." +
                ((ip >> 16) & 0xFF) + "." +
                ((ip >> 8) & 0xFF) + "." +
                (ip & 0xFF);
    }


    public static int[] ipToIntArray(String ip) {
        String[] ipParts = ip.split("\\.");
        int[] ipArray = new int[4];
        for (int i = 0; i < 4; i++) {
            ipArray[i] = Integer.parseInt(ipParts[i]);
        }
        return ipArray;
    }

    public static List<Map<String, String>> parseIpWithPrefix(String ip) {
        List<Map<String, String>> parsedIpList = new ArrayList<>();
        String[] ipAndPrefix = ip.split("/");
        String ipAddress = ipAndPrefix[0];
        int prefixLength = Integer.parseInt(ipAndPrefix[1]);

        int[] ipArray = ipToIntArray(ipAddress);
        int mask = (prefixLength >= 8) ? 255 : (256 - (1 << (8 - prefixLength % 8)));

        for (int i = 0; i < (1 << (32 - prefixLength)); i++) {
            int[] generatedIpArray = new int[4];
            generatedIpArray[0] = ipArray[0];
            generatedIpArray[1] = ipArray[1];
            generatedIpArray[2] = ipArray[2];
            generatedIpArray[3] = ipArray[3] + i;

            // 检查生成的IP地址是否在子网范围内
            if ((generatedIpArray[3] & mask) == (ipArray[3] & mask)) {
                String generatedIp = generatedIpArray[0] + "." + generatedIpArray[1] + "." + generatedIpArray[2] + "." + generatedIpArray[3];
                Map<String, String> ipMap = new HashMap<>();
                ipMap.put("IP", generatedIp);
                parsedIpList.add(ipMap);
            }
        }

        return parsedIpList;
    }

}
