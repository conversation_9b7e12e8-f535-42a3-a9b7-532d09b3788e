package cn.iocoder.cloud.module.cloudedge.controller.admin.collectorplatform;

import cn.iocoder.cloud.module.cloudedge.controller.admin.collectorplatform.vo.*;
import cn.iocoder.cloud.module.cloudedge.convert.collectorplatform.CollectorPlatformConvert;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.collectorplatform.CollectorPlatformDO;
import cn.iocoder.cloud.module.cloudedge.service.collectorplatform.CollectorPlatformService;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.excel.core.util.ExcelUtils;
import cn.iocoder.zj.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.zj.framework.security.core.annotations.PreAuthenticated;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

@Tag(name = "管理后台 - 采集器平台关联")
@RestController
@RequestMapping("/cloudedge/collector-platform")
@Validated
public class CollectorPlatformController {

    @Resource
    private CollectorPlatformService collectorPlatformService;

    @PostMapping("/create")
    @Operation(summary = "创建采集器平台关联")
    @PreAuthenticated
    public CommonResult<Long> createCollectorPlatform(@Valid @RequestBody CollectorPlatformCreateReqVO createReqVO) {
        return success(collectorPlatformService.createCollectorPlatform(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新采集器平台关联")
    @PreAuthenticated
    public CommonResult<Boolean> updateCollectorPlatform(@Valid @RequestBody CollectorPlatformUpdateReqVO updateReqVO) {
        collectorPlatformService.updateCollectorPlatform(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除采集器平台关联")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthenticated
    public CommonResult<Boolean> deleteCollectorPlatform(@RequestParam("id") Long id) {
        collectorPlatformService.deleteCollectorPlatform(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得采集器平台关联")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthenticated
    public CommonResult<CollectorPlatformRespVO> getCollectorPlatform(@RequestParam("id") Long id) {
        CollectorPlatformDO collectorPlatform = collectorPlatformService.getCollectorPlatform(id);
        return success(CollectorPlatformConvert.INSTANCE.convert(collectorPlatform));
    }

    @GetMapping("/list")
    @Operation(summary = "获得采集器平台关联列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthenticated
    public CommonResult<List<CollectorPlatformRespVO>> getCollectorPlatformList(@RequestParam("ids") Collection<Long> ids) {
        List<CollectorPlatformDO> list = collectorPlatformService.getCollectorPlatformList(ids);
        return success(CollectorPlatformConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得采集器平台关联分页")
    @PreAuthenticated
    public CommonResult<PageResult<CollectorPlatformRespVO>> getCollectorPlatformPage(@Valid CollectorPlatformPageReqVO pageVO) {
        PageResult<CollectorPlatformDO> pageResult = collectorPlatformService.getCollectorPlatformPage(pageVO);
        return success(CollectorPlatformConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出采集器平台关联 Excel")
    @PreAuthenticated
    @OperateLog(type = EXPORT)
    public void exportCollectorPlatformExcel(@Valid CollectorPlatformExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<CollectorPlatformDO> list = collectorPlatformService.getCollectorPlatformList(exportReqVO);
        // 导出 Excel
        List<CollectorPlatformExcelVO> datas = CollectorPlatformConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "采集器平台关联.xls", "数据", CollectorPlatformExcelVO.class, datas);
    }

}
