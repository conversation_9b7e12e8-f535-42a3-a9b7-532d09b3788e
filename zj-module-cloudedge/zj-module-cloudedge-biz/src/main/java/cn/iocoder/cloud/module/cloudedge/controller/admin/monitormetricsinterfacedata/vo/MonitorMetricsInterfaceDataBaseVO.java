package cn.iocoder.cloud.module.cloudedge.controller.admin.monitormetricsinterfacedata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

/**
* 监控实时数据 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class MonitorMetricsInterfaceDataBaseVO {

    @Schema(description = "监控id", required = true)
    @NotNull(message = "监控id不能为空")
    private Long monitorId;

    @Schema(description = "指标", required = true)
    @NotNull(message = "指标不能为空")
    private String metrics;

    @Schema(description = "interfaceIndex", required = true)
    @NotNull(message = "interfaceIndex不能为空")
    private String interfaceIndex;

    @Schema(description = "接口名称", required = true)
    @NotNull(message = "接口名称不能为空")
    private String interfaceName;

    @Schema(description = "接口描述", required = true)
    @NotNull(message = "接口描述不能为空")
    private String alias;

    @Schema(description = "管理口状态")
    private String adminStatus;

    @Schema(description = "操作口状态")
    private String operStatus;

}
