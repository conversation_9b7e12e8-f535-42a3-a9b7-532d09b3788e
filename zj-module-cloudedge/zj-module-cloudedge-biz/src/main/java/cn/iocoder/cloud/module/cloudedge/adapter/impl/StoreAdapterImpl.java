package cn.iocoder.cloud.module.cloudedge.adapter.impl;

import cn.iocoder.cloud.module.cloudedge.adapter.AbstractMonitorAdapter;
import cn.iocoder.cloud.module.cloudedge.adapter.MonitorType;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StoreAdapterImpl extends AbstractMonitorAdapter {
    @Override
    protected void processMetricsData(Monitor monitor, CollectRep.MetricsData metricsData) {

    }

    @Override
    public MonitorType getMonitorType() {
        return MonitorType.STORES;
    }

    @Override
    public Map<String, List<Map<String, Object>>> handleMonitorTop10MetricsData(CollectRep.MetricsData metricsData) {
        return buildTop10MetricsData(metricsData, 
                "address",
                "capacity", "available");
    }
}
