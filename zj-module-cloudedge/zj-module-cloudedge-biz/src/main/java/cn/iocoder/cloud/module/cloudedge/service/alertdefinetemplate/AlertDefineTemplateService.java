package cn.iocoder.cloud.module.cloudedge.service.alertdefinetemplate;

import java.util.*;
import javax.validation.*;
import cn.iocoder.cloud.module.cloudedge.controller.admin.alertdefinetemplate.vo.*;
import cn.iocoder.cloud.module.cloudedge.dal.dataobject.alertdefinetemplate.AlertDefineTemplateDO;
import cn.iocoder.zj.framework.common.pojo.PageResult;

/**
 * 多维告警模板 Service 接口
 *
 * <AUTHOR>
 */
public interface AlertDefineTemplateService {

    /**
     * 创建多维告警模板
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAlertDefineTemplate(@Valid AlertDefineTemplateCreateReqVO createReqVO);

    /**
     * 更新多维告警模板
     *
     * @param updateReqVO 更新信息
     */
    void updateAlertDefineTemplate(@Valid AlertDefineTemplateUpdateReqVO updateReqVO);

    /**
     * 删除多维告警模板
     *
     * @param id 编号
     */
    void deleteAlertDefineTemplate(Long id);

    /**
     * 获得多维告警模板
     *
     * @param id 编号
     * @return 多维告警模板
     */
    AlertDefineTemplateDO getAlertDefineTemplate(Long id);

    /**
     * 获得多维告警模板列表
     *
     * @param ids 编号
     * @return 多维告警模板列表
     */
    List<AlertDefineTemplateDO> getAlertDefineTemplateList(Collection<Long> ids);

    /**
     * 获得多维告警模板分页
     *
     * @param pageReqVO 分页查询
     * @return 多维告警模板分页
     */
    PageResult<AlertDefineTemplateDO> getAlertDefineTemplatePage(AlertDefineTemplatePageReqVO pageReqVO);

    /**
     * 获得多维告警模板列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 多维告警模板列表
     */
    List<AlertDefineTemplateDO> getAlertDefineTemplateList(AlertDefineTemplateExportReqVO exportReqVO);

}
