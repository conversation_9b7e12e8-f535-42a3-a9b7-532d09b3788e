package cn.iocoder.cloud.module.cloudedge.dal.dataobject.herzbeat;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 系统配置
 *
 * <AUTHOR>
 * @since 4/7/2023
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SystemConfig {

    /**
     * system time zone
     * 系统时区
     */
    private String timeZoneId;

    /**
     * system locale language region
     * 系统语言地区
     */
    private String locale;

    /**
     * layout ui theme
     */
    private String theme;
}
