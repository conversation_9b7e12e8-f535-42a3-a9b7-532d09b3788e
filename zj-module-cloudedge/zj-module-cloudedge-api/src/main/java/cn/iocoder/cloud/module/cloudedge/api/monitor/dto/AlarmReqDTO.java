package cn.iocoder.cloud.module.cloudedge.api.monitor.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class AlarmReqDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "告警定义")
    private Long alertDefineId;

    @Schema(description = "告警消息")
    private String content;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "第一次告警时间")
    private Long firstAlarmTime;

    @Schema(description = "创建时间")
    private Date gmtCreate;

    @Schema(description = "修改时间")
    private Date gmtUpdate;

    @Schema(description = "最近告警时间")
    private Long lastAlarmTime;

    @Schema(description = "修改人")
    private String modifier;

    @Schema(description = "告警级别，0严重  1警告  2提示")
    private Integer priority;

    @Schema(description = "是否已读")
    private Integer status;

    @Schema(description = "标签")
    private String tags;

    @Schema(description = "告警源")
    private String target;

    @Schema(description = "告警次数")
    private Integer times;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "平台Id")
    private Long platformId;

    @Schema(description = "工单流程状态0未创建工单，1未解决，2已解决")
    private Integer isSolved;

    @Schema(description = "告警资源uuid")
    private String monitorId;

    @Schema(description = " 0:云平台 cloud，1:非云平台")
    private Integer resourceType;

    @Schema(description = "告警资源类型")
    private String app;

    @Schema(description = "告警配置id")
    private Long alarmId;

    @Schema(description = "告警资源名称")
    private String monitorName;
    /**
     * 以下为冗余字段
     */
    @Schema(description = "告警值")
    private Double value;

    @Schema(description = "收敛次数")
    private Long alarmTime;

    @Schema(description = "告警资源名称")
    private String alarmName;

    @Schema(description = "告警配置名称")
    private String alarmConfigName;

    @Schema(description = "告警规则")
    private String alarmRule;
}
