package cn.iocoder.cloud.module.cloudedge.api.monitor.dto;

import lombok.Data;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.apache.hertzbeat.common.entity.manager.Param;

import java.util.List;

/**
 * @ClassName : MonitorDTO  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/6/7  11:24
 */
@Data
public class MonitorDTO {
    private Monitor monitor;

    private List<Param> params;

    private List<String> metrics;

    private boolean detected;

    private String collector;
}
