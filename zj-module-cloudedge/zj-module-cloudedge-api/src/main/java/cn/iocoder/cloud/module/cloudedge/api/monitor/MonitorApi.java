package cn.iocoder.cloud.module.cloudedge.api.monitor;

import cn.iocoder.cloud.module.cloudedge.api.monitor.dto.AlarmReqDTO;
import cn.iocoder.cloud.module.cloudedge.api.monitor.dto.MonitorDTO;
import cn.iocoder.cloud.module.cloudedge.api.monitor.dto.MonitorHierarchy;
import cn.iocoder.cloud.module.cloudedge.api.monitor.dto.MonitorReq;
import cn.iocoder.cloud.module.cloudedge.enums.ApiConstants;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.apache.hertzbeat.common.entity.manager.Param;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@FeignClient(name = ApiConstants.NAME) //
@Tag(name = "RPC 服务 - 查询授权")
public interface MonitorApi {
    String PREFIX = ApiConstants.PREFIX + "/monitor";

    @PostMapping(PREFIX + "/selectMonitorInfo")
    @Operation(summary = "查询监控详情")
    MonitorDTO selectMonitorInfo(@Valid @RequestParam("id") Long id);

    @PostMapping(PREFIX + "/selectMonitorParamInfo")
    @Operation(summary = "查询监控字段详情")
    List<Param> selectMonitorParamInfo(@Valid @RequestParam("id") Long id);

    @PostMapping(PREFIX + "/getMonitorByplatformId")
    @Operation(summary = "根据平台id查询列表数据")
    List<Monitor> getMonitorByplatformId(@Valid @RequestParam("platformId") Long platformId);

    @PostMapping(PREFIX + "/getMonitorByplatformIdAndCategory")
    @Operation(summary = "根据平台id查询列表数据")
    List<Monitor> getMonitorByplatformIdAndCategory(@Valid @RequestParam("platformId") Long platformId,
                                                    @Valid @RequestParam("category") String category);

    @PostMapping(PREFIX + "/updateMonitor")
    @Operation(summary = "更新平台名称")
    void updateMonitor(@Valid @RequestParam("id") Long id, @Valid @RequestParam("name") String name);

    @PostMapping(PREFIX + "/getAppHierarchy")
    @Operation(summary = "查询APP监控指标")
    List<Map<String, Object>> getAppHierarchy();

    @PostMapping(PREFIX + "/getMonitorHierarchy")
    @Operation(summary = "查询所有监控指标")
    List<MonitorHierarchy> getAllMonitorHierarchy();

    @PostMapping(PREFIX + "/getAppDefine")
    @Operation(summary = "根据监视器类型名称获取监视器结构定义")
    List<String> getAppDefine(@RequestParam(value = "app") String app);

    @PostMapping(PREFIX + "/getOsMonitorList")
    @Operation(summary = "查询操作系统监控指标")
    String getOsMonitorList(@RequestParam(value = "platformId", required = false) Long platformId,
                            @Valid @RequestParam("category") String category,
                            @Valid @RequestParam("limit") long limit);

    @PostMapping(PREFIX + "/getMonitorByCategoryList")
    @Operation(summary = "根据租户id查询列表数据")
    @Parameter(description = "categoryList 监控类型列表", required = true)
    CommonResult<List<Monitor>> getMonitorByTenantId(@RequestBody MonitorReq req);

    @PostMapping(PREFIX + "/getAlarmByMonitorId")
    @Operation(summary = "根据monitorId查询告警详情最新的一条数据返回")
    @Parameter(description = "Alert 获取详情", required = true)
    AlarmReqDTO getAlarmByMonitorId(@RequestParam(value = "monitorId", required = true) Long monitorId);
}
