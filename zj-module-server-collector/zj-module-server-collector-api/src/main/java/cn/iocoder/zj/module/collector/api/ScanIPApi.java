package cn.iocoder.zj.module.collector.api;

import cn.iocoder.zj.framework.common.dal.manager.ScanIPRequest;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.collector.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@Validated
@Tag(name = "IP扫描服务Api")
@FeignClient(name = ApiConstants.NAME)
public interface ScanIPApi {
    String PREFIX = ApiConstants.PREFIX + "/plat";

    @PostMapping(PREFIX + "/scanip")
    @Operation(summary = "ip扫描请求")
    CommonResult<String> scanIpChange(@RequestBody ScanIPRequest request);
}
