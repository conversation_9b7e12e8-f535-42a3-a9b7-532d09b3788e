package cn.iocoder.zj.module.collector.api;


import cn.iocoder.zj.module.collector.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 **/
@Validated
@Tag(name = "采集器平台服务Api")
@FeignClient(name = ApiConstants.NAME)
public interface PlatformInfoApi {

    String PREFIX = ApiConstants.PREFIX + "/plat";

    /**
     * 平台配置变更请求
     */
    @PostMapping(PREFIX + "/informationChange")
    @Operation(summary = "平台配置变更请求")
    @Parameter(name = "name", description = "采集器名称", required = true)
    void informationChange(@RequestParam("name") String name);

}
