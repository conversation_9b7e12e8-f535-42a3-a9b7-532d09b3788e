<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>cn.iocoder.cloud</groupId>
        <artifactId>zj</artifactId>
        <version>${revision}</version>
    </parent>

    <groupId>cn.iocoder.zj</groupId>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>zj-module-server-collector</artifactId>
    <packaging>pom</packaging>

    <name>${project.artifactId}</name> <!-- 3. 新增 name 为 ${project.artifactId} -->

    <description> <!-- 4. 新增 description 为该模块的描述 -->
        collector 模块
    </description>
    <modules>
        <module>zj-module-server-collector-biz</module>
        <module>zj-module-server-collector-api</module>
    </modules>

</project>