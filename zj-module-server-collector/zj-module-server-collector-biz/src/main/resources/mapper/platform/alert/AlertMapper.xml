<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.iocoder.zj.module.collector.mapper.AlertMapper">


    <select id="getAlarmByMonitorId" resultType="cn.iocoder.zj.module.collector.dal.alert.AlertDo">
        select * from  hzb_alert  WHERE monitor_id = #{monitorId} and is_solved = 0 ORDER BY id desc limit 1
    </select>
</mapper>
