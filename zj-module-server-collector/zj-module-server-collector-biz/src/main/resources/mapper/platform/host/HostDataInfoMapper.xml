<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.iocoder.zj.module.collector.mapper.HostDataInfoMapper">

    <insert id="add" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO monitor_hardware_info (
        uuid,
        name,
        state,
        ip,
        status,
        cluster_uuid,
        cluster_name,
        total_cpu_capacity,
        available_cpu_capacity,
        cpu_sockets,
        architecture,
        cpu_num,
        total_memory_capacity,
        available_memory_capacity,
        deleted,
        tenant_id,
        region_id,
        bandwidth_upstream,
        bandwidth_downstream,
        memory_used,
        cpu_used,
        packet_rate,
        disk_used,
        disk_used_bytes,
        disk_free_bytes,
        total_disk_capacity,
        platform_id,
        platform_name,
        vms,
        type_name,
        is_maintain,
        ipmi,
        manufacturer,
        total_virtual_memory,
        cpu_over_percent,
        memory_over_percent,
        manager,
        available_manager,
        cpu_commit_rate,
        memory_commit_rate,
        reserved_memory,
        cpu_type,
        brand_name,
        model,
        serial_number,tag
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.uuid},
            #{item.name},
            #{item.state},
            #{item.ip},
            #{item.status},
            #{item.clusterUuid},
            #{item.clusterName},
            #{item.totalCpuCapacity},
            #{item.availableCpuCapacity},
            #{item.cpuSockets},
            #{item.architecture},
            #{item.cpuNum},
            #{item.totalMemoryCapacity},
            #{item.availableMemoryCapacity},
            #{item.deleted},
            #{item.tenantId},
            #{item.regionId},
            #{item.bandwidthUpstream},
            #{item.bandwidthDownstream},
            #{item.memoryUsed},
            #{item.cpuUsed},
            #{item.packetRate},
            #{item.diskUsed},
            #{item.diskUsedBytes},
            #{item.diskFreeBytes},
            #{item.totalDiskCapacity},
            #{item.platformId},
            #{item.platformName},
            #{item.vms},
            #{item.typeName},
            #{item.isMaintain},
            #{item.ipmi},
            #{item.manufacturer},
            #{item.totalVirtualMemory},
            #{item.cpuOverPercent},
            #{item.memoryOverPercent},
            #{item.manager},
            #{item.availableManager},
            #{item.cpuCommitRate},
            #{item.memoryCommitRate},
            #{item.reservedMemory},
            #{item.cpuType},
            #{item.brandName},
            #{item.model},
            #{item.serialNumber},
            #{item.tag}
            )
        </foreach>
    </insert>

    <update id="updated" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE monitor_hardware_info
            SET
            name = #{item.name},
            state = #{item.state},
            ip = #{item.ip},
            status = #{item.status},
            cluster_uuid = #{item.clusterUuid},
            cluster_name = #{item.clusterName},
            total_cpu_capacity = #{item.totalCpuCapacity},
            available_cpu_capacity = #{item.availableCpuCapacity},
            cpu_sockets = #{item.cpuSockets},
            architecture = #{item.architecture},
            cpu_num = #{item.cpuNum},
            total_memory_capacity = #{item.totalMemoryCapacity},
            available_memory_capacity = #{item.availableMemoryCapacity},
            deleted = #{item.deleted},
            tenant_id = #{item.tenantId},
            region_id = #{item.regionId},
            bandwidth_upstream = #{item.bandwidthUpstream},
            bandwidth_downstream = #{item.bandwidthDownstream},
            memory_used = #{item.memoryUsed},
            cpu_used = #{item.cpuUsed},
            packet_rate = #{item.packetRate},
            disk_used = #{item.diskUsed},
            disk_used_bytes = #{item.diskUsedBytes},
            disk_free_bytes = #{item.diskFreeBytes},
            total_disk_capacity = #{item.totalDiskCapacity},
            platform_id = #{item.platformId},
            platform_name = #{item.platformName},
            vms = #{item.vms},
            type_name = #{item.typeName},
            is_maintain = #{item.isMaintain},
            ipmi = #{item.ipmi},
            manufacturer = #{item.manufacturer},
            total_virtual_memory = #{item.totalVirtualMemory},
            cpu_over_percent = #{item.cpuOverPercent},
            memory_over_percent = #{item.memoryOverPercent},
            manager = #{item.manager},
            available_manager = #{item.availableManager},
            cpu_commit_rate = #{item.cpuCommitRate},
            memory_commit_rate = #{item.memoryCommitRate},
            reserved_memory = #{item.reservedMemory},
            cpu_type = #{item.cpuType},
            brand_name = #{item.brandName},
            model = #{item.model},
            serial_number = #{item.serialNumber},
            tag = #{item.tag}
            WHERE uuid = #{item.uuid}
        </foreach>
    </update>


    <delete id="deleted" parameterType="java.util.List">
        delete from monitor_hardware_info
        where uuid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>



    <select id="selectByIdPlatformList" resultType="cn.iocoder.zj.framework.common.dal.manager.HostData">
        SELECT
            id AS id,
            uuid AS uuid,
            name AS name,
            state AS state,
            ip AS ip,
            status AS status,
            cluster_uuid AS clusterUuid,
            cluster_name AS clusterName,
            total_cpu_capacity AS totalCpuCapacity,
            available_cpu_capacity AS availableCpuCapacity,
            cpu_sockets AS cpuSockets,
            architecture AS architecture,
            cpu_num AS cpuNum,
            total_memory_capacity AS totalMemoryCapacity,
            available_memory_capacity AS availableMemoryCapacity,
            region_id AS regionId,
            tenant_id AS tenantId,
            bandwidth_upstream AS bandwidthUpstream,
            bandwidth_downstream AS bandwidthDownstream,
            memory_used AS memoryUsed,
            cpu_used AS cpuUsed,
            packet_rate AS packetRate,
            disk_used AS diskUsed,
            disk_used_bytes AS diskUsedBytes,
            disk_free_bytes AS diskFreeBytes,
            total_disk_capacity AS totalDiskCapacity,
            platform_id AS platformId,
            platform_name AS platformName,
            deleted AS deleted,
            type_name AS typeName,
            is_maintain AS isMaintain,
            create_time AS createTime,
            vms AS vms,
            ipmi AS ipmi,
            manufacturer AS manufacturer,
            total_virtual_memory AS totalVirtualMemory,
            cpu_over_percent AS cpuOverPercent,
            memory_over_percent AS memoryOverPercent,
            manager AS manager,
            available_manager AS availableManager,
            cpu_commit_rate AS cpuCommitRate,
            memory_commit_rate AS memoryCommitRate,
            reserved_memory AS reservedMemory,
            brand_name AS brandName,
            model AS model,
            serial_number AS serialNumber,
            cpu_type AS cpuType
        FROM
            monitor_hardware_info
        WHERE
            platform_id = #{platformId}
          AND deleted = 0
    </select>


</mapper>
