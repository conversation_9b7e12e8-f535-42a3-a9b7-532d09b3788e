<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.iocoder.zj.module.collector.mapper.PlatformInfoMapper">


    <update id="updatePlatformInfo">
        update system_platform_config set
            state = #{state},
            diff_time = #{diffTime},
            online_time = #{dateTime}
        where id = #{platformId}
    </update>

    <select id="getPlatformInfos" resultType="cn.iocoder.zj.module.collector.dal.platform.Platform">
        select
            id as platformId,
            username as username,
            password as password,
            url as platformUrl,
            name as platformName,
            type_code as typeCode,
            region_id as regionId,
            state as state
        from system_platform_config
        where deleted = 0 and type_code !='dummy'
    </select>
    <select id="getPlatformByTenantName" resultType="cn.iocoder.zj.module.collector.dal.platform.Platform">
        SELECT spc.id as platformId,spc.username as username,spc.password as password,
               spc.url as platformUrl,spc.name as platformName,spc.type_code as typeCode,spc.region_id as regionId,
               spc.state as state,spc.ak_type as akType FROM hzb_collector_platform hcp
                                           LEFT JOIN system_platform_config spc on spc.id  = hcp.platform_id
        WHERE hcp.collector_name = #{name} and spc.deleted =0 and spc.type_code !='dummy'
    </select>

    <update id="updateScanIP" parameterType="java.util.List">
        update ip_scan_result
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="`snmp_status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.snmpStatus != null and (item.snmpStatus == 1 or item.snmpStatus == 2)">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.snmpStatus,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`tcp_status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tcpStatus != null and (item.tcpStatus == 1 or item.tcpStatus == 2)">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.tcpStatus,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ping_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.pingStatus != null and (item.pingStatus == 1 or item.pingStatus == 2)">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.pingStatus,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            update_time = now(),
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateScanIPState">
        update ip_range set
            state = 2,update_time = now()
        where id = #{ipRangeId}
    </update>
</mapper>
