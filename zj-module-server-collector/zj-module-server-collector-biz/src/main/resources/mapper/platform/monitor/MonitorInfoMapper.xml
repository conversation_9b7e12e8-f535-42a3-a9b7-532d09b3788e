<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.iocoder.zj.module.collector.mapper.MonitorInfoMapper">


    <update id="updateHostStateByPlatformId">
        update monitor_hardware_info
        set state  = 'Disabled',
            status = 'Disconnected'
        where platform_id = #{platformId}
    </update>


    <update id="updateVmStateByPlatformId">
        update monitor_host_info
        set state  = 'Stopped'
        where platform_id = #{platformId}
    </update>

    <update id="updateStorageByPlatformId">
        update monitor_storage_info
        set status = 'Disconnected',
            state  = 'Disabled'
        where platform_id = #{platformId}
    </update>
</mapper>
