<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.iocoder.zj.module.collector.mapper.VmDataInfoMapper">

    <!-- 根据平台ID查询虚拟机列表 -->
    <select id="selectByIdPlatformList" resultType="cn.iocoder.zj.framework.common.dal.manager.VmData">
        SELECT *
        FROM monitor_host_info
        WHERE platform_id = #{platformId}
          AND deleted = 0
    </select>

    <!-- 批量新增虚拟机 -->
    <insert id="add">
        INSERT INTO monitor_host_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            uuid,name,state,ip,vip_ip,zone_uuid,cluster_uuid,hardware_uuid,
            architecture,guest_os_type,v_create_date,disk_used,cpu_used,
            memory_used,type,memory_size,cpu_num,mac,image_uuid,tenant_id,
            region_id,zone_name,cluster_name,hardware_name,network_in_bytes,
            network_out_bytes,disk_used_bytes,disk_free_bytes,total_disk_capacity,
            actual_size,cloud_size,network_in_packets,network_out_packets,
            platform_id,platform_name,deleted,type_name,vms,authorization_type,
            authorization_time,last_access_time,power_state,image_name,iso,
            auto_init_type,guide_mode,tools_installed,tools_run_status,tag
        </trim>
        VALUES
        <foreach collection="list" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.uuid},#{item.name},#{item.state},#{item.ip},#{item.vipIp},
                #{item.zoneUuid},#{item.clusterUuid},#{item.hardwareUuid},
                #{item.architecture},#{item.guestOsType},#{item.vCreateDate},
                #{item.diskUsed},#{item.cpuUsed},#{item.memoryUsed},#{item.type},
                #{item.memorySize},#{item.cpuNum},#{item.mac},#{item.imageUuid},
                #{item.tenantId},#{item.regionId},#{item.zoneName},
                #{item.clusterName},#{item.hardwareName},#{item.networkInBytes},
                #{item.networkOutBytes},#{item.diskUsedBytes},#{item.diskFreeBytes},
                #{item.totalDiskCapacity},#{item.actualSize},#{item.cloudSize},
                #{item.networkInPackets},#{item.networkOutPackets},
                #{item.platformId},#{item.platformName},#{item.deleted},
                #{item.typeName},#{item.vms},#{item.authorizationType},
                #{item.authorizationTime},#{item.lastAccessTime},
                #{item.powerState},#{item.imageName},#{item.iso},
                #{item.autoInitType},#{item.guideMode},#{item.toolsInstalled},#{item.toolsRunStatus},#{item.tag}
            </trim>
        </foreach>
    </insert>

    <!-- 批量物理删除虚拟机 -->
    <delete id="deleted">
        DELETE FROM monitor_host_info
        WHERE uuid IN
        <foreach collection="list" item="uuid" open="(" separator="," close=")">
            #{uuid}
        </foreach>
    </delete>

    <!-- 批量更新虚拟机信息 -->
    <update id="updated">
        <foreach collection="list" item="item" separator=";">
            UPDATE monitor_host_info
            <set>
                <if test="item.name != null">name = #{item.name},</if>
                <if test="item.state != null">state = #{item.state},</if>
                <if test="item.ip != null">ip = #{item.ip},</if>
                <if test="item.vipIp != null">vip_ip = #{item.vipIp},</if>
                <if test="item.zoneUuid != null">zone_uuid = #{item.zoneUuid},</if>
                <if test="item.clusterUuid != null">cluster_uuid = #{item.clusterUuid},</if>
                <if test="item.hardwareUuid != null">hardware_uuid = #{item.hardwareUuid},</if>
                <if test="item.architecture != null">architecture = #{item.architecture},</if>
                <if test="item.guestOsType != null">guest_os_type = #{item.guestOsType},</if>
                <if test="item.diskUsed != null">disk_used = #{item.diskUsed},</if>
                <if test="item.cpuUsed != null">cpu_used = #{item.cpuUsed},</if>
                <if test="item.memoryUsed != null">memory_used = #{item.memoryUsed},</if>
                <if test="item.type != null">type = #{item.type},</if>
                <if test="item.memorySize != null">memory_size = #{item.memorySize},</if>
                <if test="item.cpuNum != null">cpu_num = #{item.cpuNum},</if>
                <if test="item.mac != null">mac = #{item.mac},</if>
                <if test="item.imageUuid != null">image_uuid = #{item.imageUuid},</if>
                <if test="item.tenantId != null">tenant_id = #{item.tenantId},</if>
                <if test="item.regionId != null">region_id = #{item.regionId},</if>
                <if test="item.zoneName != null">zone_name = #{item.zoneName},</if>
                <if test="item.clusterName != null">cluster_name = #{item.clusterName},</if>
                <if test="item.hardwareName != null">hardware_name = #{item.hardwareName},</if>
                <if test="item.networkInBytes != null">network_in_bytes = #{item.networkInBytes},</if>
                <if test="item.networkOutBytes != null">network_out_bytes = #{item.networkOutBytes},</if>
                <if test="item.diskUsedBytes != null">disk_used_bytes = #{item.diskUsedBytes},</if>
                <if test="item.diskFreeBytes != null">disk_free_bytes = #{item.diskFreeBytes},</if>
                <if test="item.totalDiskCapacity != null">total_disk_capacity = #{item.totalDiskCapacity},</if>
                <if test="item.actualSize != null">actual_size = #{item.actualSize},</if>
                <if test="item.cloudSize != null">cloud_size = #{item.cloudSize},</if>
                <if test="item.networkInPackets != null">network_in_packets = #{item.networkInPackets},</if>
                <if test="item.networkOutPackets != null">network_out_packets = #{item.networkOutPackets},</if>
                <if test="item.platformId != null">platform_id = #{item.platformId},</if>
                <if test="item.platformName != null">platform_name = #{item.platformName},</if>
                <if test="item.typeName != null">type_name = #{item.typeName},</if>
                <if test="item.authorizationType != null">authorization_type = #{item.authorizationType},</if>
                <if test="item.authorizationTime != null">authorization_time = #{item.authorizationTime},</if>
                <if test="item.lastAccessTime != null">last_access_time = #{item.lastAccessTime},</if>
                <if test="item.powerState != null">power_state = #{item.powerState},</if>
                <if test="item.imageName != null">image_name = #{item.imageName},</if>
                <if test="item.iso != null">iso = #{item.iso},</if>
                <if test="item.autoInitType != null">auto_init_type = #{item.autoInitType},</if>
                <if test="item.guideMode != null">guide_mode = #{item.guideMode},</if>
                <if test="item.vms != null">vms = #{item.vms},</if>
                <if test="item.vCreateDate != null">v_create_date = #{item.vCreateDate},</if>
                <if test="item.toolsRunStatus != null">tools_run_status = #{item.toolsRunStatus},</if>
                <if test="item.toolsInstalled != null">tools_installed = #{item.toolsInstalled},</if>
                <if test="item.tag != null">tag = #{item.tag}</if>
            </set>
            WHERE uuid = #{item.uuid}
        </foreach>
    </update>


</mapper>
