package cn.iocoder.zj.module.collector.service.impl;

import cn.iocoder.zj.framework.common.dal.manager.NetWorkL2Data;
import cn.iocoder.zj.module.collector.mapper.NetWorkL2DataInfoMapper;
import cn.iocoder.zj.module.collector.service.NetWorkL2DataInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Service
public class NetWorkL2DataInfoImpl implements NetWorkL2DataInfo {

    @Resource
    NetWorkL2DataInfoMapper netWorkL2DataInfoMapper;

    @Override
    public List<NetWorkL2Data> selectByIdPlatformList(Long platformId) {
        return netWorkL2DataInfoMapper.selectList(new QueryWrapper<NetWorkL2Data>().eq("platform_id", platformId));
    }

    @Override
    public void add(List<NetWorkL2Data> added) {
      for (NetWorkL2Data addedData : added) {
          netWorkL2DataInfoMapper.insert(addedData);
      }
    }

    @Override
    public void deleted(List<String> deletList) {
        for (String id : deletList) {
            netWorkL2DataInfoMapper.delete(new QueryWrapper<NetWorkL2Data>().eq("uuid", id));
        }

    }

    @Override
    public void updated(List<NetWorkL2Data> updated) {
        for (NetWorkL2Data updatedData : updated) {
            updatedData.setUpdateTime(new Date());
            netWorkL2DataInfoMapper.update(updatedData, new QueryWrapper<NetWorkL2Data>().eq("uuid", updatedData.getUuid()));
        }
    }
}
