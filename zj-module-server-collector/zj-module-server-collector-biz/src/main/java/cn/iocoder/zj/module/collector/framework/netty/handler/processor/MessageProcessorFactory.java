/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.zj.module.collector.framework.netty.handler.processor;

import cn.iocoder.zj.framework.common.message.ClusterMsg;
import cn.iocoder.zj.framework.warehouse.store.history.HistoryDataWriter;
import cn.iocoder.zj.module.collector.framework.kafka.KafkaProducerService;
import cn.iocoder.zj.module.collector.framework.netty.task.TaskHelper;
import cn.iocoder.zj.module.collector.service.PlatformInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 消息处理器工厂
 * 负责根据消息类型选择合适的处理器
 * 
 * <AUTHOR>
 */
@Slf4j
public class MessageProcessorFactory {
    
    private final ConcurrentMap<ClusterMsg.MessageType, MessageProcessor> processors = new ConcurrentHashMap<>();
    private final PerformanceDataProcessor performanceDataProcessor;
    private final OtherProcessor otherProcessor;

    public MessageProcessorFactory(PlatformInfo platformInfo, 
                                 HistoryDataWriter historyDataWriter, 
                                 TaskHelper taskHelper, 
                                 KafkaProducerService producerService, 
                                 RedisTemplate<String, Object> redisTemplate) {
        
        // 创建处理器实例
        performanceDataProcessor = new PerformanceDataProcessor(historyDataWriter, producerService);
        otherProcessor = new OtherProcessor(platformInfo, producerService, redisTemplate);
        
        // 注册处理器
        initializeProcessors(platformInfo, taskHelper);
    }
    
    /**
     * 初始化处理器映射
     */
    private void initializeProcessors(PlatformInfo platformInfo, TaskHelper taskHelper) {
        // 认证处理器
        processors.put(ClusterMsg.MessageType.AUTH, new AuthProcessor(platformInfo, taskHelper));
        
        // 基础数据处理器
        processors.put(ClusterMsg.MessageType.BASIC, new BasicDataProcessor());
        
        // 性能数据处理器
        processors.put(ClusterMsg.MessageType.CPU_TASK, performanceDataProcessor);
        processors.put(ClusterMsg.MessageType.DISK_TASK, performanceDataProcessor);
        processors.put(ClusterMsg.MessageType.MEM_TASK, performanceDataProcessor);
        processors.put(ClusterMsg.MessageType.NET_TASK, performanceDataProcessor);
        
        // 其他处理器
        processors.put(ClusterMsg.MessageType.UPDATE, otherProcessor);
        processors.put(ClusterMsg.MessageType.SCHEDULED_INFO, otherProcessor);
        processors.put(ClusterMsg.MessageType.DETECT, otherProcessor);
        processors.put(ClusterMsg.MessageType.FAIL, otherProcessor);
        processors.put(ClusterMsg.MessageType.ALERTER, otherProcessor);
        processors.put(ClusterMsg.MessageType.OTHER, otherProcessor);
        
        log.info("[处理器工厂] 初始化完成，共注册 {} 个处理器", processors.size());
    }
    
    /**
     * 根据消息类型获取对应的处理器
     * 
     * @param messageType 消息类型
     * @return 消息处理器，如果没有找到则返回null
     */
    public MessageProcessor getProcessor(ClusterMsg.MessageType messageType) {
        return processors.get(messageType);
    }
    
    /**
     * 注册自定义处理器
     * 
     * @param messageType 消息类型
     * @param processor 处理器实例
     */
    public void registerProcessor(ClusterMsg.MessageType messageType, MessageProcessor processor) {
        processors.put(messageType, processor);
        log.info("[处理器工厂] 注册自定义处理器，消息类型: {}, 处理器: {}", 
                messageType, processor.getClass().getSimpleName());
    }
    
    /**
     * 获取所有已注册的处理器数量
     * 
     * @return 处理器数量
     */
    public int getProcessorCount() {
        return processors.size();
    }
    
    /**
     * 检查是否支持指定的消息类型
     * 
     * @param messageType 消息类型
     * @return 是否支持
     */
    public boolean supports(ClusterMsg.MessageType messageType) {
        return processors.containsKey(messageType);
    }
} 