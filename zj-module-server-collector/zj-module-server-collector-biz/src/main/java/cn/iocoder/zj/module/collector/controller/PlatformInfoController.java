package cn.iocoder.zj.module.collector.controller;

import cn.iocoder.zj.framework.common.message.ClusterMsg;
import cn.iocoder.zj.module.collector.api.PlatformInfoApi;
import cn.iocoder.zj.module.collector.dal.platform.Platform;
import cn.iocoder.zj.module.collector.service.PlatformInfo;
import com.google.gson.Gson;
import io.netty.channel.ChannelHandlerContext;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.zj.module.collector.framework.netty.server.ChannelCtxMap.getChannelCtxByClientId;

/**
 * 平台
 *
 * <AUTHOR>
 **/
@RestController
public class PlatformInfoController implements PlatformInfoApi {

    @Resource
    PlatformInfo platformInfo;

    /**
     * 平台配置变更请求
     */
    @Override
    public void informationChange(String name) {
        List<Platform> platformInfos = platformInfo.getPlatformByTenantName(name);
        List<ChannelHandlerContext> channelCtxByClientId = getChannelCtxByClientId(name);
        for (ChannelHandlerContext channelHandlerContext : channelCtxByClientId) {
            ClusterMsg.Message messageInfo = ClusterMsg.Message.newBuilder()
                    .setClientId(channelHandlerContext.channel().id().asLongText())
                    .setType(ClusterMsg.MessageType.UPDATE)
                    .setData(new Gson().toJson(platformInfos)).build();
            channelHandlerContext.channel().writeAndFlush(messageInfo);
        }
    }


}
