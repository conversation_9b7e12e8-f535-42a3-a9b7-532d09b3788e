package cn.iocoder.zj.module.collector.util;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

public class DataSetAnalyzerUtils {

    /**
     * 比较两个集合，返回新增、修改和删除的数据
     *
     * @param oldList 旧集合
     * @param newList 新集合
     * @param <T>     集合的元素类型
     * @return 一个 Map 包含新增、修改、删除的集合
     */
    public static <T> Map<String, List<T>> compareCollections(List<T> oldList, List<T> newList) {
        Map<String, List<T>> result = new HashMap<>();
        // 交集：即修改的数据
        List<T> intersection = oldList.stream()
                .filter(newList::contains)
                .collect(Collectors.toList());
        // 差集：即删除的数据
        List<T> deleted = oldList.stream()
                .filter(item -> !newList.contains(item))
                .collect(Collectors.toList());
        // 补集：即新增的数据
        List<T> added = newList.stream()
                .filter(item -> !oldList.contains(item))
                .collect(Collectors.toList());
        // 将结果存入 Map
        result.put("added", added);
        result.put("deleted", deleted);
        result.put("updated", intersection);
        return result;
    }

     /**
     * 根据指定字段比较两个集合，返回新增、修改和删除的数据
     *
     * @param oldList 旧集合
     * @param newList 新集合
     * @param keyExtractor 用于提取比较字段的函数
     * @return 包含新增、修改、删除数据的Map
     */
    public static <T, U> Map<String, List<T>> compareCollectionsByKey(List<T> oldList,List<T> newList,Function<T, U> keyExtractor) {
        Map<String, List<T>> result = new HashMap<>();
        
        // 创建新旧集合的key映射
        Map<U, T> oldMap = oldList.stream().collect(Collectors.toMap(keyExtractor, item -> item, (a, b) -> a));
        Map<U, T> newMap = newList.stream().collect(Collectors.toMap(keyExtractor, item -> item, (a, b) -> a));
        
        // 获取所有key
        Set<U> oldKeys = oldMap.keySet();
        Set<U> newKeys = newMap.keySet();
        
        // 查找更新的数据（key相同的数据）
        List<T> updated = oldKeys.stream().filter(newKeys::contains).map(newMap::get).collect(Collectors.toList());
        
        // 查找删除的数据（在旧数据中有，新数据中没有的）
        List<T> deleted = oldKeys.stream().filter(key -> !newKeys.contains(key)).map(oldMap::get).collect(Collectors.toList());
        
        // 查找新增的数据（在新数据中有，旧数据中没有的）
        List<T> added = newKeys.stream().filter(key -> !oldKeys.contains(key)).map(newMap::get).collect(Collectors.toList());
        
        result.put("added", added);
        result.put("deleted", deleted);
        result.put("updated", updated);
        
        return result;
    }


     /**
     * 根据多个指定字段比较两个集合，返回新增、修改和删除的数据
     *
     * @param oldList 旧集合
     * @param newList 新集合
     * @param keyExtractors 用于提取比较字段的函数列表
     * @return 包含新增、修改、删除数据的Map
     */
    @SafeVarargs
    public static <T, U> Map<String, List<T>> compareCollectionsByMultiKeys(List<T> oldList,
                                                                          List<T> newList,
                                                                          Function<T, U>... keyExtractors) {
        Map<String, List<T>> result = new HashMap<>();
        
        // 创建新旧集合的复合key映射
        Map<String, T> oldMap = oldList.stream()
                .collect(Collectors.toMap(
                        item -> generateCompositeKey(item, keyExtractors),
                        item -> item,
                        (a, b) -> a));
        
        Map<String, T> newMap = newList.stream()
                .collect(Collectors.toMap(
                        item -> generateCompositeKey(item, keyExtractors),
                        item -> item,
                        (a, b) -> a));
        
        // 获取所有复合key
        Set<String> oldKeys = oldMap.keySet();
        Set<String> newKeys = newMap.keySet();
        
        // 查找更新的数据（复合key相同的数据）
        List<T> updated = oldKeys.stream()
                .filter(newKeys::contains)
                .map(newMap::get)
                .collect(Collectors.toList());
        
        // 查找删除的数据
        List<T> deleted = oldKeys.stream()
                .filter(key -> !newKeys.contains(key))
                .map(oldMap::get)
                .collect(Collectors.toList());
        
        // 查找新增的数据
        List<T> added = newKeys.stream()
                .filter(key -> !oldKeys.contains(key))
                .map(newMap::get)
                .collect(Collectors.toList());
        
        result.put("added", added);
        result.put("deleted", deleted);
        result.put("updated", updated);
        
        return result;
    }

    /**
     * 生成复合键
     */
    @SafeVarargs
    private static <T, U> String generateCompositeKey(T item, Function<T, U>... keyExtractors) {
        return Arrays.stream(keyExtractors)
                .map(extractor -> String.valueOf(extractor.apply(item)))
                .collect(Collectors.joining("_"));
    }

    public static <T, K> Map<String, List<T>> compareCollectionsByKey( List<T> oldList,List<T> newList,Function<T, K> keyExtractor,BiConsumer<T, T> copyOldFieldsIfUpdated) {
        Map<String, List<T>> result = new HashMap<>();

        Map<K, T> oldMap = oldList.stream().collect(Collectors.toMap(keyExtractor, Function.identity(), (a, b) -> a));
        Map<K, T> newMap = newList.stream().collect(Collectors.toMap(keyExtractor, Function.identity(), (a, b) -> a));

        Set<K> oldKeys = oldMap.keySet();
        Set<K> newKeys = newMap.keySet();

        List<T> added = newKeys.stream().filter(k -> !oldKeys.contains(k)).map(newMap::get).collect(Collectors.toList());

        List<T> deleted = oldKeys.stream().filter(k -> !newKeys.contains(k)).map(oldMap::get).collect(Collectors.toList());

        List<T> updated = oldKeys.stream()
                .filter(newKeys::contains)
                .map(k -> {
                    T oldObj = oldMap.get(k);
                    T newObj = newMap.get(k);
                    copyOldFieldsIfUpdated.accept(newObj, oldObj);
                    return newObj;
                })
                .collect(Collectors.toList());

        result.put("added", added);
        result.put("deleted", deleted);
        result.put("updated", updated);

        return result;
    }

}
