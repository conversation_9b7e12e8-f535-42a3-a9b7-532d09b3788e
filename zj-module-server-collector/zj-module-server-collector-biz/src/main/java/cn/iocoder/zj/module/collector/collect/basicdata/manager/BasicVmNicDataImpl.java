package cn.iocoder.zj.module.collector.collect.basicdata.manager;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.zj.framework.common.dal.manager.VmNicData;
import cn.iocoder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.iocoder.zj.module.collector.service.VmNicDataInfo;
import cn.iocoder.zj.module.collector.util.DataSetAnalyzerUtils;
import cn.iocoder.zj.module.collector.util.OptimizedDataConverter;
import cn.iocoder.zj.module.collector.util.SpringBeanUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VM_VIC;

@Slf4j
public class BasicVmNicDataImpl extends AbstractBasicData {
    @Override
    public void preCheck() {

    }

    @Override
    public void collectBasicData(List<?> results,String type) {
        OptimizedDataConverter converter = SpringBeanUtils.getBean(OptimizedDataConverter.class);


        List<VmNicData> dataList =converter.convertList(results,VmNicData.class);
        VmNicDataInfo bean = SpringBeanUtils.getBean(VmNicDataInfo.class);
        List<VmNicData> oldVmData = bean.selectByIdPlatformList(dataList.get(0).getPlatformId());
        Map<String, List<VmNicData>> result = DataSetAnalyzerUtils.compareCollectionsByKey(oldVmData, dataList, VmNicData::getUuid);

        List<VmNicData> added = result.get("added");
        if (CollectionUtil.isNotEmpty(added)) {
            bean.add(added);
        }
        List<VmNicData> deleted = result.get("deleted");
        if (CollectionUtil.isNotEmpty(deleted)) {
            List<String> deletList = deleted.stream().map(VmNicData::getUuid).collect(Collectors.toList());
            bean.deleted(deletList);
        }
        List<VmNicData> updated = result.get("updated");
        if (CollectionUtil.isNotEmpty(updated)) {
            bean.updated(updated);
        }
    }

    @Override
    public String supportProtocol() {
        return BASIC_VM_VIC.code();
    }
}
