package cn.iocoder.zj.module.collector.collect.basicdata.manager;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.VolumeInfoData;
import cn.iocoder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.iocoder.zj.module.collector.service.VolumeDataInfo;
import cn.iocoder.zj.module.collector.util.DataSetAnalyzerUtils;
import cn.iocoder.zj.module.collector.util.OptimizedDataConverter;
import cn.iocoder.zj.module.collector.util.SpringBeanUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VOLUME_INFO;

@Slf4j
public class BasicVolumeInfoDataImpl extends AbstractBasicData {

    @Override
    public void preCheck() {

    }

    @Override
    public void collectBasicData(List<?> results, String type) {
        OptimizedDataConverter converter = SpringBeanUtils.getBean(OptimizedDataConverter.class);

        List<VolumeInfoData> dataList = converter.convertList(results,VolumeInfoData.class);
        VolumeDataInfo bean = SpringBeanUtils.getBean(VolumeDataInfo.class);
        List<VolumeInfoData> oldVmData = bean.selectByIdPlatformList(Convert.toLong(dataList.get(0).getPlatformId()));

        if(StrUtil.isNotEmpty(type) && type.equals("basic_type_sxf")){
            if (CollectionUtil.isNotEmpty(oldVmData)) {
                bean.deleted(oldVmData.stream()
                        .map(VolumeInfoData::getUuid)
                        .collect(Collectors.toList()));
            }

            if (CollectionUtil.isNotEmpty(dataList)) {
                bean.add(dataList);
            }
        }else {
            Map<String, List<VolumeInfoData>> result = DataSetAnalyzerUtils.compareCollectionsByKey(oldVmData, dataList,VolumeInfoData::getUuid,(newObj, oldObj) -> newObj.setId(oldObj.getId()) );
            List<VolumeInfoData> added = result.get("added");
            if (CollectionUtil.isNotEmpty(added)) {
                bean.add(added);
            }
            List<VolumeInfoData> deleted = result.get("deleted");
            if (CollectionUtil.isNotEmpty(deleted)) {
                List<String> deletList = deleted.stream().map(VolumeInfoData::getUuid).collect(Collectors.toList());
                bean.deleted(deletList);
            }
            List<VolumeInfoData> updated = result.get("updated");
            if (CollectionUtil.isNotEmpty(updated)) {
                bean.updated(updated);
            }
        }
    }

    @Override
    public String supportProtocol() {
        return BASIC_VOLUME_INFO.code();
    }
}
