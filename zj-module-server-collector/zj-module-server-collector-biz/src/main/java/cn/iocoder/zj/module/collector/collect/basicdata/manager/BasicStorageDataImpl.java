package cn.iocoder.zj.module.collector.collect.basicdata.manager;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.zj.framework.common.dal.manager.StorageData;
import cn.iocoder.zj.framework.common.dal.manager.StorageHostRelationData;
import cn.iocoder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.iocoder.zj.module.collector.service.StorageHostRelationInfo;
import cn.iocoder.zj.module.collector.service.StorageInfo;
import cn.iocoder.zj.module.collector.util.DataSetAnalyzerUtils;
import cn.iocoder.zj.module.collector.util.OptimizedDataConverter;
import cn.iocoder.zj.module.collector.util.SpringBeanUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_STORAGE;

public class BasicStorageDataImpl extends AbstractBasicData {

    @Override
    public void preCheck() {

    }

    @Override
    public void collectBasicData(List<?> results,String type) {
        OptimizedDataConverter converter = SpringBeanUtils.getBean(OptimizedDataConverter.class);
        // 获取宿主机列表
        List<StorageData> newStorageList =  converter.convertList(results,StorageData.class);
                StorageInfo bean = SpringBeanUtils.getBean(StorageInfo.class);
        List<StorageData> oldStorageData = bean.selectByIdPlatformList(newStorageList.get(0).getPlatformId());
        Map<String, List<StorageData>> result = DataSetAnalyzerUtils.compareCollectionsByKey(oldStorageData, newStorageList, StorageData::getUuid);

        // 处理存储基本信息
        handleStorageData(result, bean);

        // 处理存储与主机关系数据
        handleStorageHostRelation(newStorageList);
    }



    private void handleStorageData(Map<String, List<StorageData>> result, StorageInfo bean) {
        List<StorageData> added = result.get("added");
        if (CollectionUtil.isNotEmpty(added)) {
            bean.add(added);
        }
        List<StorageData> deleted = result.get("deleted");
        if (CollectionUtil.isNotEmpty(deleted)) {
            List<String> deletList = deleted.stream().map(StorageData::getUuid).collect(Collectors.toList());
            bean.deleted(deletList);
        }
        List<StorageData> updated = result.get("updated");
        if (CollectionUtil.isNotEmpty(updated)) {
            bean.updated(updated);
        }
    }

    private void handleStorageHostRelation(List<StorageData> newStorageList) {
        StorageHostRelationInfo storageHostRelationInfo = SpringBeanUtils.getBean(StorageHostRelationInfo.class);
        List<StorageHostRelationData> storageHostRelationDataList = newStorageList.stream()
                .map(StorageData::getStorageHostRelationDataList)
                .filter(list -> list != null)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(storageHostRelationDataList)) {

            List<StorageHostRelationData> oldStorageData = storageHostRelationInfo.selectByIdPlatformList(newStorageList.get(0).getPlatformId());

            Map<String, List<StorageHostRelationData>> result = DataSetAnalyzerUtils.compareCollectionsByMultiKeys(oldStorageData, storageHostRelationDataList, StorageHostRelationData::getHardwareUuid, StorageHostRelationData::getStorageUuid);

            List<StorageHostRelationData> added = result.get("added");
            if (CollectionUtil.isNotEmpty(added)) {
                storageHostRelationInfo.add(added);
            }
            List<StorageHostRelationData> deleted = result.get("deleted");
            if (CollectionUtil.isNotEmpty(deleted)) {
                List<Long> deletList = deleted.stream().map(StorageHostRelationData::getId).collect(Collectors.toList());
                storageHostRelationInfo.deleted(deletList);
            }
            List<StorageHostRelationData> updated = result.get("updated");
            if (CollectionUtil.isNotEmpty(updated)) {
                storageHostRelationInfo.updated(updated);
            }
        }
    }

    @Override
    public String supportProtocol() {
        return BASIC_STORAGE.code();
    }
}
