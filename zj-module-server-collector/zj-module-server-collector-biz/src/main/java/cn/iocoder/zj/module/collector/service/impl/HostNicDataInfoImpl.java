package cn.iocoder.zj.module.collector.service.impl;

import cn.iocoder.zj.framework.common.dal.manager.HostNicData;
import cn.iocoder.zj.module.collector.mapper.HostNicDataInfoMapper;
import cn.iocoder.zj.module.collector.service.HostNicDataInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class HostNicDataInfoImpl implements HostNicDataInfo {

    @Resource
    HostNicDataInfoMapper hostNicDataInfoMapper;

    @Override
    public List<HostNicData> selectByIdPlatformList(Long platformId) {
        return hostNicDataInfoMapper.selectList(new QueryWrapper<HostNicData>().eq("platform_id", platformId));
    }

    @Override
    public void add(List<HostNicData> added) {
        for (HostNicData hostNicData : added) {
            hostNicDataInfoMapper.insert(hostNicData);
        }
    }

    @Override
    public void deleted(List<String> deletList) {
        for (String uuid : deletList) {
            hostNicDataInfoMapper.delete(new QueryWrapper<HostNicData>().eq("uuid", uuid));
        }
    }

    @Override
    public void updated(List<HostNicData> updated) {
        for (HostNicData hostNicData : updated) {
            hostNicData.setUpdateTime(new Date());
            hostNicDataInfoMapper.update(hostNicData, new QueryWrapper<HostNicData>().eq("uuid", hostNicData.getUuid()));
        }
    }
}
