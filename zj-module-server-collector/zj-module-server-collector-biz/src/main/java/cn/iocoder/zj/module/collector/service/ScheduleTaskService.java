package cn.iocoder.zj.module.collector.service;

import cn.iocoder.zj.module.collector.dal.schedule.ScheduleTask;
import cn.iocoder.zj.module.collector.framework.netty.job.cache.TaskModel;

import java.util.List;


public interface ScheduleTaskService {

    void startTask(ScheduleTask scheduleTask);

    void stopTask(ScheduleTask scheduleTask);

    void modifyTask(ScheduleTask scheduleTask);

    void refreshList();

    List<TaskModel> list();

}
