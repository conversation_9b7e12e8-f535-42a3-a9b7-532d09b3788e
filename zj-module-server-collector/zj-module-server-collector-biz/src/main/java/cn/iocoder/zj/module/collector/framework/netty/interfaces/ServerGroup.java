package cn.iocoder.zj.module.collector.framework.netty.interfaces;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ServerGroup implements CommandLineRunner {

    private final List<TcpServer> tcpServers;

    public ServerGroup(List<TcpServer> tcpServers) {
        this.tcpServers = tcpServers;
    }

    public boolean isReady() {
        for (TcpServer tcpServer : tcpServers) {
            if (!tcpServer.isReady()) {
                return false;
            }
        }
        return true;
    }

    @Override
    public void run(String... args) {
        for (TcpServer tcpServer : tcpServers) {
             tcpServer.start();
        }
    }

    @PreDestroy
    public void destroy() {
        for (TcpServer tcpServer : tcpServers) {
            tcpServer.stop();
        }
    }
}
