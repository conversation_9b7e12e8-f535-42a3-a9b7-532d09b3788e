package cn.iocoder.zj.module.collector.framework.netty.job.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.module.collector.framework.netty.job.cache.TaskCacheModel;
import cn.iocoder.zj.module.collector.framework.netty.job.service.ScheduleTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;

import static cn.iocoder.zj.module.collector.framework.netty.job.annotation.YQJobProcessor.TASK_CACHE;


/**
 * <AUTHOR>
 **/
@Slf4j
@Service
public class ServiceImplScheduleImpl implements ScheduleTaskService {

    @Resource
    private TaskScheduler taskScheduler;

    private final Map<String, ScheduledFuture<?>> tasks = new ConcurrentHashMap<>(16);

    @Override
    public List<TaskCacheModel> listTasks() {
        return new ArrayList<>(TASK_CACHE.values());
    }

    @Override
    public void removeTask(TaskCacheModel taskCacheModel) {
        if (tasks.containsKey(taskCacheModel.getJobName())) {
            tasks.get(taskCacheModel.getJobName()).cancel(true);
            tasks.remove(taskCacheModel.getJobName());
        }
    }

    @Override
    public void addTask(TaskCacheModel taskCacheModel) {
        if (tasks.containsKey(taskCacheModel.getJobName())) {
            //取消现有任务
            tasks.get(taskCacheModel.getJobName()).cancel(true);
            tasks.remove(taskCacheModel.getJobName());
        }
        TaskCacheModel model = TASK_CACHE.get(taskCacheModel.getJobName());
        if (ObjectUtil.isNotEmpty(model)){
            CronTrigger cronTrigger = new CronTrigger(model.getCronExpression());
            ScheduledFuture<?> future = taskScheduler.schedule(model.getTask(), cronTrigger);
            tasks.put(taskCacheModel.getJobName(), future);
        }
    }

    @Override
    public void updateTask(String jobName) {
        if (tasks.containsKey(jobName)) {
            //取消现有任务
            tasks.get(jobName).cancel(true);
            tasks.remove(jobName);
        }
        TaskCacheModel model = TASK_CACHE.get(jobName);
        if (ObjectUtil.isNotEmpty(model)){
            Method method = model.getMethod();
            Object bean = model.getBean();
            CronTrigger cronTrigger = new CronTrigger(model.getCronExpression());
            ScheduledFuture<?> future = taskScheduler.schedule(() -> {
                try {
                    method.invoke(bean);
                } catch (Exception e) {
                    log.error("An error occurred while executing the task {}", jobName, e);
                }
            }, cronTrigger);
            tasks.put(jobName, future);
        }
    }

    /**
     * 所有任务并取消
     */
    public void clearAllTasks() {
        for (Map.Entry<String, ScheduledFuture<?>> entry : tasks.entrySet()) {
            ScheduledFuture<?> future = entry.getValue();
            if (future != null) {
                future.cancel(true);
            }
        }
        tasks.clear();
    }

}
