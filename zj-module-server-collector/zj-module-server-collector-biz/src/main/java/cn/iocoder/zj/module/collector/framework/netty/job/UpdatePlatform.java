package cn.iocoder.zj.module.collector.framework.netty.job;

import cn.iocoder.zj.framework.common.message.ClusterMsg;
import cn.iocoder.zj.module.collector.dal.platform.Platform;
import cn.iocoder.zj.module.collector.framework.netty.job.annotation.YQJob;
import cn.iocoder.zj.module.collector.framework.netty.server.ChannelCtxMap;
import cn.iocoder.zj.module.collector.service.PlatformInfo;
import com.google.gson.Gson;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class UpdatePlatform {

    private final PlatformInfo platformInfo;


    public UpdatePlatform(PlatformInfo platformInfo) {
        this.platformInfo = platformInfo;
    }

    @YQJob(value ="update_info", cronExpression = "0 0/15 * * * ?")
    public void updateInfo(ClusterMsg.Message.Builder message) {
        Map<String, List<ChannelHandlerContext>> listMap = ChannelCtxMap.groupChannelsByClientId();
        for (Map.Entry<String, List<ChannelHandlerContext>> entry : listMap.entrySet()) {
            String clientId = entry.getKey();
            List<Platform> platformInfos = platformInfo.getPlatformByTenantName(clientId);
            List<ChannelHandlerContext> contextList = entry.getValue();
            for (ChannelHandlerContext ctx : contextList) {
                message.setClientId(clientId)
                        .setType(ClusterMsg.MessageType.UPDATE)
                        .setData(new Gson().toJson(platformInfos)).build();
                ctx.writeAndFlush(message);
            }
        }
    }

}
