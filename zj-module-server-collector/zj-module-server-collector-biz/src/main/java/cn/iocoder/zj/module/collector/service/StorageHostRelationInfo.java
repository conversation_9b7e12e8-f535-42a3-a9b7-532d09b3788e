package cn.iocoder.zj.module.collector.service;

import cn.iocoder.zj.framework.common.dal.manager.StorageHostRelationData;

import java.util.List;

public interface StorageHostRelationInfo {

    List<StorageHostRelationData> selectByIdPlatformList(Long platformId);

    void add(List<StorageHostRelationData> added);

    void deleted(List<Long> deletList);

    void updated(List<StorageHostRelationData> updated);
}
