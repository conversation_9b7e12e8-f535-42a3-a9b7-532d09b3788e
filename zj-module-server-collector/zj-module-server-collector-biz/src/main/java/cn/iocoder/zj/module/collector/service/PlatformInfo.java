package cn.iocoder.zj.module.collector.service;

import cn.iocoder.zj.framework.common.dal.manager.ScanIPData;
import cn.iocoder.zj.module.collector.dal.platform.Platform;

import java.util.List;

/**
 * <AUTHOR>
 **/
public interface PlatformInfo {


    /**
     * 获取平台配置信息
     */
    List<Platform> getPlatformInfos();

    List<Platform> getPlatformByTenantName(String name);

    void updatePlatformInfo(Platform platform);

    void updateScanIP(List<ScanIPData> list);

    void updateScanIPState(Long ipRangeId);
}
