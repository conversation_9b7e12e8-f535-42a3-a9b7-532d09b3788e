package cn.iocoder.zj.module.collector.service.impl;

import cn.iocoder.zj.framework.common.dal.manager.ImageData;
import cn.iocoder.zj.module.collector.mapper.ImageDataInfoMapper;
import cn.iocoder.zj.module.collector.service.ImageDataInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Service
public class ImageDataInfoImpl  implements ImageDataInfo {

    @Resource
    ImageDataInfoMapper imageDataInfoMapper;

    @Override
    public List<ImageData> selectByIdPlatformList(Long platformId) {
        return imageDataInfoMapper.selectList(new QueryWrapper<ImageData>().eq("platform_id", platformId));
    }

    @Override
    public void add(List<ImageData> added) {
       for (ImageData imageData : added) {
           imageDataInfoMapper.insert(imageData);
       }
    }

    @Override
    public void deleted(List<String> deletList) {
        for (String id : deletList) {
            imageDataInfoMapper.delete(new QueryWrapper<ImageData>().eq("uuid", id));
        }
    }

    @Override
    public void updated(List<ImageData> updated) {
      for (ImageData imageData : updated) {
          imageDataInfoMapper.update(imageData, new QueryWrapper<ImageData>().eq("uuid", imageData.getUuid()));
      }
    }
}
