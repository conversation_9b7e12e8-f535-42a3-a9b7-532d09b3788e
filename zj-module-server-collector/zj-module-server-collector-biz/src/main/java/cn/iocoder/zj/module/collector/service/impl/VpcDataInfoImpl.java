package cn.iocoder.zj.module.collector.service.impl;

import cn.iocoder.zj.framework.common.dal.manager.VpcData;
import cn.iocoder.zj.module.collector.mapper.VpcDataInfoMapper;
import cn.iocoder.zj.module.collector.service.VpcDataInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class VpcDataInfoImpl implements VpcDataInfo {
    @Resource
    VpcDataInfoMapper vpcDataInfoMapper;

    @Override
    public List<VpcData> selectByIdPlatformList(Long platformId) {
        return vpcDataInfoMapper.selectList(new QueryWrapper<VpcData>().eq("platform_id", platformId));
    }

    @Override
    public void add(List<VpcData> added) {
        for (VpcData vpcData : added) {
            vpcDataInfoMapper.insert(vpcData);
        }
    }

    @Override
    public void deleted(List<String> deletList) {
        for (String vpcData : deletList) {
            vpcDataInfoMapper.delete(new QueryWrapper<VpcData>().eq("uuid", vpcData));
        }
    }

    @Override
    public void updated(List<VpcData> updated) {
        for (VpcData vpcData : updated) {
            vpcDataInfoMapper.update(vpcData, new QueryWrapper<VpcData>().eq("uuid", vpcData.getUuid()));
        }
    }
}
