package cn.iocoder.zj.module.collector.collect.basicdata.manager;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.zj.framework.common.dal.manager.StoragePoolData;
import cn.iocoder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.iocoder.zj.module.collector.service.StoragePoolInfo;
import cn.iocoder.zj.module.collector.util.DataSetAnalyzerUtils;
import cn.iocoder.zj.module.collector.util.OptimizedDataConverter;
import cn.iocoder.zj.module.collector.util.SpringBeanUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_STORAGE_POOL;

@Slf4j
public class BasicStoragePoolDataImpl extends AbstractBasicData {


    @Override
    public void preCheck() {

    }

    @Override
    public void collectBasicData(List<?> results,String type) {
        OptimizedDataConverter converter = SpringBeanUtils.getBean(OptimizedDataConverter.class);

        List<StoragePoolData> dataList = converter.convertList(results,StoragePoolData.class);
        StoragePoolInfo bean = SpringBeanUtils.getBean(StoragePoolInfo.class);
        List<StoragePoolData> oldVmData = bean.selectByIdPlatformList(dataList.get(0).getPlatformId());
        Map<String, List<StoragePoolData>> result = DataSetAnalyzerUtils.compareCollectionsByKey(oldVmData, dataList, StoragePoolData::getUuid);

        List<StoragePoolData> added = result.get("added");
        if (CollectionUtil.isNotEmpty(added)) {
            bean.add(added);
        }
        List<StoragePoolData> deleted = result.get("deleted");
        if (CollectionUtil.isNotEmpty(deleted)) {
            List<String> deletList = deleted.stream().map(StoragePoolData::getUuid).collect(Collectors.toList());
            bean.deleted(deletList);
        }
        List<StoragePoolData> updated = result.get("updated");
        if (CollectionUtil.isNotEmpty(updated)) {
            bean.updated(updated);
        }
    }

    @Override
    public String supportProtocol() {
        return BASIC_STORAGE_POOL.code();
    }
}
