package cn.iocoder.zj.module.collector.mapper;

import cn.iocoder.zj.framework.common.dal.manager.ScanIPData;
import cn.iocoder.zj.module.collector.dal.platform.Platform;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Mapper
public interface PlatformInfoMapper extends BaseMapper<Platform>{

    /**
     * 查询平台信息
     * @return
     */
    List<Platform> getPlatformInfos();

    void updatePlatformInfo(@Param("platformId") Long platformId, @Param("state") Long state, @Param("diffTime") Long diffTime, @Param("dateTime") Date dateTime);

    List<Platform> getPlatformByTenantName(@Param("name") String name);

    void updateScanIP(@Param("list") List<ScanIPData> list);

    void updateScanIPState(@Param("ipRangeId") Long ipRangeId);
}
