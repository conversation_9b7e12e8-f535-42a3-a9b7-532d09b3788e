package cn.iocoder.zj.module.collector.framework.netty.monitor;

import cn.iocoder.zj.module.collector.framework.netty.server.ChannelCtxMap;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 连接监控组件
 * 用于监控和诊断连接状态，帮助排查生产环境问题
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ConnectionMonitor {
    
    private final AtomicInteger totalConnections = new AtomicInteger(0);
    private final AtomicInteger authenticatedConnections = new AtomicInteger(0);
    private final AtomicInteger unauthenticatedConnections = new AtomicInteger(0);
    
    /**
     * 定期监控连接状态
     */
    @Scheduled(fixedRate = 30000) // 每30秒执行一次
    public void monitorConnections() {
        try {
            Map<String, List<ChannelHandlerContext>> groupedChannels = ChannelCtxMap.groupChannelsByClientId();
            
            int totalChannels = 0;
            int authenticatedChannels = 0;
            
            for (Map.Entry<String, List<ChannelHandlerContext>> entry : groupedChannels.entrySet()) {
                String clientId = entry.getKey();
                List<ChannelHandlerContext> contexts = entry.getValue();
                
                if (clientId != null && !clientId.trim().isEmpty()) {
                    authenticatedChannels += contexts.size();
                } else {
                    log.warn("[连接监控] 发现未认证的连接数量: {}", contexts.size());
                }
                totalChannels += contexts.size();
            }
            
            totalConnections.set(totalChannels);
            authenticatedConnections.set(authenticatedChannels);
            unauthenticatedConnections.set(totalChannels - authenticatedChannels);
            
            if (totalChannels > 0) {
                log.info("[连接监控] 总连接数: {}, 已认证: {}, 未认证: {}", 
                        totalChannels, authenticatedChannels, (totalChannels - authenticatedChannels));
            }
            
            // 如果有未认证连接，记录详细信息
            if (totalChannels - authenticatedChannels > 0) {
                logUnauthenticatedConnections();
            }
            
        } catch (Exception e) {
            log.error("[连接监控] 监控过程中发生异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 记录未认证连接的详细信息
     */
    private void logUnauthenticatedConnections() {
        try {
            Map<String, List<ChannelHandlerContext>> groupedChannels = ChannelCtxMap.groupChannelsByClientId();
            
            for (Map.Entry<String, List<ChannelHandlerContext>> entry : groupedChannels.entrySet()) {
                String clientId = entry.getKey();
                List<ChannelHandlerContext> contexts = entry.getValue();
                
                if (clientId == null || clientId.trim().isEmpty()) {
                    for (ChannelHandlerContext ctx : contexts) {
                        log.warn("[未认证连接] 通道: {}, 远程地址: {}, 连接时间: {}", 
                                ctx.channel().id().asShortText(),
                                ctx.channel().remoteAddress(),
                                "未知"); // 可以考虑添加连接时间记录
                    }
                }
            }
        } catch (Exception e) {
            log.error("[连接监控] 记录未认证连接时发生异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 获取连接统计信息
     */
    public ConnectionStats getConnectionStats() {
        return ConnectionStats.builder()
                .totalConnections(totalConnections.get())
                .authenticatedConnections(authenticatedConnections.get())
                .unauthenticatedConnections(unauthenticatedConnections.get())
                .build();
    }
    
    /**
     * 连接统计信息
     */
    public static class ConnectionStats {
        private final int totalConnections;
        private final int authenticatedConnections;
        private final int unauthenticatedConnections;
        
        private ConnectionStats(int totalConnections, int authenticatedConnections, int unauthenticatedConnections) {
            this.totalConnections = totalConnections;
            this.authenticatedConnections = authenticatedConnections;
            this.unauthenticatedConnections = unauthenticatedConnections;
        }
        
        public static ConnectionStatsBuilder builder() {
            return new ConnectionStatsBuilder();
        }
        
        public int getTotalConnections() { return totalConnections; }
        public int getAuthenticatedConnections() { return authenticatedConnections; }
        public int getUnauthenticatedConnections() { return unauthenticatedConnections; }
        
        public static class ConnectionStatsBuilder {
            private int totalConnections;
            private int authenticatedConnections;
            private int unauthenticatedConnections;
            
            public ConnectionStatsBuilder totalConnections(int totalConnections) {
                this.totalConnections = totalConnections;
                return this;
            }
            
            public ConnectionStatsBuilder authenticatedConnections(int authenticatedConnections) {
                this.authenticatedConnections = authenticatedConnections;
                return this;
            }
            
            public ConnectionStatsBuilder unauthenticatedConnections(int unauthenticatedConnections) {
                this.unauthenticatedConnections = unauthenticatedConnections;
                return this;
            }
            
            public ConnectionStats build() {
                return new ConnectionStats(totalConnections, authenticatedConnections, unauthenticatedConnections);
            }
        }
    }
}
