package cn.iocoder.zj.module.collector.collect.basicdata.manager;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.zj.framework.common.dal.manager.VpcData;
import cn.iocoder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.iocoder.zj.module.collector.service.VpcDataInfo;
import cn.iocoder.zj.module.collector.util.DataSetAnalyzerUtils;
import cn.iocoder.zj.module.collector.util.OptimizedDataConverter;
import cn.iocoder.zj.module.collector.util.SpringBeanUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VPC;

public class BasicVpcDataImpl extends AbstractBasicData {
    @Override
    public void preCheck() {

    }

    @Override
    public void collectBasicData(List<?> results,String type) {
        OptimizedDataConverter converter = SpringBeanUtils.getBean(OptimizedDataConverter.class);

        // 获取宿主机列表
        List<VpcData> newVpcList = converter.convertList(results,VpcData.class);

                VpcDataInfo vpcDataInfo = SpringBeanUtils.getBean(VpcDataInfo.class);
        List<VpcData> oldVpcData = vpcDataInfo.selectByIdPlatformList(newVpcList.get(0).getPlatformId());
        // 比较新老数据
        Map<String, List<VpcData>> result = DataSetAnalyzerUtils.compareCollectionsByKey(oldVpcData, newVpcList, VpcData::getUuid);
        List<VpcData> added = result.get("added");
        if (CollectionUtil.isNotEmpty(added)) {
            vpcDataInfo.add(added);
        }
        List<VpcData> deleted = result.get("deleted");
        if (CollectionUtil.isNotEmpty(deleted)) {
            List<String> deletList = deleted.stream().map(VpcData::getUuid).collect(Collectors.toList());
            vpcDataInfo.deleted(deletList);
        }
        List<VpcData> updated = result.get("updated");
        if (CollectionUtil.isNotEmpty(updated)) {
            vpcDataInfo.updated(updated);
        }

    }

    @Override
    public String supportProtocol() {
        return BASIC_VPC.code();
    }
}
