package cn.iocoder.zj.module.collector.service.impl;

import cn.iocoder.zj.framework.common.dal.manager.ImageData;
import cn.iocoder.zj.framework.common.dal.manager.VolumeSnapshotData;
import cn.iocoder.zj.module.collector.mapper.VolumeSnapshotInfoMapper;
import cn.iocoder.zj.module.collector.service.VolumeSnapshotInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Service
public class VolumeSnapshotInfoImpl implements VolumeSnapshotInfo {

    @Resource
    private VolumeSnapshotInfoMapper volumeSnapshotInfoMapper;

    @Override
    public List<VolumeSnapshotData> selectByIdPlatformList(Long platformId) {
        return volumeSnapshotInfoMapper.selectList(new QueryWrapper<VolumeSnapshotData>().eq("platform_id", platformId));
    }

    @Override
    public void add(List<VolumeSnapshotData> added) {
        for (VolumeSnapshotData volumeSnapshotData : added) {
            volumeSnapshotInfoMapper.insert(volumeSnapshotData);
        }
    }

    @Override
    public void deleted(List<String> deletList) {
        for (String id : deletList) {
            volumeSnapshotInfoMapper.delete(new QueryWrapper<VolumeSnapshotData>().eq("uuid", id));
        }
    }

    @Override
    public void updated(List<VolumeSnapshotData> updated) {
        for (VolumeSnapshotData volumeSnapshotData : updated) {
            volumeSnapshotInfoMapper.update(volumeSnapshotData, new QueryWrapper<VolumeSnapshotData>().eq("uuid", volumeSnapshotData.getUuid()));
        }
    }
}
