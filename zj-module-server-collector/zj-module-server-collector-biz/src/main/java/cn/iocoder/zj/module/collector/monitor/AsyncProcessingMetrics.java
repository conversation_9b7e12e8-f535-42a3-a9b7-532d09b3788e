package cn.iocoder.zj.module.collector.monitor;

import cn.iocoder.zj.framework.common.message.ClusterMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 异步处理性能监控组件
 * 监控消息处理的性能指标和统计信息
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Component
public class AsyncProcessingMetrics {
    
    /**
     * 各消息类型的处理次数
     */
    private final ConcurrentHashMap<ClusterMsg.MessageType, LongAdder> processingCounts = new ConcurrentHashMap<>();
    
    /**
     * 各消息类型的总处理时间（毫秒）
     */
    private final ConcurrentHashMap<ClusterMsg.MessageType, LongAdder> totalProcessingTime = new ConcurrentHashMap<>();
    
    /**
     * 各消息类型的总队列等待时间（毫秒）
     */
    private final ConcurrentHashMap<ClusterMsg.MessageType, LongAdder> totalQueueTime = new ConcurrentHashMap<>();
    
    /**
     * 各消息类型的最大处理时间
     */
    private final ConcurrentHashMap<ClusterMsg.MessageType, AtomicLong> maxProcessingTime = new ConcurrentHashMap<>();
    
    /**
     * 各消息类型的最大队列等待时间
     */
    private final ConcurrentHashMap<ClusterMsg.MessageType, AtomicLong> maxQueueTime = new ConcurrentHashMap<>();
    
    /**
     * 处理失败次数
     */
    private final ConcurrentHashMap<ClusterMsg.MessageType, LongAdder> failureCounts = new ConcurrentHashMap<>();
    
    /**
     * 线程池拒绝次数
     */
    private final AtomicLong rejectedCount = new AtomicLong(0);
    
    /**
     * 记录消息处理性能
     * 
     * @param messageType 消息类型
     * @param processingTime 处理时间（毫秒）
     * @param queueTime 队列等待时间（毫秒）
     */
    public void recordProcessing(ClusterMsg.MessageType messageType, long processingTime, long queueTime) {
        // 更新处理次数
        processingCounts.computeIfAbsent(messageType, k -> new LongAdder()).increment();
        
        // 更新总处理时间
        totalProcessingTime.computeIfAbsent(messageType, k -> new LongAdder()).add(processingTime);
        
        // 更新总队列时间
        totalQueueTime.computeIfAbsent(messageType, k -> new LongAdder()).add(queueTime);
        
        // 更新最大处理时间
        maxProcessingTime.computeIfAbsent(messageType, k -> new AtomicLong(0))
                .updateAndGet(current -> Math.max(current, processingTime));
        
        // 更新最大队列时间
        maxQueueTime.computeIfAbsent(messageType, k -> new AtomicLong(0))
                .updateAndGet(current -> Math.max(current, queueTime));
        
        // 记录慢处理警告
        if (processingTime > 1000) {
            log.warn("[慢处理警告] 消息类型:{}, 处理时间:{}ms, 队列时间:{}ms", 
                    messageType, processingTime, queueTime);
        }
        
        // 记录队列等待过长警告
        if (queueTime > 500) {
            log.warn("[队列等待过长] 消息类型:{}, 队列时间:{}ms, 处理时间:{}ms", 
                    messageType, queueTime, processingTime);
        }
    }
    
    /**
     * 记录处理失败
     * 
     * @param messageType 消息类型
     */
    public void recordFailure(ClusterMsg.MessageType messageType) {
        failureCounts.computeIfAbsent(messageType, k -> new LongAdder()).increment();
    }
    
    /**
     * 记录线程池拒绝
     */
    public void recordRejection() {
        rejectedCount.incrementAndGet();
    }
    
    /**
     * 获取指定消息类型的统计信息
     */
    public ProcessingStats getStats(ClusterMsg.MessageType messageType) {
        long count = processingCounts.getOrDefault(messageType, new LongAdder()).sum();
        long totalTime = totalProcessingTime.getOrDefault(messageType, new LongAdder()).sum();
        long totalQueue = totalQueueTime.getOrDefault(messageType, new LongAdder()).sum();
        long maxTime = maxProcessingTime.getOrDefault(messageType, new AtomicLong(0)).get();
        long maxQueue = maxQueueTime.getOrDefault(messageType, new AtomicLong(0)).get();
        long failures = failureCounts.getOrDefault(messageType, new LongAdder()).sum();
        
        return new ProcessingStats(messageType, count, totalTime, totalQueue, maxTime, maxQueue, failures);
    }
    
    /**
     * 打印所有统计信息
     */
    public void printAllStats() {
        log.info("=== 异步处理性能统计报告 ===");
        log.info("线程池拒绝次数: {}", rejectedCount.get());
        
        for (ClusterMsg.MessageType messageType : processingCounts.keySet()) {
            ProcessingStats stats = getStats(messageType);
            
            log.info("消息类型: {}", messageType);
            log.info("  处理次数: {}", stats.getCount());
            log.info("  失败次数: {}", stats.getFailures());
            log.info("  成功率: {:.2f}%", stats.getSuccessRate());
            log.info("  平均处理时间: {:.2f}ms", stats.getAverageProcessingTime());
            log.info("  平均队列时间: {:.2f}ms", stats.getAverageQueueTime());
            log.info("  最大处理时间: {}ms", stats.getMaxProcessingTime());
            log.info("  最大队列时间: {}ms", stats.getMaxQueueTime());
            log.info("  处理速度: {:.0f} msg/s", stats.getProcessingRate());
            log.info("  ---");
        }
    }
    
    /**
     * 重置所有统计数据
     */
    public void reset() {
        processingCounts.clear();
        totalProcessingTime.clear();
        totalQueueTime.clear();
        maxProcessingTime.clear();
        maxQueueTime.clear();
        failureCounts.clear();
        rejectedCount.set(0);
        log.info("[性能监控] 统计数据已重置");
    }
    
    /**
     * 处理统计信息数据类
     */
    public static class ProcessingStats {
        private final ClusterMsg.MessageType messageType;
        private final long count;
        private final long totalProcessingTime;
        private final long totalQueueTime;
        private final long maxProcessingTime;
        private final long maxQueueTime;
        private final long failures;
        
        public ProcessingStats(ClusterMsg.MessageType messageType, long count, long totalProcessingTime, 
                             long totalQueueTime, long maxProcessingTime, long maxQueueTime, long failures) {
            this.messageType = messageType;
            this.count = count;
            this.totalProcessingTime = totalProcessingTime;
            this.totalQueueTime = totalQueueTime;
            this.maxProcessingTime = maxProcessingTime;
            this.maxQueueTime = maxQueueTime;
            this.failures = failures;
        }
        
        public ClusterMsg.MessageType getMessageType() {
            return messageType;
        }
        
        public long getCount() {
            return count;
        }
        
        public long getTotalProcessingTime() {
            return totalProcessingTime;
        }
        
        public long getTotalQueueTime() {
            return totalQueueTime;
        }
        
        public long getMaxProcessingTime() {
            return maxProcessingTime;
        }
        
        public long getMaxQueueTime() {
            return maxQueueTime;
        }
        
        public long getFailures() {
            return failures;
        }
        
        public double getAverageProcessingTime() {
            return count > 0 ? (double) totalProcessingTime / count : 0;
        }
        
        public double getAverageQueueTime() {
            return count > 0 ? (double) totalQueueTime / count : 0;
        }
        
        public double getSuccessRate() {
            return count > 0 ? ((count - failures) * 100.0 / count) : 0;
        }
        
        public double getProcessingRate() {
            return totalProcessingTime > 0 ? (count * 1000.0 / totalProcessingTime) : 0;
        }
        
        @Override
        public String toString() {
            return String.format(
                "ProcessingStats{type=%s, count=%d, avgProcessing=%.2fms, avgQueue=%.2fms, " +
                "maxProcessing=%dms, maxQueue=%dms, successRate=%.2f%%, rate=%.0f msg/s}",
                messageType, count, getAverageProcessingTime(), getAverageQueueTime(),
                maxProcessingTime, maxQueueTime, getSuccessRate(), getProcessingRate()
            );
        }
    }
}
