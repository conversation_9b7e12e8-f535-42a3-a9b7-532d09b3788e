package cn.iocoder.zj.module.collector.service.impl;

import cn.iocoder.zj.framework.common.dal.manager.StoragePoolData;
import cn.iocoder.zj.module.collector.mapper.StoragePoolMapper;
import cn.iocoder.zj.module.collector.service.StoragePoolInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class StoragePoolInfoImpl implements StoragePoolInfo {
    @Resource
    StoragePoolMapper storagePoolMapper;

    @Override
    public List<StoragePoolData> selectByIdPlatformList(Long platformId) {
        return storagePoolMapper.selectList(new QueryWrapper<StoragePoolData>().eq("platform_id", platformId));
    }

    @Override
    public void add(List<StoragePoolData> added) {
        for (StoragePoolData storagePoolData : added) {
            storagePoolMapper.insert(storagePoolData);
        }
    }

    @Override
    public void deleted(List<String> deletList) {
        for (String id : deletList) {
            storagePoolMapper.delete(new QueryWrapper<StoragePoolData>().eq("uuid", id));
        }
    }

    @Override
    public void updated(List<StoragePoolData> updated) {
        for (StoragePoolData storageData : updated) {
            storagePoolMapper.update(storageData, new QueryWrapper<StoragePoolData>().eq("uuid", storageData.getUuid()));
        }

    }
}
