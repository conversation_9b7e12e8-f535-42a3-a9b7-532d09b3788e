package cn.iocoder.zj.module.collector.dal.schedule;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@Schema(description = "任务实体类")
public class ScheduleTask {

    @Schema(description = "客户端ID")
    @NotBlank(message = "客户端ID不能为空")
    String clientId;


    @NotEmpty(message = "任务名称集合不能为空")
    @Schema(description = "任务名称")
    List<String> jobName;
}
