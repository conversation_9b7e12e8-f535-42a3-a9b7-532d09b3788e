package cn.iocoder.zj.module.collector.service.impl;

import cn.iocoder.zj.framework.common.dal.manager.HostData;
import cn.iocoder.zj.module.collector.mapper.HostDataInfoMapper;
import cn.iocoder.zj.module.collector.service.HostDataInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Service
public class HostDataInfoImpl implements HostDataInfo {

    @Resource
    HostDataInfoMapper hostDataInfoMapper;

    @Override
    public List<HostData> selectByIdPlatformList(Long platformId) {
        return hostDataInfoMapper.selectByIdPlatformList(platformId);
    }

    @Override
    public int add(List<HostData> added) {
        return hostDataInfoMapper.add(added);
    }

    @Override
    public int deleted(List<String> deletList) {
        return hostDataInfoMapper.deleted(deletList);
    }

    @Override
    public int updated(List<HostData> updated) {
        return hostDataInfoMapper.updated(updated);
    }
}
