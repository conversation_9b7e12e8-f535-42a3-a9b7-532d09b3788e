/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.zj.module.collector.framework.netty.handler.processor;

import cn.iocoder.zj.framework.common.dal.manager.ScanIPData;
import cn.iocoder.zj.framework.common.dal.manager.ScanIPRequest;
import cn.iocoder.zj.framework.common.enums.OtherEnum;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.module.collector.constants.BusinessConstants;
import cn.iocoder.zj.module.collector.dal.platform.Platform;
import cn.iocoder.zj.module.collector.framework.kafka.KafkaProducerService;
import cn.iocoder.zj.module.collector.framework.netty.job.cache.TaskCacheModel;
import cn.iocoder.zj.module.collector.framework.netty.server.ChannelCtxMap;
import cn.iocoder.zj.module.collector.service.PlatformInfo;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 其他消息处理器
 * 处理告警、平台状态、定时任务、更新等消息
 * 
 * <AUTHOR>
 */
@Slf4j
public class OtherProcessor implements MessageProcessor {
    
    private final PlatformInfo platformInfo;
    private final KafkaProducerService producerService;
    private final RedisTemplate<String, Object> redisTemplate;
    
    private static final Set<ClusterMsg.MessageType> SUPPORTED_TYPES = Set.of(
            ClusterMsg.MessageType.UPDATE,
            ClusterMsg.MessageType.SCHEDULED_INFO,
            ClusterMsg.MessageType.DETECT,
            ClusterMsg.MessageType.FAIL,
            ClusterMsg.MessageType.ALERTER,
            ClusterMsg.MessageType.OTHER
    );

    public OtherProcessor(PlatformInfo platformInfo, KafkaProducerService producerService, RedisTemplate<String, Object> redisTemplate) {
        this.platformInfo = platformInfo;
        this.producerService = producerService;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public ClusterMsg.Message process(ChannelHandlerContext ctx, ClusterMsg.Message message) {
        ClusterMsg.MessageType messageType = message.getType();
        
        switch (messageType) {
            case UPDATE:
                return processUpdate(ctx, message);
            case SCHEDULED_INFO:
                return processScheduledInfo(message);
            case DETECT:
            case FAIL:
                return processPlatformStatus(message);
            case ALERTER:
                return processAlerter(message);
            case OTHER:
                return processOther(message);
            default:
                log.warn("[未知消息类型] 客户端: {}, 消息类型: {}, 无法处理",
                        message.getClientId(), messageType);
                return null;
        }
    }
    
    /**
     * 处理更新消息
     */
    private ClusterMsg.Message processUpdate(ChannelHandlerContext ctx, ClusterMsg.Message message) {
        log.info("[配置更新] 客户端: {}, 开始处理配置更新", message.getClientId());
        
        try {
            List<Platform> platformInfos = platformInfo.getPlatformByTenantName(message.getClientId());
            platformInfos.forEach(platform -> platform.setClientId(message.getClientId()));
            
            ClusterMsg.Message updateResponse = ClusterMsg.Message.newBuilder()
                    .setClientId(message.getClientId())
                    .setType(ClusterMsg.MessageType.UPDATE)
                    .setData(new Gson().toJson(platformInfos))
                    .build();
            
            ChannelCtxMap.addChannelCtx(ctx, message.getClientId());
            ctx.writeAndFlush(updateResponse);
            
            log.info("[配置更新] 客户端: {}, 配置更新成功", message.getClientId());
            
        } catch (Exception e) {
            log.error("[配置更新异常] 客户端: {}, 更新失败: {}", message.getClientId(), e.getMessage(), e);
        }
        
        return null;
    }
    
    /**
     * 处理定时任务信息
     */
    private ClusterMsg.Message processScheduledInfo(ClusterMsg.Message message) {
        log.info("[定时任务信息] 客户端: {}, 开始处理定时任务信息", message.getClientId());
        
        try {
            Type listType = new TypeToken<List<TaskCacheModel>>() {}.getType();
            List<TaskCacheModel> cacheModelsList = new GsonBuilder()
                    .excludeFieldsWithoutExposeAnnotation()
                    .create().fromJson(message.getData(), listType);
            
            ChannelCtxMap.scheduledInfo(message.getClientId(), cacheModelsList);
            
            log.info("[定时任务信息] 客户端: {}, 处理完成", message.getClientId());
            
        } catch (Exception e) {
            log.error("[定时任务信息异常] 客户端: {}, 处理失败: {}", message.getClientId(), e.getMessage(), e);
        }
        
        return null;
    }
    
    /**
     * 处理平台状态更新
     */
    private ClusterMsg.Message processPlatformStatus(ClusterMsg.Message message) {
        log.info("[平台状态更新] 客户端: {}, 消息类型: {}, 开始处理", message.getClientId(), message.getType());
        
        try {
            Platform platform = new Gson().fromJson(message.getData(), Platform.class);
            platformInfo.updatePlatformInfo(platform);
            
            log.info("[平台状态更新] 客户端: {}, 平台ID: {}, 更新成功",
                    message.getClientId(), platform.getPlatformId());
            
        } catch (Exception e) {
            log.error("[平台状态异常] 客户端: {}, 更新失败: {}", message.getClientId(), e.getMessage(), e);
        }
        
        return null;
    }
    
    /**
     * 处理告警消息
     */
    private ClusterMsg.Message processAlerter(ClusterMsg.Message message) {
        log.info("[告警消息处理] 客户端: {}, 开始处理告警消息", message.getClientId());
        try {
            producerService.sendMessage(BusinessConstants.PERFORMANCE_TOPIC, message.getData());
            log.info("[告警消息发送] 客户端: {}, 消息发送成功", message.getClientId());
            
        } catch (Exception e) {
            log.error("[告警消息异常] 客户端: {}, 发送失败: {}", message.getClientId(), e.getMessage(), e);
        }
        
        return null;
    }
    
    /**
     * 处理其他类型消息
     */
    private ClusterMsg.Message processOther(ClusterMsg.Message message) {
        log.info("[其他消息处理] 客户端: {}, 消息类型: {}, 开始处理", message.getClientId(), message.getType());
        
        try {
            String metricsCode = message.getMetrics();
            
            // 处理高级扫描消息
            if (OtherEnum.ADVANCES_PING.code().equals(metricsCode) ||
                    OtherEnum.ADVANCES_TCP.code().equals(metricsCode) ||
                    OtherEnum.ADVANCES_SNMP.code().equals(metricsCode)) {
                
                processAdvancedScan(message, metricsCode);
                
            } else if (Arrays.asList(OtherEnum.PING.code(), OtherEnum.TCP.code(), OtherEnum.SNMP.code()).contains(metricsCode)) {
                
                processAutoDiscovery(message, metricsCode);
                
            } else {
                log.warn("[未知指标类型] 客户端: {}, 指标: {}", message.getClientId(), metricsCode);
            }
            
        } catch (Exception e) {
            log.error("[其他消息异常] 客户端: {}, 处理失败: {}", message.getClientId(), e.getMessage(), e);
        }
        
        return null;
    }
    
    /**
     * 处理高级扫描
     */
    private void processAdvancedScan(ClusterMsg.Message message, String metricsCode) {
        ScanIPRequest scanIPRequest = new Gson().fromJson(message.getData(), ScanIPRequest.class);
        String redisKey = "SCAN_IP:" + scanIPRequest.getIpRangeId();
        
        if (!redisTemplate.hasKey(redisKey)) {
            return;
        }
        
        ScanIPRequest redis = new Gson().fromJson(redisTemplate.opsForValue().get(redisKey).toString(), ScanIPRequest.class);
        
        if (OtherEnum.ADVANCES_PING.code().equals(metricsCode)) {
            redis.setPingCount(scanIPRequest.getPingCount());
        } else if (OtherEnum.ADVANCES_TCP.code().equals(metricsCode)) {
            redis.setTcpCount(scanIPRequest.getTcpCount());
        } else {
            redis.setSnmpCount(scanIPRequest.getSnmpCount());
        }
        
        int totalCount = redis.getPingCount() + redis.getTcpCount() + redis.getSnmpCount();
        int ratio = redis.getTotal() == 0 ? 0 : (int) Math.round((totalCount * 100.0) / redis.getTotal());
        redis.setRatio(ratio);
        
        redisTemplate.opsForValue().set(redisKey, JsonUtils.toJsonString(redis), 1, TimeUnit.DAYS);
        
        if (redis.getTotal() == totalCount) {
            platformInfo.updateScanIPState(redis.getIpRangeId());
        }
    }
    
    /**
     * 处理自动发现
     */
    private void processAutoDiscovery(ClusterMsg.Message message, String metricsCode) {
        List<ScanIPData> list = new Gson().fromJson(message.getData(), new TypeToken<List<ScanIPData>>() {}.getType());
        
        if (!list.isEmpty()) {
            log.info("[自动发现-返回] 客户端: {}, 消息类型: {}, 数据: {}",
                    message.getClientId(), metricsCode, new Gson().toJson(list.get(0)));
            
            platformInfo.updateScanIP(list);
        }
    }

    @Override
    public ClusterMsg.MessageType getMessageType() {
        return ClusterMsg.MessageType.OTHER;
    }
    
    /**
     * 检查是否支持指定的消息类型
     */
    public boolean supports(ClusterMsg.MessageType messageType) {
        return SUPPORTED_TYPES.contains(messageType);
    }
} 