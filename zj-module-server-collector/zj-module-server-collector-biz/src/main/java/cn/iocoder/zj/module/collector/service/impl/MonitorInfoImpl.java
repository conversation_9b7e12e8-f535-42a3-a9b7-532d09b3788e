package cn.iocoder.zj.module.collector.service.impl;


import cn.iocoder.zj.module.collector.dal.platform.Platform;
import cn.iocoder.zj.module.collector.mapper.MonitorInfoMapper;
import cn.iocoder.zj.module.collector.service.MonitorInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class MonitorInfoImpl implements MonitorInfo {

    @Resource
    MonitorInfoMapper monitorInfoMapper;

    @Override
    public void updateMonitorStateByPlatformId(Platform platform) {
        monitorInfoMapper.updateHostStateByPlatformId(platform.getPlatformId());
        monitorInfoMapper.updateVmStateByPlatformId(platform.getPlatformId());
        monitorInfoMapper.updateStorageByPlatformId(platform.getPlatformId());
    }
}
