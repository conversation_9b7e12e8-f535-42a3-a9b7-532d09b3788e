package cn.iocoder.zj.module.collector.mapper;


import cn.iocoder.zj.framework.common.dal.manager.VmData;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface VmDataInfoMapper extends BaseMapper<VmData> {
    List<VmData> selectByIdPlatformList(@Param("platformId") Long platformId);

    int add(@Param("list") List<VmData> added);

    int deleted(@Param("list") List<String> deletList);

    int updated(@Param("list") List<VmData> updated);
}
