package cn.iocoder.zj.module.collector.service.impl;

import cn.iocoder.zj.framework.common.message.ClusterMsg;
import cn.iocoder.zj.module.collector.service.AutoDiscoveryService;
import org.springframework.stereotype.Service;

@Service
public class AutoDiscoveryServiceImpl implements AutoDiscoveryService {


    // 自动发现相关 业务服务端处理
    @Override
    public void autoDiscovery(ClusterMsg.Message messageInfo) {

    }
}
