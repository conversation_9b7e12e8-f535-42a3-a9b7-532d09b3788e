package cn.iocoder.zj.module.collector.constants;

/**
 *  业务常量
 * <AUTHOR>
 **/
public class BusinessConstants {

    /**
     * 首次连接分配一个默认客户端ID
     */
    public static final String CLIENT_ID = "default";


    /**
     * 定时任务未启动
     */
    public static final String JOB_STOP = "0";

    /**
     * 定时任务启动
     */
    public static final String JOB_START = "1";


    /**
     * 定时任务默认用户
     */
    public static final String JOB_USER_DEFAULT = "default";


    /**
     * 采集器鉴权字符
     */
    public static final String COLLECTOR_AUTH = "zj-collector-auth";

    /**
     * 消费者队列名称
     */
    public static final String PERFORMANCE_TOPIC = "alert-topic-cloud-prod";

    public static final String TAG_TOPIC = "tag-topic";
}
