package cn.iocoder.zj.module.collector.service.impl;


import cn.iocoder.zj.framework.common.dal.manager.CollectorData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import cn.iocoder.zj.module.collector.framework.netty.server.ChannelCtxMap;
import cn.iocoder.zj.module.collector.mapper.CollectorMapper;
import cn.iocoder.zj.module.collector.service.CollectorService;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.gson.Gson;
import io.netty.channel.ChannelHandlerContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.zj.module.collector.framework.netty.server.ChannelCtxMap.getChannelCtxByClientId;

@Service
public class CollectorInfoServiceImpl implements CollectorService {

    @Resource
    private CollectorMapper collectorMapper;


    @Override
    public void online(String clientId) {
        List<ChannelHandlerContext> contexts = getChannelCtxByClientId(clientId);
        for (ChannelHandlerContext context : contexts){
            ClusterMsg.Message message = ClusterMsg.Message.newBuilder()
                    .setClientId(clientId)
                    .setType(ClusterMsg.MessageType.ONLINE)
                    .setData(new Gson().toJson("online......")).build();
            context.writeAndFlush(message);
        }
        ChannelCtxMap.online(clientId);
        UpdateWrapper<CollectorData> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("name", clientId);
        updateWrapper.eq("creator", "zj");
        CollectorData data = new CollectorData();
        data.setStatus((byte)0);
        collectorMapper.update(data, updateWrapper);
    }

    @Override
    public void offline(String clientId) {
        List<ChannelHandlerContext> contexts = getChannelCtxByClientId(clientId);
        for (ChannelHandlerContext context : contexts){
            ClusterMsg.Message message = ClusterMsg.Message.newBuilder()
                    .setClientId(clientId)
                    .setType(ClusterMsg.MessageType.OFFLINE)
                    .setData(new Gson().toJson("offline......")).build();
            context.writeAndFlush(message);
        }
        UpdateWrapper<CollectorData> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("name", clientId);
        updateWrapper.eq("creator", "zj");
        CollectorData data = new CollectorData();
        data.setStatus((byte)1);
        collectorMapper.update(data, updateWrapper);
    }
}
