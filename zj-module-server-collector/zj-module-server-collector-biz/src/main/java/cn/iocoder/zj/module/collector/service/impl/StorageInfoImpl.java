package cn.iocoder.zj.module.collector.service.impl;

import cn.iocoder.zj.framework.common.dal.manager.StorageData;
import cn.iocoder.zj.module.collector.mapper.StorageInfoMapper;
import cn.iocoder.zj.module.collector.service.StorageInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class StorageInfoImpl implements StorageInfo {
    @Resource
    StorageInfoMapper storageInfoMapper;

    @Override
    public List<StorageData> selectByIdPlatformList(Long platformId) {
        return storageInfoMapper.selectList(new QueryWrapper<StorageData>().eq("platform_id", platformId));
    }

    @Override
    public void add(List<StorageData> added) {
        for (StorageData storageData : added) {
            storageData.setMediaType(null);
            storageInfoMapper.insert(storageData);
        }

    }

    @Override
    public void deleted(List<String> deletList) {
        for (String id : deletList) {
            storageInfoMapper.delete(new QueryWrapper<StorageData>().eq("uuid", id));
        }
    }

    @Override
    public void updated(List<StorageData> updated) {
        for (StorageData storageData : updated) {
            storageData.setUpdateTime(new Date());
            storageData.setMediaType(null);
            storageInfoMapper.update(storageData, new QueryWrapper<StorageData>().eq("uuid", storageData.getUuid()));
        }

    }
}
