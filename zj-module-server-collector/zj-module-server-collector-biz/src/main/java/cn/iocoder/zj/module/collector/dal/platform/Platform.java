package cn.iocoder.zj.module.collector.dal.platform;

import cn.iocoder.zj.module.collector.dal.ClientInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;


/**
 * 平台账号实体类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class Platform extends ClientInfo implements Serializable {

    /**
     * 主键ID
     */
    private Long platformId;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 接口URL
     */
    private String platformUrl;


    /**
     * 类型编码
     */
    private String typeCode;

    /**
     * 地区id
     */
    private Long regionId;

    /**
     * 状态
     */
    private Long state;

    /**
     * 时差
     */
    private Long diffTime;

    /**
     * 采集器时间
     */
    private Date dateTime;

    private Long akType;
}