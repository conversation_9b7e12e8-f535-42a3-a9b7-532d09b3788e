package cn.iocoder.zj.module.collector.util;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class SpringBeanUtils implements ApplicationContextAware {

    private static  ApplicationContext context;

    public static <T> T getBean(Class<T> c) {
        if (context == null) {
            throw new IllegalStateException("ApplicationContext is not initialized yet.");
        }
        return context.getBean(c);
    }

    public static <T> T getBean(String name, Class<T> clazz) {
        if (context == null) {
            throw new IllegalStateException("ApplicationContext is not initialized yet.");
        }
        return context.getBean(name, clazz);
    }

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        synchronized (SpringBeanUtils.class) {
            if (context == null) {
                context = applicationContext;
            } else {
                throw new IllegalStateException("ApplicationContext is already initialized.");
            }
        }
    }
}
