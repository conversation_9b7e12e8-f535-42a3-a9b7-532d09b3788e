package cn.iocoder.zj.module.collector.framework.netty.handler;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.ScanIPData;
import cn.iocoder.zj.framework.common.dal.manager.ScanIPRequest;
import cn.iocoder.zj.framework.common.enums.OtherEnum;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.framework.warehouse.store.history.HistoryDataWriter;
import cn.iocoder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.iocoder.zj.module.collector.collect.strategy.BasicStrategyFactory;
import cn.iocoder.zj.module.collector.config.WorkerPool;
import cn.iocoder.zj.module.collector.constants.BusinessConstants;
import cn.iocoder.zj.module.collector.dal.platform.Platform;
import cn.iocoder.zj.module.collector.framework.kafka.KafkaProducerService;
import cn.iocoder.zj.module.collector.framework.netty.auth.AESUtils;
import cn.iocoder.zj.module.collector.framework.netty.job.cache.TaskCacheModel;
import cn.iocoder.zj.module.collector.framework.netty.server.ChannelCtxMap;
import cn.iocoder.zj.module.collector.framework.netty.task.TaskHelper;
import cn.iocoder.zj.module.collector.monitor.AsyncProcessingMetrics;
import cn.iocoder.zj.module.collector.service.PlatformInfo;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.reflect.TypeToken;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.util.AttributeKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 **/
@Slf4j
public class ChannelHandler extends SimpleChannelInboundHandler<ClusterMsg.Message> {


    private final TaskHelper taskHelper;

    private final PlatformInfo platformInfo;

    private final HistoryDataWriter historyDataWriter;

    private final KafkaProducerService producerService;

    private final RedisTemplate redisTemplate;

    private final WorkerPool workerPool;

    private final AsyncProcessingMetrics processingMetrics;

    private static final AttributeKey<String> CLIENT_ID = AttributeKey.valueOf("clientId");

    private static final AttributeKey<String> CHANNEL_ID = AttributeKey.valueOf("channelId");

    private static final AttributeKey<String> IP_LIST = AttributeKey.valueOf("ipList");


    public ChannelHandler(PlatformInfo platformInfo, HistoryDataWriter historyDataWriter, TaskHelper taskHelper,
                          KafkaProducerService producerService, RedisTemplate redisTemplate, WorkerPool workerPool,
                          AsyncProcessingMetrics processingMetrics) {
        this.taskHelper = taskHelper;
        this.platformInfo = platformInfo;
        this.historyDataWriter = historyDataWriter;
        this.producerService = producerService;
        this.redisTemplate = redisTemplate;
        this.workerPool = workerPool;
        this.processingMetrics = processingMetrics;
    }


    /**
     * 读取到消息后进行处理 - 异步处理版本
     *
     * 性能优化：
     * 1. 将所有业务逻辑移到异步线程池执行，避免阻塞 Netty I/O 线程
     * 2. 保持异常处理和日志记录
     * 3. 添加性能监控和线程池饱和处理
     *
     * @param ctx         channel上下文
     * @param messageInfo 发送消息
     */
    @Override
    protected void channelRead0(ChannelHandlerContext ctx, ClusterMsg.Message messageInfo) {
        // 记录消息接收时间（在 I/O 线程中快速记录）
        long receiveTime = System.currentTimeMillis();

        log.debug("[消息接收] 客户端:{}, 消息类型:{}, 开始异步处理",
                messageInfo.getClientId(), messageInfo.getType());

        // 异步处理消息，避免阻塞 Netty I/O 线程
        try {
            workerPool.executeJob(() -> {
                processMessageAsync(ctx, messageInfo, receiveTime);
            });
        } catch (RejectedExecutionException e) {
            // 线程池饱和处理
            processingMetrics.recordRejection();
            log.error("[线程池饱和] 客户端:{}, 消息类型:{}, 消息被拒绝处理",
                    messageInfo.getClientId(), messageInfo.getType(), e);

            // 可选：发送错误响应给客户端
            sendErrorResponse(ctx, messageInfo, "服务器繁忙，请稍后重试");
        } catch (Exception e) {
            log.error("[异步提交失败] 客户端:{}, 消息类型:{}, 提交异步任务失败",
                    messageInfo.getClientId(), messageInfo.getType(), e);
        }
    }

    /**
     * 异步处理消息的核心方法
     * 在工作线程池中执行，不会阻塞 Netty I/O 线程
     */
    private void processMessageAsync(ChannelHandlerContext ctx, ClusterMsg.Message messageInfo, long receiveTime) {
        long startTime = System.currentTimeMillis();
        long queueTime = startTime - receiveTime; // 在队列中等待的时间

        try {
            // 原有的业务逻辑处理
            switch (messageInfo.getType().getNumber()) {
            case ClusterMsg.MessageType.AUTH_VALUE:
                sendAuthMessage(ctx, messageInfo);
                break;
            case ClusterMsg.MessageType.UPDATE_VALUE:
                sendUpdateMessage(ctx, messageInfo);
                break;
            case ClusterMsg.MessageType.SCHEDULED_INFO_VALUE:
                scheduledInfoMessage(messageInfo);
                break;
            case ClusterMsg.MessageType.BASIC_VALUE:
                BasicCollectData basicCollectData = new Gson().fromJson(messageInfo.getData(), BasicCollectData.class);
                if (basicCollectData == null) {
                    log.error("[数据解析失败] 客户端:{}, 原始数据:{}", messageInfo.getClientId(), messageInfo.getData());
                    return;
                }
                String platformName = "未知平台";
                if (CollUtil.isNotEmpty(basicCollectData.getBasicDataMap())) {
                    Object firstData = basicCollectData.getBasicDataMap().get(0);
                    if (firstData instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> dataMap = (Map<String, Object>) firstData;
                        platformName = String.valueOf(dataMap.getOrDefault("platformName", "未知平台"));
                    }
                }
                log.info("[基础数据采集] 客户端:{}, 平台:{}, 指标名称:{}, 数据量:{}",
                        messageInfo.getClientId(),
                        platformName,
                        basicCollectData.getMetricsName(),
                        CollUtil.isEmpty(basicCollectData.getBasicDataMap()) ? 0 : basicCollectData.getBasicDataMap().size());

                if (CollUtil.isNotEmpty(basicCollectData.getBasicDataMap())) {
                    String key = basicCollectData.getMetricsName();
                    List<?> results = basicCollectData.getBasicDataMap();
                    log.debug("[数据处理开始] 客户端:{}, 指标:{}, 处理数量:{}", messageInfo.getClientId(), key, results.size());
                    AbstractBasicData abstractCollect = BasicStrategyFactory.invoke(key);
                    abstractCollect.collectBasicData(results, basicCollectData.getMetricsType());
                    log.info("[数据处理完成] 客户端:{}, 指标:{}", messageInfo.getClientId(), key);
                }
                break;
            case ClusterMsg.MessageType.CPU_TASK_VALUE,
                 ClusterMsg.MessageType.DISK_TASK_VALUE,
                 ClusterMsg.MessageType.MEM_TASK_VALUE,
                 ClusterMsg.MessageType.NET_TASK_VALUE:
                log.info("[性能数据采集] 客户端:{}, 指标类型:{}, 开始异步并行处理",
                        messageInfo.getClientId(), messageInfo.getType());

                // 异步并行处理
                processPerformanceDataAsync(messageInfo);
                break;
            case ClusterMsg.MessageType.DETECT_VALUE,
                 ClusterMsg.MessageType.FAIL_VALUE:
                log.info("[平台状态更新] 客户端:{}, 消息类型:{}, 开始处理",
                        messageInfo.getClientId(), messageInfo.getType());
                try {
                    Platform platform = new Gson().fromJson(messageInfo.getData(), Platform.class);
                    platformInfo.updatePlatformInfo(platform);
                    log.info("[平台状态更新] 客户端:{}, 平台ID:{}, 更新成功",
                            messageInfo.getClientId(), platform.getPlatformId());
                } catch (Exception e) {
                    log.error("[平台状态异常] 客户端:{}, 更新失败, 原因:{}",
                            messageInfo.getClientId(), e.getMessage(), e);
                }
                break;
            case ClusterMsg.MessageType.ALERTER_VALUE:
                log.info("[告警消息处理] 客户端:{}, 开始处理告警消息", messageInfo.getClientId());
                try {
                    producerService.sendMessage(BusinessConstants.PERFORMANCE_TOPIC, messageInfo.getData());
                    log.info("[告警消息发送] 客户端:{}, 消息发送成功", messageInfo.getClientId());
                } catch (Exception e) {
                    log.error("[告警消息异常] 客户端:{}, 发送失败, 原因:{}",
                            messageInfo.getClientId(), e.getMessage(), e);
                }
                break;
            case ClusterMsg.MessageType.OTHER_VALUE:
                log.info("[其他消息处理] 客户端:{}, 消息类型:{}, 开始处理", messageInfo.getClientId(), messageInfo.getType());
                String metricsCode = messageInfo.getMetrics();
                if (OtherEnum.ADVANCES_PING.code().equals(metricsCode) ||
                        OtherEnum.ADVANCES_TCP.code().equals(metricsCode) ||
                        OtherEnum.ADVANCES_SNMP.code().equals(metricsCode)) {

                    ScanIPRequest scanIPRequest = new Gson().fromJson(messageInfo.getData(), ScanIPRequest.class);
                    String redisKey = "SCAN_IP:" + scanIPRequest.getIpRangeId();

                    if (!redisTemplate.hasKey(redisKey)) break;
                    ScanIPRequest redis = new Gson().fromJson(redisTemplate.opsForValue().get(redisKey).toString(), ScanIPRequest.class);
                    if (OtherEnum.ADVANCES_PING.code().equals(metricsCode)) {
                        redis.setPingCount(scanIPRequest.getPingCount());
                    } else if (OtherEnum.ADVANCES_TCP.code().equals(metricsCode)) {
                        redis.setTcpCount(scanIPRequest.getTcpCount());
                    } else {
                        redis.setSnmpCount(scanIPRequest.getSnmpCount());
                    }
                    int totalCount = redis.getPingCount() + redis.getTcpCount() + redis.getSnmpCount();
                    int ratio = redis.getTotal() == 0 ? 0 : (int) Math.round((totalCount * 100.0) / redis.getTotal());
                    redis.setRatio(ratio);
                    redisTemplate.opsForValue().set(redisKey, JsonUtils.toJsonString(redis), 1, TimeUnit.DAYS);
                    if (redis.getTotal() == totalCount) {
                        platformInfo.updateScanIPState(redis.getIpRangeId());
                    }
                }else if (Arrays.asList(OtherEnum.PING.code(), OtherEnum.TCP.code(), OtherEnum.SNMP.code()).contains(metricsCode)) {
                    List<ScanIPData> list = new Gson().fromJson(messageInfo.getData(), new TypeToken<List<ScanIPData>>() {}.getType());
                    log.info("[自动发现-返回] 客户端:{}, 消息类型:{}, 数据:{}",messageInfo.getClientId(), metricsCode, new Gson().toJson(list.get(0)));
                    platformInfo.updateScanIP(list);
                }
                break;
            default:
                log.warn("[未知消息类型] 客户端:{}, 消息类型:{}, 无法处理",
                        messageInfo.getClientId(), messageInfo.getType());
                break;
        }

        // 记录处理完成时间和性能指标
        long endTime = System.currentTimeMillis();
        long processingTime = endTime - startTime;
        long totalTime = endTime - receiveTime;

        // 记录性能指标
        processingMetrics.recordProcessing(messageInfo.getType(), processingTime, queueTime);

        log.info("[消息处理完成] 客户端:{}, 消息类型:{}, 处理耗时:{}ms, 总耗时:{}ms",
                messageInfo.getClientId(), messageInfo.getType(), processingTime, totalTime);

        } catch (Exception e) {
            // 异步处理中的异常捕获，不会影响 I/O 线程
            long endTime = System.currentTimeMillis();
            long processingTime = endTime - startTime;

            // 记录失败指标
            processingMetrics.recordFailure(messageInfo.getType());

            log.error("[异步处理异常] 客户端:{}, 消息类型:{}, 处理耗时:{}ms, 异常信息:{}",
                    messageInfo.getClientId(), messageInfo.getType(), processingTime, e.getMessage(), e);

            // 可选：发送错误响应给客户端
            sendErrorResponse(ctx, messageInfo, "消息处理失败: " + e.getMessage());
        }
    }

    /**
     * 发送错误响应给客户端
     */
    private void sendErrorResponse(ChannelHandlerContext ctx, ClusterMsg.Message originalMessage, String errorMessage) {
        try {
            if (ctx.channel().isActive()) {
                ClusterMsg.Message errorResponse = ClusterMsg.Message.newBuilder()
                        .setClientId(originalMessage.getClientId())
                        .setType(ClusterMsg.MessageType.OTHER) // 或定义专门的错误类型
                        .setData("{\"error\":\"" + errorMessage + "\"}")
                        .build();

                ctx.writeAndFlush(errorResponse);
                log.debug("[错误响应] 客户端:{}, 错误信息已发送", originalMessage.getClientId());
            }
        } catch (Exception e) {
            log.error("[错误响应失败] 客户端:{}, 发送错误响应失败", originalMessage.getClientId(), e);
        }
    }

    /**
     * 收到定时任务消息
     *
     * @param messageInfo
     */
    private void scheduledInfoMessage(ClusterMsg.Message messageInfo) {
        Type listType = new TypeToken<List<TaskCacheModel>>() {
        }.getType();
        List<TaskCacheModel> cacheModelsList = new GsonBuilder()
                .excludeFieldsWithoutExposeAnnotation()
                .create().fromJson(messageInfo.getData(), listType);
        ChannelCtxMap.scheduledInfo(messageInfo.getClientId(), cacheModelsList);
    }

    /**
     * 客户端上线
     *
     * @param ctx channel上下文
     */
    @Override
    public void handlerAdded(ChannelHandlerContext ctx) {
        log.info("{}连接", ctx.channel().id().asLongText());
    }

    /**
     * 客户端下线
     */
    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        ChannelCtxMap.removeChannelCtx(ctx);
        String clientId = ctx.channel().attr(CLIENT_ID).get();
        taskHelper.yqTask(clientId);
    }


    /**
     * 更新配置
     *
     * @param ctx
     * @param messageInfo
     */
    private void sendUpdateMessage(ChannelHandlerContext ctx, ClusterMsg.Message messageInfo) {
        List<Platform> platformInfos = platformInfo.getPlatformByTenantName(messageInfo.getClientId());
        platformInfos.forEach(platform -> platform.setClientId(messageInfo.getClientId()));
        ClusterMsg.Message message = ClusterMsg.Message.newBuilder()
                .setClientId(messageInfo.getClientId())
                .setType(ClusterMsg.MessageType.UPDATE)
                .setData(new Gson().toJson(platformInfos)).build();
        ChannelCtxMap.addChannelCtx(ctx, messageInfo.getClientId());
        ctx.writeAndFlush(message);
    }

    /**
     * 发送鉴权消息
     *
     * @param ctx
     * @param messageInfo
     */
    private void sendAuthMessage(ChannelHandlerContext ctx, ClusterMsg.Message messageInfo) {
        String jsonData = messageInfo.getData();
        JsonObject jsonObject = JsonParser.parseString(jsonData).getAsJsonObject();
        String clientSecretKey = jsonObject.get("clientSecretKey").getAsString();
        Gson gson = new Gson();
        Type mapType = new TypeToken<Map<String, String>>() {
        }.getType();
        Map<String, String> serverInfoMap = gson.fromJson(jsonObject.getAsJsonObject("serverInfoMap"), mapType);
        List<String> valuesList = List.copyOf(serverInfoMap.values());
        String ipList = gson.toJson(valuesList);
        String decrypt = AESUtils.decrypt(clientSecretKey);
        if (BusinessConstants.COLLECTOR_AUTH.equals(decrypt)) {
            ctx.channel().attr(CLIENT_ID).set(messageInfo.getClientId());
            ctx.channel().attr(CHANNEL_ID).set(ctx.channel().id().asLongText());
            ctx.channel().attr(IP_LIST).set(ipList);
            //发一条鉴权信息
            ClusterMsg.Message authMessage = ClusterMsg.Message.newBuilder()
                    .setClientId(messageInfo.getClientId())
                    .setType(ClusterMsg.MessageType.AUTH)
                    .setData(new Gson().toJson("auth......")).build();
            ctx.writeAndFlush(authMessage);
            //发送基础信息
            // 查询全部
//            List<Platform> platformInfos = platformInfo.getPlatformInfos();
            // 根据客户端查询
            List<Platform> platformInfos = platformInfo.getPlatformByTenantName(messageInfo.getClientId());
            platformInfos.forEach(platform -> platform.setClientId(messageInfo.getClientId()));
            ClusterMsg.Message platformInfoMessage = ClusterMsg.Message.newBuilder()
                    .setClientId(messageInfo.getClientId())
                    .setType(ClusterMsg.MessageType.INFO)
                    .setData(new Gson().toJson(platformInfos)).build();
            ChannelCtxMap.addChannelCtx(ctx, messageInfo.getClientId());
            ctx.writeAndFlush(platformInfoMessage);
            taskHelper.yqTask(messageInfo.getClientId());
            // todo 采集器表入库

        } else {
            ClusterMsg.Message.Builder builder = ClusterMsg.Message.newBuilder()
                    .setClientId(messageInfo.getClientId())
                    .setType(ClusterMsg.MessageType.AUTH)
                    .setData("Authentication failed . . .");
            ctx.writeAndFlush(builder);
        }
    }



    /**
     * 异步并行处理性能数据
     *
     * 优化策略：
     * 1. 数据库保存和 Kafka 发送并行执行，减少总延迟
     * 2. 独立的错误处理，单个操作失败不影响另一个
     * 3. 详细的性能监控和日志记录
     * 4. 优雅的异常处理，确保系统稳定性
     *
     * @param messageInfo 消息信息
     */
    private void processPerformanceDataAsync(ClusterMsg.Message messageInfo) {
        long startTime = System.currentTimeMillis();
        String clientId = messageInfo.getClientId();
        ClusterMsg.MessageType messageType = messageInfo.getType();

        // 创建数据库保存任务 - 完全异步，不阻塞
        try {
            workerPool.executeJob(() -> {
                long saveStartTime = System.currentTimeMillis();
                try {
                    historyDataWriter.saveData(messageInfo);
                    long saveDuration = System.currentTimeMillis() - saveStartTime;

                    log.debug("[历史数据保存成功] 客户端:{}, 消息类型:{}, 耗时:{}ms",
                            clientId, messageType, saveDuration);

                    // 慢保存警告
                    if (saveDuration > 1000) {
                        log.warn("[历史数据慢保存] 客户端:{}, 消息类型:{}, 保存耗时:{}ms",
                                clientId, messageType, saveDuration);
                    }

                } catch (Exception e) {
                    long saveDuration = System.currentTimeMillis() - saveStartTime;
                    log.error("[历史数据保存失败] 客户端:{}, 消息类型:{}, 耗时:{}ms, 错误:{}",
                            clientId, messageType, saveDuration, e.getMessage(), e);

                    // 记录失败指标
                    processingMetrics.recordFailure(messageType);
                }
            });
        } catch (RejectedExecutionException e) {
            log.error("[数据库保存任务提交失败] 客户端:{}, 消息类型:{}, 线程池饱和",
                    clientId, messageType, e);
            processingMetrics.recordFailure(messageType);
        }

        // 创建 Kafka 发送任务 - 完全异步，不阻塞
        try {
            workerPool.executeJob(() -> {
                long kafkaStartTime = System.currentTimeMillis();
                try {
                    producerService.sendMessageAsync(BusinessConstants.PERFORMANCE_TOPIC, messageInfo.getData());
                    long kafkaDuration = System.currentTimeMillis() - kafkaStartTime;

                    log.debug("[Kafka发送成功] 客户端:{}, 消息类型:{}, 耗时:{}ms",
                            clientId, messageType, kafkaDuration);

                } catch (Exception e) {
                    long kafkaDuration = System.currentTimeMillis() - kafkaStartTime;
                    log.error("[Kafka发送失败] 客户端:{}, 消息类型:{}, 耗时:{}ms, 错误:{}",
                            clientId, messageType, kafkaDuration, e.getMessage(), e);

                    // Kafka 发送失败不影响整体处理，因为有重试机制
                }
            });
        } catch (RejectedExecutionException e) {
            log.error("[Kafka发送任务提交失败] 客户端:{}, 消息类型:{}, 线程池饱和",
                    clientId, messageType, e);
        }

        // 完全异步执行，不等待结果 - 最大化性能
        // 数据库保存和 Kafka 发送完全独立，互不影响
        log.debug("[性能数据处理] 客户端:{}, 消息类型:{}, 已提交异步并行任务",
                clientId, messageType);
    }

}
