package cn.iocoder.zj.module.collector.service;


import cn.iocoder.zj.framework.common.dal.manager.HostSecGroupData;
import cn.iocoder.zj.framework.common.dal.manager.SecGroupData;
import cn.iocoder.zj.framework.common.dal.manager.SecGroupRuleData;

import java.util.List;

public interface SecGroupInfo {

    List<SecGroupData> selectByIdPlatformList(Long platformId);

    void add(List<SecGroupData> added);

    void deleted(List<String> deletList);

    void updated(List<SecGroupData> updated);


    List<HostSecGroupData> selectHostSecGroupPlatformList(Long platformId);

    void addHostSecGroup(List<HostSecGroupData> added);

    void deletedHostSecGroup(List<Long> deletList);

    void updatedHostSecGroup(List<HostSecGroupData> updated);

    List<SecGroupRuleData> selectSecGroupRulePlatformList(Long platformId);

    void addSecGroupRule(List<SecGroupRuleData> added);

    void deletedSecGroupRule(List<String> deletList);

    void updatedSecGroupRule(List<SecGroupRuleData> updated);


}
