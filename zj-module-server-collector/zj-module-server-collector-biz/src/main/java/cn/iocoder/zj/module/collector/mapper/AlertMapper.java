package cn.iocoder.zj.module.collector.mapper;



import cn.iocoder.zj.module.collector.dal.alert.AlertDo;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface AlertMapper extends BaseMapper<AlertDo> {
    
    @Slave
    AlertDo getAlarmByMonitorId(@Param("monitorId") Long monitorId);
    
}
