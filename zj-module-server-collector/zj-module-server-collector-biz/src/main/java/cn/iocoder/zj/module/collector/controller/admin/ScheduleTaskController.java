package cn.iocoder.zj.module.collector.controller.admin;

import cn.iocoder.zj.module.collector.dal.schedule.ScheduleTask;
import cn.iocoder.zj.module.collector.framework.netty.job.cache.TaskModel;
import cn.iocoder.zj.module.collector.service.ScheduleTaskService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 **/
@RestController
@RequestMapping("/server-collector/timer")
public class ScheduleTaskController {

    @Resource
    ScheduleTaskService scheduleTaskService;

    @PostMapping("/start")
    public  void startTask(ScheduleTask scheduleTask){
        scheduleTaskService.startTask(scheduleTask);
    }

    @PostMapping("/stop")
    public  void stopTask(ScheduleTask scheduleTask){
        scheduleTaskService.stopTask(scheduleTask);
    }


    @PostMapping("/modify")
    public  void modifyTask(ScheduleTask scheduleTask){
        scheduleTaskService.modifyTask(scheduleTask);
    }

    @PostMapping("/refreshList")
    public  void refreshList(){
        scheduleTaskService.refreshList();
    }

    @PostMapping("/list")
    public List<TaskModel> list(){
        return scheduleTaskService.list();
    }
}
