package cn.iocoder.zj.module.collector.collect.basicdata.manager;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.zj.framework.common.dal.manager.HostData;
import cn.iocoder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.iocoder.zj.module.collector.service.HostDataInfo;
import cn.iocoder.zj.module.collector.util.DataSetAnalyzerUtils;
import cn.iocoder.zj.module.collector.util.OptimizedDataConverter;
import cn.iocoder.zj.module.collector.util.SpringBeanUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_HOST;

@Slf4j
public class BasicHostDataImpl extends AbstractBasicData {

    @Override
    public void preCheck() {

    }

    @Override
    public void collectBasicData(List<?> results,String type) {
        // 性能优化：使用优化的数据转换器替代双重序列化
        OptimizedDataConverter converter = SpringBeanUtils.getBean(OptimizedDataConverter.class);

        long startTime = System.currentTimeMillis();
        List<HostData> newHardwareList = converter.convertList(results, HostData.class);
        long endTime = System.currentTimeMillis();

        log.debug("[性能优化] HostData 转换完成. 数据量: {}, 耗时: {}ms",
                results.size(), endTime - startTime);
        // 获取宿主机老列表
        HostDataInfo hostDataInfo = SpringBeanUtils.getBean(HostDataInfo.class);
        List<HostData> oldHostData = hostDataInfo.selectByIdPlatformList(newHardwareList.get(0).getPlatformId());
        Map<String, List<HostData>> result = DataSetAnalyzerUtils.compareCollectionsByKey(oldHostData, newHardwareList,HostData::getUuid,(newObj, oldObj) -> newObj.setId(oldObj.getId()) );
        List<HostData> added = result.get("added");
        if (CollectionUtil.isNotEmpty(added)) {
            hostDataInfo.add(added);
        }
        List<HostData> deleted = result.get("deleted");
        if (CollectionUtil.isNotEmpty(deleted)) {
            List<String> deletList = deleted.stream().map(HostData::getUuid).collect(Collectors.toList());
            hostDataInfo.deleted(deletList);
        }
        List<HostData> updated = result.get("updated");
        if (CollectionUtil.isNotEmpty(updated)) {
            hostDataInfo.updated(updated);
        }

    }

    @Override
    public String supportProtocol() {
        return BASIC_HOST.code();
    }
}
