package cn.iocoder.zj.module.collector.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.google.gson.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 优化的数据转换工具类
 * 解决双重序列化性能问题
 * 
 * 性能优化：
 * 1. 使用单例 Gson 实例，避免重复创建
 * 2. 优先使用 BeanUtil.copyProperties 进行对象转换
 * 3. 降级到 JSON 序列化作为备选方案
 * 4. 支持批量转换，提升处理效率
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Component
public class OptimizedDataConverter {

    /**
     * 单例 Gson 实例，线程安全
     * 配置了自定义日期反序列化器以支持多种日期格式
     */
    private static final Gson GSON = new GsonBuilder()
            .registerTypeAdapter(Date.class, new MultiFormatDateDeserializer())
            .setDateFormat("yyyy-MM-dd HH:mm:ss") // 默认格式
            .serializeNulls()
            .create();

    /**
     * 性能监控组件
     */
    @Autowired(required = false)
    private DataConversionMetrics metrics;

    /**
     * 多格式日期反序列化器
     * 支持多种常见的日期格式
     */
    private static class MultiFormatDateDeserializer implements JsonDeserializer<Date> {

        private static final String[] DATE_FORMATS = {
            "MMM dd, yyyy, h:mm:ss a",      // Aug 19, 2020, 5:25:01 PM
            "MMM dd, yyyy h:mm:ss a",       // Aug 19, 2020 5:25:01 PM
            "yyyy-MM-dd HH:mm:ss",          // 2020-08-19 17:25:01
            "yyyy-MM-dd'T'HH:mm:ss",        // 2020-08-19T17:25:01
            "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", // 2020-08-19T17:25:01.123Z
            "yyyy-MM-dd'T'HH:mm:ss.SSSZ",   // 2020-08-19T17:25:01.123+0800
            "yyyy/MM/dd HH:mm:ss",          // 2020/08/19 17:25:01
            "dd/MM/yyyy HH:mm:ss",          // 19/08/2020 17:25:01
            "MM/dd/yyyy HH:mm:ss",          // 08/19/2020 17:25:01
            "yyyy-MM-dd",                   // 2020-08-19
            "MM/dd/yyyy",                   // 08/19/2020
            "dd-MM-yyyy"                    // 19-08-2020
        };

        @Override
        public Date deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context)
                throws JsonParseException {

            String dateString = json.getAsString();

            // 尝试解析各种日期格式
            for (String format : DATE_FORMATS) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat(format, Locale.ENGLISH);
                    sdf.setLenient(true); // 宽松模式
                    return sdf.parse(dateString);
                } catch (ParseException e) {
                    // 继续尝试下一个格式
                }
            }

            // 如果所有格式都失败，尝试使用时间戳
            try {
                long timestamp = Long.parseLong(dateString);
                return new Date(timestamp);
            } catch (NumberFormatException e) {
                // 最后尝试默认解析
                try {
                    return new Date(dateString);
                } catch (Exception ex) {
                    log.warn("[日期解析] 无法解析日期字符串: {}", dateString);
                    return null; // 返回 null 而不是抛异常
                }
            }
        }
    }
    
    /**
     * 高性能单对象转换
     * 优先使用 BeanUtil，失败时降级到 JSON 转换
     *
     * @param source 源对象
     * @param targetClass 目标类型
     * @param <T> 目标类型泛型
     * @return 转换后的对象
     */
    public <T> T convert(Object source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }

        long startTime = System.currentTimeMillis();

        try {
            T result;

            // 特殊处理：如果源对象是 LinkedTreeMap（通常来自 JSON 解析）
            // 直接使用 JSON 转换，避免 BeanUtil 的类型转换问题
            if (source instanceof LinkedHashMap || source instanceof Map) {
                String json = GSON.toJson(source);
                result = GSON.fromJson(json, targetClass);
                log.debug("[JSON转换] LinkedTreeMap 转换成功. 目标类型: {}", targetClass.getSimpleName());
            } else {
                // 对于普通对象，优先使用 BeanUtil 进行属性拷贝，性能最佳
                try {
                    result = BeanUtil.copyProperties(source, targetClass);
                    log.debug("[BeanUtil转换] 转换成功. 源类型: {}, 目标类型: {}",
                            source.getClass().getSimpleName(), targetClass.getSimpleName());
                } catch (Exception beanUtilException) {
                    log.debug("[BeanUtil转换] 转换失败，降级到 JSON 转换. 源类型: {}, 目标类型: {}, 错误: {}",
                            source.getClass().getSimpleName(), targetClass.getSimpleName(), beanUtilException.getMessage());

                    // 降级方案：使用单例 Gson 进行 JSON 转换
                    String json = GSON.toJson(source);
                    result = GSON.fromJson(json, targetClass);
                }
            }

            // 记录性能指标
            long duration = System.currentTimeMillis() - startTime;
            if (metrics != null) {
                metrics.recordConversion(targetClass, 1, duration);
            }

            return result;

        } catch (Exception e) {
            log.error("[数据转换] 所有转换方式都失败. 源类型: {}, 目标类型: {}, 错误: {}",
                    source.getClass().getSimpleName(), targetClass.getSimpleName(), e.getMessage());
            log.debug("[数据转换] 源数据: {}", source);
            throw new RuntimeException("数据转换失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 高性能批量转换
     * 使用并行流处理大数据集，提升转换效率
     *
     * @param sources 源对象列表
     * @param targetClass 目标类型
     * @param <T> 目标类型泛型
     * @return 转换后的对象列表
     */
    public <T> List<T> convertList(List<?> sources, Class<T> targetClass) {
        if (CollectionUtil.isEmpty(sources)) {
            return Collections.emptyList();
        }

        long startTime = System.currentTimeMillis();

        // 小数据集使用串行处理，大数据集使用并行处理
        boolean useParallel = sources.size() > 100;

        try {
            List<T> result;
            if (useParallel) {
                result = sources.parallelStream()
                        .map(item -> convertSingle(item, targetClass))
                        .collect(Collectors.toList());
            } else {
                result = sources.stream()
                        .map(item -> convertSingle(item, targetClass))
                        .collect(Collectors.toList());
            }

            // 记录批量转换性能指标
            long duration = System.currentTimeMillis() - startTime;
            if (metrics != null) {
                metrics.recordConversion(targetClass, sources.size(), duration);
            }

            return result;
        } catch (Exception e) {
            log.error("[批量数据转换] 转换失败. 源数据量: {}, 目标类型: {}",
                    sources.size(), targetClass.getSimpleName(), e);
            throw new RuntimeException("批量数据转换失败", e);
        }
    }

    /**
     * 单个对象转换（不记录性能指标，避免重复记录）
     */
    private <T> T convertSingle(Object source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }

        // 特殊处理：如果源对象是 LinkedTreeMap（通常来自 JSON 解析）
        // 直接使用 JSON 转换，避免 BeanUtil 的类型转换问题
        if (source instanceof LinkedHashMap || source instanceof Map) {
            try {
                String json = GSON.toJson(source);
                return GSON.fromJson(json, targetClass);
            } catch (Exception jsonException) {
                log.error("[JSON转换] LinkedTreeMap 转换失败. 目标类型: {}, 错误: {}",
                        targetClass.getSimpleName(), jsonException.getMessage());
                log.debug("[JSON转换] 源数据: {}", source);
                throw new RuntimeException("LinkedTreeMap 转换失败: " + jsonException.getMessage(), jsonException);
            }
        }

        try {
            // 对于普通对象，优先使用 BeanUtil 进行属性拷贝，性能最佳
            return BeanUtil.copyProperties(source, targetClass);
        } catch (Exception e) {
            log.debug("[BeanUtil转换] 转换失败，降级到 JSON 转换. 源类型: {}, 目标类型: {}, 错误: {}",
                    source.getClass().getSimpleName(), targetClass.getSimpleName(), e.getMessage());

            try {
                // 降级方案：使用单例 Gson 进行 JSON 转换
                String json = GSON.toJson(source);
                return GSON.fromJson(json, targetClass);
            } catch (Exception jsonException) {
                log.error("[数据转换] 所有转换方式都失败. 源类型: {}, 目标类型: {}",
                        source.getClass().getSimpleName(), targetClass.getSimpleName(), jsonException);
                log.debug("[数据转换] 源数据: {}", source);
                throw new RuntimeException("数据转换失败: " + jsonException.getMessage(), jsonException);
            }
        }
    }
    
    /**
     * 专用于 JSON 字符串转换的方法
     * 当需要明确使用 JSON 转换时调用
     * 
     * @param jsonString JSON 字符串
     * @param targetClass 目标类型
     * @param <T> 目标类型泛型
     * @return 转换后的对象
     */
    public <T> T fromJson(String jsonString, Class<T> targetClass) {
        try {
            return GSON.fromJson(jsonString, targetClass);
        } catch (Exception e) {
            log.error("[JSON转换] 转换失败. JSON: {}, 目标类型: {}", 
                    jsonString, targetClass.getSimpleName(), e);
            throw new RuntimeException("JSON 转换失败", e);
        }
    }
    
    /**
     * 专用于对象转 JSON 字符串的方法
     * 
     * @param object 源对象
     * @return JSON 字符串
     */
    public String toJson(Object object) {
        try {
            return GSON.toJson(object);
        } catch (Exception e) {
            log.error("[对象转JSON] 转换失败. 对象类型: {}", 
                    object != null ? object.getClass().getSimpleName() : "null", e);
            throw new RuntimeException("对象转 JSON 失败", e);
        }
    }
    
    /**
     * 性能统计方法 - 用于监控转换性能
     * 
     * @param sources 源数据
     * @param targetClass 目标类型
     * @param <T> 目标类型泛型
     * @return 转换结果和性能统计
     */
    public <T> ConversionResult<T> convertListWithStats(List<?> sources, Class<T> targetClass) {
        if (CollectionUtil.isEmpty(sources)) {
            return new ConversionResult<>(Collections.emptyList(), 0, 0);
        }
        
        long startTime = System.currentTimeMillis();
        List<T> result = convertList(sources, targetClass);
        long endTime = System.currentTimeMillis();
        
        long duration = endTime - startTime;
        
        // 记录性能统计
        if (duration > 100) { // 超过100ms记录警告
            log.warn("[性能统计] 批量转换耗时较长. 数据量: {}, 耗时: {}ms, 目标类型: {}", 
                    sources.size(), duration, targetClass.getSimpleName());
        } else {
            log.debug("[性能统计] 批量转换完成. 数据量: {}, 耗时: {}ms, 目标类型: {}", 
                    sources.size(), duration, targetClass.getSimpleName());
        }
        
        return new ConversionResult<>(result, sources.size(), duration);
    }
    
    /**
     * 转换结果包装类，包含性能统计信息
     */
    public static class ConversionResult<T> {
        private final List<T> data;
        private final int sourceCount;
        private final long durationMs;
        
        public ConversionResult(List<T> data, int sourceCount, long durationMs) {
            this.data = data;
            this.sourceCount = sourceCount;
            this.durationMs = durationMs;
        }
        
        public List<T> getData() {
            return data;
        }
        
        public int getSourceCount() {
            return sourceCount;
        }
        
        public long getDurationMs() {
            return durationMs;
        }
        
        public double getItemsPerSecond() {
            return durationMs > 0 ? (sourceCount * 1000.0 / durationMs) : 0;
        }
    }
}
