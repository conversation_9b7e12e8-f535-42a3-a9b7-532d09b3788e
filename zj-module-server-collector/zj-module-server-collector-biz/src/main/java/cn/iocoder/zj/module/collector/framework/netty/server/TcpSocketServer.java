package cn.iocoder.zj.module.collector.framework.netty.server;


import cn.iocoder.zj.framework.common.message.ClusterMsg;
import cn.iocoder.zj.framework.warehouse.store.history.HistoryDataWriter;
import cn.iocoder.zj.module.collector.config.WorkerPool;
import cn.iocoder.zj.module.collector.framework.kafka.KafkaProducerService;
import cn.iocoder.zj.module.collector.framework.netty.handler.ChannelHandler;
import cn.iocoder.zj.module.collector.framework.netty.interfaces.TcpServer;
import cn.iocoder.zj.module.collector.framework.netty.task.TaskHelper;
import cn.iocoder.zj.module.collector.monitor.AsyncProcessingMetrics;
import cn.iocoder.zj.module.collector.service.PlatformInfo;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.codec.compression.ZlibCodecFactory;
import io.netty.handler.codec.compression.ZlibWrapper;
import io.netty.handler.codec.protobuf.ProtobufDecoder;
import io.netty.handler.codec.protobuf.ProtobufEncoder;
import io.netty.handler.codec.protobuf.ProtobufVarint32FrameDecoder;
import io.netty.handler.codec.protobuf.ProtobufVarint32LengthFieldPrepender;
import io.netty.handler.timeout.IdleStateHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 **/
@Slf4j
@Component
@ConditionalOnProperty(prefix = "collector-server", value = "enable", havingValue = "true", matchIfMissing = true)
public class TcpSocketServer implements TcpServer {

    @Value("${collector-server.port}")
    private int port;

    private volatile boolean ready = false;

    private EventLoopGroup bossGroup;
    private EventLoopGroup workGroup;

    private  final TaskHelper taskHelper;

    private  final PlatformInfo platformInfo;

    private final HistoryDataWriter historyDataWriter;

    private final KafkaProducerService producerService;

    private final RedisTemplate redisTemplate;

    private final WorkerPool workerPool;

    private  final AsyncProcessingMetrics asyncProcessingMetrics;



    public TcpSocketServer(TaskHelper taskHelper, PlatformInfo platformInfo, HistoryDataWriter historyDataWriter, KafkaProducerService producerService, RedisTemplate redisTemplate, WorkerPool workerPool, AsyncProcessingMetrics asyncProcessingMetrics) {
        this.taskHelper = taskHelper;
        this.platformInfo = platformInfo;
        this.historyDataWriter = historyDataWriter;
        this.producerService = producerService;
        this.redisTemplate = redisTemplate;
        this.workerPool = workerPool;
        this.asyncProcessingMetrics = asyncProcessingMetrics;
    }

    @Override
    public boolean isReady() {
        return ready;
    }

    @Override
    public void start() {
        ServerBootstrap bootstrap = new ServerBootstrap();
        bossGroup = new NioEventLoopGroup();
        workGroup = new NioEventLoopGroup();
        bootstrap.group(bossGroup, workGroup)
                .channel(NioServerSocketChannel.class)
                .childHandler(new ChannelInitializer<>() {
                    @Override
                    protected void initChannel(Channel ch) {
                        // 获取职责链
                        ChannelPipeline pipeline = ch.pipeline();
                        pipeline.addLast(ZlibCodecFactory.newZlibEncoder(ZlibWrapper.GZIP));
                        pipeline.addLast(ZlibCodecFactory.newZlibDecoder(ZlibWrapper.GZIP));
                        pipeline.addLast(new IdleStateHandler(120, 0, 0, TimeUnit.SECONDS));
                        pipeline.addLast(new ProtobufVarint32FrameDecoder());
                        pipeline.addLast(new ProtobufDecoder(ClusterMsg.Message.getDefaultInstance()));
                        pipeline.addLast(new ProtobufVarint32LengthFieldPrepender());
                        pipeline.addLast(new ProtobufEncoder());
                        pipeline.addLast("handler", new ChannelHandler(platformInfo,
                                historyDataWriter,taskHelper,producerService,redisTemplate,workerPool,asyncProcessingMetrics));
                    }
                })
                .option(ChannelOption.SO_BACKLOG, 5)
                .childOption(ChannelOption.SO_KEEPALIVE, true);
        try {
            bootstrap.bind(port).sync().channel();
            this.ready = true;
            log.info("tcp server 初始化完成,端口：{}", port);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.info("tcp server 初始化异常", e);
        }
    }

    @Override
    public void stop() {
        if (bossGroup != null && !bossGroup.isShuttingDown() && !bossGroup.isShutdown()) {
            bossGroup.shutdownGracefully();
        }
        if (workGroup != null && !workGroup.isShuttingDown() && !workGroup.isShutdown()) {
            workGroup.shutdownGracefully();
        }
        this.ready = false;
        log.info("tcp server 停止");
    }

}
