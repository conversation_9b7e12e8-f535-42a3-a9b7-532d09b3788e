package cn.iocoder.zj.module.collector.controller;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.zj.framework.common.dal.manager.ScanIPData;
import cn.iocoder.zj.framework.common.dal.manager.ScanIPRequest;
import cn.iocoder.zj.framework.common.enums.OtherEnum;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.module.collector.api.ScanIPApi;
import cn.iocoder.zj.module.collector.framework.netty.server.ChannelCtxMap;
import com.google.gson.Gson;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
@RestController
public class ScanIPController implements ScanIPApi {

    @Resource
    private RedisTemplate redisTemplate;

    @Override
    public CommonResult<String> scanIpChange(ScanIPRequest request){
        if (CollUtil.isEmpty(request.getScanIPDataList())) {
            return CommonResult.success("扫描ip不能为空");
        }

        List<ChannelHandlerContext> channelMap = ChannelCtxMap.getChannelCtxByClientId(request.getTenantName());
        if (CollUtil.isEmpty(channelMap)) {
            return CommonResult.success("未找到该租户下的采集器，采集器："+ request.getTenantName());
        }

        ChannelHandlerContext channelHandlerContext = channelMap.get(0);
        if (channelHandlerContext == null || !channelHandlerContext.channel().isActive()) {
            return CommonResult.success("采集器连接不可用，采集器："+ request.getTenantName());
        }

        // 按设备类型分组并发送消息
        Map<String, List<ScanIPData>> deviceGroups = groupDevicesBySupport(request.getScanIPDataList());
        AtomicInteger count = new AtomicInteger(0);

        deviceGroups.forEach((type, devices) -> {
            if (CollUtil.isNotEmpty(devices)) {
                count.addAndGet(devices.size());
                sendDeviceMessage(type, devices, request.getTenantName(), channelHandlerContext);
            }
        });

        // 更新请求信息并保存到Redis
        request.setTotal(count.get());
        request.setScanIPDataList(null);
        redisTemplate.opsForValue().set("SCAN_IP:" + request.getIpRangeId(), JsonUtils.toJsonString(request), 1, TimeUnit.DAYS);

        return CommonResult.success(null);
    }

    private void sendDeviceMessage(String type, List<ScanIPData> devices, String tenantName,ChannelHandlerContext channelHandlerContext) {
        OtherEnum metricType = getMetricType(type);
        if (metricType == null) {
            log.warn("未知设备类型：{}", type);
            throw new IllegalArgumentException("未知设备类型");
        }

        ClusterMsg.Message message = ClusterMsg.Message.newBuilder()
                .setClientId(tenantName)
                .setType(ClusterMsg.MessageType.OTHER)
                .setMetrics(metricType.code())
                .setData(new Gson().toJson(devices))
                .build();

        channelHandlerContext.channel().writeAndFlush(message);
    }

    private OtherEnum getMetricType(String type) {
        switch (type.toLowerCase()) {
            case "ping":
                return OtherEnum.PING;
            case "snmp":
                return OtherEnum.SNMP;
            case "tcp":
                return OtherEnum.TCP;
            default:
                return null;
        }
    }

    private Map<String, List<ScanIPData>> groupDevicesBySupport(List<ScanIPData> devices) {
        if (CollUtil.isEmpty(devices)) {
            Map<String, List<ScanIPData>> emptyMap = new HashMap<>();
            emptyMap.put("ping", Collections.emptyList());
            emptyMap.put("snmp", Collections.emptyList());
            emptyMap.put("tcp", Collections.emptyList());
            return emptyMap;
        }

        Map<String, Predicate<ScanIPData>> predicates = new HashMap<>();
        predicates.put("ping", data -> data.getPingSupport() != null && data.getPingSupport() == 1);
        predicates.put("snmp", data -> data.getSnmpSupport() != null && data.getSnmpSupport() == 1);
        predicates.put("tcp", data -> data.getTcpSupport() != null && data.getTcpSupport() == 1);

        return predicates.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> devices.stream()
                                .filter(entry.getValue())
                                .collect(Collectors.toList())
                ));
    }
}
