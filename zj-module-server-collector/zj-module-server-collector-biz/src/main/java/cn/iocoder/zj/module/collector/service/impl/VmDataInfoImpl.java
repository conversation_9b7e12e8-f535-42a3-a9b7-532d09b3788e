package cn.iocoder.zj.module.collector.service.impl;

import cn.iocoder.zj.framework.common.dal.manager.VmData;
import cn.iocoder.zj.module.collector.mapper.VmDataInfoMapper;
import cn.iocoder.zj.module.collector.service.VmDataInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class VmDataInfoImpl implements VmDataInfo {

    @Resource
    VmDataInfoMapper vmDataInfoMapper;

    @Override
    public List<VmData> selectByIdPlatformList(Long platformId) {
        return  vmDataInfoMapper.selectByIdPlatformList(platformId);
    }

    @Override
    public int add(List<VmData> added) {
        for (VmData vmData : added) {
            vmDataInfoMapper.insert(vmData);
        }
        return 0;
    }

    @Override
    public int deleted(List<String> deletList) {
        return vmDataInfoMapper.deleted(deletList);
    }

    @Override
    public int updated(List<VmData> updated) {
        return vmDataInfoMapper.updated(updated);
    }
}
