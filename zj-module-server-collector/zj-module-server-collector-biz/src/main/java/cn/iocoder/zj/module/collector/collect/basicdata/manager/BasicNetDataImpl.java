package cn.iocoder.zj.module.collector.collect.basicdata.manager;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.zj.framework.common.dal.manager.NetWorkL2Data;
import cn.iocoder.zj.framework.common.dal.manager.NetWorkL3Data;
import cn.iocoder.zj.framework.common.enums.MetricsType;
import cn.iocoder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.iocoder.zj.module.collector.service.NetWorkL2DataInfo;
import cn.iocoder.zj.module.collector.service.NetWorkL3DataInfo;
import cn.iocoder.zj.module.collector.util.DataSetAnalyzerUtils;
import cn.iocoder.zj.module.collector.util.OptimizedDataConverter;
import cn.iocoder.zj.module.collector.util.SpringBeanUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_NET;

public class BasicNetDataImpl extends AbstractBasicData {
    @Override
    public void preCheck() {

    }

    @Override
    public void collectBasicData(List<?> results, String type) {
        OptimizedDataConverter converter = SpringBeanUtils.getBean(OptimizedDataConverter.class);

        if (MetricsType.BASIC_NET_L2.code().equals(type)) {
            //获取网络信息
            List<NetWorkL2Data> newDataList = converter.convertList(results,NetWorkL2Data.class);
            NetWorkL2DataInfo netWorkL2DataInfo = SpringBeanUtils.getBean(NetWorkL2DataInfo.class);
            List<NetWorkL2Data> oldDataList = netWorkL2DataInfo.selectByIdPlatformList(newDataList.get(0).getPlatformId());
            Map<String, List<NetWorkL2Data>> result = DataSetAnalyzerUtils.compareCollectionsByKey(oldDataList, newDataList, NetWorkL2Data::getUuid);
            List<NetWorkL2Data> added = result.get("added");
            if (CollectionUtil.isNotEmpty(added)) {
                netWorkL2DataInfo.add(added);
            }
            List<NetWorkL2Data> deleted = result.get("deleted");
            if (CollectionUtil.isNotEmpty(deleted)) {
                List<String> deletList = deleted.stream().map(NetWorkL2Data::getUuid).collect(Collectors.toList());
                netWorkL2DataInfo.deleted(deletList);
            }
            List<NetWorkL2Data> updated = result.get("updated");
            if (CollectionUtil.isNotEmpty(updated)) {
                netWorkL2DataInfo.updated(updated);
            }
        }
        if (MetricsType.BASIC_NET_L3.code().equals(type)) {
            //获取网络信息
            List<NetWorkL3Data> newDataList = converter.convertList(results,NetWorkL3Data.class);
            NetWorkL3DataInfo netWorkL3DataInfo = SpringBeanUtils.getBean(NetWorkL3DataInfo.class);
            List<NetWorkL3Data> oldDataList = netWorkL3DataInfo.selectByIdPlatformList(newDataList.get(0).getPlatformId());
            Map<String, List<NetWorkL3Data>> result = DataSetAnalyzerUtils.compareCollectionsByKey(oldDataList, newDataList, NetWorkL3Data::getUuid);
            List<NetWorkL3Data> added = result.get("added");
            if (CollectionUtil.isNotEmpty(added)) {
                netWorkL3DataInfo.add(added);
            }
            List<NetWorkL3Data> deleted = result.get("deleted");
            if (CollectionUtil.isNotEmpty(deleted)) {
                List<String> deletList = deleted.stream().map(NetWorkL3Data::getUuid).collect(Collectors.toList());
                netWorkL3DataInfo.deleted(deletList);
            }
            List<NetWorkL3Data> updated = result.get("updated");
            if (CollectionUtil.isNotEmpty(updated)) {
                netWorkL3DataInfo.updated(updated);
            }
        }
    }

    @Override
    public String supportProtocol() {
        return BASIC_NET.code();
    }
}
