package cn.iocoder.zj.module.collector.collect.strategy;


import cn.iocoder.zj.module.collector.collect.basicdata.AbstractBasicData;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

import java.util.ServiceLoader;
import java.util.concurrent.ConcurrentHashMap;

@Configuration
@Order(value = Ordered.HIGHEST_PRECEDENCE + 1)
public class BasicStrategyFactory implements CommandLineRunner {
    /**
     * strategy container
     */
    private static final ConcurrentHashMap<String, AbstractBasicData> COLLECT_STRATEGY = new ConcurrentHashMap<>();

    /**
     * get instance of this protocol collection
     *
     * @param protocol collect protocol
     * @return implement of Metrics Collection
     */
    public static AbstractBasicData invoke(String protocol) {
        return COLLECT_STRATEGY.get(protocol);
    }

    @Override
    public void run(String... args) {
        // spi load and registry protocol and collect instance
        ServiceLoader<AbstractBasicData> loader = ServiceLoader.load(AbstractBasicData.class, AbstractBasicData.class.getClassLoader());
        for (AbstractBasicData collect : loader) {
            COLLECT_STRATEGY.put(collect.supportProtocol(), collect);
        }
    }
}
