/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.zj.module.collector.framework.netty.handler.processor;

import cn.iocoder.zj.framework.common.message.ClusterMsg;
import cn.iocoder.zj.module.collector.constants.BusinessConstants;
import cn.iocoder.zj.module.collector.dal.platform.Platform;
import cn.iocoder.zj.module.collector.framework.netty.auth.AESUtils;
import cn.iocoder.zj.module.collector.framework.netty.server.ChannelCtxMap;
import cn.iocoder.zj.module.collector.framework.netty.task.TaskHelper;
import cn.iocoder.zj.module.collector.service.PlatformInfo;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.reflect.TypeToken;
import io.netty.channel.ChannelHandlerContext;
import io.netty.util.AttributeKey;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;

/**
 * 认证消息处理器
 * 
 * <AUTHOR>
 */
@Slf4j
public class AuthProcessor implements MessageProcessor {

    private final PlatformInfo platformInfo;
    private final TaskHelper taskHelper;
    
    private static final AttributeKey<String> CLIENT_ID = AttributeKey.valueOf("clientId");
    private static final AttributeKey<String> CHANNEL_ID = AttributeKey.valueOf("channelId");
    private static final AttributeKey<String> IP_LIST = AttributeKey.valueOf("ipList");

    public AuthProcessor(PlatformInfo platformInfo, TaskHelper taskHelper) {
        this.platformInfo = platformInfo;
        this.taskHelper = taskHelper;
    }

    @Override
    public ClusterMsg.Message process(ChannelHandlerContext ctx, ClusterMsg.Message message) {
        log.info("[认证消息处理] 客户端: {}, 开始处理认证消息", message.getClientId());
        
        try {
            String jsonData = message.getData();
            JsonObject jsonObject = JsonParser.parseString(jsonData).getAsJsonObject();
            String clientSecretKey = jsonObject.get("clientSecretKey").getAsString();
            
            Gson gson = new Gson();
            Type mapType = new TypeToken<Map<String, String>>() {}.getType();
            Map<String, String> serverInfoMap = gson.fromJson(jsonObject.getAsJsonObject("serverInfoMap"), mapType);
            List<String> valuesList = List.copyOf(serverInfoMap.values());
            String ipList = gson.toJson(valuesList);
            
            String decrypt = AESUtils.decrypt(clientSecretKey);
            
            if (BusinessConstants.COLLECTOR_AUTH.equals(decrypt)) {
                // 认证成功
                ctx.channel().attr(CLIENT_ID).set(message.getClientId());
                ctx.channel().attr(CHANNEL_ID).set(ctx.channel().id().asLongText());
                ctx.channel().attr(IP_LIST).set(ipList);
                
                // 发送认证成功响应
                ClusterMsg.Message authResponse = ClusterMsg.Message.newBuilder()
                        .setClientId(message.getClientId())
                        .setType(ClusterMsg.MessageType.AUTH)
                        .setData(new Gson().toJson("auth......"))
                        .build();
                ctx.writeAndFlush(authResponse);
                
                // 发送平台信息
                List<Platform> platformInfos = platformInfo.getPlatformByTenantName(message.getClientId());
                platformInfos.forEach(platform -> platform.setClientId(message.getClientId()));
                
                ClusterMsg.Message platformInfoMessage = ClusterMsg.Message.newBuilder()
                        .setClientId(message.getClientId())
                        .setType(ClusterMsg.MessageType.INFO)
                        .setData(new Gson().toJson(platformInfos))
                        .build();
                
                ChannelCtxMap.addChannelCtx(ctx, message.getClientId());
                ctx.writeAndFlush(platformInfoMessage);
                taskHelper.yqTask(message.getClientId());
                
                log.info("[认证成功] 客户端: {}, 认证成功并发送平台信息", message.getClientId());
                
            } else {
                // 认证失败
                ClusterMsg.Message authFailResponse = ClusterMsg.Message.newBuilder()
                        .setClientId(message.getClientId())
                        .setType(ClusterMsg.MessageType.AUTH)
                        .setData("Authentication failed . . .")
                        .build();
                ctx.writeAndFlush(authFailResponse);
                
                log.warn("[认证失败] 客户端: {}, 认证密钥不匹配", message.getClientId());
            }
            
        } catch (Exception e) {
            log.error("[认证异常] 客户端: {}, 认证处理失败: {}", message.getClientId(), e.getMessage(), e);
        }
        
        return null;
    }

    @Override
    public ClusterMsg.MessageType getMessageType() {
        return ClusterMsg.MessageType.AUTH;
    }
} 