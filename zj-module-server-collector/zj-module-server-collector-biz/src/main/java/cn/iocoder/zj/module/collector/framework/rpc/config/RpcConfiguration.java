package cn.iocoder.zj.module.collector.framework.rpc.config;


import cn.iocoder.cloud.module.cloudedge.api.monitor.MonitorApi;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.monitor.api.hardware.HardWareInfoApi;
import cn.iocoder.zj.module.system.api.region.RegionApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {RegionApi.class,HardWareInfoApi.class,AlarmConfigApi.class, MonitorApi.class})
public class RpcConfiguration {
}
