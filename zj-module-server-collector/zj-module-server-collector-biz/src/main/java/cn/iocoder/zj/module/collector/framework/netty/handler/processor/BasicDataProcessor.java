/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.zj.module.collector.framework.netty.handler.processor;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import cn.iocoder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.iocoder.zj.module.collector.collect.strategy.BasicStrategyFactory;
import com.google.gson.Gson;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 基础数据处理器
 * 
 * <AUTHOR>
 */
@Slf4j
public class BasicDataProcessor implements MessageProcessor {

    @Override
    public ClusterMsg.Message process(ChannelHandlerContext ctx, ClusterMsg.Message message) {
        log.info("[基础数据处理] 客户端: {}, 开始处理基础数据", message.getClientId());
        
        try {
            BasicCollectData basicCollectData = new Gson().fromJson(message.getData(), BasicCollectData.class);
            if (basicCollectData == null) {
                log.error("[数据解析失败] 客户端: {}, 原始数据: {}", message.getClientId(), message.getData());
                return null;
            }
            
            // 提取平台名称用于日志记录
            String platformName = "未知平台";
            if (CollUtil.isNotEmpty(basicCollectData.getBasicDataMap())) {
                Object firstData = basicCollectData.getBasicDataMap().get(0);
                if (firstData instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> dataMap = (Map<String, Object>) firstData;
                    platformName = String.valueOf(dataMap.getOrDefault("platformName", "未知平台"));
                }
            }
            
            log.info("[基础数据采集] 客户端: {}, 平台: {}, 指标名称: {}, 数据量: {}",
                    message.getClientId(),
                    platformName,
                    basicCollectData.getMetricsName(),
                    CollUtil.isEmpty(basicCollectData.getBasicDataMap()) ? 0 : basicCollectData.getBasicDataMap().size());
            
            // 处理基础数据
            if (CollUtil.isNotEmpty(basicCollectData.getBasicDataMap())) {
                String key = basicCollectData.getMetricsName();
                List<?> results = basicCollectData.getBasicDataMap();
                
                log.debug("[数据处理开始] 客户端: {}, 指标: {}, 处理数量: {}", 
                        message.getClientId(), key, results.size());
                
                AbstractBasicData abstractCollect = BasicStrategyFactory.invoke(key);
                abstractCollect.collectBasicData(results, basicCollectData.getMetricsType());
                
                log.info("[数据处理完成] 客户端: {}, 指标: {}", message.getClientId(), key);
            }
            
        } catch (Exception e) {
            log.error("[基础数据异常] 客户端: {}, 处理失败: {}", message.getClientId(), e.getMessage(), e);
        }
        
        return null;
    }

    @Override
    public ClusterMsg.MessageType getMessageType() {
        return ClusterMsg.MessageType.BASIC;
    }
} 