package cn.iocoder.zj.module.collector.service;

import cn.iocoder.zj.framework.common.dal.manager.VolumeSnapshotData;

import java.util.List;

/**
 * <AUTHOR>
 **/
public interface VolumeSnapshotInfo {

    List<VolumeSnapshotData> selectByIdPlatformList(Long platformId);

    void add(List<VolumeSnapshotData> added);

    void deleted(List<String> deletList);

    void updated(List<VolumeSnapshotData> updated);
}
