package cn.iocoder.zj.module.collector.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 数据转换性能监控工具
 * 用于监控和统计数据转换的性能指标
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Component
public class DataConversionMetrics {
    
    /**
     * 转换次数统计
     */
    private final ConcurrentHashMap<String, LongAdder> conversionCounts = new ConcurrentHashMap<>();
    
    /**
     * 转换总耗时统计（毫秒）
     */
    private final ConcurrentHashMap<String, LongAdder> conversionTotalTime = new ConcurrentHashMap<>();
    
    /**
     * 转换数据量统计
     */
    private final ConcurrentHashMap<String, LongAdder> conversionDataCount = new ConcurrentHashMap<>();
    
    /**
     * 最大转换耗时
     */
    private final ConcurrentHashMap<String, AtomicLong> maxConversionTime = new ConcurrentHashMap<>();
    
    /**
     * 最小转换耗时
     */
    private final ConcurrentHashMap<String, AtomicLong> minConversionTime = new ConcurrentHashMap<>();
    
    /**
     * 记录单次转换性能
     * 
     * @param targetClass 目标类型
     * @param dataCount 数据量
     * @param durationMs 耗时（毫秒）
     */
    public void recordConversion(Class<?> targetClass, int dataCount, long durationMs) {
        String className = targetClass.getSimpleName();
        
        // 更新统计数据
        conversionCounts.computeIfAbsent(className, k -> new LongAdder()).increment();
        conversionTotalTime.computeIfAbsent(className, k -> new LongAdder()).add(durationMs);
        conversionDataCount.computeIfAbsent(className, k -> new LongAdder()).add(dataCount);
        
        // 更新最大最小耗时
        maxConversionTime.computeIfAbsent(className, k -> new AtomicLong(0))
                .updateAndGet(current -> Math.max(current, durationMs));
        
        minConversionTime.computeIfAbsent(className, k -> new AtomicLong(Long.MAX_VALUE))
                .updateAndGet(current -> Math.min(current, durationMs));
        
        // 记录慢转换警告
        if (durationMs > 100) {
            log.warn("[慢转换警告] 类型: {}, 数据量: {}, 耗时: {}ms", className, dataCount, durationMs);
        }
    }
    
    /**
     * 获取指定类型的转换统计信息
     * 
     * @param targetClass 目标类型
     * @return 统计信息
     */
    public ConversionStats getStats(Class<?> targetClass) {
        String className = targetClass.getSimpleName();
        
        long count = conversionCounts.getOrDefault(className, new LongAdder()).sum();
        long totalTime = conversionTotalTime.getOrDefault(className, new LongAdder()).sum();
        long totalData = conversionDataCount.getOrDefault(className, new LongAdder()).sum();
        long maxTime = maxConversionTime.getOrDefault(className, new AtomicLong(0)).get();
        long minTime = minConversionTime.getOrDefault(className, new AtomicLong(0)).get();
        
        if (minTime == Long.MAX_VALUE) {
            minTime = 0;
        }
        
        return new ConversionStats(className, count, totalTime, totalData, maxTime, minTime);
    }
    
    /**
     * 获取所有类型的转换统计信息
     * 
     * @return 所有统计信息
     */
    public void printAllStats() {
        log.info("=== 数据转换性能统计报告 ===");
        
        for (String className : conversionCounts.keySet()) {
            long count = conversionCounts.get(className).sum();
            long totalTime = conversionTotalTime.get(className).sum();
            long totalData = conversionDataCount.get(className).sum();
            long maxTime = maxConversionTime.get(className).get();
            long minTime = minConversionTime.get(className).get();
            
            if (minTime == Long.MAX_VALUE) {
                minTime = 0;
            }
            
            double avgTime = count > 0 ? (double) totalTime / count : 0;
            double avgDataPerConversion = count > 0 ? (double) totalData / count : 0;
            double itemsPerSecond = totalTime > 0 ? totalData * 1000.0 / totalTime : 0;
            
            log.info("类型: {}", className);
            log.info("  转换次数: {}", count);
            log.info("  总数据量: {}", totalData);
            log.info("  总耗时: {}ms", totalTime);
            log.info("  平均耗时: {:.2f}ms", avgTime);
            log.info("  最大耗时: {}ms", maxTime);
            log.info("  最小耗时: {}ms", minTime);
            log.info("  平均数据量/次: {:.1f}", avgDataPerConversion);
            log.info("  处理速度: {:.0f} items/s", itemsPerSecond);
            log.info("  ---");
        }
    }
    
    /**
     * 重置所有统计数据
     */
    public void reset() {
        conversionCounts.clear();
        conversionTotalTime.clear();
        conversionDataCount.clear();
        maxConversionTime.clear();
        minConversionTime.clear();
        log.info("[性能监控] 统计数据已重置");
    }
    
    /**
     * 重置指定类型的统计数据
     * 
     * @param targetClass 目标类型
     */
    public void reset(Class<?> targetClass) {
        String className = targetClass.getSimpleName();
        conversionCounts.remove(className);
        conversionTotalTime.remove(className);
        conversionDataCount.remove(className);
        maxConversionTime.remove(className);
        minConversionTime.remove(className);
        log.info("[性能监控] 类型 {} 的统计数据已重置", className);
    }
    
    /**
     * 转换统计信息数据类
     */
    public static class ConversionStats {
        private final String className;
        private final long conversionCount;
        private final long totalTimeMs;
        private final long totalDataCount;
        private final long maxTimeMs;
        private final long minTimeMs;
        
        public ConversionStats(String className, long conversionCount, long totalTimeMs, 
                             long totalDataCount, long maxTimeMs, long minTimeMs) {
            this.className = className;
            this.conversionCount = conversionCount;
            this.totalTimeMs = totalTimeMs;
            this.totalDataCount = totalDataCount;
            this.maxTimeMs = maxTimeMs;
            this.minTimeMs = minTimeMs;
        }
        
        public String getClassName() {
            return className;
        }
        
        public long getConversionCount() {
            return conversionCount;
        }
        
        public long getTotalTimeMs() {
            return totalTimeMs;
        }
        
        public long getTotalDataCount() {
            return totalDataCount;
        }
        
        public long getMaxTimeMs() {
            return maxTimeMs;
        }
        
        public long getMinTimeMs() {
            return minTimeMs;
        }
        
        public double getAverageTimeMs() {
            return conversionCount > 0 ? (double) totalTimeMs / conversionCount : 0;
        }
        
        public double getAverageDataPerConversion() {
            return conversionCount > 0 ? (double) totalDataCount / conversionCount : 0;
        }
        
        public double getItemsPerSecond() {
            return totalTimeMs > 0 ? totalDataCount * 1000.0 / totalTimeMs : 0;
        }
        
        @Override
        public String toString() {
            return String.format(
                "ConversionStats{className='%s', count=%d, totalTime=%dms, " +
                "totalData=%d, avgTime=%.2fms, maxTime=%dms, minTime=%dms, speed=%.0f items/s}",
                className, conversionCount, totalTimeMs, totalDataCount, 
                getAverageTimeMs(), maxTimeMs, minTimeMs, getItemsPerSecond()
            );
        }
    }
}
