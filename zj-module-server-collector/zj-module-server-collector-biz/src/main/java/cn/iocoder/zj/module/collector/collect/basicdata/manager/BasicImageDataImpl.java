package cn.iocoder.zj.module.collector.collect.basicdata.manager;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.zj.framework.common.dal.manager.ImageData;
import cn.iocoder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.iocoder.zj.module.collector.service.ImageDataInfo;
import cn.iocoder.zj.module.collector.util.DataSetAnalyzerUtils;
import cn.iocoder.zj.module.collector.util.OptimizedDataConverter;
import cn.iocoder.zj.module.collector.util.SpringBeanUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_IMAGE;

public class BasicImageDataImpl extends AbstractBasicData {

    @Override
    public void preCheck() {

    }

    @Override
    public void collectBasicData(List<?> results,String type) {
        OptimizedDataConverter converter = SpringBeanUtils.getBean(OptimizedDataConverter.class);

        //获取镜像信息
        long startTime = System.currentTimeMillis();
        List<ImageData> newDataList = converter.convertList(results,ImageData.class);
        long endTime = System.currentTimeMillis();
        //数据库处理
        ImageDataInfo imageDataInfo = SpringBeanUtils.getBean(ImageDataInfo.class);
        List<ImageData> oldDataList = imageDataInfo.selectByIdPlatformList(newDataList.get(0).getPlatformId());
        Map<String, List<ImageData>> result = DataSetAnalyzerUtils.compareCollectionsByKey(oldDataList, newDataList, ImageData::getUuid);
        List<ImageData> added = result.get("added");
        if (CollectionUtil.isNotEmpty(added)) {
            imageDataInfo.add(added);
        }
        List<ImageData> deleted = result.get("deleted");
        if (CollectionUtil.isNotEmpty(deleted)) {
            List<String> deletList = deleted.stream().map(ImageData::getUuid).collect(Collectors.toList());
            imageDataInfo.deleted(deletList);
        }
        List<ImageData> updated = result.get("updated");
        if (CollectionUtil.isNotEmpty(updated)) {
            imageDataInfo.updated(updated);
        }
    }

    @Override
    public String supportProtocol() {
        return BASIC_IMAGE.code();
    }
}
