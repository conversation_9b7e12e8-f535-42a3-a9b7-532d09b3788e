package cn.iocoder.zj.module.collector.config;

import cn.iocoder.zj.module.collector.monitor.AsyncProcessingMetrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * 异步处理配置类
 * 配置异步处理相关的监控和定时任务
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Configuration
@EnableScheduling
public class AsyncProcessingConfig {
    
    @Autowired
    private AsyncProcessingMetrics processingMetrics;
    
    @Autowired
    private WorkerPool workerPool;
    
    /**
     * 定时输出异步处理性能统计报告
     * 每5分钟输出一次统计信息
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    @ConditionalOnProperty(name = "collector.async-processing.metrics.enabled", havingValue = "true", matchIfMissing = true)
    public void printPerformanceReport() {
        try {
            log.info("=== 定时性能报告 ===");
            
            // 输出线程池状态
            log.info("[线程池状态] {}", workerPool.getPoolStatus());
            log.info("[线程池健康] {}", workerPool.isHealthy() ? "健康" : "异常");
            
            // 输出异步处理统计
            processingMetrics.printAllStats();
            
        } catch (Exception e) {
            log.error("[性能监控] 输出统计报告失败", e);
        }
    }
    
    /**
     * 定时检查线程池健康状态
     * 每1分钟检查一次
     */
    @Scheduled(fixedRate = 60000) // 1分钟
    @ConditionalOnProperty(name = "collector.async-processing.health-check.enabled", havingValue = "true", matchIfMissing = true)
    public void checkThreadPoolHealth() {
        try {
            if (!workerPool.isHealthy()) {
                log.warn("[健康检查] 线程池状态异常: {}", workerPool.getPoolStatus());
            }
        } catch (Exception e) {
            log.error("[健康检查] 检查线程池健康状态失败", e);
        }
    }
}
