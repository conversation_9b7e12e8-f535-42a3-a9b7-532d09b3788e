package cn.iocoder.zj.module.collector.service.impl;

import cn.iocoder.zj.framework.common.dal.manager.VmNicData;
import cn.iocoder.zj.module.collector.mapper.VmNicDataMapper;
import cn.iocoder.zj.module.collector.service.VmNicDataInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class VmNicDataInfoImpl implements VmNicDataInfo {

    @Resource
    VmNicDataMapper vmNicDataMapper;

    @Override
    public List<VmNicData> selectByIdPlatformList(Long platformId) {
        return vmNicDataMapper.selectList(new QueryWrapper<VmNicData>().eq("platform_id", platformId));
    }

    @Override
    public void add(List<VmNicData> added) {
        for (VmNicData vmNicData : added) {
            vmNicDataMapper.insert(vmNicData);
        }
    }

    @Override
    public void deleted(List<String> deletList) {
        for (String id : deletList) {
            vmNicDataMapper.delete(new QueryWrapper<VmNicData>().eq("uuid", id));
        }
    }

    @Override
    public void updated(List<VmNicData> updated) {
        for (VmNicData vmNicData : updated) {
            vmNicData.setUpdateTime(new Date());
            vmNicDataMapper.update(vmNicData, new QueryWrapper<VmNicData>().eq("uuid", vmNicData.getUuid()));
        }
    }
}
