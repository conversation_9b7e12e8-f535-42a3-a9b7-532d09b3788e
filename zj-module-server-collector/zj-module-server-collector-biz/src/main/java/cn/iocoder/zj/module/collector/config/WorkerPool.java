package cn.iocoder.zj.module.collector.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.stereotype.Component;

import java.util.concurrent.*;

@Component
@Slf4j
public class WorkerPool implements DisposableBean {

    private ThreadPoolExecutor workerExecutor;

    public WorkerPool() {
        initWorkExecutor();
    }

    private void initWorkExecutor() {
        // thread factory
        ThreadFactory threadFactory = new ThreadFactoryBuilder()
                .setUncaughtExceptionHandler((thread, throwable) -> {
                    log.error("[Important] WorkerPool workerExecutor has uncaughtException.", throwable);
                    log.error("Thread Name {} : {}", thread.getName(), throwable.getMessage(), throwable);
                })
                .setDaemon(true)
                .setNameFormat("collect-worker-%d")
                .build();

        // 优化线程池配置
        int coreSize = Math.max(4, Runtime.getRuntime().availableProcessors());
        int maxSize = Runtime.getRuntime().availableProcessors() * 4; // 减少最大线程数，避免过度上下文切换

        // 使用有界队列替代 SynchronousQueue，提供缓冲能力
        BlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>(2000);

        // 使用 CallerRunsPolicy 替代 AbortPolicy，避免任务丢失
        RejectedExecutionHandler rejectedHandler = new ThreadPoolExecutor.CallerRunsPolicy();

        workerExecutor = new ThreadPoolExecutor(
                coreSize,
                maxSize,
                60L, // 增加空闲线程存活时间
                TimeUnit.SECONDS,
                workQueue,
                threadFactory,
                rejectedHandler
        );

        log.info("[线程池初始化] 核心线程数:{}, 最大线程数:{}, 队列容量:{}",
                coreSize, maxSize, workQueue.remainingCapacity());
    }

    /**
     * Run the collection task thread
     *
     * @param runnable Task
     * @throws RejectedExecutionException when thread pool full
     */
    public void executeJob(Runnable runnable) throws RejectedExecutionException {
        // 检查线程池状态
        if (workerExecutor.isShutdown()) {
            throw new RejectedExecutionException("线程池已关闭");
        }

        // 记录队列状态（仅在队列较满时记录）
        int queueSize = workerExecutor.getQueue().size();
        if (queueSize > 5000) {
            log.warn("[线程池状态] 队列积压较多, 当前队列大小:{}, 活跃线程数:{}, 总线程数:{}",
                    queueSize, workerExecutor.getActiveCount(), workerExecutor.getPoolSize());
        }

        workerExecutor.execute(runnable);
    }

    /**
     * 获取线程池状态信息
     */
    public String getPoolStatus() {
        if (workerExecutor == null) {
            return "线程池未初始化";
        }

        return String.format(
                "线程池状态 - 核心线程数:%d, 最大线程数:%d, 当前线程数:%d, 活跃线程数:%d, " +
                "队列大小:%d, 已完成任务数:%d, 总提交任务数:%d",
                workerExecutor.getCorePoolSize(),
                workerExecutor.getMaximumPoolSize(),
                workerExecutor.getPoolSize(),
                workerExecutor.getActiveCount(),
                workerExecutor.getQueue().size(),
                workerExecutor.getCompletedTaskCount(),
                workerExecutor.getTaskCount()
        );
    }

    /**
     * 检查线程池是否健康
     */
    public boolean isHealthy() {
        if (workerExecutor == null || workerExecutor.isShutdown()) {
            return false;
        }

        // 检查队列是否过满
        int queueSize = workerExecutor.getQueue().size();
        int queueCapacity = 5000; // 与初始化时的容量保持一致

        return queueSize < queueCapacity * 0.8; // 队列使用率低于80%认为健康
    }

    @Override
    public void destroy() throws Exception {
        if (workerExecutor != null) {
            workerExecutor.shutdownNow();
        }
    }
}
