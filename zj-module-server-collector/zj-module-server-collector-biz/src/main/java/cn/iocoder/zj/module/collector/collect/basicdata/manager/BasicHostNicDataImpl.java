package cn.iocoder.zj.module.collector.collect.basicdata.manager;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.zj.framework.common.dal.manager.HostNicData;
import cn.iocoder.zj.framework.common.enums.MetricsType;
import cn.iocoder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.iocoder.zj.module.collector.service.HostNicDataInfo;
import cn.iocoder.zj.module.collector.util.DataSetAnalyzerUtils;
import cn.iocoder.zj.module.collector.util.OptimizedDataConverter;
import cn.iocoder.zj.module.collector.util.SpringBeanUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_HOST_VIC;

@Slf4j
public class BasicHostNicDataImpl extends AbstractBasicData {

    @Override
    public void preCheck() {

    }

    @Override
    public void collectBasicData(List<?> results,String type) {
        OptimizedDataConverter converter = SpringBeanUtils.getBean(OptimizedDataConverter.class);
        List<HostNicData> dataList = converter.convertList(results, HostNicData.class);
        HostNicDataInfo bean = SpringBeanUtils.getBean(HostNicDataInfo.class);
        List<HostNicData> oldVmData = bean.selectByIdPlatformList(dataList.get(0).getPlatformId());
        Map<String, List<HostNicData>> result;
        if (MetricsType.BASIC_HOST_VIC_FUSIONONE.code().equals(type))
            result = DataSetAnalyzerUtils.compareCollectionsByMultiKeys(oldVmData, dataList, HostNicData::getUuid, HostNicData::getHardwareUuid);
        else
            result = DataSetAnalyzerUtils.compareCollectionsByMultiKeys(oldVmData, dataList, HostNicData::getUuid, HostNicData::getHardwareUuid,HostNicData::getL2NetworkUuid);
        List<HostNicData> added = result.get("added");
        if (CollectionUtil.isNotEmpty(added)) {
            bean.add(added);
        }
        List<HostNicData> deleted = result.get("deleted");
        if (CollectionUtil.isNotEmpty(deleted)) {
            List<String> deletList = deleted.stream().map(HostNicData::getUuid).collect(Collectors.toList());
            bean.deleted(deletList);
        }
        List<HostNicData> updated = result.get("updated");
        if (CollectionUtil.isNotEmpty(updated)) {
            bean.updated(updated);
        }
    }

    @Override
    public String supportProtocol() {
        return BASIC_HOST_VIC.code();
    }
}
