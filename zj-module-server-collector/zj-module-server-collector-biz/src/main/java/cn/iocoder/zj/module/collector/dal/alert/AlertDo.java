package cn.iocoder.zj.module.collector.dal.alert;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;


@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class AlertDo {


    private Long id;


    private Long alertDefineId;


    private String content;


    private String creator;


    private Long firstAlarmTime;


    private Date gmtCreate;


    private Date gmtUpdate;


    private Long lastAlarmTime;


    private String modifier;


    private Integer priority;


    private Integer status;


    private String tags;


    private String target;

    private Integer times;


    private String platformName;


    private Long platformId;


    private Integer isSolved;


    private String monitorId;


    private Integer resourceType;


    private String app;


    private Long alarmId;


    private String monitorName;
    /**
     * 以下为冗余字段
     */

    private Double value;


    private Long alarmTime;


    private String alarmName;

    private String alarmConfigName;

    private String alarmRule;
}
