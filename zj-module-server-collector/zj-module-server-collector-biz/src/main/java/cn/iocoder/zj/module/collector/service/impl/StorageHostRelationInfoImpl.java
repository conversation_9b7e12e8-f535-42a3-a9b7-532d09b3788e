package cn.iocoder.zj.module.collector.service.impl;

import cn.iocoder.zj.framework.common.dal.manager.StorageHostRelationData;
import cn.iocoder.zj.module.collector.mapper.StorageHostRelationInfoMapper;
import cn.iocoder.zj.module.collector.service.StorageHostRelationInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


@Service
public class StorageHostRelationInfoImpl implements StorageHostRelationInfo {
    @Resource
    StorageHostRelationInfoMapper storageHostRelationInfoMapper;

    @Override
    public List<StorageHostRelationData> selectByIdPlatformList(Long platformId) {
        return storageHostRelationInfoMapper.selectList(new QueryWrapper<StorageHostRelationData>().eq("platform_id", platformId).eq("deleted", 0));
    }

    @Override
    public void add(List<StorageHostRelationData> added) {
        for (StorageHostRelationData storageHostRelationData : added) {
            storageHostRelationInfoMapper.insert(storageHostRelationData);
        }
    }

    @Override
    public void deleted(List<Long> deletList) {
        for (Long id : deletList) {
            storageHostRelationInfoMapper.delete(new QueryWrapper<StorageHostRelationData>().eq("id", id));
        }
    }

    @Override
    public void updated(List<StorageHostRelationData> updated) {
        for (StorageHostRelationData storageHostRelationData : updated) {
            storageHostRelationData.setUpdateTime(new Date());
            storageHostRelationInfoMapper.update(storageHostRelationData, new QueryWrapper<StorageHostRelationData>().eq("id", storageHostRelationData.getId()));
        }

    }
}
