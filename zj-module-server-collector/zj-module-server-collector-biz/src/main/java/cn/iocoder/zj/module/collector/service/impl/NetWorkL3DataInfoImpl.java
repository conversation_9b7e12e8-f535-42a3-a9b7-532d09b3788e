package cn.iocoder.zj.module.collector.service.impl;

import cn.iocoder.zj.framework.common.dal.manager.NetWorkL3Data;
import cn.iocoder.zj.module.collector.mapper.NetWorkL3DataInfoMapper;
import cn.iocoder.zj.module.collector.service.NetWorkL3DataInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class NetWorkL3DataInfoImpl implements NetWorkL3DataInfo {
    @Resource
    NetWorkL3DataInfoMapper netWorkL3DataInfoMapper;

    @Override
    public List<NetWorkL3Data> selectByIdPlatformList(Long platformId) {
        return netWorkL3DataInfoMapper.selectList(new QueryWrapper<NetWorkL3Data>().eq("platform_id", platformId));
    }

    @Override
    public void add(List<NetWorkL3Data> added) {
        for (NetWorkL3Data addedData : added) {
            netWorkL3DataInfoMapper.insert(addedData);
        }
    }

    @Override
    public void deleted(List<String> deletList) {
        for (String id : deletList) {
            netWorkL3DataInfoMapper.delete(new QueryWrapper<NetWorkL3Data>().eq("uuid", id));
        }
    }

    @Override
    public void updated(List<NetWorkL3Data> updated) {
        for (NetWorkL3Data updatedData : updated) {
            updatedData.setUpdateTime(new Date());
            netWorkL3DataInfoMapper.update(updatedData, new QueryWrapper<NetWorkL3Data>().eq("uuid", updatedData.getUuid()));
        }
    }
}
