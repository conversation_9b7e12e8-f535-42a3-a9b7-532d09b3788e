/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.zj.module.collector.framework.netty.handler.processor;

import cn.iocoder.zj.framework.common.message.ClusterMsg;
import cn.iocoder.zj.framework.warehouse.store.history.HistoryDataWriter;
import cn.iocoder.zj.module.collector.constants.BusinessConstants;
import cn.iocoder.zj.module.collector.framework.kafka.KafkaProducerService;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

/**
 * 性能数据处理器
 * 处理CPU、磁盘、内存、网络等性能指标
 * 
 * <AUTHOR>
 */
@Slf4j
public class PerformanceDataProcessor implements MessageProcessor {
    
    private final HistoryDataWriter historyDataWriter;
    private final KafkaProducerService producerService;
    
    private static final Set<ClusterMsg.MessageType> SUPPORTED_TYPES = Set.of(
            ClusterMsg.MessageType.CPU_TASK,
            ClusterMsg.MessageType.DISK_TASK,
            ClusterMsg.MessageType.MEM_TASK,
            ClusterMsg.MessageType.NET_TASK
    );

    public PerformanceDataProcessor(HistoryDataWriter historyDataWriter, KafkaProducerService producerService) {
        this.historyDataWriter = historyDataWriter;
        this.producerService = producerService;
    }

    @Override
    public ClusterMsg.Message process(ChannelHandlerContext ctx, ClusterMsg.Message message) {
        log.info("[性能数据采集] 客户端: {}, 指标类型: {}, 开始处理性能数据",
                message.getClientId(), message.getType());
        
        try {
            // 保存历史数据
            historyDataWriter.saveData(message);
            
            // 发送到Kafka
            producerService.sendMessage(BusinessConstants.PERFORMANCE_TOPIC, message.getData());

            log.info("[性能数据保存] 客户端: {}, 指标类型: {}, 数据保存成功",
                    message.getClientId(), message.getType());
            
        } catch (Exception e) {
            log.error("[性能数据异常] 客户端: {}, 指标类型: {}, 保存失败, 原因: {}",
                    message.getClientId(), message.getType(), e.getMessage(), e);
        }
        
        return null;
    }

    @Override
    public ClusterMsg.MessageType getMessageType() {
        // 这个处理器支持多种类型，但在接口定义中只能返回一个
        // 实际使用时会通过工厂类根据消息类型来选择处理器
        return ClusterMsg.MessageType.CPU_TASK;
    }
    
    /**
     * 检查是否支持指定的消息类型
     * 
     * @param messageType 消息类型
     * @return 是否支持
     */
    public boolean supports(ClusterMsg.MessageType messageType) {
        return SUPPORTED_TYPES.contains(messageType);
    }
} 