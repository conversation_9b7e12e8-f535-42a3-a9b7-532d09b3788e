package cn.iocoder.zj.module.collector.collect.basicdata.manager;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.zj.framework.common.dal.manager.VmData;
import cn.iocoder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.iocoder.zj.module.collector.service.VmDataInfo;
import cn.iocoder.zj.module.collector.util.DataSetAnalyzerUtils;
import cn.iocoder.zj.module.collector.util.OptimizedDataConverter;
import cn.iocoder.zj.module.collector.util.SpringBeanUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VM;

@Slf4j
public class BasicVmDataImpl extends AbstractBasicData {

    @Override
    public void preCheck() {

    }

    @Override
    public void collectBasicData(List<?> results,String type) {
        // 性能优化：使用优化的数据转换器替代双重序列化
        OptimizedDataConverter converter = SpringBeanUtils.getBean(OptimizedDataConverter.class);

        long startTime = System.currentTimeMillis();
        List<VmData> newVmList = converter.convertList(results, VmData.class);
        long endTime = System.currentTimeMillis();

        log.debug("[性能优化] VmData 转换完成. 数据量: {}, 耗时: {}ms",
                results.size(), endTime - startTime);
        VmDataInfo bean = SpringBeanUtils.getBean(VmDataInfo.class);
        List<VmData> oldVmData = bean.selectByIdPlatformList(newVmList.get(0).getPlatformId());
        Map<String, List<VmData>> result = DataSetAnalyzerUtils.compareCollectionsByKey(oldVmData, newVmList,VmData::getUuid,(newObj, oldObj) -> newObj.setId(oldObj.getId()) );

        List<VmData> added = result.get("added");
        if (CollectionUtil.isNotEmpty(added)) {
            bean.add(added);
        }
        List<VmData> deleted = result.get("deleted");
        if (CollectionUtil.isNotEmpty(deleted)) {
            List<String> deletList = deleted.stream().map(VmData::getUuid).collect(Collectors.toList());
            bean.deleted(deletList);
        }
        List<VmData> updated = result.get("updated");
        if (CollectionUtil.isNotEmpty(updated)) {
            bean.updated(updated);
        }

    }

    @Override
    public String supportProtocol() {
        return BASIC_VM.code();
    }
}
