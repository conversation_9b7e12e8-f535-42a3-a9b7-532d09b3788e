package cn.iocoder.zj.module.collector.controller.admin;


import cn.hutool.json.JSONUtil;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.warehouse.service.ZjMetricsDataService;
import cn.iocoder.zj.module.collector.framework.netty.monitor.ConnectionMonitor;
import cn.iocoder.zj.module.collector.framework.netty.server.ChannelCtxMap;
import cn.iocoder.zj.module.collector.service.CollectorService;
import io.netty.channel.ChannelHandlerContext;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

@RestController
@RequestMapping("/server-collector/client")
@Slf4j
@Tag(name = "客户端管理")
public class ClientController {

    @Resource
    private CollectorService collectorService;


    @Resource
    ZjMetricsDataService metricsDataService;

    @Resource
    private ConnectionMonitor connectionMonitor;

    @GetMapping("/online")
    @PermitAll
    public void online(String clientId){
        collectorService.online(clientId);
    }


    @GetMapping("/offline")
    @PermitAll
    public void offline(String clientId){
        ChannelCtxMap.offline(clientId);
        collectorService.offline(clientId);
    }

    @GetMapping("/getInfo")
    @PermitAll
    public String getInfo(){
     return JSONUtil.toJsonStr(metricsDataService.getMetricHistoryData("713320673550","mem_task","MemoryUsedBytes","15",false,1745164800000L ,1745251200000L, "1d", "type", ""));

    }

    /**
     * 诊断客户端通道映射状态
     * 新增：用于排查客户端消息混乱问题
     */
    @GetMapping("/diagnostic")
    @Operation(summary = "诊断客户端通道映射状态")
    public CommonResult<Map<String, Object>> diagnostic() {
        Map<String, Object> result = new HashMap<>();
        
        // 获取所有客户端的通道映射
        Map<String, List<ChannelHandlerContext>> channelGroups = ChannelCtxMap.groupChannelsByClientId();
        Map<String, Integer> clientChannelCount = new HashMap<>();
        Map<String, List<String>> clientChannelIds = new HashMap<>();
        
        for (Map.Entry<String, List<ChannelHandlerContext>> entry : channelGroups.entrySet()) {
            String clientId = entry.getKey();
            List<ChannelHandlerContext> contexts = entry.getValue();
            
            clientChannelCount.put(clientId, contexts.size());
            clientChannelIds.put(clientId, contexts.stream()
                    .map(ctx -> ctx.channel().id().asShortText())
                    .collect(Collectors.toList()));
        }
        
        result.put("clientChannelCount", clientChannelCount);
        result.put("clientChannelIds", clientChannelIds);
        result.put("totalClients", channelGroups.size());
        result.put("totalChannels", channelGroups.values().stream().mapToInt(List::size).sum());
        result.put("timestamp", System.currentTimeMillis());
        
        return success(result);
    }

    /**
     * 验证客户端平台信息
     * 新增：验证特定客户端能访问的平台信息
     */
    @GetMapping("/verify-platform/{clientId}")
    @Operation(summary = "验证客户端平台信息")
    public CommonResult<Map<String, Object>> verifyPlatform(@PathVariable String clientId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取客户端的通道信息
            List<ChannelHandlerContext> contexts = ChannelCtxMap.getChannelCtxByClientId(clientId);
            
            // 获取客户端的平台信息（需要注入PlatformInfo服务）
            // 这里需要添加依赖注入
            result.put("clientId", clientId);
            result.put("channelCount", contexts.size());
            result.put("activeChannels", contexts.stream()
                    .map(ctx -> ctx.channel().id().asShortText())
                    .collect(Collectors.toList()));
            
            // 添加更多诊断信息
            result.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            result.put("error", e.getMessage());
        }
        
        return success(result);
    }

    /**
     * 获取连接统计信息
     * 新增：用于监控连接状态和排查问题
     */
    @GetMapping("/connection-stats")
    @Operation(summary = "获取连接统计信息")
    public CommonResult<Map<String, Object>> getConnectionStats() {
        Map<String, Object> result = new HashMap<>();

        try {
            ConnectionMonitor.ConnectionStats stats = connectionMonitor.getConnectionStats();
            result.put("totalConnections", stats.getTotalConnections());
            result.put("authenticatedConnections", stats.getAuthenticatedConnections());
            result.put("unauthenticatedConnections", stats.getUnauthenticatedConnections());
            result.put("timestamp", System.currentTimeMillis());

            // 添加健康状态评估
            if (stats.getUnauthenticatedConnections() > 0) {
                result.put("healthStatus", "WARNING");
                result.put("healthMessage", "存在未认证连接，可能存在连接问题");
            } else {
                result.put("healthStatus", "HEALTHY");
                result.put("healthMessage", "所有连接状态正常");
            }

        } catch (Exception e) {
            result.put("error", e.getMessage());
            result.put("healthStatus", "ERROR");
        }

        return success(result);
    }

}
