package cn.iocoder.zj.module.collector.collect.basicdata.manager;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.zj.framework.common.dal.manager.HostSecGroupData;
import cn.iocoder.zj.framework.common.dal.manager.SecGroupData;
import cn.iocoder.zj.framework.common.dal.manager.SecGroupRuleData;
import cn.iocoder.zj.framework.common.dal.manager.aggregation.SecGroupAggData;
import cn.iocoder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.iocoder.zj.module.collector.service.SecGroupInfo;
import cn.iocoder.zj.module.collector.util.DataSetAnalyzerUtils;
import cn.iocoder.zj.module.collector.util.OptimizedDataConverter;
import cn.iocoder.zj.module.collector.util.SpringBeanUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_SEC_GROUP;

@Slf4j
public class BasicSecGroupDataImpl extends AbstractBasicData {


    @Override
    public void preCheck() {

    }

    @Override
    public void collectBasicData(List<?> results, String type) {
        if (results == null || results.isEmpty()) {
            return;
        }
        SecGroupInfo secGroupInfo = SpringBeanUtils.getBean(SecGroupInfo.class);

        // 性能优化：使用优化的数据转换器替代双重序列化
        OptimizedDataConverter converter = SpringBeanUtils.getBean(OptimizedDataConverter.class);

        long startTime = System.currentTimeMillis();
        SecGroupAggData newData = converter.convert(results.get(0), SecGroupAggData.class);
        long endTime = System.currentTimeMillis();

        log.debug("[性能优化] SecGroupAggData 转换完成. 耗时: {}ms", endTime - startTime);
        // 获取安全组信息
        if (!newData.getSecGroups().isEmpty()) {
            List<SecGroupData> secGroups = newData.getSecGroups();
            List<SecGroupData> oldSecGroupData = secGroupInfo.selectByIdPlatformList(secGroups.get(0).getPlatformId());
            Map<String, List<SecGroupData>> result = DataSetAnalyzerUtils.compareCollectionsByKey(oldSecGroupData, secGroups, SecGroupData::getUuid);
            List<SecGroupData> added = result.get("added");
            if (CollectionUtil.isNotEmpty(added)) {
                secGroupInfo.add(added);
            }
            List<SecGroupData> deleted = result.get("deleted");
            if (CollectionUtil.isNotEmpty(deleted)) {
                List<String> deletList = deleted.stream().map(SecGroupData::getUuid).collect(Collectors.toList());
                secGroupInfo.deleted(deletList);
            }
            List<SecGroupData> updated = result.get("updated");
            if (CollectionUtil.isNotEmpty(updated)) {
                secGroupInfo.updated(updated);
            }
        }

        // 获取安全组规则
        if (!newData.getSecGroupRules().isEmpty()) {
            List<SecGroupRuleData> secGroupRules = newData.getSecGroupRules();
            List<SecGroupRuleData> oldSecGroupRulesData = secGroupInfo.selectSecGroupRulePlatformList(secGroupRules.get(0).getPlatformId());
            Map<String, List<SecGroupRuleData>> result = DataSetAnalyzerUtils.compareCollectionsByKey(oldSecGroupRulesData, secGroupRules, SecGroupRuleData::getUuid);
            List<SecGroupRuleData> added = result.get("added");
            if (CollectionUtil.isNotEmpty(added)) {
                secGroupInfo.addSecGroupRule(added);
            }
            List<SecGroupRuleData> deleted = result.get("deleted");
            if (CollectionUtil.isNotEmpty(deleted)) {
                List<String> deletList = deleted.stream().map(SecGroupRuleData::getUuid).collect(Collectors.toList());
                secGroupInfo.deletedSecGroupRule(deletList);
            }
            List<SecGroupRuleData> updated = result.get("updated");
            if (CollectionUtil.isNotEmpty(updated)) {
                secGroupInfo.updatedSecGroupRule(updated);
            }
        }
        // 云主机和安全组关系
        if (!newData.getHostSecGroups().isEmpty()) {
            List<HostSecGroupData> hostSecGroups = newData.getHostSecGroups();
            List<HostSecGroupData> oldSecGroupRulesData = secGroupInfo.selectHostSecGroupPlatformList(hostSecGroups.get(0).getPlatformId());
            Map<String, List<HostSecGroupData>> result = DataSetAnalyzerUtils.compareCollectionsByMultiKeys(oldSecGroupRulesData, hostSecGroups, HostSecGroupData::getHostUuid, HostSecGroupData::getSecgroupUuid);
            List<HostSecGroupData> added = result.get("added");
            if (CollectionUtil.isNotEmpty(added)) {
                secGroupInfo.addHostSecGroup(added);
            }
            List<HostSecGroupData> deleted = result.get("deleted");
            if (CollectionUtil.isNotEmpty(deleted)) {
                List<Long> deletList = deleted.stream().map(HostSecGroupData::getId).collect(Collectors.toList());
                secGroupInfo.deletedHostSecGroup(deletList);
            }
            List<HostSecGroupData> updated = result.get("updated");
            if (CollectionUtil.isNotEmpty(updated)) {
                secGroupInfo.updatedHostSecGroup(updated);
            }
        }
    }

    @Override
    public String supportProtocol() {
        return BASIC_SEC_GROUP.code();
    }
}
