package cn.iocoder.zj.module.collector.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.TagData;
import cn.iocoder.zj.module.collector.constants.BusinessConstants;
import cn.iocoder.zj.module.collector.framework.kafka.KafkaProducerService;
import com.google.gson.Gson;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

public class SendTagUtils {
    public static <T> void sendMessage(List<T> array, String type) {
        if (CollUtil.isEmpty(array)) return;

        Gson gson = new Gson();
        KafkaProducerService producer = SpringBeanUtils.getBean(KafkaProducerService.class);

        List<TagData> tagList = new ArrayList<>();

        for (T obj : array) {
            String tagStr = getField(obj, "tag");
            if (StrUtil.isEmpty(tagStr)) continue;

            for (String entry : tagStr.split(",")) {
                String[] parts = entry.split("&");
                if (parts.length < 2) continue;

                TagData tagData = new TagData();
                tagData.setTagName(parts[0]);
                tagData.setTagUuid(parts[1]);
                tagData.setId(Convert.toLong(getField(obj, "id")));
                tagData.setPlatformId(Convert.toLong(getField(obj, "platformId")));
                tagData.setName(getField(obj, "name"));
                tagData.setType(type);
                tagList.add(tagData);
            }
        }

        if (CollUtil.isNotEmpty(tagList)) {
            producer.sendMessage(BusinessConstants.TAG_TOPIC, gson.toJson(tagList));
        }
    }

    private static String getField(Object obj, String fieldName) {
        try {
            Method method = obj.getClass().getMethod("get" + StrUtil.upperFirst(fieldName));
            Object value = method.invoke(obj);
            return value != null ? value.toString() : "";
        } catch (Exception e) {
            return "";
        }
    }
}



