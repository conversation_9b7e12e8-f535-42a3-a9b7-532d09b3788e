package cn.iocoder.zj.module.collector.service.impl;

import cn.iocoder.zj.framework.common.message.ClusterMsg;
import cn.iocoder.zj.module.collector.dal.schedule.ScheduleTask;
import cn.iocoder.zj.module.collector.framework.netty.job.cache.TaskCacheModel;
import cn.iocoder.zj.module.collector.framework.netty.job.cache.TaskModel;
import cn.iocoder.zj.module.collector.framework.netty.server.ChannelCtxMap;
import cn.iocoder.zj.module.collector.framework.netty.task.TaskHelper;
import cn.iocoder.zj.module.collector.service.ScheduleTaskService;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static cn.iocoder.zj.framework.common.message.ClusterMsg.MessageType.SCHEDULED_INFO;

@Slf4j
@Service
public class ScheduleTaskServiceImpl implements ScheduleTaskService {

    @Resource
    private TaskHelper taskHelper;

    /**
     * 启动任务
     * @param scheduleTask
     */
    @Override
    public void startTask(ScheduleTask scheduleTask) {
        List<ChannelHandlerContext> contexts = ChannelCtxMap.getChannelCtxByClientId(scheduleTask.getClientId());
        Map<ChannelHandlerContext, List<String>> allocateTasks = taskHelper.allocateTasks
                (contexts, scheduleTask.getJobName());
        allocateTasks.forEach((channel, tasks) -> {
            log.info("Channel: {} has been startTask tasks: {}", channel, tasks);
            taskHelper.buildMessage(scheduleTask.getClientId(), ClusterMsg.MessageType.SCHEDULED_START,channel, tasks);
        });
    }

    /**
     * 听着任务
     * @param scheduleTask
     */
    @Override
    public void stopTask(ScheduleTask scheduleTask) {
        List<ChannelHandlerContext> contexts = ChannelCtxMap.getChannelCtxByClientId(scheduleTask.getClientId());
        Map<ChannelHandlerContext, List<String>> allocateTasks = taskHelper.allocateTasks
                (contexts, scheduleTask.getJobName());
        allocateTasks.forEach((channel, tasks) -> {
            log.info("Channel: {} has been stopTask tasks: {}", channel, tasks);
            taskHelper.buildMessage(scheduleTask.getClientId(), ClusterMsg.MessageType.SCHEDULED_STOP,channel, tasks);
        });
    }

    @Override
    public void modifyTask(ScheduleTask scheduleTask) {
        List<ChannelHandlerContext> contexts = ChannelCtxMap.getChannelCtxByClientId(scheduleTask.getClientId());
        Map<ChannelHandlerContext, List<String>> allocateTasks = taskHelper.allocateTasks
                (contexts, scheduleTask.getJobName());
        allocateTasks.forEach((channel, tasks) -> {
            log.info("Channel: {} has been modifyTask tasks: {}", channel, tasks);
            taskHelper.buildMessage(scheduleTask.getClientId(), ClusterMsg.MessageType.SCHEDULED_MODIFY,channel, tasks);
        });
    }

    @Override
    public void refreshList() {
        Map<String, List<ChannelHandlerContext>> listMap = ChannelCtxMap.groupChannelsByClientId();
        for (Map.Entry<String, List<ChannelHandlerContext>> entry : listMap.entrySet()) {
            String clientId = entry.getKey();
            List<ChannelHandlerContext> contextList = entry.getValue();
            for (ChannelHandlerContext ctx : contextList) {
                ctx.writeAndFlush( ClusterMsg.Message.newBuilder().setClientId(clientId)
                        .setType(SCHEDULED_INFO)
                        .build());
            }
        }
    }

    @Override
    public List<TaskModel> list() {
        List<TaskModel> taskModelList = Lists.newArrayList();
        Map<String, List<TaskCacheModel>> scheduledInfo = ChannelCtxMap.getScheduledInfo();
        for (Map.Entry<String, List<TaskCacheModel>> entry : scheduledInfo.entrySet()) {
            taskModelList.add(TaskModel.builder()
                    .clientId(entry.getKey())
                    .taskCacheModel(entry.getValue())
                    .build());
        }
        return  taskModelList;
    }

}
