package cn.iocoder.zj.module.collector.service.impl;

import cn.iocoder.zj.framework.common.dal.manager.HostSecGroupData;
import cn.iocoder.zj.framework.common.dal.manager.SecGroupData;
import cn.iocoder.zj.framework.common.dal.manager.SecGroupRuleData;
import cn.iocoder.zj.module.collector.mapper.SecGroupHostInfoMapper;
import cn.iocoder.zj.module.collector.mapper.SecGroupInfoMapper;
import cn.iocoder.zj.module.collector.mapper.SecGroupRuleInfoMapper;
import cn.iocoder.zj.module.collector.service.SecGroupInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class SecGroupInfoImpl implements SecGroupInfo {

    @Resource
    SecGroupInfoMapper secGroupInfoMapper;

    @Resource
    SecGroupRuleInfoMapper secGroupRuleInfoMapper;

    @Resource
    SecGroupHostInfoMapper secGroupHostInfoMapper;

    @Override
    public List<SecGroupData> selectByIdPlatformList(Long platformId) {
        return secGroupInfoMapper.selectList(new QueryWrapper<SecGroupData>().eq("platform_id", platformId));
    }

    @Override
    public void add(List<SecGroupData> added) {
        added.forEach(secGroupInfoMapper::insert);
    }

    @Override
    public void deleted(List<String> deletList) {
        for (String id : deletList) {
            secGroupInfoMapper.delete(new QueryWrapper<SecGroupData>().eq("uuid", id));
        }
    }

    @Override
    public void updated(List<SecGroupData> updated) {
        for (SecGroupData secGroupData : updated) {
            secGroupInfoMapper.update(secGroupData, new QueryWrapper<SecGroupData>().eq("uuid", secGroupData.getUuid()));
        }
    }

    @Override
    public List<HostSecGroupData> selectHostSecGroupPlatformList(Long platformId) {
        return secGroupHostInfoMapper.selectList(new QueryWrapper<HostSecGroupData>().eq("platform_id", platformId));
    }

    @Override
    public void addHostSecGroup(List<HostSecGroupData> added) {
        added.forEach(secGroupHostInfoMapper::insert);
    }

    @Override
    public void deletedHostSecGroup(List<Long> deletList) {
        for (Long id : deletList) {
            secGroupHostInfoMapper.delete(new QueryWrapper<HostSecGroupData>().eq("id", id));
        }
    }

    @Override
    public void updatedHostSecGroup(List<HostSecGroupData> updated) {
        for (HostSecGroupData hostSecGroupData : updated) {
            secGroupHostInfoMapper.update(hostSecGroupData, new QueryWrapper<HostSecGroupData>().eq("id", hostSecGroupData.getId()));
        }
    }

    @Override
    public List<SecGroupRuleData> selectSecGroupRulePlatformList(Long platformId) {
        return secGroupRuleInfoMapper.selectList(new QueryWrapper<SecGroupRuleData>().eq("platform_id", platformId));
    }

    @Override
    public void addSecGroupRule(List<SecGroupRuleData> added) {
        added.forEach(secGroupRuleInfoMapper::insert);
    }

    @Override
    public void deletedSecGroupRule(List<String> deletList) {
        for (String id : deletList) {
            secGroupRuleInfoMapper.delete(new QueryWrapper<SecGroupRuleData>().eq("uuid", id));
        }
    }

    @Override
    public void updatedSecGroupRule(List<SecGroupRuleData> updated) {
        for (SecGroupRuleData secGroupRuleData : updated) {
            secGroupRuleInfoMapper.update(secGroupRuleData, new QueryWrapper<SecGroupRuleData>().eq("uuid", secGroupRuleData.getUuid()));
        }
    }
}
