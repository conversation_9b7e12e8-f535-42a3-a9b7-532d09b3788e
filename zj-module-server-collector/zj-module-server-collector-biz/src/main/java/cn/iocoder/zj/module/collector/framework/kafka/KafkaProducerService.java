package cn.iocoder.zj.module.collector.framework.kafka;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.*;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 优化的 Kafka 生产者服务
 *
 * 性能优化：
 * 1. 异步发送消息，避免阻塞调用线程
 * 2. 批量处理和优化的日志策略
 * 3. 完善的错误处理和重试机制
 * 4. 性能监控和指标统计
 * 5. 消息队列缓冲，提升高并发处理能力
 *
 * <AUTHOR>
 * <AUTHOR> (优化版本)
 */
@Slf4j
@Order(2)
@Component
public class KafkaProducerService implements DisposableBean {

    private KafkaProducer<String, String> producer;

    /**
     * 异步发送队列，用于缓冲高并发消息
     */
    private final BlockingQueue<MessageTask> sendQueue = new LinkedBlockingQueue<>(10000);

    /**
     * 异步发送线程池
     */
    private ExecutorService asyncSendExecutor;

    /**
     * 批量发送线程池
     */
    private ScheduledExecutorService batchSendExecutor;

    /**
     * 性能统计指标
     */
    private final LongAdder totalSentCount = new LongAdder();
    private final LongAdder totalFailedCount = new LongAdder();
    private final LongAdder totalRetryCount = new LongAdder();
    private final AtomicLong lastReportTime = new AtomicLong(System.currentTimeMillis());

    /**
     * 批量发送缓冲区
     */
    private final List<MessageTask> batchBuffer = new CopyOnWriteArrayList<>();

    /**
     * 消息任务封装类
     */
    private static class MessageTask {
        final String topic;
        final String message;
        final long createTime;
        int retryCount = 0;

        MessageTask(String topic, String message) {
            this.topic = topic;
            this.message = message;
            this.createTime = System.currentTimeMillis();
        }
    }

    public KafkaProducerService(KafkaProperties kafkaProperties) {
        initKafkaProducer(kafkaProperties);
    }

    /**
     * 初始化 Kafka 生产者，优化配置参数
     */
    private void initKafkaProducer(KafkaProperties kafkaProperties) {
        Properties props = new Properties();

        // 基础配置
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaProperties.getBootstrapServers());
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());

        // 性能优化配置
        props.put(ProducerConfig.ACKS_CONFIG, "1"); // 改为 "1" 提升性能，降低延迟
        props.put(ProducerConfig.RETRIES_CONFIG, 3);
        props.put(ProducerConfig.RETRY_BACKOFF_MS_CONFIG, 100);

        // 批处理优化
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 32768); // 32KB 批次大小
        props.put(ProducerConfig.LINGER_MS_CONFIG, 10); // 增加到 10ms，提升批处理效率
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 67108864); // 64MB 缓冲区

        // 压缩和超时配置
        props.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, "lz4"); // 使用 lz4 压缩，性能更好
        props.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, 30000); // 减少到 30s
        props.put(ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG, 120000); // 2分钟交付超时
        props.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, 10000); // 减少阻塞时间到 10s

        // 连接优化
        props.put(ProducerConfig.CONNECTIONS_MAX_IDLE_MS_CONFIG, 300000); // 5分钟空闲连接
        props.put(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION, 5); // 增加并发请求数

        // 安全配置
        props.put("security.protocol", "SASL_PLAINTEXT");
        props.put("sasl.mechanism", "PLAIN");
        props.put("sasl.jaas.config", "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"user1\" password=\"6TbRXtMkjQ\";");

        producer = new KafkaProducer<>(props);

        log.info("[Kafka生产者] 初始化完成，优化配置已应用");
    }

    /**
     * 初始化异步发送组件
     */
    @PostConstruct
    public void init() {
        // 初始化异步发送线程池
        asyncSendExecutor = new ThreadPoolExecutor(
                2, // 核心线程数
                8, // 最大线程数
                60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000),
                r -> new Thread(r, "kafka-async-sender-" + System.currentTimeMillis()),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

        // 初始化批量发送调度器
        batchSendExecutor = Executors.newScheduledThreadPool(1,
                r -> new Thread(r, "kafka-batch-sender"));

        // 启动异步发送循环
        startAsyncSendLoop();

        // 启动批量发送调度
        startBatchSendScheduler();

        // 启动性能统计报告
        startPerformanceReporting();

        log.info("[Kafka生产者] 异步发送组件初始化完成");
    }

    /**
     * 异步发送消息 - 主要接口，保持向后兼容
     *
     * @param topic 主题
     * @param message 消息内容
     */
    public void sendMessage(String topic, String message) {
        sendMessageAsync(topic, message);
    }

    /**
     * 异步发送消息 - 非阻塞实现
     *
     * @param topic 主题
     * @param message 消息内容
     */
    public void sendMessageAsync(String topic, String message) {
        if (topic == null || message == null) {
            log.warn("[Kafka发送] 主题或消息为空，跳过发送");
            return;
        }

        MessageTask task = new MessageTask(topic, message);

        // 尝试加入发送队列
        if (!sendQueue.offer(task)) {
            // 队列满时的处理策略
            totalFailedCount.increment();
            log.warn("[Kafka发送] 发送队列已满，消息被丢弃. 主题: {}, 队列大小: {}",
                    topic, sendQueue.size());

            // 可选：同步发送重要消息
            if (isImportantTopic(topic)) {
                sendMessageSync(task);
            }
        } else {
            log.debug("[Kafka发送] 消息已加入发送队列. 主题: {}, 队列大小: {}",
                    topic, sendQueue.size());
        }
    }

    /**
     * 启动异步发送循环
     */
    private void startAsyncSendLoop() {
        asyncSendExecutor.submit(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    // 从队列中取出消息任务
                    MessageTask task = sendQueue.take();

                    // 执行异步发送
                    sendMessageSync(task);

                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.info("[Kafka异步发送] 发送线程被中断");
                    break;
                } catch (Exception e) {
                    log.error("[Kafka异步发送] 发送循环异常", e);
                }
            }
        });
    }

    /**
     * 同步发送单条消息（内部使用）
     */
    private void sendMessageSync(MessageTask task) {
        try {
            ProducerRecord<String, String> record = new ProducerRecord<>(task.topic, task.message);

            // 异步发送，使用回调处理结果
            producer.send(record, new Callback() {
                @Override
                public void onCompletion(RecordMetadata metadata, Exception exception) {
                    if (exception == null) {
                        // 发送成功
                        totalSentCount.increment();
                        long sendTime = System.currentTimeMillis() - task.createTime;

                        log.debug("[Kafka发送成功] 主题: {}, 分区: {}, 偏移量: {}, 耗时: {}ms",
                                metadata.topic(), metadata.partition(), metadata.offset(), sendTime);

                        // 慢发送警告
                        if (sendTime > 1000) {
                            log.warn("[Kafka慢发送] 主题: {}, 耗时: {}ms", task.topic, sendTime);
                        }
                    } else {
                        // 发送失败，尝试重试
                        handleSendFailure(task, exception);
                    }
                }
            });

        } catch (Exception e) {
            handleSendFailure(task, e);
        }
    }

    /**
     * 处理发送失败
     */
    private void handleSendFailure(MessageTask task, Exception exception) {
        task.retryCount++;
        totalFailedCount.increment();

        log.warn("[Kafka发送失败] 主题: {}, 重试次数: {}, 错误: {}",
                task.topic, task.retryCount, exception.getMessage());

        // 重试逻辑
        if (task.retryCount <= 3) {
            totalRetryCount.increment();

            // 延迟重试
            CompletableFuture.delayedExecutor(task.retryCount * 1000L, TimeUnit.MILLISECONDS, asyncSendExecutor)
                    .execute(() -> sendMessageSync(task));

        } else {
            // 超过重试次数，记录到死信队列或日志
            log.error("[Kafka发送彻底失败] 主题: {}, 消息: {}, 最终错误: {}",
                    task.topic, task.message.substring(0, Math.min(100, task.message.length())),
                    exception.getMessage());

            // 可选：保存到死信队列或文件
            saveToDeadLetterQueue(task, exception);
        }
    }

    /**
     * 批量发送消息
     */
    public void sendMessageBatch(String topic, List<String> messages) {
        if (messages == null || messages.isEmpty()) {
            return;
        }

        log.debug("[Kafka批量发送] 主题: {}, 消息数量: {}", topic, messages.size());

        for (String message : messages) {
            sendMessageAsync(topic, message);
        }
    }

    /**
     * 启动批量发送调度器
     */
    private void startBatchSendScheduler() {
        batchSendExecutor.scheduleAtFixedRate(() -> {
            try {
                processBatchBuffer();
            } catch (Exception e) {
                log.error("[Kafka批量发送] 批量处理异常", e);
            }
        }, 5, 5, TimeUnit.SECONDS); // 每5秒处理一次批量缓冲区
    }

    /**
     * 处理批量缓冲区
     */
    private void processBatchBuffer() {
        if (batchBuffer.isEmpty()) {
            return;
        }

        List<MessageTask> toProcess = new CopyOnWriteArrayList<>(batchBuffer);
        batchBuffer.clear();

        log.debug("[Kafka批量处理] 处理缓冲区消息数量: {}", toProcess.size());

        for (MessageTask task : toProcess) {
            sendMessageAsync(task.topic, task.message);
        }
    }

    /**
     * 启动性能统计报告
     */
    private void startPerformanceReporting() {
        batchSendExecutor.scheduleAtFixedRate(() -> {
            try {
                printPerformanceStats();
            } catch (Exception e) {
                log.error("[Kafka性能统计] 统计报告异常", e);
            }
        }, 60, 300, TimeUnit.SECONDS); // 1分钟后开始，每5分钟报告一次
    }

    /**
     * 打印性能统计信息
     */
    private void printPerformanceStats() {
        long currentTime = System.currentTimeMillis();
        long lastReport = lastReportTime.getAndSet(currentTime);
        long timeDiff = currentTime - lastReport;

        long totalSent = totalSentCount.sum();
        long totalFailed = totalFailedCount.sum();
        long totalRetry = totalRetryCount.sum();

        double successRate = totalSent + totalFailed > 0 ?
                (totalSent * 100.0 / (totalSent + totalFailed)) : 0;

        double sendRate = timeDiff > 0 ? (totalSent * 1000.0 / timeDiff) : 0;

        log.info("=== Kafka生产者性能统计 ===");
        log.info("发送成功: {}, 发送失败: {}, 重试次数: {}", totalSent, totalFailed, totalRetry);
        log.info("成功率: {:.2f}%, 发送速率: {:.0f} msg/s", successRate, sendRate);
        log.info("队列大小: {}, 生产者指标: {}", sendQueue.size(), getProducerMetrics());
    }

    /**
     * 获取生产者指标
     */
    private String getProducerMetrics() {
        try {
            var metrics = producer.metrics();
            int metricsCount = metrics.size();

            // 查找缓冲区可用字节数指标
            long bufferAvailable = 0L;
            for (var entry : metrics.entrySet()) {
                if (entry.getKey().name().equals("buffer-available-bytes")) {
                    Object value = entry.getValue().metricValue();
                    if (value instanceof Number) {
                        bufferAvailable = ((Number) value).longValue();
                    }
                    break;
                }
            }

            // 查找连接数指标
            long connectionCount = 0L;
            for (var entry : metrics.entrySet()) {
                if (entry.getKey().name().equals("connection-count")) {
                    Object value = entry.getValue().metricValue();
                    if (value instanceof Number) {
                        connectionCount = ((Number) value).longValue();
                    }
                    break;
                }
            }

            return String.format("指标总数: %d, 连接数: %d, 缓冲区可用: %d bytes",
                    metricsCount, connectionCount, bufferAvailable);
        } catch (Exception e) {
            return "指标获取失败: " + e.getMessage();
        }
    }

    /**
     * 判断是否为重要主题
     */
    private boolean isImportantTopic(String topic) {
        // 可以根据业务需求定义重要主题
        return topic != null && (topic.contains("alert") || topic.contains("critical"));
    }

    /**
     * 保存到死信队列
     */
    private void saveToDeadLetterQueue(MessageTask task, Exception exception) {
        // 这里可以实现死信队列逻辑，比如保存到文件或数据库
        log.error("[死信队列] 主题: {}, 消息长度: {}, 错误: {}",
                task.topic, task.message.length(), exception.getMessage());

        // 可选：保存到文件
        // Files.write(Paths.get("dead-letter-queue.log"),
        //     (task.topic + "|" + task.message + "|" + exception.getMessage() + "\n").getBytes(),
        //     StandardOpenOption.CREATE, StandardOpenOption.APPEND);
    }

    /**
     * 获取发送队列状态
     */
    public String getQueueStatus() {
        return String.format("队列大小: %d/%d, 发送成功: %d, 发送失败: %d",
                sendQueue.size(), 10000, totalSentCount.sum(), totalFailedCount.sum());
    }

    /**
     * 检查服务健康状态
     */
    public boolean isHealthy() {
        return sendQueue.size() < 8000 && // 队列未满
               (totalSentCount.sum() + totalFailedCount.sum() == 0 || // 没有发送记录或成功率正常
                totalSentCount.sum() * 100.0 / (totalSentCount.sum() + totalFailedCount.sum()) > 80);
    }

    /**
     * 资源清理
     */
    @Override
    public void destroy() throws Exception {
        log.info("[Kafka生产者] 开始关闭服务");

        try {
            // 停止接收新消息
            if (batchSendExecutor != null && !batchSendExecutor.isShutdown()) {
                batchSendExecutor.shutdown();
                if (!batchSendExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                    batchSendExecutor.shutdownNow();
                }
            }

            // 处理剩余消息
            log.info("[Kafka生产者] 处理剩余 {} 条消息", sendQueue.size());
            int remainingMessages = 0;
            while (!sendQueue.isEmpty() && remainingMessages < 1000) { // 最多处理1000条
                MessageTask task = sendQueue.poll();
                if (task != null) {
                    sendMessageSync(task);
                    remainingMessages++;
                }
            }

            // 关闭异步发送线程池
            if (asyncSendExecutor != null && !asyncSendExecutor.isShutdown()) {
                asyncSendExecutor.shutdown();
                if (!asyncSendExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                    asyncSendExecutor.shutdownNow();
                }
            }

            // 关闭 Kafka 生产者
            if (producer != null) {
                producer.flush(); // 确保所有消息发送完成
                producer.close(Duration.ofSeconds(30));
            }

            // 最终统计
            printPerformanceStats();

            log.info("[Kafka生产者] 服务关闭完成");

        } catch (Exception e) {
            log.error("[Kafka生产者] 关闭服务时发生异常", e);
            throw e;
        }
    }
}
