package cn.iocoder.zj.module.collector.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.iocoder.cloud.module.cloudedge.api.monitor.MonitorApi;
import cn.iocoder.zj.framework.common.dal.manager.ScanIPData;
import cn.iocoder.zj.module.collector.dal.alert.AlertDo;
import cn.iocoder.zj.module.collector.dal.platform.Platform;
import cn.iocoder.zj.module.collector.mapper.AlertMapper;
import cn.iocoder.zj.module.collector.mapper.PlatformInfoMapper;
import cn.iocoder.zj.module.collector.service.PlatformInfo;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmDorisReqDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 **/
@Service
public class PlatformInfoImpl implements PlatformInfo {

    @Resource
    private PlatformInfoMapper platformInfoMapper;
    @Resource
    AlarmConfigApi alarmConfigApi;
    @Resource
    MonitorApi monitorApi;

    @Resource
    AlertMapper alertMapper;

    /**
     * 查询平台信息
     *
     * @return
     */
    @Override
    public List<Platform> getPlatformInfos() {
        return platformInfoMapper.getPlatformInfos();
    }

    @Override
    public List<Platform> getPlatformByTenantName(String name) {
        return platformInfoMapper.getPlatformByTenantName(name);
    }

    @Override
    public void updatePlatformInfo(Platform platform) {

        long between = DateUtil.between(platform.getDateTime(), new Date(), DateUnit.SECOND);
        platform.setDiffTime(between);
        platformInfoMapper.updatePlatformInfo(platform.getPlatformId(), platform.getState(), platform.getDiffTime(), platform.getDateTime());
        // 平台离线
        final Long alertId = alarmConfigApi.getMaxAlertId().getData();
        List<AlarmDorisReqDTO> alarm = new ArrayList<>();
        if (1 == platform.getState()) {
            AlertDo collectorAlerts = alertMapper.getAlarmByMonitorId(platform.getPlatformId());
            if (collectorAlerts == null) {
                AlarmDorisReqDTO collectorAlert = new AlarmDorisReqDTO();
                collectorAlert.setPriority(0);
                collectorAlert.setStatus(0);
                collectorAlert.setIsSolved(0);
                collectorAlert.setFirstAlarmTime(new Date().getTime());
                collectorAlert.setGmtCreate(new Date());
                collectorAlert.setGmtUpdate(new Date());
                collectorAlert.setLastAlarmTime(new Date().getTime());
                collectorAlert.setMonitorName(platform.getPlatformName());
                collectorAlert.setMonitorId(String.valueOf(platform.getPlatformId()));
                collectorAlert.setPlatformName(platform.getPlatformName());
                collectorAlert.setPlatformId(platform.getPlatformId());
                collectorAlert.setContent(String.format("私有云平台 (%s)已离线！", platform.getPlatformName()));
                collectorAlert.setAlarmName(platform.getPlatformName());
                collectorAlert.setTimes(1);
                collectorAlert.setResourceType(1);
                collectorAlert.setApp(platform.getTypeCode());
                collectorAlert.setAlarmId(0L);
                collectorAlert.setId(alertId + 1);
                alarm.add(collectorAlert);
                Map<String, List> alertMap = new HashMap<>();
                alertMap.put("insertList", alarm);
                // 创建方法中需要有updateList，防止空指针异常
                alertMap.put("updateList", new ArrayList<>());
                alarmConfigApi.createAlarmToDoris(alertMap);
            } else {
                Integer times = collectorAlerts.getTimes() + 1;
                List<AlertDo> toUpdate = new ArrayList<>();
                collectorAlerts.setLastAlarmTime(new Date().getTime());
                collectorAlerts.setGmtUpdate(new Date());
                collectorAlerts.setTimes(times + 1);
                toUpdate.add(collectorAlerts);
                List<AlarmDorisReqDTO> alarmDorisReqDTOS = BeanUtil.copyToList(toUpdate, AlarmDorisReqDTO.class);
                alarmConfigApi.updateAlarmDoris(alarmDorisReqDTOS);
            }

        } else {
            AlertDo collectorAlerts = alertMapper.getAlarmByMonitorId(platform.getPlatformId());
            if (collectorAlerts != null) {
                List<AlertDo> toUpdate = new ArrayList<>();
                collectorAlerts.setIsSolved(2);
                collectorAlerts.setMonitorId(String.valueOf(platform.getPlatformId()));
                collectorAlerts.setMonitorName(platform.getPlatformName());
                collectorAlerts.setLastAlarmTime(new Date().getTime());
                collectorAlerts.setGmtUpdate(new Date());
                toUpdate.add(collectorAlerts);
                List<AlarmDorisReqDTO> alarmDorisReqDTOS = BeanUtil.copyToList(toUpdate, AlarmDorisReqDTO.class);
                alarmConfigApi.updateAlarmDoris(alarmDorisReqDTOS);
            }
        }

    }

    @Override
    public void updateScanIP(List<ScanIPData> list) {
        platformInfoMapper.updateScanIP(list);
    }

    @Override
    public void updateScanIPState(Long ipRangeId) {
        platformInfoMapper.updateScanIPState(ipRangeId);
    }
}
