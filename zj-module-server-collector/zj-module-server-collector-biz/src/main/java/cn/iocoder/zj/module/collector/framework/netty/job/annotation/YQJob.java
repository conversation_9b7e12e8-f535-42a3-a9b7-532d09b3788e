package cn.iocoder.zj.module.collector.framework.netty.job.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义任务注解
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface YQJob {

    /**
     * 任务名称
     * @return
     */
    String value();

    /**
     * Cron表达式
     * @return
     */
    String cronExpression();
}
