package cn.iocoder.zj.module.collector.service.impl;

import cn.iocoder.zj.framework.common.dal.manager.VolumeInfoData;
import cn.iocoder.zj.module.collector.mapper.VolumeInfoMapper;
import cn.iocoder.zj.module.collector.service.VolumeDataInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class VolumeDataInfoImpl implements VolumeDataInfo {

    @Resource
    private VolumeInfoMapper volumeInfoMapper;

    @Override
    public List<VolumeInfoData> selectByIdPlatformList(Long platformId) {
        return  volumeInfoMapper.selectList(new QueryWrapper<VolumeInfoData>().eq("platform_id", platformId));
    }

    @Override
    public void add(List<VolumeInfoData> added) {
        for (VolumeInfoData volumeInfoData : added) {
            volumeInfoData.setMediaType(null);
            volumeInfoMapper.insert(volumeInfoData);
        }
    }

    @Override
    public void deleted(List<String> deletList) {
        for (String id : deletList) {
            volumeInfoMapper.delete(new QueryWrapper<VolumeInfoData>().eq("uuid", id));
        }
    }

    @Override
    public void updated(List<VolumeInfoData> updated) {
        for (VolumeInfoData volumeInfoData : updated) {
            volumeInfoData.setMediaType(null);
            volumeInfoMapper.update(volumeInfoData, new QueryWrapper<VolumeInfoData>().eq("uuid", volumeInfoData.getUuid()));
        }

    }
}
