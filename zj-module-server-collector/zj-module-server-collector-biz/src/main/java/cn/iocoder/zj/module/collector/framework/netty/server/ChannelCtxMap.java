package cn.iocoder.zj.module.collector.framework.netty.server;

import cn.iocoder.zj.module.collector.framework.netty.job.cache.TaskCacheModel;
import io.netty.channel.ChannelHandlerContext;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ChannelCtxMap {

    private static final Map<String, List<TaskCacheModel>> SCHEDULED_INFO = new ConcurrentHashMap<>(16);

    private static final Map<ChannelHandlerContext, String> CHANNEL_MAP = new ConcurrentHashMap<>(16);

    private static final Map<ChannelHandlerContext, String> CHANNEL_OFFLINE_MAP = new ConcurrentHashMap<>(16);

    /**
     * 添加ChannelHandlerContext到映射表
     */
    public static void addChannelCtx(ChannelHandlerContext ctx, String clientId) {
        Optional.ofNullable(ctx).orElseThrow(() -> new IllegalArgumentException("ChannelHandlerContext cannot be null"));
        Optional.ofNullable(clientId).orElseThrow(() -> new IllegalArgumentException("clientId cannot be null"));
        CHANNEL_MAP.put(ctx, clientId);
    }

    /**
     * 移除指定ChannelHandlerContext的映射
     */
    public static void removeChannelCtx(ChannelHandlerContext ctx) {
        Optional.ofNullable(ctx)
                .orElseThrow(() -> new IllegalArgumentException("ChannelHandlerContext cannot be null"));
        CHANNEL_MAP.remove(ctx);
    }

    /**
     * 根据clientId获取对应的ChannelHandlerContext列表
     */
    public static List<ChannelHandlerContext> getChannelCtxByClientId(String clientId) {
        if (clientId == null) {
            throw new IllegalArgumentException("clientId cannot be null");
        }
        return CHANNEL_MAP.entrySet()
                .stream()
                .filter(entry -> clientId.equals(entry.getValue()))
                .map(Map.Entry::getKey)
                .toList();
    }

    /**
     * 获取缓存列表
     * @return
     */
    public static Map<ChannelHandlerContext, String> getChannelMap() {
        return Collections.unmodifiableMap(CHANNEL_MAP);
    }

    /**
     * 根据clientId对CHANNEL_MAP进行分组
     * @return
     */
    public static Map<String, List<ChannelHandlerContext>> groupChannelsByClientId() {
        return Optional.of(CHANNEL_MAP)
                .filter(map -> !map.isEmpty())
                .map(map -> map.entrySet()
                        .stream()
                        .filter(entry -> Optional.ofNullable(entry.getKey()).isPresent()
                                && Optional.ofNullable(entry.getValue()).isPresent())
                        .collect(Collectors.groupingBy(
                                Map.Entry::getValue,
                                Collectors.mapping(
                                        Map.Entry::getKey,
                                        Collectors.toList()
                                )
                        )))
                .orElseGet(Collections::emptyMap);
    }


    /**
     * 客户端下线
     */
    public static void offline(String clientId) {
        Optional.ofNullable(clientId).orElseThrow(() -> new IllegalArgumentException("clientId cannot be null"));
        for (Map.Entry<ChannelHandlerContext, String> entry : CHANNEL_MAP.entrySet()) {
            if (clientId.equals(entry.getValue())) {
                ChannelHandlerContext ctx = entry.getKey();
                CHANNEL_OFFLINE_MAP.put(ctx, clientId);
                CHANNEL_MAP.remove(ctx);
                break;
            }
        }
    }

    /**
     * 客户端上线
     */
    public static void online(String clientId) {
        Optional.ofNullable(clientId).orElseThrow(() -> new IllegalArgumentException("clientId cannot be null"));
        Map<ChannelHandlerContext, String> resultMap = new ConcurrentHashMap<>();
        CHANNEL_OFFLINE_MAP.forEach((ctx, id) -> {
            if (clientId.equals(id)) {
                resultMap.put(ctx, id);
            }
        });
        resultMap.forEach((ctx, clientId2) -> {
            addChannelCtx(ctx, clientId);
        });
    }

    /**
     * 定时任务信息
     */
    public static void scheduledInfo(String key,List<TaskCacheModel> value) {
        SCHEDULED_INFO.put(key,value);
    }

    /**
     * 获取定时任务
     * @return
     */
    public static Map<String, List<TaskCacheModel>> getScheduledInfo() {
        return SCHEDULED_INFO;
    }

}
