package cn.iocoder.zj.module.collector.framework.netty.auth;

/**
 * <AUTHOR>
 */
public enum AESMode {

    CBC_PKCS5_PADDING("AES/CBC/PKCS5Padding"),
    CFB_NO_PADDING("AES/CFB/NoPadding"),
    OFB_NO_PADDING("AES/OFB/NoPadding"),
    CTR_NO_PADDING("AES/CTR/NoPadding"),
    GCM_NO_PADDING("AES/GCM/NoPadding");

    public final String mode;

    AESMode(String mode) {
        this.mode = mode;
    }

    public String getMode() {
        return this.mode;
    }
}

