package cn.iocoder.zj.module.collector.framework.netty.job.service;


import cn.iocoder.zj.module.collector.framework.netty.job.cache.TaskCacheModel;

import java.util.List;

/**
 * 定时任务服务类
 * <AUTHOR>
 **/
public interface ScheduleTaskService {

    /**
     * 查询任务列表
     * @return
     */
    List<TaskCacheModel> listTasks();

    /**
     * 移除任务
     */
    void removeTask(TaskCacheModel taskCacheModel);


    /**
     * 新增任务
     */
    void addTask(TaskCacheModel taskCacheModel);

    /**
     * 更新任务
     */
    void updateTask(String jobName);


    void clearAllTasks();
}
