package cn.iocoder.zj.module.collector.collect.basicdata.manager;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.zj.framework.common.dal.manager.VolumeSnapshotData;
import cn.iocoder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.iocoder.zj.module.collector.service.VolumeSnapshotInfo;
import cn.iocoder.zj.module.collector.util.DataSetAnalyzerUtils;
import cn.iocoder.zj.module.collector.util.OptimizedDataConverter;
import cn.iocoder.zj.module.collector.util.SpringBeanUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_SNAPSHOT;
public class BasicSnapshotDataImpl extends AbstractBasicData {
    @Override
    public void preCheck() {

    }

    @Override
    public void collectBasicData(List<?> results,String type) {
        OptimizedDataConverter converter = SpringBeanUtils.getBean(OptimizedDataConverter.class);

        //获取快照信息
        List<VolumeSnapshotData> newDataList = converter.convertList(results,VolumeSnapshotData.class);
                //数据库处理
        VolumeSnapshotInfo volumeSnapshotInfo = SpringBeanUtils.getBean(VolumeSnapshotInfo.class);
        List<VolumeSnapshotData> oldDataList = volumeSnapshotInfo.selectByIdPlatformList(newDataList.get(0).getPlatformId());
        Map<String, List<VolumeSnapshotData>> result = DataSetAnalyzerUtils.compareCollectionsByKey(oldDataList, newDataList, VolumeSnapshotData::getUuid);
        //入库
        List<VolumeSnapshotData> added = result.get("added");
        if (CollectionUtil.isNotEmpty(added)) {
            volumeSnapshotInfo.add(added);
        }
        List<VolumeSnapshotData> deleted = result.get("deleted");
        if (CollectionUtil.isNotEmpty(deleted)) {
            List<String> deletList = deleted.stream().map(VolumeSnapshotData::getUuid).collect(Collectors.toList());
            volumeSnapshotInfo.deleted(deletList);
        }
        List<VolumeSnapshotData> updated = result.get("updated");
        if (CollectionUtil.isNotEmpty(updated)) {
            volumeSnapshotInfo.updated(updated);
        }
    }

    @Override
    public String supportProtocol() {
        return BASIC_SNAPSHOT.code();
    }
}
