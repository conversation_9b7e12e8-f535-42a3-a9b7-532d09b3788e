package cn.iocoder.zj.module.collector.framework.netty.task;

import cn.hutool.core.lang.Assert;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import cn.iocoder.zj.module.collector.framework.netty.server.ChannelCtxMap;
import com.google.gson.Gson;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.text.StrPool.COMMA;

@Slf4j
@Component
public class TaskHelper {


    @Value("${collector-server.task}")
    private  String task;

    public void yqTask(String clientId) {
        Assert.notEmpty(clientId, "clientId cannot be empty");
        Assert.notEmpty(task, "task configuration cannot be empty");
        List<String> taskList = Arrays.stream(task.split(COMMA)).map(String::trim).collect(Collectors.toList());
        List<ChannelHandlerContext> contexts = ChannelCtxMap.getChannelCtxByClientId(clientId);
        if (Optional.ofNullable(contexts).orElse(Collections.emptyList()).isEmpty()) {
            log.info("No channels found for clientId: {}", clientId);
            return;
        }
        Map<ChannelHandlerContext, List<String>> allocationResult = allocateTasks(contexts, taskList);
        log.info("Task allocation details for Client ID: {}", clientId);
        allocationResult.forEach((channel, tasks) -> {
            log.info("Channel: {} has been assigned tasks: {}", channel, tasks);
            buildMessage(clientId, ClusterMsg.MessageType.SCHEDULED_START,channel, tasks);
        });
    }

    /**
     * 按轮询方式分配任务
     */
    public  Map<ChannelHandlerContext, List<String>> allocateTasks(List<ChannelHandlerContext> channels, List<String> tasks) {
        Map<ChannelHandlerContext, List<String>> allocation = new HashMap<>();
        for (ChannelHandlerContext channel : channels) {
            allocation.put(channel, new ArrayList<>());
        }
        int channelIndex = 0;
        for (String task : tasks) {
            ChannelHandlerContext channel = channels.get(channelIndex);
            allocation.get(channel).add(task);
            channelIndex = (channelIndex + 1) % channels.size();
        }
        return allocation;
    }


    /**
     * 构建消息对象
     */
    public  void buildMessage(String clientId, ClusterMsg.MessageType messageType,
                              ChannelHandlerContext channel, List<String> tasks) {
        channel.writeAndFlush(ClusterMsg.Message.newBuilder()
                .setClientId(clientId)
                .setType(messageType)
                .setData(new Gson().toJson(tasks))
                .build());
    }

}

