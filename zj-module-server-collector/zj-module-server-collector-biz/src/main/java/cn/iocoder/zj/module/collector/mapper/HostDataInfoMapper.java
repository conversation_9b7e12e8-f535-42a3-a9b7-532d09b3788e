package cn.iocoder.zj.module.collector.mapper;

import cn.iocoder.zj.framework.common.dal.manager.HostData;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 **/
@Mapper
public interface HostDataInfoMapper extends BaseMapper<HostData> {

    List<HostData> selectByIdPlatformList(@Param("platformId")Long platformId);

    int add(@Param("list") List<HostData> added);

    int deleted(@Param("list")List<String> deletList);

    int updated(@Param("list")List<HostData> updated);
}
