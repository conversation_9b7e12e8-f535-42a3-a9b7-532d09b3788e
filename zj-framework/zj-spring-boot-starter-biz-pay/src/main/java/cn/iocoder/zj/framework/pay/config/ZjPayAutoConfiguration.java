package cn.iocoder.zj.framework.pay.config;

import cn.iocoder.zj.framework.pay.core.client.PayClientFactory;
import cn.iocoder.zj.framework.pay.core.client.impl.PayClientFactoryImpl;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * 支付配置类
 *
 * <AUTHOR>
 */
@AutoConfiguration
@EnableConfigurationProperties(PayProperties.class)
public class ZjPayAutoConfiguration {

    @Bean
    public PayClientFactory payClientFactory() {
        return new PayClientFactoryImpl();
    }

}
