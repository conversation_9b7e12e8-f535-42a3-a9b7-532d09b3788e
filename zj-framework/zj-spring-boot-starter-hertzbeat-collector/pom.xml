<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>cn.iocoder.cloud</groupId>
        <artifactId>zj-framework</artifactId>
        <version>${revision}</version>
    </parent>
    <modules>
        <module>hertzbeat-collector-basic</module>
        <module>hertzbeat-collector-collector</module>
        <module>hertzbeat-collector-common</module>
        <module>hertzbeat-collector-kafka</module>
        <module>hertzbeat-collector-mongodb</module>
        <module>hertzbeat-collector-nebulagraph</module>
        <module>hertzbeat-collector-rocketmq</module>
    </modules>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>zj-spring-boot-starter-hertzbeat-collector</artifactId>
    <name>${project.artifactId}</name>
    <description>hz公共类</description>
    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
    </properties>
    <packaging>pom</packaging>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>hertzbeat-collector-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>hertzbeat-collector-basic</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>hertzbeat-collector-mongodb</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>hertzbeat-collector-kafka</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>hertzbeat-collector-nebulagraph</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.cloud</groupId>
                <artifactId>hertzbeat-collector-rocketmq</artifactId>
                <version>${revision}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.dromara.hertzbeat</groupId>
            <artifactId>zj-common-hz</artifactId>
        </dependency>
    </dependencies>

</project>