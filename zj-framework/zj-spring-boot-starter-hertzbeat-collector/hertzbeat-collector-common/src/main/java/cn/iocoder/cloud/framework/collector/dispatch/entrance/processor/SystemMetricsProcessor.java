package cn.iocoder.cloud.framework.collector.dispatch.entrance.processor;

import cn.iocoder.cloud.framework.remoting.netty.NettyRemotingProcessor;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.message.ClusterMsg;

@Slf4j
public class SystemMetricsProcessor implements NettyRemotingProcessor {

    @Override
    public ClusterMsg.Message handle(ChannelHandlerContext ctx, ClusterMsg.Message message) {
        log.info("collector receive manager server response heartbeat, time: {}. ", System.currentTimeMillis());
        return null;
    }

}
