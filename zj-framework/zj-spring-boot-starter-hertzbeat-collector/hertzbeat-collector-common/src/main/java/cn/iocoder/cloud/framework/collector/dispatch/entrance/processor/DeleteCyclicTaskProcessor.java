/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.collector.dispatch.entrance.processor;

import cn.iocoder.cloud.framework.collector.dispatch.entrance.CollectServer;
import cn.iocoder.cloud.framework.remoting.netty.NettyRemotingProcessor;
import com.fasterxml.jackson.core.type.TypeReference;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.message.ClusterMsg;
import org.apache.hertzbeat.common.util.JsonUtil;

import java.util.List;

/**
 * handle delete cyclic task message
 */
@Slf4j
public class DeleteCyclicTaskProcessor implements NettyRemotingProcessor {
    private final CollectServer collectServer;

    public DeleteCyclicTaskProcessor(CollectServer collectServer) {
        this.collectServer = collectServer;
    }


    @Override
    public ClusterMsg.Message handle(ChannelHandlerContext ctx, ClusterMsg.Message message) {
        TypeReference<List<Long>> typeReference = new TypeReference<>() {};
        List<Long> jobIds = JsonUtil.fromJson(message.getMsg(), typeReference);
        if (jobIds == null || jobIds.isEmpty()) {
            log.error("collector receive delete cyclic task job ids is null");
            return null;
        }
        for (Long jobId : jobIds) {
            collectServer.getCollectJobService().cancelAsyncCollectJob(jobId);
        }
        return null;
    }
}
