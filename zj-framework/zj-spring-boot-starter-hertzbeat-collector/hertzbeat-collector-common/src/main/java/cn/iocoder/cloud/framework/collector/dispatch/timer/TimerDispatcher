/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.collector.dispatch.timer;

import cn.iocoder.cloud.framework.collector.dispatch.entrance.internal.CollectResponseEventListener;
import com.sun.management.OperatingSystemMXBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.job.Job;
import org.apache.hertzbeat.common.entity.job.Metrics;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.apache.hertzbeat.common.util.JsonUtil;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.stereotype.Component;
import oshi.SystemInfo;
import oshi.hardware.GlobalMemory;
import oshi.hardware.HardwareAbstractionLayer;
import oshi.hardware.NetworkIF;

import java.lang.management.ManagementFactory;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * job timer dispatcher
 */
@Component
@Slf4j
public class TimerDispatcher implements TimerDispatch, DisposableBean {

    /**
     * time round schedule
     */
    private final Timer wheelTimer;
    /**
     * Existing periodic scheduled tasks
     */
    private final Map<Long, Timeout> currentCyclicTaskMap;
    /**
     * Existing temporary scheduled tasks
     */
    private final Map<Long, Timeout> currentTempTaskMap;
    /**
     * One-time task response listener holds
     * jobId - listener
     */
    private final Map<Long, CollectResponseEventListener> eventListeners;

    /**
     * is dispatcher online running
     */
    private final AtomicBoolean started;


    public TimerDispatcher() {
        this.wheelTimer = new HashedWheelTimer(r -> {
            Thread ret = new Thread(r, "wheelTimer");
            ret.setDaemon(true);
            return ret;
        }, 1, TimeUnit.SECONDS, 512);
        this.currentCyclicTaskMap = new ConcurrentHashMap<>(8);
        this.currentTempTaskMap = new ConcurrentHashMap<>(8);
        this.eventListeners = new ConcurrentHashMap<>(8);
        this.started = new AtomicBoolean(true);
    }

    @Override
    public void addJob(Job addJob, CollectResponseEventListener eventListener) {
        if (!this.started.get()) {
            log.warn("Collector is offline, can not dispatch collect jobs.");
            return;
        }
        WheelTimerTask timerJob = new WheelTimerTask(addJob);
        if (addJob.isCyclic()) {
            Timeout timeout = wheelTimer.newTimeout(timerJob, addJob.getInterval(), TimeUnit.SECONDS);
            currentCyclicTaskMap.put(addJob.getId(), timeout);
        } else {
            for (Metrics metric : addJob.getMetrics()) {
                metric.setInterval(0L);
            }
            addJob.setIntervals(new LinkedList<>(List.of(0L)));
            Timeout timeout = wheelTimer.newTimeout(timerJob, addJob.getInterval(), TimeUnit.SECONDS);
            currentTempTaskMap.put(addJob.getId(), timeout);
            eventListeners.put(addJob.getId(), eventListener);
        }
    }

    @Override
    public void cyclicJob(WheelTimerTask timerTask, long interval, TimeUnit timeUnit) {
        if (!this.started.get()) {
            log.warn("Collector is offline, can not dispatch collect jobs.");
            return;
        }
        Long jobId = timerTask.getJob().getId();
        // whether is the job has been canceled
        if (currentCyclicTaskMap.containsKey(jobId)) {
            Timeout timeout = wheelTimer.newTimeout(timerTask, interval, TimeUnit.SECONDS);
            currentCyclicTaskMap.put(timerTask.getJob().getId(), timeout);
        }
    }

    @Override
    public void deleteJob(long jobId, boolean isCyclic) {
        if (isCyclic) {
            Timeout timeout = currentCyclicTaskMap.remove(jobId);
            if (timeout != null) {
                timeout.cancel();
            }
        } else {
            Timeout timeout = currentTempTaskMap.remove(jobId);
            if (timeout != null) {
                timeout.cancel();
            }
        }
    }

    @Override
    public void goOnline() {
        currentCyclicTaskMap.forEach((key, value) -> value.cancel());
        currentCyclicTaskMap.clear();
        currentTempTaskMap.forEach((key, value) -> value.cancel());
        currentTempTaskMap.clear();
        started.set(true);
    }

    @Override
    public void goOffline() {
        started.set(false);
        currentCyclicTaskMap.forEach((key, value) -> value.cancel());
        currentCyclicTaskMap.clear();
        currentTempTaskMap.forEach((key, value) -> value.cancel());
        currentTempTaskMap.clear();
    }

    private Map<String, NetworkStats> lastNetworkStats;

    @Override
    public void responseSyncJobData(long jobId, List<CollectRep.MetricsData> metricsDataTemps) {
        currentTempTaskMap.remove(jobId);
        CollectResponseEventListener eventListener = eventListeners.remove(jobId);
        if (eventListener != null) {
            eventListener.response(metricsDataTemps);
        }
    }

    @Override
    public void destroy() throws Exception {
        this.wheelTimer.stop();
    }

    /**
     * 排除了回环接口（lo）和 Docker 虚拟接口（veth）
     * 返回相同格式的数据：rx_mbps（接收速率）和 tx_mbps（发送速率）
     * 使用相同的计算逻辑得出 MB/s 速率
     * 使用 /proc/net/dev 文件获取网络统计信息
     *
     * @return 网络使用情况
     */
    @Override
    public String getMetrics(String identity, String ip) {

        OperatingSystemMXBean osBean = (OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();
        // CPU使用率
        double cpuUsage = osBean.getCpuLoad() * 100;

        SystemInfo systemInfo = new SystemInfo();
        GlobalMemory memory = systemInfo.getHardware().getMemory();
        // 获取内存指标（单位：字节）
        long totalMemory = memory.getTotal();
        long availableMemory = memory.getAvailable();
        long usedMemory = totalMemory - availableMemory;
        double memoryUsage = ((double) usedMemory / totalMemory) * 100;

        // 网络使用情况
        Map<String, Double> networkUsage = getNetworkUsage();
        Map<String, Object> metrics = new HashMap<>();
        metrics.put("identity", identity);
        metrics.put("ip", ip);
        metrics.put("cpuUsage", cpuUsage);
        metrics.put("memUsage", memoryUsage);
        metrics.put("rxMbps", networkUsage.get("rx_mbps"));
        metrics.put("txMbps", networkUsage.get("tx_mbps"));
        metrics.put("timestamp", System.currentTimeMillis());
        return JsonUtil.toJson(metrics);
    }

    private static class NetworkStats {
        long bytesRecv;
        long bytesSent;

        public NetworkStats(long bytesRecv, long bytesSent) {
            this.bytesRecv = bytesRecv;
            this.bytesSent = bytesSent;
        }
    }

    private SystemInfo systemInfo = new SystemInfo();
    private long lastUpdateTime;

    private Map<String, Double> getNetworkUsage() {
        Map<String, Double> networkUsage = new HashMap<>();
        try {
            HardwareAbstractionLayer hal = systemInfo.getHardware();
            List<NetworkIF> networkIFs = hal.getNetworkIFs();

            // 第一次获取数据
            long totalRxBytes = 0;
            long totalTxBytes = 0;

            // 更新并获取第一次数据
            for (NetworkIF networkIF : networkIFs) {
                if (!isVirtualInterface(networkIF.getName())) {
                    networkIF.updateAttributes();
                    totalRxBytes += networkIF.getBytesRecv();
                    totalTxBytes += networkIF.getBytesSent();
                }
            }

            // 等待1秒
            Thread.sleep(1000);

            // 第二次获取数据
            long totalRxBytes1 = 0;
            long totalTxBytes1 = 0;

            // 更新并获取第二次数据
            for (NetworkIF networkIF : networkIFs) {
                if (!isVirtualInterface(networkIF.getName())) {
                    networkIF.updateAttributes();
                    totalRxBytes1 += networkIF.getBytesRecv();
                    totalTxBytes1 += networkIF.getBytesSent();
                }
            }

            // 计算速率（Mb/s）
            // (bytes/s) / (1024*1024/8) = Mb/s
            double rxSpeed = (totalRxBytes1 - totalRxBytes) * 8.0 / (1024 * 1024);
            double txSpeed = (totalTxBytes1 - totalTxBytes) * 8.0 / (1024 * 1024);

            // 保留两位小数
            rxSpeed = Math.round(rxSpeed * 100.0) / 100.0;
            txSpeed = Math.round(txSpeed * 100.0) / 100.0;

            networkUsage.put("rx_mbps", Math.max(0, rxSpeed));  // 接收速率 Mb/s
            networkUsage.put("tx_mbps", Math.max(0, txSpeed));  // 发送速率 Mb/s

        } catch (Exception e) {
            log.error("Failed to collect network metrics", e);
            networkUsage.put("rx_mbps", 0.0);
            networkUsage.put("tx_mbps", 0.0);
        }
        return networkUsage;
    }

    private boolean isVirtualInterface(String interfaceName) {
        return interfaceName.startsWith("docker") ||
                interfaceName.startsWith("br-") ||
                interfaceName.startsWith("veth") ||
                interfaceName.equals("lo");
    }
}
