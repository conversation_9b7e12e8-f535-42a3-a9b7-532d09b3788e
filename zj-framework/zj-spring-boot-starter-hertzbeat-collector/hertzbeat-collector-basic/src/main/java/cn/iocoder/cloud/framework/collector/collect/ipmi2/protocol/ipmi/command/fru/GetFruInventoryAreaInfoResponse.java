/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi.command.fru;

import cn.iocoder.cloud.framework.collector.collect.ipmi2.client.IpmiPacketContext;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi.IpmiCommandName;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi.command.AbstractIpmiResponse;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.utils.ByteConvertUtils;

import java.nio.ByteBuffer;

/**
 * Get FRU Inventory Area Info Response
 * See IPMIv2 Section 34.1
 */
public class GetFruInventoryAreaInfoResponse extends AbstractIpmiResponse {

    /**
     * FRU Inventory area size in bytes
     */
    public int fruInventoryAreaSize;

    /**
     * Device is accessed by bytes or words
     * 0 = device is accessed by bytes
     * 1 = device is accessed by 16-bit words
     */
    public boolean accessedByWords;

    @Override
    public void fromResponseData(IpmiPacketContext context, ByteBuffer buffer) {
        // First two bytes contain the FRU inventory area size (LSB first)
        byte lsByte = buffer.get();
        byte msByte = buffer.get();
        fruInventoryAreaSize = ByteConvertUtils.lsMsByteToInt(lsByte, msByte);
        
        // Third byte contains device access information
        byte accessByte = buffer.get();
        accessedByWords = (accessByte & 0x01) == 1;
    }

    @Override
    public IpmiCommandName getCommandName() {
        return IpmiCommandName.GetFruInventoryAreaInfo;
    }
}
