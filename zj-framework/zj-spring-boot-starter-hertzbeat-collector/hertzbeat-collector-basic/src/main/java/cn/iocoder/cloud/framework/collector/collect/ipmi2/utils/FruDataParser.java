/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.collector.collect.ipmi2.utils;

import lombok.extern.slf4j.Slf4j;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * FRU Data Parser for extracting storage/disk health information
 * Based on IPMI FRU Data Format Specification
 */
@Slf4j
public class FruDataParser {

    // FRU Area Type Codes
    private static final byte INTERNAL_USE_AREA = 0x00;
    private static final byte CHASSIS_INFO_AREA = 0x01;
    private static final byte BOARD_INFO_AREA = 0x02;
    private static final byte PRODUCT_INFO_AREA = 0x03;
    private static final byte MULTIRECORD_AREA = 0x04;

    // Type/Length byte format constants
    private static final int TYPE_LENGTH_MASK = 0xC0;
    private static final int LENGTH_MASK = 0x3F;
    private static final int TYPE_BINARY = 0x00;
    private static final int TYPE_BCD_PLUS = 0x40;
    private static final int TYPE_ASCII_6BIT = 0x80;
    private static final int TYPE_ASCII_8BIT = 0xC0;

    /**
     * Parse FRU Common Header
     */
    public static Map<String, Object> parseFruHeader(byte[] fruData) {
        Map<String, Object> headerInfo = new HashMap<>();
        
        if (fruData == null || fruData.length < 8) {
            log.warn("FRU data too short for header parsing");
            return headerInfo;
        }

        ByteBuffer buffer = ByteBuffer.wrap(fruData);
        
        // Format version (should be 1)
        byte formatVersion = buffer.get();
        headerInfo.put("format_version", formatVersion & 0x0F);
        
        // Area pointers (in 8-byte units, 0 = not present)
        headerInfo.put("internal_use_offset", (buffer.get() & 0xFF) * 8);
        headerInfo.put("chassis_info_offset", (buffer.get() & 0xFF) * 8);
        headerInfo.put("board_info_offset", (buffer.get() & 0xFF) * 8);
        headerInfo.put("product_info_offset", (buffer.get() & 0xFF) * 8);
        headerInfo.put("multirecord_offset", (buffer.get() & 0xFF) * 8);
        
        // PAD byte
        buffer.get();
        
        // Header checksum
        headerInfo.put("header_checksum", buffer.get() & 0xFF);
        
        return headerInfo;
    }

    /**
     * Parse Product Info Area for storage device information
     */
    public static Map<String, String> parseProductInfoArea(byte[] fruData, int offset) {
        Map<String, String> productInfo = new HashMap<>();
        
        if (fruData == null || offset <= 0 || offset >= fruData.length) {
            return productInfo;
        }

        try {
            ByteBuffer buffer = ByteBuffer.wrap(fruData, offset, fruData.length - offset);
            
            // Format version and length
            byte formatVersion = buffer.get();
            byte areaLength = buffer.get(); // in 8-byte units
            byte languageCode = buffer.get();
            
            productInfo.put("format_version", String.valueOf(formatVersion & 0x0F));
            productInfo.put("area_length", String.valueOf((areaLength & 0xFF) * 8));
            productInfo.put("language_code", String.valueOf(languageCode & 0xFF));
            
            // Parse string fields
            productInfo.put("manufacturer", parseTypeLength(buffer));
            productInfo.put("product_name", parseTypeLength(buffer));
            productInfo.put("part_number", parseTypeLength(buffer));
            productInfo.put("version", parseTypeLength(buffer));
            productInfo.put("serial_number", parseTypeLength(buffer));
            productInfo.put("asset_tag", parseTypeLength(buffer));
            
            // Parse custom fields until end marker (0xC1)
            int customFieldIndex = 1;
            while (buffer.hasRemaining()) {
                byte typeLengthByte = buffer.get(buffer.position());
                if (typeLengthByte == (byte) 0xC1) {
                    break; // End of data marker
                }
                String customField = parseTypeLength(buffer);
                if (!customField.isEmpty()) {
                    productInfo.put("custom_field_" + customFieldIndex, customField);
                    customFieldIndex++;
                }
            }
            
        } catch (Exception e) {
            log.error("Error parsing FRU Product Info Area: {}", e.getMessage());
        }
        
        return productInfo;
    }

    /**
     * Parse Board Info Area for manufacturing and health information
     */
    public static Map<String, String> parseBoardInfoArea(byte[] fruData, int offset) {
        Map<String, String> boardInfo = new HashMap<>();
        
        if (fruData == null || offset <= 0 || offset >= fruData.length) {
            return boardInfo;
        }

        try {
            ByteBuffer buffer = ByteBuffer.wrap(fruData, offset, fruData.length - offset);
            
            // Format version and length
            byte formatVersion = buffer.get();
            byte areaLength = buffer.get(); // in 8-byte units
            byte languageCode = buffer.get();
            
            boardInfo.put("format_version", String.valueOf(formatVersion & 0x0F));
            boardInfo.put("area_length", String.valueOf((areaLength & 0xFF) * 8));
            boardInfo.put("language_code", String.valueOf(languageCode & 0xFF));
            
            // Manufacturing date/time (3 bytes, minutes since 00:00 hrs 1/1/96)
            int mfgDateTime = 0;
            for (int i = 0; i < 3; i++) {
                mfgDateTime |= (buffer.get() & 0xFF) << (i * 8);
            }
            boardInfo.put("manufacturing_date", String.valueOf(mfgDateTime));
            
            // Parse string fields
            boardInfo.put("board_manufacturer", parseTypeLength(buffer));
            boardInfo.put("board_product_name", parseTypeLength(buffer));
            boardInfo.put("board_serial_number", parseTypeLength(buffer));
            boardInfo.put("board_part_number", parseTypeLength(buffer));
            
            // Parse custom fields
            int customFieldIndex = 1;
            while (buffer.hasRemaining()) {
                byte typeLengthByte = buffer.get(buffer.position());
                if (typeLengthByte == (byte) 0xC1) {
                    break; // End of data marker
                }
                String customField = parseTypeLength(buffer);
                if (!customField.isEmpty()) {
                    boardInfo.put("board_custom_field_" + customFieldIndex, customField);
                    customFieldIndex++;
                }
            }
            
        } catch (Exception e) {
            log.error("Error parsing FRU Board Info Area: {}", e.getMessage());
        }
        
        return boardInfo;
    }

    /**
     * Parse Type/Length encoded string
     */
    private static String parseTypeLength(ByteBuffer buffer) {
        if (!buffer.hasRemaining()) {
            return "";
        }
        
        byte typeLengthByte = buffer.get();
        int type = typeLengthByte & TYPE_LENGTH_MASK;
        int length = typeLengthByte & LENGTH_MASK;
        
        if (length == 0 || !buffer.hasRemaining() || buffer.remaining() < length) {
            return "";
        }
        
        byte[] data = new byte[length];
        buffer.get(data);
        
        switch (type) {
            case TYPE_ASCII_8BIT:
                return new String(data, StandardCharsets.US_ASCII).trim();
            case TYPE_ASCII_6BIT:
                return decode6BitAscii(data);
            case TYPE_BCD_PLUS:
                return decodeBcdPlus(data);
            case TYPE_BINARY:
            default:
                return bytesToHex(data);
        }
    }

    /**
     * Decode 6-bit ASCII packed string
     */
    private static String decode6BitAscii(byte[] data) {
        StringBuilder result = new StringBuilder();
        int bitBuffer = 0;
        int bitsInBuffer = 0;
        
        for (byte b : data) {
            bitBuffer |= (b & 0xFF) << bitsInBuffer;
            bitsInBuffer += 8;
            
            while (bitsInBuffer >= 6) {
                int char6bit = bitBuffer & 0x3F;
                result.append((char) (char6bit + 0x20)); // Add space offset
                bitBuffer >>= 6;
                bitsInBuffer -= 6;
            }
        }
        
        return result.toString().trim();
    }

    /**
     * Decode BCD plus string
     */
    private static String decodeBcdPlus(byte[] data) {
        StringBuilder result = new StringBuilder();
        
        for (byte b : data) {
            int high = (b >> 4) & 0x0F;
            int low = b & 0x0F;
            
            result.append(bcdDigitToChar(high));
            result.append(bcdDigitToChar(low));
        }
        
        return result.toString().trim();
    }

    private static char bcdDigitToChar(int digit) {
        if (digit >= 0 && digit <= 9) {
            return (char) ('0' + digit);
        } else if (digit == 0x0A) {
            return ' ';
        } else if (digit == 0x0B) {
            return '-';
        } else if (digit == 0x0C) {
            return '.';
        } else {
            return '?';
        }
    }

    /**
     * Convert byte array to hex string
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02X", b));
        }
        return result.toString();
    }

    /**
     * Extract storage health information from FRU data
     */
    public static Map<String, String> extractStorageHealthInfo(byte[] fruData) {
        Map<String, String> healthInfo = new HashMap<>();
        
        try {
            Map<String, Object> header = parseFruHeader(fruData);
            
            // Parse Product Info Area for device information
            int productOffset = (Integer) header.get("product_info_offset");
            if (productOffset > 0) {
                Map<String, String> productInfo = parseProductInfoArea(fruData, productOffset);
                healthInfo.putAll(productInfo);
            }
            
            // Parse Board Info Area for manufacturing information
            int boardOffset = (Integer) header.get("board_info_offset");
            if (boardOffset > 0) {
                Map<String, String> boardInfo = parseBoardInfoArea(fruData, boardOffset);
                healthInfo.putAll(boardInfo);
            }
            
            // Calculate estimated health status based on available information
            healthInfo.put("health_status", calculateHealthStatus(healthInfo));
            
        } catch (Exception e) {
            log.error("Error extracting storage health info: {}", e.getMessage());
            healthInfo.put("health_status", "Unknown");
            healthInfo.put("error", e.getMessage());
        }
        
        return healthInfo;
    }

    /**
     * Calculate health status based on available FRU information
     */
    private static String calculateHealthStatus(Map<String, String> fruInfo) {
        // This is a simplified health calculation
        // In real implementation, you would check specific health indicators
        
        if (fruInfo.containsKey("serial_number") && !fruInfo.get("serial_number").isEmpty()) {
            return "Good"; // Device is responding and has valid FRU data
        } else if (fruInfo.containsKey("product_name") && !fruInfo.get("product_name").isEmpty()) {
            return "Warning"; // Partial information available
        } else {
            return "Critical"; // No useful information available
        }
    }
}
