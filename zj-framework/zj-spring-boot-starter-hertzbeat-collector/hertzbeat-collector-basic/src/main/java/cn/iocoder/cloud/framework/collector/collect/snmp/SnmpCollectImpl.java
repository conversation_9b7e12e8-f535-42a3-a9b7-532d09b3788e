/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.collector.collect.snmp;

import cn.iocoder.cloud.framework.collector.collect.AbstractCollect;
import cn.iocoder.cloud.framework.collector.constants.CollectorConstants;
import cn.iocoder.cloud.framework.collector.dispatch.DispatchConstants;
import cn.iocoder.cloud.framework.collector.util.CollectUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.constants.CommonConstants;
import org.apache.hertzbeat.common.entity.job.Metrics;
import org.apache.hertzbeat.common.entity.job.protocol.SnmpProtocol;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.apache.hertzbeat.common.util.CommonUtil;
import org.snmp4j.PDU;
import org.snmp4j.Snmp;
import org.snmp4j.Target;
import org.snmp4j.fluent.SnmpBuilder;
import org.snmp4j.fluent.SnmpCompletableFuture;
import org.snmp4j.fluent.TargetBuilder;
import org.snmp4j.mp.MPv3;
import org.snmp4j.mp.SnmpConstants;
import org.snmp4j.security.*;
import org.snmp4j.smi.*;
import org.snmp4j.util.DefaultPDUFactory;
import org.snmp4j.util.TableEvent;
import org.snmp4j.util.TableUtils;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.Charset;
import java.nio.charset.CharsetDecoder;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;

/**
 * Snmp protocol collection implementation
 */
@Slf4j
public class SnmpCollectImpl extends AbstractCollect {

    private static final String AES128 = "1";
    private static final String SHA1 = "1";
    private static final String DEFAULT_PROTOCOL = "udp";
    private static final String OPERATION_GET = "get";
    private static final String OPERATION_WALK = "walk";
    private static final String HEX_SPLIT = ":";
    private static final String FORMAT_PATTERN =
            "{0,choice,0#|1#1 day, |1<{0,number,integer} days, }"
                    + "{1,choice,0#|1#1 hour, |1<{1,number,integer} hours, }"
                    + "{2,choice,0#|1#1 minute, |1<{2,number,integer} minutes, }"
                    + "{3,choice,0#|1#1 second, |1<{3,number,integer} seconds }";

    private final Map<Integer, Snmp> versionSnmpService = new ConcurrentHashMap<>(3);

    @Override
    public void preCheck(Metrics metrics) throws IllegalArgumentException {
        if (metrics == null || metrics.getSnmp() == null) {
            throw new IllegalArgumentException("Snmp collect must has snmp params");
        }
        SnmpProtocol snmpProtocol = metrics.getSnmp();
        Assert.hasText(snmpProtocol.getHost(), "snmp host is required.");
        Assert.hasText(snmpProtocol.getPort(), "snmp port is required.");
        Assert.notNull(snmpProtocol.getVersion(), "snmp version is required.");
    }

    @Override
    public void collect(CollectRep.MetricsData.Builder builder, long monitorId, String app, Metrics metrics) {
        long startTime = System.currentTimeMillis();
        SnmpProtocol snmpProtocol = metrics.getSnmp();
        int timeout = CollectUtil.getTimeout(snmpProtocol.getTimeout());
        int snmpVersion = getSnmpVersion(snmpProtocol.getVersion());
        Snmp snmpService = null;
        try {
            SnmpBuilder snmpBuilder = new SnmpBuilder();
            snmpService = getSnmpService(snmpVersion, snmpBuilder);
            
            // 确保SNMP服务正在监听
            try {
                snmpService.listen();
            } catch (IOException e) {
                log.warn("[snmp] Failed to start listening, may already be listening: {}", e.getMessage());
            }
            
            Target<?> target;
            Address targetAddress = GenericAddress.parse(DEFAULT_PROTOCOL + ":" + snmpProtocol.getHost()
                    + "/" + snmpProtocol.getPort());
            TargetBuilder<?> targetBuilder = snmpBuilder.target(targetAddress);
            if (snmpVersion == SnmpConstants.version3) {
                TargetBuilder.PrivProtocol privatePasswordEncryption = getPrivPasswordEncryption(snmpProtocol.getPrivPasswordEncryption());
                TargetBuilder.AuthProtocol authPasswordEncryption = getAuthPasswordEncryption(snmpProtocol.getAuthPasswordEncryption());
                target = targetBuilder
                        .user(snmpProtocol.getUsername())
                        .auth(authPasswordEncryption).authPassphrase(snmpProtocol.getAuthPassphrase())
                        .priv(privatePasswordEncryption).privPassphrase(snmpProtocol.getPrivPassphrase())
                        .done()
                        .timeout(timeout).retries(3)
                        .build();
                USM usm = new USM(SecurityProtocols.getInstance(), new OctetString(MPv3.createLocalEngineID()), 0);
                SecurityModels.getInstance().addSecurityModel(usm);
                snmpService.getUSM().addUser(
                        new OctetString(snmpProtocol.getUsername()),
                        new UsmUser(new OctetString(snmpProtocol.getUsername()),
                                AuthMD5.ID,
                                new OctetString(snmpProtocol.getAuthPassphrase()),
                                PrivDES.ID,
                                new OctetString(snmpProtocol.getPrivPassphrase()))
                );
            } else if (snmpVersion == SnmpConstants.version1) {
                target = targetBuilder
                        .v1()
                        .community(new OctetString(snmpProtocol.getCommunity()))
                        .timeout(timeout).retries(3)
                        .build();
                target.setSecurityModel(SecurityModel.SECURITY_MODEL_SNMPv1);
            } else {
                target = targetBuilder
                        .v2c()
                        .community(new OctetString(snmpProtocol.getCommunity()))
                        .timeout(timeout).retries(3)
                        .build();
                target.setSecurityModel(SecurityModel.SECURITY_MODEL_SNMPv2c);
            }
            String operation = snmpProtocol.getOperation();
            operation = StringUtils.hasText(operation) ? operation : OPERATION_GET;
            if (OPERATION_GET.equalsIgnoreCase(operation)) {
                String contextName = getContextName(snmpProtocol.getContextName());
                PDU pdu = targetBuilder.pdu().type(PDU.GET).oids(snmpProtocol.getOids().values().toArray(new String[0])).contextName(contextName).build();
                SnmpCompletableFuture snmpRequestFuture = SnmpCompletableFuture.send(snmpService, target, pdu);
                List<VariableBinding> vbs = snmpRequestFuture.get().getAll();
                long responseTime = System.currentTimeMillis() - startTime;
                Map<String, String> oidsMap = snmpProtocol.getOids();
                Map<String, String> oidsValueMap = new HashMap<>(oidsMap.size());
                for (VariableBinding binding : vbs) {
                    if (binding == null) {
                        continue;
                    }
                    Variable variable = binding.getVariable();
                    if (variable instanceof TimeTicks timeTicks) {
                        String value = formatTimeTicks(timeTicks, binding.toValueString());
                        oidsValueMap.put(binding.getOid().toDottedString(), value);
                    } else {
                        oidsValueMap.put(binding.getOid().toDottedString(), binding.toValueString());
                    }
                }
                CollectRep.ValueRow.Builder valueRowBuilder = CollectRep.ValueRow.newBuilder();
                for (String alias : metrics.getAliasFields()) {
                    if (CollectorConstants.RESPONSE_TIME.equalsIgnoreCase(alias)) {
                        valueRowBuilder.addColumns(Long.toString(responseTime));
                    } else {
                        String oid = oidsMap.get(alias);
                        String value = oidsValueMap.get(oid);
                        valueRowBuilder.addColumns(Objects.requireNonNullElse(value, CommonConstants.NULL_VALUE));
                    }
                }
                builder.addValues(valueRowBuilder.build());
            } else if (OPERATION_WALK.equalsIgnoreCase(operation)) {
                Map<String, String> oidMap = snmpProtocol.getOids();
                Assert.notEmpty(oidMap, "snmp oids is required when operation is walk.");
                TableUtils tableUtils = new TableUtils(snmpService, new DefaultPDUFactory(PDU.GETBULK));
                OID[] oids = oidMap.values().stream().map(OID::new).toArray(OID[]::new);
                List<TableEvent> tableEvents = tableUtils.getTable(target, oids, null, null);
                Assert.notNull(tableEvents, "snmp walk response empty content.");
                long responseTime = System.currentTimeMillis() - startTime;
                for (TableEvent tableEvent : tableEvents) {
                    if (tableEvent == null || tableEvent.isError()) {
                        continue;
                    }
                    VariableBinding[] varBindings = tableEvent.getColumns();
                    Map<String, String> oidsValueMap = new HashMap<>(varBindings.length);
                    for (VariableBinding binding : varBindings) {
                        if (binding == null) {
                            continue;
                        }
                        Variable variable = binding.getVariable();
                        if (variable instanceof TimeTicks timeTicks) {
                            String value = formatTimeTicks(timeTicks, binding.toValueString());
                            oidsValueMap.put(binding.getOid().trim().toDottedString(), value);
                        } else {
                            oidsValueMap.put(binding.getOid().trim().toDottedString(), bingdingHexValueToString(binding));
                        }
                    }
                    // when too many empty value field, ignore
                    if (oidsValueMap.size() < metrics.getAliasFields().size() / 2) {
                        continue;
                    }
                    CollectRep.ValueRow.Builder valueRowBuilder = CollectRep.ValueRow.newBuilder();
                    for (String alias : metrics.getAliasFields()) {
                        if (CollectorConstants.RESPONSE_TIME.equalsIgnoreCase(alias)) {
                            valueRowBuilder.addColumns(Long.toString(responseTime));
                        } else {
                            String oid = oidMap.get(alias);
                            String value = oidsValueMap.get(oid);
                            if (value == null) {
                                // get leaf
                                for (String key : oidsValueMap.keySet()) {
                                    if (key.startsWith(oid)) {
                                        value = oidsValueMap.get(key);
                                        break;
                                    }
                                }
                            }
                            valueRowBuilder.addColumns(Objects.requireNonNullElse(value, CommonConstants.NULL_VALUE));
                        }
                    }
                    builder.addValues(valueRowBuilder.build());
                }
            }
        } catch (ExecutionException | InterruptedException ex) {
            String errorMsg = CommonUtil.getMessageFromThrowable(ex);
            log.warn("[snmp collect] Host: {}, Port: {}, Version: {}, Error: {}", 
                     snmpProtocol.getHost(), snmpProtocol.getPort(), snmpProtocol.getVersion(), errorMsg);
            builder.setCode(CollectRep.Code.UN_CONNECTABLE);
            builder.setMsg(errorMsg);
        } catch (Exception e) {
            String errorMsg = CommonUtil.getMessageFromThrowable(e);
            log.warn("[snmp collect] Host: {}, Port: {}, Version: {}, Error: {}", 
                     snmpProtocol.getHost(), snmpProtocol.getPort(), snmpProtocol.getVersion(), errorMsg, e);
            builder.setCode(CollectRep.Code.FAIL);
            builder.setMsg(errorMsg);
        } finally {
            if (snmpService != null) {
                if (snmpVersion == SnmpConstants.version3) {
                    try {
                        snmpClose(snmpService, SnmpConstants.version3);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        }
    }

    @Override
    public String supportProtocol() {
        return DispatchConstants.PROTOCOL_SNMP;
    }

    private synchronized Snmp getSnmpService(int snmpVersion, SnmpBuilder snmpBuilder) throws IOException {
        Snmp snmpService = versionSnmpService.get(snmpVersion);
        if (snmpService != null) {
            // 简化健康检查 - 直接检查SNMP对象状态
            try {
                // 检查SNMP服务是否还有效，如果有异常则重新创建
                if (snmpService.getMessageDispatcher() == null) {
                    log.warn("[snmp] SNMP service invalid, recreating for version: {}", snmpVersion);
                    snmpClose(snmpService, snmpVersion);
                    snmpService = null;
                }
            } catch (Exception e) {
                log.warn("[snmp] SNMP service health check failed, recreating for version {}: {}", snmpVersion, e.getMessage());
                try {
                    snmpClose(snmpService, snmpVersion);
                } catch (IOException ioException) {
                    log.warn("[snmp] Error closing invalid SNMP service: {}", ioException.getMessage());
                }
                snmpService = null;
            }
        }
        
        if (snmpService == null) {
            if (snmpVersion == SnmpConstants.version3) {
                snmpService = snmpBuilder.udp().v3().securityProtocols(SecurityProtocols.SecurityProtocolSet.maxCompatibility).usm().threads(4).build();
            } else if (snmpVersion == SnmpConstants.version1) {
                snmpService = snmpBuilder.udp().v1().threads(4).build();
            } else {
                snmpService = snmpBuilder.udp().v2c().threads(4).build();
            }
            versionSnmpService.put(snmpVersion, snmpService);
            log.info("[snmp] Created new SNMP service for version: {}", snmpVersion);
        }
        return snmpService;
    }

    private int getSnmpVersion(String snmpVersion) {
        int version = SnmpConstants.version2c;
        if (!StringUtils.hasText(snmpVersion)) {
            return version;
        }
        if (snmpVersion.equalsIgnoreCase(String.valueOf(SnmpConstants.version1))
                || snmpVersion.equalsIgnoreCase(TargetBuilder.SnmpVersion.v1.name())) {
            return SnmpConstants.version1;
        }
        if (snmpVersion.equalsIgnoreCase(String.valueOf(SnmpConstants.version3))
                || snmpVersion.equalsIgnoreCase(TargetBuilder.SnmpVersion.v3.name())) {
            return SnmpConstants.version3;
        }
        return version;
    }

    private String bingdingHexValueToString(VariableBinding binding) {
        // whether if binding is hex
        String hexString = binding.toValueString();
        if (hexString.contains(HEX_SPLIT)) {
            // 判断是否是 MAC 地址格式（形如 XX-XX-XX-XX-XX-XX）
            String macRegex = "([0-9A-Fa-f]{2}" + HEX_SPLIT + "){5}[0-9A-Fa-f]{2}";
            if (hexString.matches(macRegex)) {
                return hexString;
            }
            try {
                String clearHexStr = hexString.replace(HEX_SPLIT, "");
                byte[] bytes = HexFormat.of().parseHex(clearHexStr);
                CharsetDecoder decoder = Charset.forName("GB2312").newDecoder();
                try {
                    CharBuffer res = decoder.decode(ByteBuffer.wrap(bytes));
                    return res.toString();
                } catch (Exception e) {
                    return new String(bytes);
                }
            } catch (Exception e) {
                return hexString;
            }
        } else {
            return hexString;
        }
    }

    private void snmpClose(Snmp snmp, int version) throws IOException {
        snmp.close();
        versionSnmpService.remove(version);
    }

    private TargetBuilder.PrivProtocol getPrivPasswordEncryption(String privPasswordEncryption) {
        if (privPasswordEncryption == null) {
            return TargetBuilder.PrivProtocol.des;
        } else if (AES128.equals(privPasswordEncryption)) {
            return TargetBuilder.PrivProtocol.aes128;
        } else {
            return TargetBuilder.PrivProtocol.des;
        }
    }

    private TargetBuilder.AuthProtocol getAuthPasswordEncryption(String authPasswordEncryption) {
        if (authPasswordEncryption == null) {
            return TargetBuilder.AuthProtocol.md5;
        } else if (SHA1.equals(authPasswordEncryption)) {
            return TargetBuilder.AuthProtocol.sha1;
        } else {
            return TargetBuilder.AuthProtocol.md5;
        }
    }

    private String getContextName(String contextName) {
        return contextName == null ? "" : contextName;
    }

    /**
     * 格式化TimeTicks，处理不同设备返回的格式差异
     */
    private String formatTimeTicks(TimeTicks timeTicks, String originalValue) {
        try {
            // 首先尝试使用标准的FORMAT_PATTERN格式化
            String formattedValue = timeTicks.toString(FORMAT_PATTERN);
            if (StringUtils.hasText(formattedValue) && !formattedValue.contains("iso.") && !formattedValue.contains("=")) {
                return formattedValue;
            }
        } catch (Exception e) {
            log.debug("[snmp] Standard TimeTicks formatting failed: {}", e.getMessage());
        }

        try {
            // 如果标准格式化失败，尝试解析原始值
            return parseTimeTicksFromString(originalValue);
        } catch (Exception e) {
            log.warn("[snmp] Failed to parse TimeTicks from original value: {}, error: {}", originalValue, e.getMessage());
            return originalValue; // 返回原始值作为fallback
        }
    }

    /**
     * 从字符串中解析TimeTicks值
     * 处理格式如: "iso.3.6.1.2.1.1.3.0 = Timeticks: (956430468) 110 days, 16:45:04.68"
     */
    private String parseTimeTicksFromString(String originalValue) {
        if (!StringUtils.hasText(originalValue)) {
            return originalValue;
        }

        // 如果包含完整的SNMP响应格式，提取时间部分
        if (originalValue.contains("Timeticks:") || originalValue.contains("TimeTicks:")) {
            // 查找时间描述部分（在括号后面）
            int parenEnd = originalValue.lastIndexOf(')');
            if (parenEnd > 0 && parenEnd < originalValue.length() - 1) {
                String timeDescription = originalValue.substring(parenEnd + 1).trim();
                
                // 移除可能的前缀（如空格）
                if (timeDescription.startsWith(" ")) {
                    timeDescription = timeDescription.substring(1);
                }
                
                // 标准化时间格式
                return standardizeTimeDescription(timeDescription);
            }
            
            // 如果找不到括号，尝试提取数字并计算
            String ticksStr = extractTicksNumber(originalValue);
            if (StringUtils.hasText(ticksStr)) {
                try {
                    long ticks = Long.parseLong(ticksStr);
                    return convertTicksToTimeString(ticks);
                } catch (NumberFormatException e) {
                    log.debug("[snmp] Failed to parse ticks number: {}", ticksStr);
                }
            }
        }

        return originalValue;
    }

    /**
     * 标准化时间描述格式
     */
    private String standardizeTimeDescription(String timeDescription) {
        if (!StringUtils.hasText(timeDescription)) {
            return timeDescription;
        }
        
        // 将 "110 days, 16:45:04.68" 格式转换为标准格式
        if (timeDescription.matches("\\d+\\s+days?,\\s+\\d+:\\d+:\\d+.*")) {
            String[] parts = timeDescription.split(",");
            if (parts.length >= 2) {
                String daysPart = parts[0].trim(); // "110 days"
                String timePart = parts[1].trim(); // "16:45:04.68"
                
                // 解析时分秒
                String[] timeParts = timePart.split(":");
                if (timeParts.length >= 3) {
                    try {
                        int hours = Integer.parseInt(timeParts[0]);
                        int minutes = Integer.parseInt(timeParts[1]);
                        int seconds = (int) Double.parseDouble(timeParts[2]); // 处理可能的小数
                        
                        StringBuilder result = new StringBuilder();
                        result.append(daysPart);
                        
                        if (hours > 0) {
                            result.append(", ").append(hours).append(hours == 1 ? " hour" : " hours");
                        }
                        if (minutes > 0) {
                            result.append(", ").append(minutes).append(minutes == 1 ? " minute" : " minutes");
                        }
                        if (seconds > 0) {
                            result.append(", ").append(seconds).append(seconds == 1 ? " second" : " seconds");
                        }
                        
                        return result.toString();
                    } catch (NumberFormatException e) {
                        log.debug("[snmp] Failed to parse time components from: {}", timePart);
                    }
                }
            }
        }
        
        return timeDescription;
    }

    /**
     * 从字符串中提取ticks数字
     */
    private String extractTicksNumber(String input) {
        // 查找括号中的数字
        int start = input.indexOf('(');
        int end = input.indexOf(')', start);
        if (start >= 0 && end > start) {
            return input.substring(start + 1, end).trim();
        }
        return null;
    }

    /**
     * 将ticks转换为时间字符串
     */
    private String convertTicksToTimeString(long ticks) {
        // TimeTicks 单位是 1/100 秒
        long totalSeconds = ticks / 100;
        
        long days = totalSeconds / (24 * 3600);
        long hours = (totalSeconds % (24 * 3600)) / 3600;
        long minutes = (totalSeconds % 3600) / 60;
        long seconds = totalSeconds % 60;
        
        StringBuilder result = new StringBuilder();
        
        if (days > 0) {
            result.append(days).append(days == 1 ? " day" : " days");
        }
        if (hours > 0) {
            if (result.length() > 0) result.append(", ");
            result.append(hours).append(hours == 1 ? " hour" : " hours");
        }
        if (minutes > 0) {
            if (result.length() > 0) result.append(", ");
            result.append(minutes).append(minutes == 1 ? " minute" : " minutes");
        }
        if (seconds > 0) {
            if (result.length() > 0) result.append(", ");
            result.append(seconds).append(seconds == 1 ? " second" : " seconds");
        }
        
        return result.length() > 0 ? result.toString() : "0 seconds";
    }
}
