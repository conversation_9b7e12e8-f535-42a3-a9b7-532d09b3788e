/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi.command.fru;

import cn.iocoder.cloud.framework.collector.collect.ipmi2.client.IpmiPacketContext;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi.IpmiCommandName;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi.command.AbstractIpmiRequest;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.utils.ByteConvertUtils;

import java.nio.ByteBuffer;

/**
 * Read FRU Data Request
 * See IPMIv2 Section 34.2
 */
public class ReadFruDataRequest extends AbstractIpmiRequest {

    private final byte fruDeviceId;
    private final int fruInventoryOffset;
    private final byte countToRead;

    public ReadFruDataRequest(byte fruDeviceId, int fruInventoryOffset, byte countToRead) {
        this.fruDeviceId = fruDeviceId;
        this.fruInventoryOffset = fruInventoryOffset;
        this.countToRead = countToRead;
    }

    @Override
    public int getDataWireLength(IpmiPacketContext context) {
        return 4; // 1 byte device ID + 2 bytes offset + 1 byte count
    }

    @Override
    public void toWireData(IpmiPacketContext context, ByteBuffer buffer) {
        buffer.put(fruDeviceId);
        
        // FRU inventory offset (LSB first)
        byte[] offsetBytes = ByteConvertUtils.intToLsMsByte(fruInventoryOffset);
        buffer.put(offsetBytes[0]);
        buffer.put(offsetBytes[1]);
        
        buffer.put(countToRead);
    }

    @Override
    public IpmiCommandName getCommandName() {
        return IpmiCommandName.ReadFruData;
    }
}
