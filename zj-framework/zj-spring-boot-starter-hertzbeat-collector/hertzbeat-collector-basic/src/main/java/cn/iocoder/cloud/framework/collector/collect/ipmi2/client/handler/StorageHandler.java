/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.collector.collect.ipmi2.client.handler;

import cn.iocoder.cloud.framework.collector.collect.ipmi2.client.IpmiSession;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.client.UdpConnection;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi.command.fru.GetFruInventoryAreaInfoRequest;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi.command.fru.GetFruInventoryAreaInfoResponse;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi.command.fru.ReadFruDataRequest;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi.command.fru.ReadFruDataResponse;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.utils.FruDataParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.constants.CommonConstants;
import org.apache.hertzbeat.common.entity.job.Metrics;
import org.apache.hertzbeat.common.entity.message.CollectRep;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * IPMI Storage Handler for collecting disk/storage health information
 * Uses FRU (Field Replaceable Unit) data to extract storage device information
 */
@Slf4j
public class StorageHandler implements IpmiHandler {

    // Common FRU device IDs for storage devices
    private static final byte[] STORAGE_FRU_DEVICE_IDS = {0x00, 0x01, 0x02, 0x03, 0x04, 0x05};
    
    // Maximum FRU data size to read (in bytes)
    private static final int MAX_FRU_DATA_SIZE = 1024;
    
    // Read chunk size for FRU data
    private static final byte FRU_READ_CHUNK_SIZE = 16;

    @Override
    public void handler(IpmiSession session, UdpConnection connection, CollectRep.MetricsData.Builder builder, Metrics metrics) throws IOException {
        log.debug("Starting storage health data collection via IPMI FRU");
        
        try {
            // Scan for storage devices using FRU device IDs
            for (byte fruDeviceId : STORAGE_FRU_DEVICE_IDS) {
                try {
                    Map<String, String> storageInfo = collectStorageDeviceInfo(session, connection, fruDeviceId);
                    if (!storageInfo.isEmpty()) {
                        addStorageMetrics(builder, metrics, storageInfo, fruDeviceId);
                    }
                } catch (Exception e) {
                    log.debug("No storage device found at FRU ID {}: {}", fruDeviceId, e.getMessage());
                    // Continue scanning other device IDs
                }
            }
            
            // If no storage devices found, add a default entry indicating no devices
            if (builder.getValuesCount() == 0) {
                addNoStorageDeviceEntry(builder, metrics);
            }
            
        } catch (Exception e) {
            log.error("Error collecting storage health information: {}", e.getMessage());
            addErrorEntry(builder, metrics, e.getMessage());
        }
    }

    /**
     * Collect storage device information from a specific FRU device ID
     */
    private Map<String, String> collectStorageDeviceInfo(IpmiSession session, UdpConnection connection, byte fruDeviceId) throws IOException {
        Map<String, String> deviceInfo = new HashMap<>();
        
        // Step 1: Get FRU Inventory Area Info
        GetFruInventoryAreaInfoRequest areaInfoRequest = new GetFruInventoryAreaInfoRequest(fruDeviceId);
        GetFruInventoryAreaInfoResponse areaInfoResponse = connection.get(session, areaInfoRequest, GetFruInventoryAreaInfoResponse.class);
        
        if (areaInfoResponse.fruInventoryAreaSize <= 0) {
            log.debug("No FRU inventory area found for device ID: {}", fruDeviceId);
            return deviceInfo;
        }
        
        log.debug("Found FRU inventory area of size {} bytes for device ID: {}", 
                 areaInfoResponse.fruInventoryAreaSize, fruDeviceId);
        
        // Step 2: Read FRU Data
        byte[] fruData = readFruData(session, connection, fruDeviceId, areaInfoResponse.fruInventoryAreaSize);
        
        if (fruData.length == 0) {
            log.debug("No FRU data available for device ID: {}", fruDeviceId);
            return deviceInfo;
        }
        
        // Step 3: Parse FRU Data for storage health information
        Map<String, String> healthInfo = FruDataParser.extractStorageHealthInfo(fruData);
        
        // Step 4: Add device-specific information
        deviceInfo.putAll(healthInfo);
        deviceInfo.put("fru_device_id", String.valueOf(fruDeviceId));
        deviceInfo.put("fru_data_size", String.valueOf(fruData.length));
        deviceInfo.put("access_method", areaInfoResponse.accessedByWords ? "16-bit words" : "bytes");
        
        // Step 5: Calculate additional health metrics
        calculateAdditionalHealthMetrics(deviceInfo);
        
        return deviceInfo;
    }

    /**
     * Read FRU data from the device
     */
    private byte[] readFruData(IpmiSession session, UdpConnection connection, byte fruDeviceId, int totalSize) throws IOException {
        int sizeToRead = Math.min(totalSize, MAX_FRU_DATA_SIZE);
        byte[] fruData = new byte[sizeToRead];
        int offset = 0;
        
        while (offset < sizeToRead) {
            int remainingBytes = sizeToRead - offset;
            byte chunkSize = (byte) Math.min(FRU_READ_CHUNK_SIZE, remainingBytes);
            
            ReadFruDataRequest readRequest = new ReadFruDataRequest(fruDeviceId, offset, chunkSize);
            ReadFruDataResponse readResponse = connection.get(session, readRequest, ReadFruDataResponse.class);
            
            if (readResponse.countReturned <= 0 || readResponse.requestedData == null) {
                log.warn("No data returned for FRU device {} at offset {}", fruDeviceId, offset);
                break;
            }
            
            int bytesToCopy = Math.min(readResponse.countReturned, remainingBytes);
            System.arraycopy(readResponse.requestedData, 0, fruData, offset, bytesToCopy);
            offset += bytesToCopy;
            
            if (readResponse.countReturned < chunkSize) {
                // End of data reached
                break;
            }
        }
        
        // Return only the portion that was actually read
        if (offset < fruData.length) {
            byte[] actualData = new byte[offset];
            System.arraycopy(fruData, 0, actualData, 0, offset);
            return actualData;
        }
        
        return fruData;
    }

    /**
     * Calculate additional health metrics based on available information
     */
    private void calculateAdditionalHealthMetrics(Map<String, String> deviceInfo) {
        // Calculate device age if manufacturing date is available
        if (deviceInfo.containsKey("manufacturing_date")) {
            try {
                long mfgMinutes = Long.parseLong(deviceInfo.get("manufacturing_date"));
                // Convert from minutes since 1/1/96 to approximate age in days
                long currentMinutes = System.currentTimeMillis() / (1000 * 60);
                long baseMinutes = 788918400L; // Minutes from epoch to 1/1/96
                long deviceAgeMinutes = currentMinutes - (baseMinutes + mfgMinutes);
                long deviceAgeDays = deviceAgeMinutes / (60 * 24);
                
                deviceInfo.put("device_age_days", String.valueOf(Math.max(0, deviceAgeDays)));
                
                // Estimate lifespan based on age (simplified calculation)
                if (deviceAgeDays > 1825) { // > 5 years
                    deviceInfo.put("estimated_lifespan", "End of Life");
                } else if (deviceAgeDays > 1095) { // > 3 years
                    deviceInfo.put("estimated_lifespan", "Aging");
                } else {
                    deviceInfo.put("estimated_lifespan", "Good");
                }
            } catch (NumberFormatException e) {
                log.debug("Could not parse manufacturing date: {}", deviceInfo.get("manufacturing_date"));
                deviceInfo.put("estimated_lifespan", "Unknown");
            }
        } else {
            deviceInfo.put("estimated_lifespan", "Unknown");
        }
        
        // Set device type based on product name
        String productName = deviceInfo.getOrDefault("product_name", "").toLowerCase();
        if (productName.contains("disk") || productName.contains("drive") || productName.contains("ssd") || productName.contains("hdd")) {
            deviceInfo.put("device_type", "Storage Device");
        } else if (!productName.isEmpty()) {
            deviceInfo.put("device_type", "Hardware Component");
        } else {
            deviceInfo.put("device_type", "Unknown");
        }
    }

    /**
     * Add storage metrics to the builder
     */
    private void addStorageMetrics(CollectRep.MetricsData.Builder builder, Metrics metrics, Map<String, String> storageInfo, byte deviceId) {
        CollectRep.ValueRow.Builder valueRowBuilder = CollectRep.ValueRow.newBuilder();
        
        for (Metrics.Field field : metrics.getFields()) {
            String fieldName = field.getField();
            String value = storageInfo.getOrDefault(fieldName, CommonConstants.NULL_VALUE);
            valueRowBuilder.addColumns(value);
        }
        
        builder.addValues(valueRowBuilder.build());
        log.debug("Added storage metrics for FRU device ID: {}", deviceId);
    }

    /**
     * Add entry when no storage devices are found
     */
    private void addNoStorageDeviceEntry(CollectRep.MetricsData.Builder builder, Metrics metrics) {
        CollectRep.ValueRow.Builder valueRowBuilder = CollectRep.ValueRow.newBuilder();
        
        for (Metrics.Field field : metrics.getFields()) {
            String value;
            switch (field.getField()) {
                case "health_status":
                    value = "No Storage Devices Found";
                    break;
                case "device_type":
                    value = "None";
                    break;
                default:
                    value = CommonConstants.NULL_VALUE;
                    break;
            }
            valueRowBuilder.addColumns(value);
        }
        
        builder.addValues(valueRowBuilder.build());
        log.info("No storage devices found via IPMI FRU scan");
    }

    /**
     * Add error entry when collection fails
     */
    private void addErrorEntry(CollectRep.MetricsData.Builder builder, Metrics metrics, String errorMessage) {
        CollectRep.ValueRow.Builder valueRowBuilder = CollectRep.ValueRow.newBuilder();
        
        for (Metrics.Field field : metrics.getFields()) {
            String value;
            switch (field.getField()) {
                case "health_status":
                    value = "Error: " + errorMessage;
                    break;
                case "device_type":
                    value = "Error";
                    break;
                default:
                    value = CommonConstants.NULL_VALUE;
                    break;
            }
            valueRowBuilder.addColumns(value);
        }
        
        builder.addValues(valueRowBuilder.build());
        log.error("Added error entry for storage collection: {}", errorMessage);
    }
}
