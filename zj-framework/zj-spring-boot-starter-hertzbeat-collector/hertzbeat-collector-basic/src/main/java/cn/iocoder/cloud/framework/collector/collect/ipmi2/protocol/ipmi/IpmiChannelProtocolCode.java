/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi;

import cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.common.IpmiCode;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.utils.ByteConvertUtils;

/**
 *  See IPMIv2 Section 6.4
 */
public enum  IpmiChannelProtocolCode implements IpmiCode.Code {
    IPMB_v1_0(0x01),
    ICMB_v1_0(0x02),
    IPMI_SMBus(0x04),
    KCS(0x05),
    SMIC(0x06),
    BT_10(0x07),
    BT_15(0x08),
    TMode(0x09),
    OEM1(0x1C),
    OEM2(0x1D),
    OEM3(0x1E),
    OEM4(0x1F);


    private final byte code;
    public static final int MASK = 0xFF;

    private IpmiChannelProtocolCode(int code) {
        this.code = ByteConvertUtils.checkCastByte(code);
    }

    @Override
    public byte getCode() {
        return code;
    }

}
