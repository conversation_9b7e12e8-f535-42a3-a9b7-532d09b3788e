/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi.command.fru;

import cn.iocoder.cloud.framework.collector.collect.ipmi2.client.IpmiPacketContext;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi.IpmiCommandName;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi.command.AbstractIpmiResponse;

import java.nio.ByteBuffer;

/**
 * Read FRU Data Response
 * See IPMIv2 Section 34.2
 */
public class ReadFruDataResponse extends AbstractIpmiResponse {

    /**
     * Count of bytes returned
     */
    public byte countReturned;

    /**
     * Requested data
     */
    public byte[] requestedData;

    @Override
    public void fromResponseData(IpmiPacketContext context, ByteBuffer buffer) {
        // First byte is the count of bytes returned
        countReturned = buffer.get();
        
        // Remaining bytes are the requested data
        int dataLength = Byte.toUnsignedInt(countReturned);
        if (dataLength > 0 && buffer.remaining() >= dataLength) {
            requestedData = new byte[dataLength];
            buffer.get(requestedData);
        } else {
            requestedData = new byte[0];
        }
    }

    @Override
    public IpmiCommandName getCommandName() {
        return IpmiCommandName.ReadFruData;
    }
}
