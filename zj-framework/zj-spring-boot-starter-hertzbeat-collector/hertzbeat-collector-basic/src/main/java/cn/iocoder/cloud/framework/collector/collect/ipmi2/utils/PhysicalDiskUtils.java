/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.collector.collect.ipmi2.utils;

import cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi.command.sdr.GetSdrResponse;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi.command.sdr.code.IpmiSensorTypeCode;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Utility class for physical disk information processing and analysis
 */
@Slf4j
public class PhysicalDiskUtils {

    // Patterns for disk identification
    private static final Pattern DISK_NAME_PATTERN = Pattern.compile("(?i)(disk|drive|hdd|ssd|nvme)\\s*(\\d+)", Pattern.CASE_INSENSITIVE);
    private static final Pattern CAPACITY_PATTERN = Pattern.compile("(\\d+(?:\\.\\d+)?)\\s*(TB|GB|MB)", Pattern.CASE_INSENSITIVE);
    private static final Pattern RPM_PATTERN = Pattern.compile("(\\d+)\\s*RPM", Pattern.CASE_INSENSITIVE);
    
    // Disk type keywords
    private static final String[] SSD_KEYWORDS = {"ssd", "solid", "flash", "nvme"};
    private static final String[] HDD_KEYWORDS = {"hdd", "hard", "mechanical", "spinning"};
    private static final String[] NVME_KEYWORDS = {"nvme", "pcie", "m.2"};
    
    // Interface type keywords
    private static final String[] SATA_KEYWORDS = {"sata", "serial ata"};
    private static final String[] SAS_KEYWORDS = {"sas", "serial attached scsi"};
    private static final String[] PCIE_KEYWORDS = {"pcie", "pci express", "nvme"};

    /**
     * Analyze disk information from sensor data
     */
    public static Map<String, String> analyzeDiskInfo(GetSdrResponse sdrResponse, double sensorReading) {
        Map<String, String> diskInfo = new HashMap<>();
        
        if (sdrResponse == null) {
            return diskInfo;
        }
        
        // Extract disk name and index
        String diskName = extractDiskName(sdrResponse);
        diskInfo.put("disk_name", diskName);
        
        // Extract disk index
        int diskIndex = extractDiskIndex(sdrResponse);
        diskInfo.put("disk_index", String.valueOf(diskIndex));
        
        // Determine disk type
        String diskType = determineDiskType(sdrResponse);
        diskInfo.put("disk_type", diskType);
        
        // Determine disk interface
        String diskInterface = determineDiskInterface(sdrResponse);
        diskInfo.put("disk_interface", diskInterface);
        
        // Analyze disk capacity (if available in sensor string)
        String diskCapacity = extractDiskCapacity(sdrResponse);
        diskInfo.put("disk_capacity", diskCapacity);
        
        // Determine disk status based on sensor reading
        String diskStatus = determineDiskStatus(sdrResponse, sensorReading);
        diskInfo.put("disk_status", diskStatus);
        
        // Determine disk health
        String diskHealth = determineDiskHealth(sdrResponse, sensorReading, diskStatus);
        diskInfo.put("disk_health", diskHealth);
        
        // Extract RPM if available
        String diskRpm = extractDiskRpm(sdrResponse);
        if (!diskRpm.isEmpty()) {
            diskInfo.put("disk_rpm", diskRpm);
        }
        
        return diskInfo;
    }

    /**
     * Extract disk name from sensor response
     */
    public static String extractDiskName(GetSdrResponse sdrResponse) {
        if (sdrResponse.sensorIdString != null && !sdrResponse.sensorIdString.trim().isEmpty()) {
            return sdrResponse.sensorIdString.trim();
        }
        
        // Generate name based on entity and sensor type
        String entityName = sdrResponse.entityIdCode != null ? sdrResponse.entityIdCode.getDescription() : "Unknown";
        String sensorType = sdrResponse.sensorTypeCode != null ? sdrResponse.sensorTypeCode.getDescription() : "Sensor";
        
        return String.format("%s_%s_%d", entityName, sensorType, sdrResponse.sensorNumber);
    }

    /**
     * Extract disk index from sensor response
     */
    public static int extractDiskIndex(GetSdrResponse sdrResponse) {
        if (sdrResponse.sensorIdString != null) {
            Matcher matcher = DISK_NAME_PATTERN.matcher(sdrResponse.sensorIdString);
            if (matcher.find()) {
                try {
                    return Integer.parseInt(matcher.group(2));
                } catch (NumberFormatException e) {
                    log.debug("Could not parse disk index from: {}", sdrResponse.sensorIdString);
                }
            }
        }
        
        // Fallback to sensor number
        return sdrResponse.sensorNumber & 0xFF;
    }

    /**
     * Determine disk type based on sensor information
     */
    public static String determineDiskType(GetSdrResponse sdrResponse) {
        if (sdrResponse.sensorIdString != null) {
            String sensorId = sdrResponse.sensorIdString.toLowerCase();
            
            // Check for NVMe first (most specific)
            for (String keyword : NVME_KEYWORDS) {
                if (sensorId.contains(keyword)) {
                    return "NVMe";
                }
            }
            
            // Check for SSD
            for (String keyword : SSD_KEYWORDS) {
                if (sensorId.contains(keyword)) {
                    return "SSD";
                }
            }
            
            // Check for HDD
            for (String keyword : HDD_KEYWORDS) {
                if (sensorId.contains(keyword)) {
                    return "HDD";
                }
            }
        }
        
        // Fallback based on entity type
        if (sdrResponse.entityIdCode != null) {
            switch (sdrResponse.entityIdCode) {
                case Disk_or_DiskBay:
                    return "HDD"; // Traditional disk bays usually house HDDs
                case Peripheral_Bay:
                    return "SSD"; // Peripheral bays often house SSDs
                default:
                    return "Unknown";
            }
        }
        
        return "Unknown";
    }

    /**
     * Determine disk interface based on sensor information
     */
    public static String determineDiskInterface(GetSdrResponse sdrResponse) {
        if (sdrResponse.sensorIdString != null) {
            String sensorId = sdrResponse.sensorIdString.toLowerCase();
            
            // Check for PCIe/NVMe
            for (String keyword : PCIE_KEYWORDS) {
                if (sensorId.contains(keyword)) {
                    return "PCIe";
                }
            }
            
            // Check for SAS
            for (String keyword : SAS_KEYWORDS) {
                if (sensorId.contains(keyword)) {
                    return "SAS";
                }
            }
            
            // Check for SATA
            for (String keyword : SATA_KEYWORDS) {
                if (sensorId.contains(keyword)) {
                    return "SATA";
                }
            }
        }
        
        // Fallback based on entity type
        if (sdrResponse.entityIdCode != null) {
            switch (sdrResponse.entityIdCode) {
                case Disk_or_DiskBay:
                case Drive_Backplane:
                    return "SATA";
                case Peripheral_Bay:
                    return "SAS";
                case System_Internal_Expansion_Board:
                    return "PCIe";
                default:
                    return "Unknown";
            }
        }
        
        return "Unknown";
    }

    /**
     * Extract disk capacity from sensor string
     */
    public static String extractDiskCapacity(GetSdrResponse sdrResponse) {
        if (sdrResponse.sensorIdString != null) {
            Matcher matcher = CAPACITY_PATTERN.matcher(sdrResponse.sensorIdString);
            if (matcher.find()) {
                return matcher.group(1) + " " + matcher.group(2);
            }
        }
        
        return "Unknown";
    }

    /**
     * Extract disk RPM from sensor string
     */
    public static String extractDiskRpm(GetSdrResponse sdrResponse) {
        if (sdrResponse.sensorIdString != null) {
            Matcher matcher = RPM_PATTERN.matcher(sdrResponse.sensorIdString);
            if (matcher.find()) {
                return matcher.group(1) + " RPM";
            }
        }
        
        return "";
    }

    /**
     * Determine disk status based on sensor reading and type
     */
    public static String determineDiskStatus(GetSdrResponse sdrResponse, double sensorReading) {
        if (sdrResponse.sensorTypeCode == null) {
            return "Unknown";
        }
        
        switch (sdrResponse.sensorTypeCode) {
            case Drive_Slot_Bay:
                return sensorReading > 0 ? "Present" : "Absent";
            case Temperature:
                if (sensorReading > 70) return "Overheating";
                if (sensorReading > 55) return "Hot";
                if (sensorReading > 40) return "Warm";
                if (sensorReading > 0) return "Normal";
                return "No Reading";
            case Voltage:
                return sensorReading > 0 ? "Powered" : "Unpowered";
            case Fan:
                if (sensorReading > 0) return "Spinning";
                return "Stopped";
            case Physical_Security:
                return sensorReading > 0 ? "Secure" : "Breached";
            default:
                return sensorReading > 0 ? "Active" : "Inactive";
        }
    }

    /**
     * Determine disk health based on sensor reading and status
     */
    public static String determineDiskHealth(GetSdrResponse sdrResponse, double sensorReading, String diskStatus) {
        // Critical conditions
        if ("Absent".equals(diskStatus) || "Unpowered".equals(diskStatus) || 
            "Overheating".equals(diskStatus) || "Breached".equals(diskStatus)) {
            return "Critical";
        }
        
        // Warning conditions
        if ("Hot".equals(diskStatus) || "Stopped".equals(diskStatus)) {
            return "Warning";
        }
        
        // Good conditions
        if ("Present".equals(diskStatus) || "Normal".equals(diskStatus) || 
            "Powered".equals(diskStatus) || "Active".equals(diskStatus) ||
            "Spinning".equals(diskStatus) || "Secure".equals(diskStatus)) {
            return "Good";
        }
        
        // Temperature-based health assessment
        if (sdrResponse.sensorTypeCode == IpmiSensorTypeCode.Temperature) {
            if (sensorReading > 70) return "Critical";
            if (sensorReading > 55) return "Warning";
            if (sensorReading > 0) return "Good";
        }
        
        return "Unknown";
    }

    /**
     * Check if sensor is related to physical disks
     */
    public static boolean isPhysicalDiskRelated(GetSdrResponse sdrResponse) {
        // Check entity ID
        if (sdrResponse.entityIdCode != null) {
            switch (sdrResponse.entityIdCode) {
                case Disk_or_DiskBay:
                case Peripheral_Bay:
                case Drive_Backplane:
                case System_Internal_Expansion_Board:
                    return true;
            }
        }
        
        // Check sensor type
        if (sdrResponse.sensorTypeCode != null) {
            switch (sdrResponse.sensorTypeCode) {
                case Drive_Slot_Bay:
                case Physical_Security:
                    return true;
                case Temperature:
                case Voltage:
                case Fan:
                    // These are disk-related if they have disk keywords in sensor ID
                    return containsDiskKeywords(sdrResponse.sensorIdString);
            }
        }
        
        // Check sensor ID string for disk-related keywords
        return containsDiskKeywords(sdrResponse.sensorIdString);
    }

    /**
     * Check if string contains disk-related keywords
     */
    private static boolean containsDiskKeywords(String text) {
        if (text == null) {
            return false;
        }
        
        String lowerText = text.toLowerCase();
        return lowerText.contains("disk") || lowerText.contains("drive") || 
               lowerText.contains("hdd") || lowerText.contains("ssd") ||
               lowerText.contains("storage") || lowerText.contains("bay") ||
               lowerText.contains("nvme");
    }

    /**
     * Format sensor reading with appropriate units
     */
    public static String formatSensorReading(GetSdrResponse sdrResponse, double sensorReading) {
        if (sdrResponse.sensorTypeCode == null) {
            return String.format("%.1f", sensorReading);
        }
        
        switch (sdrResponse.sensorTypeCode) {
            case Temperature:
                return String.format("%.1f°C", sensorReading);
            case Voltage:
                return String.format("%.2fV", sensorReading);
            case Fan:
                return String.format("%.0f RPM", sensorReading);
            default:
                String unit = sdrResponse.unitTypeCode != null ? sdrResponse.unitTypeCode.getDescription() : "";
                return String.format("%.3f %s", sensorReading, unit).trim();
        }
    }
}
