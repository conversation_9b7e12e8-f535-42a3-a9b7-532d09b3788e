/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.collector.collect.ipmi2.client.handler;

import cn.iocoder.cloud.framework.collector.collect.ipmi2.client.IpmiSession;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.client.UdpConnection;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi.command.fru.GetFruInventoryAreaInfoRequest;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi.command.fru.GetFruInventoryAreaInfoResponse;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi.command.fru.ReadFruDataRequest;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi.command.fru.ReadFruDataResponse;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi.command.sdr.*;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi.command.sdr.code.IpmiEntityIdCode;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi.command.sdr.code.IpmiSensorTypeCode;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.utils.FruDataParser;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.utils.PhysicalDiskUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.constants.CommonConstants;
import org.apache.hertzbeat.common.entity.job.Metrics;
import org.apache.hertzbeat.common.entity.message.CollectRep;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * IPMI Storage Device (Sd) Handler for collecting physical disk information
 * Enhanced version that combines SDR and FRU data to provide comprehensive disk information
 * including name, status, slot number, size, bus protocol, media type, and hot spare status
 */
@Slf4j
public class SdHandler implements IpmiHandler {

    // Physical disk related entity IDs
    private static final IpmiEntityIdCode[] DISK_ENTITY_IDS = {
        IpmiEntityIdCode.Disk_or_DiskBay,
        IpmiEntityIdCode.Peripheral_Bay,
        IpmiEntityIdCode.System_Management_Module,
        IpmiEntityIdCode.Drive_Backplane,
        IpmiEntityIdCode.System_Internal_Expansion_Board
    };

    // Physical disk related sensor types
    private static final IpmiSensorTypeCode[] DISK_SENSOR_TYPES = {
        IpmiSensorTypeCode.Drive_Slot_Bay,
        IpmiSensorTypeCode.Physical_Security,
        IpmiSensorTypeCode.Temperature,
        IpmiSensorTypeCode.Voltage,
        IpmiSensorTypeCode.Fan,
        IpmiSensorTypeCode.Other
    };

    // Patterns for extracting disk information from sensor names and FRU data
    private static final Pattern SLOT_PATTERN = Pattern.compile("(?i)slot\\s*(\\d+)|bay\\s*(\\d+)|disk\\s*(\\d+)", Pattern.CASE_INSENSITIVE);
    private static final Pattern CAPACITY_PATTERN = Pattern.compile("(\\d+(?:\\.\\d+)?)\\s*(TB|GB|MB|PB)", Pattern.CASE_INSENSITIVE);
    private static final Pattern INTERFACE_PATTERN = Pattern.compile("(?i)(sata|sas|nvme|pcie|scsi|ide)", Pattern.CASE_INSENSITIVE);
    private static final Pattern MEDIA_TYPE_PATTERN = Pattern.compile("(?i)(ssd|hdd|nvme|solid\\s*state|hard\\s*disk)", Pattern.CASE_INSENSITIVE);

    // Common FRU device IDs that might contain disk information
    private static final byte[] DISK_FRU_DEVICE_IDS = {0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07};

    // Maximum FRU data size to read
    private static final int MAX_FRU_DATA_SIZE = 512;
    private static final byte FRU_READ_CHUNK_SIZE = 16;

    @Override
    public void handler(IpmiSession session, UdpConnection connection, CollectRep.MetricsData.Builder builder, Metrics metrics) throws IOException {
        log.debug("Starting enhanced physical disk data collection via IPMI SDR and FRU");

        try {
            // Step 1: Collect disk information from SDR records
            Map<Integer, Map<String, String>> sdrDiskInfo = collectDiskInfoFromSDR(session, connection);

            // Step 2: Collect additional disk information from FRU data
            Map<Integer, Map<String, String>> fruDiskInfo = collectDiskInfoFromFRU(session, connection);

            // Step 3: Merge and enhance disk information
            Map<Integer, Map<String, String>> mergedDiskInfo = mergeDiskInformation(sdrDiskInfo, fruDiskInfo);

            log.info("Disk information collection summary: SDR={}, FRU={}, Merged={}",
                    sdrDiskInfo.size(), fruDiskInfo.size(), mergedDiskInfo.size());

            // Step 4: If no disk information found, try fallback strategy
            if (mergedDiskInfo.isEmpty()) {
                log.warn("No disk information found via standard methods, trying fallback strategy");
                mergedDiskInfo = collectFallbackDiskInfo(session, connection);
            }

            // Step 5: Add metrics for each disk found
            if (!mergedDiskInfo.isEmpty()) {
                for (Map.Entry<Integer, Map<String, String>> entry : mergedDiskInfo.entrySet()) {
                    Map<String, String> diskInfo = entry.getValue();
                    enhanceDiskInformation(diskInfo, entry.getKey());
                    addPhysicalDiskMetrics(builder, metrics, diskInfo);
                }
                log.info("Successfully collected information for {} physical disks", mergedDiskInfo.size());
            } else {
                // If no physical disks found, add a default entry
                log.warn("No physical disks found after all collection attempts");
                addNoPhysicalDisksEntry(builder, metrics);
            }

        } catch (Exception e) {
            log.error("Error collecting physical disk information: {}", e.getMessage());
            addErrorEntry(builder, metrics, e.getMessage());
        }
    }

    /**
     * Collect disk information from SDR (Sensor Data Record) repository
     */
    private Map<Integer, Map<String, String>> collectDiskInfoFromSDR(IpmiSession session, UdpConnection connection) throws IOException {
        Map<Integer, Map<String, String>> diskInfoMap = new HashMap<>();
        int totalRecordsProcessed = 0;
        int diskRelatedRecords = 0;

        try {
            // Reserve SDR Repository
            ReserveSdrRepositoryResponse response = connection.get(session, new ReserveSdrRepositoryRequest(), ReserveSdrRepositoryResponse.class);
            int reserveId = response.reserveId;
            log.debug("SDR Repository reserved with ID: {}", reserveId);

            // Scan through SDR records to find physical disk related sensors
            int recordId = GetSdrRequest.RECORD_ID_START;
            int diskIndex = 0;

            while (recordId != 0xFFFF) {
                try {
                    totalRecordsProcessed++;
                    Map<String, String> diskInfo = processSdrRecord(session, connection, reserveId, recordId, diskIndex);

                    if (!diskInfo.isEmpty()) {
                        diskRelatedRecords++;
                        diskInfoMap.put(diskIndex, diskInfo);
                        log.debug("Found disk-related SDR record {}: {}", recordId, diskInfo.get("sensor_id"));
                        diskIndex++;
                    }

                    // Get next record ID
                    GetSdrRequest headRequest = new GetSdrRequest(reserveId, recordId, (byte) 0, GetSdrRequest.HEADER_LENGTH);
                    GetSdrResponse getSdrHeadResponse = connection.get(session, headRequest, GetSdrResponse.class);
                    recordId = getSdrHeadResponse.nextRecordId;

                } catch (Exception e) {
                    log.debug("Error processing SDR record {}: {}", recordId, e.getMessage());
                    // Continue to next record
                    try {
                        GetSdrRequest headRequest = new GetSdrRequest(reserveId, recordId, (byte) 0, GetSdrRequest.HEADER_LENGTH);
                        GetSdrResponse getSdrHeadResponse = connection.get(session, headRequest, GetSdrResponse.class);
                        recordId = getSdrHeadResponse.nextRecordId;
                    } catch (Exception skipException) {
                        log.warn("Failed to skip problematic SDR record {}: {}", recordId, skipException.getMessage());
                        break;
                    }
                }
            }

            log.info("SDR scan completed: {} total records processed, {} disk-related records found",
                    totalRecordsProcessed, diskRelatedRecords);

        } catch (Exception e) {
            log.warn("Error collecting disk information from SDR: {}", e.getMessage());
        }

        return diskInfoMap;
    }

    /**
     * Collect disk information from FRU (Field Replaceable Unit) data
     */
    private Map<Integer, Map<String, String>> collectDiskInfoFromFRU(IpmiSession session, UdpConnection connection) {
        Map<Integer, Map<String, String>> fruDiskInfo = new HashMap<>();
        int fruDevicesChecked = 0;
        int diskRelatedFruDevices = 0;

        for (byte fruDeviceId : DISK_FRU_DEVICE_IDS) {
            try {
                fruDevicesChecked++;
                Map<String, String> deviceInfo = collectFruDeviceInfo(session, connection, fruDeviceId);
                log.debug("FRU Device ID {}: collected {} fields", fruDeviceId & 0xFF, deviceInfo.size());

                if (!deviceInfo.isEmpty()) {
                    // 放宽过滤条件 - 先收集所有FRU数据，后面再判断是否磁盘相关
                    boolean isDiskRelated = isDiskRelatedFruData(deviceInfo);
                    log.debug("FRU Device ID {}: isDiskRelated={}, data={}", fruDeviceId & 0xFF, isDiskRelated, deviceInfo);

                    if (isDiskRelated || deviceInfo.containsKey("product_name")) {
                        diskRelatedFruDevices++;
                        int diskIndex = extractDiskIndexFromFru(deviceInfo, fruDeviceId);
                        fruDiskInfo.put(diskIndex, deviceInfo);
                        log.debug("Added FRU device {} as disk index {}", fruDeviceId & 0xFF, diskIndex);
                    }
                }
            } catch (Exception e) {
                log.debug("Error collecting FRU data for device ID {}: {}", fruDeviceId & 0xFF, e.getMessage());
            }
        }

        log.info("FRU scan completed: {} devices checked, {} disk-related devices found",
                fruDevicesChecked, diskRelatedFruDevices);

        return fruDiskInfo;
    }

    /**
     * Process a single SDR record to extract physical disk information
     */
    private Map<String, String> processSdrRecord(IpmiSession session, UdpConnection connection, int reserveId, int recordId, int diskIndex) throws IOException {
        Map<String, String> diskInfo = new HashMap<>();

        // Get SDR header first
        GetSdrRequest headRequest = new GetSdrRequest(reserveId, recordId, (byte) 0, GetSdrRequest.HEADER_LENGTH);
        GetSdrResponse getSdrHeadResponse = connection.get(session, headRequest, GetSdrResponse.class);

        log.debug("Processing SDR record {}: type=0x{}, length={}",
                recordId, Integer.toHexString(getSdrHeadResponse.recordType), getSdrHeadResponse.recordLength);

        // Process Full Sensor Records (type 0x01) and Compact Sensor Records (type 0x02)
        if (getSdrHeadResponse.recordType != 0x01 && getSdrHeadResponse.recordType != 0x02) {
            log.debug("Skipping SDR record {} - unsupported type: 0x{}", recordId, Integer.toHexString(getSdrHeadResponse.recordType));
            return diskInfo;
        }

        // Get full SDR record
        GetSdrRequest request = new GetSdrRequest(reserveId, recordId, (byte) 0, (byte) (getSdrHeadResponse.recordLength + 5));
        GetSdrResponse getSdrBodyResponse = connection.get(session, request, GetSdrResponse.class);

        // Log sensor details for debugging
        log.debug("SDR record {}: sensorId='{}', entityId={}, sensorType={}",
                recordId,
                getSdrBodyResponse.sensorIdString,
                getSdrBodyResponse.entityIdCode,
                getSdrBodyResponse.sensorTypeCode);

        // 放宽过滤条件 - 检查是否可能与磁盘相关
        boolean isPotentiallyDiskRelated = isPotentiallyDiskRelated(getSdrBodyResponse);
        if (!isPotentiallyDiskRelated) {
            log.debug("SDR record {} not disk-related", recordId);
            return diskInfo;
        }

        log.debug("SDR record {} identified as potentially disk-related", recordId);
        
        // Extract basic disk information
        diskInfo.put("disk_index", String.valueOf(diskIndex));
        diskInfo.put("sensor_id", getSdrBodyResponse.sensorIdString != null ? getSdrBodyResponse.sensorIdString : "Unknown");
        diskInfo.put("entity_id", getSdrBodyResponse.entityIdCode != null ? getSdrBodyResponse.entityIdCode.getDescription() : "Unknown");
        diskInfo.put("sensor_type", getSdrBodyResponse.sensorTypeCode != null ? getSdrBodyResponse.sensorTypeCode.getDescription() : "Unknown");
        diskInfo.put("sensor_number", String.valueOf(getSdrBodyResponse.sensorNumber));
        
        // Try to get sensor reading if available
        if (isReadable(getSdrBodyResponse)) {
            try {
                GetSensorReadingRequest readingRequest = new GetSensorReadingRequest(getSdrBodyResponse.sensorNumber);
                readingRequest.setRqLun(getSdrBodyResponse.sensorOwnerLun);
                GetSensorReadingResponse getSensorReadingResponse = connection.get(session, readingRequest, GetSensorReadingResponse.class);
                
                double sensorReading = calcSensorValue(getSdrBodyResponse, getSensorReadingResponse.sensorReading);
                String formattedReading = PhysicalDiskUtils.formatSensorReading(getSdrBodyResponse, sensorReading);
                diskInfo.put("sensor_reading", formattedReading);
                
                // Determine disk status based on sensor reading and type
                diskInfo.put("disk_status", PhysicalDiskUtils.determineDiskStatus(getSdrBodyResponse, sensorReading));
                
            } catch (Exception e) {
                log.debug("Could not read sensor {} for disk {}: {}", getSdrBodyResponse.sensorNumber, diskIndex, e.getMessage());
                diskInfo.put("sensor_reading", "N/A");
                diskInfo.put("disk_status", "Unknown");
            }
        } else {
            diskInfo.put("sensor_reading", "N/A");
            diskInfo.put("disk_status", "Unknown");
        }
        
        // Extract additional disk properties
        extractDiskProperties(getSdrBodyResponse, diskInfo, diskIndex);
        
        return diskInfo;
    }

    /**
     * Collect FRU device information for a specific device ID
     */
    private Map<String, String> collectFruDeviceInfo(IpmiSession session, UdpConnection connection, byte fruDeviceId) throws IOException {
        Map<String, String> deviceInfo = new HashMap<>();

        try {
            // Step 1: Get FRU Inventory Area Info
            GetFruInventoryAreaInfoRequest areaInfoRequest = new GetFruInventoryAreaInfoRequest(fruDeviceId);
            GetFruInventoryAreaInfoResponse areaInfoResponse = connection.get(session, areaInfoRequest, GetFruInventoryAreaInfoResponse.class);

            if (areaInfoResponse.fruInventoryAreaSize <= 0) {
                return deviceInfo;
            }

            // Step 2: Read FRU data in chunks
            byte[] fruData = new byte[Math.min(areaInfoResponse.fruInventoryAreaSize, MAX_FRU_DATA_SIZE)];
            int offset = 0;
            int totalBytesToRead = fruData.length;

            while (offset < totalBytesToRead) {
                int bytesToRead = Math.min(FRU_READ_CHUNK_SIZE, totalBytesToRead - offset);
                ReadFruDataRequest readRequest = new ReadFruDataRequest(fruDeviceId, offset, (byte) bytesToRead);
                ReadFruDataResponse readResponse = connection.get(session, readRequest, ReadFruDataResponse.class);

                if (readResponse.requestedData != null && readResponse.requestedData.length > 0) {
                    System.arraycopy(readResponse.requestedData, 0, fruData, offset, Math.min(readResponse.requestedData.length, bytesToRead));
                    offset += readResponse.requestedData.length;
                } else {
                    break;
                }
            }

            // Step 3: Parse FRU data
            if (offset > 0) {
                byte[] actualFruData = Arrays.copyOf(fruData, offset);
                deviceInfo = FruDataParser.extractStorageHealthInfo(actualFruData);
                deviceInfo.put("fru_device_id", String.valueOf(fruDeviceId & 0xFF));
                deviceInfo.put("fru_data_size", String.valueOf(offset));
            }

        } catch (Exception e) {
            log.debug("Error collecting FRU device info for ID {}: {}", fruDeviceId, e.getMessage());
        }

        return deviceInfo;
    }

    /**
     * Check if FRU data is related to disk/storage devices
     */
    private boolean isDiskRelatedFruData(Map<String, String> fruData) {
        if (fruData.isEmpty()) {
            return false;
        }

        // Check product name and manufacturer for disk-related keywords
        String productName = fruData.getOrDefault("product_name", "").toLowerCase();
        String manufacturer = fruData.getOrDefault("manufacturer", "").toLowerCase();
        String partNumber = fruData.getOrDefault("part_number", "").toLowerCase();
        String boardProductName = fruData.getOrDefault("board_product_name", "").toLowerCase();

        // Expanded list of disk-related keywords
        String[] diskKeywords = {
            "disk", "drive", "storage", "ssd", "hdd", "nvme", "sata", "sas", "scsi",
            "raid", "array", "volume", "bay", "slot", "physical", "media", "flash",
            "solid state", "hard disk", "m.2", "pcie", "enterprise", "datacenter"
        };

        String searchText = productName + " " + manufacturer + " " + partNumber + " " + boardProductName;

        for (String keyword : diskKeywords) {
            if (searchText.contains(keyword)) {
                log.debug("FRU data matches disk keyword '{}' in text: {}", keyword, searchText.trim());
                return true;
            }
        }

        // Check for known storage manufacturers
        String[] storageManufacturers = {
            "samsung", "intel", "micron", "western digital", "wd", "seagate", "toshiba",
            "kingston", "crucial", "corsair", "sandisk", "sk hynix", "kioxia"
        };

        for (String mfg : storageManufacturers) {
            if (manufacturer.contains(mfg) || productName.contains(mfg)) {
                log.debug("FRU data matches storage manufacturer: {}", mfg);
                return true;
            }
        }

        // If we have any meaningful product information, consider it potentially disk-related
        // This is very lenient but helps capture edge cases
        if (!productName.isEmpty() && productName.length() > 3) {
            log.debug("FRU data has product name, considering as potential disk: {}", productName);
            return true;
        }

        return false;
    }

    /**
     * Extract disk index from FRU data
     */
    private int extractDiskIndexFromFru(Map<String, String> fruData, byte fruDeviceId) {
        // Try to extract from product name or part number
        String productName = fruData.getOrDefault("product_name", "");
        String partNumber = fruData.getOrDefault("part_number", "");

        Matcher matcher = SLOT_PATTERN.matcher(productName + " " + partNumber);
        if (matcher.find()) {
            for (int i = 1; i <= matcher.groupCount(); i++) {
                if (matcher.group(i) != null) {
                    try {
                        return Integer.parseInt(matcher.group(i));
                    } catch (NumberFormatException e) {
                        // Continue to next group
                    }
                }
            }
        }

        // Fallback to FRU device ID
        return fruDeviceId & 0xFF;
    }

    /**
     * Merge disk information from SDR and FRU sources
     */
    private Map<Integer, Map<String, String>> mergeDiskInformation(
            Map<Integer, Map<String, String>> sdrDiskInfo,
            Map<Integer, Map<String, String>> fruDiskInfo) {

        Map<Integer, Map<String, String>> mergedInfo = new HashMap<>(sdrDiskInfo);

        // Merge FRU data into SDR data
        for (Map.Entry<Integer, Map<String, String>> fruEntry : fruDiskInfo.entrySet()) {
            Integer diskIndex = fruEntry.getKey();
            Map<String, String> fruData = fruEntry.getValue();

            if (mergedInfo.containsKey(diskIndex)) {
                // Merge with existing SDR data
                Map<String, String> existingData = mergedInfo.get(diskIndex);
                Map<String, String> mergedData = new HashMap<>(existingData);

                // Add FRU data, preferring more detailed information
                for (Map.Entry<String, String> fruField : fruData.entrySet()) {
                    String key = fruField.getKey();
                    String value = fruField.getValue();

                    if (!mergedData.containsKey(key) ||
                        mergedData.get(key).equals("Unknown") ||
                        mergedData.get(key).equals("N/A") ||
                        value.length() > mergedData.get(key).length()) {
                        mergedData.put(key, value);
                    }
                }

                mergedInfo.put(diskIndex, mergedData);
            } else {
                // Add new disk from FRU data
                mergedInfo.put(diskIndex, new HashMap<>(fruData));
            }
        }

        return mergedInfo;
    }

    /**
     * Fallback strategy: collect all sensor data and try to identify potential disk sensors
     */
    private Map<Integer, Map<String, String>> collectFallbackDiskInfo(IpmiSession session, UdpConnection connection) {
        Map<Integer, Map<String, String>> fallbackDiskInfo = new HashMap<>();

        try {
            log.debug("Starting fallback disk information collection");

            // Reserve SDR Repository
            ReserveSdrRepositoryResponse response = connection.get(session, new ReserveSdrRepositoryRequest(), ReserveSdrRepositoryResponse.class);
            int reserveId = response.reserveId;

            // Collect ALL sensors and look for any that might be disk-related
            int recordId = GetSdrRequest.RECORD_ID_START;
            int diskIndex = 0;
            int totalSensors = 0;

            while (recordId != 0xFFFF) {
                try {
                    totalSensors++;

                    // Get SDR header
                    GetSdrRequest headRequest = new GetSdrRequest(reserveId, recordId, (byte) 0, GetSdrRequest.HEADER_LENGTH);
                    GetSdrResponse getSdrHeadResponse = connection.get(session, headRequest, GetSdrResponse.class);

                    // Process any sensor record type
                    if (getSdrHeadResponse.recordType == 0x01 || getSdrHeadResponse.recordType == 0x02) {
                        // Get full SDR record
                        GetSdrRequest request = new GetSdrRequest(reserveId, recordId, (byte) 0, (byte) (getSdrHeadResponse.recordLength + 5));
                        GetSdrResponse getSdrBodyResponse = connection.get(session, request, GetSdrResponse.class);

                        // Create basic sensor info
                        Map<String, String> sensorInfo = new HashMap<>();
                        sensorInfo.put("sensor_id", getSdrBodyResponse.sensorIdString != null ? getSdrBodyResponse.sensorIdString : "Sensor_" + getSdrBodyResponse.sensorNumber);
                        sensorInfo.put("entity_id", getSdrBodyResponse.entityIdCode != null ? getSdrBodyResponse.entityIdCode.getDescription() : "Unknown");
                        sensorInfo.put("sensor_type", getSdrBodyResponse.sensorTypeCode != null ? getSdrBodyResponse.sensorTypeCode.getDescription() : "Unknown");
                        sensorInfo.put("sensor_number", String.valueOf(getSdrBodyResponse.sensorNumber));
                        sensorInfo.put("disk_index", String.valueOf(diskIndex));

                        // Try to read sensor value
                        try {
                            if (isReadable(getSdrBodyResponse)) {
                                GetSensorReadingRequest readingRequest = new GetSensorReadingRequest(getSdrBodyResponse.sensorNumber);
                                readingRequest.setRqLun(getSdrBodyResponse.sensorOwnerLun);
                                GetSensorReadingResponse getSensorReadingResponse = connection.get(session, readingRequest, GetSensorReadingResponse.class);

                                double sensorReading = calcSensorValue(getSdrBodyResponse, getSensorReadingResponse.sensorReading);
                                sensorInfo.put("sensor_reading", String.valueOf(sensorReading));
                            } else {
                                sensorInfo.put("sensor_reading", "N/A");
                            }
                        } catch (Exception e) {
                            sensorInfo.put("sensor_reading", "Error: " + e.getMessage());
                        }

                        // Add this sensor as a potential "disk" for debugging
                        fallbackDiskInfo.put(diskIndex, sensorInfo);
                        diskIndex++;

                        log.debug("Fallback: Added sensor {} as potential disk {}", sensorInfo.get("sensor_id"), diskIndex - 1);
                    }

                    // Get next record ID
                    GetSdrRequest getSdrRequest = new GetSdrRequest(reserveId, recordId, (byte) 0, GetSdrRequest.HEADER_LENGTH);
                    GetSdrResponse getSdrResponse = connection.get(session, getSdrRequest, GetSdrResponse.class);
                    recordId = getSdrResponse.nextRecordId;

                } catch (Exception e) {
                    log.debug("Error in fallback processing for record {}: {}", recordId, e.getMessage());
                    break;
                }
            }

            log.info("Fallback collection completed: {} total sensors found, {} added as potential disks",
                    totalSensors, fallbackDiskInfo.size());

        } catch (Exception e) {
            log.warn("Error in fallback disk information collection: {}", e.getMessage());
        }

        return fallbackDiskInfo;
    }

    /**
     * Check if sensor is potentially disk-related (more lenient than PhysicalDiskUtils.isPhysicalDiskRelated)
     */
    private boolean isPotentiallyDiskRelated(GetSdrResponse sdrResponse) {
        if (sdrResponse == null) {
            return false;
        }

        // First try the strict check
        if (PhysicalDiskUtils.isPhysicalDiskRelated(sdrResponse)) {
            return true;
        }

        // More lenient checks for potential disk-related sensors
        String sensorId = sdrResponse.sensorIdString != null ? sdrResponse.sensorIdString.toLowerCase() : "";

        // Check for common disk-related keywords in sensor ID
        String[] diskKeywords = {
            "disk", "drive", "storage", "ssd", "hdd", "nvme", "sata", "sas", "scsi",
            "bay", "slot", "physical", "volume", "raid", "array"
        };

        for (String keyword : diskKeywords) {
            if (sensorId.contains(keyword)) {
                log.debug("Sensor '{}' matches disk keyword: {}", sensorId, keyword);
                return true;
            }
        }

        // Check entity types that might be related to storage
        if (sdrResponse.entityIdCode != null) {
            switch (sdrResponse.entityIdCode) {
                case Disk_or_DiskBay:
                case Peripheral_Bay:
                case Drive_Backplane:
                case System_Internal_Expansion_Board:
                case System_Management_Module:
                    log.debug("Sensor has disk-related entity ID: {}", sdrResponse.entityIdCode);
                    return true;
            }
        }

        // Check sensor types that might provide disk information
        if (sdrResponse.sensorTypeCode != null) {
            switch (sdrResponse.sensorTypeCode) {
                case Drive_Slot_Bay:
                case Physical_Security:
                case Temperature:
                case Voltage:
                case Fan:
                case Other:
                    // For these types, check if sensor ID suggests disk relation
                    if (!sensorId.isEmpty()) {
                        log.debug("Sensor type {} with ID '{}' might be disk-related",
                                sdrResponse.sensorTypeCode, sensorId);
                        return true;
                    }
                    break;
            }
        }

        return false;
    }

    /**
     * Check if sensor is readable
     */
    private boolean isReadable(GetSdrResponse sdrResponse) {
        // This is a simplified check - in real implementation you would check the sensor capabilities
        return sdrResponse.sensorNumber >= 0 && sdrResponse.sensorTypeCode != null;
    }

    /**
     * Calculate sensor value (simplified implementation)
     */
    private double calcSensorValue(GetSdrResponse sdrResponse, int rawReading) {
        // This is a simplified calculation - real implementation would use the SDR linearization formula
        return rawReading;
    }



    /**
     * Extract additional disk properties
     */
    private void extractDiskProperties(GetSdrResponse sdrResponse, Map<String, String> diskInfo, int diskIndex) {
        // Use PhysicalDiskUtils to analyze disk information
        double sensorReading = 0;
        try {
            String readingStr = diskInfo.get("sensor_reading");
            if (readingStr != null && !readingStr.equals("N/A")) {
                // Extract numeric value from sensor reading
                String[] parts = readingStr.split(" ");
                if (parts.length > 0) {
                    sensorReading = Double.parseDouble(parts[0]);
                }
            }
        } catch (NumberFormatException e) {
            log.debug("Could not parse sensor reading: {}", diskInfo.get("sensor_reading"));
        }

        // Analyze disk information using utility class
        Map<String, String> analyzedInfo = PhysicalDiskUtils.analyzeDiskInfo(sdrResponse, sensorReading);

        // Merge analyzed information with existing disk info
        diskInfo.putAll(analyzedInfo);

        // Override disk_index with our calculated value
        diskInfo.put("disk_index", String.valueOf(diskIndex));
    }

    /**
     * Enhance disk information with additional analysis and formatting
     * This method extracts and formats the specific fields shown in your screenshot:
     * 名称, 状态, 插槽编号, 大小, 总线协议, 介质类型, 热备份
     */
    private void enhanceDiskInformation(Map<String, String> diskInfo, int diskIndex) {
        // 1. 名称 (Name) - Use product name or generate from sensor info
        String diskName = diskInfo.getOrDefault("product_name", "");
        if (diskName.isEmpty() || diskName.equals("Unknown")) {
            diskName = diskInfo.getOrDefault("disk_name", "");
            if (diskName.isEmpty() || diskName.equals("Unknown")) {
                diskName = "Disk " + diskIndex;
            }
        }
        diskInfo.put("disk_name", diskName);

        // 2. 状态 (Status) - Determine from sensor readings and FRU data
        String status = determineDiskStatus(diskInfo);
        diskInfo.put("disk_status", status);

        // 3. 插槽编号 (Slot Number) - Extract from sensor name or FRU data
        String slotNumber = extractSlotNumber(diskInfo, diskIndex);
        diskInfo.put("slot_number", slotNumber);

        // 4. 大小 (Size) - Extract capacity information
        String diskSize = extractDiskSize(diskInfo);
        diskInfo.put("disk_capacity", diskSize);

        // 5. 总线协议 (Bus Protocol) - Determine interface type
        String busProtocol = determineBusProtocol(diskInfo);
        diskInfo.put("disk_interface", busProtocol);

        // 6. 介质类型 (Media Type) - Determine if SSD, HDD, etc.
        String mediaType = determineMediaType(diskInfo);
        diskInfo.put("disk_type", mediaType);

        // 7. 热备份 (Hot Spare) - Determine hot spare status
        String hotSpareStatus = determineHotSpareStatus(diskInfo);
        diskInfo.put("hot_spare_status", hotSpareStatus);

        // Additional health assessment
        String healthStatus = determineOverallHealth(diskInfo);
        diskInfo.put("disk_health", healthStatus);
    }

    /**
     * Determine disk status from available information
     */
    private String determineDiskStatus(Map<String, String> diskInfo) {
        String sensorReading = diskInfo.getOrDefault("sensor_reading", "");
        String sensorType = diskInfo.getOrDefault("sensor_type", "");
        String fruStatus = diskInfo.getOrDefault("health_status", "");

        // Check FRU health status first
        if (!fruStatus.isEmpty() && !fruStatus.equals("Unknown")) {
            if (fruStatus.toLowerCase().contains("good") || fruStatus.toLowerCase().contains("ok")) {
                return "正常";
            } else if (fruStatus.toLowerCase().contains("warning")) {
                return "警告";
            } else if (fruStatus.toLowerCase().contains("critical") || fruStatus.toLowerCase().contains("error")) {
                return "故障";
            }
        }

        // Check sensor readings
        if (sensorType.toLowerCase().contains("drive") || sensorType.toLowerCase().contains("slot")) {
            try {
                double reading = Double.parseDouble(sensorReading.replaceAll("[^0-9.]", ""));
                return reading > 0 ? "正常" : "未安装";
            } catch (NumberFormatException e) {
                // Continue with other checks
            }
        }

        // Default based on presence of data
        return diskInfo.containsKey("product_name") && !diskInfo.get("product_name").equals("Unknown") ? "正常" : "未知";
    }

    /**
     * Extract slot number from disk information
     */
    private String extractSlotNumber(Map<String, String> diskInfo, int diskIndex) {
        // Try to extract from sensor ID or product name
        String sensorId = diskInfo.getOrDefault("sensor_id", "");
        String productName = diskInfo.getOrDefault("product_name", "");
        String partNumber = diskInfo.getOrDefault("part_number", "");

        String searchText = sensorId + " " + productName + " " + partNumber;
        Matcher matcher = SLOT_PATTERN.matcher(searchText);

        if (matcher.find()) {
            for (int i = 1; i <= matcher.groupCount(); i++) {
                if (matcher.group(i) != null) {
                    return matcher.group(i);
                }
            }
        }

        // Fallback to disk index
        return String.valueOf(diskIndex);
    }

    /**
     * Extract disk size/capacity
     */
    private String extractDiskSize(Map<String, String> diskInfo) {
        // Check various fields for capacity information
        String[] capacityFields = {"disk_capacity", "product_name", "part_number", "custom_field_1", "custom_field_2"};

        for (String field : capacityFields) {
            String value = diskInfo.getOrDefault(field, "");
            if (!value.isEmpty()) {
                Matcher matcher = CAPACITY_PATTERN.matcher(value);
                if (matcher.find()) {
                    return matcher.group(1) + " " + matcher.group(2);
                }
            }
        }

        return "未知";
    }

    /**
     * Determine bus protocol/interface type
     */
    private String determineBusProtocol(Map<String, String> diskInfo) {
        String[] searchFields = {"disk_interface", "product_name", "part_number", "sensor_id"};

        for (String field : searchFields) {
            String value = diskInfo.getOrDefault(field, "").toLowerCase();
            if (!value.isEmpty()) {
                Matcher matcher = INTERFACE_PATTERN.matcher(value);
                if (matcher.find()) {
                    String protocol = matcher.group(1).toUpperCase();
                    // Map to common protocol names
                    switch (protocol) {
                        case "NVME": return "NVMe";
                        case "PCIE": return "PCIe";
                        case "SATA": return "SATA";
                        case "SAS": return "SAS";
                        case "SCSI": return "SCSI";
                        case "IDE": return "IDE";
                        default: return protocol;
                    }
                }
            }
        }

        // Fallback based on disk type
        String diskType = diskInfo.getOrDefault("disk_type", "").toLowerCase();
        if (diskType.contains("nvme")) return "NVMe";
        if (diskType.contains("ssd")) return "SATA";
        if (diskType.contains("hdd")) return "SATA";

        return "SATA"; // Most common default
    }

    /**
     * Determine media type (SSD, HDD, etc.)
     */
    private String determineMediaType(Map<String, String> diskInfo) {
        String[] searchFields = {"disk_type", "product_name", "part_number", "manufacturer"};

        for (String field : searchFields) {
            String value = diskInfo.getOrDefault(field, "").toLowerCase();
            if (!value.isEmpty()) {
                if (value.contains("nvme")) return "NVMe";
                if (value.contains("ssd") || value.contains("solid state")) return "SSD";
                if (value.contains("hdd") || value.contains("hard disk")) return "HDD";
            }
        }

        // Check for RPM information (indicates HDD)
        String rpm = diskInfo.getOrDefault("disk_rpm", "");
        if (!rpm.isEmpty() && !rpm.equals("Unknown")) {
            return "HDD";
        }

        return "SSD"; // Default assumption for modern systems
    }

    /**
     * Determine hot spare status
     */
    private String determineHotSpareStatus(Map<String, String> diskInfo) {
        // Check for hot spare indicators in various fields
        String[] searchFields = {"product_name", "part_number", "custom_field_1", "custom_field_2", "sensor_id"};

        for (String field : searchFields) {
            String value = diskInfo.getOrDefault(field, "").toLowerCase();
            if (value.contains("spare") || value.contains("standby") || value.contains("backup")) {
                return "是";
            }
        }

        // Check disk status - if disk is present but not actively used, might be spare
        String status = diskInfo.getOrDefault("disk_status", "");
        if (status.equals("正常") && diskInfo.getOrDefault("sensor_reading", "0").equals("0")) {
            return "否"; // Present but not active
        }

        return "否";
    }

    /**
     * Determine overall health status
     */
    private String determineOverallHealth(Map<String, String> diskInfo) {
        String status = diskInfo.getOrDefault("disk_status", "");

        switch (status) {
            case "正常": return "良好";
            case "警告": return "警告";
            case "故障": return "故障";
            case "未安装": return "未安装";
            default: return "未知";
        }
    }

    /**
     * Add physical disk metrics to the builder
     */
    private void addPhysicalDiskMetrics(CollectRep.MetricsData.Builder builder, Metrics metrics, Map<String, String> diskInfo) {
        CollectRep.ValueRow.Builder valueRowBuilder = CollectRep.ValueRow.newBuilder();
        
        for (Metrics.Field field : metrics.getFields()) {
            String fieldName = field.getField();
            String value = diskInfo.getOrDefault(fieldName, CommonConstants.NULL_VALUE);
            valueRowBuilder.addColumns(value);
        }
        
        builder.addValues(valueRowBuilder.build());
        log.debug("Added physical disk metrics for disk: {}", diskInfo.get("disk_name"));
    }

    /**
     * Add entry when no physical disks are found
     */
    private void addNoPhysicalDisksEntry(CollectRep.MetricsData.Builder builder, Metrics metrics) {
        CollectRep.ValueRow.Builder valueRowBuilder = CollectRep.ValueRow.newBuilder();
        
        for (Metrics.Field field : metrics.getFields()) {
            String value;
            switch (field.getField()) {
                case "disk_name":
                    value = "No Physical Disks Found";
                    break;
                case "disk_status":
                    value = "N/A";
                    break;
                case "disk_health":
                    value = "N/A";
                    break;
                default:
                    value = CommonConstants.NULL_VALUE;
                    break;
            }
            valueRowBuilder.addColumns(value);
        }
        
        builder.addValues(valueRowBuilder.build());
        log.info("No physical disks found via IPMI SDR scan");
    }

    /**
     * Add error entry when collection fails
     */
    private void addErrorEntry(CollectRep.MetricsData.Builder builder, Metrics metrics, String errorMessage) {
        CollectRep.ValueRow.Builder valueRowBuilder = CollectRep.ValueRow.newBuilder();
        
        for (Metrics.Field field : metrics.getFields()) {
            String value;
            switch (field.getField()) {
                case "disk_name":
                    value = "Error";
                    break;
                case "disk_status":
                    value = "Error: " + errorMessage;
                    break;
                case "disk_health":
                    value = "Error";
                    break;
                default:
                    value = CommonConstants.NULL_VALUE;
                    break;
            }
            valueRowBuilder.addColumns(value);
        }
        
        builder.addValues(valueRowBuilder.build());
        log.error("Added error entry for physical disk collection: {}", errorMessage);
    }
}
