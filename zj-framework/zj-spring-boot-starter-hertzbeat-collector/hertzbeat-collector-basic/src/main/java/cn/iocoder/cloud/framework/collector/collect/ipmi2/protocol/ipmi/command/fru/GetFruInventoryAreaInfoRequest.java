/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi.command.fru;

import cn.iocoder.cloud.framework.collector.collect.ipmi2.client.IpmiPacketContext;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi.IpmiCommandName;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi.command.AbstractIpmiRequest;

import java.nio.ByteBuffer;

/**
 * Get FRU Inventory Area Info Request
 * See IPMIv2 Section 34.1
 * 
 * This command returns the size of the FRU Inventory Area and whether the device
 * is accessed by bytes or 16-bit words.
 */
public class GetFruInventoryAreaInfoRequest extends AbstractIpmiRequest {

    /**
     * FRU Device ID
     * 00h = FRU for this management controller
     * 01h-FEh = FRU for other devices
     * FFh = reserved
     */
    private final byte fruDeviceId;

    /**
     * Constructor
     * 
     * @param fruDeviceId FRU Device ID (0x00-0xFE)
     */
    public GetFruInventoryAreaInfoRequest(byte fruDeviceId) {
        this.fruDeviceId = fruDeviceId;
    }

    /**
     * Default constructor for FRU Device ID 0 (this management controller)
     */
    public GetFruInventoryAreaInfoRequest() {
        this.fruDeviceId = 0x00;
    }

    @Override
    public int getDataWireLength(IpmiPacketContext context) {
        return 1; // Only 1 byte for FRU Device ID
    }

    @Override
    public void toWireData(IpmiPacketContext context, ByteBuffer buffer) {
        buffer.put(fruDeviceId);
    }

    @Override
    public IpmiCommandName getCommandName() {
        return IpmiCommandName.GetFruInventoryAreaInfo;
    }

    /**
     * Get the FRU Device ID
     * 
     * @return FRU Device ID
     */
    public byte getFruDeviceId() {
        return fruDeviceId;
    }

    @Override
    public String toString() {
        return String.format("GetFruInventoryAreaInfoRequest{fruDeviceId=0x%02X}", fruDeviceId);
    }
}
