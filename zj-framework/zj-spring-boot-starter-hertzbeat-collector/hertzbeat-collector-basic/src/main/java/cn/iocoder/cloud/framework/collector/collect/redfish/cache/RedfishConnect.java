/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.collector.collect.redfish.cache;

import cn.iocoder.cloud.framework.collector.collect.common.cache.AbstractConnection;
import cn.iocoder.cloud.framework.collector.collect.redfish.ConnectSession;
import lombok.extern.slf4j.Slf4j;

/**
 * redfish connect session
 */
@Slf4j
public class RedfishConnect extends AbstractConnection<ConnectSession> {
    private final ConnectSession reddishConnectSession;

    public RedfishConnect(ConnectSession reddishConnectSession) {
        this.reddishConnectSession = reddishConnectSession;
    }

    @Override
    public void closeConnection() throws Exception {
        if (reddishConnectSession != null) {
            reddishConnectSession.close();
        }
    }

    @Override
    public ConnectSession getConnection() {
        return reddishConnectSession;
    }
}
