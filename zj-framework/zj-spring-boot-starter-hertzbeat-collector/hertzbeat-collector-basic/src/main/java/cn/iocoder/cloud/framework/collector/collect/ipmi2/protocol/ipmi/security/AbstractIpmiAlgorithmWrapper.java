/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.ipmi.security;

import cn.iocoder.cloud.framework.collector.collect.ipmi2.client.IpmiPacketContext;
import cn.iocoder.cloud.framework.collector.collect.ipmi2.protocol.common.AbstractWireable;

import java.nio.ByteBuffer;

/**
 *  See IPMIv2 Section 13.19
 */
public abstract class AbstractIpmiAlgorithmWrapper extends AbstractWireable implements IpmiAlgorithm {
    @Override
    public int getWireLength(IpmiPacketContext context) {
        return 8;
    }

    @Override
    public void toWire(IpmiPacketContext context, ByteBuffer buffer) {
        buffer.put(getPayloadType());
        buffer.putShort((short) 0);
        buffer.put((byte) 8);
        buffer.put(getCode());
        buffer.put((byte) 0);
        buffer.putShort((short) 0);
    }

    @Override
    public void fromWire(IpmiPacketContext context, ByteBuffer buffer) {
        buffer.get();
    }
}
