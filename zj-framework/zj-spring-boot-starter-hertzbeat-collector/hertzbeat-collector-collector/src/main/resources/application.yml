# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
server:
  port: 1159
  shutdown: graceful
  tomcat:
    threads:
      min-spare: 1
spring:
  application:
    name: ${HOSTNAME:@hertzbeat-collector@}${PID}
  profiles:
    active: cluster
  lifecycle:
    timeout-per-shutdown-phase: 10s
  jackson:
    default-property-inclusion: ALWAYS
  # need to disable spring boot mongodb auto config, or default mongodb connection tried and failed...
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration, org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration, org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration

---
spring:
  config:
    activate:
      on-profile: cluster

collector:
  info:
    version: ${COLLECTOR_VERSION:1.6.1}
    ip: ${COLLECTOR_IP:}
  dispatch:
    entrance:
      netty:
        enabled: true
        # Config the collector unique identity name.
        identity: ${IDENTITY:}
        # Config the running mode(public or private), public cluster or private cloud-edge.
        mode: ${MODE:public}
        manager-host: ${MANAGER_HOST:}${MANAGER_IP:}
        manager-port: ${MANAGER_PORT:1158}

push:
  uri: "127.0.0.1:1157"


common:
  queue:
    type: netty
