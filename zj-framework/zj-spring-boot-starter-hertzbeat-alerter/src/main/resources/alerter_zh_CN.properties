# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

alerter.availability.recover = å¯ç¨æ§åè­¦æ¢å¤éç¥, ä»»å¡ç¶æå·²æ¢å¤æ­£å¸¸
alerter.alarm.recover = åè­¦æ¢å¤éç¥
alerter.notify.title = HertzBeatåè­¦éç¥
alerter.notify.target = åè­¦ç®æ å¯¹è±¡
alerter.notify.monitorId = æå±çæ§ä»»å¡ID
alerter.notify.monitorName = æå±ä»»å¡åç§°
alerter.notify.priority = åè­¦çº§å«
alerter.notify.triggerTime = åè­¦è§¦åæ¶é´
alerter.notify.times = åè­¦è§¦åæ¬¡æ°
alerter.notify.tags = åè­¦æ ç­¾
alerter.notify.content = åå®¹è¯¦æ
alerter.notify.console = ç»å¥æ§å¶å°
alerter.priority.0 = ç´§æ¥åè­¦
alerter.priority.1 = ä¸¥éåè­¦
alerter.priority.2 = è­¦ååè­¦
