package cn.iocoder.cloud.framework.alert.framework.rpc;

import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.report.api.subscription.dto.ReportSubscriptionApi;
import cn.iocoder.zj.module.system.api.mail.MailSendApi;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.tenant.TenantApi;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.wechat.WeChatSendApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName : config  //类名
 * @Description : RPC 接口  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/3/22  16:44
 */

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {
        PlatformconfigApi.class,
        RoleApi.class,
        PermissionApi.class,
        AlarmConfigApi.class,
        ReportSubscriptionApi.class,
        WeChatSendApi.class,
        AdminUserApi.class,
        MailSendApi.class,
        TenantApi.class})
public class RpcConfiguration {

}
