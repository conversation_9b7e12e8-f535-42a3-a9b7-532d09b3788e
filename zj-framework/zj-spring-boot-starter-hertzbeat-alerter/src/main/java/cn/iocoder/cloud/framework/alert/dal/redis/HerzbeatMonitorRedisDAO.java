package cn.iocoder.cloud.framework.alert.dal.redis;

import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import com.alibaba.fastjson.JSONObject;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

import static cn.iocoder.cloud.framework.alert.dal.redis.RedisKeyConstants.HERZBEAT_MONITOR_INFO;
@Repository
public class HerzbeatMonitorRedisDAO {
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    public JSONObject get(String key) {
        String redisKey = formatKey(key);
        return JsonUtils.parseObject(stringRedisTemplate.opsForValue().get(redisKey), JSONObject.class);
    }
    public void set(String key, Monitor monitor) {
        String redisKey = formatKey(key);
        stringRedisTemplate.opsForValue().set(redisKey, JsonUtils.toJsonString(monitor));
    }
    public void delete(String key) {
        String redisKey = formatKey(key);
        stringRedisTemplate.delete(redisKey);
    }
    private static String formatKey(String accessToken) {
        return String.format(HERZBEAT_MONITOR_INFO, accessToken);
    }

}
