/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.alert.service.impl;


import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import cn.iocoder.cloud.framework.alert.dao.AlertDao;
import cn.iocoder.cloud.framework.alert.dao.AlertMonitorDao;
import cn.iocoder.cloud.framework.alert.dto.AlertPriorityNum;
import cn.iocoder.cloud.framework.alert.dto.AlertSummary;
import cn.iocoder.cloud.framework.alert.reduce.AlarmCommonReduce;
import cn.iocoder.cloud.framework.alert.service.AlertService;
import com.baomidou.dynamic.datasource.annotation.Slave;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.constants.CommonConstants;
import org.apache.hertzbeat.common.entity.alerter.Alert;
import org.apache.hertzbeat.common.entity.dto.AlertReport;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * Realization of Alarm Information Service
 *
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class AlertServiceImpl implements AlertService {

    @Autowired
    private AlertDao alertDao;

    @Autowired
    private AlarmCommonReduce alarmCommonReduce;
    @Autowired
    AlertMonitorDao alertMonitorDao;


    @Override
    @Slave
    public Map<String, Object> addAlert(Alert alert) throws RuntimeException {
        Map<String, Object> result = new HashMap<>();
        Long maxid = alertDao.findMaxId();
        if (maxid == null) {
            alert.setId(1l);
        } else {
            alert.setId(maxid + 1);
        }

        // 查询监控告警中该条数据详情
        Alert alert1 = alertDao.findTopByMonitorIdAndAlarmIdOrderByLastAlarmTimeDesc(alert.getTags().get("monitorId"), alert.getAlarmId());
        if (alert1 != null) {
            if (alert1.getIsSolved() == 0 || alert1.getIsSolved() == 1) {
                alert.setAlarmId(alert.getAlarmId());
                alert1.setLastAlarmTime(alert.getLastAlarmTime());
                alert1.setContent(alert.getContent());
                alert1.setAlarmRule(alert.getAlarmRule());
                alert1.setAlarmConfigName(alert.getAlarmConfigName());
                alert1.setMonitorName(alert.getTags().get("monitorName"));
                alert1.setTimes(alert1.getTimes() + 1);
                alertDao.save(alert1);
                result.put("type", "update");
                result.put("alertId", alert1.getId());
                return result;
            }
        }
        //
        alert.setResourceType(1);
        alert.setAlarmId(alert.getAlarmId());
        alert.setAlarmRule(alert.getAlarmRule());
        alert.setAlarmConfigName(alert.getAlarmConfigName());
        alert.setApp(alert.getTags().get("app"));
        alert.setMonitorId(alert.getTags().get("monitorId"));
        alert.setMonitorName(alert.getTags().get("monitorName"));
        alert.setIsSolved((byte) 0);
        alertDao.save(alert);
        result.put("type", "insert");
        result.put("alertId", alert.getId());
        return result;
    }

    @Override
    @Slave
    public Page<Alert> getAlerts(Specification<Alert> specification, PageRequest pageRequest) {
        return alertDao.findAll(specification, pageRequest);
    }

    @Override
    @Slave
    public void deleteAlerts(HashSet<Long> ids) {
        alertDao.deleteAlertsByIdIn(ids);
    }

    @Override
    @Slave
    public void clearAlerts() {
        alertDao.deleteAll();
    }

    @Override
    @Slave
    public void editAlertStatus(Byte status, List<Long> ids) {
        if (status==3){
            Byte isSolved=2;
            alertDao.updateAlertsStatusAndIsFallBack(status,isSolved,ids);
        }else {
            alertDao.updateAlertsStatus(status, ids);
        }
    }

    @Override
    @Slave
    public AlertSummary getAlertsSummary() {
        AlertSummary alertSummary = new AlertSummary();
        // Statistics on the alarm information in the alarm state
        List<AlertPriorityNum> priorityNums = alertDao.findAlertPriorityNum();
        if (priorityNums != null) {
            for (AlertPriorityNum priorityNum : priorityNums) {
                switch (priorityNum.getPriority()) {
                    case CommonConstants
                            .ALERT_PRIORITY_CODE_WARNING:
                        alertSummary.setPriorityWarningNum(priorityNum.getNum());
                        break;
                    case CommonConstants.ALERT_PRIORITY_CODE_CRITICAL:
                        alertSummary.setPriorityCriticalNum(priorityNum.getNum());
                        break;
                    case CommonConstants.ALERT_PRIORITY_CODE_EMERGENCY:
                        alertSummary.setPriorityEmergencyNum(priorityNum.getNum());
                        break;
                    default:
                        break;
                }
            }
        }
        long total = alertDao.count();
        alertSummary.setTotal(total);
        long dealNum = total - alertSummary.getPriorityCriticalNum()
                - alertSummary.getPriorityEmergencyNum() - alertSummary.getPriorityWarningNum();
        alertSummary.setDealNum(dealNum);
        try {
            if (total == 0) {
                alertSummary.setRate(100);
            } else {
                float rate = BigDecimal.valueOf(100 * (float) dealNum / total)
                        .setScale(2, RoundingMode.HALF_UP)
                        .floatValue();
                alertSummary.setRate(rate);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return alertSummary;
    }

    @Override
    @Slave
    public void addNewAlertReport(AlertReport alertReport) {
        alarmCommonReduce.reduceAndSendAlarm(buildAlertData(alertReport));
    }

    @Override
    @Slave
    public List<Alert> getAlerts(Specification<Alert> specification) {

        return alertDao.findAll(specification);
    }

    @Override
    @Slave
    public Alert getAlertsInfo(Long ids) {
        return alertDao.findById(ids).get();
    }

    /**
     * The external alarm information is converted to Alert
     *
     * @param alertReport alarm body
     * @return Alert entity
     */
    private Alert buildAlertData(AlertReport alertReport) {
        Map<String, String> annotations = alertReport.getAnnotations();
        StringBuilder sb = new StringBuilder();
        if (alertReport.getContent() == null || alertReport.getContent().length() <= 0) {
            StringBuilder finalSb = sb;
            annotations.forEach((k, v) -> {
                finalSb.append(k).append(":").append(v).append("\n");
            });
        } else {
            sb = new StringBuilder(alertReport.getContent());
        }
        LocalDateTime dateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(alertReport.getAlertTime()),
                ZoneId.systemDefault());
        return Alert.builder()
                .content("Alert Center\n" + sb)
                .priority(alertReport.getPriority().byteValue())
                .status(CommonConstants.ALERT_STATUS_CODE_PENDING)
                .tags(alertReport.getLabels())
                .target(alertReport.getAlertName())
                .triggerTimes(1)
                .firstAlarmTime(alertReport.getAlertTime())
                .lastAlarmTime(alertReport.getAlertTime())
                .gmtCreate(dateTime)
                .gmtUpdate(dateTime)
                .build();
    }


    @Override
    public void alertsnmpTrap(String platformName, String hostName, String json) {
        log.info("接收到的json数据：" + json + "平台名称:" + platformName + "主机地址：" + hostName);
        Long platformId = alertMonitorDao.findPlatformName(platformName);
        // 根据ip找到相关的监控基础信息
        List<Monitor> monitors = alertMonitorDao.findMonitorsByPlatformIdAndHost(platformId, hostName);
        saveAllList(monitors, json);
    }

    @Slave
    private void saveAllList(List<Monitor> monitors, String json) {
        List<Alert> list = new ArrayList<>();

        if (!monitors.isEmpty()) {
            JSONArray array = JSONUtil.parseArray(json);
            List<Map> mapList = JSONUtil.toList(array, Map.class);
            for (Monitor monitor : monitors) {
                for (Map maps : mapList) {
                    Long maxid = alertDao.findMaxId();

                    Map<String, String> map = new HashMap<>();
                    map.put("app", monitor.getApp());
                    map.put("monitorId", Convert.toStr(monitor.getId()));
                    Alert alert = new Alert();
                    if (maxid == null) {
                        alert.setId(1l);
                    } else {
                        alert.setId(maxid + 1);
                    }
                    alert.setApp(monitor.getApp());
                    alert.setMonitorName(monitor.getName());
                    alert.setMonitorId(Convert.toStr(monitor.getId()));
                    alert.setFirstAlarmTime(DateUtil.current());
                    alert.setLastAlarmTime(DateUtil.current());
                    LocalDateTime dateTime = LocalDateTimeUtil.of(new Date());
                    alert.setGmtCreate(dateTime);
                    alert.setGmtUpdate(dateTime);
                    alert.setPriority(Convert.toByte(2));
                    alert.setTags(map);
                    alert.setTarget("SNMP");
                    alert.setTimes(1);
                    alert.setPlatformId(monitor.getPlatformId());
                    alert.setPlatformName(monitor.getPlatformName());
                    alert.setResourceType(3);
                    alert.setStatus(Convert.toByte(0));
                    alert.setAlarmName("SNMPTrap告警");
                    alert.setAlarmId(Convert.toStr(288));
                    alert.setIsSolved(Convert.toByte(0));
                    alert.setIsFallBack(0);
                    String content = Convert.toStr(maps.get("Name")) + " " + Convert.toStr(maps.get("Value"));
                    alert.setContent(content);
                    list.add(alert);
                }
            }
            alertDao.saveAll(list);
        }
    }

}
