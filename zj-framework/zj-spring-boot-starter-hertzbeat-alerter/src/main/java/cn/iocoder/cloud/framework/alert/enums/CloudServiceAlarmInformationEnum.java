package cn.iocoder.cloud.framework.alert.enums;

import cn.iocoder.cloud.framework.alert.dto.CloudAlertReportAbstract;
import cn.iocoder.cloud.framework.alert.dto.TenCloudAlertReport;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * Cloud server alarm enum
 */
@AllArgsConstructor
@Getter
public enum CloudServiceAlarmInformationEnum {

    TencentCloud("tencloud", TenCloudAlertReport.class);

    /**
     * cloud service name
     */
    private final String cloudServiceName;

    /**
     * cloud service body
     */
    private final Class<? extends CloudAlertReportAbstract> cloudServiceAlarmInformationEntity;

    public static CloudServiceAlarmInformationEnum getEnumFromCloudServiceName(String name) {
        return Arrays.stream(CloudServiceAlarmInformationEnum.values())
                .filter(cloudService -> cloudService.cloudServiceName.equals(name))
                .findFirst()
                .orElse(null);
    }

}
