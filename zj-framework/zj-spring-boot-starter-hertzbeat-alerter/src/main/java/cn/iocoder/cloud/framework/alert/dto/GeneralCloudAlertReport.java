package cn.iocoder.cloud.framework.alert.dto;

import cn.iocoder.cloud.framework.alert.util.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.hertzbeat.common.entity.dto.AlertReport;

import java.util.Optional;

/**
 * 通用云端告警实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class GeneralCloudAlertReport extends AlertReport {

    /**
     * Alarm date and time
     */
    private String alertDateTime;

    /**
     * DATE TIME FORMAT
     */
    private String dateTimeFormat;

    /**
     * You can refresh the timestamp of the alarm time with enhanced properties
     */
    public void refreshAlertTime() {
        if (getAlertTime() != 0L) {
            return;
        }
        if (StringUtils.isNotBlank(alertDateTime)) {
            Long timeStamp = null;
            if (StringUtils.isNotBlank(dateTimeFormat)) {
                Optional<Long> tsf = DateUtil.getTimeStampFromFormat(alertDateTime, dateTimeFormat);
                boolean present = tsf.isPresent();
                if (present) {
                    timeStamp = tsf.get();
                }
            }
            if (timeStamp == null) {
                Optional<Long> tsf = DateUtil.getTimeStampFromSomeFormats(alertDateTime);
                boolean present = tsf.isPresent();
                if (present) {
                    timeStamp = tsf.get();
                }
            }
            if (timeStamp != null) {
                setAlertTime(timeStamp);
                return;
            }
        }
        throw new RuntimeException("parse alarm time error");
    }

}
