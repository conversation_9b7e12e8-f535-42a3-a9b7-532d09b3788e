/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.alert.controller.admin;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.cloud.framework.alert.service.AlertDefineService;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.exception.ErrorCode;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.tenant.TenantApi;
import cn.iocoder.zj.module.system.api.tenant.dto.TenantUserDTO;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.hertzbeat.common.entity.alerter.AlertDefine;
import org.apache.hertzbeat.common.entity.dto.Message;
import org.jsoup.internal.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.singleton;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

/**
 * Define the batch API for alarms
 * <AUTHOR>
 */
@Tag(name = "Alert Define Batch API | 告警定义管理API")
@RestController
@RequestMapping(path = "/cloudedge/api/alert/defines", produces = {APPLICATION_JSON_VALUE})
public class AlertDefinesController {

    @Autowired
    private AlertDefineService alertDefineService;
    @Resource
    PermissionApi permissionApi;
    @Resource
    RoleApi roleApi;
    @Resource
    TenantApi tenantApi;

    @Resource
    AdminUserApi adminUserApi;

    @GetMapping
    @Operation(summary = "Example Query the alarm definition list ｜ 查询告警定义列表",
            description = "You can obtain the list of alarm definitions by querying filter items ｜ 根据查询过滤项获取告警定义信息列表")
    public ResponseEntity<Message<Page<AlertDefine>>> getAlertDefines(
            @Parameter(description = "Alarm Definition ID ｜ 告警定义ID", example = "6565463543") @RequestParam(required = false) List<Long> ids,
            @Parameter(description = "Search-Target Expr Template ｜ 模糊查询-指标对象 表达式 通知模版", example = "x") @RequestParam(required = false) String search,
            @Parameter(description = "Alarm Definition Severity ｜ 告警定义级别", example = "6565463543") @RequestParam(required = false) Byte priority,
            @Parameter(description = "Resource type | 资源类型", example = "host") @RequestParam(required = false) String app,
            @Parameter(description = "Sort field, default id ｜ 排序字段，默认gmtCreate", example = "gmtCreate") @RequestParam(defaultValue = "gmtCreate") String sortBy,
            @Parameter(description = "Sort mode: asc: ascending, desc: descending ｜ 排序方式，asc:升序，desc:降序", example = "desc") @RequestParam(defaultValue = "desc") String sortDirection,
            @Parameter(description = "List current page ｜ 列表当前分页", example = "0") @RequestParam(defaultValue = "0") int pageIndex,
            @Parameter(description = "Number of list pages ｜ 列表分页数量", example = "8") @RequestParam(defaultValue = "8") int pageSize,
            @Parameter(description = "租户ID", example = "1") @RequestParam(required = false) String maintainerIds,
            @Parameter(description = "告警名称", example = "1") @RequestParam(required = false) String name,
            @Parameter(description = "开始时间", example = "2024-01-01") @DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam(required = false) String startTime,
            @Parameter(description = "结束时间", example = "2024-12-31") @DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam(required = false) String endTime) {

        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        AdminUserRespDTO adminUserRespDTO = adminUserApi.getUser(loginUser.getId()).getData();
//        boolean notSuperAdmin;
//        if (!roleApi.hasAnySuperAdmin(roleIds)) {
//            // 不是超级管理员 根据用户查询
//            notSuperAdmin = true;
//        } else {
//            notSuperAdmin = false;
//        }
        List<Long> tenantList = new ArrayList<>();


        //判断是否为租户管理员（租户）
        Set<Long> tenantAdminRoles = roleApi.getRoleIdByCode("tenant_admin").getData();
        boolean tenantAdmin = roleIds.stream().anyMatch(tenantAdminRoles::contains);

        //判断是否为超管或超管下创建的运维管理、运维人员
        Boolean isRootOperation = roleApi.getIsRootOperation(loginUser.getId());
        Set<Long> maintenanceRoles = roleApi.getRoleIdByCode("operation_maintenance").getData();
        Set<Long> managerRoles = roleApi.getRoleIdByCode("om_manager").getData();
        Set<Long> adminRoles = roleApi.getRoleIdByCode("super_admin").getData();
        Set<Long> csAdminRoles = roleApi.getRoleIdByCode("cs_admin").getData();
        boolean isMaintenance = roleIds.stream().anyMatch(maintenanceRoles::contains);
        boolean isManager = roleIds.stream().anyMatch(managerRoles::contains);
        boolean isAdmin = roleIds.stream().anyMatch(adminRoles::contains);
        boolean csAdmin = roleIds.stream().anyMatch(csAdminRoles::contains);

        if (tenantAdmin){
            tenantList=Arrays.asList(adminUserRespDTO.getTenantId());
        }else if (isRootOperation){
            if (!StringUtil.isBlank(maintainerIds)) {
                List<Long> tenantIds = Arrays.stream(maintainerIds.split(","))
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
                tenantList=tenantIds;
            } else {
                if (isManager || isAdmin ||csAdmin) {
                    tenantList = tenantApi.getTenantIdsByPlatform(adminUserRespDTO.getTenantId()).getData();
                } else if (isMaintenance) {
                    String serviceTenantId = adminUserRespDTO.getServiceTenantId();
                    if (!StringUtil.isBlank(serviceTenantId)) {
                        tenantList = Arrays.stream(serviceTenantId.split(","))
                                .map(Long::parseLong)
                                .collect(Collectors.toList());
                    }
                }
            }
        }
        List<Long> finalTenantList = tenantList;
        Specification<AlertDefine> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> andList = new ArrayList<>();
            if (ids != null && !ids.isEmpty()) {
                CriteriaBuilder.In<Long> inPredicate = criteriaBuilder.in(root.get("id"));
                for (long id : ids) {
                    inPredicate.value(id);
                }
                andList.add(inPredicate);
            }
            CriteriaBuilder.In<Long> inPredicate = criteriaBuilder.in(root.get("tenantId"));
            for (Long tenantId : finalTenantList) {
                            inPredicate.value(tenantId);
                        }
            andList.add(inPredicate);
            // 不是超级管理员根据用户id查询模版中数据
            if (StringUtils.hasText(search)) {
                Predicate predicate = criteriaBuilder.or(
                        criteriaBuilder.like(
                                criteriaBuilder.lower(root.get("app")),
                                "%" + search.toLowerCase() + "%"
                        ),
                        criteriaBuilder.like(
                                criteriaBuilder.lower(root.get("metric")),
                                "%" + search.toLowerCase() + "%"
                        ),
                        criteriaBuilder.like(
                                criteriaBuilder.lower(root.get("field")),
                                "%" + search.toLowerCase() + "%"
                        ),
                        criteriaBuilder.like(
                                criteriaBuilder.lower(root.get("expr")),
                                "%" + search.toLowerCase() + "%"
                        ),
                        criteriaBuilder.like(
                                criteriaBuilder.lower(root.get("template")),
                                "%" + search.toLowerCase() + "%"
                        )
                );
                andList.add(predicate);
            }
            if (priority != null) {
                Predicate predicate = criteriaBuilder.equal(root.get("priority"), priority);
                andList.add(predicate);
            }
            if (app != null) {
                Predicate predicate = criteriaBuilder.equal(root.get("app"), app);
                andList.add(predicate);
            }
            if (StringUtils.hasText(startTime) && StringUtils.hasText(endTime)) {
                try {
                    // 定义格式
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

                    // 解析为LocalDateTime
                    LocalDateTime startDateTime = LocalDateTime.parse(startTime, formatter);
                    LocalDateTime endDateTime = LocalDateTime.parse(endTime, formatter);

                    // 添加时间范围条件
                    andList.add(criteriaBuilder.greaterThanOrEqualTo(root.get("gmtCreate"), startDateTime));
                    andList.add(criteriaBuilder.lessThanOrEqualTo(root.get("gmtCreate"), endDateTime));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (StringUtils.hasText(name)){
                Predicate predicate = criteriaBuilder.or(
                        criteriaBuilder.like(
                                criteriaBuilder.lower(root.get("name")),
                                "%" + name.toLowerCase() + "%"
                        )
                );
                andList.add(predicate);
            }
            Predicate[] predicates = new Predicate[andList.size()];
            return criteriaBuilder.and(andList.toArray(predicates));
        };
        Sort sortExp = Sort.by(new Sort.Order(Sort.Direction.fromString(sortDirection), sortBy));
        PageRequest pageRequest = PageRequest.of(pageIndex, pageSize, sortExp);
        Page<AlertDefine> alertDefinePage = alertDefineService.getAlertDefines(specification, pageRequest);
        //获取所有租户和对应的姓名
        List<TenantUserDTO> userList = tenantApi.getTenantAllIds().getData();
        List<AlertDefine> content = alertDefinePage.getContent();
        content.forEach(alertDefine -> {
            userList.stream()
                    .filter(item -> alertDefine.getTenantId().toString().equals(item.getId()))
                    .findFirst()
                    .ifPresent(item -> alertDefine.setTenantName(item.getName()));
        });
        for (AlertDefine alertDefine : content) {
            if (ObjectUtil.isEmpty(alertDefine.getTenantName())){
                alertDefine.setTenantName("-");
            }
        }
        Page<AlertDefine> newAlertDefinePage = new PageImpl<>(content, pageRequest, alertDefinePage.getTotalElements());
        return ResponseEntity.ok(Message.success(newAlertDefinePage));
    }

    @DeleteMapping
    @Operation(summary = "Delete alarm definitions in batches ｜ 批量删除告警定义",
            description = "Delete alarm definitions in batches based on the alarm definition ID list ｜ 根据告警定义ID列表批量删除告警定义")
    public ResponseEntity<Message<Void>> deleteAlertDefines(
            @Parameter(description = "Alarm Definition IDs ｜ 告警定义IDs", example = "6565463543") @RequestParam(required = false) List<Long> ids
    ) {
        if (ids != null && !ids.isEmpty()) {
            alertDefineService.deleteAlertDefines(new HashSet<>(ids));
        }
        return ResponseEntity.ok(Message.success());
    }

    @GetMapping("/export")
    @Operation(summary = "export alertDefine config", description = "export alarm definition configuration")
    @PreAuthorize("@ss.hasPermission('monitor:alarm-config:export')")
    public void export(
            @Parameter(description = "AlertDefine ID List", example = "656937901") @RequestParam(required = false) List<Long> ids,
            @Parameter(description = "Export Type:JSON,EXCEL,YAML") @RequestParam(defaultValue = "JSON") String type,
            @Parameter(description = "Search-Target Expr Template ｜ 模糊查询-指标对象 表达式 通知模版", example = "x") @RequestParam(required = false) String search,
            @Parameter(description = "Alarm Definition Severity ｜ 告警定义级别", example = "6565463543") @RequestParam(required = false) Byte priority,
            HttpServletResponse res) throws Exception {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        boolean notSuperAdmin;
        if (!roleApi.hasAnySuperAdmin(roleIds)) {
            // 不是超级管理员 根据用户查询
            notSuperAdmin = true;
        } else {
            notSuperAdmin = false;
        }

        Specification<AlertDefine> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> andList = new ArrayList<>();
            if (ids != null && !ids.isEmpty()) {
                CriteriaBuilder.In<Long> inPredicate = criteriaBuilder.in(root.get("id"));
                for (long id : ids) {
                    inPredicate.value(id);
                }
                andList.add(inPredicate);
            }
            // 不是超级管理员根据用户id查询模版中数据
            if (notSuperAdmin){
                Predicate predicate = criteriaBuilder.equal(root.get("tenantId"), loginUser.getTenantId());
                andList.add(predicate);
            }else {
                Predicate predicate = criteriaBuilder.equal(root.get("tenantId"), loginUser.getTenantId());
                Predicate predicate2 = criteriaBuilder.equal(root.get("tenantId"),0);
                Predicate combinedPredicate = criteriaBuilder.or(predicate, predicate2);
                andList.add(combinedPredicate);
            }
            if (StringUtils.hasText(search)) {
                Predicate predicate = criteriaBuilder.or(
                        criteriaBuilder.like(
                                criteriaBuilder.lower(root.get("app")),
                                "%" + search.toLowerCase() + "%"
                        ),
                        criteriaBuilder.like(
                                criteriaBuilder.lower(root.get("metric")),
                                "%" + search.toLowerCase() + "%"
                        ),
                        criteriaBuilder.like(
                                criteriaBuilder.lower(root.get("field")),
                                "%" + search.toLowerCase() + "%"
                        ),
                        criteriaBuilder.like(
                                criteriaBuilder.lower(root.get("expr")),
                                "%" + search.toLowerCase() + "%"
                        ),
                        criteriaBuilder.like(
                                criteriaBuilder.lower(root.get("template")),
                                "%" + search.toLowerCase() + "%"
                        )
                );
                andList.add(predicate);
            }
            if (priority != null) {
                Predicate predicate = criteriaBuilder.equal(root.get("priority"), priority);
                andList.add(predicate);
            }
            Predicate[] predicates = new Predicate[andList.size()];
            return criteriaBuilder.and(andList.toArray(predicates));
        };
        alertDefineService.export(ids, type, res,specification);
    }


    @PostMapping("/import")
    @Operation(summary = "import alertDefine config", description = "import alarm definition configuration")
    @PreAuthorize("@ss.hasPermission('monitor:alarm-config:import')")
    public ResponseEntity<Message<Void>> importDefines(MultipartFile file) throws Exception {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        try {
            alertDefineService.importConfig(file,loginUser.getTenantId());
        } catch (Exception e) {
            throw exception(new ErrorCode(2008000000,"导入文件格式有误"));
        }
        return ResponseEntity.ok(Message.success("Import success"));
    }
    
}
