/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.alert.service;

import org.apache.hertzbeat.common.entity.alerter.AlertDefine;
import org.springframework.data.jpa.domain.Specification;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;


/**
 * Configuration Import Export
 */
public interface AlertDefineImExportService {
    /**
     * Import Configuration
     * @param is input stream
     */
    void importConfig(InputStream is,Long tenantId);

    /**
     * Export Configuration
     * @param os         output stream
     * @param configList configuration list
     */
    void exportConfig(OutputStream os, List<Long> configList, Specification<AlertDefine> specification);

    /**
     * Export file type
     * @return file type
     */
    String type();

    /**
     * Get Export File Name
     * @return file name
     */
    String getFileName();
}


