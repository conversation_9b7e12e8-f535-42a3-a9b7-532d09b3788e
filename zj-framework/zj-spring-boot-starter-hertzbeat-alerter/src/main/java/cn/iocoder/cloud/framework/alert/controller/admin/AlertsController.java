/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.alert.controller.admin;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.cloud.framework.alert.dto.AlertSummary;
import cn.iocoder.cloud.framework.alert.service.AlertService;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.util.collection.CollectionUtils;
import cn.iocoder.zj.framework.common.util.string.StringUtil;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.tenant.TenantApi;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.alerter.Alert;
import org.apache.hertzbeat.common.entity.alerter.AlertRespVo;
import org.apache.hertzbeat.common.entity.dto.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.singleton;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

/**
 * Alarm Management API
 *
 * <AUTHOR>
 * <AUTHOR> href="mailto:<EMAIL>">Hua.Cheng</a>
 */
@Tag(name = "Alarm Manage Batch API | 告警批量管理API")
@RestController
@Slf4j
@RequestMapping(path = "/cloudedge/api/alerts", produces = {APPLICATION_JSON_VALUE})
public class AlertsController {

    @Autowired
    private AlertService alertService;
    @Autowired
    private PlatformconfigApi platformconfigApi;

    @Autowired
    private AdminUserApi adminUserApi;

    @Autowired
    private AlarmConfigApi alarmConfigApi;
    @Autowired
    PermissionApi permissionApi;
    @Autowired
    RoleApi roleApi;
    @Autowired
    TenantApi tenantApi;

    @GetMapping
    @Operation(summary = "Get a list of alarm information based on query filter items", description = "根据查询过滤项获取告警信息列表")
    public ResponseEntity<Message<?>> getAlerts(
            @Parameter(description = "Alarm ID List | 告警IDS", example = "6565466456") @RequestParam(required = false) List<Long> ids,
            @Parameter(description = "Alarm monitor object ID | 告警监控资源名称", example = "6565463543") @RequestParam(required = false) String monitorName,
            @Parameter(description = "Alarm monitor object ID | 告警监控对象ID", example = "6565463543") @RequestParam(required = false) String monitorId,
            @Parameter(description = "Alarm monitor object ID | 告警资源ID", example = "c02838ca-fe40-4c77-86a8-50feb4955b92") @RequestParam(required = false) String resourceId,
            @Parameter(description = "Alarm level | 告警级别", example = "6565463543") @RequestParam(required = false) Byte priority,
            @Parameter(description = "Alarm Status | 告警状态", example = "6565463543") @RequestParam(required = false) Byte status,
            @Parameter(description = "Alarm content fuzzy query | 告警内容模糊查询", example = "linux") @RequestParam(required = false) String content,
            @Parameter(description = "Sort field, default id | 排序字段，默认id", example = "name") @RequestParam(defaultValue = "id") String sortBy,
            @Parameter(description = "Sort Type | 排序方式，asc:升序，desc:降序", example = "desc") @RequestParam(defaultValue = "desc") String sortDirection,
            @Parameter(description = "Object‘s platform Id | 平台ID", example = "1") @RequestParam(required = false) Long platformId,
            @Parameter(description = "Object‘s platform name | 平台名称", example = "VMware") @RequestParam(required = false) String platformName,
            @Parameter(description = "Object‘s solved state | 流程状态", example = "1") @RequestParam(required = false) Integer isSolved,
            @Parameter(description = "Resource type | 资源类型", example = "host") @RequestParam(required = false) String app,
            @Parameter(description = "List current page | 列表当前分页", example = "0") @RequestParam(defaultValue = "0") int pageIndex,
            @Parameter(description = "Number of list pagination | 列表分页数量", example = "8") @RequestParam(defaultValue = "8") int pageSize,
            @Parameter(description = "Enable pagination | 启用分页", example = "true") @RequestParam(required = false, defaultValue = "true") boolean enablePagination) {

        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        AdminUserRespDTO adminUser = adminUserApi.getUser(loginUser.getId()).getData();

        long state = tenantApi.getStateByTenantId(adminUser.getTenantId()).getData();
        List<Map> platform = platformconfigApi.getPlatformSelectListByUserId(adminUser.getId()).getData();

//        List<Map> platform = platformconfigApi.getPlatformSelectList(String.valueOf(adminUser.getTenantId())).getData();


        Map<String, Map> platformMap = CollectionUtils.convertMap(platform, map -> String.valueOf(map.get("platformId")), map -> map);
        List<Map<String, Object>> collectorAlertMapList = new ArrayList<>();
        List<Alert> collectAlerts = new ArrayList<>();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        if (roleApi.hasAnyPlatformAdmin(roleIds) || roleApi.hasAnySuperAdmin(roleIds)) {
            if (pageSize < 10 && platform.size() > 0) {
                List<Long> values = platform.stream()
                        .map(map -> Long.parseLong(String.valueOf(map.get("platformId"))))
                        .collect(Collectors.toList());
                collectorAlertMapList = alarmConfigApi.getCollectorAlertsByPlatform(values).getData();
                for (Map<String, Object> alertMap : collectorAlertMapList) {
                    Alert collectAlert = new Alert();
                    collectAlert.setId(Long.parseLong(String.valueOf(alertMap.get("id"))));
                    collectAlert.setLastAlarmTime(Long.parseLong(String.valueOf(alertMap.get("create_time"))));
                    collectAlert.setMonitorId(String.valueOf(alertMap.get("collector_id")));
                    collectAlert.setContent(String.valueOf(alertMap.get("content")));
                    collectAlert.setResourceType(2);
                    collectAlert.setPriority(Byte.parseByte(String.valueOf(alertMap.get("priority"))));
                    collectAlert.setMonitorName(String.valueOf(alertMap.get("collector_name")));
                    collectAlert.setPlatformId(Long.parseLong(String.valueOf(alertMap.get("platform_id"))));
                    collectAlert.setPlatformName(String.valueOf(alertMap.get("platform_name")));
                    collectAlerts.add(collectAlert);
                }
                pageSize = pageSize - collectorAlertMapList.size();
            }
        }
        Specification<Alert> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> andList = new ArrayList<>();

            if (ids != null && !ids.isEmpty()) {
                CriteriaBuilder.In<Long> inPredicate = criteriaBuilder.in(root.get("id"));
                for (long id : ids) {
                    inPredicate.value(id);
                }
                andList.add(inPredicate);
            }
            if (monitorId != null) {
                Predicate predicate = criteriaBuilder.like(root.get("tags").as(String.class), "%" + monitorId + "%");
                andList.add(predicate);
            }
            if (platformId != null) {
                Predicate predicate = criteriaBuilder.equal(root.get("platformId"), platformId);
                andList.add(predicate);
            } else {
                CriteriaBuilder.In<Long> inPredicate = criteriaBuilder.in(root.get("platformId"));
                if (platform.size() > 0) {
                    for (Map map : platform) {
                        inPredicate.value(Convert.toLong(map.get("platformId")));
                    }
                }
                andList.add(inPredicate);
            }
            if (resourceId != null) {
                Predicate predicate = criteriaBuilder.equal(root.get("monitorId"), resourceId);
                andList.add(predicate);
            }
            if (state==1){
                CriteriaBuilder.In<String> inPredicate = criteriaBuilder.in(root.get("app"));
                inPredicate.value("host");
                inPredicate.value("hardware");
                inPredicate.value("storage");
                andList.add(criteriaBuilder.not(inPredicate));
            }
            if (app != null) {
                Predicate predicate = criteriaBuilder.equal(root.get("app"), app);
                andList.add(predicate);
            }
            if (priority != null) {
                Predicate predicate = criteriaBuilder.equal(root.get("priority"), priority);
                andList.add(predicate);
            }
            if (status != null) {
                Predicate predicate = criteriaBuilder.equal(root.get("status"), status);
                andList.add(predicate);
            }
            if (content != null && !content.isEmpty()) {
                Predicate predicate = criteriaBuilder.like(criteriaBuilder.lower(root.get("content")), "%" + content.toLowerCase() + "%");
                andList.add(predicate);
            }
            if (StringUtils.hasText(monitorName)) {
                Predicate predicateName = criteriaBuilder.like(root.get("monitorName"), "%" + monitorName + "%");
                andList.add(predicateName);
            }
            if (StringUtils.hasText(platformName)) {
                Predicate predicateName = criteriaBuilder.like(root.get("platformName"), "%" + platformName + "%");
                andList.add(predicateName);
            }
            if (ObjectUtil.isNotEmpty(isSolved)) {
                Predicate predicateContent = criteriaBuilder.equal(root.get("isSolved"), isSolved);
                andList.add(predicateContent);
            }
            // 条件排序表达式
            Expression<Object> priorityOrderExpression = criteriaBuilder.selectCase()
                    .when(criteriaBuilder.and(
                            criteriaBuilder.equal(root.get("isSolved"), 0),
                            criteriaBuilder.equal(root.get("app"), "collector"),
                            criteriaBuilder.equal(root.get("app"),"platform")), 0)
                    .otherwise(1);

            if (StringUtil.isNotEmpty(sortDirection)) {
                if (sortDirection.equals("asc")) {
                    query.orderBy(criteriaBuilder.asc(root.get("lastAlarmTime")));
                } else {
                    query.orderBy(criteriaBuilder.desc(root.get("lastAlarmTime")));
                }
            } else {
                query.orderBy(criteriaBuilder.desc(root.get("lastAlarmTime")));
            }

            Predicate[] predicates = new Predicate[andList.size()];
            return criteriaBuilder.and(andList.toArray(predicates));
        };
        if (enablePagination) {
            PageRequest pageRequest = PageRequest.of(pageIndex, pageSize);
            Page<Alert> alertPage = alertService.getAlerts(specification, pageRequest);
            collectAlerts.addAll(alertPage.toList());
            List<AlertRespVo> resp = new ArrayList<>();
            collectAlerts.forEach(item -> {
                AlertRespVo alertRespVo = new AlertRespVo();
                BeanUtil.copyProperties(item, alertRespVo);
                Map plat = platformMap.get(String.valueOf(item.getPlatformId()));
                String address = ObjectUtil.isNotEmpty(plat) ? (ObjectUtil.isNotEmpty(plat.get("address")) ? String.valueOf(plat.get("address")) : "") : "";
                alertRespVo.setAddress(address);
                resp.add(alertRespVo);
            });
            Page<AlertRespVo> mixAlerts = new PageImpl<>(resp, alertPage.getPageable(), alertPage.getTotalElements());
//          ResourceType为2的是采集器告警，始终在最前，其他类型的告警仅按照时间倒序排列
            Message<Page<AlertRespVo>> message = Message.success(mixAlerts);
            return ResponseEntity.ok(message);
        } else {
            List<Alert> alerts = alertService.getAlerts(specification);
            Message<List<Alert>> message = Message.success(alerts);
            return ResponseEntity.ok(message);
        }
    }

    @DeleteMapping
    @Operation(summary = "Delete alarms in batches", description = "根据告警ID列表批量删除告警")
    public ResponseEntity<Message<Void>> deleteAlerts(
            @Parameter(description = "Alarm List ID | 告警IDs", example = "6565463543") @RequestParam(required = false) List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            alertService.deleteAlerts(new HashSet<>(ids));
        }
        Message<Void> message = Message.success();
        return ResponseEntity.ok(message);
    }

    @DeleteMapping("/clear")
    @Operation(summary = "Delete alarms in batches", description = "清空所有告警信息")
    public ResponseEntity<Message<Void>> clearAllAlerts() {
        alertService.clearAlerts();
        Message<Void> message = Message.success();
        return ResponseEntity.ok(message);
    }

    @PutMapping(path = "/status/{status}")
    @Operation(summary = "Batch modify alarm status, set read and unread", description = "批量修改告警状态,设置已读未读")
    public ResponseEntity<Message<Void>> applyAlertDefinesStatus(
            @Parameter(description = "Alarm status value | 告警状态值", example = "0") @PathVariable Byte status,
            @Parameter(description = "Alarm List IDS | 告警IDS", example = "6565463543") @RequestParam(required = false) List<Long> ids) {
        if (ids != null && status != null && !ids.isEmpty()) {
            alertService.editAlertStatus(status, ids);
        }
        Message<Void> message = Message.success();
        return ResponseEntity.ok(message);
    }

    @GetMapping(path = "/summary")
    @Operation(summary = "Get alarm statistics", description = "获取告警统计信息")
    public ResponseEntity<Message<AlertSummary>> getAlertsSummary() {
        AlertSummary alertSummary = alertService.getAlertsSummary();
        Message<AlertSummary> message = Message.success(alertSummary);
        return ResponseEntity.ok(message);
    }


    @GetMapping(path = "/alertsinfo")
    @Operation(summary = "get alarms in info", description = "根据告警ID获取详情")
    public ResponseEntity<Message<Alert>> getAlertsInfo(
            @Parameter(description = "Alarm ID | 告警ID", example = "6565463543") @RequestParam(required = false) Long id) {
        Message<Alert> message = Message.success(alertService.getAlertsInfo(id));
        return ResponseEntity.ok(message);
    }


    @PostMapping(path = "/alertsnmpTrap")
    @Operation(summary = "alertsnmpTrap", description = "获取告警")
    public ResponseEntity<Message<Void>> alertsnmpTrap(
            @Parameter(description = "host | 主机地址 ", example = "127.0.0.1") @RequestParam(required = false) String hostName,
            @Parameter(description = "采集器地址", example = "136") @RequestParam(required = false) String platformName,
            @Parameter(description = "json | 告警trap Json", example = "6565463543") @RequestParam(required = false) String json) {
        if (hostName != null && json != null) {
            alertService.alertsnmpTrap(platformName, hostName, json);
        }

        Message<Void> message = Message.success();
        return ResponseEntity.ok(message);
    }

}
