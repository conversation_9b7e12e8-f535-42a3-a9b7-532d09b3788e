package cn.iocoder.cloud.framework.push.dao;

import org.apache.hertzbeat.common.entity.push.PushMetrics;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.transaction.annotation.Transactional;

/**
 * push metrics dao
 *
 * <AUTHOR>
 */
public interface PushMetricsDao extends JpaRepository<PushMetrics, Long> {
    PushMetrics findFirstByMonitorIdOrderByTimeDesc(Long monitorId);
    @Transactional
    void deleteAllByTimeBefore(Long time);
}
