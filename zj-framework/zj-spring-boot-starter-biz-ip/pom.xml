<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.iocoder.cloud</groupId>
        <artifactId>zj-framework</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>zj-spring-boot-starter-biz-ip</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>IP 拓展，支持如下功能：
        1. IP 功能：查询 IP 对应的城市信息
            基于 https://gitee.com/lionsoul/ip2region 实现
        2. 城市功能：查询城市编码对应的城市信息
            基于 https://github.com/modood/Administrative-divisions-of-China 实现
    </description>
    <url>https://github.com/YunaiV/ruoyi-vue-pro</url>

    <dependencies>
        <dependency>
            <groupId>cn.iocoder.cloud</groupId>
            <artifactId>zj-common</artifactId>
        </dependency>

        <!-- IP地址检索 -->
        <dependency>
            <groupId>org.lionsoul</groupId>
            <artifactId>ip2region</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <scope>provided</scope> <!-- 设置为 provided，只有工具类需要使用到 -->
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>cn.iocoder.cloud</groupId>
            <artifactId>zj-spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>
