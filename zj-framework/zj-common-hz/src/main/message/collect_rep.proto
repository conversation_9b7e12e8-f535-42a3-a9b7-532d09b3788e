/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";
package org.apache.hertzbeat.common.entity.message;

message MetricsData
{
  // monitoring id
  uint64 id = 1;
  // tenant id
  uint64 tenantId = 2;
  // monitoring app eg: linux | mysql | jvm
  string app = 3;
  // monitoring metrics eg: cpu | memory | health
  string metrics = 4;
  // monitoring collect priority >=0
  uint32 priority = 5;
  // collect timestamp
  uint64 time = 6;
  // collect response code
  Code code = 7;
  // collect response error message
  string msg = 8;
  // monitoring collect metric field
  repeated Field fields = 9;
  // monitoring collect metric data, mapping with the fields
  repeated ValueRow values = 10;
}

message Field
{
  // monitoring collect metric field name
  string name = 1;
  // monitoring collect metrics field type, 0-number 1-string
  uint32 type = 2;
  // monitoring collect metrics field unit, % MB GB TB S...
  string unit = 3;
  // is label field
  bool label = 4;
}

message ValueRow
{
  // monitoring collect metrics value, mapping with the fields
  repeated string columns = 1;
}

enum Code
{
  // collect success
  SUCCESS = 0;
  // collector not available
  UN_AVAILABLE = 1;
  // peer network un reachable(icmp)
  UN_REACHABLE = 2;
  // peer network server un connectable(tcp,udp...)
  UN_CONNECTABLE = 3;
  // collect metrics data failed(http,ssh,snmp...)
  FAIL = 4;
  // collect metrics data timeout
  TIMEOUT = 5;
}
