package org.apache.hertzbeat.common.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.hertzbeat.common.constants.CommonConstants;

import javax.validation.constraints.NotNull;


/**
 * collector info
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "collector info")
public class CollectorInfo {


    @NotNull
    private String name;

    private String ip;

    private String version;

    @NotNull
    private String mode = CommonConstants.MODE_PUBLIC;

}
