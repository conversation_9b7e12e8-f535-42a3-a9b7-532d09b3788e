/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.hertzbeat.common.entity.alerter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.hertzbeat.common.util.JsonUtil;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.time.LocalDateTime;
import java.util.Map;

import static io.swagger.v3.oas.annotations.media.Schema.AccessMode.READ_ONLY;
import static io.swagger.v3.oas.annotations.media.Schema.AccessMode.READ_WRITE;

/**
 * Alarm record entity
 * <AUTHOR>
 */
@Entity
@Table(name = "hzb_alert")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "Alarm record entity | 告警记录实体")
@EntityListeners(AuditingEntityListener.class)
public class Alert {

    @Id

    @Schema(title = "Alarm record entity primary key index ID",
            description = "告警记录实体主键索引ID",
            example = "87584674384", accessMode = READ_ONLY)
    private Long id;

    @Schema(title = "Alert target object: monitor availability-available metrics-app.metrics.field",
            description = "告警目标对象: 监控可用性-available 指标-app.metrics.field",
            example = "1", accessMode = READ_WRITE)
    @Length(max = 255)
    private String target;

    @Schema(title = "Alarm definition ID associated with the alarm",
            description = "告警关联的告警定义ID",
            example = "8743267443543", accessMode = READ_WRITE)
    private Long alertDefineId;

    @Schema(title = "Alarm level 0:High-Emergency-Critical Alarm 1:Medium-Critical-Critical Alarm 2:Low-Warning-Warning", 
            example = "1", accessMode = READ_WRITE)
    @Min(0)
    @Max(2)
    private byte priority;

    @Schema(title = "The actual content of the alarm notification",
            description = "告警通知实际内容",
            example = "linux_192.134.32.1: 534543534 cpu usage high",
            accessMode = READ_WRITE)
    @Column(length = 4096)
    private String content;

    @Schema(title = "Alarm status: "
            + "0-normal alarm (to be processed) "
            + "1-threshold triggered but not reached the number of alarms "
            + "2-recovered alarm "
            + "3-processed",
            description = "Alarm status: "
                    + "0-normal alarm (to be processed) "
                    + "1-threshold triggered but not reached the number of alarms "
                    + "2-recovered alarm "
                    + "3-processed",
            example = "1", accessMode = READ_WRITE)
    @Min(0)
    @Max(3)
    private byte status;
    
    @Schema(title = "Alarm times",
            description = "告警次数",
            example = "3", accessMode = READ_WRITE)
    private Integer times;
    
    @Schema(title = "Alarm trigger time (timestamp in milliseconds)",
            description = "首次告警时间(毫秒时间戳)",
            example = "1612198922000", accessMode = READ_ONLY)
    private Long firstAlarmTime;
    
    @Schema(title = "Alarm trigger time (timestamp in milliseconds)",
            description = "最近告警时间(毫秒时间戳)",
            example = "1612198922000", accessMode = READ_ONLY)
    private Long lastAlarmTime;

    @Schema(title = "Alarm threshold trigger times",
            description = "告警阈值触发次数",
            example = "3", accessMode = READ_WRITE)
    @Transient
    private Integer triggerTimes;

    @Schema(description = "告警信息标签(monitorId:xxx,monitorName:xxx)", example = "{key1:value1}", accessMode = READ_WRITE)
    @Convert(converter = JsonMapAttributeConverter.class)
    @SuppressWarnings("JpaAttributeTypeInspection")
    @Column(length = 2048)
    private Map<String, String> tags;

    @Schema(title = "platform's id",
            description = "关联告警应用的平台信息",
            example = "1", accessMode = READ_WRITE)
    private Long platformId;

    @Schema(title = "platform's name",
            description = "关联告警应用的平台信息",
            example = "深信服", accessMode = READ_WRITE)
    @Length(max = 255)
    private String platformName;
    @Schema(title = "Alarm status: 0-normal alarm (to be processed) 1-threshold triggered but not reached the number of alarms 2-recovered alarm 3-processed",
            description = "工单状态: 0：未处理，1：处理中，2：已处理",
            example = "1", accessMode = READ_WRITE)
    @Min(0)
    @Max(3)
    private byte isSolved;
    @Schema(title = "The creator of this record", example = "tom", accessMode = READ_ONLY)
    @CreatedBy
    private String creator;

    @Schema(title = "The modifier of this record", example = "tom", accessMode = READ_ONLY)
    @LastModifiedBy
    private String modifier;

    @Schema(title = "Record the latest creation time (timestamp in milliseconds)",
            description = "记录最新创建时间(毫秒时间戳)",
            example = "1612198922000", accessMode = READ_ONLY)
    @CreatedDate
    private LocalDateTime gmtCreate;

    @Schema(title = "Record modify time", example = "1612198444000", accessMode = READ_ONLY)
    @LastModifiedDate
    private LocalDateTime gmtUpdate;

    @Schema(title = "Alarm resourceType",
            description = "资源状态(0 云平台， 1 hz)",
            example = "1", accessMode = READ_WRITE)
    private Integer resourceType;

    @Schema(title = "Alarm monitorId",
            description = "资源ID",
            example = "3123", accessMode = READ_WRITE)
    private String monitorId;

    @Schema(title = "Alarm monitorName",
            description = "资源名称",
            example = "3123", accessMode = READ_WRITE)
    private String monitorName;

    @Schema(title = "Alarm app",
            description = "资源ID",
            example = "mysql", accessMode = READ_WRITE)
    private String app;

    @Schema(title = "Alarm alarmId",
            description = "告警模版id",
            example = "1", accessMode = READ_WRITE)
    private String alarmId;

    @Schema(title = "Alarm alarmId",
            description = "告警模版名称",
            example = "mysql", accessMode = READ_WRITE)
    private String alarmName;

    @Schema(title = "Alarm isFallBack",
            description = "工单是否退回 0 未退回， 1 已退回)",
            example = "0", accessMode = READ_WRITE)
    private Integer isFallBack;

    @Schema(title = "Alarm category",
            description = "监控类型:大类)",
            example = "0", accessMode = READ_WRITE)
    private String category;

    @Schema(title = "Alarm rule",
            description = "告警规则)",
            example = "qiwudg>0", accessMode = READ_WRITE)
    private String alarmRule;

    @Schema(title = "Alarm config name",
            description = "告警配置名称)",
            example = "aiugfiq>0", accessMode = READ_WRITE)
    private String alarmConfigName;

    @Override
    public Alert clone() {
        // deep clone
        return JsonUtil.fromJson(JsonUtil.toJson(this), Alert.class);
    }
}
