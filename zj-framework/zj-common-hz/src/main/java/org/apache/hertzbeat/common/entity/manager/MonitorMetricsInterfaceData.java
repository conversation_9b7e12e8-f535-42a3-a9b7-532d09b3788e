package org.apache.hertzbeat.common.entity.manager;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;

import static io.swagger.v3.oas.annotations.media.Schema.AccessMode.READ_ONLY;
import static io.swagger.v3.oas.annotations.media.Schema.AccessMode.READ_WRITE;

@Entity
@Table(name = "hzb_monitor_metrics_interface_data")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "Monitor Entity | 监控实时数据")
@EntityListeners(AuditingEntityListener.class)
public class MonitorMetricsInterfaceData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(title = "Monitor task ID", example = "87584674384", accessMode = READ_ONLY)
    private Long id;

    @Schema(title = "Monitor ID", example = "87584674384", accessMode = READ_WRITE)
    @Column(name = "monitor_id")
    private Long monitorId;

    @Schema(title = "Monitor metrics", example = "interface", accessMode = READ_WRITE)
    private String metrics;

    @Schema(title = "Monitor metrics value", example = "interface 0", accessMode = READ_WRITE)
    @Column(name = "`interface_index`")
    private String interfaceIndex;

    @Schema(title = "Monitor metrics key", example = "interface_name", accessMode = READ_WRITE)
    @Column(name = "interface_name")
    private String interfaceName;

    @Schema(title = "alias", example = "1", accessMode = READ_WRITE)
    @Column(name = "alias", nullable = false, columnDefinition = "VARCHAR(255) DEFAULT ''")
    private String alias = ""; // 默认值

    @Schema(title = "管理口状态", example = "1", accessMode = READ_WRITE)
    @Column(name = "admin_status")
    private String adminStatus;

    @Schema(title = "操作口状态", example = "1", accessMode = READ_WRITE)
    @Column(name = "oper_status")
    private String operStatus;
}
