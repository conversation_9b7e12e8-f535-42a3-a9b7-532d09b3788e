package org.apache.hertzbeat.common.serialize;


import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.apache.kafka.common.header.Headers;
import org.apache.kafka.common.serialization.Serializer;

import java.util.Map;


/**
 * kafka metrics data serializer
 * <AUTHOR>
 */
@Slf4j
public class KafkaMetricsDataSerializer implements Serializer<CollectRep.MetricsData> {

    @Override
    public void configure(Map<String, ?> configs, boolean isKey) {
        Serializer.super.configure(configs, isKey);
    }

    @Override
    public byte[] serialize(String s, CollectRep.MetricsData metricsData) {

        if (metricsData == null) {
            log.error("metricsData is null");
            return null;
        }

        return metricsData.toByteArray();
    }

    @Override
    public byte[] serialize(String topic, Headers headers, CollectRep.MetricsData data) {
        return Serializer.super.serialize(topic, headers, data);
    }

    @Override
    public void close() {
        Serializer.super.close();
    }
}
