/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: cluster_msg.proto

package org.apache.hertzbeat.common.entity.message;

@SuppressWarnings("all")
public final class ClusterMsg {
    private ClusterMsg() {
    }
    public static void registerAllExtensions(
            com.google.protobuf.ExtensionRegistryLite registry) {
    }

    public static void registerAllExtensions(
            com.google.protobuf.ExtensionRegistry registry) {
        registerAllExtensions(
                (com.google.protobuf.ExtensionRegistryLite) registry);
    }

    /**
     * Protobuf enum {@code org.apache.hertzbeat.common.entity.message.MessageType}
     */
    public enum MessageType
            implements com.google.protobuf.ProtocolMessageEnum {
        /**
         * <pre>
         * heartbeat message
         * </pre>
         *
         * <code>HEARTBEAT = 0;</code>
         */
        HEARTBEAT(0),
        /**
         * <pre>
         * collector go online to master message
         * </pre>
         *
         * <code>GO_ONLINE = 1;</code>
         */
        GO_ONLINE(1),
        /**
         * <pre>
         * collector go offline to master message
         * </pre>
         *
         * <code>GO_OFFLINE = 2;</code>
         */
        GO_OFFLINE(2),
        /**
         * <pre>
         * collector go close to master
         * </pre>
         *
         * <code>GO_CLOSE = 3;</code>
         */
        GO_CLOSE(3),
        /**
         * <pre>
         * issue cyclic collect task
         * </pre>
         *
         * <code>ISSUE_CYCLIC_TASK = 4;</code>
         */
        ISSUE_CYCLIC_TASK(4),
        /**
         * <pre>
         * delete cyclic collect task
         * </pre>
         *
         * <code>DELETE_CYCLIC_TASK = 5;</code>
         */
        DELETE_CYCLIC_TASK(5),
        /**
         * <pre>
         * issue one-time collect task
         * </pre>
         *
         * <code>ISSUE_ONE_TIME_TASK = 6;</code>
         */
        ISSUE_ONE_TIME_TASK(6),
        /**
         * <pre>
         * response one-time collect data
         * </pre>
         *
         * <code>RESPONSE_ONE_TIME_TASK_DATA = 7;</code>
         */
        RESPONSE_ONE_TIME_TASK_DATA(7),
        /**
         * <pre>
         * response cyclic collect data
         * </pre>
         *
         * <code>RESPONSE_CYCLIC_TASK_DATA = 8;</code>
         */
        RESPONSE_CYCLIC_TASK_DATA(8),
        /**
         * <pre>
         * response cyclic service discovery data
         * </pre>
         *
         * <code>RESPONSE_CYCLIC_TASK_SD_DATA = 9;</code>
         */
        RESPONSE_CYCLIC_TASK_SD_DATA(9),
        /**
         * <pre>
         * 添加新的消息类型
         * </pre>
         *
         * <code>RESPONSE_SYSTEM_METRICS = 10;</code>
         */
        RESPONSE_SYSTEM_METRICS(10),
        UNRECOGNIZED(-1),
        ;

        /**
         * <pre>
         * heartbeat message
         * </pre>
         *
         * <code>HEARTBEAT = 0;</code>
         */
        public static final int HEARTBEAT_VALUE = 0;
        /**
         * <pre>
         * collector go online to master message
         * </pre>
         *
         * <code>GO_ONLINE = 1;</code>
         */
        public static final int GO_ONLINE_VALUE = 1;
        /**
         * <pre>
         * collector go offline to master message
         * </pre>
         *
         * <code>GO_OFFLINE = 2;</code>
         */
        public static final int GO_OFFLINE_VALUE = 2;
        /**
         * <pre>
         * collector go close to master
         * </pre>
         *
         * <code>GO_CLOSE = 3;</code>
         */
        public static final int GO_CLOSE_VALUE = 3;
        /**
         * <pre>
         * issue cyclic collect task
         * </pre>
         *
         * <code>ISSUE_CYCLIC_TASK = 4;</code>
         */
        public static final int ISSUE_CYCLIC_TASK_VALUE = 4;
        /**
         * <pre>
         * delete cyclic collect task
         * </pre>
         *
         * <code>DELETE_CYCLIC_TASK = 5;</code>
         */
        public static final int DELETE_CYCLIC_TASK_VALUE = 5;
        /**
         * <pre>
         * issue one-time collect task
         * </pre>
         *
         * <code>ISSUE_ONE_TIME_TASK = 6;</code>
         */
        public static final int ISSUE_ONE_TIME_TASK_VALUE = 6;
        /**
         * <pre>
         * response one-time collect data
         * </pre>
         *
         * <code>RESPONSE_ONE_TIME_TASK_DATA = 7;</code>
         */
        public static final int RESPONSE_ONE_TIME_TASK_DATA_VALUE = 7;
        /**
         * <pre>
         * response cyclic collect data
         * </pre>
         *
         * <code>RESPONSE_CYCLIC_TASK_DATA = 8;</code>
         */
        public static final int RESPONSE_CYCLIC_TASK_DATA_VALUE = 8;
        /**
         * <pre>
         * response cyclic service discovery data
         * </pre>
         *
         * <code>RESPONSE_CYCLIC_TASK_SD_DATA = 9;</code>
         */
        public static final int RESPONSE_CYCLIC_TASK_SD_DATA_VALUE = 9;
        /**
         * <pre>
         * 添加新的消息类型
         * </pre>
         *
         * <code>RESPONSE_SYSTEM_METRICS = 10;</code>
         */
        public static final int RESPONSE_SYSTEM_METRICS_VALUE = 10;


        public final int getNumber() {
            if (this == UNRECOGNIZED) {
                throw new IllegalArgumentException(
                        "Can't get the number of an unknown enum value.");
            }
            return value;
        }

        /**
         * @param value The numeric wire value of the corresponding enum entry.
         * @return The enum associated with the given numeric wire value.
         * @deprecated Use {@link #forNumber(int)} instead.
         */
        @Deprecated
        public static MessageType valueOf(int value) {
            return forNumber(value);
        }

        /**
         * @param value The numeric wire value of the corresponding enum entry.
         * @return The enum associated with the given numeric wire value.
         */
        public static MessageType forNumber(int value) {
            switch (value) {
                case 0:
                    return HEARTBEAT;
                case 1:
                    return GO_ONLINE;
                case 2:
                    return GO_OFFLINE;
                case 3:
                    return GO_CLOSE;
                case 4:
                    return ISSUE_CYCLIC_TASK;
                case 5:
                    return DELETE_CYCLIC_TASK;
                case 6:
                    return ISSUE_ONE_TIME_TASK;
                case 7:
                    return RESPONSE_ONE_TIME_TASK_DATA;
                case 8:
                    return RESPONSE_CYCLIC_TASK_DATA;
                case 9:
                    return RESPONSE_CYCLIC_TASK_SD_DATA;
                case 10:
                    return RESPONSE_SYSTEM_METRICS;
                default:
                    return null;
            }
        }

        public static com.google.protobuf.Internal.EnumLiteMap<MessageType>
        internalGetValueMap() {
            return internalValueMap;
        }

        private static final com.google.protobuf.Internal.EnumLiteMap<
                MessageType> internalValueMap =
                new com.google.protobuf.Internal.EnumLiteMap<MessageType>() {
                    public MessageType findValueByNumber(int number) {
                        return MessageType.forNumber(number);
                    }
                };

        public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
            if (this == UNRECOGNIZED) {
                throw new IllegalStateException(
                        "Can't get the descriptor of an unrecognized enum value.");
            }
            return getDescriptor().getValues().get(ordinal());
        }

        public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
            return getDescriptor();
        }

        public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
            return ClusterMsg.getDescriptor().getEnumTypes().get(0);
        }

        private static final MessageType[] VALUES = values();

        public static MessageType valueOf(
                com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
            if (desc.getType() != getDescriptor()) {
                throw new IllegalArgumentException(
                        "EnumValueDescriptor is not for this type.");
            }
            if (desc.getIndex() == -1) {
                return UNRECOGNIZED;
            }
            return VALUES[desc.getIndex()];
        }

        private final int value;

        private MessageType(int value) {
            this.value = value;
        }

        // @@protoc_insertion_point(enum_scope:org.apache.hertzbeat.common.entity.message.MessageType)
    }

    /**
     * Protobuf enum {@code org.apache.hertzbeat.common.entity.message.Direction}
     */
    public enum Direction
            implements com.google.protobuf.ProtocolMessageEnum {
        /**
         * <pre>
         * request message
         * </pre>
         *
         * <code>REQUEST = 0;</code>
         */
        REQUEST(0),
        /**
         * <pre>
         * request response
         * </pre>
         *
         * <code>RESPONSE = 1;</code>
         */
        RESPONSE(1),
        UNRECOGNIZED(-1),
        ;

        /**
         * <pre>
         * request message
         * </pre>
         *
         * <code>REQUEST = 0;</code>
         */
        public static final int REQUEST_VALUE = 0;
        /**
         * <pre>
         * request response
         * </pre>
         *
         * <code>RESPONSE = 1;</code>
         */
        public static final int RESPONSE_VALUE = 1;


        public final int getNumber() {
            if (this == UNRECOGNIZED) {
                throw new IllegalArgumentException(
                        "Can't get the number of an unknown enum value.");
            }
            return value;
        }

        /**
         * @param value The numeric wire value of the corresponding enum entry.
         * @return The enum associated with the given numeric wire value.
         * @deprecated Use {@link #forNumber(int)} instead.
         */
        @Deprecated
        public static Direction valueOf(int value) {
            return forNumber(value);
        }

        /**
         * @param value The numeric wire value of the corresponding enum entry.
         * @return The enum associated with the given numeric wire value.
         */
        public static Direction forNumber(int value) {
            switch (value) {
                case 0:
                    return REQUEST;
                case 1:
                    return RESPONSE;
                default:
                    return null;
            }
        }

        public static com.google.protobuf.Internal.EnumLiteMap<Direction>
        internalGetValueMap() {
            return internalValueMap;
        }
        private static final com.google.protobuf.Internal.EnumLiteMap<
                Direction> internalValueMap =
                new com.google.protobuf.Internal.EnumLiteMap<Direction>() {
                    public Direction findValueByNumber(int number) {
                        return Direction.forNumber(number);
                    }
                };

        public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
            if (this == UNRECOGNIZED) {
                throw new IllegalStateException(
                        "Can't get the descriptor of an unrecognized enum value.");
            }
            return getDescriptor().getValues().get(ordinal());
        }
        public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
            return getDescriptor();
        }
        public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
            return ClusterMsg.getDescriptor().getEnumTypes().get(1);
        }

        private static final Direction[] VALUES = values();

        public static Direction valueOf(
                com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
            if (desc.getType() != getDescriptor()) {
                throw new IllegalArgumentException(
                        "EnumValueDescriptor is not for this type.");
            }
            if (desc.getIndex() == -1) {
                return UNRECOGNIZED;
            }
            return VALUES[desc.getIndex()];
        }

        private final int value;

        private Direction(int value) {
            this.value = value;
        }

        // @@protoc_insertion_point(enum_scope:org.apache.hertzbeat.common.entity.message.Direction)
    }

    public interface MessageOrBuilder extends
            // @@protoc_insertion_point(interface_extends:org.apache.hertzbeat.common.entity.message.Message)
            com.google.protobuf.MessageOrBuilder {

        /**
         * <pre>
         * collector identity
         * </pre>
         *
         * <code>string identity = 1;</code>
         * @return The identity.
         */
        String getIdentity();
        /**
         * <pre>
         * collector identity
         * </pre>
         *
         * <code>string identity = 1;</code>
         * @return The bytes for identity.
         */
        com.google.protobuf.ByteString
        getIdentityBytes();

        /**
         * <pre>
         * message direction
         * </pre>
         *
         * <code>.org.apache.hertzbeat.common.entity.message.Direction direction = 2;</code>
         * @return The enum numeric value on the wire for direction.
         */
        int getDirectionValue();
        /**
         * <pre>
         * message direction
         * </pre>
         *
         * <code>.org.apache.hertzbeat.common.entity.message.Direction direction = 2;</code>
         * @return The direction.
         */
        Direction getDirection();

        /**
         * <pre>
         * message type
         * </pre>
         *
         * <code>.org.apache.hertzbeat.common.entity.message.MessageType type = 3;</code>
         * @return The enum numeric value on the wire for type.
         */
        int getTypeValue();
        /**
         * <pre>
         * message type
         * </pre>
         *
         * <code>.org.apache.hertzbeat.common.entity.message.MessageType type = 3;</code>
         * @return The type.
         */
        MessageType getType();

        /**
         * <pre>
         * message content
         * </pre>
         *
         * <code>string msg = 4;</code>
         * @return The msg.
         */
        String getMsg();
        /**
         * <pre>
         * message content
         * </pre>
         *
         * <code>string msg = 4;</code>
         * @return The bytes for msg.
         */
        com.google.protobuf.ByteString
        getMsgBytes();
    }
    /**
     * Protobuf type {@code org.apache.hertzbeat.common.entity.message.Message}
     */
    public static final class Message extends
            com.google.protobuf.GeneratedMessageV3 implements
            // @@protoc_insertion_point(message_implements:org.apache.hertzbeat.common.entity.message.Message)
            MessageOrBuilder {
        private static final long serialVersionUID = 0L;
        // Use Message.newBuilder() to construct.
        private Message(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
            super(builder);
        }
        private Message() {
            identity_ = "";
            direction_ = 0;
            type_ = 0;
            msg_ = "";
        }

        @Override
        @SuppressWarnings({"unused"})
        protected Object newInstance(
                UnusedPrivateParameter unused) {
            return new Message();
        }

        @Override
        public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
            return this.unknownFields;
        }
        private Message(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            this();
            if (extensionRegistry == null) {
                throw new NullPointerException();
            }
            com.google.protobuf.UnknownFieldSet.Builder unknownFields =
                    com.google.protobuf.UnknownFieldSet.newBuilder();
            try {
                boolean done = false;
                while (!done) {
                    int tag = input.readTag();
                    switch (tag) {
                        case 0:
                            done = true;
                            break;
                        case 10: {
                            String s = input.readStringRequireUtf8();

                            identity_ = s;
                            break;
                        }
                        case 16: {
                            int rawValue = input.readEnum();

                            direction_ = rawValue;
                            break;
                        }
                        case 24: {
                            int rawValue = input.readEnum();

                            type_ = rawValue;
                            break;
                        }
                        case 34: {
                            String s = input.readStringRequireUtf8();

                            msg_ = s;
                            break;
                        }
                        default: {
                            if (!parseUnknownField(
                                    input, unknownFields, extensionRegistry, tag)) {
                                done = true;
                            }
                            break;
                        }
                    }
                }
            } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                throw e.setUnfinishedMessage(this);
            } catch (com.google.protobuf.UninitializedMessageException e) {
                throw e.asInvalidProtocolBufferException().setUnfinishedMessage(this);
            } catch (java.io.IOException e) {
                throw new com.google.protobuf.InvalidProtocolBufferException(
                        e).setUnfinishedMessage(this);
            } finally {
                this.unknownFields = unknownFields.build();
                makeExtensionsImmutable();
            }
        }
        public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
            return ClusterMsg.internal_static_org_apache_hertzbeat_common_entity_message_Message_descriptor;
        }

        @Override
        protected FieldAccessorTable
        internalGetFieldAccessorTable() {
            return ClusterMsg.internal_static_org_apache_hertzbeat_common_entity_message_Message_fieldAccessorTable
                    .ensureFieldAccessorsInitialized(
                            Message.class, Builder.class);
        }

        public static final int IDENTITY_FIELD_NUMBER = 1;
        private volatile Object identity_;
        /**
         * <pre>
         * collector identity
         * </pre>
         *
         * <code>string identity = 1;</code>
         * @return The identity.
         */
        @Override
        public String getIdentity() {
            Object ref = identity_;
            if (ref instanceof String) {
                return (String) ref;
            } else {
                com.google.protobuf.ByteString bs =
                        (com.google.protobuf.ByteString) ref;
                String s = bs.toStringUtf8();
                identity_ = s;
                return s;
            }
        }
        /**
         * <pre>
         * collector identity
         * </pre>
         *
         * <code>string identity = 1;</code>
         * @return The bytes for identity.
         */
        @Override
        public com.google.protobuf.ByteString
        getIdentityBytes() {
            Object ref = identity_;
            if (ref instanceof String) {
                com.google.protobuf.ByteString b =
                        com.google.protobuf.ByteString.copyFromUtf8(
                                (String) ref);
                identity_ = b;
                return b;
            } else {
                return (com.google.protobuf.ByteString) ref;
            }
        }

        public static final int DIRECTION_FIELD_NUMBER = 2;
        private int direction_;
        /**
         * <pre>
         * message direction
         * </pre>
         *
         * <code>.org.apache.hertzbeat.common.entity.message.Direction direction = 2;</code>
         * @return The enum numeric value on the wire for direction.
         */
        @Override
        public int getDirectionValue() {
            return direction_;
        }
        /**
         * <pre>
         * message direction
         * </pre>
         *
         * <code>.org.apache.hertzbeat.common.entity.message.Direction direction = 2;</code>
         * @return The direction.
         */
        @Override
        public Direction getDirection() {
            @SuppressWarnings("deprecation")
            Direction result = Direction.valueOf(direction_);
            return result == null ? Direction.UNRECOGNIZED : result;
        }

        public static final int TYPE_FIELD_NUMBER = 3;
        private int type_;
        /**
         * <pre>
         * message type
         * </pre>
         *
         * <code>.org.apache.hertzbeat.common.entity.message.MessageType type = 3;</code>
         * @return The enum numeric value on the wire for type.
         */
        @Override
        public int getTypeValue() {
            return type_;
        }
        /**
         * <pre>
         * message type
         * </pre>
         *
         * <code>.org.apache.hertzbeat.common.entity.message.MessageType type = 3;</code>
         * @return The type.
         */
        @Override
        public MessageType getType() {
            @SuppressWarnings("deprecation")
            MessageType result = MessageType.valueOf(type_);
            return result == null ? MessageType.UNRECOGNIZED : result;
        }

        public static final int MSG_FIELD_NUMBER = 4;
        private volatile Object msg_;
        /**
         * <pre>
         * message content
         * </pre>
         *
         * <code>string msg = 4;</code>
         * @return The msg.
         */
        @Override
        public String getMsg() {
            Object ref = msg_;
            if (ref instanceof String) {
                return (String) ref;
            } else {
                com.google.protobuf.ByteString bs =
                        (com.google.protobuf.ByteString) ref;
                String s = bs.toStringUtf8();
                msg_ = s;
                return s;
            }
        }
        /**
         * <pre>
         * message content
         * </pre>
         *
         * <code>string msg = 4;</code>
         * @return The bytes for msg.
         */
        @Override
        public com.google.protobuf.ByteString
        getMsgBytes() {
            Object ref = msg_;
            if (ref instanceof String) {
                com.google.protobuf.ByteString b =
                        com.google.protobuf.ByteString.copyFromUtf8(
                                (String) ref);
                msg_ = b;
                return b;
            } else {
                return (com.google.protobuf.ByteString) ref;
            }
        }

        private byte memoizedIsInitialized = -1;
        @Override
        public final boolean isInitialized() {
            byte isInitialized = memoizedIsInitialized;
            if (isInitialized == 1) return true;
            if (isInitialized == 0) return false;

            memoizedIsInitialized = 1;
            return true;
        }

        @Override
        public void writeTo(com.google.protobuf.CodedOutputStream output)
                throws java.io.IOException {
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(identity_)) {
                com.google.protobuf.GeneratedMessageV3.writeString(output, 1, identity_);
            }
            if (direction_ != Direction.REQUEST.getNumber()) {
                output.writeEnum(2, direction_);
            }
            if (type_ != MessageType.HEARTBEAT.getNumber()) {
                output.writeEnum(3, type_);
            }
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(msg_)) {
                com.google.protobuf.GeneratedMessageV3.writeString(output, 4, msg_);
            }
            unknownFields.writeTo(output);
        }

        @Override
        public int getSerializedSize() {
            int size = memoizedSize;
            if (size != -1) return size;

            size = 0;
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(identity_)) {
                size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, identity_);
            }
            if (direction_ != Direction.REQUEST.getNumber()) {
                size += com.google.protobuf.CodedOutputStream
                        .computeEnumSize(2, direction_);
            }
            if (type_ != MessageType.HEARTBEAT.getNumber()) {
                size += com.google.protobuf.CodedOutputStream
                        .computeEnumSize(3, type_);
            }
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(msg_)) {
                size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, msg_);
            }
            size += unknownFields.getSerializedSize();
            memoizedSize = size;
            return size;
        }

        @Override
        public boolean equals(final Object obj) {
            if (obj == this) {
                return true;
            }
            if (!(obj instanceof Message)) {
                return super.equals(obj);
            }
            Message other = (Message) obj;

            if (!getIdentity()
                    .equals(other.getIdentity())) return false;
            if (direction_ != other.direction_) return false;
            if (type_ != other.type_) return false;
            if (!getMsg()
                    .equals(other.getMsg())) return false;
            if (!unknownFields.equals(other.unknownFields)) return false;
            return true;
        }

        @Override
        public int hashCode() {
            if (memoizedHashCode != 0) {
                return memoizedHashCode;
            }
            int hash = 41;
            hash = (19 * hash) + getDescriptor().hashCode();
            hash = (37 * hash) + IDENTITY_FIELD_NUMBER;
            hash = (53 * hash) + getIdentity().hashCode();
            hash = (37 * hash) + DIRECTION_FIELD_NUMBER;
            hash = (53 * hash) + direction_;
            hash = (37 * hash) + TYPE_FIELD_NUMBER;
            hash = (53 * hash) + type_;
            hash = (37 * hash) + MSG_FIELD_NUMBER;
            hash = (53 * hash) + getMsg().hashCode();
            hash = (29 * hash) + unknownFields.hashCode();
            memoizedHashCode = hash;
            return hash;
        }

        public static Message parseFrom(
                java.nio.ByteBuffer data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }
        public static Message parseFrom(
                java.nio.ByteBuffer data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }
        public static Message parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }
        public static Message parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }
        public static Message parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }
        public static Message parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }
        public static Message parseFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }
        public static Message parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }
        public static Message parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input);
        }
        public static Message parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }
        public static Message parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }
        public static Message parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        @Override
        public Builder newBuilderForType() {
            return newBuilder();
        }
        public static Builder newBuilder() {
            return DEFAULT_INSTANCE.toBuilder();
        }
        public static Builder newBuilder(Message prototype) {
            return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }
        @Override
        public Builder toBuilder() {
            return this == DEFAULT_INSTANCE
                    ? new Builder() : new Builder().mergeFrom(this);
        }

        @Override
        protected Builder newBuilderForType(
                BuilderParent parent) {
            Builder builder = new Builder(parent);
            return builder;
        }
        /**
         * Protobuf type {@code org.apache.hertzbeat.common.entity.message.Message}
         */
        public static final class Builder extends
                com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
                // @@protoc_insertion_point(builder_implements:org.apache.hertzbeat.common.entity.message.Message)
                MessageOrBuilder {
            public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
                return ClusterMsg.internal_static_org_apache_hertzbeat_common_entity_message_Message_descriptor;
            }

            @Override
            protected FieldAccessorTable
            internalGetFieldAccessorTable() {
                return ClusterMsg.internal_static_org_apache_hertzbeat_common_entity_message_Message_fieldAccessorTable
                        .ensureFieldAccessorsInitialized(
                                Message.class, Builder.class);
            }

            // Construct using org.apache.hertzbeat.common.entity.message.ClusterMsg.Message.newBuilder()
            private Builder() {
                maybeForceBuilderInitialization();
            }

            private Builder(
                    BuilderParent parent) {
                super(parent);
                maybeForceBuilderInitialization();
            }
            private void maybeForceBuilderInitialization() {
                if (com.google.protobuf.GeneratedMessageV3
                        .alwaysUseFieldBuilders) {
                }
            }
            @Override
            public Builder clear() {
                super.clear();
                identity_ = "";

                direction_ = 0;

                type_ = 0;

                msg_ = "";

                return this;
            }

            @Override
            public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
                return ClusterMsg.internal_static_org_apache_hertzbeat_common_entity_message_Message_descriptor;
            }

            @Override
            public Message getDefaultInstanceForType() {
                return Message.getDefaultInstance();
            }

            @Override
            public Message build() {
                Message result = buildPartial();
                if (!result.isInitialized()) {
                    throw newUninitializedMessageException(result);
                }
                return result;
            }

            @Override
            public Message buildPartial() {
                Message result = new Message(this);
                result.identity_ = identity_;
                result.direction_ = direction_;
                result.type_ = type_;
                result.msg_ = msg_;
                onBuilt();
                return result;
            }

            @Override
            public Builder clone() {
                return super.clone();
            }
            @Override
            public Builder setField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    Object value) {
                return super.setField(field, value);
            }
            @Override
            public Builder clearField(
                    com.google.protobuf.Descriptors.FieldDescriptor field) {
                return super.clearField(field);
            }
            @Override
            public Builder clearOneof(
                    com.google.protobuf.Descriptors.OneofDescriptor oneof) {
                return super.clearOneof(oneof);
            }
            @Override
            public Builder setRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    int index, Object value) {
                return super.setRepeatedField(field, index, value);
            }
            @Override
            public Builder addRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    Object value) {
                return super.addRepeatedField(field, value);
            }
            @Override
            public Builder mergeFrom(com.google.protobuf.Message other) {
                if (other instanceof Message) {
                    return mergeFrom((Message) other);
                } else {
                    super.mergeFrom(other);
                    return this;
                }
            }

            public Builder mergeFrom(Message other) {
                if (other == Message.getDefaultInstance()) return this;
                if (!other.getIdentity().isEmpty()) {
                    identity_ = other.identity_;
                    onChanged();
                }
                if (other.direction_ != 0) {
                    setDirectionValue(other.getDirectionValue());
                }
                if (other.type_ != 0) {
                    setTypeValue(other.getTypeValue());
                }
                if (!other.getMsg().isEmpty()) {
                    msg_ = other.msg_;
                    onChanged();
                }
                this.mergeUnknownFields(other.unknownFields);
                onChanged();
                return this;
            }

            @Override
            public final boolean isInitialized() {
                return true;
            }

            @Override
            public Builder mergeFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws java.io.IOException {
                Message parsedMessage = null;
                try {
                    parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    parsedMessage = (Message) e.getUnfinishedMessage();
                    throw e.unwrapIOException();
                } finally {
                    if (parsedMessage != null) {
                        mergeFrom(parsedMessage);
                    }
                }
                return this;
            }

            private Object identity_ = "";
            /**
             * <pre>
             * collector identity
             * </pre>
             *
             * <code>string identity = 1;</code>
             * @return The identity.
             */
            public String getIdentity() {
                Object ref = identity_;
                if (!(ref instanceof String)) {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    String s = bs.toStringUtf8();
                    identity_ = s;
                    return s;
                } else {
                    return (String) ref;
                }
            }
            /**
             * <pre>
             * collector identity
             * </pre>
             *
             * <code>string identity = 1;</code>
             * @return The bytes for identity.
             */
            public com.google.protobuf.ByteString
            getIdentityBytes() {
                Object ref = identity_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (String) ref);
                    identity_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }
            /**
             * <pre>
             * collector identity
             * </pre>
             *
             * <code>string identity = 1;</code>
             * @param value The identity to set.
             * @return This builder for chaining.
             */
            public Builder setIdentity(
                    String value) {
                if (value == null) {
                    throw new NullPointerException();
                }

                identity_ = value;
                onChanged();
                return this;
            }
            /**
             * <pre>
             * collector identity
             * </pre>
             *
             * <code>string identity = 1;</code>
             * @return This builder for chaining.
             */
            public Builder clearIdentity() {

                identity_ = getDefaultInstance().getIdentity();
                onChanged();
                return this;
            }
            /**
             * <pre>
             * collector identity
             * </pre>
             *
             * <code>string identity = 1;</code>
             * @param value The bytes for identity to set.
             * @return This builder for chaining.
             */
            public Builder setIdentityBytes(
                    com.google.protobuf.ByteString value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                checkByteStringIsUtf8(value);

                identity_ = value;
                onChanged();
                return this;
            }

            private int direction_ = 0;
            /**
             * <pre>
             * message direction
             * </pre>
             *
             * <code>.org.apache.hertzbeat.common.entity.message.Direction direction = 2;</code>
             * @return The enum numeric value on the wire for direction.
             */
            @Override
            public int getDirectionValue() {
                return direction_;
            }
            /**
             * <pre>
             * message direction
             * </pre>
             *
             * <code>.org.apache.hertzbeat.common.entity.message.Direction direction = 2;</code>
             * @param value The enum numeric value on the wire for direction to set.
             * @return This builder for chaining.
             */
            public Builder setDirectionValue(int value) {

                direction_ = value;
                onChanged();
                return this;
            }
            /**
             * <pre>
             * message direction
             * </pre>
             *
             * <code>.org.apache.hertzbeat.common.entity.message.Direction direction = 2;</code>
             * @return The direction.
             */
            @Override
            public Direction getDirection() {
                @SuppressWarnings("deprecation")
                Direction result = Direction.valueOf(direction_);
                return result == null ? Direction.UNRECOGNIZED : result;
            }
            /**
             * <pre>
             * message direction
             * </pre>
             *
             * <code>.org.apache.hertzbeat.common.entity.message.Direction direction = 2;</code>
             * @param value The direction to set.
             * @return This builder for chaining.
             */
            public Builder setDirection(Direction value) {
                if (value == null) {
                    throw new NullPointerException();
                }

                direction_ = value.getNumber();
                onChanged();
                return this;
            }
            /**
             * <pre>
             * message direction
             * </pre>
             *
             * <code>.org.apache.hertzbeat.common.entity.message.Direction direction = 2;</code>
             * @return This builder for chaining.
             */
            public Builder clearDirection() {

                direction_ = 0;
                onChanged();
                return this;
            }

            private int type_ = 0;
            /**
             * <pre>
             * message type
             * </pre>
             *
             * <code>.org.apache.hertzbeat.common.entity.message.MessageType type = 3;</code>
             * @return The enum numeric value on the wire for type.
             */
            @Override
            public int getTypeValue() {
                return type_;
            }
            /**
             * <pre>
             * message type
             * </pre>
             *
             * <code>.org.apache.hertzbeat.common.entity.message.MessageType type = 3;</code>
             * @param value The enum numeric value on the wire for type to set.
             * @return This builder for chaining.
             */
            public Builder setTypeValue(int value) {

                type_ = value;
                onChanged();
                return this;
            }
            /**
             * <pre>
             * message type
             * </pre>
             *
             * <code>.org.apache.hertzbeat.common.entity.message.MessageType type = 3;</code>
             * @return The type.
             */
            @Override
            public MessageType getType() {
                @SuppressWarnings("deprecation")
                MessageType result = MessageType.valueOf(type_);
                return result == null ? MessageType.UNRECOGNIZED : result;
            }
            /**
             * <pre>
             * message type
             * </pre>
             *
             * <code>.org.apache.hertzbeat.common.entity.message.MessageType type = 3;</code>
             * @param value The type to set.
             * @return This builder for chaining.
             */
            public Builder setType(MessageType value) {
                if (value == null) {
                    throw new NullPointerException();
                }

                type_ = value.getNumber();
                onChanged();
                return this;
            }
            /**
             * <pre>
             * message type
             * </pre>
             *
             * <code>.org.apache.hertzbeat.common.entity.message.MessageType type = 3;</code>
             * @return This builder for chaining.
             */
            public Builder clearType() {

                type_ = 0;
                onChanged();
                return this;
            }

            private Object msg_ = "";
            /**
             * <pre>
             * message content
             * </pre>
             *
             * <code>string msg = 4;</code>
             * @return The msg.
             */
            public String getMsg() {
                Object ref = msg_;
                if (!(ref instanceof String)) {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    String s = bs.toStringUtf8();
                    msg_ = s;
                    return s;
                } else {
                    return (String) ref;
                }
            }
            /**
             * <pre>
             * message content
             * </pre>
             *
             * <code>string msg = 4;</code>
             * @return The bytes for msg.
             */
            public com.google.protobuf.ByteString
            getMsgBytes() {
                Object ref = msg_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (String) ref);
                    msg_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }
            /**
             * <pre>
             * message content
             * </pre>
             *
             * <code>string msg = 4;</code>
             * @param value The msg to set.
             * @return This builder for chaining.
             */
            public Builder setMsg(
                    String value) {
                if (value == null) {
                    throw new NullPointerException();
                }

                msg_ = value;
                onChanged();
                return this;
            }
            /**
             * <pre>
             * message content
             * </pre>
             *
             * <code>string msg = 4;</code>
             * @return This builder for chaining.
             */
            public Builder clearMsg() {

                msg_ = getDefaultInstance().getMsg();
                onChanged();
                return this;
            }
            /**
             * <pre>
             * message content
             * </pre>
             *
             * <code>string msg = 4;</code>
             * @param value The bytes for msg to set.
             * @return This builder for chaining.
             */
            public Builder setMsgBytes(
                    com.google.protobuf.ByteString value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                checkByteStringIsUtf8(value);

                msg_ = value;
                onChanged();
                return this;
            }
            @Override
            public final Builder setUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.setUnknownFields(unknownFields);
            }

            @Override
            public final Builder mergeUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.mergeUnknownFields(unknownFields);
            }


            // @@protoc_insertion_point(builder_scope:org.apache.hertzbeat.common.entity.message.Message)
        }

        // @@protoc_insertion_point(class_scope:org.apache.hertzbeat.common.entity.message.Message)
        private static final Message DEFAULT_INSTANCE;
        static {
            DEFAULT_INSTANCE = new Message();
        }

        public static Message getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        private static final com.google.protobuf.Parser<Message>
                PARSER = new com.google.protobuf.AbstractParser<Message>() {
            @Override
            public Message parsePartialFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                return new Message(input, extensionRegistry);
            }
        };

        public static com.google.protobuf.Parser<Message> parser() {
            return PARSER;
        }

        @Override
        public com.google.protobuf.Parser<Message> getParserForType() {
            return PARSER;
        }

        @Override
        public Message getDefaultInstanceForType() {
            return DEFAULT_INSTANCE;
        }

    }

    private static final com.google.protobuf.Descriptors.Descriptor
            internal_static_org_apache_hertzbeat_common_entity_message_Message_descriptor;
    private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internal_static_org_apache_hertzbeat_common_entity_message_Message_fieldAccessorTable;

    public static com.google.protobuf.Descriptors.FileDescriptor
    getDescriptor() {
        return descriptor;
    }

    private static com.google.protobuf.Descriptors.FileDescriptor
            descriptor;

    static {
        String[] descriptorData = {
                "\n\021cluster_msg.proto\022*org.apache.hertzbea" +
                        "t.common.entity.message\"\271\001\n\007Message\022\020\n\010i" +
                        "dentity\030\001 \001(\t\022H\n\tdirection\030\002 \001(\01625.org.a" +
                        "pache.hertzbeat.common.entity.message.Di" +
                        "rection\022E\n\004type\030\003 \001(\01627.org.apache.hertz" +
                        "beat.common.entity.message.MessageType\022\013" +
                        "\n\003msg\030\004 \001(\t*\220\002\n\013MessageType\022\r\n\tHEARTBEAT" +
                        "\020\000\022\r\n\tGO_ONLINE\020\001\022\016\n\nGO_OFFLINE\020\002\022\014\n\010GO_" +
                        "CLOSE\020\003\022\025\n\021ISSUE_CYCLIC_TASK\020\004\022\026\n\022DELETE" +
                        "_CYCLIC_TASK\020\005\022\027\n\023ISSUE_ONE_TIME_TASK\020\006\022" +
                        "\037\n\033RESPONSE_ONE_TIME_TASK_DATA\020\007\022\035\n\031RESP" +
                        "ONSE_CYCLIC_TASK_DATA\020\010\022 \n\034RESPONSE_CYCL" +
                        "IC_TASK_SD_DATA\020\t\022\033\n\027RESPONSE_SYSTEM_MET" +
                        "RICS\020\n*&\n\tDirection\022\013\n\007REQUEST\020\000\022\014\n\010RESP" +
                        "ONSE\020\001b\006proto3"
        };
        descriptor = com.google.protobuf.Descriptors.FileDescriptor
                .internalBuildGeneratedFileFrom(descriptorData,
                        new com.google.protobuf.Descriptors.FileDescriptor[]{
                        });
        internal_static_org_apache_hertzbeat_common_entity_message_Message_descriptor =
                getDescriptor().getMessageTypes().get(0);
        internal_static_org_apache_hertzbeat_common_entity_message_Message_fieldAccessorTable = new
                com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
                internal_static_org_apache_hertzbeat_common_entity_message_Message_descriptor,
                new String[]{"Identity", "Direction", "Type", "Msg",});
    }

    // @@protoc_insertion_point(outer_class_scope)
}
