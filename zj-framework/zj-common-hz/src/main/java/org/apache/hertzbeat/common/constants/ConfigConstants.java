/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.hertzbeat.common.constants;

/**
 * Extract all public strings that the project needs to configure in yml.
 */

public interface ConfigConstants {

    /**
     * System Constant: hertzbeat
     */
    interface SystemConstant {
        String PROJECT_NAME = "hertzbeat";
    }

    /**
     * Package name constant.
     */
    interface PkgConstant {
        String PKG = "org.apache.hertzbeat";
        String ZJ_PKG = "cn.iocoder.cloud.framework";
    }


    /**
     * hertzbeat project module constant.
     */
    interface FunctionModuleConstants {

        String ALERT = "alert";

        String ALERTER = "alerter";

        String COLLECTOR = "collector";

        String COMMON = "common";

        String WAREHOUSE = "warehouse";

        String AI = "ai";

        String STATUS = "status";

        String SCHEDULER = "scheduler";

        String PUSH = "push";

        String DISPATCH = "dispatch";

        String INFO = "info";

        String GRAFANA = "grafana";
    }

}
