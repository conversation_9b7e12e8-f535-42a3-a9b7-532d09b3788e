/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.hertzbeat.common.config;

import lombok.Getter;
import lombok.Setter;

/**
 * Kafka properties
 */

@Getter
@Setter
public class BaseKafkaProperties {

    /**
     * kafka's connection server url
     */
    private String servers = "127.0.0.1:9092";

    /**
     * The name of the topic that receives the data
     */
    private String topic;

    /**
     * Consumer Group ID
     */
    private String groupId;

}
