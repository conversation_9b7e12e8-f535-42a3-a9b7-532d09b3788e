/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.hertzbeat.common.entity.manager;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.hertzbeat.common.support.valid.HostValid;
import org.apache.hertzbeat.common.util.JsonUtil;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

import static io.swagger.v3.oas.annotations.media.Schema.AccessMode.READ_ONLY;
import static io.swagger.v3.oas.annotations.media.Schema.AccessMode.READ_WRITE;

/**
 * Monitor Entity
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "hzb_monitor", indexes = {
        @Index(name = "monitor_query_index", columnList = "app"),
        @Index(name = "monitor_query_index", columnList = "host"),
        @Index(name = "monitor_query_index", columnList = "name")
})
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "Monitor Entity | 监控实体")
@EntityListeners(AuditingEntityListener.class)
public class Monitor {

    /**
     * Monitor ID
     */
    /**
     * Monitor ID
     */
    @Id
    @Schema(title = "Monitor task ID", example = "87584674384", accessMode = READ_ONLY)
    private Long id;

    /**
     * Job ID
     */
    @Schema(title = "Collect task ID", example = "43243543543", accessMode = READ_ONLY)
    private Long jobId;

    /**
     * Monitor Name
     */
    @Schema(title = "task name", example = "Api-TanCloud.cn", accessMode = READ_WRITE)
    @Size(max = 100)
    private String name;

    /**
     * Type of monitoring: linux, mysql, jvm...
     */
    @Schema(title = "Type of monitoring", example = "TanCloud", accessMode = READ_WRITE)
    @Size(max = 100)
    private String app;

    /**
     * Monitored peer host: ipv4, ipv6, domain name
     */
    @Schema(title = "The host to monitor", example = "*************", accessMode = READ_WRITE)
    @Size(max = 100)
    @HostValid
    private String host;

    /**
     * Monitoring collection interval time, in seconds
     */
    @Schema(title = "Monitoring of the acquisition interval time in seconds", example = "600", accessMode = READ_WRITE)
    @Min(10)
    private Integer intervals;

    /**
     * Monitoring status 0: Paused, 1: Up, 2: Down
     */
    @Schema(title = "Task status 0: Paused, 1: Up, 2: Down", accessMode = READ_WRITE)
    @Min(0)
    @Max(4)
    private byte status;

    /**
     * Monitoring note description
     */
    @Schema(title = "Monitor note description", example = "Availability monitoring of the SAAS website TanCloud", accessMode = READ_WRITE)
    @Size(max = 255)
    private String description;

    /**
     * The creator of this record
     */
    @Schema(title = "The creator of this record", example = "tom", accessMode = READ_ONLY)
    @CreatedBy
    private String creator;

    /**
     * This record was last modified by
     */
    @Schema(title = "The modifier of this record", example = "tom", accessMode = READ_ONLY)
    @LastModifiedBy
    private String modifier;

    /**
     * Record create time
     */
    @Schema(title = "Record create time", example = "2024-07-02T20:09:34.903217", accessMode = READ_ONLY)
    @CreatedDate
    private LocalDateTime gmtCreate;

    /**
     * Record the latest modification time (timestamp in milliseconds)
     */
    @Schema(title = "Record modify time", example = "2024-07-02T20:09:34.903217", accessMode = READ_ONLY)
    @LastModifiedDate
    private LocalDateTime gmtUpdate;

    /**
     * For a many-to-many join, you need to set up a third join intermediate table, JoinTable
     * JoinTable name is the intermediate table name of the association relationship
     * joinColumns: The foreign key fields of the intermediate table relate the primary key fields of the table corresponding
     * to the current entity class
     * inverseJoinColumn：The foreign key fields of the intermediate table relate to the primary key fields of the other table
     * JoinColumn  name The associated field name of the intermediate table
     * referencedColumnName The mapping field name of the association table
     */
    @ManyToMany(targetEntity = Tag.class, cascade = CascadeType.MERGE, fetch = FetchType.EAGER)
    @JoinTable(name = "hzb_tag_monitor_bind",
            foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT),
            inverseForeignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT),
            joinColumns = {@JoinColumn(name = "monitor_id", referencedColumnName = "id")},
            inverseJoinColumns = {@JoinColumn(name = "tag_id", referencedColumnName = "id")})
    private List<Tag> tags;


    @Schema(title = "平台id", example = "1", accessMode = READ_WRITE)
    private Long platformId;

    @Schema(title = "平台名称", example = "1", accessMode = READ_WRITE)
    private String platformName;
    @Schema(title = "密码id", example = "1")
    private Long certificateId;

    @Schema(title = "采集器状态", example = "1")
    @Transient
    private Long collectorType;

    @Schema(title = "协议", example = "rdp")
    @Transient
    private String protocol;

    @Schema(title = "设备类型", example = "huawei", accessMode = READ_WRITE)
    private String manufacturer;

    @Schema(title = "监控类型", example = "os", accessMode = READ_WRITE)
    private String category;

    //网络和安全
    @Schema(title = "cpu使用率", example = "19%")
    @Transient
    private String cpuUsed;

    @Schema(title = "内存使用率", example = "19%")
    @Transient
    private String memUsage;

    @Schema(title = "磁盘使用率", example = "19%")
    @Transient
    private String diskUsage;

    @Schema(title = "接口配置状态启动数量", example = "10")
    @Transient
    private Integer interfaceConfigRun;

    @Schema(title = "接口配置状态停止数量", example = "10")
    @Transient
    private Integer interfaceConfigStop;

    @Schema(title = "接口配置状态无数据数量", example = "10")
    @Transient
    private Integer interfaceConfigNoData;

    @Schema(title = "接口当前运行状态启动数量", example = "10")
    @Transient
    private Integer interfaceCurrentRun;

    @Schema(title = "接口当前运行状态停止数量", example = "10")
    @Transient
    private Integer interfaceCurrentStop;

    @Schema(title = "接口当前运行状态无数据数量", example = "10")
    @Transient
    private Integer interfaceCurrentNoData;

    @Schema(title = "运行时长", example = "10")
    @Transient
    private String uptime;

    //存储
    @Schema(title = "电源状态", example = "1")
    @Transient
    private String powerStatus;

    @Schema(title = "系统风扇状态", example = "1")
    @Transient
    private String systemFanStatus;

    @Schema(title = "硬盘正常状态数量", example = "10")
    @Transient
    private Integer diskNormal;

    @Schema(title = "硬盘异常状态数量", example = "10")
    @Transient
    private Integer diskAbnormal;

    @Schema(title = "系统响应时间")
    @Transient
    private String responseTime;


    @Override
    public Monitor clone() {
        return JsonUtil.fromJson(JsonUtil.toJson(this), getClass());
    }
}
