package org.apache.hertzbeat.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ResourceAppEnum {
    //应用服务枚举
    APP_SERVICE_WEBSITE("website", "网站监控"),
    APP_SERVICE_PORT("port", "端口监控"),
    APP_SERVICE_SSL("ssl_cert", "SSL证书监控"),
    APP_SERVICE_PING("ping", "Ping连通性"),
    APP_SERVICE_WEBSOCKET("websocket", "WebSocket监控"),
    APP_SERVICE_API("api", "HTTP API监控"),
    APP_SERVICE_DNS("dns", "DNS服务监控"),
    APP_SERVICE_FULLSITE("fullsite", "SiteMap全站监控"),
    //    APP_SERVICE_APICODE("api_code", "API业务状态码"),
    APP_SERVICE_UDP_PORT("udp_port", "UDP端口可用性"),

    // 数据库枚举
    APP_DB_MYSQL("mysql","MySQL"),
    APP_DB_ORACLE("oracle","Oracle"),
    APP_DB_SQLSERVER("sqlserver","SQLServer"),
    APP_DB_MARIADB("mariadb","MariaDB"),
    APP_DB_DM("dm","达梦"),
    APP_DB_REDIS("redis","Redis"),
    APP_DB_TIDB("tidb","TiDB"),
    APP_DB_NEBULAGRAPH("nebulaGraph","NebulaGraph"),
    APP_DB_POSTGRESQL("postgresql","PostgreSQL"),
    APP_DB_MONGODB("mongodb","MongoDB"),
    APP_DB_OPENGAUSS("opengauss","OpenGauss"),
    //    APP_DB_CLUSTER("redis_cluster","Redis Cluster"),
//    APP_DB_SENTINEL("redis_sentinel","Redis Sentinel"),
    APP_DB_MEMCACHED("memcached","Memcached"),

    // 中间件枚举
    APP_SERVICE_TOMCAT("tomcat", "Tomcat应用服务"),
    APP_DB_ZOOKEEPER("zookeeper", "Zookeeper服务"),
    APP_DB_KAFKA("kafka", "Kafka消息系统"),
    APP_DB_NACOS("nacos", "Nacos分布式"),
    APP_DB_RABBITMQ("rabbitmq", "RabbitMQ消息系统"),
    APP_DB_NGINX("nginx", "Nginx服务"),
    APP_DB_POP3("pop3", "POP3邮件服务"),
    APP_DB_FTP("ftp", "FTP服务"),
    APP_DB_SMTP("smtp", "SMTP邮件服务"),
    APP_DB_NTP("ntp", "NTP服务"),
    APP_DB_SPRING_GATEWAY("spring_gateway", "SpringGateway"),
    APP_DB_EMQX("emqx", "EMQX MQTT"),
    APP_DB_ACTIVEMQ("activemq", "ActiveMQ消息系统"),
    APP_DB_ROCKETMQ("rocketmq", "RocketMQ消息系统"),
//    APP_DB_SHENYU("shenyu", "ShenYu网关"),

    // 操作系统
    APP_SERVICE_IPMI("ipmi", "IPMI带外管理"),
    APP_DB_WINDOWS("windows", "Windows操作系统"),
    APP_DB_LINUX("linux", "Linux-SSH"),
    APP_DB_LINUX_SNMP("linux_snmp", "Linux-SNMP"),
    APP_DB_UBUNTU("ubuntu", "Ubuntu"),
    APP_DB_COREOS("coreos", "Fedora"),
    APP_DB_OPENSUSE("opensuse", "OpenSUSE"),
    APP_DB_ROCKYLINUX("rockylinux", "RockyLinux"),
    APP_DB_EULEROS("euleros", "OpenEulerOS"),
    APP_DB_REDHAT("redhat", "RedHat"),
    APP_DB_CENTOS("centos", "CentOS"),
    APP_DB_FREEBSD("freebsd", "FreeBSD"),
    APP_DB_ALMALINUX("almalinux", "AlmaLinux"),
    APP_DB_DEBIAN("debian", "Debian"),
    // 容器
    APP_CN_DOCKER("docker", "Docker容器"),
    APP_CN_KUBERNETES("kubernetes", "Kubernetes"),

    // 网络
    APP_NETWORK_H3C_SWITCH("h3c_switch", "华三交换机"),
    APP_NETWORK_CISCO_SWITCH("cisco_switch", "思科交换机"),
    APP_NETWORK_HUAWEI_SWITCH("huawei_switch", "华为交换机"),
    APP_NETWORK_TPLINK_SWITCH("tplink_switch", "TP-LINK交换机"),
    APP_NETWORK_HPE_SWITCH("hpe_switch", "锐捷交换机"),

    // 普罗米修斯
    APP_PROMETHEUS_PH("prometheus","Prometheus监控"),
    // 安全
    APP_FIREWALL_SSL_VPN_FIREWALL("ssl_firewall","深信服SSLVPN"),
    APP_FIREWALL_AH_FIREWALL("ah_firewall", "安恒防火墙"),
    APP_FIREWALL_QAX_FIREWALL("qax_firewall", "奇安信防火墙"),
    APP_FIREWALL_SSWK_FIREWALL("sswk_firewall", "山石网科防火墙"),
    APP_FIREWALL_SXF_FIREWALL("sxf_firewall", "深信服防火墙"),
    APP_FIREWALL_SXF_AD_FIREWALL("ad_firewall", "深信服AD设备"),
    APP_FIREWALL_SXF_AC_FIREWALL("ac_firewall", "深信服AC设备"),
    APP_FIREWALL_TRX_FIREWALL("trx_firewall", "天融信防火墙"),
    APP_FIREWALL_AH_WAF("ah_waf", "安恒WAF"),
    APP_FIREWALL_LM_WAF("lm_waf", "绿盟WAF"),
    APP_FIREWALL_LM_DEFENSE("lm_defense", "绿盟入侵防御"),
    APP_FIREWALL_SSWK_DEFENSE("sswk_defense", "山石网科入侵防御"),
    APP_FIREWALL_TRX_DEFENSE("trx_defense", "天融信入侵防御"),
    // 存储
    APP_STORAGE_HUAWEI("storage_huawei", "华为存储"),
    APP_STORAGE_INSPUR("storage_inspur", "浪潮存储"),
    APP_STORAGE_SYNOLOGY("storage_synology", "群晖存储"),
    // 服务器监控
    APP_INFRA_IPMI("infra_ipmi", "IPMI带外管理"),
    APP_INFRA_NVIDIA("infra_nvidia", "NVIDIA显卡监控"),




    ;


    /**
     * 角色编码
     */
    private final String value;
    /**
     * 名字
     */
    private final String label;
}
