// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: collect_rep.proto

package org.apache.hertzbeat.common.entity.message;

import lombok.extern.slf4j.Slf4j;


@SuppressWarnings("all")
@Slf4j
public final class CollectRep {
    private CollectRep() {
    }

    public static void registerAllExtensions(
            com.google.protobuf.ExtensionRegistryLite registry) {
    }

    public static void registerAllExtensions(
            com.google.protobuf.ExtensionRegistry registry) {
        registerAllExtensions(
                (com.google.protobuf.ExtensionRegistryLite) registry);
    }

    /**
     * Protobuf enum {@code org.apache.hertzbeat.common.entity.message.Code}
     */
    public enum Code
            implements com.google.protobuf.ProtocolMessageEnum {
        /**
         * <pre>
         * collect success
         * </pre>
         *
         * <code>SUCCESS = 0;</code>
         */
        SUCCESS(0),
        /**
         * <pre>
         * collector not available
         * </pre>
         *
         * <code>UN_AVAILABLE = 1;</code>
         */
        UN_AVAILABLE(1),
        /**
         * <pre>
         * peer network un reachable(icmp)
         * </pre>
         *
         * <code>UN_REACHABLE = 2;</code>
         */
        UN_REACHABLE(2),
        /**
         * <pre>
         * peer network server un connectable(tcp,udp...)
         * </pre>
         *
         * <code>UN_CONNECTABLE = 3;</code>
         */
        UN_CONNECTABLE(3),
        /**
         * <pre>
         * collect metrics data failed(http,ssh,snmp...)
         * </pre>
         *
         * <code>FAIL = 4;</code>
         */
        FAIL(4),
        /**
         * <pre>
         * collect metrics data timeout
         * </pre>
         *
         * <code>TIMEOUT = 5;</code>
         */
        TIMEOUT(5),
        UNRECOGNIZED(-1),
        ;

        /**
         * <pre>
         * collect success
         * </pre>
         *
         * <code>SUCCESS = 0;</code>
         */
        public static final int SUCCESS_VALUE = 0;
        /**
         * <pre>
         * collector not available
         * </pre>
         *
         * <code>UN_AVAILABLE = 1;</code>
         */
        public static final int UN_AVAILABLE_VALUE = 1;
        /**
         * <pre>
         * peer network un reachable(icmp)
         * </pre>
         *
         * <code>UN_REACHABLE = 2;</code>
         */
        public static final int UN_REACHABLE_VALUE = 2;
        /**
         * <pre>
         * peer network server un connectable(tcp,udp...)
         * </pre>
         *
         * <code>UN_CONNECTABLE = 3;</code>
         */
        public static final int UN_CONNECTABLE_VALUE = 3;
        /**
         * <pre>
         * collect metrics data failed(http,ssh,snmp...)
         * </pre>
         *
         * <code>FAIL = 4;</code>
         */
        public static final int FAIL_VALUE = 4;
        /**
         * <pre>
         * collect metrics data timeout
         * </pre>
         *
         * <code>TIMEOUT = 5;</code>
         */
        public static final int TIMEOUT_VALUE = 5;


        public final int getNumber() {
            if (this == UNRECOGNIZED) {
                throw new java.lang.IllegalArgumentException(
                        "Can't get the number of an unknown enum value.");
            }
            return value;
        }

        /**
         * @param value The numeric wire value of the corresponding enum entry.
         * @return The enum associated with the given numeric wire value.
         * @deprecated Use {@link #forNumber(int)} instead.
         */
        @java.lang.Deprecated
        public static Code valueOf(int value) {
            return forNumber(value);
        }

        /**
         * @param value The numeric wire value of the corresponding enum entry.
         * @return The enum associated with the given numeric wire value.
         */
        public static Code forNumber(int value) {
            switch (value) {
                case 0:
                    return SUCCESS;
                case 1:
                    return UN_AVAILABLE;
                case 2:
                    return UN_REACHABLE;
                case 3:
                    return UN_CONNECTABLE;
                case 4:
                    return FAIL;
                case 5:
                    return TIMEOUT;
                default:
                    return null;
            }
        }

        public static com.google.protobuf.Internal.EnumLiteMap<Code>
        internalGetValueMap() {
            return internalValueMap;
        }

        private static final com.google.protobuf.Internal.EnumLiteMap<
                Code> internalValueMap =
                new com.google.protobuf.Internal.EnumLiteMap<Code>() {
                    public Code findValueByNumber(int number) {
                        return Code.forNumber(number);
                    }
                };

        public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
            if (this == UNRECOGNIZED) {
                throw new java.lang.IllegalStateException(
                        "Can't get the descriptor of an unrecognized enum value.");
            }
            return getDescriptor().getValues().get(ordinal());
        }

        public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
            return getDescriptor();
        }

        public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
            return org.apache.hertzbeat.common.entity.message.CollectRep.getDescriptor().getEnumTypes().get(0);
        }

        private static final Code[] VALUES = values();

        public static Code valueOf(
                com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
            if (desc.getType() != getDescriptor()) {
                throw new java.lang.IllegalArgumentException(
                        "EnumValueDescriptor is not for this type.");
            }
            if (desc.getIndex() == -1) {
                return UNRECOGNIZED;
            }
            return VALUES[desc.getIndex()];
        }

        private final int value;

        private Code(int value) {
            this.value = value;
        }

        // @@protoc_insertion_point(enum_scope:org.apache.hertzbeat.common.entity.message.Code)
    }

    public interface MetricsDataOrBuilder extends
            // @@protoc_insertion_point(interface_extends:org.apache.hertzbeat.common.entity.message.MetricsData)
            com.google.protobuf.MessageOrBuilder {

        /**
         * <pre>
         * monitoring id
         * </pre>
         *
         * <code>uint64 id = 1;</code>
         *
         * @return The id.
         */
        long getId();

        /**
         * <pre>
         * tenant id
         * </pre>
         *
         * <code>uint64 tenantId = 2;</code>
         *
         * @return The tenantId.
         */
        long getTenantId();

        /**
         * <pre>
         * monitoring app eg: linux | mysql | jvm
         * </pre>
         *
         * <code>string app = 3;</code>
         *
         * @return The app.
         */
        java.lang.String getApp();

        /**
         * <pre>
         * monitoring app eg: linux | mysql | jvm
         * </pre>
         *
         * <code>string app = 3;</code>
         *
         * @return The bytes for app.
         */
        com.google.protobuf.ByteString
        getAppBytes();

        /**
         * <pre>
         * monitoring metrics eg: cpu | memory | health
         * </pre>
         *
         * <code>string metrics = 4;</code>
         *
         * @return The metrics.
         */
        java.lang.String getMetrics();

        /**
         * <pre>
         * monitoring metrics eg: cpu | memory | health
         * </pre>
         *
         * <code>string metrics = 4;</code>
         *
         * @return The bytes for metrics.
         */
        com.google.protobuf.ByteString
        getMetricsBytes();

        /**
         * <pre>
         * monitoring collect priority &gt;=0
         * </pre>
         *
         * <code>uint32 priority = 5;</code>
         *
         * @return The priority.
         */
        int getPriority();

        /**
         * <pre>
         * collect timestamp
         * </pre>
         *
         * <code>uint64 time = 6;</code>
         *
         * @return The time.
         */
        long getTime();

        /**
         * <pre>
         * collect response code
         * </pre>
         *
         * <code>.org.apache.hertzbeat.common.entity.message.Code code = 7;</code>
         *
         * @return The enum numeric value on the wire for code.
         */
        int getCodeValue();

        /**
         * <pre>
         * collect response code
         * </pre>
         *
         * <code>.org.apache.hertzbeat.common.entity.message.Code code = 7;</code>
         *
         * @return The code.
         */
        org.apache.hertzbeat.common.entity.message.CollectRep.Code getCode();

        /**
         * <pre>
         * collect response error message
         * </pre>
         *
         * <code>string msg = 8;</code>
         *
         * @return The msg.
         */
        java.lang.String getMsg();

        /**
         * <pre>
         * collect response error message
         * </pre>
         *
         * <code>string msg = 8;</code>
         *
         * @return The bytes for msg.
         */
        com.google.protobuf.ByteString
        getMsgBytes();

        /**
         * <pre>
         * monitoring collect metric field
         * </pre>
         *
         * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
         */
        java.util.List<org.apache.hertzbeat.common.entity.message.CollectRep.Field>
        getFieldsList();

        /**
         * <pre>
         * monitoring collect metric field
         * </pre>
         *
         * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
         */
        org.apache.hertzbeat.common.entity.message.CollectRep.Field getFields(int index);

        /**
         * <pre>
         * monitoring collect metric field
         * </pre>
         *
         * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
         */
        int getFieldsCount();

        /**
         * <pre>
         * monitoring collect metric field
         * </pre>
         *
         * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
         */
        java.util.List<? extends org.apache.hertzbeat.common.entity.message.CollectRep.FieldOrBuilder>
        getFieldsOrBuilderList();

        /**
         * <pre>
         * monitoring collect metric field
         * </pre>
         *
         * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
         */
        org.apache.hertzbeat.common.entity.message.CollectRep.FieldOrBuilder getFieldsOrBuilder(
                int index);

        /**
         * <pre>
         * monitoring collect metric data, mapping with the fields
         * </pre>
         *
         * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
         */
        java.util.List<org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow>
        getValuesList();

        /**
         * <pre>
         * monitoring collect metric data, mapping with the fields
         * </pre>
         *
         * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
         */
        org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow getValues(int index);

        /**
         * <pre>
         * monitoring collect metric data, mapping with the fields
         * </pre>
         *
         * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
         */
        int getValuesCount();

        /**
         * <pre>
         * monitoring collect metric data, mapping with the fields
         * </pre>
         *
         * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
         */
        java.util.List<? extends org.apache.hertzbeat.common.entity.message.CollectRep.ValueRowOrBuilder>
        getValuesOrBuilderList();

        /**
         * <pre>
         * monitoring collect metric data, mapping with the fields
         * </pre>
         *
         * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
         */
        org.apache.hertzbeat.common.entity.message.CollectRep.ValueRowOrBuilder getValuesOrBuilder(
                int index);
    }

    /**
     * Protobuf type {@code org.apache.hertzbeat.common.entity.message.MetricsData}
     */
    public static final class MetricsData extends
            com.google.protobuf.GeneratedMessageV3 implements
            // @@protoc_insertion_point(message_implements:org.apache.hertzbeat.common.entity.message.MetricsData)
            MetricsDataOrBuilder {
        private static final long serialVersionUID = 0L;

        // Use MetricsData.newBuilder() to construct.
        private MetricsData(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
            super(builder);
        }

        private MetricsData() {
            app_ = "";
            metrics_ = "";
            code_ = 0;
            msg_ = "";
            fields_ = java.util.Collections.emptyList();
            values_ = java.util.Collections.emptyList();
        }

        @java.lang.Override
        @SuppressWarnings({"unused"})
        protected java.lang.Object newInstance(
                UnusedPrivateParameter unused) {
            return new MetricsData();
        }

        @java.lang.Override
        public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
            return this.unknownFields;
        }

        private MetricsData(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            this();
            if (extensionRegistry == null) {
                throw new java.lang.NullPointerException();
            }
            int mutable_bitField0_ = 0;
            com.google.protobuf.UnknownFieldSet.Builder unknownFields =
                    com.google.protobuf.UnknownFieldSet.newBuilder();
            try {
                boolean done = false;
                while (!done) {
                    int tag = input.readTag();
                    switch (tag) {
                        case 0:
                            done = true;
                            break;
                        case 8: {

                            id_ = input.readUInt64();
                            break;
                        }
                        case 16: {

                            tenantId_ = input.readUInt64();
                            break;
                        }
                        case 26: {
                            java.lang.String s = input.readStringRequireUtf8();

                            app_ = s;
                            break;
                        }
                        case 34: {
                            java.lang.String s = input.readStringRequireUtf8();

                            metrics_ = s;
                            break;
                        }
                        case 40: {

                            priority_ = input.readUInt32();
                            break;
                        }
                        case 48: {

                            time_ = input.readUInt64();
                            break;
                        }
                        case 56: {
                            int rawValue = input.readEnum();

                            code_ = rawValue;
                            break;
                        }
                        case 66: {
                            java.lang.String s = input.readStringRequireUtf8();

                            msg_ = s;
                            break;
                        }
                        case 74: {
                            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                                fields_ = new java.util.ArrayList<org.apache.hertzbeat.common.entity.message.CollectRep.Field>();
                                mutable_bitField0_ |= 0x00000001;
                            }
                            fields_.add(
                                    input.readMessage(org.apache.hertzbeat.common.entity.message.CollectRep.Field.parser(), extensionRegistry));
                            break;
                        }
                        case 82: {
                            if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                                values_ = new java.util.ArrayList<org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow>();
                                mutable_bitField0_ |= 0x00000002;
                            }
                            values_.add(
                                    input.readMessage(org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow.parser(), extensionRegistry));
                            break;
                        }
                        default: {
                            if (!parseUnknownField(
                                    input, unknownFields, extensionRegistry, tag)) {
                                done = true;
                            }
                            break;
                        }
                    }
                }
            } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                throw e.setUnfinishedMessage(this);
            } catch (com.google.protobuf.UninitializedMessageException e) {
                throw e.asInvalidProtocolBufferException().setUnfinishedMessage(this);
            } catch (java.io.IOException e) {
                throw new com.google.protobuf.InvalidProtocolBufferException(
                        e).setUnfinishedMessage(this);
            } finally {
                if (((mutable_bitField0_ & 0x00000001) != 0)) {
                    fields_ = java.util.Collections.unmodifiableList(fields_);
                }
                if (((mutable_bitField0_ & 0x00000002) != 0)) {
                    values_ = java.util.Collections.unmodifiableList(values_);
                }
                this.unknownFields = unknownFields.build();
                makeExtensionsImmutable();
            }
        }

        public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
            return org.apache.hertzbeat.common.entity.message.CollectRep.internal_static_org_apache_hertzbeat_common_entity_message_MetricsData_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
            return org.apache.hertzbeat.common.entity.message.CollectRep.internal_static_org_apache_hertzbeat_common_entity_message_MetricsData_fieldAccessorTable
                    .ensureFieldAccessorsInitialized(
                            org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData.class, org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData.Builder.class);
        }

        public static final int ID_FIELD_NUMBER = 1;
        private long id_;

        /**
         * <pre>
         * monitoring id
         * </pre>
         *
         * <code>uint64 id = 1;</code>
         *
         * @return The id.
         */
        @java.lang.Override
        public long getId() {
            return id_;
        }

        public static final int TENANTID_FIELD_NUMBER = 2;
        private long tenantId_;

        /**
         * <pre>
         * tenant id
         * </pre>
         *
         * <code>uint64 tenantId = 2;</code>
         *
         * @return The tenantId.
         */
        @java.lang.Override
        public long getTenantId() {
            return tenantId_;
        }

        public static final int APP_FIELD_NUMBER = 3;
        private volatile java.lang.Object app_;

        /**
         * <pre>
         * monitoring app eg: linux | mysql | jvm
         * </pre>
         *
         * <code>string app = 3;</code>
         *
         * @return The app.
         */
        @java.lang.Override
        public java.lang.String getApp() {
            java.lang.Object ref = app_;
            if (ref instanceof java.lang.String) {
                return (java.lang.String) ref;
            } else {
                com.google.protobuf.ByteString bs =
                        (com.google.protobuf.ByteString) ref;
                java.lang.String s = bs.toStringUtf8();
                app_ = s;
                return s;
            }
        }

        /**
         * <pre>
         * monitoring app eg: linux | mysql | jvm
         * </pre>
         *
         * <code>string app = 3;</code>
         *
         * @return The bytes for app.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getAppBytes() {
            java.lang.Object ref = app_;
            if (ref instanceof java.lang.String) {
                com.google.protobuf.ByteString b =
                        com.google.protobuf.ByteString.copyFromUtf8(
                                (java.lang.String) ref);
                app_ = b;
                return b;
            } else {
                return (com.google.protobuf.ByteString) ref;
            }
        }

        public static final int METRICS_FIELD_NUMBER = 4;
        private volatile java.lang.Object metrics_;

        /**
         * <pre>
         * monitoring metrics eg: cpu | memory | health
         * </pre>
         *
         * <code>string metrics = 4;</code>
         *
         * @return The metrics.
         */
        @java.lang.Override
        public java.lang.String getMetrics() {
            java.lang.Object ref = metrics_;
            if (ref instanceof java.lang.String) {
                return (java.lang.String) ref;
            } else {
                com.google.protobuf.ByteString bs =
                        (com.google.protobuf.ByteString) ref;
                java.lang.String s = bs.toStringUtf8();
                metrics_ = s;
                return s;
            }
        }

        /**
         * <pre>
         * monitoring metrics eg: cpu | memory | health
         * </pre>
         *
         * <code>string metrics = 4;</code>
         *
         * @return The bytes for metrics.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getMetricsBytes() {
            java.lang.Object ref = metrics_;
            if (ref instanceof java.lang.String) {
                com.google.protobuf.ByteString b =
                        com.google.protobuf.ByteString.copyFromUtf8(
                                (java.lang.String) ref);
                metrics_ = b;
                return b;
            } else {
                return (com.google.protobuf.ByteString) ref;
            }
        }

        public static final int PRIORITY_FIELD_NUMBER = 5;
        private int priority_;

        /**
         * <pre>
         * monitoring collect priority &gt;=0
         * </pre>
         *
         * <code>uint32 priority = 5;</code>
         *
         * @return The priority.
         */
        @java.lang.Override
        public int getPriority() {
            return priority_;
        }

        public static final int TIME_FIELD_NUMBER = 6;
        private long time_;

        /**
         * <pre>
         * collect timestamp
         * </pre>
         *
         * <code>uint64 time = 6;</code>
         *
         * @return The time.
         */
        @java.lang.Override
        public long getTime() {
            return time_;
        }

        public static final int CODE_FIELD_NUMBER = 7;
        private int code_;

        /**
         * <pre>
         * collect response code
         * </pre>
         *
         * <code>.org.apache.hertzbeat.common.entity.message.Code code = 7;</code>
         *
         * @return The enum numeric value on the wire for code.
         */
        @java.lang.Override
        public int getCodeValue() {
            return code_;
        }

        /**
         * <pre>
         * collect response code
         * </pre>
         *
         * <code>.org.apache.hertzbeat.common.entity.message.Code code = 7;</code>
         *
         * @return The code.
         */
        @java.lang.Override
        public org.apache.hertzbeat.common.entity.message.CollectRep.Code getCode() {
            @SuppressWarnings("deprecation")
            org.apache.hertzbeat.common.entity.message.CollectRep.Code result = org.apache.hertzbeat.common.entity.message.CollectRep.Code.valueOf(code_);
            return result == null ? org.apache.hertzbeat.common.entity.message.CollectRep.Code.UNRECOGNIZED : result;
        }

        public static final int MSG_FIELD_NUMBER = 8;
        private volatile java.lang.Object msg_;

        /**
         * <pre>
         * collect response error message
         * </pre>
         *
         * <code>string msg = 8;</code>
         *
         * @return The msg.
         */
        @java.lang.Override
        public java.lang.String getMsg() {
            java.lang.Object ref = msg_;
            if (ref instanceof java.lang.String) {
                return (java.lang.String) ref;
            } else {
                com.google.protobuf.ByteString bs =
                        (com.google.protobuf.ByteString) ref;
                java.lang.String s = bs.toStringUtf8();
                msg_ = s;
                return s;
            }
        }

        /**
         * <pre>
         * collect response error message
         * </pre>
         *
         * <code>string msg = 8;</code>
         *
         * @return The bytes for msg.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getMsgBytes() {
            java.lang.Object ref = msg_;
            if (ref instanceof java.lang.String) {
                com.google.protobuf.ByteString b =
                        com.google.protobuf.ByteString.copyFromUtf8(
                                (java.lang.String) ref);
                msg_ = b;
                return b;
            } else {
                return (com.google.protobuf.ByteString) ref;
            }
        }

        public static final int FIELDS_FIELD_NUMBER = 9;
        private java.util.List<org.apache.hertzbeat.common.entity.message.CollectRep.Field> fields_;

        /**
         * <pre>
         * monitoring collect metric field
         * </pre>
         *
         * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
         */
        @java.lang.Override
        public java.util.List<org.apache.hertzbeat.common.entity.message.CollectRep.Field> getFieldsList() {
            return fields_;
        }

        /**
         * <pre>
         * monitoring collect metric field
         * </pre>
         *
         * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
         */
        @java.lang.Override
        public java.util.List<? extends org.apache.hertzbeat.common.entity.message.CollectRep.FieldOrBuilder>
        getFieldsOrBuilderList() {
            return fields_;
        }

        /**
         * <pre>
         * monitoring collect metric field
         * </pre>
         *
         * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
         */
        @java.lang.Override
        public int getFieldsCount() {
            return fields_.size();
        }

        /**
         * <pre>
         * monitoring collect metric field
         * </pre>
         *
         * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
         */
        @java.lang.Override
        public org.apache.hertzbeat.common.entity.message.CollectRep.Field getFields(int index) {
            return fields_.get(index);
        }

        /**
         * <pre>
         * monitoring collect metric field
         * </pre>
         *
         * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
         */
        @java.lang.Override
        public org.apache.hertzbeat.common.entity.message.CollectRep.FieldOrBuilder getFieldsOrBuilder(
                int index) {
            return fields_.get(index);
        }

        public static final int VALUES_FIELD_NUMBER = 10;
        private java.util.List<org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow> values_;

        /**
         * <pre>
         * monitoring collect metric data, mapping with the fields
         * </pre>
         *
         * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
         */
        @java.lang.Override
        public java.util.List<org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow> getValuesList() {
            return values_;
        }

        /**
         * <pre>
         * monitoring collect metric data, mapping with the fields
         * </pre>
         *
         * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
         */
        @java.lang.Override
        public java.util.List<? extends org.apache.hertzbeat.common.entity.message.CollectRep.ValueRowOrBuilder>
        getValuesOrBuilderList() {
            return values_;
        }

        /**
         * <pre>
         * monitoring collect metric data, mapping with the fields
         * </pre>
         *
         * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
         */
        @java.lang.Override
        public int getValuesCount() {
            return values_.size();
        }

        /**
         * <pre>
         * monitoring collect metric data, mapping with the fields
         * </pre>
         *
         * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
         */
        @java.lang.Override
        public org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow getValues(int index) {
            return values_.get(index);
        }

        /**
         * <pre>
         * monitoring collect metric data, mapping with the fields
         * </pre>
         *
         * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
         */
        @java.lang.Override
        public org.apache.hertzbeat.common.entity.message.CollectRep.ValueRowOrBuilder getValuesOrBuilder(
                int index) {
            return values_.get(index);
        }

        private byte memoizedIsInitialized = -1;

        @java.lang.Override
        public final boolean isInitialized() {
            byte isInitialized = memoizedIsInitialized;
            if (isInitialized == 1) return true;
            if (isInitialized == 0) return false;

            memoizedIsInitialized = 1;
            return true;
        }

        @java.lang.Override
        public void writeTo(com.google.protobuf.CodedOutputStream output)
                throws java.io.IOException {
            if (id_ != 0L) {
                output.writeUInt64(1, id_);
            }
            if (tenantId_ != 0L) {
                output.writeUInt64(2, tenantId_);
            }
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(app_)) {
                com.google.protobuf.GeneratedMessageV3.writeString(output, 3, app_);
            }
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(metrics_)) {
                com.google.protobuf.GeneratedMessageV3.writeString(output, 4, metrics_);
            }
            if (priority_ != 0) {
                output.writeUInt32(5, priority_);
            }
            if (time_ != 0L) {
                output.writeUInt64(6, time_);
            }
            if (code_ != org.apache.hertzbeat.common.entity.message.CollectRep.Code.SUCCESS.getNumber()) {
                output.writeEnum(7, code_);
            }
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(msg_)) {
                com.google.protobuf.GeneratedMessageV3.writeString(output, 8, msg_);
            }
            for (int i = 0; i < fields_.size(); i++) {
                output.writeMessage(9, fields_.get(i));
            }
            for (int i = 0; i < values_.size(); i++) {
                output.writeMessage(10, values_.get(i));
            }
            unknownFields.writeTo(output);
        }

        @java.lang.Override
        public int getSerializedSize() {
            int size = memoizedSize;
            if (size != -1) return size;

            size = 0;
            if (id_ != 0L) {
                size += com.google.protobuf.CodedOutputStream
                        .computeUInt64Size(1, id_);
            }
            if (tenantId_ != 0L) {
                size += com.google.protobuf.CodedOutputStream
                        .computeUInt64Size(2, tenantId_);
            }
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(app_)) {
                size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, app_);
            }
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(metrics_)) {
                size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, metrics_);
            }
            if (priority_ != 0) {
                size += com.google.protobuf.CodedOutputStream
                        .computeUInt32Size(5, priority_);
            }
            if (time_ != 0L) {
                size += com.google.protobuf.CodedOutputStream
                        .computeUInt64Size(6, time_);
            }
            if (code_ != org.apache.hertzbeat.common.entity.message.CollectRep.Code.SUCCESS.getNumber()) {
                size += com.google.protobuf.CodedOutputStream
                        .computeEnumSize(7, code_);
            }
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(msg_)) {
                size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, msg_);
            }
            for (int i = 0; i < fields_.size(); i++) {
                size += com.google.protobuf.CodedOutputStream
                        .computeMessageSize(9, fields_.get(i));
            }
            for (int i = 0; i < values_.size(); i++) {
                size += com.google.protobuf.CodedOutputStream
                        .computeMessageSize(10, values_.get(i));
            }
            size += unknownFields.getSerializedSize();
            memoizedSize = size;
            return size;
        }

        @java.lang.Override
        public boolean equals(final java.lang.Object obj) {
            if (obj == this) {
                return true;
            }
            if (!(obj instanceof org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData)) {
                return super.equals(obj);
            }
            org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData other = (org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData) obj;

            if (getId()
                    != other.getId()) return false;
            if (getTenantId()
                    != other.getTenantId()) return false;
            if (!getApp()
                    .equals(other.getApp())) return false;
            if (!getMetrics()
                    .equals(other.getMetrics())) return false;
            if (getPriority()
                    != other.getPriority()) return false;
            if (getTime()
                    != other.getTime()) return false;
            if (code_ != other.code_) return false;
            if (!getMsg()
                    .equals(other.getMsg())) return false;
            if (!getFieldsList()
                    .equals(other.getFieldsList())) return false;
            if (!getValuesList()
                    .equals(other.getValuesList())) return false;
            if (!unknownFields.equals(other.unknownFields)) return false;
            return true;
        }

        @java.lang.Override
        public int hashCode() {
            if (memoizedHashCode != 0) {
                return memoizedHashCode;
            }
            int hash = 41;
            hash = (19 * hash) + getDescriptor().hashCode();
            hash = (37 * hash) + ID_FIELD_NUMBER;
            hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
                    getId());
            hash = (37 * hash) + TENANTID_FIELD_NUMBER;
            hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
                    getTenantId());
            hash = (37 * hash) + APP_FIELD_NUMBER;
            hash = (53 * hash) + getApp().hashCode();
            hash = (37 * hash) + METRICS_FIELD_NUMBER;
            hash = (53 * hash) + getMetrics().hashCode();
            hash = (37 * hash) + PRIORITY_FIELD_NUMBER;
            hash = (53 * hash) + getPriority();
            hash = (37 * hash) + TIME_FIELD_NUMBER;
            hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
                    getTime());
            hash = (37 * hash) + CODE_FIELD_NUMBER;
            hash = (53 * hash) + code_;
            hash = (37 * hash) + MSG_FIELD_NUMBER;
            hash = (53 * hash) + getMsg().hashCode();
            if (getFieldsCount() > 0) {
                hash = (37 * hash) + FIELDS_FIELD_NUMBER;
                hash = (53 * hash) + getFieldsList().hashCode();
            }
            if (getValuesCount() > 0) {
                hash = (37 * hash) + VALUES_FIELD_NUMBER;
                hash = (53 * hash) + getValuesList().hashCode();
            }
            hash = (29 * hash) + unknownFields.hashCode();
            memoizedHashCode = hash;
            return hash;
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData parseFrom(
                java.nio.ByteBuffer data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData parseFrom(
                java.nio.ByteBuffer data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData parseFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        @java.lang.Override
        public Builder newBuilderForType() {
            return newBuilder();
        }

        public static Builder newBuilder() {
            return DEFAULT_INSTANCE.toBuilder();
        }

        public static Builder newBuilder(org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData prototype) {
            return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }

        @java.lang.Override
        public Builder toBuilder() {
            return this == DEFAULT_INSTANCE
                    ? new Builder() : new Builder().mergeFrom(this);
        }

        @java.lang.Override
        protected Builder newBuilderForType(
                com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
            Builder builder = new Builder(parent);
            return builder;
        }

        /**
         * Protobuf type {@code org.apache.hertzbeat.common.entity.message.MetricsData}
         */
        public static final class Builder extends
                com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
                // @@protoc_insertion_point(builder_implements:org.apache.hertzbeat.common.entity.message.MetricsData)
                org.apache.hertzbeat.common.entity.message.CollectRep.MetricsDataOrBuilder {
            public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
                return org.apache.hertzbeat.common.entity.message.CollectRep.internal_static_org_apache_hertzbeat_common_entity_message_MetricsData_descriptor;
            }

            @java.lang.Override
            protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
                return org.apache.hertzbeat.common.entity.message.CollectRep.internal_static_org_apache_hertzbeat_common_entity_message_MetricsData_fieldAccessorTable
                        .ensureFieldAccessorsInitialized(
                                org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData.class, org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData.Builder.class);
            }

            // Construct using org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData.newBuilder()
            private Builder() {
                maybeForceBuilderInitialization();
            }

            private Builder(
                    com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
                super(parent);
                maybeForceBuilderInitialization();
            }

            private void maybeForceBuilderInitialization() {
                if (com.google.protobuf.GeneratedMessageV3
                        .alwaysUseFieldBuilders) {
                    getFieldsFieldBuilder();
                    getValuesFieldBuilder();
                }
            }

            @java.lang.Override
            public Builder clear() {
                super.clear();
                id_ = 0L;

                tenantId_ = 0L;

                app_ = "";

                metrics_ = "";

                priority_ = 0;

                time_ = 0L;

                code_ = 0;

                msg_ = "";

                if (fieldsBuilder_ == null) {
                    fields_ = java.util.Collections.emptyList();
                    bitField0_ = (bitField0_ & ~0x00000001);
                } else {
                    fieldsBuilder_.clear();
                }
                if (valuesBuilder_ == null) {
                    values_ = java.util.Collections.emptyList();
                    bitField0_ = (bitField0_ & ~0x00000002);
                } else {
                    valuesBuilder_.clear();
                }
                return this;
            }

            @java.lang.Override
            public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
                return org.apache.hertzbeat.common.entity.message.CollectRep.internal_static_org_apache_hertzbeat_common_entity_message_MetricsData_descriptor;
            }

            @java.lang.Override
            public org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData getDefaultInstanceForType() {
                return org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData.getDefaultInstance();
            }

            @java.lang.Override
            public org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData build() {
                org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData result = buildPartial();
                if (!result.isInitialized()) {
                    throw newUninitializedMessageException(result);
                }
                return result;
            }

            @java.lang.Override
            public org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData buildPartial() {
                org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData result = new org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData(this);
                int from_bitField0_ = bitField0_;
                result.id_ = id_;
                result.tenantId_ = tenantId_;
                result.app_ = app_;
                result.metrics_ = metrics_;
                result.priority_ = priority_;
                result.time_ = time_;
                result.code_ = code_;
                result.msg_ = msg_;
                if (fieldsBuilder_ == null) {
                    if (((bitField0_ & 0x00000001) != 0)) {
                        fields_ = java.util.Collections.unmodifiableList(fields_);
                        bitField0_ = (bitField0_ & ~0x00000001);
                    }
                    result.fields_ = fields_;
                } else {
                    result.fields_ = fieldsBuilder_.build();
                }
                if (valuesBuilder_ == null) {
                    if (((bitField0_ & 0x00000002) != 0)) {
                        values_ = java.util.Collections.unmodifiableList(values_);
                        bitField0_ = (bitField0_ & ~0x00000002);
                    }
                    result.values_ = values_;
                } else {
                    result.values_ = valuesBuilder_.build();
                }
                onBuilt();
                return result;
            }

            @java.lang.Override
            public Builder clone() {
                return super.clone();
            }

            @java.lang.Override
            public Builder setField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.setField(field, value);
            }

            @java.lang.Override
            public Builder clearField(
                    com.google.protobuf.Descriptors.FieldDescriptor field) {
                return super.clearField(field);
            }

            @java.lang.Override
            public Builder clearOneof(
                    com.google.protobuf.Descriptors.OneofDescriptor oneof) {
                return super.clearOneof(oneof);
            }

            @java.lang.Override
            public Builder setRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    int index, java.lang.Object value) {
                return super.setRepeatedField(field, index, value);
            }

            @java.lang.Override
            public Builder addRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.addRepeatedField(field, value);
            }

            @java.lang.Override
            public Builder mergeFrom(com.google.protobuf.Message other) {
                if (other instanceof org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData) {
                    return mergeFrom((org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData) other);
                } else {
                    super.mergeFrom(other);
                    return this;
                }
            }

            public Builder mergeFrom(org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData other) {
                if (other == org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData.getDefaultInstance())
                    return this;
                if (other.getId() != 0L) {
                    setId(other.getId());
                }
                if (other.getTenantId() != 0L) {
                    setTenantId(other.getTenantId());
                }
                if (!other.getApp().isEmpty()) {
                    app_ = other.app_;
                    onChanged();
                }
                if (!other.getMetrics().isEmpty()) {
                    metrics_ = other.metrics_;
                    onChanged();
                }
                if (other.getPriority() != 0) {
                    setPriority(other.getPriority());
                }
                if (other.getTime() != 0L) {
                    setTime(other.getTime());
                }
                if (other.code_ != 0) {
                    setCodeValue(other.getCodeValue());
                }
                if (!other.getMsg().isEmpty()) {
                    msg_ = other.msg_;
                    onChanged();
                }
                if (fieldsBuilder_ == null) {
                    if (!other.fields_.isEmpty()) {
                        if (fields_.isEmpty()) {
                            fields_ = other.fields_;
                            bitField0_ = (bitField0_ & ~0x00000001);
                        } else {
                            ensureFieldsIsMutable();
                            fields_.addAll(other.fields_);
                        }
                        onChanged();
                    }
                } else {
                    if (!other.fields_.isEmpty()) {
                        if (fieldsBuilder_.isEmpty()) {
                            fieldsBuilder_.dispose();
                            fieldsBuilder_ = null;
                            fields_ = other.fields_;
                            bitField0_ = (bitField0_ & ~0x00000001);
                            fieldsBuilder_ =
                                    com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                                            getFieldsFieldBuilder() : null;
                        } else {
                            fieldsBuilder_.addAllMessages(other.fields_);
                        }
                    }
                }
                if (valuesBuilder_ == null) {
                    if (!other.values_.isEmpty()) {
                        if (values_.isEmpty()) {
                            values_ = other.values_;
                            bitField0_ = (bitField0_ & ~0x00000002);
                        } else {
                            ensureValuesIsMutable();
                            values_.addAll(other.values_);
                        }
                        onChanged();
                    }
                } else {
                    if (!other.values_.isEmpty()) {
                        if (valuesBuilder_.isEmpty()) {
                            valuesBuilder_.dispose();
                            valuesBuilder_ = null;
                            values_ = other.values_;
                            bitField0_ = (bitField0_ & ~0x00000002);
                            valuesBuilder_ =
                                    com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                                            getValuesFieldBuilder() : null;
                        } else {
                            valuesBuilder_.addAllMessages(other.values_);
                        }
                    }
                }
                this.mergeUnknownFields(other.unknownFields);
                onChanged();
                return this;
            }

            @java.lang.Override
            public final boolean isInitialized() {
                return true;
            }

            @java.lang.Override
            public Builder mergeFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws java.io.IOException {
                org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData parsedMessage = null;
                try {
                    parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    parsedMessage = (org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData) e.getUnfinishedMessage();
                    throw e.unwrapIOException();
                } finally {
                    if (parsedMessage != null) {
                        mergeFrom(parsedMessage);
                    }
                }
                return this;
            }

            private int bitField0_;

            private long id_;

            /**
             * <pre>
             * monitoring id
             * </pre>
             *
             * <code>uint64 id = 1;</code>
             *
             * @return The id.
             */
            @java.lang.Override
            public long getId() {
                return id_;
            }

            /**
             * <pre>
             * monitoring id
             * </pre>
             *
             * <code>uint64 id = 1;</code>
             *
             * @param value The id to set.
             * @return This builder for chaining.
             */
            public Builder setId(long value) {

                id_ = value;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * monitoring id
             * </pre>
             *
             * <code>uint64 id = 1;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearId() {

                id_ = 0L;
                onChanged();
                return this;
            }

            private long tenantId_;

            /**
             * <pre>
             * tenant id
             * </pre>
             *
             * <code>uint64 tenantId = 2;</code>
             *
             * @return The tenantId.
             */
            @java.lang.Override
            public long getTenantId() {
                return tenantId_;
            }

            /**
             * <pre>
             * tenant id
             * </pre>
             *
             * <code>uint64 tenantId = 2;</code>
             *
             * @param value The tenantId to set.
             * @return This builder for chaining.
             */
            public Builder setTenantId(long value) {

                tenantId_ = value;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * tenant id
             * </pre>
             *
             * <code>uint64 tenantId = 2;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearTenantId() {

                tenantId_ = 0L;
                onChanged();
                return this;
            }

            private java.lang.Object app_ = "";

            /**
             * <pre>
             * monitoring app eg: linux | mysql | jvm
             * </pre>
             *
             * <code>string app = 3;</code>
             *
             * @return The app.
             */
            public java.lang.String getApp() {
                java.lang.Object ref = app_;
                if (!(ref instanceof java.lang.String)) {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    java.lang.String s = bs.toStringUtf8();
                    app_ = s;
                    return s;
                } else {
                    return (java.lang.String) ref;
                }
            }

            /**
             * <pre>
             * monitoring app eg: linux | mysql | jvm
             * </pre>
             *
             * <code>string app = 3;</code>
             *
             * @return The bytes for app.
             */
            public com.google.protobuf.ByteString
            getAppBytes() {
                java.lang.Object ref = app_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (java.lang.String) ref);
                    app_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            /**
             * <pre>
             * monitoring app eg: linux | mysql | jvm
             * </pre>
             *
             * <code>string app = 3;</code>
             *
             * @param value The app to set.
             * @return This builder for chaining.
             */
            public Builder setApp(
                    java.lang.String value) {
                if (value == null) {
                    throw new NullPointerException();
                }

                app_ = value;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * monitoring app eg: linux | mysql | jvm
             * </pre>
             *
             * <code>string app = 3;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearApp() {

                app_ = getDefaultInstance().getApp();
                onChanged();
                return this;
            }

            /**
             * <pre>
             * monitoring app eg: linux | mysql | jvm
             * </pre>
             *
             * <code>string app = 3;</code>
             *
             * @param value The bytes for app to set.
             * @return This builder for chaining.
             */
            public Builder setAppBytes(
                    com.google.protobuf.ByteString value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                checkByteStringIsUtf8(value);

                app_ = value;
                onChanged();
                return this;
            }

            private java.lang.Object metrics_ = "";

            /**
             * <pre>
             * monitoring metrics eg: cpu | memory | health
             * </pre>
             *
             * <code>string metrics = 4;</code>
             *
             * @return The metrics.
             */
            public java.lang.String getMetrics() {
                java.lang.Object ref = metrics_;
                if (!(ref instanceof java.lang.String)) {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    java.lang.String s = bs.toStringUtf8();
                    metrics_ = s;
                    return s;
                } else {
                    return (java.lang.String) ref;
                }
            }

            /**
             * <pre>
             * monitoring metrics eg: cpu | memory | health
             * </pre>
             *
             * <code>string metrics = 4;</code>
             *
             * @return The bytes for metrics.
             */
            public com.google.protobuf.ByteString
            getMetricsBytes() {
                java.lang.Object ref = metrics_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (java.lang.String) ref);
                    metrics_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            /**
             * <pre>
             * monitoring metrics eg: cpu | memory | health
             * </pre>
             *
             * <code>string metrics = 4;</code>
             *
             * @param value The metrics to set.
             * @return This builder for chaining.
             */
            public Builder setMetrics(
                    java.lang.String value) {
                if (value == null) {
                    throw new NullPointerException();
                }

                metrics_ = value;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * monitoring metrics eg: cpu | memory | health
             * </pre>
             *
             * <code>string metrics = 4;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearMetrics() {

                metrics_ = getDefaultInstance().getMetrics();
                onChanged();
                return this;
            }

            /**
             * <pre>
             * monitoring metrics eg: cpu | memory | health
             * </pre>
             *
             * <code>string metrics = 4;</code>
             *
             * @param value The bytes for metrics to set.
             * @return This builder for chaining.
             */
            public Builder setMetricsBytes(
                    com.google.protobuf.ByteString value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                checkByteStringIsUtf8(value);

                metrics_ = value;
                onChanged();
                return this;
            }

            private int priority_;

            /**
             * <pre>
             * monitoring collect priority &gt;=0
             * </pre>
             *
             * <code>uint32 priority = 5;</code>
             *
             * @return The priority.
             */
            @java.lang.Override
            public int getPriority() {
                return priority_;
            }

            /**
             * <pre>
             * monitoring collect priority &gt;=0
             * </pre>
             *
             * <code>uint32 priority = 5;</code>
             *
             * @param value The priority to set.
             * @return This builder for chaining.
             */
            public Builder setPriority(int value) {

                priority_ = value;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * monitoring collect priority &gt;=0
             * </pre>
             *
             * <code>uint32 priority = 5;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearPriority() {

                priority_ = 0;
                onChanged();
                return this;
            }

            private long time_;

            /**
             * <pre>
             * collect timestamp
             * </pre>
             *
             * <code>uint64 time = 6;</code>
             *
             * @return The time.
             */
            @java.lang.Override
            public long getTime() {
                return time_;
            }

            /**
             * <pre>
             * collect timestamp
             * </pre>
             *
             * <code>uint64 time = 6;</code>
             *
             * @param value The time to set.
             * @return This builder for chaining.
             */
            public Builder setTime(long value) {

                time_ = value;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * collect timestamp
             * </pre>
             *
             * <code>uint64 time = 6;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearTime() {

                time_ = 0L;
                onChanged();
                return this;
            }

            private int code_ = 0;

            /**
             * <pre>
             * collect response code
             * </pre>
             *
             * <code>.org.apache.hertzbeat.common.entity.message.Code code = 7;</code>
             *
             * @return The enum numeric value on the wire for code.
             */
            @java.lang.Override
            public int getCodeValue() {
                return code_;
            }

            /**
             * <pre>
             * collect response code
             * </pre>
             *
             * <code>.org.apache.hertzbeat.common.entity.message.Code code = 7;</code>
             *
             * @param value The enum numeric value on the wire for code to set.
             * @return This builder for chaining.
             */
            public Builder setCodeValue(int value) {

                code_ = value;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * collect response code
             * </pre>
             *
             * <code>.org.apache.hertzbeat.common.entity.message.Code code = 7;</code>
             *
             * @return The code.
             */
            @java.lang.Override
            public org.apache.hertzbeat.common.entity.message.CollectRep.Code getCode() {
                @SuppressWarnings("deprecation")
                org.apache.hertzbeat.common.entity.message.CollectRep.Code result = org.apache.hertzbeat.common.entity.message.CollectRep.Code.valueOf(code_);
                return result == null ? org.apache.hertzbeat.common.entity.message.CollectRep.Code.UNRECOGNIZED : result;
            }

            /**
             * <pre>
             * collect response code
             * </pre>
             *
             * <code>.org.apache.hertzbeat.common.entity.message.Code code = 7;</code>
             *
             * @param value The code to set.
             * @return This builder for chaining.
             */
            public Builder setCode(org.apache.hertzbeat.common.entity.message.CollectRep.Code value) {
                if (value == null) {
                    throw new NullPointerException();
                }

                code_ = value.getNumber();
                onChanged();
                return this;
            }

            /**
             * <pre>
             * collect response code
             * </pre>
             *
             * <code>.org.apache.hertzbeat.common.entity.message.Code code = 7;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearCode() {

                code_ = 0;
                onChanged();
                return this;
            }

            private java.lang.Object msg_ = "";

            /**
             * <pre>
             * collect response error message
             * </pre>
             *
             * <code>string msg = 8;</code>
             *
             * @return The msg.
             */
            public java.lang.String getMsg() {
                java.lang.Object ref = msg_;
                if (!(ref instanceof java.lang.String)) {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    java.lang.String s = bs.toStringUtf8();
                    msg_ = s;
                    return s;
                } else {
                    return (java.lang.String) ref;
                }
            }

            /**
             * <pre>
             * collect response error message
             * </pre>
             *
             * <code>string msg = 8;</code>
             *
             * @return The bytes for msg.
             */
            public com.google.protobuf.ByteString
            getMsgBytes() {
                java.lang.Object ref = msg_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (java.lang.String) ref);
                    msg_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            /**
             * <pre>
             * collect response error message
             * </pre>
             *
             * <code>string msg = 8;</code>
             *
             * @param value The msg to set.
             * @return This builder for chaining.
             */
            public Builder setMsg(
                    java.lang.String value) {
                if (value == null) {
                    throw new NullPointerException();
                }

                msg_ = value;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * collect response error message
             * </pre>
             *
             * <code>string msg = 8;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearMsg() {

                msg_ = getDefaultInstance().getMsg();
                onChanged();
                return this;
            }

            /**
             * <pre>
             * collect response error message
             * </pre>
             *
             * <code>string msg = 8;</code>
             *
             * @param value The bytes for msg to set.
             * @return This builder for chaining.
             */
            public Builder setMsgBytes(
                    com.google.protobuf.ByteString value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                checkByteStringIsUtf8(value);

                msg_ = value;
                onChanged();
                return this;
            }

            private java.util.List<org.apache.hertzbeat.common.entity.message.CollectRep.Field> fields_ =
                    java.util.Collections.emptyList();

            private void ensureFieldsIsMutable() {
                if (!((bitField0_ & 0x00000001) != 0)) {
                    fields_ = new java.util.ArrayList<org.apache.hertzbeat.common.entity.message.CollectRep.Field>(fields_);
                    bitField0_ |= 0x00000001;
                }
            }

            private com.google.protobuf.RepeatedFieldBuilderV3<
                    org.apache.hertzbeat.common.entity.message.CollectRep.Field, org.apache.hertzbeat.common.entity.message.CollectRep.Field.Builder, org.apache.hertzbeat.common.entity.message.CollectRep.FieldOrBuilder> fieldsBuilder_;

            /**
             * <pre>
             * monitoring collect metric field
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
             */
            public java.util.List<org.apache.hertzbeat.common.entity.message.CollectRep.Field> getFieldsList() {
                if (fieldsBuilder_ == null) {
                    return java.util.Collections.unmodifiableList(fields_);
                } else {
                    return fieldsBuilder_.getMessageList();
                }
            }

            /**
             * <pre>
             * monitoring collect metric field
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
             */
            public int getFieldsCount() {
                if (fieldsBuilder_ == null) {
                    return fields_.size();
                } else {
                    return fieldsBuilder_.getCount();
                }
            }

            /**
             * <pre>
             * monitoring collect metric field
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
             */
            public org.apache.hertzbeat.common.entity.message.CollectRep.Field getFields(int index) {
                if (fieldsBuilder_ == null) {
                    return fields_.get(index);
                } else {
                    return fieldsBuilder_.getMessage(index);
                }
            }

            /**
             * <pre>
             * monitoring collect metric field
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
             */
            public Builder setFields(
                    int index, org.apache.hertzbeat.common.entity.message.CollectRep.Field value) {
                if (fieldsBuilder_ == null) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    ensureFieldsIsMutable();
                    fields_.set(index, value);
                    onChanged();
                } else {
                    fieldsBuilder_.setMessage(index, value);
                }
                return this;
            }

            /**
             * <pre>
             * monitoring collect metric field
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
             */
            public Builder setFields(
                    int index, org.apache.hertzbeat.common.entity.message.CollectRep.Field.Builder builderForValue) {
                if (fieldsBuilder_ == null) {
                    ensureFieldsIsMutable();
                    fields_.set(index, builderForValue.build());
                    onChanged();
                } else {
                    fieldsBuilder_.setMessage(index, builderForValue.build());
                }
                return this;
            }

            /**
             * <pre>
             * monitoring collect metric field
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
             */
            public Builder addFields(org.apache.hertzbeat.common.entity.message.CollectRep.Field value) {
                if (fieldsBuilder_ == null) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    ensureFieldsIsMutable();
                    fields_.add(value);
                    onChanged();
                } else {
                    fieldsBuilder_.addMessage(value);
                }
                return this;
            }

            /**
             * <pre>
             * monitoring collect metric field
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
             */
            public Builder addFields(
                    int index, org.apache.hertzbeat.common.entity.message.CollectRep.Field value) {
                if (fieldsBuilder_ == null) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    ensureFieldsIsMutable();
                    fields_.add(index, value);
                    onChanged();
                } else {
                    fieldsBuilder_.addMessage(index, value);
                }
                return this;
            }

            /**
             * <pre>
             * monitoring collect metric field
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
             */
            public Builder addFields(
                    org.apache.hertzbeat.common.entity.message.CollectRep.Field.Builder builderForValue) {
                if (fieldsBuilder_ == null) {
                    ensureFieldsIsMutable();
                    fields_.add(builderForValue.build());
                    onChanged();
                } else {
                    fieldsBuilder_.addMessage(builderForValue.build());
                }
                return this;
            }

            /**
             * <pre>
             * monitoring collect metric field
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
             */
            public Builder addFields(
                    int index, org.apache.hertzbeat.common.entity.message.CollectRep.Field.Builder builderForValue) {
                if (fieldsBuilder_ == null) {
                    ensureFieldsIsMutable();
                    fields_.add(index, builderForValue.build());
                    onChanged();
                } else {
                    fieldsBuilder_.addMessage(index, builderForValue.build());
                }
                return this;
            }

            /**
             * <pre>
             * monitoring collect metric field
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
             */
            public Builder addAllFields(
                    java.lang.Iterable<? extends org.apache.hertzbeat.common.entity.message.CollectRep.Field> values) {
                if (fieldsBuilder_ == null) {
                    ensureFieldsIsMutable();
                    com.google.protobuf.AbstractMessageLite.Builder.addAll(
                            values, fields_);
                    onChanged();
                } else {
                    fieldsBuilder_.addAllMessages(values);
                }
                return this;
            }

            /**
             * <pre>
             * monitoring collect metric field
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
             */
            public Builder clearFields() {
                if (fieldsBuilder_ == null) {
                    fields_ = java.util.Collections.emptyList();
                    bitField0_ = (bitField0_ & ~0x00000001);
                    onChanged();
                } else {
                    fieldsBuilder_.clear();
                }
                return this;
            }

            /**
             * <pre>
             * monitoring collect metric field
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
             */
            public Builder removeFields(int index) {
                if (fieldsBuilder_ == null) {
                    ensureFieldsIsMutable();
                    fields_.remove(index);
                    onChanged();
                } else {
                    fieldsBuilder_.remove(index);
                }
                return this;
            }

            /**
             * <pre>
             * monitoring collect metric field
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
             */
            public org.apache.hertzbeat.common.entity.message.CollectRep.Field.Builder getFieldsBuilder(
                    int index) {
                return getFieldsFieldBuilder().getBuilder(index);
            }

            /**
             * <pre>
             * monitoring collect metric field
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
             */
            public org.apache.hertzbeat.common.entity.message.CollectRep.FieldOrBuilder getFieldsOrBuilder(
                    int index) {
                if (fieldsBuilder_ == null) {
                    return fields_.get(index);
                } else {
                    return fieldsBuilder_.getMessageOrBuilder(index);
                }
            }

            /**
             * <pre>
             * monitoring collect metric field
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
             */
            public java.util.List<? extends org.apache.hertzbeat.common.entity.message.CollectRep.FieldOrBuilder>
            getFieldsOrBuilderList() {
                if (fieldsBuilder_ != null) {
                    return fieldsBuilder_.getMessageOrBuilderList();
                } else {
                    return java.util.Collections.unmodifiableList(fields_);
                }
            }

            /**
             * <pre>
             * monitoring collect metric field
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
             */
            public org.apache.hertzbeat.common.entity.message.CollectRep.Field.Builder addFieldsBuilder() {
                return getFieldsFieldBuilder().addBuilder(
                        org.apache.hertzbeat.common.entity.message.CollectRep.Field.getDefaultInstance());
            }

            /**
             * <pre>
             * monitoring collect metric field
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
             */
            public org.apache.hertzbeat.common.entity.message.CollectRep.Field.Builder addFieldsBuilder(
                    int index) {
                return getFieldsFieldBuilder().addBuilder(
                        index, org.apache.hertzbeat.common.entity.message.CollectRep.Field.getDefaultInstance());
            }

            /**
             * <pre>
             * monitoring collect metric field
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.Field fields = 9;</code>
             */
            public java.util.List<org.apache.hertzbeat.common.entity.message.CollectRep.Field.Builder>
            getFieldsBuilderList() {
                return getFieldsFieldBuilder().getBuilderList();
            }

            private com.google.protobuf.RepeatedFieldBuilderV3<
                    org.apache.hertzbeat.common.entity.message.CollectRep.Field, org.apache.hertzbeat.common.entity.message.CollectRep.Field.Builder, org.apache.hertzbeat.common.entity.message.CollectRep.FieldOrBuilder>
            getFieldsFieldBuilder() {
                if (fieldsBuilder_ == null) {
                    fieldsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
                            org.apache.hertzbeat.common.entity.message.CollectRep.Field, org.apache.hertzbeat.common.entity.message.CollectRep.Field.Builder, org.apache.hertzbeat.common.entity.message.CollectRep.FieldOrBuilder>(
                            fields_,
                            ((bitField0_ & 0x00000001) != 0),
                            getParentForChildren(),
                            isClean());
                    fields_ = null;
                }
                return fieldsBuilder_;
            }

            private java.util.List<org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow> values_ =
                    java.util.Collections.emptyList();

            private void ensureValuesIsMutable() {
                if (!((bitField0_ & 0x00000002) != 0)) {
                    values_ = new java.util.ArrayList<org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow>(values_);
                    bitField0_ |= 0x00000002;
                }
            }

            private com.google.protobuf.RepeatedFieldBuilderV3<
                    org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow, org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow.Builder, org.apache.hertzbeat.common.entity.message.CollectRep.ValueRowOrBuilder> valuesBuilder_;

            /**
             * <pre>
             * monitoring collect metric data, mapping with the fields
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
             */
            public java.util.List<org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow> getValuesList() {
                if (valuesBuilder_ == null) {
                    return java.util.Collections.unmodifiableList(values_);
                } else {
                    return valuesBuilder_.getMessageList();
                }
            }

            /**
             * <pre>
             * monitoring collect metric data, mapping with the fields
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
             */
            public int getValuesCount() {
                if (valuesBuilder_ == null) {
                    return values_.size();
                } else {
                    return valuesBuilder_.getCount();
                }
            }

            /**
             * <pre>
             * monitoring collect metric data, mapping with the fields
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
             */
            public org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow getValues(int index) {
                if (valuesBuilder_ == null) {
                    return values_.get(index);
                } else {
                    return valuesBuilder_.getMessage(index);
                }
            }

            /**
             * <pre>
             * monitoring collect metric data, mapping with the fields
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
             */
            public Builder setValues(
                    int index, org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow value) {
                if (valuesBuilder_ == null) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    ensureValuesIsMutable();
                    values_.set(index, value);
                    onChanged();
                } else {
                    valuesBuilder_.setMessage(index, value);
                }
                return this;
            }

            /**
             * <pre>
             * monitoring collect metric data, mapping with the fields
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
             */
            public Builder setValues(
                    int index, org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow.Builder builderForValue) {
                if (valuesBuilder_ == null) {
                    ensureValuesIsMutable();
                    values_.set(index, builderForValue.build());
                    onChanged();
                } else {
                    valuesBuilder_.setMessage(index, builderForValue.build());
                }
                return this;
            }

            /**
             * <pre>
             * monitoring collect metric data, mapping with the fields
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
             */
            public Builder addValues(org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow value) {
                if (valuesBuilder_ == null) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    ensureValuesIsMutable();
                    values_.add(value);
                    onChanged();
                } else {
                    valuesBuilder_.addMessage(value);
                }
                return this;
            }

            /**
             * <pre>
             * monitoring collect metric data, mapping with the fields
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
             */
            public Builder addValues(
                    int index, org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow value) {
                if (valuesBuilder_ == null) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    ensureValuesIsMutable();
                    values_.add(index, value);
                    onChanged();
                } else {
                    valuesBuilder_.addMessage(index, value);
                }
                return this;
            }

            /**
             * <pre>
             * monitoring collect metric data, mapping with the fields
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
             */
            public Builder addValues(
                    org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow.Builder builderForValue) {
                if (valuesBuilder_ == null) {
                    ensureValuesIsMutable();
                    values_.add(builderForValue.build());
                    onChanged();
                } else {
                    valuesBuilder_.addMessage(builderForValue.build());
                }
                return this;
            }

            /**
             * <pre>
             * monitoring collect metric data, mapping with the fields
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
             */
            public Builder addValues(
                    int index, org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow.Builder builderForValue) {
                if (valuesBuilder_ == null) {
                    ensureValuesIsMutable();
                    values_.add(index, builderForValue.build());
                    onChanged();
                } else {
                    valuesBuilder_.addMessage(index, builderForValue.build());
                }
                return this;
            }

            /**
             * <pre>
             * monitoring collect metric data, mapping with the fields
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
             */
            public Builder addAllValues(
                    java.lang.Iterable<? extends org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow> values) {
                if (valuesBuilder_ == null) {
                    ensureValuesIsMutable();
                    com.google.protobuf.AbstractMessageLite.Builder.addAll(
                            values, values_);
                    onChanged();
                } else {
                    valuesBuilder_.addAllMessages(values);
                }
                return this;
            }

            /**
             * <pre>
             * monitoring collect metric data, mapping with the fields
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
             */
            public Builder clearValues() {
                if (valuesBuilder_ == null) {
                    values_ = java.util.Collections.emptyList();
                    bitField0_ = (bitField0_ & ~0x00000002);
                    onChanged();
                } else {
                    valuesBuilder_.clear();
                }
                return this;
            }

            /**
             * <pre>
             * monitoring collect metric data, mapping with the fields
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
             */
            public Builder removeValues(int index) {
                if (valuesBuilder_ == null) {
                    ensureValuesIsMutable();
                    values_.remove(index);
                    onChanged();
                } else {
                    valuesBuilder_.remove(index);
                }
                return this;
            }

            /**
             * <pre>
             * monitoring collect metric data, mapping with the fields
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
             */
            public org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow.Builder getValuesBuilder(
                    int index) {
                return getValuesFieldBuilder().getBuilder(index);
            }

            /**
             * <pre>
             * monitoring collect metric data, mapping with the fields
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
             */
            public org.apache.hertzbeat.common.entity.message.CollectRep.ValueRowOrBuilder getValuesOrBuilder(
                    int index) {
                if (valuesBuilder_ == null) {
                    return values_.get(index);
                } else {
                    return valuesBuilder_.getMessageOrBuilder(index);
                }
            }

            /**
             * <pre>
             * monitoring collect metric data, mapping with the fields
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
             */
            public java.util.List<? extends org.apache.hertzbeat.common.entity.message.CollectRep.ValueRowOrBuilder>
            getValuesOrBuilderList() {
                if (valuesBuilder_ != null) {
                    return valuesBuilder_.getMessageOrBuilderList();
                } else {
                    return java.util.Collections.unmodifiableList(values_);
                }
            }

            /**
             * <pre>
             * monitoring collect metric data, mapping with the fields
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
             */
            public org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow.Builder addValuesBuilder() {
                return getValuesFieldBuilder().addBuilder(
                        org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow.getDefaultInstance());
            }

            /**
             * <pre>
             * monitoring collect metric data, mapping with the fields
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
             */
            public org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow.Builder addValuesBuilder(
                    int index) {
                return getValuesFieldBuilder().addBuilder(
                        index, org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow.getDefaultInstance());
            }

            /**
             * <pre>
             * monitoring collect metric data, mapping with the fields
             * </pre>
             *
             * <code>repeated .org.apache.hertzbeat.common.entity.message.ValueRow values = 10;</code>
             */
            public java.util.List<org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow.Builder>
            getValuesBuilderList() {
                return getValuesFieldBuilder().getBuilderList();
            }

            private com.google.protobuf.RepeatedFieldBuilderV3<
                    org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow, org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow.Builder, org.apache.hertzbeat.common.entity.message.CollectRep.ValueRowOrBuilder>
            getValuesFieldBuilder() {
                if (valuesBuilder_ == null) {
                    valuesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
                            org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow, org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow.Builder, org.apache.hertzbeat.common.entity.message.CollectRep.ValueRowOrBuilder>(
                            values_,
                            ((bitField0_ & 0x00000002) != 0),
                            getParentForChildren(),
                            isClean());
                    values_ = null;
                }
                return valuesBuilder_;
            }

            @java.lang.Override
            public final Builder setUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.setUnknownFields(unknownFields);
            }

            @java.lang.Override
            public final Builder mergeUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.mergeUnknownFields(unknownFields);
            }


            // @@protoc_insertion_point(builder_scope:org.apache.hertzbeat.common.entity.message.MetricsData)
        }

        // @@protoc_insertion_point(class_scope:org.apache.hertzbeat.common.entity.message.MetricsData)
        private static final org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData DEFAULT_INSTANCE;

        static {
            DEFAULT_INSTANCE = new org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData();
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        private static final com.google.protobuf.Parser<MetricsData>
                PARSER = new com.google.protobuf.AbstractParser<MetricsData>() {
            @java.lang.Override
            public MetricsData parsePartialFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                return new MetricsData(input, extensionRegistry);
            }
        };

        public static com.google.protobuf.Parser<MetricsData> parser() {
            return PARSER;
        }

        @java.lang.Override
        public com.google.protobuf.Parser<MetricsData> getParserForType() {
            return PARSER;
        }

        @java.lang.Override
        public org.apache.hertzbeat.common.entity.message.CollectRep.MetricsData getDefaultInstanceForType() {
            return DEFAULT_INSTANCE;
        }

    }

    public interface FieldOrBuilder extends
            // @@protoc_insertion_point(interface_extends:org.apache.hertzbeat.common.entity.message.Field)
            com.google.protobuf.MessageOrBuilder {

        /**
         * <pre>
         * monitoring collect metric field name
         * </pre>
         *
         * <code>string name = 1;</code>
         *
         * @return The name.
         */
        java.lang.String getName();

        /**
         * <pre>
         * monitoring collect metric field name
         * </pre>
         *
         * <code>string name = 1;</code>
         *
         * @return The bytes for name.
         */
        com.google.protobuf.ByteString
        getNameBytes();

        /**
         * <pre>
         * monitoring collect metrics field type, 0-number 1-string
         * </pre>
         *
         * <code>uint32 type = 2;</code>
         *
         * @return The type.
         */
        int getType();

        /**
         * <pre>
         * monitoring collect metrics field unit, % MB GB TB S...
         * </pre>
         *
         * <code>string unit = 3;</code>
         *
         * @return The unit.
         */
        java.lang.String getUnit();

        /**
         * <pre>
         * monitoring collect metrics field unit, % MB GB TB S...
         * </pre>
         *
         * <code>string unit = 3;</code>
         *
         * @return The bytes for unit.
         */
        com.google.protobuf.ByteString
        getUnitBytes();

        /**
         * <pre>
         * is label field
         * </pre>
         *
         * <code>bool label = 4;</code>
         *
         * @return The label.
         */
        boolean getLabel();
    }

    /**
     * Protobuf type {@code org.apache.hertzbeat.common.entity.message.Field}
     */
    public static final class Field extends
            com.google.protobuf.GeneratedMessageV3 implements
            // @@protoc_insertion_point(message_implements:org.apache.hertzbeat.common.entity.message.Field)
            FieldOrBuilder {
        private static final long serialVersionUID = 0L;

        // Use Field.newBuilder() to construct.
        private Field(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
            super(builder);
        }

        private Field() {
            name_ = "";
            unit_ = "";
        }

        @java.lang.Override
        @SuppressWarnings({"unused"})
        protected java.lang.Object newInstance(
                UnusedPrivateParameter unused) {
            return new Field();
        }

        @java.lang.Override
        public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
            return this.unknownFields;
        }

        private Field(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            this();
            if (extensionRegistry == null) {
                throw new java.lang.NullPointerException();
            }
            com.google.protobuf.UnknownFieldSet.Builder unknownFields =
                    com.google.protobuf.UnknownFieldSet.newBuilder();
            try {
                boolean done = false;
                while (!done) {
                    int tag = input.readTag();
                    switch (tag) {
                        case 0:
                            done = true;
                            break;
                        case 10: {
                            java.lang.String s = input.readStringRequireUtf8();

                            name_ = s;
                            break;
                        }
                        case 16: {

                            type_ = input.readUInt32();
                            break;
                        }
                        case 26: {
                            java.lang.String s = input.readStringRequireUtf8();

                            unit_ = s;
                            break;
                        }
                        case 32: {

                            label_ = input.readBool();
                            break;
                        }
                        default: {
                            if (!parseUnknownField(
                                    input, unknownFields, extensionRegistry, tag)) {
                                done = true;
                            }
                            break;
                        }
                    }
                }
            } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                throw e.setUnfinishedMessage(this);
            } catch (com.google.protobuf.UninitializedMessageException e) {
                throw e.asInvalidProtocolBufferException().setUnfinishedMessage(this);
            } catch (java.io.IOException e) {
                throw new com.google.protobuf.InvalidProtocolBufferException(
                        e).setUnfinishedMessage(this);
            } finally {
                this.unknownFields = unknownFields.build();
                makeExtensionsImmutable();
            }
        }

        public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
            return org.apache.hertzbeat.common.entity.message.CollectRep.internal_static_org_apache_hertzbeat_common_entity_message_Field_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
            return org.apache.hertzbeat.common.entity.message.CollectRep.internal_static_org_apache_hertzbeat_common_entity_message_Field_fieldAccessorTable
                    .ensureFieldAccessorsInitialized(
                            org.apache.hertzbeat.common.entity.message.CollectRep.Field.class, org.apache.hertzbeat.common.entity.message.CollectRep.Field.Builder.class);
        }

        public static final int NAME_FIELD_NUMBER = 1;
        private volatile java.lang.Object name_;

        /**
         * <pre>
         * monitoring collect metric field name
         * </pre>
         *
         * <code>string name = 1;</code>
         *
         * @return The name.
         */
        @java.lang.Override
        public java.lang.String getName() {
            java.lang.Object ref = name_;
            if (ref instanceof java.lang.String) {
                return (java.lang.String) ref;
            } else {
                com.google.protobuf.ByteString bs =
                        (com.google.protobuf.ByteString) ref;
                java.lang.String s = bs.toStringUtf8();
                name_ = s;
                return s;
            }
        }

        /**
         * <pre>
         * monitoring collect metric field name
         * </pre>
         *
         * <code>string name = 1;</code>
         *
         * @return The bytes for name.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getNameBytes() {
            java.lang.Object ref = name_;
            if (ref instanceof java.lang.String) {
                com.google.protobuf.ByteString b =
                        com.google.protobuf.ByteString.copyFromUtf8(
                                (java.lang.String) ref);
                name_ = b;
                return b;
            } else {
                return (com.google.protobuf.ByteString) ref;
            }
        }

        public static final int TYPE_FIELD_NUMBER = 2;
        private int type_;

        /**
         * <pre>
         * monitoring collect metrics field type, 0-number 1-string
         * </pre>
         *
         * <code>uint32 type = 2;</code>
         *
         * @return The type.
         */
        @java.lang.Override
        public int getType() {
            return type_;
        }

        public static final int UNIT_FIELD_NUMBER = 3;
        private volatile java.lang.Object unit_;

        /**
         * <pre>
         * monitoring collect metrics field unit, % MB GB TB S...
         * </pre>
         *
         * <code>string unit = 3;</code>
         *
         * @return The unit.
         */
        @java.lang.Override
        public java.lang.String getUnit() {
            java.lang.Object ref = unit_;
            if (ref instanceof java.lang.String) {
                return (java.lang.String) ref;
            } else {
                com.google.protobuf.ByteString bs =
                        (com.google.protobuf.ByteString) ref;
                java.lang.String s = bs.toStringUtf8();
                unit_ = s;
                return s;
            }
        }

        /**
         * <pre>
         * monitoring collect metrics field unit, % MB GB TB S...
         * </pre>
         *
         * <code>string unit = 3;</code>
         *
         * @return The bytes for unit.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getUnitBytes() {
            java.lang.Object ref = unit_;
            if (ref instanceof java.lang.String) {
                com.google.protobuf.ByteString b =
                        com.google.protobuf.ByteString.copyFromUtf8(
                                (java.lang.String) ref);
                unit_ = b;
                return b;
            } else {
                return (com.google.protobuf.ByteString) ref;
            }
        }

        public static final int LABEL_FIELD_NUMBER = 4;
        private boolean label_;

        /**
         * <pre>
         * is label field
         * </pre>
         *
         * <code>bool label = 4;</code>
         *
         * @return The label.
         */
        @java.lang.Override
        public boolean getLabel() {
            return label_;
        }

        private byte memoizedIsInitialized = -1;

        @java.lang.Override
        public final boolean isInitialized() {
            byte isInitialized = memoizedIsInitialized;
            if (isInitialized == 1) return true;
            if (isInitialized == 0) return false;

            memoizedIsInitialized = 1;
            return true;
        }

        @java.lang.Override
        public void writeTo(com.google.protobuf.CodedOutputStream output)
                throws java.io.IOException {
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
                com.google.protobuf.GeneratedMessageV3.writeString(output, 1, name_);
            }
            if (type_ != 0) {
                output.writeUInt32(2, type_);
            }
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(unit_)) {
                com.google.protobuf.GeneratedMessageV3.writeString(output, 3, unit_);
            }
            if (label_ != false) {
                output.writeBool(4, label_);
            }
            unknownFields.writeTo(output);
        }

        @java.lang.Override
        public int getSerializedSize() {
            int size = memoizedSize;
            if (size != -1) return size;

            size = 0;
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
                size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, name_);
            }
            if (type_ != 0) {
                size += com.google.protobuf.CodedOutputStream
                        .computeUInt32Size(2, type_);
            }
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(unit_)) {
                size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, unit_);
            }
            if (label_ != false) {
                size += com.google.protobuf.CodedOutputStream
                        .computeBoolSize(4, label_);
            }
            size += unknownFields.getSerializedSize();
            memoizedSize = size;
            return size;
        }

        @java.lang.Override
        public boolean equals(final java.lang.Object obj) {
            if (obj == this) {
                return true;
            }
            if (!(obj instanceof org.apache.hertzbeat.common.entity.message.CollectRep.Field)) {
                return super.equals(obj);
            }
            org.apache.hertzbeat.common.entity.message.CollectRep.Field other = (org.apache.hertzbeat.common.entity.message.CollectRep.Field) obj;

            if (!getName()
                    .equals(other.getName())) return false;
            if (getType()
                    != other.getType()) return false;
            if (!getUnit()
                    .equals(other.getUnit())) return false;
            if (getLabel()
                    != other.getLabel()) return false;
            if (!unknownFields.equals(other.unknownFields)) return false;
            return true;
        }

        @java.lang.Override
        public int hashCode() {
            if (memoizedHashCode != 0) {
                return memoizedHashCode;
            }
            int hash = 41;
            hash = (19 * hash) + getDescriptor().hashCode();
            hash = (37 * hash) + NAME_FIELD_NUMBER;
            hash = (53 * hash) + getName().hashCode();
            hash = (37 * hash) + TYPE_FIELD_NUMBER;
            hash = (53 * hash) + getType();
            hash = (37 * hash) + UNIT_FIELD_NUMBER;
            hash = (53 * hash) + getUnit().hashCode();
            hash = (37 * hash) + LABEL_FIELD_NUMBER;
            hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
                    getLabel());
            hash = (29 * hash) + unknownFields.hashCode();
            memoizedHashCode = hash;
            return hash;
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.Field parseFrom(
                java.nio.ByteBuffer data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.Field parseFrom(
                java.nio.ByteBuffer data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.Field parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.Field parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.Field parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.Field parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.Field parseFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.Field parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.Field parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.Field parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.Field parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.Field parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        @java.lang.Override
        public Builder newBuilderForType() {
            return newBuilder();
        }

        public static Builder newBuilder() {
            return DEFAULT_INSTANCE.toBuilder();
        }

        public static Builder newBuilder(org.apache.hertzbeat.common.entity.message.CollectRep.Field prototype) {
            return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }

        @java.lang.Override
        public Builder toBuilder() {
            return this == DEFAULT_INSTANCE
                    ? new Builder() : new Builder().mergeFrom(this);
        }

        @java.lang.Override
        protected Builder newBuilderForType(
                com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
            Builder builder = new Builder(parent);
            return builder;
        }

        /**
         * Protobuf type {@code org.apache.hertzbeat.common.entity.message.Field}
         */
        public static final class Builder extends
                com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
                // @@protoc_insertion_point(builder_implements:org.apache.hertzbeat.common.entity.message.Field)
                org.apache.hertzbeat.common.entity.message.CollectRep.FieldOrBuilder {
            public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
                return org.apache.hertzbeat.common.entity.message.CollectRep.internal_static_org_apache_hertzbeat_common_entity_message_Field_descriptor;
            }

            @java.lang.Override
            protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
                return org.apache.hertzbeat.common.entity.message.CollectRep.internal_static_org_apache_hertzbeat_common_entity_message_Field_fieldAccessorTable
                        .ensureFieldAccessorsInitialized(
                                org.apache.hertzbeat.common.entity.message.CollectRep.Field.class, org.apache.hertzbeat.common.entity.message.CollectRep.Field.Builder.class);
            }

            // Construct using org.apache.hertzbeat.common.entity.message.CollectRep.Field.newBuilder()
            private Builder() {
                maybeForceBuilderInitialization();
            }

            private Builder(
                    com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
                super(parent);
                maybeForceBuilderInitialization();
            }

            private void maybeForceBuilderInitialization() {
                if (com.google.protobuf.GeneratedMessageV3
                        .alwaysUseFieldBuilders) {
                }
            }

            @java.lang.Override
            public Builder clear() {
                super.clear();
                name_ = "";

                type_ = 0;

                unit_ = "";

                label_ = false;

                return this;
            }

            @java.lang.Override
            public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
                return org.apache.hertzbeat.common.entity.message.CollectRep.internal_static_org_apache_hertzbeat_common_entity_message_Field_descriptor;
            }

            @java.lang.Override
            public org.apache.hertzbeat.common.entity.message.CollectRep.Field getDefaultInstanceForType() {
                return org.apache.hertzbeat.common.entity.message.CollectRep.Field.getDefaultInstance();
            }

            @java.lang.Override
            public org.apache.hertzbeat.common.entity.message.CollectRep.Field build() {
                org.apache.hertzbeat.common.entity.message.CollectRep.Field result = buildPartial();
                if (!result.isInitialized()) {
                    throw newUninitializedMessageException(result);
                }
                return result;
            }

            @java.lang.Override
            public org.apache.hertzbeat.common.entity.message.CollectRep.Field buildPartial() {
                org.apache.hertzbeat.common.entity.message.CollectRep.Field result = new org.apache.hertzbeat.common.entity.message.CollectRep.Field(this);
                result.name_ = name_;
                result.type_ = type_;
                result.unit_ = unit_;
                result.label_ = label_;
                onBuilt();
                return result;
            }

            @java.lang.Override
            public Builder clone() {
                return super.clone();
            }

            @java.lang.Override
            public Builder setField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.setField(field, value);
            }

            @java.lang.Override
            public Builder clearField(
                    com.google.protobuf.Descriptors.FieldDescriptor field) {
                return super.clearField(field);
            }

            @java.lang.Override
            public Builder clearOneof(
                    com.google.protobuf.Descriptors.OneofDescriptor oneof) {
                return super.clearOneof(oneof);
            }

            @java.lang.Override
            public Builder setRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    int index, java.lang.Object value) {
                return super.setRepeatedField(field, index, value);
            }

            @java.lang.Override
            public Builder addRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.addRepeatedField(field, value);
            }

            @java.lang.Override
            public Builder mergeFrom(com.google.protobuf.Message other) {
                if (other instanceof org.apache.hertzbeat.common.entity.message.CollectRep.Field) {
                    return mergeFrom((org.apache.hertzbeat.common.entity.message.CollectRep.Field) other);
                } else {
                    super.mergeFrom(other);
                    return this;
                }
            }

            public Builder mergeFrom(org.apache.hertzbeat.common.entity.message.CollectRep.Field other) {
                if (other == org.apache.hertzbeat.common.entity.message.CollectRep.Field.getDefaultInstance())
                    return this;
                if (!other.getName().isEmpty()) {
                    name_ = other.name_;
                    onChanged();
                }
                if (other.getType() != 0) {
                    setType(other.getType());
                }
                if (!other.getUnit().isEmpty()) {
                    unit_ = other.unit_;
                    onChanged();
                }
                if (other.getLabel() != false) {
                    setLabel(other.getLabel());
                }
                this.mergeUnknownFields(other.unknownFields);
                onChanged();
                return this;
            }

            @java.lang.Override
            public final boolean isInitialized() {
                return true;
            }

            @java.lang.Override
            public Builder mergeFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws java.io.IOException {
                org.apache.hertzbeat.common.entity.message.CollectRep.Field parsedMessage = null;
                try {
                    parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    parsedMessage = (org.apache.hertzbeat.common.entity.message.CollectRep.Field) e.getUnfinishedMessage();
                    throw e.unwrapIOException();
                } finally {
                    if (parsedMessage != null) {
                        mergeFrom(parsedMessage);
                    }
                }
                return this;
            }

            private java.lang.Object name_ = "";

            /**
             * <pre>
             * monitoring collect metric field name
             * </pre>
             *
             * <code>string name = 1;</code>
             *
             * @return The name.
             */
            public java.lang.String getName() {
                java.lang.Object ref = name_;
                if (!(ref instanceof java.lang.String)) {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    java.lang.String s = bs.toStringUtf8();
                    name_ = s;
                    return s;
                } else {
                    return (java.lang.String) ref;
                }
            }

            /**
             * <pre>
             * monitoring collect metric field name
             * </pre>
             *
             * <code>string name = 1;</code>
             *
             * @return The bytes for name.
             */
            public com.google.protobuf.ByteString
            getNameBytes() {
                java.lang.Object ref = name_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (java.lang.String) ref);
                    name_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            /**
             * <pre>
             * monitoring collect metric field name
             * </pre>
             *
             * <code>string name = 1;</code>
             *
             * @param value The name to set.
             * @return This builder for chaining.
             */
            public Builder setName(
                    java.lang.String value) {
                if (value == null) {
                    throw new NullPointerException();
                }

                name_ = value;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * monitoring collect metric field name
             * </pre>
             *
             * <code>string name = 1;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearName() {

                name_ = getDefaultInstance().getName();
                onChanged();
                return this;
            }

            /**
             * <pre>
             * monitoring collect metric field name
             * </pre>
             *
             * <code>string name = 1;</code>
             *
             * @param value The bytes for name to set.
             * @return This builder for chaining.
             */
            public Builder setNameBytes(
                    com.google.protobuf.ByteString value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                checkByteStringIsUtf8(value);

                name_ = value;
                onChanged();
                return this;
            }

            private int type_;

            /**
             * <pre>
             * monitoring collect metrics field type, 0-number 1-string
             * </pre>
             *
             * <code>uint32 type = 2;</code>
             *
             * @return The type.
             */
            @java.lang.Override
            public int getType() {
                return type_;
            }

            /**
             * <pre>
             * monitoring collect metrics field type, 0-number 1-string
             * </pre>
             *
             * <code>uint32 type = 2;</code>
             *
             * @param value The type to set.
             * @return This builder for chaining.
             */
            public Builder setType(int value) {

                type_ = value;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * monitoring collect metrics field type, 0-number 1-string
             * </pre>
             *
             * <code>uint32 type = 2;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearType() {

                type_ = 0;
                onChanged();
                return this;
            }

            private java.lang.Object unit_ = "";

            /**
             * <pre>
             * monitoring collect metrics field unit, % MB GB TB S...
             * </pre>
             *
             * <code>string unit = 3;</code>
             *
             * @return The unit.
             */
            public java.lang.String getUnit() {
                java.lang.Object ref = unit_;
                if (!(ref instanceof java.lang.String)) {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    java.lang.String s = bs.toStringUtf8();
                    unit_ = s;
                    return s;
                } else {
                    return (java.lang.String) ref;
                }
            }

            /**
             * <pre>
             * monitoring collect metrics field unit, % MB GB TB S...
             * </pre>
             *
             * <code>string unit = 3;</code>
             *
             * @return The bytes for unit.
             */
            public com.google.protobuf.ByteString
            getUnitBytes() {
                java.lang.Object ref = unit_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (java.lang.String) ref);
                    unit_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            /**
             * <pre>
             * monitoring collect metrics field unit, % MB GB TB S...
             * </pre>
             *
             * <code>string unit = 3;</code>
             *
             * @param value The unit to set.
             * @return This builder for chaining.
             */
            public Builder setUnit(
                    java.lang.String value) {
                if (value == null) {
                    throw new NullPointerException();
                }

                unit_ = value;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * monitoring collect metrics field unit, % MB GB TB S...
             * </pre>
             *
             * <code>string unit = 3;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearUnit() {

                unit_ = getDefaultInstance().getUnit();
                onChanged();
                return this;
            }

            /**
             * <pre>
             * monitoring collect metrics field unit, % MB GB TB S...
             * </pre>
             *
             * <code>string unit = 3;</code>
             *
             * @param value The bytes for unit to set.
             * @return This builder for chaining.
             */
            public Builder setUnitBytes(
                    com.google.protobuf.ByteString value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                checkByteStringIsUtf8(value);

                unit_ = value;
                onChanged();
                return this;
            }

            private boolean label_;

            /**
             * <pre>
             * is label field
             * </pre>
             *
             * <code>bool label = 4;</code>
             *
             * @return The label.
             */
            @java.lang.Override
            public boolean getLabel() {
                return label_;
            }

            /**
             * <pre>
             * is label field
             * </pre>
             *
             * <code>bool label = 4;</code>
             *
             * @param value The label to set.
             * @return This builder for chaining.
             */
            public Builder setLabel(boolean value) {

                label_ = value;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * is label field
             * </pre>
             *
             * <code>bool label = 4;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearLabel() {

                label_ = false;
                onChanged();
                return this;
            }

            @java.lang.Override
            public final Builder setUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.setUnknownFields(unknownFields);
            }

            @java.lang.Override
            public final Builder mergeUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.mergeUnknownFields(unknownFields);
            }


            // @@protoc_insertion_point(builder_scope:org.apache.hertzbeat.common.entity.message.Field)
        }

        // @@protoc_insertion_point(class_scope:org.apache.hertzbeat.common.entity.message.Field)
        private static final org.apache.hertzbeat.common.entity.message.CollectRep.Field DEFAULT_INSTANCE;

        static {
            DEFAULT_INSTANCE = new org.apache.hertzbeat.common.entity.message.CollectRep.Field();
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.Field getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        private static final com.google.protobuf.Parser<Field>
                PARSER = new com.google.protobuf.AbstractParser<Field>() {
            @java.lang.Override
            public Field parsePartialFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                return new Field(input, extensionRegistry);
            }
        };

        public static com.google.protobuf.Parser<Field> parser() {
            return PARSER;
        }

        @java.lang.Override
        public com.google.protobuf.Parser<Field> getParserForType() {
            return PARSER;
        }

        @java.lang.Override
        public org.apache.hertzbeat.common.entity.message.CollectRep.Field getDefaultInstanceForType() {
            return DEFAULT_INSTANCE;
        }

    }

    public interface ValueRowOrBuilder extends
            // @@protoc_insertion_point(interface_extends:org.apache.hertzbeat.common.entity.message.ValueRow)
            com.google.protobuf.MessageOrBuilder {

        /**
         * <pre>
         * monitoring collect metrics value, mapping with the fields
         * </pre>
         *
         * <code>repeated string columns = 1;</code>
         *
         * @return A list containing the columns.
         */
        java.util.List<java.lang.String>
        getColumnsList();

        /**
         * <pre>
         * monitoring collect metrics value, mapping with the fields
         * </pre>
         *
         * <code>repeated string columns = 1;</code>
         *
         * @return The count of columns.
         */
        int getColumnsCount();

        /**
         * <pre>
         * monitoring collect metrics value, mapping with the fields
         * </pre>
         *
         * <code>repeated string columns = 1;</code>
         *
         * @param index The index of the element to return.
         * @return The columns at the given index.
         */
        java.lang.String getColumns(int index);

        /**
         * <pre>
         * monitoring collect metrics value, mapping with the fields
         * </pre>
         *
         * <code>repeated string columns = 1;</code>
         *
         * @param index The index of the value to return.
         * @return The bytes of the columns at the given index.
         */
        com.google.protobuf.ByteString
        getColumnsBytes(int index);
    }

    /**
     * Protobuf type {@code org.apache.hertzbeat.common.entity.message.ValueRow}
     */
    public static final class ValueRow extends
            com.google.protobuf.GeneratedMessageV3 implements
            // @@protoc_insertion_point(message_implements:org.apache.hertzbeat.common.entity.message.ValueRow)
            ValueRowOrBuilder {
        private static final long serialVersionUID = 0L;

        // Use ValueRow.newBuilder() to construct.
        private ValueRow(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
            super(builder);
        }

        private ValueRow() {
            columns_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        }

        @java.lang.Override
        @SuppressWarnings({"unused"})
        protected java.lang.Object newInstance(
                UnusedPrivateParameter unused) {
            return new ValueRow();
        }

        @java.lang.Override
        public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
            return this.unknownFields;
        }

        private ValueRow(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            this();
            if (extensionRegistry == null) {
                throw new java.lang.NullPointerException();
            }
            int mutable_bitField0_ = 0;
            com.google.protobuf.UnknownFieldSet.Builder unknownFields =
                    com.google.protobuf.UnknownFieldSet.newBuilder();
            try {
                boolean done = false;
                while (!done) {
                    int tag = input.readTag();
                    switch (tag) {
                        case 0:
                            done = true;
                            break;
                        case 10: {
                            java.lang.String s = input.readStringRequireUtf8();
                            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                                columns_ = new com.google.protobuf.LazyStringArrayList();
                                mutable_bitField0_ |= 0x00000001;
                            }
                            columns_.add(s);
                            break;
                        }
                        default: {
                            if (!parseUnknownField(
                                    input, unknownFields, extensionRegistry, tag)) {
                                done = true;
                            }
                            break;
                        }
                    }
                }
            } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                throw e.setUnfinishedMessage(this);
            } catch (com.google.protobuf.UninitializedMessageException e) {
                throw e.asInvalidProtocolBufferException().setUnfinishedMessage(this);
            } catch (java.io.IOException e) {
                throw new com.google.protobuf.InvalidProtocolBufferException(
                        e).setUnfinishedMessage(this);
            } finally {
                if (((mutable_bitField0_ & 0x00000001) != 0)) {
                    columns_ = columns_.getUnmodifiableView();
                }
                this.unknownFields = unknownFields.build();
                makeExtensionsImmutable();
            }
        }

        public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
            return org.apache.hertzbeat.common.entity.message.CollectRep.internal_static_org_apache_hertzbeat_common_entity_message_ValueRow_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
            return org.apache.hertzbeat.common.entity.message.CollectRep.internal_static_org_apache_hertzbeat_common_entity_message_ValueRow_fieldAccessorTable
                    .ensureFieldAccessorsInitialized(
                            org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow.class, org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow.Builder.class);
        }

        public static final int COLUMNS_FIELD_NUMBER = 1;
        private com.google.protobuf.LazyStringList columns_;

        /**
         * <pre>
         * monitoring collect metrics value, mapping with the fields
         * </pre>
         *
         * <code>repeated string columns = 1;</code>
         *
         * @return A list containing the columns.
         */
        public com.google.protobuf.ProtocolStringList
        getColumnsList() {
            return columns_;
        }

        /**
         * <pre>
         * monitoring collect metrics value, mapping with the fields
         * </pre>
         *
         * <code>repeated string columns = 1;</code>
         *
         * @return The count of columns.
         */
        public int getColumnsCount() {
            return columns_.size();
        }

        /**
         * <pre>
         * monitoring collect metrics value, mapping with the fields
         * </pre>
         *
         * <code>repeated string columns = 1;</code>
         *
         * @param index The index of the element to return.
         * @return The columns at the given index.
         */
        public java.lang.String getColumns(int index) {
            return columns_.get(index);
        }

        /**
         * <pre>
         * monitoring collect metrics value, mapping with the fields
         * </pre>
         *
         * <code>repeated string columns = 1;</code>
         *
         * @param index The index of the value to return.
         * @return The bytes of the columns at the given index.
         */
        public com.google.protobuf.ByteString
        getColumnsBytes(int index) {
            return columns_.getByteString(index);
        }

        private byte memoizedIsInitialized = -1;

        @java.lang.Override
        public final boolean isInitialized() {
            byte isInitialized = memoizedIsInitialized;
            if (isInitialized == 1) return true;
            if (isInitialized == 0) return false;

            memoizedIsInitialized = 1;
            return true;
        }

        @java.lang.Override
        public void writeTo(com.google.protobuf.CodedOutputStream output)
                throws java.io.IOException {
            for (int i = 0; i < columns_.size(); i++) {
                com.google.protobuf.GeneratedMessageV3.writeString(output, 1, columns_.getRaw(i));
            }
            unknownFields.writeTo(output);
        }

        @java.lang.Override
        public int getSerializedSize() {
            int size = memoizedSize;
            if (size != -1) return size;

            size = 0;
            {
                int dataSize = 0;
                for (int i = 0; i < columns_.size(); i++) {
                    dataSize += computeStringSizeNoTag(columns_.getRaw(i));
                }
                size += dataSize;
                size += 1 * getColumnsList().size();
            }
            size += unknownFields.getSerializedSize();
            memoizedSize = size;
            return size;
        }

        @java.lang.Override
        public boolean equals(final java.lang.Object obj) {
            if (obj == this) {
                return true;
            }
            if (!(obj instanceof org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow)) {
                return super.equals(obj);
            }
            org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow other = (org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow) obj;

            if (!getColumnsList()
                    .equals(other.getColumnsList())) return false;
            if (!unknownFields.equals(other.unknownFields)) return false;
            return true;
        }

        @java.lang.Override
        public int hashCode() {
            if (memoizedHashCode != 0) {
                return memoizedHashCode;
            }
            int hash = 41;
            hash = (19 * hash) + getDescriptor().hashCode();
            if (getColumnsCount() > 0) {
                hash = (37 * hash) + COLUMNS_FIELD_NUMBER;
                hash = (53 * hash) + getColumnsList().hashCode();
            }
            hash = (29 * hash) + unknownFields.hashCode();
            memoizedHashCode = hash;
            return hash;
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow parseFrom(
                java.nio.ByteBuffer data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow parseFrom(
                java.nio.ByteBuffer data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow parseFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        @java.lang.Override
        public Builder newBuilderForType() {
            return newBuilder();
        }

        public static Builder newBuilder() {
            return DEFAULT_INSTANCE.toBuilder();
        }

        public static Builder newBuilder(org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow prototype) {
            return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }

        @java.lang.Override
        public Builder toBuilder() {
            return this == DEFAULT_INSTANCE
                    ? new Builder() : new Builder().mergeFrom(this);
        }

        @java.lang.Override
        protected Builder newBuilderForType(
                com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
            Builder builder = new Builder(parent);
            return builder;
        }

        /**
         * Protobuf type {@code org.apache.hertzbeat.common.entity.message.ValueRow}
         */
        public static final class Builder extends
                com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
                // @@protoc_insertion_point(builder_implements:org.apache.hertzbeat.common.entity.message.ValueRow)
                org.apache.hertzbeat.common.entity.message.CollectRep.ValueRowOrBuilder {
            public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
                return org.apache.hertzbeat.common.entity.message.CollectRep.internal_static_org_apache_hertzbeat_common_entity_message_ValueRow_descriptor;
            }

            @java.lang.Override
            protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
                return org.apache.hertzbeat.common.entity.message.CollectRep.internal_static_org_apache_hertzbeat_common_entity_message_ValueRow_fieldAccessorTable
                        .ensureFieldAccessorsInitialized(
                                org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow.class, org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow.Builder.class);
            }

            // Construct using org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow.newBuilder()
            private Builder() {
                maybeForceBuilderInitialization();
            }

            private Builder(
                    com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
                super(parent);
                maybeForceBuilderInitialization();
            }

            private void maybeForceBuilderInitialization() {
                if (com.google.protobuf.GeneratedMessageV3
                        .alwaysUseFieldBuilders) {
                }
            }

            @java.lang.Override
            public Builder clear() {
                super.clear();
                columns_ = com.google.protobuf.LazyStringArrayList.EMPTY;
                bitField0_ = (bitField0_ & ~0x00000001);
                return this;
            }

            @java.lang.Override
            public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
                return org.apache.hertzbeat.common.entity.message.CollectRep.internal_static_org_apache_hertzbeat_common_entity_message_ValueRow_descriptor;
            }

            @java.lang.Override
            public org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow getDefaultInstanceForType() {
                return org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow.getDefaultInstance();
            }

            @java.lang.Override
            public org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow build() {
                org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow result = buildPartial();
                if (!result.isInitialized()) {
                    throw newUninitializedMessageException(result);
                }
                return result;
            }

            @java.lang.Override
            public org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow buildPartial() {
                org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow result = new org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow(this);
                int from_bitField0_ = bitField0_;
                if (((bitField0_ & 0x00000001) != 0)) {
                    columns_ = columns_.getUnmodifiableView();
                    bitField0_ = (bitField0_ & ~0x00000001);
                }
                result.columns_ = columns_;
                onBuilt();
                return result;
            }

            @java.lang.Override
            public Builder clone() {
                return super.clone();
            }

            @java.lang.Override
            public Builder setField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.setField(field, value);
            }

            @java.lang.Override
            public Builder clearField(
                    com.google.protobuf.Descriptors.FieldDescriptor field) {
                return super.clearField(field);
            }

            @java.lang.Override
            public Builder clearOneof(
                    com.google.protobuf.Descriptors.OneofDescriptor oneof) {
                return super.clearOneof(oneof);
            }

            @java.lang.Override
            public Builder setRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    int index, java.lang.Object value) {
                return super.setRepeatedField(field, index, value);
            }

            @java.lang.Override
            public Builder addRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.addRepeatedField(field, value);
            }

            @java.lang.Override
            public Builder mergeFrom(com.google.protobuf.Message other) {
                if (other instanceof org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow) {
                    return mergeFrom((org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow) other);
                } else {
                    super.mergeFrom(other);
                    return this;
                }
            }

            public Builder mergeFrom(org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow other) {
                if (other == org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow.getDefaultInstance())
                    return this;
                if (!other.columns_.isEmpty()) {
                    if (columns_.isEmpty()) {
                        columns_ = other.columns_;
                        bitField0_ = (bitField0_ & ~0x00000001);
                    } else {
                        ensureColumnsIsMutable();
                        columns_.addAll(other.columns_);
                    }
                    onChanged();
                }
                this.mergeUnknownFields(other.unknownFields);
                onChanged();
                return this;
            }

            @java.lang.Override
            public final boolean isInitialized() {
                return true;
            }

            @java.lang.Override
            public Builder mergeFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws java.io.IOException {
                org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow parsedMessage = null;
                try {
                    parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    parsedMessage = (org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow) e.getUnfinishedMessage();
                    throw e.unwrapIOException();
                } finally {
                    if (parsedMessage != null) {
                        mergeFrom(parsedMessage);
                    }
                }
                return this;
            }

            private int bitField0_;

            private com.google.protobuf.LazyStringList columns_ = com.google.protobuf.LazyStringArrayList.EMPTY;

            private void ensureColumnsIsMutable() {
                if (!((bitField0_ & 0x00000001) != 0)) {
                    columns_ = new com.google.protobuf.LazyStringArrayList(columns_);
                    bitField0_ |= 0x00000001;
                }
            }

            /**
             * <pre>
             * monitoring collect metrics value, mapping with the fields
             * </pre>
             *
             * <code>repeated string columns = 1;</code>
             *
             * @return A list containing the columns.
             */
            public com.google.protobuf.ProtocolStringList
            getColumnsList() {
                return columns_.getUnmodifiableView();
            }

            /**
             * <pre>
             * monitoring collect metrics value, mapping with the fields
             * </pre>
             *
             * <code>repeated string columns = 1;</code>
             *
             * @return The count of columns.
             */
            public int getColumnsCount() {
                return columns_.size();
            }

            /**
             * <pre>
             * monitoring collect metrics value, mapping with the fields
             * </pre>
             *
             * <code>repeated string columns = 1;</code>
             *
             * @param index The index of the element to return.
             * @return The columns at the given index.
             */
            public java.lang.String getColumns(int index) {
                return columns_.get(index);
            }

            /**
             * <pre>
             * monitoring collect metrics value, mapping with the fields
             * </pre>
             *
             * <code>repeated string columns = 1;</code>
             *
             * @param index The index of the value to return.
             * @return The bytes of the columns at the given index.
             */
            public com.google.protobuf.ByteString
            getColumnsBytes(int index) {
                return columns_.getByteString(index);
            }

            /**
             * <pre>
             * monitoring collect metrics value, mapping with the fields
             * </pre>
             *
             * <code>repeated string columns = 1;</code>
             *
             * @param index The index to set the value at.
             * @param value The columns to set.
             * @return This builder for chaining.
             */
            public Builder setColumns(
                    int index, java.lang.String value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                ensureColumnsIsMutable();
                columns_.set(index, value);
                onChanged();
                return this;
            }

            /**
             * <pre>
             * monitoring collect metrics value, mapping with the fields
             * </pre>
             *
             * <code>repeated string columns = 1;</code>
             *
             * @param value The columns to add.
             * @return This builder for chaining.
             */
            public Builder addColumns(
                    java.lang.String value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                ensureColumnsIsMutable();
                columns_.add(value);
                onChanged();
                return this;
            }

            /**
             * <pre>
             * monitoring collect metrics value, mapping with the fields
             * </pre>
             *
             * <code>repeated string columns = 1;</code>
             *
             * @param values The columns to add.
             * @return This builder for chaining.
             */
            public Builder addAllColumns(
                    java.lang.Iterable<java.lang.String> values) {
                ensureColumnsIsMutable();
                com.google.protobuf.AbstractMessageLite.Builder.addAll(
                        values, columns_);
                onChanged();
                return this;
            }

            /**
             * <pre>
             * monitoring collect metrics value, mapping with the fields
             * </pre>
             *
             * <code>repeated string columns = 1;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearColumns() {
                columns_ = com.google.protobuf.LazyStringArrayList.EMPTY;
                bitField0_ = (bitField0_ & ~0x00000001);
                onChanged();
                return this;
            }

            /**
             * <pre>
             * monitoring collect metrics value, mapping with the fields
             * </pre>
             *
             * <code>repeated string columns = 1;</code>
             *
             * @param value The bytes of the columns to add.
             * @return This builder for chaining.
             */
            public Builder addColumnsBytes(
                    com.google.protobuf.ByteString value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                checkByteStringIsUtf8(value);
                ensureColumnsIsMutable();
                columns_.add(value);
                onChanged();
                return this;
            }

            @java.lang.Override
            public final Builder setUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.setUnknownFields(unknownFields);
            }

            @java.lang.Override
            public final Builder mergeUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.mergeUnknownFields(unknownFields);
            }


            // @@protoc_insertion_point(builder_scope:org.apache.hertzbeat.common.entity.message.ValueRow)
        }

        // @@protoc_insertion_point(class_scope:org.apache.hertzbeat.common.entity.message.ValueRow)
        private static final org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow DEFAULT_INSTANCE;

        static {
            DEFAULT_INSTANCE = new org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow();
        }

        public static org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        private static final com.google.protobuf.Parser<ValueRow>
                PARSER = new com.google.protobuf.AbstractParser<ValueRow>() {
            @java.lang.Override
            public ValueRow parsePartialFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                return new ValueRow(input, extensionRegistry);
            }
        };

        public static com.google.protobuf.Parser<ValueRow> parser() {
            return PARSER;
        }

        @java.lang.Override
        public com.google.protobuf.Parser<ValueRow> getParserForType() {
            return PARSER;
        }

        @java.lang.Override
        public org.apache.hertzbeat.common.entity.message.CollectRep.ValueRow getDefaultInstanceForType() {
            return DEFAULT_INSTANCE;
        }

    }

    private static final com.google.protobuf.Descriptors.Descriptor
            internal_static_org_apache_hertzbeat_common_entity_message_MetricsData_descriptor;
    private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internal_static_org_apache_hertzbeat_common_entity_message_MetricsData_fieldAccessorTable;
    private static final com.google.protobuf.Descriptors.Descriptor
            internal_static_org_apache_hertzbeat_common_entity_message_Field_descriptor;
    private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internal_static_org_apache_hertzbeat_common_entity_message_Field_fieldAccessorTable;
    private static final com.google.protobuf.Descriptors.Descriptor
            internal_static_org_apache_hertzbeat_common_entity_message_ValueRow_descriptor;
    private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internal_static_org_apache_hertzbeat_common_entity_message_ValueRow_fieldAccessorTable;

    public static com.google.protobuf.Descriptors.FileDescriptor
    getDescriptor() {
        return descriptor;
    }

    private static com.google.protobuf.Descriptors.FileDescriptor
            descriptor;

    static {
        java.lang.String[] descriptorData = {
                "\n\021collect_rep.proto\022*org.apache.hertzbea" +
                        "t.common.entity.message\"\277\002\n\013MetricsData\022" +
                        "\n\n\002id\030\001 \001(\004\022\020\n\010tenantId\030\002 \001(\004\022\013\n\003app\030\003 \001" +
                        "(\t\022\017\n\007metrics\030\004 \001(\t\022\020\n\010priority\030\005 \001(\r\022\014\n" +
                        "\004time\030\006 \001(\004\022>\n\004code\030\007 \001(\01620.org.apache.h" +
                        "ertzbeat.common.entity.message.Code\022\013\n\003m" +
                        "sg\030\010 \001(\t\022A\n\006fields\030\t \003(\01321.org.apache.he" +
                        "rtzbeat.common.entity.message.Field\022D\n\006v" +
                        "alues\030\n \003(\01324.org.apache.hertzbeat.commo" +
                        "n.entity.message.ValueRow\"@\n\005Field\022\014\n\004na" +
                        "me\030\001 \001(\t\022\014\n\004type\030\002 \001(\r\022\014\n\004unit\030\003 \001(\t\022\r\n\005" +
                        "label\030\004 \001(\010\"\033\n\010ValueRow\022\017\n\007columns\030\001 \003(\t" +
                        "*b\n\004Code\022\013\n\007SUCCESS\020\000\022\020\n\014UN_AVAILABLE\020\001\022" +
                        "\020\n\014UN_REACHABLE\020\002\022\022\n\016UN_CONNECTABLE\020\003\022\010\n" +
                        "\004FAIL\020\004\022\013\n\007TIMEOUT\020\005b\006proto3"
        };
        descriptor = com.google.protobuf.Descriptors.FileDescriptor
                .internalBuildGeneratedFileFrom(descriptorData,
                        new com.google.protobuf.Descriptors.FileDescriptor[]{
                        });
        internal_static_org_apache_hertzbeat_common_entity_message_MetricsData_descriptor =
                getDescriptor().getMessageTypes().get(0);
        internal_static_org_apache_hertzbeat_common_entity_message_MetricsData_fieldAccessorTable = new
                com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
                internal_static_org_apache_hertzbeat_common_entity_message_MetricsData_descriptor,
                new java.lang.String[]{"Id", "TenantId", "App", "Metrics", "Priority", "Time", "Code", "Msg", "Fields", "Values",});
        internal_static_org_apache_hertzbeat_common_entity_message_Field_descriptor =
                getDescriptor().getMessageTypes().get(1);
        internal_static_org_apache_hertzbeat_common_entity_message_Field_fieldAccessorTable = new
                com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
                internal_static_org_apache_hertzbeat_common_entity_message_Field_descriptor,
                new java.lang.String[]{"Name", "Type", "Unit", "Label",});
        internal_static_org_apache_hertzbeat_common_entity_message_ValueRow_descriptor =
                getDescriptor().getMessageTypes().get(2);
        internal_static_org_apache_hertzbeat_common_entity_message_ValueRow_fieldAccessorTable = new
                com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
                internal_static_org_apache_hertzbeat_common_entity_message_ValueRow_descriptor,
                new java.lang.String[]{"Columns",});
    }

    // @@protoc_insertion_point(outer_class_scope)
}
