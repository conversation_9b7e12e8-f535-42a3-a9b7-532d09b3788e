package cn.iocoder.cloud.framework.warehouse.service;

import org.apache.hertzbeat.common.entity.message.CollectRep;

import java.util.List;

/**
 * service for warehouse
 *
 * <AUTHOR>
 */
public interface WarehouseService {

    /**
     * query monitor real time metrics data by monitor id
     * @param monitorId monitor id
     * @return metrics data
     */
    List<CollectRep.MetricsData> queryMonitorMetricsData(Long monitorId);
}
