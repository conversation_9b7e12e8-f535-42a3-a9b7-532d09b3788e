/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.warehouse.store.realtime.redis;

import cn.iocoder.cloud.framework.warehouse.store.realtime.AbstractRealTimeDataStorage;
import cn.iocoder.cloud.framework.warehouse.store.realtime.redis.client.RedisCommandDelegate;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Primary;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * redis storage collects real-time data
 */
@Primary
@Component
@ConditionalOnProperty(prefix = "warehouse.real-time.redis", name = "enabled", havingValue = "true")
@Slf4j
public class RedisDataStorage extends AbstractRealTimeDataStorage {

    private final RedisCommandDelegate redisCommandDelegate;

    public RedisDataStorage(RedisProperties redisProperties) {
        final RedisCommandDelegate delegate = RedisCommandDelegate.getInstance();
        this.serverAvailable = delegate.initRedisClient(redisProperties);
        this.redisCommandDelegate = delegate;
    }

    @Override
    public CollectRep.MetricsData getCurrentMetricsData(@NonNull Long monitorId, @NonNull String metric) {
        return redisCommandDelegate.operate().hget(String.valueOf(monitorId), metric);
    }

    public CollectRep.MetricsData getOldCurrentMetricsData(Long monitorId, String metric) {
        return redisCommandDelegate.operate().hget("old_" + String.valueOf(monitorId), metric);
    }


    @Override
    public List<CollectRep.MetricsData> getCurrentMetricsData(@NonNull Long monitorId) {
        Map<String, CollectRep.MetricsData> metricsDataMap = redisCommandDelegate.operate().hgetAll(String.valueOf(monitorId));
        return new ArrayList<>(metricsDataMap.values());
    }

    @Override
    public void saveData(CollectRep.MetricsData metricsData) {
        String key = String.valueOf(metricsData.getId());
        String hashKey = metricsData.getMetrics();
        if (metricsData.getCode() != CollectRep.Code.SUCCESS || !isServerAvailable()) {
            return;
        }

        if (metricsData.getCode() == CollectRep.Code.SUCCESS) {
            if (hashKey.equals("interface")) {
                CollectRep.MetricsData old = redisCommandDelegate.operate().hget(key, hashKey);
                if (old != null) {
                    redisCommandDelegate.operate().hset("old_" + key, hashKey, old, future -> future.thenAccept(response -> {
                        if (response) {
                            log.debug("[warehouse] redis add new data {}:{}.", key, hashKey);
                        } else {
                            log.debug("[warehouse] redis replace data {}:{}.", key, hashKey);
                        }
                    }));
                }
            }


            redisCommandDelegate.operate().hset(key, hashKey, metricsData, future -> future.thenAccept(response -> {
                if (response) {
                    log.debug("[warehouse] redis add new data {}:{}.", key, hashKey);
                } else {
                    log.debug("[warehouse] redis replace data {}:{}.", key, hashKey);
                }
            }));
        }
    }


    @Override
    public void destroy() throws Exception {
        redisCommandDelegate.destroy();
    }
}
