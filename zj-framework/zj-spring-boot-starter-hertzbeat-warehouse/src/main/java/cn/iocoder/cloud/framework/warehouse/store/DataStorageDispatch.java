/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.warehouse.store;

import cn.iocoder.cloud.framework.warehouse.WarehouseWorkerPool;
import cn.iocoder.cloud.framework.warehouse.dao.MonitorMetricsInterfaceDataDao;
import cn.iocoder.cloud.framework.warehouse.store.history.HistoryDataWriter;
import cn.iocoder.cloud.framework.warehouse.store.realtime.RealTimeDataWriter;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.manager.MonitorMetricsInterfaceData;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.apache.hertzbeat.common.queue.CommonDataQueue;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * dispatch storage metrics data
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DataStorageDispatch {

    private final CommonDataQueue commonDataQueue;
    private final WarehouseWorkerPool workerPool;

    private final RealTimeDataWriter realTimeDataWriter;
    private final Optional<HistoryDataWriter> historyDataWriter;
    private final MonitorMetricsInterfaceDataDao monitorMetricsDataDao;

    // 多线程处理配置
    private static final int REALTIME_CONSUMER_THREADS = 5;     // 实时存储消费者线程数
    private static final int PERSISTENT_CONSUMER_THREADS = 8;   // 持久化存储消费者线程数
    private static final int BATCH_SIZE = 100;                  // 批量处理大小

    private final AtomicInteger realTimeThreadCounter = new AtomicInteger(0);
    private final AtomicInteger persistentThreadCounter = new AtomicInteger(0);

    public DataStorageDispatch(CommonDataQueue commonDataQueue,
                               WarehouseWorkerPool workerPool,
                               Optional<HistoryDataWriter> historyDataWriter,
                               RealTimeDataWriter realTimeDataWriter, MonitorMetricsInterfaceDataDao monitorMetricsDataDao) {
        this.commonDataQueue = commonDataQueue;
        this.workerPool = workerPool;
        this.realTimeDataWriter = realTimeDataWriter;
        this.historyDataWriter = historyDataWriter;
        this.monitorMetricsDataDao = monitorMetricsDataDao;
        
        startPersistentDataStorage();
        startRealTimeDataStorage();
    }

    /**
     * 启动多线程实时数据存储消费者
     */
    private void startRealTimeDataStorage() {
        log.info("Starting {} realtime data storage consumer threads", REALTIME_CONSUMER_THREADS);
        
        for (int i = 0; i < REALTIME_CONSUMER_THREADS; i++) {
            Runnable runnable = () -> {
                int threadId = realTimeThreadCounter.incrementAndGet();
                Thread.currentThread().setName("warehouse-realtime-consumer-" + threadId);
                
                while (!Thread.currentThread().isInterrupted()) {
                    try {
                        CollectRep.MetricsData metricsData = commonDataQueue.pollMetricsDataToRealTimeStorage();
                        if (metricsData == null) {
                            continue;
                        }
                        
                        // 异步处理单个数据
                        processRealTimeDataAsync(metricsData, threadId);
                        
                    } catch (InterruptedException interruptedException) {
                        Thread.currentThread().interrupt();
                        log.info("Realtime storage consumer thread-{} interrupted", threadId);
                        break;
                    } catch (Exception e) {
                        log.error("Error in realtime storage consumer thread-{}: {}", threadId, e.getMessage(), e);
                    }
                }
            };
            workerPool.executeJob(runnable);
        }
    }

    /**
     * 启动多线程持久化数据存储消费者
     */
    protected void startPersistentDataStorage() {
        log.info("Starting {} persistent data storage consumer threads", PERSISTENT_CONSUMER_THREADS);
        
        for (int i = 0; i < PERSISTENT_CONSUMER_THREADS; i++) {
            Runnable runnable = () -> {
                int threadId = persistentThreadCounter.incrementAndGet();
                Thread.currentThread().setName("warehouse-persistent-consumer-" + threadId);
                
                while (!Thread.currentThread().isInterrupted()) {
                    try {
                        CollectRep.MetricsData metricsData = commonDataQueue.pollMetricsDataToPersistentStorage();
                        if (metricsData == null) {
                            continue;
                        }
                        
                        // 异步处理单个数据
                        processPersistentDataAsync(metricsData, threadId);

                    } catch (InterruptedException interruptedException) {
                        Thread.currentThread().interrupt();
                        log.info("Persistent storage consumer thread-{} interrupted", threadId);
                        break;
                    } catch (Exception e) {
                        log.error("Error in persistent storage consumer thread-{}: {}", threadId, e.getMessage(), e);
                    }
                }
            };
            workerPool.executeJob(runnable);
        }
    }

    /**
     * 异步处理实时数据存储
     */
    private void processRealTimeDataAsync(CollectRep.MetricsData metricsData, int threadId) {
        try {
            long startTime = System.currentTimeMillis();
            realTimeDataWriter.saveData(metricsData);
            saveDbMetricsData(metricsData);
            long cost = System.currentTimeMillis() - startTime;
            
            if (log.isDebugEnabled()) {
                log.debug("Realtime thread-{} processed monitorId:{} metrics:{} cost:{}ms", 
                         threadId, metricsData.getId(), metricsData.getMetrics(), cost);
            }
            
            // 监控处理耗时，如果超过阈值则告警
            if (cost > 1000) {
                log.warn("Realtime storage slow: thread-{} monitorId:{} metrics:{} cost:{}ms", 
                        threadId, metricsData.getId(), metricsData.getMetrics(), cost);
            }
            
        } catch (Exception e) {
            log.error("Failed to save realtime data in thread-{} for monitorId:{} metrics:{}: {}", 
                     threadId, metricsData.getId(), metricsData.getMetrics(), e.getMessage(), e);
        }
    }

    private void saveDbMetricsData(CollectRep.MetricsData metricsData) {
        long monitorId = metricsData.getId();
        String metrics = metricsData.getMetrics();
        if (metricsData.getCode() != CollectRep.Code.SUCCESS) {
            return;
        }

        if (metrics.equals("interface")) {
            // 1. 解析当前上报的数据
            List<MonitorMetricsInterfaceData> currentDataList = parseCurrentData(metricsData);

            // 2. 查询数据库中现有数据
            List<MonitorMetricsInterfaceData> existingDataList = monitorMetricsDataDao
                    .findByMonitorIdAndMetrics(monitorId, metrics);

            // 3. 数据比对处理
            processDataChanges(currentDataList, existingDataList);
        }
    }

    private List<MonitorMetricsInterfaceData> parseCurrentData(CollectRep.MetricsData metricsData) {
        List<MonitorMetricsInterfaceData> currentDataList = new ArrayList<>();
        List<CollectRep.Field> fields = metricsData.getFieldsList();
        //判断fields  的name  中是否存在index 字段
        boolean hasIndexField = fields.stream().anyMatch(field -> field.getName().equals("index"));
        if (!hasIndexField) {
            return new ArrayList<>();
        }
        for (CollectRep.ValueRow valueRow : metricsData.getValuesList()) {
            MonitorMetricsInterfaceData data = getMonitorMetricsInterfaceData(metricsData, valueRow, fields);
            currentDataList.add(data);
        }
        return currentDataList;
    }

    @NotNull
    private static MonitorMetricsInterfaceData getMonitorMetricsInterfaceData(CollectRep.MetricsData metricsData, CollectRep.ValueRow valueRow, List<CollectRep.Field> fields) {
        MonitorMetricsInterfaceData data = new MonitorMetricsInterfaceData();
        data.setMonitorId(metricsData.getId());
        data.setMetrics(metricsData.getMetrics());
        data.setAlias("");
        data.setAdminStatus("");
        data.setOperStatus("");

        for (int i = 0; i < fields.size(); i++) {
            String fieldName = fields.get(i).getName();
            String value = valueRow.getColumns(i);
            if (value.equals("&nbsp;")) {
                value = "";
            }

            switch (fieldName) {
                case "interface_name": data.setInterfaceName(value); break;
                case "index": data.setInterfaceIndex(value); break;
                case "alias": data.setAlias(value); break;
                case "admin_status": data.setAdminStatus(value); break;
                case "oper_status": data.setOperStatus(value); break;
            }
        }
        return data;
    }

    private void processDataChanges(List<MonitorMetricsInterfaceData> currentDataList,
                                    List<MonitorMetricsInterfaceData> existingDataList) {
        // 创建索引映射
        Map<String, MonitorMetricsInterfaceData> currentDataMap = currentDataList.stream()
                .collect(Collectors.toMap(MonitorMetricsInterfaceData::getInterfaceIndex, Function.identity()));

        Map<String, MonitorMetricsInterfaceData> existingDataMap = existingDataList.stream()
                .collect(Collectors.toMap(MonitorMetricsInterfaceData::getInterfaceIndex, Function.identity()));

        // 处理新增/修改
        currentDataMap.forEach((index, currentData) -> {
            if (existingDataMap.containsKey(index)) {
                // 修改操作：更新现有记录
                MonitorMetricsInterfaceData existingData = existingDataMap.get(index);
                updateExistingData(existingData, currentData);
                monitorMetricsDataDao.save(existingData);
            } else {
                // 新增操作
                monitorMetricsDataDao.save(currentData);
            }
        });

        // 处理删除（存在于数据库但不在当前数据中）
        existingDataMap.keySet().removeAll(currentDataMap.keySet());
        existingDataMap.values().forEach(data ->
                monitorMetricsDataDao.deleteById(data.getId()));
    }

    /**
     * 更新现有数据记录
     * @param existingData 数据库中已存在的记录
     * @param currentData 当前上报的新数据
     */
    private void updateExistingData(MonitorMetricsInterfaceData existingData,
                                    MonitorMetricsInterfaceData currentData) {
        // 更新所有需要变更的字段
        existingData.setInterfaceName(currentData.getInterfaceName());
        if (existingData.getAlias().isEmpty() && !currentData.getAlias().isEmpty()) {
            existingData.setAlias(currentData.getAlias());
        }
        existingData.setAdminStatus(currentData.getAdminStatus());
        existingData.setOperStatus(currentData.getOperStatus());
    }
        /**
     * 异步处理持久化数据存储
     */
    private void processPersistentDataAsync(CollectRep.MetricsData metricsData, int threadId) {
        try {
            if (historyDataWriter.isPresent()) {
                long startTime = System.currentTimeMillis();
                historyDataWriter.get().saveData(metricsData);
                long cost = System.currentTimeMillis() - startTime;
                
                if (log.isDebugEnabled()) {
                    log.debug("Persistent thread-{} processed monitorId:{} metrics:{} cost:{}ms", 
                             threadId, metricsData.getId(), metricsData.getMetrics(), cost);
                }
                
                // 监控处理耗时，持久化存储相对较慢是正常的
                if (cost > 3000) {
                    log.warn("Persistent storage slow: thread-{} monitorId:{} metrics:{} cost:{}ms", 
                            threadId, metricsData.getId(), metricsData.getMetrics(), cost);
                }
            }
        } catch (Exception e) {
            log.error("Failed to save persistent data in thread-{} for monitorId:{} metrics:{}: {}", 
                     threadId, metricsData.getId(), metricsData.getMetrics(), e.getMessage(), e);
        }
    }
}
