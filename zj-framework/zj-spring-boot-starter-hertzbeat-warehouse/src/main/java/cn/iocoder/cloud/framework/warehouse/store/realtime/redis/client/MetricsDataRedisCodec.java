package cn.iocoder.cloud.framework.warehouse.store.realtime.redis.client;

import io.lettuce.core.codec.RedisCodec;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.message.CollectRep;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

@Slf4j
public class MetricsDataRedisCodec implements RedisCodec<String, CollectRep.MetricsData> {

    @Override
    public String decodeKey(ByteBuffer byteBuffer) {
        return StandardCharsets.UTF_8.decode(byteBuffer).toString();
    }

    @Override
    public CollectRep.MetricsData decodeValue(ByteBuffer byteBuffer) {
        try {
            return CollectRep.MetricsData.parseFrom(byteBuffer);
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }

    @Override
    public ByteBuffer encodeKey(String s) {
        return ByteBuffer.wrap(s.getBytes(StandardCharsets.UTF_8));
    }

    @Override
    public ByteBuffer encodeValue(CollectRep.MetricsData metricsData) {
        return ByteBuffer.wrap(metricsData.toByteArray());
    }
}
