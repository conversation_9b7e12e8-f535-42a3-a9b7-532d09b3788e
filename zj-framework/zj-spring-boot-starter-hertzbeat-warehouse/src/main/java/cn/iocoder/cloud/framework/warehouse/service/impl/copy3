/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.warehouse.service.impl;


import cn.iocoder.cloud.framework.warehouse.dao.MonitorInfoDao;
import cn.iocoder.cloud.framework.warehouse.service.MetricsDataService;
import cn.iocoder.cloud.framework.warehouse.store.history.AbstractHistoryDataStorage;
import cn.iocoder.cloud.framework.warehouse.store.history.HistoryDataReader;
import cn.iocoder.cloud.framework.warehouse.store.realtime.RealTimeDataReader;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.constants.CommonConstants;
import org.apache.hertzbeat.common.constants.ResourceAppEnum;
import org.apache.hertzbeat.common.entity.dto.*;
import org.apache.hertzbeat.common.entity.manager.Monitor;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.apache.hertzbeat.common.support.exception.CommonException;
import org.apache.hertzbeat.common.util.ResourceCategoryUtil;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Metrics Data Service impl
 */
@Slf4j
@Service
public class MetricsDataServiceImpl implements MetricsDataService {

    private final RealTimeDataReader realTimeDataReader;

    private final Optional<HistoryDataReader> historyDataReader;

    private final MonitorInfoDao monitorInfoDao;

    private final List<AbstractHistoryDataStorage> historyDataStorages;

    public MetricsDataServiceImpl(RealTimeDataReader realTimeDataReader, Optional<HistoryDataReader> historyDataReader, MonitorInfoDao monitorInfoDao, List<AbstractHistoryDataStorage> historyDataStorages) {
        this.realTimeDataReader = realTimeDataReader;
        this.historyDataReader = historyDataReader;
        this.monitorInfoDao = monitorInfoDao;
        this.historyDataStorages = historyDataStorages;
    }

    @Override
    public Boolean getWarehouseStorageServerStatus() {
        return historyDataReader.isPresent() && historyDataReader.get().isServerAvailable();
    }

    @Override
    public MetricsData getMetricsData(Long monitorId, String metrics) {
        boolean available = realTimeDataReader.isServerAvailable();
        if (!available) {
            throw new CommonException("real time store not available");
        }
        CollectRep.MetricsData storageData = realTimeDataReader.getCurrentMetricsData(monitorId, metrics);
        if (storageData == null) {
            return null;
        }
        MetricsData.MetricsDataBuilder dataBuilder = MetricsData.builder();
        dataBuilder.id(storageData.getId()).app(storageData.getApp()).metrics(storageData.getMetrics())
                .time(storageData.getTime());
        List<Field> fields = storageData.getFieldsList().stream().map(tmpField ->
                        Field.builder().name(tmpField.getName())
                                .type(Integer.valueOf(tmpField.getType()).byteValue())
                                .label(tmpField.getLabel())
                                .unit(tmpField.getUnit())
                                .build())
                .collect(Collectors.toList());
        dataBuilder.fields(fields);
        List<ValueRow> valueRows = new LinkedList<>();
        for (CollectRep.ValueRow valueRow : storageData.getValuesList()) {
            Map<String, String> labels = new HashMap<>(8);
            List<Value> values = new LinkedList<>();
            for (int i = 0; i < fields.size(); i++) {
                Field field = fields.get(i);
                String origin = valueRow.getColumns(i);
                if (CommonConstants.NULL_VALUE.equals(origin)) {
                    values.add(new Value());
                } else {
                    values.add(new Value(origin));
                    if (field.getLabel()) {
                        labels.put(field.getName(), origin);
                    }
                }
            }
            valueRows.add(ValueRow.builder().labels(labels).values(values).build());
        }

        if (metrics.equals("interface")) {
            Monitor monitor = monitorInfoDao.getMonitorById(monitorId);
            List<ResourceAppEnum> subEnums = ResourceCategoryUtil.getSubEnumsByCategory("os");
            List<String> app = new ArrayList<>();
            for (ResourceAppEnum resourceAppEnum : subEnums) {
                if (resourceAppEnum.getValue() != "windows" && resourceAppEnum.getValue() != "linux_snmp") {
                    app.add(resourceAppEnum.getValue());
                }
            }
            // 老数据
            CollectRep.MetricsData oldStorageData = realTimeDataReader.getOldCurrentMetricsData(monitorId, metrics);
            if (oldStorageData != null) {
                MetricsData.MetricsDataBuilder oldataBuilder = MetricsData.builder();
                oldataBuilder.id(oldStorageData.getId()).app(oldStorageData.getApp()).metrics(oldStorageData.getMetrics())
                        .time(oldStorageData.getTime());
                List<Field> oldfields = oldStorageData.getFieldsList().stream().map(tmpField ->
                                Field.builder().name(tmpField.getName())
                                        .type(Integer.valueOf(tmpField.getType()).byteValue())
                                        .label(tmpField.getLabel())
                                        .unit(tmpField.getUnit())
                                        .build())
                        .collect(Collectors.toList());
                oldataBuilder.fields(oldfields);
                List<ValueRow> oldValueRows = new LinkedList<>();
                for (CollectRep.ValueRow valueRow : oldStorageData.getValuesList()) {
                    Map<String, String> labels = new HashMap<>(8);
                    List<Value> values = new LinkedList<>();
                    for (int i = 0; i < fields.size(); i++) {
                        Field field = fields.get(i);
                        String origin = valueRow.getColumns(i);
                        if (CommonConstants.NULL_VALUE.equals(origin)) {
                            values.add(new Value());
                        } else {
                            values.add(new Value(origin));
                            if (field.getLabel()) {
                                labels.put(field.getName(), origin);
                            }
                        }
                    }
                    oldValueRows.add(ValueRow.builder().labels(labels).values(values).build());
                }
                for (ValueRow valueRow : valueRows) {
                    for (ValueRow oldvalueRow : oldValueRows) {
                        if (valueRow.getLabels().get("interface_name").equals(oldvalueRow.getLabels().get("interface_name"))) {
                            // 端口入方向
                            double t1 = 0;
                            double t2 = 0;
                            if (app.contains(monitor.getApp())) {
                                if (valueRow.getValues().get(1).getOrigin() != null) {
                                    t1 = Double.parseDouble(valueRow.getValues().get(1).getOrigin());
                                }
                                if (oldvalueRow.getValues().get(1).getOrigin() != null) {
                                    t2 = Double.parseDouble(oldvalueRow.getValues().get(1).getOrigin());
                                }
                            } else {
                                if (valueRow.getValues().get(6).getOrigin() != null) {
                                    t1 = Double.parseDouble(valueRow.getValues().get(6).getOrigin());
                                }
                                if (oldvalueRow.getValues().get(6).getOrigin() != null) {
                                    t2 = Double.parseDouble(oldvalueRow.getValues().get(6).getOrigin());
                                }
                            }
                            double in = 8 * (t1 - t2) / 60;

                            // 端口出方向
                            double t1out = 0;
                            double t2out = 0;
                            if (app.contains(monitor.getApp())) {
                                if (valueRow.getValues().get(2).getOrigin() != null) {
                                    t1out = Double.parseDouble(valueRow.getValues().get(2).getOrigin());
                                }
                                if (oldvalueRow.getValues().get(2).getOrigin() != null) {
                                    t2out = Double.parseDouble(oldvalueRow.getValues().get(2).getOrigin());
                                }
                            } else {
                                if (valueRow.getValues().get(9).getOrigin() != null) {
                                    t1out = Double.parseDouble(valueRow.getValues().get(9).getOrigin());
                                }
                                if (oldvalueRow.getValues().get(9).getOrigin() != null) {
                                    t2out = Double.parseDouble(oldvalueRow.getValues().get(9).getOrigin());
                                }
                            }

                            double out = 8 * (t1out - t2out) / 60;

                            List<Value> values = valueRow.getValues();
                            for (int i = 0; i < values.size(); i++) {
                                if (app.contains(monitor.getApp())) {
                                    if (i == 1) {
                                        if (in < 0) {
                                            in = 0;
                                        }
                                        String formattedIn = String.format("%.4f", in);
                                        values.set(1, new Value(formattedIn));  // 使用 set 替代 add
                                    }
                                    if (i == 2) {
                                        if (out < 0) {
                                            out = 0;
                                        }
                                        String formattedIn = String.format("%.4f", out);
                                        values.set(2, new Value(formattedIn));  // 使用 set 替代 add
                                    }
                                } else {
                                    if (i == 6) {
                                        if (in < 0) {
                                            in = 0;
                                        }
                                        String formattedIn = String.format("%.4f", in);
                                        values.set(6, new Value(formattedIn));  // 使用 set 替代 add
                                    }
                                    if (i == 9) {
                                        if (out < 0) {
                                            out = 0;
                                        }
                                        String formattedIn = String.format("%.4f", out);
                                        values.set(9, new Value(formattedIn));  // 使用 set 替代 add
                                    }
                                }

                            }
                        }
                    }
                }
            }
        }
        dataBuilder.valueRows(valueRows);
        return dataBuilder.build();
    }


    @Override
    public MetricsHistoryData getMetricHistoryData(Long monitorId, String app, String metrics, String metric, String label, String history, Boolean interval) {
        if (history == null) {
            history = "6h";
        }
        // 获取 InfluxDB 实现
        AbstractHistoryDataStorage influxStorage = historyDataStorages.stream()
                .filter(storage -> storage.getClass().getSimpleName().toLowerCase().contains("influxdb"))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("未找到 InfluxDB 存储实现"));

        Map<String, List<Value>> instanceValuesMap;

        if (interval == null || !interval) {
            instanceValuesMap = historyDataReader.get().getHistoryMetricData(monitorId, app, metrics, metric, label, history);
        } else {
            instanceValuesMap = historyDataReader.get().getHistoryIntervalMetricData(monitorId, app, metrics, metric, label, history);
        }
        return MetricsHistoryData.builder()
                .id(monitorId).metrics(metrics).values(instanceValuesMap)
                .field(Field.builder().name(metric).type(CommonConstants.TYPE_NUMBER).build())
                .build();
    }

    @Override
    public MetricsData getMetricsDataByLabel(Long monitorId, String metrics, String label) {
        boolean available = realTimeDataReader.isServerAvailable();
        if (!available) {
            throw new CommonException("real time store not available");
        }
        CollectRep.MetricsData storageData = realTimeDataReader.getCurrentMetricsData(monitorId, metrics);
        if (storageData == null) {
            return null;
        }
        {
            MetricsData.MetricsDataBuilder dataBuilder = MetricsData.builder();
            dataBuilder.id(storageData.getId()).app(storageData.getApp()).metrics(storageData.getMetrics())
                    .time(storageData.getTime());
            List<Field> fields = storageData.getFieldsList().stream().map(tmpField ->
                            Field.builder().name(tmpField.getName())
                                    .type(Integer.valueOf(tmpField.getType()).byteValue())
                                    .label(tmpField.getLabel())
                                    .unit(tmpField.getUnit())
                                    .build())
                    .collect(Collectors.toList());
            dataBuilder.fields(fields);

            List<ValueRow> valueRows = new LinkedList<>();
            List<String> list = Arrays.asList(label.split(","));
            for (CollectRep.ValueRow valueRow : storageData.getValuesList()) {
                Map<String, String> labels = new HashMap<>(8);
                List<Value> values = new LinkedList<>();
                if (list.contains(valueRow.getColumns(1)) || list.contains(valueRow.getColumns(0))) {
                    for (int i = 0; i < fields.size(); i++) {
                        Field field = fields.get(i);
                        String origin = valueRow.getColumns(i);

                        if (CommonConstants.NULL_VALUE.equals(origin)) {
                            values.add(new Value());
                        } else {
                            values.add(new Value(origin));
                            if (field.getLabel()) {
                                labels.put(field.getName(), origin);
                            }
                        }
                    }
                    valueRows.add(ValueRow.builder().labels(labels).values(values).build());
                }


            }
            dataBuilder.valueRows(valueRows);
            return dataBuilder.build();
        }
    }
}
