/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.warehouse;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.*;

/**
 * warehouse 工作线程池
 * <AUTHOR>
 *
 */
@Component
@Slf4j
public class WarehouseWorkerPool {

    private ThreadPoolExecutor workerExecutor;

    // 优化线程池配置，支持高并发数据存储
    private static final int CORE_POOL_SIZE = 20;           // 核心线程数：增加到20个
    private static final int MAXIMUM_POOL_SIZE = 50;        // 最大线程数：增加到50个  
    private static final long KEEP_ALIVE_TIME = 60;         // 线程空闲时间：60秒
    private static final int QUEUE_CAPACITY = 1000;         // 队列容量：1000个任务

    public WarehouseWorkerPool() {
        initWorkExecutor();
    }

    private void initWorkExecutor() {
        // Thread factory
        ThreadFactory threadFactory = new ThreadFactoryBuilder()
                .setUncaughtExceptionHandler((thread, throwable) -> {
                    log.error("WarehouseWorkerPool thread {} has uncaughtException: {}", 
                             thread.getName(), throwable.getMessage());
                    log.error(throwable.getMessage(), throwable); 
                })
                .setDaemon(true)
                .setNameFormat("warehouse-worker-%d")
                .build();

        // 使用有界队列替代SynchronousQueue，避免任务拒绝
        BlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>(QUEUE_CAPACITY);
        
        // 自定义拒绝策略：当队列满时，在调用线程中执行
        RejectedExecutionHandler rejectedHandler = new ThreadPoolExecutor.CallerRunsPolicy();
        
        workerExecutor = new ThreadPoolExecutor(
                CORE_POOL_SIZE,         // 核心线程数  
                MAXIMUM_POOL_SIZE,      // 最大线程数
                KEEP_ALIVE_TIME,        // 线程空闲时间
                TimeUnit.SECONDS,       // 时间单位
                workQueue,              // 工作队列
                threadFactory,          // 线程工厂
                rejectedHandler         // 拒绝策略
        );
        
        // 允许核心线程超时回收
        workerExecutor.allowCoreThreadTimeOut(true);
        
        log.info("WarehouseWorkerPool initialized: coreSize={}, maxSize={}, queueCapacity={}", 
                CORE_POOL_SIZE, MAXIMUM_POOL_SIZE, QUEUE_CAPACITY);
    }

    /**
     * Run warehouse task
     * @param runnable task
     * @throws RejectedExecutionException when THREAD POOL FULL
     */
    public void executeJob(Runnable runnable) throws RejectedExecutionException {
        workerExecutor.execute(runnable);
        
        // 监控线程池状态
        if (log.isDebugEnabled()) {
            log.debug("WarehouseWorkerPool status: active={}, poolSize={}, queueSize={}", 
                     workerExecutor.getActiveCount(), 
                     workerExecutor.getPoolSize(),
                     workerExecutor.getQueue().size());
        }
    }

    /**
     * 获取线程池状态信息
     */
    public String getPoolStatus() {
        return String.format("WarehouseWorkerPool[active=%d, poolSize=%d, queueSize=%d, completedTasks=%d]",
                workerExecutor.getActiveCount(),
                workerExecutor.getPoolSize(), 
                workerExecutor.getQueue().size(),
                workerExecutor.getCompletedTaskCount());
    }

    /**
     * 优雅关闭线程池
     */
    public void shutdown() {
        if (workerExecutor != null && !workerExecutor.isShutdown()) {
            log.info("Shutting down WarehouseWorkerPool...");
            workerExecutor.shutdown();
            try {
                if (!workerExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                    log.warn("WarehouseWorkerPool did not terminate gracefully, forcing shutdown");
                    workerExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                log.warn("Interrupted while waiting for WarehouseWorkerPool termination");
                workerExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}
