package cn.iocoder.cloud.framework.warehouse.service.impl;

import cn.iocoder.cloud.framework.warehouse.service.WarehouseService;
import cn.iocoder.cloud.framework.warehouse.store.realtime.AbstractRealTimeDataStorage;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * warehouse service impl
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class WarehouseServiceImpl implements WarehouseService {

    private final AbstractRealTimeDataStorage realTimeDataStorage;

    public WarehouseServiceImpl(AbstractRealTimeDataStorage realTimeDataStorage) {
        this.realTimeDataStorage = realTimeDataStorage;
    }

    @Override
    public List<CollectRep.MetricsData> queryMonitorMetricsData(Long monitorId) {
        boolean available = realTimeDataStorage.isServerAvailable();
        if (!available) {
            log.error("real time store not available");
            return Collections.emptyList();
        }
        return realTimeDataStorage.getCurrentMetricsData(monitorId);
    }
}