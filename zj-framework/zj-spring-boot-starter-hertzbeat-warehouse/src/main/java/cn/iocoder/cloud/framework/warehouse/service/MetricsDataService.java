/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.warehouse.service;


import org.apache.hertzbeat.common.entity.dto.MetricsData;
import org.apache.hertzbeat.common.entity.dto.MetricsHistoryData;

/**
 * service for metrics data
 */
public interface MetricsDataService {

    /**
     * warehouse storage server availability or not
     *
     * @return true or false
     */
    Boolean getWarehouseStorageServerStatus();

    /**
     * Query Real Time Metrics Data
     *
     * @param monitorId Monitor Id
     * @param metrics   Metrics Name
     * @return metrics data
     */
    MetricsData getMetricsData(Long monitorId, String metrics);

    /**
     * Queries historical data for a specified metric for monitoring
     *
     * @param monitorId Monitor Id
     * @param app       Monitor Type
     * @param metrics   Metrics Name
     * @param metric    Metrics Field Name
     * @param label     Label Filter
     * @param history   Query Historical Time Period
     * @param interval  aggregate data calc
     * @return metrics history data
     */
    MetricsHistoryData getMetricHistoryData(Long monitorId, String app, String metrics, String metric, String label, String history, Boolean interval);

    /**
     * Queries historical data for a specified metric for monitoring
     *
     * @param monitorId Monitor Id
     * @param app       Monitor Type
     * @param metrics   Metrics Name
     * @param metric    Metrics Field Name
     * @param label     Label Filter
     * @param history   Query Historical Time Period
     * @param step
     * @return metrics history data
     */
    MetricsHistoryData getHistoryIntervalMetricData(Long monitorId, String app, String metrics, String metric, String label, String history, String step);

    /**
     * Queries historical data for a specified metric for monitoring
     *
     * @param monitorId Monitor Id
     * @param app       Monitor Type
     * @param metrics   Metrics Name
     * @param metric    Metrics Field Name
     * @param label     Label Filter
     * @param history   Query Historical Time Period
     * @param start     start time
     * @param end       end time
     * @param step
     * @param type
     * @return metrics history data
     */
    MetricsHistoryData getHistoryIntervalMetricData(Long monitorId, String app, String metrics, String metric, String label, String history, Long start, Long end, String step, String type);

    MetricsData getMetricsDataByLabel(Long monitorId, String metrics, String label);

    MetricsData getMetricData(Long monitorId, String metrics);
}
