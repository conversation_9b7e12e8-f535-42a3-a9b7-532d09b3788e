/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.warehouse.store.history.vm;

import cn.iocoder.cloud.framework.warehouse.store.history.AbstractHistoryDataStorage;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.hertzbeat.common.constants.CommonConstants;
import org.apache.hertzbeat.common.constants.NetworkConstants;
import org.apache.hertzbeat.common.constants.SignConstants;
import org.apache.hertzbeat.common.entity.dto.Value;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.apache.hertzbeat.common.util.Base64Util;
import org.apache.hertzbeat.common.util.CommonUtil;
import org.apache.hertzbeat.common.util.JsonUtil;
import org.apache.hertzbeat.common.util.TimePeriodUtil;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Primary;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.ZonedDateTime;
import java.time.temporal.TemporalAmount;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;

import static org.apache.hertzbeat.common.constants.ConfigConstants.FunctionModuleConstants.STATUS;


/**
 * tdengine data storage
 */
@Primary
@Component
@ConditionalOnProperty(prefix = "warehouse.store.victoria-metrics.cluster", name = "enabled", havingValue = "true")
@Slf4j
public class VictoriaMetricsClusterDataStorage extends AbstractHistoryDataStorage {

    private static final String IMPORT_PATH = "/0/prometheus/api/v1/import";
    private static final String EXPORT_PATH = "/0/prometheus/api/v1/export";
    private static final String STATUS_PATH = "/0/prometheus/api/v1/status/tsdb";
    private static final String STATUS_SUCCESS = "success";
    private static final String QUERY_RANGE_PATH = "/0/prometheus/api/v1/query_range";
    private static final String LABEL_KEY_NAME = "__name__";
    private static final String LABEL_KEY_JOB = "job";
    private static final String LABEL_KEY_INSTANCE = "instance";
    private static final String LABEL_KEY_INSTANCE_NAME = "interface_name";
    private static final String SPILT = "_";
    private static final String MONITOR_METRICS_KEY = "__metrics__";
    private static final String MONITOR_METRIC_KEY = "__metric__";

//    private final VictoriaMetricsInsertProperties vmInsertProps;
//    private final VictoriaMetricsSelectProperties vmSelectProps;

    private final VictoriaMetricsClusterProperties vmClusterProps;

    private final RestTemplate restTemplate;

    public VictoriaMetricsClusterDataStorage(VictoriaMetricsClusterProperties vmClusterProps,
                                             RestTemplate restTemplate) {
        if (vmClusterProps == null) {
            log.error("init error, please config Warehouse victoriaMetrics cluster props in application.yml");
            throw new IllegalArgumentException("please config Warehouse victoriaMetrics cluster props");
        }
        this.restTemplate = restTemplate;
        this.vmClusterProps = vmClusterProps;
        serverAvailable = checkVictoriaMetricsDatasourceAvailable();
    }

    private boolean checkVictoriaMetricsDatasourceAvailable() {
        // check server status
        try {
            String result = restTemplate.getForObject(vmClusterProps.getSUrl() + STATUS_PATH, String.class);

            JsonNode jsonNode = JsonUtil.fromJson(result);
            if (jsonNode != null && STATUS_SUCCESS.equalsIgnoreCase(jsonNode.get(STATUS).asText())) {
                return true;
            }
            log.error("check victoria metrics cluster server status not success: {}.", result);
        } catch (Exception e) {
            log.error("check victoria metrics cluster server status error: {}.", e.getMessage());
        }
        return false;
    }

    @Override
    public void saveData(CollectRep.MetricsData metricsData) {
        if (!isServerAvailable()) {
            serverAvailable = checkVictoriaMetricsDatasourceAvailable();
        }
        if (!isServerAvailable() || metricsData.getCode() != CollectRep.Code.SUCCESS) {
            return;
        }
        if (metricsData.getValuesList().isEmpty()) {
            log.info("[warehouse victoria-metrics] flush metrics data {} {} {} is null, ignore.",
                    metricsData.getId(), metricsData.getApp(), metricsData.getMetrics());
            return;
        }
        Map<String, String> defaultLabels = new HashMap<>(8);
        defaultLabels.put(MONITOR_METRICS_KEY, metricsData.getMetrics());
        boolean isPrometheusAuto = false;
        if (metricsData.getApp().startsWith(CommonConstants.PROMETHEUS_APP_PREFIX)) {
            isPrometheusAuto = true;
            defaultLabels.remove(MONITOR_METRICS_KEY);
            defaultLabels.put(LABEL_KEY_JOB, metricsData.getApp()
                    .substring(CommonConstants.PROMETHEUS_APP_PREFIX.length()));
        } else {
            defaultLabels.put(LABEL_KEY_JOB, metricsData.getApp());
        }
        defaultLabels.put(LABEL_KEY_INSTANCE, String.valueOf(metricsData.getId()));

        List<CollectRep.Field> fields = metricsData.getFieldsList();
        Long[] timestamp = new Long[]{metricsData.getTime()};
        Map<String, Double> fieldsValue = new HashMap<>(fields.size());
        Map<String, String> labels = new HashMap<>(fields.size());
        List<VictoriaMetricsDataStorage.VictoriaMetricsContent> contentList = new LinkedList<>();
        for (CollectRep.ValueRow valueRow : metricsData.getValuesList()) {
            fieldsValue.clear();
            labels.clear();
            for (int index = 0; index < fields.size(); index++) {
                CollectRep.Field field = fields.get(index);
                String value = valueRow.getColumns(index);
                if (field.getType() == CommonConstants.TYPE_NUMBER && !field.getLabel()) {
                    // number metrics data
                    if (!CommonConstants.NULL_VALUE.equals(value)) {
                        fieldsValue.put(field.getName(), CommonUtil.parseStrDouble(value));
                    }
                }
                // label
                if (field.getLabel() && !CommonConstants.NULL_VALUE.equals(value)) {
                    labels.put(field.getName(), value);
                }
            }
            for (Map.Entry<String, Double> entry : fieldsValue.entrySet()) {
                if (entry.getKey() != null && entry.getValue() != null) {
                    try {
                        labels.putAll(defaultLabels);
                        String labelName = isPrometheusAuto ? metricsData.getMetrics()
                                : metricsData.getMetrics() + SPILT + entry.getKey();
                        labels.put(LABEL_KEY_NAME, labelName);
                        if (!isPrometheusAuto) {
                            labels.put(MONITOR_METRIC_KEY, entry.getKey());
                        }
                        VictoriaMetricsDataStorage.VictoriaMetricsContent content = VictoriaMetricsDataStorage.VictoriaMetricsContent.builder()
                                .metric(new HashMap<>(labels))
                                .values(new Double[]{entry.getValue()})
                                .timestamps(timestamp)
                                .build();
                        contentList.add(content);
                    } catch (Exception e) {
                        log.error("combine metrics data error: {}.", e.getMessage(), e);
                    }

                }
            }
        }
        if (contentList.isEmpty()) {
            log.info("[warehouse victoria-metrics] flush metrics data {} is empty, ignore.", metricsData.getId());
            return;
        }
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            if (StringUtils.hasText(vmClusterProps.getUsername())
                    && StringUtils.hasText(vmClusterProps.getPassword())) {
                String authStr = vmClusterProps.getUsername() + ":" + vmClusterProps.getPassword();
                String encodedAuth = new String(Base64.encodeBase64(authStr.getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8);
                headers.add(HttpHeaders.AUTHORIZATION, NetworkConstants.BASIC + SignConstants.BLANK + encodedAuth);
            }
            StringBuilder stringBuilder = new StringBuilder();
            for (VictoriaMetricsDataStorage.VictoriaMetricsContent content : contentList) {
                stringBuilder.append(JsonUtil.toJson(content)).append("\n");
            }
            HttpEntity<String> httpEntity = new HttpEntity<>(stringBuilder.toString(), headers);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(vmClusterProps.getIUrl() + IMPORT_PATH,
                    httpEntity, String.class);
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                log.debug("insert metrics data to victoria-metrics success.");
            } else {
                log.error("insert metrics data to victoria-metrics failed. {}", responseEntity.getBody());
            }
        } catch (Exception e) {
            log.error("flush metrics data to victoria-metrics error: {}.", e.getMessage(), e);
        }
    }


    @Override
    public void destroy() {
    }

    @Override
    public Map<String, List<Value>> getHistoryMetricData(Long monitorId, String app, String metrics, String metric,
                                                         String label, String history) {
        String labelName = metrics + SPILT + metric;
        if (CommonConstants.PROMETHEUS.equals(app)) {
            labelName = metrics;
        }
        Map<String, List<Value>> instanceValuesMap = new HashMap<>(8);

        if (metric.equals("interface")) {
            Set<String> instances = new HashSet<>(8);
            if (label != null) {
                instances.add(label);
            }
            if (instances.isEmpty()) {
                instances.add("");
            }
            for (String instanceValue : instances) {
                if (app.equals("linux")) {
                    labelName = metrics + SPILT + "(receive_bytes|transmit_bytes)";
                } else {
                    labelName = metrics + SPILT + "(in_octets|out_octets)";
                }

                String timeSeriesSelector =
                        LABEL_KEY_NAME + "=~\"" + labelName + "\"" + "," + LABEL_KEY_INSTANCE + "=\"" + monitorId + "\"" + ","
                                + LABEL_KEY_INSTANCE_NAME + "=\"" + instanceValue + "\"";
                try {
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_JSON);
                    headers.setAccept(List.of(MediaType.APPLICATION_JSON));
                    if (StringUtils.hasText(vmClusterProps.getUsername()) && StringUtils.hasText(vmClusterProps.getPassword())) {
                        String authStr = vmClusterProps.getUsername() + ":" + vmClusterProps.getPassword();
                        String encodedAuth = new String(Base64.encodeBase64(authStr.getBytes(StandardCharsets.UTF_8)),
                                StandardCharsets.UTF_8);
                        headers.add(HttpHeaders.AUTHORIZATION, NetworkConstants.BASIC
                                + SignConstants.BLANK + encodedAuth);
                    }
                    HttpEntity<Void> httpEntity = new HttpEntity<>(headers);
                    URI uri = UriComponentsBuilder.fromHttpUrl(vmClusterProps.getSUrl() + EXPORT_PATH)
                            .queryParam(URLEncoder.encode("match[]", StandardCharsets.UTF_8),
                                    URLEncoder.encode("{" + timeSeriesSelector + "}", StandardCharsets.UTF_8))
                            .queryParam("start", URLEncoder.encode("now-" + history, StandardCharsets.UTF_8))
                            .queryParam("end", "now").build(true).toUri();
                    ResponseEntity<String> responseEntity = restTemplate.exchange(uri, HttpMethod.GET, httpEntity,
                            String.class);

                    if (responseEntity.getStatusCode().is2xxSuccessful()) {
                        log.debug("query metrics data from victoria-metrics success. {}", uri);
                        if (StringUtils.hasText(responseEntity.getBody())) {
                            String[] contentJsonArr = responseEntity.getBody().split("\n");
                            List<VictoriaMetricsContent> contents = Arrays.stream(contentJsonArr)
                                    .map(item -> JsonUtil.fromJson(item, VictoriaMetricsContent.class)).toList();
                            for (VictoriaMetricsContent content : contents) {
                                Map<String, String> labels = content.getMetric();
                                String inOctets = content.getMetric().get(MONITOR_METRIC_KEY);
                                labels.remove(LABEL_KEY_NAME);
                                labels.remove(LABEL_KEY_JOB);
                                labels.remove(LABEL_KEY_INSTANCE);
                                labels.remove(MONITOR_METRICS_KEY);
                                labels.remove(MONITOR_METRIC_KEY);


//                                String labelStr = JsonUtil.toJson(labels);
                                if (content.getValues() != null && content.getTimestamps() != null) {
                                    List<Value> valueList = instanceValuesMap.computeIfAbsent(instanceValue,
                                            k -> new LinkedList<>());
                                    if (content.getValues().length != content.getTimestamps().length) {
                                        log.error("content.getValues().length != content.getTimestamps().length");
                                        continue;
                                    }
                                    Double[] values = content.getValues();
                                    Long[] timestamps = content.getTimestamps();
                                    for (int index = 0; index < content.getValues().length; index++) {

                                        String strValue = BigDecimal.valueOf(values[index]).setScale(4, RoundingMode.HALF_UP)
                                                .stripTrailingZeros().toPlainString();
                                        // read timestamp here is ms unit
                                        long time = timestamps[index];
                                        if (inOctets.equals("in_octets") || inOctets.equals("receive_bytes")) {
                                            valueList.add(new Value(strValue, time));
                                        } else if (inOctets.equals("out_octets") || inOctets.equals("transmit_bytes")) {
                                            Value existingValue = valueList.stream()
                                                    .filter(v -> v.getTime() == time)
                                                    .findFirst()
                                                    .orElse(null);
                                            existingValue.setOutigin(strValue);
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        log.error("query metrics data from victoria-metrics failed. {}", responseEntity);
                    }
                } catch (Exception e) {
                    log.error("query metrics data from victoria-metrics error. {}.", e.getMessage(), e);
                }

            }


        } else {
            String timeSeriesSelector =
                    LABEL_KEY_NAME + "=\"" + labelName + "\"" + "," + LABEL_KEY_INSTANCE + "=\"" + monitorId + "\"" + ","
                            + MONITOR_METRIC_KEY + "=\"" + metric + "\"";

            try {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                headers.setAccept(List.of(MediaType.APPLICATION_JSON));
                if (StringUtils.hasText(vmClusterProps.getUsername()) && StringUtils.hasText(vmClusterProps.getPassword())) {
                    String authStr = vmClusterProps.getUsername() + ":" + vmClusterProps.getPassword();
                    String encodedAuth = new String(Base64.encodeBase64(authStr.getBytes(StandardCharsets.UTF_8)),
                            StandardCharsets.UTF_8);
                    headers.add(HttpHeaders.AUTHORIZATION, NetworkConstants.BASIC
                            + SignConstants.BLANK + encodedAuth);
                }
                HttpEntity<Void> httpEntity = new HttpEntity<>(headers);
                URI uri = UriComponentsBuilder.fromHttpUrl(vmClusterProps.getSUrl() + EXPORT_PATH)
                        .queryParam(URLEncoder.encode("match[]", StandardCharsets.UTF_8),
                                URLEncoder.encode("{" + timeSeriesSelector + "}", StandardCharsets.UTF_8))
                        .queryParam("start", URLEncoder.encode("now-" + history, StandardCharsets.UTF_8))
                        .queryParam("end", "now").build(true).toUri();
                ResponseEntity<String> responseEntity = restTemplate.exchange(uri, HttpMethod.GET, httpEntity,
                        String.class);
                if (responseEntity.getStatusCode().is2xxSuccessful()) {
                    log.debug("query metrics data from victoria-metrics success. {}", uri);
                    if (StringUtils.hasText(responseEntity.getBody())) {
                        String[] contentJsonArr = responseEntity.getBody().split("\n");
                        List<VictoriaMetricsContent> contents = Arrays.stream(contentJsonArr)
                                .map(item -> JsonUtil.fromJson(item, VictoriaMetricsContent.class)).toList();
                        for (VictoriaMetricsContent content : contents) {
                            Map<String, String> labels = content.getMetric();
                            String inOctets = content.getMetric().get(MONITOR_METRIC_KEY);
                            labels.remove(LABEL_KEY_NAME);
                            labels.remove(LABEL_KEY_JOB);
                            labels.remove(LABEL_KEY_INSTANCE);
                            labels.remove(MONITOR_METRICS_KEY);
                            labels.remove(MONITOR_METRIC_KEY);
                            String labelStr = JsonUtil.toJson(labels);
                            if (content.getValues() != null && content.getTimestamps() != null) {
                                List<Value> valueList = instanceValuesMap.computeIfAbsent(labelStr,
                                        k -> new LinkedList<>());
                                if (content.getValues().length != content.getTimestamps().length) {
                                    log.error("content.getValues().length != content.getTimestamps().length");
                                    continue;
                                }

                                Double[] values = content.getValues();
                                Long[] timestamps = content.getTimestamps();
                                for (int index = 0; index < content.getValues().length; index++) {
                                    String strValue = BigDecimal.valueOf(values[index]).setScale(4, RoundingMode.HALF_UP)
                                            .stripTrailingZeros().toPlainString();
                                    // read timestamp here is ms unit
                                    valueList.add(new Value(strValue, timestamps[index]));
                                }
                            }
                        }
                    }
                } else {
                    log.error("query metrics data from victoria-metrics failed. {}", responseEntity);
                }
            } catch (Exception e) {
                log.error("query metrics data from victoria-metrics error. {}.", e.getMessage(), e);
            }
        }


        if (metric.equals("in_octets") || metric.equals("out_octets")) {
            for (Map.Entry<String, List<Value>> entry : instanceValuesMap.entrySet()) {
                String key = entry.getKey(); // 获取 Map 的键
                List<Value> valueList = entry.getValue(); // 获取对应的值（List<Value>）
                if (!valueList.isEmpty()) {
                    for (int i = 0; i < valueList.size() - 1; i++) {
                        Value currentValue = valueList.get(i);
                        Value nextValue = valueList.get(i + 1);
                        // 假设 Value 类有一个 getValue() 方法返回该值
                        double result = Double.parseDouble(nextValue.getOrigin()) - Double.parseDouble(currentValue.getOrigin());
                        if (result < 0) {
                            result = 0;
                        }
                        double values = 8 * result / 60;
                        String formattedIn = String.format("%.4f", values);
                        currentValue.setOrigin(formattedIn);
                    }
                }
                valueList.remove(valueList.size() - 1);
            }
        }

        if (metric.equals("interface")) {
            for (Map.Entry<String, List<Value>> entry : instanceValuesMap.entrySet()) {
                String key = entry.getKey(); // 获取 Map 的键
                List<Value> valueList = entry.getValue(); // 获取对应的值（List<Value>）
                if (!valueList.isEmpty()) {
                    for (int i = 0; i < valueList.size() - 1; i++) {
                        Value currentValue = valueList.get(i);
                        Value nextValue = valueList.get(i + 1);
                        // 假设 Value 类有一个 getValue() 方法返回该值
                        double result = Double.parseDouble(nextValue.getOrigin()) - Double.parseDouble(currentValue.getOrigin());
                        if (result < 0) {
                            result = 0;
                        }
                        double values = 8 * result / 60;
                        String formattedIn = String.format("%.4f", values);
                        currentValue.setOrigin(formattedIn);

                        double outResult = Double.parseDouble(nextValue.getOutigin()) - Double.parseDouble(currentValue.getOutigin());
                        if (outResult < 0) {
                            outResult = 0;
                        }
                        double outValues = 8 * outResult / 60;
                        String formattedOut = String.format("%.4f", outValues);
                        currentValue.setOutigin(formattedOut);
                    }

                }
                if (valueList.size() > 0) {
                    valueList.remove(valueList.size() - 1);
                }

            }

        }

        return instanceValuesMap;
    }

    @Override
    public Map<String, List<Value>> getHistoryIntervalMetricData(Long monitorId, String app, String metrics,
                                                                 String metric, String label, String history) {
        if (!serverAvailable) {
            log.error("""
                    
                    \t---------------VictoriaMetrics Init Failed---------------
                    \t--------------Please Config VictoriaMetrics--------------
                    \t----------Can Not Use Metric History Now----------
                    """);
            return Collections.emptyMap();
        }
        long endTime = ZonedDateTime.now().toEpochSecond();
        long startTime;
        try {
            if (NumberUtils.isParsable(history)) {
                startTime = NumberUtils.toLong(history);
                startTime = (ZonedDateTime.now().toEpochSecond() - startTime);
            } else {
                TemporalAmount temporalAmount = TimePeriodUtil.parseTokenTime(history);
                ZonedDateTime dateTime = ZonedDateTime.now().minus(temporalAmount);
                startTime = dateTime.toEpochSecond();
            }
        } catch (Exception e) {
            log.error("history time error: {}. use default: 6h", e.getMessage());
            ZonedDateTime dateTime = ZonedDateTime.now().minus(Duration.ofHours(6));
            startTime = dateTime.toEpochSecond();
        }
        String labelName = metrics + SPILT + metric;
        if (CommonConstants.PROMETHEUS.equals(app)) {
            labelName = metrics;
        }

        Map<String, List<Value>> instanceValuesMap = new HashMap<>(8);
        Set<String> instances = new HashSet<>(8);
        if (label != null) {
            instances.add(label);
        }
        if (instances.isEmpty()) {
            instances.add("");
        }
        if (metric.equals("interface")) {
            for (String instanceValue : instances) {
                // 处理入站流量
                if (app.equals("linux")) {
                    labelName = metrics + SPILT + "(receive_bytes|transmit_bytes)";
                } else {
                    labelName = metrics + SPILT + "(in_octets|out_octets)";
                }

                String timeSeriesSelector =
                        LABEL_KEY_NAME + "=~\"" + labelName + "\"" + "," + LABEL_KEY_INSTANCE + "=\"" + monitorId + "\"" + ","
                                + LABEL_KEY_INSTANCE_NAME + "=\"" + instanceValue + "\"";
                try {
                    // 查询出站流量数据
                    queryAndProcessMetrics(timeSeriesSelector, startTime, endTime, instanceValuesMap, false, instanceValue);

                } catch (Exception e) {
                    log.error("query metrics data from victoria-metrics error. {}.", e.getMessage(), e);
                }
            }
        } else {
            String timeSeriesSelector =
                    LABEL_KEY_NAME + "=\"" + labelName + "\"" + "," + LABEL_KEY_INSTANCE + "=\"" + monitorId + "\"" + ","
                            + MONITOR_METRIC_KEY + "=\"" + metric + "\"";
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                headers.setAccept(List.of(MediaType.APPLICATION_JSON));
                if (StringUtils.hasText(vmClusterProps.getUsername()) && StringUtils.hasText(vmClusterProps.getPassword())) {
                    String authStr = vmClusterProps.getUsername() + ":" + vmClusterProps.getPassword();
                    String encodedAuth = Base64Util.encode(authStr);
                    headers.add(HttpHeaders.AUTHORIZATION, NetworkConstants.BASIC
                            + SignConstants.BLANK + encodedAuth);
                }
                HttpEntity<Void> httpEntity = new HttpEntity<>(headers);
                URI uri = UriComponentsBuilder.fromHttpUrl(vmClusterProps.getSUrl() + QUERY_RANGE_PATH)
                        .queryParam(URLEncoder.encode("query", StandardCharsets.UTF_8),
                                URLEncoder.encode("{" + timeSeriesSelector + "}", StandardCharsets.UTF_8))
                        .queryParam("step", "4h").queryParam("start", startTime).queryParam("end", endTime).build(true)
                        .toUri();
                ResponseEntity<PromQlQueryContent> responseEntity = restTemplate.exchange(uri, HttpMethod.GET,
                        httpEntity, PromQlQueryContent.class);
                if (responseEntity.getStatusCode().is2xxSuccessful()) {
                    log.debug("query metrics data from victoria-metrics success. {}", uri);
                    if (responseEntity.getBody() != null && responseEntity.getBody().getData() != null
                            && responseEntity.getBody().getData().getResult() != null) {
                        List<PromQlQueryContent.ContentData.Content> contents = responseEntity.getBody().getData()
                                .getResult();
                        for (PromQlQueryContent.ContentData.Content content : contents) {
                            Map<String, String> labels = content.getMetric();
                            labels.remove(LABEL_KEY_NAME);
                            labels.remove(LABEL_KEY_JOB);
                            labels.remove(LABEL_KEY_INSTANCE);
                            labels.remove(MONITOR_METRICS_KEY);
                            labels.remove(MONITOR_METRIC_KEY);
                            String labelStr = JsonUtil.toJson(labels);
                            if (content.getValues() != null && !content.getValues().isEmpty()) {
                                List<Value> valueList = instanceValuesMap.computeIfAbsent(labelStr,
                                        k -> new LinkedList<>());
                                for (Object[] valueArr : content.getValues()) {
                                    long timestamp = Long.parseLong(String.valueOf(valueArr[0]));
                                    String value = new BigDecimal(String.valueOf(valueArr[1])).setScale(4,
                                            RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
                                    // read timestamp here is s unit
                                    valueList.add(new Value(value, timestamp * 1000));
                                }
                            }
                        }
                    }
                } else {
                    log.error("query metrics data from victoria-metrics failed. {}", responseEntity);
                }
                // max
                uri = UriComponentsBuilder.fromHttpUrl(vmClusterProps.getSUrl() + QUERY_RANGE_PATH)
                        .queryParam(URLEncoder.encode("query", StandardCharsets.UTF_8),
                                URLEncoder.encode("max_over_time({" + timeSeriesSelector + "})", StandardCharsets.UTF_8))
                        .queryParam("step", "4h").queryParam("start", startTime).queryParam("end", endTime).build(true)
                        .toUri();
                responseEntity = restTemplate.exchange(uri, HttpMethod.GET, httpEntity, PromQlQueryContent.class);
                if (responseEntity.getStatusCode().is2xxSuccessful()) {
                    if (responseEntity.getBody() != null && responseEntity.getBody().getData() != null
                            && responseEntity.getBody().getData().getResult() != null) {
                        List<PromQlQueryContent.ContentData.Content> contents = responseEntity.getBody().getData()
                                .getResult();
                        for (PromQlQueryContent.ContentData.Content content : contents) {
                            Map<String, String> labels = content.getMetric();
                            labels.remove(LABEL_KEY_NAME);
                            labels.remove(LABEL_KEY_JOB);
                            labels.remove(LABEL_KEY_INSTANCE);
                            labels.remove(MONITOR_METRICS_KEY);
                            labels.remove(MONITOR_METRIC_KEY);
                            String labelStr = JsonUtil.toJson(labels);
                            if (content.getValues() != null && !content.getValues().isEmpty()) {
                                List<Value> valueList = instanceValuesMap.computeIfAbsent(labelStr,
                                        k -> new LinkedList<>());
                                if (valueList.size() == content.getValues().size()) {
                                    for (int timestampIndex = 0; timestampIndex < valueList.size(); timestampIndex++) {
                                        Value value = valueList.get(timestampIndex);
                                        Object[] valueArr = content.getValues().get(timestampIndex);
                                        String maxValue = new BigDecimal(String.valueOf(valueArr[1])).setScale(4,
                                                RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
                                        value.setMax(maxValue);
                                    }
                                }
                            }
                        }
                    }
                }
                // min
                uri = UriComponentsBuilder.fromHttpUrl(vmClusterProps.getSUrl() + QUERY_RANGE_PATH)
                        .queryParam(URLEncoder.encode("query", StandardCharsets.UTF_8),
                                URLEncoder.encode("min_over_time({" + timeSeriesSelector + "})", StandardCharsets.UTF_8))
                        .queryParam("step", "4h").queryParam("start", startTime).queryParam("end", endTime).build(true)
                        .toUri();
                responseEntity = restTemplate.exchange(uri, HttpMethod.GET, httpEntity, PromQlQueryContent.class);
                if (responseEntity.getStatusCode().is2xxSuccessful()) {
                    if (responseEntity.getBody() != null && responseEntity.getBody().getData() != null
                            && responseEntity.getBody().getData().getResult() != null) {
                        List<PromQlQueryContent.ContentData.Content> contents = responseEntity.getBody().getData()
                                .getResult();
                        for (PromQlQueryContent.ContentData.Content content : contents) {
                            Map<String, String> labels = content.getMetric();
                            labels.remove(LABEL_KEY_NAME);
                            labels.remove(LABEL_KEY_JOB);
                            labels.remove(LABEL_KEY_INSTANCE);
                            labels.remove(MONITOR_METRICS_KEY);
                            labels.remove(MONITOR_METRIC_KEY);
                            String labelStr = JsonUtil.toJson(labels);
                            if (content.getValues() != null && !content.getValues().isEmpty()) {
                                List<Value> valueList = instanceValuesMap.computeIfAbsent(labelStr,
                                        k -> new LinkedList<>());
                                if (valueList.size() == content.getValues().size()) {
                                    for (int timestampIndex = 0; timestampIndex < valueList.size(); timestampIndex++) {
                                        Value value = valueList.get(timestampIndex);
                                        Object[] valueArr = content.getValues().get(timestampIndex);
                                        String minValue = new BigDecimal(String.valueOf(valueArr[1])).setScale(4,
                                                RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
                                        value.setMin(minValue);
                                    }
                                }
                            }
                        }
                    }
                }
                // avg
                uri = UriComponentsBuilder.fromHttpUrl(vmClusterProps.getSUrl() + QUERY_RANGE_PATH)
                        .queryParam(URLEncoder.encode("query", StandardCharsets.UTF_8),
                                URLEncoder.encode("avg_over_time({" + timeSeriesSelector + "})", StandardCharsets.UTF_8))
                        .queryParam("step", "4h").queryParam("start", startTime).queryParam("end", endTime).build(true)
                        .toUri();
                responseEntity = restTemplate.exchange(uri, HttpMethod.GET, httpEntity, PromQlQueryContent.class);
                if (responseEntity.getStatusCode().is2xxSuccessful()) {
                    if (responseEntity.getBody() != null && responseEntity.getBody().getData() != null
                            && responseEntity.getBody().getData().getResult() != null) {
                        List<PromQlQueryContent.ContentData.Content> contents = responseEntity.getBody().getData()
                                .getResult();
                        for (PromQlQueryContent.ContentData.Content content : contents) {
                            Map<String, String> labels = content.getMetric();
                            labels.remove(LABEL_KEY_NAME);
                            labels.remove(LABEL_KEY_JOB);
                            labels.remove(LABEL_KEY_INSTANCE);
                            labels.remove(MONITOR_METRICS_KEY);
                            labels.remove(MONITOR_METRIC_KEY);
                            String labelStr = JsonUtil.toJson(labels);
                            if (content.getValues() != null && !content.getValues().isEmpty()) {
                                List<Value> valueList = instanceValuesMap.computeIfAbsent(labelStr,
                                        k -> new LinkedList<>());
                                if (valueList.size() == content.getValues().size()) {
                                    for (int timestampIndex = 0; timestampIndex < valueList.size(); timestampIndex++) {
                                        Value value = valueList.get(timestampIndex);
                                        Object[] valueArr = content.getValues().get(timestampIndex);
                                        String avgValue = new BigDecimal(String.valueOf(valueArr[1])).setScale(4,
                                                RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
                                        value.setMean(avgValue);
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("query metrics data from victoria-metrics error. {}.", e.getMessage(), e);
            }
        }
        if (metric.equals("in_octets") || metric.equals("out_octets") || metric.equals("receive_bytes") || metric.equals("transmit_bytes")) {
            for (Map.Entry<String, List<Value>> entry : instanceValuesMap.entrySet()) {
                String key = entry.getKey(); // 获取 Map 的键
                List<Value> valueList = entry.getValue(); // 获取对应的值（List<Value>）
                for (int i = 0; i < valueList.size(); i++) {
                    Value currentValue = valueList.get(i);
                    double newOrigin = Double.parseDouble(currentValue.getOrigin()) / 60;
                    String formattedIn = String.format("%.4f", newOrigin);
                    currentValue.setOrigin(formattedIn);
                    if (currentValue.getMean() != null) {
                        double newMean = Double.parseDouble(currentValue.getMean()) / 60;
                        String formattedMean = String.format("%.4f", newMean);
                        currentValue.setMean(formattedMean);
                    }
                    if (currentValue.getMax() != null) {
                        double newMax = Double.parseDouble(currentValue.getMax()) / 60;
                        String formattedMax = String.format("%.4f", newMax);
                        currentValue.setMax(formattedMax);
                    }
                    if (currentValue.getMin() != null) {
                        double newMin = Double.parseDouble(currentValue.getMin()) / 60;
                        String formattedMin = String.format("%.4f", newMin);
                        currentValue.setMin(formattedMin);
                    }
                }
            }
        }

        if (metric.equals("interface")) {
            for (Map.Entry<String, List<Value>> entry : instanceValuesMap.entrySet()) {
                String key = entry.getKey(); // 获取 Map 的键
                List<Value> valueList = entry.getValue(); // 获取对应的值（List<Value>）
                if (!valueList.isEmpty()) {
                    for (int i = 0; i < valueList.size(); i++) {
                        Value currentValue = valueList.get(i);
                        if (currentValue.getOrigin() != null) {
                            double inOrigin = Double.parseDouble(currentValue.getOrigin()) / 60;
                            String formattedIn = String.format("%.4f", inOrigin);
                            currentValue.setOrigin(formattedIn);
                        } else {
                            double outOrigin = Double.parseDouble(currentValue.getOutigin()) / 60;
                            String formattedIn = String.format("%.4f", outOrigin);
                            currentValue.setOutigin(formattedIn);
                        }
                        if (currentValue.getMean() != null) {
                            double newMean = Double.parseDouble(currentValue.getMean()) / 60;
                            String formattedMean = String.format("%.4f", newMean);
                            currentValue.setMean(formattedMean);
                        }
                        if (currentValue.getMax() != null) {
                            double newMax = Double.parseDouble(currentValue.getMax()) / 60;
                            String formattedMax = String.format("%.4f", newMax);
                            currentValue.setMax(formattedMax);
                        }
                        if (currentValue.getMin() != null) {
                            double newMin = Double.parseDouble(currentValue.getMin()) / 60;
                            String formattedMin = String.format("%.4f", newMin);
                            currentValue.setMin(formattedMin);
                        }
                    }
                }
            }
        }

        return instanceValuesMap;
    }

    @Override
    public Map<String, List<Value>> getHistoryIntervalMetricData(Long monitorId, String app, String metrics, String metric, String label, String history, String step) {
        if (!serverAvailable) {
            log.error("""
                    
                    \t---------------VictoriaMetrics Init Failed---------------
                    \t--------------Please Config VictoriaMetrics--------------
                    \t----------Can Not Use Metric History Now----------
                    """);
            return Collections.emptyMap();
        }
        long endTime = ZonedDateTime.now().toEpochSecond();
        long startTime;
        try {
            if (NumberUtils.isParsable(history)) {
                startTime = NumberUtils.toLong(history);
                startTime = (ZonedDateTime.now().toEpochSecond() - startTime);
            } else {
                TemporalAmount temporalAmount = TimePeriodUtil.parseTokenTime(history);
                ZonedDateTime dateTime = ZonedDateTime.now().minus(temporalAmount);
                startTime = dateTime.toEpochSecond();
            }
        } catch (Exception e) {
            log.error("history time error: {}. use default: 6h", e.getMessage());
            ZonedDateTime dateTime = ZonedDateTime.now().minus(Duration.ofHours(6));
            startTime = dateTime.toEpochSecond();
        }
        String labelName = metrics + SPILT + metric;
        if (CommonConstants.PROMETHEUS.equals(app)) {
            labelName = metrics;
        }

        Map<String, List<Value>> instanceValuesMap = new HashMap<>(8);
        Set<String> instances = new HashSet<>(8);
        if (label != null) {
            instances.add(label);
        }
        if (instances.isEmpty()) {
            instances.add("");
        }
        if (metric.equals("interface")) {
            for (String instanceValue : instances) {
                // 处理入站流量
                if (app.equals("linux")) {
                    labelName = metrics + SPILT + "(receive_bytes|transmit_bytes)";
                } else {
                    labelName = metrics + SPILT + "(in_octets|out_octets)";
                }

                String timeSeriesSelector =
                        LABEL_KEY_NAME + "=~\"" + labelName + "\"" + "," + LABEL_KEY_INSTANCE + "=\"" + monitorId + "\"" + ","
                                + LABEL_KEY_INSTANCE_NAME + "=\"" + instanceValue + "\"";
                try {
                    // 查询出站流量数据
                    queryAndProcessMetrics(timeSeriesSelector, startTime, endTime, instanceValuesMap, false, instanceValue);

                } catch (Exception e) {
                    log.error("query metrics data from victoria-metrics error. {}.", e.getMessage(), e);
                }
            }
        } else {
            String timeSeriesSelector =
                    LABEL_KEY_NAME + "=\"" + labelName + "\"" + "," + LABEL_KEY_INSTANCE + "=\"" + monitorId + "\"" + ","
                            + MONITOR_METRIC_KEY + "=\"" + metric + "\"";
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                headers.setAccept(List.of(MediaType.APPLICATION_JSON));
                if (StringUtils.hasText(vmClusterProps.getUsername()) && StringUtils.hasText(vmClusterProps.getPassword())) {
                    String authStr = vmClusterProps.getUsername() + ":" + vmClusterProps.getPassword();
                    String encodedAuth = Base64Util.encode(authStr);
                    headers.add(HttpHeaders.AUTHORIZATION, NetworkConstants.BASIC
                            + SignConstants.BLANK + encodedAuth);
                }
                HttpEntity<Void> httpEntity = new HttpEntity<>(headers);
                URI uri = UriComponentsBuilder.fromHttpUrl(vmClusterProps.getSUrl() + QUERY_RANGE_PATH)
                        .queryParam(URLEncoder.encode("query", StandardCharsets.UTF_8),
                                URLEncoder.encode("{" + timeSeriesSelector + "}", StandardCharsets.UTF_8))
                        .queryParam("step", step).queryParam("start", startTime).queryParam("end", endTime).build(true)
                        .toUri();
                ResponseEntity<PromQlQueryContent> responseEntity = restTemplate.exchange(uri, HttpMethod.GET,
                        httpEntity, PromQlQueryContent.class);
                if (responseEntity.getStatusCode().is2xxSuccessful()) {
                    log.debug("query metrics data from victoria-metrics success. {}", uri);
                    if (responseEntity.getBody() != null && responseEntity.getBody().getData() != null
                            && responseEntity.getBody().getData().getResult() != null) {
                        List<PromQlQueryContent.ContentData.Content> contents = responseEntity.getBody().getData()
                                .getResult();
                        for (PromQlQueryContent.ContentData.Content content : contents) {
                            Map<String, String> labels = content.getMetric();
                            labels.remove(LABEL_KEY_NAME);
                            labels.remove(LABEL_KEY_JOB);
                            labels.remove(LABEL_KEY_INSTANCE);
                            labels.remove(MONITOR_METRICS_KEY);
                            labels.remove(MONITOR_METRIC_KEY);
                            String labelStr = JsonUtil.toJson(labels);
                            if (content.getValues() != null && !content.getValues().isEmpty()) {
                                List<Value> valueList = instanceValuesMap.computeIfAbsent(labelStr,
                                        k -> new LinkedList<>());
                                for (Object[] valueArr : content.getValues()) {
                                    long timestamp = Long.parseLong(String.valueOf(valueArr[0]));
                                    String value = new BigDecimal(String.valueOf(valueArr[1])).setScale(4,
                                            RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
                                    // read timestamp here is s unit
                                    valueList.add(new Value(value, timestamp * 1000));
                                }
                            }
                        }
                    }
                } else {
                    log.error("query metrics data from victoria-metrics failed. {}", responseEntity);
                }

                // max
                uri = UriComponentsBuilder.fromHttpUrl(vmClusterProps.getSUrl() + QUERY_RANGE_PATH)
                        .queryParam(URLEncoder.encode("query", StandardCharsets.UTF_8),
                                URLEncoder.encode("max_over_time({" + timeSeriesSelector + "})", StandardCharsets.UTF_8))
                        .queryParam("step", step).queryParam("start", startTime).queryParam("end", endTime).build(true)
                        .toUri();
                responseEntity = restTemplate.exchange(uri, HttpMethod.GET, httpEntity, PromQlQueryContent.class);
                if (responseEntity.getStatusCode().is2xxSuccessful()) {
                    if (responseEntity.getBody() != null && responseEntity.getBody().getData() != null
                            && responseEntity.getBody().getData().getResult() != null) {
                        List<PromQlQueryContent.ContentData.Content> contents = responseEntity.getBody().getData()
                                .getResult();
                        for (PromQlQueryContent.ContentData.Content content : contents) {
                            Map<String, String> labels = content.getMetric();
                            labels.remove(LABEL_KEY_NAME);
                            labels.remove(LABEL_KEY_JOB);
                            labels.remove(LABEL_KEY_INSTANCE);
                            labels.remove(MONITOR_METRICS_KEY);
                            labels.remove(MONITOR_METRIC_KEY);
                            String labelStr = JsonUtil.toJson(labels);
                            if (content.getValues() != null && !content.getValues().isEmpty()) {
                                List<Value> valueList = instanceValuesMap.computeIfAbsent(labelStr,
                                        k -> new LinkedList<>());
                                if (valueList.size() == content.getValues().size()) {
                                    for (int timestampIndex = 0; timestampIndex < valueList.size(); timestampIndex++) {
                                        Value value = valueList.get(timestampIndex);
                                        Object[] valueArr = content.getValues().get(timestampIndex);
                                        String maxValue = new BigDecimal(String.valueOf(valueArr[1])).setScale(4,
                                                RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
                                        value.setMax(maxValue);
                                    }
                                }
                            }
                        }
                    }
                }
                // min
                uri = UriComponentsBuilder.fromHttpUrl(vmClusterProps.getSUrl() + QUERY_RANGE_PATH)
                        .queryParam(URLEncoder.encode("query", StandardCharsets.UTF_8),
                                URLEncoder.encode("min_over_time({" + timeSeriesSelector + "})", StandardCharsets.UTF_8))
                        .queryParam("step", step).queryParam("start", startTime).queryParam("end", endTime).build(true)
                        .toUri();
                responseEntity = restTemplate.exchange(uri, HttpMethod.GET, httpEntity, PromQlQueryContent.class);
                if (responseEntity.getStatusCode().is2xxSuccessful()) {
                    if (responseEntity.getBody() != null && responseEntity.getBody().getData() != null
                            && responseEntity.getBody().getData().getResult() != null) {
                        List<PromQlQueryContent.ContentData.Content> contents = responseEntity.getBody().getData()
                                .getResult();
                        for (PromQlQueryContent.ContentData.Content content : contents) {
                            Map<String, String> labels = content.getMetric();
                            labels.remove(LABEL_KEY_NAME);
                            labels.remove(LABEL_KEY_JOB);
                            labels.remove(LABEL_KEY_INSTANCE);
                            labels.remove(MONITOR_METRICS_KEY);
                            labels.remove(MONITOR_METRIC_KEY);
                            String labelStr = JsonUtil.toJson(labels);
                            if (content.getValues() != null && !content.getValues().isEmpty()) {
                                List<Value> valueList = instanceValuesMap.computeIfAbsent(labelStr,
                                        k -> new LinkedList<>());
                                if (valueList.size() == content.getValues().size()) {
                                    for (int timestampIndex = 0; timestampIndex < valueList.size(); timestampIndex++) {
                                        Value value = valueList.get(timestampIndex);
                                        Object[] valueArr = content.getValues().get(timestampIndex);
                                        String minValue = new BigDecimal(String.valueOf(valueArr[1])).setScale(4,
                                                RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
                                        value.setMin(minValue);
                                    }
                                }
                            }
                        }
                    }
                }
                // avg
                uri = UriComponentsBuilder.fromHttpUrl(vmClusterProps.getSUrl() + QUERY_RANGE_PATH)
                        .queryParam(URLEncoder.encode("query", StandardCharsets.UTF_8),
                                URLEncoder.encode("avg_over_time({" + timeSeriesSelector + "})", StandardCharsets.UTF_8))
                        .queryParam("step", step).queryParam("start", startTime).queryParam("end", endTime).build(true)
                        .toUri();
                responseEntity = restTemplate.exchange(uri, HttpMethod.GET, httpEntity, PromQlQueryContent.class);
                if (responseEntity.getStatusCode().is2xxSuccessful()) {
                    if (responseEntity.getBody() != null && responseEntity.getBody().getData() != null
                            && responseEntity.getBody().getData().getResult() != null) {
                        List<PromQlQueryContent.ContentData.Content> contents = responseEntity.getBody().getData()
                                .getResult();
                        for (PromQlQueryContent.ContentData.Content content : contents) {
                            Map<String, String> labels = content.getMetric();
                            labels.remove(LABEL_KEY_NAME);
                            labels.remove(LABEL_KEY_JOB);
                            labels.remove(LABEL_KEY_INSTANCE);
                            labels.remove(MONITOR_METRICS_KEY);
                            labels.remove(MONITOR_METRIC_KEY);
                            String labelStr = JsonUtil.toJson(labels);
                            if (content.getValues() != null && !content.getValues().isEmpty()) {
                                List<Value> valueList = instanceValuesMap.computeIfAbsent(labelStr,
                                        k -> new LinkedList<>());
                                if (valueList.size() == content.getValues().size()) {
                                    for (int timestampIndex = 0; timestampIndex < valueList.size(); timestampIndex++) {
                                        Value value = valueList.get(timestampIndex);
                                        Object[] valueArr = content.getValues().get(timestampIndex);
                                        String avgValue = new BigDecimal(String.valueOf(valueArr[1])).setScale(4,
                                                RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
                                        value.setMean(avgValue);
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("query metrics data from victoria-metrics error. {}.", e.getMessage(), e);
            }
        }
        if (metric.equals("in_octets") || metric.equals("out_octets") || metric.equals("receive_bytes") || metric.equals("transmit_bytes")) {
            for (Map.Entry<String, List<Value>> entry : instanceValuesMap.entrySet()) {
                String key = entry.getKey(); // 获取 Map 的键
                List<Value> valueList = entry.getValue(); // 获取对应的值（List<Value>）
                for (int i = 0; i < valueList.size(); i++) {
                    Value currentValue = valueList.get(i);
                    double newOrigin = Double.parseDouble(currentValue.getOrigin()) / 60;
                    String formattedIn = String.format("%.4f", newOrigin);
                    currentValue.setOrigin(formattedIn);
                    if (currentValue.getMean() != null) {
                        double newMean = Double.parseDouble(currentValue.getMean()) / 60;
                        String formattedMean = String.format("%.4f", newMean);
                        currentValue.setMean(formattedMean);
                    }
                    if (currentValue.getMax() != null) {
                        double newMax = Double.parseDouble(currentValue.getMax()) / 60;
                        String formattedMax = String.format("%.4f", newMax);
                        currentValue.setMax(formattedMax);
                    }
                    if (currentValue.getMin() != null) {
                        double newMin = Double.parseDouble(currentValue.getMin()) / 60;
                        String formattedMin = String.format("%.4f", newMin);
                        currentValue.setMin(formattedMin);
                    }
                }
            }
        }

        if (metric.equals("interface")) {
            for (Map.Entry<String, List<Value>> entry : instanceValuesMap.entrySet()) {
                String key = entry.getKey(); // 获取 Map 的键
                List<Value> valueList = entry.getValue(); // 获取对应的值（List<Value>）
                if (!valueList.isEmpty()) {
                    for (int i = 0; i < valueList.size(); i++) {
                        Value currentValue = valueList.get(i);
                        if (currentValue.getOrigin() != null) {
                            double inOrigin = Double.parseDouble(currentValue.getOrigin()) / 60;
                            String formattedIn = String.format("%.4f", inOrigin);
                            currentValue.setOrigin(formattedIn);
                        } else {
                            double outOrigin = Double.parseDouble(currentValue.getOutigin()) / 60;
                            String formattedIn = String.format("%.4f", outOrigin);
                            currentValue.setOutigin(formattedIn);
                        }
                        if (currentValue.getMean() != null) {
                            double newMean = Double.parseDouble(currentValue.getMean()) / 60;
                            String formattedMean = String.format("%.4f", newMean);
                            currentValue.setMean(formattedMean);
                        }
                        if (currentValue.getMax() != null) {
                            double newMax = Double.parseDouble(currentValue.getMax()) / 60;
                            String formattedMax = String.format("%.4f", newMax);
                            currentValue.setMax(formattedMax);
                        }
                        if (currentValue.getMin() != null) {
                            double newMin = Double.parseDouble(currentValue.getMin()) / 60;
                            String formattedMin = String.format("%.4f", newMin);
                            currentValue.setMin(formattedMin);
                        }
                    }
                }
            }
        }

        return instanceValuesMap;
    }

    @Override
    public Map<String, List<Value>> getHistoryIntervalMetricData(Long monitorId, String app, String metrics, String metric, String label, String history, Long start, Long end, String step, String type) {
        if (!serverAvailable) {
            log.error("""
                    
                    \t---------------VictoriaMetrics Init Failed---------------
                    \t--------------Please Config VictoriaMetrics--------------
                    \t----------Can Not Use Metric History Now----------
                    """);
            return Collections.emptyMap();
        }

        // 使用传入的start和end时间，如果为null则使用默认逻辑
        long startTime;
        long endTime;

        if (start != null && end != null) {
            startTime = start;
            endTime = end;
        } else {
            // 如果start和end为null，则使用history参数计算时间范围
            endTime = ZonedDateTime.now().toEpochSecond();
            try {
                if (NumberUtils.isParsable(history)) {
                    startTime = NumberUtils.toLong(history);
                    startTime = (ZonedDateTime.now().toEpochSecond() - startTime);
                } else {
                    TemporalAmount temporalAmount = TimePeriodUtil.parseTokenTime(history);
                    ZonedDateTime dateTime = ZonedDateTime.now().minus(temporalAmount);
                    startTime = dateTime.toEpochSecond();
                }
            } catch (Exception e) {
                log.error("history time error: {}. use default: 6h", e.getMessage());
                ZonedDateTime dateTime = ZonedDateTime.now().minus(Duration.ofHours(6));
                startTime = dateTime.toEpochSecond();
            }
        }

        String labelName = metrics + SPILT + metric;
        if (CommonConstants.PROMETHEUS.equals(app)) {
            labelName = metrics;
        }

        Map<String, List<Value>> instanceValuesMap = new HashMap<>(8);
        Set<String> instances = new HashSet<>(8);
        if (label != null) {
            instances.add(label);
        }
        if (instances.isEmpty()) {
            instances.add("");
        }

        if (metric.equals("interface")) {
            for (String instanceValue : instances) {
                // 处理入站流量
                if (app.equals("linux")) {
                    labelName = metrics + SPILT + "(receive_bytes|transmit_bytes)";
                } else {
                    labelName = metrics + SPILT + "(in_octets|out_octets)";
                }

                String timeSeriesSelector =
                        LABEL_KEY_NAME + "=~\"" + labelName + "\"" + "," + LABEL_KEY_INSTANCE + "=\"" + monitorId + "\"" + ","
                                + LABEL_KEY_INSTANCE_NAME + "=\"" + instanceValue + "\"";
                try {
                    // 查询出站流量数据
                    queryAndProcessMetrics(timeSeriesSelector, startTime, endTime, instanceValuesMap, false, instanceValue);

                } catch (Exception e) {
                    log.error("query metrics data from victoria-metrics error. {}.", e.getMessage(), e);
                }
            }
        } else {
            String timeSeriesSelector =
                    LABEL_KEY_NAME + "=\"" + labelName + "\"" + "," + LABEL_KEY_INSTANCE + "=\"" + monitorId + "\"" + ","
                            + MONITOR_METRIC_KEY + "=\"" + metric + "\"";
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                headers.setAccept(List.of(MediaType.APPLICATION_JSON));
                if (StringUtils.hasText(vmClusterProps.getUsername()) && StringUtils.hasText(vmClusterProps.getPassword())) {
                    String authStr = vmClusterProps.getUsername() + ":" + vmClusterProps.getPassword();
                    String encodedAuth = Base64Util.encode(authStr);
                    headers.add(HttpHeaders.AUTHORIZATION, NetworkConstants.BASIC
                            + SignConstants.BLANK + encodedAuth);
                }
                HttpEntity<Void> httpEntity = new HttpEntity<>(headers);
                String query;
                if (type == null) {
                    query = "{" + timeSeriesSelector + "}";
                } else {
                    query = switch (type) {
                        case "avg" -> "avg_over_time({" + timeSeriesSelector + "})";
                        case "min" -> "min_over_time({" + timeSeriesSelector + "})";
                        case "max" -> "max_over_time({" + timeSeriesSelector + "})";
                        default -> "{" + timeSeriesSelector + "}";
                    };
                }
                URI uri = UriComponentsBuilder.fromHttpUrl(vmClusterProps.getSUrl() + QUERY_RANGE_PATH)
                        .queryParam(URLEncoder.encode("query", StandardCharsets.UTF_8),
                                URLEncoder.encode(query, StandardCharsets.UTF_8))
                        .queryParam("step", step).queryParam("start", startTime).queryParam("end", endTime).build(true)
                        .toUri();
                ResponseEntity<PromQlQueryContent> responseEntity = restTemplate.exchange(uri, HttpMethod.GET,
                        httpEntity, PromQlQueryContent.class);
                if (responseEntity.getStatusCode().is2xxSuccessful()) {
                    log.debug("query metrics data from victoria-metrics success. {}", uri);
                    if (responseEntity.getBody() != null && responseEntity.getBody().getData() != null
                            && responseEntity.getBody().getData().getResult() != null) {
                        List<PromQlQueryContent.ContentData.Content> contents = responseEntity.getBody().getData()
                                .getResult();
                        for (PromQlQueryContent.ContentData.Content content : contents) {
                            Map<String, String> labels = content.getMetric();
                            labels.remove(LABEL_KEY_NAME);
                            labels.remove(LABEL_KEY_JOB);
                            labels.remove(LABEL_KEY_INSTANCE);
                            labels.remove(MONITOR_METRICS_KEY);
                            labels.remove(MONITOR_METRIC_KEY);
                            String labelStr = JsonUtil.toJson(labels);
                            if (content.getValues() != null && !content.getValues().isEmpty()) {
                                List<Value> valueList = instanceValuesMap.computeIfAbsent(labelStr,
                                        k -> new LinkedList<>());
                                for (Object[] valueArr : content.getValues()) {
                                    long timestamp = Long.parseLong(String.valueOf(valueArr[0]));
                                    String value = new BigDecimal(String.valueOf(valueArr[1])).setScale(4,
                                            RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
                                    // read timestamp here is s unit
                                    valueList.add(new Value(value, timestamp * 1000));
                                }
                            }
                        }
                    }
                } else {
                    log.error("query metrics data from victoria-metrics failed. {}", responseEntity);
                }
                if (type == null) {
                    // max
                    uri = UriComponentsBuilder.fromHttpUrl(vmClusterProps.getSUrl() + QUERY_RANGE_PATH)
                            .queryParam(URLEncoder.encode("query", StandardCharsets.UTF_8),
                                    URLEncoder.encode("max_over_time({" + timeSeriesSelector + "})", StandardCharsets.UTF_8))
                            .queryParam("step", step).queryParam("start", startTime).queryParam("end", endTime).build(true)
                            .toUri();
                    responseEntity = restTemplate.exchange(uri, HttpMethod.GET, httpEntity, PromQlQueryContent.class);
                    if (responseEntity.getStatusCode().is2xxSuccessful()) {
                        if (responseEntity.getBody() != null && responseEntity.getBody().getData() != null
                                && responseEntity.getBody().getData().getResult() != null) {
                            List<PromQlQueryContent.ContentData.Content> contents = responseEntity.getBody().getData()
                                    .getResult();
                            for (PromQlQueryContent.ContentData.Content content : contents) {
                                Map<String, String> labels = content.getMetric();
                                labels.remove(LABEL_KEY_NAME);
                                labels.remove(LABEL_KEY_JOB);
                                labels.remove(LABEL_KEY_INSTANCE);
                                labels.remove(MONITOR_METRICS_KEY);
                                labels.remove(MONITOR_METRIC_KEY);
                                String labelStr = JsonUtil.toJson(labels);
                                if (content.getValues() != null && !content.getValues().isEmpty()) {
                                    List<Value> valueList = instanceValuesMap.computeIfAbsent(labelStr,
                                            k -> new LinkedList<>());

                                    //获取content.getValues()相同时间戳的value值赋值到valueList
                                    // 将 content.getValues() 转为时间戳 -> 值 映射
                                    Map<Long, String> timestampToMaxValue = new HashMap<>();
                                    for (Object[] valueArr : content.getValues()) {
                                        long timestamp = Long.parseLong(String.valueOf(valueArr[0]));
                                        String maxValue = new BigDecimal(String.valueOf(valueArr[1]))
                                                .setScale(4, RoundingMode.HALF_UP)
                                                .stripTrailingZeros()
                                                .toPlainString();
                                        timestampToMaxValue.put(timestamp * 1000, maxValue);
                                    }

                                    // 遍历 valueList，根据时间戳设置最小值
                                    for (Value value : valueList) {
                                        String maxValue = timestampToMaxValue.get(value.getTime());
                                        if (maxValue != null) {
                                            value.setMax(maxValue);
                                        }
                                    }

                                }
                            }
                        }
                    }
                    // min
                    uri = UriComponentsBuilder.fromHttpUrl(vmClusterProps.getSUrl() + QUERY_RANGE_PATH)
                            .queryParam(URLEncoder.encode("query", StandardCharsets.UTF_8),
                                    URLEncoder.encode("min_over_time({" + timeSeriesSelector + "})", StandardCharsets.UTF_8))
                            .queryParam("step", step).queryParam("start", startTime).queryParam("end", endTime).build(true)
                            .toUri();
                    responseEntity = restTemplate.exchange(uri, HttpMethod.GET, httpEntity, PromQlQueryContent.class);
                    if (responseEntity.getStatusCode().is2xxSuccessful()) {
                        if (responseEntity.getBody() != null && responseEntity.getBody().getData() != null
                                && responseEntity.getBody().getData().getResult() != null) {
                            List<PromQlQueryContent.ContentData.Content> contents = responseEntity.getBody().getData()
                                    .getResult();
                            for (PromQlQueryContent.ContentData.Content content : contents) {
                                Map<String, String> labels = content.getMetric();
                                labels.remove(LABEL_KEY_NAME);
                                labels.remove(LABEL_KEY_JOB);
                                labels.remove(LABEL_KEY_INSTANCE);
                                labels.remove(MONITOR_METRICS_KEY);
                                labels.remove(MONITOR_METRIC_KEY);
                                String labelStr = JsonUtil.toJson(labels);
                                if (content.getValues() != null && !content.getValues().isEmpty()) {
                                    List<Value> valueList = instanceValuesMap.computeIfAbsent(labelStr,
                                            k -> new LinkedList<>());
                                    // 将 content.getValues() 转为时间戳 -> 值 映射
                                    Map<Long, String> timestampToMinValue = new HashMap<>();
                                    for (Object[] valueArr : content.getValues()) {
                                        long timestamp = Long.parseLong(String.valueOf(valueArr[0]));
                                        String minValue = new BigDecimal(String.valueOf(valueArr[1]))
                                                .setScale(4, RoundingMode.HALF_UP)
                                                .stripTrailingZeros()
                                                .toPlainString();
                                        timestampToMinValue.put(timestamp * 1000, minValue);
                                    }

                                    // 遍历 valueList，根据时间戳设置最小值
                                    for (Value value : valueList) {
                                        String minValue = timestampToMinValue.get(value.getTime());
                                        if (minValue != null) {
                                            value.setMin(minValue);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    // avg
                    uri = UriComponentsBuilder.fromHttpUrl(vmClusterProps.getSUrl() + QUERY_RANGE_PATH)
                            .queryParam(URLEncoder.encode("query", StandardCharsets.UTF_8),
                                    URLEncoder.encode("avg_over_time({" + timeSeriesSelector + "})", StandardCharsets.UTF_8))
                            .queryParam("step", step).queryParam("start", startTime).queryParam("end", endTime).build(true)
                            .toUri();
                    responseEntity = restTemplate.exchange(uri, HttpMethod.GET, httpEntity, PromQlQueryContent.class);
                    if (responseEntity.getStatusCode().is2xxSuccessful()) {
                        if (responseEntity.getBody() != null && responseEntity.getBody().getData() != null
                                && responseEntity.getBody().getData().getResult() != null) {
                            List<PromQlQueryContent.ContentData.Content> contents = responseEntity.getBody().getData()
                                    .getResult();
                            for (PromQlQueryContent.ContentData.Content content : contents) {
                                Map<String, String> labels = content.getMetric();
                                labels.remove(LABEL_KEY_NAME);
                                labels.remove(LABEL_KEY_JOB);
                                labels.remove(LABEL_KEY_INSTANCE);
                                labels.remove(MONITOR_METRICS_KEY);
                                labels.remove(MONITOR_METRIC_KEY);
                                String labelStr = JsonUtil.toJson(labels);
                                if (content.getValues() != null && !content.getValues().isEmpty()) {
                                    List<Value> valueList = instanceValuesMap.computeIfAbsent(labelStr,
                                            k -> new LinkedList<>());
                                    // 将 content.getValues() 转为时间戳 -> 值 映射
                                    Map<Long, String> timestampToAvgValue = new HashMap<>();
                                    for (Object[] valueArr : content.getValues()) {
                                        long timestamp = Long.parseLong(String.valueOf(valueArr[0]));
                                        String avgValue = new BigDecimal(String.valueOf(valueArr[1]))
                                                .setScale(4, RoundingMode.HALF_UP)
                                                .stripTrailingZeros()
                                                .toPlainString();
                                        timestampToAvgValue.put(timestamp * 1000, avgValue);
                                    }

                                    // 遍历 valueList，根据时间戳设置最小值
                                    for (Value value : valueList) {
                                        String avgValue = timestampToAvgValue.get(value.getTime());
                                        if (avgValue != null) {
                                            value.setMean(avgValue);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("query metrics data from victoria-metrics error. {}.", e.getMessage(), e);
            }
        }

        // 处理网络流量指标的单位转换
        if (metric.equals("in_octets") || metric.equals("out_octets") || metric.equals("receive_bytes") || metric.equals("transmit_bytes")) {
            for (Map.Entry<String, List<Value>> entry : instanceValuesMap.entrySet()) {
                List<Value> valueList = entry.getValue();
                for (Value currentValue : valueList) {
                    double newOrigin = Double.parseDouble(currentValue.getOrigin()) / 60;
                    String formattedIn = String.format("%.4f", newOrigin);
                    currentValue.setOrigin(formattedIn);
                    if (currentValue.getMean() != null) {
                        double newMean = Double.parseDouble(currentValue.getMean()) / 60;
                        String formattedMean = String.format("%.4f", newMean);
                        currentValue.setMean(formattedMean);
                    }
                    if (currentValue.getMax() != null) {
                        double newMax = Double.parseDouble(currentValue.getMax()) / 60;
                        String formattedMax = String.format("%.4f", newMax);
                        currentValue.setMax(formattedMax);
                    }
                    if (currentValue.getMin() != null) {
                        double newMin = Double.parseDouble(currentValue.getMin()) / 60;
                        String formattedMin = String.format("%.4f", newMin);
                        currentValue.setMin(formattedMin);
                    }
                }
            }
        }

        // 处理interface指标的特殊逻辑
        if (metric.equals("interface")) {
            for (Map.Entry<String, List<Value>> entry : instanceValuesMap.entrySet()) {
                List<Value> valueList = entry.getValue();
                if (!valueList.isEmpty()) {
                    for (Value currentValue : valueList) {
                        if (currentValue.getOrigin() != null) {
                            double inOrigin = Double.parseDouble(currentValue.getOrigin()) / 60;
                            String formattedIn = String.format("%.4f", inOrigin);
                            currentValue.setOrigin(formattedIn);
                        } else {
                            double outOrigin = Double.parseDouble(currentValue.getOutigin()) / 60;
                            String formattedIn = String.format("%.4f", outOrigin);
                            currentValue.setOutigin(formattedIn);
                        }
                        if (currentValue.getMean() != null) {
                            double newMean = Double.parseDouble(currentValue.getMean()) / 60;
                            String formattedMean = String.format("%.4f", newMean);
                            currentValue.setMean(formattedMean);
                        }
                        if (currentValue.getMax() != null) {
                            double newMax = Double.parseDouble(currentValue.getMax()) / 60;
                            String formattedMax = String.format("%.4f", newMax);
                            currentValue.setMax(formattedMax);
                        }
                        if (currentValue.getMin() != null) {
                            double newMin = Double.parseDouble(currentValue.getMin()) / 60;
                            String formattedMin = String.format("%.4f", newMin);
                            currentValue.setMin(formattedMin);
                        }
                    }
                }
            }
        }

        return instanceValuesMap;
    }

    private void queryAndProcessMetrics(String timeSeriesSelector, long startTime, long endTime,
                                        Map<String, List<Value>> instanceValuesMap, boolean isInbound, String instanceValue) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(List.of(MediaType.APPLICATION_JSON));

        if (StringUtils.hasText(vmClusterProps.getUsername()) && StringUtils.hasText(vmClusterProps.getPassword())) {
            String authStr = vmClusterProps.getUsername() + ":" + vmClusterProps.getPassword();
            String encodedAuth = Base64Util.encode(authStr);
            headers.add(HttpHeaders.AUTHORIZATION, NetworkConstants.BASIC + SignConstants.BLANK + encodedAuth);
        }

        HttpEntity<Void> httpEntity = new HttpEntity<>(headers);

        // 查询原始值（FIRST等效）
        queryMetricValues(timeSeriesSelector, startTime, endTime, httpEntity, instanceValuesMap, isInbound, instanceValue);

        // 查询最大值
        queryAggregatedValues(timeSeriesSelector, "max_over_time", startTime, endTime, httpEntity,
                instanceValuesMap, value -> value::setMax, instanceValue);

        // 查询最小值
        queryAggregatedValues(timeSeriesSelector, "min_over_time", startTime, endTime, httpEntity,
                instanceValuesMap, value -> value::setMin, instanceValue);

        // 查询平均值
        queryAggregatedValues(timeSeriesSelector, "avg_over_time", startTime, endTime, httpEntity,
                instanceValuesMap, value -> value::setMean, instanceValue);
    }


    private void queryMetricValues(String timeSeriesSelector, long startTime, long endTime,
                                   HttpEntity<Void> httpEntity, Map<String, List<Value>> instanceValuesMap,
                                   boolean isInbound, String instanceValue) {
        try {
            URI uri = UriComponentsBuilder.fromHttpUrl(vmClusterProps.getSUrl() + QUERY_RANGE_PATH)
                    .queryParam(URLEncoder.encode("query", StandardCharsets.UTF_8),
                            URLEncoder.encode("{" + timeSeriesSelector + "}", StandardCharsets.UTF_8))
                    .queryParam("step", "4h")
                    .queryParam("start", startTime)
                    .queryParam("end", endTime)
                    .build(true)
                    .toUri();

            ResponseEntity<PromQlQueryContent> responseEntity = restTemplate.exchange(uri, HttpMethod.GET,
                    httpEntity, PromQlQueryContent.class);

            if (responseEntity.getStatusCode().is2xxSuccessful() && responseEntity.getBody() != null
                    && responseEntity.getBody().getData() != null
                    && responseEntity.getBody().getData().getResult() != null) {

                processQueryResults(responseEntity.getBody().getData().getResult(), instanceValuesMap, isInbound, instanceValue);
            } else {
                log.error("query metrics data from victoria-metrics failed. {}", responseEntity);
            }
        } catch (Exception e) {
            log.error("Query metric values failed: {}", e.getMessage(), e);
        }
    }

    private void queryAggregatedValues(String timeSeriesSelector, String aggregation, long startTime,
                                       long endTime, HttpEntity<Void> httpEntity,
                                       Map<String, List<Value>> instanceValuesMap,
                                       Function<Value, Consumer<String>> valueSetter, String instanceValue) {
        try {
            URI uri = UriComponentsBuilder.fromHttpUrl(vmClusterProps.getSUrl() + QUERY_RANGE_PATH)
                    .queryParam(URLEncoder.encode("query", StandardCharsets.UTF_8),
                            URLEncoder.encode(aggregation + "({" + timeSeriesSelector + "})", StandardCharsets.UTF_8))
                    .queryParam("step", "4h")
                    .queryParam("start", startTime)
                    .queryParam("end", endTime)
                    .build(true)
                    .toUri();

            ResponseEntity<PromQlQueryContent> responseEntity = restTemplate.exchange(uri,
                    HttpMethod.GET, httpEntity, PromQlQueryContent.class);

            if (responseEntity.getStatusCode().is2xxSuccessful() && responseEntity.getBody() != null
                    && responseEntity.getBody().getData() != null
                    && responseEntity.getBody().getData().getResult() != null) {

                List<PromQlQueryContent.ContentData.Content> contents = responseEntity.getBody().getData().getResult();
                for (PromQlQueryContent.ContentData.Content content : contents) {
                    Map<String, String> labels = new HashMap<>(content.getMetric());
                    String metric = content.getMetric().get(MONITOR_METRIC_KEY);
                    labels.remove(LABEL_KEY_NAME);
                    labels.remove(LABEL_KEY_JOB);
                    labels.remove(LABEL_KEY_INSTANCE);
                    labels.remove(MONITOR_METRICS_KEY);
                    labels.remove(MONITOR_METRIC_KEY);

//                    String labelStr = JsonUtil.toJson(labels);
                    if (content.getValues() != null && !content.getValues().isEmpty()) {
                        List<Value> valueList = instanceValuesMap.get(instanceValue);
                        if (content.getValues() != null && !content.getValues().isEmpty()) {
                            if (valueList != null && valueList.size() == content.getValues().size()) {
                                for (int i = 0; i < valueList.size(); i++) {
                                    Value value = valueList.get(i);
                                    Object[] valueArr = content.getValues().get(i);
                                    String aggValue = new BigDecimal(String.valueOf(valueArr[1]))
                                            .setScale(4, RoundingMode.HALF_UP)
                                            .stripTrailingZeros()
                                            .toPlainString();
                                    valueSetter.apply(value).accept(aggValue);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Query {} values failed: {}", aggregation, e.getMessage(), e);
        }
    }


    private void processQueryResults(List<PromQlQueryContent.ContentData.Content> contents,
                                     Map<String, List<Value>> instanceValuesMap,
                                     boolean isInbound, String instanceValue) {
        for (PromQlQueryContent.ContentData.Content content : contents) {
            Map<String, String> labels = new HashMap<>(content.getMetric());
            labels.remove(LABEL_KEY_NAME);
            labels.remove(LABEL_KEY_JOB);
            labels.remove(LABEL_KEY_INSTANCE);
            labels.remove(MONITOR_METRICS_KEY);
            labels.remove(MONITOR_METRIC_KEY);

//            String labelStr = JsonUtil.toJson(labels.get("interface_name"));
            if (content.getValues() != null && !content.getValues().isEmpty()) {
                List<Value> valueList = instanceValuesMap.computeIfAbsent(instanceValue, k -> new LinkedList<>());
                for (Object[] valueArr : content.getValues()) {
                    String inOctets = content.getMetric().get(MONITOR_METRIC_KEY);
                    long timestamp = Long.parseLong(String.valueOf(valueArr[0]));
                    String value = new BigDecimal(String.valueOf(valueArr[1]))
                            .setScale(4, RoundingMode.HALF_UP)
                            .stripTrailingZeros()
                            .toPlainString();
                    if (inOctets.equals("in_octets") || inOctets.equals("receive_bytes")) {
                        valueList.add(new Value(value, timestamp * 1000));
                    } else if (inOctets.equals("out_octets") || inOctets.equals("transmit_bytes")) {
                        // 如果是出站流量，找到对应时间戳的记录并设置outOrigin
                        Value existingValue = valueList.stream()
                                .filter(v -> v.getTime() == timestamp * 1000)
                                .findFirst()
                                .orElseGet(() -> {
                                    Value v = new Value(null, value, timestamp * 1000);
                                    valueList.add(v);
                                    return v;
                                });
                        existingValue.setOutigin(value);
                    } else {
                        valueList.add(new Value(value, timestamp * 1000));
                    }
                }
            }
        }
    }


    /**
     * victoria metrics content
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static final class VictoriaMetricsContent {

        /**
         * metric contains metric name plus labels for a particular time series
         */
        private Map<String, String> metric;

        /**
         * values contains raw sample values for the given time series
         */
        private Double[] values;

        /**
         * timestamps contains raw sample UNIX timestamps in milliseconds for the given time series
         * every timestamp is associated with the value at the corresponding position
         */
        private Long[] timestamps;
    }
}
