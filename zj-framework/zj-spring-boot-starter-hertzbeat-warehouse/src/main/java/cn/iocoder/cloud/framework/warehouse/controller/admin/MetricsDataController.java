/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.warehouse.controller.admin;

import cn.iocoder.cloud.framework.warehouse.service.MetricsDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.hertzbeat.common.entity.dto.Message;
import org.apache.hertzbeat.common.entity.dto.MetricsData;
import org.apache.hertzbeat.common.entity.dto.MetricsHistoryData;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static org.apache.hertzbeat.common.constants.CommonConstants.FAIL_CODE;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

/**
 * 指标数据查询接口
 *
 * <AUTHOR>
 */

@RestController
@RequestMapping(path = "/cloudedge", produces = {APPLICATION_JSON_VALUE})
@Tag(name = "Metrics Data API | 监控指标数据API")
public class MetricsDataController {


    private static final Integer METRIC_FULL_LENGTH = 3;

    private final MetricsDataService metricsDataService;

    public MetricsDataController(MetricsDataService metricsDataService) {
        this.metricsDataService = metricsDataService;
    }

    @GetMapping("/api/warehouse/storage/status")
    @Operation(summary = "Query Warehouse Storage Server Status", description = "查询仓储下存储服务的可用性状态")
    public ResponseEntity<Message<Void>> getWarehouseStorageServerStatus() {
        Boolean status = metricsDataService.getWarehouseStorageServerStatus();
        if (Boolean.TRUE.equals(status)) {
            return ResponseEntity.ok(Message.success());
        }
        // historyDataReader does not exist or is not available
        return ResponseEntity.ok(Message.fail(FAIL_CODE, "Service not available!"));

    }

    @GetMapping("/api/monitor/{monitorId}/metrics/{metrics}")
    @Operation(summary = "Query Real Time Metrics Data", description = "查询监控指标的实时指标数据")
    public ResponseEntity<Message<MetricsData>> getMetricsData(
            @Parameter(description = "Monitor Id", example = "343254354")
            @PathVariable Long monitorId,
            @Parameter(description = "Metrics Name", example = "cpu")
            @PathVariable String metrics) {
        MetricsData metricsData = metricsDataService.getMetricsData(monitorId, metrics);
        if (metricsData == null) {
            return ResponseEntity.ok(Message.success("query metrics data is empty"));
        }
        return ResponseEntity.ok(Message.success(metricsData));

//        AbstractRealTimeDataStorage realTimeDataStorage = realTimeDataStorages.stream()
//                .filter(AbstractRealTimeDataStorage::isServerAvailable)
//                .max((o1, o2) -> {
//                    if (o1 instanceof RealTimeMemoryDataStorage) {
//                        return -1;
//                    } else if (o2 instanceof RealTimeMemoryDataStorage) {
//                        return 1;
//                    } else {
//                        return 0;
//                    }
//                }).orElse(null);
//        if (realTimeDataStorage == null) {
//            return ResponseEntity.ok(Message.fail(FAIL_CODE, "real time store not available"));
//        }
//        CollectRep.MetricsData storageData = realTimeDataStorage.getCurrentMetricsData(monitorId, metrics);
//        if (storageData == null) {
//            return ResponseEntity.ok(Message.success("query metrics data is empty"));
//        }
//        {
//            MetricsData.MetricsDataBuilder dataBuilder = MetricsData.builder();
//            dataBuilder.id(storageData.getId()).app(storageData.getApp()).metrics(storageData.getMetrics())
//                    .time(storageData.getTime());
//            List<Field> fields = storageData.getFieldsList().stream().map(tmpField ->
//                            Field.builder().name(tmpField.getName())
//                                    .type(Integer.valueOf(tmpField.getType()).byteValue())
//                                    .label(tmpField.getLabel())
//                                    .unit(tmpField.getUnit())
//                                    .build())
//                    .collect(Collectors.toList());
//            dataBuilder.fields(fields);
//            List<ValueRow> valueRows = new LinkedList<>();
//            for (CollectRep.ValueRow valueRow : storageData.getValuesList()) {
//                Map<String, String> labels = new HashMap<>(8);
//                List<Value> values = new LinkedList<>();
//                for (int i = 0; i < fields.size(); i++) {
//                    Field field = fields.get(i);
//                    String origin = valueRow.getColumns(i);
//                    if (CommonConstants.NULL_VALUE.equals(origin)) {
//                        values.add(new Value());
//                    } else {
//                        values.add(new Value(origin));
//                        if (field.getLabel()) {
//                            labels.put(field.getName(), origin);
//                        }
//                    }
//                }
//                valueRows.add(ValueRow.builder().labels(labels).values(values).build());
//            }
//            if (metrics.equals("interface")) {
//                // 老数据
//                CollectRep.MetricsData oldStorageData = realTimeDataStorage.getOldCurrentMetricsData(monitorId, metrics);
//                if (oldStorageData != null) {
//                    MetricsData.MetricsDataBuilder oldataBuilder = MetricsData.builder();
//                    oldataBuilder.id(oldStorageData.getId()).app(oldStorageData.getApp()).metrics(oldStorageData.getMetrics())
//                            .time(oldStorageData.getTime());
//                    List<Field> oldfields = oldStorageData.getFieldsList().stream().map(tmpField ->
//                                    Field.builder().name(tmpField.getName())
//                                            .type(Integer.valueOf(tmpField.getType()).byteValue())
//                                            .label(tmpField.getLabel())
//                                            .unit(tmpField.getUnit())
//                                            .build())
//                            .collect(Collectors.toList());
//                    oldataBuilder.fields(oldfields);
//                    List<ValueRow> oldValueRows = new LinkedList<>();
//                    for (CollectRep.ValueRow valueRow : oldStorageData.getValuesList()) {
//                        Map<String, String> labels = new HashMap<>(8);
//                        List<Value> values = new LinkedList<>();
//                        for (int i = 0; i < fields.size(); i++) {
//                            Field field = fields.get(i);
//                            String origin = valueRow.getColumns(i);
//                            if (CommonConstants.NULL_VALUE.equals(origin)) {
//                                values.add(new Value());
//                            } else {
//                                values.add(new Value(origin));
//                                if (field.getLabel()) {
//                                    labels.put(field.getName(), origin);
//                                }
//                            }
//                        }
//                        oldValueRows.add(ValueRow.builder().labels(labels).values(values).build());
//                    }
//                    for (ValueRow valueRow : valueRows) {
//                        for (ValueRow oldvalueRow : oldValueRows) {
//                            if (valueRow.getLabels().get("interface_name").equals(oldvalueRow.getLabels().get("interface_name"))) {
//
//                                // 端口入方向
//                                double t1 = 0;
//                                double t2 = 0;
//                                if (valueRow.getValues().get(6).getOrigin() != null) {
//                                    t1 = Double.parseDouble(valueRow.getValues().get(6).getOrigin());
//                                }
//                                if (oldvalueRow.getValues().get(6).getOrigin() != null) {
//                                    t2 = Double.parseDouble(oldvalueRow.getValues().get(6).getOrigin());
//                                }
//
//                                double in = 8 * (t1 - t2) / 60;
//
//                                // 端口出方向
//                                double t1out = 0;
//                                double t2out = 0;
//                                if (valueRow.getValues().get(9).getOrigin() != null) {
//                                    t1out = Double.parseDouble(valueRow.getValues().get(9).getOrigin());
//                                }
//                                if (oldvalueRow.getValues().get(9).getOrigin() != null) {
//                                    t2out = Double.parseDouble(oldvalueRow.getValues().get(9).getOrigin());
//                                }
//
//
//                                double out = 8 * (t1out - t2out) / 60;
//
//                                List<Value> values = valueRow.getValues();
//                                for (int i = 0; i < values.size(); i++) {
//                                    if (i == 6) {
//                                        if (in < 0) {
//                                            in = 0;
//                                        }
//                                        String formattedIn = String.format("%.4f", in);
//                                        values.add(6, new Value(formattedIn));
//                                    }
//                                    if (i == 9) {
//                                        if (out < 0) {
//                                            out = 0;
//                                        }
//                                        String formattedIn = String.format("%.4f", out);
//                                        values.add(9, new Value(formattedIn));
//                                    }
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//            dataBuilder.valueRows(valueRows);
//            return ResponseEntity.ok(Message.success(dataBuilder.build()));
//        }
    }

    @GetMapping("/api/monitor/{monitorId}/metric/{metricFull}")
    @Operation(summary = "查询监控的指定指标的历史数据", description = "查询监控下的指定指标的历史数据")
    public ResponseEntity<Message<MetricsHistoryData>> getMetricHistoryData(
            @Parameter(description = "监控任务ID", example = "343254354")
            @PathVariable Long monitorId,
            @Parameter(description = "监控指标全路径", example = "linux.cpu.usage")
            @PathVariable() String metricFull,
            @Parameter(description = "标签过滤,默认空", example = "disk2")
            @RequestParam(required = false) String label,
            @Parameter(description = "查询历史时间段,默认6h-6小时:s-秒、m-分, h-小时, d-天, w-周", example = "6h")
            @RequestParam(required = false) String history,
            @Parameter(description = "是否计算聚合数据,需查询时间段大于1周以上,默认不开启,聚合降样时间窗口默认为4小时", example = "false")
            @RequestParam(required = false) Boolean interval
    ) {
        if (!metricsDataService.getWarehouseStorageServerStatus()) {
            return ResponseEntity.ok(Message.fail(FAIL_CODE, "time series database not available"));
        }
        String[] names = metricFull.split("\\.");
        if (names.length != METRIC_FULL_LENGTH) {
            throw new IllegalArgumentException("metrics full name: " + metricFull + " is illegal.");
        }
        String app = names[0];
        String metrics = names[1];
        String metric = names[2];
        MetricsHistoryData historyData = metricsDataService.getMetricHistoryData(monitorId, app, metrics, metric, label, history, interval);
        return ResponseEntity.ok(Message.success(historyData));

//        AbstractHistoryDataStorage historyDataStorage = historyDataStorages.stream()
//                .filter(AbstractHistoryDataStorage::isServerAvailable).max((o1, o2) -> {
//                    if (o1 instanceof HistoryJpaDatabaseDataStorage) {
//                        return -1;
//                    } else if (o2 instanceof HistoryJpaDatabaseDataStorage) {
//                        return 1;
//                    } else {
//                        return 0;
//                    }
//                }).orElse(null);
//        if (historyDataStorage == null) {
//            return ResponseEntity.ok(Message.fail(FAIL_CODE, "time series database not available"));
//        }
//        String[] names = metricFull.split("\\.");
//        if (names.length != METRIC_FULL_LENGTH) {
//            throw new IllegalArgumentException("metrics full name: " + metricFull + " is illegal.");
//        }
//        String app = names[0];
//        String metrics = names[1];
//        String metric = names[2];
//        if (history == null) {
//            history = "6h";
//        }
//        Map<String, List<Value>> instanceValuesMap;
//        if (interval == null || !interval) {
//            instanceValuesMap = historyDataStorage.getHistoryMetricData(monitorId, app, metrics, metric, label, history);
//        } else {
//            instanceValuesMap = historyDataStorage.getHistoryIntervalMetricData(monitorId, app, metrics, metric, label, history);
//        }
//        MetricsHistoryData historyData = MetricsHistoryData.builder()
//                .id(monitorId).metrics(metrics).values(instanceValuesMap)
//                .field(Field.builder().name(metric).type(CommonConstants.TYPE_NUMBER).build())
//                .build();
//        return ResponseEntity.ok(Message.success(historyData));
    }


    @GetMapping("/api/monitor/{monitorId}/metrics/{metrics}/label")
    @Operation(summary = "Query Real Time Metrics Data", description = "查询监控指标的实时指标数据")
    public ResponseEntity<Message<MetricsData>> getMetricsDataByLabel(
            @Parameter(description = "Monitor Id", example = "343254354")
            @PathVariable Long monitorId,
            @Parameter(description = "Metrics Name", example = "cpu")
            @PathVariable String metrics,
            @Parameter(description = "Metrics label", example = "label")
            @RequestParam(required = false) String label) {


        MetricsData metricsData = metricsDataService.getMetricsDataByLabel(monitorId, metrics, label);
        if (metricsData == null) {
            return ResponseEntity.ok(Message.success("query metrics data is empty"));
        }
        return ResponseEntity.ok(Message.success(metricsData));
    }


}
