package cn.iocoder.cloud.framework.warehouse.dao;

import org.apache.hertzbeat.common.entity.manager.MonitorMetricsInterfaceData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;

public interface MonitorMetricsInterfaceDataDao extends JpaRepository<MonitorMetricsInterfaceData, Long>, JpaSpecificationExecutor<MonitorMetricsInterfaceData> {

    Optional<MonitorMetricsInterfaceData> findByMonitorIdAndMetricsAndInterfaceIndex(Long monitorId, String metrics, String index);

    List<MonitorMetricsInterfaceData> findByMonitorIdAndMetrics(Long monitorId, String metrics);
}
