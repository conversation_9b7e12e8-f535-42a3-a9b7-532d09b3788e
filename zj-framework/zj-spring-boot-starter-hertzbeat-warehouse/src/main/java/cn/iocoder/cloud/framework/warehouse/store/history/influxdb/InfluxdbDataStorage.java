/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.warehouse.store.history.influxdb;

import cn.iocoder.cloud.framework.warehouse.store.history.AbstractHistoryDataStorage;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import org.apache.hertzbeat.common.constants.CommonConstants;
import org.apache.hertzbeat.common.constants.NetworkConstants;
import org.apache.hertzbeat.common.entity.dto.Value;
import org.apache.hertzbeat.common.entity.message.CollectRep;
import org.apache.hertzbeat.common.util.JsonUtil;
import org.apache.http.ssl.SSLContexts;
import org.influxdb.InfluxDB;
import org.influxdb.InfluxDBFactory;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.net.ssl.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * HistoryInfluxdbDataStorage class
 */
@Component
@ConditionalOnProperty(prefix = "warehouse.store.influxdb", name = "enabled", havingValue = "true")
@Slf4j
public class InfluxdbDataStorage extends AbstractHistoryDataStorage {

    private static final String DATABASE = "zj_cloud_hz";

    private static final String SHOW_DATABASE = "SHOW DATABASES";

    private static final String CREATE_DATABASE = "CREATE DATABASE %s";

    private static final String QUERY_HISTORY_SQL = "SELECT instance, %s FROM %s WHERE time >= now() - %s order by time desc";

    private static final String QUERY_HISTORY_SQL_WITH_INSTANCE = "SELECT instance, %s FROM %s WHERE instance = '%s' and time >= now() - %s order by time desc";

    private static final String QUERY_HISTORY_INTERVAL_WITH_INSTANCE_SQL =
            "SELECT FIRST(%s), MEAN(%s), MAX(%s), MIN(%s) FROM %s WHERE instance = '%s' and time >= now() - %s GROUP BY time(4h)";

    private static final String CREATE_RETENTION_POLICY = "CREATE RETENTION POLICY \"%s_retention\" ON \"%s\" DURATION %s REPLICATION %d DEFAULT";

    private static final String QUERY_INSTANCE_SQL = "show tag values from %s with key = \"instance\"";


    private static final String QUERY_HISTORY_WINSQL_WITH_INSTANCE = "SELECT instance, in_octets, out_octets FROM %s WHERE time >= now() - %s order by time desc";
    private static final String QUERY_HISTORY_BYLINUX_SQL = "SELECT instance, receive_bytes ,transmit_bytes FROM %s WHERE time >= now() - %s and instance = '%s' order by time desc";

    private static final String QUERY_HISTORY_BYDESCR_SQL = "SELECT instance, in_octets, out_octets FROM %s WHERE time >= now() - %s and instance = '%s' order by time desc";


    private InfluxDB influxDb;

    public InfluxdbDataStorage(InfluxdbProperties influxdbProperties) {
        this.initInfluxDb(influxdbProperties);
    }

    public void initInfluxDb(InfluxdbProperties influxdbProperties) {

        var client = new OkHttpClient.Builder()
                .readTimeout(NetworkConstants.HttpClientConstants.READ_TIME_OUT, TimeUnit.SECONDS)
                .writeTimeout(NetworkConstants.HttpClientConstants.WRITE_TIME_OUT, TimeUnit.SECONDS)
                .connectTimeout(NetworkConstants.HttpClientConstants.CONNECT_TIME_OUT, TimeUnit.SECONDS)
                .connectionPool(new ConnectionPool(
                        NetworkConstants.HttpClientConstants.MAX_IDLE_CONNECTIONS,
                        NetworkConstants.HttpClientConstants.KEEP_ALIVE_TIMEOUT,
                        TimeUnit.SECONDS)
                ).sslSocketFactory(defaultSslSocketFactory(), defaultTrustManager())
                .hostnameVerifier(noopHostnameVerifier())
                .retryOnConnectionFailure(true);

        this.influxDb = InfluxDBFactory.connect(influxdbProperties.getServerUrl(), influxdbProperties.getUsername(), influxdbProperties.getPassword(), client);
        // Close it if your application is terminating, or you are not using it anymore.
        Runtime.getRuntime().addShutdownHook(new Thread(influxDb::close));

        this.serverAvailable = this.createDatabase(influxdbProperties);
    }

    private boolean createDatabase(InfluxdbProperties influxdbProperties) {
        QueryResult queryResult = this.influxDb.query(new Query(SHOW_DATABASE));

        if (queryResult.hasError()) {
            log.error("show databases in influxdb error, msg: {}", queryResult.getError());
            return false;
        }

        for (QueryResult.Result result : queryResult.getResults()) {
            for (QueryResult.Series series : result.getSeries()) {
                for (List<Object> values : series.getValues()) {
                    if (values.contains(DATABASE)) {
                        // database exists
                        return true;
                    }
                }
            }
        }

        // create the database
        String createDatabaseSql = String.format(CREATE_DATABASE, DATABASE);
        QueryResult createDatabaseResult = this.influxDb.query(new Query(createDatabaseSql));
        if (createDatabaseResult.hasError()) {
            log.error("create database {} in influxdb error, msg: {}", DATABASE, createDatabaseResult.getError());
            return false;
        }
        // set the expiration time
        String createRetentionPolicySql = String.format(CREATE_RETENTION_POLICY, DATABASE, DATABASE,
                influxdbProperties.getExpireTime(), influxdbProperties.getReplication());
        QueryResult createRetentionPolicySqlResult = this.influxDb.query(new Query(createRetentionPolicySql));
        if (createRetentionPolicySqlResult.hasError()) {
            log.error("create retention policy in influxdb error, msg: {}", createDatabaseResult.getError());
            return false;
        }

        return true;
    }

    @Override
    public void saveData(CollectRep.MetricsData metricsData) {
        if (!isServerAvailable() || metricsData.getCode() != CollectRep.Code.SUCCESS) {
            return;
        }
        if (metricsData.getValuesList().isEmpty()) {
            log.info("[warehouse influxdb] flush metrics data {} is null, ignore.", metricsData.getId());
            return;
        }
        List<CollectRep.Field> fieldsList = metricsData.getFieldsList();

        String table = this.generateTable(metricsData.getApp(), metricsData.getMetrics(), metricsData.getId());

        List<Point> points = new ArrayList<>();
        for (CollectRep.ValueRow valueRow : metricsData.getValuesList()) {
            Point.Builder builder = Point.measurement(table);
            builder.time(metricsData.getTime(), TimeUnit.MILLISECONDS);
            Map<String, String> labels = new HashMap<>(8);
            for (int i = 0; i < fieldsList.size(); i++) {
                CollectRep.Field field = fieldsList.get(i);
                if (!CommonConstants.NULL_VALUE.equals(valueRow.getColumns(i))) {
                    if (field.getType() == CommonConstants.TYPE_NUMBER) {
                        builder.addField(field.getName(), Double.parseDouble(valueRow.getColumns(i)));
                    } else if (field.getType() == CommonConstants.TYPE_STRING) {
                        builder.addField(field.getName(), valueRow.getColumns(i));
                    }
                    if (field.getLabel()) {
                        labels.put(field.getName(), valueRow.getColumns(i));
                    }
                } else {
                    builder.addField(field.getName(), "");
                }
            }

            StringBuilder concatenatedValues = new StringBuilder();
            if (labels.size() != 0) {
                String json = JsonUtil.toJson(labels);
                JSONObject jsonObject = JSONObject.parseObject(json);
                for (String key : jsonObject.keySet()){
                    Object value =jsonObject.get(key);
                    // 在每次迭代中将当前的 Value 添加到字符串，并添加逗号
                    concatenatedValues.append(value).append(",");
                }
                // 删除最后一个多余的逗号
                if (concatenatedValues.length() > 0) {
                    concatenatedValues.deleteCharAt(concatenatedValues.length() - 1);
                }
                builder.tag("instance", concatenatedValues.toString());

            }else {
                builder.tag("instance", "");
            }
            points.add(builder.build());
        }
        BatchPoints.Builder builder = BatchPoints.database(DATABASE);
        builder.points(points);
        this.influxDb.write(builder.build());
    }


    @Override
    public Map<String, List<Value>> getHistoryMetricData(Long monitorId, String app, String metrics, String metric, String label, String history) {
        String table = this.generateTable(app, metrics, monitorId);
        String selectSql;
        Map<String, List<Value>> instanceValueMap = new HashMap<>(8);

        if (metric.equals("interface")) {
            if (app.equals("windows")) {
                selectSql = String.format(QUERY_HISTORY_WINSQL_WITH_INSTANCE, table, history);
                try {
                    QueryResult selectResult = this.influxDb.query(new Query(selectSql, DATABASE), TimeUnit.MILLISECONDS);
                    for (QueryResult.Result result : selectResult.getResults()) {
                        if (result.getSeries() == null) {
                            continue;
                        }
                        for (QueryResult.Series series : result.getSeries()) {
                            for (List<Object> value : series.getValues()) {
                                long time = this.parseTimeToMillis(value.get(0));
                                label = label.replace("\0", "");
                                String instanceValue = value.get(1) == null ? "" : String.valueOf(value.get(1));
                                String invalue = instanceValue.replace("\0", "");
                                if (invalue.equals(label)) {
                                    String inValue = value.get(2) == null ? null : this.parseDoubleValue(value.get(2).toString());
                                    if (inValue == null) {
                                        continue;
                                    }

                                    String outValue = value.get(3) == null ? null : this.parseDoubleValue(value.get(3).toString());
                                    if (outValue == null) {
                                        continue;
                                    }
                                    List<Value> valueList = instanceValueMap.computeIfAbsent(instanceValue, k -> new LinkedList<>());
                                    valueList.add(new Value(inValue, outValue, time));
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("select history metric data in influxdb error, sql:{}, msg: {}", selectSql, e.getMessage());
                }

            } else {
                selectSql = String.format(QUERY_HISTORY_BYDESCR_SQL, table, history, label);
                try {
                    QueryResult selectResult = this.influxDb.query(new Query(selectSql, DATABASE), TimeUnit.MILLISECONDS);
                    // 如果没查到则查liunx字段
                    if (selectResult.getResults().get(0).getSeries() == null) {
                        selectSql = String.format(QUERY_HISTORY_BYLINUX_SQL, table, history, label);
                        selectResult = this.influxDb.query(new Query(selectSql, DATABASE), TimeUnit.MILLISECONDS);
                    }
                    for (QueryResult.Result result : selectResult.getResults()) {
                        if (result.getSeries() == null) {
                            continue;
                        }
                        for (QueryResult.Series series : result.getSeries()) {
                            for (List<Object> value : series.getValues()) {
                                long time = this.parseTimeToMillis(value.get(0));
                                String instanceValue = value.get(1) == null ? "" : String.valueOf(value.get(1));
                                String inValue = value.get(2) == null ? null : this.parseDoubleValue(value.get(2).toString());
                                if (inValue == null) {
                                    continue;
                                }

                                String outValue = value.get(3) == null ? null : this.parseDoubleValue(value.get(3).toString());
                                if (outValue == null) {
                                    continue;
                                }
                                List<Value> valueList = instanceValueMap.computeIfAbsent(instanceValue, k -> new LinkedList<>());
                                valueList.add(new Value(inValue, outValue, time));
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("select history metric data in influxdb error, sql:{}, msg: {}", selectSql, e.getMessage());
                }
            }


        } else {
            selectSql = label == null ? String.format(QUERY_HISTORY_SQL, metric, table, history)
                    : String.format(QUERY_HISTORY_SQL_WITH_INSTANCE, metric, table, label, history);
            try {
                QueryResult selectResult = this.influxDb.query(new Query(selectSql, DATABASE), TimeUnit.MILLISECONDS);
                for (QueryResult.Result result : selectResult.getResults()) {
                    if (result.getSeries() == null) {
                        continue;
                    }
                    for (QueryResult.Series series : result.getSeries()) {
                        for (List<Object> value : series.getValues()) {
                            long time = this.parseTimeToMillis(value.get(0));
                            String instanceValue = value.get(1) == null ? "" : String.valueOf(value.get(1));
                            String strValue = value.get(2) == null ? null : this.parseDoubleValue(value.get(2).toString());
                            if (strValue == null) {
                                continue;
                            }
                            List<Value> valueList = instanceValueMap.computeIfAbsent(instanceValue, k -> new LinkedList<>());
                            valueList.add(new Value(strValue, time));
                        }
                    }
                }
            } catch (Exception e) {
                log.error("select history metric data in influxdb error, sql:{}, msg: {}", selectSql, e.getMessage());
            }
        }

        if (metric.equals("in_octets") || metric.equals("out_octets")) {
            for (Map.Entry<String, List<Value>> entry : instanceValueMap.entrySet()) {
                String key = entry.getKey(); // 获取 Map 的键
                if (key.equals("GigabitEthernet1/0/19")){
                    System.out.println("1");
                }
                List<Value> valueList = entry.getValue(); // 获取对应的值（List<Value>）
                if (!valueList.isEmpty()) {
                    for (int i = 0; i < valueList.size() - 1; i++) {
                        Value currentValue = valueList.get(i);
                        Value nextValue = valueList.get(i + 1);
                        // 假设 Value 类有一个 getValue() 方法返回该值
                        double result = Double.parseDouble(currentValue.getOrigin()) - Double.parseDouble(nextValue.getOrigin());
                        if (result < 0) {
                            result = 0;
                        }
                        double values = 8 * result / 60;
                        String formattedIn = String.format("%.4f", values);
                        currentValue.setOrigin(formattedIn);
                    }

                }
                valueList.remove(valueList.size() - 1);
            }
        }
        if (metric.equals("interface")){
            for (Map.Entry<String, List<Value>> entry : instanceValueMap.entrySet()) {
                String key = entry.getKey(); // 获取 Map 的键
                List<Value> valueList = entry.getValue(); // 获取对应的值（List<Value>）
                if (!valueList.isEmpty()) {
                    for (int i = 0; i < valueList.size() - 1; i++) {
                        Value currentValue = valueList.get(i);
                        Value nextValue = valueList.get(i + 1);
                        // 假设 Value 类有一个 getValue() 方法返回该值
                        double result = Double.parseDouble(currentValue.getOrigin()) - Double.parseDouble(nextValue.getOrigin());
                        if (result < 0) {
                            result = 0;
                        }
                        double values = 8 * result / 60;
                        String formattedIn = String.format("%.4f", values);
                        currentValue.setOrigin(formattedIn);

                        double outResult = Double.parseDouble(currentValue.getOutigin()) - Double.parseDouble(nextValue.getOutigin());
                        if (outResult < 0) {
                            outResult = 0;
                        }
                        double outValues = 8 * outResult / 60;
                        String formattedOut = String.format("%.4f", outValues);
                        currentValue.setOutigin(formattedOut);
                    }

                }
                valueList.remove(valueList.size() - 1);
            }

        }


        return instanceValueMap;
    }

    @Override
    public Map<String, List<Value>> getHistoryIntervalMetricData(Long monitorId, String app, String metrics, String metric, String label, String history) {
        String table = this.generateTable(app, metrics, monitorId);
        Map<String, List<Value>> instanceValueMap = new HashMap<>(8);
        Set<String> instances = new HashSet<>(8);
        if (label != null) {
            instances.add(label);
        }
        // query the instance near 1week
        String queryInstanceSql = String.format(QUERY_INSTANCE_SQL, table);
        if (instances.isEmpty()) {
            try {
                QueryResult instanceQueryResult = this.influxDb.query(new Query(queryInstanceSql, DATABASE), TimeUnit.MILLISECONDS);
                for (QueryResult.Result result : instanceQueryResult.getResults()) {
                    if (result.getSeries() == null) {
                        continue;
                    }
                    for (QueryResult.Series series : result.getSeries()) {
                        for (List<Object> value : series.getValues()) {
                            if (value != null && value.get(1) != null) {
                                instances.add(value.get(1).toString());
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("select history metric data in influxdb error, sql:{}, msg: {}", queryInstanceSql, e.getMessage());
            }
        }

        try {
            history = history.toLowerCase();
            if (instances.isEmpty()) {
                instances.add("");
            }
            if (metric.equals("interface")) {
                for (String instanceValue : instances) {
                    String selectSql = String.format(QUERY_HISTORY_INTERVAL_WITH_INSTANCE_SQL, "in_octets", "in_octets", "in_octets", "in_octets", table, instanceValue, history);
                    QueryResult selectResult = this.influxDb.query(new Query(selectSql, DATABASE), TimeUnit.MILLISECONDS);
                    if (selectResult.getResults().get(0).getSeries() == null) {
                        selectSql = String.format(QUERY_HISTORY_INTERVAL_WITH_INSTANCE_SQL, "receive_bytes", "receive_bytes", "receive_bytes", "receive_bytes", table, instanceValue, history);
                        selectResult = this.influxDb.query(new Query(selectSql, DATABASE), TimeUnit.MILLISECONDS);
                    }
                    for (QueryResult.Result result : selectResult.getResults()) {
                        if (result.getSeries() == null) {
                            continue;
                        }
                        for (QueryResult.Series series : result.getSeries()) {
                            for (List<Object> value : series.getValues()) {
                                Value.ValueBuilder valueBuilder = Value.builder();
                                long time = this.parseTimeToMillis(value.get(0));
                                valueBuilder.time(time);

                                if (value.get(1) != null) {
                                    valueBuilder.origin(this.parseDoubleValue(value.get(1).toString()));
                                } else {
                                    continue;
                                }
                                if (value.get(2) != null) {
                                    valueBuilder.mean(this.parseDoubleValue(value.get(2).toString()));
                                } else {
                                    continue;
                                }
                                if (value.get(3) != null) {
                                    valueBuilder.max(this.parseDoubleValue(value.get(3).toString()));
                                } else {
                                    continue;
                                }
                                if (value.get(4) != null) {
                                    valueBuilder.min(this.parseDoubleValue(value.get(4).toString()));
                                } else {
                                    continue;
                                }

                                List<Value> valueList = instanceValueMap.computeIfAbsent(instanceValue, k -> new LinkedList<>());
                                valueList.add(valueBuilder.build());
                            }
                        }
                    }
                    String outSql = String.format(QUERY_HISTORY_INTERVAL_WITH_INSTANCE_SQL, "out_octets", "out_octets", "out_octets", "out_octets", table, instanceValue, history);
                    QueryResult outResult = this.influxDb.query(new Query(outSql, DATABASE), TimeUnit.MILLISECONDS);
                    if (outResult.getResults().get(0).getSeries() == null) {
                        outSql = String.format(QUERY_HISTORY_INTERVAL_WITH_INSTANCE_SQL, "transmit_bytes", "transmit_bytes", "transmit_bytes", "transmit_bytes", table, instanceValue, history);
                        outResult = this.influxDb.query(new Query(outSql, DATABASE), TimeUnit.MILLISECONDS);
                    }

                    for (QueryResult.Result result : outResult.getResults()) {
                        if (result.getSeries() == null) {
                            continue;
                        }
                        for (QueryResult.Series series : result.getSeries()) {
                            for (List<Object> value : series.getValues()) {
                                Value.ValueBuilder valueBuilder = Value.builder();
                                long time = this.parseTimeToMillis(value.get(0));
                                valueBuilder.time(time);

                                if (value.get(1) != null) {
                                    valueBuilder.outigin(this.parseDoubleValue(value.get(1).toString()));
                                } else {
                                    continue;
                                }
                                if (value.get(2) != null) {
                                    valueBuilder.mean(this.parseDoubleValue(value.get(2).toString()));
                                } else {
                                    continue;
                                }
                                if (value.get(3) != null) {
                                    valueBuilder.max(this.parseDoubleValue(value.get(3).toString()));
                                } else {
                                    continue;
                                }
                                if (value.get(4) != null) {
                                    valueBuilder.min(this.parseDoubleValue(value.get(4).toString()));
                                } else {
                                    continue;
                                }

                                List<Value> valueList = instanceValueMap.computeIfAbsent(instanceValue, k -> new LinkedList<>());
                                valueList.add(valueBuilder.build());
                            }
                        }
                    }

                    List<Value> instanceValueList = instanceValueMap.get(instanceValue);
                    if (instanceValueList == null || instanceValueList.isEmpty()) {
                        instanceValueMap.remove(instanceValue);
                    }
                }
            } else {
                for (String instanceValue : instances) {
                    String selectSql = String.format(QUERY_HISTORY_INTERVAL_WITH_INSTANCE_SQL, metric, metric, metric, metric, table, instanceValue, history);
                    QueryResult selectResult = this.influxDb.query(new Query(selectSql, DATABASE), TimeUnit.MILLISECONDS);
                    for (QueryResult.Result result : selectResult.getResults()) {
                        if (result.getSeries() == null) {
                            continue;
                        }
                        for (QueryResult.Series series : result.getSeries()) {
                            for (List<Object> value : series.getValues()) {
                                Value.ValueBuilder valueBuilder = Value.builder();
                                long time = this.parseTimeToMillis(value.get(0));
                                valueBuilder.time(time);

                                if (value.get(1) != null) {
                                    valueBuilder.origin(this.parseDoubleValue(value.get(1).toString()));
                                } else {
                                    continue;
                                }
                                if (value.get(2) != null) {
                                    valueBuilder.mean(this.parseDoubleValue(value.get(2).toString()));
                                } else {
                                    continue;
                                }
                                if (value.get(3) != null) {
                                    valueBuilder.max(this.parseDoubleValue(value.get(3).toString()));
                                } else {
                                    continue;
                                }
                                if (value.get(4) != null) {
                                    valueBuilder.min(this.parseDoubleValue(value.get(4).toString()));
                                } else {
                                    continue;
                                }

                                List<Value> valueList = instanceValueMap.computeIfAbsent(instanceValue, k -> new LinkedList<>());
                                valueList.add(valueBuilder.build());
                            }
                        }
                    }
                    List<Value> instanceValueList = instanceValueMap.get(instanceValue);
                    if (instanceValueList == null || instanceValueList.isEmpty()) {
                        instanceValueMap.remove(instanceValue);
                    }
                }
            }
        } catch (Exception e) {
            log.error("select history interval metric data in influxdb error, msg: {}", e.getMessage());
        }

        if (metric.equals("in_octets") || metric.equals("out_octets")) {
            for (Map.Entry<String, List<Value>> entry : instanceValueMap.entrySet()) {
                String key = entry.getKey(); // 获取 Map 的键
                List<Value> valueList = entry.getValue(); // 获取对应的值（List<Value>）
                for (int i = 0; i < valueList.size(); i++) {
                    Value currentValue = valueList.get(i);
                    double newOrigin = Double.parseDouble(currentValue.getOrigin()) / 60;
                    double newMean = Double.parseDouble(currentValue.getMean()) / 60;
                    double newMax = Double.parseDouble(currentValue.getMax()) / 60;
                    double newMin = Double.parseDouble(currentValue.getMin()) / 60;
                    String formattedIn = String.format("%.4f", newOrigin);
                    String formattedMean = String.format("%.4f", newMean);
                    String formattedMax = String.format("%.4f", newMax);
                    String formattedMin = String.format("%.4f", newMin);
                    currentValue.setOrigin(formattedIn);
                    currentValue.setMean(formattedMean);
                    currentValue.setMax(formattedMax);
                    currentValue.setMin(formattedMin);
                }
            }
        }
        if (metric.equals("interface")){
            for (Map.Entry<String, List<Value>> entry : instanceValueMap.entrySet()) {
                String key = entry.getKey(); // 获取 Map 的键
                List<Value> valueList = entry.getValue(); // 获取对应的值（List<Value>）
                for (int i = 0; i < valueList.size(); i++) {
                    Value currentValue = valueList.get(i);
                    if (currentValue.getOrigin()!=null){
                        double inOrigin = Double.parseDouble(currentValue.getOrigin()) / 60;
                        String formattedIn = String.format("%.4f", inOrigin);
                        currentValue.setOrigin(formattedIn);
                    }else {
                        double outOrigin = Double.parseDouble(currentValue.getOutigin()) / 60;
                        String formattedIn = String.format("%.4f", outOrigin);
                        currentValue.setOutigin(formattedIn);
                    }
                    double newMean = Double.parseDouble(currentValue.getMean()) / 60;
                    double newMax = Double.parseDouble(currentValue.getMax()) / 60;
                    double newMin = Double.parseDouble(currentValue.getMin()) / 60;
                    String formattedMean = String.format("%.4f", newMean);
                    String formattedMax = String.format("%.4f", newMax);
                    String formattedMin = String.format("%.4f", newMin);
                    currentValue.setMean(formattedMean);
                    currentValue.setMax(formattedMax);
                    currentValue.setMin(formattedMin);
                }
            }
        }
        return instanceValueMap;
    }

    @Override
    public Map<String, List<Value>> getHistoryIntervalMetricData(Long monitorId, String app, String metrics, String metric, String label, String history, String step) {
        return Map.of();
    }

    @Override
    public Map<String, List<Value>> getHistoryIntervalMetricData(Long monitorId, String app, String metrics, String metric, String label, String history, Long start, Long end, String step, String type) {
        return Map.of();
    }

    private String generateTable(String app, String metrics, Long monitorId) {
        return app + "_" + metrics + "_" + monitorId;
    }

    private long parseTimeToMillis(Object time) {
        if (time == null) {
            return 0;
        }
        Double doubleTime = (Double) time;
        return doubleTime.longValue();
    }

    private String parseDoubleValue(String value) {
        return (new BigDecimal(value)).setScale(4, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
    }

    private static X509TrustManager defaultTrustManager() {
        return new X509TrustManager() {
            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[0];
            }

            @Override
            public void checkClientTrusted(X509Certificate[] certs, String authType) {
            }

            @Override
            public void checkServerTrusted(X509Certificate[] certs, String authType) {
            }
        };
    }

    private static SSLSocketFactory defaultSslSocketFactory() {
        try {
            SSLContext sslContext = SSLContexts.createDefault();
            sslContext.init(null, new TrustManager[]{
                    defaultTrustManager()
            }, new SecureRandom());
            return sslContext.getSocketFactory();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static HostnameVerifier noopHostnameVerifier() {
        return (s, sslSession) -> true;
    }

    @Override
    public void destroy() throws Exception {
        if (this.influxDb != null) {
            this.influxDb.close();
        }
    }
}
