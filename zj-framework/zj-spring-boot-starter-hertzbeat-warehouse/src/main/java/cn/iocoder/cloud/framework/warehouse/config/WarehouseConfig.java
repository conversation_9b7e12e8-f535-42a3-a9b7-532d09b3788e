/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.cloud.framework.warehouse.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 数据仓库配置类
 * 
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "warehouse.store")
public class WarehouseConfig {

    /**
     * 实时存储配置
     */
    private RealTimeConfig realtime = new RealTimeConfig();

    /**
     * 持久化存储配置
     */
    private PersistentConfig persistent = new PersistentConfig();

    /**
     * 线程池配置
     */
    private ThreadPoolConfig threadPool = new ThreadPoolConfig();

    /**
     * 性能监控配置
     */
    private MonitorConfig monitor = new MonitorConfig();

    @Data
    public static class RealTimeConfig {
        /**
         * 实时存储消费者线程数
         */
        private int consumerThreads = 5;

        /**
         * 实时存储批量大小
         */
        private int batchSize = 50;

        /**
         * 慢操作告警阈值(ms)
         */
        private long slowThreshold = 1000;

        /**
         * 是否启用批量处理
         */
        private boolean batchEnabled = false;
    }

    @Data
    public static class PersistentConfig {
        /**
         * 持久化存储消费者线程数
         */
        private int consumerThreads = 8;

        /**
         * 持久化存储批量大小
         */
        private int batchSize = 100;

        /**
         * 慢操作告警阈值(ms)
         */
        private long slowThreshold = 3000;

        /**
         * 是否启用批量处理
         */
        private boolean batchEnabled = true;

        /**
         * 批量处理最大等待时间(ms)
         */
        private long batchMaxWaitTime = 5000;
    }

    @Data
    public static class ThreadPoolConfig {
        /**
         * 核心线程数
         */
        private int corePoolSize = 20;

        /**
         * 最大线程数
         */
        private int maximumPoolSize = 50;

        /**
         * 线程空闲时间(秒)
         */
        private long keepAliveTime = 60;

        /**
         * 队列容量
         */
        private int queueCapacity = 1000;

        /**
         * 是否允许核心线程超时
         */
        private boolean allowCoreThreadTimeOut = true;
    }

    @Data
    public static class MonitorConfig {
        /**
         * 是否启用性能监控
         */
        private boolean enabled = true;

        /**
         * 统计输出间隔(秒)
         */
        private int statsInterval = 60;

        /**
         * 是否启用详细日志
         */
        private boolean detailedLogging = false;
    }
} 