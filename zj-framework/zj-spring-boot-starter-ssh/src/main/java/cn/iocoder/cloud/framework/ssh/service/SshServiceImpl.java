package cn.iocoder.cloud.framework.ssh.service;

import cn.hutool.extra.ssh.JschUtil;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import cn.iocoder.cloud.framework.ssh.config.ZjSshProperties;
import cn.iocoder.cloud.framework.ssh.dal.SshSessionInfo;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;


import org.springframework.stereotype.Service;


/**
 * @ClassName : SshServiceImpl  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/5/21  11:06
 */
@Service
public class SshServiceImpl implements SshService {

    private static final Log log = LogFactory.get();

    private final ZjSshProperties properties;

    public SshServiceImpl(ZjSshProperties properties) {
        this.properties = properties;
    }


    @Override
    public Session SSHProxy() {
        try {

            Session session = JschUtil.getSession(properties.getHost(), properties.getPort(), properties.getUser(), properties.getPassword());
            log.info("SSH代理已连接。");
            return session;
        } catch (Exception e) {
            log.error("SSH连接失败", e);
            return null;
        }
    }

    @Override
    public SshSessionInfo setupPortForwarding(Session session, int localPort, String remoteHost, int remotePort) throws JSchException {


        if (session != null && session.isConnected()) {

            boolean d = JschUtil.bindPort(session, remoteHost, remotePort, localPort);
            if (d) {
                log.info("SSH端口映射成功。" + remoteHost);
            }
            // Construct and return the session info object
            SshSessionInfo sessionInfo = new SshSessionInfo();

            sessionInfo.setHost(session.getHost());
            sessionInfo.setPort(session.getPort());
            sessionInfo.setUserName(session.getUserName());
            sessionInfo.setLocalPort(localPort);
            sessionInfo.setRemoteHost(remoteHost);
            sessionInfo.setRemotePort(remotePort);
            return sessionInfo; // Return the constructed SshSessionInfo object

        } else {
            throw new IllegalStateException("SSH Session is not connected.");
        }
    }

    public void handleSshConnectionError(Exception e) {
        // 记录错误、等待一段时间然后重试，或者抛出异常让上层处理
        log.error("SSH Connection Error: " + e.getMessage());
        waitForRetry();
        SSHProxy(); // 尝试重新连接
    }


    public void waitForRetry() {
        // 等待一段时间，例如5秒
        try {
            Thread.sleep(5000);
        } catch (InterruptedException ie) {
            Thread.currentThread().interrupt(); // 处理中断的正确方式
        }
    }


}
