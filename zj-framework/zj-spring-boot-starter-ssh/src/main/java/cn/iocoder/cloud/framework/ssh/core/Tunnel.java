package cn.iocoder.cloud.framework.ssh.core;

import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.JSchException;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName : Tunnel  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/5/21  14:21
 */


public class Tunnel {
    private String id;
    private String localHost;
    private int localPort;
    private String remoteHost;
    private int remotePort;
    private ServerSocket listener;
    private List<Socket> localConnections;
    private List<Socket> remoteConnections;
    private Session session;
    private boolean keepRunning;



    public Tunnel(String id, String localHost, int localPort, String remoteHost, int remotePort, Session session) {
        this.id = id;
        this.localHost = localHost;
        this.localPort = localPort;
        this.remoteHost = remoteHost;
        this.remotePort = remotePort;
        this.session = session;
        this.localConnections = new ArrayList<>();
        this.remoteConnections = new ArrayList<>();
        this.keepRunning = true;
    }


    public void open(Session session) throws IOException, JSchException {
        listener = new ServerSocket(localPort);
        while (keepRunning) {
            Socket localConn = listener.accept();
            localConnections.add(localConn);

            String remoteAddr = String.format("%s:%d", remoteHost, remotePort);
            Socket remoteConn = new Socket(remoteHost, remotePort);
            remoteConnections.add(remoteConn);

            new Thread(() -> copyConn(localConn, remoteConn)).start();
            new Thread(() -> copyConn(remoteConn, localConn)).start();
        }
    }



    public void close() throws IOException {
        keepRunning = false;
        for (Socket conn : localConnections) {
            conn.close();
        }
        for (Socket conn : remoteConnections) {
            conn.close();
        }
        if (listener != null) {
            listener.close();
        }
    }
    // 添加其他必要的方法...
    // 注意：这个Java版本的copyConn函数并不完全等效于Go版本。
    // TCP转发通常更复杂，可能需要调整以符合您的具体需求。
    public static void copyConn(Socket writer, Socket reader) {
        try {
            InputStream in = reader.getInputStream();
            OutputStream out = writer.getOutputStream();
            byte[] buffer = new byte[1024];
            int len;
            while ((len = in.read(buffer)) != -1) {
                out.write(buffer, 0, len);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                writer.close();
                reader.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
