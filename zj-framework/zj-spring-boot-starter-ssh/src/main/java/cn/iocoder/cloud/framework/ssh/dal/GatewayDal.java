package cn.iocoder.cloud.framework.ssh.dal;

import com.jcraft.jsch.Session;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @ClassName : GatewayDal  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/5/21  17:17
 */
@Data
public class GatewayDal {
    private String id;
    private String gatewayType;
    private String ip;
    private int port;
    private String username;
    private String password;
    private String privateKey;
    private String passphrase;
    private boolean connected = false;
    private String message = "暂未使用";
    private Session sshSession;

}
