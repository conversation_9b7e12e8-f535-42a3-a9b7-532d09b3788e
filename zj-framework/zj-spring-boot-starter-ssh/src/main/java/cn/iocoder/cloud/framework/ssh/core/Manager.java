package cn.iocoder.cloud.framework.ssh.core;

import cn.iocoder.cloud.framework.ssh.dal.AccessGateway;
import cn.iocoder.cloud.framework.ssh.dal.GatewayDal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;

/**
 * @ClassName : Manager  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/5/21  16:43
 */

public class Manager {
    private ConcurrentHashMap<String, Gateway> gateways = new ConcurrentHashMap<>();


    public Gateway getById(String id) {
        return gateways.get(id);
    }

    public Gateway add(GatewayDal model) {
        Gateway gateway = new Gateway(model);
        // 然后用给定的model来设置Bean的数据
        gateways.put(model.getId(), gateway);
        return gateway;
    }

    public void del(String id) {
        Gateway g = getById(id);
        if (g != null) {
            g.close(); // Assuming you have a close method
        }
        gateways.remove(id);
    }
}
