package cn.iocoder.cloud.framework.ssh.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * @ClassName : ZjSshProperties  //类名
 * @Description : ssh配置项  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/5/21  10:55
 */

@ConfigurationProperties("zj.ssh")
@Data
@Validated
public class ZjSshProperties {

    private String host;
    private int port;
    private String user;
    private String password;
}
