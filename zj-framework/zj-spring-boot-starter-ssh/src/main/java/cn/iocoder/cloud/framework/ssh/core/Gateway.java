package cn.iocoder.cloud.framework.ssh.core;

import cn.hutool.extra.ssh.Connector;
import cn.hutool.extra.ssh.JschUtil;
import cn.iocoder.cloud.framework.ssh.dal.GatewayDal;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @ClassName : Gateway  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/5/21  16:44
 */

public class Gateway {

    private String id;
    private String gatewayType;
    private String ip;
    private int port;
    private String username;
    private String password;
    private String privateKey;
    private String passphrase;
    private boolean connected = false;
    private String message = "暂未使用";
    private Session sshSession;
    private int localPort; // SSH隧道的本地端口

    private final Object lock = new Object();
    private ConcurrentHashMap<String, Tunnel> tunnels;
    // 无参构造函数，对于Spring托管的bean是必要的
    public Gateway(GatewayDal gatewayDal) {
        this.id = gatewayDal.getId();
        this.gatewayType = gatewayDal.getGatewayType();
        this.ip = gatewayDal.getIp();
        this.port = gatewayDal.getPort();
        this.username = gatewayDal.getUsername();
        this.password = gatewayDal.getPassword();
        this.privateKey = gatewayDal.getPrivateKey();
        this.passphrase = gatewayDal.getPassphrase();
        this.tunnels = new ConcurrentHashMap<>();

    }


    public Tunnel openSshTunnel(String tunnelId, String remoteIp, int remotePort) throws JSchException, IOException {
        synchronized (lock) {
            if (!connected) {
                JSch jsch = new JSch();
                if (privateKey != null && !privateKey.isEmpty()) {
                    jsch.addIdentity(privateKey, passphrase);
                }
                sshSession = JschUtil.createSession(jsch,ip,port,username);
                if (password != null && !password.isEmpty()) {
                    sshSession.setPassword(password);
                }
//                java.util.Properties config = new java.util.Properties();
//                config.put("StrictHostKeyChecking", "no");
//                sshSession.setConfig(config);
                sshSession.connect();
                connected = true;
                message = "使用中";
            }
        }


        // 开启本地ServerSocket监听

        try (ServerSocket serverSocket = new ServerSocket(0)) {
            this.localPort = serverSocket.getLocalPort();
            System.out.println("SSH 隧道已开启，本地端口:" +   this.localPort);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        // 创建并启动隧道
        Tunnel tunnel = new Tunnel(tunnelId, "localhost", localPort, remoteIp, remotePort,sshSession);


        tunnel.open(sshSession);
        synchronized (lock) {
            tunnels.put(tunnelId, tunnel);
        }

        return tunnel;
    }


    public void openConnection(Connector connector,String remoteIp, int remotePort) throws Exception {
        int d =  JschUtil.openAndBindPortToLocal(connector,"127.0.0.1",remotePort);
        this.localPort = d;
        System.out.println("SSH 隧道已开启，本地端口:" + d);
    }


    public void closeSshTunnel(String tunnelId) {
        synchronized (lock) {
            Tunnel t = tunnels.remove(tunnelId);
            if (t != null) {
                try {
                    t.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
            if (tunnels.isEmpty() && sshSession != null) {
                sshSession.disconnect();
                connected = false;
                message = "暂未使用";
            }
        }
    }

    public void close() {
        // 关闭所有隧道
        synchronized (lock) {
            for (String id : tunnels.keySet()) {
                closeSshTunnel(id);
            }
        }
    }

    private int assignAvailablePort() throws Exception {
        // 动态获取一个可用的本地端口
        try (ServerSocket s = new ServerSocket(0)) {
            return s.getLocalPort();
        }
    }

    public int getLocalPort() {
        return this.localPort;
    }


    public void closeConnection() {
        if (sshSession != null && sshSession.isConnected()) {
            sshSession.disconnect();
            System.out.println("SSH 隧道已关闭");
        }
    }

}