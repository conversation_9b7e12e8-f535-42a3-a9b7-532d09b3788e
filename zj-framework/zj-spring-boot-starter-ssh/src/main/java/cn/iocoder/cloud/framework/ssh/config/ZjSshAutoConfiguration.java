package cn.iocoder.cloud.framework.ssh.config;

import cn.iocoder.cloud.framework.ssh.service.SshService;
import cn.iocoder.cloud.framework.ssh.service.SshServiceImpl;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;

/**
 * @ClassName : ZjSshAutoConfiguration  //类名
 * @Description :   SSH配置//描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/5/21  10:53
 */
@ComponentScan(basePackages = "cn.iocoder.cloud.framework.ssh")
@EnableConfigurationProperties(ZjSshProperties.class)
public class ZjSshAutoConfiguration {
    @Bean
    public SshService sshService(ZjSshProperties properties) {
        return new SshServiceImpl(properties);
    }
}
