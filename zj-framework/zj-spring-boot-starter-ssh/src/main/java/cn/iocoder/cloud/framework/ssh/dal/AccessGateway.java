package cn.iocoder.cloud.framework.ssh.dal;

import com.jcraft.jsch.Session;
import lombok.Data;

/**
 * @ClassName : AccessGateway  //类名
 * @Description :   //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/5/21  16:47
 */
@Data
public class AccessGateway {
    private String id;
    private String localHost;
    private int localPort;
    private String remoteHost;
    private int remotePort;
    private String sshHost;
    private int sshPort;
    private String username;
    private String password;
    private Session session;
}
