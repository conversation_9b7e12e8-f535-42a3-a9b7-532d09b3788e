package cn.iocoder.zj.framework.sms.core.client;

import cn.iocoder.zj.framework.common.exception.ErrorCode;
import cn.iocoder.zj.framework.sms.core.enums.SmsFrameworkErrorCodeConstants;

import java.util.function.Function;

/**
 * 将 API 的错误码，转换为通用的错误码
 *
 * @see SmsCommonResult
 * @see SmsFrameworkErrorCodeConstants
 *
 * <AUTHOR>
 */
public interface SmsCodeMapping extends Function<String, ErrorCode> {
}
