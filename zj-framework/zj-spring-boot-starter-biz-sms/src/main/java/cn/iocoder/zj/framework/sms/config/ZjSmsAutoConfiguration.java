package cn.iocoder.zj.framework.sms.config;

import cn.iocoder.zj.framework.sms.core.client.SmsClientFactory;
import cn.iocoder.zj.framework.sms.core.client.impl.SmsClientFactoryImpl;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;

/**
 * 短信配置类
 *
 * <AUTHOR>
 */
@AutoConfiguration
public class ZjSmsAutoConfiguration {

    @Bean
    public SmsClientFactory smsClientFactory() {
        return new SmsClientFactoryImpl();
    }

}
