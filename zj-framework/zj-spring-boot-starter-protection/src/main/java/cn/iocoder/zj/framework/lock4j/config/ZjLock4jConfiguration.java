package cn.iocoder.zj.framework.lock4j.config;

import cn.hutool.core.util.ClassUtil;
import cn.iocoder.zj.framework.lock4j.core.DefaultLockFailureStrategy;
import cn.iocoder.zj.framework.lock4j.core.Lock4jRedisKeyConstants;
import com.baomidou.lock.spring.boot.autoconfigure.LockAutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.context.annotation.Bean;

@AutoConfiguration
@AutoConfigureBefore(LockAutoConfiguration.class)
public class ZjLock4jConfiguration {

    @Bean
    public DefaultLockFailureStrategy lockFailureStrategy() {
        return new DefaultLockFailureStrategy();
    }

}
