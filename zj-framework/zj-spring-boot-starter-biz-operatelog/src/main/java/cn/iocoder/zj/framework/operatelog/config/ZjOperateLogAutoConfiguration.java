package cn.iocoder.zj.framework.operatelog.config;

import cn.iocoder.zj.framework.operatelog.core.aop.OperateLogAspect;
import cn.iocoder.zj.framework.operatelog.core.service.OperateLogFrameworkService;
import cn.iocoder.zj.framework.operatelog.core.service.OperateLogFrameworkServiceImpl;
import cn.iocoder.zj.module.system.api.logger.OperateLogApi;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;

@AutoConfiguration
public class ZjOperateLogAutoConfiguration {

    @Bean
    public OperateLogAspect operateLogAspect() {
        return new OperateLogAspect();
    }

    @Bean
    public OperateLogFrameworkService operateLogFrameworkService(OperateLogApi operateLogApi) {
        return new OperateLogFrameworkServiceImpl(operateLogApi);
    }

}
