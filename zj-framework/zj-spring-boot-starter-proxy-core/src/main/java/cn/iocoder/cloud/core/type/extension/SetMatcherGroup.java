package cn.iocoder.cloud.core.type.extension;

import cn.iocoder.cloud.core.type.AbstractMatcherGroup;
import cn.iocoder.cloud.core.type.TypeMatchInfo;
import cn.iocoder.cloud.core.type.TypeMatcher;

import java.util.Collection;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * set匹配器组
 * @author: aoshiguchen
 * @date: 2022/6/29
 */
public class SetMatcherGroup extends AbstractMatcherGroup {

	public SetMatcherGroup(int distanceMin, int distanceMax) {
		super(distanceMin, distanceMax);
	}

	@Override
	public void init() {
		add(new TypeMatcher() {
			@Override
			public TypeMatchInfo match(Class<?> clazz, Class<?> targetClass) {
				TypeMatchInfo matchInfo = new TypeMatchInfo(clazz, targetClass, this);
				if (Set.class == targetClass && clazz.isArray()) {
					matchInfo.setTypeDistance(getDistanceMin());
					matchInfo.setTypeConverter(((value, targetType) -> Stream.of((Object[])value).collect(Collectors.toSet())));
				}
				return matchInfo;
			}
		});
		add(new TypeMatcher() {
			@Override
			public TypeMatchInfo match(Class<?> clazz, Class<?> targetClass) {
				TypeMatchInfo matchInfo = new TypeMatchInfo(clazz, targetClass, this);
				if (Set.class == targetClass && Collection.class.isAssignableFrom(clazz)) {
					matchInfo.setTypeDistance(getDistanceMin() + 1);
					matchInfo.setTypeConverter(((value, targetType) -> ((Collection)value).stream().collect(Collectors.toSet())));
				}
				return matchInfo;
			}
		});
	}

}
