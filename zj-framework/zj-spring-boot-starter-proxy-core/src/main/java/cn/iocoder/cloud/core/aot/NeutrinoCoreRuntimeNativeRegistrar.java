package cn.iocoder.cloud.core.aot;

import cn.hutool.core.util.ClassUtil;
import io.netty.channel.SimpleChannelInboundHandler;
import cn.iocoder.cloud.core.ProxyMessage;
import org.noear.solon.aot.RuntimeNativeMetadata;
import org.noear.solon.aot.RuntimeNativeRegistrar;
import org.noear.solon.aot.hint.MemberCategory;
import org.noear.solon.core.AppContext;

import java.nio.ByteBuffer;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/10/23 21:33
 */
public class NeutrinoCoreRuntimeNativeRegistrar implements RuntimeNativeRegistrar {
    @Override
    public void register(AppContext context, RuntimeNativeMetadata metadata) {
        Set<Class<?>> channelInboundClasses = ClassUtil.scanPackageBySuper("cn.iocoder.cloud", SimpleChannelInboundHandler.class);
        for (Class<?> clazz : channelInboundClasses) {
            metadata.registerReflection(clazz, MemberCategory.INVOKE_DECLARED_METHODS);
        }

        Set<Class<?>> byteBufferClasses = ClassUtil.scanPackageBySuper("java", ByteBuffer.class);
        for (Class<?> clazz : byteBufferClasses) {
            metadata.registerReflection(clazz, MemberCategory.INVOKE_DECLARED_METHODS);
        }

        metadata.registerReflection(ProxyMessage.class, MemberCategory.DECLARED_FIELDS, MemberCategory.INVOKE_DECLARED_METHODS, MemberCategory.INVOKE_DECLARED_CONSTRUCTORS);
        metadata.registerReflection(ProxyMessage.UdpBaseInfo.class, MemberCategory.DECLARED_FIELDS, MemberCategory.INVOKE_DECLARED_METHODS, MemberCategory.INVOKE_DECLARED_CONSTRUCTORS);

        metadata.registerArg("-J--add-opens=java.base/java.lang.invoke=ALL-UNNAMED");
        metadata.registerArg("-J--add-opens=java.base/java.nio=ALL-UNNAMED");
        metadata.registerArg("-J--add-exports=java.base/jdk.internal.misc=ALL-UNNAMED");

        metadata.registerArg("-march=compatibility");
    }
}
