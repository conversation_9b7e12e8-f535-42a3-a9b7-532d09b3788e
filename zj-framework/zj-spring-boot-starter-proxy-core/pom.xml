<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>cn.iocoder.cloud</groupId>
        <artifactId>zj-framework</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>zj-spring-boot-starter-proxy-core</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>proxy 封装拓展</description>

    <dependencies>
        <!--solon aot start（用于 aot 时注册 native 元信息）-->
        <dependency>
            <groupId>org.noear</groupId>
            <artifactId>solon.aot</artifactId>
            <version>2.7.4</version>
        </dependency>
        <!--solon aot end-->

        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
        </dependency>
        <!--hutool -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <version>5.8.20</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-cache</artifactId>
            <version>5.8.20</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
    </dependencies>
    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>

                <includes>
                    <include>META-INF/native-image/neutrino-proxy-core/*.json</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

</project>