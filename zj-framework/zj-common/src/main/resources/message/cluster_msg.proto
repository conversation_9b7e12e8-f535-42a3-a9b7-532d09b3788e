syntax = "proto3";

package cn.iocoder.zj.framework.common.message;

message Message
{
  // 客户端id
  string clientId = 1;
  // 消息类型
  MessageType type = 2;
  // monitoring metrics
  string metrics = 3;
  // 消息内容
  string data = 4;
  // 采集时间
  uint64 time = 5;
}

enum MessageType
{
  // 认证
  AUTH = 0;
  // 心跳
  HEART = 1;
  // 更改
  UPDATE = 2;
  // 初始化配置token
  INFO = 3;
  // 基础数据详情
  BASIC = 4;
  // CPU指令
  CPU_TASK = 5;
  // DISK指令
  DISK_TASK = 6;
  // MEM指令
  MEM_TASK = 7;
  // NET指令
  NET_TASK = 8;
  // 上线
  ONLINE = 9;
  // 下线
  OFFLINE = 10;
  // 启动
  SCHEDULED_START = 11;
  // 停止
  SCHEDULED_STOP = 12;
  // 修改
  SCHEDULED_MODIFY = 13;
  // 采集成功
  SUCCESS = 14;
  // 采集失败
  FAIL = 15;
  // 探测
  DETECT = 16;
  // 采集定时任务状态
  SCHEDULED_INFO = 17;
  //告警
  ALERTER = 18;
  // 其他
  OTHER = 19;

}
