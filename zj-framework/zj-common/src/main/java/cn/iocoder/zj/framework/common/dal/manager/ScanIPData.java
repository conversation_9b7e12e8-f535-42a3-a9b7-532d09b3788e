package cn.iocoder.zj.framework.common.dal.manager;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ScanIPData {
    private Long id;

    /**
     * IP段编号
     */
    private Long ipRangeId;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * Ping支持
     *
     * 0 - 不支持
     * 1 - 支持
     */
    private Integer pingSupport;


    private Integer pingStatus;

    /**
     * SNMP支持
     *
     * 0 - 不支持
     * 1 - 支持
     */
    private Integer snmpSupport;

    private Integer snmpStatus;

    /**
     * TCP支持
     *
     * 0 - 不支持
     * 1 - 支持
     */
    private Integer tcpSupport;

    private Integer tcpStatus;

    private Integer snmpPort;

    /**
     * SNMP团体名
     */
    private String snmpCommunity;

    private String snmpVersion;

    /**
     * TCP端口
     */
    private Integer tcpPort;
}
