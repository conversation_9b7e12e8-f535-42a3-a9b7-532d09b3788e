package cn.iocoder.zj.framework.common.util.threadpool;


import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
public final class ThreadPool {
    private static final Logger LOGGER = LoggerFactory.getLogger(ThreadPool.class);
    private static final ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();

    static {
        threadPoolTaskExecutor.setCorePoolSize(10);
        threadPoolTaskExecutor.setMaxPoolSize(100);
        threadPoolTaskExecutor.setQueueCapacity(1000);
        threadPoolTaskExecutor.setKeepAliveSeconds(300);
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        threadPoolTaskExecutor.initialize();
    }

    public static void asyn(AsynRun asynRun) {
        String traceId = MDC.get("X-B3-TraceId");
        threadPoolTaskExecutor.execute(() -> {
            try {
                if (traceId != null && !traceId.isEmpty()) {
                    MDC.put("X-B3-TraceId", traceId);
                }
                asynRun.execute();
            } catch (Exception e) {
                LOGGER.error(e.getMessage(), e);
            }
        });
    }

    public static void main(String... args) throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(11); // 设为 11

        for (int i = 0; i <= 10; i++) {
            asyn(() -> {
                System.out.println("任务执行");
            });
            latch.countDown(); // 每个任务完成后计数
        }

        System.out.println("等待子线程运行结束");
        latch.await();
        System.out.println("子线程运行结束");
    }

    private ThreadPool() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    public interface AsynRun {
        void execute();
    }
}
