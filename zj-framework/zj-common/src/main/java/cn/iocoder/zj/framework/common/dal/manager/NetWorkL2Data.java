package cn.iocoder.zj.framework.common.dal.manager;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 **/
@TableName("monitor_network_l2")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "monitor_network_l2  | 二层网络实体")
public class NetWorkL2Data {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 二级网络名称
     */
    private String name;
    /**
     * 二级网络uuid
     */
    private String uuid;
    /**
     * 网卡
     */
    private String physicalInterface;
    /**
     * 二级网络类型
     */
    private String type;
    /**
     * vlan
     */
    private String vlan;
    /**
     * 虚拟网络标识
     */
    private Integer virtualNetworkId;
    /**
     * 地区名称
     */
    private String regionName;
    /**
     * 地区id
     */
    private Long regionId;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;

    private Long tenantId;

    private String typeName;

    private Date createTime;

    private Date updateTime;
}
