package cn.iocoder.zj.framework.common.enums;

import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
public enum BasicServiceType {
    BASIC_HOST("host", "物理机"),
    BASIC_VM("vm", "虚拟机"),
    BASIC_VPC("vpc", "虚拟网络"),
    BASIC_STORAGE("storage", "存储"),
    BASIC_STORAGE_POOL("storage_pool", "存储池"),
    BASIC_SNAPSHOT("snapshot", "快照"),
    BASIC_IMAGE("image", "镜像"),
    BASIC_VM_VIC("vm_nic", "云主机网络"),
    BASIC_HOST_VIC("host_nic", "物理机网络"),
    BASIC_SEC_GROUP("sec_group", "安全组"),
    BASIC_VOLUME_INFO("volume_info", "云盘"),
    BASIC_VOLUME_INFO_SNAPSHOT("volume_info_snapshot", "云硬盘快照"),
    BASIC_NET("net", "网络");


    private final String code;

    private final String desc;

    public static BasicServiceType fromCode(String code) {
        for (BasicServiceType typeEnum : values()) {
            if (typeEnum.code.equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    public String code() {
        return this.code;
    }

    public static List<String> getAllBasicTypeCodes() {
        List<String> codes = new ArrayList<>();
        for (BasicServiceType basicType : BasicServiceType.values()) {
            codes.add(basicType.code());
        }
        return codes;
    }

}
