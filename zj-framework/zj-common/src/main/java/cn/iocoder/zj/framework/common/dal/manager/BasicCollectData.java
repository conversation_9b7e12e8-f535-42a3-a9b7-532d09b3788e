package cn.iocoder.zj.framework.common.dal.manager;

import cn.iocoder.zj.framework.common.enums.MetricsType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class BasicCollectData {

    /**
     * 采集指标
     */
    private String metricsName;

    /**
     * 采集指标类型
     */
    private String metricsType;

    /**
     * 采集指标数据
     */
    private List<?> basicDataMap;
}
