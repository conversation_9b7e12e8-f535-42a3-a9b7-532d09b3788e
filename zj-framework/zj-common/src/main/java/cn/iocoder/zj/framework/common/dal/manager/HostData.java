package cn.iocoder.zj.framework.common.dal.manager;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;

import java.math.BigDecimal;
import java.util.Date;

import static io.swagger.v3.oas.annotations.media.Schema.AccessMode.READ_ONLY;
import static io.swagger.v3.oas.annotations.media.Schema.AccessMode.READ_WRITE;


/**
 * 物理机基础实体类
 */
@TableName("monitor_hardware_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "monitor_hardware_info  | 监控物理机实体")
public class HostData {
    /**
     * 主键ID
     */
    @TableId
    @Schema(title = "主键id", example = "87584674384", accessMode = READ_ONLY)
    private Long id;
    /**
     * 自己生成的uuid
     */
    @Schema(title = "自己生成的uuid", example = "87584674384", accessMode = READ_WRITE)
    private String uuid;
    /**
     * 物理机名称
     */
    @Schema(title = "物理机名称", example = "host-server-1", accessMode = READ_WRITE)
    private String name;

    /**
     * 物理机状态：
     * Enabled - 启用
     * Disabled - 禁用
     * PreMaintenance - 准备维护
     * Maintenance - 维护中
     */
    @Schema(title = "物理机状态", description = "Enabled - 启用, Disabled - 禁用, PreMaintenance - 准备维护, Maintenance - 维护中", example = "Enabled", accessMode = READ_WRITE)
    private String state;

    /**
     * 管理IP地址
     */
    @Schema(title = "管理IP地址", example = "*************", accessMode = READ_WRITE)
    private String ip;

    /**
     * 连接状态：
     * Connecting - 连接中
     * Connected - 已连接
     * Disconnected - 已断开
     */
    @Schema(title = "连接状态", description = "Connecting - 连接中, Connected - 已连接, Disconnected - 已断开", example = "Connected", accessMode = READ_WRITE)
    private String status;

    /**
     * 所属集群UUID
     */
    @Schema(title = "所属集群UUID", example = "cluster-uuid-12345", accessMode = READ_WRITE)
    private String clusterUuid;

    /**
     * 所属集群名称
     */
    @Schema(title = "所属集群名称", example = "Production Cluster", accessMode = READ_WRITE)
    private String clusterName;

    /**
     * CPU总核数
     */
    @Schema(title = "CPU总核数", example = "32", accessMode = READ_WRITE)
    private Long totalCpuCapacity;

    /**
     * CPU可用核数
     */
    @Schema(title = "CPU可用核数", example = "24", accessMode = READ_WRITE)
    private Long availableCpuCapacity;

    /**
     * CPU物理插槽数
     */
    @Schema(title = "CPU物理插槽数", example = "2", accessMode = READ_WRITE)
    private Integer cpuSockets;

    /**
     * CPU架构类型
     */
    @Schema(title = "CPU架构类型", example = "x86_64", accessMode = READ_WRITE)
    private String architecture;

    /**
     * 已分配CPU核数
     */
    @Schema(title = "已分配CPU核数", example = "8", accessMode = READ_WRITE)
    private Integer cpuNum;

    /**
     * 内存总容量（字节）
     */
    @Schema(title = "内存总容量", description = "单位：字节", example = "68719476736", accessMode = READ_WRITE)
    private Long totalMemoryCapacity;

    /**
     * 内存可用容量（字节）
     */
    @Schema(title = "内存可用容量", description = "单位：字节", example = "34359738368", accessMode = READ_WRITE)
    private Long availableMemoryCapacity;

    /**
     * 区域ID
     */
    @Schema(title = "区域ID", example = "1", accessMode = READ_WRITE)
    private Long regionId;

    /**
     * 租户ID
     */
    @Schema(title = "租户ID", example = "1001", accessMode = READ_WRITE)
    private Long tenantId;

    /**
     * 网络上行带宽（Mbps）
     */
    @Schema(title = "网络上行带宽", description = "单位：Mbps", example = "1000.0", accessMode = READ_WRITE)
    private BigDecimal bandwidthUpstream;

    /**
     * 网络下行带宽（Mbps）
     */
    @Schema(title = "网络下行带宽", description = "单位：Mbps", example = "1000.0", accessMode = READ_WRITE)
    private BigDecimal bandwidthDownstream;

    /**
     * 内存使用率（%）
     */
    @Schema(title = "内存使用率", description = "单位：%", example = "65.5", accessMode = READ_WRITE)
    private BigDecimal memoryUsed;

    /**
     * CPU使用率（%）
     */
    @Schema(title = "CPU使用率", description = "单位：%", example = "45.8", accessMode = READ_WRITE)
    private BigDecimal cpuUsed;

    /**
     * 网络包处理速率（pps）
     */
    @Schema(title = "网络包处理速率", description = "单位：pps", example = "5000.0", accessMode = READ_WRITE)
    private BigDecimal packetRate;

    /**
     * 磁盘使用率（%）
     */
    @Schema(title = "磁盘使用率", description = "单位：%", example = "70.5", accessMode = READ_WRITE)
    private BigDecimal diskUsed;

    /**
     * 已用磁盘容量（字节）
     */
    @Schema(title = "已用磁盘容量", description = "单位：字节", example = "536870912000", accessMode = READ_WRITE)
    private BigDecimal diskUsedBytes;

    /**
     * 可用磁盘容量（字节）
     */
    @Schema(title = "可用磁盘容量", description = "单位：字节", example = "214748364800", accessMode = READ_WRITE)
    private BigDecimal diskFreeBytes;

    /**
     * 磁盘总容量（字节）
     */
    @Schema(title = "磁盘总容量", description = "单位：字节", example = "751619276800", accessMode = READ_WRITE)
    private BigDecimal totalDiskCapacity;

    /**
     * 所属平台ID
     */
    @Schema(title = "所属平台ID", example = "1", accessMode = READ_WRITE)
    private Long platformId;

    /**
     * 所属平台名称
     */
    @Schema(title = "所属平台名称", example = "OpenStack", accessMode = READ_WRITE)
    private String platformName;

    /**
     * 删除标记（0-未删除，1-已删除）
     */
    @Schema(title = "删除标记", description = "0-未删除，1-已删除", example = "0", accessMode = READ_WRITE)
    private Integer deleted;

    /**
     * 物理机类型
     */
    @Schema(title = "物理机类型", example = "compute", accessMode = READ_WRITE)
    private String typeName;

    /**
     * 维护状态（0-正常，1-维护中）
     */
    @Schema(title = "维护状态", description = "0-正常，1-维护中", example = "0", accessMode = READ_WRITE)
    private Integer isMaintain;

    /**
     * 创建时间
     */
    @Schema(title = "创建时间", example = "2023-01-01T12:00:00", accessMode = READ_ONLY)
    @CreatedDate
    private Date createTime;

    /**
     * 虚拟机列表（JSON格式）
     */
    @Schema(title = "虚拟机列表", description = "JSON格式", example = "[{\"id\":\"vm-001\",\"name\":\"web-server\"}]", accessMode = READ_WRITE)
    private String vms;

    /**
     * IPMI地址
     */
    @Schema(title = "IPMI地址", example = "*************", accessMode = READ_WRITE)
    private String ipmi;

    /**
     * 服务器制造商
     */
    @Schema(title = "服务器制造商", example = "Dell", accessMode = READ_WRITE)
    private String manufacturer;

    /**
     * 虚拟内存总量（字节）
     */
    @Schema(title = "虚拟内存总量", description = "单位：字节", example = "8589934592", accessMode = READ_WRITE)
    private BigDecimal totalVirtualMemory;

    /**
     * CPU超分比率（%）
     */
    @Schema(title = "CPU超分比率", description = "单位：%", example = "150.0", accessMode = READ_WRITE)
    private BigDecimal cpuOverPercent;

    /**
     * 内存超分比率（%）
     */
    @Schema(title = "内存超分比率", description = "单位：%", example = "120.0", accessMode = READ_WRITE)
    private BigDecimal memoryOverPercent;

    /**
     * 数据中心
     */
    @Schema(title = "数据中心", example = "DC01", accessMode = READ_WRITE)
    private String manager;

    /**
     * 可用区
     */
    @Schema(title = "可用区", example = "AZ-1", accessMode = READ_WRITE)
    private String availableManager;

    /**
     * 当前CPU超分率（%）
     */
    @Schema(title = "当前CPU超分率", description = "单位：%", example = "125.5", accessMode = READ_WRITE)
    private BigDecimal cpuCommitRate;

    /**
     * 当前内存超分率（%）
     */
    @Schema(title = "当前内存超分率", description = "单位：%", example = "110.5", accessMode = READ_WRITE)
    private BigDecimal memoryCommitRate;

    /**
     * 系统预留内存（字节）
     */
    @Schema(title = "系统预留内存", description = "单位：字节", example = "4294967296", accessMode = READ_WRITE)
    private BigDecimal reservedMemory;

    /**
     * 服务器品牌
     */
    @Schema(title = "服务器品牌", example = "PowerEdge", accessMode = READ_WRITE)
    private String brandName;

    /**
     * 服务器型号
     */
    @Schema(title = "服务器型号", example = "R740", accessMode = READ_WRITE)
    private String model;

    /**
     * 服务器序列号
     */
    @Schema(title = "服务器序列号", example = "SN12345678", accessMode = READ_WRITE)
    private String serialNumber;

    /**
     * CPU型号描述
     */
    @Schema(title = "CPU型号描述", example = "Intel Xeon Gold 6230R 2.1GHz", accessMode = READ_WRITE)
    private String cpuType;

    @Schema(title = "创建者", example = "admin", accessMode = READ_WRITE)
    private String creator;
    @Schema(title = "修改者", example = "admin", accessMode = READ_WRITE)
    private String updater;


    @Schema(title = "Record update time", example = "1612198922000", accessMode = READ_ONLY)
    @CreatedDate
    private Date updateTime;

    @Schema(title = "标签", example = "")
    private String tag;
}
