package cn.iocoder.zj.framework.common.enums;

import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
public enum OtherEnum {
    ADVANCES_PING ("advances_ping", "ping进度"),
    ADVANCES_TCP ("advances_tcp", "tcp进度"),
    ADVANCES_SNMP ("advances_snmp", "snmp进度"),
    PING("ping", "自动发现-ping"),
    TCP("tcp", "自动发现-tcp"),
    SNMP("snmp", "自动发现-snmp");




    private final String code;

    private final String desc;

    public static OtherEnum fromCode(String code) {
        for (OtherEnum typeEnum : values()) {
            if (typeEnum.code.equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    public String code() {
        return this.code;
    }

    public static List<String> getAllBasicTypeCodes() {
        List<String> codes = new ArrayList<>();
        for (OtherEnum basicType : OtherEnum.values()) {
            codes.add(basicType.code());
        }
        return codes;
    }
}
