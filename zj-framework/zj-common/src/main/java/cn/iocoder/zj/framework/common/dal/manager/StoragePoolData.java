package cn.iocoder.zj.framework.common.dal.manager;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@TableName("monitor_storage_pool")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "monitor_storage_pool  | 存储池表")
public class StoragePoolData {

    @TableId
    @Schema(title = "主键id", example = "87584674384")
    private Long id;
    /**
     * 存储池uuid
     */
    private String uuid;
    /**
     * 存储名称
     */
    private String name;
    /**
     * 存储池类型
     */
    private String type;
    /**
     * 存储名称
     */
    private String description;
    /**
     * 存储池创建时间
     */
    private Date vCreateDate;
    /**
     * 存储池修改时间
     */
    private Date lastOpDate;
    /**
     * 已使用容量
     */
    private BigDecimal usedCapacity;
    /**
     * 虚拟可用容量
     */
    private BigDecimal availableCapacity;
    /**
     * 总容量
     */
    private BigDecimal totalCapacity;
    /**
     * 主存储uuid
     */
    private String storageUuid;
    /**
     * 数据安全类型
     */
    private String securityPolicy;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;

}
