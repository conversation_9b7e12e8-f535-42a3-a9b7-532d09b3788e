// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: cluster_msg.proto

package cn.iocoder.zj.framework.common.message;

@SuppressWarnings("all")
public final class ClusterMsg {
  private ClusterMsg() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code cn.iocoder.zj.framework.common.message.MessageType}
   */
  public enum MessageType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     * 认证
     * </pre>
     *
     * <code>AUTH = 0;</code>
     */
    AUTH(0),
    /**
     * <pre>
     * 心跳
     * </pre>
     *
     * <code>HEART = 1;</code>
     */
    HEART(1),
    /**
     * <pre>
     * 更改
     * </pre>
     *
     * <code>UPDATE = 2;</code>
     */
    UPDATE(2),
    /**
     * <pre>
     * 初始化配置token
     * </pre>
     *
     * <code>INFO = 3;</code>
     */
    INFO(3),
    /**
     * <pre>
     * 基础数据详情
     * </pre>
     *
     * <code>BASIC = 4;</code>
     */
    BASIC(4),
    /**
     * <pre>
     * CPU指令
     * </pre>
     *
     * <code>CPU_TASK = 5;</code>
     */
    CPU_TASK(5),
    /**
     * <pre>
     * DISK指令
     * </pre>
     *
     * <code>DISK_TASK = 6;</code>
     */
    DISK_TASK(6),
    /**
     * <pre>
     * MEM指令
     * </pre>
     *
     * <code>MEM_TASK = 7;</code>
     */
    MEM_TASK(7),
    /**
     * <pre>
     * NET指令
     * </pre>
     *
     * <code>NET_TASK = 8;</code>
     */
    NET_TASK(8),
    /**
     * <pre>
     * 上线
     * </pre>
     *
     * <code>ONLINE = 9;</code>
     */
    ONLINE(9),
    /**
     * <pre>
     * 下线
     * </pre>
     *
     * <code>OFFLINE = 10;</code>
     */
    OFFLINE(10),
    /**
     * <pre>
     * 启动
     * </pre>
     *
     * <code>SCHEDULED_START = 11;</code>
     */
    SCHEDULED_START(11),
    /**
     * <pre>
     * 停止
     * </pre>
     *
     * <code>SCHEDULED_STOP = 12;</code>
     */
    SCHEDULED_STOP(12),
    /**
     * <pre>
     * 修改
     * </pre>
     *
     * <code>SCHEDULED_MODIFY = 13;</code>
     */
    SCHEDULED_MODIFY(13),
    /**
     * <pre>
     * 采集成功
     * </pre>
     *
     * <code>SUCCESS = 14;</code>
     */
    SUCCESS(14),
    /**
     * <pre>
     * 采集失败
     * </pre>
     *
     * <code>FAIL = 15;</code>
     */
    FAIL(15),
    /**
     * <pre>
     * 探测
     * </pre>
     *
     * <code>DETECT = 16;</code>
     */
    DETECT(16),
    /**
     * <pre>
     * 采集定时任务状态
     * </pre>
     *
     * <code>SCHEDULED_INFO = 17;</code>
     */
    SCHEDULED_INFO(17),
    /**
     * <pre>
     *告警
     * </pre>
     *
     * <code>ALERTER = 18;</code>
     */
    ALERTER(18),
    /**
     * <pre>
     * 其他
     * </pre>
     *
     * <code>OTHER = 19;</code>
     */
    OTHER(19),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     * 认证
     * </pre>
     *
     * <code>AUTH = 0;</code>
     */
    public static final int AUTH_VALUE = 0;
    /**
     * <pre>
     * 心跳
     * </pre>
     *
     * <code>HEART = 1;</code>
     */
    public static final int HEART_VALUE = 1;
    /**
     * <pre>
     * 更改
     * </pre>
     *
     * <code>UPDATE = 2;</code>
     */
    public static final int UPDATE_VALUE = 2;
    /**
     * <pre>
     * 初始化配置token
     * </pre>
     *
     * <code>INFO = 3;</code>
     */
    public static final int INFO_VALUE = 3;
    /**
     * <pre>
     * 基础数据详情
     * </pre>
     *
     * <code>BASIC = 4;</code>
     */
    public static final int BASIC_VALUE = 4;
    /**
     * <pre>
     * CPU指令
     * </pre>
     *
     * <code>CPU_TASK = 5;</code>
     */
    public static final int CPU_TASK_VALUE = 5;
    /**
     * <pre>
     * DISK指令
     * </pre>
     *
     * <code>DISK_TASK = 6;</code>
     */
    public static final int DISK_TASK_VALUE = 6;
    /**
     * <pre>
     * MEM指令
     * </pre>
     *
     * <code>MEM_TASK = 7;</code>
     */
    public static final int MEM_TASK_VALUE = 7;
    /**
     * <pre>
     * NET指令
     * </pre>
     *
     * <code>NET_TASK = 8;</code>
     */
    public static final int NET_TASK_VALUE = 8;
    /**
     * <pre>
     * 上线
     * </pre>
     *
     * <code>ONLINE = 9;</code>
     */
    public static final int ONLINE_VALUE = 9;
    /**
     * <pre>
     * 下线
     * </pre>
     *
     * <code>OFFLINE = 10;</code>
     */
    public static final int OFFLINE_VALUE = 10;
    /**
     * <pre>
     * 启动
     * </pre>
     *
     * <code>SCHEDULED_START = 11;</code>
     */
    public static final int SCHEDULED_START_VALUE = 11;
    /**
     * <pre>
     * 停止
     * </pre>
     *
     * <code>SCHEDULED_STOP = 12;</code>
     */
    public static final int SCHEDULED_STOP_VALUE = 12;
    /**
     * <pre>
     * 修改
     * </pre>
     *
     * <code>SCHEDULED_MODIFY = 13;</code>
     */
    public static final int SCHEDULED_MODIFY_VALUE = 13;
    /**
     * <pre>
     * 采集成功
     * </pre>
     *
     * <code>SUCCESS = 14;</code>
     */
    public static final int SUCCESS_VALUE = 14;
    /**
     * <pre>
     * 采集失败
     * </pre>
     *
     * <code>FAIL = 15;</code>
     */
    public static final int FAIL_VALUE = 15;
    /**
     * <pre>
     * 探测
     * </pre>
     *
     * <code>DETECT = 16;</code>
     */
    public static final int DETECT_VALUE = 16;
    /**
     * <pre>
     * 采集定时任务状态
     * </pre>
     *
     * <code>SCHEDULED_INFO = 17;</code>
     */
    public static final int SCHEDULED_INFO_VALUE = 17;
    /**
     * <pre>
     *告警
     * </pre>
     *
     * <code>ALERTER = 18;</code>
     */
    public static final int ALERTER_VALUE = 18;
    /**
     * <pre>
     * 其他
     * </pre>
     *
     * <code>OTHER = 19;</code>
     */
    public static final int OTHER_VALUE = 19;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static MessageType valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static MessageType forNumber(int value) {
      switch (value) {
        case 0: return AUTH;
        case 1: return HEART;
        case 2: return UPDATE;
        case 3: return INFO;
        case 4: return BASIC;
        case 5: return CPU_TASK;
        case 6: return DISK_TASK;
        case 7: return MEM_TASK;
        case 8: return NET_TASK;
        case 9: return ONLINE;
        case 10: return OFFLINE;
        case 11: return SCHEDULED_START;
        case 12: return SCHEDULED_STOP;
        case 13: return SCHEDULED_MODIFY;
        case 14: return SUCCESS;
        case 15: return FAIL;
        case 16: return DETECT;
        case 17: return SCHEDULED_INFO;
        case 18: return ALERTER;
        case 19: return OTHER;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<MessageType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        MessageType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<MessageType>() {
            public MessageType findValueByNumber(int number) {
              return MessageType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.iocoder.zj.framework.common.message.ClusterMsg.getDescriptor().getEnumTypes().get(0);
    }

    private static final MessageType[] VALUES = values();

    public static MessageType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private MessageType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.iocoder.zj.framework.common.message.MessageType)
  }

  public interface MessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:cn.iocoder.zj.framework.common.message.Message)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 客户端id
     * </pre>
     *
     * <code>string clientId = 1;</code>
     * @return The clientId.
     */
    java.lang.String getClientId();
    /**
     * <pre>
     * 客户端id
     * </pre>
     *
     * <code>string clientId = 1;</code>
     * @return The bytes for clientId.
     */
    com.google.protobuf.ByteString
        getClientIdBytes();

    /**
     * <pre>
     * 消息类型
     * </pre>
     *
     * <code>.cn.iocoder.zj.framework.common.message.MessageType type = 2;</code>
     * @return The enum numeric value on the wire for type.
     */
    int getTypeValue();
    /**
     * <pre>
     * 消息类型
     * </pre>
     *
     * <code>.cn.iocoder.zj.framework.common.message.MessageType type = 2;</code>
     * @return The type.
     */
    cn.iocoder.zj.framework.common.message.ClusterMsg.MessageType getType();

    /**
     * <pre>
     * monitoring metrics
     * </pre>
     *
     * <code>string metrics = 3;</code>
     * @return The metrics.
     */
    java.lang.String getMetrics();
    /**
     * <pre>
     * monitoring metrics
     * </pre>
     *
     * <code>string metrics = 3;</code>
     * @return The bytes for metrics.
     */
    com.google.protobuf.ByteString
        getMetricsBytes();

    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>string data = 4;</code>
     * @return The data.
     */
    java.lang.String getData();
    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>string data = 4;</code>
     * @return The bytes for data.
     */
    com.google.protobuf.ByteString
        getDataBytes();

    /**
     * <pre>
     * 采集时间
     * </pre>
     *
     * <code>uint64 time = 5;</code>
     * @return The time.
     */
    long getTime();
  }
  /**
   * Protobuf type {@code cn.iocoder.zj.framework.common.message.Message}
   */
  public static final class Message extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:cn.iocoder.zj.framework.common.message.Message)
      MessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Message.newBuilder() to construct.
    private Message(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Message() {
      clientId_ = "";
      type_ = 0;
      metrics_ = "";
      data_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Message();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Message(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              clientId_ = s;
              break;
            }
            case 16: {
              int rawValue = input.readEnum();

              type_ = rawValue;
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              metrics_ = s;
              break;
            }
            case 34: {
              java.lang.String s = input.readStringRequireUtf8();

              data_ = s;
              break;
            }
            case 40: {

              time_ = input.readUInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.iocoder.zj.framework.common.message.ClusterMsg.internal_static_cn_iocoder_zj_framework_common_message_Message_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.iocoder.zj.framework.common.message.ClusterMsg.internal_static_cn_iocoder_zj_framework_common_message_Message_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.iocoder.zj.framework.common.message.ClusterMsg.Message.class, cn.iocoder.zj.framework.common.message.ClusterMsg.Message.Builder.class);
    }

    public static final int CLIENTID_FIELD_NUMBER = 1;
    private volatile java.lang.Object clientId_;
    /**
     * <pre>
     * 客户端id
     * </pre>
     *
     * <code>string clientId = 1;</code>
     * @return The clientId.
     */
    @java.lang.Override
    public java.lang.String getClientId() {
      java.lang.Object ref = clientId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        clientId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 客户端id
     * </pre>
     *
     * <code>string clientId = 1;</code>
     * @return The bytes for clientId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getClientIdBytes() {
      java.lang.Object ref = clientId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        clientId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TYPE_FIELD_NUMBER = 2;
    private int type_;
    /**
     * <pre>
     * 消息类型
     * </pre>
     *
     * <code>.cn.iocoder.zj.framework.common.message.MessageType type = 2;</code>
     * @return The enum numeric value on the wire for type.
     */
    @java.lang.Override public int getTypeValue() {
      return type_;
    }
    /**
     * <pre>
     * 消息类型
     * </pre>
     *
     * <code>.cn.iocoder.zj.framework.common.message.MessageType type = 2;</code>
     * @return The type.
     */
    @java.lang.Override public cn.iocoder.zj.framework.common.message.ClusterMsg.MessageType getType() {
      @SuppressWarnings("deprecation")
      cn.iocoder.zj.framework.common.message.ClusterMsg.MessageType result = cn.iocoder.zj.framework.common.message.ClusterMsg.MessageType.valueOf(type_);
      return result == null ? cn.iocoder.zj.framework.common.message.ClusterMsg.MessageType.UNRECOGNIZED : result;
    }

    public static final int METRICS_FIELD_NUMBER = 3;
    private volatile java.lang.Object metrics_;
    /**
     * <pre>
     * monitoring metrics
     * </pre>
     *
     * <code>string metrics = 3;</code>
     * @return The metrics.
     */
    @java.lang.Override
    public java.lang.String getMetrics() {
      java.lang.Object ref = metrics_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        metrics_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * monitoring metrics
     * </pre>
     *
     * <code>string metrics = 3;</code>
     * @return The bytes for metrics.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getMetricsBytes() {
      java.lang.Object ref = metrics_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        metrics_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DATA_FIELD_NUMBER = 4;
    private volatile java.lang.Object data_;
    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>string data = 4;</code>
     * @return The data.
     */
    @java.lang.Override
    public java.lang.String getData() {
      java.lang.Object ref = data_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        data_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>string data = 4;</code>
     * @return The bytes for data.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDataBytes() {
      java.lang.Object ref = data_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        data_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TIME_FIELD_NUMBER = 5;
    private long time_;
    /**
     * <pre>
     * 采集时间
     * </pre>
     *
     * <code>uint64 time = 5;</code>
     * @return The time.
     */
    @java.lang.Override
    public long getTime() {
      return time_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(clientId_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, clientId_);
      }
      if (type_ != cn.iocoder.zj.framework.common.message.ClusterMsg.MessageType.AUTH.getNumber()) {
        output.writeEnum(2, type_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(metrics_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, metrics_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(data_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, data_);
      }
      if (time_ != 0L) {
        output.writeUInt64(5, time_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(clientId_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, clientId_);
      }
      if (type_ != cn.iocoder.zj.framework.common.message.ClusterMsg.MessageType.AUTH.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, type_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(metrics_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, metrics_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(data_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, data_);
      }
      if (time_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(5, time_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.iocoder.zj.framework.common.message.ClusterMsg.Message)) {
        return super.equals(obj);
      }
      cn.iocoder.zj.framework.common.message.ClusterMsg.Message other = (cn.iocoder.zj.framework.common.message.ClusterMsg.Message) obj;

      if (!getClientId()
          .equals(other.getClientId())) return false;
      if (type_ != other.type_) return false;
      if (!getMetrics()
          .equals(other.getMetrics())) return false;
      if (!getData()
          .equals(other.getData())) return false;
      if (getTime()
          != other.getTime()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CLIENTID_FIELD_NUMBER;
      hash = (53 * hash) + getClientId().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + type_;
      hash = (37 * hash) + METRICS_FIELD_NUMBER;
      hash = (53 * hash) + getMetrics().hashCode();
      hash = (37 * hash) + DATA_FIELD_NUMBER;
      hash = (53 * hash) + getData().hashCode();
      hash = (37 * hash) + TIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTime());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.iocoder.zj.framework.common.message.ClusterMsg.Message parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.iocoder.zj.framework.common.message.ClusterMsg.Message parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.iocoder.zj.framework.common.message.ClusterMsg.Message parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.iocoder.zj.framework.common.message.ClusterMsg.Message parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.iocoder.zj.framework.common.message.ClusterMsg.Message parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.iocoder.zj.framework.common.message.ClusterMsg.Message parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.iocoder.zj.framework.common.message.ClusterMsg.Message parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.iocoder.zj.framework.common.message.ClusterMsg.Message parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.iocoder.zj.framework.common.message.ClusterMsg.Message parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.iocoder.zj.framework.common.message.ClusterMsg.Message parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.iocoder.zj.framework.common.message.ClusterMsg.Message parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.iocoder.zj.framework.common.message.ClusterMsg.Message parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.iocoder.zj.framework.common.message.ClusterMsg.Message prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code cn.iocoder.zj.framework.common.message.Message}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:cn.iocoder.zj.framework.common.message.Message)
        cn.iocoder.zj.framework.common.message.ClusterMsg.MessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.iocoder.zj.framework.common.message.ClusterMsg.internal_static_cn_iocoder_zj_framework_common_message_Message_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.iocoder.zj.framework.common.message.ClusterMsg.internal_static_cn_iocoder_zj_framework_common_message_Message_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.iocoder.zj.framework.common.message.ClusterMsg.Message.class, cn.iocoder.zj.framework.common.message.ClusterMsg.Message.Builder.class);
      }

      // Construct using cn.iocoder.zj.framework.common.message.ClusterMsg.Message.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        clientId_ = "";

        type_ = 0;

        metrics_ = "";

        data_ = "";

        time_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.iocoder.zj.framework.common.message.ClusterMsg.internal_static_cn_iocoder_zj_framework_common_message_Message_descriptor;
      }

      @java.lang.Override
      public cn.iocoder.zj.framework.common.message.ClusterMsg.Message getDefaultInstanceForType() {
        return cn.iocoder.zj.framework.common.message.ClusterMsg.Message.getDefaultInstance();
      }

      @java.lang.Override
      public cn.iocoder.zj.framework.common.message.ClusterMsg.Message build() {
        cn.iocoder.zj.framework.common.message.ClusterMsg.Message result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public cn.iocoder.zj.framework.common.message.ClusterMsg.Message buildPartial() {
        cn.iocoder.zj.framework.common.message.ClusterMsg.Message result = new cn.iocoder.zj.framework.common.message.ClusterMsg.Message(this);
        result.clientId_ = clientId_;
        result.type_ = type_;
        result.metrics_ = metrics_;
        result.data_ = data_;
        result.time_ = time_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.iocoder.zj.framework.common.message.ClusterMsg.Message) {
          return mergeFrom((cn.iocoder.zj.framework.common.message.ClusterMsg.Message)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.iocoder.zj.framework.common.message.ClusterMsg.Message other) {
        if (other == cn.iocoder.zj.framework.common.message.ClusterMsg.Message.getDefaultInstance()) return this;
        if (!other.getClientId().isEmpty()) {
          clientId_ = other.clientId_;
          onChanged();
        }
        if (other.type_ != 0) {
          setTypeValue(other.getTypeValue());
        }
        if (!other.getMetrics().isEmpty()) {
          metrics_ = other.metrics_;
          onChanged();
        }
        if (!other.getData().isEmpty()) {
          data_ = other.data_;
          onChanged();
        }
        if (other.getTime() != 0L) {
          setTime(other.getTime());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.iocoder.zj.framework.common.message.ClusterMsg.Message parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.iocoder.zj.framework.common.message.ClusterMsg.Message) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private java.lang.Object clientId_ = "";
      /**
       * <pre>
       * 客户端id
       * </pre>
       *
       * <code>string clientId = 1;</code>
       * @return The clientId.
       */
      public java.lang.String getClientId() {
        java.lang.Object ref = clientId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          clientId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 客户端id
       * </pre>
       *
       * <code>string clientId = 1;</code>
       * @return The bytes for clientId.
       */
      public com.google.protobuf.ByteString
          getClientIdBytes() {
        java.lang.Object ref = clientId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          clientId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 客户端id
       * </pre>
       *
       * <code>string clientId = 1;</code>
       * @param value The clientId to set.
       * @return This builder for chaining.
       */
      public Builder setClientId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        clientId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 客户端id
       * </pre>
       *
       * <code>string clientId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearClientId() {
        
        clientId_ = getDefaultInstance().getClientId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 客户端id
       * </pre>
       *
       * <code>string clientId = 1;</code>
       * @param value The bytes for clientId to set.
       * @return This builder for chaining.
       */
      public Builder setClientIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        clientId_ = value;
        onChanged();
        return this;
      }

      private int type_ = 0;
      /**
       * <pre>
       * 消息类型
       * </pre>
       *
       * <code>.cn.iocoder.zj.framework.common.message.MessageType type = 2;</code>
       * @return The enum numeric value on the wire for type.
       */
      @java.lang.Override public int getTypeValue() {
        return type_;
      }
      /**
       * <pre>
       * 消息类型
       * </pre>
       *
       * <code>.cn.iocoder.zj.framework.common.message.MessageType type = 2;</code>
       * @param value The enum numeric value on the wire for type to set.
       * @return This builder for chaining.
       */
      public Builder setTypeValue(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 消息类型
       * </pre>
       *
       * <code>.cn.iocoder.zj.framework.common.message.MessageType type = 2;</code>
       * @return The type.
       */
      @java.lang.Override
      public cn.iocoder.zj.framework.common.message.ClusterMsg.MessageType getType() {
        @SuppressWarnings("deprecation")
        cn.iocoder.zj.framework.common.message.ClusterMsg.MessageType result = cn.iocoder.zj.framework.common.message.ClusterMsg.MessageType.valueOf(type_);
        return result == null ? cn.iocoder.zj.framework.common.message.ClusterMsg.MessageType.UNRECOGNIZED : result;
      }
      /**
       * <pre>
       * 消息类型
       * </pre>
       *
       * <code>.cn.iocoder.zj.framework.common.message.MessageType type = 2;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(cn.iocoder.zj.framework.common.message.ClusterMsg.MessageType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        type_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 消息类型
       * </pre>
       *
       * <code>.cn.iocoder.zj.framework.common.message.MessageType type = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object metrics_ = "";
      /**
       * <pre>
       * monitoring metrics
       * </pre>
       *
       * <code>string metrics = 3;</code>
       * @return The metrics.
       */
      public java.lang.String getMetrics() {
        java.lang.Object ref = metrics_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          metrics_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * monitoring metrics
       * </pre>
       *
       * <code>string metrics = 3;</code>
       * @return The bytes for metrics.
       */
      public com.google.protobuf.ByteString
          getMetricsBytes() {
        java.lang.Object ref = metrics_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          metrics_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * monitoring metrics
       * </pre>
       *
       * <code>string metrics = 3;</code>
       * @param value The metrics to set.
       * @return This builder for chaining.
       */
      public Builder setMetrics(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        metrics_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * monitoring metrics
       * </pre>
       *
       * <code>string metrics = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearMetrics() {
        
        metrics_ = getDefaultInstance().getMetrics();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * monitoring metrics
       * </pre>
       *
       * <code>string metrics = 3;</code>
       * @param value The bytes for metrics to set.
       * @return This builder for chaining.
       */
      public Builder setMetricsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        metrics_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object data_ = "";
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>string data = 4;</code>
       * @return The data.
       */
      public java.lang.String getData() {
        java.lang.Object ref = data_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          data_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>string data = 4;</code>
       * @return The bytes for data.
       */
      public com.google.protobuf.ByteString
          getDataBytes() {
        java.lang.Object ref = data_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          data_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>string data = 4;</code>
       * @param value The data to set.
       * @return This builder for chaining.
       */
      public Builder setData(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        data_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>string data = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearData() {
        
        data_ = getDefaultInstance().getData();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>string data = 4;</code>
       * @param value The bytes for data to set.
       * @return This builder for chaining.
       */
      public Builder setDataBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        data_ = value;
        onChanged();
        return this;
      }

      private long time_ ;
      /**
       * <pre>
       * 采集时间
       * </pre>
       *
       * <code>uint64 time = 5;</code>
       * @return The time.
       */
      @java.lang.Override
      public long getTime() {
        return time_;
      }
      /**
       * <pre>
       * 采集时间
       * </pre>
       *
       * <code>uint64 time = 5;</code>
       * @param value The time to set.
       * @return This builder for chaining.
       */
      public Builder setTime(long value) {
        
        time_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 采集时间
       * </pre>
       *
       * <code>uint64 time = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearTime() {
        
        time_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:cn.iocoder.zj.framework.common.message.Message)
    }

    // @@protoc_insertion_point(class_scope:cn.iocoder.zj.framework.common.message.Message)
    private static final cn.iocoder.zj.framework.common.message.ClusterMsg.Message DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.iocoder.zj.framework.common.message.ClusterMsg.Message();
    }

    public static cn.iocoder.zj.framework.common.message.ClusterMsg.Message getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Message>
        PARSER = new com.google.protobuf.AbstractParser<Message>() {
      @java.lang.Override
      public Message parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Message(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Message> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Message> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public cn.iocoder.zj.framework.common.message.ClusterMsg.Message getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_iocoder_zj_framework_common_message_Message_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_iocoder_zj_framework_common_message_Message_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\021cluster_msg.proto\022&cn.iocoder.zj.frame" +
      "work.common.message\"\213\001\n\007Message\022\020\n\010clien" +
      "tId\030\001 \001(\t\022A\n\004type\030\002 \001(\01623.cn.iocoder.zj." +
      "framework.common.message.MessageType\022\017\n\007" +
      "metrics\030\003 \001(\t\022\014\n\004data\030\004 \001(\t\022\014\n\004time\030\005 \001(" +
      "\004*\243\002\n\013MessageType\022\010\n\004AUTH\020\000\022\t\n\005HEART\020\001\022\n" +
      "\n\006UPDATE\020\002\022\010\n\004INFO\020\003\022\t\n\005BASIC\020\004\022\014\n\010CPU_T" +
      "ASK\020\005\022\r\n\tDISK_TASK\020\006\022\014\n\010MEM_TASK\020\007\022\014\n\010NE" +
      "T_TASK\020\010\022\n\n\006ONLINE\020\t\022\013\n\007OFFLINE\020\n\022\023\n\017SCH" +
      "EDULED_START\020\013\022\022\n\016SCHEDULED_STOP\020\014\022\024\n\020SC" +
      "HEDULED_MODIFY\020\r\022\013\n\007SUCCESS\020\016\022\010\n\004FAIL\020\017\022" +
      "\n\n\006DETECT\020\020\022\022\n\016SCHEDULED_INFO\020\021\022\013\n\007ALERT" +
      "ER\020\022\022\t\n\005OTHER\020\023b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_cn_iocoder_zj_framework_common_message_Message_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_cn_iocoder_zj_framework_common_message_Message_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_iocoder_zj_framework_common_message_Message_descriptor,
        new java.lang.String[] { "ClientId", "Type", "Metrics", "Data", "Time", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
