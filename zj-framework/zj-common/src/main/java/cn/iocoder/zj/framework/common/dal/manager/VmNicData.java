package cn.iocoder.zj.framework.common.dal.manager;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


/**
 * 虚拟机网络设施信息
 */


@TableName("monitor_host_nic")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VmNicData {

    @TableId
    @Schema(title = "主键id", example = "87584674384")
    private Long id;
    /**
     * uuid
     */
    private String uuid;
    /**
     * 名称
     */
    private String name;
    /**
     * 云主机uuid
     */
    private String hostUuid;
    /**
     * ipV6
     */
    private String ip6;
    /**
     * ip
     */
    private String ip;
    /**
     * mac
     */
    private String mac;
    /**
     * 驱动
     */
    private String driver;
    /**
     * 在经典网络
     */
    private Byte inClassicNetwork;
    /**
     * 网络uuid
     */
    private String networkUuid;
    /**
     * 三级网络名称
     */
    private String networkName;
    /**
     * 队列数量
     */
    private String numQueues;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 更新时间
     */
    private Date updateTime;

}
