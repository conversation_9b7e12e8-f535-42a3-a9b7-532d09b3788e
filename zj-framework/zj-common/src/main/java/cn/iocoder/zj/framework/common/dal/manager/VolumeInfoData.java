package cn.iocoder.zj.framework.common.dal.manager;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@TableName("monitor_volume_info")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VolumeInfoData {
    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 云盘uuid
     */
    private String uuid;
    /**
     * 平台id
     */
    private String platformId;
    /**
     * 平台名称
     */
    private String platformName;
    /**
     * 云盘名称
     */
    private String name;
    /**
     * 云盘详细描述
     */
    private String description;
    /**
     * 主存储uuid
     */
    private String primaryStorageUuid;
    /**
     * 主存储名称
     */
    private String primaryStorageName;
    /**
     * 主存储类型
     */
    private String primaryStorageType;
    /**
     * 云主机uuid
     */
    private String vmInstanceUuid;
    /**
     * 云主机名称
     */
    private String vmInstanceName;
    /**
     * 云盘类型，数据云盘/根云盘
     */
    private String type;
    /**
     * 云盘格式
     */
    private String format;
    /**
     * 云盘大小
     */
    private Long size;
    /**
     * 云盘真实大小
     */
    private Long actualSize;
    /**
     * 云盘是否开启
     */
    private String state;
    /**
     * 云盘状态
     */
    private String status;

    private String actualRatio;

    private Long actualFree;

    private Long actualUse;

    // 云盘创建时间
    private Date vCreateDate;

    // 云盘创建时间
    private Date vUpdateDate;

    // 最大iops max_iops
    private Long maxIops;

    //吞吐量
    private BigDecimal throughput;

    //介质类型
    private String mediaType;

    //挂载状态
    private Boolean isMount;

    private Integer deleted;

    private Date createTime;

    private String tag;
}
