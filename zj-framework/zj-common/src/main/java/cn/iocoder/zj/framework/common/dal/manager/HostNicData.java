package cn.iocoder.zj.framework.common.dal.manager;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


/**
 * 硬件网络设施信息
 */
@TableName("monitor_hardware_nic")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HostNicData {

    /**
     * 主键
     */
    @TableId
    @Schema(title = "主键id", example = "87584674384")
    private Long id;
    /**
     * 网卡uuid(v3)
     */
    private String uuid;
    /**
     * 宿主机uuid
     */
    private String hardwareUuid;
    /**
     * mac
     */
    private String mac;
    /**
     * 网卡类型
     */
    private String networkType;
    /**
     * ip地址
     */
    private String ipAddresses;
    /**
     * ip子网
     */
    private String ipSubnet;
    /**
     * 二层网络uuid
     */
    private String l2NetworkUuid;
    /**
     * 二层网络名称
     */
    private String l2NetworkName;
    /**
     * 是否离线
     */
    private Boolean state;


    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 更新时间
     */
    private Date updateTime;

}
