package cn.iocoder.zj.framework.common.dal.manager;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@TableName("monitor_storage_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "monitor_storage_info  | 存储设备信息表")
public class StorageData {

    @TableId
    @Schema(title = "主键id", example = "87584674384")
    private Long id;

    @Schema(title = "存储名称", example = "ceph-storage-01")
    private String name;

    @Schema(title = "存储UUID", example = "550e8400-e29b-41d4-a716-************")
    private String uuid;

    @Schema(title = "存储URL", example = "ceph://*************:6789")
    private String url;

    @Schema(title = "存储状态", example = "Ready")
    private String state;

    @Schema(title = "存储类型", example = "Ceph", description = "如：Ceph等")
    private String type;

    @Schema(title = "连接状态", example = "Connected", description = "Connected：已连接，DisConnected: 未连接")
    private String status;

    @Schema(title = "容量使用率", example = "75.5", description = "单位：%")
    private BigDecimal capacityUtilization;

    @Schema(title = "总容量", example = "1099511627776", description = "单位：字节")
    private Long totalCapacity;

    @Schema(title = "已用容量", example = "************", description = "单位：字节")
    private Long usedCapacity;

    @Schema(title = "地区ID", example = "1")
    private Long regionId;

    @Schema(title = "租户ID", example = "10001")
    private Long tenantId;

    @Schema(title = "可用物理容量", example = "************", description = "单位：字节")
    private BigDecimal availablePhysicalCapacity;

    @Schema(title = "总物理容量", example = "1099511627776", description = "单位：字节")
    private BigDecimal totalPhysicalCapacity;

    @Schema(title = "可用容量", example = "************", description = "单位：字节")
    private BigDecimal availableCapacity;

    @Schema(title = "平台ID", example = "1")
    private Long platformId;

    @Schema(title = "平台名称", example = "OpenStack")
    private String platformName;

    @Schema(title = "删除标记", example = "0", description = "0-未删除，1-已删除")
    private Integer deleted;

    @Schema(title = "存储类型名称", example = "Ceph存储")
    private String typeName;

    @Schema(title = "集群名称", example = "ceph-cluster-01")
    private String clusterName;

    @Schema(title = "集群UUID", example = "cluster-550e8400")
    private String clusterUuid;

    @Schema(title = "创建时间", example = "2024-03-17 10:00:00")
    private Date createTime;

    @Schema(title = "系统创建时间", example = "2024-03-17 10:00:00")
    private Date sCreateTime;

    @Schema(title = "介质类型", example = "SSD")
    private String mediaType;

    @Schema(title = "虚拟容量", example = "2199023255552", description = "单位：字节")
    private BigDecimal virtualCapacity;

    @Schema(title = "超售比", example = "2.0", description = "单位：倍")
    private BigDecimal storagePercent;

    @Schema(title = "分配率", example = "80.5", description = "单位：%")
    private BigDecimal commitRate;

    @Schema(title = "分配容量", example = "************", description = "单位：字节")
    private BigDecimal allocation;

    @Schema(title = "区域", example = "华东")
    private String manager;

    @Schema(title = "可用区域", example = "可用区1")
    private String availableManager;

    @Schema(title = "预留容量", example = "109951162777", description = "单位：字节")
    private BigDecimal reserveCapacity;

    @Schema(title = "浪费容量", example = "21990232555", description = "单位：字节")
    private BigDecimal wasteCapacity;

    @Schema(title = "更新时间", example = "2024-03-17 15:00:00")
    private Date vUpdateTime;

    @Schema(title = "备注", example = "生产环境主存储")
    private String remark;

    @Schema(title = "存储关系表", example = "存储关系表")
    @TableField(exist = false)
    private  List<StorageHostRelationData> storageHostRelationDataList;

    private Date updateTime;

}
