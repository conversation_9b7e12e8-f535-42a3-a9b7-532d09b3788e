package cn.iocoder.zj.framework.common.pojo;

import lombok.*;

import java.time.LocalDateTime;

@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserBindDTO{

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 企业微信推送启用状态，0未启用，1已启用
     */
    private Integer wxState;
    /**
     * 邮箱推送启用状态，0未启用，1已启用
     */
    private Integer emailState;
    /**
     * 钉钉推送启用状态，0未启用,1启用中
     */
    private Integer dingtalkState;
    /**
     * 钉钉应用key
     */
    private String dingtalkAppKey;
    /**
     * 钉钉应用秘钥
     */
    private String dingtalkAppSecret;
    /**
     * 应用id
     */
    private String dingtalkAgentId;
    /**
     * 钉钉手机号
     */
    private String dingtalkPhone;
    /**
     * 企微应用id
     */
    private String wxAgentId;
    /**
     * 企微企业id
     */
    private String wxCorpid;
    /**
     * 企微企业秘钥
     */
    private String wxCorpsecret;
    /**
     * 企微手机号
     */
    private String wxPhone;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String callbackUrl;

    private String mailAddress;

    private String mailPassword;

    private String mailSmtpHost;
}
