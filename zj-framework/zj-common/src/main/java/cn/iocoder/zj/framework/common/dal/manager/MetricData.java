package cn.iocoder.zj.framework.common.dal.manager;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 指标数据实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MetricData {


    /**
     * 平台id
     */
    private Long platformId;

    /**
     * 监控id
     */
    private String resourceId;

    /**
     * 监控名称
     */
    private String resourceName;

    /**
     * 指标类型
     */
    private String metricName;

    /**
     * 时间
     */
    private List<Long> timestamps;

    /**
     * value值
     */
    private List<Double> values;

    private String type;
}