package cn.iocoder.zj.framework.common.dal.manager;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

import static io.swagger.v3.oas.annotations.media.Schema.AccessMode.READ_ONLY;


@TableName("monitor_host_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "monitor_host_info  | 监控物理机实体")
public class VmData {

    @TableId
    @Schema(title = "主键id", example = "87584674384", accessMode = READ_ONLY)
    private Long id;

    @Schema(title = "虚拟机UUID", example = "550e8400-e29b-41d4-a716-************")
    private String uuid;

    @Schema(title = "虚拟机名称", example = "test-vm-001")
    private String name;

    @Schema(title = "虚拟机状态", example = "Running", description = "Running-运行中, Stopped-已停止, Starting-启动中, Stopping-停止中")
    private String state;

    @Schema(title = "虚拟机IP地址", example = "*************")
    private String ip;

    @Schema(title = "虚拟机弹性IP", example = "**********")
    private String vipIp;

    @Schema(title = "可用区UUID", example = "zone-550e8400")
    private String zoneUuid;

    @Schema(title = "所属集群UUID", example = "cluster-550e8400")
    private String clusterUuid;

    @Schema(title = "宿主机UUID", example = "host-550e8400")
    private String hardwareUuid;

    @Schema(title = "CPU架构类型", example = "x86_64")
    private String architecture;

    @Schema(title = "客户机操作系统类型", example = "Linux")
    private String guestOsType;

    @Schema(title = "虚拟机创建时间", example = "2024-03-17 10:00:00")
    private Date vCreateDate;

    @Schema(title = "磁盘使用率", example = "75.5", description = "单位：%")
    private BigDecimal diskUsed;

    @Schema(title = "CPU使用率", example = "65.8", description = "单位：%")
    private BigDecimal cpuUsed;

    @Schema(title = "内存使用率", example = "80.2", description = "单位：%")
    private BigDecimal memoryUsed;

    @Schema(title = "虚拟机类型", example = "KVM")
    private String type;

    @Schema(title = "分配内存容量", example = "8589934592", description = "单位：字节")
    private Long memorySize;

    @Schema(title = "分配CPU核数", example = "4")
    private Integer cpuNum;

    @Schema(title = "网卡MAC地址", example = "00:50:56:9a:00:01")
    private String mac;

    @Schema(title = "镜像UUID", example = "image-550e8400")
    private String imageUuid;

    @Schema(title = "镜像名称", example = "image")
    private String imageName;

    @Schema(title = "租户ID", example = "10001")
    private Long tenantId;

    @Schema(title = "区域ID", example = "1")
    private Long regionId;

    @Schema(title = "可用区名称", example = "可用区1")
    private String zoneName;

    @Schema(title = "集群名称", example = "生产集群01")
    private String clusterName;

    @Schema(title = "宿主机名称", example = "host-01")
    private String hardwareName;

    @Schema(title = "网络入流量", example = "1024.5", description = "单位：字节/秒")
    private BigDecimal networkInBytes;

    @Schema(title = "网络出流量", example = "512.8", description = "单位：字节/秒")
    private BigDecimal networkOutBytes;

    @Schema(title = "已用磁盘容量", example = "107374182400", description = "单位：字节")
    private BigDecimal diskUsedBytes;

    @Schema(title = "可用磁盘容量", example = "214748364800", description = "单位：字节")
    private BigDecimal diskFreeBytes;

    @Schema(title = "磁盘总容量", example = "322122547200", description = "单位：字节")
    private BigDecimal totalDiskCapacity;

    @Schema(title = "实际使用空间", example = "85899345920", description = "单位：字节")
    private BigDecimal actualSize;

    @Schema(title = "云盘容量", example = "107374182400", description = "单位：字节")
    private BigDecimal cloudSize;

    @Schema(title = "网络入包速率", example = "1000", description = "单位：包/秒")
    private BigDecimal networkInPackets;

    @Schema(title = "网络出包速率", example = "800", description = "单位：包/秒")
    private BigDecimal networkOutPackets;

    @Schema(title = "所属平台ID", example = "1")
    private Long platformId;

    @Schema(title = "所属平台名称", example = "OpenStack")
    private String platformName;

    @Schema(title = "删除标记", example = "0", description = "0-未删除，1-已删除")
    private Integer deleted;

    @Schema(title = "虚拟机类型名称", example = "KVM虚拟机")
    private String typeName;

    @Schema(title = "虚拟机相关信息", example = "{\"os\":\"CentOS 7.9\",\"status\":\"normal\"}")
    private String vms;

    /**
     * 电源状态
     */
    @Schema(title = "电源状态", example = "KVM虚拟机")
    private String powerState;

    @Schema(title = "iso", example = "")
    private String iso;

    @Schema(title = "自动启动或高可用模式", example = "None或NeverStop")
    private String autoInitType;

    @Schema(title = "引导模式", example = "Legacy或UEFI")
    private String guideMode;

    @Schema(title = "授权", example = "1")
    private String authorizationType;

    @Schema(title = "授权时间", example = "1")
    private String authorizationTime;

    @Schema(title = "最后授权时间", example = "1")
    private String lastAccessTime;

    @Schema(title = "工具是否安装", example = "false或true")
    private String toolsInstalled;

    @Schema(title = "工具运行状态", example = "run或stop")
    private String toolsRunStatus;

    @Schema(title = "标签", example = "")
    private String tag;
}
