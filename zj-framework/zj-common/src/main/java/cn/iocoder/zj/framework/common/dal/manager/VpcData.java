package cn.iocoder.zj.framework.common.dal.manager;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

import static io.swagger.v3.oas.annotations.media.Schema.AccessMode.READ_ONLY;


@TableName("monitor_network_vpc")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "monitor_network_vpc  | 网络资源路由器")
public class VpcData {

    @TableId
    @Schema(title = "主键id", example = "87584674384", accessMode = READ_ONLY)
    private Long id;

    @Schema(title = "VPC路由器UUID", example = "550e8400-e29b-41d4-a716-************")
    private String uuid;

    @Schema(title = "路由器名称", example = "vpc-router-001")
    private String name;

    @Schema(title = "CPU核数", example = "2")
    private Integer cpuNum;

    @Schema(title = "内存大小", example = "4294967296", description = "单位：字节")
    private Long memorySize;

    @Schema(title = "CPU架构", example = "x86_64")
    private String architecture;

    @Schema(title = "路由DNS", example = "*******")
    private String dns;

    @Schema(title = "就绪状态", example = "Ready")
    private String status;

    @Schema(title = "启用状态", example = "Enabled")
    private String state;

    @Schema(title = "L3网络UUID", example = "l3-550e8400-e29b-41d4")
    private String l3NetworkUuid;

    @Schema(title = "IPv4地址", example = "***********")
    private String ip;

    @Schema(title = "管理网络UUID", example = "mn-550e8400-e29b-41d4")
    private String managementNetworkUuid;

    @Schema(title = "管理网络IP", example = "********")
    private String managementNetworkIp;

    @Schema(title = "三层网络名称", example = "public-network")
    private String l3NetworkName;

    @Schema(title = "集群UUID", example = "cluster-550e8400")
    private String clusterUuid;

    @Schema(title = "集群名称", example = "cluster-01")
    private String clusterName;

    @Schema(title = "虚拟化技术", example = "KVM")
    private String hypervisorType;

    @Schema(title = "MAC地址", example = "00:50:56:9a:00:01", description = "IPV4")
    private String mac;

    @Schema(title = "宿主机UUID", example = "host-550e8400")
    private String hostUuid;

    @Schema(title = "宿主机名称", example = "host-01")
    private String hostName;

    @Schema(title = "平台ID", example = "1")
    private Long platformId;

    @Schema(title = "平台名称", example = "OpenStack")
    private String platformName;

    @Schema(title = "租户ID", example = "10001")
    private Long tenantId;

    @Schema(title = "创建时间", example = "2024-03-17 10:00:00")
    private Date createTime;
}
