package cn.iocoder.zj.framework.common.dal.manager;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@TableName("monitor_hardware_storage")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "monitor_hardware_storage  | 存储和物理机的关系表")
public class StorageHostRelationData {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 宿主机id
     */
    private Long hardwareId;
    /**
     * 宿主机uuid
     */
    private String hardwareUuid;
    /**
     * 主存储id
     */
    private Long storageId;
    /**
     * 主存储uuid
     */
    private String storageUuid;

    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;

    private Date updateTime;
}
