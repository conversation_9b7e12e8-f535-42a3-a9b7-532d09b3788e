package cn.iocoder.zj.framework.common.dal.manager;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 **/
@TableName("monitor_secgroup_rule")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "monitor_secgroup_rule  | 安全组规则实体")
public class SecGroupRuleData {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * uuid
     */
    private String uuid;
    /**
     * 状态
     */
    private String status;
    /**
     * 安全组uuid
     */
    private String secgroupUuid;
    /**
     * 优先级
     */
    private Integer priority;
    /**
     * 协议
     */
    private String protocol;
    /**
     * 端口
     */
    private String ports;
    /**
     * 方向
     */
    private String direction;
    /**
     * cidr
     */
    private String cidr;
    /**
     * 策略
     */
    private String action;
    /**
     * 备注
     */
    private String description;

    /**
     * 平台ID
     */
    private Long platformId;

    /**
     * 备注
     */
    private String platformName;
}
