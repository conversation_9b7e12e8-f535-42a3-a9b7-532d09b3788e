package cn.iocoder.zj.framework.common.dal.manager;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.hertzbeat.common.entity.dto.Value;

import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "历史记录性能数据")
public class MetricHistoryData {


    @Schema(title = "监控uuid")
    private String uuid;

    @Schema(title = "监控指标")
    private String metrics;

    private List<String> metricList;

    @Schema(description = "返回集合数据")
    private Map<String, List<Value>> values;
}
