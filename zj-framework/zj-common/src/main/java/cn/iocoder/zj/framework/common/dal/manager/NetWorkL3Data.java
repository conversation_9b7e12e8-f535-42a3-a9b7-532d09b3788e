package cn.iocoder.zj.framework.common.dal.manager;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 **/
@TableName("monitor_network_l3")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "monitor_network_l3  | 三层网络实体")
public class NetWorkL3Data {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 三级网络uuid
     */
    private String uuid;
    /**
     * 二级网络名称
     */
    private String l2NetworkName;
    /**
     * 二级网络uuid
     */
    private String l2NetworkUuid;
    /**
     * 三级网络名称
     */
    private String name;
    /**
     * dns 逗号分割
     */
    private String dns;
    /**
     * 网络资源类型（L3BasicNetwork 公有网络， L3VpcNetwork  vpc 网络 ）
     */
    private String type;
    /**
     * 网络服务 逗号分割
     */
    private String networkServices;
    /**
     * 起始ip
     */
    private String startIp;
    /**
     * 结束ip
     */
    private String endIp;
    /**
     * 子网掩码
     */
    private String netmask;
    /**
     * 网关
     */
    private String gateway;
    /**
     * 网段名称
     */
    private String networkSegment;
    /**
     * IPv4 CIDR
     */
    private String networkCidr;
    /**
     * IPV4 DHCP
     */
    private String nextHopIp;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;

    private Long tenantId;

    private Date createTime;

    private String typeName;

    private Date updateTime;
}
