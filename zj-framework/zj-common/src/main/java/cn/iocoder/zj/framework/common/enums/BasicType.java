package cn.iocoder.zj.framework.common.enums;

import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
public enum BasicType {

    BASIC_ZS_TACK_HOST("zs_tack_host", "zstack物理机"),
    BASIC_ZS_TACK_VM("zs_tack_vm", "zstack虚拟机"),
    BASIC_ZS_TACK_VPC("zs_tack_vpc", "zstack虚拟网络"),
    BASIC_ZS_TACK_STORAGE("zs_tack_storage", "zstack存储"),
    BASIC_ZS_TACK_STORAGE_POOL("zs_tack_storage_pool", "zstack存储池"),
    BASIC_ZS_TACK_SNAPSHOT("zs_tack_snapshot", "zstack快照"),
    BASIC_ZS_TACK_IMAGE("zs_tack_image", "zstack镜像"),
    BASIC_ZS_TACK_VM_VIC("zs_tack_vm_nic", "云主机网络"),
    BASIC_ZS_TACK_HOST_VIC("zs_tack_host_nic", "物理机网络"),
    BASIC_ZS_TACK_NET("zs_tack_net", "zstack网络"),
    BASIC_ZS_TACK_SEC_GROUP("zs_tack_sec_group", "zstack安全组"),
    BASIC_ZS_TACK_VOLUME_INFO("zs_tack_volume_info", "zstack云盘"),

    BASIC_FUSION_COMPUTE_HOST("fusion_compute_host", "fusion_compute物理机"),
    BASIC_FUSION_COMPUTE_VM("fusion_compute_vm", "fusion_compute虚拟机"),
    BASIC_FUSION_COMPUTE_VPC("fusion_compute_vpc", "fusion_compute虚拟网络"),
    BASIC_FUSION_COMPUTE_STORAGE("fusion_compute_storage", "fusion_compute存储"),
    BASIC_FUSION_COMPUTE_STORAGE_POOL("fusion_compute_storage_pool", "fusion_compute存储池"),
    BASIC_FUSION_COMPUTE_SNAPSHOT("fusion_compute_snapshot", "fusion_compute快照"),
    BASIC_FUSION_COMPUTE_IMAGE("fusion_compute_image", "fusion_compute镜像"),
    BASIC_FUSION_COMPUTE_VM_VIC("fusion_compute_vm_nic", "云主机网络"),
    BASIC_FUSION_COMPUTE_HOST_VIC("fusion_compute_host_nic", "物理机网络"),
    BASIC_FUSION_COMPUTE_NET("fusion_compute_net", "fusion_compute网络"),
    BASIC_FUSION_COMPUTE_SEC_GROUP("fusion_compute_sec_group", "fusion_compute安全组"),
    BASIC_FUSION_COMPUTE_VOLUME_INFO("fusion_compute_volume_info", "fusion_compute云盘"),

    BASIC_IS_TACK_HOST("is_tack_host", "istack物理机"),
    BASIC_IS_TACK_VM("is_tack_vm", "istack虚拟机"),
    BASIC_IS_TACK_VPC("is_tack_vpc", "istack虚拟网络"),
    BASIC_IS_TACK_STORAGE("is_tack_storage", "istack存储"),
    BASIC_IS_TACK_STORAGE_POOL("is_tack_storage_pool", "istack存储池"),
    BASIC_IS_TACK_SNAPSHOT("is_tack_snapshot", "istack快照"),
    BASIC_IS_TACK_IMAGE("is_tack_image", "istack镜像"),
    BASIC_IS_TACK_VM_VIC("is_tack_vm_nic", "istack云主机网络"),
    BASIC_IS_TACK_HOST_VIC("is_tack_host_nic", "istack物理机网络"),
    BASIC_IS_TACK_NET("is_tack_net", "istack网络"),
    BASIC_IS_TACK_SEC_GROUP("is_tack_sec_group", "istack安全组"),
    BASIC_IS_TACK_VOLUME_INFO("is_tack_volume_info", "istack云盘"),

    BASIC_VMWARE_HOST("vmware_host", "vmware物理机"),
    BASIC_VMWARE_VM("vmware_vm", "vmware虚拟机"),
    BASIC_VMWARE_VPC("vmware_vpc", "vmware虚拟网络"),
    BASIC_VMWARE_STORAGE("vmware_storage", "vmware存储"),
    BASIC_VMWARE_STORAGE_POOL("vmware_storage_pool", "vmware存储池"),
    BASIC_VMWARE_SNAPSHOT("vmware_snapshot", "vmware快照"),
    BASIC_VMWARE_IMAGE("vmware_image", "vmware镜像"),
    BASIC_VMWARE_VM_VIC("vmware_vm_nic", "vmware云主机网络"),
    BASIC_VMWARE_HOST_VIC("vmware_host_nic", "vmware物理机网络"),
    BASIC_VMWARE_NET("vmware_net", "vmware网络"),
    BASIC_VMWARE_SEC_GROUP("vmware_sec_group", "vmware安全组"),
    BASIC_VMWARE_VOLUME_INFO("vmware_volume_info", "vmware云盘"),

    BASIC_SXF_HOST("sxf_host", "深信服物理机"),
    BASIC_SXF_VM("sxf_vm", "深信服虚拟机"),
    BASIC_SXF_VPC("sxf_vpc", "深信服虚拟网络"),
    BASIC_SXF_STORAGE("sxf_storage", "深信服存储"),
    BASIC_SXF_STORAGE_POOL("sxf_storage_pool", "深信服存储池"),
    BASIC_SXF_SNAPSHOT("sxf_snapshot", "深信服快照"),
    BASIC_SXF_IMAGE("sxf_image", "深信服镜像"),
    BASIC_SXF_VM_VIC("sxf_vm_nic", "云主机网络"),
    BASIC_SXF_HOST_VIC("sxf_host_nic", "物理机网络"),
    BASIC_SXF_NET("sxf_net", "深信服网络"),
    BASIC_SXF_SEC_GROUP("sxf_sec_group", "深信服安全组"),
    BASIC_SXF_VOLUME_INFO("sxf_volume_info", "深信服云盘"),

    BASIC_WIN_HONG_HOST("win_hong_host", "win_hong物理机"),
    BASIC_WIN_HONG_VM("win_hong_vm", "win_hong虚拟机"),
    BASIC_WIN_HONG_VPC("win_hong_vpc", "win_hong虚拟网络"),
    BASIC_WIN_HONG_STORAGE("win_hong_storage", "win_hong存储"),
    BASIC_WIN_HONG_STORAGE_POOL("win_hong_storage_pool", "win_hong存储池"),
    BASIC_WIN_HONG_SNAPSHOT("win_hong_snapshot", "win_hong快照"),
    BASIC_WIN_HONG_IMAGE("win_hong_image", "win_hong镜像"),
    BASIC_WIN_HONG_VM_VIC("win_hong_vm_nic", "云主机网络"),
    BASIC_WIN_HONG_HOST_VIC("win_hong_host_nic", "物理机网络"),
    BASIC_WIN_HONG_NET("win_hong_net", "win_hong网络"),
    BASIC_WIN_HONG_VOLUME_INFO("win_hong_volume_info", "win_hong云盘"),
    BASIC_WIN_HONG_SEC_GROUP("win_hong_sec_group", "win_hong安全组"),

    BASIC_IN_SPUR_HOST("in_spur_host", "浪潮物理机"),
    BASIC_IN_SPUR_VM("in_spur_vm", "浪潮虚拟机"),
    BASIC_IN_SPUR_VPC("in_spur_vpc", "浪潮虚拟网络"),
    BASIC_IN_SPUR_STORAGE("in_spur_storage", "浪潮存储"),
    BASIC_IN_SPUR_STORAGE_POOL("in_spur_storage_pool", "浪潮存储池"),
    BASIC_IN_SPUR_SNAPSHOT("in_spur_snapshot", "浪潮快照"),
    BASIC_IN_SPUR_IMAGE("in_spur_image", "浪潮镜像"),
    BASIC_IN_SPUR_VM_VIC("in_spur_vm_nic", "云主机网络"),
    BASIC_IN_SPUR_HOST_VIC("in_spur_host_nic", "物理机网络"),
    BASIC_IN_SPUR_NET("in_spur_net", "浪潮网络"),
    BASIC_IN_SPUR_SEC_GROUP("in_spur_sec_group", "浪潮安全组"),
    BASIC_IN_SPUR_VOLUME_INFO("in_spur_volume_info", "浪潮云盘");

    private final String code;

    private final String desc;

    public static BasicType fromCode(String code) {
        for (BasicType typeEnum : values()) {
            if (typeEnum.code.equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    public String code() {
        return this.code;
    }

    public static List<String> getAllBasicTypeCodes() {
        List<String> codes = new ArrayList<>();
        for (BasicType basicType : BasicType.values()) {
            codes.add(basicType.code());
        }
        return codes;
    }
}
