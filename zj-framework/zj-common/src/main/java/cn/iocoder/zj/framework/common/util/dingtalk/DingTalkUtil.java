package cn.iocoder.zj.framework.common.util.dingtalk;

import cn.iocoder.zj.framework.common.pojo.DingDingAuthorizationDTO;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import com.aliyun.dingtalkim_1_0.*;
import com.aliyun.dingtalkim_1_0.models.*;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.models.RuntimeOptions;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.*;
import com.dingtalk.api.response.*;
import com.taobao.api.ApiException;
import com.taobao.api.FileItem;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.internal.StringUtil;

import java.util.*;
import java.util.stream.Collectors;


@Slf4j
public class DingTalkUtil {
    private static final String ACCESS_TOKEN = "4f058b19b336328d95b23bf5c2cb0b62";
    private static final String ROBOT_CODE = "dingvkpgrknyfcdghv9w";
    private static final String CALLBACK_URL = "https://itsm2.zjiecn.com/dingtalk";
    private static final String TEMPLATE_ID = "StandardCard";
    private static final String USER_ID = "216850135238013210";

    public static String getAccessToken(String appKey ,String appSecret) throws ApiException {
        DefaultDingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/gettoken");
        OapiGettokenRequest request = new OapiGettokenRequest();
        request.setAppkey(appKey);
        request.setAppsecret(appSecret);
        // /*请求方式*/
        request.setHttpMethod("GET");
        OapiGettokenResponse response = client.execute(request);
        return response.getAccessToken();
    }

    public static boolean sendDingDingMessage(DingDingAuthorizationDTO dingAuthorization, String accessToken, String type) {
        if (StringUtil.isBlank(dingAuthorization.getMobile())) {
            return false;
        }

        String urid = getUserIdByMobile(dingAuthorization.getMobile(), accessToken);
        if (urid == null) {
            return false;
        }

        if (type.equals("File")) {
            return sendFileMessage(dingAuthorization, accessToken, urid);
        } else if(type.equals("Oa")){
            return sendOAMessage(dingAuthorization, accessToken, urid);
        }else {
            return sendOANumMessage(dingAuthorization, accessToken, urid);
        }
    }

    public static String getUserIdByMobile(String mobile, String accessToken) {
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/user/get_by_mobile");
            OapiUserGetByMobileRequest req = new OapiUserGetByMobileRequest();
            req.setMobile(mobile);
            req.setHttpMethod("GET");
            OapiUserGetByMobileResponse rsp = client.execute(req, accessToken);
            if ("ok".equals(rsp.getErrmsg())) {
                return rsp.getUserid();
            }
        } catch (ApiException e) {
            e.printStackTrace();
        }
        return null;
    }

    private static boolean sendFileMessage(DingDingAuthorizationDTO dingAuthorization, String accessToken, String urid) {
        try {
            String mediaId = uploadMedia(dingAuthorization.getUrl(), accessToken);
            if (mediaId == null) return false;

            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
            OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
            request.setUseridList(urid);
            request.setAgentId(Long.parseLong(dingAuthorization.getAgentId()));
            request.setToAllUser(false);

            OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            msg.setMsgtype("file");
            msg.setFile(new OapiMessageCorpconversationAsyncsendV2Request.File());
            msg.getFile().setMediaId(mediaId);
            request.setMsg(msg);
            log.info("钉钉返回File信息: " + JsonUtils.toJsonString(request));
            OapiMessageCorpconversationAsyncsendV2Response response = client.execute(request, accessToken);
            log.info("钉钉返回File结果: " + JsonUtils.toJsonString(response));
            return response.isSuccess();
        } catch (ApiException e) {
            e.printStackTrace();
        }
        return false;
    }

    private static String uploadMedia(String fileUrl, String accessToken) {
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/media/upload");
            OapiMediaUploadRequest request = new OapiMediaUploadRequest();
            request.setType("file");
            request.setMedia(new FileItem(fileUrl));
            OapiMediaUploadResponse response = client.execute(request, accessToken);
            if (response.isSuccess()) {
                return response.getMediaId();
            }
        } catch (ApiException e) {
            e.printStackTrace();
        }
        return null;
    }

    private static boolean sendOAMessage(DingDingAuthorizationDTO dingAuthorization, String accessToken, String urid) {
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
            OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
            request.setUseridList(urid);
            request.setAgentId(Long.parseLong(dingAuthorization.getAgentId()));
            request.setToAllUser(false);

            OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            msg.setOa(new OapiMessageCorpconversationAsyncsendV2Request.OA());
            msg.getOa().setMessageUrl(dingAuthorization.getUrl());
            msg.getOa().setPcMessageUrl(dingAuthorization.getUrl());
            msg.getOa().setHead(new OapiMessageCorpconversationAsyncsendV2Request.Head());
            msg.getOa().getHead().setText("待办事宜");
            msg.getOa().getHead().setBgcolor("00409eff");
            msg.getOa().setBody(new OapiMessageCorpconversationAsyncsendV2Request.Body());

            List<OapiMessageCorpconversationAsyncsendV2Request.Form> form = new ArrayList<>();
            form.add(createForm("告警时间", dingAuthorization.getWarningTime()));
            form.add(createForm("告警项目", dingAuthorization.getWarningItem()));
            form.add(createForm("告警对象", dingAuthorization.getWarningObject()));
            msg.getOa().getBody().setForm(form);
            msg.setMsgtype("oa");
            request.setMsg(msg);
            log.info("钉钉发送OA信息: " + JsonUtils.toJsonString(request));
            OapiMessageCorpconversationAsyncsendV2Response response = client.execute(request, accessToken);
            log.info("钉钉返回OA结果: " + JsonUtils.toJsonString(response));
            return response.isSuccess();
        } catch (ApiException e) {
            e.printStackTrace();
        }
        return false;
    }

    private static boolean sendOANumMessage(DingDingAuthorizationDTO dingAuthorization, String accessToken,
                                           String urid) {
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
            OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
            request.setUseridList(urid);
            request.setAgentId(Long.parseLong(dingAuthorization.getAgentId()));
            request.setToAllUser(false);

            OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            msg.setOa(new OapiMessageCorpconversationAsyncsendV2Request.OA());
            msg.getOa().setMessageUrl(dingAuthorization.getUrl());
            msg.getOa().setPcMessageUrl(dingAuthorization.getUrl());
            msg.getOa().setHead(new OapiMessageCorpconversationAsyncsendV2Request.Head());
            msg.getOa().getHead().setText("待办事宜");
            msg.getOa().getHead().setBgcolor("00409eff");
            msg.getOa().setBody(new OapiMessageCorpconversationAsyncsendV2Request.Body());

            List<OapiMessageCorpconversationAsyncsendV2Request.Form> form = new ArrayList<>();
            form.add(createForm("告警时间", dingAuthorization.getWarningTime()));
            form.add(createForm("告警项目", dingAuthorization.getWarningItem()));
            form.add(createForm("告警条数", dingAuthorization.getWarningObject()));
            msg.getOa().getBody().setForm(form);
            msg.setMsgtype("oa");
            request.setMsg(msg);
            log.info("钉钉发送OA信息: " + JsonUtils.toJsonString(request));
            OapiMessageCorpconversationAsyncsendV2Response response = client.execute(request, accessToken);
            log.info("钉钉返回OA结果: " + JsonUtils.toJsonString(response));
            return response.isSuccess();
        } catch (ApiException e) {
            e.printStackTrace();
        }
        return false;
    }

    private static OapiMessageCorpconversationAsyncsendV2Request.Form createForm(String key, String value) {
        OapiMessageCorpconversationAsyncsendV2Request.Form form = new OapiMessageCorpconversationAsyncsendV2Request.Form();
        form.setKey(key);
        form.setValue(value);
        return form;
    }

    private static OapiMessageCorpconversationAsyncsendV2Request.BtnJsonList createBtn(String key, String value) {
        OapiMessageCorpconversationAsyncsendV2Request.BtnJsonList form = new OapiMessageCorpconversationAsyncsendV2Request.BtnJsonList();
        form.setTitle(key);
        form.setActionUrl(value);
        return form;
    }



    public static com.aliyun.dingtalkim_1_0.Client createClient() throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkim_1_0.Client(config);
    }
    public static void main(String[] args) throws Exception {
        String appKey = "dingvkpgrknyfcdghv9w";
        String appSecret = "SG8hZ_T5h6t8VknpUjXaCjVpGGAJzKe6qdliuNiSbOrpAP8MnNQnQ-5H5-8PVY2J";
        String accessToken = getAccessToken(appKey, appSecret);
        System.out.println("accessToken: " + accessToken);
        String userIdByMobile = getUserIdByMobile("13515645394", accessToken);
        String uuid = UUID.randomUUID().toString();
//        System.out.println("userId: " + userIdByMobile);
//        sendCardMessage(accessToken,userIdByMobile);
//        sendCardMessage("0",accessToken,userIdByMobile,uuid);
    }

    public static void sendCardMessage(String status, String accessToken, String userIdByMobile,String uuid,Map<String, String> templateParams) throws Exception {
        Client client = DingTalkUtil.createClient();
        SendRobotInteractiveCardHeaders headers = sendCreateHeaders(accessToken);

        List<ButtonData> buttons = getButtonsForStatus(status);
        String titleText = getTitleTextForStatus(status);
        String cardData = generateCardData(buttons, titleText, templateParams);

        SendRobotInteractiveCardRequest request = new SendRobotInteractiveCardRequest()
                .setCardTemplateId(TEMPLATE_ID)
                .setSingleChatReceiver(String.format("{\"userId\":\"%s\"}", userIdByMobile))
                .setCardBizId(uuid)
                .setRobotCode(ROBOT_CODE)
                .setCallbackUrl(CALLBACK_URL)
                .setCardData(cardData)
                .setPullStrategy(false);
        log.info("钉钉发送卡片信息: " + JsonUtils.toJsonString(request));
        try {
            SendRobotInteractiveCardResponse sendRobotInteractiveCardResponse =
                    client.sendRobotInteractiveCardWithOptions(request, headers, new RuntimeOptions());
            log.info("钉钉返回卡片信息: " + JsonUtils.toJsonString(sendRobotInteractiveCardResponse));
        } catch (TeaException e) {
            handleTeaException(e);
        } catch (Exception e) {
            handleTeaException(new TeaException(e.getMessage(), e));
        }
    }

    public static void updateCardMessage(String status, String accessToken, String cardBizId,Map<String, String> templateParams) throws Exception {
        Client client = DingTalkUtil.createClient();
        UpdateRobotInteractiveCardHeaders headers = updateCreateHeaders(accessToken);

        List<ButtonData> buttons = getButtonsForStatus(status);
        String titleText = getTitleTextForStatus(status);
        String cardData = generateCardData(buttons, titleText, templateParams);
        UpdateRobotInteractiveCardRequest.UpdateRobotInteractiveCardRequestUpdateOptions updateOptions = new UpdateRobotInteractiveCardRequest.UpdateRobotInteractiveCardRequestUpdateOptions()
                .setUpdateCardDataByKey(false)
                .setUpdatePrivateDataByKey(false);
        UpdateRobotInteractiveCardRequest request = new UpdateRobotInteractiveCardRequest()
                .setCardBizId(cardBizId)
                .setCardData(cardData)
                .setUpdateOptions(updateOptions);
        log.info("钉钉更新卡片信息: " + JsonUtils.toJsonString(request));
        try {
            UpdateRobotInteractiveCardResponse updateRobotInteractiveCardResponse =
                    client.updateRobotInteractiveCardWithOptions(request, headers, new RuntimeOptions());
            log.info("钉钉返回更新信息: " + JsonUtils.toJsonString(updateRobotInteractiveCardResponse));
        } catch (TeaException e) {
            handleTeaException(e);
        } catch (Exception e) {
            handleTeaException(new TeaException(e.getMessage(), e));
        }
    }

    private static SendRobotInteractiveCardHeaders sendCreateHeaders(String accessToken) {
        SendRobotInteractiveCardHeaders headers = new SendRobotInteractiveCardHeaders();
        headers.xAcsDingtalkAccessToken = accessToken;
        return headers;
    }

    private static UpdateRobotInteractiveCardHeaders updateCreateHeaders(String accessToken) {
        UpdateRobotInteractiveCardHeaders headers = new UpdateRobotInteractiveCardHeaders();
        headers.xAcsDingtalkAccessToken = accessToken;
        return headers;
    }

    private static void handleTeaException(TeaException e) {
        System.err.println("Error code: " + e.code);
        System.err.println("Error message: " + e.message);
    }


    private static List<ButtonData> getButtonsForStatus(String status) {
        switch (status) {
            case "0":
                return List.of(
                        new ButtonData("同意", "icon_accept", "primary","1"),
                        new ButtonData("拒绝", "icon_wd_close", "normal","2")
                );
            case "1":
                return List.of(new ButtonData("已通过", "icon_accept", "primary","1"));
            case "2":
                return List.of(new ButtonData("已驳回", "icon_wd_close", "normal","2"));
            case "3":
                return List.of(new ButtonData("已过期", "icon_wd_close", "normal","2"));
            case "4":
                return List.of(new ButtonData("已授权", "icon_wd_close", "normal","2"));
            default:
                return Collections.emptyList();
        }
    }

    private static String getTitleTextForStatus(String status) {
        switch (status) {
            case "0":
                return "您的资产正在申请接入授权，如果您同意，申请人将可接入下述资产，申请人的所有操作将会被记录和审计。";
            case "1":
                return "您的资产接入申请已通过，申请人的所有操作将会被记录和审计";
            case "2":
                return "您的资产接入申请已驳回，如有需要请从新发起申请";
            case "3":
                return "资产接入申请已过期，如有需要请重新申请";
            case "4":
                return "资产接入申请已授权";
            default:
                return "";
        }
    }

    private static String generateCardData(List<ButtonData> buttons, String titleText, Map<String, String> templateParams) {
        String header = createHeader("授权申请");
        String markdownText = createMarkdownText(titleText);
        String sectionFields = createSectionFields(templateParams);
        String actionButtons = createActionButtons(buttons);

        return String.format(
                "{ \"config\": { \"autoLayout\": true, \"enableForward\": true }, " +
                        "\"header\": %s, \"contents\": [ %s, %s, { \"type\": \"divider\" }, { \"type\": \"divider\" }, %s ] }",
                header, markdownText, sectionFields, actionButtons
        );
    }

    private static String createHeader(String title) {
        return String.format(
                "{ \"title\": { \"type\": \"text\", \"text\": \"%s\" }, \"logo\": \"@lALPDtXaA1csu9g4MA\" }",
                title
        );
    }

    private static String createMarkdownText(String text) {
        return String.format(
                "{ \"type\": \"markdown\", \"text\": \"<font size=6 color=common_level3_base_color>%s</font>\" }",
                text
        );
    }

    private static String createSectionFields(Map<String, String> templateParams) {
        String hostName = templateParams.get("hostName");
        String truncatedHostName = hostName.length() >= 20 ? hostName.substring(0, 17) + "..." : hostName;
        return String.format(
                "{ \"type\": \"section\", \"fields\": { \"shortContent\": true, \"list\": [" +
                        createField("资源名称", truncatedHostName) + ", " +
                        createField("主机地址", templateParams.get("assetName")) + ", " +
                        createField("申请时间", templateParams.get("date")) + ", " +
                        createField("结束时间", templateParams.get("dateEnd")) + ", " +
                        createField("申请人", templateParams.get("nickname")) +
                        "] } }"
        );
    }

    private static String createField(String label, String value) {
        return String.format(
                "{ \"type\": \"markdown\", \"text\": \"<font size=6 color=common_level3_base_color>%s：</font>%s\" }",
                label, value
        );
    }

    private static String createActionButtons(List<ButtonData> buttons) {
        boolean disabled = buttons.size() <= 1;
        String buttonsJson = buttons.stream()
                .map(button -> String.format(
                        "{ \"type\": \"button\", \"label\": { \"type\": \"text\", \"text\": \"%s\"" +
                                "}, \"iconCode\": \"%s\", " +
                                "\"actionType\": \"request\", \"status\": \"%s\", \"disabled\": %b , \"id\": \"%s\"}",
                        button.buttonText ,button.iconCode, button.status, disabled,button.id
                ))
                .collect(Collectors.joining(", ", "[ ", " ]"));

        return String.format("{ \"type\": \"action\", \"actions\": %s }", buttonsJson);
    }

    public static class ButtonData {
        String buttonText;
        String iconCode;
        String status;
        String id;

        public ButtonData(String buttonText, String iconCode, String status,String id) {
            this.buttonText = buttonText;
            this.iconCode = iconCode;
            this.status = status;
            this.id = id;
        }
    }

}
