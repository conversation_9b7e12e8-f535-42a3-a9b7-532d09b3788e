package cn.iocoder.zj.framework.common.enums;

import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 **/
@AllArgsConstructor
public enum MetricsType {

    BASIC_NET_L2("basic_net_l2", "二层网络"),

    BASIC_TYPE_SXF("basic_type_sxf", "深信服"),

    BASIC_NET_L3("basic_net_l3", "三层网络"),

    BASIC_HOST_VIC_FUSIONONE("basic_host_vic_fusionone", "FusionOne云主机网络");


    private final String code;

    private final String desc;

    public static MetricsType fromCode(String code) {
        for (MetricsType typeEnum : values()) {
            if (typeEnum.code.equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    public String code() {
        return this.code;
    }

    public static List<String> getAllBasicTypeCodes() {
        List<String> codes = new ArrayList<>();
        for (MetricsType basicType : MetricsType.values()) {
            codes.add(basicType.code());
        }
        return codes;
    }
}
