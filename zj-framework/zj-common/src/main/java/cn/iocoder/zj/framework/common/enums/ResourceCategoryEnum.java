package cn.iocoder.zj.framework.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
@AllArgsConstructor
public enum ResourceCategoryEnum {
    APP_HARDWARE("hardware", "宿主机"),
    APP_HOST("host", "云主机"),
    APP_STORAGE("storage", "云存储"),
    APP_SERVICE("service", "应用系统", Arrays.asList(
            ResourceAppEnum.APP_SERVICE_WEBSITE,
            ResourceAppEnum.APP_SERVICE_PORT,
            ResourceAppEnum.APP_SERVICE_SSL,
            ResourceAppEnum.APP_SERVICE_PING,
            ResourceAppEnum.APP_SERVICE_WEBSOCKET,
            ResourceAppEnum.APP_SERVICE_API,
            ResourceAppEnum.APP_SERVICE_DNS,
            ResourceAppEnum.APP_SERVICE_FULLSITE,
//            ResourceAppEnum.APP_SERVICE_APICODE,
            ResourceAppEnum.APP_SERVICE_UDP_PORT
    )),
    APP_DB("db", "数据库", Arrays.asList(
            ResourceAppEnum.APP_DB_MYSQL,
            ResourceAppEnum.APP_DB_ORACLE,
            ResourceAppEnum.APP_DB_SQLSERVER,
            ResourceAppEnum.APP_DB_MARIADB,
            ResourceAppEnum.APP_DB_DM,
            ResourceAppEnum.APP_DB_REDIS,
            ResourceAppEnum.APP_DB_TIDB,
            ResourceAppEnum.APP_DB_NEBULAGRAPH,
            ResourceAppEnum.APP_DB_POSTGRESQL,
            ResourceAppEnum.APP_DB_MONGODB,
            ResourceAppEnum.APP_DB_OPENGAUSS,
//            ResourceAppEnum.APP_DB_CLUSTER,
//            ResourceAppEnum.APP_DB_SENTINEL,
            ResourceAppEnum.APP_DB_MEMCACHED
    )),
    APP_MID("mid", "中间件", Arrays.asList(
            ResourceAppEnum.APP_SERVICE_TOMCAT,
            ResourceAppEnum.APP_DB_ZOOKEEPER,
            ResourceAppEnum.APP_DB_KAFKA,
            ResourceAppEnum.APP_DB_NACOS,
            ResourceAppEnum.APP_DB_RABBITMQ,
            ResourceAppEnum.APP_DB_NGINX,
            ResourceAppEnum.APP_DB_POP3,
            ResourceAppEnum.APP_DB_FTP,
            ResourceAppEnum.APP_DB_SMTP,
            ResourceAppEnum.APP_DB_NTP,
            ResourceAppEnum.APP_DB_SPRING_GATEWAY,
            ResourceAppEnum.APP_DB_EMQX,
            ResourceAppEnum.APP_DB_ACTIVEMQ,
            ResourceAppEnum.APP_DB_ROCKETMQ,
            ResourceAppEnum.APP_DB_ES,
            ResourceAppEnum.APP_TOMEE,
            ResourceAppEnum.APP_RESIN
//            ResourceAppEnum.APP_DB_SHENYU
    )),
    APP_OS("os", "操作系统", Arrays.asList(
            ResourceAppEnum.APP_DB_WINDOWS,
            ResourceAppEnum.APP_DB_LINUX,
            ResourceAppEnum.APP_DB_LINUX_SNMP,
            ResourceAppEnum.APP_DB_UBUNTU,
            ResourceAppEnum.APP_DB_COREOS,
            ResourceAppEnum.APP_DB_OPENSUSE,
            ResourceAppEnum.APP_DB_ROCKYLINUX,
            ResourceAppEnum.APP_DB_EULEROS,
            ResourceAppEnum.APP_DB_REDHAT,
            ResourceAppEnum.APP_DB_CENTOS,
            ResourceAppEnum.APP_DB_FREEBSD,
            ResourceAppEnum.APP_DB_ALMALINUX,
            ResourceAppEnum.APP_DB_DEBIAN
    )),
    APP_CN("cn", "容器", Arrays.asList(
            ResourceAppEnum.APP_CN_DOCKER,
            ResourceAppEnum.APP_CN_KUBERNETES
    )),
    //    APP_PROMETHEUS("auto", "Prometheus监控", Arrays.asList(ResourceAppEnum.APP_PROMETHEUS_PH)),
    APP_FIREWALL("firewall", "安全", Arrays.asList(
            ResourceAppEnum.APP_FIREWALL_SSL_VPN_FIREWALL,
            ResourceAppEnum.APP_FIREWALL_AH_FIREWALL,
            ResourceAppEnum.APP_FIREWALL_QAX_FIREWALL,
            ResourceAppEnum.APP_FIREWALL_SSWK_FIREWALL,
            ResourceAppEnum.APP_FIREWALL_SXF_FIREWALL,
            ResourceAppEnum.APP_FIREWALL_SXF_AD_FIREWALL,
            ResourceAppEnum.APP_FIREWALL_SXF_AC_FIREWALL,
            ResourceAppEnum.APP_FIREWALL_TRX_FIREWALL,
            ResourceAppEnum.APP_FIREWALL_AH_WAF,
            ResourceAppEnum.APP_FIREWALL_LM_WAF,
            ResourceAppEnum.APP_FIREWALL_LM_DEFENSE,
            ResourceAppEnum.APP_FIREWALL_SSWK_DEFENSE,
            ResourceAppEnum.APP_FIREWALL_TRX_DEFENSE,
            ResourceAppEnum.APP_FIREWALL_QMXC_FIREWALL
    )),
    APP_NETWORK("network", "网络", Arrays.asList(
            ResourceAppEnum.APP_NETWORK_H3C_SWITCH,
            ResourceAppEnum.APP_NETWORK_CISCO_SWITCH,
            ResourceAppEnum.APP_NETWORK_HUAWEI_SWITCH,
            ResourceAppEnum.APP_NETWORK_TPLINK_SWITCH,
            ResourceAppEnum.APP_NETWORK_HPE_SWITCH,
            ResourceAppEnum.APP_NETWORK_HUAWEI_ROUTER,
            ResourceAppEnum.APP_NETWORK_SWITCH_H3C_WIRELESS,
            ResourceAppEnum.APP_NETWORK_SWITCH_XINRUI_WIRELESS,
            ResourceAppEnum.APP_NETWORK_CISCO_ROUTER

    )),

    APP_STORED("stored", "存储", Arrays.asList(
            ResourceAppEnum.APP_STORAGE_HUAWEI,
            ResourceAppEnum.APP_STORAGE_INSPUR,
            ResourceAppEnum.APP_STORAGE_SYNOLOGY,
            ResourceAppEnum.APP_STORAGE_3PAR,
            ResourceAppEnum.APP_STORAGE_NETAPP
    )),

    APP_INFRA("infra", "服务器", Arrays.asList(
            ResourceAppEnum.APP_INFRA_IPMI,
            ResourceAppEnum.APP_INFRA_NVIDIA,
            ResourceAppEnum.APP_INFRA_REDFISH
    ));


    /**
     * 角色编码
     */
    private final String code;
    /**
     * 名字
     */
    private final String name;

    private final List<ResourceAppEnum> subEnums;

    // 构造函数重载，不带子级枚举列表的情况
    ResourceCategoryEnum(String code, String name) {
        this.code = code;
        this.name = name;
        this.subEnums = null; // 不包含子级枚举列表
    }
}
