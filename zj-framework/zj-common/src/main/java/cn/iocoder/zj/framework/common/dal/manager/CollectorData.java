package cn.iocoder.zj.framework.common.dal.manager;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.Date;

@TableName("zj_collector")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "zj_collector  | 采集器信息实体")
public class CollectorData {

    @TableId
    @Schema(title = "primary id", example = "2")
    private Long id;

    @Schema(title = "collector identity name", description = "collector identity name")
    @NotBlank(message = "name can not null")
    private String name;

    @Schema(title = "collector ip", description = "collector remote ip")
    @NotBlank(message = "ip can not null")
    private String ip;

    @Schema(title = "collector version", description = "collector version")
    private String version;

    @Schema(title = "collector status: 0-online 1-offline")
    @Min(0)
    private byte status;

    @Schema(title = "collector mode: public or private")
    private String mode;

    @Schema(title = "The creator of this record", example = "tom")
    private String creator;

    @Schema(title = "This record was last modified by")
    private String modifier;

    @Schema(title = "This record creation time (millisecond timestamp)")
    @CreatedDate
    private Date gmtCreate;

    @Schema(title = "Record the latest modification time (timestamp in milliseconds)")
    private Date gmtUpdate;

    @Schema(title = "平台id")
    private Long platformId;

    @Schema(title = "平台名称")
    private String platformName;

    @Schema(title = "项目Id")
    private Long projectId;

    @Schema(title = "项目名称")
    private String projectName;

    @Schema(title = "离线时间")
    private Date offlineTime;

    @Schema(title = "CPU使用率")
    private Double cpuUsage;

    @Schema(title = "内存使用率")
    private Double memUsage;

    @Schema(title = "接收速率")
    private Double rxMbps;

    @Schema(title = "发送速率")
    private Double txMbps;
}
