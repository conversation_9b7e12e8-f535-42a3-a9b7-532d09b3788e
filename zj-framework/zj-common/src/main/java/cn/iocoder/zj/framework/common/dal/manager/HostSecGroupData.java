package cn.iocoder.zj.framework.common.dal.manager;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 **/
@TableName("monitor_host_secgroup")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "monitor_host_secgroup  | 云主机安全组关系实体")
public class HostSecGroupData {

    @TableId
    private Long id;
    /**
     * 云主机uuid
     */
    private String hostUuid;
    /**
     * 安全组uuid
     */
    private String secgroupUuid;
    /**
     * 平台id
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;
}
