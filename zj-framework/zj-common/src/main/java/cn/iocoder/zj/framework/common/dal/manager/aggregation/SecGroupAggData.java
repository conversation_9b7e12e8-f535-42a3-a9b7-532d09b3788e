package cn.iocoder.zj.framework.common.dal.manager.aggregation;

import cn.iocoder.zj.framework.common.dal.manager.HostSecGroupData;
import cn.iocoder.zj.framework.common.dal.manager.SecGroupData;
import cn.iocoder.zj.framework.common.dal.manager.SecGroupRuleData;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 **/
@Data
public class SecGroupAggData {

    /**
     * 安全组
     */
    List<SecGroupData>  secGroups;

    /**
     * 安全组规则
     */
    List<SecGroupRuleData>  secGroupRules;

    /**
     * 云主机和安全组关系
     */
    List<HostSecGroupData>  hostSecGroups;
}
