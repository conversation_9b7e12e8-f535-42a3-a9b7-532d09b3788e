package cn.iocoder.zj.framework.common.util.date;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtils {

    /**
     * 时区 - 默认
     */
    public static final String TIME_ZONE_DEFAULT = "GMT+8";

    /**
     * 秒转换成毫秒
     */
    public static final long SECOND_MILLIS = 1000;

    public static final String FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND = "yyyy-MM-dd HH:mm:ss";

    /**
     * 将 LocalDateTime 转换成 Date
     *
     * @param date LocalDateTime
     * @return LocalDateTime
     */
    public static Date of(LocalDateTime date) {
        if (date == null) {
            return null;
        }
        // 将此日期时间与时区相结合以创建 ZonedDateTime
        ZonedDateTime zonedDateTime = date.atZone(ZoneId.systemDefault());
        // 本地时间线 LocalDateTime 到即时时间线 Instant 时间戳
        Instant instant = zonedDateTime.toInstant();
        // UTC时间(世界协调时间,UTC + 00:00)转北京(北京,UTC + 8:00)时间
        return Date.from(instant);
    }

    /**
     * 将 Date 转换成 LocalDateTime
     *
     * @param date Date
     * @return LocalDateTime
     */
    public static LocalDateTime of(Date date) {
        if (date == null) {
            return null;
        }
        // 转为时间戳
        Instant instant = date.toInstant();
        // UTC时间(世界协调时间,UTC + 00:00)转北京(北京,UTC + 8:00)时间
        return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }

    public static Date addTime(Duration duration) {
        return new Date(System.currentTimeMillis() + duration.toMillis());
    }

    public static boolean isExpired(Date time) {
        return System.currentTimeMillis() > time.getTime();
    }

    public static boolean isExpired(LocalDateTime time) {
        LocalDateTime now = LocalDateTime.now();
        return now.isAfter(time);
    }

    public static long diff(Date endTime, Date startTime) {
        return endTime.getTime() - startTime.getTime();
    }

    /**
     * 创建指定时间
     *
     * @param year  年
     * @param mouth 月
     * @param day   日
     * @return 指定时间
     */
    public static Date buildTime(int year, int mouth, int day) {
        return buildTime(year, mouth, day, 0, 0, 0);
    }

    public static LocalDateTime buildLocalDateTime(int year, int mouth, int day) {
        return LocalDateTime.of(year, mouth, day, 0, 0, 0);
    }

    /**
     * 创建指定时间
     *
     * @param year   年
     * @param mouth  月
     * @param day    日
     * @param hour   小时
     * @param minute 分钟
     * @param second 秒
     * @return 指定时间
     */
    public static Date buildTime(int year, int mouth, int day,
                                 int hour, int minute, int second) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, mouth - 1);
        calendar.set(Calendar.DAY_OF_MONTH, day);
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, second);
        calendar.set(Calendar.MILLISECOND, 0); // 一般情况下，都是 0 毫秒
        return calendar.getTime();
    }

    public static Date max(Date a, Date b) {
        if (a == null) {
            return b;
        }
        if (b == null) {
            return a;
        }
        return a.compareTo(b) > 0 ? a : b;
    }

    public static LocalDateTime max(LocalDateTime a, LocalDateTime b) {
        if (a == null) {
            return b;
        }
        if (b == null) {
            return a;
        }
        return a.isAfter(b) ? a : b;
    }

    public static boolean beforeNow(Date date) {
        return date.getTime() < System.currentTimeMillis();
    }

    public static boolean afterNow(Date date) {
        return date.getTime() >= System.currentTimeMillis();
    }

    public static boolean afterNow(LocalDateTime localDateTime) {
        return localDateTime.isAfter(LocalDateTime.now());
    }

    /**
     * 计算当期时间相差的日期
     *
     * @param field  日历字段.<br/>eg:Calendar.MONTH,Calendar.DAY_OF_MONTH,<br/>Calendar.HOUR_OF_DAY等.
     * @param amount 相差的数值
     * @return 计算后的日志
     */
    public static Date addDate(int field, int amount) {
        return addDate(null, field, amount);
    }

    /**
     * 计算当期时间相差的日期
     *
     * @param date   设置时间
     * @param field  日历字段 例如说，{@link Calendar#DAY_OF_MONTH} 等
     * @param amount 相差的数值
     * @return 计算后的日志
     */
    public static Date addDate(Date date, int field, int amount) {
        if (amount == 0) {
            return date;
        }
        Calendar c = Calendar.getInstance();
        if (date != null) {
            c.setTime(date);
        }
        c.add(field, amount);
        return c.getTime();
    }

    /**
     * 是否今天
     *
     * @param date 日期
     * @return 是否
     */
    public static boolean isToday(Date date) {
        if (date == null) {
            return false;
        }
        return DateUtil.isSameDay(date, new Date());
    }

    /**
     * 是否今天
     *
     * @param date 日期
     * @return 是否
     */
    public static boolean isToday(LocalDateTime date) {
        return LocalDateTimeUtil.isSameDay(date, LocalDateTime.now());
    }

    /**
     * 获取年月的最后一天
     *
     * @param year 年
     * @param month 月
     * @return 最后一天
     */

    public static Integer getLastDayOfMonth(String year,String month){
        Calendar cal = Calendar.getInstance();

        //设置年份

        cal.set(Calendar.YEAR,Integer.valueOf(year));

        //设置月份

        cal.set(Calendar.MONTH, Integer.valueOf(month)-1);

        //获取某月最大天数

        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);

//        //设置日历中月份的最大天数
//
//        cal.set(Calendar.DAY_OF_MONTH, lastDay);
//
//        //格式化日期
//
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//
//        String lastDayOfMonth = sdf.format(cal.getTime());

        return lastDay;
    }

    public static Date getYesterday(){
        // 获取当前日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        // 减去一天
        calendar.add(Calendar.DAY_OF_MONTH, -1);

        // 获取昨天日期的Date对象
        Date yesterday = calendar.getTime();
        return yesterday;
    }

    public static Date getLastWeek() {
        // 获取当前日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());

        // 设置为当前周的周一
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);

        // 减去一周
        calendar.add(Calendar.WEEK_OF_YEAR, -1);

        // 获取上一周周一的Date对象
        return calendar.getTime();
    }

    public static Date getFirstDayOfLastMonth() {
        // 获取当前日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());

        // 设置为上个月
        calendar.add(Calendar.MONTH, -1);
        // 设置为1号
        calendar.set(Calendar.DAY_OF_MONTH, 1);

        // 获取上个月1号的Date对象
        return calendar.getTime();
    }

    public static Boolean compareDate(String dateString1,String dateString2){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date date1 = sdf.parse(dateString1);
            Date date2 = sdf.parse(dateString2);

            if (date1.compareTo(date2) <= 0) {
                return false;
            } else if (date1.compareTo(date2) >= 0) {
                System.out.println(dateString1 + " is after " + dateString2);
                return true;
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return false;
    }

    public static Date toDate(String dateString){
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

        try {
            Date date = format.parse(dateString);
            return date;

        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String getCurrentDate(Long timestamp){
        Instant instant = Instant.ofEpochMilli(timestamp);
        LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String dateString = dateTime.format(formatter);
        return dateString;
    }


    public static String dateFormatExample(Date date){
        SimpleDateFormat sdf = new SimpleDateFormat(FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND);
        String formattedDate = sdf.format(date);
        return formattedDate;
    }


    public static List<String> getLastMonthDates() {
        List<String> dates = new ArrayList<>();
        // 获取本月第一天
        DateTime start = DateUtil.beginOfMonth(DateUtil.date());
        // 获取本月最后一天
        DateTime end = DateUtil.endOfMonth(DateUtil.date());

        while (!start.isAfter(end)) {
            dates.add(DateUtil.formatDate(start));
            start = DateUtil.offsetDay(start, 1); // 每次循环加一天
        }
        return dates;
    }

    public static List<String> getMonthDates() {
        List<String> dates = new ArrayList<>();
        // 获取当前日期
        DateTime today = DateUtil.date();
        // 获取上个月的第一天
        DateTime startOfLastMonth = DateUtil.beginOfMonth(DateUtil.offsetMonth(today, -1));
        // 获取上个月的最后一天
        DateTime endOfLastMonth = DateUtil.endOfMonth(DateUtil.offsetMonth(today, -1));

        // 从上个月的第一天开始迭代到最后一天
        while (!startOfLastMonth.isAfter(endOfLastMonth)) {
            dates.add(DateUtil.formatDate(startOfLastMonth));
            startOfLastMonth = DateUtil.offsetDay(startOfLastMonth, 1); // 每次循环加一天
        }
        return dates;
    }

    public static List<String> getLastWeekDates() {
        List<String> dates = new ArrayList<>();
        DateTime today = DateUtil.date();
        // 获取本周的开始日期（周一）
        DateTime startOfWeek = DateUtil.beginOfWeek(today);
        // 从周一开始迭代至周日
        for (int i = 0; i < 7; i++) {
            DateTime day = DateUtil.offsetDay(startOfWeek, i);
            dates.add(DateUtil.formatDate(day));
        }
        return dates;
    }

    public static List<String> getWeekDates() {
        List<String> dates = new ArrayList<>();
        DateTime today = DateUtil.date();
        // 获取本周的开始日期（周一）
        DateTime startOfWeek = DateUtil.beginOfWeek(today);
        // 获取上周的开始日期（上周的周一）
        DateTime startOfLastWeek = DateUtil.offsetDay(startOfWeek, -7);
        // 从上周的周一开始迭代至上周的周日
        for (int i = 0; i < 7; i++) {
            DateTime day = DateUtil.offsetDay(startOfLastWeek, i);
            dates.add(DateUtil.formatDate(day));
        }
        return dates;
    }

    public static Date convertDate(String dateString) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ");
            Date date = sdf.parse(dateString);
            return date;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


}
