package cn.iocoder.zj.framework.common.dal.manager;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class ScanIPRequest {
    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 扫描IP数据列表
     */
    private List<ScanIPData> scanIPDataList;

    private Long ipRangeId;

    //总数
    private Integer total;

    private Integer pingCount;

    private Integer tcpCount;

    private Integer snmpCount;

    private Integer ratio;
}