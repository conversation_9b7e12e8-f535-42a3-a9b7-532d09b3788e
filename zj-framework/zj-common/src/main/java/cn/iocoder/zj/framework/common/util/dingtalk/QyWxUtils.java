package cn.iocoder.zj.framework.common.util.dingtalk;

import cn.iocoder.zj.framework.common.pojo.DingDingAuthorizationDTO;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jsoup.internal.StringUtil;
import org.springframework.util.CollectionUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class QyWxUtils {
    private static final String ACCESS_TOKEN_URL = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=";
    private static final String SEND_MESSAGE_URL = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=";
    private static final String GET_USER_ID_URL = "https://qyapi.weixin.qq.com/cgi-bin/user/getuserid?access_token=";
    private static final String MEDIA_UPLOAD_URL = "https://qyapi.weixin.qq.com/cgi-bin/media/upload?access_token=";

    public static String getUserid(String accessToken, String code) {
        String url = "https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo?access_token="+accessToken+ "&code="+code;
        WeComResponse weComResponse = httpGet(url);
        if (weComResponse != null && "ok".equals(weComResponse.getErrmsg())) {
            return weComResponse.getUserid();
        }
        return null;
    }


    /**
     * 获取企业微信 Token
     */
    public static String getToken(String appKey, String appSecret) {
        String url = ACCESS_TOKEN_URL + appKey + "&corpsecret=" + appSecret;
        WeComResponse response = httpGet(url);
        if (response != null && "ok".equals(response.getErrmsg())) {
            return response.getAccess_token();
        }
        return null;
    }

    /**
     * 发送企业微信消息
     */
    public static void sendTextMsg(String accessToken, DingDingAuthorizationDTO dto, String type) {
        String userID = getUserID(dto.getMobile(), accessToken);
        if (StringUtil.isBlank(userID)) {
            log.info("企微userID为空,未获取到用户UserId,{}",dto.getMobile());
            return;
        }

        Map<String, Object> message = createBaseMessage(userID, dto, type);

        if ("File".equals(type)) {
            WeComResponse response = uploadMedia(dto.getUrl(), accessToken);
            if (response != null && "ok".equals(response.getErrmsg())) {
                Map<String, Object> fileCard = new HashMap<>();
                fileCard.put("media_id", response.getMedia_id());
                message.put("file", fileCard);
            }
        }

        sendMessage(accessToken, message);
    }

    private static Map<String, Object> createBaseMessage(String userID, DingDingAuthorizationDTO dto, String type) {
        Map<String, Object> message = new HashMap<>();
        message.put("touser", userID);
        message.put("agentid", dto.getAgentId());

        Map<String, Object> textCard = new HashMap<>();
        textCard.put("title", "云监控告警通知");

        if ("File".equals(type)) {
            textCard.put("description", generateHtmlContent(dto.getWarningTime(), dto.getWarningItem(), dto.getWarningObject()));
            textCard.put("url", dto.getUrl());
            message.put("msgtype", "file");
        } else if("text".equals(type)){
            textCard.put("description", generateHtmlContent(dto.getWarningTime(), dto.getWarningObject(), dto.getWarningItem()));
            textCard.put("url", dto.getUrl());
            message.put("msgtype", "textcard");
        }else {
            textCard.put("description", generateHtmlContentTo2(dto.getWarningTime(), dto.getWarningObject(), dto.getWarningItem()));
            textCard.put("url", dto.getUrl());
            message.put("msgtype", "textcard");
        }

        message.put("textcard", textCard);
        return message;
    }


    /**
     * 发送 HTTP GET 请求
     */
    private static WeComResponse httpGet(String url) {
        StringBuilder response = new StringBuilder();

        try {
            HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
            connection.setRequestMethod("GET");
            connection.setRequestProperty("Accept", "*/*");
            connection.setRequestProperty("User-Agent", "Mozilla/5.0");
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
            } finally {
                connection.disconnect();
            }
            log.info("企微返回结果: " + JsonUtils.toJsonString(response));
            return JsonUtils.parseObject2(response.toString(), WeComResponse.class);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return null;
    }

    /**
     * 发送 HTTP POST 请求
     */
    public static WeComResponse httpPost(String url, Map<String, Object> params) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost post = new HttpPost(url);
            post.addHeader("Content-Type", "application/json;charset=utf-8");

            // 设置请求参数
            if (!CollectionUtils.isEmpty(params)) {
                String json = JsonUtils.toJsonString(params);
                post.setEntity(new StringEntity(json, StandardCharsets.UTF_8));
            }

            try (CloseableHttpResponse response = httpClient.execute(post)) {
                HttpEntity entity = response.getEntity();
                if (entity != null) {
                    String result = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                    log.info("企微返回结果: " + JsonUtils.toJsonString(result));
                    return JsonUtils.parseObject2(result, WeComResponse.class);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 发送消息
     */
    private static void sendMessage(String accessToken, Map<String, Object> message) {
        log.info("企微发送信息: " + JsonUtils.toJsonString(message));
        WeComResponse response = httpPost(SEND_MESSAGE_URL + accessToken, message);
        if (response != null && "ok".equals(response.getErrmsg())) {
            System.out.println("Message sent successfully.");
        } else {
            System.err.println("Failed to send message: " + (response != null ? response.getErrmsg() : "Unknown error"));
        }
    }

    /**
     * 获取用户 ID
     */
    public static String getUserID(String mobile, String token) {
        Map<String, Object> params = new HashMap<>();
        params.put("mobile", mobile);
        WeComResponse response = httpPost(GET_USER_ID_URL + token, params);
        log.info("企微获取userid信息: {},{}" + JsonUtils.toJsonString(response),mobile);
        if (response != null && "ok".equals(response.getErrmsg())) {
            return response.getUserid();
        } else if (response != null && response.getErrcode() == 60020) {
            log.info("ip限制，未加入白名单");
            return null;
        }
        return null;
    }


    public static WeComResponse uploadMedia(String fileUrl, String accessToken) {
        File file = new File(fileUrl);
        if (!file.exists()) {
            log.info("上传文件为空");
            return null;
        }

        HttpPost uploadFile = new HttpPost(MEDIA_UPLOAD_URL + accessToken + "&type=file");
        MultipartEntityBuilder builder = MultipartEntityBuilder.create().setMode(HttpMultipartMode.RFC6532);
        builder.addBinaryBody("media", file);
        HttpEntity multipart = builder.build();
        uploadFile.setEntity(multipart);

        try (CloseableHttpClient httpClient = HttpClients.createDefault();
             CloseableHttpResponse response = httpClient.execute(uploadFile)) {

            HttpEntity responseEntity = response.getEntity();
            if (responseEntity != null) {
                String result = EntityUtils.toString(responseEntity, StandardCharsets.UTF_8);
                return JsonUtils.parseObject2(result, WeComResponse.class);
            }
        } catch (IOException e) {
            System.err.println("An error occurred during the media upload: " + e.getMessage());
        }
        return null;
    }



    private static String generateHtmlContent(String alarmTime, String alarmProject, String alarmCount) {
        return "<div class=\"normal\">告警时间: " + alarmTime + "</div>" +
               "<div class=\"normal\">告警项目: " + alarmProject + "</div>" +
               "<div class=\"normal\">告警对象: " + alarmCount + "</div>";
    }

    private static String generateHtmlContentTo2(String alarmTime, String alarmProject, String alarmCount) {
        return "<div class=\"normal\">告警时间: " + alarmTime + "</div>" +
                "<div class=\"normal\">告警项目: " + alarmProject + "</div>" +
                "<div class=\"normal\">告警条数: " + alarmCount + "</div>";
    }

    public static class WeComResponse {
        private String errmsg;
        private int errcode;
        private String access_token;
        private String userid;
        private String media_id;
        private String response_code;

        public String getErrmsg() { return errmsg; }
        public void setErrmsg(String errmsg) { this.errmsg = errmsg; }
        public int getErrcode() { return errcode; }
        public void setErrcode(int errcode) { this.errcode = errcode; }
        public String getAccess_token() { return access_token; }
        public void setAccess_token(String access_token) { this.access_token = access_token; }
        public String getUserid() { return userid; }
        public void setUserid(String userid) { this.userid = userid; }
        public String getMedia_id() { return media_id; }
        public String getResponse_code() { return response_code; }
        public void setMedia_id(String media_id) { this.media_id = media_id; }
        public void setResponse_code(String response_code) { this.response_code = response_code; }
    }
}


