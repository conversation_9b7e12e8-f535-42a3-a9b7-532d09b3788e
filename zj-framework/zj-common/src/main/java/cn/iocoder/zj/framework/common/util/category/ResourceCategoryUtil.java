package cn.iocoder.zj.framework.common.util.category;

import cn.iocoder.zj.framework.common.enums.ResourceAppEnum;
import cn.iocoder.zj.framework.common.enums.ResourceCategoryEnum;

import java.util.List;
import java.util.Optional;

/**
 * @ClassName : ResourceCategoryUtil  //类名
 * @Description : hz资源类型  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/4/2  11:17
 */
public class ResourceCategoryUtil {
    public static List<ResourceAppEnum> getSubEnumsByCategory(String category) {
        Optional<ResourceCategoryEnum> optionalCategory = getCategoryByCode(category);
        if (optionalCategory.isPresent()) {
            return optionalCategory.get().getSubEnums();
        } else {
            throw new IllegalArgumentException("Invalid category: " + category);
        }
    }

    private static Optional<ResourceCategoryEnum> getCategoryByCode(String code) {
        for (ResourceCategoryEnum category : ResourceCategoryEnum.values()) {
            if (category.getCode().equals(code)) {
                return Optional.of(category);
            }
        }
        return Optional.empty();
    }

    public static String getResourceLabel(String app) {
        for (ResourceAppEnum resource : ResourceAppEnum.values()) {
            if (resource.getValue().equals(app)) {
                return resource.getLabel();
            }
        }
        return null; // 如果没有匹配的枚举值，返回null或者其他默认值
    }
}
