package cn.iocoder.zj.framework.file.config;

import cn.iocoder.zj.framework.file.core.client.FileClientFactory;
import cn.iocoder.zj.framework.file.core.client.FileClientFactoryImpl;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;

/**
 * 文件配置类
 *
 * <AUTHOR>
 */
@AutoConfiguration
public class ZjFileAutoConfiguration {

    @Bean
    public FileClientFactory fileClientFactory() {
        return new FileClientFactoryImpl();
    }

}
