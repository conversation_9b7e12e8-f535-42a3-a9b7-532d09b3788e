<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>zj</artifactId>
        <groupId>cn.iocoder.cloud</groupId>
        <version>${revision}</version>
    </parent>
    <packaging>pom</packaging>
    <modules>
        <module>zj-common</module>
        <module>zj-spring-boot-starter-env</module>
        <module>zj-spring-boot-starter-banner</module>
        <module>zj-spring-boot-starter-mybatis</module>
        <module>zj-spring-boot-starter-redis</module>
        <module>zj-spring-boot-starter-web</module>
        <module>zj-spring-boot-starter-security</module>

        <module>zj-spring-boot-starter-file</module>
        <module>zj-spring-boot-starter-monitor</module>
        <module>zj-spring-boot-starter-protection</module>
<!--        <module>zj-spring-boot-starter-config</module>-->
        <module>zj-spring-boot-starter-job</module>
        <module>zj-spring-boot-starter-mq</module>
        <module>zj-spring-boot-starter-rpc</module>

        <module>zj-spring-boot-starter-excel</module>
        <module>zj-spring-boot-starter-test</module>

        <module>zj-spring-boot-starter-biz-operatelog</module>
        <module>zj-spring-boot-starter-biz-dict</module>
        <module>zj-spring-boot-starter-biz-sms</module>

        <module>zj-spring-boot-starter-biz-pay</module>
        <module>zj-spring-boot-starter-biz-weixin</module>
        <module>zj-spring-boot-starter-biz-social</module>
        <module>zj-spring-boot-starter-biz-tenant</module>
        <module>zj-spring-boot-starter-biz-data-permission</module>
        <module>zj-spring-boot-starter-biz-error-code</module>
        <module>zj-spring-boot-starter-biz-ip</module>

        <module>zj-spring-boot-starter-flowable</module>
        <module>zj-spring-boot-starter-captcha</module>
        <module>zj-spring-boot-starter-desensitize</module>
        <module>zj-spring-boot-starter-es</module>
        <module>zj-spring-boot-starter-influx</module>
        <module>zj-spring-boot-starter-hertzbeat-warehouse</module>
        <module>zj-spring-boot-starter-hertzbeat-alerter</module>
        <module>zj-spring-boot-starter-hertzbeat-remoting</module>
        <module>zj-spring-boot-starter-hertzbeat-push</module>
        <module>zj-spring-boot-starter-hertzbeat-collector</module>
        <module>zj-common-hz</module>
        <module>zj-spring-boot-starter-ssh</module>
        <module>zj-spring-boot-starter-proxy-core</module>
        <module>zj-spring-boot-starter-hertzbeat-plugin</module>
        <module>zj-spring-boot-starter-hertzbeat-grafana</module>
        <module>zj-spring-boot-starter-warehouse</module>

    </modules>

    <artifactId>zj-framework</artifactId>
    <description>
        该包是技术组件，每个子包，代表一个组件。每个组件包括两部分：
            1. core 包：是该组件的核心封装
            2. config 包：是该组件基于 Spring 的配置

        技术组件，也分成两类：
            1. 框架组件：和我们熟悉的 MyBatis、Redis 等等的拓展
            2. 业务组件：和业务相关的组件的封装，例如说数据字典、操作日志等等。
        如果是业务组件，Maven 名字会包含 biz
    </description>
    <url>https://github.com/YunaiV/ruoyi-vue-pro</url>

    <distributionManagement>
        <repository>
            <id>nexus</id>
            <name>release</name>
            <url>http://**************:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus</id>
            <name>snapshot</name>
            <url>http://**************:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
