package cn.iocoder.zj.framework.redis.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * @ClassName : ZjCacheProperties  //类名
 * @Description :  * Cache 配置项
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2023/8/25  15:23
 */
@ConfigurationProperties("zj.cache")
@Data
@Validated
public class ZjCacheProperties {
    /**
     * {@link #redisScanBatchSize} 默认值
     */
    private static final Integer REDIS_SCAN_BATCH_SIZE_DEFAULT = 30;

    /**
     * redis scan 一次返回数量
     */
    private Integer redisScanBatchSize = REDIS_SCAN_BATCH_SIZE_DEFAULT;


}
