<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.iocoder.cloud</groupId>
        <artifactId>zj-framework</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>zj-spring-boot-starter-biz-weixin</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>微信拓展
        1. 基于 weixin-java-mp 库，对接微信公众号平台。目前主要解决微信公众号的支付场景。
    </description>
    <url>https://github.com/YunaiV/ruoyi-vue-pro</url>

    <dependencies>
        <dependency>
            <groupId>cn.iocoder.cloud</groupId>
            <artifactId>zj-common</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>cn.iocoder.cloud</groupId>
            <artifactId>zj-spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 三方云服务相关 -->
        <dependency>
            <groupId>com.github.binarywang</groupId>
<!--            <artifactId>weixin-java-mp</artifactId>-->
            <artifactId>wx-java-mp-spring-boot-starter</artifactId>
            <version>4.4.0</version>
        </dependency>
        <!-- TODO 芋艿：清理 -->
    </dependencies>

</project>
