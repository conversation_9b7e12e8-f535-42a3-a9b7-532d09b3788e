package cn.iocoder.zj.framework.warehouse.convert;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * InfluxDB 查询语句转换为 PromQL 的工具类
 */
@Slf4j
public class InfluxToPromQLConverter {

    private static final String SELECT_PATTERN = "SELECT\\s+([^\\s]+)\\s+(?:AS\\s+([^\\s]+)\\s+)?FROM\\s+([^\\s]+)(.*)";
    private static final String WHERE_PATTERN = "WHERE\\s+(.+?)(?:\\s+GROUP\\s+BY|\\s*$)";
    private static final String GROUP_BY_PATTERN = "GROUP\\s+BY\\s+time\\(([^)]+)\\)";
    private static final String FILL_PATTERN = "FILL\\(([^)]+)\\)";
    private static final Pattern TIME_RANGE_PATTERN = Pattern.compile("time\\s*([><=]+)\\s*([^\\s]+)");

    /**
     * 转换 InfluxDB 查询为 PromQL
     *
     * @param influxQuery InfluxDB 查询语句
     * @return PromQL 查询语句
     */
    public static String convert(String influxQuery) {
        try {
            // 解析基本组件
            Pattern pattern = Pattern.compile(SELECT_PATTERN, Pattern.CASE_INSENSITIVE);
            Matcher matcher = pattern.matcher(influxQuery);

            if (!matcher.find()) {
                throw new IllegalArgumentException("Invalid InfluxDB query format");
            }

            // 提取查询组件
            String function = matcher.group(1);
            String measurement = matcher.group(3);
            String remainingQuery = matcher.group(4);

            // 解析 WHERE 子句
            Map<String, String> labels = new HashMap<>();
            String timeRange = extractTimeRange(remainingQuery);
            extractWhereClauses(remainingQuery, labels);

            // 解析 GROUP BY 时间间隔
            String interval = extractGroupByInterval(remainingQuery);

            // 构建 PromQL
            return buildPromQL(measurement, function, labels, interval, timeRange);

        } catch (Exception e) {
            log.error("Error converting InfluxDB query to PromQL", e);
            throw new RuntimeException("Failed to convert query", e);
        }
    }

    /**
     * 提取时间范围条件
     */
    private static String extractTimeRange(String query) {
        Matcher matcher = TIME_RANGE_PATTERN.matcher(query);
        if (matcher.find()) {
            String operator = matcher.group(1);
            String timeValue = matcher.group(2);
            
            // 处理 now() 相关的时间
            if (timeValue.contains("now()")) {
                return convertTimeRange(timeValue);
            }
            
            return timeValue;
        }
        return null;
    }

    /**
     * 转换时间范围表达式
     */
    private static String convertTimeRange(String timeRange) {
        if (timeRange.contains("-")) {
            // 处理 now() - 24h 这样的格式
            String duration = timeRange.substring(timeRange.indexOf("-")).trim();
            return duration.replace("now() - ", "");
        }
        return timeRange;
    }

    /**
     * 提取 WHERE 子句中的标签条件
     */
    private static void extractWhereClauses(String query, Map<String, String> labels) {
        Pattern pattern = Pattern.compile(WHERE_PATTERN, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(query);
        
        if (matcher.find()) {
            String whereClauses = matcher.group(1);
            String[] conditions = whereClauses.split("AND");
            
            for (String condition : conditions) {
                condition = condition.trim();
                if (!condition.toLowerCase().contains("time")) {
                    String[] parts = condition.split("=");
                    if (parts.length == 2) {
                        String key = parts[0].trim();
                        String value = parts[1].trim().replace("'", "").replace("\"", "");
                        labels.put(key, value);
                    }
                }
            }
        }
    }

    /**
     * 提取 GROUP BY 时间间隔
     */
    private static String extractGroupByInterval(String query) {
        Pattern pattern = Pattern.compile(GROUP_BY_PATTERN, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(query);
        
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    /**
     * 构建 PromQL 查询
     */
    private static String buildPromQL(String measurement, String function, 
                                    Map<String, String> labels, String interval, 
                                    String timeRange) {
        StringBuilder promQL = new StringBuilder();

        // 处理聚合函数
        String promFunction = convertFunction(function);
        promQL.append(promFunction).append("(");

        // 添加指标名称和标签
        promQL.append(measurement);
        if (!labels.isEmpty()) {
            promQL.append("{");
            List<String> labelStrings = new ArrayList<>();
            labels.forEach((k, v) -> labelStrings.add(k + "=\"" + v + "\""));
            promQL.append(String.join(",", labelStrings));
            promQL.append("}");
        }

        // 添加时间窗口
        if (interval != null) {
            promQL.append("[").append(interval).append("]");
        }

        promQL.append(")");

        return promQL.toString();
    }

    /**
     * 转换 InfluxDB 函数为 PromQL 函数
     */
    private static String convertFunction(String influxFunction) {
        String func = influxFunction.toUpperCase();
        switch (func) {
            case "MEAN":
                return "avg_over_time";
            case "MAX":
                return "max_over_time";
            case "MIN":
                return "min_over_time";
            case "SUM":
                return "sum_over_time";
            case "COUNT":
                return "count_over_time";
            default:
                return "avg_over_time"; // 默认使用 avg_over_time
        }
    }

    /**
     * 生成完整的 VictoriaMetrics URL
     */
    public static String generateVictoriaMetricsUrl(String baseUrl, String promQL, 
                                                   String timeRange, String step) {
        try {
            StringBuilder url = new StringBuilder(baseUrl);
            url.append("/api/v1/query_range")
               .append("?query=").append(java.net.URLEncoder.encode(promQL, "UTF-8"))
               .append("&step=").append(step);

            if (timeRange != null) {
                long endTime = System.currentTimeMillis() / 1000;
                long startTime = endTime - parseTimeRange(timeRange);
                url.append("&start=").append(startTime)
                   .append("&end=").append(endTime);
            }

            return url.toString();
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate URL", e);
        }
    }

    /**
     * 解析时间范围
     */
    private static long parseTimeRange(String timeRange) {
        timeRange = timeRange.toLowerCase();
        long multiplier;
        
        if (timeRange.endsWith("h")) {
            multiplier = 3600;
        } else if (timeRange.endsWith("d")) {
            multiplier = 86400;
        } else if (timeRange.endsWith("w")) {
            multiplier = 604800;
        } else if (timeRange.endsWith("m")) {
            multiplier = 60;
        } else {
            throw new IllegalArgumentException("Unsupported time range format: " + timeRange);
        }

        String number = timeRange.substring(0, timeRange.length() - 1);
        return Long.parseLong(number) * multiplier;
    }

}

