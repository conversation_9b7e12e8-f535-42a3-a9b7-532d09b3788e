package cn.iocoder.zj.framework.warehouse.config;


import cn.iocoder.zj.framework.warehouse.store.history.influxdb.InfluxdbProperties;
import cn.iocoder.zj.framework.warehouse.store.history.vm.VictoriaMetricsClusterProperties;
import cn.iocoder.zj.framework.warehouse.store.history.vm.VictoriaMetricsProperties;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;

@AutoConfiguration
@EnableConfigurationProperties({VictoriaMetricsProperties.class,
        VictoriaMetricsClusterProperties.class,
        InfluxdbProperties.class})
@ComponentScan(basePackages = "cn.iocoder.zj.framework.warehouse")
public class WarehousedbAutoConfiguration {

}
