package cn.iocoder.zj.framework.warehouse.controller.admin;


import cn.iocoder.zj.framework.common.dal.manager.MetricHistoryData;
import cn.iocoder.zj.framework.warehouse.service.ZjMetricsDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.hertzbeat.common.entity.dto.Message;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static org.apache.hertzbeat.common.constants.CommonConstants.FAIL_CODE;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

@RestController
@RequestMapping(path = "/server-collector", produces = {APPLICATION_JSON_VALUE})
@Tag(name = "私有云监控指标数据API")
public class MetricsCloudDataController {

    private final ZjMetricsDataService metricsDataService;

    public MetricsCloudDataController(ZjMetricsDataService metricsDataService) {
        this.metricsDataService = metricsDataService;
    }


    @GetMapping("/api/monitor/{resourceId}/metric/{metricFull}")
    @Operation(summary = "查询监控的指定指标的历史数据", description = "查询监控下的指定指标的历史数据")
    public ResponseEntity<Message<MetricHistoryData>> getMetricHistoryData(
            @Parameter(description = "监控任务ID", example = "343254354")
            @PathVariable String resourceId,
            @Parameter(description = "监控指标全路径", example = "mem_task.MemoryUsedBytes")
            @PathVariable() String metricFull,
            @Parameter(description = "查询历史时间段,默认6h-6小时:s-秒、m-分, h-小时, d-天, w-周", example = "6h")
            @RequestParam(required = false) String history,
            @Parameter(description = "开始时间", example = "1745164800000")
            @RequestParam(required = false) Long startTime,
            @Parameter(description = "结束时间", example = "1745251200000")
            @RequestParam(required = false) Long endTime,
            @Parameter(description = "取值间隔", example = "5m s-秒、m-分, h-小时, d-天, w-周")
            @RequestParam(required = false) String step,
            @Parameter(description = "取值类型", example = "avg 平均, min 最小值, max 最大值")
            @RequestParam(required = false) String type,
            @Parameter(description = "平台id", example = "1")
            @RequestParam(required = false) String platformId
    ) {
        if (!metricsDataService.getWarehouseStorageServerStatus()) {
            return ResponseEntity.ok(Message.fail(FAIL_CODE, "time series database not available"));
        }
        String[] names = metricFull.split("\\.");

        String metric = names[0];
        String metrics = names[1];
        MetricHistoryData historyData = metricsDataService.getMetricHistoryData(resourceId, metric, metrics, history, false, startTime, endTime,step,type, platformId);
        return ResponseEntity.ok(Message.success(historyData));
    }


    @GetMapping("/api/monitor/metric/{metricFull}")
    @Operation(summary = "查询首页监控的指定指标的历史数据", description = "查询监控下的指定指标的历史数据")
    public ResponseEntity<Message<MetricHistoryData>> getMetricHistoryData(
            @Parameter(description = "监控任务ID", example = "343254354")
            @PathVariable() String metricFull,
            @Parameter(description = "查询历史时间段,默认6h-6小时:s-秒、m-分, h-小时, d-天, w-周", example = "6h")
            @RequestParam(required = false) String history,
            @Parameter(description = "开始时间", example = "1745164800000")
            @RequestParam(required = false) Long startTime,
            @Parameter(description = "结束时间", example = "1745251200000")
            @RequestParam(required = false) Long endTime,
            @Parameter(description = "取值间隔", example = "5m s-秒、m-分, h-小时, d-天, w-周")
            @RequestParam(required = false) String step,
            @Parameter(description = "取值类型", example = "avg 平均, min 最小值, max 最大值")
            @RequestParam(required = false) String type,
            @Parameter(description = "平台id", example = "1")
            @RequestParam(required = false) String platformId
    ) {
        if (!metricsDataService.getWarehouseStorageServerStatus()) {
            return ResponseEntity.ok(Message.fail(FAIL_CODE, "time series database not available"));
        }
        String[] names = metricFull.split("\\.");

        String metric = names[0];
        String metrics = names[1];

        MetricHistoryData historyData = metricsDataService.getMetricHistoryData("", metric, metrics, history, false, startTime, endTime, step, type,platformId);
        return ResponseEntity.ok(Message.success(historyData));
    }

}
