/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.zj.framework.warehouse.store.history.influxdb;

import cn.iocoder.zj.framework.common.message.ClusterMsg;
import cn.iocoder.zj.framework.common.pojo.VictoriaMetricsDTO;
import cn.iocoder.zj.framework.warehouse.store.history.AbstractHistoryDataStorage;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import org.apache.hertzbeat.common.constants.NetworkConstants;
import org.apache.hertzbeat.common.entity.dto.Value;
import org.apache.http.ssl.SSLContexts;
import org.influxdb.InfluxDB;
import org.influxdb.InfluxDBFactory;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.net.ssl.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * HistoryInfluxdbDataStorage class
 */
@Component
@ConditionalOnProperty(prefix = "warehouse.store.influxdb", name = "enabled", havingValue = "true")
@Slf4j
public class InfluxdbDataStorage extends AbstractHistoryDataStorage {

    private static final String DATABASE = "zj_cloud_hz";

    private static final String SHOW_DATABASE = "SHOW DATABASES";

    private static final String CREATE_DATABASE = "CREATE DATABASE %s";

    private static final String QUERY_HISTORY_SQL = "SELECT instance, %s FROM %s WHERE time >= now() - %s order by time desc";

    private static final String QUERY_HISTORY_SQL_WITH_INSTANCE = "SELECT instance, %s FROM %s WHERE instance = '%s' and time >= now() - %s order by time desc";

    private static final String QUERY_HISTORY_INTERVAL_WITH_INSTANCE_SQL =
            "SELECT FIRST(%s), MEAN(%s), MAX(%s), MIN(%s) FROM %s WHERE instance = '%s' and time >= now() - %s GROUP BY time(4h)";

    private static final String CREATE_RETENTION_POLICY = "CREATE RETENTION POLICY \"%s_retention\" ON \"%s\" DURATION %s REPLICATION %d DEFAULT";

    private static final String QUERY_INSTANCE_SQL = "show tag values from %s with key = \"instance\"";


    private static final String QUERY_HISTORY_WINSQL_WITH_INSTANCE = "SELECT instance, in_octets, out_octets FROM %s WHERE time >= now() - %s order by time desc";
    private static final String QUERY_HISTORY_BYLINUX_SQL = "SELECT instance, receive_bytes ,transmit_bytes FROM %s WHERE time >= now() - %s and instance = '%s' order by time desc";

    private static final String QUERY_HISTORY_BYDESCR_SQL = "SELECT instance, in_octets, out_octets FROM %s WHERE time >= now() - %s and instance = '%s' order by time desc";


    private InfluxDB influxDb;

    public InfluxdbDataStorage(InfluxdbProperties influxdbProperties) {
        this.initInfluxDb(influxdbProperties);
    }

    public void initInfluxDb(InfluxdbProperties influxdbProperties) {

        var client = new OkHttpClient.Builder()
                .readTimeout(NetworkConstants.HttpClientConstants.READ_TIME_OUT, TimeUnit.SECONDS)
                .writeTimeout(NetworkConstants.HttpClientConstants.WRITE_TIME_OUT, TimeUnit.SECONDS)
                .connectTimeout(NetworkConstants.HttpClientConstants.CONNECT_TIME_OUT, TimeUnit.SECONDS)
                .connectionPool(new ConnectionPool(
                        NetworkConstants.HttpClientConstants.MAX_IDLE_CONNECTIONS,
                        NetworkConstants.HttpClientConstants.KEEP_ALIVE_TIMEOUT,
                        TimeUnit.SECONDS)
                ).sslSocketFactory(defaultSslSocketFactory(), defaultTrustManager())
                .hostnameVerifier(noopHostnameVerifier())
                .retryOnConnectionFailure(true);

        this.influxDb = InfluxDBFactory.connect(influxdbProperties.getServerUrl(), influxdbProperties.getUsername(), influxdbProperties.getPassword(), client);
        // Close it if your application is terminating, or you are not using it anymore.
        Runtime.getRuntime().addShutdownHook(new Thread(influxDb::close));

        this.serverAvailable = this.createDatabase(influxdbProperties);
    }

    private boolean createDatabase(InfluxdbProperties influxdbProperties) {
        QueryResult queryResult = this.influxDb.query(new Query(SHOW_DATABASE));

        if (queryResult.hasError()) {
            log.error("show databases in influxdb error, msg: {}", queryResult.getError());
            return false;
        }

        for (QueryResult.Result result : queryResult.getResults()) {
            for (QueryResult.Series series : result.getSeries()) {
                for (List<Object> values : series.getValues()) {
                    if (values.contains(DATABASE)) {
                        // database exists
                        return true;
                    }
                }
            }
        }

        // create the database
        String createDatabaseSql = String.format(CREATE_DATABASE, DATABASE);
        QueryResult createDatabaseResult = this.influxDb.query(new Query(createDatabaseSql));
        if (createDatabaseResult.hasError()) {
            log.error("create database {} in influxdb error, msg: {}", DATABASE, createDatabaseResult.getError());
            return false;
        }
        // set the expiration time
        String createRetentionPolicySql = String.format(CREATE_RETENTION_POLICY, DATABASE, DATABASE,
                influxdbProperties.getExpireTime(), influxdbProperties.getReplication());
        QueryResult createRetentionPolicySqlResult = this.influxDb.query(new Query(createRetentionPolicySql));
        if (createRetentionPolicySqlResult.hasError()) {
            log.error("create retention policy in influxdb error, msg: {}", createDatabaseResult.getError());
            return false;
        }

        return true;
    }

    @Override
    public void saveData(ClusterMsg.Message message) {
        BatchPoints.Builder builder = BatchPoints.database(DATABASE);
        this.influxDb.write(builder.build());
    }


    private String generateTable(String app, String metrics, Long monitorId) {
        return app + "_" + metrics + "_" + monitorId;
    }

    private long parseTimeToMillis(Object time) {
        if (time == null) {
            return 0;
        }
        Double doubleTime = (Double) time;
        return doubleTime.longValue();
    }

    private String parseDoubleValue(String value) {
        return (new BigDecimal(value)).setScale(4, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
    }

    private static X509TrustManager defaultTrustManager() {
        return new X509TrustManager() {
            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[0];
            }

            @Override
            public void checkClientTrusted(X509Certificate[] certs, String authType) {
            }

            @Override
            public void checkServerTrusted(X509Certificate[] certs, String authType) {
            }
        };
    }

    private static SSLSocketFactory defaultSslSocketFactory() {
        try {
            SSLContext sslContext = SSLContexts.createDefault();
            sslContext.init(null, new TrustManager[]{
                    defaultTrustManager()
            }, new SecureRandom());
            return sslContext.getSocketFactory();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static HostnameVerifier noopHostnameVerifier() {
        return (s, sslSession) -> true;
    }

    @Override
    public void destroy() throws Exception {
        if (this.influxDb != null) {
            this.influxDb.close();
        }
    }

    @Override
    public Map<String, List<Value>> getHistoryMetricData(String uuid, String metrics, String metric, String history, Long start, Long end, String step, String type, List<Map> platformId) {
        return Map.of();
    }

    @Override
    public Map<String, List<Value>> getHistoryMetricData(String uuid, String metrics, String metric, String history, Long start, Long end, String step, String type) {
        return Map.of();
    }

    @Override
    public Map<String, List<Value>> getHistoryIntervalMetricData(String uuid, String metrics, String metric, String history, Long start, Long end, String step, String type, List<Map> platformId) {
        return Map.of();
    }

    @Override
    public List<VictoriaMetricsDTO> getMetricData(List<String> uuids, List<String> metrics, String history, Long start, Long end, String step, String type,Integer num) {
        return new ArrayList<>();
    }

    @Override
    public List<VictoriaMetricsDTO> getMetricData(List<String> uuids, List<String> metrics, String history, Long start, Long end, String step) {
        return new ArrayList<>();
    }
}
