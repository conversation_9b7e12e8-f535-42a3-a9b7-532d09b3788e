/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.zj.framework.warehouse.store.history;

import cn.iocoder.zj.framework.common.pojo.VictoriaMetricsDTO;
import org.apache.hertzbeat.common.entity.dto.Value;

import java.util.List;
import java.util.Map;

/**
 * history data reader
 */
public interface HistoryDataReader {

    /**
     * @return data storage available
     */
    boolean isServerAvailable();


    Map<String, List<Value>> getHistoryMetricData(String uuid, String metrics, String metric, String history, Long start, Long end, String step, String type, List<Map> platformId);

    Map<String, List<Value>> getHistoryMetricData(String uuid, String metrics, String metric, String history, Long start, Long end, String step, String type);

    Map<String, List<Value>> getHistoryIntervalMetricData(String uuid, String metrics, String metric, String history, Long start, Long end, String step, String type, List<Map> platformId);

    List<VictoriaMetricsDTO> getMetricData(List<String> uuids, List<String> metrics, String history, Long start, Long end, String step, String type,Integer num);

    List<VictoriaMetricsDTO> getMetricData(List<String> uuids, List<String> metrics, String history, Long start, Long end, String step);
}
