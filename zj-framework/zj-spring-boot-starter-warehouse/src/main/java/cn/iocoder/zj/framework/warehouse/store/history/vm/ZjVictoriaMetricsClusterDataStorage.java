/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.zj.framework.warehouse.store.history.vm;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.MetricData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import cn.iocoder.zj.framework.common.pojo.VictoriaMetricsDTO;
import cn.iocoder.zj.framework.warehouse.store.history.AbstractHistoryDataStorage;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.gson.Gson;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.hertzbeat.common.constants.NetworkConstants;
import org.apache.hertzbeat.common.constants.SignConstants;
import org.apache.hertzbeat.common.entity.dto.Value;
import org.apache.hertzbeat.common.util.Base64Util;
import org.apache.hertzbeat.common.util.JsonUtil;
import org.apache.hertzbeat.common.util.TimePeriodUtil;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Primary;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.ZonedDateTime;
import java.time.temporal.TemporalAmount;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.apache.hertzbeat.common.constants.ConfigConstants.FunctionModuleConstants.STATUS;


/**
 * tdengine data storage
 */
@Primary
@Component
@ConditionalOnProperty(prefix = "warehouse.store.victoria-metrics.cluster", name = "enabled", havingValue = "true")
@Slf4j
public class ZjVictoriaMetricsClusterDataStorage extends AbstractHistoryDataStorage {

    private static final String IMPORT_PATH = "/0/prometheus/api/v1/import";
    private static final String EXPORT_PATH = "/0/prometheus/api/v1/export";
    private static final String STATUS_PATH = "/0/prometheus/api/v1/status/tsdb";
    private static final String INFLUXDB_PATH = "/0/influx/write";
    private static final String STATUS_SUCCESS = "success";
    private static final String QUERY_RANGE_PATH = "/0/prometheus/api/v1/query_range";

    private static final String LABEL_KEY_JOB = "job";
    private static final String LABEL_KEY_INSTANCE = "instance";
    private static final String LABEL_KEY_INSTANCE_NAME = "interface_name";
    private static final String SPILT = "_";
    private static final String MONITOR_METRICS_KEY = "__metrics__";
    private static final String MONITOR_METRIC_KEY = "__metric__";
    private static final String MONITOR_PLATFORM_ID = "platform_id";
    private static final String MEMORY_USED_BYTES = "MemoryUsedBytes";
    private static final String MEMORY_FREE_BYTES = "MemoryFreeBytes";
    private static final String MONITOR_TYPE = "type";


    private static final String LABEL_KEY_NAME = "__name__";


//    private final VictoriaMetricsInsertProperties vmInsertProps;
//    private final VictoriaMetricsSelectProperties vmSelectProps;

    private final VictoriaMetricsClusterProperties vmClusterProps;

    private final RestTemplate restTemplate;

    public ZjVictoriaMetricsClusterDataStorage(VictoriaMetricsClusterProperties vmClusterProps,
                                               RestTemplate restTemplate) {
        if (vmClusterProps == null) {
            log.error("init error, please config Warehouse victoriaMetrics cluster props in application.yml");
            throw new IllegalArgumentException("please config Warehouse victoriaMetrics cluster props");
        }
        this.restTemplate = restTemplate;
        this.vmClusterProps = vmClusterProps;
        serverAvailable = checkVictoriaMetricsDatasourceAvailable();
    }

    private boolean checkVictoriaMetricsDatasourceAvailable() {
        // check server status
        try {
            String result = restTemplate.getForObject(vmClusterProps.getSUrl() + STATUS_PATH, String.class);

            JsonNode jsonNode = JsonUtil.fromJson(result);
            if (jsonNode != null && STATUS_SUCCESS.equalsIgnoreCase(jsonNode.get(STATUS).asText())) {
                return true;
            }
            log.error("check victoria metrics cluster server status not success: {}.", result);
        } catch (Exception e) {
            log.error("check victoria metrics cluster server status error: {}.", e.getMessage());
        }
        return false;
    }

    @Override
    public void saveData(ClusterMsg.Message metricsData) {
        if (!isServerAvailable()) {
            serverAvailable = checkVictoriaMetricsDatasourceAvailable();
        }
        if (!isServerAvailable()) {
            return;
        }
        if (metricsData.getData().isEmpty()) {
            log.info("[warehouse victoria-metrics] flush metrics data {} {} is null, ignore.",
                    metricsData.getClientId(), metricsData.getType());
            return;
        }

        MetricData[] metricDataArray = new Gson().fromJson(metricsData.getData(), MetricData[].class);

        if (metricDataArray == null) {
            log.info("[warehouse victoria-metrics] flush metrics data {} {} is null, ignore.",
                    metricsData.getClientId(), metricsData.getType());
            return;
        }

        String job = metricsData.getType().getValueDescriptor().toString().toLowerCase();

        Map<String, String> defaultLabels = new HashMap<>(8);
        Long[] timestamp = new Long[]{metricsData.getTime()};
        // 处理数组中的所有元素
        List<ZjVictoriaMetricsDataStorage.VictoriaMetricsContent> contentList = new LinkedList<>();
        for (MetricData metricData : metricDataArray) {
            defaultLabels.put(MONITOR_METRICS_KEY, metricData.getMetricName());
            defaultLabels.put(LABEL_KEY_JOB, job);
            defaultLabels.put(MONITOR_METRIC_KEY, metricData.getMetricName());

            defaultLabels.put(LABEL_KEY_INSTANCE, String.valueOf(metricData.getResourceId()));
            defaultLabels.put(LABEL_KEY_NAME, metricData.getMetricName());
            defaultLabels.put(MONITOR_PLATFORM_ID, Convert.toStr(metricData.getPlatformId()));
            defaultLabels.put(MONITOR_TYPE, Convert.toStr(metricData.getType()));

            List<Double> values = metricData.getValues();
            ZjVictoriaMetricsDataStorage.VictoriaMetricsContent content = ZjVictoriaMetricsDataStorage.VictoriaMetricsContent.builder()
                    .metric(new HashMap<>(defaultLabels))
                    .values(values.toArray(new Double[0]))
                    .timestamps(timestamp)
                    .build();
            contentList.add(content);
        }

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            if (StringUtils.hasText(vmClusterProps.getUsername())
                    && StringUtils.hasText(vmClusterProps.getPassword())) {
                String authStr = vmClusterProps.getUsername() + ":" + vmClusterProps.getPassword();
                String encodedAuth = new String(Base64.encodeBase64(authStr.getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8);
                headers.add(HttpHeaders.AUTHORIZATION, NetworkConstants.BASIC + SignConstants.BLANK + encodedAuth);
            }
            StringBuilder stringBuilder = new StringBuilder();
            for (ZjVictoriaMetricsDataStorage.VictoriaMetricsContent content : contentList) {
                stringBuilder.append(JsonUtil.toJson(content)).append("\n");
            }
            HttpEntity<String> httpEntity = new HttpEntity<>(stringBuilder.toString(), headers);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(vmClusterProps.getIUrl() + IMPORT_PATH,
                    httpEntity, String.class);
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                log.debug("insert metrics data to victoria-metrics success.");
            } else {
                log.error("insert metrics data to victoria-metrics failed. {}", responseEntity.getBody());
            }
        } catch (Exception e) {
            log.error("flush metrics data to victoria-metrics error: {}.", e.getMessage(), e);
        }
    }


    @Override
    public void destroy() {
    }

    @Override
    public Map<String, List<Value>> getHistoryMetricData(String uuid, String metrics, String metric, String history, Long start, Long end, String step, String type, List<Map> platformId) {
        Map<String, List<Value>> instanceValuesMap = new HashMap<>(8);
        String timeSeriesSelector = "";
        if (!uuid.isEmpty()) {
            timeSeriesSelector =
                    LABEL_KEY_INSTANCE + "=\"" + uuid + "\"" + ","
                            + MONITOR_METRIC_KEY + "=\"" + metric + "\"";
        } else if (!platformId.isEmpty()) {
            StringBuilder result = new StringBuilder();
            for (Map map : platformId) {
                if (map != null && map.containsKey("platformId")) {
                    String platformValue = map.get("platformId").toString();
                    if (result.length() > 0) {
                        result.append("|");
                    }
                    result.append(platformValue);
                }
            }
            timeSeriesSelector = MONITOR_METRIC_KEY + "=\"" + metric + "\"" + ","
                    + MONITOR_PLATFORM_ID + "=~\"" + result + "\"" + "," + MONITOR_TYPE + "=\"" + "host" + "\"";
        }

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(List.of(MediaType.APPLICATION_JSON));
            if (StringUtils.hasText(vmClusterProps.getUsername()) && StringUtils.hasText(vmClusterProps.getPassword())) {
                String authStr = vmClusterProps.getUsername() + ":" + vmClusterProps.getPassword();
                String encodedAuth = new String(Base64.encodeBase64(authStr.getBytes(StandardCharsets.UTF_8)),
                        StandardCharsets.UTF_8);
                headers.add(HttpHeaders.AUTHORIZATION, NetworkConstants.BASIC
                        + SignConstants.BLANK + encodedAuth);
            }
            HttpEntity<Void> httpEntity = new HttpEntity<>(headers);
            UriComponentsBuilder builder;

            if (!uuid.isEmpty()) {
                builder = UriComponentsBuilder.fromHttpUrl(vmClusterProps.getSUrl() + QUERY_RANGE_PATH);
                builder.queryParam(URLEncoder.encode("query", StandardCharsets.UTF_8),
                        URLEncoder.encode("avg({" + timeSeriesSelector + "})", StandardCharsets.UTF_8));
            } else {
                builder = UriComponentsBuilder.fromHttpUrl(vmClusterProps.getSUrl() + QUERY_RANGE_PATH);
                String query;
                if ("avg".equals(type)) {
                    query = "avg({" + timeSeriesSelector + "})";
                } else if ("min".equals(type)) {
                    query = "min({" + timeSeriesSelector + "})";
                } else if ("max".equals(type)) {
                    query = "max({" + timeSeriesSelector + "})";
                } else {
                    // 默认不需要聚合函数
                    query = "{" + timeSeriesSelector + "}";
                }

                if (MEMORY_FREE_BYTES.equals(metric) || MEMORY_USED_BYTES.equals(metric)) {
                    // 对于内存指标，使用sum汇总
                    query = "sum({" + timeSeriesSelector + "})";
                }
                builder.queryParam(URLEncoder.encode("query", StandardCharsets.UTF_8),
                        URLEncoder.encode(query + " or vector(0) ", StandardCharsets.UTF_8));

            }
            // 根据参数选择时间范围
            if (StringUtils.hasText(history)) {
                if (vmClusterProps.isEnableTimeAlignment()) {
                    // 使用固定时间边界避免时间对齐问题
                    long currentTime = System.currentTimeMillis() / 1000;
                    long historySeconds = parseHistoryToSeconds(history);
                    long alignedEndTime = alignTimeToStep(currentTime, step);
                    long alignedStartTime = alignedEndTime - historySeconds;

                    builder.queryParam("step", step)
                            .queryParam("start", alignedStartTime)
                            .queryParam("end", alignedEndTime);
                } else {
                    // 使用原始的相对时间查询
                    builder.queryParam("step", step)
                            .queryParam("start", URLEncoder.encode("now-" + history, StandardCharsets.UTF_8))
                            .queryParam("end", "now");
                }
            } else if (start != null && end != null) {
                if (vmClusterProps.isEnableTimeAlignment()) {
                    // 使用绝对时间范围，Victoria Metrics期望Unix时间戳（秒）
                    long alignedStart = alignTimeToStep(start / 1000, step);
                    long alignedEnd = alignTimeToStep(end / 1000, step);

                    builder.queryParam("step", step)
                            .queryParam("start", alignedStart)
                            .queryParam("end", alignedEnd);
                } else {
                    builder.queryParam("step", step)
                            .queryParam("start", start / 1000)
                            .queryParam("end", end / 1000);
                }
            } else {
                log.error("Neither history nor start/end time range provided");
                return instanceValuesMap;
            }

            URI uri = builder.build(true).toUri();
            ResponseEntity<PromQlQueryContent> responseEntity = restTemplate.exchange(uri, HttpMethod.GET,
                    httpEntity, PromQlQueryContent.class);
            if (responseEntity.getBody() != null && responseEntity.getBody().getData() != null
                    && responseEntity.getBody().getData().getResult() != null) {
                List<PromQlQueryContent.ContentData.Content> contents = responseEntity.getBody().getData()
                        .getResult();
                for (PromQlQueryContent.ContentData.Content content : contents) {
                    Map<String, String> labels = content.getMetric();
                    labels.remove(LABEL_KEY_NAME);
                    labels.remove(LABEL_KEY_JOB);
                    labels.remove(LABEL_KEY_INSTANCE);
                    labels.remove(MONITOR_METRICS_KEY);
                    labels.remove(MONITOR_METRIC_KEY);
                    String labelStr = JsonUtil.toJson(labels);
                    if (content.getValues() != null && !content.getValues().isEmpty()) {
                        List<Value> valueList = instanceValuesMap.computeIfAbsent(metric,
                                k -> new LinkedList<>());
                        for (Object[] valueArr : content.getValues()) {
                            long timestamp = new BigDecimal(String.valueOf(valueArr[0])).longValue();
                            String value = new BigDecimal(String.valueOf(valueArr[1])).setScale(4,
                                    RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
                            // read timestamp here is s unit
                            valueList.add(new Value(value, timestamp * 1000));
                        }
                    }
                }
            } else {
                log.error("query metrics data from victoria-metrics failed. {}", responseEntity);
            }
        } catch (Exception e) {
            log.error("query metrics data from victoria-metrics error. {}.", e.getMessage(), e);
        }
        return instanceValuesMap;
    }

    @Override
    public Map<String, List<Value>> getHistoryMetricData(String uuid, String metrics, String metric, String history, Long start, Long end, String step, String type) {
        Map<String, List<Value>> instanceValuesMap = new HashMap<>(8);
        String timeSeriesSelector = "";
        if (!uuid.isEmpty()) {
            timeSeriesSelector =
                    LABEL_KEY_INSTANCE + "=\"" + uuid + "\"" + ","
                            + MONITOR_METRIC_KEY + "=\"" + metric + "\"";
        } else {
            timeSeriesSelector = MONITOR_METRIC_KEY + "=\"" + metric + "\"";
        }

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(List.of(MediaType.APPLICATION_JSON));
            if (StringUtils.hasText(vmClusterProps.getUsername()) && StringUtils.hasText(vmClusterProps.getPassword())) {
                String authStr = vmClusterProps.getUsername() + ":" + vmClusterProps.getPassword();
                String encodedAuth = new String(Base64.encodeBase64(authStr.getBytes(StandardCharsets.UTF_8)),
                        StandardCharsets.UTF_8);
                headers.add(HttpHeaders.AUTHORIZATION, NetworkConstants.BASIC
                        + SignConstants.BLANK + encodedAuth);
            }
            HttpEntity<Void> httpEntity = new HttpEntity<>(headers);
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(vmClusterProps.getSUrl() + QUERY_RANGE_PATH);
            String query;
            if ("avg".equals(type)) {
                query = "avg_over_time({" + timeSeriesSelector + "})";
            } else if ("min".equals(type)) {
                query = "min_over_time({" + timeSeriesSelector + "})";
            } else if ("max".equals(type)) {
                query = "max_over_time({" + timeSeriesSelector + "})";
            } else {
                // 默认不需要聚合函数
                query = "{" + timeSeriesSelector + "}";
            }

            builder.queryParam(URLEncoder.encode("query", StandardCharsets.UTF_8),
                    URLEncoder.encode(query, StandardCharsets.UTF_8));
            // 根据参数选择时间范围
            if (StringUtils.hasText(history)) {
                if (vmClusterProps.isEnableTimeAlignment()) {
                    // 使用固定时间边界避免时间对齐问题
                    long currentTime = System.currentTimeMillis() / 1000;
                    long historySeconds = parseHistoryToSeconds(history);
                    long alignedEndTime = alignTimeToStep(currentTime, step);
                    long alignedStartTime = alignedEndTime - historySeconds;

                    builder.queryParam("step", step)
                            .queryParam("start", alignedStartTime)
                            .queryParam("end", alignedEndTime);
                } else {
                    // 使用原始的相对时间查询
                    builder.queryParam("step", step)
                            .queryParam("start", URLEncoder.encode("now-" + history, StandardCharsets.UTF_8))
                            .queryParam("end", "now");
                }
            } else if (start != null && end != null) {
                if (vmClusterProps.isEnableTimeAlignment()) {
                    // 使用绝对时间范围，Victoria Metrics期望Unix时间戳（秒）
                    long alignedStart = alignTimeToStep(start / 1000, step);
                    long alignedEnd = alignTimeToStep(end / 1000, step);

                    builder.queryParam("step", step)
                            .queryParam("start", alignedStart)
                            .queryParam("end", alignedEnd);
                } else {
                    builder.queryParam("step", step)
                            .queryParam("start", start / 1000)
                            .queryParam("end", end / 1000);
                }
            } else {
                log.error("Neither history nor start/end time range provided");
                return instanceValuesMap;
            }

            URI uri = builder.build(true).toUri();
            ResponseEntity<PromQlQueryContent> responseEntity = restTemplate.exchange(uri, HttpMethod.GET,
                    httpEntity, PromQlQueryContent.class);
            if (responseEntity.getBody() != null && responseEntity.getBody().getData() != null
                    && responseEntity.getBody().getData().getResult() != null) {
                List<PromQlQueryContent.ContentData.Content> contents = responseEntity.getBody().getData()
                        .getResult();
                for (PromQlQueryContent.ContentData.Content content : contents) {
                    Map<String, String> labels = content.getMetric();
                    labels.remove(LABEL_KEY_NAME);
                    labels.remove(LABEL_KEY_JOB);
                    labels.remove(LABEL_KEY_INSTANCE);
                    labels.remove(MONITOR_METRICS_KEY);
                    labels.remove(MONITOR_METRIC_KEY);
                    String labelStr = JsonUtil.toJson(labels);
                    if (content.getValues() != null && !content.getValues().isEmpty()) {
                        List<Value> valueList = instanceValuesMap.computeIfAbsent(metric,
                                k -> new LinkedList<>());
                        for (Object[] valueArr : content.getValues()) {
                            long timestamp = new BigDecimal(String.valueOf(valueArr[0])).longValue();
                            String value = new BigDecimal(String.valueOf(valueArr[1])).setScale(4,
                                    RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
                            // read timestamp here is s unit
                            valueList.add(new Value(value, timestamp * 1000));
                        }
                    }
                }
            } else {
                log.error("query metrics data from victoria-metrics failed. {}", responseEntity);
            }
        } catch (Exception e) {
            log.error("query metrics data from victoria-metrics error. {}.", e.getMessage(), e);
        }
        return instanceValuesMap;
    }

    @Override
    public Map<String, List<Value>> getHistoryIntervalMetricData(String uuid, String metrics, String metric, String history, Long start, Long end, String step, String type, List<Map> platformId) {
        if (!serverAvailable) {
            log.error("""
                    
                    \t---------------VictoriaMetrics Init Failed---------------
                    \t--------------Please Config VictoriaMetrics--------------
                    \t----------Can Not Use Metric History Now----------
                    """);
            return Collections.emptyMap();
        }

        long endTime = ZonedDateTime.now().toEpochSecond();
        long startTime;
        try {
            if (NumberUtils.isParsable(history)) {
                startTime = NumberUtils.toLong(history);
                startTime = (ZonedDateTime.now().toEpochSecond() - startTime);
            } else {
                TemporalAmount temporalAmount = TimePeriodUtil.parseTokenTime(history);
                ZonedDateTime dateTime = ZonedDateTime.now().minus(temporalAmount);
                startTime = dateTime.toEpochSecond();
            }
        } catch (Exception e) {
            log.error("history time error: {}. use default: 6h", e.getMessage());
            ZonedDateTime dateTime = ZonedDateTime.now().minus(Duration.ofHours(6));
            startTime = dateTime.toEpochSecond();
        }

        Map<String, List<Value>> instanceValuesMap = new HashMap<>(8);
        Set<String> instances = new HashSet<>(8);

        String timeSeriesSelector;
        if (!uuid.isEmpty()) {
            timeSeriesSelector =
                    LABEL_KEY_INSTANCE + "=\"" + uuid + "\"" + ","
                            + MONITOR_METRIC_KEY + "=\"" + metric + "\"";
        } else {
            timeSeriesSelector = MONITOR_METRIC_KEY + "=\"" + metric + "\"";
        }
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(List.of(MediaType.APPLICATION_JSON));
            if (StringUtils.hasText(vmClusterProps.getUsername()) && StringUtils.hasText(vmClusterProps.getPassword())) {
                String authStr = vmClusterProps.getUsername() + ":" + vmClusterProps.getPassword();
                String encodedAuth = Base64Util.encode(authStr);
                headers.add(HttpHeaders.AUTHORIZATION, NetworkConstants.BASIC
                        + SignConstants.BLANK + encodedAuth);
            }
            HttpEntity<Void> httpEntity = new HttpEntity<>(headers);
            URI uri = UriComponentsBuilder.fromHttpUrl(vmClusterProps.getSUrl() + QUERY_RANGE_PATH)
                    .queryParam(URLEncoder.encode("query", StandardCharsets.UTF_8),
                            URLEncoder.encode("{" + timeSeriesSelector + "}", StandardCharsets.UTF_8))
                    .queryParam("step", "4h").queryParam("start", startTime).queryParam("end", endTime).build(true)
                    .toUri();
            ResponseEntity<PromQlQueryContent> responseEntity = restTemplate.exchange(uri, HttpMethod.GET,
                    httpEntity, PromQlQueryContent.class);
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                log.debug("query metrics data from victoria-metrics success. {}", uri);
                if (responseEntity.getBody() != null && responseEntity.getBody().getData() != null
                        && responseEntity.getBody().getData().getResult() != null) {
                    List<PromQlQueryContent.ContentData.Content> contents = responseEntity.getBody().getData()
                            .getResult();
                    for (PromQlQueryContent.ContentData.Content content : contents) {
                        Map<String, String> labels = content.getMetric();
                        labels.remove(LABEL_KEY_NAME);
                        labels.remove(LABEL_KEY_JOB);
                        labels.remove(LABEL_KEY_INSTANCE);
                        labels.remove(MONITOR_METRICS_KEY);
                        labels.remove(MONITOR_METRIC_KEY);
                        String labelStr = JsonUtil.toJson(labels);
                        if (content.getValues() != null && !content.getValues().isEmpty()) {
                            List<Value> valueList = instanceValuesMap.computeIfAbsent(labelStr,
                                    k -> new LinkedList<>());
                            for (Object[] valueArr : content.getValues()) {
                                long timestamp = Long.parseLong(String.valueOf(valueArr[0]));
                                String value = new BigDecimal(String.valueOf(valueArr[1])).setScale(4,
                                        RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
                                // read timestamp here is s unit
                                valueList.add(new Value(value, timestamp * 1000));
                            }
                        }
                    }
                }
            } else {
                log.error("query metrics data from victoria-metrics failed. {}", responseEntity);
            }
            // max
            uri = UriComponentsBuilder.fromHttpUrl(vmClusterProps.getSUrl() + QUERY_RANGE_PATH)
                    .queryParam(URLEncoder.encode("query", StandardCharsets.UTF_8),
                            URLEncoder.encode("max_over_time({" + timeSeriesSelector + "})", StandardCharsets.UTF_8))
                    .queryParam("step", "4h").queryParam("start", startTime).queryParam("end", endTime).build(true)
                    .toUri();
            responseEntity = restTemplate.exchange(uri, HttpMethod.GET, httpEntity, PromQlQueryContent.class);
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                if (responseEntity.getBody() != null && responseEntity.getBody().getData() != null
                        && responseEntity.getBody().getData().getResult() != null) {
                    List<PromQlQueryContent.ContentData.Content> contents = responseEntity.getBody().getData()
                            .getResult();
                    for (PromQlQueryContent.ContentData.Content content : contents) {
                        Map<String, String> labels = content.getMetric();
                        labels.remove(LABEL_KEY_NAME);
                        labels.remove(LABEL_KEY_JOB);
                        labels.remove(LABEL_KEY_INSTANCE);
                        labels.remove(MONITOR_METRICS_KEY);
                        labels.remove(MONITOR_METRIC_KEY);
                        String labelStr = JsonUtil.toJson(labels);
                        if (content.getValues() != null && !content.getValues().isEmpty()) {
                            List<Value> valueList = instanceValuesMap.computeIfAbsent(labelStr,
                                    k -> new LinkedList<>());
                            if (valueList.size() == content.getValues().size()) {
                                for (int timestampIndex = 0; timestampIndex < valueList.size(); timestampIndex++) {
                                    Value value = valueList.get(timestampIndex);
                                    Object[] valueArr = content.getValues().get(timestampIndex);
                                    String maxValue = new BigDecimal(String.valueOf(valueArr[1])).setScale(4,
                                            RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
                                    value.setMax(maxValue);
                                }
                            }
                        }
                    }
                }
            }
            // min
            uri = UriComponentsBuilder.fromHttpUrl(vmClusterProps.getSUrl() + QUERY_RANGE_PATH)
                    .queryParam(URLEncoder.encode("query", StandardCharsets.UTF_8),
                            URLEncoder.encode("min_over_time({" + timeSeriesSelector + "})", StandardCharsets.UTF_8))
                    .queryParam("step", "4h").queryParam("start", startTime).queryParam("end", endTime).build(true)
                    .toUri();
            responseEntity = restTemplate.exchange(uri, HttpMethod.GET, httpEntity, PromQlQueryContent.class);
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                if (responseEntity.getBody() != null && responseEntity.getBody().getData() != null
                        && responseEntity.getBody().getData().getResult() != null) {
                    List<PromQlQueryContent.ContentData.Content> contents = responseEntity.getBody().getData()
                            .getResult();
                    for (PromQlQueryContent.ContentData.Content content : contents) {
                        Map<String, String> labels = content.getMetric();
                        labels.remove(LABEL_KEY_NAME);
                        labels.remove(LABEL_KEY_JOB);
                        labels.remove(LABEL_KEY_INSTANCE);
                        labels.remove(MONITOR_METRICS_KEY);
                        labels.remove(MONITOR_METRIC_KEY);
                        String labelStr = JsonUtil.toJson(labels);
                        if (content.getValues() != null && !content.getValues().isEmpty()) {
                            List<Value> valueList = instanceValuesMap.computeIfAbsent(labelStr,
                                    k -> new LinkedList<>());
                            if (valueList.size() == content.getValues().size()) {
                                for (int timestampIndex = 0; timestampIndex < valueList.size(); timestampIndex++) {
                                    Value value = valueList.get(timestampIndex);
                                    Object[] valueArr = content.getValues().get(timestampIndex);
                                    String minValue = new BigDecimal(String.valueOf(valueArr[1])).setScale(4,
                                            RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
                                    value.setMin(minValue);
                                }
                            }
                        }
                    }
                }
            }
            // avg
            uri = UriComponentsBuilder.fromHttpUrl(vmClusterProps.getSUrl() + QUERY_RANGE_PATH)
                    .queryParam(URLEncoder.encode("query", StandardCharsets.UTF_8),
                            URLEncoder.encode("avg_over_time({" + timeSeriesSelector + "})", StandardCharsets.UTF_8))
                    .queryParam("step", "4h").queryParam("start", startTime).queryParam("end", endTime).build(true)
                    .toUri();
            responseEntity = restTemplate.exchange(uri, HttpMethod.GET, httpEntity, PromQlQueryContent.class);
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                if (responseEntity.getBody() != null && responseEntity.getBody().getData() != null
                        && responseEntity.getBody().getData().getResult() != null) {
                    List<PromQlQueryContent.ContentData.Content> contents = responseEntity.getBody().getData()
                            .getResult();
                    for (PromQlQueryContent.ContentData.Content content : contents) {
                        Map<String, String> labels = content.getMetric();
                        labels.remove(LABEL_KEY_NAME);
                        labels.remove(LABEL_KEY_JOB);
                        labels.remove(LABEL_KEY_INSTANCE);
                        labels.remove(MONITOR_METRICS_KEY);
                        labels.remove(MONITOR_METRIC_KEY);
                        String labelStr = JsonUtil.toJson(labels);
                        if (content.getValues() != null && !content.getValues().isEmpty()) {
                            List<Value> valueList = instanceValuesMap.computeIfAbsent(labelStr,
                                    k -> new LinkedList<>());
                            if (valueList.size() == content.getValues().size()) {
                                for (int timestampIndex = 0; timestampIndex < valueList.size(); timestampIndex++) {
                                    Value value = valueList.get(timestampIndex);
                                    Object[] valueArr = content.getValues().get(timestampIndex);
                                    String avgValue = new BigDecimal(String.valueOf(valueArr[1])).setScale(4,
                                            RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
                                    value.setMean(avgValue);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("query metrics data from victoria-metrics error. {}.", e.getMessage(), e);
        }


        return Map.of();
    }

    @Override
    public List<VictoriaMetricsDTO> getMetricData(List<String> uuidList, List<String> metrics, String history, Long start, Long end, String step, String type, Integer num) {
        List<VictoriaMetricsDTO> list = new ArrayList<>();
        List<VictoriaMetricsDTO> arrayList = new ArrayList<>();

        // 构建时间序列选择器
        String joinedMetrics = String.join("|", metrics);
        String timeSeriesSelector = MONITOR_METRIC_KEY + "=~\"" + joinedMetrics + "\"";

        if (CollUtil.isNotEmpty(uuidList)) {
            timeSeriesSelector = LABEL_KEY_INSTANCE + "=~\"" + String.join("|", uuidList) + "\"," + timeSeriesSelector;
        }

        try {
            // 设置 HTTP 头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

            // 如果提供了凭据，则添加基本认证
            if (StringUtils.hasText(vmClusterProps.getUsername()) && StringUtils.hasText(vmClusterProps.getPassword())) {
                String authStr = vmClusterProps.getUsername() + ":" + vmClusterProps.getPassword();
                String encodedAuth = new String(Base64.encodeBase64(authStr.getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8);
                headers.add(HttpHeaders.AUTHORIZATION, NetworkConstants.BASIC + SignConstants.BLANK + encodedAuth);
            }

            // 构建请求的 URI
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(vmClusterProps.getSUrl() + QUERY_RANGE_PATH);
            String query = "{" + timeSeriesSelector + "}";
            builder.queryParam("query", URLEncoder.encode(query, StandardCharsets.UTF_8));

            // 根据历史或显式的开始/结束时间添加时间参数
            if (StringUtils.hasText(history)) {
                if (vmClusterProps.isEnableTimeAlignment()) {
                    // 使用固定时间边界避免时间对齐问题
                    long currentTime = System.currentTimeMillis() / 1000;
                    long historySeconds = parseHistoryToSeconds(history);
                    long alignedEndTime = alignTimeToStep(currentTime, step);
                    long alignedStartTime = alignedEndTime - historySeconds;

                    builder.queryParam("step", step)
                            .queryParam("start", alignedStartTime)
                            .queryParam("end", alignedEndTime);
                } else {
                    builder.queryParam("step", step)
                            .queryParam("start", "now-" + history)
                            .queryParam("end", "now");
                }
            } else if (start != null && end != null) {
                if (vmClusterProps.isEnableTimeAlignment()) {
                    long alignedStart = alignTimeToStep(start / 1000, step);
                    long alignedEnd = alignTimeToStep(end / 1000, step);

                    builder.queryParam("step", step)
                            .queryParam("start", alignedStart)
                            .queryParam("end", alignedEnd);
                } else {
                    builder.queryParam("step", step)
                            .queryParam("start", start / 1000)
                            .queryParam("end", end / 1000);
                }
            }

            // 执行 HTTP 请求并获取响应
            URI uri = builder.build(true).toUri();
            ResponseEntity<PromQlQueryContent> responseEntity = restTemplate.exchange(
                    uri, HttpMethod.GET, new HttpEntity<>(headers), PromQlQueryContent.class);

            // 处理响应数据
            if (responseEntity.getBody() != null &&
                    responseEntity.getBody().getData() != null &&
                    responseEntity.getBody().getData().getResult() != null) {

                List<PromQlQueryContent.ContentData.Content> contents = responseEntity.getBody().getData().getResult();
                if (CollUtil.isNotEmpty(contents)) {
                    Set<String> excludeKeys = Set.of("__name__", "__metric__", "__metrics__", "instance", "job");
                    boolean hasType = StrUtil.isNotEmpty(type);

                    for (PromQlQueryContent.ContentData.Content content : contents) {
                        if (content.getValues() == null || content.getValues().isEmpty()) continue;

                        Map<String, String> labels = content.getMetric();
                        String metric = labels.get("__metric__");
                        String instance = labels.get("instance");
                        String pidmetrics = labels.get("__metrics__");

                        if (hasType) {
                            VictoriaMetricsDTO dto = new VictoriaMetricsDTO();
                            dto.setMetric(metric);
                            dto.setAssetId(instance);
                            dto.setPidMetric(pidmetrics);
                            dto.setValue(calculateValue(content.getValues(), type));

                            if (metric != null && labels.size() > 5) {
                                labels.entrySet().stream()
                                        .filter(entry -> !excludeKeys.contains(entry.getKey()))
                                        .findFirst()
                                        .ifPresent(entry -> dto.setInterfaceName(entry.getValue()));
                            }
                            list.add(dto);
                        } else {
                            for (Object[] value : content.getValues()) {
                                VictoriaMetricsDTO dto = new VictoriaMetricsDTO();
                                dto.setMetric(metric);
                                dto.setAssetId(instance);
                                dto.setPidMetric(pidmetrics);
                                dto.setValue(new BigDecimal(String.valueOf(value[1])).setScale(4, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
                                dto.setDateTime(new BigDecimal(String.valueOf(value[0])).setScale(4, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
                                list.add(dto);
                            }
                        }
                    }
                }

            }
        } catch (Exception e) {
            log.error("查询 VictoriaMetrics 时出错: {}", e.getMessage(), e);
        }

        // 分组和处理结果
        Map<String, List<VictoriaMetricsDTO>> groupMap = list.stream()
                .collect(Collectors.groupingBy(VictoriaMetricsDTO::getMetric));
        boolean hasType = StrUtil.isNotEmpty(type);

        groupMap.forEach((key, value) -> {
            boolean hasInterfaceName = value.stream()
                    .anyMatch(dto -> dto.getInterfaceName() != null && !dto.getInterfaceName().isBlank());

            if (hasInterfaceName) {
                arrayList.addAll(sortAndLimit(value, num, VictoriaMetricsDTO::getValue, true));
            } else if (hasType) {
                value.stream()
                        .collect(Collectors.groupingBy(VictoriaMetricsDTO::getAssetId))
                        .forEach((item, values) -> arrayList.add(getAggregateDto(values, type)));
            } else {
                arrayList.addAll(value);
            }
        });


        return arrayList;
    }

    @Override
    public List<VictoriaMetricsDTO> getMetricData(List<String> uuidList, List<String> metrics, String history, Long start, Long end, String step) {
        List<VictoriaMetricsDTO> list = new ArrayList<>();

        // 构建时间序列选择器
        String joinedMetrics = String.join("|", metrics);
        String timeSeriesSelector = MONITOR_METRIC_KEY + "=~\"" + joinedMetrics + "\"";

        if (CollUtil.isNotEmpty(uuidList)) {
            timeSeriesSelector = LABEL_KEY_INSTANCE + "=~\"" + String.join("|", uuidList) + "\"," + timeSeriesSelector;
        }

        try {
            // 设置 HTTP 头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

            // 如果提供了凭据，则添加基本认证
            if (StringUtils.hasText(vmClusterProps.getUsername()) && StringUtils.hasText(vmClusterProps.getPassword())) {
                String authStr = vmClusterProps.getUsername() + ":" + vmClusterProps.getPassword();
                String encodedAuth = new String(Base64.encodeBase64(authStr.getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8);
                headers.add(HttpHeaders.AUTHORIZATION, NetworkConstants.BASIC + SignConstants.BLANK + encodedAuth);
            }

            // 构建请求的 URI
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(vmClusterProps.getSUrl() + QUERY_RANGE_PATH);
            String query = "max_over_time({" + timeSeriesSelector + "})";
            builder.queryParam("query", URLEncoder.encode(query, StandardCharsets.UTF_8));

            // 根据历史或显式的开始/结束时间添加时间参数
            if (StringUtils.hasText(history)) {
                if (vmClusterProps.isEnableTimeAlignment()) {
                    // 使用固定时间边界避免时间对齐问题
                    long currentTime = System.currentTimeMillis() / 1000;
                    long historySeconds = parseHistoryToSeconds(history);
                    long alignedEndTime = alignTimeToStep(currentTime, step);
                    long alignedStartTime = alignedEndTime - historySeconds;

                    builder.queryParam("step", step)
                            .queryParam("start", alignedStartTime)
                            .queryParam("end", alignedEndTime);
                } else {
                    builder.queryParam("step", step)
                            .queryParam("start", "now-" + history)
                            .queryParam("end", "now");
                }
            } else if (start != null && end != null) {
                if (vmClusterProps.isEnableTimeAlignment()) {
                    long alignedStart = alignTimeToStep(start / 1000, step);
                    long alignedEnd = alignTimeToStep(end / 1000, step);

                    builder.queryParam("step", step)
                            .queryParam("start", alignedStart)
                            .queryParam("end", alignedEnd);
                } else {
                    builder.queryParam("step", step)
                            .queryParam("start", start / 1000)
                            .queryParam("end", end / 1000);
                }
            }

            // 执行 HTTP 请求并获取响应
            URI uri = builder.build(true).toUri();
            ResponseEntity<PromQlQueryContent> responseEntity = restTemplate.exchange(
                    uri, HttpMethod.GET, new HttpEntity<>(headers), PromQlQueryContent.class);

            // 处理响应数据
            if (responseEntity.getBody() != null &&
                    responseEntity.getBody().getData() != null &&
                    responseEntity.getBody().getData().getResult() != null) {

                List<PromQlQueryContent.ContentData.Content> contents = responseEntity.getBody().getData().getResult();
                if (CollUtil.isNotEmpty(contents)) {
                    for (PromQlQueryContent.ContentData.Content content : contents) {
                        if (content.getValues() == null || content.getValues().isEmpty()) continue;

                        Map<String, String> labels = content.getMetric();
                        String metric = labels.get("__metric__");
                        String instance = labels.get("instance");
                        String pidmetrics = labels.get("__metrics__");

                        for (Object[] value : content.getValues()) {
                            VictoriaMetricsDTO dto = new VictoriaMetricsDTO();
                            dto.setMetric(metric);
                            dto.setAssetId(instance);
                            dto.setPidMetric(pidmetrics);
                            dto.setValue(new BigDecimal(String.valueOf(value[1])).setScale(4, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
                            dto.setDateTime(new BigDecimal(String.valueOf(value[0])).setScale(4, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
                            list.add(dto);
                        }
                    }
                }

            }
        } catch (Exception e) {
            log.error("查询 VictoriaMetrics 时出错: {}", e.getMessage(), e);
        }
        return list;
    }

    private String calculateValue(List<Object[]> values, String type) {
        return switch (type) {
            case "avg" -> String.format("%.2f",
                    values.stream()
                            .map(row -> row[1])
                            .filter(Objects::nonNull)
                            .mapToDouble(val -> Double.parseDouble(val.toString()))
                            .average()
                            .orElse(0)
            );
            case "min" -> String.format("%.2f",
                    values.stream()
                            .map(row -> row[1])
                            .filter(Objects::nonNull)
                            .mapToDouble(val -> Double.parseDouble(val.toString()))
                            .min()
                            .orElse(0)
            );
            case "max" -> String.format("%.2f",
                    values.stream()
                            .map(row -> row[1])
                            .filter(Objects::nonNull)
                            .mapToDouble(val -> Double.parseDouble(val.toString()))
                            .max()
                            .orElse(0)
            );
            default -> "0.00";
        };
    }

    public static VictoriaMetricsDTO getAggregateDto(List<VictoriaMetricsDTO> dtos, String type) {
        if (dtos == null || dtos.isEmpty()) return null;

        switch (type) {
            case "min":
                return dtos.stream()
                        .filter(dto -> StrUtil.isNotEmpty(dto.getValue()))
                        .min(Comparator.comparingDouble(dto -> Double.parseDouble(dto.getValue())))
                        .orElse(null);
            case "max":
                return dtos.stream()
                        .filter(dto -> StrUtil.isNotEmpty(dto.getValue()))
                        .max(Comparator.comparingDouble(dto -> Double.parseDouble(dto.getValue())))
                        .orElse(null); // 如果没有找到，返回 null
            case "avg": {
                double avg = dtos.stream()
                        .filter(dto -> StrUtil.isNotEmpty(dto.getValue()))
                        .mapToDouble(dto -> Double.parseDouble(dto.getValue()))
                        .average()
                        .orElse(0.0);

                String formatted = String.format("%.2f", avg);

                // 创建一个新的 VictoriaMetricsDTO 对象来存储平均值
                VictoriaMetricsDTO dto = new VictoriaMetricsDTO();
                dto.setMetric(dtos.get(0).getMetric());
                dto.setAssetId(dtos.get(0).getAssetId());
                dto.setValue(formatted);
                return dto;
            }
            default:
                return null;
        }
    }

    /**
     * victoria metrics content
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static final class VictoriaMetricsContent {

        /**
         * metric contains metric name plus labels for a particular time series
         */
        private Map<String, String> metric;

        /**
         * values contains raw sample values for the given time series
         */
        private Double[] values;

        /**
         * timestamps contains raw sample UNIX timestamps in milliseconds for the given time series
         * every timestamp is associated with the value at the corresponding position
         */
        private Long[] timestamps;
    }

    public static <T> List<T> sortAndLimit(List<T> items, int num, Function<T, ? extends Comparable<?>> keyExtractor, boolean isDesc) {
        if (items == null || items.isEmpty()) {
            return Collections.emptyList();
        }
        Function<T, Integer> keyConverter = obj -> {
            Comparable<?> value = keyExtractor.apply(obj);
            return convertToInteger(value);
        };
        Comparator<Integer> integerComparator = Comparator.nullsLast(
                isDesc ? Comparator.<Integer>reverseOrder() : Comparator.<Integer>naturalOrder()
        );
        return items.stream()
                .sorted(Comparator.comparing(keyConverter, integerComparator))
                .limit(num)
                .collect(Collectors.toList());
    }

    private static Integer convertToInteger(Object value) {
        if (value == null || "".equals(value)) {
            return 0;
        }
        try {
            if (value instanceof Number) {
                return ((Number) value).intValue();
            } else if (value instanceof String) {
                String strValue = (String) value;
                if (strValue.contains(".")) {
                    return new BigDecimal(strValue).setScale(0, BigDecimal.ROUND_HALF_UP).intValue();
                } else {
                    return Integer.parseInt(strValue);
                }
            }
        } catch (NumberFormatException e) {
            System.err.println("转换为Integer失败: " + e.getMessage());
        }
        return 0;
    }

    /**
     * 解析历史时间字符串为秒数
     *
     * @param history 历史时间字符串，如 "1h", "30m", "6h"
     * @return 秒数
     */
    private long parseHistoryToSeconds(String history) {
        try {
            if (NumberUtils.isParsable(history)) {
                return NumberUtils.toLong(history);
            } else {
                TemporalAmount temporalAmount = TimePeriodUtil.parseTokenTime(history);
                return convertTemporalAmountToSeconds(temporalAmount);
            }
        } catch (Exception e) {
            log.warn("Failed to parse history time: {}, using default 6h", history);
            return Duration.ofHours(6).getSeconds();
        }
    }

    /**
     * 将时间戳对齐到step边界
     *
     * @param timestamp 时间戳（秒）
     * @param step      step字符串，如 "3m", "5m"
     * @return 对齐后的时间戳
     */
    private long alignTimeToStep(long timestamp, String step) {
        try {
            long stepSeconds = parseStepToSeconds(step);
            // 向下对齐到step边界
            return (timestamp / stepSeconds) * stepSeconds;
        } catch (Exception e) {
            log.warn("Failed to align time to step: {}, returning original timestamp", step);
            return timestamp;
        }
    }

    /**
     * 解析step字符串为秒数
     *
     * @param step step字符串，如 "3m", "5m", "1h"
     * @return 秒数
     */
    private long parseStepToSeconds(String step) {
        if (step == null || step.isEmpty()) {
            return 60; // 默认1分钟
        }

        String numPart = step.replaceAll("[^0-9]", "");
        String unitPart = step.replaceAll("[0-9]", "");

        long num = NumberUtils.toLong(numPart, 1);

        return switch (unitPart.toLowerCase()) {
            case "s" -> num;
            case "m" -> num * 60;
            case "h" -> num * 3600;
            case "d" -> num * 86400;
            default -> num * 60; // 默认按分钟处理
        };
    }

    /**
     * 将 TemporalAmount 转换为秒数
     * 处理 Duration 和 Period 两种类型的 TemporalAmount
     *
     * @param temporalAmount 时间量
     * @return 秒数
     */
    private long convertTemporalAmountToSeconds(TemporalAmount temporalAmount) {
        try {
            // 尝试直接转换为 Duration
            if (temporalAmount instanceof Duration) {
                return ((Duration) temporalAmount).getSeconds();
            }

            // 如果是 Period 类型，需要特殊处理
            if (temporalAmount instanceof java.time.Period) {
                java.time.Period period = (java.time.Period) temporalAmount;
                long totalSeconds = 0;

                // 转换年为秒 (假设一年365天)
                totalSeconds += period.getYears() * 365L * 24 * 3600;

                // 转换月为秒 (假设一月30天)
                totalSeconds += period.getMonths() * 30L * 24 * 3600;

                // 转换天为秒
                totalSeconds += period.getDays() * 24L * 3600;

                return totalSeconds;
            }

            // 尝试使用 Duration.from() 方法
            return Duration.from(temporalAmount).getSeconds();

        } catch (Exception e) {
            log.warn("Failed to convert TemporalAmount to seconds: {}, using default 6h", e.getMessage());
            return Duration.ofHours(6).getSeconds();
        }
    }
}
