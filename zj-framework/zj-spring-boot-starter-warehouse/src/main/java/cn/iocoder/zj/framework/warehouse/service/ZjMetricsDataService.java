package cn.iocoder.zj.framework.warehouse.service;

import cn.iocoder.zj.framework.common.dal.manager.MetricData;
import cn.iocoder.zj.framework.common.dal.manager.MetricHistoryData;
import cn.iocoder.zj.framework.common.pojo.VictoriaMetricsDTO;

import java.util.List;

public interface ZjMetricsDataService {

    /**
     * warehouse storage server availability or not
     *
     * @return true or false
     */
    Boolean getWarehouseStorageServerStatus();

    /**
     * Query Real Time Metrics Data
     *
     * @param monitorId Monitor Id
     * @param metrics   Metrics Name
     * @return metrics data
     */
    MetricData getMetricsData(Long monitorId, String metrics);

    /**
     * Queries historical data for a specified metric for monitoring
     *
     * @param uuid       Monitor Id
     * @param metrics    Metrics Name
     * @param history    Query Historical Time Period
     * @param interval   aggregate data calc
     * @param start
     * @param end
     * @param step
     * @param type
     * @param platformId
     * @return metrics history data
     */
    MetricHistoryData getMetricHistoryData(String uuid, String metrics, String metric, String history, Boolean interval, Long start, Long end, String step, String type, String platformId);

    MetricHistoryData getMetricHistoryData(String uuid, String metrics, String metric, String history, Long start, Long end, String step, String type);

    List<VictoriaMetricsDTO> getMetricData(List<String> uuids, List<String> metrics, String history, Long start, Long end, String step, String type,Integer num);

    List<VictoriaMetricsDTO> getMetricData(List<String> uuids, List<String> metrics, String history, Long start, Long end, String step);
}


