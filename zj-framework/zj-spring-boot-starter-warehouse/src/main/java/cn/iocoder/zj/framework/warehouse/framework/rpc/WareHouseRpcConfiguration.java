package cn.iocoder.zj.framework.warehouse.framework.rpc;

import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName : config  //类名
 * @Description : RPC 接口  //描述
 * <AUTHOR> <EMAIL> //作者
 * @Date: 2024/3/22  16:44
 */

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {
        PlatformconfigApi.class})
public class WareHouseRpcConfiguration {

}
