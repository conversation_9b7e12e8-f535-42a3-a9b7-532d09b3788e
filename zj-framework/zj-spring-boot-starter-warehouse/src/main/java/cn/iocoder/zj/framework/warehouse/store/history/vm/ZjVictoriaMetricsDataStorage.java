/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.zj.framework.warehouse.store.history.vm;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import cn.iocoder.zj.framework.common.pojo.VictoriaMetricsDTO;
import cn.iocoder.zj.framework.warehouse.store.history.AbstractHistoryDataStorage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.hertzbeat.common.constants.NetworkConstants;
import org.apache.hertzbeat.common.constants.SignConstants;
import org.apache.hertzbeat.common.entity.dto.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Primary;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * VictoriaMetrics data storage
 */
@Primary
@Component
@ConditionalOnProperty(prefix = "warehouse.store.victoria-metrics", name = "enabled", havingValue = "true")
@Slf4j
public class ZjVictoriaMetricsDataStorage extends AbstractHistoryDataStorage {

    private static final String IMPORT_PATH = "/api/v1/import";
    /**
     * <a href="https://docs.victoriametrics.com/Single-server-VictoriaMetrics.html#how-to-export-data-in-json-line-format">
     *     https://docs.victoriametrics.com/Single-server-VictoriaMetrics.html#how-to-export-data-in-json-line-format
     * </a>
     */
    private static final String EXPORT_PATH = "/api/v1/export";
    private static final String QUERY_RANGE_PATH = "/api/v1/query_range";
    private static final String STATUS_PATH = "/api/v1/status/tsdb";
    private static final String LABEL_KEY_NAME = "__name__";
    private static final String LABEL_KEY_JOB = "job";
    private static final String LABEL_KEY_INSTANCE = "instance";
    private static final String SPILT = "_";
    private static final String MONITOR_METRICS_KEY = "__metrics__";
    private static final String MONITOR_METRIC_KEY = "__metric__";

    private final VictoriaMetricsProperties victoriaMetricsProp;
    
    private final RestTemplate restTemplate;

    public ZjVictoriaMetricsDataStorage(VictoriaMetricsProperties victoriaMetricsProperties, RestTemplate restTemplate) {
        if (victoriaMetricsProperties == null) {
            log.error("init error, please config Warehouse victoriaMetrics props in application.yml");
            throw new IllegalArgumentException("please config Warehouse victoriaMetrics props");
        }
        this.restTemplate = restTemplate;
        victoriaMetricsProp = victoriaMetricsProperties;
        serverAvailable = checkVictoriaMetricsDatasourceAvailable();
    }

    private boolean checkVictoriaMetricsDatasourceAvailable() {
        // check server status
        try {
            HttpHeaders headers = new HttpHeaders();
            if (StringUtils.hasText(victoriaMetricsProp.getUsername())
                    && StringUtils.hasText(victoriaMetricsProp.getPassword())) {
                String authStr = victoriaMetricsProp.getUsername() + SignConstants.DOUBLE_MARK + victoriaMetricsProp.getPassword();
                String encodedAuth = new String(Base64.encodeBase64(authStr.getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8);
                headers.add(HttpHeaders.AUTHORIZATION, NetworkConstants.BASIC + " " + encodedAuth);
            }
            HttpEntity<Void> httpEntity = new HttpEntity<>(headers);
            ResponseEntity<String> responseEntity = restTemplate.exchange(victoriaMetricsProp.getUrl() + STATUS_PATH,
                    HttpMethod.GET, httpEntity, String.class);
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                log.info("check victoria metrics server status success.");
                return true;
            }
            log.error("check victoria metrics server status not success: {}.", responseEntity.getBody());
        } catch (Exception e) {
            log.error("check victoria metrics server status error: {}.", e.getMessage());
        }
        return false;
    }

    @Override
    public void saveData(ClusterMsg.Message message) {
        if (!isServerAvailable()) {
            serverAvailable = checkVictoriaMetricsDatasourceAvailable();
        }

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            if (StringUtils.hasText(victoriaMetricsProp.getUsername())
                    && StringUtils.hasText(victoriaMetricsProp.getPassword())) {
                String authStr = victoriaMetricsProp.getUsername() + ":" + victoriaMetricsProp.getPassword();
                String encodedAuth = new String(Base64.encodeBase64(authStr.getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8);
                headers.add(HttpHeaders.AUTHORIZATION, NetworkConstants.BASIC + SignConstants.BLANK + encodedAuth);
            }
            StringBuilder stringBuilder = new StringBuilder();
            HttpEntity<String> httpEntity = new HttpEntity<>(stringBuilder.toString(), headers);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(victoriaMetricsProp.getUrl() + IMPORT_PATH,
                    httpEntity, String.class);
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                log.debug("insert metrics data to victoria-metrics success.");
            } else {
                log.error("insert metrics data to victoria-metrics failed. {}", responseEntity.getBody());
            }
        } catch (Exception e) {
            log.error("flush metrics data to victoria-metrics error: {}.", e.getMessage(), e);
        }
    }


    @Override
    public void destroy() {}

    @Override
    public Map<String, List<Value>> getHistoryMetricData(String uuid, String metrics, String metric, String history, Long start, Long end, String step, String type, List<Map> platformId) {
        return Map.of();
    }

    @Override
    public Map<String, List<Value>> getHistoryMetricData(String uuid, String metrics, String metric, String history, Long start, Long end, String step, String type) {
        return Map.of();
    }

    @Override
    public Map<String, List<Value>> getHistoryIntervalMetricData(String uuid, String metrics, String metric, String history, Long start, Long end, String step, String type, List<Map> platformId) {
        return Map.of();
    }

    @Override
    public List<VictoriaMetricsDTO> getMetricData(List<String> uuids, List<String> metrics, String history, Long start, Long end, String step, String type,Integer num) {
        return new ArrayList<>();
    }

    @Override
    public List<VictoriaMetricsDTO> getMetricData(List<String> uuids, List<String> metrics, String history, Long start, Long end, String step) {
        return new ArrayList<>();
    }

    /**
     * victoria metrics content
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static final class VictoriaMetricsContent {

        /**
         * metric contains metric name plus labels for a particular time series
         */
        private Map<String, String> metric;

        /**
         * values contains raw sample values for the given time series
         */
        private Double[] values;

        /**
         * timestamps contains raw sample UNIX timestamps in milliseconds for the given time series
         * every timestamp is associated with the value at the corresponding position
         */
        private Long[] timestamps;
    }




    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static final class MetricsContent {

        /**
         * metric contains metric name plus labels for a particular time series
         */
        private Map<String, String> metric;

        /**
         * values contains raw sample values for the given time series
         */
        private Double[] values;

        /**
         * timestamps contains raw sample UNIX timestamps in milliseconds for the given time series
         * every timestamp is associated with the value at the corresponding position
         */
        private Long[] timestamps;
    }
}
