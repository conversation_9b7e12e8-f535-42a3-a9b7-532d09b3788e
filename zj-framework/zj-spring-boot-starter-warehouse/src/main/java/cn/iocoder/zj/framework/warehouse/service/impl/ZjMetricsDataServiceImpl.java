package cn.iocoder.zj.framework.warehouse.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.MetricData;
import cn.iocoder.zj.framework.common.dal.manager.MetricHistoryData;
import cn.iocoder.zj.framework.common.pojo.VictoriaMetricsDTO;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.warehouse.service.ZjMetricsDataService;
import cn.iocoder.zj.framework.warehouse.store.history.HistoryDataReader;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.hertzbeat.common.entity.dto.Value;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;


@Service
@Slf4j
public class ZjMetricsDataServiceImpl implements ZjMetricsDataService {

    @Autowired
    PlatformconfigApi platformconfigApi;

    private final Optional<HistoryDataReader> historyDataReader;

    public ZjMetricsDataServiceImpl(Optional<HistoryDataReader> historyDataReader) {
        this.historyDataReader = historyDataReader;
    }


    @Override
    public Boolean getWarehouseStorageServerStatus() {
        return historyDataReader.isPresent() && historyDataReader.get().isServerAvailable();
    }

    @Override
    public MetricData getMetricsData(Long monitorId, String metrics) {
        return null;
    }

    @Override
    public MetricHistoryData getMetricHistoryData(String uuid, String metrics, String metric, String history, Boolean interval, Long start, Long end, String step, String type, String platformId) {
//        if (history == null) {
//            history = "15m";
//        }
        LoginUser currentUser = SecurityFrameworkUtils.getLoginUser();
        List<Map> platform = new ArrayList<>();
        if (StrUtil.isNotEmpty(platformId)) {
            Map m = new HashMap();
            m.put("platformId", platformId);
            platform.add(m);
        } else {
            if (currentUser != null)
                platform = platformconfigApi.getPlatformSelectList(String.valueOf(currentUser.getTenantId())).getData();
        }

        Map<String, List<Value>> instanceValuesMap;
        if (interval == null || !interval) {
            instanceValuesMap = historyDataReader.get().getHistoryMetricData(uuid, metrics, metric, history, start, end, step, type, platform);
        } else {
            instanceValuesMap = historyDataReader.get().getHistoryIntervalMetricData(uuid, metrics, metric, history, start, end, step, type, platform);
        }
        return MetricHistoryData.builder()
                .uuid(uuid).metrics(metrics).values(instanceValuesMap).build();
    }

    @Override
    public MetricHistoryData getMetricHistoryData(String uuid, String metrics, String metric, String history, Long start, Long end, String step, String type) {
        Map<String, List<Value>> instanceValuesMap = historyDataReader.get().getHistoryMetricData(uuid, metrics, metric, history, start, end, step, type);
        return MetricHistoryData.builder()
                .uuid(uuid).metrics(metrics).values(instanceValuesMap).build();
    }


    @Override
    public List<VictoriaMetricsDTO> getMetricData(List<String> uuids, List<String> metrics, String history, Long start, Long end, String step, String type,Integer num) {
        return historyDataReader.get().getMetricData(uuids, metrics, history, start, end,  step, type, num);
    }

    @Override
    public List<VictoriaMetricsDTO> getMetricData(List<String> uuids, List<String> metrics, String history, Long start, Long end, String step) {
        return historyDataReader.get().getMetricData(uuids, metrics, history, start, end,  step);
    }

}
