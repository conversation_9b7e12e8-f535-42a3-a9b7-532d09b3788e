package cn.iocoder.zj.module.bpm.api.task.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
public class BpmTaskExtDTO {
    /**
     * 编号，自增
     */
    private Long id;

    /**
     * 任务的审批人
     *
     * 冗余 Task 的 assignee 属性
     */
    private Long assigneeUserId;
    /**
     * 任务的名字
     *
     * 冗余 Task 的 name 属性，为了筛选
     */
    private String name;
    /**
     * 任务的编号
     *
     * 关联 Task 的 id 属性
     */
    private String taskId;

    private Integer result;
    /**
     * 审批建议
     */
    private String reason;
    /**
     * 任务的结束时间
     *
     * 冗余 HistoricTaskInstance 的 endTime  属性
     */
    private LocalDateTime endTime;

    /**
     * 流程实例的编号
     *
     * 关联 ProcessInstance 的 id 属性
     */
    private String processInstanceId;
    /**
     * 流程定义的编号
     *
     * 关联 ProcessDefinition 的 id 属性
     */
    private String processDefinitionId;
    /**
     * 超时标识，0未超时，1已超时
     */
    private Integer isOvertime;

    private String processName;

    private String formVariables;

    private String assignee;

    private Date createTime;
}
