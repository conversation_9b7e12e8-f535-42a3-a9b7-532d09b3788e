package cn.iocoder.zj.module.bpm.api.task;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.bpm.api.task.dto.BpmTaskExtDTO;
import cn.iocoder.zj.module.bpm.enums.ApiConstants;
import com.fasterxml.jackson.databind.introspect.AnnotationCollector;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 任务统计")
public interface BpmTaskApi {
    String PREFIX = ApiConstants.PREFIX + "/bpm-task-count";

    @GetMapping(PREFIX + "/workOrderStatistics")
    @Operation(summary = "运维看板-工单处理与审批统计")
    @Parameters({
            @Parameter(name = "homologousUsers", description = "用户编号", example = "2", required = true),
            @Parameter(name = "userRole", description = "用户角色", example = "superAdmin", required = true)
    })
    CommonResult<Map<String,Object>> workOrderStatistics(@RequestParam("homologousUsers") List<Long> homologousUsers,
                                                         @RequestParam("userRole")  String userRole);

    @GetMapping(PREFIX + "/WorkOrderStatisticsInWeek")
    @Operation(summary = "运维看板-进五日工单完成情况")
    @Parameters({
            @Parameter(name = "homologousUsers", description = "用户编号", example = "2", required = true),
            @Parameter(name = "userRole", description = "用户角色", example = "superAdmin", required = true)
    })
    List<Map<String,Object>> getWorkOrderStatisticsInWeek(@RequestParam("homologousUsers")List<Long> homologousUsers,
                                                          @RequestParam("userRole") String role);
    @GetMapping(PREFIX + "/getRanking")
    @Operation(summary = "运维看板-工单执行排行")
    @Parameters({
            @Parameter(name = "homologousUsers", description = "用户编号", example = "2", required = true),
            @Parameter(name = "role", description = "用户角色", example = "superAdmin", required = true)
    })
    List<Map<String, Object>> getRanking(@RequestParam("homologousUsers") List<Long> homologousUsers,
                                         @RequestParam("role") String role);

    @GetMapping(PREFIX + "/workOrderSourceType")
    @Operation(summary = "运维看板-工单资源分布")
    @Parameters({
            @Parameter(name = "homologousUsers", description = "用户编号", example = "2", required = true),
            @Parameter(name = "role", description = "用户角色", example = "superAdmin", required = true)
    })
    CommonResult<List<Map<String, String>>> getWorkOrderSourceType(@RequestParam("homologousUsers") List<Long> homologousUsers,
                                                                   @RequestParam("role")String role);
    @GetMapping(PREFIX + "/createInWeek")
    @Operation(summary = "运维看板-近七日工单走势")
    @Parameters({
            @Parameter(name = "homologousUsers", description = "用户编号", example = "2", required = true),
            @Parameter(name = "role", description = "用户角色", example = "superAdmin", required = true)
    })
    CommonResult<List<Map<String, String>>> getCreateInWeek(@RequestParam("homologousUsers") List<Long> homologousUsers,
                                                            @RequestParam("role")String role);
    @GetMapping(PREFIX + "/getPersonWorkOrderInfo")
    @Operation(summary = "首页—个人工单情况统计")
    @Parameters({
            @Parameter(name = "homologousUsers", description = "用户编号", example = "2", required = true),
            @Parameter(name = "userId", description = "用户ID", example = "2", required = true),
            @Parameter(name = "role", description = "用户角色", example = "superAdmin", required = true)
    })
    CommonResult<Map<String, String>> getPersonWorkOrderInfo(@RequestParam("homologousUsers")List<Long> homologousUsers,
                                                             @RequestParam("userId")Long userId,
                                                             @RequestParam("role")String role);
    @GetMapping(PREFIX + "/getOvertimeTasks")
    @Operation(summary = "统计每个人的任务完成情况")
    @Parameter(name = "targetUsers", description = "指定用户", example = "1,2,3", required = true)
    CommonResult<List<Map>> getTasksCount(@RequestParam(value = "userName",required = false)String userName,
                                          @RequestParam("tenantId")Long tenantId,@RequestParam(value = "startTime",required = false)String startTime,@RequestParam(value = "endTime",required = false)String endTime);
    @GetMapping(PREFIX + "/getSLAResponse")
    @Operation(summary = "统计总的超时情况")
    @Parameter(name = "targetUsers", description = "指定用户", example = "1,2,3", required = true)
    CommonResult< Map> getSLAResponse(@RequestParam("targetUsers")List<Long> targetUsers);
    @GetMapping(PREFIX + "/getTodoTask")
    @Operation(summary = "获取待办列表")
    @Parameter(name = "platformId", description = "平台id", example = "1", required = false)
    CommonResult<List<Map>> getTodoTask(@RequestParam(value = "platformId",required = false)Long platformId);

    @GetMapping(PREFIX + "/getInfo")
    @Operation(summary = "获取问题工单的流程详情")
    CommonResult<Map<String, Object>> getProcessInfo(@RequestParam("alarmId") Long alarmId, @RequestParam("type")  Integer type);
}
