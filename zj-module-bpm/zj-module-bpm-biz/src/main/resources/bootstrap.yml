# Tomcat
server:
  port: 48083

flowable:
  # 1. false: 默认值，Flowable 启动时，对比数据库表中保存的版本，如果不匹配。将抛出异常
  # 2. true: 启动时会对数据库中所有表进行更新操作，如果表存在，不做处理，反之，自动创建表
  # 3. create_drop: 启动时自动创建表，关闭时自动删除表
  # 4. drop_create: 启动时，删除旧表，再创建新表
  database-schema-update: true # 设置为 false，可通过 https://github.com/flowable/flowable-sql 初始化
  db-history-used: true # flowable6 默认 true 生成信息表，无需手动设置
  check-process-definitions: false # 设置为 false，禁用 /resources/processes 自动部署 BPMN XML 流程
  history-level: full # full：保存历史数据的最高级别，可保存全部流程相关细节，包括流程流转各节点参数

# Spring
spring:
  main:
    allow-circular-references: true  # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许 Bean 覆盖，例如说 Dubbo 或者 Feign 等会存在重复定义的服务
  application:
    # 应用名称
    name: bpm-server
  profiles:
    # 环境配置
    active: prod
  cloud:
    nacos:
      discovery:
        namespace: ${spring.profiles.active} # 命名空间。这里使用 dev 开发环境
        # 服务注册地址
        server-addr: nacos-production.zj-cloud:8848
      config:
        # 配置中心地址
        server-addr: nacos-production.zj-cloud:8848
        namespace: ${spring.profiles.active} # 命名空间。这里使用 dev 开发环境
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP
        name: ${spring.application.name}-${spring.profiles.active} # 使用的 Nacos 配置集的 dataId，默认为 spring.application.name
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - data-id: application.yml
            group: DEFAULT_GROUP
            refresh: true # 是否自动刷新配置，默认为 false
logging:
  file:
    name: ${user.home}/logs/${spring.application.name}.log # 日志文件名，全路径
