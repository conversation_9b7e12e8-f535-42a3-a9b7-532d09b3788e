<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.bpm.dal.mysql.definition.FlowNodeMapper">
    <update id="updateModelNumberByModelId">
        UPDATE ACT_RE_MODEL
        SET NUMBER = #{number}
        WHERE ID_= #{modelId};
    </update>
    <update id="updateProcessNumberById">
        UPDATE bpm_process_instance_ext
        SET process_number = #{processNumber}
        WHERE process_instance_id = #{processInstanceId}
        and deleted=0;
    </update>
    <update id="updateActinstOverTimeList">
        UPDATE bpm_task_ext
        SET is_overtime = 1
         ,is_read=0
        WHERE 1=1
            AND id IN
            <foreach collection="list" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
    </update>
    <update id="updateIsRead">
        UPDATE bpm_task_ext
        SET is_read = 1
        WHERE task_id in
        <foreach collection="taskIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <delete id="deleteByModeId">
        DELETE FROM bpm_flow_node WHERE model_id=#{modelId};
    </delete>
    <select id="selectByNodeId"
            resultType="cn.iocoder.zj.module.bpm.controller.admin.definition.vo.node.FlowNodeBaseVO">
        SELECT
            *
        FROM
            bpm_flow_node
        WHERE
            deleted = 0
          AND node_id =#{flowNode.nodeId}
          AND model_id=#{flowNode.model_id}
            LIMIT 1

    </select>
    <select id="selectByProcessInstanceId"
            resultType="cn.iocoder.zj.module.bpm.dal.dataobject.definition.FlowNodeDO">
        SELECT
            *
        FROM
            bpm_flow_node node
        WHERE
                node_id =(
                SELECT
                    ACT_ID_
                FROM
                    ACT_HI_ACTINST
                WHERE
                    1 = 1
                  AND TASK_ID_ =(
                    SELECT
                        task_id
                    FROM
                        bpm_task_ext
                    WHERE
                        process_instance_id = #{processInstanceId}
                      AND result = 1
                      AND deleted = 0
                ))
    </select>
    <select id="selectFinishByProcessInstanceId"
            resultType="java.lang.String">
        SELECT
            ACT_ID_
        FROM
            ACT_HI_ACTINST
        WHERE
            PROC_INST_ID_ = #{processInstanceId}
          AND ACT_TYPE_ = #{actType}
    </select>

    <select id="getNameByDefinitionId" resultType="java.lang.String">
        SELECT NAME
        FROM
            bpm_form
        WHERE
                id =(
                SELECT
                    form_id
                FROM
                    bpm_process_definition_ext
                WHERE
                    process_definition_id = #{definitionId}
                  AND deleted = 0)
          AND deleted =0
    </select>

    <select id="getNameByTaskId" resultType="java.lang.String">
        SELECT NAME
        FROM
            bpm_form
        WHERE
                id =(
                SELECT
                    form_id
                FROM
                    bpm_process_definition_ext
                WHERE
                        process_definition_id = ( SELECT process_definition_id FROM bpm_task_ext WHERE task_id = #{taskId} )
                  AND deleted = 0
            )
          AND deleted = 0
    </select>
    <select id="getNumberByInstanceId" resultType="java.lang.String">
        SELECT
            NUMBER
        FROM
            ACT_RE_MODEL
        WHERE
                ID_ =( SELECT
                           model_id
                       FROM
                           bpm_process_definition_ext
                       WHERE
                               process_definition_id =(
                               SELECT
                                   process_definition_id
                               FROM
                                   bpm_process_instance_ext
                               WHERE
                                   process_instance_id = #{processInstanceId}
                           )
            )
    </select>
    <select id="getNumberCount" resultType="java.lang.Integer">
        SELECT
            COUNT(*) AS count
        FROM
            bpm_process_instance_ext
        WHERE
            process_number LIKE #{yearNumber};
    </select>
    <select id="selectActinstOverTimeMap" resultType="java.util.Map">
        SELECT
            task.id,
            task.NAME,
            task.task_id,
            actinst.START_TIME_,
            actinst.END_TIME_,
            actinst.timeout_time
        FROM
            bpm_task_ext task
                LEFT JOIN (
                SELECT
                    act.START_TIME_,
                    act.END_TIME_,
                    act.TASK_ID_,
                    node.timeout_time
                FROM
                    ACT_HI_ACTINST act
                        LEFT JOIN ( SELECT * FROM bpm_flow_node WHERE is_timeout = 1 AND deleted = 0 ) node ON act.ACT_ID_ = node.node_id
                        AND act.PROC_DEF_ID_ = node.process_definition_id
                WHERE
                    act.TASK_ID_ IS NOT NULL
                  AND act.REV_ = 1
                GROUP BY
                    act.ID_
            ) actinst ON task.task_id = actinst.TASK_ID_
        WHERE
            task.is_overtime = 0
          AND task.deleted = 0
          AND task.result =1
          and actinst.timeout_time is not null
    </select>
    <select id="getProcessInstanceOvertimeMap" resultType="java.util.Map">
        SELECT
        task.task_id AS taskId,
        task.process_instance_id AS instanceId,
        task.NAME AS taskName,
        instance.NAME AS instanceName,
        instance.process_number AS processNumber
        FROM
        bpm_task_ext task
        LEFT JOIN bpm_process_instance_ext instance ON task.process_instance_id = instance.process_instance_id
        WHERE
        task.result = 1
        AND task.task_id IN
        <foreach collection="list" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>
    <select id="getTenantIdByUserId" resultType="java.lang.Long">
        SELECT
            tenant_id
        FROM
            system_users
        WHERE
            id = #{userId}
          AND deleted =0
    </select>
    <select id="getModelNumberByModelId" resultType="java.lang.String">
        SELECT
            NUMBER as number
        FROM
            ACT_RE_MODEL
        WHERE
            ID_=#{modelId}
    </select>
    <select id="getNodeIdByProcessInstanceId" resultType="java.lang.String">
        SELECT
            ACT_ID_ as nodeId
        FROM
            ACT_HI_ACTINST
        WHERE
            1 = 1
          AND TASK_ID_ in(
            SELECT
                task_id
            FROM
                bpm_task_ext
            WHERE
                process_instance_id = #{processInstanceId}
              AND result = 1
              AND deleted = 0
        )
          AND ASSIGNEE_= #{userId}
        GROUP BY ACT_ID_
    </select>

    <select id="selectModelListByNumber" resultType="java.lang.String">
        SELECT
            id_
        FROM
            ACT_RE_MODEL
        WHERE
            NUMBER IN
        <foreach collection="eventProcessNum" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>

    <select id="getWorkOrderSourceTyp" resultType="java.util.Map">
        SELECT
            IF(result.hardwareNum is null,0,result.hardwareNum) hardwareNum,
            IF(result.storageNum is null,0,result.storageNum) storageNum,
            IF(result.hostNum is null,0,result.hostNum) hostNum,
            COUNT(*) total

        FROM (
                 SELECT
                     SUM( CASE monitorAlarm.source_type WHEN 'monitor_alarm_hardware' THEN 1 ELSE 0 END ) hardwareNum,
                     SUM( CASE monitorAlarm.source_type WHEN 'monitor_alarm_disk' THEN 1 ELSE 0 END ) storageNum,
                     SUM( CASE monitorAlarm.source_type WHEN 'monitor_alarm_host' THEN 1 ELSE 0 END ) hostNum
                 from
                     (SELECT
                          SUBSTRING_INDEX( dc.abstract, ',', 1 ) orderId,
                          SUBSTRING_INDEX( dc.tableSource, ',', 1 ) tableName
                      FROM
                          (
                              SELECT
                                  substring_index( formatFelds, 'requestId:',- 1 ) AS abstract,
                                  substring_index( formatFelds, 'table:',- 1 ) AS tableSource
                              FROM
                                  (
                                      SELECT
                                          *,
                                          REPLACE ( REPLACE ( REPLACE ( form_variables, '"', '' ), '}', '' ), '{', '' ) AS formatFelds
                                      FROM
                                          bpm_process_instance_ext
                                  ) newIns
                              WHERE
                                  newIns.process_number LIKE '%WT%'
                                AND newIns.formatFelds != ''
                                AND newIns.formatFelds IS NOT NULL
                                AND newIns.formatFelds LIKE '%requestId:%'
                                AND newIns.formatFelds LIKE '%table:%'
                                  <if test="role == 'personal'">
                                      AND start_user_id = #{id}
                                  </if>
                                  <if test="role == 'tenantAdmin'">
                                      AND start_user_id in
                                      <foreach collection="homologousUsers" open="(" close=")" separator="," item="item">
                                        #{item}
                                      </foreach>
                                  </if>
                          ) dc) instances
                         LEFT JOIN monitor_alarm_record monitorAlarm on instances.orderId = monitorAlarm.id and instances.tableName = 'monitorAlarm'
             ) result

    </select>

    <select id="getCreateInWeek" resultType="java.util.Map">
        SELECT
            count(START_TIME_) total,
            SUM( IF(END_TIME_ is not null,1,0)) doneCount,
            SUM( IF(END_TIME_ is null,1,0)) todoCount,
            DATE_FORMAT( START_TIME_, "%Y-%m-%d" ) dateStr
        FROM
            ACT_HI_PROCINST
        WHERE
            <foreach collection="dateStrList" open="(" separator="or" close=")" item="date">
                DATE_FORMAT( START_TIME_, "%Y-%m-%d" ) = #{date}
            </foreach>
            <if test="role !='superAdmin'">
                AND START_USER_ID_ IN
                <foreach collection="homologousUsers" open="(" separator="," close=")" item="user">
                    #{user}
                </foreach>
            </if>
        GROUP BY
            DATE_FORMAT( START_TIME_, "%Y-%m-%d" )
        ORDER BY
            DATE_FORMAT( START_TIME_, "%Y-%m-%d" ) ASC
    </select>

    <select id="getPersonWorkOrderInfo" resultType="java.util.Map">
        SELECT
        0 backCount,
        total,
        IF(result.todoCount is null,0,result.todoCount) todoCount,
        IF(result.doneCount is null,0,result.doneCount) doneCount
        FROM(
        SELECT
        count(*) total,
        SUM(IF(END_TIME_ IS NULL ,1,0)) todoCount,
        SUM(IF(END_TIME_ IS NOT NULL ,1,0)) doneCount
        FROM
        ACT_HI_PROCINST
        <if test="role != 'personal'">WHERE
            START_USER_ID_ IN (
            SELECT
            id
            FROM
            system_users
            WHERE
            tenant_id = ( SELECT tenant_id FROM system_users WHERE id = #{userId} ))
        </if>
        <if test="role =='personal'">
            WHERE
            START_USER_ID_ = #{userId}
        </if>
        ) result
    </select>

    <select id="countBackTack" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT process_instance_id) AS reject
        FROM (
        SELECT
        task.process_instance_id
        FROM
        bpm_process_instance_ext pro
        LEFT JOIN (SELECT process_instance_id FROM bpm_task_ext WHERE result = 5 GROUP BY process_instance_id) task ON
        task.process_instance_id = pro.process_instance_id
        AND pro.end_time IS NULL
        WHERE
        pro.deleted = 0
        AND task.process_instance_id IS NOT NULL
        <if test="userRole == 'personal'">
            AND pro.start_user_id = #{id}
        </if>
        <if test="userRole != 'personal'">
            AND pro.start_user_id IN
            <foreach collection="homologousUsers" open="(" separator="," close=")" item="user">
                #{user}
            </foreach>
        </if>
        UNION
        SELECT
        pro.process_instance_id
        FROM
        bpm_process_instance_ext pro
        WHERE
        pro.deleted = 0
        AND pro.is_fall_back = 1

        <if test="userRole == 'personal'">
            AND pro.start_user_id = #{id}
        </if>
        <if test="userRole != 'personal'">
            AND pro.start_user_id IN
            <foreach collection="homologousUsers" open="(" separator="," close=")" item="user">
                #{user}
            </foreach>
        </if>
        ) combined;
    </select>
    <select id="getModelIdByDefinitionId" resultType="java.lang.String">
        SELECT
            model_id
        FROM
            bpm_task_assign_rule
        WHERE
            process_definition_id =#{definitionId}
            limit 1
    </select>

    <select id="getTasksCount" resultType="java.util.Map">
        SELECT
            task2.assignee_ assigneeId,
            users.nickname assigneer,
            SUM(CASE WHEN task1.is_overtime = 1 THEN 1 ELSE 0 END ) overtime,
            SUM(CASE WHEN task2.END_TIME_ is not null THEN 1 ELSE 0 END ) total
        FROM
            bpm_task_ext task1
                LEFT JOIN ACT_HI_TASKINST task2 ON task2.id_ = task1.task_id
                LEFT JOIN system_users users on task2.assignee_ = users.id
        WHERE
            (task1.result != 4 or task1.reason not like '%系统自动取消%')
            and task2.END_TIME_ is not null
            and task2.assignee_ IN
            (select id from system_users where tenant_id = #{tenantId} and deleted = 0
            <if test="userName !='' and userName != null">
                and nickname like concat("%",#{userName},"%")
            </if>
            )
        <if test="startTime !='' and startTime != null">
            and to_days( task2.START_TIME_ ) &gt;= to_days(#{startTime})
        </if>
        <if test="endTime !='' and endTime != null">
            and to_days( task2.END_TIME_ ) &lt;= to_days( #{endTime})
        </if>
        GROUP BY users.id
    </select>

    <select id="getSLAResponse" resultType="java.util.Map">
        SELECT
        CASE WHEN t1.overtimeCount is null THEN 0 ELSE t1.overtimeCount END overtimeCount,
        t1.total
        FROM(
        SELECT
        SUM( CASE WHEN task.overtime > 0 THEN 1 ELSE 0 END ) overtimeCount,
        count(task.taskId) total
        FROM
        (
        SELECT
        SUM( is_overtime ) overtime,
        ext.task_id taskId,
        inst.ASSIGNEE_ assignee
        FROM
        bpm_task_ext ext
        LEFT JOIN ACT_HI_TASKINST inst ON ext.task_id = inst.id_
        where inst.END_TIME_ IS NOT NULL
        GROUP BY
        process_instance_id
        ) task
        where task.assignee in
        <foreach collection="targetUsers" separator="," close=")" open="(" item="user">#{user}
        </foreach>
        )t1
    </select>

    <select id="getTodoTaskDO" resultType="cn.iocoder.zj.module.bpm.api.task.dto.BpmTaskExtDTO">
        SELECT
        bte.task_id,bte.process_instance_id,bte.process_definition_id,bte.create_time,bpie.name processName,bpie.form_variables,
        users.nickname assignee
        FROM
            bpm_task_ext bte
        LEFT JOIN ACT_HI_TASKINST tinst ON bte.task_id = tinst.id_
        LEFT JOIN bpm_process_instance_ext bpie on bpie.process_instance_id = bte.process_instance_id
        LEFT JOIN system_users users on users.id = tinst.ASSIGNEE_
        WHERE
            ( bte.result != 4 OR bte.reason NOT LIKE '%系统自动取消%' )
          AND tinst.END_TIME_ IS NULL
          AND ASSIGNEE_ IN
        <foreach collection="targetUsers" separator="," close=")" open="(" item="user">
            #{user}
        </foreach>
        <if test="platformId != null">
            and bpie.form_variables like concat("%",#{platformId},"%")
        </if>
    </select>

    <select id="getFormByDefinitionId" resultType="java.util.Map">
        SELECT
            bpde.process_definition_id processDefinitionId,
            bf.fields AS formatFelds
        FROM
            bpm_process_definition_ext bpde
        LEFT JOIN bpm_form bf on bf.id = bpde.form_id
        WHERE
            bpde.process_definition_id in
            <foreach collection="DefinitionId"  separator="," close=")" open="(" item="user">
                #{user}
            </foreach>
    </select>

    <select id="getOverTimeTaskCount" resultType="java.util.Map">
        SELECT
        CASE WHEN task.notovertime is null THEN 0 ELSE task.notovertime END notovertime,
        CASE WHEN task.overtime is null THEN 0 ELSE task.overtime END overtime,
        CASE WHEN task.total is null THEN 0 ELSE task.total END total
        FROM(
        SELECT
        SUM( CASE WHEN task1.is_overtime = 0 THEN 1 ELSE 0 END ) notovertime,
        SUM( CASE WHEN task1.is_overtime = 1 THEN 1 ELSE 0 END ) overtime,
        SUM( CASE WHEN task2.END_TIME_ IS NOT NULL THEN 1 ELSE 0 END ) total
        FROM
        bpm_task_ext task1
        LEFT JOIN ACT_HI_TASKINST task2 ON task2.id_ = task1.task_id
        LEFT JOIN system_users users ON task2.assignee_ = users.id
        WHERE
        task2.END_TIME_ IS NOT NULL
        <if test="userRole == 'personal'">and task2.ASSIGNEE_ = #{id}
        </if>
        <if test="
        userRole != 'personal'">AND task2.ASSIGNEE_ IN
            <foreach collection="homologousUsers" open="(" separator="," close=")" item="user">
                    #{user}
                </foreach>
            </if>
          AND ( task1.result != 4 OR task1.reason NOT LIKE '%系统自动取消%' )
        ) task
    </select>

    <select id="getAllProcessInstance"
            resultType="cn.iocoder.zj.module.bpm.controller.admin.task.vo.task.BpmProcessInstanceExtRespVo">
        select form_variables formVariablesStr,start_user_id,id,process_instance_id,process_definition_id
        from bpm_process_instance_ext
        where 1=1
        <if test="userRole == 'personal'">
          and start_user_id = #{id}
        </if>
        <if test="userRole != 'personal'">
            AND start_user_id IN
            <foreach collection="homologousUsers" open="(" separator="," close=")" item="user">
                #{user}
            </foreach>
        </if>
    </select>

    <select id="getHzbAlarmByIds" resultType="java.util.Map">
        <if test="requestIds == null or requestIds.size ==0">
        SELECT
            0 total,
            0 hostNum,
            0 hardwareNum,
            0 storageNum
        </if>
        <if test="requestIds != null and requestIds.size >0">
        SELECT
        alert.total,
        (case when alert.hostNum is not null THEN alert.hostNum ELSE 0 end )         hostNum,
        (case when alert.hardwareNum is not null THEN alert.hardwareNum ELSE  0 end) hardwareNum,
        (case when alert.storageNum is not null then alert.storageNum ELSE 0 end )   storageNum
        FROM
        (SELECT
        count(id) as total,
        SUM( CASE WHEN app = 'host' THEN 1 ELSE 0 END ) hostNum,
        SUM( CASE WHEN app = 'hardware' THEN 1 ELSE 0 END ) hardwareNum,
        SUM( CASE WHEN app = 'storage' THEN 1 ELSE 0 END ) storageNum
        FROM
        hzb_alert
        WHERE
        app IN ( 'host', 'hardware', 'storage' )
        and id in
        <foreach collection="requestIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        ) alert
        </if>
    </select>

    <select id="getCloudAlarmByIds" resultType="java.util.Map">
        <if test="cloudAlarm == null or cloudAlarm.size ==0">
        SELECT
            0 total,
            0 hostNum,
            0 hardwareNum,
            0 storageNum
        </if>
        <if test="cloudAlarm != null and cloudAlarm.size >0">
        SELECT
        alarm.total,
        (case when alarm.hostNum is not null THEN alarm.hostNum ELSE 0 end )         hostNum,
        (case when alarm.hardwareNum is not null THEN alarm.hardwareNum ELSE  0 end) hardwareNum,
        ( case when alarm.storageNum is not null then alarm.storageNum ELSE 0 end )   storageNum
        FROM
        (SELECT
        count(id) total,
        SUM(CASE WHEN source_type = 'monitor_alarm_host' THEN 1 ELSE 0 END) hostNum,
        SUM(CASE WHEN source_type = 'monitor_alarm_hardware' THEN 1 ELSE 0 END) hardwareNum,
        SUM(CASE WHEN source_type = 'monitor_alarm_disk' THEN 1 ELSE 0 END) storageNum
        from monitor_alarm_record
        where id in
        <foreach collection="cloudAlarm" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        ) alarm
        </if>
    </select>

    <select id="getDefinitionIdByFormId" resultType="java.lang.String">
         SELECT
             process_definition_id
         FROM
             bpm_process_definition_ext t1
                 INNER JOIN ( SELECT model_id, MAX( create_time ) AS create_time FROM bpm_process_definition_ext GROUP BY model_id ) t2 ON t1.model_id = t2.model_id
                 AND t1.create_time = t2.create_time
         WHERE
             form_id = #{formId}
    </select>

    <update id="updateNodeBatchByNodeId">
        <foreach collection="flowNodeDOList" item="item" index="index" open="" close="" separator=";">
            update bpm_flow_node
            <set>
                node_fields= ${item.nodeFields}
            </set>
            where id = ${item.id}
        </foreach>
    </update>

    <select id="getMonitorAssetList" resultType="java.util.Map">
        SELECT t.*
        FROM (
        SELECT hostName, MAX(createTime) as maxCreateTime
        FROM (
        SELECT omm.id, oma.asset_name as assetName, oma.hostname as hostName, omm.create_time as createTime
        FROM om_monitor_asset oma
        LEFT JOIN om_monitor_authorization omm ON oma.id = omm.monitor_asset_id
        WHERE oma.platform_id in
        <foreach collection="platformId" item="platformId" open="(" close=")" separator=",">
            #{platformId}
        </foreach>
        AND oma.deleted = 0 AND oma.authorization_type = 1 AND omm.id IS NOT NULL AND omm.deleted = 0
        ) d
        GROUP BY hostName
        ) g
        JOIN (
        SELECT omm.id, oma.asset_name as assetName, oma.hostname as hostName, omm.create_time as createTime
        FROM om_monitor_asset oma
        LEFT JOIN om_monitor_authorization omm ON oma.id = omm.monitor_asset_id
        WHERE oma.platform_id in
        <foreach collection="platformId" item="platformId" open="(" close=")" separator=",">
            #{platformId}
        </foreach>
        AND oma.deleted = 0 AND oma.authorization_type = 1 AND omm.id IS NOT NULL AND omm.deleted = 0
        ) t ON t.hostName = g.hostName AND t.createTime = g.maxCreateTime
        ORDER BY t.createTime DESC;
    </select>


    <select id="getMonitorAssetLists" resultType="java.util.Map">
        SELECT
            *
        FROM
            (
                SELECT
                    t.id,
                    t.assetName,
                    t.hostName,
                    t.createTime
                FROM
                    (
                        SELECT
                            hostName,
                            MAX( createTime ) AS maxCreateTime
                        FROM
                            (
                                SELECT
                                    omm.id,
                                    oma.asset_name AS assetName,
                                    oma.hostname AS hostName,
                                    omm.create_time AS createTime
                                FROM
                                    om_monitor_asset oma
                                        LEFT JOIN om_monitor_authorization omm ON oma.id = omm.monitor_asset_id
                                WHERE
                                    oma.platform_id in
                                    <foreach collection="platformId" item="platformId" open="(" close=")" separator=",">
                                        #{platformId}
                                    </foreach>
                                  AND oma.deleted = 0
                                  AND oma.authorization_type = 1
                                  AND omm.id IS NOT NULL
                                  AND omm.deleted = 0
                            ) d
                        GROUP BY
                            hostName
                    ) g
                        JOIN (
                        SELECT
                            omm.id,
                            oma.asset_name AS assetName,
                            oma.hostname AS hostName,
                            omm.create_time AS createTime
                        FROM
                            om_monitor_asset oma
                                LEFT JOIN om_monitor_authorization omm ON oma.id = omm.monitor_asset_id
                        WHERE
                            oma.platform_id in
                            <foreach collection="platformId" item="platformId" open="(" close=")" separator=",">
                                #{platformId}
                            </foreach>
                          AND oma.deleted = 0
                          AND oma.authorization_type = 1
                          AND omm.id IS NOT NULL
                          AND omm.deleted = 0
                    ) t ON t.hostName = g.hostName
                        AND t.createTime = g.maxCreateTime UNION ALL
                SELECT
                    t.id,
                    t.assetName,
                    t.hostName,
                    t.createTime
                FROM
                    (
                        SELECT
                            hostName,
                            MAX( createTime ) AS maxCreateTime
                        FROM
                            (
                                SELECT
                                    omm.id,
                                    oma.NAME AS assetName,
                                    oma.platform_name AS hostName,
                                    omm.create_time AS createTime
                                FROM
                                    monitor_host_info oma
                                        LEFT JOIN om_monitor_authorization omm ON oma.id = omm.monitor_asset_id
                                WHERE
                                    oma.platform_id in
                                    <foreach collection="platformId" item="platformId" open="(" close=")" separator=",">
                                        #{platformId}
                                    </foreach>
                                  AND oma.deleted = 0
                                  AND oma.authorization_type = 1
                                  AND omm.id IS NOT NULL
                                  AND omm.deleted = 0
                            ) d
                        GROUP BY
                            hostName
                    ) g
                        JOIN (
                        SELECT
                            omm.id,
                            oma.NAME AS assetName,
                            oma.platform_name AS hostName,
                            omm.create_time AS createTime
                        FROM
                            monitor_host_info oma
                                LEFT JOIN om_monitor_authorization omm ON oma.id = omm.monitor_asset_id
                        WHERE
                            oma.platform_id in
                            <foreach collection="platformId" item="platformId" open="(" close=")" separator=",">
                                #{platformId}
                            </foreach>
                          AND oma.deleted = 0
                          AND oma.authorization_type = 1
                          AND omm.id IS NOT NULL
                          AND omm.deleted = 0
                    ) t ON t.hostName = g.hostName
                        AND t.createTime = g.maxCreateTime
            ) combined_results
        ORDER BY
            createTime DESC;
    </select>
    <select id="getProcessInstanceStartId" resultType="java.lang.String">
        SELECT creator FROM bpm_process_instance_ext where process_instance_id=#{instanceId}
    </select>
    <select id="getFormFieldsByDefinitionId" resultType="java.lang.String">
        SELECT
            form_fields AS formFields
        FROM
            bpm_process_definition_ext
        WHERE
            process_definition_id = #{definitionId}
          AND deleted =0
    </select>
    <select id="getFormVariablesByInstanceId" resultType="java.lang.String">
        SELECT
            form_variables AS formVariables
        FROM
            bpm_process_instance_ext
        WHERE
            process_instance_id = #{instanceId}
          AND deleted =0
    </select>
    <select id="selectRoleExistence" resultType="java.lang.Integer">
        SELECT
            COUNT(*) AS count
        FROM
            system_users
        WHERE
            id IN (
            SELECT
            user_id
            FROM
            system_user_role
            WHERE
            role_id = ( SELECT id FROM system_role WHERE NAME = '运维管理' AND deleted = 0 AND tenant_id = 1 LIMIT 1 )
          AND deleted = 0
            )
          AND deleted =0
    </select>

    <select id="getRoleExistence" resultType="cn.iocoder.zj.module.bpm.dal.dataobject.task.ReportSubscriptionDTO">
        SELECT
            rsu.user_id,
            rsu.user_name,
            rsu.wechat_state,
            rsu.email_state,
            swb.open_id,
            su.email
        FROM
            `report_subscription_user` rsu
                JOIN system_users su ON rsu.user_id = su.id
                AND su.deleted = 0
                AND su.STATUS = 0
                JOIN system_user_role sur ON su.id = sur.user_id
                AND sur.deleted = 0
                JOIN system_role sr ON sur.role_id = sr.id
                AND sr.deleted = 0
                JOIN report_subscription rs ON rsu.subscription_uuid = rs.uuid
                LEFT JOIN system_wechat_binding swb ON swb.user_id = rsu.user_id
        WHERE
            sr.NAME = '运维管理'
          AND sr.tenant_id = 1
          AND rs.method_name = 'alertSubscription';
    </select>
</mapper>