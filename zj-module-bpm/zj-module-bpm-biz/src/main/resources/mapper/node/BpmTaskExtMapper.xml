<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.zj.module.bpm.dal.mysql.task.BpmTaskExtMapper">

<select id="getIdsByProcInstId" resultType="java.lang.String">
    select ID_ from ACT_RU_TASK where PROC_INST_ID_ = #{processInstanceId} and TASK_DEF_KEY_ = #{taskDefKey}
    </select>

<select id="getAssigneesByProcInstId" resultType="java.lang.String">
    select ASSIGNEE_ from ACT_HI_TASKINST where PROC_INST_ID_ = #{processInstanceId} and TASK_DEF_KEY_ = #{taskDefKey} and END_TIME_ is not null group by ASSIGNEE_
    </select>

<update id="setHisAssignee">
    update ACT_HI_TASKINST set ASSIGNEE_ = #{assignee} where ID_ = #{taskId}
    </update>

<update id="setRunAssignee">
    update ACT_RU_TASK set ASSIGNEE_ = #{assignee} where ID_ = #{taskId}
    </update>
    <update id="setActinstAssignee">
        update ACT_HI_ACTINST set ASSIGNEE_ = #{assignee} where TASK_ID_ = #{taskId}
    </update>

<select id="getProblemTasksInfo" resultType="cn.iocoder.zj.module.bpm.controller.admin.task.vo.task.BpmTaskRespVO">
    SELECT
        aha.act_id_ actId,
        aha.act_type_ actType,
        aha.start_time_ startTime,
        aha.end_time_ endTime,
        aha.duration_,
        aha.proc_inst_id_ processInstanceId,
        su.id assignee,
        su.nickname assigneeName,
        aha.PROC_DEF_ID_ processDefId,
        bte.result result,
        bte.reason
    FROM
        ACT_HI_ACTINST aha
            LEFT JOIN system_users su ON aha.ASSIGNEE_ = su.id
            LEFT JOIN bpm_task_ext bte ON bte.task_id = aha.task_id_
    WHERE
        aha.PROC_INST_ID_ = #{processInstanceId}
        and aha.act_id_ in
        <foreach collection="dictList" item="dict" open="(" close=")" separator=",">
            #{dict.value}
        </foreach>
    </select>
</mapper>