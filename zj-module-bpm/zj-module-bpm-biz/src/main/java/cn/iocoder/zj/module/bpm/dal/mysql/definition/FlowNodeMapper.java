package cn.iocoder.zj.module.bpm.dal.mysql.definition;


import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.bpm.api.task.dto.BpmTaskExtDTO;
import cn.iocoder.zj.module.bpm.controller.admin.definition.vo.node.FlowNodeBaseVO;
import cn.iocoder.zj.module.bpm.controller.admin.definition.vo.node.FlowNodeExportReqVO;
import cn.iocoder.zj.module.bpm.controller.admin.definition.vo.node.FlowNodePageReqVO;
import cn.iocoder.zj.module.bpm.controller.admin.task.vo.task.BpmProcessInstanceExtRespVo;
import cn.iocoder.zj.module.bpm.dal.dataobject.definition.FlowNodeDO;
import cn.iocoder.zj.module.bpm.dal.dataobject.task.ReportSubscriptionDTO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * 工作流表单的节点定义 Mapper
 *
 * <AUTHOR>
 */
@Mapper

public interface FlowNodeMapper extends BaseMapperX<FlowNodeDO> {

    default PageResult<FlowNodeDO> selectPage(FlowNodePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<FlowNodeDO>()
                .eqIfPresent(FlowNodeDO::getNodeId, reqVO.getNodeId())
                .likeIfPresent(FlowNodeDO::getNodeName, reqVO.getNodeName())
                .eqIfPresent(FlowNodeDO::getFormId, reqVO.getFromId())
                .eqIfPresent(FlowNodeDO::getModelId, reqVO.getModelId())
                .eqIfPresent(FlowNodeDO::getNodeFields, reqVO.getNodeFields())
                .betweenIfPresent(FlowNodeDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(FlowNodeDO::getId));
    }

    default List<FlowNodeDO> selectList(FlowNodeExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<FlowNodeDO>()
                .eqIfPresent(FlowNodeDO::getNodeId, reqVO.getNodeId())
                .likeIfPresent(FlowNodeDO::getNodeName, reqVO.getNodeName())
                .eqIfPresent(FlowNodeDO::getFormId, reqVO.getFromId())
                .eqIfPresent(FlowNodeDO::getModelId, reqVO.getModelId())
                .eqIfPresent(FlowNodeDO::getNodeFields, reqVO.getNodeFields())
                .betweenIfPresent(FlowNodeDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(FlowNodeDO::getId));
    }

    void deleteByModeId(@Param("modelId") String modelId);

    FlowNodeBaseVO selectByNodeId(@Param("flowNode") FlowNodeBaseVO flowNode);

    FlowNodeDO selectByProcessInstanceId(@Param("processInstanceId")String processInstanceId);

    String selectFinishByProcessInstanceId(@Param("processInstanceId")String processInstanceId,@Param("actType")String actType);

    String getNodeIdByProcessInstanceId(@Param("processInstanceId")String processInstanceId,@Param("userId")String userId);

    String getNameByDefinitionId(@Param("definitionId") String definitionId);

    String getNameByTaskId(@Param("taskId") String taskId);

    void updateModelNumberByModelId(@Param("number")String number, @Param("modelId")String modelId);

    String getNumberByInstanceId(@Param("processInstanceId")String processInstanceId);

    int getNumberCount(@Param("yearNumber")String yearNumber);

    void updateProcessNumberById(@Param("processInstanceId")String processInstanceId,@Param("processNumber") String processNumber);

//    BpmProcessInstanceExtDO getNumberList(@Param("taskId")List<String> list);

    List<Map<String, Object>> selectActinstOverTimeMap();

    void updateActinstOverTimeList(@Param("list")List<String> list);

    List<Map<String,String>> getProcessInstanceOvertimeMap(@Param("list")List<String> list);

    Long getTenantIdByUserId(@Param("userId")Long userId);

    void updateIsRead(@Param("taskIds")Collection<String> taskIds);

    String getModelNumberByModelId(@Param("modelId")String modelId);

    List<String> selectModelListByNumber(@Param("eventProcessNum")List<String> eventProcessNum);

    List<Map<String, String>> getWorkOrderSourceTyp(@Param("homologousUsers")List<Long> homologousUsers,
                                                    @Param("id")Long id,
                                                    @Param("role")String role);

    List<Map<String, String>> getCreateInWeek(@Param("dateStrList")List<String> dateStrList,
                                              @Param("homologousUsers")List<Long> homologousUsers,
                                              @Param("role")String role);

    Map<String, String> getPersonWorkOrderInfo( @Param("userId")Long userId,
                                                @Param("role")String role);

    Long countBackTack(@Param("homologousUsers")List<Long> homologousUsers,
                       @Param("id") Long id,
                       @Param("userRole")String userRole);

    String getModelIdByDefinitionId(@Param("definitionId")String definitionId);

    List<Map> getTasksCount(@Param("userName")String userName,
                            @Param("tenantId")Long tenantId,@Param("startTime")String startTime,@Param("endTime")String endTime);

    Map getSLAResponse(@Param("targetUsers")List<Long> targetUsers);

    List<BpmTaskExtDTO> getTodoTaskDO(@Param("targetUsers")List<Long> targetUsers,@Param("platformId")String platformId);

    List<Map> getFormByDefinitionId(@Param("DefinitionId")Set<String> DefinitionId);

    Map getOverTimeTaskCount(@Param("homologousUsers")List<Long> homologousUsers,
                             @Param("id")Long id,
                             @Param("userRole") String userRole);

    List<BpmProcessInstanceExtRespVo> getAllProcessInstance(@Param("homologousUsers")List<Long> homologousUsers,
                                                            @Param("id") Long id,
                                                            @Param("userRole")String userRole);
    @DS("doris")
    Map<String,Object> getHzbAlarmByIds(@Param("requestIds")List<Integer> requestIds);

    Map<String, Object> getCloudAlarmByIds(@Param("cloudAlarm")List<Integer> cloudAlarm);

    List<String> getDefinitionIdByFormId(@Param("formId")Long formId);

    void updateNodeBatchByNodeId(@Param("flowNodeDOList")List<FlowNodeDO> flowNodeDOList);

//    BpmFormDO getByName(@Param("name")String name);

    @TenantIgnore
    List<Map> getMonitorAssetList(@Param("platformId") List<String> platformId);

    String getProcessInstanceStartId(@Param("instanceId") String instanceId);

    String getFormFieldsByDefinitionId(@Param("definitionId")String definitionId);

    String getFormVariablesByInstanceId(@Param("instanceId")String instanceId);

    Integer selectRoleExistence();

    List<ReportSubscriptionDTO> getRoleExistence();
    @TenantIgnore
    List<Map> getMonitorAssetLists(@Param("platformId")List<String> list);
}
