package cn.iocoder.zj.module.bpm.service.definition;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.bpm.controller.admin.definition.vo.node.*;
import cn.iocoder.zj.module.bpm.dal.dataobject.definition.FlowNodeDO;
import org.dom4j.DocumentException;

import java.util.*;
import javax.validation.*;


/**
 * 工作流表单的节点定义 Service 接口
 *
 * <AUTHOR>
 */
public interface FlowNodeService {

    /**
     * 创建工作流表单的节点定义
     *
     * @param list 创建信息
     * @return 编号
     */
    Boolean createFlowNode(@Valid List<FlowNodeDO> list);

    /**
     * 更新工作流表单的节点定义
     *
     * @param flowNodeDO 创建信息
     */
    void updateFlowNode(@Valid FlowNodeDO flowNodeDO) throws DocumentException;

    /**
     * 删除工作流表单的节点定义
     *
     * @param id 编号
     */
    void deleteFlowNode(Long id);

    /**
     * 获得工作流表单的节点定义
     *
     * @return 工作流表单的节点定义
     */
    FlowNodeDO getFlowNode(FlowNodeBaseVO nodeBaseVO) throws DocumentException;

    /**
     * 获得工作流表单的节点定义列表
     *
     * @param ids 编号
     * @return 工作流表单的节点定义列表
     */
    List<FlowNodeDO> getFlowNodeList(Collection<Long> ids);

    /**
     * 获得工作流表单的节点定义分页
     *
     * @param pageReqVO 分页查询
     * @return 工作流表单的节点定义分页
     */
    PageResult<FlowNodeDO> getFlowNodePage(FlowNodePageReqVO pageReqVO);


    FlowNodeDO getNodeForm(String processInstanceId);

}
