package cn.iocoder.zj.module.bpm.convert.definition;

import java.util.*;

import cn.iocoder.zj.framework.common.pojo.PageResult;

import cn.iocoder.zj.module.bpm.controller.admin.definition.vo.node.FlowNodeCreateReqVO;
import cn.iocoder.zj.module.bpm.controller.admin.definition.vo.node.FlowNodeRespVO;
import cn.iocoder.zj.module.bpm.controller.admin.definition.vo.node.FlowNodeUpdateReqVO;
import cn.iocoder.zj.module.bpm.dal.dataobject.definition.FlowNodeDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * 工作流表单的节点定义 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface FlowNodeConvert {

    FlowNodeConvert INSTANCE = Mappers.getMapper(FlowNodeConvert.class);

    FlowNodeDO convert(FlowNodeCreateReqVO bean);

    FlowNodeDO convert(FlowNodeUpdateReqVO bean);

    FlowNodeRespVO convert(FlowNodeDO bean);

    List<FlowNodeRespVO> convertList(List<FlowNodeDO> list);

    PageResult<FlowNodeRespVO> convertPage(PageResult<FlowNodeDO> page);


    List<FlowNodeRespVO> convertList04(List<FlowNodeRespVO> list);
}
