package cn.iocoder.zj.module.bpm.service.definition;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.util.object.PageUtils;
import cn.iocoder.zj.framework.flowable.core.util.BpmnModelUtils;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.bpm.controller.admin.definition.vo.process.BpmProcessDefinitionListReqVO;
import cn.iocoder.zj.module.bpm.controller.admin.definition.vo.process.BpmProcessDefinitionPageItemRespVO;
import cn.iocoder.zj.module.bpm.controller.admin.definition.vo.process.BpmProcessDefinitionPageReqVO;
import cn.iocoder.zj.module.bpm.controller.admin.definition.vo.process.BpmProcessDefinitionRespVO;
import cn.iocoder.zj.module.bpm.convert.definition.BpmProcessDefinitionConvert;
import cn.iocoder.zj.module.bpm.dal.dataobject.definition.BpmFormDO;
import cn.iocoder.zj.module.bpm.dal.dataobject.definition.BpmProcessDefinitionExtDO;
import cn.iocoder.zj.module.bpm.dal.dataobject.definition.FlowNodeDO;
import cn.iocoder.zj.module.bpm.dal.mysql.definition.BpmProcessDefinitionExtMapper;
import cn.iocoder.zj.module.bpm.dal.mysql.definition.FlowNodeMapper;
import cn.iocoder.zj.module.bpm.service.definition.dto.BpmProcessDefinitionCreateReqDTO;
import cn.iocoder.zj.module.system.api.dict.DictDataApi;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.common.engine.impl.db.SuspensionState;
import org.flowable.common.engine.impl.util.io.BytesStreamSource;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.repository.*;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.*;
import static cn.iocoder.zj.module.bpm.enums.ErrorCodeConstants.*;
import static java.util.Collections.emptyList;

/**
 * 流程定义实现
 * 主要进行 Flowable {@link ProcessDefinition} 和 {@link Deployment} 的维护
 *
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class BpmProcessDefinitionServiceImpl implements BpmProcessDefinitionService {

    private static final String BPMN_FILE_SUFFIX = ".bpmn";
    //可能需要请求接口的组件类型
    private static final List<String> INTERFACE_REQUEST_TYPE = Arrays.asList("redio","checkbox","select","cascader","tree");

    @Resource
    private RepositoryService repositoryService;

    @Resource
    private BpmProcessDefinitionExtMapper processDefinitionMapper;

    @Resource
    private BpmFormService formService;

    @Resource
    private FlowNodeMapper flowNodeMapper;
    @Resource
    private DictDataApi dictDataApi;

    @Resource AdminUserApi adminUserApi;

    @Override
    public ProcessDefinition getProcessDefinition(String id) {
        return repositoryService.getProcessDefinition(id);
    }

    @Override
    public ProcessDefinition getProcessDefinition2(String id) {
        return repositoryService.createProcessDefinitionQuery().processDefinitionId(id).singleResult();
    }

    @Override
    public ProcessDefinition getProcessDefinitionByDeploymentId(String deploymentId) {
        if (StrUtil.isEmpty(deploymentId)) {
            return null;
        }
        return repositoryService.createProcessDefinitionQuery().deploymentId(deploymentId).singleResult();
    }

    @Override
    public List<ProcessDefinition> getProcessDefinitionListByDeploymentIds(Set<String> deploymentIds) {
        if (CollUtil.isEmpty(deploymentIds)) {
            return emptyList();
        }
        return repositoryService.createProcessDefinitionQuery().deploymentIds(deploymentIds).list();
    }

    @Override
    public ProcessDefinition getActiveProcessDefinition(String key) {
        return repositoryService.createProcessDefinitionQuery().processDefinitionKey(key).active().singleResult();
    }

    @Override
    public List<Deployment> getDeployments(Set<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return emptyList();
        }
        List<Deployment> list = new ArrayList<>(ids.size());
        for (String id : ids) {
            addIfNotNull(list, getDeployment(id));
        }
        return list;
    }

    @Override
    public Deployment getDeployment(String id) {
        if (StrUtil.isEmpty(id)) {
            return null;
        }
        return repositoryService.createDeploymentQuery().deploymentId(id).singleResult();
    }

    @Override
    public BpmnModel getBpmnModel(String processDefinitionId) {
        String modelId=flowNodeMapper.getModelIdByDefinitionId(processDefinitionId);
        return getBpmnModelById(modelId);

//        return repositoryService.getBpmnModel(processDefinitionId);
    }

    @Override
    public BpmProcessDefinitionRespVO getEventProcessDefinition(String processNumberPrefix) {
        List<String> eventProcessNum = dictDataApi.getDictDataListByType(processNumberPrefix).getData();
        if(eventProcessNum.size()==0){
            throw exception(PROCESS_INSTANCE_CANCEL_FAIL_NOT_EXISTS);
        }
        //获取问题工单
        List<String> model = flowNodeMapper.selectModelListByNumber(eventProcessNum);
        if(model.size()==0){
            throw exception(PROCESS_INSTANCE_CANCEL_FAIL_NOT_EXISTS);
        }
        //获取流程
        List<BpmProcessDefinitionExtDO> processDefinitionDOs = processDefinitionMapper.selectListByModelIds(model);
        Map<String, BpmProcessDefinitionExtDO> processDefinitionDOMap = convertMap(processDefinitionDOs,
                BpmProcessDefinitionExtDO::getProcessDefinitionId);
        ProcessDefinitionQuery definitionQuery = repositoryService.createProcessDefinitionQuery();
        Set<String> processDefinitionIds = processDefinitionDOMap.keySet();
        List<ProcessDefinition> processDefinitions = definitionQuery.processDefinitionIds(processDefinitionIds).active().list();
        LambdaQueryWrapperX<FlowNodeDO> lqw = new LambdaQueryWrapperX<>();
        lqw.inIfPresent(FlowNodeDO::getProcessDefinitionId,processDefinitionIds)
                .eq(FlowNodeDO::getType,"startEvent");
        List<FlowNodeDO> flowNodeDOList = flowNodeMapper.selectList(lqw);
        //筛选初始节点信息
        for (String key : processDefinitionDOMap.keySet()) {
            BpmProcessDefinitionExtDO bpmProcessDefinitionExtDO = processDefinitionDOMap.get(key);
            List<String> formFields = bpmProcessDefinitionExtDO.getFormFields();
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                for (int i = 0; i < formFields.size(); i++) {
                    JsonNode aNode = objectMapper.readTree(formFields.get(i));
                    String field = aNode.get("field").asText();
                    String type = aNode.get("type").asText();
                    System.out.println(aNode.get("title").asText());
//                    if(INTERFACE_REQUEST_TYPE.contains(type)&& StringUtils.isNotBlank(aNode.get("effect").toString())){
//                        JSONObject filedJsonObj = JSONObject.parseObject(aNode.get("effect").toString());
//                        JSONObject fetch = new JSONObject();
//                        JSONObject headers = new JSONObject();
//                        headers.put("Authorization","Bearer "+accessToken);
//                        headers.put("token",accessToken);
//                        fetch.put("headers", JSONUtils.toJSONString(headers));
//                        filedJsonObj.put("fetch",fetch);
//                        ObjectNode aObjectNode = (ObjectNode) aNode;
//                        aObjectNode.put("effect", JSONUtils.toJSONString(filedJsonObj));
//                    }
                    for (FlowNodeDO flowNodeDO : flowNodeDOList) {
                        List<String> nodeFields = flowNodeDO.getNodeFields();
                        for (String bJson : nodeFields) {
                            JsonNode bNode = objectMapper.readTree(bJson);
                            if (field.equals(bNode.get("field").asText())) {
                                ObjectNode props = objectMapper.createObjectNode();
                                //
                                Boolean required  = Boolean.valueOf(String.valueOf(bNode.get("$required")));
                                Boolean hidden = Boolean.valueOf(String.valueOf(bNode.get("hidden")));
                                Boolean disable =Boolean.valueOf(bNode.get("disabled")!=null?String.valueOf(bNode.get("disabled")):"false");
                                props.put("required",required);
                                props.put("hidden",hidden);
                                props.put("disabled", disable);
                                //
                                ObjectNode aObjectNode = (ObjectNode) aNode;
                                aObjectNode.put("$required", bNode.get("$required"));
                                aObjectNode.put("hidden", bNode.get("hidden"));
                                ObjectNode propsNode = (ObjectNode)aObjectNode.get("props");
                                if(propsNode == null){
                                    aObjectNode.put("props",props);
                                }else {
                                    if(propsNode.get("disabled")==null){
                                        propsNode.put("disabled",disable);
                                    }
                                    if(propsNode.get("hidden")==null){
                                        propsNode.put("hidden",hidden);
                                    }
                                    if(propsNode.get("required")==null){
                                        propsNode.put("required",required);
                                    }
                                }
                                break;
                            }
                        }
                    }
                    formFields.set(i, aNode.toString());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            bpmProcessDefinitionExtDO.setFormFields(formFields);
        }
        // 执行查询，并返回
        return BpmProcessDefinitionConvert.INSTANCE.convertList3(processDefinitions, processDefinitionDOMap).get(0);
    }

    @Override
    public String createProcessDefinition(@Valid BpmProcessDefinitionCreateReqDTO createReqDTO) {
        LoginUser currentUser = SecurityFrameworkUtils.getLoginUser();
        // 创建 Deployment 部署
        Deployment deploy = repositoryService.createDeployment()
                .key(createReqDTO.getKey()).name(createReqDTO.getName()).category(createReqDTO.getCategory())
                .addBytes(createReqDTO.getKey() + BPMN_FILE_SUFFIX, createReqDTO.getBpmnBytes())
                .deploy();

        // 设置 ProcessDefinition 的 category 分类
        ProcessDefinition definition = repositoryService.createProcessDefinitionQuery()
                .deploymentId(deploy.getId()).singleResult();
        repositoryService.setProcessDefinitionCategory(definition.getId(), createReqDTO.getCategory());
        // 注意 1，ProcessDefinition 的 key 和 name 是通过 BPMN 中的 <bpmn2:process /> 的 id 和 name 决定
        // 注意 2，目前该项目的设计上，需要保证 Model、Deployment、ProcessDefinition 使用相同的 key，保证关联性。
        //          否则，会导致 ProcessDefinition 的分页无法查询到。
        if (!Objects.equals(definition.getKey(), createReqDTO.getKey())) {
            throw exception(PROCESS_DEFINITION_KEY_NOT_MATCH, createReqDTO.getKey(), definition.getKey());
        }
        if (!Objects.equals(definition.getName(), createReqDTO.getName())) {
            throw exception(PROCESS_DEFINITION_NAME_NOT_MATCH, createReqDTO.getName(), definition.getName());
        }

        // 插入拓展表
        BpmProcessDefinitionExtDO definitionDO = BpmProcessDefinitionConvert.INSTANCE.convert2(createReqDTO)
                .setProcessDefinitionId(definition.getId());
        definitionDO.setTenantId(currentUser.getTenantId());
        definitionDO.setProcessKey(createReqDTO.getKey());
        processDefinitionMapper.insert(definitionDO);
        return definition.getId();
    }

    @Override
    public void updateProcessDefinitionState(String id, Integer state) {
        // 激活
        if (Objects.equals(SuspensionState.ACTIVE.getStateCode(), state)) {
            repositoryService.activateProcessDefinitionById(id, false, null);
            return;
        }
        // 挂起
        if (Objects.equals(SuspensionState.SUSPENDED.getStateCode(), state)) {
            // suspendProcessInstances = false，进行中的任务，不进行挂起。
            // 原因：只要新的流程不允许发起即可，老流程继续可以执行。
            repositoryService.suspendProcessDefinitionById(id, false, null);
            return;
        }
        log.error("[updateProcessDefinitionState][流程定义({}) 修改未知状态({})]", id, state);
    }

    @Override
    public String getProcessDefinitionBpmnXML(String id) {
        BpmnModel bpmnModel = repositoryService.getBpmnModel(id);
        if (bpmnModel == null) {
            return null;
        }
        BpmnXMLConverter converter = new BpmnXMLConverter();
        return StrUtil.utf8Str(converter.convertToXML(bpmnModel));
    }

    @Override
    public boolean isProcessDefinitionEquals(@Valid BpmProcessDefinitionCreateReqDTO createReqDTO) {
        // 校验 name、description 是否更新
        ProcessDefinition oldProcessDefinition = getActiveProcessDefinition(createReqDTO.getKey());
        if (oldProcessDefinition == null) {
            return false;
        }
        BpmProcessDefinitionExtDO oldProcessDefinitionExt = getProcessDefinitionExt(oldProcessDefinition.getId());
        if (!StrUtil.equals(createReqDTO.getName(), oldProcessDefinition.getName())
                || !StrUtil.equals(createReqDTO.getDescription(), oldProcessDefinitionExt.getDescription())
                || !StrUtil.equals(createReqDTO.getCategory(), oldProcessDefinition.getCategory())) {
            return false;
        }
        // 校验 form 信息是否更新
        if (!ObjectUtil.equal(createReqDTO.getFormType(), oldProcessDefinitionExt.getFormType())
                || !ObjectUtil.equal(createReqDTO.getFormId(), oldProcessDefinitionExt.getFormId())
                || !ObjectUtil.equal(createReqDTO.getFormConf(), oldProcessDefinitionExt.getFormConf())
                || !ObjectUtil.equal(createReqDTO.getFormFields(), oldProcessDefinitionExt.getFormFields())
                || !ObjectUtil.equal(createReqDTO.getFormCustomCreatePath(), oldProcessDefinitionExt.getFormCustomCreatePath())
                || !ObjectUtil.equal(createReqDTO.getFormCustomViewPath(), oldProcessDefinitionExt.getFormCustomViewPath())) {
            return false;
        }
        // 校验 BPMN XML 信息
        BpmnModel newModel = buildBpmnModel(createReqDTO.getBpmnBytes());
        BpmnModel oldModel = getBpmnModel(oldProcessDefinition.getId());
        // 对比字节变化
        if (!BpmnModelUtils.equals(oldModel, newModel)) {
            return false;
        }
        // 最终发现都一致，则返回 true
        return true;
    }

    public BpmnModel getBpmnModelById(String id) {
        byte[] bpmnBytes = repositoryService.getModelEditorSource(id);
        if (ArrayUtil.isEmpty(bpmnBytes)) {
            return null;
        }
        BpmnXMLConverter converter = new BpmnXMLConverter();
        return converter.convertToBpmnModel(new BytesStreamSource(bpmnBytes), true, true);
    }

    /**
     * 构建对应的 BPMN Model
     *
     * @param bpmnBytes 原始的 BPMN XML 字节数组
     * @return BPMN Model
     */
    private  BpmnModel buildBpmnModel(byte[] bpmnBytes) {
        // 转换成 BpmnModel 对象
        BpmnXMLConverter converter = new BpmnXMLConverter();
        return converter.convertToBpmnModel(new BytesStreamSource(bpmnBytes), true, true);
    }

    @Override
    public BpmProcessDefinitionExtDO getProcessDefinitionExt(String id) {
        return processDefinitionMapper.selectByProcessDefinitionId(id);
    }

    @Override
    public List<BpmProcessDefinitionRespVO> getProcessDefinitionList(BpmProcessDefinitionListReqVO listReqVO, String accessToken) {
        LoginUser currentUser = SecurityFrameworkUtils.getLoginUser();
        // 拼接查询条件
        ProcessDefinitionQuery definitionQuery = repositoryService.createProcessDefinitionQuery();
        if (Objects.equals(SuspensionState.SUSPENDED.getStateCode(), listReqVO.getSuspensionState())) {
            definitionQuery.suspended();
        } else if (Objects.equals(SuspensionState.ACTIVE.getStateCode(), listReqVO.getSuspensionState())) {
            definitionQuery.active();
        }
        // 执行查询
        List<ProcessDefinition> processDefinitions = definitionQuery.list();
        if (CollUtil.isEmpty(processDefinitions)) {
            return Collections.emptyList();
        }
        List<String> keyList = processDefinitions.stream()
                .filter(processDefinition -> processDefinition.getCategory().equals("5"))
                .map(ProcessDefinition::getKey)
                .collect(Collectors.toList());
        // 获得 BpmProcessDefinitionDO Map
        List<BpmProcessDefinitionExtDO> processDefinitionDOs = processDefinitionMapper.selectListByProcessDefinitionIds(
                convertList(processDefinitions, ProcessDefinition::getId),currentUser.getTenantId(),keyList);
        Map<String, BpmProcessDefinitionExtDO> processDefinitionDOMap = convertMap(processDefinitionDOs,
                BpmProcessDefinitionExtDO::getProcessDefinitionId);

        processDefinitions = processDefinitions.stream()
                .filter(processDefinition -> processDefinitionDOs.stream()
                        .anyMatch(bpmProcessDefinition -> bpmProcessDefinition.getProcessDefinitionId().equals(processDefinition.getId())))
                .collect(Collectors.toList());
        //
        Set<String> processDefinitionIds = processDefinitionDOMap.keySet();
        LambdaQueryWrapperX<FlowNodeDO> lqw = new LambdaQueryWrapperX<>();
        lqw.inIfPresent(FlowNodeDO::getProcessDefinitionId,processDefinitionIds)
                .eq(FlowNodeDO::getType,"startEvent");
        List<FlowNodeDO> flowNodeDOList = flowNodeMapper.selectList(lqw);
        //筛选初始节点信息
        for (String key : processDefinitionDOMap.keySet()) {
            BpmProcessDefinitionExtDO bpmProcessDefinitionExtDO = processDefinitionDOMap.get(key);
            List<String> formFields = bpmProcessDefinitionExtDO.getFormFields();
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                for (int i = 0; i < formFields.size(); i++) {
                    JsonNode aNode = objectMapper.readTree(formFields.get(i));
                    String field = aNode.get("field").asText();
                    String type = aNode.get("type").asText();
                    System.out.println(aNode.get("title").asText());
                    for (FlowNodeDO flowNodeDO : flowNodeDOList) {
                        if (bpmProcessDefinitionExtDO.getModelId().equals(flowNodeDO.getModelId())) {
                            List<String> nodeFields = flowNodeDO.getNodeFields();
                            for (String bJson : nodeFields) {
                                JsonNode bNode = objectMapper.readTree(bJson);
                                if (field.equals(bNode.get("field").asText())) {
                                    ObjectNode props = objectMapper.createObjectNode();
                                    //
                                    Boolean required = Boolean.valueOf(String.valueOf(bNode.get("$required")));
                                    Boolean hidden = Boolean.valueOf(String.valueOf(bNode.get("hidden")));
                                    Boolean disable = Boolean.valueOf(bNode.get("disabled") != null ? String.valueOf(bNode.get("disabled")) : "false");
                                    props.put("required", required);
                                    props.put("hidden", hidden);
                                    props.put("disabled", disable);
                                    //
                                    ObjectNode aObjectNode = (ObjectNode) aNode;
                                    aObjectNode.put("$required", bNode.get("$required"));
                                    aObjectNode.put("hidden", bNode.get("hidden"));
                                    ObjectNode propsNode = (ObjectNode) aObjectNode.get("props");
                                    if (propsNode == null) {
                                        aObjectNode.put("props", props);
                                    } else {
                                        if (propsNode.get("disabled") == null) {
                                            propsNode.put("disabled", disable);
                                        }
                                        if (propsNode.get("hidden") == null) {
                                            propsNode.put("hidden", hidden);
                                        }
                                        if (propsNode.get("required") == null) {
                                            propsNode.put("required", required);
                                        }
                                    }
                                    break;
                                }
                            }
                        }
                        formFields.set(i, aNode.toString());
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            bpmProcessDefinitionExtDO.setFormFields(formFields);
        }

        // 执行查询，并返回
        return BpmProcessDefinitionConvert.INSTANCE.convertList3(processDefinitions, processDefinitionDOMap);
    }

    @Override
    public PageResult<BpmProcessDefinitionPageItemRespVO> getProcessDefinitionPage(BpmProcessDefinitionPageReqVO pageVO) {
        LoginUser currentUser = SecurityFrameworkUtils.getLoginUser();
        ProcessDefinitionQuery definitionQuery = repositoryService.createProcessDefinitionQuery();
        if (StrUtil.isNotBlank(pageVO.getKey())) {
            definitionQuery.processDefinitionKey(pageVO.getKey());
        }

        // 执行查询
        List<ProcessDefinition> processDefinitions = definitionQuery.orderByProcessDefinitionVersion().desc()
                .listPage(PageUtils.getStart(pageVO), pageVO.getPageSize());

        if (CollUtil.isEmpty(processDefinitions)) {
            return new PageResult<>(emptyList(), definitionQuery.count());
        }
        // 获得 Deployment Map
        Set<String> deploymentIds = new HashSet<>();
        processDefinitions.forEach(definition -> addIfNotNull(deploymentIds, definition.getDeploymentId()));
        Map<String, Deployment> deploymentMap = getDeploymentMap(deploymentIds);
        List<String> keyList = processDefinitions.stream()
                .filter(processDefinition -> processDefinition.getCategory().equals("5"))
                .map(ProcessDefinition::getKey)
                .collect(Collectors.toList());
        // 获得 BpmProcessDefinitionDO Map
        List<BpmProcessDefinitionExtDO> processDefinitionDOs = processDefinitionMapper.selectListByProcessDefinitionIds(
                convertList(processDefinitions, ProcessDefinition::getId),currentUser.getTenantId(),keyList);
        Map<String, BpmProcessDefinitionExtDO> processDefinitionDOMap = convertMap(processDefinitionDOs,
                BpmProcessDefinitionExtDO::getProcessDefinitionId);

        // 获得 Form Map
        Set<Long> formIds = convertSet(processDefinitionDOs, BpmProcessDefinitionExtDO::getFormId);
        Map<Long, BpmFormDO> formMap = formService.getFormMap(formIds);

        // 拼接结果
        long definitionCount = definitionQuery.count();
        return new PageResult<>(BpmProcessDefinitionConvert.INSTANCE.convertList(processDefinitions, deploymentMap,
                processDefinitionDOMap, formMap), definitionCount);
    }

}
