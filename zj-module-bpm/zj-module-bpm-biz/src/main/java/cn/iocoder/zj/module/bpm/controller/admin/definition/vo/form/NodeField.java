package cn.iocoder.zj.module.bpm.controller.admin.definition.vo.form;

public class NodeField {


    //id
    private String field;
    //名称
    private String title;

    private Boolean $required;
    private Boolean hidden;
    private Boolean disabled;

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Boolean get$required() {
        return $required;
    }

    public void set$required(Boolean $required) {
        this.$required = $required;
    }

    public Boolean getHidden() {
        return hidden;
    }

    public void setHidden(Boolean hidden) {
        this.hidden = hidden;
    }

    public Boolean getDisabled() {
        return disabled;
    }

    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }
}
