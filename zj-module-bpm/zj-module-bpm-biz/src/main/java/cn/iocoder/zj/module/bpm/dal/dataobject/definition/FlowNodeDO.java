package cn.iocoder.zj.module.bpm.dal.dataobject.definition;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.*;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.List;

/**
 * 工作流表单的节点定义 DO
 *
 * <AUTHOR>
 */
@TableName(value = "bpm_flow_node",autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlowNodeDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 节点id
     */
    private String nodeId;
    /**
     * 节点名称
     */
    private String nodeName;
    /**
     * 表单id
     */
    private Long formId;
    /**
     * 模型id
     */
    private String modelId;
    /**
     * 节点表单项的配置
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> nodeFields;

    /**
     * 流程定义编号
     *
     * 关联 ProcessDefinition 的 id 属性
     */
    private String processDefinitionId;

    //节点类型
    private String type;
    //是否设置超时，0否，1是
    private Integer isTimeout;
    //超时时间
    private Long timeoutTime;


}
