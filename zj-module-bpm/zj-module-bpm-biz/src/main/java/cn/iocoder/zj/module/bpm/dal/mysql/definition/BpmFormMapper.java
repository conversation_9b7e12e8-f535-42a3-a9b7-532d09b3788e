package cn.iocoder.zj.module.bpm.dal.mysql.definition;



import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.QueryWrapperX;
import cn.iocoder.zj.module.bpm.controller.admin.definition.vo.form.BpmFormPageReqVO;
import cn.iocoder.zj.module.bpm.dal.dataobject.definition.BpmFormDO;
import jodd.util.StringUtil;
import org.apache.ibatis.annotations.Mapper;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

/**
 * 动态表单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BpmFormMapper extends BaseMapperX<BpmFormDO> {

    default PageResult<BpmFormDO> selectPage(BpmFormPageReqVO reqVO) {
        QueryWrapperX<BpmFormDO> wrapperX = new QueryWrapperX<BpmFormDO>()
                .eqIfPresent("tenant_id", reqVO.getTenantId())
                .eqIfPresent("status", reqVO.getStatus())
                .likeIfPresent("name", reqVO.getName());


        if (StringUtil.isNotEmpty(reqVO.getStartTime()) && StringUtil.isNotEmpty(reqVO.getEndTime())) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            try {
                date = sdf.parse(reqVO.getEndTime());
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(date);
                // 把日期设置为当天最后一秒
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                date = calendar.getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            wrapperX.betweenIfPresent("create_time", reqVO.getStartTime(), sdf.format(date));
        }


        if (StringUtil.isNotEmpty(reqVO.getSortDirection()) && reqVO.getSortDirection().equals("asc")){
            if (reqVO.getSortBy().equals("createTime")){
                wrapperX.orderByAsc("create_time");
            }
        }else {
            wrapperX.orderByDesc("create_time");
        }
        return selectPage(reqVO, wrapperX);
    }

}
