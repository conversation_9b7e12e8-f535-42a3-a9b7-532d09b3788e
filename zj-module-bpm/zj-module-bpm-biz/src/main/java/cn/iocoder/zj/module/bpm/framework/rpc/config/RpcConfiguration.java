package cn.iocoder.zj.module.bpm.framework.rpc.config;

import cn.iocoder.zj.module.bpm.api.task.BpmProcessInstanceApi;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.system.api.dept.DeptApi;
import cn.iocoder.zj.module.system.api.dept.PostApi;
import cn.iocoder.zj.module.system.api.dict.DictDataApi;
import cn.iocoder.zj.module.system.api.mail.MailSendApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.sms.SmsSendApi;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.wechat.WeChatSendApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {RoleApi.class,
        DeptApi.class,
        PostApi.class,
        AdminUserApi.class,
        SmsSendApi.class,
        DictDataApi.class,
        AlarmConfigApi.class,
        PlatformconfigApi.class,
        BpmProcessInstanceApi.class,WeChatSendApi.class,MailSendApi.class})
public class RpcConfiguration {
}
