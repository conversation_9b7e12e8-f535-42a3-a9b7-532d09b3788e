package cn.iocoder.zj.module.bpm.dal.mysql.definition;


import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.module.bpm.controller.admin.definition.vo.group.BpmUserGroupPageReqVO;
import cn.iocoder.zj.module.bpm.dal.dataobject.definition.BpmUserGroupDO;
import jodd.util.StringUtil;
import org.apache.ibatis.annotations.Mapper;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

/**
 * 用户组 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BpmUserGroupMapper extends BaseMapperX<BpmUserGroupDO> {

    default PageResult<BpmUserGroupDO> selectPage(BpmUserGroupPageReqVO reqVO) {

        LambdaQueryWrapperX<BpmUserGroupDO> wrapperX = new LambdaQueryWrapperX<BpmUserGroupDO>()
                .likeIfPresent(BpmUserGroupDO::getName, reqVO.getName())
                .eqIfPresent(BpmUserGroupDO::getTenantId, reqVO.getTenantId())
                .eqIfPresent(BpmUserGroupDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(BpmUserGroupDO::getCreateTime, reqVO.getCreateTime());

        if (StringUtil.isNotEmpty(reqVO.getStartTime()) && StringUtil.isNotEmpty(reqVO.getEndTime())) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            try {
                date = sdf.parse(reqVO.getEndTime());
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(date);
                // 把日期设置为当天最后一秒
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                date = calendar.getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            wrapperX.betweenIfPresent(BpmUserGroupDO::getCreateTime, reqVO.getStartTime(), sdf.format(date));
        }

        if (StringUtil.isNotEmpty(reqVO.getSortDirection()) && reqVO.getSortDirection().equals("asc")){
            if (reqVO.getSortBy().equals("createTime")){
                wrapperX.orderByAsc(BpmUserGroupDO::getCreateTime);
            }
        }else {
            wrapperX.orderByDesc(BpmUserGroupDO::getCreateTime);
        }
        return selectPage(reqVO, wrapperX);
    }

    default List<BpmUserGroupDO> selectListByStatus(Integer status,Long tenantId) {
        return selectList(new LambdaQueryWrapperX<BpmUserGroupDO>()
                .eqIfPresent(BpmUserGroupDO::getStatus,status)
                .eqIfPresent(BpmUserGroupDO::getTenantId,tenantId));
    }

}
