package cn.iocoder.zj.module.bpm.service.definition;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.module.bpm.controller.admin.definition.vo.form.Field;
import cn.iocoder.zj.module.bpm.controller.admin.definition.vo.form.NodeField;
import cn.iocoder.zj.module.bpm.controller.admin.definition.vo.model.BpmModelRespVO;
import cn.iocoder.zj.module.bpm.controller.admin.definition.vo.node.FlowNodeBaseVO;
import cn.iocoder.zj.module.bpm.controller.admin.definition.vo.node.FlowNodePageReqVO;
import cn.iocoder.zj.module.bpm.controller.admin.definition.vo.node.FlowNodeRespVO;
import cn.iocoder.zj.module.bpm.convert.definition.BpmModelConvert;
import cn.iocoder.zj.module.bpm.dal.dataobject.definition.BpmFormDO;
import cn.iocoder.zj.module.bpm.dal.dataobject.definition.BpmProcessDefinitionExtDO;
import cn.iocoder.zj.module.bpm.dal.dataobject.definition.FlowNodeDO;
import cn.iocoder.zj.module.bpm.dal.dataobject.task.BpmTaskExtDO;
import cn.iocoder.zj.module.bpm.dal.mysql.definition.BpmFormMapper;
import cn.iocoder.zj.module.bpm.dal.mysql.definition.BpmProcessDefinitionExtMapper;
import cn.iocoder.zj.module.bpm.dal.mysql.definition.FlowNodeMapper;
import cn.iocoder.zj.module.bpm.dal.mysql.task.BpmTaskExtMapper;
import cn.iocoder.zj.module.bpm.service.definition.dto.BpmModelMetaInfoRespDTO;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import org.dom4j.*;
import org.dom4j.tree.DefaultElement;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.repository.Model;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.bpm.enums.ErrorCodeConstants.*;
import static java.util.stream.Collectors.toList;


/**
 * 工作流表单的节点定义 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FlowNodeServiceImpl implements FlowNodeService {


    @Resource
    private FlowNodeMapper flowNodeMapper;

    @Resource
    private RepositoryService repositoryService;

    @Resource
    private BpmFormMapper bpmFormMapper;

    @Resource
    private BpmTaskExtMapper bpmTaskExtMapper;

    @Resource
    private BpmProcessDefinitionExtMapper bpmProcessDefinitionExtMapper;

    @Resource
    private BpmProcessDefinitionService processDefinitionService;

    @Override
    public Boolean createFlowNode(List<FlowNodeDO> list) {
        flowNodeMapper.insertBatch(list);
        // 返回
        return true;
    }

    @Override
    public void updateFlowNode(FlowNodeDO flowNodeDO) throws DocumentException {
        if (flowNodeDO.getIsTimeout()==null){
            throw exception(ISTIMEOUT_IS_REQUIRED);
        }
        //判断是否超时，如果为超时判断提醒提前时间
        if (flowNodeDO.getIsTimeout() == 1){
            if (flowNodeDO.getTimeoutTime()==null){
                throw exception(OUTTIME_IS_REQUIRED);
            }else if (flowNodeDO.getTimeoutTime()<1){
                throw exception(OUTTIME_GREATER_ONE);
            }
        }

        // 校验流程模型存在
        Model model = repositoryService.getModel(flowNodeDO.getModelId());
        if (model == null) {
            throw exception(MODEL_NOT_EXISTS);
        }
        //添加实例id
//        ProcessDefinition processDefinition = processDefinitionService.getProcessDefinitionByDeploymentId(model.getDeploymentId());
//        System.out.println(processDefinition.getId());
//        flowNodeDO.setProcessDefinitionId(processDefinition.getId());
        // 拼接 bpmn XML
        byte[] bpmnBytes = repositoryService.getModelEditorSource(flowNodeDO.getModelId()); //ACT _GE BYTEARRAY
        String xmlString=StrUtil.utf8Str(bpmnBytes);
        //将给定的String文本解析为XML文档并返回新创建的document
        Document document = DocumentHelper.parseText(xmlString);
        //获取根节点,在例子中就是responsedata节点
        Element roots = document.getRootElement();
        String startEventId="";
        String endEventId="";
        List root=roots.content();
        for (int i=0;i<root.size();i++){
            if (root.get(i).getClass().getName().equals("org.dom4j.tree.DefaultElement")){
                DefaultElement defaultElement=(DefaultElement)root.get(i);
                if (defaultElement.getQName().getName().equals("process")){
                    List nodeList=defaultElement.content();
                    for (int j=0;j<nodeList.size();j++){
                        DefaultElement node=(DefaultElement)nodeList.get(j);
                        if (node.getQName().getName().equals("startEvent")){
                            Attribute attribute=node.attribute("id");
                            startEventId=attribute.getValue();
                        }else if (node.getQName().getName().equals("endEvent")){
                            Attribute attribute=node.attribute("id");
                            endEventId=attribute.getValue();
                        }
                    }
                }
            }
        }
        //判断有无id
        if (flowNodeDO.getNodeId().equals(startEventId)){
            flowNodeDO.setType("startEvent");
        }else if (flowNodeDO.getNodeId().equals(endEventId)){
            flowNodeDO.setType("endEvent");
        }else {
            flowNodeDO.setType("userTask");
        }

        if (flowNodeDO.getId() == null) {
            //新增
            flowNodeMapper.insert(flowNodeDO);
        } else {
            //修改
            flowNodeMapper.updateById(flowNodeDO);
        }
    }

    @Override
    public void deleteFlowNode(Long id) {
        // 删除
        flowNodeMapper.deleteById(id);

    }



    @Override
    public FlowNodeDO getFlowNode(FlowNodeBaseVO nodeBaseVO) throws DocumentException {
        //版本信息如果没有则查询模型对应最新的版本信息

        FlowNodeDO flowNode = new FlowNodeDO();
        if (nodeBaseVO.getNodeId() != null) {
            LambdaQueryWrapperX<FlowNodeDO> nodeLqw = new LambdaQueryWrapperX<>();
            nodeLqw.eqIfPresent(FlowNodeDO::getNodeId, nodeBaseVO.getNodeId())
                    .eqIfPresent(FlowNodeDO::getModelId, nodeBaseVO.getModelId())
                    .eqIfPresent(FlowNodeDO::getProcessDefinitionId,nodeBaseVO.getProcessDefinitionId())
                    .eq(FlowNodeDO::getDeleted, 0);
            flowNode = flowNodeMapper.selectOne(nodeLqw);

        }

        if (flowNode == null) {
            //如果不存在，封装主表单信息返回
            String modelId = nodeBaseVO.getModelId();
            if (modelId == null) {
                throw exception(MODEL_ID_NOT_EXISTS);
            }
            Model model = repositoryService.getModel(modelId);
            BpmModelMetaInfoRespDTO metaInfo = JsonUtils.parseObject(model.getMetaInfo(), BpmModelMetaInfoRespDTO.class);
            //表单名称
            BpmFormDO bpmForm = bpmFormMapper.selectById(metaInfo.getFormId());
            if (bpmForm==null){
                throw exception(MODEL_NOT_CORRELATION_FORM);
            }
            Gson gson = new Gson();
            List<String> fields = bpmForm.getFields();
            ArrayList<String> nodeFields = new ArrayList<>();
            for (String field : fields) {
                Field result = gson.fromJson(field, Field.class);
                NodeField nodeField = gson.fromJson(field, NodeField.class);
                if (result != null) {
                    if (result.getProps()!=null && result.getProps().getDisabled()!=null){
                        nodeField.setDisabled(result.getProps().getDisabled());
                    }else {
                        nodeField.setDisabled(false);
                    }
                    nodeFields.add(gson.toJson(nodeField));
                }
            }
            FlowNodeDO flowNodeDO = new FlowNodeDO();
            flowNodeDO.setFormId(bpmForm.getId());
            flowNodeDO.setNodeFields(nodeFields);
            // 拼接 bpmn XML
            byte[] bpmnBytes = repositoryService.getModelEditorSource(modelId);  //ACT_GE_BYTEARRAY
            String xmlString=StrUtil.utf8Str(bpmnBytes);
            //将给定的String文本解析为XML文档并返回新创建的document
            Document document = DocumentHelper.parseText(xmlString);
            //获取根节点,在例子中就是responsedata节点
            Element roots = document.getRootElement();
            String startEventId="";
            String endEventId="";
            List root=roots.content();
            for (int i=0;i<root.size();i++){
                if (root.get(i).getClass().getName().equals("org.dom4j.tree.DefaultElement")){
                    DefaultElement defaultElement=(DefaultElement)root.get(i);
                    if (defaultElement.getQName().getName().equals("process")){
                        List nodeList=defaultElement.content();
                        for (int j=0;j<nodeList.size();j++){
                            DefaultElement node=(DefaultElement)nodeList.get(j);
                            if (node.getQName().getName().equals("startEvent")){
                                Attribute attribute=node.attribute("id");
                                startEventId=attribute.getValue();
                            }else if (node.getQName().getName().equals("endEvent")){
                                Attribute attribute=node.attribute("id");
                                endEventId=attribute.getValue();
                            }
                        }
                    }
                }
            }
            //判断有无id
            if (nodeBaseVO.getNodeId().equals(startEventId)){
                flowNodeDO.setType("startEvent");
            }else if (nodeBaseVO.getNodeId().equals(endEventId)){
                flowNodeDO.setType("endEvent");
            }else {
                flowNodeDO.setType("userTask");
            }
            return flowNodeDO;
        }
        return flowNode;
    }
//    public FlowNodeDO getFlowNode(FlowNodeBaseVO nodeBaseVO) throws DocumentException {
//        //版本信息如果没有则查询模型对应最新的版本信息
//
//        FlowNodeDO flowNode = new FlowNodeDO();
//        if (nodeBaseVO.getNodeId() != null) {
//            LambdaQueryWrapperX<FlowNodeDO> nodeLqw = new LambdaQueryWrapperX<>();
//            nodeLqw.eqIfPresent(FlowNodeDO::getNodeId, nodeBaseVO.getNodeId())
//                    .eqIfPresent(FlowNodeDO::getModelId, nodeBaseVO.getModelId())
//                    .eqIfPresent(FlowNodeDO::getProcessDefinitionId,nodeBaseVO.getProcessDefinitionId())
//                    .eq(FlowNodeDO::getDeleted, 0);
//            flowNode = flowNodeMapper.selectOne(nodeLqw);
//
//        }
//        //如果不存在，封装主表单信息返回
//        String modelId = nodeBaseVO.getModelId();
//        if (modelId == null) {
//            throw exception(MODEL_ID_NOT_EXISTS);
//        }
//        Model model = repositoryService.getModel(modelId);
//        BpmModelMetaInfoRespDTO metaInfo = JsonUtils.parseObject(model.getMetaInfo(), BpmModelMetaInfoRespDTO.class);
//        //表单名称
//        BpmFormDO bpmForm = bpmFormMapper.selectById(metaInfo.getFormId());
//        if (bpmForm==null){
//            throw exception(MODEL_NOT_CORRELATION_FORM);
//        }
//        if (flowNode == null) {
//            Gson gson = new Gson();
//            List<String> fields = bpmForm.getFields();
//            ArrayList<String> nodeFields = new ArrayList<>();
//            for (String field : fields) {
//                Field result = gson.fromJson(field, Field.class);
//                NodeField nodeField = gson.fromJson(field, NodeField.class);
//                if (result != null) {
//                    if (result.getProps()!=null && result.getProps().getDisabled()!=null){
//                        nodeField.setDisabled(result.getProps().getDisabled());
//                    }else {
//                        nodeField.setDisabled(false);
//                    }
//                    nodeFields.add(gson.toJson(nodeField));
//                }
//            }
//            FlowNodeDO flowNodeDO = new FlowNodeDO();
//            flowNodeDO.setFormId(bpmForm.getId());
//            flowNodeDO.setNodeFields(nodeFields);
//            // 拼接 bpmn XML
//            byte[] bpmnBytes = repositoryService.getModelEditorSource(modelId);  //ACT_GE_BYTEARRAY
//            String xmlString=StrUtil.utf8Str(bpmnBytes);
//            //将给定的String文本解析为XML文档并返回新创建的document
//            Document document = DocumentHelper.parseText(xmlString);
//            //获取根节点,在例子中就是responsedata节点
//            Element roots = document.getRootElement();
//            String startEventId="";
//            String endEventId="";
//            List root=roots.content();
//            for (int i=0;i<root.size();i++){
//                if (root.get(i).getClass().getName().equals("org.dom4j.tree.DefaultElement")){
//                    DefaultElement defaultElement=(DefaultElement)root.get(i);
//                    if (defaultElement.getQName().getName().equals("process")){
//                        List nodeList=defaultElement.content();
//                        for (int j=0;j<nodeList.size();j++){
//                            DefaultElement node=(DefaultElement)nodeList.get(j);
//                            if (node.getQName().getName().equals("startEvent")){
//                                Attribute attribute=node.attribute("id");
//                                startEventId=attribute.getValue();
//                            }else if (node.getQName().getName().equals("endEvent")){
//                                Attribute attribute=node.attribute("id");
//                                endEventId=attribute.getValue();
//                            }
//                        }
//                    }
//                }
//            }
//            //判断有无id
//            if (nodeBaseVO.getNodeId().equals(startEventId)){
//                flowNodeDO.setType("startEvent");
//            }else if (nodeBaseVO.getNodeId().equals(endEventId)){
//                flowNodeDO.setType("endEvent");
//            }else {
//                flowNodeDO.setType("userTask");
//            }
//            return flowNodeDO;
//        }else {
//            List<JSONObject> oldFieldInfo = new ArrayList<>();
//            //原节点中涉及的字段
//            List<String> oldFields = flowNode.getNodeFields();
//            //取出字段和字段名
//            oldFields.forEach(item->{
//                JSONObject  fieldObj = JSONObject.parseObject(item);
//                JSONObject target = new JSONObject();
//                target.put("field",fieldObj.getString("field"));
//                target.put("title",fieldObj.getString("title"));
//                oldFieldInfo.add(target);
//            });
//
//            List<String> newFields = bpmForm.getFields();
//            List<String> targetFields = new ArrayList<>();
//            //取出字段和字段名
//            newFields.forEach(a->{
//                JSONObject  objNew = JSONObject.parseObject(a);
//                JSONObject targetObj = new JSONObject(new LinkedHashMap<>());
//                targetObj.put("field",objNew.getString("field"));
//                targetObj.put("title",objNew.getString("title"));
//                targetObj.put("$required",false);
//                targetObj.put("hidden",false);
//                targetObj.put("disabled",false);
//                oldFields.forEach(b->{
//                    JSONObject  objOld = JSONObject.parseObject(b);
//                    if(objNew.getString("title").equals(objOld.getString("title"))){
//                        targetObj.put("$required",objOld.getBoolean("$required"));
//                        targetObj.put("hidden",objOld.getBoolean("hidden"));
//                        targetObj.put("disabled",objOld.getBoolean("disabled"));
//                    }
//                });
//                targetFields.add(JSONUtil.toJsonStr(targetObj));
//            });
//            flowNode.setNodeFields(targetFields);
//        }
//        return flowNode;
//    }

    @Override
    public List<FlowNodeDO> getFlowNodeList(Collection<Long> ids) {
        return flowNodeMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<FlowNodeDO> getFlowNodePage(FlowNodePageReqVO pageReqVO) {
        return flowNodeMapper.selectPage(pageReqVO);
    }

    @Override
    public FlowNodeDO getNodeForm(String processInstanceId) {
        //获取流程示例表当前流程数据 bpm_task_est
        LambdaQueryWrapperX<BpmTaskExtDO> lqw = new LambdaQueryWrapperX<>();
        lqw.eqIfPresent(BpmTaskExtDO::getProcessDefinitionId,processInstanceId)
                .eq(BpmTaskExtDO::getResult,1)
                .eq(BpmTaskExtDO::getDeleted,0);
        BpmTaskExtDO taskExtDO = bpmTaskExtMapper.selectOne(lqw);
        return null;
    }

}
