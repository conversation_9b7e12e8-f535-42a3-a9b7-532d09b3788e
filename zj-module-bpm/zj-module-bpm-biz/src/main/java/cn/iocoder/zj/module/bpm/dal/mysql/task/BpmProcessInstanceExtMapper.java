package cn.iocoder.zj.module.bpm.dal.mysql.task;

import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceMyPageReqVO;
import cn.iocoder.zj.module.bpm.dal.dataobject.task.BpmProcessInstanceExtDO;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

@Mapper
public interface BpmProcessInstanceExtMapper extends BaseMapperX<BpmProcessInstanceExtDO> {

    default PageResult<BpmProcessInstanceExtDO> selectPage(AdminUserRespDTO user, BpmProcessInstanceMyPageReqVO reqVO, Boolean isAdmin) {
        LambdaQueryWrapperX<BpmProcessInstanceExtDO> wrapper = new LambdaQueryWrapperX<>();
        if(StringUtils.isNotBlank(reqVO.getCategory())){
            wrapper.eqIfPresent(BpmProcessInstanceExtDO::getCategory,reqVO.getCategory() );
        }
        if(StringUtils.isNotBlank(reqVO.getName())){
            wrapper.likeIfPresent(BpmProcessInstanceExtDO::getName, reqVO.getName());
        }
        if(reqVO.getStatus()!=null){
            wrapper.eqIfPresent(BpmProcessInstanceExtDO::getStatus,reqVO.getStatus());
        }
        if(reqVO.getResult()!=null){
            if (reqVO.getResult() == 5){
                wrapper.eqIfPresent(BpmProcessInstanceExtDO::getResult, 4)
                        .apply("creator <> updater");
            }else if(reqVO.getResult() == 4){
                wrapper.eqIfPresent(BpmProcessInstanceExtDO::getResult, reqVO.getResult())
                        .apply("creator = updater");
            }else {
                wrapper.eqIfPresent(BpmProcessInstanceExtDO::getResult, reqVO.getResult());
            }
        }
        if(reqVO.getCreateTime()!=null && reqVO.getCreateTime().length>0){
            LocalDateTime[] createTimeArray = reqVO.getCreateTime();
            wrapper.betweenIfPresent(BaseDO::getCreateTime,createTimeArray[0],createTimeArray[1]);
        }
        wrapper.eqIfPresent(BpmProcessInstanceExtDO::getStartUserId, user.getId());
        if(StringUtils.isNotBlank(reqVO.getProcessNumber())) {
            wrapper.likeIfPresent(BpmProcessInstanceExtDO::getProcessNumber, reqVO.getProcessNumber());
        }
        if (jodd.util.StringUtil.isNotEmpty(reqVO.getStartTime()) && jodd.util.StringUtil.isNotEmpty(reqVO.getEndTime())) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            try {
                date = sdf.parse(reqVO.getEndTime());
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(date);
                // 把日期设置为当天最后一秒
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                date = calendar.getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            wrapper.betweenIfPresent(BpmProcessInstanceExtDO::getCreateTime, reqVO.getStartTime(), sdf.format(date));
        }
        if (StringUtil.isNotEmpty(reqVO.getSortDirection())){
            if (reqVO.getSortDirection().equals("asc")){
                if (reqVO.getSortBy().equals("createTime")){
                    wrapper.orderByAsc(BpmProcessInstanceExtDO::getCreateTime);
                }
                if (reqVO.getSortBy().equals("endTime")){
                    wrapper.orderByAsc(BpmProcessInstanceExtDO::getEndTime);
                }
            }else if (reqVO.getSortDirection().equals("desc")){
                if (reqVO.getSortBy().equals("createTime")){
                    wrapper.orderByDesc(BpmProcessInstanceExtDO::getCreateTime);
                }
                if (reqVO.getSortBy().equals("endTime")){
                    wrapper.orderByDesc(BpmProcessInstanceExtDO::getEndTime);
                }
            }
        }else {
            wrapper.orderByDesc(BpmProcessInstanceExtDO::getCreateTime);
        }

//        wrapper.orderByDesc(BpmProcessInstanceExtDO::getCreateTime);
        return selectPage(reqVO,wrapper);

    }

    default BpmProcessInstanceExtDO selectByProcessInstanceId(String processInstanceId) {
        return selectOne(BpmProcessInstanceExtDO::getProcessInstanceId, processInstanceId);
    }

    default void updateByProcessInstanceId(BpmProcessInstanceExtDO updateObj) {
        update(updateObj, new LambdaQueryWrapperX<BpmProcessInstanceExtDO>()
                .eq(BpmProcessInstanceExtDO::getProcessInstanceId, updateObj.getProcessInstanceId()));
    }

    default BpmProcessInstanceExtDO getProcessInfoByAlarmId(Long alarmId){
        return selectOne( new LambdaQueryWrapperX<BpmProcessInstanceExtDO>()
                .ne(BpmProcessInstanceExtDO::getResult,4)
                .apply(StringUtils.isNotEmpty(String.valueOf(alarmId)),"json_extract(form_variables, '$.requestId') = "+alarmId)
                .orderByDesc(BpmProcessInstanceExtDO::getCreateTime).last("limit 1"));
    }
}
