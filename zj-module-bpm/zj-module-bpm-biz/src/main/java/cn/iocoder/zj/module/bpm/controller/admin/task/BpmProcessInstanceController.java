package cn.iocoder.zj.module.bpm.controller.admin.task;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.bpm.controller.admin.task.vo.instance.*;
import cn.iocoder.zj.module.bpm.service.task.BpmProcessInstanceService;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import com.alibaba.cloud.commons.lang.StringUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name =  "管理后台 - 流程实例") // 流程实例，通过流程定义创建的一次“申请”
@RestController
@RequestMapping("/bpm/process-instance")
@Validated
public class BpmProcessInstanceController {

    @Resource
    private BpmProcessInstanceService processInstanceService;

    @Resource
    private PlatformconfigApi platformconfigApi;

    @GetMapping("/my-page")
    @Operation(summary = "获得我的实例分页列表", description = "在【我的流程】菜单中，进行调用")
//    @PreAuthorize("@ss.hasPermission('bpm:process-instance:query')")
    public CommonResult<PageResult<BpmProcessInstancePageItemRespVO>> getMyProcessInstancePage(
            @Valid BpmProcessInstanceMyPageReqVO pageReqVO) {
        return success(processInstanceService.getMyProcessInstancePage(pageReqVO));
    }

    @PostMapping("/create")
    @Operation(summary = "新建流程实例")
    @PreAuthorize("@ss.hasPermission('bpm:process-instance:create')")
    public CommonResult<String> createProcessInstance(@Valid @RequestBody BpmProcessInstanceCreateReqVO createReqVO) {
        return success(processInstanceService.createProcessInstance(getLoginUserId(), createReqVO));
    }

    @GetMapping("/get")
    @Operation(summary = "获得指定流程实例", description = "在【流程详细】界面中，进行调用")
    @Parameter(name = "id", description = "流程实例的编号", required = true)
//    @PreAuthorize("@ss.hasPermission('bpm:process-instance:query')")
    public CommonResult<BpmProcessInstanceRespVO> getProcessInstance(@RequestParam("id") String id) {
        return success(processInstanceService.getProcessInstanceVO(id));
    }

    @DeleteMapping("/cancel")
    @Operation(summary = "取消流程实例", description = "撤回发起的流程")
//    @PreAuthorize("@ss.hasPermission('bpm:process-instance:cancel')")
    public CommonResult<Boolean> cancelProcessInstance(@Valid @RequestBody BpmProcessInstanceCancelReqVO cancelReqVO) {
        processInstanceService.cancelProcessInstance(getLoginUserId(), cancelReqVO);
        return success(true);
    }

    @GetMapping("/getProcessInfo")
    @Operation(summary = "获取问题工单处理流程信息", description = "获取问题工单处理流程信息")
    public CommonResult<Map<String,Object>> getProcessInfo(@RequestParam("alarmId") Long alarmId, @RequestParam("type") Integer type) {

        return success(processInstanceService.getProcessInfo(alarmId,type));
    }

    //获取平台配置
    @GetMapping("/platformBpmSelect")
    public CommonResult<List<Map>> platformProjectBpm(@RequestParam(required = false, defaultValue = "") String tenantId, String id) {
        List<Map> tenantData = platformconfigApi.getPlatformSelectList(tenantId).getData();
        if (StringUtils.isNotEmpty(id)) {
            String startUserId = processInstanceService.getProcessInstanceStartId(id);
            List<Map> userData = platformconfigApi.getPlatformListByUserId(startUserId).getData();
            tenantData = Stream.concat(userData.stream(), tenantData.stream())
                    .collect(Collectors.toMap(map -> map.entrySet().toString(), map -> map, (existing, replacement) -> existing))
                    .values().stream()
                    .collect(Collectors.toList());
        }
        return success(tenantData);
    }
}