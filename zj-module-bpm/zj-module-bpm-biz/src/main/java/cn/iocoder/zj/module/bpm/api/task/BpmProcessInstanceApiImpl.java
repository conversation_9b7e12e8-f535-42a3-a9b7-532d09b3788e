package cn.iocoder.zj.module.bpm.api.task;

import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.module.bpm.api.task.dto.BpmProcessInstanceCreateReqDTO;
import cn.iocoder.zj.module.bpm.service.task.BpmProcessInstanceService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.Map;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;

/**
 * Flowable 流程实例 Api 实现类
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@Service
@Validated
public class BpmProcessInstanceApiImpl implements BpmProcessInstanceApi {

    @Resource
    private BpmProcessInstanceService processInstanceService;

    @Override
    public CommonResult<String> createProcessInstance(Long userId, @Valid BpmProcessInstanceCreateReqDTO reqDTO) {
        return success(processInstanceService.createProcessInstance(userId, reqDTO));
    }
}
