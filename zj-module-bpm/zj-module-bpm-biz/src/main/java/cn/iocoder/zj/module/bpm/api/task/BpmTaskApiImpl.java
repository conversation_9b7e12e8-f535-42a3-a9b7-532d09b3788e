package cn.iocoder.zj.module.bpm.api.task;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.util.collection.CollectionUtils;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.module.bpm.api.task.dto.BpmTaskExtDTO;
import cn.iocoder.zj.module.bpm.controller.admin.task.vo.task.BpmProcessInstanceExtRespVo;
import cn.iocoder.zj.module.bpm.dal.dataobject.task.BpmTaskExtDO;
import cn.iocoder.zj.module.bpm.dal.mysql.definition.FlowNodeMapper;
import cn.iocoder.zj.module.bpm.dal.mysql.task.BpmTaskExtMapper;
import cn.iocoder.zj.module.bpm.service.task.BpmProcessInstanceService;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.http.client.utils.DateUtils;
import org.flowable.engine.HistoryService;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.convertSet;
import static cn.iocoder.zj.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class BpmTaskApiImpl implements BpmTaskApi{
    @Resource
    private HistoryService historyService;
    @Resource
    private BpmTaskExtMapper taskExtMapper;
    @Resource
    private AdminUserApi adminUserApi;
    @Resource
    private FlowNodeMapper flowNodeMapper;
    @Resource
    private PlatformconfigApi platformconfigApi;
    @Resource
    private BpmProcessInstanceService processInstanceService;
    @Override
    public CommonResult<Map<String,Object>> workOrderStatistics(List<Long> homologousUsers, String userRole) {
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("todoCount",0);
        resultMap.put("doneCount",0);
        resultMap.put("total",0);
        resultMap.put("overTimeTasks",0);
        resultMap.put("notOverTimeTasks",0);
        resultMap.put("overTimeRate","0%");
        resultMap.put("backCount",0);
        LoginUser user =  SecurityFrameworkUtils.getLoginUser();
        List<String> targetUsers = new ArrayList<>();
        homologousUsers.forEach(item->{
            targetUsers.add(String.valueOf(item));
        });
        //创建查询
        //待办任务
        HistoricProcessInstanceQuery todoTasks = historyService.createHistoricProcessInstanceQuery().unfinished();
        //已办任务
        HistoricProcessInstanceQuery doneTasks = historyService.createHistoricProcessInstanceQuery().finished();
        //超时任务
        HistoricProcessInstanceQuery allTasks  = historyService.createHistoricProcessInstanceQuery();
        //按角色设置查询条件
        //待办任务数
        Long todoCount=0L;
        //已办任务数
        Long doneCount=0L;
        //总任务数
        Long total=0L;
        //回退任务数
        Long backCount = flowNodeMapper.countBackTack(homologousUsers,user.getId(),userRole);
        if(userRole.equals("personal")){
            todoCount = todoTasks.startedBy(String.valueOf(user.getId())).count();
            doneCount = doneTasks.startedBy(String.valueOf(user.getId())).finished().count();
            total     = allTasks.startedBy(String.valueOf(user.getId())).count();
        } else if (userRole.equals("tenantAdmin")||userRole.equals("superAdmin")) {
            if(ObjectUtil.isNotEmpty(targetUsers)) {
                for (String userId : targetUsers) {
                    todoCount += todoTasks.startedBy(userId).count();
                    doneCount += doneTasks.startedBy(userId).finished().count();
                    total += allTasks.startedBy(userId).count();
                }
            }
        }
        DecimalFormat df = new DecimalFormat("#.##");
        Map overTimeTaskCount = flowNodeMapper.getOverTimeTaskCount(homologousUsers,user.getId(),userRole);
        BigDecimal overTime = (BigDecimal)overTimeTaskCount.get("overtime");
        BigDecimal doneTotal =  (BigDecimal)overTimeTaskCount.get("total");
        BigDecimal notovertime =  (BigDecimal)overTimeTaskCount.get("notovertime");
        Double overTimeRate = doneTotal.compareTo(new BigDecimal(0))<1?0:(double)overTime.intValue()/doneTotal.intValue();
        resultMap.put("todoCount",todoCount);
        resultMap.put("doneCount",doneCount);
        resultMap.put("total",total);
        resultMap.put("overTimeTasks",overTime);
        resultMap.put("notOverTimeTasks",notovertime);
        resultMap.put("overTimeRate",df.format(overTimeRate*100)+"%");
        resultMap.put("backCount",backCount);
        return CommonResult.success(resultMap);
    }

    @Override
    public List<Map<String, Object>> getWorkOrderStatisticsInWeek(List<Long> homologousUsers, String userRole) {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        calendar.add(Calendar.DATE, -5);
        String fiveDaysAgo = sdf.format(calendar.getTime());
        Date targetDate = DateUtil.parseDate(fiveDaysAgo);
        Map<String,Object> resultMap = new HashMap<>();
        LoginUser user =  SecurityFrameworkUtils.getLoginUser();
        Set<String> targetUsers = new HashSet<>();
        homologousUsers.forEach(item->{
            targetUsers.add(String.valueOf(item));
        });
        //创建查询
        //待办任务
        HistoricProcessInstanceQuery todoTasks = historyService.createHistoricProcessInstanceQuery().unfinished().startedAfter(targetDate);
        //已办任务
        HistoricProcessInstanceQuery doneTasks = historyService.createHistoricProcessInstanceQuery().finished().startedAfter(targetDate);
        //超时任务
        HistoricProcessInstanceQuery allTasks  = historyService.createHistoricProcessInstanceQuery().startedAfter(targetDate);
        //按角色设置查询条件
        //待办任务数
        Long todoCount=0L;
        //已办任务数
        Long doneCount=0L;
        //总任务数
        Long total=0L;
        if(userRole.equals("personal")){
                todoCount = todoTasks.startedBy(String.valueOf(user.getId())).count();
                doneCount = doneTasks.startedBy(String.valueOf(user.getId())).finished().count();
                total     = allTasks.startedBy(String.valueOf(user.getId())).count();
        } else if (userRole.equals("tenantAdmin")||userRole.equals("superAdmin")) {
                for (String userId:targetUsers) {
                    todoCount += todoTasks.startedBy(userId).count();
                    doneCount += doneTasks.startedBy(userId).finished().count();
                    total     += allTasks.startedBy(userId).count();
                }
        }
        List<HistoricTaskInstance> tasks =historyService.createHistoricTaskInstanceQuery().list();
        LambdaQueryWrapperX<BpmTaskExtDO> lqw = new LambdaQueryWrapperX<>();
        lqw.inIfPresent(BpmTaskExtDO::getTaskId,convertSet(tasks, HistoricTaskInstance::getId));
        lqw.eq(BpmTaskExtDO::getIsOvertime,1);
        List<BpmTaskExtDO> overTimeTasks =taskExtMapper.selectList(lqw).stream()
                                                       .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(BpmTaskExtDO::getProcessInstanceId))),
                        ArrayList::new));
        //未超时数
        Long notOverTimeTasks = doneCount-overTimeTasks.size();
        resultMap.put("todoCount",todoCount);
        resultMap.put("doneCount",doneCount);
        resultMap.put("total",total);
        resultMap.put("overTimeTasks",overTimeTasks.size());
        resultMap.put("notOverTimeTasks",notOverTimeTasks);
        List<Map<String,Object>> result = new ArrayList<>();
        result.add(resultMap);
        return result;
    }

    @Override
    public List<Map<String, Object>> getRanking(List<Long> homologousUsers, String role) {
        List<Map<String,Object>> resultList = new ArrayList<>();
        List<String> targetUsers = new ArrayList<>();
        homologousUsers.forEach(item->{
            targetUsers.add(String.valueOf(item));
        });
        List<AdminUserRespDTO> usersInfo = adminUserApi.getUserList(homologousUsers).getData();
        //待办任务
        HistoricProcessInstanceQuery todoTasks = historyService.createHistoricProcessInstanceQuery().unfinished();
        //已办任务
        HistoricProcessInstanceQuery doneTasks = historyService.createHistoricProcessInstanceQuery().finished();

        for (String userId:targetUsers) {
            //待办任务数
            Long todoCount=0L;
            //已办任务数
            Long doneCount=0L;
            Map<String,Object> resultMap = new HashMap<>();
            todoCount += todoTasks.startedBy(userId).count();
            doneCount += doneTasks.startedBy(userId).finished().count();
            resultMap.put("doneCount",doneCount);
            resultMap.put("todoCount",todoCount);
            resultMap.put("enforcer_id",userId);
            for (AdminUserRespDTO user:usersInfo) {
                if(String.valueOf(user.getId()).equals(userId)){
                    resultMap.put("enforcer_name",user.getNickname());
                    break;
                }

            }
            resultList.add(resultMap);
        }
        Comparator<Map<String, Object>> comparator = Comparator.comparing(m -> (Long) m.get("doneCount"));
        // 对mapList进行排序
        resultList.sort(comparator.reversed());
        //保留前五条数据
        return resultList.stream().limit(5).collect(Collectors.toList());
    }

    @Override
    public CommonResult<List<Map<String, String>>> getWorkOrderSourceType(List<Long> homologousUsers, String role) {
        LoginUser user =  SecurityFrameworkUtils.getLoginUser();
        List<BpmProcessInstanceExtRespVo> instanceExts =  flowNodeMapper.getAllProcessInstance(homologousUsers,user.getId(),role);
        Long hostNum = 0L,hardwareNum = 0L,storageNum = 0L,total = 0L;
        List<Integer> hzbAlert = new ArrayList<>();
        List<Integer> cloudAlarm = new ArrayList<>();
        for (BpmProcessInstanceExtRespVo instance: instanceExts) {
            JSONObject formVariableObj = JSONObject.parseObject(instance.getFormVariablesStr());
            if(formVariableObj.get("requestId")!=null && formVariableObj.get("requestId")!=""){
                if(formVariableObj.get("table").equals("hzbAlert")){
                    hzbAlert.add((Integer)formVariableObj.get("requestId"));
                }else {
                    cloudAlarm.add((Integer) formVariableObj.get("requestId"));
                }
            } else if (formVariableObj.get("sourceType")!=null) {
                total++;
                switch ((String)formVariableObj.get("sourceType")){
                    case "host" :hostNum++;break;
                    case "hardware" :hardwareNum++;break;
                    case "storage" :storageNum++;break;
                }
            }
        }
        Map<String,Object> hzbAlarmCountMap = getHzbAlarmByIds(hzbAlert);        //获取doris中的数据
        Map<String,Object> cloudAlarmCountMap = flowNodeMapper.getCloudAlarmByIds(cloudAlarm);        //获取mysql中的数据
        //求和
        total = Convert.toLong(hzbAlarmCountMap.get("total"))+Convert.toLong(cloudAlarmCountMap.get("total"))+total;
        hostNum = Convert.toLong(hzbAlarmCountMap.get("hostNum"))+Convert.toLong(cloudAlarmCountMap.get("hostNum"))+hostNum;
        hardwareNum =Convert.toLong(hzbAlarmCountMap.get("hardwareNum"))+Convert.toLong(cloudAlarmCountMap.get("hardwareNum"))+hardwareNum;
        storageNum = Convert.toLong(hzbAlarmCountMap.get("storageNum"))+Convert.toLong(cloudAlarmCountMap.get("storageNum"))+storageNum;
        Map<String,String> resultMap = new HashMap<>();
        resultMap.put("total",String.valueOf(total));
        resultMap.put("hostNum",String.valueOf(hostNum));
        resultMap.put("hardwareNum",String.valueOf(hardwareNum));
        resultMap.put("storageNum",String.valueOf(storageNum));
        List<Map<String,String>> resultList = new ArrayList<>();
        resultList.add(resultMap);
        return CommonResult.success(resultList);
    }

    @Override
    public CommonResult<List<Map<String, String>>> getCreateInWeek(List<Long> homologousUsers, String role) {
        List<String> dateStrList = new ArrayList<>();
        //格式化时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        //获取过去7天的日期并格式化
        LocalDate currentDate = LocalDate.now();
        for (int i = 0; i < 7; i++) {
            String formattedDate = currentDate.minusDays(i).format(formatter);
            dateStrList.add(formattedDate);
        }
        List<Map<String, String>> result = flowNodeMapper.getCreateInWeek(dateStrList,homologousUsers,role);
        List<Map<String, String>> finalResult = result;
        List<String> insertDate = dateStrList.stream()
                .filter(str -> finalResult.stream().noneMatch(map -> map.containsKey("dateStr") && map.get("dateStr").equals(str)))
                .collect(Collectors.toList());
        for (String item:insertDate) {
            Map<String,String> map = new HashMap<>();
            map.put("dateStr",item);
            map.put("total","0");
            map.put("doneCount","0");
            map.put("todoCount","0");
            result.add(map);
        }
        //按时间排序
        result = result.stream().sorted(
                Comparator.comparingLong(
                        e -> DateUtils.parseDate(e.get("dateStr"), new String[]{"yyyy-MM-dd"}).getTime()
                )
        ).collect(Collectors.toList());
        return  CommonResult.success(result);
    }

    @Override
    public CommonResult<Map<String, String>> getPersonWorkOrderInfo(List<Long> homologousUsers,Long userId, String role) {
        //回退任务数
        Long backCount = flowNodeMapper.countBackTack(homologousUsers,userId,role);
        Map<String, String> resultMap = flowNodeMapper.getPersonWorkOrderInfo(userId,role);
        resultMap.put("backCount",String.valueOf(backCount));
        return CommonResult.success(resultMap);
    }
    @Override
    public CommonResult<List<Map>> getTasksCount(String userName,Long tenantId,String startTime,String endTime) {
        return CommonResult.success(flowNodeMapper.getTasksCount(userName,tenantId,startTime,endTime));
    }

    @Override
    public CommonResult<Map> getSLAResponse(List<Long> targetUsers) {
        return CommonResult.success(flowNodeMapper.getSLAResponse(targetUsers));
    }

    @Override
    public CommonResult<List<Map>> getTodoTask(Long platformId) {
        LoginUser currentUser = SecurityFrameworkUtils.getLoginUser();
        List<Long> homologousUsers = adminUserApi.getHomologousUsers(currentUser.getId()).getData();
        String platformIdStr = "\"platformId\":";
        platformIdStr = platformId==null?null:platformIdStr+platformId;
        List<Map> resultList = new ArrayList<>();
        List<BpmTaskExtDTO> toTaskDOs = flowNodeMapper.getTodoTaskDO(homologousUsers,platformIdStr);
        if(toTaskDOs.size()>0) {
            List<Map> formMapList = flowNodeMapper.getFormByDefinitionId(convertSet(toTaskDOs, BpmTaskExtDTO::getProcessDefinitionId));

            List<PlatformconfigDTO> platform = platformconfigApi.getPlatList().getData();
            Map<Long, PlatformconfigDTO> platformMap = CollectionUtils.convertMap(platform, PlatformconfigDTO::getId);
            for (BpmTaskExtDTO task : toTaskDOs) {
                String formVariables = task.getFormVariables();
                JSONObject variableObj = JSONObject.parseObject(formVariables);
                Map resultMap = new HashMap<>();
                resultMap.put("name", task.getProcessName());
                resultMap.put("assignee", task.getAssignee());
                resultMap.put("createTime", task.getCreateTime());
                resultMap.put("taskId", task.getTaskId());
                resultMap.put("priority", "");
                resultMap.put("platformId", "");
                resultMap.put("platformName", "");
                for (Map form : formMapList) {
                    if (task.getProcessDefinitionId().equals(form.get("processDefinitionId"))) {
                        String filedStr = (String) form.get("formatFelds");
                        JSONArray filedArr = filedStrConvert(filedStr);
                        for (Object filedObj : filedArr) {
                            JSONObject jsonObj = JSONObject.parseObject(JSONUtil.toJsonStr(filedObj));
                            if (jsonObj.get("title").equals("优先级") && variableObj.getString(jsonObj.getString("field")) != null) {
                                resultMap.put("priority", variableObj.getInteger(jsonObj.getString("field")));
                            }
                            if (jsonObj.get("field").equals("platformId") && variableObj.getLong("platformId") != null) {
                                Long platId = variableObj.getLong("platformId");
                                String platformName = platformMap.get(platId).getName();
                                resultMap.put("platformId", platId);
                                resultMap.put("platformName", platformName);
                            }
                        }
                    }
                }
                resultList.add(resultMap);
            }
        }
        return CommonResult.success(resultList);
    }

    @Override
    public CommonResult<Map<String, Object>> getProcessInfo(Long alarmId, Integer type) {
        return success(processInstanceService.getProcessInfo(alarmId,type));
    }

    private JSONArray filedStrConvert(String filedStr){
        List<Object> fieldArray = JSON.parseArray(filedStr);
        JSONArray jsonArray = new JSONArray();
        for (Object input : fieldArray) {
            JSONObject jsonObject = JSONObject.parseObject(JSONUtil.toJsonStr(input));
            jsonArray.add(jsonObject);
        }
        return jsonArray;
    }


    private Map<String,Object> getHzbAlarmByIds(List<Integer> requestIds){
        return  flowNodeMapper.getHzbAlarmByIds(requestIds);
    }
}
