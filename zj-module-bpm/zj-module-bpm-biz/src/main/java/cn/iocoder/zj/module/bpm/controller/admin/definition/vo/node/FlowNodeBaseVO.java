package cn.iocoder.zj.module.bpm.controller.admin.definition.vo.node;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
 * 工作流表单的节点定义 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class FlowNodeBaseVO {

    @Schema(description = "节点id", required = true)
    @NotNull(message = "节点id不能为空")
    private String nodeId;

    @Schema(description = "节点名称")
    private String nodeName;

    @Schema(description = "表单id")
    private Long fromId;

    @Schema(description = "模型id", required = true)
    private String modelId;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> nodeFields;



    private String xml;

    /**
     * 流程定义编号
     *
     * 关联 ProcessDefinition 的 id 属性
     */
    private String processDefinitionId;

    //节点类型
    private String type;



}
