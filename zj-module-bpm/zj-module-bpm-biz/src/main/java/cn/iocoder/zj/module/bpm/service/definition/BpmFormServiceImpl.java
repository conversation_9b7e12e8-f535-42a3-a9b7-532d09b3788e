package cn.iocoder.zj.module.bpm.service.definition;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.util.json.JsonUtils;
import cn.iocoder.zj.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.web.core.util.WebFrameworkUtils;
import cn.iocoder.zj.module.bpm.controller.admin.definition.vo.form.BpmFormCreateReqVO;
import cn.iocoder.zj.module.bpm.controller.admin.definition.vo.form.BpmFormPageReqVO;
import cn.iocoder.zj.module.bpm.controller.admin.definition.vo.form.BpmFormUpdateReqVO;
import cn.iocoder.zj.module.bpm.convert.definition.BpmFormConvert;
import cn.iocoder.zj.module.bpm.dal.dataobject.definition.BpmFormDO;
import cn.iocoder.zj.module.bpm.dal.dataobject.definition.BpmProcessDefinitionExtDO;
import cn.iocoder.zj.module.bpm.dal.dataobject.definition.FlowNodeDO;
import cn.iocoder.zj.module.bpm.dal.mysql.definition.BpmFormMapper;
import cn.iocoder.zj.module.bpm.dal.mysql.definition.FlowNodeMapper;
import cn.iocoder.zj.module.bpm.enums.ErrorCodeConstants;
import cn.iocoder.zj.module.bpm.enums.definition.BpmModelFormTypeEnum;
import cn.iocoder.zj.module.bpm.service.definition.dto.BpmFormFieldRespDTO;
import cn.iocoder.zj.module.bpm.service.definition.dto.BpmModelMetaInfoRespDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.Statement;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.bpm.enums.ErrorCodeConstants.*;


/**
 * 动态表单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BpmFormServiceImpl implements BpmFormService {

    @Value("${spring.datasource.dynamic.datasource.master.url}")
    private String url;

    @Value("${spring.datasource.dynamic.datasource.master.username}")
    private String username;

    @Value("${spring.datasource.dynamic.datasource.master.password}")
    private String  password;


    @Resource
    private BpmFormMapper formMapper;

    @Resource
    private FlowNodeMapper flowNodeMapper;

    @Override
    public Long createForm(BpmFormCreateReqVO createReqVO) {
        //验证表单字段是否重复
        this.validateFormField(createReqVO.getFields());
        this.checkFields(createReqVO.getFields());
        // 插入
        BpmFormDO form = BpmFormConvert.INSTANCE.convert(createReqVO);
        List<String> fields = createReqVO.getFields();
        try{
            // 将 JSON 数据转换为 Java 对象
            ObjectMapper objectMapper = new ObjectMapper();
            List<Map<String, Object>> formData = objectMapper.readValue(fields.toString(), List.class);

            // 连接到 MySQL 数据库
            Connection connection = DriverManager.getConnection(url, username, password);
            // 创建表单
            String tableName="bpm_form_"+createReqVO.getName();
            String createTableQuery = "CREATE TABLE " + tableName + " (id BIGINT AUTO_INCREMENT PRIMARY KEY,";
            for (int i = 0; i < formData.size(); i++) {
                Map<String, Object> fieldData = formData.get(i);
                String fieldType = (String) fieldData.get("type");
                String fieldName = (String) fieldData.get("field");
                String fieldTitle = (String) fieldData.get("title");

                String columnType;
                if ("input".equals(fieldType)||"input".equals(fieldType)||"checkbox".equals(fieldType)||"timePicker".equals(fieldType)||"datePicker".equals(fieldType)||"slider".equals(fieldType)||"upload".equals(fieldType)||"el-transfer".equals(fieldType)) {
                    columnType = "varchar(64)";
                } else if ("inputNumber".equals(fieldType)||"radio".equals(fieldType)||"select".equals(fieldType)||"rate".equals(fieldType)) {
                    columnType = "int(20)";
                }else if ("fc-editor".equals(fieldType)){
                    columnType = "text(1000)";
                }else if ("colorPicker".equals(fieldType)||"cascader".equals(fieldType)){
                    columnType = "varchar(20)";
                }
                else {
                    // 处理其他类型
                    continue;
                }
                createTableQuery += fieldName + " " + columnType + " COMMENT '" + fieldTitle+"' ";
                if (i != formData.size() - 1) {
                    createTableQuery += ", ";
                }
            }
            createTableQuery+=", process_instance_id varchar(64) COMMENT '流程实例的编号', process_definition_id varchar(64) COMMENT '流程定义的编号' ,`creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',\n" +
                    "  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n" +
                    "  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',\n" +
                    "  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',\n" +
                    "  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除')ENGINE=InnoDB AUTO_INCREMENT=45 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC";
            System.out.println(createTableQuery);
            // 执行 CREATE TABLE 语句
            Statement statement = connection.createStatement();
            statement.executeUpdate(createTableQuery);
            // 关闭连接
            statement.close();
            connection.close();
        }catch (Exception e){
            e.printStackTrace();
        }
        Long userId = WebFrameworkUtils.getLoginUserId();
        //根据userId获取tenantId
        Long tenantId=flowNodeMapper.getTenantIdByUserId(userId);
        form.setTenantId(tenantId);
        formMapper.insert(form);
        // 返回
        return form.getId();
    }

    @Override
    public void updateForm(BpmFormUpdateReqVO updateReqVO) {
        //验证表单字段是否重复
        this.validateFormField(updateReqVO.getFields());

        this.checkFields(updateReqVO.getFields());
        // 校验存在
        this.validateFormExists(updateReqVO.getId());
        List<String> newFields = updateReqVO.getFields();
        List<String> definitionIds = flowNodeMapper.getDefinitionIdByFormId(updateReqVO.getId());
        List<FlowNodeDO> flowNodeDOList = new ArrayList<>();
        if(definitionIds.size()>0){
            LambdaQueryWrapperX<FlowNodeDO> nodeLqw = new LambdaQueryWrapperX<>();
            nodeLqw.inIfPresent(FlowNodeDO::getProcessDefinitionId,definitionIds)
                    .eq(FlowNodeDO::getDeleted, 0);
            flowNodeDOList = flowNodeMapper.selectList(nodeLqw);
        }
        if(flowNodeDOList.size()>0) {
            for (FlowNodeDO nodeDo : flowNodeDOList) {
                List<String> fieldArray = nodeDo.getNodeFields();
                int length = fieldArray.size();
                for (String newFieldStr : newFields) {
                    JSONObject newFieldObj = JSONObject.parseObject(newFieldStr);
                    boolean exist = false;
                    for (int i = 0; i < length; i++) {
                        JSONObject fieldObj = JSONObject.parseObject(ObjectUtil.toString(fieldArray.get(i)));
                        if (newFieldObj.getString("field").equalsIgnoreCase(fieldObj.getString("field"))) {
                            exist = true;
                            break;
                        }
                    }
                    if (!exist) {
                        JSONObject addTarget = new JSONObject();
                        addTarget.put("title", newFieldObj.getString("title"));
                        addTarget.put("field", newFieldObj.getString("field"));
                        addTarget.put("hidden", false);
                        addTarget.put("disabled", false);
                        addTarget.put("$required", newFieldObj.getString("$required"));
                        fieldArray.add(JsonUtils.toJsonString(addTarget));
                    }
                }
                nodeDo.setNodeFields(fieldArray);
            }
        }
        // 更新
        BpmFormDO updateObj = BpmFormConvert.INSTANCE.convert(updateReqVO);
        if(CollectionUtil.isNotEmpty(flowNodeDOList)) {
            flowNodeMapper.updateBatch(flowNodeDOList);
        }
        formMapper.updateById(updateObj);
    }

    @Override
    public void deleteForm(Long id) {
        // 校验存在
        this.validateFormExists(id);
        // 删除
        formMapper.deleteById(id);
    }

    private void validateFormExists(Long id) {
        if (formMapper.selectById(id) == null) {
            throw exception(FORM_NOT_EXISTS);
        }
    }

    @Override
    public BpmFormDO getForm(Long id) {
        return formMapper.selectById(id);
    }

    @Override
    public List<BpmFormDO> getFormList() {
        Long userId = WebFrameworkUtils.getLoginUserId();
        LambdaQueryWrapperX<BpmFormDO> lqw = new LambdaQueryWrapperX<>();
        Long tenantId = flowNodeMapper.getTenantIdByUserId(userId);
        lqw.eqIfPresent(BpmFormDO::getTenantId,tenantId)
                .eq(BaseDO::getDeleted,0);
        return formMapper.selectList(lqw);
    }

    @Override
    public List<BpmFormDO> getFormList(Collection<Long> ids) {
        return formMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<BpmFormDO> getFormPage(BpmFormPageReqVO pageReqVO) {
        Long userId = WebFrameworkUtils.getLoginUserId();
        Long tenantId = flowNodeMapper.getTenantIdByUserId(userId);
        pageReqVO.setTenantId(tenantId);

        return formMapper.selectPage(pageReqVO);

    }


    @Override
    public BpmFormDO checkFormConfig(String configStr) {
        BpmModelMetaInfoRespDTO metaInfo = JsonUtils.parseObject(configStr, BpmModelMetaInfoRespDTO.class);
        if (metaInfo == null || metaInfo.getFormType() == null) {
            throw exception(MODEL_DEPLOY_FAIL_FORM_NOT_CONFIG);
        }
        // 校验表单存在
        if (Objects.equals(metaInfo.getFormType(), BpmModelFormTypeEnum.NORMAL.getType())) {
            BpmFormDO form = getForm(metaInfo.getFormId());
            if (form == null) {
                throw exception(FORM_NOT_EXISTS);
            }
            return form;
        }
        return null;
    }

    /**
     * 校验 Field，避免 field 重复
     *
     * @param fields field 数组
     */
    private void checkFields(List<String> fields) {
        if (true) { // TODO 芋艿：兼容 Vue3 工作流：因为采用了新的表单设计器，所以暂时不校验
            return;
        }
        Map<String, String> fieldMap = new HashMap<>(); // key 是 vModel，value 是 label
        for (String field : fields) {
            BpmFormFieldRespDTO fieldDTO = JsonUtils.parseObject(field, BpmFormFieldRespDTO.class);
            Assert.notNull(fieldDTO);
            String oldLabel = fieldMap.put(fieldDTO.getVModel(), fieldDTO.getLabel());
            // 如果不存在，则直接返回
            if (oldLabel == null) {
                continue;
            }
            // 如果存在，则报错
            throw exception(ErrorCodeConstants.FORM_FIELD_REPEAT, oldLabel, fieldDTO.getLabel(), fieldDTO.getVModel());
        }
    }
    private void validateFormField(List<String> fields) {
        List<JSONObject> fieldObjList = new ArrayList<>();
        fields.forEach(item->{
            JSONObject fieldObj = JSONObject.parseObject(item);
            JSONObject target = new JSONObject();
            target.put("field",fieldObj.getString("field"));
            target.put("title",fieldObj.getString("title"));
            fieldObjList.add(target);
        });
        Set<JSONObject> set = new HashSet<>();
        boolean hasDuplicate = false;
        String fieldTitle = "";
        for(JSONObject obj : fieldObjList) {
            if(!set.add(obj)) { // 如果无法添加元素到set中，说明已经存在重复元素
                hasDuplicate = true;
                fieldTitle = obj.getString("title");
                break;
            }
        }
        if (hasDuplicate) {
            throw exception(ErrorCodeConstants.FIELD_HAS_DUPLICATE,fieldTitle);
        }
    }
}
