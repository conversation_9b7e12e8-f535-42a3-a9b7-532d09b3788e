package cn.iocoder.zj.module.bpm.service.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.common.util.collection.CollectionUtils;
import cn.iocoder.zj.framework.common.util.number.NumberUtils;
import cn.iocoder.zj.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.framework.web.core.util.WebFrameworkUtils;
import cn.iocoder.zj.module.bpm.api.task.dto.BpmProcessInstanceCreateReqDTO;
import cn.iocoder.zj.module.bpm.controller.admin.definition.vo.model.BpmModelRespVO;
import cn.iocoder.zj.module.bpm.controller.admin.task.vo.instance.*;
import cn.iocoder.zj.module.bpm.controller.admin.task.vo.task.BpmTaskRespVO;
import cn.iocoder.zj.module.bpm.convert.task.BpmProcessInstanceConvert;
import cn.iocoder.zj.module.bpm.dal.dataobject.definition.BpmProcessDefinitionExtDO;
import cn.iocoder.zj.module.bpm.dal.dataobject.definition.FlowNodeDO;
import cn.iocoder.zj.module.bpm.dal.dataobject.task.BpmProcessInstanceExtDO;
import cn.iocoder.zj.module.bpm.dal.dataobject.task.ReportSubscriptionDTO;
import cn.iocoder.zj.module.bpm.dal.mysql.definition.FlowNodeMapper;
import cn.iocoder.zj.module.bpm.dal.mysql.task.BpmProcessInstanceExtMapper;
import cn.iocoder.zj.module.bpm.enums.task.BpmProcessInstanceDeleteReasonEnum;
import cn.iocoder.zj.module.bpm.enums.task.BpmProcessInstanceResultEnum;
import cn.iocoder.zj.module.bpm.enums.task.BpmProcessInstanceStatusEnum;
import cn.iocoder.zj.module.bpm.framework.bpm.core.event.BpmProcessInstanceResultEventPublisher;
import cn.iocoder.zj.module.bpm.service.definition.BpmModelServiceImpl;
import cn.iocoder.zj.module.bpm.service.definition.BpmProcessDefinitionService;
import cn.iocoder.zj.module.bpm.service.message.BpmMessageService;
import cn.iocoder.zj.module.monitor.api.alarm.AlarmConfigApi;
import cn.iocoder.zj.module.monitor.api.alarm.dto.AlarmInfoDTO;
import cn.iocoder.zj.module.system.api.dept.DeptApi;
import cn.iocoder.zj.module.system.api.dept.dto.DeptRespDTO;
import cn.iocoder.zj.module.system.api.dict.DictDataApi;
import cn.iocoder.zj.module.system.api.dict.dto.DictDataRespDTO;
import cn.iocoder.zj.module.system.api.mail.MailSendApi;
import cn.iocoder.zj.module.system.api.mail.dto.MailSendSingleToUserReqDTO;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import cn.iocoder.zj.module.system.api.platformconfig.dto.PlatformconfigDTO;
import cn.iocoder.zj.module.system.api.user.AdminUserApi;
import cn.iocoder.zj.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.zj.module.system.api.wechat.WeChatSendApi;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.delegate.event.FlowableCancelledEvent;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.convertList;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.singleton;
import static cn.iocoder.zj.module.bpm.enums.ErrorCodeConstants.*;

/**
 * 流程实例 Service 实现类
 * <p>
 * ProcessDefinition & ProcessInstance & Execution & Task 的关系：
 * 1. <a href="https://blog.csdn.net/bobozai86/article/details/105210414" />
 * <p>
 * HistoricProcessInstance & ProcessInstance 的关系：
 * 1. <a href=" https://my.oschina.net/843294669/blog/71902" />
 * <p>
 * 简单来说，前者 = 历史 + 运行中的流程实例，后者仅是运行中的流程实例
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class BpmProcessInstanceServiceImpl implements BpmProcessInstanceService {

    @Resource
    private RuntimeService runtimeService;
    @Resource
    private BpmProcessInstanceExtMapper processInstanceExtMapper;
    @Resource
    @Lazy // 解决循环依赖
    private BpmTaskService taskService;
    @Resource
    private BpmProcessDefinitionService processDefinitionService;
    @Resource
    private HistoryService historyService;
    @Resource
    private AdminUserApi adminUserApi;
    @Resource
    private DeptApi deptApi;
    @Resource
    private BpmProcessInstanceResultEventPublisher processInstanceResultEventPublisher;
    @Resource
    private BpmMessageService messageService;
    @Resource
    private FlowNodeMapper flowNodeMapper;
    @Resource
    private AlarmConfigApi alarmConfigApi;
    @Resource
    private DictDataApi dictDataApi;
    @Resource
    private RepositoryService repositoryService;
    @Resource
    private RoleApi roleApi;
    @Resource
    private WeChatSendApi weChatSendApi;
    @Resource
    private MailSendApi mailSendApi;
    @Resource
    private PlatformconfigApi platformconfigApi;

    @Value("${spring.datasource.dynamic.datasource.master.url}")
    private String url;

    @Value("${spring.datasource.dynamic.datasource.master.username}")
    private String username;

    @Value("${spring.datasource.dynamic.datasource.master.password}")
    private String password;

    @Resource
    PermissionApi permissionApi;

    @Resource
    BpmModelServiceImpl bpmModelService;
    @Override
    public ProcessInstance getProcessInstance(String id) {
        return runtimeService.createProcessInstanceQuery().processInstanceId(id).singleResult();
    }

    @Override
    public List<ProcessInstance> getProcessInstances(Set<String> ids) {
        return runtimeService.createProcessInstanceQuery().processInstanceIds(ids).list();
    }

    @Override
    public PageResult<BpmProcessInstancePageItemRespVO> getMyProcessInstancePage(BpmProcessInstanceMyPageReqVO pageReqVO) {
        LoginUser user = SecurityFrameworkUtils.getLoginUser();
        AdminUserRespDTO userDTO = adminUserApi.getUser(user.getId()).getData();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(user.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        boolean isAdmin = false;
        if(roleApi.hasAnySuperAdmin(roleIds)||roleApi.hasAnyTenantAdmin(roleIds)){
            isAdmin = true;
        }
        // 通过 BpmProcessInstanceExtDO 表，先查询到对应的分页
        PageResult<BpmProcessInstanceExtDO> pageResult = processInstanceExtMapper.selectPage(userDTO, pageReqVO,isAdmin);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return new PageResult<>(pageResult.getTotal());
        }

        // 获得流程 Task Map
        List<String> processInstanceIds = convertList(pageResult.getList(), BpmProcessInstanceExtDO::getProcessInstanceId);
        Map<String, List<Task>> taskMap = taskService.getTaskMapByProcessInstanceIds(processInstanceIds);
        // 转换返回
        return BpmProcessInstanceConvert.INSTANCE.convertPage(pageResult, taskMap);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createProcessInstance(Long userId, @Valid BpmProcessInstanceCreateReqVO createReqVO) {
        // 获得流程定义
        ProcessDefinition definition = processDefinitionService.getProcessDefinition(createReqVO.getProcessDefinitionId());
        BpmProcessDefinitionExtDO definitionExtDO = processDefinitionService.getProcessDefinitionExt(definition.getId());
//        从模板中获取
        BpmModelRespVO modelRespVO =  bpmModelService.getModel(definitionExtDO.getModelId());
        List<DictDataRespDTO> dictDataRespDTOS = dictDataApi.getDictDataInfoByType("bpm_model_category").getData();
        for (DictDataRespDTO item:dictDataRespDTOS) {
            //从告警发起的流程工单需要创建对应的工单记录

            if(item.getLabel().equals("问题工单")&&item.getValue().equals(String.valueOf(modelRespVO.getCategory()))){
                //判断是否有运维管理和对应的用户存在
                Integer count=flowNodeMapper.selectRoleExistence();
                if (count==0){
                    throw exception(TASK_CREATE_FAIL_NO_CANDIDATE_USER);
                }
                AlarmInfoDTO alarmInfoDTO = new AlarmInfoDTO();
                alarmInfoDTO.setAlarmId(String.valueOf(createReqVO.getVariables().get("requestId")));
                alarmInfoDTO.setProcessingType(1);
                alarmConfigApi.createAlarmInfo(alarmInfoDTO);
            }
        }
        // 发起流程
        return createProcessInstance0(userId, definition, createReqVO.getVariables(), null);
    }

    @Override
    public String createProcessInstance(Long userId, @Valid BpmProcessInstanceCreateReqDTO createReqDTO) {
        // 获得流程定义
        ProcessDefinition definition = processDefinitionService.getActiveProcessDefinition(createReqDTO.getProcessDefinitionKey());
        // 发起流程
        return createProcessInstance0(userId, definition, createReqDTO.getVariables(), createReqDTO.getBusinessKey());
    }

    @Override
    public BpmProcessInstanceRespVO getProcessInstanceVO(String id) {
        // 获得流程实例
        HistoricProcessInstance processInstance = getHistoricProcessInstance(id);
        if (processInstance == null) {
            return null;
        }
        BpmProcessInstanceExtDO processInstanceExt = processInstanceExtMapper.selectByProcessInstanceId(id);
        Assert.notNull(processInstanceExt, "流程实例拓展({}) 不存在", id);
        // 获得流程定义
        ProcessDefinition processDefinition = processDefinitionService
                .getProcessDefinition(processInstance.getProcessDefinitionId());
        Assert.notNull(processDefinition, "流程定义({}) 不存在", processInstance.getProcessDefinitionId());
        BpmProcessDefinitionExtDO processDefinitionExt = processDefinitionService.getProcessDefinitionExt(
                processInstance.getProcessDefinitionId());  //bpm_process_definition_ext
        Assert.notNull(processDefinitionExt, "流程定义拓展({}) 不存在", id);
        //查看当前节点自己是否可以处理,可以则返回节点id
        Long userId = WebFrameworkUtils.getLoginUserId();
        String nodeId = flowNodeMapper.getNodeIdByProcessInstanceId(id,userId.toString());
        //如果节点id为空，说明当前节点不是登录人审批节点，封装创建节点信息返回
        //nodeId为多个
        FlowNodeDO flowNodeDO=null;
        //结束节点如何判断
        Boolean isApprovalUser=false;
        if (nodeId!=null) {
            LambdaQueryWrapperX<FlowNodeDO> lqw = new LambdaQueryWrapperX<>();
            lqw.eqIfPresent(FlowNodeDO::getNodeId,nodeId);
            flowNodeDO = flowNodeMapper.selectOne(lqw);
            isApprovalUser=true;
            //如果结果为空，说明流程已经走到结束节点,查找结束节点信息
            if (flowNodeDO==null){
                String targetNodeId = flowNodeMapper.selectFinishByProcessInstanceId(id,"endEvent");
                flowNodeDO = flowNodeMapper.selectOne("node_id",targetNodeId,"process_definition_id",processInstanceExt.getProcessDefinitionId());
                isApprovalUser=false;
            }
        }else {
            //如果节点id为null，封装创建节点信息
            String targetNodeId = flowNodeMapper.selectFinishByProcessInstanceId(id,"startEvent");
            flowNodeDO = flowNodeMapper.selectOne("node_id",targetNodeId,"process_definition_id",processInstanceExt.getProcessDefinitionId());
        }
        ObjectMapper objectMapper = new ObjectMapper();
        if (flowNodeDO != null) {

            //主表单
            List<String> formFields = processDefinitionExt.getFormFields();
            List<String> nodeFields = flowNodeDO.getNodeFields();
                try {
                    // 第一段代码：处理和修改 formFields 列表中的 JSON 数据
                    for (int i = 0; i < formFields.size(); i++) {
                        // 读取 formFields 列表中的第 i 个 JSON 字符串并解析成 JsonNode 对象
                        JsonNode aNode = objectMapper.readTree(formFields.get(i));
                        // 获取字段名
                        String field = aNode.get("field").asText();
                        // 将 JsonNode 转换为 ObjectNode 以便修改
                        ObjectNode aObjectNode = (ObjectNode) aNode;

                        // 遍历 nodeFields 列表中的每个 JSON 字符串
                        for (String bJson : nodeFields) {
                            // 解析 bJson 字符串为 JsonNode 对象
                            JsonNode bNode = objectMapper.readTree(bJson);
                            // 创建一个新的 ObjectNode 用于存储属性
                            ObjectNode props = objectMapper.createObjectNode();

                            // 检查字段名是否相同
                            if (field.equals(bNode.get("field").asText())) {
                                // 获取 bNode 中的 required, hidden, disabled 属性
                                Boolean required = Boolean.valueOf(String.valueOf(bNode.get("$required")));
                                Boolean hidden = Boolean.valueOf(String.valueOf(bNode.get("hidden")));
                                Boolean disable = Boolean.valueOf(bNode.get("disabled") != null ? String.valueOf(bNode.get("disabled")) : "false");

                                // 根据 isApprovalUser 的值进行不同处理
                                if (isApprovalUser) {
                                    // 如果是审批用户，按原样设置属性
                                    props.put("required", required);
                                    props.put("hidden", hidden);
                                    props.put("disabled", disable);
                                    aObjectNode.put("$required", bNode.get("$required"));
                                    aObjectNode.put("hidden", bNode.get("hidden"));
                                } else {
                                    // 如果不是审批用户，强制设置 disabled 为 true
                                    props.put("required", required);
                                    props.put("hidden", hidden);
                                    props.put("disabled", true);
                                    aObjectNode.put("$required", bNode.get("$required"));
                                    aObjectNode.put("hidden", bNode.get("hidden"));
                                }

                                // 获取或创建 props 节点并更新其属性
                                ObjectNode propsNode = (ObjectNode) aObjectNode.get("props");
                                if (propsNode == null) {
                                    aObjectNode.put("props", props);
                                } else {
                                    propsNode.put("disabled", disable);
                                    propsNode.put("hidden", hidden);
                                    propsNode.put("required", required);
                                }

                                // 跳出内层循环，因为已经找到匹配的字段
                                break;
                            }
                        }

                        // 将修改后的 aNode 重新转换为字符串并更新 formFields 列表中对应的位置
                        formFields.set(i, aNode.toString());
                    }

                    // 第二段代码：后置条件检查，移除符合条件的 control 属性
                    for (int i = 0; i < formFields.size(); i++) {
                        // 读取 formFields 列表中的第 i 个 JSON 字符串并解析成 JsonNode 对象
                        JsonNode aNode = objectMapper.readTree(formFields.get(i));
                        // 将 JsonNode 转换为 ObjectNode 以便修改
                        ObjectNode aObjectNode = (ObjectNode) aNode;

                        // 获取 props 节点
                        ObjectNode propsNode = (ObjectNode) aObjectNode.get("props");
                        if (propsNode != null) {
                            // 检查 props 中的 disabled 属性是否为 true(应该改为查询control中的关联字段的disabled是否为true)
                                // 如果 aObjectNode 中有 control 属性，则移除它
                                if (aObjectNode.has("control")) {
                                    aObjectNode.remove("control");
                                }
                        }

                        // 将修改后的 aNode 重新转换为字符串并更新 formFields 列表中对应的位置
                        formFields.set(i, aNode.toString());
                    }
                } catch (Exception e) {
                    // 捕获并打印异常
                    e.printStackTrace();
                }
//            if (processInstanceExt.getStatus()!=null && processInstanceExt.getStatus()==2){
//                //流程已结束，封装上一节点的数据,disable改为true
//                log.info("流程已结束");
//                List<String> updatedFormFields = new ArrayList<>();
//                for (String jsonStr : formFields) {
//                    JSONObject jsonObject = JSON.parseObject(jsonStr);
//                    if (jsonObject.containsKey("hidden")) {
//                        jsonObject.put("hidden", false);
//                    }
//                    if (jsonObject.containsKey("props")) {
//                        JSONObject props = jsonObject.getJSONObject("props");
//                        if (props.containsKey("disabled")) {
//                            props.put("disabled", true);
//                        }
//                    }
//                    String updatedJsonStr = jsonObject.toJSONString();
//                    updatedFormFields.add(updatedJsonStr);
//                }
//
//                // 将updatedFormFields重新赋值给formFields
//                formFields=updatedFormFields;
//            }
                processDefinitionExt.setFormFields(formFields);
        }

        String bpmnXml = processDefinitionService.getProcessDefinitionBpmnXML(processInstance.getProcessDefinitionId());

        // 获得 User
        AdminUserRespDTO startUser = adminUserApi.getUser(NumberUtils.parseLong(processInstance.getStartUserId())).getCheckedData();
        DeptRespDTO dept = null;
        if (startUser.getDeptId() != null) {
            dept = deptApi.getDept(startUser.getDeptId()).getCheckedData();
        }

        // 拼接结果
        return BpmProcessInstanceConvert.INSTANCE.convert2(processInstance, processInstanceExt,
                processDefinition, processDefinitionExt, bpmnXml, startUser, dept,isApprovalUser);
    }

    @Override
    public void cancelProcessInstance(Long userId, @Valid BpmProcessInstanceCancelReqVO cancelReqVO) {
        // 校验流程实例存在
        ProcessInstance instance = getProcessInstance(cancelReqVO.getId());
        if (instance == null) {
            throw exception(PROCESS_INSTANCE_CANCEL_FAIL_NOT_EXISTS);
        }
//        // 只能取消自己的
//        if (!Objects.equals(instance.getStartUserId(), String.valueOf(userId))) {
//            throw exception(PROCESS_INSTANCE_CANCEL_FAIL_NOT_SELF);
//        }

        BpmProcessInstanceExtDO processInstanceExtDO = processInstanceExtMapper.selectByProcessInstanceId(instance.getId());
        List<String> eventProcessNum = dictDataApi.getDictDataListByType("bpm_process_number").getData();
        for (String item:eventProcessNum) {
            if(processInstanceExtDO.getProcessNumber().toLowerCase().contains(item.toLowerCase())){
                Long recordeId = Long.valueOf(String.valueOf(processInstanceExtDO.getFormVariables().get("requestId")));
                taskOver(instance.getProcessInstanceId(),recordeId);
                processInstanceExtDO.setIsFallBack(1);
                processInstanceExtMapper.updateById(processInstanceExtDO);
            }
        }

        // 通过删除流程实例，实现流程实例的取消,
        // 删除流程实例，正则执行任务 ACT_RU_TASK. 任务会被删除。通过历史表查询
        deleteProcessInstance(cancelReqVO.getId(),
                BpmProcessInstanceDeleteReasonEnum.CANCEL_TASK.format(cancelReqVO.getReason()));
    }

    /**
     * 获得历史的流程实例
     *
     * @param id 流程实例的编号
     * @return 历史的流程实例
     */
    @Override
    public HistoricProcessInstance getHistoricProcessInstance(String id) {
        return historyService.createHistoricProcessInstanceQuery().processInstanceId(id).singleResult();
    }

    @Override
    public List<HistoricProcessInstance> getHistoricProcessInstances(Set<String> ids) {
        return historyService.createHistoricProcessInstanceQuery().processInstanceIds(ids).list();
    }

    @Override
    public void createProcessInstanceExt(ProcessInstance instance) {
        // 获得流程定义
        ProcessDefinition definition = processDefinitionService.getProcessDefinition2(instance.getProcessDefinitionId());
        // 插入 BpmProcessInstanceExtDO 对象
        BpmProcessInstanceExtDO instanceExtDO = new BpmProcessInstanceExtDO()
                .setProcessInstanceId(instance.getId())
                .setProcessDefinitionId(definition.getId())
                .setName(instance.getProcessDefinitionName())
                .setStartUserId(Long.valueOf(instance.getStartUserId()))
                .setCategory(definition.getCategory())
                .setStatus(BpmProcessInstanceStatusEnum.RUNNING.getStatus())
                .setResult(BpmProcessInstanceResultEnum.PROCESS.getResult());

        processInstanceExtMapper.insert(instanceExtDO);
    }

    @Override
    public void updateProcessInstanceExtCancel(FlowableCancelledEvent event) {
        // 判断是否为 Reject 不通过。如果是，则不进行更新.
        // 因为，updateProcessInstanceExtReject 方法，已经进行更新了
        if (BpmProcessInstanceDeleteReasonEnum.isRejectReason((String) event.getCause())) {
            return;
        }

        // 需要主动查询，因为 instance 只有 id 属性
        // 另外，此时如果去查询 ProcessInstance 的话，字段是不全的，所以去查询了 HistoricProcessInstance
        HistoricProcessInstance processInstance = getHistoricProcessInstance(event.getProcessInstanceId());
        // 更新拓展表
        BpmProcessInstanceExtDO instanceExtDO = new BpmProcessInstanceExtDO()
                .setProcessInstanceId(event.getProcessInstanceId())
                .setEndTime(LocalDateTime.now()) // 由于 ProcessInstance 里没有办法拿到 endTime，所以这里设置
                .setStatus(BpmProcessInstanceStatusEnum.FINISH.getStatus())
                .setResult(BpmProcessInstanceResultEnum.CANCEL.getResult());
        processInstanceExtMapper.updateByProcessInstanceId(instanceExtDO);

        // 发送流程实例的状态事件
        processInstanceResultEventPublisher.sendProcessInstanceResultEvent(
                BpmProcessInstanceConvert.INSTANCE.convert(this, processInstance, instanceExtDO.getResult()));
    }

    @Override
    public void updateProcessInstanceExtComplete(ProcessInstance instance) {
        // 需要主动查询，因为 instance 只有 id 属性
        // 另外，此时如果去查询 ProcessInstance 的话，字段是不全的，所以去查询了 HistoricProcessInstance
        HistoricProcessInstance processInstance = getHistoricProcessInstance(instance.getId());
        // 更新拓展表
        BpmProcessInstanceExtDO instanceExtDO = new BpmProcessInstanceExtDO()
                .setProcessInstanceId(instance.getProcessInstanceId())
                .setEndTime(LocalDateTime.now()) // 由于 ProcessInstance 里没有办法拿到 endTime，所以这里设置
                .setStatus(BpmProcessInstanceStatusEnum.FINISH.getStatus())
                .setResult(BpmProcessInstanceResultEnum.APPROVE.getResult()); // 如果正常完全，说明审批通过
        processInstanceExtMapper.updateByProcessInstanceId(instanceExtDO);

        // 发送流程被通过的消息
        messageService.sendMessageWhenProcessInstanceApprove(BpmProcessInstanceConvert.INSTANCE.convert2ApprovedReq(instance));

        // 发送流程实例的状态事件
        processInstanceResultEventPublisher.sendProcessInstanceResultEvent(
                BpmProcessInstanceConvert.INSTANCE.convert(this, processInstance, instanceExtDO.getResult()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProcessInstanceExtReject(String id, String reason) {
        // 需要主动查询，因为 instance 只有 id 属性
        ProcessInstance processInstance = getProcessInstance(id);
        // 删除流程实例，以实现驳回任务时，取消整个审批流程
        deleteProcessInstance(id, StrUtil.format(BpmProcessInstanceDeleteReasonEnum.REJECT_TASK.format(reason)));

        // 更新 status + result
        // 注意，不能和上面的逻辑更换位置。因为 deleteProcessInstance 会触发流程的取消，进而调用 updateProcessInstanceExtCancel 方法，
        // 设置 result 为 BpmProcessInstanceStatusEnum.CANCEL，显然和 result 不一定是一致的
        BpmProcessInstanceExtDO instanceExtDO = new BpmProcessInstanceExtDO().setProcessInstanceId(id)
                .setStatus(BpmProcessInstanceStatusEnum.FINISH.getStatus())
                .setResult(BpmProcessInstanceResultEnum.REJECT.getResult());
        processInstanceExtMapper.updateByProcessInstanceId(instanceExtDO);

        // 发送流程被不通过的消息
        messageService.sendMessageWhenProcessInstanceReject(BpmProcessInstanceConvert.INSTANCE.convert2RejectReq(processInstance, reason));

        // 发送流程实例的状态事件
        processInstanceResultEventPublisher.sendProcessInstanceResultEvent(
                BpmProcessInstanceConvert.INSTANCE.convert(this, processInstance, instanceExtDO.getResult()));
    }

    @Override
    public Map<String, Object> getProcessInfo(Long alarmId, Integer type) {
        Map<String,Object> processInfo = new HashMap<>();
        //利用字典项来确认任务节点
        List<DictDataRespDTO> dictDataRespDTOS = dictDataApi.getDictDataInfoByType("processNodes").getData();
        if(type==1){
            //流程节点回退视作没有到达该节点，获取新的节点任务
            //专用于查询问题工单实例
            BpmProcessInstanceExtDO instanceExtDO = processInstanceExtMapper.getProcessInfoByAlarmId(alarmId);
//            获取创建人信息
            AdminUserRespDTO userInfo = adminUserApi.getUser(instanceExtDO.getStartUserId()).getData();
            if(ObjectUtil.isEmpty(instanceExtDO)){
                return processInfo;
            }
            List<BpmTaskRespVO> taskList = taskService.getProblemTasksInfo(instanceExtDO.getProcessInstanceId(),dictDataRespDTOS);
            Map<String, BpmTaskRespVO> taskMap= CollectionUtils.convertMap(taskList,BpmTaskRespVO::getActId);
//            判断任务流程是否结束
            Boolean isOver = true;
            //判断是否存在确认节点
            for (DictDataRespDTO dictData:dictDataRespDTOS) {
                BpmTaskRespVO task = taskMap.get(dictData.getValue());
                switch (dictData.getLabel()) {
                    case "创建节点": {
                        //通过字典项来判断任务节点，并获取信息
                        Map<String, Object> createOrder = new HashMap<>();
                        createOrder.put("assigneeName", userInfo.getNickname());
                        createOrder.put("assignee", userInfo.getId());
                        createOrder.put("createTime", DateUtil.format(task.getStartTime(), "yyyy-MM-dd HH:mm:ss"));
                        processInfo.put("createOrder", createOrder);
                    }
                    continue;
                    case "处理节点": {
                        Map<String, Object> processing = new HashMap<>();
                        if (ObjectUtil.isNotEmpty(task)){
                            if (ObjectUtil.isNotEmpty(task.getEndTime())&&ObjectUtil.isNotEmpty(task.getResult())) {
                                processing.put("assigneeName", task.getAssigneeName());
                                processing.put("assignee", task.getAssignee());
                                processing.put("endTime", DateUtil.format(task.getStartTime(), "yyyy-MM-dd HH:mm:ss"));
                                // 获取表单字段
                                String definitionId = task.getProcessDefId();
                                String formFields = flowNodeMapper.getFormFieldsByDefinitionId(definitionId);
                                String result = "";

                                if (StringUtil.isNotEmpty(formFields)) {
                                    // 查询表单对应审批建议字段
                                    Gson gson = new Gson();
                                    JsonArray jsonArray = JsonParser.parseString(formFields).getAsJsonArray();
                                    String fieldValue = null;

                                    for (int i = 0; i < jsonArray.size(); i++) {
                                        JsonObject jsonObject = gson.fromJson(jsonArray.get(i).getAsString(), JsonObject.class);
                                        if ("审批意见".equals(jsonObject.get("title").getAsString())) {
                                            fieldValue = jsonObject.get("field").getAsString();
                                            break;
                                        }
                                    }

                                    if (fieldValue != null) {
                                        log.info("审批意见的 field 值是: {}", fieldValue);
                                        // 获取当前节点的数据
                                        String processInstanceId = task.getProcessInstanceId();
                                        String formVariables = flowNodeMapper.getFormVariablesByInstanceId(processInstanceId);
                                        JsonObject formVariablesObject = gson.fromJson(formVariables, JsonObject.class);
                                        result = formVariablesObject.get(fieldValue).getAsString();
                                    }
                                }
                                // 设置理由
                                processing.put("reason", StringUtil.isNotEmpty(result) ? result : task.getReason());
                                processInfo.put("processing", processing);
                            } else {
                                processInfo.put("processing", processing);
                                isOver = false;
                            }
                        } else {
                            processInfo.put("processing", processing);
                            isOver = false;
                        }
                    }
                    continue;
                    case "确认节点": {
                        Map<String, Object> confirm = new HashMap<>();
                        //如果存在确认节点，则判断确认节点任务的完成状态，确认节点任务完成则流程结束
                        //如果不存在确认节点，那么审批和处理节点完成后刘成就结束
                        if (ObjectUtil.isNotEmpty(task) &&
                            ObjectUtil.isNotEmpty(task.getEndTime()) && task.getResult() == 2) {
                            confirm.put("assigneeName", task.getAssigneeName());
                            confirm.put("assignee", task.getAssignee());
                            confirm.put("endTime", DateUtil.format(task.getStartTime(), "yyyy-MM-dd HH:mm:ss"));
                            confirm.put("reason", task.getReason());
                            processInfo.put("confirm", confirm);
                            isOver = true;
                        }else {
                            processInfo.put("confirm", confirm);
                            isOver = false;
                        }
                    }
                    continue;
                }
            }
            processInfo.put("processEnd",isOver);
        }
        return processInfo;
    }

    @Override
    @TenantIgnore
    public String getProcessInstanceStartId(String id) {
        return flowNodeMapper.getProcessInstanceStartId(id);
    }

    private void deleteProcessInstance(String id, String reason) {
        runtimeService.deleteProcessInstance(id, reason);
    }

    private String createProcessInstance0(Long userId, ProcessDefinition definition,
                                          Map<String, Object> variables, String businessKey) {
        LoginUser currentUser = SecurityFrameworkUtils.getLoginUser();
        AdminUserRespDTO userDTO = adminUserApi.getUser(currentUser.getId()).getData();
        // 校验流程定义
        if (definition == null) {
            throw exception(PROCESS_DEFINITION_NOT_EXISTS);
        }
        if (definition.isSuspended()) {
            throw exception(PROCESS_DEFINITION_IS_SUSPENDED);
        }

        // 创建流程实例
        ProcessInstance instance = runtimeService.createProcessInstanceBuilder()
                .processDefinitionId(definition.getId())
                .tenantId(String.valueOf(userDTO.getTenantId()))
                .businessKey(businessKey)
                .name(definition.getName().trim())
                .variables(variables)
                .start();
        // 设置流程名字
        runtimeService.setProcessInstanceName(instance.getId(), definition.getName());
        // 查询流程编号
        String number=flowNodeMapper.getNumberByInstanceId(instance.getId());
        //查询当前年份编号出现次数
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        String yearNumber="%"+number+year+"%";
        int count=flowNodeMapper.getNumberCount(yearNumber);
        //当前时间
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmm");
        String currentDateTime = dateFormat.format(calendar.getTime());
        //最终编号
        String processNumber = number+currentDateTime+String.format("%03d", count+1);
        flowNodeMapper.updateProcessNumberById(instance.getId(),processNumber);

        //添加进创建的实例表单
        String definitionId = definition.getId();
        String formName = flowNodeMapper.getNameByDefinitionId(definitionId);
        String tableName = "bpm_form_"+formName;


//        if (variables != null) {
//            try {
//                // 连接到 MySQL 数据库
//                Connection connection = DriverManager.getConnection(url, username, password);
//                // 创建表单
//                String createTableQuery = "INSERT IGNORE INTO " + tableName ;
//                String keys = "(";
//                String values = "(";
//                for (String key : variables.keySet()) {
//                    Object value = variables.get(key);
//                    keys +="`"+key+ "`,";
//                    values += "'"+value + "',";
//                }
//                keys += "`process_instance_id`, `process_definition_id`, `creator`)";
//                values += "'"+ instance.getId() + "','" + definition.getId() + "','" + WebFrameworkUtils.getLoginUserId() +"')";
//                createTableQuery += keys + " VALUES " + values;
//                System.out.println(createTableQuery);
//                // 执行 CREATE TABLE 语句
//                Statement statement = connection.createStatement();
//                statement.executeUpdate(createTableQuery);
//                // 关闭连接
//                statement.close();
//                connection.close();
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
        BpmProcessInstanceExtDO extDO = new BpmProcessInstanceExtDO();
        extDO.setProcessInstanceId(instance.getId());
        extDO.setFormVariables(variables);
        extDO.setTenantId(userDTO.getTenantId());
        // 补全流程实例的拓展表
        processInstanceExtMapper.updateByProcessInstanceId(extDO);
        //更改告警处理流程的状态
        List<String> eventProcessNum = dictDataApi.getDictDataListByType("bpm_process_number").getData();
        for (String item:eventProcessNum) {
            if(processNumber.toLowerCase().contains(item.toLowerCase())){
//                String table = String.valueOf(extDO.getFormVariables().get("table"));
                Long alarmId =Long.valueOf(String.valueOf(extDO.getFormVariables().get("requestId")));
                alarmConfigApi.changeAlarmSolvedState(alarmId,1);
            }
        }
        //发送信息
        List<ReportSubscriptionDTO> list = flowNodeMapper.getRoleExistence();
        if (CollUtil.isNotEmpty(list)) {
            String format = DateUtil.format(new Date(), "yyyy年MM月dd日 hh:mm");
            String content = "新工单已创建，请注意查收";
            Long platformId = Convert.toLong(variables.get("platformId"));
            PlatformconfigDTO platformconfig = platformconfigApi.getByConfigId(platformId).getData();
            String platformName = platformconfig.getName();
            String name = "新工单提醒";

            // 处理微信通知
            sendWeChatNotifications(list, format, content, platformName, processNumber,userDTO.getNickname());

            // 处理邮件通知
            sendEmailNotifications(list, format, content, platformName, name,processNumber,userDTO.getNickname());
        }
        return instance.getId();
    }

    private void taskOver(String processInstanceId,Long recordeId){
        //先查历史表
        HistoricProcessInstance historicProcessInstance =historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        if(Objects.isNull(historicProcessInstance)) {
            throw exception(PROCESS_INSTANCE_NOT_EXISTS);
        }
        //取消，状态改为未创工单
        alarmConfigApi.changeAlarmIsFallback(recordeId, 1);
    }

    // 发送微信通知的方法
    private void sendWeChatNotifications(List<ReportSubscriptionDTO> list, String format, String content, String platformName, String processNumber,String nickname) {
        List<ReportSubscriptionDTO> wechatList = list.stream()
                .filter(x -> x.getWechatState() == 1 && StringUtil.isNotEmpty(x.getOpenId()))
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(wechatList)) {
            wechatList.forEach(item -> {
                Map<String, Object> templateParams = createTemplateParams(platformName, content, item.getOpenId(), processNumber, format,nickname);
                weChatSendApi.sendBpmMessage(templateParams);
                log.info(item.getUserName() + "的消息已送达 "+JSON.toJSONString(templateParams));
            });
        }
    }
    // 发送邮件通知的方法
    private void sendEmailNotifications(List<ReportSubscriptionDTO> list, String format, String content, String platformName, String templateName,String processNumber,String nickname) {
        List<ReportSubscriptionDTO> emailList = list.stream()
                .filter(x -> x.getEmailState() == 1 && StringUtil.isNotEmpty(x.getEmail()))
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(emailList)) {
            emailList.forEach(item -> {
                Map<String, Object> templateParams = createTemplateParams(platformName, content, item.getOpenId(), processNumber, format,nickname);
                MailSendSingleToUserReqDTO reqDTO = new MailSendSingleToUserReqDTO();
                CommonResult<String> commonResult = mailSendApi.getTemplateByName(templateName);
                reqDTO.setTemplateCode(commonResult.getData());
                reqDTO.setTemplateParams(templateParams);
                reqDTO.setMail(item.getEmail());
                reqDTO.setUserId(item.getUserId());
                mailSendApi.sendSingleMailToBpm(reqDTO).getData();
                log.info(item.getUserName() + "的邮件已送达 "+JSON.toJSONString(reqDTO));
            });
        }
    }

    // 创建模板参数的方法
    private Map<String, Object> createTemplateParams(String platformName, String content, String openId, String processNumber, String createTime, String nickname) {
        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("platformName", platformName);
        templateParams.put("content", content);
        templateParams.put("openId", openId);
        templateParams.put("number", processNumber);
        templateParams.put("createTime", createTime);
        templateParams.put("nickname", nickname);
        templateParams.put("url", "www.baidu.com");
        return templateParams;
    }
}
