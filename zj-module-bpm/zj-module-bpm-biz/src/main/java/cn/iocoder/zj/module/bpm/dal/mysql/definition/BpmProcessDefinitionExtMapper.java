package cn.iocoder.zj.module.bpm.dal.mysql.definition;


import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.zj.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.zj.framework.mybatis.core.query.QueryWrapperX;
import cn.iocoder.zj.module.bpm.dal.dataobject.definition.BpmProcessDefinitionExtDO;
import cn.iocoder.zj.module.bpm.dal.dataobject.definition.BpmTaskAssignRuleDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface BpmProcessDefinitionExtMapper extends BaseMapperX<BpmProcessDefinitionExtDO> {

    default List<BpmProcessDefinitionExtDO> selectListByProcessDefinitionIds(Collection<String> processDefinitionIds,Long tenantId,List<String> keyList) {
        QueryWrapperX<BpmProcessDefinitionExtDO> wrapperX =  new QueryWrapperX<BpmProcessDefinitionExtDO>()
                .eq("tenant_id",tenantId)
                .in("process_definition_id",processDefinitionIds);
        if(CollectionUtil.isNotEmpty(keyList)){
            wrapperX.or().in("`process_key`",keyList);
        }
        return selectList(wrapperX);
    }

    default BpmProcessDefinitionExtDO selectByProcessDefinitionId(String processDefinitionId) {
        return selectOne("process_definition_id", processDefinitionId);
    }

    default BpmProcessDefinitionExtDO selectBymodelId(String modelId) {
        return selectOne(new QueryWrapperX<BpmProcessDefinitionExtDO>()
                .eq("model_id", modelId)
                .orderByDesc("update_time")
                .last("limit 1"));
    }

    default List<BpmProcessDefinitionExtDO> selectListByModelIds(List<String> model){
        return selectList("model_id", model);
    }
}
