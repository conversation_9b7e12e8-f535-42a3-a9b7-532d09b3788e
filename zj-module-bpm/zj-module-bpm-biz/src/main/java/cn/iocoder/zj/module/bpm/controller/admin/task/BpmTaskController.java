package cn.iocoder.zj.module.bpm.controller.admin.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.pojo.CommonResult;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.framework.security.core.LoginUser;
import cn.iocoder.zj.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.zj.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.zj.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.zj.module.bpm.controller.admin.task.vo.task.*;
import cn.iocoder.zj.module.bpm.dal.mysql.definition.FlowNodeMapper;
import cn.iocoder.zj.module.bpm.service.task.BpmTaskService;
import cn.iocoder.zj.module.system.api.permission.PermissionApi;
import cn.iocoder.zj.module.system.api.permission.RoleApi;
import cn.iocoder.zj.module.system.api.platformconfig.PlatformconfigApi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;
import static cn.iocoder.zj.framework.common.util.collection.CollectionUtils.singleton;
import static cn.iocoder.zj.framework.web.core.util.WebFrameworkUtils.getLoginUserId;
import static cn.iocoder.zj.module.bpm.enums.ErrorCodeConstants.TASK_NOT_TASKIDS;

@Tag(name = "管理后台 - 流程任务实例")
@RestController
@RequestMapping("/bpm/task")
@Validated
public class BpmTaskController {

    @Resource
    private BpmTaskService taskService;
    @Resource
    private PlatformconfigApi platformconfigApi;
    @Autowired
    PermissionApi permissionApi;
    @Autowired
    RoleApi roleApi;
    @Autowired
    FlowNodeMapper flowNodeMapper;

    @GetMapping("todo-page")
    @Operation(summary = "获取 Todo 待办任务分页")
    public CommonResult<PageResult<BpmTaskTodoPageItemRespVO>> getTodoTaskPage(@Valid BpmTaskTodoPageReqVO pageVO) {
        return success(taskService.getTodoTaskPage(getLoginUserId(), pageVO));
    }

    @GetMapping("done-page")
    @Operation(summary = "获取 Done 已办任务分页")
    public CommonResult<PageResult<BpmTaskDonePageItemRespVO>> getDoneTaskPage(@Valid BpmTaskDonePageReqVO pageVO) {
        return success(taskService.getDoneTaskPage(getLoginUserId(), pageVO));
    }

    @GetMapping("/list-by-process-instance-id")
    @Operation(summary = "获得指定流程实例的任务列表", description = "包括完成的、未完成的")
    @Parameter(name = "processInstanceId", description = "流程实例的编号", required = true)
    public CommonResult<List<BpmTaskRespVO>> getTaskListByProcessInstanceId(
            @RequestParam("processInstanceId") String processInstanceId) {
        return success(taskService.getTaskListByProcessInstanceId(processInstanceId));
    }

    @PutMapping("/approve")
    @Operation(summary = "通过任务")
    @PreAuthorize("@ss.hasPermission('bpm:task:update')")
    public CommonResult<Boolean> approveTask(@Valid @RequestBody BpmTaskApproveReqVO reqVO) {
        taskService.approveTask(getLoginUserId(), reqVO);
        return success(true);
    }

    @PutMapping("/reject")
    @Operation(summary = "不通过任务")
    @PreAuthorize("@ss.hasPermission('bpm:task:update')")
    public CommonResult<Boolean> rejectTask(@Valid @RequestBody BpmTaskRejectReqVO reqVO) {
        taskService.rejectTask(getLoginUserId(), reqVO);
        return success(true);
    }

    @PutMapping("/update-assignee")
    @Operation(summary = "更新任务的负责人", description = "用于【流程详情】的【转派】按钮")
    @PreAuthorize("@ss.hasPermission('bpm:task:update')")
    public CommonResult<Boolean> updateTaskAssignee(@Valid @RequestBody BpmTaskUpdateAssigneeReqVO reqVO) {
        taskService.updateTaskAssignee(getLoginUserId(), reqVO);
        return success(true);
    }

    @GetMapping("/return-list")
    @Operation(summary = "获取所有可回退的节点", description = "用于【流程详情】的【回退】按钮")
    @Parameter(name = "taskId", description = "当前任务ID", required = true)
    @PreAuthorize("@ss.hasPermission('bpm:task:update')")
    public CommonResult<List<BpmTaskSimpleRespVO>> getReturnList(@RequestParam("taskId") String taskId) {
        return success(taskService.getReturnTaskList(taskId));
    }

    @PutMapping("/return")
    @Operation(summary = "回退任务", description = "用于【流程详情】的【回退】按钮")
    @PreAuthorize("@ss.hasPermission('bpm:task:update')")
    public CommonResult<Boolean> returnTask(@Valid @RequestBody BpmTaskReturnReqVO reqVO) {
        taskService.returnTask(getLoginUserId(), reqVO);
        return success(true);
    }

    @PutMapping("/delegate")
    @Operation(summary = "委派任务", description = "用于【流程详情】的【委派】按钮。和向前【加签】有点像，唯一区别是【委托】没有单独创立任务")
    @PreAuthorize("@ss.hasPermission('bpm:task:update')")
    public CommonResult<Boolean> delegateTask(@Valid @RequestBody BpmTaskDelegateReqVO reqVO) {
        taskService.delegateTask(getLoginUserId(), reqVO);
        return success(true);
    }

    @PutMapping("/create-sign")
    @Operation(summary = "加签", description = "before 前加签，after 后加签")
    @PreAuthorize("@ss.hasPermission('bpm:task:update')")
    public CommonResult<Boolean> createSignTask(@Valid @RequestBody BpmTaskAddSignReqVO reqVO) {
        taskService.createSignTask(getLoginUserId(), reqVO);
        return success(true);
    }

    @DeleteMapping("/delete-sign")
    @Operation(summary = "减签")
    @PreAuthorize("@ss.hasPermission('bpm:task:update')")
    public CommonResult<Boolean> deleteSignTask(@Valid @RequestBody BpmTaskSubSignReqVO reqVO) {
        taskService.deleteSignTask(getLoginUserId(), reqVO);
        return success(true);
    }

    @GetMapping("children-list")
    @Operation(summary = "获取能被减签的任务")
    @Parameter(name = "parentId", description = "父级任务 ID", required = true)
    @PreAuthorize("@ss.hasPermission('bpm:task:update')")
    public CommonResult<List<BpmTaskSubSignRespVO>> getChildrenTaskList(@RequestParam("parentId") String parentId) {
        return success(taskService.getChildrenTaskList(parentId));
    }

    @GetMapping("/getUnsolvedTask")
    @Operation(summary = "获得最近待审批的流程")
    @TenantIgnore
    public CommonResult<List<Map<String, String>>> getUnsolvedTask() {
        List<Map<String, String>> list = taskService.getUnsolvedTask();
        return success(list);
    }


    @PutMapping("/updateTaskIsRead")
    @Operation(summary = "修改是否已读")
    @PreAuthenticated
    @TenantIgnore
    public CommonResult<Boolean> updateIsRead(@RequestParam("taskIds") Collection<String> taskIds) {
        if (taskIds.size() == 0) {
            throw exception(TASK_NOT_TASKIDS);
        }
        taskService.updateIsRead(taskIds);
        return success(true);
    }

    @GetMapping("todo-page-list")
    @Operation(summary = "获取 Todo 列表")
    public CommonResult<PageResult<BpmTaskTodoPageItemRespVO>> getTodoTaskList(@Valid BpmTaskTodoPageReqVO pageVO) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        List<Map> platform = platformconfigApi.getPlatformSelectList(loginUser.getTenantId().toString()).getData();
        Set<Long> roleIds = permissionApi.getUserRoleIdsFromCache(loginUser.getId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        // 如果是租户管理员
        PageResult<BpmTaskTodoPageItemRespVO> pageResult = taskService.getTodoTaskList(getLoginUserId(), pageVO);
        Long size = pageResult.getTotal();
        if(CollectionUtil.isEmpty(platform)){
            pageResult.setTotal(0L);
            return success(pageResult);
        }
        List<String> list = new ArrayList<>();
        for (Map map : platform) {
            list.add(Convert.toStr(map.get("platformId")));
        }

        List<BpmTaskTodoPageItemRespVO> bpmTaskTodoPageItemRespVOS = new ArrayList<>();
        if (roleApi.hasAnyTenantAdmin(roleIds) || roleApi.hasAnySuperAdmin(roleIds)) {
            List<Map> map = flowNodeMapper.getMonitorAssetLists(list);
            for (Map map1 : map) {
                BpmTaskTodoPageItemRespVO bpmTaskTodoPageItemRespVO = new BpmTaskTodoPageItemRespVO();
                bpmTaskTodoPageItemRespVO.setId(Convert.toStr(map1.get("id")));
                bpmTaskTodoPageItemRespVO.setType(1L);
                Date date = DateUtil.parse(map1.get("createTime").toString());
                bpmTaskTodoPageItemRespVO.setCreateTime(DateUtil.toLocalDateTime(date));
                Map map2 = new HashMap();
                map2.put("assetName", Convert.toStr(map1.get("assetName")));
                map2.put("hostName", Convert.toStr(map1.get("hostName")));

                String jsonStr = JSONUtil.toJsonStr(map2);
                bpmTaskTodoPageItemRespVO.setAssetJson(jsonStr);
                bpmTaskTodoPageItemRespVOS.add(bpmTaskTodoPageItemRespVO);
            }
            bpmTaskTodoPageItemRespVOS.addAll(pageResult.getList());
            pageResult.setList(bpmTaskTodoPageItemRespVOS);
            int page = bpmTaskTodoPageItemRespVOS.size();
            pageResult.setTotal(Integer.toUnsignedLong(page));
            // 如果大于了分页条数则删除最后多出的几条数据
            if (pageResult.getList().size() > pageVO.getPageSize()) {
                List<BpmTaskTodoPageItemRespVO> sublist = pageResult.getList().subList(0, pageVO.getPageSize());
                pageResult.setList(new ArrayList<>(sublist));
            }
        }

        pageResult.getList().stream().sorted(Comparator.comparing(BpmTaskTodoPageItemRespVO::getCreateTime, Comparator.reverseOrder()));
        return success(pageResult);
    }

}