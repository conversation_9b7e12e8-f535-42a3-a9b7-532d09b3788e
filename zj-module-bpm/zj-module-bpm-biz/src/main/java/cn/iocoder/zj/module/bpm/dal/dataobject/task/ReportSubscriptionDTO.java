package cn.iocoder.zj.module.bpm.dal.dataobject.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ReportSubscriptionDTO {
    private Long userId;
    @Schema(description = "微信推送启用状态，0未启用，1已启用")
    private Integer wechatState;
    @Schema(description = "短信推送启用状态，0未启用，1已启用")
    private Integer emailState;
    @Schema(description = "钉钉推送启用状态，0未启用，1已启用")
    private Integer dingtalkState;
    @Schema(description = "企微推送启用状态，0未启用，1已启用")
    private Integer wecomState;
    @Schema(description = "用户邮箱")
    private String email;
    @Schema(description = "平台ID")
    private String platformId;
    @Schema(description = "用户唯一openId")
    private String openId;

    @Schema(description = "钉钉应用key")
    private String appKey;

    @Schema(description = "钉钉应用秘钥")
    private String appSecret;

    @Schema(description = "钉钉应用id")
    private String agentId;

    @Schema(description = "钉钉手机号")
    private String dingtalkPhone;

    @Schema(description = "企微应用id")
    private String wecomAgentId;

    @Schema(description = "企微企业id")
    private String corpid;

    @Schema(description = "企微企业秘钥")
    private String corpsecret;

    @Schema(description = "企微手机号")
    private String wecomPhone;

    private String userName;
}
