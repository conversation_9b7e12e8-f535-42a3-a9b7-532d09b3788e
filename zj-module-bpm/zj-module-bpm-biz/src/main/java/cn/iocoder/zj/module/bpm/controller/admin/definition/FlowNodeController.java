package cn.iocoder.zj.module.bpm.controller.admin.definition;

import cn.iocoder.zj.framework.common.enums.CommonStatusEnum;
import cn.iocoder.zj.framework.common.pojo.PageResult;
import cn.iocoder.zj.module.bpm.controller.admin.definition.vo.group.BpmUserGroupRespVO;
import cn.iocoder.zj.module.bpm.controller.admin.definition.vo.node.*;
import cn.iocoder.zj.module.bpm.convert.definition.BpmUserGroupConvert;
import cn.iocoder.zj.module.bpm.convert.definition.FlowNodeConvert;
import cn.iocoder.zj.module.bpm.dal.dataobject.definition.BpmUserGroupDO;
import cn.iocoder.zj.module.bpm.dal.dataobject.definition.FlowNodeDO;
import cn.iocoder.zj.module.bpm.service.definition.FlowNodeService;
import org.dom4j.DocumentException;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;


import javax.validation.*;
import java.util.*;


import cn.iocoder.zj.framework.common.pojo.CommonResult;
import static cn.iocoder.zj.framework.common.pojo.CommonResult.success;



@Tag(name = "管理后台 - 工作流表单的节点定义")
@RestController
@RequestMapping("/bpm/flow-node")
@Validated
public class FlowNodeController {

    @Resource
    private FlowNodeService flowNodeService;

    @PostMapping("/create")
    @Operation(summary = "创建工作流表单的节点定义")
//    @PreAuthorize("@ss.hasPermission('bpm:flow-node:create')")
    public CommonResult<Boolean> createFlowNode(@Valid @RequestBody List<FlowNodeDO> list) {
        return success(flowNodeService.createFlowNode(list));
    }

    @PutMapping("/update")
    @Operation(summary = "更新工作流表单的节点定义")
//    @PreAuthorize("@ss.hasPermission('bpm:flow-node:update')")
    public CommonResult<Boolean> updateFlowNode(@Valid @RequestBody FlowNodeDO flowNodeDO) throws DocumentException {
        flowNodeService.updateFlowNode(flowNodeDO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除工作流表单的节点定义")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('bpm:flow-node:delete')")
    public CommonResult<Boolean> deleteFlowNode(@RequestParam("id") Long id) {
        flowNodeService.deleteFlowNode(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得工作流表单的节点定义")
    public CommonResult<FlowNodeDO> getFlowNode(FlowNodeBaseVO flowNodeBaseVO) throws DocumentException {
        FlowNodeDO flowNode = flowNodeService.getFlowNode(flowNodeBaseVO);
        return success(flowNode);
    }


    @GetMapping("/getNodeForm")
    @Operation(summary = "获得流程节点表单")
    public CommonResult<FlowNodeDO> getNodeForm(@RequestParam("processInstanceId") String processInstanceId) {
        FlowNodeDO flowNode = flowNodeService.getNodeForm(processInstanceId);
        return success(flowNode);
    }


    @GetMapping("/list")
    @Operation(summary = "获得工作流表单的节点定义列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    public CommonResult<List<FlowNodeRespVO>> getFlowNodeList(@RequestParam("ids") Collection<Long> ids) {
        List<FlowNodeDO> list = flowNodeService.getFlowNodeList(ids);
        return success(FlowNodeConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得工作流表单的节点定义分页")
    public CommonResult<PageResult<FlowNodeRespVO>> getFlowNodePage(@Valid FlowNodePageReqVO pageVO) {
        PageResult<FlowNodeDO> pageResult = flowNodeService.getFlowNodePage(pageVO);
        return success(FlowNodeConvert.INSTANCE.convertPage(pageResult));
    }

}
