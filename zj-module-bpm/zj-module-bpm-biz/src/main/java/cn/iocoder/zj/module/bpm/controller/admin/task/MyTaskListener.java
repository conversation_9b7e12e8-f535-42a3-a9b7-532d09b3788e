package cn.iocoder.zj.module.bpm.controller.admin.task;

import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.zj.module.bpm.service.task.BpmProcessInstanceService;
import com.alibaba.cloud.commons.lang.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static cn.iocoder.zj.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.zj.module.bpm.enums.ErrorCodeConstants.TASK_CREATE_FAIL_NO_CANDIDATE_USER;
@Slf4j
@Component
public class MyTaskListener implements TaskListener {
//    @Value("${bpm.task-listener.node-id.confirm-id}")
    private String confirmNode="Activity_0qdl7yk";

    @Autowired(required = false)
    private BpmProcessInstanceService processInstance = SpringUtil.getBean(BpmProcessInstanceService.class);
    @Resource
    private TaskService taskExtMapper = SpringUtil.getBean(TaskService.class);
    @Override
    public void notify(DelegateTask delegateTask) {
        ProcessInstance historicProcessInstance = processInstance.getProcessInstance(delegateTask.getProcessInstanceId());
            String assignee = "";
            if(delegateTask.getTaskDefinitionKey().equals(confirmNode)){
                if (!StringUtils.isEmpty(historicProcessInstance.getStartUserId())) {
                    assignee = historicProcessInstance.getStartUserId();
                }
                log.info("下一节点处理人是："+assignee);
                if(StringUtils.isNotEmpty(assignee)) {
                    delegateTask.setAssignee(assignee);
                }else {
                    throw exception(TASK_CREATE_FAIL_NO_CANDIDATE_USER);
                }
            }
    }
}
