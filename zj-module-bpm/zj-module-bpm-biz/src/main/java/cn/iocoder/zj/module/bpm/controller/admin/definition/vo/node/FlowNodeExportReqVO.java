package cn.iocoder.zj.module.bpm.controller.admin.definition.vo.node;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.zj.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.zj.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 工作流表单的节点定义 Excel 导出 Request VO，参数和 FlowNodePageReqVO 是一致的")
@Data
public class FlowNodeExportReqVO {

    @Schema(description = "节点id")
    private String nodeId;

    @Schema(description = "节点名称")
    private String nodeName;

    @Schema(description = "表单id")
    private Long fromId;

    @Schema(description = "模型id")
    private String modelId;

    @Schema(description = "节点表单项的配置")
    private String nodeFields;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
