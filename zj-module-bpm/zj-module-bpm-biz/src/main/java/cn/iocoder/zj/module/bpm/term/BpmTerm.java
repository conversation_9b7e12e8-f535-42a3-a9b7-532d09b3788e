package cn.iocoder.zj.module.bpm.term;

import cn.iocoder.zj.module.bpm.dal.mysql.definition.FlowNodeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class BpmTerm {

    private final FlowNodeMapper flowNodeMapper;

    public BpmTerm(FlowNodeMapper flowNodeMapper) {
        this.flowNodeMapper = flowNodeMapper;
        expiration();
    }

    public void expiration() {
        ScheduledExecutorService executor = Executors.newScheduledThreadPool(1);
        Runnable task = new Runnable() {
            @Override
            public void run() {
                try {
                    //查询ACT_HI_ACTINST中所有task_ID不为空、是否超时为0的节点
                    List<Map<String, Object>> map = flowNodeMapper.selectActinstOverTimeMap();
                    ArrayList<String> list = new ArrayList<>();
                    for (Map<String, Object> item : map) {
                        LocalDateTime startTime = (LocalDateTime) item.get("START_TIME_");
                        String timeout_time = item.get("timeout_time").toString();
                        //计算end_time
                        LocalDateTime endTime = startTime.plusMinutes(Integer.valueOf(timeout_time));
                        LocalDateTime now = LocalDateTime.now();
                        int compare = endTime.compareTo(now);
                        if (compare < 0) {
                            list.add(item.get("id").toString());
                        }
                    }
                    if (list.size() > 0) {
                        flowNodeMapper.updateActinstOverTimeList(list);
                    }
                    System.out.println(list);

                    // 执行定时任务的代码
                    log.info("定时任务执行了");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        };

        executor.scheduleAtFixedRate(task, 1, 30, TimeUnit.MINUTES);
        //创建了一个ScheduledExecutorService对象，并通过Runnable的匿名内部类创建了一个定时任务。
        // 使用executor.scheduleAtFixedRate方法设置了定时任务的执行时间和间隔时间。
        // 定时任务将在1分钟后开始执行，之后每2分钟执行一次。
    }
}

