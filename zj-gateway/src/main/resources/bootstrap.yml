# Tomcat
server:
  port: 48080
spring:
  main:
    allow-circular-references: true  # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
  application:
    # 应用名称
    name: gateway-server
  profiles:
    # 环境配置
    active: prod
  cloud:
    nacos:
      # 注册中心相关配置
      discovery:
        namespace: ${spring.profiles.active} # 命名空间。这里使用 dev 开发环境
        # 服务注册地址
        server-addr: nacos-production.zj-cloud:8848
      # 配置中心相关配置
      config:
        # 配置中心地址
        server-addr: nacos-production.zj-cloud:8848
        namespace: ${spring.profiles.active} # 命名空间。这里使用 dev 开发环境
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP
        name: ${spring.application.name} # 使用的 Nacos 配置集的 dataId，默认为 spring.application.name
        # 配置文件格式
        file-extension: yml

# 日志文件配置。注意，如果 logging.file.name 不放在 bootstrap.yml 配置文件，而是放在 application.yaml 中，会导致出现 LOG_FILE_IS_UNDEFINED 文件
logging:
  file:
    name: ${user.home}/logs/${spring.application.name}.log # 日志文件名，全路径
